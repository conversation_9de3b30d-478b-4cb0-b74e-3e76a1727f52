const fs = require('fs')
const readline = require('readline')
const util = require('../util')
const {
  AUDIT_LOG_ROUTER_PATH,
  AUDIT_LOG_SOURCE_PATHS,
  BATCH_EXPORT_COMPONENT,
  BATCH_EXPORT_CODE_TPL,
  resetAuditLogRoutes,
  isShowBathExport,
  genBatchExportCode
} = require('../gen-batch-export')

/**
 * 读取组件注释
 * @param path 文件路径
 * @returns {Promise<string[]>} 注释内容
 */
function readComponentComments(path) {
  return new Promise(resolve => {
    const rl = readline.createInterface({ input: fs.createReadStream(path), output: process.stdout, terminal: false })
    let startComment = false
    const comments = []
    rl.on('line', text => {
      if (startComment) {
        if (text.endsWith(util.XML_COMMENT_SUFFIX)) {
          const commentLen = text.length - util.XML_COMMENT_SUFFIX.length
          if (commentLen > 0) {
            comments.push(text.slice(0, commentLen))
          }
          rl.close()
        } else {
          comments.push(text)
        }
      } else if (text.startsWith(util.XML_COMMENT_PREFIX)) {
        startComment = true
        if (text.endsWith(util.XML_COMMENT_SUFFIX)) {
          comments.push(text.slice(util.XML_COMMENT_PREFIX.length, text.length - util.XML_COMMENT_SUFFIX.length))
          rl.close()
        } else if (text.length > util.XML_COMMENT_SUFFIX.length) {
          comments.push(text.slice(util.XML_COMMENT_PREFIX.length, text.length))
        }
      }
    })
    rl.on('close', () => {
      resolve(comments.map(comment => comment.trim()).filter(comment => comment.length > 0))
    })
  })
}

/**
 * 文件时间是否变化
 * @param mtime 文件修改时间
 * @param commentedTimeStr 文件注释时间
 * @returns {boolean}
 */
function isFileTimeChanged(mtime, commentedTimeStr) {
  try {
    // 两秒内认为无变化
    return Math.abs(mtime - parseInt(commentedTimeStr)) > 2000
  } catch (e) {
    console.error(e)
    return true
  }
}

class MyCodePlugin {
  constructor() {
    this._pluginName = 'my-code-plugin'
    // this._contextDependency = null
  }

  apply(compiler) {
    compiler.hooks.watchRun.tapAsync(this._pluginName, async(compiler, cb) => {
      console.log('File change watching...')
      const mtimes = compiler.watchFileSystem.watcher.mtimes;
      const mfiles = Object.keys(mtimes);
      if (mfiles.length > 0) {
        console.log(`This time, ${mfiles.length} files were modified:`)
        console.log(mfiles)
        console.log('--------------------------')
        const logsRouterChanged = AUDIT_LOG_ROUTER_PATH in mtimes
        if (logsRouterChanged) {
          resetAuditLogRoutes()
        }
        if (isShowBathExport()) {
          let genAgain = logsRouterChanged || BATCH_EXPORT_CODE_TPL in mtimes || AUDIT_LOG_SOURCE_PATHS.some(path => path in mtimes)
          if (!genAgain) {
            const mtime = mtimes[BATCH_EXPORT_COMPONENT]
            if (mtime) {
              const comments = await readComponentComments(BATCH_EXPORT_COMPONENT)
              genAgain = comments.length < 2 || comments[0] !== util.AUTO_GEN_MARK || isFileTimeChanged(mtime, comments[1])
            }
          }
          if (genAgain) {
            try {
              genBatchExportCode()
            } catch (e) {
              console.error(e)
              process.exit(1)
            }
            // this._contextDependency = BATCH_EXPORT_COMPONENT
          }
        }
      }
      cb()
    })
    // compiler.hooks.thisCompilation.tap(this._pluginName, (compilation, compilationParams) => {
    //   compilation.hooks.buildModule.tap(this._pluginName, module => {
    //     if (module.rawRequest && module.rawRequest.startsWith('@')) {
    //       console.log('module', module.resource)
    //     }
    //   })
    // })
    // compiler.hooks.afterCompile.tap(this._pluginName, compilation => {
    //   if (this._contextDependency) {
    //     // 强制重新编译 批量导出 代码，需配合 webpack 的配置 cache: false 一起才能生效
    //     compilation.contextDependencies.add(this._contextDependency)
    //     this._contextDependency = null
    //   }
    // })
    compiler.hooks.watchClose.tap(this._pluginName, () => {
      console.log('Stop watch file change.')
    })
  }
}

module.exports = MyCodePlugin

