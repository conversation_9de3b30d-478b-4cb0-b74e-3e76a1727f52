const babel = require('./babel')
const files = require('./files')
const generator = require('./generator')
const parser = require('./parser')
const util = require('./util')
const { templateStart, templateEnd, groupStart, groupEnd, itemStart, itemEnd } = require('./template')
const vueCompiler = require('vue-template-compiler')

// 审计日志路由配置文件路径
const AUDIT_LOG_ROUTER_PATH = files.resolvePath(parser.auditLogRouterPath)
// 各项审计日志源码文件路径
const AUDIT_LOG_SOURCE_PATHS = []
// 批量导出组件文件路径
const BATCH_EXPORT_COMPONENT = files.resolvePath('./src/views/behaviorAuditing/batchExport/index.vue')
// 批量导出代码模板文件路径
const BATCH_EXPORT_CODE_TPL = files.resolvePath('./src/views/behaviorAuditing/batchExport/index-tpl.vue')
// 生成的批量导出代码
let batchExportGenerated = ''
// 审计日志路由配置
let auditLogRoutes = null
// 路由配置中是否显示批量导出菜单
let showBatchExport = null

/**
 * 获取审计日志路由配置
 */
function getAuditLogRoutes() {
  if (auditLogRoutes == null) {
    auditLogRoutes = parser.getAuditLogRoutes()
  }
  return auditLogRoutes
}

/**
 * 从路由配置中解析是否显示批量导出菜单界面
 */
function parseShowBathExport() {
  const routes = getAuditLogRoutes()
  for (let i = 0; i < routes.length; i++) {
    const group = routes[i]
    if (group.name === 'Auditing') {
      const batchRoute = (group.children || []).filter(item => item.name === 'BatchExportLog')[0]
      return !!batchRoute && !batchRoute.hidden
    }
  }
  return false
}

/**
 * 重置审计日志路由信息
 */
function resetAuditLogRoutes() {
  auditLogRoutes = parser.getAuditLogRoutes()
  showBatchExport = parseShowBathExport()
}

/**
 * 是否显示批量导出菜单界面
 */
function isShowBathExport() {
  if (showBatchExport == null) {
    showBatchExport = parseShowBathExport()
  }
  return showBatchExport
}

/**
 * 读取批量导出原文件模板代码
 * @returns {string} 批量导出原文件模板代码
 */
function readSource() {
  return files.readFileText(BATCH_EXPORT_CODE_TPL)
}

/**
 * 生成批量导出代码
 * @param sourceCode 批量导出原文件模板代码
 * @param outPath 代码输出路径
 * @returns {string} 生成的代码
 */
function genBatchExportBySource(sourceCode, outPath = BATCH_EXPORT_COMPONENT) {
  if (!isShowBathExport()) {
    console.warn('The menu of batch export log is hidden.')
    return sourceCode
  }
  if (AUDIT_LOG_SOURCE_PATHS.length > 1) {
    AUDIT_LOG_SOURCE_PATHS.splice(1, AUDIT_LOG_SOURCE_PATHS.length - 1)
  }
  let generatedTemplate = templateStart
  const menus = {}
  const allMenuCodes = []
  getAuditLogRoutes().forEach(group => {
    if (group.name === 'Auditing') {
      return
    }
    const searchBars = (group.children || [])
      // 流量观察不需导出
      .filter(item => !item.hidden && item.path !== 'netFlowLogRT')
      .map(item => parser.parseAuditLogRoute(item))
      .filter(searchBar => !!searchBar)
    if (searchBars.length === 0) {
      return
    }
    console.log('group: ', group.meta.title)
    const groupPermissions = []
    let itemsContent = ''
    searchBars.forEach(searchBar => {
      if ('354' === searchBar.permission && group.name === 'ContentLog') {
        // 敏感内容管控记录中不显示全盘扫描记录
        return
      }
      AUDIT_LOG_SOURCE_PATHS.push(searchBar.path)
      groupPermissions.push(searchBar.permission)
      console.log(searchBar.permission + ': ' + searchBar.path)
      const menuOpts = {
        permission: searchBar.permission,
        path: searchBar.path,
        models: [],
        variables: [],
        imports: [],
        props: [],
        events: []
      }
      let barContent = itemStart(searchBar.path, searchBar.title, searchBar.name, searchBar.permission)
      searchBar.nodes.forEach(node => {
        barContent += generator.genTemplateFromVueASTNode(node, menuOpts, 8) + generator.LF
      })
      barContent += itemEnd
      itemsContent += barContent
      // 去重
      Object.entries(menuOpts).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          menuOpts[key] = Array.from(new Set(value))
        }
      })
      console.log('menuOpts', menuOpts)
      if (!Object.values(menuOpts).filter(Array.isArray).some(item => item.length > 0)) {
        return
      }
      const menu = { models: {}, variables: {}, events: {}, computed: [], mapGetters: [], created: [] }
      const scriptAst = searchBar.script.ast || babel.parse(searchBar.script.content)
      const scriptModule = parser.parseVueScriptAst(scriptAst)
      menuOpts.variables.forEach(variable => {
        if (typeof variable === 'string') {
          parser.handleSingleVarValue(variable, menu, menuOpts, scriptModule)
        } else {
          menu.variables[variable.join('_')] = parser.getPropInitialValue(variable, scriptModule.data, menuOpts)
        }
      })
      menuOpts.models.forEach(model => {
        menu.models[model[model.length - 1]] = parser.getPropInitialValue(model, scriptModule.data, menuOpts)
      })
      parser.handleVarInitInCreated(menu, menuOpts, scriptModule)
      new Set(menuOpts.events).forEach(name => parser.handleMethod(name, menu, menuOpts, scriptModule))
      menuOpts.props.forEach(prop => {
        if (!menu.variables[prop]) {
          const value = scriptModule.data[prop]
          if (!value) {
            throw new Error(`Can't find bind prop [${prop}] from script in path: ${searchBar.path}`)
          }
          menu.variables[prop] = value
        }
      })
      if (menuOpts.imports.length > 0) {
        menu.imports = Array.from(new Set(menuOpts.imports)).map(imported => scriptModule.imports[imported])
      }
      menus[searchBar.permission] = menu
    })
    allMenuCodes.push(...groupPermissions)
    generatedTemplate += groupStart(group.meta.title, groupPermissions.join(',')) + itemsContent + groupEnd
  })
  generatedTemplate += templateEnd

  const parsedComponent = vueCompiler.parseComponent(sourceCode)
  const generatedScript = generator.genScriptFromSourceAndMenus(parsedComponent.script.content, allMenuCodes, menus)
  const generatedStyles = generator.genStyleFromSourceStyles(parsedComponent.styles)

  const now = new Date()
  const componentContent = `${util.XML_COMMENT_PREFIX}
  ${util.AUTO_GEN_MARK}
  ${now.getTime()}
${util.XML_COMMENT_SUFFIX}
${generatedTemplate}
<script>
// gen @${util.CN_DATE_FORMATTER.format(now)}
${generatedScript}
</script>
${generatedStyles}`

  files.writeToFile(outPath, componentContent)
  console.log('generated: ' + outPath)
  return componentContent
}

/**
 * 生成批量导出代码
 * @param outPath 代码输出路径
 * @returns {string} 生成的代码
 */
function genBatchExportCode(outPath) {
  batchExportGenerated = genBatchExportBySource(readSource(), outPath)
  return batchExportGenerated
}

module.exports = {
  AUDIT_LOG_ROUTER_PATH,
  AUDIT_LOG_SOURCE_PATHS,
  BATCH_EXPORT_COMPONENT,
  BATCH_EXPORT_CODE_TPL,
  resetAuditLogRoutes,
  isShowBathExport,
  readSource,
  genBatchExportBySource,
  genBatchExportCode,
  getGeneratedBatchExportCode: () => batchExportGenerated
}
