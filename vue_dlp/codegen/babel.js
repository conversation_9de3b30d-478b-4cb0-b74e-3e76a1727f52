const types = require('@babel/types')
const { parse, parseExpression } = require('@babel/parser')
const { default: traverse } = require('@babel/traverse')
const { default: generate } = require('@babel/generator')
const { default: template } = require('@babel/template')
// const importsHelper = require('@babel/helper-module-imports')

const THIS = types.thisExpression()
const NULL = types.nullLiteral()
const UNDEFINED = types.identifier('undefined')
const TRUE = types.booleanLiteral(true)
const FALSE = types.booleanLiteral(false)

/**
 * 是否 null
 */
function isNull(node) {
  return types.isNullLiteral(node)
}

/**
 * 是否 undefined
 */
function isUndefined(node) {
  return types.isIdentifier(node) && node.name === 'undefined'
}

/**
 * 是否常量（数字、布尔值、字符串）
 */
function isConst(node) {
  return types.isNumericLiteral(node) || types.isBooleanLiteral(node) || types.isStringLiteral(node)
}

/**
 * 常量翻译
 */
function isConstIi8n(node) {
  return types.isCallExpression(node) &&
    generate(node.callee).code.split('.').pop() === '$t' &&
    node.arguments.length === 1 && types.isStringLiteral(node.arguments[0])
}

/**
 * 是否原始字面量
 */
function isRawLiteral(node) {
  return isNull(node) || isUndefined(node) || isConst(node) || isConstIi8n(node)
}

/**
 * 单引号字符串字面量
 */
function singleQuoteString(value) {
  const literal = types.stringLiteral(value)
  literal.extra = { rawValue: value, raw: `'${value}'` }
  return literal
}

module.exports = {
  THIS,
  NULL,
  UNDEFINED,
  TRUE,
  FALSE,
  types,
  template,
  isNull,
  isUndefined,
  isConst,
  isConstIi8n,
  isRawLiteral,
  singleQuoteString,
  parse(input) {
    return parse(input, { sourceType: 'module', plugins: ['jsx'] })
  },
  parseExpression(input) {
    return parseExpression(input, { sourceType: 'module' })
  },
  traverse(ast, visitors) {
    return traverse(ast, visitors, {})
  },
  generate(ast) {
    // jsescOption: { minimal: true } 保留中文内容，防止被转为unicode形式。
    return generate(ast, { jsescOption: { minimal: true }}).code
  }
}
