const babel = require('./babel')
const files = require('./files')
const vueCompiler = require('vue-template-compiler')
// "gogocode": "1.0.55",
// const $ = require('gogocode')
// "@vue/compiler-sfc": "2.7.13",
// const vueCompilerSfc = require('@vue/compiler-sfc')

// 审计日志路由配置文件路径
const auditLogRouterPath = 'src/router/modules/behaviorAuditing.js'
// 资产变更记录公共组件路径
const assetsLogComponentPath = '@/views/assets/assetsConfig/assetLog'
// 程序使用记录组件路径
const appLogComponentPath = '@/views/behaviorAuditing/terminal/appLog'
// 计算机系统日志组件路径
const systemLogComponentPath = '@/views/behaviorAuditing/systemLog/systemLog'
// 文件操作记录
const fileLogComponentPath = '@/views/behaviorAuditing/terminal/file'
// 全盘扫描记录组件路径
const diskScanLogComponentPath = '@/views/behaviorAuditing/encryption/diskScanLog'

// Array polyfill
if (!Array.prototype.flat) {
  Object.defineProperty(Array.prototype, 'flat', {
    configurable: true,
    value: function flat() {
      const depth = isNaN(arguments[0]) ? 1 : Number(arguments[0]);

      return depth ? Array.prototype.reduce.call(this, function(acc, cur) {
        if (Array.isArray(cur)) {
          acc.push.apply(acc, flat.call(cur, depth - 1));
        } else {
          acc.push(cur);
        }

        return acc;
      }, []) : Array.prototype.slice.call(this);
    },
    writable: true
  });
}
if (!Array.prototype.flatMap) {
  Object.defineProperty(Array.prototype, 'flatMap', {
    configurable: true,
    value: function flatMap(callback) {
      return Array.prototype.map.apply(this, arguments).flat();
    },
    writable: true
  });
}

/**
 * 获取审计日志路由信息
 */
function getAuditLogRoutes() {
  const routerContent = files.readFileText(auditLogRouterPath)
  const routerAST = babel.parse(routerContent)
  return routerAST.program.body
    .filter(node => babel.types.isVariableDeclaration(node))
    .flatMap(node => node.declarations)
    .filter(node => babel.types.isVariableDeclarator(node) && node.id.name === 'behaviorAuditingRouter' && babel.types.isObjectExpression(node.init))
    .flatMap(node => node.init.properties)
    .filter(node => node.key.name === 'children' && babel.types.isArrayExpression(node.value))
    .flatMap(node => node.value.elements)
    .map(getRouteASTNodeValue)
}

/**
 * 获取路由AST节点值
 * @param node AST节点
 * @returns {string|{}|*}
 */
function getRouteASTNodeValue(node) {
  switch (node.type) {
    case 'ThisExpression':
      return 'this'
    case 'MemberExpression':
      return getRouteASTNodeValue(node.object) + '.' + getRouteASTNodeValue(node.property)
    case 'ObjectExpression': {
      const objVal = {}
      node.properties.forEach(prop => {
        objVal[getRouteASTNodeValue(prop.key)] = getRouteASTNodeValue(prop.value)
      })
      return objVal
    }
    case 'ArrayExpression':
      return node.elements.map(getRouteASTNodeValue)
    case 'CallExpression':
      if (node.callee.type === 'Import') {
        return getRouteASTNodeValue(node.arguments[0])
      }
      return getRouteASTNodeValue(node.callee) + '(' + node.arguments.map(getRouteASTNodeValue).join(', ') + ')'
    case 'ArrowFunctionExpression':
      if (node.params.length === 0 && node.body.type === 'CallExpression') {
        return getRouteASTNodeValue(node.body)
      }
      return '(' + node.params.map(getRouteASTNodeValue).join(', ') + ') => ' + getRouteASTNodeValue(node.body)
    case 'Identifier':
      return node.name
    case 'NullLiteral':
      return 'null'
    default:
      return node.value
  }
}

/**
 * 解析审计日志路由
 * @param route 审计日志路由
 * @returns {null|{path: string, nodes: *[], permission: string, name: string, title: string, script: {ast: *, content: string}}}
 */
function parseAuditLogRoute(route) {
  const component = files.readComponent(route.component)
  // 解析 Vue 组件
  const parsedComponent = vueCompiler.parseComponent(component.content)
  // 提取模板和脚本部分
  const template = parsedComponent.template.content
  const compiledResult = vueCompiler.compile(template)

  const result = { path: component.path, name: route.name, title: route.meta.title }

  // 软硬件资产变更告警及记录
  if (compiledResult.ast.tag === 'assets-log') {
    const assetsLogSearchBar = getSearchBarByComponentPath(assetsLogComponentPath)
    const scriptAst = babel.parse(assetsLogSearchBar.script)
    babel.traverse(scriptAst, {
      ExportDefaultDeclaration(path, state) {
        const componentDef = {}
        path.node.declaration.properties.forEach(node => {
          componentDef[node.key.name] = node
        })
        const props = componentDef['props'].value.properties
        let type
        for (let i = 0; i < props.length; i++) {
          if (props[i].key.name === 'type') {
            type = props[i].value.properties.filter(item => item.key.name === 'default')[0].value.value
            props.splice(i, 1)
            break
          }
        }
        const typeAttr = compiledResult.ast.attrsMap[':type']
        if (typeAttr) {
          type = parseInt(typeAttr)
        }
        const dataBlockBody = componentDef['data'].body.body
        const dataDef = dataBlockBody[dataBlockBody.length - 1].argument.properties
        dataDef.splice(0, 0, babel.types.objectProperty(babel.types.identifier('type'), babel.types.numericLiteral(type)))
      }
    })
    const exportAble = compiledResult.ast.attrsMap[':export-able']
    return {
      ...result,
      nodes: assetsLogSearchBar.nodes.map(item => {
        if (item.attrsMap['v-if'] === 'dealAble') {
          item.attrsMap['v-if'] = compiledResult.ast.attrsMap[':detail-able']
        }
        return item
      }),
      permission: exportAble.slice('hasPermission(\''.length, exportAble.length - 2),
      script: { content: assetsLogSearchBar.script, ast: scriptAst }
    }
  }

  // 窗口标题记录
  if (compiledResult.ast.tag === 'app-log') {
    const appLogSearchBar = getSearchBarByComponentPath(appLogComponentPath)
    return {
      ...result,
      nodes: appLogSearchBar.nodes,
      permission: '322',
      script: { content: appLogSearchBar.script }
    }
  }
  // 程序使用记录
  if (route.component === appLogComponentPath) {
    const searchBar = getSearchBar(compiledResult.ast)
    return {
      ...result,
      nodes: searchBar.nodes,
      permission: '323',
      script: { content: parsedComponent.script.content }
    }
  }

  // 计算机系统日志
  if (route.component === systemLogComponentPath) {
    const searchBar = getSearchBar(compiledResult.ast)
    removeVIf(searchBar.nodes, vif => vif === 'logType==2')
    return {
      ...result,
      nodes: searchBar.nodes,
      permission: '328',
      script: { content: parsedComponent.script.content }
    }
  }
  // 计算机状态变更日志
  if (compiledResult.ast.tag === 'system-log') {
    const systemLogSearchBar = getSearchBarByComponentPath(systemLogComponentPath)
    removeVIf(systemLogSearchBar.nodes, vif => vif === 'logType==1')
    return {
      ...result,
      nodes: systemLogSearchBar.nodes,
      permission: '329',
      script: { content: systemLogSearchBar.script }
    }
  }
  // 全盘扫描记录
  if (route.component === diskScanLogComponentPath) { // 全盘扫描记录
    // removeVIf(searchBar.nodes, vif => vif !== `showOnTab('logTab')`)
    const parent = extractASTNode(compiledResult.ast.children, node => node.type === 1 && node.tag === 'el-tabs')
    const children = parent.children.filter(node => node.type === 1 && node.tag === 'el-tab-pane')
      .map(node => {
        node.tag = 'el-option'
        node.attrsMap.value = node.attrsMap.name
        delete node.attrsMap.name
        node.children = []
        return node
      })
    parent.tag = 'el-select'
    parent.attrsMap = { 'v-model': parent.attrsMap['v-model'], style: 'width: 150px', '@change': 'changeExportDataType' }
    parent.children = children

    const searchBar = getSearchBar(compiledResult.ast)
    searchBar.nodes.unshift(parent)

    const scriptAst = babel.parse(parsedComponent.script.content)
    babel.traverse(scriptAst, {
      ExportDefaultDeclaration(path, state) {
        const componentDef = {}
        path.node.declaration.properties.forEach(node => {
          componentDef[node.key.name] = node
        })
        const methods = componentDef['methods'].value.properties
        const functionDef = `
        function changeExportDataType(value) {
          this.tabName = value
          this.query.exportDataType = { logTab: 1, encOrDecTab: 2, sensTab: 3, addTagTab: 4, tagDetailTab: 5, tagDeleteTab: 6 }[value]
        }
        `
        const functionAst = babel.template.ast(functionDef)
        methods.push(babel.types.objectMethod('method', functionAst.id, functionAst.params, functionAst.body))
      }
    })

    return {
      ...result,
      nodes: searchBar.nodes,
      permission: searchBar.permission,
      script: { content: parsedComponent.script.content, ast: scriptAst }
    }
  }

  const searchBar = getSearchBar(compiledResult.ast)
  if (searchBar && searchBar.permission) {
    if (route.component === fileLogComponentPath) { // 文件操作记录
      removeVIf(searchBar.nodes, vif => vif === 'logType==2')
    }
    return {
      ...result,
      nodes: searchBar.nodes,
      permission: searchBar.permission,
      script: { content: parsedComponent.script.content }
    }
  }
  return null
}

/**
 * 根据组件路径获取搜索栏内容
 * @param componentPath
 * @returns {{nodes: *[], permission: (null|string)}}
 */
function getSearchBarByComponentPath(componentPath) {
  const parsedComponent = vueCompiler.parseComponent(files.readComponent(componentPath).content)
  const compiledResult = vueCompiler.compile(parsedComponent.template.content)
  const searchBar = getSearchBar(compiledResult.ast)
  searchBar.script = parsedComponent.script.content
  return searchBar
}

/**
 * 移除节点中的v-if和v-show指令，符合test条件的节点直接删除，不符合则删除节点的v-if和v-show属性
 * @param nodes
 * @param test
 */
function removeVIf(nodes, test) {
  for (let i = nodes.length - 1; i >= 0; i--) {
    const node = nodes[i]
    if (node.type !== 1) {
      continue
    }
    const vif = node.attrsMap['v-if'] || node.attrsMap['v-show']
    if (test(vif)) {
      nodes.splice(i, 1)
      continue
    }
    if (vif) {
      delete node.attrsMap['v-if']
      delete node.attrsMap['v-show']
    }
    removeVIf(node.children, test)
  }
}

/**
 * 提取AST节点
 * @param nodes Vue模板AST节点数组
 * @param filter 节点过滤函数
 * @returns {null|*} toolbar的AST节点或null(未找到)
 */
function extractASTNode(nodes, filter) {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i]
    if (filter(node)) {
      return node
    }
    if (node.children) {
      const result = extractASTNode(node.children, filter)
      if (result != null) {
        return result
      }
    }
  }
  return null
}

/**
 * 从Vue模板AST节点中提取toolbar节点
 * @param nodes Vue模板AST节点数组
 * @returns {null|*} toolbar的AST节点或null(未找到)
 */
function extractToolBar(nodes) {
  return extractASTNode(nodes, node => {
    if (node.type !== 1) {
      return false
    }
    const classNames = (node.attrsMap.class || '').split(' ')
    return classNames.includes('toolbar')
  })
}

/**
 * 从模板AST节点中获取搜索栏节点和导出权限
 * @param tplAST 模板AST节点
 * @returns {null|{nodes: [], permission: null|string}}
 */
function getSearchBar(tplAST) {
  const toolBar = extractToolBar(tplAST.children);
  if (toolBar == null) {
    return null
  }
  const exporterNodes = toolBar.children.filter(node => node.type === 1 && node.tag === 'audit-log-exporter')
  if (exporterNodes.length === 0) {
    // 无导出功能
    return null
  }
  const permission = exporterNodes[0].attrsMap['v-permission']
  let permissionCode = null
  if (!permission) {
    const vif = exporterNodes[0].attrsMap['v-if']
    if (!vif) {
      console.error('[WARN] No permission set for export')
    } else {
      const regex = /^hasPermission\('([^']+)'\)$/
      const match = vif.match(regex)
      if (match && match[1]) {
        permissionCode = match[1]
      } else {
        console.error('[WARN] No permission set for export')
      }
    }
  } else if (permission.charAt(0) !== '\'') {
    console.error('[WARN] unsupported permission: ', permission)
  } else {
    permissionCode = permission.slice(1, permission.length - 1)
  }
  const searchBar = { permission: permissionCode, nodes: [] }

  // 时间搜索特殊：通讯工具聊天记录，审批记录
  // 无时间：流量观察， 文档追踪、盲水印
  let foundTime = false
  for (let i = 0; i < toolBar.children.length; i++) {
    const node = toolBar.children[i]
    if (foundTime) {
      if (node.type === 1 && node.tag === 'el-button') {
        break
      }
      if (node.type === 3 && (node.text == null || node.text.trim().length === 0)) {
        continue
      }
      searchBar.nodes.push(node)
      continue
    }
    if (node.type === 1) {
      if (node.tag === 'TimeQuery' || node.tag === 'time-query') {
        foundTime = true
      } else if (node.tag === 'el-date-picker' && i + 2 < toolBar.children.length) {
        const nextDatePiker = toolBar.children[i + 2]
        if (nextDatePiker.type === 1 && nextDatePiker.tag === 'el-date-picker') {
          if (permissionCode === '367') {
            for (let j = i - 1; j >= 0; j--) {
              const firstNode = toolBar.children[j]
              if (firstNode.type === 1 && firstNode.tag === 'el-select') {
                searchBar.nodes.push(firstNode)
                break
              }
            }
          }
          i += 2
        }
        foundTime = true
      }
    }
  }

  return searchBar
}

/**
 * 是否受支持的数据节点类型（支持 null、undefined、常量等）
 */
function isSupportedData(node) {
  return babel.isNull(node) || babel.isUndefined(node) || babel.isConst(node) ||
    babel.types.isCallExpression(node) || babel.types.isObjectExpression(node) || babel.types.isArrayExpression(node)
}

/**
 * 将成员表达式和函数调用表达式按调用顺序整理
 * @param node
 * @returns {{node: *, type: string, value: *}[]|{node: *, type: *, value: *}[]|[]}
 */
function parseMemberOrCallExpression(node) {
  if (babel.types.isIdentifier(node)) {
    return [{ value: node.name, type: 'Identifier', node }]
  }
  if (babel.types.isNumericLiteral(node) || babel.types.isStringLiteral(node)) {
    return [{ value: node.value, type: node.type, node }]
  }
  const members = []
  _parseMemberOrCallExpression(node, members)
  return members
}

function _parseMemberOrCallExpression(node, members, callArgs) {
  if (babel.types.isMemberExpression(node)) {
    const right = node.property
    let member
    if (babel.types.isIdentifier(right)) {
      member = { value: right.name, type: 'Identifier', node: right }
    } else if (babel.types.isNumericLiteral(right) || babel.types.isStringLiteral(right)) {
      member = { value: right.value, type: right.type, node: right }
    } else {
      throw new TypeError('Unsupported property type of MemberExpression: ' + right.type)
    }
    if (callArgs) {
      member.callArgs = callArgs
    }
    members.splice(0, 0, member)

    const left = node.object
    if (babel.types.isIdentifier(left)) {
      members.splice(0, 0, { value: left.name, type: 'Identifier', node: left })
    } else if (babel.types.isThisExpression(left)) {
      members.splice(0, 0, { value: 'this', type: 'ThisExpression', node: left })
    } else if (babel.types.isMemberExpression(left) || babel.types.isCallExpression(left)) {
      _parseMemberOrCallExpression(left, members)
    } else if (babel.types.isArrayExpression(left)) {
      members.splice(0, 0, { array: true, type: 'ArrayExpression', node: left })
    } else {
      throw new TypeError('Unsupported object type of MemberExpression: ' + left.type)
    }
  } else if (babel.types.isCallExpression(node)) {
    const callee = node.callee
    if (babel.types.isIdentifier(callee)) {
      members.splice(0, 0, { value: callee.name, type: 'Identifier', node: callee, callArgs: node.arguments })
    } else if (babel.types.isMemberExpression(callee) || babel.types.isCallExpression(callee)) {
      _parseMemberOrCallExpression(callee, members, node.arguments)
    } else {
      throw new TypeError('Unsupported callee type of CallExpression: ' + callee.type)
    }
  } else {
    throw new TypeError('Unexpected node type : ' + node.type)
  }
}

/**
 * 解析Vue文件Script部分的AST
 * @param scriptAst js的AST节点
 * @returns {{imports: {}, data: {}, computed: {}, created: [], methods: {}, props: {}, mapGetters: []}}
 */
function parseVueScriptAst(scriptAst) {
  const scriptModule = {
    imports: {},
    props: {},
    data: {},
    computed: {},
    mapGetters: [],
    created: [],
    methods: {}
  }
  babel.traverse(scriptAst, {
    ImportDeclaration(path, state) {
      path.node.specifiers.forEach(item => {
        const hinted = item.local.name
        const importSource = { from: path.node.source.value, hinted, binding: path.scope.getBinding(hinted) }
        scriptModule.imports[hinted] = importSource
        if (babel.types.isImportDefaultSpecifier(item)) {
          importSource.default = true
          importSource.imported = hinted
        } else if (babel.types.isImportNamespaceSpecifier(item)) {
          importSource.default = true
          importSource.imported = '*'
        } else { // ImportSpecifier
          importSource.imported = item.imported.name || item.imported.value
        }
      })
      path.skip()
    },
    ExportDefaultDeclaration(path, state) {
      // ExportDefaultDeclaration -> declaration: ObjectExpression
      const exportItemPaths = path.get('declaration.properties')
      exportItemPaths.forEach(itemPath => {
        const name = itemPath.node.key.name
        if (name === 'props') return
        if (name === 'data') {
          // ObjectMethod -> body: BlockStatement
          const blockBodyItems = itemPath.get('body.body')
          // if (blockBodyItems.length > 1) {
          //   throw new Error('Unsupported data() block for Statements: ' + blockBodyItems.map(item => item.type).filter(type => type !== 'ReturnStatement'))
          // }
          // ReturnStatement -> argument: ObjectExpression
          blockBodyItems[blockBodyItems.length - 1].get('argument.properties').filter(item => item.type === 'ObjectProperty').forEach(item => {
            if (isSupportedData(item.node.value)) {
              scriptModule.data[item.node.key.name] = item.node.value
            }
          })
          return
        }
        if (name === 'computed') {
          // ObjectProperty -> value: ObjectExpression
          itemPath.get('value.properties').forEach(item => {
            if (item.isObjectMethod()) {
              scriptModule.computed[item.node.key.name] = item.node
              return
            }
            if (item.isSpreadElement()) {
              const argument = item.node.argument
              if (babel.types.isCallExpression(argument)) {
                const callee = argument.callee
                if (babel.types.isIdentifier(callee) && callee.name === 'mapGetters') {
                  // Array[StringLiteral]
                  argument.arguments[0].elements.forEach(ele => scriptModule.mapGetters.push(ele.value))
                }
              }
            }
          })
          return
        }
        if (name === 'created') {
          // ObjectMethod -> body: BlockStatement
          itemPath.get('body.body').filter(item => item.isExpressionStatement()).map(item => item.get('expression')).forEach(item => {
            if (item.isAssignmentExpression()) {
              const left = item.node.left
              if (babel.types.isMemberExpression(left) && babel.types.isThisExpression(left.object)) {
                scriptModule.created.push({ type: 'assign', expr: item.node, key: left.property.name })
              }
              return
            }
            if (item.isCallExpression()) {
              const members = parseMemberOrCallExpression(item.node)
              if (members[0].type === 'ThisExpression' && members[1].callArgs) {
                scriptModule.created.push({ type: 'call', expr: item.node, key: members[1].value })
                if (members.length === 2) {
                  return
                }
              } else if (!members[0].callArgs || (members.length === 1 && members[0].callArgs.length === 0)) {
                return
              }
            }
            item.traverse({
              AssignmentExpression(path, state) {
                const left = path.node.left
                if (babel.types.isMemberExpression(left) && babel.types.isThisExpression(left.object)) {
                  scriptModule.created.push({ type: 'assign', expr: item.node, key: left.property.name })
                }
                path.skip()
              },
              CallExpression(path, state) {
                if (path.node.arguments.length !== 3) {
                  return
                }
                if (!path.get('callee').isMemberExpression()) {
                  return
                }
                const members = parseMemberOrCallExpression(path.node.callee)
                if (members[0].type !== 'ThisExpression') {
                  return
                }
                if (members.length === 2 && members[1].value === '$set') {
                  const arg0 = path.node.arguments[0]
                  if (babel.types.isMemberExpression(arg0) && babel.types.isThisExpression(arg0.object)) {
                    scriptModule.created.push({ type: '$set', expr: item.node, key: arg0.property.name })
                  }
                  path.skip()
                  return
                }
                if (members.length === 3 && members[2].value === 'splice') {
                  scriptModule.created.push({ type: 'splice', expr: item.node, key: members[1].value })
                  path.skip()
                }
              }
            })
          })
          return
        }
        if (name === 'methods') {
          // ObjectProperty -> value: ObjectExpression
          itemPath.get('value.properties').forEach(item => {
            const methodName = item.node.key.name
            if (item.type === 'ObjectMethod') {
              scriptModule.methods[methodName] = {
                name: methodName,
                async: item.node.async,
                params: item.node.params,
                body: item.node.body
              }
              return
            }
            if (item.type === 'ObjectProperty') {
              const funcExpr = item.node.value
              if (funcExpr.type === 'Identifier') {
                scriptModule.methods[methodName] = funcExpr.name
              } else {
                // FunctionExpression
                // ArrowFunctionExpression
                const body = funcExpr.body
                if (!babel.types.isBlockStatement(body)) {
                  throw new Error('Unsupported method [' + methodName + '] body: ' + body)
                }
                scriptModule.methods[methodName] = {
                  name: methodName,
                  async: funcExpr.async,
                  params: funcExpr.params,
                  body: funcExpr.body
                }
              }
            }
          })
        }
      })
      path.skip()
    }
  })

  return scriptModule
}

/**
 * 获取节点中导入的模块
 */
function getImportModules(modules, node) {
  return Object.values(modules)
    .filter(item => item.binding.referencePaths.some(path => path.node.start >= node.start && path.node.end <= node.end))
    .map(item => item.hinted)
}

/**
 * 获取属性初始值
 * @param propSegments 属性切片
 * @param data 属性数据集
 * @param opts 配置选项
 * @returns {Node} 属性初始值AST
 */
function getPropInitialValue(propSegments, data, opts) {
  let value = data[propSegments[0]]
  if (!value) {
    throw new Error(`Can't find bind prop [${propSegments[0]}] from script in path: ${opts.path}`)
  }
  if (propSegments.length > 1) {
    for (let i = 1; i < propSegments.length; i++) {
      value = value.properties.filter(item => item.key.name === propSegments[i]).map(item => item.value)[0]
    }
    if (!value) {
      value = babel.UNDEFINED
      console.error(`[WARN] Can't find bind prop [${propSegments.join('.')}] from script in path: ${opts.path}`)
    }
  }
  return checkPropValue(value, opts)
}

/**
 * 校验属性值
 * @param value 属性值
 * @param opts 配置选项
 * @returns {Node} 验证替换后的属性值
 */
function checkPropValue(value, opts) {
  if (babel.types.isIdentifier(value) && !babel.isUndefined(value)) {
    opts.imports.push(value.name)
  } else if (babel.types.isMemberExpression(value) || babel.types.isCallExpression(value)) {
    const segments = babel.generate(value).split('.')
    const segment0 = segments[0]
    if (segment0 === 'this') {
      const segment1 = segments[1]
      if (segment1.charAt(0) !== '$') {
        if (segment1.charAt(segment1.length - 1) === ')') {
          const methodName = segment1.slice(0, segment1.indexOf('('))
          opts.events.push(methodName)
          segments[1] = methodName + '_' + opts.permission
        } else {
          opts.props.push(segment1)
          segments[1] = segment1 + '_' + opts.permission
        }
        value = babel.parseExpression(segments.join('.'))
      }
    } else {
      const idx = segment0.indexOf('(')
      opts.imports.push(idx > 0 ? segment0.slice(0, idx) : segment0)
    }
  }
  return value
}

/**
 * 处理单个变量值
 * @param variable 变量名
 * @param menu 菜单项
 * @param opts 菜单配置
 * @param scriptModule js模块
 */
function handleSingleVarValue(variable, menu, opts, scriptModule) {
  if (menu.variables[variable] ||
    menu.mapGetters[variable] ||
    menu.computed[variable]) {
    return
  }
  let value = scriptModule.data[variable]
  if (value) {
    value = checkPropValue(value, opts)
    menu.variables[variable] = value
  } else if (scriptModule.mapGetters[variable]) {
    menu.mapGetters.push(variable)
  } else {
    value = scriptModule.computed[variable]
    if (!value) {
      throw new Error(`Can't find bind prop [${variable}] from script in path: ${opts.path}`)
    }
    menu.computed.push(value)
    handleMethodBody(value.body, menu, opts, scriptModule)
  }
}

/**
 * 处理方法
 * @param name 方法名
 * @param menu 菜单项
 * @param opts 菜单配置
 * @param scriptModule js模块
 */
function handleMethod(name, menu, opts, scriptModule) {
  const method = scriptModule.methods[name]
  if (!method) {
    throw new Error(`Can't find bind event method [${name}] from script in path: ${opts.path}`)
  }
  if (typeof method === 'string') {
    opts.imports.push(method)
  } else {
    handleMethodBody(method.body, menu, opts, scriptModule)
  }
  menu.events[name] = method
}

/**
 * 处理方法体
 * @param body 方法体
 * @param menu 菜单项
 * @param opts 菜单配置
 * @param scriptModule js模块
 */
function handleMethodBody(body, menu, opts, scriptModule) {
  opts.imports.push(...getImportModules(scriptModule.imports, body))
  babel.traverse(body, {
    CallExpression(path) {
      const members = parseMemberOrCallExpression(path.node)
      if (members[members.length - 1].value === 'execRowDataApi') {
        path.remove()
      }
    },
    ThisExpression(path) {
      if (!babel.types.isMemberExpression(path.parent)) {
        return
      }
      const next = path.parent.property
      if (!babel.types.isIdentifier(next)) {
        throw new Error(`Unsupported expression at: ${opts.path}(line: ${next.loc.start.line})`)
      }
      const prop = next.name
      if (!scriptModule.data[prop] && !scriptModule.computed[prop] && !scriptModule.methods[prop]) {
        return
      }
      if (path.parentPath && babel.types.isCallExpression(path.parentPath.container)) {
        next.name = `${prop}_${opts.permission}`
        handleMethod(prop, menu, opts, scriptModule)
        return
      }
      if (prop === 'query') {
        next.name = `query['${opts.permission}']`
        const parentParent = path.parentPath.parent
        if (babel.types.isMemberExpression(parentParent)) {
          const model = parentParent.property.name
          if (!menu.models[model]) {
            menu.models[model] = getPropInitialValue([prop, model], scriptModule.data, opts)
          }
        }
      } else {
        next.name = `${prop}_${opts.permission}`
        handleSingleVarValue(prop, menu, opts, scriptModule)
      }
    }
  })
}

/**
 * 处理 created 生命周期函数中的变量初始化
 * @param menu 菜单项
 * @param opts 菜单配置
 * @param scriptModule js模块
 */
function handleVarInitInCreated(menu, opts, scriptModule) {
  scriptModule.created.forEach(item => {
    if (item.type === 'call') {
      const method = scriptModule.methods[item.key]
      if (!method || typeof method === 'string') {
        return
      }
      babel.traverse(method.body, {
        AssignmentExpression(path, state) {
          const left = path.node.left
          if (isVarInitInMethod(left, menu)) {
            menu.created.push(checkCreatedExpr(item.expr, opts, scriptModule))
            opts.events.push(item.key)
          }
          path.skip()
        },
        CallExpression(path, state) {
          if (path.node.arguments.length !== 3) {
            return
          }
          if (!path.get('callee').isMemberExpression()) {
            return
          }
          const members = parseMemberOrCallExpression(path.node.callee)
          if (members[0].type !== 'ThisExpression') {
            return
          }
          if (members.length === 2 && members[1].value === '$set') {
            const arg0 = path.node.arguments[0]
            if (isVarInitInMethod(arg0, menu)) {
              menu.created.push(checkCreatedExpr(item.expr, opts, scriptModule))
              opts.events.push(item.key)
            }
            path.skip()
            return
          }
          if (members.length === 3 && members[2].value === 'splice' && menu.variables[members[1].value]) {
            menu.created.push(checkCreatedExpr(item.expr, opts, scriptModule))
            opts.events.push(item.key)
            path.skip()
          }
        }
      })
    } else {
      // item.type === 'assign' || '$set' || 'splice'
      const assign = menu.variables[item.key]
      if (!assign) {
        return
      }
      menu.created.push(checkCreatedExpr(item.expr, opts, scriptModule))
    }
  })
}

/**
 * 变量是否在方法中初始化
 * @param node 方法体
 * @param menu 菜单项
 */
function isVarInitInMethod(node, menu) {
  return babel.types.isMemberExpression(node) &&
    babel.types.isThisExpression(node.object) &&
    menu.variables[node.property.name]
}

/**
 * 检查 created 生命周期函数中的 表达式
 * @param expr 表达式
 * @param opts 菜单配置
 * @param scriptModule js模块
 */
function checkCreatedExpr(expr, opts, scriptModule) {
  opts.imports.push(...getImportModules(scriptModule.imports, expr))
  babel.traverse(expr, {
    ThisExpression(path) {
      const prop = path.parent.property
      const name = prop.name
      if (!scriptModule.data[name] && !scriptModule.computed[name] && !scriptModule.methods[name]) {
        return
      }
      if (name === 'query') {
        throw new Error(`Unexpected var init for 'query' in path: ${opts.path}`)
      }
      prop.name = name + '_' + opts.permission
      if (scriptModule.methods[name]) {
        opts.events.push(name)
      } else {
        opts.props.push(name)
      }
      path.skip()
    }
  })
  return expr
}

module.exports = {
  auditLogRouterPath,
  getAuditLogRoutes,
  parseAuditLogRoute,
  parseMemberOrCallExpression,
  parseVueScriptAst,
  getPropInitialValue,
  handleSingleVarValue,
  handleMethod,
  handleVarInitInCreated
}
