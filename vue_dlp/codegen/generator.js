const babel = require('./babel')
const parser = require('./parser')
const { codePadding } = require('./template')

/**
 * 换行符
 * @type {string}
 */
const LF = '\n'

/**
 * 处理绑定的属性
 * @param attr :开头的属性名
 * @param value 属性值
 * @param vmodel v-model绑定变量
 * @param vforVars v-for定义变量
 * @param menuOpts 菜单配置
 * @returns {string|*} 处理后的属性值（常量或添加权限后缀的变量名）
 */
function handleBindProp(attr, value, vmodel, vforVars, menuOpts) {
  if ('null' === value || 'undefined' === value || 'true' === value || 'false' === value || value.startsWith('$t')) {
    return value
  }
  if (vmodel.old && value.startsWith(vmodel.old + '.')) {
    return vmodel.new + value.slice(vmodel.old.length)
  }
  const expr = babel.parseExpression(value)
  if (babel.isRawLiteral(expr)) {
    return value
  }
  if (babel.types.isIdentifier(expr)) {
    if (vforVars.includes(value)) {
      return value
    }
    menuOpts.variables.push(value)
    return value + '_' + menuOpts.permission
  }
  if (babel.types.isMemberExpression(expr)) {
    const members = parser.parseMemberOrCallExpression(expr)
    if (vforVars.includes(members[0].value)) {
      return value
    }
    if (members[0].value === 'query') {
      return `query['${menuOpts.permission}']${value.slice(value.indexOf('.'))}`
    }
    const segments = value.split('.')
    menuOpts.variables.push(segments)
    return [...segments, menuOpts.permission].join('_')
  }
  if (babel.types.isObjectExpression(expr) && expr.properties.every(item => babel.types.isObjectProperty(item) && babel.isRawLiteral(item.value))) {
    return value
  }
  if (babel.types.isCallExpression(expr) && babel.types.isIdentifier(expr.callee) && expr.arguments.length <= vforVars.length && expr.arguments.every(item => babel.isRawLiteral(item) || (babel.types.isIdentifier(item) && vforVars.includes(item.name)))) {
    const callee = expr.callee.name
    if (!global[callee]) {
      menuOpts.events.push(callee)
      expr.callee.name = callee + '_' + menuOpts.permission
    }
    return expr.callee.name + value.slice(value.indexOf('('))
  }
  throw new TypeError(`[${menuOpts.permission}] Unsupported ast node type [${expr.type}] for vue bind prop: ${attr}="${value}"`)
}

/**
 * 处理绑定的事件
 * @param attr @开头的属性名
 * @param value 属性值
 * @param vmodel v-model绑定变量
 * @param menuOpts 菜单配置
 * @returns {string|*} 处理后的事件方法
 */
function handleBindEvent(attr, value, vmodel, menuOpts) {
  const expr = babel.parseExpression(value)
  if (babel.types.isIdentifier(expr)) {
    menuOpts.events.push(value)
    return value + '_' + menuOpts.permission
  }
  if (babel.types.isArrowFunctionExpression(expr) && babel.types.isAssignmentExpression(expr.body) && babel.generate(expr.body.left) === vmodel.old) {
    expr.body.left = babel.parseExpression(vmodel.new)
    return babel.generate(expr)
  }
  throw new TypeError(`[${menuOpts.permission}] Unsupported vue bind event: ${attr}="${value}"`)
}

/**
 * 处理元素显示状态
 * @param attr 属性名（v-if或v-show）
 * @param value 属性值
 * @param vforVars v-for定义变量
 * @param menuOpts 菜单配置
 * @returns {string|*} 处理后的显示条件
 */
function handleElementShow(attr, value, vforVars, menuOpts) {
  if (value.indexOf('isTrialDlp()') >= 0 || value.indexOf('hasPermission(') >= 0) {
    return value
  }
  const resolveMembersValue = node => {
    const members = parser.parseMemberOrCallExpression(node)
    if (vforVars.includes(members[0].value)) {
      return babel.generate(node)
    }
    if (members[0].value === 'query') {
      const resultValue = babel.generate(node)
      return `query['${menuOpts.permission}']${resultValue.slice(resultValue.indexOf('.'))}`
    }
    if (members[0].callArgs && members[0].callArgs.every(item => (babel.types.isIdentifier(item) && vforVars.includes(item)) || babel.isRawLiteral(item))) {
      const methodName = members[0].value
      if (menuOpts.events.indexOf(methodName) < 0) {
        menuOpts.events.push(methodName)
      }
      const resultValue = babel.generate(node)
      return `${methodName}_${menuOpts.permission}${resultValue.slice(resultValue.indexOf('('))}`
    }
    throw new TypeError(`[${menuOpts.permission}] Unsupported vue directive: ${attr}="${value}"`)
  }
  const resolveShowValue = node => {
    if (babel.types.isIdentifier(node) || babel.types.isMemberExpression(node) || babel.types.isCallExpression(node)) {
      return resolveMembersValue(node)
    }
    if (babel.types.isUnaryExpression(node) && node.operator === '!') {
      // operator: "void" | "throw" | "delete" | "!" | "+" | "-" | "~" | "typeof";
      const resultValue = resolveMembersValue(node.argument)
      if (['!', '+', '-', '~'].includes(node.operator)) {
        return node.operator + resultValue
      }
      return node.operator + ' ' + resultValue
    }
    if (babel.types.isLogicalExpression(node)) {
      // 应尽量避免复杂的逻辑表达式
      // operator: "||" | "&&" | "??";
      return `${resolveShowValue(node.left)} ${node.operator} ${resolveShowValue(node.right)}`
    }
    if (babel.types.isBinaryExpression(node)) {
      const leftMembers = parser.parseMemberOrCallExpression(node.left)
      const rightMembers = parser.parseMemberOrCallExpression(node.right)
      if ((vforVars.includes(leftMembers[0].value) || leftMembers[0].value === 'query' || babel.isRawLiteral(leftMembers[0].node)) &&
        (vforVars.includes(rightMembers[0].value) || rightMembers[0].value === 'query' || babel.isRawLiteral(rightMembers[0].node))) {
        let left = babel.generate(node.left)
        if (leftMembers[0].value === 'query') {
          left = left.replace('query.', `query['${menuOpts.permission}'].`)
        }
        let right = babel.generate(node.right)
        if (rightMembers[0].value === 'query') {
          right = right.replace('query.', `query['${menuOpts.permission}'].`)
        }
        return left + node.operator + right
      }
    }
    throw new TypeError(`[${menuOpts.permission}] Unsupported vue directive: ${attr}="${value}"`)
  }
  return resolveShowValue(babel.parseExpression(value))
}

/**
 * 获取 ASTExpression 或 ASTText 的文本
 * @param node ASTExpression 或 ASTText 节点
 * @param padding 行偏移量（行前补全空格数量）
 * @returns {string} 节点文本
 */
function getASTNodeText(node, padding) {
  let text = node.text.trim()
  if (text.startsWith(LF)) {
    text = text.slice(1).trim()
  }
  if (text.endsWith(LF)) {
    text = text.slice(-1)
  }
  return codePadding.repeat(padding) + text
}

/**
 * 通过Vue组件AST节点生成模板代码
 * @param node AST节点
 * @param menuOpts permission 导出权限编码, models v-model绑定属性集, variables AST节点中绑定的变量, events: 事件绑定方法
 * @param padding 行偏移量（行前补全空格数量）
 * @param vforVars v-for指令变量
 * @returns {string} 模板代码
 */
function genTemplateFromVueASTNode(node, menuOpts, padding, vforVars = []) {
  // 2 ASTExpression
  // 3 ASTText
  if (node.type !== 1) {
    return getASTNodeText(node, padding)
  }
  // 1 ASTElement
  let template = codePadding.repeat(padding) + '<' + node.tag
  const vmodel = {}
  Object.entries(node.attrsMap).forEach(([attr, value]) => {
    let transformedValue
    if (attr === 'v-model') {
      const segments = value.split('.')
      const model = segments[segments.length - 1]
      menuOpts.models.push(segments)
      vmodel.old = value
      vmodel.new = `query['${menuOpts.permission}'].${model}`
      transformedValue = vmodel.new
    } else if (attr === 'v-for') {
      menuOpts.variables.push(node.for.indexOf('.') > 0 ? node.for.split('.') : node.for)
      vforVars.push(node.alias)
      Object.entries(node).forEach(([key, val]) => {
        if (/^iterator[0-9]+$/.test(key) && val) {
          vforVars.push(val)
        }
      })
      transformedValue = value.replace(/\./g, '_') + '_' + menuOpts.permission
    } else if (attr.charAt(0) === ':') {
      transformedValue = handleBindProp(attr, value, vmodel, vforVars, menuOpts)
    } else if (attr.charAt(0) === '@') {
      transformedValue = handleBindEvent(attr, value, vmodel, menuOpts)
    } else if (attr === 'v-if' || attr === 'v-show') {
      transformedValue = handleElementShow(attr, value, vforVars, menuOpts)
    } else {
      transformedValue = value
    }
    template += ' ' + attr
    if (transformedValue) {
      template += `="${transformedValue}"`
    }
  })
  const children = (node.children || []).filter(item => item.type !== 3 || (item.text && item.text.trim().length > 0))
  if (children.length > 0) {
    template += '>'
    if (children.length === 1 && children[0].type !== 1) {
      template += getASTNodeText(children[0], 0)
    } else {
      template += LF
      children.forEach(item => {
        template += genTemplateFromVueASTNode(item, menuOpts, padding + 1, vforVars) + LF
      })
      template += codePadding.repeat(padding)
    }
    template += '</' + node.tag + '>'
  } else {
    template += '/>'
  }
  return template
}

/**
 * 生成各菜单项的导入声明
 */
function genImportDeclarations(menus) {
  const declarations = []
  const menuEntries = Object.entries(menus)
  if (menuEntries.some(([, menu]) => menu.mapGetters.length > 0)) {
    declarations.push(babel.template.ast(`import { mapGetters } from 'vuex'`))
  }
  const importsMap = {}
  const vars = {}
  menuEntries.forEach(([code, menu]) => {
    if (menu.imports) {
      menu.imports.forEach(item => {
        let importOpts = importsMap[item.from]
        if (!importOpts) {
          importOpts = {}
          importsMap[item.from] = importOpts
        }
        const imported = importOpts[item.imported]
        if (!imported) {
          const imports = Object.values(importsMap).flatMap(Object.keys)
          if (imports.includes(item.imported)) {
            item.hinted = item.hinted + '_' + code
            item.binding.referencePaths.forEach(refPath => {
              refPath.node.name = item.hinted
            })
          }
          importOpts[item.imported] = item
        } else if (imported.hinted !== item.hinted) {
          vars[item.hinted] = imported.hinted
        }
      })
      console.log(code, menu.imports)
    }
  })

  Object.entries(importsMap).forEach(([from, imports]) => {
    const specifiers = []
    Object.values(imports).forEach(item => {
      if (!item.default) {
        specifiers.push(babel.types.importSpecifier(babel.types.identifier(item.hinted), babel.types.identifier(item.imported)))
        return
      }
      if (item.imported === '*') {
        specifiers.push(babel.types.importNamespaceSpecifier(babel.types.identifier(item.hinted)))
      } else {
        specifiers.push(babel.types.importDefaultSpecifier(babel.types.identifier(item.hinted)))
      }
    })
    declarations.push(babel.types.importDeclaration(specifiers, babel.singleQuoteString(from)))
  })
  return declarations
}

/**
 * 处理各菜单项的data变量生成
 */
function handleDataGen(dataNode, menus, allMenuCodes) {
  babel.traverse(dataNode, {
    ReturnStatement(returnPath) {
      const dataProps = returnPath.node.argument.properties
      const data = {}
      dataProps.forEach(item => {
        data[item.key.name] = item
      })
      data['checkedMenus'].value.properties = allMenuCodes
        .map(code => babel.types.objectProperty(babel.types.identifier(code), babel.TRUE))
      const queryProps = data['query'].value.properties
      Object.entries(menus).forEach(([code, menu]) => {
        const models = Object.entries(menu.models)
        if (models.length > 0) {
          const props = models.map(([key, val]) => babel.types.objectProperty(babel.types.identifier(key), val))
          queryProps.push(babel.types.objectProperty(babel.types.identifier(code), babel.types.objectExpression(props)))
        }
        const variables = Object.entries(menu.variables)
        variables.forEach(([key, val]) => {
          dataProps.push(babel.types.objectProperty(babel.types.identifier(key + '_' + code), val))
        })
      })
    }
  })
}

/**
 * 处理各菜单项的计算属性生成
 */
function handleComputedGen(computedNode, menus) {
  const computedProps = computedNode.value.properties
  const mapGetters = new Set()
  Object.entries(menus).forEach(([code, menu]) => {
    menu.computed.forEach(item => {
      item.key.name += '_' + code
      computedProps.push(item)
    })
    menu.mapGetters.forEach(item => mapGetters.add(item))
  })
  if (mapGetters.size > 0) {
    let mapGettersNode = computedProps.find(item => babel.types.isSpreadElement(item))
    if (!mapGettersNode) {
      mapGettersNode = babel.template.expression(`({...mapGetters(['${Array.from(mapGetters).join("','")}'])})`)()
      computedProps.unshift(...mapGettersNode.properties)
    } else {
      // SpreadElement -> argument: Expression ( mapGetters 为 CallExpression )
      // CallExpression -> arguments: : Array<Expression | SpreadElement | JSXNamespacedName | ArgumentPlaceholder>
      // arguments[0]: ArrayExpression -> elements: Array<null | Expression | SpreadElement>
      const elements = mapGettersNode.argument.arguments[0].elements
      // StringLiteral -> value: string
      const existed = elements.map(ele => ele.value)
      mapGetters.forEach(item => {
        if (!existed.includes(item)) {
          elements.push(babel.singleQuoteString(item))
        }
      })
    }
  }
}

/**
 * 处理各菜单项的初始化表达式生成
 */
function handleCreatedGen(createdNode, menus) {
  const stmts = createdNode.body.body
  Object.entries(menus).forEach(([code, menu]) => {
    new Set(menu.created).forEach(expr => {
      stmts.push(babel.types.expressionStatement(expr))
    })
  })
}

/**
 * 处理各菜单项的方法生成
 */
function handleMethodsGen(methodsNode, menus) {
  const methods = methodsNode.value.properties
  Object.entries(menus).forEach(([code, menu]) => {
    Object.entries(menu.events).forEach(([name, func]) => {
      if (typeof func === 'string') {
        methods.push(babel.types.objectProperty(babel.types.identifier(name + '_' + code), babel.types.identifier(func)))
      } else if (babel.types.isBlockStatement(func.body)) {
        methods.push(babel.types.objectMethod('method', babel.types.identifier(name + '_' + code), func.params, func.body, false, false, func.async))
      } else {
        methods.push(babel.types.objectProperty(babel.types.identifier(name + '_' + code), babel.types.arrowFunctionExpression(func.params, func.body)))
      }
    })
  })
}

/**
 * 通过原文件的JS代码和各菜单项配置生成JS代码
 * @param sourceScript 原文件JS代码
 * @param allMenuCodes 所有的菜单导出权限编码
 * @param menus 各菜单项
 * @returns {string} JS代码
 */
function genScriptFromSourceAndMenus(sourceScript, allMenuCodes, menus) {
  const scriptAST = babel.parse(sourceScript)
  babel.traverse(scriptAST, {
    Program(path) {
      path.node.body.unshift(...genImportDeclarations(menus))
    },
    ExportDefaultDeclaration(path, state) {
      path.node.declaration.properties.forEach(node => {
        if (node.key.name === 'name') {
          node.value = babel.singleQuoteString('BatchExportLog')
          return
        }
        if (node.key.name === 'data') {
          handleDataGen(node, menus, allMenuCodes)
          return
        }
        if (node.key.name === 'computed') {
          handleComputedGen(node, menus)
          return
        }
        if (node.key.name === 'created') {
          handleCreatedGen(node, menus)
          return
        }
        if (node.key.name === 'methods') {
          handleMethodsGen(node, menus)
        }
      })
    }
  })
  return babel.generate(scriptAST)
}

/**
 * 通过原文件的样式定义生成样式
 * @param styles 原文件的样式定义
 * @returns {string} 样式代码
 */
function genStyleFromSourceStyles(styles) {
  let styleContent = ''
  styles.forEach(style => {
    styleContent += '\n<style'
    if (style.lang) {
      styleContent += ' lang="' + style.lang + '"'
    }
    if (style.scoped) {
      styleContent += ' scoped'
    }
    styleContent += '>'
    styleContent += style.content
    styleContent += '</style>\n'
  })
  return styleContent
}

module.exports = {
  LF,
  genTemplateFromVueASTNode,
  genScriptFromSourceAndMenus,
  genStyleFromSourceStyles
}
