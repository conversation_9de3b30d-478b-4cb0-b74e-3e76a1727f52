const fs = require('fs')
const path = require('path')

function resolvePath(filePath) {
  return path.resolve(filePath)
}

/**
 * 读取文本文件内容
 * @param filePath 文件路径
 * @returns {string} 文件内容
 */
function readFileText(filePath) {
  return fs.readFileSync(resolvePath(filePath), 'utf8')
}

/**
 * 读取组件文件内容
 * @param component 组件路径
 * @param fileSuffix 文件后缀名（默认.vue）
 * @returns {{path: string, content: string}} { path: 组件绝对路径, content: 组件内容 }
 */
function readComponent(component, fileSuffix = '.vue') {
  let componentPath = path.resolve(component.replace('@', 'src'))
  if (!componentPath.toLowerCase().endsWith(fileSuffix.toLowerCase())) {
    if (fs.existsSync(componentPath)) {
      componentPath = path.resolve(componentPath, 'index' + fileSuffix)
    } else {
      componentPath += fileSuffix
    }
  }
  return { path: componentPath, content: fs.readFileSync(componentPath, 'utf8') }
}

/**
 * 写入到文件
 * @param filePath 文件路径
 * @param content 要写入的内容
 * @param append 是否追加到文件末尾（默认false，覆写文件内容）
 */
function writeToFile(filePath, content, append = false) {
  fs.writeFileSync(filePath, content, { flag: append ? 'a+' : 'w+' })
}

module.exports = {
  resolvePath,
  readFileText,
  readComponent,
  writeToFile
}
