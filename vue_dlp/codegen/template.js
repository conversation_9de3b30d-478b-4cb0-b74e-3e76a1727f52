/**
 * 行偏移字符串
 * @type {string}
 */
const codePadding = '  '

/**
 * 模板开头
 * @type {string}
 */
const templateStart = `<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <TimeQuery @getTimeParams="getTimeParams"/>
        <audit-log-exporter batch :name="getFileName" :request="handleExport"/>
        <el-button type="text" @click="handleCheckAllChange(true)">{{ $t('button.selectAll') }}</el-button>
        <el-button type="text" @click="handleCheckAllChange(false)">{{ $t('button.cancelSelectAll') }}</el-button>
      </div>
      <div class="tableBox">
`

/**
 * 模板结尾
 * @type {string}
 */
const templateEnd = `${codePadding.repeat(3)}</div>
${codePadding.repeat(2)}</div>
${codePadding}</div>
</template>
`

function groupStart(groupTitle, permission) {
  return `${codePadding.repeat(4)}<div v-permission="'${permission}'" class="export-group">
${codePadding.repeat(5)}<el-divider class="export-group-label" content-position="left">{{ formatLogTitle('${groupTitle}') }}</el-divider>
${codePadding.repeat(5)}<div class="export-group-items">
`
}

const groupEnd = `${codePadding.repeat(5)}</div>
${codePadding.repeat(4)}</div>
`

function itemStart(itemPath, itemTitle, itemName, permission) {
  return `${codePadding.repeat(6)}<!--- ${itemPath} -->
${codePadding.repeat(6)}<div v-permission="'${permission}'" class="export-group-item">
${codePadding.repeat(7)}<el-checkbox v-model="checkedMenus['${permission}']" class="export-group-item-label">{{ formatLogTitle('${itemTitle}') }}</el-checkbox>
${codePadding.repeat(7)}<el-button class="export-group-item-route" @click="$router.push({name:'${itemName}'})">E</el-button>
${codePadding.repeat(7)}<div class="export-group-item-content">
`
}

const itemEnd = `${codePadding.repeat(7)}</div>
${codePadding.repeat(6)}</div>
`

module.exports = {
  codePadding,
  templateStart,
  templateEnd,
  groupStart,
  groupEnd,
  itemStart,
  itemEnd
}
