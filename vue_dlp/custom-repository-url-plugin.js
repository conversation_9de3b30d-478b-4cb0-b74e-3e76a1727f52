// custom-repository-url-plugin.js
class CustomRepositoryUrlPlugin {
  apply(compiler) {
    compiler.hooks.emit.tapAsync('CustomRepositoryUrlPlugin', (compilation, callback) => {
      Object.keys(compilation.assets).forEach(fileName => {
        const asset = compilation.assets[fileName];
        let source = asset.source();

        // 检查并替换 repository, author 等信息
        if (typeof source === 'string') {
          // repository信息
          source = source.replace(/repository:\s*\{\s*type:\s*"git"\s*,\s*url:\s*"([^"]+)"\s*\}/g, 'repository:{}');
          // author信息
          source = source.replace(/\s*\{\s*name:\s*"([^"]+)"\s*,\s*email:\s*"([^"]+)"\s*,\s*url:\s*"([^"]+)"\s*\}/g, '{}');
          source = source.replace(/\s*\{\s*name:\s*"([^"]+)"\s*,\s*email:\s*"([^"]+)"\s*\}/g, '{}');
          source = source.replace(/,author:"([^"]+)"/g, ',author:""');
          // 路径信息
          source = source.replace(/_where:"([^"]+)"/g, '_where:""');
          // cookie
          if (fileName.indexOf('index.html') === -1) {
            source = source.replace(/document.cookie/g, 'document.obj.cok')
          }
          
          compilation.assets[fileName] = {
            source: () => source,
            size: () => source.length
          };
        }
      });
      callback();
    });
  }
}

module.exports = CustomRepositoryUrlPlugin;
