{"name": "vue-admin-template", "version": "4.2.1", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "license": "MIT", "scripts": {"dev": "node --max_old_space_size=2048 node_modules/@vue/cli-service/bin/vue-cli-service.js serve --open", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node --max_old_space_size=2048 build/index.js --preview --report", "lint": "eslint --fix --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@antv/g6": "^3.8.5", "@tinymce/tinymce-vue": "^3.0.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "@wchbrad/vue-easy-tree": "^1.0.10", "axios": "0.18.1", "crypto-js": "^4.1.1", "echarts": "^4.7.0", "el-tree-transfer": "^2.4.7", "element-china-area-data": "^6.0.0", "element-ui": "2.15.6", "exceljs": "^4.3.0", "js-cookie": "2.2.0", "js-md5": "0.8.3", "localforage": "^1.10.0", "moment": "^2.24.0", "node-emoji": "1.11.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qrcodejs2": "0.0.2", "screenfull": "4.2.0", "sortablejs": "^1.15.3", "tiff.js": "^1.0.0", "tinymce": "^6.8.2", "upng-js": "2.1.0", "vue": "2.6.14", "vue-count-to": "^1.0.13", "vue-fullscreen": "^2.6.1", "vue-i18n": "^8.14.1", "vue-qr": "^4.0.6", "vue-router": "3.0.6", "vue-seamless-scroll": "^1.1.23", "vuex": "3.1.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.6.0", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.6.3", "@vue/cli-service": "3.6.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "^5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "node-sass": "^4.14.1", "runjs": "^4.3.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "sockjs-client": "1.4.0", "stompjs": "^2.3.3", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "terser-webpack-plugin": "^4.2.3", "vue-template-compiler": "2.6.14", "webpack-obfuscator": "^0.18.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}