import Mock from 'mockjs'
const Random = Mock.Random  // Mock.Random 是一个工具类，用于生成各种随机数据
const terminalList30 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 30; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'remark': Random.cparagraph(),
    'terminal': 'T' + (i + 1),
    'terminalGroup': '终端分组1',
    'user': 'U' + (i + 1),
    'userGroup': '操作员分组1',
    'ruleName': '关键字规则',
    'tacticsName': '策略' + (i + 1),
    'severity': '严重',
    'divulge': 'USB',
    'file': 'gz.doc',
    'sender': 'xiaox',
    'dept': '研发',
    num: i + 1
  }
  terminalList30.push(template)
}

const terminalList10 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 10; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'remark': Random.cparagraph(),
    'terminal': 'T' + (i + 1),
    'terminalGroup': '终端分组1',
    'user': 'U' + (i + 1),
    'userGroup': '操作员分组1',
    'ruleName': '关键字规则',
    'tacticsName': '策略' + (i + 1),
    'severity': '严重',
    'divulge': 'USB',
    'file': 'gz.doc',
    'sender': 'xiaox',
    'dept': '研发',
    num: i + 1
  }
  terminalList10.push(template)
}

const terminalList4 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 4; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'remark': Random.cparagraph(),
    'terminal': 'T' + (i + 1),
    'terminalGroup': '终端分组1',
    'user': 'U' + (i + 1),
    'userGroup': '操作员分组1',
    'ruleName': '关键字规则',
    'tacticsName': '策略' + (i + 1),
    'severity': '严重',
    'divulge': 'USB',
    'file': 'gz.doc',
    'sender': 'xiaox',
    'dept': '研发',
    num: i + 1
  }
  terminalList4.push(template)
}
// 违规严重程度统计报表，初始页面弹框列表
const severityList = [
  {
    id: 1,
    terminalListMessage: terminalList30
  },
  {
    id: 2,
    terminalListMessage: terminalList10
  },
  {
    id: 3,
    terminalListMessage: terminalList4
  }
]

// 违规严重程度统计报表，初始页面
const severityInception = [
  {
    itemValue: {
      trigger: '2',
      all: '30',
      dept: '10',
      user: '4',
      terminal: '4'
    },
    // 饼图 严重程度占比分析
    chartsDivulgeNameDatas: [
      { value: 120, name: '非常严重' },
      { value: 200, name: '严重' },
      { value: 150, name: '一般' },
      { value: 80, name: '轻微' }
    ],
    chartDivulgeNameOption: {
      title: {
        text: '严重程度占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    },
    // 严重程度柱状统计图
    barChartDivulgeData: [120, 200, 150, 80],
    barChartDivulgeOption: {
      title: {
        'text': '严重程度统计图',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      yAxis: {
        type: 'category',
        data: ['非常严重', '严重', '一般', '轻微']
      },
      series: [
        {
          data: [120, 200, 150, 80],
          type: 'bar'
        }
      ]
    },
    // 部门严重程度数量柱状图数据
    barChartData: [120, 200, 150, 80, 100],
    barChartOption: {
      title: {
        'text': '部门严重程度',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      xAxis: [
        {
          type: 'category',
          axisTick: { show: false },
          data: ['研发一部', '研发二部', '研发三部', '研发四部', '测试部']
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: '非常严重',
          type: 'bar',
          barGap: 0,
          emphasis: {
            focus: 'series'
          },
          data: [320, 332, 301, 334, 390]
        },
        {
          name: '严重',
          type: 'bar',
          emphasis: {
            focus: 'series'
          },
          data: [220, 182, 191, 234, 290]
        },
        {
          name: '一般',
          type: 'bar',
          emphasis: {
            focus: 'series'
          },
          data: [150, 232, 201, 154, 190]
        },
        {
          name: '轻微',
          type: 'bar',
          emphasis: {
            focus: 'series'
          },
          data: [98, 77, 101, 99, 40]
        }
      ]
    },
    // 旭日图 终端严重程度数量图
    chartsTerminalDatas: [
      {
        name: 'T1',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      },
      {
        name: 'T2',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      },
      {
        name: 'T3',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      },
      {
        name: 'T4',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      },
      {
        name: 'T5',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      }
    ],
    chartTerminalOption: {
      title: {
        text: '终端严重程度数量图',
        left: 'center',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      series: [
        {
          radius: [0, '62%'],
          label: {
            rotate: 'radial'
          }
        }
      ]
    },
    // 旭日图 操作员严重程度数量图
    chartsUserDatas: [
      {
        name: '小新',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      },
      {
        name: '小红',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      },
      {
        name: '小黑',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      },
      {
        name: '小绿',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      },
      {
        name: '小二',
        children: [
          { name: '非常严重', value: 2 },
          { name: '严重', value: 5 },
          { name: '轻微', value: 4 },
          { name: '一般', value: 4 }
        ]
      }
    ],
    chartUserOption: {
      title: {
        text: '操作员严重程度数量图',
        left: 'center',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      series: [
        {
          radius: [0, '62%'],
          label: {
            rotate: 'radial'
          }
        }
      ]
    },
    // 折线图数据 违规数量趋势分析
    lineChartData: [150, 230, 224, 218, 135, 147, 260],
    lineOption: {
      title: {
        'text': '严重程度趋势分析',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    }
  }
]

// 部门分析
// 违规严重程度统计报表，部门分析（终端/左边列表点击详情）
const severityDeptAnalyzeTerminal = [
  {
    id: 1,
    name: '研发一部(终端)',
    total: 10,
    type: '非常严重、严重、一般'
  },
  {
    id: 2,
    name: '研发二部(终端)',
    total: 4,
    type: '轻微'
  }
]
// 违规严重程度统计报表，部门分析（操作员/左边列表点击详情）
const severityDeptAnalyzeUser = [
  {
    id: 1,
    name: '项目部(操作员)',
    total: 10,
    type: '非常严重、严重、轻微'
  },
  {
    id: 2,
    name: '测试部(操作员)',
    total: 4,
    type: '一般'
  }
]
// 违规严重程度统计报表，部门分析（操作员/右侧详情）
const severityDeptAnalyzeUserInfo = [
  {
    id: 1,
    deptName: '项目部(操作员)',
    total: 10,
    // 部门分析
    deptItemOption: [
      { label: '项目部(操作员)', value: '2人' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 严重程度统计
    chartsUserDatas: [
      { value: 30, name: '严重' },
      { value: 40, name: '非常严重' },
      { value: 15, name: '一般' },
      { value: 25, name: '轻微' }
    ],
    chartUserOption: {
      title: {
        text: '严重程度统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          // radius: '25%',
          // center: ['50%', '30%']
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '测试部(操作员)',
    total: 4,
    // 部门分析
    deptItemOption: [
      { label: '测试部(操作员)', value: '2人' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 严重程度统计
    chartsUserDatas: [
      { value: 30, name: '严重' },
      { value: 40, name: '非常严重' },
      { value: 15, name: '一般' },
      { value: 25, name: '轻微' }
    ],
    chartUserOption: {
      title: {
        text: '严重程度统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          // radius: '25%',
          // center: ['50%', '30%']
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    }
  }
]
// 违规严重程度统计报表，部门分析（终端/右侧详情）
const severityDeptAnalyzeTerminalInfo = [
  {
    id: 1,
    deptName: '研发一部(终端)',
    total: 10,
    // 部门分析
    deptItemOption: [
      { label: '研发二部(终端)', value: '2台' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 严重程度统计
    chartsUserDatas: [
      { value: 30, name: '严重' },
      { value: 40, name: '非常严重' },
      { value: 15, name: '一般' },
      { value: 25, name: '轻微' }
    ],
    chartUserOption: {
      title: {
        text: '严重程度统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          // radius: '25%',
          // center: ['50%', '30%']
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发一部(终端)',
    total: 4,
    // 部门分析
    deptItemOption: [
      { label: '研发二部(终端)', value: '2台' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 严重程度统计
    chartsUserDatas: [
      { value: 30, name: '严重' },
      { value: 40, name: '非常严重' },
      { value: 15, name: '一般' },
      { value: 25, name: '轻微' }
    ],
    chartUserOption: {
      title: {
        text: '严重程度统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          // radius: '25%',
          // center: ['50%', '30%']
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    }
  }
]

// 终端操作员分析
// 违规严重程度统计报表，终端操作员分析（终端/左边列表点击详情）
const severityAnalyzeTerminal = [
  {
    id: 1,
    terminal: 'T1',
    total: 10,
    type: '非常严重、严重、轻微'
  },
  {
    id: 2,
    terminal: 'T2',
    total: 4,
    type: '严重'
  }
]
// 违规严重程度统计报表，终端操作员分析（操作员/左边列表点击详情）
const severityAnalyzeUser = [
  {
    id: 1,
    user: 'U1',
    total: 10,
    type: '非常严重、严重、一般'
  },
  {
    id: 2,
    user: 'U2',
    total: 4,
    type: '轻微'
  }
]
// 违规严重程度统计报表，终端操作员分析（操作员/右侧详情）
const severityAnalyzeUserInfo = [
  {
    id: 1,
    deptName: '研发四部',
    name: 'xiaox',
    total: 10,
    itemOption: [
      { label: 'U1', value: '研发四部' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 严重程度占比图
    chartsUserDatas: [
      { value: 30, name: '严重' },
      { value: 40, name: '非常严重' },
      { value: 15, name: '一般' },
      { value: 25, name: '轻微' }
    ],
    chartUserOption: {
      title: {
        text: '严重程度占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发四部',
    name: 'xiaowu',
    total: 4,
    itemOption: [
      { label: 'U2', value: '研发四部' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 严重程度占比图
    chartsUserDatas: [
      { value: 30, name: '严重' },
      { value: 40, name: '非常严重' },
      { value: 15, name: '一般' },
      { value: 25, name: '轻微' }
    ],
    chartUserOption: {
      title: {
        text: '严重程度占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  }
]
// 违规严重程度统计报表，终端操作员分析（终端/右侧详情）
const severityAnalyzeTerminalInfo = [
  {
    id: 1,
    deptName: '研发一部(终端)',
    name: 'T1',
    total: 10,
    itemOption: [
      { label: 'T1', value: '研发一部' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 严重程度占比图
    chartsUserDatas: [
      { value: 30, name: '严重' },
      { value: 40, name: '非常严重' },
      { value: 15, name: '一般' },
      { value: 25, name: '轻微' }
    ],
    chartUserOption: {
      title: {
        text: '严重程度占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发一部(终端)',
    name: 'T2',
    total: 4,
    itemOption: [
      { label: 'T2', value: '研发四部' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 严重程度占比图
    chartsUserDatas: [
      { value: 30, name: '严重' },
      { value: 40, name: '非常严重' },
      { value: 15, name: '一般' },
      { value: 25, name: '轻微' }
    ],
    chartUserOption: {
      title: {
        text: '严重程度占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  }
]
// 违规严重程度统计报表，趋势分析
const severityAnalyzeOnOffTrend = [
  {
    id: 1,
    // 折线图数据
    lineChartData: [150, 230, 224, 218, 135, 147, 260],
    lineOption: {
      title: {
        'text': '严重程度数量趋势图',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    },
    // 饼图 严重程度数量统计
    chartsWorkDatas: [
      { value: 30, name: '严重' },
      { value: 40, name: '非常严重' },
      { value: 15, name: '一般' },
      { value: 25, name: '轻微' }
    ],
    chartWorkOption: {
      title: {
        text: '严重程度数量统计',
        'subtext': '最近7天',
        left: 'center'
      },
      toolbox: {
        show: false
      },
      series: [
        {
          radius: '25%'
        }
      ]
    },
    // 柱状图 严重程度分析图
    barChartData: [120, 200, 150, 80],
    barChartOption: {
      title: {
        'text': '严重程度分析图',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['非常严重', '严重', '一般', '轻微']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80],
          type: 'bar'
        },
        {
          type: 'line',
          data: [120, 200, 150, 80]
        }
      ]
    }
  }
]

/**
 * 获取违规严重程度统计报表（初始页面弹框列表）
 */
Mock.mock('/severityList/list', 'get', severityList)

/**
 * 获取违规严重程度统计报表（初始页面）
 */
Mock.mock('/severityInception/list', 'get', severityInception)

/**
 * 获取违规严重程度统计报表（部门分析--左边列表---终端信息）
 */
Mock.mock('/severityDeptAnalyzeTerminal/list', 'get', severityDeptAnalyzeTerminal)
/**
 * 获取违规严重程度统计报表（部门分析--左边列表---操作员信息）
 */
Mock.mock('/severityDeptAnalyzeUser/list', 'get', severityDeptAnalyzeUser)
/**
 * 获取违规严重程度统计报表（部门分析--右边详情---操作员信息）
 */
Mock.mock('/severityDeptAnalyzeUserInfo/list', 'get', severityDeptAnalyzeUserInfo)
/**
 * 获取违规严重程度统计报表（（部门分析--右边详情---终端信息）
 */
Mock.mock('/severityDeptAnalyzeTerminalInfo/list', 'get', severityDeptAnalyzeTerminalInfo)

/**
 * 获取违规严重程度统计报表（终端操作员分析--左边列表---终端信息）
 */
Mock.mock('/severityAnalyzeTerminal/list', 'get', severityAnalyzeTerminal)
/**
 * 获取违规严重程度统计报表（终端操作员分析--左边列表---操作员信息）
 */
Mock.mock('/severityAnalyzeUser/list', 'get', severityAnalyzeUser)
/**
 * 获取违规严重程度统计报表（终端操作员分析--右边详情---操作员信息）
 */
Mock.mock('/severityAnalyzeUserInfo/list', 'get', severityAnalyzeUserInfo)
/**
 * 获取违规严重程度统计报表（（终端操作员分析--右边详情---终端信息）
 */
Mock.mock('/severityAnalyzeTerminalInfo/list', 'get', severityAnalyzeTerminalInfo)

/**
 * 获取违规严重程度统计报表（趋势分析页面内容）
 */
Mock.mock('/severityAnalyzeOnOffTrend/list', 'get', severityAnalyzeOnOffTrend)

export default [

]
