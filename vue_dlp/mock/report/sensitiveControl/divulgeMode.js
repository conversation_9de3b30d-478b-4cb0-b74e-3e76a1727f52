import Mock from 'mockjs'
const Random = Mock.Random  // Mock.Random 是一个工具类，用于生成各种随机数据
const terminalList30 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 30; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'remark': Random.cparagraph(),
    'terminal': 'T' + (i + 1),
    'terminalGroup': '终端分组1',
    'user': 'U' + (i + 1),
    'userGroup': '操作员分组1',
    'ruleName': '关键字规则',
    'tacticsName': '策略' + (i + 1),
    'severity': '严重',
    'divulge': 'USB',
    'file': 'gz.doc',
    'sender': 'xiaox',
    'dept': '研发',
    num: i + 1
  }
  terminalList30.push(template)
}

const terminalList10 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 10; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'remark': Random.cparagraph(),
    'terminal': 'T' + (i + 1),
    'terminalGroup': '终端分组1',
    'user': 'U' + (i + 1),
    'userGroup': '操作员分组1',
    'ruleName': '关键字规则',
    'tacticsName': '策略' + (i + 1),
    'severity': '严重',
    'divulge': 'USB',
    'file': 'gz.doc',
    'sender': 'xiaox',
    'dept': '研发',
    num: i + 1
  }
  terminalList10.push(template)
}

const terminalList4 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 4; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'remark': Random.cparagraph(),
    'terminal': 'T' + (i + 1),
    'terminalGroup': '终端分组1',
    'user': 'U' + (i + 1),
    'userGroup': '操作员分组1',
    'ruleName': '关键字规则',
    'tacticsName': '策略' + (i + 1),
    'severity': '严重',
    'divulge': 'USB',
    'file': 'gz.doc',
    'sender': 'xiaox',
    'dept': '研发',
    num: i + 1
  }
  terminalList4.push(template)
}
// 违规严重程度统计报表，初始页面弹框列表
const divulgeModeList = [
  {
    id: 1,
    terminalListMessage: terminalList30
  },
  {
    id: 2,
    terminalListMessage: terminalList10
  },
  {
    id: 3,
    terminalListMessage: terminalList4
  }
]

// 违规严重程度统计报表，初始页面
const divulgeModeInception = [
  {
    itemValue: {
      trigger: '1',
      all: '30',
      dept: '10',
      user: '4',
      terminal: '4'
    },
    // 泄露方式柱状统计图图
    barChartDivulgeData: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
    barChartDivulgeOption: {
      title: {
        'text': '泄露方式统计图',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['即时通讯发送文本内容', '即时通讯发送文件', '即时通讯接收文本内容', '即时通讯下载文件', 'USB复制文件', 'USB剪切文件', 'USB另存为文件', '访问网页文本内容', '网页上传文件', '网页粘贴文本内容', '论坛发帖文本内容', '邮件传输文本内容', '邮件附件内容', '文件打印内容', '本地共享文件', '远程共享文件外发', '网盘上传文件', '网盘下载文件', '光盘刻录文件', 'FTP上传文件', '蓝牙外发文件', 'ADB外发文件', 'MTP外发文件'],
        axisLabel: {
          interval: 0,
          // 倾斜的程度
          rotate: -40
        }
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
          type: 'bar'
        }
      ]
    },
    // 部门泄露数量柱状图数据
    barChartData: [120, 200, 150, 80, 100],
    barChartOption: {
      title: {
        'text': '部门泄露数量图',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['研发一部', '研发二部', '研发三部', '研发四部', '测试部']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 100],
          type: 'bar'
        }
      ]
    },
    // 饼图 终端泄露数量图
    chartsTerminalDatas: [
      { value: 100, name: 'T1' },
      { value: 85, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' },
      { value: 35, name: 'T5' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端泄露数量图',
        left: 'center',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    },
    // 饼图 操作员泄露数量图
    chartsUserDatas: [
      { value: 100, name: '小新' },
      { value: 85, name: '小红' },
      { value: 15, name: '小蓝' },
      { value: 25, name: '小绿' },
      { value: 35, name: '小子' }
    ],
    chartUserOption: {
      title: {
        text: '操作员泄露数量图',
        left: 'center',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    },
    // 折线图数据 违规数量趋势分析
    lineChartData: [150, 230, 224, 218, 135, 147, 260],
    lineOption: {
      title: {
        'text': '违规泄露趋势分析',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    }
  }
]

// 部门分析
// 违规严重程度统计报表，部门分析（终端/左边列表点击详情）
const divulgeModeDeptAnalyzeTerminal = [
  {
    id: 1,
    name: '研发一部(终端)',
    total: 10,
    type: '即时通讯发送文本内容、USB复制文件、文件打印内容'
  },
  {
    id: 2,
    name: '研发二部(终端)',
    total: 4,
    type: '本地共享文件'
  }
]
// 违规严重程度统计报表，部门分析（操作员/左边列表点击详情）
const divulgeModeDeptAnalyzeUser = [
  {
    id: 1,
    name: '项目部(操作员)',
    total: 10,
    type: '网页上传文件、USB复制文件、文件打印内容'
  },
  {
    id: 2,
    name: '测试部(操作员)',
    total: 4,
    type: '光盘刻录文件'
  }
]
// 违规严重程度统计报表，部门分析（操作员/右侧详情）
const divulgeModeDeptAnalyzeUserInfo = [
  {
    id: 1,
    deptName: '项目部(操作员)',
    total: 10,
    // 部门分析
    deptItemOption: [
      { label: '项目部(操作员)', value: '2人' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 泄露方式柱状统计图图
    barChartDivulgeData: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
    barChartDivulgeOption: {
      title: {
        'text': '部门泄露方式统计',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['即时通讯发送文本内容', '即时通讯发送文件', '即时通讯接收文本内容', '即时通讯下载文件', 'USB复制文件', 'USB剪切文件', 'USB另存为文件', '访问网页文本内容', '网页上传文件', '网页粘贴文本内容', '论坛发帖文本内容', '邮件传输文本内容', '邮件附件内容', '文件打印内容', '本地共享文件', '远程共享文件外发', '网盘上传文件', '网盘下载文件', '光盘刻录文件', 'FTP上传文件', '蓝牙外发文件', 'ADB外发文件', 'MTP外发文件'],
        axisLabel: {
          interval: 0,
          // 倾斜的程度
          rotate: -40
        }
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
          type: 'bar'
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '测试部(操作员)',
    total: 4,
    // 部门分析
    deptItemOption: [
      { label: '测试部(操作员)', value: '2人' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 泄露方式柱状统计图图
    barChartDivulgeData: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
    barChartDivulgeOption: {
      title: {
        'text': '部门泄露方式统计',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['即时通讯发送文本内容', '即时通讯发送文件', '即时通讯接收文本内容', '即时通讯下载文件', 'USB复制文件', 'USB剪切文件', 'USB另存为文件', '访问网页文本内容', '网页上传文件', '网页粘贴文本内容', '论坛发帖文本内容', '邮件传输文本内容', '邮件附件内容', '文件打印内容', '本地共享文件', '远程共享文件外发', '网盘上传文件', '网盘下载文件', '光盘刻录文件', 'FTP上传文件', '蓝牙外发文件', 'ADB外发文件', 'MTP外发文件'],
        axisLabel: {
          interval: 0,
          // 倾斜的程度
          rotate: -40
        }
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
          type: 'bar'
        }
      ]
    }
  }
]
// 违规严重程度统计报表，部门分析（终端/右侧详情）
const divulgeModeDeptAnalyzeTerminalInfo = [
  {
    id: 1,
    deptName: '研发一部(终端)',
    total: 10,
    // 部门分析
    deptItemOption: [
      { label: '研发二部(终端)', value: '2台' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 泄露方式柱状统计图图
    barChartDivulgeData: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
    barChartDivulgeOption: {
      title: {
        'text': '部门泄露方式统计',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['即时通讯发送文本内容', '即时通讯发送文件', '即时通讯接收文本内容', '即时通讯下载文件', 'USB复制文件', 'USB剪切文件', 'USB另存为文件', '访问网页文本内容', '网页上传文件', '网页粘贴文本内容', '论坛发帖文本内容', '邮件传输文本内容', '邮件附件内容', '文件打印内容', '本地共享文件', '远程共享文件外发', '网盘上传文件', '网盘下载文件', '光盘刻录文件', 'FTP上传文件', '蓝牙外发文件', 'ADB外发文件', 'MTP外发文件'],
        axisLabel: {
          interval: 0,
          // 倾斜的程度
          rotate: -40
        }
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
          type: 'bar'
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发一部(终端)',
    total: 4,
    // 部门分析
    deptItemOption: [
      { label: '研发二部(终端)', value: '2台' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 泄露方式柱状统计图图
    barChartDivulgeData: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
    barChartDivulgeOption: {
      title: {
        'text': '部门泄露方式统计',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['即时通讯发送文本内容', '即时通讯发送文件', '即时通讯接收文本内容', '即时通讯下载文件', 'USB复制文件', 'USB剪切文件', 'USB另存为文件', '访问网页文本内容', '网页上传文件', '网页粘贴文本内容', '论坛发帖文本内容', '邮件传输文本内容', '邮件附件内容', '文件打印内容', '本地共享文件', '远程共享文件外发', '网盘上传文件', '网盘下载文件', '光盘刻录文件', 'FTP上传文件', '蓝牙外发文件', 'ADB外发文件', 'MTP外发文件'],
        axisLabel: {
          interval: 0,
          // 倾斜的程度
          rotate: -40
        }
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
          type: 'bar'
        }
      ]
    }
  }
]

// 终端操作员分析
// 违规严重程度统计报表，终端操作员分析（终端/左边列表点击详情）
const divulgeModeAnalyzeTerminal = [
  {
    id: 1,
    terminal: 'T1',
    total: 10,
    type: '网页上传文件、USB另存为文件、FTP上传文件'
  },
  {
    id: 2,
    terminal: 'T2',
    total: 4,
    type: '蓝牙外发文件'
  }
]
// 违规泄露方式统计报表，终端操作员分析（操作员/左边列表点击详情）
const divulgeModeAnalyzeUser = [
  {
    id: 1,
    user: 'U1',
    total: 10,
    type: 'ADB外发文件、MTP外发文件、邮件传输文本内容'
  },
  {
    id: 2,
    user: 'U2',
    total: 4,
    type: '文本打印内容'
  }
]
// 违规泄露方式统计报表，终端操作员分析（操作员/右侧详情）
const divulgeModeAnalyzeUserInfo = [
  {
    id: 1,
    deptName: '研发四部',
    name: 'xiaox',
    total: 10,
    itemOption: [
      { label: 'U1', value: '研发四部' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 泄露方式占比图
    chartsUserDatas: [
      { value: 100, name: '即时通讯发送文本内容' },
      { value: 85, name: '即时通讯发送文件' },
      { value: 15, name: '即时通讯接收文本内容' },
      { value: 25, name: '即时通讯下载文件' },
      { value: 35, name: 'USB剪切文件' }
    ],
    chartUserOption: {
      title: {
        text: '泄露方式占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发四部',
    name: 'xiaowu',
    total: 4,
    itemOption: [
      { label: 'U2', value: '研发四部' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 泄露方式占比图
    chartsUserDatas: [
      { value: 100, name: '即时通讯发送文本内容' },
      { value: 85, name: '即时通讯发送文件' },
      { value: 15, name: '即时通讯接收文本内容' },
      { value: 25, name: '即时通讯下载文件' },
      { value: 35, name: 'USB剪切文件' }
    ],
    chartUserOption: {
      title: {
        text: '泄露方式占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    }
  }
]
// 违规泄露方式统计报表，终端操作员分析（终端/右侧详情）
const divulgeModeAnalyzeTerminalInfo = [
  {
    id: 1,
    deptName: '研发一部(终端)',
    name: 'T1',
    total: 10,
    itemOption: [
      { label: 'T1', value: '研发一部' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 泄露方式占比图
    chartsUserDatas: [
      { value: 100, name: '即时通讯发送文本内容' },
      { value: 85, name: '即时通讯发送文件' },
      { value: 15, name: '即时通讯接收文本内容' },
      { value: 25, name: '即时通讯下载文件' },
      { value: 35, name: 'USB剪切文件' }
    ],
    chartUserOption: {
      title: {
        text: '泄露方式占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发一部(终端)',
    name: 'T2',
    total: 4,
    itemOption: [
      { label: 'T2', value: '研发四部' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 泄露方式占比图
    chartsUserDatas: [
      { value: 100, name: '即时通讯发送文本内容' },
      { value: 85, name: '即时通讯发送文件' },
      { value: 15, name: '即时通讯接收文本内容' },
      { value: 25, name: '即时通讯下载文件' },
      { value: 35, name: 'USB剪切文件' }
    ],
    chartUserOption: {
      title: {
        text: '泄露方式占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    }
  }
]
// 违规泄露方式统计报表，趋势分析
const divulgeModeAnalyzeOnOffTrend = [
  {
    id: 1,
    // 折线图数据
    lineChartData: [150, 230, 224, 218, 135, 147, 260],
    lineOption: {
      title: {
        'text': '违规泄露数量趋势图',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    },
    // 泄露方式柱状统计图图
    barChartDivulgeData: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
    barChartDivulgeOption: {
      title: {
        'text': '泄露方式数量图',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['即时通讯发送文本内容', '即时通讯发送文件', '即时通讯接收文本内容', '即时通讯下载文件', 'USB复制文件', 'USB剪切文件', 'USB另存为文件', '访问网页文本内容', '网页上传文件', '网页粘贴文本内容', '论坛发帖文本内容', '邮件传输文本内容', '邮件附件内容', '文件打印内容', '本地共享文件', '远程共享文件外发', '网盘上传文件', '网盘下载文件', '光盘刻录文件', 'FTP上传文件', '蓝牙外发文件', 'ADB外发文件', 'MTP外发文件'],
        axisLabel: {
          interval: 0,
          // 倾斜的程度
          rotate: -40
        }
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 21, 45, 234, 531, 321, 345, 123, 42, 123, 434, 12, 43, 45, 123, 234, 531, 123, 435, 123],
          type: 'bar'
        }
      ]
    }
  }
]

/**
 * 获取违规泄露方式统计报表（初始页面弹框列表）
 */
Mock.mock('/divulgeModeList/list', 'get', divulgeModeList)

/**
 * 获取违规泄露方式统计报表（初始页面）
 */
Mock.mock('/divulgeModeInception/list', 'get', divulgeModeInception)

/**
 * 获取违规泄露方式统计报表（部门分析--左边列表---终端信息）
 */
Mock.mock('/divulgeModeDeptAnalyzeTerminal/list', 'get', divulgeModeDeptAnalyzeTerminal)
/**
 * 获取违规泄露方式统计报表（部门分析--左边列表---操作员信息）
 */
Mock.mock('/divulgeModeDeptAnalyzeUser/list', 'get', divulgeModeDeptAnalyzeUser)
/**
 * 获取违规泄露方式统计报表（部门分析--右边详情---操作员信息）
 */
Mock.mock('/divulgeModeDeptAnalyzeUserInfo/list', 'get', divulgeModeDeptAnalyzeUserInfo)
/**
 * 获取违规泄露方式统计报表（（部门分析--右边详情---终端信息）
 */
Mock.mock('/divulgeModeDeptAnalyzeTerminalInfo/list', 'get', divulgeModeDeptAnalyzeTerminalInfo)

/**
 * 获取违规泄露方式统计报表（终端操作员分析--左边列表---终端信息）
 */
Mock.mock('/divulgeModeAnalyzeTerminal/list', 'get', divulgeModeAnalyzeTerminal)
/**
 * 获取违规泄露方式统计报表（终端操作员分析--左边列表---操作员信息）
 */
Mock.mock('/divulgeModeAnalyzeUser/list', 'get', divulgeModeAnalyzeUser)
/**
 * 获取违规泄露方式统计报表（终端操作员分析--右边详情---操作员信息）
 */
Mock.mock('/divulgeModeAnalyzeUserInfo/list', 'get', divulgeModeAnalyzeUserInfo)
/**
 * 获取违规泄露方式统计报表（（终端操作员分析--右边详情---终端信息）
 */
Mock.mock('/divulgeModeAnalyzeTerminalInfo/list', 'get', divulgeModeAnalyzeTerminalInfo)

/**
 * 获取违规泄露方式统计报表（趋势分析页面内容）
 */
Mock.mock('/divulgeModeAnalyzeOnOffTrend/list', 'get', divulgeModeAnalyzeOnOffTrend)

export default [

]
