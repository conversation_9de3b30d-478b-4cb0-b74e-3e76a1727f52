import Mock from 'mockjs'
const Random = Mock.Random  // Mock.Random 是一个工具类，用于生成各种随机数据
const terminalList30 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 30; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'remark': Random.cparagraph(),
    'terminal': 'T' + (i + 1),
    'terminalGroup': '终端分组1',
    'user': 'U' + (i + 1),
    'userGroup': '操作员分组1',
    'ruleName': '关键字规则',
    'tacticsName': '策略' + (i + 1),
    'severity': '严重',
    'divulge': 'USB',
    'file': 'gz.doc',
    'sender': 'xiaox',
    'dept': '研发',
    num: i + 1
  }
  terminalList30.push(template)
}

const terminalList10 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 10; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'remark': Random.cparagraph(),
    'terminal': 'T' + (i + 1),
    'terminalGroup': '终端分组1',
    'user': 'U' + (i + 1),
    'userGroup': '操作员分组1',
    'ruleName': '关键字规则',
    'tacticsName': '策略' + (i + 1),
    'severity': '严重',
    'divulge': 'USB',
    'file': 'gz.doc',
    'sender': 'xiaox',
    'dept': '研发',
    num: i + 1
  }
  terminalList10.push(template)
}

const terminalList4 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 4; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'remark': Random.cparagraph(),
    'terminal': 'T' + (i + 1),
    'terminalGroup': '终端分组1',
    'user': 'U' + (i + 1),
    'userGroup': '操作员分组1',
    'ruleName': '关键字规则',
    'tacticsName': '策略' + (i + 1),
    'severity': '严重',
    'divulge': 'USB',
    'file': 'gz.doc',
    'sender': 'xiaox',
    'dept': '研发',
    num: i + 1
  }
  terminalList4.push(template)
}
// 敏感内容征兆报表，初始页面弹框列表
const ruleTypeList = [
  {
    id: 1,
    terminalListMessage: terminalList30
  },
  {
    id: 2,
    terminalListMessage: terminalList10
  },
  {
    id: 3,
    terminalListMessage: terminalList4
  }
]

const ruleTypeInception = [
  {
    itemValue: {
      all: '30',
      dept: '10',
      user: '4',
      terminal: '4'
    },
    // 规则类型柱状统计图
    barChartDivulgeData: [120, 200, 150, 80, 30, 130, 200],
    barChartDivulgeOption: {
      title: {
        'text': '规则类型分析',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      yAxis: {
        type: 'category',
        data: ['文档分类规则', '文件指纹规则', '数据库指纹规则', '数据标识符规则', '文件属性规则', '关键字规则', '源代码规则']
      },
      series: [
        {
          data: [120, 200, 150, 80, 30, 130, 200],
          type: 'bar'
        }
      ]
    },
    // 部门规则类型数量柱状图数据
    barChartData: [120, 200, 150, 80, 100],
    barChartOption: {
      title: {
        'text': '部门规则类型数量图',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['研发一部', '研发二部', '研发三部', '研发四部', '测试部']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 100],
          type: 'bar'
        }
      ]
    },
    // 饼图 终端规则类型数量图
    chartsTerminalDatas: [
      { value: 100, name: 'T1' },
      { value: 85, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' },
      { value: 35, name: 'T5' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端规则类型数量图',
        left: 'center',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    },
    // 饼图 操作员规则类型数量图
    chartsUserDatas: [
      { value: 100, name: '小新' },
      { value: 85, name: '小红' },
      { value: 15, name: '小蓝' },
      { value: 25, name: '小绿' },
      { value: 35, name: '小子' }
    ],
    chartUserOption: {
      title: {
        text: '操作员规则类型数量图',
        left: 'center',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    },
    // 折线图数据 违规数量趋势分析
    lineChartData: [150, 230, 224, 218, 135, 147, 260],
    lineOption: {
      title: {
        'text': '规则类型趋势分析',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    }
  }
]
// 规则类型分析
// 违规规则类型统计报表，规则分析（终端/左边列表）
const ruleAnalyzeRuleTypeLeftListTerminal = [
  {
    id: 1,
    name: '关键字规则（终端）',
    total: 10
  },
  {
    id: 2,
    name: '文件属性规则',
    total: 10
  },
  {
    id: 3,
    name: '数据标识符规则',
    total: 10
  },
  {
    id: 4,
    name: '源代码规则',
    total: 10
  },
  {
    id: 5,
    name: '文档分类规则',
    total: 10
  },
  {
    id: 6,
    name: '文档指纹规则',
    total: 10
  },
  {
    id: 7,
    name: '数据库指纹规则',
    total: 4
  }
]
// 违规规则类型统计报表，规则分析（操作员/左边列表）
const ruleAnalyzeRuleTypeLeftListUser = [
  {
    id: 1,
    name: '关键字规则(操作员)',
    total: 10
  },
  {
    id: 2,
    name: '文件属性规则',
    total: 10
  },
  {
    id: 3,
    name: '数据标识符规则',
    total: 10
  },
  {
    id: 4,
    name: '源代码规则',
    total: 10
  },
  {
    id: 5,
    name: '文档分类规则',
    total: 10
  },
  {
    id: 6,
    name: '文档指纹规则',
    total: 10
  },
  {
    id: 7,
    name: '数据库指纹规则',
    total: 4
  }
]
// 违规规则类型统计报表，规则分析（终端/左边列表点击详情）
const ruleAnalyzeRuleTypeRightInfo = [
  {
    id: 1,
    total: 10,
    itemOption: [
      { label: '关键字规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 终端占比图统计
    chartsTerminalDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsUserDatas: [
      { value: 30, name: '小新' },
      { value: 40, name: '小红' },
      { value: 15, name: '小黄' },
      { value: 25, name: '小黑' }
    ],
    chartUserOption: {
      title: {
        text: '操作员占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsRuleNameDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 25, name: '规则4' }
    ],
    chartRuleNameOption: {
      title: {
        text: '规则名称占比图',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 2,
    total: 10,
    itemOption: [
      { label: '文件属性规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 终端占比图统计
    chartsTerminalDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsUserDatas: [
      { value: 30, name: '小新' },
      { value: 40, name: '小红' },
      { value: 15, name: '小黄' },
      { value: 25, name: '小黑' }
    ],
    chartUserOption: {
      title: {
        text: '操作员占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsRuleNameDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 25, name: '规则4' }
    ],
    chartRuleNameOption: {
      title: {
        text: '规则名称占比图',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 3,
    total: 10,
    itemOption: [
      { label: '数据标识符规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 终端占比图统计
    chartsTerminalDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsUserDatas: [
      { value: 30, name: '小新' },
      { value: 40, name: '小红' },
      { value: 15, name: '小黄' },
      { value: 25, name: '小黑' }
    ],
    chartUserOption: {
      title: {
        text: '操作员占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsRuleNameDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 25, name: '规则4' }
    ],
    chartRuleNameOption: {
      title: {
        text: '规则名称占比图',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 4,
    total: 10,
    itemOption: [
      { label: '源代码规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 终端占比图统计
    chartsTerminalDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsUserDatas: [
      { value: 30, name: '小新' },
      { value: 40, name: '小红' },
      { value: 15, name: '小黄' },
      { value: 25, name: '小黑' }
    ],
    chartUserOption: {
      title: {
        text: '操作员占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsRuleNameDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 25, name: '规则4' }
    ],
    chartRuleNameOption: {
      title: {
        text: '规则名称占比图',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 5,
    total: 10,
    itemOption: [
      { label: '文档分类规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 终端占比图统计
    chartsTerminalDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsUserDatas: [
      { value: 30, name: '小新' },
      { value: 40, name: '小红' },
      { value: 15, name: '小黄' },
      { value: 25, name: '小黑' }
    ],
    chartUserOption: {
      title: {
        text: '操作员占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsRuleNameDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 25, name: '规则4' }
    ],
    chartRuleNameOption: {
      title: {
        text: '规则名称占比图',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 6,
    total: 10,
    itemOption: [
      { label: '文档指纹规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 终端占比图统计
    chartsTerminalDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsUserDatas: [
      { value: 30, name: '小新' },
      { value: 40, name: '小红' },
      { value: 15, name: '小黄' },
      { value: 25, name: '小黑' }
    ],
    chartUserOption: {
      title: {
        text: '操作员占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsRuleNameDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 25, name: '规则4' }
    ],
    chartRuleNameOption: {
      title: {
        text: '规则名称占比图',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 7,
    total: 4,
    itemOption: [
      { label: '数据库指纹规则', value: '' },
      { label: '敏感规则数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 终端占比图统计
    chartsTerminalDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsUserDatas: [
      { value: 30, name: '小新' },
      { value: 40, name: '小红' },
      { value: 15, name: '小黄' },
      { value: 25, name: '小黑' }
    ],
    chartUserOption: {
      title: {
        text: '操作员占比图统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 饼图 操作员占比图统计
    chartsRuleNameDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 25, name: '规则4' }
    ],
    chartRuleNameOption: {
      title: {
        text: '规则名称占比图',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  }
]
// 部门分析
// 违规规则类型统计报表，部门分析（终端/左边列表点击详情）
const ruleTypeDeptAnalyzeTerminal = [
  {
    id: 1,
    name: '研发一部(终端)',
    total: 10,
    type: '文档分类规则、文件指纹规则、数据库指纹规则'
  },
  {
    id: 2,
    name: '研发二部(终端)',
    total: 4,
    type: '数据库指纹规则'
  }
]
// 违规规则类型统计报表，部门分析（操作员/左边列表点击详情）
const ruleTypeDeptAnalyzeUser = [
  {
    id: 1,
    name: '项目部(操作员)',
    total: 10,
    type: '源代码规则、文件指纹规则、数据库指纹规则'
  },
  {
    id: 2,
    name: '测试部(操作员)',
    total: 4,
    type: '关键字规则'
  }
]
// 违规规则类型统计报表，部门分析（操作员/右侧详情）
const ruleTypeDeptAnalyzeUserInfo = [
  {
    id: 1,
    deptName: '项目部(操作员)',
    total: 10,
    // 部门分析
    deptItemOption: [
      { label: '项目部(操作员)', value: '2人' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 规则类型统计
    chartsUserDatas: [
      { value: 30, name: '文件属性规则' },
      { value: 40, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 15, name: '数据标识符规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 15, name: '文件指纹规则' },
      { value: 25, name: '文档分类规则' }
    ],
    chartUserOption: {
      title: {
        text: '规则类型统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '测试部(操作员)',
    total: 4,
    // 部门分析
    deptItemOption: [
      { label: '测试部(操作员)', value: '2人' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 规则类型统计
    chartsUserDatas: [
      { value: 30, name: '文件属性规则' },
      { value: 40, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 15, name: '数据标识符规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 15, name: '文件指纹规则' },
      { value: 25, name: '文档分类规则' }
    ],
    chartUserOption: {
      title: {
        text: '规则类型统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  }
]
// 违规规则类型统计报表，部门分析（终端/右侧详情）
const ruleTypeDeptAnalyzeTerminalInfo = [
  {
    id: 1,
    deptName: '研发一部(终端)',
    total: 10,
    // 部门分析
    deptItemOption: [
      { label: '研发二部(终端)', value: '2台' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 规则类型统计
    chartsUserDatas: [
      { value: 30, name: '文件属性规则' },
      { value: 40, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 15, name: '数据标识符规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 15, name: '文件指纹规则' },
      { value: 25, name: '文档分类规则' }
    ],
    chartUserOption: {
      title: {
        text: '规则类型统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发一部(终端)',
    total: 4,
    // 部门分析
    deptItemOption: [
      { label: '研发二部(终端)', value: '2台' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 规则类型统计
    chartsUserDatas: [
      { value: 30, name: '文件属性规则' },
      { value: 40, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 15, name: '数据标识符规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 15, name: '文件指纹规则' },
      { value: 25, name: '文档分类规则' }
    ],
    chartUserOption: {
      title: {
        text: '规则类型统计',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  }
]

// 终端操作员分析
// 违规规则类型统计报表，终端操作员分析（终端/左边列表点击详情）
const ruleTypeAnalyzeTerminal = [
  {
    id: 1,
    terminal: 'T1',
    total: 10,
    type: '文档分类规则、文件指纹规则、数据库指纹规则'
  },
  {
    id: 2,
    terminal: 'T2',
    total: 4,
    type: '数据库指纹规则'
  }
]
// 违规规则类型统计报表，终端操作员分析（操作员/左边列表点击详情）
const ruleTypeAnalyzeUser = [
  {
    id: 1,
    user: 'U1',
    total: 10,
    type: '源代码规则、文件指纹规则、数据库指纹规则'
  },
  {
    id: 2,
    user: 'U2',
    total: 4,
    type: '关键字规则'
  }
]
// 违规规则类型统计报表，终端操作员分析（操作员/右侧详情）
const ruleTypeAnalyzeUserInfo = [
  {
    id: 1,
    deptName: '研发四部',
    name: 'xiaox',
    total: 10,
    itemOption: [
      { label: 'U1', value: '研发四部' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 严重程度占比图
    chartsUserDatas: [
      { value: 30, name: '文件属性规则' },
      { value: 40, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 15, name: '数据标识符规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 15, name: '文件指纹规则' },
      { value: 25, name: '文档分类规则' }
    ],
    chartUserOption: {
      title: {
        text: '规则类型占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发四部',
    name: 'xiaowu',
    total: 4,
    itemOption: [
      { label: 'U2', value: '研发四部' },
      { label: '违规次数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 严重程度占比图
    chartsUserDatas: [
      { value: 30, name: '文件属性规则' },
      { value: 40, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 15, name: '数据标识符规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 15, name: '文件指纹规则' },
      { value: 25, name: '文档分类规则' }
    ],
    chartUserOption: {
      title: {
        text: '规则类型占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    }
  }
]
// 违规规则类型统计报表，终端操作员分析（终端/右侧详情）
const ruleTypeAnalyzeTerminalInfo = [
  {
    id: 1,
    deptName: '研发一部(终端)',
    name: 'T1',
    total: 10,
    itemOption: [
      { label: 'T1', value: '研发一部' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 严重程度占比图
    chartsUserDatas: [
      { value: 30, name: '文件属性规则' },
      { value: 40, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 15, name: '数据标识符规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 15, name: '文件指纹规则' },
      { value: 25, name: '文档分类规则' }
    ],
    chartUserOption: {
      title: {
        text: '规则类型占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发一部(终端)',
    name: 'T2',
    total: 4,
    itemOption: [
      { label: 'T2', value: '研发四部' },
      { label: '违规次数', value: '10次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 严重程度占比图
    chartsUserDatas: [
      { value: 30, name: '文件属性规则' },
      { value: 40, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 15, name: '数据标识符规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 15, name: '文件指纹规则' },
      { value: 25, name: '文档分类规则' }
    ],
    chartUserOption: {
      title: {
        text: '规则类型占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    }
  }
]
// 违规规则类型统计报表，趋势分析
const ruleTypeAnalyzeOnOffTrend = [
  {
    id: 1,
    lineChartData: [150, 230, 224, 218, 135, 147, 260],
    lineOption: {
      title: {
        'text': '规则类型数量趋势图',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    },
    // 饼图规则类型数量统计
    chartsWorkDatas: [
      { value: 30, name: '文件属性规则' },
      { value: 40, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 15, name: '数据标识符规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 15, name: '文件指纹规则' },
      { value: 25, name: '文档分类规则' }
    ],
    chartWorkOption: {
      title: {
        text: '规则类型数量统计',
        'subtext': '最近7天',
        left: 'center'
      },
      toolbox: {
        show: false
      },
      series: [
        {
          radius: '25%'
        }
      ]
    },
    // 柱状图 严重程度分析图
    barChartData: [120, 200, 150, 80, 12, 234, 321],
    barChartOption: {
      title: {
        'text': '规则类型分析图',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      yAxis: {
        type: 'category',
        data: ['文件属性规则', '源代码规则', '关键字规则', '数据标识符规则', '数据库指纹规则', '文件指纹规则', '文档分类规则']
      },
      series: [
        {
          data: [120, 200, 150, 80, 12, 234, 321],
          type: 'bar'
        }
      ]
    }
  }
]

/**
 * 获取违规规则类型统计报表（初始页面弹框列表）
 */
Mock.mock('/ruleTypeList/list', 'get', ruleTypeList)

/**
 * 获取违规规则类型统计报表（初始页面）
 */
Mock.mock('/ruleTypeInception/list', 'get', ruleTypeInception)
/**
 * 获取违规规则类型统计报表（规则分析--左边列表---终端信息）
 */
Mock.mock('/ruleAnalyzeRuleTypeLeftListTerminal/list', 'get', ruleAnalyzeRuleTypeLeftListTerminal)
/**
 * 获取违规规则类型统计报表（规则分析--左边列表---操作员信息）
 */
Mock.mock('/ruleAnalyzeRuleTypeLeftListUser/list', 'get', ruleAnalyzeRuleTypeLeftListUser)
/**
 * 获取违规规则类型统计报表（规则分析--右边详情）
 */
Mock.mock('/ruleAnalyzeRuleTypeRightInfo/list', 'get', ruleAnalyzeRuleTypeRightInfo)

/**
 * 获取违规规则类型统计报表（部门分析--左边列表---终端信息）
 */
Mock.mock('/ruleTypeDeptAnalyzeTerminal/list', 'get', ruleTypeDeptAnalyzeTerminal)
/**
 * 获取违规规则类型统计报表（部门分析--左边列表---操作员信息）
 */
Mock.mock('/ruleTypeDeptAnalyzeUser/list', 'get', ruleTypeDeptAnalyzeUser)
/**
 * 获取违规规则类型统计报表（部门分析--右边详情---操作员信息）
 */
Mock.mock('/ruleTypeDeptAnalyzeUserInfo/list', 'get', ruleTypeDeptAnalyzeUserInfo)
/**
 * 获取违规规则类型统计报表（（部门分析--右边详情---终端信息）
 */
Mock.mock('/ruleTypeDeptAnalyzeTerminalInfo/list', 'get', ruleTypeDeptAnalyzeTerminalInfo)

/**
 * 获取违规规则类型统计报表（终端操作员分析--左边列表---终端信息）
 */
Mock.mock('/ruleTypeAnalyzeTerminal/list', 'get', ruleTypeAnalyzeTerminal)
/**
 * 获取违规规则类型统计报表（终端操作员分析--左边列表---操作员信息）
 */
Mock.mock('/ruleTypeAnalyzeUser/list', 'get', ruleTypeAnalyzeUser)
/**
 * 获取违规规则类型统计报表（终端操作员分析--右边详情---操作员信息）
 */
Mock.mock('/ruleTypeAnalyzeUserInfo/list', 'get', ruleTypeAnalyzeUserInfo)
/**
 * 获取违规规则类型统计报表（（终端操作员分析--右边详情---终端信息）
 */
Mock.mock('/ruleTypeAnalyzeTerminalInfo/list', 'get', ruleTypeAnalyzeTerminalInfo)

/**
 * 获取违规规则类型统计报表（趋势分析页面内容）
 */
Mock.mock('/ruleTypeAnalyzeOnOffTrend/list', 'get', ruleTypeAnalyzeOnOffTrend)

export default [

]
