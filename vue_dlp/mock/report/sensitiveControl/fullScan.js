import Mock from 'mockjs'
const Random = Mock.Random  // Mock.Random 是一个工具类，用于生成各种随机数据

// 全盘扫描文件报表  （初始页面）

const terminalList30 = [] // 违规文件总数
for (let i = 0; i < 30; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    taskNum: i + 1, // 任务编号
    terminal: 'T1',
    terminalGroup: '终端分组1',
    statisticalType: '按终端统计',
    severity: '非常严重',
    ruleType: '关键字规则',
    ruleName: '规则' + i,
    fileName: '策略' + i,
    filePath: 'D:/file' + i,
    time: Random.date(), // 生成一个随机日期,可加参数定义日期格式
    remark: Random.cparagraph(),
    dept: '研发'
  }
  terminalList30.push(template)
}

const terminalList10 = [] // 非常严重文件数
for (let i = 0; i < 10; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    taskNum: i + 1, // 任务编号
    terminal: 'T1',
    terminalGroup: '终端分组1',
    statisticalType: '按终端统计',
    severity: '非常严重',
    ruleType: '关键字规则',
    ruleName: '规则' + i,
    fileName: '策略' + i,
    filePath: 'D:/file' + i,
    time: Random.date(), // 生成一个随机日期,可加参数定义日期格式
    remark: Random.cparagraph(),
    dept: '研发'
  }
  terminalList10.push(template)
}

const terminalList4 = [] // 严重文件数
for (let i = 0; i < 4; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    taskNum: i + 1, // 任务编号
    terminal: 'T1',
    terminalGroup: '终端分组1',
    statisticalType: '按终端统计',
    severity: '非常严重',
    ruleType: '关键字规则',
    ruleName: '规则' + i,
    fileName: '策略' + i,
    filePath: 'D:/file' + i,
    time: Random.date(), // 生成一个随机日期,可加参数定义日期格式
    remark: Random.cparagraph(),
    dept: '研发'
  }
  terminalList4.push(template)
}
// 敏感内容征兆报表，初始页面弹框列表
const fullScanInceptionDialog = [
  {
    id: 1,
    terminalListMessage: terminalList30
  },
  {
    id: 2,
    terminalListMessage: terminalList10
  },
  {
    id: 3,
    terminalListMessage: terminalList4
  }
]

const fullScanInception = [
  {
    itemValue: {
      all: '30',
      dept: '10',
      user: '4',
      terminal: '4'
    },
    // 终端敏感文件分析，柱状图
    barChartDivulgeData: [220, 210, 200, 190, 180, 170, 160, 150, 120, 110, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10],
    barChartDivulgeOption: {
      title: {
        'text': '终端敏感文件分析',
        'subtext': '前20名'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12', 'T13', 'T14', 'T15', 'T16', 'T17', 'T18', 'T19', 'T20']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [220, 210, 200, 190, 180, 170, 160, 150, 120, 110, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10],
          type: 'bar'
        }
      ]
    },
    // 部门风险行为数量柱状图数据
    barChartData: [120, 200, 150, 80, 100],
    barChartOption: {
      title: {
        'text': '部门敏感文件分析',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['研发一部', '研发二部', '研发三部', '研发四部', '测试部']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 100],
          type: 'bar'
        }
      ]
    },
    // 饼图 敏感文件规则分析
    chartsRulelDatas: [
      { value: 100, name: '文件指纹规则' },
      { value: 85, name: '文档分类规则' },
      { value: 15, name: '数据库指纹规则' },
      { value: 25, name: '数据标识规则' },
      { value: 34, name: '源代码规则' },
      { value: 15, name: '关键字规则' },
      { value: 35, name: '文件属性规则' }
    ],
    chartRuleOption: {
      title: {
        text: '敏感文件规则分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    },
    // 饼图 敏感文件严重程度分析
    chartsSeverityDatas: [
      { value: 1048, name: '非常严重' },
      { value: 735, name: '严重' },
      { value: 580, name: '一般' },
      { value: 484, name: '轻微' }
    ],
    chartSeverityOption: {
      title: {
        text: '敏感文件严重程度分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: ['25%', '30%']
        }
      ]
    }
  }
]
const terminalAlysicList4 = [] // 终端敏感文件分析
for (let i = 0; i < 4; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i + 1, // 数字从当前开始，后续+1
    terminal: 'T' + (i + 1),
    dept: '研发',
    total: i + 10
  }
  terminalAlysicList4.push(template)
}
// 终端敏感文件分析
// 全盘扫描文件报表，终端敏感文件分析（终端/左边列表）
const terminalAnalyzeFullScanLeftList = terminalAlysicList4

// 全盘扫描文件报表，终端敏感文件分析（终端/左边列表点击详情）
const terminalAnalyzeFullScanRightInfo = [
  {
    id: 1,
    deptName: '研发三部(终端)',
    total: 10,
    itemOption: [
      { label: 'T1', value: '研发三部(终端)' },
      { label: '敏感文件数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 严重程度占比图
    chartDatas: [
      { value: 1048, name: '非常严重' },
      { value: 735, name: '严重' },
      { value: 580, name: '一般' },
      { value: 484, name: '轻微' }
    ],
    chartOption: {
      title: {
        text: '终端严重程度占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: ['25%', '30%']
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发四部(终端)',
    total: 10,
    itemOption: [
      { label: 'T2', value: '研发四部(终端)' },
      { label: '敏感文件数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 严重程度占比图
    chartDatas: [
      { value: 1048, name: '非常严重' },
      { value: 735, name: '严重' },
      { value: 580, name: '一般' },
      { value: 484, name: '轻微' }
    ],
    chartOption: {
      title: {
        text: '终端严重程度占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: ['25%', '30%']
        }
      ]
    }
  },
  {
    id: 3,
    deptName: '测试部(终端)',
    total: 10,
    itemOption: [
      { label: 'T3', value: '测试部(终端)' },
      { label: '敏感文件数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 严重程度占比图
    chartDatas: [
      { value: 1048, name: '非常严重' },
      { value: 735, name: '严重' },
      { value: 580, name: '一般' },
      { value: 484, name: '轻微' }
    ],
    chartOption: {
      title: {
        text: '终端严重程度占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: ['25%', '30%']
        }
      ]
    }
  },
  {
    id: 4,
    deptName: '市场部(终端)',
    total: 4,
    itemOption: [
      { label: 'T4', value: '市场部(终端)' },
      { label: '敏感文件数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 严重程度占比图
    chartDatas: [
      { value: 1048, name: '非常严重' },
      { value: 735, name: '严重' },
      { value: 580, name: '一般' },
      { value: 484, name: '轻微' }
    ],
    chartOption: {
      title: {
        text: '终端严重程度占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: ['25%', '30%']
        }
      ]
    }
  }
]

// 全盘扫描文件报表，部门分析（终端/左边列表）
const deptAnalyzeFullScanLeftList = [
  {
    id: 1,
    terminal: 'T1',
    terminalNum: '5台',
    name: '研发三部(终端)',
    total: 10
  },
  {
    id: 2,
    terminal: 'T2',
    terminalNum: '3台',
    name: '研发四部(终端)',
    total: 4
  }
]

// 全盘扫描文件报表，部门分析（终端/左边列表点击详情）
const deptAnalyzeFullScanRightInfo = [
  {
    id: 1,
    deptName: '研发三部(终端)',
    total: 10,
    deptItemOption: [
      { label: '研发三部(终端)', value: '5台' },
      { label: '敏感文件数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 规则类型统计
    chartsDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 15, name: 'T4' },
      { value: 15, name: 'T5' }
    ],
    chartsOption: {
      title: {
        text: '部门敏感文件终端占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 2,
    deptName: '研发四部(终端)',
    total: 4,
    deptItemOption: [
      { label: '研发三部(终端)', value: '2台' },
      { label: '敏感文件数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 规则类型统计
    chartsDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' }
    ],
    chartsOption: {
      title: {
        text: '部门敏感文件终端占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  }
]

// 全盘扫描文件报表，规则分析（终端/左边列表）
const ruleAnalyzeFullScanLeftList = [
  {
    id: 1,
    name: '关键字规则',
    total: 10
  },
  {
    id: 2,
    name: '文件属性规则',
    total: 10
  },
  {
    id: 3,
    name: '数据标识符规则',
    total: 10
  },
  {
    id: 4,
    name: '源代码规则',
    total: 10
  },
  {
    id: 5,
    name: '文档分类规则',
    total: 10
  },
  {
    id: 6,
    name: '文档指纹规则',
    total: 10
  },
  {
    id: 7,
    name: '数据库指纹规则',
    total: 4
  }
]

// 全盘扫描文件报表，规则分析（终端/左边列表点击详情）
const ruleAnalyzeFullScanRightInfo = [
  {
    id: 1,
    total: 10,
    itemOption: [
      { label: '关键字规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 规则类型统计
    chartsDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 15, name: '规则4' },
      { value: 15, name: '规则5' },
      { value: 15, name: '规则6' },
      { value: 25, name: '规则7' }
    ],
    chartsOption: {
      title: {
        text: '规则名称占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 2,
    total: 10,
    itemOption: [
      { label: '文件属性规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 规则类型统计
    chartsDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 15, name: '规则4' },
      { value: 15, name: '规则5' },
      { value: 15, name: '规则6' },
      { value: 25, name: '规则7' }
    ],
    chartsOption: {
      title: {
        text: '规则名称占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 3,
    total: 10,
    itemOption: [
      { label: '数据标识符规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 规则类型统计
    chartsDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 15, name: '规则4' },
      { value: 15, name: '规则5' },
      { value: 15, name: '规则6' },
      { value: 25, name: '规则7' }
    ],
    chartsOption: {
      title: {
        text: '规则名称占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 4,
    total: 10,
    itemOption: [
      { label: '源代码规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 规则类型统计
    chartsDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 15, name: '规则4' },
      { value: 15, name: '规则5' },
      { value: 15, name: '规则6' },
      { value: 25, name: '规则7' }
    ],
    chartsOption: {
      title: {
        text: '规则名称占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 5,
    total: 10,
    itemOption: [
      { label: '文档分类规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 规则类型统计
    chartsDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 15, name: '规则4' },
      { value: 15, name: '规则5' },
      { value: 15, name: '规则6' },
      { value: 25, name: '规则7' }
    ],
    chartsOption: {
      title: {
        text: '规则名称占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 6,
    total: 10,
    itemOption: [
      { label: '文档指纹规则', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 规则类型统计
    chartsDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 15, name: '规则4' },
      { value: 15, name: '规则5' },
      { value: 15, name: '规则6' },
      { value: 25, name: '规则7' }
    ],
    chartsOption: {
      title: {
        text: '规则名称占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 7,
    total: 4,
    itemOption: [
      { label: '数据库指纹规则', value: '' },
      { label: '敏感规则数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 规则类型统计
    chartsDatas: [
      { value: 30, name: '规则1' },
      { value: 40, name: '规则2' },
      { value: 15, name: '规则3' },
      { value: 15, name: '规则4' },
      { value: 15, name: '规则5' },
      { value: 15, name: '规则6' },
      { value: 25, name: '规则7' }
    ],
    chartsOption: {
      title: {
        text: '规则名称占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  }
]

// 严重程度

// 全盘扫描文件报表，严重程度分析（终端/左边列表）
const severityAnalyzeFullScanLeftList = [
  {
    id: 1,
    name: '轻微',
    total: 10
  },
  {
    id: 2,
    name: '一般',
    total: 10
  },
  {
    id: 3,
    name: '严重',
    total: 10
  },
  {
    id: 4,
    name: '非常严重',
    total: 4
  }
]

// 全盘扫描文件报表，严重程度（终端/左边列表点击详情）
const severityAnalyzeFullScanRightInfo = [
  {
    id: 1,
    total: 10,
    itemOption: [
      { label: '轻微', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 敏感文件违规占比分析
    chartsDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 15, name: 'T4' },
      { value: 15, name: 'T5' },
      { value: 15, name: 'T6' },
      { value: 25, name: 'T7' }
    ],
    chartsOption: {
      title: {
        text: '敏感文件违规占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 2,
    total: 10,
    itemOption: [
      { label: '一般', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 敏感文件违规占比分析
    chartsDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 15, name: 'T4' },
      { value: 15, name: 'T5' },
      { value: 15, name: 'T6' },
      { value: 25, name: 'T7' }
    ],
    chartsOption: {
      title: {
        text: '敏感文件违规占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 3,
    total: 10,
    itemOption: [
      { label: '严重', value: '' },
      { label: '敏感规则数', value: '10次' }
    ],
    terminalListMessage: terminalList10,
    // 饼图 敏感文件违规占比分析
    chartsDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 15, name: 'T4' },
      { value: 15, name: 'T5' },
      { value: 15, name: 'T6' },
      { value: 25, name: 'T7' }
    ],
    chartsOption: {
      title: {
        text: '敏感文件违规占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  },
  {
    id: 4,
    total: 4,
    itemOption: [
      { label: '非常严重', value: '' },
      { label: '敏感规则数', value: '4次' }
    ],
    terminalListMessage: terminalList4,
    // 饼图 敏感文件违规占比分析
    chartsDatas: [
      { value: 30, name: 'T1' },
      { value: 40, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 15, name: 'T4' },
      { value: 15, name: 'T5' },
      { value: 15, name: 'T6' },
      { value: 25, name: 'T7' }
    ],
    chartsOption: {
      title: {
        text: '敏感文件违规占比分析',
        left: 'center',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '25%'
        }
      ]
    }
  }
]

/**
 * 获取全盘扫描文件报表（初始页面弹框列表）
 */
Mock.mock('/diskScanSensitive/dialogList', 'get', fullScanInceptionDialog)
/**
 * 获取全盘扫描文件报表（初始页面）
 */
Mock.mock('/diskScanSensitive/initialList', 'get', fullScanInception)
/**
 * 获取全盘扫描文件报表（终端敏感文件分析--左边列表）
 */
Mock.mock('/diskScanSensitive/sensitiveFile/list', 'get', terminalAnalyzeFullScanLeftList)
/**
 * 获取全盘扫描文件报表（终端敏感文件分析--右边详情）
 */
Mock.mock('/diskScanSensitive/sensitiveFile/info', 'get', terminalAnalyzeFullScanRightInfo)
/**
 * 获取全盘扫描文件报表（部门分析--左边列表）
 */
Mock.mock('/diskScanSensitive/dept/list', 'get', deptAnalyzeFullScanLeftList)
/**
 * 获取全盘扫描文件报表（部门分析--右边详情）
 */
Mock.mock('/diskScanSensitive/dept/info', 'get', deptAnalyzeFullScanRightInfo)
/**
 * 获取全盘扫描文件报表（规则类型分析--左边列表）
 */
Mock.mock('/diskScanSensitive/rule/list', 'get', ruleAnalyzeFullScanLeftList)
/**
 * 获取全盘扫描文件报表（规则类型分析--右边详情）
 */
Mock.mock('/diskScanSensitive/rule/info', 'get', ruleAnalyzeFullScanRightInfo)

/**
 * 获取全盘扫描文件报表（严重程度--左边列表）
 */
Mock.mock('/diskScanSensitive/severity/list', 'get', severityAnalyzeFullScanLeftList)
/**
 * 获取全盘扫描文件报表（严重程度--右边详情）
 */
Mock.mock('/diskScanSensitive/severity/info', 'get', severityAnalyzeFullScanRightInfo)

export default [

]
