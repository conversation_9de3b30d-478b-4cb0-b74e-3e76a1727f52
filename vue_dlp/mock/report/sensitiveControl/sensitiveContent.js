import Mock from 'mockjs'
const Random = Mock.Random  // Mock.Random 是一个工具类，用于生成各种随机数据

const terminalList30 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 30; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'riskBehavior': Random.cparagraph(),
    'dept': '研发',
    'terminal': 'T1',
    'terminalGroup': '终端分组1',
    'user': 'U1',
    'userGroup': '操作员分组1',
    'num': i + 1
  }
  terminalList30.push(template)
}

const terminalList10 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 10; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'riskBehavior': Random.cparagraph(),
    'dept': '研发',
    'terminal': 'T1',
    'terminalGroup': '终端分组1',
    'user': 'U1',
    'userGroup': '操作员分组1',
    'num': i + 1
  }
  terminalList10.push(template)
}

const terminalList4 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 4; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'riskBehavior': Random.cparagraph(),
    'dept': '研发',
    'terminal': 'T1',
    'terminalGroup': '终端分组1',
    'user': 'U1',
    'userGroup': '操作员分组1',
    'num': i + 1
  }
  terminalList4.push(template)
}
// 敏感内容征兆报表，初始页面弹框列表
const sensitiveContentList = [
  {
    id: 1,
    terminalListMessage: terminalList30
  },
  {
    id: 2,
    terminalListMessage: terminalList10
  },
  {
    id: 3,
    terminalListMessage: terminalList4
  }
]
// 敏感内容征兆报表，初始页面
const sensitiveContentInception = [
  {
    itemValue: {
      all: '30',
      dept: '10',
      user: '5',
      terminal: '4'
    },
    // 风险行为统计 横向柱状统计图
    barChartDivulgeData: [120, 200, 150, 80],
    barChartDivulgeOption: {
      title: {
        'text': '风险行为统计图',
        'subtext': ''
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      yAxis: {
        type: 'category',
        data: ['全盘扫描敏感文件数', '文件外发审批数', '零星检测-数据泄露违规数', '常规检测-数据泄露违规数']
      },
      series: [
        {
          data: [120, 200, 150, 80],
          type: 'bar'
        }
      ]
    },
    // 雷达图 风险行为分析
    chartIndicator: [
      { name: '全盘扫描敏感文件数', max: 6500 },
      { name: '文件外发审批数', max: 16000 },
      { name: '零星检测-数据泄露违规数', max: 30000 },
      { name: '常规检测-数据泄露违规数', max: 38000 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [4200, 3000, 20000, 35000],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    },
    // 部门风险行为数量柱状图数据
    barChartData: [120, 200, 150, 80, 100],
    barChartOption: {
      title: {
        'text': '部门风险行为',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['研发一部', '研发二部', '研发三部', '研发四部', '测试部']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 100],
          type: 'bar'
        }
      ]
    },
    // 饼图 终端风险行为数量图
    chartsTerminalDatas: [
      { value: 100, name: 'T1' },
      { value: 85, name: 'T2' },
      { value: 15, name: 'T3' },
      { value: 25, name: 'T4' },
      { value: 35, name: 'T5' }
    ],
    chartTerminalOption: {
      title: {
        text: '终端风险行为数量图',
        left: 'center',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    },
    // 饼图 操作员风险行为数量图
    chartsUserDatas: [
      { value: 100, name: '小新' },
      { value: 85, name: '小红' },
      { value: 15, name: '小蓝' },
      { value: 25, name: '小绿' },
      { value: 35, name: '小子' }
    ],
    chartUserOption: {
      title: {
        text: '操作员风险行为数量图',
        left: 'center',
        'subtext': '前5名'
      },
      toolbox: {
        show: false
      },
      legend: {
        left: 'center'
      },
      series: [
        {
          radius: '30%'
        }
      ]
    },
    // 折线图数据 违规数量趋势分析
    lineChartData: [150, 230, 224, 218, 135, 147, 260],
    lineOption: {
      title: {
        'text': '风险行为趋势分析',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    }
  }
]

const onOffTrend = [
  {
    id: 1,
    lineChartData: [150, 230, 224, 218, 135, 147, 260],
    lineOption: {
      title: {
        'text': '风险行为数量趋势图',
        'subtext': '最近7天'
      },
      toolbox: {
        show: false
      },
      xAxis: {
        type: 'category',
        data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
      },
      yAxis: {
        type: 'value',
        // 控制分隔线
        splitLine: {
          show: true,
          lineStyle: {
            width: 0.5,
            color: '#bce5ff'
          }
        },
        // 控制隔区域
        splitArea: {
          show: false
        }
      },
      series: [
        {
          data: [150, 230, 224, 218, 135, 147, 260],
          type: 'line'
        }
      ]
    },
    // 饼图 风险行为数量统计
    chartsWorkDatas: [
      { value: 30, name: '全盘扫描敏感文件数' },
      { value: 40, name: '文件外发审批数' },
      { value: 15, name: '零星检测-数据泄露违规数' },
      { value: 25, name: '常规检测-数据泄露违规数' }
    ],
    chartWorkOption: {
      title: {
        text: '风险行为数量统计',
        subtext: '最近7天',
        left: 'center'
      },
      toolbox: {
        show: false
      },
      series: [
        {
          radius: [20, '30%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          }
        }
      ]
    },
    // 雷达图 风险行为分析
    chartIndicator: [
      { name: '全盘扫描敏感文件数', max: 6500 },
      { name: '文件外发审批数', max: 16000 },
      { name: '零星检测-数据泄露违规数', max: 30000 },
      { name: '常规检测-数据泄露违规数', max: 38000 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [4200, 3000, 20000, 35000],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        subtext: '最近7天',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    }
  }
]
// 敏感内容征兆报表，部门分析（操作员）
const deptAnalyzeUser = [
  {
    id: 1,
    name: '项目部(操作员)',
    user: '小新',
    total: 20,
    leakageQuantityRoutine: 5,
    leakageQuantityAFew: 6,
    leakageQuantityOutsource: 4,
    leakageQuantityComprehensive: 5
  },
  {
    id: 2,
    name: '测试部(操作员)',
    user: '小们',
    total: 15,
    leakageQuantityRoutine: 2,
    leakageQuantityAFew: 4,
    leakageQuantityOutsource: 6,
    leakageQuantityComprehensive: 3
  }
]

// 敏感内容征兆报表，部门分析（操作员/左边列表点击详情）
const deptAnalyzeUserInfo = [
  {
    id: 1,
    deptName: '项目部(操作员)',
    total: 20,
    deptItemOption: [
      { label: '常规检测-数据泄露违规数', value: 5 },
      { label: '零星检测-数据泄露违规数', value: 6 },
      { label: '文件外发审批数', value: 4 },
      { label: '全盘扫描敏感文件数', value: 5 }
    ],
    terminalListMessage: [
      {
        userName: '小赵',
        userGroup: '操作员分组2',
        leakageQuantityRoutine: 2,
        leakageQuantityAFew: 4,
        leakageQuantityOutsource: 1,
        leakageQuantityComprehensive: 3
      },
      {
        userName: '小新',
        userGroup: '操作员分组2',
        leakageQuantityRoutine: 3,
        leakageQuantityAFew: 2,
        leakageQuantityOutsource: 3,
        leakageQuantityComprehensive: 2
      }
    ],
    chartIndicator: [
      { name: '常规检测-数据泄露违规数', max: 20 },
      { name: '零星检测-数据泄露违规数', max: 20 },
      { name: '文件外发审批数', max: 20 },
      { name: '全盘扫描敏感文件数', max: 20 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [5, 6, 4, 5],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    }
  },
  {
    id: 2,
    deptName: '测试部(操作员)',
    total: 15,
    deptItemOption: [
      { label: '常规检测-数据泄露违规数', value: 2 },
      { label: '零星检测-数据泄露违规数', value: 4 },
      { label: '文件外发审批数', value: 6 },
      { label: '全盘扫描敏感文件数', value: 3 }
    ],
    terminalListMessage: [
      {
        userName: 'U20021',
        userGroup: '操作员分组1',
        leakageQuantityRoutine: 1,
        leakageQuantityAFew: 2,
        leakageQuantityOutsource: 1,
        leakageQuantityComprehensive: 1
      },
      {
        userName: 'U2005',
        userGroup: '操作员分组2',
        leakageQuantityRoutine: 1,
        leakageQuantityAFew: 2,
        leakageQuantityOutsource: 5,
        leakageQuantityComprehensive: 2
      }
    ],
    chartIndicator: [
      { name: '常规检测-数据泄露违规数', max: 20 },
      { name: '零星检测-数据泄露违规数', max: 20 },
      { name: '文件外发审批数', max: 20 },
      { name: '全盘扫描敏感文件数', max: 20 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [2, 4, 6, 3],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    }
  }
]

// 敏感内容征兆报表，部门分析（终端/左边列表）
const deptAnalyzeTerminal = [
  {
    id: 1,
    name: '研发三部(终端)',
    total: 20,
    leakageQuantityRoutine: 5,
    leakageQuantityAFew: 6,
    leakageQuantityOutsource: 4,
    leakageQuantityComprehensive: 5
  },
  {
    id: 2,
    name: '研发四部(终端)',
    total: 15,
    leakageQuantityRoutine: 2,
    leakageQuantityAFew: 4,
    leakageQuantityOutsource: 6,
    leakageQuantityComprehensive: 3
  }
]

// 敏感内容征兆报表，部门分析（终端/左边列表点击详情）
const deptAnalyzeTerminalInfo = [
  {
    id: 1,
    deptName: '研发三部(终端)',
    total: 20,
    deptItemOption: [
      { label: '常规检测-数据泄露违规数', value: 5 },
      { label: '零星检测-数据泄露违规数', value: 6 },
      { label: '文件外发审批数', value: 4 },
      { label: '全盘扫描敏感文件数', value: 5 }
    ],
    terminalListMessage: [
      {
        terminalName: 'T1',
        terminalGroup: '终端分组2',
        leakageQuantityRoutine: 2,
        leakageQuantityAFew: 4,
        leakageQuantityOutsource: 1,
        leakageQuantityComprehensive: 3
      },
      {
        terminalName: 'T2',
        terminalGroup: '终端分组2',
        leakageQuantityRoutine: 3,
        leakageQuantityAFew: 2,
        leakageQuantityOutsource: 3,
        leakageQuantityComprehensive: 2
      }
    ],
    chartIndicator: [
      { name: '常规检测-数据泄露违规数', max: 20 },
      { name: '零星检测-数据泄露违规数', max: 20 },
      { name: '文件外发审批数', max: 20 },
      { name: '全盘扫描敏感文件数', max: 20 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [5, 6, 4, 5],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    }
  },
  {
    id: 2,
    deptName: '研发四部(终端)',
    total: 15,
    deptItemOption: [
      { label: '常规检测-数据泄露违规数', value: 2 },
      { label: '零星检测-数据泄露违规数', value: 4 },
      { label: '文件外发审批数', value: 6 },
      { label: '全盘扫描敏感文件数', value: 3 }
    ],
    terminalListMessage: [
      {
        terminalName: 'T1006',
        terminalGroup: '终端分组1',
        leakageQuantityRoutine: 1,
        leakageQuantityAFew: 2,
        leakageQuantityOutsource: 1,
        leakageQuantityComprehensive: 1
      },
      {
        terminalName: 'T2005',
        terminalGroup: '终端分组2',
        leakageQuantityRoutine: 1,
        leakageQuantityAFew: 2,
        leakageQuantityOutsource: 5,
        leakageQuantityComprehensive: 2
      }
    ],
    chartIndicator: [
      { name: '常规检测-数据泄露违规数', max: 20 },
      { name: '零星检测-数据泄露违规数', max: 20 },
      { name: '文件外发审批数', max: 20 },
      { name: '全盘扫描敏感文件数', max: 20 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [2, 4, 6, 3],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    }
  }
]

// 敏感内容征兆报表，部门分析（下钻列表信息）
const deptMsgInfo = [
  {
    id: 1,
    time: '2023-7-6 16:15',
    terminalName: 'T1009',
    terminalGroup: '终端分组1',
    userName: 'U1009',
    userGroup: '操作员分组1',
    type: 1,
    severity: '非常严重',
    content: '飒飒撒',
    dispatcher: 'xiaox'
  },
  {
    id: 2,
    time: '2023-7-6 16:18',
    terminalName: 'T10012',
    terminalGroup: '终端分组2',
    userName: 'U10032',
    userGroup: '操作员分组12',
    type: 2,
    severity: '严重',
    content: '无法为',
    dispatcher: 'xiaod'
  }
]

// 敏感内容征兆报表，操作员分析（操作员/左侧列表）
const analysisUser = [
  {
    id: 1,
    name: '项目部(操作员)',
    user: '小新',
    total: 20,
    leakageQuantityRoutine: 5,
    leakageQuantityAFew: 6,
    leakageQuantityOutsource: 4,
    leakageQuantityComprehensive: 5
  },
  {
    id: 2,
    name: '测试部(操作员)',
    user: '小们',
    total: 15,
    leakageQuantityRoutine: 2,
    leakageQuantityAFew: 4,
    leakageQuantityOutsource: 6,
    leakageQuantityComprehensive: 3
  }
]
const userList20 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 20; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'userName': '小新', // 名字为随机中文名字
    'userGroup': '操作员分组1', // 名字为随机中文名字
    'type': 1,
    'severity': '非常严重',
    'content': Random.cparagraph(), // 随机生成中文
    'dispatcher': 'xiaox'
  }
  userList20.push(template)
}

const userList15 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 15; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'userName': '小们', // 名字为随机中文名字
    'userGroup': '操作员分组2', // 名字为随机中文名字
    'type': 2,
    'severity': '严重',
    'content': Random.cparagraph(), // 随机生成中文
    'dispatcher': 'xiaoe'
  }
  userList15.push(template)
}
// 敏感内容征兆报表，操作员分析（左边列表点击详情）
const analysisUserInfo = [
  {
    id: 1,
    deptName: '项目部（操作员）',
    miniTitle: '小新',
    total: 20,
    itemOption: [
      { label: '常规检测-数据泄露违规数', value: 5 },
      { label: '零星检测-数据泄露违规数', value: 6 },
      { label: '文件外发审批数', value: 4 },
      { label: '全盘扫描敏感文件数', value: 5 }
    ],
    terminalListMessage: userList20,
    chartIndicator: [
      { name: '常规检测-数据泄露违规数', max: 20 },
      { name: '零星检测-数据泄露违规数', max: 20 },
      { name: '文件外发审批数', max: 20 },
      { name: '全盘扫描敏感文件数', max: 20 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [5, 6, 4, 5],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    }
  },
  {
    id: 2,
    deptName: '测试部(操作员)',
    miniTitle: '小们',
    total: 15,
    itemOption: [
      { label: '常规检测-数据泄露违规数', value: 2 },
      { label: '零星检测-数据泄露违规数', value: 4 },
      { label: '文件外发审批数', value: 6 },
      { label: '全盘扫描敏感文件数', value: 3 }
    ],
    terminalListMessage: userList15,
    chartIndicator: [
      { name: '常规检测-数据泄露违规数', max: 20 },
      { name: '零星检测-数据泄露违规数', max: 20 },
      { name: '文件外发审批数', max: 20 },
      { name: '全盘扫描敏感文件数', max: 20 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [2, 4, 6, 3],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    }
  }
]

// 敏感内容征兆报表，终端分析（终端/左边列表）
const analysisTerminal = [
  {
    id: 1,
    name: '研发三部(终端)',
    terminal: 'T1',
    total: 20,
    leakageQuantityRoutine: 5,
    leakageQuantityAFew: 6,
    leakageQuantityOutsource: 4,
    leakageQuantityComprehensive: 5
  },
  {
    id: 2,
    name: '研发四部(终端)',
    terminal: 'T2',
    total: 15,
    leakageQuantityRoutine: 2,
    leakageQuantityAFew: 4,
    leakageQuantityOutsource: 6,
    leakageQuantityComprehensive: 3
  }
]
const terminalList20 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 20; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'terminal': 'T1', // 名字为随机中文名字
    'terminalGroup': '终端分组1', // 名字为随机中文名字
    'type': 1,
    'severity': '非常严重',
    'content': Random.cparagraph(), // 随机生成中文
    'dispatcher': 'xiaox'
  }
  terminalList20.push(template)
}

const terminalList15 = [] // 用于接受生成数据的数组(部门数据)
for (let i = 0; i < 15; i++) { // 可自定义生成的个数
  const template = {
    'id|+1': i, // 数字从当前开始，后续+1
    'time': Random.date(), // 生成一个随机日期,可加参数定义日期格式
    'terminal': 'T2', // 名字为随机中文名字
    'terminalGroup': '终端分组2', // 名字为随机中文名字
    'type': 2,
    'severity': '严重',
    'content': Random.cparagraph(), // 随机生成中文
    'dispatcher': 'xiaoe'
  }
  terminalList15.push(template)
}
// 敏感内容征兆报表，终端分析（终端/左边列表点击详情）
const analysisTerminalInfo = [
  {
    id: 1,
    deptName: '研发三部(终端)',
    miniTitle: 'T1',
    total: 20,
    itemOption: [
      { label: '常规检测-数据泄露违规数', value: 5 },
      { label: '零星检测-数据泄露违规数', value: 6 },
      { label: '文件外发审批数', value: 4 },
      { label: '全盘扫描敏感文件数', value: 5 }
    ],
    terminalListMessage: terminalList20,
    chartIndicator: [
      { name: '常规检测-数据泄露违规数', max: 20 },
      { name: '零星检测-数据泄露违规数', max: 20 },
      { name: '文件外发审批数', max: 20 },
      { name: '全盘扫描敏感文件数', max: 20 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [5, 6, 4, 5],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    }
  },
  {
    id: 2,
    deptName: '研发四部(终端)',
    miniTitle: 'T2',
    total: 15,
    itemOption: [
      { label: '常规检测-数据泄露违规数', value: 2 },
      { label: '零星检测-数据泄露违规数', value: 4 },
      { label: '文件外发审批数', value: 6 },
      { label: '全盘扫描敏感文件数', value: 3 }
    ],
    terminalListMessage: terminalList15,
    chartIndicator: [
      { name: '常规检测-数据泄露违规数', max: 20 },
      { name: '零星检测-数据泄露违规数', max: 20 },
      { name: '文件外发审批数', max: 20 },
      { name: '全盘扫描敏感文件数', max: 20 }
    ],
    chartsDatasRadar: [
      {
        name: '风险行为分析',
        value: [2, 4, 6, 3],
        areaStyle: {
          color: '#32dadd'
        }
      }
    ],
    chartOptionRadar: {
      title: {
        text: '风险行为分析',
        left: 'center'
      },
      radar: {
        radius: '40%'
      },
      toolbox: {
        show: false
      }
    }
  }
]
/**
 * 获取敏感内容征兆报表（初始页面弹框列表）
 */
Mock.mock('/sensitiveContentList/list', 'get', sensitiveContentList)

/**
 * 获取敏感内容征兆报表（初始页面）
 */
Mock.mock('/sensitiveContentInception/list', 'get', sensitiveContentInception)

/**
 * 获取左边部门（操作员）信息
 */
Mock.mock('/deptAnalyzeUser/list', 'get', deptAnalyzeUser)

/**
 * 获取左边部门（操作员）点击的详细信息
 */
Mock.mock('/deptAnalyzeUserInfo/info', 'get', deptAnalyzeUserInfo)
/**
 * 获取左边部门（终端）信息
 */
Mock.mock('/deptAnalyzeTerminal/list', 'get', deptAnalyzeTerminal)
/**
 * 获取左边部门（终端）点击的详细信息
 */
Mock.mock('/deptAnalyzeTerminal/info', 'get', deptAnalyzeTerminalInfo)

/**
 * 右侧下钻列表
 */
Mock.mock('/deptMsgInfo/list', 'get', deptMsgInfo)

/**
 * 获取左边（操作员）信息
 */
Mock.mock('/analysisUser/list', 'get', analysisUser)

/**
 * 获取左边（操作员）点击的详细信息
 */
Mock.mock('/analysisUserInfo/info', 'get', analysisUserInfo)
/**
 * 获取左边（终端）信息
 */
Mock.mock('/analysisTerminal/list', 'get', analysisTerminal)
/**
 * 获取左边（终端）点击的详细信息
 */
Mock.mock('/analysisTerminalInfo/info', 'get', analysisTerminalInfo)

/**
 * 获取敏感内容征兆报表（趋势分析页面内容）
 */
Mock.mock('/onOffTrend/list', 'get', onOffTrend)
export default [

]
