// import Mock from 'mockjs'

// const data = Mock.mock({
//   'filterText': '',
//   'items': [{
//     id: '@id',
//     label: '天锐',
//     'children|3': [{
//       id: '@id',
//       label: '部门',
//       'children|3': [{
//         id: '@id',
//         label: '子部门'
//       }]
//     }]
//   }]
// })

const deptData = [
  {
    id: 1,
    label: '系统管理',
    children: [
      {
        id: 2,
        label: '组织管理',
        children: [
          { id: 3, label: '系统用户' },
          { id: 4, label: '系统角色' },
          { id: 5, label: '系统角色权限' }
        ]
      },
      {
        id: 6,
        label: '终端管理',
        children: [
          { id: 7, label: '部门管理' },
          { id: 8, label: '终端用户' },
          { id: 9, label: '终端信息' }
        ]
      }
    ]
  }
]

export default [
  {
    url: '/permission/listTreeNode',
    type: 'get',
    response: config => {
      const items = deptData
      return {
        code: 20000,
        data: items
      }
    }
  }
]
