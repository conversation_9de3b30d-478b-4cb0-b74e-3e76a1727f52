import Mock from 'mockjs'

const List = []
const count = 5

const baseContent = '我是测试数据'

for (let i = 0; i < count; i++) {
  List.push(Mock.mock({
    id: i + 1,
    name: '角色' + (i + 1),
    active: 1,
    remark: baseContent
  }))
}

export default [
  {
    url: '/role/getPage',
    type: 'post',
    response: config => {
      const { page = 1, limit = 20, sort } = config.body

      let mockList = List.filter(item => {
        // if (importance && item.importance !== +importance) return false
        // if (type && item.type !== type) return false
        // if (title && item.title.indexOf(title) < 0) return false
        return true
      })

      if (sort === '-id') {
        mockList = mockList.reverse()
      }

      const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

      return {
        code: 20000,
        data: {
          total: mockList.length,
          items: pageList
        }
      }
    }
  },

  {
    url: '/role/listTree',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: [
          { id: 1, label: '系统管理员' },
          { id: 2, label: '安全管理员' }
        ]
      }
    }
  },

  {
    url: '/role/listTreeNode',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: [
          { id: 1, label: '系统管理员' },
          { id: 2, label: '安全管理员' }
        ]
      }
    }
  },

  {
    url: '/role/listSysRole',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: [
          { id: 1, name: '系统管理员' },
          { id: 2, name: '安全管理员' }
        ]
      }
    }
  },

  {
    url: '/role/detail',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: []
      }
    }
  },

  {
    url: '/role/add',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/role/update',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/role/delete',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: 'success'
      }
    }
  },

  {
    url: '/role/getByName',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: null
      }
    }
  }

]

