import Mock from 'mockjs'

const List = []
let threeList = []
const count = 5

for (let i = 0; i < count; i++) {
  List.push(Mock.mock({
    id: i + 1,
    account: 'account' + (i + 1),
    name: '管理员' + (i + 1),
    roleId: 1,
    email: '<EMAIL>',
    active: 1
  }))
}

threeList = [
  { 'id': 1, 'name': '超级管理员', 'account': 'admin', 'email': '<EMAIL>', 'remark': '我是测试数据', 'active': 1 },
  { 'id': 2, 'name': '系统管理员', 'account': 'sysAdmin', 'email': '<EMAIL>', 'remark': '我是测试数据', 'active': 1 },
  { 'id': 3, 'name': '安全管理员', 'account': 'secAdmin', 'email': '<EMAIL>', 'remark': '我是测试数据', 'active': 1 },
  { 'id': 4, 'name': '审计管理员', 'account': 'audAdmin', 'email': '<EMAIL>', 'remark': '我是测试数据', 'active': 1 }
]

export default [
  {
    url: '/sysUser/getPage',
    type: 'post',
    response: config => {
      const { page = 1, limit = 20, sort } = config.body

      let mockList = List.filter(item => {
        // if (importance && item.importance !== +importance) return false
        // if (type && item.type !== type) return false
        // if (title && item.title.indexOf(title) < 0) return false
        return true
      })

      if (sort === '-id') {
        mockList = mockList.reverse()
      }

      const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

      return {
        code: 20000,
        data: {
          total: mockList.length,
          items: pageList
        }
      }
    }
  },

  {
    url: '/threeUser/getPage',
    type: 'post',
    response: config => {
      const { page = 1, limit = 20, sort } = config.body

      let mockList = threeList.filter(item => {
        return true
      })

      if (sort === '-id') {
        mockList = mockList.reverse()
      }

      const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

      return {
        code: 20000,
        data: {
          total: mockList.length,
          items: pageList
        }
      }
    }
  },

  {
    url: '/sysUser/detail',
    type: 'get',
    response: config => {
      // const { id } = config.query
      return {
        code: 20000,
        data: 'success'
      }
    }
  },

  {
    url: '/sysUser/add',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/sysUser/update',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/sysUser/delete',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: 'success'
      }
    }
  },

  {
    url: '/threeUser/isSuperMode',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: 1
      }
    }
  },

  {
    url: '/sysUser/isSamePassword',
    type: 'post',
    response: config => {
      const { password } = config.body
      const data = password === 'admin'
      return {
        code: 20000,
        data: data
      }
    }
  }
]

