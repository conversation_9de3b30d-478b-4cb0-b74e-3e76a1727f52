import Mock from 'mockjs'

const List = []
const count = 50

const baseContent = '我是测试数据'

for (let i = 0; i < count; i++) {
  List.push(Mock.mock({
    id: i + 2,
    name: '部门 ' + i,
    parentPath: '天锐',
    parentId: 1, // 上级部门id
    remark: baseContent
  }))
}

const deptData = [
  {
    id: 1,
    label: '天锐',
    children: [
      {
        id: 2,
        label: '部门',
        children: [
          { id: 3, label: '子部门' },
          { id: 4, label: '子部门' },
          { id: 5, label: '子部门' }
        ]
      },
      {
        id: 6,
        label: '部门',
        children: [
          { id: 7, label: '子部门' },
          { id: 8, label: '子部门' },
          { id: 9, label: '子部门' }
        ]
      },
      {
        id: 10,
        label: '部门',
        children: [
          { id: 11, label: '子部门' },
          { id: 12, label: '子部门' },
          { id: 13, label: '子部门' }
        ]
      }
    ]
  }
]

export default [
  {
    url: '/department/getPage',
    type: 'post',
    response: config => {
      const { page = 1, limit = 20, sort, name } = config.body

      let mockList = List.filter(item => {
        // if (importance && item.importance !== +importance) return false
        // if (type && item.type !== type) return false
        if (name && item.name.indexOf(name) < 0) return false
        return true
      })

      if (sort === '-id') {
        mockList = mockList.reverse()
      }

      const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

      return {
        code: 20000,
        data: {
          total: mockList.length,
          items: pageList
        }
      }
    }
  },

  {
    url: '/department/detail',
    type: 'get',
    response: config => {
      const { id } = config.query
      for (const article of List) {
        if (article.id === +id) {
          return {
            code: 20000,
            data: article
          }
        }
      }
    }
  },

  {
    url: '/department/add',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/department/update',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/department/getByName',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: {
          id: 2,
          name: 'test',
          parentId: 1
        }
      }
    }
  },

  {
    url: '/department/getByName',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: {
          id: 2,
          name: 'test',
          parentId: 1
        }
      }
    }
  },

  {
    url: '/department/listTreeNode4LoginUser',
    type: 'get',
    response: config => {
      const items = deptData
      return {
        code: 20000,
        data: items
      }
    }
  },

  {
    url: '/department/listTreeNode',
    type: 'get',
    response: config => {
      const items = deptData
      return {
        code: 20000,
        data: items
      }
    }
  }
]

