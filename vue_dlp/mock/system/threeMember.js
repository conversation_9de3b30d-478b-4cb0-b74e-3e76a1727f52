// import Mock from 'mockjs'

const List = [
  { 'id': 1, 'role': '超级管理员', 'account': 'admin', 'permission': '权限组1，权限组2，权限组3', 'status': 'on' },
  { 'id': 2, 'role': '系统管理员', 'account': 'sysAdmin', 'permission': '权限组1', 'status': 'on' },
  { 'id': 3, 'role': '安全管理员', 'account': 'secAdmin', 'permission': '权限组2', 'status': 'on' },
  { 'id': 4, 'role': '审计管理员', 'account': 'audAdmin', 'permission': '权限组3', 'status': 'on' }
]
// const count = 4

// for (let i = 0; i < count; i++) {
//   List.push(Mock.mock({
//     id: '@increment',
//     role: 'account',
//     account: '管理员 @increment',
//     permission: 'system',
//     status: 'on'
//   }))
// }

export default [
  {
    url: '/threeMember/list',
    type: 'post',
    response: config => {
      const { page = 1, limit = 20, sort } = config.body

      let mockList = List.filter(item => {
        // if (importance && item.importance !== +importance) return false
        // if (type && item.type !== type) return false
        // if (title && item.title.indexOf(title) < 0) return false
        return true
      })

      if (sort === '-id') {
        mockList = mockList.reverse()
      }

      const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

      return {
        code: 20000,
        data: {
          total: mockList.length,
          items: pageList
        }
      }
    }
  }
]

