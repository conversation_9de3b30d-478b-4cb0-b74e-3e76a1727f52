import Mock from 'mockjs'

const List = []
const count = 5

for (let i = 0; i < count; i++) {
  List.push(Mock.mock({
    id: '@increment',
    account: 'account',
    name: '管理员 @increment',
    role: 'system',
    source: '系统内置',
    permission: 'default',
    status: 'on'
  }))
}

export default [
  {
    url: '/administrator/list',
    type: 'post',
    response: config => {
      const { page = 1, limit = 20, sort } = config.body

      let mockList = List.filter(item => {
        // if (importance && item.importance !== +importance) return false
        // if (type && item.type !== type) return false
        // if (title && item.title.indexOf(title) < 0) return false
        return true
      })

      if (sort === '-id') {
        mockList = mockList.reverse()
      }

      const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

      return {
        code: 20000,
        data: {
          total: mockList.length,
          items: pageList
        }
      }
    }
  },

  {
    url: '/administrator/detail',
    type: 'get',
    response: config => {
      const { id } = config.query
      for (const article of List) {
        if (article.id === +id) {
          return {
            code: 20000,
            data: article
          }
        }
      }
    }
  },

  {
    url: '/administrator/add',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/administrator/update',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  }
]

