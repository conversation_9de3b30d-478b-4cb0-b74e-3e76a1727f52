import Mock from 'mockjs'

const data1 = Mock.mock({
  'filterText': '',
  'items': [{
    id: '@id',
    label: '天锐',
    disabled: true,
    'children|3': [{
      id: '@id',
      label: '部门',
      disabled: true,
      'children|3': [{
        id: '@id',
        label: '子部门',
        disabled: true,
        'children|3': [{
          id: '@id',
          label: '用户'
        }]
      }]
    }]
  }]
})

export default [
  {
    url: '/user/listTreeNode4LoginUser',
    type: 'get',
    response: config => {
      const items = data1.items
      return {
        code: 20000,
        data: items
      }
    }
  }
]
