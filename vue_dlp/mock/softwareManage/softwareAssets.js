import Mock from 'mockjs'

const List = []
const count = 5

const baseContent = '我是测试数据'

for (let i = 0; i < count; i++) {
  List.push(Mock.mock({
    id: i + 1,
    name: '软件' + (i + 1),
    remark: baseContent
  }))
}

export default [
  {
    url: '/softwareInfo/getSoftwareAssetsPage',
    type: 'post',
    response: config => {
      const { page = 1, limit = 20, sort } = config.body

      let mockList = List.filter(item => {
        // if (importance && item.importance !== +importance) return false
        // if (type && item.type !== type) return false
        // if (title && item.title.indexOf(title) < 0) return false
        return true
      })

      if (sort === '-id') {
        mockList = mockList.reverse()
      }

      const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

      return {
        code: 20000,
        data: {
          total: mockList.length,
          items: pageList
        }
      }
    }
  },

  {
    url: '/softwareInfo/listTree',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: [
          { id: 1, label: '系统管理员' },
          { id: 2, label: '安全管理员' }
        ]
      }
    }
  },

  {
    url: '/softwareInfo/listTreeNode',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: [
          { id: 1, label: '系统管理员' },
          { id: 2, label: '安全管理员' }
        ]
      }
    }
  },

  {
    url: '/softwareInfo/listSysRole',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: [
          { id: 1, name: '系统管理员' },
          { id: 2, name: '安全管理员' }
        ]
      }
    }
  },

  {
    url: '/softwareInfo/detail',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: []
      }
    }
  },

  {
    url: '/softwareInfo/add',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/softwareInfo/update',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/softwareInfo/delete',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: 'success'
      }
    }
  },

  {
    url: '/softwareInfo/getByName',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: null
      }
    }
  }

]

