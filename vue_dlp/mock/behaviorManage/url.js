import Mock from 'mockjs'

const List = []
const count = 5

const baseContent = '我是测试数据'

for (let i = 0; i < count; i++) {
  List.push(Mock.mock({
    id: i + 1,
    name: '策略 ' + (i + 1),
    mode: '1',
    timeId: '1',
    remark: baseContent
  }))
}

const urlData = [
  {
    id: 1,
    label: '网址库',
    children: [
      {
        id: 2,
        label: '财经网',
        children: [
          { id: 3, label: '网址1（www.xxx.com）' },
          { id: 4, label: '网址2（www.xxx.com）' },
          { id: 5, label: '网址3（www.xxx.com）' }
        ]
      },
      {
        id: 6,
        label: '购物网',
        children: [
          { id: 7, label: '网址1（www.xxx.com）' },
          { id: 8, label: '网址2（www.xxx.com）' },
          { id: 9, label: '网址3（www.xxx.com）' }
        ]
      },
      {
        id: 10,
        label: '视频网',
        children: [
          { id: 11, label: '网址1（www.xxx.com）' },
          { id: 12, label: '网址2（www.xxx.com）' },
          { id: 13, label: '网址3（www.xxx.com）' }
        ]
      }
    ]
  }
]

export default [
  {
    url: '/urlStrategy/getPage',
    type: 'post',
    response: config => {
      const { page = 1, limit = 20, sort, name } = config.body

      let mockList = List.filter(item => {
        // if (importance && item.importance !== +importance) return false
        // if (type && item.type !== type) return false
        if (name && item.name.indexOf(name) < 0) return false
        return true
      })

      if (sort === '-id') {
        mockList = mockList.reverse()
      }

      const pageList = mockList.filter((item, index) => index < limit * page && index >= limit * (page - 1))

      return {
        code: 20000,
        data: {
          total: mockList.length,
          items: pageList
        }
      }
    }
  },

  {
    url: '/urlStrategy/detail',
    type: 'get',
    response: config => {
      const { id } = config.query
      for (const article of List) {
        if (article.id === +id) {
          return {
            code: 20000,
            data: article
          }
        }
      }
    }
  },

  {
    url: '/urlStrategy/add',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/urlStrategy/update',
    type: 'post',
    response: config => {
      const data = config.body
      return {
        code: 20000,
        data: data
      }
    }
  },

  {
    url: '/urlStrategy/delete',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: 'success'
      }
    }
  },

  {
    url: '/urlStrategy/getByName',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: {
          id: 2,
          name: 'test',
          parentId: 1
        }
      }
    }
  },

  {
    url: '/url/listTreeNode',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: urlData
      }
    }
  }
]

