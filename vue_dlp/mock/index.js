import Mock from 'mockjs'
import { param2Obj } from '../src/utils'

import user from './user'
import tree from './tree'
import sysUser from './system/sysUser'
import role from './system/role'
import permissions from './system/permissions'
import threeMember from './system/threeMember'
import department from './system/department'
import url from './behaviorManage/url'
import sensitiveContent from './report/sensitiveControl/sensitiveContent'
import fullScan from './report/sensitiveControl/fullScan';
import ruleType from './report/sensitiveControl/ruleType';
import severity from './report/sensitiveControl/severity'
import divulgeMode from './report/sensitiveControl/divulgeMode';
const mocks = [
  ...user,
  ...tree,
  ...sysUser,
  ...role,
  ...permissions,
  ...threeMember,
  ...department,
  ...url,
  ...sensitiveContent,
  ...fullScan,
  ...ruleType,
  ...severity,
  ...divulgeMode
]

// for front mock
// 请谨慎使用它，它将重新定义XMLHttpRequest，
// 这将导致您的许多第三方库失效(如进度事件)。
export function mockXHR() {
  // mock patch
  // https://github.com/nuysoft/Mock/issues/300
  Mock.XHR.prototype.proxy_send = Mock.XHR.prototype.send
  Mock.XHR.prototype.send = function() {
    if (this.custom.xhr) {
      this.custom.xhr.withCredentials = this.withCredentials || false

      if (this.responseType) {
        this.custom.xhr.responseType = this.responseType
      }
    }
    this.proxy_send(...arguments)
  }

  function XHR2ExpressReqWrap(respond) {
    return function(options) {
      let result = null
      if (respond instanceof Function) {
        const { body, type, url } = options
        // https://expressjs.com/en/4x/api.html#req
        result = respond({
          method: type,
          body: JSON.parse(body),
          query: param2Obj(url)
        })
      } else {
        result = respond
      }
      return Mock.mock(result)
    }
  }

  for (const i of mocks) {
    Mock.mock(new RegExp(i.url), i.type || 'get', XHR2ExpressReqWrap(i.response))
  }
}

// for mock server
const responseFake = (url, type, respond) => {
  return {
    url: new RegExp(`/mock${url}`),
    type: type || 'get',
    response(req, res) {
      res.json(Mock.mock(respond instanceof Function ? respond(req, res) : respond))
    }
  }
}

export default mocks.map(route => {
  return responseFake(route.url, route.type, route.response)
})
