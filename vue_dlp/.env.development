# 自定义变量名： 必须以VUE_APP_开头，否则无法识别，并且值是一个string类型，哪怕设置的值为数字，布尔值等格式，最后获取获取到的值也只是一个string

# just a flag
ENV = 'development'

# base api
VUE_APP_BASE_API = '/dev-api'

# 前端请求的代理路径
# target: 'http://localhost:28080/dlp'
VUE_APP_PROXY_TARGET = 'http://localhost:28080/dlp'

# 前端 websocket 请求的代理路径
# target: 'ws://localhost:28080/dlp'
VUE_APP_PROXY_WEBSOCKET_TARGET = 'ws://localhost:28080/dlp'

# NAC服务器ip
# 考虑到自测场景：nac前后端分离（ip、端口不一致）
VUE_APP_NAC_SERVER_IP = '**************'
VUE_APP_NAC_SERVER_PORT = '7000'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true
