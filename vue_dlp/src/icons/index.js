import Vue from 'vue'
import SvgIcon from '@/components/SvgIcon'// svg component

// register globally
Vue.component('SvgIcon', SvgIcon)

const req = require.context('./svg', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys().map(requireContext)
requireAll(req)
Vue.prototype.svgIconMap = req.keys().map(name => name.replace('./', '').replace('.svg', ''))

// gif,png图标
// 使用方法：const src = this.imgIconMap[key] , key为图标的文件名
//          <img :src="src">
const fileIcon = require.context('./img', false, /(\.gif|\.png)$/)
const fileIconMap = {}
fileIcon.keys().forEach((item, key) => {
  const name = item.substr(2).split('.')[0]
  fileIconMap[name] = fileIcon(item, key, fileIcon.keys())
})
Vue.prototype.imgIconMap = fileIconMap
