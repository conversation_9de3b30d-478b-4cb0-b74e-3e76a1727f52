import i18n from '@/lang';
// require.context() 返回的是函数，通过keys()获取到的是文件名数组
const req = require.context('@/icons/ext', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys().map(requireContext)
requireAll(req)

/**
 * 文件后缀对应图标映射
 */
const fileIconMap = {}
req.keys().forEach(item => {
  const name = item.slice(2, item.indexOf('.', 2))
  fileIconMap[name] = [name]
})

fileIconMap.accdb = ['accdb', 'mdb']
fileIconMap.dll = ['bat', 'cmd', 'dll']
fileIconMap.css = ['css', 'scss', 'sass', 'less']
fileIconMap.html = ['html', 'htm', 'xhtml']
fileIconMap.docx = ['docx', 'rtf', 'wps']
fileIconMap.xls = ['xls', 'et']
fileIconMap.ppt = ['ppt', 'dps']
fileIconMap.postscript = ['ps', 'prn']
// 文本文件后缀
fileIconMap.text = ['txt', 'log', 'java', 'kt', 'h', 'c', 'hpp', 'cpp', 'cs', 'php', 'js', 'ts', 'py', 'go', 'json', 'yml', 'yaml', 'vue', 'ini', 'properties']
// 压缩文件后缀
fileIconMap.compress = ['7z', 'rar', 'zip', 'ace', 'ar', 'arc', 'arj', 'bz', 'bz2', 'cab', 'gz', 'gzip', 'jar', 'war', 'aar', 'lz4', 'lzh', 'lzm', 'lzma', 'lzo', 'lzx', 'tar', 'tbz', 'tbz2', 'tgz']
// 图片后缀
fileIconMap.image = ['bmp', 'gif', 'ico', 'jpg', 'jpeg', 'png', 'raw', 'eps', 'exif']
// 音频文件后缀
fileIconMap.audio = ['acm', 'aif', 'aifc', 'aiff', 'au', 'cda', 'wav', 'mp3', 'mid', 'wma', 'ra', 'vqf', 'ape', 'pcm', 'flac', 'alac', 'aac', 'ogg']
// 视频文件后缀
fileIconMap.video = ['flv', 'avi', 'mov', 'mp4', 'm4v', 'asf', 'wmv', 'mkv', 'rm', 'rmvb', 'ts', 'dat', '3gp', 'vob', 'mpeg', 'mpg', 'qt']

/**
 * 获取文件后缀对应图标
 * @param suffix 后缀名
 * @returns {string} 图标名称
 */
export function getFileIcon(suffix) {
  if (suffix) {
    for (const key in fileIconMap) {
      const suffixes = fileIconMap[key]
      if (suffixes.indexOf(suffix) >= 0) {
        return key
      }
    }
  }
  return 'ion'
}

/**
 * 获取文件名对应图标
 * @param filename 文件名
 * @returns {string} 图标名称
 */
export function getFileNameIcon(filename) {
  const i = filename.lastIndexOf('.')
  if (i < 0) {
    return 'ion'
  }
  const suffix = filename.slice(i + 1).toLowerCase()
  return getFileIcon(suffix)
}

/**
 * 格式化文件类型
 * @param filename 文件名
 * @returns {string} 文件类型描述
 */
export function formatFileType(filename) {
  const i = filename.lastIndexOf('.')
  if (i < 0) {
    return '.'
  }
  const suffix = filename.slice(i + 1).toLowerCase()
  return getFileTypeBySuffix(suffix)
}
export function getFileTypeBySuffix(suffix) {
  if (!suffix) {
    return '.'
  }
  if (suffix === 'bat') {
    // return 'Windows 批处理文件'
    return i18n.t('pages.windowsBatchFile')
  }
  if (suffix === 'cmd') {
    // return 'Windows 命令脚本'
    return i18n.t('pages.windowsCommandScript')
  }
  if (suffix === 'dll') {
    // return '应用程序扩展'
    return i18n.t('pages.applicationExtension')
  }
  if (suffix === 'exe') {
    // return '应用程序'
    return i18n.t('pages.applicationProgram')
  }
  if (suffix === 'ini') {
    // return '配置设置'
    return i18n.t('pages.configurationSetting')
  }
  if (suffix === 'lnk') {
    // return '快捷方式'
    return i18n.t('pages.shortcut')
  }
  if (suffix === 'iso') {
    // return '光盘映像文件'
    return i18n.t('pages.CDImage')
  }
  if (suffix === 'css') {
    // return '层叠样式表文档'
    return i18n.t('pages.cssDoc')
  }
  if (suffix === 'chm') {
    // return '编译的 HTML 帮助文件'
    return i18n.t('pages.compiledHTMLHelpFile')
  }
  if (suffix === 'ico') {
    // return '图标'
    return i18n.t('table.icon')
  }
  if (['bmp', 'gif', 'jpg', 'jpeg', 'png', 'tiff'].indexOf(suffix) > -1) {
    // return suffix.toUpperCase() + ' 图像'
    return suffix.toUpperCase() + ' ' + i18n.t('pages.image')
  }
  if (fileIconMap.compress.indexOf(suffix) > -1) {
    // return suffix.toUpperCase() + ' 压缩文件'
    return suffix.toUpperCase() + ' ' + i18n.t('pages.compressedFile')
  }
  if (fileIconMap.audio.indexOf(suffix) > -1) {
    // return suffix.toUpperCase() + ' 音频文件'
    return suffix.toUpperCase() + ' ' + i18n.t('pages.audioFile')
  }
  if (fileIconMap.video.indexOf(suffix) > -1) {
    // return suffix.toUpperCase() + ' 视频文件'
    return suffix.toUpperCase() + ' ' + i18n.t('pages.videoFile')
  }
  if (fileIconMap.html.indexOf(suffix) > -1) {
    // return 'HTML 文档'
    return i18n.t('pages.HTMLDoc')
  }
  if (['txt', 'log'].indexOf(suffix) > -1) {
    // return '文本文档'
    return i18n.t('pages.textDoc')
  }
  if (['doc', 'docx', 'pdf', 'wps'].indexOf(suffix) > -1) {
    // return suffix.toUpperCase() + ' 文档'
    return suffix.toUpperCase() + ' ' + i18n.t('pages.doc')
  }
  if (['xls', 'xlsx'].indexOf(suffix) > -1) {
    // return suffix.toUpperCase() + ' 工作表'
    return suffix.toUpperCase() + ' ' + i18n.t('pages.sheet')
  }
  if (suffix === 'et') {
    return 'WPS ' + i18n.t('pages.sheet')
  }
  if (['ppt', 'pptx'].indexOf(suffix) > -1) {
    // return suffix.toUpperCase() + ' 演示文稿'
    return suffix.toUpperCase() + ' ' + i18n.t('pages.presentation')
  }
  if (suffix === 'dps') {
    return 'WPS ' + i18n.t('pages.presentation')
  }
  // return suffix.toUpperCase() + ' 文件'
  return suffix.toUpperCase() + ' ' + i18n.t('table.file')
}
