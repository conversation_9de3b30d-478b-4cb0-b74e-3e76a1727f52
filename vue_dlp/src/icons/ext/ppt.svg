<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.2.3, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 200 200" style="enable-background:new 0 0 200 200;" xml:space="preserve">
<style type="text/css">
	.st0{clip-path:url(#SVGID_2_);}
	.st1{fill-rule:evenodd;clip-rule:evenodd;fill:#EC6814;}
	.st2{fill-rule:evenodd;clip-rule:evenodd;fill:#EC6913;}
	.st3{fill-rule:evenodd;clip-rule:evenodd;fill:#EC6A13;}
	.st4{fill-rule:evenodd;clip-rule:evenodd;fill:#EC6B12;}
	.st5{fill-rule:evenodd;clip-rule:evenodd;fill:#ED6C12;}
	.st6{fill-rule:evenodd;clip-rule:evenodd;fill:#ED6D11;}
	.st7{fill-rule:evenodd;clip-rule:evenodd;fill:#ED6E10;}
	.st8{fill-rule:evenodd;clip-rule:evenodd;fill:#ED6F10;}
	.st9{fill-rule:evenodd;clip-rule:evenodd;fill:#ED700F;}
	.st10{fill-rule:evenodd;clip-rule:evenodd;fill:#ED710F;}
	.st11{fill-rule:evenodd;clip-rule:evenodd;fill:#ED720E;}
	.st12{fill-rule:evenodd;clip-rule:evenodd;fill:#EE730E;}
	.st13{fill-rule:evenodd;clip-rule:evenodd;fill:#EE740D;}
	.st14{fill-rule:evenodd;clip-rule:evenodd;fill:#EE750C;}
	.st15{fill-rule:evenodd;clip-rule:evenodd;fill:#EE760C;}
	.st16{fill-rule:evenodd;clip-rule:evenodd;fill:#EE770B;}
	.st17{fill-rule:evenodd;clip-rule:evenodd;fill:#EE780B;}
	.st18{fill-rule:evenodd;clip-rule:evenodd;fill:#EF7A0A;}
	.st19{fill-rule:evenodd;clip-rule:evenodd;fill:#EF7B09;}
	.st20{fill-rule:evenodd;clip-rule:evenodd;fill:#EF7C09;}
	.st21{fill-rule:evenodd;clip-rule:evenodd;fill:#EF7D08;}
	.st22{fill-rule:evenodd;clip-rule:evenodd;fill:#EF7E08;}
	.st23{fill-rule:evenodd;clip-rule:evenodd;fill:#EF7F07;}
	.st24{fill-rule:evenodd;clip-rule:evenodd;fill:#EF8006;}
	.st25{fill-rule:evenodd;clip-rule:evenodd;fill:#F08106;}
	.st26{fill-rule:evenodd;clip-rule:evenodd;fill:#F08205;}
	.st27{fill-rule:evenodd;clip-rule:evenodd;fill:#F08305;}
	.st28{fill-rule:evenodd;clip-rule:evenodd;fill:#F08404;}
	.st29{fill-rule:evenodd;clip-rule:evenodd;fill:#F08504;}
	.st30{fill-rule:evenodd;clip-rule:evenodd;fill:#F08603;}
	.st31{fill-rule:evenodd;clip-rule:evenodd;fill:#F08702;}
	.st32{fill-rule:evenodd;clip-rule:evenodd;fill:#F18802;}
	.st33{fill-rule:evenodd;clip-rule:evenodd;fill:#F18901;}
	.st34{fill-rule:evenodd;clip-rule:evenodd;fill:#F18A01;}
	.st35{fill-rule:evenodd;clip-rule:evenodd;fill:#F18B00;}
	.st36{fill-rule:evenodd;clip-rule:evenodd;fill:#F18B01;}
	.st37{fill-rule:evenodd;clip-rule:evenodd;fill:#F18C02;}
	.st38{fill-rule:evenodd;clip-rule:evenodd;fill:#F18C03;}
	.st39{fill-rule:evenodd;clip-rule:evenodd;fill:#F18D04;}
	.st40{fill-rule:evenodd;clip-rule:evenodd;fill:#F18D05;}
	.st41{fill-rule:evenodd;clip-rule:evenodd;fill:#F18E06;}
	.st42{fill-rule:evenodd;clip-rule:evenodd;fill:#F18E07;}
	.st43{fill-rule:evenodd;clip-rule:evenodd;fill:#F18E08;}
	.st44{fill-rule:evenodd;clip-rule:evenodd;fill:#F28F09;}
	.st45{fill-rule:evenodd;clip-rule:evenodd;fill:#F28F0A;}
	.st46{fill-rule:evenodd;clip-rule:evenodd;fill:#F2900B;}
	.st47{fill-rule:evenodd;clip-rule:evenodd;fill:#F2900C;}
	.st48{fill-rule:evenodd;clip-rule:evenodd;fill:#F2900D;}
	.st49{fill-rule:evenodd;clip-rule:evenodd;fill:#F2910E;}
	.st50{fill-rule:evenodd;clip-rule:evenodd;fill:#F2910F;}
	.st51{fill-rule:evenodd;clip-rule:evenodd;fill:#F29210;}
	.st52{fill-rule:evenodd;clip-rule:evenodd;fill:#F29211;}
	.st53{fill-rule:evenodd;clip-rule:evenodd;fill:#F29312;}
	.st54{fill-rule:evenodd;clip-rule:evenodd;fill:#F29313;}
	.st55{fill-rule:evenodd;clip-rule:evenodd;fill:#F29314;}
	.st56{fill-rule:evenodd;clip-rule:evenodd;fill:#F29415;}
	.st57{fill-rule:evenodd;clip-rule:evenodd;fill:#F29416;}
	.st58{fill-rule:evenodd;clip-rule:evenodd;fill:#F29517;}
	.st59{fill-rule:evenodd;clip-rule:evenodd;fill:#F29518;}
	.st60{fill-rule:evenodd;clip-rule:evenodd;fill:#F29519;}
	.st61{fill-rule:evenodd;clip-rule:evenodd;fill:#F3961A;}
	.st62{fill-rule:evenodd;clip-rule:evenodd;fill:#F3961B;}
	.st63{fill-rule:evenodd;clip-rule:evenodd;fill:#F3971C;}
	.st64{fill-rule:evenodd;clip-rule:evenodd;fill:#F3971D;}
	.st65{fill-rule:evenodd;clip-rule:evenodd;fill:#F3981E;}
	.st66{fill-rule:evenodd;clip-rule:evenodd;fill:#F3981F;}
	.st67{fill-rule:evenodd;clip-rule:evenodd;fill:#F39820;}
	.st68{fill-rule:evenodd;clip-rule:evenodd;fill:#F39921;}
	.st69{fill-rule:evenodd;clip-rule:evenodd;fill:#F39922;}
	.st70{fill-rule:evenodd;clip-rule:evenodd;fill:#F39A23;}
	.st71{fill-rule:evenodd;clip-rule:evenodd;fill:#F39A24;}
	.st72{fill-rule:evenodd;clip-rule:evenodd;fill:#F39A25;}
	.st73{fill-rule:evenodd;clip-rule:evenodd;fill:#F39B26;}
	.st74{fill-rule:evenodd;clip-rule:evenodd;fill:#F39B27;}
	.st75{fill-rule:evenodd;clip-rule:evenodd;fill:#F39C28;}
	.st76{fill-rule:evenodd;clip-rule:evenodd;fill:#F39C29;}
	.st77{fill-rule:evenodd;clip-rule:evenodd;fill:#F39D2A;}
	.st78{fill-rule:evenodd;clip-rule:evenodd;fill:#F49D2B;}
	.st79{fill-rule:evenodd;clip-rule:evenodd;fill:#F49D2C;}
	.st80{fill-rule:evenodd;clip-rule:evenodd;fill:#F49E2D;}
	.st81{fill-rule:evenodd;clip-rule:evenodd;fill:#F49E2E;}
	.st82{fill-rule:evenodd;clip-rule:evenodd;fill:#F49F2F;}
	.st83{fill-rule:evenodd;clip-rule:evenodd;fill:#F49F30;}
	.st84{fill-rule:evenodd;clip-rule:evenodd;fill:#F49F31;}
	.st85{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A032;}
	.st86{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A033;}
	.st87{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A135;}
	.st88{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A136;}
	.st89{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A237;}
	.st90{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A238;}
	.st91{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A239;}
	.st92{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A33A;}
	.st93{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A33B;}
	.st94{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A43C;}
	.st95{fill-rule:evenodd;clip-rule:evenodd;fill:#F4A43D;}
	.st96{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A43E;}
	.st97{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A53F;}
	.st98{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A540;}
	.st99{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A641;}
	.st100{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A642;}
	.st101{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A743;}
	.st102{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A744;}
	.st103{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A745;}
	.st104{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A846;}
	.st105{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A847;}
	.st106{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A948;}
	.st107{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A949;}
	.st108{fill-rule:evenodd;clip-rule:evenodd;fill:#F5A94A;}
	.st109{fill-rule:evenodd;clip-rule:evenodd;fill:#F5AA4B;}
	.st110{fill-rule:evenodd;clip-rule:evenodd;fill:#F5AA4C;}
	.st111{fill-rule:evenodd;clip-rule:evenodd;fill:#F5AB4D;}
	.st112{fill-rule:evenodd;clip-rule:evenodd;fill:#F5AB4E;}
	.st113{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AC4F;}
	.st114{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AC50;}
	.st115{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AC51;}
	.st116{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AD52;}
	.st117{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AD53;}
	.st118{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AE54;}
	.st119{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AE55;}
	.st120{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AE56;}
	.st121{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AF57;}
	.st122{fill-rule:evenodd;clip-rule:evenodd;fill:#F6AF58;}
	.st123{fill-rule:evenodd;clip-rule:evenodd;fill:#F6B059;}
	.st124{fill-rule:evenodd;clip-rule:evenodd;fill:#F6B05A;}
	.st125{fill-rule:evenodd;clip-rule:evenodd;fill:#F6B15B;}
	.st126{fill-rule:evenodd;clip-rule:evenodd;fill:#F6B15C;}
	.st127{fill-rule:evenodd;clip-rule:evenodd;fill:#F6B15D;}
	.st128{fill-rule:evenodd;clip-rule:evenodd;fill:#F6B25E;}
	.st129{fill-rule:evenodd;clip-rule:evenodd;fill:#F6B25F;}
	.st130{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B360;}
	.st131{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B361;}
	.st132{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B362;}
	.st133{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B463;}
	.st134{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B464;}
	.st135{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B565;}
	.st136{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B566;}
	.st137{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B667;}
	.st138{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B668;}
	.st139{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B669;}
	.st140{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B76A;}
	.st141{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B76B;}
	.st142{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B86C;}
	.st143{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B86D;}
	.st144{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B86E;}
	.st145{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B96F;}
	.st146{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B970;}
	.st147{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BA71;}
	.st148{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BA72;}
	.st149{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BA73;}
	.st150{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BB74;}
	.st151{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BB75;}
	.st152{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BB76;}
	.st153{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BC77;}
	.st154{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BC78;}
	.st155{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BD79;}
	.st156{fill-rule:evenodd;clip-rule:evenodd;fill:#F7BD7A;}
	.st157{fill-rule:evenodd;clip-rule:evenodd;fill:#F8BD7B;}
	.st158{fill-rule:evenodd;clip-rule:evenodd;fill:#F8BE7C;}
	.st159{fill-rule:evenodd;clip-rule:evenodd;fill:#F8BE7D;}
	.st160{fill-rule:evenodd;clip-rule:evenodd;fill:#F8BF7E;}
	.st161{fill-rule:evenodd;clip-rule:evenodd;fill:#F8BF7F;}
	.st162{fill-rule:evenodd;clip-rule:evenodd;fill:#F8BF80;}
	.st163{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C081;}
	.st164{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C082;}
	.st165{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C183;}
	.st166{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C184;}
	.st167{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C185;}
	.st168{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C286;}
	.st169{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C287;}
	.st170{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C388;}
	.st171{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C389;}
	.st172{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C38A;}
	.st173{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C48B;}
	.st174{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C48C;}
	.st175{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C48D;}
	.st176{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C58E;}
	.st177{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C58F;}
	.st178{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C690;}
	.st179{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C691;}
	.st180{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C692;}
	.st181{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C793;}
	.st182{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C794;}
	.st183{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C895;}
	.st184{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C896;}
	.st185{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C897;}
	.st186{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C998;}
	.st187{fill-rule:evenodd;clip-rule:evenodd;fill:#F8C999;}
	.st188{fill-rule:evenodd;clip-rule:evenodd;fill:#F8CA9A;}
	.st189{fill-rule:evenodd;clip-rule:evenodd;fill:#F8CA9B;}
	.st190{fill-rule:evenodd;clip-rule:evenodd;fill:#F8CA9C;}
	.st191{fill-rule:evenodd;clip-rule:evenodd;fill:#F8CB9D;}
	.st192{fill-rule:evenodd;clip-rule:evenodd;fill:#F8CB9E;}
	.st193{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CCA0;}
	.st194{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CCA1;}
	.st195{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CCA2;}
	.st196{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CDA3;}
	.st197{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CDA4;}
	.st198{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CDA5;}
	.st199{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CEA6;}
	.st200{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CEA7;}
	.st201{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CFA8;}
	.st202{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CFA9;}
	.st203{fill-rule:evenodd;clip-rule:evenodd;fill:#F9CFAA;}
	.st204{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D0AB;}
	.st205{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D0AC;}
	.st206{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D1AD;}
	.st207{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D1AE;}
	.st208{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D1AF;}
	.st209{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D2B0;}
	.st210{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D2B1;}
	.st211{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D3B2;}
	.st212{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D3B3;}
	.st213{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D3B4;}
	.st214{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D4B5;}
	.st215{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D4B6;}
	.st216{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D4B7;}
	.st217{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D5B8;}
	.st218{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D5B9;}
	.st219{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D6BA;}
	.st220{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D6BB;}
	.st221{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D6BC;}
	.st222{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D7BD;}
	.st223{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D7BE;}
	.st224{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D8BF;}
	.st225{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D8C0;}
	.st226{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D8C1;}
	.st227{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D9C2;}
	.st228{fill-rule:evenodd;clip-rule:evenodd;fill:#F9D9C3;}
	.st229{fill-rule:evenodd;clip-rule:evenodd;fill:#F9DAC4;}
	.st230{fill-rule:evenodd;clip-rule:evenodd;fill:#FADAC5;}
	.st231{fill-rule:evenodd;clip-rule:evenodd;fill:#FADAC6;}
	.st232{fill-rule:evenodd;clip-rule:evenodd;fill:#FADBC7;}
	.st233{fill-rule:evenodd;clip-rule:evenodd;fill:#FADBC8;}
	.st234{fill-rule:evenodd;clip-rule:evenodd;fill:#FADCC9;}
	.st235{fill-rule:evenodd;clip-rule:evenodd;fill:#FADCCA;}
	.st236{fill-rule:evenodd;clip-rule:evenodd;fill:#FADCCB;}
	.st237{fill-rule:evenodd;clip-rule:evenodd;fill:#FADDCC;}
	.st238{fill-rule:evenodd;clip-rule:evenodd;fill:#FADDCD;}
	.st239{fill-rule:evenodd;clip-rule:evenodd;fill:#FADDCE;}
	.st240{fill-rule:evenodd;clip-rule:evenodd;fill:#FADECF;}
	.st241{fill-rule:evenodd;clip-rule:evenodd;fill:#FADED0;}
	.st242{fill-rule:evenodd;clip-rule:evenodd;fill:#FADFD1;}
	.st243{fill-rule:evenodd;clip-rule:evenodd;fill:#FADFD2;}
	.st244{fill-rule:evenodd;clip-rule:evenodd;fill:#FADFD3;}
	.st245{fill-rule:evenodd;clip-rule:evenodd;fill:#FAE0D4;}
	.st246{fill-rule:evenodd;clip-rule:evenodd;fill:#FAE0D5;}
	.st247{fill-rule:evenodd;clip-rule:evenodd;fill:#FAE1D6;}
	.st248{fill-rule:evenodd;clip-rule:evenodd;fill:#FAE1D7;}
	.st249{fill-rule:evenodd;clip-rule:evenodd;fill:#FAE2D8;}
	.st250{fill-rule:evenodd;clip-rule:evenodd;fill:#FAE3D9;}
	.st251{fill-rule:evenodd;clip-rule:evenodd;fill:#FAE3DA;}
	.st252{fill-rule:evenodd;clip-rule:evenodd;fill:#FBE4DB;}
	.st253{fill-rule:evenodd;clip-rule:evenodd;fill:#FBE5DC;}
	.st254{fill-rule:evenodd;clip-rule:evenodd;fill:#FBE6DD;}
	.st255{fill-rule:evenodd;clip-rule:evenodd;fill:#FBE6DE;}
	.st256{fill-rule:evenodd;clip-rule:evenodd;fill:#FBE7DF;}
	.st257{fill-rule:evenodd;clip-rule:evenodd;fill:#FBE8E0;}
	.st258{fill-rule:evenodd;clip-rule:evenodd;fill:#FBE9E1;}
	.st259{fill-rule:evenodd;clip-rule:evenodd;fill:#FBE9E2;}
	.st260{fill-rule:evenodd;clip-rule:evenodd;fill:#FCEAE3;}
	.st261{fill-rule:evenodd;clip-rule:evenodd;fill:#FCEBE4;}
	.st262{fill-rule:evenodd;clip-rule:evenodd;fill:#FCECE5;}
	.st263{fill-rule:evenodd;clip-rule:evenodd;fill:#FCEDE6;}
	.st264{fill-rule:evenodd;clip-rule:evenodd;fill:#FCEDE7;}
	.st265{fill-rule:evenodd;clip-rule:evenodd;fill:#FCEEE8;}
	.st266{fill-rule:evenodd;clip-rule:evenodd;fill:#FCEFE9;}
	.st267{fill-rule:evenodd;clip-rule:evenodd;fill:#FCF0EA;}
	.st268{fill-rule:evenodd;clip-rule:evenodd;fill:#FDF0EC;}
	.st269{fill-rule:evenodd;clip-rule:evenodd;fill:#FDF1ED;}
	.st270{fill-rule:evenodd;clip-rule:evenodd;fill:#FDF2EE;}
	.st271{fill-rule:evenodd;clip-rule:evenodd;fill:#FDF3EF;}
	.st272{fill-rule:evenodd;clip-rule:evenodd;fill:#FDF3F0;}
	.st273{fill-rule:evenodd;clip-rule:evenodd;fill:#FDF4F1;}
	.st274{fill-rule:evenodd;clip-rule:evenodd;fill:#FDF5F2;}
	.st275{fill-rule:evenodd;clip-rule:evenodd;fill:#FDF6F3;}
	.st276{fill-rule:evenodd;clip-rule:evenodd;fill:#FEF7F4;}
	.st277{fill-rule:evenodd;clip-rule:evenodd;fill:#FEF7F5;}
	.st278{fill-rule:evenodd;clip-rule:evenodd;fill:#FEF8F6;}
	.st279{fill-rule:evenodd;clip-rule:evenodd;fill:#FEF9F7;}
	.st280{fill-rule:evenodd;clip-rule:evenodd;fill:#FEFAF8;}
	.st281{fill-rule:evenodd;clip-rule:evenodd;fill:#FEFAF9;}
	.st282{fill-rule:evenodd;clip-rule:evenodd;fill:#FEFBFA;}
	.st283{fill-rule:evenodd;clip-rule:evenodd;fill:#FEFCFB;}
	.st284{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFDFC;}
	.st285{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFDFD;}
	.st286{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFEFE;}
	.st287{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st288{fill:#EE7B3B;}
	.st289{fill:#FEFEFE;}
	.st290{fill-rule:evenodd;clip-rule:evenodd;fill:#FEFEFE;}
	.st291{clip-path:url(#SVGID_4_);}
	.st292{fill-rule:evenodd;clip-rule:evenodd;fill:#D42218;}
	.st293{fill-rule:evenodd;clip-rule:evenodd;fill:#D42318;}
	.st294{fill-rule:evenodd;clip-rule:evenodd;fill:#D42418;}
	.st295{fill-rule:evenodd;clip-rule:evenodd;fill:#D52519;}
	.st296{fill-rule:evenodd;clip-rule:evenodd;fill:#D52619;}
	.st297{fill-rule:evenodd;clip-rule:evenodd;fill:#D52719;}
	.st298{fill-rule:evenodd;clip-rule:evenodd;fill:#D52819;}
	.st299{fill-rule:evenodd;clip-rule:evenodd;fill:#D6291A;}
	.st300{fill-rule:evenodd;clip-rule:evenodd;fill:#D62A1A;}
	.st301{fill-rule:evenodd;clip-rule:evenodd;fill:#D62B1A;}
	.st302{fill-rule:evenodd;clip-rule:evenodd;fill:#D62C1A;}
	.st303{fill-rule:evenodd;clip-rule:evenodd;fill:#D72D1B;}
	.st304{fill-rule:evenodd;clip-rule:evenodd;fill:#D72E1B;}
	.st305{fill-rule:evenodd;clip-rule:evenodd;fill:#D72F1B;}
	.st306{fill-rule:evenodd;clip-rule:evenodd;fill:#D7301B;}
	.st307{fill-rule:evenodd;clip-rule:evenodd;fill:#D8311C;}
	.st308{fill-rule:evenodd;clip-rule:evenodd;fill:#D8321C;}
	.st309{fill-rule:evenodd;clip-rule:evenodd;fill:#D8331C;}
	.st310{fill-rule:evenodd;clip-rule:evenodd;fill:#D8341C;}
	.st311{fill-rule:evenodd;clip-rule:evenodd;fill:#D9351D;}
	.st312{fill-rule:evenodd;clip-rule:evenodd;fill:#D9361D;}
	.st313{fill-rule:evenodd;clip-rule:evenodd;fill:#D9371D;}
	.st314{fill-rule:evenodd;clip-rule:evenodd;fill:#D9381D;}
	.st315{fill-rule:evenodd;clip-rule:evenodd;fill:#DA391D;}
	.st316{fill-rule:evenodd;clip-rule:evenodd;fill:#DA3A1E;}
	.st317{fill-rule:evenodd;clip-rule:evenodd;fill:#DA3B1E;}
	.st318{fill-rule:evenodd;clip-rule:evenodd;fill:#DA3C1E;}
	.st319{fill-rule:evenodd;clip-rule:evenodd;fill:#DB3D1E;}
	.st320{fill-rule:evenodd;clip-rule:evenodd;fill:#DB3E1F;}
	.st321{fill-rule:evenodd;clip-rule:evenodd;fill:#DB3F1F;}
	.st322{fill-rule:evenodd;clip-rule:evenodd;fill:#DB401F;}
	.st323{fill-rule:evenodd;clip-rule:evenodd;fill:#DC411F;}
	.st324{fill-rule:evenodd;clip-rule:evenodd;fill:#DC4220;}
	.st325{fill-rule:evenodd;clip-rule:evenodd;fill:#DC4320;}
	.st326{fill-rule:evenodd;clip-rule:evenodd;fill:#DC4420;}
	.st327{fill-rule:evenodd;clip-rule:evenodd;fill:#DD4520;}
	.st328{fill-rule:evenodd;clip-rule:evenodd;fill:#DD4621;}
	.st329{fill-rule:evenodd;clip-rule:evenodd;fill:#DD4721;}
	.st330{fill-rule:evenodd;clip-rule:evenodd;fill:#DD4821;}
	.st331{fill-rule:evenodd;clip-rule:evenodd;fill:#DE4921;}
	.st332{fill-rule:evenodd;clip-rule:evenodd;fill:#DE4A22;}
	.st333{fill-rule:evenodd;clip-rule:evenodd;fill:#DE4B22;}
	.st334{fill-rule:evenodd;clip-rule:evenodd;fill:#DE4C22;}
	.st335{fill-rule:evenodd;clip-rule:evenodd;fill:#DF4D22;}
	.st336{fill-rule:evenodd;clip-rule:evenodd;fill:#DF4E23;}
	.st337{fill-rule:evenodd;clip-rule:evenodd;fill:#DF4F23;}
	.st338{fill-rule:evenodd;clip-rule:evenodd;fill:#DF5023;}
	.st339{fill-rule:evenodd;clip-rule:evenodd;fill:#E05123;}
	.st340{fill-rule:evenodd;clip-rule:evenodd;fill:#E05223;}
	.st341{fill-rule:evenodd;clip-rule:evenodd;fill:#E05324;}
	.st342{fill-rule:evenodd;clip-rule:evenodd;fill:#E05424;}
	.st343{fill-rule:evenodd;clip-rule:evenodd;fill:#E15524;}
	.st344{fill-rule:evenodd;clip-rule:evenodd;fill:#E15624;}
	.st345{fill-rule:evenodd;clip-rule:evenodd;fill:#E15725;}
	.st346{fill-rule:evenodd;clip-rule:evenodd;fill:#E15825;}
	.st347{fill-rule:evenodd;clip-rule:evenodd;fill:#E25925;}
	.st348{fill-rule:evenodd;clip-rule:evenodd;fill:#E25A25;}
	.st349{fill-rule:evenodd;clip-rule:evenodd;fill:#E25B26;}
	.st350{fill-rule:evenodd;clip-rule:evenodd;fill:#E25C26;}
	.st351{fill-rule:evenodd;clip-rule:evenodd;fill:#E35D26;}
	.st352{fill-rule:evenodd;clip-rule:evenodd;fill:#E35E26;}
	.st353{fill-rule:evenodd;clip-rule:evenodd;fill:#E36027;}
	.st354{fill-rule:evenodd;clip-rule:evenodd;fill:#E36127;}
	.st355{fill-rule:evenodd;clip-rule:evenodd;fill:#E46227;}
	.st356{fill-rule:evenodd;clip-rule:evenodd;fill:#E46327;}
	.st357{fill-rule:evenodd;clip-rule:evenodd;fill:#E46428;}
	.st358{fill-rule:evenodd;clip-rule:evenodd;fill:#E46528;}
	.st359{fill-rule:evenodd;clip-rule:evenodd;fill:#E56628;}
	.st360{fill-rule:evenodd;clip-rule:evenodd;fill:#E56728;}
	.st361{fill-rule:evenodd;clip-rule:evenodd;fill:#E56829;}
	.st362{fill-rule:evenodd;clip-rule:evenodd;fill:#E56929;}
	.st363{fill-rule:evenodd;clip-rule:evenodd;fill:#E66A29;}
	.st364{fill-rule:evenodd;clip-rule:evenodd;fill:#E66B29;}
	.st365{fill-rule:evenodd;clip-rule:evenodd;fill:#E66C2A;}
	.st366{fill-rule:evenodd;clip-rule:evenodd;fill:#E66D2A;}
	.st367{fill-rule:evenodd;clip-rule:evenodd;fill:#E76E2A;}
	.st368{fill-rule:evenodd;clip-rule:evenodd;fill:#E76F2A;}
	.st369{fill-rule:evenodd;clip-rule:evenodd;fill:#E7702A;}
	.st370{fill-rule:evenodd;clip-rule:evenodd;fill:#E7712B;}
	.st371{fill-rule:evenodd;clip-rule:evenodd;fill:#E8722B;}
	.st372{fill-rule:evenodd;clip-rule:evenodd;fill:#E8732B;}
	.st373{fill-rule:evenodd;clip-rule:evenodd;fill:#E8742B;}
	.st374{fill-rule:evenodd;clip-rule:evenodd;fill:#E8752C;}
	.st375{fill-rule:evenodd;clip-rule:evenodd;fill:#E9762C;}
	.st376{fill-rule:evenodd;clip-rule:evenodd;fill:#E9772C;}
	.st377{fill-rule:evenodd;clip-rule:evenodd;fill:#E9782C;}
	.st378{fill-rule:evenodd;clip-rule:evenodd;fill:#E9792D;}
	.st379{fill-rule:evenodd;clip-rule:evenodd;fill:#EA7A2D;}
	.st380{fill-rule:evenodd;clip-rule:evenodd;fill:#EA7B2D;}
	.st381{fill-rule:evenodd;clip-rule:evenodd;fill:#EA7C2D;}
	.st382{fill-rule:evenodd;clip-rule:evenodd;fill:#EA7D2E;}
	.st383{fill-rule:evenodd;clip-rule:evenodd;fill:#EB7E2E;}
	.st384{fill-rule:evenodd;clip-rule:evenodd;fill:#EB7F2E;}
	.st385{fill-rule:evenodd;clip-rule:evenodd;fill:#EB802E;}
	.st386{fill-rule:evenodd;clip-rule:evenodd;fill:#EB812F;}
	.st387{fill-rule:evenodd;clip-rule:evenodd;fill:#EC822F;}
	.st388{fill-rule:evenodd;clip-rule:evenodd;fill:#EC832F;}
	.st389{fill-rule:evenodd;clip-rule:evenodd;fill:#EC842F;}
	.st390{fill-rule:evenodd;clip-rule:evenodd;fill:#EC8530;}
	.st391{fill-rule:evenodd;clip-rule:evenodd;fill:#ED8630;}
	.st392{fill-rule:evenodd;clip-rule:evenodd;fill:#ED8730;}
	.st393{fill-rule:evenodd;clip-rule:evenodd;fill:#ED8830;}
	.st394{fill-rule:evenodd;clip-rule:evenodd;fill:#ED8930;}
	.st395{fill-rule:evenodd;clip-rule:evenodd;fill:#EE8A31;}
	.st396{fill-rule:evenodd;clip-rule:evenodd;fill:#EE8B31;}
	.st397{fill-rule:evenodd;clip-rule:evenodd;fill:#EE8C31;}
	.st398{fill-rule:evenodd;clip-rule:evenodd;fill:#EE8D31;}
	.st399{fill-rule:evenodd;clip-rule:evenodd;fill:#EF8E32;}
	.st400{fill-rule:evenodd;clip-rule:evenodd;fill:#EF8F32;}
	.st401{fill-rule:evenodd;clip-rule:evenodd;fill:#EF9032;}
	.st402{fill-rule:evenodd;clip-rule:evenodd;fill:#EF9132;}
	.st403{fill-rule:evenodd;clip-rule:evenodd;fill:#F09233;}
	.st404{fill-rule:evenodd;clip-rule:evenodd;fill:#F09333;}
	.st405{fill-rule:evenodd;clip-rule:evenodd;fill:#F09433;}
	.st406{fill-rule:evenodd;clip-rule:evenodd;fill:#F09533;}
	.st407{fill-rule:evenodd;clip-rule:evenodd;fill:#F19634;}
	.st408{fill-rule:evenodd;clip-rule:evenodd;fill:#F19734;}
	.st409{fill-rule:evenodd;clip-rule:evenodd;fill:#F19834;}
	.st410{fill-rule:evenodd;clip-rule:evenodd;fill:#F19934;}
	.st411{fill-rule:evenodd;clip-rule:evenodd;fill:#F29A35;}
	.st412{fill-rule:evenodd;clip-rule:evenodd;fill:#F29B35;}
	.st413{fill-rule:evenodd;clip-rule:evenodd;fill:#F29C35;}
	.st414{fill-rule:evenodd;clip-rule:evenodd;fill:#F29D35;}
	.st415{fill-rule:evenodd;clip-rule:evenodd;fill:#F39E36;}
	.st416{fill-rule:evenodd;clip-rule:evenodd;fill:#F39F36;}
	.st417{fill-rule:evenodd;clip-rule:evenodd;fill:#F3A036;}
</style>
<g>
	<g>
		<defs>
			<path id="SVGID_1_" d="M70.8,9.8h100.5V178H3.8V76.6C3.9,39.7,33.9,9.8,70.8,9.8z"/>
		</defs>
		<clipPath id="SVGID_2_">
			<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st0">
			<polygon class="st1" points="29.8,178 171.3,170.4 171.3,178 			"/>
			<polygon class="st1" points="3.8,178 171.3,169 171.3,170.4 29.8,178 3.8,178 			"/>
			<polygon class="st2" points="3.8,176.6 171.3,167.6 171.3,169 3.8,178 			"/>
			<polygon class="st3" points="3.8,175.2 171.3,166.2 171.3,167.6 3.8,176.6 			"/>
			<polygon class="st4" points="3.8,173.8 171.3,164.8 171.3,166.2 3.8,175.2 			"/>
			<polygon class="st5" points="3.8,172.4 171.3,163.4 171.3,164.8 3.8,173.8 			"/>
			<polygon class="st6" points="3.8,171 171.3,162 171.3,163.4 3.8,172.4 			"/>
			<polygon class="st7" points="3.8,169.6 171.3,160.6 171.3,162 3.8,171 			"/>
			<polygon class="st8" points="3.8,168.2 171.3,159.2 171.3,160.6 3.8,169.6 			"/>
			<polygon class="st9" points="3.8,166.8 171.3,157.8 171.3,159.2 3.8,168.2 			"/>
			<polygon class="st10" points="3.8,165.4 171.3,156.4 171.3,157.8 3.8,166.8 			"/>
			<polygon class="st11" points="3.8,164 171.3,155 171.3,156.4 3.8,165.4 			"/>
			<polygon class="st12" points="3.8,162.6 171.3,153.6 171.3,155 3.8,164 			"/>
			<polygon class="st13" points="3.8,161.2 171.3,152.2 171.3,153.6 3.8,162.6 			"/>
			<polygon class="st14" points="3.8,159.8 171.3,150.8 171.3,152.2 3.8,161.2 			"/>
			<polygon class="st15" points="3.8,158.4 171.3,149.4 171.3,150.8 3.8,159.8 			"/>
			<polygon class="st16" points="3.8,157 171.3,148 171.3,149.4 3.8,158.4 			"/>
			<polygon class="st17" points="3.8,155.6 171.3,146.6 171.3,148 3.8,157 			"/>
			<polygon class="st18" points="3.8,154.2 171.3,145.2 171.3,146.6 3.8,155.6 			"/>
			<polygon class="st19" points="3.8,152.8 171.3,143.7 171.3,145.2 3.8,154.2 			"/>
			<polygon class="st20" points="3.8,151.4 171.3,142.3 171.3,143.7 3.8,152.8 			"/>
			<polygon class="st21" points="3.8,150 171.3,140.9 171.3,142.3 3.8,151.4 			"/>
			<polygon class="st22" points="3.8,148.5 171.3,139.5 171.3,140.9 3.8,150 			"/>
			<polygon class="st23" points="3.8,147.1 171.3,138.1 171.3,139.5 3.8,148.5 			"/>
			<polygon class="st24" points="3.8,145.7 171.3,136.7 171.3,138.1 3.8,147.1 			"/>
			<polygon class="st25" points="3.8,144.3 171.3,135.3 171.3,136.7 3.8,145.7 			"/>
			<polygon class="st26" points="3.8,142.9 171.3,133.9 171.3,135.3 3.8,144.3 			"/>
			<polygon class="st27" points="3.8,141.5 171.3,132.5 171.3,133.9 3.8,142.9 			"/>
			<polygon class="st28" points="3.8,140.1 171.3,131.1 171.3,132.5 3.8,141.5 			"/>
			<polygon class="st29" points="3.8,138.7 171.3,129.7 171.3,131.1 3.8,140.1 			"/>
			<polygon class="st30" points="3.8,137.3 171.3,128.3 171.3,129.7 3.8,138.7 			"/>
			<polygon class="st31" points="3.8,135.9 171.3,126.9 171.3,128.3 3.8,137.3 			"/>
			<polygon class="st32" points="3.8,134.5 171.3,125.5 171.3,126.9 3.8,135.9 			"/>
			<polygon class="st33" points="3.8,133.1 171.3,124.1 171.3,125.5 3.8,134.5 			"/>
			<polygon class="st34" points="3.8,131.7 171.3,122.7 171.3,124.1 3.8,133.1 			"/>
			<polygon class="st35" points="3.8,130.3 171.3,121.3 171.3,122.7 3.8,131.7 			"/>
			<polygon class="st35" points="3.8,129.8 171.3,120.8 171.3,121.3 3.8,130.3 			"/>
			<polygon class="st36" points="3.8,129.4 171.3,120.4 171.3,120.8 3.8,129.8 			"/>
			<polygon class="st37" points="3.8,128.9 171.3,119.9 171.3,120.4 3.8,129.4 			"/>
			<polygon class="st38" points="3.8,128.5 171.3,119.4 171.3,119.9 3.8,128.9 			"/>
			<polygon class="st39" points="3.8,128 171.3,119 171.3,119.4 3.8,128.5 			"/>
			<polygon class="st40" points="3.8,127.5 171.3,118.5 171.3,119 3.8,128 			"/>
			<polygon class="st41" points="3.8,127.1 171.3,118.1 171.3,118.5 3.8,127.5 			"/>
			<polygon class="st42" points="3.8,126.6 171.3,117.6 171.3,118.1 3.8,127.1 			"/>
			<polygon class="st43" points="3.8,126.2 171.3,117.2 171.3,117.6 3.8,126.6 			"/>
			<polygon class="st44" points="3.8,125.7 171.3,116.7 171.3,117.2 3.8,126.2 			"/>
			<polygon class="st45" points="3.8,125.3 171.3,116.2 171.3,116.7 3.8,125.7 			"/>
			<polygon class="st46" points="3.8,124.8 171.3,115.8 171.3,116.2 3.8,125.3 			"/>
			<polygon class="st47" points="3.8,124.3 171.3,115.3 171.3,115.8 3.8,124.8 			"/>
			<polygon class="st48" points="3.8,123.9 171.3,114.9 171.3,115.3 3.8,124.3 			"/>
			<polygon class="st49" points="3.8,123.4 171.3,114.4 171.3,114.9 3.8,123.9 			"/>
			<polygon class="st50" points="3.8,123 171.3,114 171.3,114.4 3.8,123.4 			"/>
			<polygon class="st51" points="3.8,122.5 171.3,113.5 171.3,114 3.8,123 			"/>
			<polygon class="st52" points="3.8,122.1 171.3,113 171.3,113.5 3.8,122.5 			"/>
			<polygon class="st53" points="3.8,121.6 171.3,112.6 171.3,113 3.8,122.1 			"/>
			<polygon class="st54" points="3.8,121.1 171.3,112.1 171.3,112.6 3.8,121.6 			"/>
			<polygon class="st55" points="3.8,120.7 171.3,111.7 171.3,112.1 3.8,121.1 			"/>
			<polygon class="st56" points="3.8,120.2 171.3,111.2 171.3,111.7 3.8,120.7 			"/>
			<polygon class="st57" points="3.8,119.8 171.3,110.8 171.3,111.2 3.8,120.2 			"/>
			<polygon class="st58" points="3.8,119.3 171.3,110.3 171.3,110.8 3.8,119.8 			"/>
			<polygon class="st59" points="3.8,118.9 171.3,109.8 171.3,110.3 3.8,119.3 			"/>
			<polygon class="st60" points="3.8,118.4 171.3,109.4 171.3,109.8 3.8,118.9 			"/>
			<polygon class="st61" points="3.8,117.9 171.3,108.9 171.3,109.4 3.8,118.4 			"/>
			<polygon class="st62" points="3.8,117.5 171.3,108.5 171.3,108.9 3.8,117.9 			"/>
			<polygon class="st63" points="3.8,117 171.3,108 171.3,108.5 3.8,117.5 			"/>
			<polygon class="st64" points="3.8,116.6 171.3,107.6 171.3,108 3.8,117 			"/>
			<polygon class="st65" points="3.8,116.1 171.3,107.1 171.3,107.6 3.8,116.6 			"/>
			<polygon class="st66" points="3.8,115.7 171.3,106.6 171.3,107.1 3.8,116.1 			"/>
			<polygon class="st67" points="3.8,115.2 171.3,106.2 171.3,106.6 3.8,115.7 			"/>
			<polygon class="st68" points="3.8,114.7 171.3,105.7 171.3,106.2 3.8,115.2 			"/>
			<polygon class="st69" points="3.8,114.3 171.3,105.3 171.3,105.7 3.8,114.7 			"/>
			<polygon class="st70" points="3.8,113.8 171.3,104.8 171.3,105.3 3.8,114.3 			"/>
			<polygon class="st71" points="3.8,113.4 171.3,104.4 171.3,104.8 3.8,113.8 			"/>
			<polygon class="st72" points="3.8,112.9 171.3,103.9 171.3,104.4 3.8,113.4 			"/>
			<polygon class="st73" points="3.8,112.4 171.3,103.4 171.3,103.9 3.8,112.9 			"/>
			<polygon class="st74" points="3.8,112 171.3,103 171.3,103.4 3.8,112.4 			"/>
			<polygon class="st75" points="3.8,111.5 171.3,102.5 171.3,103 3.8,112 			"/>
			<polygon class="st76" points="3.8,111.1 171.3,102.1 171.3,102.5 3.8,111.5 			"/>
			<polygon class="st77" points="3.8,110.6 171.3,101.6 171.3,102.1 3.8,111.1 			"/>
			<polygon class="st78" points="3.8,110.2 171.3,101.1 171.3,101.6 3.8,110.6 			"/>
			<polygon class="st79" points="3.8,109.7 171.3,100.7 171.3,101.1 3.8,110.2 			"/>
			<polygon class="st80" points="3.8,109.2 171.3,100.2 171.3,100.7 3.8,109.7 			"/>
			<polygon class="st81" points="3.8,108.8 171.3,99.8 171.3,100.2 3.8,109.2 			"/>
			<polygon class="st82" points="3.8,108.3 171.3,99.3 171.3,99.8 3.8,108.8 			"/>
			<polygon class="st83" points="3.8,107.9 171.3,98.9 171.3,99.3 3.8,108.3 			"/>
			<polygon class="st84" points="3.8,107.4 171.3,98.4 171.3,98.9 3.8,107.9 			"/>
			<polygon class="st85" points="3.8,107 171.3,97.9 171.3,98.4 3.8,107.4 			"/>
			<polygon class="st86" points="3.8,106.5 171.3,97.5 171.3,97.9 3.8,107 			"/>
			<polygon class="st87" points="3.8,106 171.3,97 171.3,97.5 3.8,106.5 			"/>
			<polygon class="st88" points="3.8,105.6 171.3,96.6 171.3,97 3.8,106 			"/>
			<polygon class="st89" points="3.8,105.1 171.3,96.1 171.3,96.6 3.8,105.6 			"/>
			<polygon class="st90" points="3.8,104.7 171.3,95.7 171.3,96.1 3.8,105.1 			"/>
			<polygon class="st91" points="3.8,104.2 171.3,95.2 171.3,95.7 3.8,104.7 			"/>
			<polygon class="st92" points="3.8,103.8 171.3,94.7 171.3,95.2 3.8,104.2 			"/>
			<polygon class="st93" points="3.8,103.3 171.3,94.3 171.3,94.7 3.8,103.8 			"/>
			<polygon class="st94" points="3.8,102.8 171.3,93.8 171.3,94.3 3.8,103.3 			"/>
			<polygon class="st95" points="3.8,102.4 171.3,93.4 171.3,93.8 3.8,102.8 			"/>
			<polygon class="st96" points="3.8,101.9 171.3,92.9 171.3,93.4 3.8,102.4 			"/>
			<polygon class="st97" points="3.8,101.5 171.3,92.5 171.3,92.9 3.8,101.9 			"/>
			<polygon class="st98" points="3.8,101 171.3,92 171.3,92.5 3.8,101.5 			"/>
			<polygon class="st99" points="3.8,100.6 171.3,91.5 171.3,92 3.8,101 			"/>
			<polygon class="st100" points="3.8,100.1 171.3,91.1 171.3,91.5 3.8,100.6 			"/>
			<polygon class="st101" points="3.8,99.6 171.3,90.6 171.3,91.1 3.8,100.1 			"/>
			<polygon class="st102" points="3.8,99.2 171.3,90.2 171.3,90.6 3.8,99.6 			"/>
			<polygon class="st103" points="3.8,98.7 171.3,89.7 171.3,90.2 3.8,99.2 			"/>
			<polygon class="st104" points="3.8,98.3 171.3,89.3 171.3,89.7 3.8,98.7 			"/>
			<polygon class="st105" points="3.8,97.8 171.3,88.8 171.3,89.3 3.8,98.3 			"/>
			<polygon class="st106" points="3.8,97.4 171.3,88.3 171.3,88.8 3.8,97.8 			"/>
			<polygon class="st107" points="3.8,96.9 171.3,87.9 171.3,88.3 3.8,97.4 			"/>
			<polygon class="st108" points="3.8,96.4 171.3,87.4 171.3,87.9 3.8,96.9 			"/>
			<polygon class="st109" points="3.8,96 171.3,87 171.3,87.4 3.8,96.4 			"/>
			<polygon class="st110" points="3.8,95.5 171.3,86.5 171.3,87 3.8,96 			"/>
			<polygon class="st111" points="3.8,95.1 171.3,86.1 171.3,86.5 3.8,95.5 			"/>
			<polygon class="st112" points="3.8,94.6 171.3,85.6 171.3,86.1 3.8,95.1 			"/>
			<polygon class="st113" points="3.8,94.2 171.3,85.1 171.3,85.6 3.8,94.6 			"/>
			<polygon class="st114" points="3.8,93.7 171.3,84.7 171.3,85.1 3.8,94.2 			"/>
			<polygon class="st115" points="3.8,93.2 171.3,84.2 171.3,84.7 3.8,93.7 			"/>
			<polygon class="st116" points="3.8,92.8 171.3,83.8 171.3,84.2 3.8,93.2 			"/>
			<polygon class="st117" points="3.8,92.3 171.3,83.3 171.3,83.8 3.8,92.8 			"/>
			<polygon class="st118" points="3.8,91.9 171.3,82.9 171.3,83.3 3.8,92.3 			"/>
			<polygon class="st119" points="3.8,91.4 171.3,82.4 171.3,82.9 3.8,91.9 			"/>
			<polygon class="st120" points="3.8,90.9 171.3,81.9 171.3,82.4 3.8,91.4 			"/>
			<polygon class="st121" points="3.8,90.5 171.3,81.5 171.3,81.9 3.8,90.9 			"/>
			<polygon class="st122" points="3.8,90 171.3,81 171.3,81.5 3.8,90.5 			"/>
			<polygon class="st123" points="3.8,89.6 171.3,80.6 171.3,81 3.8,90 			"/>
			<polygon class="st124" points="3.8,89.1 171.3,80.1 171.3,80.6 3.8,89.6 			"/>
			<polygon class="st125" points="3.8,88.7 171.3,79.6 171.3,80.1 3.8,89.1 			"/>
			<polygon class="st126" points="3.8,88.2 171.3,79.2 171.3,79.6 3.8,88.7 			"/>
			<polygon class="st127" points="3.8,87.7 171.3,78.7 171.3,79.2 3.8,88.2 			"/>
			<polygon class="st128" points="3.8,87.3 171.3,78.3 171.3,78.7 3.8,87.7 			"/>
			<polygon class="st129" points="3.8,86.8 171.3,77.8 171.3,78.3 3.8,87.3 			"/>
			<polygon class="st130" points="3.8,86.4 171.3,77.4 171.3,77.8 3.8,86.8 			"/>
			<polygon class="st131" points="3.8,85.9 171.3,76.9 171.3,77.4 3.8,86.4 			"/>
			<polygon class="st132" points="3.8,85.5 171.3,76.4 171.3,76.9 3.8,85.9 			"/>
			<polygon class="st133" points="3.8,85 171.3,76 171.3,76.4 3.8,85.5 			"/>
			<polygon class="st134" points="3.8,84.5 171.3,75.5 171.3,76 3.8,85 			"/>
			<polygon class="st135" points="3.8,84.1 171.3,75.1 171.3,75.5 3.8,84.5 			"/>
			<polygon class="st136" points="3.8,83.6 171.3,74.6 171.3,75.1 3.8,84.1 			"/>
			<polygon class="st137" points="3.8,83.2 171.3,74.2 171.3,74.6 3.8,83.6 			"/>
			<polygon class="st138" points="3.8,82.7 171.3,73.7 171.3,74.2 3.8,83.2 			"/>
			<polygon class="st138" points="3.8,82.5 171.3,73.4 171.3,73.7 3.8,82.7 			"/>
			<polygon class="st139" points="3.8,82.2 171.3,73.2 171.3,73.4 3.8,82.5 			"/>
			<polygon class="st140" points="3.8,81.9 171.3,72.9 171.3,73.2 3.8,82.2 			"/>
			<polygon class="st141" points="3.8,81.7 171.3,72.7 171.3,72.9 3.8,81.9 			"/>
			<polygon class="st142" points="3.8,81.4 171.3,72.4 171.3,72.7 3.8,81.7 			"/>
			<polygon class="st143" points="3.8,81.2 171.3,72.2 171.3,72.4 3.8,81.4 			"/>
			<polygon class="st144" points="3.8,80.9 171.3,71.9 171.3,72.2 3.8,81.2 			"/>
			<polygon class="st145" points="3.8,80.7 171.3,71.6 171.3,71.9 3.8,80.9 			"/>
			<polygon class="st146" points="3.8,80.4 171.3,71.4 171.3,71.6 3.8,80.7 			"/>
			<polygon class="st147" points="3.8,80.1 171.3,71.1 171.3,71.4 3.8,80.4 			"/>
			<polygon class="st148" points="3.8,79.9 171.3,70.9 171.3,71.1 3.8,80.1 			"/>
			<polygon class="st149" points="3.8,79.6 171.3,70.6 171.3,70.9 3.8,79.9 			"/>
			<polygon class="st150" points="3.8,79.4 171.3,70.4 171.3,70.6 3.8,79.6 			"/>
			<polygon class="st151" points="3.8,79.1 171.3,70.1 171.3,70.4 3.8,79.4 			"/>
			<polygon class="st152" points="3.8,78.9 171.3,69.8 171.3,70.1 3.8,79.1 			"/>
			<polygon class="st153" points="3.8,78.6 171.3,69.6 171.3,69.8 3.8,78.9 			"/>
			<polygon class="st154" points="3.8,78.3 171.3,69.3 171.3,69.6 3.8,78.6 			"/>
			<polygon class="st155" points="3.8,78.1 171.3,69.1 171.3,69.3 3.8,78.3 			"/>
			<polygon class="st156" points="3.8,77.8 171.3,68.8 171.3,69.1 3.8,78.1 			"/>
			<polygon class="st157" points="3.8,77.6 171.3,68.6 171.3,68.8 3.8,77.8 			"/>
			<polygon class="st158" points="3.8,77.3 171.3,68.3 171.3,68.6 3.8,77.6 			"/>
			<polygon class="st159" points="3.8,77.1 171.3,68 171.3,68.3 3.8,77.3 			"/>
			<polygon class="st160" points="3.8,76.8 171.3,67.8 171.3,68 3.8,77.1 			"/>
			<polygon class="st161" points="3.8,76.5 171.3,67.5 171.3,67.8 3.8,76.8 			"/>
			<polygon class="st162" points="3.8,76.3 171.3,67.3 171.3,67.5 3.8,76.5 			"/>
			<polygon class="st163" points="3.8,76 171.3,67 171.3,67.3 3.8,76.3 			"/>
			<polygon class="st164" points="3.8,75.8 171.3,66.8 171.3,67 3.8,76 			"/>
			<polygon class="st165" points="3.8,75.5 171.3,66.5 171.3,66.8 3.8,75.8 			"/>
			<polygon class="st166" points="3.8,75.3 171.3,66.2 171.3,66.5 3.8,75.5 			"/>
			<polygon class="st167" points="3.8,75 171.3,66 171.3,66.2 3.8,75.3 			"/>
			<polygon class="st168" points="3.8,74.7 171.3,65.7 171.3,66 3.8,75 			"/>
			<polygon class="st169" points="3.8,74.5 171.3,65.5 171.3,65.7 3.8,74.7 			"/>
			<polygon class="st170" points="3.8,74.2 171.3,65.2 171.3,65.5 3.8,74.5 			"/>
			<polygon class="st171" points="3.8,74 171.3,65 171.3,65.2 3.8,74.2 			"/>
			<polygon class="st172" points="3.8,73.7 171.3,64.7 171.3,65 3.8,74 			"/>
			<polygon class="st173" points="3.8,73.5 171.3,64.4 171.3,64.7 3.8,73.7 			"/>
			<polygon class="st174" points="3.8,73.2 171.3,64.2 171.3,64.4 3.8,73.5 			"/>
			<polygon class="st175" points="3.8,72.9 171.3,63.9 171.3,64.2 3.8,73.2 			"/>
			<polygon class="st176" points="3.8,72.7 171.3,63.7 171.3,63.9 3.8,72.9 			"/>
			<polygon class="st177" points="3.8,72.4 171.3,63.4 171.3,63.7 3.8,72.7 			"/>
			<polygon class="st178" points="3.8,72.2 171.3,63.2 171.3,63.4 3.8,72.4 			"/>
			<polygon class="st179" points="3.8,71.9 171.3,62.9 171.3,63.2 3.8,72.2 			"/>
			<polygon class="st180" points="3.8,71.7 171.3,62.6 171.3,62.9 3.8,71.9 			"/>
			<polygon class="st181" points="3.8,71.4 171.3,62.4 171.3,62.6 3.8,71.7 			"/>
			<polygon class="st182" points="3.8,71.1 171.3,62.1 171.3,62.4 3.8,71.4 			"/>
			<polygon class="st183" points="3.8,70.9 171.3,61.9 171.3,62.1 3.8,71.1 			"/>
			<polygon class="st184" points="3.8,70.6 171.3,61.6 171.3,61.9 3.8,70.9 			"/>
			<polygon class="st185" points="3.8,70.4 171.3,61.4 171.3,61.6 3.8,70.6 			"/>
			<polygon class="st186" points="3.8,70.1 171.3,61.1 171.3,61.4 3.8,70.4 			"/>
			<polygon class="st187" points="3.8,69.9 171.3,60.8 171.3,61.1 3.8,70.1 			"/>
			<polygon class="st188" points="3.8,69.6 171.3,60.6 171.3,60.8 3.8,69.9 			"/>
			<polygon class="st189" points="3.8,69.3 171.3,60.3 171.3,60.6 3.8,69.6 			"/>
			<polygon class="st190" points="3.8,69.1 171.3,60.1 171.3,60.3 3.8,69.3 			"/>
			<polygon class="st191" points="3.8,68.8 171.3,59.8 171.3,60.1 3.8,69.1 			"/>
			<polygon class="st192" points="3.8,68.6 171.3,59.6 171.3,59.8 3.8,68.8 			"/>
			<polygon class="st193" points="3.8,68.3 171.3,59.3 171.3,59.6 3.8,68.6 			"/>
			<polygon class="st194" points="3.8,68.1 171.3,59 171.3,59.3 3.8,68.3 			"/>
			<polygon class="st195" points="3.8,67.8 171.3,58.8 171.3,59 3.8,68.1 			"/>
			<polygon class="st196" points="3.8,67.5 171.3,58.5 171.3,58.8 3.8,67.8 			"/>
			<polygon class="st197" points="3.8,67.3 171.3,58.3 171.3,58.5 3.8,67.5 			"/>
			<polygon class="st198" points="3.8,67 171.3,58 171.3,58.3 3.8,67.3 			"/>
			<polygon class="st199" points="3.8,66.8 171.3,57.8 171.3,58 3.8,67 			"/>
			<polygon class="st200" points="3.8,66.5 171.3,57.5 171.3,57.8 3.8,66.8 			"/>
			<polygon class="st201" points="3.8,66.3 171.3,57.2 171.3,57.5 3.8,66.5 			"/>
			<polygon class="st202" points="3.8,66 171.3,57 171.3,57.2 3.8,66.3 			"/>
			<polygon class="st203" points="3.8,65.7 171.3,56.7 171.3,57 3.8,66 			"/>
			<polygon class="st204" points="3.8,65.5 171.3,56.5 171.3,56.7 3.8,65.7 			"/>
			<polygon class="st205" points="3.8,65.2 171.3,56.2 171.3,56.5 3.8,65.5 			"/>
			<polygon class="st206" points="3.8,65 171.3,56 171.3,56.2 3.8,65.2 			"/>
			<polygon class="st207" points="3.8,64.7 171.3,55.7 171.3,56 3.8,65 			"/>
			<polygon class="st208" points="3.8,64.5 171.3,55.4 171.3,55.7 3.8,64.7 			"/>
			<polygon class="st209" points="3.8,64.2 171.3,55.2 171.3,55.4 3.8,64.5 			"/>
			<polygon class="st210" points="3.8,63.9 171.3,54.9 171.3,55.2 3.8,64.2 			"/>
			<polygon class="st211" points="3.8,63.7 171.3,54.7 171.3,54.9 3.8,63.9 			"/>
			<polygon class="st212" points="3.8,63.4 171.3,54.4 171.3,54.7 3.8,63.7 			"/>
			<polygon class="st213" points="3.8,63.2 171.3,54.2 171.3,54.4 3.8,63.4 			"/>
			<polygon class="st214" points="3.8,62.9 171.3,53.9 171.3,54.2 3.8,63.2 			"/>
			<polygon class="st215" points="3.8,62.7 171.3,53.6 171.3,53.9 3.8,62.9 			"/>
			<polygon class="st216" points="3.8,62.4 171.3,53.4 171.3,53.6 3.8,62.7 			"/>
			<polygon class="st217" points="3.8,62.1 171.3,53.1 171.3,53.4 3.8,62.4 			"/>
			<polygon class="st218" points="3.8,61.9 171.3,52.9 171.3,53.1 3.8,62.1 			"/>
			<polygon class="st219" points="3.8,61.6 171.3,52.6 171.3,52.9 3.8,61.9 			"/>
			<polygon class="st220" points="3.8,61.4 171.3,52.4 171.3,52.6 3.8,61.6 			"/>
			<polygon class="st221" points="3.8,61.1 171.3,52.1 171.3,52.4 3.8,61.4 			"/>
			<polygon class="st222" points="3.8,60.9 171.3,51.8 171.3,52.1 3.8,61.1 			"/>
			<polygon class="st223" points="3.8,60.6 171.3,51.6 171.3,51.8 3.8,60.9 			"/>
			<polygon class="st224" points="3.8,60.3 171.3,51.3 171.3,51.6 3.8,60.6 			"/>
			<polygon class="st225" points="3.8,60.1 171.3,51.1 171.3,51.3 3.8,60.3 			"/>
			<polygon class="st226" points="3.8,59.8 171.3,50.8 171.3,51.1 3.8,60.1 			"/>
			<polygon class="st227" points="3.8,59.6 171.3,50.6 171.3,50.8 3.8,59.8 			"/>
			<polygon class="st228" points="3.8,59.3 171.3,50.3 171.3,50.6 3.8,59.6 			"/>
			<polygon class="st229" points="3.8,59.1 171.3,50 171.3,50.3 3.8,59.3 			"/>
			<polygon class="st230" points="3.8,58.8 171.3,49.8 171.3,50 3.8,59.1 			"/>
			<polygon class="st231" points="3.8,58.5 171.3,49.5 171.3,49.8 3.8,58.8 			"/>
			<polygon class="st232" points="3.8,58.3 171.3,49.3 171.3,49.5 3.8,58.5 			"/>
			<polygon class="st233" points="3.8,58 171.3,49 171.3,49.3 3.8,58.3 			"/>
			<polygon class="st234" points="3.8,57.8 171.3,48.8 171.3,49 3.8,58 			"/>
			<polygon class="st235" points="3.8,57.5 171.3,48.5 171.3,48.8 3.8,57.8 			"/>
			<polygon class="st236" points="3.8,57.3 171.3,48.2 171.3,48.5 3.8,57.5 			"/>
			<polygon class="st237" points="3.8,57 171.3,48 171.3,48.2 3.8,57.3 			"/>
			<polygon class="st238" points="3.8,56.7 171.3,47.7 171.3,48 3.8,57 			"/>
			<polygon class="st239" points="3.8,56.5 171.3,47.5 171.3,47.7 3.8,56.7 			"/>
			<polygon class="st240" points="3.8,56.2 171.3,47.2 171.3,47.5 3.8,56.5 			"/>
			<polygon class="st241" points="3.8,56 171.3,47 171.3,47.2 3.8,56.2 			"/>
			<polygon class="st242" points="3.8,55.7 171.3,46.7 171.3,47 3.8,56 			"/>
			<polygon class="st243" points="3.8,55.5 171.3,46.4 171.3,46.7 3.8,55.7 			"/>
			<polygon class="st244" points="3.8,55.2 171.3,46.2 171.3,46.4 3.8,55.5 			"/>
			<polygon class="st245" points="3.8,54.9 171.3,45.9 171.3,46.2 3.8,55.2 			"/>
			<polygon class="st246" points="3.8,54.7 171.3,45.7 171.3,45.9 3.8,54.9 			"/>
			<polygon class="st247" points="3.8,54.4 171.3,45.4 171.3,45.7 3.8,54.7 			"/>
			<polygon class="st248" points="3.8,54.2 171.3,45.2 171.3,45.4 3.8,54.4 			"/>
			<polygon class="st248" points="3.8,53.4 171.3,44.4 171.3,45.2 3.8,54.2 			"/>
			<polygon class="st249" points="3.8,52.6 171.3,43.6 171.3,44.4 3.8,53.4 			"/>
			<polygon class="st250" points="3.8,51.8 171.3,42.8 171.3,43.6 3.8,52.6 			"/>
			<polygon class="st251" points="3.8,51 171.3,42 171.3,42.8 3.8,51.8 			"/>
			<polygon class="st252" points="3.8,50.2 171.3,41.2 171.3,42 3.8,51 			"/>
			<polygon class="st253" points="3.8,49.4 171.3,40.4 171.3,41.2 3.8,50.2 			"/>
			<polygon class="st254" points="3.8,48.6 171.3,39.6 171.3,40.4 3.8,49.4 			"/>
			<polygon class="st255" points="3.8,47.8 171.3,38.8 171.3,39.6 3.8,48.6 			"/>
			<polygon class="st256" points="3.8,47 171.3,38 171.3,38.8 3.8,47.8 			"/>
			<polygon class="st257" points="3.8,46.2 171.3,37.2 171.3,38 3.8,47 			"/>
			<polygon class="st258" points="3.8,45.5 171.3,36.4 171.3,37.2 3.8,46.2 			"/>
			<polygon class="st259" points="3.8,44.7 171.3,35.6 171.3,36.4 3.8,45.5 			"/>
			<polygon class="st260" points="3.8,43.9 171.3,34.9 171.3,35.6 3.8,44.7 			"/>
			<polygon class="st261" points="3.8,43.1 171.3,34.1 171.3,34.9 3.8,43.9 			"/>
			<polygon class="st262" points="3.8,42.3 171.3,33.3 171.3,34.1 3.8,43.1 			"/>
			<polygon class="st263" points="3.8,41.5 171.3,32.5 171.3,33.3 3.8,42.3 			"/>
			<polygon class="st264" points="3.8,40.7 171.3,31.7 171.3,32.5 3.8,41.5 			"/>
			<polygon class="st265" points="3.8,39.9 171.3,30.9 171.3,31.7 3.8,40.7 			"/>
			<polygon class="st266" points="3.8,39.1 171.3,30.1 171.3,30.9 3.8,39.9 			"/>
			<polygon class="st267" points="3.8,38.3 171.3,29.3 171.3,30.1 3.8,39.1 			"/>
			<polygon class="st268" points="3.8,37.5 171.3,28.5 171.3,29.3 3.8,38.3 			"/>
			<polygon class="st269" points="3.8,36.7 171.3,27.7 171.3,28.5 3.8,37.5 			"/>
			<polygon class="st270" points="3.8,35.9 171.3,26.9 171.3,27.7 3.8,36.7 			"/>
			<polygon class="st271" points="3.8,35.1 171.3,26.1 171.3,26.9 3.8,35.9 			"/>
			<polygon class="st272" points="3.8,34.4 171.3,25.3 171.3,26.1 3.8,35.1 			"/>
			<polygon class="st273" points="3.8,33.6 171.3,24.5 171.3,25.3 3.8,34.4 			"/>
			<polygon class="st274" points="3.8,32.8 171.3,23.8 171.3,24.5 3.8,33.6 			"/>
			<polygon class="st275" points="3.8,32 171.3,23 171.3,23.8 3.8,32.8 			"/>
			<polygon class="st276" points="3.8,31.2 171.3,22.2 171.3,23 3.8,32 			"/>
			<polygon class="st277" points="3.8,30.4 171.3,21.4 171.3,22.2 3.8,31.2 			"/>
			<polygon class="st278" points="3.8,29.6 171.3,20.6 171.3,21.4 3.8,30.4 			"/>
			<polygon class="st279" points="3.8,28.8 171.3,19.8 171.3,20.6 3.8,29.6 			"/>
			<polygon class="st280" points="3.8,28 171.3,19 171.3,19.8 3.8,28.8 			"/>
			<polygon class="st281" points="3.8,27.2 171.3,18.2 171.3,19 3.8,28 			"/>
			<polygon class="st282" points="3.8,26.4 171.3,17.4 171.3,18.2 3.8,27.2 			"/>
			<polygon class="st283" points="3.8,25.6 171.3,16.6 171.3,17.4 3.8,26.4 			"/>
			<polygon class="st284" points="3.8,24.8 171.3,15.8 171.3,16.6 3.8,25.6 			"/>
			<polygon class="st285" points="3.8,24 171.3,15 171.3,15.8 3.8,24.8 			"/>
			<polygon class="st286" points="3.8,23.3 171.3,14.2 171.3,15 3.8,24 			"/>
			<polygon class="st287" points="3.8,22.5 171.3,13.4 171.3,14.2 3.8,23.3 			"/>
			<polygon class="st287" points="3.8,22.4 171.3,13.4 171.3,13.4 3.8,22.5 			"/>
			<polygon class="st286" points="3.8,22.4 171.3,13.4 171.3,13.4 3.8,22.4 			"/>
			<polygon class="st285" points="3.8,22.3 171.3,13.3 171.3,13.4 3.8,22.4 			"/>
			<polygon class="st284" points="3.8,22.3 171.3,13.3 171.3,13.3 3.8,22.3 			"/>
			<polygon class="st283" points="3.8,22.3 171.3,13.2 171.3,13.3 3.8,22.3 			"/>
			<polygon class="st282" points="3.8,22.2 171.3,13.2 171.3,13.2 3.8,22.3 			"/>
			<polygon class="st281" points="3.8,22.2 171.3,13.2 171.3,13.2 3.8,22.2 			"/>
			<polygon class="st280" points="3.8,22.1 171.3,13.1 171.3,13.2 3.8,22.2 			"/>
			<polygon class="st279" points="3.8,22.1 171.3,13.1 171.3,13.1 3.8,22.1 			"/>
			<polygon class="st278" points="3.8,22.1 171.3,13 171.3,13.1 3.8,22.1 			"/>
			<polygon class="st277" points="3.8,22 171.3,13 171.3,13 3.8,22.1 			"/>
			<polygon class="st276" points="3.8,22 171.3,13 171.3,13 3.8,22 			"/>
			<polygon class="st275" points="3.8,21.9 171.3,12.9 171.3,13 3.8,22 			"/>
			<polygon class="st274" points="3.8,21.9 171.3,12.9 171.3,12.9 3.8,21.9 			"/>
			<polygon class="st273" points="3.8,21.9 171.3,12.9 171.3,12.9 3.8,21.9 			"/>
			<polygon class="st272" points="3.8,21.8 171.3,12.8 171.3,12.9 3.8,21.9 			"/>
			<polygon class="st271" points="3.8,21.8 171.3,12.8 171.3,12.8 3.8,21.8 			"/>
			<polygon class="st270" points="3.8,21.7 171.3,12.7 171.3,12.8 3.8,21.8 			"/>
			<polygon class="st269" points="3.8,21.7 171.3,12.7 171.3,12.7 3.8,21.7 			"/>
			<polygon class="st268" points="3.8,21.7 171.3,12.7 171.3,12.7 3.8,21.7 			"/>
			<polygon class="st267" points="3.8,21.6 171.3,12.6 171.3,12.7 3.8,21.7 			"/>
			<polygon class="st266" points="3.8,21.6 171.3,12.6 171.3,12.6 3.8,21.6 			"/>
			<polygon class="st265" points="3.8,21.5 171.3,12.5 171.3,12.6 3.8,21.6 			"/>
			<polygon class="st264" points="3.8,21.5 171.3,12.5 171.3,12.5 3.8,21.5 			"/>
			<polygon class="st263" points="3.8,21.5 171.3,12.5 171.3,12.5 3.8,21.5 			"/>
			<polygon class="st262" points="3.8,21.4 171.3,12.4 171.3,12.5 3.8,21.5 			"/>
			<polygon class="st261" points="3.8,21.4 171.3,12.4 171.3,12.4 3.8,21.4 			"/>
			<polygon class="st260" points="3.8,21.3 171.3,12.3 171.3,12.4 3.8,21.4 			"/>
			<polygon class="st259" points="3.8,21.3 171.3,12.3 171.3,12.3 3.8,21.3 			"/>
			<polygon class="st258" points="3.8,21.3 171.3,12.3 171.3,12.3 3.8,21.3 			"/>
			<polygon class="st257" points="3.8,21.2 171.3,12.2 171.3,12.3 3.8,21.3 			"/>
			<polygon class="st256" points="3.8,21.2 171.3,12.2 171.3,12.2 3.8,21.2 			"/>
			<polygon class="st255" points="3.8,21.2 171.3,12.1 171.3,12.2 3.8,21.2 			"/>
			<polygon class="st254" points="3.8,21.1 171.3,12.1 171.3,12.1 3.8,21.2 			"/>
			<polygon class="st253" points="3.8,21.1 171.3,12.1 171.3,12.1 3.8,21.1 			"/>
			<polygon class="st252" points="3.8,21 171.3,12 171.3,12.1 3.8,21.1 			"/>
			<polygon class="st251" points="3.8,21 171.3,12 171.3,12 3.8,21 			"/>
			<polygon class="st250" points="3.8,21 171.3,11.9 171.3,12 3.8,21 			"/>
			<polygon class="st249" points="3.8,20.9 171.3,11.9 171.3,11.9 3.8,21 			"/>
			<polygon class="st248" points="3.8,20.9 171.3,11.9 171.3,11.9 3.8,20.9 			"/>
			<polygon class="st248" points="171.3,11.9 3.8,20.9 3.8,9.8 171.3,9.8 			"/>
		</g>
	</g>
	<path class="st288" d="M174.7,181.5H0.3V76.6C0.4,37.7,31.9,6.3,70.8,6.3h104V181.5z M7.2,174.6h160.6V13.2H70.8
		c-35,0-63.5,28.3-63.6,63.4V174.6z"/>
	<path class="st289" d="M169.6,176.4H5.4V77.6c0.1-36.6,29.8-66.2,66.4-66.2h97.8V176.4z M12.3,169.5h150.4V18.4H71.8
		c-32.8,0-59.4,26.5-59.5,59.3L12.3,169.5z"/>
	<polygon class="st290" points="42.2,52.7 18.1,172.9 193.8,193.7 199.7,58 	"/>
	<g>
		<defs>
			<polygon id="SVGID_3_" points="56.5,69.8 42.4,157.4 169.9,173.5 176.2,74.9 			"/>
		</defs>
		<clipPath id="SVGID_4_">
			<use xlink:href="#SVGID_3_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st291">
			<polygon class="st292" points="176.2,74.7 175.7,69.8 176.2,69.8 			"/>
			<polygon class="st293" points="176.2,86.1 174.6,69.8 175.7,69.8 176.2,74.7 			"/>
			<polygon class="st294" points="176.2,97.6 173.4,69.8 174.6,69.8 176.2,86.1 			"/>
			<polygon class="st295" points="176.2,109.1 172.3,69.8 173.4,69.8 176.2,97.6 			"/>
			<polygon class="st296" points="176.2,120.6 171.1,69.8 172.3,69.8 176.2,109.1 			"/>
			<polygon class="st297" points="176.2,132.1 170,69.8 171.1,69.8 176.2,120.6 			"/>
			<polygon class="st298" points="176.2,143.6 168.8,69.8 170,69.8 176.2,132.1 			"/>
			<polygon class="st299" points="176.2,155 167.7,69.8 168.8,69.8 176.2,143.6 			"/>
			<polygon class="st300" points="176.2,166.5 166.5,69.8 167.7,69.8 176.2,155 			"/>
			<polygon class="st301" points="175.7,173.5 165.4,69.8 166.5,69.8 176.2,166.5 176.2,173.5 			"/>
			<polygon class="st302" points="174.6,173.5 164.2,69.8 165.4,69.8 175.7,173.5 			"/>
			<polygon class="st303" points="173.5,173.5 163.1,69.8 164.2,69.8 174.6,173.5 			"/>
			<polygon class="st304" points="172.3,173.5 161.9,69.8 163.1,69.8 173.5,173.5 			"/>
			<polygon class="st305" points="171.2,173.5 160.8,69.8 161.9,69.8 172.3,173.5 			"/>
			<polygon class="st306" points="170,173.5 159.6,69.8 160.8,69.8 171.2,173.5 			"/>
			<polygon class="st307" points="168.9,173.5 158.5,69.8 159.6,69.8 170,173.5 			"/>
			<polygon class="st308" points="167.7,173.5 157.3,69.8 158.5,69.8 168.9,173.5 			"/>
			<polygon class="st309" points="166.6,173.5 156.2,69.8 157.3,69.8 167.7,173.5 			"/>
			<polygon class="st310" points="165.4,173.5 155.1,69.8 156.2,69.8 166.6,173.5 			"/>
			<polygon class="st311" points="164.3,173.5 153.9,69.8 155.1,69.8 165.4,173.5 			"/>
			<polygon class="st312" points="163.1,173.5 152.8,69.8 153.9,69.8 164.3,173.5 			"/>
			<polygon class="st313" points="162,173.5 151.6,69.8 152.8,69.8 163.1,173.5 			"/>
			<polygon class="st314" points="160.8,173.5 150.5,69.8 151.6,69.8 162,173.5 			"/>
			<polygon class="st315" points="159.7,173.5 149.3,69.8 150.5,69.8 160.8,173.5 			"/>
			<polygon class="st316" points="158.5,173.5 148.2,69.8 149.3,69.8 159.7,173.5 			"/>
			<polygon class="st317" points="157.4,173.5 147,69.8 148.2,69.8 158.5,173.5 			"/>
			<polygon class="st318" points="156.2,173.5 145.9,69.8 147,69.8 157.4,173.5 			"/>
			<polygon class="st319" points="155.1,173.5 144.7,69.8 145.9,69.8 156.2,173.5 			"/>
			<polygon class="st320" points="153.9,173.5 143.6,69.8 144.7,69.8 155.1,173.5 			"/>
			<polygon class="st321" points="152.8,173.5 142.4,69.8 143.6,69.8 153.9,173.5 			"/>
			<polygon class="st322" points="151.6,173.5 141.3,69.8 142.4,69.8 152.8,173.5 			"/>
			<polygon class="st323" points="150.5,173.5 140.1,69.8 141.3,69.8 151.6,173.5 			"/>
			<polygon class="st324" points="149.3,173.5 139,69.8 140.1,69.8 150.5,173.5 			"/>
			<polygon class="st325" points="148.2,173.5 137.8,69.8 139,69.8 149.3,173.5 			"/>
			<polygon class="st326" points="147,173.5 136.7,69.8 137.8,69.8 148.2,173.5 			"/>
			<polygon class="st327" points="145.9,173.5 135.5,69.8 136.7,69.8 147,173.5 			"/>
			<polygon class="st328" points="144.7,173.5 134.4,69.8 135.5,69.8 145.9,173.5 			"/>
			<polygon class="st329" points="143.6,173.5 133.2,69.8 134.4,69.8 144.7,173.5 			"/>
			<polygon class="st330" points="142.4,173.5 132.1,69.8 133.2,69.8 143.6,173.5 			"/>
			<polygon class="st331" points="141.3,173.5 130.9,69.8 132.1,69.8 142.4,173.5 			"/>
			<polygon class="st332" points="140.2,173.5 129.8,69.8 130.9,69.8 141.3,173.5 			"/>
			<polygon class="st333" points="139,173.5 128.6,69.8 129.8,69.8 140.2,173.5 			"/>
			<polygon class="st334" points="137.9,173.5 127.5,69.8 128.6,69.8 139,173.5 			"/>
			<polygon class="st335" points="136.7,173.5 126.3,69.8 127.5,69.8 137.9,173.5 			"/>
			<polygon class="st336" points="135.6,173.5 125.2,69.8 126.3,69.8 136.7,173.5 			"/>
			<polygon class="st337" points="134.4,173.5 124,69.8 125.2,69.8 135.6,173.5 			"/>
			<polygon class="st338" points="133.3,173.5 122.9,69.8 124,69.8 134.4,173.5 			"/>
			<polygon class="st339" points="132.1,173.5 121.8,69.8 122.9,69.8 133.3,173.5 			"/>
			<polygon class="st340" points="131,173.5 120.6,69.8 121.8,69.8 132.1,173.5 			"/>
			<polygon class="st341" points="129.8,173.5 119.5,69.8 120.6,69.8 131,173.5 			"/>
			<polygon class="st342" points="128.7,173.5 118.3,69.8 119.5,69.8 129.8,173.5 			"/>
			<polygon class="st343" points="127.5,173.5 117.2,69.8 118.3,69.8 128.7,173.5 			"/>
			<polygon class="st344" points="126.4,173.5 116,69.8 117.2,69.8 127.5,173.5 			"/>
			<polygon class="st345" points="125.2,173.5 114.9,69.8 116,69.8 126.4,173.5 			"/>
			<polygon class="st346" points="124.1,173.5 113.7,69.8 114.9,69.8 125.2,173.5 			"/>
			<polygon class="st347" points="122.9,173.5 112.6,69.8 113.7,69.8 124.1,173.5 			"/>
			<polygon class="st348" points="121.8,173.5 111.4,69.8 112.6,69.8 122.9,173.5 			"/>
			<polygon class="st349" points="120.6,173.5 110.3,69.8 111.4,69.8 121.8,173.5 			"/>
			<polygon class="st350" points="119.5,173.5 109.1,69.8 110.3,69.8 120.6,173.5 			"/>
			<polygon class="st351" points="118.3,173.5 108,69.8 109.1,69.8 119.5,173.5 			"/>
			<polygon class="st352" points="117.2,173.5 106.8,69.8 108,69.8 118.3,173.5 			"/>
			<polygon class="st353" points="116,173.5 105.7,69.8 106.8,69.8 117.2,173.5 			"/>
			<polygon class="st354" points="114.9,173.5 104.5,69.8 105.7,69.8 116,173.5 			"/>
			<polygon class="st355" points="113.7,173.5 103.4,69.8 104.5,69.8 114.9,173.5 			"/>
			<polygon class="st356" points="112.6,173.5 102.2,69.8 103.4,69.8 113.7,173.5 			"/>
			<polygon class="st357" points="111.4,173.5 101.1,69.8 102.2,69.8 112.6,173.5 			"/>
			<polygon class="st358" points="110.3,173.5 99.9,69.8 101.1,69.8 111.4,173.5 			"/>
			<polygon class="st359" points="109.1,173.5 98.8,69.8 99.9,69.8 110.3,173.5 			"/>
			<polygon class="st360" points="108,173.5 97.6,69.8 98.8,69.8 109.1,173.5 			"/>
			<polygon class="st361" points="106.9,173.5 96.5,69.8 97.6,69.8 108,173.5 			"/>
			<polygon class="st362" points="105.7,173.5 95.3,69.8 96.5,69.8 106.9,173.5 			"/>
			<polygon class="st363" points="104.6,173.5 94.2,69.8 95.3,69.8 105.7,173.5 			"/>
			<polygon class="st364" points="103.4,173.5 93,69.8 94.2,69.8 104.6,173.5 			"/>
			<polygon class="st365" points="102.3,173.5 91.9,69.8 93,69.8 103.4,173.5 			"/>
			<polygon class="st366" points="101.1,173.5 90.7,69.8 91.9,69.8 102.3,173.5 			"/>
			<polygon class="st367" points="100,173.5 89.6,69.8 90.7,69.8 101.1,173.5 			"/>
			<polygon class="st368" points="98.8,173.5 88.5,69.8 89.6,69.8 100,173.5 			"/>
			<polygon class="st369" points="97.7,173.5 87.3,69.8 88.5,69.8 98.8,173.5 			"/>
			<polygon class="st370" points="96.5,173.5 86.2,69.8 87.3,69.8 97.7,173.5 			"/>
			<polygon class="st371" points="95.4,173.5 85,69.8 86.2,69.8 96.5,173.5 			"/>
			<polygon class="st372" points="94.2,173.5 83.9,69.8 85,69.8 95.4,173.5 			"/>
			<polygon class="st373" points="93.1,173.5 82.7,69.8 83.9,69.8 94.2,173.5 			"/>
			<polygon class="st374" points="91.9,173.5 81.6,69.8 82.7,69.8 93.1,173.5 			"/>
			<polygon class="st375" points="90.8,173.5 80.4,69.8 81.6,69.8 91.9,173.5 			"/>
			<polygon class="st376" points="89.6,173.5 79.3,69.8 80.4,69.8 90.8,173.5 			"/>
			<polygon class="st377" points="88.5,173.5 78.1,69.8 79.3,69.8 89.6,173.5 			"/>
			<polygon class="st378" points="87.3,173.5 77,69.8 78.1,69.8 88.5,173.5 			"/>
			<polygon class="st379" points="86.2,173.5 75.8,69.8 77,69.8 87.3,173.5 			"/>
			<polygon class="st380" points="85,173.5 74.7,69.8 75.8,69.8 86.2,173.5 			"/>
			<polygon class="st381" points="83.9,173.5 73.5,69.8 74.7,69.8 85,173.5 			"/>
			<polygon class="st382" points="82.7,173.5 72.4,69.8 73.5,69.8 83.9,173.5 			"/>
			<polygon class="st383" points="81.6,173.5 71.2,69.8 72.4,69.8 82.7,173.5 			"/>
			<polygon class="st384" points="80.4,173.5 70.1,69.8 71.2,69.8 81.6,173.5 			"/>
			<polygon class="st385" points="79.3,173.5 68.9,69.8 70.1,69.8 80.4,173.5 			"/>
			<polygon class="st386" points="78.1,173.5 67.8,69.8 68.9,69.8 79.3,173.5 			"/>
			<polygon class="st387" points="77,173.5 66.6,69.8 67.8,69.8 78.1,173.5 			"/>
			<polygon class="st388" points="75.8,173.5 65.5,69.8 66.6,69.8 77,173.5 			"/>
			<polygon class="st389" points="74.7,173.5 64.3,69.8 65.5,69.8 75.8,173.5 			"/>
			<polygon class="st390" points="73.6,173.5 63.2,69.8 64.3,69.8 74.7,173.5 			"/>
			<polygon class="st391" points="72.4,173.5 62,69.8 63.2,69.8 73.6,173.5 			"/>
			<polygon class="st392" points="71.3,173.5 60.9,69.8 62,69.8 72.4,173.5 			"/>
			<polygon class="st393" points="70.1,173.5 59.7,69.8 60.9,69.8 71.3,173.5 			"/>
			<polygon class="st394" points="69,173.5 58.6,69.8 59.7,69.8 70.1,173.5 			"/>
			<polygon class="st395" points="67.8,173.5 57.4,69.8 58.6,69.8 69,173.5 			"/>
			<polygon class="st396" points="66.7,173.5 56.3,69.8 57.4,69.8 67.8,173.5 			"/>
			<polygon class="st397" points="65.5,173.5 55.1,69.8 56.3,69.8 66.7,173.5 			"/>
			<polygon class="st398" points="64.4,173.5 54,69.8 55.1,69.8 65.5,173.5 			"/>
			<polygon class="st399" points="63.2,173.5 52.9,69.8 54,69.8 64.4,173.5 			"/>
			<polygon class="st400" points="62.1,173.5 51.7,69.8 52.9,69.8 63.2,173.5 			"/>
			<polygon class="st401" points="60.9,173.5 50.6,69.8 51.7,69.8 62.1,173.5 			"/>
			<polygon class="st402" points="59.8,173.5 49.4,69.8 50.6,69.8 60.9,173.5 			"/>
			<polygon class="st403" points="58.6,173.5 48.3,69.8 49.4,69.8 59.8,173.5 			"/>
			<polygon class="st404" points="57.5,173.5 47.1,69.8 48.3,69.8 58.6,173.5 			"/>
			<polygon class="st405" points="56.3,173.5 46,69.8 47.1,69.8 57.5,173.5 			"/>
			<polygon class="st406" points="55.2,173.5 44.8,69.8 46,69.8 56.3,173.5 			"/>
			<polygon class="st407" points="54,173.5 43.7,69.8 44.8,69.8 55.2,173.5 			"/>
			<polygon class="st408" points="52.9,173.5 42.5,69.8 43.7,69.8 54,173.5 			"/>
			<polygon class="st409" points="51.7,173.5 42.4,80.6 42.4,69.8 42.5,69.8 52.9,173.5 			"/>
			<polygon class="st410" points="50.6,173.5 42.4,92.1 42.4,80.6 51.7,173.5 			"/>
			<polygon class="st411" points="49.4,173.5 42.4,103.6 42.4,92.1 50.6,173.5 			"/>
			<polygon class="st412" points="48.3,173.5 42.4,115.1 42.4,103.6 49.4,173.5 			"/>
			<polygon class="st413" points="47.1,173.5 42.4,126.5 42.4,115.1 48.3,173.5 			"/>
			<polygon class="st414" points="46,173.5 42.4,138 42.4,126.5 47.1,173.5 			"/>
			<polygon class="st415" points="44.8,173.5 42.4,149.5 42.4,138 46,173.5 			"/>
			<polygon class="st416" points="43.7,173.5 42.4,161 42.4,149.5 44.8,173.5 			"/>
			<polygon class="st417" points="42.5,173.5 42.4,172.5 42.4,161 43.7,173.5 			"/>
			<polygon class="st417" points="42.4,172.5 42.5,173.5 42.4,173.5 			"/>
		</g>
	</g>
	<path class="st290" d="M136.3,91.7c17.1,0,30.9,13.1,30.9,29.2c0,16.1-13.8,29.2-30.9,29.2c-17.1,0-30.9-13.1-30.9-29.2
		C105.4,104.8,119.3,91.7,136.3,91.7z M149.6,94.9l-15.4,22l31.3,11.6l0,0l0.1,0c0.2-0.8,0.3-1.5,0.4-2.2c1.5-7.6,0.3-13.7-2.6-18.7
		C160.3,102,155.5,97.5,149.6,94.9L149.6,94.9L149.6,94.9z"/>
	<path class="st289" d="M136.3,152.4c-18.3,0-33.2-14.1-33.2-31.5s14.9-31.5,33.2-31.5s33.2,14.1,33.2,31.5S154.6,152.4,136.3,152.4
		z M136.3,94c-15.8,0-28.6,12.1-28.6,26.9c0,14.8,12.8,26.9,28.6,26.9c12.4,0,22.9-7.4,26.9-17.8l-32.6-12l15.6-22.3
		C143.1,94.6,139.7,94,136.3,94z M137.8,115.8l26,9.6c1.2-6.5,0.4-11.9-2.4-16.7c-2.5-4.6-6.4-8.4-11.1-10.9L137.8,115.8z"/>
	<polygon class="st290" points="58.6,125.7 91.9,128.2 90.3,138.4 57,135.8 	"/>
	<polygon class="st290" points="62,107.7 95.3,110.2 93.7,120.3 60.3,117.8 	"/>
	<polygon class="st290" points="64.9,91.8 98.2,94.3 96.6,104.4 63.3,101.9 	"/>
</g>
</svg>
