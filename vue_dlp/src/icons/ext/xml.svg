<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.2.3, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 200 200" style="enable-background:new 0 0 200 200;" xml:space="preserve">
<style type="text/css">
	.st0{clip-path:url(#SVGID_2_);fill:#FFFFFF;}
	.st1{clip-path:url(#SVGID_2_);fill:#FEFEFE;}
	.st2{clip-path:url(#SVGID_2_);fill:#FDFDFD;}
	.st3{clip-path:url(#SVGID_2_);fill:#FCFCFC;}
	.st4{clip-path:url(#SVGID_2_);fill:#FBFBFB;}
	.st5{clip-path:url(#SVGID_2_);fill:#FAFAFA;}
	.st6{clip-path:url(#SVGID_2_);fill:#F9F9F9;}
	.st7{clip-path:url(#SVGID_2_);fill:#F8F8F8;}
	.st8{clip-path:url(#SVGID_2_);fill:#F7F7F7;}
	.st9{clip-path:url(#SVGID_2_);fill:#F6F6F6;}
	.st10{clip-path:url(#SVGID_2_);fill:#F5F5F5;}
	.st11{clip-path:url(#SVGID_2_);fill:#F4F4F4;}
	.st12{clip-path:url(#SVGID_2_);fill:#F3F3F3;}
	.st13{clip-path:url(#SVGID_2_);fill:#F2F2F2;}
	.st14{clip-path:url(#SVGID_2_);fill:#F1F1F1;}
	.st15{clip-path:url(#SVGID_2_);fill:#F0F0F0;}
	.st16{clip-path:url(#SVGID_2_);fill:#EFEFEF;}
	.st17{clip-path:url(#SVGID_2_);fill:#EEEEEE;}
	.st18{clip-path:url(#SVGID_2_);fill:#EDEDED;}
	.st19{clip-path:url(#SVGID_2_);fill:#ECECEC;}
	.st20{clip-path:url(#SVGID_2_);fill:#EBEBEB;}
	.st21{clip-path:url(#SVGID_2_);fill:#EAEAEA;}
	.st22{clip-path:url(#SVGID_2_);fill:#E9E9E9;}
	.st23{clip-path:url(#SVGID_2_);fill:#E8E8E8;}
	.st24{clip-path:url(#SVGID_2_);fill:#E7E7E7;}
	.st25{clip-path:url(#SVGID_2_);fill:#E6E6E6;}
	.st26{clip-path:url(#SVGID_2_);fill:#E4E4E4;}
	.st27{clip-path:url(#SVGID_2_);fill:#E3E3E3;}
	.st28{clip-path:url(#SVGID_2_);fill:#E2E2E2;}
	.st29{clip-path:url(#SVGID_2_);fill:#E1E1E1;}
	.st30{clip-path:url(#SVGID_2_);fill:#E0E0E0;}
	.st31{clip-path:url(#SVGID_2_);fill:#DFDFDF;}
	.st32{clip-path:url(#SVGID_2_);fill:#DEDEDE;}
	.st33{clip-path:url(#SVGID_2_);fill:#DDDDDD;}
	.st34{clip-path:url(#SVGID_2_);fill:#DCDCDC;}
	.st35{clip-path:url(#SVGID_2_);fill:#DBDBDB;}
	.st36{clip-path:url(#SVGID_2_);fill:#DADADA;}
	.st37{clip-path:url(#SVGID_2_);fill:#D9D9D9;}
	.st38{clip-path:url(#SVGID_2_);fill:#D8D8D8;}
	.st39{clip-path:url(#SVGID_2_);fill:#D7D7D7;}
	.st40{clip-path:url(#SVGID_2_);fill:#D6D6D6;}
	.st41{clip-path:url(#SVGID_2_);fill:#D5D5D5;}
	.st42{clip-path:url(#SVGID_2_);fill:#D4D4D4;}
	.st43{clip-path:url(#SVGID_2_);fill:#D3D3D3;}
	.st44{clip-path:url(#SVGID_2_);fill:#D2D2D2;}
	.st45{clip-path:url(#SVGID_2_);fill:#D1D1D1;}
	.st46{clip-path:url(#SVGID_2_);fill:#D0D0D0;}
	.st47{clip-path:url(#SVGID_2_);fill:#CFCFCF;}
	.st48{clip-path:url(#SVGID_2_);fill:#CECECE;}
	.st49{clip-path:url(#SVGID_2_);fill:#CDCDCD;}
	.st50{clip-path:url(#SVGID_2_);fill:#CCCCCC;}
	.st51{fill:#A0A09F;}
	.st52{fill:#898989;}
	.st53{clip-path:url(#SVGID_4_);fill:#FFFFFF;}
	.st54{clip-path:url(#SVGID_6_);fill:#E8EDF1;}
	.st55{clip-path:url(#SVGID_6_);fill:#E9EEF2;}
	.st56{clip-path:url(#SVGID_6_);fill:#EAEFF3;}
	.st57{clip-path:url(#SVGID_6_);fill:#EBF0F3;}
	.st58{clip-path:url(#SVGID_6_);fill:#ECF1F4;}
	.st59{clip-path:url(#SVGID_6_);fill:#EDF1F5;}
	.st60{clip-path:url(#SVGID_6_);fill:#EEF2F5;}
	.st61{clip-path:url(#SVGID_6_);fill:#EFF3F6;}
	.st62{clip-path:url(#SVGID_6_);fill:#F0F4F7;}
	.st63{clip-path:url(#SVGID_6_);fill:#F2F5F7;}
	.st64{clip-path:url(#SVGID_6_);fill:#F3F6F8;}
	.st65{clip-path:url(#SVGID_6_);fill:#F4F7F9;}
	.st66{clip-path:url(#SVGID_6_);fill:#F5F8F9;}
	.st67{clip-path:url(#SVGID_6_);fill:#F6F8FA;}
	.st68{clip-path:url(#SVGID_6_);fill:#F7F9FB;}
	.st69{clip-path:url(#SVGID_6_);fill:#F8FAFB;}
	.st70{clip-path:url(#SVGID_6_);fill:#F9FBFC;}
	.st71{clip-path:url(#SVGID_6_);fill:#FAFCFD;}
	.st72{clip-path:url(#SVGID_6_);fill:#FBFDFD;}
	.st73{clip-path:url(#SVGID_6_);fill:#FDFDFE;}
	.st74{clip-path:url(#SVGID_6_);fill:#FEFEFE;}
	.st75{clip-path:url(#SVGID_6_);fill:#FFFFFF;}
	.st76{fill:#B5B5B6;}
	.st77{opacity:0.73;enable-background:new    ;}
	.st78{clip-path:url(#SVGID_8_);fill:#E8EDF1;}
	.st79{clip-path:url(#SVGID_8_);fill:#E9EEF2;}
	.st80{clip-path:url(#SVGID_8_);fill:#EAEFF3;}
	.st81{clip-path:url(#SVGID_8_);fill:#EBF0F3;}
	.st82{clip-path:url(#SVGID_8_);fill:#ECF1F4;}
	.st83{clip-path:url(#SVGID_8_);fill:#EDF1F5;}
	.st84{clip-path:url(#SVGID_8_);fill:#EEF2F5;}
	.st85{clip-path:url(#SVGID_8_);fill:#EFF3F6;}
	.st86{clip-path:url(#SVGID_8_);fill:#F0F4F7;}
	.st87{clip-path:url(#SVGID_8_);fill:#F2F5F7;}
	.st88{clip-path:url(#SVGID_8_);fill:#F3F6F8;}
	.st89{clip-path:url(#SVGID_8_);fill:#F4F7F9;}
	.st90{clip-path:url(#SVGID_8_);fill:#F5F8F9;}
	.st91{clip-path:url(#SVGID_8_);fill:#F6F8FA;}
	.st92{clip-path:url(#SVGID_8_);fill:#F7F9FB;}
	.st93{clip-path:url(#SVGID_8_);fill:#F8FAFB;}
	.st94{clip-path:url(#SVGID_8_);fill:#F9FBFC;}
	.st95{clip-path:url(#SVGID_8_);fill:#FAFCFD;}
	.st96{clip-path:url(#SVGID_8_);fill:#FBFDFD;}
	.st97{clip-path:url(#SVGID_8_);fill:#FDFDFE;}
	.st98{clip-path:url(#SVGID_8_);fill:#FEFEFE;}
	.st99{clip-path:url(#SVGID_8_);fill:#FFFFFF;}
	.st100{clip-path:url(#SVGID_10_);fill:#E8EDF1;}
	.st101{clip-path:url(#SVGID_10_);fill:#E9EEF2;}
	.st102{clip-path:url(#SVGID_10_);fill:#EAEFF3;}
	.st103{clip-path:url(#SVGID_10_);fill:#EBF0F3;}
	.st104{clip-path:url(#SVGID_10_);fill:#ECF1F4;}
	.st105{clip-path:url(#SVGID_10_);fill:#EDF1F5;}
	.st106{clip-path:url(#SVGID_10_);fill:#EEF2F5;}
	.st107{clip-path:url(#SVGID_10_);fill:#EFF3F6;}
	.st108{clip-path:url(#SVGID_10_);fill:#F0F4F7;}
	.st109{clip-path:url(#SVGID_10_);fill:#F2F5F7;}
	.st110{clip-path:url(#SVGID_10_);fill:#F3F6F8;}
	.st111{clip-path:url(#SVGID_10_);fill:#F4F7F9;}
	.st112{clip-path:url(#SVGID_10_);fill:#F5F8F9;}
	.st113{clip-path:url(#SVGID_10_);fill:#F6F8FA;}
	.st114{clip-path:url(#SVGID_10_);fill:#F7F9FB;}
	.st115{clip-path:url(#SVGID_10_);fill:#F8FAFB;}
	.st116{clip-path:url(#SVGID_10_);fill:#F9FBFC;}
	.st117{clip-path:url(#SVGID_10_);fill:#FAFCFD;}
	.st118{clip-path:url(#SVGID_10_);fill:#FBFDFD;}
	.st119{clip-path:url(#SVGID_10_);fill:#FDFDFE;}
	.st120{clip-path:url(#SVGID_10_);fill:#FEFEFE;}
	.st121{clip-path:url(#SVGID_10_);fill:#FFFFFF;}
	.st122{clip-path:url(#SVGID_12_);fill:#F05A43;}
	.st123{clip-path:url(#SVGID_12_);fill:#EF5943;}
	.st124{clip-path:url(#SVGID_12_);fill:#EE5842;}
	.st125{clip-path:url(#SVGID_12_);fill:#ED5842;}
	.st126{clip-path:url(#SVGID_12_);fill:#EC5741;}
	.st127{clip-path:url(#SVGID_12_);fill:#EB5641;}
	.st128{clip-path:url(#SVGID_12_);fill:#EA5640;}
	.st129{clip-path:url(#SVGID_12_);fill:#E95540;}
	.st130{clip-path:url(#SVGID_12_);fill:#E8543F;}
	.st131{clip-path:url(#SVGID_12_);fill:#E7543F;}
	.st132{clip-path:url(#SVGID_12_);fill:#E5533E;}
	.st133{clip-path:url(#SVGID_12_);fill:#E4533E;}
	.st134{clip-path:url(#SVGID_12_);fill:#E3523D;}
	.st135{clip-path:url(#SVGID_12_);fill:#E2513D;}
	.st136{clip-path:url(#SVGID_12_);fill:#E1513C;}
	.st137{clip-path:url(#SVGID_12_);fill:#E0503C;}
	.st138{clip-path:url(#SVGID_12_);fill:#DF4F3B;}
	.st139{clip-path:url(#SVGID_12_);fill:#DE4F3B;}
	.st140{clip-path:url(#SVGID_12_);fill:#DD4E3A;}
	.st141{clip-path:url(#SVGID_12_);fill:#DC4D3A;}
	.st142{clip-path:url(#SVGID_12_);fill:#DB4D39;}
	.st143{clip-path:url(#SVGID_12_);fill:#DA4C39;}
	.st144{clip-path:url(#SVGID_12_);fill:#D94B38;}
	.st145{clip-path:url(#SVGID_12_);fill:#D84B38;}
	.st146{clip-path:url(#SVGID_12_);fill:#D74A37;}
	.st147{clip-path:url(#SVGID_12_);fill:#D64937;}
	.st148{clip-path:url(#SVGID_12_);fill:#D54936;}
	.st149{clip-path:url(#SVGID_12_);fill:#D44836;}
	.st150{clip-path:url(#SVGID_12_);fill:#D34735;}
	.st151{clip-path:url(#SVGID_12_);fill:#D24735;}
	.st152{clip-path:url(#SVGID_12_);fill:#D14634;}
	.st153{clip-path:url(#SVGID_12_);fill:#D04534;}
	.st154{clip-path:url(#SVGID_12_);fill:#CF4533;}
	.st155{clip-path:url(#SVGID_12_);fill:#CE4433;}
	.st156{clip-path:url(#SVGID_12_);fill:#CD4332;}
	.st157{opacity:0.2;enable-background:new    ;}
	.st158{clip-path:url(#SVGID_14_);fill:#F05A43;}
	.st159{clip-path:url(#SVGID_14_);fill:#EF5943;}
	.st160{clip-path:url(#SVGID_14_);fill:#EE5842;}
	.st161{clip-path:url(#SVGID_14_);fill:#ED5842;}
	.st162{clip-path:url(#SVGID_14_);fill:#EC5741;}
	.st163{clip-path:url(#SVGID_14_);fill:#EB5641;}
	.st164{clip-path:url(#SVGID_14_);fill:#EA5640;}
	.st165{clip-path:url(#SVGID_14_);fill:#E95540;}
	.st166{clip-path:url(#SVGID_14_);fill:#E8543F;}
	.st167{clip-path:url(#SVGID_14_);fill:#E7543F;}
	.st168{clip-path:url(#SVGID_14_);fill:#E5533E;}
	.st169{clip-path:url(#SVGID_14_);fill:#E4533E;}
	.st170{clip-path:url(#SVGID_14_);fill:#E3523D;}
	.st171{clip-path:url(#SVGID_14_);fill:#E2513D;}
	.st172{clip-path:url(#SVGID_14_);fill:#E1513C;}
	.st173{clip-path:url(#SVGID_14_);fill:#E0503C;}
	.st174{clip-path:url(#SVGID_14_);fill:#DF4F3B;}
	.st175{clip-path:url(#SVGID_14_);fill:#DE4F3B;}
	.st176{clip-path:url(#SVGID_14_);fill:#DD4E3A;}
	.st177{clip-path:url(#SVGID_14_);fill:#DC4D3A;}
	.st178{clip-path:url(#SVGID_14_);fill:#DB4D39;}
	.st179{clip-path:url(#SVGID_14_);fill:#DA4C39;}
	.st180{clip-path:url(#SVGID_14_);fill:#D94B38;}
	.st181{clip-path:url(#SVGID_14_);fill:#D84B38;}
	.st182{clip-path:url(#SVGID_14_);fill:#D74A37;}
	.st183{clip-path:url(#SVGID_14_);fill:#D64937;}
	.st184{clip-path:url(#SVGID_14_);fill:#D54936;}
	.st185{clip-path:url(#SVGID_14_);fill:#D44836;}
	.st186{clip-path:url(#SVGID_14_);fill:#D34735;}
	.st187{clip-path:url(#SVGID_14_);fill:#D24735;}
	.st188{clip-path:url(#SVGID_14_);fill:#D14634;}
	.st189{clip-path:url(#SVGID_14_);fill:#D04534;}
	.st190{clip-path:url(#SVGID_14_);fill:#CF4533;}
	.st191{clip-path:url(#SVGID_14_);fill:#CE4433;}
	.st192{clip-path:url(#SVGID_14_);fill:#CD4332;}
	.st193{opacity:0.3;enable-background:new    ;}
	.st194{clip-path:url(#SVGID_16_);fill:#F05A43;}
	.st195{clip-path:url(#SVGID_16_);fill:#EF5943;}
	.st196{clip-path:url(#SVGID_16_);fill:#EE5842;}
	.st197{clip-path:url(#SVGID_16_);fill:#ED5842;}
	.st198{clip-path:url(#SVGID_16_);fill:#EC5741;}
	.st199{clip-path:url(#SVGID_16_);fill:#EB5641;}
	.st200{clip-path:url(#SVGID_16_);fill:#EA5640;}
	.st201{clip-path:url(#SVGID_16_);fill:#E95540;}
	.st202{clip-path:url(#SVGID_16_);fill:#E8543F;}
	.st203{clip-path:url(#SVGID_16_);fill:#E7543F;}
	.st204{clip-path:url(#SVGID_16_);fill:#E5533E;}
	.st205{clip-path:url(#SVGID_16_);fill:#E4533E;}
	.st206{clip-path:url(#SVGID_16_);fill:#E3523D;}
	.st207{clip-path:url(#SVGID_16_);fill:#E2513D;}
	.st208{clip-path:url(#SVGID_16_);fill:#E1513C;}
	.st209{clip-path:url(#SVGID_16_);fill:#E0503C;}
	.st210{clip-path:url(#SVGID_16_);fill:#DF4F3B;}
	.st211{clip-path:url(#SVGID_16_);fill:#DE4F3B;}
	.st212{clip-path:url(#SVGID_16_);fill:#DD4E3A;}
	.st213{clip-path:url(#SVGID_16_);fill:#DC4D3A;}
	.st214{clip-path:url(#SVGID_16_);fill:#DB4D39;}
	.st215{clip-path:url(#SVGID_16_);fill:#DA4C39;}
	.st216{clip-path:url(#SVGID_16_);fill:#D94B38;}
	.st217{clip-path:url(#SVGID_16_);fill:#D84B38;}
	.st218{clip-path:url(#SVGID_16_);fill:#D74A37;}
	.st219{clip-path:url(#SVGID_16_);fill:#D64937;}
	.st220{clip-path:url(#SVGID_16_);fill:#D54936;}
	.st221{clip-path:url(#SVGID_16_);fill:#D44836;}
	.st222{clip-path:url(#SVGID_16_);fill:#D34735;}
	.st223{clip-path:url(#SVGID_16_);fill:#D24735;}
	.st224{clip-path:url(#SVGID_16_);fill:#D14634;}
	.st225{clip-path:url(#SVGID_16_);fill:#D04534;}
	.st226{clip-path:url(#SVGID_16_);fill:#CF4533;}
	.st227{clip-path:url(#SVGID_16_);fill:#CE4433;}
	.st228{clip-path:url(#SVGID_16_);fill:#CD4332;}
	.st229{fill:#FFFFFF;}
	.st230{opacity:0.5;}
	.st231{fill:#808080;}
	.st232{fill:#D53E2C;}
</style>
<g>
	<g>
		<g>
			<defs>
				<path id="SVGID_1_" d="M34.1,2.1c-5.9,0-10.7,4.8-10.7,10.7v174.5c0,5.9,4.8,10.7,10.7,10.7h131.8c5.9,0,10.7-4.8,10.7-10.7
					v-142L133.5,2.1H34.1z"/>
			</defs>
			<clipPath id="SVGID_2_">
				<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
			</clipPath>
			<path class="st0" d="M23.4,198h153.2H23.4z"/>
			<rect x="23.4" y="194.2" class="st0" width="153.2" height="3.8"/>
			<rect x="23.4" y="190.3" class="st1" width="153.2" height="3.8"/>
			<rect x="23.4" y="186.5" class="st2" width="153.2" height="3.8"/>
			<rect x="23.4" y="182.6" class="st3" width="153.2" height="3.8"/>
			<rect x="23.4" y="178.8" class="st4" width="153.2" height="3.8"/>
			<rect x="23.4" y="175" class="st5" width="153.2" height="3.8"/>
			<rect x="23.4" y="171.1" class="st6" width="153.2" height="3.8"/>
			<rect x="23.4" y="167.3" class="st7" width="153.2" height="3.8"/>
			<rect x="23.4" y="163.4" class="st8" width="153.2" height="3.8"/>
			<rect x="23.4" y="159.6" class="st9" width="153.2" height="3.8"/>
			<rect x="23.4" y="155.7" class="st10" width="153.2" height="3.8"/>
			<rect x="23.4" y="151.9" class="st11" width="153.2" height="3.8"/>
			<rect x="23.4" y="148.1" class="st12" width="153.2" height="3.8"/>
			<rect x="23.4" y="144.2" class="st13" width="153.2" height="3.8"/>
			<rect x="23.4" y="140.4" class="st14" width="153.2" height="3.8"/>
			<rect x="23.4" y="136.5" class="st15" width="153.2" height="3.8"/>
			<rect x="23.4" y="132.7" class="st16" width="153.2" height="3.8"/>
			<rect x="23.4" y="128.9" class="st17" width="153.2" height="3.8"/>
			<rect x="23.4" y="125" class="st18" width="153.2" height="3.8"/>
			<rect x="23.4" y="121.2" class="st19" width="153.2" height="3.8"/>
			<rect x="23.4" y="117.3" class="st20" width="153.2" height="3.8"/>
			<rect x="23.4" y="113.5" class="st21" width="153.2" height="3.8"/>
			<rect x="23.4" y="109.7" class="st22" width="153.2" height="3.8"/>
			<rect x="23.4" y="105.8" class="st23" width="153.2" height="3.8"/>
			<rect x="23.4" y="102" class="st24" width="153.2" height="3.8"/>
			<rect x="23.4" y="98.1" class="st25" width="153.2" height="3.8"/>
			<rect x="23.4" y="94.3" class="st26" width="153.2" height="3.8"/>
			<rect x="23.4" y="90.4" class="st27" width="153.2" height="3.8"/>
			<rect x="23.4" y="86.6" class="st28" width="153.2" height="3.8"/>
			<rect x="23.4" y="82.8" class="st29" width="153.2" height="3.8"/>
			<rect x="23.4" y="78.9" class="st30" width="153.2" height="3.8"/>
			<rect x="23.4" y="75.1" class="st31" width="153.2" height="3.8"/>
			<rect x="23.4" y="71.2" class="st32" width="153.2" height="3.8"/>
			<rect x="23.4" y="67.4" class="st33" width="153.2" height="3.8"/>
			<rect x="23.4" y="63.6" class="st34" width="153.2" height="3.8"/>
			<rect x="23.4" y="59.7" class="st35" width="153.2" height="3.8"/>
			<rect x="23.4" y="55.9" class="st36" width="153.2" height="3.8"/>
			<rect x="23.4" y="52" class="st37" width="153.2" height="3.8"/>
			<rect x="23.4" y="48.2" class="st38" width="153.2" height="3.8"/>
			<rect x="23.4" y="44.4" class="st39" width="153.2" height="3.8"/>
			<rect x="23.4" y="40.5" class="st40" width="153.2" height="3.8"/>
			<rect x="23.4" y="36.7" class="st41" width="153.2" height="3.8"/>
			<rect x="23.4" y="32.8" class="st42" width="153.2" height="3.8"/>
			<rect x="23.4" y="29" class="st43" width="153.2" height="3.8"/>
			<rect x="23.4" y="25.1" class="st44" width="153.2" height="3.8"/>
			<rect x="23.4" y="21.3" class="st45" width="153.2" height="3.8"/>
			<rect x="23.4" y="17.5" class="st46" width="153.2" height="3.8"/>
			<rect x="23.4" y="13.6" class="st47" width="153.2" height="3.8"/>
			<rect x="23.4" y="9.8" class="st48" width="153.2" height="3.8"/>
			<rect x="23.4" y="5.9" class="st49" width="153.2" height="3.8"/>
			<rect x="23.4" y="2.1" class="st50" width="153.2" height="3.8"/>
		</g>
	</g>
	<g>
		<path class="st51" d="M133.6,2.8l-1.1,7.8l-4.8,32.6c0,3.2,1.8,6.2,4.8,7.6c1.2,0.6,2.6,0.9,4,0.8l40.4-5.9L133.6,2.8z"/>
	</g>
	<g>
		<path class="st52" d="M165.9,199.8H34.1c-6.9,0-12.5-5.6-12.5-12.5V12.7c0-6.9,5.6-12.5,12.5-12.5h100.2l44.2,44.2v142.9
			C178.5,194.2,172.8,199.8,165.9,199.8z M34.1,3.8c-4.9,0-8.9,4-8.9,8.9v174.5c0,4.9,4,8.9,8.9,8.9h131.8c4.9,0,8.9-4,8.9-8.9V45.9
			L132.7,3.8H34.1z"/>
	</g>
	<g>
		<g>
			<defs>
				<path id="SVGID_3_" d="M142.3,45.4h34.5L133.6,2.2v34.5C133.6,41.5,137.5,45.4,142.3,45.4z"/>
			</defs>
			<clipPath id="SVGID_4_">
				<use xlink:href="#SVGID_3_"  style="overflow:visible;"/>
			</clipPath>
			<polygon class="st53" points="176.8,42.6 133.6,2.3 133.6,2.2 176.8,2.2 			"/>
			<polygon class="st53" points="138.8,45.4 133.6,40.5 133.6,2.3 176.8,42.6 176.8,45.4 			"/>
			<polygon class="st53" points="133.6,40.5 138.8,45.4 133.6,45.4 			"/>
		</g>
	</g>
</g>
<g>
	<g>
		<defs>
			<path id="SVGID_5_" d="M62.3,72.1h75.3c6.7,0,12.2,5.5,12.2,12.2l0,0v48.9c0,6.7-5.5,12.2-12.2,12.2l0,0H62.3
				c-6.7,0-12.2-5.5-12.2-12.2V84.3C50.1,77.6,55.6,72.1,62.3,72.1z"/>
		</defs>
		<clipPath id="SVGID_6_">
			<use xlink:href="#SVGID_5_"  style="overflow:visible;"/>
		</clipPath>
		<rect x="50.1" y="72.1" class="st54" width="99.7" height="73.3"/>
		<circle class="st54" cx="99.9" cy="108.8" r="43.8"/>
		<circle class="st55" cx="99.9" cy="108.8" r="43"/>
		<circle class="st55" cx="99.9" cy="108.8" r="42.2"/>
		<circle class="st56" cx="99.9" cy="108.8" r="40.7"/>
		<circle class="st57" cx="99.9" cy="108.8" r="39.2"/>
		<circle class="st58" cx="99.9" cy="108.8" r="37.7"/>
		<circle class="st59" cx="99.9" cy="108.8" r="36.2"/>
		<circle class="st60" cx="99.9" cy="108.8" r="34.8"/>
		<circle class="st61" cx="99.9" cy="108.8" r="33.3"/>
		<circle class="st62" cx="99.9" cy="108.8" r="31.8"/>
		<circle class="st63" cx="99.9" cy="108.8" r="30.3"/>
		<circle class="st64" cx="99.9" cy="108.8" r="28.8"/>
		<circle class="st65" cx="99.9" cy="108.8" r="27.3"/>
		<circle class="st66" cx="99.9" cy="108.8" r="25.9"/>
		<circle class="st67" cx="99.9" cy="108.8" r="24.4"/>
		<circle class="st68" cx="99.9" cy="108.8" r="22.9"/>
		<circle class="st69" cx="99.9" cy="108.8" r="21.4"/>
		<circle class="st70" cx="99.9" cy="108.8" r="19.9"/>
		<circle class="st70" cx="99.9" cy="108.8" r="18.4"/>
		<circle class="st71" cx="99.9" cy="108.8" r="15.4"/>
		<circle class="st72" cx="99.9" cy="108.8" r="12.3"/>
		<circle class="st73" cx="99.9" cy="108.8" r="9.2"/>
		<circle class="st74" cx="99.9" cy="108.8" r="6.1"/>
		<circle class="st75" cx="99.9" cy="108.8" r="3.1"/>
	</g>
	<path class="st76" d="M137.6,147.4H62.3c-7.8,0-14.2-6.4-14.2-14.2V84.3c0-7.8,6.4-14.2,14.2-14.2h75.3c7.8,0,14.2,6.4,14.2,14.2
		v48.9C151.8,141,145.4,147.4,137.6,147.4z M62.3,74.1c-5.6,0-10.2,4.6-10.2,10.2v48.9c0,5.6,4.6,10.2,10.2,10.2h75.3
		c5.6,0,10.2-4.6,10.2-10.2V84.3c0-5.6-4.6-10.2-10.2-10.2H62.3z"/>
</g>
<g class="st77">
	<g>
		<defs>
			<path id="SVGID_7_" d="M107,104.8c15.5,0,30.6,5.3,42.9,14.8V84.3c0-6.7-5.5-12.2-12.2-12.2H62.3c-6.7,0-12.2,5.5-12.2,12.2v48.9
				c0,0.3,0,0.5,0,0.8C63.4,115.8,84.5,105,107,104.8z"/>
		</defs>
		<clipPath id="SVGID_8_">
			<use xlink:href="#SVGID_7_"  style="overflow:visible;"/>
		</clipPath>
		<rect x="50.1" y="72.1" class="st78" width="99.8" height="61.9"/>
		<path class="st78" d="M143.8,108.8c0,12.1-4.9,23-12.8,30.9V135H69.1v4.7c-7.9-7.9-12.8-18.9-12.8-30.9C56.2,84.6,75.8,65,100,65
			S143.8,84.6,143.8,108.8z"/>
		<path class="st79" d="M143,108.8c0,11.9-4.8,22.6-12.6,30.4V135H69.6v4.1c-7.8-7.8-12.6-18.5-12.6-30.4c0-23.7,19.2-43,43-43
			S143,85,143,108.8z"/>
		<path class="st79" d="M142.2,108.8c0,11.6-4.7,22.2-12.4,29.8V135H70.2v3.6c-7.6-7.6-12.4-18.2-12.4-29.8
			c0-23.3,18.9-42.2,42.2-42.2S142.2,85.5,142.2,108.8z"/>
		<path class="st80" d="M140.7,108.8c0,11.2-4.6,21.4-11.9,28.8V135H71.2v2.5c-7.4-7.4-11.9-17.5-11.9-28.8
			c0-22.5,18.2-40.7,40.7-40.7S140.7,86.3,140.7,108.8z"/>
		<path class="st81" d="M139.2,108.8c0,10.8-4.4,20.6-11.5,27.7V135H72.3v1.5c-7.1-7.1-11.5-16.9-11.5-27.7
			c0-21.7,17.6-39.2,39.2-39.2S139.2,87.1,139.2,108.8z"/>
		<path class="st82" d="M137.7,108.8c0,10.4-4.2,19.8-11,26.7V135H73.3v0.4c-6.8-6.8-11-16.3-11-26.7C62.3,87.9,79.2,71,100,71
			S137.7,87.9,137.7,108.8z"/>
		<path class="st83" d="M136.2,108.8c0,10-4.1,19.1-10.6,25.6c-1.6,1.6-3.4,3.1-5.4,4.4V135H79.7v3.8c-1.9-1.3-3.7-2.8-5.4-4.4
			c-6.6-6.6-10.6-15.6-10.6-25.6c0-20,16.2-36.2,36.2-36.2S136.2,88.8,136.2,108.8z"/>
		<path class="st84" d="M134.8,108.8c0,9.6-3.9,18.3-10.2,24.6c-3.1,3.1-6.9,5.7-11,7.4V135H86.5v5.8c-4.2-1.8-7.9-4.3-11-7.4
			c-6.3-6.3-10.2-15-10.2-24.6C65.2,89.6,80.8,74,100,74S134.8,89.6,134.8,108.8z"/>
		<path class="st85" d="M133.3,108.8c0,9.2-3.7,17.5-9.7,23.5c-3,3-6.6,5.4-10.6,7.1V135H87v4.4c-4-1.7-7.6-4.1-10.6-7.1
			c-6-6-9.7-14.3-9.7-23.5c0-18.4,14.9-33.3,33.3-33.3S133.3,90.4,133.3,108.8z"/>
		<circle class="st86" cx="100" cy="108.8" r="31.8"/>
		<circle class="st87" cx="100" cy="108.8" r="30.3"/>
		<circle class="st88" cx="100" cy="108.8" r="28.8"/>
		<circle class="st89" cx="100" cy="108.8" r="27.3"/>
		<circle class="st90" cx="100" cy="108.8" r="25.9"/>
		<circle class="st91" cx="100" cy="108.8" r="24.4"/>
		<circle class="st92" cx="100" cy="108.8" r="22.9"/>
		<circle class="st93" cx="100" cy="108.8" r="21.4"/>
		<circle class="st94" cx="100" cy="108.8" r="19.9"/>
		<circle class="st94" cx="100" cy="108.8" r="18.4"/>
		<circle class="st95" cx="100" cy="108.8" r="15.4"/>
		<circle class="st96" cx="100" cy="108.8" r="12.3"/>
		<circle class="st97" cx="100" cy="108.8" r="9.2"/>
		<circle class="st98" cx="100" cy="108.8" r="6.1"/>
		<circle class="st99" cx="100" cy="108.8" r="3.1"/>
	</g>
</g>
<g class="st77">
	<g>
		<defs>
			<path id="SVGID_9_" d="M147.2,76.8c-7.4,31-35.7,54.7-68.7,54.7c-9.8,0-19.4-2.1-28.4-6.1v7.9c0,6.7,5.5,12.2,12.2,12.2h75.3
				c6.7,0,12.2-5.5,12.2-12.2v-49C149.9,81.6,148.9,78.9,147.2,76.8z"/>
		</defs>
		<clipPath id="SVGID_10_">
			<use xlink:href="#SVGID_9_"  style="overflow:visible;"/>
		</clipPath>
		<rect x="50.1" y="76.8" class="st100" width="99.8" height="68.7"/>
		<path class="st100" d="M143.7,108.8c0,24.2-19.6,43.8-43.8,43.8S56.2,133,56.2,108.8c0-12.1,4.9-23,12.8-31c2-2,4.2-3.8,6.5-5.3
			v3.3h49v-3.3c2.3,1.6,4.5,3.4,6.5,5.3C138.8,85.7,143.7,96.7,143.7,108.8z"/>
		<path class="st101" d="M142.9,108.8c0,23.7-19.2,43-43,43s-43-19.2-43-43c0-11.9,4.8-22.6,12.6-30.4c3.9-3.9,8.5-7,13.7-9.2v6.6
			h33.5v-6.6c5.1,2.2,9.8,5.3,13.7,9.2C138.1,86.1,142.9,96.9,142.9,108.8z"/>
		<path class="st101" d="M142.2,108.8c0,23.3-18.9,42.2-42.2,42.2s-42.2-18.9-42.2-42.2c0-11.7,4.7-22.2,12.4-29.8
			c3.8-3.8,8.4-6.9,13.4-9v5.9h32.9v-5.9c5,2.1,9.6,5.2,13.4,9C137.4,86.6,142.2,97.1,142.2,108.8z"/>
		<path class="st102" d="M140.7,108.8c0,22.5-18.2,40.7-40.7,40.7s-40.7-18.2-40.7-40.7c0-11.2,4.6-21.4,11.9-28.8
			c3.7-3.7,8.1-6.7,12.9-8.7v4.6h31.7v-4.6c4.9,2.1,9.3,5,12.9,8.7C136.1,87.3,140.7,97.5,140.7,108.8z"/>
		<circle class="st103" cx="100" cy="108.8" r="39.2"/>
		<circle class="st104" cx="100" cy="108.8" r="37.7"/>
		<circle class="st105" cx="100" cy="108.8" r="36.3"/>
		<circle class="st106" cx="100" cy="108.8" r="34.8"/>
		<circle class="st107" cx="100" cy="108.8" r="33.3"/>
		<circle class="st108" cx="100" cy="108.8" r="31.8"/>
		<circle class="st109" cx="100" cy="108.8" r="30.3"/>
		<circle class="st110" cx="100" cy="108.8" r="28.8"/>
		<circle class="st111" cx="100" cy="108.8" r="27.4"/>
		<circle class="st112" cx="100" cy="108.8" r="25.9"/>
		<circle class="st113" cx="100" cy="108.8" r="24.4"/>
		<circle class="st114" cx="100" cy="108.8" r="22.9"/>
		<circle class="st115" cx="100" cy="108.8" r="21.4"/>
		<circle class="st116" cx="100" cy="108.8" r="19.9"/>
		<circle class="st116" cx="100" cy="108.8" r="18.5"/>
		<circle class="st117" cx="100" cy="108.8" r="15.4"/>
		<circle class="st118" cx="100" cy="108.8" r="12.3"/>
		<circle class="st119" cx="100" cy="108.8" r="9.2"/>
		<circle class="st120" cx="100" cy="108.8" r="6.2"/>
		<circle class="st121" cx="100" cy="108.8" r="3.1"/>
	</g>
</g>
<g>
	<g>
		<defs>
			<path id="SVGID_11_" d="M149.9,91.9v-7.6c0-6.7-5.5-12.2-12.2-12.2H62.3c-6.7,0-12.2,5.5-12.2,12.2v7.6H149.9z"/>
		</defs>
		<clipPath id="SVGID_12_">
			<use xlink:href="#SVGID_11_"  style="overflow:visible;"/>
		</clipPath>
		<rect x="50.1" y="72.1" class="st122" width="99.8" height="0.2"/>
		<rect x="50.1" y="72.3" class="st123" width="99.8" height="0.5"/>
		<rect x="50.1" y="72.8" class="st124" width="99.8" height="0.5"/>
		<rect x="50.1" y="73.3" class="st125" width="99.8" height="0.5"/>
		<rect x="50.1" y="73.9" class="st126" width="99.8" height="0.5"/>
		<rect x="50.1" y="74.4" class="st127" width="99.8" height="0.5"/>
		<rect x="50.1" y="74.9" class="st128" width="99.8" height="0.5"/>
		<rect x="50.1" y="75.5" class="st129" width="99.8" height="0.5"/>
		<rect x="50.1" y="76" class="st130" width="99.8" height="0.5"/>
		<rect x="50.1" y="76.6" class="st131" width="99.8" height="0.5"/>
		<rect x="50.1" y="77.1" class="st132" width="99.8" height="0.5"/>
		<rect x="50.1" y="77.6" class="st133" width="99.8" height="0.5"/>
		<rect x="50.1" y="78.2" class="st134" width="99.8" height="0.5"/>
		<rect x="50.1" y="78.7" class="st135" width="99.8" height="0.5"/>
		<rect x="50.1" y="79.2" class="st136" width="99.8" height="0.5"/>
		<rect x="50.1" y="79.8" class="st137" width="99.8" height="0.5"/>
		<rect x="50.1" y="80.3" class="st138" width="99.8" height="0.5"/>
		<rect x="50.1" y="80.9" class="st139" width="99.8" height="0.5"/>
		<rect x="50.1" y="81.4" class="st140" width="99.8" height="0.5"/>
		<rect x="50.1" y="81.9" class="st141" width="99.8" height="0.5"/>
		<rect x="50.1" y="82.5" class="st142" width="99.8" height="0.5"/>
		<rect x="50.1" y="83" class="st143" width="99.8" height="0.5"/>
		<rect x="50.1" y="83.5" class="st144" width="99.8" height="0.5"/>
		<rect x="50.1" y="84.1" class="st145" width="99.8" height="0.5"/>
		<rect x="50.1" y="84.6" class="st146" width="99.8" height="0.5"/>
		<rect x="50.1" y="85.2" class="st147" width="99.8" height="0.5"/>
		<rect x="50.1" y="85.7" class="st148" width="99.8" height="0.5"/>
		<rect x="50.1" y="86.2" class="st149" width="99.8" height="0.5"/>
		<rect x="50.1" y="86.8" class="st149" width="99.8" height="0.7"/>
		<rect x="50.1" y="87.4" class="st150" width="99.8" height="0.7"/>
		<rect x="50.1" y="88.1" class="st151" width="99.8" height="0.7"/>
		<rect x="50.1" y="88.8" class="st152" width="99.8" height="0.7"/>
		<rect x="50.1" y="89.4" class="st153" width="99.8" height="0.7"/>
		<rect x="50.1" y="90.1" class="st154" width="99.8" height="0.7"/>
		<rect x="50.1" y="90.7" class="st155" width="99.8" height="0.7"/>
		<rect x="50.1" y="91.4" class="st156" width="99.8" height="0.5"/>
	</g>
</g>
<g class="st157">
	<g>
		<defs>
			<path id="SVGID_13_" d="M137.7,72.1h-9.2c-12.3,9.8-27.5,15.2-43.3,15.3c-11.8,0-23.3-3-33.6-8.8c-0.9,1.8-1.4,3.7-1.4,5.7v7.6
				h99.7v-7.6C149.8,77.6,144.4,72.1,137.7,72.1z"/>
		</defs>
		<clipPath id="SVGID_14_">
			<use xlink:href="#SVGID_13_"  style="overflow:visible;"/>
		</clipPath>
		<rect x="50.2" y="72.1" class="st158" width="99.7" height="0.2"/>
		<rect x="50.2" y="72.3" class="st159" width="99.7" height="0.5"/>
		<rect x="50.2" y="72.8" class="st160" width="99.7" height="0.5"/>
		<rect x="50.2" y="73.3" class="st161" width="99.7" height="0.5"/>
		<rect x="50.2" y="73.9" class="st162" width="99.7" height="0.5"/>
		<rect x="50.2" y="74.4" class="st163" width="99.7" height="0.5"/>
		<rect x="50.2" y="74.9" class="st164" width="99.7" height="0.5"/>
		<rect x="50.2" y="75.5" class="st165" width="99.7" height="0.5"/>
		<rect x="50.2" y="76" class="st166" width="99.7" height="0.5"/>
		<rect x="50.2" y="76.6" class="st167" width="99.7" height="0.5"/>
		<rect x="50.2" y="77.1" class="st168" width="99.7" height="0.5"/>
		<rect x="50.2" y="77.6" class="st169" width="99.7" height="0.5"/>
		<rect x="50.2" y="78.2" class="st170" width="99.7" height="0.5"/>
		<rect x="50.2" y="78.7" class="st171" width="99.7" height="0.5"/>
		<rect x="50.2" y="79.2" class="st172" width="99.7" height="0.5"/>
		<rect x="50.2" y="79.8" class="st173" width="99.7" height="0.5"/>
		<rect x="50.2" y="80.3" class="st174" width="99.7" height="0.5"/>
		<rect x="50.2" y="80.9" class="st175" width="99.7" height="0.5"/>
		<rect x="50.2" y="81.4" class="st176" width="99.7" height="0.5"/>
		<rect x="50.2" y="81.9" class="st177" width="99.7" height="0.5"/>
		<rect x="50.2" y="82.5" class="st178" width="99.7" height="0.5"/>
		<rect x="50.2" y="83" class="st179" width="99.7" height="0.5"/>
		<rect x="50.2" y="83.5" class="st180" width="99.7" height="0.5"/>
		<rect x="50.2" y="84.1" class="st181" width="99.7" height="0.5"/>
		<rect x="50.2" y="84.6" class="st182" width="99.7" height="0.5"/>
		<rect x="50.2" y="85.2" class="st183" width="99.7" height="0.5"/>
		<rect x="50.2" y="85.7" class="st184" width="99.7" height="0.5"/>
		<rect x="50.2" y="86.2" class="st185" width="99.7" height="0.5"/>
		<rect x="50.2" y="86.8" class="st185" width="99.7" height="0.7"/>
		<rect x="50.2" y="87.4" class="st186" width="99.7" height="0.7"/>
		<rect x="50.2" y="88.1" class="st187" width="99.7" height="0.7"/>
		<rect x="50.2" y="88.8" class="st188" width="99.7" height="0.7"/>
		<rect x="50.2" y="89.4" class="st189" width="99.7" height="0.7"/>
		<rect x="50.2" y="90.1" class="st190" width="99.7" height="0.7"/>
		<rect x="50.2" y="90.7" class="st191" width="99.7" height="0.7"/>
		<rect x="50.2" y="91.4" class="st192" width="99.7" height="0.5"/>
	</g>
</g>
<g class="st193">
	<g>
		<defs>
			<path id="SVGID_15_" d="M89.8,72.1H62.3c-6.7,0-12.2,5.5-12.2,12.2v7.6h55.6C98.9,86.7,93.5,79.9,89.8,72.1z"/>
		</defs>
		<clipPath id="SVGID_16_">
			<use xlink:href="#SVGID_15_"  style="overflow:visible;"/>
		</clipPath>
		<rect x="50.1" y="72.1" class="st194" width="55.6" height="0.2"/>
		<rect x="50.1" y="72.3" class="st195" width="55.6" height="0.5"/>
		<rect x="50.1" y="72.8" class="st196" width="55.6" height="0.5"/>
		<rect x="50.1" y="73.3" class="st197" width="55.6" height="0.5"/>
		<rect x="50.1" y="73.9" class="st198" width="55.6" height="0.5"/>
		<rect x="50.1" y="74.4" class="st199" width="55.6" height="0.5"/>
		<rect x="50.1" y="74.9" class="st200" width="55.6" height="0.5"/>
		<rect x="50.1" y="75.5" class="st201" width="55.6" height="0.5"/>
		<rect x="50.1" y="76" class="st202" width="55.6" height="0.5"/>
		<rect x="50.1" y="76.6" class="st203" width="55.6" height="0.5"/>
		<rect x="50.1" y="77.1" class="st204" width="55.6" height="0.5"/>
		<rect x="50.1" y="77.6" class="st205" width="55.6" height="0.5"/>
		<rect x="50.1" y="78.2" class="st206" width="55.6" height="0.5"/>
		<rect x="50.1" y="78.7" class="st207" width="55.6" height="0.5"/>
		<rect x="50.1" y="79.2" class="st208" width="55.6" height="0.5"/>
		<rect x="50.1" y="79.8" class="st209" width="55.6" height="0.5"/>
		<rect x="50.1" y="80.3" class="st210" width="55.6" height="0.5"/>
		<rect x="50.1" y="80.9" class="st211" width="55.6" height="0.5"/>
		<rect x="50.1" y="81.4" class="st212" width="55.6" height="0.5"/>
		<rect x="50.1" y="81.9" class="st213" width="55.6" height="0.5"/>
		<rect x="50.1" y="82.5" class="st214" width="55.6" height="0.5"/>
		<rect x="50.1" y="83" class="st215" width="55.6" height="0.5"/>
		<rect x="50.1" y="83.5" class="st216" width="55.6" height="0.5"/>
		<rect x="50.1" y="84.1" class="st217" width="55.6" height="0.5"/>
		<rect x="50.1" y="84.6" class="st218" width="55.6" height="0.5"/>
		<rect x="50.1" y="85.2" class="st219" width="55.6" height="0.5"/>
		<rect x="50.1" y="85.7" class="st220" width="55.6" height="0.5"/>
		<rect x="50.1" y="86.2" class="st221" width="55.6" height="0.5"/>
		<rect x="50.1" y="86.8" class="st221" width="55.6" height="0.7"/>
		<rect x="50.1" y="87.4" class="st222" width="55.6" height="0.7"/>
		<rect x="50.1" y="88.1" class="st223" width="55.6" height="0.7"/>
		<rect x="50.1" y="88.8" class="st224" width="55.6" height="0.7"/>
		<rect x="50.1" y="89.4" class="st225" width="55.6" height="0.7"/>
		<rect x="50.1" y="90.1" class="st226" width="55.6" height="0.7"/>
		<rect x="50.1" y="90.7" class="st227" width="55.6" height="0.7"/>
		<rect x="50.1" y="91.4" class="st228" width="55.6" height="0.5"/>
	</g>
</g>
<g>
	<path class="st229" d="M112.6,84.2c0-1.7,1.4-3.1,3.1-3.1l0,0l0,0c1.7,0,3.1,1.4,3.1,3.1l0,0l0,0c0,1.7-1.4,3.1-3.1,3.1l0,0l0,0
		C114,87.3,112.6,85.9,112.6,84.2C112.6,84.3,112.6,84.3,112.6,84.2z"/>
</g>
<g>
	<path class="st229" d="M125.6,84.2c0-1.7,1.4-3.1,3.1-3.1l0,0l0,0c1.7,0,3.1,1.4,3.1,3.1l0,0l0,0c0,1.7-1.4,3.1-3.1,3.1l0,0l0,0
		C127,87.3,125.6,85.9,125.6,84.2C125.6,84.3,125.6,84.3,125.6,84.2z"/>
</g>
<g>
	<path class="st229" d="M138.7,84.2c0-1.7,1.4-3.1,3.1-3.1l0,0l0,0c1.7,0,3.1,1.4,3.1,3.1l0,0l0,0c0,1.7-1.4,3.1-3.1,3.1l0,0l0,0
		C140,87.3,138.7,85.9,138.7,84.2C138.7,84.3,138.7,84.3,138.7,84.2z"/>
</g>
<g class="st230">
	<g>
		<path class="st231" d="M79.9,131.1l-12.5-12.4c-1-1-1-2.7,0-3.8l0,0l12.5-12.4c1-1,2.7-1,3.8,0l0,0l0,0c1,1,1,2.7,0,3.8l0,0
			l-10.6,10.6l10.6,10.6c1,1,1,2.7,0,3.8l0,0c-0.5,0.5-1.2,0.8-1.9,0.8l0,0C81.1,131.9,80.4,131.6,79.9,131.1z"/>
	</g>
	<g>
		<path class="st231" d="M120.1,131.1l12.5-12.4c1-1,1-2.7,0-3.8l0,0l-12.5-12.4c-1-1-2.7-1-3.8,0l0,0l0,0c-1,1-1,2.7,0,3.8l0,0
			l10.6,10.6l-10.6,10.6c-1,1-1,2.7,0,3.8l0,0c0.5,0.5,1.2,0.8,1.9,0.8l0,0C118.9,131.9,119.6,131.6,120.1,131.1z"/>
	</g>
</g>
<g>
	<path class="st232" d="M79.9,128.9l-12.5-12.4c-1-1-1-2.7,0-3.8l0,0l12.5-12.4c1-1,2.7-1,3.8,0l0,0l0,0c1,1,1,2.7,0,3.8l0,0
		l-10.6,10.6l10.6,10.6c1,1,1,2.7,0,3.8l0,0c-0.5,0.5-1.2,0.8-1.9,0.8l0,0C81.1,129.7,80.4,129.5,79.9,128.9z"/>
</g>
<g>
	<path class="st232" d="M120.1,128.9l12.5-12.4c1-1,1-2.7,0-3.8l0,0l-12.5-12.4c-1-1-2.7-1-3.8,0l0,0l0,0c-1,1-1,2.7,0,3.8l0,0
		l10.6,10.6l-10.6,10.6c-1,1-1,2.7,0,3.8l0,0c0.5,0.5,1.2,0.8,1.9,0.8l0,0C118.9,129.7,119.6,129.5,120.1,128.9z"/>
</g>
</svg>
