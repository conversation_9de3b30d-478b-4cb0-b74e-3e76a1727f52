<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="hasLayout && device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <topbar v-if="hasLayout" class="topbar-container" @searchMenu="changeMenu"/>
    <sidebar v-if="hasLayout" ref="sidebar" class="sidebar-container" />
    <div class="main-container">
      <div v-if="hasLayout" class="fixed-header">
        <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
        <tags-view v-if="tagsView" />
      </div>
      <app-main />
    </div>
    <alarm-prompt v-if="hasLayout" ref="alarmPrompt" />
    <sys-property v-if="hasLayout" ref="property" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { Topbar, Sidebar, TagsView, AppMain, AlarmPrompt, SysProperty } from './components/index.js'
import Hamburger from '@/components/Hamburger'
import ResizeMixin from './mixin/ResizeHandler'
import { getOfflineTerminals } from '@/api/system/configManage/cleanUpConfig'
import { getUrlParams } from '@/utils'

export default {
  name: 'Layout',
  components: {
    Topbar,
    Sidebar,
    Hamburger,
    TagsView,
    AppMain,
    AlarmPrompt,
    SysProperty
  },
  mixins: [ResizeMixin],
  data() {
    return {
      cleanDuration: undefined,
      hasLayout: true
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'device',
      'tagsView',
      'theme',
      'sysResources'
    ]),
    classObj() {
      return {
        noLayout: !this.hasLayout,
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  async created() {
    const theme = this.theme || this.sysResources.theme
    document.body.className = theme
    // 从url中获取参数noLayout，若noLayout有值且为 'true', 则不显示控制台布局，只显示菜单页面内容
    const { noLayout } = getUrlParams(window.location.href)
    this.hasLayout = noLayout !== 'true'
    if (this.hasLayout) {
      await this.getOfflineTerminals()
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    changeMenu(menu) {
      this.$refs.sidebar.currentMenus = menu
    },
    getOfflineTerminals() {
      // 定期清理未上线终端功能只允许超管/系统管理员配置，界面限制只有超管/系统管理员才会去请求当前未上线终端
      if (this.$store.getters.isSuperRole || this.$store.getters.isSysRole) {
        getOfflineTerminals().then(response => {
          const h = this.$createElement
          if (response.data.conunt > 0) {
            this.cleanDuration = response.data.cleanDuration
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: h('div', null, [
                h('span', null, this.$t('layout.termNotLogin', { info: response.data.conunt })),
                h(
                  'a',
                  {
                    style: 'margin-top:5px;color: #409EFF;cursor: pointer;',
                    on: { click: this.goToTerminalPage }
                  },
                  this.$t('layout.viewDetail')
                )
              ]),
              type: 'warning'
            })
          }
        })
      }
    },
    goToTerminalPage() {
      if (this.$route.name == 'Terminal') {
        this.$store.dispatch('user/setCleanDuration', this.cleanDuration)
        // 通知列表刷新
        this.$store.dispatch('commonData/changeNotice', 'autoReloadData')
      } else {
        this.$router.push({ name: 'Terminal', params: { status: 0, offLineType: this.cleanDuration, useType: 0 }})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";

  .app-wrapper {
    @include clearfix;
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
    &.mobile.openSidebar{
      position: fixed;
      top: 0;
    }
  }
  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }
  .hamburger-container{
    width: 50px;
    height: 34px;
    padding: 7px 15px;
    float: left;
    color: #eee;
    background: #0c161e;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;
  }
  .fixed-header {
    position: fixed;
    top: 50px;
    right: 0;
    z-index: 99;
    width: calc(100% - 210px);
    transition: width 0.28s;
    background: #0c161e;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px)
  }

  .mobile .fixed-header {
    width: 100%;
  }
</style>
