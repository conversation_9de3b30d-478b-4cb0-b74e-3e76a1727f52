<template>
  <div class="tags-view-container">
    <i v-show="leftArrowShow" class="el-icon-d-arrow-left arrow arrow-left" @click="arrowClick(-1)"></i>
    <i v-show="rightArrowShow" class="el-icon-d-arrow-right arrow arrow-right" @click="arrowClick(1)"></i>
    <scroll-pane
      ref="scrollPane"
      class="tags-view-wrapper"
      @isScrollLeft="val => { leftArrowShow = !val }"
      @isScrollRight="val => { rightArrowShow = !val }"
    >
      <router-link
        v-for="tag in visitedViews"
        ref="tag"
        :key="tag.path"
        :class="isActive(tag)?'active':''"
        :to="{ name: tag.name, query: tag.query, fullPath: tag.fullPath, params: tag.params }"
        tag="span"
        class="tags-view-item"
        @click.middle.native="closeSelectedTag(tag)"
        @contextmenu.prevent.native="openMenu(tag,$event)"
      >
        {{ generateTitle(tag.title) }}
        <span v-if="!tag.meta.affix" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
      </router-link>
    </scroll-pane>

    <!-- 右键菜单 -->
    <ul v-show="visible" :style="{left:left+'px',top:top+'px'}" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">
        {{ $t('tagsView.refresh') }}
      </li>
      <li v-if="!(selectedTag.meta&&selectedTag.meta.affix)" @click="closeSelectedTag(selectedTag)">
        {{ $t('tagsView.close') }}
      </li>
      <li @click="closeOthersTags">
        {{ $t('tagsView.closeOthers') }}
      </li>
      <li @click="closeAllTags(selectedTag)">
        {{ $t('tagsView.closeAll') }}
      </li>
      <li @click="setting(false)">
        {{ $t('tagsView.setting') }}
      </li>
      <li v-if="(selectedTag.meta&&selectedTag.meta.customPath)" @click="fullscreenTags()">
        {{ $t('tagsView.fullScreen') }}
      </li>
    </ul>

    <!-- 设置页面上线配置的弹窗 -->
    <el-dialog
      :title="$t('tagsView.setting')"
      :close-on-click-modal="false"
      :modal="false"
      append-to-body
      :visible.sync="settingVisible"
      width="400px"
    >
      <p style="margin-top: -10px">{{ firstShow ? $t('tagsView.content') : '' }}</p>
      <i18n path="tagsView.interfaceUpperLimitNum">
        <template slot="number">
          <el-input
            v-model="pageLimitValue"
            style="width: 60px"
            @input="upperInput"
            @change="(val) => val < 10 ? pageLimitValue = 10 : (val > 20 ? pageLimitValue = 20 : pageLimitValue)"
          />
        </template>
      </i18n>
      <el-tooltip effect="dark" placement="top-start">
        <div slot="content">
          {{ $t('tagsView.upperLimitPrompt') }}<br/>
        </div>
        <i class="el-icon-info" />
      </el-tooltip>
      <br/><br/>
      <div style="margin-buttom: 30px">
        {{ $t('tagsView.content1') }}
        <el-radio-group v-model="closeTypeValue">
          <el-radio :label="1">{{ $t('tagsView.content2') }}</el-radio>
          <el-radio :label="2">{{ $t('tagsView.content3') }}</el-radio>
        </el-radio-group>
      </div>
      <span slot="footer" class="dialog-footer" >
        <el-button type="primary" @click="saveSetting">{{ $t('button.confirm') }}</el-button>
        <el-button @click="settingVisible = false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ScrollPane from './ScrollPane'
import { generateTitle } from '@/utils/i18n'
import path from 'path'
import { insertPersonalization } from '@/api/user'
import { mapGetters } from 'vuex';
import { EventBus } from '@/layout/event-bus';

export default {
  name: 'TagsView',
  components: { ScrollPane },
  data() {
    return {
      visible: false,                      // 右键菜单显示
      settingVisible: false,               // 设置弹窗显示
      firstShow: false,                    // 设置弹窗是否首次显示（首次显示有额外提示信息）
      closeTypeValue: 1,                   // 页面超出上限时的处理方式
      pageLimitValue: 10,                  // 页面上限
      top: 0,                              // 右键菜单top
      left: 0,                             // 右键菜单left
      leftArrowShow: false,                // tag栏左侧箭头是否显示
      rightArrowShow: false,               // tag栏右侧箭头是否显示
      currentTag: null,                    // 当前页面的 visitedViews 菜单路由
      selectedTag: {},                     // 右键菜单选中的菜单路由
      affixTags: [],                       // 配置 affix 的路由
      flag: false                          // 
    }
  },
  computed: {
    ...mapGetters([
      'closeType',
      'pageLimit',
      'visitedViews',
      'permission_routes',
      'userId'
    ])
  },
  watch: {
    $route() {
      if (this.currentTag) {
        // 离开当前页面时，将 params 和 query 清除，避免通过tag返回时，影响页面状态
        // 但保留 treeable（预定义策略页面判断是否显示树组件）, stgTypeNumber 内容策略页面使用的参数
        const fullPath = this.currentTag.fullPath.split('?')[0]
        const stgTypeNumber = this.currentTag.query.stgTypeNumber
        const treeable = this.currentTag.query.treeable
        Object.assign(this.currentTag, { fullPath, params: {}, query: {}})
        if (stgTypeNumber != undefined) {
          this.currentTag.query.stgTypeNumber = stgTypeNumber 
        }
        if (treeable != undefined) {
          this.currentTag.query.treeable = treeable 
        }
      }
      this.addTags()
      this.moveToCurrentTag()
    },
    closeType(value) {
      this.closeTypeValue = value
    },
    pageLimit(value) {
      this.pageLimitValue = value
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.initTags()
    this.addTags()
  },
  created() {
    // 保存tagsView实例，用于其他地方使用
    this.$store.dispatch('tagsView/setTagsViewVm', this)
    this.closeTypeValue = this.closeType || 1
    this.pageLimitValue = this.pageLimit || 10
  },
  methods: {
    generateTitle, // generateTitle by vue-i18n
    // 当前活动tag
    isActive(route) {
      return route.path === this.$route.path
    },
    // 过滤出配置了 affix 的路由
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach(route => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path)
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta }
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    // 初始化tag菜单
    initTags() {
      const affixTags = this.affixTags = this.filterAffixTags(this.permission_routes)
      for (const tag of affixTags) {
        // 将有配置 affix 为 true，且有 name 的路由添加到 visitedViews
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', tag)
        }
      }
    },
    // 添加tag
    addTags() {
      const { name } = this.$route
      // const { title } = this.$route.meta
      if (name) {
        // 全盘扫描 实现多个菜单入口共用一个组件的功能（从另一个全盘扫描路由进入已打开的全盘扫描页面时，更新路由）
        // if (title == 'diskScan') {
        //   const view = this.visitedViews.find(view => view.title == title)
        //   if (view) {
        //     Object.assign(view, this.$route)
        //     return false
        //   }
        // }
        this.$store.dispatch('tagsView/addView', this.$route)
      }
      return false
    },
    // 移动到当前页面的tag
    moveToCurrentTag() {
      const tags = this.$refs.tag
      this.$nextTick(() => {
        for (const tag of tags) {
          // 此处to为router-link组件to所绑定的对象
          const fullPath = tag.to.fullPath.split('?')[0]
          if (fullPath === this.$route.path) {
            this.$refs.scrollPane.moveToTarget(tag)
            // 当 query 或 params 不一致时，更新 visitedView
            const queryDifferent = JSON.stringify(tag.to.query) != JSON.stringify(this.$route.query)
            const paramsDifferent = JSON.stringify(tag.to.params) != JSON.stringify(this.$route.params)
            if (queryDifferent || paramsDifferent) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            this.setCurrentView()
            break
          }
        }
      })
    },
    // 将当前页面的路由存为 currentTag
    setCurrentView() {
      const views = this.visitedViews
      this.$nextTick(() => {
        for (const view of views) {
          const fullPath = view.fullPath.split('?')[0]
          if (fullPath === this.$route.path) {
            this.currentTag = view
            break
          }
        }
      })
    },
    // 刷新选中的菜单页面
    refreshSelectedTag(view) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    closeSelectedTag(view) {
      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        const currentPath = this.$route.path
        if (this.affixTags.some(tag => tag.path === currentPath)) {
          return
        }
        this.toLastView(visitedViews)
      })
    },
    setting(val) {
      this.firstShow = val
      this.settingVisible = true
    },
    saveSetting() {
      if (!this.pageLimitValue) {
        this.$message({
          message: this.$t('tagsView.content8'),
          type: 'error',
          duration: 2000
        })
        return
      }
      insertPersonalization({ sysUserId: this.userId, value: `${this.pageLimitValue}${this.closeTypeValue}`, type: 1 }).then(response => {
        this.settingVisible = false
        this.$message({
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        if (response.data.length > 0) {
          this.flag = true
        }
        this.$store.dispatch('user/setPageLimit', this.pageLimitValue)
        this.$store.dispatch('user/setCloseType', this.closeTypeValue)
      }).catch(res => {
      })
      if (this.flag) {
        this.firstShow = false
      }
    },
    /**
     * 全屏
     * */
    fullscreenTags() {
      EventBus.$emit('changeFullScreen')
    },
    toLastView(visitedViews) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView)
      } else {
        // You can set another route
        this.$router.push('/')
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 15 // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY - 20
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    arrowClick(type) {
      this.$refs.scrollPane.handleScroll({ wheelDelta: 480 * type })
    },
    upperInput(value) {
      if (typeof (value) == 'string') value = value.replace(/[^\d]/g, '')
      this.pageLimitValue = value == '' ? '' : Number(value)
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 34px;
  margin: 0 20px 0 50px;
  background: #0c161e;
  border-bottom: 1px solid #2674b2;
  position: relative;
  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      border: 1px solid #295477;
      color: #ccc;
      background: #0c161e;
      padding: 0 8px;
      font-size: 12px;
      margin-left: 5px;
      margin-top: 4px;
      &:first-of-type {
        margin-left: 15px;
      }
      &:last-of-type {
        margin-right: 15px;
      }
      &.active {
        background-color: #295477;
        color: #fff;
        border-color: #295477;
        &::before {
          content: '';
          background: #fff;
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: relative;
          margin-right: 2px;
        }
      }
    }
  }
  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 100;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}
.arrow {
  position: absolute;
  padding: 9px 0 8px;
  background: inherit;
  cursor: pointer;
  z-index: 2;
  &:hover {
    color: #fff;
  }
}
.arrow-left {
  left: 0;
}
.arrow-right {
  right: 0;
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all .3s cubic-bezier(.645, .045, .355, 1);
      transform-origin: 100% 50%;
      &:before {
        transform: scale(.6);
        display: inline-block;
        vertical-align: -3px;
      }
      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}
</style>
