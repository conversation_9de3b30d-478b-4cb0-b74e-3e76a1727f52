<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      :title="$t('layout.advancedConfig')"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="!dialogFormVisible"
      width="800px"
    >
      <el-tabs :active-name="activeTab" style="height: 465px;">
        <el-tab-pane :label="$t('layout.advancedConfig')" name="configTab">
          <div class="table-container">
            <div class="toolbar">
              <el-button v-if="showHidden" type="primary" size="mini" @click="handleFileImport">
                {{ $t('layout.importAuthorizationFile') }}
              </el-button>
              <el-button v-if="showHidden" type="primary" size="mini" @click="handleExport">
                {{ $t('layout.exportKey') }}
              </el-button>
              <el-button v-if="showHidden" v-permission="'122'" type="primary" size="mini" @click="handleCompetitor">
                {{ $t('layout.compatibleSetting') }}
              </el-button>
              <div class="searchCon" style="float: right;">
                <el-input v-model="query.code" v-trim clearable :placeholder="$t('layout.configItem')" style="width: 200px;top: -2px;" @keyup.enter.native="handleFilter" />
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <grid-table ref="propList" :height="390" :multi-select="false" :col-model="colModel" :row-data-api="rowDataApi" pager-small />
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('layout.SysRunLogs')" name="logTab">
          <log-file/>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('layout.updateSetting')"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 450px;">
        <FormItem :label="$t('layout.configItem')" prop="code">
          <el-input v-model="temp.code" :maxlength="60" disabled />
        </FormItem>
        <FormItem :label="$t('layout.configValue')" prop="value">
          <el-switch
            v-if="temp.type === 'boolean'"
            v-model="temp.value"
            :active-value="rule.truthy == null ? 'true' : rule.truthy"
            :inactive-value="rule.falsy == null ? 'false' : rule.falsy"
          />
          <el-input-number
            v-else-if="temp.type === 'number'"
            v-model="temp.value"
            :min="rule.min || 0"
            :max="rule.max || Number.MAX_SAFE_INTEGER"
            :step="rule.step || 1"
            :step-strictly="rule.strict"
            :precision="rule.precision || 0"
            controls-position="right"
          />
          <el-input
            v-else-if="temp.type === 'string'"
            v-model="temp.value"
            :type="rule.type || 'text'"
            :minlength="rule.minlength || 0"
            :maxlength="rule.maxlength"
            :show-password="rule.type === 'password'"
            :rows="3"
            show-word-limit
          />
          <el-select
            v-else-if="temp.type === 'enum'"
            v-model="temp.value"
            :multiple="rule.multiple"
            filterable
          >
            <el-option v-for="item in (rule.items || [])" :key="item" :label="item" :value="item" />
          </el-select>
          <div v-else-if="temp.type === 'datetime'">
            <el-time-picker
              v-if="rule.type === 'time' || rule.type === 'timerange'"
              v-model="temp.value"
              range-separator="~"
              :is-range="rule.type === 'timerange'"
              :clearable="false"
              :value-format="rule.format"
            />
            <el-date-picker
              v-else
              v-model="temp.value"
              range-separator="~"
              :type="rule.type"
              :clearable="false"
              :value-format="rule.format"
            />
          </div>
          <el-input v-else v-model="temp.value" :maxlength="20" show-word-limit/>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" :rows="3" resize="none" :maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('layout.importAuthorizationFile2')"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFileVisible"
      width="500px"
    >
      <Form ref="fileDataForm" :rules="fileRules" :model="fileTemp" label-position="right" label-width="90px" style="width: 450px;">
        <FormItem label-width="0">
          <el-col :span="18">
            <FormItem :label="$t('pages.file1')" prop="fileName">
              <el-input v-model="fileTemp.fileName" readonly :placeholder="$t('layout.importAuthorizationFile3')"/>
            </FormItem>
          </el-col>
          <el-col :span="4">
            <el-upload
              ref="upload"
              accept=".ini"
              class="upload-demo"
              name="uploadFile"
              action="aaaaaa"
              :show-file-list="false"
              :on-change="beforeUpload"
              :file-list="fileList"
              :auto-upload="false"
            >
              <el-button size="small" type="primary">{{ $t('components.upload') }}</el-button>
            </el-upload>
          </el-col>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateFile">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFileVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <main-key-file ref="mainKeyFile"/>
  </div>
</template>

<script>
import LogFile from '../LogFile'
import { getPropertyPage, updateProperty } from '@/api/property';
import { uploadFile } from '@/api/grantAuth';
import MainKeyFile from '@/layout/components/MainKeyFile'

export default {
  name: 'SysProperty',
  components: { LogFile, MainKeyFile },
  data() {
    return {
      colModel: [
        { prop: 'code', label: 'configItem', width: '150', sort: true },
        { prop: 'value', label: 'configValue', width: '200' },
        { prop: 'remark', label: 'remark', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '50',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        code: ''
      },
      temp: {},
      fileTemp: {
        fileName: ''
      },
      rule: undefined,
      dialogVisible: false,
      dialogFileVisible: false,
      dialogFormVisible: false,
      submitting: false,
      activeTab: 'configTab',
      rules: {
        value: [{ required: true, message: this.$t('layout.enterConfigValue'), trigger: 'blur' }],
        remark: [{ required: true, message: this.$t('layout.enterRemark'), trigger: 'blur' }]
      },
      fileRules: {
        fileName: [
          { required: true, message: this.$t('pages.signatureData_text2'), trigger: 'blur' },
          { validator: this.fileNameValid, trigger: 'blur' }
        ]
      },
      showHidden: false,
      fileList: []
    };
  },
  computed: {
    gridTable() {
      return this.$refs['propList']
    }
  },
  created() {
    // document.addEventListener('keyup', this.handleKeyUp)
  },
  methods: {
    parseRule(rule) {
      if (typeof rule !== 'string' || rule.trim().length === 0) {
        return {}
      }
      try {
        return JSON.parse(rule)
      } catch (e) {
        return {}
      }
    },
    notifySuccess(msg) {
      this.$notify({
        title: this.$t('text.success'),
        message: msg,
        type: 'success',
        duration: 2000
      })
    },
    handleKeyUp(e) {
      if (e.ctrlKey && e.altKey && e.shiftKey) {
        if (e.key === 's' || e.key === 'S') {
          this.dialogVisible = true
        }
        e.preventDefault()
      }
      if (e.ctrlKey && e.altKey && e.shiftKey) {
        if (e.key === 'k' || e.key === 'K') {
          this.showHidden = true
        }
        e.preventDefault()
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleCompetitor() {
      this.dialogVisible = false
      this.$router.push({ path: '/a/seamlessReplace' })
    },
    handleExport() {
      this.$refs.mainKeyFile.handleExport()
    },
    handleFileImport() {
      this.fileTemp = {
        fileName: ''
      }
      this.$nextTick(() => {
        this.$refs['upload'].clearFiles()
      })
      this.dialogFileVisible = true
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPropertyPage(searchQuery)
    },
    handleUpdate(row) {
      this.submitting = false
      this.temp = Object.assign({}, row)
      this.rule = this.parseRule(this.temp.rule)
      if ((this.temp.type === 'enum' && this.rule.multiple)) {
        this.temp.value = this.temp.value.split(',')
      } else if (this.temp.type === 'datetime' && this.rule.type && this.rule.type.endsWith('range')) {
        this.temp.value = this.temp.value.split('~')
      }
      this.dialogFormVisible = true
      this.$nextTick(() => this.$refs['dataForm'].clearValidate())
    },
    updateFile() {
      this.submitting = true
      this.$refs['fileDataForm'].validate((valid) => {
        if (valid) {
          const fd = new FormData()
          // const file = this.$refs['upload']
          const uploadFiles = this.$refs['upload'].uploadFiles
          fd.append('uploadFile', uploadFiles[uploadFiles.length - 1].raw) // 传文件
          uploadFile(fd).then(res => {
            this.dialogFileVisible = false
            this.submitting = false
            this.$message({
              message: this.$t('text.uploadSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = {
            code: this.temp.code,
            value: Array.isArray(this.temp.value) ? this.temp.value.join(this.temp.type === 'enum' ? ',' : '~') : this.temp.value,
            remark: this.temp.remark
          }
          updateProperty(tempData).then(_ => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.notifySuccess(this.$t('text.updateSuccess'))
          }).catch(_ => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    beforeUpload(file, fileList) {
      this.fileTemp.fileName = file.name
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    fileNameValid(rule, value, callback) {
      if (this.fileTemp.fileName != 'ArResRights.ini') {
        callback(new Error(this.$t('layout.ArResRights')))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-date-editor.el-input, .el-range-editor.el-input__inner {
    width: 360px;
  }
</style>
