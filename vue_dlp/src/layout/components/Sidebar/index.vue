<template>
  <div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        ref="sideMenu"
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item v-for="route in getRoutes" :key="route.path" :item="route" :base-path="setBasePath(route.path)" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SidebarItem from './SidebarItem'

export default {
  components: { SidebarItem },
  data() {
    return {
      currentMenus: null,
      currentMenusChange: false
    }
  },
  computed: {
    ...mapGetters([
      'permission_routes',
      'sidebar',
      'common_routes',
      'theme'
    ]),
    commonRoute() { // 常用菜单路由
      const route = [{
        path: 'common_routes',
        name: 'common_routes',
        alwaysShow: true,
        meta: {
          title: this.$t('route.commonRoutes'),
          icon: 'star'
        },
        children: []
      }]
      route[0].children = this.common_routes
      return route
    },
    getRoutes() {
      const routePath = this.currentMenusChange ? this.currentMenus.fullPath : this.$route.path
      const permission_routes = this.permission_routes
      const rootMenu = routePath.split('/')[1].toLowerCase()
      let routes = []
      permission_routes.forEach(element => {
        if (element.name && element.name.toLowerCase() === rootMenu) {
          routes = element.children
          return false
        }
      })
      if (routes[0] && routes[0].name === 'common_routes') {
        routes[0] = this.commonRoute[0]
      } else {
        routes.unshift(this.commonRoute[0])
      }
      return routes || []
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // 如果设置路径，侧栏将突出显示您设置的路径
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  watch: {
    currentMenus(val) {
      if (val) {
        this.currentMenusChange = true
        this.$nextTick(() => {
          if (val.path !== val.fullPath) {
            this.$refs.sideMenu.open(val.path)
          }
        })
      }
    },
    '$route.path'(val) {
      this.currentMenus = null
      this.currentMenusChange = false
    }
  },
  methods: {
    setBasePath(path) {
      const routePath = this.currentMenusChange ? this.currentMenus.fullPath : this.$route.path
      return '/' + routePath.split('/')[1] + '/' + path
    }
  }
}
</script>

