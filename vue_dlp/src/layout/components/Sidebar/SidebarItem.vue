<template>
  <div v-if="!item.hidden && !item.noRender" class="menu-wrapper">
    <template v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="{path:resolvePath(onlyOneChild.path),query:onlyOneChild.query}">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown':!isNest}">
          <item :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)" :title="generateTitle(onlyOneChild.meta.title)" />
          <svg-icon icon-class="star" :class="favoriconClass" @click.stop.prevent="handleFavoricon(onlyOneChild)" />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu v-else ref="subMenu" :index="item.path" popper-append-to-body>
      <template slot="title">
        <item v-if="item.meta" :icon="item.meta && item.meta.icon" :title="generateTitle(item.meta.title)" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { generateTitle } from '@/utils/i18n'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {}
  },
  computed: {
    favoriconClass() {
      const active = this.favoriconArr.indexOf(this.basePath) > -1
      return active ? 'sidebar-favoricon is-active' : 'sidebar-favoricon'
    },
    favoriconArr() {
      return this.$store.state.permission.favoriconArr
    }
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(如果只有一个显示子元素，则使用)
          this.onlyOneChild = item
          return true
        }
      })

      // 当只有一个子路由器时，默认情况下显示子路由器
      if (showingChildren.length === 1) {
        return true
      }

      // 如果没有要显示的子路由器，则显示父路由器
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    },
    handleFavoricon(route) {
      const tem = { ... route }
      tem.path = this.basePath

      if (this.favoriconArr.indexOf(this.basePath) > -1) {
        this.$confirmBox(this.$t('layout.removeCommon'), this.$t('text.prompt')).then(() => {
          this.$store.dispatch('permission/deleteCommonMenu', tem)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('layout.removeCommon2'),
            type: 'success',
            duration: 2000
          })
        }).catch(() => {})
      } else {
        this.$confirmBox(this.$t('layout.addCommon'), this.$t('text.prompt')).then(() => {
          this.$store.dispatch('permission/addCommonMenu', tem)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('layout.addCommon2'),
            type: 'success',
            duration: 2000
          })
        }).catch(() => {})
      }
    },
    generateTitle
  }
}
</script>
