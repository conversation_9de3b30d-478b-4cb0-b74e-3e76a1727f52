<template>
  <div>
  </div>
</template>

<script>
import { downloadFile, startDownload } from '@/api/dataEncryption/encryption/seamlessReplace'
import { buildDownloadFileByName } from '@/utils/download/helper'

export default {
  name: 'MainKeyFile',
  components: {},
  data() {
    return {
      statusCodeMap: {
        0: 'pages.pKeyInfoFileResult1',
        1: 'pages.pKeyInfoFileResult1',
        2: 'pages.pKeyInfoFileResult2',
        3: 'pages.pKeyInfoFileResult3',
        9: 'pages.pKeyInfoFileResult9',
        'other': 'pages.enterpriseKeyUnKnownError'
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    handleExport(file) {
      if (file == null) {
        file = buildDownloadFileByName(this.$t('table.pkey'))
      }
      file.steps = 4
      this.submitting = true
      startDownload().then(response => {
        const that = this
        file.active = 1
        this.$socket.subscribeToAjax(response, 'mainkeyfile/result', (respond, handle) => {
          // 得到异步结果
          handle.close()
          // 请求文件下载
          const data = respond.data
          // 如果引擎返回的结果type不等于（1成功），则显示具体错误信息msg
          if (data.type !== 1) {
            file.error = data.msg
            file.errorI18Key = this.statusCodeMap[data.type] || this.statusCodeMap['other']
            file.canceled = true
            file.active = 0
            this.submitting = false
            return;
          }
          const opts = { file, jwt: true, topic: 'MainKeyFile' }
          downloadFile({ files: data.fileName }, opts).then(() => {
            that.submitting = false
          })
        })
      }).catch(reason => {
        console.log(reason)
        this.submitting = false
      })
    }
  }
}
</script>

