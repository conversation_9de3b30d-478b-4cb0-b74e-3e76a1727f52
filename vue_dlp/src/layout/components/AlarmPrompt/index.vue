<template>
  <div>
    <el-dialog
      v-drag-alarm
      :close-on-click-modal="false"
      :modal="false"
      class="alarm-prompt"
      :visible.sync="dialogVisible"
      width="400px"
      @close="closeDlg"
      @closed="closed"
    >
      <div slot="title">
        <span class="el-dialog__title">{{ $t('layout.alarmPrompt') }}</span>
        <span class="delay">
          <el-checkbox v-model="showDelay">{{ $t('layout.notShownInShortTerm') }}</el-checkbox>
          <svg-icon v-if="showDelay" icon-class="setting" @click="handleSet" />
        </span>
      </div>
      <Form ref="alarmForm" v-loading="loadingData" :model="temp" label-position="left" label-width="70px" :extra-width="{en: 30}" style="height: 200px;display: flex; flex-direction: column;">
        <fieldset>
          <legend>{{ $t('layout.conventional') }}</legend>
          <el-row v-if="temp.bizCode == '101'">
            <el-col :span="11">
              <FormItem :label="$t('table.terminalName')">
                <span :title="temp.terminalName">{{ temp.terminalName }}</span>
              </FormItem>
            </el-col>
            <el-col :span="13">
              <FormItem :label="$t('table.user')">
                <span :title="temp.userName">{{ temp.userName }}</span>
              </FormItem>
            </el-col>
            <el-col :span="11">
              <FormItem :label="$t('layout.computerName')">
                <span :title="temp.computerName">{{ temp.computerName }}</span>
              </FormItem>
            </el-col>
            <el-col :span="13">
              <FormItem :label="$t('layout.alarmSource')">
                <span :title="temp.alarmTypeName">{{ temp.alarmTypeName }}</span>
              </FormItem>
            </el-col>
            <el-col :span="11">
              <FormItem :label="$t('layout.alarmType')">
                <span :title="temp.sstTypeName">{{ temp.sstTypeName }}</span>
              </FormItem>
            </el-col>
            <el-col :span="13">
              <FormItem :label="$t('layout.alarmLevel')">
                <span :title="temp.alarmLevelName">{{ temp.alarmLevelName }}</span>
              </FormItem>
            </el-col>
            <el-col :span="11">
              <FormItem :label="$t('layout.action')">
                <span :title="temp.actionName">{{ temp.actionName }}</span>
              </FormItem>
            </el-col>
            <el-col :span="13">
              <FormItem :label="$t('layout.alarmTime')">
                <span :title="temp.createTime">{{ temp.createTime }}</span>
              </FormItem>
            </el-col>
          </el-row>
          <service-status v-else-if="temp.bizCode == '3'" :row-data="temp"/>
          <el-row v-else-if="temp.formDTOs">
            <el-col v-for="(item, index) in temp.formDTOs" :key="index" :span="item.span">
              <FormItem :label="item.label">
                <span :title="item.value">{{ item.value }}</span>
              </FormItem>
            </el-col>
          </el-row>
          <el-row v-else-if="temp.bizCode != '101'">
            <el-col :span="11">
              <FormItem :label="$t('layout.alarmSource')">
                <span :title="temp.alarmTypeName">{{ temp.alarmTypeName }}</span>
              </FormItem>
            </el-col>
            <el-col :span="13">
              <FormItem :label="$t('layout.alarmTime')">
                <span :title="temp.createTime">{{ temp.createTime }}</span>
              </FormItem>
            </el-col>
            <el-col v-show="invalid" :span="11">
              <FormItem label="消息状态">
                <span title="消息已失效" style="color: red;">{{ '该消息已失效' }}</span>
              </FormItem>
            </el-col>
          </el-row>
        </fieldset>
        <fieldset class="detail-fieldset">
          <legend>{{ $t('text.details') }}</legend>
          <!-- <p :style="active" @mouseover="over" @mouseleave="leave" @click="goToDetail">{{ temp.msg }}</p> -->
          <p>{{ formatMsg(temp.msg) }}</p>
        </fieldset>
      </Form>
      <div slot="footer" class="dialog-footer">
        <div class="btn-group">
          <el-button type="primary" :disabled="loadingData || currentIndex<=1" @click="first">|&lt;</el-button>
          <el-button type="primary" :disabled="loadingData || currentIndex<=1" @click="prev">&lt;</el-button>
          {{ currentIndex }}
          <el-button type="primary" :disabled="loadingData || currentIndex>=totalNum" @click="next">&gt;</el-button>
          <el-button type="primary" :disabled="loadingData || currentIndex>=totalNum" @click="last">&gt;|</el-button>
          {{ $t('layout.total1', { total: totalNum }) }}
        </div>
        <el-tooltip class="item" effect="dark" placement="top" :content="$t('text.details') + (temp.hasChild && temp.total > 1 ? `(${temp.total})` : '')">
          <el-button type="primary" :disabled="loadingData" @click="goToDetail">
            <svg-icon icon-class="view-detail"></svg-icon>{{ temp.hasChild && temp.total > 1 ? `(${temp.total})` : '' }}
          </el-button>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" placement="top" :content="$t('layout.ignoreAll')">
          <el-button type="primary" :disabled="loadingData" icon="el-icon-close-notification" @click="ignoreAll"></el-button>
        </el-tooltip>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="settingDlgVisible"
      width="400px"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('layout.alarmPromptSetting') }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('layout.alarmPromptMsg1') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form ref="settingForm" :model="settingTemp" label-position="right" label-width="20px" :extra-width="{en: -20}">
        <FormItem prop="interval">
          <i18n path="layout.alarmPromptMsg2">
            <el-input-number slot="minute" v-model="settingTemp.interval" :controls="false" :step="1" step-strictly :max="999" :min="0" style="width: 70px;" @blur="blurinterval"></el-input-number>
          </i18n>
        </FormItem>
        <FormItem prop="times">
          <i18n path="layout.alarmPromptMsg3">
            <el-input-number slot="count" v-model="settingTemp.times" :controls="false" :step="1" step-strictly :max="999" :min="0" style="width: 70px;" @blur="blurtimes"></el-input-number>
          </i18n>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="() => { saveSetting(); settingDlgVisible = false }">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="settingDlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.alarmDetails')"
      :visible.sync="detailDlgVisible"
      width="1000px"
    >
      <grid-table
        ref="gridTableList"
        row-key="id"
        :multi-select="false"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :height="400"
        :after-load="afterLoad"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getAlarmIds, getAlarmMsg, dealAlarmMsg, dealAllAlarmMsg, getAlarmMsgList } from '@/api/behaviorAuditing/alarmDetailLog'
import { getDictLabel, getSeverityDict, getActionDict, getSstTypeDict, getAlarmBusDict } from '@/utils/dictionary'
import { insertPersonalization, getValueByCondition } from '@/api/user'
import moment from 'moment'
import ServiceStatus from '@/layout/components/AlarmPrompt/serviceStatus';
import { getAlarmLevelDict } from '@/api/system/baseData/alarmLevel'

export default {
  name: 'AlarmPrompt',
  components: { ServiceStatus },
  directives: {
    dragAlarm: {
      // 指令的定义
      bind(el, binding, vnode) {
        const dragDom = el
        const dialogHeaderEl = el.querySelector('.el-dialog__header')
        dialogHeaderEl.style.cssText += ';cursor:move;'

        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
        const getStyle = (function() {
          if (document.body.currentStyle) {
            return (dom, attr) => dom.currentStyle[attr]
          } else {
            return (dom, attr) => getComputedStyle(dom, false)[attr]
          }
        })()

        dialogHeaderEl.onmousedown = (e) => {
          // 鼠标按下，计算当前元素距离可视区的距离
          const disX = e.clientX - dialogHeaderEl.offsetLeft
          const disY = e.clientY - dialogHeaderEl.offsetTop

          const dragDomWidth = dragDom.offsetWidth
          const dragDomHeight = dragDom.offsetHeight

          // 此处以body的宽、高为边界
          const screenWidth = document.body.clientWidth
          const screenHeight = document.body.clientHeight

          const minDragDomLeft = dragDom.offsetLeft
          const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth

          const minDragDomTop = dragDom.offsetTop
          const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomHeight

          // 获取到的值带px 正则匹配替换
          let styL = getStyle(dragDom, 'left')
          let styT = getStyle(dragDom, 'top')

          if (styL == 'auto') {
            styL = screenWidth - dragDomWidth - getStyle(dragDom, 'right').replace(/\px/g, '')
            styT = screenHeight - dragDomHeight - getStyle(dragDom, 'bottom').replace(/\px/g, '')
          } else if (styL.includes('%')) {
            styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100)
            styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100)
          } else {
            styL = +styL.replace(/\px/g, '')
            styT = +styT.replace(/\px/g, '')
          }

          document.onmousemove = function(e) {
            // 通过事件委托，计算移动的距离
            let left = e.clientX - disX
            let top = e.clientY - disY

            // 边界处理
            if (-(left) > minDragDomLeft) {
              left = -minDragDomLeft
            } else if (left > maxDragDomLeft) {
              left = maxDragDomLeft
            }

            if (-(top) > minDragDomTop) {
              top = -minDragDomTop
            } else if (top > maxDragDomTop && maxDragDomTop >= 0) { // 当dialog的下边框处于可视范围内时，才进行边界处理
              top = maxDragDomTop
            }

            // 移动当前元素
            dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`

            // emit onDrag event
            vnode.child.$emit('dragDialog')
          }

          document.onmouseup = function(e) {
            document.onmousemove = null
            document.onmouseup = null
          }
        }
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'alarmTypeName', label: 'alarmSource', width: '100', fixed: true },
        { prop: 'sstTypeName', label: 'alarmType', width: '100', formatter: this.sstTypeNameFormatter },
        { prop: 'actionName', label: 'action', width: '50', formatter: this.actionNameFormatter },
        { prop: 'alarmLevelName', label: 'alarmLevel', width: '50', formatter: this.alarmLevelNameFormatter },
        { prop: 'createTime', label: 'alarmTime', width: '100' },
        { prop: 'msg', label: 'details', width: '200' }
      ],
      userId: '',
      dialogVisible: false,
      temp: {
        bizCode: '',
        computerName: '',
        termId: '',
        alarmTypeName: '',
        sstTypeName: '',
        alarmLevelName: '',
        actionName: '',
        createTime: ''
      },
      showDelay: false, // 是否勾选短期不显示
      settingDlgVisible: false,
      detailDlgVisible: false,
      settingTemp: {
        interval: 10,
        times: 10,
        showTime: null
      },
      query: { // 查询条件
        page: 1
      },
      alarmLevelDict: [],
      severityDict: [],
      actionDict: [],
      sstTypeDict: [],
      alarmBusDict: [],
      alarmDataIds: [], // 从后台获取到的未读消息id
      readNum: 0,  // 已读消息数量
      readMap: {}, // 已读消息的id
      alarmDataMap: {}, // 告警消息map
      currentIndex: null,
      currentId: null,
      direction: 1, // 加载数据的方向，1向后 -1向前
      totalNum: 0, // 从弹窗开始计算，已读+未读消息的数量
      igAll: false, // 是否忽略全部
      loadingData: false, // 是否正在请求告警消息
      submitting: false,
      alarmMsgIdsGetTime: 0, // 上一次获取控制台告警信息的时间
      active: 'white-space: pre-wrap;',
      deletedMsgIds: [], // 已失效告警消息ID
      invalid: false,
      // 详情跳转的信息
      // routeName: 路由名
      // judgeFunc判断当前控制台弹窗的消息对应为哪个detailInfo
      // permissionCode判断是否有跳转后页面的权限
      // params 跳转携带的参数
      // noticeMsg 通知列表刷新的标识（由各日志组件进行监听）
      // noPermissionMsg 跳转后无权限的提示说明
      goDetailInfos: [
        // 征兆报表推送
        {
          routeName: 'SignReportPushLog',
          judgeFunc: (detail) => detail.bizCode == '5',
          permissionCode: 'H93',
          params: (detail) => { return { id: detail.bizId, time: new Date().getTime() } },
          noticeMsg: 'generalAlarmDetail',
          noPermissionMsg: this.$t('pages.noSignReportPushLogPermission')
        },
        // {
        //   // 服务器告警记录已不用这个页面，整合至系统高级记录
        //   routeName: 'ServiceAlarmLog',
        //   judgeFunc: (detail) => detail.bizCode == '3',
        //   permissionCode: 'H14',
        //   params: (detail) => { return { id: detail.bizId, createTime: detail.createTime } },
        //   noticeMsg: 'serviceAlarmDetail',
        //   noPermissionMsg: this.$t('pages.noServiceAlarmLogPermission')
        // },
        {
          routeName: 'AlarmDetailLog',
          judgeFunc: (detail) => detail.param.guid && detail.param.sstType === 0,
          permissionCode: 'H91',
          params: (detail) => { return { guid: detail.param.guid, taskGuid: detail.param.taskGuid, createDate: detail.createTime.slice(0, 10), hasChild: detail.hasChild ? 1 : 0, taskId: detail.taskId } },
          noticeMsg: 'generalAlarmDetail',
          noPermissionMsg: this.$t('pages.ordinaryAlarmRecordsMenu')
        },
        {
          routeName: 'SensitiveAlarmLog',
          judgeFunc: (detail) => detail.param.guid && detail.param.sstType === 1,
          permissionCode: 'F22',
          params: (detail) => { return { guid: detail.param.guid, taskGuid: detail.param.taskGuid, createDate: detail.createTime.slice(0, 10), hasChild: detail.hasChild ? 1 : 0, taskId: detail.taskId } },
          noticeMsg: 'sensitiveAlarmDetail',
          noPermissionMsg: this.$t('pages.sensitiveAlarmLogMenu')
        },
        {
          routeName: 'SysAlarmLog',
          judgeFunc: (detail) => true,
          permissionCode: 'H92',
          params: (detail) => { return { id: detail.param.id, createDate: detail.createTime.slice(0, 10), unionSql: false, bizType: detail.bizCode == 3 ? detail.bizCode : undefined } },
          noticeMsg: 'sysAlarmDetail',
          noPermissionMsg: this.$t('pages.systemLogMenu')
        }
      ]
    }
  },
  computed: {
  },
  watch: {
    '$store.state.commonData.notice.showAlarm'(val) {
      this.showAlarmDlg(true)
    },
    async '$store.state.commonData.notice.refreshAlarm'() {
      // 告警等级
      await this.getAlarmLevelDictDec()
      if (this.temp.param.sstType === 0) {
        this.$set(this.temp, 'alarmLevelName', getDictLabel(this.alarmLevelDict, this.temp.param.alarmLevel))
      } else {
        this.$set(this.temp, 'alarmLevelName', getDictLabel(this.severityDict, this.temp.param.alarmLevel))
      }
      this.$forceUpdate()
    },
    alarmDataIds: {
      deep: true,
      handler(val) {
        if (this.currentId) {
          this.currentIndex = val.indexOf(this.currentId) + 1
        }
        this.totalNum = val.length
        const unreadNum = this.totalNum - this.readNum
        this.$store.dispatch('commonData/changeWsNoticeInfo', { alarmNotic: unreadNum })
      }
    },
    readNum(val) {
      const unreadNum = this.totalNum - val
      this.$store.dispatch('commonData/changeWsNoticeInfo', { alarmNotic: unreadNum })
    },
    currentIndex(val) {
      if (!val) return
      const key = val - 1
      const id = this.alarmDataIds[key]
      if (this.currentId == id) return
      this.currentId = id
      if (this.alarmDataMap[id]) {
        this.showData(id)
        const nextId = this.alarmDataIds[key + this.direction]
        if (!this.alarmDataMap[nextId]) {
          this.getAlarmMsgsByIndex(key)
        }
      } else {
        this.loadingData = true
        this.getAlarmMsgsByIndex(key)
      }
      // 验证当前告警消息是否已失效
      if (this.deletedMsgIds.length > 0 && this.deletedMsgIds.indexOf(Number(this.currentId)) > -1) {
        this.invalid = true
      } else {
        this.invalid = false
      }
    },
    dialogVisible(val) {
      if (val) {
        this.currentIndex = 1
      }
    }
  },
  async created() {
    // 获取告警弹窗设置
    this.userId = this.$store.getters.userId
    // this.settingTemp = JSON.parse(localStorage.getItem('alarmSetting') || '{}')[this.account] || this.settingTemp
    // this.showDelay = !!localStorage.getItem('showDelay')
    this.getSettingData()

    // 告警等级
    await this.getAlarmLevelDictDec()
    // 严重程度
    this.severityDict = getSeverityDict()
    // 执行动作
    this.actionDict = getActionDict()
    // 敏感检测类型等级
    this.sstTypeDict = getSstTypeDict()
    // 普通告警类型
    this.alarmBusDict = getAlarmBusDict()
    // 获取未读告警消息ids
    this.getAlarmMsgIds()
    this.$socket.subscribe({ url: '/topic/alarm', callback: (respond, handle) => {
      this.getAlarmMsgIds()
    } })
    this.deletedMsgIds = []
    this.$socket.subscribe({ url: '/topic/alarm/delete', callback: (respond, handle) => {
      respond.data.forEach(item => {
        this.deletedMsgIds.push(item)
      })
    } })
  },
  methods: {
    async getAlarmLevelDictDec() {
      await getAlarmLevelDict().then(res => {
        const dict = res.data.map(item => {
          return { value: item.level, label: item.levelName }
        })
        this.alarmLevelDict.splice(0, dict.length, ...dict)
      })
    },
    over() {
      this.active = 'white-space: pre-wrap; color: blue; cursor: pointer;'
    },
    leave() {
      this.active = 'white-space: pre-wrap;'
    },
    saveSetting(showDelay) {
      if (showDelay) {
        const date = new Date()
        date.setMinutes(date.getMinutes() + this.settingTemp.interval)
        this.settingTemp.showTime = moment(date).format('YYYY/MM/DD HH:mm:ss')
        const value = JSON.stringify(this.settingTemp)
        insertPersonalization({ sysUserId: this.userId, value: value, type: 3 }).then(response => {
          this.settingDlgVisible = false
          this.submitting = false
        }).catch(res => {
          this.submitting = false
        })
      }
    },
    getSettingData() {
      getValueByCondition({ sysUserId: this.userId, type: 3 }).then(res => {
        if (res.data) {
          const temp = JSON.parse(res.data.value)
          this.showDelay = new Date(temp.showTime) > new Date()
          Object.assign(this.settingTemp, temp)
        }
      })
    },
    resetData() {
      this.direction = 1
      this.currentIndex = null
      this.currentId = null
      this.readNum = 0
      this.alarmDataMap = {}
      if (this.igAll) {
        this.alarmDataIds = []
        this.igAll = false
      } else {
        this.alarmDataIds = this.alarmDataIds.filter(id => !this.readMap[id])
      }
      this.readMap = {}
    },
    first() {
      this.gotoPage('first')
    },
    prev() {
      this.gotoPage('prev')
    },
    next() {
      this.gotoPage('next')
    },
    last() {
      this.gotoPage('last')
    },
    gotoPage(type) {
      this.deleteAlarmMsgData(this.temp)
      this.direction = { first: 1, prev: -1, next: 1, last: -1 }[type]
      const temp = { first: 1 - this.currentIndex, prev: -1, next: 1, last: this.totalNum - this.currentIndex }[type]
      this.currentIndex += temp
    },
    // 获取所有未读告警消息id
    getAlarmMsgIds() {
      if (new Date().getTime() - this.alarmMsgIdsGetTime < 1000) {
        // 如果上一次请求的时间还比较短，则不发送请求
        return;
      }
      this.alarmMsgIdsGetTime = new Date().getTime()
      getAlarmIds().then(res => {
        this.alarmDataIds = [...new Set(this.alarmDataIds.concat(res.data))]
        const totalNum = this.alarmDataIds.length
        if (totalNum != this.totalNum) {
          this.totalNum = totalNum
          this.showAlarmDlg()
        }
        // 验证当前告警消息是否已失效
        if (this.deletedMsgIds.length > 0 && this.deletedMsgIds.indexOf(Number(this.currentId)) > -1) {
          this.invalid = true
        } else {
          this.invalid = false
        }
      })
    },
    // 通过告警消息id数组的index获取10条数据
    getAlarmMsgsByIndex(index) {
      let ids
      if (this.direction == 1) { // 获取index后10条数据（包括index）
        const target = index + 10 * this.direction
        ids = this.alarmDataIds.slice(index, target)
      } else if (this.direction == -1) { // 获取index前10条数据（包括index）
        index = index + 1
        let target = index + 10 * this.direction
        target = target >= 0 ? target : 0
        ids = this.alarmDataIds.slice(target, index)
      }
      this.getAlarmMsgByIds(ids)
    },
    // 通过id获取告警消息的详情
    getAlarmMsgByIds(ids) {
      const update = !this.alarmDataMap[this.currentId]
      getAlarmMsg({ msgIds: ids }).then(res => {
        this.loadingData = false
        res.data.forEach(msg => {
          this.alarmDataMap[msg.id] = msg
        })
        if (update) {
          this.showData(this.currentId)
        }
      })
    },
    // handleShow 是否手动触发显示弹窗
    showAlarmDlg(handleShow = false) {
      const unread = this.totalNum - this.readNum
      // 告警弹窗未弹出，且有未读消息
      if (!this.dialogVisible && unread > 0) {
        // 有设置短期不提示
        if (this.showDelay) {
          const { showTime, times } = this.settingTemp
          // 超出设置的时间或未读消息数
          const isShowable = new Date() > new Date(showTime) || unread >= times
          this.dialogVisible = isShowable || handleShow
          this.showDelay = !this.dialogVisible
        } else {
          this.dialogVisible = true
        }
      }
    },
    // 显示指定id的告警消息详情
    async showData(id) {
      const data = this.alarmDataMap[id]
      this.currentId = id
      if (!data) return
      this.temp = Object.assign({}, data)
      if (data.param) {
        const { computerName, terminalId, action, sstType, alarmLevel, alarmBus } = data.param
        this.temp.computerName = computerName
        this.temp.termId = terminalId
        this.temp.actionName = getDictLabel(this.actionDict, action)
        this.temp.sstTypeName = alarmBus ? getDictLabel(this.alarmBusDict, alarmBus) : getDictLabel(this.sstTypeDict, sstType)
        if (this.alarmLevelDict.length == 0) {
          await this.getAlarmLevelDictDec()
        }
        if (sstType === 0) {
          this.temp.alarmLevelName = getDictLabel(this.alarmLevelDict, alarmLevel)
        } else {
          this.temp.alarmLevelName = getDictLabel(this.severityDict, alarmLevel)
        }
      }
    },
    formatMsg(msg) {
      // 部分模板使用 <br> 换行，替换为 \r\n，如果需要使用标签，则可以修改为 v-html + this.html2Escape
      if (msg) {
        return msg.replaceAll('<br>', '\r\n')
      }
      return msg
    },
    // 将传入的消息标记为已读
    deleteAlarmMsgData(data) {
      if (this.readMap[data.id]) return
      const { id, alarmLimit, dealStatus } = data
      if (id) {
        dealAlarmMsg({ id, alarmLimit, dealStatus })
        this.readMap[id] = true
        this.readNum++
      }
    },
    goToDetail() {
      const paramObj = this.goDetailInfos.find(info => info.judgeFunc(this.temp))
      if (paramObj) {
        if (this.hasPermission(paramObj['permissionCode'])) {
          const params = {}
          if (typeof paramObj.params === 'function') {
            Object.assign(params, paramObj.params(this.temp))
          } else if (typeof paramObj.params === 'string') {
            params[paramObj.params] = this.temp[paramObj.params]
          } else if (Array.isArray(paramObj.params)) {
            paramObj.params.forEach(key => { params[key] = this.temp[key] })
          }
          if (this.$route.name == paramObj['routeName']) {
            this.$store.dispatch('commonData/setAlarmMsg', params)
            // 通知列表刷新
            this.$store.dispatch('commonData/changeNotice', paramObj['noticeMsg'])
          } else {
            if (this.temp.bizCode == '5') {
              this.$router.push({ name: 'SignReportPushLog', params: { id: this.temp.bizId }})
              this.$store.dispatch('commonData/setAlarmMsg', params)
              this.$store.dispatch('commonData/changeNotice', 'generalAlarmDetail')
            } else {
              this.$router.push({ name: paramObj['routeName'], params })
            }
          }
        } else {
          this.$message({
            message: paramObj['noPermissionMsg'],
            type: 'error',
            duration: 2000
          })
        }
        return
      }
    },
    handleDetail() {
      this.detailDlgVisible = true
      this.$refs['gridTableList'] && this.$refs['gridTableList'].execRowDataApi()
    },
    rowDataApi(option) {
      const newOption = Object.assign(this.query, option)
      const { taskId } = this.temp
      const searchQuery = Object.assign({ msgIds: [taskId] }, newOption)
      return getAlarmMsgList(searchQuery)
    },
    afterLoad(rowData, grid) {
      // if (rowData) {
      //   rowData.forEach(data => {
      //     this.$set(data, 'sstTypeName', getDictLabel(this.sstTypeDict, data.param.sstType))
      //     this.$set(data, 'actionName', getDictLabel(this.actionDict, data.param.action))
      //     this.$set(data, 'alarmLevelName', data.param.sstType === 0 ? getDictLabel(this.alarmLevelDict, data.param.alarmLevel) : getDictLabel(this.severityDict, data.param.alarmLevel))
      //   })
      // }
    },
    // 忽略所有消息，将所有消息标记为已读
    ignoreAll() {
      dealAllAlarmMsg().then(respond => {
        this.igAll = true
        this.dialogVisible = false
      })
    },
    closeDlg() {
      this.saveSetting(this.showDelay)
      this.dialogVisible = false
    },
    closed() {
      const dom = document.querySelector('.alarm-prompt')
      dom.style.cssText += `;top:auto;left:auto;`
      if (!this.igAll) {
        this.deleteAlarmMsgData(this.temp)
      }
      this.resetData()
    },
    handleSet() {
      this.settingDlgVisible = true
    },
    blurinterval(e, val) {
      if (!e.target.value) {
        this.settingTemp.interval = 0
      }
    },
    blurtimes(e, val) {
      if (!e.target.value) {
        this.settingTemp.times = 0
      }
    },
    sstTypeNameFormatter: function(row, data) {
      if (data) {
        return data
      }
      return row.param.sstType != null ? getDictLabel(this.sstTypeDict, row.param.sstType) : getDictLabel(this.sstTypeDict, 99)
    },
    actionNameFormatter: function(row, data) {
      if (data) {
        return data
      }
      return row.param.action != null ? getDictLabel(this.actionDict, row.param.action) : getDictLabel(this.actionDict, 3)
    },
    alarmLevelNameFormatter: function(row, data) {
      const dict = !row.param.sstType ? this.alarmLevelDict : this.severityDict
      return row.param.alarmLevel != null ? getDictLabel(dict, row.param.alarmLevel) : getDictLabel(dict, 1)
    }
  }
}
</script>

<style lang='scss' scoped>
.alarm-prompt{
  width: 400px;
  height: 283px;
  top: auto;
  left: auto;
  bottom: 5px;
  right: 10px;
  border-radius: 4px 4px 0 0;
  >>>.el-dialog{
    margin-top: 0 !important;
    .el-dialog__header{
      padding-right: 20px;
    }
    .el-dialog__title {
      display: unset;
    }
    .el-dialog__body{
      height: 214px;
      padding: 5px;
      border: 1px solid #888;
      border-top: 0;
      border-bottom: 0;
      .el-form{
        .el-form-item {
          max-width: 372px;
        }
        .el-form-item__label, .el-form-item__content{
          line-height: 18px;
        }
        .el-form-item__content{
          font-size: 13px;
          span{
            word-break:keep-all;
            text-overflow:ellipsis;
            overflow:hidden;
            white-space: nowrap;
            display: block;
            width: 100%;
          }
        }
      }
    }
    .el-dialog__footer{
      height: 35px;
      padding: 5px;
      border: 1px solid #888;
      border-top: 0;
      .btn-group {
        position: absolute;
        left: 10px;
        color: black;
        font-size: 15px;
        line-height: 24px;
      }
      .el-button{
        padding: 5px;
        vertical-align: bottom;
        margin-left: 5px;
        float: none;
      }
    }
    fieldset{
      padding: 0.35em 0.5em 0.625em;
      border: 1px solid #888;
    }
    .detail-fieldset {
      min-height: 0;
      flex: 1;
    }
    p{
      height: 100%;
      margin: 0;
      padding: 0 10px;
      overflow: auto;
      white-space: pre-wrap;
    }
  }
  .delay {
    margin-right: 25px;
    float: right;
    color: #666;
    .svg-icon{
      cursor: pointer;
    }
    >>>.el-checkbox__label {
      padding-left: 5px;
      color: #666;
    }
  }
}
</style>
