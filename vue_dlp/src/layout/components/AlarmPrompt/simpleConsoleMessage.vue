<template>
  <div style="height: 200px; display: flex; flex-direction: column">
    <fieldset>
      <legend>{{ $t('layout.conventional') }}</legend>
      <el-row>
        <el-col :span="11">
          <FormItem :label="$t('layout.alarmSource')">
            <span :title="getAlarmSource()">{{ getAlarmSource() }}</span>
          </FormItem>
        </el-col>
        <el-col :span="13">
          <FormItem :label="$t('layout.alarmTime')">
            <span :title="messageObj.createTime">{{ messageObj.createTime }}</span>
          </FormItem>
        </el-col>
      </el-row>
    </fieldset>
    <fieldset style="flex: 1; min-height: 0;">
      <legend>{{ $t('text.details') }}</legend>
      <p style="height: 100%; white-space: pre-wrap; overflow: auto;">{{ messageObj.eventDesc }}</p>
    </fieldset>
  </div>
</template>

<script>
export default {
  name: 'SimpleConsoleMessage',
  props: {
    messageObj: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  methods: {
    getAlarmSource() {
      const contentType = this.messageObj.contentType
      let source = ''
      if (contentType) {
        // contentType 要与后端的MessageTypeDict字典一致
        switch (contentType) {
          case 1: source = ''; break;
        }
      }
      return source
    }
  }
}
</script>

<style scoped>

</style>
