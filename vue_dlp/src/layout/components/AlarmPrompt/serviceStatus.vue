<template>
  <div>
    <el-row v-show="rowData.devType">
      <el-col :span="12">
        <FormItem :label="$t('table.serviceType')" label-width="76px">
          <span style="width: 100%" :title="rowData.devType">{{ rowData.devType }}</span>
        </FormItem>
      </el-col>
      <el-col :span="12">
        <FormItem :label="$t('table.serviceNickName')" label-width="76px">
          <span style="width: 100%" :title="rowData.devName">{{ rowData.devName }}</span>
        </FormItem>
      </el-col>
      <el-col :span="12">
        <FormItem :label="$t('layout.alarmSource')" label-width="76px">
          <span style="width: 100%" :title="rowData.alarmSource">{{ rowData.alarmSource }}</span>
        </FormItem>
      </el-col>
      <el-col :span="12">
        <FormItem :label="$t('layout.alarmType')" label-width="76px">
          <span style="width: 100%" :title="rowData.alarmType">{{ rowData.alarmType }}</span>
        </FormItem>
      </el-col>
      <el-col :span="24">
        <FormItem :label="$t('layout.alarmTime')" label-width="76px">
          <span :title="rowData.alarmTime">{{ rowData.alarmTime }}</span>
        </FormItem>
      </el-col>
    </el-row>
    <el-row v-show="!rowData.devType">
      <el-col :span="12">
        <FormItem :label="$t('layout.alarmSource')" >
          <span style="width: 100%" :title="rowData.alarmSource">{{ rowData.alarmSource }}</span>
        </FormItem>
      </el-col>
      <el-col :span="12">
        <FormItem :label="$t('layout.alarmType')">
          <span style="width: 100%" :title="rowData.alarmType">{{ rowData.alarmType }}</span>
        </FormItem>
      </el-col>
      <el-col v-if="rowData.terminalName" :span="24">
        <FormItem :label="$t('pages.terminalName')">
          <span style="width: 100%" :title="rowData.terminalName">{{ rowData.terminalName }}</span>
        </FormItem>
      </el-col>
      <el-col :span="24">
        <FormItem :label="$t('layout.alarmTime')">
          <span style="width: 100%" :title="rowData.alarmTime">{{ rowData.alarmTime }}</span>
        </FormItem>
      </el-col>
      <div v-if="rowData.curOfflineSum">
        <el-col :span="12">
          <FormItem :label="`终端离线台数阈值`" label-width="126px">
            <span style="width: 100%" :title="rowData.offlineSum">{{ rowData.offlineSum }}</span>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="`当前终端离线台数`" label-width="124px">
            <span style="width: 100%" :title="rowData.curOfflineSum">{{ rowData.curOfflineSum }}</span>
          </FormItem>
        </el-col>
        <el-col v-if="rowData.offlineRate" :span="12">
          <FormItem :label="`终端离线率阈值`" label-width="124px">
            <span style="width: 100%" :title="rowData.offlineRate">{{ rowData.offlineRate }}</span>
          </FormItem>
        </el-col>
        <el-col v-if="rowData.curOfflineRate" :span="12">
          <FormItem :label="`当前终端离线率`" label-width="124px">
            <span style="width: 100%" :title="rowData.curOfflineRate">{{ rowData.curOfflineRate }}</span>
          </FormItem>
        </el-col>
        <el-col :span="24">
          <FormItem :label="`检测范围：`" label-width="84px"></FormItem>
        </el-col>
        <el-col v-if="rowData.termGroup" :span="24">
          <FormItem :label="`终端组`">
            <span style="width: 100%" :title="rowData.termGroup">{{ rowData.termGroup }}</span>
          </FormItem>
        </el-col>
        <el-col v-if="rowData.terms" :span="24">
          <FormItem :label="`终端`">
            <span style="width: 100%" :title="rowData.terms">{{ rowData.terms }}</span>
          </FormItem>
        </el-col>
      </div>
    </el-row>
  </div>
</template>

<script>

export default {
  name: 'ServiceStatus',
  props: {
    rowData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  methods: {

  }
}
</script>

<style scoped>

</style>
