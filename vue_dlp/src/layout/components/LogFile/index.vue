<template>
  <div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-folder" size="mini" @click="logDirVisible=true">
          {{ $t('pages.serverlog_directory_config' ) }}
        </el-button>
        <el-button icon="el-icon-download" size="mini" :loading="downloadSelectedLoading" :disabled="selectedFiles.length === 0" @click="downloadSelectedFile">
          {{ $t('pages.serverlog_package_download') }}
        </el-button>
        <div class="searchCon" style="float: right">
          <el-select v-model="queryType" style="width: 110px;top: -2px;" @change="resetQuery">
            <el-option value="name" :label="$t('pages.fileName')"/>
            <el-option value="dir" :label="$t('pages.serverlog_file_directory')"/>
            <el-option value="date" :label="$t('pages.serverlog_file_date')"/>
          </el-select>
          <el-input v-if="queryType === 'dir'" v-model="query.dir" v-trim clearable :placeholder="$t('pages.serverlog_file_directory')" style="width: 150px;top: -2px;" @keyup.enter.native="handleFilter"/>
          <el-input v-if="queryType === 'name'" v-model="query.name" v-trim clearable :placeholder="$t('pages.fileName')" style="width: 150px;top: -2px;" @keyup.enter.native="handleFilter"/>
          <el-date-picker v-if="queryType === 'date'" v-model="query.date" style="top: -2px;" clearable type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :picker-options="pickerOptions" :placeholder="$t('pages.serverlog_file_date')"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>

      <grid-table
        ref="fileList"
        row-key="name"
        :height="390"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        pager-small
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      append-to-body
      :title="$t('pages.serverlog_directory_config' )"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="logDirVisible"
      width="800px"
    >
      <log-dir ref="logDirTable" :selection.sync="dirSelection"/>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="userDirUpdating" type="primary" @click="handleUserDirUpdate">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="logDirVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      append-to-body
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="previewVisible"
      width="800px"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('pages.serverlog_preview') }}：
        <div style="position: absolute;top: 7px;left: 100px;font-size: small;width: 650px;text-overflow: ellipsis;overflow: hidden;" :title="previewFileName">{{ previewFileName }}</div>
      </div>
      <div style="min-height: 450px;">
        <div v-for="(item, index) in previewFileContent" :key="index" style="line-height: 25px;">
          {{ item }}
        </div>
        <el-button v-if="previewMore" type="warning" plain size="mini" style="display: block;margin: auto;" @click="loadPreviewLog">
          {{ $t('text.loadMore') }} <i class="el-icon-more" />
        </el-button>
        <el-button v-else disabled size="mini" style="display: block;margin: auto;">{{ $t('text.noMore') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLogFilePage, getLogFileChildren, previewLogFile, downloadLogFile, downloadZipLogFile } from '@/api/logFile';
import LogDir from './LogDir'

export default {
  name: 'LogFile',
  components: { LogDir },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const buttons = [
      { label: this.$t('pages.overview'), disabled: row => row.folded, click: this.handleFilePreview },
      { label: this.$t('table.download'), disabled: row => row.folded, click: this.handleFileDownload },
      { label: this.$t('table.compress'), click: this.handleFileZipDownload }
    ]
    return {
      rowButtons: buttons,
      colModel: [
        { prop: 'name', label: 'fileName', width: '175', sort: true, iconFormatter: () => { return 'file' } },
        { prop: 'dir', label: this.$t('pages.serverlog_file_directory'), width: '175', sort: true },
        { prop: 'date', label: this.$t('pages.serverlog_file_date'), width: '100', sort: true },
        { prop: 'size', label: this.$t('table.maxFileSize2'), width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '125', buttons: buttons }
      ],
      query: {
        page: 1,
        name: '',
        date: '',
        dir: ''
      },
      queryType: 'name',
      dirSelection: [],
      userDirUpdating: false,
      downloadSelectedLoading: false,
      selectedFiles: [],
      dialogVisible: this.visible,
      logDirVisible: false,
      previewVisible: false,
      previewMore: true,
      previewStartLine: 1,
      previewFileName: '',
      previewFileContent: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: this.$t('text.today'),
            onClick(picker) {
              picker.$emit('pick', new Date());
            }
          },
          {
            text: this.$t('text.yesterday'),
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit('pick', date);
            }
          },
          {
            text: this.$t('text.aWeekAgo'),
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', date);
            }
          }
        ]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['fileList']
    },
    logDirTable() {
      return this.$refs['logDirTable']
    }
  },
  watch: {
    visible(value) {
      this.dialogVisible = value
    }
  },
  methods: {
    resetQuery() {
      this.query = {
        page: 1,
        name: '',
        date: '',
        dir: ''
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable && this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getLogFilePage(searchQuery)
    },
    childrenLoadApi(option) {
      return getLogFileChildren(option).then(response => response.data.filter(item => {
        if (this.query.name.trim().length && item.name.indexOf(this.query.name.trim()) < 0) {
          return false
        }
        if (!item.folded) {
          delete item.folded
        }
        return this.query.date.trim() === '' || item.date === this.query.date.trim();
      }))
    },
    loadPreviewLog() {
      previewLogFile({ name: this.previewFileName, start: this.previewStartLine }).then(respond => {
        this.previewMore = !respond.data.finished
        this.previewStartLine += respond.data.limit
        this.previewFileContent.push(...respond.data.content)
      })
    },
    handleFilePreview(row) {
      this.previewVisible = true
      this.previewFileName = row.path
      this.previewStartLine = 1
      this.previewFileContent.length = 0
      this.loadPreviewLog()
    },
    handleFileDownload(row) {
      downloadLogFile(row)
    },
    handleFileZipDownload(row) {
      downloadZipLogFile([row.path])
    },
    selectionChangeEnd(rows) {
      this.selectedFiles = rows
    },
    downloadSelectedFile() {
      this.downloadSelectedLoading = true
      downloadZipLogFile(this.selectedFiles.map(file => file.path))
        .then(_ => {
          this.downloadSelectedLoading = false
        })
        .catch(_ => {
          this.downloadSelectedLoading = false
        })
    },
    handleUserDirUpdate() {
      this.userDirUpdating = true
      this.logDirTable.handleUserDirUpdate(this.dirSelection).then(_ => {
        this.handleFilter()
        this.userDirUpdating = false
        this.logDirVisible = false
      }).catch(_ => {
        this.userDirUpdating = false
        this.logDirVisible = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .dialog-footer {
    .remark-label {
      color: #666666;
      position: relative;
      float: left;
      top: 5px;
      .remark-keyword {
        color: #409eff;
      }
    }
  }
</style>
