<template>
  <div :class="['el-form-item', { 'is-required': required, 'is-error': isError }]" style="margin-bottom: 0;">
    <label class="el-form-item__label" style="padding: 0;"/>
    <el-input
      ref="editor"
      :value="value"
      type="textarea"
      :placeholder="placeholder"
      :maxlength="maxlength"
      show-word-limit
      @blur="handleBlur"
      @input="handleInput"
      @change="handleChange"
    />
    <div v-show="isError" class="el-form-item__error" style="position: relative; left: 8px;">
      {{ message }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'CellEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default(row) {
        return this.$t('pages.plzEnter')
      }
    },
    maxlength: {
      type: [Number, String],
      default: 50
    },
    focused: {
      type: <PERSON><PERSON>an,
      default: false
    },
    required: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    message: {
      type: String,
      default: 'Content is required!'
    }
  },
  data() {
    return {
      isError: false
    }
  },
  computed: {
    editor() {
      return this.$refs['editor']
    }
  },
  mounted() {
    if (this.focused) {
      this.editor.focus()
    }
  },
  methods: {
    blur() {
      this.editor.focus()
      this.editor.blur()
    },
    focus() {
      this.editor.focus()
    },
    select() {
      this.editor.select()
    },
    handleBlur(event) {
      // form validation
      this.isError = this.required && event.target.value.trim().length === 0
      this.$emit('blur', event);
    },
    handleInput(value) {
      this.$emit('input', value)
    },
    handleChange(value) {
      // 处理纯空格
      if (Object.prototype.toString.call(value) === '[object String]' && value.trim() === '') {
        this.$emit('input', '')
      }
    }
  }
}
</script>
