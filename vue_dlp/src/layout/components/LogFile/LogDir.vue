<template>
  <div class="table-container">
    <div class="toolbar">
      <slot name="toolbar"></slot>
      <el-button icon="el-icon-plus" size="mini" @click="handleInsert">
        {{ $t('text.add') }}
      </el-button>
      <div class="searchCon" style="float: right">
        <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.nameOrPath')" style="width: 200px;top: -2px;"/>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="search = query.name">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </div>
    <el-table
      ref="dirList"
      v-loading="tableLoading"
      stripe
      border
      tooltip-effect="dark"
      :data="tableData.filter(filerRow)"
      row-key="id"
      style="width: 100%"
      height="350"
      @selection-change="selectionChange"
    >
      <el-table-column type="selection" width="40" :selectable="(row, index) => row.enable" reserve-selection/>
      <el-table-column prop="name" :label="$t('table.name')" min-width="150" sortable show-overflow-tooltip>
        <template slot-scope="scope">
          <cell-editor
            v-if="scope.row.editable"
            :ref="`editor_name_${scope.$index}`"
            v-model="scope.row.name"
            :placeholder="$t('pages.validateMsg_enterName')"
            focused
            required
            :message="$t('text.cantNullInfo', { info: $t('table.name') })"
          />
          <span v-else>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="path" :label="$t('table.path')" min-width="200" sortable show-overflow-tooltip>
        <template slot-scope="scope">
          <cell-editor
            v-if="scope.row.editable"
            :ref="`editor_path_${scope.$index}`"
            v-model="scope.row.path"
            :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.path') } )"
            maxlength="255"
            required
            :message="$t('text.cantNullInfo', { info: $t('pages.path') })"
          />
          <span
            v-else
            :title="scope.row.fullpath"
            :style="scope.row.enable ? 'color: #409EFF;' : 'text-decoration: line-through;'"
          >
            {{ scope.row.path }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" :label="$t('text.remark')" min-width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-input
            v-if="scope.row.editable"
            v-model="scope.row.remark"
            type="textarea"
            :placeholder="$t('text.pleaseEnterInfo', { info: $t('text.remark') })"
            maxlength="200"
            show-word-limit
          />
          <span v-else>{{ scope.row.remark }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" width="160">
        <template slot-scope="scope">
          <el-button
            size="mini"
            :loading="scope.row.saveLoading"
            type="primary"
            plain
            :icon="scope.row.editable ? 'el-icon-check' : 'el-icon-edit'"
            @click="handleInsertOrSave(scope.row, scope.$index)"
          >
            {{ scope.row.editable ? $t('button.save') : $t('button.edit') }}
          </el-button>
          <el-button
            size="mini"
            type="warning"
            plain
            :icon="scope.row.editable ? 'el-icon-close' : 'el-icon-delete'"
            @click="handleUpdateOrCancel(scope.row, scope.$index)"
          >
            {{ scope.row.editable ? $t('button.cancel') : $t('button.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <label class="remark-label">
      <i18n path="pages.logFileRemark">
        <span slot="A" class="remark-keyword">${catalina.home}</span>
        <span slot="B" class="remark-keyword">~</span>
      </i18n>
    </label>
  </div>
</template>

<script>
import CellEditor from './CellEditor';
import { getLogDirList, saveLogDir, deleteLogDir, updateUserLogDir } from '@/api/logFile';

export default {
  name: 'LogDir',
  components: { CellEditor },
  props: {
    selection: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      search: '',
      tableLoading: false,
      tableSelection: [],
      tableData: [],
      editRowIndex: -1,
      editRowData: null,
      insertRowData: {
        id: null,
        name: '',
        path: '',
        fullpath: null,
        enable: true,
        checked: false,
        editable: true
      },
      rules: {
        name: [{ required: true, message: this.$t('text.pleaseEnterInfo', { info: this.$t('pages.directoryName') }), trigger: 'blur' }],
        path: [{ required: true, message: this.$t('text.pleaseEnterInfo', { info: this.$t('pages.directoryPath') }), trigger: 'blur' }]
      },
      query: {
        page: 1,
        name: ''
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['dirList']
    }
  },
  watch: {
    tableSelection(value) {
      this.$emit('update:selection', value)
    }
  },
  created() {
    this.tableLoading = true
    getLogDirList().then(respond => {
      this.tableData = respond.data.map(this.preprocessRowData)
      this.tableLoading = false
      this.afterTableLoad()
    }).catch(_ => {
      this.tableLoading = false
    })
  },
  methods: {
    filerRow(row) {
      return !this.search ||
        row.name.toLowerCase().includes(this.search.toLowerCase()) ||
        row.fullpath.toLowerCase().includes(this.search.toLowerCase())
    },
    preprocessRowData(row) {
      row.editable = false
      row.saveLoading = false
      row.deleteLoading = false
      return row
    },
    afterTableLoad() {
      this.$nextTick(() => {
        this.tableData.forEach(row => {
          if (row.checked) {
            this.gridTable.toggleRowSelection(row, true);
          }
        })
      })
    },
    selectionChange(selection) {
      this.tableSelection = selection
    },
    handleSave(row, index) {
      if (row.name.length === 0 || row.path.length === 0) {
        this.$refs[`editor_name_${index}`].blur()
        this.$refs[`editor_path_${index}`].blur()
        return
      }
      row.saveLoading = true
      // this.$set(this.tableData, index, row)
      saveLogDir(row).then(respond => {
        this.$set(this.tableData, index, Object.assign(respond.data, {
          saveLoading: false,
          editable: false
        }))
        this.editRowIndex = -1
        this.editRowData = null
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(_ => {
        row.saveLoading = false
        this.$set(this.tableData, index, row)
      })
    },
    handleCancel(row, index) {
      if (this.editRowData) {
        // this.editRowData.editable = false
        this.$set(this.tableData, index, this.editRowData)
        this.editRowData = null
      } else {
        this.tableData.splice(index, 1)
      }
      this.editRowIndex = -1
    },
    handleNextEdit() {
      this.$confirmBox(this.$t('pages.editSaveTips'), this.$t('text.warning'), {
        confirmButtonText: this.$t('button.save')
      }).then(_ => {
        this.handleSave(this.tableData[this.editRowIndex], this.editRowIndex)
      }).catch(_ => {
        this.handleCancel(this.tableData[this.editRowIndex], this.editRowIndex)
      });
    },
    handleInsert() {
      this.tableData.splice(0, 0, Object.assign({}, this.insertRowData))
      this.editRowIndex = 0
    },
    handleInsertOrSave(row, index) {
      if (row.editable) {
        // 保存
        this.handleSave(row, index)
      } else if (this.editRowIndex >= 0) {
        this.handleNextEdit()
      } else {
        // 修改
        this.editRowData = Object.assign({}, row)
        row.editable = true
        this.$set(this.tableData, index, row)
        this.editRowIndex = index
      }
    },
    handleUpdateOrCancel(row, index) {
      if (row.editable) {
        // 取消
        this.handleCancel(row, index)
      } else if (this.editRowIndex >= 0) {
        this.handleNextEdit()
      } else {
        // 删除
        this.handleDelete(row, index)
      }
    },
    handleDelete(row, index) {
      if (row.id) {
        row.deleteLoading = true
        this.$set(this.tableData, index, row)
        this.$confirmBox(this.$t('pages.validateMsg_deleteMsg')).then(() => {
          deleteLogDir(row.id).then(_ => {
            this.tableData.splice(index, 1)
            row.deleteLoading = false
            this.editRowIndex = -1
            this.editRowData = null
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(_ => {
            row.deleteLoading = false
          })
        }).catch(_ => {
          row.deleteLoading = false
        })
      } else {
        this.tableData.splice(index, 1)
      }
    },
    handleUserDirUpdate(selection) {
      return updateUserLogDir(selection.map(row => row.id).join(','))
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-table th .cell .el-input {
    line-height: 28px;
    padding: 0;
  }

  .el-table td .cell .el-button--mini {
    padding: 7px 8px;
    margin-bottom: 0;
    float: left;
  }
</style>
