<template>
  <div class="table-box">
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      row-key="path"
      border
      stripe
      tooltip-effect="dark"
      lazy
      height="350"
      :load="load"
      :tree-props="{children: children<PERSON>ey, hasChildren: has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}"
      @selection-change="selectionChange"
    >
      <el-table-column type="selection" width="40" reserve-selection/>
      <el-table-column prop="name" :label="$t('table.fileName')" min-width="175" sortable show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="!scope.row.folded" class="file-icon">
            <i class="el-icon-document"></i>
          </div>
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column prop="dir" :label="$t('pages.serverlog_file_directory')" min-width="175" sortable show-overflow-tooltip/>
      <el-table-column prop="date" :label="$t('pages.serverlog_file_date')" min-width="100" sortable show-overflow-tooltip/>
      <el-table-column prop="size" :label="$t('table.fileSizeSumAll')" min-width="100" show-overflow-tooltip/>
      <el-table-column :label="$t('table.operate')" width="125">
        <template slot-scope="scope">
          <el-button
            v-for="(btn, index) in rowButtons"
            :key="index"
            type="text"
            :disabled="checkBtnDisabled(btn, scope.row)"
            @click="btn.click(scope.row, scope.$index)"
          >
            {{ btn.label }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <el-pagination
        small
        background
        :total="total"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :page-sizes="pageSizes"
        :pager-count="pagerCount"
        :layout="pageLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { scrollTo } from '@/utils/scrollTo'

export default {
  name: 'FileTable',
  props: {
    childrenKey: {
      type: String,
      default: 'children'
    },
    hasChildrenKey: {
      type: String,
      default: 'hasChildren'
    },
    childrenLoadApi: {
      type: Function,
      default() {
        return Promise.resolve({ data: [] })
      }
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      }
    },
    pagerCount: {
      type: Number,
      default: 5
    },
    pageLayout: {
      type: String,
      default: 'slot, total, sizes, prev, pager, next, jumper'
    },
    rowData: {
      type: Array,
      default: function() {
        return []
      }
    },
    rowDataApi: {
      type: Function,
      default: function() {
        return Promise.resolve({
          code: 20000,
          data: {
            total: this.rowData.length,
            items: this.rowData
          }
        })
      }
    },
    rowButtons: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      tableHeight: 200,
      sortName: undefined,
      sortOrder: undefined
    }
  },
  created() {
    this.execRowDataApi()
  },
  methods: {
    load(row, treeNode, resolve) {
      const option = { name: `${row.dir}/${row.name}`, fullpath: row.path }
      this.childrenLoadApi.call(null, option).then(response => resolve(response))
    },
    execRowDataApi(option) {
      this.loading = true
      if (option) {
        this.pageSize = option.limit || this.pageSize
        this.currentPage = option.page || this.currentPage
        this.sortName = option.sortName || this.sortName
        this.sortOrder = option.sortOrder || this.sortOrder
      } else {
        option = {}
      }
      option.page = this.currentPage
      option.limit = this.pageSize
      if (this.sortName) {
        option.sortName = this.sortName
        option.sortOrder = this.sortOrder && this.sortOrder.startsWith('a') ? 'asc' : 'desc'
      }
      this.rowDataApi.call(null, option).then(response => {
        let data;
        if (Array.isArray(response.data)) {
          data = response.data
          this.total = response.data.length
        } else {
          data = response.data.items
          this.total = response.data.total
        }
        data.forEach(item => {
          if (!item.folded) {
            delete item.folded
          }
        })
        this.tableData = data
        this.loading = false
      }).catch(_ => {
        this.loading = false
      })
    },
    checkBtnDisabled(btn, row) {
      const type = typeof btn.disabled
      if (type === 'boolean') {
        return btn.disabled
      }
      if (type === 'function') {
        return btn.disabled(row)
      }
      return false
    },
    selectionChange(selection) {
      this.$emit('selection-change', selection)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.execRowDataApi()
      scrollTo(0, 800)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.execRowDataApi()
      scrollTo(0, 800)
    }
  }
}
</script>

<style lang="scss" scoped>
  .table-box {
    .el-table {
      >>>.el-table__placeholder {
        width: 0;
      }
      >>>.file-icon {
        display: inline-block;
        width: 20px;
        line-height: 20px;
        height: 20px;
        text-align: center;
        color: #68A8D0;
      }
      >>>.el-table__expand-icon {
        margin-right: 0;
        font-size: 14px;
        .el-icon-arrow-right {
          color: inherit;
          &:before {
            content: "\e78a";
          }
        }
      }
      >>>.el-table__expand-icon--expanded {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
        .el-icon-arrow-right:before {
          content: "\e784";
        }
      }
    }
  }
</style>
