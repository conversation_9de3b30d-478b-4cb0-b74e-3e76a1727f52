<template>
  <div class="topbar-logo-container">
    <transition name="topbarLogoFade">
      <router-link key="expand" class="topbar-logo-link" to="/">
        <img v-if="showLogo" :src="logoSrc" class="topbar-logo">
        <h1 :class="{'topbar-title': true, 'en': language == 'en'}">{{ title }}</h1>
      </router-link>
    </transition>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getSystemResources } from '@/utils/i18n'

export default {
  name: 'TopbarLogo',
  props: {
    screenWidth: {
      type: Number,
      default: document.body.clientWidth
    }
  },
  data() {
    return {
      showLogo: true
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ]),
    title() {
      return getSystemResources('title')
    },
    logoSrc() {
      return getSystemResources('topLogo')
    }
  }
}
</script>

<style lang="scss">
  .topbarLogoFade-enter-active {
    transition: opacity 1.5s;
  }

  .topbarLogoFade-enter,
  .topbarLogoFade-leave-to {
    opacity: 0;
  }

  .topbar-logo-container {
    position: relative;
    width: 240px;
    height: 100%;
    line-height: 50px;
    text-align: center;
    overflow: hidden;

    & .topbar-logo-link {
      height: 100%;
      width: 100%;

      & .topbar-logo {
        width: 24px;
        height: 24px;
        vertical-align: middle;
      }

      & .topbar-title {
        width: 195px;
        line-height: 50px;
        margin: 0;
        display: inline-block;
        font-weight: 600;
        font-size: 16px;
        font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
        vertical-align: middle;
        &.en {
          width: 200px;
          line-height: 20px;
          font-size: 14px;
        }
      }
    }
  }
</style>
