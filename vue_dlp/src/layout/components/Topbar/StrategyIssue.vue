<template>
  <el-popover
    v-if="stgIssuePermission"
    placement="bottom-end"
    width="450"
    popper-class="strategy-issue"
    trigger="hover"
    :close-delay="500"
    @show="showNoticePanel"
    @hide="hideNoticePanel"
  >
    <el-tabs id="tabBox" v-model="activeName" type="card" stretch @tab-click="handleClick">
      <el-tab-pane
        v-for="tab in tabList"
        :key="tab.value"
        :label="tabNameFormat(tab)"
        :name="tab.value"
      >
        <ul v-if="tab.value==='other'" :ref="`${tab.value}Tab`">
          <li v-for="(item, index) in tab.strategyNotices" :key="index" class="text item" @click="noticeClick(item.type)">
            <el-row>
              <el-col :span="24" class="backup_hint" onmouseover="this.title = this.innerText">
                <router-link v-if="item.type && checkClickable(item.type)" :to="resolveRoute(item)">
                  {{ item.content }}
                </router-link>
                <a v-else-if="item.type && !checkClickable(item.type)" @click="noticeClick('noPermission')">
                  {{ item.content }}
                </a>
                <a v-else href="JavaScript:;" @click="noticeClick('showAlarm')">{{ item.content }}</a>
              </el-col>
            </el-row>
          </li>
        </ul>
        <ul v-if="tab.value==='approval'" :ref="`${tab.value}Tab`">
          <li v-for="(item, index) in tab.strategyNotices" :key="index" class="text item" onmouseover="this.title = this.innerText" @click="noticeClick(item.type)">
            <router-link :to="resolveRoute(item)">{{ item.content }}</router-link>
          </li>
        </ul>
        <ul v-if="tab.value==='strategy'" :ref="`${tab.value}Tab`">
          <li v-for="(item, index) in tab.strategyNotices" :key="index" class="text item" onmouseover="this.title = this.innerText">
            {{ item }}
          </li>
        </ul>
      </el-tab-pane>

    </el-tabs>
    <div v-if="isLoading" class="loading"><i class="el-icon-loading" style="font-size:20px"></i></div>
    <div v-show="tabList[0].strategyNotices.length > 0 && activeName==='strategy'" class="notifications-footer">
      <el-button size="mini" @click="activeStrategy">{{ $t('layout.stgIssue') }}</el-button>
    </div>
    <span slot="reference" style="position: relative; margin-right: 20px;" >
      <svg-icon icon-class="notice" />
      <span v-show="noticeNum > 0" class="notice-num">{{ iconNum }}</span>
    </span>
  </el-popover>
</template>

<script>

import {
  activeStrategy,
  getApprovalMessageList,
  getIssueList,
  getNoticeSize,
  gettherMessageList
} from '@/api/strategyIssue'
// import { debounce } from '@/utils'
import { mapGetters } from 'vuex'

export default {
  name: 'StrategyIssue',
  data() {
    return {
      // 策略变更消息，审批消息，其他消息。noticeNum：消息数量。strategyNotices：消息列表
      // name 是i18n的key
      tabList: [
        { name: 'layout.stgChangeMsg', value: 'strategy', noticeNum: 0, strategyNotices: [] },
        { name: 'layout.approvalMsg', value: 'approval', noticeNum: 0, strategyNotices: [] },
        { name: 'layout.otherMsg', value: 'other', noticeNum: 0, strategyNotices: [] }
      ],
      // 消息列表数据的 option，type：tabList 的索引，func：消息列表数据的请求接口
      tabOption: {
        strategy: { type: 0, func: getIssueList },
        approval: { type: 1, func: getApprovalMessageList },
        other: { type: 2, func: gettherMessageList }
      },
      activeName: 'strategy',                                        // 当前显示 tab 的 name
      routerMap: {
        1: '/terminalManage/terminalManage/approvalAccess',          // 终端接入审批
        2: '/system/baseData/usbDevice',                             // USB设备库-USB接入审批
        3: '/terminalManage/terminalManage/terminalGrpNickNameV2',     // 终端分组和名称规范
        5: '/system/deviceManage/DBServer',                          // 数据库服务器
        6: '/system/structure/dataSourceConfig',                     // 第三方系统数据源设置
        7: '/terminalManage/terminalManage/mobileTerminalFileTool',   // 移动终端文档阅读设置
        8: '/system/baseData/mailLibrary',                            // 邮箱信息库
        9: '/system/deviceManage/serverAccessApproval',                //  服务器接入审批
        10: '/terminalManage/softwareManager/softwareRepository'         // 软件上架审批
      },
      changeSize: 1,                       // 请求消息列表前，websocket通知stgIssue变更的次数
      isLoading: false,                    // 是否正在请求
      tabSearch: {                         // tab搜索条件
        page: 1,                           // 请求消息的页码
        limit: 20                          // 请求消息的数量
      }
    }
  },
  computed: {
    ...mapGetters([
      'routesPath',             // 拥有权限的 路由path 的 map
      'wsNoticeInfo',           // websocket消息通知信息
      'menuCodeMapper',         // 菜单编码映射表
      'menuCodeFullTitleMap'    // 菜单路径 map
    ]),
    // 消息总数量
    noticeNum() {
      return this.tabList.reduce((total, cur) => total + cur.noticeNum, 0)
    },
    // 气泡显示的消息数量
    iconNum() {
      return this.noticeNum > 99 ? '99+' : this.noticeNum
    },
    // 是否拥有策略消息功能
    stgIssuePermission() {
      return this.hasPermission('111|119')
    }
  },
  watch: {
    wsNoticeInfo: {
      deep: true,
      handler(val) {
        this.setTotalNum(val)
      }
    }
  },
  created() {
    this.countTotalNum()
    this.$socket.subscribe({ url: '/topic/stgIssue', callback: (respond, handle) => {
      // 此订阅当作所有消息的通知接口
      if (respond.data) {
        // 添加时间戳
        respond.data.timestamp = new Date()
        this.updateWsNoticeInfo(respond.data)
        // 这个是什么消息？
        console.log('socket---stgIssue', respond.data);
        if (respond.data.hasOwnProperty('SoftwareOrderPage')) {
          return
        }
      }
      this.changeSize++
    } })
  },
  mounted() {
    // 策略消息不分页返回数据，注释该代码
    // 防抖
    // this._onTabScroll = debounce(() => {
    //   this.onTabScroll()
    // }, 100)
    // const tabBox = document.getElementById('tabBox')
    // tabBox && tabBox.addEventListener('scroll', this._onTabScroll, true);
  },
  beforeDestroy() {
    // 移除监听事件
    // window.removeEventListener('scroll', this._onTabScroll)
  },
  methods: {
    tabNameFormat(tab) {
      const name = this.$t(tab.name)
      return tab.noticeNum > 0 ? `${name}(${tab.noticeNum})` : `${name}`
    },
    // 判断消息是否可跳转的路由
    checkClickable(type) {
      const url = this.routerMap[type]
      const hasPermission = !!this.routesPath[url]
      return hasPermission
    },
    // 处理路由（加上跳转路由后需要带上的参数）
    resolveRoute(data) {
      const { type, noteId = null, createTime = null, typeList } = data
      const path = this.routerMap[type]
      const query = {}
      if (typeList) {
        const prop = !typeList.some(v => v != 3) ? 'mobile' : 'pc'
        query[prop] = new Date().getTime()
      } else {
        const querys = {
          2: { tabName: 'UsbApprovalAccess' },
          3: { tabName: 'other' },
          6: { tabName: 'syncConflictTab' },
          5: { tabName: 'DataBaseBackLog', noteId, createTime },
          8: { dlgName: 'mailIdleTime' },
          9: { tabName: 'config' },
          10: { tabName: 'SoftApproval' }
        }
        Object.assign(query, querys[type])
      }
      return { path, query }
    },
    // 点击消息，发送通知 需要进行的额外操作
    noticeClick(type) {
      if (type == 'noPermission') {
        // 没有菜单权限，无法跳转
        this.$message({
          message: this.$t('pages.encOrDecLog_Msg1'),
          type: 'error'
        })
        return
      }
      // type: 1 终端审批, 2 usb审批, 3 终端分组和名称规范, 6 第三方系统数据源设置, showAlarm 显示右下角消息弹窗
      const opt = {
        1: 'ApprovalAccess', 2: 'usbApproval', 3: 'terminalGrpNickName', 6: 'dataSourceConfig', 8: 'mailIdleTime', 9: 'serverAccessApproval', 10: 'SoftApproval',
        'showAlarm': 'showAlarm', 5: 'DBServer'
      }
      const noticeName = opt[type]
      // 如果有 noticeName，则发送通知
      noticeName && this.$store.dispatch('commonData/changeNotice', noticeName)
    },
    // 消息面板显示时默认显示有信息的界面，若3种面板都有信息，则按照策略变更信息>审批信息>其他信息 这样的优先级显示
    showNoticePanel() {
      const { strategy, term, usb, software, other, alarmNotic, idleMail, server } = this.wsNoticeInfo
      if (strategy > 0) {
        this.activeName = 'strategy'
      } else if (term > 0 || usb > 0 || software > 0 || server > 0) {
        this.activeName = 'approval'
      } else if (other > 0 || alarmNotic > 0 || idleMail > 0) {
        this.activeName = 'other'
      } else {
        this.activeName = 'strategy'
      }
      this.isLoading = false
      this.tabSearch.page = 1
      this.changeSize++
      this.fetchData()
    },
    // 消息面板隐藏时，清除消息列表数据
    hideNoticePanel() {
      this.tabList.forEach(item => { item.strategyNotices.splice(0) })
    },
    // 获取消息列表数据
    fetchData() {
      // 获取当前 tab 的接口数据
      const option = this.tabOption[this.activeName]
      // 获取 type 和 func
      const { type, func } = option
      if (!this.isLoading && this.changeSize > 0) {
        this.isLoading = true
        func(this.tabSearch).then(response => {
          if (type == 2) {
            //  其它消息，没有分页功能，所以先清空数据
            this.tabList[type].strategyNotices.splice(0)
            this.tabList[type].strategyNotices.push(...response.data)
            // 添加告警信息 alarmNotic
            const { alarmNotic } = this.wsNoticeInfo
            if (alarmNotic > 0) {
              const alarmData = { content: this.$t('layout.stgIssue_Msg1', { alarmNotic: alarmNotic }) }
              this.tabList[type].strategyNotices.push(alarmData)
            }
          } else if (type == 0) {
            // 策略消息
            if (response.data.length == 0) {
              this.tabList[type].noticeNum = 0
            } else {
              response.data.forEach(t => {
                const startIndex = t.indexOf('{')
                const endIndex = t.indexOf('}')
                if (startIndex < 0 || endIndex < 0) {
                  this.tabList[type].strategyNotices.push(t)
                } else {
                  // 如果消息中存在待替换的菜单编码（例如：{000}被更新），那么进行转换
                  const menuCode = t.substring(startIndex + 1, endIndex)
                  // F15:文件自检内容检测策略,E28全盘扫描内容检测策略，菜单屏蔽，但有跳转路口，管理员日志路径显示主策略路径
                  const targetMenuCode = this.menuCodeMapper[menuCode] || menuCode
                  const menuPathArray = this.menuCodeFullTitleMap[targetMenuCode]
                  let menuLabel = [...(menuPathArray || [])].pop()
                  if ('E26' === targetMenuCode) {
                    // 全盘扫描策略拆分成3个菜单，但是显示的时候只显示一个
                    menuLabel = this.$t('route.diskScan')
                  } else if (menuCode === 'D54') {
                    menuLabel = this.$t('route.webFlowConfigStrategy')
                  }
                  const msg = t.replace(`{${menuCode}}`, menuLabel)
                  this.tabList[type].strategyNotices.push(msg)
                }
              })
            }
          } else {
            // 审批消息
            this.tabList[type].strategyNotices.push(...response.data)
          }
          this.isLoading = false;
          this.changeSize = 0
        }).catch(res => {
          this.isLoading = false
        })
      }
    },
    // 更新消息数量
    updateWsNoticeInfo(data) {
      this.$store.dispatch('commonData/changeWsNoticeInfo', data)
    },
    // 获取消息数据
    countTotalNum() {
      getNoticeSize().then(respond => {
        // 初始化消息数量
        for (let i = 0; i < this.tabList.length; i++) {
          this.tabList[i].noticeNum = 0
        }
        if (respond.data) {
          this.updateWsNoticeInfo(respond.data)
        }
      });
    },
    // 设置消息数量
    setTotalNum(wsNoticeInfo) {
      const { strategy, term, usb, software, other, alarmNotic, idleMail, server } = wsNoticeInfo
      // 审批消息数量
      let approvalSize = 0
      // 终端接入审批
      if (this.hasPermission('B21')) {
        approvalSize += term
      }
      // USB接入审批
      if (this.hasPermission('A5B')) {
        approvalSize += usb
      }
      //  服务器接入审批
      if (this.hasPermission('A4M')) {
        approvalSize += server;
      }      // 软件上架审批
      if (this.hasPermission('B61')) {
        approvalSize += software
      }      // 其他消息数量
      let otherNum = other + (alarmNotic > 0 ? alarmNotic : 0)
      if (this.hasPermission('482')) {
        otherNum += idleMail
      }
      // 更新各个 tab 的消息数量
      if (strategy != undefined) {
        [strategy, approvalSize, otherNum].forEach((num, index) => {
          this.tabList[index].noticeNum = num
        })
      }
    },
    // 策略下发
    activeStrategy() {
      activeStrategy().then(() => {
        // 清空策略消息列表
        this.tabList[0].strategyNotices.splice(0)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('layout.issueSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    // 点击tab，重新获取 list 数据
    handleClick(tab) {
      this.tabList.forEach(item => {
        item.strategyNotices.splice(0)
      })
      this.changeSize++
      this.isLoading = false
      this.tabSearch.page = 1
      this.fetchData()
    },
    // 策略消息不分页返回数据，注释该代码
    // 消息tab页面滚动事件
    // onTabScroll() {
    //   const list = this.getLoadData(this.activeName)
    //   const listDom = this.$refs[`${this.activeName}Tab`][0]
    //   // scrollHeight 列表高度, scrollTop 滚动的高度, offsetHeight 容器的高度
    //   const { scrollHeight, scrollTop, offsetHeight } = listDom
    //   // 是否滚动到底部
    //   const isScrollToBottom = scrollHeight <= (scrollTop + offsetHeight + 50)
    //   // 是否有未加载的消息
    //   const hasUnloadNotice = list.strategyNotices.length < list.noticeNum
    //   if (isScrollToBottom && !this.isLoading && hasUnloadNotice) {
    //     this.changeSize++
    //     this.tabSearch.page++
    //     this.fetchData()
    //   }
    // },
    // 获取当前 tab 显示的数据list
    getLoadData(tabName) {
      for (let i = 0; i < this.tabList.length; i++) {
        const item = this.tabList[i]
        if (item.value === tabName) {
          return item
        }
      }
      return null
    }
  }
}
</script>

<style lang='scss'>
  .el-popover__reference .svg-icon{
    cursor: pointer;
  }
  .el-popover.strategy-issue{
    border: none;
    border-radius: 6px;
    padding: 0;
    .el-tabs{
      border: none;
      ul{
        height: 209px;
        overflow-y: auto;
        margin: 0;
        padding: 0 10px;
        list-style: none;
        li{
          /*padding: 2px 10px 3px;*/
          height: 42px;
          line-height: 42px;
          border-bottom: 1px solid #cccccc;
          cursor: default;
          overflow:hidden;
          text-overflow:ellipsis;
          white-space:nowrap;
          font-size: 12px;
          padding-left: 15px;
          /*&:nth-child(odd){*/
          /*  background: #f5f5f5;*/
          /*}*/
          /*&:hover{*/
          /*  background: #ddd;*/
          /*}*/
        }
      }
    }
    .el-tabs__item{
      color: #888;
      &.is-active{
        color: #409EFF;
      }
    }
  }
  .notice-num{
    position: absolute;
    top: -10px;
    left: 13px;
    border-radius: 8px;
    background: red;
    font-size: 12px;
    line-height: 12px;
    padding: 2px 5px;
  }
  .notifications-footer{
    border-top: 1px solid #ebebeb;
    display: flex;
    justify-content: flex-end;
    height: 43px;
    padding: 6px 8px 0 0;
    line-height: 43px;
    background: #fff;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .loading{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;

  }
  .backup_hint {
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }
</style>
