<template>
  <div>
    <div class="topbar">
      <logo ref="logo" :screen-width="screenWidth" />
      <el-menu
        v-if="!isMenuOverflow"
        ref="tiledMenu"
        mode="horizontal"
        :router="false"
        :default-active="routePath"
        @select="handleSelect"
      >
        <top-menu-item v-for="route in getRoutes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
      <el-menu
        v-else
        mode="horizontal"
        :router="false"
        @select="handleSelect"
      >
        <template v-for="route in foldRoutes" slot>
          <top-menu-item v-if="route.type != 'fold'" :key="route.path" :item="route" :base-path="route.path" />
          <el-submenu v-else :key="route.key" :index="route.index">
            <template slot="title"><span class="menu-title">{{ route.label }}</span></template>
            <template v-for="childRoute in route.children" slot>
              <top-menu-item :key="childRoute.path" :item="childRoute" :base-path="childRoute.path" />
            </template>
          </el-submenu>
        </template>
      </el-menu>

      <div class="right-menu">
        <el-dropdown v-if="baseServerList.length > 0" v-permission="'A4F | A4A'" class="avatar-container" trigger="hover" @visible-change="handleNacServerList">
          <div class="avatar-wrapper" style="margin-top: 0">
            <span style="font-size: 14px;color:#ffffff;cursor: pointer" >{{ $t('pages.otherSystems') }}</span>
            <i class="el-icon-caret-bottom" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown" >
            <el-dropdown-item v-for="item in baseServerList" :key="item.devId" :divided="smallScreen" @click.native="productClick(item)">
              <span style="display:block;">{{ item.name }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <lang-select class="right-menu-item hover-effect" />
        <!-- 数据大屏 -->
        <large-screen/>
        <!-- 角色切换 -->
        <el-dropdown :title="$t('layout.switchRole')" trigger="hover" placement="bottom" @command="changeRole">
          <span style="color: #eee;font-size: 20px;">
            <i class="el-icon-user-solid" style="cursor: pointer;"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="role in roleList" :key="role.id" :disabled="role.id === currentRoleId" :command="role.id">{{ role.name }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <!-- 下载管理 -->
        <download-manager v-if="isEnableDownloadManager()"/>

        <!-- 菜单搜索 -->
        <el-popover
          v-model="searchShow"
          placement="bottom"
          trigger="click"
        >
          <el-autocomplete
            ref="autocomplete"
            v-model="searchMenu"
            value-key="label"
            :fetch-suggestions="querySearchAsync"
            :placeholder="$t('layout.searchMenu')"
            clearable
            @select="selectSearch"
            @clear="queryClear"
            @keyup.enter.native="enterFunc"
          >
            <template slot-scope="{ item }">
              <svg-icon :icon-class="item.noComponent ? item.meta.icon : ''"></svg-icon>
              <span :title="item.label">{{ item.label }}</span>
            </template>
          </el-autocomplete>
          <el-tooltip slot="reference" class="item" effect="light" :content="$t('layout.clickSearchMenu')" placement="bottom">
            <span><svg-icon icon-class="search" class="cursor-pointer"></svg-icon></span>
          </el-tooltip>
        </el-popover>

        <!-- 消息中心 -->
        <strategy-issue/>

        <!-- 管理员名称 -->
        <div v-if="screenWidth >= 1440" class="name-wrapper ellipsis" style="width: 80px; text-align: center;">
          <span :title="name">{{ name }}</span>
        </div>
        <!-- 管理员下拉菜单 -->
        <el-dropdown class="avatar-container" trigger="hover" :title="smallScreen ? name : ''">
          <div class="avatar-wrapper">
            <svg-icon icon-class="headPortrait" class="user-avatar" />
            <i class="el-icon-caret-bottom" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <!-- 小屏幕下显示管理员名称 -->
            <el-dropdown-item v-if="smallScreen">
              <span style="display:block;">{{ name }}</span>
            </el-dropdown-item>
            <!-- <router-link to="/">
              <el-dropdown-item :divided="smallScreen">{{ $t('navbar.dashboard') }}</el-dropdown-item>
            </router-link> -->
            <!-- 注册 -->
            <el-dropdown-item :divided="smallScreen">
              <span style="display:block;" @click="register">{{ $t('layout.register') }}</span>
            </el-dropdown-item>
            <!-- 图标列表(仅供开发模式下使用，不需做多语言) -->
            <el-dropdown-item v-if="isDev()" divided>
              <span style="display:block;" @click="goToIconList">图标列表</span>
            </el-dropdown-item>
            <!-- 系统设置 -->
            <el-dropdown-item divided>
              <span style="display:block;" @click="handleSetting">{{ $t('pages.systemConfig') }}</span>
            </el-dropdown-item>
            <!-- 修改密码 -->
            <el-dropdown-item divided>
              <span style="display:block;" @click="changePassword">{{ $t('pages.changePassword') }}</span>
            </el-dropdown-item>
            <!-- 切换模式 -->
            <el-dropdown-item v-if="showChangeMode" v-permission="'101'" divided>
              <span style="display:block;" @click="handleModeSwitch">{{ $t('pages.changeMode') }}</span>
            </el-dropdown-item>
            <!-- 退出登录 -->
            <el-dropdown-item v-permission="'100'" divided>
              <span style="display:block;" @click="logout">{{ $t('navbar.logOut') }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <!-- 处理部分页面自动填充账号密码的问题 -->
      <input type="text" class="autocomplete">
      <input type="password" class="autocomplete">
    </div>

    <!-- 切换模式 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.changeTo', { mode: changeToModeName })"
      :visible.sync="modeDlgVisible"
      :width="$store.getters.language === 'en' ? '600px' : '510px'"
    >
      <Form ref="modeSwitchForm" :model="tempModeSwitch" :hide-required-asterisk="true" :rules="rules" label-position="right">
        <div v-if="isSuperMode" class="text-tip">
          {{ $t('pages.modeSwitchTip') }}
        </div>
        <div v-else class="text-tip">
          {{ $t('pages.modeSwitchTip1') }}
          <ul style="margin: 0;">
            <li v-for="(value, index) in switchStatusOfThreeUser" :key="index">
              {{ userOptions[index] }}
              <span :style="{color: value ? 'green' : 'red'}">({{ value ? $t('pages.modeSwitched') : $t('pages.modeNotSwitched') }})</span>
            </li>
          </ul>
          {{ $t('pages.modeSwitchTip2') }}
        </div>
        <FormItem :label="$t('pages.sysUserPassword')" label-width="80px" :extra-width="{en: 95}" prop="modeSwitchConfirmPassword">
          <el-input v-model="tempModeSwitch.modeSwitchConfirmPassword" type="password" :placeholder="this.$t('pages.validateMsg_password')" show-password></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="modeSwitch">{{ $t('button.confirm') }}</el-button>
        <el-button @click="modeDlgVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 系统设置 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.systemConfig')"
      :visible.sync="settingDlgVisible"
      width="600px"
      @closed="closeSetting"
    >
      <!-- 页面设置 -->
      <el-divider content-position="left">{{ $t('pages.pageSetting') }}</el-divider>
      <div class="set-con">
        <!-- <p >{{ $t('tagsView.content') }}</p> -->
        <div>
          <i18n path="tagsView.interfaceUpperLimitNum">
            <template slot="number">
              <el-input
                v-model="pageLimitValue"
                style="width: 60px"
                @input="upperInput"
                @change="(val) => val < 10 ? pageLimitValue = 10 : (val > 20 ? pageLimitValue = 20 : pageLimitValue)"
              />
            </template>
          </i18n>
          <el-tooltip effect="dark" placement="top-start">
            <div slot="content">
              {{ $t('tagsView.upperLimitPrompt') }}<br/>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </div>
        <br>
        {{ $t('tagsView.content1') }}
        <el-radio-group v-model="closeTypeValue">
          <el-radio :label="1" class="option-item">{{ $t('tagsView.content2') }}</el-radio>
          <el-radio :label="2" class="option-item">{{ $t('tagsView.content3') }}</el-radio>
        </el-radio-group>
      </div>

      <!-- 皮肤设置 -->
      <el-divider content-position="left">{{ $t('pages.themeSetting') }}</el-divider>
      <div class="set-con">
        <el-radio-group v-model="themeValue" @change="switchTheme">
          <el-radio v-for="(option, i) in themeOption" :key="i" :label="option.theme" class="option-item theme-item">{{ option.label }}</el-radio>
        </el-radio-group>
      </div>

      <!-- 操作设置 -->
      <el-divider content-position="left">{{ $t('pages.operateSetting') }}</el-divider>
      <div class="set-con">
        <el-checkbox v-model="cacheSelectedNodeValue" :true-label="1" :false-label="0" class="option-item">{{ $t('pages.rememberSelectedNode') }}</el-checkbox>
        <el-checkbox v-model="cacheTimeQueryValue" :true-label="1" :false-label="0" class="option-item">{{ $t('pages.rememberQueryTime') }}</el-checkbox>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveSetting">{{ $t('button.confirm') }}</el-button>
        <el-button @click="closeSetting">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <register-detail-dlg ref="registerDetailDlg"/>
    <update-pwd-dlg ref="updatePwdDialog" @submitEnd="submitEnd"/>
    <backup-file-decode ref="backupFileDecode"/>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookies from 'js-cookie';

import { isTimeToUpdatePwd, changeRole } from '@/api/system/organizational/sysUser'
import { updateMode } from '@/api/system/organizational/threeUser'
import { getConfig } from '@/api/system/configManage/globalConfig'
import { insertPersonalization } from '@/api/user'
import { findProductList, getNacTokenByTicket } from '@/api/system/deviceManage/nacServer'
import { exchangeTrwfe } from '@/api/system/terminalManage/chargePlugAuth';
import { scanTriggerSoftwareOrderAlarm } from '@/api/softwareManage/copyrightManage/orders'
import { activeEnhancedPlug, getPropertyByCode } from '@/api/property';
import { getAppType, redirectReportLog } from '@/api/report/baseReport/issueReport/issue'

import { generateTitle, getSystemResources } from '@/utils/i18n'
import { ping, getClientIp } from '@/utils/nacToDlp'
import { base64EncodeSpe } from '@/utils/encrypt';
import { isIPv4, isIPv6 } from '@/utils/validate';

import Logo from './Logo'
import TopMenuItem from './TopMenuItem'
import StrategyIssue from './StrategyIssue'
import LangSelect from '@/components/LangSelect'
import DownloadManager from '@/components/DownloadManager'
import UpdatePwdDlg from '@/views/system/organizational/sysUser/updatePwdDlg'
import RegisterDetailDlg from '@/views/register/detailDlg'
import BackupFileDecode from '@/views/system/terminalManage/databaseBackupRecord/backupFileDecode'
import largeScreen from '@/views/report/largeScreen/carouselConfig/index'
import axios from 'axios'

export default {
  name: 'TopBar',
  components: {
    RegisterDetailDlg,
    UpdatePwdDlg,
    Logo,
    TopMenuItem,
    StrategyIssue,
    LangSelect,
    BackupFileDecode,
    DownloadManager,
    largeScreen
  },
  data() {
    return {
      allMeunWidth: 0,         // 完整菜单的宽度
      menuWidthArray: [],      // 每个菜单的宽度数组
      logoWidth: 0,
      rightMenuWidth: 0,
      dialogVisible: false,
      detailsVisible: false,
      themeDlgVisible: false,
      screenWidth: document.body.clientWidth,
      searchMenu: '',
      searchShow: false,
      themeValue: '',                           // 皮肤的class值
      oldTheme: '',                             // 皮肤的class旧值
      closeTypeValue: 1,                        // 页面超出上限时的处理方式
      pageLimitValue: 10,                       // 页面上限
      cacheSelectedNodeValue: 0,                // 组织架构树记住选中节点(所有页面选中相同节点)
      cacheTimeQueryValue: 0,                   // 审计日志记住查询时间(所有页面同步时间)
      regularUpPwd: '',
      passwordLevel: 1,
      forceValiPwdLevel: false,
      themeOption: [
        { value: 1, theme: 'default', label: this.$t('layout.default') },
        { value: 2, theme: 'custom-blue', label: this.$t('layout.lightBlue') }
        // 5.01 版本不上绿色皮肤
        // { value: 3, theme: 'custom-green', label: this.$t('layout.lightGreen') }
      ],
      baseServerList: [],
      dataVer: {},
      // 超管是否有重置密码权限
      resetPasswordFlag: JSON.parse(localStorage.getItem('resetPasswordFlag') || false),
      msgMap: {
        '-9999': this.$t('report.reportJumpErrorMsg'),
        '-1': this.$t('report.reportJumpErrorMsg1'),
        '-2': this.$t('report.reportJumpErrorMsg2'),
        '-3': this.$t('report.reportJumpErrorMsg3'),
        '-4': this.$t('report.reportJumpErrorMsg4')
      },
      factoryPwdFlag: JSON.parse(localStorage.getItem('factoryPwdFlag') || false),
      modeDlgVisible: false,
      settingDlgVisible: false,
      settingTab: 'page',
      tempModeSwitch: {
        modeSwitchConfirmPassword: ''
      },
      // [系统管理员, 安全管理员, 审计管理员]
      userOptions: [this.$t('buildIn.sysAdmin'), this.$t('buildIn.secAdmin'), this.$t('buildIn.audAdmin')],
      // [系统管理员, 安全管理员, 审计管理员], 值代表管理员是否已切换模式（0未切换，1已切换）
      switchStatusOfThreeUser: [0, 0, 0],
      rules: {
        modeSwitchConfirmPassword: [{ required: true, message: this.$t('pages.pwdCantNull'), trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'permission_routes',
      'avatar',
      'userId',
      'name',
      'theme',
      'closeType',
      'pageLimit',
      'cacheSelectedNode',
      'cacheTimeQuery',
      'isSuperMode',
      'isSuperRole',
      'isSuperThreeUser',
      'roleList',
      'currentRoleId',
      'routesMap',
      'routesPath',
      'upPwdTime',
      'tagsViewVm',
      'password',
      'language'
    ]),
    routePath() {
      return this.$route.path
    },
    title() {
      return getSystemResources('title')
    },
    copyright() {
      return getSystemResources('copyright')
    },
    website() {
      return getSystemResources('website')
    },
    // 系统模式名称
    modeName() {
      return this.isSuperMode ? this.$t('pages.superMode') : this.$t('pages.threeMode')
    },
    // 切换后的系统模式名称
    changeToModeName() {
      return !this.isSuperMode ? this.$t('pages.superMode') : this.$t('pages.threeMode')
    },
    getRoutes() {
      const permission_routes = this.permission_routes
      const routes = []

      permission_routes.forEach(element => {
        if (element.path === '/') {
          routes.push(element)
        } else if (!element.hidden) {
          routes.push(element)
        }
      })
      return routes.filter(v => !(v.name == 'Report' && v.children.length == 1 && v.children[0].noRender))
    },
    foldRoutes() {
      const index = this.showMenuNumber
      const foldMenu = this.getRoutes.slice(index)
      const moreMenu = { key: '1', index: '1', type: 'fold', label: this.$t('layout.moreMenu'), children: foldMenu }
      const newRoutes = [...this.getRoutes.slice(0, index), moreMenu]
      return newRoutes
    },
    smallScreen() {
      return this.screenWidth < 1580 || (this.language == 'en' && this.screenWidth < 1900)
    },
    // 菜单容器的宽度，随着浏览器窗口大小变化而变化
    menubarWidth() {
      // 扣除logo、右侧菜单栏，并额外扣除100
      return this.screenWidth - this.logoWidth - this.rightMenuWidth - 100
    },
    // 菜单是否超出容器范围
    isMenuOverflow() {
      return this.allMeunWidth > this.menubarWidth
    },
    showMenuNumber() {
      let totalWidth = 0
      const showNumber = this.menuWidthArray.reduce((result, width) => {
        totalWidth += width
        if (totalWidth < this.menubarWidth) {
          result++
        }
        return result
      }, 0)
      return showNumber > 2 ? showNumber - 1 : 1
    },
    showChangeMode() {
      return (this.isSuperMode && this.isSuperRole) || (!this.isSuperMode && this.isSuperThreeUser)
    }
  },
  watch: {
    theme(value) {
      this.themeValue = value
    },
    closeType(value) {
      this.closeTypeValue = value
    },
    pageLimit(value) {
      this.pageLimitValue = value
    },
    cacheSelectedNode(value) {
      this.cacheSelectedNodeValue = value
    },
    cacheTimeQuery(value) {
      this.cacheTimeQueryValue = value
    }
  },
  created() {
    this.handleNacServerList(true)
    this.getConfig()
    this.observeTableChange()
    this.scanTriggerAlarm()
    this.getCleanOffilneTermResult()
    window.addEventListener('resize', this.handleResize)
    this.$nextTick(() => {
      this.handleResize()
      // 获取菜单元素的宽度
      this.menuWidthArray = this.$refs.tiledMenu.$children.map(comparent => comparent.$el.offsetWidth)
      this.allMeunWidth = this.menuWidthArray.reduce((total, val) => total + val)
    })
    this.getRoleList()
    getClientIp()
    this.getSwitchStatusOfThreeUser()
    this.restoreSetting()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    async pingServerWithAxios(ip, port, path) {
      try {
        let http = 'http://'
        if (window.location.href.startsWith('https')) {
          http = 'https://'
          // 报表目前https的端口是写死的
          port = 28444
        }
        if (isIPv6(ip)) {
          ip = '[' + ip + ']'
        }
        const url = http + ip + ':' + port + path
        const response = await axios.post(url);
        return response.status == 200
      } catch (error) {
        return false
      }
    },
    // logo、右侧菜单栏的宽度
    getMenuWidth() {
      this.logoWidth = this.$refs.logo.$el.offsetWidth
      this.rightMenuWidth = this.$el.getElementsByClassName('right-menu')[0].offsetWidth
    },
    scanTriggerAlarm() {
      // 触发增强型文档插件权限校验，数据同步以及预警触发扫描
      this.getEnhancedPlugPermission()
      // 触发软件订单预警扫描
      this.scanTriggerSoftwareOrderAlarm()
    },
    scanTriggerSoftwareOrderAlarm() {
      scanTriggerSoftwareOrderAlarm().then(() => {})
    },
    getEnhancedPlugPermission() {
      activeEnhancedPlug().then(res => {
        if (res.data) {
          this.syncChargePlugUsedChange()
        }
      })
    },
    enterFunc(e) {
      let isMatch = false
      const commandOps = {
        '$config': { routeName: 'SpecialConfig', permission: '101' },              // 系统高级配置
        '$confighiddentool': { routeName: 'HiddenTool' },                          // 隐藏工具
        '$scheduled': { routeName: 'Scheduled' },                                  // 定时器监控
        '$configtzm': { routeName: 'SignatureData' },                              // 浏览器特征码
        '$configskzr': { routeName: 'ProcessInject' },                             // 进程高级管控（受控注入）
        '$configthjp': { routeName: 'SeamlessReplace', permission: '122' },        // 替换竞品
        '$configld': { routeName: 'LvDun', permission: '101' },                    // 导入绿盾相关配置，用于绿盾升级DLP
        '$configjjmjksq': { routeName: 'InterfaceAuth', permission: '123' },       // 加解密接口授权
        '$configwjwf': { routeName: 'SensitiveConfig', permission: 'G22' },        // 文件外发配置
        '$utermdel': { routeName: 'AuthorizeUninstall' },                          // 验证码卸载U盘终端
        '$idmApiTool': { routeName: 'IdmApiTool' },                                // IDM接口工具
        '$configgwrjxz': { routeName: 'SoftwareBlackList' },                       // 高危软件限制
        '$confignac': { routeName: 'NacServerConfig' },
        '$termgroupname': { routeName: 'terminalGrpNickName' },
        '$termdisableguid': { routeName: 'TermGuidBlacklist' }  // 終端GUID黑名單
      }
      const commandOp = commandOps[this.searchMenu]
      if (commandOp) {
        if (!commandOp.permission || this.hasPermission(commandOp.permission)) {
          if (commandOp.routeName === 'BackupFileDecode') {
            this.$refs['backupFileDecode'].show()
            isMatch = true
          } else {
            // params不能与path同时使用，所以用name替换path
            this.$router.push({
              name: commandOp.routeName,
              params: { search: true }
            })
            isMatch = true
          }
        }
      }
      if (isMatch) {
        this.searchShow = false
        this.searchMenu = ''
      }
    },
    generateTitle,
    getRoutesMap() {
      return this.routesMap
    },
    // 获取角色数据
    getRoleList() {
      // 如果 currentRoleId 没有值，则去获取角色数据
      if (!this.currentRoleId) {
        this.$store.dispatch('user/getRoleData')
      }
    },
    // 切换当前管理员的角色
    changeRole(id) {
      changeRole(id).then(res => {
        if (res.data) {
          Cookies.set('roleId', id)
          // 切换成功，重新定位到首页
          window.location = '/'
        }
      }).catch(e => {
        console.error(e);
      })
    },
    querySearchAsync(queryString, cb) {
      var searchList = this.getRoutesMap()
      // 根据搜索内容，过滤节点数据
      var results = queryString ? searchList.filter(state => {
        // 非一级菜单
        const isNotFirstLevel = state.noComponent || state.code
        // 有title，且noSearch不为true
        const canSearch = state.meta && state.meta.title && !state.meta.noSearch
        if (isNotFirstLevel && canSearch) {
          state.label = this.generateTitle(state.meta.title)
        }
        return state.label && state.label.toLowerCase().indexOf(queryString.toLowerCase()) > -1
      }).sort((a, b) => {
        // 二三级菜单分开排序
        // if (a.noComponent !== b.noComponent) {
        //   return a.noComponent ? -1 : 1
        // }
        // 按菜单fullPath排序
        return a.fullPath > b.fullPath ? 1 : -1
      }) : []
      cb(results)
    },
    selectSearch(nodeData) {
      const routesPath = this.routesPath
      if (routesPath[nodeData.fullPath]) {
        this.$router.push(routesPath[nodeData.fullPath])
      } else {
        this.$emit('searchMenu', nodeData)
      }
      this.searchShow = false
      this.searchMenu = ''
    },
    queryClear() {
      const input = this.$refs.autocomplete.$refs.input
      this.$nextTick(() => {
        input.blur()
        input.focus()
      })
    },
    handleResize(event) {
      this.screenWidth = document.body.clientWidth
      this.getMenuWidth()
    },
    async getConfig() {
      await getConfig().then(respond => {
        const data = respond.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'regularUpdatePwd') {
            this.regularUpPwd = item.value.substr(0, item.value.length - 1)
          }
          if (item.key === 'passwordLevel') {
            this.passwordLevel = item.value
          }
          if (item.key === 'forceValiPwdLevel') {
            this.forceValiPwdLevel = item.value == 'true' ? Boolean(true) : false
          }
        }
      })
      this.mustUpdatePwd()
    },
    getSwitchStatusOfThreeUser() {
      getPropertyByCode('user.mode.code').then((respond) => {
        // modeCode=0，为三员模式  modeCode=14，已切换为超管模式
        const modeCode = respond.data.value
        // 还未完全切换为超管模式，则记录已经切换模式的管理员
        if (modeCode != 14) {
          // 管理员是否已切换模式（0未切换，1已切换）, 2代表系统管理员，4代表安全管理员，8代表审计管理员
          const sysAdmin = modeCode & 2 ? 1 : 0
          const secAdmin = modeCode & 4 ? 1 : 0
          const audAdmin = modeCode & 8 ? 1 : 0
          this.switchStatusOfThreeUser = [sysAdmin, secAdmin, audAdmin]
        }
      })
    },
    handleModeSwitch() {
      this.tempModeSwitch.modeSwitchConfirmPassword = ''
      this.modeDlgVisible = true
      this.getSwitchStatusOfThreeUser()
      this.$nextTick(() => {
        this.$refs.modeSwitchForm.clearValidate()
      })
    },
    modeSwitch: function() {
      this.$refs.modeSwitchForm.validate(valid => {
        if (valid) {
          // 三员模式下，如果当前三员管理员已完成模式切换，则提示：该管理员已完成模式切换，无需再次切换
          if (!this.isSuperMode) {
            this.getSwitchStatusOfThreeUser()
            // 系统管理员, 安全管理员, 审计管理员 id 分别为 2,3,4；减掉2 对应 switchStatusOfThreeUser 的下标
            const index = this.userId - 2
            const status = this.switchStatusOfThreeUser[index]
            if (status == 1) {
              this.$message({
                message: this.$t('pages.validateMsg_change1'),
                type: 'error',
                duration: 2000
              })
              return;
            }
          }
          // 切换后的模式值
          const mode = !this.isSuperMode ? '1' : '0'
          updateMode({
            mode,
            id: this.userId,
            password: this.tempModeSwitch.modeSwitchConfirmPassword,
            encryptProps: ['password']
          }).then(async(respond) => {
            let msg = this.$t('pages.successMsg', { msg: this.$t('pages.changeMode').toLowerCase() })
            if (!respond.data) {
              msg += this.$t('pages.validateMsg_change')
            }
            this.$notify({
              title: this.$t('text.success'),
              message: msg,
              type: 'success',
              duration: 2000
            })
            setTimeout(() => {
              this.loginOut()
            }, 2000);
          })
        }
      })
    },
    // 打开系统设置
    handleSetting() {
      this.settingDlgVisible = true
      this.restoreSetting()
    },
    // 还原设置
    restoreSetting() {
      this.themeValue = this.theme || 'default'
      this.oldTheme = this.themeValue
      this.closeTypeValue = this.closeType || 1
      this.pageLimitValue = this.pageLimit || 10
      this.cacheSelectedNodeValue = this.cacheSelectedNode
      this.cacheTimeQueryValue = this.cacheTimeQuery
    },
    // 保存设置
    saveSetting() {
      if (!this.pageLimitValue) {
        this.$message({
          message: this.$t('tagsView.content8'),
          type: 'error',
          duration: 2000
        })
        return
      }
      Promise.all([
        insertPersonalization({ sysUserId: this.userId, value: `${this.pageLimitValue}${this.closeTypeValue}`, type: 1 }),
        insertPersonalization({ sysUserId: this.userId, value: this.themeValue, type: 2 }),
        insertPersonalization({ sysUserId: this.userId, value: `${this.cacheSelectedNodeValue}${this.cacheTimeQueryValue}`, type: 5 })
      ]).then(response => {
        this.$message({
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.$store.dispatch('user/setPageLimit', this.pageLimitValue)
        this.$store.dispatch('user/setCloseType', this.closeTypeValue)
        this.$store.dispatch('user/setTheme', this.themeValue)
        this.$store.dispatch('user/setCacheSelectedNode', this.cacheSelectedNodeValue)
        this.$store.dispatch('user/setCacheTimeQuery', this.cacheTimeQueryValue)
        this.settingDlgVisible = false
      }).catch(res => {
      })
    },
    // 关闭系统设置
    closeSetting() {
      this.settingDlgVisible = false
      this.toggleClass(this.oldTheme, this.theme)
    },
    // 切换皮肤
    switchTheme(theme) {
      this.toggleClass(this.oldTheme, theme)
    },
    // 切换body的类，应用皮肤
    toggleClass(oldClass, newClass) {
      const el = document.body
      const className = el.className
      const classSet = new Set(className.split(' '))
      classSet.delete(oldClass)
      classSet.add(newClass)
      const newClassName = Array.from(classSet).join(' ')
      this.oldTheme = newClass
      el.className = newClassName
    },
    upperInput(value) {
      if (typeof (value) == 'string') value = value.replace(/[^\d]/g, '')
      this.pageLimitValue = value == '' ? '' : Number(value)
    },
    logout() {
      this.$confirmBox(this.$t('layout.logoutSystem'), this.$t('text.prompt')).then(() => {
        this.loginOut()
      }).catch(() => {})
    },
    /**
     *
     * 跳转报表控制台
     * @param param
     * @returns {Promise<boolean>}
     */
    async reportRedirect(baseServer) {
      const value = Cookies.get('vue_dlp_up')
      const dlpUrl = encodeURIComponent(base64EncodeSpe(window.location.href.split('#')[0]))
      const param = '?ticket=' + value + '&dlpUrl=' + dlpUrl + '&title=' + this.title
      const response = await getAppType({})
      if (response.data.code != 1) {
        // 综合报表系统是不支持跳转，原因说明
        const msg = this.msgMap[response.data.code + '']
        if (msg != null && msg != undefined) {
          this.$notify({
            title: this.$t('text.prompt'),
            dangerouslyUseHTMLString: true,
            message: msg,
            type: 'warning',
            duration: 2000
          })
        }
        return false
      } else {
        const url = await this.testConnectReport(baseServer)
        if (url != null) {
          const redirectUrl = url.replace('/analysis/dlpReport/test', '/#/dlpLogin' + param)
          redirectReportLog()
          this.$nextTick(() => {
            // 跳转到报表系统前，将cookies中的token清除，当重新返回时，就会处于无token的状态
            this.$store.dispatch('user/setToken', '')
            window.location.href = redirectUrl
          })
        } else {
          this.$notify({
            title: this.$t('text.prompt'),
            dangerouslyUseHTMLString: true,
            message: this.msgMap['-9999'],
            type: 'warning',
            duration: 2000
          })
        }
      }
    },
    async testConnectReport(baseServer) {
      const urls = []
      if (baseServer.intranetIp) {
        let ip = baseServer.intranetIp
        if (isIPv6(ip)) {
          ip = '[' + ip + ']'
        }
        urls.push('http://' + ip + ':' + baseServer.intranetPort + '/analysis/dlpReport/test')
        urls.push('https://' + ip + ':' + baseServer.intranetPort + '/analysis/dlpReport/test')
      }
      if (baseServer.internetIp) {
        let ip = baseServer.internetIp
        if (isIPv6(ip)) {
          ip = '[' + ip + ']'
        }
        urls.push('http://' + ip + ':' + baseServer.internetPort + '/analysis/dlpReport/test')
        urls.push('https://' + ip + ':' + baseServer.internetPort + '/analysis/dlpReport/test')
      }
      for (const url of urls) {
        try {
          const response = await axios.post(url);
          if (response.status == 200) {
            return url
          }
        } catch (error) {
          console.log(url + '连通失败' + error.message)
        }
      }
      return null
    },
    async productClick(baseServer) {
      const value = Cookies.get('vue_dlp_up')
      console.log('DLP ticket:{}', value)
      // if (Cookies.get('vue_dlp_up').includes('+')) {
      //   value = encodeURIComponent(Cookies.get('vue_dlp_up'))
      //   console.log('DLP encodeURIComponent ticket:{}', value)
      // }
      if (baseServer.name.includes('数据泄露')) {
        window.location.href = 'http://' + baseServer.intranetIp + ':' + baseServer.intranetPort + '/#/login?ticket=' + value
      } else if (baseServer.devType === 34) {
        await this.reportRedirect(baseServer)
      } else {
        // 当浏览器使用ip访问，如果是ipv6 则跳转优先用ipv6跳转。
        // 当浏览器使用域名访问,通过插件获取浏览器本机ip地址，能获取到ipv6就用ipv6跳转，能获取到ipv4就用ipv4跳转.
        const hostname = window.location.hostname.replace(/\[|\]/g, '')
        console.log('window.location.hostname', hostname)
        let browserSupportsIpv4 = isIPv4(hostname)
        const browserSupportsIpv6 = isIPv6(hostname)
        const computerSupportsIpv6 = sessionStorage.getItem('current_pc_ipv6') != null
        const computerSupportsIpv4 = sessionStorage.getItem('current_pc_ipv4') != null
        console.warn('browserSupportsIpv4', browserSupportsIpv4)
        console.warn('browserSupportsIpv6', browserSupportsIpv6)
        console.warn('computerSupportsIpv6', computerSupportsIpv6)
        console.warn('computerSupportsIpv4', computerSupportsIpv4)
        if (!browserSupportsIpv4 && !browserSupportsIpv6 &&
            !computerSupportsIpv6 && !computerSupportsIpv4) {
          // DLP控制台浏览器使用域名、插件未安装场景-->默认使用ipv4
          browserSupportsIpv4 = true
        }
        const nacImgPath = '/static/img/404.a57b6f31.png'
        const ipv6InternetPing = ping(baseServer.internetIpv6, baseServer.internetPort, nacImgPath)
        const ipv4InternetPing = ping(baseServer.internetIp, baseServer.internetPort, nacImgPath)
        const intranetIpv6Ping = ping(baseServer.intranetIpv6, baseServer.intranetPort, nacImgPath)
        const intranetPing = ping(baseServer.intranetIp, baseServer.intranetPort, nacImgPath)
        // 当所有的场景都不能用时，给予提示。
        let allIpNoUse = true;
        setTimeout(() => {
          const intranetIp = baseServer.intranetIp
          let devNacIp
          let devNacPort
          // 开发环境获取NAC服务器ip，配置文件 .env.development
          if (this.isDev()) {
            devNacIp = process.env.VUE_APP_NAC_SERVER_IP
            devNacPort = process.env.VUE_APP_NAC_SERVER_PORT
          }
          console.warn('devNacIp', devNacIp)
          console.warn('devNacPort', devNacPort)
          ipv6InternetPing.then((ipv6Internet) => {
            console.warn('ipv6InternetPing', ipv6Internet);
            if (ipv6Internet && (browserSupportsIpv6 || computerSupportsIpv6)) {
              allIpNoUse = false
              const tokenPromise = this.handleGetNacToken(devNacIp || baseServer.internetIpv6, devNacPort || baseServer.internetPort, decodeURIComponent(value), baseServer.devId)
              tokenPromise.then((data) => {
                const token = data.data.token
                console.warn('nac 获取 nac token', token)
                // 切换清空dlp token,在nac退出登录时才能退出到dlp登录页，而不是首页
                sessionStorage.removeItem('token')
                Cookies.remove('vue_dlp_token')
                window.location.href = 'http://[' + baseServer.internetIpv6 + ']:' + baseServer.internetPort + '/#/login?ticket=' + value + '&token=' + token
              })
            }
          })
          setTimeout(() => {
            ipv4InternetPing.then((ipv4Internet) => {
              console.warn('ipv4InternetPing', ipv4Internet);
              if (ipv4Internet && (browserSupportsIpv4 || computerSupportsIpv4)) {
                allIpNoUse = false
                const tokenPromise = this.handleGetNacToken(devNacIp || baseServer.internetIp, devNacPort || baseServer.internetPort, decodeURIComponent(value), baseServer.devId)
                tokenPromise.then((data) => {
                  const token = data.data.token
                  console.warn('nac 获取 nac token', token)
                  // 切换清空dlp token,在nac退出登录时才能退出到dlp登录页，而不是首页
                  sessionStorage.removeItem('token')
                  Cookies.remove('vue_dlp_token')
                  window.location.href = 'http://' + baseServer.internetIp + ':' + baseServer.internetPort + '/#/login?ticket=' + value + '&token=' + token
                })
              }
            })
          }, 500)
          setTimeout(() => {
            intranetIpv6Ping.then((ipv6Use) => {
              console.warn('intranetIpv6Ping', ipv6Use);
              if (ipv6Use && (browserSupportsIpv6 || computerSupportsIpv6)) {
                allIpNoUse = false
                const tokenPromise = this.handleGetNacToken(devNacIp || baseServer.intranetIpv6, devNacPort || baseServer.intranetPort, decodeURIComponent(value), baseServer.devId)
                tokenPromise.then((data) => {
                  const token = data.data.token
                  console.warn('token', token)
                  // 切换清空dlp token,在nac退出登录时才能退出到dlp登录页，而不是首页
                  sessionStorage.removeItem('token')
                  Cookies.remove('vue_dlp_token')
                  window.location.href = 'http://[' + baseServer.intranetIpv6 + ']:' + baseServer.intranetPort + '/#/login?ticket=' + value + '&token=' + token
                })
              }
            })
            setTimeout(() => {
              intranetPing.then((intranetUse) => {
                console.warn('intranetPing', intranetUse);
                if (intranetUse && (browserSupportsIpv4 || computerSupportsIpv4)) {
                  allIpNoUse = false
                  const tokenPromise = this.handleGetNacToken(devNacIp || baseServer.intranetIp, devNacPort || baseServer.intranetPort, decodeURIComponent(value), baseServer.devId)
                  tokenPromise.then((data) => {
                    const token = data.data.token
                    console.warn('token', token)
                    // 切换清空dlp token,在nac退出登录时才能退出到dlp登录页，而不是首页
                    sessionStorage.removeItem('token')
                    Cookies.remove('vue_dlp_token')
                    window.location.href = 'http://' + intranetIp + ':' + baseServer.intranetPort + '/#/login?ticket=' + value + '&token=' + token
                  })
                } else {
                  if (allIpNoUse) {
                    this.unUseNacTip()
                  }
                }
              })
            }, 500)
          }, 1000)
        }, 2000)
      }
    },
    unUseNacTip() {
      this.$notify({
        title: this.$t('text.prompt'),
        message: this.$t('pages.nacServerTip1'),
        type: 'warning',
        duration: 2000
      })
    },
    handleSelect(key, keyPath) {
      // console.log(key, keyPath)
    },
    changePassword() {
      this.$refs['updatePwdDialog'].handleUpdate({
        id: this.userId,
        regularUpPwd: this.regularUpPwd
      })
    },
    submitEnd() {
      this.loginOut()
    },
    async loginOut() {
      await this.$store.dispatch('user/logout')
      this.$store.dispatch('commonData/clearWsNoticeInfo')
      this.$store.dispatch('permission/clearCommonRoute')
      this.$store.dispatch('tagsView/delAllViews')
      // this.$router.push('/login')
      // dlp整合nac 跳转的链接才能正常退出
      window.location.href = '/'
    },
    register() {
      this.$refs['registerDetailDlg'].register()
    },
    goToIconList() {
      this.$router.push('/iconList')
    },
    mustUpdatePwd() {
      if (this.resetPasswordFlag) {
        // 如果是超管并且有重置密码权限的，强制弹窗修改密码窗口，并且要求更改完密码后方可操作
        this.$refs['updatePwdDialog'].handleUpdate({
          id: this.userId,
          upPwdTime: this.upPwdTime,
          regularUpPwd: this.regularUpPwd,
          resetPasswordFlag: true
        }, this.$t('layout.resetPasswordTitle'))
        return;
      }
      // 校验当前使用密码是否是出厂密码，若为出厂密码强制修改
      if (this.factoryPwdFlag) {
        this.$refs['updatePwdDialog'].handleUpdate({
          id: this.userId,
          factoryPwdFlag: true
        }, this.$t('layout.resetPwdTips'))
        return;
      }
      isTimeToUpdatePwd().then(respond => {
        if (respond.data.updatePwd || (this.forceValiPwdLevel && localStorage.getItem('isMatchLevel') == 'false')) {
          this.forceUpdatePassword()
        } else if (respond.data.showTips) {
          this.$confirmBox(this.$t('layout.showUpdatePwdTips'), this.$t('text.prompt')).then(() => {
            this.forceUpdatePassword()
          }).catch(() => {})
        }
      })
    },
    forceUpdatePassword() {
      this.$refs['updatePwdDialog'].handleUpdate({
        id: this.userId,
        upPwdTime: this.upPwdTime,
        regularUpPwd: this.regularUpPwd,
        forceValiPwdLevel: this.forceValiPwdLevel
      }, this.$t('layout.changePwd'))
    },
    syncChargePlugUsedChange() {
      exchangeTrwfe().then(res => {})
    },
    observeTableChange() { // 监听表数据的变更
      this.$socket.subscribe({ url: '/topic/dataChange', callback: (respond, handle) => {
        if (respond.data && respond.data.ver) {
          // 如果返回值带有版本号，并且版本号没有变更，则无需调用更新函数
          const oldVer = this.dataVer[respond.data.code]
          if (oldVer && oldVer === respond.data.ver) {
            return
          }
          this.dataVer[respond.data.code] = respond.data.ver
        }
        const dataCode = respond.data.code ? respond.data.code : respond.data
        switch (dataCode) {
          case 'terminal': this.$store.dispatch('commonData/setTermNode'); break;
          case 'termTreeDisplay': this.$store.dispatch('commonData/setTermNode', true); break;
          case 'user': this.$store.dispatch('commonData/setUserNode'); break;
          case 'userDisplay': this.$store.dispatch('commonData/setUserNode', true); break;
          case 'dept': this.$store.dispatch('commonData/setDeptTree'); break;
          case 'terminalStatus': this.$store.dispatch('commonData/setTermStatusMap'); break;
          case 'responseRule': this.$store.dispatch('commonData/setAlarmRules'); break;
          case 'backupRule': this.$store.dispatch('commonData/setBackupRules'); break;
          case 'timeInfo': this.$store.dispatch('commonData/setTimeOptions'); break;
          case 'mailServer': this.$store.dispatch('commonData/setHasMailServer'); break;
          case 'sysUser': this.$store.dispatch('commonData/setSysUserList'); break;
          case 'userRole': this.$store.dispatch('commonData/setUserRoleOptions'); break;
          case 'softLimitType': this.$store.dispatch('commonData/changeNotice', 'installAppLibGroup'); break;
          default: break;
        }
      } })
    },
    getCleanOffilneTermResult() {
      if (this.$store.getters.isSuperRole || this.$store.getters.isSysRole) {
        this.$socket.subscribe({ url: '/topic/respondAutoCleanResult', callback: (respond, handle) => {
          if (respond.data && respond.data.type == 0) {
            if (respond.data.cleanSuccessNum > 0) {
              this.$notify({ title: this.$t('text.success'), message: this.$t('pages.clean_term_msg', { cleanSuccessNum: respond.data.cleanSuccessNum }), type: 'success', duration: 5000 })
              // 用于通知列表刷新
              this.$store.dispatch('commonData/changeNotice', 'autoCleanOffilneTerm')
            } else if (respond.data.executeFail == 1) {
              // executeFail == 1，表示请求失败，如引擎在写入数据库时发生异常
              this.$notify({ title: this.$t('text.error'), message: this.$t('pages.offline_terminal_text19'), type: 'error', duration: 5000 })
            } else if (respond.data.executeFail == 0) {
              // executeFail == 0，表示无满足清理规则的终端
              this.$notify({ title: this.$t('text.success'), message: this.$t('pages.offline_terminal_text29'), type: 'success', duration: 5000 })
            }
          }
        } })
      }
    },
    handleNacServerList(flag) {
      if (flag) {
        findProductList({ ridType: 'DLP' }).then(response => {
          this.baseServerList.splice(0)
          if (response.data) {
            response.data.forEach(item => {
              if (item.devType == 34 && this.hasPermission('A4A')) {
                this.baseServerList.push(item)
              }
              if (item.devType == 33 && this.hasPermission('A4F')) {
                this.baseServerList.push(item)
              }
              // 显示 其他系统 时，可能会与菜单重叠，这里重新设置菜单
              this.$nextTick(() => {
                this.handleResize()
              })
            })
            console.warn('this.baseServerList', this.baseServerList)
          }
        }).catch(res => {
        })
      }
    },
    async handleGetNacToken(ip, port, ticket, devId) {
      // 获取NAC token
      return getNacTokenByTicket({ ip: ip, port: port, ticket: ticket, devId: devId });
    },
    isIpPortAvailable(ip, port) {
      const url = `http://${ip}:${port}`;
      return fetch(url)
        .then(response => {
        // 如果服务器响应，则IP和端口是可用的
          return true;
        }).catch(reason => {
        // 如果请求超时或者其他错误发生，则IP和端口不可用
          return false;
        });
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";
  .set-con {
    margin-top: 20px;
    margin-left: 20px;
  }
  .option-item {
    height: 30px;
    display: block;
  }
  .theme-item {
    display: inline-block;
  }

  >>>.el-tab-pane {
    padding: 10px;
    margin: 0px 10px;
    position: relative;
  }
  .tab-btns {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: right;
  }
  .text-tip {
    line-height: 20px;
    margin-left: 8px;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: small;
    color: rgb(43, 122, 172);
  }
</style>
