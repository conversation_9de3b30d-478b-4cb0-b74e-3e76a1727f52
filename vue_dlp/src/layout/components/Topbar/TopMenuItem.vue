<template>
  <el-menu-item v-if="item.path ==='/'" :index="resolvePath(item.path)">
    <!-- 由于标签栏首页不允许关掉，与顶部菜单的首页有重复显示的问题，故此处首页菜单隐藏掉 -->
    <!-- <router-link :to="resolveRoute(item.path,item)">{{ generateTitle(item.children[0].meta.title) }}</router-link> -->
  </el-menu-item>

  <el-submenu v-else-if="!item.hidden" :index="resolvePath(item.path)" :popper-append-to-body="false">
    <template slot="title">
      <span>{{ generateTitle(item.meta.title) }}</span>
    </template>
    <template v-for="child in item.children" slot>
      <el-menu-item v-if="!child.children && !child.hidden && child.name!='common_routes'" :key="child.path" :index="resolvePath(child.path)" :item="child">
        <router-link :to="resolveRoute(child.path,child)">{{ generateTitle(child.meta.title) }}</router-link>\
      </el-menu-item>
      <el-submenu v-else-if="!child.hidden && child.name!='common_routes' && !child.noRender" :key="child.path" :index="resolvePath(child.path)">
        <template slot="title"><span class="menu-title">{{ generateTitle(child.meta.title) }}</span></template>
        <template v-for="c in child.children" slot>
          <el-menu-item v-if="!c.hidden" :key="c.path" :index="resolvePath(child.path + '/' + c.path)" :item="c">
            <router-link :to="resolveRoute(child.path + '/' + c.path,c)">{{ generateTitle(c.meta.title) }}</router-link>
          </el-menu-item>
        </template>
      </el-submenu>
    </template>
  </el-submenu>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import { generateTitle } from '@/utils/i18n'

export default {
  name: 'TopMenuItem',
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  methods: {
    resolveRoute(path, route) {
      return {
        path: this.resolvePath(path),
        query: route.query
      }
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    },
    generateTitle
  }
}
</script>

<style lang="scss">
  .el-menu--horizontal{
    .el-menu--popup{
      min-width: 150px;
      max-height: calc(100vh - 60px);
      overflow-y: auto;
      >.el-submenu.is-active .el-submenu__title{
        border-bottom: 0;
      }
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 20px;
      }
    }

    .el-menu-item{
      min-width: 0;
      padding: 0;
      a{
        width: 100%;
        height: 100%;
        display: inline-block;
        padding: 0 20px;
        vertical-align: top;
      }
    }
    >.el-submenu .el-submenu__title{
      height: 100%;
      .menu-title{
        width: calc(100% - 12px);
        padding-left: 10px;
        display: inline-block;
      }
      .el-submenu__icon-arrow{
        margin-left: 4px;
        color: #ccc;
      }
    }
  }

  @media screen and (max-width: 1599px){
    >>>.el-menu--horizontal .el-submenu__title{
      padding: 0 10px;
    }
  }
</style>
