<template>
  <section class="app-main">
    <div :class="{ mask: true, hidden: maskHidden }"></div>
    <!-- 公共策略树组件 -->
    <div v-if="commonStrategyTargetTree" class="tree-container" :class="(isCommonView && commonShowTree) ? '' : 'hidden'">
      <!-- 策略页面的树组件 -->
      <strategy-target-tree
        v-show="isStgView"
        ref="strategyTargetTree"
        :resize-width.sync="resizeWidth"
        is-common
        accordion
        :os-type-filter="osTypeFilter"
        :common-terminal-filter="terminalFilterKey"
        @data-change="strategyTargetNodeChange"
      />
      <!-- 日志页面的树组件 -->
      <strategy-target-tree
        v-show="isLogView"
        ref="logStrategyTargetTree"
        :resize-width.sync="resizeWidth"
        is-common
        accordion
        is-log-terminal-tree
        :os-type-filter="osTypeFilter"
        @data-change="strategyTargetNodeChange"
      />
    </div>
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViewNames">
        <router-view :key="routerKey" :style="containerStyle"/>
      </keep-alive>
    </transition>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'
import { commonStrategyTargetTree } from '@/settings'
import { entityLink } from '@/utils'
import { EventBus } from '@/layout/event-bus';
import { generateTitle } from '@/utils/i18n'

export default {
  name: 'AppMain',
  data() {
    return {
      maskHidden: true,             // 是否隐藏遮罩层
      commonStrategyTargetTree,     // 是否使用公共的策略应用树组件
      onlySelectedNode: false,      // 是否只选中节点（返回已访问页面，重新选中节点时不需要加载列表）
      isInitTab: false,             // 是否需要初始化tab
      usedScopeMap: {},             // 记录使用公共树组件的页面 map （即使用了 TransferTree 组件的页面）
      commonShowTree: true,         // 公共树是否显示
      tempShowTree: true,
      resizeWidth: 0,               // 策略树组件调整的宽度
      currentVmTimer: null,         // 定时器
      entityNode: null,             // 记录需要选中的节点信息
      osTypeFilter: null,           // 手动控制显示的终端类型，1-win,2-linux,4-mac,8-移动设备（数值的和）
      terminalFilterKey: null,      // 外部配置的终端树过滤节点的方法
      isLogTerminalTree: false,     // 组件使用的数据源（区分为是否有回收站节点，false: 有回收站，true: 没有回收站）
      routePath: '',
      debuggerMode: undefined       // 用于判断当前是否处于 debugger 模式下，便于输出相关调试信息到控制台。
    }
  },
  computed: {
    ...mapGetters([
      'commonVm',
      'currentVm',
      'cachedViews',
      'stgTargetCheckedNodeMap',
      'stgBaseConfig',
      'cacheSelectedNode'
    ]),
    stgBaseConfigMap() {
      const config = Object.values(this.stgBaseConfig || {})
      const configMap = config.reduce((map, config) => {
        map[config.routerPath] = config
        return map
      }, {})
      return configMap
    },
    // 缓存的页面 name 数组，通过路由name处理
    cachedViewNames() {
      // 缓存的菜单
      const tempViews = []
      const newCachedViews = this.cachedViews.map(view => {
        // 路由name首字母转成大写
        view = view.substr(0, 1).toUpperCase() + view.substr(1)
        // 组件 name 的长度
        let componentLength = view.length
        if (view.endsWith('P')) {
          // 日志审计页面路由name与组件name不同导致无法缓存的处理方式
          componentLength = view.length - 1
        } else if (view.endsWith('Prepare')) {
          // 预定义策略缓存处理
          componentLength = view.length - 7
        } else if (view.startsWith('StandardSymptomReport')) {
          // 征兆报表(自定义菜单，共用一个组件)
          componentLength = 21
        }
        // 如果 路由name 和 组件name 长度不一致，则将 组件name 添加到 tempViews
        if (view.length != componentLength) {
          const componentName = view.substr(0, componentLength)
          this.cachedViews.indexOf(componentName) == -1 && tempViews.push(componentName)
        }
        return view
      })
      return newCachedViews.concat(tempViews)
    },
    routerKey() {
      // 全盘扫描 实现多个菜单入口共用一个组件的功能（返回相同的key，则组件可被复用）
      // if (this.$route.path == '/dataEncryption/encryption/diskScan' || this.$route.path == '/contentStrategy/contentStg/diskScan') {
      //   return 'diskScan'
      // }
      return this.$route.path
    },
    key() {
      return this.routePath
    },
    // 是否策略菜单
    isStgView() {
      const isNotLog = !this.isLogTerminalTree
      const showCommon = !!this.usedScopeMap[this.key]
      return isNotLog && showCommon
    },
    // 是否日志菜单
    isLogView() {
      const isLog = this.isLogTerminalTree
      const showCommon = !!this.usedScopeMap[this.key]
      return isLog && showCommon
    },
    // 是否使用公共组件的页面
    isCommonView() {
      return this.isStgView || this.isLogView
    },
    // 页面容器的样式，用于设置 margi-left
    containerStyle() {
      let width = 0
      if (this.commonStrategyTargetTree) {
        if (this.isStgView && this.$refs.strategyTargetTree) {
          width = this.$refs.strategyTargetTree.width + 25
        }
        if (this.isLogView && this.$refs.logStrategyTargetTree) {
          width = this.$refs.logStrategyTargetTree.width + 25
        }
      }
      return width ? `margin-left: ${width}px;` : ''
    }
  },
  watch: {
    currentVm(val) {
      // 未开启公共树组件，返回
      if (!this.commonStrategyTargetTree) return
      // 还没有缓存节点，返回
      if (Object.keys(this.stgTargetCheckedNodeMap).length == 0) return
      // 获取不到公共树组件，返回
      const treeVm = this.isStgView ? this.$refs['strategyTargetTree'] : this.isLogView ? this.$refs['logStrategyTargetTree'] : null
      if (!treeVm) return
      // 当前页面缓存的节点数据
      const cachedCheckedNode = this.stgTargetCheckedNodeMap[this.routerKey]

      // 每个页面记住各自点击的节点的情况
      if (!this.cacheSelectedNode) {
        if (!cachedCheckedNode) {
          // 当前页面没有缓存节点，则清除选中的节点
          treeVm.setCurrentKey()
        } else {
          // 根据缓存的节点，切换tab，并选中节点
          const { tabName, checkedNode } = cachedCheckedNode
          if (tabName) {
            treeVm.checkedTab = tabName
          }
          if (checkedNode) {
            // 仅选中节点，不触发页面数据重新加载
            this.onlySelectedNode = true
            treeVm.$refs[tabName][0].selectSearch(checkedNode)
          } else {
            treeVm.setCurrentKey()
          }
        }
      } else {
        //每个页面同步点击的节点的情况
        const currentPanel = treeVm.getCurrentPanel()
        const currentNode = treeVm.getCurrentNode()
        const { tabName, checkedNode = {}} = cachedCheckedNode || {}
        // 有选中的节点 且 与缓存节点不同，则选中该节点并触发页面数据重新加载
        if (currentNode && (currentPanel.name != tabName || currentNode.id != checkedNode.id)) {
          this.strategyTargetNodeChange(currentPanel.name, currentNode)
        }
      }
    },
    $route(val) {
      // 路由发生变化时，通知页面修改 showTree 的值
      EventBus.$emit(`${this.routerKey}-showTree`, this.commonShowTree)
      this.setCommonTreeVm()

      // 从路由的 query 获取选中节点的信息
      const { entityId, entityType } = val.query
      if (entityId && entityType) {
        this.entityNode = { entityType, entityId }
      }

      // 从 sessionStorage 中获取 debuggerMode
      if (!this.debuggerMode) {
        this.debuggerMode = sessionStorage.getItem('debuggerMode')
      }

      this.saveCurrentVm()
      // 路由变化后，设置一个延时1秒的遮罩层，阻止页面内容对鼠标的响应
      this.maskHidden = false
      setTimeout(() => {
        this.maskHidden = true
      }, 1000);
    },
    isCommonView(val) {
      if (val) {
        this.updateCommonShowTree(this.tempShowTree)
      }
    }
  },
  created() {
    this.saveCurrentVm()
    if (this.commonStrategyTargetTree) {
      this.routePath = this.routerKey
      EventBus.$on('usedScope', this.updateUsedScope)
      EventBus.$on('showTree', this.updateCommonShowTree)
      EventBus.$on('osTypeFilter', this.updateOsTypeFilter)
      EventBus.$on('terminalFilterKey', this.updateTerminalFilterKey)
      EventBus.$on('isLogTerminalTree', this.updateIsLogTerminalTree)
      this.$nextTick(() => {
        this.setCommonTreeVm()
      })
    }
  },
  methods: {
    saveCurrentVm() {
      setTimeout(() => {
        // 延时修改 routePath 的值，是为了在切换页面后，公共树组件不会有消失再出现的情况
        this.routePath = this.routerKey
      }, 600);
      clearInterval(this.currentVmTimer)
      // 因为页面跳转可能会出现较长时间的等待时间，避免出现无法获取当前实例的情况，使用定时器每300毫秒获取一次。
      this.currentVmTimer = setInterval(() => {
        const components = this.$children
        for (let i = 0; i < components.length; i++) {
          const component = components[i]
          if (component.$vnode.data.key == this.routerKey) {
            // 如果需要选中节点
            if (this.entityNode) {
              this.isInitTab = false
              // 部分页面是 tabs 结构的，获取当前 tabPane
              const tabPane = component.$refs[component.activeName]
              // 获取树组件或包含树组件的页面组件
              const tree = tabPane && tabPane.$refs.strategyTargetTree
              const vm = tree || component
              entityLink(this.entityNode, {}, vm)
            }
            this.entityNode = null
            this.$store.dispatch('tagsView/setCurrentVm', component)
            if (this.debuggerMode) {
              this.consoleInfo(component)
            }
            clearInterval(this.currentVmTimer)
            break
          }
        }
      }, 300);
    },
    // 将公共树组件的实例存到 store, init：是否页面初始化时调用
    setCommonTreeVm(init) {
      const isLog = this.isLogTerminalTree
      const treeVm = isLog ? this.$refs['logStrategyTargetTree'] : this.$refs['strategyTargetTree']
      if (init) {
        // 页面初始化时，记录当前页面树的 tab（默认为第一个tab）
        setTimeout(() => {
          // 不是初始化 或 每个页面同步点击的节点，则 返回
          if (!this.isInitTab || this.cacheSelectedNode) return
          this.isInitTab = false

          const { showedTree, showHead } = treeVm
          const tabName = showedTree[0]
          if (showHead) {
            // 切换成第一个 tab
            treeVm.checkedTab = tabName
            // 记录当前页面树的 tab
            const checkedNode = null
            this.stgTargetCheckedNodeMap[this.routerKey] = { tabName, checkedNode }
            this.$store.dispatch('tagsView/setStgTargetCheckedNodeMap', this.stgTargetCheckedNodeMap)
          }
        }, 500);
      }
      this.$store.dispatch('tagsView/setCommonVm', { 'strategyTargetTree': treeVm })
    },
    strategyTargetNodeChange(tabName, checkedNode) {
      if (this.onlySelectedNode) {
        this.onlySelectedNode = false
        return
      }
      this.stgTargetCheckedNodeMap[this.routerKey] = { tabName, checkedNode }
      this.$store.dispatch('tagsView/setStgTargetCheckedNodeMap', this.stgTargetCheckedNodeMap)
      this.$nextTick(() => {
        EventBus.$emit(`${this.routerKey}-dataChange`, tabName, checkedNode)
      })
    },
    // 更新 usedScopeMap
    updateUsedScope(val) {
      const map = JSON.parse(JSON.stringify(this.usedScopeMap))
      const key = this.routerKey
      if (!map.hasOwnProperty(key)) {
        map[key] = val
      }
      this.usedScopeMap = map
      this.isInitTab = true
      this.setCommonTreeVm(true)
    },
    // 更新 commonShowTree 的值
    updateCommonShowTree(boolean) {
      this.tempShowTree = boolean
      this.$nextTick(() => {
        if (this.isCommonView) {
          this.commonShowTree = boolean
          EventBus.commonShowTree = boolean
        }
      })
    },
    updateOsTypeFilter(val) {
      this.osTypeFilter = val
    },
    updateTerminalFilterKey(val) {
      this.terminalFilterKey = val
    },
    updateIsLogTerminalTree(val) {
      this.isLogTerminalTree = val
    },
    // 打印调试信息
    consoleInfo(vm) {
      const route = this.$route
      const { fullPath, meta } = route || {}
      const label = generateTitle(meta.title)
      console.group(`菜单：${label}`)
      console.info({ fullPath })
      console.info({ route })
      console.info(`组件实例：`, vm)
      console.groupEnd()
      // 将实例添加到 window 中
      window.__vueInstance__ = vm
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /*50 = navbar  */
  width: 100%;
  height: calc(100vh - 50px);
  position: relative;
  overflow: hidden;
}
.fixed-header+.app-main {
  padding-top: 35px;
  z-index: 98;
}
.mask {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  z-index: 11;
}
.tree-container {
  margin: 5px 10px 20px 15px;
  height: calc(100% - 25px);
}
.tree-container+.app-container {
  margin-left: 220px;
  padding-left: 0;
}
.tree-container.hidden+.app-container {
  margin-left: 0 !important;
  padding-left: 15px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
