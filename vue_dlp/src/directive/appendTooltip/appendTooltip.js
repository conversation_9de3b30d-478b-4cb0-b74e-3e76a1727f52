
/**
 * 该指令用于解决 tooltip 设置 :append-to-body="false" 后，不会显示悬浮信息的问题。
 * 
 * 若 tooltip 在绑定指令的组件内部，则绑定时不需要指定 ref
 * eg: <el-button v-append-tooltip>
          <el-tooltip :append-to-body="false">
            <div slot="content">xxx</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
        
    若 tooltip 不在绑定指令的组件内部，则需要绑定 tooltip 的 ref
    eg: <el-button v-append-tooltip="'tooltipRef'">
          xxxxx
        </el-button>
        <el-tooltip ref="tooltipRef" :append-to-body="false">
          <div slot="content">xxx</div>
          <i class="el-icon-info" />
        </el-tooltip>
 */

export default {
  inserted(el, binding, vnode) {
    // 绑定的是否html的原生标签，因为获取组件实例的方式不一样，需要通过这个来判断
    const isTag = !vnode.componentInstance
    // 当前页面的实例
    const currentInstance = vnode.context
    // 绑定指令的组件的子组件
    const childrenInstance = !isTag ? vnode.componentInstance.$children : vnode.children
    // 绑定的 ref 的值
    const { value } = binding
    // tooltip 实例
    let tooltipInstance = value
      ? currentInstance.$refs[value]
      : childrenInstance.find(instance => {
        return isTag ? instance.componentOptions.tag == 'el-tooltip' : instance.$options._componentTag == 'el-tooltip'
      })
    // 如果有实例，且有 popperVM，则将 popperVM 的节点添加到 绑定指令的组件 下
    if (tooltipInstance) {
      // 修正实例
      tooltipInstance = tooltipInstance.componentInstance || tooltipInstance
      // 实例有 popperVM，则将 popperVM 的 $el 添加到 绑定指令的组件 下
      if (tooltipInstance.popperVM) {
        const popperElement = tooltipInstance.popperVM.$el;
        el.appendChild(popperElement);
      }
    }
  }
}
