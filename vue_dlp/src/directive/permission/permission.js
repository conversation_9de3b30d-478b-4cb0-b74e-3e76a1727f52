
/**
 * 被绑定元素插入父节点时，判断该元素是否有权限，如果没有权限则移除
 * 绑定值为字符串，可传入多个编码，用 , 隔开, 也支持 ! & | 运算符组成的表达式
 * 使用 <component v-permission="'A11 & 100'"></component>
 */
import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    let { value } = binding

    // allPermission 值为 true 时，开放所有菜单权限
    if (process.env.NODE_ENV == 'development' && store.getters.allPermission) return true
    // 有绑定值
    if (value) {
      // 权限 Map
      const userMenuCodesMap = store.getters.userMenuCodesMap
      let hasPermission = false
      // 将 , 转换成 |
      value = value.replaceAll(',', '|')
      // 通过 | 拆分成 或数组
      const orArr = value.split('|')
      // 遍历 或数组
      for (let i = 0; i < orArr.length; i++) {
        const code = orArr[i].trim();
        if (code.includes('&')) {
          // 包含 & 运算符，通过 & 拆分成 且数组
          const andArr = code.split('&')
          let andPermission = true
          // 遍历 且数组
          for (let i = 0; i < andArr.length; i++) {
            const code = andArr[i].trim();
            // 判断是否有权限
            const hasP = code.indexOf('!') == 0 ? !userMenuCodesMap[code.substr(1)] : userMenuCodesMap[code]
            if (!hasP) {
              // 没有权限，给 andPermission 赋值，并结束 且数组 遍历
              andPermission = hasP
              break
            }
          }
          if (andPermission) {
            // 且 表达式有权限，结束 或数组 遍历
            hasPermission = andPermission
            break
          }
        } else {
          // 不包含 & 运算符，判断是否有权限
          const hasP = code.indexOf('!') == 0 ? !userMenuCodesMap[code.substr(1)] : userMenuCodesMap[code]
          if (hasP) {
            // 有权限，结束 或数组 遍历
            hasPermission = hasP
            break
          }
        }
      }
      if (!hasPermission) {
        // 没有权限，移除元素
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      // 没有绑定值
      throw new Error(`需要传入菜单编码表达式! eg: v-permission="'A11 & 100'"`)
    }
  }
}
