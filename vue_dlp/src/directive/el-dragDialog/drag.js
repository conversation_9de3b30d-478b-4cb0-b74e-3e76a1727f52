export default {
  bind(el, binding, vnode) {
    const { value } = binding
    // 如果指令绑定的值是 false，直接 return
    if (value === false) return

    // 获取弹窗标题栏和弹窗整体，如果获取不到则 return
    const dialogHeaderEl = el.querySelector('.el-dialog__header')
    const dragDom = el.querySelector('.el-dialog')
    if (!dialogHeaderEl || !dragDom) return
    // 修改样式
    dialogHeaderEl.style.cssText += ';cursor:move;'
    dragDom.style.cssText += ';top:0px;'
    
    // 绑定指令的组件标签名
    const tag = vnode.componentOptions.tag
    const bindTrDialog = tag === 'TrDialog' || tag === 'tr-dialog'
    // 如果是 TrDialog 组件绑定的指令，需要查找组件下的 dialog__wrapper 元素
    const dialog__wrapper = bindTrDialog ? el.querySelector('.el-dialog__wrapper') : el

    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const getStyle = (function() {
      if (window.document.currentStyle) {
        return (dom, attr) => dom.currentStyle[attr]
      } else {
        return (dom, attr) => getComputedStyle(dom, false)[attr]
      }
    })()

    dialogHeaderEl.onmousedown = (e) => {
      // 鼠标按下，计算标题栏距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft
      const disY = e.clientY - dialogHeaderEl.offsetTop
      
      // 弹窗的宽高
      const dragDomWidth = dragDom.offsetWidth
      const dragDomHeight = dragDom.offsetHeight

      // 弹窗可拖拽范围的宽高
      // const screenWidth = document.body.clientWidth
      // const screenHeight = document.body.clientHeight
      const screenWidth = dialog__wrapper.clientWidth
      const screenHeight = dialog__wrapper.clientHeight
      
      // 弹窗左上角、右下角距离边界的位置
      const minDragDomLeft = dragDom.offsetLeft
      const minDragDomTop = dragDom.offsetTop
      const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth
      const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomHeight

      // 获取弹窗 left top 的值
      let styL = getStyle(dragDom, 'left')
      let styT = getStyle(dragDom, 'top')

      if (styL.includes('%')) {
        //  获取到的值带% 计算出像素值
        styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100)
        styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100)
      } else {
        // 获取到的值带px 正则匹配替换
        styL = +styL.replace(/\px/g, '')
        styT = +styT.replace(/\px/g, '')
      }

      document.onmousemove = function(e) {
        // 通过事件委托，计算移动的距离
        let left = e.clientX - disX
        let top = e.clientY - disY

        // 边界处理（左右）
        if (-(left) > minDragDomLeft) {
          left = -minDragDomLeft
        } else if (left > maxDragDomLeft) {
          left = maxDragDomLeft
        }
        // 边界处理（上下）
        if (-(top) > minDragDomTop) {
          top = -minDragDomTop
        } else if (top > maxDragDomTop && maxDragDomTop >= 0) { // 当dialog的下边框处于可视范围内时，才进行边界处理
          top = maxDragDomTop
        }

        // 移动当前元素
        dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`

        // emit onDrag event
        vnode.child.$emit('dragDialog')
      }

      document.onmouseup = function(e) {
        document.onmousemove = null
        document.onmouseup = null
      }
    }
  }
}
