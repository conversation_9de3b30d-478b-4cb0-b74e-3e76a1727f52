/**
 * 实现在 dialog 弹窗标题栏右侧添加 最大化/还原 按钮，通过点击按钮使窗口最大化和还原的功能
 * 
 * 注意事项：需要在组件设置 fullscreen 属性，并满足 1.变量名与属性一致 或 2.使用 .sync 修饰符
 * 
 * 示例： <el-dialog
 *          v-zoom-dialog
 * 1.       :fullscreen="fullscreen"
 * 2.       :fullscreen.sync="anyFullscreen"
 *          @zoom-in="zoomInFunc"        // 窗口变化的事件
 *        >
 *          // 弹窗内容
 *        </el-dialog>
 */
export default {
  bind(el, binding, vnode) {
    const { fullscreen } = vnode.componentOptions.propsData
    // 需要在组件设置 fullscreen 来实现窗口最大化和还原功能
    if (typeof fullscreen == 'undefined') {
      console.error('请设置 fullscreen 属性(变量名与属性一致), v-zoom-dialog 指令才能生效')
      return
    }
    // dialog 的标题栏
    const dialogHeaderEl = el.querySelector('.el-dialog__header')
    // dialog 所在组件的 vue 实例
    const vm = vnode.context
    // dialog 实例
    const dialogVm = vnode.child

    // 创建最大化/还原 按钮
    const zoomBtn = document.createElement('button')
    const maximise = vm.$t('button.maximise')
    const restore = vm.$t('button.restore')
    zoomBtn.className += 'el-dialog__headerbtn'
    zoomBtn.style.right = '40px'
    zoomBtn.title = vm.fullscreen ? restore : maximise
    
    const iconClass = vm.fullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'
    zoomBtn.innerHTML = `<i class="${iconClass}" onMouseOver="this.style.color=\'#409EFF\'" onMouseOut="this.style.color=\'inherit\'"></i>`
    dialogHeaderEl.insertBefore(zoomBtn, dialogHeaderEl.childNodes[1])

    // 最大化/还原 方法
    function zoom(e) {
      if (vm.fullscreen) {
        const i = zoomBtn.querySelector('.el-icon-copy-document')
        i.className = 'el-icon-full-screen'
        zoomBtn.title = maximise
        updateFullscreen(false)
        dialogHeaderEl.style.cursor = 'move'
        dialogVm.$emit('zoom-in', false)
      } else {
        const i = zoomBtn.querySelector('.el-icon-full-screen')
        i.className = 'el-icon-copy-document'
        zoomBtn.title = restore
        updateFullscreen(true)
        dialogHeaderEl.style.cursor = 'initial'
        dialogVm.$emit('zoom-in', true)
      }
    }

    function updateFullscreen(boolean) {
      // 1.变量名与属性一致
      vm.fullscreen = boolean
      // 2.使用 .sync 修饰符
      dialogVm.$emit('update:fullscreen', boolean)
    }
 
    // 点击放大缩小效果
    zoomBtn.onclick = zoom
    // 双击头部效果
    dialogHeaderEl.ondblclick = zoom
  }
}
