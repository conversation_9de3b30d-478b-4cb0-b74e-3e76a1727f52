/**
 * 输入框（input，textarea）失去焦点或者按下回车键（仅input）时，去除文本前后的空格
 * 使用 <el-input v-model="xxx" v-trim></el-input>
 */
// 获取组件下的 input、textarea 元素
function getInput(el) {
  let inputEle
  if (el && el.tagName !== 'INPUT') {
    inputEle = el.querySelector('input') || el.querySelector('textarea')
  } else {
    inputEle = el
  }
  return inputEle
}
// 使用 js代码 触发事件的方法
function dispatchEvent(el, type) {
  const evt = document.createEvent('HTMLEvents')
  evt.initEvent(type, true, true)
  el.dispatchEvent(evt)
}
export default {
  inserted: el => {
    const inputEle = getInput(el)
    // blur 事件
    const handler = function(event) {
      const newVal = event.target.value.trim()
      if (event.target.value != newVal) {
        event.target.value = newVal
        dispatchEvent(inputEle, 'input')
      }
    }
    // 按下回车键 的事件
    const enterHandler = function(event) {
      if (event.key === 'Enter' && inputEle.type === 'text') {
        handler(event)
      }
    }
    el.inputEle = inputEle
    el._blurHandler = handler
    el._enterHandler = enterHandler
    inputEle.addEventListener('blur', handler)
    inputEle.addEventListener('keydown', enterHandler)
  },
  unbind(el) {
    const { inputEle } = el
    inputEle.removeEventListener('blur', el._blurHandler)
    inputEle.removeEventListener('keydown', el._enterHandler)
  }
}
