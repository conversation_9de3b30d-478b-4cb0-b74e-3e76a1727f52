import drag from './el-dragDialog'
import trim from './trim'
import zoom from './zoomDialog'
import permission from './permission'
import appendTooltip from './appendTooltip'

export default {
  install(Vue) {
    Vue.directive('el-drag-dialog', drag)
    Vue.directive('trim', trim)
    Vue.directive('zoom-dialog', zoom)
    Vue.directive('permission', permission)
    Vue.directive('append-tooltip', appendTooltip)
  }
}
