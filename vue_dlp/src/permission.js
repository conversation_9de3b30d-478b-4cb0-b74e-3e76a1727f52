import Vue from 'vue'
import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // 进度条
import 'nprogress/nprogress.css' // 进度条
import { getBackupRecovering, getIsRecovering, setIsRecovering } from '@/api/system/terminalManage/manageLibrary'
import Cookies from 'js-cookie'
import i18n from '@/lang'
import { updateI18nData } from '@/lang/index'
import { deepClone } from '@/utils'
import { getToken } from '@/utils/auth' // get token from cookie
// import { base64DecodeSpe } from '@/utils/encrypt';
import { getPageTitle } from '@/utils/i18n'
import { getUrlParam } from '@/utils/nacToDlp'

// NProgress Configuration  不显示转圈的图片
NProgress.configure({ showSpinner: false })

// 不重定向白名单,未登录也可访问
const whiteList = ['/login', '/autoLogin', '/findPassword', '/recovering']
// 缓存动态添加的路由，用于追加子路由时使用（如：征兆报表）
const dynamicRoutes = []

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()

  // TODO 从NAC跳转回DLP的业务流程放这里不合适，考虑使用报表跳转回DLP的方式，使用 autoLogin 路由
  const param = window.location.hash.split('#')[1];
  let hasToken;
  if (param.indexOf('?') !== -1) {
    const token = getUrlParam('token')
    const ticket = getUrlParam('ticket')
    if (token !== null && token !== 'undefined') {
      console.log('执行：updateI18nData')
      // 特殊场景：TRDLP-17630 当清除浏览器缓存且从nac登录切换到DLP控制台时，告警弹窗多语言和铃铛的多语言显示为英文
      // 重新加载多语言数据
      updateI18nData()
      hasToken = token
      // sessionStorage.setItem('token', token)
      store.dispatch('user/setToken', token)
    }
    if (ticket && ticket !== 'undefined') {
      const expireTime = new Date(new Date().getTime() + 60 * 1000);
      Cookies.set('from_nac_to_dlp', true, { expires: expireTime })
      sessionStorage.setItem('ticket', ticket)
      Cookies.set('vue_dlp_up', ticket)
    }
  }

  // 确定用户是否已登录
  if (!hasToken || hasToken === 'undefined') {
    hasToken = getToken()
  }
  // 保留日志打印，方便查看通过url获取的token
  console.log('hasToken', hasToken)

  // 获取系统资源
  const resourcesLoaded = store.getters.resourcesLoaded
  if (!resourcesLoaded) store.dispatch('user/getSysResources')

  // 设置浏览器窗口标题
  document.title = getPageTitle(to.meta.title)

  //  数据库正在恢复时，无法跳转到其他界面  恢复状态： 3/正在恢复
  const recovering = await getRecovering()
  if (recovering) {
    if (to.path !== '/recovering') {
      next('/recovering')
    } else {
      next()
    }
    NProgress.done()
    return;
  } else if (to.path === '/recovering') {
    if (from.path === '/recovering') {
      next('/login')
    } else {
      next(from.path)
    }
    NProgress.done();
    return;
  }

  // 确定用户是否已登录
  // const hasToken = getToken()
  if (hasToken && hasToken !== 'null' && hasToken !== 'undefined') {
    // 如果从nac跳转过来时，浏览器cookie已存在vue_dlp_token,清除链接上的ticket
    let url = window.location.href;                    // 获取当前页面的url
    if (url.indexOf('ticket') != -1) {                        // 判断是否存在参数
      url = url.replace(/(\?|#|login)[^'"]*/, '#/login');           // 去除参数
      window.history.pushState({}, 0, url);
      // Cookies.set('from_nac_to_dlp', true)
    }
    if (to.path === '/login') {
      // 如果已登录，则重定向到主页
      next({ path: '/' })
      NProgress.done()
    } else {
      // 确定用户是否通过getInfo获得了他的权限角色
      const { roles } = store.getters
      const hasRoles = roles && roles.length > 0
      if (hasRoles) {
        const { visitedViews, pageLimit, closeType } = store.getters
        const isRouterVisited = visitedViews.some(view => view.name === to.name)
        const redirect = to.path.indexOf('/redirect') == 0
        // 打开的页面超过上限时，根据配置选择处理方式
        if (!isRouterVisited && !redirect && visitedViews.length > pageLimit) {
          if (closeType == 1) { // 自动关闭页面
            // 获取固定在tags-view上的页面 name 的 map
            const affixMap = visitedViews.reduce((map, view) => {
              if (view.meta && view.meta.affix) {
                map[view.name] = true
              }
              return map
            }, {})
            // cachedViews 过滤掉固定在tags-view上的页面
            const cachedViews = store.getters.cachedViews.filter(name => !affixMap[name])
            const firstVisited = visitedViews.filter(view => view.name === cachedViews[0])[0]
            store.dispatch('tagsView/delView', firstVisited)
            next()
          } else if (closeType == 2) { // 手动关闭页面
            Vue.prototype.$message({
              message: i18n.t('tagsView.content7'),
              type: 'error',
              duration: 5000
            })
            next(from.path)
          } else { // 未配置，弹窗让用户选择
            const vm = store.getters.tagsViewVm
            vm.firstShow = true
            vm.setting(true)
          }
        } else {
          // 如果从登录页进入的页面是没有权限的页面，控制台会跳转到404页面，此时修改成跳转到首页
          const loginNoPermission = to.path == '/404' && from.path == '/login'
          // 如果在征兆报表页面刷新，由于还未加载到征兆报表的菜单数据，控制台会跳转到404页面，此时修改成跳转到首页
          const signReportRefresh = to.path == '/404' && to.redirectedFrom && to.redirectedFrom.includes('/report/standardSign/statistical')
          if (loginNoPermission || signReportRefresh) {
            next({ path: '/' })
          } else {
            store.dispatch('tagsView/updateCachedViews', to)
            next()
          }
        }
      } else {
        try {
          // 获取用户信息，并提取权限信息
          const { permissions } = await store.dispatch('user/getInfo')
          // 根据角色生成可访问路由映射
          await store.dispatch('permission/getMenuCollection')
          const accessRoutes = await store.dispatch('permission/generateRoutes', permissions)
          // 生成由 menuCode 为key，titleArray 为value的map
          store.dispatch('permission/generateMenuCodeFullTitleMap', permissions)
          store.dispatch('permission/addEnableConfig')

          // 获取基础数据 (非必要，不在登录时请求数据，而是在使用时才去请求，修改方式见 getters.js 顶部注释)
          store.dispatch('commonData/setDeptTree')
          store.dispatch('commonData/setTermNode')
          store.dispatch('commonData/setUserNode')
          store.dispatch('commonData/setStgBaseConfig')
          store.dispatch('commonData/setMstgBaseConfig')
          store.dispatch('commonData/setReportModule')

          // 由于存在使用时再请求数据导致页面显示有问题的情况，暂时恢复下述请求
          store.dispatch('commonData/setTimeOptions')
          store.dispatch('commonData/setSysUserList')
          store.dispatch('commonData/setAlarmRules')
          store.dispatch('commonData/setBackupRules')
          store.dispatch('commonData/setTermStatusMap')
          store.dispatch('commonData/setUserRoleOptions')
          store.dispatch('commonData/setHasMailServer')

          // 动态添加可访问路由
          router.addRoutes(accessRoutes)
          dynamicRoutes.push(...accessRoutes)
          
          // 追加征兆报表路由
          const signReportRoutes = store.dispatch('permission/getSignReport')
          signReportRoutes.then(routes => {
            if (routes.length === 0) return
            // 查找报表路由
            const parentRoute = dynamicRoutes.find(r => r.name === 'Report');
            if (parentRoute) {
              // 深拷贝父路由
              const clonedParent = deepClone(parentRoute)
              const newChildRoute = routes[0].children
              
              // 追加子路由
              clonedParent.children = [
                // ...(clonedParent.children || []),
                ...newChildRoute
              ];
            
              // 动态添加更新后的路由
              router.addRoutes([clonedParent]);
              // 更新缓存的路由
              parentRoute.children.push(...newChildRoute)

              // 强制刷新（可选）
              // router.replace(router.currentRoute)
            } else {
              console.error('父路由未找到！');
            }
          })

          // hack method to ensure that addRoutes is complete
          // 设置replace: true，这样导航就不会留下历史记录
          next({ ...to, replace: true })
        } catch (error) {
          // 删除token，进入登录页面重新登录
          await store.dispatch('user/resetToken')
          sessionStorage.removeItem('token')
          Message.error(error || 'Has Error')
          next(`/login?redirect=${to.fullPath}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免权限白名单内，直接跳转
      next()
    } else {
      if (Cookies.get('isExternal')) {
        Cookies.remove('isExternal')
        // 客户二开：通过第三方渠道打开新窗口跳转到dlp控制台，如果登陆状态失效了（退出登录或超时），则关闭页面，不返回登录页面
        // 通过浏览器实现的打开新页面(a标签 || window.open())，可以被 window.close() 关闭
        window.close()
      }
      // 没有访问权限的其他页面被重定向到登录页面。
      next(`/login?redirect=${to.fullPath}`)
      NProgress.done()
    }
  }
})

/** 获取数据恢复状态是否为1，3，6  1：数据库恢复成功（不代表跳转到登录页面），3：恢复中，6：服务重启  状态的变更  3 -> 1 -> 5 -> null(清空缓存）  **/
async function getRecovering() {
  let flag = getIsRecovering();
  if (flag === undefined || flag === null || flag == 6) {
    try {
      const res = await getBackupRecovering(0);
      flag = res.data.status || 0
    } catch (e) {
      flag = 0
    }
    setIsRecovering(flag)
  }
  flag = parseInt(flag + '')
  return !!flag
}

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
