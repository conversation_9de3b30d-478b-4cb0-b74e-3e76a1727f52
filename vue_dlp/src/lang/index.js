
/* 使用代码代替项目中的文字，实现多语言转换
  html:
    $t('xx.xx') 获取xx.xx的值
    $te('xx.xx') 验证xx.xx是否存在
  js:
    this.$t('xx.xx')
    this.$te('xx.xx')
*/

import Vue from 'vue'
import VueI18n from 'vue-i18n'
import Cookies from 'js-cookie'
import axios from 'axios'
import store from '@/store'
import elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'// element-ui lang
import elementTwLocale from 'element-ui/lib/locale/lang/zh-TW'// element-ui lang
import resource from './defaultResource'
import { detectMissingData, verifyTraditionalResources, validateValue } from '@/utils/i18n'

const Base64 = require('js-base64').Base64

// 由于request.js 文件引用了当前文件，避免互相引用，这里另外创建 axios
const request = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  timeout: 60000
})
const isDev = process.env.NODE_ENV == 'development'
// 开发模式下，对多语言资源进行校验，并打印在浏览器控制台
const validateResource = resource => {
  if (isDev) {
    const { zh, tw, en } = resource
    const twMissingData = detectMissingData(zh, tw)
    const enMissingData = detectMissingData(zh, en)
    const twTraditional = verifyTraditionalResources(tw)
    const validate1 = validateValue(twMissingData)
    const validate2 = validateValue(enMissingData)
    const validate3 = validateValue(twTraditional)
    if (validate1) {
      console.warn('简体-繁体：繁体文件缺少的项：', twMissingData);
    }
    if (validate2) {
      console.warn('简体-英文：英文文件缺少的项：', enMissingData);
    }
    if (validate3) {
      console.warn('繁体翻译与《简繁体名词对照表》翻译不一致的内容：', twTraditional);
    }
    // 存在需要修改的内容时，弹窗提示
    if (!sessionStorage.getItem('i18nNotice') && (validate1 || validate2 || validate3)) {
      sessionStorage.setItem('i18nNotice', true)
      alert('多语言资源存在需要修改的内容，请通过浏览器控制台(F12)查看并修改！')
    }
  }
}

// 生成 多语言下拉框选项
const setLanguageOption = resource => {
  const languageOption = []
  if (typeof resource === 'object') {
    Object.entries(resource).forEach(([lang, data]) => {
      const label = data.languageLabel || lang
      const option = { lang, label }
      languageOption.push(option)
    })
    Vue.prototype.$nextTick(() => {
      store.dispatch('app/setLanguageOption', languageOption)
    })
  }
}

// element 多语言资源
const elementLang = { 
  en: elementEnLocale,
  zh: elementZhLocale,
  tw: elementTwLocale 
}

// 从文件读取的多语言资源
let dlpI18nResource = isDev ? undefined : window.DLP_LANG_RESOURCE
const language = Cookies.get('language') || 'zh'
const messages = {}
// 设置默认资源
messages[language] = { ...elementLang[language], ...resource[language] }

if (dlpI18nResource) {
  try {
    // 如果从文件读取到数据，则解码并替换
    const parseData = JSON.parse(Base64.decode(dlpI18nResource))
    messages[language] = { ...elementLang[language], ...parseData[language] }

    // 生成 多语言下拉框选项
    setLanguageOption(parseData)
    
    // 校验多语言资源
    validateResource(parseData)
  } catch (error) {
    // 未能正确解码，则将 dlpI18nResource 改为 undefined
    dlpI18nResource = undefined
    console.error('读取文件多语言资源异常，尝试从 localStorage 获取。');
  }
}

// 读取 localStorage 中的数据
const localResource = localStorage.getItem('i18nData')
// 没有资源文件 且 localStorage 中有数据
if (!dlpI18nResource && localResource) {
  try {
    const parseData = JSON.parse(Base64.decode(localResource))
    const languageData = parseData[language]
    // 有数据且包含 buildIn ，则替换
    if (languageData && languageData['buildIn']) {
      messages[language] = { ...elementLang[language], ...languageData }
    }
  } catch (error) {
    console.error('多语言资源解密失败。');
    try {
      const parseData = JSON.parse(localResource)
      const languageData = parseData[language]
      // 有数据且包含 buildIn ，则替换
      if (languageData && languageData['buildIn']) {
        messages[language] = { ...elementLang[language], ...languageData }
      }
    } catch (error) {
      console.error('多语言资源不是JSON字符串。');
    }
  }
}

// 清除 DLP_LANG_RESOURCE 数据
window.DLP_LANG_RESOURCE = undefined

Vue.use(VueI18n)
// 初始化 VueI18n 实例
const i18n = new VueI18n({
  // set locale
  // options: en | zh
  locale: language,
  // set locale messages
  messages: messages
})

let reloadTimes = 0
if (!dlpI18nResource) {
  // 如果从文件读取不到数据，则调用 updateI18nData 更新数据
  updateI18nData();
}

// 从后台获取多语言数据，并更新到本地缓存中
export function updateI18nData() {
  request({
    url: '/i18n/data',
    method: 'get'
  }).then(res => {
    const data = res.data.data
    if (!data) return
    const parseData = JSON.parse(Base64.decode(data))
    const i18nData = { ...messages[language], ...parseData[language] }
    // 因为切换语言需要刷新浏览器，所以只设置当前语言的资源
    i18n.setLocaleMessage(language, i18nData);
    const saveData = {}
    saveData[language] = parseData[language]
    localStorage.setItem('i18nData', Base64.encode(JSON.stringify(saveData)))
    console.info('成功请求多语言资源，并更新到 localStorage');

    // 生成 多语言下拉框选项
    setLanguageOption(parseData)

    // 校验多语言资源
    validateResource(parseData)
  }).catch(err => {
    console.error('请求多语言资源失败', err);
    // 多语言请求失败后，重新发送请求
    if (reloadTimes < 5) {
      reloadTimes++
      updateI18nData()
    }
  })
}

export default i18n
