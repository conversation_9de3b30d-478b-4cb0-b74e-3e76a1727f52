import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import i18n from './lang' // Internationalization
import '@/permission' // permission control

import confirmBox from './utils/confirmBox'

import StompSocket from './utils/websocket'

import Funcs from './utils/fun'

import Print from './utils/print'

import Components from './components'
import Directive from './directive'
import Mixins from './mixins'
import { commonStrategyTargetTree } from '@/settings'

/**
 *如果不想使用mock-server
 *您希望使用MockJs作为模拟api
 *您可以执行:mockXHR()
 *
 *目前MockJs将在生产环境中使用，
 *请在上线前删除!!!
 */
// import { mockXHR } from '../mock'
// if (process.env.NODE_ENV === 'production') {
//   mockXHR()
// }

// ElementUI 组件添加 mixins
ElementUI.Input.mixins.push(Mixins.inputLimit)
ElementUI.Form.mixins = [Mixins.formMixins]
ElementUI.FormItem.mixins.push(Mixins.formItemMixins)

// 将ElementUI语言设置为locale, 语言由导入文件决定
Vue.use(ElementUI, {
  i18n: (key, value) => i18n.t(key, value)
})

Vue.config.productionTip = false
Vue.config.devtools = true;
Vue.prototype.$confirmBox = confirmBox
Vue.prototype.$message = function(msg) {
  return ElementUI.Message({ showClose: true, ...msg })  // 消息提示全局配置可关闭
}

Vue.use(Funcs)
Vue.use(Directive)

const socket = new StompSocket(process.env.VUE_APP_BASE_API + '/websocketJS')
socket.debugEnable(true)
socket.setSubscribeTimeout(60000)
// 由于登录页也需要用到websocket，因此需要在此连接：注册业务
socket.initAndConn()
// socket.connect() // 在登录后再执行连接，方便带上token进行身份认证
Vue.prototype.$socket = socket

// 非公共策略应用树，作为各个页面策略应用树的if条件，可通过 commonStrategyTargetTree 切换使用公共组件或页面中的组件
Vue.prototype.__NONCOMMON_STRATEGY_TARGET_TREE__ = !commonStrategyTargetTree

Vue.use(Print)
Vue.use(Components)

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})
