import { getPropertyByCode } from '@/api/property.js'
import confirmBox from '../../utils/confirmBox'

export const cloudEnableMixins = {
  beforeRouteEnter(to, from, next) {
    // 这里无法获取$store，所以每次点击时都会到后台获取配置的值
    const key = 'cloud.enable'
    getPropertyByCode(key).then(res => {
      const cloudEnable = res.data ? res.data.value : 'false'
      if (cloudEnable && cloudEnable.toLowerCase() === 'true') {
        next()
      } else {
        confirmBox('该功能需要连接云服务（联网），请确定是否连接。可在云服务管理界面开启, 跳转到配置界面？')
          .then(() => {
            console.log(to, from)
            // 这里跳转到配置界面, 这里不用能this, 改成next(path)
            // this.$router.push({ name: 'cloudServer' })
            next('/system/deviceManage/cloudServer')
          })
        next(false)
      }
    })
  },
  methods: {

  }
};
