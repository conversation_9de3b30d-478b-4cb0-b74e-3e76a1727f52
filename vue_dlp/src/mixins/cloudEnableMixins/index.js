import confirmBox from '../../utils/confirmBox'
import { getCloudEnableStatus } from '@/api/system/deviceManage/cloudServiceApply';

export const cloudEnableMixins = {
  beforeRouteEnter(to, from, next) {
    // 从后端获取到云服务的开启状态
    getCloudEnableStatus().then(res => {
      const cloudEnable = res.data ? res.data : false
      if (cloudEnable && cloudEnable === true) {
        next()
      } else {
        confirmBox('该功能需要连接云服务（联网），请确定是否连接。可在云服务管理界面开启, 跳转到配置界面？')
          .then(() => {
            console.log(to, from)
            // 这里跳转到配置界面, 这里不用能this, 改成next(path)
            // this.$router.push({ name: 'cloudServer' })
            next('/system/deviceManage/cloudServer')
          })
        next(false)
      }
    })
  },
  methods: {

  }
};
