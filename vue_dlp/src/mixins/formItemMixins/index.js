/**
 * form-item 组件的 mixin
 * 
 */
const formItemMixins = {
  props: {
  },
  data() {
    return {
      messageValue: ''
    }
  },
  created() {
    if (!this.isDev()) return
    this.$nextTick(() => {
      this.validateTag()
    })
  },
  methods: {
    // 验证 formItem 组件是否使用了封装的 FormItem 组件
    validateTag() {
      const parentComponent = this.$parent
      const parentTag = parentComponent.$options._componentTag
      if (parentTag !== 'FormItem') {
        this.$el.style.background = '#f17676'
        const message = '请使用<FormItem>标签代替<el-form-item>'
        if (this.messageValue == message) return
        this.messageValue = message

        this.$message({
          message,
          type: 'error',
          duration: 10000,
          onClose: () => {
            this.messageValue = ''
          }
        })
      }
    }
  }
}
export default formItemMixins
