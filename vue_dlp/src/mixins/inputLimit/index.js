/**
 * input、textarea 限制输入字符的 mixin
 * 增加 props: {
 *  noLimit       // input 是否不限制输入 limitRegExp 规定的内容以外的字符，true为不限制，默认值 false
 *  limitRegExp   // input 允许输入的字符的正则表达式
 * }
 *
 */
import { limitRegExp } from './limitRegExp.js'
import i18n from '@/lang'

function getInput(el) {
  let inputEle
  if (el && el.tagName !== 'INPUT') {
    inputEle = el.querySelector('input') || el.querySelector('textarea')
  } else {
    inputEle = el
  }
  return inputEle
}

const inputLimit = {
  props: {
    // input 是否不限制输入 limitRegExp 规定的内容以外的字符，true为不限制，默认值 false
    noLimit: {
      type: Boolean,
      default() {
        return false
      }
    },
    // input 允许输入的字符的正则表达式
    limitRegExp: {
      type: RegExp,
      default() {
        return limitRegExp
      }
    }
  },
  data() {
    return {
      messageValue: ''              // 提示消息的内容，用于防止弹出多条重复信息
    }
  },
  computed: {
    dontValidateValue() {
      return this.noLimit || (this.type != 'text' && this.type != 'textarea')
    }
  },
  watch: {
    '$props.value'(val) {
      if (this.dontValidateValue) return
      this.validateValue()
    }
  },
  created() {
    this.validateMaxlength()
    if (this.dontValidateValue) return
    this.$nextTick(() => {
      const el = getInput(this.$el)
      if (!el) return
      this.addEvent(el)
      this.validateValue()
    })
  },
  methods: {
    // 添加 input、blur 事件
    addEvent(el) {
      // 添加 input 监听事件
      el.addEventListener('input', (event) => {
        const value = event.target.value || ''
        // 使用 this.limitRegExp 规则替换掉限制输入的内容
        const newValue = String(value).replace(this.limitRegExp, '')
        if (value != newValue) {
          this.notice(value)
        }
      })
      // 添加 blur 监听事件
      el.addEventListener('blur', (event) => {
        this.$nextTick(() => {
          const value = event.target.value || ''
          // 使用 this.limitRegExp 规则替换掉限制输入的内容
          const newValue = String(value).replace(this.limitRegExp, '')
          if (value != newValue) {
            this.$set(event.target, 'value', newValue)
            this.notice(value)
            // 触发 input 事件，使 v-model 绑定的值同步更新
            event.target.dispatchEvent(new Event('input'))
          }
        })
      })
    },
    // 验证 value 是否包含限制输入的内容
    validateValue() {
      const inputEl = getInput(this.$el)
      const value = this.value || ''
      // 使用 this.limitRegExp 规则替换掉限制输入的内容
      const newValue = String(value).replace(this.limitRegExp, '')
      if (newValue) {
        inputEl.title = newValue
      } else {
        inputEl.title = inputEl.placeholder
      }
      if (value != newValue) {
        this.$set(inputEl, 'value', newValue)
        this.notice(value)
        // 触发 input 事件，使 v-model 绑定的值同步更新
        inputEl.dispatchEvent(new Event('input'))
      }
    },
    // 弹出提示信息
    notice(value) {
      if (this.messageValue == value) return
      this.messageValue = value
      this.$message({
        message: i18n.t('components.inputNotice', { text: value }),
        type: 'error',
        duration: 5000,
        onClose: () => {
          this.messageValue = ''
        }
      })
    },
    // 验证表单中的输入框是否设置了 maxlength 属性
    validateMaxlength() {
      if (!this.isDev()) return
      const isFormInput = !!this.elFormItem
      const hasMaxlength = this.$attrs.hasOwnProperty('maxlength')
      const disabled = this.disabled || this.readonly
      if (isFormInput && !hasMaxlength && !disabled) {
        const inputEl = getInput(this.$el)
        if (!inputEl) return
        inputEl.style.background = '#f17676'
        const message = '背景为红色的输入框需要设置 maxlength, 请根据数据库字段长度进行修改！不需要限制长度的输入框，可只添加 maxlength 属性不设置值。'
        if (this.messageValue == message) return
        this.messageValue = message
        this.$message({
          message,
          type: 'error',
          duration: 10000,
          onClose: () => {
            this.messageValue = ''
          }
        })
      }
    },
    // todo 密码大写输入提示
    capitalTip(id) {
      // $('#' + id).after('<div style="width:130px;height:30px;line-height:30px;padding:0 2px 2px 26px;position:absolute;color:#124fed;display:none;overflow:hidden;z-index:4;background: url(resources/css/images/capslock.png) no-repeat;" class="capslock" id="capital_'+ id +'"><span>大写锁定已开启</span></div>');
      // var capital = false; //聚焦初始化，防止刚聚焦时点击Caps按键提示信息显隐错误
  
      // // 获取大写提示的标签，并提供大写提示显示隐藏的调用接口
      // var capitalTip = {
      //   $elem: $('#capital_'+id),
      //   toggle: function (s) {
      //     if(s === 'none'){
      //       this.$elem.hide();
      //     }else if(s === 'block'){
      //       this.$elem.show();
      //     }else if(this.$elem.is(':hidden')){
      //       this.$elem.show();
      //     }else{
      //       this.$elem.hide();
      //     }
      //   }
      // }
      // $('#' + id).on('keydown.caps',function(e){
      //   if (e.keyCode === 20 && capital) { // 点击Caps大写提示显隐切换
      //     capitalTip.toggle();
      //   }
      // }).on('focus.caps',function(){capital = false;}).on('keypress.caps',function(e){capsLock(e);}).on('blur.caps',function(e){
      //   //输入框失去焦点，提示隐藏
      //   capitalTip.toggle('none');
      // });
      // function capsLock(e){
      //   var keyCode = e.keyCode || e.which;// 按键的keyCode
      //   var isShift = e.shiftKey || keyCode === 16 || false;// shift键是否按住
      //   if(keyCode === 9){
      //     capitalTip.toggle('none');
      //   }else{
      //     //指定位置的字符的 Unicode 编码 , 通过与shift键对于的keycode，就可以判断capslock是否开启了
      //     // 90 Caps Lock 打开，且没有按住shift键
      //     if (((keyCode >= 65 && keyCode <= 90) && !isShift) || ((keyCode >= 97 && keyCode <= 122) && isShift)) {
      //       // 122 Caps Lock打开，且按住shift键
      //       capitalTip.toggle('block'); // 大写开启时弹出提示框
      //       capital = true;
      //     } else {
      //       capitalTip.toggle('none');
      //       capital = true;
      //     }
      //   }
      // }
    }
  }
}
export default inputLimit
