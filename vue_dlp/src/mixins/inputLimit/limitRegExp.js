/**
 * input限制可以输入的内容的正则表达式
 * 参考 https://blog.csdn.net/weixin_43933446/article/details/132490854
 * 【常用字符】数字 字母
 * 【C0控制符及基本拉丁文】【\u0000-\u007F】
 * 【C1控制符及拉丁文补充】【\u0080-\u00FF】
 * 【常用标点】【\u2000-\u206F】
 * 【罗马数字】【\u2160-\u217f】
 * 【箭头】【\u2190-\u21FF】
 * 【封闭式字母数字】【\u2460-\u24FF】
 * 【CJK 符号和标点】【\u3000-\u303F】
 * 【日文】【\u3040-\u30FF\u31F0-\u31FF】
 * 【中文】【\u4e00-\u9fa5】
 * 【朝鲜文】【\u1100-\u11FF\u3130-\u318F\uAC00-\uD7AF】
 * 【半型及全型形式】【\uFF00-\uFFEF】
 * @type {RegExp}
 */
// export const limitRegExp = /[^\d^\[a-zA-Z\]\u0000-\u007F\u0080-\u00FF\u2000-\u206F\u2160-\u217f\u2190-\u21FF\u2460-\u24FF\u3000-\u303F\u3040-\u30FF\u31F0-\u31FF\u4e00-\u9fa5\u1100-\u11FF\u3130-\u318F\uAC00-\uD7AF\uFF00-\uFFEF]/g;

// 放开限制范围，大部分基本平面的字符都允许输入
// 参考 https://www.ruanyifeng.com/blog/2014/12/unicode.html
// UTF-16编码规则，辅助平面的字符由以下2个范围的码点组成
// 【\uD800-\uDBFF】# High-half zone of UTF-16
// 【\uDC00-\uDFFF】# Low-half zone of UTF-16
export const limitRegExp = /[^\d^\[a-zA-Z\]\u0000-\uD7AF\uE000-\uFFEF]/g;
