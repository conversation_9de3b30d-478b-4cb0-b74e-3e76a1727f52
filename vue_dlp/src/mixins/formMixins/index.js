/**
 * form 组件的 mixin
 * 
 */
const formMixins = {
  props: {
  },
  data() {
    return {
      messageValue: ''
    }
  },
  created() {
    if (!this.isDev()) return
    this.$nextTick(() => {
      this.validateTag()
    })
  },
  methods: {
    // 验证 form 组件是否使用了封装的 Form 组件
    validateTag() {
      const parentComponent = this.$parent
      const parentTag = parentComponent.$options._componentTag
      if (parentTag !== 'Form') {
        this.$el.style.background = '#f17676'
        const message = '请使用<Form>标签代替<el-form>'
        if (this.messageValue == message) return
        this.messageValue = message

        this.$message({
          message,
          type: 'error',
          duration: 10000,
          onClose: () => {
            this.messageValue = ''
          }
        })
      }
    }
  }
}
export default formMixins
