import { entityLink } from '@/utils';

export const auditLogRouterMixin = {

  beforeRouteEnter(to, from, next) {
    if (to.query) {
      next(vm => {
        vm.navigate(vm, to.query)
      })
    }
  },
  created() {
    // 这里简单通过判断传过来的参数如果有createDate,那么就认为是要查询的，
    // 所以将autoload设置为false，那么在生成gridTable时，就不会自动加载数据
    // 使用此混入的，都需要加上autoload 才可以生效
    if (this.$route.query && this.$route.query.createDate && this.autoload) {
      this.autoload = false
    }
  },
  methods: {
    navigate(vm, queryParam) {
      vm.query = Object.assign(vm.query, queryParam)
      const { objectType, objectId } = queryParam
      const timeQuery = vm.$refs.timeQuery || vm.$refs.searchToolbar
      timeQuery && timeQuery.setDate(queryParam.createDate)
      vm.$nextTick(() => {
        entityLink({ entityType: objectType, entityId: objectId }, {}, vm)
      })
    }
  }
};
