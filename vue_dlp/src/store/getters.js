import Vue from 'vue'

// 部分数据在使用时，通过 getters 获取值时，判断是否已加载数据，若未加载则使用 Vue.prototype.$store.dispatch 去加载数据，并返回默认数据。
// 官方不建议使用该方式，原因：1. 违背设计原则 2. 响应式问题。但确实是可以实现的，只是需要注意下述事项。
// 注意事项：必须保证加载回来的数据与默认数据不同，使判断未加载数据的代码只执行一次。

// 按文件顺序填写
const getters = {
  // app.js
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  language: state => state.app.language,
  languageOption: state => state.app.languageOption,

  // commonData.js
  timeOptions: state => {
    if (!state.commonData.timeOptions) {
      Vue.prototype.$store.dispatch('commonData/setTimeOptions')
    }
    return state.commonData.timeOptions || []
  },
  roleTree: state => state.commonData.roleTree,
  deptTree: state => state.commonData.deptTree,
  deptTreeList: state => state.commonData.deptTreeList,
  termTree: state => state.commonData.termTree,
  termTreeList: state => state.commonData.termTreeList,
  logTermTree: state => state.commonData.logTermTree,
  logTermTreeList: state => state.commonData.logTermTreeList,
  userTree: state => state.commonData.userTree,
  userTreeList: state => state.commonData.userTreeList,
  // <--- todo 部分变量已废弃，后续删除，删除前需要全局搜索是否未使用
  deptIdMap: state => state.commonData.deptIdMap,
  deptTreeMap: state => state.commonData.deptTreeMap,
  userNodes: state => state.commonData.userNodes,
  termNodes: state => state.commonData.termNodes,
  // --->
  termStatusMap: state => {
    if (!state.commonData.termStatusMap) {
      Vue.prototype.$store.dispatch('commonData/setTermStatusMap')
    }
    return state.commonData.termStatusMap || {}
  },
  userStatusMap: state => {
    if (!state.commonData.userStatusMap) {
      Vue.prototype.$store.dispatch('commonData/setTermStatusMap')
    }
    return state.commonData.userStatusMap || {}
  },
  sysUserList: state => {
    if (!state.commonData.sysUserList) {
      Vue.prototype.$store.dispatch('commonData/setSysUserList')
    }
    return state.commonData.sysUserList || []
  },
  alarmRules: state => {
    if (!state.commonData.alarmRules) {
      Vue.prototype.$store.dispatch('commonData/setAlarmRules')
    }
    return state.commonData.alarmRules || []
  },
  stgBaseConfig: state => state.commonData.stgBaseConfig,
  mstgBaseConfig: state => state.commonData.mstgBaseConfig,
  wsNoticeInfo: state => state.commonData.wsNoticeInfo,
  notice: state => state.commonData.notice,
  doctrackProcesses: state => state.commonData.doctrackProcesses,
  trialDlp: state => state.commonData.trialDlp,
  cloudAddress: state => state.commonData.cloudAddress,
  reportTypeChange: state => state.commonData.reportTypeChange,
  hasMailServer: state => {
    if (typeof state.commonData.hasMailServer === 'undefined') {
      Vue.prototype.$store.dispatch('commonData/setHasMailServer')
      return true
    }
    return !!state.commonData.hasMailServer
  },
  reportModule: state => state.commonData.reportModule,
  engineOsType: state => state.commonData.engineOsType,
  isWindows: state => state.commonData.isWindows,
  alarmTemplateParams: state => state.commonData.alarmTemplateParams,
  backupRules: state => {
    if (!state.commonData.backupRules) {
      Vue.prototype.$store.dispatch('commonData/setBackupRules')
    }
    return state.commonData.backupRules || []
  },
  saleModuleIds: state => state.commonData.saleModuleIds,
  masterDbType: state => state.commonData.masterDbType,
  alarmMsg: state => state.commonData.alarmMsg,
  softwareCategories: state => state.commonData.softwareCategories,
  softwareRepositoryVer: state => state.commonData.softwareRepositoryVer,
  userRoleOptions: state => {
    if (!state.commonData.userRoleOptions) {
      Vue.prototype.$store.dispatch('commonData/setUserRoleOptions')
    }
    return state.commonData.userRoleOptions || []
  },
  searchCondition: state => {
    if (!state.commonData.searchCondition) {
      Vue.prototype.$store.dispatch('commonData/setSearchCondition')
    }
    return state.commonData.searchCondition || []
  },

  // downloader.js
  downloadFiles: state => state.downloader.downloadFiles,
  downloadLogEnabled: state => state.downloader.downloadLogEnabled,
  downloadListVisible: state => state.downloader.downloadListVisible,
  streamDownloadErrorCount: state => state.downloader.streamDownloadErrorCount,

  // permission.js
  permission_routes: state => state.permission.routes,
  common_routes: state => state.permission.commonRoutes,
  routesPath: state => state.permission.routesPath,
  routesMap: state => state.permission.routesMap,
  menuCodeFullTitleMap: state => state.permission.menuCodeFullTitleMap,
  menuCodeMapper: state => state.permission.menuCodeMapper,
  auditingDeleteAble: state => state.permission.auditingDeleteAble,
  adminLogDeleteAble: state => state.permission.adminLogDeleteAble,
  adminValidConfigAble: state => state.permission.adminValidConfigAble,
  userValidConfigAble: state => state.permission.userValidConfigAble,
  desensitizeContentAble: state => state.permission.desensitizeContentAble,

  // settings.js
  version: state => state.settings.version,
  tagsView: state => state.settings.tagsView,
  devI18nMode: state => state.settings.devI18nMode,
  allPermission: state => state.settings.allPermission,
  logoutWhenClose: state => state.settings.logoutWhenClose,

  // tagsView.js
  commonVm: state => state.tagsView.commonVm,
  currentVm: state => state.tagsView.currentVm,
  stgTargetCheckedNodeMap: state => state.tagsView.stgTargetCheckedNodeMap,
  tagsViewVm: state => state.tagsView.tagsViewVm,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  viewTimestamp: state => state.tagsView.viewTimestamp,

  // user.js
  token: state => state.user.token,
  name: state => state.user.name,
  account: state => state.user.account,
  avatar: state => state.user.avatar,
  roles: state => state.user.roles,
  roleList: state => state.user.roleList,
  currentRoleId: state => state.user.currentRoleId,
  userId: state => state.user.userId,
  isSuperMode: state => state.user.isSuperMode,
  isSuperRole: state => state.user.isSuperRole,
  isSysRole: state => state.user.isSysRole,
  isSuperThreeUser: state => state.user.isSuperThreeUser,
  isMasterSubAble: state => state.user.isMasterSubAble,
  isI18nMode: state => state.user.isI18nMode,
  userMenuCodes: state => state.user.userMenuCodes,
  userMenuCodesMap: state => state.user.userMenuCodesMap,
  upPwdTime: state => state.user.upPwdTime,
  sysResources: state => state.user.sysResources,
  resourcesLoaded: state => state.user.resourcesLoaded,
  theme: state => state.user.theme,
  pageLimit: state => state.user.pageLimit,
  closeType: state => state.user.closeType,
  cacheSelectedNode: state => state.user.cacheSelectedNode,
  cacheTimeQuery: state => state.user.cacheTimeQuery,
  cacheDateObj: state => state.user.cacheDateObj,
  password: state => state.user.password,
  cleanDuration: state => state.user.cleanDuration,
  requestTimeout: state => state.user.requestTimeout,
  serverTime: state => state.user.serverTime,
  browserTime: state => state.user.browserTime
}
export default getters
