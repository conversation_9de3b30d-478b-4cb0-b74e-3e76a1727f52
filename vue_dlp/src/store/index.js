import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import app from './modules/app'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import user from './modules/user'
import commonData from './modules/commonData'
import downloader from './modules/downloader'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    permission,
    tagsView,
    settings,
    user,
    commonData,
    downloader
  },
  getters
})

// 将 store 实例存到 Vue.prototype, 便于在 getters 中使用。
Vue.prototype.$store = store

export default store
