import Vue from 'vue'
import i18n from '@/lang'
import { getTimeInfoOptions } from '@/api/system/baseData/timeInfo'
import { listSysRoleTreeNode } from '@/api/system/organizational/role'
import { getDeptTree } from '@/api/system/terminalManage/department'
import { getAllUserNode } from '@/api/system/terminalManage/user'
import { getTermNodeMap, getTerminalStatus
  // , getUserStatus
} from '@/api/system/terminalManage/terminal'
import { getSysUserList, getRules } from '@/api/system/baseData/alarmSetup'
import { getBackupRules } from '@/api/system/baseData/backupRule'
import { getMap as getStgBaseConfigMap, getMstgMap } from '@/api/system/baseData/strategyType'
import { isTrialVersion, getIsWindows } from '@/api/system/register/reg'
import { listSaleModuleId } from '@/api/system/terminalManage/moduleConfig'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import { listCategories } from '@/api/system/terminalManage/softwareCategory'
import { getUserRoleTreeNode } from '@/api/system/terminalManage/userRole'
import { hasMailServer } from '@/api/system/deviceManage/mailServer'
import { getSearchCondition } from '@/api/behaviorAuditing/searchCondition'
import {
  mergeNodeMap,
  addRecycleNode,
  addLeafNodeMap,
  getMaxDataVerFromNodeMap,
  getMaxModifyVerFromStatusMap, mergeStatusMap
} from '@/utils/tree'

const state = {
  loadDataStatus: {},             // 加载数据的状态，空值代表没有在加载，有值代表正在加载
  timeOptions: undefined,         // 策略生效时间选项
  roleTree: [],                   // 管理员角色树

  deptTree: [],                   // 部门树
  deptTreeList: undefined,        // 部门列表
  termTree: undefined,            // 终端树数据
  termTreeList: [],               // 终端树数据列表
  logTermTree: undefined,         // 日志终端树数据
  logTermTreeList: undefined,     // 日志终端树数据列表
  userTree: undefined,            // 操作员树数据
  userTreeList: [],               // 操作员树数据列表

  // <--- todo 部分变量已废弃，后续删除，删除前需要全局搜索是否未使用
  deptIdMap: undefined,           // 部门id map
  deptTreeMap: undefined,         // 部门map
  userNodes: undefined,           // 操作员节点信息，key为分组ID,value为操作员节点
  userNodeMaxModifyVer: undefined, // 操作员节点信息的最大修改版本
  termNodes: undefined,           // 终端节点信息，key为分组ID,value为终端节点
  termNodeMaxModifyVer: undefined, // 终端节点信息的最大修改版本
  // --->

  termStatusMap: undefined,       // 终端状态map,只包含在线终端
  termStatusAllMap: undefined,       // 终端状态map,包含离线终端
  termStatusMaxModifyVer: undefined, // 终端状态信息的最大修改版本
  userStatusMap: undefined,       // 用户状态map  userId:terminalId   使用userId对应终端id 再从终端id获取登录状态
  sysUserList: undefined,         // 系统管理员列表
  alarmRules: undefined,          // 违规响应规则
  stgBaseConfig: null,            // 策略基础配置, key为策略编号，value为策略基础配置
  mstgBaseConfig: null,           // 多继承策略基础配置, key为策略编号，value为策略基础配置
  wsNoticeInfo: {},               // websocket消息通知信息，主要是服务端通知终端数据变更
  notice: {},                     // 用于跨组件间通知需要发生变化（做某些操作、重新请求数据等）使用, 通过changeNotice更新key(自定义名称)，在需要变化的组件中监听notice.key
  doctrackProcesses: undefined,   // 文档追踪进程映射关系
  trialDlp: false,                // 是否试用版dlp
  cloudAddress: undefined,        // 云平台地址
  reportTypeChange: false,        // 自定义报表增删改时，通知任务配置的页面，更新自定义报表的内容
  reportModule: false,            // 是否购买报表模块
  hasMailServer: undefined,       // 是否存在邮箱服务器
  bindUser: false,                // 用于上送用户后，通知相关引用进行更新
  engineOsType: null,             // 引擎所在的服务器的操作系统类型
  isWindows: false,               // 是否windows环境
  alarmTemplateParams: [],        // 告警模板变量列表
  backupRules: undefined,         // 备份过滤规则
  saleModuleIds: [],              // 销售模块id
  masterDbType: null,             // 主数据库类型（策略库）
  alarmMsg: undefined,            // 控制台告警数据的guid，用于跳转至对应告警记录
  softwareCategories: [],         // 软件分类
  softwareRepositoryVer: 0,       // 软件库变化版本号
  userRoleOptions: undefined,     // 操作员角色
  searchCondition: null           // 所有保存的搜索条件
}

const mutations = {
  SET_TIME_OPTIONS: (state, options) => {
    state.timeOptions = options
  },
  SET_ROLE_TREE: (state, options) => {
    state.roleTree = options
  },
  SET_DEPT_TREE: (state, options) => {
    state.deptTree = options
  },
  SET_DEPT_TREE_LIST: (state, options) => {
    state.deptTreeList = options
  },
  SET_DEPT_ID_MAP: (state, options) => {
    state.deptIdMap = options
  },
  SET_DEPT_TREE_MAP: (state, options) => {
    state.deptTreeMap = options
  },
  SET_USER_TREE: (state, options) => {
    state.userTree = options
  },
  SET_USER_TREE_LIST: (state, options) => {
    state.userTreeList = options
  },
  SET_USER_NODE: (state, options) => {
    state.userNodes = options.data
    state.userNodeMaxModifyVer = options.maxVer
  },
  SET_TERM_TREE: (state, options) => {
    state.termTree = options
  },
  SET_TERM_TREE_LIST: (state, options) => {
    state.termTreeList = options
  },
  SET_LOG_TERM_TREE: (state, options) => {
    state.logTermTree = options
  },
  SET_LOG_TERM_TREE_LIST: (state, options) => {
    state.logTermTreeList = options
  },
  SET_TERM_NODE: (state, options) => {
    state.termNodes = options.data
    state.termNodeMaxModifyVer = options.maxVer
  },
  SET_TERM_STATUS_MAP: (state, options) => {
    // 缓存所有终端状态信息，包含离线终端
    state.termStatusAllMap = options.data
    // 缓存所有在线终端状态信息
    const termStatusMap = {}
    for (const k in options.data) {
      if (options.data[k].status !== 0) {
        termStatusMap[k] = options.data[k]
      }
    }
    state.termStatusMap = termStatusMap
    state.termStatusMaxModifyVer = options.maxVer
  },
  SET_USER_STATUS_MAP: (state, options) => {
    state.userStatusMap = options
  },
  SET_SYS_USER_LIST: (state, options) => {
    state.sysUserList = options
  },
  SET_ALARM_RULES: (state, options) => {
    state.alarmRules = options
  },
  SET_STG_BASE_CONFIG: (state, options) => {
    state.stgBaseConfig = options
  },
  SET_MSTG_BASE_CONFIG: (state, options) => {
    state.mstgBaseConfig = options
  },
  SET_WS_NOTICE_INFO: (state, options) => {
    for (const k in options) {
      Vue.set(state.wsNoticeInfo, k, options[k])
    }
  },
  CLEAR_WS_NOTICE_INFO: (state, options) => {
    state.wsNoticeInfo = options
  },
  SET_NOTICE: (state, options) => {
    for (const k in options) {
      Vue.set(state.notice, k, options[k])
    }
  },
  SET_DOCTRACK_PROCESSES: (state, options) => {
    state.doctrackProcesses = options
  },
  SET_CLOUD_ADDRESS: (state, options) => {
    state.cloudAddress = options
  },
  SET_REPORT_TYPE_CHANGE: (state, options) => {
    state.reportTypeChange = !state.reportTypeChange
  },
  SET_REPORT_MODULE: (state, options) => {
    state.reportModule = options
  },
  SET_HAS_MAIL_SERVER: (state, options) => {
    state.hasMailServer = options
  },
  SET_BIND_USER: (state, options) => {
    state.bindUser = !state.bindUser
  },
  SET_TRIAL_DLP: (state, options) => {
    state.trialDlp = options
  },
  SET_ENGINE_OS_TYPE: (state, options) => {
    state.engineOsType = options
  },
  SET_IS_WINDOWS: (state, options) => {
    state.isWindows = options
  },
  SET_ALARM_TEMPLATE_PARAMS: (state, options) => {
    state.alarmTemplateParams = options
  },
  SET_BACKUP_RULES: (state, options) => {
    state.backupRules = options
  },
  SET_SALE_MODULE_IDS: (state, options) => {
    state.saleModuleIds = options
  },
  SET_MASTER_DB_TYPE: (state, options) => {
    state.masterDbType = options
  },
  SET_ALARM_MSG: (state, options) => {
    state.alarmMsg = options
  },
  SET_SOFTWARE_CATEGORIES: (state, options) => {
    state.softwareCategories = options
  },
  SET_SOFTWARE_REPOSITORY_VER: (state, options) => {
    state.softwareRepositoryVer++
  },
  SET_USER_ROLE_OPTIONS: (state, options) => {
    state.userRoleOptions = options
  },
  SET_SEARCH_CONDITION: (state, options) => {
    state.searchCondition = options
  },
  ADD_SEARCH_CONDITION: (state, condition) => {
    state.searchCondition.push(condition)
  },
  DEL_SEARCH_CONDITION: (state, condition) => {
    const i = state.searchCondition.findIndex(item => item.id == condition.id)
    state.searchCondition.splice(i, 1)
  }
}
/**
 * 根据部门树和终端（操作员）节点构造终端（操作员）树
 * @param {Array} deptTree 树结构部门数据
 * @param {Object} termOrUserNodeMap <parentId,[]>格式数据，将相同parentId的节点放到同一数组中
 * @param {Boolean} isAddRecycleNode 是否添加回收站节点，并将oriData大于0的节点加入回收站子节点
 */
const toTermOrUserTree = (deptTree, termOrUserNodeMap, isAddRecycleNode) => {
  const groupTree = JSON.parse(JSON.stringify(deptTree))
  const leafNodes = {}
  const recycleChildrenNodes = []
  Object.keys(termOrUserNodeMap).forEach(key => {
    // termOrUserNodeMap这边的parentId是数字，与deptTree的id不匹配，因此需要拼接字符串G
    leafNodes['G' + key] = []
    termOrUserNodeMap[key].forEach(node => {
      if (!node.disabled) {
        if (isAddRecycleNode && node.oriData) {
          // 放到回收站节点下
          recycleChildrenNodes.push(node)
        } else {
          leafNodes['G' + key].push(JSON.parse(JSON.stringify(node)))
        }
      }
    })
    if (leafNodes['G' + key].length === 0) {
      delete leafNodes['G' + key]
    }
  })
  if (isAddRecycleNode) {
    addRecycleNode(groupTree, recycleChildrenNodes)
  }
  return addLeafNodeMap(groupTree, leafNodes, 'id')
}
/**
 * 树结构数组转成平级数组
 * @param {Array} treeDatas 树结构部门数据
 * @param {Array} lists 存储部门数据的数组，数据不包含子节点
 * @param {Object} idMap 存储部门id和子分组id的对象
 */
const treeDatasToLists = (treeDatas, lists = [], idMap = {}) => {
  for (let i = 0; i < treeDatas.length; i++) {
    const treeNode = JSON.parse(JSON.stringify(treeDatas[i]))
    const childNodes = treeNode.children
    delete treeNode.children
    const { id, parentId } = treeNode
    if (id && !idMap[id]) {
      idMap[id] = []
      lists.push(treeNode)
    }
    if (idMap[parentId]) {
      idMap[parentId].push(id)
    } else {
      idMap[parentId] = [id]
    }
    if (childNodes) {
      treeDatasToLists(childNodes, lists, idMap)
    }
  }
  return { lists, idMap }
}
/**
 * 终端树、操作员树数据转化成 list
 * @param {Array} data 树结构数据
 * @returns Array 返回节点数组
 */
const treeDataToList = (treeData = [], treeType, list = []) => {
  for (let i = 0; i < treeData.length; i++) {
    const data = treeData[i];
    // 操作员树去掉 回收站 节点
    if (treeType == 'user' && data.dataId == '-2') {
      treeData.splice(i, 1)
      i--
      continue
    }
    if (data.children) {
      treeDataToList(data.children, treeType, list)
    }

    if (data.type == 'G') {
      // 分组节点属性值修改
      let type
      if ('terminal' === treeType) {
        type = '3'
      } else if ('user' === treeType) {
        type = '4'
      }
      if (type) {
        data.type = type
        data.dataType = 'G'
      }
    } else {
      // 子节点在线状态修改
      if (treeType == 'terminal') {
        data.online = !!state.termStatusMap && !!state.termStatusMap[data.dataId]
      } else if (treeType == 'user') {
        data.online = !!state.userStatusMap && !!state.userStatusMap[data.dataId]
      }
    }

    // 拷贝数据，添加到 list
    const tempData = JSON.parse(JSON.stringify(data))
    delete tempData.children
    list.push(tempData)
  }
  return list
}

const callActionIfNotLoading = (callback, funcName, func) => {
  if (state.loadDataStatus[funcName]) {
    state.loadDataStatus[funcName] += 1
    return
  }
  state.loadDataStatus[funcName] = 1
  func((data) => {
    state.loadDataStatus[funcName] -= 1
    if (typeof callback === 'function') {
      callback(data)
    }
    // 如果call期间，有其他请求，则call成功后，再次运行一次
    // 原因：在本次调用数据返回期间，数据库的数据发生了变更，如果没有再次运行，有可能导致数据不是最新的
    if (state.loadDataStatus[funcName] > 0) {
      state.loadDataStatus[funcName] = 0
      callActionIfNotLoading(callback, funcName, func)
    }
  }).catch(reason => {
    state.loadDataStatus[funcName] = 0
  })
}

const actions = {
  setTimeOptions({ commit }) {
    getTimeInfoOptions().then(res => {
      commit('SET_TIME_OPTIONS', res.data)
    })
  },
  setRoleTree({ commit }, callback) {
    callActionIfNotLoading(callback, 'setRoleTree', (callFunc) => {
      return listSysRoleTreeNode().then(res => {
        commit('SET_ROLE_TREE', res.data)
        if (typeof callFunc === 'function') {
          callFunc(res.data)
        }
      })
    })
  },
  setDeptTree({ commit }, callback) {
    callActionIfNotLoading(callback, 'setDeptTree', (callFunc) => {
      return getDeptTree().then(res => {
        if (JSON.stringify(state.deptTree) !== JSON.stringify(res.data)) {
          commit('SET_DEPT_TREE', res.data)
          actions.setDeptTreeList({ commit })
          setTimeout(() => {
            actions.setTermTree({ commit })
            actions.setLogTermTree({ commit })
            actions.setUserTree({ commit })
          }, 100)
        }
        if (typeof callFunc === 'function') {
          callFunc()
        }
      })
    })
  },
  setDeptTreeList({ commit }, callback) {
    const lists = []
    const idMap = {}
    const data = JSON.parse(JSON.stringify(state.deptTree))
    treeDatasToLists(data, lists, idMap)
    commit('SET_DEPT_TREE_LIST', lists)
    commit('SET_DEPT_ID_MAP', idMap)
    actions.setDeptTreeMap({ commit })
    if (typeof callback === 'function') {
      callback()
    }
  },
  setDeptTreeMap({ commit }, callback) {
    const map = {}
    const list = JSON.parse(JSON.stringify(state.deptTreeList))
    list.forEach(dept => {
      map[dept.id] = dept
    })
    commit('SET_DEPT_TREE_MAP', map)
    if (typeof callback === 'function') {
      callback()
    }
  },
  setUserNode({ commit }, isFull, callback) {
    callActionIfNotLoading(callback, 'setUserNode', (callFunc) => {
      return getAllUserNode(isFull ? null : state.userNodeMaxModifyVer).then(res => {
        commit('SET_USER_NODE', {
          data: mergeNodeMap(isFull ? null : state.userNodes, res.data, 'id'),
          maxVer: getMaxDataVerFromNodeMap(res.data, isFull ? null : state.userNodeMaxModifyVer)
        })
        setTimeout(() => {
          actions.setUserTree({ commit })
        }, 100)
        if (typeof callFunc === 'function') {
          callFunc()
        }
      })
    })
  },
  setUserTree({ commit }, callback) {
    if (!state.deptTree || !state.userNodes) return
    const userTree = toTermOrUserTree(state.deptTree, state.userNodes, false)
    const userTreeList = treeDataToList(userTree, 'user')
    commit('SET_USER_TREE', userTree)
    if (typeof callback === 'function') {
      callback()
    }
    setTimeout(() => {
      actions.setUserTreeList({ commit }, userTreeList)
    }, 100)
  },
  setUserTreeList({ commit }, list, callback) {
    if (!state.userTree) return
    commit('SET_USER_TREE_LIST', list)
  },
  setTermTree({ commit }, callback) {
    if (!state.deptTree || !state.termNodes) return
    const termTree = toTermOrUserTree(state.deptTree, state.termNodes, true)
    const termTreeList = treeDataToList(termTree, 'terminal')
    commit('SET_TERM_TREE', termTree)
    if (typeof callback === 'function') {
      callback()
    }
    setTimeout(() => {
      actions.setTermTreeList({ commit }, termTreeList)
    }, 100)
  },
  setTermTreeList({ commit }, list, callback) {
    if (!state.termTree) return
    commit('SET_TERM_TREE_LIST', list)
  },
  setLogTermTree({ commit }, callback) {
    if (!state.deptTree || !state.termNodes) return
    const termTree = toTermOrUserTree(state.deptTree, state.termNodes, false)
    const termTreeList = treeDataToList(termTree, 'terminal')
    commit('SET_LOG_TERM_TREE', termTree)
    if (typeof callback === 'function') {
      callback()
    }
    setTimeout(() => {
      actions.setLogTermTreeList({ commit }, termTreeList)
    }, 100)
  },
  setLogTermTreeList({ commit }, list, callback) {
    if (!state.logTermTree) return
    commit('SET_LOG_TERM_TREE_LIST', list)
  },
  setTermNode({ commit }, isFull, callback) {
    callActionIfNotLoading(callback, 'setTermNode', (callFunc) => {
      return getTermNodeMap(isFull ? null : state.termNodeMaxModifyVer).then(res => {
        commit('SET_TERM_NODE', {
          data: mergeNodeMap(isFull ? null : state.termNodes, res.data, 'id'),
          maxVer: getMaxDataVerFromNodeMap(res.data, isFull ? null : state.termNodeMaxModifyVer)
        })
        setTimeout(() => {
          actions.setTermTree({ commit })
          actions.setLogTermTree({ commit })
        }, 100)
        if (typeof callFunc === 'function') {
          callFunc()
        }
      })
    })
  },
  setTermStatusMap({ commit }) {
    callActionIfNotLoading(null, 'setTermStatusMap', (callFunc) => {
      return getTerminalStatus(state.termStatusMaxModifyVer).then(res => {
        commit('SET_TERM_STATUS_MAP', { data: mergeStatusMap(state.termStatusAllMap, res.data), maxVer: getMaxModifyVerFromStatusMap(res.data, state.termStatusMaxModifyVer) })
        // 更新终端列表中的终端在线状态
        if (state.termTreeList) {
          const termList = state.termTreeList.map(data => {
            if (data.type == 1) {
              data.online = !!state.termStatusMap[data.dataId]
            }
            return data
          })
          actions.setTermTreeList({ commit }, termList)
        }
        if (state.logTermTreeList) {
          const logTermList = state.logTermTreeList.map(data => {
            if (data.type == 1) {
              data.online = !!state.termStatusMap[data.dataId]
            }
            return data
          })
          actions.setLogTermTreeList({ commit }, logTermList)
        }
        // 根据终端在线信息，更新操作员在线信息
        actions.setUserStatusMap({ commit }, state.termStatusMap)
        if (typeof callFunc === 'function') {
          callFunc()
        }
      })
    })
  },
  setUserStatusMap({ commit }, data) {
    // callActionIfNotLoading(null, 'setUserStatusMap', (callFunc) => {
    //   return getUserStatus().then(res => {
    //     commit('SET_USER_STATUS_MAP', res.data)
    //     // 更新操作员列表中的操作员在线状态
    //     if (state.userTreeList) {
    //       const userList = state.userTreeList.map(data => {
    //         if (data.type == 2) {
    //           data.online = !!res.data[data.dataId]
    //         }
    //         return data
    //       })
    //       actions.setUserTreeList({ commit }, userList)
    //     }
    //     if (typeof callFunc === 'function') {
    //       callFunc()
    //     }
    //   })
    // })
    const userStatusMap = {}
    if (data && typeof data === 'object') {
      const arr = Object.values(data)
      arr.forEach(item => {
        if (item.status === 3) {
          userStatusMap[item.userId] = item.terminalId
        }
      })
    }
    commit('SET_USER_STATUS_MAP', userStatusMap)
    // 更新操作员列表中的操作员在线状态
    if (state.userTreeList) {
      const userList = state.userTreeList.map(data => {
        if (data.type == 2) {
          data.online = !!userStatusMap[data.dataId]
        }
        return data
      })
      actions.setUserTreeList({ commit }, userList)
    }
  },
  // 设置系统管理员列表，并添加 所有管理员 选项
  setSysUserList({ commit }) {
    callActionIfNotLoading(null, 'setSysUserList', (callFunc) => {
      return getSysUserList().then(res => {
        const name = i18n.t('pages.allSysUsers')
        commit('SET_SYS_USER_LIST', [{ name: name, id: 0 }, ...res.data])
        if (typeof callFunc === 'function') {
          callFunc()
        }
      })
    })
  },
  setAlarmRules({ commit }) {
    callActionIfNotLoading(null, 'setAlarmRules', (callFunc) => {
      return getRules().then(res => {
        commit('SET_ALARM_RULES', res.data)
        if (typeof callFunc === 'function') {
          callFunc()
        }
      })
    })
  },
  setBackupRules({ commit }) {
    callActionIfNotLoading(null, 'setBackupRules', (callFunc) => {
      return getBackupRules().then(res => {
        commit('SET_BACKUP_RULES', res.data)
        if (typeof callFunc === 'function') {
          callFunc()
        }
      })
    })
  },
  setStgBaseConfig({ commit }) {
    return new Promise((resolve, reject) => {
      getStgBaseConfigMap().then(res => {
        commit('SET_STG_BASE_CONFIG', res.data)
        resolve(res.data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  setMstgBaseConfig({ commit }) {
    return new Promise((resolve, reject) => {
      getMstgMap().then(res => {
        commit('SET_MSTG_BASE_CONFIG', res.data)
        resolve(res.data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  changeWsNoticeInfo({ commit }, data) {
    commit('SET_WS_NOTICE_INFO', data)
  },
  clearWsNoticeInfo({ commit }, data) {
    commit('CLEAR_WS_NOTICE_INFO', {})
  },
  changeNotice({ commit }, data) {
    const opt = {}
    let val = state.notice[data]
    val = val ? ++val : 1
    opt[data] = val
    commit('SET_NOTICE', opt)
  },
  setCloudAddress({ commit }, data) {
    commit('SET_CLOUD_ADDRESS', data)
  },
  setReportTypeChange({ commit }, data) {
    commit('SET_REPORT_TYPE_CHANGE', {})
  },
  setReportModule({ commit }) {
    existSaleModule(2).then(res => {
      commit('SET_REPORT_MODULE', res.data)
    })
  },
  setHasMailServer({ commit }) {
    hasMailServer().then(res => {
      commit('SET_HAS_MAIL_SERVER', res.data)
    })
  },
  setBindUser({ commit }, data) {
    commit('SET_BIND_USER', data)
  },
  setTrialDlp({ commit }) {
    isTrialVersion().then(res => {
      commit('SET_TRIAL_DLP', !!res.data)
    })
  },
  setEngineOsType({ commit }, data) {
    commit('SET_ENGINE_OS_TYPE', data)
  },
  isWindows({ commit }) {
    getIsWindows().then(res => {
      commit('SET_IS_WINDOWS', !!res.data)
      localStorage.setItem('isWindows', !!res.data)
    })
  },
  setSaleModuleIds({ commit }) {
    listSaleModuleId().then(res => {
      commit('SET_SALE_MODULE_IDS', res.data)
    })
  },
  setMasterDbType({ commit }, data) {
    commit('SET_MASTER_DB_TYPE', data)
  },
  setAlarmMsg({ commit }, data) {
    commit('SET_ALARM_MSG', data)
  },
  setSoftwareCategories({ commit }) {
    callActionIfNotLoading(null, 'setSoftwareCategories', (callFunc) => {
      return listCategories().then(res => {
        commit('SET_SOFTWARE_CATEGORIES', res.data)
        if (typeof callFunc === 'function') {
          callFunc()
        }
      })
    })
  },
  setSoftwareRepositoryVer({ commit }) {
    commit('SET_SOFTWARE_REPOSITORY_VER')
  },
  setUserRoleOptions({ commit }) {
    callActionIfNotLoading(null, 'setUserRoleOptions', (callFunc) => {
      return getUserRoleTreeNode().then(res => {
        res.data.map(item => {
          item.disabled = true;
        })
        commit('SET_USER_ROLE_OPTIONS', res.data)
        if (typeof callFunc === 'function') {
          callFunc()
        }
      })
    })
  },
  setSearchCondition({ commit }) {
    getSearchCondition().then(res => {
      const conditions = res.data.map(item => {
        item.queryCondition = JSON.parse(item.queryCondition || '{}')
        return item
      })
      commit('SET_SEARCH_CONDITION', conditions)
    })
  },
  addSearchCondition({ commit }, data) {
    commit('ADD_SEARCH_CONDITION', data)
  },
  delSearchCondition({ commit }, data) {
    commit('DEL_SEARCH_CONDITION', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
