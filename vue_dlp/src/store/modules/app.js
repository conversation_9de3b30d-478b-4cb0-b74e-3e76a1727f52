import Cookies from 'js-cookie'
import resource from '@/lang/defaultResource'

const languageOption = []
// 前端默认资源，语言选择框的选项
for (const key in resource) {
  if (Object.hasOwnProperty.call(resource, key)) {
    const lang = resource[key];
    const label = lang.languageLabel || key
    const bcp47Label = lang.bcp47Label
    languageOption.push({ lang: key, label, bcp47Label })
  }
}

// 先读取cookies中的语言
let language = Cookies.get('language')
// 若cookies中没有存储，则根据浏览器的语言进行适配（从后台获取资源后会根据 isI18nMode 和 defaultLanguage 重新设置语言）
if (!language) {
  // 浏览器语言
  const browserLang = navigator.language
  // 浏览器语言对应的系统语言map
  const languageMap = { 'zh-CN': 'zh', 'zh': 'zh', 'zh-TW': 'tw', 'zh-HK': 'tw', 'en': 'en', 'en-GB': 'en', 'en-US': 'en' }
  // 根据浏览器语言适配，无法适配则默认选择中文
  language = languageMap[browserLang] || 'zh'
}
Cookies.set('language', language, { expires: 365 })

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  language: language,
  languageOption: languageOption
}

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_LANGUAGE: (state, language) => {
    state.language = language
  },
  SET_LANGUAGE_OPTION: (state, languageOption) => {
    state.languageOption = languageOption
  }
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setLanguage({ commit }, language) {
    Cookies.set('language', language, { expires: 365 })
    commit('SET_LANGUAGE', language)
  },
  setLanguageOption({ commit }, languageOption) {
    commit('SET_LANGUAGE_OPTION', languageOption)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
