import i18n from '@/lang'
import store from '@/store'
import { asyncRoutes, constantRoutes } from '@/router'
import { getEnableConfig } from '@/api/property'
import { listMenuCollection, createMenuCollection, deleteMenuCollection } from '@/api/menuCollection'
import { getSignReportList } from '@/api/report/baseReport/signReport'
import { getOsTypeMap, getUsedScopeMap } from '@/utils/dictionary'

const state = {
  routes: [],
  addRoutes: [],
  commonRoutes: [],
  favoriconArr: [],
  routesPath: {},
  routesMap: [],
  menuCodeFullTitleMap: {},  // 菜单路径 map， menuCode为key，titleArray 为value
  menuCodeMapper: { 'F15': 'F14', 'E28': 'E26', 'E2A': 'E21', 'E30': 'E24' },  // F15:文件自检内容检测策略,E28全盘扫描内容检测策略，菜单屏蔽，但有跳转路口，管理员日志、策略变更消息等需要转换
  adminLogDeleteAble: false,
  auditingDeleteAble: false,
  adminValidConfigAble: false,
  userValidConfigAble: false,
  desensitizeContentAble: false
}

const osTypeMap = getOsTypeMap()
const usedScopeMap = getUsedScopeMap()

/**
 * 获取菜单名称
 * @param title   路由meta.title
 */
function getMenuTitle(title) {
  const hasKey = i18n.te(`route.${title}`)
  if (hasKey) {
    return i18n.t(`route.${title}`)
  }
  return title
}

/**
 * 确定当前用户是否具有权限
 * @param permissions      权限菜单编码 eg: ['A12','B11']
 * @param menuCode         菜单编码 eg: 'A12'
 */
function hasPermission(permissions, menuCode) {
  // allPermission 值为 true 时，开放所有菜单权限
  if (process.env.NODE_ENV == 'development' && store.getters.allPermission) return true
  return permissions.indexOf('*') > -1 || menuCode && permissions.indexOf(menuCode) > -1
}

/**
 * 拼接父级路径，返回完整的路由路径
 * @param parentPath      eg: '/system'
 * @param path            eg: 'organization'
 */
export function appendPath(parentPath, path) {
  if (!parentPath) {
    return path
  }
  if (!path.startsWith('/') && !parentPath.endsWith('/')) {
    parentPath += '/'
  }
  return parentPath + path
}

/**
 * 根据管理员权限移除 日志审计 或 其他模块 的日志路由
 * @param {*} route 当前需要判断是否移除的路由
 * @param {*} permissions 管理员权限
 * @returns 返回是否需要移除，true 为移除
 */
export function isRemoveRouteByLog(route, permissions) {
  // 日志审计路由是否显示, '120' 为日志审计的菜单编码
  const isLogRouterShow = permissions.indexOf('120') > -1
  if (!isLogRouterShow) {
    // 如果不包含“日志审计”的销售模块，则移除“日志审计”一级菜单
    return route.name === 'BehaviorAuditing'
  }
  // 包含 日志审计 模块，去掉在其他模块中的日志路由（其他模块日志有 isLog 属性）
  return route.meta && route.meta.isLog
}

/**
 * 根据管理员权限移除 数据安全 或 敏感内容识别 的 全盘扫描及全盘扫描记录 路由
 * @param {*} route 当前需要判断是否移除的路由
 * @param {*} permissions 管理员权限
 * @returns 返回是否需要移除，true 为移除
 */
export function isRemoveRouteByDiskScan(route, permissions) {
  // E26 全盘扫描；E28 全盘扫描内容检测策略; E52 全盘扫描记录;
  if (route.code == 'E26' || route.code == 'E28' || route.code == 'E52') {
    // 124 数据安全模块
    const isDataEncryptionShow = permissions.indexOf('124') > -1
    // 113 敏感内容识别
    const isContentStgShow = permissions.indexOf('113') > -1
    if (isDataEncryptionShow) {
      // 移除 敏感内容识别和文档标签 的 全盘扫描路由;
      return route.meta && ['contentStg', 'contentLog', 'documentLabel', 'terminalSystem'].indexOf(route.meta.parentName) > -1
    } else if (isContentStgShow) {
      // 移除 数据安全和文档标签 的 全盘扫描路由;
      return route.meta && ['encryption', 'encryptionLog', 'documentLabel', 'terminalSystem'].indexOf(route.meta.parentName) > -1
    } else {
      // 移除 数据安全和敏感内容识别 的 全盘扫描路由；
      return route.meta && ['encryption', 'encryptionLog', 'contentStg', 'contentLog'].indexOf(route.meta.parentName) > -1
    }
  }
  return false
}

/**
 * 根据是否有敏感内容识别权限，去除带文档标签路由
 * @param {*} route 当前需要判断是否移除的路由
 * @param {*} permissions 管理员权限
 * @returns 返回是否需要移除，true 为移除
 */
export function isRemoveRouteLabelPermission(route, permissions) {
  // 113 敏感内容识别
  const isContentStgShow = permissions.indexOf('113') > -1
  if (!isContentStgShow) {
    return route.name === 'LandLabeling'
  }
}

/**
 * 通过递归过滤异步路由表
 * @param  parentPath 父路由路径
 * @param  routes 路由数组
 * @param  permissions 权限数组
 */
export function filterAsyncRoutes(parentPath, routes, permissions) {
  const res = []
  routes.forEach(route => {
    const tmp = { ...route }
    // 日志审计 模块 和 其它模块中的日志，只能显示一个
    if (isRemoveRouteByLog(tmp, permissions)) {
      return false
    }
    // 数据安全 或 敏感内容识别 的 全盘扫描 路由，去掉一个
    if (isRemoveRouteByDiskScan(tmp, permissions)) {
      return false;
    }
    // 根据是否有敏感内容识别权限，去除落地加标签路由
    if (isRemoveRouteLabelPermission(route, permissions)) {
      return false
    }
    const fullPath = appendPath(parentPath, tmp.path)
    tmp.fullPath = fullPath
    // 打印隐藏与禁用的路由
    // if (tmp.hidden || tmp.disabled) {
    //   console.log('隐藏与禁用的路由', tmp, tmp.name, tmp.code)
    // }
    if (tmp.children) { // 过滤子路由
      tmp.children = filterAsyncRoutes(fullPath, tmp.children, permissions)
      let showMenu = false
      tmp.children.forEach(item => {
        if (!item.hidden) {
          showMenu = true
        }
      })
      // 子路由至少包含一个可显示菜单，才显示二级菜单
      if (showMenu) {
        tmp.alwaysShow = true // 二级菜单始终显示
        res.push(tmp)
      }
    } else if (tmp.path === '*' || hasPermission(permissions, tmp.code)) {
      // 路由配置了disabled，则去掉
      if (tmp.disabled) return
      if (state.favoriconArr.indexOf(fullPath) > -1) {
        // 根据后台常用菜单数组，生成路由数组
        const tmpRoute = { ...route }
        tmpRoute.path = fullPath
        state.commonRoutes.push(tmpRoute)
      }

      // 路由重定向路径修改
      if (tmp.redirect && tmp.children && tmp.children.length > 0) {
        const redirect = tmp.redirect.toString()
        const subPath = tmp.children[0].path
        if (!redirect.endsWith(subPath)) {
          const index = redirect.lastIndexOf('/')
          const newRedirect = redirect.substring(0, index + 1) + tmp.children[0].path
          tmp.redirect = newRedirect
        }
      }

      // 对路由配置中添加了prepare属性的叶子节点路由，自动拷贝一份，作为预定义（策略）路由
      if (tmp.prepare) { // 路由存在prepare属性时才有效
        const prepareRoute = JSON.parse(JSON.stringify(tmp))
        prepareRoute.hidden = true
        prepareRoute.component = tmp.component
        prepareRoute.path += '/prepare'
        prepareRoute.name += 'Prepare'
        prepareRoute.meta.title = getMenuTitle(prepareRoute.meta.title) + getMenuTitle('prepare')
        // 预定义策略未显示菜单路径，故赋值code，即menu_code，例：C67-P MTP管控 jira：TRDLP-7717
        prepareRoute.meta.code = tmp.code + '-P';
        res.push(prepareRoute)
      }

      // 当路由有code属性时，保存路由路径、添加属性到 meta
      if (tmp.code) {
        state.routesPath[tmp.fullPath] = fullPath
        // 把code添加到meta中
        tmp.meta.code = tmp.code
        // 菜单支持的osType、usedScope添加到meta中
        const { osType, usedScope } = tmp.meta
        tmp.meta.osType = osTypeMap[tmp.code] || osType
        tmp.meta.usedScope = usedScopeMap[tmp.code] || usedScope
      }

      // 开发模式，输出日志信息到浏览器控制台，用于检测路由name和组件name不同的情况
      if (process.env.NODE_ENV == 'development') {
        // 检测路由name与组件name不相同的页面
        tmp.component && tmp.component().then(res => {
          // 组件name
          const componentName = res.default.name
          // 路由name，有些首字母没有大写，手动修改
          let routerName = tmp.name.substr(0, 1).toUpperCase() + tmp.name.substr(1)
          // 日志审计页面路由 name 额外添加了 P，这边删掉
          if (routerName.endsWith('P')) {
            routerName = routerName.substr(0, routerName.length - 1)
          } else if (routerName.endsWith('Prepare')) {
            // 预定义策略缓存处理
            routerName = routerName.substr(0, routerName.length - 7)
          } else if (routerName.startsWith('StandardSymptomReport')) {
            // 征兆报表(自定义菜单，公用一个组件)
            routerName = routerName.substr(0, 21)
          }
          // 如果组件name与路由name不相同，则输出到浏览器控制台
          if (componentName != routerName) {
            console.warn(`路由name与组件name不相同，会导致页面无法缓存状态。路由name: ${routerName}; 组件name: ${componentName}`);
          }
        })
      }

      res.push(tmp)
    } else {
      return false
    }

    // 路由map
    if (state.routesMap.indexOf(tmp) == -1 && !tmp.hidden) {
      // 如果子路由都没有权限，则父路由也不添加到routesMap中
      if (!(tmp.children && tmp.children.length == 0)) {
        state.routesMap.push(tmp)
      }
    }
  })
  return res
}

/**
 * 二级菜单（路由）不提供组件接入，目的是为了缓存三级菜单页面
 * 修改路由的path，当路由的 noComponent为true时，将path作为前缀添加到子路由的path,并将子路由提到上级路由
 * @param {Array} router 路由
 * @param {String} prefix 拼接路径前缀
 */
function delteFakeParent(router, prefix) {
  var newRouter = { ...router }
  if (prefix) {
    newRouter.path = prefix + '/' + router.path
  }
  if (!router.children) return newRouter
  var children = []
  const pre = router.noComponent ? newRouter.path : ''

  // 遍历子路由
  for (let i = 0; i < router.children.length; i++) {
    const item = delteFakeParent(router.children[i], pre)
    if (Array.isArray(item)) {
      item.forEach(el => {
        children.push(el)
      })
    } else {
      children.push(item)
    }
  }

  if (router.noComponent) {
    newRouter = children
  } else {
    newRouter.children = children
  }
  return newRouter
}

function filterRoutes(routes) {
  var res = []
  routes.forEach(router => {
    const tmp = delteFakeParent(router)
    res.push(tmp)
  })
  return res
}

// 生成由 menuCode 为 key，titleArray 为 value 的 map
// 使用 [title1,title2]的方式，可以避免部分title中包含有分隔符，导致使用时获取title不准确
function generateMenuCodeFullTitleMap(routes, titleArray, permissions) {
  const menuCodeFullTitleMap = {}
  routes.forEach(route => {
    const parentTitleArray = [...titleArray]
    // 日志审计 模块 和 其它模块中的日志，只能显示一个
    if (isRemoveRouteByLog(route, permissions)) {
      return false
    }
    // 数据安全 或 敏感内容识别 的 全盘扫描 路由，去掉一个
    if (isRemoveRouteByDiskScan(route, permissions)) {
      return false;
    }
    // 根据是否有敏感内容识别权限，去除落地加标签路由
    if (isRemoveRouteLabelPermission(route, permissions)) {
      return false
    }
    // 正常展示的菜单
    if (route.meta && route.meta.title && !route.hidden) {
      parentTitleArray.push(getMenuTitle(route.meta.title))
      if (route.children) {
        const tempMap = generateMenuCodeFullTitleMap(route.children, [...parentTitleArray], permissions);
        Object.assign(menuCodeFullTitleMap, tempMap)
      }

      if (route.code) {
        menuCodeFullTitleMap[route.code] = parentTitleArray
      }
    }
    // 隐藏的 config 菜单
    if (route.path == '/config') {
      const tempMap = generateMenuCodeFullTitleMap(route.children, [''], permissions);
      Object.assign(menuCodeFullTitleMap, tempMap)
    }
  })
  return menuCodeFullTitleMap
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  SET_COMMON_ROUTES: (state, routes) => {
    state.commonRoutes = routes
  },
  ADD_COMMON_ROUTE: (state, route) => {
    state.commonRoutes.push(route)
  },
  DEL_COMMON_ROUTE: (state, route) => {
    for (const [i, v] of state.commonRoutes.entries()) {
      if (v.path === route.path) {
        state.commonRoutes.splice(i, 1)
        break
      }
    }
  },
  CLEAR_COMMON_ROUTE: (state) => {
    state.commonRoutes = []
  },
  SET_FAVORICON_ARR: (state, arr) => {
    state.favoriconArr = arr
  },
  ADD_FAVORICON: (state, route) => {
    state.favoriconArr.push(route.path)
  },
  DEL_FAVORICON: (state, route) => {
    const i = state.favoriconArr.indexOf(route.path)
    state.favoriconArr.splice(i, 1)
  },
  SET_MENU_CODE_FULL_TITLE_MAP: (state, menuCodeFullTitleMap) => {
    state.menuCodeFullTitleMap = menuCodeFullTitleMap
  },
  ADD_AUDITING_DELETE_ABLE: (state, auditingDeleteAble) => {
    state.auditingDeleteAble = auditingDeleteAble
  },
  ADD_ADMIN_DELETE_ABLE: (state, adminLogDeleteAble) => {
    state.adminLogDeleteAble = adminLogDeleteAble
  },
  ADD_ADMIN_VALID_CONFIG_ABLE: (state, adminValidConfigAble) => {
    state.adminValidConfigAble = adminValidConfigAble
  },
  ADD_USER_VALID_CONFIG_ABLE: (state, userValidConfigAble) => {
    state.userValidConfigAble = userValidConfigAble
  },
  ADD_DESENSITIZE_CONTENT_ABLE: (state, desensitizeContentAble) => {
    state.desensitizeContentAble = desensitizeContentAble
    // console.log(state.desensitizeContentAble)
  }
}

// 获取征兆报表菜单
async function getSignReport() {
  let resp = {}
  try {
    resp = await getSignReportList()
  } catch (e) {
    console.error('获取征兆报表列表失败', e)
    return []
  }
  const reportList = resp.data
  // 根据征兆报表设置路由
  const childrenRouter = reportList.map(v => {
    return {
      code: 'SS9',
      path: 'statistical-' + v.id,
      component: () => import('@/views/report/standardSign/statistical'),
      name: 'standardSymptomReport' + v.id,
      meta: { title: v.name }
    }
  })
  // 如果有征兆报表路由，返回路由，否则返回 []
  return childrenRouter.length > 0 ? [{
    path: 'standardSign',
    noComponent: true,
    code: 'SS9',
    name: 'standardSign',
    meta: {
      title: 'signReport',
      icon: 'report13'
    },
    alwaysShow: true,
    children: childrenRouter
  }] : []
}

const actions = {
  // 生成路由
  generateRoutes({ commit }, permissions) {
    return new Promise(async resolve => {
      // 清空routesMap
      state.routesMap.splice(0)
      function filterAndResolve() {
        // 需要判断每个路由权限
        const accessedRoutes = filterAsyncRoutes('', asyncRoutes, permissions)
        commit('SET_ROUTES', accessedRoutes)
        // 过滤路由，二级菜单不接入组件
        const newRouter = filterRoutes(accessedRoutes)
        resolve(newRouter)
      }
      filterAndResolve()
    })
  },
  // 获取征兆报表菜单数据
  getSignReport({ commit }, route) {
    return new Promise(resolve => {
      getSignReport().then(res => {
        const addRoutes = state.addRoutes
        let newRouter = []
        if (res.length > 0) {
          addRoutes.forEach(route => {
            // 将征兆报表添加到报表路由下
            if (route.name === 'Report') {
              const index = route.children.findIndex(route => route.name === 'ApprovalManagement')
              if (index != -1) {
                // 如果有审批管理报表，则添加在后面
                route.children.splice(index + 1, 0, ...res)
              } else {
                // 如果没有审批管理报表，则添加在最后面
                route.children.push(...res)
              }
              // 获取只包含征兆报表的报表路由
              const reportRoute = JSON.parse(JSON.stringify(route))
              reportRoute.children = res
              const signRoute = filterAsyncRoutes('', [reportRoute], store.getters.userMenuCodes)
              newRouter = filterRoutes(signRoute)
            }
          })
        }
        commit('SET_ROUTES', addRoutes)
        resolve(newRouter)
      })
    })
  },
  // 获取常用菜单数据
  getMenuCollection({ commit }, callback) {
    return new Promise(resolve => {
      listMenuCollection().then(res => {
        const arr = []
        res.data.items.forEach(item => {
          arr.push(item.redirect)
        })
        commit('SET_FAVORICON_ARR', arr)
        resolve(arr)
      }).catch()
    })
  },
  addCommonMenu({ dispatch }, route) {
    return new Promise((resolve, reject) => {
      createMenuCollection({
        'redirect': route.path,
        'menuName': getMenuTitle(route.meta.title),
        'menuFullPath': state.menuCodeFullTitleMap[route.code].join('/')
      }).then(response => {
        dispatch('addCommonRoute', route)
        dispatch('addFavoricon', route)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  addCommonRoute({ commit }, route) {
    commit('ADD_COMMON_ROUTE', route)
  },
  addFavoricon({ commit }, route) {
    commit('ADD_FAVORICON', route)
  },
  deleteCommonMenu({ dispatch }, route) {
    return new Promise((resolve, reject) => {
      deleteMenuCollection({
        'redirect': route.path,
        'menuName': getMenuTitle(route.meta.title),
        'menuFullPath': state.menuCodeFullTitleMap[route.code].join('/')
      }).then(response => {
        dispatch('delCommonRoute', route)
        dispatch('delFavoricon', route)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  delCommonRoute({ commit }, route) {
    commit('DEL_COMMON_ROUTE', route)
  },
  delFavoricon({ commit }, route) {
    commit('DEL_FAVORICON', route)
  },
  clearCommonRoute({ commit }) {
    commit('CLEAR_COMMON_ROUTE')
  },
  generateMenuCodeFullTitleMap({ commit }, permissions) {
    const menuCodeFullTitleMap = generateMenuCodeFullTitleMap(asyncRoutes.concat(constantRoutes), [''], permissions);
    commit('SET_MENU_CODE_FULL_TITLE_MAP', menuCodeFullTitleMap)
  },
  addEnableConfig({ commit }) {
    // 获取开关配置
    getEnableConfig().then(resp => {
      const enableConfig = resp.data
      if (enableConfig) {
        commit('ADD_ADMIN_VALID_CONFIG_ABLE', enableConfig['adminValidTime'])
        commit('ADD_USER_VALID_CONFIG_ABLE', enableConfig['userValidTime'])
        commit('ADD_ADMIN_DELETE_ABLE', enableConfig['adminLogDel'])
        commit('ADD_AUDITING_DELETE_ABLE', enableConfig['auditLogDel'])
        commit('ADD_DESENSITIZE_CONTENT_ABLE', enableConfig['desensitizeContent'])
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
