
const state = {
  commonVm: {},                   // 公共组件的Vue实例，{ key: vm }
  currentVm: null,                // 当前页面vue实例
  stgTargetCheckedNodeMap: {},    // strategyTargetTree 各个页面选中节点的map
  tagsViewVm: null,               // tagsView实例
  visitedViews: [],               // 已访问的菜单路由
  cachedViews: [],                // 缓存的菜单路由的name
  viewTimestamp: {}               // 记录每个view视图的访问时间戳
}

const mutations = {
  SET_COMMON_VM: (state, vm) => {
    state.commonVm = vm
  },
  SET_CURRENT_VM: (state, vm) => {
    state.currentVm = vm
  },
  SET_STG_TARGET_CHECKED_NODE_MAP: (state, map) => {
    state.stgTargetCheckedNodeMap = map
  },
  SET_TAGS_VIEW_VM: (state, vm) => {
    state.tagsViewVm = vm
  },
  ADD_VISITED_VIEW: (state, view) => {
    state.viewTimestamp[view.path] = new Date().getTime()
    state.viewTimestamp[view.name] = state.viewTimestamp[view.path]
    const index = state.visitedViews.findIndex(v => v.path === view.path)
    // 若未添加则添加，反之则更新
    if (index == -1) {
      state.visitedViews.push(Object.assign({}, view, { title: view.meta.title || 'no-name' })
      )
    } else {
      state.visitedViews.splice(index, 1, Object.assign({}, view, { title: view.meta.title || 'no-name' }))
    }
  },
  ADD_CACHED_VIEW: (state, view) => {
    if (state.cachedViews.includes(view.name)) return
    if (!view.meta.noCache) {
      state.cachedViews.push(view.name)
    }
  },

  DEL_VISITED_VIEW: (state, view) => {
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.path === view.path) {
        state.visitedViews.splice(i, 1)
        break
      }
    }
  },
  DEL_CACHED_VIEW: (state, view) => {
    for (const i of state.cachedViews) {
      if (i === view.name) {
        const index = state.cachedViews.indexOf(i)
        state.cachedViews.splice(index, 1)
        break
      }
    }
  },

  DEL_OTHERS_VISITED_VIEWS: (state, view) => {
    state.visitedViews = state.visitedViews.filter(v => {
      return v.meta.affix || v.path === view.path
    })
  },
  DEL_OTHERS_CACHED_VIEWS: (state, view) => {
    for (const i of state.cachedViews) {
      if (i === view.name) {
        const index = state.cachedViews.indexOf(i)
        state.cachedViews = state.cachedViews.slice(index, index + 1)
        break
      }
    }
  },

  DEL_ALL_VISITED_VIEWS: state => {
    // keep affix tags
    const affixTags = state.visitedViews.filter(tag => tag.meta.affix)
    state.visitedViews = affixTags
  },
  DEL_ALL_CACHED_VIEWS: state => {
    state.cachedViews = []
  },

  UPDATE_VISITED_VIEW: (state, view) => {
    for (let v of state.visitedViews) {
      if (v.path === view.path) {
        v = Object.assign(v, view)
        break
      }
    }
  },
  UPDATE_CACHED_VIEWS: (state, view) => {
    // 更新缓存页面的访问顺序
    const index = state.cachedViews.indexOf(view.name)
    if (index > -1) {
      state.cachedViews.push(...state.cachedViews.splice(index, 1))
    }
  }
}

const actions = {
  setCommonVm({ commit }, vm) {
    const commonVm = Object.assign({}, state.commonVm, vm)
    commit('SET_COMMON_VM', commonVm)
  },
  setCurrentVm({ commit }, vm) {
    commit('SET_CURRENT_VM', vm)
  },
  setStgTargetCheckedNodeMap({ commit }, map) {
    commit('SET_STG_TARGET_CHECKED_NODE_MAP', map)
  },
  setTagsViewVm({ commit }, vm) {
    commit('SET_TAGS_VIEW_VM', vm)
  },
  addView({ dispatch }, view) {
    dispatch('addVisitedView', view)
    dispatch('addCachedView', view)
  },
  addVisitedView({ commit }, view) {
    commit('ADD_VISITED_VIEW', view)
  },
  addCachedView({ commit }, view) {
    commit('ADD_CACHED_VIEW', view)
  },

  delView({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delVisitedView', view)
      dispatch('delCachedView', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delVisitedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_VISITED_VIEW', view)
      resolve([...state.visitedViews])
    })
  },
  delCachedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_CACHED_VIEW', view)
      resolve([...state.cachedViews])
    })
  },

  delOthersViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delOthersVisitedViews', view)
      dispatch('delOthersCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delOthersVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_VISITED_VIEWS', view)
      resolve([...state.visitedViews])
    })
  },
  delOthersCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_CACHED_VIEWS', view)
      resolve([...state.cachedViews])
    })
  },

  delAllViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delAllVisitedViews', view)
      dispatch('delAllCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delAllVisitedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_VISITED_VIEWS')
      resolve([...state.visitedViews])
    })
  },
  delAllCachedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_CACHED_VIEWS')
      resolve([...state.cachedViews])
    })
  },

  updateVisitedView({ commit }, view) {
    commit('UPDATE_VISITED_VIEW', view)
  },
  updateCachedViews({ commit }, view) {
    commit('UPDATE_CACHED_VIEWS', view)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
