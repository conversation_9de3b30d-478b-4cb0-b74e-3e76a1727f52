const storageKey = 'downloadFiles'

function initFiles() {
  const storageFiles = sessionStorage.getItem(storageKey)
  if (storageFiles) {
    try {
      const downloadFiles = JSON.parse(storageFiles)
      if (Array.isArray(downloadFiles)) {
        return downloadFiles
      }
    } catch (e) {
      console.warn('Error to get download files from session storage', e)
    }
  }
  return []
}

const state = {
  downloadFiles: initFiles(),
  downloadLogEnabled: false,
  downloadListVisible: false,
  streamDownloadErrorCount: 0
}

const mutations = {
  RESET: (state, options) => {
    if (Array.isArray(options) && options.length > 0) {
      state.downloadFiles = options
      sessionStorage.setItem(storageKey, JSON.stringify(options))
    } else {
      state.downloadFiles = []
      sessionStorage.removeItem(storageKey)
    }
  },
  UPDATE: (state, options) => {
    const index = getFileIndex(state, options)
    if (index < 0) {
      state.downloadFiles.unshift(options)
      // state.downloadFiles.splice(0, 0, options)
      // state.downloadFiles = [options, ...state.downloadFiles]
      if (state.downloadLogEnabled) {
        console.trace('[download] store add: ', options)
      }
      state.downloadListVisible = true
    } else {
      state.downloadFiles.splice(index, 1, options)
    }
    sessionStorage.setItem(storageKey, JSON.stringify(state.downloadFiles))
  },
  REMOVE: (state, options) => {
    const index = getFileIndex(state, options)
    if (index >= 0) {
      state.downloadFiles.splice(index, 1)
      if (state.downloadFiles.length > 0) {
        sessionStorage.setItem(storageKey, JSON.stringify(state.downloadFiles))
      } else {
        sessionStorage.removeItem(storageKey)
      }
    }
  },
  VISIBLE: (state, options) => {
    const visible = !!options
    if (state.downloadListVisible !== visible) {
      state.downloadListVisible = visible
    }
  },
  ENABLE_LOG: (state, options) => {
    state.downloadLogEnabled = !!options
  },
  COUNT_ERROR: (state, options) => {
    state.streamDownloadErrorCount = options
  }
}

function getFileIndex(state, options) {
  return state.downloadFiles.map(file => file.guid).indexOf(options.guid)
}

const actions = {
  reset({ commit }, files) {
    commit('RESET', files)
  },
  update({ commit }, file) {
    commit('UPDATE', file)
  },
  remove({ commit }, file) {
    commit('REMOVE', file)
  },
  visible({ commit }, visible) {
    commit('VISIBLE', visible)
  },
  enableLog({ commit }, enable) {
    commit('ENABLE_LOG', enable)
  },
  countError({ commit }, count) {
    commit('COUNT_ERROR', count)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
