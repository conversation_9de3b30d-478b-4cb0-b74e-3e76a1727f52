import { getInfo, getResources, login, logout } from '@/api/user'
import { getRoleList } from '@/api/system/organizational/sysUser'
import { getIsMasterSub } from '@/api/property'
import { cookieSetPwd, getToken, removeToken, removeUserId, setToken, setTokenTimestamp, setUserId } from '@/utils/auth'
import { aesEncode /*, formatAesKey*/ } from '@/utils/encrypt'
import { resetRouter } from '@/router'
import store from '@/store'
import i18n from '@/lang'
import { resource } from '@/resource'
import request from '@/utils/request'

const state = {
  token: getToken(),
  name: '',
  account: '',
  avatar: '',
  roles: [],                     // 当前管理员拥有的角色id
  roleList: [],                  // 当前管理员拥有的角色List
  currentRoleId: null,           // 当前管理员使用的角色id
  userId: undefined,             // 当前管理员的id
  isSuperMode: true,             // 超管模式
  isSuperRole: false,            // 超级管理员
  isSysRole: false,              // 系统管理员
  isSuperThreeUser: false,       // 三员模式下的3个主管理员
  isMasterSubAble: false,        // 总分服务器开关
  isI18nMode: false,             // 是否多语言版本
  defaultLanguage: '',           // 打包时设置的默认语言。非多语言版本：控制台以该语言为准；多语言版本：该配置不生效。
  userMenuCodes: [],             // 当前管理员拥有的菜单权限（code）数组
  userMenuCodesMap: {},          // 当前管理员拥有的菜单权限（code）Map
  upPwdTime: '',                 // 区分是否首次登录
  sysResources: JSON.parse(localStorage.getItem('resource')) || resource, // 系统资源使用 localStorage 存储的数据 或 原始数据
  resourcesLoaded: false,
  theme: 'default',              // 主题皮肤
  pageLimit: 10,                 // 页面上限
  closeType: 1,                  // 页面超出上限时的处理方式，1：关闭最早访问的页面，打开新页面 2：手动关闭已打开页面，再重新打开新页面
  cacheSelectedNode: 0,          // 公共树组件缓存点击节点的方式，1：每个页面同步点击的节点(不记住点击的节点) 0：每个页面记住各自点击的节点
  cacheTimeQuery: 0,             // 审计日志时间组件是否同步时间，1：同步配置的时间 0：不同步配置的时间
  cacheDateObj: null,            // 缓存配置的时间
  password: '',
  serverTime: undefined,         // 服务端的时间，避免时间差
  browserTime: undefined,        // 浏览器的时间，记录serverTime获取的浏览器时间，用于对比时间差
  cleanDuration: undefined,
  requestTimeout: 60000          // 请求超时时间，通过后台修改
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USER_ID: (state, userId) => {
    state.userId = userId
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_ACCOUNT: (state, account) => {
    state.account = account
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_ROLE_LIST: (state, list) => {
    state.roleList = list
  },
  SET_CURRENT_ROLE_ID: (state, id) => {
    state.currentRoleId = id
  },
  IS_SUPER_MODE: (state, isSuperMode) => {
    state.isSuperMode = isSuperMode
  },
  IS_SUPER_ROLE: (state, isSuperRole) => {
    state.isSuperRole = isSuperRole
  },
  IS_SYS_ROLE: (state, isSysRole) => {
    state.isSysRole = isSysRole
  },
  IS_SUPER_THREE_USER: (state, isSuperThreeUser) => {
    state.isSuperThreeUser = isSuperThreeUser
  },
  SET_USER_MENU_CODES: (state, userMenuCodes) => {
    state.userMenuCodes = userMenuCodes
  },
  SET_USER_MENU_CODES_MAP: (state, map) => {
    state.userMenuCodesMap = map
  },
  IS_MASTER_SUB: (state, isMasterSubAble) => {
    state.isMasterSubAble = isMasterSubAble
  },
  IS_I18N_MODE: (state, isI18nMode) => {
    state.isI18nMode = !!isI18nMode
  },
  SET_DEFAULT_LANGUAGE: (state, defaultLanguage) => {
    state.defaultLanguage = defaultLanguage
  },
  SET_UP_PWD_TIME: (state, upPwdTime) => {
    state.upPwdTime = upPwdTime
  },
  SET_SYS_RESOURCES: (state, resources) => {
    state.sysResources = resources
  },
  SET_THEME: (state, theme) => {
    state.theme = theme
  },
  SET_PAGE_LIMIT: (state, pageLimit) => {
    state.pageLimit = pageLimit
  },
  SET_CLOSE_TYPE: (state, closeType) => {
    state.closeType = closeType
  },
  SET_CACHE_SELECTED_NODE: (state, cacheSelectedNode) => {
    state.cacheSelectedNode = cacheSelectedNode
  },
  SET_CACHE_TIME_QUERY: (state, cacheTimeQuery) => {
    state.cacheTimeQuery = cacheTimeQuery
  },
  SET_CACHE_DATE_OBJ: (state, cacheDateObj) => {
    state.cacheDateObj = cacheDateObj
  },
  SET_PASSWORD: (state, password) => {
    state.password = password
  },
  SET_CLEAN_DURATION: (state, cleanDuration) => {
    state.cleanDuration = cleanDuration
  },
  SET_REQUEST_TIMEOUT: (state, requestTimeout) => {
    state.requestTimeout = requestTimeout
  }
}

const actions = {
  setToken({ commit }, token) {
    let tokenStr = token
    let ignoreTimestamp = false
    if (typeof token === 'object') {
      tokenStr = token.token
      ignoreTimestamp = token.ignoreTimestamp
    }
    if (tokenStr !== state.token) {
      commit('SET_TOKEN', tokenStr)
      setToken(tokenStr, ignoreTimestamp)
    } else if (!ignoreTimestamp) {
      setTokenTimestamp()
    }
  },
  setTokenTimestamp({ commit }) {
    setTokenTimestamp()
  },
  // 获取系统资源
  getSysResources({ commit }) {
    return new Promise((resolve, reject) => {
      getResources().then(response => {
        const { data } = response
        // 从后台未获取到系统资源，则使用 sysResources
        let webSource = data.webSource
        if (!webSource) {
          webSource = state.sysResources
        }
        const { i18nMode, defaultLanguage } = data
        commit('IS_I18N_MODE', i18nMode)
        commit('SET_DEFAULT_LANGUAGE', defaultLanguage)
        commit('SET_SYS_RESOURCES', webSource)
        const language = store.getters.language
        const devI18nMode = store.getters.devI18nMode
        // 当非多语言模式下（包括开发模式）且 有默认语言时，将控制台语言设置为默认语言
        if (!i18nMode && !devI18nMode && defaultLanguage) {
          // 控制台语言与默认语言不一致，设置成默认语言
          if (language !== defaultLanguage) {
            store.dispatch('app/setLanguage', defaultLanguage)
            location.reload()
          }
        }
        state.resourcesLoaded = true
        state.serverTime = data['timeMillis']
        state.browserTime = new Date().getTime()
        localStorage.setItem('resource', JSON.stringify(webSource))
        const link = document.querySelector("link[rel*='icon']") || document.createElement('link')
        link.type = 'image/x-icon'
        link.rel = 'shortcut icon'
        link.href = webSource.favicon
        document.getElementsByTagName('head')[0].appendChild(link)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user login
  login({ commit }, userInfo) {
    const { username, password, rememberPassword } = userInfo
    return new Promise((resolve, reject) => {
      // const aesPwd = aesEncode(password, formatAesKey('tr838408', username.trim()))
      const aesUserPass = aesEncode(JSON.stringify({ username: username, password: password }), 'tr838408userpass')
      login({ username: aesUserPass }).then(response => {
        if (response.mock) {
          commit('SET_TOKEN', response.token)
          setToken(response.token)
        }
        // dlp和nac整合需要前端记住账号和密码
        // rememberPassword=true时，默认自动填写dlp登录账号密码在登录页面上，否则不自动填写
        cookieSetPwd(username, password, rememberPassword)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response
        if (!data) {
          reject(i18n.t('pages.userJs_Msg'))
        }
        let pageLimit = 10
        let theme = 'default'
        let closeType = ''
        let cacheSelectedNode = 0
        let cacheTimeQuery = 0
        const { id, roleIds, isSuperMode, isSuperRole, isSysRole, isSuperThreeUser,
          isMasterSubAble, name, account, permissions, upPwdTime, requestTimeout, viewMySelfLogAble } = data
        const headPic = data.headPic || require('@/assets/defaultHeadpic.gif')

        // 个性化配置
        if (response.data.personalizationConfig) {
          response.data.personalizationConfig.forEach(item => {
            const { sysUserId, type, value } = item
            if (type === 1 && sysUserId === id) {
              // 页面配置
              pageLimit = parseInt(value.substr(0, value.length - 1))
              closeType = parseInt(value.substr(value.length - 1, 1))
            } else if (type === 2 && sysUserId === id) {
              // 皮肤配置
              theme = value
            } else if (type === 5 && sysUserId === id) {
              // 操作配置，value 第一个字符是 缓存点击节点的配置 第二个字符是 同步时间的配置
              cacheSelectedNode = Number(value[0])
              cacheTimeQuery = Number(value[1])
            }
          });
        }
        if (!isSuperRole) {
          // 不是超级管理员才判断是否显示管理员日志
          if (!viewMySelfLogAble) {
            // 不允许查看自身管理员日志, 在权限列表中过滤掉管理员日志页面
            permissions.forEach((item, index) => {
              if (item === 'A15') {
                permissions.splice(index, 1)
              }
            })
          } else {
            // 允许查看自身管理员日志，不管是否有管理员日志权限，都在菜单列表中显示管理员日志菜单
            if (!permissions.includes('A15')) {
              permissions.push('A15')
            }
          }
        }
        // 从后台获取到的 请求超时 配置，修改 request 的默认超时时间
        const timeout = typeof requestTimeout == 'number' && requestTimeout >= 0 ? requestTimeout : state.requestTimeout
        request.defaults.timeout = timeout
        const permissionsMap = permissions.reduce((map, code) => {
          map[code] = true
          return map
        }, {})
        setUserId(id)
        commit('SET_ROLES', roleIds)
        commit('IS_SUPER_MODE', isSuperMode)
        commit('IS_SUPER_ROLE', isSuperRole)
        commit('IS_SYS_ROLE', isSysRole)
        commit('IS_SUPER_THREE_USER', isSuperThreeUser)
        commit('IS_MASTER_SUB', isMasterSubAble)
        commit('SET_NAME', name)
        commit('SET_ACCOUNT', account)
        commit('SET_USER_ID', id)
        commit('SET_AVATAR', headPic)
        commit('SET_USER_MENU_CODES', permissions)
        commit('SET_USER_MENU_CODES_MAP', permissionsMap)
        commit('SET_UP_PWD_TIME', upPwdTime)
        commit('SET_PAGE_LIMIT', pageLimit)
        commit('SET_THEME', theme)
        commit('SET_CLOSE_TYPE', closeType)
        commit('SET_CACHE_SELECTED_NODE', cacheSelectedNode)
        commit('SET_CACHE_TIME_QUERY', cacheTimeQuery)
        commit('SET_REQUEST_TIMEOUT', timeout)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  getRoleData({ commit }) {
    return new Promise((resolve, reject) => {
      getRoleList().then(res => {
        const roleList = res.data
        const currentRoleId = roleList.find(v => v.checked).id
        commit('SET_ROLE_LIST', roleList)
        commit('SET_CURRENT_ROLE_ID', currentRoleId)
        resolve({ roleList, currentRoleId })
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        removeUserId()
        removeToken()
        sessionStorage.removeItem('token')
        resetRouter()
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  },
  resetUpPwdTime({ commit }, newUpPwdTime) {
    return new Promise(resolve => {
      commit('SET_UP_PWD_TIME', newUpPwdTime)
    })
  },
  // is active master sub
  loadIsMasterSub({ commit }) {
    return new Promise((resolve, reject) => {
      getIsMasterSub().then(response => {
        commit('IS_MASTER_SUB', response.data)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  setCleanDuration({ commit }, newCleanDuration) {
    return new Promise(resolve => {
      commit('SET_CLEAN_DURATION', newCleanDuration)
    })
  },
  setRequestTimeout({ commit }, requestTimeout) {
    const timeout = typeof requestTimeout == 'number' && requestTimeout >= 0 ? requestTimeout : state.requestTimeout
    request.defaults.timeout = timeout
    commit('SET_REQUEST_TIMEOUT', timeout)
  },
  setTheme({ commit }, theme) {
    commit('SET_THEME', theme)
  },
  setPageLimit({ commit }, pageLimit) {
    commit('SET_PAGE_LIMIT', pageLimit)
  },
  setCloseType({ commit }, closeType) {
    commit('SET_CLOSE_TYPE', closeType)
  },
  setCacheSelectedNode({ commit }, cacheSelectedNode) {
    commit('SET_CACHE_SELECTED_NODE', cacheSelectedNode)
  },
  setCacheTimeQuery({ commit }, cacheTimeQuery) {
    commit('SET_CACHE_TIME_QUERY', cacheTimeQuery)
  },
  setCacheDateObj({ commit }, cacheDateObj) {
    commit('SET_CACHE_DATE_OBJ', cacheDateObj)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
