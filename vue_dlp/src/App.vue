<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { keepalive } from '@/api/system/organizational/sysUser'
import { getVersion } from '@/api/user'
import { getTokenTimestamp } from '@/utils/auth'

export default {
  name: 'App',
  data() {
    return {
      lastActiveTime: -1,
      keepaliveTimer: null,
      loginTimeoutMs: -1,
      visitCount: 1,           // 记录浏览器访问控制台的页面数量
      VISIT_COUNT: null        // 刷新或关闭时，获取Cookie中的 visitCount, 获取后该值不再修改
    }
  },
  mounted() {
    // 监听全局鼠标及键盘事件以更新用户的最后活跃时间
    document.addEventListener('mousedown', () => {
      this.lastActiveTime = new Date().getTime()
    })
    document.addEventListener('mousemove', () => {
      this.lastActiveTime = new Date().getTime()
    })
    document.addEventListener('keydown', (e) => {
      this.lastActiveTime = new Date().getTime()
      if (e.ctrlKey && e.key === 'b') {
        sessionStorage.setItem('debuggerMode', 1)
        console.warn('开启调试！')
      }
    })
    clearInterval(this.keepaliveTimer)
    // 定时监控用户登录情况
    this.keepaliveTimer = setInterval(async() => {
      const token = this.$store.getters.token
      const tokenTimestamp = getTokenTimestamp()
      if (!token || !tokenTimestamp || this.lastActiveTime === -1) { // 未登录 或 未执行鼠标和键盘操作
        if (token) {
          this.lastActiveTime = new Date().getTime()
        }
        console.log('token or lastActiveTime is empty')
        return
      }
      const current = new Date().getTime()
      if (this.lastActiveTime >= tokenTimestamp) { // 上次执行操作时间大于token的时间戳
        if (current - tokenTimestamp >= 60000) { // 当前时间与token时间戳的时间差大于1分钟，才执行请求，避免太过频繁的请求
          this.keepAliveAndGetTimeout()
        }
        console.log('lastActiveTime >= tokenTimestamp')
        return
      }
      if (this.loginTimeoutMs === -1) {
        this.keepAliveAndGetTimeout()
      }
      console.log(`当前时间戳=${current}, token时间戳=${tokenTimestamp}, 超时时间=${this.loginTimeoutMs / 1000}s, 已停止操作时长=${((current - tokenTimestamp) / 1000).toFixed(2)}s`)
      // 超时了，前端登出
      if (this.loginTimeoutMs > 0 && current - tokenTimestamp > this.loginTimeoutMs) {
        clearInterval(this.keepaliveTimer)
        await this.$store.dispatch('user/logout')
        this.$store.dispatch('commonData/clearWsNoticeInfo')
        this.$store.dispatch('permission/clearCommonRoute')
        this.$store.dispatch('tagsView/delAllViews')
        // 退出登录后，再次发送请求使页面跳转到首页并提示消息
        this.keepAliveAndGetTimeout()
      }
    }, 30000);
  },
  created() {
    // 验证加载的版本是否一致
    const version = localStorage.getItem('dlp_version')
    getVersion().then(res => {
      const newVersion = res.data
      if (!version || newVersion !== version) {
        // 本地未存储版本号 或 版本不一致，则记录版本号，并刷新浏览器
        localStorage.setItem('dlp_version', newVersion)
        location.reload()
      }
    }).catch(e => {
      console.error(e)
    })

    this.$store.dispatch('commonData/setTrialDlp')
    /**
     * 实现浏览器通过tab标签关闭系统时，再次访问系统时应处于未登录状态
     * from_nac_to_dlp = true 说明是从nac页面切换回dlp,dlp应处于登录状态
     **/
    // 开发模式下，通过 logoutWhenClose 控制是否实现上述功能
    if (this.isDev() && !this.$store.getters.logoutWhenClose) return
    window.onload = () => {
      const refresh = sessionStorage.getItem('isRefresh')
      const isRefresh = !!refresh && refresh > new Date().getTime()
      const isLogin = Cookies.get('isLogin')
      const vue_dlp_token = Cookies.get('vue_dlp_token')
      const from_nac_to_dlp = Cookies.get('from_nac_to_dlp')

      // 记录访问控制台的页面数量
      const visitCount = Cookies.get('visitCount')
      this.visitCount = Number(visitCount) ? Number(visitCount) + 1 : 1
      Cookies.set('visitCount', this.visitCount)

      if (isRefresh) {
        // 刷新，重新设置登录状态
        Cookies.set('isLogin', true)
        sessionStorage.removeItem('isRefresh')
      } else {
        // Cookies中没有登录状态isLogin，但存在 token
        if (!isLogin && vue_dlp_token) {
          // 如果不是从 nac 页面跳转过来的，则退出登录
          if (!from_nac_to_dlp) {
            this.$store.dispatch('user/logout')
          }
          // 清除 websocket通知信息
          this.$store.dispatch('commonData/clearWsNoticeInfo')
        }
      }
    }
    // onbeforeunload，onunload 兼容不同浏览器。
    window.onbeforeunload = () => {
      this.unloadFunction()
    }
    window.onunload = () => {
      this.unloadFunction()
    }
  },
  methods: {
    unloadFunction() {
      // 刷新或关闭时，更新 visitCount，因为这边需要多次调用，避免值不准，使用 VISIT_COUNT
      if (this.VISIT_COUNT == null) {
        const visitCount = Cookies.get('visitCount')
        this.VISIT_COUNT = Number(visitCount) ? Number(visitCount) - 1 : 0
      }
      Cookies.set('visitCount', this.VISIT_COUNT)
      // 当只打开一个页面时，才需要设置过期
      if (this.VISIT_COUNT <= 0) {
        // 设置3s过期的isRefresh，并删除isLogin
        var expiration = new Date().getTime() + 3 * 1000
        sessionStorage.setItem('isRefresh', expiration)
        Cookies.remove('isLogin')
      }
    },
    async keepAliveAndGetTimeout() {
      const resp = await keepalive()
      this.loginTimeoutMs = resp.data.loginTimeoutMs
    }
  },
  unmounted() {
    clearInterval(this.keepaliveTimer)
  }
}
</script>
