import { version } from '@/settings'

const code2char = (codes = '') => { return codes && String.fromCharCode(...codes.split(',')) }
// 下面这个函数用于将文字转为Unicode：
// ((str = '') => { return str.split('').map(s => s.charCodeAt()).toString() })('示例文字')

const resourceOption = {
  // 天锐版本
  tipray: {
    title: {
      // '天锐数据泄露防护系统'
      zh: code2char('22825,38160,25968,25454,27844,38706,38450,25252,31995,32479'),
      // '天銳數據洩露防護系統'
      tw: code2char('22825,37555,25976,25818,27945,38706,38450,35703,31995,32113'),
      // 'Tipray Data Loss Prevention System'
      en: code2char('84,105,112,114,97,121,32,68,97,116,97,32,76,111,115,115,32,80,114,101,118,101,110,116,105,111,110,32,83,121,115,116,101,109')
    },
    copyright: {
      // 'Copyright©2006-2023 厦门天锐科技股份有限公司'
      zh: code2char('67,111,112,121,114,105,103,104,116,169,50,48,48,54,45,50,48,50,51,32,21414,38376,22825,38160,31185,25216,32929,20221,26377,38480,20844,21496'),
      // 'Copyright©2006-2023 廈門天銳科技股份有限公司'
      tw: code2char('67,111,112,121,114,105,103,104,116,169,50,48,48,54,45,50,48,50,51,32,24264,38272,22825,37555,31185,25216,32929,20221,26377,38480,20844,21496'),
      // 'Copyright©2006-2023 Xiamen Tipray Technology Co.,Ltd'
      en: code2char('67,111,112,121,114,105,103,104,116,169,50,48,48,54,45,50,48,50,51,32,88,105,97,109,101,110,32,84,105,112,114,97,121,32,84,101,99,104,110,111,108,111,103,121,32,67,111,46,44,76,116,100')
    },
    website: code2char(''),
    theme: '',
    loginLogo: require('./tipray/logo-80.png'),
    topLogo: require('./tipray/logo-24.png'),
    favicon: require('./tipray/logo-16.png'),
    loginBackground: require('@/assets/login/loginBg.jpg')
  },
  // 中性版本
  neutral: {
    title: {
      // '数据泄露防护系统'
      zh: code2char('25968,25454,27844,38706,38450,25252,31995,32479'),
      // '數據洩露防護系統'
      tw: code2char('25976,25818,27945,38706,38450,35703,31995,32113'),
      // 'Data Loss Prevention System'
      en: code2char('68,97,116,97,32,76,111,115,115,32,80,114,101,118,101,110,116,105,111,110,32,83,121,115,116,101,109')
    },
    copyright: {
      // 'Copyright©2006-2023'
      zh: code2char('67,111,112,121,114,105,103,104,116,169,50,48,48,54,45,50,48,50,51'),
      tw: code2char('67,111,112,121,114,105,103,104,116,169,50,48,48,54,45,50,48,50,51'),
      en: code2char('67,111,112,121,114,105,103,104,116,169,50,48,48,54,45,50,48,50,51')
    },
    website: code2char(''),
    theme: '',
    loginLogo: require('./neutral/logo-80.png'),
    topLogo: require('./neutral/logo-24.png'),
    favicon: require('./neutral/logo-16.png'),
    loginBackground: require('@/assets/login/loginBg.jpg')
  }
}

const resource = resourceOption[version]

export { resource }
