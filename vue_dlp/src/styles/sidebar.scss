$sideBarWidth: 210px;

.topbar-container {
  z-index: 1111;
}
.main-container {
  height: calc( 100% - 50px );
  transition: margin-left .28s;
  margin-left: $sideBarWidth;
  overflow: auto;
  position: relative;
  top: 50px;
  z-index: 99;
}
.noLayout .main-container {
  height: 100%;
  margin-left: 0;
  top: 5px;
  .app-main {
    height: 100%;
    .app-container {
      height: 100%;
    }
  }
}

.sidebar-container {
  transition: width 0.28s;
  width: $sideBarWidth !important;
  position: fixed;
  font-size: 0px;
  top: 50px;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: visible;

  .horizontal-collapse-transition {
    transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
  }

  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }

  .el-scrollbar__bar.is-vertical {
    right: 0px;
  }

  .el-scrollbar {
    height: 100%;
  }

  .is-horizontal {
    display: none;
  }

  a {
    display: inline-block;
    width: 100%;
    overflow: hidden;
  }

  .svg-icon {
    margin-right: 10px;
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
  }

  & .nest-menu .el-submenu>.el-submenu__title,
  & .el-submenu .el-menu-item {
    min-width: $sideBarWidth !important;
    padding-left: 60px !important;
    padding-right: 20px;
    font-size: 13px;
    @include ellipsis;
  }
}

li.el-menu-item:hover .sidebar-favoricon {
  display: block;
}

.hideSidebar {
  .sidebar-container {
    width: 54px !important;
    
    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
  }

  .main-container {
    margin-left: 54px;
  }

  .submenu-title-noDropdown {
    padding: 0 !important;
    position: relative;

    .el-tooltip {
      padding: 0 !important;

      .svg-icon {
        margin-left: 20px;
      }
    }
  }

  .el-menu--collapse {
    .el-submenu {
      &>.el-submenu__title {
        &>span {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }
      }
    }
  }
}

.el-menu--collapse .el-menu .el-submenu {
  min-width: $sideBarWidth !important;
}

// mobile responsive
.mobile {
  .main-container {
    margin-left: 0px;
  }

  .sidebar-container {
    transition: transform .28s;
    width: $sideBarWidth !important;
  }

  &.hideSidebar {
    .sidebar-container {
      pointer-events: none;
      transition-duration: 0.3s;
      transform: translate3d(-$sideBarWidth, 0, 0);
    }
  }
}

.withoutAnimation {

  .main-container,
  .sidebar-container {
    transition: none;
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: calc(100vh - 58px);
    padding: 0;
    overflow-y: auto;
    .nest-menu:first-of-type {
      margin-top: 5px;
    }
    .nest-menu:last-of-type {
      margin-bottom: 5px;
    }
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 20px;
    }
  }
}
