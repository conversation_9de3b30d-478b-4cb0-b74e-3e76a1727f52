body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, Arial, Microsoft Yahei, Hiragino Sans GB, Heiti SC, WenQuanYi Micro Hei, sans-serif;
  color: #eee;
  overflow: hidden;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
  overflow: auto;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// 滚动条
// 滚动条整体部分
::-webkit-scrollbar{
  position: absolute;
  width: 9px;
  height: 11px;
  background-color: transparent;
}
// 滚动条的轨道
::-webkit-scrollbar-track{
  box-shadow: inset 0 0 6px rgba(0,0,0,0.2);
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.2);
  border-radius: 10px;
  background-color: transparent;
}
// 滚动条里面的小方块
::-webkit-scrollbar-thumb{
  background-color: rgb(70, 102, 161);
  box-shadow: inset 0 0 6px rgba(0,0,0,0.2);
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.2);
  border-radius: 10px;
  border-width: 1.5px;
  border-style: solid;
  border-color: transparent;
  background-clip: padding-box;
}
::-webkit-scrollbar-thumb:hover {
  background: #2256b6;
}
.el-dialog__body::-webkit-scrollbar-thumb{
  border-width: 0;
}
.el-dialog__body::-webkit-scrollbar{
  width: 12px;
}
// 边角，及两个滚动条的交汇处
::-webkit-scrollbar-corner {
  background-color: transparent;
}

// main-container global css
// navbar
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    color: #eee;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;
  }

  .breadcrumb-container {
    float: left;
  }
}
.el-tab-pane .app-container{
  height: 100%;
}
.app-main>.app-container{
  position: relative;
}
.app-container {
  height: calc(100vh - 85px);
  padding: 5px 20px 20px 15px;
}
.tree-container{
  width:200px;
  height:100%;
  float:left;
  &.hidden{
    display: none;
    &+.table-container, &+.table-container-dialog{
      margin-left: 0 !important;
    }
  }
}
.table-container, .table-container-dialog {
  height: 100%;
  margin-left: 0;
  display: flex;
  flex-direction: column;
  .toolbar{
    flex-shrink: 0;
    >span{
      display: inline-block;
    }
  }
  .tableBox{
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    height: 0;
    clear: both;
  }
}
.tree-container+.table-container,
.tree-container+.table-container-dialog {
  margin-left: 210px;
}
.tree-container+.app-container{
  margin-left: 216px;
}
.tree-container.hidden+.app-container{
  margin-left: 0;
}
.app-main{
  height: 100%;
  .toolbar{
    margin-bottom: 7px;
    font-size: 14px;
    >span{
      display: inline-block;
    }
    >.el-button, >.el-input,
    >span .el-input, >span .el-select, 
    >span .el-cascader, >span .el-button,
    .download-executor .el-button,
    .el-date-editor.el-input,
    .el-range-editor.el-input__inner {
      margin-bottom: 3px;
    }
    .el-input-group__append>.el-select {
      margin-bottom: 0;
    }
    .searchCon{
      float: right;
    }
  }
}
.hidden{
  display: none;
}

.underline {
  text-decoration: underline;
}

.ellipsis {
  @include ellipsis
}

.cursor-pointer {
  cursor: pointer;
}