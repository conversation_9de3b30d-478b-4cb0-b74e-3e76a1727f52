// body需要添加与文件名相同的类名，以实现覆盖样式的需求

$bodyBg:#f5f5f5;
$bodyColor: #666;
$greyBd: 1px solid #aaa;
$inputBd: 1px solid #aaa;
$tableBd: 1px solid #089ba2;

// topbar
$topMenuBg: #148676;
$topMenuBgHover: #1dad9c;
$topMenuText: #fff;
$topMenuActiveText: #ffbe00;

// sidebar
$menuBg:#19a794;
$menuHover:#1e9d8d;
$subMenuBg:#bdf5ed;
$subMenuHover:#a3f3e7;
$menuText:#555;
$menuActiveText:#222;
$subMenuActiveText:#222;

// tags-view
$tagsActiveBg: #148676;
$tagsActiveBd: #077c6c;
$tagsActiveText: #fff;

// button
$btnText: #36bfac;
$btnBg: transparent;
$btnBd: $btnText;
$btnTextHover: #3ab4a4;
$btnBgHover: transparent;
$btnBdHover: $btnTextHover;
$btnTextActive: #2ea796;
$btnBgActive: transparent;
$btnBdActive: $btnTextActive;
$btnTextDisabled: #888;
$btnBgDisabled: transparent;
$btnBdDisabled: $btnTextDisabled;

$btnTextPrimary: #085e53;
$btnBgPrimary: #4ccdbb;
$btnBdPrimary: $btnBgPrimary;
$btnTextHoverPrimary: $btnTextPrimary;
$btnBgHoverPrimary: #34ad9b;
$btnBdHoverPrimary: $btnBgHoverPrimary;
$btnTextActivePrimary: $btnTextPrimary;
$btnBgActivePrimary: #1e9180;
$btnBdActivePrimary: $btnBgActivePrimary;

// table
$thText: $btnTextPrimary;
$thBg: $btnBd;
$tdBg: #eee;

$tbCheckboxDisabled: #bbb;
$tbBtnText: #19ab96;

// dialog
$dlgHeadText: #333;
// $dlgHeadBg: linear-gradient(#c0fae0, #148676);
$dlgHeadBg: linear-gradient($subMenuHover, $topMenuBg);
$dlgHeadBdBottom: 1px solid #616f78;
$dlgBodyBg: #e4e7e9;

$dlgCancleText: #fff;
$dlgCancleBg: linear-gradient(#d7d7d7, #6c6c6c);
$dlgCancleBd: #d7d7d7;
$dlgCancleTextHover: #fff;
$dlgCancleBgHover: linear-gradient(#d7d7d7, #5c5c5c);
$dlgCancleBdHover: #d7d7d7;
$dlgCancleTextActive: #fff;
$dlgCancleBgActive: linear-gradient(#d7d7d7, #4c4c4c);
$dlgCancleBdActive: #d7d7d7;
$dlgCancleTextDisabled: #a9a9a9;
$dlgCancleBgDisabled: linear-gradient(#d7d7d7, #d7d7d7);
$dlgCancleBdDisabled: #d7d7d7;

$dlgConfirmText: #fff;
$dlgConfirmBg: linear-gradient(#a3f3e7, #2c9889);
$dlgConfirmBd: #87ded1;
$dlgConfirmTextHover: #fff;
$dlgConfirmBgHover: linear-gradient(#95f3e5, #229b8a);
$dlgConfirmBdHover: #7bdbcd;
$dlgConfirmTextActive: #fff;
$dlgConfirmBgActive: linear-gradient(#7febdb, #158373);
$dlgConfirmBdActive: #6cc7ba;

$scrollbarThumb: #46a184;
$scrollbarThumbHover: #2a7e63;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  bodyBg: $bodyBg;
}

body.custom-green {
  background: $bodyBg;
  color: $bodyColor;

  // 滚动条里面的小方块
  ::-webkit-scrollbar-thumb{
    background-color: $scrollbarThumb;
    box-shadow: inset 0 0 6px rgba(0,0,0,0.2);
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.2);
  }
  ::-webkit-scrollbar-thumb:hover {
    background: $scrollbarThumbHover;
  }

  .navbar {
    background: $bodyBg;
  }

  // topbar
  .topbar {
    background: $topMenuBg;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
  }
  .topbar-logo-container {
    background: $topMenuBg;
    & .topbar-logo-link .topbar-title {
      color: $topMenuText;
    }
  }
  .right-menu {
    color: $topMenuText;
    .right-menu-item {
      color: $topMenuText;
      &.hover-effect {
        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }
    .avatar-container .el-icon-caret-bottom{
      color: $topMenuText;
    }
  }

  // el-menu

  .el-menu {
    background: $topMenuBg;
  }
  .el-submenu__title:hover, .el-menu-item:hover{
    background: $topMenuBgHover !important;
  }
  .el-menu--horizontal {
    .router-link-active{
      color: $topMenuActiveText;
    }
    .el-menu  {
      .el-menu-item, .el-submenu__title {
        background: $topMenuBg;
        color: $topMenuText;
      }
    }
    >.el-menu{
      .el-menu-item.is-active, .el-submenu.is-active>.el-submenu__title{
        color: $topMenuActiveText;
      }
    }
    >.el-submenu {
      .el-submenu__title{
        color: $topMenuText;
      }
      &:focus .el-submenu__title,
      &:hover .el-submenu__title{
        color: $topMenuText;
      }
      &.is-active .el-submenu__title{
        color: $topMenuText;
      }
      &.is-active>.el-submenu__title{
        color: $topMenuActiveText;
        border-bottom-color: $topMenuActiveText;
      }
    }
    .el-menu-item{
      color: $topMenuText;
      &:not(.is-disabled):focus, &:not(.is-disabled):hover{
        color: $topMenuText;
      }
      &.is-active{
        color: $topMenuActiveText;
      }
    }
  }
  // 滚动条颜色
  .el-menu--popup::-webkit-scrollbar-thumb {
    background: rgba(17,80,205,0.3);
  }

  // 左侧菜单
  .sidebar-container{
    background-color: $menuBg;
    .el-submenu__title{
      background-color: $menuBg;
      color: $menuText;
    }
    .is-active>.el-submenu__title {
      color: $menuActiveText !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        background-color: $menuHover !important;
      }
    }
    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      background-color: $subMenuBg !important;
      color: $menuText;
      &:hover, &.is-active {
        background-color: $subMenuHover !important;
      }
      &.is-active{
        color: $subMenuActiveText;
      }
    }
  }
  // 左侧菜单收缩
  .el-menu--vertical {
    .nest-menu .el-submenu>.el-submenu__title,
    .el-menu-item {
      color: #eee;
      &:hover {
        // you can use $subMenuHover
        background-color: $menuHover !important;
      }
      &.is-active{
        color: $subMenuActiveText;
      }
    }
    .sidebar-favoricon {
      left: unset;
    }
  }
  .sidebar-favoricon{
    &:hover, &:active{
      color: #000;
    }
    &.is-active{
      color: #ffbe00;
      display: block;
    }
  }

  // 面包屑导航
  .hamburger-container {
    background: $bodyBg;
    color: $bodyColor;
  }

  .tags-view-container {
    background: $bodyBg;
    color: $bodyColor;
    border-bottom: 1px solid #2674b2;
    .tags-view-wrapper {
      .tags-view-item {
        border: $greyBd;
        color: $bodyColor;
        background: $bodyBg;
        &.active {
          background-color: $tagsActiveBg;
          color: $tagsActiveText;
          border-color: $tagsActiveBd;
          &::before {
            background: $tagsActiveText;
          }
        }
      }
    }
    .contextmenu {
      background: #fff;
      color: #333;
      border: 1px solid #888;
      box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
      li {
        &:hover {
          background: #eee;
        }
      }
    }
  }

  .fixed-header {
    background: $bodyBg;
  }

  .dashboard-editor-container {
    background: $bodyBg;
    .chart-wrapper {
      background: #fff;
    }
    .panel-group .card-panel {
        color: #666;
        background: #fff;
        box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
        border-color: rgba(0, 0, 0, .05);
        &:hover {
          .card-panel-icon-wrapper {
            color: #fff;
          }
        }
        .card-panel-description {
          .card-panel-text {
            color: #666;
          }
        }
      }
  }

  @mixin normalTable {
    .el-table {
      color: $bodyColor;
      background-color: $bodyBg;
      border: $inputBd;
      thead {
        color: $thText;
      }
      th {
        background-color: $thBg;
      }
      td {
        background-color: $tdBg;
      }
      td, th.is-leaf {
        border-bottom: $inputBd;
      }
      .el-checkbox__input.is-disabled .el-checkbox__inner {
        background-color: $tbCheckboxDisabled;
        &::after {
          border-color: $tbCheckboxDisabled;
        }
      }
      .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
        border-color: $thBg;
      }
      .ascending .sort-caret.ascending {
        border-bottom-color: $thText;
      }
      .descending .sort-caret.descending {
        border-top-color: $thText;
      }
      .el-table__fixed-right-patch {
        background-color: $thBg;
        border-color: $thBg;
      }
      &.el-table--border th.gutter:last-of-type {
        border-color: $thBg;
      }
      // .el-button--text {
      //   color: $tbBtnText;
      // }
    }
    .el-table--striped .el-table__body tr.el-table__row--striped td {
      background: #f8f8f8;
    }
    .el-table--enable-row-hover .el-table__body tr:hover>td {
      background-color: #ccc;
    }
    .el-table--striped .el-table__body tr.el-table__row--striped.current-row td, .el-table__body tr.current-row>td, .el-table__body tr.hover-row.current-row>td, .el-table__body tr.hover-row.el-table__row--striped.current-row>td, .el-table__body tr.hover-row.el-table__row--striped>td, .el-table__body tr.hover-row>td {
      background-color: #ccc;
    }
    .el-table--border::after, .el-table--group::after, .el-table::before {
      background-color: $bodyBg;
    }
    .el-table--border th.el-table__cell, .el-table__fixed-right-patch{
      border-bottom:1px solid #aaaaaa;
    }
    .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
      border-right: $inputBd;
    }
    .el-loading-mask {
      background-color: rgba(0,0,0,0.7);
    }
    .el-table__fixed-right::before, .el-table__fixed::before {
      background-color: transparent;
    }
    .el-table__fixed, .el-table__fixed-right{
      -webkit-box-shadow: 0 0 10px rgba(0,0,0,.12);
      box-shadow: 0 0 10px rgba(0,0,0,.12);
    }
  }
  @include normalTable;
  
  @mixin dialogTable {
    .el-table__fixed-right-patch {
      background-color: #e4e7e9;
      border-color: #aaa;
    }
  }
  // dialog、data-editor, popover, drawer 中 其他组件的样式
  .el-dialog__body, .data-editor, .el-popover, .el-drawer__body {
    @include dialogTable;
  }
  .el-drawer__body {
    .el-input-group__append, .el-input-group__prepend {
      background-color: transparent;
    }
    .el-form-item__content {
      color: #666;
    }
  }

  // icon
  .el-icon-my-checkbox{
    background: url("~@/assets/icon/checkbox-gray.png") center no-repeat;
  }
  .el-icon-my-checkbox:hover{
    background: url("~@/assets/icon/checkbox-gray.png") center no-repeat;
  }
  .el-icon-my-checkbox-disabled {
    background: #bbb url("~@/assets/icon/checkbox-gray.png") center no-repeat;
  }
  .el-icon-my-checked-disabled {
    background: #fff url("~@/assets/icon/checkbox-checked-disabled2.png") center no-repeat;
  }

  // button
  @mixin normalButton {
    .el-button {
      color: $btnText;
      background-color: $btnBg;
      border-color: $btnBd;
      &:focus, &:hover {
        color: $btnTextHover;
        background-color: $btnBgHover;
        border-color: $btnBdHover;
      }
      &.is-active, &:active {
        color: $btnTextActive;
        background-color: $btnBgActive;
        border-color: $btnBdActive;
      }
      &.is-disabled, &.is-disabled:focus, &.is-disabled:hover {
        color: $btnTextDisabled;
        background-color: $btnBgDisabled;
        border-color: $btnBdDisabled;
      }
    }
    .el-button--text {
      color: $tbBtnText;
      background-color: transparent;
    }
    .el-button--primary {
      color: $btnTextPrimary;
      background-color: $btnBgPrimary;
      border-color: $btnBdPrimary;
      &:focus, &:hover {
        color: $btnTextHoverPrimary;
        background-color: $btnBgHoverPrimary;
        border-color: $btnBdHoverPrimary;
      }
      &.is-active, &:active {
        color: $btnTextActivePrimary;
        background-color: $btnBgActivePrimary;
        border-color: $btnBdActivePrimary;
      }
    }
  }
  @mixin footerButton {
    .el-button {
      color: $dlgCancleText;
      background: $dlgCancleBg;
      border-color: $dlgCancleBd;
      &:focus, &:hover {
        color: $dlgCancleTextHover;
        background: $dlgCancleBgHover;
        border-color: $dlgCancleBdHover;
      }
      &.is-active, &:active {
        color: $dlgCancleTextActive;
        background: $dlgCancleBgActive;
        border-color: $dlgCancleBdActive;
      }
      &.is-disabled, &.is-disabled:focus, &.is-disabled:hover {
        color: $dlgCancleTextDisabled;
        background: $dlgCancleBgDisabled;
        border-color: $dlgCancleBdDisabled;
      }
    }
    .el-button--primary {
      color: $dlgConfirmText;
      background: $dlgConfirmBg;
      border-color: $dlgConfirmBd;
      &:focus, &:hover {
        color: $dlgConfirmTextHover;
        background: $dlgConfirmBgHover;
        border-color: $dlgConfirmBdHover;
      }
      &.is-active, &:active {
        color: $dlgConfirmTextActive;
        background: $dlgConfirmBgActive;
        border-color: $dlgConfirmBdActive;
      }
    }
  }
  @include normalButton;

  // alert
  .el-alert--info {
    border: $greyBd;
  }

  // tree
  .tree-menu {
    border: $greyBd;
  }
  .el-tree {
    background: transparent;
    color: $bodyColor;
    .el-tree-node__content:hover {
      background-color: #ddd;
    }
    .el-tree-node:focus>.el-tree-node__content {
      background-color: transparent;
      &:hover{
        background-color: #ddd;
      }
    }
    &.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
      background-color: #ddd;
    }
    .el-tree-node__expand-icon {
      color: #aaa;
      &.is-leaf {
        color: transparent;
      }
    }
  }

  // input
  .el-input {
    .el-input__count .el-input__count-inner {
      background: transparent;
    }
    &.is-disabled .el-input__inner{
      background-color: #e8e8e8;
      border-color: #aaa;
      color: #888;
    }
  }
  .el-input__inner {
    background-color: transparent;
    border: $inputBd;
    color: #666;
    &:hover {
      border-color: #888;
    }
    &:focus {
      border-color: #666;
    }
  }
  input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
    color: #aaa !important;
  }
  input::-moz-placeholder, textarea::-moz-placeholder {
    color: #aaa !important;
  }
  input:-ms-input-placeholder, textarea:-ms-input-placeholder {
    color: #aaa !important;
  }
  .el-input-group__append, .el-input-group__prepend {
    border: 1px solid #aaa;
  }
  .el-input-group__append {
    border-left: 0;
    .el-input__inner, .el-input__inner:read-only {
      padding-bottom: 1px;
      border: 0;
      border-radius: 0px 3px 3px 0;
    }
  }
  .el-checkbox__label {
    color: #666;
  }
  .el-checkbox__inner {
    background-color: transparent;
    border: 1px solid #888;
  }
  .el-checkbox__input.is-disabled .el-checkbox__inner{
    background-color: transparent;
  }
  .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner{
    background-color: #888;
    border-color: #888;
  }
  .el-radio{
    color: #666;
  }

  // textarea
  .el-textarea .el-input__count {
    background: transparent;
  }
  .el-textarea__inner {
    background: transparent;
  }

  .el-date-editor .el-range-separator {
    color: #ccc;
  }

  // pagination
  @mixin normalPagination {
    .pagination-container {
      background: $bodyBg;
      .el-pagination__total {
        color: $bodyColor;
      }
      .el-pagination__jump {
        color: $bodyColor;
      }
    }
    .el-pagination__total, .el-pagination__jump {
      color: $bodyColor;
    }
    .el-pagination.is-background .el-pager li:not(.disabled):hover {
      color: $btnText;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      color: #fff;
      background: $btnBd;
    }
    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
      background: $bodyBg;
      color: $bodyColor;
    }
    .el-pagination.is-background .btn-next.disabled,
    .el-pagination.is-background .btn-next:disabled,
    .el-pagination.is-background .btn-prev.disabled,
    .el-pagination.is-background .btn-prev:disabled,
    .el-pagination.is-background .el-pager li.disabled {
      color: #ccc;
    }
  }
  @include normalPagination;

  // tabs
  .el-tabs {
    border: 1px solid #aaa;
  }
  .el-tabs__item {
    color: $bodyColor;
    &.is-active {
      color: #409EFF;
    }
  }
  .el-tabs__nav-wrap::after {
    color: #888;
    background-color: #ccc;
  }

  // icon
  .text-button-icon {
    color: $tbBtnText;
    cursor: pointer;
  }

  //tree-menu
  .custom-tree-node {
    color: #4386c6;
  }

  .el-ic {
    .svg-icon {
      color: #4386c6;
    }
  }

  .el-tree-node__content:hover .el-ic {
    color: #428bca !important;
  }

  // card
  .el-card{
    border: solid 1px #aaaaaa;
  }
  .el-card__header {
    border-bottom: 1px solid #878787;
  }

  // dialog
  .el-dialog__wrapper {
    background: rgba(0,0,0,0.5);
  }
  .el-dialog {
    background: $dlgBodyBg;
  }
  .el-dialog__header {
    background: $dlgHeadBg;
    border-bottom: $dlgHeadBdBottom;
  }
  .el-dialog__title {
    color: $dlgHeadText;
  }
  .el-dialog__headerbtn {
    .el-dialog__close {
      color: $dlgHeadText;
    }
  }
  .el-dialog__body {
    color: #666;
    .el-input {
      .el-input__count .el-input__count-inner {
        background-color: #f5f5f5;
      }
      &.is-disabled .el-input__inner {
        background-color: #e4e7e9;
        border-color: #aaa;
        color: #888;
      }
    }
    .el-input__inner, .el-textarea__inner {
      background-color: #f5f5f5;
      border: 1px solid #aaa;
      color: #666;
      &:focus, &:hover {
        border: 1px solid #888;
      }
    }
    .el-radio__inner {
      border: 1px solid #888;
    }
    .el-checkbox__inner {
      background-color: #fff;
      border: 1px solid #888;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #409EFF;
      border-color: #409EFF;
    }
    .el-checkbox__input.is-disabled {
      .el-checkbox__inner{
        background-color: #f5f7fa;
      }
    }
    .el-checkbox__input.is-disabled.is-checked,
    .el-checkbox__input.is-disabled.is-indeterminate {
      .el-checkbox__inner{
        background-color: #888;
        border-color: #888;
      }
    }
    .el-checkbox__label {
      color: #666;
    }
    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #409EFF;;
    }
    .el-checkbox__input.is-disabled+span.el-checkbox__label {
      color: #888;
    }
    .el-textarea.is-disabled .el-textarea__inner {
      background-color: #e4e7e9;
      border-color: #aaa;
      color: #888;
      &:hover {
        border-color: #aaa;
      }
    }
    .el-select {
      .el-input.is-disabled .el-input__inner:hover {
        border-color: #aaa;
      }
    }

    .el-input-group__append {
      .el-input__inner, .el-input__inner:read-only {
        border: 0;
      }
    }
    .el-radio {
      color: #666;
    }
    .el-switch__core {
      background: #ccc;
      border: 1px solid #ccc;
    }
    .el-switch.is-checked .el-switch__core {
      border-color: #409EFF;
      background-color: #409EFF;
    }
    .el-tag.el-tag--info {
      border-color: #b6b6b6;
    }
    .el-tabs.el-tabs--top {
      border: 1px solid #aaa;
    }
    .tree-menu {
      border: 1px solid #aaa !important;
      .el-input__inner {
        border: 1px solid #aaa !important;
        &:focus, &:hover {
          border: 1px solid #888 !important;
        }
      }
      .el-tree-node__loading-icon {
        color: #0082ff;
      }
    }
    .el-tree {
      background: transparent;
      color: #666;
      .el-tree-node__content:hover {
        background-color: #ccc;
      }
      .el-tree-node:focus>.el-tree-node__content {
        background-color: transparent;
        &:hover{
          background-color: #ccc;
        }
      }
      &.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
        background-color: #ccc;
      }
      .el-tree-node__expand-icon {
        color: #666;
      }
      .el-tree-node__expand-icon.is-leaf {
        color: transparent;
      }
    }
    .el-table {
      color: #666;
      background-color: transparent;
      border: 1px solid #aaa;
      thead {
        color: #666;
      }
      th {
        background-color: #e4e7e9;
      }
      td {
        background-color: #ddd;
      }
      td, th.is-leaf {
        border-bottom: 1px solid #aaa;
      }
      .el-checkbox__input.is-disabled .el-checkbox__inner {
        background-color: #e4e7e9;
        border: 1px solid #aaa;
        color: #888;
      }
      .ascending .sort-caret.ascending {
        border-bottom-color: #666;
      }
      .descending .sort-caret.descending {
        border-top-color: #666;
      }
    }
    .el-table--striped .el-table__body tr.el-table__row--striped td {
      background: #e4e7e9;
    }
    .el-table--enable-row-hover .el-table__body tr:hover>td {
      background-color: #ccc;
    }
    .el-table--striped .el-table__body tr.el-table__row--striped.current-row td, .el-table__body tr.current-row>td, .el-table__body tr.hover-row.current-row>td, .el-table__body tr.hover-row.el-table__row--striped.current-row>td, .el-table__body tr.hover-row.el-table__row--striped>td, .el-table__body tr.hover-row>td {
      background-color: #ccc;
    }
    .el-table--border::after, .el-table--group::after, .el-table::before {
      background-color: #e4e7e9;
    }
    .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
      border-right: 1px solid #aaa;
    }
    .el-loading-mask {
      background-color: rgba(0,0,0,0.7);
    }
    // .el-checkbox__inner {
    //   background-color: #fff;
    //   border: 1px solid #888;
    // }
    .pagination-container {
      background: #e4e7e9;
      .el-pagination__total {
        color: #666;
      }
      .el-pagination__jump {
        color: #666;
      }
      .el-pagination .el-select .el-input .el-input__inner {
        background: #fff;
      }
      .el-pagination.is-background .el-pager li:not(.disabled).active {
        background: #bababa;
      }
      .el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li {
        background: #e4e7e9;
        color: #666;
      }
      .el-pagination.is-background .btn-next.disabled, .el-pagination.is-background .btn-next:disabled, .el-pagination.is-background .btn-prev.disabled, .el-pagination.is-background .btn-prev:disabled, .el-pagination.is-background .el-pager li.disabled {
        color: #ccc;
      }
    }
    .el-card {
      background-color: #e4e7e9;
      border-color: #EBEEF5;
      &.is-always-shadow, &.is-hover-shadow:focus, &.is-hover-shadow:hover {
        -webkit-box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.2);
        box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.2);
      }
    }
    .el-card__header {
      border-bottom: 1px solid #ccc;
    }
    .el-tabs__item {
      color: #666;
      &.is-active {
        color: #409EFF;
      }
    }
    .el-tabs__nav-wrap::after {
      background-color: #888;
    }
    .el-divider .el-divider__text {
      background-color: #e4e7e9;
    }
  }
  .el-dialog__footer {
    @include footerButton;
  }

  // messageBox
  .el-message-box {
    background: $dlgBodyBg;
  }
  .el-message-box__header {
    background: $dlgHeadBg;
    border-bottom: $dlgHeadBdBottom;
  }
  .el-message-box__title {
    color: $dlgHeadText;
  }
  .el-message-box__headerbtn {
    .el-message-box__close {
      color: $dlgHeadText;
    }
  }

  .el-message-box__content {
    color: #666;
    .el-input__inner, .el-textarea__inner {
      background-color: #f5f5f5;
      border: 1px solid #aaa;
      color: #666;
      &:focus, &:hover {
        border: 1px solid #888;
      }
    }
  }
  .el-message-box__btns {
    @include footerButton;
  }
  .el-drawer__footer {
    @include footerButton;
  }
  // popover
  .el-popover{
    .el-input{
      background-color: transparent;
      .el-input__count .el-input__count-inner{
        background-color: #f5f5f5;
      }
      &.is-disabled .el-input__inner{
        background-color: #eee;
        border-color: #aaa;
        color: #888;
      }
    }
    .el-input__inner, .el-textarea__inner{
      color: #666;
      background-color: transparent;
      border: 1px solid #aaa;
      &:focus, &:hover{
        border: 1px solid #888;
      }
    }
    .el-checkbox__label{
      color: #666;
    }
    .el-checkbox__inner{
      background-color: #fff;
      border: 1px solid #888;
    }
    .el-checkbox__input.is-disabled .el-checkbox__inner {
      background-color: #eee;
    }
  }

  .app-container>.el-form,
  .app-container>div>.el-form,
  .app-main>.el-form {
    .el-form-item__label{
      color: #666;
    }
    .el-switch__label{
      color:#666;
    }
    .el-switch__label.is-active{
      color:#409EFF;
    }
    .el-radio__inner{
      border: 1px solid #aaa;
    }
    .el-divider__text {
      color: #333;
      background: $bodyBg;
    }
  }
  .el-radio__inner{
    border: 1px solid #888;
  }
  .el-divider {
    background-color: #888;
  }
  .el-divider__text {
    color: #333;
    background: $bodyBg;
  }
  .serverInfo .server-form .el-divider__text {
    color: #333;
    background: $bodyBg;
  }
  .global-config {
    .el-form-item__label{
      color: #666;
    }
    .el-divider__text {
      color: #333;
      background: $bodyBg;
    }
  }
  
  .config-all {
    border: 1px solid $subMenuHover;
    background: linear-gradient(135deg, $subMenuBg 0%, $menuBg 100%);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  .config-all:hover {
    background: linear-gradient(135deg, $subMenuHover 0%, $menuHover 100%);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  .config-all .title {
    color: #374151;
  }
  .config-all .description {
    color: #6b7280;
  }

  // 服务器状态
  .legend {
    border: 1px solid #666;
  }
  .serverInfo {
    .el-collapse-item__header {
      color: #333;
      border-color: #aaa;
    }
  }

  // 资源管理器布局
  .layout-box {
    color: #666;
    background-color: #f5f5f5;
    border: 1px solid #aaa;
    .highlight-border {
      border: 1px solid #aaa;
    }
    .highlight-item {
      background: #ccc;
      &:hover {
        background-color: #ccc;
        border: 1px solid #aaa;
      }
    }
  }
  .layout-item:hover {
    background-color: #eee;
    border: 1px solid #eee;
  }

  // 通讯工具聊天记录
  .msg-box {
    border: 1px solid #aaa;
    &>div {
      border: 1px solid #ccc;
    }
  }
  // 控制台敏感内容管控报表配置
  .sensitiveness-report {
    // 时间线样式
    .el-timeline-item__content{
      color: $bodyColor;
    }
  }
  .rounded-rectangle {
    border: 1px solid #aaa;
    background-color: rgba(255,255,255,0.7); /* 设置矩形的背景颜色 */
  }
  .thediv-span {
    color: $btnText;
    border: 1px solid #aaa;
    border-bottom: none;
    background-color: rgba(255,255,255,0.7);
  }
  // 大屏--报表样式
  .largeScreen-report{
    .large-screen-head-title{
      color:$topMenuBg;
    }
    .large-screen-row>.el-col{
      .large-screen-border{
        border:1px solid #cccccc;
      }
      .large-screen-title-div{
        background: $topMenuBg;
      }
      //中间特殊的小块
      .large-screen-middle-out{
        border: 1px solid #cccccc;
      }
    }
  }


}
