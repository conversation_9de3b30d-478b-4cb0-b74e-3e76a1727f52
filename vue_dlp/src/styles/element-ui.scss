$bodyBg:#0c161f;
$greyBd: 1px solid #666;
$inputBd: 1px solid #68a8d0;
$tableBd: 1px solid #089ba2;

// topbar
$topMenuBg: #132a3f;
$topMenuBgHover: #295577;
$topMenuText: #fff;
$topMenuActiveText: #409EFF;

// sidebar
$menuBg:#295577;
$menuHover:#095998;
$subMenuBg:#3b749c;
$subMenuHover:#5087ad;
$menuText:#bbbbbb;
$menuActiveText:#ffffff;
$subMenuActiveText:#ffffff;


// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  bodyBg: $bodyBg;
}


body {
  background: $bodyBg;
}
.navbar {
  background: $bodyBg;
}

// topbar
.topbar {
  height: 50px;
  width: 100%;
  background: $topMenuBg;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  position: fixed;
  top: 0;
  z-index: 1111;
  overflow: hidden;
}
.topbar-logo-container {
  background: $topMenuBg;
  & .topbar-logo-link .topbar-title {
    color: $topMenuText;
  }
}

.el-menu.el-menu--horizontal{
  height: 100%;
  border-bottom: 0;
  position: absolute;
  top: 0;
  left: 250px;
  >li {
    line-height: 50px;
    >.el-submenu__title {
      line-height: 50px;
      .el-submenu__icon-arrow {
        position: absolute;
        right: 5px;
      }
    }
    &.is-opened>.el-submenu__title {
      max-width: none;
    }
  }
  li{
    height: 100%;
  }
}
.el-menu--horizontal .el-menu {
  .el-submenu__title {
    padding: 0 30px 0 10px;
  }
  .el-menu-item {
    padding: 0;
  }
  .el-submenu__icon-arrow {
    right: 15px;
  }
}

.right-menu {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  line-height: 50px;
  color: $topMenuText;
  &>div{
    display: inline-block;
    vertical-align: middle;
  }

  &:focus {
    outline: none;
  }

  .el-icon-search {
    margin-right: 5px;
    outline: none;
    cursor: pointer;
  }

  .right-menu-item {
    display: inline-block;
    padding: 0 8px;
    height: 100%;
    font-size: 18px;
    color: $topMenuText;

    &.hover-effect {
      line-height: 46px;
      cursor: pointer;
      transition: background .3s;

      &:hover {
        background: rgba(0, 0, 0, .025)
      }
    }
  }

  .avatar-container {
    height: 100%;
    margin-right: 30px;

    .avatar-wrapper {
      margin-top: 5px;
      position: relative;

      .user-avatar {
        color: #eee;
        cursor: pointer;
        font-size: 26px;
      }

      .el-icon-caret-bottom {
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translate(0, -6px);
        color: $topMenuText;
        font-size: 12px;
        cursor: pointer;
      }
    }
  }
}

.el-col>label {
  width: auto;
  @include ellipsis;
}

// el-menu
.el-menu {
  background: $topMenuBg;
}
.el-submenu__title:focus, .el-menu-item:focus{
  background: transparent;
}
.el-submenu__title:hover, .el-menu-item:hover{
  background: $topMenuBgHover !important;
}
.el-submenu__title:active, .el-menu-item:active{
  background: $topMenuBgHover !important;
}
.el-menu--horizontal {
  .router-link-active{
    color: $topMenuActiveText;
  }
  .el-menu  {
    .el-menu-item, .el-submenu__title {
      background: $topMenuBg;
      color: $topMenuText;
    }
  }
  >.el-menu{
    .el-menu-item.is-active, .el-submenu.is-active>.el-submenu__title{
      color: $topMenuActiveText;
    }
  }
  >.el-submenu {
    .el-submenu__title{
      color: $topMenuText;
    }
    &:focus .el-submenu__title,
    &:hover .el-submenu__title{
      color: $topMenuText;
    }
    &.is-active .el-submenu__title{
      color: $topMenuText;
    }
    &.is-active>.el-submenu__title{
      color: $topMenuActiveText;
      border-bottom-color: $topMenuActiveText;
    }
  }
  .el-menu-item{
    color: $topMenuText;
    &:not(.is-disabled):focus, &:not(.is-disabled):hover{
      color: $topMenuText;
    }
    &.is-active{
      color: $topMenuActiveText;
    }
  }
}
// 滚动条颜色
.el-menu--popup::-webkit-scrollbar-thumb {
  background: rgba(144,147,153,.3);
}

// 左侧菜单
.sidebar-container{
  background-color: $menuBg;
  .el-submenu__title{
    padding: 0 30px 0 20px;
    background-color: $menuBg;
    color: $menuText;
    @include ellipsis;
  }
  .is-active>.el-submenu__title {
    color: $menuActiveText !important;
  }
  // menu hover
  .submenu-title-noDropdown,
  .el-submenu__title {
    &:hover {
      background-color: $menuHover !important;
    }
  }
  & .nest-menu .el-submenu>.el-submenu__title,
  & .el-submenu .el-menu-item {
    background-color: $subMenuBg !important;
    color: $menuText;
    &:hover, &.is-active {
      background-color: $subMenuHover !important;
    }
    &.is-active{
      color: $subMenuActiveText;
    }
  }
}
// 左侧菜单收缩
.el-menu--vertical {
  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    color: $menuText;
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
    }
    &.is-active{
      color: $subMenuActiveText;
    }
    span {
      margin-left: 20px;
    }
  }
  .sidebar-favoricon {
    left: unset;
  }
}
.sidebar-favoricon{
  position: absolute;
  left: 44px;
  top: 50%;
  margin-top: -8px;
  display: none;
  &:hover, &:active{
    color: #fff;
  }
  &.is-active{
    color: #ffbe00;
    display: block;
  }
}

// 面包屑导航
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
  color: inherit;
}
.el-breadcrumb__inner.is-link{
  color: inherit;
}
.el-breadcrumb__item:last-child .el-breadcrumb__inner,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
  color: inherit;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}
.el-upload__input {
  display: none;
}
// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown {
  vertical-align: top;
}
.el-dropdown-menu {
  a {
    display: block;
  }
}

// loading-mask
.el-loading-mask{
  background-color: rgba(0,0,0,0.3);
}

// button
@mixin normalButton {
  .el-button [class*=el-icon-]+span{
    margin-left: 2px;
  }
  .el-button{
    color: #68a8d0;
    background-color: transparent;
    border-color: #68a8d0;
    font-weight: bold;
    vertical-align: middle;
    &:focus, &:hover{
      color: #4995c5;
      background-color: transparent;
      border-color: #4995c5;
    }
    &.is-active, &:active{
      color: #3990c7;
      background-color: transparent;
      border-color: #3990c7;
    }
    &.is-disabled, &.is-disabled:focus, &.is-disabled:hover{
      color: #888;
      background-color: transparent;
      border-color: #888;
      .svg-icon {
        cursor: not-allowed;
      }
    }
    &.editBtn{
      height: 30px;
      padding: 0 10px !important;
      float: right;
    }
  }
  .el-button+.el-button {
    margin-left: 0;
  }
  .el-button.hidden+.el-button:not(.hidden) {
    margin-left: 0;
  }
  div+.el-button, span+.el-button,
  .el-button:not([style*="display: none"])+.el-button,
  .button-container:not([style*="display: none"])+.el-button,
  .el-button:not([style*="display: none"])+.button-container {
    margin-left: 10px;
  }
  .el-button--mini{
    height: 30px;
  }
  .el-button--text{
    line-height: 19px;
    color: #68a8d0;
    background-color: transparent;
    border: 0;
    font-weight: 500;
  }
  .el-button--primary {
    color: #0d485a;
    background-color: #68a8d0;
    border-color: #68a8d0;
    &:focus, &:hover{
      color: #0d485a;
      background-color: #4995c5;
      border-color: #4995c5;
    }
    &.is-active, &:active{
      color: #0d485a;
      background-color: #3990c7;
      border-color: #3990c7;
    }
  }
}
@mixin dialogButton {
  .el-button{
    padding: 5px;
    margin-bottom: 5px;
  }
  .el-button+.el-button, div+.el-button, span+.el-button {
    margin-left: 2px;
  }
  .el-button--mini{
    padding: 7px 15px;
  }
  .el-upload .el-button{
    padding: 7px 13px;
  }
}
@mixin footerButton {
  .el-button{
    margin-left: 10px;
    padding: 7px 15px;
    color: #fff;
    background: linear-gradient(#d7d7d7, #6c6c6c);
    border-color: #d7d7d7;
    &:focus, &:hover{
      color: #fff;
      background: linear-gradient(#d7d7d7, #5c5c5c);
      border-color: #d7d7d7;
    }
    &.is-active, &:active{
      color: #fff;
      background: linear-gradient(#d7d7d7, #4c4c4c);
      border-color: #d7d7d7;
    }
    &.is-disabled, &.is-disabled:focus, &.is-disabled:hover{
      color: #a9a9a9;
      background: linear-gradient(#d7d7d7, #d7d7d7);
      border-color: #d7d7d7;
    }
  }
  .el-button--primary{
    color: #fff;
    background: linear-gradient(#7cc0ea, #4384ad);
    border-color: #7cc0ea;
    &:focus, &:hover{
      color: #fff;
      background: linear-gradient(#7cc0ea, #358cc2);
      border-color: #7cc0ea;
    }
    &.is-active, &:active{
      color: #fff;
      background: linear-gradient(#7cc0ea, #2b7aac);
      border-color: #7cc0ea;
    }
  }
}

// input
@mixin normalInput {
  .el-input{
    height: 30px;
    line-height: 30px;
    vertical-align: middle;
    .el-input__count .el-input__count-inner{
      background: transparent;
      line-height: normal;
    }
    &.is-disabled .el-input__inner{
      background-color: #161e24;
      border-color: #787b80;
      color: #838588;
    }
    // 限制字数是2位数时
    &.word-limit-2digits .el-input__inner{
      padding-right: 48px;
    }
    // 限制字数是3位数时
    &.word-limit-3digits .el-input__inner{
      padding-right: 61px;
    }
  }
  .el-input--small .el-input__inner{
    height: 30px;
    line-height: 30px;
  }
  .el-input__inner{
    height: 100%;
    line-height: inherit;
    background-color: transparent;
    border: $inputBd;
    color: $menuText;
    &:hover{
      border-color: #57d6dd;
    }
    &:focus{
      border-color: #50ecf3;
    }
  }
  .el-input .el-input__clear {
    color: #68a8d0;
    &:hover {
      color: #4995c5;
    }
  }
  input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
    font-size: 12px;
    color: #888 !important;
  }
  input::-moz-placeholder, textarea::-moz-placeholder {
    font-size: 12px;
    color: #888 !important;
  }
  input:-ms-input-placeholder, textarea:-ms-input-placeholder {
    font-size: 12px;
    color: #888 !important;
  }
  // 隐藏 输入框尾部验证值是否正确的图标
  .el-input__validateIcon {
    display: none;
  }

  .el-textarea .el-input__count{
    background: transparent;
  }
  .el-textarea__inner{
    background: transparent;
    overflow-wrap: break-word;
  }

  .el-input__icon{
    line-height: 30px;
  }
  .autocomplete{
    position: absolute;
    top: -100px;
    z-index: -99;
  }
  .el-switch__core{
    background: #bbb;
  }
  // 输入框后有一个按钮的样式
  .input-with-button {
    width: calc(100% - 46px);
    display: inline-block;
    &+.el-button {
      width: 42px;
      margin: 2px 0 0 !important;
      vertical-align: top;
    }
  }

  .el-input-group__append {
    .el-select {
      vertical-align: initial;
    }
    .el-select .el-input {
      height: 100%;
    }
    .el-select .el-input .el-input__inner {
      min-height: 30px;
      padding-bottom: 1px;
    }
    .el-input__inner, .el-input__inner:read-only {
      border: 0;
      border-radius: 0px 3px 3px 0;
    }
  }
}
@mixin dialogInput {
  .el-input .el-input__count .el-input__count-inner{
    background-color: #f5f5f5;
  }
  .el-input:hover .el-input__count{
    transition: all 0.3s;
    visibility: hidden;
  }
  .el-input__inner, .el-textarea__inner{
    background-color: #f5f5f5;
    border: 1px solid #aaa;
    color: #666;
    &:focus, &:hover{
      border: 1px solid #888;
    }
  }
  .el-input.is-disabled .el-input__inner,
  .el-input .el-input__inner:read-only{
    background-color: #e4e7e9;
    border-color: #aaa;
    color: #888;
  }
  .el-textarea.is-disabled .el-textarea__inner{
    background-color: #e4e7e9;
    border-color: #aaa;
    color: #888;
    &:hover{
      border-color: #aaa;
    }
  }
  .el-input.el-date-editor .el-input__inner:read-only{
    background-color: #f5f5f5;
    color: #666;
  }
  input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
    color: #bbb !important;
  }
  input::-moz-placeholder, textarea::-moz-placeholder {
    color: #bbb !important;
  }
  input:-ms-input-placeholder, textarea:-ms-input-placeholder {
    color: #bbb !important;
  }
  .el-switch__core{
    background: #ccc;
    border: 1px solid #ccc;
  }
  .el-input-group__append, .el-input-group__prepend {
    background-color: #e4e7e9;
    border: 1px solid #aaa;
    .el-select {
      vertical-align: initial;
    }
  }
  .el-input-group__append {
    border-left: 0;
    .el-input__inner, .el-input__inner:read-only {
      padding-bottom: 1px;
      border: 0;
      border-radius: 0px 3px 3px 0;
    }
  }
  .el-table {
    .el-input__inner, .el-textarea__inner{
      background-color: #fff;
    }
  }
}
@mixin popoverInput {
  .el-input .el-input__count .el-input__count-inner{
    background-color: #fff;
  }
  .el-input__inner, .el-textarea__inner{
    background-color: transparent;
  }
  .el-input-group__append, .el-input-group__prepend {
    background-color: transparent;
    .el-input.is-disabled .el-input__inner {
      background-color: #e8e8e8;
      color: #888;
    }
  }
  .el-input.el-date-editor .el-input__inner:read-only {
    background: #efefef;
  }
}

//el-input-number
@mixin normalInputNumber {
  .el-input-number {
    width: 100%;
    line-height: 32px;
  }
  .el-input-number__decrease, .el-input-number__increase{
    height:0px;
  }
  .el-input-number__increase{
    top: 2px;
  }
  .el-input-number.is-controls-right .el-input-number__decrease,
  .el-input-number.is-controls-right .el-input-number__increase{
    line-height: 14px;
  }
}
@mixin dialogInputNumber {
  .el-input-number.is-controls-right .el-input-number__decrease,
  .el-input-number.is-controls-right .el-input-number__increase{
    line-height: 12px;
  }
}

// select
@mixin normalSelect {
  .el-select{
    vertical-align: middle;
    .el-input{
      // height: auto;
      .el-input__inner{
        min-height: 30px;
      }
    }
    .el-input__suffix {
      height: auto;
      right: 0;
    }
  }
  .el-input--suffix .el-input__inner {
    padding-right: 20px;
  }
  .el-select__tags{
    max-height: 102px;
    overflow: auto;
    padding: 0;
    top: 1px;
    -webkit-transform: translateY(0);
    transform: translateY(0);
    &+.el-input {
      height: auto;
      max-height: 108px;
      .el-input__inner {
        vertical-align: top;
      }
    }
  }
}
@mixin dialogSelect {
  .el-select{
    width: 100%;
    border-radius: 4px;
    vertical-align: middle;
    .el-input {
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    .el-input.is-disabled .el-input__inner:hover{
      border-color: #aaa;
    }
    .el-input__inner:read-only {
      color: #666;
      background-color: transparent;
    }
    .el-input__inner:disabled {
      background-color: #e4e7e9;
    }
  }
  .el-select.input-with-button {
    width: calc(100% - 46px);
  }
  .el-table {
    .el-select{
      background-color: #fff;
      .el-input {
        background-color: #fff;
      }
    }
  }
}
@mixin popoverSelect {
  .el-select{
    background-color: transparent;
    .el-input {
      background-color: transparent;
    }
    .el-input__inner:disabled {
      background: #efefef;
    }
  }
}

// checkbox
@mixin normalCheckbox {
  .el-checkbox__input {
    height: 14px;
  }
  .el-checkbox__inner{
    background-color: transparent;
  }
  .el-checkbox__label{
    padding-left: 3px;
    color: #ccc;
    display: inline;
    overflow-wrap: break-word;
    word-wrap: break-word;
    white-space: initial;
  }
  .el-checkbox.ellipsis .el-checkbox__label {
    white-space: nowrap;
  }
  .el-checkbox.is-disabled{
    cursor: not-allowed;
  }
  .el-checkbox__input.is-disabled {
    cursor: not-allowed;
    .el-checkbox__inner{
      background-color: #f5f7fa;
      border: 1px solid #888;
      color: #888;
    }
    &.is-checked .el-checkbox__inner{
      background-color: #888;
      border-color: #888;
    }
    &+span.el-checkbox__label{
      color: #888;
    }
  }
  .el-checkbox {
    // 在多选框中的，数字输入框、select 对齐方式
    .el-input-number, .el-select {
      vertical-align: middle;
    }
  }

}
@mixin dialogCheckbox {
  .el-checkbox__label{
    color: #666;
  }
  .el-checkbox__inner{
    background-color: #fff;
    border: 1px solid #888;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #409EFF;
    border-color: #409EFF;
  }
  .el-checkbox__input.is-disabled .el-checkbox__inner {
    background-color: #f5f7fa;
    border-color: #bbb;
  }
  .el-checkbox__input.is-checked.is-disabled,
  .el-checkbox__input.is-indeterminate.is-disabled {
    .el-checkbox__inner {
      background-color: #888;
      border-color: #888;
    }
  }
}

// radio
@mixin normalRadio {
  .el-radio-group {
    display: block;
  }
  .el-radio{
    line-height: 30px;
    color: #ccc;
    overflow-wrap: break-word;
    &.is-disabled{
      cursor: not-allowed;
    }
  }
  .el-radio__inner{
    background-color: transparent;
  }
}
@mixin dialogRadio {
  .el-radio{
    color: #666;
  }
  .el-radio__inner{
    background-color: #f5f5f5;
    border: 1px solid #888;
  }
  .el-radio__input.is-disabled+span.el-radio__label{
    color: #888;
  }
  .el-radio__label {
    white-space: initial;
  }
  .el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner{
    border-color: #bbb;
  }
  .el-radio__input.is-disabled.is-checked .el-radio__inner::after{
    background-color: #888;
  }
}

// 在正常页面中的 table 样式
@mixin normalTable {
  .el-table{
    color: #ccc;
    background-color: $bodyBg;
    border: $tableBd;
    thead{
      color: #0d485a;
    }
    th.el-table__cell{
      background-color: #68a8d0;
      padding: 3px 0;
    }
    th:nth-last-child(2) .cell{
      padding-right: 16px;
    }
    td.el-table__cell{
      background-color: #03333d;
      padding: 3px 0;
    }
    td.el-table__cell, th.el-table__cell.is-leaf{
      border-bottom: $tableBd;
    }
    .cell{
      @include ellipsis;
    }
    .el-table-column--selection .cell {
      padding-left: 10px;
      padding-right: 10px;
    }
    p{
      margin: 0;
    }
    .el-checkbox__input.is-disabled .el-checkbox__inner{
      background-color: #47565f;
      &::after{
        border-color: #68a8d0;
      }
    }
    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after{
      border-color: #68a8d0;
    }
    .el-button--text{
      padding: 0;
    }
    .caret-wrapper{
      height: 22px;
      vertical-align: top;
    }
    .sort-caret.ascending{
      top: 0;
    }
    .ascending .sort-caret.ascending{
      border-bottom-color: #125781;
    }
    .sort-caret.descending{
      bottom: 0;
    }
    .descending .sort-caret.descending {
      border-top-color: #125781;
    }
    .el-table__fixed-right-patch{
      background-color: #68a8d0;
      border-color: #68a8d0;
    }
    &.el-table--border th.gutter:last-of-type{
      border-color: #68a8d0;
    }
    label {
      margin-bottom: 0;
    }
  }
  .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: #0b4855;
  }
  .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
    background-color: #127085;
  }
  .el-table--striped .el-table__body tr.el-table__row--striped.current-row td.el-table__cell,
  .el-table__body tr.current-row>td.el-table__cell,
  .el-table__body tr.hover-row.current-row>td.el-table__cell,
  .el-table__body tr.hover-row.el-table__row--striped.current-row>td.el-table__cell,
  .el-table__body tr.hover-row.el-table__row--striped>td.el-table__cell,
  .el-table__body tr.hover-row>td.el-table__cell {
    background-color: #127085;
  }
  .el-table--border.el-loading-parent--relative {
    border-color: #089ba2;
  }
  .el-table--border::after, .el-table--group::after, .el-table::before{
    background-color: $bodyBg;
  }
  .el-table--border th.el-table__cell,
  .el-table__fixed-right-patch{
    border-bottom: $tableBd;
    border-color: #aaa;
  }
  .el-table--border td.el-table__cell,
  .el-table--border th.el-table__cell,
  .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed{
    border-right: $tableBd;
  }
  .el-loading-mask{
    background-color: rgba(0,0,0,0.7);
  }
  .el-table__fixed-body-wrapper{
    top: 28px;
  }
  .el-table__fixed-right::before, .el-table__fixed::before{
    height: 0px;
    background-color: transparent;
  }
  .el-table__fixed, .el-table__fixed-right{
    -webkit-box-shadow: 0 0 10px rgba(255,255,255,.12);
    box-shadow: 0 0 10px rgba(255,255,255,.12);
  }
  // 处理gridTable 因为滚动条和固定列产生的行数据对不齐的问题
  .el-table.el-table--scrollable-x {
    .el-table__fixed, .el-table__fixed-right {
      height: calc(100% - 10px) !important;
      .el-table__fixed-body-wrapper{
        height: calc(100% - 31px) !important;
      }
    }
  }
  // IE浏览器处理gridTable 因为滚动条和固定列产生的行数据对不齐的问题
  @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    /* IE10-specific styles go here */
    .el-table.el-table--scrollable-x {
      .el-table__fixed, .el-table__fixed-right {
        height: calc(100% - 20px) !important;
      }
    }
  }
  // 火狐浏览器处理gridTable 因为滚动条和固定列产生的行数据对不齐的问题
  @-moz-document url-prefix(){
    .el-table.el-table--scrollable-x {
      .el-table__fixed, .el-table__fixed-right {
        height: calc(100% - 16px) !important;
      }
    }
  }
  .el-table th.el-table__cell.is-sortable>.cell {
    padding-right: 0;
  }
  .is-sortable {
    span {
      line-height: 16px;
      width: calc(100% - 30px);
      display: inline-block;
      vertical-align: text-bottom;
      @include ellipsis;
    }
  }
}
@mixin dialogTable {
  .tableBox{
    height: 100%;
  }
  .el-table{
    color: #666;
    background-color: transparent;
    border: 1px solid #aaa;
    thead{
      color: #666;
    }
    th{
      background-color: #e4e7e9;
      padding: 3px 0;
    }
    td{
      background-color: #ddd;
      padding: 3px 0;
    }
    td, th.is-leaf{
      border-bottom: 1px solid #aaa;
    }
    .cell{
      @include ellipsis;
    }
    p{
      margin: 0;
    }
    .el-checkbox__input.is-disabled .el-checkbox__inner{
      background-color: #e4e7e9;
      border: 1px solid #aaa;
      color: #888;
    }
    .el-button--text{
      padding: 0;
    }
    .caret-wrapper{
      height: 22px;
      vertical-align: top;
    }
    .sort-caret.ascending{
      top: 0;
    }
    .ascending .sort-caret.ascending{
      border-bottom-color: #666;
    }
    .sort-caret.descending{
      bottom: 0;
    }
    .descending .sort-caret.descending {
      border-top-color: #666;
    }
    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after{
      border-color: #aaa;
    }
  }
  .el-table__fixed, .el-table__fixed-right {
    -webkit-box-shadow: 0 0 10px rgba(0,0,0,0.12);
    box-shadow: 0 0 10px rgba(0,0,0,0.12);
  }
  .el-table__fixed-right-patch {
    background-color: #e4e7e9;
    border-color: #aaa;
  }
  .el-table--striped .el-table__body tr.el-table__row--striped td {
    background: #e4e7e9;
  }
  .el-table--enable-row-hover .el-table__body tr:hover>td{
    background-color: #ccc;
  }
  .el-table--striped .el-table__body tr.el-table__row--striped.current-row td, .el-table__body tr.current-row>td, .el-table__body tr.hover-row.current-row>td, .el-table__body tr.hover-row.el-table__row--striped.current-row>td, .el-table__body tr.hover-row.el-table__row--striped>td, .el-table__body tr.hover-row>td {
    background-color: #ccc;
  }
  .el-table--border::after, .el-table--group::after, .el-table::before{
    background-color: #e4e7e9;
  }
  .el-table--border td, .el-table--border th, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed{
    border-right: 1px solid #aaa;
  }
  .el-loading-mask{
    background-color: rgba(0,0,0,0.7);
  }
  
  // 处理gridTable 因为滚动条和固定列产生的行数据对不齐的问题
  .el-table.el-table--scrollable-x {
    .el-table__fixed, .el-table__fixed-right {
      height: calc(100% - 10px) !important;
      .el-table__fixed-body-wrapper{
        height: calc(100% - 36px) !important;
      }
    }
  }
}
@mixin popoverTable {
  .el-table{
    th{
      background-color: #eaeaea;
    }
    td{
      background-color: #fff;
    }
    .el-checkbox__input.is-disabled .el-checkbox__inner{
      background-color: #eaeaea;
      border: 1px solid #aaa;
      color: #888;
    }
  }
  .el-table--striped .el-table__body tr.el-table__row--striped td {
    background: #f5f5f5;
  }
  .el-table--enable-row-hover .el-table__body tr:hover>td{
    background-color: #ccc;
  }
}

// pagination
@mixin normalPagination {
  .pagination-container {
    background: $bodyBg;
    padding: 8px 16px 0;
    .el-pagination__total {
      color: #cccccc;
    }
    .el-pagination__jump {
      color: #cccccc;
    }
    &.hidden {
      display: none;
    }
  }
  .el-pagination .el-select {
    &:hover .el-input__inner {
      border-color: #57d6dd;
    }
    .el-input.is-focus .el-input__inner{
      border-color: #57d6dd;
    }
    .el-input .el-input__inner{
      &:hover, &:focus{
        border-color: #57d6dd;
      }
    }
    .el-input.is-disabled .el-input__inner:hover {
      border-color: #787b80;
    }
  }
  .el-pagination__total, .el-pagination__jump{
    color: #ccc;
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active{
    background: #68a8d0;
  }
  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li{
    background: $bodyBg;
    color: #ccc;
  }
  .el-pagination.is-background .btn-next.disabled,
  .el-pagination.is-background .btn-next:disabled,
  .el-pagination.is-background .btn-prev.disabled,
  .el-pagination.is-background .btn-prev:disabled,
  .el-pagination.is-background .el-pager li.disabled{
    color: #666;
  }
}
@mixin dialogPagination {
  .pagination-container {
    background: #e4e7e9;
    padding: 8px 0 0;
    .el-pagination__total{
      color: #666;
    }
    .el-pagination__jump{
      margin-left: 10px;
      color: #666;
    }
    .el-input .el-input__inner {
      display: block;
    }
    .el-pagination .el-select .el-input{
      width: 90px;
    }
    .el-pagination .el-select .el-input .el-input__inner{
      padding-left: 0;
      background: #fff;
    }
    .el-pagination--small .el-select .el-input{
      height: 22px;
    }
    .el-pagination--small .el-select .el-input .el-input__inner{
      min-height: 22px;
      height: 22px;
    }
    .el-pagination--small .el-input--mini .el-input__icon{
      line-height: 22px;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background: #bababa;
    }
    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
      background: #e4e7e9;
      color: #666;
    }
    .el-pagination.is-background .btn-next.disabled,
    .el-pagination.is-background .btn-next:disabled,
    .el-pagination.is-background .btn-prev.disabled,
    .el-pagination.is-background .btn-prev:disabled,
    .el-pagination.is-background .el-pager li.disabled{
      color: #ccc;
    }
  }
}

// tree
@mixin normalTree {
  .el-tree{
    background: transparent;
    color: $menuText;
    .el-tree-node__content:hover{
      background-color: #1b384c;
    }
    .el-tree-node:focus>.el-tree-node__content{
      background-color: transparent;
      &:hover{
        background-color: #1b384c;
      }
    }
    &.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
      background-color: #1b384c;
    }
  }
}
@mixin dialogTree {
  .el-tree{
    background: transparent;
    color: #666;
    .el-tree-node__content:hover{
      background-color: #ccc;
    }
    .el-tree-node:focus>.el-tree-node__content{
      background-color: transparent;
      &:hover{
        background-color: #ccc;
      }
    }
    &.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
      background-color: #ccc;
    }
    .el-tree-node__expand-icon{
      color: #666;
    }
    .el-tree-node__expand-icon.is-leaf{
      color: transparent;
    }
  }
}

// form
@mixin normalForm {
  .el-form-item__label {
    color: inherit;
  }
  .el-form--label-left .el-form-item__label {
    padding-left: 5px;
  }
  .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before,
  .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
    margin-right: 0;
    position: relative;
    left: -2px;
  }
  .el-form-item.content-flex .el-form-item__content {
    display: flex;
    div+.el-button {
      margin: 0 0 0 3px !important;
    }
  }
}
@mixin dialogForm {
  .el-form{
    margin: 0 auto;
    .el-form-item{
      margin-bottom: 5px;
    }
    .el-form-item__label, label:not(.el-checkbox){
      line-height: 30px;
      color: #666;
    }
    .el-form-item__label{
      padding: 0 4px 0 2px;
    }
    .el-form-item__content{
      line-height: 30px;
      color: #666;
      .el-input__inner{
        line-height: 30px;
      }
      .el-form-item__error{
        padding-top: 2px;
        position: relative;
        overflow-wrap: break-word;
      }
    }
  }
}

// card
@mixin normalCard {
  .el-card{
    background-color: transparent;
    color: inherit;
    border-color: #676767;
  }
  .el-card__header{
    padding: 10px 20px;
    border-bottom: 1px solid #878787;
  }
}
@mixin dialogCard {
  .el-card{
    margin-bottom: 10px;
    color: #666;
    border-color: #EBEEF5;
    &.is-always-shadow, &.is-hover-shadow:focus, &.is-hover-shadow:hover{
      -webkit-box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.2);
      box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.2);
    }
  }
  .el-card__header{
    padding: 5px 20px;
    background-color: #e4e7e9;
    border-bottom: 1px solid #ccc;
    font-weight: bold;
  }
}

// tabs
@mixin normalTabs {
  .el-tabs {
    height: 100%;
    border: 1px solid #666;
    .el-tab-pane>.tree-menu{
      border: 0 !important;
      .svg-icon{
        font-size: 14px;
      }
}
}
.el-checkbox__input.is-disabled .el-checkbox__inner{
  background-color: transparent;
  border: 1px solid #888;
  color: #888;
    }
.el-checkbox__input.is-disabled .el-checkbox__inner{
  background-color: transparent;
  border: 1px solid #888;
  color: #888;
  }
  .el-tabs__header{
    margin: 0 0 5px;
  }
  .el-tabs__content{
    height: calc( 100% - 45px);
  }
  .el-tab-pane{
    height: 100%;
  }
  .el-tabs--card{
    &>.el-tabs__header{
      border-bottom: 1px solid #aaa;
      .el-tabs__nav{
        border: none;
        border-right: 1px solid #aaa;
      }
      .el-tabs__item{
        border-left: 1px solid #aaa;
        &.is-active{
          border-bottom: 1px solid #409EFF;
        }
      }
}
}
.el-radio-group {
  display: block;
    }
.el-radio-group {
  display: block;
  }
  .el-tabs__item{
    color: #ccc;
  }
  .el-tabs__item.is-disabled {
    cursor: not-allowed;
  }
  .el-tabs__nav-wrap::after{
    color: #888;
  }
  .el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
  .el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--top .el-tabs__item.is-top:nth-child(2) {
    padding-left: 15px;
  }
}
@mixin dialogTabs {
  .el-tabs.el-tabs--top{
    border: 1px solid #aaa;
    overflow: hidden;
  }
  .el-tabs__item{
    color: #666;
    &.is-active {
      color: #409EFF;
    }
    &.is-disabled{
      color: #8a8b8d;
    }
  }
  .el-tabs__nav-wrap::after{
    background-color: #888;
  }
  .el-tabs__content{
    height: calc(100% - 45px);
    padding: 0 7px;
  }
}

@include normalButton;
@include normalInput;
@include normalInputNumber;
@include normalSelect;
@include normalCheckbox;
@include normalRadio;

@include normalTable;
@include normalPagination;
@include normalTree;
@include normalForm;
@include normalCard;
@include normalTabs;

// dialog、data-editor, popover, drawer 中 其他组件的样式
.el-dialog__body, .data-editor, .el-popover, .el-drawer__body {
  @include dialogButton;
  @include dialogInput;
  @include dialogInputNumber;
  @include dialogSelect;
  @include dialogCheckbox;
  @include dialogRadio;

  @include dialogTable;
  @include dialogPagination;
  @include dialogTree;
  @include dialogForm;
  @include dialogCard;
  @include dialogTabs;
}
.data-editor, .el-popover, .el-drawer__body {
  @include popoverInput;
  @include popoverSelect;
  @include popoverTable;
  font-size: 14px;
  color: #666;
  .os-tabs {
    height: unset;
  }
  .el-divider__text.is-left {
    font-weight: 800;
  }
}

.el-drawer__footer {
  @include footerButton;
}
// popover
.el-popover{
  text-align: left;
  overflow-wrap: break-word;
  .el-input{
    background-color: transparent;
    &.is-disabled .el-input__inner{
      background-color: #eee;
    }
  }
  .el-input__inner, .el-textarea__inner{
    background-color: transparent;
  }
}
.el-popover__title {
  font-weight: bold;
}

// icon
.el-icon-my-checkbox{
  background: url("~@/assets/icon/checkbox.png") center no-repeat;
  background-size: 16px 16px;
}
.el-icon-my-checkbox:hover{
  background: url("~@/assets/icon/checkbox-hover.png") center no-repeat;
  background-size: 16px 16px;
}
.el-icon-my-checkbox-disabled {
  background: #47565f url("~@/assets/icon/checkbox-gray.png") center no-repeat;
  background-size: 16px 16px;
  border-radius: 2px;
}
.el-icon-my-checked{
  background: #fff url("~@/assets/icon/checkbox-checked.png") center no-repeat;
  background-size: 18px 18px;
  border-radius: 2px;
}
.el-icon-my-checked-green{
  background: #fff url("~@/assets/icon/checkbox-green.png") center no-repeat;
  background-size: 18px 18px;
  border-radius: 2px;
}
.el-icon-my-checked-disabled {
  background: #fff url("~@/assets/icon/checkbox-checked-disabled.png") center no-repeat;
  background-size: 18px 18px;
  border-radius: 2px;
}
[class*=el-icon-my-]::before{
  content: "\8d3a";
  font-size: 14px;
  visibility: hidden;
}

// alert
.el-alert--info{
  background-color: transparent;
  border: $greyBd;
}

// cascader
.el-cascader {
  vertical-align: middle;
}
.el-cascader__tags{
  top: 5px;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

// date-picker
.el-date-range-picker .el-input.is-disabled .el-input__inner{
  background-color: #F5F7FA;
  border-color: #E4E7ED;
  color: #C0C4CC;
}
.el-date-editor.el-input,
.el-range-editor.el-input__inner{
  width: 150px;
  height: 30px;
  vertical-align: middle;
}
.el-date-editor .el-range__icon{
  line-height: 22px;
}
.el-date-editor .el-range-input {
  background: transparent;
  color: inherit;
}
.el-date-editor .el-range-separator{
  width: 10%;
  line-height: 22px;
  margin: 0 5px;
  padding: 0;
  color: inherit;
}
.el-date-editor .el-range__close-icon{
  line-height: 22px;
}
.el-picker-panel.no-atTheMoment {
  .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}

  // icon
  .text-button-icon {
    color: #68a8d0;
    cursor: pointer;
  }

//tree-menu
.custom-tree-node {
  // flex: 1;
  display: flex;
  align-items: center;
  // justify-content: flex-start;
  font-size: 14px;
  padding-right: 10px;
  color: #4386c6;
  .svg-icon{
    margin-right: 2px;
  }
}
// tree-select
.tree-select-popover{
  background: #fff;
  border: 1px solid #666;
  padding: 12px 11px;
  .el-scrollbar__wrap {
    overflow-x: hidden !important;
  }
  .el-scrollbar{
    height: 100%;
  }
  .input-container{
    margin-bottom: 5px;
  }
  .input-container+.el-scrollbar{
    height: calc( 100% - 30px );
  }
  .el-tree{
    color: #666;
  }
  .el-tree-node__content:hover {
    background-color: #ccc;
  }
  .el-tree-node:focus>.el-tree-node__content{
    background-color: #ccc;
  }
  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
    background-color: #ccc;
  }
  .el-tree-node__expand-icon{
    color: #666;
  }
  .el-tree-node__expand-icon.is-leaf{
    color: transparent;
  }
  .el-checkbox__inner{
    border-color: #888;
  }
  &[x-placement^=bottom] .popper__arrow{
    border-bottom-color: #666;
  }
  &[x-placement^=top] .popper__arrow{
    border-top-color: #666;
  }
}

// 自定义节点icon样式
.el-ic {
  visibility: hidden;
  margin-left: 10px;
  span {
    padding: 0 14px;
    font-size: 18px;
    font-weight: 600;
  }

  .svg-icon {
    color: #4386c6;
    margin-right: 10px;
  }
  .addRule{
    margin-left: 5px;
  }
}
.tree-select-popover .el-ic span {
  font-size: 13px;
}
.el-tree-node__content:hover .el-ic {
  color: #428bca !important;
  visibility: visible;
}

// dialog
.el-dialog__wrapper{
  position: absolute;
  background: rgba(0,0,0,0.5);
}
.el-tabs__content .el-dialog__wrapper{
  top: 0;
}
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  background: #e4e7e9;
  border-radius: 4px;
  overflow: hidden;
}
.el-dialog__header{
  padding: 7px 48px 6px 20px;
  background: linear-gradient(#dbdcdd, #8fa1b2);
  border-radius: 4px 4px 0 0;
  border-bottom: 1px solid #616f78;
}
.el-dialog__title{
  line-height: 20px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
  display: inherit;
  user-select: none;
  @include ellipsis
}
.el-dialog__headerbtn{
  top: 8px;
  .el-dialog__close{
    color: #333;
  }
}
.el-dialog__body {
  max-height: 500px;
  padding: 20px 19px 15px;
  color: #666;
  overflow: auto;
  overflow-wrap: break-word;

  .toolbar .el-input {
    margin-bottom: 5px;
  }
  .tree-menu{
    border: 1px solid #aaa !important;
    .el-input__inner{
      border: 1px solid #aaa !important;
      &:focus, &:hover{
        border: 1px solid #888 !important;
      }
    }
    .el-tree-node__loading-icon{
      color: #0082ff;
    }
  }

  .el-tag.el-tag--info{
    border-color: #b6b6b6;
  }

  // tree-transfer
  .transfer-left,
  .transfer-right {
    border: 1px solid #aaa !important;
  }
  .transfer-title {
    border-bottom: 1px solid #aaa !important;
    height: 36px !important;
    line-height: 36px !important;
    background-color: #c5c5c5 !important;
  }
  .address-list-li:hover {
    background-color: #c5c5c5 !important;
  }
  .address-list-del {
    background-color: #fef0f0 !important;
  }
  .el-divider.el-divider--horizontal{
    margin: 25px 0 15px;
    background-color: #adaeaf;
  }
  .el-form>.el-divider.el-divider--horizontal:first-child{
    margin-top: 10px;
  }
  .el-divider__text.is-left {
    font-weight: 800;
    color: #303133;
    background-color: #e4e7e9;
  }
}
.el-dialog__footer{
  padding: 12px 20px;
  border-top: 1px solid #ccc;
  box-shadow: 0px 2px 6px 0px inset #ccc;
  @include footerButton;
}
.el-dialog.is-fullscreen {
  height: 100% !important;
  top: 0 !important;
  left: 0 !important;
  .el-form {
    margin: 0;
  }
  .el-dialog__body {
    max-height: none;
    height: calc(100% - 90px);
    >.el-tabs {
      max-height: calc(100% - 150px);
    }
  }
}

// Message
.el-message .el-message__content{
  min-height: 15px;
  max-height: 128px;
  overflow: auto;
  margin-right: 20px;
  line-height: 18px;
}

// messageBox
.el-message-box{
  width: 400px;
  background: #e4e7e9;
  border: 0;
}
.el-message-box__header{
  padding: 7px 20px 6px;
  background: linear-gradient(#dbdcdd, #8fa1b2);
  border-radius: 4px 4px 0 0;
  border-bottom: 1px solid #616f78;
}
.el-message-box__title{
  line-height: 20px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}
.el-message-box__headerbtn{
  top: 8px;
  .el-message-box__close{
    color: #333;
  }
}
.el-message-box__content {
  color: #666;
  font-size: 14px;
  .el-input__inner, .el-textarea__inner{
    background-color: #f5f5f5;
    border: 1px solid #aaa;
    color: #666;
    &:focus, &:hover{
      border: 1px solid #888;
    }
  }
}
.el-message-box__message {
  max-height: 140px;
  overflow: auto;
}
.el-message-box__message p {
  word-wrap: break-word;
}
.el-message-box__btns{
  padding: 20px 20px 0;
  @include footerButton;
  .btn-custom-cancel{
    float: right;
    margin-left: 10px;
  }
}
.el-message-box__status {
  top: 12px;
}


// todo 自定义 input-tooltip{
.input-tooltip{
  max-width: 400px;
  line-height: 28px;
  padding: 0 10px;
  margin-top: -40px;
  position: fixed;

  color: #F56C6C;
  background: #fef0f0;
  border: 1px solid #F56C6C;
  border-radius: 5px;
  text-align: left;
  z-index: 111;
}
.input-tooltip::before{
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  /* 箭头靠右边 */
  // top: 13px;
  // right: -15px;
  // border-top: 10px solid transparent;
  // border-bottom: 10px solid transparent;
  // border-left: 15px solid #F56C6C;
  /* 箭头靠下边 */
  left: 20px;
  bottom: -10px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 10px solid #F56C6C;
  /* 箭头靠左边 */
  /* top: 13px;
  left: -15px;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 15px solid #F56C6C; */
  /* 箭头靠下边 */
  /* left: 20px;
  top: -15px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 15px solid #F56C6C; */
}
.input-tooltip::after{
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  /* 箭头靠右边 */
  // top: 13px;
  // right: -13px;
  // border-top: 10px solid transparent;
  // border-bottom: 10px solid transparent;
  // border-left: 15px solid #fef0f0;
  /* 箭头靠下边 */
  left: 20px;
  bottom: -8px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 10px solid #fef0f0;
  /* 箭头靠左边 */
  /* top: 13px;
  left: -13px;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 15px solid #fef0f0; */
  /* 箭头靠下边 */
  /* left: 20px;
  top: -13px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 15px solid #fef0f0; */
}

.el-tooltip__popper{
  max-width: 700px;
  font-weight: normal;
}
.el-tooltip__popper.is-light {
  color: #000;
}

// descriptions 描述列表
.el-descriptions {
  margin-bottom: 10px;
  color: inherit;
}
.el-descriptions .is-bordered .el-descriptions-item__cell {
  border-color: #ababab;
}
.el-descriptions__body {
  color: inherit;
  background-color: inherit;
}
.el-descriptions-item__label.is-bordered-label {
  background-color: #ddd;
}
.el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    color: inherit;
  }
  .el-descriptions-item__label {
    font-weight: bold;
  }
}
.el-descriptions-item__label {
  min-width: 110px;
}
.el-descriptions-item__content {
  min-width: 110px;
  max-width: 220px;
}
.el-descriptions-item__cell .el-button {
  max-width: 100%;
  @include ellipsis
}

// 规则集- 检测规则、响应规则
.select-box{
  width: 100%;
  border: 1px solid #aaa;
  border-radius: 4px;
  background: #f5f5f5;
  min-height: 32px;
  max-height: 120px;
  overflow: auto;
  .el-link{
    top: -2px;
  }
  .el-link.el-link--default:hover{
    color: #666;
  }
}
.select-box>.select-button{
  height: 28px;
  float: right;
  margin-right: 2px;
  position: sticky;
  top: 0;
  background: inherit;
  z-index: 1;
}
.select-box>.select-tag{
  float: left;
  height: 24px;
  line-height: 24px;
  margin: 2px 5px 2px 1px;
  cursor: default;
}
.select-tag a:hover,
.select-tag a:active{
  text-decoration: none;
}
.choose-btn{
  height: 24px;
  margin: 0 !important;
  vertical-align: unset;
}

// 点阵水印预览
.watermark-preview .dot-matrix-image{
  width: 120px;
  height: 120px;
  margin: 3px 0 0 3px;
  .dot-group{
    width: 30%;
    height: 30%;
    margin: 0 3% 3% 0;
    float: left;
    box-sizing: border-box;
    position: relative;
    .dot-box{
      width: 33%;
      height: 33%;
      float: left;
      box-sizing: border-box;
      position: relative;
      .dot{
        position: absolute;
        left: 50%;
        top: 50%;
        visibility: hidden;
      }
      .visible{
        visibility: visible;
      }
    }
  }
}
.el-main {
  padding: 0px 0px 0px 5px;
  label{
    margin-bottom: 5px;
    display: inline-block;
  }
  .preview-container{
    height: calc(100% - 24px);
    border: 1px solid #888;
    overflow: hidden;
    position: relative;
  }
  .pic-container{
    height: 100%;
    color: #000;
    position: relative;
    overflow: hidden;
  }
  .watermark-preview{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }
  .mask-div{
    position: absolute;
  }
  .c1 {
    top: 0;
    left: 0
  }
  .c2 {
    top: 0;
    left: 50%;
    transform: translateX(-50%)
  }
  .c3 {
    top: 0;
    right: 0
  }
  .c4 {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  .c5 {
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
  }
  .c6 {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }
  .c7 {
    bottom: 0;
    left: 0
  }
  .c8 {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }
  .c9 {
    bottom: 0;
    right: 0
  }
}

// find password
.page-container{
  .topbar {
    padding-left: 10px;
    .topbar-logo {
      height: 24px;
      vertical-align: middle;
    }
    .topbar-title {
      color: #fff;
      display: inline-block;
      margin: 0;
      font-weight: 600;
      line-height: 50px;
      font-size: 16px;
      font-family: Helvetica Neue, Helvetica, Arial, Microsoft Yahei, Hiragino Sans GB, Heiti SC, WenQuanYi Micro Hei, sans-serif;
      vertical-align: middle;
    }
  }
  .fp-container {
    height: calc(100% - 50px);
    margin-top: 50px;
  }
  .fp-form {
    >>>.el-form-item__label, >>>.el-form-item__content{
      color: #eeeeee;
    }
    .btn-container {
      button{
        background: #295576;
        color: #fff;
      }
      button.is-disabled{
        background: grey;
      }
    }
  }
}

.app-container>.el-form,
.app-container>div>.el-form,
.app-main>.el-form {
  .el-form-item__label{
    color: #eee;
  }
  .el-switch__label{
    color:#ccc;
  }
  .el-switch__label.is-active{
    color:#409EFF;
  }
  .el-divider__text {
    color: #eee;
    background: $bodyBg;
  }
}
.serverInfo .server-form .el-divider__text {
  color: #eee;
  background: $bodyBg;
}
.global-config {
  padding: 10px 30px;
  .el-form-item__label{
    color: #eee;
  }
  .el-divider__text {
    color: #eee;
    background: $bodyBg;
  }
}
.config-all {
  border: 1px solid $subMenuHover;
  border-right: 0;
  background: linear-gradient(135deg, $subMenuBg 0%, $menuBg 100%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.config-all:hover {
  background: linear-gradient(135deg, $subMenuHover 0%, $menuHover 100%);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.config-all .title {
  color: #eee;
}
.config-all .description {
  color: #aaa;
}

// 报表日期选择框 选中√的位置
.el-cascader-node__prefix{
  top: 10px;
}
// 策略生效对象选择框
.targetObject{
  padding-left: 5px;
}
// notify
.el-notification__content{
  text-align: left;
  p{
    max-height: 200px;
    overflow: auto;
    overflow-wrap: break-word;
  }
}

// 服务器状态
.legend {
  border: 1px solid #Fff;
}
.serverInfo {
  .el-collapse-item__header {
    background: transparent;
    color: #fff;
  }
  .el-collapse-item__wrap{
    background-color: transparent;
  }
}

// 资源管理器布局
.layout-box {
  color: #ccc;
  background-color: #0c161f;
  border: 1px solid #089ba2;
  height: 100%;
  padding: 10px;
  overflow: auto;
  cursor: default;
  user-select: none;
  .highlight-border {
    border: 1px solid #089ba2;
  }
  .highlight-item {
    background: #127085;
    &:hover {
      background-color: #127085;
      border: 1px solid #089ba2;
    }
  }
}
.layout-item {
  border: 1px solid transparent;
  &:hover {
    background-color: #03333d;
    border: 1px solid #03333d;
  }
}

// 通讯工具聊天记录
.msg-box {
  border: 1px solid #666;
  &>div {
    border: 1px solid #333;
  }
}
.rounded-rectangle {
  border: 1px solid #272a2f;
  background-color: #202328; /* 设置矩形的背景颜色 */
}
.thediv-span {
  color: #68a8d0;
  border: 1px solid #272a2f;
  border-bottom: none;
  background-color: rgba(74, 74, 74, 1);
}

// 下载列表弹窗
.download-popover {
  top: 32px !important;
}

// 控制台web服务器配置
.addr-autocomplete li {
  line-height: 17px;
  padding: 5px 10px;
  .addr-ip {
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .addr-name {
    font-size: 12px;
    color: #b4b4b4;
  }
  .highlighted .addr-name {
    color: #ddd;
  }
}

// 控制台敏感内容管控报表配置
.sensitiveness-report {

  // 复选框样式修改
  .el-checkbox__input{
    height: 15px;
  }
  .sensitiveness-query{
    .el-breadcrumb {
      margin-left: 20px;
    }
  }
  // 折叠面板样式修改
  .el-collapse-item__header{
    background: transparent;
    color: #008acd;
    border-bottom: 1px solid transparent;
  }
  .el-collapse-item__wrap{
    background: transparent;
  }
  .el-collapse{
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
  }
  .el-collapse-item__wrap{
    border-bottom: 1px solid transparent;
  }
  // 时间线样式
  .el-timeline-item__content{
    color: #eeeeee;
  }
  // 敏感报表上方面包屑
  .el-breadcrumb{
    line-height: 40px;
    padding-left: 10px;
  }
  .el-breadcrumb__inner a{
    color: #68a8d0 ;
  }
  .detailsDiv{
    height: calc(100% - 30px);
    overflow-x: hidden;
    overflow-y: auto;
    /*border: 1px solid #666666;*/
  }
  .left-data-panel {
    width: 100%;
    height: 265px;
    padding: 0 20px 0px 20px;
    display: flex;
    flex-direction: column;
  }
  .left-data-item {
    flex: 1;
    border: 1px solid #3b749c;
    display: inline-block;
    margin-left: 1%;
    display: flex;
    label{
      margin-inline: 15px;
      align-self: center;
      font-weight: normal;
      flex: 2;
      text-align: right;
      text-align: justify;
      text-align-last: justify;
    }
    span{
      flex: 1;
      text-align: left;
      text-align: center;
      line-height: 30px;
      align-self: center;
      >>>.el-button--text{
        text-decoration: underline;
      }
    }
  }

  /*小方格样式*/
  .data-panel {
    width: 100%;
    min-width: 0;
    padding: 5px 10px 15px;
    display: flex;
    gap: 20px;
  }
  .data-item {
    min-width: 0;
    height: 70px;
    line-height: 70px;
    flex: 1;
    display: flex;
    justify-content: space-evenly;
    background: #1a2835;
    border-radius: 10px;
    box-shadow: 1px 1px 3px rgba(255,255,255,0.3);
    transition: all .5s;
    .icon {
      font-size: 35px;
    }
    .content{
      line-height: 20px;
      font-weight: normal;
      text-align: center;
      display: flex;
      align-items: center;
    }
    .el-button--text{
      line-height: 50px;
      width: 150px;
      padding: 0 0 0 30px;
      margin-bottom: 10px;
      font-size: 30px;
      text-align: left;
      text-decoration: underline;
    }
  }
  .data-item:hover {
    box-shadow: 0px 0px 10px rgba(255,255,255,0.7);
  }
  // 分析详情里面的小方块不需要背景颜色和悬停效果
  .data-item-details {
    background-color: transparent;
    box-shadow: 0px 0px 0px;
    border: 1px solid #089ba2;
    .content{
      label{
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 200px;
      }
      span{
        padding: 0 0 0 5px;
        font-size: 30px;
        color: #68a8d0;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 200px;
        line-height: 35px;
      }
    }
  }
  .data-item-details:hover {
    box-shadow: 0px 0px 0px;
  }
  @media screen and (max-width: 1500px){
    .content {
      flex-direction: column;
      justify-content: space-evenly;
      .el-button--text{
        line-height: 40px;
        margin: -15px 0 0;
        padding: 0;
        text-align: center;
      }
    }
  }

  /* 图表 */
  .chart-panel {
    width: 100%;
    min-width: 0;
    padding: 5px 10px 15px;
    display: flex;
    gap: 20px;
  }
  .chart-item {
    height: 318px;
    position: relative;
    display: flex;
    flex-direction: column;
    background: #1a2835;
    border-radius: 10px;
    box-shadow: 1px 1px 3px rgba(255,255,255,0.3);
    transition: all .5s;
    &:first-child {
      margin-left: 0;
    }
  }
  .chart-item:hover {
    box-shadow: 0px 0px 10px rgba(255,255,255,0.7);
  }
  .chart-item.flex-2+.flex-1 {
    width: calc((100% - 40px) / 3);
    flex: none;
  }
  .chart-item.col-1 {
    width: calc((100% - 40px) / 3);
    flex: none;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-2 {
    flex: 2;
  }
  .flex-3 {
    flex: 3;
  }

  .mini-title{
    height: 30px;
    line-height: 30px;
    padding-left: 20px;
    margin-bottom: 10px;
  }
  .mini-title>span{
    font-weight: bold;
    color: #68a8d0;
    cursor: pointer;
  }
  .big-svg{
    margin-left: 10px;
    color: #68a8d0;
    cursor: pointer;
  }
  .noData{
    width: 120px;
    height: 30px;
    text-align: center;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    font-size: 14px;
  }

  // 详情页面
  .data-details{
    width: 100%;
    height: 100%;
    padding-left: 10px;
    display: flex;
    min-width: 0;
    gap: 20px;
  }
  .data-analysis-table{
    padding-top: 5px;
    flex: 1;
    min-width: 0;
  }
  .data-analysis-details{
    flex: 2;
    min-width: 0;
    .el-tabs__content {
      padding: 0 5px 5px;
    }
  }
  /*解决表格宽度随浏览器大小变化自适应问题*/
  .table-parent-size{
    position: relative;
  }
  .table-left-size{
    width: 100%;
    height: calc(100% - 37px);
    position: absolute;
  }
  .table-right-size{
    width: 100%;
    position: absolute;
  }
  .el-pagination{
    overflow-x: auto;
    overflow-y: hidden;
  }
  .search-icon-left{
    position: absolute;
    right: -24px;
    top: 0px;
    cursor: pointer;
    padding: 4px;
  }
  .search-icon-right{
    position: absolute;
    right: 15px;
    top: 5px;
    cursor: pointer;
    padding: 4px;
    z-index: 1;
  }
  /*图表解读*/
  .panel-wrapper{
    border: 0;
  }
}
.sensitive-control-echartDlg{
  height: 90%;
}
/*图表解读*/
.panel-wrapper{
  height: 100%;
  border: 1px solid #3b749c;
}
.unscramble{
  min-height: 100px;
  padding:0 10px 5px 10px;
  font-size: 14px;
  display: flex;
  flex-direction: column;
}
.unscramble-title{
  line-height: 25px;
  color: #008acd;
}
.unscramble-body {
  line-height: 22px;
  flex: 1;
  overflow: auto;
}
.unscramble-red{
  color: #b61307;
}
.unscramble-green{
  color: #00ff1a;
}
@media (min-width: 1200px) { /* lg 断点 */
  .dynamic-height {
    height: 100%;
  }
}
// 大屏--报表样式
.largeScreen-report{
  .today-color{
    color: #ff6600;
    font-weight: bold;
    margin-right: 3px;
  }
  .large-screen-head::after {
    content: "";
    display: table;
    clear: both;
  }
  .large-screen-head{
    width: 100%;
    height: 80px;
    background-color: transparent;
    background-position: 50% 0;
    border: none;
    .large-screen-head-title{
      font-size: 40px;
      //font-family: cursive;
      color:#3b749c;  /*设置文字颜色*/
      //text-shadow: 0 2px 4px #9933ff;  /*设置文字阴影*/
      font-weight: bolder;  /*设置文字宽度*/
      text-align: center;  /*设置文字居中*/
      position: absolute;
      left: 50%;
      transform: translate(-50%);
    }
    .large-screen-head-time{
      width: 260px;
      float: left;
      margin-top: 20px;
      margin-left: 10px;
      font-size: 12px;
      height: 30px;
      line-height: 30px;
      text-align: right;
    }
    .large-screen-head-search{
      float: right;
      margin-right: 40px;
      margin-top: 40px;
      font-size: 12px;
    }
  }

  /* 媒体查询：当屏幕宽度小于 768px 时，修改大屏标题部分的样式 */
  @media (max-width: 820px) {
    .large-screen-head{
      .large-screen-head-title{
        font-size: 25px;
      }
      .large-screen-head-time{
        width: 100%;
        font-size: 12px;
        text-align: left;
        margin-top: 20px;
        div{
          display: inline-block;
        }
        div:last-child{
          margin-left: 20px;
        }
      }
      .large-screen-head-search{
        width: 100%;
        margin-top: 0px;
        padding-left: 50px;
      }
    }
  }
  .large-screen-body{
    overflow: hidden auto;
  }
  .large-screen-row>.el-col{
    .large-screen-border-row3{
      height: 35vh;
    }
    .large-screen-border-row2{
      height: 37vh;
    }
    .large-screen-border{
      margin: 10px;
      border:1px solid #18324C;
      position: relative;
      /*四个角角包裹高亮*/
      background: linear-gradient(to left, #9EE7FF, #1C75AC) left top no-repeat,
      linear-gradient(to bottom, #9EE7FF, #1C75AC) left top no-repeat,
      linear-gradient(to left, #9EE7FF, #1C75AC) right top no-repeat,
      linear-gradient(to bottom, #9EE7FF, #1C75AC) right top no-repeat,
      linear-gradient(to left, #9EE7FF, #1C75AC) left bottom no-repeat,
      linear-gradient(to bottom, #9EE7FF, #1C75AC) left bottom no-repeat,
      linear-gradient(to left, #9EE7FF, #1C75AC) right bottom no-repeat,
      linear-gradient(to left, #9EE7FF, #1C75AC) right bottom no-repeat;
      background-size: 2px 10px, 10px 2px, 2px 10px, 10px 2px;
    }
    .large-screen-title-div{
      height: 30px;
      line-height: 30px;
      width: 99%;
      margin: 4px auto;
      background: #3b749c;
      display: flex;
      /*background: linear-gradient(to right, #09205b, #1E90FF);*/
      .large-screen-title{
        flex: 2;
        font-size: 12px;
        padding-left: 10px;
        color: #eeeeee;
        .large-icon{
          float: right;
          margin-right: 10px;
          margin-top: 10px;
          cursor: pointer;
        }
      }
      .large-screen-search{
        flex: 1;
        .el-select{width: 100%}
        .el-select .el-input .el-input__inner{
          border: 0;
          color: #eeeeee;
          font-size: 12px;
        }
      }
    }
    .large-screen-chart-div{
      .no-data{
        width: 100%;
        font-size: 14px;
        display: flex; /* 使用flex布局 */
        align-items: center; /* 垂直居中 */
        justify-content: center; /* 水平居中 */
      }
    }
    //中间特殊的小块
    .large-screen-middle-out{
      border: 1px solid #18324C;
      /*margin: 8px;*/
      /*四个角角包裹高亮*/
      background: linear-gradient(to left, #9EE7FF, #1C75AC) left top no-repeat,
      linear-gradient(to bottom, #9EE7FF, #1C75AC) left top no-repeat,
      linear-gradient(to left, #9EE7FF, #1C75AC) right top no-repeat,
      linear-gradient(to bottom, #9EE7FF, #1C75AC) right top no-repeat,
      linear-gradient(to left, #9EE7FF, #1C75AC) left bottom no-repeat,
      linear-gradient(to bottom, #9EE7FF, #1C75AC) left bottom no-repeat,
      linear-gradient(to left, #9EE7FF, #1C75AC) right bottom no-repeat,
      linear-gradient(to left, #9EE7FF, #1C75AC) right bottom no-repeat;
      background-size: 2px 10px, 10px 2px, 2px 10px, 10px 2px;
    }
    .large-screen-middle-out:nth-child(2n+2) {
      margin-left: 2%;
    }
    .large-screen-middle-out:nth-child(3),.large-screen-middle-out:nth-child(4),.large-screen-middle-out:nth-child(5),.large-screen-middle-out:nth-child(6) {
      margin-top: 10px;
    }
    /*列表样式*/
    .div_risk_ranking{
      width: 100%;
      /*background: pink;*/
      /*padding: 5px;*/
      overflow-y: hidden;
    }
    .div_risk_ranking_item{
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      width: 90%;
      margin: auto;
      display: flex;
      span{
        padding: 0 5px;
        white-space: nowrap; /* 确保文本在一行内显示 */
        overflow: hidden; /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis; /* 超出部分显示为省略号 */
      }
    }
    .last_span_time{ /*最后一列是时间*/
      span:last-child{
        width: 140px !important;
      }
      span:not(:last-child) {
        flex: 1;
      }
    }
    .last_span_no_time{ /*最后一列不是时间*/
      span{
        flex: 1;
      }
    }
  }
}
