/**
 * 判断防伪冒方式是否可以设置为进程特征
 * @param temp
 * @returns {boolean}
 */
export function canSetPropertyMd5(temp) {
  if (temp.propertyMd5 != null && temp.propertyMd5 != '' && temp.propertyMd5 != undefined) { // 兼容绿盾旧数据，绿盾只保存了属性md5，但是没有保存进程的属性
    return true
  }
  // 判断需要的进程属性是否大于等于两个，必须大于两个才能设置防伪冒方式为程序特征。
  let count = 0
  if (temp.originalFilename != null && temp.originalFilename != undefined && temp.originalFilename != '') {
    count++
  }
  if (temp.companyName != null && temp.companyName != undefined && temp.companyName != '') {
    count++
  }
  if (temp.internalName != null && temp.internalName != undefined && temp.internalName != '') {
    count++
  }
  if (temp.fileDesc != null && temp.fileDesc != undefined && temp.fileDesc != '') {
    count++
  }
  if (temp.productName != null && temp.productName != undefined && temp.productName != '') {
    count++
  }
  return count >= 2
}

/**
 * 判断防伪冒方式是否可以设置为程序指纹
 * @param temp
 * @returns {boolean}
 */
export function canSetQuicklyMd5(temp) {
  // 程序的指纹不能为空，才能验证
  return temp.quicklyMd5 != null && temp.quicklyMd5 != undefined && temp.quicklyMd5 != ''
}

/**
 * 当程序改变时，需要修改一下程序的指纹防伪冒级别。因为新的程序可能不支持某些算法。
 * @param temp
 */
export function changeMd5Level(temp) {
  const flag1 = canSetPropertyMd5(temp)
  const flag2 = canSetQuicklyMd5(temp)
  if (!flag1 && !flag2) {
    // 如果不支持程序指纹和进程特征算法，那就关闭进程防伪冒功能
    temp.checkMd5 = 0
    return
  }
  if (!flag1 && flag2) {
    // 如果不支持进程特征，但是支持程序指纹
    temp.md5Level = 1
    return
  }
  if (flag1 && !flag2) {
    // 如果不支持程序指纹，但是支持进程特征
    temp.md5Level = 3
    return
  }
}

/**
 * 构造进程版本
 */
export function createProcessVersion(str1, str2) {
  const maxLength = 60
  if (str1.length >= maxLength) {
    return str1.substring(0, maxLength - 1)
  } else if (str2) {
    const appendStr = str1 + '(' + str2 + ')'
    return appendStr.length >= maxLength ? str1 : appendStr
  }
  return str1
}
