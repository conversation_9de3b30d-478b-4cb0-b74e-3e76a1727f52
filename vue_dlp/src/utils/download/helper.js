import axios from 'axios'
import moment from 'moment'
import i18n from '@/lang'
import store from '@/store'
import router from '@/router'
import request, { setHashCodeHeader } from '@/utils/request'
import { getToken } from '@/utils/auth'
import { getFileIcon } from '@/icons/extension'
import { downloadFile, downloadFileWithUrl, getFilenameFromHeader } from './index'
import StreamDownload from './stream'

/**
 * 生成GUID
 */
export function generateGUID() {
  const s = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-'
  return s.join('')
}

/**
 * 浏览器控制台日志输出下载信息
 * @param message {string|function} 消息文本或消息获取函数
 * @param level {string} 日志级别 error|warn|info(默认)|trace
 */
export function logDownloadMessage(message, level = 'log') {
  if (store.getters.downloadLogEnabled) {
    console[level](typeof message === 'function' ? message() : message)
  }
}

/**
 * 创建代理对象
 * @param targetObj 目标对象
 * @param defaultVal 默认值
 * @param setter 代理事件处理函数
 * @returns {Proxy} 代理对象
 */
export function createProxyObj(targetObj, defaultVal, setter) {
  return new Proxy(targetObj, {
    get(obj, key) {
      return key in obj ? obj[key] : defaultVal
    },
    set(obj, key, val) {
      const oldVal = obj[key]
      obj[key] = val
      if (typeof setter === 'function') {
        setter(obj, key, val, oldVal)
      }
      return true
    }
  })
}

/**
 * 默认下载文件信息
 * @type {{canceled: boolean, progress: number, active: number, finished: boolean, error: boolean, steps: number, percent: number}}
 */
export const DEFAULT_DOWNLOAD_FILE = {
  progress: 0, // 下载总进度百分比
  finished: false, // 文件是否已取消下载，默认false
  canceled: false, // 文件是否下载完成，默认false
  error: false, // 是否出错，Boolean或String类型；若为String，表示出错信息
  steps: 1, // 下载过程总共有几个步骤（至少一个步骤：下载）
  active: 0, // 当前激活步骤（索引从0开始）
  percent: 0 // 当前激活步骤的下载进度
}

/**
 * 根据文件名构建下载文件
 * @param filename {string} 文件名
 * @param zipped {boolean} 是否压缩
 * @param nonreactive {boolean} 是否非响应式
 * @returns {Object|Proxy<{canceled: boolean, name: *, icon: string, guid: string, active: number, progress: number, finished: boolean, error: boolean, percent: number, steps: number}>}
 */
export function buildDownloadFileByName(filename, zipped = false, nonreactive = false) {
  const name = filename.replace(/\u0000/g, '').replace(/\//g, '').replace(/\\/g, '_')
  const guid = generateGUID()
  const icon = getDownloadFileIcon(name, zipped)
  const fileInfo = { guid, name, icon, aTag: false, mtime: Date.now(), ...DEFAULT_DOWNLOAD_FILE }
  const currentRoute = router.currentRoute;
  if (currentRoute.meta && currentRoute.meta.code) {
    fileInfo.menuCode = currentRoute.meta.code
  }
  updateAbortFunc(fileInfo, () => { fileInfo.canceled = true })
  if (nonreactive) {
    return fileInfo
  }
  return createProxyObj(fileInfo, undefined, (obj, key, val, oldVal) => {
    if (key === 'name') {
      if (val !== oldVal) {
        obj.icon = getDownloadFileIcon(val)
        storeDownloadFile(obj)
        showDownloadList()
      }
      return
    }
    if (!(key in DEFAULT_DOWNLOAD_FILE) || key === 'progress') {
      return
    }
    if (key === 'finished') {
      calcDownloadFilePercent(obj)
      storeDownloadFile(obj)
      showDownloadList()
      return
    }
    if (isDownloadReady(obj)) {
      return
    }
    if (key === 'canceled' || key === 'error') {
      storeDownloadFile(obj)
      showDownloadList()
      return
    }
    if (key === 'active') {
      obj.percent = 0
    }
    if (calcDownloadFilePercent(obj) === 100) {
      obj.error = false
      obj.canceled = false
      storeDownloadFile(obj)
      showDownloadList()
      return
    }
    storeDownloadFile(obj)
  })
}

/**
 * 构建下载文件
 * @param selection {Array} 表格选中的行数据
 * @param zipped {boolean} 是否压缩
 * @param zipName {string} 压缩包名称，不带后缀
 * @param nonreactive {boolean} 是否非响应式
 * @returns {Object|Proxy<{canceled: boolean, name: *, icon: string, guid: string, active: number, progress: number, finished: boolean, error: boolean, percent: number, steps: number}>}
 */
export function buildDownloadFile(selection, zipped, zipName, nonreactive = false) {
  const name = getDownloadFileName(selection, zipped, zipName)
  return buildDownloadFileByName(name, zipped, nonreactive)
}

/**
 * 获取下载文件进度条颜色
 * @param file
 * @returns {string}
 */
export function getDownloadFileColor(file) {
  if (file.finished) {
    return '#67c23a'
  }
  if (isDownloadReady(file)) {
    return '#409eff'
  }
  if (file.error) {
    return '#f56c6c'
  }
  if (file.canceled) {
    return '#909399'
  }
  return '#409eff'
}

/**
 * 格式化下载文件状态
 */
export function formatDownloadFileStatus(file) {
  if (file.finished) {
    return i18n.t('components.completed')
  }
  if (file.aTag && isDownloadReady(file)) {
    return i18n.t('components.downloadReady')
  }
  if (file.error) {
    return typeof file.error === 'string' ? file.error : i18n.t('components.downloadError')
  }
  if (file.canceled) {
    return i18n.t('components.canceled')
  }
  return undefined
}

/**
 * 下载文件是否可移除
 */
export function isDownloadFileRemovable(file) {
  return file.canceled || file.finished || isDownloadReady(file)
}

/**
 * 处理 axios 下载请求错误
 */
export function handleAxiosRequestError(file, error) {
  if (error instanceof axios.Cancel) {
    return handleDownloadRequestAbort()
  }
  if (!(error instanceof Error)) {
    return handleDownloadRequestError(file, error ? error.toString() : '')
  }
  if (error.response) {
    const response = error.response
    if (response.data) {
      const resData = response.data
      const isUnknown = !resData.data || resData.data === 'No message available'
      const errMsg = isUnknown ? i18n.t('pages.serverUnknownError') : resData.data
      return handleDownloadRequestError(file, errMsg)
    }
    if (response.status === 404) {
      return handleDownloadRequestError(file, i18n.t('text.file_not_found'))
    }
  }
  const isTimeout = error.message === 'Network Error' || error.code === 'ECONNABORTED'
  if (isTimeout) {
    return handleDownloadRequestError(file, i18n.t('pages.timeout'))
  }
  return handleDownloadRequestError(file, error.message)
}

/**
 * 通用下载（两步下载：获取令牌 -> 下载文件）
 */
export function fetchFile({
  topic,                      // 请求主题
  url,                        // 请求路径
  data,                       // 请求数据
  method,                     // 请求方法 GET/POST
  file,                       // 下载文件信息
  keepSteps                   // 保持步骤数目不变
}) {
  if (!keepSteps) {
    const originSteps = file.steps || 1
    file.steps = originSteps + 1
  }
  return request({ url, data, method: method || 'post', headers: { 'Menu-Code': file.menuCode }})
    .then(response => isFetchReady(response.headers.location, file))
    .catch(error => handleAxiosRequestError(file, error))
    .then(url => fetchDownload({ topic, url, file }))
}

/**
 * 下载请求是否准备完成
 * @param token 下载令牌
 * @param file 下载文件信息
 * @returns Promise.resolve(downloadUrl)
 */
export function isFetchReady(token, file) {
  if (file.canceled) {
    return Promise.reject(new axios.Cancel())
  }
  file.active = file.steps - 2
  const url = '/download/status/' + token
  const sleep = millis => new Promise(resolve => setTimeout(resolve, millis))
  const getReadyStatus = () => request.get(url).then(respond => {
    if (file.canceled) {
      return Promise.reject(new axios.Cancel())
    }
    if (respond.data) {
      file.active = file.steps - 1
      return '/download/common/' + token + '/' + encodeURIComponent(file.name)
    }
    if (file.percent < 99) {
      file.percent += 0.1
    } else {
      storeDownloadFile(file)
    }
    return sleep(1500).then(getReadyStatus)
  })
  return getReadyStatus()
}

/**
 * 下载文件
 */
export function fetchDownload({
  topic,                      // 请求主题
  url,                        // 请求路径
  jwt = false,                // 是否需要JWT认证
  method = 'get',             // 请求方法 GET/POST
  headers = {},               // 请求头
  data,                       // 请求数据，为FormData时表示上传文件（Content-Type: multipart/form-data），为object时表示json格式参数，为string时表示表单格式参数
  file,                       // 下载文件信息
  size                        // 文件大小
}) {
  if (file.canceled) {
    return handleDownloadRequestAbort()
  }
  headers['Menu-Code'] = file.menuCode
  // service worker 代理流式下载
  if (!isStreamDownloadErrorHappened() && StreamDownload.isEnabled() && StreamDownload.isSupported()) {
    const controller = new AbortController()
    updateAbortFunc(file, () => { controller.abort() })
    const init = { method, headers, signal: controller.signal, credentials: 'include', redirect: 'follow' }
    if (jwt && store.getters.token) {
      headers['Authorization'] = getToken()
    }
    if (method && (method.toLowerCase() !== 'get')) {
      if (data instanceof FormData) {
        init.body = data
        // 删除手动设置的Content-Type请求头，避免后台报错 Error: Multipart: Boundary not found
        // 请求数据类型为FormData时，浏览器会自动添加带boundary信息的Content-Type请求头
        delete headers['Content-Type']
        delete headers['content-type']
      } else {
        const dataType = typeof data
        if (dataType === 'object') {
          headers['Content-Type'] = 'application/json'
          init.body = JSON.stringify(data)
        } else if (dataType === 'string') {
          headers['Content-Type'] = 'application/x-www-form-urlencoded'
          init.body = data
        }
      }
    }
    setHashCodeHeader(headers, url)
    return fetch(correctUrl(url), init).then(response => {
      if (response.status === 204) {
        return Promise.reject(response.statusText || i18n.t('components.downloadIntercepted'))
      }
      if (response.status === 403) {
        return Promise.reject(i18n.t('httpStatus.403'))
      }
      if (response.status === 404) {
        return Promise.reject(i18n.t('httpStatus.404'))
      }
      if (response.status === 410) {
        return Promise.reject(i18n.t('httpStatus.410'))
      }
      if (response.status === 425) {
        return Promise.reject(i18n.t('httpStatus.425'))
      }
      if (!response.ok) {
        return response.json().then(res => Promise.reject(res.data))
      }
      const filename = getFilenameFromHeader(response.headers) || file.name
      file.name = filename
      const total = response.headers.get('Content-Length') || size
      const reader = response.body.getReader()
      const writer = StreamDownload.createWritableStream(filename, total, topic).getWriter()
      let loaded = 0
      const pump = () => reader.read().then(res => {
        if (res.done) {
          handleDownloadProgress(total, loaded, true, false, file)
          writer.close()
          logDownloadMessage('Download completed')
        } else {
          loaded += res.value.length
          handleDownloadProgress(total, loaded, false, false, file)
          if (total) {
            logDownloadMessage(() => `Downloaded ${loaded} of ${total} (${(loaded / total * 100).toFixed(2)}%)`)
          } else {
            logDownloadMessage(() => `Downloaded ${loaded}`)
          }
          return writer.write(res.value).catch(reason => {
            incrementStreamDownloadErrorCount()
            handleDownloadProgress(total, loaded, false, true, file)
            console.error('Write stream error...\n\t', reason)
            // 流式下载失败（可能浏览器所在磁盘空间不足），请重试！
            return Promise.reject(i18n.t('components.streamDownloadError'))
          }).then(pump)
        }
      })
      return pump()
    }).catch(e => {
      if (e instanceof DOMException && e.name === 'AbortError') {
        // The user aborted a request.
        return handleDownloadRequestAbort()
      }
      return handleDownloadRequestError(file, e.message || e)
    })
  }

  // a标签下载
  if ((!method || (method.toLowerCase() === 'get')) && !jwt) {
    file.aTag = true
    return new Promise((resolve, reject) => {
      try {
        const download = !(location.protocol === 'https:' && window.isSecureContext)
        downloadFileWithUrl(correctUrl(url), file.name, download)
        // 控制台这边处理已完成，但下载过程交由浏览器处理，不保证下载一定完成
        handleDownloadProgress(100, 100, false, false, file)
        resolve()
      } catch (e) {
        handleDownloadRequestError(file, e.message || e).catch(reject)
      }
    })
  }

  // axios 下载
  const cancelTokenSource = axios.CancelToken.source()
  updateAbortFunc(file, cancelTokenSource.cancel)
  return request({ url, method, headers, data,
    cancelToken: cancelTokenSource.token,
    responseType: 'blob',
    timeout: 0,
    onDownloadProgress(progressEvent) {
      const total = progressEvent.total || size
      const loaded = progressEvent.loaded
      handleDownloadProgress(total, loaded, total === loaded, false, file)
    }
  }).then(res => {
    if (res.loaded) {
      file.name = res.filename
    } else {
      downloadFile(file.name, res)
    }
    handleDownloadProgress(size, size, true, false, file)
  }).catch(e => handleAxiosRequestError(file, e))
}

// ------------------------------private function zone------------------------------
/**
 * 获取下载文件名
 * @param selection 表格选中的行数据
 * @param zipped 是否压缩
 * @param zipName 压缩包名称，不带后缀
 * @returns {string} 下载文件名
 */
function getDownloadFileName(selection, zipped, zipName) {
  const fileName = selection[0].fileName || selection[0].name
  if (!zipped) {
    return fileName
  }
  if (selection.length === 1) {
    return fileName + '.zip'
  }
  return `${zipName}_${moment().format('YYYY-MM-DD HH-mm-ss')}.zip`
}

/**
 * 获取下载文件图标
 * @param filename 文件名
 * @param zipped 是否压缩文件
 * @returns {string} 图标类名
 */
function getDownloadFileIcon(filename, zipped) {
  if (zipped) {
    return getFileIcon('zip')
  }
  const i = filename.lastIndexOf('.')
  if (i < 0) {
    return getFileIcon()
  }
  const suffix = filename.slice(i + 1).toLowerCase()
  if (!suffix) {
    return getFileIcon()
  }
  return getFileIcon(suffix)
}

/**
 * 文件是否下载就绪
 */
function isDownloadReady(file) {
  return file.progress === 100 || file.progress === '100%'
}

/**
 * 计算下载文件总进度
 */
function calcDownloadFilePercent(file) {
  if (isDownloadReady(file)) {
    logDownloadMessage('calcPercent 6')
    return 100
  }
  const steps = file.steps
  if (file.finished || (file.percent === 100 && file.active === steps - 1)) {
    logDownloadMessage('calcPercent 1')
    return updateDownloadFilePercent(file, 100)
  }
  if (steps === 0) {
    logDownloadMessage('calcPercent 2')
    return updateDownloadFilePercent(file, 100)
  }
  if (file.active === steps) {
    logDownloadMessage('calcPercent 5')
    return parseInt(file.progress)
  }
  if (steps === 1) {
    logDownloadMessage('calcPercent 3')
    return updateDownloadFilePercent(file, Math.floor(file.percent))
  }
  const weight = 1 / steps
  const percent = Math.floor(file.active * weight * 100 + file.percent * weight)
  logDownloadMessage('calcPercent 4')
  return updateDownloadFilePercent(file, percent)
}

/**
 * 更新下载文件总进度
 */
function updateDownloadFilePercent(file, percent) {
  logDownloadMessage(() => `steps ${file.steps}, active ${file.active}, current step percent ${file.percent}, total percent ${percent}`)
  file.progress = percent + '%'
  return percent
}

/**
 * 纠正url
 */
function correctUrl(url) {
  if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith(process.env.VUE_APP_BASE_API)) {
    return url
  }
  return process.env.VUE_APP_BASE_API + url
}

/**
 * 更新请求中断函数
 * @param file 下载文件信息
 * @param abort 请求中断函数
 */
function updateAbortFunc(file, abort) {
  const abortFunc = file.abort
  file.abort = () => {
    file.canceled = true
    abort()
    if (typeof abortFunc === 'function') {
      abortFunc()
    }
  }
}

/**
 * 处理下载进度
 * @param total 文件总大小
 * @param loaded 已下载大小
 * @param done 是否下载完成
 * @param canceled 是否已取消下载
 * @param file 下载文件信息
 */
function handleDownloadProgress(total, loaded, done, canceled, file) {
  if (canceled) {
    file.canceled = true
    return
  }
  if (done) {
    file.finished = true
    return
  }
  if (total && total >= loaded) {
    file.percent = loaded / total * 100
    return
  }
  if (!total && file.percent < 99) {
    file.percent += 0.5
  } else {
    storeDownloadFile(file)
  }
}

/**
 * 处理下载请求错误（文本信息）
 */
function handleDownloadRequestError(file, error) {
  file.error = error || true
  if (file.percent < 10) {
    file.percent = 10
  }
  return Promise.reject(error)
}

/**
 * 处理下载请求中断
 */
function handleDownloadRequestAbort() {
  console.warn('Download request is canceled.')
  return Promise.reject('AbortError')
}

/**
 * 存储下载文件信息
 */
function storeDownloadFile(file) {
  file.mtime = Date.now()
  store.dispatch('downloader/update', file)
}

/**
 * 显示下载列表
 */
function showDownloadList() {
  logDownloadMessage('[download] showDownloadList', 'trace')
  store.dispatch('downloader/visible', true)
}

/**
 * 是否发生流式下载错误
 */
function isStreamDownloadErrorHappened() {
  return store.getters.streamDownloadErrorCount > 0
}

/**
 * 增加流式下载错误次数
 */
function incrementStreamDownloadErrorCount() {
  store.dispatch('downloader/countError', store.getters.streamDownloadErrorCount + 1)
}
