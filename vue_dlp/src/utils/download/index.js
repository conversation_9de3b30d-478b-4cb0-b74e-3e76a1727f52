import { EMPTY_BLOB } from '@/utils/blob'

/**
 * 下载文件
 * @param filename 文件名
 * @param fileParts 文件数据
 * @param mimeType MIME类型
 */
export function downloadFile(filename, fileParts, mimeType) {
  if (window.navigator.msSaveBlob) {  // IE以及IE内核的浏览器
    try {
      if (Array.isArray(fileParts)) {
        const blob = new Blob(fileParts, { type: mimeType || 'application/octet-stream' })
        window.navigator.msSaveBlob(blob, filename)
      } else {
        window.navigator.msSaveBlob(fileParts, filename)
      }
      // window.navigator.msSaveOrOpenBlob(file, fileName);  //此方法类似上面的方法，区别可自行百度
    } catch (e) {
      console && console.log(e)
    }
  } else {
    const blob = new Blob(Array.isArray(fileParts) ? fileParts : [fileParts], { type: mimeType || 'application/octet-stream' })
    const url = window.URL.createObjectURL(blob);
    downloadFileWithUrl(url, filename)
    window.URL.revokeObjectURL(url) // 释放掉blob对象
  }
}

/**
 * 下载大小为0的文件
 * @param filename 文件名
 */
export function downloadZeroByteFile(filename) {
  if (window.navigator.msSaveBlob) {
    window.navigator.msSaveBlob(EMPTY_BLOB, filename)
  } else {
    const url = window.URL.createObjectURL(EMPTY_BLOB)
    downloadFileWithUrl(url, filename)
    window.URL.revokeObjectURL(url)
  }
}

/**
 * 使用url路径下载文件
 * @param url 下载路径
 * @param filename 文件名
 * @param download 是否强制下载
 */
export function downloadFileWithUrl(url, filename, download = true) {
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  if (download) {
    link.setAttribute('download', filename)// 文件名
  }
  document.body.appendChild(link)
  link.addEventListener('click', stopPropagation)
  link.click()
  link.removeEventListener('click', stopPropagation)
  document.body.removeChild(link) // 下载完成移除元素
}

/**
 * 阻止事件冒泡
 */
function stopPropagation(event) {
  event.stopPropagation()
}

/**
 * 获取响应头名称获取响应头的值
 */
export function getHeaderValue(headers, headerName) {
  if (headers instanceof Headers) {
    return headers.get(headerName)
  }
  return headers[headerName]
}

/**
 * 根据响应头获取下载文件名
 */
export function getFilenameFromHeader(headers) {
  // 约定filename响应头，非标准
  const filename = getHeaderValue(headers, 'filename')
  if (filename) {
    return decodeURIComponent(filename)
  }
  const header = getHeaderValue(headers, 'content-disposition')
  return getFilenameFromContentDisposition(header)
}

/**
 * Content-Disposition作为响应头时，其内容主要有以下五种形式：第5种主要用于Safari浏览器，以 filename*= 后的文件名为准
 * Content-Disposition: inline
 * Content-Disposition: attachment
 * Content-Disposition: attachment; filename="filename.jpg"
 * Content-Disposition: attachment; filename*=utf-8''filename.jpg
 * Content-Disposition: attachment; filename="filename.jpg"; filename*=utf-8''filename.jpg
 */
export function getFilenameFromContentDisposition(contentDisposition) {
  if (contentDisposition) {
    let filename = ''
    const items = contentDisposition.split(';').map(item => item.trim())
    for (let i = 0; i < items.length; i++) {
      filename = items[i]
      if (filename.startsWith('filename*=')) {
        return decodeURIComponent(filename.split('\'\'')[1])
      }
    }
    if (filename && filename.startsWith('filename=')) {
      // 兼容 Content-Disposition: attachment; filename=filename.jpg 形式响应头
      filename = filename.slice(9)
      if (filename.charAt(0) === '"' && filename.slice(-1) === '"') {
        filename = filename.slice(1, -1)
      }
      return decodeURIComponent(filename)
    }
  }
  return null
}

/**
 * 根据下载URL获取下载文件名
 */
export function getFilenameFromUrl(response) {
  const url = response.config.url.slice(response.config.baseURL.length)
  const index = url.lastIndexOf('/')
  if (index > 0) {
    return decodeURIComponent(url.slice(index + 1))
  }
  return null
}

const FILE_CONTENT_TYPE_MAP = {
  'text/plain': '.txt',
  'text/html': '.html',
  'text/css': '.css',
  'application/javascript': '.js',
  'application/json': '.json',
  'application/xml': '.xml',
  'image/png': '.png',
  'image/jpeg': '.jpg',
  'image/gif': '.gif',
  'application/msword': '.doc',
  'application/vnd.ms-excel': '.xls',
  'application/vnd.ms-powerpoint': '.ppt',
  'application/pdf': '.pdf'
}

/**
 * 根据 content-type 响应头获取下载文件后缀名
 */
export function getFileExtFromContentType(response) {
  const header = response.headers['content-type']
  for (const key in FILE_CONTENT_TYPE_MAP) {
    if (header.indexOf(key) >= 0) {
      return FILE_CONTENT_TYPE_MAP[key]
    }
  }
  return ''
}

/**
 * 处理下载
 */
export function handleDownload(response) {
  const filename = getFilenameFromHeader(response.headers)
  if (filename) {
    downloadFile(filename, response.data)
    response.data.loaded = true
    response.data.filename = filename
  }
}
