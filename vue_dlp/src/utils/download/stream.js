let supported = !!(
  window.isSecureContext &&
  window.MessageChannel &&
  window.ReadableStream &&
  window.WritableStream &&
  window.TransformStream &&
  navigator.serviceWorker
) && isStreamTransferableByUA() && isReadableStreamTransferable()

if (supported) {
  // 需要验证 ServiceWorker 能够成功注册才能保证流式代理下载正常运行
  supported = false
  navigator.serviceWorker.register('download/worker.js', { scope: './download/' }).then(swReg => {
    console.info('ServiceWorker Register OK!')
    supported = true
    swReg.unregister()
  }).catch(err => {
    console.warn('ServiceWorker Register Failed!\n', err)
  })
} else {
  console.warn('ServiceWorker unsupported!')
}

/**
 * 根据用户代理判断 ReadableStream、WritableStream、TransformStream 是否是可转移对象
 */
function isStreamTransferableByUA() {
  const ua = navigator.userAgent
  if (ua.indexOf('Mozilla/5.0 (') !== 0) {
    return false
  }
  // 火狐浏览器103版之后支持转移流
  const firefoxfIdx = ua.indexOf(' Gecko/20100101 Firefox/')
  if (firefoxfIdx > 0) {
    return isSupportedVersion(ua.slice(firefoxfIdx + 24), 103)
  }
  // Chrome内核版本87之后支持转移流
  const chromeIdx = ua.indexOf(' AppleWebKit/537.36 (KHTML, like Gecko) Chrome/')
  if (chromeIdx > 0) {
    return isSupportedVersion(ua.slice(chromeIdx + 47), 87)
  }
  // 其它内核的浏览器情况太复杂，按不支持处理
  return false
}

/**
 * 是否受支持的版本
 * @param verStr {string} 详细版本字符串
 * @param minVer {number} 支持的最低版本
 * @returns {boolean} 是否支持
 */
function isSupportedVersion(verStr, minVer) {
  const mainVer = parseInt(verStr.slice(0, verStr.indexOf('.')))
  return mainVer >= minVer
}

/**
 * 可读流是否可以通过postMessage方法传递（主要是360极速浏览器不支持）
 */
function isReadableStreamTransferable() {
  const { readable } = new window.TransformStream()
  const { port1, port2 } = new window.MessageChannel()
  try {
    port2.onmessage = function(event) {
      const transferable = event.data.readable
      if (transferable) {
        transferable.cancel()
        port1.close()
        port2.close()
      }
    }
    port1.postMessage({ readable }, [readable])
  } catch (e) {
    readable.cancel()
    port1.close()
    port2.close()
    return false
  }
  return true
}

/**
 * 是否启用流式下载
 */
function isEnabled() {
  return true
}

/**
 * 是否支持serviceWorker流式下载
 */
function isSupported() {
  return supported
}

/**
 * create a hidden iframe and append it to the DOM (body)
 *
 * @param  {string} src page to load
 * @return {HTMLIFrameElement} page to load
 */
function createIframe(src) {
  const iframe = document.createElement('iframe')
  iframe.hidden = true
  iframe.src = src
  document.body.appendChild(iframe)
  return iframe
}

/**
 * 创建可写流
 * @see https://github.com/jimmywarting/StreamSaver.js
 * @param filename 文件名
 * @param size 文件大小
 * @param topic 文件主题
 * @returns {WritableStream<any>}
 */
function createWritableStream(filename, size, topic) {
  const { readable, writable } = new window.TransformStream()
  const channel = new window.MessageChannel()
  channel.port1.onmessage = evt => {
    if (evt.data.ready) { // Service worker is ready.
      channel.port1.postMessage({ readable }, [readable])
    } else if (evt.data.download) { // Service worker sent us a link that we should open.
      // We never remove this iframes b/c it can interrupt saving
      createIframe(evt.data.download)
    } else if (evt.data.abort) {
      channel.port1.postMessage('abort') // send back so controller is aborted
      channel.port1.onmessage = null
      channel.port1.close()
      channel.port2.close()
    }
  }
  let mitm = document.querySelector('iframe[name="mitm"]')
  if (mitm == null) {
    mitm = createIframe('./download/mitm.html?version=1.0')
    mitm.name = 'mitm'
  }
  if (mitm.loaded) {
    mitm.contentWindow.postMessage({ filename, size, topic }, location.origin, [channel.port2])
  } else {
    mitm.addEventListener('load', () => {
      mitm.loaded = true
      mitm.contentWindow.postMessage({ filename, size, topic }, location.origin, [channel.port2])
    }, { once: true })
  }
  return writable
}

const StreamDownload = { isEnabled, isSupported, createWritableStream }

export default StreamDownload
