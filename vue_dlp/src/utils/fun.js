import store from '@/store'
import StreamDownload from '@/utils/download/stream'
import { setTheFirstLetterToUppercase } from '@/utils/i18n'
import { deepClone } from '@/utils'

export default {
  install(Vue) {
    /**
     * 标题、文字拼接新增、修改等内容的方法
     * @param {String} text 需要拼接的文字
     * @param {String} type 需要拼接的类型
     * @param {Boolean} edit 是否可编辑，当type=='update'时，值为false时，拼接详情
     * @returns 拼接完的文字
     */
    Vue.prototype.i18nConcatText = function(text, type, edit) {
      const lang = store.getters.language
      const typeParam = {
        create: 'text.addInfo',
        update: edit === false ? 'text.detailsInfo' : 'text.editInfo',
        delete: 'text.deleteInfo',
        details: 'text.detailsInfo',
        import: 'text.importInfo',
        export: 'text.exportInfo',
        batchAdd: 'text.batchAddInfo'
      }[type]
      if (lang == 'en') {
        return setTheFirstLetterToUppercase(this.$t(typeParam, { info: text }))
      } else {
        return this.$t(typeParam, { info: text })
      }
    }
    // 判断两个对象属性和值是否相等
    Vue.prototype.isEqual = function(objA, objB) {
      // 相等
      if (objA === objB) return objA !== 0 || 1 / objA === 1 / objB
      // 空判断
      if (objA == null || objB == null) return objA === objB
      // 类型判断
      if (Object.prototype.toString.call(objA) !== Object.prototype.toString.call(objB)) return false

      switch (Object.prototype.toString.call(objA)) {
        case '[object RegExp]':
        case '[object String]':
          // 字符串转换比较
          return '' + objA === '' + objB
        case '[object Number]':
          // 数字转换比较,判断是否为NaN
          // eslint-disable-next-line no-self-compare
          if (+objA !== +objA) {
            // eslint-disable-next-line no-self-compare
            return +objB !== +objB
          }

          return +objA === 0 ? 1 / +objA === 1 / objB : +objA === +objB
        case '[object Date]':
        case '[object Boolean]':
          return +objA === +objB
        case '[object Array]':
          // 判断数组
          for (let i = 0; i < objA.length; i++) {
            if (!this.isEqual(objA[i], objB[i])) return false
          }
          return true
        case '[object Object]':
          // 判断对象
          // eslint-disable-next-line no-case-declarations
          let keys = Object.keys(objA)
          for (let i = 0; i < keys.length; i++) {
            if (!this.isEqual(objA[keys[i]], objB[keys[i]])) return false
          }

          keys = Object.keys(objB)
          for (let i = 0; i < keys.length; i++) {
            if (!this.isEqual(objA[keys[i]], objB[keys[i]])) return false
          }

          return true
        default :
          return false
      }
    }

    // 定义一个深拷贝函数  接收目标target参数
    Vue.prototype.deepClone = deepClone
    /**
       * 把对象转成formData
       * @param val
       * @returns {FormData}
       */
    Vue.prototype.toFormData = function(val) {
      const formData = new FormData()
      for (const i in val) {
        isArray(val[i], i)
      }
      function isArray(array, key) {
        if (array == undefined || typeof array == 'function') {
          return false
        }
        if (typeof array != 'object') {
          formData.append(key, array)
        } else if (array instanceof Array) {
          if (array.length != 0) {
            for (const i in array) {
              for (const j in array[i]) {
                isArray(array[i][j], `${key}[${i}].${j}`)
              }
            }
          }
        } else {
          const arr = Object.keys(array)
          if (arr.indexOf('uid') == -1) {
            for (const j in array) {
              isArray(array[j], `${key}.${j}`)
            }
          } else {
            formData.append(`${key}`, array)
          }
        }
      }
      return formData
    }
    /**
     * 获取目标对象指定属性的值，可获取深层次的值
     * @param {Object} object 目标对象
     * @param {Array, String} attrs 需要获取值的属性，eg: ['attr1', 'attr2']、'attr1.attr2'
     * @param {String} split 属性的分隔符，当attrs是字符串时，多个属性之间的分隔符，默认为 '.'
     * @returns 返回指定属性的值，没有值返回 undefined
     */
    Vue.prototype.getValueFromObject = function(object, attrs, split = '.') {
      if (Array.isArray(attrs)) {
        return attrs.reduce((value, key) => value && value[key], object)
      } else if (typeof attrs === 'string') {
        return attrs.split(split).reduce((value, key) => value && value[key], object)
      } else {
        throw new Error('Parameter error')
      }
    }
    /**
     * 把一个数字拆成二进制数组，
     * 例如 7 => 1,2,4
     *      8 => 8
     *      9 => 1,8
     * @param num
     * @returns []
     */
    Vue.prototype.numToArr = function(num) {
      const list = []
      let d = num
      for (let i = 0; d > 0; i++) {
        const pow = Math.pow(2, i)
        if ((num & pow) > 0) {
          // 计算对数
          list.push(pow)
          d -= pow
        }
      }
      return d < 0 ? [] : list
    }
    /**
       * 把一个数字拆成二进制数组， 例如 7 => 1,2,4
       * @param num
       * @param count
       * @returns []
       */
    Vue.prototype.numToList = function(num, count) {
      const list = []
      for (let i = 0; i < count; i++) {
        const pow = Math.pow(2, i)
        if ((num & pow) > 0) {
          // 计算对数
          list.push(pow)
        }
      }
      return list
    }
    /**
       * 求出数组的综合
       * @param list
       * @returns {number}
       */
    Vue.prototype.getSum = function(list) {
      let sum = 0
      list.forEach(item => {
        sum += item
      })
      return sum
    }
    /**
     * 字符串分词为数字数组
     * @param str
     * @returns {number}
     */
    Vue.prototype.splitToNum = function(str, separator) {
      const result = []
      if (!str || str.length === 0) return result
      separator = !separator ? ',' : separator
      const array = str.split(separator)
      array.forEach(numStr => result.push(Number.parseInt(numStr)))
      return result
    }
    /**
     * IE不支持startsWith，endsWith方法，扩充这两个方法
     */
    if (typeof String.prototype.startsWith !== 'function') {
      String.prototype.startsWith = function(str) {
        const reg = new RegExp('^' + str)
        return reg.test(this)
      }
    }
    if (typeof String.prototype.endsWith !== 'function') {
      String.prototype.endsWith = function(str) {
        const reg = new RegExp(str + '$')
        return reg.test(this)
      }
    }
    /**
     * IE不支持includes方法，扩充这两个方法
     */
    if (typeof String.prototype.includes !== 'function') {
      String.prototype.includes = function(str, start) {
        if (start >= this.length) {
          return false
        } else {
          return this.indexOf(str, start) > -1
        }
      }
    }
    if (typeof Array.prototype.includes !== 'function') {
      Array.prototype.includes = function(item, start) {
        let index = 0
        if (start === undefined) start = 0
        if (start >= 0) {
          if (start >= this.length) {
            return false
          }
          index = start
        } else {
          index = this.length + start
          if (index < 0) {
            index = 0
          }
        }
        const itemIsNaN = typeof item === 'number' && isNaN(item)
        let flag = false
        for (let i = index; i < this.length; i++) {
          if (itemIsNaN && typeof this[i] === 'number' && isNaN(this[i])) {
            flag = true
            break
          } else if (item === this[i]) {
            flag = true
            break
          }
        }
        return flag
      }
    }
    /**
     * IE不支持replaceAll方法，扩充该方法
     */
    if (typeof String.prototype.replaceAll !== 'function') {
      String.prototype.replaceAll = function(findText, repText) {
        var sourceStr = this.valueOf();
        while (sourceStr.indexOf(findText) !== -1) {
          sourceStr = sourceStr.replace(findText, repText);
        }
        return sourceStr;
      }
    }
    /**
     * 部分旧版浏览器不支持 at 方法，扩充该方法
     */
    // if (typeof Array.prototype.at !== 'function') {
    //   console.log('Array', Array.prototype.length)
    //   Array.prototype.at = function(index) {
    //     console.log('index', index)
    //     // 将 index 转成数值并取整
    //     index = parseInt(Number(index) || 0)
    //     // index 是负数的话，从数组尾部开始取值
    //     if (index < 0) {
    //       index += this.length
    //     }
    //     return this[index]
    //   }
    // }
    /**
     * 通过代码促使页面resize
     */
    Vue.prototype.triggerResize = function() {
      if (document.createEvent) {
        var event = document.createEvent('HTMLEvents')
        event.initEvent('resize', true, true)
        window.dispatchEvent(event)
      } else if (document.createEventObject) {
        window.fireEvent('onresize')
      }
    }
    /**
     * 普通字符转换成转意符
     */
    Vue.prototype.html2Escape = function(str) {
      if (!str) { return '' }
      return !!str && str.replace(/[<>&"]/g, function(c) { return { '<': '&lt;', '>': '&gt;', '&': '&amp;', '"': '&quot;' }[c] })
    }
    Vue.prototype.isIE = function() {
      return navigator && !!(/trident/i).exec(navigator.userAgent);
    }
    Vue.prototype.isEdge = function() {
      return navigator && !!(/edge/i).exec(navigator.userAgent);
    }
    /**
     * 是否有菜单权限
     * @param menuCode     菜单编码，String，可传入多个编码，用 , 隔开, 也支持 ! & | 运算符组成的表达式
     * @returns {boolean}
     */
    Vue.prototype.hasPermission = function(menuCode) {
      // allPermission 值为 true 时，开放所有菜单权限
      if (process.env.NODE_ENV == 'development' && store.getters.allPermission) return true
      if (menuCode) {
        // 权限 Map
        const userMenuCodesMap = store.getters.userMenuCodesMap
        let hasPermission = false
        // 将 , 转换成 |
        menuCode = menuCode.replaceAll(',', '|')
        // 通过 | 拆分成 或数组
        const orArr = menuCode.split('|')
        // 遍历 或数组
        for (let i = 0; i < orArr.length; i++) {
          const code = orArr[i].trim();
          if (code.includes('&')) {
            // 包含 & 运算符，通过 & 拆分成 且数组
            const andArr = code.split('&')
            let andPermission = true
            // 遍历 且数组
            for (let i = 0; i < andArr.length; i++) {
              const code = andArr[i].trim();
              // 判断是否有权限
              const hasP = code.indexOf('!') == 0 ? !userMenuCodesMap[code.substr(1)] : userMenuCodesMap[code]
              if (!hasP) {
                // 没有权限，给 andPermission 赋值，并结束 且数组 遍历
                andPermission = hasP
                break
              }
            }
            if (andPermission) {
              // 且 表达式有权限，结束 或数组 遍历
              hasPermission = andPermission
              break
            }
          } else {
            // 不包含 & 运算符，判断是否有权限
            const hasP = code.indexOf('!') == 0 ? !userMenuCodesMap[code.substr(1)] : userMenuCodesMap[code]
            if (hasP) {
              // 有权限，结束 或数组 遍历
              hasPermission = hasP
              break
            }
          }
        }
        return !!hasPermission
      }
      return false
    }
    /**
     * 控制台当前语言环境是否英文
     */
    Vue.prototype.isEnglish = function() {
      return store.getters.language == 'en'
    }
    /**
     * 是否试用版dlp
     */
    Vue.prototype.isTrialDlp = function() {
      return store.getters.trialDlp
    }
    /**
     * 是否启用统一下载管理器
     */
    Vue.prototype.isEnableDownloadManager = function() {
      return StreamDownload.isEnabled()
    }
    /**
     * 是否是开发模式
     */
    Vue.prototype.isDev = function() {
      return process.env.NODE_ENV == 'development'
    }
    /**
     * 使用 sessionStorage 记录菜单是否访问过
     * @param {String} menu 菜单name
     */
    Vue.prototype.setMenuAccessStatus = function(menu) {
      const status = JSON.parse(sessionStorage.getItem('menuStatus')) || {}
      status[menu] = true
      sessionStorage.setItem('menuStatus', JSON.stringify(status))
    }
    /**
     * 使用 sessionStorage 获取菜单的访问状态
     * @param {String} menu 菜单name
     */
    Vue.prototype.getMenuAccessStatus = function(menu) {
      const status = JSON.parse(sessionStorage.getItem('menuStatus')) || {}
      return status[menu]
    }
    window.getMenuAccessStatus = function(menu) {
      const status = JSON.parse(sessionStorage.getItem('menuStatus')) || {}
      return status[menu]
    }
    /**
     * 删除 sessionStorage 的菜单访问记录
     */
    Vue.prototype.removeMenuAccessStatus = function() {
      sessionStorage.removeItem('menuStatus')
    }

    /**
     * 软件资产等相关页面，进入详情等页面之前调用的方法，该方法在beforeRouteEnter中使用
     * 该方法用于在详情页刷新后，返回原列表页
     * @param {*} to
     * @param {*} from
     * @param {*} next
     */
    window.intoSoftware = function(to, from, next) {
      let title = from.meta.title
      const path = from.path

      const qid = !to.params || Object.keys(to.params).length === 0 ? null : JSON.stringify(to.params)
      const software = JSON.parse(sessionStorage.getItem('software'))
      if (title) {
        next(vm => {
          // 因为当钩子执行前，组件实例还没被创建
          // vm 就是当前组件的实例，所以在 next 方法里你就可以把 vm 当 this 来用了。
          // if (!vm.parentPage) {
          if (path && title && (!vm.qid || qid && vm.qid !== qid)) {
            const hasKey = vm.$te(`route.${title}`)
            title = hasKey ? vm.$t(`route.${title}`) : title
            vm.title = title
            vm.parentPage = path
            vm.qid = qid
            sessionStorage.setItem('software', JSON.stringify({ title, path }))
          }
        })
      } else if (software) {
        sessionStorage.removeItem('software')
        next(software.path)
      } else {
        next('/dashboard')
      }
    };
    /**
     * ie不支持TextEncoder、TextDecoder方法，而vue-qr（生成二维码的组件）中使用了TextEncoder方法，由于只使用了utf8编码，
     * 故可以用支持ie的unescape和encodeURIComponent伪装该函数
     */
    (function(window) {
      if (undefined !== window.TextEncoder) { return }
      function _TextEncoder() {

      }
      _TextEncoder.prototype.encode = function(s) {
        return unescape(encodeURIComponent(s)).split('').map(function(val) { return val.charCodeAt() })
      }
      function _TextDecoder() {

      }
      _TextDecoder.prototype.decode = function(code_arr) {
        return decodeURIComponent(escape(String.fromCharCode.apply(null, code_arr)))
      }
      window.TextEncoder = _TextEncoder
      window.TextDecoder = _TextDecoder
    })(window)
  }
}
