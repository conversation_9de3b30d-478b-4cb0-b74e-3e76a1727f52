import i18n from '@/lang'

// 从字典项和字典值中获取选项名称
export function getDictLabel(dicts, dictValue) {
  const dict = getDict(dicts, dictValue)
  return dict ? dict.label : null
}
// 从字典项和字典值中获取选项
export function getDict(dict, dictValue) {
  if (!dict || dictValue === undefined || dictValue === null) return null
  for (let i = 0, size = dict.length; i < size; i++) {
    const temp = dict[i]
    if (temp.value == dictValue) {
      return temp
    }
  }
  return null
}

// 加密算法
export function getEncAlgorithmDict() {
  return [
    { value: 0, label: i18n.t('pages.encAlgorithmRc') },
    { value: 23, label: i18n.t('pages.encAlgorithmZuc') },
    { value: 1, label: i18n.t('pages.encAlgorithmAes') }
  ]
}

// 泄露方式字典项
export function getLossTypeDict(cb) {
  return [
    { value: 2, label: i18n.t('pages.usbCopyFile') },
    { value: 3, label: i18n.t('pages.usbCutFile') },
    { value: 4, label: i18n.t('pages.usbSaveFile') },
    { value: 5, label: i18n.t('pages.stgLabelFilePrintContent') },
    { value: 13, label: i18n.t('pages.stgLabelCdBurnFile') },
    { value: 17, label: i18n.t('pages.stgLabelLocalShareFile') },
    { value: 7, label: i18n.t('pages.stgLabelRemoteShareFile') },
    { value: 1, label: i18n.t('pages.stgLabelEmailTransferContent') },
    { value: 18, label: i18n.t('pages.stgLabelMail') },
    { value: 8, label: i18n.t('pages.stgLabelImSendContent') },
    { value: 9, label: i18n.t('pages.stgLabelImSendFile') },
    { value: 14, label: i18n.t('pages.stgLabelWebAccessContent') },
    { value: 16, label: i18n.t('pages.stgLabelWebPasteContent') },
    { value: 15, label: i18n.t('pages.stgLabelWebUploadFile') },
    { value: 11, label: i18n.t('pages.stgLabelForumPostContent') },
    { value: 20, label: i18n.t('pages.stgLabelBluetooth') },
    { value: 23, label: i18n.t('pages.stgLabelWebDiskUploadFile') },
    { value: 24, label: i18n.t('pages.stgLabelWebDiskDownloadFile') },
    { value: 25, label: i18n.t('pages.stgLabelImDownloadFile') },
    { value: 26, label: i18n.t('pages.stgLabelImReceiveFile') },
    { value: 27, label: i18n.t('pages.stgLabelAdbSendFile') },
    { value: 29, label: i18n.t('pages.stgLabelMtpSendFile') },
    { value: 30, label: i18n.t('pages.stgLabelCdDownloadFile') },
    { value: 31, label: i18n.t('pages.stgLabelWebDownloadFile') }
  ]
}
// 严重程度
export function getSeverityDict() {
  return [
    { value: 1, label: i18n.t('pages.severityOptions1') },
    { value: 2, label: i18n.t('pages.severityOptions2') },
    { value: 3, label: i18n.t('pages.severityOptions3') },
    { value: 4, label: i18n.t('pages.severityOptions4') }
  ]
}
// 应用对象
export function getEntityTypeDict() {
  return [
    { value: 1, label: i18n.t('pages.terminal') },
    { value: 2, label: i18n.t('pages.user') },
    { value: 3, label: i18n.t('pages.terminalGroup') },
    { value: 4, label: i18n.t('pages.userGroup') }
  ]
}
export function getEntityTypeLabel(dictValue) {
  return getDictLabel(getEntityTypeDict(), dictValue)
}
// 加解密受控进程操作类型
export function getProcessActionDict() {
  return [
    { value: 0, label: i18n.t('pages.processStgMsg6') },
    // { value: 2, label: '二代加密算法' },
    { value: 3, label: i18n.t('pages.dictionary_Msg2') },
    // { value: 4, label: '关闭内存映射文件' },
    // { value: 8, label: '不加密可执行文件' },
    // { value: 16, label: '禁止阅读密文' },
    // { value: 32, label: '只允许保存指定后缀的文件' },
    { value: 64, label: i18n.t('pages.dictionary_Msg3') },
    // { value: 128, label: '排除增强网络访问' },
    // { value: 256, label: '切换模式下禁止访问密文' },
    { value: 512, label: i18n.t('pages.dictionary_Msg4') },
    { value: 1024, label: i18n.t('pages.processStgMsg5') }
    // ,{ value: 4096, label: '拷贝到U盘加密' }
  ]
}
export function getProcessActionDict2() {
  return [
    { value: 0, label: i18n.t('pages.processStgMsg6') },
    // { value: 2, label: '二代加密算法' },
    { value: 3, label: i18n.t('pages.dictionary_Msg2') },
    // { value: 4, label: '关闭内存映射文件' },
    // { value: 8, label: '不加密可执行文件' },
    // { value: 16, label: '禁止阅读密文' },
    // { value: 32, label: '只允许保存指定后缀的文件' },
    { value: 64, label: i18n.t('pages.dictionary_Msg3') },
    // { value: 128, label: '排除增强网络访问' },
    // { value: 256, label: '切换模式下禁止访问密文' },
    { value: 512, label: i18n.t('pages.dictionary_Msg4') }
    // ,{ value: 4096, label: '拷贝到U盘加密' }
  ]
}
// 邮箱服务器配置
export function getMailServerDict() {
  return [
    { suffix: '@163.com', sendHost: 'smtp.163.com', sendPort: 25, sendSslPort: 465, recvHost: 'imap.163.com', recvPort: 143, recvSslPort: 993 },
    { suffix: '@126.com', sendHost: 'smtp.126.com', sendPort: 25, sendSslPort: 465, recvHost: 'imap.126.com', recvPort: 143, recvSslPort: 993 },
    { suffix: '@139.com', sendHost: 'smtp.139.com', sendPort: 25, sendSslPort: 465, recvHost: 'pop.139.com', recvPort: 110, recvSslPort: 995 },
    { suffix: '@yeah.net', sendHost: 'smtp.yeah.net', sendPort: 25, sendSslPort: 465, recvHost: 'pop.yeah.net', recvPort: 110, recvSslPort: 995 },
    { suffix: '@sina.com', sendHost: 'smtp.sina.com', sendPort: 25, sendSslPort: 465, recvHost: 'pop.sina.com', recvPort: 110, recvSslPort: 995 },
    { suffix: '@sohu.com', sendHost: 'smtp.sohu.com', sendPort: 25, sendSslPort: 465, recvHost: 'pop3.sohu.com', recvPort: 110, recvSslPort: 995 },
    { suffix: '@qq.com', sendHost: 'smtp.qq.com', sendPort: 25, sendSslPort: 465, recvHost: 'pop.qq.com', recvPort: 110, recvSslPort: 995 },
    { suffix: '@qq.vip.com', sendHost: 'smtp.qq.com', sendPort: 25, sendSslPort: 465, recvHost: 'imap.qq.com', recvPort: 143, recvSslPort: 993 },
    { suffix: '@outlook.com', sendHost: 'smtp-mail.outlook.com', sendPort: 587, sendSslPort: 587, recvHost: 'outlook.office365.com', recvPort: 143, recvSslPort: 993 },
    { suffix: '@gmail.com', sendHost: 'smtp.gmail.com', sendPort: 587, sendSslPort: 587, recvHost: 'pop.gmail.com', recvPort: 110, recvSslPort: 995 }
  ]
}

export function getAiNameDict() {
  return [
    { value: 1, label: '豆包' },
    { value: 2, label: 'DeepSeek' },
    { value: 3, label: '智谱清言' },
    { value: 4, label: '腾讯元宝' },
    { value: 5, label: '通义千问' },
    { value: 6, label: '文心一言' },
    { value: 7, label: 'Kimi' },
    { value: 8, label: '讯飞星火' }
  ]
}

// 操作系统类型
export function getOsTypeDict() {
  return [
    { value: 1, iconClass: 'windows', label: 'Windows' },
    { value: 2, iconClass: 'linux', label: 'Linux' },
    { value: 4, iconClass: 'mac', label: 'Mac' },
    { value: 8, iconClass: 'phone', label: i18n.t('pages.mobile') }
  ]
}

// 终端类型
export function getTermTypeDict() {
  return [
    { value: 0x00, type: 'windows', label: i18n.t('pages.dictionary_Msg5'), icon: 'windows' },
    { value: 0x80, type: 'windows', label: i18n.t('pages.dictionary_Msg6'), icon: 'offline-win' },
    { value: 0x10, type: 'windows', label: i18n.t('pages.WinBoss'), icon: 'boss' },
    { value: 0x20, type: 'windows', label: i18n.t('pages.winUsb'), icon: 'usb-win' },
    { value: 0x01, type: 'linux', label: i18n.t('pages.dictionary_Msg7'), icon: 'linux' },
    { value: 0x81, type: 'linux', label: i18n.t('pages.dictionary_Msg8'), icon: 'offline-linux' },
    { value: 0x11, type: 'linux', label: i18n.t('pages.LinuxBoss'), icon: 'boss' },
    { value: 0x21, type: 'linux', label: i18n.t('pages.linuxUsb'), icon: 'usb-linux' },
    { value: 0x02, type: 'mac', label: i18n.t('pages.dictionary_Msg9'), icon: 'mac' },
    { value: 0x82, type: 'mac', label: i18n.t('pages.dictionary_Msg10'), icon: 'offline-mac' },
    { value: 0x12, type: 'mac', label: i18n.t('pages.MacBoss'), icon: 'boss' },
    { value: 0x22, type: 'mac', label: i18n.t('pages.macUsb'), icon: 'usb-mac' },
    { value: 0x03, type: 'phone', label: i18n.t('pages.dictionary_Msg11'), icon: 'phone' },
    { value: 0x83, type: 'phone', label: i18n.t('pages.dictionary_Msg12'), icon: 'phone' },
    { value: 0x13, type: 'phone', label: i18n.t('pages.dictionary_Msg13'), icon: 'boss' }
  ]
}

// 登录模式字典
export function getLoginModeDict() {
  return [
    { value: 0, label: i18n.t('table.manualLogin') },
    { value: 1, label: i18n.t('table.userAutoLogin') },
    { value: 2, label: i18n.t('table.domainAutoLogin') }
  ]
}

// 告警等级
export function getAlarmLevelDict() {
  return [
    { value: 1, label: i18n.t('text.prompt') },
    { value: 2, label: i18n.t('text.warning') }
  ]
}
// 执行动作
export function getActionDict() {
  return [
    { value: 0, label: i18n.t('pages.allow') },
    { value: 1, label: i18n.t('pages.forbid') },
    { value: 2, label: i18n.t('pages.readOnly') },
    { value: 3, label: i18n.t('pages.null') },
    { value: 4, label: i18n.t('pages.uninstall') },
    { value: 5, label: i18n.t('pages.dictionary_Msg14') }
  ]
}
// 敏感检测类型等级
export function getSstTypeDict() {
  return [
    { value: 0, label: i18n.t('pages.dictionary_Msg16') },
    { value: 1, label: i18n.t('pages.dictionary_Msg17') },
    { value: 2, label: i18n.t('pages.dictionary_Msg18') },
    { value: 99, label: i18n.t('pages.dictionary_Msg45') }
  ]
}

// 普通告警类型
export function getAlarmBusDict() {
  return [
    { value: 0, label: i18n.t('pages.dictionary_Msg16') },
    { value: 1, label: i18n.t('pages.alarmType1') },
    { value: 2, label: i18n.t('pages.alarmType2') }
  ]
}

/**
 * 敏感检测配置：外发配置 + 下载/接收配置
 * 在同一个地方配置key、value和label，避免遗漏
 */
export function getContentStgConfigDict() {
  return {
    out: [
      { key: 'isChatText', value: 1003, label: i18n.t('pages.stgLabelImSendContent') },
      { key: 'isChatFile', value: 1004, label: i18n.t('pages.stgLabelImSendFile') },
      { key: 'isUsbDev', value: 1002, label: i18n.t('pages.stgMessage1') },
      { key: 'isBrowseWebUrl', value: 1005, label: i18n.t('pages.stgLabelWebAccessContent') },
      { key: 'isBrowerPasterOption', value: 1007, label: i18n.t('pages.stgLabelWebPasteContent') },
      { key: 'isBrowerSendFileOption', value: 1006, label: i18n.t('pages.stgLabelWebUploadFile') },
      { key: 'isForumData', value: 1009, label: i18n.t('pages.stgLabelForumPostContent') },
      { key: 'isEmailContent', value: 1008, label: i18n.t('pages.stgLabelEmailTransferContent') },
      { key: 'isEmailAttach', value: 1012, label: i18n.t('pages.stgLabelMail') },
      { key: 'isPrint', value: 1013, label: i18n.t('pages.stgLabelFilePrintContent') },
      { key: 'isLocalDir', value: 1010, label: i18n.t('pages.stgLabelLocalShareFile') },
      { key: 'isRemoteDir', value: 1011, label: i18n.t('pages.stgLabelRemoteShareFile') },
      { key: 'isCDBurn', value: 1001, label: i18n.t('pages.stgLabelCdBurnFile') },
      { key: 'isNetDiskAware', value: 1016, label: i18n.t('pages.stgLabelWebDiskUploadFile') },
      { key: 'isFtpFileAware', value: 1017, label: i18n.t('pages.stgLabelFtpUploadFile') },
      { key: 'isBlueFile', value: 1014, label: i18n.t('pages.stgLabelBluetooth') },
      { key: 'isADBFile', value: 1020, label: i18n.t('pages.stgLabelAdbSendFile') },
      { key: 'isMTP', value: 1021, label: i18n.t('pages.stgLabelMtpSendFile') },
      { key: 'isMSTSC', value: 1024, label: i18n.t('pages.stgLabelRemoteOutgoingFile') },
      { key: 'isRemoteUploadTool', value: 1025, label: i18n.t('pages.stgLabelRemoteToolUpload') },
      { key: 'isAISendMsgAware', value: 1026, label: i18n.t('pages.stgLabelAISendMsgAware') },
      { key: 'isAISendFileAware', value: 1027, label: i18n.t('pages.stgLabelAISendFileAware') }
    ],
    download: [
      { key: 'isChatFileDownload', value: 1015, label: i18n.t('pages.stgLabelImDownloadFile') },
      { key: 'isChatDownloadText', value: 1019, label: i18n.t('pages.stgLabelImReceiveFile') },
      { key: 'isNetDiskDownloadAware', value: 1018, label: i18n.t('pages.stgLabelWebDiskDownloadFile') },
      { key: 'isCDBurnFileDownload', value: 1022, label: i18n.t('pages.stgLabelCdDownloadFile') },
      { key: 'isWebFileDownload', value: 1023, label: i18n.t('pages.stgLabelWebDownloadFile') }
    ]
  }
}
// 敏感检测配置：外发配置，return { key: value }
export function getOutgoingConfigDict() {
  const outDictList = getContentStgConfigDict().out
  return outDictList.reduce((result, { key, value }) => {
    result[key] = value
    return result
  }, {})
}
// 敏感检测配置：下载/接收配置，return { key: value }
export function getDownloadConfigDict() {
  const downloadDictList = getContentStgConfigDict().download
  return downloadDictList.reduce((result, { key, value }) => {
    result[key] = value
    return result
  }, {})
}
// 文件外发量管控：业务类型基础配置
export function getFileOutgoingCheckCount() {
  return {
    isCDBurn: 1001, isUsbDev: 1002, isChatFile: 1004, isBrowerSendFileOption: 1006,
    isRemoteDir: 1011, isEmailAttach: 1012, isBlueFile: 1014, isNetDisk: 1016,
    isFtpFileAware: 1017, isADBFile: 1020, isMTP: 1021, isRemoteDesktop: 1024
  }
}

// 服务器、终端
export function getAlarmTermTypeDict() {
  return [
    { value: 0, label: i18n.t('pages.service') },
    { value: 1, label: i18n.t('pages.terminal') }
  ]
}

export function getSysTypeDict() {
  return [
    // { value: 0, label: `空间使用率` }
  ]
}

// 多语言
export function getLanguageDict() {
  return [
    { value: 'zh_CN', label: i18n.t('pages.zh_CN') },
    { value: 'zh_TW', label: i18n.t('pages.zh_TW') },
    { value: 'en_US', label: 'English' }
  ]
}

export function getAlarmLimitDict() {
  return [
    { label: i18n.t('pages.respondActions1'), value: 1 },
    { label: i18n.t('pages.respondActions2'), value: 2 },
    { label: i18n.t('pages.respondActions3'), value: 4 },
    { label: i18n.t('pages.respondActions4'), value: 8 },
    // { label: i18n.t('pages.wxAlarm'), value: 512 },
    // { label: '短信告警', value: 16 },
    { label: i18n.t('pages.lockScreen'), value: 32 },
    // { label: '断网', value: 64 },
    { label: i18n.t('pages.hotkeyOption5'), value: 128 },
    { label: i18n.t('pages.recordingScreen'), value: 256 },
    { label: i18n.t('pages.thirdPartyPush'), value: 2048 }
  ]
}
export function getUsbTypeDict() {
  return [
    { value: 0, label: i18n.t('pages.dictionary_Msg19') },
    { value: 1, label: i18n.t('pages.dictionary_Msg21') },
    { value: 2, label: i18n.t('pages.decryptKey') },
    { value: 3, label: i18n.t('pages.outFileUDisk') },
    { value: 4, label: i18n.t('pages.safeUDisk') }
    // { value: 5, label: i18n.t('pages.customProfessionalUDisk') }
  ]
}
// 屏幕水印字体
// 为了多语言以及兼容旧策略，value全都改为中文形式，然后利用strFont遍历列表，比对value，取出label，实现多语言
export function getTypeFont() {
  return [
    // { label: '@Batang', value: '@Batang' },
    // { label: '@BatangChe', value: '@BatangChe' },
    // { label: '@Dotum', value: '@Dotum' },
    // { label: '@DotumChe', value: '@DotumChe' },
    // { label: '@Fixedsys', value: '@Fixedsys' },
    // { label: '@Gulim', value: '@Gulim' },
    // { label: '@GulimChe', value: '@GulimChe' },
    // { label: '@Gungsuh', value: '@Gungsuh' },
    // { label: '@GungsuhChe', value: '@GungsuhChe' },
    // { label: '@MingLiU', value: '@MingLiU' },
    // { label: '@MS Gothic', value: '@MS Gothic' },
    // { label: '@MS Mincho', value: '@MS Mincho' },
    // { label: '@MS PGothic', value: '@MS PGothic' },
    // { label: '@MS PMincho', value: '@MS PMincho' },
    // { label: '@MS UI Gothic', value: '@MS UI Gothic' },
    // { label: '@PMingLiU', value: '@PMingLiU' },
    // { label: '@System', value: '@System' },
    // { label: '@Terminal', value: '@Terminal' },
    // { label: '@方正舒体', value: '@方正舒体' },
    // { label: '@方正姚体', value: '@方正姚体' },
    // { label: '@仿宋_GB2312', value: '@仿宋_GB2312' },
    // { label: '@汉仪旗黑-55', value: '@汉仪旗黑-55' },
    // { label: '@汉仪旗黑-55S', value: '@汉仪旗黑-55S' },
    // { label: '@黑体', value: '@黑体' },
    // { label: '@华文彩云', value: '@华文彩云' },
    // { label: '@华文仿宋', value: '@华文仿宋' },
    // { label: '@华文琥珀', value: '@华文琥珀' },
    // { label: '@华文楷体', value: '@华文楷体' },
    // { label: '@华文隶书', value: '@华文隶书' },
    // { label: '@华文宋体', value: '@华文宋体' },
    // { label: '@华文细黑', value: '@华文细黑' },
    // { label: '@华文新魏', value: '@华文新魏' },
    // { label: '@华文行楷', value: '@华文行楷' },
    // { label: '@华文中宋', value: '@华文中宋' },
    // { label: '@楷体_GB2312', value: '@楷体_GB2312' },
    // { label: '@隶书', value: '@隶书' },
    // { label: '@宋体', value: '@宋体' },
    // { label: '@宋体-PUA', value: '@宋体-PUA' },
    // { label: '@微软雅黑', value: '@微软雅黑' },
    // { label: '@新宋体', value: '@新宋体' },
    // { label: '@幼圆', value: '@幼圆' },
    { label: 'Arial', value: 'Arial' },
    { label: 'Arial Black', value: 'Arial Black' },
    { label: 'Batang', value: 'Batang' },
    { label: 'BatangChe', value: 'BatangChe' },
    { label: 'Bitstream Vera Sans Mono', value: 'Bitstream Vera Sans Mono' },
    { label: 'Comic Sans MS', value: 'Comic Sans MS' },
    { label: 'Courier', value: 'Courier' },
    { label: 'Courier New', value: 'Courier New' },
    { label: 'Default', value: 'Default' },
    { label: 'Dotum', value: 'Dotum' },
    { label: 'DotumChe', value: 'DotumChe' },
    { label: 'Estrangelo Edessa', value: 'Estrangelo Edessa' },
    { label: 'Fixedsys', value: 'Fixedsys' },
    { label: 'Franklin Gothic Medium', value: 'Franklin Gothic Medium' },
    { label: 'Gautami', value: 'Gautami' },
    { label: 'Georgia', value: 'Georgia' },
    { label: 'Gulim', value: 'Gulim' },
    { label: 'GulimChe', value: 'GulimChe' },
    { label: 'Gungsuh', value: 'Gungsuh' },
    { label: 'GungsuhChe', value: 'GungsuhChe' },
    { label: 'Impact', value: 'Impact' },
    { label: 'Kartika', value: 'Kartika' },
    { label: 'Latha', value: 'Latha' },
    { label: 'Lucida Console', value: 'Lucida Console' },
    { label: 'Lucida Sans', value: 'Lucida Sans' },
    { label: 'Lucida Sans Unicode', value: 'Lucida Sans Unicode' },
    { label: 'Mangal', value: 'Mangal' },
    { label: 'Marlett', value: 'Marlett' },
    { label: 'Microsoft Sans Serif', value: 'Microsoft Sans Serif' },
    { label: 'MingLiU', value: 'MingLiU' },
    { label: 'Modern', value: 'Modern' },
    { label: 'MS Gothic', value: 'MS Gothic' },
    { label: 'MS Mincho', value: 'MS Mincho' },
    { label: 'MS PGothic', value: 'MS PGothic' },
    { label: 'MS PMincho', value: 'MS PMincho' },
    { label: 'MS Sans Serif', value: 'MS Sans Serif' },
    { label: 'MS Serif', value: 'MS Serif' },
    { label: 'MS UI Gothic', value: 'MS UI Gothic' },
    { label: 'MT Extra', value: 'MT Extra' },
    { label: 'MV Boli', value: 'MV Boli' },
    { label: 'Palatino Linotype', value: 'Palatino Linotype' },
    { label: 'PMingLiU', value: 'PMingLiU' },
    { label: 'Raavi', value: 'Raavi' },
    { label: 'Roman', value: 'Roman' },
    { label: 'Script', value: 'Script' },
    { label: 'Shruti', value: 'Shruti' },
    { label: 'Small Fonts', value: 'Small Fonts' },
    { label: 'Sylfaen', value: 'Sylfaen' },
    { label: 'Symbol', value: 'Symbol' },
    { label: 'System', value: 'System' },
    { label: 'Tahoma', value: 'Tahoma' },
    { label: 'TeamViewer12', value: 'TeamViewer12' },
    { label: 'Terminal', value: 'Terminal' },
    { label: 'Times New Roman', value: 'Times New Roman' },
    { label: 'Trebuchet MS', value: 'Trebuchet MS' },
    { label: 'Tunga', value: 'Tunga' },
    { label: 'Verdana', value: 'Verdana' },
    { label: 'Vrinda', value: 'Vrinda' },
    { label: 'Webdings', value: 'Webdings' },
    { label: 'Wingdings', value: 'Wingdings' },
    { label: 'WST_Czec', value: 'WST_Czec' },
    { label: 'WST_Engl', value: 'WST_Engl' },
    { label: 'WST_Fren', value: 'WST_Fren' },
    { label: 'WST_Germ', value: 'WST_Germ' },
    { label: 'WST_Ital', value: 'WST_Ital' },
    { label: 'WST_Span', value: 'WST_Span' },
    { label: 'WST_Swed', value: 'WST_Swed' },
    { label: i18n.t('pages.dictionary_Msg22'), value: i18n.t('pages.dictionary_Msg22') },
    { label: i18n.t('pages.dictionary_Msg23'), value: i18n.t('pages.dictionary_Msg23') },
    { label: i18n.t('pages.dictionary_Msg24'), value: i18n.t('pages.dictionary_Msg24') },
    { label: i18n.t('pages.dictionary_Msg25'), value: i18n.t('pages.dictionary_Msg25') },
    { label: i18n.t('pages.dictionary_Msg26'), value: i18n.t('pages.dictionary_Msg26') },
    { label: i18n.t('pages.dictionary_Msg27'), value: i18n.t('pages.dictionary_Msg27') },
    { label: i18n.t('pages.dictionary_Msg28'), value: i18n.t('pages.dictionary_Msg28') },
    { label: i18n.t('pages.dictionary_Msg29'), value: i18n.t('pages.dictionary_Msg29') },
    { label: i18n.t('pages.dictionary_Msg30'), value: i18n.t('pages.dictionary_Msg30') },
    { label: i18n.t('pages.dictionary_Msg31'), value: i18n.t('pages.dictionary_Msg31') },
    { label: i18n.t('pages.dictionary_Msg32'), value: i18n.t('pages.dictionary_Msg32') },
    { label: i18n.t('pages.dictionary_Msg33'), value: i18n.t('pages.dictionary_Msg33') },
    { label: i18n.t('pages.dictionary_Msg34'), value: i18n.t('pages.dictionary_Msg34') },
    { label: i18n.t('pages.dictionary_Msg35'), value: i18n.t('pages.dictionary_Msg35') },
    { label: i18n.t('pages.dictionary_Msg36'), value: i18n.t('pages.dictionary_Msg36') },
    { label: i18n.t('pages.dictionary_Msg37'), value: i18n.t('pages.dictionary_Msg37') },
    { label: i18n.t('pages.dictionary_Msg38'), value: i18n.t('pages.dictionary_Msg38') },
    { label: i18n.t('pages.dictionary_Msg39'), value: i18n.t('pages.dictionary_Msg39') },
    { label: i18n.t('pages.dictionary_Msg40'), value: i18n.t('pages.dictionary_Msg40') },
    { label: i18n.t('pages.dictionary_Msg41'), value: i18n.t('pages.dictionary_Msg41') },
    { label: i18n.t('pages.dictionary_Msg42'), value: i18n.t('pages.dictionary_Msg42') },
    { label: i18n.t('pages.dictionary_Msg43'), value: i18n.t('pages.dictionary_Msg43') },
    { label: i18n.t('pages.dictionary_Msg44'), value: i18n.t('pages.dictionary_Msg44') }
  ]
}

// 前端根据菜单限制终端类型的显示，这边添加至菜单路由meta
export function getOsTypeMap() {
  return {
    A22: 15, A5F: 7,
    B22: 7, B26: 7, B27: 7, B28: 7, B29: 7, B2A: 7, B2F: 7, B2G: 7, B2I: 7,
    B31: 15, B32: 7, B36: 7, B37: 7, B38: 7, B39: 7, B3A: 7, B3E: 7,
    B3L: 7, B42: 7, B43: 7, B44: 7, B46: 7, B47: 7,
    C25: 7, C26: 7, C27: 7, C28: 7, C32: 7, C35: 7,
    C42: 7, C43: 8, C54: 7, C63: 7, C64: 7, C66: 7, C73: 7, C75: 7, C83: 7,
    D27: 7, D28: 7, D29: 7, D2A: 7, D2C: 7, D2D: 7, D35: 7,
    D39: 7, D45: 7, D52: 7, D53: 7, D64: 7, D82: 7, D84: 7,
    E48: 7, E51: 7, E52: 7, E53: 7, E54: 7, E55: 7, E56: 7,
    H11: 7, H12: 7, H13: 7, E76: 7, E79: 7
  }
}
// 前端根据菜单限制 生效范围 {1终端、2操作员、3两者}，这边添加至菜单路由meta
// 公共树组件根据 meta.scope 的值决定是否显示
export function getUsedScopeMap() {
  return {
    A22: 1, A23: 1, A24: 1, A44: 1, A64: 3, A65: 3, A6A: 3, A6B: 3, A6C: 3, A66: 2,
    B25: 3, B2H: 3, B2K: 1, B2Q: 1, B32: 1, B37: 1, B38: 1, B39: 1, B3A: 1, B3E: 1, B3G: 1, B3H: 1, B3I: 1, B3J: 1,
    B31: 1, B33: 1, B42: 1, B45: 1, B4C: 3, B4D: 3, B4E: 3, B51: 3, B62: 3, B64: 1, B67: 1,
    C21: 3, C22: 3, C23: 3, C24: 3, C31: 3, C34: 3, C41: 3, C51: 3, C52: 3, C61: 3,
    C65: 3, C67: 3, C71: 3, C74: 3, C81: 3, C91: 3, C92: 1, C93: 1, C94: 1, CA1: 3, CA2: 1, CA3: 3,
    D21: 3, D22: 3, D2B: 3, D2E: 3, D2F: 3, D2G: 3, D2H: 3, D2I: 3, D31: 3, D32: 3, D41: 3, D42: 3,
    D44: 3, D46: 3, D51: 3, D61: 3, D65: 2, D66: 3, D71: 3, D81: 3, D83: 3,
    E3A: 3, E3B: 1, E3C: 2, E3D: 1, E3E: 3, E3F: 1, E22: 2, E23: 2, E24: 2, E25: 2,
    E26: 1, E27: 2, E33: 2, E34: 3, E35: 3, E36: 2, E3G: 1, E37: 1, E38: 2, E39: 3, E41: 2,
    E42: 2, E44: 2, E45: 2, E46: 2, E47: 2, E62: 2, E71: 3, E74: 3,
    F12: 3, F13: 3, F14: 3, F21: 3, F23: 3, F61: 3, F62: 3,
    G13: 2, G14: 2,
    H55: 3, H57: 3,
    122: 3, 132: 3,
    // 以下为日志菜单
    A5F: 3,
    B22: 1, B26: 1, B27: 1, B28: 1, B29: 1, B2A: 1, B2G: 1, B2I: 3, B2U: 1, B2T: 1, B2O: 1,
    B2R: 1, B36: 3, B63: 3, B44: 1, B43: 1, B47: 1, B46: 1, B66: 1, B75: 1, B68: 3,
    C25: 3, C26: 3, C27: 3, C28: 3, C32: 1, C35: 3, C42: 3, C43: 3, C54: 3,
    C63: 3, C64: 3, C66: 1, C68: 3, C73: 3, C75: 3, C83: 3, C95: 3, C90: 3,
    D27: 3, D28: 3, D29: 3, D2A: 3, D2C: 3, D2D: 3, D2J: 3, D35: 3, D39: 1,
    D40: 3, D45: 3, D52: 1, D53: 1, D64: 3, D74: 3, D75: 3, D82: 3, D84: 3,
    E51: 3, E53: 3, E52: 3, E77: 3, E78: 3, E54: 3, E48: 3, E56: 2, E55: 2,
    F16: 3, F22: 3,
    H11: 3, H12: 3, H56: 3, H58: 3, H76: 1, H91: 3,
    E76: 1, E79: 1, E90: 3,
    // 以下为报表菜单
    IAA: 2
  }
}
// 训练组件结果码映射
export function getCollectorResultDict() {
  return {
    '10030': i18n.t('pages.rule_mismatch'),
    '10031': i18n.t('pages.rule_match'),
    '10040': i18n.t('pages.rule_mismatch'),
    '10041': i18n.t('pages.rule_match'),
    '10050': i18n.t('pages.vml_Msg3'),
    '10051': i18n.t('pages.vml_Msg4'),
    '10060': i18n.t('pages.rule_mismatch'),
    '10061': i18n.t('pages.rule_match'),
    '201': i18n.t('pages.collector_result_201'),
    '40000': i18n.t('pages.collector_result_40000'),
    '40001': i18n.t('pages.collector_result_40001'),
    '40002': i18n.t('pages.collector_result_40002'),
    '40003': i18n.t('pages.collector_result_40003'),
    '40004': i18n.t('pages.collector_result_40004'),
    '40005': i18n.t('pages.collector_result_40005'),
    '40006': i18n.t('pages.collector_result_40006'),
    '40007': i18n.t('pages.collector_result_40007'),
    '40008': i18n.t('pages.collector_result_40008'),
    '40009': i18n.t('pages.collector_result_40009'),
    '40010': i18n.t('pages.collector_result_40010'),
    '4001001': i18n.t('pages.collector_result_4001001'),
    '4001002': i18n.t('pages.collector_result_4001002'),
    '4001003': i18n.t('pages.collector_result_4001003'),
    '4001004': i18n.t('pages.collector_result_4001004'),
    '4001005': i18n.t('pages.collector_result_4001005'),
    '40011': i18n.t('pages.collector_result_40011'),
    '40012': i18n.t('pages.collector_result_40012'),
    '40013': i18n.t('pages.collector_result_40013'),
    '49998': i18n.t('pages.collectionTimeout'),
    '49999': i18n.t('pages.collectionFailed')
  }
}
// 管理员出厂密码,经encryp.js的base64加密特殊方法
export function getFactoryPwd() {
  return {
    '': '2YW3RtaW4=0',                          // admin
    'admin': '1Q8WRtaW5AMjAwNg==2',            // 超管
    'sysadmin': '3c93lzQWRtaW5AMjAwNg==2',      // 系统管理员
    'secadmin': '2c82VjQWRtaW5AMjAwNg==3',      // 安全管理员
    'audadmin': '7YX1VkQWRtaW5AMjAwNg==9',      // 审计管理员
    'unlockadmin': '29dW5sb2NrQWRtaW5AMjAwNg==2', // 解锁管理员
    '1': '1Q8WRtaW5AMjAwNg==2',                // 超管
    '2': '3c93lzQWRtaW5AMjAwNg==2',             // 系统管理员
    '3': '2c82VjQWRtaW5AMjAwNg==3',             // 安全管理员
    '4': '7YX1VkQWRtaW5AMjAwNg==9'              // 审计管理员
  }
}
