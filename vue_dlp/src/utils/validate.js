import i18n from '@/lang'
import store from '@/store'
/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * 是否IPv4
 * \d       : 0 - 9
 * [1-9]\d  : 10 - 99
 * 1\d\d    : 100 - 199
 * 2[0-4]\d : 200 - 249
 * 25[0-5]  : 250 - 255
 * @param value 被验证的值
 * @returns {*|boolean} 返回 布尔值
 */
export function isIPv4(value) {
  const re = /^((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])$/
  return value && re.test(value)
}

/**
 * 是否IPv6
 * @param value
 * @returns {*|boolean}
 */
export function isIPv6(value) {
  const re = /^((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/
  return value && re.test(value)
}

/**
 * 是否端口
 * @param value
 * @returns {*|boolean}
 */
export function isPort(value) {
  return value && !isNaN(Number(value)) && Number(value) === parseInt(value) && value > 0 && value < 65536
}

export function isPort1(value) {
  return value && !isNaN(Number(value)) && Number(value) === parseInt(value) && value > 0 && value < 65536 && value.toString().indexOf('.') == -1
}

/**
 * 比较IP的大小
 * @param startIp
 * @param endIp
 * @returns {boolean}
 */
export function compareIP(startIp, endIp) {
  if (!startIp && !endIp) return 0
  if (!startIp) return -1
  if (!endIp) return 1
  const temp1 = startIp.split('.')
  const temp2 = endIp.split('.')
  for (var i = 0; i < 4; i++) {
    if (temp1[i].length == 1) temp1[i] = '00' + temp1[i]
    if (temp1[i].length == 2) temp1[i] = '0' + temp1[i]
    if (temp2[i].length == 1) temp2[i] = '00' + temp2[i]
    if (temp2[i].length == 2) temp2[i] = '0' + temp2[i]
  }
  return parseInt(temp1.join('')) - parseInt(temp2.join(''))
}

/**
 * 验证名称的唯一性
 * @param {Object} temp temp对象，包含id属性
 * @param {Function} getByName 传入api方法
 */
export function validUniqueName(temp, getByName) {
  return (rule, value, callback) => {
    getByName({
      name: value
    }).then(respond => {
      const data = respond.data
      if (data && data.id !== temp.id) {
        callback(new Error(i18n.t('valid.sameName')))
      } else {
        callback()
      }
    })
  }
}

export function ipValidator(rule, value, callback, errorMsg) {
  const re = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$|^(([a-zA-Z]|[a-zA-Z][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z]|[A-Za-z][A-Za-z0-9\-]*[A-Za-z0-9])$|^(?:(?:(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):){6})(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):(?:(?:[0-9a-fA-F]{1,4})))|(?:(?:(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))\.){3}(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])))))))|(?:(?:::(?:(?:(?:[0-9a-fA-F]{1,4})):){5})(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):(?:(?:[0-9a-fA-F]{1,4})))|(?:(?:(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))\.){3}(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])))))))|(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})))?::(?:(?:(?:[0-9a-fA-F]{1,4})):){4})(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):(?:(?:[0-9a-fA-F]{1,4})))|(?:(?:(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))\.){3}(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])))))))|(?:(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):){0,1}(?:(?:[0-9a-fA-F]{1,4})))?::(?:(?:(?:[0-9a-fA-F]{1,4})):){3})(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):(?:(?:[0-9a-fA-F]{1,4})))|(?:(?:(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))\.){3}(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])))))))|(?:(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):){0,2}(?:(?:[0-9a-fA-F]{1,4})))?::(?:(?:(?:[0-9a-fA-F]{1,4})):){2})(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):(?:(?:[0-9a-fA-F]{1,4})))|(?:(?:(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))\.){3}(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])))))))|(?:(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):){0,3}(?:(?:[0-9a-fA-F]{1,4})))?::(?:(?:[0-9a-fA-F]{1,4})):)(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):(?:(?:[0-9a-fA-F]{1,4})))|(?:(?:(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))\.){3}(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])))))))|(?:(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):){0,4}(?:(?:[0-9a-fA-F]{1,4})))?::)(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):(?:(?:[0-9a-fA-F]{1,4})))|(?:(?:(?:(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9]))\.){3}(?:(?:25[0-5]|(?:[1-9]|1[0-9]|2[0-4])?[0-9])))))))|(?:(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):){0,5}(?:(?:[0-9a-fA-F]{1,4})))?::)(?:(?:[0-9a-fA-F]{1,4})))|(?:(?:(?:(?:(?:(?:[0-9a-fA-F]{1,4})):){0,6}(?:(?:[0-9a-fA-F]{1,4})))?::))))$/
  if (!(value && re.test(value))) {
    callback(new Error(!errorMsg ? i18n.t('pages.serverLibrary_text4') : errorMsg))
  } else {
    callback()
  }
}
export function macValidator(rule, value, callback, errorMsg) {
  const re = /^[A-Fa-f0-9]+$/
  const val = value.split('-').join('')
  if (val.length != 12 || !re.test(val)) {
    callback(new Error(!errorMsg ? i18n.t('pages.ipMac_Msg7') : errorMsg))
  } else {
    callback()
  }
}
export function portValidator(rule, value, callback, errorMsg) {
  if (!isPort(value)) {
    callback(new Error(!errorMsg ? i18n.t('pages.validateMsg_PortError') : errorMsg))
  } else {
    callback()
  }
}
/**
 * 策略生效时间校验已选项已否存在（避免选中项被删除后还正常保存的情况）
 * @param {object} rule 验证规则
 * @param {*} value 验证字段的值
 * @param {function} callback 回调函数，‌用于通知验证结果
 * @param {*} param 传入另外的参数
 */
export function timeIdValidator(rule, value, callback, param) {
  const timeInfoValues = store.getters.timeOptions.map(op => op.value)
  if (value && !timeInfoValues.includes(value)) {
    // 如果传入的是方法，则将 false 传出去
    if (typeof param == 'function') param(false)
    callback(i18n.t('pages.validateMsg_optionDeleted'))
  } else {
    // 如果传入的是方法，则将 true 传出去
    if (typeof param == 'function') param(true)
    callback()
  }
}

/**
 * 新增策略表单验证
 * @param option----this.temp
 * @param $this---弹框this
 * @param $strategyDefType-----是否预定义策略（1/0 是/否）---预定义策略不需要判断非空
 * @returns {boolean}
 */
export function validatePolicy(option, $this, $strategyDefType) {
  // const entityType = option.entityType || option.objectType
  // 校验是否是回收站分组，回收站分组不允许配置策略
  if ((option.entityId && option.entityId == -2) || (option.objectGroupIds && (option.objectGroupIds.includes(-2) || option.objectGroupIds.includes('-2')))) {
    $this.$message({
      message: i18n.t('components.recycleNodeTips'),
      type: 'error',
      duration: 2000
    })
    return false
  }
  // if ((isEmpty(entityType) || isEmpty(option.entityId) && isEmpty(option.objectIds) && isEmpty(option.objectGroupIds) && option.entityId != 0) && $strategyDefType != 1) {
  //   $this.$message({
  //     message: i18n.t('components.chooseApplicationObj'),
  //     type: 'error',
  //     duration: 2000
  //   })
  //   return false
  // }
  // 策略应用对象的校验改到后台：因为子管理员的权限范围较低时，界面可以置空应用对象，由后台判断
  return true
}

/**
 * 密码验证
 * @param oldPassword-----原始密码，用于判断原始密码和修改密码是否一致
 * @param password-----当前密码
 * @returns {data}
 */
export function validatePassword(oldPassword, password, data) {
  const short = data.passwordLength.split('-')[0]
  const long = data.passwordLength.split('-')[1]
  // 匹配空格
  const space = /^[\s]*$/
  // 弱密码：含数字、字母、特殊字符其中一种
  const weakReg = /^[0-9]+$|^[A-Za-z]+$|^[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]+$/
  // 中密码：含数字、字母、特殊字符两种及以上
  const mid = /^(?![0-9]+$)(?![a-zA-Z]+$)(?!([^(0-9a-zA-Z)]|[\(\)])+$)([^(0-9a-zA-Z)]|[\(\)]|[a-zA-Z]|[0-9])+$/
  // 强密码：含数字、字母、特殊字符(需包含_)三种
  const strongReg = /^(?:(?=.*[0-9].*)(?=.*[A-Za-z].*)(?=.*[\W_].*))[\W_0-9A-Za-z]+$/
  if (password.length) {
    if (short && long) {
      if (password.length >= short && password.length <= long) {
        data.successLength = true
        data.failLength = false
      } else {
        data.successLength = false
        data.failLength = true
      }
    }
    if (data.passwordLevel == 1 && (password.match(weakReg) || password.match(mid))) {
      data.successLevel = true
      data.failLevel = false
    } else if (data.passwordLevel == 2 && password.match(mid)) {
      data.successLevel = true
      data.failLevel = false
    } else if (data.passwordLevel == 3 && password.match(strongReg)) {
      data.successLevel = true
      data.failLevel = false
    } else {
      data.successLevel = false
      data.failLevel = true
    }
    if (password.match(space)) {
      data.failLength = false
      data.successLength = true
      data.failLevel = false
      data.successLevel = true
    }
    if (oldPassword !== undefined) {
      if (password == oldPassword) {
        data.failPassword = true
        data.successPassword = false
      } else {
        data.failPassword = false
        data.successPassword = true
      }
    }
  } else {
    data.successLength = false
    data.failLength = false
    data.successLevel = false
    data.failLevel = false
    data.failPassword = false
    data.successPassword = false
  }
  return data
}

/**
 * 判断是否当前密码是否满足配置的密码强度等级要求
 * @param password-----密码
 * @param level----配置的密码等级
 * @param forceValiPwdLevel----是否校验当前密码等级是否符合要求
 * @returns {boolean}
 */
export function isMatchPasswordLevel(password, level, forceValiPwdLevel) {
  // 匹配空格
  const space = /^[\s]*$/
  // 弱密码：含数字、字母、特殊字符其中一种
  const weakReg = /^[0-9]+$|^[A-Za-z]+$|^[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]+$/
  // 中密码：含数字、字母、特殊字符两种及以上
  const mid = /^(?![0-9]+$)(?![a-zA-Z]+$)(?!([^(0-9a-zA-Z)]|[\(\)])+$)([^(0-9a-zA-Z)]|[\(\)]|[a-zA-Z]|[0-9])+$/
  // 强密码：含数字、字母、特殊字符(需包含_)三种
  const strongReg = /^(?:(?=.*[0-9].*)(?=.*[A-Za-z].*)(?=.*[\W_].*))[\W_0-9A-Za-z]+$/
  if ((level == 1 && (password.match(weakReg) || password.match(mid) || password.match(strongReg))) || (level == 2 && (password.match(mid) || password.match(strongReg))) || (level == 3 && password.match(strongReg)) || password.match(space)) {
    return true
  }
  return false
}

/**
 * 判断是否空数据或空数组
 * @param val
 * @returns {boolean}
 */
export function isEmpty(val) {
  return val === undefined || val === null || val === '' || Array.isArray(val) && val.length === 0
}
