import store from '../store'
import i18n from '@/lang'
import request from '@/utils/request'

/**
 * 判断是否空数据或空数组
 * @param val
 * @returns {boolean}
 */
export function isEmpty(val) {
  return val === undefined || val === null || val === '' || Array.isArray(val) && val.length === 0
}

/**
 * 将时间解析为字符串
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/(y|m|d|h|i|s|a)+/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return [
        i18n.t('pages.sunday'),
        i18n.t('pages.monday'),
        i18n.t('pages.tuesday'),
        i18n.t('pages.wednesday'),
        i18n.t('pages.Thursday'),
        i18n.t('pages.friday'),
        i18n.t('pages.saturday')
      ][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return i18n.t('pages.just')
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + i18n.t('pages.minutesAgo')
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + i18n.t('pages.hoursBefore')
  } else if (diff < 3600 * 24 * 2) {
    return i18n.t('pages.oneDayBefore')
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      i18n.t('text.month') +
      d.getDate() +
      i18n.t('text.day') +
      d.getHours() +
      i18n.t('text.hour') +
      d.getMinutes() +
      i18n.t('text.minute')
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, ' ') +
      '"}'
  )
}
/**
 * 防抖函数 debounce 指的是某个函数在某段时间内，无论触发了多少次回调，都只执行最后一次
 * @param {function} func 执行的函数
 * @param {Number} wait 间隔的时长
 * @param {Boolean} immediate 第一次是否立即执行
 * @returns 返回新的函数
 */
export function debounce(func, wait, immediate) {
  let timeout, context, timestamp, result

  const later = function(context, args) {
    // 距上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(() => later(context, args), wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(() => later(context, args), wait)
    if (callNow) {
      result = func.apply(context, args)
      context = null
    }

    return result
  }
}

/**
 * 判断策略是否可新增的方法
 * @param {Object} rowData gridTable的行数据
 * @param {Object} vm 组件实例，this
 */
export function enableStgBtn(rowData, vm) {
  if (!vm.query.objectType && vm.treeable) {
    vm.addBtnAble = false
    return
  }
  vm.addBtnAble = true
  // vm.addBtnAble = !!vm.query.objectType
  // rowData.forEach(row => {
  //   if (row.entityType == vm.query.objectType && row.entityId == vm.query.objectId) {
  //     vm.addBtnAble = false
  //   }
  // })
}

/**
 * 判断策略是否可删除的方法
 * @param {Object} rowData gridTable的行数据
 * @param {Object} vm 组件实例，this
 */
export function enableStgDelete(rowData, vm) {
  vm.deleteable = rowData.length > 0
  // if (rowData.length > 0 && vm.query.strategyDefType == 1) { // 预定义策略
  //   vm.deleteable = true
  // } else if (rowData.length == 1 && vm.query.objectType) {
  //   const { entityType, entityId } = rowData[0]
  //   const { objectType, objectId } = vm.query
  //   vm.deleteable = entityType === objectType && entityId === objectId
  // } else {
  //   vm.deleteable = false
  // }
}

/**
 * 判断策略行数据CheckBox是否可勾选
 * @param {Object} row gridTable的行数据
 * @param {*} index
 */
export function selectable(row, index, vm) {
  const tree = getStgTargetTree(vm, row.entityType || row.objectType)
  if (vm && !tree) {
    // 如果树不存在，一般是以为兼容问题才会出现树不存在，例如：开始策略分配给操作员，后续禁止分配给操作员
    return true
  }
  return !row.entityName || row.entityName.indexOf(i18n.t('pages.self')) > -1 || isEmpty(row.entityType) && isEmpty(row.entityId) && isEmpty(row.objectType) && isEmpty(row.objectIds) && isEmpty(row.objectGroupIds)
}
/**
 * 判断策略是否应用给指定对象
 * @param {Object} stg 策略数据
 * @param {Number} objType 应用对象类型
 * @param {Number} objId   应用对象ID
 * @param {*} index
 */
export function containStgObject(stg, objType, objId) {
  if (isEmpty(stg) || isEmpty(objType) || isEmpty(objId)) {
    return false
  }
  if (!isEmpty(stg.entityType) && !isEmpty(stg.entityId)) {
    return stg.entityType == objType && stg.entityId == objId
  }
  const entityType = stg.entityType || stg.objectType
  const isUOrG4Entity = entityType == 2 || entityType == 4
  const isUOrG4Obj = objType == 2 || objType == 4
  if (isUOrG4Entity !== isUOrG4Obj) {
    return false
  }
  const isG = objType == 3 || objType == 4
  const objIds = isG ? stg.objectGroupIds : stg.objectIds
  if (isEmpty(objIds)) {
    return false
  }
  // 使用相等比较，避免类型不匹配（string=number）
  for (let i = 0; i < objIds.length; i++) {
    if (objIds[i] == objId) {
      return true
    }
  }
  return false
}

/**
 * 判断时间戳是否变更（根据存储在当前组件中的历史时间戳，和最新的时间戳进行比较）
 * @param component    当前页面组件
 * @param path         对比路由路径
 * @returns {boolean|*}
 */
export function isSameTimestamp(component, path) {
  // 已保存的时间戳
  const viewTimestamp = component.viewTimestamp
  if (viewTimestamp) {
    return viewTimestamp[path] === store.getters.viewTimestamp[path]
  }
  return false
}

/**
 * 将当前时间戳拷贝存储在组件中，为isSameTimestamp进行时间戳对比提供数据
 * @param component
 */
export function initTimestamp(component) {
  component.viewTimestamp = Object.assign({}, store.getters.viewTimestamp)
}

/**
 * 策略页面来源信息格式化（统一放这边，以后改动比较好改）
 * @param row
 * @param data
 * @param vm
 * @returns {*}
 */
export function objectFormatter(row, data, vm) {
  return row.entityName
}

/**
 * 获取策略应用树
 * @param vm
 * @param entityType
 * @returns {null|*}
 */
export function getStgTargetTree(vm, entityType) {
  if (!vm || !entityType) {
    return null
  }
  const tabName = { 1: 'terminal', 2: 'user', 3: 'terminal', 4: 'user' }[entityType]
  // vm传入的如果是策略树，则直接使用vm
  const isVmTree = vm.$vnode.data.ref == 'strategyTargetTree'
  // 先从页面中获取左侧树实例，获取不到时，从公共组件获取
  const stTree = isVmTree ? vm : vm.$refs['strategyTargetTree'] || vm.$refs['terminalTree'] || store.getters.commonVm.strategyTargetTree
  // todo 策略树如果作为唯一组件，则使用 commonVm 获取实例
  // const stTree = vm.$store.getters.commonVm['strategyTargetTree']
  return stTree && stTree.$refs[tabName]
}

/**
 * 策略页面 点击来源左侧树自动选中对应节点，并刷新列表数据
 * @param {*} row 行数据
 * @param {*} data
 * @param {*} vm
 */
export function entityLink(row, data, vm) {
  // 点击来源为自身，则直接返回
  if (row.entityName === i18n.t('pages.self')) return

  // 根据 entityType 获取 tabName
  const entityType = row.entityType || row.objectType
  const tabName = { 1: 'terminal', 2: 'user', 3: 'terminal', 4: 'user' }[entityType]

  // vm传入的如果是策略树，则直接使用vm
  const isVmTree = vm.$vnode.data.ref == 'strategyTargetTree'
  // 先从页面中获取左侧树实例，获取不到时，从公共组件获取
  const stTree = isVmTree ? vm : vm.$refs['strategyTargetTree'] || vm.$refs['terminalTree'] || vm.$refs['objectTree'] || store.getters.commonVm.strategyTargetTree
  console.log('stTree', stTree, store.getters.commonVm.strategyTargetTree)

  if (!stTree || !stTree.$refs[tabName]) {
    console.warn('strategyTargetTree is not exist')
    // 如果树不存在，则直接返回，一般是因为兼容问题才会出现树不存在，例如：开始策略分配给操作员，后续禁止分配给操作员
    return
  }
  // 所有树节点的数组
  const list = stTree.$refs[tabName][0].getSearchList()
  // 根据 objectIds 和 objectGroupIds，获取应用节点的id map
  const objectIdMap = row.objectIds ? row.objectIds.reduce((map, id) => { map[id] = true; return map }, {}) : {}
  const objectGroupIdMap = row.objectGroupIds ? row.objectGroupIds.reduce((map, id) => { map[id] = true; return map }, {}) : {}
  // 兼容使用 entityId 记录应用节点的数据
  const isGroup = entityType == 3 || entityType == 4
  if (row.entityId) {
    isGroup ? objectGroupIdMap[row.entityId] = true : objectIdMap[row.entityId] = true
  }
  // 记录应用对象的数量
  let objectNum = Object.keys(objectIdMap).concat(Object.keys(objectGroupIdMap)).length
  // 选中的节点数组
  const nodes = []
  for (let i = 0; i < list.length; i++) {
    // objectNum 为 0 时，说明找到所有节点了，结束遍历
    if (objectNum == 0) break
    const node = list[i];
    const isG = node.dataType === 'G'
    if ((isG && objectGroupIdMap[node.dataId]) || (!isG && objectIdMap[node.dataId])) {
      // 选中的节点添加到 nodes
      nodes.push(node)
      objectNum--
    }
  }

  // 切换tab
  stTree.checkedTab = tabName
  // 当前选中的节点
  const currentNode = stTree.$refs[tabName][0].tree().getCurrentNode()
  // 当前选中的树节点是否是策略应用的对象之一
  const isNodeSelected = currentNode && nodes.findIndex(node => node.dataId == currentNode.dataId) != -1

  // 如果策略来自策略包，同时当前点击树节点是策略的应用对象，那么跳转到策略包界面
  if (row.stgGroupId && isNodeSelected) {
    // todo 备注，此处 stgGroupId 可能不是当前节点对应的策略包的 id，导致跳转后显示的策略包数据不准确，需要后台做修改，返回准确的 stgGroupId
    request({ method: 'get', url: '/stgGroup/get/' + row.stgGroupId }).then(resp => {
      const { name, groupType } = resp.data
      // 1: 数据安全策略包 2: 终端行为策略包 4: 网络行为策略包
      const routerName = { 1: 'EncryptionGroup', 2: 'BehaviorGroup', 4: 'NetGroup' }[groupType]
      const code = { 1: 'E11', 2: 'C11', 4: 'D11' }[groupType]

      if (vm.hasPermission(code)) {
        const searchInfo = name
        vm.$router.push({ name: routerName, params: { searchInfo }})
      }
    })
  } else {
    // 选中新节点，默认第一个
    const node = nodes[0]
    node && stTree.$refs[tabName][0].selectSearch(node)
  }
}
/**
 *  刷新页面，重置左侧树的状态，重新加载表格数据
 * @param {*} vm 传入当前页面的vue实例
 */
export function refreshPage(vm) {
  // 先从页面中获取左侧树实例，获取不到时，从公共组件获取
  const stTree = vm.$refs.strategyTargetTree || store.getters.commonVm.strategyTargetTree
  if (stTree) {
    // 清除选中节点
    const currentTree = stTree.$refs[stTree.checkedTab][0]
    currentTree.clearSelectedNode()
    // 清除记录的选中节点
    const tabName = stTree.checkedTab
    const checkedNode = null
    const checkedNodeMap = store.getters.stgTargetCheckedNodeMap
    checkedNodeMap[vm.$route.path] = { tabName, checkedNode }
    store.dispatch('tagsView/setStgTargetCheckedNodeMap', checkedNodeMap)
    // 触发节点变化方法
    vm.strategyTargetNodeChange(stTree.checkedTab, null)
  } else {
    console.error('树组件不存在！');
  }
}

export function buttonFormatter(row, vm) {
  if (vm.hasOwnProperty('treeable') && !vm.treeable) {
    return i18n.t('text.edit')
  }
  if (isEmpty(row.entityId) && isEmpty(row.objectIds) && isEmpty(row.objectGroupIds)) {
    // 如果策略应用对象为空，则允许直接修改；
    return i18n.t('text.edit')
  }
  const tree = getStgTargetTree(vm, row.entityType || row.objectType)
  if (!tree) {
    // 如果树不存在，一般是以为兼容问题才会出现树不存在，例如：开始策略分配给操作员，后续禁止分配给操作员
    return i18n.t('text.edit')
  }
  if (vm.query.objectId === undefined) {
    return ''
  }
  if (row.strategyDefType != 1) {
    if (row.entityId == vm.query.objectId && row.entityType == vm.query.objectType) {
      return i18n.t('text.edit')
    }
    const isG = vm.query.objectType == 3 || vm.query.objectType == 4
    if (!isG && row.objectIds && row.objectIds.indexOf(Number.parseInt(vm.query.objectId)) > -1) {
      return i18n.t('text.edit')
    } else if (isG && row.objectGroupIds && row.objectGroupIds.indexOf(Number.parseInt(vm.query.objectId)) > -1) {
      return i18n.t('text.edit')
    }
  }
  return ''
}
// 是否隐藏启用字段和策略来源对象
export function hiddenActiveAndEntity(colModel, treeable) {
  colModel.forEach(col => {
    if (col.prop == 'name' && col.iconFormatter && !treeable) {
      col.iconFormatter = null
    }
    if (col.prop == 'active' && col.type == 'icon') {
      col.hidden = !treeable
    }
    if (col.prop == 'entityName') {
      col.hidden = !treeable
    }
    if (col.prop == 'entityId') {
      col.hidden = !treeable
    }
  })
}

/**
 * 定义一个深拷贝函数  接收目标target参数
 * @param {*} target 目标
 */
export function deepClone(target) {
  // 定义一个变量
  let result;
  // 如果当前需要深拷贝的是一个对象的话
  if (typeof target === 'object') {
    // 如果是一个数组的话
    if (Array.isArray(target)) {
      result = []; // 将result赋值为一个数组，并且执行遍历
      for (let i = 0; i < target.length; i++) {
        // 递归克隆数组中的每一项
        result.push(deepClone(target[i]))
      }
      // 判断如果当前的值是null的话；直接赋值为null
    } else if (target === null) {
      result = null;
      // 判断如果当前的值是一个RegExp对象的话，直接赋值
    } else if (target.constructor === RegExp) {
      result = target;
    } else {
      // 否则是普通对象，直接for in循环，递归赋值对象的所有值
      result = {};
      for (const i in target) {
        result[i] = deepClone(target[i]);
      }
    }
    // 如果不是对象的话，就是基本数据类型，那么直接赋值
  } else {
    result = target;
  }
  // 返回最终结果
  return result;
}

/**
 * 将两个对象(数组)深度合并
 * @param {Object} target 合并的目标对象，该对象会被改变
 * @param {Object} sources 合并的来源对象
 */
export function deepMerge(target, sources) {
  if (sources == null) {
    return target
  }
  const typeOption = ['[object Object]', '[object Array]']
  const targetType = Object.prototype.toString.call(target)
  const sourcesType = Object.prototype.toString.call(sources)
  if (!(targetType === sourcesType && typeOption.includes(targetType))) {
    throw new Error('Parameter type error')
  }
  // 遍历来源对象的属性
  for (const key in sources) {
    const targetType = Object.prototype.toString.call(target[key])
    const sourcesType = Object.prototype.toString.call(sources[key])
    // 当target[key]与sources[key]类型相同 且是对象或数组
    if (targetType === sourcesType && typeOption.includes(targetType)) {
      // 数组，删除多余的元素
      if (targetType === '[object Array]') {
        target[key].splice(sources[key].length)
      }
      // 合并
      target[key] = deepMerge(target[key], sources[key])
    } else {
      // 将sources[key]深拷贝，并赋值给target[key]
      target[key] = deepClone(sources[key])
    }
  }
  return target
}

export function toRGB(a) {
  // 十六进制颜色值的转为RBG
  var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
  var sColor = a.toLowerCase()
  if (sColor && reg.test(sColor)) {
    if (sColor.length === 4) {
      var sColorNew = '#'
      for (var i = 1; i < 4; i += 1) {
        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
      }
      sColor = sColorNew
    }
    // 处理六位的颜色值
    var sColorChange = []
    for (let i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
    }
    return sColorChange
  }
  return []
}

export function utc2Beijing(time) {
  var date = new Date(time)
  var year = date.getFullYear()
  /* 在日期格式中，月份是从0开始的，因此要加0
     * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
     * */
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  // 拼接
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}

// JS大小转化B KB MB GB的转化方法
export function convert(limit) {
  var size = ''
  if (limit < 1024) { // 如果小于1MB转化成KB
    size = limit.toFixed(2) + 'KB'
  } else if (limit < 1024 * 1024) { // 如果小于1GB转化成MB
    size = (limit / 1024).toFixed(2) + 'MB'
  } else if (limit < 1024 * 1024 * 1024) { // 如果小于1TB转化成GB
    size = (limit / 1024 / 1024).toFixed(2) + 'GB'
  } else { // 其他转化成TB
    size = (limit / (1024 * 1024 * 1024)).toFixed(2) + 'TB'
  }

  var sizestr = size + ''
  var len = sizestr.indexOf('\.')
  var dec = sizestr.substr(len + 1, 2)
  if (dec == '00') { // 当小数点后为00时 去掉小数部分
    return sizestr.substring(0, len) + sizestr.substr(len + 3, 2)
  }
  return sizestr
}

// limit 值单位为 B。同时向上转换，比如1.0111，保持2个小数点，则转换成1.02
export function convertFileSize(limit) {
  const toCeilFixed = (n) => {
    return Math.ceil((n * 100)) / 100
  }
  let size = ''
  if (limit < 1024) { // 如果小于1KB
    size = toCeilFixed(limit) + 'B'
  } else if (limit < Math.pow(1024, 2)) { // 如果小于1MB
    size = toCeilFixed(limit / 1024) + 'KB'
  } else if (limit < Math.pow(1024, 3)) { // 如果小于1GB
    size = toCeilFixed(limit / Math.pow(1024, 2)) + 'MB'
  } else if (limit < Math.pow(1024, 4)) { // 如果小于1TB
    size = toCeilFixed(limit / Math.pow(1024, 3)) + 'GB'
  } else { // 其他转化成TB
    size = toCeilFixed(limit / Math.pow(1024, 4)) + 'TB'
  }
  return size
}

// JS把秒转为时分秒方法
export function formatSeconds(value) {
  const result = parseInt(value)
  const h = Math.floor(result / 3600)
  const m = Math.floor((result / 60 % 60))
  const s = Math.floor((result % 60))

  let res = ''
  if (h !== 0) res += `${h}` + i18n.t('text.hour1')
  if (m !== 0) res += `${m}` + i18n.t('text.minute1')
  res += `${s}` + i18n.t('text.second1')
  return res
}

// JS把秒转为时分秒方法
export function formatSeconds2(value) {
  const result = parseInt(value)
  const h = Math.floor(result / 3600)
  const m = Math.floor((result / 60 % 60))
  const s = Math.floor((result % 60))

  let res = ''
  if (h !== 0) res += `${h}` + 'h'
  if (m !== 0) res += `${m}` + 'min'
  res += `${s}` + 's'
  return res
}

// 获取URL中的参数，返回 {key：value}
export function getUrlParams(url) {
  const params = {}
  if (url.indexOf('?') > -1) {
    const paraArr = url.split('?').pop().split('&')
    paraArr.forEach(para => {
      para = para.split('=')
      params[para[0]] = para[1]
    })
  }
  return params
}
/** 单例调用方法，避免一个方法短期内被重复调用*/
export function callSingle(vm, func) {
  if (!func) {
    return
  }
  if (!vm.funcLoadStatus) {
    vm.funcLoadStatus = {}
  }
  const funcName = !func.name ? func.toString() : func.name
  if (vm.funcLoadStatus[funcName]) {
    console.warn('exist func :' + funcName)
    return;
  }
  vm.funcLoadStatus[funcName] = 1
  window.setTimeout(() => {
    if (vm.funcLoadStatus[funcName]) {
      func()
      delete vm.funcLoadStatus[funcName]
    }
    // timeout字段添加一个随机数，降低重复的概率
  }, 300 + Math.floor(Math.random() * 100))
}

/**
 * 格式化文件大小, 输出成带单位的字符串
 * @param {Number} size 文件大小
 * @param {Number} precision 精确到的小数点位数
 * @returns string 格式化后带单位的文件大小
 */
export function formatFileSize(size, precision = 2) {
  if (isNaN(size)) {
    return '-'
  }
  if (size === 0) {
    return '0'
  }
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let unit;
  while ((unit = units.shift()) && Math.round(size) >= 1024) {
    size = size / 1024;
  }
  return (size === Math.round(size) ? size : size.toFixed(precision)) + unit;
}
/**
 * 格式化数量，超过一万时，单位加上万
 * @param {Number} count 数量
 * @param {Number} precision 精确到的小数点位数
 * @returns string 格式化后带单位的数量
 */
export function formatCount(count, precision = 2) {
  if (isNaN(count)) {
    return '-'
  }
  if (count === 0) {
    return '0'
  }
  if (count < 10000) {
    return count
  } else {
    return (count / 10000).toFixed(precision) + '万'
  }
}

/**
 * 是否内网IP
 */
export function isIntranetIp(ip) {
  if (typeof ip !== 'string' || ip.length < 7) {
    return false
  }
  const addr = ip.split('.')
  if (addr.length !== 4) {
    return false
  }
  const b0 = parseInt(addr[0])
  const b1 = parseInt(addr[1])
  // 10.x.xx/8
  const SECTION_1 = 0x0A
  // 172.16.x.x/12
  const SECTION_2 = 0xAC
  const SECTION_3 = 0x10
  const SECTION_4 = 0x1F
  // 192.168.x.x/16
  const SECTION_5 = 0xC0
  const SECTION_6 = 0xA8
  switch (b0) {
    case SECTION_1:
      return true
    case SECTION_2:
      return b1 >= SECTION_3 && b1 <= SECTION_4
    case SECTION_5:
      return b1 === SECTION_6
    default:
      return false
  }
}

/**
 * 判断两个对象是否相等
 * @param obj1
 * @param obj2
 * @returns {boolean}
 */
export function equalsObj(obj1, obj2) {
  if (typeof obj1 !== typeof obj2) {
    return false
  } else if (!obj1 && !obj2) {
    return true
  } else if (obj1 && obj2) {
    if (obj1 === obj2) {
      return true
    }
    if (typeof obj1 !== 'object') {
      return false
    }
    if (Object.keys(obj1).length !== Object.keys(obj2).length) {
      return false
    }
    for (const key in obj1) {
      const val1 = obj1[key]
      const val2 = obj2[key]
      if (typeof val1 == 'undefined') {
        if (typeof val1 != 'undefined') {
          return false
        }
      } else if (!equalsObj(val1, val2)) {
        return false
      }
    }
    return true
  }
  return false
}

/**
 * 选中HTML元素的文字
 * @param el HTML元素
 */
export function selectText(el) {
  const selection = window.getSelection()
  selection.removeAllRanges()
  const range = document.createRange()
  range.selectNodeContents(el)
  selection.addRange(range)
}

/**
 * 取消选中文字
 */
export function cancelSelectText() {
  window.getSelection().removeAllRanges()
}

/**
 * 时间轴
 * @param option
 */
export function timeAxis(option) {
  if (!option) {
    return
  }
  option.yAxis = [{
    name: i18n.t('components.time')
  }]
  option.tooltip = {
    formatter: (d) => {
      let res = d[0].axisValueLabel.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '<br>'
      for (let i = 0; i < d.length; i++) {
        const value = formatSeconds(d[i].data)
        res += d[i].seriesName + ' : ' + value + '<br>'
      }
      return res
    }
  }
}

/**
 * 流量轴
 * @param option
 */
export function flowAxis(option) {
  if (!option) {
    return
  }
  option.yAxis = [{
    name: i18n.t('components.traffic')
  }]
  option.tooltip = {
    formatter: (d) => {
      let res = d[0].axisValueLabel.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '<br>'
      for (let i = 0; i < d.length; i++) {
        const value = convert(d[i].data)
        res += d[i].seriesName + ' : ' + value + '<br>'
      }
      return res
    }
  }
}

/**
 *  读取小端byte数组为short
 * @param b
 * @return
 */
export function bytes2ShortLittle(b) {
  return (((b[1] << 8) | b[0] & 0xff));
}
/**
 * 小端的方式把字节转int
 * @param bytes
 * @return
 */
export function bytes2IntLittle(bytes) {
  const int1 = bytes[0] & 0xff;
  const int2 = (bytes[1] & 0xff) << 8;
  const int3 = (bytes[2] & 0xff) << 16;
  const int4 = (bytes[3] & 0xff) << 24;
  return int1 | int2 | int3 | int4;
}

/**
 * byte数组转base64图片
 * @param buffer
 * @returns {string}
 */
export function arrayBufferToBase64(buffer) {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary)
}

/**
 * 获取服务端时间
 */
export function getCurrentServerTime() {
  const st = store.getters.serverTime
  const bt = store.getters.browserTime
  // console.log('serverTime=' + st + ', browserTime=' + bt + ', curTime=' + new Date().getTime())
  if (st && bt) {
    return st + new Date().getTime() - bt
  }
  return null
}

/**
 *  将ipV4地址转换为Int类型
 *  例如：*********** 转换为 192168001001
 * @param ip
 * @returns {bigint}
 */
export function ipv4ToInt(ip) {
  const parts = ip.split('.');
  let partStr = '';
  parts.forEach((part, index) => {
    while (part.length < 3) {
      part = '0' + part;
    }
    partStr += part;
  })
  return parseInt(partStr);
}
