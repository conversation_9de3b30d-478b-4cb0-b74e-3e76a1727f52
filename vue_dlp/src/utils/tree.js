import i18n from '@/lang'

/**
 * 给指定树添加回收站节点
 * @param tree                    指定树
 * @param recycleChildrenNodes    回收站节点的子节点，可以为null
 */
export function addRecycleNode(tree, recycleChildrenNodes) {
  const recycleNode = {
    id: 'G-2',
    label: i18n.t('pages.offline_terminal_text15'),
    type: 'G',
    dataType: 'G',
    children: recycleChildrenNodes,
    dataCode: '',
    dataId: '-2',
    disabled: true,
    parentId: 'G0'
  }
  // 如果存在G0节点，则将回收站节点放到根节点下
  const treeData = (tree.length > 0 && tree[0].id === 'G0') ? tree[0].children : tree
  const recycleNodeIndex = treeData.findIndex(item => item.id == 'G-2')
  // 如果已存在回收站节点，则替换，否则添加到末尾
  recycleNodeIndex > -1 ? treeData.splice(recycleNodeIndex, 1, recycleNode) : treeData.push(recycleNode)
  return tree
}
/**
 * 通过指定属性的值查找树节点数据
 * @param {Array} nodes 树数据
 * @param {*} keyValue 查找的值
 * @param {*} keyField 查找的属性
 * @returns 节点数据
 */
export function findNode(nodes, keyValue, keyField) {
  if (nodes && nodes.length > 0) {
    for (const i in nodes) {
      const nodei = nodes[i]
      if (!nodei) {
        return null
      } else if (nodei[keyField] + '' === keyValue + '') {
        return nodei
      } else {
        const temp = findNode(nodei.children, keyValue, keyField)
        if (temp) return temp
      }
    }
  }
  return null
}

/**
 * 通过指定属性的值查找树节点的label
 * @param {Array} nodes 树数据
 * @param {*} keyValue 查找的值
 * @param {*} keyField 查找的属性
 * @returns 节点数据
 */
export function findNodeLabel(nodes, keyValue, keyField) {
  const node = findNode(nodes, keyValue, keyField)
  return node ? node.label : null
}

/**
 * 获取所有子节点指定属性 keyField 的值
 * @param {Array、Object} nodes 树数据
 * @param {*} keyField 指定属性
 * @returns array
 */
export function toNodeValue(nodes, keyField) {
  const result = []
  if (nodes) {
    nodes = Array.isArray(nodes) ? nodes : [nodes]
    for (const i in nodes) {
      const nodei = nodes[i]
      result.push(nodei[keyField])
      result.splice(0, 0, ...toNodeValue(nodei.children, keyField))
    }
  }
  return result
}

/**
 * 树结构数组转成平级数组
 * @param {Array} treeDatas 树结构部门数据
 * @param {Array} lists 存储部门数据的数组，数据不包含子节点
 * @param {Object} idMap 存储部门id和子分组id的对象
 */
export function treeDatasToLists(treeDatas, lists = [], idMap = {}) {
  for (let i = 0; i < treeDatas.length; i++) {
    const treeNode = JSON.parse(JSON.stringify(treeDatas[i]))
    const childNodes = treeNode.children
    delete treeNode.children
    const { id, parentId } = treeNode
    if (id && !idMap[id]) {
      idMap[id] = []
      lists.push(treeNode)
    }
    if (idMap[parentId]) {
      idMap[parentId].push(id)
    } else {
      idMap[parentId] = [id]
    }
    if (childNodes) {
      treeDatasToLists(childNodes, lists, idMap)
    }
  }
  return { lists, idMap }
}

/**
 * 集合数组转换成树节点数据：即子节点数据添加到父节点的children属性中
 * @param {Array} lists 存储部门数据的数组，数据不包含子节点
 * @param {Object} nodeKey 节点的唯一标识符的属性名
 * @param {Object} parentKey 父级节点的唯一标识符的属性名
 */
export function listToTreeData(lists, nodeKey, parentKey) {
  const dataMap = {}
  // 构造Map：<parentId, [item]>，方便后续快速获取子节点
  lists.forEach(item => {
    const parentId = item[parentKey]
    if (dataMap[parentId]) {
      dataMap[parentId].push(item)
    } else {
      dataMap[parentId] = [item]
    }
  })
  return mapToTreeData(dataMap, nodeKey)
}
/**
 * MAP<parentId,[]>转换成树节点数据：即子节点数据添加到父节点的children属性中
 * @param {Object} map <parentId,[]>格式数据，将相同parentId的节点放到同一数组中
 * @param {Object} nodeKey 节点的唯一标识符的属性名
 */
export function mapToTreeData(map, nodeKey) {
  // const startTime = new Date().getTime()
  const isChildNodeIds = []
  // 给存在子节点的节点添加children属性，并将存在对应父节点的子节点id添加到hasParentNodeIds中
  for (const parentId in map) {
    map[parentId].forEach(item => {
      const id = item[nodeKey]
      const childDatas = map[id]
      if (childDatas) {
        isChildNodeIds.push(id)
        item.children = childDatas
      }
    })
  }
  const treeData = []
  // 过滤掉存在对应父节点的子节点，剩下的根节点就是目标树节点
  Object.keys(map).forEach(parentId => {
    if (isChildNodeIds.indexOf(parentId) < 0) {
      treeData.push(...map[parentId])
    }
  })
  return treeData
}
/**
 * 给树节点添加叶子节点数据
 * @param {Array} tree []树节点集合
 * @param {Object} leafDataMap <parentId,[]>格式数据，将相同parentId的节点放到同一数组中
 * @param {Object} nodeKey 节点的唯一标识符的属性名
 */
export function addLeafNodeMap(tree, leafDataMap, nodeKey) {
  if (!tree || !leafDataMap) {
    return tree
  }
  tree.forEach(node => {
    if (node.children && node.children.length > 0) {
      // 需要先给树节点的当前子节点设置叶子节点数据，然后再添加叶子节点数据
      addLeafNodeMap(node.children, leafDataMap, nodeKey)
    }
    const id = node[nodeKey]
    const childDatas = leafDataMap[id]
    if (childDatas) {
      if (!node.children || node.children.length === 0) {
        node.children = childDatas
      } else {
        node.children.push(...childDatas)
      }
    }
  })
  return tree
}
/**
 * 合并节点数据：根据nodeKey合并
 * @param {Array} nodes 目标节点集合
 * @param {Array} appendNodes 待合并节点集合
 * @param {Object} nodeKey 节点的唯一标识符的属性名
 */
export function mergeNodeList(nodes, appendNodes, nodeKey) {
  if (!nodes || nodes.length === 0) {
    return appendNodes
  }
  if (!appendNodes || appendNodes.length === 0) {
    return nodes
  }
  const nodeMap = {}
  nodes.forEach(node => {
    nodeMap[node[nodeKey]] = node
  })
  appendNodes.forEach(node => {
    const oldNode = nodeMap[node[nodeKey]]
    if (oldNode) {
      const oldKeys = Object.keys(oldNode)
      for (const nodeKey in node) {
        oldNode[nodeKey] = node[nodeKey]
        const index = oldKeys.indexOf(nodeKey)
        if (index > -1) {
          oldKeys.splice(index, 1)
        }
      }
      oldKeys.forEach(key => {
        delete oldNode[key]
      })
    } else {
      nodes.push(node)
    }
  })
  return nodes
}
/**
 * 合并节点数据：根据nodeKey合并
 * @param {Object} nodeMap <parentId,[]>目标节点
 * @param {Object} appendNodeMap <parentId,[]>待合并节点
 * @param {Object} nodeKey 节点的唯一标识符的属性名
 */
export function mergeNodeMap(nodeMap, appendNodeMap, nodeKey) {
  if (!nodeMap) {
    return appendNodeMap
  }
  if (!appendNodeMap) {
    return nodeMap
  }
  const idNodeMap = {}
  for (const parentId in nodeMap) {
    nodeMap[parentId].forEach(item => {
      item.parentDataId = parentId
      idNodeMap[item[nodeKey]] = item
    })
  }
  for (const parentId in appendNodeMap) {
    appendNodeMap[parentId].forEach(item => {
      const oldNode = idNodeMap[item[nodeKey]]
      if (oldNode) {
        const oldKeys = Object.keys(oldNode)
        for (const nodeKey in item) {
          oldNode[nodeKey] = item[nodeKey]
          const index = oldKeys.indexOf(nodeKey)
          if (index > -1) {
            oldKeys.splice(index, 1)
          }
        }
        oldKeys.forEach(key => {
          delete oldNode[key]
        })
        oldNode.parentDataId = parentId
      } else {
        item.parentDataId = parentId
        idNodeMap[item[nodeKey]] = item
      }
    })
  }
  const targetNodeMap = {}
  for (const nodeId in idNodeMap) {
    const item = idNodeMap[nodeId]
    const parentDataId = item.parentDataId
    if (targetNodeMap[parentDataId]) {
      targetNodeMap[parentDataId].push(item)
    } else {
      targetNodeMap[parentDataId] = [item]
    }
    delete item.parentDataId
  }
  return targetNodeMap
}
/**
 * 获取节点的最大dataVer
 * @param {Object} nodeMap <parentId,[]>目标节点
 * @param {Number} maxVer 作为对比值，返回大于此值的版本，为null则返回所有节点的最大版本
 */
export function getMaxDataVerFromNodeMap(nodeMap, maxVer) {
  if (!maxVer) {
    maxVer = 0
  }
  for (const parentId in nodeMap) {
    nodeMap[parentId].forEach(node => {
      if (node.dataVer > maxVer) {
        maxVer = node.dataVer
      }
    })
  }
  return maxVer
}
/**
 * 合并节点数据：根据nodeKey合并
 * @param {Object} statusMap <termId,obj>目标节点
 * @param {Object} appendStatusMap <termId,obj>待合并节点
 */
export function mergeStatusMap(statusMap, appendStatusMap) {
  if (!statusMap || !!appendStatusMap['0']) {
    // 如果存在0节点，则说明当前是全量更新，因此只需要返回appendStatusMap
    delete appendStatusMap['0']
    return appendStatusMap
  }
  if (!appendStatusMap) {
    return statusMap
  }
  return Object.assign({}, statusMap, appendStatusMap)
}
/**
 * 获取Map的value的最大modifyVer
 * @param {Object} statusMap <termId,obj>目标节点
 * @param {Number} maxVer 作为对比值，返回大于此值的版本，为null则返回所有节点的最大版本
 */
export function getMaxModifyVerFromStatusMap(statusMap, maxVer) {
  if (!maxVer) {
    maxVer = 0
  }
  for (const termId in statusMap) {
    const dataVer = statusMap[termId].modifyVer
    if (dataVer > maxVer) {
      maxVer = dataVer
    }
  }
  return maxVer
}

/**
 * 从节点集合中获取目标key对应的路径集合
 * @param nodeList    节点集合，注意不是树结构
 * @param keyField
 * @param keyValue
 * @return [节点, 上1级节点, 上2级节点 ...]
 */
export function getNodePathByList(nodeList, keyField, keyValue) {
  const result = []
  for (const i in nodeList) {
    const nodei = nodeList[i]
    if (nodei[keyField] == keyValue) {
      result.push(nodei)
      const parentId = nodei.parentId
      if (parentId !== undefined && parentId !== null && parentId !== '') {
        const parentPath = getNodePathByList(nodeList, 'id', parentId)
        result.push(...parentPath)
      }
      break
    }
  }
  return result
}

export function removeNode(nodes, keyField, keyValue) {
  if (nodes && nodes.length > 0) {
    for (const i in nodes) {
      const nodei = nodes[i]
      if (!nodei) {
        return null
      } else if (nodei[keyField] + '' === keyValue + '') {
        nodes.splice(i, 1)
        return nodei
      } else {
        const temp = removeNode(nodei.children, keyValue, keyField)
        if (temp) return temp
      }
    }
  }
  return null
}

export function addNode(nodes, keyField, node) {
  if (node && node.parentId) {
    // parentId 的值对应父节点nodeKey属性的值
    const temp = findNode(nodes, node.parentId, keyField)
    if (temp) {
      temp.children.push(node)
    } else {
      nodes.push(node)
    }
  }
}

export function updateNode(nodes, keyField, node) {
  if (node && node.parentId) {
    // parentId 的值对应父节点nodeKey属性的值
    const tempParent = findNode(nodes, node.parentId, keyField)
    const loopNode = !tempParent ? nodes : tempParent.children
    for (let i = 0; i < loopNode.length; i++) {
      const temp = loopNode[i]
      if (temp[keyField] == node[keyField]) {
        loopNode.splice(i, 1, node)
        return
      }
    }
  }
}
