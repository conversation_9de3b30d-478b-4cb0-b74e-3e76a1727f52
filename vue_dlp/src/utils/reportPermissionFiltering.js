/**
 * 为了减少冗余代码，方便某些报表中配置项，统计项，自定义列的权限过滤，进行统一维护
 */
import Vue from 'vue'
/**
 * 敏感内容识别模块是否存在（比较多，单独写个方法）
 * 判断是否存在：敏感内容识别模块(如果存在才显示全盘扫描敏感文件数和全盘扫描非敏感文件数)
 */
function isSensitiveness() {
  if (Vue.prototype.hasPermission('F21 | F23 | F11 | F12 | F13 | F14 | F15 | F31 | F32 | F41 | F42 | F43 | F44 | F51 | F52 | F53')) {
    return true
  } else {
    return false
  }
}

/**
 * 数据安全管控报表：（普通报表报表、趋势图---自定义列过滤；；；对比报表---统计项配置过滤）
 * @param statisticalList 需要过滤的原始数据，格式为GridTable那种格式的表格数据
 * @returns {[]}
 */
export function filterDataSecurityLogCustom(statisticalList) {
  // console.log('statisticalList', JSON.parse(JSON.stringify(statisticalList)))
  const filterCustomList = []
  statisticalList.forEach(item => {
    if (Vue.prototype.hasPermission('E53') && (item.prop == 'encOrDecSumAll' || item.prop == 'encSumAll' || item.prop == 'decSumAll')) {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('E52') && (item.prop == 'diskScanSumAll' || item.prop == 'diskScanEncSumAll' || item.prop == 'diskScanDecSumAll')) {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('E52') && (isSensitiveness()) && (item.prop == 'diskScanSstSumAll' || item.prop == 'diskScanNonsstSumAll')) {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('E48') && (item.prop == 'outFileSumAll' || item.prop == 'directOutFileSumAll' || item.prop == 'directOutFileSizeSumAll' || item.prop == 'applyOutFileSumAll' || item.prop == 'applyOutFileSizeSumAll')) {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('E54') && (item.prop == 'fileBackupSumAll' || item.prop == 'fileBackupSizeSumAll')) {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('E56') && (item.prop == 'encFileSumAll')) {
      filterCustomList.push(item)
    }
  })
  return filterCustomList
}

/**
 * 数据安全管控报表：（普通报表、趋势图---默认展示列过滤）
 * @param defaultList 需要过滤的原始数据，格式为GridTable那种格式的表格数据
 * @param propName prop值，（普通报表item.prop == 'label'、趋势图item.prop == 'labelValue'值不一样）
 * @returns {[]}
 */
export function filterDataSecurityLogDefault(defaultList, propName) {
  // console.log('defaultList', JSON.parse(JSON.stringify(defaultList)))
  const filterColModel = []
  defaultList.forEach(item => {
    if (item.prop == propName) {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('E53') && item.prop == 'encOrDecSumAll') {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('E52') && item.prop == 'diskScanSumAll') {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('E48') && item.prop == 'outFileSumAll') {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('E54') && item.prop == 'fileBackupSumAll') {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('E56') && item.prop == 'encFileSumAll') {
      filterColModel.push(item)
    }
  })
  // console.log('filterColModel', JSON.parse(JSON.stringify(filterColModel)))
  return filterColModel
}

/**
 * 移动存储：（普通报表报表、趋势图---自定义列过滤；；；对比报表---统计项配置过滤）
 * @param statisticalList 需要过滤的原始数据，格式为GridTable那种格式的表格数据
 * @returns {[]}
 */
export function filterMobileStorageCustom(statisticalList) {
  // console.log('statisticalList', JSON.parse(JSON.stringify(statisticalList)))
  const filterCustomList = []
  statisticalList.forEach(item => {
    if (Vue.prototype.hasPermission('C73') && item.prop == 'deviceInsertTimesSumAll') {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('C42') && (item.prop == 'fileNumSumAll' || item.prop == 'fileSizeSumAll' || item.prop == 'createFileNumSumAll' ||
      item.prop == 'createFileSizeSumAll' || item.prop == 'copyFileNumSumAll' || item.prop == 'copyFileSizeSumAll' || item.prop == 'renameFileNumSumAll' ||
      item.prop == 'renameFileSizeSumAll' || item.prop == 'deleteFileNumSumAll' || item.prop == 'deleteFileSizeSumAll' || item.prop == 'openFileNumSumAll' ||
      item.prop == 'openFileSizeSumAll' || item.prop == 'updateFileNumSumAll' || item.prop == 'updateFileSizeSumAll')) {
      filterCustomList.push(item)
    }
  })
  return filterCustomList
}

/**
 * 移动存储：（普通报表、趋势图---默认展示列过滤）
 * @param defaultList 需要过滤的原始数据，格式为GridTable那种格式的表格数据
 * @param propName prop值，（普通报表item.prop == 'label'、趋势图item.prop == 'labelValue'值不一样）
 * @returns {[]}
 */
export function filterMobileStorageDefault(defaultList, propName) {
  // console.log('defaultList', JSON.parse(JSON.stringify(defaultList)))
  const filterColModel = []
  defaultList.forEach(item => {
    if (item.prop == propName) {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('C73') && item.prop == 'deviceInsertTimesSumAll') {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('C42') && (item.prop == 'createFileNumSumAll' ||
      item.prop == 'copyFileNumSumAll' || item.prop == 'openFileNumSumAll' || item.prop == 'updateFileNumSumAll')) {
      filterColModel.push(item)
    }
  })
  // console.log('filterColModel', JSON.parse(JSON.stringify(filterColModel)))
  return filterColModel
}

/**
 * 综合报表：（普通报表报表、趋势图---自定义列过滤；；；对比报表---统计项配置过滤）
 * @param statisticalList 需要过滤的原始数据，格式为GridTable那种格式的表格数据
 * @returns {[]}
 */
export function filterIntegrateLogCustom(statisticalList) {
  // console.log('statisticalList', JSON.parse(JSON.stringify(statisticalList)))
  const filterCustomList = []
  statisticalList.forEach(item => {
    if (Vue.prototype.hasPermission('D64') && (item.prop == 'emailNumSumAll' || item.prop == 'emailFileSizeSumAll')) {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('C54') && (item.prop == 'printedCopiesSumAll' || item.prop == 'printedPagesSumAll')) {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('C42') && (item.prop == 'mobileStorageFileNumSumAll' || item.prop == 'mobileStorageFileSizeSumAll')) {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('E48') && (item.prop == 'outFileNumSumAll' || item.prop == 'outFileSizeSumAll')) {
      filterCustomList.push(item)
    } else if (Vue.prototype.hasPermission('D29') && (item.prop == 'browserUploadFileNumSumAll' || item.prop == 'browserUploadFileSizeSumAll')) {
      filterCustomList.push(item)
    }
  })
  return filterCustomList
}

/**
 * 综合报表：（普通报表、趋势图---默认展示列过滤）
 * @param defaultList 需要过滤的原始数据，格式为GridTable那种格式的表格数据
 * @param propName prop值，（普通报表item.prop == 'label'、趋势图item.prop == 'labelValue'值不一样）
 * @returns {[]}
 */
export function filterIntegrateLogDefault(defaultList, propName) {
  // console.log('defaultList', JSON.parse(JSON.stringify(defaultList)))
  const filterColModel = []
  defaultList.forEach(item => {
    if (item.prop == propName) {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('D64') && item.prop == 'emailNumSumAll') {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('C54') && item.prop == 'printedCopiesSumAll') {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('C42') && item.prop == 'mobileStorageFileNumSumAll') {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('E48') && item.prop == 'outFileNumSumAll') {
      filterColModel.push(item)
    } else if (Vue.prototype.hasPermission('D29') && item.prop == 'browserUploadFileNumSumAll') {
      filterColModel.push(item)
    }
  })
  // console.log('filterColModel', JSON.parse(JSON.stringify(filterColModel)))
  return filterColModel
}

/**
 * 审批报表：（普通报表报表、趋势图---自定义列过滤；；；对比报表---统计项配置过滤）
 * @param statisticalList 需要过滤的原始数据，格式为GridTable那种格式的表格数据
 * @returns {[]}
 */
export function filterTrwfeLogCustom(statisticalList, valueList) {
  // testValue 是否是 baseValue 的倍数，倍数值为 1,2,4,8
  const isMultiples = (testValue, baseValue) => {
    return testValue == baseValue || testValue == 2 * baseValue || testValue == 4 * baseValue || testValue == 8 * baseValue
  }
  // console.log('statisticalList', JSON.parse(JSON.stringify(statisticalList)))
  // console.log('valueList', JSON.parse(JSON.stringify(valueList)))
  const filterCustomList = []
  const baseValueList = [1, 16, 256, 4096, 65536, 268435456, 4294967296, 68719476736, 1099511627776]
  valueList.forEach(value => {
    if (baseValueList.includes(value)) {
      filterCustomList.push(...statisticalList.filter(data => isMultiples(data.value, value)))
    }
  })
  return filterCustomList
}

/**
 * 应用程序运行时长报表数据列根据权限进行过滤
 * @param dataColumns
 * @returns {*[]}
 */
export function filterAppRuntimeColumn(dataColumns) {
  const result = []
  dataColumns.forEach(item => {
    if (item.prop === 'label' || item.prop === 'labelValue') {
      result.push(item)
    }
    if (Vue.prototype.hasPermission('B26') && (item.prop === 'totalUptime' || item.prop === 'activeUptime')) {
      result.push(item)
    }
    if (Vue.prototype.hasPermission('C27') && item.prop === 'runTimeSumAll') {
      result.push(item)
    }
  })
  return result
}
