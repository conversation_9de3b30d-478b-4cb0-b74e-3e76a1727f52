import { readAsArrayBuffer, readAsText } from '@/utils/blob'

// 切片大小：1MB
const CHUNK_SIZE = 1024 * 1024

/**
 * 是否UTF-8编码文件
 */
export async function isUtf8File(file) {
  // 如果文件非常大，比如几个G，浏览器读到的内容直接放在内存中，fileReader实例会直接触发onerror，抛出错误，有时浏览器会直接崩溃。
  // 对大文件切片处理可避免OOM，并快速失败
  let result = 0
  for (let i = 0; i < file.size; i += CHUNK_SIZE) {
    const chunk = file.slice(i, i + CHUNK_SIZE)
    const buffer = await readAsArrayBuffer(chunk)
    result = isUtf8Buffer(buffer, i === 0)
    if (result < 0) {
      return false
    }
    i -= result
  }
  return result === 0
}

/**
 * 使用FileReader以utf-8格式读取文件，根据文件内容是否包含乱码字符�，来判断文件是否为utf-8。 如果存在�，即文件编码非utf-8，反之为utf-8。
 */
export async function isUtf8FileByReadText(file) {
  try {
    const result = await readAsText(file)
    return result.indexOf('�') < 0
  } catch (e) {
    return false
  }
}

/**
 * 统计utf-8编码字符串的字节数
 * @param str utf-8字符串
 * @returns {number} 总字节数
 */
export function countUtf8Byte(str) {
  if (typeof str != 'string') {
    return 0
  }
  let total = 0
  for (let i = 0, len = str.length; i < len; i++) {
    const charCode = str.charCodeAt(i)
    if (charCode <= 0x007f) {
      // 字符代码在000000 – 00007F之间的，用一个字节编码
      total += 1
    } else if (charCode <= 0x07ff) {
      // 000080 – 0007FF之间的字符用两个字节
      total += 2
    } else if (charCode <= 0xffff) {
      // 000800 – 00D7FF 和 00E000 – 00FFFF 之间的用三个字节，注: Unicode在范围 D800-DFFF 中不存在任何字符
      total += 3
    } else {
      // 010000 – 10FFFF 之间的用4个字节
      total += 4
    }
  }
  return total
}

/**
 * UTF-8是一种多字节编码的字符集，表示一个Unicode字符时，它可以是1个至多个字节，在表示上有规律：
 * 1字节：0xxxxxxx
 * 2字节：110xxxxx 10xxxxxx
 * 3字节：1110xxxx 10xxxxxx 10xxxxxx
 * 4字节：11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
 *
 * UTF-8字符串的各个字节的取值有一定的范围，并不是所有的值都是有效的UTF-8字符
 * 另外BOM本身也符合3字节UTF-8字符编码规律，所以本方法对带BOM的UTF-8字符串也是有效的。
 * @param buffer ArrayBuffer
 * @param checkBOM 是否校验BOM
 * @returns {number} -1：不是utf8编码；0：utf8编码；正数：需进一步验证
 */
function isUtf8Buffer(buffer, checkBOM) {
  const bytes = new Uint8Array(buffer)
  // BOM (Byte Order Mark, 字节序标志)，即0xEF, 0xBB,0xBF
  if (checkBOM && bytes.length >= 3 && bytes[0] === 0xEF && bytes[1] === 0xBB && bytes[2] === 0xBF) {
    return 0
  }
  let i = 0
  let byte
  while (i < bytes.length) {
    byte = bytes[i]
    // (10000000): 值小于0x80的为ASCII字符
    if (byte < 0x80) {
      i++
      continue
    }
    // (11000000): 值介于0x80与0xC0之间的为无效UTF-8字符
    if (byte < 0xC0) {
      return -1
    }
    // (11100000): 值介于0xC0与0xE0之间为2字节UTF-8字符
    if (byte < 0xE0) {
      // 剩余长度
      const remaining = bytes.length - i
      if (remaining < 2) {
        return remaining
      }
      // 10xxxxxx
      if ((bytes[i + 1] & 0xC0) === 0x80) {
        i += 2
        continue
      }
      return -1
    }
    // (11110000): 值介于0xE0与0xF0之间为3字节UTF-8字符
    if (byte < 0xF0) {
      // 剩余长度
      const remaining = bytes.length - i
      if (remaining < 3) {
        return remaining
      }
      if ((bytes[i + 1] & 0xC0) === 0x80 && (bytes[i + 2] & 0xC0) === 0x80) {
        i += 3
        continue
      }
      return -1
    }
    // (11111111): 值介于0xF0与0xFF之间为4字节UTF-8字符
    if (byte <= 0xFF) {
      // 剩余长度
      const remaining = bytes.length - i
      if (remaining < 4) {
        return remaining
      }
      if ((bytes[i + 1] & 0xC0) === 0x80 && (bytes[i + 2] & 0xC0) === 0x80 && (bytes[i + 3] & 0xC0) === 0x80) {
        i += 4
        continue
      }
      return -1
    }
  }
  return 0
}

// https://github.com/wayfind/is-utf8/blob/master/is-utf8.js
// exports = module.exports = function(bytes)
// {
//   var i = 0;
//   while(i < bytes.length)
//   {
//     if(     (// ASCII
//       bytes[i] == 0x09 ||
//       bytes[i] == 0x0A ||
//       bytes[i] == 0x0D ||
//       (0x20 <= bytes[i] && bytes[i] <= 0x7E)
//     )
//     ) {
//       i += 1;
//       continue;
//     }
//
//     if(     (// non-overlong 2-byte
//       (0xC2 <= bytes[i] && bytes[i] <= 0xDF) &&
//       (0x80 <= bytes[i+1] && bytes[i+1] <= 0xBF)
//     )
//     ) {
//       i += 2;
//       continue;
//     }
//
//     if(     (// excluding overlongs
//         bytes[i] == 0xE0 &&
//         (0xA0 <= bytes[i + 1] && bytes[i + 1] <= 0xBF) &&
//         (0x80 <= bytes[i + 2] && bytes[i + 2] <= 0xBF)
//       ) ||
//       (// straight 3-byte
//         ((0xE1 <= bytes[i] && bytes[i] <= 0xEC) ||
//           bytes[i] == 0xEE ||
//           bytes[i] == 0xEF) &&
//         (0x80 <= bytes[i + 1] && bytes[i+1] <= 0xBF) &&
//         (0x80 <= bytes[i+2] && bytes[i+2] <= 0xBF)
//       ) ||
//       (// excluding surrogates
//         bytes[i] == 0xED &&
//         (0x80 <= bytes[i+1] && bytes[i+1] <= 0x9F) &&
//         (0x80 <= bytes[i+2] && bytes[i+2] <= 0xBF)
//       )
//     ) {
//       i += 3;
//       continue;
//     }
//
//     if(     (// planes 1-3
//         bytes[i] == 0xF0 &&
//         (0x90 <= bytes[i + 1] && bytes[i + 1] <= 0xBF) &&
//         (0x80 <= bytes[i + 2] && bytes[i + 2] <= 0xBF) &&
//         (0x80 <= bytes[i + 3] && bytes[i + 3] <= 0xBF)
//       ) ||
//       (// planes 4-15
//         (0xF1 <= bytes[i] && bytes[i] <= 0xF3) &&
//         (0x80 <= bytes[i + 1] && bytes[i + 1] <= 0xBF) &&
//         (0x80 <= bytes[i + 2] && bytes[i + 2] <= 0xBF) &&
//         (0x80 <= bytes[i + 3] && bytes[i + 3] <= 0xBF)
//       ) ||
//       (// plane 16
//         bytes[i] == 0xF4 &&
//         (0x80 <= bytes[i + 1] && bytes[i + 1] <= 0x8F) &&
//         (0x80 <= bytes[i + 2] && bytes[i + 2] <= 0xBF) &&
//         (0x80 <= bytes[i + 3] && bytes[i + 3] <= 0xBF)
//       )
//     ) {
//       i += 4;
//       continue;
//     }
//
//     return false;
//   }
//
//   return true;
// }
