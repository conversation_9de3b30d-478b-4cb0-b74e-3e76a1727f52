/**
 * 文档水印策略相关变量及函数
 * <AUTHOR>
 */
import i18n from '@/lang';
import Vue from 'vue';

export const browserOptions = [
  { label: i18n.t('pages.browserOptions1'), value: '360SE.EXE' },
  { label: i18n.t('pages.browserOptions2'), value: '360CHROME.EXE' },
  { label: i18n.t('pages.browserOptions2_1'), value: '360CHROMEX.EXE' },
  { label: i18n.t('pages.browserOptions3'), value: 'FIREFOX.EXE' },
  { label: i18n.t('pages.browserOptions4'), value: 'IEXPLORE.EXE' },
  { label: i18n.t('pages.browserOptions5'), value: 'SOGOUEXPLORER.EXE' },
  { label: i18n.t('pages.browserOptions6'), value: '2345EXPLORER.EXE' },
  { label: i18n.t('pages.browserOptions7'), value: 'CHROME.EXE' },
  { label: i18n.t('pages.browserOptions8'), value: 'QQBROWSER.EXE' }
]

export const imOptions = [
  { label: i18n.t('pages.qq'), value: 'QQ.EXE' },
  { label: i18n.t('pages.wechat'), value: 'WECHAT.EXE' },
  { label: i18n.t('pages.enterpriseWeChat'), value: 'WXWORK.EXE' },
  { label: i18n.t('pages.dingTalk'), value: 'DINGTALK.EXE' },
  { label: i18n.t('pages.tim'), value: 'TIM.EXE' },
  { label: i18n.t('pages.feiShu'), value: 'FEISHU.EXE' }
]

export const netDiskOptions = [
  { label: i18n.t('pages.softBaiDuNetDisk'), value: 'BAIDUNETDISK.EXE' }
]

export const effectFileSuffixOpts = [
  {
    value: '*.*',
    label: i18n.t('pages.allDocumentSuffix'),
    disabled: true
  },
  {
    value: 'office_suffix',
    label: i18n.t('pages.officeDocumentSuffix'),
    suffixes: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']
  },
  {
    value: 'wps_suffix',
    label: i18n.t('pages.wpsDocumentSuffix'),
    suffixes: ['wps', 'wpt', 'et', 'ett', 'dps', 'dpt']
  },
  {
    value: 'image_suffix',
    label: i18n.t('pages.imageSuffix'),
    suffixes: ['bmp', 'jpeg', 'jpe', 'jpg', 'png', 'tiff']
  },
  {
    value: 'pdf_suffix',
    label: i18n.t('pages.pdfDocumentSuffix'),
    suffixes: ['pdf']
  },
  {
    value: 'ofd_suffix',
    label: i18n.t('pages.ofdDocumentSuffix'),
    suffixes: ['ofd']
  }
]

export const getAddWatermarkOpts = function() {
  return [
    // moduleId: 对应的销售模块，未配置时不受销售模块影响
    // unSupExt: 不支持的文档后缀
    { label: i18n.t('pages.officeMarkWay1', { desc: '' }), value: 1, unSupExt: ['image_suffix'] },
    { label: i18n.t('pages.officeMarkWay2', { desc: '' }), value: 2, unSupExt: ['image_suffix', 'ofd_suffix'] },
    { label: i18n.t(Vue.prototype.hasPermission('E22') ? 'pages.officeMarkWay4' : 'pages.officeMarkWay4NoPermission', { desc: '' }), value: 4, moduleId: 51 },
    { label: i18n.t('pages.officeMarkWay8'), value: 8 },
    { label: i18n.t('pages.officeMarkWay16', { desc: '' }), value: 16, moduleId: 16, unSupExt: ['image_suffix'] },
    { label: i18n.t('pages.officeMarkWay32', { desc: '' }), value: 32, moduleId: 32, unSupExt: ['image_suffix'] },
    { label: i18n.t('pages.officeMarkWay64', { desc: '' }), value: 64, moduleId: 36, unSupExt: ['image_suffix'] },
    { label: i18n.t('pages.officeMarkWay128', { desc: '' }), value: 128, unSupExt: ['image_suffix'] },
    { label: i18n.t('pages.officeMarkWay256', { desc: '' }), value: 256, moduleId: 31, unSupExt: ['image_suffix'] },
    { label: i18n.t('pages.officeMarkWay512', { desc: '' }), value: 512, moduleId: 32, unSupExt: ['image_suffix'] }
  ]
}

