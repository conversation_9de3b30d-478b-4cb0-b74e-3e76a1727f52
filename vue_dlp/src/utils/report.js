/**
 * 为了减少冗余代码，方便自定义报表添加数据项，将报表公用的数据放到这里统一维护
 */
import Vue from 'vue'
import i18n from '@/lang'
import { formatSeconds, formatFileSize } from '@/utils/index'
import { getAllChatLogDetail, getAssetLogDetail, getDiskScanLogDetail, getMobileFileOpDetail, getMobileStorageDetail } from '@/api/report/baseReport/issueReport/detail';

// ================================================= 自定义报表数据项 =================================================
export function getDataItems(viewDetails) {
  const dataItems = [
    { prop: 'encOrDecSumAll', label: 'encOrDecSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol(), meCode: 'E53' },
    { prop: 'diskScanSumAll', label: 'diskScanSumAll', width: '150', sort: true, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad, meCode: 'E52' },
    { prop: 'trwfeSumAll', label: 'trwfeSumAll', width: '150', sort: true, detailTable: 'dwd_act_hi_taskinst', detailCol: getTrwfeLogCol(), meCode: 'E55' },
    { prop: 'outFileSumAll', label: 'outFileSumAll', width: '150', sort: true, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol(), meCode: 'E48' },
    { prop: 'fileBackupSumAll', label: 'fileBackupSumAll', width: '150', sort: true, detailTable: 'dwd_file_backup_record', detailCol: getFileBackupDetailCol(), meCode: 'E54' },
    { prop: 'encFileSumAll', label: 'encFileSumAll', width: '150', sort: true, detailTable: 'dwd_enc_dec_files_log', detailCol: getEncDecFilesLogCol(), beforeLoadDetail: beforeLoadEncOrDec, meCode: 'E56' },
    { prop: 'securityLevelConvertCountSum', label: 'securityLevelConvertCountSum', width: '150', sort: true, detailTable: 'dwd_dense_trans_details_log', detailCol: getSecurityLevelConvertNumDetailCol(), meCode: 'E51' },
    { prop: 'backupRecordCountSum', label: 'backupRecordCountSum', width: '150', sort: true, detailTable: 'dwd_smart_backup_log', detailCol: getSmartBackupNumDetailCol(), meCode: 'E76' },
    { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll', width: '150', sort: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol(), meCode: 'C54' },
    { prop: 'bluetoothPairingRecordCountSum', label: 'bluetoothPairingRecordCountSum', width: '150', sort: true, detailTable: 'dwd_bt_conn_change', detailCol: getBluetoothParingNumDetailCol(), meCode: 'C63' },
    { prop: 'bluetoothFileTransferCountSum', label: 'bluetoothFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol(), meCode: 'C64' },
    { prop: 'fileOpNum', label: 'fileOpNum', width: '150', sort: true, detailTable: 'dwd_file_op_log', detailCol: getFileOpNumDetailCol(), meCode: 'C42' },
    { prop: 'deviceInsertTimesSumAll', label: 'deviceInsertTimesSumAll', width: '150', sort: true, detailCol: getDeviceInsertTimesDetailCol(), getDetailMethod: getMobileStorageDetail, beforeLoadDetail: beforeLoad, meCode: 'C73' },
    { prop: 'outgoingFileCountSum', label: 'outgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol(), meCode: 'C66' },
    { prop: 'mtpOutgoingFileCountSum', label: 'mtpOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol(), meCode: 'C68' },
    { prop: 'usbOutgoingFileCountSum', label: 'usbOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol(), meCode: 'C75' },
    { prop: 'cdBurnRecordCountSum', label: 'cdBurnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol(), meCode: 'C83' },
    { prop: 'systemLogNum', label: 'systemLogNum', width: '150', sort: true, detailTable: 'dwd_system_log', detailCol: getSystemLogNumDetailCol(), meCode: 'B26' },
    { prop: 'computerEnergySavingCountSum', label: 'computerEnergySavingCountSum', width: '150', sort: true, detailTable: 'dwd_energy_saving_record', detailCol: getComputerEnergySavingNumDetailCol(), meCode: 'B2R' },
    { prop: 'urlNumSumAll', label: 'urlNumSumAll', width: '150', sort: true, detailTable: 'dwd_urls_log', detailCol: getWebBrowsingNumDetailCol(), meCode: 'D27' },
    { prop: 'totalTimeSumAll', label: 'totalTimeSumAll', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_web_browsing_time', detailCol: getWebBrowsingTimeDetailCol(), meCode: 'D27' },
    { prop: 'urlSearchSumAll', label: 'urlSearchSumAll', width: '150', sort: true, detailTable: 'dwd_urls_search', detailCol: getWebSearchNumDetailCol(), meCode: 'D28' },
    { prop: 'browserPasteSumAll', label: 'browserPasteSumAll', width: '150', sort: true, detailTable: 'dwd_browser_paste_log', detailCol: getWebPasteNumDetailCol(), meCode: 'D2D' },
    { prop: 'postCountSum', label: 'postCountSum', width: '150', sort: true, detailTable: 'dwd_forum_data_log', detailCol: getForumPostNumDetailCol(), meCode: 'D2A' },
    { prop: 'runTimeSumAll', label: 'runTimeSumAll', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_app_reportex', detailCol: getAppRunTimeDetailCol(), meCode: 'C27' },
    { prop: 'emailNumSumAll', label: 'emailNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: getEmailNumDetailCol(viewDetails), meCode: 'D64' },
    { prop: 'generalScanViolateLogSumAll', label: 'generalScanViolateLogSumAll', width: '150', sort: true, detailTable: 'dwd_general_scan_violate_log', detailCol: getGeneralDetailCol(), meCode: 'H11' },
    { prop: 'dripScanViolateLogSumAll', label: 'dripScanViolateLogSumAll', width: '150', sort: true, detailTable: 'dwd_drip_scan_violate_log', detailCol: getDripDetailCol(), meCode: 'H12' },
    { prop: 'chatRecordNumSumAll', label: 'chatRecordNumSumAll', width: '150', sort: true, detailCol: getAllChatLogDetailCol(viewDetails), getDetailMethod: getAllChatLogDetail, meCode: 'D39' },
    { prop: 'loginNumSumAll', label: 'loginNumSumAll', width: '150', sort: true, detailTable: 'dwd_chat_login_log', detailCol: getChatLoginLogDetailCol(), meCode: 'D35' },
    { prop: 'wifiConnRecordCountSum', label: 'wifiConnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_wifi_alarm_log', detailCol: getWifiConnNumDetailCol(), meCode: 'D45' },
    { prop: 'hardwareAssetChangeTimes', label: 'hardwareAssetChangeTimes', width: '150', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail, meCode: 'B43' },
    { prop: 'softwareAssetChangeTimes', label: 'softwareAssetChangeTimes', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail, meCode: 'B46' },
    { prop: 'browserUploadFileNumSumAll', label: 'browserUploadFileNumSumAll', width: '150', sort: true, detailTable: 'dwd_browser_upload_log', detailCol: getBrowserUploadDetailCol(), meCode: 'D29' },
    { prop: 'mobileStorageFileNumSumAll', label: 'mobileStorageFileNumSumAll', width: '150', sort: true, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoadMobileDiskOpFileNum, meCode: 'C42' },
    { prop: 'totalFlowNumSumAll', label: 'totalFlow', width: '150', sort: true, sortOriginal: true, flag: 'size', detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol(), meCode: 'D53' },
    { prop: 'cmdDataNumSumAll', label: 'cmdDataNumSumAll', width: '150', sort: true, detailTable: 'dwd_telnet_monitor_log', detailCol: getCmdNumDetailCol(), meCode: 'H56' },
    { prop: 'localFileShareCountSum', label: 'localFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol(), meCode: 'D74' },
    { prop: 'netFileShareCountSum', label: 'netFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol(), meCode: 'D75' },
    { prop: 'fileNameNumSumAll', label: 'fileNameNumSumAll', width: '150', sort: true, detailTable: 'dwd_cur_task_info_log', detailCol: getAppUninstallNumDetailCol(), meCode: 'B36' },
    { prop: 'networkDiskAuditFileNumSumAll', label: 'networkDiskAuditFileNumSumAll', width: '150', sort: true, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol(), meCode: 'D82' },
    { prop: 'ftpFileTransferCountSum', label: 'ftpFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol(), meCode: 'D84' },
    { prop: 'sensitiveKeywordTriggerCount', label: 'sensitiveKeywordTriggerCount', width: '150', sort: true, detailTable: 'dwd_sensitive_keyword_log', detailCol: getSensitiveKeywordNumDetailCol(), meCode: 'H11' }
  ]

  return dataItems.filter(item => Vue.prototype.hasPermission(item.meCode))
}

// ================================================= 通用方法 =================================================
export function beforeLoad(row, column) {
  // 此方法返回的参数会带入到详情查询条件中
  return {
    colProp: column.property
  }
}

// ================================================= 批量加解密报表 =================================================
const opTypeMap = {
  1: i18n.t('pages.loadTypeOptions3'),
  2: i18n.t('pages.loadTypeOptions2')
}
export function getEncOrDecDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'op_name', label: 'encryptionDecryption', width: '150', formatter: row => {
      return opTypeMap[row.op_type]
    } },
    { prop: 'file_total', label: 'fileAllNum', width: '150' },
    { prop: 'succ_count', label: 'successCount', width: '150' },
    { prop: 'fail_count', label: 'errorCount', width: '150' }
  ]
}

// ================================================= 数据安全报表 =================================================
const diskScanOpTypeMap = {
  1: i18n.t('pages.fullDecryption'),
  2: i18n.t('pages.globalEncryption'),
  3: i18n.t('pages.diskScan_Msg4'),
  5: i18n.t('pages.diskScan_Msg3'),
  7: i18n.t('pages.diskScan_Msg47'),
  8: i18n.t('pages.diskScan_Msg48'),
  9: i18n.t('pages.diskScan_Msg49')
}
const outFileMakeTypeMap = {
  0: i18n.t('pages.fileOutgoingLog_Msg'),
  1: i18n.t('pages.fileOutgoingLog_Msg1')
}
const outgoingFileType = {
  0: i18n.t('pages.fileOutgoingLog_Msg2'),
  1: i18n.t('pages.fileOutgoingLog_Msg3')
}
const controlCodeMap = {
  1: i18n.t('pages.ctrlValueList2'),
  2: i18n.t('pages.ctrlValueList3'),
  4: i18n.t('pages.ctrlValueList1'),
  8: i18n.t('pages.ctrlValueList4'),
  16: i18n.t('pages.ctrlValueList7'),
  32: i18n.t('pages.ctrlValueList5'),
  64: i18n.t('pages.ctrlValueList6'),
  1024: i18n.t('pages.ctrlValueList19')
}
const rightRulesMap = {
  1: i18n.t('pages.validateValue1'),
  2: i18n.t('pages.fileOutgoingLog_Msg8'),
  4: i18n.t('pages.validateValue3'),
  8: i18n.t('pages.validateValue4'),
  16: i18n.t('pages.validateValue5'),
  32: i18n.t('pages.validateValue6'),
  64: i18n.t('pages.validateValue7'),
  128: i18n.t('pages.validateValue2'),
  256: i18n.t('pages.validateValue15')
}
const diskScanStatusTypeMap = {
  0: i18n.t('pages.startScan'),
  1: i18n.t('pages.endScan'),
  2: i18n.t('pages.pauseScan')
}
function screenFormatter(row) {
  return row.screen_water_name ? row.screen_water_name : i18n.t('text.dontOpen1')
}
function printFormatter(row) {
  return row.print_water_name ? row.print_water_name : i18n.t('text.dontOpen1')
}
function readTimesFormatter(row) {
  if (row.read_times === 65535) {
    return i18n.t('pages.notLimit')
  } else {
    return row.read_times
  }
}
function beginReadTimeFormatter(row) {
  if (row.begin_read_time === '0001-01-01 00:00:00') {
    return i18n.t('pages.notLimit')
  } else {
    return row.begin_read_time
  }
}
function endReadTimeFormatter(row) {
  if (row.end_read_time === '9999-12-31 23:59:59') {
    return i18n.t('pages.notLimit')
  } else {
    return row.end_read_time
  }
}
function fileNameFormatter(row) {
  if (row.file_full_path) {
    if (row.file_full_path.lastIndexOf('\\') >= 0) {
      return row.file_full_path.substr(row.file_full_path.lastIndexOf('\\') + 1, row.file_full_path.length)
    } else if (row.file_full_path.startsWith('/')) {
      // linux/mac 路径适配
      return row.file_full_path.split('/').pop();
    }
    return row.file_full_path
  }
  return ''
}
/**
 * 判断是否存在：敏感内容识别模块(如果存在才显示敏感文件数和非敏感文件数的表格字段)
 */
function isSensitiveness() {
  if (Vue.prototype.hasPermission('E52') && Vue.prototype.hasPermission('F21 | F23 | F11 | F12 | F13 | F14 | F15 | F31 | F32 | F41 | F42 | F43 | F44 | F51 | F52 | F53')) {
    return true
  } else {
    return false
  }
}
export function getDiskScanDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'op_type_name', label: 'operateType', width: '150', formatter: row => {
      return diskScanOpTypeMap[row.op_type]
    } },
    { prop: 'start_time', label: 'startTime', width: '150' },
    { prop: 'end_time', label: 'endTime', width: '150' },
    { prop: 'status_type_name', label: 'taskStatus', width: '100', formatter: row => {
      return diskScanStatusTypeMap[row.status_type]
    } },
    { prop: 'proc_total', label: 'totalNumberScan', width: '110' },
    { prop: 'proc_suc', label: 'numberSuccessProcess', width: '100' },
    { prop: 'proc_fail', label: 'numberFailProcess', width: '100' },
    { prop: 'sst_file', label: 'numberSensitive', width: '100', hidden: !isSensitiveness() },
    { prop: 'nonsst_file', label: 'numberNonSensitive', width: '110', hidden: !isSensitiveness() }
  ]
}
export function getOutFileDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'file_full_path', label: 'fileName', width: '150', formatter: fileNameFormatter },
    { prop: 'file_size', label: 'maxFileSize1', width: '150', flag: 'size' },
    { prop: 'make_type_name', label: 'productionType', width: '150', formatter: (row) => {
      return outFileMakeTypeMap[row.make_type]
    } },
    { prop: 'out_send_file_type_name', label: 'outgoingFileType', width: '150', formatter: (row) => {
      return outgoingFileType[row.out_send_file_type]
    } },
    { prop: 'control_code_name', label: 'controlCode', width: '150', formatter: (row) => {
      let result = ''
      const controlCodes = Vue.prototype.numToArr(row.control_code)
      controlCodes.forEach(op => {
        result += (controlCodeMap[op] || '') + ' '
      })
      return result
    } },
    { prop: 'right_rules_name', label: 'rightRules', width: '150', formatter: (row) => {
      let result = ''
      const rightRulesS = Vue.prototype.numToList(row.right_rules, 7)
      rightRulesS.forEach(op => {
        result += rightRulesMap[op] + ' '
      })
      return result
    } },
    { prop: 'read_times', label: 'readTimes', width: '150', formatter: readTimesFormatter },
    { prop: 'begin_read_time', label: 'beginReadTime', width: '150', formatter: beginReadTimeFormatter },
    { prop: 'end_read_time', label: 'endReadTime', width: '150', formatter: endReadTimeFormatter },
    { prop: 'msg_tip', label: 'msgTip', width: '150' },
    { prop: 'screen_water_name', label: 'labelScreen', width: '150', formatter: screenFormatter },
    { prop: 'print_water_name', label: 'labelPrint', width: '150', formatter: printFormatter }
  ]
}
export function getFileBackupDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    // 原单位为（B）
    { prop: 'file_size', label: 'maxFileSize1', flag: 'size', width: '150', formatter: row => {
      return (row.file_size / 1024.0).toFixed(2)
    } },
    { label: 'fileName', width: '150', formatter: (row) => {
      if (row.local_file_name) {
        if (row.local_file_name.lastIndexOf('\\') >= 0) {
          return row.local_file_name.substr(row.local_file_name.lastIndexOf('\\') + 1, row.local_file_name.length)
        }
        return row.local_file_name
      }
      return ''
    } },
    { prop: 'local_file_name', label: 'localFileName', width: '150' }
  ]
}
export function getEncDecFilesLogCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'file_path', label: 'filePath', width: '150' },
    { prop: 'file_ext', label: 'suffixes', width: '150' },
    { prop: 'file_proc', label: 'processName1', width: '150' }
  ]
}
export function beforeLoadEncOrDec(row, column) {
  if ('encSumAll' === column.property || 'encFileSumAll' === column.property) {
    return {
      condition: { op_type: 1 }
    }
  }
  return {
    condition: { op_type: 2 }
  }
}
export function beforeLoadOutFile(row, column) {
  if ('directOutFileSumAll' === column.property || 'directOutFileSizeSumAll' === column.property) {
    return {
      condition: { make_type: 0 }
    }
  }
  return {
    condition: { make_type: 1 }
  }
}

// ================================================= 打印报表 =================================================
export function getPrintDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'file_name', label: 'fileName1', width: '150' },
    { prop: 'file_size', label: 'maxFileSize1', flag: 'size', width: '150', formatter: row => {
      return (row.file_size / 1024.0).toFixed(2)
    } },
    { prop: 'printed_copies', label: 'printedCopiesSumAll', width: '150' },
    { prop: 'printed_pages', label: 'printedPagesSumAll', width: '150' },
    { prop: 'printer_name', label: 'printer', width: '150' },
    { prop: 'print_ip_addr', label: 'printerIP', width: '150' }
  ]
}

// ================================================= 文件操作报表 =================================================
const fileOpTypeMap = {
  1: i18n.t('pages.create'),
  2: i18n.t('pages.rename'),
  3: i18n.t('pages.delete'),
  4: i18n.t('pages.copy'),
  17: i18n.t('pages.open'),
  18: i18n.t('pages.edit')
}
export function getFileOpNumDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'process_name', label: 'processName', width: '100' },
    { prop: 'file_op_type_name', label: 'operateType', width: '100', formatter: (row) => {
      return fileOpTypeMap[row.file_op_type]
    } },
    { prop: 'file_name', label: 'operateObject', width: '200' },
    { prop: 'src_file_size', label: 'fromFileSize', width: '150' }
  ]
}

export function getFileOpTypeMap() {
  return fileOpTypeMap
}

export function beforeLoadFileOpDetail(row, column) {
  const fileOpType = column.property.split('_')[1]
  return {
    condition: {
      file_op_type: fileOpType || '-1'
    }
  }
}

// ================================================= 移动存储报表 =================================================
const eventTypeMap = {
  0: i18n.t('pages.unknownType'),
  1: i18n.t('pages.insert1'),
  2: i18n.t('pages.pullOut')
}
const deviceTypeMap = {
  0: i18n.t('pages.devType25'),
  1: i18n.t('pages.devType2'),
  2: i18n.t('pages.devType6'),
  3: i18n.t('pages.devType5'),
  4: i18n.t('pages.devType3'),
  5: i18n.t('pages.devType4'),
  6: i18n.t('pages.devType7'),
  7: i18n.t('pages.devType8'),
  8: i18n.t('pages.devType9'),
  9: i18n.t('pages.devType10'),
  10: i18n.t('pages.devType1'),
  11: i18n.t('pages.devType11'),
  12: i18n.t('pages.devType12'),
  13: i18n.t('pages.devType13'),
  14: i18n.t('pages.devType14'),
  15: i18n.t('pages.devType15'),
  16: i18n.t('pages.devType16'),
  17: i18n.t('pages.devType17'),
  18: i18n.t('pages.devType18'),
  19: i18n.t('pages.devType19'),
  20: i18n.t('pages.devType20'),
  21: i18n.t('pages.devType21'),
  22: i18n.t('pages.devType22'),
  23: i18n.t('pages.devType23'),
  24: i18n.t('pages.devType24')
}
export function getDeviceInsertTimesDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'event_type_name', label: 'eventType', width: '100', formatter: (row) => {
      return eventTypeMap[row.event_type]
    } },
    { prop: 'device_type', label: 'devType', width: '100', formatter: row => {
      return deviceTypeMap[row.dev_type_id]
    } },
    { prop: 'device_desc', label: 'devDesc', width: '200' }
  ]
}

// ================================================= 系统日志报表 =================================================
const logLevelMap = {
  0: i18n.t('pages.keyword'),
  1: i18n.t('text.error'),
  2: i18n.t('text.warning'),
  3: i18n.t('pages.info')
}
const eventTypeOptions = {
  1: i18n.t('pages.sleepStart'),
  2: i18n.t('pages.interactiveLogon'),
  3: i18n.t('pages.net'),
  7: i18n.t('pages.unlock'),
  10: i18n.t('pages.remoteInteraction'),
  // 开机：win7系统及以上
  12: i18n.t('pages.powerOn'),
  // 关机：win7系统及以上
  13: i18n.t('pages.shutDown'),
  42: i18n.t('pages.sleep'),
  // 睡眠：arm系统
  506: i18n.t('pages.sleep'),
  // 睡眠启动：arm系统
  507: i18n.t('pages.sleepStart'),
  4624: i18n.t('pages.loginSuccess'),
  4625: i18n.t('pages.loginFail'),
  4634: i18n.t('pages.cancellation'),
  // 注销：xp系统
  4647: i18n.t('pages.cancellation'),
  // 注销后登录：xp系统
  4648: i18n.t('pages.logoutThenLogin'),
  4778: i18n.t('pages.reSwitchUser'),
  4779: i18n.t('pages.switchUserMiss'),
  4800: i18n.t('pages.lockScreen'),
  4801: i18n.t('pages.unlockScreen'),
  4802: i18n.t('pages.inScreensaver'),
  4803: i18n.t('pages.outScreensaver'),
  // 开机：xp系统
  6005: i18n.t('pages.powerOn'),
  // 关机：xp系统
  6006: i18n.t('pages.shutDown'),
  6008: i18n.t('pages.unexpectedShutdown'),
  // 注销后登录：win7系统及以上
  7001: i18n.t('pages.logoutThenLogin'),
  // 注销：win7系统及以上
  7002: i18n.t('pages.cancellation'),
  // 虚拟关机
  100: i18n.t('pages.virtualshutdown'),
  // 虚拟开机
  101: i18n.t('pages.virtualBoot')
}
export function getSystemLogNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'record_id', label: 'logRecNum', width: '80' },
    { prop: 'event_id', label: 'eventId', width: '80' },
    { prop: 'event_name', label: 'operateEvent', width: '100', formatter: row => {
      return eventTypeOptions[row.event_id]
    } },
    { prop: 'log_level_name', label: 'logLevel', width: '100', formatter: row => {
      return logLevelMap[row.log_level]
    } },
    { prop: 'log_src', label: 'logSource', width: '80' },
    { prop: 'log_desc', label: 'eventDesc', width: '100' }
  ]
}

// ================================================= 网页管控报表 =================================================
export function getWebBrowsingNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'host', label: 'website', width: '150' },
    { prop: 'web_title', label: 'webTitle', width: '200' },
    { prop: 'url', label: 'websiteUrl', width: '200', formatter: row => { return row.url + row.url_ext } }
  ]
}
export function getWebBrowsingTimeDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'host', label: 'website', width: '150' },
    { prop: 'total_time', label: 'browseDuration', width: '150', formatter: (row, data) => { return formatSeconds(data) } }
  ]
}
export function getWebSearchNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'host', label: 'website', width: '150' },
    { prop: 'web_title', label: 'webTitle', width: '200' },
    { prop: 'url', label: 'websiteUrl', width: '200' },
    { prop: 'search_word', label: 'searchContent', width: '200' }
  ]
}
export function getWebPasteNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'browser_type', label: 'browserType', width: '150', formatter: (row, data) => { return browserTypeMap[data] } },
    { prop: 'paste_content', label: 'pasteContent', width: '200' }
  ]
}
export function getForumPostNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'title', label: 'postTitle', width: '200' },
    { prop: 'url', label: 'postUrl', width: '200' }
  ]
}

export function beforeLoadWebBrowsingDetail(row, column) {
  const id = column.property.split('_')[1]
  const hosts = row['items_' + id]
  return {
    condition: {
      host: hosts || '-1'
    }
  }
}

// ================================================= 应用程序运行时长报表 =================================================
export function getAppRunTimeDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'start_time', label: 'processStartTime', width: '150' },
    { prop: 'end_time', label: 'processEndTime', width: '150' },
    { prop: 'run_time', label: 'runTime', width: '150', formatter: (row, data) => { return formatSeconds(data) } },
    { prop: 'sys_user_name', label: 'linuxLoginUser', width: '150' },
    { prop: 'win_title', label: 'processWindowTitle', width: '150' },
    { prop: 'product_name', label: 'productName', width: '150' },
    { prop: 'product_ver', label: 'productVersion', width: '150' },
    { prop: 'process_name', label: 'processName', width: '150' },
    { prop: 'file_desc', label: 'fileDesc', width: '150' },
    { prop: 'company_name', label: 'companyName', width: '150' },
    { prop: 'copyright', label: 'copyright', width: '150' },
    { prop: 'inter_name', label: 'internalName', width: '150' }
  ]
}

export function getComputerWorkTimeDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'record_id', label: 'logRecNum', width: '80' },
    { prop: 'event_id', label: 'eventId', width: '80' },
    { prop: '', label: 'operateEvent', width: '100', formatter: row => {
      return eventTypeOptions[row.event_id]
    } },
    { prop: 'sys_log_source', label: 'logSource', width: '80' },
    { prop: 'log_description', label: 'eventDesc', width: '100' }
  ]
}

export function beforeLoadAppRunTimeDetail(row, column) {
  const id = column.property.split('_')[1]
  const processNames = row['items_' + id]
  return {
    condition: {
      process_name: processNames || '-1'
    }
  }
}

// ================================================= 邮件报表 =================================================
const emailTransceiverFlagMap = {
  1: i18n.t('pages.emailLog_Msg'),
  2: i18n.t('pages.emailLog_Msg1'),
  3: i18n.t('pages.emailLog_Msg2'),
  11: i18n.t('pages.emailLog_Msg4'),
  14: i18n.t('pages.emailLog_Msg5'),
  15: i18n.t('pages.emailLog_Msg6')
}
const attachCountMap = {
  0: i18n.t('pages.inExistAttachment'),
  1: i18n.t('pages.existAttachment')
}
export function getEmailNumDetailCol(viewDetails) {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'attach_count_name', label: 'isAttachment', width: '100', formatter: (row) => attachCountMap[row.attach_count] },
    { prop: 'cmd_type_name', label: 'transceiverFlag', width: '150', formatter: (row) => emailTransceiverFlagMap[row.cmd_type] },
    { prop: 'file_size', label: 'size1', width: '100', formatter: row => { return Math.ceil(parseInt(row.file_size) / 1024) } },
    { prop: 'mail_title', label: 'mailTitle', width: '200', sensitive: true, hidden: true, span: 2 },
    { prop: 'sender', label: 'sender', width: '150', sensitive: true, hidden: true, span: 2 },
    { prop: 'receiver', label: 'addressee', width: '150', sensitive: true, hidden: true },
    { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !viewDetails,
      buttons: [
        { label: 'detail' }
      ]
    }
  ]
}

// ================================================= 征兆报表 =================================================
const severityMap = {
  4: i18n.t('pages.severityOptions4'),
  3: i18n.t('pages.severityOptions3'),
  2: i18n.t('pages.severityOptions2'),
  1: i18n.t('pages.severityOptions1')
}
const lossTypeOptions = {
  10001: i18n.t('pages.doDrip'),
  0: i18n.t('pages.stgLabelFtpUploadFile'),
  1: i18n.t('pages.stgLabelEmailTransferContent'),
  2: i18n.t('pages.usbCopyFile'),
  3: i18n.t('pages.usbCutFile'),
  4: i18n.t('pages.usbSaveFile'),
  5: i18n.t('pages.stgLabelFilePrintContent'),
  7: i18n.t('pages.stgLabelRemoteShareFile'),
  8: i18n.t('pages.stgLabelImSendContent'),
  9: i18n.t('pages.stgLabelImSendFile'),
  11: i18n.t('pages.stgLabelForumPostContent'),
  13: i18n.t('pages.stgLabelCdBurnFile'),
  14: i18n.t('pages.stgLabelWebAccessContent'),
  15: i18n.t('pages.stgLabelWebUploadFile'),
  16: i18n.t('pages.stgLabelWebPasteContent'),
  17: i18n.t('pages.stgLabelLocalShareFile'),
  18: i18n.t('pages.stgLabelMail'),
  20: i18n.t('pages.stgLabelBluetooth'),
  23: i18n.t('pages.stgLabelWebDiskUploadFile'),
  24: i18n.t('pages.stgLabelWebDiskDownloadFile'),
  25: i18n.t('pages.stgLabelImDownloadFile'),
  26: i18n.t('pages.stgLabelImReceiveFile'),
  27: i18n.t('pages.stgLabelAdbSendFile'),
  29: i18n.t('pages.stgLabelMtpSendFile'),
  30: i18n.t('pages.stgLabelCdDownloadFile'),
  31: i18n.t('pages.stgLabelWebDownloadFile'),
  32: i18n.t('pages.stgLabelRemoteOutgoingFile'),
  33: i18n.t('pages.stgLabelRemoteToolUpload'),
  34: i18n.t('pages.stgLabelAISendMsgAware'),
  35: i18n.t('pages.stgLabelAISendFileAware')
}
const typeMap = {
  1: i18n.t('pages.doDrip'),
  2: i18n.t('pages.nomalCheck')
}
function senderIpFormatter(row) {
  let result = ''
  if (row.sender) result += row.sender
  if (row.sender_ip) {
    result += result.length > 0 ? ('(' + row.sender_ip + ')') : row.sender_ip
  }
  return result
}
export function getIllegalLogDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'type', label: 'type', width: '100', formatter: row => {
      return typeMap[row.type]
    } },
    { prop: 'severity_name', label: 'severity', width: '100', formatter: (row) => {
      return severityMap[row.severity]
    } },
    { prop: 'stg_def_name', label: 'contentStg', width: '100' },
    { prop: 'sender_ip', label: 'sender1', width: '150', formatter: senderIpFormatter }
  ]
}
export function getGeneralDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'loss_type_name', label: 'lossType', width: '100', formatter: row => {
      return lossTypeOptions[row.loss_type]
    } },
    { prop: 'severity_name', label: 'severity', width: '80', formatter: (row) => {
      return severityMap[row.severity]
    } },
    { prop: 'file_name', label: 'sensitiveFile', width: '150' },
    { prop: 'stg_def_name', label: 'contentStg', width: '100' },
    { prop: 'sender_ip', label: 'sender1', width: '150', formatter: senderIpFormatter }
  ]
}
export function getDripDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'severity_name', label: 'severity', width: '100', formatter: (row) => {
      return severityMap[row.severity]
    } },
    { prop: 'stg_def_name', label: 'contentStg', width: '100' },
    { prop: 'sender_ip', label: 'sender1', width: '100', formatter: senderIpFormatter }
  ]
}

// ================================================= 即时通讯报表 =================================================
const chatTypeMap = {
  3: i18n.t('pages.qq'),
  23: i18n.t('pages.wechat'),
  25: i18n.t('pages.dingTalk'),
  27: i18n.t('pages.enterpriseWeChat'),
  13: i18n.t('pages.aliTalk'),
  20: i18n.t('pages.feiQ'),
  29: i18n.t('pages.feiShu'),
  10: 'Skype',
  31: i18n.t('pages.tim'),
  32: i18n.t('pages.shiyeLine'),
  100: i18n.t('pages.other')
}
const loginTypeMap = {
  0: i18n.t('pages.login'),
  1: i18n.t('pages.logout')
}
export function getAllChatLogDetailCol(viewDetails) {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'chat_type_name', label: 'chatTypeName', width: '150', formatter: row => {
      return chatTypeMap[row.chat_type]
    } },
    { prop: 'msg_type', label: 'messageType', width: '150', formatter: (row, data) => {
      switch (row.msg_type) {
        case 0:
          return i18n.t('pages.writtenWords')
        case 1:
          return i18n.t('pages.picture')
        case 2:
          return i18n.t('pages.file1')
        default:
          return row.msg_type
      }
    } },
    { prop: 'chat_session_info', label: 'chatWindowName', width: '150', sensitive: true, hidden: true },
    { prop: 'msg', label: 'message', width: '150', sensitive: true, hidden: true },
    { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !viewDetails,
      buttons: [
        { label: 'detail' }
      ]
    }
  ]
}
export function getChatLogDetailCol(viewDetails) {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'chat_type_name', label: 'chatTypeName', width: '150', formatter: (row, data) => {
      return chatTypeMap[row.chat_type]
    } },
    { prop: 'char_num', label: 'charNumSumAll', width: '150' },
    { prop: 'chat_session_info', label: 'chatWindowName', width: '150', sensitive: true, hidden: true },
    { prop: 'msg', label: 'message', width: '150', sensitive: true, hidden: true },
    { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !viewDetails,
      buttons: [
        { label: 'detail' }
      ]
    }
  ]
}
export function getChatImageLogDetailCol(viewDetails) {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'chat_type_name', label: 'chatTypeName', width: '150', formatter: (row, data) => {
      return chatTypeMap[row.chat_type]
    } },
    { prop: 'chat_session_info', label: 'chatWindowName', width: '150', sensitive: true, hidden: true },
    { prop: 'image_name', label: 'pictureName', width: '150', sensitive: true, hidden: true },
    { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !viewDetails,
      buttons: [
        { label: 'detail' }
      ]
    }
  ]
}
export function getChatFileLogDetailCol(viewDetails) {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'chat_type_name', label: 'chatTypeName', width: '150', formatter: (row, data) => {
      return chatTypeMap[row.chat_type]
    } },
    { prop: 'file_size', label: 'maxFileSize1', width: '150' },
    { prop: 'chat_session_info', label: 'chatWindowName', width: '150', sensitive: true, hidden: true },
    { prop: 'file_name', label: 'fileName', width: '150', sensitive: true, hidden: true },
    { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !viewDetails,
      buttons: [
        { label: 'detail' }
      ]
    }
  ]
}
export function getChatLoginLogDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'chat_type_name', label: 'chatTypeName', width: '150', formatter: (row, data) => {
      return chatTypeMap[row.chat_type]
    } },
    { prop: 'login_type_name', label: 'loginType', width: '150', formatter: (row, data) => {
      return loginTypeMap[row.login_type]
    } }
  ]
}

// ================================================= 资产报表 =================================================
const logTypeMap = {
  1: i18n.t('pages.add'),
  2: i18n.t('pages.delete'),
  3: i18n.t('text.edit')
}
const hardwareAssetIdMap = {
  cpuChangeTimesSumAll: 1001,
  moboChangeTimesSumAll: 1002,
  vcChangeTimesSumAll: 1005,
  ramChangeTimesSumAll: 1006,
  hddChangeTimesSumAll: 1007,
  cpuChangeTimesSum: 1001,
  moboChangeTimesSum: 1002,
  vcChangeTimesSum: 1005,
  ramChangeTimesSum: 1006,
  hddChangeTimesSum: 1007
}
const softwareAssetIdMap = {
  osChangeTimesSumAll: 2001,
  avChangeTimesSumAll: 2002,
  spChangeTimesSumAll: 2003,
  appChangeTimesSumAll: 2004,
  osChangeTimesSum: 2001,
  avChangeTimesSum: 2002,
  spChangeTimesSum: 2003,
  appChangeTimesSum: 2004
}
export function getHardwareAssetChangeTimesDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'parent_prop_name', label: 'asset', width: '150' },
    { prop: 'log_type_name', label: 'event', width: '100', formatter: (row) => {
      return logTypeMap[row.log_type]
    } },
    { prop: 'desp', label: 'fileDescription', width: '250' }
  ]
}
export function beforeLoadHardwareAssetChangeTimesDetail(row, column) {
  return {
    colProp: column.property === 'hardwareAssetChangeTimes' ? 'changeTimesSumAll' : column.property,
    keyword1: 0,
    keyword2: hardwareAssetIdMap[column.property]
  }
}
export function getSoftwareAssetChangeTimesDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'parent_prop_name', label: 'asset', width: '150' },
    { prop: 'log_type_name', label: 'event', width: '100', formatter: (row) => {
      return logTypeMap[row.log_type]
    } },
    { prop: 'desp', label: 'fileDescription', width: '250' }
  ]
}
export function beforeLoadSoftwareAssetChangeTimesDetail(row, column) {
  return {
    colProp: column.property === 'softwareAssetChangeTimes' ? 'changeTimesSumAll' : column.property,
    keyword1: 1,
    keyword2: softwareAssetIdMap[column.property]
  }
}

// ================================================= 综合报表 =================================================
const browserTypeMap = {
  1: i18n.t('pages.browserOptions2'),
  2: i18n.t('pages.browserOptions5'),
  3: i18n.t('pages.browserOptions7'),
  4: i18n.t('pages.browserOptions3'),
  5: i18n.t('pages.browserOptions8'),
  6: i18n.t('pages.browserOptions1'),
  7: i18n.t('pages.browserOptions4'),
  8: i18n.t('pages.browserOptions6'),
  9: i18n.t('pages.browserOptions9'),
  100: i18n.t('pages.other')
}
export function getBrowserUploadDetailCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'browser_session_info', label: 'webTitle', width: '100' },
    { prop: 'browser_type_name', label: 'browserType', width: '100', formatter: (row) => {
      return browserTypeMap[row.browser_type]
    } },
    { prop: 'file_name', label: 'fileName1', width: '100' },
    { prop: 'file_size', label: 'maxFileSize1', flag: 'size', width: '200' }
  ]
}
export function beforeLoadMobileDiskOpFileNum(row, column) {
  let property = column.property
  if (column.property === 'mobileStorageFileNumSumAll') {
    property = 'fileNumSumAll'
  }
  if (column.property === 'mobileStorageFileSizeSumAll') {
    property = 'fileNumSumAll'
  }
  return {
    colProp: property
  }
}

// ================================================= 审批报表 =================================================
const procTypeMap = {
  fileDecrypt: i18n.t('pages.decryptionApproval'),
  printOutsend: i18n.t('pages.printOutApproval'),
  offline: i18n.t('pages.offlineApproval'),
  outSend: i18n.t('pages.directOutApproval'),
  changeFileLevel: i18n.t('pages.classifiedApproval'),
  filePrint: i18n.t('pages.printApproval'),
  cancelWatermark: i18n.t('pages.cancelWatermarkApproval'),
  sensitiveFileOutSend: i18n.t('pages.externalApprovalSensitive'),
  fileRelieveJurisdiction: i18n.t('pages.readConversionApproval'),
  behaviorControl: i18n.t('pages.behaviorControlApproval')
}
const auditPassMap = {
  'true': i18n.t('pages.approvalLog_Msg26'),
  'pass': i18n.t('pages.approvalLog_Msg23'),
  'false': i18n.t('pages.approvalLog_Msg28')
}
export function getTrwfeLogCol() {
  return [
    { prop: 'create_time', label: 'operateTime', width: '150' },
    { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'proc_type_name', label: 'floatType', width: '100', formatter: row => {
      return procTypeMap[row.category]
    } },
    { prop: 'audit_pass_name', label: 'approvalResult', width: '100', formatter: row => {
      return auditPassMap[row.audit_pass]
    } }
  ]
}

// ================================================= 流量统计报表 =================================================
export function getNetFlowDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'total_flow_num', label: 'totalFlowKB', width: '150' },
    { prop: 'receive_flow_num', label: 'receivingFlowKB', width: '150' },
    { prop: 'send_flow_num', label: 'sendingFlowKB', width: '150' }
  ]
}

// ================================================= Telnet监控报表 =================================================
const cmdTypeMap = {
  1: 'pages.send',
  3: 'pages.connect'
}
export function getCmdNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'cmd_type_name', label: 'controlCommand', width: '150', formatter: row => i18n.t(cmdTypeMap[row.cmd_type]) },
    { prop: 'ip_arr', label: 'ipAddr', width: '150' },
    { prop: 'cmd_data', label: 'commandLine', width: '150' }
  ]
}

// ================================================= 软件卸载数量报表 =================================================
const taskStatusMap = {
  9: i18n.t('pages.prepareUninstall'),
  10: i18n.t('pages.uninstallFail'),
  11: i18n.t('pages.uninstallComplete'),
  1201: i18n.t('pages.curTaskLog_Msg'),
  1202: i18n.t('pages.curTaskLog_Msg1'),
  1203: i18n.t('pages.curTaskLog_Msg2'),
  1301: i18n.t('pages.curTaskLog_Msg3')
}
export function getAppUninstallNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'file_name', label: 'softwareName', width: '150' },
    { prop: 'task_status_name', label: 'result', width: '150', formatter: row => {
      return taskStatusMap[row.task_status]
    } }
  ]
}

// ================================================= 网盘审计报表 =================================================
const signMap = {
  0: i18n.t('pages.uploadFile1'),
  1: i18n.t('pages.downloadFile'),
  4: i18n.t('pages.uploadFile1'),
  5: i18n.t('pages.downloadFile'),
  6: i18n.t('components.uploadFolder'),
  7: i18n.t('components.downloadFolder')
}
export function getNetworkDiskAuditFileNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'file_name', label: 'fileName', width: '150' },
    { prop: 'file_size', label: 'maxFileSize1', flag: 'size', width: '150', formatter: row => {
      return (row.file_size / 1024.0).toFixed(2)
    } },
    { prop: 'sign', label: 'signName', width: '150', formatter: (row, data) => { return signMap[data] } }
  ]
}

// ================================================= 敏感关键字报表 =================================================
const keywordTypeMap = {
  1: i18n.t('pages.sensitiveKeywordType1'),
  2: i18n.t('pages.sensitiveKeywordType2'),
  3: i18n.t('pages.sensitiveKeywordType3')
}
export function getSensitiveKeywordNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'keyword', label: 'sensitiveKeyword', width: '150' },
    { prop: 'keyword_type', label: 'sensitiveKeywordType', width: '150', formatter: row => { return keywordTypeMap[row.keyword_type] } }
  ]
}

// ================================================= ADB监控报表 =================================================
const actionTypeMap = {
  0: i18n.t('pages.allow'),
  1: i18n.t('pages.forbid')
}
export function getAdbOutgoingFileNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'file_path', label: 'filePath', width: '150' },
    { prop: 'file_name', label: 'fileName', width: '150' },
    { prop: 'file_size', label: 'maxFileSize1', width: '120' },
    { prop: 'action_type', label: 'actionTypeName', width: '120', formatter: row => { return actionTypeMap[row.action_type] || i18n.t('pages.unknown') } },
    { prop: 'serial_num', label: 'adb_log_serial', width: '150' },
    { prop: 'manufacturer', label: 'adb_log_manufacturer', width: '150' }
  ]
}

// ================================================= MTP监控报表 =================================================
export function getMtpOutgoingFileNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'file_name', label: 'fileName', width: '150' },
    { prop: 'file_size', label: 'maxFileSize1', width: '120' },
    { prop: 'action_type', label: 'actionTypeName', width: '120', formatter: row => { return actionTypeMap[row.action_type] || i18n.t('pages.unknown') } },
    { prop: 'src_file_path', label: 'fileSrcPath', width: '150' },
    { prop: 'dest_file_path', label: 'targetPath', width: '150' }
  ]
}

// ================================================= 密级转换报表 =================================================
const opResultMap = {
  0: i18n.t('text.success'),
  1: i18n.t('text.fail'),
  2: i18n.t('pages.secretLevelLog_Msg'),
  3: i18n.t('pages.secretLevelLog_Msg1'),
  4: i18n.t('pages.secretLevelLog_Msg2'),
  5: i18n.t('pages.secretLevelLog_Msg3')
}
export function getSecurityLevelConvertNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'src_dense_desc', label: 'srcLevel', width: '150' },
    { prop: 'dst_dense_desc', label: 'desLevel', width: '150' },
    { prop: 'op_result', label: 'opResult', width: '150', formatter: row => { return opResultMap[row.op_result] } },
    { prop: 'file_path', label: 'fileName', width: '150' }
  ]
}

// ================================================= FTP监控报表 =================================================
const ftpCmdTypeMap = {
  1: i18n.t('pages.downloadFile'),
  2: i18n.t('pages.uploadFile1')
}
export function getFtpFileTransferNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'host', label: 'serverIp1', width: '150' },
    { prop: 'process_name', label: 'processName1', width: '150' },
    { prop: 'cmd_type', label: 'terminalOperateType', width: '150', formatter: row => { return ftpCmdTypeMap[row.cmd_type] } },
    { prop: 'action_type', label: 'actionTypeName', width: '120', formatter: row => { return actionTypeMap[row.action_type] || i18n.t('pages.unknown') } },
    { prop: 'file_path', label: 'filePath', width: '150' },
    { prop: 'file_name', label: 'fileName', width: '150' },
    { prop: 'file_size', label: 'maxFileSize1', width: '120' }
  ]
}

// ================================================= 蓝牙配对报表 =================================================
const bluetoothDevTypeMap = {
  '-1': i18n.t('pages.unknown'),
  0: i18n.t('pages.other'),
  1: i18n.t('pages.computer'),
  2: i18n.t('pages.phone'),
  3: i18n.t('pages.networkAccessPoint'),
  4: i18n.t('pages.audioAndVideo'),
  5: i18n.t('pages.parts'),
  6: i18n.t('pages.imaging'),
  7: i18n.t('pages.wearable'),
  8: i18n.t('pages.toys'),
  9: i18n.t('pages.healthy')
}
const changeTypeMap = {
  1: i18n.t('pages.insert'),
  2: i18n.t('pages.delete')
}
export function getBluetoothParingNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'addr', label: 'deviceAddr', width: '150' },
    { prop: 'dev_name', label: 'blueName', width: '150' },
    { prop: 'dev_type', label: 'devType', width: '150', formatter: row => { return bluetoothDevTypeMap[row.dev_type] } },
    { prop: 'change_type', label: 'changeType', width: '120', formatter: row => { return changeTypeMap[row.change_type] } }
  ]
}

// ================================================= 蓝牙文件传输报表 =================================================
export function getBluetoothFileTransferNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'blue_address', label: 'deviceAddr', width: '150' },
    { prop: 'blue_name', label: 'blueName', width: '150' },
    { prop: 'dev_type', label: 'devType', width: '150', formatter: row => { return bluetoothDevTypeMap[row.dev_type] } },
    { prop: 'action_type', label: 'actionTypeName', width: '120', formatter: row => { return actionTypeMap[row.action_type] || i18n.t('pages.unknown') } },
    { prop: 'file_path', label: 'filePath', width: '200' },
    { prop: 'file_name', label: 'fileName1', width: '150' },
    { prop: 'file_size', label: 'maxFileSize1', width: '120' }
  ]
}

// =================================================WiFi连接报表 =================================================
export function getWifiConnNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'wifi_name', label: 'wifiName', width: '150' },
    { prop: 'wifi_mac', label: 'wifiMac', width: '150' }
  ]
}

// =================================================本地共享报表 =================================================
const localFileShareOpTypeMap = {
  1: i18n.t('pages.fileCreate'),
  2: i18n.t('pages.fileEdit'),
  4: i18n.t('pages.fileRename')
}
const yesOrNoMap = {
  0: i18n.t('text.no'),
  1: i18n.t('text.yes')
}
export function getLocalFileShareNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'file_name', label: 'fileOrDirName', width: '150' },
    { prop: 'file_full_path', label: 'filePath', width: '150' },
    { prop: 'file_size', label: 'maxFileSize1', width: '150' },
    { prop: 'file_op_type', label: 'operateType', width: '150', formatter: row => { return localFileShareOpTypeMap[row.file_op_type] || i18n.t('pages.unknown') } },
    { prop: 'file_type', label: 'isDir', width: '150', formatter: row => { return yesOrNoMap[row.file_type] } }
  ]
}

// =================================================网络共享报表 =================================================
const netFileShareOpTypeMap = {
  1: i18n.t('pages.copyFile'),
  2: i18n.t('pages.cutFile'),
  4: i18n.t('pages.saveFile')
}
export function getNetFileShareNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'file_name', label: 'fileName', width: '150' },
    { prop: 'file_size', label: 'maxFileSize1', width: '150' },
    { prop: 'file_op_type', label: 'operateType', width: '150', formatter: row => { return netFileShareOpTypeMap[row.file_op_type] || i18n.t('pages.unknown') } },
    { prop: 'action_type', label: 'action', width: '150', formatter: row => { return actionTypeMap[row.action_type] || i18n.t('pages.unknown') } }
  ]
}

// ================================================= USB监控报表 =================================================
const usbOutgoingOpTypeMap = {
  1: i18n.t('pages.copyFile'),
  2: i18n.t('pages.cutFile'),
  3: i18n.t('pages.saveFile')
}
export function getUsbOutgoingFileNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'usb_name', label: 'usbName', width: '120' },
    { prop: 'drive_serial', label: 'pnpDeviceId', width: '150' },
    { prop: 'is_white_list', label: 'isWhiteList', width: '120', formatter: row => { return yesOrNoMap[row.is_white_list] } },
    { prop: 'process_name', label: 'processName', width: '100' },
    { prop: 'file_op_type', label: 'operateType', width: '100', formatter: row => { return usbOutgoingOpTypeMap[row.file_op_type] || i18n.t('pages.unknown') } },
    { prop: 'action_type', label: 'actionTypeName', width: '120', formatter: row => { return actionTypeMap[row.action_type] || i18n.t('pages.unknown') } },
    { prop: 'src_file_path', label: 'fileSrcPath', width: '150' },
    { prop: 'dest_file_path', label: 'targetPath', width: '150' },
    { prop: 'file_size', label: 'maxFileSize1', width: '120' }
  ]
}

// ================================================= 计算机节能报表 =================================================
const computerEnergySavingTypeMap = {
  1: i18n.t('pages.energySaving_shutdown'),
  2: i18n.t('pages.energySaving_restart'),
  3: i18n.t('pages.energySaving_closeMonitor'),
  4: i18n.t('pages.energySaving_sleep'),
  5: i18n.t('pages.energySaving_dormancy')
}
export function getComputerEnergySavingNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'exec_type', label: 'execType', width: '150', formatter: row => { return computerEnergySavingTypeMap[row.exec_type] || i18n.t('pages.unknown') } }
  ]
}

// ================================================= 刻录机监控报表 =================================================
export function getCdBurnNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'device_info', label: 'recordingDriverName', width: '150' },
    { prop: 'burn_cd_info', label: 'discName', width: '150' },
    { prop: 'action_type', label: 'actionTypeName', width: '120', formatter: row => { return actionTypeMap[row.action_type] || i18n.t('pages.unknown') } },
    { prop: 'burn_size', label: 'burnFileSize', width: '130', formatter: row => { return (row.burn_size / 100).toFixed(2) } }
  ]
}

// ================================================= 智能备份报表 =================================================
const businessTypeMap = {
  1: i18n.t('pages.fullScan'),
  2: i18n.t('pages.timedScan'),
  3: i18n.t('pages.activeBackup'),
  4: i18n.t('pages.instantBackupCreate'),
  5: i18n.t('pages.instantBackupModify'),
  6: i18n.t('pages.instantBackupRename')
}
export function getSmartBackupNumDetailCol() {
  return [
    { prop: 'create_time', label: 'time', width: '100' },
    { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
    { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
    { prop: 'user_name', label: 'user', width: '100', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
    { prop: 'user_group_name', label: 'userGroup', width: '100', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
    { prop: 'business_type', label: 'bizType', formatter: row => { return businessTypeMap[row.business_type] || i18n.t('pages.unknown') } },
    { prop: 'backup_result', label: i18n.t('pages.backupResult'), formatter: row => { return row.backup_result === 0 ? i18n.t('text.success') : i18n.t('text.fail') } },
    { prop: 'file_size', label: 'maxFileSize2', formatter: row => formatFileSize(row.file_size) },
    { prop: 'file_path', label: 'localFilePath' }
  ]
}
