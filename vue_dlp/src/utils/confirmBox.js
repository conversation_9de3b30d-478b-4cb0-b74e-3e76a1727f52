import Vue from 'vue'
import i18n from '@/lang'
/**
 * 封装confirm
 * @param {String} message
 * @param {String?} title
 * @param {Object?} option 可配置confirm的参数，type字段表明消息类型，可以为success，error，info和warning
 */
export default function confirmBox(message, title, option) {
  const msg = message || i18n.t('components.confirmEnd')
  const t = title || i18n.t('text.prompt')
  const opt = Object.assign({
    confirmButtonText: i18n.t('button.confirm2'),
    cancelButtonText: i18n.t('button.cancel'),
    cancelButtonClass: 'btn-custom-cancel',
    closeOnClickModal: false,
    type: 'warning'
  }, option)
  return Vue.prototype.$confirm(msg, t, opt)
}
