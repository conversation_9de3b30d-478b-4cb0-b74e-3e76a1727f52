import CryptoJS from 'crypto-js'

const Base64 = require('js-base64').Base64

// base64加密特殊方法
export function base64EncodeSpe(str) {
  str = Base64.encode(str)
  // 定义两位数字，然后往a-b的位置中插入一个随机数字变成 str1
  // 然后返回a + str1 + b
  const a = Math.floor(Math.random() * 10)
  const b = Math.floor(Math.random() * 10)
  let index = Math.abs(a - b);
  for (let i = str.length; i < index;) {
    index -= str.length
  }
  const r = Math.floor(Math.random() * 10)
  return a + str.substring(0, index) + r + str.substring(index) + b
}
// base64 解密特殊方法
export function base64DecodeSpe(str) {
  if (str === null || str === undefined) {
    return ''
  }
  const a = parseInt(str.substring(0, 1))
  const b = parseInt(str.substring(str.length - 1, str.length))
  if (Number.isInteger(a) && Number.isInteger(b)) {
    str = str.substring(1, str.length - 1)
    let index = Math.abs(a - b);
    for (let i = str.length; i < index;) {
      index -= str.length
    }
    str = str.substring(0, index) + str.substring(index + 1)
  }
  return Base64.decode(str)
}
// aes加密：key长度需16倍数
export function aesEncode(str, key) {
  const keyUtf8 = CryptoJS.enc.Utf8.parse(key);
  // 加密
  const encrypted = CryptoJS.AES.encrypt(str, keyUtf8, {
    iv: CryptoJS.enc.Utf8.parse('0123456789abcdef'),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
    format: CryptoJS.format.Hex
  });
  return encrypted.toString()
}
// aes解密：key长度需16倍数
export function aesDecode(str, key) {
  const keyUtf8 = CryptoJS.enc.Utf8.parse(key);
  // 解密
  const decrypted = CryptoJS.AES.decrypt(str, keyUtf8, {
    iv: CryptoJS.enc.Utf8.parse('0123456789abcdef'),
    padding: CryptoJS.pad.Pkcs7,
    format: CryptoJS.format.Hex
  });
  return decrypted.toString(CryptoJS.enc.Utf8)
}

// 格式化Aes的key：key长度需16倍数，由于中文转utf8长度会变化，因此需要转换
export function formatAesKey(str1, str2, length) {
  if (!length) {
    length = 16
  }
  let result = ''
  const str = str1 + str2
  for (let i = 0; i < length; i++) {
    const ucode = i < str.length ? str.charCodeAt(i) : 0
    if (ucode > 122 || ucode < 48) {
      result += ucode === 0 ? '0' : String.fromCharCode(48 + ucode % 74)
    } else {
      result += str.charAt(i)
    }
  }
  return result
}
