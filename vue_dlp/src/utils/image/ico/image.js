
/**
 * ICO条目图像（bmp或png）
 */
export class IcoEntryImage {
  /**
   * The constructor of IcoRawImage
   * @param type {string} The image media type, eg: image/bmp, image/png
   * @param buffer {ArrayBuffer} The image file byte buffer.
   */
  constructor(type, buffer) {
    this.type = type
    this._buffer = buffer
  }

  /**
   * @returns {number} The ico entry image width.
   */
  get width() {
    return 0
  }

  /**
   * @returns {number} The ico entry image height.
   */
  get height() {
    return 0
  }

  getBuffer() {
    return this._buffer
  }

  /**
   * @returns {ArrayBuffer} Returns a ArrayBuffer representing the ico entry image.
   */
  getIcoBuffer() {
    return this._buffer
  }

  /**
   * Get pixel data of the ico entry image which resize to the given width and height.
   * @param width {number?} The width to resized.
   * @param height {number?} The height to resized.
   * @returns {Promise<ImageData>} Returns a ImageData representing the ico entry image.
   */
  getImageData(width, height) {
    return Promise.resolve(new ImageData(width || this.width, height || this.height))
  }

  /**
   * @returns {Blob} Returns a Blob representing the ico entry image.
   */
  toBlob() {
    return new Blob([this.getBuffer()], { type: this.type })
  }

  /**
   * @returns {string} Returns a DataURL representing the entry image.
   */
  toDataURL() {
    const codes = new Uint8Array(this.getBuffer())
    const base64 = btoa(String.fromCharCode(...codes))
    return 'data:' + this.type + ';base64,' + base64
  }
}
