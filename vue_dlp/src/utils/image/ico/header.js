/**
 * ICO文件头
 */
export class IcoFileHeader {
  static SIZE_OF = 6

  /**
   * The constructor of IcoFileHeader
   * @param reserved {number} [WORD]保留字段，必须为 0
   * @param type {number} [WORD]资源类型（图标为 1）
   * @param count {number} [WORD]图标中的图像数量
   */
  constructor(reserved = 0, type = 1, count) {
    this.reserved = reserved
    this.type = type
    this.count = count
  }

  /**
   * Create ICO file header from the ArrayBuffer (byte array).
   * @param view {DataView} The DataView of ico file header ArrayBuffer.
   * @returns {IcoFileHeader} ico file header
   */
  static from(view) {
    const reserved = view.getUint16(0, true)
    const type = view.getUint16(2, true)
    const count = view.getUint16(4, true)
    return new IcoFileHeader(reserved, type, count)
  }

  /**
   * Get ico file header data.
   * @returns {ArrayBuffer} ico file header data.
   */
  get data() {
    const buffer = new ArrayBuffer(IcoFileHeader.SIZE_OF)
    const view = new DataView(buffer)
    view.setUint16(0, this.reserved, true)
    view.setUint16(2, this.type, true)
    view.setUint16(4, this.count, true)
    return buffer
  }
}

/**
 * ICO信息头
 */
export class IcoInfoHeader {
  static SIZE_OF = 16

  /**
   * The constructor of IcoInfoHeader
   * @param width {number} [BYTE]图像的宽度，单位为像素
   * @param height {number} [BYTE]图像的高度，单位为像素
   * @param colorCount {number} [BYTE]图像中的颜色数量 （如果多于 8 位深则为 0）
   * @param reserved {number} [BYTE]保留字段
   * @param planes {number} [WORD]色彩平面数
   * @param bitCount {number} [WORD]每像素位数
   * @param bytesInRes {number} [DWORD]图像资源的字节数
   * @param imageOffset {number} [DWORD]图像资源偏移位置
   */
  constructor(width, height, colorCount, reserved, planes, bitCount, bytesInRes, imageOffset) {
    this.width = width
    this.height = height
    this.colorCount = colorCount
    this.reserved = reserved
    this.planes = planes
    this.bitCount = bitCount
    this.bytesInRes = bytesInRes
    this.imageOffset = imageOffset
  }

  /**
   * Create ICO info header from the buffer.
   * @param view {DataView} The ICO info header image buffer.
   */
  static from(view) {
    const width = view.getUint8(0)
    const height = view.getUint8(1)
    const colorCount = view.getUint8(2)
    const reserved = view.getUint8(3)
    const planes = view.getUint16(4, true)
    const bitCount = view.getUint16(6, true)
    const bytesInRes = view.getUint32(8, true)
    const imageOffset = view.getUint32(12, true)
    return new IcoInfoHeader(width, height, colorCount, reserved, planes, bitCount, bytesInRes, imageOffset)
  }

  /**
   * Get ico info header data.
   * @returns {ArrayBuffer} The ico info header data.
   */
  get data() {
    const buffer = new ArrayBuffer(IcoInfoHeader.SIZE_OF)
    const view = new DataView(buffer)
    view.setUint8(0, this.width)
    view.setUint8(1, this.height)
    view.setUint8(2, this.colorCount)
    view.setUint8(3, this.reserved)
    view.setUint16(4, this.planes, true)
    view.setUint16(6, this.bitCount, true)
    view.setUint32(8, this.bytesInRes, true)
    view.setUint32(12, this.imageOffset, true)
    return buffer
  }
}
