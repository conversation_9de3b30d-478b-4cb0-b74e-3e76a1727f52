import { BmpImage } from '../bmp'
import { PngImage } from '../png'

const BMP_MAGIC_LE = 0x00000028
const PNG_MAGIC_LE = 0x474E5089
const PNG_MAGIC2_LE = 0x0A1A0A0D

/**
 * ICO图像条目
 */
export class IcoEntry {
  /**
   * The constructor of IcoEntry
   * @param header {IcoInfoHeader}
   * @param image {BmpImage|PngImage}
   */
  constructor(header, image) {
    this.header = header
    this.image = image
  }

  /**
   * Create IcoEntry from the icon buffer.
   * @param header {IcoInfoHeader}
   * @param buffer {ArrayBuffer}
   * @returns {IcoEntry}
   */
  static from(header, buffer) {
    const { bytesInRes, imageOffset } = header
    const image = parseEntryImage(buffer, imageOffset, bytesInRes)
    return new IcoEntry(header, image)
  }

  /**
   * @returns {number} The ico entry image width.
   */
  get width() {
    return this.image.width || this.header.width || 256
  }

  /**
   * @returns {number} The ico entry image width.
   */
  get height() {
    return this.image.height || this.header.height || 256
  }

  /**
   * @returns {number} The ico entry image color count.
   */
  get colorCount() {
    const bpp = this.header.colorCount === 32 ? 24 : this.header.colorCount
    return bpp < 0 ? -1 : (1 << bpp)
  }
}

/**
 * Parse ico entry image from the icon buffer.
 * @param buffer {ArrayBuffer} The ico buffer.
 * @param offset {number} The offset of buffer to read the entry image.
 * @param length {number} The entry image data length.
 * @returns {BmpImage|PngImage} Parsed ico entry image (bmp image or png image).
 */
function parseEntryImage(buffer, offset, length) {
  const view = new DataView(buffer, offset, length)
  const magic = view.getUint32(0, true)
  // check for BMP magic header
  if (magic === BMP_MAGIC_LE) {
    return BmpImage.from(view)
  }
  // check for PNG magic header and that image height and width =
  // 0 = 256 -> Vista format
  if (magic === PNG_MAGIC_LE) {
    const magic2 = view.getUint32(4, true)
    if (magic2 === PNG_MAGIC2_LE) {
      return PngImage.from(view)
    }
  }
  throw new TypeError('Unrecognized icon format for image magic number: ' + magic)
}
