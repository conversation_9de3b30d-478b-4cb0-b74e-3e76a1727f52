import { IcoEntry } from './entry'
import { IcoFileHeader, IcoInfoHeader } from './header'
import { readAsArrayBuffer, readAsDataURL } from '@/utils/blob'

/**
 * ICO图像
 */
export class IcoImage {
  /**
   * The constructor of IcoImage
   * @param header {IcoFileHeader} The ico file header.
   * @param entries {IcoEntry[]} The entries of ico file.
   */
  constructor(header, entries) {
    this.header = header
    this.entries = entries
  }

  /**
   * Create IcoImage from the icon buffer.
   * @param buffer {ArrayBuffer} The ico file buffer.
   * @returns {IcoImage} The ico image.
   */
  static from(buffer) {
    let pos = 0
    const fileHeader = IcoFileHeader.from(new DataView(buffer, pos, IcoFileHeader.SIZE_OF))
    pos += IcoFileHeader.SIZE_OF

    const infoHeaders = []
    for (let i = 0; i < fileHeader.count; i++) {
      infoHeaders.push(IcoInfoHeader.from(new DataView(buffer, pos, IcoInfoHeader.SIZE_OF)))
      pos += IcoInfoHeader.SIZE_OF
    }
    const entries = infoHeaders.map(infoHeader => IcoEntry.from(infoHeader, buffer))
    return new IcoImage(fileHeader, entries)
  }

  /**
   * Create IcoImage from the icon file.
   * @param file {File|Blob} The icon file.
   * @returns {Promise<IcoImage>} The ico image.
   */
  static fromFile(file) {
    return readAsArrayBuffer(file).then(IcoImage.from)
  }

  /**
   * @returns {Blob} Returns a Blob representing the ico file.
   */
  toBlob() {
    const blobParts = [this.header.data]
    for (let i = 0; i < this.entries.length; i++) {
      blobParts.push(this.entries[i].header.data)
    }
    for (let i = 0; i < this.entries.length; i++) {
      blobParts.push(this.entries[i].image.getIcoBuffer())
    }
    return new Blob(blobParts, { type: 'image/x-icon' })
  }

  /**
   * @returns {Promise<string>} Returns a data-URI Promise representing the ico file.
   */
  toDataURL() {
    return readAsDataURL(this.toBlob())
  }
}
