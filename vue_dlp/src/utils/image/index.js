export { IcoImage } from './ico'
export { BmpImage } from './bmp'
export { PngImage } from './png'

/**
 * 常见图像文件类型
 */
export const IMAGE_FILE_FORMATS = {
  // 动态便携式网络图像
  APNG: {
    name: 'Animated Portable Network Graphics',
    mimeType: 'image/apng',
    fileExtension: '.apng',
    compression: 'Lossless',
    supportedBrowsers: 'Chrome, Edge, Firefox, Opera, Safari'
  },
  // AV1 图像文件格式
  AVIF: {
    name: 'AV1 Image File Format',
    mimeType: 'image/avif',
    fileExtension: '.avif',
    compression: 'Lossy and lossless',
    supportedBrowsers: 'Chrome, Firefox (still images only: animated images not implemented), Opera, Safari'
  },
  // 位图文件
  BMP: {
    name: 'Bitmap file',
    mimeType: 'image/bmp',
    fileExtension: '.bmp',
    compression: 'Several compression methods are supported, including lossy or lossless algorithms',
    supportedBrowsers: 'Chrome, Edge, Firefox, IE, Opera, Safari'
  },
  // 图像互换格式
  GIF: {
    name: 'Graphics Interchange Format',
    mimeType: 'image/gif',
    fileExtension: '.gif',
    compression: 'Lossless (LZW)',
    supportedBrowsers: 'Chrome, Edge, Firefox, IE, Opera, Safari.'
  },
  // 微软图标
  ICO: {
    name: 'Microsoft Icon',
    mimeType: 'image/x-icon',
    fileExtension: '.ico, .cur',
    compression: 'BMP-format icons nearly always use lossless compression, but lossy methods are available. PNG icons are always compressed losslessly.',
    supportedBrowsers: 'Chrome, Edge, Firefox, IE, Opera, Safari'
  },
  // 联合影像专家小组图像
  JPEG: {
    name: 'Joint Photographic Expert Group image',
    mimeType: 'image/jpeg',
    fileExtension: '.jpg, .jpeg, .jfif, .pjpeg, .pjp',
    compression: 'Lossy; based on the discrete cosine transform',
    supportedBrowsers: 'Chrome, Edge, Firefox, IE, Opera, Safari.'
  },
  // 便携式网络图像
  PNG: {
    name: 'Portable Network Graphics',
    mimeType: 'image/png',
    fileExtension: '.png',
    compression: 'Lossless, optionally indexed color like GIF',
    supportedBrowsers: 'Chrome, Edge, Firefox, IE, Opera, Safari.'
  },
  // 可缩放矢量图形
  SVG: {
    name: 'Scalable Vector Graphics',
    mimeType: 'image/svg+xml',
    fileExtension: '.svg',
    compression: 'SVG source may be compressed during transit using HTTP compression techniques, or on disk as an .svgz file.',
    supportedBrowsers: 'Chrome, Edge, Firefox, IE, Opera, Safari.'
  },
  // 标签图像文件格式
  TIFF: {
    name: 'Tagged Image File Format',
    mimeType: 'image/tiff',
    fileExtension: '.tif, .tiff',
    compression: 'Most TIFF files are uncompressed, but lossless PackBits and LZW compression are supported, as is lossy JPEG compression.',
    supportedBrowsers: 'Safari'
  },
  // 万维网图像格式
  WebP: {
    name: 'Web Picture format',
    mimeType: 'image/webp',
    fileExtension: '.webp',
    compression: 'Lossless (Huffman, LZ77, or color cache codes) or lossy (VP8).',
    supportedBrowsers: 'Chrome, Edge, Firefox, Opera, Safari'
  }
}

/**
 * 是否是图像 DataURL
 * @param content {string} DataURL 文本内容
 * @param filter {function?} 图像格式过滤（可用于筛选受支持的格式），若为空则默认支持所有图像格式
 * @returns {boolean}
 */
export function isImageDataURL(content, filter) {
  // data:image/png;base64,iVBORxxxxxx
  if ('string' !== typeof content || content.length < 30 || !content.startsWith('data:image/')) {
    return false
  }
  const semicolonIndex = content.indexOf(';')
  if (semicolonIndex < 14) {
    return false
  }
  const base64Mark = content.slice(semicolonIndex + 1, semicolonIndex + 8)
  if ('base64,' !== base64Mark) {
    return false
  }
  const mimeType = content.slice(5, semicolonIndex)
  if ('function' !== typeof filter) {
    return Object.values(IMAGE_FILE_FORMATS).map(imageFormat => imageFormat.mimeType).indexOf(mimeType) < 0
  }
  return Object.entries(IMAGE_FILE_FORMATS).filter(entry => filter(entry[0]) && entry[1].mimeType === mimeType).length > 0
}

/**
 * 获取图像的像素数据
 * @param image {CanvasImageSource} Canvas图像源
 * @param width {number?} 图像宽度
 * @param height {number?} 图像高度
 * @returns {ImageData}
 */
export function getImageData(image, width, height) {
  const canvas = document.createElement('canvas')
  canvas.width = width || image.width
  canvas.height = height || image.height
  const context = canvas.getContext('2d')
  context.drawImage(image, 0, 0, canvas.width, canvas.height)
  return context.getImageData(0, 0, canvas.width, canvas.height)
}

/**
 * 加载图像
 * @param file {File|Blob} 图像数据文件
 * @returns {Promise<HTMLImageElement>}
 */
export function loadImage(file) {
  return new Promise((resolve, reject) => {
    const url = URL.createObjectURL(file)
    const image = new Image()
    image.onload = () => {
      resolve(image)
      URL.revokeObjectURL(url)
    }
    image.onerror = e => {
      URL.revokeObjectURL(url)
      reject(e)
    }
    image.src = url
  })
}

/**
 * 加载图像的像素数据
 * @param image {ImageBitmapSource} ImageBitmap图像源
 * @param width {number?} 图像宽度
 * @param height {number?} 图像高度
 * @returns {Promise<ImageData>}
 */
export function loadImageData(image, width, height) {
  return createImageBitmap(image).then(imageBitmap => getImageData(imageBitmap, width, height))
}
