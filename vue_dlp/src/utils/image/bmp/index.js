import { <PERSON><PERSON><PERSON><PERSON>Header, BmpHeader } from './header'
import { IcoEntryImage } from '../ico/image'
import { decodePaletteBmp, decodeTrueColorBmp } from './decode'
import { loadImageData } from '../index'

/**
 * BMP图像（位图）
 */
export class BmpImage extends IcoEntryImage {
  /**
   * The constructor of BmpImage
   * @param header {BmpHeader} The bmp header.
   * @param buffer {ArrayBuffer} The bmp image file buffer.
   */
  constructor(header, buffer) {
    super('image/bmp', buffer)
    this.header = header
  }
  /**
   * Create BmpImage from the bmp buffer.
   * @param view {DataView} The view of bmp buffer.
   * @returns {BmpImage} The bmp image.
   */
  static from(view) {
    const header = BmpHeader.from(view)
    const buffer = view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength)
    return new BmpImage(header, buffer)
  }

  get width() {
    return this.header.width
  }

  get height() {
    return this.header.height
  }

  getBuffer() {
    if (!this.header.isIcoEntry()) {
      return this._buffer
    }
    const fhdr = new BmpFileHeader('BM', BmpFileHeader.SIZE_OF + this._buffer.byteLength)
    const buffer = new ArrayBuffer(fhdr.size)
    fhdr.writeTo(buffer)
    const view = new DataView(buffer)
    const _view = new DataView(this._buffer)
    for (let i = 0; i < _view.byteLength; i++) {
      view.setUint8(BmpFileHeader.SIZE_OF + i, _view.getUint8(i))
    }
    // correct info header height
    view.setUint32(BmpFileHeader.SIZE_OF + 8, this.height, true)
    return buffer
  }

  getIcoBuffer() {
    if (this.header.isIcoEntry()) {
      return this._buffer
    }
    const buf = this._buffer.slice(BmpFileHeader.SIZE_OF)
    const view = new DataView(buf)
    view.setUint32(8, this.height * 2, true)
    return buf
  }

  /**
   * Get pixel data of the bmp image which resize to the given width and height.
   * @param resizeW {number?} The width to resized.
   * @param resizeH {number?} The height to resized.
   * @returns {Promise<ImageData>} Returns a ImageData representing the bmp image.
   */
  getImageData(resizeW, resizeH) {
    const { width, height, colorDepth, colorCount } = this.header
    const byteOffset = this.header.sizeof
    const bitmapData = new Uint8Array(this._buffer, byteOffset, this._buffer.byteLength - byteOffset)
    const icon = this.header.isIcoEntry()
    const result = colorCount
      ? decodePaletteBmp(bitmapData, { width, height, colorDepth, colorCount, icon })
      : decodeTrueColorBmp(bitmapData, { width, height, colorDepth, icon })
    const imageData = new ImageData(result, width, height)
    if (resizeW && resizeH) {
      return loadImageData(imageData, resizeW, resizeH)
    }
    return Promise.resolve(imageData)
  }
}
