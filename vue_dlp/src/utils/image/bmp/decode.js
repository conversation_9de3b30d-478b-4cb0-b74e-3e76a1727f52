// see decode-bmp
// https://github.com/LinusU/decode-bmp/blob/master/index.js
export class Bitmap {
  constructor(data, offset, { width, height, colorDepth, format }) {
    this.format = format
    this.offset = offset
    this.depth = colorDepth
    this.stride = Math.ceil(width * this.depth / 8 / 4) * 4
    this.size = (this.stride * height)
    this.data = data.slice(this.offset, this.offset + this.size)

    if (this.size !== this.data.byteLength) {
      throw new Error('Truncated bitmap data')
    }
  }

  get(x, y, channel) {
    const idx = this.format.indexOf(channel)

    if (this.depth === 1) {
      const slice = this.data[(y * this.stride) + (x / 8 | 0)]
      const mask = 1 << (7 - (x % 8))

      return (slice & mask) >> (7 - (x % 8))
    }

    if (this.depth === 2) {
      const slice = this.data[(y * this.stride) + (x / 4 | 0)]
      const mask = 3 << (6 - (x % 4) * 2)

      return (slice & mask) >>> (6 - (x % 4) * 2)
    }

    if (this.depth === 4) {
      const slice = this.data[(y * this.stride) + (x / 2 | 0)]
      const mask = 15 << (4 - (x % 2) * 4)

      return (slice & mask) >>> (4 - (x % 2) * 4)
    }

    return this.data[(y * this.stride) + (x * (this.depth / 8)) + idx]
  }
}

export function decodeTrueColorBmp(data, { width, height, colorDepth, icon }) {
  if (colorDepth !== 32 && colorDepth !== 24) {
    throw new Error(`A color depth of ${colorDepth} is not supported`)
  }

  const xor = new Bitmap(data, 0, { width, height, colorDepth, format: 'BGRA' })
  const and = (colorDepth === 24 && icon)
    ? new Bitmap(data, xor.offset + xor.size, { width, height, colorDepth: 1, format: 'A' })
    : null

  const result = new Uint8Array(width * height * 4)

  let idx = 0
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      result[idx++] = xor.get(x, height - y - 1, 'R')
      result[idx++] = xor.get(x, height - y - 1, 'G')
      result[idx++] = xor.get(x, height - y - 1, 'B')

      if (colorDepth === 32) {
        result[idx++] = xor.get(x, height - y - 1, 'A')
      } else {
        result[idx++] = and && and.get(x, height - y - 1, 'A') ? 0 : 255
      }
    }
  }

  return new Uint8ClampedArray(result.buffer, result.byteOffset, result.byteLength)
}

export function decodePaletteBmp(data, { width, height, colorDepth, colorCount, icon }) {
  if (colorDepth !== 8 && colorDepth !== 4 && colorDepth !== 2 && colorDepth !== 1) {
    throw new Error(`A color depth of ${colorDepth} is not supported`)
  }

  const colors = new Bitmap(data, 0, { width: colorCount, height: 1, colorDepth: 32, format: 'BGRA' })
  const xor = new Bitmap(data, colors.offset + colors.size, { width, height, colorDepth, format: 'C' })
  const and = icon ? new Bitmap(data, xor.offset + xor.size, { width, height, colorDepth: 1, format: 'A' }) : null

  const result = new Uint8Array(width * height * 4)

  let idx = 0
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const colorIndex = xor.get(x, height - y - 1, 'C')

      result[idx++] = colors.get(colorIndex, 0, 'R')
      result[idx++] = colors.get(colorIndex, 0, 'G')
      result[idx++] = colors.get(colorIndex, 0, 'B')
      result[idx++] = and && and.get(x, height - y - 1, 'A') ? 0 : 255
    }
  }

  return new Uint8ClampedArray(result.buffer, result.byteOffset, result.byteLength)
}
