/**
 * BMP图像头信息
 */
export class BmpHeader {
  /**
   * The constructor of PngFileHeader
   * @param fhdr {BmpFileHeader} BMP文件头
   * @param ihdr {BmpInfoHeader} BMP信息头
   */
  constructor(fhdr, ihdr) {
    this.fhdr = fhdr
    this.ihdr = ihdr
  }

  /**
   * Create BmpHeader from the bmp buffer.
   * @param view {DataView} The view of bmp buffer.
   * @param offset {number} The byte offset of bmp buffer.
   * @returns {BmpHeader} The bmp file header.
   */
  static from(view, offset = 0) {
    const type = view.getUint16(offset, true)
    if (Object.values(BmpFileHeader.BMP_MAGICS).includes(type)) {
      const fhdr = BmpFileHeader.from(view, offset)
      const ihdr = BmpInfoHeader.from(view, offset + BmpFileHeader.SIZE_OF)
      return new BmpHeader(fhdr, ihdr)
    }
    return new BmpHeader(null, BmpInfoHeader.from(view, offset))
  }

  get sizeof() {
    let size = BmpInfoHeader.SIZE_OF
    if (this.fhdr) {
      size += BmpFileHeader.SIZE_OF
    }
    return size
  }

  /**
   * @returns {number} The bmp image width.
   */
  get width() {
    return this.ihdr.width
  }

  /**
   * @returns {number} The bmp image height.
   */
  get height() {
    return this.isIcoEntry() ? this.ihdr.height / 2 : this.ihdr.height
  }

  /**
   * Get bmp image color depth.
   * @returns {number} The bmp image color depth.
   */
  get colorDepth() {
    return this.ihdr.bitCount
  }

  /**
   * Get bmp image color count.
   * @returns {number} The bmp image color count.
   */
  get colorCount() {
    if (this.ihdr.clrUsed === 0 && this.colorDepth <= 8) {
      return (1 << this.colorDepth)
    }
    return this.ihdr.clrUsed
  }

  /**
   * @returns {boolean} Returns the bmp image is one entry of a ico image file or not.
   */
  isIcoEntry() {
    return !this.fhdr
  }
}

/**
 * 位图文件头
 */
export class BmpFileHeader {
  static SIZE_OF = 14
  /**
   * BMP文件前两个字节保存位图文件的标识符，用于标识BMP和DIB文件的魔数，一般为0x42 0x4D，即ASCII的BM。
   * 以下为可能的取值：
   * BM – Windows 3.1x, 95, NT, … etc.
   * BA – OS/2 struct Bitmap Array
   * CI – OS/2 struct Color Icon
   * CP – OS/2 const Color Pointer
   * IC – OS/2 struct Icon
   * PT – OS/2 Pointer
   */
  static BMP_MAGICS = {
    BM: 0x4d42,
    BA: 0x4142,
    CI: 0x4943,
    CP: 0x5043,
    IC: 0x4349,
    PT: 0x5450
  }

  /**
   * The constructor of BmpFileHeader
   * @param type {string} [WORD]标识：42 4D（ascii码为BM）
   * @param size {number} [DWORD]整个BMP文件的大小
   * @param reserved {number} [DWORD]保留字，0
   * @param offBits {number} [DWORD]位图数据位置的地址偏移，也就是起始地址。即 位图文件头+位图信息头+调色板 的大小：54（36 00 00 00）
   */
  constructor(type = 'BM', size, reserved = 0, offBits = 54) {
    this.type = type
    this.size = size
    this.reserved = reserved
    this.offBits = offBits
  }

  /**
   * Create BmpFileHeader from the bmp buffer.
   * @param view {DataView} The view of bmp buffer.
   * @param offset {number} The byte offset of bmp buffer.
   * @returns {BmpFileHeader} The bmp file header.
   */
  static from(view, offset = 0) {
    const type = String.fromCharCode(view.getUint8(offset), view.getUint8(offset + 1))
    if (!BmpFileHeader.BMP_MAGICS[type]) {
      throw new TypeError('Unsupported file header type: ' + type);
    }
    const size = view.getUint32(offset + 2, true)
    const reserved = view.getUint32(offset + 6, true)
    const offBits = view.getUint32(offset + 10, true)
    return new BmpFileHeader(type, size, reserved, offBits)
  }

  /**
   * Get bmp file header data.
   * @returns {ArrayBuffer} bmp file header data.
   */
  get data() {
    const buffer = new ArrayBuffer(BmpFileHeader.SIZE_OF)
    this.writeTo(buffer)
    return buffer
  }

  /**
   * Write bmp file header data to a buffer.
   * @param buffer {ArrayBuffer} The buffer to be written.
   * @param offset {number} The index in the buffer at which the values are to be written.
   */
  writeTo(buffer, offset = 0) {
    const view = new DataView(buffer)
    view.setUint16(offset, BmpFileHeader.BMP_MAGICS[this.type], true)
    view.setUint32(offset + 2, this.size, true)
    view.setUint32(offset + 6, this.reserved, true)
    view.setUint32(offset + 10, this.offBits, true)
  }
}

/**
 * 位图信息头
 */
export class BmpInfoHeader {
  static SIZE_OF = 40
  /**
   * 位图数据允许的值是0、1、2、3、4、5。
   * 0 - 没有压缩（也用BI_RGB表示）
   * 1 - 行程长度编码 8位/像素（也用BI_RLE8表示）
   * 2 - 行程长度编码4位/像素（也用BI_RLE4表示）
   * 3 - Bit field（也用BI_BITFIELDS表示）
   * 4 - JPEG图像（也用BI_JPEG表示）
   * 5 - PNG图像（也用BI_PNG表示）
   */
  static BMP_COMPRESS_METHODS = {
    BI_RGB: 0,
    BI_RLE8: 1,
    BI_RLE4: 2,
    BI_BITFIELDS: 3,
    BI_JPEG: 4,
    BI_PNG: 5
  }

  /**
   * The constructor of BmpInfoHeader
   * @param size {number} [DWORD]位图信息头的大小：40（28 00 00 00）
   * @param width {number} [DWORD]位图的宽度，单位是像素
   * @param height {number} [DWORD]位图的高度，单位是像素
   * @param planes {number} [WORD]色彩平面数量，固定值1
   * @param bitCount {number} [WORD]每个像素的位数：1-黑白图，4-16色，8-256色，16 24-真彩色，32-带透明度Alpha通道的真彩色
   * @param compression {number} [DWORD]压缩方式，BI_RGB(0)为不压缩
   * @param sizeImage {number} [DWORD]位图全部像素占用的字节数，BI_RGB时可设为0
   * @param xPelsPerMeter {number} [DWORD]水平分辨率(像素/米)
   * @param yPelsPerMeter {number} [DWORD]垂直分辨率(像素/米)
   * @param clrUsed {number} [DWORD]位图使用的颜色数：如果为0，则颜色数为2的biBitCount次方
   * @param clrImportant {number} [DWORD]重要的颜色数，0代表所有颜色都重要
   */
  constructor(size = 40, width, height, planes = 1, bitCount, compression = 0, sizeImage, xPelsPerMeter, yPelsPerMeter, clrUsed, clrImportant) {
    this.size = size
    this.width = width
    this.height = height
    this.planes = planes
    this.bitCount = bitCount
    this.compression = compression
    this.sizeImage = sizeImage
    this.xPelsPerMeter = xPelsPerMeter
    this.yPelsPerMeter = yPelsPerMeter
    this.clrUsed = clrUsed
    this.clrImportant = clrImportant
  }

  /**
   * Create bmp info header from the buffer.
   * @param view {DataView} The view of bmp buffer.
   * @param offset {number} The byte offset of bmp buffer.
   * @returns {BmpInfoHeader} The bmp info header.
   */
  static from(view, offset = 0) {
    const size = view.getUint32(offset, true)
    const width = view.getUint32(offset + 4, true)
    const height = view.getUint32(offset + 8, true)
    const planes = view.getUint16(offset + 12, true)
    const bitCount = view.getUint16(offset + 14, true)
    const compression = view.getUint32(offset + 16, true)
    const sizeImage = view.getUint32(offset + 20, true)
    const xPelsPerMeter = view.getUint32(offset + 24, true)
    const yPelsPerMeter = view.getUint32(offset + 28, true)
    const clrUsed = view.getUint32(offset + 32, true)
    const clrImportant = view.getUint32(offset + 36, true)
    return new BmpInfoHeader(size, width, height, planes, bitCount, compression, sizeImage, xPelsPerMeter, yPelsPerMeter, clrUsed, clrImportant)
  }

  /**
   * Get bmp file header data.
   * @returns {ArrayBuffer} The bmp file header data.
   */
  get data() {
    const buffer = new ArrayBuffer(BmpInfoHeader.SIZE_OF)
    const view = new DataView(buffer)
    view.setUint32(0, this.size, true)
    view.setUint32(4, this.width, true)
    view.setUint32(8, this.height, true)
    view.setUint16(12, this.planes, true)
    view.setUint16(14, this.bitCount, true)
    view.setUint32(16, this.compression, true)
    view.setUint32(20, this.sizeImage, true)
    view.setUint32(24, this.xPelsPerMeter, true)
    view.setUint32(28, this.yPelsPerMeter, true)
    view.setUint32(32, this.clrUsed, true)
    view.setUint32(36, this.clrImportant, true)
    return buffer
  }
}
