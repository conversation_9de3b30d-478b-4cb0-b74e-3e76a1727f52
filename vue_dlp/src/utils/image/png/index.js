import { Png<PERSON>eader } from './header'
import { IcoEntryImage } from '../ico/image'
import { loadImageData } from '../index'

/**
 * PNG图像
 */
export class PngImage extends IcoEntryImage {
  /**
   * The constructor of PngImage
   * @param header {PngHeader} The png header.
   * @param buffer {ArrayBuffer} The png image file buffer.
   */
  constructor(header, buffer) {
    super('image/png', buffer)
    this.header = header
  }

  /**
   * Create PngImage from the png buffer.
   * @param view {DataView} The view of png buffer.
   * @returns {PngImage} The png image.
   */
  static from(view) {
    const header = PngHeader.from(view)
    const buffer = view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength)
    return new PngImage(header, buffer)
  }

  /**
   * @returns {number} The png image width.
   */
  get width() {
    return this.header.width
  }

  /**
   * @returns {number} The png image height.
   */
  get height() {
    return this.header.height
  }

  /**
   * Get pixel data of the png image which resize to the given width and height.
   * @param width {number?} The width to resized.
   * @param height {number?} The height to resized.
   * @returns {Promise<ImageData>} Returns a ImageData representing the png image.
   */
  getImageData(width, height) {
    return loadImageData(this.toBlob(), width || this.width, height || this.height)
  }
}
