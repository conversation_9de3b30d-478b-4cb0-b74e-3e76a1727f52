/**
 * PNG图像头信息
 */
export class PngHeader {
  static SIZE_OF = 33

  /**
   * The constructor of PngFileHeader
   * @param fhdr {PngFileHeader} PNG文件头
   * @param ihdr {PngInfoHeader} PNG信息头
   */
  constructor(fhdr, ihdr) {
    this.fhdr = fhdr
    this.ihdr = ihdr
  }

  /**
   * Create PngHeader from the png buffer.
   * @param view {DataView} The view of png buffer.
   * @param offset {number} The byte offset of png buffer.
   * @returns {PngHeader} The png header.
   */
  static from(view, offset = 0) {
    const fhdr = PngFileHeader.from(view, offset)
    const ihdr = PngInfoHeader.from(view, offset + 8)
    return new PngHeader(fhdr, ihdr)
  }

  /**
   * @returns {number} The png image width.
   */
  get width() {
    return this.ihdr.ihdrSection.width
  }

  /**
   * @returns {number} The png image height.
   */
  get height() {
    return this.ihdr.ihdrSection.height
  }
}

/**
 * PNG文件头
 */
export class PngFileHeader {
  static SIZE_OF = 8
  static IDENTIFIER_1 = 0x89504E47
  static IDENTIFIER_2 = 0x0D0A1A0A

  /**
   * The constructor of PngFileHeader
   * @param identifier1 {number} [DWORD]文件标识1：89 50 4E 47 （\x89 P N G）
   * @param identifier2 {number} [DWORD]文件标识2：0D 0A 1A 0A （\r \n SUB(Substitute) \n）
   */
  constructor(identifier1 = PngFileHeader.IDENTIFIER_1, identifier2 = PngFileHeader.IDENTIFIER_2) {
    this.identifier1 = identifier1
    this.identifier2 = identifier2
  }

  /**
   * Create PngFileHeader from the png buffer.
   * @param view {DataView} The view of png buffer.
   * @param offset {number} The byte offset of png buffer.
   * @returns {PngFileHeader} The png file header.
   */
  static from(view, offset = 0) {
    const identifier1 = view.getUint32(offset)
    const identifier2 = view.getUint32(offset + 4)
    return new PngFileHeader(identifier1, identifier2)
  }
}

/**
 * PNG信息头（包含IHDR数据块）
 */
export class PngInfoHeader {
  static SIZE_OF = 25

  /**
   * The constructor of PngInfoHeader
   * @param length {number} [DWORD]IHDR数据块中数据域的长度：13（00 00 00 0D）
   * @param chunkTypeCode {number} [DWORD]数据块类型码：IHDR标识 49 48 44 52（ascii码为IHDR）
   * @param ihdrSection {IHDRSection} [13BYTE]IHDR数据块信息
   * @param crc {number} [DWORD]循环冗余检测，存储用来检测是否有错误的循环冗余码。CRC(cyclic redundancy check)域中的值是对Chunk Type Code域和Chunk Data域中的数据进行计算得到的。
   */
  constructor(length = IHDRSection.SIZE_OF, chunkTypeCode = 0x49484452, ihdrSection, crc) {
    this.length = length
    this.chunkTypeCode = chunkTypeCode
    this.ihdrSection = ihdrSection
    this.crc = crc
  }

  /**
   * Create PngInfoHeader from the png buffer.
   * @param view {DataView} The view of png buffer.
   * @param offset {number} The byte offset of png buffer.
   * @returns {PngInfoHeader} The png info header.
   */
  static from(view, offset = 0) {
    const length = view.getUint32(offset)
    const chunkTypeCode = view.getUint32(offset + 4)
    const ihdrSection = IHDRSection.from(view, offset + 8)
    const crc = view.getUint32(offset + 21)
    return new PngInfoHeader(length, chunkTypeCode, ihdrSection, crc)
  }
}

/**
 * IHDR数据块
 */
class IHDRSection {
  static SIZE_OF = 13

  /**
   * The constructor of IHDRSection
   * @param width {number} [DWORD]图像宽度，以像素为单位
   * @param height {number} [DWORD]图像高度，以像素为单位
   * @param bitDepth {number} [BYTE]图像深度：索引彩色图像：1，2，4或8 灰度图像：1，2，4，8或16 真彩色图像：8或16
   * @param colorType {number} [BYTE]颜色类型：0： 灰度图像, 1，2，4，8或16 2： 真彩色图像，8或16 3：索引彩色图像，1，2，4或8 4：带α通道数据的灰度图像，8或16 6：带α通道数据的真彩色图像，8或16
   * @param compression {number} [BYTE]压缩方法(LZ77派生算法)： PNG Spec规定此处总为0（非0值为将来使用更好的压缩方法预留），表示使用deflate压缩方法(LZ77派生算法)
   * @param filter {number} [BYTE]滤波器方法
   * @param interlace {number} [BYTE]隔行扫描方法：0：非隔行扫描 1： Adam7(由Adam M. Costello开发的7遍隔行扫描方法)
   */
  constructor(width, height, bitDepth, colorType, compression = 0, filter, interlace) {
    this.width = width
    this.height = height
    this.bitDepth = bitDepth
    this.colorType = colorType
    this.compression = compression
    this.filter = filter
    this.interlace = interlace
  }

  /**
   * Create IHDRSection from the png buffer.
   * @param view {DataView} The view of png buffer.
   * @param offset {number} The byte offset of png buffer.
   * @returns {IHDRSection} The png IHDR section.
   */
  static from(view, offset = 0) {
    const width = view.getUint32(offset)
    const height = view.getUint32(offset + 4)
    const bitDepth = view.getUint8(offset + 8)
    const colorType = view.getUint8(offset + 9)
    const compression = view.getUint8(offset + 10)
    const filter = view.getUint8(offset + 11)
    const interlace = view.getUint8(offset + 12)
    return new IHDRSection(width, height, bitDepth, colorType, compression, filter, interlace)
  }
}
