import UPNG from 'upng-js'

// see UPNG: fast PNG minifier (http://upng.photopea.com/)
// function moveQual(val) {
//   if(val>990) cnum=0;
//   else cnum = Math.max(2, Math.round(510*val/1000));
//   for(var i=0; i<pngs.length; i++) recompute(i);
//   update();
// }
function convertQualityToCnum(quality) {
  if (quality < 0 || quality > 0.99) {
    return 0
  }
  return Math.max(2, Math.round(quality * 510))
}

/**
 * 压缩PNG图像
 * @param imageData {ImageData} 图像的像素数据
 * @param quality {number?} 图像压缩质量（0 ~ 1）
 * @returns {ArrayBuffer} 压缩后的图像数据
 */
export function compressPng(imageData, quality) {
  const cnum = quality == null ? convertQualityToCnum(0.2) : convertQualityToCnum(quality)
  return UPNG.encode([imageData.data.buffer], imageData.width, imageData.height, cnum)
}
