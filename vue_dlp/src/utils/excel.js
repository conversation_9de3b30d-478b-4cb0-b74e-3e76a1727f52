import i18n from '@/lang'
import { downloadFile } from '@/utils/download/index'
import { buildDownloadFileByName } from '@/utils/download/helper'

const EXCEL = require('exceljs')

/**
 * 纯前端导出excel文件，各属性（colModels属性与后端的ColModel一致）
 * @param colModels 列表类型，直接传入GridTable的colModel即可，做了转化(仅做了type == null)
 * @param rowDatas 列表类型，列表中的元素一定是对象，且对象的key要与colModels中的prop保持一致
 * @param excelName excel表文件名(String)
 * @param sheetName sheet表名(String)
 */
export function easyExportExcel(colModels, rowDatas, excelName, sheetName) {
  validateParam(colModels, rowDatas, excelName, sheetName)
  const file = buildDownloadFileByName(excelName, false)
  file.percent = 1
  rowDatas = copyRowDatas(rowDatas)
  colModels = transferColModels(colModels)
  sheetName = transferSheetName(sheetName)
  const excel = new EXCEL.Workbook()
  const sheet = excel.addWorksheet(sheetName)
  initSheetHeader(colModels, sheet)
  wrapThenWriteSheetBody(sheet, rowDatas, colModels)
  excel.xlsx.writeBuffer().then(buffer => {
    downloadFile(excelName, [buffer], 'application/vnd.ms-excel')
    file.finished = true
  })
}

/**
 * 初始化excel表头样式以及标题
 */
function initSheetHeader(colModels, sheet) {
  // 获取excel第一行对象
  const row = sheet.getRow(1)
  row.height = 25
  const columns = []
  for (let i = 0; i < colModels.length; i++) {
    const cell = row.getCell(i + 1);
    const colModel = colModels[i]
    // 将默认的样式复制到表头行
    Object.assign(cell, headerStyle)
    columns.push({ header: colModel.header, key: colModel.key, width: colModel.width })
  }
  sheet.columns = columns
}

/**
 * 给行添加样式并写入数据行至excel
 */
function wrapThenWriteSheetBody(sheet, rowDatas, colModels) {
  for (let i = 0; i < (rowDatas || []).length; i++) {
    const row = sheet.getRow(i + 2)
    row.height = 13.5
    row.font = { name: 'Arial', size: 10, color: { argb: 'FF000000' }}
    row.alignment = { horizontal: 'left', vertical: 'middle', wrapText: false }
    processFormatter(colModels, rowDatas[i])
    row.values = rowDatas[i]
  }
}

/**
 * 格式化属性
 */
function processFormatter(colModels, rowData) {
  colModels.forEach(colModel => {
    if (colModel.formatter) {
      const { key } = colModel
      rowData[key] = colModel.formatter(rowData, rowData[key]) || ''
    }
  })
}

/**
 * 校验导出excel的参数
 */
function validateParam(colModels, rowDatas, excelName, sheetName) {
  if (!colModels) {
    throw new Error('excel表表头标题不能为空')
  } else {
    if (Object.prototype.toString.call(colModels) !== '[object Array]') {
      throw new Error('excel表表头标题属性对象需为数组类型')
    }
  }
  if (!excelName) {
    throw new Error('excel文件名不能为空')
  } else {
    if (typeof excelName !== 'string') {
      throw new Error('excel文件名必须为字符串')
    }
  }
}

/**
 * 获取工作表名，如果没有输入，则sheetName默认为工作表
 * @param sheetName
 * @returns {string|VueI18n.LocaleMessages}
 */
function transferSheetName(sheetName) {
  if (!sheetName) {
    sheetName = i18n.t('pages.sheet')
  } else {
    if (typeof sheetName !== 'string') {
      sheetName = i18n.t('pages.sheet')
    }
  }
  return sheetName
}
/**
 * 将gridTable表格的colModel对象转换成excel的colModel
 * @param colModels
 */
function transferColModels(colModels) {
  return colModels.filter(colModel => !colModel.type)
    .map(colModel => {
      return {
        header: i18n.te(`table.${colModel.label}`) ? i18n.t(`table.${colModel.label}`) : colModel.label,
        key: colModel.prop,
        width: (colModel.width ? colModel.width : 10) / 10 * 1.5,
        formatter: colModel.formatter
      }
    })
}

function copyRowDatas(rowDatas) {
  return JSON.parse(JSON.stringify(rowDatas))
}

// 表头样式
const headerStyle = {
  font: { name: 'Arial', size: 12, color: { argb: 'FFFFFFFF' }, bold: true },
  fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF3366FF' }, bgColor: { argb: 'FF3366FF' }},
  border: {
    left: { style: 'thin', color: { argb: 'FF000000' }},
    right: { style: 'thin', color: { argb: 'FF000000' }}
  },
  alignment: { horizontal: 'left', vertical: 'middle', wrapText: false }
}
