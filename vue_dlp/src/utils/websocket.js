// import Vue from 'vue'
import SockJS from 'sockjs-client'
import Stomp from 'stompjs'
import axios from 'axios'
import { aesEncode } from '@/utils/encrypt'
// const socket = new SockJS('websocketJS')
// const stomp = Stomp.over(socket)
// stomp.connect({}, function(frame) {
//   // 订阅后端主动推消息到前端的topic
//   // stomp.subscribe('/topic/serverTime', function(r) {
//   //   console.log('当前服务器时间：' + r.body)
//   // })
//   // // 阅后端主动推消息到前端的topic,只有指定的用户(hzb)收到的的消息
//   // stomp.subscribe('/user/admin/info', function(r) {
//   //   console.log('用户【admin】获取当前服务器时间：' + r.body)
//   // })
//   // // 阅后端主动推消息到前端的topic,只有指定的用户(hzb)收到的的消息
//   // stomp.subscribe('/user/test/info', function(r) {
//   //   console.log('用户【test】获取当前服务器时间：' + r.body)
//   // })
//   // 订阅前端发到后台，后台又将消息返回前台的topic
//   stomp.subscribe('/topic/receive', function(msg) {
//     console.log(msg.body)
//   })
// })
// 订阅的句柄，用于取消订阅
function SubscribeHandle(stompSocket, url) {
  this.url = url
  this.close = () => {
    stompSocket.unsubscribe(this.url)
  }
}
function StompSocket(baseUrl, option) {
  const that = this
  this.option = Object.assign({}, {
    logEnable: true,
    deamon: true, // 是否守护连接，即断线是否自动重连
    successCallback: (e) => { console.log('invoke success callback, param=' + e) },
    errorCallback: (e) => { console.error('invoke error callback, param=' + e) }
  }, option)
  this.prefix = { // api接口 前缀
    app: '/app',
    user: '/user',
    topic: '/topic'
  }
  this.baseUrl = baseUrl
  this.header = {}
  this.toSubscribeMap = {} // 待订阅的Map，用于处理哪些在环境未准备好之前订阅的连接
  this.subscribeMap = {} // 已订阅的Map
  this.bindCallbackMap = {} // 绑定的订阅路径域callback
  this.connected = false
  this.increaseNum = 0 // 自增数
  this.subscribeTimeout = 60000 // 发送消息超时：发送消息后，如果超过超时时间，则取消订阅，并调用超时函数
  this.scheduleHandle = {}
  this.stomp = null

  StompSocket.prototype.initAndConn = () => {
    axios.get(baseUrl + '/sync').then(resp => {
      const token = aesEncode('' + resp.data, 'tr838408websocke')
      this.init(token)
      this.connect()
    })
  }

  StompSocket.prototype.init = (token) => {
    const socket = new SockJS(baseUrl + '?token=' + token, null, { timeout: 15000 })
    this.stomp = Stomp.over(socket)
    this.stomp.heartbeat.outgoing = 20000 // client will send heartbeats every 20000ms
    this.stomp.heartbeat.incoming = 0 // client does not want to receive heartbeats from the server
    this.stomp.onreceipt = function(data) {
      if (data) {
        const dataType = data.headers['data-type']
        if (dataType === 'wsid') {
          that.sessionId = data.body
        }
      }
    }
  }

  StompSocket.prototype._parseJson = (str) => {
    try {
      return JSON.parse(str)
    } catch (e) {
      return str
    }
  }
  StompSocket.prototype._appendUrl = (urls) => {
    let result = ''
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i]
      if (url !== null) {
        result += '/' + (Number.isInteger(url) ? url : url.trim())
      }
    }
    while (result.indexOf('//') >= 0) {
      result = result.replace(/\/\//g, '/')
    }
    return result.replace(/\/$/, '')
  }
  StompSocket.prototype._formatUrl = (option) => {
    let targetUrl = ''
    if (option.prefix) {
      targetUrl = this._appendUrl([targetUrl, option.prefix])
    }
    if (option.appendSID) {
      targetUrl = this._appendUrl([targetUrl, this.sessionId])
    }
    if (option.id || option.id === 0) {
      targetUrl = this._appendUrl([targetUrl, option.id])
    }
    return this._appendUrl([targetUrl, option.url])
  }
  // 从待订阅列表中提取数据进行订阅
  // 解决问题：未连接成功时，就已经调用了订阅函数。则只能将订阅数据存放到缓存列表中，待连接成功时再进行订阅
  StompSocket.prototype._subscribeFromToSubscribeMap = () => {
    if (!this.connected) return
    for (const url in this.toSubscribeMap) {
      this.unsubscribe(url)
      const subscribeInfo = this.toSubscribeMap[url]
      const fullUrl = this._formatUrl(subscribeInfo)
      const subscribeHandle = this.stomp.subscribe(fullUrl, (respond) => {
        if (subscribeHandle.timeoutHandle) {
          clearTimeout(subscribeHandle.timeoutHandle)
        }
        respond.data = this._parseJson(respond.body)
        subscribeInfo.callback(respond, new SubscribeHandle(this, url))
        if (subscribeInfo.once) { // 如果once为true，则接收到订阅消息后，就自动取消订阅
          this.unsubscribe(url)
        }
      })
      if (subscribeInfo.timeoutCallback) {
        subscribeHandle.timeoutHandle = setTimeout(() => {
          subscribeInfo.timeoutCallback(new SubscribeHandle(this, url))
        }, this.subscribeTimeout)
      }
      subscribeHandle.subscribeInfo = subscribeInfo
      this.subscribeMap[url] = subscribeHandle
      delete this.toSubscribeMap[url]
    }
  }
  // 订阅send响应，当send数据头中包含bind属性时，代码主动订阅对应的bind路径
  StompSocket.prototype._waitForSendRespond = () => {
    const that = this
    this.subscribeToUser('', '/messageId', (respond) => {
      const bindUrl = respond.headers.bind
      if (bindUrl) {
        const urlId = respond.headers.uid
        const bindInfo = that.bindCallbackMap[respond.headers.mid]
        if (bindInfo) {
          that.subscribe({
            prefix: this.prefix.user,
            id: urlId,
            url: bindUrl,
            callback: bindInfo.func,
            timeoutCallback: bindInfo.timeoutFunc,
            once: bindInfo.once
          })
        }
      }
    })
  }

  StompSocket.prototype.connect = (successCallback, errorCallback) => {
    const that = this
    if (successCallback) {
      that.option.successCallback = successCallback
    }
    if (errorCallback) {
      that.option.errorCallback = errorCallback
    }
    that.stomp.connect(that.header,
      (r) => {
        that.connected = true
        that._waitForSendRespond()
        that._subscribeFromToSubscribeMap()
        if (that.option.successCallback) that.option.successCallback(r)
      }, (err) => {
        that.connected = false
        if (that.option.errorCallback) that.option.errorCallback(err)
        if (that.option.deamon) {
          setTimeout(() => { that.reconnect() }, 1000)
        }
      }
    )
    this.debugEnable(this.option.logEnable)
  }
  StompSocket.prototype.reconnect = () => { // 重新连接到websocket服务器
    console.warn('invoke reconnect')
    if (this.stomp && this.stomp.connected) {
      this.stomp.disconnect()
    }
    for (const url in this.subscribeMap) {
      if (!this.toSubscribeMap[url] && url !== '/messageId') {
        this.toSubscribeMap[url] = this.subscribeMap[url].subscribeInfo
      }
    }
    this.connected = false
    const newSocket = new StompSocket(this.baseUrl, this.option)
    newSocket.toSubscribeMap = this.toSubscribeMap
    newSocket.initAndConn()
  }
  StompSocket.prototype.disconnect = () => { // 断开连接
    console.warn('invoke disconnect')
    if (this.stomp.connected) {
      this.stomp.disconnect()
    }
    this.stomp = null
    this.connected = false
    this.toSubscribeMap = {}
    this.subscribeMap = {}
  }
  StompSocket.prototype.subscribe = (option) => { // 订阅：订阅后自动接收服务端发送的消息
    const dfop = Object.assign({
      prefix: undefined, // 前缀，如this.profix.user
      appendSID: false, // 是否拼接sessionId
      id: undefined, // 为订阅设置不同的ID，避免订阅重复
      url: undefined,
      callback: undefined,
      timeoutCallback: undefined, // 订阅超时回调函数
      once: false
    }, option)
    this.toSubscribeMap[dfop.url] = dfop
    this._subscribeFromToSubscribeMap()
    return new SubscribeHandle(this, dfop.url)
  }
  StompSocket.prototype.subscribeToUser = (userId, url, callback, appendSID) => {
    if (!(typeof appendSID === 'boolean')) {
      appendSID = true
    }
    return this.subscribe({
      prefix: this.prefix.user,
      appendSID: appendSID,
      url: this._appendUrl([userId, url]),
      callback: callback
    })
  }
  StompSocket.prototype.subscribeToUserTimeOut = (userId, url, callback, appendSID, timeoutCallback) => {
    if (!(typeof appendSID === 'boolean')) {
      appendSID = true
    }
    return this.subscribe({
      prefix: this.prefix.user,
      appendSID: appendSID,
      url: this._appendUrl([userId, url]),
      callback: callback,
      timeoutCallback: timeoutCallback
    })
  }
  /** 根据ajax响应结果进行订阅*/
  StompSocket.prototype.subscribeToAjax = (ajaxRespond, url, callback) => {
    if (ajaxRespond.requestId != null) {
      return this.subscribe({
        prefix: this.prefix.user,
        id: ajaxRespond.requestId,
        url: url,
        callback: callback
      })
    } else {
      return this.subscribe({
        prefix: this.prefix.user,
        appendSID: true,
        url: url,
        callback: callback
      })
    }
  }
  StompSocket.prototype.unsubscribe = (url) => { // 取消订阅
    if (this.subscribeMap.hasOwnProperty(url)) {
      const subscribeHandle = this.subscribeMap[url]
      subscribeHandle.unsubscribe()
      if (subscribeHandle.timeoutHandle) clearTimeout(subscribeHandle.timeoutHandle)
      delete this.subscribeMap[url]
    }
  }
  StompSocket.prototype.send = (url, headers, data) => { // 发送：向服务端发送消息
    if (this.stomp && this.connected) {
      const sendHeaders = Object.assign({}, this.header, headers)
      this.stomp.send(url, sendHeaders, JSON.stringify(data))
    }
  }
  /**
   * 发送并自动订阅一次：向服务端发送消息
   * 示例：userId=10001，url=/getData
   * 则需要在服务端进行以下配置
   * 1、配置@MessageMapping("/getData")，供前端调用
   * 2、向订阅url="/user/10001/getData"推送数据
   */
  StompSocket.prototype.sendToUser = (userId, url, data, callback, timeoutCallback) => {
    const sendHeaders = {}
    if (this.stomp && this.connected && callback) {
      sendHeaders.mid = Date.now() + '' + this.increaseNum++
      sendHeaders.bind = url
      sendHeaders.uid = userId
      this.bindCallbackMap[sendHeaders.mid] = {
        url,
        func: callback,
        timeoutFunc: timeoutCallback
      }
    }
    this.send(this.prefix.app + url, sendHeaders, data)
    return sendHeaders.mid
  }
  StompSocket.prototype.setSubscribeTimeout = (millisecond) => {
    this.subscribeTimeout = millisecond
  }
  StompSocket.prototype.debugEnable = (enable) => {
    this.option.logEnable = enable === null || enable === undefined ? this.option.logEnable : enable
    if (this.stomp && !this.option.logEnable) {
      this.stomp.debug = null
    }
  }
  StompSocket.prototype.debug = (str) => {
    if (this.stomp && this.stomp.debug) {
      this.stomp.debug(str)
    }
  }
}

export default StompSocket
