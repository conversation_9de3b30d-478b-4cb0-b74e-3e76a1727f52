import { isIPv6 } from '@/utils/validate'
import { Level, UTermPluginClient } from '@/views/system/terminalManage/terminal/uterm/client';
import { compareVersion } from '@/api/system/terminalManage/uterm';

// 获取连接后面的参数值
export function getUrlParam(name) {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)'); // 构造一个含有目标参数的正则表达式对象
  // const r = window.location.search.substr(1).match(reg); // 匹配目标参数
  const r = window.location.hash.split('?')[1].match(reg);
  if (r != null) return decodeURIComponent(r[2]); return null; // 返回参数值
}

export function ping(ip, port, imgPath) {
  return new Promise(async(resolve, reject) => {
    // let ipv6Flag = false
    const img = new Image();
    const start = new Date().getTime();
    let url = 'http://';
    if (isIPv6(ip)) {
      url = url.concat('[').concat(ip).concat(']:').concat(port)
    } else {
      url = url.concat(ip).concat(':').concat(port)
    }
    if (!imgPath) {
      url = url.concat('?t=').concat(start)
    } else {
      url = url.concat(imgPath).concat('?t=').concat(start)
    }
    console.warn('url', url)
    img.src = url
    let flag = false;
    if (ip === '' || ip === undefined) {
      console.warn('IP 地址不存在')
      resolve(false);
      return false;
    }
    if (port === '' || port === undefined) {
      console.warn('端口 地址不存在')
      resolve(false);
      return false;
    }
    img.onload = function(event) {
      console.warn('地址：', 'http://' + ip + ':' + port, ' 可用')
      flag = true;
      resolve(flag);
    }
    img.onerror = async function(event) {
      if (isIPv6(ip)) {
        resolve(true);
        // await testIPv6Support().then(function(supported) {
        //   if (supported) {
        //     console.warn('当前网络[本机]支持IPv6：', 'http://' + ip + ':' + port, ' 可用')
        //     flag = true;
        //     resolve(flag);
        //   } else {
        //     console.warn('当前网络[本机]不支持IPv6');
        //     resolve(false)
        //   }
        // });
      } else {
        console.warn('地址：', 'http://' + ip + ':' + port, ' 可用')
        flag = true;
        resolve(flag);
      }
    };
    setTimeout(function() {
      if (!flag) {
        console.warn('链接超时,地址：', 'http://' + ip + ':' + port, ' 不可用')
        flag = false;
        resolve(flag);
      }
    }, 2000);
  })
}

/**
 * @Deprecated
 * 检验浏览器所在的设备是否支持ipv6 支持：true 不支持：false
 * @returns {Promise<unknown>}
 */
export function testIPv6Support() {
  return new Promise(function(resolve, reject) {
    const ipv6Url = 'https://api6.ipify.org?format=json';

    const xhr = new XMLHttpRequest();
    xhr.open('GET', ipv6Url, true);
    xhr.onload = function() {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          const ipAddress = response.ip;
          if (isIPv6(ipAddress)) {
            resolve(true);
          } else {
            resolve(false);
          }
        } catch (error) {
          resolve(false);
        }
      } else {
        resolve(false);
      }
    };
    xhr.onerror = function() {
      resolve(false);
    };
    setTimeout(function() {
      // 当请求第三方接口不通就说明不支持ipv6
      resolve(false);
    }, 2000);
    xhr.send();
  });
}

/**
 * @Deprecated
 * @param ipAddress
 * @returns {boolean}
 */
export function isIPv6Address(ipAddress) {
  // 检查IP地址是否为IPv6格式
  const regex = /^([0-9a-fA-F]{0,4}:){1,7}[0-9a-fA-F]{0,4}$/;
  return regex.test(ipAddress);
}

/**
 * 在访问首页的时候，获取浏览器所在的电脑的ip
 * @returns {Promise<void>}
 */
export function getClientIp() {
  const uPlugin = new UTermPluginClient(null, UTermPluginClient.getPort(), Level.DEBUG, true)
  uPlugin.connectedPromise
    .then(() => uPlugin.getPlugInfo())
    .then(res => res.PlugVersion || '') // 当前插件版本号
    .then(version => compareVersion(version, '1.01.230824.SC') >= 0) // 校验插件版本号,最低版本支持 1.01.230824.SC
    .then(newer => newer ? uPlugin.listIpAddress() : Promise.reject('The plugin version is too lower!'))
    .then(res => Array.isArray(res) && res.length > 0 ? res : Promise.reject('The ip list got by plugin is empty!'))
    .then(ips => handleIpList(ips))
    .catch(e => console.warn('[GetClientIp] error: ', e))
}

function handleIpList(ips) {
  let ipv4, ipv6
  for (let i = 0; i < ips.length; i++) {
    const item = ips[i]
    ipv4 = ipv4 || item.IPv4Address
    ipv6 = ipv6 || item.IPv6Address
  }
  if (ipv4) {
    sessionStorage.setItem('current_pc_ipv4', ipv4)
  } else {
    sessionStorage.removeItem('current_pc_ipv4')
  }
  if (ipv6) {
    sessionStorage.setItem('current_pc_ipv6', ipv6)
  } else {
    sessionStorage.removeItem('current_pc_ipv6')
  }
  console.log('current_pc_ipv4: ', sessionStorage.getItem('current_pc_ipv4'))
  console.log('current_pc_ipv6: ', sessionStorage.getItem('current_pc_ipv6'))
}
