import QRCode from 'qrcodejs2'
import i18n from '@/lang'
import Vue from 'vue';
import { getDictLabel, getTypeFont } from '@/utils/dictionary';

const dotMatrixOption = {
  begin: [1, 3, 7, 9],
  0: [2, 5, 8],
  1: [3, 5, 7],
  2: [4, 5, 6],
  3: [1, 5, 9],
  4: [1, 7, 8],
  5: [1, 3, 4],
  6: [2, 3, 9],
  7: [6, 7, 9],
  8: [2, 4, 6],
  9: [2, 6, 8],
  A: [4, 6, 8],
  B: [2, 4, 8],
  C: [5, 6, 7],
  D: [1, 5, 8],
  E: [3, 4, 5],
  F: [2, 5, 9]
}
const dotMatrix = ['begin', 0, 0, 0, 0, 0, 1, 2, 3]

export const options = [
  { value: '00', label: i18n.t('pages.itemRowData1') },         // 自定义内容
  { value: '01', label: i18n.t('pages.itemRowData2') },         // 换行标志
  { value: '02', label: i18n.t('pages.itemRowData3') },         // 空格
  { value: '03', label: i18n.t('pages.itemRowData4') },         // 操作员账号
  { value: '0C', label: i18n.t('pages.itemRowData12') },        // 操作员编号
  { value: '04', label: i18n.t('pages.itemRowData5') },         // 终端名称
  { value: '0D', label: i18n.t('pages.itemRowData13') },        // 终端编号
  { value: '05', label: i18n.t('pages.itemRowData6') },         // 当前日期
  { value: '06', label: i18n.t('pages.itemRowData7') },         // 当前时间
  { value: '07', label: i18n.t('pages.itemRowData8') },         // IP信息
  { value: '08', label: i18n.t('pages.itemRowData9') },         // MAC信息
  { value: '09', label: i18n.t('pages.itemRowData10') },        // 计算机全名
  { value: '0A', label: i18n.t('pages.itemRowData11') }         // 计算机登入用户名
]

// 模拟数据
const optionMap = {
  '00': i18n.t('pages.itemRowData1'),
  '01': '<br>',
  '02': ' ',
  '03': 'User10001',
  '04': 'WIN-520L',
  '05': i18n.t('pages.itemRowData6'),
  '06': i18n.t('pages.itemRowData7'),
  '07': '127.0.0.1',
  '08': '00-0C-29-BB-26-2F',
  '09': 'DESKTOP-ALEP64C',
  '0A': 'Administrator',
  '0C': '1',
  '0D': '10001'

}

export function getPreviewText(code, keyword) {
  if (code == '00') {
    return keyword
  } else if (code == '05') {
    return day()
  } else if (code == '06') {
    return time()
  }
  const txt = optionMap[code]
  return !txt ? '' : txt
}

/* function now() {
  var date = new Date()
  var year = date.getFullYear()
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  // 拼接
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
} */
// 数字转16进制颜色
function numberToColor(num) {
  if (num && Number.isInteger(num)) {
    let color = num.toString(16)
    for (let i = color.length; i < 6; i++) {
      color = '0' + color
    }
    return '#' + color
  }
  return ''
}
function day() {
  var date = new Date()
  var year = date.getFullYear()
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  // 拼接
  return `${year}/${month}/${day}`
}
function time() {
  var date = new Date()
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  // 拼接
  return `${hours}:${minutes}:${seconds}`
}
// 如果值为空，则重新赋值，如果不为空，不赋值
function setIfEmpty(obj, key, val) {
  if (obj[key] === null || obj[key] === undefined || obj[key] === '') {
    obj[key] = val
  }
}

/**
 * 水印预览效果渲染
 * @param vm  页面的this对象
 */
export function watermark(vm) {
  const view_div = document.getElementById(vm.viewId)
  const mask_div = document.getElementById(vm.maskId)
  if (!view_div || !mask_div) return
  const elements = view_div.getElementsByClassName('clone')
  for (let i = elements.length - 1; i >= 0; i--) { // 清除平铺clone的节点
    elements[i].parentNode.removeChild(elements[i])
  }
  // 默认设置
  var dfo = {
    txt: '',
    topMargin: vm.temp.topMargin,                     // 页面上边距
    bottomMargin: vm.temp.bottomMargin,               // 页面下边距
    leftMargin: vm.temp.leftMargin,                   // 页面左边距
    rightMargin: vm.temp.rightMargin,                 // 页面右边距
    x_space: vm.temp.lrDistance,                      // 水印x轴间隔,单位 mm
    y_space: vm.temp.tbDistance,                      // 水印y轴间隔,单位 mm
    pic_x_space: vm.temp.imageLrDistance,             // 图片水印x轴间隔（水平间距），单位 mm（目前仅有Office文档水印使用）
    pic_y_space: vm.temp.imageTbDistance,             // 图片水印y轴间隔（垂直间距），单位 mm（目前仅有Office文档水印使用）
    iplace: vm.temp.iplace,                           // 水印位置
    waterMarkForm: vm.temp.waterMarkForm,             // 水印类型 0文字 1二维码 2点阵
    dwWordType: vm.temp.dwWordType,                   // 字体类型：1空心体 0实行体; 或者点阵码圆点大小: 1 - 10
    color: vm.temp.color,                             // 水印字体颜色
    fontSize: vm.temp.fontHeight,                     // 水印字体大小
    fgorbg: vm.temp.fgorbg,                           // 文字图层位置 （打印水印，1 前景 2 背景）
    font: vm.temp.strFont,                            // 字体
    angle: vm.temp.dwEscapement,                      // 水印倾斜度数
    alpha: (255 - vm.temp.alphaValue) / 255,          // 水印透明度
    isWaterMark: vm.temp.isWaterMark,                 // 是否增加文字水印
    isWordMark: vm.temp.isWordMark,                   // 是否增加文字水印(office文档)
    isImage: vm.temp.isImageMark,                     // 是否增加背景图片
    imagePlace: vm.temp.imagePlace,                   // 背景图片位置
    imageZoom: vm.temp.imageZoom,                     // 背景图片大小
    picTurbid: vm.temp.picTurbid,                     // 图片冲浊
    fontShapeLeft: vm.temp.fontShapeLeft,             // 文字水印水平对齐选项的key
    fontShapeTop: vm.temp.fontShapeTop,               // 文字水印垂直对齐选项的key
    fontShapeLeftSize: vm.temp.fontShapeLeftSize,     // 文字水印水平对齐偏移值
    fontShapeTopSize: vm.temp.fontShapeTopSize,       // 文字水印垂直对齐偏移值
    picShapeLeft: vm.temp.picShapeLeft,               // 文字水印水平对齐选项的key
    picShapeTop: vm.temp.picShapeTop,                 // 文字水印垂直对齐选项的key
    picShapeLeftSize: vm.temp.picShapeLeftSize,       // 文字水印水平对齐偏移值
    picShapeTopSize: vm.temp.picShapeTopSize          // 文字水印垂直对齐偏移值
  }
  // 如果为空，则尝试从其他字段中取值
  setIfEmpty(dfo, 'fontSize', vm.temp.fontSize)
  setIfEmpty(dfo, 'topMargin', vm.topMargin)
  setIfEmpty(dfo, 'bottomMargin', vm.bottomMargin)
  setIfEmpty(dfo, 'leftMargin', vm.leftMargin)
  setIfEmpty(dfo, 'rightMargin', vm.rightMargin)
  setIfEmpty(dfo, 'angle', vm.dwEscapement)
  setIfEmpty(dfo, 'color', vm.color)
  setIfEmpty(dfo, 'imageZoom', vm.imageZoom)
  setIfEmpty(dfo, 'waterMarkForm', 0)
  setIfEmpty(dfo, 'iplace', vm.iplace)
  if (Number.isInteger(dfo.color)) {
    dfo.color = numberToColor(dfo.color)
  }
  // 如果传入了透明度的百分比，则透明度直接取这个值。解决透明度范围不同导致的问题
  if (vm.hasOwnProperty('alphaPercent')) {
    dfo.alpha = vm.alphaPercent
  }
  const textArr = []
  if (vm.contentCode) { // 水印内容编码，例如 00010203
    for (let i = 0; i < vm.contentCode.length; i += 2) {
      const code = vm.contentCode.substring(i, i + 2)
      textArr.push(getPreviewText(code, vm.temp.keyword))
    }
  } else if (vm.temp.strategyOptionList) {
    vm.temp.strategyOptionList.forEach(item => {
      textArr.push(getPreviewText(item.dataId, vm.temp.keyword))
    })
  }
  dfo.txt = textArr.join('')
  if (vm.stgTypeNumber == 44) {
    dfo.topMargin = 1
    dfo.bottomMargin = 1
    dfo.leftMargin = 1
    dfo.rightMargin = 1
    dfo.iplace = 15
    dfo.x_space = dfo.y_space = vm.temp.markDistance
  }

  let iplace = dfo.iplace
  var scale = 1
  var transformOrigin = ''
  // 外层容器样式设置页面边距
  vm.containerStyle = {
    padding: dfo.topMargin + 'mm ' + dfo.rightMargin + 'mm ' + dfo.bottomMargin + 'mm ' + dfo.leftMargin + 'mm '
  }
  vm.viewStyle = {
    position: 'absolute',
    zIndex: dfo.fgorbg == 2 ? -1 : 0
  }
  vm.picStyle = {
    opacity: dfo.alpha == 1 ? 0.99 : dfo.alpha
  }
  const isOffice = vm.stgTypeNumber == 151
  if (vm.stgTypeNumber == 151) { // office文档水印
    vm.viewStyle = {
      // width: 'auto',
      // height: 'auto',
      position: 'absolute',
      opacity: dfo.alpha == 1 ? 0.99 : dfo.alpha
      // top: dfo.topMargin + 'mm',
      // right: dfo.rightMargin + 'mm',
      // bottom: dfo.bottomMargin + 'mm',
      // left: dfo.leftMargin + 'mm'
    }
    vm.picStyle = {
      opacity: !dfo.picTurbid ? 0.5 : 1
    }
  }
  const pre = vm.$refs.preview
  let height = pre.offsetHeight
  let width = pre.offsetWidth
  // 是否设置图片水印
  if (dfo.isImage === 1) {
    // 已上传图片
    if (vm.fileList.length > 0) {
      // 图片的url
      const url = vm.fileList[0].url
      // background属性的各个值
      let background = ''
      const bgUrl = `url(${url})`
      let bgPosition = '0 0'
      let bgSize = vm.sizeH ? `${vm.sizeW}px ${vm.sizeH}px` : 'auto auto'
      const bgRepeat = 'no-repeat'

      // 创建img图片，获取图片大小，用于处理图片水印的预览效果
      const img = new Image()
      img.onload = () => {
        let sizeW = img.width * dfo.imageZoom / 100
        let sizeH = img.height * dfo.imageZoom / 100
        if (vm.stgTypeNumber == 151) { // office文档水印
          sizeW = sizeW / 2
          sizeH = sizeH / 2
        }
        bgSize = `${sizeW}px ${sizeH}px`
        // 记录图片大小，重新渲染时使用，避免图片闪烁
        vm.sizeW = sizeW
        vm.sizeH = sizeH

        if (dfo.imagePlace == 15) { // 平铺
          // 图片水印宽高+间距 (间距单位mm，转成px转换系数大约是3.8 )
          const spaceWidth = Math.round(vm.sizeW + (isOffice ? dfo.pic_x_space * 4 / 3 : dfo.x_space * 3.8))
          const spaceHeight = Math.round(vm.sizeH + (isOffice ? dfo.pic_y_space * 4 / 3 : dfo.y_space * 3.8))
          background = getBackground(spaceWidth, spaceHeight, width, height, bgUrl, bgSize, bgRepeat)
        } else {
          background = `${bgUrl} ${bgPosition}/${bgSize} ${bgRepeat}`
        }
        vm.$set(vm.picStyle, 'background', background)
      }
      img.src = url
      if (dfo.imagePlace !== 15) { // 非平铺
        const position = vm.placeCssType[dfo.imagePlace]
        let positionX = ''
        let positionY = ''
        if (position === undefined) {
          if (dfo.picShapeLeft >= 0 && dfo.picShapeTop >= 0) {
            positionX = Math.round(dfo.picShapeLeftSize / 21 * width) + 'px'
            positionY = Math.round(dfo.picShapeTopSize / 30 * height) + 'px'
          } else if (dfo.picShapeLeft >= 0) {
            positionX = Math.round(dfo.picShapeLeftSize / 21 * width) + 'px'
            positionY = ({ '-999999': ' top', '-999997': 'bottom', '-999995': 'center' })[dfo.picShapeTop]
          } else if (dfo.picShapeTop >= 0) {
            positionX = ({ '-999998': 'left', '-999996': 'right', '-999995': 'center' })[dfo.picShapeLeft]
            positionY = Math.round(dfo.picShapeTopSize / 30 * height) + 'px'
          }
          bgPosition = `${positionX} ${positionY}`
        } else {
          bgPosition = position
        }
        background = `${bgUrl} ${bgPosition}/${bgSize} ${bgRepeat}`
      } else {
        // 图片水印宽高+间距 (间距单位mm，转成px转换系数大约是3.8 )
        const spaceWidth = Math.round(vm.sizeW + (isOffice ? dfo.pic_x_space * 4 / 3 : dfo.x_space * 3.8))
        const spaceHeight = Math.round(vm.sizeH + (isOffice ? dfo.pic_y_space * 4 / 3 : dfo.y_space * 3.8))
        background = getBackground(spaceWidth, spaceHeight, width, height, bgUrl, bgSize, bgRepeat)
      }
      vm.$set(vm.picStyle, 'background', background)
    }
  }
  // 文字参数未勾选则不显示文字水印
  if (dfo.isWaterMark === 0 || dfo.isWordMark === 0) {
    vm.maskStyle = ''
    mask_div.innerHTML = ''
    return
  }
  // 水印样式设置
  vm.maskClass = 'mask-div'
  vm.maskStyle = 'position: absolute; z-index: 9999999; pointer-events: none;'
  mask_div.innerHTML = ''
  if (dfo.waterMarkForm === 1) { // 二维码
    new QRCode(mask_div, {
      width: dfo.fontSize,
      height: dfo.fontSize, // 高度
      text: dfo.txt // 二维码内容
      // render: 'canvas' ,   // 设置渲染方式（有两种方式 table和canvas，默认是canvas）
    })
  } else if (dfo.waterMarkForm === 2) { // 点阵图
    // timestamp是为了区别上次渲染的内容，以驱动视图重新渲染
    let html = '<div id="dianzhen" class="dot-matrix-image" timestamp="' + new Date().getTime() + '" style="position: relative;height: ' + dfo.fontSize * 6 + 'px;width: ' + dfo.fontSize * 6 + 'px">'
    for (let i = 1; i <= 9; i++) {
      html += '<div class="dot-group" style="">'
      for (let j = 1; j <= 9; j++) {
        html += '<div class="dot-box"><div class="dot ' + (dotMatrixOption[dotMatrix[i - 1]].indexOf(j) > -1 ? 'visible' : '') + '" style="width: ' + dfo.dwWordType + 'px; height: ' + dfo.dwWordType + 'px; background: ' + dfo.color + ';"></div></div>'
      }
      html += '</div>'
    }
    html += '</div>'
    vm.maskContent = html
  } else { // 文字 dfo.waterMarkForm === 0
    const { color, fontSize, txt, font, dwWordType } = dfo
    // 实心字体颜色
    const textColor = dwWordType === 1 ? '#e4e7e9' : color
    // 空心字体颜色
    const textShadow = dwWordType === 1 ? `1px 1px ${color}, -1px -1px ${color}, 1px -1px ${color}, -1px 1px ${color}` : 'none'
    const lineHeight = fontSize < 20 ? fontSize + 2 : fontSize + 5

    mask_div.innerHTML = txt
    vm.maskStyle += `font-size: ${fontSize}px; font-family: ${font}; line-height: ${lineHeight}px;` +
    `text-align: ${isOffice ? 'left' : 'center'}; white-space: nowrap; overflow: hidden; color: ${textColor}; text-shadow: ${textShadow};`
  }
  // 位置, mask_div的宽度、高度需要获取渲染之后的值
  vm.$nextTick(() => {
    // 文字水印位置：1顶部，2中间，3底部，13左侧，14右侧，6左上，7右上，8左下，9右下，15平铺
    const maskW = mask_div.offsetWidth
    const maskH = mask_div.offsetHeight
    const r = (maskW - maskH) / 2
    const sin = Math.sin(dfo.angle * Math.PI / 180)
    const cos = Math.cos(dfo.angle * Math.PI / 180)
    let rotate = ''
    let marginLeft = 0
    let marginTop = 0
    let extraX = 0
    let extraY = 0

    if (vm.stgTypeNumber == 151) { // office文档水印
      scale = 0.5
      extraX = -Math.round(maskW / 2)
      extraY = -Math.round(maskH / 2)
    }

    let offsetX = -maskW / 2 + extraX  // 文字水印水平的偏移
    let offsetY = -maskH / 2 + extraY  // 文字水印垂直的偏移

    // iplace为null，说明是自定义的位置
    if (iplace === null) {
      height = pre.offsetHeight
      width = pre.offsetWidth
      if (dfo.fontShapeLeft >= 0 && dfo.fontShapeTop >= 0) {
        marginLeft = Math.round(dfo.fontShapeLeftSize / 21 * width)
        marginTop = Math.round(dfo.fontShapeTopSize / 30 * height)
        iplace = 6
      } else if (dfo.fontShapeLeft >= 0) {
        marginLeft = Math.round(dfo.fontShapeLeftSize / 21 * width)
        iplace = ({ '-999999': 6, '-999997': 8, '-999995': 13 })[dfo.fontShapeTop]
      } else if (dfo.fontShapeTop >= 0) {
        marginTop = Math.round(dfo.fontShapeTopSize / 30 * height)
        iplace = ({ '-999998': 6, '-999996': 7, '-999995': 1 })[dfo.fontShapeLeft]
      }
    }
    // 1顶部，2中间，3底部，13左侧，14右侧，6左上，7右上，8左下，9右下，15平铺
    if (iplace === 1) {
      vm.maskClass += ' c2' // 通过class控制位置，二维码、点阵图使用
      offsetY = parseInt(Math.abs(r * sin) + extraY)
    } else if (iplace === 2) {
      vm.maskClass += ' c5'
    } else if (iplace === 3) {
      vm.maskClass += ' c8'
      offsetY = -parseInt(Math.abs(r * sin) + extraY)
    } else if (iplace === 13) {
      vm.maskClass += ' c4'
      offsetX = -parseInt(r * (1 - Math.abs(cos)) - extraX)
    } else if (iplace === 14) {
      vm.maskClass += ' c6'
      offsetX = parseInt(r * (1 - Math.abs(cos)) - extraX)
    } else if (iplace === 6) {
      vm.maskClass += ' c1'
      offsetX = -parseInt(r * (1 - Math.abs(cos)) - extraX)
      offsetY = parseInt(Math.abs(r * sin) + extraY)
    } else if (iplace === 7) {
      vm.maskClass += ' c3'
      offsetX = parseInt(r * (1 - Math.abs(cos)) - extraX)
      offsetY = parseInt(Math.abs(r * sin) + extraY)
    } else if (iplace === 8) {
      vm.maskClass += ' c7'
      offsetX = -parseInt(r * (1 - Math.abs(cos)) - extraX)
      offsetY = -parseInt(Math.abs(r * sin) + extraY)
    } else if (iplace === 9) {
      vm.maskClass += ' c9'
      offsetX = parseInt(r * (1 - Math.abs(cos)) - extraX)
      offsetY = -parseInt(Math.abs(r * sin) + extraY)
    } else if (iplace === 15) {
      if (dfo.waterMarkForm === 1) vm.maskClass += ' c5'
      offsetX = 0
      offsetY = 0
    }
    if (dfo.waterMarkForm === 0) { // 文字水印旋转、位移
      rotate = ' rotate(-' + dfo.angle + 'deg)'
      const transform = `transform: scale(${scale}) translate(${offsetX}px, ${offsetY}px) ${rotate};`
      const margin = `margin-left: ${marginLeft}px; margin-top: ${marginTop}px;`
      vm.maskStyle += '-webkit-' + transform +
        '-moz-' + transform +
        '-ms-' + transform +
        '-o-' + transform +
        transform + margin + transformOrigin
    }
    vm.$nextTick(() => {
      if (iplace === 15 && dfo.waterMarkForm !== 1) {
        const vw = view_div.offsetWidth
        const vh = view_div.offsetHeight
        const d = vw > vh ? vw * 1.4 : vh * 1.4  // 所有水印所占范围的长度
        const xs = dfo.x_space * (isOffice ? 1.33 : 2.8)
        const ys = dfo.y_space * (isOffice ? 1.33 : 2.8)
        const offsetWidth = isOffice ? mask_div.offsetWidth * cos + mask_div.offsetHeight * sin : mask_div.offsetWidth
        const offsetHeight = isOffice ? mask_div.offsetWidth * sin + mask_div.offsetHeight * cos : mask_div.offsetHeight
        const mw = (offsetWidth + xs) * scale  // 单个水印+间距的宽度
        const mh = (offsetHeight + ys) * scale // 单个水印+间距的高度
        const offsetTop = isOffice ? (mask_div.offsetWidth * sin * Math.tan(dfo.angle / 2)) * scale : 0
        // 减少行列数，渲染过多会造成卡顿
        let row = Math.ceil(d / mh) // 水印行数
        let col = Math.ceil(d / mw)// 水印列数
        row = row % 2 === 1 ? row : row + 1
        col = col % 2 === 1 ? col : col + 1
        for (let i = 0; i < row; i++) {
          for (let j = 0; j < col; j++) {
            // 复制水印并定位
            const clone = mask_div.cloneNode(true)
            clone.id = 'clone' + i + j
            clone.className = 'mask-div clone'
            clone.style.top = mh * i + offsetTop + 'px'
            clone.style.left = mw * j + 'px'
            clone.style.transform = 'translate(0px, 0px)'
            if (scale != 1) {
              clone.style.transform += ` scale(${scale})`
              clone.style.transformOrigin = '0 0'
            }
            // 当水印为office水印时，旋转水印内容
            if (vm.stgTypeNumber == 151) {
              clone.style.transform += `${rotate}`
              clone.style.transformOrigin = '0 0'
              clone.style.textAlign = 'left'
            }
            view_div.appendChild(clone)
          }
        }
        // 其他水印进行整体旋转，office水印将clone的div进行旋转
        if (vm.stgTypeNumber != 151) {
          // 使水印与可视窗口的圆心尽量靠近且第一个水印显示完整
          vm.viewStyle.left = -mw * Math.round((col - vw / mw) / 2) + 'px'
          vm.viewStyle.top = -mh * Math.round((row - vh / mh) / 2) + 'px'
          // 修改旋转圆心位置
          vm.viewStyle['transform-origin'] = (mw * col) / 2 + 'px ' + (mh * row) / 2 + 'px'
          vm.viewStyle.transform = rotate
        }
        vm.maskStyle += 'display: none;'
      }
    })
  })
}
/**
 * 获取background属性的值（通过设置多张背景图片实现背景图平铺效果）
 * @param {*} picW 图片宽度
 * @param {*} picH 图片高度
 * @param {*} conW 容器宽度
 * @param {*} conH 容器高度
 * @param {*} bgUrl 图片url
 * @param {*} bgSize 图片尺寸
 * @param {*} bgRepeat 图片repeat
 * @returns background的值，多个背景图片值的拼接
 */
function getBackground(picW, picH, conW, conH, bgUrl, bgSize, bgRepeat) {
  const x = conW / picW
  const y = conH / picH
  const bgArr = []
  let bgPosition
  for (let i = 0; i < x; i++) {
    for (let j = 0; j < y; j++) {
      bgPosition = `${i * picW}px ${j * picH}px`
      bgArr.push(`${bgUrl} ${bgPosition}/${bgSize} ${bgRepeat}`)
    }
  }
  return bgArr.join(',')
}

/**
 * 水印模板库对应的水印类型;对应screen/print/office WaterMarkDlg 的 dataType
 */
export const watermarkTypeMap = {
  // 屏幕水印
  'screen': 1,
  // 打印水印
  'print': 2,
  // office水印
  'office': 3
}

/**
 * ！！ 24.2.26
 * 注意：目前前端和后端的水印模板格式化分了两份，前端显示一份，后端显示一份，后续应该将前端的描述格式化交给后端来处理，不然改一处还得再改另外一处
 * （目前仅同步了屏幕水印设置的模板描述）
 * 获取水印的多语言模板描述 (两参数必传)
 * @param watermarkObj 水印对象 水印的参数信息
 * @param watermarkType 水印类型 对应水印模板库的水印类型 参考后端 WaterMarkLib 类 type 与常量watermarkTypeMap一致
 */
export function formatWatermarkDetail(watermarkObj, watermarkType) {
  if (!watermarkObj || !watermarkType) { return '' }
  let detail = ''
  if (watermarkType === 1) {
    detail = formatScreenWatermarkDetail(watermarkObj)
  } else if (watermarkType === 2) {
    detail = formatPrintWatermarkDetail(watermarkObj)
  } else if (watermarkType === 3) {
    detail = formatOfficeWatermarkDetail(watermarkObj)
  }
  return detail || watermarkObj.remark
}

// =================================================
// 将这些属性交到js文件中是为了格式化方法需要这些属性
// 以下为屏幕水印和打印水印的属性
// 字体类型
export const wordType = {
  0: i18n.t('pages.wordType1'),
  1: i18n.t('pages.wordType2')
}
// 水印形式
export const waterFormType = {
  0: i18n.t('pages.waterMarkForm1'),
  1: i18n.t('pages.waterMarkForm2'), // 屏幕水印要过滤掉
  2: i18n.t('pages.waterMarkForm3')
}
// 图层位置
export const fgorbgType = {
  1: i18n.t('pages.fgorbg1'),
  2: i18n.t('pages.fgorbg2')
}
// ===============================

// 打印水印的图片位置
export const printPlaceOptions = [
  // 6   1  7
  // 13  2  14
  // 8   3  9
  { label: i18n.t('pages.placeOptions1'), value: 1 },
  { label: i18n.t('pages.placeOptions2'), value: 2 },
  { label: i18n.t('pages.placeOptions3'), value: 3 },
  { label: i18n.t('pages.placeOptions8'), value: 13 },
  { label: i18n.t('pages.placeOptions9'), value: 14 },
  { label: i18n.t('pages.placeOptions4'), value: 6 },
  { label: i18n.t('pages.placeOptions5'), value: 7 },
  { label: i18n.t('pages.placeOptions6'), value: 8 },
  { label: i18n.t('pages.placeOptions7'), value: 9 },
  { label: i18n.t('pages.placeOptions10'), value: 15 } // 平铺
]

// =====================
// 以下为office水印的属性
export const rotationOptions = [
  { label: i18n.t('pages.rotationOptions1'), value: 0 },
  { label: i18n.t('pages.rotationOptions2'), value: 315 }
]

export const shapeLeftOptions = [
  { label: i18n.t('pages.shapeLeftOptions1'), value: -999998 },
  { label: i18n.t('pages.shapeLeftOptions2'), value: -999996 },
  { label: i18n.t('pages.shapeLeftOptions3'), value: -999995 },
  { label: i18n.t('pages.shapeLeftOptions4'), value: 0 }
]

export const shapeTopOptions = [
  { label: i18n.t('pages.shapeTopOptions1'), value: -999999 },
  { label: i18n.t('pages.shapeTopOptions2'), value: -999997 },
  { label: i18n.t('pages.shapeTopOptions3'), value: -999995 },
  { label: i18n.t('pages.shapeTopOptions4'), value: 0 }
]
// ====================

// 水印内容转成map
function getOptionsMap() {
  return options.reduce((map, option) => { map[option.value] = option.label; return map }, {})
}
/**
 * 获取屏幕水印多语言模板描述
 */
function formatScreenWatermarkDetail(obj) {
  if (!obj) { return '' }
  obj = JSON.parse(JSON.stringify(obj))
  const { dwWordType, strFont,
    alphaValue, fontHeight, color, dwEscapement, markDistance } = obj
  Object.assign(obj, { optionValueList: obj.optionValue ? Vue.prototype.numToList(obj.optionValue, 3) : [] })
  const optionsMap = getOptionsMap()
  const strategyOptionList = screenWatermarkVersionDeal(obj, optionsMap)
  // 获取字体
  const font = (getTypeFont().find(opt => opt.value === strFont) || {}).label || strFont
  // 水印 文字内容 (选项拼接在一起)
  const watermarkContent = strategyOptionList.reduce((content, option) => {
    const optionLabel = optionsMap[option.dataId]
    return content ? `${content}、${optionLabel}` : optionLabel
  }, '')
  let waterMarkForm = obj.waterMarkForm
  // 组件screenWaterMarkDlg中handleUpdate函数发现： 当为点阵时，数据库的JSON其实是存储的waterMarkForm的值为1，所以需要作一下转换
  if (waterMarkForm === 1) { waterMarkForm = 2 }
  // 圆点大小
  const dotSizeTemplate = waterMarkForm === 2 ? i18n.t('pages.dotSizeTemplate', { size: dwWordType }) : ''
  // 字体
  const fontTemplate = waterMarkForm === 2 ? '' : i18n.t('pages.fontTemplate', { font: `${font} ${fontHeight} ${wordType[dwWordType]}` })
  // 截屏时才显示水印
  const showWatermark = obj.capture === 1
    ? obj.encryptCapture == 1 ? `${i18n.t('pages.encryptScreenShotWatermark')}；` : `${i18n.t('pages.showWatermarkOnlyWhenScreenshot')}；`
    : ''
  // 离线时才显示水印
  const showOfflineWatermark = obj.offlineMark === 1 ? `${i18n.t('pages.showWatermarkOnlyWhenOffline')}；` : ''
  const showEncryptCtrlProcess = obj.encryptCtrlProcess === 1 ? `${i18n.t('pages.encryptCtrlProcessWatermark')}；` : ''
  return i18n.t('pages.screenTextTemplate', {
    watermarkContent, dotSizeTemplate, fontTemplate, showWatermark, color, degree: dwEscapement,
    waterMarkForm: waterFormType[waterMarkForm], alphaValue: parseInt((255 - alphaValue) * 100 / 255), markDistance, showOfflineWatermark, showEncryptCtrlProcess
  })
}
/**
 * 屏幕水印兼容旧数据
 */
function screenWatermarkVersionDeal(obj, optionsMap) {
  const list = obj.strategyOptionList || []
  // 版本数据处理，有些旧的数据需要进行格式化，兼容一下
  if (obj.strategyOption) {
    // strategyOption的每两个字符表示一个文字项设置
    for (let i = 0; i < obj.strategyOption.length; i = i + 2) {
      const code = obj.strategyOption[i] + obj.strategyOption[i + 1]
      if (optionsMap[code]) {
        list.push({ label: optionsMap[code], id: i, dataId: code })
      }
    }
  } else {
    // strategyOption为空说明是旧数据，需要进行兼容处理
    if (obj.isUsedTime == 0) {
      // 添加时间
      list.push({ label: optionsMap['05'], id: 1, dataId: '05' })
      list.push({ label: optionsMap['02'], id: 2, dataId: '02' })
      list.push({ label: optionsMap['06'], id: 3, dataId: '06' })
      list.push({ label: optionsMap['01'], id: 4, dataId: '01' })
    }
    if (obj.isUsedName == 0) {
      // 添加终端名称
      list.push({ label: optionsMap['04'], id: 5, dataId: '04' })
      if (obj.optionValueList.indexOf(4) != -1) {
        // 如果下个文字项是操作员，则要显示为一行
        list.push({ label: optionsMap['02'], id: 6, dataId: '02' })
      } else {
        list.push({ label: optionsMap['01'], id: 7, dataId: '01' })
      }
    }
    if (obj.optionValueList.indexOf(4) != -1) {
      // 添加操作员账号
      list.push({ label: optionsMap['03'], id: 8, dataId: '03' })
      list.push({ label: optionsMap['01'], id: 9, dataId: '01' })
    }
    if (obj.optionValueList.indexOf(1) != -1) {
      // 添加ip
      list.push({ label: optionsMap['07'], id: 10, dataId: '07' })
      if (obj.optionValueList.indexOf(2) != -1) {
        // 如果下个文字项是Mac，则要显示为一行
        list.push({ label: optionsMap['02'], id: 11, dataId: '02' })
      } else {
        list.push({ label: optionsMap['01'], id: 12, dataId: '01' })
      }
    }
    if (obj.optionValueList.indexOf(2) != -1) {
      // 添加mac
      list.push({ label: optionsMap['08'], id: 13, dataId: '08' })
      list.push({ label: optionsMap['01'], id: 14, dataId: '01' })
    }
    if (list && list[list.length - 1].dataId == '01') {
      // 如果最后一个文字项是回车符，则去掉
      list.pop()
    }
  }
  // return obj.strategyOptionList
  return list
}

/**
 * 获取打印水印多语言模板描述
 */
function formatPrintWatermarkDetail(obj) {
  if (!obj) { return '' }
  obj = JSON.parse(JSON.stringify(obj))
  const { isWaterMark, isImageMark, waterMarkForm, dwWordType, strFont,
    fontHeight, color, iplace, fgorbg, dwEscapement, alphaValue, imagePlace, imageZoom } = obj
  const font = (getTypeFont().find(opt => opt.value === strFont) || {}).label || strFont
  // 水印类型：1.文字+图片 2.文字 3.图片
  const type = (isWaterMark && isImageMark) ? 1 : isWaterMark ? 2 : isImageMark ? 3 : ''
  const watermarkType = {
    1: `${i18n.t('pages.waterMarkType3')}`,
    2: `${i18n.t('pages.waterMarkType1')}`,
    3: `${i18n.t('pages.waterMarkType2')}`
  }[type]
  Object.assign(obj, { printOptionList: obj.printOption ? Vue.prototype.numToArr(obj.printOption) : [] })
  const optionsMap = getOptionsMap()
  // 水印 文字内容 (选项拼接在一起)
  const strategyOptionList = printWatermarkVersionDeal(obj, optionsMap)
  const watermarkContent = isWaterMark === 1 ? strategyOptionList.reduce((content, option) => {
    const optionLabel = optionsMap[option.dataId]
    return content ? `${content}、${optionLabel}` : optionLabel
  }, '') : ''
  // 圆点大小
  const dotSizeTemplate = waterMarkForm === 2 ? i18n.t('pages.dotSizeTemplate', { size: dwWordType }) : ''
  // 字体
  const fontTemplate = waterMarkForm === 2 ? '' : i18n.t('pages.fontTemplate', { font: `${font} ${fontHeight} ${wordType[dwWordType]}` })
  // 文字水印详情 (文字内容、圆点/字体、颜色、文字位置、图层位置、倾斜度、水印形式、透明度)
  const textTemplate = isWaterMark ? i18n.t('pages.textTemplate', {
    watermarkContent, dotSizeTemplate, fontTemplate, color, textPlace: getDictLabel(printPlaceOptions, iplace),
    layerPosition: fgorbgType[fgorbg], degree: dwEscapement / 10,
    waterMarkForm: waterFormType[waterMarkForm], alphaValue: parseInt((alphaValue) * 100 / 255)
  }) : ''
  // 图片水印
  const imageTemplate = isImageMark ? i18n.t('pages.imageTemplate', { imagePosition: getDictLabel(printPlaceOptions, imagePlace), imageZoom }) : ''
  // 模板描述
  return watermarkType ? i18n.t('pages.watermarkTemplate', { watermarkType, textTemplate, imageTemplate }) : ''
}
/**
 * 打印水印兼容旧数据
 */
function printWatermarkVersionDeal(obj, optionsMap) {
  const list = obj.strategyOptionList || []
  // 版本数据处理，有些旧的数据需要进行格式化，兼容一下
  if (obj.strategyOption) {
    // strategyOption的每两个字符表示一个文字项设置
    for (let i = 0; i < obj.strategyOption.length; i = i + 2) {
      let code = obj.strategyOption[i] + obj.strategyOption[i + 1]
      // 打印水印与屏幕水印不太一样，03表示的是终端昵称，04表示的是操作员名称。需要对调一下。
      const oldCode = code
      if (oldCode == '03') {
        code = '04'
      }
      if (oldCode == '04') {
        code = '03'
      }
      if (optionsMap[code]) {
        list.push({ label: optionsMap[code], id: i, dataId: code })
      }
    }
  } else {
    // strategyOption为空说明是旧数据，需要进行兼容处理
    if (obj.isUsedTime == 0) {
      // 添加时间
      list.push({ label: optionsMap['05'], id: 1, dataId: '05' })
      list.push({ label: optionsMap['02'], id: 2, dataId: '02' })
      list.push({ label: optionsMap['06'], id: 3, dataId: '06' })
      list.push({ label: optionsMap['01'], id: 4, dataId: '01' })
    }
    if (obj.isUsedName == 0) {
      // 添加操作员账号
      list.push({ label: optionsMap['03'], id: 5, dataId: '03' })
      if (obj.printOptionList.indexOf(1) != -1) {
        // 如果下个文字项是终端，则要显示为一行
        list.push({ label: optionsMap['02'], id: 6, dataId: '02' })
      } else {
        list.push({ label: optionsMap['01'], id: 7, dataId: '01' })
      }
    }
    if (obj.printOptionList.indexOf(1) != -1) {
      // 添加终端名称
      list.push({ label: optionsMap['04'], id: 8, dataId: '04' })
      list.push({ label: optionsMap['01'], id: 9, dataId: '01' })
    }
    if (list && (list[list.length - 1] || {}).dataId == '01') {
      // 如果最后一个文字项是回车符，则去掉
      list.pop()
    }
  }
  return list
}
/**
 * 获取Office水印多语言模板描述
 */
function formatOfficeWatermarkDetail(obj) {
  if (!obj) { return '' }
  obj = JSON.parse(JSON.stringify(obj))
  const { isWordMark, isImageMark, /* markWay,*/waterMarkForm, strFont, fontSize, color,
    fontLayout, imageLayout, lrDistance, tbDistance, imageLrDistance, imageTbDistance,
    fontRotation, fontShapeLeft, fontShapeTop, picSize, picTurbid, picShapeLeft, picShapeTop } = obj
  // 水印类型：1.文字+图片 2.文字 3.图片
  const type = (isWordMark && isImageMark) ? 1 : isWordMark ? 2 : isImageMark ? 3 : ''
  const watermarkType = {
    1: `${i18n.t('pages.waterMarkType3')}`,
    2: `${i18n.t('pages.waterMarkType1')}`,
    3: `${i18n.t('pages.waterMarkType2')}`
  }[type]
  const font = (getTypeFont().find(opt => opt.value === strFont) || {}).label || strFont
  // 生效方式
  // 24D3小版本 去除生效方式 水印模板仅包含模板信息
  // const markWayLabel = { 1: i18n.t('pages.markWay1'), 2: i18n.t('pages.markWay2'), 4: i18n.t('pages.markWay3') }[markWay]
  // const effectiveMode = i18n.t('pages.effectiveMode', { markWayLabel })
  const effectiveMode = ''
  // 水印 文字内容 (选项拼接在一起)
  const watermarkContent = getOfficeWatermarkContent(obj.extendContent)
  // 字体
  const fontTemplate = waterMarkForm === 2 ? '' : i18n.t('pages.fontTemplate', { font: `${font} ${fontSize}` })
  // 版式、对齐方式
  const fontShape = getDictLabel(rotationOptions, fontRotation)
  const horizontalAlignment = getDictLabel(shapeLeftOptions, fontShapeLeft)
  const verticalAlignment = getDictLabel(shapeTopOptions, fontShapeTop)
  // 透明度
  const alphaValue = 100 - obj.alphaValue
  // 获取颜色代码
  const colorCode = '#' + (color && color !== '#008080' ? color.toString(16).padStart(6, '0').toUpperCase() : '008080')
  // 文字水印详情 (文字内容、字体、颜色、版式、水印位置、水平属性名、水平参数、垂直属性名、垂直参数、透明度)
  const textPlace = !fontLayout ? i18n.t('pages.wordPlace1') : i18n.t('pages.wordPlace2')
  const horizontalField = !fontLayout ? i18n.t('pages.fontShapeLeft') : i18n.t('pages.horizontalSpacing')
  const horizontalAttribute = !fontLayout ? horizontalAlignment : `${lrDistance}pt`
  const verticalField = !fontLayout ? i18n.t('pages.fontShapeTop') : i18n.t('pages.verticalSpacing')
  const verticalAttribute = !fontLayout ? verticalAlignment : `${tbDistance}pt`
  const textTemplate = isWordMark ? i18n.t('pages.officeTextTemplate', {
    watermarkContent, fontTemplate, color: colorCode, fontShape, textPlace, horizontalField, horizontalAttribute, verticalField, verticalAttribute, alphaValue
  }) : ''
  // 图片效果，图片水平对齐、垂直对齐
  const imageEffects = !picTurbid ? i18n.t('pages.imageEffects') : ''
  const picHorizontalAlignment = getDictLabel(shapeLeftOptions, picShapeLeft)
  const picVerticalAlignment = getDictLabel(shapeTopOptions, picShapeTop)
  // 图片水印
  const imagePlace = !imageLayout ? i18n.t('pages.wordPlace1') : i18n.t('pages.wordPlace2')
  const imageHorizontalField = !imageLayout ? i18n.t('pages.fontShapeLeft') : i18n.t('pages.horizontalSpacing')
  const imageHorizontalAttribute = !imageLayout ? picHorizontalAlignment : `${imageLrDistance}pt`
  const imageVerticalField = !imageLayout ? i18n.t('pages.fontShapeTop') : i18n.t('pages.verticalSpacing')
  const imageVerticalAttribute = !imageLayout ? picVerticalAlignment : `${imageTbDistance}pt`
  const imageTemplate = isImageMark ? i18n.t('pages.officeImageTemplate', {
    picSize, imageEffects, imagePlace, imageHorizontalField, imageHorizontalAttribute, imageVerticalField, imageVerticalAttribute }) : ''
  // 模板描述
  return watermarkType ? i18n.t('pages.watermarkTemplate', { watermarkType, effectiveMode, textTemplate, imageTemplate }) : ''
}
/**
 * Office水印兼容旧数据
 */
function getOfficeWatermarkContent(str) {
  if (!str) { return '' }
  const itemRowData = filterKeywordItem(options.map((item, index) => {
    return { id: index, label: item.label, dataId: item.value }
  }))
  const labels = []
  for (let i = 0; i < str.length; i += 2) {
    const c = str.substring(i, i + 2)
    const eqVal = itemRowData.find(item => item.dataId === c)
    eqVal && labels.push(eqVal.label)
  }
  return labels.join(',')
}

export function filterKeywordItem(datas) {
  // @see waterMark.js - options
  // 00：自定义内容, 02：空格, 03：操作员账号, 0C：操作员编号, 04：终端名称, 0D：终端编号, 05：当前日期, 06: 当前时间
  const filterMap = { '00': true, '01': true, '02': true, '03': true, '0C': true, '04': true, '0D': true, '05': true, '06': true, '07': true }
  const keywordItem = datas.filter(({ dataId }) => filterMap[dataId])
  return keywordItem
}
