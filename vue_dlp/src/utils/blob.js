/**
 * 空 Blob
 */
export const EMPTY_BLOB = new Blob()

/**
 * 开始读取指定的 Blob中的内容，一旦完成，result 属性中保存的将是被读取文件的 ArrayBuffer 数据对象。
 */
export function readAsArrayBuffer(blob) {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()
    fileReader.onload = function(event) {
      resolve(event.target.result)
    }
    fileReader.onerror = function(event) {
      reject(event.target.error)
    }
    fileReader.readAsArrayBuffer(blob)
  })
}

/**
 * 开始读取指定的Blob中的内容。一旦完成，result属性中将包含一个字符串以表示所读取的文件内容。
 */
export function readAsText(blob) {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()
    fileReader.onload = function(event) {
      resolve(event.target.result)
    }
    fileReader.onerror = function(event) {
      reject(event.target.error)
    }
    fileReader.readAsText(blob)
  })
}

/**
 * 开始读取指定的Blob中的内容。一旦完成，result属性中将包含一个data: URL 格式的 Base64 字符串以表示所读取文件的内容。
 */
export function readAsDataURL(blob) {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader()
    fileReader.onload = function(event) {
      resolve(event.target.result)
    }
    fileReader.onerror = function(event) {
      reject(event.target.error)
    }
    fileReader.readAsDataURL(blob)
  })
}
