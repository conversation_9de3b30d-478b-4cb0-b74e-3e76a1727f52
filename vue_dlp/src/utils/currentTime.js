function padNumber(num) {
  return num < 10 ? '0' + num : num;
}

/**
 * 获取系统当前时间
 * @returns {string}
 */
export function getCurrentTime() {
  const now = new Date();
  const year = now.getFullYear();
  const month = padNumber(now.getMonth() + 1);
  const day = padNumber(now.getDate());
  const hours = padNumber(now.getHours());
  const minutes = padNumber(now.getMinutes());
  const seconds = padNumber(now.getSeconds());
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
