/**
 * 输入限制
 * @param value 原始输入值
 * @param allow 允许输入字符判断函数
 * @returns {string} 过滤后的值
 */
export function limit(value, allow) {
  if (!value) {
    return ''
  }
  if (typeof allow !== 'function') {
    return value
  }
  const codes = []
  let char
  let code
  for (let i = 0; i < value.length; i++) {
    char = value.charAt(i)
    code = value.charCodeAt(i)
    if (allow(char, code)) {
      codes.push(code)
    }
  }
  return String.fromCharCode(...codes)
}

/**
 * 仅数字（整数）
 * @param value 原始输入值
 * @returns {string} 仅保留整数字符后的值
 */
export function onlyInt(value) {
  return limit(value, (char, code) => code > 47 && code < 58)
}

/**
 * 不包含空格
 * @param value 原始输入值
 * @returns {string} 去除空格后的值
 */
export function notSpace(value) {
  return limit(value, (char, code) => code > 32 && code !== 127)
}

/**
 * 可见ASCII字符（包括空格）
 * @param value 原始输入值
 * @returns {string} 去除非可见ASCII字符后的值
 */
export function visibleAscii(value) {
  return limit(value, (char, code) => code > 31 && code < 127)
}

/**
 * 可见ASCII字符（不包括空格）
 * @param value 原始输入值
 * @returns {string} 去除非可见ASCII字符和空格后的值
 */
export function visibleAsciiWithoutSpace(value) {
  return limit(value, (char, code) => code > 32 && code < 127)
}
