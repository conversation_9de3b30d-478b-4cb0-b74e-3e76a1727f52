import Cookies from 'js-cookie'
import { base64EncodeSpe, base64DecodeSpe } from '@/utils/encrypt'

const TokenKey = 'vue_dlp_token'

export function getToken() {
  return Cookies.get(TokenKey)
}
// 获取token的时间戳
export function getTokenTimestamp() {
  return Cookies.get(Token<PERSON>ey + '_ms')
}
export function setTokenTimestamp() {
  // console.log('setTokenTimestamp')
  return Cookies.set(TokenKey + '_ms', new Date().getTime())
}

export function setToken(token, ignoreTimestamp = false) {
  if (ignoreTimestamp === null || ignoreTimestamp === undefined || ignoreTimestamp === false) {
    setTokenTimestamp()
  }
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  Cookies.remove(TokenKey + '_ms')
  return Cookies.remove(TokenKey)
}

const userIdKey = 'uid'
export function getUserId() {
  return Cookies.get(userIdKey)
}

export function setUserId(id) {
  return Cookies.set(userIdKey, id)
}

export function removeUserId() {
  return Cookies.remove(userIdKey)
}

export function cookieSetPwd(username, password, rememberPassword) {
  const rememberPwdInfo = { username: username, password: password, rememberPassword: rememberPassword }
  const value = encodeURIComponent(base64EncodeSpe(JSON.stringify(rememberPwdInfo)))
  Cookies.set('vue_dlp_up', value, { expires: 7 })
}
export function cookieGetPwd() {
  const value = Cookies.get('vue_dlp_up')
  return value ? JSON.parse(base64DecodeSpe(decodeURIComponent(value))) : null
}
export function cookieRemovePwd() {
  Cookies.remove('vue_dlp_up')
}
