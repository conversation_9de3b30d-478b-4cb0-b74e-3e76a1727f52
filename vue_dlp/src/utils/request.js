import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getCurrentServerTime } from '@/utils'
import { getUserId } from '@/utils/auth'
import { handleDownload } from '@/utils/download/index'
import { readAsText } from '@/utils/blob'
import { aesDecode, aesEncode, formatAesKey } from '@/utils/encrypt';
import Cookies from 'js-cookie'
import MD5 from 'js-md5'
import qs from 'qs'
import i18n from '@/lang'
import router from '@/router'
import { getIsRecovering, setIsRecovering } from '@/api/system/terminalManage/manageLibrary';

let timestamp
let showDlg = true
let errMessage = ''
const notDealTimeoutUrl = [
  '/stgIssue/list'
]
// 不刷新token更新的时间：管理员没有操作鼠标和键盘一段时间后，应该跳转到登录界面，重新登录。但是一些后台通知前端去更新的接口，会导致token一直被刷新，因此需要过滤，不更新token的前端缓存时间
const notRefreshTokenTimeUrl = [
  '/terminal/getTerminalStatus',
  '/terminal/getUserStatus',
  '/terminal/listTermNode',
  '/user/listUserNode',
  '/alarmLog/listMsgIds',
  '/alarmLog/listMsg',
  '/violationResponseRule/getAllRules',
  '/backupRule/getAllBackupRules'
]
// http状态码
const errorStatus = {
  403: i18n.t('httpStatus.403'),
  404: i18n.t('httpStatus.404'),
  500: i18n.t('httpStatus.500')
}
let diffNotice = false
const lastData = {}
let timer
// 创建一个axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // 当跨域请求时发送cookie
  timeout: store.getters.requestTimeout, // 请求超时
  headers: {
    'Content-Security-Policy': "script-src 'self'; object-src 'none';style-src 'self'; child-src https:",
    'X-XSS-Protection': '1',
    'X-Content-Type-Options': 'nosniff'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // store和cookie中都有userId且不相同，说明需要刷新浏览器
    const userId = store.getters.userId
    const uid = getUserId()
    if (userId && uid && userId != uid) {
      // 避免多次弹出提示窗口
      if (!diffNotice) {
        diffNotice = true
        alert(i18n.t('pages.adminChange'))
        // 定位到首页
        location.href = location.origin
      }
      return
    }

    // 对传入的部分数据进行拷贝并替换，避免改变外部数据（针对直接将 this.temp 作为参数传递的情况）
    const configData = config.data
    // 有值且为对象
    if (configData && typeof configData == 'object') {
      // 仅针对构造函数是 Object 的对象
      if (configData.constructor === Object) {
        // Object.assign 无法深度拷贝，JSON.stringify 会导致属性丢失（值为undefined或方法的属性）
        config.data = Object.assign({}, configData, JSON.parse(JSON.stringify(configData)))
      }
    }

    const { data, headers, params } = config
    let url = config.url
    // 限制短时间内的重复请求
    // 和上次的请求url、data是否相同, data 转成json字符串比较
    const sameUrl = url && url == lastData.url
    const sameData = data && JSON.stringify(data) == JSON.stringify(lastData.data)
    // 新增、修改、删除
    const isLimitUrl = url && (url.includes('/add') || url.includes('/update') || url.includes('/delete'))
    // 短时间内相同url和data的新增、修改、删除请求不发送
    if (sameUrl && sameData && isLimitUrl) return Promise.reject(new Error('不允许在短时间内重复发送请求'));
    clearTimeout(timer)
    // 将上次请求的url、data储存起来
    lastData.url = url
    lastData.data = data
    // 0.5秒后，清除已储存的url、data
    timer = setTimeout(() => {
      lastData.url = ''
      lastData.data = ''
    }, 500);

    // 在发送请求之前做些什么
    if (!timestamp || Math.round(new Date() / 1000) - timestamp > 3) {
      // 3秒内发送的请求不更新状态
      timestamp = Math.round(new Date() / 1000)
      showDlg = true
    }
    if (store.getters.token) {
      // 让每个请求携带 token
      headers['Authorization'] = store.getters.token
    }

    // 将当前请求的菜单编码放入请求头中
    if (!headers['Menu-Code']) {
      const currentRoute = router.currentRoute;
      if (url.indexOf('/menuCollection') === -1 && currentRoute.meta && currentRoute.meta.code) {
        headers['Menu-Code'] = currentRoute.meta.code
      }
    }
    // 请求头添加 语言类型
    headers['Lang'] = store.getters.language

    // 参数编码
    if (params) {
      url += '?'
      url += qs.stringify(params)
      config.params = {}
    }
    config.url = url

    // 在头部增加code，避免重放攻击
    setHashCodeHeader(headers, url)

    // 对指定属性的值进行加密
    if (data && data.encryptProps) {
      encryptionByProps(data)
      headers['encrypt-props'] = data.encryptProps
      delete data.encryptProps
    }
    // 删除指定不提交的属性
    if (data && data.noSubmitProps) {
      data.noSubmitProps.forEach(prop => {
        delete data[prop]
      });
      delete data.noSubmitProps
    }
    return config
  },
  error => {
    // 处理请求错误
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  /**
   * 如果您想获得http信息，例如头信息或状态信息
   * Please return  response => response
  */

  /**
   * 通过自定义代码确定请求状态
   * 这里只是一个例子
   * 您还可以通过HTTP状态代码来判断状态
   */
  response => {
    //  缓存数据库恢复状态
    if (response.data && response.data.recoverStatus) {
      setIsRecovering(response.data && response.data.recoverStatus)
      if (router.currentRoute.path !== '/recovering') {
        router.push({ path: '/recovering' })
      }
    }
    if (response.status === 204) {
      return Promise.reject(response.statusText || i18n.t('components.downloadIntercepted'))
    }
    if (response.status === 200 && response.headers.location) {
      return response
    }
    const res = response.data
    // 更新token
    setTokenByResponse(response)
    const authenticationExpired = response.headers['authentication-expired']
    if (authenticationExpired && authenticationExpired === 'true') {
      return handleAuthenticationExpired(res)
    }
    // 如果自定义代码不是20000，则判断为错误。
    if ((!(res instanceof Blob) && res.code !== 20000) || (res instanceof Blob && response.status != 200)) {
      // 50014:令牌过期;500141:令牌过期-异地登录;
      if (res.code === 50014 || res.code === 500141) {
        return handleAuthenticationExpired(res)
      } else {
        res.data && errMsgBox(res.data)
        const requestUrl = response.config.url
        // 输出错误信息到控制台
        console.error('request error: ', requestUrl, response)
      }
      return Promise.reject(res.data || `request error.`)
    } else {
      // 假如返回的是文件流的话，要保存为文件
      if (res instanceof Blob) {
        handleDownload(response)
      }
      // 对指定属性的值进行解密
      const encryptProps = response.headers['encrypt-props']
      if (encryptProps) {
        // 需要兼容返回数据为对象的情况
        const data = Array.isArray(response.data) ? response.data
          : Array.isArray(response.data.data) ? response.data.data
            : !response.data.data ? [response.data]
              : Array.isArray(response.data.data.items) ? response.data.data.items
                : !response.data.data.items ? [response.data.data]
                  : [response.data.data.items]
        console.log('decrypt Data = ')
        console.log(data)
        console.log('decrypt Props = ' + encryptProps)
        decryptionByProps(data, encryptProps.split(','))
      }

      //  如果为获取验证码
      if ((res instanceof Blob && response.status == 200) && response.headers && response.headers['x-captcha-id']) {
        return response;
      }

      return res
    }
  },
  error => {
    if (error instanceof axios.Cancel) {
      if (!error.message) {
        error.message = 'The user aborted a request.'
      }
      return Promise.reject(error)
    }
    const response = error.response
    // 更新token
    setTokenByResponse(response)
    const { url, baseURL } = error.config || {}
    if (url && url.indexOf(baseURL + '/transferTask/getDownloadTask') == 0) {
      return Promise.reject(error)
    }
    if (response && response.data && response.data instanceof Blob) {
      // 当请求为下载文件请求时，返回的错误消息是blob类型，需要转为json对象
      return getErrorBlobMsg(response).then(() => Promise.reject(error))
    }
    //  当处于数据库恢复状态时，不弹出提示错误信息
    const recoveringStatus = getIsRecovering()
    if (recoveringStatus && recoveringStatus == 6) {
      return;
    }
    let showErrMsg = true
    if ('Error: Network Error' == error) {
      // 计算机个性化-批量上传时路径太长时报错隐藏
      showErrMsg = false
    }
    const isTimeout = error.message && error.message.indexOf('timeout') == 0
    // 请求超时提示信息：当还未加载多语言资源时，请求超时，则直接使用中文提示
    const timeoutMsg = i18n.te('pages.timeout') ? i18n.t('pages.timeout') : '请求超时，请检查网络或稍后重试！'
    if (isTimeout) {
      for (let i = 0; i < notDealTimeoutUrl.length; i++) {
        if (url.match(notDealTimeoutUrl[i])) {
          showErrMsg = false
        }
      }
    }
    if (response && response.data && response.data.code && response.data.code == 601) {
      // 如果返回code = 601,说明是云服务外发管理乐观锁未通过，不提示异常信息，需要在内部的axios自己做错误处理
      return Promise.reject(error)
    }
    if (showErrMsg) {
      // 其他请求返回的错误信息则是json格式，直接访问
      const errMsg = response && response.data && response.data.data
      let errMsg1 = isTimeout ? timeoutMsg : error.message
      if (!isTimeout && response && errorStatus[response.status]) {
        errMsg1 = response.status + ' ' + errorStatus[response.status]
      }
      const message = errMsg || errMsg1
      if (errMessage != message) {
        errMessage = message
        errMsgBox(message)
      }
      setTimeout(() => {
        errMessage = ''
      }, 1000);
    }
    return Promise.reject(error)
  }
)

export function encryptionByProps(data) {
  if (!data) return
  const { encryptProps } = data
  encryptProps.forEach(prop => {
    if (data[prop]) {
      data[prop] = aesEncode(data[prop], formatAesKey('tr838408', data.name || ''))
    }
  });
}

export function decryptionByProps(data, props) {
  if (!data || !props) return
  data.forEach(item => {
    props.forEach(prop => {
      if (item[prop]) {
        item[prop] = aesDecode(item[prop], formatAesKey('tr838408', item.name || ''))
      }
    });
  });
}

export function setHashCodeHeader(headers, url) {
  try {
    if (url) {
      const urlParser = document.createElement('a')
      urlParser.href = url
      url = urlParser.pathname
      if (url.startsWith('/' + process.env.VUE_APP_BASE_API)) {
        url = url.substring(process.env.VUE_APP_BASE_API.length + 1)
      }
    }
    const c1 = headers['Authorization']
    const c2 = headers['Lang']
    const c3 = headers['Menu-Code']
    const c4 = getCurrentServerTime()
    const c5 = Math.random() + ''
    const c6 = c5.substring(c5.length - 3, c5.length)
    const info = [url, c1, c2, c3, c4, c5, c6]
    let msg = ''
    for (let i = 0; i < info.length; i++) {
      const c = info[i]
      msg += !c ? 0 : c
    }
    const md5 = MD5.create()
    md5.update(msg)
    headers['X-Code'] = md5.hex()
    if (c4) {
      headers['X-Time'] = c4 + ''
    }
    headers['X-Notice'] = c5
  } catch (e) {
    console.error('set code header error')
    console.error(e)
  }
}

function handleAuthenticationExpired(res) {
  return getAuthenticationExpiredResult(res).then(result => {
    // 避免同一时间的多个请求造成多次弹窗
    if (showDlg) {
      showDlg = false
      Cookies.set('loginErrorCode', result.code)
      store.dispatch('user/resetToken').then(() => {
        location.reload()
      })
    }
    return Promise.reject(result.data)
  })
}

function getAuthenticationExpiredResult(res) {
  if (res instanceof Blob) {
    return readAsText(res).then(result => JSON.parse(result))
  } else {
    return Promise.resolve(res)
  }
}

function getErrorBlobMsg(response) {
  if (response.status === 404) {
    errMsgBox(i18n.t('text.file_not_found'))
    response.data = { code: 404, data: i18n.t('text.file_not_found') }
    return Promise.resolve()
  }
  return readAsText(response.data).then(result => {
    const jsonData = JSON.parse(result)
    if (jsonData.code) {
      errMsgBox(jsonData.data)
    }
    response.data = jsonData
  }).catch(() => {
    errMsgBox(i18n.t('pages.serverUnknownError'))
    response.data = { code: 500, data: i18n.t('pages.serverUnknownError') }
  })
}

function errMsgBox(message, duration) {
  Message({
    message: message,
    type: 'error',
    showClose: true,
    duration: duration || (5 * 1000)
  })
}

function setTokenByResponse(response) {
  const authorization = response.headers.authorization
  const isIgnoreTimestamp = isRespondByNotRefreshTokenTimeUrl(response)
  // console.log('setToken by url=' + (response && response.config ? response.config.url : '') + ', ignoreTimestamp=' + isIgnoreTimestamp)
  if (authorization) {
    store.dispatch('user/setToken', { token: authorization, ignoreTimestamp: isIgnoreTimestamp })
  } else if (!isIgnoreTimestamp) {
    store.dispatch('user/setTokenTimestamp')
  }
}

function isRespondByNotRefreshTokenTimeUrl(response) {
  if (!response || !response.config || !response.config.url) {
    return false
  }
  const url = response.config.url
  for (let i = 0; i < notRefreshTokenTimeUrl.length; i++) {
    const notUrl = notRefreshTokenTimeUrl[i]
    if (url.indexOf(notUrl) > -1) {
      return true
    }
  }
  return false
}

export default service
