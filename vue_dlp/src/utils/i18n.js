import i18n from '@/lang'
import store from '@/store'

export function getPageTitle(key) {
  const title = getSystemResources('title')
  // 返回页面名称+系统名称
  // const hasKey = i18n.te(`route.${key}`)
  // if (hasKey) {
  //   const pageName = i18n.t(`route.${key}`)
  //   return `${pageName} - ${title}`
  // }
  // 返回系统名称
  return `${title}`
}

// 翻译router.meta.title，用于菜单、标签视图等
export function generateTitle(title) {
  const hasKey = i18n.te('route.' + title)

  if (hasKey) {
    // $t :this method from vue-i18n, inject in @/lang/index.js
    const translatedTitle = i18n.t('route.' + title)

    return translatedTitle
  }
  return title
}

/**
 * 获取系统数据或某个值
 * @param {String} key 需要获取的数据的 key，不传值则获取 sysResources
 * @returns sysResources 或者对应 key 的值
 */
export function getSystemResources(key) {
  const { sysResources, language } = store.getters
  if (key) {
    // 有区分多语言的属性
    const i18nKeys = ['title', 'pluginName', 'copyright']
    const val = sysResources[key]
    if (i18nKeys.includes(key) && typeof val == 'object') {
      return val[language]
    } else {
      return val
    }
  } else {
    return sysResources
  }
}

/**
 * 将键值对形式的字符串数组中的值首字母修改为大写（冠词连词介词除外）
 * @param {Array} array 字符串数组（key: value | "key": "value"）
 * @param {String} connector key、value之间的连接符，默认为 ': '
 * @returns 返回字符串
 */
export function setWordUppercase(array, connector, split) {
  connector = connector || ': '
  split = split || ',\n    '
  return array.map((item) => {
    var strArr = item.split(connector);
    strArr[1] = setTheFirstLetterToUppercase(strArr[1])
    return strArr.join(connector);
  }).join(split)
}

/**
 * 将英文句子转换成每个单词首字母大写（冠词、介词、连词除外）
 * @param {String} words 英文句子
 * @returns 返回字符串
 */
export function setTheFirstLetterToUppercase(words) {
  if (!words) return words
  // 冠词、介词、连词 首字母不大写
  const filterWords = ['an', 'a', 'the', 'and', 'or', 'but', 'of', 'in', 'on', 'under', 'between', 'beside', 'for', 'from', 'despite', 'beneath']
  const wordArr = words.split(' ')
  return wordArr.map(word => {
    if (filterWords.includes(word)) {
      return word
    } else {
      return word.substring(0, 1).toUpperCase() + word.substring(1)
    }
  }).join(' ')
}

/**
 * 通过与参照物对象对比，返回目标对象中缺少的数据
 * @param {object} contrast 检测的参照物
 * @param {object} target 检测的目标
 * @returns 返回差异的数据
 */
export function detectMissingData(contrast, target) {
  if (!contrast || !target) {
    console.error('parameter error')
    return
  }
  const data = {}
  // 遍历参照物对象
  for (const key in contrast) {
    if (Object.hasOwnProperty.call(contrast, key)) {
      const contrastItem = contrast[key]
      const targetItem = target[key]
      if (typeof contrastItem != typeof targetItem) {
        // 如果值的数据类型不一样，说明目标对象缺少该值，记录下来
        data[key] = contrastItem
      } else if (typeof contrastItem == 'object') {
        // 遍历子对象
        data[key] = detectMissingData(contrastItem, targetItem)
      }
    }
  }
  return data
}

/**
 * 验证繁体文字与《简繁体名词对照表》翻译不一致的内容
 * @param {Object} resources 语言资源对象
 * @returns 返回包含被替换资源的对象
 */
export function verifyTraditionalResources(resources) {
  if (!resources) {
    console.error('parameter error')
    return
  }
  const data = {}
  // 遍历参照物对象
  for (const key in resources) {
    if (Object.hasOwnProperty.call(resources, key)) {
      const value = resources[key]
      if (typeof value == 'string') {
        const newValue = replaceCertainTraditionalCharacters(value)
        if (value != newValue) {
          const contrast = `${value} ---> ${newValue}`
          data[key] = contrast
        }
      } else if (typeof value == 'object') {
        // 遍历子对象
        data[key] = verifyTraditionalResources(value)
      }
    }
  }
  return data
}

/**
 * 将繁体文字与《简繁体名词对照表》进行比对，返回替换后的文本
 * @param {String} text 需要被替换的文本
 * @returns 返回替换后的文本
 */
export function replaceCertainTraditionalCharacters(text) {
  twArr.forEach(item => {
    const { chtw, tw } = item
    text = text.replaceAll(chtw, tw)
  })
  return text
}

/**
 * 校验多语言资源是否有需要修改的内容，如果属性的值是字符串则视为有需要修改的内容
 * @param {object} object 多语言资源对象
 * @returns 是否有值
 */
export function validateValue(object) {
  let hasStringValue = false
  const valueArray = Object.values(object)
  while (!hasStringValue && valueArray.length > 0) {
    const value = valueArray.shift()
    if (typeof value == 'string') {
      hasStringValue = true
    } else if (typeof value == 'object') {
      const array = Object.values(value)
      valueArray.unshift(...array)
    }
  }
  return hasStringValue
}

// 《简繁体名词对照表》
var twArr = [
  { chtw: '局域網', tw: '區域網路' },
  { chtw: '域帳戶', tw: '網域帳戶' },
  { chtw: '網網域帳戶', tw: '網域帳戶' },       // 網域帳戶 会被修改为 網網域帳戶，这边改回来
  { chtw: '采用域管理', tw: '採用網域管理' },
  { chtw: '域', tw: '網域' },
  { chtw: '網網域', tw: '網域' },              // 網域 会被修改为 網網域，这边改回来
  { chtw: '區網域網路', tw: '區域網路' },       // 區域網路 会被修改为 區網域網路，这边改回来
  { chtw: '區網域', tw: '區域' },              // 區域 会被修改为 區網域，这边改回来
  { chtw: 'Mask地址', tw: '子網路遮罩' },
  { chtw: 'U盤', tw: '隨身碟' },
  { chtw: '按', tw: '依' },                    // 代表按照意思的时候才是 依
  { chtw: '依鈕', tw: '按鈕' },                // 代表动作按时，不改变
  { chtw: '依住', tw: '按住' },                // 代表动作按时，不改变
  { chtw: '依下', tw: '按下' },                // 代表动作按时，不改变
  { chtw: '依回車', tw: '按回車' },            // 代表动作按时，不改变
  { chtw: '依“F5”', tw: '按“F5”' },            // 代表动作按时，不改变
  { chtw: '那麼', tw: '那麼' },
  { chtw: '保存', tw: '儲存' },
  { chtw: '變量', tw: '變數' },
  { chtw: '餅圖', tw: '圓形圖' },
  { chtw: '并口', tw: '並列埠' },
  { chtw: '并', tw: '並' },
  { chtw: '采集', tw: '採集' },
  { chtw: '菜單', tw: '選單' },
  { chtw: '操作系統', tw: '作業系統' },
  { chtw: '常規', tw: '常態' },
  { chtw: '廠家', tw: '廠商' },
  { chtw: '串口', tw: '序列埠' },
  { chtw: '窗口', tw: '視窗' },

  { chtw: '新建', tw: '新增' },
  { chtw: '創建', tw: '建立' },
  { chtw: '新增立', tw: '新建立' },             // 新建立 会被修改为 新增立，这边改回来
  { chtw: '磁道', tw: '磁軌' },
  { chtw: '磁盤', tw: '磁碟' },
  { chtw: '磁碟驅動器', tw: '磁碟機' },
  { chtw: '驅動器', tw: '磁碟機' },
  { chtw: '存儲', tw: '儲存' },
  { chtw: '打開', tw: '開啟' },
  { chtw: '打印機', tw: '印表機' },
  { chtw: '打印', tw: '列印' },
  { chtw: '代理服務器', tw: '代理伺服器' },
  { chtw: '服務器', tw: '伺服器' },
  { chtw: '導出', tw: '匯出' },
  { chtw: '導入', tw: '匯入' },
  { chtw: '登陸', tw: '登入' },
  { chtw: '登錄', tw: '登入' },
  { chtw: '端口', tw: '連接埠' },
  { chtw: '隊列', tw: '佇列' },
  { chtw: '發帖', tw: '發文' },
  { chtw: '訪問', tw: '瀏覽' },
  { chtw: '分辨率', tw: '解析度' },
  { chtw: '分佈式', tw: '分散式' },
  { chtw: '拷貝', tw: '複製' },
  { chtw: '高級', tw: '進階' },

  { chtw: '工作組', tw: '工作群組' },
  { chtw: '共享', tw: '共用' },
  { chtw: '固件', tw: '韌體' },
  { chtw: '關于', tw: '關於' },
  { chtw: '于', tw: '於' },
  { chtw: '光標', tw: '游標' },
  { chtw: '光盤', tw: '光碟' },
  { chtw: '滾動條', tw: '捲軸' },
  { chtw: '後綴名', tw: '副檔名' },
  { chtw: '後綴', tw: '副檔名' },
  { chtw: '后綴名', tw: '副檔名' },
  { chtw: '后綴', tw: '副檔名' },
  { chtw: '后', tw: '後' },
  { chtw: '回收站', tw: '回收筒' },
  { chtw: '計算機', tw: '電腦' },
  { chtw: '加載', tw: '載入' },
  { chtw: '審計日志', tw: '監看日誌' },
  { chtw: '審計日誌', tw: '監看日誌' },
  { chtw: '行為審計系統', tw: '行為監看系統' },
  { chtw: '審計', tw: '查閱' },
  { chtw: '粘貼', tw: '貼上' },
  { chtw: '剪貼板', tw: '剪貼簿' },
  { chtw: '剪切板', tw: '剪貼簿' },
  { chtw: '剪切', tw: '剪下' },
  { chtw: '人體學接口設備', tw: '人性化介面裝置' },
  { chtw: '接口', tw: '介面' },
  { chtw: '屏幕截屏', tw: '截取螢幕' },
  { chtw: '屏幕', tw: '螢幕' },
  { chtw: '截屏', tw: '截取螢幕' },
  { chtw: '界面', tw: '介面' },

  { chtw: '程序', tw: '程式' },
  { chtw: '進程數', tw: '處理程序' },
  { chtw: '進程', tw: '程式' },
  { chtw: '程式程式', tw: '程式' },                // 程序進程 会被修改为 程式程式，这边修改为 程式
  { chtw: '處理程式', tw: '處理程序' },            // 處理程序 会被修改为 處理程式，这边改回来
  { chtw: '靜默卸載', tw: '幕後解除安裝' },
  { chtw: '句柄數', tw: '控制代碼' },
  { chtw: '刻錄', tw: '燒錄' },
  { chtw: '控制台', tw: '主控台' },
  { chtw: '控製臺', tw: '主控台' },
  { chtw: '寬帶', tw: '寬頻' },
  { chtw: '擴展名', tw: '副檔名' },
  { chtw: '錄像', tw: '錄影' },
  { chtw: '模塊', tw: '模組' },
  { chtw: '默認', tw: '預設' },
  { chtw: '昵稱', tw: '暱稱' },
  { chtw: '內存', tw: '記憶體' },
  { chtw: '內置', tw: '預設' },
  { chtw: '盤片', tw: '磁片' },
  { chtw: '配置', tw: '設定' },
  { chtw: '批量', tw: '批次' },
  { chtw: '前綴', tw: '首碼' },
  { chtw: '全盤', tw: '全碟' },

  { chtw: '日志', tw: '日誌' },
  { chtw: '軟件', tw: '軟體' },
  { chtw: '軟盤', tw: '軟碟' },
  { chtw: '掃描儀', tw: '掃描器' },
  { chtw: '殺毒', tw: '防毒' },
  { chtw: '閃存', tw: '快閃記憶體' },
  { chtw: '上傳', tw: '上傳' },
  { chtw: '設備', tw: '裝置' },
  { chtw: '預裝置份', tw: '預設備份' },                 // 預設備份 会被修改为 預裝置份，这边改回来
  { chtw: '設置', tw: '設定' },
  { chtw: '聲卡', tw: '音效卡' },
  { chtw: '聲明', tw: '宣告' },
  { chtw: '剩余空間', tw: '剩餘空間' },
  { chtw: '視頻', tw: '視訊' },
  { chtw: '屬性', tw: '內容' },
  { chtw: '鼠標', tw: '滑鼠' },
  { chtw: '數據緩存', tw: '資料暫存' },
  { chtw: '數據結構', tw: '資料結構' },
  { chtw: '數據', tw: '資料' },
  { chtw: '資料洩露防護系統', tw: '數據洩露防護系統' },  // 系统名 数据不翻译
  { chtw: '天銳資料防洩露', tw: '天銳數據防洩露' },      // 系统名 数据不翻译
  { chtw: '刷新', tw: '重新整理' },
  { chtw: '算法', tw: '演算法' },
  { chtw: '演演算法', tw: '演算法' },                   // 演算法 会被修改为 演演算法，这边改回来
  { chtw: '條數', tw: '筆數' },
  { chtw: '調製解調器', tw: '數據機' },
  { chtw: '資料機', tw: '數據機' },                     // 數據機 会被修改为 資料機，这边改回来
  { chtw: '帖子', tw: '發文' },
  { chtw: '個性化', tw: '個人化' },

  { chtw: '圖標', tw: '圖示' },
  { chtw: '推送', tw: '推播' },
  { chtw: '脫機', tw: '離線' },
  { chtw: '萬維網', tw: '全球資訊網' },
  { chtw: '網關', tw: '閘道' },
  { chtw: '網卡', tw: '網路介面卡' },
  { chtw: '網絡', tw: '網路' },
  { chtw: '網上鄰居', tw: '網路位置' },
  { chtw: '可執行文件', tw: '可執行檔' },
  { chtw: '字體文件', tw: '字型檔案' },
  { chtw: '許可文件', tw: '許可檔案' },
  { chtw: '文件夾', tw: '資料夾' },
  { chtw: '文件', tw: '檔案' },
  { chtw: '文本', tw: '文字' },
  { chtw: '文檔', tw: '檔案' },
  { chtw: '明檔案案', tw: '明文檔案' },                   // 明文檔案 会被修改为 明檔案案，这边改回来
  { chtw: '密檔案案', tw: '密文檔案' },                   // 密文檔案 会被修改为 密檔案案，这边改回来
  { chtw: '物理地址', tw: '實體位址' },
  { chtw: '地址', tw: '位址' },
  { chtw: '系統補丁', tw: '系統更新' },
  { chtw: '系統盤', tw: '系統碟' },
  { chtw: '系統自動應答', tw: '系統自動審批' },
  { chtw: '顯卡', tw: '顯示卡' },
  { chtw: '線程數', tw: '執行緒' },
  { chtw: '項目', tw: '專案' },
  { chtw: '協議', tw: '合約' },
  { chtw: '卸載', tw: '解除安裝' },

  { chtw: '芯片', tw: '晶片' },
  { chtw: '信息', tw: '資訊' },
  { chtw: '虛擬機', tw: '虛擬機器' },
  { chtw: '虛擬機器器', tw: '虛擬機器' },                   // 虛擬機器 会被修改为 虛擬機器器，这边改回来
  { chtw: '選中', tw: '選取' },
  { chtw: '以太網', tw: '乙太網路' },
  { chtw: '映像', tw: '影像' },
  { chtw: '硬件', tw: '硬體' },
  { chtw: '硬盤', tw: '硬碟' },
  { chtw: '用戶', tw: '使用者' },
  { chtw: '優先級', tw: '優先順序' },
  { chtw: '源路徑', tw: '來源路徑' },
  { chtw: '來來源路徑', tw: '來源路徑' },                   // 來源路徑 会被修改为 來來源路徑，这边改回来
  { chtw: '遠程', tw: '遠端' },
  { chtw: '運算符', tw: '運算子' },
  { chtw: '支持', tw: '支援' },
  { chtw: '製作', tw: '製作' },
  { chtw: '質量', tw: '品質' },
  { chtw: '終端製程', tw: '終端程式' },
  { chtw: '終端屏保', tw: '終端螢幕保護' },
  { chtw: '主板', tw: '主機板' },
  { chtw: '注冊表', tw: '登錄檔' },
  { chtw: '登入檔', tw: '登錄檔' },                         // 登錄檔 会被修改为 登入檔，这边改回来
  { chtw: '注冊', tw: '註冊' },

  { chtw: '注銷', tw: '登出' },
  { chtw: '柱狀圖', tw: '長條圖' },
  { chtw: '裝載', tw: '安裝' },
  { chtw: '資源管理器', tw: '檔案總管' },
  { chtw: '資源瀏覽器', tw: '資源總管' },
  { chtw: '字符', tw: '字元' },
  { chtw: '字號', tw: '字型大小' },
  { chtw: '總線', tw: '匯流排' },
  { chtw: '向導', tw: '導引' },
  { chtw: '目標盤符', tw: '目標硬碟' },
  { chtw: '范圍', tw: '範圍' },
  { chtw: '密碼口令', tw: '密碼' },
  { chtw: '口令', tw: '密碼' },
  { chtw: '起始', tw: '啟始' },
  { chtw: '綠盾光盤刻錄', tw: '預設光碟燒錄' },
  { chtw: '復制', tw: '複製' },
  { chtw: '標志', tw: '標誌' },
  { chtw: '制造商', tw: '製造商' },
  { chtw: '運行', tw: '執行' },
  { chtw: '發送', tw: '傳送' },
  { chtw: '激活', tw: '啟用' },
  { chtw: '證書', tw: '憑證' },
  { chtw: '水印', tw: '浮水印' },
  { chtw: '浮浮水印', tw: '浮水印' },                        // 浮水印 会被修改为 浮浮水印，这边改回来
  { chtw: '數字簽名', tw: '數位簽章' },
  { chtw: '類型', tw: '型别' },
  { chtw: '綁定', tw: '繫結' },
  { chtw: '插件', tw: '外掛程式' },
  { chtw: '重命名', tw: '重新命名' },
  { chtw: '參數', tw: '引數' },
  { chtw: '調試', tw: '除錯' },
  { chtw: '在線', tw: '線上' },
  { chtw: '存線上上', tw: '存在線上' },
  { chtw: '查看', tw: '檢視' },
  { chtw: '穀歌', tw: '谷歌' },
  { chtw: '搜索', tw: '搜尋' },
  { chtw: '知識產權', tw: '智慧財產權' },
  { chtw: '子網掩碼', tw: '子網路遮罩' },
  { chtw: '命令行', tw: '命令列' },
  { chtw: '光驅', tw: '光碟機' },
  { chtw: '軟驅', tw: '軟碟機' },
  { chtw: '源代碼', tw: '原始碼' },
  { chtw: '正则表达式', tw: '正規表示式' },
  { chtw: '正則運算式', tw: '正規表示式' },
  { chtw: '通配符', tw: '萬用字元' },
  { chtw: '點擊', tw: '點選' },
  { chtw: '標識符', tw: '識別符號' },
  { chtw: '分割', tw: '分隔' },
  { chtw: '分區', tw: '分割區' },
  { chtw: '分隔區', tw: '分割區' }        // 分割區 会被修改为 分隔區，这边改回来

]
