function padNumber(num) {
  return num < 10 ? '0' + num : num;
}

/**
 * 获取系统当前时间(大屏中每个页面都显示当前时间)
 * @returns {string}
 */
export function getCurrentTime() {
  const now = new Date();
  const year = now.getFullYear();
  const month = padNumber(now.getMonth() + 1);
  const day = padNumber(now.getDate());
  const hours = padNumber(now.getHours());
  const minutes = padNumber(now.getMinutes());
  const seconds = padNumber(now.getSeconds());
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
/**
 * 获取定时器时长（大屏中几乎每张报表都用到好多定时器，根据大屏配置动态显示定时器时长）
 * @param value 文本框中输入的时长（正整数）
 * @param type 0：秒，1：分钟，2：小时
 * @returns {number} 返回时长，直接用于setInterval方法的时长即可
 */
export function getSetIntervalTime(value, type) {
  let setIntervalTime = 0
  if (type === 0) {
    // 1秒=1*1000
    setIntervalTime = value * 1000
  } else if (type === 1) {
    // 1分钟=1*1000*60
    setIntervalTime = value * 1000 * 60
  } else if (type === 2) {
    // 1小时=1*1000*60*60
    setIntervalTime = value * 1000 * 60 * 60
  }
  return setIntervalTime
}
