import { getMultipleVideoInfo, getRelLogVideo } from '@/api/behaviorManage/monitor/videoRecorder'
import moment from 'moment';
import i18n from '@/lang';
import Vue from 'vue';
import store from '@/store'

/**
 * 组件添加查看录屏按钮
 * ！！ 注意：查看录屏的权限就是查看详情，只要管理员有日志的查看详情权限，就可以查看录屏
 * @param that 组件的this对象
 * @param isShowFunc 查看录屏的field的isShow方法，优先使用传入的参数，如果参数为undefined，尝试获取原操作栏中的查看详情按钮的isShow，还是没有，则获取操作栏的hidden属性
 * @param colModelKey 组件中表格属性ColModel的key
 * @param reAddTimes 重新添加的次数，当组件在创建时，直接调用add方法，但是获取控制台是否有屏幕录像模块的请求还没有结束，这个时候需要进行重试，该值代表重试的次数，最多重试60次
 */
export function addViewVideoBtn(that, isShowFunc, colModelKey, reAddTimes) {
  if (!hasSale()) { return }
  // 操作栏在ColModel表格的最后一栏
  const colModel = that[colModelKey] || that['colModel']
  if (colModel && colModel.length > 0) {
    const operatorField = colModel[colModel.length - 1]
    // 包含操作栏，添加查看录屏到操作栏中
    const buttonOperatorField = operatorField['buttons'];
    if (operatorField['type'] === 'button' && buttonOperatorField) {
      if (buttonOperatorField.includes(obj => obj.label === i18n.t('pages.viewScreenRecord'))) {
        return
      }
      const fixedWidth = operatorField['fixedWidth']
      if (fixedWidth) {
        operatorField['fixedWidth'] = String(80 + Number(fixedWidth))
      } else {
        if (operatorField['width']) {
          operatorField['width'] = String(80 + Number(operatorField['width']))
        }
      }
      const detailInfo = buttonOperatorField.find(f => f.label === 'detail')
      const isShow = isShowFunc || (detailInfo && detailInfo['isShow']) ||
        (() => operatorField['hidden'] instanceof Function ? !operatorField['hidden']() : !operatorField['hidden'])
      buttonOperatorField.push(
        {
          label: i18n.t('pages.viewScreenRecord'),
          // disabledFormatter: disabledLogViewerBtn,
          click: row => handleViewLogVideo(row, that),
          isShow: row => isShow(row) && !disabledLogViewerBtn(row),
          width: '80'
        }
      )
    } else {
      // 不包含操作栏，新增操作栏，并添加查看录屏到操作栏中
      const isShow = isShowFunc || (() => true)
      colModel.push(
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            {
              label: i18n.t('pages.viewScreenRecord'),
              // disabledFormatter: disabledLogViewerBtn,
              click: row => handleViewLogVideo(row, that),
              isShow: row => isShow(row) && !disabledLogViewerBtn(row),
              width: '100'
            }
          ]
        }
      )
    }
  }
  return true
}

/**
 * 异步获取审计日志关联的录屏信息
 * @param rowData 审计日志列表
 * @param detailPermissionCode 查看详情的编码；可以传入方法 （先判断是否有查看录屏的权限，没有则不进行查询）
 * @param searchReport 数据源
 * @param terminalIdKey 终端Id对应rowData单对象的key(terminalIdKye || 'terminalId' || 'termId')
 * @param logTimeKey 日志时间对应rowData单对象的key(logTimeKey || 'createTime' || 'logTime')
 */
export function asyncGetLogVideoInfo(rowData, detailPermissionCode, searchReport, terminalIdKey, logTimeKey) {
  if (!hasSale()) { return null }
  if ((detailPermissionCode && (typeof detailPermissionCode === 'function' ? !detailPermissionCode() : !Vue.prototype.hasPermission(detailPermissionCode))) || !rowData || rowData.length === 0) { return }
  const logParams = rowData.map(item => {
    return {
      terminalId: terminalIdKey ? item[terminalIdKey] : item['terminalId'] || item['termId'],
      createTime: logTimeKey ? item[logTimeKey] : item['createTime'] || item['logTime']
    }
  }).filter(item => item.terminalId && item.createTime)
    .map(item => {
      if (typeof item.createTime === 'number') {
        let time = item.createTime
        if (String(item.createTime).length === 13) {
          time = time / 1000
        }
        time = moment.unix(time).format('YYYY-MM-DD HH:mm:ss')
        item.createTime = time
      }
      return item
    })
  // 当不存在searchReport时，默认数据源选择3 查询范围 DLP+报表 更广
  const param = { logParams, searchReport: searchReport || (store.getters.reportModule ? 3 : 1) }
  const requestMethod = () => {
    getRelLogVideo(param).then(res => {
      const data = res.data;
      if (rowData.length === data.length) {
        for (let i = 0; i < data.length; i++) {
          rowData[i].videoInfo = data[i]
        }
      }
      // 重新渲染表格
      rowData.splice(0, 0)
    })
  }
  requestMethod()
  return requestMethod
}

/**
 * 查看录屏按钮方法
 * @param row
 * @param that
 * @param refKey
 */
export function handleViewLogVideo(row, that, refKey) {
  getMultipleVideoInfo(row.videoInfo).then(r => {
    row.videoInfo.multipleScreens = r.data
    that.$refs[refKey || 'videoViewer'].play(row.videoInfo)
  })
}

/**
 * 禁用录屏按钮方法(查看录屏形式不再使用禁/启用，使用show的形式),判断规则交由后端处理，如果审计日志当前时间区间无录屏，则videoInfo为空
 * @param row
 * @returns {boolean|*}
 */
export function disabledLogViewerBtn(row) {
  return !row.videoInfo
}

/**
 * 是否存在屏幕录像的销售模块
 */
export function hasSale() {
  return Vue.prototype.hasPermission('137')
}

export function alertError(status) {
  const statusMap = {
    1: i18n.t('exception.videoNotExist'),
    2: i18n.t('exception.sysBusy'),
    3: i18n.t('exception.noFoundFileServer'),
    4: i18n.t('exception.noEffectVideoData')
  }
  Vue.prototype.$message({
    message: statusMap[status] || i18n.t('pages.transferStatusDict32'),
    type: 'error',
    duration: 4000
  })
}
