import { getDict, getDictLabel, getOsTypeDict, getTermTypeDict, getLoginModeDict } from '@/utils/dictionary'
import { findNode } from '@/utils/tree'
import store from '@/store'
import i18n from '@/lang'

export function osTypeFormatter(row, data) {
  const label = getDictLabel(getOsTypeDict(), data)
  return !label ? i18n.t('pages.collectAll') : label
}

export function osTypeIconFormatter(row) {
  const dict = getDict(getOsTypeDict(), row.osType)
  return dict ? [{ class: dict.iconClass, title: i18n.t('table.osType') + ': ' + dict.label }] : null
}

export function deptNameFormatter(row, data, col, groupTreeData) {
  const groupNames = []
  if (data) {
    if (!groupTreeData) {
      groupTreeData = store.getters.deptTree
    }
    if (typeof data === 'object') {
      data.forEach(groupId => {
        const node = findNode(groupTreeData, groupId, 'dataId')
        if (node) groupNames.push(node.label)
      })
    }
    if (typeof data === 'number') {
      const node = findNode(groupTreeData, data, 'dataId')
      if (node) groupNames.push(node.label)
    }
  }
  return groupNames.join(',')
}

export function termStatusIconFormatter(data) {
  const icons = []
  const termTypeDict = getDict(getTermTypeDict(), data.type)
  if (termTypeDict) {
    const icon = { class: termTypeDict.icon, title: termTypeDict.label }
    if (data.loginStatus && data.loginStatus.status != 0) {
      icon.style = { 'color': 'green' }
      icon.title = i18n.t('pages.online') + '\r\n' + icon.title
    } else if (data.guid) {
      icon.title = i18n.t('pages.offline') + '\r\n' + icon.title
    } else {
      // 永久离线终端
      icon.title = i18n.t('pages.unused') + '\r\n' + icon.title
      icon.class += '-x'
    }
    icons.push(icon)
  }
  return icons
}

export function termStatusIconFormatterByStore(termType, termId) {
  const icons = []
  const termTypeDict = getDict(getTermTypeDict(), termType)
  if (termTypeDict) {
    const icon = { class: termTypeDict.icon, title: termTypeDict.label }
    let loginStatus = null
    if (store.getters.termStatusMap) {
      loginStatus = store.getters.termStatusMap[termId]
    }
    if (loginStatus && loginStatus.status != 0) {
      icon.style = { 'color': 'green' }
      icon.title = i18n.t('pages.online') + '\r\n' + icon.title
    } else {
      icon.title = i18n.t('pages.offline') + '\r\n' + icon.title
    }
    icons.push(icon)
  }
  return icons
}

export function loginModeFormatter(loginMode) {
  const loginModeDict = getLoginModeDict()
  for (let i = 0; i < loginModeDict.length; i++) {
    if (loginModeDict[i].value == loginMode) {
      return loginModeDict[i].label
    }
  }
  return ''
}

export function timeInfoFormatter(row, data) {
  const timeOptions = store.getters.timeOptions
  const option = timeOptions.find(op => op.value == (data || 1))
  return option ? option.label : ''
}

/**
 * 生效时间(日期)格式化，因有些策略仅有生效时间不存在日期，所以在新增一个方法
 */
export function timeDateInfoFormatter(row, data) {
  const str = timeInfoFormatter(row, data)
  return str ? str + `(${timePeriodFormatter(row)})` : str
}

/**
 * 生效日期格式化
 */
export function timePeriodFormatter(row) {
  if (row.isPermanent == 1 || (!row.isPermanent && row.isPermanent != 0) || (!row.beginTime && !row.endTime)) {
    return i18n.t('pages.longTermEffective')
  }
  let anyNotExistChar = ''
  if (row.beginTime && !row.endTime) {
    anyNotExistChar = '>='
  } else if (!row.beginTime && row.endTime) {
    anyNotExistChar = '<='
  }
  return `${anyNotExistChar}${(row.beginTime || '').substr(0, 10)}${!anyNotExistChar ? ' ~ ' : ''}${(row.endTime || '').substr(0, 10)}`
}

export function activeIconFormatter(row) {
  const activeValue = { true: true, false: false, 1: true, 0: false }[row.active]
  const activeOptions = {
    true: i18n.t('text.enable'),
    false: i18n.t('text.disable')
  }
  const label = activeOptions[activeValue]
  return activeValue ? [{ class: 'active', title: label }] : [{ class: 'offline', title: label, style: 'color: #888;' }]
}

export function stgActiveIconFormatter(row) {
  const activeValue = { true: true, false: false, 1: true, 0: false }[row.active]
  const activeOptions = {
    true: i18n.t('text.enable'),
    false: i18n.t('text.disable2')
  }
  const label = activeOptions[activeValue]
  return activeValue ? [{ class: 'active', title: label }] : [{ class: 'offline', title: label, style: 'color: #888;' }]
}

export function logSourceFormatter(row, data) {
  const name = row.searchReport === 2 ? 'report' : 'database'
  const icon = require(`@/assets/${name}.png`)
  const tips = row.searchReport === 2 ? i18n.t('route.reportServer') : i18n.t('route.DBServer')
  const img = `<img src="${icon}" style="margin-right: 5px; border-radius: 3px; vertical-align: text-bottom;" alt="${name}" title="${tips}">`
  if (data && data.startsWith(img)) {
    return data
  }
  return img + data
}

/**
 * 策略生效对象继承图标格式化：用于告知当前策略生效对象的下级对象不会继承当前策略且未配置下级对象的策略
 * @param row
 * @param vm
 * @returns {*|*[]}
 */
export function stgEntityIconFormatter(row, vm) {
  if (row && vm && vm.$refs && vm.$refs['stgExtend']) {
    const activeValue = { true: true, false: false, 1: true, 0: false }[row.active]
    if (activeValue && vm.$refs['stgExtend'].stgExtendIconFormatter) {
      return vm.$refs['stgExtend'].stgExtendIconFormatter(row)
    }
  }
  return []
}
