import MD5 from 'js-md5'

/**
 * 计算文件的md5
 * @param file {File|Blob} 要计算 MD5 值的文件
 * @param onResult {function} MD5 计算结果回调，Function(md5)
 * @param onProgress {function?} MD5 计算进度回调，Function(loaded, total, abort)
 * @returns {function} 返回中断函数，用于中断MD5计算过程
 */
MD5.md5File = function(file, onResult, onProgress) {
  const md5 = MD5.create()
  const reader = file.stream().getReader()
  const doProgress = 'function' === typeof onProgress
  let loaded = 0
  let aborted = false
  const abort = reason => {
    reader.cancel(reason)
    aborted = true
  }
  const pump = () => reader.read().then(res => {
    if (res.done) {
      return true
    }
    if (doProgress) {
      loaded += res.value.length
      onProgress(loaded, file.size, abort)
    }
    if (aborted) {
      return false
    }
    md5.update(res.value)
    return pump()
  })
  pump().then(done => {
    if (done) {
      onResult(md5.hex())
    }
  })
  return abort
}

export default MD5
