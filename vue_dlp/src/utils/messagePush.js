import i18n from '@/lang';
import { findNode } from '@/utils/tree';

export function getTypeOptions() {
  return [{ value: 1, label: i18n.t('pages.user') }, { value: 2, label: i18n.t('pages.terminal') }]
}

export function getReportScopeList() {
  return [{ id: 2, name: i18n.t('pages.company') }, { id: 1, name: i18n.t('pages.dept') }, { id: 0, name: i18n.t('pages.scopeOption3') }]
}
export function getPushTypeDict() {
  return {
    1: i18n.t('pages.pushType1'), // 手机短信推送
    2: i18n.t('pages.pushType2'), // 微信公众号推送
    3: i18n.t('pages.pushType3'), // 邮件推送
    4: i18n.t('pages.pushType4') // 控制台弹窗推送
  }
}

export function getSplitChar() {
  return [
    { label: '逗号', value: ',' },
    { label: '换行', value: '\n' },
    { label: '空格', value: ' ' },
    { label: '自定义', value: '0' }
  ]
}

export function getReportTimeTypesList() {
  return [
    { value: 5, label: '日报' },
    { value: 2, label: '周报' },
    { value: 1, label: '月报' },
    { value: 3, label: '季报' },
    { value: 4, label: '年报' }
  ]
}
export function getDateTypeOptions() {
  return [
    { value: 5, label: i18n.t('pages.dateTypeOptions1') },
    { value: 2, label: i18n.t('pages.dateTypeOptions2') },
    { value: 1, label: i18n.t('pages.dateTypeOptions3') },
    { value: 3, label: i18n.t('pages.dateTypeOptions4') },
    { value: 4, label: i18n.t('pages.dateTypeOptions5') }
  ]
}
/**
 * 格式化推送内容
 * @param row 行JSON数据
 * @param data 当前列数据
 * @param reportLists 报表类型列表
 * @returns {string}
 */
export function dataContentFormatter(row, data, reportLists, deptTree, termUserList, newLine) {
  if (row.bizCode == 1001 || row.dataType == 1) {
    const reportJson = data
    const strArr = []
    // 报表类型
    const reportType = reportJson.reportType || 0
    strArr.push(formatterReportType(reportType, reportJson.customizeTypeIds, reportLists))
    // 报表范围
    let scope = i18n.t('pages.reportScope') + '：' + (getReportScopeList().find(item => item.id == reportJson.groupType) || {})['name']
    if (reportJson.objectIds) {
      const tempArr = []
      if (reportJson.groupType == 1 && deptTree) {
        reportJson.objectIds.forEach(item => {
          const node = findNode(deptTree, item, 'dataId')
          if (node) {
            tempArr.push(node.label)
          }
        })
      } else if (reportJson.groupType == 0 && termUserList) {
        reportJson.objectIds.forEach(item => {
          const index = termUserList.findIndex(term => term.id == item)
          if (index >= 0) {
            tempArr.push(termUserList[index].name)
          }
        })
      }
      if (tempArr.length > 0) scope += '(' + tempArr.join(',') + ')'
    }
    strArr.push(scope)
    // 报表日期
    let str = i18n.t('pages.dimBaseType') + '：'
    str += formatterDim(reportJson)
    strArr.push(str)
    // 统计类型
    const typeOptions = getTypeOptions()
    for (let i = 0; i < typeOptions.length; i++) {
      if (reportJson.countByObject == typeOptions[i].value) {
        strArr.push(i18n.t('pages.countByObject') + '：' + typeOptions[i].label)
        break
      }
    }

    if (newLine) { return strArr.join('</br>') } else { return strArr.join(', ') }
  }
}

export function formatterDim(reportJson) {
  const timeTypes = getReportTimeTypesList()
  for (let i = 0; i < timeTypes.length; i++) {
    if (timeTypes[i].value == reportJson.dimBaseType) {
      return timeTypes[i].label
    }
  }
  return ''
}

export function formatterReportType(reportType, customizeTypeIds, reportLists) {
  let str = ''
  const arr = []
  if (reportLists && reportLists.length == 2) {
    const baseReportList = reportLists[0].children
    const customizeReportList = reportLists[1].children
    reportType = reportType || 0
    for (let i = 0; i < baseReportList.length; i++) {
      const number = 1 << (baseReportList[i].id - 1);
      if ((reportType & number) == number) {
        arr.push(baseReportList[i].label)
      }
    }
    if (customizeTypeIds) {
      const customizeIdList = Array.isArray(customizeTypeIds) ? customizeTypeIds : customizeTypeIds.split(',')
      customizeIdList.forEach(id => {
        if (customizeReportList && customizeReportList.length > 0) {
          const index = customizeReportList.findIndex(item => item.id == id)
          if (index >= 0) {
            arr.push(customizeReportList[index].label)
          }
        }
      })
    }
    str += i18n.t('pages.reportType') + '：' + arr.join('、')
  }
  return str
}

export function receiveObjectFormatter(row, data, infoBindUserList) {
  if (infoBindUserList) {
    return data.map(item => {
      for (let i = 0; i < infoBindUserList.length; i++) {
        const index = infoBindUserList[i].children.findIndex(report => report.dataId == item);
        if (index >= 0) {
          return infoBindUserList[i].children[index].label
        }
      }
    }).join('、')
  }
  return ''
}

export function pushMethodFormatter(row, data) {
  let num = data + 0;
  let str = ''
  const pushTypeDict = getPushTypeDict()
  let i = 1
  while (num > 0) {
    if ((num & 1) == 1) {
      if (str.length > 0) { str += ',' }
      str += pushTypeDict[i]
    }
    num >>= 1
    i <<= 1
  }
  return str
}
