import i18n from '@/lang';

export function getCommonMatchingRules() {
  return [
    { value: i18n.t('pages.regular_common.digital'), regular: '[0-9]' },
    { value: i18n.t('pages.regular_common.non_digital'), regular: '[^0-9]' },
    { value: i18n.t('pages.regular_common.white_space'), regular: '\\s' },
    { value: i18n.t('pages.regular_common.non_white_space'), regular: '\\S' },
    { value: i18n.t('pages.regular_common.char'), regular: '\\w' },
    { value: i18n.t('pages.regular_common.non_char'), regular: '\\W' },
    { value: i18n.t('pages.regular_common.lowercase'), regular: '[a-z]' },
    { value: i18n.t('pages.regular_common.capitalcase'), regular: '[A-Z]' },
    { value: i18n.t('pages.regular_common.letter'), regular: '[a-zA-Z]' },
    { value: i18n.t('pages.regular_common.chinese'), regular: '[\\u4e00-\\u9fa5]' },
    { value: i18n.t('pages.regular_common.except_newlines'), regular: '.' }
  ]
}
