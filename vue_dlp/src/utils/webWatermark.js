/**
 * 网页添加水印的方法
 */
(function() {
  setInterval(() => {
    let watermarkEle = document.getElementById('web-watermark');
    if (!watermarkEle) {
      watermarkEle = document.createElement('div');
      document.body.appendChild(watermarkEle);
    }
    watermarkEle.id = 'web-watermark';
    const watermarkText = 'web水印';
    watermarkEle.style.background = `url("data:image/svg+xml,%3Csvg width='200' height='200' xmlns='http://www.w3.org/2000/svg'%3E%3Ctext x='50%25' y='50%25' font-size='18' fill-opacity='0.5' text-anchor='middle' dominant-baseline='middle' transform='rotate(-45, 100 100)'%3E${watermarkText}%3C/text%3E%3C/svg%3E")`;
    watermarkEle.style.backgroundSize = '150px';
    watermarkEle.style.position = 'fixed';
    watermarkEle.style.top = '0px';
    watermarkEle.style.bottom = '0px';
    watermarkEle.style.left = '0px';
    watermarkEle.style.right = '0px';
    // pointer-events 值为 none：该元素永远不会成为鼠标事件的 target。
    watermarkEle.style.pointerEvents = 'none';
    watermarkEle.style.zIndex = 10000;
    watermarkEle.style.opacity = 1;
  }, 1000 * 1);
})()
