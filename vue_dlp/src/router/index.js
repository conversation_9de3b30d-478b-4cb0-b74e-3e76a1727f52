import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* Router Modules */
import systemRouter from './modules/system'
import terminalRouter from './modules/terminalManage'
import behaviorManageRouter from './modules/behaviorManage'
import netManageRouter from './modules/netManage'
import dataEncryptionRouter from './modules/dataEncryption'
import contentStrategyRouter from './modules/contentStrategy'
import approvalRouter from './modules/approvalManage'
import behaviorAuditingRouter from './modules/behaviorAuditing'
import warningRouter from './modules/warning'
import reportRouter from './modules/report'

/**
 * 注意:子菜单只在路由的 children.length >= 1 时出现
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   如果设置为true，路由可以访问，但不会显示在菜单中，也不能搜索(默认为false)
 * disabled: true                 如果设置为true，路由不可访问，且不会显示在菜单中，也不能搜索(默认为false)
 * alwaysShow: true               如果设置为true，将始终显示根菜单
 *                                如果不设置alwaysShow，当项目有多个子路由时，它将成为嵌套模式，
 *                                只有一个子路由时，不显示根菜单
 * redirect: noRedirect           当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * noComponent：true              设置noComponent为true，则当前路由不导入组件内容，用于不需要显示页面的菜单选项
 * name:'router-name'             设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 *                                路由name与组件name必须完全一样，页面才能正常缓存，特殊情况需另做处理(src/layout/components/AppMain.vue)
 * meta : {
    roles: ['admin','editor']    控制页面角色(可以设置多个角色)
    title: 'title'               显示在侧边栏和面包屑的名称（推荐设置）
    icon: 'svg-name'             设置该路由的图标
    breadcrumb: false            如果设置为false，则该项将隐藏在breadcrumb中(默认为true)
    activeMenu: '/example/list'  如果设置路径，侧栏将突出显示您设置的路径
    noCache: true                如果为true，页面将不会被缓存(默认为false)
    affix: true                  如果为true，则tag将固定在tags-view上
    noSearch: true               如果为true，则通过搜索框无法搜索到这个菜单
  }
 */

/**
 * constantRoutes
 * 没有权限要求的基本页
 * 所有角色都可以访问
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/autoLogin',
    component: () => import('@/views/autoLogin/index'),
    hidden: true
  },
  {
    path: '/findPassword',
    component: () => import('@/views/findPwd/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },
  //  正在数据库恢复
  {
    path: '/recovering',
    component: () => import('@/views/recovering'),
    //  allowBack: false 禁止浏览器后退
    name: 'Recovering',
    meta: { allowBack: false, title: 'Recovering' },
    hidden: true
  },
  {
    path: '/',
    hidden: true,
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index'),
        meta: { title: 'dashboard', icon: 'dashboard', affix: true }
      }
    ].concat(process.env.NODE_ENV == 'development' ? [
      // 仅开发模式下才能访问
      {
        path: 'iconList',
        name: 'iconList',
        component: () => import('@/views/example/iconList'),
        meta: { title: '图标列表' }
      }
    ] : [])
  }
]

/**
 * asyncRoutes
 * 需要根据用户角色动态加载的路由
 */
export const asyncRoutes = [
  systemRouter,               // code: A11
  terminalRouter,             // code: B11
  behaviorManageRouter,       // code: C11
  netManageRouter,            // code: D11
  dataEncryptionRouter,       // code: E11
  contentStrategyRouter,      // code: F11
  approvalRouter,             // code: G11
  behaviorAuditingRouter,     // code: H11
  warningRouter,              // code:
  reportRouter,               // code: J11

  {
    path: '/config',
    hidden: true,
    // meta: { title: 'SpecialConfig' },
    component: Layout,
    children: [
      {
        code: '130',
        path: 'specialConfig',
        component: () => import('@/views/system/configManage/specialConfig'),
        name: 'SpecialConfig',
        meta: { title: 'SpecialConfig', noSearch: true }
      },
      {
        code: '127',
        path: 'hiddenTool',
        component: () => import('@/views/system/configManage/hiddenTool'),
        name: 'HiddenTool',
        meta: { title: 'HiddenTool', noSearch: true }
      },
      {
        code: '126',
        path: 'langExport',
        component: () => import('@/views/system/configManage/langExport'),
        name: 'LangExport',
        meta: { title: 'LangExport', noSearch: true }
      },
      {
        code: '122',
        name: 'SeamlessReplace',
        path: 'seamlessReplace',
        component: () => import('@/views/dataEncryption/encryption/seamlessReplace'),
        meta: { title: 'seamlessReplace', noSearch: true }
      },
      {
        code: '129',
        path: 'Scheduled',
        component: () => import('@/views/system/scheduled'),
        name: 'Scheduled',
        meta: { title: 'Scheduled', noSearch: true }
      },
      {
        code: '131',
        path: 'signatureData',
        component: () => import('@/views/system/baseData/signatureData'),
        name: 'SignatureData',
        meta: { title: 'signatureData', noSearch: true }
      },
      {
        code: '132',
        path: 'processInject',
        component: () => import('@/views/system/terminalManage/processInject'),
        name: 'ProcessInject',
        meta: { title: 'processInject', noSearch: true }
      },
      {
        code: '133',
        path: 'LD',
        component: () => import('@/views/system/configManage/ldConfig'),
        name: 'LvDun',
        meta: { title: 'lvDun', noSearch: true }
      },
      {
        code: '123',
        name: 'InterfaceAuth',
        path: 'InterfaceAuth',
        component: () => import('@/views/dataEncryption/encryption/interfaceAuth'),
        meta: { title: 'interfaceAuth', noSearch: true }
      },
      {
        code: 'G22',
        path: 'sensitiveConfig',
        component: () => import('@/views/dataEncryption/encryption/sensitiveFileConfig/sensitiveConfig'),
        name: 'SensitiveConfig',
        meta: { title: 'configwjwf', noSearch: true }
      },
      {
        code: '134',
        path: 'authorizeUninstall',
        component: () => import('@/views/system/terminalManage/terminal/authorizeUninstall'),
        name: 'AuthorizeUninstall',
        meta: { title: 'authorizeUninstall', noSearch: true }
      },
      {
        code: '138',
        path: 'softwareBlackList',
        component: () => import('@/views/softwareManage/strategy/softwareBlackList'),
        name: 'SoftwareBlackList',
        meta: { title: 'softwareBlackList', noSearch: true }
      },
      {
        code: '135',
        path: 'downloadHistory',
        component: () => import('@/components/DownloadManager/history'),
        name: 'DownloadHistory',
        meta: { title: 'downloadHistory', noSearch: true }
      },
      {
        code: '136',
        path: 'downloadSetting',
        component: () => import('@/components/DownloadManager/setting'),
        name: 'DownloadSetting',
        meta: { title: 'downloadSetting', noSearch: true }
      },
      {
        code: '128',
        path: 'idmApiTool',
        component: () => import('@/views/system/configManage/dataSourceConfig/idmApiTool'),
        name: 'IdmApiTool',
        meta: { title: 'idmApiTool', noSearch: true }
      },
      {
        code: 'A4G',
        path: 'nacServerConfig',
        component: () => import('@/views/system/configManage/nacServerConfig'),
        name: 'NacServerConfig',
        meta: { title: 'nacServerConfig' }
      },
      {
        code: 'B2V',
        path: 'terminalGrpNickName',
        component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/terminalGrpNickName'),
        name: 'terminalGrpNickName',
        meta: { title: '终端分组和名称规范（旧终端）', noSearch: true, isLog: false }
      },
      {
        code: '139',
        path: 'termGuidBlacklist',
        component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/configManage/termGuidBlacklist'),
        name: 'TermGuidBlacklist',
        meta: { title: 'termGuidBlacklist', noSearch: true }
      }
    ]
  },
  // 404页面必须放在最后!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

// 路由只按code作为主键，title和path作为value
export function toCodeMap(routes, parentPath) {
  const codePathObj = {}
  parentPath = !parentPath ? '' : parentPath
  routes.forEach(route => {
    const curPath = parentPath + (route.path.startsWith('/') ? route.path : '/' + route.path)
    if (route.children) {
      const childCodePathObj = toCodeMap(route.children, curPath)
      Object.assign(codePathObj, childCodePathObj)
    }
    // 常用菜单的路由不加入map中
    if (route.code && curPath.indexOf('common_routes') == -1) {
      codePathObj[route.code] = { title: route.meta.title, path: curPath }
    }
  })
  return codePathObj
}

/**
 * 1、过滤掉登录账号的路由中不存在的menuCode对应的option
 * 2、在option中添加path和title
 */
export function formatMenuCode(routes, option, menuCodeField) {
  if (!menuCodeField) menuCodeField = 'menuCode'
  const options = Array.isArray(option) ? option : [option]
  const codeObj = toCodeMap(routes)
  for (let i = 0; i < options.length; i++) {
    const op = options[i]
    const opMenuCode = op[menuCodeField]
    const titlePath = codeObj[opMenuCode]
    const isHidden = (typeof op.isHidden === 'function') && op.isHidden()
    if (titlePath && !isHidden) {
      op.editUrl = titlePath.path
      if (!op.title) op.title = titlePath.title
    } else { // 说明没有此权限，需要去除
      options.splice(i, 1)
      i--
    }
  }
}

export default router
