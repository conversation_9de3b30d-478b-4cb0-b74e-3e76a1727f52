/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
 **/

import Layout from '@/layout'

const approvalRouter = {
  path: '/approvalManage',
  component: Layout,
  name: 'ApprovalManage',
  meta: {
    title: 'approvalManage',
    icon: 'form'
  },
  children: [
    {
      path: 'ProcessSet',
      noComponent: true,
      name: 'ProcessSet',
      meta: {
        title: 'ProcessSet',
        icon: 'processSet'
      },
      children: [
        {
          code: 'G11',
          path: 'approvalProcess',
          component: () => import(/* webpackChunkName: 'approvalModule' */ '@/views/dataEncryption/encryption/approvalProcess'),
          name: 'approvalProcess',
          meta: { title: 'approvalProcess' }
        },
        {
          code: 'G12',
          path: 'delegateProcess',
          component: () => import(/* webpackChunkName: 'approvalModule' */ '@/views/dataEncryption/encryption/approvalProcess/delegateProcess'),
          name: 'DelegateProcess',
          meta: { title: 'delegateProcess' }
        }
      ]
    },
    {
      path: 'emailSet',
      noComponent: true,
      name: 'EmailSet',
      meta: {
        title: 'emailSet',
        icon: 'processSet'
      },
      children: [
        {
          code: 'G13',
          path: 'emailOutSend',
          component: () => import('@/views/dataEncryption/encryption/emailOutSend'),
          name: 'EmailOutSend',
          meta: { title: 'emailBaseStg' }
        },
        {
          code: 'G14',
          path: 'approveOutgoingEmailSetting',
          component: () => import(/* webpackChunkName: 'approvalModule' */ '@/views/system/configManage/approveOutgoingEmailSetting'),
          name: 'ApproveOutgoingEmailSetting',
          meta: { title: 'emailInfoStg' }
        }
      ]
    },
    {
      path: 'ApprovalConfig',
      noComponent: true,
      name: 'ApprovalConfig',
      meta: {
        title: 'approvalConfig',
        icon: 'processSet'
      },
      children: [
        {
          code: 'A63',
          path: 'approvalConfig',
          component: () => import(/* webpackChunkName: 'approvalModule' */ '@/views/system/configManage/approvalConfig'),
          name: 'ApprovalConfig',
          meta: { title: 'approvalConfig' }
        }
      ]
    }
    // ,
    // {
    //   path: 'path1',
    //   noComponent: true,
    //   name: 'path1',
    //   meta: {
    //     title: 'USB设备接入审批',
    //     icon: 'behaviorGroup'
    //   },
    //   alwaysShow: true,
    //   children: [
    //     {
    //       path: 'path1-1',
    //       component: () => import(/* webpackChunkName: 'approvalModule' */ '@/views/nested/menu2/index'),
    //       name: 'path1-1',
    //       meta: { title: 'USB设备接入审批' }
    //     }
    //   ]
    // },
    // {
    //   path: 'path2',
    //   noComponent: true,
    //   name: 'path2',
    //   meta: {
    //     title: '解密审批',
    //     icon: 'behaviorGroup'
    //   },
    //   alwaysShow: true,
    //   children: [
    //     {
    //       path: 'path2-1',
    //       component: () => import(/* webpackChunkName: 'approvalModule' */ '@/views/nested/menu2/index'),
    //       name: 'path2-1',
    //       meta: { title: '解密审批' }
    //     }
    //   ]
    // },
    // {
    //   path: 'path3',
    //   noComponent: true,
    //   name: 'path3',
    //   meta: {
    //     title: '外发审批',
    //     icon: 'behaviorGroup'
    //   },
    //   alwaysShow: true,
    //   children: [
    //     {
    //       path: 'path3-1',
    //       component: () => import(/* webpackChunkName: 'approvalModule' */ '@/views/nested/menu2/index'),
    //       name: 'path3-1',
    //       meta: { title: '外发审批' }
    //     }
    //   ]
    // },
    // {
    //   path: 'path4',
    //   noComponent: true,
    //   name: 'path4',
    //   meta: {
    //     title: '临时离线审批',
    //     icon: 'behaviorGroup'
    //   },
    //   alwaysShow: true,
    //   children: [
    //     {
    //       path: 'path4-1',
    //       component: () => import(/* webpackChunkName: 'approvalModule' */ '@/views/nested/menu2/index'),
    //       name: 'path4-1',
    //       meta: { title: '临时离线审批' }
    //     }
    //   ]
    // },
    // {
    //   path: 'path5',
    //   noComponent: true,
    //   name: 'path5',
    //   meta: {
    //     title: '审批历史记录',
    //     icon: 'behaviorGroup'
    //   },
    //   alwaysShow: true,
    //   children: [
    //     {
    //       path: 'path5-1',
    //       component: () => import(/* webpackChunkName: 'approvalModule' */ '@/views/nested/menu2/index'),
    //       name: 'path5-1',
    //       meta: { title: '审批历史记录' }
    //     }
    //   ]
    // }
  ]
}

export default approvalRouter
