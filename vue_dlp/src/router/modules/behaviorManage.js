/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
 *
 *    isLog: 是否日志模块：在销售模块中包含“日志审计”时，去除isLog=true的菜单，如果销售模块不包含，则显示
 **/

import Layout from '@/layout'

const behaviorManageRouter = {
  path: '/behaviorManage',
  component: Layout,
  name: 'BehaviorManage',
  meta: {
    title: 'behaviorManage',
    icon: 'form'
  },
  children: [
    {
      path: 'BehaviorGroup',
      noComponent: true,
      name: 'behaviorGroup',
      meta: {
        title: 'BehaviorGroup',
        icon: 'behaviorGroup'
      },
      alwaysShow: true,
      children: [
        {
          code: 'C11',
          path: 'BehaviorGroup',
          component: () => import(/* webpackChunkName: 'stgGroup' */ '@/views/behaviorManage/stgGroup'),
          name: 'BehaviorGroup',
          meta: { title: 'BehaviorGroup' }
        }
      ]
    },
    {
      path: 'applicationLimit',
      noComponent: true,
      name: 'ApplicationLimit',
      meta: {
        title: 'applicationLimit',
        icon: 'application'
      },
      children: [
        {
          code: 'A64',
          path: 'ProcessCollectRule',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/application/processCollectRule'),
          name: 'ProcessCollectRule',
          meta: { title: 'ProcessCollectRule' },
          prepare: true
        },
        {
          code: 'C21',
          path: 'AppBlock',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/application/appBlock'),
          name: 'AppBlock',
          meta: { title: 'AppBlock', stgCode: 6 },
          prepare: true
        },
        {
          code: 'C22',
          path: 'WinTitle',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/application/winTitle'),
          name: 'WinTitle',
          meta: { title: 'WinTitle' },
          prepare: true
        },
        {
          code: 'C23',
          path: 'AppVersion',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/application/appVersion'),
          name: 'AppVersion',
          meta: { title: 'AppVersion' },
          prepare: true
        },
        {
          code: 'A6B',
          path: 'appLogConfig',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/application/appLogConfig'),
          name: 'AppLogConfig',
          meta: { title: 'appLogConfig', stgCode: 159 },
          prepare: true
        },
        {
          code: 'A6A',
          path: 'appReportSet',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/system/terminalManage/appReportSet'),
          name: 'AppReportSet',
          meta: { title: 'appReportSet' },
          prepare: true
        },
        {
          code: 'C27',
          path: 'appReportEx',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/appReportEx'),
          name: 'AppReportEx',
          meta: { title: 'appReportEx', isLog: true }
        },
        {
          code: 'C25',
          path: 'appLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/appLog'),
          name: 'AppLog',
          meta: { title: 'appLog', isLog: true }
        },
        {
          code: 'C26',
          path: 'winTitleLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/winTitleLog'),
          name: 'WinTitleLog',
          meta: { title: 'winTitleLog', isLog: true }
        }
      ]
    },
    {
      path: 'monitor',
      noComponent: true,
      name: 'Monitor',
      meta: {
        title: 'monitor',
        icon: 'screen'
      },
      children: [
        {
          code: 'C31',
          path: 'video',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/monitor/video'),
          name: 'Video',
          meta: { title: 'video' },
          prepare: true
        },
        {
          code: 'C32',
          path: 'videoRecorder',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorManage/monitor/videoRecorder'),
          name: 'VideoRecorder',
          meta: { title: 'videoRecorder', isLog: true }
        },
        {
          code: 'E39',
          path: 'ScreenWaterMark',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/screenWaterMark'),
          name: 'ScreenWaterMark',
          meta: { title: 'ScreenWaterMark' },
          prepare: true
        },
        {
          code: 'C33',
          path: 'track',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/monitor/track'),
          name: 'Track',
          meta: { title: 'track' }
        },
        {
          code: 'C34',
          path: 'processMonitor',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/processMonitor'),
          name: 'ProcessMonitor',
          meta: { title: 'processMonitor' },
          prepare: true
        },
        {
          code: 'C35',
          path: 'sensitiveOpLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorManage/hardware/sensitiveOpLog'),
          name: 'SensitiveOpLog',
          meta: { title: 'sensitiveOpLog', isLog: true }
        }
        // ,
        // {
        //   hidden: true,
        //   path: 'shots',
        //   component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/nested/menu2/index'),
        //   name: 'Shots',
        //   meta: { title: '屏幕截图' }
        // },
        // {
        //   hidden: true,
        //   path: 'capture',
        //   component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/nested/menu2/index'),
        //   name: 'Capture',
        //   meta: { title: '截屏操作管控' }
        // },

      ]
    },
    {
      path: 'documentMonitor',
      noComponent: true,
      name: 'DocumentMonitor',
      meta: {
        title: 'documentMonitor',
        icon: 'file'
      },
      children: [
        {
          code: 'C41',
          path: 'fileFilter',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/monitor/fileFilter'),
          name: 'FileFilter',
          meta: { title: 'fileFilter' },
          prepare: true
        },
        {
          code: 'C42',
          path: 'fileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/file'),
          name: 'FileLog',
          meta: { title: 'fileLog', isLog: true }
        }
        // {
        //   hidden: false,
        //   code: 'C44',
        //   path: 'fileTrackLog',
        //   component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorAuditing/terminal/fileTrack'),
        //   name: 'FileTrackLog',
        //   meta: { title: 'fileTrackLog' }
        // },
        // {
        //   code: 'C74',
        //   path: 'usbFileLog',
        //   component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorAuditing/terminal/usbFile'),
        //   name: 'UsbFileLog',
        //   meta: { title: 'USB文件操作记录' }
        // }
      ]
    },
    {
      path: 'PrintControl',
      noComponent: true,
      name: 'PrintControl',
      meta: {
        title: 'PrintControl',
        icon: 'print'
      },
      children: [
        {
          code: 'C51',
          path: 'PrintSet',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/printerSet'),
          name: 'PrintSet',
          meta: { title: 'PrintSet' },
          prepare: true
        },
        {
          code: 'C52',
          path: 'WaterMark',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/waterMark'),
          name: 'WaterMark',
          meta: { title: 'WaterMark' },
          prepare: true
        },
        {
          code: 'C54',
          path: 'printLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/printer'),
          name: 'PrintLog',
          meta: { title: 'printLog', isLog: true }
        }
      ]
    },
    {
      path: 'BluetoothManage',
      noComponent: true,
      name: 'BluetoothManage',
      meta: {
        title: 'BluetoothManage',
        icon: 'portableDevice'
      },
      children: [
        {
          code: 'C61',
          path: 'BlueTooth',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/blueTooth'),
          name: 'BlueTooth',
          meta: { title: 'BlueTooth' },
          prepare: true
        },
        {
          code: 'C63',
          path: 'btConnChange',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/btConnChange'),
          name: 'BtConnChange',
          meta: { title: 'btConnChange', isLog: true }
        },
        {
          code: 'C64',
          path: 'btFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/btFileLog'),
          name: 'BtFileLog',
          meta: { title: 'btFileLog', isLog: true }
        },
        {
          code: 'C65',
          path: 'AdbLimit',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/adbLimit'),
          name: 'AdbLimit',
          meta: { title: 'AdbLimit' },
          query: {
            stgTypeNumber: 218
          },
          prepare: true
        },
        {
          code: 'C66',
          path: 'AdbLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/adbLog'),
          name: 'AdbLog',
          meta: { title: 'AdbLog', isLog: true }
        },
        {
          code: 'C67',
          path: 'MtpConfig',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/mtp'),
          name: 'MtpConfig',
          meta: { title: 'MtpConfig' },
          prepare: true
        },
        {
          code: 'C68',
          path: 'MtpLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/mtpLog'),
          name: 'MtpLog',
          meta: { title: 'MtpLog', isLog: true }
        }
      ]
    },
    {
      path: 'StorageDevice',
      noComponent: true,
      name: 'StorageDevice',
      meta: {
        title: 'StorageDevice',
        icon: 'storage'
      },
      children: [
        {
          code: 'C71',
          path: 'Driver',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/driver'),
          name: 'Driver',
          meta: { title: 'Driver' },
          prepare: true
        },
        {
          code: 'C73',
          path: 'usbEvent',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/usbEvent'),
          name: 'UsbEvent',
          meta: { title: 'usbEvent', isLog: true }
        },
        {
          code: 'C74',
          path: 'usbFileConfig',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/usbFileConfig'),
          name: 'UsbFileConfig',
          meta: { title: 'usbFileConfig' },
          prepare: true
        },
        {
          code: 'C75',
          path: 'usbFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/usbFile'),
          name: 'UsbFileLog',
          meta: { title: 'usbFileLog', isLog: true }
        }
      ]
    },
    {
      path: 'BurnControl',
      noComponent: true,
      name: 'BurnControl',
      meta: {
        title: 'BurnControl',
        icon: 'burner'
      },
      children: [
        {
          code: 'C81',
          path: 'burnConfig',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/burner/burnConfig'),
          name: 'BurnConfig',
          meta: { title: 'burnConfig' },
          prepare: true
        },
        {
          code: 'C83',
          path: 'burnLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorManage/burner/burnLog'),
          name: 'BurnLog',
          meta: { title: 'burnLog', isLog: true }
        }
      ]
    },
    {
      path: 'ComputerManage',
      noComponent: true,
      name: 'ComputerManage',
      meta: {
        title: 'ComputerManage',
        icon: 'computerManage'
      },
      alwaysShow: true,
      children: [
        {
          code: 'CA1',
          path: 'SysBaseConfig',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/application/sysBaseConfig'),
          name: 'SysBaseConfig',
          meta: { title: 'SysBaseConfig' },
          prepare: true
        },
        {
          code: 'CA3',
          path: 'SysAlarmConfig',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/application/sysAlarmConfig'),
          name: 'SysAlarmConfig',
          meta: { title: 'SysAlarmConfig' },
          prepare: true
        },
        {
          code: 'CA2',
          path: 'groupPolicy',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/application/groupPolicy'),
          name: 'GroupPolicy',
          meta: { title: 'groupPolicy' },
          prepare: true
        },
        {
          code: 'C92',
          path: 'PersonalizePolicy',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/personalizePolicy'),
          name: 'PersonalizePolicy',
          meta: { title: 'PersonalizePolicy' },
          prepare: true
        },
        {
          code: 'B25',
          path: 'logFilter',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/monitor/logFilter'),
          name: 'LogFilter',
          meta: { title: 'logFilter' },
          prepare: true
        },
        {
          code: 'B2Q',
          path: 'energySaving',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/monitor/energySaving'),
          name: 'energySaving',
          meta: { title: 'energySaving' },
          prepare: true
        },
        {
          code: 'B26',
          path: 'systemLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/systemLog'),
          name: 'SystemLog',
          meta: { title: 'systemLog', isLog: true }
        },
        {
          code: 'B27',
          path: 'systemStateLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/systemStateLog'),
          name: 'SystemStateLog',
          meta: { title: 'systemStateLog', isLog: true }
        },
        {
          code: 'B28',
          path: 'loginLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/loginLog'),
          name: 'LoginLog',
          meta: { title: 'loginLog', isLog: true }
        },
        {
          code: 'B2G',
          path: 'computerAppLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/computerAppLog'),
          name: 'ComputerAppLog',
          meta: { title: 'computerAppLog', isLog: true }
        },
        {
          code: 'B29',
          path: 'userChangeLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/userChangeLog'),
          name: 'UserChangeLog',
          meta: { title: 'userChangeLog', isLog: true }
        },
        {
          code: 'B2A',
          path: 'groupChangeLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/groupChangeLog'),
          name: 'GroupChangeLog',
          meta: { title: 'groupChangeLog', isLog: true }
        },
        {
          code: 'B2R',
          path: 'energySavingLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/energySavingLog'),
          name: 'energySavingLog',
          meta: { title: 'energySavingLog', isLog: true }
        },
        {
          code: 'C91',
          path: 'OtherDeviceLimit',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/otherDeviceLimit'),
          name: 'OtherDeviceLimit',
          meta: { title: 'OtherDeviceLimit' },
          prepare: true
        },
        {
          code: 'C93',
          path: 'usbInterfaceLimit',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/usbInterfaceLimit'),
          name: 'UsbInterfaceLimit',
          meta: { title: 'usbInterfaceLimit' },
          prepare: true
        },
        {
          code: 'C94',
          path: 'netInterfaceLimit',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/netInterfaceLimit'),
          name: 'NetInterfaceLimit',
          meta: { title: 'netInterfaceLimit' },
          prepare: true
        },
        {
          code: 'C95',
          path: 'netInterfaceLimitLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/netInterfaceLimitLog'),
          name: 'netInterfaceLimitLog',
          meta: { title: 'netInterfaceLimitLog', isLog: true }
        }
      ]
    },
    {
      path: 'FileOutgoingCheckStatisticsConfig',
      noComponent: true,
      name: 'FileOutgoingCheckStatisticsConfig',
      meta: {
        title: 'fileOutgoingCheckStatisticsConfig',
        icon: 'fileTransfer'
      },
      children: [
        {
          code: 'A59',
          path: 'fileOutgoingCheckStatisticsSet',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/fileOutgoingCheckStatisticsConfig'),
          name: 'FileOutgoingCheckStatisticsSet',
          meta: { title: 'fileOutgoingCheckStatisticsSet' }
        }
      ]
    }
  ]
}

export default behaviorManageRouter
