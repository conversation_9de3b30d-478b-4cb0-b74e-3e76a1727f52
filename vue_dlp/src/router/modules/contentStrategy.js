/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
 *
 *    isLog: 是否日志模块：在销售模块中包含“日志审计”时，去除isLog=true的菜单，如果销售模块不包含，则显示
 **/

import Layout from '@/layout'

const contentStrategyRouter = {
  path: '/contentStrategy',
  component: Layout,
  redirect: '/contentStrategy/strategy',
  name: 'ContentStrategy',
  meta: {
    title: 'contentStrategy',
    icon: 'form'
  },
  children: [
    {
      hidden: true,
      path: 'contentStgGroup',
      noComponent: true,
      name: 'contentStgGroup',
      meta: {
        title: 'contentStgGroup',
        icon: 'contentStgGroup'
      },
      alwaysShow: true,
      children: [
        {
          disabled: true,
          code: 'F11',
          path: 'ContentStrategyGroup',
          component: () => import(/* webpackChunkName: 'stgGroup' */ '@/views/contentStrategy/stgGroup'),
          name: 'ContentStrategyGroup',
          meta: { title: 'ContentStrategyGroup' }
        }
      ]
    },
    {
      path: 'EffectiveContent',
      noComponent: true,
      name: 'EffectiveContent',
      meta: {
        title: 'EffectiveContent',
        icon: 'testConfig'
      },
      alwaysShow: true,
      children: [
        {
          code: 'F21',
          path: 'effectiveContentStrategy',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/strategy/effectiveContentStrategy'),
          name: 'EffectiveContentStrategy',
          meta: { title: 'effectiveContentStrategy' },
          prepare: true
        },
        {
          code: 'F24',
          path: 'effectiveContentConfig',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/strategy/effectiveContentConfig'),
          name: 'EffectiveContentConfig',
          meta: { title: 'effectiveContentConfig' },
          prepare: true
        },
        {
          code: 'F23',
          path: 'sensitiveBackupConfig',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/strategy/sensitiveBackupConfig'),
          name: 'SensitiveBackupConfig',
          meta: { title: 'sensitiveBackupConfig' },
          prepare: true
        }
      ]
    },
    {
      path: 'contentStg',
      noComponent: true,
      name: 'contentStg',
      meta: {
        title: 'contentStg',
        icon: 'contentStgGroup'
      },
      alwaysShow: true,
      children: [
        {
          code: 'F12',
          path: 'Content',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/strategy/content'),
          name: 'Content',
          meta: { title: 'Content' },
          prepare: true
        },
        {
          code: 'F13',
          path: 'dripContent',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/strategy/content/drip'),
          name: 'DripContentStg',
          meta: { title: 'dripContent' },
          prepare: true
        },
        {
          code: 'F14',
          path: 'diskScanSelfCheck',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/strategy/diskScanSelfCheck'),
          name: 'DiskScanSelfCheck',
          meta: { title: 'diskScanSelfCheck', stgCode: 223 },
          prepare: true
        },
        {
          hidden: true,
          code: 'F15',
          path: 'diskScanSelfCheckSensitive',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/strategy/diskScanSelfCheck/contentStg'),
          name: 'DiskScanSelfCheckSensitive',
          meta: { title: 'diskScanSelfCheckSensitive' },
          query: {
            stgTypeNumber: 224,
            treeable: false
          }
        },
        {
          code: 'F17',
          path: 'diskScan',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/strategy/diskScanSensCheck'),
          name: 'DiskScanSensCheck',
          meta: { title: 'diskScanSensCheck', stgCode: 95 }
        },
        {
          hidden: true,
          code: 'E28',
          path: 'diskScanSens',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/dataEncryption/encryption/diskScan/contentStg'),
          name: 'DiskScanSens',
          meta: { title: 'diskScanSens', parentName: 'contentStg' },
          query: {
            stgTypeNumber: 125,
            treeable: false
          }
        }
      ]
    },
    {
      path: 'RuleGroup',
      noComponent: true,
      name: 'RuleGroup',
      meta: {
        title: 'RuleGroup',
        icon: 'ruleGroup'
      },
      children: [
        {
          code: 'F31',
          path: 'group',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/rule/group'),
          name: 'Group',
          meta: { title: 'group' }
        },
        {
          code: 'F32',
          path: 'respond',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/rule/respond'),
          name: 'RespondRule',
          meta: { title: 'respond' }
        }
      ]
    },
    {
      path: 'rule',
      noComponent: true,
      name: 'Rule',
      meta: {
        title: 'rule',
        icon: 'rule'
      },
      children: [
        {
          code: 'F41',
          path: 'keyword',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/rule/keyword'),
          name: 'KeywordRule',
          meta: { title: 'keyword' }
        },
        {
          code: 'F43',
          path: 'file',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/rule/fileAttribute'),
          name: 'FileRule',
          meta: { title: 'file' }
        },
        {
          code: 'F42',
          path: 'regular',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/rule/regular'),
          name: 'RegularRule',
          meta: { title: 'regular' }
        },
        {
          code: 'F44',
          path: 'sourceCode',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/rule/sourceCode'),
          name: 'SourceCode',
          meta: { title: 'sourceCode' }
        }
      ]
    },
    {
      path: 'AdvancedRules',
      noComponent: true,
      name: 'AdvancedRules',
      meta: {
        title: 'AdvancedRules',
        icon: 'algorithm'
      },
      children: [
        {
          code: 'F52',
          path: 'edm',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/rule/edm'),
          name: 'EdmRule',
          meta: { title: 'edm' }
        },
        {
          code: 'F51',
          path: 'fileFp',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/rule/fileFp'),
          name: 'FileFpRule',
          meta: { title: 'fileFp' }
        },
        {
          code: 'F53',
          path: 'vml',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/contentStrategy/rule/vml'),
          name: 'VmlRule',
          meta: { title: 'vml' }
        }
      ]
    },
    {
      path: 'ContentAudit',
      noComponent: true,
      name: 'ContentAudit',
      meta: {
        title: 'ContentAudit',
        icon: 'contentStgGroup'
      },
      children: [
        {
          code: 'H11',
          path: 'issueLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/issue/issueLog'),
          name: 'IssueLog',
          meta: { title: 'issueLog', isLog: true }
        },
        {
          code: 'H12',
          path: 'dripIssueLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/issue/dripLog'),
          name: 'DripIssueLog',
          meta: { title: 'dripIssueLog', isLog: true }
        },
        {
          code: 'H13',
          path: 'sensitiveFileOutLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/sensitiveFileOutLog'),
          name: 'SensitiveFileOutLog',
          meta: { title: 'sensitiveFileOutLog', isLog: true }
        },
        {
          code: 'E52',
          path: 'diskScanLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/diskScanLog'),
          name: 'DiskScanLog',
          meta: { title: 'diskScanLog', parentName: 'contentStg', isLog: true }
        }
      ]
    }
  ]
}

export default contentStrategyRouter
