/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
 *
 *    isLog: 是否日志模块：在销售模块中包含“日志审计”时，去除isLog=true的菜单，如果销售模块不包含，则显示
 **/

import Layout from '@/layout'

const netManageRouter = {
  path: '/netManage',
  component: Layout,
  name: 'NetManage',
  meta: {
    title: 'netManage',
    icon: 'form'
  },
  children: [
    {
      path: 'NetGroup',
      noComponent: true,
      name: 'NetGroup',
      meta: {
        title: 'NetGroup',
        icon: 'behaviorGroup'
      },
      alwaysShow: true,
      children: [
        {
          code: 'D11',
          path: 'NetGroup',
          component: () => import(/* webpackChunkName: 'stgGroup' */ '@/views/behaviorManage/stgGroup/net'),
          name: 'NetGroup',
          meta: { title: 'NetGroup' }
        }
      ]
    },
    {
      path: 'NetBrowse',
      noComponent: true,
      name: 'NetBrowse',
      meta: {
        title: 'NetBrowse',
        icon: 'ie'
      },
      children: [
        {
          code: 'D21',
          path: 'Url',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/url'),
          name: 'Url',
          meta: { title: 'Url' },
          prepare: true
        },
        {
          code: 'D22',
          path: 'WebPost',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/webPost'),
          name: 'WebPost',
          meta: { title: 'WebPost' },
          prepare: true
        },
        {
          code: 'D27',
          path: 'urlLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/url'),
          name: 'UrlLog',
          meta: { title: 'urlLog', isLog: true }
        },
        {
          code: 'D28',
          path: 'urlsSearch',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/urlsSearch'),
          name: 'UrlsSearch',
          meta: { title: 'urlsSearch', isLog: true }
        },
        {
          code: 'D29',
          path: 'browserUpload',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/browserUpload'),
          name: 'BrowserUpload',
          meta: { title: 'browserUpload', isLog: true }
        },
        {
          code: 'D2J',
          path: 'browserDownload',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/browserDownload'),
          name: 'BrowserDownload',
          meta: { title: 'browserDownload', isLog: true }
        },
        {
          code: 'D2A',
          path: 'forumdataLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/forumdata'),
          name: 'ForumdataLog',
          meta: { title: 'forumdataLog', isLog: true }
        },
        {
          code: 'D2B',
          path: 'BrowserFileStrategy',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorAuditing/network/browserFileStrategy'),
          name: 'BrowserFileStrategy',
          meta: { title: 'BrowserFileStrategy' },
          prepare: true
        },
        {
          code: 'D2I',
          path: 'BrowserFileDownloadStrategy',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorAuditing/network/browserFileDownloadStrategy'),
          name: 'BrowserFileDownloadStrategy',
          meta: { title: 'BrowserFileDownloadStrategy' },
          prepare: true
        },
        {
          code: 'D2C',
          path: 'webBrowseTime',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/webBrowseTime'),
          name: 'WebBrowseTime',
          meta: { title: 'webBrowseTime', isLog: true }
        },
        {
          code: 'D2D',
          path: 'browserPasteLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/browserPasteLog'),
          name: 'BrowserPasteLog',
          meta: { title: 'browserPasteLog', isLog: true }
        },
        {
          code: 'D2E',
          path: 'forumUrlFilter',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/forumUrlFilter'),
          name: 'ForumUrlFilter',
          // meta: { title: 'forumPostsURLFiltering' },
          meta: { title: 'forumPostingMonitoring' },
          prepare: true
        },
        {
          code: 'D2F',
          path: 'webBrowseURLFilter',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/webBrowseURLFilter'),
          name: 'WebBrowseURLFilter',
          meta: { title: 'webBrowsingUrlFiltering' },
          prepare: true
        },
        {
          code: 'D2G',
          path: 'webpagePasteAudit',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/webpagePasteAudit'),
          name: 'WebpagePasteAudit',
          meta: { title: 'webpagePasteAudit' },
          prepare: true
        },
        {
          code: 'D2H',
          path: 'webpageBrowseAudit',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/webpageBrowseAudit'),
          name: 'WebpageBrowseAudit',
          meta: { title: 'webpageBrowseAudit' },
          prepare: true
        }
      ]
    },
    {
      path: 'InstantMessaging',
      noComponent: true,
      name: 'InstantMessaging',
      meta: {
        title: 'InstantMessaging',
        icon: 'im'
      },
      children: [
        {
          code: 'D31',
          path: 'ImTool',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/application/imTool'),
          name: 'ImTool',
          meta: { title: 'ImTool', stgCode: 4 },
          prepare: true
        },
        {
          code: 'D32',
          path: 'ImFile',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/application/imFile'),
          name: 'ImFile',
          meta: { title: 'ImFile' },
          prepare: true
        },
        {
          code: 'D47',
          path: 'ImToolAutiFake',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/application/imToolAutiFake'),
          name: 'ImToolAutiFake',
          meta: { title: 'ImToolAutiFake' }
        },
        {
          code: 'D35',
          path: 'chatLoginLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatLoginLog'),
          name: 'ChatLoginLog',
          meta: { title: 'chatLoginLog', isLog: true }
        },
        {
          hidden: true,
          code: 'D39',
          path: 'chatTextLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatTextLog'),
          name: 'ChatTextLog',
          meta: { title: 'chatTextLog', isLog: true }
        },
        {
          hidden: true,
          code: 'D39',
          path: 'chatImageLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatImageLog'),
          name: 'ChatImageLog',
          meta: { title: 'chatImageLog', isLog: true }
        },
        {
          hidden: true,
          code: 'D39',
          path: 'chatFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatFileLog'),
          name: 'ChatFileLog',
          meta: { title: 'chatFileLog', isLog: true }
        },
        {
          code: 'D39',
          path: 'chatAllLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatAllLog'),
          name: 'ChatAllLog',
          meta: { title: 'chatAllLog', isLog: true }
        }
      ]
    },
    {
      path: 'NetworkControl',
      noComponent: true,
      name: 'NetworkLimit',
      meta: {
        title: 'NetworkControl',
        icon: 'networkBehavior'
      },
      children: [
        {
          code: 'D41',
          path: 'WebPort',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/webPort'),
          name: 'WebPort',
          meta: { title: 'WebPort' },
          prepare: true
        },
        {
          hidden: true,
          code: 'D42',
          path: 'Isolation',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/isolation'),
          name: 'Isolation',
          meta: { title: 'Isolation' },
          prepare: true
        },
        {
          code: 'D43',
          path: 'Lansegment',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/lansegment'),
          name: 'Lansegment',
          meta: { title: 'Lansegment' }
        },
        {
          code: 'D46',
          path: 'WifiCollect',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/wifiCollect'),
          name: 'WifiCollect',
          meta: { title: 'WifiCollect' },
          prepare: true
        },
        {
          code: 'D44',
          path: 'WifiBlock',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/wifiBlock'),
          name: 'WifiBlock',
          meta: { title: 'WifiBlock' },
          prepare: true
        },
        {
          code: 'D45',
          path: 'WIFILog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/wifiLog'),
          name: 'wifiLog',
          meta: { title: 'WIFILog', isLog: true }
        },
        {
          code: 'H55',
          path: 'telnetCommControl',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/telnetCommControl'),
          name: 'TelnetCommControl',
          meta: { title: 'telnetCommControl' },
          prepare: true
        },
        {
          code: 'H56',
          path: 'telnetMonitorLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/telnetMonitorLog'),
          name: 'TelnetMonitorLog',
          meta: { title: 'telnetMonitorLog', isLog: true }
        },
        {
          code: 'H57',
          path: 'remoteDesktopControl',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/system/terminalManage/remoteDesktopControl'),
          name: 'RemoteDesktopControl',
          meta: { title: 'remoteDesktopControl' },
          prepare: true
        },
        {
          code: 'H58',
          path: 'remoteDesktopControlLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/remoteDesktopLog'),
          name: 'RemoteDesktopControlLog',
          meta: { title: 'remoteDesktopControlLog', isLog: true }
        },
        {
          code: 'H59',
          path: 'remoteToolControl',
          component: () => import('@/views/system/terminalManage/remoteToolControl'),
          name: 'RemoteToolControl',
          meta: { title: 'remoteToolControl' },
          prepare: true
        }
      ]
    },
    {
      path: 'TrafficControl',
      noComponent: true,
      name: 'TrafficControl',
      meta: {
        title: 'TrafficControl',
        icon: 'traffic'
      },
      children: [
        {
          code: 'D51',
          path: 'WebFlow',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/webFlow'),
          name: 'WebFlow',
          meta: { title: 'WebFlow' },
          prepare: true
        },
        {
          code: 'D52',
          path: 'netFlowLogRT',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/netFlowRT'),
          name: 'NetFlowLogRT',
          meta: { title: 'netFlowLogRT', isLog: true }
        },
        {
          code: 'D53',
          path: 'netFlowLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/netFlow'),
          name: 'NetFlowLog',
          meta: { title: 'netFlowLog', isLog: true }
        }
      ]
    },
    {
      path: 'EmailControl',
      noComponent: true,
      name: 'EmailControl',
      meta: {
        title: 'EmailControl',
        icon: 'email'
      },
      children: [
        {
          code: 'D61',
          path: 'EmailKeyword',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/emailKeyword'),
          name: 'EmailKeyword',
          meta: { title: 'EmailKeyword' },
          prepare: true
        },
        {
          code: 'D66',
          path: 'EmailAttachFile',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/emailAttachFile'),
          name: 'EmailAttachFile',
          meta: { title: 'EmailAttachFile' },
          prepare: true
        },
        {
          code: 'D65',
          path: 'EmailCopy',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/dataEncryption/encryption/mailWhiteList/mailCopy'),
          name: 'EmailCopy',
          meta: { title: 'EmailCopy' },
          prepare: true
        },
        {
          code: 'D64',
          path: 'emailLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/email'),
          name: 'EmailLog',
          meta: { title: 'emailLog', isLog: true }
        },
        {
          code: 'D68',
          path: 'emailAutiFake',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/emailAutiFake'),
          name: 'EmailAutiFake',
          meta: { title: 'emailAutiFake' }
        }
      ]
    },
    {
      path: 'SharingControl',
      noComponent: true,
      name: 'SharingControl',
      meta: {
        title: 'SharingControl',
        icon: 'share'
      },
      children: [
        {
          code: 'D71',
          path: 'ShareConfig',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/shareConfig'),
          name: 'ShareConfig',
          meta: { title: 'ShareConfig' },
          prepare: true
        },
        {
          code: 'A5K',
          path: 'localShareExtBase',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/localShareExtBase'),
          name: 'LocalShareExtBase',
          meta: { title: 'localShareExtBase' }
        }
      ]
    },
    {
      path: 'NetDisk',
      noComponent: true,
      name: 'NetDisk',
      meta: {
        title: 'NetDisk',
        icon: 'netDisk'
      },
      children: [
        {
          code: 'D81',
          path: 'NetDiskStrategy',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/netDisk'),
          name: 'NetDiskStrategy',
          meta: { title: 'NetDiskStrategy' },
          prepare: true
        },
        {
          code: 'D82',
          path: 'NetDiskLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/netDiskLog'),
          name: 'NetDiskLog',
          meta: { title: 'NetDiskLog', isLog: true }
        }
      ]
    },
    {
      path: 'FtpControl',
      noComponent: true,
      name: 'FtpControl',
      meta: {
        title: 'FtpControl',
        icon: 'ftp'
      },
      children: [
        {
          code: 'D83',
          path: 'ftpControlConfig',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/ftpControl'),
          name: 'FtpControlConfig',
          meta: { title: 'FtpControlConfig' },
          prepare: true
        },
        {
          code: 'D84',
          path: 'ftpLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/ftp'),
          name: 'FtpLog',
          meta: { title: 'FtpControlConfigLog', isLog: true }
        }
      ]
    },
    {
      path: 'AiControl',
      noComponent: true,
      name: 'AiControl',
      meta: {
        title: 'AiControl',
        icon: 'ie'
      },
      children: [
        {
          code: 'D89',
          path: 'AiModelStrategy',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/aiModelStrategy'),
          name: 'AiModelStrategy',
          meta: { title: 'AiModelStrategy' },
          prepare: true
        },
        {
          code: 'D85',
          path: 'AiChatStrategy',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/aiChatStrategy'),
          name: 'AiChatStrategy',
          meta: { title: 'AiChatStrategy' },
          prepare: true
        },
        {
          code: 'D86',
          path: 'AiUploadFile',
          component: () => import(/* webpackChunkName: 'netManageModule' */ '@/views/behaviorManage/network/aiUploadFile'),
          name: 'AiUploadFile',
          meta: { title: 'AiFileStrategy' },
          prepare: true
        }
      ]
    }
  ]
}

export default netManageRouter
