/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
 *
 *    name: 由于日志相关路由可能在其他路由中使用，为避免路由名称冲突导致出现问题，故当前页面最后一级路由name后添加大写P以作区分。
 *          路由名称与组件名称不同导致无法保存状态的情况已在 layout/components/AppMain.vue 中做处理 ( cachedViewNames )
 **/
import Layout from '@/layout'

const behaviorAuditingRouter = {
  path: '/behaviorAuditing',
  component: Layout,
  name: 'BehaviorAuditing',
  meta: {
    title: 'behaviorAuditing',
    icon: 'form'
  },
  children: [
    // 审计日志聚合搜索 25D1暂时不上
    {
      path: 'Auditing',
      noComponent: true,
      name: 'Auditing',
      meta: {
        title: 'auditingQuery',
        icon: 'issueLog'
      },
      alwaysShow: true,
      children: [
        {
          code: 'H94',
          path: 'auditLogUnifiedQuery2',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/auditLogUnifiedQuery/logQuery2'),
          name: 'logQuery2',
          meta: { title: 'auditLogUnifiedQuery' }
        },
        {
          hidden: true,
          code: 'H96',
          path: 'batchExportLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/batchExport'),
          name: 'BatchExportLog',
          meta: { title: 'batchExportLog' }
        },
        {
          // 此配置项是为了开发模式下能够监听到 批量导出审计日志模板文件 的变化，重新生成 批量导出审计日志 菜单界面
          hidden: true,
          code: 'H96',
          path: 'batchExportLogTpl',
          component: () => import('@/views/behaviorAuditing/batchExport/index-tpl'),
          name: 'BatchExportLogTpl',
          meta: { title: 'batchExportLog' }
        }
      ]
    },
    {
      path: 'alarmDetailLog',
      noComponent: true,
      name: 'alarmDetailLog',
      meta: {
        title: 'alarmDetailLog',
        icon: 'issueLog'
      },
      alwaysShow: true,
      children: [
        {
          code: 'H93',
          path: 'SignReportPushLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/signReportPushLog'),
          name: 'SignReportPushLog',
          meta: { title: 'SignReportPushLog' }
        },
        {
          code: 'H92',
          path: 'SysAlarmLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/sysAlarmLog'),
          name: 'SysAlarmLog',
          meta: { title: 'SysAlarmLog' }
        },
        {
          code: 'H91',
          path: 'AlarmDetailLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/alarmDetailLog'),
          name: 'AlarmDetailLog',
          meta: { title: 'AlarmDetailLog' }
        },
        {
          code: 'F22',
          path: 'sensitiveAlarmLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/sensitiveAlarmLog'),
          name: 'SensitiveAlarmLog',
          meta: { title: 'sensitiveAlarmLog' }
        }
        // { 隐藏服务告警记录，服务告警记录整合至系统告警记录
        //   code: 'H14',
        //   path: 'serviceAlarmLog',
        //   component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/serviceAlarmLog'),
        //   name: 'ServiceAlarmLog',
        //   meta: { title: 'serviceAlarmLog' }
        // }
      ]
    },
    {
      path: 'systemLog',
      noComponent: true,
      alwaysShow: true,
      name: 'SystemLog',
      meta: {
        title: 'SystemLog',
        icon: 'terminalManage'
      },
      children: [
        {
          code: 'B22',
          path: 'accessLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/accessLog'),
          name: 'AccessLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'accessLog' }
        },
        {
          code: 'B2U',
          path: 'terminalUpgradeLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/terminalUpgradeLog'),
          name: 'TerminalUpgradeLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'terminalUpgradeLog' }
        },
        {
          code: 'B2T',
          path: 'mobileTerminalUpgradeLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/mobileTerminalUpgradeLog'),
          name: 'MobileTerminalUpgradeLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'mobileTerminalUpgradeLog' }
        },
        {
          code: 'B2O',
          path: 'terminalOnlineLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/terminalOnlineLog'),
          name: 'TerminalOnlineLogP',
          meta: { title: 'terminalOnlineLog' }
        },
        {
          code: 'B2I',
          path: 'lockScreenLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/lockScreenLog'),
          name: 'LockScreenLogP',
          meta: { title: 'lockScreenLog' }
        },
        {
          code: 'B44',
          path: 'HardwareChangeAlarm',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/assetsConfig/hardwareChangeAlarm'),
          name: 'HardwareChangeAlarmP',
          meta: { title: 'HardwareChangeAlarm' }
        },
        {
          code: 'B43',
          path: 'HardwareAssetLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/assetsConfig/hardwareAssetLog'),
          name: 'HardwareAssetLogP',
          meta: { title: 'HardwareAssetLog' }
        },
        {
          code: 'B47',
          path: 'softwareChangeAlarm',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/assetsConfig/softwareChangeAlarm'),
          name: 'SoftwareChangeAlarmP',
          meta: { title: 'softwareChangeAlarm' }
        },
        {
          code: 'B46',
          path: 'softwareAssetLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/assetsConfig/softwareAssetLog'),
          name: 'SoftwareAssetLogP',
          meta: { title: 'softwareAssetLog' }
        },
        {
          code: 'B36',
          path: 'CurTaskLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/systemMaintenance/CurTaskLog'),
          name: 'CurTaskLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'CurTaskLog' }
        },
        {
          code: 'B5B',
          path: 'softwareUnauthorAuditLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/softwareUnauthorAuditLog'),
          name: 'SoftwareUnauthorAuditLog', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'softwareUnauthorAuditLog' }
        },
        {
          code: 'C28',
          path: 'softLimitLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/softLimitLog'),
          name: 'SoftLimitLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'softLimitLog' }
        },
        {
          code: 'B63',
          path: 'softwareAuditLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/softwareManager/softwareAuditLog'),
          name: 'SoftwareAuditLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'softwareAuditLog' } // 软件管家审计记录
        },
        {
          code: 'B68',
          path: 'ApprovalSoftwareSubmitLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/approvalSoftwareSubmitLog'),
          name: 'ApprovalSoftwareSubmitLog', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'approvalSoftwareSubmitLog' } // 软件入库审批记录
        },
        {
          code: 'B66',
          path: 'termSecurityLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/softwareManager/termSecurity/log'),
          name: 'TermSecurityLogP',
          meta: { title: 'termSecurityLog' }
        },
        {
          code: 'B75',
          path: 'taskDistributeLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/taskDistributeLog'),
          name: 'TaskDistributeLogP',
          meta: { title: 'taskDistributeLog' }
        },
        {
          code: 'B76',
          path: 'patchInstallLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/patchManage/patchInstallLog'),
          name: 'PatchInstallLogP',
          meta: { title: 'patchInstallLog' }
        }
      ]
    },
    {
      path: 'HardwareLog',
      noComponent: true,
      name: 'HardwareLog',
      meta: {
        title: 'HardwareLog',
        icon: 'hardware'
      },
      children: [
        {
          code: 'C54',
          path: 'printLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/printer'),
          name: 'PrintLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'printLog' }
        },
        {
          code: 'C63',
          path: 'btConnChange',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/btConnChange'),
          name: 'BtConnChangeP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'btConnChange' }
        },
        {
          code: 'C64',
          path: 'btFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/btFileLog'),
          name: 'BtFileLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'btFileLog' }
        },
        {
          code: 'C66',
          path: 'AdbLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/adbLog'),
          name: 'AdbLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'AdbLog' }
        },
        {
          code: 'C68',
          path: 'MtpLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/mtpLog'),
          name: 'MtpLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'MtpLog' }
        },
        {
          code: 'A5F',
          path: 'usbApprovalAccessLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/usbApprovalAccessLog'),
          name: 'UsbApprovalAccessLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'usbApprovalAccessLog' }
        },
        {
          code: 'C73',
          path: 'usbEvent',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/usbEvent'),
          name: 'UsbEventP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'usbEvent' }
        },
        {
          code: 'C75',
          path: 'usbFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/usbFile'),
          name: 'UsbFileLogP',
          meta: { title: 'usbFileLog' }
        },
        {
          code: 'C83',
          path: 'burnLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorManage/burner/burnLog'),
          name: 'BurnLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'burnLog' }
        }
      ]
    },
    {
      path: 'terminalSystem',
      noComponent: true,
      name: 'terminalSystem',
      meta: {
        title: 'terminalSystem',
        icon: 'terminalBehavior'
      },
      children: [
        {
          code: 'C26',
          path: 'winTitleLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/winTitleLog'),
          name: 'WinTitleLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'winTitleLog' }
        },
        {
          code: 'C25',
          path: 'appLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/appLog'),
          name: 'AppLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'appLog' }
        },
        {
          code: 'C35',
          path: 'sensitiveOpLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorManage/hardware/sensitiveOpLog'),
          name: 'SensitiveOpLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'sensitiveOpLog' }
        },
        {
          code: 'C27',
          path: 'appReportEx',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/appReportEx'),
          name: 'AppReportExP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'appReportEx' }
        },
        {
          code: 'C32',
          path: 'videoRecorder',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorManage/monitor/videoRecorder'),
          name: 'VideoRecorderP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'videoRecorder' }
        },
        {
          code: 'C42',
          path: 'fileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/file'),
          name: 'FileLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'fileLog' }
        },
        {
          code: 'B26',
          path: 'systemLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/systemLog'),
          name: 'SystemLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'systemLog' }
        },
        {
          code: 'B27',
          path: 'systemStateLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/systemStateLog'),
          name: 'SystemStateLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'systemStateLog' }
        },
        {
          code: 'B28',
          path: 'loginLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/loginLog'),
          name: 'LoginLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'loginLog' }
        },
        {
          code: 'B2G',
          path: 'computerAppLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/computerAppLog'),
          name: 'ComputerAppLogP',
          meta: { title: 'computerAppLog' }
        },
        {
          code: 'B29',
          path: 'userChangeLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/userChangeLog'),
          name: 'UserChangeLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'userChangeLog' }
        },
        {
          code: 'B2A',
          path: 'groupChangeLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/groupChangeLog'),
          name: 'GroupChangeLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'groupChangeLog' }
        },
        {
          code: 'B2R',
          path: 'energySavingLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/systemLog/energySavingLog'),
          name: 'EnergySavingLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'energySavingLog' }
        },
        {
          code: 'C43',
          path: 'appOpenFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/appOpenFileLog'),
          name: 'AppOpenFileLog', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'appOpenFileLog' }
        },
        {
          code: 'C95',
          path: 'netInterfaceLimitLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/netInterfaceLimitLog'),
          name: 'NetInterfaceLimitLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'netInterfaceLimitLog' }
        }
      ]
    },
    {
      path: 'ApplicationLog',
      noComponent: true,
      name: 'ApplicationLog',
      meta: {
        title: 'ApplicationLog',
        icon: 'application'
      },
      children: [
        {
          code: 'D27',
          path: 'urlLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/url'),
          name: 'UrlLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'urlLog' }
        },
        {
          code: 'D28',
          path: 'urlsSearch',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/urlsSearch'),
          name: 'UrlsSearchP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'urlsSearch' }
        },
        {
          code: 'D2D',
          path: 'browserPasteLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/browserPasteLog'),
          name: 'BrowserPasteLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'browserPasteLog' }
        },
        {
          code: 'D29',
          path: 'browserUpload',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/browserUpload'),
          name: 'BrowserUploadP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'browserUpload' }
        },
        {
          code: 'D2J',
          path: 'browserDownload',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/browserDownload'),
          name: 'BrowserDownloadP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'browserDownload' }
        },
        {
          code: 'D2C',
          path: 'webBrowseTime',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/webBrowseTime'),
          name: 'WebBrowseTimeP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'webBrowseTime' }
        },
        {
          code: 'D2A',
          path: 'forumdataLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/forumdata'),
          name: 'ForumdataLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'forumdataLog' }
        }
      ]
    },
    {
      path: 'networkAuditing',
      noComponent: true,
      name: 'NetworkAuditing',
      meta: {
        title: 'networkAuditing',
        icon: 'networkBehavior'
      },
      children: [
        {
          code: 'D35',
          path: 'chatLoginLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatLoginLog'),
          name: 'ChatLoginLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'chatLoginLog' }
        },
        {
          hidden: true,
          code: 'D39',
          path: 'chatTextLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatTextLog'),
          name: 'ChatTextLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'chatTextLog' }
        },
        {
          hidden: true,
          code: 'D39',
          path: 'chatImageLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatImageLog'),
          name: 'ChatImageLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'chatImageLog' }
        },
        {
          hidden: true,
          code: 'D39',
          path: 'chatFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatFileLog'),
          name: 'ChatFileLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'chatFileLog' }
        },
        {
          code: 'D39',
          path: 'chatAllLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatAllLog'),
          name: 'ChatAllLogP',
          meta: { title: 'chatAllLog' }
        },
        {
          hidden: false,
          code: 'D40',
          path: 'chatToolDownloadLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/chatToolDownloadLog'),
          name: 'ChatToolDownloadLog',
          meta: { title: 'chatToolDownloadLog' }
        },
        {
          code: 'D45',
          path: 'WIFILog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/wifiLog'),
          name: 'wifiLogP',
          meta: { title: 'WIFILog' }
        },
        {
          code: 'D52',
          path: 'netFlowLogRT',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/netFlowRT'),
          name: 'NetFlowLogRTP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'netFlowLogRT' }
        },
        {
          code: 'D53',
          path: 'netFlowLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/netFlow'),
          name: 'NetFlowLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'netFlowLog' }
        },
        {
          code: 'D64',
          path: 'emailLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/email'),
          name: 'EmailLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'emailLog' }
        },
        {
          code: 'H56',
          path: 'telnetMonitorLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/telnetMonitorLog'),
          name: 'TelnetMonitorLog',
          meta: { title: 'telnetMonitorLog' }
        },
        {
          code: 'D74',
          path: 'localFileShareLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/localFileShareLog'),
          name: 'LocalFileShareLog',
          meta: { title: 'localFileBackupRecord' }
        },
        {
          code: 'D75',
          path: 'netFileShareLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/netFileShareLog'),
          name: 'NetFileShareLog',
          meta: { title: 'netFileBackupRecord' }
        },
        {
          code: 'D82',
          path: 'netDiskLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/netDiskLog'),
          name: 'NetDiskLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'netDiskLog' }
        },
        {
          code: 'D84',
          path: 'ftpLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/ftp'),
          name: 'FtpLogP',
          meta: { title: 'ftpLog' }
        },
        {
          code: 'H58',
          path: 'remoteDesktopLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/remoteDesktopLog'),
          name: 'RemoteDesktopLog',
          meta: { title: 'remoteDesktopControlLog' }
        },
        {
          code: 'D87',
          path: 'aiChatLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/aiChatLog'),
          name: 'AiChatLog',
          meta: { title: 'AiChatLog' }
        },
        {
          code: 'D88',
          path: 'aiUploadFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/aiUploadFileLog'),
          name: 'AiUploadFileLog',
          meta: { title: 'AiUploadFileLog' }
        }
      ]
    },
    {
      path: 'encryptionLog',
      noComponent: true,
      alwaysShow: true,
      name: 'EncryptionLog',
      meta: {
        title: 'encryptionLog',
        icon: 'dataSecurity'
      },
      children: [
        {
          code: 'E51',
          path: 'secretLevelLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/secretLevelLog'),
          name: 'SecretLevelLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'secretLevelLog' }
        },
        {
          code: 'E53',
          path: 'encOrDecLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/encOrDecLog'),
          name: 'EncOrDecLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'encOrDecLog' }
        },
        {
          code: 'E52',
          path: 'diskScanLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/diskScanLog'),
          name: 'DiskScanLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'diskScanEncDecLog' }
        },
        {
          code: 'E77',
          path: 'mailWhiteLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/mailWhiteLog'),
          name: 'mailWhiteLog',
          meta: { title: 'mailWhiteLog' }
        },
        {
          code: 'E90',
          path: 'clipboardLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/clipboardLog'),
          name: 'clipboardLog',
          meta: { title: 'clipboardLog' }
        },
        {
          code: 'E78',
          path: 'httpWhiteListDecLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/httpWhiteListDecLog'),
          name: 'httpWhiteListDecLog',
          meta: { title: 'httpWhiteListDecLog' }
        },
        {
          code: 'E54',
          path: 'fileBackupLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/fileBackupLog'),
          name: 'FileBackupLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'fileBackupLog' }
        },
        {
          code: 'E48',
          path: 'fileOutgoingLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/fileOutgoingLog'),
          name: 'FileOutgoingLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'fileOutgoingLog' }
        },
        // {
        //   hidden: true,
        //   code: 'H76',
        //   path: 'decFileStatistics',
        //   component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/decFileStatistics'),
        //   name: 'DecFileStatisticsP', // Tip: 命名规则请查看文件首行注释
        //   meta: { title: '解密文件统计' }
        // },
        {
          code: 'E56',
          path: 'enDeFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/EnDeFileLog'),
          name: 'EnDeFileLogP',
          meta: { title: 'enDeFileLog' }
        },
        {
          code: 'E72',
          path: 'documentOrigins',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/dataEncryption/fileTrace/documentOrigins'),
          name: 'documentOrigins',
          meta: { title: 'documentOrigins' }
        },
        {
          code: 'E73',
          path: 'documentSpread',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/dataEncryption/fileTrace/documentSpread'),
          name: 'documentSpread',
          meta: { title: 'documentSpread' }
        },
        {
          code: 'E75',
          path: 'blindWatermarkLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/dataEncryption/fileTrace/blindWatermarkLog'),
          name: 'blindWatermarkLog',
          meta: { title: 'blindWatermarkLog' }
        },
        {
          code: 'E76',
          path: 'smartBackupLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/smartBackupLog'),
          name: 'SmartBackupLog',
          meta: { title: 'smartBackupLog' }
        },
        {
          code: 'E79',
          path: 'smartBackupRestoreLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/smartBackupRestoreLog'),
          name: 'SmartBackupRestoreLog',
          meta: { title: 'smartBackupRestoreLog' }
        },
        {
          code: 'C90',
          path: 'tagingLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/tagingLog'),
          name: 'TagingLog', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'tagingLog' }
        },
        {
          code: 'C9B',
          path: 'diskScanTagLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/diskScanTagLog'),
          name: 'DiskScanTagLog', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'diskScanTagLog' }
        },
        {
          code: 'C9C',
          path: 'tagFileOutSendLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/tagFileOutSendLog'),
          name: 'TagFileOutSendLog', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'tagFileOutSendLog' }
        }
      ]
    },
    {
      path: 'contentLog',
      noComponent: true,
      name: 'ContentLog',
      meta: {
        title: 'contentLog',
        icon: 'contentStgGroup'
      },
      alwaysShow: true,
      children: [
        {
          code: 'H11',
          path: 'issueLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/issue/issueLog'),
          name: 'IssueLog',
          meta: { title: 'issueLog' }
        },
        {
          code: 'H12',
          path: 'dripIssueLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/issue/dripLog'),
          name: 'DripIssueLog',
          meta: { title: 'dripIssueLog' }
        },
        // {
        //   code: 'F16',
        //   path: 'diskScanSelfCheckLog',
        //   component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/diskScanSelfCheckLog'),
        //   name: 'DiskScanSelfCheckLog',
        //   meta: { title: 'diskScanSelfCheckLog' }
        // },
        {
          code: 'F18',
          path: 'diskScanLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/issue/diskScanLog'),
          name: 'DiskScanSensCheckLog', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'diskScanSensCheckLog' }
        }
      ]
    },
    {
      path: 'approvalLog',
      noComponent: true,
      name: 'ApprovalLog',
      meta: {
        title: 'approvalMgrLog',
        icon: 'processSet'
      },
      alwaysShow: true,
      children: [
        {
          code: 'E55',
          path: 'approvalLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/approvalLog'),
          name: 'ApprovalLogP', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'approvalLog' }
        },
        {
          code: 'H13',
          path: 'sensitiveFileOutLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/sensitiveFileOutLog'),
          name: 'SensitiveFileOutLog',
          meta: { title: 'sensitiveFileOutLog' }
        }
      ]
    }
  ]
}

export default behaviorAuditingRouter
