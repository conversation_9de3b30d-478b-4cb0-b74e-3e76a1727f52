/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
 **/

import Layout from '@/layout'

const assetsRouter = {
  path: '/assets',
  component: Layout,
  redirect: '/assets/systemMaintenance',
  name: 'Assets',
  meta: {
    title: 'assetManagement',
    icon: 'form'
  },
  children: [
    {
      path: 'systemMaintenance',
      noComponent: true,
      redirect: '/resourceManage/systemMaintenance/path1-1-1',
      name: 'systemMaintenance',
      meta: {
        title: 'systemOperational',
        icon: 'systemMaintenance'
      },
      children: [
        {
          hidden: true,
          path: 'ArpFireWall',
          component: () => import('@/views/assets/systemMaintenance/arpFireWall'),
          name: 'ArpFireWall',
          meta: { title: 'firewallSettings' }
        }
      ]
    }
  ]
}

export default assetsRouter
