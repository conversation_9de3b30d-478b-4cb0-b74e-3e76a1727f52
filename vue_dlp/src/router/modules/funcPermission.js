/**
 * 功能说明：
 *    1、此文件的配置主要用于角色权限分配界面，显示每一个功能权限对应的文字（多语言翻译）
 *    2、这些功能权限属于哪些菜单，从后台配置permissionConfig.xml获取，当前文件无需保存
 * 属性说明
 * key为功能性权限菜单编码，用于控制按钮等元素是否显示，对应permissionConfig.xml中的编码
 * value为此菜单下，需要控制的功能性权限，格式为{ rel: '208' }
 *      其中：rel 依赖code，比如A必须在B权限下，才有效
 */
// 多语言的翻译移到permissionConfig.xml，避免需要多个地方配置，容易遗漏
const funcMenuCode = {
  '321': { rel: '208' },  // 审批日志
  '217': { rel: '216' },  // 刻录机使用记录
  '602': { rel: '600' },  // 综合报表
  '607': { rel: '605' },  // 综合趋势图
  '712': { rel: '710' },  // 邮件报表
  '717': { rel: '715' },  // 邮件趋势图
  '742': { rel: '740' },  // 即时通讯报表
  '747': { rel: '745' },  // 即时通讯趋势图
  '867': { rel: '865' },  // 自定义报表
  '872': { rel: '870' }   // 自定义趋势图
}

export default funcMenuCode
