/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
 *
 *    isLog: 是否日志模块：在销售模块中包含“日志审计”时，去除isLog=true的菜单，如果销售模块不包含，则显示
 **/

import Layout from '@/layout'

const terminalManageRouter = {
  path: '/terminalManage',
  component: Layout,
  name: 'TerminalManage',
  meta: {
    title: 'terminalManage'
  },
  children: [
    {
      path: 'overView',
      noComponent: true,
      name: 'OverView',
      meta: {
        title: 'overview',
        icon: 'overview'
      },
      alwaysShow: true,
      children: [
        {
          code: 'B11',
          path: 'strategyOverViews',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/strategyGroup/overview'),
          name: 'StrategyOverViews',
          meta: { title: 'strategyOverViews' }
        },
        {
          code: 'B51',
          path: 'ruleStrategyOverViews',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/strategyGroup/ruleOverView'),
          name: 'RuleStrategyOverViews',
          meta: { title: 'ruleStrategyOverViews' }
        }
      ]
    },
    {
      path: 'terminalManage',
      noComponent: true,
      name: 'TerminalManage',
      meta: {
        title: 'terminalManage1',
        icon: 'terminalManage'
      },
      children: [
        {
          code: 'B21',
          path: 'approvalAccess',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/approvalAccess'),
          name: 'ApprovalAccess',
          meta: { title: 'approvalAccess' }
        },
        {
          code: 'B22',
          path: 'accessLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/accessLog'),
          name: 'AccessLog',
          meta: { title: 'accessLog', isLog: true }
        },
        {
          code: 'B23',
          path: 'user',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/user'),
          name: 'User',
          meta: { title: 'user' }
        },
        {
          code: 'B2S',
          path: 'userRole',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/userRole'),
          name: 'UserRole',
          meta: { title: 'userRole' }
        },
        {
          code: 'B24',
          path: 'terminal',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/terminal'),
          name: 'Terminal',
          meta: { title: 'terminal' }
        },
        {
          code: 'B2J',
          path: 'terminalGrpNickNameV2',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/terminalGrpNickNameV2'),
          name: 'terminalGrpNickNameV2',
          meta: { title: 'terminalGrpNickName', isLog: false }
        },
        {
          // hidden: true,
          code: 'B2B',
          path: 'hotkey',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/hotkey'),
          name: 'Hotkey',
          meta: { title: 'hotkey' }
        },
        {
          code: 'B2C',
          path: 'offlineStrategy',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/dataEncryption/encryption/offlineStrategy'),
          name: 'OfflineStrategy',
          meta: { title: 'offlineStrategy' }
        },
        {
          hidden: true, // 这个策略包是用来维护离线策略下发的策略包，只提供按钮跳转，不在菜单栏里初始化
          code: 'B2C',
          path: 'allGroup',
          component: () => import(/* webpackChunkName: 'stgGroup' */ '@/views/dataEncryption/encryption/allGroup'),
          name: 'AllGroup',
          meta: { title: 'allGroup' }
        },
        {
          code: 'B2D',
          path: 'TerminalUpgrade',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/assets/systemMaintenance/terminalUpgrade'),
          name: 'TerminalUpgrade',
          meta: { title: 'TerminalUpgrade' }
        },
        {
          code: 'B2N',
          path: 'mobileTerminalUpgrade',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/mobileTerminalUpgrade'),
          name: 'MobileTerminalUpgrade',
          meta: { title: 'mobileTerminalUpgrade' }
        },
        {
          code: 'B2K',
          path: 'mobileTerminal',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/mobileTerminal'),
          name: 'MobileTerminal',
          meta: { title: 'mobileTerminal' }
        },
        {
          code: 'B2P',
          path: 'mobileTerminalFileTool',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/mobileTerminalFileTool'),
          name: 'MobileTerminalFileTool',
          meta: { title: 'mobileTerminalFileTool', stgCode: 232 }
        },
        {
          code: 'B2I',
          path: 'lockScreenLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/lockScreenLog'),
          name: 'LockScreenLog',
          meta: { title: 'lockScreenLog', isLog: true }
        }
      ]
    },
    {
      path: 'telemaintenance',
      noComponent: true,
      name: 'Telemaintenance',
      meta: {
        title: 'telemaintenance',
        icon: 'configManage'
      },
      children: [
        {
          code: 'B31',
          path: 'systemInformation',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/system'), // Parent router-view
          name: 'SystemInformation',
          meta: { title: 'systemInformation' }
        },
        {
          code: 'B32',
          path: 'SystemProcess',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/process'),
          name: 'SystemProcess',
          meta: { title: 'SystemProcess' }
        },
        {
          code: 'B33',
          path: 'SoftwareTask',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/softwareTask'),
          name: 'SoftwareTask',
          meta: { title: 'SoftwareTask' }
        },
        {
          code: 'B34',
          path: 'Explorer',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/explorer'),
          name: 'Explorer',
          meta: { title: 'Explorer' }
        },
        {
          code: 'B37',
          path: 'SystemDevice',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/device'),
          name: 'SystemDevice',
          meta: { title: 'SystemDevice' }
        },
        {
          code: 'B38',
          path: 'ShareManage',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/shareManage'),
          name: 'ShareManage',
          meta: { title: 'ShareManage' }
        },
        {
          code: 'B39',
          path: 'SystemService',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/service'),
          name: 'SystemService',
          meta: { title: 'SystemService' }
        },
        {
          code: 'B3E',
          path: 'StartupItem',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/startupItem'),
          name: 'StartupItem',
          meta: { title: 'StartupItem' }
        },
        {
          code: 'B3G',
          path: 'localUserAndGroup',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/localUserAndGroup'),
          name: 'LocalUserAndGroup',
          meta: { title: 'localUserAndGroup' }
        },
        {
          code: 'B3I',
          path: 'networkResourceMonitor',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/networkResourceMonitor'),
          name: 'NetworkResourceMonitor',
          meta: { title: 'networkResourceMonitor' }
        },
        {
          code: 'B3J',
          path: 'diskPerformance',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/diskPerformance'),
          name: 'DiskPerformance',
          meta: { title: 'diskPerformance' }
        },
        {
          code: 'B3H',
          path: 'registry',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/registry'),
          name: 'Registry',
          meta: { title: 'registry' }
        },
        {
          code: 'B3C',
          path: 'vnc',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/vnc'),
          name: 'Vnc',
          meta: { title: 'vnc' }
        },
        {
          code: 'B3L',
          path: 'CmdCommand',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/cmdCommand'),
          name: 'CmdCommand',
          meta: { title: 'CmdCommand' }
        },
        {
          code: 'B3A',
          path: 'IPAndMAC',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/assets/systemMaintenance/IPAndMAC'),
          name: 'IPAndMAC',
          meta: { title: 'IPAndMAC' },
          prepare: true
        },
        {
          code: 'B2H',
          path: 'offlineLockScreen',
          component: () => import(/* webpackChunkName: 'remoteMaintenance' */ '@/views/system/terminalManage/offlineLockScreen'),
          name: 'OfflineLockScreen',
          meta: { title: 'offlineLockScreen' },
          prepare: true
        }
      ]
    },
    {
      path: 'hardAsset',
      noComponent: true,
      name: 'HardAsset',
      meta: {
        title: 'hardwareAssetsConfig',
        icon: 'hardware'
      },
      children: [
        {
          code: 'B42',
          path: 'HardwareAssetsView',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/assets/assetsConfig/hardwareAssetsView'), // Parent router-view
          name: 'HardwareAssetsView',
          meta: { title: 'HardwareAssetsView' }
        },
        {
          code: 'B41',
          path: 'AssetAlarmSetup',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/assets/assetsConfig/assetAlarmSetup'), // Parent router-view
          name: 'AssetAlarmSetup',
          meta: { title: 'HardwareAssetChangeAlarmLogSettings' }
        },
        {
          code: 'B44',
          path: 'HardwareChangeAlarm',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/assetsConfig/hardwareChangeAlarm'),
          name: 'HardwareChangeAlarm',
          meta: { title: 'HardwareChangeAlarm', isLog: true }
        },
        {
          code: 'B43',
          path: 'HardwareAssetLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/assetsConfig/hardwareAssetLog'),
          name: 'HardwareAssetLog',
          meta: { title: 'HardwareAssetLog', isLog: true }
        }
      ]
    },
    {
      path: 'softAsset',
      noComponent: true,
      name: 'SoftAsset',
      meta: {
        title: 'softwareAssetsConfig',
        icon: 'software'
      },
      children: [
        {
          code: 'B48',
          path: 'SoftwareAssets',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/softwareAssets/assets'),
          name: 'SoftwareAssets',
          meta: { title: 'SoftwareAssets' }
        },
        {
          code: 'B45',
          path: 'SoftwareStatistics',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/softwareAssets/statistics'),
          name: 'SoftwareStatistics',
          meta: { title: 'SoftwareStatistics' }
        },
        {
          code: 'B4F',
          path: 'softwareAssetAlarmSetup',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/assetAlarmSetup'),
          name: 'SoftwareAssetAlarmSetup',
          meta: { title: 'softwareAssetChangeAlarmLogSettings' }
        },
        {
          code: 'B47',
          path: 'softwareChangeAlarm',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/assetsConfig/softwareChangeAlarm'),
          name: 'SoftwareChangeAlarm',
          meta: { title: 'softwareChangeAlarm', isLog: true }
        },
        {
          code: 'B46',
          path: 'softwareAssetLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/assetsConfig/softwareAssetLog'),
          name: 'SoftwareAssetLog',
          meta: { title: 'softwareAssetLog', isLog: true }
        },
        {
          hidden: true,
          code: 'B45',
          path: 'softwareDetail',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/softwareDetail'),
          name: 'SoftwareDetail',
          meta: { title: 'softwareDetail' }
        }
      ]
    },
    {
      path: 'copyrightManage',
      noComponent: true,
      name: 'CopyrightManage',
      meta: {
        title: 'copyrightManage',
        icon: 'copyright'
      },
      children: [
        // {
        //   code: 'B48',
        //   path: 'SoftwareOrder',
        //   component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/copyrightManage/orders'),
        //   name: 'SoftwareOrder',
        //   meta: { title: 'SoftwareOrder' }
        // },
        {
          hidden: true,
          code: 'B48',
          path: 'orderForSoftware',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/copyrightManage/orderManage/orderForSoftware'),
          name: 'OrderForSoftware',
          meta: { title: 'orderForSoftware' }
        },
        {
          code: 'B49',
          path: 'SoftwareOrderManage',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/copyrightManage/orderManage'),
          name: 'SoftwareOrderManage',
          meta: { title: 'SoftwareOrderManage' }
        },
        {
          code: 'B4A',
          path: 'SoftwareAssetLicense',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/copyrightManage/assetLicense'),
          name: 'SoftwareAssetLicense',
          meta: { title: 'SoftwareAssetLicense' }
        },
        {
          hidden: true,
          code: 'B4A',
          path: 'installedSoftware',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/copyrightManage/assetLicense/softwareList/installedSoftware'),
          name: 'InstalledSoftware',
          meta: { title: 'installedSoftware' }
        },
        {
          hidden: true,
          code: 'B4A',
          path: 'authorizedInstalled',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/copyrightManage/assetLicense/softwareList/authorizedInstalled'),
          name: 'AuthorizedInstalled',
          meta: { title: 'authorizedInstalled' }
        },
        {
          hidden: true,
          code: 'B4A',
          path: 'illegalInstalled',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/copyrightManage/assetLicense/softwareList/illegalInstalled'),
          name: 'IllegalInstalled',
          meta: { title: 'illegalInstalled' }
        },
        {
          code: 'B4B',
          path: 'SoftwareCopyrightStrategy',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/copyrightManage/strategy'),
          name: 'SoftwareCopyrightStrategy',
          meta: { title: 'SoftwareCopyrightStrategy' }
        }
      ]
    },
    {
      path: 'strategy',
      noComponent: true,
      name: 'Strategy',
      meta: {
        title: 'strategy',
        icon: 'strategy'
      },
      children: [
        {
          code: 'C24',
          path: 'installPackage',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/behaviorManage/application/install'),
          name: 'InstallPackage',
          meta: { title: 'installPackage', stgCode: 9 },
          prepare: true
        },
        {
          code: 'B4C',
          path: 'softwareLimit',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/strategy/softwareLimit'),
          name: 'SoftwareLimit',
          meta: { title: 'softwareLimit' },
          prepare: true
        },
        {
          code: 'B4D',
          path: 'requireInstall',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/strategy/requireInstall'),
          name: 'RequireInstall',
          meta: { title: 'requireInstall' },
          prepare: true
        },
        {
          code: 'B4E',
          path: 'requireRun',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/softwareManage/strategy/requireRun'),
          name: 'RequireRun',
          meta: { title: 'requireRun' },
          prepare: true
        }
      ]
    },
    {
      path: 'uninstallManage',
      noComponent: true,
      name: 'UninstallManage',
      meta: {
        title: 'uninstallManage',
        icon: 'uninstall'
      },
      children: [
        {
          code: 'B35',
          path: 'SoftwareTaskStrategy',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/assets/systemMaintenance/softWareTaskStrategy'),
          name: 'SoftwareTaskStrategy',
          meta: { title: 'SoftwareTaskStrategy' }
        },
        {
          code: 'B36',
          path: 'CurTaskLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/assets/systemMaintenance/CurTaskLog'),
          name: 'CurTaskLog',
          meta: { title: 'CurTaskLog', isLog: true }
        }
      ]
    },
    {
      path: 'softwareManager',
      noComponent: true,
      name: 'softwareManager',
      meta: {
        title: 'softwareManager',
        icon: 'softwareManager'
      },
      children: [
        {
          code: 'B61',
          path: 'softwareRepository',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/system/terminalManage/softwareManager/repository'),
          name: 'softwareRepository',
          meta: { title: 'softwareRepository' }
        },
        {
          code: 'B62',
          path: 'softwareDownloadStrategy',
          component: () => import(/* webpackChunkName: 'assetsManage' */ '@/views/system/terminalManage/softwareManager/downloadStrategy'),
          name: 'softwareDownloadStrategy',
          meta: { title: 'softwareDownloadStrategy' }
        },
        {
          code: 'B65',
          path: 'softwareAuditLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/system/terminalManage/softwareManager/softwareAuditLog'),
          name: 'SoftwareAuditLog',
          meta: { title: 'softwareAuditLog', isLog: true }
        }
      ]
    },
    {
      path: 'taskDistribute',
      noComponent: true,
      name: 'taskDistribute',
      meta: {
        title: 'taskDistribute',
        icon: 'taskPush'
      },
      children: [
        {
          code: 'B74',
          path: 'taskDistribute',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/taskDistribute'),
          name: 'TaskDistribute',
          meta: { title: 'taskDistribute' }
        }
      ]
    },
    {
      path: 'termSecurityManager',
      noComponent: true,
      name: 'termSecurityDetection',
      meta: {
        title: 'termSecurityDetection',
        icon: 'termSecurityManager'
      },
      children: [
        //  终端安全检测功能
        {
          code: 'B64',
          path: 'termSecurityDetection',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/softwareManager/termSecurity'),
          name: 'termSecurityDetection',
          meta: { title: 'termSecurityDetection', stgCode: 246 },
          prepare: true
        },
        {
          code: 'B65',
          path: 'fixPlanLibrary',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/softwareManager/termSecurity/fixPlanLibrary'),
          name: 'fixPlanLibrary',
          meta: { title: 'fixPlanLibrary' }
        },
        {
          code: 'B67',
          path: 'termSecurityStatus',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/system/terminalManage/softwareManager/termSecurity/status'),
          name: 'termSecurityStatus',
          meta: { title: 'termSecurityStatus' }
        }
      ]
    },
    {
      hidden: false,
      path: 'patchManage',
      noComponent: true,
      name: 'patchManage',
      meta: {
        title: 'patchManagement',
        icon: 'softwareManager'
      },
      children: [
        {
          code: 'B72',
          path: 'PatchStrategy',
          hidden: false,
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/assets/patchManage/patchStrategy'),
          name: 'PatchStrategy',
          meta: { title: 'patchStrategy' }
        },
        {
          code: 'B73',
          path: 'patchManage',
          hidden: false,
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/assets/patchManage/patchManage'),
          name: 'PatchManagement',
          meta: { title: 'patchManagement' }
        },
        {
          code: 'B71',
          path: 'Patch',
          component: () => import(/* webpackChunkName: 'terminalManage' */ '@/views/assets/patchManage/downloadAndDistribution'), // Parent router-view
          name: 'PatchLibManagement',
          meta: { title: 'patchLibManagement' }
        }
      ]
    }
  ]
}

export default terminalManageRouter
