/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
 **/

import Layout from '@/layout'

const reportRouter = {
  // hidden: true,
  path: '/report',
  component: Layout,
  name: 'Report',
  meta: {
    title: 'report'
  },
  children: [
    {
      path: 'largeScreen',
      noComponent: true,
      name: 'LargeScreen',
      meta: {
        title: 'largeDataScreen',
        icon: 'report5'
      },
      children: [
        {
          code: 'IA7',
          path: 'encOrDecFileAnalysis/largeScreen',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/encOrDecFileAnalysis/index'),
          name: 'EncOrDecFileAnalysisLargeScreen',
          meta: { title: 'encOrDecFileAnalysisLargeScreen', customPath: true }
        },
        {
          code: 'IL3',
          path: 'todayReport',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/todayReport/index'),
          name: 'TodayReport',
          meta: { title: 'todayRiskData', customPath: true }
        },
        // {
        //   code: 'IL2',
        //   path: 'clientUsage',
        //   component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/clientUsage/index'),
        //   name: 'ClientUsage',
        //   meta: { title: '终端使用情况数据', customPath: true }
        // },
        {
          code: 'S13',
          path: 'softwareInstallUse',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/softwareInstallUse/index'),
          name: 'SoftwareInstallUse',
          meta: { title: 'softwareInstallationAndUsageData', customPath: true }
        },
        {
          code: 'S15',
          path: 'sensitiveFileOutLog',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/sensitiveFileOutLog/index'),
          name: 'SensitiveFileOutLargeScreen',
          meta: { title: 'outgoingFileAnalysisData', customPath: true }
        },
        // {
        //   code: 'IL2',
        //   path: 'comprehensiveAnalysis',
        //   component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/comprehensiveAnalysis/index'),
        //   name: 'ComprehensiveAnalysis',
        //   meta: { title: '综合分析数据', customPath: true }
        // },
        // {
        //   code: 'IL2',
        //   path: 'mobileStorage',
        //   component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/mobileStorage/index'),
        //   name: 'MobileStorage',
        //   meta: { title: '移动存储使用数据', customPath: true }
        // },
        // {
        //   code: 'IL2',
        //   path: 'onlineBehavior',
        //   component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/onlineBehavior/index'),
        //   name: 'OnlineBehavior',
        //   meta: { title: '上网行为数据', customPath: true }
        // },
        // {
        //   code: 'IL2',
        //   path: 'chatLog',
        //   component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/chatLog/index'),
        //   name: 'ChatLog',
        //   meta: { title: '即时通讯工具数据', customPath: true }
        // },
        {
          code: 'S12',
          path: 'operationMaintenance',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/operationMaintenance/index'),
          name: 'OperationMaintenance',
          meta: { title: 'operationMaintenance', customPath: true }
        },
        {
          code: 'S14',
          path: 'carousel',
          hidden: true,
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/largeScreen/carousel'),
          name: 'Carousel',
          meta: { title: '大屏轮播', customPath: true }
        }
      ]
    },
    {
      path: 'operationalAnalysis',
      noComponent: true,
      name: 'OperationalAnalysis',
      meta: {
        title: 'operationalReport',
        icon: 'report1'
      },
      children: [
        {
          code: '910',
          path: 'templateLibrary',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/operationalAnalysis/templateLibrary'),
          name: 'TemplateLibrary',
          meta: { title: 'baseTempLib' }
        },
        {
          code: '907',
          path: 'templateConfig',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/operationalAnalysis/templateConfig'),
          name: 'TemplateConfig',
          meta: { title: 'enterpriseTempConfig' }
        },
        {
          code: '908',
          path: 'sendConfig',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/operationalAnalysis/sendConfig'),
          name: 'SendConfig',
          meta: { title: 'pushTaskConfig' }
        },
        {
          code: '909',
          path: 'templateRecord',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/operationalAnalysis/templateRecord'),
          name: 'TemplateRecord',
          meta: { title: 'pushingRecord' }
        }
      ]
    },
    {
      path: 'terminalManagement',
      noComponent: true,
      name: 'TerminalManagement',
      meta: {
        title: 'termManagementReport',
        icon: 'report3'
      },
      children: [
        {
          code: 'IC1',
          path: 'hardwareAssetLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalManagement/hardwareAssetLog/statistical'),
          name: 'HardwareStatistical',
          meta: { title: 'HardwareStatistical' }
        },
        {
          code: 'IC2',
          path: 'hardwareAssetLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalManagement/hardwareAssetLog/trend'),
          name: 'HardwareTrend',
          meta: { title: 'HardwareTrend' }
        },
        {
          code: 'IC5',
          path: 'hardwareAssetLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalManagement/hardwareAssetLog/compare'),
          name: 'HardwareCompare',
          meta: { title: 'HardwareCompare' }
        },
        {
          code: 'IC3',
          path: 'softwareAssetLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalManagement/softwareAssetLog/statistical'),
          name: 'SoftwareStatistical',
          meta: { title: 'SoftwareStatistical' }
        },
        {
          code: 'IC4',
          path: 'softwareAssetLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalManagement/softwareAssetLog/trend'),
          name: 'SoftwareTrend',
          meta: { title: 'SoftwareTrend' }
        },
        {
          code: 'IC6',
          path: 'softwareAssetLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalManagement/softwareAssetLog/compare'),
          name: 'SoftwareCompare',
          meta: { title: 'SoftwareCompare' }
        },
        {
          code: 'IH1',
          path: 'appUninstall/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalManagement/appUninstall/statistical'),
          name: 'AppUninstallStatistical',
          meta: { title: 'AppUninstallStatistical' }
        },
        {
          code: 'IH2',
          path: 'appUninstall/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalManagement/appUninstall/trend'),
          name: 'AppUninstallTrend',
          meta: { title: 'AppUninstallTrend' }
        },
        {
          code: 'IH3',
          path: 'appUninstall/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalManagement/appUninstall/compare'),
          name: 'AppUninstallCompare',
          meta: { title: 'AppUninstallCompare' }
        }
      ]
    },
    {
      path: 'hardwareDeviceManagement',
      noComponent: true,
      name: 'HardwareDeviceManagement',
      meta: {
        title: 'hardwareDeviceManagementReport',
        icon: 'report2'
      },
      children: [
        {
          code: 'I21',
          path: 'printMonitor/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/printMonitor/statistical'),
          name: 'PrintStatistical',
          meta: { title: 'PrintStatistical' }
        },
        {
          code: 'I22',
          path: 'printMonitor/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/printMonitor/trend'),
          name: 'PrintTrend',
          meta: { title: 'PrintTrend' }
        },
        {
          code: 'I23',
          path: 'printMonitor/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/printMonitor/compare'),
          name: 'PrintCompare',
          meta: { title: 'PrintCompare' }
        },
        {
          code: 'I2A',
          path: 'btConnChange/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/btConnChange/statistical'),
          name: 'BtConnChangeStatistical',
          meta: { title: 'BtConnChangeStatistical' }
        },
        {
          code: 'I2B',
          path: 'btConnChange/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/btConnChange/trend'),
          name: 'BtConnChangeTrend',
          meta: { title: 'BtConnChangeTrend' }
        },
        {
          code: 'I2C',
          path: 'btConnChange/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/btConnChange/compare'),
          name: 'BtConnChangeCompare',
          meta: { title: 'BtConnChangeCompare' }
        },
        {
          code: 'I2D',
          path: 'btFileLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/btFileLog/statistical'),
          name: 'BtFileLogStatistical',
          meta: { title: 'BtFileLogStatistical' }
        },
        {
          code: 'I2E',
          path: 'btFileLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/btFileLog/trend'),
          name: 'BtFileLogTrend',
          meta: { title: 'BtFileLogTrend' }
        },
        {
          code: 'I2F',
          path: 'btFileLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/btFileLog/compare'),
          name: 'BtFileLogCompare',
          meta: { title: 'BtFileLogCompare' }
        },
        {
          code: 'I24',
          path: 'adbMonitorLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/adbMonitorLog/statistical'),
          name: 'AdbMonitorLogStatistical',
          meta: { title: 'AdbMonitorLogStatistical' }
        },
        {
          code: 'I25',
          path: 'adbMonitorLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/adbMonitorLog/trend'),
          name: 'AdbMonitorLogTrend',
          meta: { title: 'AdbMonitorLogTrend' }
        },
        {
          code: 'I26',
          path: 'adbMonitorLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/adbMonitorLog/compare'),
          name: 'AdbMonitorLogCompare',
          meta: { title: 'AdbMonitorLogCompare' }
        },
        {
          code: 'I27',
          path: 'mtpFileLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/mtpFileLog/statistical'),
          name: 'MtpFileLogStatistical',
          meta: { title: 'MtpFileLogStatistical' }
        },
        {
          code: 'I28',
          path: 'mtpFileLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/mtpFileLog/trend'),
          name: 'MtpFileLogTrend',
          meta: { title: 'MtpFileLogTrend' }
        },
        {
          code: 'I29',
          path: 'mtpFileLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/mtpFileLog/compare'),
          name: 'MtpFileLogCompare',
          meta: { title: 'MtpFileLogCompare' }
        },
        {
          code: 'I2G',
          path: 'usbDeviceOperaLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/usbDeviceOperaLog/statistical'),
          name: 'UsbDeviceOperaLogStatistical',
          meta: { title: 'UsbDeviceOperaLogStatistical' }
        },
        {
          code: 'I2H',
          path: 'usbDeviceOperaLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/usbDeviceOperaLog/trend'),
          name: 'UsbDeviceOperaLogTrend',
          meta: { title: 'UsbDeviceOperaLogTrend' }
        },
        {
          code: 'I2I',
          path: 'usbDeviceOperaLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/usbDeviceOperaLog/compare'),
          name: 'UsbDeviceOperaLogCompare',
          meta: { title: 'UsbDeviceOperaLogCompare' }
        },
        {
          code: 'I2J',
          path: 'cdBurnLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/cdBurnLog/statistical'),
          name: 'CdBurnLogStatistical',
          meta: { title: 'CdBurnLogStatistical' }
        },
        {
          code: 'I2K',
          path: 'cdBurnLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/cdBurnLog/trend'),
          name: 'CdBurnLogTrend',
          meta: { title: 'CdBurnLogTrend' }
        },
        {
          code: 'I2L',
          path: 'cdBurnLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/cdBurnLog/compare'),
          name: 'CdBurnLogCompare',
          meta: { title: 'CdBurnLogCompare' }
        },
        {
          code: 'I41',
          path: 'mobileStorage/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/mobileStorage/statistical'),
          name: 'MobileStorageStatistical',
          meta: { title: 'MobileStorageStatistical' }
        },
        {
          code: 'I42',
          path: 'mobileStorage/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/mobileStorage/trend'),
          name: 'MobileStorageTrend',
          meta: { title: 'MobileStorageTrend' }
        },
        {
          code: 'I43',
          path: 'mobileStorage/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/hardwareDeviceManagement/mobileStorage/compare'),
          name: 'MobileStorageCompare',
          meta: { title: 'MobileStorageCompare' }
        }
      ]
    },
    {
      path: 'terminalBehaviorControl',
      noComponent: true,
      name: 'TerminalBehaviorControl',
      meta: {
        title: 'termBehaviorControlReport',
        icon: 'report4'
      },
      children: [
        {
          code: 'I71',
          path: 'appReportex/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/appReportex/statistical'),
          name: 'AppReportexStatistical',
          meta: { title: 'AppReportexStatistical' }
        },
        {
          code: 'I72',
          path: 'appReportex/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/appReportex/trend'),
          name: 'AppReportexTrend',
          meta: { title: 'AppReportexTrend' }
        },
        {
          code: 'I73',
          path: 'appReportex/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/appReportex/compare'),
          name: 'AppReportexCompare',
          meta: { title: 'AppReportexCompare' }
        },
        {
          code: 'I31',
          path: 'fileOpLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/fileOpLog/statistical'),
          name: 'FileOpStatistical',
          meta: { title: 'FileOpStatistical' }
        },
        {
          code: 'I32',
          path: 'fileOpLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/fileOpLog/trend'),
          name: 'FileOpTrend',
          meta: { title: 'FileOpTrend' }
        },
        {
          code: 'I33',
          path: 'fileOpLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/fileOpLog/compare'),
          name: 'FileOpCompare',
          meta: { title: 'FileOpCompare' }
        },
        {
          code: 'I51',
          path: 'systemLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/systemLog/statistical'),
          name: 'SystemLogStatistical',
          meta: { title: 'SystemLogStatistical' }
        },
        {
          code: 'I52',
          path: 'systemLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/systemLog/trend'),
          name: 'SystemLogTrend',
          meta: { title: 'SystemLogTrend' }
        },
        {
          code: 'I53',
          path: 'systemLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/systemLog/compare'),
          name: 'SystemLogCompare',
          meta: { title: 'SystemLogCompare' }
        },
        {
          code: 'I54',
          path: 'energySavingRecord/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/energySavingRecord/statistical'),
          name: 'EnergySavingRecordStatistical',
          meta: { title: 'EnergySavingRecordStatistical' }
        },
        {
          code: 'I55',
          path: 'energySavingRecord/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/energySavingRecord/trend'),
          name: 'EnergySavingRecordTrend',
          meta: { title: 'EnergySavingRecordTrend' }
        },
        {
          code: 'I56',
          path: 'energySavingRecord/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/energySavingRecord/compare'),
          name: 'EnergySavingRecordCompare',
          meta: { title: 'EnergySavingRecordCompare' }
        }
        // ,
        // {
        //   code: 'I57',
        //   path: 'documentTag/statistical',
        //   component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/documentTag/statistical/index'),
        //   name: 'DocumentTagStatistical',
        //   meta: { title: 'taggedRecordReport' }
        // },
        // {
        //   code: 'I58',
        //   path: 'documentTag/compare',
        //   component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/documentTag/compare'),
        //   name: 'DocumentTagCompare',
        //   meta: { title: 'labeledComparisonTrendChart' }
        // }
      ]
    },
    {
      path: 'webpageControl',
      noComponent: true,
      name: 'WebpageControl',
      meta: {
        title: 'WebpageControl',
        icon: 'report10'
      },
      children: [
        {
          code: 'I61',
          path: 'urlsLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/urlsLog/statistical'),
          name: 'UrlsLogStatistical',
          meta: { title: 'UrlsLogStatistical' }
        },
        {
          code: 'I62',
          path: 'urlsLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/urlsLog/trend'),
          name: 'UrlsLogTrend',
          meta: { title: 'UrlsLogTrend' }
        },
        {
          code: 'I69',
          path: 'urlsLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/urlsLog/compare'),
          name: 'UrlsLogCompare',
          meta: { title: 'UrlsLogCompare' }
        },
        {
          code: 'I63',
          path: 'webBrowsingTime/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/webBrowsingTime/statistical'),
          name: 'WebBrowsingTimeStatistical',
          meta: { title: 'WebBrowsingTimeStatistical' }
        },
        {
          code: 'I64',
          path: 'webBrowsingTime/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/webBrowsingTime/trend'),
          name: 'WebBrowsingTimeTrend',
          meta: { title: 'WebBrowsingTimeTrend' }
        },
        {
          code: 'I6A',
          path: 'webBrowsingTime/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/webBrowsingTime/compare'),
          name: 'WebBrowsingTimeCompare',
          meta: { title: 'WebBrowsingTimeCompare' }
        },
        {
          code: 'I65',
          path: 'urlsSearch/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/urlsSearch/statistical'),
          name: 'UrlsSearchStatistical',
          meta: { title: 'UrlsSearchStatistical' }
        },
        {
          code: 'I66',
          path: 'urlsSearch/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/urlsSearch/trend'),
          name: 'UrlsSearchTrend',
          meta: { title: 'UrlsSearchTrend' }
        },
        {
          code: 'I6B',
          path: 'urlsSearch/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/urlsSearch/compare'),
          name: 'UrlsSearchCompare',
          meta: { title: 'UrlsSearchCompare' }
        },
        {
          code: 'I67',
          path: 'browserPasteLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/browserPasteLog/statistical'),
          name: 'BrowserPasteLogStatistical',
          meta: { title: 'BrowserPasteLogStatistical' }
        },
        {
          code: 'I68',
          path: 'browserPasteLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/browserPasteLog/trend'),
          name: 'BrowserPasteLogTrend',
          meta: { title: 'BrowserPasteLogTrend' }
        },
        {
          code: 'I6C',
          path: 'browserPasteLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/browserPasteLog/compare'),
          name: 'BrowserPasteLogCompare',
          meta: { title: 'BrowserPasteLogCompare' }
        },
        {
          code: 'I6D',
          path: 'forumDataLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/forumDataLog/statistical'),
          name: 'ForumDataLogStatistical',
          meta: { title: 'ForumDataLogStatistical' }
        },
        {
          code: 'I6E',
          path: 'forumDataLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/forumDataLog/trend'),
          name: 'ForumDataLogTrend',
          meta: { title: 'ForumDataLogTrend' }
        },
        {
          code: 'I6F',
          path: 'forumDataLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/webpageControl/forumDataLog/compare'),
          name: 'ForumDataLogCompare',
          meta: { title: 'ForumDataLogCompare' }
        }
      ]
    },
    {
      path: 'networkBehaviorControl',
      noComponent: true,
      name: 'NetworkBehaviorControl',
      meta: {
        title: 'netWorkBehaviorControlReport',
        icon: 'report5'
      },
      children: [
        {
          code: 'IB1',
          path: 'chatLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/chatLog/statistical'),
          name: 'ChatLogStatistical',
          meta: { title: 'ChatLogStatistical' }
        },
        {
          code: 'IB2',
          path: 'chatLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/chatLog/trend'),
          name: 'ChatLogTrend',
          meta: { title: 'ChatLogTrend' }
        },
        {
          code: 'IB3',
          path: 'chatLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/chatLog/compare'),
          name: 'ChatLogCompare',
          meta: { title: 'ChatLogCompare' }
        },
        {
          code: 'IB7',
          path: 'wifiAlarmLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/wifiAlarmLog/statistical'),
          name: 'WifiAlarmLogStatistical',
          meta: { title: 'WifiAlarmLogStatistical' }
        },
        {
          code: 'IB8',
          path: 'wifiAlarmLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/wifiAlarmLog/trend'),
          name: 'WifiAlarmLogTrend',
          meta: { title: 'WifiAlarmLogTrend' }
        },
        {
          code: 'IB9',
          path: 'wifiAlarmLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/wifiAlarmLog/compare'),
          name: 'WifiAlarmLogCompare',
          meta: { title: 'WifiAlarmLogCompare' }
        },
        {
          code: 'IF1',
          path: 'netFlowLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/netFlowLog/statistical'),
          name: 'NetFlowLogStatistical',
          meta: { title: 'NetFlowLogStatistical' }
        },
        {
          code: 'IF2',
          path: 'netFlowLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/netFlowLog/trend'),
          name: 'NetFlowLogTrend',
          meta: { title: 'NetFlowLogTrend' }
        },
        {
          code: 'IF3',
          path: 'netFlowLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/netFlowLog/compare'),
          name: 'NetFlowLogCompare',
          meta: { title: 'NetFlowLogCompare' }
        },
        {
          code: 'I81',
          path: 'mailLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/mailLog/statistical'),
          name: 'MailLogStatistical',
          meta: { title: 'MailLogStatistical' }
        },
        {
          code: 'I82',
          path: 'mailLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/mailLog/trend'),
          name: 'MailLogTrend',
          meta: { title: 'MailLogTrend' }
        },
        {
          code: 'I83',
          path: 'mailLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/mailLog/compare'),
          name: 'MailLogCompare',
          meta: { title: 'MailLogCompare' }
        },
        {
          code: 'IG1',
          path: 'telnetMonitorLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/telnetMonitorLog/statistical'),
          name: 'TelnetMonitorStatistical',
          meta: { title: 'TelnetMonitorStatistical' }
        },
        {
          code: 'IG2',
          path: 'telnetMonitorLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/telnetMonitorLog/trend'),
          name: 'TelnetMonitorTrend',
          meta: { title: 'TelnetMonitorTrend' }
        },
        {
          code: 'IG3',
          path: 'telnetMonitorLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/telnetMonitorLog/compare'),
          name: 'TelnetMonitorCompare',
          meta: { title: 'TelnetMonitorCompare' }
        },
        {
          code: 'IBA',
          path: 'localFileShareRecord/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/localFileShareRecord/statistical'),
          name: 'LocalFileShareRecordStatistical',
          meta: { title: 'LocalFileShareRecordStatistical' }
        },
        {
          code: 'IBB',
          path: 'localFileShareRecord/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/localFileShareRecord/trend'),
          name: 'LocalFileShareRecordTrend',
          meta: { title: 'LocalFileShareRecordTrend' }
        },
        {
          code: 'IBC',
          path: 'localFileShareRecord/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/localFileShareRecord/compare'),
          name: 'LocalFileShareRecordCompare',
          meta: { title: 'LocalFileShareRecordCompare' }
        },
        {
          code: 'IBD',
          path: 'netFileShareRecord/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/netFileShareRecord/statistical'),
          name: 'NetFileShareRecordStatistical',
          meta: { title: 'NetFileShareRecordStatistical' }
        },
        {
          code: 'IBE',
          path: 'netFileShareRecord/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/netFileShareRecord/trend'),
          name: 'NetFileShareRecordTrend',
          meta: { title: 'NetFileShareRecordTrend' }
        },
        {
          code: 'IBF',
          path: 'netFileShareRecord/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/netFileShareRecord/compare'),
          name: 'NetFileShareRecordCompare',
          meta: { title: 'NetFileShareRecordCompare' }
        },
        {
          code: 'II1',
          path: 'netDiskLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/netDiskLog/statistical'),
          name: 'NetDiskLogStatistical',
          meta: { title: 'NetDiskLogStatistical' }
        },
        {
          code: 'II2',
          path: 'netDiskLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/netDiskLog/trend'),
          name: 'NetDiskLogTrend',
          meta: { title: 'NetDiskLogTrend' }
        },
        {
          code: 'II3',
          path: 'netDiskLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/netDiskLog/compare'),
          name: 'NetDiskLogCompare',
          meta: { title: 'NetDiskLogCompare' }
        },
        {
          code: 'IB4',
          path: 'ftpMonitorLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/ftpMonitorLog/statistical'),
          name: 'FtpMonitorLogStatistical',
          meta: { title: 'FtpMonitorLogStatistical' }
        },
        {
          code: 'IB5',
          path: 'ftpMonitorLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/ftpMonitorLog/trend'),
          name: 'FtpMonitorLogTrend',
          meta: { title: 'FtpMonitorLogTrend' }
        },
        {
          code: 'IB6',
          path: 'ftpMonitorLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/networkBehaviorControl/ftpMonitorLog/compare'),
          name: 'FtpMonitorLogCompare',
          meta: { title: 'FtpMonitorLogCompare' }
        }
      ]
    },
    {
      path: 'dataSecurityControl',
      noComponent: true,
      name: 'DataSecurityControl',
      meta: {
        title: 'dataSecurityControlReport',
        icon: 'report11'
      },
      children: [
        {
          code: 'IA1',
          path: 'dataSecurityLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataSecurityControl/dataSecurityLog/statistical'),
          name: 'DataSecurityStatistical',
          meta: { title: 'DataSecurityStatistical' }
        },
        {
          code: 'IA2',
          path: 'dataSecurityLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataSecurityControl/dataSecurityLog/trend'),
          name: 'DataSecurityTrend',
          meta: { title: 'DataSecurityTrend' }
        },
        {
          code: 'IA3',
          path: 'dataSecurityLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataSecurityControl/dataSecurityLog/compare'),
          name: 'DataSecurityCompare',
          meta: { title: 'DataSecurityCompare' }
        },
        {
          code: 'IA4',
          path: 'denseTransDetailsLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataSecurityControl/denseTransDetailsLog/statistical'),
          name: 'DenseTransDetailsLogStatistical',
          meta: { title: 'DenseTransDetailsLogStatistical' }
        },
        {
          code: 'IA5',
          path: 'denseTransDetailsLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataSecurityControl/denseTransDetailsLog/trend'),
          name: 'DenseTransDetailsLogTrend',
          meta: { title: 'DenseTransDetailsLogTrend' }
        },
        {
          code: 'IA6',
          path: 'denseTransDetailsLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataSecurityControl/denseTransDetailsLog/compare'),
          name: 'DenseTransDetailsLogCompare',
          meta: { title: 'DenseTransDetailsLogCompare' }
        },
        {
          code: 'IAA',
          path: 'enDeFileLog/statistical',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/report/dataSecurityControl/EnDeFileLog'),
          name: 'EnDeFileLogStatistical',
          meta: { title: 'EnDeFileLogStatistical' }
        },
        {
          code: 'IA8',
          path: 'smartBackupLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataSecurityControl/smartBackupLog/statistical'),
          name: 'SmartBackupLogStatistical',
          meta: { title: 'SmartBackupLogStatistical' }
        },
        {
          code: 'IA9',
          path: 'smartBackupLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataSecurityControl/smartBackupLog/compare'),
          name: 'SmartBackupLogCompare',
          meta: { title: 'SmartBackupLogCompare' }
        },
        {
          code: 'I57',
          path: 'documentTag/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/documentTag/statistical/index'),
          name: 'DocumentTagStatistical',
          meta: { title: 'taggedRecordReport' }
        },
        {
          code: 'I58',
          path: 'documentTag/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/documentTag/compare'),
          name: 'DocumentTagCompare',
          meta: { title: 'labeledComparisonTrendChart' }
        },
        {
          code: 'I59',
          path: 'documentTag/statisticalFullScan',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/documentTag/statisticalFullScan/index'),
          name: 'DocumentTagStatisticalFullScan',
          meta: { title: 'fullDiskScanTagReport' }
        },
        {
          code: 'I5A',
          path: 'documentTag/compareFullScan',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalBehaviorControl/documentTag/compareFullScan'),
          name: 'DocumentTagCompareFullScan',
          meta: { title: 'fullDiskScanTagComparisonReport' }
        }
      ]
    },
    {
      path: 'sensitiveControl',
      noComponent: true,
      name: 'SensitiveControl',
      meta: {
        title: 'SensitiveContentControl',
        icon: 'report6'
      },
      children: [
        {
          code: 'IL2',
          path: 'riskStatement',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/riskStatement/index'),
          name: 'RiskStatement',
          meta: { title: 'RiskReport' }
        },
        // {
        //   code: 'IL4',
        //   path: 'riskStatementCompare',
        //   component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/riskStatement/compare'),
        //   name: 'RiskStatementCompare',
        //   meta: { title: 'RiskReportCompare' }
        // },
        {
          code: 'IL4',
          path: 'riskStatementCompare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/riskStatement/compare'),
          name: 'RiskStatementCompare',
          meta: { title: 'RiskReportCompare' }
        },
        {
          code: 'IM1',
          path: 'divulgeModeGeneral',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/divulgeModeGeneral/index'),
          name: 'DivulgeModeGeneral',
          meta: { title: 'GeneralViolationDisclosureMode' }
        },
        {
          code: 'IM9',
          path: 'generalViolateLossTypeCompare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/divulgeModeGeneral/compare'),
          name: 'GeneralViolateLossTypeCompare',
          meta: { title: 'GeneralViolateLossTypeCompare' }
        },
        {
          code: 'IM2',
          path: 'severityGeneral',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/severityGeneral/index'),
          name: 'SeverityGeneral',
          meta: { title: 'GeneralViolationSeverity' }
        },
        {
          code: 'IMA',
          path: 'severityGeneralCompare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/severityGeneral/compare'),
          name: 'SeverityGeneralCompare',
          meta: { title: 'GeneralViolationSeverityCompare' }
        },
        {
          code: 'IM3',
          path: 'ruleType',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/ruleType/index'),
          name: 'RuleType',
          meta: { title: 'GeneralViolationRuleType' }
        },
        {
          code: 'IMB',
          path: 'ruleTypeCompare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/ruleType/compare'),
          name: 'GeneralRuleTypeCompare',
          meta: { title: 'GeneralViolationRuleTypeCompare' }
        },
        {
          code: 'IM4',
          path: 'divulgeModeSporadic',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/divulgeModeSporadic/index'),
          name: 'DivulgeModeSporadic',
          meta: { title: 'SporadicViolationDisclosureMode' }
        },
        {
          code: 'IMC',
          path: 'SporadicViolateLossTypeCompare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/divulgeModeSporadic/compare'),
          name: 'SporadicViolateLossTypeCompare',
          meta: { title: 'SporadicViolateLossTypeCompare' }
        },
        {
          code: 'IM5',
          path: 'severitySporadic',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/severitySporadic/index'),
          name: 'SeveritySporadic',
          meta: { title: 'SporadicViolationSeverity' }
        },
        {
          code: 'IMD',
          path: 'SporadicViolateSeverityCompare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/severitySporadic/compare'),
          name: 'SporadicViolateSeverityCompare',
          meta: { title: 'SporadicViolationSeverityCompare' }
        },
        {
          code: 'IM6',
          path: 'fullScan',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/fullScan/index'),
          name: 'FullScan',
          meta: { title: 'fullScanFileReport' }
        },
        {
          code: 'IM7',
          path: 'sensitiveContent',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/sensitiveContent/index'),
          name: 'SensitiveContent',
          meta: { title: 'sensitiveContentSymptomReports' }
        },
        {
          code: 'IM8',
          path: 'sensitiveContentCompare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/sensitiveContent/compare'),
          name: 'SensitiveContentCompare',
          meta: { title: 'SensitiveContentCompare' }
        },
        {
          code: 'IJ1',
          path: 'sensitiveKeywordLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/sensitiveKeywordLog/statistical'),
          name: 'SensitiveKeywordLogStatistical',
          meta: { title: 'SensitiveKeywordLogStatistical' }
        },
        {
          code: 'IJ2',
          path: 'sensitiveKeywordLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/sensitiveKeywordLog/trend'),
          name: 'SensitiveKeywordLogTrend',
          meta: { title: 'SensitiveKeywordLogTrend' }
        },
        {
          code: 'IJ3',
          path: 'sensitiveKeywordLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/sensitiveControl/sensitiveKeywordLog/compare'),
          name: 'SensitiveKeywordLogCompare',
          meta: { title: 'SensitiveKeywordLogCompare' }
        }
      ]
    },
    {
      path: 'approvalManagement',
      noComponent: true,
      name: 'ApprovalManagement',
      meta: {
        title: 'approvalManagementReport',
        icon: 'report7'
      },
      children: [
        {
          code: 'IE1',
          path: 'trwfeLog/statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/approvalManagement/trwfeLog/statistical'),
          name: 'TrwfeLogStatistical',
          meta: { title: 'TrwfeLogStatistical' }
        },
        {
          code: 'IE2',
          path: 'trwfeLog/trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/approvalManagement/trwfeLog/trend'),
          name: 'TrwfeLogTrend',
          meta: { title: 'TrwfeLogTrend' }
        },
        {
          code: 'IE3',
          path: 'trwfeLog/compare',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/approvalManagement/trwfeLog/compare'),
          name: 'TrwfeLogCompare',
          meta: { title: 'TrwfeLogCompare' }
        }
      ]
    },
    {
      path: 'dataTansaction',
      noComponent: true,
      name: 'DataTansaction',
      meta: {
        title: 'DataTansaction',
        icon: 'report11'
      },
      children: [
        {
          code: 'IL1',
          path: 'dataTansaction',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataTansaction/index'),
          name: 'DataTansaction',
          meta: { title: 'DataTansaction' }
        }
      ]
    },
    {
      path: 'integrateLog',
      noComponent: true,
      name: 'IntegrateLog',
      meta: {
        title: 'IntegrateLog',
        icon: 'report12'
      },
      children: [
        {
          code: 'ID1',
          path: 'statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/integrateLog/statistical'),
          name: 'IntegrateLogStatistical',
          meta: { title: 'IntegrateLogStatistical' }
        },
        {
          code: 'ID2',
          path: 'trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/integrateLog/trend'),
          name: 'IntegrateLogTrend',
          meta: { title: 'IntegrateLogTrend' }
        }
      ]
    },
    {
      path: 'custom',
      noComponent: true,
      name: 'CustomStatistical',
      meta: {
        title: 'CustomStatistical',
        icon: 'report8'
      },
      children: [
        {
          code: 'IN1',
          path: 'statistical',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/custom/statistical'),
          name: 'CustomStatistical',
          meta: { title: 'CustomStatistical' }
        },
        {
          code: 'IN2',
          path: 'trend',
          component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/custom/trend'),
          name: 'CustomTrend',
          meta: { title: 'CustomTrend' }
        }
      ]
    }
    // {
    //   path: 'dataPanorama',
    //   noComponent: true,
    //   name: 'DataPanorama',
    //   meta: {
    //     title: 'dataPanorama',
    //     icon: 'report5'
    //   },
    //   children: [
    //     {
    //       code: 'IO1',
    //       path: 'dataPanorama',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataPanorama'),
    //       name: 'DataPanorama',
    //       meta: { title: 'dataPanorama' }
    //     }
    //   ]
    // }
    // {
    //   path: 'dataIncrement',
    //   noComponent: true,
    //   name: 'DataIncrement',
    //   meta: {
    //     title: 'dataIncrement',
    //     icon: 'report1'
    //   },
    //   children: [
    //     {
    //       code: 'IL5',
    //       path: 'dataIncrement',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/dataIncrement'),
    //       name: 'DataIncrement',
    //       meta: { title: 'dataIncrement' }
    //     }
    //   ]
    // }
    // {
    //   path: 'nac',
    //   noComponent: true,
    //   name: 'NAC报表',
    //   meta: {
    //     title: 'NAC报表',
    //     icon: 'report9'
    //   },
    //   children: [
    //     {
    //       code: 'NC8',
    //       path: 'illegalAccessLog',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/nac/illegalAccess/statistical'),
    //       name: 'illegalAccessReport',
    //       meta: { title: 'illegalAccessReport' }
    //     },
    //     {
    //       code: 'NC8',
    //       path: 'illegalAccessTrend',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/nac/illegalAccess/trend'),
    //       name: 'illegalAccessTrend',
    //       meta: { title: 'illegalAccessTrend' }
    //     },
    //     {
    //       code: 'NC9',
    //       path: 'guestAccessTrend',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/nac/guestAccess/trend'),
    //       name: 'guestAccess',
    //       meta: { title: 'guestAccess' }
    //     },
    //     {
    //       code: 'NCA',
    //       path: 'userAccessTrend',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/nac/userAccess/trend'),
    //       name: 'userAccess',
    //       meta: { title: 'userAccess' }
    //     },
    //     {
    //       code: 'NCB',
    //       path: 'deviceAccessTrend',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/nac/deviceAccess/trend'),
    //       name: 'deviceAccess',
    //       meta: { title: 'deviceAccess' }
    //     }
    //   ]
    // }
    // {
    //   path: 'overtimeAnalysis',
    //   noComponent: true,
    //   name: 'overtimeAnalysis',
    //   meta: {
    //     title: '加班分析',
    //     icon: 'chart'
    //   },
    //   children: [
    //     {
    //       code: 'I11',
    //       path: 'employeeOvertime',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/overtimeAnalysis/employeeOvertime'),
    //       name: 'EmployeeOvertime',
    //       meta: { title: '加班分析' }
    //     }
    //   ]
    // },
    // {
    //   path: 'terminalNotOffline',
    //   noComponent: true,
    //   name: 'terminalNotOffline',
    //   meta: {
    //     title: '终端未关机报表',
    //     icon: 'chart'
    //   },
    //   children: [
    //     {
    //       code: 'I11',
    //       path: 'notOffline',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalNotOffline/notOffline'),
    //       name: 'NotOffline',
    //       meta: { title: '终端未关机报表' }
    //     }
    //   ]
    // },
    // {
    //   path: 'encOrDec',
    //   noComponent: true,
    //   name: 'EncOrDec',
    //   meta: {
    //     title: 'EncOrDec',
    //     icon: 'chart'
    //   },
    //   children: [
    //     {
    //       code: 'I11',
    //       path: 'statistical',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/encOrDec/statistical'),
    //       name: 'EncOrDecStatistical',
    //       meta: { title: 'EncOrDecStatistical' }
    //     },
    //     {
    //       code: 'I12',
    //       path: 'trend',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/encOrDec/trend'),
    //       name: 'EncOrDecTrend',
    //       meta: { title: 'EncOrDecTrend' }
    //     }
    //     // ,{
    //     //   code: 'I12',
    //     //   path: 'compare',
    //     //   component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/encOrDec/compare'),
    //     //   name: 'EncOrDecCompare',
    //     //   meta: { title: '批量加解密对比趋势图' }
    //     // }
    //   ]
    // },
    // {
    //   path: 'scanViolateLog',
    //   noComponent: true,
    //   name: 'ScanViolateLog',
    //   meta: {
    //     title: 'ScanViolateLog',
    //     icon: 'chart'
    //   },
    //   children: [
    //     {
    //       code: 'I91',
    //       path: 'statistical',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/scanViolateLog/statistical'),
    //       name: 'ScanViolateStatistical',
    //       meta: { title: 'ScanViolateStatistical' }
    //     },
    //     {
    //       code: 'I92',
    //       path: 'trend',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/scanViolateLog/trend'),
    //       name: 'ScanViolateTrend',
    //       meta: { title: 'ScanViolateTrend' }
    //     }
    //   ]
    // },
    // {
    //   path: 'computerWorkOvertime',
    //   noComponent: true,
    //   name: 'ComputerWorkOvertime',
    //   meta: {
    //     title: 'ComputerWorkOvertime',
    //     icon: 'chart'
    //   },
    //   children: [
    //     {
    //       code: 'IK1',
    //       path: 'statistical',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/computerWorkOvertime/statistical'),
    //       name: 'ComputerWorkOvertimeStatistical',
    //       meta: { title: 'ComputerWorkOvertimeStatistical' }
    //     },
    //     {
    //       code: 'IK2',
    //       path: 'trend',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/computerWorkOvertime/trend'),
    //       name: 'ComputerWorkOvertimeTrend',
    //       meta: { title: 'ComputerWorkOvertimeTrend' }
    //     },
    //     {
    //       code: 'IK3',
    //       path: 'ComputerWorkTimeStatistical',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/computerWorkTime/statistical'),
    //       name: 'ComputerWorkTimeStatistical',
    //       meta: { title: 'ComputerWorkTimeStatistical' }
    //     },
    //     {
    //       code: 'IK4',
    //       path: 'ComputerWorkTimeTrend',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/computerWorkTime/trend'),
    //       name: 'ComputerWorkTimeTrend',
    //       meta: { title: 'ComputerWorkTimeTrend' }
    //     }
    //   ]
    // },
    // {
    //   path: 'terminalNotOffline',
    //   noComponent: true,
    //   name: 'terminalNotOffline',
    //   meta: {
    //     title: '终端未下线报表',
    //     icon: 'chart'
    //   },
    //   children: [
    //     {
    //       code: 'I11',
    //       path: 'notOffline',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/terminalNotOffline/notOffline'),
    //       name: 'NotOffline',
    //       meta: { title: '终端未下线报表' }
    //     }
    //   ]
    // },
    // {
    //   path: 'overtimeAnalysis',
    //   noComponent: true,
    //   name: 'overtimeAnalysis',
    //   meta: {
    //     title: '加班分析',
    //     icon: 'chart'
    //   },
    //   children: [
    //     {
    //       code: 'I11',
    //       path: 'employeeOvertime',
    //       component: () => import(/* webpackChunkName: 'reportModule' */ '@/views/report/overtimeAnalysis/employeeOvertime'),
    //       name: 'EmployeeOvertime',
    //       meta: { title: '加班分析' }
    //     }
    //   ]
    // }
  ]
}
// 标准征兆报表自定义菜单

// console.log('reportRouter', JSON.parse(JSON.stringify(reportRouter)))
export default reportRouter
