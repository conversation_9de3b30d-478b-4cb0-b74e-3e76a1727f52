/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
**/

import Layout from '@/layout'

const systemRouter = {
  path: '/system',
  component: Layout,
  name: 'System',
  meta: {
    title: 'system'
  },
  // hidden: true,
  children: [
    {
      path: 'organizational',
      noComponent: true,
      name: 'Organizational',
      meta: {
        title: 'organizational',
        icon: 'permission'
      },
      children: [
        {
          code: 'A11',
          path: 'role',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/organizational/role'),
          name: 'Role',
          meta: { title: 'role' }
        },
        {
          code: 'A13',
          path: 'sysUser',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/organizational/sysUser'),
          name: 'Administrator',
          meta: { title: 'administrator' }
        },
        {
          code: 'A15',
          path: 'opLog',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/organizational/opLog'),
          name: 'OpLog',
          meta: { title: 'opLog' }
        }
      ]
    },
    {
      path: 'systemRegistry',
      noComponent: true,
      name: 'SystemRegistry',
      meta: {
        title: 'systemRegistry',
        icon: 'register'
      },
      children: [
        {
          hidden: true,
          code: 'A21',
          path: 'register',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/register'),
          name: 'Register',
          meta: { title: 'register' }
        },
        {
          code: 'A22',
          path: 'moduleConfig',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/terminalManage/moduleConfig'),
          name: 'ModuleConfig',
          meta: { title: 'moduleConfig' }
        },
        {
          code: 'A23',
          path: 'moduleFilter',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/terminalManage/moduleFilter'),
          name: 'ModuleFilter',
          meta: { title: 'moduleFilter' }
        }
      ]
    },
    {
      path: 'structure',
      noComponent: true,
      name: 'Structure',
      meta: {
        title: 'structure',
        icon: 'organizational'
      },
      children: [
        {
          code: 'A31',
          path: 'department',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/terminalManage/department'),
          name: 'Department',
          meta: { title: 'department' }
        },
        {
          code: 'A32',
          path: 'dataSourceConfig',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/dataSourceConfig'),
          name: 'DataSourceConfig',
          meta: { title: 'dataSourceConfig' }
        },
        {
          code: 'A33',
          path: 'syncExtAppConfig',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/syncExtAppConfig'),
          name: 'SyncExtAppConfig',
          meta: { title: 'syncExtAppConfig' }
        }
      ]
    },
    {
      path: 'deviceManage',
      noComponent: true,
      name: 'DeviceManage',
      meta: {
        title: 'deviceManage',
        icon: 'deviceManage'
      },
      children: [
        {
          hidden: false,
          code: 'A4B',
          path: 'serverInfo',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/serverInfo'),
          name: 'serverInfo',
          meta: { title: 'serverInfo' }
        },
        {
          hidden: false,
          code: 'A4M',
          path: 'serverAccessApproval',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/serverAccessApproval/index'),
          name: 'serverAccessApproval',
          meta: { title: 'serverAccessApproval' }
        },
        {
          code: 'A42',
          path: 'daqServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/daqServer'),
          name: 'DAQServer',
          meta: { title: 'daqServer' }
        },
        {
          code: 'A43',
          path: 'backupServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/backupServer'),
          name: 'BackupServer',
          meta: { title: 'backupServer' }
        },
        {
          code: 'A44',
          path: 'applySecurityAccess',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/dataEncryption/encryption/applySecurityAccess'),
          name: 'ApplySecurityAccess',
          meta: { title: 'applySecurityAccess' },
          prepare: true
        },
        {
          code: 'A45',
          path: 'approvalServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/approvalServer'),
          name: 'approvalServer',
          meta: { title: 'approvalServer' }
        },
        {
          code: 'A4A',
          path: 'reportServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/reportServer'),
          name: 'reportServer',
          meta: { title: 'reportServer' }
        },
        {
          code: 'A46',
          path: 'DBServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/dbServer'),
          name: 'DBServer',
          meta: { title: 'DBServer' }
        },
        {
          code: 'A48',
          path: 'DetectionServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/detectionServer'),
          name: 'DetectionServer',
          meta: { title: 'DetectionServer' }
        },
        {
          code: 'A4C',
          path: 'docTrackServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/docTrackServer'),
          name: 'docTrackServer',
          meta: { title: 'docTrackServer' }
        },
        // 此功能Q4版本不上暂时隐藏
        {
          code: 'A4F',
          path: 'nacServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/nacServer'),
          name: 'nacServer',
          meta: { title: 'nacServer' }
        },
        // {
        //   code: 'A4E',
        //   path: 'serverAlarm',
        //   component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/serverAlarm'),
        //   name: 'serverAlarm',
        //   meta: { title: 'serverAlarm' }
        // },

        // D3版本不上云平台服务器
        {
          code: 'A4H',
          path: 'cloudServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/cloudServer'),
          name: 'cloudServer',
          meta: { title: 'cloudServer' }
        },
        {
          code: 'A4K',
          path: 'softwareServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/softwareServer'),
          name: 'softwareServer',
          meta: { title: 'softwareServer' }
        },
        // 服务器日志迁移到服务器状态下
        // {
        //   code: 'A4D',
        //   path: 'serverLog',
        //   component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/serverLog/logs'),
        //   name: 'ServerLog',
        //   meta: { title: 'ServerLog' }
        // },
        {
          hidden: true,
          code: 'A47',
          path: 'spaceConfig',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/spaceConfig'),
          name: 'SpaceConfig',
          meta: { title: 'spaceConfig' }
        },
        {
          hidden: false,
          code: 'A4L',
          path: 'smartBackupServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/smartBackupServer'),
          name: 'SmartBackupServer',
          meta: { title: 'intelligentBackupServer' }
        }
      ]
    },
    {
      path: 'baseData',
      noComponent: true,
      name: 'BaseData',
      meta: {
        title: 'baseData',
        icon: 'basedata'
      },
      children: [
        {
          code: 'AA0',
          path: 'alarmTemplate',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/alarmTemplate'),
          name: 'AlarmTemplate',
          meta: { title: 'alarmTemplate' }
        },
        {
          code: 'A51',
          path: 'timeInfo',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/timeInfo'),
          name: 'TimeInfo',
          meta: { title: 'timeInfo' }
        },
        {
          code: 'A52',
          path: 'mailLibrary',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/mailLibrary'),
          name: 'MailLibrary',
          meta: { title: 'mailLibrary' }
        },
        {
          code: 'A53',
          path: 'urlLibrary',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/urlLibrary'),
          name: 'UrlLibrary',
          meta: { title: 'urlLibrary' }
        },
        // {
        //   code: 'A57',
        //   path: 'appLibrary',
        //   component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/appLibrary'),
        //   name: 'AppLibrary',
        //   meta: { title: '程序信息库' }
        // },
        {
          code: 'A57',
          path: 'serverLibrary',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/serverLibrary'),
          name: 'ServerLibrary',
          meta: { title: 'serverLibrary', stgCode: 56 }
        },
        {
          code: 'A5B',
          path: 'usbDevice',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/usbDevice'),
          name: 'UsbLibrary',
          meta: { title: 'usbLibrary' }
        },
        {
          code: 'A5P',
          path: 'printerLibrary',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/printerLibrary'),
          name: 'PrinterLibrary',
          meta: { title: 'printerLibrary' }
        },
        {
          code: 'A5H',
          path: 'wifiLibrary',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/wifiLibrary'),
          name: 'WifiLibrary',
          meta: { title: 'wifiLibrary' }
        },
        {
          code: 'A58',
          path: 'appGroup',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/appGroup'),
          name: 'AppGroup',
          meta: { title: 'appGroup' }
        },
        {
          code: 'A5G',
          path: 'fileSuffixLib',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/fileSuffixLib'),
          name: 'FileSuffixLib',
          meta: { title: 'fileSuffixLib' }
        },
        {
          code: 'A5A',
          path: 'waterMarkLib',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/waterMarkLib'),
          name: 'WaterMarkLib',
          meta: { title: 'waterMarkLib' }
        },
        {
          code: 'A5C',
          path: 'alarmSetup',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/alarmSetup'),
          name: 'AlarmSetup',
          meta: { title: 'alarmSetup', stgCode: 126 }
        },
        {
          code: 'A5I',
          path: 'backupRule',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/backupRule'),
          name: 'BackupRule',
          meta: { title: 'backupRule' }
        },
        // {
        //   code: 'A5D',
        //   path: 'outgoingTemplate',
        //   component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/outgoingTemplate'),
        //   name: 'OutgoingTemplate',
        //   meta: { title: 'outgoingTemplate' }
        // },
        {
          code: 'A5F',
          path: 'usbApprovalAccessLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/terminal/usbApprovalAccessLog'),
          name: 'UsbApprovalAccessLog',
          meta: { title: 'usbApprovalAccessLog', isLog: true }
        },
        {
          code: 'A5J',
          path: 'webpageMatchRule',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/webpageMatchRule'),
          name: 'WebpageMatchRule',
          meta: { title: 'webpageMatchRuleLib' }
        },
        {
          code: 'A5L',
          path: 'appUserRobot',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/appUser'),
          name: 'appUserRobot',
          meta: { title: 'appUserManager' }
        }
      ]
    },
    // Q1暂不上消息推送功能
    // {
    //   path: 'messageNotify',
    //   noComponent: true,
    //   name: 'MessageNotify',
    //   meta: {
    //     title: 'messageNotify',
    //     icon: 'messageNotify'
    //   },
    //   children: [
    //     {
    //       code: 'A71',
    //       path: 'userBind',
    //       component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/messageNotification/userBind'),
    //       name: 'UserBind',
    //       meta: { title: 'userBind' }
    //     },
    //     {
    //       code: 'A72',
    //       path: 'messageConfig',
    //       component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/messageNotification/messageConfig'),
    //       name: 'MessageConfig',
    //       meta: { title: 'messageConfig' }
    //     },
    //     {
    //       code: 'A74',
    //       path: 'customizeReport',
    //       component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/messageNotification/customizeReport'),
    //       name: 'CustomizeReport',
    //       meta: { title: 'customizeReport' }
    //     },
    //     {
    //       code: 'A73',
    //       path: 'pushingRecord',
    //       component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/messageNotification/pushingRecord'),
    //       name: 'PushingRecord',
    //       meta: { title: 'pushingRecord' }
    //     }
    //   ]
    // },
    {
      path: 'configManage',
      noComponent: true,
      name: 'ConfigManage',
      meta: {
        title: 'configManage',
        icon: 'configManage'
      },
      children: [
        {
          code: 'A68',
          path: 'globalConfig',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/globalConfig'),
          name: 'GlobalConfig',
          meta: { title: 'globalConfig' }
        },
        {
          code: 'A49',
          path: 'MailServer',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/deviceManage/mailServer'),
          name: 'MailServer',
          meta: { title: 'MailServer' }
        },
        {
          hidden: true,
          code: 'A62',
          path: 'emailConfig',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/emailConfig'),
          name: 'EmailConfig',
          meta: { title: 'emailConfig' }
        },
        // D3版本不上规则库配置功能
        // {
        //   code: 'A79',
        //   path: 'cloudRuleConfig',
        //   component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/cloudRuleConfig'),
        //   name: 'CloudRuleConfig',
        //   meta: { title: 'cloudRuleConfig' }
        // },
        {
          code: 'A65',
          path: 'terminalConfig',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/terminalManage/terminalConfig'),
          name: 'TerminalConfig',
          meta: { title: 'terminalConfig' },
          prepare: true
        },
        {
          code: 'A24',
          path: 'terminalMenu',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/terminalManage/terminalMenu'),
          name: 'TerminalMenu',
          meta: { title: 'terminalMenu' },
          prepare: true
        },
        // {
        //   code: 'A66',
        //   path: 'otherConfig',
        //   component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/otherConfig'),
        //   name: 'OtherConfig',
        //   meta: { title: '其它设置' }
        // },
        {
          hidden: true,
          code: 'A67',
          path: 'popUpConfig',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/popUpConfig'),
          name: 'PopUpConfig',
          meta: { title: 'popUpConfig' }
        },
        {
          code: 'A69',
          path: 'bossCode',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/terminalManage/bossCode'),
          name: 'BossCode',
          meta: { title: 'bossCode' }
        },
        {
          code: 'A80',
          path: 'monitorMailPortConfig',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/monitorMailPortConfig'),
          name: 'MonitorMailPortConfig',
          meta: { title: 'monitorMailPortConfig' }
        },
        // {
        //   code: 'A59',
        //   path: 'fileOutgoingCheckStatisticsConfig',
        //   component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/configManage/fileOutgoingCheckStatisticsConfig'),
        //   name: 'FileOutgoingCheckStatisticsConfig',
        //   meta: { title: 'fileOutgoingCheckStatisticsConfig' }
        // },
        {
          code: 'A81',
          path: 'domainAccount',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/terminalManage/domainAccount'),
          name: 'DomainAccount',
          meta: { title: 'domainAccount' }
        }
      ]
    },
    {
      path: 'pkgManage',
      noComponent: true,
      name: 'PkgManage',
      meta: {
        title: 'installPkgManage',
        icon: 'configManage'
      },
      children: [
        {
          code: 'A82',
          path: 'installPkgCode',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/installPkgManage/installPkgCode'),
          name: 'InstallPkgCode',
          meta: { title: 'installPkgCode' }
        },
        {
          code: 'A83',
          path: 'installPkgMake',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/installPkgManage/installPkgMake'),
          name: 'InstallPkgMake',
          meta: { title: 'installPkgMake' }
        }
      ]
    }
  ]
}

export default systemRouter
