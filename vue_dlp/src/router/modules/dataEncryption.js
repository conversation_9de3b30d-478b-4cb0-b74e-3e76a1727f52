/** 当路由表太长时，可以将其拆分为小模块
 *  属性字段说明：
 *    prepare: true 创建路由时代码自动创建一条 预定义 的路径，用于预定义策略的编辑
 *    code: A11 菜单编码，总共三位数，每一位取值0-9A-Z（36进制）。只需要给叶子节点菜单配置编码
 *
 *    isLog: 是否日志模块：在销售模块中包含“日志审计”时，去除isLog=true的菜单，如果销售模块不包含，则显示
 **/

import Layout from '@/layout'

const dataEncryptionRouter = {
  path: '/dataEncryption',
  component: Layout,
  name: 'DataEncryption',
  meta: {
    title: 'dataEncryption',
    icon: 'form'
  },
  children: [
    {
      path: 'encryptionGroup',
      noComponent: true,
      name: 'EncryptionGroup',
      meta: {
        title: 'encryptionGroup',
        icon: 'behaviorGroup'
      },
      alwaysShow: true,
      children: [
        {
          code: 'E11',
          path: 'encryptionGroup',
          component: () => import(/* webpackChunkName: 'stgGroup' */ '@/views/dataEncryption/stgGroup'),
          name: 'EncryptionGroup',
          meta: { title: 'encryptionGroup' }
        }
      ]
    },
    {
      path: 'encryption',
      noComponent: true,
      name: 'encryption',
      meta: {
        title: 'encryption',
        icon: 'encryption'
      },
      children: [
        {
          code: 'E21',
          path: 'processStgLib',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/processStgLib'),
          name: 'ProcessStgLib',
          meta: { title: 'processStgLib', stgCode: 80 }
        },
        {
          hidden: true,
          code: 'E2A',
          path: 'processStgLibConfig',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/processStgLib/configTab'),
          name: 'ProcessStgLibConfig',
          meta: { title: 'processStgLibConfig' }
        },
        {
          code: 'E22',
          path: 'processStg',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/processStg'),
          name: 'ProcessStg',
          meta: { title: 'processStg' },
          prepare: true
        },
        {
          code: 'E23',
          path: 'translucentEncStrategy',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/translucentEncStrategy'),
          name: 'TranslucentEncStrategy',
          meta: { title: 'translucentEncStrategy' },
          prepare: true
        },
        {
          code: 'E24',
          path: 'smartEncStrategy',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/smartEncStrategy'),
          name: 'SmartEncStrategy',
          meta: { title: 'smartEncStrategy' },
          prepare: true
        },
        {
          hidden: true,
          code: 'E30',
          path: 'smartEncStrategySensitive',
          component: () => import(/* webpackChunkName: 'dataEncryption' */'@/views/dataEncryption/encryption/smartEncStrategy/contentStg'),
          name: 'SmartEncStrategySensitive',
          meta: { title: 'smartEncStrategySensitive' },
          query: {
            stgTypeNumber: 269,
            treeable: false
          }
        },
        {
          code: 'E25',
          path: 'encOrDecStg',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/encOrDecStg'),
          name: 'EncOrDecStg',
          meta: { title: 'encOrDecStg' },
          prepare: true
        },
        {
          code: 'E26',
          path: 'diskScan',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/diskScan/index'),
          name: 'DiskScan',
          meta: { title: 'diskScanEncDec', stgCode: 95 }
        },
        {
          hidden: true,
          code: 'E28',
          path: 'diskScanSens',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/diskScan/contentStg'),
          name: 'DiskScanSens',
          meta: { title: 'diskScanSens', parentName: 'encryption' },
          query: {
            stgTypeNumber: 125,
            treeable: false
          }
        },
        {
          code: 'E27',
          path: 'enDeFileScan',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/EnDeFileScan'),
          name: 'EnDeFileScan',
          meta: { title: 'enDeFileScan' },
          prepare: true
        }
      ]
    },
    {
      path: 'FileSecuritySet',
      noComponent: true,
      name: 'FileSecuritySet',
      meta: {
        title: 'FileSecuritySet',
        icon: 'docPemission'
      },
      children: [
        {
          code: 'E31',
          path: 'enterpriseKey',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/system/enterpriseKey'),
          name: 'EnterpriseKey',
          meta: { title: 'enterpriseKey' }
        },
        {
          code: 'E32',
          path: 'denseSet',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/docPemission/denseSet'),
          name: 'DenseSet',
          meta: { title: 'denseSet' }
        },
        {
          code: 'E33',
          path: 'userDense',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/userDense'),
          name: 'UserDense',
          meta: { title: 'userDense' },
          prepare: true
        },
        {
          code: 'E34',
          path: 'clipboard',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/clipboard'),
          name: 'Clipboard',
          meta: { title: 'clipboard' },
          prepare: true
        },
        {
          code: 'E35',
          path: 'screenshotStrategy',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/screenshotStrategy'),
          name: 'ScreenshotStrategy',
          meta: { title: 'screenshotStrategy' },
          prepare: true
        },
        {
          code: 'E36',
          path: 'specialSuffix',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/specialSuffix'),
          name: 'SpecialSuffix',
          meta: { title: 'specialSuffix' },
          prepare: true
        },
        {
          code: 'E37',
          path: 'specialPath',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/specialPath'),
          name: 'SpecialPath',
          meta: { title: 'specialPath' },
          prepare: true
        },
        {
          code: 'E3G',
          path: 'filePermissionControl',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/filePermissionControl'),
          name: 'FilePermissionControl',
          meta: { title: 'filePermissionControl' },
          prepare: true
        },
        {
          code: 'E3H',
          path: 'filePermissionControlLib',
          component: () => import(/* webpackChunkName: 'systemModule' */ '@/views/system/baseData/filePermissionControlLib'),
          name: 'FilePermissionControlLib',
          meta: { title: 'filePermissionControlLib' }
        },
        {
          code: 'E38',
          path: 'readPermission',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/readPermission'),
          name: 'ReadPermission',
          meta: { title: 'readPermission' },
          prepare: true
        },
        {
          code: 'E3A',
          path: 'workMode',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/workMode'),
          name: 'WorkMode',
          meta: { title: 'workMode', stgCode: 72 },
          prepare: true
        },
        {
          code: 'E3B',
          path: 'backUpConfig',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/backUpConfig'),
          name: 'BackUpConfig',
          meta: { title: 'backUpConfig' },
          prepare: true
        },
        {
          code: 'E3C',
          path: 'shortOfflineStrategy',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/shortOfflineStrategy'),
          name: 'ShortOfflineStrategy',
          meta: { title: 'shortOfflineStrategy' },
          prepare: true
        },
        {
          code: 'E3D',
          path: 'operatorConfig',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/operatorConfig'),
          name: 'OperatorConfig',
          meta: { title: 'operatorConfig' },
          prepare: true
        },
        {
          code: 'E3F',
          path: 'iconRefreshDir',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/iconRefreshDir'),
          name: 'iconRefreshDir',
          meta: { title: 'iconRefreshDir' },
          prepare: true
        },
        {
          code: 'E29',
          path: 'decryptionKey',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/decryptionKey'),
          name: 'DecryptionKey',
          meta: { title: 'decryptionKey' },
          prepare: true
        }
      ]
    },
    {
      path: 'FileOutgoingManage',
      noComponent: true,
      name: 'FileOutgoingManage',
      meta: {
        title: 'FileOutgoingManage',
        icon: 'docOutgo'
      },
      alwaysShow: true,
      children: [
        {
          code: 'E41',
          path: 'machineCodeWhiteList',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/mcode'),
          name: 'MachineCodeWhiteList',
          meta: { title: 'machineCodeWhiteList' },
          prepare: true
        },
        {
          code: 'E42',
          path: 'outgoingProcess',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/app'),
          name: 'OutgoingProcess',
          meta: { title: 'outgoingProcess' },
          prepare: true
        },
        {
          code: 'E59',
          path: 'softwareBlacklist',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/softwareBlacklist'),
          name: 'SoftwareBlacklist',
          meta: { title: 'softwareBlacklist' },
          prepare: true
        },
        {
          code: 'E43',
          path: 'webSite',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/webSite'),
          name: 'WebSite',
          meta: { title: 'webSite' }
        },
        {
          code: 'E44',
          path: 'screenWaterMark',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/screenWaterMark'),
          name: 'OutgoingScreenWaterMark',
          meta: { title: 'outgoingScreenWaterMark' },
          prepare: true
        },
        {
          code: 'E45',
          path: 'printWaterMark',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/printWaterMark'),
          name: 'OutgoingPrintWaterMark',
          meta: { title: 'outgoingPrintWaterMark' },
          prepare: true
        },
        {
          code: 'E46',
          path: 'fileOutgoingConfig',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/config'),
          name: 'FileOutgoingConfig',
          meta: { title: 'fileOutgoingConfig' },
          prepare: true
        },
        {
          code: 'E47',
          path: 'templateSet',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/template'),
          name: 'TemplateSet',
          meta: { title: 'templateSet', stgCode: 137 },
          prepare: true
        },
        {
          code: 'E48',
          path: 'fileOutgoingLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/fileOutgoingLog'),
          name: 'FileOutgoingLog',
          meta: { title: 'fileOutgoingLog', isLog: true }
        },
        // D3版本不上云平台外发文件管理
        // {
        //   code: 'E50',
        //   path: 'cloudOutfile',
        //   component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/cloudOutfile'),
        //   name: 'CloudOutfile',
        //   meta: { title: 'cloudOutfile' }
        // }
        {
          code: 'E50',
          path: 'dlpCloudOutFile',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/dlpCloudOutFile'),
          name: 'DlpCloudOutFile',
          meta: { title: 'dlpCloudOutFile' }
        }
        // {
        //   code: 'E49',
        //   path: 'directOutgoingWhiteList',
        //   component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/fileOutgoing/directOutgoingWhilteList'),
        //   name: 'DirectOutgoingWhiteList',
        //   meta: { title: 'directOutgoingWhiteList' },
        //   prepare: true
        // }
      ]
    },
    {
      path: 'SecurityAudit',
      noComponent: true,
      name: 'SecurityAudit',
      meta: {
        title: 'SecurityAudit',
        icon: 'securityAudit'
      },
      children: [
        {
          code: 'E51',
          path: 'secretLevelLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/secretLevelLog'),
          name: 'SecretLevelLog',
          meta: { title: 'secretLevelLog', isLog: true }
        },
        {
          code: 'E52',
          path: 'diskScanLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/diskScanLog'),
          name: 'DiskScanLog',
          meta: { title: 'diskScanLog', parentName: 'encryption', isLog: true }
        },
        {
          code: 'E53',
          path: 'encOrDecLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/encOrDecLog'),
          name: 'EncOrDecLog',
          meta: { title: 'encOrDecLog', isLog: true }
        },
        {
          code: 'E54',
          path: 'fileBackupLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/fileBackupLog'),
          name: 'FileBackupLog',
          meta: { title: 'fileBackupLog', isLog: true }
        },
        {
          code: 'E55',
          path: 'approvalLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/approvalLog'),
          name: 'ApprovalLog',
          meta: { title: 'approvalLog', isLog: true }
        },
        {
          code: 'E56',
          path: 'enDeFileLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/encryption/EnDeFileLog'),
          name: 'EnDeFileLog',
          meta: { title: 'enDeFileLog', isLog: true }
        }
      ]
    },
    {
      path: 'whiteList',
      noComponent: true,
      name: 'whiteList',
      meta: {
        title: 'whiteList',
        icon: 'whiteList'
      },
      children: [
        {
          code: 'E61',
          path: 'mailWhiteList',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/mailWhiteList'),
          name: 'MailWhiteList',
          meta: { title: 'mailWhiteList', stgCode: 60 },
          prepare: true
        },
        {
          code: 'E62',
          path: 'httpWhiteList',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/httpWhiteList'),
          name: 'HttpWhiteList',
          meta: { title: 'httpWhiteList' },
          prepare: true
        }
      ]
    },
    {
      path: 'fileTrace',
      noComponent: true,
      name: 'FileTrace',
      meta: {
        title: 'fileTrace',
        icon: 'fileTrace'
      },
      children: [
        {
          code: 'E71',
          path: 'documentTrack',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/fileTrace/documentTrack'),
          name: 'documentTrack',
          meta: { title: 'documentTrack', stgCode: 213 },
          prepare: true
        },
        {
          code: 'E74',
          path: 'blindWatermark',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/fileTrace/blindWatermark'),
          name: 'blindWatermark',
          meta: { title: 'blindWatermark' },
          prepare: true
        },
        {
          code: 'E3E',
          path: 'OfficeWaterMark',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/encryption/officeWaterMark'),
          name: 'OfficeWaterMark',
          meta: { title: 'OfficeWaterMark' },
          prepare: true
        }
      ]
    },
    {
      path: 'smartBackup',
      noComponent: true,
      name: 'SmartBackup',
      meta: {
        title: 'intelligentBackup',
        icon: 'smartBackup'
      },
      children: [
        {
          code: 'E81',
          path: 'timelyBackup',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/backup/timelyBackup'),
          name: 'TimelyBackup',
          meta: { title: 'timelyBackup' },
          prepare: true
        },
        {
          code: 'E82',
          path: 'fullDiskScanBackup',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/backup/fullDiskScanBackup'),
          name: 'FullDiskScanBackup',
          meta: { title: 'fullDiskScanBackup' },
          prepare: true
        },
        {
          code: 'E83',
          path: 'serverBackupConfig',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/backup/serverBackupConfig'),
          name: 'ServerBackupConfig',
          meta: { title: 'serverBackupConfig' },
          prepare: true
        },
        {
          code: 'E84',
          path: 'backupRepository',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/backup/backupRepository'),
          name: 'SmartBackupRepository',
          meta: { title: 'backupRepository' },
          prepare: true
        },
        {
          code: 'E85',
          path: 'highConfig',
          component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/dataEncryption/backup/highConfig'),
          name: 'BackupHighConfig',
          meta: { title: 'highConfig' },
          prepare: true
        }
      ]
    },
    {
      path: 'DocumentLabel',
      noComponent: true,
      name: 'DocumentLabel',
      meta: {
        title: 'documentLabel',
        icon: 'docLabel'
      },
      children: [
        {
          code: 'C96',
          path: 'manualLabel',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/manualLabel'),
          name: 'ManualLabel',
          meta: { title: 'manualLabel' },
          prepare: true
        },
        {
          code: 'C97',
          path: 'landLabeling',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/landLabeling'),
          name: 'LandLabeling',
          meta: { title: 'landLabeling' },
          prepare: true
        },
        {
          code: 'C98',
          path: 'labelPermissionControl',
          component: () => import(/* webpackChunkName: 'behaviorModule' */ '@/views/behaviorManage/hardware/labelPermissionControl'),
          name: 'LabelPermissionControl',
          meta: { title: 'labelPermissionControl' },
          prepare: true
        },
        {
          code: 'C99',
          path: 'baseLabelConfig',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/behaviorManage/hardware/baseLabelConfig'),
          name: 'BaseLabelConfig',
          meta: { title: 'baseLabelConfig' },
          prepare: true
        },
        {
          code: 'C90',
          path: 'tagingLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/tagingLog'),
          name: 'tagingLog',
          meta: { title: 'tagingLog', isLog: true }
        },
        {
          code: 'C9A',
          path: 'diskScan',
          component: () => import(/* webpackChunkName: 'contentModule' */ '@/views/behaviorManage/hardware/labelDiskScan'),
          name: 'DiskScanTag',
          meta: { title: 'diskScanTag', stgCode: 95 }
        },
        {
          code: 'C9C',
          path: 'tagFileOutSendLog',
          component: () => import(/* webpackChunkName: 'auditingModule' */ '@/views/behaviorAuditing/network/tagFileOutSendLog'),
          name: 'TagFileOutSendLog', // Tip: 命名规则请查看文件首行注释
          meta: { title: 'tagFileOutSendLog', isLog: true }
        }
      ]
    }
    // ,
    // {
    //   hidden: true,
    //   path: 'docPemission',
    //   noComponent: true,
    //   name: 'docPemission',
    //   meta: {
    //     title: '文档权限管理',
    //     icon: 'docPemission'
    //   },
    //   children: [
    //     {
    //       hidden: true,
    //       path: 'path2-3-1',
    //       component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/nested/menu2/index'), // Parent router-view
    //       name: 'path2-3-1',
    //       meta: { title: '部门隔离管理' }
    //     },
    //     {
    //       hidden: true,
    //       path: 'path2-3-3',
    //       component: () => import(/* webpackChunkName: 'dataEncryption' */ '@/views/nested/menu2/index'),
    //       name: 'path2-3-3',
    //       meta: { title: '审批管理' }
    //     }
    //   ]
    // }
  ]
}

export default dataEncryptionRouter
