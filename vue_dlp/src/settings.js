// 前端系统配置，在这边修改不需要重启环境
// 使用方式，可直接 import 该文件，或通过 store 使用 (1. store.getters.xxx，需添加到getters.js 2. store.state.settings.xxx)

module.exports = {
  /**
   * @type {string} 天锐：tipray | 中性：neutral
   * @description 当后台没有返回资源包时，控制台显示的默认版本
   */
  version: 'tipray',

  /**
   * @type {boolean} true | false
   * @description 是否显示菜单标签导航 tagsView
   */
  tagsView: true,

  /**
   * @type {boolean} true | false
   * @description 开发模式下，是否显示多语言切换功能
   */
  devI18nMode: true,

  /**
   * @type {boolean} true | false
   * @description 开放所有菜单权限，用于测试环境，默认为false
   */
  allPermission: false,

  /**
   * @type {boolean} true | false
   * @description 开发模式下，通过tab标签关闭控制台时，是否退出登录
   */
  logoutWhenClose: false,

  /**
   * @type {boolean} true | false
   * @description 在调用i18n方法时，给组件添加上属性 data-i18nKey
   */
  addI18nKey: false,

  /**
   * @type {boolean} true | false
   * @description 是否使用公共的策略应用树组件StrategyTargetTree
   */
  commonStrategyTargetTree: true,

  /**
   * @type {boolean} true | false
   * @description 当使用公共的策略应用树组件时，是否缓存每个页面选中的树节点, commonStrategyTargetTree 为 true 时生效
   */
  cacheSelectedNode: true
}
