<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('layout.registerInfo')"
      :visible.sync="dialogVisible"
      width="600px"
      custom-class="registerInfo"
    >
      <p>
        {{ title }}<br/>
        {{ $t('layout.softwareVersion') }}<label :title="registerInfo.webVersion" style="font-weight: 100;">{{ registerInfo.webMainVersion }}</label><br/>
        {{ copyright }}<br v-if="!!copyright"/>
        <el-link v-if="website" type="primary" :href="website" target="_blank">{{ website }}</el-link><br v-if="!!website"/>
        <span v-if="hotline">{{ $t('layout.websiteHotline') }}：{{ hotline }}</span>
      </p>
      <div class="info">
        <ul>
          <li>{{ $t('layout.userNo') }}{{ registerInfo.userNo }}</li>
          <li>{{ $t('layout.userName') }}{{ registerInfo.companyInfo }}</li>
          <li>{{ $t('layout.userLicense') }}{{ userCountinFormatter(registerInfo, registerInfo.userCountin) }}</li>
          <li v-if="registerInfo.useDate">{{ $t('table.useLimit') }}：{{ timeFarmatter(registerInfo, registerInfo.useDate) }}</li>
          <li>{{ $t('table.upgradeLimit') }}：{{ timeFarmatter(registerInfo, registerInfo.updateData) }}</li>
        </ul>
      </div>
      <div v-if="showRegBtn" class="dialog-footer">
        <el-button type="primary" @click="handleRegister">{{ $t('layout.register') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.close') }}</el-button>
      </div>
      <fieldset>
        <legend>{{ $t('layout.productInfo') }}</legend>
        <div class="table-con">
          <grid-table
            ref="infoTable"
            :height="120"
            :row-datas="tableData"
            :multi-select="false"
            :show-pager="false"
            :col-model="colModel"
          />
        </div>
      </fieldset>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('layout.productDetail')"
      :visible.sync="detailsVisible"
      width="600px"
      custom-class="registerInfo"
    >
      <div class="info">
        <ul>
          <li>{{ $t('layout.softwareName') }}{{ productNameFormatter(detailInfo, detailInfo.productName) }}</li>
          <li>{{ $t('layout.userLicense') }}{{ userCountinFormatter(detailInfo, detailInfo.userCountin) }}</li>
          <li>{{ $t('layout.upgradePeriod') }}{{ timeFarmatter(detailInfo, detailInfo.updateData) }}</li>
        </ul>
      </div>
      <fieldset style="margin-top:20px">
        <legend>{{ $t('layout.moduleInfo') }}</legend>
        <el-button type="primary" icon="el-icon-search" size="mini" style="margin-left: 5px;float: right;" @click="handleFilter">{{ $t('table.search') }}</el-button>
        <el-input v-model="searchInfo" v-trim clearable :placeholder="$t('pages.inputModuleName')" style="width: 200px;float: right;" @keyup.enter.native="handleFilter"></el-input>
        <div class="table-con" style="margin-top: 5px;">
          <el-table
            :data="moduleInfos"
            height="200"
            border
            size="mini"
            style="width: 100%"
          >
            <el-table-column
              prop="moduleName"
              :label="$t('layout.moduleName')"
              min-width="100"
              :resizable="false"
            >
              <template v-slot="{ row, row: { osGroup } }">
                <span :title="iconTitle[osGroup] + row.moduleName">
                  <svg-icon :icon-class="iconClass[osGroup]" />
                </span>
                {{ row.moduleName }}
              </template>
            </el-table-column>
            <el-table-column
              prop="numbers"
              :label="$t('layout.license')"
              min-width="100"
              :resizable="false"
            >
            </el-table-column>
          </el-table>
        </div>
      </fieldset>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailsVisible = false;searchInfo =''">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>

    <register-dialog ref="registerDialog"/>
  </div>
</template>

<script>
import { getInfo, getModuleInfoByProdId, regEnable } from '@/api/system/register/reg'
import { parseTime, isEmpty } from '@/utils'
import { getSystemResources } from '@/utils/i18n'
import RegisterDialog from '@/views/register/registerDialog'

export default {
  name: 'RegisterDetailDlg',
  components: { RegisterDialog },
  data() {
    return {
      dialogVisible: false,
      detailsVisible: false,
      showRegBtn: false, // 显示注册按钮
      colModel: [
        { prop: 'productName', label: 'softwareName', width: '130', formatter: this.productNameFormatter },
        { prop: 'userCountin', label: 'userLicense', width: '60', formatter: this.userCountinFormatter },
        { prop: 'updateData', label: 'upgradeLimit', width: '120', formatter: this.timeFarmatter },
        { label: 'operate', type: 'button', fixedWidth: '60',
          buttons: [
            { label: 'view', click: this.handleDetails }
          ]
        }
      ],
      tableData: [],
      iconClass: {
        'win': 'windows',
        'linux': 'linux',
        'mac': 'mac',
        'phone': 'phone',
        '': 'basedata'
      },
      iconTitle: {
        'win': 'WINDOWS_',
        'linux': 'LINUX_',
        'mac': 'MAC_',
        'phone': 'PHONE_',
        '': ''
      },
      serialForm: {
        serialNo: '',
        checked: false
      },
      moduleInfos: [],
      allModuleInfos: [],
      registerInfo: {
        serNo: '',
        userNo: '',
        companyInfo: '',
        userCountin: '',
        webVersion: '',
        webMainVersion: ''
      },
      unLimitCode: 9999, // 不限制的编码
      detailInfo: {},
      productNameMap: {
        511: this.$t('layout.product_511')
      },
      searchInfo: '',
      prodId: ''
    }
  },
  computed: {
    title() {
      return getSystemResources('title')
    },
    copyright() {
      return getSystemResources('copyright')
    },
    website() {
      return getSystemResources('website')
    },
    hotline() {
      return getSystemResources('hotline')
    }
  },
  created() {
    this.enableReg()
  },
  methods: {
    handleFilter() {
      const searchInfo = this.searchInfo.toLowerCase()
      this.moduleInfos = this.allModuleInfos.filter(item => item.moduleName && item.moduleName.toLowerCase().includes(searchInfo))
    },
    register() {
      this.dialogVisible = true
      this.$refs.registerDialog.resetTemp()
      getInfo().then(response => {
        const data = response.data
        if (data) {
          const regMap = {}
          data.forEach(item => {
            regMap[item.productId] = item
            if ((item.productId + '').startsWith('9')) {
              this.registerInfo = item
            }
          })
          this.$refs.registerDialog.serialForm.serialNo = this.registerInfo.serNo
          this.tableData = regMap[511] ? [regMap[511]] : []
          this.productNameMap[511] = this.title
        }
      })
    },
    handleDetails(row) {
      this.prodId = row.productId
      getModuleInfoByProdId(row.productId).then(resp => {
        this.detailsVisible = true
        this.detailInfo = row
        this.moduleInfos = resp.data.filter(item => !isEmpty(item.moduleName))
        this.allModuleInfos = this.moduleInfos
      })
    },
    handleRegister() {
      this.$refs.registerDialog.serialForm.serialNo = this.registerInfo.serNo
      this.$refs.registerDialog.handleRegister()
    },
    enableReg() {
      regEnable().then(respond => {
        this.showRegBtn = respond.data
      })
    },
    timeFarmatter(row, data) {
      return this.$t('layout.expiresDate', { date: parseTime(data, 'y-m-d') })
    },
    productNameFormatter(row, data) {
      return this.productNameMap[row.productId] ? this.productNameMap[row.productId] : row.productName
    },
    userCountinFormatter(row, data) {
      return data == this.unLimitCode ? this.$t('layout.unLimit') : data
    }
  }
}
</script>

<style lang="scss">
.table-con{
  width: 537px;
}
.registerInfo{
  .el-dialog__body{
    line-height: 25px;
    p{
      margin: 0;
    }
    .info{
      margin-top: 10px;
      border: 1px solid #999;
      ul{
        list-style: none;
        margin: 10px 0 10px 20px;
        padding: 0;
      }
    }
    .dialog-footer{
      margin-top: 10px;
      float:right;
    }
    fieldset {
      width: 100%;
      border-width: 1px;
      border-color:#ddd;
      margin: 0;
    }
  }
}
</style>
