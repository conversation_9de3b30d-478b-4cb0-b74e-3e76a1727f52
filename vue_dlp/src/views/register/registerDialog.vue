<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.inputSerialNumber')"
      :visible.sync="registerVisible"
      width="600px"
      custom-class="registerDialog"
    >
      <el-tabs v-model="activeName" type="border-card" size="mini" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.serialNumberReg')" name="serialNumber" style="height: 200px;">
          <fieldset>
            <legend>{{ $t('layout.register') }}</legend>
            <p style="margin-left:10px">{{ $t('pages.inputSerialNumberRegOriginal') }}</p>
            <Form ref="dataForm" :rules="serialRules" :model="serialForm" label-position="right" label-width="100px" size="mini">
              <FormItem :label="$t('pages.serialNumber')" prop="serialNo">
                <el-input v-model="serialForm.serialNo" maxlength=""/>
              </FormItem>
            </Form>
          </fieldset>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.useLicenseFileRegistration')" name="licenseFile" style="height: 200px;">
          <Form>
            <FormItem style="margin-bottom: 0px;">
              <label>{{ $t('pages.serverMcode') }}</label>
              <el-tooltip class="item" effect="dark" placement="top-start">
                <div slot="content">
                  <i18n path="pages.regMessage_6">
                    <br slot="br" />
                  </i18n>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <span> {{ ':' + mcode }} </span>
            </FormItem>
            <FormItem :label="$t('pages.pleaseSelectLicenseFile')">
              <el-upload
                ref="upload"
                action="regByFile"
                list-type="text"
                style="display: inline-block;"
                :limit="1"
                :on-exceed="onExceed"
                :auto-upload="false"
                accept=".lic"
              >
                <el-button size="small" type="primary">{{ $t('pages.selectFile') }}</el-button>
              </el-upload>
            </FormItem>
          </Form>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="!regAble" :loading="submitting" @click="prepareRegForm">
          {{ $t('layout.register') }}
        </el-button>
        <el-button @click="registerVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('layout.register')"
      :visible.sync="confirmRegVisible"
      width="500px"
    >
      <Form ref="confirmRegForm" :rules="serialRules" :model="serialForm" label-position="right" label-width="100px" size="mini">
        <input type="text" class="autocomplete">
        <input type="password" class="autocomplete">
        <FormItem :label="$t('table.account')" prop="account">
          <el-input v-model="serialForm.account" :disabled="!editAccountAble" maxlength="20" clearable />
        </FormItem>
        <FormItem :label="$t('pages.pwdInfo')" prop="password">
          <el-input v-model="serialForm.password" type="password" maxlength="20" show-password clearable class="pwd-input" />
        </FormItem>
        <FormItem>
          <div style="line-height: 18px; color: #409eff; font-size: small; font-weight: bold;">{{ $t('pages.regMessage_1') }}</div>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="submitRegForm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="confirmRegVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { regByFile, regBySerial, getMcode } from '@/api/system/register/reg'
import { aesEncode, formatAesKey } from '@/utils/encrypt'

export default {
  name: 'RegisterDialog',
  data() {
    return {
      registerVisible: false,
      confirmRegVisible: false,
      editAccountAble: true,
      regAble: true,
      activeName: 'serialNumber',
      serialForm: {
        serialNo: '',
        checked: false,
        account: undefined,
        password: undefined
      },
      serialRules: {
        serialNo: [{ required: true, message: this.$t('pages.regMessage_2') }],
        account: [{ required: true, message: this.$t('pages.regMessage_3') }],
        password: [{ required: true, message: this.$t('pages.regMessage_4') }]
      },
      submitting: false,
      mcode: ''
    }
  },
  created() {
  },
  methods: {
    tabClick(pane, event) {
      if (pane.name === 'licenseFile') {
        this.getMcode()
      }
    },
    getMcode() {
      getMcode().then(response => {
        this.$socket.subscribeToAjax(response, 'getMcode/result', (respond, handle) => {
          this.mcode = respond.data.code == 1 ? respond.data.mcode : respond.data.msg
        })
      }).catch(reason => {
        this.submitting = false
      })
    },
    resetTemp() {
      this.serialForm = {
        serialNo: '',
        checked: false,
        account: undefined,
        password: undefined
      }
    },
    handleRegister() {
      this.registerVisible = true
      this.regAble = true
      this.$nextTick(() => {
        this.$refs['upload'] && this.$refs['upload'].clearFiles()
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    prepareRegForm() {
      let validResult = false
      if (this.activeName === 'serialNumber') {
        this.$refs['dataForm'].validate(valid => {
          validResult = valid
        })
      } else {
        const uploadFiles = this.$refs.upload.uploadFiles
        if (uploadFiles.length == 0) {
          this.$message({
            message: this.$t('pages.regMessage_7'),
            type: 'warning',
            duration: 2000
          })
          return false
        }
        validResult = uploadFiles.length > 0
      }
      if (validResult) {
        this.editAccountAble = true
        this.confirmRegVisible = true
        this.serialForm.account = ''
        this.serialForm.password = ''
        const loginAccount = this.$store.getters.account
        if (loginAccount) {
          this.serialForm.account = loginAccount
          this.editAccountAble = false
        }
        this.$nextTick(() => {
          this.$refs['confirmRegForm'] && this.$refs['confirmRegForm'].clearValidate()
        })
      }
    },
    submitRegForm() {
      let confirmRegFormValidResult = false
      this.$refs['confirmRegForm'].validate((valid) => { confirmRegFormValidResult = valid })
      if (!confirmRegFormValidResult) return
      if (this.activeName === 'serialNumber') {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.submitting = true
            regBySerial(Object.assign({}, this.serialForm, { encryptProps: ['serialNo', 'password'] })).then(response => {
              this.$socket.subscribeToAjax(response, 'registerInfo/registerResult', this.regRespondCallback)
              this.regAble = false
              this.confirmRegVisible = false
              this.submitting = false
            }).catch(reason => {
              this.submitting = false
            }).finally(() => {
              this.$store.dispatch('commonData/setTrialDlp')
            })
          } else {
            return false
          }
        })
      } else {
        // 通过文件注册
        this.submitting = true
        const formData = new FormData()
        const uploadFiles = this.$refs.upload.uploadFiles
        if (!uploadFiles[0]) {
          this.submitting = false
          return false
        }
        formData.append('regFile', uploadFiles[0].raw)
        formData.append('account', this.serialForm.account)
        formData.append('password', aesEncode(this.serialForm.password, formatAesKey('tr838408', this.serialForm.account)))
        regByFile(formData).then(response => {
          this.$socket.subscribeToAjax(response, 'registerInfo/registerResult', this.regRespondCallback)
          this.confirmRegVisible = false
          this.submitting = false
        }).catch(reason => {
          this.submitting = false
        }).finally(() => {
          this.$store.dispatch('commonData/setTrialDlp')
        })
      }
    },
    regRespondCallback(respond, handle) {
      this.regAble = true
      this.registerVisible = false
      let msgType = 'info'
      let msgInfo = respond.data
      if (respond.data && respond.data.hasOwnProperty('code')) {
        const respondCode = respond.data.code
        msgType = respondCode === 0 ? 'error' : (respondCode === 1 ? 'success' : 'info')
        if (respondCode === 0) {
          const desc = this.$t(respond.data.msg)
          msgInfo = this.$t('pages.regFailure') + (respond.data.errorCode > 0 ? `[${respond.data.errorCode}]${desc ? ('：' + desc) : ''}` : '')
        } else if (respondCode === 1) {
          msgInfo = this.$t('pages.regSuccess')
        } else {
          msgInfo = this.$t('pages.unknown')
        }
      }
      this.$emit('regEnd')
      this.$notify({ title: this.$t('text.prompt'), message: msgInfo, type: msgType })
      handle.close() // 手动关闭订阅
    },
    onExceed(file, fileList) {
      alert(this.$t('pages.regMessage_5'))
    }
  }
}
</script>

<style lang='scss' scoped>
  .registerDialog{
    fieldset {
      margin: 0 10px;
      border-width: 1px;
      border-color:#ddd;
    }
  }
  .pwd-input >>>.el-input__inner{
    padding-right: 26px;
  }
</style>
