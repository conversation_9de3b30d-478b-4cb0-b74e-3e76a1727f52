<template>
  <strategy-group ref="strategyGroup" :group-id="1" :stg-type-ops="stgTypeOps" :group-name="$t('route.encryptionGroup')" />
</template>
<script>
import StrategyGroup from '@/views/strategyGroup'
import { stgTypeOps } from '@/views/common/stgTypeOps';
export default {
  name: 'EncryptionGroup',
  components: { StrategyGroup },
  data() {
    return {
      stgTypeOps: stgTypeOps.encryptionGroup
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      const { searchInfo } = to.params
      if (searchInfo) {
        const strategyGroup = vm.$refs.strategyGroup
        strategyGroup.query.searchInfo = searchInfo
        strategyGroup.handleFilter()
        to.params.searchInfo = ''
      }
    })
  }
}
</script>

