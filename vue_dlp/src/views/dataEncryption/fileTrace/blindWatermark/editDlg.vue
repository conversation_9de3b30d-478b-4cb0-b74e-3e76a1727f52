<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.blindWatermark_strategy')"
      :stg-code="212"
      :active-able="activeAble"
      :model="defaultTemp"
      :rules="rules"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <el-radio-group v-model="temp.blindWatermarkConfig.controlType" :disabled="!formable" @change="handleControlTypeChange">
          <el-radio style="margin-right: 25px" :label="1">{{ $t('pages.blindWatermark_controlType1') }}</el-radio>
          <el-radio style="margin-right: 25px" :label="2">{{ $t('pages.blindWatermark_controlType2') }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">{{ $t('pages.controlProcess_tip') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
          <el-radio :label="3">{{ $t('pages.blindWatermark_controlType3') }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">{{ $t('pages.controlProcess_tip') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
        </el-radio-group>
        <el-card v-if="temp.blindWatermarkConfig.controlType > 1" class="box-card" :body-style="{'padding': '0', 'padding-top': '5px'}">
          <div slot="header" class="clearfix">
            <el-button v-if="formable" size="small" @click="showAppImport">
              {{ $t('button.applicationLibraryImport') }}
            </el-button>
            <el-button v-if="formable" size="small" @click="handleClear">
              {{ $t('button.clear') }}
            </el-button>
          </div>
          <tag
            ref="tag"
            :list="temp.blindWatermarkProc"
            :valid-rule="processRule"
            :disabled="!formable"
            :class="{ 'process-tag-err': showErr }"
            :placeholder="$t('pages.blindWatermark_text9')"
          />
          <div v-show="showErr" class="el-form-item__error">
            {{ $t('pages.program_list_cannot_null') }}
          </div>
        </el-card>

        <FormItem class="backup-limit" label-width="0" prop="backUpLimit">
          <el-checkbox v-model="temp.isBackup" :disabled="!formable" :true-label="1" :false-label="0" @change="backupChange">
            <i18n path="pages.blindWatermark_backupLimit">
              <el-input-number slot="size" v-model="temp.backUpLimit" :disabled="!formable || !temp.isBackup" step-strictly :controls="false" :min="1" :max="102400" size="mini" style="width: 100px;"/>
            </i18n>
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content" style="width: 480px;">{{ $t('pages.blindWatermark_backupLimitTips') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
        </FormItem>
      </template>
    </stg-dialog>
    <app-select-dlg ref="appSelectDlg" @select="handleAppImport"/>
  </div>
</template>

<script>
import { createStrategy, updateStrategy } from '@/api/dataEncryption/fileTrace/blindWatermark'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'

export default {
  name: 'BlindWatermarkDlg',
  components: { AppSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        blindWatermarkConfig: {
          dataSources: 1,
          controlType: 1
        },
        isBackup: 0,
        backUpLimit: 20,
        blindWatermarkProc: []
      },
      allowList: [],
      limitList: [],
      processRule: [
        { validator: this.processNameValidator, trigger: 'blur' }
      ],
      rules: {
        backUpLimit: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ]
      },
      slotName: undefined,
      showErr: false
    }
  },
  watch: {
    'temp.blindWatermarkProc'(value) {
      if (value.length) {
        this.showErr = false
      }
    },
    'temp.isBackup'(value, old) {
      console.trace('watch temp.isBackup: ', old, ' -> ', value)
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    createStrategy,
    updateStrategy,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.$refs['stgDlg'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      const data = JSON.parse(JSON.stringify(row))
      const backUpLimit = data.blindWatermarkConfig.backUpLimit
      data.backUpLimit = backUpLimit || 20
      data.isBackup = backUpLimit === 0 ? 0 : 1
      this.$refs['stgDlg'].show(data, this.formable)
    },
    handleShow: function(row, isGenerateStrategy) {
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    formatRowData(row) {
      this.resetTemp()
      row.blindWatermarkProc = row.blindWatermarkProc.map(item => item.processName)
      if (row.blindWatermarkConfig.controlType === 2) {
        this.allowList = row.blindWatermarkProc
      } else if (row.blindWatermarkConfig.controlType === 3) {
        this.limitList = row.blindWatermarkProc
      }
    },
    formatFormData(data) {
      if (!data.isBackup) {
        data.blindWatermarkConfig.backUpLimit = 0
      } else {
        data.blindWatermarkConfig.backUpLimit = data.backUpLimit
      }
      if (data.blindWatermarkConfig.controlType <= 1) {
        data.blindWatermarkProc = []
        return
      }
      data.blindWatermarkProc = data.blindWatermarkProc.map(item => ({ processName: item }))
    },
    validateForm() {
      this.showErr = this.temp.blindWatermarkConfig.controlType > 1 && this.temp.blindWatermarkProc.length === 0
      return !this.showErr
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$refs['stgDlg'].$refs['headForm4StgDialog'].clearValidate()
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.allowList = []
      this.limitList = []
    },
    processNameValidator(rule, value, callback) {
      if (value == '*.*' || value == '.' || value == '.exe' || value == '*.exe*') {
        callback(new Error(this.$t('pages.appLogConfig_text1') + value))
      } else {
        callback()
      }
    },
    handleControlTypeChange(controlType) {
      this.showErr = false
      if (controlType === 2) {
        this.temp.blindWatermarkProc = this.allowList
      } else if (controlType === 3) {
        this.temp.blindWatermarkProc = this.limitList
      } else {
        this.temp.blindWatermarkProc = []
      }
    },
    showAppImport() {
      this.$refs['appSelectDlg'].show()
    },
    handleAppImport(selection) {
      if (selection && selection.length) {
        selection.forEach(item => {
          if (this.temp.blindWatermarkProc.indexOf(item.processName) < 0) {
            this.temp.blindWatermarkProc.push(item.processName)
          }
        })
      }
    },
    handleClear() {
      this.temp.blindWatermarkProc.splice(0)
    },
    backupChange(val) {
      if (!val) {
        const stgDlg = this.$refs['stgDlg']
        if (stgDlg) {
          const formDlg = stgDlg.$refs['dataForm4StgDialog' + this.slotName]
          formDlg && formDlg.clearValidate('backUpLimit')
        }
      }
      this.temp.isBackup = val
    }
  }
}
</script>
<style lang="scss" scoped>
  .box-card {
    >>>.el-form-item__error {
      position: relative;
      display: inline-block;
      padding-left: 10px;
      font-weight: normal;
    }
    >>>.input-new-tag {
      width: 140px;
    }
  }
  .process-tag-err {
    border: 1px solid #F56C6C;
    border-radius: 4px;
    padding: 1px;
  }
  .backup-limit>>>.el-form-item__content .el-form-item__error {
    margin-left: 105px;
  }
</style>
