<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="$t('table.circulationDetail1')"
      width="800px"
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="visible"
      :append-to-body="appendToBody"
    >
      <div ref="listPanel" class="list-panel" @scroll="handleScroll">
        <div
          v-for="(item, index) in originList"
          v-show="!item.hide"
          :key="index"
          class="list-item"
          :class="{ highlight: item.circulationId === detailInfo.circulationId }"
          @click="handleListClick(item, index)"
        >
          <span class="list-item-key">{{ item.circulationId }}</span>
          <!--<span class="list-item-summary" onmouseover="this.title = this.innerText">{{ timestampFormatter(item.circulationTime) }} {{ $t('pages.documentTrack_text11') }}{{ item.userName }}、{{ item.terminalName }}</span>-->
        </div>
        <div v-if="originListLoading" class="list-loading">
          <i class="el-icon-loading"></i>
          <p class="el-loading-text">{{ $t('pages.documentTrack_text12') }}</p>
        </div>
        <div v-if="hasMoreList" class="list-loading">
          <i class="el-icon-more" style="cursor: pointer" :title="$t('pages.click_to_load_more')" @click="loadMore"></i>
        </div>
      </div>
      <div class="detail-panel">
        <doc-info-item :label="$t('table.documentName')" :content="detailInfo.fileName"/>
        <doc-info-item :label="$t('table.documentId')" :content="detailInfo.documentId" copyable :copytips="$t('pages.documentTrack_text28')"/>
        <doc-info-item :label="$t('table.parentDocumentId')" :content="parentDocumentIdFormatter(detailInfo.parentDocumentId)"/>
        <doc-info-item :label="$t('table.circulationId')" :content="detailInfo.circulationId" copyable :copytips="$t('pages.documentTrack_text29')"/>
        <doc-info-item :label="$t('table.parentCirculationId')" :content="detailInfo.parentCirculationId" :invalid="detailInfo.parentCirculationIdInvalid" :errormsg="$t('pages.documentTrack_text31')"/>
        <doc-info-item :label="$t('table.userAccount')" :content="userFormatter(detailInfo.userName)"/>
        <doc-info-item :label="$t('table.userGroup')" :content="detailInfo.userGroupName"/>
        <doc-info-item :label="$t('table.terminalName')" :content="detailInfo.terminalName"/>
        <doc-info-item :label="$t('table.terminalGroup')" :content="detailInfo.terminalGroupName"/>
        <doc-info-item :label="$t('table.circulationType')" :content="parseCirculationType(detailInfo.trackOpprtmoment)"/>
        <doc-info-item :label="$t('table.processName')" :content="processFormatter(detailInfo.processName)"/>
        <doc-info-item :label="$t('table.documentSize')" :content="formatFileSize(detailInfo.fileSize)"/>
        <doc-info-item :label="$t('table.circulationTime')" :content="timestampFormatter(detailInfo.circulationTime)"/>
        <doc-info-item :label="$t('pages.ipAddr')" :content="detailInfo.ip"/>
        <doc-info-item :label="$t('pages.macAddr')" :content="detailInfo.mac"/>
        <doc-info-item :label="$t('pages.operatingSystem')" :content="parseOs(detailInfo)"/>
        <doc-info-item :label="$t('pages.osUser')" :content="detailInfo.oprtSysLoginUserName"/>
        <doc-info-item :label="$t('table.documentPath')" :content="detailInfo.filePath"/>
        <doc-info-item :label="$t('table.backupFileName')" :content="parentDocumentIdFormatter(detailInfo.localFilePath)"/>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="upCirculationLoading" :disabled="upCirculationDisabled" @click="handleUpCirculation">
          {{ $t('pages.documentTrack_btn1') }}
        </el-button>
        <el-button type="primary" :loading="downCirculationLoading" :disabled="downCirculationDisabled" @click="handleDownCirculation">
          {{ $t('pages.documentTrack_btn2') }}
        </el-button>
        <el-button type="primary" :disabled="downloadDisabled" @click="downloadDocument">
          {{ $t('components.download') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <audit-file-downloader ref="auditFileDownloader" :show="false" :append-to-body="appendToBody" :before-download="beforeDownload"/>
  </div>
</template>

<script>
import {
  formatProcess,
  getDetail,
  getDetailList,
  getProcessMapping,
  parseCirculationType,
  timestampFormatter
} from '@/api/dataEncryption/fileTrace/documentTrack'
import DocInfoItem from './DocInfoItem'
import { formatFileSize } from '@/utils'

export default {
  name: 'CirculationDetail',
  components: { DocInfoItem },
  props: {
    appendToBody: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      limit: 2000,
      offset: 0,
      detailInfo: {
        fileName: undefined,
        documentId: undefined,
        parentDocumentId: undefined,
        circulationId: undefined,
        parentCirculationId: undefined,
        parentCirculationIdInvalid: false,
        fileSize: 0,
        trackOpprtmoment: undefined,
        processName: undefined,
        terminalId: undefined,
        terminalName: undefined,
        userId: undefined,
        userName: undefined,
        ip: undefined,
        mac: undefined,
        circulationTime: undefined,
        filePath: undefined,
        uploadFileGuid: undefined,
        backupServerId: undefined
      },
      upCirculationLoading: false,
      downCirculationLoading: false,
      originList: [],
      autoScroll: false,
      lastScrollTime: 0,
      originListLoading: false,
      osType: {
        0: 'windows',
        1: 'mac',
        2: 'CentOs',
        3: 'Ubuntu',
        4: 'Readhat',
        5: 'Debian',
        6: 'Fedora'
      },
      tempTask: {},
      defaultTempTask: {
        backType: 24,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      hasMoreList: false
    }
  },
  computed: {
    listPanel() {
      return this.$refs['listPanel']
    },
    upCirculationDisabled() {
      return !this.detailInfo.parentCirculationId || this.detailInfo.parentCirculationId === '0' || this.detailInfo.parentCirculationIdInvalid
    },
    downCirculationDisabled() {
      return this.offset === 0 || this.originList.length === 0
    },
    downloadDisabled() {
      return !this.detailInfo.uploadFileGuid || !this.detailInfo.backupServerId
    }
  },
  created() {
    getProcessMapping().then(respond => {
      this.$store.commit('commonData/SET_DOCTRACK_PROCESSES', respond.data)
    })
  },
  methods: {
    formatFileSize,
    userFormatter(data) {
      if (data) return data
      return this.$t('pages.user_not_login')
    },
    processFormatter(processName) {
      return formatProcess(this.$store.getters.doctrackProcesses, processName, true)
    },
    parseCirculationType,
    timestampFormatter(timestamp) {
      return timestampFormatter(null, timestamp)
    },
    parentDocumentIdFormatter(data) {
      return data || this.$t('pages.null1')
    },
    parseOs(data) {
      return `${this.osType[data.oprtSystemKind]}${data.oprtSystemVer}(${data.oprtSystemBit})`
    },
    show(documentId, circulationId) {
      this.offset = 0
      this.hasMoreList = false
      this.originList = []
      this.detailInfo = { fileSize: 0 }
      this.getDetail(documentId, circulationId).then(data => {
        if (data) {
          this.visible = true
          if (this.detailInfo.parentCirculationId && this.detailInfo.parentCirculationId !== '0') {
            this.getDetailList(documentId, circulationId)
          } else {
            this.originList = [this.detailInfo]
          }
        }
      })
    },
    handleParentCirculationInvalid() {
      this.$set(this.detailInfo, 'parentCirculationIdInvalid', true)
      this.$notify({
        title: this.$t('text.warning'),
        message: this.$t('pages.documentTrack_text30'),
        type: 'warning',
        duration: 2000
      })
    },
    getDetail(documentId, circulationId) {
      return getDetail({ documentId, circulationId }).then(respond => {
        if (respond.data) {
          this.detailInfo = respond.data
        } else {
          this.hasMoreList = false
          this.handleParentCirculationInvalid()
        }
        return respond.data
      })
    },
    getDetailList(documentId, circulationId) {
      this.hasMoreList = false
      this.originListLoading = true
      return this.$nextTick().then(() => {
        this.autoScroll = true
        this.listPanel.scrollTop = this.listPanel.scrollHeight
        return getDetailList({ documentId, circulationId })
      }).then(respond => {
        const data = (respond.data || []).filter(item => (item.circulationId && item.circulationId !== '0'))
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if ('parentCirculationId' in item) {
            item.documentId = documentId
          } else {
            item.documentId = this.originList[this.originList.length - 1].parentDocumentId
            item.invalid = !item.documentId
            item.hide = true
          }
          this.originList.push(item)
          // 列表数量上限 2000
          if (this.originList.length >= this.limit) {
            break
          }
        }
        const lastNode = data.length && data[data.length - 1]
        this.hasMoreList = lastNode && ((lastNode.parentCirculationId && lastNode.parentCirculationId !== '0') || (lastNode.hide && lastNode.circulationId !== '0'))
        this.originListLoading = false
        return data
      }).catch(() => {
        this.originListLoading = false
        return Promise.reject()
      })
    },
    handleListClick(data, index) {
      this.offset = index
      this.getDetail(data.documentId, data.circulationId)
    },
    handleUpCirculation() {
      this.upCirculationLoading = true
      this.offset++
      if (this.offset >= this.limit) {
        this.offset--
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.documentTrack_text13'),
          type: 'warning',
          duration: 2000
        })
        return
      }
      const originNode = this.originList[this.offset]
      if (originNode && originNode.invalid) {
        this.offset--
        this.upCirculationLoading = false
        this.hasMoreList = false
        this.handleParentCirculationInvalid()
        return
      }
      if (originNode) {
        this.getDetail(originNode.documentId, originNode.circulationId).then(data => {
          if (!data) {
            this.offset--
            this.upCirculationLoading = false
            return
          }
          if (originNode.hide) {
            this.originList.splice(this.originList.length - 1, 1)
            return this.getDetailList(data.documentId, data.circulationId).then(() => {
              this.upCirculationLoading = false
              this.resetScrollTop()
            })
          } else {
            this.upCirculationLoading = false
            this.resetScrollTop()
          }
        }).catch(() => {
          this.offset--
          this.upCirculationLoading = false
        })
      } else {
        Promise.all([
          this.getDetail(this.detailInfo.documentId, this.detailInfo.parentCirculationId),
          this.getDetailList(this.detailInfo.documentId, this.detailInfo.parentCirculationId)
        ]).then(([detail, list]) => {
          if (!detail) {
            this.offset--
          }
          this.upCirculationLoading = false
          this.resetScrollTop()
        }).catch(() => {
          this.offset--
          this.upCirculationLoading = false
        })
      }
    },
    handleDownCirculation() {
      this.downCirculationLoading = true
      this.offset--
      this.resetScrollTop()
      const originNode = this.originList[this.offset]
      this.getDetail(originNode.documentId, originNode.circulationId)
        .then(() => { this.downCirculationLoading = false })
        .catch(() => {
          this.offset++
          this.downCirculationLoading = false
        })
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.backupServerId
      this.tempTask.fileGuid = row.uploadFileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    },
    downloadDocument() {
      // download from ftp
      if (this.$refs.auditFileDownloader) {
        this.$refs.auditFileDownloader.handleDownload(this.detailInfo)
      }
    },
    resetScrollTop() {
      this.autoScroll = true
      this.listPanel.scrollTop = this.listPanel.scrollHeight * this.offset / this.originList.length
    },
    // 防抖动
    antiShake() {
      if (this.upCirculationLoading || this.downCirculationLoading || this.originListLoading) {
        return true
      }
      if (this.autoScroll) {
        this.autoScroll = false
        return true
      }
      const currentTime = Date.now()
      if (currentTime - this.lastScrollTime <= 10) {
        return true
      }
      this.lastScrollTime = currentTime
      return false
    },
    handleScroll() {
      if (this.antiShake()) {
        return
      }
      if (this.originList.length === 0 || this.originList.length >= this.limit) {
        return
      }
      const originNode = this.originList[this.originList.length - 1]
      if (!originNode.hide && !originNode.parentCirculationId || originNode.parentCirculationId === '0') {
        return
      }
      if (this.listPanel.scrollTop === this.listPanel.scrollHeight - this.listPanel.clientHeight) {
        // 刷新列表
        if (originNode.hide) {
          if (originNode.circulationId) {
            this.originList.splice(this.originList.length - 1, 1)
            this.getDetailList(originNode.documentId, originNode.circulationId)
          }
        } else {
          this.getDetailList(originNode.documentId, originNode.parentCirculationId)
        }
      }
    },
    loadMore() {
      const originNode = this.originList[this.originList.length - 1]
      if (originNode.hide) {
        if (originNode.circulationId) {
          this.originList.splice(this.originList.length - 1, 1)
          this.getDetailList(originNode.documentId, originNode.circulationId)
        }
      } else {
        this.getDetailList(originNode.documentId, originNode.parentCirculationId)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-dialog__wrapper>>>.el-dialog__body {
    max-height: 530px;
  }
  .list-panel {
    width: 165px;
    height: 440px;
    position: relative;
    float: left;
    overflow-y: auto;
    border-right: 1px solid #e6e6e6;
    box-sizing: content-box;
    border-right: 1px dashed #cccccc;
  }
  .list-item {
    border-bottom: 1px dashed #ccc;
    cursor: pointer;
    padding: 5px 0 5px 5px;
    position: relative;
  }
  .list-item-summary {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    margin-top: 3px;
    font-size: 12px;
  }
  .list-loading {
    text-align: center;
    padding: 5px 0;
    width: 100%;
    i {
      color: #409eff;
    }
    .el-loading-text {
      color: #409eff;
      margin: 3px 0;
      font-size: 14px;
    }
  }
  .highlight {
    /*background: #ebf2f9;*/
    color: #409eff;
    font-weight: 700;
  }
  .detail-panel {
    margin-left: 182px;
    >>>.doc-info-item {
      font-size: 14px;
    }
    >>>.doc-info-item__label {
      width: 95px;
    }
    >>>.doc-info-item__content {
      width: calc(100% - 105px);
    }
  }
</style>
