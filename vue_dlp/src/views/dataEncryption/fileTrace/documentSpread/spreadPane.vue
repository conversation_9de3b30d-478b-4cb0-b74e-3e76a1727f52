<template>
  <div class="spread-pane">
    <div class="toolbar">
      <div class="searchCon">
        <user-term-select
          v-if="dataType === 'terminal'"
          :value="value"
          :leaf-key="dataType"
          :placeholder="placeholder"
          class="term-select"
          @change="handleChange"
        />
        <template v-else>
          <el-input
            v-trim
            :value="value"
            :maxlength="60"
            clearable
            :placeholder="placeholder"
            class="user-input"
            @input="handleChange"
          />
          <el-tooltip effect="dark" placement="top" :content="$t('text.fuzzyQueryUnsupported')">
            <i class="el-icon-info" />
          </el-tooltip>
        </template>

        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="grid"
      :height="300"
      :col-model="colModel"
      :multi-select="false"
      :row-data-api="rowDataApi"
      retain-pages
    />
  </div>
</template>

<script>
import UserTermSelect from '../UserTermSelect'

export default {
  name: 'SpreadPane',
  components: { UserTermSelect },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: undefined
    },
    placeholder: {
      type: String,
      default() {
        return this.$t('text.select')
      }
    },
    dataType: {
      type: String,
      default: 'user'
    },
    colModel: {
      type: Array,
      default() {
        return []
      }
    },
    rowDataApi: {
      type: Function,
      default() {
        return Promise.resolve({
          code: 20000,
          data: {
            total: 0,
            items: []
          }
        })
      }
    }
  },
  methods: {
    reloadTable(data) {
      this.$refs.grid.execRowDataApi(data)
    },
    handleChange(data) {
      this.$emit('change', data)
    },
    handleFilter() {
      this.$emit('search')
    }
  }
}
</script>

<style lang="scss" scoped>
  .spread-pane {
    .searchCon {
      float: right;

      .term-select {
        display: inline-block;
        position: relative;
        top: 1px;
      }
      .user-input {
        width: 182px;
        margin-bottom: 3px;
      }
    }
  }
</style>
