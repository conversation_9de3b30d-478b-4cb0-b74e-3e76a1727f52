<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="$t('table.spreadAnalysis1')"
      width="850px"
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="visible"
    >
      <el-tabs ref="tabs" v-model="srcQuery.searchInfo" @tab-click="handleTabClick">
        <el-tab-pane :label="$t('pages.documentTrack_text18')" name="user">
          <spread-pane
            ref="userPane"
            v-model="query.userName"
            data-type="user"
            :placeholder="$t('pages.documentTrack_text19')"
            :col-model="userColModel"
            :row-data-api="rcvRowDataApi('user')"
            @search="handleSearch('user')"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.documentTrack_text20')" name="terminal">
          <spread-pane
            ref="terminalPane"
            v-model="query.terminalId"
            data-type="terminal"
            :placeholder="$t('pages.documentTrack_text21')"
            :col-model="terminalColModel"
            :row-data-api="rcvRowDataApi('terminal')"
            @search="handleSearch('terminal')"
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('table.spreadDetail1')"
      width="900px"
      append-to-body
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="visibleDialog"
    >
      <spread-pane
        ref="srcPane"
        v-model="srcObjId"
        :data-type="srcQuery.searchInfo"
        :placeholder="placeholder"
        :col-model="srcColModel"
        :row-data-api="srcRowDataApi"
        @search="handleSrcSearch"
      />
    </el-dialog>

    <circulation-detail
      v-if="visibleDetail"
      ref="detailDialog"
      append-to-body
    />
  </div>
</template>

<script>
import SpreadPane from './spreadPane'
import CirculationDetail from '../circulationDetail'
import {
  formatProcess,
  getProcessMapping,
  getSpreadRcvPage,
  getSpreadSrcPage,
  parseCirculationType,
  timestampFormatter
} from '@/api/dataEncryption/fileTrace/documentTrack'

export default {
  name: 'SpreadAnalysis',
  components: { SpreadPane, CirculationDetail },
  data() {
    const that = this
    const createColModel = function(prop, label, deptProp, clickFn) {
      return [
        { prop: 'fileName', label: 'documentName', width: '150' },
        { prop: prop, label: label, width: '150', formatter: that.objFormatter(prop) },
        { prop: deptProp, label: 'department', width: '120', formatter: that.objFormatter('dept') },
        { prop: 'documentId', label: 'documentId', fixedWidth: '272' },
        { label: 'operate', type: 'button', fixedWidth: '80', fixed: 'right',
          buttons: [
            { label: 'spreadDetail', click: clickFn }
          ]
        }
      ]
    }
    return {
      visible: false,
      visibleDialog: false,
      visibleDetail: false,
      userColModel: createColModel('userName', 'rcvUser', 'userGroupName', this.handleSrcDetail),
      terminalColModel: createColModel('terminalName', 'rcvTerminal', 'terminalGroupName', this.handleSrcDetail),
      query: {
        page: 1,
        documentId: undefined,
        // userId: undefined,
        userName: undefined,
        terminalId: undefined
      },
      srcQuery: {
        page: 1,
        searchInfo: 'user',
        documentId: undefined,
        // parentUserId: undefined,
        parentUserName: undefined,
        userId: undefined,
        parentTerminalId: undefined,
        terminalId: undefined
      },
      srcObjId: undefined
    }
  },
  computed: {
    placeholder() {
      if (this.srcQuery.searchInfo === 'terminal') {
        return this.$t('pages.documentTrack_text22')
      } else {
        return this.$t('pages.documentTrack_text23')
      }
    },
    srcColModel() {
      const objType = this.srcQuery.searchInfo
      const rcvObjProp = objType + 'Name'
      const objTypeTitle = this.toTitleCase(objType)
      const srcObjProp = 'parent' + objTypeTitle + 'Name'
      const rcvObjLabel = 'rcv' + objTypeTitle
      const srcObjLabel = 'src' + objTypeTitle
      return [
        { prop: 'circulationTime', label: 'circulationTime', fixedWidth: '160', formatter: timestampFormatter },
        { prop: 'fileName', label: 'documentName', width: '150' },
        { prop: srcObjProp, label: srcObjLabel, width: '150', formatter: this.objFormatter(objType, true) },
        { prop: rcvObjProp, label: rcvObjLabel, width: '150', formatter: this.objFormatter(objType, false) },
        { prop: 'trackOpprtmoment', label: 'circulationType', width: '150', formatter: this.circulationTypeFormat },
        { prop: 'processName', label: 'processName', width: '100', formatter: this.processFormatter },
        { prop: 'circulationId', label: 'circulationId', fixedWidth: '180' },
        { label: 'operate', type: 'button', fixedWidth: '80', fixed: 'right',
          buttons: [
            { label: 'circulationDetail', click: this.handleDetail }
          ]
        }
      ]
    }
  },
  created() {
    getProcessMapping().then(respond => {
      this.$store.commit('commonData/SET_DOCTRACK_PROCESSES', respond.data)
    })
  },
  methods: {
    show(documentId) {
      this.query.documentId = documentId
      this.query.userName = undefined
      this.query.terminalId = undefined
      this.srcQuery.searchInfo = 'user'
      this.srcQuery.parentUserName = undefined
      this.srcQuery.parentTerminalId = undefined
      this.visible = true
      if (!this.$refs.userPane || !this.$refs.terminalPane) {
        return
      }
      this.$nextTick(() => {
        this.handleSearch('user')
        this.handleSearch('terminal')
      })
    },
    rcvRowDataApi(searchInfo) {
      return data => {
        data = Object.assign({}, this.query, { searchInfo }, data)
        return getSpreadRcvPage(data)
      }
    },
    srcRowDataApi(data) {
      data = Object.assign({}, this.srcQuery, data)
      return getSpreadSrcPage(data)
    },
    handleTabClick(tab, event) {
    },
    handleSearch(searchInfo) {
      const data = Object.assign({}, this.query, { searchInfo })
      this.$refs[searchInfo + 'Pane'].reloadTable(data)
    },
    handleSrcSearch() {
      const data = Object.assign({}, this.srcQuery)
      if (data.searchInfo === 'terminal') {
        data.parentUserName = undefined
        data.parentTerminalId = this.srcObjId
      } else {
        data.parentUserName = this.srcObjId
        data.parentTerminalId = undefined
      }
      this.$refs.srcPane.reloadTable(data)
    },
    handleSrcDetail(row) {
      this.visibleDialog = true
      this.srcObjId = undefined
      this.srcQuery.documentId = row.documentId
      if (this.srcQuery.searchInfo === 'terminal') {
        this.srcQuery.userId = undefined
        this.srcQuery.terminalId = row.terminalId
      } else {
        this.srcQuery.userId = row.userId
        this.srcQuery.terminalId = undefined
      }
      if (!this.$refs.srcPane) {
        return
      }
      this.$nextTick(() => {
        this.handleSrcSearch()
      })
    },
    handleDetail(row) {
      this.visibleDetail = true
      this.$nextTick(() => {
        this.$refs.detailDialog.show(row.documentId, row.circulationId)
      })
    },
    objFormatter(objType, isParent) {
      const that = this
      return function(row, data) {
        if (data) return data
        if (objType.toLowerCase().indexOf('user') >= 0 && (!isParent || row.parentCirculationId !== '0')) {
          return that.$t('pages.user_not_login')
        }
        return that.$t('pages.null1')
      }
    },
    processFormatter(row, data) {
      return formatProcess(this.$store.getters.doctrackProcesses, data)
    },
    circulationTypeFormat(row, data) {
      return parseCirculationType(data)
    },
    toTitleCase(str) {
      return str.charAt(0).toUpperCase() + str.slice(1)
    }
  }
}
</script>
