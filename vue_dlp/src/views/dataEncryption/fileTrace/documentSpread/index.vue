<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="searchCon">
          <document-upload :label="$t('pages.documentTrack_text16')" @uploaded="handleUploaded"/>
          <el-input
            v-model="query.documentId"
            v-trim
            clearable
            :placeholder="$t('pages.documentTrack_text17')"
            style="width: 310px;"
            :maxlength="32"
            @input="handleInput"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="366"
            trigger="click"
          >
            <Form ref="searchForm" label-position="left" :model="advancedQuery" label-width="94px">
              <FormItem :label="$t('table.documentName')">
                <el-input v-model="advancedQuery.fileName" v-trim :maxlength="250" clearable/>
              </FormItem>
              <FormItem :label="$t('table.documentId')">
                <el-input v-model="advancedQuery.documentId" v-trim :maxlength="32" clearable @input="handleAdvancedInput"/>
              </FormItem>
              <FormItem :label="$t('table.userAccount')" :tooltip-content="$t('text.fuzzyQueryUnsupported')">
                <!--<user-term-select v-model="advancedQuery.userId"/>-->
                <el-input v-model="advancedQuery.userName" v-trim :maxlength="60" clearable/>
              </FormItem>
              <FormItem :label="$t('table.terminalName')">
                <user-term-select v-model="advancedQuery.terminalId" leaf-key="terminal"/>
              </FormItem>
              <FormItem :label="$t('table.processName')">
                <!--<el-input v-trim v-model="advancedQuery.processName"/>-->
                <process-input ref="processMapping" v-model="advancedQuery.processName" clearable :type-filter="() => true"/>
              </FormItem>
              <FormItem :label="$t('table.circulationType')">
                <el-select v-model="advancedQuery.trackOpprtmoment" clearable>
                  <el-option v-for="(item, index) in opprtmoments" :key="index" :value="item.value" :label="item.label"/>
                </el-select>
              </FormItem>
              <FormItem :label="$t('table.circulationTime')">
                <date-range-picker v-model="dateRange"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetAdvancedQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleAdvancedSearch">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="logList"
        :col-model="colModel"
        :multi-select="false"
        :row-data-api="rowDataApi"
        :show-pager="showPager"
        :custom-col="true"
        :after-load="row => queryVideoMethod = asyncGetLogVideoInfo(row, '386', undefined, undefined, 'circulationTime')"
        retain-pages
      />
    </div>

    <spread-analysis ref="analysisDlg"/>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import DateRangePicker from '../DateRangePicker'
import DocumentUpload from '../DocumentUpload'
import ProcessInput from '../ProcessInput'
import UserTermSelect from '../UserTermSelect'
import SpreadAnalysis from './spreadAnalysis'
import { getSpreadPage, timestampFormatter, parseCirculationType, CIRCULATION_TYPES } from '@/api/dataEncryption/fileTrace/documentTrack'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo';

export default {
  name: 'DocumentSpread',
  components: { DateRangePicker, DocumentUpload, ProcessInput, UserTermSelect, SpreadAnalysis },
  data() {
    return {
      colModel: [
        { prop: 'circulationTime', label: 'circulationTime', width: '160', formatter: timestampFormatter },
        { prop: 'fileName', label: 'documentName', width: '150' },
        { prop: 'terminalName', label: 'terminalName', width: '150' },
        { prop: 'terminalGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'userAccount', width: '150', formatter: this.userFormatter },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'trackOpprtmoment', label: 'circulationType', width: '150', formatter: this.circulationTypeFormat },
        { prop: 'processName', label: 'processName', width: '100', formatter: this.processFormatter },
        { prop: 'documentId', label: 'documentId', width: '272' },
        { label: 'operate', type: 'button', fixedWidth: '80', fixed: 'right', hidden: !this.hasPermission('386'),
          buttons: [
            { label: 'spreadAnalysis', click: this.handleDetail }
          ]
        }
      ],
      showPager: false,
      query: { // 查询条件
        page: 1,
        documentId: undefined
      },
      advancedQuery: {},
      defaultAdvancedQuery: { // 高级查询
        page: 1,
        fileName: undefined, // 文档名称
        documentId: undefined, // 文档ID
        // userId: undefined, // 操作员
        userName: undefined, // 操作员账号
        terminalId: undefined, // 终端名称
        // terminalName: undefined, // 终端名称
        processName: undefined, // 进程名
        trackOpprtmoment: undefined, // 流转方式
        startDate: 0, // 流转时间
        endDate: 0
      },
      realQuery: {},
      dateRange: [],
      opprtmoments: undefined,
      queryVideoMethod: undefined
    }
  },
  created() {
    this.opprtmoments = CIRCULATION_TYPES
      .map(group => group.options.filter(item => !item.hide))
      .reduce((pre, cur) => pre.concat(cur), [])
    this.resetAdvancedQuery()
    addViewVideoBtn(this)
    this.realQuery = this.query
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    asyncGetLogVideoInfo,
    handleRefresh() {
      this.query = {
        page: 1,
        documentId: undefined
      }
      this.realQuery = this.query
      this.$refs.logList.execRowDataApi({ ...this.query, limit: 50 })
      this.showPager = false
    },
    handleUploaded(data, file) {
      this.query.documentId = data.documentId
      this.handleFilter()
    },
    // 重置
    resetAdvancedQuery() {
      this.advancedQuery = Object.assign({}, this.defaultAdvancedQuery)
      this.dateRange = []
    },
    correctDocumentIdInput(value) {
      if (value.length === 0) {
        return
      }
      let correctiveValue = ''
      let code
      for (let i = 0; i < value.length; i++) {
        code = value.charCodeAt(i)
        // 可以输入数字0~9
        if (code >= 48 && code <= 57) {
          correctiveValue += value.charAt(i)
          continue
        }
        // 可以输入小写字母a~f
        if (code >= 97 && code <= 102) {
          correctiveValue += value.charAt(i)
          continue
        }
        // 大写字母A~F转成小写
        if (code >= 65 && code <= 70) {
          correctiveValue += String.fromCharCode(code + 32)
        }
      }
      return correctiveValue
    },
    handleInput(value) {
      this.query.documentId = this.correctDocumentIdInput(value)
    },
    handleAdvancedInput(value) {
      this.advancedQuery.documentId = this.correctDocumentIdInput(value)
    },
    handleFilter() {
      if (!this.query.documentId) {
        // this.$message({
        //   message: this.$t('pages.documentTrack_text16'),
        //   type: 'error'
        // })
        this.handleRefresh()
        return
      }
      this.realQuery = this.query
      this.$refs.logList.execRowDataApi(this.query)
      this.showPager = true
    },
    handleAdvancedSearch() {
      if (!this.dateRange || this.dateRange.length === 0) {
        this.advancedQuery.startDate = undefined
        this.advancedQuery.endDate = undefined
      } else {
        this.advancedQuery.startDate = Math.floor(this.dateRange[0].getTime() / 1000)
        this.advancedQuery.endDate = Math.ceil(this.dateRange[1].getTime() / 1000) + 24 * 60 * 60 - 1
      }
      if (!this.advancedQuery.fileName && !this.advancedQuery.documentId &&
        !this.advancedQuery.userId && !this.advancedQuery.terminalId &&
        !this.advancedQuery.processName && (!this.advancedQuery.trackOpprtmoment && this.advancedQuery.trackOpprtmoment !== 0) &&
        this.advancedQuery.startDate === 0 && this.advancedQuery.endDate === 0) {
        // this.$message({
        //   message: this.$t('pages.documentTrack_text24'),
        //   type: 'error'
        // })
        this.handleRefresh()
        return
      }
      this.realQuery = this.advancedQuery
      this.$refs.logList.execRowDataApi(this.advancedQuery)
      this.showPager = true
    },
    handleDetail(row) {
      this.$refs.analysisDlg.show(row.documentId)
    },
    rowDataApi(data) {
      const searchQuery = Object.assign({}, this.realQuery, data)
      if (!this.showPager) {
        searchQuery.limit = 50
      }
      return getSpreadPage(searchQuery)
    },
    userFormatter(row, data) {
      if (data) return data
      return this.$t('pages.user_not_login')
    },
    processFormatter(row, data) {
      return this.$refs.processMapping.formatProcess(data)
    },
    circulationTypeFormat(row, data) {
      return parseCirculationType(data)
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-popover.el-popper {
    .el-form {
      .el-form-item {
        .el-autocomplete, .el-select {
          width: 100%;
        }
      }
    }
  }
</style>
