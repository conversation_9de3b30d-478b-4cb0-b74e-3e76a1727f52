<template>
  <div>
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
        {{ $t('button.add') }}
      </el-button>
      <el-button icon="el-icon-delete" size="mini" :disabled="!deletable" @click="handleDelete">
        {{ $t('button.delete') }}
      </el-button>
      <div class="searchCon">
        <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.processNameOrAppName')" style="width: 150px;" @keyup.enter.native="handleFilter" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="strategyList"
      :col-model="colModel"
      :height="height"
      :row-datas="sortedProcesses"
      :default-sort="{prop:''}"
      :selectable="selectable"
      :show-pager="false"
      @selectionChangeEnd="selectionChangeEnd"
    />

    <el-dialog
      v-el-drag-dialog
      :title="i18nConcatText($t('pages.mappingRelationshipBetweenProcessNameAndProgramName'), isUpdate ? 'update' : 'create')"
      :width="$store.getters.language === 'en' ? '550px' : '450px'"
      :modal="false"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="visible"
    >
      <Form ref="processForm" :model="temp" :rules="rules" label-position="right" label-width="110px">
        <FormItem :label="$t('table.processName1')" :tooltip-content="$t('pages.processNamesAreNotCaseSensitive')" prop="value" tooltip-placement="bottom-start">
          <el-input v-model="temp.value" clearable maxlength="255"/>
        </FormItem>
        <FormItem :label="$t('table.programName2')" prop="label">
          <el-input v-model="temp.label" maxlength="250" clearable/>
        </FormItem>
        <FormItem :label="$t('table.typeId')" prop="type">
          <el-select v-model="temp.type" clearable @change="ways = []">
            <el-option v-for="(val, key) in typeMapping" :key="key" :value="key" :label="val"/>
          </el-select>
        </FormItem>
        <FormItem v-show="showWays" :label="$t('table.supportedCirculationType')" prop="way">
          <el-checkbox-group v-model="ways">
            <el-checkbox v-for="item in supportedCirculationTypes" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleEdit">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addProcessMapping, updateProcessMapping, deleteProcessMapping, decodeValToArr, encodeArrToVal, CIRCULATION_TYPES
} from '@/api/dataEncryption/fileTrace/documentTrack'

export default {
  name: 'DocumentTrackProcess',
  props: {
    height: {
      type: Number,
      default: 350
    },
    processes: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      query: {
        searchInfo: ''
      },
      colModel: [
        { prop: 'value', label: 'processName1', width: '150', sort: true },
        { prop: 'label', label: 'programName2', width: '150', sort: true },
        { prop: 'type', label: 'typeId', width: '136', sort: true, sortOriginal: true, formatter: this.typeFormatter },
        { prop: 'way', label: 'supportedCirculationType', width: '120', formatter: this.wayFormatter },
        { label: 'operate', type: 'button', fixedWidth: '55', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      typeMapping: {
        IM: this.$t('pages.IMDescription'),
        Browser: this.$t('pages.browser'),
        NetDisk: this.$t('pages.networkDiskClient'),
        Email: this.$t('pages.emailClient'),
        FileMgr: this.$t('pages.fileManager'),
        DLP: this.$t('pages.lanDefender')
      },
      selection: [],
      isUpdate: false,
      temp: {},
      defaultTemp: {
        value: undefined,
        label: undefined,
        type: undefined
      },
      ways: [],
      rules: {
        value: [
          { required: true, message: this.$t('pages.validateMsg_processName') },
          { validator: this.processNameValidator }
        ],
        label: [
          { required: true, message: this.$t('text.pleaseEnterInfo', { info: this.$t('table.programName2') }) }
        ],
        type: [
          { required: true, message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.processCategory') }) }
        ]
      }
    }
  },
  computed: {
    deletable() {
      return Array.isArray(this.selection) && this.selection.length > 0
    },
    sortedProcesses() {
      if (this.processes) {
        const sortedProcesses = [...this.processes]
        sortedProcesses.sort((a, b) => {
          const cp = a.editable === b.editable ? a.id - b.id : 1;
          return a.editable ? -cp : cp;
        }).forEach(item => {
          if (item.ways && Array.isArray(item.ways)) {
            return
          }
          item.ways = decodeValToArr(item.way, 12)
        })
        return sortedProcesses
      }
      return this.processes
    },
    showWays() {
      return this.temp.type && this.temp.type !== this.typeMapping.DLP
    },
    supportedCirculationTypes() {
      if (!this.showWays) {
        return []
      }
      return CIRCULATION_TYPES
        .flatMap(item => item.options)
        .filter(item => {
          if (item.hide || !item.type) {
            return false
          }
          if (item.menuCode && !this.hasPermission(item.menuCode)) {
            return false
          }
          return item.type === this.temp.type
        })
    }
  },
  methods: {
    selectable(row, index) {
      return row.editable > 0
    },
    handleCreate() {
      this.temp = { ...this.defaultTemp }
      this.ways = []
      this.isUpdate = false
      this.visible = true
      this.$nextTick(() => {
        if (this.$refs.processForm) {
          this.$refs.processForm.clearValidate()
        }
      })
    },
    handleUpdate(row) {
      this.temp = { ...row }
      this.ways = [...row.ways]
      this.isUpdate = true
      this.visible = true
    },
    createProcess() {
      this.submitting = true
      const data = { ...this.temp, editable: true }
      addProcessMapping(data).then(respond => {
        this.submitting = false
        this.visible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.insertSuccess'),
          type: 'success',
          duration: 2000
        })
        const item = respond.data
        item.ways = this.ways
        this.processes.push(item)
        this.$store.commit('commonData/SET_DOCTRACK_PROCESSES', this.processes)
      }).catch(() => { this.submitting = false })
    },
    updateProcess() {
      this.submitting = true
      updateProcessMapping(this.temp).then(respond => {
        this.submitting = false
        this.visible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.editSuccess'),
          type: 'success',
          duration: 2000
        })
        const item = respond.data
        item.ways = this.ways
        for (let i = 0; i < this.processes.length; i++) {
          if (this.processes[i].id === item.id) {
            this.processes.splice(i, 1, item)
            this.$store.commit('commonData/SET_DOCTRACK_PROCESSES', this.processes)
            break
          }
        }
      }).catch(() => { this.submitting = false })
    },
    handleEdit() {
      this.$refs.processForm.validate(valid => {
        if (valid) {
          this.temp.way = encodeArrToVal(this.ways)
          this.isUpdate ? this.updateProcess() : this.createProcess()
        }
      })
    },
    handleDelete() {
      this.submitting = true
      const ids = this.selection.map(item => item.id)
      deleteProcessMapping({ ids: ids.join(',') }).then(() => {
        this.submitting = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.deleteSuccess'),
          type: 'success',
          duration: 2000
        })
        for (let i = this.processes.length - 1; i >= 0; i--) {
          if (ids.indexOf(this.processes[i].id) >= 0) {
            this.processes.splice(i, 1)
          }
        }
        this.$store.commit('commonData/SET_DOCTRACK_PROCESSES', this.processes)
      }).catch(() => { this.submitting = false })
    },
    selectionChangeEnd(selection) {
      this.selection = selection
    },
    typeFormatter(row, data) {
      return this.typeMapping[data]
    },
    wayFormatter(row, data) {
      if (!data) return undefined
      return CIRCULATION_TYPES
        .flatMap(item => item.options)
        .filter(item => !item.hide && (!item.menuCode || this.hasPermission(item.menuCode)) && row.ways.indexOf(item.value) >= 0)
        .map(item => item.label)
        .join('、')
    },
    buttonFormatter(row) {
      return row.editable ? this.$t('text.edit') : ''
    },
    processNameValidator(rule, value, callback) {
      const processName = this.temp.value.toLowerCase()
      for (let i = 0; i < this.processes.length; i++) {
        let isSame = this.processes[i].value.toLowerCase() === processName
        if (!isSame) {
          continue
        }
        if (this.isUpdate) {
          isSame = isSame && this.processes[i].id !== this.temp.id
        }
        if (isSame) {
          callback(new Error(this.$t('pages.documentTrack_text32')))
          return
        }
      }
      callback()
    },
    handleFilter() {
      const processName = this.query.searchInfo.toLowerCase()
      this.$emit('searchProcess', processName)
    }
  }
}
</script>
