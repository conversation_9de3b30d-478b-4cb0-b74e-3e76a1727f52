<template>
  <stg-dialog
    ref="stgDlg"
    :title="$t('pages.documentTrack_strategy')"
    :stg-code="213"
    :active-able="activeAble"
    :rules="rules"
    :model="defaultTemp"
    :entity-node="entityNode"
    :create="createStrategy"
    :update="updateStrategy"
    :format-row-data="formatRowData"
    :format-form-data="formatFormData"
    @submitEnd="submitEnd"
    @slotChange="slotChange"
  >
    <template :slot="slotName">
      <el-divider content-position="left">{{ $t('table.whitelistProcess') }}</el-divider>
      <data-editor
        :formable="formable"
        :popover-width="680"
        :updateable="editIndex >= 0"
        :deletable="selection.length !== 0"
        :addable="addable || false"
        :showable="showAble"
        :add-func="addWhitelist"
        :update-func="updateWhitelist"
        :delete-func="deleteWhiteList"
        :cancel-func="cancelWhiteTemp"
        :before-update="beforeUpdateWhiteTemp"
      >
        <Form ref="form" :model="whiteTemp" label-width="80px" label-position="right">
          <FormItem :label="$t('table.processName1')">
            <process-input ref="processMapping" v-model="whiteTemp.process" :placeholder="$t('pages.documentTrack_text1')" style="padding-left: 10px;" />
          </FormItem>
          <FormItem :label="$t('table.stgContent')">
            <el-checkbox-group v-model="whiteTemp.channel" style="padding-left: 10px;">
              <el-checkbox v-show="isShowChannel(2)" :label="1">{{ $t('pages.documentTrack_whitelistChannels1') }}</el-checkbox>
              <el-checkbox v-show="isShowChannel(1)" :label="2">{{ $t('pages.documentTrack_whitelistChannels2') }}</el-checkbox>
            </el-checkbox-group>
          </FormItem>
        </Form>
      </data-editor>
      <grid-table
        ref="ruleTable"
        :height="180"
        :show-pager="false"
        :multi-select="formable"
        :selectable="() => { return formable }"
        :col-model="colModel"
        row-key="process"
        :row-datas="temp.documentTraceWhiteProcess"
        @selectionChangeEnd="handleSelectionChange"
      />
      <FormItem label-width="18px">
        <!--<el-switch v-model="temp.documentTraceAddStrategy.addType" :active-value="2" :inactive-value="0"/>-->
        <el-checkbox v-model="temp.documentTraceAddStrategy.addType" :true-label="2" :false-label="0" :disabled="!formable">
          {{ $t('table.documentTrackAddType') }}
        </el-checkbox>
      </FormItem>
      <span class="add-type">
        {{ $t('pages.documentTrack_text25') }}
      </span>
    </template>
  </stg-dialog>
</template>

<script>
import {
  formatProcess,
  createStrategy,
  updateStrategy,
  encodeArrToVal,
  decodeValToArr,
  CIRCULATION_TYPES,
  WHITELIST_CHANNELS
} from '@/api/dataEncryption/fileTrace/documentTrack'
import ProcessInput from '../ProcessInput'

export default {
  name: 'DocumentStrategyDlg',
  components: { ProcessInput },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      colModel: [
        { prop: 'process', label: 'processName1', width: '80', sort: true, formatter: this.processFormatter },
        { prop: 'channel', label: 'stgContent', width: '80', formatter: this.whitelistChannelFormatter }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        // addType: [], // 追踪方式
        documentTraceAddStrategy: { addType: 0 },
        documentTraceWhiteProcess: []
      },
      whiteTemp: {
        process: undefined,
        channel: []
      },
      selection: [],
      editIndex: -1,
      showAble: false,
      addable: true,
      rules: {
        addType: [
          { validator: this.addTypeValidator, trigger: 'change' }
          // { type: 'array', required: true, message: '请至少选择一种追踪方式', trigger: 'change' }
        ]
      },
      slotName: undefined
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    addTypeValidator(rule, value, callback) {
      if (this.temp.addType && this.temp.addType.length) {
        callback()
      } else {
        callback(new Error(this.$t('pages.documentTrack_text2')))
      }
    },
    createStrategy,
    updateStrategy,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    handleCreate() {
      this.resetTemp()
      this.resetWhiteTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.$refs['stgDlg'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetWhiteTemp()
      this.$refs['stgDlg'].show(row, this.formable)
    },
    handleShow: function(row, isGenerateStrategy) {
      this.resetWhiteTemp()
      //  将内容展示出来
      this.showAble = true
      //  将新增按钮隐藏
      this.addable = false
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    formatRowData(row) {
      // row.addType = decodeValToArr(row.documentTraceAddStrategy.addType)
    },
    formatFormData(data) {
      // data.documentTraceAddStrategy.addType = encodeArrToVal(data.addType)
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$refs['stgDlg'].$refs['headForm4StgDialog'].clearValidate()
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    handleRowClick(rowData, column, event) {
      this.editIndex = this.temp.documentTraceWhiteProcess.map(item => item.process).indexOf(rowData.process)
      this.whiteTemp = {
        process: rowData.process,
        channel: decodeValToArr(rowData.channel)
      }
    },
    // 表格复选框选中时
    handleSelectionChange(selection) {
      this.selection = selection.map(item => item.process)
      if (selection.length === 1) {
        this.editIndex = this.temp.documentTraceWhiteProcess.map(item => item.process).indexOf(selection[0].process)
      } else {
        this.resetWhiteTemp()
      }
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    resetWhiteTemp() {
      this.whiteTemp = {
        process: undefined,
        channel: []
      }
      this.editIndex = -1
    },
    cancelWhiteTemp() {
      this.whiteTemp = {
        process: undefined,
        channel: []
      }
      // this.$refs['ruleTable'] && this.$refs['ruleTable'].clearValidate()
    },
    beforeUpdateWhiteTemp() {
      const selected = this.$refs['ruleTable'].getSelectedDatas()[0]
      this.whiteTemp = {
        process: selected.process,
        channel: decodeValToArr(selected.channel)
      }
    },
    addWhitelist() {
      return this.editWhitelist(0)
    },
    updateWhitelist() {
      return this.editWhitelist(1)
    },
    editWhitelist(deleteCount) {
      if (!this.whiteTemp.process) {
        this.$message({
          message: this.$t('pages.documentTrack_text1'),
          type: 'error',
          duration: 2000
        })
        return
      }
      const index = this.temp.documentTraceWhiteProcess.map(item => item.process).indexOf(this.whiteTemp.process)
      let isExisted = index >= 0
      if (deleteCount > 0) {
        isExisted = isExisted && index !== this.editIndex
      }
      if (isExisted) {
        this.$message({
          message: this.$t('pages.documentTrack_text3'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.temp.documentTraceWhiteProcess.splice(deleteCount > 0 ? this.editIndex : 0, deleteCount, {
        process: this.whiteTemp.process,
        channel: encodeArrToVal(this.whiteTemp.channel)
      })
      this.resetWhiteTemp()
      return true
    },
    // 删除白名单
    deleteWhiteList() {
      this.selection.forEach(process => {
        const start = this.temp.documentTraceWhiteProcess.map(item => item.process).indexOf(process)
        if (start === this.editIndex) {
          this.resetWhiteTemp()
        }
        this.temp.documentTraceWhiteProcess.splice(start, 1)
      })
      this.selection = []
    },
    processFormatter(row, data) {
      return this.$refs.processMapping ? this.$refs.processMapping.formatProcess(data, true) : formatProcess(this.$store.getters.doctrackProcesses || [], data, true);
    },
    whitelistChannelFormatter(row, data) {
      return WHITELIST_CHANNELS[data]
    },
    isShowChannel(prop) {
      if (!this.whiteTemp.process) {
        return true
      }
      const process = this.$store.getters.doctrackProcesses.filter(item => item.value === this.whiteTemp.process)[0]
      if (!process.ways) {
        process.ways = decodeValToArr(process.way, 12)
      }
      return CIRCULATION_TYPES
        .filter(item => item.prop === prop)
        .flatMap(item => item.options)
        .filter(item => !item.hide && (!item.menuCode || this.hasPermission(item.menuCode)) && process.ways.indexOf(item.value) >= 0)
        .map(item => item.type)
        .includes(process.type)
    }
  }
}
</script>
<style lang="scss" scoped>
  .add-type {
    position: relative;
    left: 16px;
    font-size: 13px;
    color: #2674b2;
  }
  >>>.el-dialog__body {
    overflow: hidden;
    max-height: 530px;
  }
</style>
