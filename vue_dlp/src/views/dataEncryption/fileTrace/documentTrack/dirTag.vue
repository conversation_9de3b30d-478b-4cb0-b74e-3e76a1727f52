<template>
  <div :class="['tag-div', { 'tag-div-border': border }]">
    <el-tag
      v-for="tag in dynamicTags"
      :key="tag"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
    >
      {{ tag }}
    </el-tag>
    <el-input
      v-if="inputVisible"
      ref="saveTagInput"
      v-model="inputValue"
      class="input-new-tag"
      size="small"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
    >
    </el-input>
    <el-button v-else class="button-new-tag" size="small" @click="showInput">+</el-button>
  </div>
</template>

<script>
export default {
  name: 'DirTag',
  model: {
    prop: 'value',
    event: 'changed'
  },
  props: {
    value: {
      type: Array,
      default() {
        return []
      }
    },
    border: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dynamicTags: [],
      inputVisible: false,
      inputValue: '',
      autoChanged: false
    };
  },
  watch: {
    value() {
      if (this.autoChanged) {
        this.autoChanged = false
        return
      }
      this.dynamicTags = Object.assign([], this.value)
    }
  },
  created() {
    this.dynamicTags = Object.assign([], this.value)
  },
  methods: {
    copyValue() {
      return Object.assign([], this.dynamicTags)
    },
    handleClose(tag) {
      const oldValue = this.copyValue()
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      this.autoChanged = true
      this.$emit('changed', this.copyValue(), oldValue)
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    handleInputConfirm() {
      const inputValue = this.inputValue;
      if (inputValue && this.dynamicTags.indexOf(inputValue) < 0) {
        const oldValue = this.copyValue()
        this.dynamicTags.push(inputValue);
        this.autoChanged = true
        this.$emit('changed', this.copyValue(), oldValue)
      }
      this.inputVisible = false;
      this.inputValue = '';
    }
  }
}
</script>

<style lang="scss" scoped>
  .tag-div {
    height: 40px;
    padding: 1px;
  }
  .tag-div-border {
    border: 1px solid #aaa;
    border-radius: 4px;
  }
  .el-tag {
    height: 30px;
    line-height: 28px;
    max-width: 200px;
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: middle;
    margin: 3px 5px;
    position: relative;
    padding-right: 20px;
  }
  .el-tag >>>.el-icon-close {
    top: 6px;
    right: 2px;
    position: absolute;
  }
  .button-new-tag {
    width: 28px;
    height: 30px;
    margin: 3px 5px;
    font-size: 14px;
  }
  .input-new-tag {
    width: 100px;
    margin: 3px 5px !important;
    text-align: center;
    vertical-align: middle;
  }
</style>
