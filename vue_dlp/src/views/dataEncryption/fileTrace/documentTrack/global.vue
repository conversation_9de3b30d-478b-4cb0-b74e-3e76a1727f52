<template>
  <div style="display: inline-block">
    <el-button icon="el-icon-s-tools" size="mini" @click="handleGlobal">
      {{ $t('button.highConfig') }}
    </el-button>
    <el-dialog
      v-el-drag-dialog
      :title="$t('route.documentTrack') + ' ' + $t('button.highConfig')"
      width="720px"
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="visible"
    >
      <el-tabs v-model="tabName" :before-leave="handleBeforeLeave">
        <el-tab-pane :label="$t('button.highConfig')" name="config" class="block">
          <Form ref="trackForm" :model="temp" :rules="rules" label-width="80px" label-position="left" style="margin-bottom: 20px;">
            <FormItem
              class="opprtmomentType"
              :class="{ opprtmomentTypeErr: temp.opprtmomentType.length === 0 }"
              :label="$t('pages.documentTrack_label1')"
              prop="opprtmomentType"
            >
              <el-table
                v-for="(group, index) in opprtmomentTypeGroupOpts"
                :key="index"
                ref="table"
                :data="group.options"
                :height="tableHeight"
                :stripe="false"
                :border="false"
                tooltip-effect="dark"
                style="width: 235px; display: inline-block;"
                @header-click="handleHeaderClick(index)"
                @row-click="row => handleRowClick(row, index)"
                @selection-change="selection => handleSelectionChange(selection, index)"
              >
                <el-table-column type="selection" width="36"/>
                <el-table-column prop="label" :label="group.label">
                  <template slot-scope="scope">
                    <span class="ellipsis tooltip" :title="scope.row.label">{{ scope.row.label }}</span>
                    <el-tooltip v-if="'type' in scope.row" effect="dark" placement="right">
                      <div slot="content">{{ buildCirculationTypeTips(scope.row) }}</div>
                      <i class="el-icon-info"/>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </FormItem>
            <FormItem class="suffix-list" :label="$t('pages.documentTrack_label3')" prop="suffixList">
              <el-checkbox v-model="checkAll" :indeterminate="indeterminate" @change="handleCheckAllChange">{{ $t('button.selectAll') }}</el-checkbox>
              <el-checkbox-group v-model="temp.suffixList" @change="handleCheckedSuffixChange">
                <el-checkbox v-for="item in suffixListOpts" :key="item" :label="item">{{ item }}</el-checkbox>
              </el-checkbox-group>
            </FormItem>
            <FormItem :label="$t('pages.documentTrack_label4')" prop="dirPath">
              <el-radio-group v-model="temp.monFilePrptyKind" @change="handleDirPathChanged">
                <el-radio :label="0">{{ $t('pages.documentTrack_dirType0') }}</el-radio>
                <el-radio :label="1">{{ $t('pages.documentTrack_dirType1') }}</el-radio>
                <el-radio :label="2">{{ $t('pages.documentTrack_dirType2') }}</el-radio>
              </el-radio-group>
              <dir-tag v-show="temp.monFilePrptyKind > 0" v-model="temp.dirPath" @changed="handleDirPathChanged"/>
            </FormItem>
            <FormItem :label="$t('pages.documentTrack_label5')" label-width="208px">
              <el-switch v-model="temp.modifyFileId" :active-value="1" :inactive-value="0"/>
            </FormItem>
            <FormItem :label="$t('pages.documentTrack_label6')">
              <el-checkbox-group v-model="temp.backupFileSwitch">
                <el-checkbox :label="2">{{ $t('pages.documentTrack_backupFileSwitch2') }}</el-checkbox>
                <el-checkbox :label="1">{{ $t('pages.documentTrack_backupFileSwitch1') }}</el-checkbox>
              </el-checkbox-group>
              <i18n v-show="showBackSize" path="pages.documentTrack_label7">
                <el-input-number slot="size" v-model="temp.backupFileMaxSize" :controls="false" :precision="0" style="width:105px" :min="0" :max="200"/>
                <el-tooltip slot="tooltip" class="item" effect="dark" placement="right">
                  <div slot="content">
                    {{ $t('pages.documentTrack_text8') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </i18n>
            </FormItem>
          </Form>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.mappingRelationshipBetweenProcessNameAndProgramName')" name="mapping">
          <document-track-process :processes="processes" style="margin: 3px 0 6px" @searchProcess="handleSearchProcess"/>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button v-show="tabName === 'config'" :loading="submitting" type="primary" @click="handleUpdate">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getGlobalStg,
  updateGlobalStg,
  getProcessMapping,
  encodeArrToVal,
  decodeValToArr,
  CIRCULATION_TYPES
} from '@/api/dataEncryption/fileTrace/documentTrack'
import DirTag from './dirTag'
import DocumentTrackProcess from './process'

export default {
  name: 'DocumentStrategyGlobal',
  components: { DirTag, DocumentTrackProcess },
  data() {
    return {
      visible: false,
      submitting: false,
      tabName: 'config',
      temp: {},
      defaultTemp: {
        opprtmomentType: [],
        monitorFilePosType: [],
        suffixList: [],
        monFilePrptyKind: 1,
        dirPath: [],
        modifyFileId: undefined,
        backupFileSwitch: [],
        backupFileMaxSize: 20
      },
      stg: {},
      defaultStg: {
        name: 'document_track_global_stg',
        active: true,
        entityType: 3,
        entityId: 0
      },
      opprtmomentTypeGroupOpts: undefined,
      monitorFilePosTypeOpts: [{
        value: 1,
        label: this.$t('pages.documentTrack_filePosType1')
      }, {
        value: 2,
        label: this.$t('pages.documentTrack_filePosType2')
      }, {
        value: 4,
        label: this.$t('pages.documentTrack_filePosType3')
      }],
      suffixListOpts: ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf'],
      checkAll: false,
      indeterminate: false,
      rules: {
        opprtmomentType: [
          { required: true, message: this.$t('pages.documentTrack_text4'), trigger: 'change' }
        ],
        suffixList: [
          { required: true, message: this.$t('pages.documentTrack_text6'), trigger: 'change' }
        ],
        monFilePrptyKind: [
          { required: true, message: this.$t('pages.documentTrack_text10'), trigger: 'change' }
        ],
        dirPath: [
          { required: true, validator: this.dirPathValidator, trigger: 'changed' }
        ]
      },
      processes: []
    }
  },
  computed: {
    tableHeight() {
      if (!this.opprtmomentTypeGroupOpts) {
        return 100
      }
      const maxItems = Math.max(...this.opprtmomentTypeGroupOpts.map(group => group.options.length))
      return Math.max(39 + 29 * maxItems, 100)
    },
    showBackSize() {
      return this.temp.backupFileSwitch && this.temp.backupFileSwitch.length
    }
  },
  created() {
    this.opprtmomentTypeGroupOpts = CIRCULATION_TYPES.filter(group => group.prop > 0).map(group => ({
      label: group.label,
      selection: [],
      options: group.options.filter(item => {
        if (item.hide) {
          return false
        }
        if (item.menuCode) {
          return this.hasPermission(item.menuCode)
        }
        return true
      }).map(item => ({ ...item, prop: group.prop }))
    }))
    this.temp = Object.assign({}, this.defaultTemp)
  },
  methods: {
    handleBeforeLeave(activeName, oldActiveName) {
      return !this.submitting
    },
    handleGlobal() {
      this.visible = true
      this.initForm()
    },
    initForm() {
      // 高级配置
      if (this.$refs.trackForm) {
        this.$refs.trackForm.clearValidate()
      }
      getGlobalStg().then(respond => {
        this.stg = respond.data
        if (this.stg.documentTraceSundrySrategy.backupFileSwitch == 0) {
          this.stg.documentTraceSundrySrategy.backupFileMaxSize = 20
        }
        this.stgToForm(this.stg)
        this.$nextTick(() => {
          const opprtmomentType = Object.assign([], this.temp.opprtmomentType)
          for (let i = 0; i < this.opprtmomentTypeGroupOpts.length; i++) {
            const table = this.tableRef(i)
            const options = this.opprtmomentTypeGroupOpts[i].options
            for (let j = 0; j < options.length; j++) {
              table.toggleRowSelection(options[j], opprtmomentType.indexOf(options[j].value) >= 0)
            }
          }
          this.handleCheckedSuffixChange(this.temp.suffixList)
        })
      })
      // 进程映射
      getProcessMapping().then(respond => {
        this.processes = respond.data.map(item => ({ ...item, ways: decodeValToArr(item.way, 12) }))
      })
    },
    handleUpdate() {
      this.$refs['trackForm'].validate(valid => {
        if (valid) {
          this.submitting = true
          const stg = Object.assign(this.stg, this.formToStg(), this.defaultStg)
          updateGlobalStg(stg).then(() => {
            this.submitting = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.visible = false
          }).catch(() => { this.submitting = false })
        }
      })
    },
    stgToForm(stg) {
      const temp = Object.assign({}, this.temp)
      if (stg.documentTraceBaseStrategy.length) {
        temp.opprtmomentType = stg.documentTraceBaseStrategy.map(item => item.opprtmomentType)
        temp.monitorFilePosType = decodeValToArr(stg.documentTraceBaseStrategy[0].monitorFilePosType, 3)
      }
      if (stg.documentTraceFilePrpty.length) {
        if (stg.documentTraceFilePrpty.length === 1 && stg.documentTraceFilePrpty[0].dirPath === '*.*') {
          temp.monFilePrptyKind = 0
          temp.dirPath = []
        } else {
          const monFilePrptyKind = stg.documentTraceSundrySrategy.monFilePrptyKind
          temp.monFilePrptyKind = monFilePrptyKind === 0 ? 1 : monFilePrptyKind
          temp.dirPath = stg.documentTraceFilePrpty.map(item => item.dirPath)
        }
      } else {
        temp.monFilePrptyKind = stg.documentTraceSundrySrategy.monFilePrptyKind
        temp.dirPath = []
      }
      temp.modifyFileId = stg.documentTraceUpdateStrategy.modifyFileId
      temp.backupFileSwitch = decodeValToArr(stg.documentTraceSundrySrategy.backupFileSwitch)
      temp.backupFileMaxSize = stg.documentTraceSundrySrategy.backupFileMaxSize
      const suffixList = stg.documentTraceSundrySrategy.suffixList
      if (suffixList) {
        temp.suffixList = suffixList.split('|')
      }
      this.temp = temp
    },
    formToStg() {
      const documentTraceBaseStrategy = [] // 文档追踪基础策略表
      const documentTraceFilePrpty = [] // 文档追踪要监控文件属性
      const documentTraceUpdateStrategy = {} // 文档追踪信息更新策略表
      const documentTraceSundrySrategy = {} // 文档追踪杂项策略表
      if (this.temp.opprtmomentType && this.temp.opprtmomentType.length) {
        // const monitorFilePosType = encodeArrToVal(this.temp.monitorFilePosType)
        this.temp.opprtmomentType.forEach(item => {
          documentTraceBaseStrategy.push({
            opprtmomentType: item,
            monOpprtmontPrpty: this.parseMonOpprtmontPrpty(item),
            monitorFilePosType: 7
          })
        })
      }
      if (this.temp.monFilePrptyKind > 0) {
        this.temp.dirPath.forEach(item => {
          documentTraceFilePrpty.push({
            dirPath: item,
            fileName: '*.*'
          })
        })
      }
      documentTraceUpdateStrategy.modifyFileId = this.temp.modifyFileId
      documentTraceSundrySrategy.backupFileSwitch = encodeArrToVal(this.temp.backupFileSwitch)
      documentTraceSundrySrategy.backupFileMaxSize = this.temp.backupFileMaxSize
      documentTraceSundrySrategy.suffixList = (this.temp.suffixList || []).join('|')
      documentTraceSundrySrategy.monFilePrptyKind = this.temp.monFilePrptyKind
      return { documentTraceBaseStrategy, documentTraceFilePrpty, documentTraceUpdateStrategy, documentTraceSundrySrategy }
    },
    parseMonOpprtmontPrpty(opprtmomentType) {
      for (let i = 0; i < this.opprtmomentTypeGroupOpts.length; i++) {
        const options = this.opprtmomentTypeGroupOpts[i].options
        for (let j = 0; j < options.length; j++) {
          const item = options[j]
          if (item.value === opprtmomentType) {
            return item.prop
          }
        }
      }
      return 0
    },
    tableRef(index) {
      return this.$refs['table'][index]
    },
    handleHeaderClick(index) {
      const group = this.opprtmomentTypeGroupOpts[index]
      const table = this.tableRef(index)
      const selectedCount = group.selection.length
      if (selectedCount > 0) {
        table.clearSelection()
      }
      if (selectedCount < group.options.length) {
        table.toggleAllSelection()
      }
    },
    handleRowClick(row, index) {
      this.tableRef(index).toggleRowSelection(row)
    },
    handleSelectionChange(selection, index) {
      this.opprtmomentTypeGroupOpts[index].selection = selection
      this.opprtmomentTypeGroupOpts[0].selection.map(item => item.value)
      this.temp.opprtmomentType = this.mapSelectionOpprtmomentType(0).concat(this.mapSelectionOpprtmomentType(1))
    },
    mapSelectionOpprtmomentType(index) {
      return this.opprtmomentTypeGroupOpts[index].selection.map(item => item.value)
    },
    handleCheckAllChange(val) {
      this.temp.suffixList = val ? this.suffixListOpts : [];
      this.indeterminate = false;
    },
    handleCheckedSuffixChange(value) {
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.suffixListOpts.length;
      this.indeterminate = checkedCount > 0 && !this.checkAll;
    },
    buildCirculationTypeTips({ value, type }) {
      return this.processes
        .filter(item => item.ways.indexOf(value) >= 0 && item.type === type) // 过滤
        .map(item => item.label) // 转换
        .filter((item, index, arr) => arr.indexOf(item, 0) === index) // 去重
        .join('、') // 拼接
    },
    dirPathValidator(rule, value, callback) {
      if (this.temp.monFilePrptyKind === 0 || this.temp.dirPath.length > 0) {
        callback()
      } else {
        callback(new Error(this.$t('pages.documentTrack_text7')))
      }
    },
    handleDirPathChanged() {
      this.$refs['trackForm'].validateField('dirPath')
    },
    handleSearchProcess(processName) {
      // 进程映射
      getProcessMapping().then(respond => {
        const processes = respond.data.map(item => ({ ...item, ways: decodeValToArr(item.way, 12) }))
        this.processes = processes.filter((item) => item.value.toLowerCase().includes(processName) || item.label.toLowerCase().includes(processName))
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.block{
  >>>.el-form-item__label{
    /*font-size: 15px;*/
    color: #eee;
  }
  >>>.el-select{
    width: 500px;
  }
  >>>.el-table {
    /*border: 0;*/
    td, th.is-leaf {
      background: #e4e7e9;
    }
    td {
      border: 0;
    }
    tr {
      cursor: pointer;
    }
  }
  >>>.el-checkbox {
    margin-right: 20px;
  }
  >>>.el-checkbox:last-of-type {
    margin-right: 0;
  }
}
.opprtmomentType>>>.el-form-item__content {
  .el-table:nth-child(2) {
    margin-left: 24px;
  }
}
>>>.el-tab-pane {
  height: auto;
}
>>>.el-tabs__content {
  height: 415px;
  overflow: auto;
}
.ellipsis.tooltip {
  max-width: calc(100% - 20px);
  line-height: 16px;
  display: inline-block;
  vertical-align: text-top;
}
</style>
