<template>
  <tree-select
    ref="tree"
    node-key="id"
    :height="350"
    :width="275"
    :local-search="false"
    is-filter
    clearable
    :placeholder="placeholder"
    :leaf-key="leafKey"
    rewrite-node-click-fuc
    :node-click-fuc="handleTreeClick"
    @change="handleChange"
  />
</template>

<script>

export default {
  name: 'UserTermSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number],
      default: undefined
    },
    leafKey: {
      type: String,
      default: 'user'
    },
    placeholder: {
      type: String,
      default() {
        return this.$t('text.select')
      }
    }
  },
  watch: {
    value(val) {
      if (val == null) {
        this.$refs.tree.clearSelectedNode()
      }
    }
  },
  methods: {
    handleTreeClick(data, node, vm) {
      if (data.dataType === 'G' || data.type === 'G') {
        // 分组节点不选中
        return false
      }
    },
    handleChange(selectedKeys, data) {
      this.$emit('change', data ? data.dataId : undefined)
    }
  }
}
</script>
