<template>
  <el-tooltip :content="content" :placement="placement" :effect="effect" :disabled="disabled">
    <div class="content-wrapper" @mouseover="handleMouseover">
      <span ref="contentSpan" :class="{ 'content-invalid': invalid }">{{ content }}</span>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  name: 'OverflowTooltip',
  props: {
    effect: {
      type: String,
      default: 'dark'
    },
    content: {
      type: String,
      default: undefined
    },
    invalid: {
      type: Boolean,
      default: false
    },
    placement: {
      type: String,
      default: 'top'
    }
  },
  data() {
    return {
      disabled: true
    }
  },
  methods: {
    handleMouseover() {
      const el = this.$refs.contentSpan
      const parent = el.parentElement
      const parentWidth = parent.getBoundingClientRect().width
      const contentWidth = el.getBoundingClientRect().width
      const safariDisabled = el.offsetWidth >= parent.offsetWidth && contentWidth <= parentWidth
      this.disabled = safariDisabled || contentWidth === parentWidth || parent.scrollWidth <= parentWidth
    }
  }
}
</script>

<style lang="scss" scoped>
  .content-wrapper {
    display: inline-block;
    /* 解决设置inline-block元素的overflow：hidden意外增加元素总体高度的问题 */
    vertical-align: bottom;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .content-invalid {
    text-decoration: line-through;
  }
</style>
