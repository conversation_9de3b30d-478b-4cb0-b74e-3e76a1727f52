<template>
  <div class="img-container">
    <el-upload
      action=""
      accept=".bmp,.jpg,.jpeg,.png"
      :before-upload="beforeUpload"
    >
      <el-tooltip effect="dark" placement="top">
        <div slot="content">{{ $t('pages.blindWatermark_text7') }}</div>
        <el-button size="mini" type="primary" icon="el-icon-upload" :loading="loading">{{ $t('pages.blindWatermark_text1') }}</el-button>
      </el-tooltip>
    </el-upload>
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.blindWatermark_text2')"
      width="720px"
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="visible"
    >
      <div class="img-toolbar">
        <div class="img-code">
          <el-input
            v-model="waterCode"
            v-trim
            clearable
            :placeholder="$t('pages.blindWatermark_text')"
            :maxlength="10"
            @input="handleInput"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
        <watermark-tooltip class="img-tooltip" labeled pictured exampled/>
      </div>

      <div v-for="(image, key) in images" :key="key" class="img-holder">
        <el-image :src="image.src" :preview-src-list="srcList" :fit="image.fit" :class="image.class">
          <div v-for="slot in slots" :key="slot" :slot="slot" class="img-slot">
            <i class="el-icon-picture-outline"></i>
            {{ $t('pages.loading') }}<span class="dot">...</span>
            <i class="el-icon-loading"></i>
          </div>
        </el-image>
        <div class="img-title">{{ image.title }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createImgDownloadUrl,
  parseWatermarkFromImg,
  readCodeFromImg
} from '@/api/dataEncryption/fileTrace/blindWatermark'
import WatermarkTooltip from './WatermarkTooltip'
import axios from 'axios'
import { readAsArrayBuffer } from '@/utils/blob'

export default {
  name: 'WatermarkPreview',
  components: { WatermarkTooltip },
  props: {
    correctInput: {
      type: Function,
      default(value) {
        return value
      }
    },
    queryRestrict: {
      type: Function,
      default(value) {
        return true
      }
    }
  },
  data() {
    return {
      images: {
        ori: {
          src: '',
          fit: 'scale-down',
          class: '',
          title: this.$t('pages.blindWatermark_img0')
        },
        sub: {
          src: '',
          fit: 'scale-down',
          class: '',
          title: this.$t('pages.blindWatermark_img1')
        },
        hrz: {
          src: '',
          fit: 'cover',
          class: 'img-auto',
          title: this.$t('pages.blindWatermark_img2')
        },
        vtc: {
          src: '',
          fit: 'cover',
          class: 'img-auto',
          title: this.$t('pages.blindWatermark_img3')
        }
      },
      slots: ['placeholder', 'error'],
      waterCode: undefined,
      loading: false,
      visible: false,
      source: undefined,
      fileSizeLimit: 20 * 1024 * 1024 // 20M
    }
  },
  computed: {
    srcList() {
      return Object.values(this.images).map(image => image.src).filter(src => src && src.length > 0)
    },
    isIEBrowser() {
      return this.isIE() || this.isEdge()
    }
  },
  watch: {
    visible(value) {
      if (!value) {
        this.loading = false
        this.resetImages()
      }
    }
  },
  created() {
    this.resetImages()
  },
  methods: {
    resetImages() {
      if (this.source) {
        this.source.cancel()
        this.source = undefined
      }
      this.waterCode = undefined
      let image
      for (const key in this.images) {
        if (this.images.hasOwnProperty(key)) {
          image = this.images[key]
          if (!this.isIEBrowser) {
            URL.revokeObjectURL(image.src)
          }
          image.src = ''
        }
      }
      image = undefined
    },
    handleInput(value) {
      this.waterCode = this.correctInput(value)
    },
    handleFilter() {
      this.$emit('search', this.waterCode)
      this.visible = !this.queryRestrict(this.waterCode)
    },
    beforeUpload(file) {
      this.resetImages()
      if (file.size === 0) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.blindWatermark_text8'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      this.loading = true
      this.visible = true
      this.source = axios.CancelToken.source()
      if (this.isIEBrowser || file.size > this.fileSizeLimit) {
        parseWatermarkFromImg(file, this.source.token).then(respond => {
          this.afterUpload()
          if (!this.visible) {
            return
          }
          const uuid = respond.data
          this.images.ori.src = createImgDownloadUrl(uuid + '_0.jpg')
          this.images.sub.src = createImgDownloadUrl(uuid + '_1.jpg')
          this.images.hrz.src = createImgDownloadUrl(uuid + '_2.jpg')
          this.images.vtc.src = createImgDownloadUrl(uuid + '_3.jpg')
        }).catch(this.afterUpload)
      } else {
        this.images.ori.src = URL.createObjectURL(file)
        // readAsDataURL(file).then(url => {
        //   // URL.createObjectURL(this.images.ori.src)
        //   this.images.ori.src = url
        // })
        readCodeFromImg(file, this.source.token).then(blob => {
          this.afterUpload()
          if (!this.visible) {
            return
          }
          const contentType = 'image/png'
          let start = 0
          let end = 12
          const lens = blob.slice(start, end)
          readAsArrayBuffer(lens).then(buffer => {
            if (!this.visible) {
              return
            }
            const array = new Uint32Array(buffer)
            const subLen = array[0]
            const hrzLen = array[1]
            const vtcLen = array[2]
            start = end
            end += subLen
            const subBlob = blob.slice(start, end, contentType)
            this.images.sub.src = URL.createObjectURL(subBlob)
            // readAsDataURL(subBlob).then(url => {
            //   this.images.sub.src = url
            // })
            start = end
            end += hrzLen
            const hrzBlob = blob.slice(start, end, contentType)
            this.images.hrz.src = URL.createObjectURL(hrzBlob)
            // readAsDataURL(hrzBlob).then(url => {
            //   this.images.hrz.src = url
            // })
            start = end
            end += vtcLen
            const vtcBlob = blob.slice(start, end, contentType)
            this.images.vtc.src = URL.createObjectURL(vtcBlob)
            // readAsDataURL(vtcBlob).then(url => {
            //   this.images.vtc.src = url
            // })
          })
        }).catch(this.afterUpload)
      }
      return false
    },
    afterUpload() {
      this.source = undefined
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
  .img-container {
    display: inline-block;
  }
  .img-container >>>.el-dialog__wrapper {
    /*遮罩层元素增加可穿透点击事件*/
    pointer-events: none;
    /*遮罩层透明*/
    background-color: rgba(0,0,0,0);
  }
  .img-container >>>.el-dialog {
    /*弹窗层元素不可穿透点击事件（不影响弹窗层元素的点击事件）*/
    pointer-events: auto;
  }
  .img-container >>>.el-dialog__body {
    max-height: 550px;
    padding: 10px;
  }
  .img-toolbar {
    width: 100%;
    padding: 0 5px;
  }
  .img-toolbar>.img-code .el-input {
    width: 200px;
  }
  .img-toolbar>.img-code .el-input >>>.el-input__inner {
    font-family: 'Consolas', 'DejaVu Sans Mono', 'Lucida Console', 'Courier New', monospace;
    letter-spacing: 1px;
  }
  .img-toolbar>.img-code .el-input >>>.el-input__inner::placeholder {
    font-family: sans-serif;
  }
  .img-toolbar>.img-tooltip >>>span:first-child {
    display: block;
    color: #2674b2;
  }
  .img-toolbar>.img-tooltip >>>.el-tooltip {
    position: relative;
  }
  .img-holder {
    width: 50%;
    height: 205px;
    padding: 5px;
    display: inline-block;
    position: relative;
    overflow: hidden;
  }
  .img-holder>.img-title {
    position: absolute;
    bottom: 0;
    width: 340px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: rgba(0, 0, 0, 0.6);
    color: #ffffff;
  }
  .img-holder .el-image {
    width: 340px;
    height: 200px;
  }
  .img-holder .img-auto >>>.el-image__inner {
    width: auto;
    height: auto;
    top: 0;
    left: 0;
    transform: translate(0, 0);
  }
  .img-holder .img-slot {
    text-align: center;
    line-height: 200px;
    font-size: 30px;
  }
</style>
