<template>
  <div v-if="labeled">
    <span v-for="(tip, index) in tips" :key="index">{{ tip }}</span>
    <el-tooltip v-if="exampled" effect="light" placement="bottom">
      <div slot="content" class="sample-container">
        <div class="sample-title">{{ $t('pages.blindWaterToolTip_text1') }}</div>
        <img class="sample-image" :src="require('@/assets/blindwatermark.png')" :alt="$t('pages.blindWaterToolTip_text6')">
        <div class="sample-content">
          <div class="sample-content__remark">{{ $t('pages.blindWaterToolTip_text2') }}</div>
          <div>{{ $t('pages.blindWaterToolTip_text3') }}</div>
          <div class="sample-content__code">WH7#<span class="sample-content__placeholder">%</span></div>
          <div class="sample-content__code">7C39<span class="sample-content__placeholder">%</span></div>
          <div class="sample-content__remark">
            <i18n path="pages.blindWaterToolTip_text4">
              <span slot="char1" class="sample-content__placeholder">%</span>
              <span slot="char2" class="sample-content__placeholder">#</span>
              <span slot="char3" class="sample-content__placeholder">$</span>
            </i18n>
          </div>
          <div>{{ $t('pages.blindWaterToolTip_text6') }}<span class="sample-content__code">#%WH79%7C3</span></div>
        </div>
      </div>
      <i class="el-icon-info" />
    </el-tooltip>
  </div>
  <el-tooltip v-else effect="dark" placement="bottom">
    <div slot="content">
      <div v-for="(tip, index) in tips" :key="index">{{ tip }}</div>
    </div>
    <i class="el-icon-info" />
  </el-tooltip>
</template>

<script>
export default {
  name: 'WatermarkTooltip',
  props: {
    labeled: {
      type: Boolean,
      default: false
    },
    pictured: {
      type: Boolean,
      default: false
    },
    // 示例，必须 labeled = true 才生效
    exampled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    tips() {
      const arr = [this.$t('pages.blindWatermark_tip1')]
      if (this.pictured) {
        arr.push(this.$t('pages.blindWatermark_tip2'))
      } else {
        arr.push(this.$t('pages.blindWatermark_tip3'))
      }
      return arr
    }
  }
}
</script>

<style lang="scss" scoped>
  .sample-container {
    width: 322px;
    .sample-title {
      font-size: 16px;
      font-weight: 700;
      padding: 7px 5px 6px;
      background: -webkit-gradient(linear, left top, left bottom, from(#dbdcdd), to(#8fa1b2));
      background: linear-gradient(#dbdcdd, #8fa1b2);
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #616f78;
    }
    .sample-image {
      border: 5px solid #8fa1b2;
    }
    .sample-content {
      div {
        line-height: 20px;
      }
      .sample-content__code {
        color: #409eff;
        font-family: 'Consolas', 'DejaVu Sans Mono', 'Lucida Console', 'Courier New', monospace;
        letter-spacing: 2px;
        font-size: 15px;
        font-weight: 700;
      }
      .sample-content__placeholder {
        color: red;
        margin: 0 2px;
      }
      .sample-content__remark {
        color: #2674b2;
        font-weight: 700;
      }
    }
  }
</style>
