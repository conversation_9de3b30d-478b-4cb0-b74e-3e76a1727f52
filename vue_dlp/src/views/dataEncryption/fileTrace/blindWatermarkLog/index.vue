<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="searchCon">
          <watermark-preview :correct-input="correctWaterCodeInput" :query-restrict="waterCodeCanSearch" @search="handleUploadSearch"/>
          <el-input
            v-model="query.waterCode"
            v-trim
            clearable
            :placeholder="$t('pages.blindWatermark_text')"
            class="code-input"
            :maxlength="10"
            @input="handleInput"
            @keyup.enter.native="handleFilter"
          />
          <watermark-tooltip />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="380"
            trigger="click"
            @show="refreshProcesses"
          >
            <Form ref="searchForm" label-position="right" :model="advancedQuery" label-width="100px">
              <FormItem :label="$t('table.waterCode')">
                <watermark-tooltip slot="tooltip"/>
                <el-input v-model="advancedQuery.waterCode" v-trim :maxlength="10" clearable @input="handleAdvancedInput"/>
              </FormItem>
              <!--<FormItem label="数据来源">
                <el-select v-model="advancedQuery.dataSources" clearable>
                  <el-option value="1" label="截图"/>
                </el-select>
              </FormItem>-->
              <FormItem :label="$t('table.userAccount')" :tooltip-content="$t('text.fuzzyQueryUnsupported')">
                <!--<user-term-select v-model="advancedQuery.userId"/>-->
                <el-input v-model="advancedQuery.userName" v-trim :maxlength="60" clearable/>
              </FormItem>
              <FormItem :label="$t('table.terminalName')">
                <user-term-select v-model="advancedQuery.terminalId" leaf-key="terminal"/>
              </FormItem>
              <FormItem :label="$t('table.processName1')">
                <el-select v-if="processes.length > 0" v-model="advancedQuery.processName" filterable clearable>
                  <el-option v-for="item in processes" :key="item" :value="item" :label="item"/>
                </el-select>
                <el-input v-else v-model="advancedQuery.processName" :maxlength="250" clearable :placeholder="$t('pages.blindWatermark_text9')"/>
              </FormItem>
              <FormItem :label="$t('table.operateTime')">
                <date-range-picker v-model="dateRange"/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetAdvancedQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleAdvancedSearch">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
          <audit-file-downloader ref="auditFileDownloader" v-permission="'302'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        </div>
      </div>
      <grid-table
        ref="logList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :show-pager="showPager"
        retain-pages
        :selectable="selectable"
        :custom-col="true"
        :after-load="row => queryVideoMethod = asyncGetLogVideoInfo(row, '303', undefined, undefined, 'opTimeStamp')"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.blindWatermarkLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ timestampFormatter(rowDetail.opTimeStamp) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.waterCode')">
            {{ rowDetail.waterCode }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            {{ rowDetail.terminalName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalGroup')">
            {{ rowDetail.terminalGroupName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.userAccount')">
            {{ rowDetail.userName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.userGroup')">
            {{ rowDetail.userGroupName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.processName1')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
          <!--<el-descriptions-item span="2" :label="$t('table.fileName')">
            <el-button
              type="text"
              style="margin-bottom: 0;padding: 0;width:50%;white-space:initial;text-align: left"
              :disabled="!rowDetail.uploadFileGuid || !rowDetail.backupServerId"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
          </el-descriptions-item>-->
          <!--<el-descriptions-item span="2" :label="$t('table.localFilePath1')">
            {{ rowDetail.localFilePath }}
          </el-descriptions-item>-->
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import WatermarkPreview from './WatermarkPreview'
import WatermarkTooltip from './WatermarkTooltip'
import DateRangePicker from '../DateRangePicker'
import UserTermSelect from '../UserTermSelect'
import { timestampFormatter } from '@/api/dataEncryption/fileTrace/documentTrack'
import { getProcesses, getLogPage } from '@/api/dataEncryption/fileTrace/blindWatermark'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo';

export default {
  name: 'BlindWatermarkLog',
  components: { WatermarkPreview, WatermarkTooltip, DateRangePicker, UserTermSelect },
  data() {
    return {
      waterCodeChars: '#$23456789ACEHKJMNRTWXYacehkjmnrtwxy',
      placeholderChar: '%',
      colModel: [
        { prop: 'opTimeStamp', label: 'operateTime', fixedWidth: '160', formatter: timestampFormatter },
        { prop: 'waterCode', label: 'waterCode', fixedWidth: '150' },
        // { prop: 'dataSources', label: '数据来源', width: '120' },
        { prop: 'terminalName', label: 'terminalName', width: '150' },
        { prop: 'terminalGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'userAccount', width: '150', formatter: this.userFormatter },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'processName', label: 'processName1', width: '100' },
        // { prop: 'localFilePath', label: 'localFilePath1', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('302,303'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.disabledFormatter, isShow: () => this.hasPermission('302') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('303') }
          ]
        }
      ],
      showPager: false,
      query: { // 查询条件
        page: 1,
        waterCode: undefined
      },
      advancedQuery: {},
      defaultAdvancedQuery: { // 高级查询
        page: 1,
        waterCode: undefined, // 水印信息
        dataSources: 1, // 数据来源
        // userId: undefined, // 操作员
        userName: undefined, // 操作员账号
        terminalId: undefined, // 终端名称
        processName: undefined, // 进程名
        startDate: 0, // 流转时间
        endDate: 0
      },
      realQuery: {},
      dateRange: [],
      tempTask: {},
      defaultTempTask: {
        backType: 24, // 盲水印和文档追踪共用一个备份类型
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      processes: [],
      queryVideoMethod: undefined
    }
  },
  created() {
    this.resetAdvancedQuery()
    addViewVideoBtn(this)
    this.realQuery = this.query
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    asyncGetLogVideoInfo,
    selectable(row, index) {
      return !this.disabledFormatter(row)
    },
    handleRefresh() {
      this.query = {
        page: 1,
        waterCode: undefined
      }
      this.realQuery = this.query
      this.$refs.logList.execRowDataApi({ ...this.query, limit: 50 })
      this.showPager = false
    },
    resetAdvancedQuery() {
      this.advancedQuery = Object.assign({}, this.defaultAdvancedQuery)
      this.dateRange = []
    },
    rowDataApi(data) {
      const searchQuery = Object.assign({}, this.realQuery, data)
      if (!this.showPager) {
        searchQuery.limit = 50
      }
      return getLogPage(searchQuery)
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    refreshProcesses() {
      getProcesses().then(res => {
        this.processes = Array.isArray(res.data) ? res.data : []
      }).catch(() => {
        this.processes = []
      })
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.backupServerId
      this.tempTask.fileGuid = row.uploadFileGuid
      this.tempTask.fileName = row.fileName || row.waterCode + '.png'
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs['auditFileDownloader'] && this.$refs['auditFileDownloader'].handleDownload(row)
    },
    userFormatter(row, data) {
      if (data) return data
      return this.$t('pages.user_not_login')
    },
    disabledFormatter(data, btn) {
      return !data.uploadFileGuid || !data.backupServerId
    },
    handleUploadSearch(waterCode) {
      this.query.waterCode = waterCode
      this.handleFilter()
    },
    correctWaterCodeInput(value) {
      if (value.length === 0) {
        return
      }
      let correctiveValue = ''
      let last
      let char
      for (let i = 0; i < value.length; i++) {
        last = char
        char = value.charAt(i)
        if (i > 0 && '#$'.indexOf(char) >= 0) { // 起始标记只能为第一个输入的字符
          continue
        }
        if (this.waterCodeChars.indexOf(char) >= 0) {
          correctiveValue += char
          continue
        }
        if (i === 0 && char === '￥') { // 中文输入法状态下直接输入$字符
          correctiveValue += '$'
          continue
        }
        if (char === this.placeholderChar && last !== char) {
          correctiveValue += char
        }
      }
      return correctiveValue.toUpperCase()
    },
    handleInput(value) {
      this.query.waterCode = this.correctWaterCodeInput(value)
    },
    handleAdvancedInput(value) {
      this.advancedQuery.waterCode = this.correctWaterCodeInput(value)
    },
    waterCodeCanSearch(value) {
      if (!value || value.length < 4) {
        return false
      }
      let char
      let count = 0
      for (let i = 0; i < value.length; i++) {
        char = value.charAt(i)
        if (this.waterCodeChars.indexOf(char) >= 0) {
          count++
        }
      }
      return count >= 4
    },
    handleFilter() {
      if (!this.query.waterCode) {
        this.handleRefresh()
        return
      }
      if (!this.waterCodeCanSearch(this.query.waterCode)) {
        this.$message({
          message: this.$t('pages.blindWatermark_text6'),
          type: 'error'
        })
        return
      }
      this.realQuery = this.query
      this.$refs.logList.execRowDataApi(this.query)
      this.showPager = true
    },
    handleAdvancedSearch() {
      if (this.advancedQuery.waterCode && !this.waterCodeCanSearch(this.advancedQuery.waterCode)) {
        this.$message({
          message: this.$t('pages.blindWatermark_text6'),
          type: 'error'
        })
        return
      }
      if (!this.dateRange || this.dateRange.length === 0) {
        this.advancedQuery.startDate = undefined
        this.advancedQuery.endDate = undefined
      } else {
        this.advancedQuery.startDate = Math.floor(this.dateRange[0].getTime() / 1000)
        this.advancedQuery.endDate = Math.ceil(this.dateRange[1].getTime() / 1000) + 24 * 60 * 60 - 1
      }
      if (!this.advancedQuery.waterCode && !this.advancedQuery.userId && !this.advancedQuery.terminalId &&
        !this.advancedQuery.processName && this.advancedQuery.startDate === 0 && this.advancedQuery.endDate === 0) {
        this.handleRefresh()
        return
      }
      if (this.advancedQuery.processName) {
        this.advancedQuery.processName = this.advancedQuery.processName.trim()
      }
      this.realQuery = this.advancedQuery
      this.$refs.logList.execRowDataApi(this.advancedQuery)
      this.showPager = true
    },
    timestampFormatter(timestamp) {
      return timestampFormatter(null, timestamp)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    selectionChangeEnd(rowDatas) {
      this.selection = rowDatas
    }
  }
}
</script>
<style lang="scss" scoped>
  .code-input {
    width: 200px;
  }
  .code-input >>>.el-input__inner {
    font-family: 'Consolas', 'DejaVu Sans Mono', 'Lucida Console', 'Courier New', monospace;
    letter-spacing: 1px;
  }
  .code-input >>>.el-input__inner::placeholder {
    font-family: sans-serif;
  }
  .el-popover {
    .el-select {
      width: 100%;
    }
  }
</style>
