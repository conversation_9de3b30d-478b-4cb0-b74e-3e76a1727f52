<template>
  <el-popover
    v-if="supported"
    v-model="visible"
    placement="bottom"
    width="306"
    popper-class="doc-popover"
    trigger="manual"
  >
    <div class="doc-info">
      <doc-info-item :label="$t('table.documentId')" :content="temp.documentId"/>
      <doc-info-item :label="$t('table.circulationId')" :content="temp.circulationId"/>
      <doc-info-item :label="$t('table.userAccount')" :content="temp.userName"/>
      <doc-info-item :label="$t('table.terminalName')" :content="temp.terminalName"/>
      <doc-info-item :label="$t('table.circulationType')" :content="temp.circulationType"/>
      <doc-info-item :label="$t('table.processName')" :content="temp.processName"/>
      <doc-info-item :label="$t('table.circulationTime')" :content="temp.circulationTime"/>
    </div>
    <div class="doc-cancel">
      <el-button size="mini" @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
    <el-upload
      slot="reference"
      class="doc-upload"
      action=""
      accept=".doc,.docx,.ppt,.pptx,.xls,.xlsx,.pdf"
      :before-upload="beforeUpload"
    >
      <el-button size="mini" type="primary" icon="el-icon-upload" :loading="loading">{{ label }}</el-button>
    </el-upload>
  </el-popover>
  <common-downloader
    v-else
    :loading="loading"
    :name="$t('pages.documentTrackInfoTool') + '.exe'"
    :button-name="$t('pages.documentTrack_btn3')"
    button-type="primary"
    button-size="mini"
    @download="handleDownload"
  />
</template>

<script>
import {
  isSupportedReadDocInfo,
  readInfoFromFile,
  timestampFormatter,
  parseCirculationType,
  getProcessMapping,
  formatProcess
} from '@/api/dataEncryption/fileTrace/documentTrack'
import DocInfoItem from './DocInfoItem'
import { downloadTool } from '@/api/dataEncryption/encryption/fileOutgoing'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'DocumentUpload',
  components: { DocInfoItem, CommonDownloader },
  props: {
    label: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      supported: false,
      loading: false,
      visible: false,
      temp: {}
    }
  },
  created() {
    isSupportedReadDocInfo().then(respond => {
      this.supported = !!respond.data
    })
    getProcessMapping().then(respond => {
      this.$store.commit('commonData/SET_DOCTRACK_PROCESSES', respond.data)
    })
    document.addEventListener('click', this.closePopover)
  },
  destroyed() {
    document.removeEventListener('click', this.closePopover)
  },
  methods: {
    closePopover() {
      this.visible = false
    },
    beforeUpload(file) {
      if (file.size === 0) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.documentTrack_text26'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      this.loading = true
      // this.visible = false
      this.temp = {}
      readInfoFromFile(file).then(data => {
        this.loading = false
        this.handleUpload(data, file)
      }).catch(() => {
        this.loading = false
      })
      return false
    },
    handleUpload(data, file) {
      const res = data.res
      if (res === 0) { // 提取文档追踪信息成功
        this.temp = {
          documentId: data.documentId,
          circulationId: data.circulationId,
          userName: data.userName,
          terminalName: data.terminalName,
          circulationType: parseCirculationType(data.trackOpprtmoment),
          processName: formatProcess(this.$store.getters.doctrackProcesses, data.processName, true),
          circulationTime: timestampFormatter(null, data.circulationTime)
        }
        this.visible = true
        this.$emit('uploaded', data, file)
        return
      }
      if (res === 1) { // 不存在文档追踪信息
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.documentTrack_text26'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (res < 0) { // 提取文档追踪信息异常
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.documentTrack_text27'),
          type: 'error',
          duration: 2000
        })
      }
    },
    handleDownload(file) {
      this.loading = true
      downloadTool({ toolType: 6 }, file).finally(() => { this.loading = false })
    }
  }
}
</script>

<style lang="scss" scoped>
  .doc-info {
    margin-top: 5px;
  }
  .doc-cancel {
    text-align: right;
    margin: 0 5px 5px;
    .el-button {
      height: 25px;
      padding: 6px 10px;
    }
  }
  .doc-upload {
    display: inline-block;
  }
  .doc-popover {
    padding: 0 !important;
  }
</style>
