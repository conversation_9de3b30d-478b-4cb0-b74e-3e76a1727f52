<template>
  <!--<el-autocomplete
    :value="value"
    clearable
    :fetch-suggestions="fetchSuggestions"
    :placeholder="placeholder"
    @input="handleInput"
  />-->
  <el-select
    :value="value"
    :multiple="multiple"
    :collapse-tags="collapseTags"
    :multiple-limit="multipleLimit"
    :filterable="filterable"
    :filter-method="filterMethod"
    :clearable="clearable"
    :placeholder="placeholder"
    @change="handleInput"
    @visible-change="handleVisibleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
      <span style="float: left">{{ item.label }}</span>
      <span v-if="detailed" style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
    </el-option>
  </el-select>
</template>

<script>
import { getProcessMapping, formatProcess } from '@/api/dataEncryption/fileTrace/documentTrack'

export default {
  name: 'ProcessInput',
  props: {
    value: {
      type: [String, Array],
      default: undefined
    },
    detailed: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
    multipleLimit: {
      type: Number,
      default: 0
    },
    clearable: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: undefined
    },
    typeFilter: {
      type: Function,
      default(type) {
        return type !== 'DLP'
      }
    }
  },
  data() {
    return {
      processes: [],
      filteredData: [],
      options: []
    }
  },
  watch: {
    '$store.getters.doctrackProcesses'(processes, oldValue) {
      this.initProcesses(processes)
    }
  },
  created() {
    getProcessMapping().then(respond => {
      this.$store.commit('commonData/SET_DOCTRACK_PROCESSES', respond.data)
    })
  },
  methods: {
    // fetchSuggestions(queryString, callback) {
    //   if (queryString) {
    //     const queryLowerCase = queryString.toLowerCase()
    //     const results = this.processes.filter(item => item.value.toLowerCase().indexOf(queryLowerCase) >= 0)
    //     callback(results)
    //   } else {
    //     callback(this.processes)
    //   }
    // },
    initProcesses(processes) {
      if (processes) {
        this.processes = processes
        this.filteredData = this.processes.filter(item => this.typeFilter(item.type))
        this.filterMethod(this.value)
      }
    },
    formatProcess(processName, showDetail = false) {
      return formatProcess(this.processes, processName, showDetail)
    },
    handleInput(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    },
    handleVisibleChange(visible) {
      if (!visible) {
        this.options = this.filteredData
      }
    },
    filterMethod(value) {
      const query = value && value.trim().toLowerCase()
      if (query) {
        this.options = this.filteredData.filter(item => {
          if (item.label.toLowerCase().indexOf(query) >= 0) {
            return true
          }
          return this.detailed && item.value.toLowerCase().indexOf(query) >= 0
        })
      } else {
        this.options = this.filteredData
      }
    }
  }
}
</script>
