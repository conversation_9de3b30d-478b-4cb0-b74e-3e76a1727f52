<template>
  <div class="doc-info-item">
    <div class="doc-info-item__label ellipsis">
      <slot name="label"><span :title="label">{{ label }}</span></slot>
    </div>
    <div class="doc-info-item__content">
      <slot>
        <div class="doc-info-item__content-inner" :style="{ maxWidth: contentWidth }">
          <overflow-tooltip :content="content" :invalid="invalid"/>
        </div>
        <el-tooltip v-if="invalid" :content="errormsg" placement="top-end">
          <i class="el-icon-info"/>
        </el-tooltip>
        <el-tooltip v-if="copyable" :content="copytips" placement="bottom">
          <el-button class="doc-info-item__content-copier" icon="el-icon-document-copy" @click="copyContent"/>
        </el-tooltip>
      </slot>
    </div>
  </div>
</template>

<script>
import OverflowTooltip from './OverflowTooltip'
import { copy } from '@/api/dataEncryption/fileTrace/documentTrack'

export default {
  name: 'DocInfoItem',
  components: { OverflowTooltip },
  props: {
    label: {
      type: String,
      default: undefined
    },
    content: {
      type: String,
      default: undefined
    },
    invalid: {
      type: Boolean,
      default: false
    },
    errormsg: {
      type: String,
      default: undefined
    },
    copyable: {
      type: Boolean,
      default: false
    },
    copytips: {
      type: String,
      default() {
        return this.$t('button.copy')
      }
    }
  },
  computed: {
    contentWidth() {
      const text = this.content
      if (text == null) {
        return '100%'
      }
      let minus = 0
      if (this.invalid) {
        minus += 18
      }
      if (this.copyable) {
        minus += 45
      }
      return minus > 0 ? `calc(100% - ${minus}px)` : '100%'
    }
  },
  methods: {
    copyContent() {
      copy(this.content, r => {
        this.$message({
          message: this.$t('pages.copy_success'),
          type: 'success',
          duration: 1000
        });
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .doc-info-item {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
  }
  .doc-info-item__label {
    width: 70px;
    margin-right: 5px;
    display: inline-block;
    font-weight: 700;
    text-align: right;
    cursor: default;
    &:after {
      content: '';
    }
  }
  .doc-info-item__content {
    width: calc(100% - 80px);
    padding-left: 15px;
    display: inline-block;
    vertical-align: top;
    .doc-info-item__content-inner {
      display: inline-block;
    }
    .doc-info-item__content-copier {
      margin: 0 0 0 15px;
    }
  }
</style>
