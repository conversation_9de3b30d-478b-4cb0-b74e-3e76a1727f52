<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="searchCon">
          <document-upload :label="$t('pages.documentTrack_text14')" @uploaded="handleUploaded"/>
          <el-input
            v-model="query.circulationId"
            v-trim
            clearable
            :placeholder="$t('pages.documentTrack_text15')"
            style="width: 210px;"
            :maxlength="20"
            @input="handleInput"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="logList"
        :col-model="colModel"
        :multi-select="false"
        :row-data-api="rowDataApi"
        :show-pager="showPager"
        :custom-col="true"
        retain-pages
        :after-load="row => queryVideoMethod = asyncGetLogVideoInfo(row, undefined, undefined, undefined, 'circulationTime')"
      />
    </div>
    <circulation-detail
      v-if="visibleDialog"
      ref="detailDialog"
    />
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import CirculationDetail from '../circulationDetail'
import DocumentUpload from '../DocumentUpload'
import {
  getOriginsPage,
  timestampFormatter,
  parseCirculationType,
  getProcessMapping,
  formatProcess
} from '@/api/dataEncryption/fileTrace/documentTrack'
import { onlyInt } from '@/utils/inputLimit'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo';

export default {
  name: 'DocumentOrigins',
  components: { CirculationDetail, DocumentUpload },
  data() {
    return {
      colModel: [
        { prop: 'circulationTime', label: 'circulationTime', width: '160', formatter: timestampFormatter },
        { prop: 'fileName', label: 'documentName', width: '150' },
        { prop: 'terminalName', label: 'terminalName', width: '150' },
        { prop: 'terminalGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'userAccount', width: '150', formatter: this.userFormatter },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'trackOpprtmoment', label: 'circulationType', width: '150', formatter: this.circulationTypeFormat },
        { prop: 'processName', label: 'processName', width: '100', formatter: this.processFormatter },
        { prop: 'circulationId', label: 'circulationId', width: '180' },
        // { prop: 'documentId', label: 'documentId', fixedWidth: '272' },
        // { prop: 'occurrenceTime', label: '流转时间', width: '120' },
        { label: 'operate', type: 'button', fixedWidth: '80', fixed: 'right', hidden: !this.hasPermission('385'),
          buttons: [
            { label: 'circulationDetail', click: this.handleDetail }
          ]
        }
      ],
      showPager: false,
      query: { // 查询条件
        page: 1,
        circulationId: undefined, // 流转ID
        documentId: undefined // 文档ID
      },
      visibleDialog: false,
      queryVideoMethod: undefined
    }
  },
  created() {
    getProcessMapping().then(respond => {
      this.$store.commit('commonData/SET_DOCTRACK_PROCESSES', respond.data)
    })
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    asyncGetLogVideoInfo,
    handleRefresh() {
      this.query = {
        page: 1,
        circulationId: undefined,
        documentId: undefined
      }
      this.$refs.logList.execRowDataApi({ ...this.query, limit: 50 })
      this.showPager = false
    },
    handleUploaded(data, file) {
      this.query.circulationId = data.circulationId
      this.query.documentId = data.documentId
      this.$refs.logList.execRowDataApi(this.query)
      this.showPager = true
    },
    handleInput(value) {
      this.query.circulationId = onlyInt(value)
    },
    handleFilter() {
      if (!this.query.circulationId) {
        // this.$message({
        //   message: this.$t('pages.documentTrack_text15'),
        //   type: 'error'
        // })
        this.handleRefresh()
        return
      }
      this.query.documentId = undefined
      this.$refs.logList.execRowDataApi(this.query)
      this.showPager = true
    },
    handleDetail(row) {
      this.visibleDialog = true
      this.$nextTick(() => {
        this.$refs.detailDialog.show(row.documentId, row.circulationId)
      })
    },
    rowDataApi(data) {
      const searchQuery = Object.assign({}, this.query, data)
      if (!this.showPager) {
        searchQuery.limit = 50
      }
      return getOriginsPage(searchQuery)
    },
    userFormatter(row, data) {
      if (data) return data
      return this.$t('pages.user_not_login')
    },
    processFormatter(row, data) {
      return formatProcess(this.$store.getters.doctrackProcesses, data)
    },
    circulationTypeFormat(row, data) {
      return parseCirculationType(data)
    }
  }
}
</script>
