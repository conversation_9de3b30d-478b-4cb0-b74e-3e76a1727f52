<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="dlgVisible"
    :width="dialogStatus == 'create' ? '500px' : '640px'"
  >
    <Form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="100px"
    >
      <FormItem v-if="dialogStatus == 'create'" label-width="100px" label="阅读范围">
        <tree-select
          ref="chooseOperData"
          multiple
          is-filter
          check-strictly
          :disabled="!formable"
          :leaf-key="'user'"
          node-key="id"
          :checked-keys="checkedKeys"
          :width="296"
          @change="changeData"
        />
      </FormItem>
      <FormItem
        :label="$t('pages.treatment')"
        label-width="100px"
      >
        <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
          <div slot="content">
            指定：代表拥有选中部门或操作员权限；例外：排除选中部门或操作员权限；
            <!-- <i18n path="pages.repeatNameDealTypeContent">
              <template slot="tip">{{ tip }}</template>
              <br slot="br"/>
            </i18n> -->
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
        <el-radio-group v-model="temp.handleMode" tooltip-content="">
          <el-radio :label="0">{{ $t('pages.optioned') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.exception') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem v-show="isIncludeDeptOption" label-width="100px" :label="$t('pages.readPermission_info1')" tooltip-content="适用于部门">
        <el-radio-group v-model="temp.isCascade">
          <el-radio :label="0">{{ $t('text.yes') }}</el-radio>
          <el-radio :label="1" style="margin-left: 14px">{{ $t('text.no') }}</el-radio>
        </el-radio-group>
      </formitem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dlgVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'AddPermissionDlg',
  props: {
    permissionDeptList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      dlgVisible: false,
      dialogStatus: 'create',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.readPermissionOption'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.readPermissionOption'), 'create')
      },
      checkedKeys: [],
      rules: {},
      temp: {},
      defaultTemp: {
        PermissionIds: [''],
        deptDatas: {},
        handleMode: 0, // 部门处理方式
        operHandleMode: 0, // 操作员处理方式
        isCascade: 0  // 是否级联
      },
      formable: true,
      submitting: false,
      isIncludeDeptOption: false
    }
  },
  methods: {
    resetTemp() {
      this.handleMode = 0
      this.isCascade = 0
      this.temp = Object.assign({}, this.defaultTemp)
    },
    show(dlgStatus, data) {
      this.dlgVisible = true
      this.resetTemp()
      this.dialogStatus = dlgStatus
      this.$nextTick(() => {
        if (dlgStatus == 'create') {
          this.$refs['chooseOperData'].clearSelectedNode()
        }
      })
    },
    createData() {
      const selects = this.$refs.chooseOperData.getSelectedNode() || []
      this.temp.deptDatas = selects.filter(d => d.type == '4')
      this.temp.operDatas = selects.filter(d => d.type == '2')
      this.temp.operHandleMode = this.temp.handleMode
      console.log('this.temp.deptDatas', this.temp.deptDatas)
      console.log('this.temp.operDatas', this.temp.operDatas)
      if (this.temp.deptDatas.length == 0 && this.temp.operDatas.length == 0) {
        this.$message({
          message: this.$t('pages.deptChoosePrompt1'),
          type: 'error',
          duration: 3000
        })
        return
      }
      this.$emit('handle-operator', 'create', this.temp)
      this.dlgVisible = false
    },
    changeData(data) {
      // 当用户选择包含子部门时，才显示包含子部门
      const event = (e) => e.includes('G')
      this.isIncludeDeptOption = data.some(event)
      // console.log('changeData', data.some(event))
    }
  }
}
</script>
