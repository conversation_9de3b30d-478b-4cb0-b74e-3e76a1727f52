<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="dlgVisible"
    :width="dialogStatus == 'create' ? '500px' : '640px'"
  >

    <Form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="right"
      label-width="100px"
    >
      <!--      <el-tabs ref="tabs" v-model="tabName" :before-leave="changeTab" style="height: 320px;">-->
      <!--        <el-tab-pane :label="$t('pages.dept')" name="deptTab" style="padding: 5px; overflow: auto;">-->
      <!--        </el-tab-pane>-->
      <FormItem v-if="dialogStatus == 'create'" label-width="100px" :label="$t('pages.chooseDept')">
        <tree-select
          ref="chooseDeptData"
          multiple
          is-filter
          check-strictly
          :disabled="!formable"
          :data="permissionDeptList"
          node-key="id"
          :checked-keys="checkedKeys"
          :width="296"
        />
      </FormItem>
      <div v-if="dialogStatus == 'update'">
        <el-row>
          <el-col :span="24">
            <grid-table
              ref="deptChooseList"
              :stripe="false"
              row-key="dataId"
              default-expand-all
              :height="240"
              :col-model="colModel"
              :cell-style="cellStyle"
              :multi-select="false"
              :row-data-api="rowDataApi"
              :show-pager="false"
            />
          </el-col>
        </el-row>
      </div>
      <FormItem
        :label="$t('pages.treatment')"
        label-width="100px"
      >
        <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
          <div slot="content">
            指定：代表拥有选中部门或操作员权限；例外：排除选中部门或操作员权限；
            <!--            <i18n path="pages.repeatNameDealTypeContent">-->
            <!--              <template slot="tip">{{ tip }}</template>-->
            <!--              <br slot="br"/>-->
            <!--            </i18n>-->
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
        <el-radio-group v-model="temp.handleMode">
          <el-radio :label="0">{{ $t('pages.optioned') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.exception') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem label-width="100px" :label="$t('pages.readPermission_info1')">
        <el-radio-group v-model="temp.isCascade">
          <el-radio :label="0">{{ $t('text.yes') }}</el-radio>
          <el-radio :label="1" style="margin-left: 14px">{{ $t('text.no') }}</el-radio>
        </el-radio-group>
      </formitem>

      <!--      操作员-->
      <!--        <el-tab-pane :label="$t('table.user')" name="operTab" style="padding: 5px; overflow: auto;">-->
      <!--          <FormItem v-if="dialogStatus == 'create'" label-width="100px" :label="$t('pages.selectUser')">-->
      <!--            <tree-select-->
      <!--              ref="chooseOperData"-->
      <!--              clearable-->
      <!--              multiple-->
      <!--              is-filter-->
      <!--              check-strictly-->
      <!--              :disabled="!formable"-->
      <!--              :leaf-key="'user'"-->
      <!--              :disabled-nodes="disabledNodes"-->
      <!--              node-key="id"-->
      <!--              :checked-keys="checkedOperKeys"-->
      <!--              :width="296"-->
      <!--            />-->
      <!--          </FormItem>-->
      <!--          <div v-if="dialogStatus == 'update'">-->
      <!--            <el-row>-->
      <!--              <el-col :span="24">-->
      <!--                <grid-table-->
      <!--                  ref="operChooseList"-->
      <!--                  :stripe="false"-->
      <!--                  row-key="dataId"-->
      <!--                  default-expand-all-->
      <!--                  :height="240"-->
      <!--                  :col-model="operColModel"-->
      <!--                  :cell-style="cellStyle"-->
      <!--                  :multi-select="false"-->
      <!--                  :row-data-api="rowDataApi2"-->
      <!--                  :show-pager="false"-->
      <!--                />-->
      <!--              </el-col>-->
      <!--            </el-row>-->
      <!--          </div>-->

      <!--          <FormItem-->
      <!--            :label="$t('pages.treatment')"-->
      <!--            label-width="100px"-->
      <!--          >-->
      <!--            <el-radio-group v-model="temp.operHandleMode">-->
      <!--              <el-radio :label="0">{{ $t('pages.optioned') }}</el-radio>-->
      <!--              <el-radio :label="1">{{ $t('pages.exception') }}</el-radio>-->
      <!--            </el-radio-group>-->
      <!--          </FormItem>-->
      <!--        </el-tab-pane>-->
      <!--      </el-tabs>-->
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dlgVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>

</template>

<script>

export default {
  name: 'DeptHandleDlg',
  props: {
    permissionDeptList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      dlgVisible: false,
      dialogStatus: 'create',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.readPermissionOption'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.readPermissionOption'), 'create')
      },
      checkedKeys: [],
      checkedOperKeys: [],
      type: 1, // 1:部门 2:操作员
      tabName: 'deptTab',
      rules: {},
      temp: {},
      defaultTemp: {
        PermissionIds: [''],
        deptDatas: {},
        handleMode: 0, // 部门处理方式
        operHandleMode: 0, // 操作员处理方式
        isCascade: 0  // 是否级联
      },
      colModel: [
        { prop: 'designatedDept', label: '阅读范围', width: '240', formatter: this.nameFormatter },
        { prop: 'include', label: '包含子部门', width: '120', sort: true, formatter: this.includeFormatter }
      ],
      operColModel: [
        { prop: 'designatedDept', label: 'operatorSelection', width: '240', formatter: this.nameFormatter },
        { prop: 'include', label: '包含子部门', width: '120', sort: true, formatter: this.includeFormatter }
      ],
      deptRowDatas: [],
      operRowDatas: [],
      rowGroupNameMap: [this.$t('pages.optionedDept'), this.$t('pages.assignmentOperator'), this.$t('pages.excepeDept'), this.$t('pages.exceptionalOperator')],
      formable: true,
      submitting: false
    }
  },
  computed: {
    deptChooseList() {
      return this.$refs['deptChooseList']
    },
    chooseDeptData() {
      return this.$refs['chooseDeptData']
    }
  },
  methods: {
    changeConfig(value) {
      this.type = value
    },
    changeTab(activeName, oldActiveName) {
    },
    show(dlgStatus, data) {
      console.log('show', data)
      this.dlgVisible = true
      this.resetTemp()
      this.dialogStatus = dlgStatus
      this.$nextTick(() => {
        if (dlgStatus == 'create') {
          this.$refs['chooseDeptData'].clearSelectedNode()
        } else if (dlgStatus == 'update') {
          // 部门显示
          // this.deptRowDatas[0].children = data[0].children
          // this.deptRowDatas[2].children = data[2].children
          // console.log('update', data)
          // const assignDeptLength = data[0].children.length;
          // const exceptDeptLength = data[2].children.length
          // if (assignDeptLength + exceptDeptLength == 1) {
          //   let deptRowData
          //   if (assignDeptLength == 1) {
          //     deptRowData = data[0].children[0]
          //   } else if (exceptDeptLength == 1) {
          //     deptRowData = data[2].children[0]
          //   }
          //   if (deptRowData) {
          //     this.temp.handleMode = deptRowData.handleMode
          //     this.temp.isCascade = deptRowData.isCascade
          //   }
          // }
          // this.$refs['deptChooseList'].execRowDataApi()
          // 操作员显示
          // this.operRowDatas[0].children = data[1].children
          // this.operRowDatas[1].children = data[3].children
          // console.log('update', this.operRowDatas)
          // const assignOperLength = data[1].children.length;
          // const exceptOperLength = data[3].children.length
          // if (assignOperLength + exceptOperLength == 1) {
          //   let operRowData
          //   if (assignOperLength == 1) {
          //     operRowData = data[1].children[0]
          //   } else if (exceptOperLength == 1) {
          //     operRowData = data[3].children[0]
          //   }
          //   if (operRowData) {
          //     this.temp.operHandleMode = operRowData.handleMode
          //     // this.temp.isCascade = operRowData.isCascade
          //   }
          // }
          // this.$refs['operChooseList'].execRowDataApi()

          // 部门和操作员一起显示
          this.deptRowDatas[0].children = data[0].children
          this.deptRowDatas[1].children = data[1].children
          this.deptRowDatas[2].children = data[2].children
          this.deptRowDatas[3].children = data[3].children
          console.log('update', this.deptRowDatas)
          const assignDeptLength = data[0].children.length;
          const exceptDeptLength = data[2].children.length
          if (assignDeptLength + exceptDeptLength == 1) {
            let deptRowData
            if (assignDeptLength == 1) {
              deptRowData = data[0].children[0]
            } else if (exceptDeptLength == 1) {
              deptRowData = data[2].children[0]
            }
            if (deptRowData) {
              this.temp.handleMode = deptRowData.handleMode
              this.temp.isCascade = deptRowData.isCascade
            }
          }
          this.$refs['deptChooseList'].execRowDataApi()
        }
      })
    },
    groupIdSelectChange(data) {
      this.temp.PermissionIds = data
    },
    resetTemp() {
      this.handleMode = 0
      this.isCascade = 0
      this.temp = Object.assign({}, this.defaultTemp)
      this.deptRowDatas = [{ label: this.rowGroupNameMap[0], type: 'group', dataId: 'Group1', handleMode: 0, children: [] },
        { label: this.rowGroupNameMap[1], type: 'oper', dataId: 'Oper1', handleMode: 0, children: [] },
        { label: this.rowGroupNameMap[2], type: 'group', dataId: 'Group2', handleMode: 1, children: [] },
        { label: this.rowGroupNameMap[3], type: 'oper', dataId: 'Oper2', handleMode: 1, children: [] }]
      // 区分部门和操作员tab
      // this.deptRowDatas = [{ label: this.rowGroupNameMap[0], type: 'group', dataId: 'Group1', handleMode: 0, children: [] },
      //   { label: this.rowGroupNameMap[2], type: 'group', dataId: 'Group2', handleMode: 1, children: [] }]
      // this.operRowDatas = [{ label: this.rowGroupNameMap[1], type: 'oper', dataId: 'Oper1', handleMode: 0, children: [] },
      //   { label: this.rowGroupNameMap[3], type: 'oper', dataId: 'Oper2', handleMode: 1, children: [] }]
    },
    rowDataApi() {
      return new Promise((resolve, reject) => { resolve({ data: this.deptRowDatas }) })
    },
    rowDataApi2() {
      return new Promise((resolve, reject) => { resolve({ data: this.operRowDatas }) })
    },
    createData() {
      this.temp.deptDatas = this.$refs.chooseDeptData.getSelectedNode() || []
      this.temp.operDatas = this.$refs.chooseOperData.getSelectedNode() || []
      console.log('this.temp.operDatas', this.temp.operDatas)
      if (this.temp.deptDatas.length == 0 && this.temp.operDatas.length == 0) {
        this.$message({
          message: '请选择部门|操作员',
          type: 'error',
          duration: 3000
        })
        return
      }
      this.$emit('handle-operator', 'create', this.temp)
      this.dlgVisible = false
    },
    updateData() {
      // this.deptRowDatas.forEach(item => {
      //   if (!item.children) item.children = []
      //   item.children.forEach(child => {
      //     child.dataId = 'G' + child.id
      //     console.log('child.handleMode', child.handleMode)
      //     child.type = this.temp.handleMode == 0 ? 'AG' : 'EG'
      //     child.isCascade = this.temp.isCascade
      //     child.handleMode = this.temp.handleMode
      //   })
      // })
      // console.log('this.temp.operhandleMode', this.temp.operHandleMode)
      // this.operRowDatas.forEach(item => {
      //   if (!item.children) item.children = []
      //   item.children.forEach(child => {
      //     child.dataId = 'U' + child.id
      //     // child.isCascade = this.temp.isCascade
      //     child.handleMode = this.temp.operHandleMode
      //     child.type = this.temp.operHandleMode == 0 ? 'AU' : 'EU'
      //   })
      // })
      // const rowDatas = [{ label: this.rowGroupNameMap[0], type: 'group', dataId: 'Group1', handleMode: 0, children: [] },
      //   { label: this.rowGroupNameMap[1], type: 'oper', dataId: 'Oper1', handleMode: 0, children: [] },
      //   { label: this.rowGroupNameMap[2], type: 'group', dataId: 'Group2', handleMode: 1, children: [] },
      //   { label: this.rowGroupNameMap[3], type: 'oper', dataId: 'Oper2', handleMode: 1, children: [] }]
      // rowDatas[0].children = this.deptRowDatas[0].children != undefined ? this.deptRowDatas[0].children : []
      // rowDatas[1].children = this.operRowDatas[0].children != undefined ? this.operRowDatas[0].children : []
      // rowDatas[2].children = this.deptRowDatas[1].children != undefined ? this.deptRowDatas[1].children : []
      // rowDatas[3].children = this.operRowDatas[1].children != undefined ? this.operRowDatas[1].children : []
      // console.log('updateData 1111111', rowDatas)
      // this.$emit('handle-operator', 'update', JSON.parse(JSON.stringify(rowDatas)))

      this.deptRowDatas.forEach(item => {
        if (!item.children) item.children = []
        let prefix = ''
        if (item.type == 'group') {
          prefix = 'G'
        }
        if (item.type == 'oper') {
          prefix = 'U'
        }
        item.children.forEach(child => {
          child.dataId = prefix + child.id
          console.log('child.handleMode', child.handleMode)
          child.type = (this.temp.handleMode == 0 ? 'A' : 'E') + prefix
          if (item.type == 'group') {
            child.isCascade = this.temp.isCascade
          }
          child.handleMode = this.temp.handleMode
        })
      })
      this.$emit('handle-operator', 'update', JSON.parse(JSON.stringify(this.deptRowDatas)))

      // 清理数据
      this.deptRowDatas.splice(0, this.deptRowDatas.length)
      this.operRowDatas.splice(0, this.operRowDatas.length)
      this.resetTemp()
      console.log('重置后', this.deptRowDatas)
      this.rowDataApi()
      this.rowDataApi2()
      this.dlgVisible = false
    },
    changeTreeSelect(datas) {
      this.temp.PermissionIds = datas
    },
    nameFormatter: function(row, data) {
      if (row.children && row.children.length > 0 && row.type == 'group') {
        return '<label>' + row.label + '</label>'
      }
      if (row.children && row.children.length > 0 && row.type == 'oper') {
        return '<label>' + row.label + '</label>'
      }
      return this.html2Escape(data)
    },
    includeFormatter: function(row, data) {
      if (row.type == 'group') return ''
      let str = ''
      if (row.handleMode >= 0) {
        if (row.isCascade == 0) str = this.$t('pages.readPermission_info1')
        if (row.isCascade == 1) str = this.$t('pages.readPermission_info2')
      }
      return str
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (row.type == 'group' && row.children && row.children.length == 0) {
        return 'display: none'
      }
      if (row.type == 'oper' && (!row.children || row.children && row.children.length == 0)) {
        return 'display: none'
      }
      if ((columnIndex == 1 || columnIndex == 2) && row.type == 'group') {
        return 'border-right: 0px'
      }
      if ((columnIndex == 1 || columnIndex == 2) && row.type == 'oper') {
        return 'border-right: 0px'
      }
      return ''
    },
    // 操作员
    disabledNodes(data, node) {
      console.log('disabledNodes data', data)
      console.log('disabledNodes node', node)
      return data.type == '4'
    }
  }
}
</script>
