<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        <span>{{ textMap[dialogStatus] }}</span>
        <el-tooltip slot="content" class="item" effect="dark" placement="right" :content="'特别注意：当策略配置根部门且包含子部门时,旧终端将没有阅读权限,请升级终端！'">
          <div></div>
          <i class="el-icon-info"/>
        </el-tooltip>
      </div>
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <FormItem >
          <stg-dept-choose ref="stgDeptChoose" :formable="formable" :permission-tree-type="permissionTreeNodeType" :user-map="userMap" @dept-handle-add="deptHandleAdd" @dept-handle-update="deptHandleUpdate"></stg-dept-choose>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dlgCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <add-permission-dlg ref="addPermission" @handle-operator="dealWithDeptDlg"></add-permission-dlg>
    <dept-handle-dlg ref="deptHandleDlg" :permission-dept-list="permissionTreeData" @handle-operator="dealWithDeptDlg"></dept-handle-dlg>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importReadPermissionStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import {
  getStrategyPage, getStrategyByName, getStrategyById, createStrategy, updateStrategy, deleteStrategy
} from '@/api/dataEncryption/encryption/readPermission'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { exportStg } from '@/api/stgCommon'
import { listUser } from '@/api/system/terminalManage/user'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter,
  entityLink, refreshPage, buttonFormatter, isSameTimestamp, initTimestamp } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import StgDeptChoose from './stgDeptChoose';
import DeptHandleDlg from './deptHandleDlg';
import AddPermissionDlg from '@/views/dataEncryption/encryption/readPermission/addPermissionDlg.vue';

export default {
  name: 'ReadPermission',
  components: { AddPermissionDlg, DeptHandleDlg, StgDeptChoose, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 88,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'cascadeDeptId', label: 'cascadeDeptId', width: '140', formatter: this.permissionFormatter },
        { prop: 'assignDeptId', label: 'assignDeptId', width: '140', formatter: this.permissionFormatter },
        { prop: 'cascadeExceptDeptId', label: 'cascadeExceptDeptId', width: '140', formatter: this.permissionFormatter },
        { prop: 'exceptDeptId', label: 'exceptDeptId', width: '140', formatter: this.permissionFormatter },
        { prop: 'assignOperatorId', label: this.$t('pages.assignmentOperator'), width: '140', formatter: this.userFormatter },
        { prop: 'exceptOperatorId', label: this.$t('pages.exceptionalOperator'), width: '140', formatter: this.userFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      permissionTreeData: [],
      permissionMap: {},
      userMap: {},
      permissionTreeNodeType: undefined,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.readPermission'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.readPermission'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  watch: {
    '$store.getters.deptTree'() {
      this.initPermissionTreeNode()
    }
  },
  created() {
    initTimestamp(this)
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.initPermissionTreeNode()
    this.initUserMap()
  },
  activated() {
    if (!isSameTimestamp(this, 'Department')) {
      this.initPermissionTreeNode()
      this.initUserMap()
    }
  },
  methods: {
    stgDeptChoose() {
      return this.$refs.stgDeptChoose
    },
    deptHandleDlg() {
      return this.$refs.deptHandleDlg
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    initPermissionTreeNode: function() {
      getDeptTreeFromCache().then(respond => {
        this.permissionTreeData = respond.data
        if (respond.data && respond.data.length > 0) {
          this.permissionTreeNodeType = respond.data[0].type
        }
        this.initPermissionMap(respond.data)
        this.gridTable.execRowDataApi(this.query)
      })
    },
    initPermissionMap(nodeDatas) {
      const that = this
      if (nodeDatas && nodeDatas.length > 0) {
        nodeDatas.forEach(nodeData => {
          const { dataId, label, children } = nodeData
          that.permissionMap[dataId] = label
          children && that.initPermissionMap(children)
        })
        console.log('initPermissionMap', this.permissionMap)
      }
    },
    initUserMap: function() {
      const that = this
      listUser({}).then(respond => {
        respond.data.forEach(user => {
          console.log('user', user)
          that.userMap[user.id] = user.account
        })
        console.log('initUserMap', that.userMap)
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined
      }
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.stgDeptChoose().reloadDlg()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      Object.assign(this.temp, JSON.parse(JSON.stringify(row)))
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      getStrategyById({ id: row.id }).then(resp => {
        Object.assign(this.temp, JSON.parse(JSON.stringify(resp.data)))
        this.$refs['dataForm'].clearValidate()
        const dataMap = this.formatData(this.temp, ['assignDeptId', 'cascadeDeptId', 'cascadeExceptDeptId', 'exceptDeptId', 'assignOperatorId', 'exceptOperatorId'])
        this.stgDeptChoose().updateDataShow(dataMap)
      })
    },
    formatData(obj, props) {
      if (!Object.prototype.toString.call(obj) == '[object Object]') return {}
      if (!Array.isArray(props) || props.length == 0) return {}
      const dataMap = {}
      // [部门（1）|操作员（2），指定（0）|例外（1），级联（0）|不级联（1）]
      const opts = {
        'assignDeptId': [1, 0, 1], 'cascadeDeptId': [1, 0, 0], 'cascadeExceptDeptId': [1, 1, 0], 'exceptDeptId': [1, 1, 1],
        'assignOperatorId': [2, 0, -1], 'exceptOperatorId': [2, 1, -1]
      }
      props.forEach(prop => {
        const ids = this.splitToNum(obj[prop])
        ids.forEach(id => this.formatUpdateParam(...opts[prop], id, dataMap))
      })
      return dataMap
    },
    formatUpdateParam(objType, handleMode, isCascade, id, dataMap) {
      console.log('this.userMap', this.userMap)
      let designatedDept;
      // 部门
      if (objType === 1) designatedDept = this.permissionMap[id]
      // 操作员
      if (objType === 2) designatedDept = this.userMap[id]
      console.log('designatedDept', designatedDept)

      let type
      if (handleMode === 0 && objType === 1) {
        // 指定 && 部门
        type = 'AG'
      }
      if (handleMode === 0 && objType === 2) {
        // 指定 && 操作员
        type = 'AU'
      }
      if (handleMode === 1 && objType === 1) {
        // 例外 && 部门
        type = 'EG'
      }
      if (handleMode === 1 && objType === 2) {
        // 例外 && 操作员
        type = 'EU'
      }

      const dataId = type.substring(type.length - 1, type.length) + id
      const ob = { id, handleMode, isCascade, designatedDept, type, dataId }
      console.log('formatUpdateParam', ob)
      if (!dataMap[type]) {
        dataMap[type] = []
      }
      dataMap[type].push(ob)
      // const type = handleMode == 0 ? 'G' : 'E'
      // const dataId = type + id
      // const ob = { id, handleMode, isCascade, designatedDept, type, dataId }
      // console.log('formatUpdateParam', ob)
      // if (!dataMap[handleMode]) {
      //   dataMap[handleMode] = []
      // }
      // dataMap[handleMode].push(ob)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    formatDeptParams() {
      const tableDatas = this.stgDeptChoose().rowDatas
      // 级联指定部门ID
      this.temp.cascadeDeptId = []
      // 指定部门ID
      this.temp.assignDeptId = []
      // 级联例外部门ID
      this.temp.cascadeExceptDeptId = []
      // 例外部门ID
      this.temp.exceptDeptId = []
      const deptList = [...(tableDatas[0].children || []).concat(tableDatas[2].children || [])]
      const deptRowData = {}
      deptList.forEach((data) => {
        if (!data) return
        const { id, handleMode, isCascade } = data
        const type = handleMode == 0 ? { 0: 'cascadeDeptId', 1: 'assignDeptId' }[isCascade]
          : { 0: 'cascadeExceptDeptId', 1: 'exceptDeptId' }[isCascade]
        if (!deptRowData[type]) { deptRowData[type] = [] }
        deptRowData[type].push(id)
      })
      this.temp.cascadeDeptId = (deptRowData.cascadeDeptId || []).join(',')
      this.temp.assignDeptId = (deptRowData.assignDeptId || []).join(',')
      this.temp.cascadeExceptDeptId = (deptRowData.cascadeExceptDeptId || []).join(',')
      this.temp.exceptDeptId = (deptRowData.exceptDeptId || []).join(',')

      const operList = [...(tableDatas[1].children || []).concat(tableDatas[3].children || [])]
      const operRowData = {}
      operList.forEach((data) => {
        if (!data) return
        const { id, handleMode } = data
        const type = handleMode == 0 ? 'assignOperatorId' : 'exceptOperatorId'
        if (!operRowData[type]) { operRowData[type] = [] }
        operRowData[type].push(id)
      })
      this.temp.assignOperatorId = (operRowData.assignOperatorId || []).join(',')
      this.temp.exceptOperatorId = (operRowData.exceptOperatorId || []).join(',')
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        valid = valid & this.validDeptSelect()
        if (valid) {
          // this.formatSubmitParams()
          this.formatDeptParams()
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        valid = valid & this.validDeptSelect()
        if (valid) {
          this.formatDeptParams()
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    permissionFormatter: function(row, data) {
      if (data) {
        const ids = data.split(',')
        return ids.reduce((labels, id) => {
          const label = this.html2Escape(this.permissionMap[id])
          label && labels.push(label)
          return labels
        }, []).join('；')
      } else {
        return ''
      }
    },
    userFormatter: function(row, data) {
      if (data) {
        const ids = data.split(',')
        return ids.reduce((labels, id) => {
          const label = this.html2Escape(this.userMap[id])
          label && labels.push(label)
          return labels
        }, []).join('；')
      } else {
        return ''
      }
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    deptHandleAdd() {
      this.$refs.addPermission.show('create')
      // this.deptHandleDlg().show('create')
    },
    deptHandleUpdate(row) {
      this.deptHandleDlg().show('update', row)
    },
    dealWithDeptDlg(status, data) {
      if (status == 'create') {
        this.stgDeptChoose().handleCreateDataInfo(data)
      } else if (status == 'update') {
        this.stgDeptChoose().handleUpdateDataInfo(data)
      }
    },
    validDeptSelect() {
      const tableDatas = this.stgDeptChoose().rowDatas
      console.log('validDeptSelect', tableDatas)
      const specifyDeptList = tableDatas[0].children || []
      const specifyOperList = tableDatas[1].children || []
      const exceptDeptList = tableDatas[2].children || []
      const exceptOperList = tableDatas[3].children || []
      if (specifyDeptList.length == 0 && exceptDeptList.length > 0) {
        this.$message({
          message: this.$t('pages.deptChoosePrompt2'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (specifyDeptList.length == 0 && specifyOperList.length == 0 &&
        exceptOperList.length > 0) {
        // 终端要求：不能仅配置例外操作员,需配置指定全局
        this.$message({
          message: this.$t('pages.operChoosePrompt3'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    dlgCancel() {
      this.stgDeptChoose().reloadDlg()
      this.dialogFormVisible = false
    }
  }
}
</script>
