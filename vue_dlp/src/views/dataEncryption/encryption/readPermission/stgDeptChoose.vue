<template>
  <div>
    <el-button size="small" :disabled="addable || !formable" @click.native="handleAdd">{{ $t('button.add') }}</el-button>
    <el-button size="small" :disabled="!deletable || !formable" @click.native="handleUpdate">{{ $t('button.edit') }}</el-button>
    <el-button size="small" :disabled="!deletable || !formable" @click.native="handleDelete">{{ $t('button.delete') }}</el-button>
    <grid-table
      ref="deptChooseList"
      :stripe="false"
      row-key="dataId"
      default-expand-all
      :height="285"
      :col-model="colModel"
      :multi-select="true"
      :is-saved-selected="true"
      :selectable="() => { return formable }"
      :cell-style="cellStyle"
      :row-data-api="rowDataApi"
      :show-pager="false"
      @selectionChangeEnd="handleSelectionChange"
    />
  </div>
</template>

<script>

export default {
  name: 'StgDeptChoose',
  props: {
    formable: { type: Boolean, default: true },
    permissionTreeData: {
      type: Array,
      default() {
        return []
      }
    },
    userMap: {
      type: Object,
      default() {
        return {}
      }
    },
    rowData: {
      type: Array,
      default() {
        return [];
      }
    },
    permissionTreeType: {
      type: String,
      default: 'G'
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'designatedDept', label: '阅读范围', width: '300', formatter: this.nameFormatter },
        { prop: 'include', label: '包含子部门', width: '120', sort: true, formatter: this.includeFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', disabledFormatter: () => { return !this.formable }, formatter: this.buttonFormatter, click: this.handleUpdate2 }
          ]
        }
      ],
      rowDatas: [],
      deptTreeMap: this.$store.getters.deptTreeMap,
      userTreeList: this.$store.getters.userTreeList,
      designatedDeptType: 'G',
      exceptDeptType: 'E',
      rowGroupNameMap: [this.$t('pages.optionedDept'), '指定操作员', this.$t('pages.excepeDept'), '例外操作员'],
      addable: false,
      deletable: false
    }
  },
  computed: {
    deptChooseList() {
      return this.$refs['deptChooseList']
    }
  },
  created() {
    this.reloadDlg()
  },
  methods: {
    nameFormatter: function(row, data) {
      // console.log('nameFormatter', JSON.parse(JSON.stringify(row)))
      // && row.children.length > 0
      if (row.children && row.type == 'group') {
        return '<label>' + row.label + '</label>'
      }
      if (row.children && row.type == 'oper') {
        return '<label>' + row.label + '</label>'
      }
      return this.html2Escape(data)
    },
    buttonFormatter: function(row, data) {
      if (row.type == 'group' || row.type == 'oper') { return '' }
      return this.$t('table.edit')
    },
    includeFormatter: function(row, data) {
      if (row.type == 'group') return ''
      let str = ''
      if (row.handleMode >= 0) {
        if (row.isCascade == 0) str = this.$t('pages.readPermission_info1')
        if (row.isCascade == 1) str = this.$t('pages.readPermission_info2')
      }
      return str
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (row.type == 'group' && (!row.children || row.children && row.children.length == 0)) {
        return 'display: none'
      }
      if (row.type == 'oper' && (!row.children || row.children && row.children.length == 0)) {
        return 'display: none'
      }
      if ((columnIndex == 1 || columnIndex == 2) && row.type == 'group') {
        return 'border-right: 0px'
      }
      return ''
    },
    handleAdd() {
      this.$emit('dept-handle-add')
    },
    handleUpdate() {
      const row = this.$refs['deptChooseList'].getSelectedDatas()
      console.log('formable', this.formable)
      console.log('handleUpdate', row)
      this.$emit('dept-handle-update', this.formatUpdateDlgData(row))
    },
    handleUpdate2(row, data) {
      console.log('handleUpdate2', row)
      this.$emit('dept-handle-update', this.formatUpdateDlgData([row]))
    },
    formatUpdateDlgData(rows) {
      const rowDatas = [{ label: this.rowGroupNameMap[0], type: 'group', dataId: 'Group1', handleMode: 0, children: [] },
        { label: this.rowGroupNameMap[1], type: 'oper', dataId: 'Oper1', handleMode: 0, children: [] },
        { label: this.rowGroupNameMap[2], type: 'group', dataId: 'Group2', handleMode: 1, children: [] },
        { label: this.rowGroupNameMap[3], type: 'oper', dataId: 'Oper2', handleMode: 1, children: [] }]
      console.log('formatUpdateDlgData', rows)
      rows.forEach(item => {
        // 选中父级添加
        if (item.dataId == 'Group1')rowDatas[0].children.push(...item.children)
        if (item.dataId == 'Oper1')rowDatas[1].children.push(...item.children)
        if (item.dataId == 'Group2')rowDatas[2].children.push(...item.children)
        if (item.dataId == 'Oper2')rowDatas[3].children.push(...item.children)

        // 选中子级添加
        if (item.type == 'AG' && item.handleMode == 0) {
          rowDatas[0].children.push(item)
        }
        if (item.type == 'AU' && item.handleMode == 0) {
          rowDatas[1].children.push(item)
        }
        if (item.type == 'EG' && item.handleMode == 1) {
          rowDatas[2].children.push(item)
        }
        if (item.type == 'EU' && item.handleMode == 1) {
          rowDatas[3].children.push(item)
        }
      })
      // 去重
      const ag = rowDatas[0].children.filter((item, index, self) => index === self.findIndex(t => t.id === item.id))
      const au = rowDatas[1].children.filter((item, index, self) => index === self.findIndex(t => t.id === item.id))
      const eg = rowDatas[2].children.filter((item, index, self) => index === self.findIndex(t => t.id === item.id))
      const eu = rowDatas[3].children.filter((item, index, self) => index === self.findIndex(t => t.id === item.id))
      console.log('rowDatas[0].children', rowDatas[0].children)
      console.log('AG', ag)
      rowDatas[0].children = []
      rowDatas[1].children = []
      rowDatas[2].children = []
      rowDatas[3].children = []
      rowDatas[0].children.push(...ag)
      rowDatas[1].children.push(...au)
      rowDatas[2].children.push(...eg)
      rowDatas[3].children.push(...eu)
      console.log('formatUpdateDlgData rowDatas', rowDatas)
      return rowDatas
    },
    handleDelete() {
      const selectedDatas = this.$refs.deptChooseList.getSelectedDatas();
      console.log('handleDelete', selectedDatas)
      selectedDatas.forEach(item => {
        console.log('handleDelete item', item.dataId)
        let no
        if (item.dataId == 'Group1' || item.type == 'AG') {
          // 父级 Group1 和 子级 AG(指定分组)
          no = 0
        }
        if (item.dataId == 'Oper1' || item.type == 'AU') {
          no = 1
        }
        if (item.dataId == 'Group2' || item.type == 'EG') {
          no = 2
        }
        if (item.dataId == 'Oper2' || item.type == 'EU') {
          no = 3
        }
        if (item.type == 'group' || item.type == 'oper') {
          this.rowDatas[no].children = []
        }
        const list = this.rowDatas[no].children
        const index = list.findIndex(data => data.id == item.id)
        list.splice(index, 1)
        if (list == undefined) this.rowDatas[no].children = []
      })
      this.$refs.deptChooseList.clearSelection()
      // 当删除例外的时候，清除指定操作员
      this.clearSpecifyOperMethod()
    },
    rowDataApi() {
      return new Promise((resolve, reject) => { resolve({ data: this.rowDatas }) })
    },
    reloadDlg() {
      this.$nextTick(() => {
        this.deptChooseList.clearSelection()
        this.rowDatas = [{ label: this.rowGroupNameMap[0], type: 'group', dataId: 'Group1', handleMode: 0, children: [] },
          { label: this.rowGroupNameMap[1], type: 'oper', dataId: 'Oper1', handleMode: 2, children: [] },
          { label: this.rowGroupNameMap[2], type: 'group', dataId: 'Group2', handleMode: 1, children: [] },
          { label: this.rowGroupNameMap[3], type: 'oper', dataId: 'Oper2', handleMode: 3, children: [] }
        ]
        this.$refs.deptChooseList.execRowDataApi()
      })
    },
    handleCreateDataInfo(data) {
      console.log('userTreeList', this.userTreeList)
      console.log('data', data)
      console.log('handleCreateDataInfo list', this.rowDatas)
      const handleMode = data.handleMode
      const operHandleMode = data.operHandleMode
      const isCascade = data.isCascade
      let list = []; let type = ''; let order
      if (data.deptDatas.length > 0) {
        // 部门 handleMode: 0 (指定) 1 （例外）
        type = handleMode == 0 ? 'AG' : 'EG'
        order = handleMode == 0 ? 0 : 2 // 0:指定部门  1：指定操作员 2：例外部门 3：例外操作员
        console.log('order', order)
        const deptIds = data.deptDatas.map(item => Number(item.dataId))
        console.log('deptIds', deptIds)
        if (handleMode == 0) {
          // 添加指定部门
          // 移除例外部门
          this.rowDatas[2].children = this.rowDatas[2].children.filter(r => !deptIds.includes(Number(r.id)))
          // this.rowDatas[2].children
          console.log('this.rowDatas[2].children', this.rowDatas[2].children)
        } else {
          // 过滤掉指定类型中的操作员
          this.rowDatas[0].children = this.rowDatas[0].children.filter(r => !deptIds.includes(Number(r.id)))
          console.log('this.rowDatas[0].children', this.rowDatas[0].children)
        }
        list = this.rowDatas[order].children
        data.deptDatas.forEach(dept => {
          const index = list.findIndex(item => item.id == dept.dataId)
          if (index < 0) {
            const ob = {}
            ob.type = type
            ob.dataId = 'G' + dept.dataId
            ob.designatedDept = dept.label
            ob.id = dept.dataId
            ob.handleMode = handleMode
            ob.isCascade = isCascade
            list.push(ob)
          } else {
            if (list[index].isCascade == 1) list[index].isCascade = isCascade
          }
        })
        // 指定部门或例外部门内-避免重复配置
        this.clearSelfMethod(list)
        // 指定中的部门，避免在例外中配置
        this.clearCrossSector()
        // 当删除例外的时候，清除指定操作员
        this.clearSpecifyOperMethod()
      }
      if (data.operDatas.length > 0) {
        type = operHandleMode == 0 ? 'AU' : 'EU'
        order = operHandleMode == 0 ? 1 : 3
        const operIds = data.operDatas.map(item => Number(item.dataId))
        console.log('operIds', operIds)
        if (operHandleMode == 0) {
          // 添加指定操作员
          // 移除例外操作员
          this.rowDatas[3].children = this.rowDatas[3].children.filter(r => !operIds.includes(Number(r.id)))
          console.log('this.rowDatas[3].children', this.rowDatas[3].children)
        } else {
          // 过滤掉指定类型中的操作员
          this.rowDatas[1].children = this.rowDatas[1].children.filter(r => !operIds.includes(Number(r.id)))
          console.log('this.rowDatas[1].children', this.rowDatas[1].children)
        }
        console.log('order', order)
        list = this.rowDatas[order].children
        console.log('list', list)
        data.operDatas.forEach(user => {
          const index = list.findIndex(item => Number(item.id) == Number(user.dataId))
          if (index < 0) {
            const ob = {}
            ob.type = type
            ob.dataId = 'U' + user.dataId
            ob.designatedDept = user.label
            ob.id = user.dataId
            ob.handleMode = operHandleMode
            list.push(ob)
          } else {
            // if (list[index].isCascade == 1) list[index].isCascade = isCascade
          }
        })
        this.clearSpecifyOperMethod()
      }
      this.$nextTick(() => {
        this.deptChooseList.execRowDataApi()
      })
    },
    clearSelfSector() {
      this.clearSelfMethod(this.rowDatas[0].children)
      this.clearSelfMethod(this.rowDatas[2].children)
    },
    // 指定部门或例外部门内-避免重复配置
    clearSelfMethod(data) {
      data = data || []
      const len = data.length - 1
      for (let i = len; i >= 0; i--) {
        console.log('当前节点id', data[i].id)
        // 获取当前部门节点及父节点，如果当前所有数据中存在父节点同时节点状态是级联时，移除当前节点。防止节点重复配置。
        const arrays = this.getNodeIdPath('G' + data[i].id);
        console.log('当前节点路径', arrays)
        for (let j = 0; j < arrays.length - 1; j++) {
          const index = data.findIndex(item => 'G' + item.id == arrays[j])
          if (index >= 0 && data[index].isCascade == 0) {
            // 存在指定部门包含当前部门的节点，故当前节点无需添加
            data.splice(i, 1)
            break
          }
        }
      }
    },
    // 指定中的部门，避免在例外中配置
    clearCrossSector() {
      const specifyDept = this.rowDatas[0].children || []
      const exceptionDept = this.rowDatas[2].children || []
      for (let i = specifyDept.length - 1; i >= 0; i--) {
        const arrays = this.getNodeIdPath(specifyDept[i].dataId)
        for (let j = 0; j < arrays.length; j++) {
          const index = exceptionDept.findIndex(item => 'G' + item.id == arrays[j])
          if (index >= 0) {
            if (exceptionDept[index].isCascade == specifyDept[i].isCascade) {
              specifyDept.splice(i, 1)
              exceptionDept.splice(index, 1)
            } else if (exceptionDept[index].isCascade == 0) {
              specifyDept.splice(i, 1)
            }
          }
        }
      }
    },
    // 清除指定操作员
    clearSpecifyOperMethod() {
      const specifyDept = this.rowDatas[0].children || []
      const specifyOper = this.rowDatas[1].children || []
      const exceptionDept = this.rowDatas[2].children || []
      console.log('specifyOper', specifyOper)
      for (let i = specifyOper.length - 1; i >= 0; i--) {
        const operNode = this.getOperNode(specifyOper[i].dataId)
        console.log('operNode', operNode)
        const deptArr = this.getNodeIdPath(operNode[0].parentId)
        console.log('deptArr', deptArr)
        for (let j = deptArr.length - 1; j >= 0; j--) {
          // 如果先在例外中找到上级部门id,则不用移除
          const index = exceptionDept.findIndex(item => 'G' + item.id == deptArr[j])
          if (index >= 0) {
            // 根部门 - 部门1 - 操作员；
            // 根部门指定包含子部门，部门1配置例外，操作员配置指定场景
            return
          }
          const index2 = specifyDept.findIndex(item => 'G' + item.id == deptArr[j])
          if (index2 >= 0 && specifyDept[index2].isCascade == 0) {
            specifyOper.splice(i, 1)
          }
        }
      }
    },
    getOperNode(id) {
      return this.userTreeList.filter(node => node.id == id)
    },
    getNodeIdPath(id) {
      console.log('this.deptTreeMap', this.deptTreeMap)
      const ids = []
      while (this.deptTreeMap[id]) {
        ids.unshift(id)
        id = this.deptTreeMap[id].parentId
      }
      return ids
    },
    handleUpdateDataInfo(data) {
      // 清理历史选中
      this.$refs.deptChooseList.clearSelection()
      this.$refs.deptChooseList.clearSaveSelection()
      console.log('this.$refs.deptChooseList', this.$refs.deptChooseList.getSelectedDatas())
      console.log('handleUpdateDataInfo', data)
      console.log('handleUpdateDataInfo2', this.rowDatas)
      // data 包含指定部门、指定操作员、例外部门、例外操作员。
      if (!data || data.length == 0) return
      for (let j = 0; j < data.length; j++) {
        const children = this.rowDatas[j].children != undefined ? this.rowDatas[j].children : []
        const dataChild = data[j].children != undefined ? data[j].children : []
        console.log('children', children)
        console.log('dataChild', dataChild)
        for (let i = dataChild.length - 1; i >= 0; i--) {
          const index = children.findIndex(item => item.id == dataChild[i].id)
          this.rowDatas[j].children.splice(index, 1)
          // if (this.rowDatas[dataChild[i].handleMode].children == undefined) {
          //   this.rowDatas[dataChild[i].handleMode].children = []
          // }
          // this.rowDatas[dataChild[i].handleMode].children.push(dataChild[i])
          let no
          let type
          if (dataChild[i].handleMode == undefined) {
            continue
          }
          if (dataChild[i].type.includes('G') && dataChild[i].handleMode == 0) {
            no = 0
            type = 'G'
          }
          if (dataChild[i].type.includes('G') && dataChild[i].handleMode == 1) {
            no = 2
            type = 'G'
          }
          if (dataChild[i].type.includes('U') && dataChild[i].handleMode == 0) {
            no = 1
            type = 'U'
          }
          if (dataChild[i].type.includes('U') && dataChild[i].handleMode == 1) {
            no = 3
            type = 'U'
          }
          console.log('dataChild[i].handleMode', dataChild[i].handleMode)
          console.log('handleUpdateDataInfo ' + i, no, this.rowDatas[no])
          console.log(type);
          if (this.rowDatas[no] === undefined) {
            continue;
          }
          if (this.rowDatas[no].children === undefined) {
            this.rowDatas[no].children == []
          }
          this.rowDatas[no].children.push(dataChild[i])
        }
      }
      // 指定部门或例外部门内-避免重复配置
      this.clearSelfSector()
      // 指定中的部门，避免在例外中配置
      this.clearCrossSector()
      // 当删除例外的时候，清除指定操作员
      this.clearSpecifyOperMethod()
    },
    formatDataInfo(data) {
      const info = []
      data.deptDatas.forEach(item => {
        const { dataId, label: designatedDept } = item
        const { handleMode, isCascade } = item
        const ob = { dataId, designatedDept, handleMode, isCascade, type: 'dept' }
        info.push(ob)
      })
      return info
    },
    updateDataShow(data) {
      console.log('this.deptTreeMap', this.deptTreeMap)
      if (data) {
        this.rowDatas = [{ label: this.rowGroupNameMap[0], type: 'group', dataId: 'Group1', handleMode: 0, children: [] },
          { label: this.rowGroupNameMap[1], type: 'oper', dataId: 'Oper1', handleMode: 0, children: [] },
          { label: this.rowGroupNameMap[2], type: 'group', dataId: 'Group2', handleMode: 1, children: [] },
          { label: this.rowGroupNameMap[3], type: 'oper', dataId: 'Oper2', handleMode: 1, children: [] }]
        this.rowDatas[0].children = data['AG'] !== undefined ? data['AG'] : []
        this.rowDatas[1].children = data['AU'] !== undefined ? data['AU'] : []
        this.rowDatas[2].children = data['EG'] !== undefined ? data['EG'] : []
        this.rowDatas[3].children = data['EU'] !== undefined ? data['EU'] : []
        console.log('userTreeList', this.rowDatas)
        this.$nextTick(() => {
          this.$refs.deptChooseList.execRowDataApi()
        })
      }
    },
    handleSelectionChange(rows) {
      this.deletable = rows.length > 0
    }
  }
}
</script>
