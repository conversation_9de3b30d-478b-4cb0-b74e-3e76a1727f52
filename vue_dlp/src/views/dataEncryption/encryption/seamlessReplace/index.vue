<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleRestart">
          {{ $t('pages.retryInvoke') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleStop">
          {{ $t('pages.stopInvoke') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.stgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <seamless-replace-dlg
      ref="stgDlg"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    ></seamless-replace-dlg>
  </div>
</template>

<script>
import SeamlessReplaceDlg from './editDlg'
import { getStrategyList, deleteStrategy, stop, restart, listCompetitorInfo } from '@/api/dataEncryption/encryption/seamlessReplace'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'

export default {
  name: 'SeamlessReplace',
  components: { SeamlessReplaceDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      stgCode: 204,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'stgMessage', width: '200', formatter: this.processNameFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ]
      },
      submitting: false,
      checkedEntityNode: {},
      productOption: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    },
    productMap() {
      const map = {}
      for (const item of this.productOption) {
        map[item.id] = item
      }
      return map
    }
  },
  beforeRouteEnter(to, from, next) {
    const isPageLoaded = window.getMenuAccessStatus('SeamlessReplace')
    const isRefresh = from.path == '/redirect/config/seamlessReplace'
    // 通过 搜索、已访问过、右键菜单刷新
    if (to.params.search || isPageLoaded || isRefresh) {
      next()
    } else {
      next('/404')
    }
  },
  created() {
    this.setMenuAccessStatus('SeamlessReplace')
    listCompetitorInfo().then(res => {
      this.productOption = res.data
    })
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    strategyTable() {
      return this.$refs['strategyInnerList']
    },
    encTypeTable() {
      return this.$refs['encTypeList']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    importSuccess() {
      this.handleFilter()
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleRestart() {
      this.$confirmBox(this.$t('pages.sureWhatTodo', { what: this.$t('pages.retryInvoke') }), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        restart({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.retryInvokeSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleStop() {
      this.$confirmBox(this.$t('pages.sureWhatTodo', { what: this.$t('pages.stopInvoke') }), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        stop({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.stopSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    processNameFormatter: function(row, data) {
      const i18n = (i18nKey) => this.$t(i18nKey)
      const workMap = {
        1: i18n('pages.idleScanning'),
        2: i18n('pages.highPerformance'),
        3: i18n('pages.shutterPriority')
      }
      const info = `${i18n('table.productName')}：${this.productMap[row.competitorId].competitorName}，${i18n('pages.showCompatOther')}：${row.decryptRight ? i18n('text.yes') : i18n('text.no')}，${i18n('pages.workMode')}：${workMap[row.workMode]}，${i18n('pages.autoOverall')}：${row.decryptFullDisk ? i18n('text.yes') : i18n('text.no')}，${i18n('pages.periodicallyDelBackup')}：${row.isDelBak ? i18n('text.yes') : i18n('text.no')}，${i18n('pages.reencryption')}：${row.configFlag ? i18n('text.yes') : i18n('text.no')}`
      return info
    }
  }
}
</script>

