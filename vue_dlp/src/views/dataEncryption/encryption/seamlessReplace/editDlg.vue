<template>
  <stg-dialog
    ref="stgDlg"
    :title="$t('layout.compatibleSetting')"
    :stg-code="204"
    :active-able="activeAble"
    :rules="rules"
    :model="defaultTemp"
    :entity-node="entityNode"
    :create="createStrategy"
    :update="updateStrategy"
    :get-by-name="getStrategyByName"
    :format-row-data="formatRowData"
    :format-form-data="formatFormData"
    @submitEnd="submitEnd"
    @slotChange="slotChange"
  >
    <template :slot="slotName">
      <el-card class="box-card">
        <div style="line-height: 22px;">
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('table.productName')" prop="competitorId">
                <el-select v-model="temp.competitorId" style="width: 75%" @change="clearFile">
                  <el-option v-for="item in productOption" :key="item.id" :label="item.competitorName" :value="item.id"></el-option>
                </el-select>
              </FormItem>
            </el-col>
            <el-col v-if="isShowNoneEnv && productMap[temp.competitorId] && ((productMap[temp.competitorId].controlCode)&4)==4" :span="12">
              <FormItem label-width="0">
                <el-col :span="9">
                  <el-checkbox v-model="temp.isNonEnv" :false-label="0" :true-label="1">{{ $t('pages.noOtherProductEnv') }}</el-checkbox>
                </el-col>
                <el-col :span="9">
                  <FormItem label-width="0" prop="packFileName">
                    <el-tooltip :disabled="isShowTooltip" effect="dark" :content="temp.packFileName" placement="top">
                      <el-input v-model="temp.packFileName" readonly :disabled="temp.isNonEnv==0" :placeholder="$t('pages.pleasePostPatch')" @mouseover.native="isTooltip"/>
                    </el-tooltip>
                  </FormItem>
                </el-col>
                <el-col :span="4">
                  <el-upload
                    ref="upload"
                    accept=".dat"
                    :disabled="temp.isNonEnv==0"
                    class="upload-demo"
                    name="uploadFile"
                    action="aaaaaa"
                    :limit="1"
                    :show-file-list="false"
                    :before-upload="beforeUpload"
                  >
                    <el-button size="small" :disabled="temp.isNonEnv==0" type="primary">{{ loadText }}</el-button>
                  </el-upload>
                </el-col>
              </FormItem>
            </el-col>
            <el-col v-if="productMap[temp.competitorId] && ((productMap[temp.competitorId].controlCode)&1)==1" :span="12">
              <el-col :span="18">
                <FormItem :label="$t('pages.keyFile')" prop="packFileName">
                  <el-tooltip slot="tooltip" class="item" effect="dark" placement="bottom">
                    <div slot="content">{{ $t('pages.keyFileTip1') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                  <el-tooltip :disabled="disabledTooltip" effect="dark" :content="temp.packFileName" placement="top">
                    <el-input ref="packFileName" v-model="temp.packFileName" readonly :placeholder="$t('pages.pleasePostKeyFile')" @mouseover.native="showTooltip"/>
                  </el-tooltip>
                </FormItem>
              </el-col>
              <el-col :span="4">
                <el-upload
                  ref="upload"
                  accept=".ldk"
                  class="upload-demo"
                  name="uploadFile"
                  action="aaaaaa"
                  :limit="1"
                  :show-file-list="false"
                  :before-upload="beforeUpload"
                >
                  <el-button size="small" type="primary">{{ loadText }}</el-button>
                </el-upload>
              </el-col>
            </el-col>
            <el-col v-if="productMap[temp.competitorId] && ((productMap[temp.competitorId].controlCode)&2)==2" :span="12">
              <FormItem :label="$t('pages.zipPierceSuffix')" label-width="140">
                <el-checkbox-group v-model="temp.penetrateExtArr">
                  <el-checkbox label=".zip" >.zip</el-checkbox>
                  <el-checkbox label=".rar" >.rar</el-checkbox>
                  <el-checkbox label=".7z" >.7z</el-checkbox>
                </el-checkbox-group>
              </FormItem>
            </el-col>
          </el-row>
          <el-row v-if="isShowNoneEnv && productMap[temp.competitorId] && ((productMap[temp.competitorId].controlCode)&4)==4 && temp.isNonEnv==1">
            <el-col :span="12" :offset="1">
              <FormItem :label="$t('pages.zipPierceSuffix')" label-width="140">
                <el-checkbox-group v-model="temp.penetrateExtArr">
                  <el-checkbox label=".zip" >.zip</el-checkbox>
                  <el-checkbox label=".rar" >.rar</el-checkbox>
                  <el-checkbox label=".7z" >.7z</el-checkbox>
                </el-checkbox-group>
              </FormItem>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <FormItem label-width="38px">
                <el-checkbox v-model="temp.decryptRight" :false-label="0" :true-label="1">{{ $t('pages.showCompatOther') }}</el-checkbox>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.workMode')" prop="workMode">
                <el-select v-model="temp.workMode" style="width: 66%">
                  <el-option v-for="item in workModeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </FormItem>
            </el-col>
          </el-row>
          <el-row v-if="temp.competitorId == 4">
            <el-col :span="9" style="margin-left: 37px">
              <el-checkbox v-model="temp.configFlag" :false-label="0" :true-label="1">{{ $t('pages.reencryption') }}</el-checkbox>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <el-card class="box-card">
        <div style="line-height: 22px;">
          <FormItem label-width="38px">
            <el-checkbox v-model="temp.decryptFullDisk" :false-label="0" :true-label="1">{{ $t('pages.autoOverall') }}</el-checkbox>
          </FormItem>
          <el-row>
            <el-col :span="12">
              <FormItem label-width="38px">
                <el-checkbox v-model="temp.showProgress" :disabled="temp.decryptFullDisk==0" :false-label="0" :true-label="1">{{ $t('pages.termShowProgressWin') }}</el-checkbox>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.fullScanDeadline')" label-width="130px" prop="scanEndTime">
                <el-date-picker v-model="temp.scanEndTime" :disabled="temp.decryptFullDisk==0" :clearable="false" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :picker-options="pickerOptions"></el-date-picker>
              </FormItem>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <el-card class="box-card">
        <div style="line-height: 22px;">
          <FormItem label-width="38px">
            <el-checkbox v-model="temp.isDelBak" :false-label="0" :true-label="1">{{ $t('pages.periodicallyDelBackup') }}</el-checkbox>
          </FormItem>
          <el-row>
            <el-col :span="12">
              <FormItem label-width="38px">
                <el-checkbox v-model="temp.showDelTipWnd" :disabled="temp.isDelBak==0" :false-label="0" :true-label="1">{{ $t('pages.popUpPromptDelFile') }}</el-checkbox>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.scheduleDelDeadline')" label-width="130px" prop="delBakTime">
                <el-date-picker v-model="temp.delBakTime" :disabled="temp.isDelBak==0" :clearable="false" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
              </FormItem>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </template>
  </stg-dialog>
</template>

<script>
import {
  getStrategyByName, createStrategy, updateStrategy, listCompetitorInfo, uploadFile
} from '@/api/dataEncryption/encryption/seamlessReplace'
import { isShowNoneEnv } from '@/api/grantAuth'
import moment from 'moment'
import { getTransferStatus } from '@/api/behaviorManage/hardware/pictureLib';

export default {
  name: 'SeamlessReplaceDlg',
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        }
      },
      loadText: this.$t('button.upload'),
      temp: { }, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        guid: '',
        scanBeginTime: moment(new Date()).format('YYYY-MM-DD'),
        scanEndTime: moment(new Date()).add(1, 'd').format('YYYY-MM-DD'),
        competitorId: null,
        decryptRight: 1,
        decryptFullDisk: 1,
        showProgress: 0,
        penetrateExtArr: [],
        penetrateExt: '',
        isDelBak: 0,
        delBakTime: moment(new Date()).format('YYYY-MM-DD'),
        showDelTipWnd: 0,
        workMode: 1,
        cpuMem: '',
        stopGuid: '',
        isNonEnv: 0,
        packFileGuid: '',
        packFileName: '',
        configFlag: 0
      },
      nowDay: '',
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        packFileName: { validator: this.packFileNameValid, trigger: 'blur' },
        scanEndTime: { required: true, validator: this.endTimeValid, trigger: 'blur' }
      },
      productOption: [
      ],
      workModeOption: [
        { label: this.$t('pages.idleScanning'), value: 1 },
        { label: this.$t('pages.highPerformance'), value: 2 },
        { label: this.$t('pages.shutterPriority'), value: 3 }
      ],
      statusDict: {},
      submitting: false,
      slotName: undefined,
      dialogFormVisible: false,
      isShowNoneEnv: false,
      disabledTooltip: true,
      isShowTooltip: true
    }
  },
  computed: {
    productMap() {
      const map = {}
      for (const item of this.productOption) {
        map[item.id] = item
      }
      return map
    }
  },
  watch: {
    'temp.decryptFullDisk'(val) {
      if (!val) {
        this.temp.showProgress = 0
      }
    },
    'temp.isDelBak'(val) {
      if (!val) {
        this.temp.showDelTipWnd = 0
      }
    }
  },
  created() {
    isShowNoneEnv().then(res => {
      this.isShowNoneEnv = res.data
    })

    this.nowDay = moment(new Date()).format('YYYY-MM-DD')
    this.resetTemp()
    listCompetitorInfo().then(res => {
      this.productOption = res.data
      if (this.productOption) {
        this.defaultTemp.competitorId = this.productOption[0].id
      }
    })
    this.getStatusInfo()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    getStatusInfo() {
      getTransferStatus().then(res => {
        this.statusDict = res.data || {}
      })
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleFilter() {
    },
    handleDrag() {
    },
    clearFile(val) {
      this.temp.packFileGuid = ''
      this.temp.packFileName = ''
      this.disabledTooltip = true
      this.isShowTooltip = true
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(rowData) {
      if (rowData.penetrateExt) {
        this.$set(rowData, 'penetrateExtArr', rowData.penetrateExt.split('|'))
      }
      if (!rowData.decryptFullDisk) {
        rowData.scanEndTime = this.nowDay
      }
      // 是否开启定时删除备份，要根据备份删除时间是否为空来判断。
      if (!rowData.delBakTime) {
        this.$set(rowData, 'isDelBak', 0)
        this.$set(rowData, 'delBakTime', this.nowDay)
      } else {
        this.$set(rowData, 'isDelBak', 1)
      }
    },
    formatFormData(formData) {
      // 穿透后缀格式化
      formData.penetrateExt = ''
      if (((this.productMap[formData.competitorId].controlCode) & 2) == 2 && formData.penetrateExtArr) {
        formData.penetrateExt = formData.penetrateExtArr.join('|')
      }
      // 如果这个竞品不支持无环境替换，那就不能开启无环境替换
      if (this.productMap[formData.competitorId] == null || ((this.productMap[formData.competitorId].controlCode) & 4) != 4) {
        formData.isNonEnv = 0
      }
      // 如果支持无竞品环境无缝替换，也要格式化穿透后缀。
      if (this.isShowNoneEnv && this.productMap[formData.competitorId] && ((this.productMap[formData.competitorId].controlCode) & 4) == 4 && formData.isNonEnv == 1) {
        formData.penetrateExt = formData.penetrateExtArr.join('|')
      }
      // 如果不开启定时删除备份，那就把时间设置为空，终端就不会开启了。
      if (!formData.isDelBak) {
        formData.delBakTime = ''
      }
      // 以终端登陆操作员id重新加密只有当产品名称为DLP多套体系密钥互通时才有该配置项，非该产品名称时，该配置项默认不勾选
      if (formData.competitorId != 4 && formData.configFlag == 1) {
        formData.configFlag = 0
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    beforeUpload(file) {
      this.submitting = true
      const fd = new FormData()
      fd.append('uploadFile', file)// 传文件
      fd.append('controlCode', this.productMap[this.temp.competitorId].controlCode)
      const loading = this.$loading({
        lock: true,
        text: this.$t('pages.fileFp_Msg19'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      uploadFile(fd).then(res => {
        this.submitting = false
        // 订阅文件上传情况
        this.$socket.subscribeToUser('uploadResult', res.data.fileGuid, (respond, handle) => {
          // 此订阅当作所有消息的通知接口
          if (respond.data && (respond.data.status == 6 || respond.data.status > 999)) {
            if (respond.data.status == 6) {
              this.temp.packFileGuid = respond.data.guid
              this.temp.packFileName = file.name
            }
            this.$message({
              message: this.$t(this.statusDict[respond.data.status]) || respond.data.statusInfo,
              type: respond.data.status == 6 ? 'success' : 'error',
              duration: 2000
            })
            loading.close()
            handle.close()
          }
        }, false)
      }).catch(res => {
        loading.close()
        this.submitting = false
      })
      return false // 屏蔽了action的默认上传
    },
    packFileNameValid(rule, value, callback) {
      if (this.productMap[this.temp.competitorId] && ((this.productMap[this.temp.competitorId].controlCode) & 1) == 1) {
        if (this.temp.packFileName == '') {
          callback(new Error(this.$t('pages.signatureData_text2')))
        } else {
          callback()
        }
      } else {
        if (this.temp.isNonEnv && this.temp.packFileName == '') {
          callback(new Error(this.$t('pages.signatureData_text2')))
        } else {
          callback()
        }
      }
    },
    endTimeValid(rule, value, callback) {
      if (this.temp.decryptFullDisk && !value) {
        callback(new Error(this.$t('pages.selectDeadline')))
      } else if (this.temp.decryptFullDisk && new Date(value) < new Date()) {
        callback(new Error(this.$t('pages.deadlineGreaterThanDateOfDay')))
      } else {
        callback()
      }
    },
    showTooltip() {
      if (this.temp.packFileName != '') {
        this.disabledTooltip = false
      }
    },
    isTooltip() {
      if (this.temp.packFileName != '') {
        this.isShowTooltip = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.text {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width:165px;
}
</style>
