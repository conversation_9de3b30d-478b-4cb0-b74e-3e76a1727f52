<template>
  <div class="app-container">
    <el-tabs
      ref="tabs"
      v-model="activeName"
      type="card"
      :before-leave="changeTab"
      @tab-click="tabClick"
    >
      <el-tab-pane :label="$t('pages.editingProcess')" name="editProcess">
        <EditProcess ref="editProcess"></EditProcess>
      </el-tab-pane>
      <el-tab-pane :label="$t('pages.allocationProcess')" name="assignProcess">
        <AssignProcess ref="assignProcess"></AssignProcess>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import EditProcess from '@/views/dataEncryption/encryption/approvalProcess/editProcess'
import AssignProcess from '@/views/dataEncryption/encryption/approvalProcess/assignProcess'

// export let categoryList = [
//   { value: 'fileDecrypt', label: '解密审批' },
//   // { value: 'printOutsend', label: '打印外发审批' },
//   { value: 'offline', label: '离线审批' },
//   { value: 'outSend', label: '直接外发审批' },
//   { value: 'changeFileLevel', label: '定密审批' }
//   // { value: 'filePrint', label: '打印审批' },
//   // { value: 'fileRelieveJurisdiction', label: '阅读权限转换审批' }
// ]

export default {
  name: 'ApprovalProcess',
  components: { EditProcess, AssignProcess },
  data() {
    return {
      activeName: 'editProcess'
    }
  },
  methods: {
    tabClick(pane, event) {
    },
    changeTab(activeName, oldActiveName) {
    }
  }
}

</script>
<style scoped>
  .app-container{
    padding: 10px 15px;
  }
</style>
