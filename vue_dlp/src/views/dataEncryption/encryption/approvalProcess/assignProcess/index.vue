<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="roleAndUserTree"
        :data="roleAndUserData"
        :is-filter="true"
        :default-expand-all="false"
        :icon-option="iconRoleOption"
        @node-click="handleNodeClickFunc"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button :loading="submitting" type="primary" size="mini" :disabled="!currentNode || currentNode.type == 'RG'" @click="handleAssign">{{ $t('pages.processAllocation') }}</el-button>
      </div>
      <grid-table
        ref="tableList"
        v-loading="tableLoading"
        :col-model="colModel"
        :row-datas="rowData"
        :multi-select="false"
        :show-pager="false"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.processAllocation1')"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="600px"
    >
      <el-checkbox v-show="currentNode && currentNode.type != 'R'" v-model="inherit" style="margin-bottom:10px" :disabled="disabledCheck">{{ $t('pages.approvalProcess_Msg') }}</el-checkbox>
      <tree-menu
        ref="processTree"
        :height="300"
        :width="560"
        :data="processTreeData"
        :is-filter="true"
        :multiple="true"
        :checked-keys="checkedKeys"
        :render-content="renderDeviceContentFunc"
        node-key="id"
        class="assign-tree"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="assignProcess">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.processSteps')"
      :visible.sync="dialogStepsVisible"
      :append-to-body="true"
      width="600px"
    >
      <ApprovalChart v-if="dialogStepsVisible === true" ref="chart" :approver-list="approverList" :initiator-list="initiatorList" :editable="false"/>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogStepsVisible=false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getProcessTree, getProcessList, getProcessById, getAssignedList, assignProcess } from '@/api/dataEncryption/encryption/approvalProcess'
import ApprovalChart from '@/components/ApprovalChart'
import { mapGetters } from 'vuex';

// const categoryType = [
//   { value: 'fileDecrypt', label: this.$t('pages.decryptionApproval') },
//   { value: 'printOutsend', label: this.$t('pages.printOutApproval') },
//   { value: 'offline', label: this.$t('pages.offlineApproval') },
//   { value: 'outSend', label: this.$t('pages.directOutApproval') },
//   { value: 'changeFileLevel', label: this.$t('pages.classifiedApproval') },
//   { value: 'filePrint', label: this.$t('pages.printApproval') },
//   { value: 'fileRelieveJurisdiction', label: this.$t('pages.readConversionApproval') },
//   { value: 'cancelWatermark', label: this.$t('pages.cancelWatermarkApproval') },
//   { value: 'sensitiveFileOutSend', label: this.$t('pages.externalApprovalSensitive') }
// ]
const keyValueFilter = function(options) {
  const typeKeyValue = options.reduce((acc, cur) => {
    acc[cur.value] = cur.label
    return acc
  }, {})
  return typeKeyValue
}

export default {
  name: 'AssignProcess',
  components: { ApprovalChart },
  data() {
    return {
      tableLoading: false,
      showTree: true,
      dialogVisible: false,
      dialogStepsVisible: false,
      processTreeData: [],
      inherit: true,
      oriProcessIds: [],
      disabledCheck: false,
      submitting: false,
      rowData: [],
      checkedKeys: [],
      currentNode: undefined,
      categoryType: [
        { value: 'fileDecrypt', label: this.$t('pages.decryptionApproval') },
        { value: 'printOutsend', label: this.$t('pages.printOutApproval') },
        { value: 'offline', label: this.$t('pages.offlineApproval') },
        { value: 'outSend', label: this.$t('pages.directOutApproval') },
        { value: 'changeFileLevel', label: this.$t('pages.classifiedApproval') },
        { value: 'filePrint', label: this.$t('pages.printApproval') },
        { value: 'fileRelieveJurisdiction', label: this.$t('pages.readConversionApproval') },
        { value: 'cancelWatermark', label: this.$t('pages.cancelWatermarkApproval') },
        { value: 'sensitiveFileOutSend', label: this.$t('pages.externalApprovalSensitive') },
        { value: 'behaviorControl', label: this.$t('pages.behaviorControlApproval') }
      ],
      colModel: [
        { label: 'applicant', width: '130', formatter: this.userFormatter },
        { prop: 'type', label: 'deptUser', formatter: this.typeFormatter, width: '100' },
        { prop: 'name', label: 'floatName', width: '130' },
        { prop: 'groupName', label: 'floatGroup', width: '130' },
        { prop: 'category', label: 'funType', width: '130', formatter: this.categoryFormatter },
        { prop: 'inheritAbove', label: 'sourceAllocation', width: '150', formatter: this.inheritFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100',
          buttons: [
            { label: 'processSteps', click: this.processSteps, isShow: (data) => !data.auto }
          ]
        }
      ],
      iconOption: {
        'G': 'service',
        'L': 'process'
      },
      iconRoleOption: {
        'RG': 'roleGroup',
        'R': 'role'
      }
    }
  },
  computed: {
    ...mapGetters([
      'userTree',
      'userRoleOptions'
    ]),
    // 获取岗位树，并将岗位树的节点类型改为R，方便跟用户树做区分
    roleData() {
      const roles = JSON.parse(JSON.stringify(this.userRoleOptions))
      roles.forEach(item => {
        item.type = 'RG'
        item.id = item.id.replace('G', 'RG')
        item.parentId = item.parentId.replace('G', 'RG')
        if (item.children) {
          item.children.forEach(child => {
            child.type = 'R'
            child.id = child.id.replace('G', 'R')
            child.parentId = child.parentId.replace('G', 'RG')
          })
        }
      })
      return [{
        'dataId': '0',
        'id': 'RG0',
        'disabled': true,
        'label': this.$t('pages.userRoleLibrary'),
        'parentId': 'RG-1',
        'type': 'RG',
        'children': roles
      }]
    },
    // 角色树和操作员树
    roleAndUserData() {
      return this.userTree ? this.roleData.concat(this.userTree) : this.roleData
    }
  },
  methods: {
    renderDeviceContentFunc(h, { node, data, store }) {
      const iconClass = <svg-icon icon-class={this.iconOption[data.dataCode]} />
      return (<span>{iconClass} {node.label}</span>)
    },
    typeFormatter(row, data) {
      if (this.currentNode.type == '4') {
        return this.$t('pages.dept')
      } else if (this.currentNode.type == 'R') {
        return this.$t('pages.role')
      } else {
        return this.$t('pages.user')
      }
    },
    userFormatter(row, data) {
      return this.currentNode.label
    },
    categoryFormatter(row, data) {
      return keyValueFilter(this.categoryType)[row.category]
    },
    inheritFormatter(row, data) {
      return data ? this.$t('pages.approvalProcess_Msg1') : this.$t('pages.approvalProcess_Msg2')
    },
    handleNodeClickFunc(data, node, el) {
      this.tableLoading = true
      this.currentNode = data
      const requestBy = {}
      if (data.type == '4') {
        requestBy.startableByDept = data.dataId
      } else if (data.type == 'RG') {
        this.rowData = []
        this.tableLoading = false
        return
      } else if (data.type == 'R') {
        requestBy.assigneedToGroup = data.dataId
      } else {
        requestBy.startableByUser = data.dataId
      }
      getProcessList(requestBy).then(res => {
        this.rowData = res.data
        this.tableLoading = false
      })
    },
    handleAssign() {
      this.submitting = true
      this.checkedKeys = []
      getProcessTree().then(res => {
        this.processTreeData = res.data
        const requestBy = {}
        if (this.currentNode.type == '4') {
          requestBy.deptId = this.currentNode.dataId
        } else if (this.currentNode.type == 'R') {
          if (this.currentNode.id == 0 || this.currentNode.parentId == 'RG0') {
            return
          }
          requestBy.groupId = this.currentNode.dataId
        } else {
          requestBy.userId = this.currentNode.dataId
        }
        getAssignedList(requestBy).then(res => {
          if (res.code === 20000) {
            this.dialogVisible = true
            this.inherit = this.currentNode.dataId === '0' ? false : res.data.flowInherit
            this.disabledCheck = this.currentNode.dataId === '0'
            this.submitting = false
            this.$nextTick(() => {
              this.checkedKeys = res.data.approvalFlows
              this.oriProcessIds = res.data.approvalFlows
            })
          } else {
            this.submitting = false
          }
        }).catch(err => {
          console.log(err)
          this.submitting = false
        })
      }).catch(err => {
        console.log(err)
        this.submitting = false
      })
    },
    assignProcess() {
      this.submitting = true
      const checkedKeys = this.$refs.processTree.$refs.tree.getCheckedNodes().filter(item => item.dataCode === 'L').map(item => item.dataId).join()
      let obj = {}
      if (this.currentNode.type == '4') {
        obj = {
          processDefinitionId: checkedKeys,
          inherit: this.inherit,
          deptId: this.currentNode.dataId,
          oriProcessIds: this.oriProcessIds
        }
      } else if (this.currentNode.type == 'R') {
        obj = {
          processDefinitionId: checkedKeys,
          // inherit: true,
          groupId: this.currentNode.dataId,
          oriProcessIds: this.oriProcessIds
        }
      } else {
        obj = {
          processDefinitionId: checkedKeys,
          inherit: this.inherit,
          userId: this.currentNode.dataId,
          oriProcessIds: this.oriProcessIds
        }
      }
      assignProcess(obj).then(res => {
        this.dialogVisible = false
        this.submitting = false
        this.handleNodeClickFunc(this.currentNode)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.allocationSucceeded'),
          type: 'success',
          duration: 2000
        })
      }).catch(err => {
        console.log(err)
        this.submitting = false
      })
    },
    processSteps(row) {
      getProcessById(row.id).then(res => {
        this.dialogStepsVisible = true
        const data = res.data
        this.approverList = data.steps
        this.initiatorList = data.assignedUserOrDeptVOs
      })
    }
  }
}
</script>

<style lang="scss">
.assign-tree{
  .el-tree{
    margin-bottom: 10px;
  }
}

</style>
