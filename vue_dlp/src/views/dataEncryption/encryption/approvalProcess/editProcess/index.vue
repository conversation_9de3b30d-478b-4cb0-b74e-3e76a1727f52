<template>
  <div class="app-container edit-process">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="processTree"
        :data="processTreeData"
        :is-filter="false"
        :render-content="renderDeviceContentFunc"
        @node-click="handleNodeClickFunc"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :disabled="!currentNode" @click="handleCreateGroup">
          {{ $t('pages.addFloatGroup') }}
        </el-button>
        <el-button type="primary" size="mini" :disabled="!currentNode" @click="handleCreateProcess">
          {{ $t('pages.addFloat') }}
        </el-button>
        <el-button type="primary" size="mini" :disabled="!deleteable" @click="handleBatchUpdate">
          {{ $t('pages.batchEditFloatGroup') }}
        </el-button>
        <el-button type="primary" size="mini" :disabled="!deleteable" @click="batchDelProcess">
          {{ $t('pages.batchDeleteFloat') }}
        </el-button>
        <el-button icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <el-button icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <div class="searchCon">
          <el-select v-model="query.category" :placeholder="$t('pages.funName')" style="width: 150px;">
            <el-option
              v-for="item in categoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            v-model="query.nameLike"
            v-trim
            clearable
            :placeholder="$t('pages.floatName')"
            style="width: 160px"
            @keyup.enter.native="handleFilter"
          ></el-input>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleFilter"
          >{{ $t('table.search') }}</el-button>
          <el-popover
            placement="bottom-end"
            width="350"
            trigger="click"
            :append-to-body="false"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('pages.approverName')">
                <el-input v-model="query.approverNameLike" v-trim clearable maxlength=""/>
              </FormItem>
              <FormItem :label="$t('pages.approverAccount')">
                <el-input v-model="query.approverAccountLike" v-trim clearable maxlength=""/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="() => { resetQuery() }">{{ $t('button.reset') }}</el-button>
              <el-button :loading="submitting" type="primary" size="mini" @click="handleFilter">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="tableList"
        v-loading="tableLoading"
        :col-model="colModel"
        :show-pager="false"
        :row-datas="rowData"
        :default-sort="{prop:'', order: ''}"
        :selectable="selectable"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <export-dlg ref="exportDlg" :title="title" :group-tree-data="allProcessTreeData" :node-key="nodeKey" :export-func="exportFunc" :group-tree-id="selectTreeId" @childSelectNodeData="childSelectNodeData"/>
    <import-dlg ref="importDlg" :title="title" template="approvalFlow" :show-import-type="false" :file-name="title" :tip="tip" :show-import-way="true" :upload-func="upload" @success="importEndFunc"/>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogGroupVisible"
      :append-to-body="false"
      :width="$store.getters.language === 'en' ? '800px' : '700px'"
    >
      <Form
        ref="groupForm"
        :rules="groupRules"
        :model="groupForm"
        label-position="right"
        label-width="80px"
        :style="{width: $store.getters.language === 'en' ? '750px' : '600px'}"
      >
        <FormItem :label="$t('pages.funType')">
          <el-row>
            <el-col>
              <el-checkbox v-model="checkAll" :disabled="dialogStatus == 'updateGroup'" :indeterminate="isIndeterminate" @change="handleCheckAllChange">{{ $t('button.selectAll') }}</el-checkbox>
            </el-col>
          </el-row>
          <el-row v-for="(item,key) in formData" :key="key">
            <el-col :span="7">
              <el-checkbox v-model="item.isSel" :disabled="item.checkDisabled">{{ item.label }}</el-checkbox>
            </el-col>
            <el-col :span="17">
              <FormItem :label="$t('pages.groupParentNode')" label-width="100px">
                <tree-select
                  ref="tree"
                  :data="item.treeData"
                  node-key="dataId"
                  :checked-keys="item.defaultCheckedKeys"
                  :disabled="item.selectDisabled"
                  @clickSelect="clickSelect"
                ></tree-select>
              </FormItem>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('pages.groupNames')" prop="groupName">
          <el-input v-model="groupForm.groupName" v-trim :maxlength="255"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :loading="submitting"
          @click="dialogStatus==='createGroup'?createGroup():updateGroup()"
        >{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogGroupVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogProcessVisible"
      :append-to-body="false"
      width="800px"
    >
      <el-tabs v-model="activeName" type="card" class="process-tab" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.baseInfo')" name="first">
          <Form
            ref="processForm"
            :rules="processRules"
            :model="processForm"
            label-position="right"
            label-width="80px"
            style="width: 600px;"
          >
            <FormItem :label="$t('pages.funType')">
              <el-row>
                <el-col>
                  <el-checkbox v-model="checkAll" :disabled="dialogStatus == 'updateProcess'" :indeterminate="isIndeterminate" @change="handleCheckAllChange">{{ $t('button.selectAll') }}</el-checkbox>
                </el-col>
              </el-row>
              <el-row v-for="(item,key) in formData" :key="key">
                <el-col :span="8">
                  <el-checkbox v-model="item.isSel" :disabled="item.checkDisabled" @change="handleCheckedItemsChange">{{ item.label }}</el-checkbox>
                </el-col>
                <el-col :span="16">
                  <FormItem :label="$t('pages.floatGroup')" label-width="100px">
                    <tree-select
                      ref="tree"
                      :data="item.treeData"
                      node-key="dataId"
                      :checked-keys="item.defaultCheckedKeys"
                      :disabled="item.selectDisabled"
                      @clickSelect="clickSelect"
                    />
                  </FormItem>
                </el-col>
              </el-row>
            </FormItem>
            <FormItem :label="$t('pages.floatType')">
              <el-radio
                v-model="processForm.verifycode"
                :label="false"
                :disabled="dialogStatus==='updateProcess'"
              >{{ $t('pages.commonFloat') }}</el-radio>
              <el-radio
                v-model="processForm.verifycode"
                :label="true"
                :disabled="dialogStatus==='updateProcess'"
              >{{ $t('pages.codeFloat') }}</el-radio>
            </FormItem>
            <FormItem :label="$t('pages.floatName')" prop="flowName">
              <el-input v-model="processForm.flowName" v-trim :maxlength="100"></el-input>
            </FormItem>
          </Form>
          <ApprovalChart
            v-if="dialogProcessVisible === true"
            ref="chart"
            :approver-list="approverList"
            :initiator-list="initiatorList"
            :status="dialogStatus"
            :verifycode="processForm.verifycode"
          />
        </el-tab-pane>
        <el-tab-pane v-if="showSetting" :label="$t('pages.fileOutgoingConfig')" name="second" class="advanced-setting">
          <Form ref="settingForm" :model="settingForm" label-position="left" style="width: 600px;">
            <div v-if="showLimit('secret')">
              <span class="desc">{{ $t('pages.approvalProcess_Msg3') }}<span v-if="!showText">:</span><span v-if="showText">--{{ $t('pages.approvalProcess_Msg4') }}</span></span>
              <FormItem >
                <el-checkbox-group v-model="settingForm.decryptLevelLimit">
                  <el-checkbox v-for="item in secretType" v-show="item.value<=visibleCount" :key="item.value" :label="item.value">
                    {{ item.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </FormItem>
            </div>
            <div v-if="showLimit('secretAndSensitiveOutsend')">
              <span class="desc">{{ $t('pages.approvalProcess_Msg58') }}<span v-if="!showText">:</span><span v-if="showText">--{{ $t('pages.approvalProcess_Msg64') }}</span></span>
              <FormItem>
                <el-checkbox-group v-model="settingForm.penetrateSuffix">
                  <el-checkbox v-for="(item, index) in zipSuffixes" :key="index" :label="item">{{ item }}</el-checkbox>
                </el-checkbox-group>
              </FormItem>
            </div>
            <div v-if="showLimit('sensitiveFile')">
              <span class="desc">{{ $t('pages.approvalProcess_Msg60') }}<span v-if="!showText">:</span><span v-if="showText">--{{ $t('pages.approvalProcess_Msg59') }}</span></span>
              <FormItem >
                <el-checkbox v-show="hasEncPermision" v-model="settingForm.forbiddenApplySensitive" :true-label="'1'" :false-label="'0'">
                  {{ $t('pages.approvalProcess_Msg62') }}
                </el-checkbox>
                <el-checkbox v-model="settingForm.autoDecFile" :true-label="'1'" :false-label="'0'">{{ $t('pages.approvalProcess_Msg63') }}</el-checkbox>
              </FormItem>
            </div>
            <div v-if="showLimit('suffix')" >
              <span class="desc">{{ $t('pages.approvalProcess_Msg5') }}<span v-if="!showText">:</span><span v-if="showText">--{{ $t('pages.approvalProcess_Msg6') }}</span></span>
              <el-row>
                <el-col :span="22">
                  <FormItem :label="$t('pages.approvalProcess_Msg7')" label-width="60px">
                    <el-input
                      v-model="settingForm.fileExtAllow"
                      type="textarea"
                      :placeholder="$t('pages.approvalProcess_Msg8')"
                      :maxlength="100"
                    ></el-input>
                  </FormItem>
                </el-col>
                <el-col :span="2">
                  <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
                    <el-button style="margin-left: 1px" type="primary" size="mini" @click="handleFileSuffixImport('fileExtAllow')">
                      <svg-icon icon-class="import" />
                    </el-button>
                  </el-tooltip>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="22">
                  <FormItem :label="$t('pages.approvalProcess_Msg9')" label-width="60px">
                    <el-input
                      v-model="settingForm.fileExtForbit"
                      type="textarea"
                      :placeholder="$t('pages.approvalProcess_Msg8')"
                      :maxlength="100"
                    ></el-input>
                  </FormItem>
                </el-col>
                <el-col :span="2">
                  <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
                    <el-button style="margin-left: 1px" type="primary" size="mini" @click="handleFileSuffixImport('fileExtForbit')">
                      <svg-icon icon-class="import" />
                    </el-button>
                  </el-tooltip>
                </el-col>
              </el-row>
              <span class="littleTip">{{ $t('pages.approvalProcess_Msg10') }}</span>
            </div>
            <div v-if="showLimit('intelligence')" >
              <span class="desc">{{ $t('pages.approvalProcess_Msg11') }}<span v-if="!showText">:</span><span v-if="showText">--{{ $t('pages.approvalProcess_Msg61') }}</span></span>
              <div class="intelligence" style="width: 92%">
                <p>
                  <span v-if="intelligence&&(settingForm.intelligenceProperties.count||settingForm.intelligenceProperties.size)" class="text">
                    <i18n path="pages.approvalProcess_Msg12">
                      <label slot="time" style="color:#409EFF">{{ time }}</label>
                      <span slot="limit" style="font-size: 0px">
                        <span v-if="!isNaN(settingForm.intelligenceProperties.count)&&settingForm.intelligenceProperties.count">
                          <i18n path="pages.approvalProcess_Msg13" style="font-size: 14px">
                            <label slot="count" style="color:#409EFF">{{ settingForm.intelligenceProperties.count }}</label>
                          </i18n>
                        </span>
                        <span v-if="!isNaN(settingForm.intelligenceProperties.size)&&settingForm.intelligenceProperties.size">
                          <span v-if="settingForm.intelligenceProperties.count&&settingForm.intelligenceProperties.size" style="font-size: 14px">{{ `${$t('pages.approvalProcess_Msg15')} ` }}</span>
                          <i18n path="pages.approvalProcess_Msg14" style="font-size: 14px">
                            <label slot="count" style="color:#409EFF">{{ settingForm.intelligenceProperties.size }}</label>
                          </i18n>
                        </span>
                      </span>
                    </i18n>
                  </span>
                  <span v-else class="placeholder">{{ $t('pages.approvalProcess_Msg16') }}</span>
                </p>
                <div class="form">
                  <FormItem label-width="130px" :label="$t('pages.approvalProcess_Msg17')">
                    <el-switch v-model="intelligence" @change="handleIntelligence"></el-switch>
                  </FormItem>
                  <FormItem label-width="130px" :label="$t('pages.approvalProcess_Msg18')">
                    <el-radio-group v-model="settingForm.intelligenceProperties.timeType" :disabled="!intelligence" >
                      <el-radio v-for="item in timeType" :key="item.value" :label="item.value">
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </FormItem>

                  <FormItem
                    label-width="130px"
                    :label="$t('pages.approvalProcess_Msg19')"
                    prop="intelligenceProperties.count"
                  >
                    <el-input-number
                      v-model="settingForm.intelligenceProperties.count"
                      :disabled="!intelligence"
                      style="width:70px"
                      size="mini"
                      :controls="false"
                      :min="1"
                      :max="9999"
                      :precision="0"
                    > </el-input-number> {{ $t('pages.approvalProcess_Msg20') }}
                  </FormItem>

                  <FormItem
                    label-width="130px"
                    :label="$t('pages.approvalProcess_Msg21')"
                    prop="intelligenceProperties.size"
                  >
                    <el-input-number
                      v-model="settingForm.intelligenceProperties.size"
                      style="width:70px"
                      :disabled="!intelligence"
                      size="mini"
                      :controls="false"
                      :min="1"
                      :max="9999"
                      :precision="0"
                    ></el-input-number> (MB)
                  </FormItem>
                  <label v-if="showTip" style="color:red">{{ $t('pages.approvalProcess_Msg22') }}</label>
                </div>
              </div>
            </div>
            <div v-if="showLimit('intelligence')" style="margin-top: 10px">
              <span class="desc">{{ $t('pages.approvalProcess_Msg75') }}<span v-if="!showText">:</span><span v-if="showText">--{{ $t('pages.approvalProcess_Msg61') }}</span></span>
              <div class="intelligence" style="width: 92%">
                <p>
                  <span v-if="applyLimit&&(settingForm.applyLimits.fileCount||settingForm.applyLimits.fileSize)" class="text">
                    <i18n path="pages.approvalProcess_Msg78">
                      <label slot="time" style="color:#409EFF">{{ limitTime }}</label>
                      <span slot="limit" style="font-size: 0px">
                        <span v-if="!isNaN(settingForm.applyLimits.fileCount)&&settingForm.applyLimits.fileCount">
                          <i18n path="pages.approvalProcess_Msg79" style="font-size: 14px">
                            <label slot="count" style="color:#409EFF">{{ settingForm.applyLimits.fileCount }}</label>
                          </i18n>
                        </span>
                        <span v-if="!isNaN(settingForm.applyLimits.fileSize)&&settingForm.applyLimits.fileSize">
                          <span v-if="settingForm.applyLimits.fileCount&&settingForm.applyLimits.fileSize" style="font-size: 14px">{{ `${$t('pages.approvalProcess_Msg81')}` }}</span>
                          <i18n path="pages.approvalProcess_Msg80" style="font-size: 14px">
                            <label slot="count" style="color:#409EFF">{{ settingForm.applyLimits.fileSize }}</label>
                          </i18n>
                        </span>
                      </span>
                    </i18n>
                  </span>
                  <!-- <span v-else class="placeholder">{{ $t('pages.approvalProcess_Msg16') }}</span> -->
                  <span v-else class="placeholder">{{ $t('pages.approvalProcess_Msg77') }}</span>
                </p>
                <div class="form">
                  <FormItem label-width="130px" :label="$t('pages.approvalProcess_Msg76')">
                    <el-switch v-model="applyLimit" @change="handleApplyLimit"></el-switch>
                  </FormItem>
                  <FormItem label-width="130px" :label="$t('pages.approvalProcess_Msg18')">
                    <el-radio-group v-model="settingForm.applyLimits.timeType" :disabled="!applyLimit" >
                      <el-radio v-for="item in timeType" :key="item.value" :label="item.value">
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </FormItem>

                  <FormItem
                    label-width="130px"
                    :label="$t('pages.approvalProcess_Msg85')"
                    prop="applyLimits.fileCount"
                  >
                    <el-input-number
                      v-model="settingForm.applyLimits.fileCount"
                      :disabled="!applyLimit"
                      style="width:70px"
                      size="mini"
                      :controls="false"
                      :min="1"
                      :max="9999"
                      :precision="0"
                    > </el-input-number> {{ $t('pages.approvalProcess_Msg86') }}
                  </FormItem>

                  <FormItem
                    label-width="130px"
                    :label="$t('pages.approvalProcess_Msg21')"
                    prop="applyLimits.fileSize"
                  >
                    <el-input-number
                      v-model="settingForm.applyLimits.fileSize"
                      style="width:70px"
                      :disabled="!applyLimit"
                      size="mini"
                      :controls="false"
                      :min="1"
                      :max="9999"
                      :precision="0"
                    ></el-input-number> (MB)
                  </FormItem>
                  <label v-if="showLimitTip" style="color:red">{{ $t('pages.approvalProcess_Msg22_1') }}</label>
                </div>
              </div>
            </div>
            <div v-if="showLimit('watermarkEx')" style="margin-top: 10px">
              <el-checkbox v-model="watermarkLimitsEx"><span class="desc">{{ $t('pages.approvalProcess_Msg23') }}</span></el-checkbox>
              <span v-if="!showText" style="margin-left:-20px">:</span><span v-if="showText" class="desc" style="margin-left:-20px">--{{ $t('pages.approvalProcess_Msg89') }}</span>
              <fieldset>
                <legend>{{ $t('pages.approvalProcess_Msg24') }}</legend>
                <el-radio-group v-model="watermarkTimeOptEx">
                  <el-radio :label="4" :disabled="!watermarkLimitsEx">{{ $t('pages.approvalProcess_Msg91') }}</el-radio>
                  <br />
                  <el-radio :label="1" :disabled="!watermarkLimitsEx || !(JSON.parse(watermarkAllowMdyEx) == JSON.parse(allowNotLimitEx))">{{ $t('pages.approvalProcess_Msg90') }}</el-radio>
                  <br />
                  <el-radio :label="2" :disabled="!watermarkLimitsEx">
                    <i18n path="pages.approvalProcess_Msg25">
                      <el-input-number slot="minutes" v-model="watermarkMinutesEx" :controls="false" :disabled="!watermarkLimitsEx || watermarkTimeOptEx != 2" :min="1" :max="16777214" size="mini" :precision="0" style="width: 200px;"/>
                    </i18n>
                  </el-radio>
                  <br />
                  <label v-if="timeTipEx" style="color:red">{{ $t('pages.timeNotNull') }}</label>
                  <br />
                  <el-radio :label="3" :disabled="!watermarkLimitsEx">
                    <i18n path="pages.approvalProcess_Msg92">
                      <el-date-picker
                        slot="range"
                        v-model="watermarkDateRangeEx"
                        type="datetime"
                        :placeholder="$t('pages.endDate')"
                        :disabled="!watermarkLimitsEx || watermarkTimeOptEx != 3"
                        :class="{dateDisabled: !watermarkLimitsEx || watermarkTimeOptEx != 3}"
                        style="width: 200px;"
                      >
                      </el-date-picker>
                    </i18n>
                  </el-radio>
                  <br />
                  <label v-if="timeRangeTipEx" style="color:red">{{ !watermarkDateRangeEx ? $t('pages.timeRangeNotNull') : $t('pages.approvalProcess_Msg71') }}</label>
                </el-radio-group>
              </fieldset>
              <el-checkbox v-model="watermarkAllowMdyEx" :disabled="!watermarkLimitsEx" true-label="true" false-label="false" style="margin-top: 10px; color: #666 !importment;" @change="allowMdyChangeEx">{{ $t('pages.approvalProcess_Msg29') }}</el-checkbox><br/>
              <el-checkbox v-model="allowNotLimitEx" :disabled="!(watermarkLimitsEx && JSON.parse(watermarkAllowMdyEx))" true-label="true" false-label="false" style="margin-top: 10px; color: #666 !importment;" @change="allowChangeEx">{{ $t('pages.approvalProcess_Msg65') }}</el-checkbox>
            </div>
            <div v-if="showLimit('watermark')" style="margin-top: 10px">
              <el-checkbox v-model="watermarkLimits"><span class="desc">{{ $t('pages.approvalProcess_Msg23') }}</span></el-checkbox>
              <span v-if="!showText" style="margin-left:-20px">:</span><span v-if="showText" class="desc" style="margin-left:-20px">--{{ $t('pages.approvalProcess_Msg26') }}</span>
              <fieldset>
                <legend>{{ $t('pages.approvalProcess_Msg24') }}</legend>
                <el-radio-group v-model="watermarkTimeOpt">
                  <el-radio :label="1" :disabled="!watermarkLimits || !(JSON.parse(watermarkAllowMdy) == JSON.parse(allowNotLimit))">{{ $t('pages.approvalProcess_Msg90') }}</el-radio>
                  <br />
                  <el-radio :label="2" :disabled="!watermarkLimits">
                    <i18n path="pages.approvalProcess_Msg25">
                      <el-input-number slot="minutes" v-model="watermarkMinutes" :controls="false" :disabled="!watermarkLimits || watermarkTimeOpt != 2" :min="1" :max="16777214" size="mini" :precision="0" style="width: 200px;"/>
                    </i18n>
                  </el-radio>
                  <br />
                  <label v-if="timeTip" style="color:red">{{ $t('pages.timeNotNull') }}</label>
                  <br />
                  <el-radio :label="3" :disabled="!watermarkLimits">
                    <i18n path="pages.approvalProcess_Msg27">
                      <el-date-picker
                        slot="range"
                        v-model="watermarkDateRange"
                        type="datetimerange"
                        :range-separator="$t('pages.till')"
                        :start-placeholder="$t('pages.startDate')"
                        :end-placeholder="$t('pages.endDate')"
                        :default-time="['00:00:00', '23:59:59']"
                        :disabled="!watermarkLimits || watermarkTimeOpt != 3"
                        style="width: 400px;"
                      />
                    </i18n>
                  </el-radio>
                  <br />
                  <label v-if="timeRangeTip" style="color:red">{{ !watermarkDateRange ? $t('pages.timeRangeNotNull') : $t('pages.approvalProcess_Msg71') }}</label>
                </el-radio-group>
              </fieldset>
              <el-checkbox v-model="watermarkAllowMdy" :disabled="!watermarkLimits" true-label="true" false-label="false" style="margin-top: 10px; color: #666 !importment;" @change="allowMdyChange">{{ $t('pages.approvalProcess_Msg29') }}</el-checkbox><br/>
              <el-checkbox v-model="allowNotLimit" :disabled="!(watermarkLimits && JSON.parse(watermarkAllowMdy))" true-label="true" false-label="false" style="margin-top: 10px; color: #666 !importment;" @change="allowChange">{{ $t('pages.approvalProcess_Msg65') }}</el-checkbox>
            </div>
          </Form>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="dialogStatus == 'updateProcess'" type="primary" :loading="submitting" @click="saveAs">
          {{ $t('components.saveAs') }}
        </el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="dialogStatus==='createProcess'?createProcess():updateProcess()"
        >{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogProcessVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.batchEditFloatGroup')"
      :visible.sync="batchVisible"
      width="400px"
    >
      <Form
        ref="batchForm"
        label-position="right"
        label-width="80px"
      >
        <FormItem :label="$t('pages.floatGroup')" label-width="100px">
          <tree-select ref="belongTree" :data="belongTreeData" node-key="id" :checked-keys="checkedKeys"></tree-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="batchUpdate">{{ $t('button.confirm') }}</el-button>
        <el-button @click="batchVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      title="流程另存为"
      :visible.sync="saveAsVisible"
      width="400px"
    >
      <p style="padding-left: 10px;">确认将当前流程内容拷贝并另存为：</p>
      <Form
        ref="saveAsForm"
        label-position="right"
        label-width="80px"
      >
        <FormItem :label="$t('pages.floatName')" prop="flowName" class="required">
          <el-input v-model="saveAsflowName" v-trim :maxlength="100"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="saveAsProcess()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="saveAsVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <TreeTableTransfer
      :dialog-visible="dialogShow"
      :node-model="nodeModel"
      @sendApprover="getApprover"
      @close="dialogShow = false"
    />
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
  </div>
</template>

<script>
import {
  addGroup,
  addProcess,
  batchDelProcess,
  batchUpdateGroup,
  beforeDelProcess,
  beforeUpdateProcess,
  delGroup,
  delProcess,
  getCategory,
  getDenseSet,
  getGroupTree,
  getList,
  getProcessById,
  getProcessList,
  updateGroup,
  updateInitiator,
  updateProcess,
  exportExcel
} from '@/api/dataEncryption/encryption/approvalProcess'
import ApprovalChart from '@/components/ApprovalChart'
import TreeTableTransfer from '@/components/TreeTableTransfer'
import { parseTime } from '@/utils'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import ExportDlg from '@/views/common/export'
import ImportDlg from '@/views/common/import'
import request from '@/utils/request'
import moment from 'moment'
import { getSetting } from '@/api/dataEncryption/docPemission/denseSet'
// 筛选出只有分组的树
// const filterTree = (data) => {
//   const arr = data.filter(item => {
//     return item.dataCode === 'G'
//   }).map(item => {
//     item = Object.assign({}, item)
//     if (item.children) {
//       item.children = filterTree(item.children)
//     }
//     return item
//   })
//   return arr
// }

// const timeType = [
//   { value: 'day', label: this.$t('pages.sameDay') },
//   { value: 'week', label: this.$t('pages.thisWeek') },
//   { value: 'month', label: this.$t('pages.thisMonth') }
// ]

// const secretType = [
//   { value: '0', label: this.$t('pages.publicDocuments') },
//   { value: '1', label: this.$t('pages.internalDocument') },
//   { value: '2', label: this.$t('pages.secretDocuments') },
//   { value: '3', label: this.$t('pages.confidentialDocuments') },
//   { value: '4', label: this.$t('pages.topSecretDocument') }
// ]

const keyValueFilter = function(options) {
  const typeKeyValue = options.reduce((acc, cur) => {
    acc[cur.value] = cur.label
    return acc
  }, {})
  return typeKeyValue
}

export default {
  name: 'EditProcess',
  components: { ExportDlg, ImportDlg, ApprovalChart, TreeTableTransfer, FileSuffixLibImport },
  data() {
    return {
      query: {
        category: '',
        nameLike: '',
        approverAccountLike: '',
        approverNameLike: ''
      },
      processTreeData: [],
      allProcessTreeData: [], // 流程分组树，加上  导出所有流程
      // defaultExpandedKeys: ['U'],
      expandedNodeList: [],
      tableLoading: false,
      showTree: true,
      submitting: false,
      iconOption: {
        G: 'service',
        L: 'process'
      },
      textMap: {
        updateProcess: this.$t('pages.editFloat'),
        createProcess: this.$t('pages.addFloat1'),
        createGroup: this.$t('pages.addFloatGroup1'),
        updateGroup: this.$t('pages.editFloatGroup')
      },
      dialogStatus: '',
      dialogGroupVisible: false,
      dialogProcessVisible: false,
      groupRules: {
        groupName: [
          { required: true, message: this.$t('pages.approvalProcess_Msg35'), trigger: 'blur' }
        ]
      },
      groupForm: {
        id: '',
        groupName: '',
        type: ''
      },
      processRules: {
        flowName: [
          { required: true, message: this.$t('pages.approvalProcess_Msg36'), trigger: 'blur' }
        ]
      },
      processForm: {
        id: '',
        verifycode: false,
        flowName: ''
      },
      settingForm: {
        decryptLevelLimit: [],
        penetrateSuffix: [],
        fileExtAllow: '',
        fileExtForbit: '',
        intelligenceProperties: {
          timeType: '',
          count: undefined,
          size: undefined
        },
        applyLimits: {
          timeType: '',
          fileCount: undefined,
          fileSize: undefined
        },
        cancelWatermarkLimitsEx: {
          effectiveOnce: '0',
          cancelWatermarkAllowMdy: '',
          cancelWatermarkMinutes: '',
          cancelWatermarkBeginTime: '',
          cancelWatermarkEndTime: '',
          allowNotLimit: ''
        },
        cancelWatermarkLimits: {
          cancelWatermarkAllowMdy: '',
          cancelWatermarkMinutes: '',
          cancelWatermarkBeginTime: '',
          cancelWatermarkEndTime: '',
          allowNotLimit: ''
        },
        forbiddenApplySensitive: '0', // 申请文件需要敏感检测 '1'-不允许 '0'-允许
        autoDecFile: '0'  // 允许通过后自动解密文件 '1'-是 '0'-否
      },
      watermarkLimits: false, // 是否勾选流程参数申请限制-打印审批行为管控审批
      watermarkTimeOpt: 2,
      watermarkMinutes: '30',
      watermarkDateRange: ['2021-07-07 01:00:00', '2021-07-07 01:00:00'],
      watermarkAllowMdy: 'false',
      allowNotLimit: 'false',
      watermarkLimitsEx: false, // 是否勾选流程参数申请限制-取消文档水印审批
      watermarkTimeOptEx: 4,
      watermarkMinutesEx: '30',
      watermarkDateRangeEx: '2021-07-07 01:00:00',
      watermarkAllowMdyEx: 'false',
      allowNotLimitEx: 'false',
      checked1: true,
      activeName: 'first',
      formData: [],
      rowData: [],
      currentNode: undefined,
      timeType: [
        { value: 'day', label: this.$t('pages.sameDay') },
        { value: 'week', label: this.$t('pages.thisWeek') },
        { value: 'month', label: this.$t('pages.thisMonth') }
      ],
      secretType: [],
      zipSuffixes: ['.zip', '.rar', '.7z', '.tar', '.gz'],
      deleteable: false,
      approverList: [],
      initiatorList: [],
      batchVisible: false,
      treeData: [],
      belongTreeData: [],
      checkedKeys: [],
      dialogShow: false,
      nodeModel: {},
      currentFlowId: '',
      intelligence: false,
      applyLimit: false, // 是否限制流程
      visibleCount: undefined,
      categoryList: [],
      sameOperation: true,
      cancelOperation: false,
      // 用于判断当前列表显示的是搜索的结果，还是显示点击树后查询的结果
      searchFilter: false,
      importFileSuffixType: '',
      hasEncPermision: true,  // 是包含敏感信息识别模块  80
      suffixMaxLength: 255,
      userGroupTreeData: [],
      selectTreeId: '-1',
      selectNode: undefined,
      nodeKey: 'id',
      title: this.$t('export.approvalFlow'),
      tip: this.$t('table.floatName'),
      checkAll: false,
      isIndeterminate: true,
      saveAsVisible: false,
      saveAsflowName: '' // 流程另存为流程名称
    }
  },
  computed: {
    gridTable() {
      return this.$refs['tableList']
    },
    time() {
      return keyValueFilter(this.timeType)[this.settingForm.intelligenceProperties.timeType]
    },
    limitTime() {
      return keyValueFilter(this.timeType)[this.settingForm.applyLimits.timeType]
    },
    showSetting() {
      return this.showLimit('secret') || this.showLimit('secretAndSensitiveOutsend') || this.showLimit('suffix') || this.showLimit('intelligence') || this.showLimit('watermarkEx') || this.showLimit('watermark') || this.showLimit('sensitiveFile')
    },
    showTip() {
      return this.intelligence && (!this.settingForm.intelligenceProperties.size && !this.settingForm.intelligenceProperties.count)
    },
    showLimitTip() {
      return this.applyLimit && (!this.settingForm.applyLimits.fileSize && !this.settingForm.applyLimits.fileCount)
    },
    showText() {
      return this.formData.filter(item => item.isSel).length > 1
    },
    timeRangeTip() {
      if (this.watermarkLimits && this.watermarkTimeOpt == 3) {
        if (!this.watermarkDateRange) {
          return true
        } else if (new Date(this.watermarkDateRange[1]).getTime() < new Date().getTime()) {
          return true
        } else {
          return false
        }
      }
      return false
    },
    timeTip() {
      if (this.watermarkLimits && this.watermarkTimeOpt == 2 && !this.watermarkMinutes) {
        return true
      } else {
        return false
      }
    },
    timeRangeTipEx() {
      if (this.watermarkLimitsEx && this.watermarkTimeOptEx == 3) {
        if (!this.watermarkDateRangeEx) {
          return true
        } else if (new Date(this.watermarkDateRangeEx).getTime() < new Date().getTime()) {
          return true
        } else {
          return false
        }
      }
      return false
    },
    timeTipEx() {
      if (this.watermarkLimitsEx && this.watermarkTimeOptEx == 2 && !this.watermarkMinutesEx) {
        return true
      } else {
        return false
      }
    },
    colModel() {
      const colModel = [
        { prop: 'label', label: 'name', width: '150' },
        { label: 'groupFloat', width: '100', formatter: this.entityFormatter },
        { label: 'funType', width: '130', formatter: this.typeFormatter },
        { label: 'floatGroup', width: '130', formatter: this.parentFormatter },
        {
          prop: 'verifycode',
          label: 'floatType',
          width: '100',
          formatter: this.verifycodeFormatter
        },
        {
          prop: 'intelligence',
          label: 'fileOutgoingConfig',
          width: '100',
          formatter: this.intelligenceFormatter
        },
        {
          label: 'operate',
          type: 'button',
          fixedWidth: '160',
          buttons: [
            { label: 'applicant', click: this.handleInitiator, isShow: (data) => data.dataCode === 'L' },
            { label: 'edit', click: this.handleUpdate, isShow: (data) => data.oriData.auto !== true },
            { label: 'delete', click: this.handleDel, isShow: (data) => data.oriData.auto !== true }
          ]
        }
      ]
      const type = this.currentNode && this.currentNode.type
      if (type === 'offline' || type === 'fileRelieveJurisdiction') {
        const index = colModel.findIndex(item => item.prop === 'intelligence')
        index >= 0 && colModel.splice(index, 1)
      }
      return colModel
    }
  },
  mounted() {

  },
  created() {
    // 获取注册模块
    this.listModule()
    this.getProcessTree()
    this.getDenseSet()
  },
  methods: {
    // 获取密级设置
    getDenseSet() {
      getSetting().then(res => {
        this.secretType = res.data.denseInfoList.map(item => {
          return { value: String(item.encryptLevel), label: item.denseName }
        })
      })
    },
    async listModule() {
      await existSaleModule(80).then(resp => {
        this.hasEncPermision = resp.data
      })
    },
    disabledFun() {
      return true
    },
    selectable(row, index) {
      return row.dataCode === 'L' && row.oriData.auto === false
    },
    verifycodeFormatter: function(row, data) {
      return row.oriData.verifycode !== undefined
        ? row.oriData.verifycode
          ? this.$t('pages.codeFloat')
          : this.$t('pages.commonFloat')
        : ''
    },
    intelligenceFormatter: function(row, data) {
      return row.dataCode === 'G' ? '' : row.oriData.extralPropertiesDTO ? this.$t('text.open') : this.$t('text.dontOpen1')
    },
    entityFormatter: function(row, data) {
      return row.dataCode === 'G' ? this.$t('pages.group') : this.$t('pages.float')
    },
    typeFormatter: function(row, data) {
      return keyValueFilter(this.categoryList)[row.type]
    },
    parentFormatter: function(row, data) {
      return row.dataCode === 'G' ? row.oriData.parentGroupName : row.oriData.groupName
    },
    handleFilter() {
      this.searchFilter = true
      getProcessList({
        category: this.query.category,
        nameLike: this.query.nameLike,
        approverAccountLike: this.query.approverAccountLike,
        approverNameLike: this.query.approverNameLike,
        treeType: true
      }).then(res => {
        this.rowData = res.data
        this.tableLoading = false
      })
    },
    handleUpdate(row) {
      row.dataCode === 'G'
        ? this.handleUpdateGroup(row)
        : this.handleUpdateProcess(row)
    },
    handleDel(row) {
      row.dataCode === 'G'
        ? this.handleDelGroup(row)
        : this.handleDelProcess(row)
    },
    getProcessTree() {
      getCategory().then(res => {
        this.categoryList = res.data.map(item => {
          return {
            value: item.category,
            label: item.remark
          }
        })
        this.query.category = this.categoryList[0].value
        getGroupTree().then(res => {
        // this.treeData = filterTree(res.data)
          this.treeData = res.data
          this.processTreeData = res.data
          this.allProcessTreeData.splice(0)
          const headData = { 'id': '-1', 'dataId': '-1', 'type': '', 'label': this.$t('export.exportAll') }
          this.allProcessTreeData.push(headData)
          this.processTreeData.forEach(item => { this.allProcessTreeData.push(item) })
          this.formData = this.categoryList.map(category => {
            return {
              name: category.value,
              label: category.label,
              treeData: this.treeData.filter(item => item.type === category.value),
              isSel: false,
              defaultCheckedKeys: ['0'],
              checkDisabled: false,
              selectDisabled: false
            }
          })
        })
      })
    },
    handleNodeClickFunc(data, node, el) {
      this.searchFilter = false
      this.tableLoading = true
      this.currentNode = data
      getList(data.dataId, { category: data.type }).then(res => {
        this.rowData = res.data
        this.tableLoading = false
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    // nodeExpand(node) {
    //   this.expandedNodeList.push(node)
    //   this.defaultExpandedKeys = [
    //     ...new Set(this.expandedNodeList.map(item => item.id))
    //   ]
    // },
    // nodeCollapse(node) {
    //   this.expandedNodeList = this.expandedNodeList.filter(item => {
    //     return item.id !== node.id
    //   })
    //   this.defaultExpandedKeys = [
    //     ...new Set(this.expandedNodeList.map(item => item.id))
    //   ]
    // },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    resetGroup() {
      this.groupForm.groupName = ''
      this.formData.forEach(item => {
        item.isSel = false
        item.defaultCheckedKeys = ['0']
        item.checkDisabled = false
        item.selectDisabled = false
      })
      this.checkAll = false
      this.isIndeterminate = true
    },
    resetProcess() {
      this.formData.forEach(item => {
        item.isSel = false
        item.defaultCheckedKeys = ['0']
        item.checkDisabled = false
        item.selectDisabled = false
      })
      this.checkAll = false
      this.isIndeterminate = true
      this.processForm = {
        verifycode: false,
        flowName: ''
      }
      this.settingForm = {
        decryptLevelLimit: [],
        penetrateSuffix: [],
        fileExtAllow: '',
        fileExtForbit: '',
        intelligenceProperties: {
          timeType: '',
          count: undefined,
          size: undefined
        },
        applyLimits: {
          timeType: '',
          fileCount: undefined,
          fileSize: undefined
        },
        cancelWatermarkLimitsEx: {
          effectiveOnce: '0',
          cancelWatermarkAllowMdy: '',
          cancelWatermarkMinutes: '',
          cancelWatermarkBeginTime: '',
          cancelWatermarkEndTime: '',
          allowNotLimit: ''
        },
        cancelWatermarkLimits: {
          cancelWatermarkAllowMdy: '',
          cancelWatermarkMinutes: '',
          cancelWatermarkBeginTime: '',
          cancelWatermarkEndTime: '',
          allowNotLimit: ''
        },
        forbiddenApplySensitive: '0',
        autoDecFile: '0'
      }
      this.approverList = []
      this.initiatorList = []
      this.activeName = 'first'
      this.intelligence = false
      this.applyLimit = false
    },
    formatData(type) {
      const treeArr = this.$refs.tree
      let tempData = Object.assign([], this.formData)
      treeArr.forEach(item => {
        const node = item.getCurrentNode()
        if (node) {
          const index = tempData.findIndex(item => node.type === item.name)
          tempData[index].parentId = node.dataId
          // 分组父节点名称 用于记录管理员日志
          tempData[index].parentGroupName = node.label
        }
      })
      tempData = type === 1
        ? tempData
          .filter(item => item.isSel)
          .map(item => {
            return {
              groupName: this.groupForm.groupName,
              parentId: item.parentId,
              parentGroupName: item.parentGroupName,
              category: item.name
            }
          })
        : tempData
          .filter(item => item.isSel)
          .map(item => {
            return {
              tenantId: 'ld',
              verifycode: this.processForm.verifycode,
              flowName: this.processForm.flowName,
              groupId: item.parentId,
              parentGroupName: item.parentGroupName,
              categoryId: item.name
            }
          })
      return tempData
    },
    handleCreateGroup() {
      this.resetGroup()
      this.dialogStatus = 'createGroup'
      this.dialogGroupVisible = true
      const obj = this.formData.find(
        item => item.name === this.currentNode.type
      )
      if (obj) {
        obj.isSel = true
        obj.defaultCheckedKeys = [this.currentNode.dataId]
      }
      this.$nextTick(() => {
        this.$refs['groupForm'].clearValidate()
      })
    },
    handleUpdateGroup(data) {
      this.resetGroup()
      this.dialogStatus = 'updateGroup'
      this.dialogGroupVisible = true
      this.groupForm.groupName = data.label
      this.groupForm.id = data.dataId
      this.groupForm.type = data.type
      const obj = this.formData.find(item => item.name === data.type)
      this.$nextTick(() => {
        this.$refs['groupForm'].clearValidate()
        this.formData.forEach(item => {
          item.checkDisabled = true
          item.selectDisabled = item.name !== data.type && true
        })
        obj.isSel = true
        obj.disabled = true
        obj.defaultCheckedKeys = [data.oriData.parentId]
      })
    },
    createGroup() {
      const tempData = this.formatData(1)
      this.$refs['groupForm'].validate(valid => {
        if (valid) {
          if (tempData.length > 0) {
            this.submitting = true
            addGroup(tempData).then(res => {
              this.dialogGroupVisible = false
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.insertSuccess'),
                type: 'success',
                duration: 2000
              })
              this.getProcessTree()
              this.handleNodeClickFunc(this.currentNode)
              this.submitting = false
            }).catch(err => {
              console.log(err)
              this.submitting = false
            })
          } else {
            this.$message({
              message: this.$t('pages.approvalProcess_Msg37'),
              type: 'error'
            })
          }
        }
      })
    },
    updateGroup() {
      const tempData = this.formatData(1)
      this.$refs['groupForm'].validate(valid => {
        if (valid) {
          this.submitting = true
          updateGroup(tempData[0], this.groupForm.id).then(res => {
            this.dialogGroupVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.getProcessTree()
            this.handleNodeClickFunc(this.currentNode)
            this.submitting = false
          }).catch(err => {
            console.log(err)
            this.submitting = false
          })
        }
      })
    },
    handleDelGroup(data) {
      this.$confirmBox(this.$t('pages.approvalProcess_Msg30', { info: data.label }))
        .then(() => {
          delGroup({ groupName: data.label, category: data.type }, data.dataId).then(respond => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
            this.getProcessTree()
            this.handleNodeClickFunc(this.currentNode)
          })
        })
        .catch(() => {})
    },
    handleCreateProcess() {
      this.saveAsflowName = ''
      this.resetProcess()
      this.resetWatermark()
      this.resetWatermarkEx()
      this.dialogProcessVisible = true
      this.dialogStatus = 'createProcess'
      const obj = this.formData.find(
        item => item.name === this.currentNode.type
      )
      if (obj) {
        obj.isSel = true
        obj.defaultCheckedKeys = [this.currentNode.dataId]
      }
      getDenseSet().then(res => {
        this.visibleCount = res.data.visibleCount
      })
      this.$nextTick(() => {
        this.$refs['processForm'].clearValidate()
      })
    },
    handleUpdateProcess(rowData) {
      this.resetProcess()
      this.processForm.flowName = rowData.label
      this.processForm.id = rowData.dataId
      getProcessById(rowData.dataId).then(res => {
        this.dialogProcessVisible = true
        this.dialogStatus = 'updateProcess'
        const data = res.data
        this.processForm.verifycode = data.verifycode
        this.initiatorList = data.assignedUserOrDeptVOs
        this.approverList = data.steps
        // 这边调用下resetWatermark,避免当高级设置未设置时没有重置这些值
        this.resetWatermark()
        this.resetWatermarkEx()
        if (data.extralPropertiesDTO) {
          const extralProperties = data.extralPropertiesDTO
          this.intelligence = data.intelligence
          this.applyLimit = data.applyLimit
          Object.assign(this.settingForm, extralProperties)
          this.settingForm.decryptLevelLimit = extralProperties.decryptLevelLimit ? extralProperties.decryptLevelLimit.split(',') : []
          this.settingForm.penetrateSuffix = extralProperties.penetrateSuffix ? extralProperties.penetrateSuffix.split('|') : []
          const inteProp = this.settingForm.intelligenceProperties
          inteProp.size = inteProp.size ? inteProp.size : undefined
          inteProp.count = inteProp.count ? inteProp.count : undefined
          const inteLimitProp = this.settingForm.applyLimits
          inteLimitProp.fileSize = inteLimitProp.fileSize ? inteLimitProp.fileSize : undefined
          inteLimitProp.fileCount = inteLimitProp.fileCount ? inteLimitProp.fileCount : undefined
          if (this.settingForm.cancelWatermarkLimits) {
            this.resetWatermark(this.settingForm.cancelWatermarkLimits)
          }
          if (this.settingForm.cancelWatermarkLimitsEx) {
            this.resetWatermarkEx(this.settingForm.cancelWatermarkLimitsEx)
          }
        }
        getDenseSet().then(res => {
          this.visibleCount = res.data.visibleCount
          this.formData.forEach(item => {
            item.checkDisabled = true
            item.selectDisabled = item.name !== rowData.type
          })
          const obj = this.formData.find(item => item.name === rowData.type)
          obj.isSel = true
          obj.disabled = true
          obj.defaultCheckedKeys = [rowData.oriData.groupId]
        })
      })
      this.$nextTick(() => {
        this.$refs['processForm'] && this.$refs['processForm'].clearValidate()
      })
    },
    handleDelProcess(data) {
      beforeDelProcess(data.id).then(
        res => {
          if (res.data.suspendRunning) {
            this.$confirmBox(this.$t('pages.approvalProcess_Msg32'), this.$t('text.prompt')).then(() => {
              delProcess({ flowName: data.label, categoryId: data.type, parentGroupName: data.oriData.groupName }, data.dataId).then(respond => {
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.deleteSuccess'),
                  type: 'success',
                  duration: 2000
                })
                this.getProcessTree()
                if (this.searchFilter === true) {
                  this.handleFilter()
                } else {
                  this.handleNodeClickFunc(this.currentNode)
                }
              })
            })
              .catch(() => {})
          } else {
            this.$confirmBox(this.$t('pages.approvalProcess_Msg33'), this.$t('text.prompt')).then(() => {
              delProcess({ flowName: data.label, categoryId: data.type, parentGroupName: data.oriData.groupName }, data.dataId).then(respond => {
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.deleteSuccess'),
                  type: 'success',
                  duration: 2000
                })
                this.getProcessTree()
                if (this.searchFilter === true) {
                  this.handleFilter()
                } else {
                  this.handleNodeClickFunc(this.currentNode)
                }
              })
            })
              .catch(() => {})
          }
        })
    },
    delFunc(selectedData, ids, index) {
      if (index === selectedData.length) {
        if (ids.length > 0) {
          // flowNames,categoryId, groupName用于记录管理员日志
          const flowNames = selectedData.map(
            item => item.label
          )
          const categoryId = selectedData[0].type
          const groupName = selectedData[0].oriData.groupName
          batchDelProcess({ flowIds: ids, flowNames, categoryId, groupName }).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.batchDeleteSuccess'),
              type: 'success',
              duration: 2000
            })
            this.cancelOperation = false
            this.sameOperation = true
            this.getProcessTree()
            if (this.searchFilter === true) {
              this.handleFilter()
            } else {
              this.handleNodeClickFunc(this.currentNode)
            }
          }).catch(() => {
            this.cancelOperation = false
            this.sameOperation = true
          })
        } else {
          this.cancelOperation = false
          this.sameOperation = true
        }
      } else {
        this.handleBatchDelProcess(selectedData, ids, index)
      }
    },
    handleBatchDelProcess(selectedData, ids, index) {
      const item = selectedData[index]
      beforeDelProcess(item.dataId).then(res => {
        if (res.data.suspendRunning) {
          if (!this.cancelOperation) {
            const h = this.$createElement
            const that = this
            this.$msgbox({
              title: this.$t('pages.approvalProcess_Msg38'),
              type: 'warning',
              customClass: 'confirm-box',
              message: h('div', null, [
                h('p', null, this.$t('pages.approvalProcess_Msg39', { info: item.label })),
                h('ElCheckbox', {
                  props: { checked: that.sameOperation },
                  on: { change(val) {
                    that.sameOperation = val
                  } }
                }, this.$t('pages.approvalProcess_Msg41'))
              ]),
              showCancelButton: true,
              cancelButtonText: this.$t('pages.cancel'),
              confirmButtonText: this.$t('pages.processStgLib_Msg50')

            }).then(() => {
              if (this.sameOperation) {
                // flowNames, categoryId, groupName用于记录管理员日志
                const flowNames = selectedData.map(
                  item => item.label
                )
                const categoryId = selectedData[0].type
                const groupName = selectedData[0].oriData.groupName
                batchDelProcess({ flowIds: ids, flowNames, categoryId, groupName }).then(res => {
                  this.$notify({
                    title: this.$t('text.success'),
                    message: this.$t('text.batchDeleteSuccess'),
                    type: 'success',
                    duration: 2000
                  })
                  this.getProcessTree()
                  if (this.searchFilter === true) {
                    this.handleFilter()
                  } else {
                    this.handleNodeClickFunc(this.currentNode)
                  }
                })
              } else {
                index++
                this.delFunc(selectedData, ids, index)
              }
            }).catch(() => {
              this.cancelOperation = !!this.sameOperation
              index++
              const idIndex = ids.findIndex(id => id === item.dataId)
              idIndex >= 0 && ids.splice(idIndex, 1)
              this.delFunc(selectedData, ids, index)
            })
          } else {
            index++
            const idIndex = ids.findIndex(id => id === item.dataId)
            idIndex >= 0 && ids.splice(idIndex, 1)
            this.delFunc(selectedData, ids, index)
          }
        } else {
          index++
          this.delFunc(selectedData, ids, index)
        }
      })
    },
    batchDelProcess() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const selectedData = this.$refs['tableList'].selectedData
        const ids = selectedData.map(
          item => item.dataId
        )
        this.handleBatchDelProcess(selectedData, ids, 0)
      }).catch(() => {})
    },
    getSteps() {
      const g6Data = this.$refs['chart'].g6Data.nodes
      const rectData = g6Data.filter(item => {
        const idArr = item.id.split('_')
        return idArr[0] === 'modelRect'
      })
      const launchData = rectData.find(item => item.id === 'modelRect_launch_1')
      const assignByUser = launchData ? launchData.choosedData
        .filter(item => item.type == '2')
        .map(item => item.dataId)
        .join() : ''
      const assignByDept = launchData ? launchData.choosedData
        .filter(item => item.type == '4')
        .map(item => item.dataId)
        .join() : ''
      const assignByGroup = launchData ? launchData.choosedData
        .filter(item => item.type == 'R')
        .map(item => item.dataId)
        .join() : ''
      const steps = rectData.filter(item => item.id !== 'modelRect_launch_1')
        .map((item, index) => {
          const auditType = item.auditType
          const emptyAuditorToUser = item.emptyAuditorToUserSelected == 'true' ? item.emptyAuditorToUser : ''
          if (auditType == 1) {
            return {
              index: index + 1, // 步骤排序 1.2.3
              stepName: `[${index + 1}]${this.$t('pages.shortLevelApproval')}`, // 步骤名称
              auditType: auditType, // 步骤审批类型 1.部门主管审批 2.审批岗位审批 3.指定人审批 4.部门分管领导 5.直属领导
              directorLevel: `${item.mainDirectorLevel}|${item.replyType}`, // 部门主管级别
              auditGroups: '', // 审批岗位（多个用逗号隔开）
              auditUsers: '', // 审批人（多个用逗号隔开）
              auditLeader: '',
              noDirectorToParent: item.noDirectorToParent,
              emptyAuditorToUser: emptyAuditorToUser
            }
          } else if (auditType == 2) {
            return {
              index: index + 1, // 步骤排序 1.2.3
              stepName: `[${index + 1}]${this.$t('pages.shortPositionApproval')}`, // 步骤名称
              auditType: auditType, // 步骤审批类型 1.部门主管审批 2.审批岗位审批 3.指定人审批 4.部门分管领导 5.直属领导
              directorLevel: '', // 部门主管级别
              auditGroups: item.choosedData
                ? item.choosedData
                  .map(item => {
                    return `${item.dataId}|${item.replyType}`
                  })
                  .join()
                : '', // 审批岗位（多个用逗号隔开）
              auditUsers: '', // 审批人（多个用逗号隔开）
              auditLeader: '',
              noDirectorToParent: '',
              emptyAuditorToUser: emptyAuditorToUser
            }
          } else if (auditType == 3) {
            return {
              index: index + 1, // 步骤排序 1.2.3
              stepName: `[${index + 1}]${this.$t('pages.approvalProcess_Msg42')}`, // 步骤名称
              auditType: auditType, // 步骤审批类型 1.部门主管审批 2.审批岗位审批 3.指定人审批 4.部门分管领导 5.直属领导
              directorLevel: '', // 部门主管级别
              auditGroups: '', // 审批岗位（多个用逗号隔开）
              auditLeader: '',
              auditUsers: item.choosedData
                ? item.choosedData
                  .map(item => {
                    return `${item.dataId}|${item.replyType}`
                  })
                  .join()
                : '',  // 审批人（多个用逗号隔开）
              noDirectorToParent: '',
              emptyAuditorToUser: '',
              multiple: item.multiple
            }
          } else if (auditType == 4) {
            return {
              index: index + 1, // 步骤排序 1.2.3
              stepName: `[${index + 1}]${this.$t('pages.shortBranchApproval')}`, // 步骤名称
              auditType: auditType, // 步骤审批类型 1.部门主管审批 2.审批岗位审批 3.指定人审批 4.部门分管领导 5.直属领导
              directorLevel: '', // 部门主管级别
              auditGroups: '', // 审批岗位（多个用逗号隔开）
              auditUsers: '',  // 审批人（多个用逗号隔开）
              auditLeader: `deptBranchLeader|${item.replyType}`,
              noDirectorToParent: '',
              emptyAuditorToUser: emptyAuditorToUser
            }
          } else if (auditType == 5) {
            return {
              index: index + 1, // 步骤排序 1.2.3
              stepName: `[${index + 1}]${this.$t('pages.shortDirectApproval')}`, // 步骤名称
              auditType: auditType, // 步骤审批类型 1.部门主管审批 2.审批岗位审批 3.指定人审批 4.部门分管领导 5.直属领导
              directorLevel: '', // 部门主管级别
              auditGroups: '', // 审批岗位（多个用逗号隔开）
              auditUsers: '',  // 审批人（多个用逗号隔开）
              auditLeader: `userDirectLeader|${item.replyType}`,
              noDirectorToParent: '',
              emptyAuditorToUser: emptyAuditorToUser
            }
          } else {
            return {
              index: index + 1, // 步骤排序 1.2.3
              stepName: `[${index + 1}]${this.$t('pages.approvalProcess_Msg42')}`, // 步骤名称
              auditType: '3', // 步骤审批类型 1.部门主管审批 2.审批岗位审批 3.指定人审批 4.部门分管领导 5.直属领导
              directorLevel: '', // 部门主管级别
              auditGroups: '', // 审批岗位（多个用逗号隔开）
              auditUsers: item.choosedData
                ? item.choosedData
                  .map(item => {
                    return `${item.dataId}|${item.replyType}`
                  })
                  .join()
                : '' // 审批人（多个用逗号隔开）
            }
          }
        })
      return {
        assignByUser,
        assignByDept,
        assignByGroup,
        steps
      }
    },
    saveAsProcess() {
      if (this.saveAsflowName === '' || this.saveAsflowName == null) {
        this.$message({
          message: this.$t('pages.flowNameNotNull'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.createProcess()
    },
    createProcess() {
      const tempData = this.formatData(2)
      tempData.forEach(item => {
        if (this.saveAsflowName) {
          item.flowName = this.saveAsflowName
        }
        item.assignByUser = this.getSteps().assignByUser
        item.assignByDept = this.getSteps().assignByDept
        item.assignByGroup = this.getSteps().assignByGroup
        item.steps = this.getSteps().steps
        item.extralPropertiesDTO = JSON.parse(JSON.stringify(this.settingForm))
        item.extralPropertiesDTO.decryptLevelLimit = this.settingForm.decryptLevelLimit.join()
        item.extralPropertiesDTO.penetrateSuffix = this.settingForm.penetrateSuffix.join('|')
        const temp = item.extralPropertiesDTO.intelligenceProperties
        temp.size = temp.size ? temp.size.toString() : ''
        temp.count = temp.count ? temp.count.toString() : ''
        const limitTemp = item.extralPropertiesDTO.applyLimits
        limitTemp.fileSize = limitTemp.fileSize ? limitTemp.fileSize.toString() : ''
        limitTemp.fileCount = limitTemp.fileCount ? limitTemp.fileCount.toString() : ''
        this.formatterWatermark(item.extralPropertiesDTO)
        this.formatterWatermarkEx(item.extralPropertiesDTO)
      })
      this.$refs['processForm'].validate(valid => {
        if (valid && !this.showTip && !this.showLimitTip && !this.timeRangeTip && !this.timeRangeTipEx && !this.timeTip) {
          if (tempData.length > 0) {
            if (tempData[0].assignByUser || tempData[0].assignByDept || tempData[0].assignByGroup) {
              let hasAuditUser = []
              let hasAuditGroup = []
              // 校验步骤
              hasAuditUser = tempData[0].steps.filter(
                item => item.auditType == 3 && item.auditUsers === ''
              )
              hasAuditGroup = tempData[0].steps.filter(
                item => item.auditType == 2 && item.auditGroups === ''
              )
              if (hasAuditUser.length === 0 && hasAuditGroup.length === 0) {
                this.submitting = true
                addProcess(tempData).then(res => {
                  this.$notify({
                    title: this.$t('text.success'),
                    message: this.$t('text.insertSuccess'),
                    type: 'success',
                    duration: 2000
                  })
                  this.dialogProcessVisible = false
                  this.saveAsVisible = false
                  this.submitting = false
                  this.getProcessTree()
                  this.handleNodeClickFunc(this.currentNode)
                }).catch(err => {
                  console.log(err)
                  this.submitting = false
                })
              } else {
                this.$message({
                  message: this.$t('pages.approvalProcess_Msg43'),
                  type: 'error'
                })
              }
            } else {
              this.$message({
                message: this.$t('pages.approvalProcess_Msg44'),
                type: 'error'
              })
            }
          } else {
            this.$message({
              message: this.$t('pages.approvalProcess_Msg37'),
              type: 'error'
            })
          }
        }
      })
    },
    updateProcess() {
      const tempData = this.formatData(2)
      tempData.forEach(item => {
        item.assignByUser = this.getSteps().assignByUser
        item.assignByDept = this.getSteps().assignByDept
        item.assignByGroup = this.getSteps().assignByGroup
        item.steps = this.getSteps().steps
        item.extralPropertiesDTO = JSON.parse(JSON.stringify(this.settingForm))
        item.extralPropertiesDTO.decryptLevelLimit = this.settingForm.decryptLevelLimit.join()
        item.extralPropertiesDTO.penetrateSuffix = this.settingForm.penetrateSuffix.join('|')
        const temp = item.extralPropertiesDTO.intelligenceProperties
        temp.size = temp.size ? temp.size.toString() : ''
        temp.count = temp.count ? temp.count.toString() : ''
        const limitTemp = item.extralPropertiesDTO.applyLimits
        limitTemp.fileSize = limitTemp.fileSize ? limitTemp.fileSize.toString() : ''
        limitTemp.fileCount = limitTemp.fileCount ? limitTemp.fileCount.toString() : ''
        this.formatterWatermark(item.extralPropertiesDTO)
        this.formatterWatermarkEx(item.extralPropertiesDTO)
      })
      this.$refs['processForm'].validate(valid => {
        if (valid && !this.showTip && !this.showLimitTip && !this.timeRangeTip && !this.timeRangeTipEx && !this.timeTip) {
          if (tempData.length > 0) {
            let hasAuditUser = []
            let hasAuditGroup = []
            // 校验步骤
            hasAuditUser = tempData[0].steps.filter(
              item => item.auditType == 3 && item.auditUsers === ''
            )
            hasAuditGroup = tempData[0].steps.filter(
              item => item.auditType == 2 && item.auditGroups === ''
            )
            if (hasAuditUser.length === 0 && hasAuditGroup.length === 0) {
              beforeUpdateProcess(tempData[0], this.processForm.id).then(
                res => {
                  if (res.data.suspendRunning) {
                    this.$confirmBox(this.$t('pages.approvalProcess_Msg34'), this.$t('text.prompt')).then(() => {
                      this.submitting = true
                      updateProcess(tempData[0], this.processForm.id).then(
                        res => {
                          this.dialogProcessVisible = false
                          this.$notify({
                            title: this.$t('text.success'),
                            message: this.$t('text.updateSuccess'),
                            type: 'success',
                            duration: 2000
                          })
                          this.getProcessTree()
                          if (this.searchFilter === true) {
                            this.handleFilter()
                          } else {
                            this.handleNodeClickFunc(this.currentNode)
                          }
                          this.submitting = false
                        }
                      ).catch(err => {
                        console.log(err)
                        this.submitting = false
                      })
                    })
                      .catch(() => {})
                  } else {
                    this.submitting = true
                    updateProcess(tempData[0], this.processForm.id).then(
                      res => {
                        this.dialogProcessVisible = false
                        this.$notify({
                          title: this.$t('text.success'),
                          message: this.$t('text.updateSuccess'),
                          type: 'success',
                          duration: 2000
                        })
                        this.getProcessTree()
                        if (this.searchFilter === true) {
                          this.handleFilter()
                        } else {
                          this.handleNodeClickFunc(this.currentNode)
                        }
                        this.submitting = false
                      }
                    ).catch(err => {
                      console.log(err)
                      this.submitting = false
                    })
                  }
                }
              )
            } else {
              this.$message({
                message: this.$t('pages.approvalProcess_Msg43'),
                type: 'error'
              })
            }
          } else {
            this.$message({
              message: this.$t('pages.approvalProcess_Msg37'),
              type: 'error'
            })
          }
        }
      })
    },
    resetWatermark(data) {
      if (data) {
        this.watermarkLimits = true
        const minutes = data.cancelWatermarkMinutes
        const begin = data.cancelWatermarkBeginTime
        const end = data.cancelWatermarkEndTime
        const date = parseTime(new Date(), 'y-m-d h:i:s')
        this.watermarkDateRange = begin ? [begin, end] : [date, new Date(moment().endOf('day'))]
        this.watermarkTimeOpt = minutes ? (minutes === '16777215' ? 1 : 2) : 3
        this.watermarkMinutes = (minutes == '16777215' || !minutes) ? '30' : minutes
        this.watermarkAllowMdy = data.cancelWatermarkAllowMdy
        // 兼容旧版本，以前没有这个字段，但以前是允许用户修改为不限制时间的
        this.allowNotLimit = ((data.allowNotLimit == null || data.allowNotLimit == undefined) && data.watermarkAllowMdy) ? 'true' : data.allowNotLimit
      } else {
        this.watermarkLimits = false
        this.watermarkTimeOpt = 2
        this.watermarkMinutes = '30'
        const date = parseTime(new Date(), 'y-m-d h:i:s')
        this.watermarkDateRange = [date, new Date(moment().endOf('day'))]
        this.watermarkAllowMdy = 'false'
        this.allowNotLimit = 'false'
      }
    },
    resetWatermarkEx(data) {
      if (data) {
        this.watermarkLimitsEx = true
        const minutes = data.cancelWatermarkMinutes
        const begin = data.cancelWatermarkBeginTime
        const end = data.cancelWatermarkEndTime
        // const date = parseTime(new Date(), 'y-m-d h:i:s')
        this.watermarkDateRangeEx = begin ? end : new Date(moment().endOf('day'))
        if (data.effectiveOnce == '1') {
          this.watermarkTimeOptEx = 4
        } else {
          this.watermarkTimeOptEx = minutes ? (minutes === '16777215' ? 1 : 2) : 3
        }
        this.watermarkAllowMdyEx = data.cancelWatermarkAllowMdy
        // 兼容旧版本，以前没有这个字段，但以前是允许用户修改为不限制时间的
        this.allowNotLimitEx = ((data.allowNotLimit == null || data.allowNotLimit == undefined) && data.watermarkAllowMdy) ? 'true' : data.allowNotLimit
        this.watermarkMinutesEx = (minutes == '16777215' || !minutes) ? '30' : minutes
      } else {
        this.watermarkLimitsEx = false
        this.watermarkTimeOptEx = 4
        this.watermarkMinutesEx = '30'
        // const date = parseTime(new Date(), 'y-m-d h:i:s')
        this.watermarkDateRangeEx = new Date(moment().endOf('day'))
        this.watermarkAllowMdyEx = 'false'
        this.allowNotLimitEx = 'false'
      }
    },
    formatterWatermark(data) {
      if (this.watermarkLimits) {
        const wl = data.cancelWatermarkLimits
        wl.cancelWatermarkAllowMdy = this.watermarkAllowMdy
        wl.allowNotLimit = this.allowNotLimit
        switch (this.watermarkTimeOpt) {
          case 1:
            wl.cancelWatermarkMinutes = '16777215'
            wl.cancelWatermarkBeginTime = ''
            wl.cancelWatermarkEndTime = ''
            break
          case 2:
            wl.cancelWatermarkMinutes = this.watermarkMinutes
            wl.cancelWatermarkBeginTime = ''
            wl.cancelWatermarkEndTime = ''
            break
          case 3:
            wl.cancelWatermarkMinutes = ''
            wl.cancelWatermarkBeginTime = parseTime(this.watermarkDateRange[0], 'y-m-d h:i:s')
            wl.cancelWatermarkEndTime = parseTime(this.watermarkDateRange[1], 'y-m-d h:i:s')
            break
        }
      } else {
        delete data.cancelWatermarkLimits
      }
    },
    formatterWatermarkEx(data) {
      if (this.watermarkLimitsEx) {
        const wl = data.cancelWatermarkLimitsEx
        wl.cancelWatermarkAllowMdy = this.watermarkAllowMdyEx
        wl.allowNotLimit = this.allowNotLimitEx
        switch (this.watermarkTimeOptEx) {
          case 1:
            wl.cancelWatermarkMinutes = '16777215'
            wl.cancelWatermarkBeginTime = ''
            wl.cancelWatermarkEndTime = ''
            wl.effectiveOnce = '0'
            break
          case 2:
            wl.cancelWatermarkMinutes = this.watermarkMinutesEx
            wl.cancelWatermarkBeginTime = ''
            wl.cancelWatermarkEndTime = ''
            wl.effectiveOnce = '0'
            break
          case 3:
            wl.cancelWatermarkMinutes = ''
            wl.cancelWatermarkBeginTime = parseTime(new Date(moment().startOf('day')), 'y-m-d h:i:s')
            wl.cancelWatermarkEndTime = parseTime(this.watermarkDateRangeEx, 'y-m-d h:i:s')
            wl.effectiveOnce = '0'
            break
          case 4:
            wl.cancelWatermarkMinutes = ''
            wl.cancelWatermarkBeginTime = ''
            wl.cancelWatermarkEndTime = ''
            wl.effectiveOnce = '1'
            break
        }
      } else {
        delete data.cancelWatermarkLimitsEx
      }
    },
    handleBatchUpdate() {
      this.batchVisible = true
      if (this.searchFilter == true) {
        this.belongTreeData = this.treeData.filter(
          item => item.type === this.query.category
        )
        this.$nextTick(() => {
          this.checkedKeys = [this.query.category + '0']
        })
      } else {
        this.belongTreeData = this.treeData.filter(
          item => item.type === this.currentNode.type
        )
        this.$nextTick(() => {
          this.checkedKeys = [this.currentNode.id]
        })
      }
    },
    batchUpdate() {
      const ids = this.$refs['tableList'].selectedData.map(item => item.dataId)
      const flowNames = this.$refs['tableList'].selectedData.map(item => item.label)
      const node = this.$refs.belongTree.getCurrentNode()
      this.submitting = true
      batchUpdateGroup({
        flowIds: ids,
        categoryId: node.type,
        groupId: Number(node.dataId),
        // 用于记录管理员日志
        groupName: String(node.label),
        // 用于记录管理员日志
        flowNames: flowNames
      }).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.batchVisible = false
        this.submitting = false
        this.getProcessTree()
        if (this.searchFilter === true) {
          this.handleFilter()
        } else {
          this.handleNodeClickFunc(this.currentNode)
        }
      }).catch(err => {
        console.log(err)
        this.submitting = false
      })
    },
    renderDeviceContentFunc(h, { node, data, store }) {
      const iconClass = (
        <svg-icon icon-class={this.iconOption[data.dataCode]} />
      )
      return (
        <span>
          {iconClass} {node.label}
        </span>
      )
    },
    handleInitiator(row) {
      this.currentFlowId = row.dataId
      getProcessById(row.dataId).then(res => {
        this.dialogShow = true
        const data = res.data
        this.nodeModel = {
          id: 'modelRect_launch_1',
          choosedData: data.assignedUserOrDeptVOs,
          // flowName, flowGroupName用于记录管理员日志
          flowName: row.label,
          flowGroupName: row.oriData.groupName
        }
      })
    },
    getApprover(value, flowName, flowGroupName) {
      const deptId = value
        .filter(item => item.type == '4')
        .map(item => item.dataId)
        .join()
      const userId = value
        .filter(item => item.type == '2')
        .map(item => item.dataId)
        .join()
      const groupId = value
        .filter(item => item.type == 'R')
        .map(item => item.dataId)
        .join()
      updateInitiator({ deptId, userId, groupId, flowName, flowGroupName }, this.currentFlowId).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.dialogShow = false
        if (this.searchFilter === true) {
          this.handleFilter()
        } else {
          this.handleNodeClickFunc(this.currentNode)
        }
      })
    },
    handleIntelligence(value) {
      if (value) {
        this.settingForm.intelligenceProperties.timeType = 'day'
      } else {
        this.settingForm.intelligenceProperties.size = undefined
        this.settingForm.intelligenceProperties.count = undefined
      }
    },
    handleApplyLimit(value) {
      if (value) {
        this.settingForm.applyLimits.timeType = 'day'
      } else {
        this.settingForm.applyLimits.fileSize = undefined
        this.settingForm.applyLimits.fileCount = undefined
      }
    },
    showLimit(type) {
      let arr = []
      const checkedKeys = this.formData.filter(item => item.isSel).map(item => item.name)
      if (type === 'secret') {
        arr = ['fileDecrypt']
      } else if (type === 'secretAndSensitiveOutsend') {
        arr = ['fileDecrypt', 'sensitiveFileOutSend']
      } else if (type === 'suffix') {
        arr = ['fileDecrypt', 'outSend', 'changeFileLevel']
      } else if (type === 'intelligence') {
        if (this.processForm.verifycode) {
          return false
        }
        arr = ['fileDecrypt', 'printOutsend', 'outSend', 'changeFileLevel', 'sensitiveFileOutSend']
      } else if (type === 'watermarkEx') { // 取消文档水印
        arr = ['cancelWatermark']
      } else if (type === 'watermark') { // 打印审批，行为管控审批
        arr = ['filePrint', 'behaviorControl']
      } else if (type === 'sensitiveFile') {
        arr = ['sensitiveFileOutSend']
      }
      const intersection = checkedKeys.filter(item => { return arr.indexOf(item) !== -1 })
      return intersection.length > 0
    },
    tabClick(pane, event) {
      if (this.showTip) {
        this.intelligence = false
      }
      if (this.showLimitTip) {
        this.applyLimit = false
      }
    },
    clickSelect() {
      const popoverLists = document.getElementsByClassName('el-popover')
      Array.from(popoverLists).forEach(item => { item.style.display = 'none' })
    },
    handleFileSuffixImport(type) {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
      this.importFileSuffixType = type
    },
    duplicateRemoval(nv, ov) {
      let union_suffix = null
      if (ov == null || ov === '') {
        union_suffix = [...new Set(nv.split('|'))].join('|')
      } else {
        union_suffix = [...new Set((ov + '|' + nv).split('|'))].join('|')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      return union_suffix
    },
    importFileSuffix(suffix) {
      if (this.importFileSuffixType === 'fileExtAllow') {
        this.settingForm.fileExtAllow = this.duplicateRemoval(suffix, this.settingForm.fileExtAllow)
      } else if (this.importFileSuffixType === 'fileExtForbit') {
        this.settingForm.fileExtForbit = this.duplicateRemoval(suffix, this.settingForm.fileExtForbit)
      }
    },
    allowChange(data) {
      if (data == 'false' && this.watermarkTimeOpt == '1') {
        this.watermarkTimeOpt = 2
      }
    },
    allowMdyChange(data) {
      if (data == 'false') {
        this.allowNotLimit = 'false'
        if (this.watermarkTimeOpt == '1') {
          this.watermarkTimeOpt = 2
        }
      }
      if (data == 'true' && this.allowNotLimit == 'false') {
        if (this.watermarkTimeOpt == '1') {
          this.watermarkTimeOpt = 2
        }
      }
    },
    allowChangeEx(data) {
      if (data == 'false' && this.watermarkTimeOptEx == '1') {
        this.watermarkTimeOptEx = 2
      }
    },
    allowMdyChangeEx(data) {
      if (data == 'false') {
        this.allowNotLimitEx = 'false'
        if (this.watermarkTimeOptEx == '1') {
          this.watermarkTimeOptEx = 2
        }
      }
      if (data == 'true' && this.allowNotLimitEx == 'false') {
        if (this.watermarkTimeOptEx == '1') {
          this.watermarkTimeOptEx = 2
        }
      }
    },
    handleExport() {
      this.selectNode = this.allProcessTreeData[0]
      this.selectTreeId = '-1'
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    // exportFunc(formData, opts) {
    //   // const { sortName, sortOrder } = this.searchQuery
    //   if (formData.type == 3) {
    //     let ids = this.gridTable.rowData.filter(item => item.dataCode == 'L').map(item => item.id).join(',')
    //     if (!ids) {
    //       ids = '-1'
    //     }
    //     return exportExcel({ ids: ids }, opts)
    //   } else {
    //     return exportExcel({
    //       ids: formData.type === 1 ? formData.dataIds.join(',') : null,
    //       category: (formData.type === 2 && this.selectNode.dataId != -1) ? this.selectNode.type : null,
    //       groupId: (formData.type === 2) ? this.selectNode.dataId : null,
    //       groupName: (formData.type === 2) ? this.selectNode.label : null
    //     }, opts)
    //   }
    // },
    exportFunc(formData, opts) {
      // const { sortName, sortOrder } = this.searchQuery
      if (formData.type == 3) {
        let ids = this.gridTable.rowData.filter(item => item.dataCode == 'L').map(item => item.id).join(',')
        if (!ids) {
          ids = '-1'
        }
        return exportExcel({ ids: ids }, opts)
      } else {
        return exportExcel({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          category: (formData.type === 2 && this.selectNode.dataId != -1) ? this.selectNode.type : null,
          groupId: (formData.type === 2) ? this.selectNode.dataId : null,
          groupName: (formData.type === 2) ? this.selectNode.label : null
        }, opts)
      }
    },
    childSelectNodeData(data) {
      if (Array.isArray(data) && data.length > 0) {
        this.selectNode = data[0]
      } else {
        this.selectNode = data
      }
    },
    upload(data) {
      return request.post('/approvalFlow/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    importEndFunc(groupId) {
      this.getProcessTree()
    },
    handleCheckAllChange(val) {
      this.formData.forEach(item => {
        item.isSel = val
      })
      this.isIndeterminate = false;
    },
    handleCheckedItemsChange(value) {
      const checkedCount = this.formData.filter(item => item.isSel).length
      this.checkAll = checkedCount === this.formData.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.formData.length
    },
    saveAs() {
      this.$refs['processForm'].validate(valid => {
        if (valid && !this.showTip && !this.showLimitTip && !this.timeRangeTip && !this.timeRangeTipEx && !this.timeTip) {
          this.saveAsflowName = this.processForm.flowName
          this.saveAsVisible = true
        } 
      })
    },
    resetQuery() {
      this.query.category = this.categoryList[0].value
      this.query.nameLike = ''
      this.query.approverAccountLike = ''
      this.query.approverNameLike = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.process-tab {
  .el-tab-pane {
    padding: 10px;
  }
}
fieldset {
  border-width: 1px;
  border-color: #666;
}
.littleTip {
  color: #037bf3;
  font-size: 12px;
  display: inline-block;
  margin: 10px 0;
  line-height: 20px;
}
.tip {
  color: #f56c6c;
  font-size: 12px;
  display: inline-block;
  margin: 10px 0;
  line-height: 20px;
}
.intelligence{
  border: 1px solid #666;
  .form{
    padding: 6px 0 6px 6px;
  }
  p{
    margin: 0;
    padding: 6px 0 6px 6px;
    border-bottom: 1px solid #666;
    .text{
      label{
        color: #409EFF;
      }
    }
    .placeholder{
      color: #666;
    }
  }

}
>>>.el-radio__label{
    color: #666 !important;
  }
>>>.el-checkbox__label{
  color: #666 !important;
  }
</style>

<style lang="scss">
.confirm-box{
  .el-checkbox{
    position: absolute;
    bottom: -50px;
    left: 20px;
    color: #000 !important;

  }
  .el-checkbox__label{
    color: rgb(109, 94, 94);
  }
  .el-checkbox__inner{
    background-color: #fff;
  }
  .el-button--default{
    float: right;
    margin-left: 10px;
  }
}
.advanced-setting {
  .el-form label:not(.el-checkbox) {
    font-size: 12px;
    line-height: 20px;
    font-weight: normal;
    margin-top: 6px;
  }
  .el-form label.el-radio {
    line-height: 28px;
    margin-top: 0;
  }
  .el-checkbox__label {
    font-size: 12px;
  }
  .el-checkbox{
    margin-right: 18px;
  }
  .desc{
    font-weight: bold;
    font-size: 13px;
    display: inline-block;
    margin-bottom: 10px;
  }
}
.edit-process{
  .el-table .cell{
    width: 100%;
    float: right;
  }
}
.dateDisabled {
  .el-input__inner {
   background-color: #F5F7FA !important;
   color: #C0C4CC !important;
  }
}
</style>
