<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['user']" @data-change="strategyTargetNodeChange" />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :disabled="!currentNode||currentNode.type=='4'" @click="handleCreate">{{ $t('pages.newDelegation') }}</el-button>
        <el-button type="primary" size="mini" :disabled="!deleteable" @click="batchDel">
          {{ $t('pages.batchDelete') }}
        </el-button>
        <el-button type="primary" size="mini" :disabled="!currentNode" @click="handleDelegateLimit">{{ $t('pages.delegationLimit') }}</el-button>
        <label v-if="noDelegate" style="color:#fd2222;font-size:13px;;margin-left:80px">{{ $t('pages.delegateLimitTip') }}</label>
      </div>
      <div class="searchCon">
        <el-date-picker
          v-model="query.startTime"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :placeholder="$t('pages.effectTime')"
          style="width:200px"
          :picker-options="pickerOptionsStart"
        ></el-date-picker>
        —>
        <el-date-picker
          v-model="query.endTime"
          default-time="23:59:59"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :placeholder="$t('pages.failureTime')"
          style="width:200px"
          :picker-options="pickerOptionsEnd"
        ></el-date-picker>
        <el-select v-model="query.scope" :placeholder="$t('pages.range')" style="width: 160px">
          <el-option
            v-for="item in scopeType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select v-model="query.msgeffect" :placeholder="$t('pages.delegateStatus')" style="width: 160px">
          <el-option
            v-for="item in msgeffectType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select v-model="query.categoryId" clearable :placeholder="$t('pages.funType2')" style="width: 160px">
          <el-option
            v-for="item in categoryList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-input v-model="query.processName" v-trim clearable :placeholder="$t('pages.floatName')" style="width: 160px" @keyup.enter.native="handleFilter"></el-input>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
      <grid-table
        ref="tableList"
        v-loading="tableLoading"
        :col-model="colModel"
        :row-datas="rowData"
        :show-pager="false"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogDelegateVisible"
      :append-to-body="true"
      width="600px"
    >
      <Form ref="delegateForm" :rules="delegateRules" :model="delegateForm" label-position="right" label-width="80px" style="width: 520px;" >
        <FormItem :label="$t('pages.principal')">
          <el-input v-model="delegateForm.principal" disabled="" :maxlength="100"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.trustee')" prop="lastNameLabel">
          <el-tooltip :disabled="!delegateForm.lastNameLabel" :content="delegateForm.lastNameLabel" placement="top">
            <el-input v-model="delegateForm.lastNameLabel" disabled="" style="width:93%" :maxlength="100"></el-input>
          </el-tooltip>
          <el-button :disabled="dialogStatus==='update'" @click="dialogShow=true">...</el-button>
        </FormItem>
        <FormItem :label="$t('pages.effectTime')" prop="beginTime">
          <el-date-picker
            v-model="delegateForm.beginTime"
            :clearable="false"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:100%"
            :picker-options="pickerOptionsStart2"
          ></el-date-picker>
        </FormItem>
        <FormItem :label="$t('pages.failureTime')" prop="endTime">
          <el-date-picker
            v-model="delegateForm.endTime"
            :clearable="false"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:100%"
            :picker-options="pickerOptionsEnd2"
          ></el-date-picker>
        </FormItem>
        <FormItem :label="$t('pages.delegationProcess')" prop="processIdArr">
          <tree-menu
            v-if="dialogDelegateVisible"
            ref="processTree"
            :height="250"
            :width="$store.getters.language === 'en' ? 400 : 440"
            :data="processTreeData"
            :render-content="renderDeviceContentFunc"
            :is-filter="false"
            :multiple="true"
            :checked-keys="checkedKeys"
            :disabled-all-nodes="disabledAllNodes"
            :default-expanded-keys="defaultExpandedKeys"
            node-key="id"
            class="delegate-tree"
            @check-change="checkChange"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createDelegation():updateDelegation()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogDelegateVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <tree-table-transfer
      :dialog-visible="dialogShow"
      :node-model="nodeModel"
      :filter-node="currentNode"
      @sendApprover="getApprover"
      @close="dialogShow = false"
    >
    </tree-table-transfer>

    <tree-table-transfer
      :dialog-visible="delegateLimitShow"
      :node-model="delegateLimitModel"
      @sendApprover="getDelegateLimit"
      @close="delegateLimitShow = false"
    >
      <div slot="delegate" style="margin-left:32px">
        <div style="margin-bottom:10px">
          <label>{{ $t('pages.inheritStrategy') }}:</label>
          <el-checkbox v-model="delegateLimitModel.inherit" :disabled="inheritDisabled">{{ $t('pages.approvalProcess_Msg') }}</el-checkbox>
        </div>
        <div style="margin-bottom:10px">
          <label>{{ $t('pages.delegateRange') }}:</label>
          <el-radio-group v-model="delegateLimitModel.range" :disabled="delegateLimitModel.inherit">
            <el-radio label="0">{{ $t('pages.noLimit') }}</el-radio>
            <el-radio label="1">{{ $t('pages.forbidDelegate') }}</el-radio>
            <el-radio label="2">{{ $t('pages.modDelegateRange') }}</el-radio>
          </el-radio-group>
        </div>
      </div>
    </tree-table-transfer>
  </div>
</template>

<script>
import { getDelegateList, getAuditTree, addDelegation, isAuditor, delDelegation, updateDelegation, setupApprovalTrustee, getRange } from '@/api/dataEncryption/encryption/approvalProcess'
import TreeTableTransfer from '@/components/TreeTableTransfer'
import moment from 'moment'
import { getCategory } from '@/api/dataEncryption/encryption/approvalProcess'
import { getApprovalConfig } from '@/api/system/configManage/approvalConfig.js'

// const scopeType = [
//   { value: 0, label: '查询所有' },
//   { value: 1, label: '查询委托信息' },
//   { value: 2, label: '查询被委托信息' }
// ]

// const msgeffectType = [
//   { value: 'AllMsg', label: '全部委托' },
//   { value: 'true', label: '有效委托' },
//   { value: 'false', label: '失效委托' }
// ]

const keyValueFilter = function(options) {
  const typeKeyValue = options.reduce((acc, cur) => {
    acc[cur.value] = cur.label
    return acc
  }, {})
  return typeKeyValue
}

export default {
  name: 'DelegateProcess',
  components: { TreeTableTransfer },
  data() {
    const validateLastName = (rule, value, callback) => {
      if (value) {
        callback()
      } else {
        return callback(new Error(this.$t('pages.approvalProcess_Msg45')))
      }
    }
    const validateProcessIdArr = (rule, value, callback) => {
      if (this.checkedList.length > 0) {
        callback()
      } else {
        return callback(new Error(this.$t('pages.approvalProcess_Msg46')))
      }
    }
    return {
      pickerOptionsStart: {
        disabledDate: time => {
          const endDateVal = this.query.endTime
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime()
          }
        }
      },
      pickerOptionsStart2: {
        disabledDate: time => {
          const endDateVal = this.delegateForm.endTime
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime()
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.query.startTime
          if (beginDateVal) {
            return time.getTime() < new Date(beginDateVal).getTime() - 24 * 60 * 60 * 1000
          }
        }
      },
      pickerOptionsEnd2: {
        disabledDate: time => {
          const beginDateVal = this.delegateForm.beginTime
          if (beginDateVal) {
            return time.getTime() < new Date(beginDateVal).getTime() - 24 * 60 * 60 * 1000
          }
        }
      },
      query: {
        processName: '',
        deptId: '0',
        userId: '',
        scope: 0,
        msgeffect: 'AllMsg',
        categoryId: '',
        startTime: '',
        endTime: ''
      },
      tableLoading: false,
      submitting: false,
      showTree: true,
      processTreeData: [],
      checkedKeys: [],
      checkedList: [],
      rowData: [],
      colModel: [
        { prop: 'principalDeptName', label: 'principalGrouping', width: '120' },
        { prop: 'principalName', label: 'principal', width: '120' },
        { prop: 'trusteeDeptName', label: 'trusteeGroup', width: '120' },
        { prop: 'trusteeName', label: 'trustee', width: '120' },
        { prop: 'sfProcess', label: 'funType', width: '150', formatter: this.categoryFormatter },
        { prop: 'sfProcessName', label: 'floatName', width: '120' },
        { prop: 'isDelete', label: 'delegateStatus', width: '120', formatter: this.statusFormatter },
        { prop: 'beginTime', label: 'effectTime', width: '160' },
        { prop: 'endTime', label: 'failureTime', width: '160' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'delete', click: this.handleDel }
          ]
        }
      ],
      currentNode: undefined,
      deleteable: false,
      scopeType: [
        { value: 0, label: this.$t('pages.approvalProcess_Msg47') },
        { value: 1, label: this.$t('pages.approvalProcess_Msg48') },
        { value: 2, label: this.$t('pages.approvalProcess_Msg49') }
      ],
      categoryList: [],
      msgeffectType: [
        { value: 'AllMsg', label: this.$t('pages.approvalProcess_Msg50') },
        { value: 'true', label: this.$t('pages.approvalProcess_Msg51') },
        { value: 'false', label: this.$t('pages.approvalProcess_Msg52') }
      ],
      dialogDelegateVisible: false,
      dialogStatus: 'create',
      textMap: {
        create: this.$t('pages.approvalProcess_Msg53'),
        update: this.$t('pages.approvalProcess_Msg54')
      },
      iconOption: {
        'G': 'service',
        'L': 'process'
      },
      delegateForm: {
        lastNameLabel: '',
        trusteeState: true,
        trusteeMap: {},
        beginTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().add(7, 'd').endOf('day').format('YYYY-MM-DD HH:mm:ss')
      },
      updateObj: {},
      delegateRules: {
        lastNameLabel: [
          {
            required: true,
            trigger: 'change',
            validator: validateLastName
          }
        ],
        processIdArr: [
          {
            required: true,
            trigger: 'blur',
            validator: validateProcessIdArr
          }
        ],
        beginTime: [
          { required: true, message: this.$t('pages.approvalProcess_Msg66'), trigger: 'blur' },
          { validator: this.validatebeEffectTime, trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: this.$t('pages.approvalProcess_Msg67'), trigger: 'blur' },
          { validator: this.validateFailureTime, trigger: 'blur' }
        ]
      },
      disabledAllNodes: false,
      dialogShow: false,
      delegateLimitShow: false,
      nodeModel: {
        id: 'trust',
        // islimitRange: true, // 委托审批可选范围
        choosedData: [],
        userTreeData: [],
        rangeIds: []
      },
      delegateLimitModel: {
        id: 'delegationLimit',
        inherit: true,
        range: '0', // 0-不限制 1-不允许委托 2-自定义委托范围
        userLabel: '',
        choosedData: []
      },
      delegateLimitModelTemp: {
        id: 'delegationLimit',
        inherit: true,
        range: '0', // 0-不限制 1-不允许委托 2-自定义委托范围
        userLabel: '',
        choosedData: []
      },
      defaultExpandedKeys: [],
      inheritDisabled: false,
      noDelegate: false
    }
  },
  created() {
    this.strategyTargetNodeChange('user')
    this.getCategory()
    getApprovalConfig().then(res => {
      if (res.data.trusteeMenuSwitch == '1') {
        this.noDelegate = true
      } else {
        this.noDelegate = false
      }
    })
  },
  methods: {
    getCategory() {
      getCategory().then(res => {
        this.categoryList = res.data.map(item => {
          return {
            value: item.category,
            label: item.remark
          }
        })
      })
    },
    renderDeviceContentFunc(h, { node, data, store }) {
      const iconClass = <svg-icon icon-class={this.iconOption[data.dataCode]} />
      return (<span>{iconClass} {node.label}</span>)
    },
    categoryFormatter(row, data) {
      const type = Object.keys(keyValueFilter(this.categoryList)).find(item => {
        return row.sfProcess.includes(item)
      })
      return keyValueFilter(this.categoryList)[type]
    },
    statusFormatter(row, data) {
      return data == '0' ? this.$t('pages.approvalProcess_Msg51') : this.$t('pages.approvalProcess_Msg52')  
    },
    handleFilter() {
      this.strategyTargetNodeChange('user', this.currentNode)
    },
    strategyTargetNodeChange(tabName, data) {
      this.tableLoading = true
      if (data) {
        this.currentNode = data
        this.delegateForm.id = data.dataId
        this.delegateForm.principal = data.label
        if (data.type == '4') {
          this.query.deptId = data.dataId
          this.query.userId = ''
        } else {
          this.query.userId = data.dataId
          this.query.deptId = ''
        }
      }
      getDelegateList(this.query).then(res => {
        this.rowData = res.data
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    checkChange(value, data) {
      this.checkedList = data.map(item => item.id)
    },
    getApprover(value) {
      this.nodeModel.choosedData = value
      this.delegateForm.lastNameLabel = value.map(item => item.label).join()
      this.delegateForm.lastName = value.map(item => item.dataId).join()
      this.delegateForm.trusteeMap = keyValueFilter(value.map(item => { return { value: item.dataId, label: item.label } }))
      this.dialogShow = false
    },
    handleDelegateLimit() {
      this.inheritDisabled = false
      this.delegateLimitModel = Object.assign({}, this.delegateLimitModelTemp)
      this.delegateLimitModel.choosedData = []
      this.delegateLimitModel.userLabel = this.currentNode.label
      const limitQuery = {}
      if (this.currentNode.type == '4') {
        limitQuery.deptId = this.currentNode.dataId
      } else {
        limitQuery.userId = this.currentNode.dataId
      }
      getRange(limitQuery).then(res => {
        const data = res.data
        this.delegateLimitModel.choosedData = data.users
        this.delegateLimitModel.inherit = data.inherit
        this.delegateLimitModel.range = data.range
        if (this.currentNode.type == '4' && this.currentNode.dataId == '0') {
          this.inheritDisabled = true
          this.delegateLimitModel.inherit = false
        }
        this.delegateLimitShow = true
      }).catch(err => {
        console.log(err)
      })
    },
    getDelegateLimit(value) {
      const ids = value.map(item => item.dataId).join()
      const limit = {}
      if (this.currentNode.type == '4') {
        limit.deptId = this.currentNode.dataId
      } else {
        limit.userId = this.currentNode.dataId
      }
      
      if (this.delegateLimitModel.inherit) {  // 继承上一级
        limit.trusteeId = ''
        limit.trusteeDeptId = ''
      } else if (this.delegateLimitModel.range == '0') { // 不限制
        limit.trusteeId = '0'
        limit.trusteeDeptId = '0'
      } else if (this.delegateLimitModel.range == '1') { // 不允许委托
        limit.trusteeId = '-1'
        limit.trusteeDeptId = '-1'
      } else { //
        limit.trusteeId = ids
        limit.trusteeDeptId = ''
      }
      limit.inherit = this.delegateLimitModel.inherit
      if (!this.delegateLimitModel.inherit && this.delegateLimitModel.range == '2' && !limit.trusteeId) {
        this.$message({
          message: this.$t('pages.approvalProcess_Msg72'),
          type: 'error'
        })
        return
      } else {
        setupApprovalTrustee(limit).then(res => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.createSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(() => {

        })
      }
      this.delegateLimitShow = false
    },
    resetDelegateForm() {
      this.$nextTick(() => {
        this.disabledAllNodes = false
        this.delegateForm.lastNameLabel = ''
        this.delegateForm.beginTime = moment().format('YYYY-MM-DD HH:mm:ss')
        this.delegateForm.endTime = moment().add(7, 'd').endOf('day').format('YYYY-MM-DD HH:mm:ss')
      })
    },
    handleCreate() {
      this.defaultExpandedKeys = []
      this.checkedKeys = []
      this.checkedList = []
      this.nodeModel.choosedData = []
      this.resetDelegateForm()
      this.dialogDelegateVisible = true
      isAuditor({
        tenantId: 'ld',
        userId: this.currentNode.dataId,
        trustExclude: true
      }).then(res => {
        if (res.data === 'true') {
          this.dialogStatus = 'create'
          getAuditTree(this.currentNode.dataId).then(res => {
            this.processTreeData = res.data
          })
        } else {
          this.$message({
            message: this.$t('pages.approvalProcess_Msg55'),
            type: 'error'
          })
        }
        this.$nextTick(() => {
          this.$refs['delegateForm'].clearValidate()
        })
      })
    },
    handleUpdate(row) {
      this.defaultExpandedKeys = []
      this.checkedKeys = []
      this.checkedList = []
      this.dialogStatus = 'update'
      this.dialogDelegateVisible = true
      this.updateObj = Object.assign({}, row)
      this.delegateForm.principal = row.principalName
      this.delegateForm.lastNameLabel = row.trusteeName
      this.delegateForm.beginTime = row.beginTime
      this.delegateForm.endTime = row.endTime
      this.disabledAllNodes = true
      getAuditTree(row.principalId).then(res => {
        this.processTreeData = res.data
        this.$nextTick(() => {
          this.checkedKeys = row.sfProcess.split(',')
          this.defaultExpandedKeys = [...this.checkedKeys]
          this.checkedList = this.checkedKeys
          this.$nextTick(() => {
            this.$refs['delegateForm'].clearValidate()
          })
        })
      })
    },
    createDelegation() {
      this.delegateForm.processIdArr = this.$refs.processTree.$refs.tree.getCheckedNodes().filter(item => item.dataCode === 'L').map(item => item.dataId).join()
      this.$refs['delegateForm'].validate((valid) => {
        if (valid) {
          if (this.delegateForm.processIdArr) {
            this.submitting = true
            addDelegation(this.delegateForm).then(res => {
              this.dialogDelegateVisible = false
              this.strategyTargetNodeChange('user', this.currentNode)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
              this.submitting = false
            }).catch(err => {
              console.log(err)
              this.submitting = false
            })
          } else {
            this.$message({
              message: this.$t('pages.approvalProcess_Msg56'),
              type: 'error'
            })
          }
        }
      })
    },
    updateDelegation() {
      this.delegateForm.processIdArr = this.$refs.processTree.$refs.tree.getCheckedNodes().filter(item => item.dataCode === 'L').map(item => item.dataId).join()
      this.$refs['delegateForm'].validate((valid) => {
        if (valid) {
          if (this.delegateForm.processIdArr) {
            this.submitting = true
            this.updateObj.beginTime = this.delegateForm.beginTime
            this.updateObj.endTime = this.delegateForm.endTime
            updateDelegation(this.updateObj).then(res => {
              this.dialogDelegateVisible = false
              this.strategyTargetNodeChange('user', this.currentNode)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
              this.submitting = false
            }).catch(err => {
              console.log(err)
              this.submitting = false
            })
          } else {
            this.$message({
              message: this.$t('pages.approvalProcess_Msg56'),
              type: 'error'
            })
          }
        }
      })
    },
    handleDel(data) {
      this.$confirmBox(this.$t('pages.approvalProcess_Msg57'), this.$t('text.prompt')).then(() => {
        delDelegation([data]).then(respond => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.strategyTargetNodeChange('user', this.currentNode)
        })
      }).catch(() => {})
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    batchDel() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const data = this.$refs['tableList'].selectedData
        delDelegation(data).then(respond => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.strategyTargetNodeChange('user', this.currentNode)
        })
      }).catch(() => {})
    },
    validatebeEffectTime(rule, value, callback) {
      if (this.delegateForm.endTime) {
        var begin = new Date(this.delegateForm.beginTime)
        var end = new Date(this.delegateForm.endTime)
        if (begin.getTime() > end.getTime()) {
          callback(new Error(this.$t('pages.approvalProcess_Msg70')))
        } else {
          callback()
        }
      }
    },
    validateFailureTime(rule, value, callback) {
      var end = new Date(this.delegateForm.endTime)
      if (this.delegateForm.beginTime) {
        var begin = new Date(this.delegateForm.beginTime)
        if (begin.getTime() > end.getTime()) {
          callback(new Error(this.$t('pages.approvalProcess_Msg69')))
        }
      }
      var now = new Date()
      if (now.getTime() > end.getTime()) {
        callback(new Error(this.$t('pages.approvalProcess_Msg68')))
      } else {
        callback()
      }
    },
    // 跳转到审批参数配置
    toApprovalConfig() {
      if (this.hasPermission('A63')) {
        this.$router.push('/approvalManage/ApprovalConfig/approvalConfig')
      } else {
        this.$message({
          message: this.$t('pages.encOrDecLog_Msg1'),
          type: 'error',
          duration: 2000
        })
      }
    }
     
  }
}
</script>

<style lang="scss">
.delegate-tree{
  .el-tree{
    margin-bottom: 10px;
  }
}

</style>

<style lang="scss" scoped>
.el-input, .el-select,.el-button{
  margin-bottom: 8px;
}
.tableBox{
  height: calc(100% - 60px);
}
</style>
