<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <el-button v-if="showHighConfig && treeable && tabLabel == 'serverTab'" icon="el-icon-setting" size="mini" @click="handleConfig">
          {{ $t('table.highConfig') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="tabLabel === 'serverTab' ? 62 : tabLabel === 'processTab' ? 85 : ''"/>

        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <el-tabs ref="tabs" v-model="tabLabel" type="card" style="height: calc( 100% - 40px);" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.httpWhiteList')" name="serverTab" class="table-container">
          <grid-table ref="serverList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="handleSelectionChange"/>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.processFilter')" name="processTab" class="table-container">
          <grid-table ref="processList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="handleSelectionChange"/>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ textMap[dialogStatus] }}
        <!-- <el-tooltip v-show="dialogStatus.startsWith('createP') || dialogStatus.startsWith('updateP')" effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('pages.httpWhiteList_text1') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip> -->
      </div>
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="62"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="stgTargetDisabled"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <!-- <FormItem v-if="tabLabel=='processTab'" :label="$t('table.processName1')" prop="nameStr">
          <el-input v-model="temp.nameStr" :placeholder="$t('pages.httpWhiteList_text2')" maxlength="" :disabled="!formable"></el-input>
        </FormItem> -->
        <el-divider v-if="tabLabel=='processTab'" content-position="left">{{ $t('pages.serverProcess_Msg24') }}</el-divider>
        <LocalZoomIn v-if="tabLabel=='processTab'" parent-tag="el-dialog">
          <el-row v-if="formable">
            <el-col :span="15">
              <el-button size="small" @click="handleCreate2()">{{ $t('button.insert') }}</el-button>
              <el-button size="small" :disabled="!deleteable1" @click="deleteProcessName()">{{ $t('button.delete') }}</el-button>
            </el-col>
          </el-row>
          <grid-table
            ref="processList1"
            auto-height
            :row-datas="nameTag"
            :show-pager="false"
            :col-model="colModel2"
            :multi-select="true"
            @selectionChangeEnd="selectionChangeEnd1"
          />
        </LocalZoomIn>
        <!-- <FormItem v-if="tabLabel=='processTab'" :label="$t('pages.processType')">
          <el-radio-group v-model="temp.controlCode">
            <el-radio :label="0">{{ $t('pages.serverProcess_Msg2') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.serverProcess_Msg1') }}</el-radio>
          </el-radio-group>
        </FormItem> -->
        <div v-show="tabLabel=='serverTab'">
          <el-divider content-position="left">{{ $t('text.select') }}{{ $store.getters.language === 'en'?' ':'' }}{{ stgName }}</el-divider>
          <import-table
            ref="whiteListTable"
            auto-height
            :search-info-prompt-message="$t('pages.serverLibrary_text13')"
            :row-no-label="$t('table.keyId')"
            :delete-disabled="!deleteable"
            :col-model="whiteListColModel"
            :row-datas="tempWhiteListElgData"
            :formable="formable"
            :selectable="whiteListSelectable"
            :handle-create="handleWhiteListCreateImport"
            :handle-delete="handleWhiteListDelete"
            :handle-import="handleWhiteListImport"
            :handle-search="handleWhiteListSearch"
            @selectionChangeEnd="selectionChangeEnd"
          />
        </div>
        <div v-show="tabLabel=='serverTab'">
          <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
          <FormItem label-width="30px">
            <el-checkbox v-model="temp.isRecord" :disabled="!formable" :true-label="1" :false-label="0">
              {{ $t('pages.recordingServerWhitelist') }}
              <el-tooltip class="item" effect="dark" placement="right" :content="$t('pages.serverWhiteListTip1')">
                <i class="el-icon-info" />
              </el-tooltip>
            </el-checkbox><br>
            <el-checkbox v-model="temp.unlawfulProcess" :disabled="!formable" :true-label="1" :false-label="0" @change="unlawfulProcessChange()">
              {{ $t('pages.serverProcess_Msg5') }}
            </el-checkbox>
            <el-checkbox v-model="temp.prohibitStgMerge" :disabled="!formable" :true-label="1" :false-label="0" @change="prohibitStgMergeChange()">
              {{ $t('pages.serverProcess_Msg6') }}
            </el-checkbox>

            <!-- 终端要求 去除违规响应规则的配置项，同时移除 策略表 关于定义的 OptionId == 10003 的配置项   -->
            <!-- <ResponseContent-->
            <!--   :status="dialogStatus"-->
            <!--   style="line-height: 15px !important;"-->
            <!--   :select-style="{ 'margin-top': '5px', 'margin-left': '-18px' }"-->
            <!--   :show-select="true"-->
            <!--   :editable="formable && propRuleDisabled"-->
            <!--   read-only-->
            <!--   :prop-rule-id="propRuleId"-->
            <!--   :prop-check-rule="!!temp.isAlarm"-->
            <!--   :show-check-rule="true"-->
            <!--   @getRuleId="getRuleId"-->
            <!--   @ruleIsCheck="getEncRuleIsCheck"-->
            <!-- />-->
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <link-button
          v-if="formable && tabLabel==='serverTab'"
          btn-type="primary"
          btn-style="float: left"
          :formable="formable && stgName===$t('pages.service')"
          :menu-code="'A57'"
          :btn-text="$t('pages.maintainInfoBase', { info: stgName })"
          :click-func="'configRelateData'"
          @configRelateData="configRelateData"
        />
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="tabLabel === 'serverTab' ? 62 : tabLabel === 'processTab' ? 85 : null"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus.startsWith('create')?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.serverProcess_Msg24')"
      :visible.sync="stgVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="stgForm" :rules="stgRules" :model="stgTemp" label-position="right" label-width="90px" style="width: 400px;">
        <FormItem :label="$t('table.processName')" prop="name">
          <el-input v-model="stgTemp.name" v-trim :maxlength="50"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.serverProcessType')">
          <el-radio-group v-model="stgTemp.controlCode">
            <el-radio :label="0">{{ $t('pages.serverProcess_Msg2') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.serverProcess_Msg1') }}</el-radio>
          </el-radio-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="stgVisibleStatus === 'create' ? createData2() : updateData2()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="stgVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 新增,修改 弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[whiteListDialogStatus]"
      :visible.sync="whiteListDialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="whiteListDataForm" :rules="whiteListRules" :model="whiteListTemp" label-position="right" label-width="110px" style="width: 700px;">
        <FormItem :label="$t('pages.serverName')" prop="name">
          <el-input v-model="whiteListTemp.name" v-trim :maxlength="60"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="addGroupAble ? 22 : 24">
              <el-select v-model="whiteListTemp.groupId" filterable :placeholder="$t('text.select')">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
            </el-col>
            <el-col v-if="addGroupAble" style="padding-top:1px" :span="2">
              <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem>
          <el-switch v-model="whiteListTemp.useNewVersion" :active-text="$t('pages.useNewVersion')" :active-value="0" :inactive-value="1" @change="versionChange"/>
        </FormItem>
        <FormItem v-if="whiteListTemp.limitNet!==1" :label="$t('pages.ipType')" prop="ipType">
          <el-select v-model="whiteListTemp.ipType" style="width: 150px;" @change="ipTypeChange">
            <el-option v-for="(item, key) in ipTypeOptions" :key="key" :label="item.label" :value="parseInt(item.value)"></el-option>
          </el-select>
        </FormItem>
        <FormItem v-if="whiteListTemp.useNewVersion==1 || whiteListTemp.useNewVersion==2" :label="$t('pages.whiteListMode')">
          <el-select v-model="whiteListTemp.limitNet" style="width: 150px;" @change="limitNetTypeChange">
            <el-option v-for="(value, key) in netOptions" :key="key" :label="value" :value="parseInt(key)"></el-option>
          </el-select>
        </FormItem>
        <div v-if="whiteListTemp.ipType==0 || whiteListTemp.ipType==2">
          <div v-if="whiteListTemp.useNewVersion == 0">
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('pages.beginIp')" prop="beginIp">
                  <el-input v-model="whiteListTemp.beginIp" @blur="inputBlur('endIp')"></el-input>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('pages.endIp')" prop="endIp">
                  <el-input v-model="whiteListTemp.endIp" @blur="inputBlur('beginIp')"></el-input>
                </FormItem>
              </el-col>
            </el-row>
          </div>
          <div v-if="whiteListTemp.useNewVersion == 1 || whiteListTemp.useNewVersion == 2">
            <FormItem :tooltip-content="$t('pages.ipOrDomainConfigTip', { ipOrDomain: $t('pages.serverIp') })" :label="$t('pages.serverIp')" prop="ip">
              <el-input v-model="whiteListTemp.ip" v-trim></el-input>
            </FormItem>
          </div>
        </div>
        <div v-if="whiteListTemp.ipType==1">
          <FormItem :tooltip-content="$t('pages.ipOrDomainConfigTip', { ipOrDomain: whiteListTemp.limitNet==1 ? $t('pages.serverAddress') : $t('pages.domainName') })" :label="whiteListTemp.limitNet==1 ? $t('pages.serverAddress') : $t('pages.domainName')" prop="domainName">
            <el-input v-model="whiteListTemp.domainName" v-trim :maxlength="64"></el-input>
          </FormItem>
        </div>
        <div v-if="whiteListTemp.useNewVersion == 0">
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('pages.beginPort')" prop="beginPort">
                <el-input v-model="whiteListTemp.beginPort" maxlength="5" @blur="inputBlur('endPort')" @input="handleBeginInput"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.endPort')" prop="endPort">
                <el-input v-model="whiteListTemp.endPort" maxlength="5" @blur="inputBlur('beginPort')" @input="handleEndInput"></el-input>
              </FormItem>
            </el-col>
          </el-row>
        </div>
        <div v-if="whiteListTemp.useNewVersion == 1 || whiteListTemp.useNewVersion == 2">
          <FormItem :label="$t('pages.portMode')">
            <el-radio-group v-model="whiteListTemp.portMode" @change="portModeChange">
              <el-radio v-for="(value, key) in portModeOptions" :key="key" :label="parseInt(key)">{{ value }}</el-radio>
            </el-radio-group>
          </FormItem>
          <FormItem v-if="whiteListTemp.portMode === 1" :label="$t('pages.serverPort')" prop="port">
            <el-input v-model="whiteListTemp.port" maxlength="5" @input="handleInput"></el-input>
          </FormItem>
          <el-row v-if="whiteListTemp.portMode === 2">
            <el-col :span="12">
              <FormItem :label="$t('pages.beginPort')" prop="beginPort">
                <el-input v-model="whiteListTemp.beginPort" maxlength="5" @blur="inputBlur('endPort')" @input="handleBeginInput"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.endPort')" prop="endPort">
                <el-input v-model="whiteListTemp.endPort" maxlength="5" @blur="inputBlur('beginPort')" @input="handleEndInput"></el-input>
              </FormItem>
            </el-col>
          </el-row>
        </div>
        <FormItem :label="$t('pages.serverProcess_Msg10')" prop="encDecType">
          <el-radio-group v-model="whiteListTemp.encDecType" @change="encAndDecChange">
            <el-radio :label="1">{{ $t('pages.serverProcess_Msg20') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.serverProcess_Msg19') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <div>
          <FormItem :label="$t('pages.uploadType')" prop="uploadType">
            <el-select v-model="whiteListTemp.uploadType" style="width: 150px;" :disabled="whiteListTemp.encDecType === 1">
              <el-option
                v-for="(value, key) in loadTypeOptions"
                v-show="key!=2"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              />
            </el-select>
          </FormItem>
          <el-row>
            <el-col :span="20">
              <FormItem :label="$t('pages.uploadFileExt')" prop="uploadFileExt">
                <el-input v-model="whiteListTemp.uploadFileExt" :disabled="whiteListTemp.allOfUpload || whiteListTemp.encDecType === 1" maxlength="400"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="3" style="margin: 5px 0 0 5px;">
              <el-checkbox v-model="whiteListTemp.allOfUpload" :disabled="whiteListTemp.allOfUploadAble" @change="uploadFileExtChange">{{ $t('pages.allType') }}</el-checkbox>
            </el-col>
          </el-row>
          <FormItem :label="$t('pages.downloadType')" prop="downloadType">
            <el-select v-model="whiteListTemp.downloadType" style="width: 150px;" :disabled="whiteListTemp.encDecType === 1">
              <el-option
                v-for="(value, key) in loadTypeOptions"
                v-show="key!=1"
                :key="key"
                :label="value"
                :value="parseInt(key)"
              />
            </el-select>
          </FormItem>
          <el-row>
            <el-col :span="20">
              <FormItem :label="$t('pages.downloadFileExt')" prop="downloadFileExt">
                <el-input v-model="whiteListTemp.downloadFileExt" :disabled="whiteListTemp.allOfDownload || whiteListTemp.encDecType === 1" maxlength="400"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="3" style="margin: 5px 0 0 5px;">
              <el-checkbox v-model="whiteListTemp.allOfDownload" :disabled="whiteListTemp.allOfDownloadAble" @change="downloadFileExtChange">{{ $t('pages.allType') }}</el-checkbox>
            </el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="20">
            <FormItem :label="$t('pages.processName')" prop="processName">
              <el-input v-if="whiteListTemp.useNewVersion==2" v-model="whiteListTemp.processName" v-trim type="textarea" maxlength="255" rows="3" ></el-input>
              <el-input v-if="whiteListTemp.useNewVersion==1" v-model="whiteListTemp.processName" v-trim type="textarea" maxlength="255" rows="3" ></el-input>
              <el-input v-if="whiteListTemp.useNewVersion==0" v-model="whiteListTemp.processName" v-trim disabled type="textarea" maxlength="255" rows="3" ></el-input>
            </FormItem>
          </el-col>
          <el-col v-if="whiteListTemp.useNewVersion==1 || whiteListTemp.useNewVersion==2" :span="3" style="margin: 5px 0 0 5px;">
            <el-button icon="el-icon-add" size="small" @click="dialogBrowserVisible=true">{{ $t('button.choose') }}</el-button>
          </el-col>
        </el-row>
        <el-row>
          <span style="color:red;margin-left: 110px;display: inline-block; width: 590px">{{ tipMsg }}</span>
        </el-row>
        <el-row v-if="(whiteListTemp.useNewVersion == 1 || whiteListTemp.useNewVersion == 2) && whiteListTemp.limitNet == 0">
          <el-col :span="11">
            <FormItem :label="$t('pages.upLoadLimitSpeed')" prop="upLoadLimitSpeed">
              <!-- <el-input v-model="whiteListTemp.upLoadLimitSpeed" maxlength="10" @keyup.native="flowNumber('upLoadLimitSpeed')" /> -->
              <el-input-number v-model="whiteListTemp.upLoadLimitSpeed" :min="-1" :max="9999999999"></el-input-number>
            </FormItem>
          </el-col>
          <el-col :span="2" style="padding-top: 8px; padding-left: 5px;">
            <span>(B/S)</span>
            <el-tooltip class="item" effect="dark" placement="bottom-start">
              <div slot="content">{{ $t('pages.serverLibrary_text1') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-col>
        </el-row>
        <!--<FormItem prop="waitAccessLogin">
          <el-checkbox v-model="temp.waitAccessLogin" :false-label="0" :true-label="1" >验证服务器</el-checkbox>
          <el-tooltip class="item" effect="dark" placement="right">
            <div slot="content">验证服务器需要结合应用安全接入系统才可实现</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="whiteListDialogStatus==='createWhiteList'?createWhiteList(): whiteListDialogStatus==='updateWhiteList'?updateWhiteList():{}">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="whiteListDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.fileOutgoingConfig')"
      :visible.sync="dialogHighConfigVisible"
      width="700px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="advancedDataForm"
        :model="advancedConfig"
        :rules="advancedConfigRules"
        label-position="right"
        label-width="105px"
        style="width: 630px; margin-left:10px;"
      >
        <div>
          <label style="margin-left: 13px;">{{ $t('pages.serverProcess_Msg8') }}</label>
          <fieldset style="border-width: 1px;margin-top: 7px;">
            <legend>{{ $t('pages.serverProcess_Msg9') }}</legend>

            <FormItem :label="$t('pages.uploadType')" prop="uploadType">
              <el-select v-model="advancedConfig.uploadType" style="width: 150px;">
                <el-option
                  v-for="(value, key) in loadTypeOptions"
                  v-show="key!=2"
                  :key="key"
                  :label="value"
                  :value="parseInt(key)"
                />
              </el-select>
            </FormItem>
            <el-row>
              <el-col :span="20">
                <FormItem :label="$t('pages.uploadFileExt')" prop="uploadFileExt">
                  <el-input v-model="advancedConfig.uploadFileExt" :disabled="advancedConfig.allOfUpload" maxlength="400"></el-input>
                </FormItem>
              </el-col>
              <el-col :span="3" style="margin: 5px 0 0 5px;">
                <el-checkbox v-model="advancedConfig.allOfUpload" @change="advancedConfigUploadFileExtChange">{{ $t('pages.allType') }}</el-checkbox>
              </el-col>
            </el-row>

            <!--  -->
            <FormItem :label="$t('pages.downloadType')" prop="downloadType">
              <el-select v-model="advancedConfig.downloadType" style="width: 150px;">
                <el-option
                  v-for="(value, key) in loadTypeOptions"
                  v-show="key!=1"
                  :key="key"
                  :label="value"
                  :value="parseInt(key)"
                />
              </el-select>
            </FormItem>
            <el-row>
              <el-col :span="20">
                <FormItem :label="$t('pages.downloadFileExt')" prop="downloadFileExt">
                  <el-input v-model="advancedConfig.downloadFileExt" :disabled="advancedConfig.allOfDownload" maxlength="400"></el-input>
                </FormItem>
              </el-col>
              <el-col :span="3" style="margin: 5px 0 0 5px;">
                <el-checkbox v-model="advancedConfig.allOfDownload" @change="advancedConfigDownloadFileExtChange">{{ $t('pages.allType') }}</el-checkbox>
              </el-col>
            </el-row>
            <label style="color: #3296FA;">{{ $t('pages.serverProcess_Msg3') }}</label>
          </fieldset>
          <fieldset style="border-width: 1px;margin-top: 7px;">
            <legend>{{ $t('pages.serverProcess_Msg10') }}</legend>
            <FormItem>
              <el-radio-group v-model="advancedConfig.encAndDecCode" style="margin-left: -50px;">
                <el-radio :label="0">
                  {{ $t('pages.serverProcess_Msg15') }}
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.serverProcess_Msg16') }}
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-radio>
                <el-radio :label="1">
                  {{ $t('pages.serverProcess_Msg17') }}
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.serverProcess_Msg18') }}
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
            </FormItem>
          </fieldset>
        </div>
        <div style="margin-top: 10px;">
          <label style="margin-left: 13px;">{{ $t('pages.serverProcess_Msg11') }}</label>
          <fieldset style="border-width: 1px;margin-top: 7px;">
            <legend>{{ $t('pages.serverProcess_Msg12') }}</legend>
            <FormItem style="margin-left: -50px;">
              <el-checkbox v-model="advancedConfig.prohibitUpload" :false-label="0" :true-label="1">
                {{ $t('pages.prohibitUploadMsg1') }}
              </el-checkbox>
            </FormItem>
          </fieldset>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveConfig()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogHighConfigVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.serverLibrary_text2')"
      :visible.sync="dialogBrowserVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <grid-table ref="browserList" :height="200" :col-model="browserColModel" :show-pager="false" :row-datas="browserDatas" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addBrowser()">
          {{ $t('button.insert') }}
        </el-button>
        <el-button @click="dialogBrowserVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-table-dlg
      ref="whiteListImportTable"
      :exits-list-data="whiteListElgData"
      :elg-title="$t('pages.importServerLibrary')"
      :group-root-name="$t('pages.serverLibrary')"
      :search-info-name="searchInfoName"
      :confirm-button-name="$t('pages.addServer')"
      :prompt-message="$t('pages.serverLibrary_text12')"
      :group-title="$t('pages.serverGroup')"
      :col-model="importColModel"
      :invoke-query="tableQuery"
      tree-root-data-id="-1"
      not-group-data-id="0"
      :list="getInfoList"
      :load-group-tree="listGroupTreeData"
      :create-group="createWhiteListServerGroup"
      :update-group="updateWhiteListServerGroup"
      :delete-group="deleteWhiteListServerGroup"
      :count-by-group="countChildByGroupId"
      :get-group-by-name="getGroupByName"
      :delete="deleteWhiteListServer"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteWhiteListServerGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleWhiteListCreate"
      :get-list-by-group-ids="getServerLibraryByGroupIds"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
      @elgCancelAfter="elgCancelAfter"
      @changeGroupAfter="changeGroupAfter"
    >
      <template slot="search-left">
        <el-checkbox v-model="tableQuery.opt" :true-label="1" :false-label="0" @change="changeOpt">{{ $t('pages.accordToQuery', { info: $t('pages.processName1') } ) }}</el-checkbox>
      </template>
    </import-table-dlg>

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="tabLabel === 'serverTab' ? $t('pages.importHttpWhiteListStg') : tabLabel === 'processTab' ? $t('pages.importHttpWhiteListProcessFilterStg') : ''"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="tabLabel === 'serverTab' ? 62 : tabLabel === 'processTab' ? 85 : null"
      @success="importSuccess"
    />
    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :edit-valid-func="getGroupByName"
      :add-func="createWhiteListServerGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>

<script>
import {
  createProcessStg, createServerStg, deleteProcessStg, deleteServerStg, getProcessStgByName, getProcessStgPage,
  getServerStgByName, getServerStgPage, getServerTreeNode, updateProcessStg, updateServerStg, updateConfig
} from '@/api/dataEncryption/encryption/httpWhiteList'
import {
  countChildByGroupId, createWhiteListServer, createWhiteListServerGroup, deleteGroupAndData, deleteWhiteListServer,
  deleteWhiteListServerGroup, getGroupByName, getServerLibraryByGroupIds, getServerLibraryByIds, getSysBrowserPage,
  getWhiteListServerByName, getWhiteListServerPage, listGroupTree, moveGroup, moveGroupToOther,
  updateWhiteListServer, updateWhiteListServerGroup, updateDefaultEncStgServer
} from '@/api/system/baseData/serverLibrary'
import {
  buttonFormatter, enableStgDelete, entityLink, hiddenActiveAndEntity,
  objectFormatter, refreshPage, selectable
} from '@/utils'
import { isIPv4, isIPv6, validatePolicy } from '@/utils/validate'
// import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import { findNodeLabel } from '@/utils/tree'
import EditGroupDlg from '@/views/common/editGroupDlg'
import { getConfigByKey } from '@/api/system/configManage/globalConfig'
import { getPropertyByCode } from '@/api/property'

export default {
  name: 'HttpWhiteList',
  components: { ImportStg, ImportTable, ImportTableDlg, EditGroupDlg/* , ResponseContent*/ },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    tabName: { type: String, default: 'serverTab' },
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'serverName', hidden: this.stgHidden, width: '200', formatter: this.stgInfoFormatter },
        // { prop: 'controlCode', label: 'serverProcessType', hidden: () => this.processTypeFlag, width: '100', formatter: this.typeFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      colModel2: [
        { prop: 'name', label: 'processName', width: '150', sort: true },
        { prop: 'controlCode', label: 'serverProcessType', width: '150', sort: true, formatter: this.typeFormatter },
        { label: 'operate', type: 'button', width: '100', hidden: !this.formable,
          buttons: [{
            label: 'edit', click: this.handleUpdate2
          }]
        }
      ],
      tabLabel: this.tabName,
      stgName: this.$t('pages.service'),
      stgHidden: false,
      processTypeFlag: true,
      query: { // 查询条件
        entityType: undefined,
        entityId: undefined
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        active: true,
        name: '',
        remark: '',
        processIds: [],
        names: [],
        servers: [],
        serverIds: [],
        entityId: undefined,
        nameStr: '',
        isRecord: 0,
        unlawfulProcess: 0,
        prohibitStgMerge: 0,
        popupConfig: [],
        processConfig: [],
        isAlarm: 0
      },
      showTree: true,
      treeable: true,
      addBtnAble: false,
      deleteable: false,
      submitting: false,
      stgVisible: false,
      stateOptions: { 0: this.$t('text.normal'), 1: this.$t('text.disable') },
      serverTreeData: [], // 服务器信息树
      processTreeData: [], // 进程信息树
      relateDataTreeData: [
        { label: this.$t('pages.serverGroup'),
          dataId: '',
          children: [{ label: this.$t('pages.ungrouped'), dataId: '0', children: [], type: 'G' }],
          type: 'G'
        }
      ],
      iconOption: { G: 'group', '': 'server' },
      checkedTreeIds: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        updateS: this.i18nConcatText(this.$t('pages.serverWhitelistStg'), 'update'),
        createS: this.i18nConcatText(this.$t('pages.serverWhitelistStg'), 'create'),
        updateP: this.i18nConcatText(this.$t('pages.processFilterConfigStg'), 'update'),
        createP: this.i18nConcatText(this.$t('pages.processFilterConfigStg'), 'create'),
        createWhiteList: this.i18nConcatText(this.$t('pages.serverInformation'), 'create'),
        updateWhiteList: this.i18nConcatText(this.$t('pages.serverInformation'), 'update')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        nameStr: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ]

      },
      stgRules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ]
      },
      advancedConfigRules: {
        uploadFileExt: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        downloadFileExt: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ]
      },
      whiteListColModel: [
        { prop: 'name', label: 'serverName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, sortOriginal: true, formatter: this.serverGroupNameFormatter },
        { prop: 'useNewVersion', label: 'useNewVersion', width: '150', sort: true, sortOriginal: true, formatter: this.useNewVersionFormatter },
        { label: 'whiteListMode', width: '150', formatter: this.modelFormatter },
        { prop: 'ipType', label: 'ipType', width: '150', sort: true, sortOriginal: true, formatter: this.ipTypeFormatter },
        { prop: 'beginIp', label: 'beginIp', width: '150', sort: true },
        { prop: 'endIp', label: 'endIp2', width: '150', sort: true },
        { prop: 'beginPort', label: 'beginPort', width: '150', sort: true },
        { prop: 'endPort', label: 'endPort', width: '150', sort: true },
        { prop: 'uploadType', label: 'uploadType', width: '150', sort: true, sortOriginal: true, formatter: this.loadTypeFormatter },
        { prop: 'uploadFileExt', label: 'uploadFileExt', width: '150', sort: true },
        { prop: 'downloadType', label: 'downloadType', width: '150', sort: true, sortOriginal: true, formatter: this.loadTypeFormatter },
        { prop: 'downloadFileExt', label: 'downloadFileExt', width: '150', sort: true },
        { prop: 'processName', label: 'processName', width: '150', sort: true },
        { prop: 'upLoadLimitSpeed', label: 'upLoadLimitSpeed', width: '150', sort: true },
        { label: 'operate', type: 'button', width: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleWhiteListUpdateBase }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'serverName', width: '150' },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.serverGroupNameFormatter },
        { prop: 'useNewVersion', label: 'useNewVersion', width: '150', formatter: this.useNewVersionFormatter },
        { label: 'whiteListMode', width: '150', formatter: this.modelFormatter },
        { prop: 'ipType', label: 'ipType', width: '150', formatter: this.ipTypeFormatter },
        { prop: 'beginIp', label: 'beginIp', width: '150' },
        { prop: 'endIp', label: 'endIp2', width: '150' },
        { prop: 'beginPort', label: 'beginPort', width: '150' },
        { prop: 'endPort', label: 'endPort', width: '150' },
        { prop: 'uploadType', label: 'uploadType', width: '150', formatter: this.loadTypeFormatter },
        { prop: 'uploadFileExt', label: 'uploadFileExt', width: '150' },
        { prop: 'downloadType', label: 'downloadType', width: '150', formatter: this.loadTypeFormatter },
        { prop: 'downloadFileExt', label: 'downloadFileExt', width: '150' },
        { prop: 'processName', label: 'processName', width: '150' },
        { prop: 'upLoadLimitSpeed', label: 'upLoadLimitSpeed', width: '150' },
        { label: 'operate', type: 'button', width: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleWhiteListUpdate }
          ]
        }
      ],
      tempWhiteListElgData: [],
      whiteListElgData: [],
      treeSelectNode: [{ label: this.$t('pages.ungrouped'), dataId: '0' }],
      whiteListDialogFormVisible: false,
      whiteListDialogStatus: '',
      stgTemp: {},
      stgDefaultTemp: {
        id: '',
        name: '',
        controlCode: 0
      },
      nameTag: [],
      whiteListTemp: {},
      whiteListDefaultTemp: { // 表单字段
        id: undefined,
        name: '',
        groupId: undefined,
        ipType: 1,
        beginIp: '',
        endIp: '',
        ip: '',
        domainName: '',
        beginPort: 80,
        endPort: 80,
        portMode: 1,
        port: 80,
        uploadType: 0,
        uploadFileExt: '*.*',
        downloadType: 0,
        downloadFileExt: '*.*',
        processName: '',
        waitAccessLogin: 0,
        upLoadLimitSpeed: 0,
        bufferTime: 2000,
        useNewVersion: 1,
        encDecType: 1,
        limitNet: 0,
        allOfDownload: true,
        allOfUpload: true,
        allOfUploadAble: true,
        allOfDownloadAble: true
      },
      deleteable1: false,
      whiteListRules: {
        name: [{ required: true, trigger: 'blur', validator: this.urlNameValidator }],
        beginIp: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        endIp: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        ip: [
          { required: true, message: this.$t('pages.validateMsg_Ip'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        domainName: [{ required: true, message: this.$t('pages.validateMsg_domainName'), trigger: 'blur' }],
        beginPort: [
          { required: true, message: this.$t('pages.validateMsg_beginPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        endPort: [
          { required: true, message: this.$t('pages.validateMsg_endPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        port: [
          { required: true, message: this.$t('pages.validateMsg_port'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        uploadFileExt: [{ required: true, message: this.$t('pages.validateMsg_uploadFileExt'), trigger: 'blur' }],
        downloadFileExt: [{ required: true, message: this.$t('pages.validateMsg_downloadFileExt'), trigger: 'blur' }],
        processName: [
          { required: true, message: this.$t('pages.validateMsg_processName'), trigger: ['blur', 'change'] },
          { validator: this.processNameValidator, trigger: ['blur', 'change'] }
        ],
        upLoadLimitSpeed: [{ required: true, message: this.$t('pages.validateMsg_upLoadLimitSpeed'), trigger: 'blur' }],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'change' }
        ]
      },
      useNewVersionOptions: { 0: this.$t('text.yes'), 1: this.$t('text.no'), 2: this.$t('text.no') },
      ipTypeOptionMap: { 0: this.$t('pages.IPv4Addr'), 1: this.$t('pages.domain'), 2: this.$t('pages.IPv6Addr') },
      ipTypeOptions: [{ label: this.$t('pages.domain'), value: 1 }, { label: this.$t('pages.IPv4Addr'), value: 0 }, { label: this.$t('pages.IPv6Addr'), value: 2 }],
      netOptions: { 0: this.$t('pages.serverLibrayWhiteListMode1'), 1: this.$t('pages.serverLibrayWhiteListMode2') },
      portModeOptions: { 1: this.$t('pages.portModeOptions1'), 2: this.$t('pages.portModeOptions2'), 3: this.$t('pages.portModeOptions3') },
      loadTypeOptions: { 0: this.$t('pages.null'), 1: this.$t('pages.loadTypeOptions2'), 2: this.$t('pages.loadTypeOptions3') },
      oldProcessName: '',
      oldIpType: 1,
      dialogBrowserVisible: false,
      browserColModel: [
        { prop: 'name', label: 'browserName', width: '150' },
        { prop: 'processName', label: 'browserProcess', width: '150' }
      ],
      searchInfoName: this.$t('pages.serverName'),
      tableQuery: { opt: 0 },
      allBrowserList: [],
      browserDatas: [],
      tipMsg: '',
      createWhiteListFlag: false,
      updateFlag: false,
      needIds: [],
      addGroupAble: false,
      dialogHighConfigVisible: false,
      advancedConfig: {
        encAndDecCode: 0,
        prohibitUpload: 0,
        downloadType: 0,
        uploadType: 0,
        uploadFileExt: '*.*',
        downloadFileExt: '*.*',
        allOfUpload: true,
        allOfDownload: true
      },
      prohibitUpload: { key: 'prohibitUpload', isProp: false, label: this.$t('pages.prohibitUploadMsg'), value: 0 }, // 允许上网模式，是否禁止上传加密文件到非白名单网站
      showHighConfig: false,
      encAndDecCode: { key: 'serverWhitelistSecMode', isProp: false, value: 0 },
      propertyStg: {},
      propRuleId: undefined,
      propRuleDisabled: false,
      validRule: true,
      stgVisibleStatus: 'create'
    }
  },
  computed: {
    stgTargetDisabled() {
      return this.dialogStatus != 'createS' && this.dialogStatus != 'createP'
    }
  },
  watch: {
    tabLabel(val) {
      if (val === 'processTab') {
        this.processTypeFlag = false
      } else {
        this.processTypeFlag = true
      }
      this.stgName = this.isServerTab() ? this.$t('pages.service') : this.$t('pages.process')
      const label = this.isServerTab() ? this.$t('table.serverName') : this.$t('table.processName')
      this.colModel.splice(2, 1, Object.assign(this.colModel[2], { label: label }))
    },
    whiteListElgData(val) {
      (val || []).forEach(item => {
        item.groupName = this.getGroupNameByDataId(this.treeSelectNode, item.groupId)
      })
      this.handleWhiteListSearch()
    },
    needIds(val) {
      if (val.length > 0) {
        getServerLibraryByIds({ ids: val.join(',') }).then(res => {
          this.whiteListElgData = res.data || []
        })
      }
    },
    'whiteListTemp.limitNet'(newVal, oldVal) {
      this.browserDatas = [...this.allBrowserList]
      const tipList = []
      if (newVal == 1) {
        const partBrowserList = []
        this.browserDatas.forEach(item => {
          if (item.processName && (item.processName == 'iexplore.exe' || item.processName == 'chrome.exe' || item.processName == 'QQBrowser.exe' || item.processName == 'firefox.exe' || item.processName == 'TSBrowser.exe' || item.processName.startsWith('360') ||
          item.processName == 'msedge.exe' || item == '360chromeie.exe')) {
            partBrowserList.push(item)
          }
        })
        this.browserDatas.splice(0, this.browserDatas.length, ...partBrowserList)
        const processName = this.browserDatas.map(item => item.processName)
        this.whiteListTemp.processName.split('|').forEach(item => {
          if (!processName.includes(item) && item != '360chromeie.exe' && item != '') {
            tipList.push(item)
          }
        })
        if (tipList.length > 0) {
          this.tipMsg = this.$t('pages.serverLibrayWhiteListMsg', { process: tipList.join('|') })
        } else {
          this.tipMsg = ''
        }
      } else {
        this.tipMsg = ''
        // this.browserDatas = this.browserDatas.filter(item => item.processName != 'TSBrowser.exe')
        // const processName = this.browserDatas.map(item => item.processName)
        // this.whiteListTemp.processName.split('|').forEach(item => {
        //   if (!processName.includes(item) && item != '') {
        //     tipList.push(item)
        //   }
        // })
        // if (tipList.length > 0) {
        //   this.tipMsg = this.$t('pages.serverLibrayWhiteListMsg', { process: tipList.join('|') })
        // } else {
        //   this.tipMsg = ''
        // }
      }
    },
    'whiteListTemp.processName'(newVal, oldVal) {
      this.oldProcessName = oldVal
      if (this.whiteListTemp.limitNet == 1) {
        const tipList = []
        const processName = this.browserDatas.map(item => item.processName)
        newVal.split('|').forEach(item => {
          if (!processName.includes(item) && item != '360chromeie.exe' && item != '') {
            tipList.push(item)
          }
        })
        if (tipList.length > 0) {
          this.tipMsg = this.$t('pages.serverLibrayWhiteListMsg', { process: tipList.join('|') })
        } else {
          this.tipMsg = ''
        }
      } else {
        this.tipMsg = ''
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    this.tabLabel = this.$route.query.tabName || this.tabLabel
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.loadRelateDataTree()
    this.loadGroupTree()
    this.getSysBrowserPage()
    this.getShowHighConfig()
  },
  activated() {
    this.tabLabel = this.$route.query.tabName || this.tabLabel
    this.loadRelateDataTree()
    this.loadGroupTree()
    this.getShowHighConfig()
  },
  methods: {
    getServerLibraryByGroupIds,
    deleteWhiteListServer,
    createWhiteListServerGroup,
    updateWhiteListServerGroup,
    deleteWhiteListServerGroup,
    listGroupTree,
    countChildByGroupId,
    getGroupByName,
    moveGroup,
    deleteGroupAndData,
    moveGroupToOther,
    tabClick(pane, event) {
      this.handleFilter()
      this.deleteable = false
    },
    isServerTab() {
      return this.tabLabel === 'serverTab'
    },
    configRelateData() {
      this.$router.push('/system/baseData/serverLibrary')
    },
    gridTable() {
      const tableRef = this.isServerTab() ? 'serverList' : 'processList'
      return this.$refs[tableRef]
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    loadRelateDataTree: function() {
      getServerTreeNode().then(respond => {
        this.relateDataTreeData = [{ label: this.$t('pages.serverGroup'),
          dataId: '',
          children: [{ label: this.$t('pages.ungrouped'), dataId: '0', children: [], type: 'G' }],
          type: 'G'
        }]
        const groups = respond.data
        groups.forEach(group => {
          if (group.id.indexOf('G') > -1) {
            this.relateDataTreeData[0].children.push(group)
          } else {
            this.relateDataTreeData[0].children.forEach(child => {
              if (child.dataId == group.oriData.groupId) {
                child.children.push(group)
              }
            })
          }
        })
      })
    },
    receiverTreeNodeCheckChange: function(keys, datas) {
      keys.forEach(function(id, i) {
        if (id.indexOf('G') > -1) {
          keys.splice(i, 1)
        }
      })
      this.temp.receiverIds = keys
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, { page: 1 }, this.query, option)
      return this.isServerTab() ? getServerStgPage(searchQuery) : getProcessStgPage(searchQuery)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.propRuleDisabled = false
      this.propRuleId = undefined
      this.checkedTreeIds = []
    },
    handleDrag() {},
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable().getSelectedIds()
      const stgTypeNumber = this.tabLabel === 'serverTab' ? 62 : this.tabLabel === 'processTab' ? 85 : null
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: stgTypeNumber })
    },
    selectionChangeEnd1: function(rowDatas) {
      this.deleteable1 = rowDatas && rowDatas.length > 0
    },
    deleteProcessName() {
      const data = this.$refs.processList1.getSelectedDatas()
      this.nameTag = this.nameTag.filter(item1 => !data.some(item => item.id === item1.id))
    },
    handleCreate2() {
      this.stgTemp = Object.assign({}, this.stgDefaultTemp)
      this.stgVisibleStatus = 'create'
      this.stgVisible = true
      this.$nextTick(() => {
        this.$refs.stgForm.clearValidate()
      })
    },
    handleUpdate2(row) {
      this.stgTemp = Object.assign({}, JSON.parse(JSON.stringify(row)))
      this.stgVisibleStatus = 'update'
      this.stgVisible = true
    },
    importSuccess() {
      this.handleFilter()
    },
    async handleCreate() {
      const resp = await getPropertyByCode('server.encrypt-decrypt.stg')
      this.propertyStg = Object.assign({}, JSON.parse(resp.data.value))
      this.resetTemp()
      this.nameTag = []
      this.dialogStatus = this.isServerTab() ? 'createS' : 'createP'
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.whiteListElgData = []
      this.dialogFormVisible = true
      // this.$refs.relateDataTree && this.$refs.relateDataTree.clearFilter()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleUpdate(row) {
      const resp = await getPropertyByCode('server.encrypt-decrypt.stg')
      this.propertyStg = Object.assign({}, JSON.parse(resp.data.value))
      this.resetTemp()
      if (row.tabName !== undefined && row.tabName !== null) {
        this.tabLabel = this.tabName
      }
      this.dialogStatus = this.isServerTab() ? 'updateS' : 'updateP'
      this.temp = Object.assign(this.temp, JSON.parse(JSON.stringify(row)))
      if (row.popupConfig) {
        row.popupConfig.forEach(item => {
          if (item.id === 10001) {
            this.temp.unlawfulProcess = item.value
            if (item.value == 1) {
              this.propRuleDisabled = true
            }
          } else if (item.id === 10002) {
            this.temp.prohibitStgMerge = item.value
            if (item.value == 1) {
              this.propRuleDisabled = true
            }
          }
          // else {
          //   终端要求 去除违规响应规则的配置项，同时移除 策略表 关于定义的 OptionId == 10003 的配置项
          //   this.temp.isAlarm = item.value
          //   this.propRuleId = item.value
          // }
        })
      } else {
        // 不存在popupConfig则是旧策略，默认都不勾选
        this.propRuleId = undefined
      }
      if (!row.processConfig && !this.isServerTab()) {
        var data = []
        row.processNames.forEach((item, index) => {
          var pos = 0
          if (item.includes(';')) {
            const processNameTags = item.split(';')
            processNameTags.forEach(nameTag => {
              const obj = {
                id: pos,
                name: nameTag,
                controlCode: 0
              }
              data.push(obj)
              pos++
            })
          } else {
            const obj = {
              id: pos,
              name: item,
              controlCode: 0
            }
            data.push(obj)
            pos++
          }
        })
        this.temp.processConfig = [...data]
      }
      if (!this.isServerTab()) {
        this.nameTag = [...this.temp.processConfig]
      }
      this.temp.nameStr = this.temp.names.join(',')
      //  获取数据
      this.osType = row.osType
      this.whiteListElgData = []
      getServerLibraryByIds({ ids: (row.serverIds || []).join(',') }).then(res => {
        this.whiteListElgData = res.data || []
      })
      this.$nextTick(() => {
        this.$refs['whiteListTable'].clearSearchInfo();
        this.$refs['dataForm'].clearValidate()
      })
      this.dialogFormVisible = true
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable().getSelectedIds()
        const deleteFunc = this.isServerTab() ? deleteServerStg : deleteProcessStg
        deleteFunc({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable().deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    handleConfig() {
      this.dialogHighConfigVisible = true
      this.getConfigByKey()
      this.$nextTick(() => {
        this.$refs.advancedDataForm.clearValidate()
      })
    },
    formatSubmitParams: function() {
      this.temp.servers = undefined
      this.temp.processIds = undefined
      this.temp.names = undefined
      this.temp.popupConfig = undefined
      this.temp.processConfig = undefined
      if (this.isServerTab()) {
        this.temp.servers = []
        this.temp.serverIds = []
        this.temp.serverNames = []
        this.temp.popupConfig = []
        this.temp.popupConfig.push({ id: 10001, value: this.temp.unlawfulProcess, strValue: '' })
        this.temp.popupConfig.push({ id: 10002, value: this.temp.prohibitStgMerge, strValue: '' })
        // 终端要求 去除违规响应规则的配置项，同时移除 策略表 关于定义的 OptionId == 10003 的配置项
        // if (this.temp.isAlarm != 0 && this.propRuleId) {
        //   this.temp.popupConfig.push({ id: 10003, value: this.propRuleId, strValue: '' })
        // } else {
        //   this.temp.popupConfig.push({ id: 10003, value: 0, strValue: '' })
        // }
      } else {
        var names = []
        this.nameTag.forEach(item => {
          names.push(item.name)
        })
        this.temp.names = names
        this.temp.processNames = this.temp.names
        this.temp.processConfig = [...this.nameTag]
      }
      if (this.dialogStatus === 'createS' || this.dialogStatus === 'updateS') {
        this.getServerId(this.whiteListElgData)
      }
    },
    getServerId(data) {
      data.forEach(node => {
        if (this.isServerTab()) {
          this.temp.serverIds.push(node.id)
          this.temp.servers.push({ id: node.id, type: node.useNewVersion })
          this.temp.serverNames.push(node.name)
        }
      })
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    getEncRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    unlawfulProcessChange() {
      if (this.temp.unlawfulProcess == 0 && this.temp.prohibitStgMerge == 0) {
        this.propRuleId = undefined
        this.propRuleDisabled = false
        this.temp.isAlarm = 0
      } else {
        this.propRuleDisabled = true
      }
    },
    prohibitStgMergeChange() {
      if (this.temp.unlawfulProcess == 0 && this.temp.prohibitStgMerge == 0) {
        this.propRuleId = undefined
        this.propRuleDisabled = false
        this.temp.isAlarm = 0
      } else {
        this.propRuleDisabled = true
      }
    },
    createData2() {
      this.$refs['stgForm'].validate((valid) => {
        if (valid) {
          const processData = {
            id: Date.now(),
            name: this.stgTemp.name,
            controlCode: this.stgTemp.controlCode
          }
          var flag = false
          this.nameTag.forEach(item => {
            if (item.name === processData.name) {
              flag = true
            }
          })
          if (flag) {
            this.$message({
              title: this.$t('text.error'),
              message: this.$t('pages.PrintSet_Msg30'),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.nameTag.push(processData)
          this.stgVisible = false
        }
      })
    },
    updateData2() {
      const id = this.stgTemp.id
      for (const processData of this.nameTag) {
        if (processData.id === id) {
          processData.name = this.stgTemp.name
          processData.controlCode = this.stgTemp.controlCode
          break
        }
      }
      this.stgVisible = false
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      // if ((this.temp.unlawfulProcess === 1 || this.temp.prohibitStgMerge === 1) && this.temp.isAlarm === 0) {
      //   this.$message({ title: this.$t('text.error'), message: this.$t('pages.serverProcess_Msg23'), type: 'error', duration: 2000 })
      //   return
      // }
      if (this.nameTag.length === 0 && !this.isServerTab()) {
        this.$message({ title: this.$t('text.error'), message: this.$t('pages.serverProcess_Msg25'), type: 'error', duration: 2000 })
        return
      }
      // if (this.temp.isAlarm != 0 && !this.propRuleId) {
      //   this.validRule = false
      // } else {
      //   this.validRule = true
      // }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRule) {
          this.formatSubmitParams()
          const createFunc = this.isServerTab() ? createServerStg : createProcessStg
          createFunc(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable().execRowDataApi()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      // if ((this.temp.unlawfulProcess === 1 || this.temp.prohibitStgMerge === 1) && this.temp.isAlarm === 0) {
      //   this.$message({ title: this.$t('text.error'), message: this.$t('pages.serverProcess_Msg23'), type: 'error', duration: 2000 })
      //   return
      // }
      if (this.nameTag.length === 0 && !this.isServerTab()) {
        this.$message({ title: this.$t('text.error'), message: this.$t('pages.serverProcess_Msg25'), type: 'error', duration: 2000 })
        return
      }
      // if (this.temp.isAlarm != 0 && !this.propRuleId) {
      //   this.validRule = false
      // } else {
      //   this.validRule = true
      // }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRule) {
          this.formatSubmitParams()
          const updateFunc = this.isServerTab() ? updateServerStg : updateProcessStg
          updateFunc(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable().execRowDataApi()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    stateFormatter: function(row, data) {
      return this.stateOptions[data]
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    typeFormatter: function(row) {
      if (!row.controlCode || row.controlCode === 0) {
        return this.$t('pages.serverProcess_Msg14')
      } else {
        return this.$t('pages.serverProcess_Msg13')
      }
    },
    stgInfoFormatter: function(row, data) {
      let result = ''
      if (row.names) {
        row.names.forEach(name => {
          if (result) {
            result += ', ' + name
          } else {
            result = name
          }
        })
      }
      return result
    },
    nameValidator(rule, value, callback) {
      const getByNameFunc = this.isServerTab() ? getServerStgByName : getProcessStgByName
      getByNameFunc({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    urlNameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getWhiteListServerByName({ name: value }).then(respond => {
          const timeInfo = respond.data
          if (timeInfo && timeInfo.id != this.whiteListTemp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    portValidator(rule, value, callback) {
      if (isNaN(Number(value)) || Number(value) != parseInt(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text5')))
      }
      if (value < 1 || value > 65535) {
        callback(new Error(this.$t('pages.serverLibrary_text6')))
      } else {
        const beginPort = Number(this.whiteListTemp.beginPort)
        const endPort = Number(this.whiteListTemp.endPort)
        if (beginPort && endPort) {
          if (beginPort > endPort) {
            callback(new Error(this.$t('pages.serverLibrary_text7')))
          } else {
            this.$refs['whiteListDataForm'].clearValidate(['beginPort', 'endPort'])
            callback()
          }
        } else {
          callback()
        }
      }
    },
    useNewVersionFormatter: function(row, data) {
      return this.useNewVersionOptions[data]
    },
    modelFormatter: function(row, data) {
      if (row.useNewVersion === 1) {
        return this.$t('pages.serverLibrayWhiteListMode1')
      } else if (row.useNewVersion === 2) {
        return this.$t('pages.serverLibrayWhiteListMode2')
      }
    },
    serverGroupNameFormatter: function(row, data) {
      const groupId = row.groupId;
      if (groupId === 0) {
        return this.$t('pages.ungrouped')
      } else {
        return this.getGroupNameByDataId(this.treeSelectNode, data)
      }
    },
    ipTypeFormatter: function(row, data) {
      return this.ipTypeOptionMap[data]
    },
    loadTypeFormatter: function(row, data) {
      return this.loadTypeOptions[data]
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId, nodeKey) {
      nodeKey = nodeKey || 'dataId'
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData[nodeKey] == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId, nodeKey)
          if (result) return result
        }
      }
      return ''
    },
    handleWhiteListCreateImport() {
      this.handleWhiteListCreate();
      this.addGroupAble = true
    },
    //  新增，修改弹窗
    async handleWhiteListCreate(selectedGroupId, flag) {
      this.addGroupAble = false
      this.whiteListDialogStatus = 'createWhiteList'
      this.whiteListTemp = Object.assign({}, this.whiteListDefaultTemp)
      this.whiteListTemp.groupId = selectedGroupId ? selectedGroupId + '' : null

      this.whiteListTemp.uploadType = this.propertyStg.uploadType
      this.whiteListTemp.uploadFileExt = this.propertyStg.uploadFileExt
      if (this.propertyStg.uploadFileExt === '*.*') {
        this.whiteListTemp.allOfUpload = true
      } else {
        this.whiteListTemp.allOfUpload = false
      }
      this.whiteListTemp.downloadType = this.propertyStg.downloadType
      this.whiteListTemp.downloadFileExt = this.propertyStg.downloadFileExt
      if (this.propertyStg.downloadFileExt === '*.*') {
        this.whiteListTemp.allOfDownload = true
      } else {
        this.whiteListTemp.allOfDownload = false
      }

      this.whiteListDialogFormVisible = true
      this.createWhiteListFlag = flag || false
      this.$nextTick(() => {
        this.$refs['whiteListDataForm'].clearValidate()
      })
    },
    encAndDecChange() {
      if (this.whiteListTemp.encDecType === 0) {
        this.whiteListTemp.uploadType = 0
        this.whiteListTemp.uploadFileExt = '*.*'
        this.whiteListTemp.downloadType = 0
        this.whiteListTemp.downloadFileExt = '*.*'
        this.whiteListTemp.allOfUpload = true
        this.whiteListTemp.allOfDownload = true
        this.whiteListTemp.allOfUploadAble = false
        this.whiteListTemp.allOfDownloadAble = false
      } else {
        this.whiteListTemp.uploadType = this.propertyStg.uploadType
        this.whiteListTemp.uploadFileExt = this.propertyStg.uploadFileExt
        if (this.propertyStg.uploadFileExt === '*.*') {
          this.whiteListTemp.allOfUpload = true
        } else {
          this.whiteListTemp.allOfUpload = false
        }
        this.whiteListTemp.downloadType = this.propertyStg.downloadType
        this.whiteListTemp.downloadFileExt = this.propertyStg.downloadFileExt
        if (this.propertyStg.downloadFileExt === '*.*') {
          this.whiteListTemp.allOfDownload = true
        } else {
          this.whiteListTemp.allOfDownload = false
        }
        this.whiteListTemp.allOfUploadAble = true
        this.whiteListTemp.allOfDownloadAble = true
      }
    },
    handleWhiteListDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs['whiteListTable'].getSelectedIds() || []
        this.whiteListElgData = this.$refs['whiteListTable'].deleteTableData(this.whiteListElgData, toDeleteIds)
      }).catch(() => {})
    },
    handleWhiteListSearch(searchInfo) {
      searchInfo = searchInfo || ''
      if (searchInfo === '') {
        this.tempWhiteListElgData = this.whiteListElgData
      } else {
        //  条件查询
        this.tempWhiteListElgData = this.whiteListElgData.filter(item => {
          return (item.name && item.name.toLowerCase().indexOf(searchInfo) !== -1) ||
            (item.groupName && item.groupName.toLowerCase().indexOf(searchInfo) !== -1)
        })
      }
    },
    handleWhiteListImport() {
      this.$refs['whiteListImportTable'].show();
    },
    createWhiteList() {
      this.submitting = true
      this.$refs['whiteListDataForm'].validate((valid) => {
        if (valid) {
          this.formatterData()
          this.whiteListTemp.groupName = findNodeLabel(this.treeSelectNode, this.whiteListTemp.groupId, 'dataId')
          createWhiteListServer(this.whiteListTemp).then(respond => {
            this.submitting = false
            this.isImportElg(respond.data, 'create')
            this.whiteListDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createWhiteListFlag) {
          this.$refs['whiteListImportTable'].refreshTableData()
        } else {
          this.whiteListElgData.push(data)
        }
        this.createWhiteListFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$refs['whiteListImportTable'].refreshTableData()
        } else {
          for (let i = 0; i < this.whiteListElgData.length; i++) {
            if (this.whiteListElgData[i].id === data.id) {
              this.whiteListElgData.splice(i, 1)
              this.whiteListElgData.push(data)
              break
            }
          }
        }
        this.updateFlag = false
      }
    },
    updateWhiteList() {
      this.submitting = true
      this.$refs['whiteListDataForm'].validate((valid) => {
        if (valid) {
          this.formatterData()
          const tempData = Object.assign({}, this.whiteListTemp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.whiteListTemp.groupId, 'dataId')
          updateWhiteListServer(tempData).then(respond => {
            this.submitting = false
            this.isImportElg(respond.data, 'update')
            this.whiteListDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    whiteListSelectable(row, index) {
      return selectable(row, index);
    },
    //  以下方法是点击导入按钮的弹窗方法
    //  导入方法的row-data-api
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.type = undefined
      return getWhiteListServerPage(searchQuery)
    },
    getNeedAddIds(needIds) {
      this.loadGroupTree()
      this.needIds = needIds
    },
    //  删除记录
    submitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      this.whiteListElgData = this.whiteListElgData.filter(item => { return deleteIdsList.filter(i => i === item.id).length === 0 })
    },
    elgCancelAfter() {
      this.loadGroupTree();
      this.getWhiteListListByIds();
    },
    //  弹窗分组的数据发送变化时
    changeGroupAfter() {
      this.loadGroupTree()
    },
    changeOpt(val) {
      this.searchInfoName = val == 0 ? this.$t('pages.serverName') : this.$t('table.processName')
    },
    resetWhiteTemp() {
      this.whiteListTemp = Object.assign({}, this.whiteListDefaultTemp)
      this.oldProcessName = ''
      this.oldIpType = 1
    },
    async handleWhiteListUpdateBase(row) {
      this.addGroupAble = true
      this.resetWhiteTemp()
      this.whiteListDialogStatus = 'updateWhiteList'
      this.whiteListTemp = Object.assign({}, this.whiteListTemp, row)
      this.oldIpType = row.ipType
      this.whiteListTemp.groupId += ''
      this.whiteListTemp.allOfUpload = this.whiteListTemp.uploadFileExt === '*.*'
      this.whiteListTemp.allOfDownload = this.whiteListTemp.downloadFileExt === '*.*'
      if (this.whiteListTemp.useNewVersion == 1) {
        this.whiteListTemp.limitNet = 0
        if (row.beginPort === row.endPort) {
          this.whiteListTemp.portMode = 1
          this.whiteListTemp.port = this.whiteListTemp.beginPort
        } else if (row.beginPort === 1 && row.endPort === 65535) {
          this.whiteListTemp.portMode = 3
        } else {
          this.whiteListTemp.portMode = 2
        }
        if (this.whiteListTemp.ipType == 1) {
          this.whiteListTemp.domainName = this.whiteListTemp.beginIp
        } else {
          this.whiteListTemp.ip = this.whiteListTemp.beginIp
        }
      } else if (this.whiteListTemp.useNewVersion == 2) {
        this.whiteListTemp.limitNet = 1
        if (row.beginPort === row.endPort) {
          this.whiteListTemp.portMode = 1
          this.whiteListTemp.port = this.whiteListTemp.beginPort
        } else if (row.beginPort === 1 && row.endPort === 65535) {
          this.whiteListTemp.portMode = 3
        } else {
          this.whiteListTemp.portMode = 2
        }
        if (this.whiteListTemp.ipType == 1) {
          this.whiteListTemp.domainName = this.whiteListTemp.beginIp
        } else {
          this.whiteListTemp.ip = this.whiteListTemp.beginIp
        }
      } else {
        if (this.whiteListTemp.ipType == 1) {
          this.whiteListTemp.domainName = this.whiteListTemp.beginIp
          this.whiteListTemp.beginIp = this.whiteListTemp.endIp = ''
        }
      }
      this.whiteListTemp.groupId = this.whiteListTemp.groupId ? this.whiteListTemp.groupId + '' : '';
      if (row.encDecType === 1) {
        this.whiteListTemp.allOfUploadAble = true
        this.whiteListTemp.allOfDownloadAble = true
      } else {
        this.whiteListTemp.allOfUploadAble = false
        this.whiteListTemp.allOfDownloadAble = false
      }
      this.whiteListDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['whiteListDataForm'].clearValidate()
      })
    },
    handleWhiteListUpdate(row) {
      this.updateFlag = true
      this.handleWhiteListUpdateBase(row)
      this.addGroupAble = false
    },
    loadGroupTree: function() {
      return this.listGroupTreeData().then(res => {
        this.treeSelectNode = res.data || []
      })
    },
    listGroupTreeData() {
      const groupArray = [{ label: this.$t('pages.ungrouped'), dataId: '0', groupName: this.$t('pages.ungrouped') }]
      const result = listGroupTree();
      result.then(respond => {
        respond.data.forEach(el => {
          el.groupName = el.label
          groupArray.push(el)
        })
        respond.data = groupArray
      })
      return result;
    },
    getWhiteListListByIds() {
      const ids = this.$refs['whiteListTable'].getIdsByList(this.whiteListElgData) || []
      if (ids.length === 0) {
        this.whiteListElgData = []
        return;
      }
      getServerLibraryByIds({ ids: ids.join(',') }).then(res => {
        this.whiteListElgData = res.data || []
      })
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    //  新增，修改弹窗方法
    versionChange(val) {
      this.whiteListTemp.limitNet = 0
      if (val) {
        this.whiteListTemp.processName = this.oldProcessName
      } else {
        this.whiteListTemp.processName = '*.*'
        this.whiteListTemp.upLoadLimitSpeed = 0
      }
      this.$nextTick(() => {
        this.$refs['whiteListDataForm'].clearValidate()
      })
    },
    ipTypeChange(val) {
      this.$refs['whiteListDataForm'].clearValidate()
      this.whiteListTemp.ip = ''
      this.whiteListTemp.beginIp = ''
      this.whiteListTemp.endIp = ''
      this.whiteListTemp.domainName = ''
    },
    limitNetTypeChange(val) {
      this.$refs['whiteListDataForm'].clearValidate()
      if (val == 0) {
        this.whiteListTemp.ipType = this.oldIpType
      } else if (val == 1) {
        this.whiteListTemp.ipType = 1
      }
    },
    portModeChange(val) {
      this.$refs['whiteListDataForm'].clearValidate()
    },
    downloadFileExtChange(val) {
      this.whiteListTemp.downloadFileExt = val ? '*.*' : this.whiteListTemp.downloadFileExt
    },
    uploadFileExtChange(val) {
      this.whiteListTemp.uploadFileExt = val ? '*.*' : this.whiteListTemp.uploadFileExt
    },
    advancedConfigUploadFileExtChange(val) {
      this.advancedConfig.uploadFileExt = val ? '*.*' : this.advancedConfig.uploadFileExt
      this.$refs.advancedDataForm.clearValidate('uploadFileExt')
    },
    advancedConfigDownloadFileExtChange(val) {
      this.advancedConfig.downloadFileExt = val ? '*.*' : this.advancedConfig.downloadFileExt
      this.$refs.advancedDataForm.clearValidate('downloadFileExt')
    },
    flowNumber(field) {
      if (!(this.whiteListTemp[field] === '-' || this.whiteListTemp[field] === '-1')) {
        if (this.whiteListTemp[field].startsWith('-1')) {
          this.whiteListTemp[field] = '-1'
        } else {
          this.whiteListTemp[field] = this.whiteListTemp[field].replace(/[^\d]/g, '')
        }
      }
    },
    browserRowDataApi: function(option) {
      const searchQuery = Object.assign({}, { page: 1 }, option)
      return getSysBrowserPage(searchQuery)
    },
    getShowHighConfig() {
      getConfigByKey({ key: 'hidden.serverWhite.config' }).then(resp => {
        if (resp.data) {
          this.showHighConfig = JSON.parse(resp.data.value)
        }
      })
    },
    browserTable() {
      return this.$refs['browserList']
    },
    getSysBrowserPage() {
      const searchQuery = Object.assign({}, { page: 1 })
      getSysBrowserPage(searchQuery).then(res => {
        this.allBrowserList = [...res.data.items]
        this.browserDatas = [...res.data.items].filter(item => item.processName != 'TSBrowser.exe')
      })
    },
    addBrowser() {
      const selectedDatas = this.browserTable().getSelectedDatas()
      const processName = this.whiteListTemp.processName
      // 字符串转成数组，并去除空值
      const processArr = processName ? processName.split('|').filter(el => el && el.trim()) : []
      const processNames = selectedDatas.map(data => data.processName)
      if ((processNames.indexOf('360ChromeX.exe') > -1 || processNames.indexOf('360chrome.exe') > -1 || processNames.indexOf('360SE.exe') > -1 ||
      processArr.indexOf('360ChromeX.exe') > -1 || processArr.indexOf('360chrome.exe') > -1 || processArr.indexOf('360SE.exe') > -1) && processArr.indexOf('360chromeie.exe') == -1) {
        processArr.push('360chromeie.exe')
      }
      // 添加选中的数据，并使用Set过滤重复数据
      this.whiteListTemp.processName = Array.from(new Set([...processArr, ...selectedDatas.map(data => data.processName)])).join('|')
      this.browserTable().clearSelection()
      this.dialogBrowserVisible = false
    },
    getConfigByKey() {
      getConfigByKey({ key: 'prohibitUpload' }).then(resp => {
        if (resp.data) {
          this.advancedConfig.prohibitUpload = parseInt(resp.data.value)
        }
      })
      getConfigByKey({ key: 'serverWhitelistSecMode' }).then(resp => {
        if (resp.data) {
          this.advancedConfig.encAndDecCode = parseInt(resp.data.value)
        } else {
          this.advancedConfig.encAndDecCode = 0
        }
      })
      getPropertyByCode('server.encrypt-decrypt.stg').then(resp => {
        var defaultStg = JSON.parse(resp.data.value)
        this.advancedConfig.uploadType = defaultStg.uploadType
        defaultStg.uploadFileExt === '*.*' ? this.advancedConfig.allOfUpload = true : this.advancedConfig.allOfUpload = false
        this.advancedConfig.uploadFileExt = defaultStg.uploadFileExt
        this.advancedConfig.downloadType = defaultStg.downloadType
        defaultStg.downloadFileExt === '*.*' ? this.advancedConfig.allOfDownload = true : this.advancedConfig.allOfDownload = false
        this.advancedConfig.downloadFileExt = defaultStg.downloadFileExt
      })
    },
    async saveConfig() {
      this.prohibitUpload.value = this.advancedConfig.prohibitUpload
      const data = [this.prohibitUpload]
      this.encAndDecCode.value = this.advancedConfig.encAndDecCode
      data.push(this.encAndDecCode)
      var resp = await getPropertyByCode('server.encrypt-decrypt.stg')
      var propertyStg = Object.assign({}, resp.data)
      const propertyValue = '{"uploadType":' + this.advancedConfig.uploadType + ',"uploadFileExt":' + '"' + this.advancedConfig.uploadFileExt + '",' + '"downloadType":' + this.advancedConfig.downloadType + ',"downloadFileExt":' + '"' + this.advancedConfig.downloadFileExt + '"}'
      propertyStg.value = propertyValue
      this.$refs['advancedDataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const obj = {
            key: propertyStg.code,
            type: propertyStg.type,
            value: propertyStg.value,
            isProp: true
          }
          data.push(obj)
          updateConfig(data).then(result => {
            updateDefaultEncStgServer().then(res => {
              this.submitting = false
              this.dialogHighConfigVisible = false
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            }).catch(e => {
              this.submitting = false
            })
          }).catch(e => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    ipValidator(rule, value, callback) {
      if (this.whiteListTemp.ipType == 0) {
        if (value && isIPv4(value)) {
          if (this.whiteListTemp.useNewVersion === 0 && this.whiteListTemp.beginIp && this.whiteListTemp.endIp) {
            const temp1 = this.whiteListTemp.beginIp.split('.')
            const temp2 = this.whiteListTemp.endIp.split('.')
            let flag = false
            for (var i = 0; i < 4; i++) {
              if (temp1[i] - temp2[i] == 0) {
                continue
              } else if (temp1[i] - temp2[i] > 0) {
                flag = true
              }
              break
            }
            if (flag) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      } else {
        if (value && isIPv6(value)) {
          if (this.whiteListTemp.useNewVersion === 0 && this.whiteListTemp.beginIp && this.whiteListTemp.endIp) {
            const fullbeginIpv6 = this.getFullIPv6(this.whiteListTemp.beginIp)
            const fullendIpv6 = this.getFullIPv6(this.whiteListTemp.endIp)
            if (fullbeginIpv6 > fullendIpv6) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      }
    },
    getFullIPv6(src) {
      const temp = src.replace('::', '*')
      const secs = temp.split(':')
      let ret = ''
      for (let i = 0; i < secs.length; i++) {
        const t = secs[i]
        if (t.indexOf('*') >= 0) {
          const ss = t.split('*')
          ret += ss[0].padStart(4, '0')
          for (let j = 1; j < (8 - secs.length); j++) {
            ret += '0000'
          }
          ret += ss[1].padStart(4, '0');
        } else {
          ret += t.padStart(4, '0');
        }
      }
      return ret.toLocaleLowerCase();
    },
    formatterData() {
      if (this.whiteListTemp.useNewVersion === 0) {
        this.whiteListTemp.limitNet = null
      }
      if (this.whiteListTemp.limitNet === 1) {
        this.whiteListTemp.useNewVersion = 2
      } else if (this.whiteListTemp.limitNet === 0) {
        this.whiteListTemp.useNewVersion = 1
      }
      if (this.whiteListTemp.useNewVersion === 1 || this.whiteListTemp.useNewVersion === 2) {
        this.whiteListTemp.beginIp = this.whiteListTemp.endIp = this.whiteListTemp.ip
        if (this.whiteListTemp.portMode === 1) {
          this.whiteListTemp.beginPort = this.whiteListTemp.endPort = this.whiteListTemp.port
        } else if (this.whiteListTemp.portMode === 3) {
          this.whiteListTemp.beginPort = 1
          this.whiteListTemp.endPort = 65535
        }
      }
      if (this.whiteListTemp.ipType == 1) {
        this.whiteListTemp.beginIp = this.whiteListTemp.endIp = this.whiteListTemp.domainName
      }
      if (this.whiteListTemp.limitNet == 1 && this.whiteListTemp.upLoadLimitSpeed != null) {
        this.whiteListTemp.upLoadLimitSpeed = null
      }
      if (this.whiteListTemp.encDecType === 1) {
        // 使用默认加解密策略
        this.whiteListTemp.uploadType = this.propertyStg.uploadType
        this.whiteListTemp.uploadFileExt = this.propertyStg.uploadFileExt
        this.whiteListTemp.downloadType = this.propertyStg.downloadType
        this.whiteListTemp.downloadFileExt = this.propertyStg.downloadFileExt
      }
    },
    processNameValidator(rule, value, callback) {
      if (this.whiteListTemp.useNewVersion == 1) {
        value.indexOf('*.*') > -1 ? callback(this.$t('pages.serverLibrary_text8')) : callback()
      }
      callback()
    },
    //  添加分组事件
    handleTypeCreate() {
      this.$refs['createGroupDlg'].handleCreate();
    },
    //  新分组添加完成后事件
    createGroupAddEnd(row) {
      this.loadGroupTree().then(() => {
        this.whiteListTemp.groupId = row.id + ''
      })
    },
    handleInput(value) {
      if (isNaN(Number(value)) || Number(value) !== parseInt(value)) {
        this.whiteListTemp.port = undefined
      } else if (value.includes('.')) {
        this.whiteListTemp.port = value.split('.')[0]
      } else {
        this.whiteListTemp.port = value
      }
    },
    handleBeginInput(value) {
      if (isNaN(Number(value)) || Number(value) !== parseInt(value)) {
        this.whiteListTemp.beginPort = undefined
      } else if (value.includes('.')) {
        this.whiteListTemp.beginPort = value.split('.')[0]
      } else {
        this.whiteListTemp.beginPort = value
      }
    },
    handleEndInput(value) {
      if (isNaN(Number(value)) || Number(value) !== parseInt(value)) {
        this.whiteListTemp.endPort = undefined
      } else if (value.includes('.')) {
        this.whiteListTemp.endPort = value.split('.')[0]
      } else {
        this.whiteListTemp.endPort = value
      }
    },
    inputBlur(validateProp) {
      this.$refs['whiteListDataForm'].validateField(validateProp)
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
</style>
