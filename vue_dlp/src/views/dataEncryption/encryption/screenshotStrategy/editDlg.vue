<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.screenshotStg')"
      :stg-code="69"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowDataImport"
      :format-form-data="formatFormDataImport"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <el-checkbox-group v-model="temp.checkedCode" @change="checkChange">
          <div v-for="item in screenshotOptions" :key="item.key">
            <FormItem>
              <el-checkbox :key="item.key" :label="item.key" :disabled="!formable">
                {{ item.value }}
              </el-checkbox>
            </FormItem>
            <FormItem
              v-if="item.processType"
              v-show="temp.checkedCode.indexOf(item.key) > -1"
              :label="item.key === 1 ? $t('table.exceptScreenshotProcess') : item.key === 4 ? $t('table.exceptScreenshotProcess') : item.key === 8 ? $t('table.exceptControlProcess') : item.key === 16 ? $t('pages.appProcess') : ''"
              label-width="120px"
              :prop="item.prop"
            >
              <el-row>
                <el-col :span="20">
                  <FormItem>
                    <tag :key="item.processType" v-model="temp.processName[item.processType]" :border="true" :list="temp.processName[item.processType]" :overflow-able="true" max-height="150px" :disabled="!formable" min-height="40px" @tagChange="tagChange"/>
                  </FormItem>
                </el-col>
                <el-col :span="4">
                  <el-button :disabled="!formable" type="primary" size="mini" style="height: 40px; text-align: center;margin-left: 2px" @click="showAppSelectDlg(item.processType)">
                    {{ $t('button.import') }}
                  </el-button>
                </el-col>
              </el-row>
            </FormItem>
          </div>
        </el-checkbox-group>
      </template>
    </stg-dialog>
    <!-- 应用程序库 -->
    <app-select-dlg ref="appLib" :append-to-body="true" @select="appendFile"/>
  </div>
</template>

<script>
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/dataEncryption/encryption/screenshotStrategy'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'

export default {
  name: 'ScreenshotStgDlg',
  components: { AppSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        code: 0,
        process: [],
        checkedCode: [],
        processName: {},
        forbidCopyScreen: null,
        entityType: undefined,
        entityId: undefined
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        // 指定进程窗口-应用程序验证规则
        specified: [{ required: true, validator: this.specifiedValidator, trigger: 'blur' }]
      },
      screenshotOptions: [],
      screenshotOptionsMap: {
        1: [
          { key: 1, value: this.$t('pages.screenshotOptions1'), processType: 1 },
          { key: 2, value: this.$t('pages.screenshotOptions2') },
          { key: 4, value: this.$t('pages.screenshotOptions3'), processType: 2 },
          { key: 8, value: this.$t('pages.screenshotOptions6'), processType: 3 },
          { key: 16, value: this.$t('pages.screenshotOptions5'), processType: 4, prop: 'specified' },
          { key: 10001, value: this.$t('pages.screenshotOptions7') } //  禁止复制屏幕
        ],
        2: [
          { key: 1, value: this.$t('pages.screenshotOptions1'), processType: 1 },
          { key: 4, value: this.$t('pages.screenshotOptions3'), processType: 2 }
        ],
        4: [
          { key: 1, value: this.$t('pages.screenshotOptions1') },
          { key: 4, value: this.$t('pages.screenshotOptions3') }
        ]
      },
      slotName: 1,
      processType: null
    }
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
    this.screenshotOptions = this.getScreenshotOptions(this.slotName)
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.screenshotOptions = this.getScreenshotOptions(name)
      this.temp.checkedCode = this.screenshotOptions.filter(opt => {
        return (slotTemp.code & opt.key) && opt.key !== 10001
      }).map(opt => opt.key)
      if (this.temp.forbidCopyScreen) {
        //  禁止复制屏幕
        this.temp.checkedCode.push(10001);
      }
      this.checkChange(this.temp.checkedCode)
    },
    checkChange(val) {
      const options = this.getScreenshotOptions(this.slotName)
      const list = []
      options.forEach(item => {
        if (val.includes(item.key)) {
          list.push(item.processType)
        }
      })
      if (list.length > 0) {
        list.forEach(processType => {
          const processNames = this.temp.processName[processType]
          if (processNames === undefined || processNames === null) {
            this.$set(this.temp.processName, processType, [])
          }
        })
      }
      // 禁止截屏与不禁止截屏，勾选其中一项，另一项需要取消勾选
      const valMap = { 1: 4, 4: 1 }
      const deleteValue = valMap[[...val].pop()]
      this.temp.checkedCode = val.filter(v => v != deleteValue)
      // 过滤掉 禁止复制屏幕（key=10001)
      let checkedCode = [...this.temp.checkedCode]
      this.temp.forbidCopyScreen = checkedCode.includes(10001) ? 1 : 0;
      checkedCode = checkedCode.filter(code => { return ![10001].includes(code); });
      this.temp.code = this.getSum(checkedCode)
    },
    // 获取不同终端类型的checkbox选项
    getScreenshotOptions(osType) {
      return this.screenshotOptionsMap[osType]
    },
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.$refs['stgDlg'].show(row, this.formable)
    },
    handleShow(row, isGenerateStrategy) {
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    // 数据渲染到页面时，将行数据格式化成表单的数据
    formatRowDataImport(rowData) {
      if (rowData.process) {
        rowData.process.forEach(item => {
          if (rowData.processName[item.type] === undefined || rowData.processName[item.type] === null) {
            this.$set(rowData.processName, item.type, [])
          }
          //  过滤重复的
          if (rowData.processName[item.type].findIndex(it => { return it === item.name }) === -1) {
            rowData.processName[item.type].push(item.name)
          }
        })
      }
      rowData.checkedCode = this.screenshotOptions.filter(opt => {
        return (rowData.code & opt.key) && opt.key !== 10001
      }).map(opt => opt.key)
      //  禁止复制屏幕
      if (rowData.forbidCopyScreen) {
        rowData.checkedCode.push(10001)
      }
    },
    // 提交数据前，对表单数据进行处理
    formatFormDataImport(formData) {
      const process = []
      this.getScreenshotOptions(formData.osType).forEach(op => {
        if (formData.checkedCode.indexOf(op.key) > -1) {
          const procNames = formData.processName[op.processType] || []
          if (procNames.length > 0) {
            procNames.forEach(name => {
              name && process.push({ type: op.processType, name: name })
            })
          }
        }
      })
      formData.process = process
    },
    specifiedValidator(rule, value, callback) {
      const temp = this.temp
      // 指定进程窗口选项
      if (temp.osType == 1 && temp.checkedCode.indexOf(16) > -1) {
        if (temp.processName[4] && temp.processName[4].length === 0) {
          callback(this.$t('text.cantNull'))
        }
      }
      callback()
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    // 去除掉空格，换行等数据
    tansf(e) {
      return e ? e.replace(/\r\n/g, '').replace(/\n/g, '').replace(/\s/g, '').replace(/\t/g, '') : '' // 去空格
    },
    tagChange(list, type) {
      this.filterRepetitionData(list);
    },
    //  过滤重复数据
    filterRepetitionData(list) {
      const tList = []
      list.forEach(item => {
        if (!tList.includes(item)) {
          tList.push(item)
        }
      })
    },
    showAppSelectDlg(processType) {
      this.processType = processType
      this.$refs['appLib'].show()
    },
    appendFile(softs) {
      softs = softs || []
      if (this.temp.processName[this.processType] === undefined) {
        this.$set(this.temp.processName, this.processType, [])
        this.temp.processName[this.processType] = []
      }
      softs.forEach(item => {
        if (!this.temp.processName[this.processType].includes(item.processName)) {
          this.temp.processName[this.processType].push(item.processName)
        }
      })
      this.processType = null
    }
  }
}
</script>
