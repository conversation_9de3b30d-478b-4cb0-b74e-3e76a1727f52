<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="appList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('components.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit />
        </FormItem>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('components.enable')">
              <el-switch v-model="temp.active" :disabled="!formable" />
            </FormItem>
          </el-col>
          <el-col v-if="!!formable" :span="12">
            <el-button size="small" style="float: right;" @click="clearAllData">
              {{ $t('pages.clearCurrentConfiguration') }}
            </el-button>
          </el-col>
        </el-row>
      </Form>
      <el-card class="box-card" :body-style="{'padding': '0'}">
        <div slot="header" style="padding: 5px 0;">
          <span>{{ $t('pages.accessServer') }}</span>
          <span style="color:red">{{ ipValidMsg }}</span>
          <div class="btn-box">
            <el-button size="small" :disabled="!formable" @click="createIp">{{ $t('button.insert') }}</el-button>
            <el-button size="small" :disabled="!ipDeleteable" @click="deleteIp">{{ $t('button.delete') }}</el-button>
          </div>
        </div>
        <grid-table
          ref="ipList"
          :height="230"
          :multi-select="true"
          :selectable="selectable"
          :show-pager="false"
          :col-model="ipColModel"
          :row-datas="ipRowData"
          @selectionChangeEnd="(rowDatas) => { ipDeleteable = rowDatas.length > 0 }"
        />
      </el-card>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importApplySecurityAccessStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="false"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { getApplySecurityAccessList, createApplySecurityAccess, updateApplySecurityAccess, deleteApplySecurityAccess } from '@/api/dataEncryption/encryption/applySecurityAccess'
import { refreshPage, hiddenActiveAndEntity, enableStgBtn, entityLink, objectFormatter, buttonFormatter } from '@/utils'
import { isPort1 } from '@/utils/validate'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'ApplySecurityAccess',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 79,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', iconFormatter: stgActiveIconFormatter, sort: true },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'Data', label: 'stgMessage', width: '200', formatter: this.formatStrategy },
        { prop: 'remark', label: 'remark', width: '100', sort: true },
        {
          label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      ipColModel: [
        { prop: 'serverIp', label: 'serverIp', type: 'input', width: '150', disabled: !this.formable },
        { prop: 'tcpPort', label: 'port', type: 'input', width: '150', disabled: !this.formable }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      deleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        servers: []
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.appSecurityAccessStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.appSecurityAccessStg'), 'create')
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateStgName'), trigger: 'blur' }]
      },
      editable: true,
      showTree: true,
      treeable: true,
      addBtnAble: false,
      ipDeleteable: false,
      ipRowData: [],
      ipValidMsg: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs['appList']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      if (this.formable == false) {
        return false
      } else {
        return true
      }
    },
    clearAllData() {
      this.$confirmBox(this.$t('pages.validateClearCurrentConfiguration'), this.$t('text.prompt')).then(() => {
        this.resetTemp()
      }).catch(() => {})
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    refresh() {
      return refreshPage(this)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getApplySecurityAccessList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.ipRowData = []
      this.ipValidMsg = ''
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.ipRowData = []
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.ipRowData = []
      this.temp = Object.assign({}, row)
      if (row.servers) {
        row.servers.forEach((item, index) => {
          this.ipRowData.push(Object.assign({}, item, { id: row.servers.length - index }))
        })
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    validateIp() {
      this.ipValidMsg = ''
      if (this.ipRowData.length === 0) {
        this.ipValidMsg = this.$t('pages.validateConfigServerIpOrPort')
      }
      for (let i = 0; this.ipValidMsg.length === 0 && i < this.ipRowData.length; i++) {
        const rowData = this.ipRowData[i]
        if (!rowData.serverIp) {
          // ip 不能为空，不验证ip格式
          this.ipValidMsg = this.$t('pages.validateServerIpError', { row: i + 1 })
        } else if (!isPort1(rowData.tcpPort)) {
          this.ipValidMsg = this.$t('pages.validatePortError', { row: i + 1 })
        }
      }
      return this.ipValidMsg.length === 0
    },
    formatSubmitData() {
      const ipSegments = []
      const keys = []
      this.ipRowData.forEach(ips => {
        const ipPort = ips.serverIp + '-' + ips.tcpPort
        if (keys.indexOf(ipPort) < 0) {
          keys.push(ipPort)
          delete ips.id
          ipSegments.push(ips)
        }
      })
      this.temp.servers = ipSegments
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validateIp()) {
          this.formatSubmitData()
          createApplySecurityAccess(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validateIp()) {
          this.formatSubmitData()
          const tempData = Object.assign({}, this.temp)
          updateApplySecurityAccess(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteApplySecurityAccess({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    createIp() {
      this.ipValidMsg = ''
      let tempData = null
      if (this.ipRowData && this.ipRowData.length > 0) {
        tempData = this.ipRowData[0]
      }
      if (!tempData || tempData.serverIp || tempData.tcpPort) {
        this.ipRowData.splice(0, 0, { id: new Date().getTime(), serverIp: '', tcpPort: 30099 })
      }
    },
    deleteIp() {
      this.ipValidMsg = ''
      const toDeleteIds = this.$refs['ipList'].getSelectedIds()
      for (let i = 0; i < this.ipRowData.length; i++) {
        const item = this.ipRowData[i]
        if (toDeleteIds.indexOf(item.id) > -1) {
          this.ipRowData.splice(i, 1)
          i--
        }
      }
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    formatStrategy(row) {
      const ipArr = row.servers.map(item => '\xa0' + item.serverIp + ':' + item.tcpPort)
      return this.$t('pages.serverList') + `：[${ipArr} ]`
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>

<style lang="scss"  scoped>
.el-input-number.is-without-controls .el-input__inner{
  text-align: left;
}
.box-card{
  position: relative;
  >>>.el-card__header{
    padding: 10px 20px;
  }
}
.btn-box{
  position: absolute;
  top: 5px;
  right: 20px;
}
.el-table .DisableSelection .cell .el-checkbox__inner{
  display: none;
  position: relative;
}
.el-table .DisableSelection .cell:before{
  content: "";
  position: absolute;
  // right: 11px;
}
</style>
