<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.processStgLib_ImportTitle')"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <el-tabs ref="tabs" v-model="tabName" type="card" @tab-click="tabClick">
        <el-tab-pane label="Window" name="windowTab">
          <process-select-table ref="windowTab" :height="350" :os-type="1" :multiple="true" :lib-tree-node="getStgTypeTree" :lib-file-suffix-page="getStgPage"/>
        </el-tab-pane>
        <el-tab-pane label="Linux" name="linuxTab">
          <process-select-table ref="linuxTab" :height="350" :os-type="2" :multiple="true" :lib-tree-node="getStgTypeTree" :lib-file-suffix-page="getStgPage"/>
        </el-tab-pane>
        <el-tab-pane label="Mac" name="macTab">
          <process-select-table ref="macTab" :height="350" :os-type="4" :multiple="true" :lib-tree-node="getStgTypeTree" :lib-file-suffix-page="getStgPage"/>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :menu-code="'A5G'" :link-url="'/dataEncryption/encryption/processStgLib'" :btn-text="$t('pages.processStgLib')" :click-func="'clickLink'" @clickLink="clickLink('/dataEncryption/encryption/processStgLib')"/>
        <el-button type="primary" @click="confirmImport()">
          {{ $t('pages.processStgLib_addSelectedProcessName') }}
        </el-button>
        <el-button @click="hide">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getStgTypeTree, getStgPage } from '@/api/dataEncryption/encryption/processStgLib'
import ProcessSelectTable from '@/views/dataEncryption/encryption/processStgLib/processSelectTable'

export default {
  name: 'ProcessSelectImportDlg',
  components: { ProcessSelectTable },
  props: {
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      tabName: 'windowTab',
      dialogVisible: false
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    getStgTypeTree,
    getStgPage,
    tabClick(pane, event) {
      this.handleShow()
    },
    show() {
      this.dialogVisible = true
      this.handleShow()
    },
    handleShow() {
      setTimeout(() => {
        this.$refs[this.tabName] && this.$refs[this.tabName].show()
      }, 100)
    },
    hide() {
      this.dialogVisible = false
    },
    confirmImport() {
      this.$refs[this.tabName].getSelectedDatas().then(resp => {
        if (resp == null || resp.length === 0) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.processStgLib_addSelectedProcessNameHint'),
            type: 'error',
            duration: 2000
          })
          return
        }
        this.$emit('importProcess', resp)
        this.hide()
      })
    },
    clickLink() {
      this.dialogVisible = false
      this.$router.push('/dataEncryption/encryption/processStgLib')
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
</style>
