<template>
  <div>
    <FormItem prop="disableNet">
      <el-checkbox v-model="temp.disableNet" :true-label="1" :false-label="0">{{ $t('pages.restrictInternetAccess') }}</el-checkbox>
    </FormItem>
    <FormItem v-if="temp.disableNet == 1">
      <div>
        <el-button type="primary" size="smaller" @click="handleItemCreate">
          {{ $t('button.insert') }}
        </el-button>
        <el-button type="primary" size="smaller" :disabled="!deleteable" @click="handleDeleteItem">
          {{ $t('button.delete') }}
        </el-button>
      </div>
      <grid-table ref="netTable" :height="150" :show-pager="false" :col-model="colModel" :row-datas="temp.netConfigs" @selectionChangeEnd="selectionChangeEnd" />
    </FormItem>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :modal="false"
      :title="isUpdateFormMode?$t('pages.editRestrictInternetAccess') : $t('pages.addRestrictInternetAccess')"
      :visible.sync="appVisible"
      width="500px"
    >
      <Form ref="appForm" :rules="rules" :model="itemTemp" label-position="right" label-width="90px">
        <!--<FormItem label="名称" prop="name">
          <el-input v-model="itemTemp.name" maxlength="30"></el-input>
        </FormItem>-->
        <FormItem :label="$t('pages.behavior')" prop="mode">
          <el-select v-model="itemTemp.mode">
            <el-option v-for="(value, key) in modeOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.protocol')" prop="protocol">
          <el-select v-model="itemTemp.protocol" clearable>
            <el-option v-for="(value, key) in protocolOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.portRange')">
          <el-select v-model="portType" @change="portTypeChange">
            <el-option v-for="(value, key) in portTypeOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.beginPort')" prop="beginPort">
              <el-input v-model.number="itemTemp.beginPort" maxlength="5" :disabled="portType === '1'" @keyup.native="number('beginPort')"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.endPort')" prop="endPort">
              <el-input v-model.number="itemTemp.endPort" maxlength="5" :disabled="portType === '1'" @keyup.native="number('endPort')"></el-input>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.ipRange')">
          <el-select v-model="ipType" @change="ipTypeChange">
            <el-option v-for="(value, key) in ipTypeOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
        <FormItem v-if="ipType == '1' || ipType == '2'" :label="$t('pages.startIP')" prop="beginIp">
          <el-input v-model="itemTemp.beginIp" :disabled="ipType === '1'" @blur="inputBlur('endIp')"></el-input>
        </FormItem>
        <FormItem v-if="ipType == '1' || ipType == '2'" :label="$t('pages.endIP')" prop="endIp">
          <el-input v-model="itemTemp.endIp" :disabled="ipType === '1'" @blur="inputBlur('beginIp')"></el-input>
        </FormItem>
        <FormItem v-if="ipType == '3' || ipType == '4'" :label="$t('pages.startIP')" prop="beginIpv6">
          <el-input v-model="itemTemp.beginIpv6" :disabled="ipType == '3'" @blur="inputBlur('endIpv6')"></el-input>
        </FormItem>
        <FormItem v-if="ipType == '3' || ipType == '4'" :label="$t('pages.endIP')" prop="endIpv6">
          <el-input v-model="itemTemp.endIpv6" :disabled="ipType == '3'" @blur="inputBlur('beginIpv6')"></el-input>
        </FormItem>
        <div style="color: #0c60a5; padding-left: 20px;padding-top: 6px;width: 450px">
          {{ $t('pages.processStgLib_Msg82') }}
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="isUpdateFormMode?updateItem():createItem()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="appVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { isIPv4, isIPv6 } from '@/utils/validate'

export default {
  name: 'OpenNet',
  props: {
    temp: {
      type: Object,
      default: function() {
        return {
          id: null,
          disableNet: 0,
          netConfigs: []
        }
      }
    }
  },
  data() {
    return {
      colModel: [
        // { prop: 'name', label: '名称', width: '100', fixed: true },
        { prop: 'mode', label: 'behavior', width: '60', formatter: this.modeFormatter },
        { prop: 'protocol', label: 'protocol', width: '60', formatter: this.protocolFormatter },
        { label: 'range', width: '120', formatter: this.ipFormatter },
        { label: 'portRange', width: '80', formatter: this.portFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 50,
          buttons: [
            { label: 'edit', click: this.handleItemUpdate }
          ]
        }
      ],
      isUpdateFormMode: true,
      itemTemp: {},
      defaultItemTemp: {
        id: undefined,
        name: '',
        processStgId: undefined,
        mode: '1',
        protocol: '1',
        beginIp: '0.0.0.0',
        endIp: '***************',
        beginIpv6: '0:0:0:0:0:0:0:0',
        endIpv6: 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff',
        beginPort: 1,
        endPort: 65535,
        includeLan: 0
      },
      modeOptions: { 1: this.$t('pages.allow'), 2: this.$t('pages.forbid') },
      protocolOptions: { 1: 'TCP', 2: 'UDP' },
      portTypeOptions: { 1: this.$t('pages.allPort'), 2: this.$t('pages.userDefined') },
      ipTypeOptions: { '1': this.$t('pages.allIPv4'), '2': this.$t('pages.customIPv4'), '3': this.$t('pages.allIPv6'), '4': this.$t('pages.customIPv6') },
      submitting: false,
      dialogFormVisible: false,
      appVisible: false,
      fingerVisible: false,
      portType: undefined,
      ipType: undefined,
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        beginPort: [
          { required: true, message: this.$t('pages.validateMsg_beginPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        endPort: [
          { required: true, message: this.$t('pages.validateMsg_endPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        beginIp: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        endIp: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        beginIpv6: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipv6Validator, trigger: 'blur' }
        ],
        endIpv6: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipv6Validator, trigger: 'blur' }
        ]
      },
      deleteable: false
    }
  },
  watch: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    ipTypeChange(value) {
      if (value === '1') {
        this.$refs['appForm'].clearValidate('beginIp')
        this.$refs['appForm'].clearValidate('beginIp')
        this.itemTemp.beginIp = '0.0.0.0'
        this.itemTemp.endIp = '***************'
      } else if (value === '3') {
        this.$refs['appForm'].clearValidate('beginIpv6')
        this.$refs['appForm'].clearValidate('endIpv6')
        this.itemTemp.beginIpv6 = '0:0:0:0:0:0:0:0'
        this.itemTemp.endIpv6 = 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff'
      }
    },
    portTypeChange(value) {
      if (value === '1') {
        this.$refs['appForm'].clearValidate('beginPort')
        this.$refs['appForm'].clearValidate('endPort')
        this.itemTemp.beginPort = 1
        this.itemTemp.endPort = 65535
      }
    },
    handleItemCreate() {
      this.resettemp()
      this.isUpdateFormMode = false
      this.appVisible = true
      this.$nextTick(() => {
        this.$refs['appForm'].clearValidate()
      })
    },
    handleItemUpdate(app) {
      this.resettemp()
      this.itemTemp = Object.assign(this.itemTemp, app)
      this.itemTemp.mode = '' + this.itemTemp.mode
      this.itemTemp.protocol = '' + this.itemTemp.protocol
      this.portType = this.itemTemp.beginPort === 1 && this.itemTemp.endPort === 65535 ? '1' : '2'
      // this.ipType = this.itemTemp.beginIp === '0.0.0.0' && this.itemTemp.endIp === '***************' ? '1' : '2'
      if (app.beginIp == '0.0.0.0' && app.endIp == '***************') {
        this.ipType = '1'
      } else if (app.beginIpv6 == '0:0:0:0:0:0:0:0' && app.endIpv6 == 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff') {
        this.ipType = '3'
      } else if (app.beginIp) {
        this.ipType = '2'
      } else {
        this.ipType = '4'
      }
      this.isUpdateFormMode = true
      this.appVisible = true
      this.$nextTick(() => {
        this.$refs.appForm.clearValidate()
      })
    },
    handleDeleteItem() {
      this.$confirmBox(this.$t('pages.processStgLib_Msg40'), this.$t('text.prompt')).then(() => {
        const rows = this.$refs.netTable.getSelectedDatas()
        // 创建待删除键的集合
        const deleteKeys = new Set(rows.map(({ id }) => `${id}`));
        // 使用filter一次性过滤
        this.temp.netConfigs = this.temp.netConfigs.filter(({ id }) => 
          !deleteKeys.has(`${id}`)
        );
      }).catch(() => {})
    },
    createItem() {
      this.submitting = true
      this.$refs['appForm'].validate((valid) => {
        if (valid) {
          this.formatterIpPort(this.itemTemp)
          this.itemTemp.id = new Date().getTime()
          const sameConfig = this.valiSameConfig(this.temp.netConfigs, this.itemTemp)
          if (!sameConfig) {
            this.temp.netConfigs.push(this.itemTemp)
          }
          this.appVisible = false
          this.submitting = false
        } else {
          this.submitting = false
        }
      })
      this.submitting = false
    },
    valiSameConfig(allConfig, data) {
      let sameConfig = false
      if (allConfig.length > 0) {
        allConfig.forEach(config => {
          if (data.id != config.id && data.beginIp == config.beginIp && data.beginIpv6 == config.beginIpv6 && data.endIp == config.endIp &&
              data.endIpv6 == config.endIpv6 && data.endPort == config.endPort && data.includeLan == config.includeLan &&
              data.mode == config.mode && data.protocol == config.protocol) {
            sameConfig = true
          }
        })
      }
      return sameConfig
    },
    updateItem() {
      this.submitting = true
      this.$refs['appForm'].validate((valid) => {
        if (valid) {
          this.formatterIpPort(this.itemTemp)
          const sameConfig = this.valiSameConfig(this.temp.netConfigs, this.itemTemp)
          const index = this.temp.netConfigs.findIndex(item => {
            return item.id == this.itemTemp.id
          })
          if (sameConfig) {
            this.temp.netConfigs.splice(index, 1)
          } else {
            this.temp.netConfigs.splice(index, 1, this.itemTemp)
          }
          this.appVisible = false
          this.submitting = false
        } else {
          this.submitting = false
        }
      })
    },
    resettemp() {
      this.itemTemp = Object.assign({}, this.defaultItemTemp)
      this.portType = '1'
      this.ipType = '1'
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    nameValidator(rule, value, callback) {
      const index = this.temp.netConfigs.findIndex(item => {
        return item.name == this.itemTemp.name && item.id != this.itemTemp.id
      })
      if (index >= 0) {
        callback(new Error(this.$t('pages.processStgLib_Msg83')))
      } else {
        callback()
      }
    },
    modeFormatter: function(row, data) {
      return this.modeOptions[data]
    },
    protocolFormatter: function(row, data) {
      return this.protocolOptions[data]
    },
    ipFormatter(row, data) {
      if (row.beginIp || row.endIp) {
        return row.beginIp + ' - ' + row.endIp
      } else if (row.beginIpv6 || row.endIpv6) {
        return row.beginIpv6 + ' - ' + row.endIpv6
      }
    },
    portFormatter(row, data) {
      return (row.beginPort ? row.beginPort : '') + ' - ' + (row.endPort ? row.endPort : '')
    },
    includeLanFormatter(row, data) {
      return data == 1 ? this.$t('pages.takeEffect') : this.$t('pages.inoperative')
    },
    portValidator(rule, value, callback) {
      // if (rule.field === 'beginPort') {
      //   const port = this.tempP.endPort
      //   this.$refs['appForm'].fields.forEach((e) => {
      //     if (e.prop == 'endPort') {
      //       e.resetField()
      //     }
      //   })
      //   this.tempP.endPort = port
      // } else {
      //   const port = this.tempP.beginPort
      //   this.$refs['appForm'].fields.forEach((e) => {
      //     if (e.prop == 'beginPort') {
      //       e.resetField()
      //     }
      //   })
      //   this.tempP.beginPort = port
      // }
      if (isNaN(Number(value)) || Number(value) != parseInt(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text5')))
      } else {
        if (value < 1 || value > 65535) {
          callback(new Error(this.$t('pages.serverLibrary_text6')))
        } else {
          const beginPort = Number(this.itemTemp.beginPort)
          const endPort = Number(this.itemTemp.endPort)
          if (beginPort && endPort) {
            if (beginPort > endPort) {
              callback(new Error(this.$t('pages.serverLibrary_text7')))
            } else {
              this.$refs['appForm'].clearValidate(['beginPort', 'endPort'])
              callback()
            }
          } else {
            callback()
          }
        }
      }
    },
    ipValidator(rule, value, callback) {
      if (this.ipType == '1' || this.ipType == '2') {
        if (value && isIPv4(value)) {
          if (this.itemTemp.beginIp && this.itemTemp.endIp) {
            const temp1 = this.itemTemp.beginIp.split('.')
            const temp2 = this.itemTemp.endIp.split('.')
            let flag = false
            for (var i = 0; i < 4; i++) {
              if (temp1[i] - temp2[i] == 0) {
                continue
              } else if (temp1[i] - temp2[i] > 0) {
                flag = true
              }
              break
            }
            if (flag) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      } else {
        callback()
      }
    },
    ipv6Validator(rule, value, callback) {
      if (isIPv6(value)) {
        if (this.itemTemp.beginIpv6 && this.itemTemp.endIpv6) {
          const fullbeginIpv6 = this.getFullIPv6(this.itemTemp.beginIpv6)
          const fullendIpv6 = this.getFullIPv6(this.itemTemp.endIpv6)
          if (fullbeginIpv6 > fullendIpv6) {
            callback(new Error(this.$t('pages.serverLibrary_text3')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback(new Error(this.$t('pages.ipv6_text1')))
      }
    },
    getFullIPv6(src) {
      const temp = src.replace('::', '*')
      const secs = temp.split(':')
      let ret = ''
      for (let i = 0; i < secs.length; i++) {
        const t = secs[i]
        if (t.indexOf('*') >= 0) {
          const ss = t.split('*')
          ret += ss[0].padStart(4, '0')
          for (let j = 1; j < (8 - secs.length); j++) {
            ret += '0000'
          }
          ret += ss[1].padStart(4, '0');
        } else {
          ret += t.padStart(4, '0');
        }
      }
      return ret.toLocaleLowerCase();
    },
    beginPortFocus(event) {
      if (this.itemTemp.beginPort === 1) {
        this.itemTemp.beginPort = undefined
      }
    },
    beginPortBlur(event) {
      if (!this.itemTemp.beginPort) {
        this.itemTemp.beginPort = 1
      }
    },
    endPortFocus(event) {
      if (this.itemTemp.endPort === 65535) {
        this.itemTemp.endPort = undefined
      }
    },
    endPortBlur(event) {
      if (!this.itemTemp.endPort) {
        this.itemTemp.endPort = 65535
      }
    },
    number(field) {
      if (this.itemTemp[field].toString().length > 0 && this.itemTemp[field] == 0) {
        this.itemTemp[field] = 1
      } else if (isNaN(this.itemTemp[field])) {
        this.itemTemp[field] = this.itemTemp[field].replace(/[^\d]/g, '')
      }
    },
    focus(event) {
      event.currentTarget.select()
    },
    inputBlur(validateProp) {
      this.$refs['appForm'].validateField(validateProp)
    },
    formatterIpPort(data) {
      if (this.ipType == '1' || this.ipType == '2') {
        data.beginIpv6 = undefined
        data.endIpv6 = undefined
      } else if (this.ipType === '3' || this.ipType == '4') {
        data.beginIp = undefined
        data.endIp = undefined
      }
    }
  }
}
</script>
