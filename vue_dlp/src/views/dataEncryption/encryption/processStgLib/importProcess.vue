<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.processStgLib_Msg73')"
      :visible.sync="dialogVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <el-upload
        ref="uploadFileList"
        action="1111"
        class="upload-demo"
        style="display:inline-block;max-width: 560px;"
        :limit="10"
        multiple
        list-type="text"
        :file-list="fileList"
        accept=".tip,.tipa"
        :auto-upload="false"
        :on-change="onFileChange"
        :on-remove="onFileChange"
        :http-request="onUpload"
      >
        <el-tooltip class="item" effect="dark" :content="$t('pages.processStgLib_Msg74', { num: 10 })" placement="right-start">
          <el-button size="small" type="primary">{{ $t('pages.processStgLib_Msg75') }}</el-button>
        </el-tooltip>
        <i v-show="fileList.length > 0" class="el-icon-delete-solid" :title="$t('pages.processStgLib_Msg76')" @click="clearFiles"></i>
        <div slot="tip" class="el-upload__tip" style="position: absolute; top: 90px; left: 35px;">
          <span v-show="errorMsg" style="color:red">({{ errorMsg }})</span>
        </div>
      </el-upload>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.processStgLib_Msg77') }}</span>
        </div>
        <el-radio-group v-model="importType">
          <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
          <el-radio :label="2">{{ $t('pages.importAndAdd') }}</el-radio>
        </el-radio-group>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="createData">{{ $t('button.confirm') }}</el-button>
        <el-button @click="hide">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { importProcess } from '@/api/dataEncryption/encryption/processStgLib'

export default {
  name: 'ImportProcess',
  props: {
    osType: { type: Number, default: 1 } // 进程系统类型：1-windows，2-linux，4-mac
  },
  data() {
    return {
      fileList: [],
      dialogVisible: false,
      submitting: false,
      submitReturnSize: 0, // 提交返回的数量，包括成功和失败的提交
      importType: 1,
      errorMsg: ''
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
  },
  methods: {
    show() {
      this.resetTemp()
      this.dialogVisible = true
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.errorMsg = ''
      this.fileList.splice(0)
      this.submitReturnSize = 0
    },
    onUpload(data) {
      this.submitting = true
      const fileName = data.file.name
      const fd = new FormData()
      fd.append('uploadFile', data.file)// 传文件
      fd.append('importType', this.importType)
      fd.append('osType', this.osType)
      importProcess(fd).then(res => {
        this.$notify({ title: this.$t('text.success'), message: this.$t('pages.processStgLib_Msg79', { name: fileName }), type: 'success', duration: 2000 })
        this.uploadEnd()
      }).catch(err => {
        console.log(err)
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.processStgLib_Msg80', { name: fileName }), type: 'error', duration: 2000 })
        this.uploadEnd()
      })
      return false // 屏蔽了action的默认上传
    },
    uploadEnd() {
      this.submitReturnSize++
      if (this.submitReturnSize === this.fileList.length) {
        this.submitting = false
        this.hide()
        this.$emit('success', this)
      }
    },
    onFileChange(file, list) {
      this.fileList = list
      this.errorMsg = ''
    },
    clearFiles(e) {
      window.event ? window.event.cancelBubble = true : e.stopPropagation()
      this.fileList = []
      this.submitReturnSize = 0
      this.$refs.uploadFileList.clearFiles()
    },
    handleDrag() {
    },
    createData() {
      if (this.fileList.length === 0) {
        this.errorMsg = this.$t('pages.processStgLib_Msg81')
        return
      }
      this.$refs.uploadFileList.submit()
    }
  }
}
</script>

<style lang='scss' scoped>
  .upload-demo>>>.el-upload-list{
    min-height: 20px;
    max-height: 188px;
    overflow: auto;
    margin-top: 2px;
    .el-upload-list__item:hover {
      background-color: #dbdbdb;
    }
  }
  .el-icon-delete-solid{
    margin-left: 20px;
    font-size: 16px;
    &:hover{
      color: rgb(12, 123, 226);
    }
  }
</style>
