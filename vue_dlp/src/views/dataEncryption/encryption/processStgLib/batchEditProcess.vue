<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.batchModification')"
      :visible.sync="dialogFormVisible1"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="batchEditForm" :rules="rules" :model="tempF" label-position="right" label-width="120px">
        <grid-table ref="processList" :height="200" style="padding-bottom: 5px" :show-pager="false" :multi-select="false" :col-model="colModel" :row-datas="tempF.processList"/>
        <FormItem :label="$t('pages.processStgLib_Msg3')" prop="decReadSfx" style="margin-top: 10px;">
          <el-checkbox v-model="tempF.editDecReadSfx" style="margin-left: 10px;">{{ $t('pages.edit') }}</el-checkbox>
          <el-input v-model="tempF.decReadSfx" :disabled="!tempF.editDecReadSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button style="margin-left: 1px" :disabled="!tempF.editDecReadSfx" type="primary" size="mini" @click="handleFileSuffixImport('decReadSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.processStgLib_Msg4')" prop="encWriteSfx">
          <el-checkbox v-model="tempF.editEncWriteSfx" style="margin-left: 10px;">{{ $t('pages.edit') }}</el-checkbox>
          <el-input v-model="tempF.encWriteSfx" :disabled="!tempF.editEncWriteSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button style="margin-left: 1px" :disabled="!tempF.editEncWriteSfx" type="primary" size="mini" @click="handleFileSuffixImport('encWriteSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.processStgLib_Msg6')" prop="encOpenSfx">
          <el-checkbox v-model="tempF.editEncOpenSfx" style="margin-left: 10px;">{{ $t('pages.edit') }}</el-checkbox>
          <el-input v-model="tempF.encOpenSfx" :disabled="!tempF.editEncOpenSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button style="margin-left: 1px" :disabled="!tempF.editEncOpenSfx" type="primary" size="mini" @click="handleFileSuffixImport('encOpenSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>
        <FormItem label="" prop="enablePast">
          <el-checkbox v-model="tempF.editEnablePast" style="margin-left: 10px;">{{ $t('pages.edit') }}</el-checkbox>
          <el-checkbox v-model="tempF.enablePast" :disabled="!tempF.editEnablePast" :true-label="65535" :false-label="0">{{ $t('pages.processStgLib_Msg7') }}</el-checkbox>
        </FormItem>
        <FormItem v-if="showFinger" label="" prop="checkMd5">
          <el-checkbox v-model="tempF.editCheckMd5" style="margin-left: 10px;">{{ $t('pages.edit') }}</el-checkbox>
          <el-tooltip class="item" effect="dark" :content="$t('pages.processStgLib_Msg19')" placement="bottom-start">
            <el-checkbox v-model="tempF.checkMd5" :disabled="!tempF.editCheckMd5" :true-label="1" :false-label="0">{{ $t('pages.supportMd5') }}</el-checkbox>
          </el-tooltip>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content">{{ $t('pages.processStgLib_Msg104') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem v-if="showNet" label="" prop="disableNet">
          <el-checkbox v-model="tempF.editDisableNet" style="margin-left: 10px;">{{ $t('pages.edit') }}</el-checkbox>
          <el-checkbox v-model="tempF.disableNet" :disabled="!tempF.editDisableNet" :true-label="1" :false-label="0">{{ $t('pages.restrictInternetAccess') }}</el-checkbox>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content">{{ $t('pages.processStgLib_Msg105') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="createData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible1 = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
  </div>
</template>
<script>
import { updateProcessBatch } from '@/api/dataEncryption/encryption/processStgLib'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'

export default {
  name: 'BatchEdit',
  components: { FileSuffixLibImport },
  props: {
    osType: { // 终端类型：1=windows，2=linux，4=mac
      type: Number,
      default: 1
    },
    showNet: { type: Boolean, default: true },
    showFinger: { type: Boolean, default: true }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'programName', width: '150', fixed: true },
        { prop: 'decReadSfx', label: 'fileSuffixOpen', width: '150' },
        { prop: 'encWriteSfx', label: 'fileSuffixSave', width: '150' },
        { prop: 'encOpenSfx', label: 'fileSuffixVisit', width: '150' },
        { prop: 'checkMd5', hidden: !this.showFinger, label: 'enableAntiCounterfeiting', width: '100', formatter: this.md5LevelFormatter },
        { prop: 'disableNet', label: 'restrictInternetAccess', hidden: !this.showNet, width: '80', formatter: this.disableNetFormatter },
        { prop: 'enablePast', label: 'pasteEncryption', width: '100', formatter: this.enableFormatter },
        { prop: 'name', label: 'fileDescription', width: '150' }
      ],
      tempF: {},
      defaultTempF: { // 表单字段
        decReadSfx: '',
        editDecReadSfx: false,
        encWriteSfx: '',
        editEncWriteSfx: false,
        encOpenSfx: '',
        editEncOpenSfx: false,
        enablePast: 0,
        editEnablePast: false,
        checkMd5: 0,
        editCheckMd5: false,
        disableNet: 0,
        editDisableNet: false,
        processList: []
      },
      dialogFormVisible1: false,
      rules: {
        encOpenSfx: [
          { validator: this.suffixValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      importFileSuffixType: '',
      suffixMaxLength: 200
    }
  },
  computed: {
    softTable: function() {
      return this.$refs['softTable']
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.tempF = Object.assign({}, this.defaultTempF)
    },
    handleDrag() {
    },
    handleCreate(processList) {
      this.resetTemp()
      this.tempF.processList.splice(0)
      this.tempF.processList.push(...processList)
      this.dialogFormVisible1 = true
      this.$nextTick(() => {
        this.$refs['batchEditForm'].clearValidate()
      })
    },
    createData() {
      if (!this.tempF.editDecReadSfx && !this.tempF.editEncWriteSfx && !this.tempF.editEncOpenSfx && !this.tempF.editEnablePast && !this.tempF.editCheckMd5 && !this.tempF.editDisableNet) {
        this.$message({ message: this.$t('pages.processStgLib_Msg48'), type: 'error', duration: 2000 })
        return
      }
      if (this.tempF.editCheckMd5 && this.tempF.checkMd5) {
        const emptyRelaFingerPrint = this.tempF.processList.filter(item => item.relaFingerPrint.length == 0)
        if (emptyRelaFingerPrint.length > 0) {
          const name = [...new Set(emptyRelaFingerPrint.map(item => item.processName))].join(',')
          this.$message({ message: this.$t('pages.processStgLib_Msg106', { name: name }), type: 'error', duration: 2000 })
          return
        }
      }
      this.submitting = true
      this.$refs['batchEditForm'].validate((valid) => {
        if (valid) {
          updateProcessBatch(this.tempF).then(respond => {
            this.submitting = false
            this.dialogFormVisible1 = false
            this.$emit('success', respond.data)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    enableFormatter(row, data) {
      return data > 0 ? this.$t('pages.allow') : ''
    },
    md5LevelFormatter(row, data) {
      return data == 1 ? this.$t('pages.enable') : this.$t('pages.notEnabled')
    },
    disableNetFormatter(row, data) {
      return data == 1 ? this.$t('pages.limit') : this.$t('pages.notLimit')
    },
    suffixValidator(rule, value, callback) {
      if (value == '*.*') {
        callback(new Error(this.$t('pages.appGroup_text5')))
      } else {
        callback()
      }
    },
    handleFileSuffixImport(type) {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
      this.importFileSuffixType = type
    },
    duplicateRemoval(nv, ov) {
      let union_suffix = null
      if (ov == null || ov === '') {
        union_suffix = [...new Set(nv.split('|'))].join('|')
      } else {
        union_suffix = [...new Set((ov + '|' + nv).split('|'))].join('|')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      return union_suffix
    },
    importFileSuffix(suffix) {
      if (this.importFileSuffixType === 'decReadSfx') {
        this.tempF.decReadSfx = this.duplicateRemoval(suffix, this.tempF.decReadSfx)
      } else if (this.importFileSuffixType === 'encWriteSfx') {
        this.tempF.encWriteSfx = this.duplicateRemoval(suffix, this.tempF.encWriteSfx)
      } else if (this.importFileSuffixType === 'encOpenSfx') {
        this.tempF.encOpenSfx = this.duplicateRemoval(suffix, this.tempF.encOpenSfx)
      }
    }
  }
}
</script>
