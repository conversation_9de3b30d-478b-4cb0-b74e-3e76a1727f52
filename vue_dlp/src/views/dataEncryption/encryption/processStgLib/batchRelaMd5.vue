<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :modal-append-to-body="true"
      :append-to-body="appendToBody"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.processStgLib_Msg49')"
      :visible.sync="dialogFormVisible1"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm2" :rules="rules" :model="tempF" label-position="right" label-width="140px">
        <div class="toolbar">
          <upload-dir v-if="!isIE() && osType == 1" ref="uploadDir" :popover-height="235" :loading="fileSubmitting" :disabled="extracting" style="display: inline-block;" @changeFile="changeFile" />
          <el-upload
            ref="upload"
            name="uploadFile"
            action="aaaaaa"
            :accept="osType == 1 ? '.exe' : ''"
            :disabled="fileSubmitting"
            :show-file-list="false"
            :before-upload="beforeUpload"
            style="display: inline-block;"
          >
            <el-button type="primary" :loading="fileSubmitting" :disabled="extracting" size="mini" style="margin-top: 0;">{{ $t('pages.uploadFile') }}</el-button>
          </el-upload>
          <el-button v-if="osType == 1" type="primary" :loading="extracting" :disabled="fileSubmitting" size="mini" @click="handleTerminalSoft">
            {{ $t('pages.selectTerminalSoft') }}
          </el-button>
          <FormItem v-if="fileSubmitting" label-width="0">
            <el-progress type="line" :percentage="percentage" style="width: calc(100% - 46px); display: inline-block;"/>
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </FormItem>
          <FormItem v-if="extracting" label-width="0">
            <el-progress type="line" :percentage="extractingPercentage" style="width: calc(100% - 46px); display: inline-block;"/>
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="stopExtract"></el-button>
          </FormItem>
        </div>
        <FormItem :label="$t('pages.checkMd5Label')" label-width="80px" :extra-width="{en: 100}">
          <el-radio-group v-model="tempF.md5Level" style="padding-left: 20px;">
            <el-tooltip class="item" effect="dark" :content="$t('pages.appGroup_text20')" placement="bottom">
              <el-radio :label="3">{{ $t('pages.md5LevelMap3') }}</el-radio>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="$t('pages.appGroup_text21')" placement="bottom">
              <el-radio :label="1">{{ $t('pages.md5LevelMap1') }}</el-radio>
            </el-tooltip>
          </el-radio-group>
        </FormItem>
        <grid-table
          ref="fileList"
          :height="350"
          row-key="rowKey"
          :multi-select="true"
          :show-pager="false"
          :col-model="colModel"
          :row-datas="fileList"
          :is-clear-saved-selected-row-datas-change="false"
          style="margin-bottom: 5px;"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="createData1">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible1 = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <terminal-soft ref="terminalSoft" @select="receiveSoft" @beforeSelect="beforeExtractSoft"/>
  </div>
</template>
<script>
import { changeFiles, loopUploadFiles } from '@/api/behaviorManage/application/appGroup'
import { updateRelaMd5, getStgTree } from '@/api/dataEncryption/encryption/processStgLib'
import UploadDir from '@/components/UploadDir'
import TerminalSoft from '@/views/dataEncryption/encryption/processStgLib/TerminalSoft'
import axios from 'axios'

export default {
  name: 'BatchRelaMd5',
  components: { UploadDir, TerminalSoft },
  props: {
    appendToBody: { type: Boolean, default: true },
    osType: { // 终端类型：1=windows，2=linux，4=mac
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      colModel: [
        // 此处分组节点和子节点的dataId存在重复，所以nodeKey使用id
        { prop: 'id', label: 'associatedLocation', width: '120', sort: true, type: 'treeSelect', nodeKey: 'id',
          treeData: [], checkedKeysFieldName: 'checkedKeys', isFilter: true, nodeChange: this.nodeChange },
        { prop: 'processName', label: 'programName', width: '100', sort: true },
        { prop: 'productVersion', label: 'softwareVersion', width: '80', type: 'input', maxlength: 30 },
        { prop: 'componentVersion', label: 'componentVersion', width: '100' },
        { prop: 'checkMd5', label: 'counterfeitingLevel', width: '100', sort: true, sortOriginal: true, type: 'select', change: this.selectChange, optionDisabled: this.disable, options: [
          { label: 'processCharacter', value: 3 },
          { label: 'programFinger', value: 1 }
        ] }
      ],
      tempF: {},
      defaultTempF: { // 表单字段
        md5Level: 3,
        osType: this.osType,
        processList: []
      },
      fileList: [],
      dialogFormVisible1: false,
      dialogStatus: '',
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      },
      submitting: false,
      fileSubmitting: false,
      fileLimitSize: 1024,
      percentage: 0,
      source: null,
      selfLibTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib'), parentId: '', children: [] }],
      extracting: false,
      extractingPercentage: 0,
      receiveSoftNum: 0
    }
  },
  computed: {
    softTable: function() {
      return this.$refs['softTable']
    },
    processNameMap() {
      const obj = {}
      const queue = []
      queue.push(...this.selfLibTreeData[0].children)
      while (queue.length > 0) {
        const node = queue.pop()
        if (node.type == 'process') {
          obj[node.label.toLowerCase()] = node.dataId
        }
        if (node.children) {
          queue.push(...node.children)
        }
      }
      return obj
    }
  },
  created() {
    this.resetTemp()
    this.getProcessLibTree()
  },
  activated() {
    this.getProcessLibTree()
  },
  methods: {
    nodeChange(nodeData, node, vm, rowData, col) {
      if (nodeData.type == 'process') {
        const relationAppName = nodeData.label || ''
        const rowAppName = rowData.processName || ''
        if (relationAppName.toLowerCase() !== rowAppName.toLowerCase()) {
          this.$message({
            showClose: true,
            message: this.$t('pages.batchRelationMd5Msg1'),
            type: 'warning'
          })
          return
        }
        // nodeKey是id， key的值为 L + dataId
        rowData.checkedKeys = [`L${nodeData.dataId}`]
        rowData[col.prop] = nodeData.dataId
      } else {
        return false
      }
    },
    /**
     * 终端提取程序弹窗
     * */
    handleTerminalSoft() {
      this.receiveSoftNum = 0
      this.extractingPercentage = 0
      this.$refs.terminalSoft.show()
    },
    /**
     * 停止提取终端程序属性
     */
    stopExtract() {
      this.extracting = false
      this.extractingPercentage = 0
      this.$refs.terminalSoft.stop()
    },
    beforeExtractSoft(param) {
      this.extracting = true
      this.extractingPercentage = 0
    },
    /**
     * 响应终端上传日志
     * @param datas
     */
    receiveSoft(data) {
      this.$refs.terminalSoft.close()
      if (data.flag == 1) {
        this.receiveSoftNum = this.receiveSoftNum + data.softNum
        this.extractingPercentage = Math.ceil(this.receiveSoftNum / data.allNum * 100)
      }
      // 如果传输完毕
      if (data.allNum == data.hasLoadNum) {
        this.extracting = false
        this.extractingPercentage = 0
      }
      if (data && data.softList && data.softList.length > 0) {
        this.appendFile(data.softList, false)
      }
    },
    disable(item, row, col) {
      if (item.value == 1 && !row.quicklyMd5) {
        return true
      }
      if (item.value == 3 && !row.propertyMd5) {
        return true
      }
      return false
    },
    selectChange(row, col) {

    },
    getProcessLibTree() {
      getStgTree({ osType: this.osType }).then(res => {
        this.selfLibTreeData[0].children = res.data
      })
    },
    resetTemp() {
      this.tempF = Object.assign({}, this.defaultTempF)
      this.fileList.splice(0)
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('pages.processStgLib_Msg50'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      // const fileName = file.name
      // const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      loopUploadFiles([file], this)
      return false // 屏蔽了action的默认上传
    },
    changeFile(files) {
      changeFiles(files, this)
    },
    toFingerprintData(appData) {
      const obj = {
        processName: appData.processName,
        productVersion: '',
        componentVersion: appData.productVersion,
        propertyMd5: appData.propertyMd5,
        fileMd5: appData.fileMd5,
        quicklyMd5: appData.quicklyMd5,
        checkMd5: this.tempF.md5Level,
        propertyMark: appData.propertyMark,
        productMd5: appData.productMd5,
        remark: '',
        rowKey: appData.processName + appData.quicklyMd5,
        id: this.processNameMap[appData.processName.toLowerCase()]
      }
      if (obj.id) {
        // nodeKey是id， key的值为 L + dataId
        obj.checkedKeys = [`L${obj.id}`]
      }
      if (this.tempF.md5Level == 3 && !appData.propertyMd5) {
        obj.checkMd5 = 1
      }
      return obj
    },
    existsFile(soft) {
      return this.fileList.findIndex(item => {
        return item.processName && item.processName.toLocaleLowerCase() == soft.processName.toLocaleLowerCase() && item.quicklyMd5 == soft.quicklyMd5
      })
    },
    appendFile(softs, clear) {
      if (clear) {
        this.fileList.splice(0)
      }
      const newList = []
      softs.forEach((item, index) => {
        if (this.existsFile(item) < 0) {
          const data = this.toFingerprintData(item)
          this.fileList.push(data)
          if (data.id) {
            newList.push(data)
          }
        }
      })
      return this.$nextTick().then(() => {
        newList.forEach(item => {
          this.$refs.fileList.toggleRowSelection(item, true)
        })
      })
    },
    handleDrag() {
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create1'
      this.dialogFormVisible1 = true
      this.getProcessLibTree()
      this.colModel[0].treeData = this.selfLibTreeData
      this.$nextTick(() => {
        this.$refs['dataForm2'].clearValidate()
      })
    },
    parentTypeSelectChange(data) {
      this.tempF.typeId = data
      this.$refs['dataForm2'].clearValidate()
    },
    validData() {
      const datas = this.$refs.fileList.getSelectedDatas()
      if (datas.length == 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.processStgLib_Msg51'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      let index = datas.findIndex(item => {
        return item.id == null || item.id == undefined
      })
      if (index >= 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.processStgLib_Msg52'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      index = datas.findIndex(item => {
        return !item.productVersion
      })
      if (index >= 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.processStgLib_Msg53'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    createData1() {
      if (!this.validData()) {
        return
      }
      this.submitting = true
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          this.tempF.processList = this.$refs.fileList.getSelectedDatas()
          updateRelaMd5(this.tempF).then(respond => {
            this.submitting = false
            this.dialogFormVisible1 = false
            this.$emit('success', respond.data)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.insertSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .toolbar>div {
    vertical-align: top;
  }
</style>
