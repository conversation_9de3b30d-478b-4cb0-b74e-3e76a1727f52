<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.templateLibrary')"
    :visible.sync="dlgVisible"
    width="800px"
    @close="hide"
  >
    <div>
      <div class="tree-container">
        <tree-menu
          ref="libGroupTree"
          multiple
          :height="325"
          :data="libGroupTreeData"
          :default-expanded-keys="defaultExpandedKeys"
          @node-click="libGroupCheckChange"
          @check="libGroupCkeckFunc"
        />
      </div>
      <div class="table-container">
        <div class="toolbar">
          <div class="searchCon">
            <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.typeOptions1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <div class="">
          <grid-table
            ref="libDataList"
            :col-model="colModel"
            :height="285"
            pager-small
            :default-sort="{ prop: 'processName', order: 'ascending' }"
            :row-data-api="rowDataApi"
            @selectionChangeEnd="selectionChangeEnd"
          />
        </div>
      </div>
      <div>
        <Form ref="dataForm" label-width="120px">
          <FormItem v-if="showFinger || showNet" :label="$t('table.limitWay')" :tooltip-content="$t('pages.processStgLib_limit')" tooltip-placement="bottom-start">
            <el-tooltip v-if="showFinger" class="item" effect="dark" :content="$t('pages.processStgLib_Msg19')" placement="bottom-start">
              <el-checkbox v-model="temp.checkMd5" :true-label="1" :false-label="0">{{ $t('pages.supportMd5') }}</el-checkbox>
            </el-tooltip>
            <el-checkbox v-if="showNet" v-model="temp.disableNet" :true-label="1" :false-label="0">{{ $t('pages.restrictInternetAccess') }}</el-checkbox>
          </FormItem>
          <FormItem :label="$t('pages.repeatNameDealType')" prop="objectType">
            <el-radio-group v-model="temp.importType" >
              <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
              <el-radio :label="2">{{ $t('pages.importAndAdd') }}</el-radio>
              <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
            </el-radio-group>
          </FormItem>
          <FormItem :label="$t('pages.importGroupDisposal')" prop="objectType">
            <el-radio-group v-model="temp.groupType">
              <el-radio :label="1" style="display: block">
                <span>{{ $t('pages.importGroupDisposalType1', { type: $t('pages.templateLibrary'), target: $t('pages.customLibrary')}) }}</span>
                <el-tooltip :content="$t('pages.processStgLib_Msg')" class="item" effect="dark" placement="bottom-start">
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-radio>
              <div style="display: flex">
                <el-radio :label="0" style="margin-right: 10px;">
                  <i18n path="pages.importGroupDisposalType2">
                    <template slot="type">{{ $t('pages.templateLibrary') }}</template>
                    <el-tooltip slot="data" :content="$t('pages.processStgLib_Msg1')" class="item" effect="dark" placement="bottom-start">
                      <i class="el-icon-info"/>
                    </el-tooltip>
                  </i18n>
                </el-radio>
                <tree-select
                  v-model="temp.targetGroupId"
                  :checked-keys="[temp.targetGroupId]"
                  :disabled="temp.groupType == 1"
                  :data="groupTreeData"
                  node-key="dataId"
                  :width="296"
                  style="width: 200px;"
                  @change="groupSelectChange"
                />
              </div>
            </el-radio-group>
          </FormItem>
        </Form>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="doImport()">
        {{ $t('button.import') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { findNodeLabel } from '@/utils/tree'
import { getStgPage, getStgTypeTree, importStg, getImportProgress } from '@/api/dataEncryption/encryption/processStgLib'

export default {
  name: 'ProcessLibImportDlg',
  props: {
    groupTreeData: {
      type: Array,
      default: function() {
        return []
      }
    },
    showNet: { type: Boolean, default: true },
    showFinger: { type: Boolean, default: true },    // 是否隐藏掉列表内容
    osType: { type: Number, default: 1 } // 进程系统类型：1-windows，2-linux，4-mac
  },
  data() {
    return {
      dlgVisible: false, // 列表页是否显示
      submitting: false,
      defaultExpandedKeys: ['G0'],
      cacheLibGroupTreeData: {},
      libGroupTreeData: [],
      colModel: [
        { prop: 'processName', label: 'programName', width: '150', fixed: true, sort: true },
        { prop: 'groupId', label: 'appType', width: '120', formatter: this.groupFormatter },
        { prop: 'decReadSfx', label: 'fileSuffixOpen', width: '150' },
        { prop: 'encWriteSfx', label: 'fileSuffixSave', width: '150' },
        { prop: 'encOpenSfx', label: 'fileSuffixVisit', width: '150' },
        { prop: 'checkMd5', hidden: !this.showFinger, label: 'enableAntiCounterfeiting', width: '100', formatter: this.md5LevelFormatter },
        { prop: 'disableNet', label: 'restrictInternetAccess', hidden: !this.showNet, width: '80', formatter: this.disableNetFormatter },
        { prop: 'enablePast', label: 'pasteEncryption', width: '100', formatter: this.enableFormatter },
        { prop: 'name', label: 'fileDescription', width: '150' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: null,
        groupIds: ''
      },
      temp: {},
      defaultTemp: {
        importType: 1,
        groupType: 1,
        checkMd5: 0,
        disableNet: 0,
        targetGroupId: undefined
      }
    }
  },
  computed: {
    libGroupTree: function() {
      return this.$refs['libGroupTree']
    },
    gridTable() {
      return this.$refs['libDataList']
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.query.groupIds = ''
    },
    show() {
      this.resetTemp()
      this.loadLibGroupTreeData()
      this.dlgVisible = true
      this.$nextTick(() => {
        this.handleFilter()
        if (this.libGroupTree) {
          this.libGroupTree.clearFilter()
          this.libGroupTree.clearSelectedNodes()
          this.libGroupTree.clearSelectedNode()
        }
      })
    },
    hide() {
      this.dlgVisible = false
      if (this.libGroupTree) {
        this.libGroupTree.clearFilter()
        this.libGroupTree.clearSelectedNodes()
        this.libGroupTree.clearSelectedNode()
      }
    },
    // 获取模板库的树数据
    loadLibGroupTreeData: function() {
      if (this.cacheLibGroupTreeData[this.osType]) {
        this.libGroupTreeData = this.cacheLibGroupTreeData[this.osType]
      } else {
        getStgTypeTree({ isModule: true, osType: this.osType }).then(respond => {
          this.cacheLibGroupTreeData[this.osType] = [
            { id: 'G0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: [
              ...respond.data
            ] }
          ]
          this.libGroupTreeData = this.cacheLibGroupTreeData[this.osType]
        })
      }
    },
    libGroupCkeckFunc: function(nodeData, checkedInfo) {
      // this.importAbleChange(checkedInfo)
    },
    libGroupCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      // this.addBtnAble = !!checkedNode && checkedNode.data.dataId !== '0'
      const checkNodeIds = []
      this.listNodeIdAndChildNodeIds(checkNodeIds, checkedNode.data)
      this.query.groupIds = checkNodeIds.join(',')
      // if (checkedNode) {
      //   this.query.groupId = checkedNode.data.dataId
      // } else {
      //   this.query.groupId = undefined
      // }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    listNodeIdAndChildNodeIds(checkNodeIds, checkedData) {
      checkNodeIds.push(checkedData.dataId)
      if (checkedData.children) {
        checkedData.children.forEach(data => {
          this.listNodeIdAndChildNodeIds(checkNodeIds, data)
        })
      }
    },
    // 列表
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const queryParam = Object.assign({}, this.query, option, { isModule: true, groupId: undefined, osType: this.osType })
      return getStgPage(queryParam)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    // 表单
    groupSelectChange(data) {
      this.temp.targetGroupId = data
    },
    groupFormatter(row) {
      return findNodeLabel(this.libGroupTreeData, row.groupId, 'dataId')
    },
    md5LevelFormatter(row, data) {
      return data == 1 ? this.$t('pages.enable') : this.$t('pages.notEnabled')
    },
    disableNetFormatter(row, data) {
      return data == 1 ? this.$t('pages.limit') : this.$t('pages.notLimit')
    },
    enableFormatter(row, data) {
      return data > 0 ? this.$t('pages.allow') : this.$t('pages.notAllow')
    },
    // 功能
    async doImport() {
      this.submitting = true
      const nodes = this.$refs.libGroupTree.getCheckedNodes()
      const tableDatas = this.$refs.libDataList.getSelectedDatas()
      let targetGroupId = this.temp.targetGroupId;
      const groupType = this.temp.groupType
      const importType = this.temp.importType
      if (nodes.length === 0 && tableDatas.length === 0) {
        this.$message({ message: this.$t('pages.appGroup_text22'), type: 'error', duration: 2000 })
        this.submitting = false
        return
      }
      if (groupType === 0 && (typeof targetGroupId == 'undefined' || targetGroupId === '')) {
        this.$message({ message: this.$t('pages.validaGroup'), type: 'error', duration: 2000 })
        this.submitting = false
        return
      }

      if (groupType === 1) {
        targetGroupId = ''
      }
      const groupIds = []
      const dataIds = []
      nodes.forEach(item => {
        if (item.type == 'G') {
          groupIds.push(item.dataId)
        }
      })
      tableDatas.forEach(item => {
        dataIds.push(item.id)
      })
      getImportProgress().then(resp => {
        if (resp.data.percent == 0 || resp.data.percent >= 100) {
          this.$confirmBox(this.$t('pages.confirmToImportTheSelected', { info: this.$t('pages.templateLibrary'), target: this.$t('pages.customLibrary') }), this.$t('text.prompt')).then(() => {
            importStg({
              groupIds: groupIds.join(','),
              stgIds: dataIds.join(','),
              osType: this.osType,
              targetId: targetGroupId,
              importType: importType,
              checkMd5: this.temp.checkMd5,
              disableNet: this.temp.disableNet
            }).then(respond => {
              this.submitting = false
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.importSuccess'), type: 'success', duration: 2000 })
              this.hide()
              this.$emit('submitEnd')
            }).catch(res => {
              this.submitting = false
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.importExecutingMsg', { percent: resp.data.percent }),
            type: 'info',
            duration: 2000
          })
        }
      })
    }
  }
}
</script>
