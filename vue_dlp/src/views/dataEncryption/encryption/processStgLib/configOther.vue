<template>
  <div class="table-container">
    <grid-table ref="otherConfigList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="(rowDatas) => {this.$emit('selectChange', rowDatas)}"/>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 530px;">
        <FormItem :label="$t('pages.processStgLib_Msg58')" prop="processStgId">
          <el-input v-model="processStg.label" class="input-with-button" :disabled="true"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim class="input-with-button" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.process_Msg5')" prop="suffix">
          <el-input
            v-model="temp.suffix"
            type="textarea"
            rows="2"
            resize="none"
            :maxlength="suffixMaxLength"
            show-word-limit
            class="input-with-button"
            :placeholder="$t('pages.process_Msg6')"
          ></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" size="mini" @click="handleFileSuffixImport('suffix')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.catalogue')" prop="dir">
          <el-input v-model="temp.dir" type="textarea" class="input-with-button" resize="none" maxlength="100" show-word-limit :placeholder="$t('pages.processStgLib_Msg67')"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.type')" prop="code">
          <el-select v-model="temp.code" class="input-with-button">
            <el-option v-for="(item, index) in codeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </FormItem>
        <div style="color: #0c60a5;padding-top: 10px;padding-left: 10%">
          {{ $t('pages.processStgLib_Msg66') }}
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="showDlg=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
  </div>
</template>

<script>
import { getOtherConfigPage, createOtherConfig, updateOtherConfig, deleteOtherConfig, getOtherConfigByName } from '@/api/dataEncryption/encryption/processStgLibConfig'
import { getProcessActionDict2, getDictLabel } from '@/utils/dictionary'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'

export default {
  name: 'ProcessConfigOther',
  components: { FileSuffixLibImport },
  props: {
    processStg: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true },
        { prop: 'parentProcessName', label: 'parentProcess', width: '150', type: 'button',
          buttons: [{ formatter: this.parentProcessFormatter, click: this.parentProcessClick }]
        },
        { prop: 'suffix', label: 'suffixes', width: '200', sort: 'custom' },
        { prop: 'dir', label: 'catalogue', width: '200', sort: 'custom' },
        { prop: 'code', label: 'type', width: '200', sort: 'custom', formatter: this.codeFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', click: this.handleUpdate, isShow: () => { return this.processStg.id !== undefined } }
          ]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        processStgId: undefined,
        code: undefined,
        suffix: '',
        dir: ''
      },
      codeOptions: getProcessActionDict2(),
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        code: [{ required: true, message: this.$t('pages.processStgLib_Msg68'), trigger: 'blur' }],
        dir: [{ required: true, message: this.$t('pages.processStgLib_Msg69'), trigger: 'blur' }],
        suffix: [{ required: true, message: this.$t('pages.processStgLib_Msg70'), trigger: 'blur' }]
      },
      query: {},
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      textMap: {
        update: this.$t('pages.processStgLib_Msg71'),
        create: this.$t('pages.processStgLib_Msg72')
      },
      suffixMaxLength: 100
    }
  },
  computed: {
    gridTable() {
      return this.$refs['otherConfigList']
    }
  },
  watch: {

  },
  created() {

  },
  methods: {
    rowDataApi: function(option) {
      return getOtherConfigPage(option)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.groupId = this.query.groupId
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.processStgId = this.processStg.dataId
          createOtherConfig(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.processStgId = this.processStg.dataId
          const tempData = Object.assign({}, this.temp)
          updateOtherConfig(tempData).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.updateRowData(this.temp)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    deleteData() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      deleteOtherConfig({ ids: toDeleteIds.join(',') }).then(respond => {
        this.gridTable.deleteRowData(toDeleteIds)
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
      })
    },
    nameValidator(rule, value, callback) {
      getOtherConfigByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    parentProcessFormatter(row) {
      return row.parentProcessName
    },
    parentProcessClick(row) {
      this.$emit('parentProcessClick', row)
    },
    codeFormatter(row, data) {
      return getDictLabel(this.codeOptions, data)
    },
    groupFormatter(row, data) {
      return this.processStg.label
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      let union_suffix
      if (this.temp.suffix == null || this.temp.suffix === '') {
        union_suffix = [...new Set(suffix.split('|'))].join('|')
      } else {
        union_suffix = [...new Set((this.temp.suffix + '|' + suffix).split('|'))].join('|')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      this.temp.suffix = union_suffix
    }
  }
}
</script>
