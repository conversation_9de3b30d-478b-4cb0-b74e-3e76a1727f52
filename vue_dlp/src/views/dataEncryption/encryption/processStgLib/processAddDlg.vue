<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="130px" style="width: 700px;">
        <upload-process ref="upload" :write-able="!isUpdateMode" :only-exe="false" :app-temp="temp" :can-upload="!isUpdateMode" :after-upload="afterUpload"/>

        <FormItem :label="$t('pages.appType')" prop="groupId">
          <tree-select v-model="temp.groupId" :data="groupTreeData" node-key="dataId" :width="568" :checked-keys="[temp.groupId]" class="input-with-button" @change="groupSelectChange" />
          <el-button v-if="addGroup != null" :title="$t('pages.install_Msg42')" class="editBtn" @click="addGroup"><svg-icon icon-class="add" /></el-button>
        </FormItem>

        <FormItem :label="$t('pages.exeDesc')" prop="name">
          <el-input v-model="temp.name" v-trim maxlength="100" class="input-with-button"></el-input>
        </FormItem>

        <FormItem :label="$t('pages.processStgLib_Msg3')" prop="decReadSfx">
          <el-input v-model="temp.decReadSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button style="margin-left: 1px" type="primary" size="mini" @click="handleFileSuffixImport('decReadSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>

        <FormItem :label="$t('pages.processStgLib_Msg4')" prop="encWriteSfx">
          <el-input v-model="temp.encWriteSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button style="margin-left: 1px" type="primary" size="mini" @click="handleFileSuffixImport('encWriteSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>

        <FormItem :label="$t('pages.processStgLib_Msg6')" prop="encOpenSfx">
          <el-input v-model="temp.encOpenSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.processStgLib_Msg5')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button style="margin-left: 1px" type="primary" size="mini" @click="handleFileSuffixImport('encOpenSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>

        <FormItem prop="enablePast">
          <el-checkbox v-model="temp.enablePast" :true-label="65535" :false-label="0">{{ $t('pages.processStgLib_Msg7') }}</el-checkbox>
        </FormItem>
        <batch-fingerprint v-show="showFinger" ref="batchFingerprint" :open-match="true" :temp="temp" />
        <open-net v-show="showNet" :temp="temp" />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
  </div>
</template>

<script type="text/jsx">
import { createStg, getStgByName, updateStg, validProcessName } from '@/api/dataEncryption/encryption/processStgLib'
import { createProcessVersion } from '@/utils/fingerprint'
import BatchFingerprint from './OpenFingerprint/batchFingerprint'
import UploadProcess from '@/views/behaviorManage/application/appBlock/uploadProcess'
import OpenNet from '@/views/dataEncryption/encryption/processStgLib/openNet'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'

export default {
  name: 'ProcessAddDlg',
  components: { BatchFingerprint, UploadProcess, OpenNet, FileSuffixLibImport },
  props: {
    showNet: { type: Boolean, default: true },
    showFinger: { type: Boolean, default: true },    // 是否隐藏掉列表内容
    osType: { type: Number, default: 1 }, // 进程系统类型：1-windows，2-linux，4-mac
    appendToBody: { type: Boolean, default: false },
    groupTreeData: {
      type: Array,
      default() {
        return []
      }
    },
    addGroup: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      isUpdateMode: false, // 是否修改form表单模式
      temp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        groupId: 0,
        osType: this.osType,
        enablePast: 0,
        decReadSfx: '',
        encWriteSfx: '',
        encOpenSfx: '',
        disableNet: 0,
        checkMd5: 0,
        md5Level: 3,
        useSelf: 0,
        fingerprintGroup: 0,
        processName: '',
        productName: '',
        productVersion: '',
        originalFilename: '',
        fileDesc: '',
        companyName: '',
        internalName: '',
        legalCopyright: '',
        fileMd5: '',
        quicklyMd5: '',
        softSign: '',
        relaFingerPrint: [],
        netConfigs: [],
        matchRealName: 0
      },
      rules: {
        groupId: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        processName: [
          { required: true, message: this.$t('pages.processStgLib_Msg21'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('pages.appGroup_text24'), trigger: 'blur' }
          /*, { validator: this.stgNameValidator, trigger: 'blur' }*/
        ],
        encOpenSfx: [
          { validator: this.suffixValidator, trigger: 'blur' }
        ],
        md5Level: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.$t('pages.processStgLib_Msg23'),
        create: this.$t('pages.processStgLib_Msg24')
      },
      importFileSuffixType: '',
      suffixMaxLength: 200
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
  },
  methods: {
    handleCreate(groupId) {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.isUpdateMode = false
      this.temp.groupId = !groupId ? 0 : groupId
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.dialogStatus = !row ? 'create' : 'update'
      this.isUpdateMode = !!row
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.batchFingerprint && this.$refs.batchFingerprint.formatHandle(row)
        this.temp = JSON.parse(JSON.stringify(row)) // copy obj
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    groupSelectChange(data) {
      this.temp.groupId = data
      this.$refs.dataForm.validateField('groupId')
    },
    afterUpload(appData) {
      const obj = {
        processName: appData.processName,
        processVersion: createProcessVersion(appData.processName, appData.productVersion),
        componentVersion: appData.productVersion,
        propertyMd5: appData.propertyMd5,
        fileMd5: appData.fileMd5,
        quicklyMd5: appData.quicklyMd5,
        checkMd5: appData.propertyMd5 ? 3 : 1,
        propertyMark: appData.propertyMark,
        remark: ''
      }
      if (this.temp.osType != 1) {
        // 非window终端只支持全量md5防伪冒验证方式
        obj.checkMd5 = 2
      }
      this.temp.relaFingerPrint.splice(0, this.temp.relaFingerPrint.length, obj)
      this.$refs.dataForm.validateField('processName')
    },
    resetTemp() {
      // 必须要使用JSON.parse(JSON.stringify来复制对象，
      // 不然的话defaultTemp里面如果有数组，会把数组地址复制过去，从而导致修改temp的数组内容而影响到了defaultTemp里面的内容
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.temp.osType = this.osType
      this.isUpdateMode = false
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    createStg() {
      this.$refs.batchFingerprint.formatSave(this.temp)
      createStg(this.temp).then(resp => {
        this.submitting = false
        this.dialogVisible = false
        this.$emit('success', resp.data)
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
      }).catch(() => {
        this.submitting = false
      })
    },
    validProcess() {
      if (this.temp.checkMd5 == 1 && this.temp.relaFingerPrint.length == 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text1'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (this.temp.disableNet == 1 && this.temp.netConfigs.length == 0) {
        this.$message({
          message: this.$t('pages.processStgLib_Msg29'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      if (this.temp.relaFingerPrint.length > 0) {
        // 验证是否存在进程名称不一样的指纹
        // 这个场景主要是防止用户已经上传了指纹信息，但是又把主进程名称改掉的问题
        const difPros = []
        this.temp.relaFingerPrint.forEach(item => {
          if (item.processName.toLowerCase() != this.temp.processName.toLowerCase()) {
            difPros.push(item)
          }
        })
        if (difPros.length > 0) {
          this.$message({
            message: this.$t('pages.appGroup_text2'),
            type: 'error',
            duration: 2000
          })
          return false
        }
      }
      return true
    },
    createData() {
      if (this.temp.groupId == undefined) {
        this.$message({
          message: this.$t('pages.outgoingProcess_text5'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.validProcess()) {
            this.submitting = false
            return
          }
          validProcessName({ processNameStr: this.temp.processName, osType: this.osType }).then(res => {
            if (res.data != null && res.data.length > 0) {
              this.$confirmBox(`${this.$t('pages.processStgLib_Msg30', { name: res.data.join('、') })}`, this.$t('text.prompt')).then(() => {
                this.createStg()
              }).catch(() => {
                this.submitting = false
              })
            } else {
              this.createStg()
            }
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.validProcess()) {
            this.submitting = false
            return
          }
          const tempData = Object.assign({}, this.temp)
          this.$refs.batchFingerprint.formatSave(tempData)
          updateStg(tempData).then(resp => {
            this.submitting = false
            this.dialogVisible = false
            this.$emit('success', resp.data)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    stgNameValidator(rule, value, callback) {
      getStgByName({ name: value, osType: this.osType }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    suffixValidator(rule, value, callback) {
      if (value == '*.*') {
        callback(new Error(this.$t('pages.appGroup_text5')))
      } else {
        callback()
      }
    },
    handleFileSuffixImport(type) {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
      this.importFileSuffixType = type
    },
    duplicateRemoval(nv, ov) {
      let union_suffix = null
      if (ov == null || ov === '') {
        union_suffix = [...new Set(nv.split('|'))].join('|')
      } else {
        union_suffix = [...new Set((ov + '|' + nv).split('|'))].join('|')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      return union_suffix
    },
    importFileSuffix(suffix) {
      if (this.importFileSuffixType === 'decReadSfx') {
        this.temp.decReadSfx = this.duplicateRemoval(suffix, this.temp.decReadSfx)
      } else if (this.importFileSuffixType === 'encWriteSfx') {
        this.temp.encWriteSfx = this.duplicateRemoval(suffix, this.temp.encWriteSfx)
      } else if (this.importFileSuffixType === 'encOpenSfx') {
        this.temp.encOpenSfx = this.duplicateRemoval(suffix, this.temp.encOpenSfx)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .no-header-tab>>>.el-tabs__header{
    display: none;
  }
  .no-header-tab>>>.el-tabs__content{
    height: 100%;
  }
  .align-center {
    display: flex;
    align-items: center;
  }
</style>
