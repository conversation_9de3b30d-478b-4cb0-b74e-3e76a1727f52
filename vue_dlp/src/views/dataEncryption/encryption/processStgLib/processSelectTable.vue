<template>
  <el-container>
    <el-aside width="210px">
      <tree-menu
        ref="groupTree"
        :data="treeData"
        :height="height + 40"
        :multiple="multiple"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="treeNodeClick"
      />
    </el-aside>
    <el-main>
      <div class="toolbar">
        <el-row>
          <el-col :span="9"><label style="font-size: 15px;"></label></el-col>
          <el-col :span="15">
            <div style="float: right;">
              <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('table.processName')" style="width: 225px;"/>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <grid-table
        ref="InfoList"
        :height="height"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="multiple"
        :page-sizes="[ 10, 20, 50, 100, 200 ]"
        pager-small
        @currentChange="currentChange"
      />
    </el-main>
  </el-container>
</template>

<script>
export default {
  name: 'ProcessSelectTable',
  props: {
    height: { type: Number, default: 420 },
    multiple: { type: Boolean, default: true },
    libTreeNode: {
      type: Function,
      default: null
    },
    libFileSuffixPage: {
      type: Function,
      default: null
    },
    osType: { type: Number, default: 1 } // 进程系统类型：1-windows，2-linux，4-mac
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'programName', width: '150', fixed: true },
        { prop: 'groupId', label: 'appType', width: '120', formatter: this.groupFormatter },
        // { prop: 'decReadSfx', label: 'fileSuffixOpen', width: '150' },
        // { prop: 'encWriteSfx', label: 'fileSuffixSave', width: '150' },
        // { prop: 'encOpenSfx', label: 'fileSuffixVisit', width: '150' },
        // { prop: 'checkMd5', hidden: false, label: 'enableAntiCounterfeiting', width: '100', formatter: this.md5LevelFormatter },
        // { prop: 'disableNet', label: 'restrictInternetAccess', hidden: !this.showNet, width: '80', formatter: this.disableNetFormatter },
        // { prop: 'enablePast', label: 'pasteEncryption', width: '100', formatter: this.enableFormatter },
        { prop: 'name', label: 'fileDescription', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: this.multiple,
          buttons: [
            { label: 'addSelectedApp', click: this.singleSelectApp }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: null,
        groupIds: '',
        osType: this.osType
      },
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: [] }],
      defaultExpandedKeys: ['G0'],
      currentData: null
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    suffixTable: function() {
      return this.$refs['InfoList']
    }
  },
  created() {
    // this.show()
  },
  activated() {
    // this.show()
  },
  methods: {
    show() {
      this.query.searchInfo = null
      this.query.groupId = undefined
      this.query.groupIds = ''
      this.currentData = null
      this.loadGroupTree()
      this.$nextTick(() => {
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNodes()
      })
    },
    treeNodeClick: function(data, node, element) {
      var groupIds = this.getChildGroup(node)
      if (data.dataId === '0') {
        this.query.groupId = data.dataId
        this.query.groupIds = ''
      } else {
        this.query.groupId = undefined
        this.query.groupIds = groupIds.substr(0, groupIds.length - 1)
      }
      this.handleFilter()
    },
    getChildGroup: function(node) {
      var groupIds = node.data.dataId + ','
      if (node.childNodes != null) {
        node.childNodes.forEach(child => {
          groupIds += this.getChildGroup(child)
        })
      }
      return groupIds
    },
    handleFilter() {
      this.query.page = 1
      this.query.osType = this.osType
      this.suffixTable && this.suffixTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const newOption = Object.assign(this.query, option)
      return this.libFileSuffixPage(newOption)
    },
    loadGroupTree: function() {
      this.libTreeNode({ osType: this.osType }).then(respond => {
        this.treeData[0].children = respond.data
      })
    },
    currentChange: function(currentRow) {
      this.currentData = currentRow
    },
    async getSelectedDatas() {
      var datas = null
      if (this.multiple) { // 说明支持多选
        datas = this.suffixTable.getSelectedDatas()
        const groupNodes = this.$refs['groupTree'].getCheckedNodes()
        const groupIds = []
        groupNodes.forEach(data => groupIds.push(data.dataId))
        if (groupIds.length > 0) {
          await this.libFileSuffixPage({ groupIds: groupIds.join(',') }).then(resp => {
            datas.splice(0, 0, ...resp.data.items)
          })
        }
      } else if (this.currentData != null) {
        datas = [this.currentData]
      }
      return datas
    },
    groupClick: function(row) {
      const node = this.getTree().getNode('G' + row.groupId);
      if (null == node) {
        return
      }
      this.expandNodeParent(node)
      this.libTypeNodeCheckChange(null, node)
      this.$nextTick(() => {
        this.getTree().setCurrent(node)
      })
    },
    expandNodeParent(node) {
      if (node.parent) {
        node.parent.expanded = true
        this.expandNodeParent(node.parent)
      }
    },
    enableFormatter(row, data) {
      return data > 0 ? this.$t('pages.allow') : this.$t('pages.notAllow')
    },
    groupFormatter(row) {
      return this.getGroupLabel(this.treeData[0], row);
    },
    getGroupLabel(arr, row) {
      if (arr.children !== undefined && arr.children !== null) {
        for (let i = 0, len = arr.children.length; i < len; i++) {
          if (arr.children[i].dataId == row.groupId) {
            return arr.children[i].label
          }
          const label = this.getGroupLabel(arr.children[i], row);
          if (label !== null) {
            return label;
          }
        }
      }
      return null;
    },
    md5LevelFormatter(row, data) {
      return data == 1 ? this.$t('pages.enable') : this.$t('pages.notEnabled')
    },
    disableNetFormatter(row, data) {
      return data == 1 ? this.$t('pages.limit') : this.$t('pages.notLimit')
    },
    singleSelectApp(row) {
      this.$emit('single-select', row);
    }
  }
}
</script>
