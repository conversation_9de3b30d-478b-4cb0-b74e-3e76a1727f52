<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu ref="selfLibTree" :data="selfLibTreeData" :render-content="renderContent" @node-click="stgNodeClickFunc" />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">{{ $t('table.search') }}</el-button>
        </div>
      </div>
      <el-tabs ref="tabs" v-model="activeTabName" type="card" style="height: calc( 100% - 40px);" @tab-click="tabClickFunc" >
        <el-tab-pane :label="$t('pages.processStgLib_Msg56')" name="childStgTab">
          <process-config-child-stg ref="childStg" :process-stg="selectedNode" @selectChange="selectRowChangeFunc" @parentProcessClick="parentProcessClick"></process-config-child-stg>
        </el-tab-pane>
        <!-- <el-tab-pane label="限制上网策略" name="disableNetTab">
          <process-config-disable-net ref="disableNet" :process-stg="selectedNode" @selectChange="selectRowChangeFunc"></process-config-disable-net>
        </el-tab-pane> -->
        <!-- <el-tab-pane label="防伪冒策略" name="checkMD5Tab">
          <process-config-md5 ref="checkMD5" :process-stg="selectedNode" @selectChange="selectRowChangeFunc"></process-config-md5>
        </el-tab-pane> -->
        <el-tab-pane v-if="hasPermission('E2B')" :label="$t('pages.processStgLib_Msg57')" name="otherStgTab">
          <process-config-other ref="otherStg" :process-stg="selectedNode" @selectChange="selectRowChangeFunc" @parentProcessClick="parentProcessClick"></process-config-other>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { getStgTree } from '@/api/dataEncryption/encryption/processStgLibConfig'
import { isSameTimestamp, initTimestamp } from '@/utils'
import ProcessConfigChildStg from './configChild'
// import ProcessConfigDisableNet from './configNet'
import ProcessConfigOther from './configOther'

export default {
  name: 'Config',
  components: { ProcessConfigOther/*, ProcessConfigDisableNet*/, ProcessConfigChildStg },
  props: {
    osType: { type: Number, default: 1 } // 进程系统类型：1-windows，2-linux，4-mac
  },
  data() {
    return {
      colModel: [],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      activeTabName: 'childStgTab',
      selfLibTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib'), parentId: '', children: [] }],
      selectedNode: {},
      configMap: {
        'childStgTab': 'childStg',
        'disableNetTab': 'disableNet',
        'checkMD5Tab': 'checkMD5',
        'otherStgTab': 'otherStg'
      }
    }
  },
  computed: {

  },
  created() {
    initTimestamp(this)
    this.loadLibTypeTree()
  },
  activated() {
    if (!isSameTimestamp(this, 'ProcessStgLib')) {
      this.loadLibTypeTree()
    }
  },
  methods: {
    resetHeight() {
      this.handleFilter()
    },
    tabClickFunc: function(pane, event) {
      this.query.searchInfo = null
      this.handleFilter()
    },
    getTree() {
      return this.$refs['selfLibTree']
    },
    getConfig() {
      const configRef = this.configMap[this.activeTabName]
      return this.$refs[configRef]
    },
    stgNodeClickFunc: function(tabName, checkedNode) {
      checkedNode.expanded = true
      if (checkedNode) {
        this.query.groupId = checkedNode.data.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.addBtnAble = !!checkedNode && checkedNode.data.id.indexOf('G' + checkedNode.data.dataId) < 0
      this.selectedNode = this.addBtnAble ? checkedNode.data : {}
      if (this.addBtnAble) {
        this.handleFilter()
      }
    },
    loadLibTypeTree: function() {
      getStgTree({ osType: this.osType }).then(respond => {
        this.selfLibTreeData[0].children = respond.data
      })
    },
    selectRowChangeFunc(rowDatas) {
      this.deleteable = false
      if (rowDatas && rowDatas.length > 0) {
        this.deleteable = true
      }
    },
    refresh() {
      this.query = {
        page: 1,
        searchInfo: '',
        groupId: undefined
      }
      this.selectedNode = {}
      this.$refs.selfLibTree.clearSelectedNode()
      this.handleFilter()
    },
    parentProcessClick: function(row) {
      const node = this.$refs['selfLibTree'].getNode('L' + row.processStgId);
      if (null == node) {
        return
      }
      this.expandNodeParent(node)
      this.stgNodeClickFunc(null, node)
      this.$nextTick(() => {
        this.$refs['selfLibTree'].setCurrent(node)
      })
    },
    expandNodeParent(node) {
      if (node.parent) {
        node.parent.expanded = true
        this.expandNodeParent(node.parent)
      }
    },
    handleCreate() {
      this.getConfig().handleCreate()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        this.getConfig().deleteData()
      }).catch(() => {})
    },
    handleFilter() {
      this.query.page = 1
      this.getConfig().searchData(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleConfig() {
      this.$router.push('/dataEncryption/encryption/processStgLibConfig')
    },
    renderContent(h, { node, data, store }) {
      const icon = data.id.indexOf('G' + data.dataId) >= 0 ? <svg-icon icon-class='contentStgGroup'/> : ''
      return (<span>{icon} {node.label}</span>)
    }
  }
}
</script>
