<template>
  <div class="table-container">
    <grid-table ref="childStgList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="(rowDatas) => {this.$emit('selectChange', rowDatas)}"/>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px" style="width: 550px;">
        <FormItem :label="$t('pages.processStgLib_Msg58')" prop="processStgId">
          <el-input v-model="processStg.label" class="input-with-button" :disabled="true"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.subProcessName')" prop="processName">
          <el-input v-model="temp.processName" class="input-with-button" maxlength="100"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.processStgLib_Msg3')" prop="decReadSfx">
          <el-input v-model="temp.decReadSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" size="mini" @click="handleFileSuffixImport('decReadSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.processStgLib_Msg4')" prop="encWriteSfx">
          <el-input v-model="temp.encWriteSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" size="mini" @click="handleFileSuffixImport('encWriteSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.processStgLib_Msg6')" prop="encOpenSfx">
          <el-input v-model="temp.encOpenSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" size="mini" @click="handleFileSuffixImport('encOpenSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>
        <FormItem>
          <el-checkbox v-model="priorityCode" :true-label="1" :false-label="0">{{ $t('pages.processStgLib_Msg60') }}</el-checkbox>
        </FormItem>
        <FormItem>
          <el-checkbox v-model="loopCode" :true-label="2" :false-label="0">{{ $t('pages.processStgLib_Msg61') }}</el-checkbox>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="showDlg=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
  </div>
</template>

<script>
import { getChildStgPage, createChildStg, updateChildStg, deleteChildStg, getChildStgByName } from '@/api/dataEncryption/encryption/processStgLibConfig'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
export default {
  name: 'ProcessConfigChildStg',
  components: { FileSuffixLibImport },
  props: {
    processStg: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'programName', width: '150', sort: 'custom', fixed: true },
        { prop: 'parentProcessName', label: 'parentProcess', width: '150', type: 'button',
          buttons: [{ formatter: this.parentProcessFormatter, click: this.parentProcessClick }]
        },
        { prop: 'decReadSfx', label: 'fileSuffixOpen', width: '200' },
        { prop: 'encWriteSfx', label: 'fileSuffixSave', width: '200' },
        { prop: 'encOpenSfx', label: 'fileSuffixVisit', width: '200' },
        { label: 'subPriority', width: '150', formatter: this.priorityFormatter },
        { label: 'infiniteSon', width: '150', formatter: this.loopFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', click: this.handleUpdate, isShow: () => { return this.processStg.id !== undefined } }
          ]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        processName: '',
        processStgId: undefined,
        code: undefined,
        decReadSfx: '',
        encWriteSfx: '',
        encOpenSfx: ''
      },
      priorityCode: 0,
      loopCode: 0,
      rules: {
        processName: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.processNameValidator, trigger: 'blur' }
        ]
      },
      query: {},
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      textMap: {
        update: this.$t('pages.processStgLib_Msg62'),
        create: this.$t('pages.processStgLib_Msg63')
      },
      importFileSuffixType: '',
      suffixMaxLength: 200
    }
  },
  computed: {
    gridTable() {
      return this.$refs['childStgList']
    }
  },
  watch: {

  },
  created() {

  },
  methods: {
    rowDataApi: function(option) {
      return getChildStgPage(option)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.groupId = this.query.groupId
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      const codeList = this.numToList(this.temp.code, 2)
      this.priorityCode = codeList.indexOf(1) >= 0 ? 1 : 0
      this.loopCode = codeList.indexOf(2) >= 0 ? 2 : 0
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.processStgId = this.processStg.dataId
          this.temp.code = this.getSum([this.priorityCode, this.loopCode])
          createChildStg(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.processStgId = this.processStg.dataId
          this.temp.code = this.getSum([this.priorityCode, this.loopCode])
          const tempData = Object.assign({}, this.temp)
          updateChildStg(tempData).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.updateRowData(this.temp)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    deleteData() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      deleteChildStg({ ids: toDeleteIds.join(',') }).then(respond => {
        this.gridTable.deleteRowData(toDeleteIds)
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
      })
    },
    processNameValidator(rule, value, callback) {
      getChildStgByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    parentProcessFormatter(row) {
      return row.parentProcessName
    },
    parentProcessClick(row) {
      this.$emit('parentProcessClick', row)
    },
    priorityFormatter(row, data) {
      return this.numToList(row.code, 2).indexOf(1) >= 0 ? this.$t('text.yes') : ''
    },
    loopFormatter(row, data) {
      return this.numToList(row.code, 2).indexOf(2) >= 0 ? this.$t('text.yes') : ''
    },
    groupFormatter(row, data) {
      return this.processStg.label
    },
    handleFileSuffixImport(type) {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
      this.importFileSuffixType = type
    },
    duplicateRemoval(nv, ov) {
      let union_suffix = null
      if (ov == null || ov === '') {
        union_suffix = [...new Set(nv.split('|'))].join('|')
      } else {
        union_suffix = [...new Set((ov + '|' + nv).split('|'))].join('|')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      return union_suffix
    },
    importFileSuffix(suffix) {
      if (this.importFileSuffixType === 'decReadSfx') {
        this.temp.decReadSfx = this.duplicateRemoval(suffix, this.temp.decReadSfx)
      } else if (this.importFileSuffixType === 'encWriteSfx') {
        this.temp.encWriteSfx = this.duplicateRemoval(suffix, this.temp.encWriteSfx)
      } else if (this.importFileSuffixType === 'encOpenSfx') {
        this.temp.encOpenSfx = this.duplicateRemoval(suffix, this.temp.encOpenSfx)
      }
    }
  }
}
</script>
