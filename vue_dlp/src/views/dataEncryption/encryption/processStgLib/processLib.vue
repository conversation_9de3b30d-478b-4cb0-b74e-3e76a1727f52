<template>
  <div
    v-loading="loading"
    class="app-container"
    :element-loading-text="$t('pages.importing')"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div v-if="listable" class="tree-container" :class="showTree?'':'hidden'">
      <el-tabs v-model="activeTabName" :class="{'no-header-tab': osType!=1}" @tab-click="tabClickFunc">
        <el-tab-pane :label="$t('pages.customLibrary')" name="selfLib">
          <tree-menu
            ref="selfLibTree"
            :data="selfLibTreeData"
            :default-expanded-keys="selfLibExpandKeys"
            :render-content="renderContent"
            @node-click="libTypeNodeCheckChange"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.templateLibrary')" name="inLib">
          <tree-menu
            ref="inLibTree"
            multiple
            :data="inLibTreeData"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="libTypeNodeCheckChange"
            @check="inLibCkeckFunc"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <!--<el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>-->
        <el-tooltip v-show="!isSelfLib" :content="$t('pages.processStgLib_Msg')" class="item" effect="dark" placement="bottom">
          <el-button v-show="!isSelfLib" icon="el-icon-download" size="mini" :disabled="!importAble" @click="handleImport">
            {{ $t('pages.fullImport') }}
          </el-button>
        </el-tooltip>
        <el-tooltip v-show="!isSelfLib" :content="$t('pages.processStgLib_Msg1')" class="item" effect="dark" placement="bottom">
          <el-button v-show="!isSelfLib" icon="el-icon-download" size="mini" :disabled="!importAble" @click="handleCustomImport">
            {{ $t('pages.customImport') }}
          </el-button>
        </el-tooltip>
        <el-tooltip v-show="!isSelfLib" class="item" :content="$t('pages.processStgLib_Msg2')" effect="dark" placement="right">
          <i class="el-icon-question"/>
        </el-tooltip>
        <el-button v-show="isSelfLib" type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button v-show="isSelfLib" type="primary" icon="el-icon-plus" size="mini" @click="handleBatchAdd">
          {{ $t('pages.batchAdd') }}
        </el-button>
        <el-button v-show="isSelfLib" icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <el-button v-show="isSelfLib && showFinger" type="primary" icon="el-icon-edit" size="mini" @click="handleBatchRelaMd5">
          {{ $t('pages.importFingerprint') }}
        </el-button>
        <el-button v-show="isSelfLib" type="primary" icon="el-icon-download" size="mini" :disabled="!addBtnAble" @click="handleExportStg">
          {{ $t('pages.downloadStrategy') }}
        </el-button>
        <el-button v-show="isSelfLib" type="primary" icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleBatchEdit">
          {{ $t('pages.batchModification') }}
        </el-button>
        <el-button v-show="isSelfLib" icon="el-icon-upload2" size="mini" @click="handleImportFile">{{ $t('button.import') }}</el-button>
        <el-button v-show="isSelfLib" v-if="osType==1" icon="el-icon-setting" size="mini" @click="handleConfig">
          {{ $t('pages.fileOutgoingConfig') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.typeOptions1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="urlList" :col-model="colModel" :row-data-api="rowDataApi" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-if="importVisible"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.processStgLib_Msg8')"
      :visible.sync="importVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="importForm" :rules="fingerRules" :model="tempF" label-position="top" label-width="80px" style="width: 300px; margin-left:20px;">
        <FormItem :label="$t('pages.repeatNameDealType')" prop="importType">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.isGlobalUnique') }}<br/>
              {{ $t('pages.importAndUpdate_Msg') }}<br/>
              {{ $t('pages.importAndIgnore_Msg') }}<br/>
              {{ $t('pages.importAndAdd_Msg') }}<br/>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-radio-group v-model="tempF.importType" >
            <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.importAndAdd') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.processStgLib_Msg17')" prop="typeId">
          <tree-select
            v-model="tempF.typeId"
            :data="selectLibTypeTreeData"
            node-key="dataId"
            :checked-keys="checkedParentType"
            :width="296"
            @change="parentTypeSelectChange"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="customImport">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="importVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="downloadDialogVisible"
      width="600px"
      @dragDialog="handleDrag"
      @close="downloadClose"
    >
      <Form ref="downloadDataForm" :model="tempF" label-position="right" label-width="120px" style="width: 500px; margin-left:20px;">

        <FormItem :label="$t('pages.appType')" prop="typeId">
          <tree-select v-model="tempF.typeId" :data="selectLibTypeTreeData" node-key="dataId" :checked-keys="checkedParentType" :width="296" @change="downloadSelectChange" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <common-downloader
          :loading="submitting"
          :name="filename"
          :button-name="$t('button.confirm')"
          button-type="primary"
          button-icon=""
          @download="downloadConfirm"
        />
        <el-button @click="downloadDialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.processStgLib_Msg20')"
      show-parent
      :group-label="$t('pages.groupName1')"
      :parent-label="$t('pages.install_Msg44')"
      :group-tree-data="selfLibTreeData"
      :add-func="createLibType"
      :update-func="updateLibType"
      :delete-func="deleteStgType"
      :edit-valid-func="getLibTypeByName"
      @addEnd="loadLibTypeTree"
      @updateEnd="loadLibTypeTree"
      @deleteEnd="loadLibTypeTree"
    />

    <process-add-dlg ref="processAddDlg" :os-type="osType" :group-tree-data="selectLibTypeTreeData" :show-finger="showFinger" :show-net="showNet" @success="handleFilter"/>
    <batch-upload ref="batchUpload" :os-type="osType" :type-tree-data="selectLibTypeTreeData" :append-to-body="false" :show-finger="showFinger" :show-net="showNet" :group-id="typeId" @success="handleFilter"/>
    <import-stg ref="importProcessDlg" :os-type="osType" accept=".tip" data-type="1" :term-able="false" :user-able="false" :auto-name="false" :import-type2="importType2" @success="importProcessSuccess"/>
    <batch-edit ref="batchEdit" :os-type="osType" @success="handleFilter"/>
    <batch-rela-md5 ref="batchRelaMd5" :os-type="osType" :append-to-body="false" @success="handleFilter"/>
  </div>
</template>

<script type="text/jsx">
import {
  getStgTypeTree, getStgTypeByName, getStgPage, createStgType, updateStgType, deleteStgType, deleteStg,
  getImportProgress, importStg, exportStg
} from '@/api/dataEncryption/encryption/processStgLib'
import { isSameTimestamp, initTimestamp } from '@/utils'
import BatchUpload from '@/views/dataEncryption/encryption/processStgLib/batchUploadProcess'
import ImportStg from '@/views/common/importStg'
import BatchEdit from '@/views/dataEncryption/encryption/processStgLib/batchEditProcess'
import BatchRelaMd5 from '@/views/dataEncryption/encryption/processStgLib/batchRelaMd5'
import EditGroupDlg from '@/views/common/editGroupDlg'
import ProcessAddDlg from './processAddDlg'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'ProcessLib',
  components: { ProcessAddDlg, ImportStg, BatchUpload, BatchEdit, BatchRelaMd5, EditGroupDlg, CommonDownloader },
  props: {
    showNet: { type: Boolean, default: true },
    showFinger: { type: Boolean, default: true },    // 是否隐藏掉列表内容
    listable: { type: Boolean, default: true },
    osType: { type: Number, default: 1 } // 进程系统类型：1-windows，2-linux，4-mac
  },
  data() {
    return {
      filename: '透明加密策略库.tip',
      loading: false,
      colModel: [
        { prop: 'processName', label: 'programName', width: '150', fixed: true },
        { prop: 'groupId', label: 'appType', width: '120', type: 'button',
          buttons: [{ formatter: this.groupFormatter, click: this.groupClick }]
        },
        { prop: 'decReadSfx', label: 'fileSuffixOpen', width: '150' },
        { prop: 'encWriteSfx', label: 'fileSuffixSave', width: '150' },
        { prop: 'encOpenSfx', label: 'fileSuffixVisit', width: '150' },
        { prop: 'checkMd5', hidden: !this.showFinger, label: 'enableAntiCounterfeiting', width: '100', formatter: this.md5LevelFormatter },
        { prop: 'disableNet', label: 'restrictInternetAccess', hidden: !this.showNet, width: '80', formatter: this.disableNetFormatter },
        { prop: 'enablePast', label: 'pasteEncryption', width: '100', formatter: this.enableFormatter },
        { prop: 'name', label: 'fileDescription', width: '150' },
        { label: '', hidden: false, type: 'button', fixed: 'right', fixedWidth: 50,
          buttons: [
            { label: 'edit', click: this.handleUpdate, isShow: () => { return this.isSelfLib } }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: null,
        groupIds: '',
        osType: this.osType
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      importAble: false,
      isSelfLib: true,
      activeTabName: 'selfLib',
      selfLibTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: [] }],
      selfLibExpandKeys: ['G0'],
      inLibTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: [] }],
      defaultExpandedKeys: ['G0'],
      selfLabelMap: {},
      inLabelMap: {},
      curLabelMap: {},
      selectLibTypeTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: [] }],
      typeId: null,
      checkedParentType: [],
      tempF: {},
      defaultTempF: { // 表单字段
        typeId: null,
        decReadSfx: '',
        encWriteSfx: '',
        encOpenSfx: '',
        enablePast: 0,
        checkMd5: 0,
        md5Level: 3,
        fingerPrintIds: [],
        processList: []
      },
      fingerRules: {
        importType: [{ required: true, message: this.$t('pages.processStgLib_Msg22'), trigger: 'blur' }],
        fingerPrintIds: [{ required: true, message: this.$t('pages.appGroup_text22'), trigger: 'blur' }],
        typeId: [{ required: true, message: this.$t('pages.outgoingProcess_text5'), trigger: 'blur' }],
        encOpenSfx: [{ validator: this.suffixValidator, trigger: 'blur' }]
      },
      downloadDialogVisible: false,
      dialogStatus: '',
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      },
      submitting: false,
      textMap: {
        update: this.$t('pages.processStgLib_Msg23'),
        create: this.$t('pages.processStgLib_Msg24'),
        createGroup: this.$t('pages.processStgLib_Msg25'),
        updateGroup: this.$t('pages.processStgLib_Msg26'),
        download: this.$t('pages.downloadStrategy')
      },
      importVisible: false,
      importType2: {
        name: this.$t('pages.importAndAdd'),
        tipMsg: this.$t('pages.importAndAdd_Msg')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['urlList']
    }
  },
  created() {
    initTimestamp(this)
    this.resetTemp()
    this.loadLibTypeTree()
    this.loadInLibTypeTreeData()
  },
  activated() {
    if (!isSameTimestamp(this, 'ProcessStg')) {
      this.gridTable.execRowDataApi(this.query)
    }
    // this.loadLibTypeTree()
    // this.loadInLibTypeTreeData()
  },
  methods: {
    deleteStgType,
    tabClickFunc(tab, event) {
      this.isSelfLib = tab.name === 'selfLib'
      this.query.groupId = null
      this.query.groupIds = ''
      this.query.searchInfo = null
      this.$refs.selfLibTree && this.$refs.selfLibTree.clearSelectedNode()
      this.$refs.inLibTree && this.$refs.inLibTree.clearSelectedNode()
      this.handleFilter()
    },
    getTree() {
      return this.isSelfLib ? this.$refs['selfLibTree'] : this.$refs['inLibTree']
    },
    rowDataApi: function(option) {
      const queryParam = Object.assign({}, this.query, option, { isModule: !this.isSelfLib })
      if (!this.isSelfLib) {
        queryParam.groupId = undefined
      }
      return getStgPage(queryParam)
    },
    // table数据更新后，切换labelMap的数据源
    afterLoad() {
      this.updateCurLabel()
      this.colModel[9].hidden = !this.isSelfLib
    },
    updateCurLabel() {
      this.curLabelMap = this.isSelfLib ? this.selfLabelMap : this.inLabelMap
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
      const nodes = this.$refs.inLibTree.getCheckedNodes()
      this.importAbleChange({ checkedKeys: nodes })
    },
    importAbleChange: function(checkedInfo) {
      this.importAble = this.deleteable
      if (!this.importAble && checkedInfo) {
        this.importAble = checkedInfo.checkedKeys.length > 0
      }
    },
    listNodeIdAndChildNodeIds(checkNodeIds, checkedData) {
      checkNodeIds.push(checkedData.dataId)
      if (checkedData.children) {
        checkedData.children.forEach(data => {
          this.listNodeIdAndChildNodeIds(checkNodeIds, data)
        })
      }
    },
    libTypeNodeCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedNode && checkedNode.data.dataId !== '0'
      const checkNodeIds = []
      this.listNodeIdAndChildNodeIds(checkNodeIds, checkedNode.data)
      this.query.groupIds = checkNodeIds.join(',')
      if (checkedNode) {
        this.query.groupId = checkedNode.data.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    inLibCkeckFunc: function(nodeData, checkedInfo) {
      this.importAbleChange(checkedInfo)
    },
    // 获取自定义库的树数据
    loadLibTypeTree: function(data) {
      getStgTypeTree({ isModule: false, osType: this.osType }).then(respond => {
        this.selfLibTreeData[0].children = respond.data
        this.selfLabelMap = this.getNodeLabelMap(this.selfLabelMap, respond.data, true)
        this.updateCurLabel()
        if (data) {
          let parentId
          if (typeof data == 'string') {
            const node = this.$refs['selfLibTree'].getNode('G' + data)
            parentId = node.data.parentId
          } else {
            parentId = 'G' + data.parentId
          }
          this.selfLibExpandKeys.splice(0, 1, parentId)
        }
      })
    },
    // 获取模板库的树数据
    loadInLibTypeTreeData: function() {
      getStgTypeTree({ isModule: true, osType: this.osType }).then(respond => {
        this.inLibTreeData[0].children = respond.data
        this.inLabelMap = this.getNodeLabelMap(this.inLabelMap, respond.data, true)
        this.updateCurLabel()
      })
    },
    // 通过树数据获取label的map数据
    getNodeLabelMap(labelMap, treeData, init) {
      if (init) {
        labelMap = {}
      }
      treeData.forEach(data => {
        labelMap[data.dataId] = data.label
        if (data.children) {
          this.getNodeLabelMap(labelMap, data.children, false)
        }
      })
      return labelMap
    },
    parentTypeSelectChange(data) {
      this.tempF.typeId = data
    },
    setSelectLibTypeTreeData() {
      const isTypeTree = this.dialogStatus.indexOf('G') > 0
      const treeData = this.isSelfLib ? this.selfLibTreeData[0] : this.inLibTreeData[0]
      this.selectLibTypeTreeData.splice(0)
      if (isTypeTree) {
        this.selectLibTypeTreeData.push(treeData)
      } else {
        treeData.children.forEach((nodeData) => {
          this.selectLibTypeTreeData.push(nodeData)
        })
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      // 必须要使用JSON.parse(JSON.stringify来复制对象，
      // 不然的话defaultTemp里面如果有数组，会把数组地址复制过去，从而导致修改temp的数组内容而影响到了defaultTemp里面的内容
      this.tempF = JSON.parse(JSON.stringify(this.defaultTempF))
    },
    refresh() {
      this.query.searchInfo = ''
      this.query.groupId = undefined
      this.$refs.selfLibTree.clearSelectedNode()
      this.handleFilter()
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreateLibType(data) {
      this.$refs['editGroupDlg'].handleCreate(data.dataId)
    },
    handleUpdateLibType: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleDeleteLibType(data) {
      this.$refs['editGroupDlg'].handleDelete(data.dataId)
    },
    createLibType(data) {
      const tempData = Object.assign({}, data, { osType: this.osType })
      return createStgType(tempData)
    },
    updateLibType(data) {
      const tempData = Object.assign({}, data, { osType: this.osType })
      return updateStgType(tempData)
    },
    getLibTypeByName(data) {
      const tempData = Object.assign({}, data, { osType: this.osType })
      return getStgTypeByName(tempData)
    },
    handleAdd() {
      this.setSelectLibTypeTreeData()
      this.$nextTick(() => {
        this.$refs.processAddDlg.handleCreate(this.query.groupId)
      })
    },
    handleBatchAdd() {
      this.setSelectLibTypeTreeData()
      this.typeId = this.query.groupId
      this.$nextTick(() => {
        this.$refs.batchUpload.handleCreate()
      })
    },
    handleBatchEdit() {
      const datas = this.gridTable.getSelectedDatas()
      if (datas.length == 0) {
        this.$message({
          message: this.$t('pages.processStgLib_Msg27'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.$refs.batchEdit.handleCreate(datas)
    },
    handleBatchRelaMd5() {
      this.$refs.batchRelaMd5.handleCreate()
    },
    handleExportStg() {
      this.dialogStatus = 'download'
      this.resetTemp()
      this.setSelectLibTypeTreeData()
      this.checkedParentType.splice(0, 1, this.query.groupId)
      this.tempF.typeId = this.query.groupId
      this.downloadDialogVisible = true
      this.$refs.fingerTree && this.$refs.fingerTree.clearFilter()
      this.$nextTick(() => {
        this.$refs['downloadDataForm'] && this.$refs['downloadDataForm'].clearValidate()
      })
    },
    downloadSelectChange(data, options) {
      this.query.groupId = data
      this.tempF.typeId = data
      this.filename = (Array.isArray(options) ? options[0].label : options.label) + '.tip'
    },
    downloadConfirm(file) {
      const data = { osType: this.osType, groupId: this.query.groupId }
      const opts = { file, jwt: true, topic: this.$route.name }
      exportStg(data, opts)
      this.downloadDialogVisible = false
    },
    downloadClose() {
      this.query.groupId = this.$refs.selfLibTree.tree().getCurrentNode().dataId
    },
    handleImportFile() {
      this.$refs.importProcessDlg.show()
    },
    importProcessSuccess() {
      this.loadLibTypeTree()
      this.handleFilter()
    },
    handleUpdate: function(row) {
      this.setSelectLibTypeTreeData()
      this.$nextTick(() => {
        this.$refs.processAddDlg.handleUpdate(row)
      })
    },
    handleImport() {
      const groupIds = []
      const nodeDatas = this.getTree().getCheckedNodes()
      nodeDatas.forEach((nodeData) => groupIds.push(nodeData.dataId))
      getImportProgress().then(resp => {
        if (resp.data.percent == 0 || resp.data.percent >= 100) {
          this.$confirmBox(this.$t('pages.processStgLib_Msg28'), this.$t('text.prompt')).then(() => {
            const ids = this.gridTable.getSelectedIds()
            this.loading = true
            importStg({ groupIds: groupIds.join(','), stgIds: ids.join(','), osType: this.osType }).then(respond => {
              this.loading = false
              this.gridTable.execRowDataApi(this.query)
              this.loadLibTypeTree()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.importSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(res => {
              this.loading = false
            })
          }).catch(() => {
          })
        } else {
          this.$notify({ title: this.$t('text.prompt'), message: this.$t('pages.importExecutingMsg', { percent: resp.data.percent }), type: 'info', duration: 2000 })
        }
      })
    },
    handleExport() {},
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStg({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    handleConfig() {
      this.$router.push('/dataEncryption/encryption/processStgLibConfig')
    },
    suffixValidator(rule, value, callback) {
      if (value == '*.*') {
        callback(new Error(this.$t('pages.appGroup_text5')))
      } else {
        callback()
      }
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleCreateLibType(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleUpdateLibType(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.handleDeleteLibType(data)} />
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      return Number.parseInt((data + '').replace('G', ''))
    },
    groupClick: function(row) {
      const node = this.getTree().getNode('G' + row.groupId);
      if (null == node) {
        return
      }
      this.expandNodeParent(node)
      this.libTypeNodeCheckChange(null, node)
      this.$nextTick(() => {
        this.getTree().setCurrent(node)
      })
    },
    expandNodeParent(node) {
      if (node.parent) {
        node.parent.expanded = true
        this.expandNodeParent(node.parent)
      }
    },
    enableFormatter(row, data) {
      return data > 0 ? this.$t('pages.allow') : this.$t('pages.notAllow')
    },
    groupFormatter(row) {
      return this.curLabelMap[row.groupId] || ''
    },
    md5LevelFormatter(row, data) {
      return data == 1 ? this.$t('pages.enable') : this.$t('pages.notEnabled')
    },
    disableNetFormatter(row, data) {
      return data == 1 ? this.$t('pages.limit') : this.$t('pages.notLimit')
    },
    handleCustomImport() {
      this.resetTemp()
      this.selectLibTypeTreeData.splice(0)
      this.selfLibTreeData[0].children.forEach((nodeData) => {
        this.selectLibTypeTreeData.push(nodeData)
      })
      this.checkedParentType.splice(0)
      getImportProgress().then(resp => {
        if (resp.data.percent == 0 || resp.data.percent >= 100) {
          this.importVisible = true
        } else {
          this.$notify({ title: this.$t('text.prompt'), message: this.$t('pages.importExecutingMsg', { percent: resp.data.percent }), type: 'info', duration: 2000 })
        }
      })
      this.$nextTick(() => {
        this.$refs['importForm'] && this.$refs['importForm'].clearValidate()
      })
    },
    customImport() {
      this.$refs['importForm'].validate((valid) => {
        if (valid) {
          const targetId = this.tempF.typeId
          const importType = this.tempF.importType
          const groupIds = []
          const nodeDatas = this.getTree().getCheckedNodes()
          nodeDatas.forEach((nodeData) => groupIds.push(nodeData.dataId))
          const ids = this.gridTable.getSelectedIds()
          this.$confirmBox(this.$t('pages.processStgLib_Msg28'), this.$t('text.prompt')).then(() => {
            this.loading = true
            this.importVisible = false
            importStg({ groupIds: groupIds.join(','), stgIds: ids.join(','), osType: this.osType, targetId: targetId, importType: importType }).then(respond => {
              this.loading = false
              this.gridTable.execRowDataApi(this.query)
              this.loadLibTypeTree()
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.importSuccess'), type: 'success', duration: 2000 })
            }).catch(res => {
              this.loading = false
            })
          }).catch(() => {})
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .no-header-tab>>>.el-tabs__header{
    display: none;
  }
  .no-header-tab>>>.el-tabs__content{
    height: 100%;
  }
  .typeGroupId:before{
    content: '*';
    color: #F56C6C;
    position: relative;
    left: 56px;
    top: 23px;
  }
</style>
