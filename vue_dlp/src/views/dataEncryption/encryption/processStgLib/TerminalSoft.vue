<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :append-to-body="false"
    :modal="false"
    :title="$t('pages.terminalExtractSoft')"
    :visible.sync="appVisible"
    width="800px"
  >
    <file-manager out-class="" :file-path="curFilePath" @refresh="refresh" @clickPath="curFilePathClick">
      <template slot="tree">
        <div style="height: 465px">
          <strategy-target-tree
            ref="terminalTree"
            :resizeable="false"
            :showed-tree="['terminal']"
            :os-type-filter="1"
            @data-change="refresh"
          />
        </div>
      </template>
      <template slot="toolbar">
        <file-search
          :tips="$t('pages.serverlog_search_tips')"
          :disabled="searchDisabled"
          :support-date-range="false"
          style="float: right; margin-left: 10px;"
          @search="handleSearch"
        />
      </template>
      <div>
        <grid-table
          ref="tableList"
          v-loading="tableLoading"
          :height="425"
          :col-model="colModel"
          :selectable="selectable"
          :multi-select="true"
          :show-pager="false"
          :row-datas="rowData"
          :default-sort="defaultSort"
          @selectionChangeEnd="selectionChangeEnd"
          @row-dblclick="rowDblclickFunc"
        />
      </div>
    </file-manager>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="fileSubmitting" @click="extractSoft(selection)">{{ $t('pages.importApp') }}</el-button>
      <el-button @click="appVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>

</template>

<script>
import FileManager from '@/components/FileManager'
import FileSearch from '@/components/FileManager/search'
import { getFileIcon } from '@/icons/extension'
import moment from 'moment'
import { fileTypeFormatter, fileSizeFormatter } from '@/api/assets/systemMaintenance/explorer'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'
import { generateGuid } from '@/api/assets/systemMaintenance/explorer'

export default {
  name: 'TerminalSoft',
  components: { FileManager, FileSearch },
  data() {
    return {
      appVisible: false,
      ctrlAble: false,
      deleteable: false,
      fileSubmitting: false,
      parentPath: '',
      defaultParentPath: '$TERMINAL_LOG',
      iconOption: {
        0: 'disk',
        1: 'dir',
        2: 'file'
      },
      fileType: {
        1: this.$t('pages.folder'),
        2: this.$t('pages.file1')
      },
      curFilePath: ['Terminal'],
      defaultSort: { prop: 'oriData.type', order: 'desc' },
      colModel: [
        { prop: 'label', label: 'name', width: '200', sort: true, iconFormatter: this.iconClassFormatter },
        { prop: 'oriData.type', label: 'fileType', width: '150', sort: true, sortOriginal: true, sortArr: ['oriData.type', 'suffix'], formatter: this.fileTypeFormatter },
        { prop: 'oriData.size', label: 'size1', width: '150', sort: true, sortOriginal: true, formatter: this.fileSizeFormatter },
        { prop: 'oriData.modifyTime', label: 'updateTime', width: '150', sort: true },
        { prop: 'oriData.createTime', label: 'createTime', width: '150', sort: true }
      ],
      allRowData: [],
      tempRowData: [],
      rowData: [],
      rowDataCache: undefined,
      tableLoading: false,
      curNodeData: undefined,
      terminalId: undefined,
      terminalName: undefined,
      selection: [],
      downloadPath: [], // 获取文件路径有用的片段
      downloadObj: {}, // 文件上传参数
      dirPath: '', // 拼接文件路径
      guid: '',  // 提取exe协议guid，判断每次协议的唯一值
      hasLoadNum: 0
    }
  },
  computed: {
    gridTable() {
      return this.$refs['tableList']
    },
    terminalTree() {
      return this.$refs['terminalTree']
    },
    searchDisabled() {
      if (this.tableLoading || !this.curNodeData) {
        return true
      }
      const termType = this.curNodeData.dataType
      if (termType === '0' || termType === '16') {
        return !this.curNodeData.online
      }
      return true
    },
    curDir() {
      const paths = this.curFilePath.slice(1)
      paths.unshift(this.defaultParentPath)
      return paths.join('/')
    }
  },
  watch: {
    tempRowData(val) {
      this.rowDataCache = undefined
      if (val.length > 0) {
        this.tableLoading = true
        this.rowData = [...this.tempRowData]
        this.tempRowData.splice(0)
        this.$nextTick(() => {
          this.tableLoading = false
        })
      }
    }
  },
  methods: {
    fileTypeFormatter,
    fileSizeFormatter,
    show() {
      this.resetTempRow()
      this.appVisible = true
      this.$nextTick(() => {
        this.terminalTree.setCurrent(null)
      })
    },
    resetTempRow() {
      this.curFilePath = ['Terminal']
      this.rowData = []
    },
    close() {
      this.appVisible = false
    },
    /**
     * 停止上传文件属性
     * */
    stop() {
      const mode = 0
      const guid = this.guid
      const terminalId = this.terminalId
      this.$socket.sendToUser(terminalId, '/listTerminalSoftInfo', {
        mode, terminalId, guid
      }, (respond, handle) => {
        handle.close()
      }, (handle) => {
        handle.close()
      })
    },
    selectable(row, index) {
      const i = row.label.lastIndexOf('.')
      if (i < 0) {
        return false
      }
      const suffix = row.label.slice(i + 1).toLowerCase()
      return suffix === 'exe'
    },
    selectionChangeEnd(selection) {
      this.deleteable = selection && selection.length > 0
      this.selection = selection.map(item => {
        const { oriData: { fileName: name, size, type, terminalId }} = item
        return { name, size, type, terminalId }
      })
    },
    /**
     * 提取EXE属性
     * @param val 表格选中值
     */
    extractSoft(val) {
      if (val.length === 0) {
        this.$message({
          message: this.$t('pages.serverlog_select_msg4'),
          type: 'error',
          duration: 2000
        })
        this.downloadObj = undefined
        return
      }
      this.downloadPath = this.curFilePath.slice(1)
      this.dirPath = '' // 拼接文件路径
      for (let i = 0; i < this.downloadPath.length; i++) {
        this.dirPath += this.downloadPath[i] + '/'
      }
      if (val.length !== 0) {
        const file = []
        for (let i = 0; i < val.length; i++) { // 获取选中文件的名称
          file.push(val[i].name)
        }
        this.terminalId = val[0].terminalId
        this.hasLoadNum = 0
        const downloadObj = { // 需要传给后台的值
          mode: 2,  // 2表示新发送
          terminalId: val[0].terminalId,
          dir: this.dirPath.substring(0, this.dirPath.length - 1),
          files: file.join('|')
        }
        const that = this
        // 先去后台获取随机生成的guid，之所以到后台，是怕重复
        generateGuid().then(res => {
          const guid = res.data
          this.guid = guid
          downloadObj.guid = guid
          // 获取exe属性前方法回调
          this.$emit('beforeSelect', downloadObj)
          // 调用接口获取终端这些exe的
          this.$socket.sendToUser(val[0].terminalId, '/listTerminalSoftInfo', downloadObj, (respond, handle) => {
            const data = respond.data
            if (this.guid != data.guid) {
              return
            }
            if (data && data.softList && data.softList.length > 0) {
              this.hasLoadNum += data.softList.length
              data.allNum = val.length
              data.hasLoadNum = this.hasLoadNum
              this.$emit('select', data)
            }
            if (this.hasLoadNum == val.length) { // 所有程序信息都接收完毕
              handle.close()
            }
          }, (handle) => {
            handle.close()
            that.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
          })
          this.appVisible = false
        })
      }
    },
    handleNodeClickFunc(tabName, data) {
      this.curNodeData = data
      const that = this
      const terminalId = data.dataId
      const terminalName = data.label
      const continueFlag = 2
      const ext = 'exe'
      let fileType = 37
      var parentPath = ''
      if (that.curFilePath.length > 1) {
        var pathTemp = that.curFilePath.slice(1)
        pathTemp.forEach((item, index) => {
          parentPath += item
          if (index != pathTemp.length - 1) {
            parentPath += '\\'
          }
        })
      } else {
        fileType = 0
      }
      this.rowData = []
      if (data.id.indexOf('G') < 0) {
        this.terminalId = terminalId
        this.terminalName = terminalName
        this.tableLoading = true
        this.checkCtrlAble(terminalId).then(() => {
          if (!this.ctrlAble) {
            this.tableLoading = false
            return
          }
          this.$socket.sendToUser(terminalId, '/listChildFile', {
            terminalId, continueFlag, fileType, parentPath, ext, businessType: 1
          }, (respond, handle) => {
            handle.close()
            const datas = respond.data.map(item => {
              item.suffix = item.label.indexOf('.') > -1 ? item.label.split('.').pop() : ''
              return item
            })
            that.tempRowData = [...datas]
            that.tableLoading = false
          }, (handle) => {
            handle.close()
            if (that.tableLoading) {
              that.tableLoading = false
              that.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
            }
          })
        })
      } else {
        this.terminalId = undefined
        this.terminalName = undefined
        this.tableLoading = false
        this.tempRowData = []
      }
    },
    rowDblclickFunc(rowData, column, event) {
      if (rowData.oriData.type == 2) return
      this.curFilePath.push(rowData.label)
      const curNodeData = this.terminalTree.getCurrentNode()
      curNodeData && this.handleNodeClickFunc('terminal', curNodeData)
    },
    refresh() {
      this.curFilePath.splice(1, this.curFilePath.length - 1)
      const curNodeData = this.terminalTree.getCurrentNode()
      curNodeData && this.handleNodeClickFunc('terminal', curNodeData)
    },
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('termLog', termId).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    iconClassFormatter: function(row) {
      const icons = []
      // 0：硬盘 1：文件夹 2. 文件
      const type = row.oriData.type
      let title
      let iconName
      if (type == 2) {
        title = row.label.split('.').pop().toLowerCase()
        iconName = getFileIcon(title)
      } else {
        title = { 0: '磁盘', 1: '文件夹' }[type]
        iconName = { 0: 'disk1', 1: 'dir1' }[type]
      }
      icons.push({ class: iconName, title: title })
      return icons
    },
    attributesFormatter: function(row, data) {
      const result = []
      if (data & 1) {
        result.push(this.$t('pages.readOnly'))
      }
      if (data & 2) {
        result.push(this.$t('pages.hide'))
      }
      if (data & 32) {
        result.push(this.$t('pages.files1'))
      }
      return result.join(' | ')
    },
    // 面包屑点击方法
    curFilePathClick: function(index) {
      const curNodeData = this.terminalTree.getCurrentNode()
      curNodeData && this.handleNodeClickFunc('terminal', curNodeData)
    },
    handleSearch(data) {
      if (!this.rowDataCache) {
        this.rowDataCache = this.rowData
      }
      this.rowData = this.rowDataCache.filter(item => {
        if (data.name && item.label.toLowerCase().indexOf(data.name.toLowerCase()) < 0) {
          return false
        }
        if (item.oriData.type === 0) { // 磁盘没有时间
          return true
        }
        if (data.modified) {
          const modifyTime = moment(item.oriData.modifyTime, 'YYYY-MM-DD HH:mm:ss').unix()
          if (modifyTime < data.modified[0] || modifyTime > data.modified[1]) {
            return false
          }
        }
        if (data.creation) {
          const createTime = moment(item.oriData.createTime, 'YYYY-MM-DD HH:mm:ss').unix()
          if (createTime < data.creation[0] || createTime > data.creation[1]) {
            return false
          }
        }
        return true
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-breadcrumb__inner a {
    max-width: 200px;
    display: inline-block;
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  >>>.el-breadcrumb__separator {
    vertical-align: top;
  }
</style>
