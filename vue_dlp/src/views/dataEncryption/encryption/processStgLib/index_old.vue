<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="tabName" type="card" @tab-click="tabClick">
      <el-tab-pane label="Windows" name="windowTab">
        <process-lib :os-type="1"/>
      </el-tab-pane>
      <el-tab-pane label="Linux" name="linuxTab">
        <process-lib :os-type="2"/>
      </el-tab-pane>
      <el-tab-pane label="Mac" name="macTab">
        <process-lib :os-type="4"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ProcessLib from '@/views/dataEncryption/encryption/processStgLib/processLib'
export default {
  name: 'ProcessStgLib',
  components: { ProcessLib },
  data() {
    return {
      tabName: 'windowTab'
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    tabClick(pane, event) {
      setTimeout(() => {
        // this.handleFilter()
      }, 0)
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
</style>
