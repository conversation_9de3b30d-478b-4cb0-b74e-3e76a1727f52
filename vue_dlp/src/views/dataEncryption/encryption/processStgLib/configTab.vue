<template>
  <div class="app-container">
    <config :os-type="1"/>
    <!--<el-tabs ref="tabs" v-model="tabName" type="card" style="height: calc(100% - 40px);" class="" @tab-click="tabClick">
      <el-tab-pane label="Window" name="windowTab">
        <config :os-type="1"/>
      </el-tab-pane>
      <el-tab-pane label="Linux" name="linuxTab">
        <config :os-type="2"/>
      </el-tab-pane>
      <el-tab-pane label="Mac" name="macTab">
        <config :os-type="4"/>
      </el-tab-pane>
    </el-tabs>-->
  </div>
</template>

<script>
import config from '@/views/dataEncryption/encryption/processStgLib/config'
export default {
  name: 'ProcessStgLibConfig',
  components: { config },
  data() {
    return {
      tabName: 'windowTab'
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    tabClick(pane, event) {
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
</style>
