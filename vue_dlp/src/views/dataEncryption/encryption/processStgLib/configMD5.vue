<template>
  <div class="table-container">
    <grid-table ref="checkMD5List" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="(rowDatas) => {this.$emit('selectChange', rowDatas)}"/>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
      >
        <FormItem :label="$t('pages.processStgLib_Msg64')" prop="md5Level">
          <el-radio-group v-model="temp.md5Level">
            <el-tooltip class="item" effect="dark" :content="$t('pages.processStgLib_Msg65')" placement="top">
              <el-radio :label="3">{{ $t('pages.strict') }}</el-radio>
            </el-tooltip>
            <el-radio :label="1">{{ $t('pages.veryStrict') }}</el-radio>
            <!--<el-radio :label="2">超级严格</el-radio>-->
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.associatedFinger')" >
          <tree-menu ref="formTree" style="height: 200px;" :data="formTreeData" multiple :checked-keys="checkedKeys" @check-change="parentIdChange" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="showDlg=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCheckMD5Page, getAppMd5Tree, createCheckMD5, deleteCheckMD5 } from '@/api/dataEncryption/encryption/processStgLibConfig'
export default {
  name: 'ProcessConfigMd5',
  props: {
    processStg: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'processName', width: '80' },
        { prop: 'checkMd5', label: 'algorithmLevel', width: '80', formatter: this.checkMd5Formatter },
        { prop: 'processMd5', label: 'fingerprint', width: '50' }
      ],
      md5LevelMap: {
        1: this.$t('pages.veryStrict'),
        2: this.$t('pages.superStrict'),
        3: this.$t('pages.strict')
      },
      formTreeData: [],
      checkedKeys: [],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        processStgId: undefined,
        md5Level: 3,
        fingerPrintIds: ''
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      query: {},
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      textMap: {
        update: this.$t('pages.updateStg'),
        create: this.$t('pages.createStg')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['checkMD5List']
    }
  },
  watch: {

  },
  created() {
    this.loadAppMd5Tree()
  },
  methods: {
    rowDataApi: function(option) {
      return getCheckMD5Page(option)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.checkedKeys.splice(0)
    },
    loadAppMd5Tree() {
      getAppMd5Tree().then(respond => {
        this.formTreeData = respond.data
      })
    },
    parentIdChange(keys, datas) {
      const fingerPrintIds = []
      this.checkedKeys.splice(0)
      datas.forEach(item => {
        if (item.type === 'process') {
          fingerPrintIds.push(item.dataId)
          this.checkedKeys.push(item.id)
        }
      })
      this.temp.fingerPrintIds = fingerPrintIds.join(',')
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.showDlg = true
      this.$refs.formTree && this.$refs.formTree.clearFilter()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    formatFormData() {
      this.temp.processStgId = this.processStg.dataId
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormData()
          createCheckMD5(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    deleteData() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      deleteCheckMD5({ ids: toDeleteIds.join(',') }).then(respond => {
        this.gridTable.deleteRowData(toDeleteIds)
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
      })
    },
    nameValidator(rule, value, callback) {
      callback(new Error(this.$t('valid.sameName')))
    },
    codeFormatter(row, data) {
      return this.codeOptions[data]
    },
    groupFormatter(row, data) {
      return this.processStg.label
    },
    checkMd5Formatter(row, data) {
      const data1 = data & 1 // 第一位数字
      const data2 = data & 2 // 第二位数字
      const level = data1 + data2
      return this.md5LevelMap[level]
    }
  }
}
</script>
