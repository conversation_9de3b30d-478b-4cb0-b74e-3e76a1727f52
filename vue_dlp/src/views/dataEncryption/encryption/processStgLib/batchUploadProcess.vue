<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :modal-append-to-body="true"
      :append-to-body="appendToBody"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.processStgLib_Msg54')"
      :visible.sync="dialogFormVisible1"
      width="800px"
      @dragDialog="handleDrag"
      @close="handleClose"
    >
      <Form
        ref="dataForm2"
        :rules="rules"
        :model="tempF"
        label-position="right"
        label-width="120px"
        style="width: 750px;"
      >
        <div class="toolbar">
          <div style="display: inline-block; max-width: 210px;">
            <upload-dir
              v-if="!isIE() && osType===1 && !plugIsInWork"
              ref="uploadDir"
              :popover-height="235"
              :loading="fileSubmitting"
              style="display: inline-block;"
              @changeFile="changeFile"
            />
            <el-upload
              v-show="!plugIsInWork"
              ref="upload"
              name="uploadFile"
              action="aaaaaa"
              accept=".exe"
              :disabled="fileSubmitting"
              :show-file-list="false"
              :before-upload="beforeUpload"
              style="display: inline-block;"
            >
              <el-button type="primary" :loading="fileSubmitting" size="mini">{{ $t('pages.uploadFile') }}</el-button>
            </el-upload>
            <!-- 打开窗口时，在进行心跳检测 -->
            <app-scan-plug ref="appScanPlug" :heartbeat-enable="dialogFormVisible1" @plugIsInWork="(inWork) => plugIsInWork = inWork" @uploadEnd="appendFile"/>
          </div>
          <el-button v-if="osType===1" type="primary" size="mini" @click="showAppSelectDlg">
            {{ $t('pages.software_Msg16') }}
          </el-button>
          <el-button v-if="showFinger && osType===1" :loading="fileSelecting" type="primary" size="mini" @click="showTermAppSelectDlg">
            {{ $t('pages.selectTerminalSoft') }}
          </el-button>
          <FormItem v-if="fileSubmitting" label-width="0">
            <el-progress type="line" :percentage="percentage" style="width: calc(100% - 46px); display: inline-block;"/>
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </FormItem>
        </div>
        <grid-table
          ref="fileList"
          :height="200"
          :multi-select="true"
          :show-pager="false"
          row-key="fileMd5"
          :col-model="colModel"
          :row-datas="fileList"
          style="margin-bottom: 5px;"
        />

        <FormItem :label="$t('pages.appType')" prop="typeId">
          <tree-select v-model="tempF.typeId" :data="typeTreeData" node-key="dataId" class="input-with-button" :checked-keys="[tempF.typeId]" @change="groupTreeSelectChange" />
          <el-button v-if="addType != null" :title="$t('pages.install_Msg42')" class="editBtn" @click="addType"><svg-icon icon-class="add" /></el-button>
        </FormItem>

        <FormItem :label="$t('pages.processStgLib_Msg3')" prop="decReadSfx">
          <el-input v-model="tempF.decReadSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" size="mini" @click="handleFileSuffixImport('decReadSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>

        <FormItem :label="$t('pages.processStgLib_Msg4')" prop="encWriteSfx">
          <el-input v-model="tempF.encWriteSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.process_Msg6')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" size="mini" @click="handleFileSuffixImport('encWriteSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>

        <FormItem :label="$t('pages.processStgLib_Msg6')" prop="encOpenSfx">
          <el-input v-model="tempF.encOpenSfx" type="textarea" class="input-with-button" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.processStgLib_Msg5')"></el-input>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" size="mini" @click="handleFileSuffixImport('encOpenSfx')">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
        </FormItem>

        <FormItem prop="enablePast">
          <el-checkbox v-model="tempF.enablePast" :true-label="65535" :false-label="0">{{ $t('pages.processStgLib_Msg7') }}</el-checkbox>
        </FormItem>
        <FormItem v-show="showFinger">
          <el-tooltip class="item" effect="dark" :content="$t('pages.processStgLib_Msg19')" placement="right">
            <el-checkbox v-model="tempF.checkMd5" :true-label="1" :false-label="0">{{ $t('pages.supportMd5') }}</el-checkbox>
          </el-tooltip>
        </FormItem>
        <FormItem v-if="tempF.checkMd5 == 1" prop="">
          <label style="float: left;padding-right: 10px;">{{ $t('pages.checkMd5Label') }}</label>
          <el-radio-group v-model="tempF.md5Level">
            <el-tooltip class="item" effect="dark" :content="$t('pages.appGroup_text20')" placement="top">
              <el-radio :label="3">{{ $t('pages.md5LevelMap3') }}</el-radio>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="$t('pages.appGroup_text21')" placement="top">
              <el-radio :label="1">{{ $t('pages.md5LevelMap1') }}</el-radio>
            </el-tooltip>
          </el-radio-group>
        </FormItem>
        <open-net v-show="showNet" :temp="tempF"/>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="createData1">{{ $t('button.confirm') }}</el-button>
        <el-button @click="handleClose">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
    <app-select-dlg ref="appSelectDlg" :append-to-body="true" @select="appendFile"/>
    <terminal-soft ref="termAppSelectDlg" @select="selectTermFile"/>
  </div>
</template>
<script>
import { changeFiles, loopUploadFiles } from '@/api/behaviorManage/application/appGroup'
import { saveProcessBatch, validProcessName } from '@/api/dataEncryption/encryption/processStgLib'
import { createProcessVersion } from '@/utils/fingerprint'
import UploadDir from '@/components/UploadDir'
import OpenNet from '@/views/dataEncryption/encryption/processStgLib/openNet'
import axios from 'axios'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import TerminalSoft from '@/views/dataEncryption/encryption/processStgLib/TerminalSoft'
import AppScanPlug from '@/views/system/baseData/appGroup/appScanPlug'

export default {
  name: 'BatchUpload',
  components: { AppScanPlug, UploadDir, OpenNet, FileSuffixLibImport, AppSelectDlg, TerminalSoft },
  props: {
    showNet: { type: Boolean, default: true },
    showFinger: { type: Boolean, default: true },
    appendToBody: { type: Boolean, default: true },
    typeTreeData: {
      type: Array,
      default() {
        return []
      }
    },
    groupId: {
      type: [Number, String],
      default: null
    },
    addType: {
      type: Function,
      default: null
    },
    osType: { // 终端类型：1=windows，2=linux，4=mac
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'processName', width: '120', sort: true },
        { prop: 'productName', label: 'productName', width: '100', sort: true },
        { prop: 'productVersion', label: 'productVersion', width: '80' },
        { prop: 'fileDescription', label: 'fileDescription', width: '100', sort: true },
        { prop: 'companyName', label: 'companyName', width: '100', sort: true },
        { prop: 'originalFilename', label: 'originalFilename', width: '100', sort: true },
        { prop: 'softSign', label: 'softSign', width: '100', sort: true }
      ],
      tempF: {},
      defaultTempF: { // 表单字段
        typeId: null,
        decReadSfx: '',
        encWriteSfx: '',
        encOpenSfx: '',
        enablePast: 0,
        disableNet: 0,
        checkMd5: 0,
        md5Level: 3,
        osType: this.osType,
        fingerPrintIds: [],
        processList: [],
        netConfigs: []
      },
      fileList: [],
      temp1: { // 表单字段
        id: undefined,
        processName: '',
        originalFilename: '',
        fileMd5: '',
        productName: '',
        productVersion: '',
        fileDescription: '',
        companyName: '',
        legalCopyright: '',
        quicklyMD5: '',
        internalName: '',
        remarks: '',
        classId: 0
      },
      dialogFormVisible1: false,
      dialogStatus: '',
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        typeId: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        encOpenSfx: [{ validator: this.suffixValidator, trigger: 'blur' }]
      },
      submitting: false,
      fileSelecting: false,
      fileSubmitting: false,
      fileLimitSize: 1024,
      percentage: 0,
      source: null,
      importFileSuffixType: '',
      suffixMaxLength: 200,
      plugIsInWork: false
    }
  },
  computed: {
    softTable: function() {
      return this.$refs['softTable']
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.tempF = JSON.parse(JSON.stringify(this.defaultTempF))
      this.tempF.typeId = !this.groupId ? 0 : this.groupId
      this.fileList.splice(0)
    },
    showAppSelectDlg() {
      this.$refs['appSelectDlg'].show()
    },
    showTermAppSelectDlg() {
      this.$refs['termAppSelectDlg'].show()
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('pages.processStgLib_Msg50'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      const fileName = file.name
      const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      if (ext == 'exe') {
        loopUploadFiles([file], this)
      } else {
        this.$message({
          message: this.$t('pages.processStgLib_Msg55'),
          type: 'error',
          duration: 2000
        })
      }
      return false // 屏蔽了action的默认上传
    },
    changeFile(files) {
      changeFiles(files, this)
    },
    appendFile(softs) {
      softs.forEach(item => {
        const index = this.fileList.findIndex(item2 => {
          return item2.fileMd5 == item.fileMd5
        })
        if (index == -1) {
          this.fileList.push(item)
        }
      })
      return this.$nextTick().then(() => {
        this.$refs.fileList.toggleAllSelection()
      })
    },
    selectTermFile(data) {
      this.fileSelecting = true
      // 如果传输完毕
      if (data.allNum == data.hasLoadNum) {
        this.fileSelecting = false
      }
      if (data && data.softList && data.softList.length > 0) {
        this.appendFile(data.softList, false)
      }
    },
    handleDrag() {
    },
    handleClose() {
      this.$refs.appScanPlug.closePlug()
      this.dialogFormVisible1 = false
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create1'
      this.dialogFormVisible1 = true
      this.$nextTick(() => {
        this.$refs['dataForm2'].clearValidate()
        this.$refs.appScanPlug.startPlug()
      })
    },
    groupTreeSelectChange(data) {
      this.tempF.typeId = data
      this.$refs['dataForm2'] && this.$refs['dataForm2'].clearValidate()
    },
    createData1() {
      const datas = this.$refs.fileList.getSelectedDatas()
      if (datas.length == 0) {
        this.$message({
          message: this.$t('pages.appGroup_text7'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.submitting = true
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          if (!this.validProcess()) {
            this.submitting = false
            return
          }
          this.tempF.processList = datas
          const nameStr = this.tempF.processList.map(item => {
            return item.processName
          }).join(',')
          validProcessName({ processNameStr: nameStr }).then(res => {
            if (res.data != null && res.data.length > 0) {
              this.$confirmBox(`${this.$t('pages.processStgLib_Msg30', { name: res.data.join('、') })}`, this.$t('text.prompt')).then(() => {
                this.save()
              }).catch(() => {
                this.submitting = false
              })
            } else {
              this.save()
            }
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    validProcess() {
      if (this.tempF.disableNet == 1 && this.tempF.netConfigs.length == 0) {
        this.$message({
          message: this.$t('pages.processStgLib_Msg29'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    toFingerprintData(appData) {
      const obj = {
        processName: appData.processName,
        processVersion: createProcessVersion(appData.processName, appData.productVersion),
        componentVersion: appData.productVersion,
        propertyMd5: appData.propertyMd5,
        fileMd5: appData.fileMd5,
        quicklyMd5: appData.quicklyMd5,
        checkMd5: this.tempF.md5Level,
        propertyMark: appData.propertyMark,
        productMd5: appData.productMd5,
        remark: ''
      }
      return obj
    },
    save() {
      this.tempF.processList.forEach(item => {
        item.relaFingerPrint = [this.toFingerprintData(item)]
        item.netConfigs = this.tempF.netConfigs
      })
      saveProcessBatch(this.tempF).then(respond => {
        this.submitting = false
        this.dialogFormVisible1 = false
        this.$refs.appScanPlug.closePlug()
        this.$emit('success', respond.data)
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
      }).catch(() => {
        this.submitting = false
      })
    },
    suffixValidator(rule, value, callback) {
      if (value == '*.*') {
        callback(new Error(this.$t('pages.appGroup_text5')))
      } else {
        callback()
      }
    },
    handleFileSuffixImport(type) {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
      this.importFileSuffixType = type
    },
    duplicateRemoval(nv, ov) {
      let union_suffix = null
      if (ov == null || ov === '') {
        union_suffix = [...new Set(nv.split('|'))].join('|')
      } else {
        union_suffix = [...new Set((ov + '|' + nv).split('|'))].join('|')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      return union_suffix
    },
    importFileSuffix(suffix) {
      if (this.importFileSuffixType === 'decReadSfx') {
        this.tempF.decReadSfx = this.duplicateRemoval(suffix, this.tempF.decReadSfx)
      } else if (this.importFileSuffixType === 'encWriteSfx') {
        this.tempF.encWriteSfx = this.duplicateRemoval(suffix, this.tempF.encWriteSfx)
      } else if (this.importFileSuffixType === 'encOpenSfx') {
        this.tempF.encOpenSfx = this.duplicateRemoval(suffix, this.tempF.encOpenSfx)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.toolbar>div {
  vertical-align: top;
}
</style>
