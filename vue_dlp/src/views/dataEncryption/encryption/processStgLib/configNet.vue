<template>
  <div class="table-container">
    <grid-table ref="disableNetList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="(rowDatas) => {this.$emit('selectChange', rowDatas)}"/>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 500px; margin-left:20px;">
        <FormItem :label="$t('pages.exeDesc')" prop="processStgId">
          <el-input v-model="processStg.label" :disabled="true"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.behavior')" prop="mode">
          <el-select v-model="temp.mode">
            <el-option v-for="(value, key) in modeOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.protocol')" prop="protocol">
          <el-select v-model="temp.protocol" clearable>
            <el-option v-for="(value, key) in protocolOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.portRange')">
          <el-select v-model="portType" @change="portTypeChange">
            <el-option v-for="(value, key) in portTypeOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.beginPort')" prop="beginPort">
              <el-input v-model.number="temp.beginPort" maxlength="5" :disabled="portType === '1'" @keyup.native="number('beginPort')"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.endPort')" prop="endPort">
              <el-input v-model.number="temp.endPort" maxlength="5" :disabled="portType === '1'" @keyup.native="number('endPort')"></el-input>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.ipRange')">
          <el-select v-model="ipType" @change="ipTypeChange">
            <el-option v-for="(value, key) in ipTypeOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </FormItem>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.startIP')" prop="beginIp">
              <el-input v-model="temp.beginIp" :disabled="ipType === '1'"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.endIP')" prop="endIp">
              <el-input v-model="temp.endIp" :disabled="ipType === '1'"></el-input>
            </FormItem>
          </el-col>
        </el-row>
        <!-- <FormItem label="" prop="includeLan">
          <el-checkbox v-model="temp.includeLan" :false-label="0" :true-label="1" style="margin-left: -25px;">该规则对局域网生效</el-checkbox>
        </FormItem>-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="showDlg=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDisableNetPage, createDisableNet, updateDisableNet, deleteDisableNet, getDisableNetByName } from '@/api/dataEncryption/encryption/processStgLibConfig'

export default {
  name: 'ProcessConfigDisableNet',
  props: {
    processStg: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', fixed: true },
        { prop: 'parentProcessName', label: 'parentProcess', width: '150' },
        { prop: 'mode', label: 'behavior', width: '150', formatter: this.modeFormatter },
        { prop: 'protocol', label: 'protocol', width: '150', formatter: this.protocolFormatter },
        { label: 'ipRange', width: '200', formatter: this.ipFormatter },
        { label: 'portRange', width: '200', formatter: this.portFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', click: this.handleUpdate, isShow: () => { return this.processStg.id !== undefined } }
          ]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        processStgId: undefined,
        mode: '1',
        protocol: undefined,
        beginIp: '0.0.0.0',
        endIp: '***************',
        beginPort: 1,
        endPort: 65535,
        includeLan: 0
      },
      modeOptions: { 1: this.$t('pages.allow'), 2: this.$t('pages.forbid') },
      protocolOptions: { 1: 'TCP', 2: 'UDP' },
      portTypeOptions: { 1: this.$t('pages.allPort'), 2: this.$t('pages.userDefined') },
      ipTypeOptions: { 1: this.$t('pages.allIP'), 2: this.$t('pages.userDefined') },
      portType: undefined,
      ipType: undefined,
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      query: {},
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      textMap: {
        update: this.$t('pages.updateStg'),
        create: this.$t('pages.createStg')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['disableNetList']
    }
  },
  watch: {
  },
  created() {

  },
  methods: {
    rowDataApi: function(option) {
      return getDisableNetPage(option)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.portType = '1'
      this.ipType = '1'
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.groupId = this.query.groupId
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.mode = '' + this.temp.mode
      this.temp.protocol = '' + this.temp.protocol
      this.portType = this.temp.beginPort === 1 && this.temp.endPort === 65535 ? '1' : '2'
      this.ipType = this.temp.beginIp === '0.0.0.0' && this.temp.endIp === '***************' ? '1' : '2'
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.processStgId = this.processStg.dataId
          this.temp.code = this.getSum([this.portType, this.ipType])
          createDisableNet(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.processStgId = this.processStg.dataId
          this.temp.code = this.getSum([this.portType, this.ipType])
          const tempData = Object.assign({}, this.temp)
          updateDisableNet(tempData).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.updateRowData(this.temp)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    deleteData() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      deleteDisableNet({ ids: toDeleteIds.join(',') }).then(respond => {
        this.gridTable.deleteRowData(toDeleteIds)
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
      })
    },
    portTypeChange(value) {
      if (value === '1') {
        this.$refs['dataForm'].clearValidate('beginPort')
        this.$refs['dataForm'].clearValidate('endPort')
        this.temp.beginPort = 1
        this.temp.endPort = 65535
      }
    },
    ipTypeChange(value) {
      if (value === '1') {
        this.$refs['dataForm'].clearValidate('beginIp')
        this.$refs['dataForm'].clearValidate('endIp')
        this.temp.beginIp = '0.0.0.0'
        this.temp.endIp = '***************'
      }
    },
    nameValidator(rule, value, callback) {
      getDisableNetByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    processNameFormatter(row, data) {
      return this.processStg.label
    },
    modeFormatter: function(row, data) {
      return this.modeOptions[data]
    },
    protocolFormatter: function(row, data) {
      return this.protocolOptions[data]
    },
    ipFormatter(row, data) {
      return row.beginIp + ' - ' + row.endIp
    },
    portFormatter(row, data) {
      return (row.beginPort ? row.beginPort : '') + ' - ' + (row.endPort ? row.endPort : '')
    },
    includeLanFormatter(row, data) {
      return data == 1 ? this.$t('pages.takeEffect') : this.$t('pages.inoperative')
    }
  }
}
</script>
