<template>
  <div>
    <FormItem :label="$t('pages.supportMd5')" :tooltip-content="$t('pages.appGroup_text18')">
      <el-switch v-model="temp.checkMd5" :disabled="!canSetPropertyMd5() && !canSetQuicklyMd5() && validMd5" :active-value="1" :inactive-value="0"></el-switch>
    </FormItem>
    <FormItem v-if="temp.checkMd5 == 1 && temp.osType == 1" :label="$t('pages.checkMd5Label')" prop="md5Level">
      <el-radio-group v-model="temp.md5Level">
        <el-radio :disabled="!canSetPropertyMd5() && validMd5" :label="3">{{ $t('pages.md5LevelMap3') }}
          <el-tooltip class="item" effect="dark" content="" placement="bottom">
            <div slot="content">{{ $t('pages.appGroup_text20') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-radio>
        <el-radio :disabled="!canSetQuicklyMd5() && validMd5" :label="1">{{ $t('pages.md5LevelMap1') }}
          <el-tooltip class="item" effect="dark" content="" placement="bottom">
            <div slot="content">{{ $t('pages.appGroup_text21') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-radio>
        <el-radio v-if="!disableProductMd5" :disabled="(temp.productMd5=='' || temp.productMd5==null) && validMd5" :label="4">
          {{ $t('pages.md5LevelMap4') }}
        </el-radio>
        <!--<el-radio :label="2">超级严格</el-radio>-->
      </el-radio-group>
    </FormItem>
    <div v-if="temp.id == null && temp.osType == 1">
      <FormItem :label="$t('pages.install_Msg40')">
        <el-switch v-model="temp.useSelf" :active-value="1" :inactive-value="0"></el-switch>
      </FormItem>
      <FormItem v-if="temp.useSelf == 1" :label="$t('pages.install_Msg41')" prop="fingerprintGroup">
        <tree-select
          :data="fingerprintTypeTree"
          node-key="dataId"
          :checked-keys="[temp.fingerprintGroup]"
          class="input-with-button"
          @change="chengeTypeId"
        />
        <el-button :title="$t('pages.install_Msg42')" class="editBtn" @click="handleCreateNode"><svg-icon icon-class="add" /></el-button>
      </FormItem>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :modal="false"
      :title="$t('pages.install_Msg43')"
      :visible.sync="dialogFormVisible"
      width="400px"
    >
      <Form ref="dataForm" :rules="rules" :model="typeTemp" label-position="right" label-width="80px">
        <FormItem :label="$t('pages.groupName1')" prop="name">
          <el-input v-model="typeTemp.name" v-trim maxlength="20" />
        </FormItem>
        <FormItem :label="$t('pages.install_Msg44')" prop="parentId">
          <tree-select
            ref="parentSelectTree"
            v-model="typeTemp.parentId"
            :data="fingerprintTypeTree"
            node-key="dataId"
            :checked-keys="checkedKeys"
            @change="groupTypeChage"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="createNode()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getFormTree } from '@/api/behaviorManage/application/appVersion'
import { canSetPropertyMd5, canSetQuicklyMd5 } from '@/utils/fingerprint'
import { createData, getGroupByName } from '@/api/behaviorManage/application/appGroup'

export default {
  name: 'OpenFingerprint',
  props: {
    validMd5: { // 是否验证可以设置指纹算法
      type: Boolean,
      default: true
    },
    disableProductMd5: { // 是否禁用“产品名称”防伪冒
      type: Boolean,
      default: true
    },
    temp: {
      type: Object,
      default: function() {
        return {
          id: null,
          checkMd5: 0,
          md5Level: 3, // 进程验证指纹的级别
          useSelf: 1, // 是否使用进程本身指纹数据
          fingerPrintIds: '', // 进程关联的指纹id
          fingerprintGroup: 0, // 进程生成的指纹归属类别
          fingerprintData: null // 指纹
        }
      }
    },
    status: { // 已弃用
      type: String,
      default: 'create'
    },
    processType: { // 已弃用
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      submitting: false,
      dialogFormVisible: false,
      checkedKeys: [],
      fingerprintTypeTree: [],
      typeTemp: { // 表单字段
        id: undefined,
        name: '',
        parentId: 0,
        parentName: ''
      },
      rules: {
        name: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.groupNameValidator, trigger: 'blur' }
        ],
        parentId: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      }
    }
  },
  watch: {
  },
  created() {
    this.getFingerprintTypeTree()
  },
  activated() {
    this.getFingerprintTypeTree()
  },
  methods: {
    handleCreateNode() {
      this.resetTemp()
      this.checkedKeys.splice(0, this.checkedKeys.length, this.typeTemp.parentId)
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createNode() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.typeTemp)
          createData(tempData).then((respond) => {
            this.submitting = false
            this.dialogFormVisible = false
            tempData.id = respond.data.id
            this.getFingerprintTypeTree()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    groupNameValidator(rule, value, callback) {
      getGroupByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    resetTemp() {
      this.typeTemp = {
        id: undefined,
        name: '',
        parentId: 0,
        parentName: ''
      }
    },
    groupTypeChage(data) {
      this.typeTemp.parentId = data.replace('G', '')
      this.typeTemp.parentName = this.$refs.parentSelectTree.$refs.tree.getNode(data).label
    },
    canSetPropertyMd5() {
      return canSetPropertyMd5(this.temp)
    },
    canSetQuicklyMd5() {
      return canSetQuicklyMd5(this.temp)
    },
    parentIdChange(keys, datas) {
      this.temp.fingerPrintIds.splice(0)
      datas.forEach(item => {
        if (item.type == 'process') {
          this.temp.fingerPrintIds.push(item.dataId)
        }
      })
    },
    chengeTypeId(data) {
      this.temp.fingerprintGroup = data.replace('G', '')
    },
    getFingerprintTypeTree() {
      getFormTree().then(res => {
        this.fingerprintTypeTree = res.data
      })
    }

  }
}
</script>
