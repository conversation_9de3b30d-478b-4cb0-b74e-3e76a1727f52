<template>
  <div>
    <FormItem>
      <el-checkbox v-model="temp.checkMd5" :disabled="temp.processName == null || temp.processName ==''" :true-label="1" :false-label="0">
        {{ $t('pages.supportMd5') }}
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">{{ $t('pages.processStgLib_Msg19') }}<br/></div>
          <i class="el-icon-info" />
        </el-tooltip>
      </el-checkbox>
    </FormItem>
    <FormItem v-if="temp.checkMd5 == 1 && openMatch && showFlag " :label="$t('pages.processStgLib_Msg32')" :tooltip-content="$t('pages.processStgLib_Msg33')">
      <el-switch v-model="temp.matchRealName" :active-value="2" :inactive-value="0"></el-switch>
    </FormItem>
    <FormItem v-if="temp.checkMd5 == 1">
      <div>
        <el-button v-if="osType == 1" type="primary" size="smaller" :disabled="temp.processName == null || temp.processName ==''" @click="handleAppCreate">
          {{ $t('button.insert') }}
        </el-button>
        <el-button v-if="temp.osType==1" type="primary" size="smaller" :disabled="temp.processName == null || temp.processName ==''" @click="handleFingerImport">
          {{ $t('button.applicationLibraryImport') }}
        </el-button>
        <el-button type="primary" size="smaller" :disabled="!deleteable || temp.processName == null || temp.processName ==''" @click="handleDeleteApp">
          {{ $t('button.delete') }}
        </el-button>
      </div>
      <grid-table ref="tableList" :height="150" :show-pager="false" :col-model="colModel" :row-datas="temp.relaFingerPrint" @selectionChangeEnd="selectionChangeEnd" />
    </FormItem>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :modal="false"
      :title="$t('pages.processStgLib_Msg34')"
      :visible.sync="appVisible"
      width="500px"
    >
      <Form ref="appForm" :rules="appRules" :model="appTemp" label-position="right" label-width="110px">
        <upload-process
          ref="upload"
          :valid-process="validProcess"
          :only-exe="false"
          :app-temp="appTemp"
          :can-upload="!isUpdateFormMode"
          :after-upload="afterUpload"
        />
        <FormItem :label="$t('pages.processStgLib_Msg35')" prop="processVersion">
          <el-input v-model="appTemp.processVersion" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.processStgLib_Msg36')" prop="componentVersion">
          <el-input v-model="appTemp.componentVersion" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.exeFingerprintDesc')" prop="remark">
          <el-input v-model="appTemp.remark" maxlength="80"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.checkMd5Label')" prop="md5Level">
          <el-radio-group v-model="appTemp.checkMd5">
            <el-radio :disabled="!appTemp.propertyMd5" :label="3">{{ $t('pages.md5LevelMap3') }}
              <el-tooltip class="item" effect="dark" content="" placement="bottom">
                <div slot="content">{{ $t('pages.appGroup_text20') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-radio>
            <el-radio :disabled="appTemp.quicklyMd5==''" :label="1">{{ $t('pages.md5LevelMap1') }}
              <el-tooltip class="item" effect="dark" content="" placement="bottom">
                <div slot="content">{{ $t('pages.appGroup_text21') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-radio>
            <el-radio v-if="!disableProductMd5" :disabled="appTemp.productMd5=='' || appTemp.productMd5==undefined" :label="4">
              {{ $t('pages.md5LevelMap4') }}
            </el-radio>
          </el-radio-group>
        </FormItem>
        <span style="color: #0c60a5">
          {{ $t('pages.processStgLib_Msg37') }}
        </span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="isUpdateFormMode?updateApp():createApp()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="appVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :modal="false"
      :title="$t('pages.install_Msg43')"
      :visible.sync="dialogFormVisible"
      width="400px"
    >
      <Form ref="dataForm" :rules="rules" :model="typeTemp" label-position="right" label-width="80px">
        <FormItem :label="$t('pages.groupName1')" prop="name">
          <el-input v-model="typeTemp.name" v-trim maxlength="20" />
        </FormItem>
        <FormItem :label="$t('pages.install_Msg44')" prop="parentId">
          <tree-select
            ref="parentSelectTree"
            v-model="typeTemp.parentId"
            :data="fingerprintTypeTree"
            node-key="dataId"
            :checked-keys="checkedKeys"
            @change="groupTypeChage"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="createNode()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.importLibrary1')"
      :visible.sync="fingerVisible"
      width="600px"
    >
      <Form ref="fingerDataForm" :rules="fingerRules" :model="libTemp" label-position="right" label-width="135px">
        <FormItem :label="$t('pages.chooseApp')" prop="fingerPrintIds">
          <tree-menu
            ref="fingerTree"
            v-model="libTemp.fingerPrintIds"
            default-expand-all
            :is-filter="false"
            style="height: 220px;"
            :data="fingerprintTree"
            multiple
            :checked-keys="libTemp.fingerPrintIds"
            :filter-node-method="filterParentNodeMethod"
            @check-change="parentIdChange"
          />
        </FormItem>
        <FormItem :label="$t('pages.checkMd5Label')">
          <el-radio-group v-model="libTemp.md5Level">
            <el-tooltip class="item" effect="dark" :content="$t('pages.appGroup_text20')" placement="top">
              <el-radio :label="3">{{ $t('pages.md5LevelMap3') }}</el-radio>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="$t('pages.appGroup_text21')" placement="top">
              <el-radio :label="1">{{ $t('pages.md5Level2') }}</el-radio>
            </el-tooltip>
            <el-radio v-if="!disableProductMd5" :label="4">{{ $t('pages.md5LevelMap4') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.processStgLib_Msg39')" :tooltip-content="$t('pages.processStgLib_Msg38')">
          <el-radio-group v-model="libTemp.dealFlag">
            <el-radio :label="1">{{ $t('pages.cover') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.skip') }}</el-radio>
          </el-radio-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="importFromLib()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="fingerVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import { listFingerprintTree } from '@/api/system/baseData/fingerprintRela'
import { getFormTree } from '@/api/behaviorManage/application/appVersion'
import { canSetPropertyMd5, canSetQuicklyMd5, createProcessVersion } from '@/utils/fingerprint'
import { createData, getGroupByName } from '@/api/behaviorManage/application/appGroup'
import UploadProcess from '@/views/behaviorManage/application/appBlock/uploadProcess'

export default {
  name: 'BatchFingerprint',
  components: { UploadProcess },
  props: {
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    temp: {
      type: Object,
      default: function() {
        return {
          id: null,
          matchRealName: 0,
          checkMd5: 0,
          useSelf: 0, // 是否使用进程本身指纹数据
          fingerprintGroup: 0, // 进程生成的指纹归属类别
          relaFingerPrint: []
        }
      }
    },
    disableProductMd5: {
      type: Boolean,
      default: true
    },
    status: { // 已弃用
      type: String,
      default: 'create'
    },
    processType: { // 已弃用
      type: Number,
      default: 1
    },
    openMatch: { // 是否可以修改匹配进程真实名称
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'processVersion', label: 'processVersion', width: '120', sort: true },
        { prop: 'componentVersion', label: 'componentVersion', width: '100', sort: true },
        { prop: 'remark', label: 'exeFingerprintDesc', width: '120', sort: true },
        { prop: 'checkMd5', label: 'checkMd5Label', sort: true, sortOriginal: true, hidden: () => { return this.temp.osType != 1 }, width: '90', formatter: this.md5LevelFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 50,
          buttons: [
            { label: 'edit', click: this.handleAppUpdate }
          ]
        }
      ],
      isUpdateFormMode: true,
      appTemp: {},
      defaultAppTemp: {
        processName: '',
        processVersion: '', // 进程版本
        componentVersion: '', // 组件版本
        propertyMd5: '', // 属性md5
        fileMd5: '', // 全量md5
        quicklyMd5: '', // 快速md5
        checkMd5: 1, // 指纹算法：1-快速指纹，2-全量指纹，3-属性指纹
        propertyMark: '', // 属性md5位
        remark: '' // 描述
      },
      libTemp: {},
      defaultLibTemp: {
        processList: [],
        md5Level: 3,
        fingerPrintIds: [],
        dealFlag: 1
      },
      submitting: false,
      dialogFormVisible: false,
      appVisible: false,
      fingerVisible: false,
      checkedKeys: [],
      fingerprintTree: [],
      fingerprintTypeTree: [],
      groupCheckIds: [],
      tabNameMap: {
        1: 'first',
        2: 'second',
        3: 'third'
      },
      typeTemp: { // 表单字段
        id: undefined,
        name: '',
        parentId: 0,
        parentName: ''
      },
      rules: {
        name: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.groupNameValidator, trigger: 'blur' }
        ],
        parentId: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      },
      appRules: {
        processName: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }],
        processVersion: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }]
      },
      fingerRules: {
        fingerPrintIds: [
          { required: true, message: this.$t('pages.appGroup_text22'), trigger: 'blur' }
        ]
      },
      deleteable: false,
      keyCodes: [],
      showFlag: false
    }
  },
  watch: {
    'temp.osType'() {
      this.colModel.forEach(item => {
        if (item.prop == 'checkMd5') {
          item.hidden = this.temp.osType != 1
        }
      })
    }
  },
  created() {
    this.getFingerprintTypeTree()
  },
  mounted() {
    const this$ = this
    document.onkeydown = function(e) {
      this$.keyCodes.splice(0)
    }
    document.onkeyup = function(e) {
      this$.keyCodes.push(e.keyCode)
      if (this$.keyCodes.length == 3 && this$.keyCodes.indexOf(17) != -1 && this$.keyCodes.indexOf(18) != -1 && this$.keyCodes.indexOf(115) != -1) {
        this$.showFlag = true
      }
    }
  },
  activated() {
    this.getFingerprintTypeTree()
  },
  methods: {
    filterParentNodeMethod(value, data) {
      return data.type == 'process'
    },
    formatHandle(appTemp) {
      appTemp.matchRealName = appTemp.checkMd5 & 2
      appTemp.checkMd5 = appTemp.checkMd5 & 1
    },
    formatSave(appTemp) {
      if (appTemp.checkMd5 && appTemp.matchRealName) {
        appTemp.checkMd5 = appTemp.checkMd5 + appTemp.matchRealName
      }
    },
    handleAppCreate() {
      this.resetAppTemp()
      this.isUpdateFormMode = false
      this.appVisible = true
      this.$nextTick(() => {
        this.$refs['appForm'].clearValidate()
      })
    },
    handleAppUpdate(app) {
      this.appTemp = Object.assign({}, app)
      this.isUpdateFormMode = true
      this.appVisible = true
      this.$nextTick(() => {
        this.$refs['appForm'].clearValidate()
      })
    },
    handleFingerImport() {
      this.getFingerprintTree().then(res => {
        this.libTemp.fingerPrintIds.splice(0, this.libTemp.fingerPrintIds.length)
      })
      this.resetLibTemp()
      this.fingerVisible = true
    },
    parentIdChange(keys, datas) {
      const processDatas = datas.filter(item => item.type == 'process')
      const tempFingerPrintIds = processDatas.map(({ dataId }) => dataId)
      const fingerPrintIds = this.libTemp.fingerPrintIds
      const comparePrintIds = fingerPrintIds.slice().sort()
      let index = 0
      if (tempFingerPrintIds.length === comparePrintIds.length && tempFingerPrintIds.slice().sort().every(id => id == comparePrintIds[index++])) {
        return
      }
      fingerPrintIds.splice(0, fingerPrintIds.length, ...tempFingerPrintIds)
      this.libTemp.processList.splice(0, this.libTemp.processList.length, ...processDatas.map(({ oriData }) => oriData))
      this.$refs['fingerDataForm'].clearValidate()
    },
    handleDeleteApp() {
      this.$confirmBox(this.$t('pages.processStgLib_Msg40'), this.$t('text.prompt')).then(() => {
        const rows = this.$refs.tableList.getSelectedDatas()
        // 创建待删除键的集合
        const deleteKeys = new Set(rows.map(({ quicklyMd5 }) => `${quicklyMd5}`));
        // 使用filter一次性过滤
        this.temp.relaFingerPrint = this.temp.relaFingerPrint.filter(({ quicklyMd5 }) =>
          !deleteKeys.has(`${quicklyMd5}`)
        );
      }).catch(() => {})
    },
    createApp() {
      this.submitting = true
      this.$refs['appForm'].validate((valid) => {
        if (valid) {
          // 查询列表中是否存在相同指纹的程序
          const index = this.temp.relaFingerPrint.findIndex(item => {
            return item.quicklyMd5 == this.appTemp.quicklyMd5
          })
          if (index >= 0) {
            this.$confirmBox(this.$t('pages.processStgLib_Msg41', { process: this.temp.relaFingerPrint[index].processVersion }), this.$t('text.prompt')).then(() => {
              this.temp.relaFingerPrint.splice(index, 1, this.appTemp)
            }).catch(() => {})
          } else {
            // 查询列表中是否存在相同的版本号
            const index2 = this.temp.relaFingerPrint.findIndex(item => {
              return item.processVersion == this.appTemp.processVersion &&
                item.componentVersion == this.appTemp.componentVersion
            })
            if (index2 >= 0) {
              this.$confirmBox(this.$t('pages.processStgLib_Msg43'), this.$t('text.prompt')).then(() => {
                this.temp.relaFingerPrint.push(this.appTemp)
              }).catch(() => {})
            } else {
              this.temp.relaFingerPrint.push(this.appTemp)
            }
          }
          this.appVisible = false
          this.submitting = false
        } else {
          this.submitting = false
        }
      })
    },
    updateApp() {
      this.submitting = true
      this.$refs['appForm'].validate((valid) => {
        if (valid) {
          // 查询列表中是否存在相同的版本号
          const index2 = this.temp.relaFingerPrint.findIndex(item => {
            return item.processVersion == this.appTemp.processVersion &&
              item.componentVersion == this.appTemp.componentVersion &&
              item.quicklyMd5 != this.appTemp.quicklyMd5
          })
          if (index2 >= 0) {
            this.$confirmBox(this.$t('pages.processStgLib_Msg44'), this.$t('text.prompt')).then(() => {
              const index3 = this.temp.relaFingerPrint.findIndex(item => {
                return item.quicklyMd5 == this.appTemp.quicklyMd5
              })
              this.temp.relaFingerPrint.splice(index3, 1, this.appTemp)
            }).catch(() => {})
          } else {
            const index3 = this.temp.relaFingerPrint.findIndex(item => {
              return item.quicklyMd5 == this.appTemp.quicklyMd5
            })
            this.temp.relaFingerPrint.splice(index3, 1, this.appTemp)
          }
          this.appVisible = false
          this.submitting = false
        } else {
          this.submitting = false
        }
      })
    },
    importFromLib() {
      this.submitting = true
      this.$refs['fingerDataForm'].validate((valid) => {
        if (valid) {
          this.libTemp.processList.forEach(item1 => {
            item1.processName = item1.name
            // 验证列表里面是否已存在相同指纹的数据
            const index = this.temp.relaFingerPrint.findIndex(item2 => {
              return item1.quicklyMd5 == item2.quicklyMd5
            })
            item1.checkMd5 = this.libTemp.md5Level
            if (index > -1) {
              if (this.libTemp.dealFlag == 1) {
                // 遇到重复数据进行覆盖
                this.temp.relaFingerPrint.splice(index, 1, this.toFingerprintData(item1))
              }
            } else {
              this.temp.relaFingerPrint.push(this.toFingerprintData(item1))
            }
          })
          this.fingerVisible = false
          this.submitting = false
        } else {
          this.submitting = false
        }
      })
    },
    // 把程序数据装成指纹数据
    toFingerprintData(appData) {
      const obj = {
        id: appData.id,
        processName: appData.processName,
        processVersion: createProcessVersion(appData.processName, appData.productVersion),
        componentVersion: appData.productVersion,
        propertyMd5: appData.propertyMd5,
        fileMd5: appData.fileMd5,
        quicklyMd5: appData.quicklyMd5,
        checkMd5: appData.checkMd5,
        propertyMark: appData.propertyMark,
        productMd5: appData.productMd5,
        remark: ''
      }
      return obj
    },
    validProcess(file) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.processStgLib_Msg45'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const fileName = file.name
      if (fileName != this.temp.processName) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.processStgLib_Msg46', { info: this.temp.processName }),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    afterUpload(appData) {
      appData.checkMd5 = appData.propertyMd5 ? 3 : 1
      this.appTemp = this.toFingerprintData(appData)
    },
    resetAppTemp() {
      this.appTemp = Object.assign({}, this.defaultAppTemp)
    },
    resetLibTemp() {
      // defaultLibTemp里面存在数组，需要使用JSON的方法复制对象，不然会把数组地址复制过去
      this.libTemp = JSON.parse(JSON.stringify(this.defaultLibTemp))
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleCreateNode() {
      this.resetTemp()
      this.checkedKeys.splice(0, this.checkedKeys.length, this.typeTemp.parentId)
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createNode() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.typeTemp)
          createData(tempData).then((respond) => {
            this.submitting = false
            this.dialogFormVisible = false
            tempData.id = respond.data.id
            this.getFingerprintTypeTree()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    groupNameValidator(rule, value, callback) {
      getGroupByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    resetTemp() {
      this.typeTemp = {
        id: undefined,
        name: '',
        parentId: 0,
        parentName: ''
      }
    },
    groupTypeChage(data) {
      this.typeTemp.parentId = data.replace('G', '')
      this.typeTemp.parentName = this.$refs.parentSelectTree.$refs.tree.getNode(data).label
    },
    canSetPropertyMd5() {
      return canSetPropertyMd5(this.temp)
    },
    canSetQuicklyMd5() {
      return canSetQuicklyMd5(this.temp)
    },
    chengeTypeId(data) {
      this.temp.fingerprintGroup = data.replace('G', '')
    },
    getFingerprintTree() {
      return listFingerprintTree({ filterName: this.temp.processName }).then(res => {
        this.fingerprintTree = res.data
      })
    },
    getFingerprintTypeTree() {
      getFormTree().then(res => {
        this.fingerprintTypeTree = res.data
      })
    },
    md5LevelFormatter(row, data) {
      return data == 1 ? this.$t('pages.md5LevelMap1') : data == 2 ? this.$t('pages.md5LevelMap2') : data == 3 ? this.$t('pages.md5LevelMap3') : data == 4 ? this.$t('pages.md5LevelMap4') : '关闭'
    }

  }
}
</script>
