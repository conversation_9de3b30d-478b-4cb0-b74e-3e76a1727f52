<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.processStgLib_ImportTitle')"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <process-select-table
        ref="processSelectTable"
        :height="400"
        :os-type="osType"
        :multiple="multiple"
        :lib-tree-node="getStgTypeTree"
        :lib-file-suffix-page="getStgPage"
        @single-select="singleSelectApp"
      />
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :menu-code="'A5G'" :link-url="'/dataEncryption/encryption/processStgLib'" :btn-text="$t('pages.processStgLib')" :click-func="'clickLink'" @clickLink="clickLink('/dataEncryption/encryption/processStgLib')"/>
        <el-button v-if="multiple" type="primary" @click="confirmImport()">
          {{ $t('pages.processStgLib_addSelectedProcessName') }}
        </el-button>
        <el-button @click="hide">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getStgTypeTree, getStgPage } from '@/api/dataEncryption/encryption/processStgLib'
import ProcessSelectTable from '@/views/dataEncryption/encryption/processStgLib/processSelectTable'

export default {
  name: 'ProcessImportDlg',
  components: { ProcessSelectTable },
  props: {
    multiple: { type: Boolean, default: true }, // 能否多选
    appendToBody: { type: Boolean, default: false },
    osType: { type: Number, default: 1 } // 进程系统类型：1-windows，2-linux，4-mac
  },
  data() {
    return {
      dialogVisible: false,
      fileSuffixTree: []
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    getStgTypeTree,
    getStgPage,
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.processSelectTable.show()
      })
    },
    hide() {
      this.dialogVisible = false
    },
    confirmImport() {
      this.$refs.processSelectTable.getSelectedDatas().then(resp => {
        if (resp == null || resp.length === 0) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.processStgLib_addSelectedProcessNameHint'),
            type: 'error',
            duration: 2000
          })
          return
        }
        this.$emit('importProcess', resp)
        this.hide()
      })
    },
    clickLink() {
      this.dialogVisible = false
      this.$router.push('/dataEncryption/encryption/processStgLib')
    },
    singleSelectApp(data) {
      this.$emit('importProcess', data)
      this.dialogVisible = false
    }
  }
}
</script>
