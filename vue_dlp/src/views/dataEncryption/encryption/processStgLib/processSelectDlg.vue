<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.processStgLib_Msg73')"
      :visible.sync="appVisible"
      width="900px"
      @dragDialog="handleDrag"
    >
      <el-container style="height: 400px;">
        <el-aside v-if="showTree" width="210px">
          <tree-menu
            ref="groupTreeRef"
            :multiple="importGroupAble"
            check-strictly
            :default-expand-all="false"
            :default-expanded-keys="defaultExpandedKeys"
            :data="appTypeTreeData"
            :render-content="renderContent"
            @node-click="appTypeTreeNodeCheckChange"
            @check-change="checkChange"
          />
        </el-aside>
        <el-main>
          <div>
            <div class="toolbar">
              <el-button v-if="editAble" type="primary" icon="el-icon-plus" size="mini" :disabled="!appAddBtnAble" @click="handleAdd">
                {{ $t('button.add') }}
              </el-button>
              <el-button v-if="editAble" type="primary" icon="el-icon-plus" size="mini" :disabled="!appAddBtnAble" @click="handleBatchAdd">
                {{ $t('pages.batchAdd') }}
              </el-button>
              <el-button v-if="editAble" icon="el-icon-delete" size="mini" :disabled="!appDeleteable" @click="deleteApp">
                {{ $t('button.delete') }}
              </el-button>
              <div class="searchCon" style="float: right;">
                <el-input v-model="appQuery.searchInfo" v-trim clearable :placeholder="$t('pages.processName1')" style="width: 130px; vertical-align: baseline;" @keyup.enter.native="handleAppFilter" />
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleAppFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
              <div style="height: 360px;">
                <grid-table
                  ref="appInfoList"
                  :height="360"
                  pager-small
                  :col-model="appColModel"
                  :row-data-api="loadAppList"
                  :after-load="afterLoad"
                  is-saved-selected
                  saved-selected-prop="processName"
                  @selectionChangeEnd="appSelectionChangeEnd"
                />
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-tooltip v-if="importGroupAble" :content="$t('pages.processStgMsg11')" effect="dark" placement="top">
          <el-button type="primary" :loading="submitting" @click="selectTypeEnd()">
            {{ $t('pages.addType') }}
          </el-button>
        </el-tooltip>
        <el-button type="primary" :loading="submitting" @click="selectAppEnd()">
          {{ $t('pages.addApp') }}
        </el-button>
        <el-button @click="appVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.processStgLib_Msg20')"
      :append-to-body="appendToBody"
      show-parent
      :group-label="$t('pages.groupName1')"
      :parent-label="$t('pages.install_Msg44')"
      :group-tree-data="appTypeTreeData"
      :add-func="createNode"
      :update-func="updateNode"
      :delete-func="deleteStgType"
      :edit-valid-func="getGroupByName"
      @addEnd="addNodeEnd"
      @updateEnd="updateNodeEnd"
      @deleteEnd="deleteNodeEnd"
    />
    <process-add-dlg
      ref="processAddDlg"
      :os-type="osType"
      :group-tree-data="treeSelectNode"
      :append-to-body="appendToBody"
      :show-finger="showFinger"
      :show-net="showNet"
      :add-group="handleAppTypeCreate"
      @success="submitEnd"
    />
    <batch-upload
      ref="batchUpload"
      :os-type="osType"
      :type-tree-data="treeSelectNode"
      :append-to-body="appendToBody"
      :show-finger="showFinger"
      :show-net="showNet"
      :add-type="handleAppTypeCreate"
      :group-id="checkId"
      @success="submitEnd"
    />
  </div>
</template>

<script>
import {
  getStgTypeTree, getStgTypeByName, getStgPage,
  createStgType, updateStgType, deleteStgType, deleteStg
} from '@/api/dataEncryption/encryption/processStgLib'
import BatchUpload from '@/views/dataEncryption/encryption/processStgLib/batchUploadProcess'
import ProcessAddDlg from '@/views/dataEncryption/encryption/processStgLib/processAddDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'

export default {
  name: 'ProcessSelectDlg',
  components: { BatchUpload, EditGroupDlg, ProcessAddDlg },
  props: {
    appendToBody: { type: Boolean, default: false },
    showNet: { type: Boolean, default: true },
    showFinger: { type: Boolean, default: true },
    showPast: { type: Boolean, default: true }, // 是否显示粘贴
    formable: { type: Boolean, default: true }, // 能否提交表单
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    importGroupAble: { type: Boolean, default: true }, // 能否显示添加分组按钮
    editAble: { type: Boolean, default: true }, // 是否显示新增按钮
    showTree: { type: Boolean, default: true } // 是否显示新增按钮
  },
  data() {
    return {
      appColModel: [
        { prop: 'processName', label: 'processName1', width: '100', sort: 'custom' },
        { prop: 'groupId', label: 'appType', width: '100', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'decReadSfx', label: 'fileSuffixOpen', width: '150' },
        { prop: 'encWriteSfx', label: 'fileSuffixSave', width: '150' },
        { prop: 'encOpenSfx', label: 'fileSuffixVisit', width: '150' },
        { prop: 'checkMd5', hidden: !this.showFinger, label: 'processCounterfeiting', width: '120', sort: 'custom', formatter: this.md5LevelFormatter },
        { prop: 'disableNet', hidden: !this.showNet, label: 'restrictInternetAccess', width: '100', sort: 'custom', formatter: (row, data) => { return data == 0 ? this.$t('pages.noLimit') : this.$t('pages.limit') } },
        { prop: 'enablePast', hidden: !this.showPast, label: 'pasteEncryption', width: '100', sort: 'custom', formatter: this.enableFormatter },
        { prop: 'name', label: 'fileDescription', width: '150', sort: 'custom' },
        { label: 'operate', hidden: !this.editAble, type: 'button', fixed: 'right', fixedWidth: '80',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      appQuery: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined,
        osType: this.osType
      },
      appDeleteable: false,
      checkAppDeleteable: false,
      appAddBtnAble: false,
      appVisible: false,
      submitting: false,
      appTypeTreeData: [],
      defaultExpandedKeys: ['0'],
      treeSelectNode: [],
      checkId: null,
      outerShow: false,
      checkedRowKeys: [],
      checkedPageRowKeys: [],
      copyCheckedRowKeys: [],
      removeCheckedRowKeys: [],
      tempRowDatas: [],
      categoryIds: []
    }
  },
  computed: {
  },
  watch: {
    osType() {
      this.loadTypeTree()
    }
  },
  created() {
    this.loadTypeTree()
  },
  methods: {
    deleteStgType,
    appGridTable() {
      return this.$refs['appInfoList']
    },
    groupTree: function() {
      return this.$refs['groupTreeRef']
    },
    getGroupTreeData() {
      return this.appTypeTreeData
    },
    setCheckedRowKeys(data) {
      this.checkedRowKeys = [...data]
      this.copyCheckedRowKeys = [...data]
    },
    setCategoryIds(data) {
      this.categoryIds = [...data]
    },
    async show(groupId, typeIds, processIds) {
      if (groupId) {
        this.appQuery.groupId = groupId
      } else {
        this.appQuery.groupId = undefined
      }
      this.appQuery.searchInfo = ''
      this.appVisible = true
      this.defaultExpandedKeys.splice(0)
      this.defaultExpandedKeys.push(groupId ? ('G' + groupId) : '0')
      this.resetRowKeys()
      this.changeTypeTree()
      this.$nextTick(() => {
        this.handleAppFilter()
        if (this.groupTree()) {
          this.groupTree().clearFilter()
          this.groupTree().clearSelectedNodes()
          this.groupTree().setCurrent(this.defaultExpandedKeys[0])
        }
        if (this.appGridTable()) {
          this.appGridTable().clearSaveSelection()
        }
        if (this.importGroupAble) {
          if (typeIds) {
            this.setCategoryIds(typeIds)
            this.groupTree() && this.groupTree().setCheckedKeys(typeIds)
          }
          if (processIds) {
            this.setCheckedRowKeys(processIds)
            // 这里不需要勾选，表格勾选数据在afterLoad方法，即表格数据加载后执行
            // this.appGridTable().checkSelectedRows(processIds)
          }
        }
      })
    },
    resetRowKeys() {
      this.checkedRowKeys = []
      this.checkedPageRowKeys = []
      this.copyCheckedRowKeys = []
      this.removeCheckedRowKeys = []
      this.tempRowDatas = []
      this.categoryIds = []
    },
    getRemoveCheckedRowKeys() {
      return this.removeCheckedRowKeys
    },
    showCreate() {
      this.changeTypeTree().then(() => {
        this.handleAdd(true)
      })
    },
    showBatchCreate() {
      this.changeTypeTree().then(() => {
        this.handleBatchAdd(true)
      })
    },
    showUpdate(data) {
      this.changeTypeTree().then(() => {
        this.handleUpdate(data, true)
      })
    },
    loadAppList: function(option) {
      const optionTemp = Object.assign(this.appQuery, option, { isModule: false, osType: this.osType })
      return getStgPage(optionTemp)
    },
    afterLoad() {
      if (this.importGroupAble) {
        const checkedRowKeysSet = new Set(this.checkedRowKeys)
        // 用于存储当前页需要进行勾选的行数据
        const checkPageDataIdsSet = new Set()
        // 获取表格当前页，在策略里存在的数据
        this.appGridTable().getDatas()
          .filter(data => checkedRowKeysSet.has(data.id))
          .forEach(data => {
            checkPageDataIdsSet.add(data.id)
          })
        // 记录表格当前页，在策略里存在的数据
        this.checkedPageRowKeys = Array.from(checkPageDataIdsSet)
        // 获取整个表格勾选的数据
        this.appGridTable().getSelectedDatas().forEach(item => {
          checkPageDataIdsSet.add(item.id)
        })
        this.$nextTick(() => {
          const checkPageDataIds = Array.from(checkPageDataIdsSet)
          if (checkPageDataIds.length > 0) {
            // 在表格当前页，将选中的数据勾选上
            this.appGridTable().checkSelectedRows(checkPageDataIds)
          }
        })
      }
    },
    getRemoveApp(rowDatas) {
      // 整个表格勾选的数据的 map
      const rowDataIdsMap = rowDatas.reduce((map, item) => { map[item.id] = item.id; return map; }, {})
      // 策略选中的程序信息的 map
      const copyCheckedRowKeysMap = this.copyCheckedRowKeys.reduce((map, item) => { map[item] = item; return map; }, {})
      // tempRowDats 存储了表格上一次勾选状态变更时所勾选的数据, unCheckIds 为 被取消勾选 且 存在于策略里 的数据的 id
      const unCheckIds = this.tempRowDatas.filter(item => !rowDataIdsMap[item.id] && !!copyCheckedRowKeysMap[item.id]).map(item => item.id)
      // 合并、去重
      this.removeCheckedRowKeys = Array.from(new Set(this.removeCheckedRowKeys.concat(unCheckIds)))
    },
    removeStrategyApp(rowDatas) {
      const checkedPageRowKeysSet = new Set(this.checkedPageRowKeys)
      // 用来存储当前页还有哪些策略程序被勾选
      const checkedIdsSet = new Set(
        rowDatas.filter(item => checkedPageRowKeysSet.has(item.id)).map(item => item.id)
      );

      // 收集取消勾选的数据
      const toRemove = new Set(
        this.checkedPageRowKeys.filter(item => !checkedIdsSet.has(item))
      );

      // 从checkedRowKeys数组里移除取消勾选的数据
      this.checkedRowKeys = this.checkedRowKeys.filter(data => !toRemove.has(data));
    },
    appSelectionChangeEnd: function(rowDatas) {
      if (this.importGroupAble) {
        // 获取被移除的程序（需要获取原先策略里存在的程序，哪些被取消勾选了）
        this.getRemoveApp(rowDatas)
        this.tempRowDatas = [...rowDatas]
        // 策略里的程序被移除时，更新存储策略程序的数组
        this.removeStrategyApp(rowDatas)
      }
      this.appDeleteable = rowDatas && rowDatas.length > 0
    },
    loadTypeTree() {
      return getStgTypeTree({ isModule: false, osType: this.osType }).then(res => {
        this.appTypeTreeData = [{ id: '0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: res.data }]
      })
    },
    changeTypeTree() {
      return new Promise((resolve, reject) => {
        this.changeTreeSelectNode()
        if (this.importGroupAble) {
          this.$nextTick(() => {
            this.groupTree() && this.groupTree().setCheckedKeys(this.categoryIds)
          })
        }
        resolve(this.appTypeTreeData)
      })
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleAppTypeCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleAppTypeUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.handleAppTypeDelete(data)} />
          </span>
        </div>
      )
    },
    checkChange(data, nodes) {
      if (this.importGroupAble) {
        this.categoryIds = [...data]
      }
    },
    appTypeTreeNodeCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      this.appAddBtnAble = !!checkedNode && checkedNode.data.dataId != '0'
      if (checkedNode) {
        this.appQuery.groupId = checkedNode.data.dataId
      } else {
        this.appQuery.groupId = undefined
      }
      this.appQuery.page = 1
      this.appGridTable().execRowDataApi(this.appQuery)
    },
    handleAppFilter() {
      this.appQuery.page = 1
      this.appGridTable().execRowDataApi(this.appQuery)
    },
    handleAdd(outerShow) {
      this.outerShow = true === outerShow
      this.changeTreeSelectNode()
      this.$nextTick(() => {
        this.$refs.processAddDlg.handleCreate(this.appQuery.groupId)
      })
    },
    handleBatchAdd(outerShow) {
      this.outerShow = true === outerShow
      this.changeTreeSelectNode()
      this.checkId = this.appQuery.groupId == null ? '' : this.appQuery.groupId
      this.$nextTick(() => {
        this.$refs.batchUpload.handleCreate()
      })
    },
    handleUpdate: function(row, outerShow) {
      this.outerShow = true === outerShow
      this.changeTreeSelectNode()
      this.$nextTick(() => {
        this.$refs.processAddDlg.handleUpdate(row)
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.appTypeTreeData[0].children
    },
    replaceTreeNodeType(data) {
      return Number.parseInt((data + '').replace('G', ''))
    },
    handleAppTypeCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(data.dataId)
    },
    handleAppTypeUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleAppTypeDelete: function(data) {
      this.$refs['editGroupDlg'].handleDelete(data.dataId)
    },
    createNode(data) {
      const tempData = Object.assign({}, data, { osType: this.osType })
      return createStgType(tempData)
    },
    updateNode(data) {
      const tempData = Object.assign({}, data, { osType: this.osType })
      return updateStgType(tempData)
    },
    addNodeEnd(data) {
      this.loadTypeTree().then(() => {
        this.changeTypeTree()
        const addDlgRef = this.$refs['processAddDlg']
        const batchAddDlgRef = this.$refs['batchUpload']
        // 判断是否为 新增 / 批量新增 的动作
        if (addDlgRef.dialogVisible) {
          addDlgRef.groupSelectChange(data.id)
        } else {
          batchAddDlgRef.groupTreeSelectChange(data.id)
        }
      })
    },
    updateNodeEnd(data) {
      this.loadTypeTree().then(() => {
        this.changeTypeTree()
      })
      this.$emit('updateGroupEnd', data)
    },
    deleteNodeEnd(dataId) {
      this.loadTypeTree().then(() => {
        this.changeTypeTree()
      })
      this.$emit('deleteGroupEnd', dataId)
    },
    getGroupByName(data) {
      const tempData = Object.assign({}, data, { osType: this.osType })
      return getStgTypeByName(tempData)
    },
    deleteApp() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.appGridTable().getSelectedIds()
        deleteStg({ ids: toDeleteIds.join(',') }).then(respond => {
          this.appGridTable().deleteRowData(toDeleteIds)
          this.$emit('deleteEnd', toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    selectAppEnd() {
      const rows = this.appGridTable().getSelectedDatas()
      if (rows.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text15'),
          type: 'error',
          duration: 2000
        })
        return
      }
      const selectIdSet = new Set(rows.map(item => item.id))
      this.removeCheckedRowKeys = this.removeCheckedRowKeys.filter(item =>
        !selectIdSet.has(item)
      )
      if (rows && rows.length > 0) {
        this.$emit('selectEnd', rows)
      }
      this.appVisible = false
    },
    selectTypeEnd() {
      const nodes = this.groupTree().getCheckedNodes()
      if (nodes.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text16'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (nodes && nodes.length > 0) {
        this.$emit('selectGroupEnd', nodes)
      }
      this.appVisible = false
    },
    submitEnd(data) {
      if (this.outerShow) {
        this.$emit('submitEnd', data && !Array.isArray(data) ? [data] : data)
      } else {
        this.appGridTable().execRowDataApi()
      }
      this.outerShow = false
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    md5LevelFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      } else if (data === 0) {
        return this.$t('text.disable2')
      } else {
        return this.$t('text.enable')
      }
    },
    enableFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      }
      return data > 0 ? this.$t('pages.allow') : this.$t('pages.notAllow')
    },
    groupFormatter(row, data) {
      const node = this.groupTree().getNode('G' + data)
      return node === null ? data : node.data.label
    }
  }
}
</script>

<style lang="scss" scoped>
  .editBtn{
    margin: 1px 0 0;
  }
</style>
