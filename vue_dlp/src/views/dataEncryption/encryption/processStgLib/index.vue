<template>
  <div
    v-loading="loading"
    class="app-container"
    :element-loading-text="$t('pages.importing')"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div v-if="listable" class="tree-container" :class="showTree?'':'hidden'">
      <el-tabs v-model="activeTabName" @tab-click="tabClickFunc">
        <el-tab-pane label="Windows" name="windowsLib">
          <tree-menu
            ref="windowsLibTree"
            :data="windowsLibTreeData"
            :default-expanded-keys="windowsLibExpandKeys"
            :render-content="renderContent"
            @node-click="libTypeNodeCheckChange"
          />
        </el-tab-pane>
        <el-tab-pane label="Linux" name="linuxLib">
          <tree-menu
            ref="linuxLibTree"
            :data="linuxLibTreeData"
            :default-expanded-keys="linuxLibExpandKeys"
            :render-content="renderContent"
            @node-click="libTypeNodeCheckChange"
          />
        </el-tab-pane>
        <el-tab-pane label="Mac" name="macLib">
          <tree-menu
            ref="macLibTree"
            :data="macLibTreeData"
            :default-expanded-keys="macLibExpandKeys"
            :render-content="renderContent"
            @node-click="libTypeNodeCheckChange"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <!--<el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>-->
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleBatchAdd">
          {{ $t('pages.batchAdd') }}
        </el-button>
        <el-button type="primary" icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleBatchEdit">
          {{ $t('pages.batchModification') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <el-button v-show="showFinger" icon="el-icon-upload2" size="mini" @click="handleBatchRelaMd5">
          {{ $t('pages.importFingerprint') }}
        </el-button>
        <el-button v-permission="'141'" icon="el-icon-download" size="mini" @click="handleExportStg">
          {{ $t('button.export') }}
        </el-button>
        <el-dropdown v-permission="'142'" style="padding-left: 10px;" @command="handleImportControl">
          <el-button size="mini">
            {{ $t('button.import') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1" icon="el-icon-reading">{{ $t('pages.importFromFile') }}</el-dropdown-item>
            <el-dropdown-item :command="2" icon="el-icon-reading">{{ $t('pages.importFromLibrary') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button v-if="osType===1" icon="el-icon-setting" size="mini" @click="handleConfig">
          {{ $t('pages.fileOutgoingConfig') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.typeOptions1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="urlList" :col-model="colModel" :row-data-api="rowDataApi" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="downloadDialogVisible"
      width="600px"
      @dragDialog="handleDrag"
      @close="downloadClose"
    >
      <Form ref="downloadDataForm" :model="tempF" label-position="right" label-width="120px" style="width: 500px; margin-left:20px;">

        <FormItem :label="$t('pages.appType')" prop="typeId">
          <tree-select v-model="tempF.typeId" :data="selectLibTypeTreeData" node-key="dataId" :checked-keys="checkedParentType" :width="296" @change="downloadSelectChange" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <common-downloader
          :loading="submitting"
          :name="filename"
          :button-name="$t('button.confirm')"
          button-type="primary"
          button-icon=""
          :disabled="!tempF.typeId"
          :before-download="beforeDownload"
          @download="downloadConfirm"
        />
        <el-button @click="downloadDialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.processStgLib_Msg20')"
      show-parent
      :group-label="$t('pages.groupName1')"
      :parent-label="$t('pages.install_Msg44')"
      :group-tree-data="groupTreeData"
      :add-func="createLibType"
      :update-func="updateLibType"
      :delete-func="deleteStgType"
      :edit-valid-func="getLibTypeByName"
      @addEnd="loadLibTypeTree"
      @updateEnd="loadLibTypeTree"
      @deleteEnd="loadLibTypeTree"
    />
    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="textMap.delete"
      :dlg-title="$t('pages.controlApp')"
      :select-tree-data="groupTreeData"
      :delete-group-and-data="deleteStgTypeAndData"
      :delete-func="deleteStgType"
      :move-group-to-other="moveStgTypeToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    />

    <process-add-dlg ref="processAddDlg" :os-type="osType" :group-tree-data="selectLibTypeTreeData" :show-finger="showFinger" :show-net="showNet" @success="handleFilter"/>
    <batch-upload ref="batchUpload" :os-type="osType" :type-tree-data="selectLibTypeTreeData" :append-to-body="false" :show-finger="showFinger" :show-net="showNet" :group-id="typeId" @success="handleFilter"/>
    <import-stg ref="importProcessDlg" :os-type="osType" accept=".tip" data-type="1" :term-able="false" :user-able="false" :auto-name="false" :import-type2="importType2" @success="importProcessSuccess"/>
    <batch-edit ref="batchEdit" :os-type="osType" @success="handleFilter"/>
    <batch-rela-md5 ref="batchRelaMd5" :os-type="osType" :append-to-body="false" @success="handleFilter"/>
    <process-lib-import-dlg ref="libImportDlg" :os-type="osType" :group-tree-data="groupTreeData[0].children" :show-net="activeTabName != 'windowsLib' ? false : true" :show-finger="activeTabName != 'windowsLib' ? false : true" @submitEnd="importProcessSuccess"/>
  </div>
</template>

<script type="text/jsx">
import {
  getStgTypeTree, getStgTypeByName, getStgPage, createStgType, updateStgType,
  deleteStgType, deleteStg, deleteStgTypeAndData, moveStgTypeToOther,
  getImportProgress, exportStg, countChildByGroupId
} from '@/api/dataEncryption/encryption/processStgLib'
import { isSameTimestamp, initTimestamp } from '@/utils'
import BatchUpload from '@/views/dataEncryption/encryption/processStgLib/batchUploadProcess'
import ImportStg from '@/views/common/importStg'
import BatchEdit from '@/views/dataEncryption/encryption/processStgLib/batchEditProcess'
import BatchRelaMd5 from '@/views/dataEncryption/encryption/processStgLib/batchRelaMd5'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'
import ProcessAddDlg from './processAddDlg'
import CommonDownloader from '@/components/DownloadManager/common'
import ProcessLibImportDlg from './libImportDlg'

export default {
  name: 'ProcessStgLib',
  components: { ProcessLibImportDlg, ProcessAddDlg, ImportStg, BatchUpload, BatchEdit, BatchRelaMd5, EditGroupDlg, DeleteGroupDlg, CommonDownloader },
  props: {
    showNet: { type: Boolean, default: true },
    showFinger: { type: Boolean, default: true },    // 是否隐藏掉列表内容
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      filename: this.$t('route.' + this.$route.meta.title) + '.tip',
      loading: false,
      colModel: [
        { prop: 'processName', label: 'programName', width: '150', sort: 'custom', fixed: true },
        { prop: 'groupId', label: 'appType', width: '120', sort: 'custom', type: 'button',
          buttons: [{ formatter: this.groupFormatter, click: this.groupClick }]
        },
        { prop: 'decReadSfx', label: 'fileSuffixOpen', width: '150' },
        { prop: 'encWriteSfx', label: 'fileSuffixSave', width: '150' },
        { prop: 'encOpenSfx', label: 'fileSuffixVisit', width: '150' },
        { prop: 'checkMd5', hidden: !this.showFinger, label: 'enableAntiCounterfeiting', width: '120', sort: 'custom', formatter: this.md5LevelFormatter },
        { prop: 'disableNet', label: 'restrictInternetAccess', hidden: !this.showNet, width: '100', sort: 'custom', formatter: this.disableNetFormatter },
        { prop: 'enablePast', label: 'pasteEncryption', width: '100', sort: 'custom', formatter: this.enableFormatter },
        { prop: 'name', label: 'fileDescription', width: '150', sort: 'custom' },
        { label: 'operate', hidden: false, type: 'button', fixed: 'right', fixedWidth: 50,
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: null,
        groupIds: ''
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      osType: 1, // 进程系统类型：1-windows，2-linux，4-mac
      activeTabName: 'windowsLib',
      windowsLibTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: [] }],
      windowsLibExpandKeys: ['G0'],
      linuxLibTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: [] }],
      linuxLibExpandKeys: ['G0'],
      macLibTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: [] }],
      macLibExpandKeys: ['G0'],
      curLabelMap: {},
      selectLibTypeTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.processStgLib_Msg20'), parentId: '', children: [] }],
      typeId: null,
      checkedParentType: [],
      tempF: {},
      defaultTempF: { // 表单字段
        typeId: null,
        decReadSfx: '',
        encWriteSfx: '',
        encOpenSfx: '',
        enablePast: 0,
        checkMd5: 0,
        md5Level: 3,
        fingerPrintIds: [],
        processList: []
      },
      downloadDialogVisible: false,
      dialogStatus: '',
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      },
      submitting: false,
      textMap: {
        update: this.$t('pages.processStgLib_Msg23'),
        create: this.$t('pages.processStgLib_Msg24'),
        createGroup: this.$t('pages.processStgLib_Msg25'),
        updateGroup: this.$t('pages.processStgLib_Msg26'),
        download: this.$t('pages.downloadStrategy')
      },
      importType2: {
        name: this.$t('pages.importAndAdd'),
        tipMsg: this.$t('pages.importAndAdd_Msg')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['urlList']
    },
    groupTree() {
      if (this.osType === 2) {
        return this.$refs['linuxLibTree']
      } else if (this.osType === 4) {
        return this.$refs['macLibTree']
      } else {
        return this.$refs['windowsLibTree']
      }
    },
    groupTreeData() {
      if (this.osType === 2) {
        return this.linuxLibTreeData
      } else if (this.osType === 4) {
        return this.macLibTreeData
      } else {
        return this.windowsLibTreeData
      }
    },
    groupExpandKeys() {
      if (this.osType === 2) {
        return this.linuxLibExpandKeys
      } else if (this.osType === 4) {
        return this.macLibExpandKeys
      } else {
        return this.windowsLibExpandKeys
      }
    }
  },
  created() {
    initTimestamp(this)
    this.resetTemp()
    this.loadLibTypeTree()
  },
  activated() {
    if (!isSameTimestamp(this, 'ProcessStg')) {
      this.gridTable.execRowDataApi(this.query)
    }
    // this.loadLibTypeTree()
  },
  methods: {
    deleteStgTypeAndData,
    moveStgTypeToOther,
    deleteStgType,
    tabClickFunc(tab, event) {
      if (tab.name.indexOf('linux') > -1) {
        this.osType = 2
      } else if (tab.name.indexOf('mac') > -1) {
        this.osType = 4
      } else {
        this.osType = 1
      }
      this.loadLibTypeTree()
      this.query.groupId = null
      this.query.groupIds = ''
      this.query.searchInfo = null
      this.groupTree && this.groupTree.clearSelectedNode()
      this.gridTable && this.gridTable.clearRowData()
      this.handleFilter()
    },
    getTree() {
      return this.groupTree
    },
    rowDataApi: function(option) {
      const queryParam = Object.assign({}, this.query, option, { isModule: false, osType: this.osType })
      return getStgPage(queryParam)
    },
    // table数据更新后，切换labelMap的数据源
    afterLoad() {
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    removeGroupEnd(dataId) {
      const nodeData = this.groupTree.findNode(this.groupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.groupTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
    },
    refreshTableData() {
      this.query.groupId = null
      this.gridTable.clearRowData()
      this.gridTable.execRowDataApi()
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.groupTree.findNode(this.groupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.groupTree.selectCurrentNode(nodeData.id)
      }
    },
    listNodeIdAndChildNodeIds(checkNodeIds, checkedData) {
      checkNodeIds.push(checkedData.dataId)
      if (checkedData.children) {
        checkedData.children.forEach(data => {
          this.listNodeIdAndChildNodeIds(checkNodeIds, data)
        })
      }
    },
    libTypeNodeCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedNode && checkedNode.data.dataId !== '0'
      const checkNodeIds = []
      this.listNodeIdAndChildNodeIds(checkNodeIds, checkedNode.data)
      this.query.groupIds = checkNodeIds.join(',')
      if (checkedNode) {
        this.query.groupId = checkedNode.data.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    // 获取自定义库的树数据
    loadLibTypeTree: function(data) {
      getStgTypeTree({ isModule: false, osType: this.osType }).then(respond => {
        this.groupTreeData[0].children = respond.data
        this.curLabelMap = this.getNodeLabelMap(this.curLabelMap, respond.data, true)
        if (data) {
          let parentId
          if (typeof data == 'string') {
            const node = this.getTree().getNode('G' + data)
            parentId = node.data.parentId
          } else {
            parentId = 'G' + data.parentId
          }
          this.groupExpandKeys.splice(0, 1, parentId)
        }
      })
    },
    // 通过树数据获取label的map数据
    getNodeLabelMap(labelMap, treeData, init) {
      if (init) {
        labelMap = {}
      }
      treeData.forEach(data => {
        labelMap[data.dataId] = data.label
        if (data.children) {
          this.getNodeLabelMap(labelMap, data.children, false)
        }
      })
      return labelMap
    },
    setSelectLibTypeTreeData() {
      const isTypeTree = this.dialogStatus.indexOf('G') > 0
      const treeData = this.groupTreeData[0]
      this.selectLibTypeTreeData.splice(0)
      if (isTypeTree) {
        this.selectLibTypeTreeData.push(treeData)
      } else {
        treeData.children.forEach((nodeData) => {
          this.selectLibTypeTreeData.push(nodeData)
        })
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      // 必须要使用JSON.parse(JSON.stringify来复制对象，
      // 不然的话defaultTemp里面如果有数组，会把数组地址复制过去，从而导致修改temp的数组内容而影响到了defaultTemp里面的内容
      this.tempF = JSON.parse(JSON.stringify(this.defaultTempF))
    },
    refresh() {
      this.query.searchInfo = ''
      this.query.groupId = undefined
      this.groupTree && this.groupTree.clearSelectedNode()
      this.handleFilter()
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreateLibType(data) {
      this.$refs['editGroupDlg'].handleCreate(data.dataId)
    },
    handleUpdateLibType: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    handleDeleteLibType(data) {
      countChildByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: data.dataId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    createLibType(data) {
      const tempData = Object.assign({}, data, { osType: this.osType })
      return createStgType(tempData)
    },
    updateLibType(data) {
      const tempData = Object.assign({}, data, { osType: this.osType })
      return updateStgType(tempData)
    },
    getLibTypeByName(data) {
      const tempData = Object.assign({}, data, { osType: this.osType })
      return getStgTypeByName(tempData)
    },
    handleAdd() {
      this.setSelectLibTypeTreeData()
      this.$nextTick(() => {
        this.$refs.processAddDlg.handleCreate(this.query.groupId)
      })
    },
    handleBatchAdd() {
      this.setSelectLibTypeTreeData()
      this.typeId = this.query.groupId
      this.$nextTick(() => {
        this.$refs.batchUpload.handleCreate()
      })
    },
    handleBatchEdit() {
      const datas = this.gridTable.getSelectedDatas()
      if (datas.length == 0) {
        this.$message({
          message: this.$t('pages.processStgLib_Msg27'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.$refs.batchEdit.handleCreate(datas)
    },
    handleBatchRelaMd5() {
      this.$refs.batchRelaMd5.handleCreate()
    },
    handleExportStg() {
      this.dialogStatus = 'download'
      this.resetTemp()
      this.setSelectLibTypeTreeData()
      if (this.query.groupId) {
        this.checkedParentType.splice(0, 1, this.query.groupId)
      } else {
        this.checkedParentType.splice(0)
      }
      this.tempF.typeId = this.query.groupId
      this.downloadDialogVisible = true
      this.$refs.fingerTree && this.$refs.fingerTree.clearFilter()
      this.$nextTick(() => {
        this.$refs['downloadDataForm'] && this.$refs['downloadDataForm'].clearValidate()
      })
    },
    downloadSelectChange(data, options) {
      this.query.groupId = data
      this.tempF.typeId = data
      this.filename = (Array.isArray(options) ? options[0].label : options.label) + '.tip'
    },
    beforeDownload() {
      if (this.tempF.typeId === undefined || this.tempF.typeId === null || this.tempF.typeId === '') {
        this.downloadDialogVisible = false
        return false
      }
      return true
    },
    downloadConfirm(file) {
      const data = { osType: this.osType, groupId: this.query.groupId, name: file.name }
      const opts = { file, jwt: true, topic: this.$route.name }
      exportStg(data, opts)
      this.downloadDialogVisible = false
    },
    downloadClose() {
      // this.query.groupId = this.groupTree.tree().getCurrentNode().dataId
    },
    handleImportControl(type) {
      if (type === 1) {
        this.$refs.importProcessDlg.show()
      } else {
        // 如果后台正在进行导入操作，则提示并禁止本次导入
        getImportProgress().then(resp => {
          if (resp.data.percent == 0 || resp.data.percent >= 100) {
            this.$refs.libImportDlg.show()
          } else {
            this.$notify({ title: this.$t('text.prompt'), message: this.$t('pages.importExecutingMsg', { percent: resp.data.percent }), type: 'info', duration: 2000 })
          }
        })
      }
    },
    importProcessSuccess() {
      this.loadLibTypeTree()
      this.handleFilter()
    },
    handleUpdate: function(row) {
      this.setSelectLibTypeTreeData()
      this.$nextTick(() => {
        this.$refs.processAddDlg.handleUpdate(row)
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStg({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    handleConfig() {
      this.$router.push('/dataEncryption/encryption/processStgLibConfig')
    },
    suffixValidator(rule, value, callback) {
      if (value == '*.*') {
        callback(new Error(this.$t('pages.appGroup_text5')))
      } else {
        callback()
      }
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleCreateLibType(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleUpdateLibType(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.handleDeleteLibType(data)} />
          </span>
        </div>
      )
    },
    replaceTreeNodeType: function(data) {
      return Number.parseInt((data + '').replace('G', ''))
    },
    groupClick: function(row) {
      const node = this.getTree().getNode('G' + row.groupId);
      if (null == node) {
        return
      }
      this.expandNodeParent(node)
      this.libTypeNodeCheckChange(null, node)
      this.$nextTick(() => {
        this.getTree().setCurrent(node)
      })
    },
    expandNodeParent(node) {
      if (node.parent) {
        node.parent.expanded = true
        this.expandNodeParent(node.parent)
      }
    },
    enableFormatter(row, data) {
      return data > 0 ? this.$t('pages.allow') : this.$t('pages.notAllow')
    },
    groupFormatter(row) {
      return this.curLabelMap[row.groupId] || ''
    },
    md5LevelFormatter(row, data) {
      return data == 1 ? this.$t('pages.enable') : this.$t('pages.notEnabled')
    },
    disableNetFormatter(row, data) {
      return data == 1 ? this.$t('pages.limit') : this.$t('pages.notLimit')
    }
  }
}
</script>
<style lang="scss" scoped>
  .no-header-tab>>>.el-tabs__header{
    display: none;
  }
  .no-header-tab>>>.el-tabs__content{
    height: 100%;
  }
  .typeGroupId:before{
    content: '*';
    color: #F56C6C;
    position: relative;
    left: 56px;
    top: 23px;
  }
  .tree-container>>>.el-tabs__item.is-top{
    padding: 0 10px;
  }
</style>
