<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.workModeStg')"
      :stg-code="72"
      time-able
      :time-mode="1"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="debounceSlotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-row>
          <el-col :span="10">
            <FormItem label-width="100px" label="">
              <el-checkbox
                v-model="temp.ctrlCode"
                :true-label="1"
                :false-label="0"
                :disabled="!formable"
                @change="codeChange"
              >{{ $t('table.ctrlCode') }}</el-checkbox>
            </FormItem>
          </el-col>
          <el-col :span="12" :offset="1">
            <FormItem label-width="0px" prop="closeCode">
              <el-checkbox-group v-model="temp.closeCode" :disabled="!formable || !temp.ctrlCode">
                <!--<el-checkbox :label="1">关闭终端监控</el-checkbox>-->
                <el-checkbox :label="2">{{ $t('table.forbidApp') }}</el-checkbox>
              </el-checkbox-group>
            </FormItem>
          </el-col>
        </el-row>
        <el-card v-if="temp.closeCode.indexOf(2) > -1" class="box-card" :body-style="{'padding': '5px'}">
          <div slot="header">
            <span>{{ $t('table.forbidApp') }}</span>
            <el-tooltip class="item" effect="dark" placement="bottom-start">
              <div slot="content">{{ $t('pages.workMode_text1') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-button v-if="formable" size="small" @click="handleCreateApp">
              {{ $t('button.insert') }}
            </el-button>
            <el-button v-if="formable" size="small" @click="handleImportAppFromSelfLib">
              {{ $t('text.infoImport', { info: $t('pages.workModeGroup') }) }}
            </el-button>
            <el-button v-if="formable" size="small" :disabled="!checkAppDeleteable" @click="handleDeleteCheckedApp">
              {{ $t('pages.delete') }}
            </el-button>
            <el-button type="primary" icon="el-icon-search" size="small" :title="$t('table.search')" style="margin-left: 1px;height: 26px;float: right;" @click="handleFilter"/>
            <el-input v-model="searchInfo" v-trim clearable :placeholder="$t('text.pleaseEnterInfo',{ info : $t('table.processName1')})" style="width: 150px;height: 26px;float: right;" @keyup.enter.native="handleFilter"></el-input>
          </div>
          <grid-table
            ref="checkedAppGrid"
            :show-pager="false"
            :height="170"
            :multi-select="formable"
            :col-model="checkedColModel"
            :row-datas="filterRowData"
            @selectionChangeEnd="checkAppSelectionChangeEnd"
          />
        </el-card>
        <ResponseContent
          v-if="temp.closeCode.indexOf(2) > -1"
          ref="responseContent"
          :show-select="true"
          :editable="formable"
          read-only
          :prop-check-rule="propCheckRule"
          :prop-rule-id="propRuleId"
          @getRuleId="getRuleId"
        />
      </template>
    </stg-dialog>
    <app-import-table
      ref="appImportTable"
      :os-type="osType==7?1:osType"
      :support-md5="supportMd5"
      :type-tree-data="typeTreeData"
      :group-root-name="$t('pages.workModeGroup')"
      :list="getSoftInfoPage"
      :count-by-group="countInfoByGroupId"
      :create="createSoftInfo"
      :batch-create="batchAddSoftInfo"
      :update="updateSoftInfo"
      :delete="deleteSoftInfo"
      :import-func="importSoftInfoFromLib"
      :create-group="createSoftType"
      :update-group="updateSoftType"
      :delete-group="deleteSoftType"
      :get-group-by-name="getSoftTypeByName"
      support-select-terminal-soft
      @submitEnd="appMd5ImportSubmitEnd"
    />
  </div>
</template>

<script>
import {
  getGroupIdAndSoftNameMap,
  getStrategyByName, createStrategy, updateStrategy,
  createSoftType, updateSoftType, deleteSoftType, getSoftTypeByName, getSoftTypeTreeNode,
  createSoftInfo, batchAddSoftInfo, updateSoftInfo, deleteSoftInfo, importSoftInfoFromLib, getSoftInfoPage, countInfoByGroupId
} from '@/api/dataEncryption/encryption/workMode'
import { isEmpty } from '@/utils'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import AppImportTable from '@/views/system/baseData/appLibrary/appImportTableDlg'

export default {
  name: 'WorkModeDlg',
  components: { AppImportTable, ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      osType: 1,
      supportMd5: false, // 是否支持指纹防伪冒（某些功能终端还不支持，所以加个开关）      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        timeId: 1,
        ctrlCode: 0,
        closeCode: [],
        ruleId: '',
        checkApp: []
      },
      checkedColModel: [
        { prop: 'typeId', label: 'typeId', width: '80', sort: true, sortOriginal: true, formatter: this.typeIdFormatter },
        { prop: 'processName', label: 'processName1', width: '150', sort: true, formatter: this.nameFormatter },
        /* { prop: 'productVersion', label: '版本号', width: '150' },*/
        { prop: 'checkMd5', hidden: () => !this.supportMd5, label: 'isCheckMd5', width: '150', sort: true, formatter: this.md5LevelFormatter },
        { label: 'operate', type: 'button', fixedWidth: '150', hidden: !this.formable,
          buttons: [
            { label: 'edit', isShow: (row) => { return row.itemType == 1 }, click: this.handleUpdateProcess },
            { label: 'viewProcess', isShow: (row) => { return row.itemType == 2 }, click: this.handleViewProcess }
          ]
        }
      ],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ]
      },
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5Level1')
      },
      checkAppDeleteable: false,
      typeTreeDataWin: [],
      typeTreeDataLinux: [],
      typeTreeDataMac: [],
      propCheckRule: false,
      propRuleId: undefined,
      dialogStatus: '',
      timer: null,
      searchInfo: undefined,
      filterRowData: []
    }
  },
  computed: {
    typeTreeData() {
      if (this.osType == 1) {
        return this.typeTreeDataWin
      } else if (this.osType == 2) {
        return this.typeTreeDataLinux
      } else if (this.osType == 4) {
        return this.typeTreeDataMac
      } else if (this.osType == 7) {
        return this.typeTreeDataWin
      }
      return null
    }
  },
  created() {
    this.loadAppTypeTree()
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    createStrategy(temp) {
      if (this.temp.closeCode.indexOf(2) > -1) {
        if (this.temp.checkApp.length === 0) {
          this.$message({
            message: this.$t('text.cantNullInfo', { info: this.$t('table.forbidApp') }),
            type: 'error',
            duration: 2000
          })
          return Promise.reject()
        } else {
          return createStrategy(temp)
        }
      } else {
        return createStrategy(temp)
      }
    },
    updateStrategy(temp) {
      if (this.temp.closeCode.indexOf(2) > -1) {
        if (this.temp.checkApp.length === 0) {
          this.$message({
            message: this.$t('text.cantNullInfo', { info: this.$t('table.forbidApp') }),
            type: 'error',
            duration: 2000
          })
          return Promise.reject()
        } else {
          return updateStrategy(temp)
        }
      } else {
        return updateStrategy(temp)
      }
    },
    getStrategyByName,
    createSoftInfo,
    batchAddSoftInfo,
    updateSoftInfo,
    deleteSoftInfo,
    importSoftInfoFromLib,
    getSoftInfoPage,
    countInfoByGroupId,
    createSoftType,
    updateSoftType,
    deleteSoftType,
    getSoftTypeByName,
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.propCheckRule = false
      this.propRuleId = undefined
      this.searchInfo = undefined
    },
    closed() {
      this.resetTemp()
    },
    slotChange(name, slotTemp) {
      this.osType = name
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      if (this.temp.ruleId) {
        this.propCheckRule = true
        this.propRuleId = this.temp.ruleId
      }
      this.handleFilter()
    },
    // 防抖处理
    debounceSlotChange(name, slotTemp) {
      if (!this.timer) {
        this.timer = setTimeout(() => {
          this.slotChange(name, slotTemp)
        }, 50)
      } else {
        clearTimeout(this.timer)
        this.timer = setTimeout(() => {
          this.slotChange(name, slotTemp)
        }, 50)
      }
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    loadAppTypeTree: function() {
      getSoftTypeTreeNode({ osType: 1 }).then(respond => {
        this.typeTreeDataWin = respond.data
      })
      getSoftTypeTreeNode({ osType: 2 }).then(respond => {
        this.typeTreeDataLinux = respond.data
      })
      getSoftTypeTreeNode({ osType: 4 }).then(respond => {
        this.typeTreeDataMac = respond.data
      })
    },
    checkAppSelectionChangeEnd(rowDatas) {
      this.checkAppDeleteable = rowDatas && rowDatas.length > 0
    },
    async handleFilter() {
      this.filterRowData.splice(0)
      if (!isEmpty(this.searchInfo)) {
        const groupIds = []
        this.temp.checkApp.forEach(item => {
          if (item.itemType == 1 && item.processName && item.processName.toLowerCase().includes(this.searchInfo)) {
            this.filterRowData.push(item)
          }
          if (item.itemType == 2) {
            groupIds.push(item.id)
          }
        })
        if (groupIds.length > 0) {
          await getGroupIdAndSoftNameMap({ groupIds: groupIds.join(','), searchInfo: this.searchInfo }).then(resp => {
            this.temp.checkApp.forEach(item => {
              if (item.itemType == 2 && resp.data[item.id]) {
                this.filterRowData.push(Object.assign({ childrenNames: resp.data[item.id] }, item))
              }
            })
          })
        }
      } else {
        this.filterRowData.push(...this.temp.checkApp)
      }
    },
    getProcessListId(processList) {
      const typeIds = []
      const processIds = []
      processList.forEach(item => {
        if (item.itemType == 2) {
          // 通过类别添加，需要将类别id加入
          typeIds.push(item.id)
        } else {
          // 非 通过类别添加的程序（即手动添加的程序），需要将程序id加入
          processIds.push(item.id)
        }
      })
      const resultMap = new Map()
      typeIds.length > 0 && resultMap.set('type', typeIds)
      processIds.length > 0 && resultMap.set('process', processIds)
      return resultMap;
    },
    handleImportAppFromSelfLib() {
      const map = this.getProcessListId(this.temp.checkApp)
      const typeIds = map.get('type') ? map.get('type').map(id => `G${id}`) : null
      const processIds = map.get('process') || null
      this.$refs.appImportTable.show(typeIds, processIds)
    },
    handleCreateApp() {
      this.$refs.appImportTable.showBatchCreate()
    },
    handleDeleteCheckedApp() {
      this.$confirmBox(this.$t('pages.install_Msg28'), this.$t('text.prompt')).then(() => {
        const rows = this.$refs['checkedAppGrid'].getSelectedDatas()
        // 创建待删除键的集合
        const deleteKeys = new Set(rows.map(({ id, itemType }) => `${id}-${itemType}`));
        // 使用filter一次性过滤
        this.temp.checkApp = this.temp.checkApp.filter(({ id, itemType }) =>
          !deleteKeys.has(`${id}-${itemType}`)
        );
        this.handleFilter()
      }).catch(() => {})
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.dialogStatus = 'update'
      this.$refs['stgDlg'].show(row, this.formable)
    },
    codeChange(val) {
      if (val === 0) {
        this.temp.closeCode = []
      }
    },
    formatRowData(rowData) {
      rowData.closeCode = this.numToList(rowData.closeCode, 2)
    },
    formatFormData(formData) {
      formData.softList = []
      if (formData.closeCode.indexOf(2) < 0) {
        formData.checkApp = []
      }
      formData.closeCode = this.getSum(formData.closeCode)
      delete formData.isPermanent
      delete formData.beginTime
      delete formData.endTime
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    appAddSubmitEnd(data, dlgStatus) {
      const appBean = {
        id: data.id,
        md5Level: data.md5Level,
        checkMd5: data.checkMd5,
        processName: data.processName,
        productVersion: data.productVersion,
        itemType: 1,  // 记录类型：1应用程序2类别
        typeId: data.typeId
      }
      this.temp.checkApp.unshift(appBean)
    },
    appMd5ImportSubmitEnd(data, editMode) {
      // editMode编辑模式，0-删除，1-添加程序，2-添加类别
      if (editMode === 0) {
        // 创建待删除键的集合
        const deleteKeys = new Set(data.map(({ id, itemType }) => `${id}-${itemType}`));
        // 使用filter一次性过滤
        this.temp.checkApp = this.temp.checkApp.filter(({ id, itemType }) =>
          !deleteKeys.has(`${id}-${itemType}`)
        );
      } else {
        // 创建一个 Map 用于存储列表中的元素，以方便快速查找
        const listMap = new Map();
        this.temp.checkApp.forEach((item) => {
          const key = `${item.id}-${item.itemType}`;
          listMap.set(key, item);
        });
        // 提前计算 itemType
        const itemType = editMode === 2 ? 2 : 1;

        data.forEach(item => {
          const id = itemType === 1 ? item.id : item.dataId;
          const key = `${id}-${itemType}`;
          const existApp = listMap.get(key);
          if (!existApp) {
            if (editMode === 1) {
              this.appAddSubmitEnd(item)
            } else if (editMode === 2 && item.id != '0') {
              const appBean = {
                id: item.dataId,
                typeId: item.dataId,
                typeName: item.label,
                processName: item.label,
                itemType: 2  // 记录类型：1应用程序2类别
              }
              this.temp.checkApp.unshift(appBean)
              // 同时更新 Map
              listMap.set(`${item.dataId}-2`, appBean);
            }
          } else {
            const { md5Level, checkMd5: isCheckMd5, typeId } = item
            Object.assign(existApp, { md5Level, isCheckMd5, typeId })
          }
        })
      }
      this.handleFilter()
    },
    typeIdFormatter(row, data) {
      const typeId = row.itemType == 2 ? row.id : row.typeId
      let msg = ''
      if (this.typeTreeData) {
        this.typeTreeData.some(node => {
          if (node.dataId == typeId) {
            msg = node.label
            return true
          }
        })
      }
      return msg
    },
    md5LevelFormatter(row, data) {
      return this.$refs.appImportTable.md5LevelFormatter(row, data)
    },
    nameFormatter(row, data) {
      if (row.itemType == 2) {
        return this.$t('pages.collectAll') + (!row.childrenNames ? '' : `(${this.$t('pages.include')}: ${row.childrenNames.join(', ')})`)
      } else {
        return row.processName
      }
    },
    itemTypeFormatter(row, data) {
      return this.itemTypeMap[data]
    },
    handleUpdateProcess(row) {
      this.$refs.appImportTable.handleUpdate(row)
    },
    handleViewProcess(row) {
      if (this.$refs.appImportTable) {
        const map = this.getProcessListId(this.temp.checkApp)
        const typeIds = map.get('type') ? map.get('type').map(id => `G${id}`) : null
        const processIds = map.get('process') || null
        this.$refs.appImportTable.show(typeIds, processIds, row.typeId);
      }
    }
  }
}
</script>
