<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      time-able
      :title="$t('pages.clipboardStg')"
      :stg-code="68"
      :pane-height="600"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      os-label-w="110px"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-tabs v-model="activeTab" style="min-height: 260px;">
          <el-tab-pane :lazy="false" :label="$t('pages.encryptedCode')" name="first">
            <FormItem :label="$t('pages.controlParams')">
              <el-radio-group v-model="temp.encryptedCode" :disabled="!formable" @change="encryptedCodeChange">
                <el-radio :label="0">{{ $t('pages.encryptedCode1') }}</el-radio>
                <el-radio :label="2">{{ $t('pages.encryptedCode2') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <FormItem v-if="slotName == 1" :label="$t('pages.encryptedMaxLength')" prop="encryptedMaxLength">
              <el-tooltip class="item" effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.clipboard_text1') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-input-number v-model="temp.encryptedMaxLength" :disabled="!formable || temp.encryptedCode == 0" :min="0" :max="999999999" :step="1" step-strictly :controls="false" style="width: 150px;"/>
            </FormItem>
            <el-card v-if="slotName == 1" class="box-card" :body-style="{'padding': '0px', 'padding-top': '5px' }">
              <div slot="header">
                <span>{{ temp.encryptedCode==0?$t('pages.clipboard_text2'):$t('pages.clipboard_text3') }}</span>
                <el-button v-if="formable" size="small" @click="handleImportApp">
                  {{ $t('button.import') }}
                </el-button>
                <el-button v-if="formable" size="small" :disabled="!appDeleteAble" @click="handleDeleteApp">
                  {{ $t('button.delete') }}
                </el-button>
                <span v-if="formable" class="searchCon" style="float: right">
                  <el-input v-model="searchInfo" clearable :placeholder="$t('table.processName1')" style="width: 125px;height: 27px;" @keyup.enter.native="handleFilter" />
                  <el-button type="primary" icon="el-icon-search" size="small" :title="$t('table.search')" style="margin-bottom: 0" @click="handleFilter"/>
                </span>
              </div>
              <grid-table
                ref="checkedAppGrid"
                :show-pager="false"
                :height="200"
                :col-model="checkedColModel"
                :row-data-api="getAppRows"
                :auto-load="false"
                @selectionChangeEnd="appSelectChangeEnd"
              />
            </el-card>
            <el-checkbox v-model="temp.enableClipboardAudit" :disabled="!formable" :true-label="1" :false-label="0">
              {{ $t('pages.recordCutUseLog') }}
            </el-checkbox><br>
          </el-tab-pane>
          <el-tab-pane v-if="slotName == 1" :lazy="false" :label="$t('pages.unencryptedCode')" name="second" style="padding: 0px;min-height: 135px;">
            <FormItem :label="$t('pages.controlParams')" prop="unencryptedCode">
              <el-radio-group v-model="temp.unencryptedCode" :disabled="!formable">
                <el-radio :label="0">{{ $t('pages.unencryptedCode1') }}</el-radio>
                <el-radio :label="1">{{ $t('pages.unencryptedCode2') }}</el-radio>
                <el-radio :label="4">{{ $t('pages.unencryptedCode4') }}</el-radio>
                <el-radio :label="5">{{ $t('pages.unencryptedCode5') }}</el-radio>
                <el-radio :label="3">{{ $t('pages.unencryptedCode3') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <FormItem :label="$t('pages.encryptedMaxLength')" prop="unencryptedMaxLength">
              <el-tooltip class="item" effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.clipboard_text1') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-input-number v-model="temp.unencryptedMaxLength" :disabled="!formable || [1,3].indexOf(temp.unencryptedCode)== -1" :min="0" :max="999999999" :step="1" step-strictly :controls="false" style="width: 150px;"/>
            </FormItem>
            <el-card v-if="[0,1].indexOf(temp.unencryptedCode)!=-1" class="box-card" style="padding: 10px;">
              <div slot="header">
                <span>{{ temp.unencryptedCode==0?$t('pages.forbidProcess'):$t('pages.allowProcess') }}</span>
                <el-button :disabled="!formable || [0,1].indexOf(temp.unencryptedCode)== -1" @click="handleCreate3">
                  {{ $t('button.import') }}
                </el-button>
                <br/>
                <span v-show="showErrMsg" slot="tail" style="color:#F56C6C;margin-top: 6px">{{ $t('pages.required1') }}</span>
              </div>
              <tag :list="temp.unencryptedProcess" :disabled="!formable || [0,1].indexOf(temp.unencryptedCode)== -1" @tagChange="processChange" />
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </template>
    </stg-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.importApp')"
      :visible.sync="processDlgVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="processForm" :hide-required-asterisk="true" :rules="rules" :model="processTemp" label-width="80px">
        <FormItem :label="$t('pages.processName1')" prop="processName">
          <el-col :span="16">
            <el-input v-model="processTemp.processName" :maxlength="255"></el-input>
          </el-col>
          <el-upload
            ref="upload"
            class="upload-demo"
            name="uploadFile"
            action="1111"
            :limit="1"
            accept=".exe"
            :show-file-list="false"
            :before-upload="beforeUpload"
          >
            <el-button size="mini" type="primary" style="margin-top: 1px;">{{ $t('pages.selectFile') }}</el-button>
          </el-upload>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createProcess()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="processDlgVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <process-select-dlg
      ref="processSelectDlg"
      :add-able="false"
      :import-group-able="false"
      @selectEnd="selectAppEnd"
    />
  </div>
</template>

<script>
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/dataEncryption/encryption/clipboard'
import { getProcessByIds } from '@/api/dataEncryption/encryption/processStgLib'
import { findNode } from '@/utils/tree'
import ProcessSelectDlg from '@/views/dataEncryption/encryption/processStgLib/processSelectDlg'

export default {
  name: 'ClipboardDlg',
  components: { ProcessSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      activeTab: 'first',
      temp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        active: false,
        remark: '',
        encryptedCode: 0,
        encryptedMaxLength: 0,
        encryptedProcess: [],
        unencryptedCode: 3,
        unencryptedMaxLength: 0,
        unencryptedProcess: [],
        entityType: '',
        entityId: null,
        enableClipboardAudit: 0
      },
      processTemp: {
        processName: ''
      },
      showErrMsg: false,
      checkedColModel: [
        { prop: 'processName', label: 'processName1', width: '100', sort: true },
        { prop: 'groupId', label: 'appType', width: '100', sort: true, sortOriginal: true, formatter: this.groupFormatter },
        { prop: 'name', label: 'fileDescription', width: '150', sort: true }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        encryptedMaxLength: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        unencryptedMaxLength: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        processName: [{ required: true, validator: this.processValidator, trigger: ['blur', 'change'] }]
      },
      submitting: false,
      processDlgVisible: false,
      slotName: undefined,
      searchInfo: undefined,
      appDeleteAble: false
    }
  },
  watch: {
    'temp.encryptedProcess.length'(val) {
      this.handleFilter()
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.showErrMsg = false
      this.temp.encryptedProcess = []
      this.temp.unencryptedProcess = []
      this.searchInfo = undefined
      this.$refs['checkedAppGrid'] && this.$refs['checkedAppGrid'].clearRowData()
    },
    closed() {
      this.resetTemp()
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.activeTab = 'first'
    },
    getAppRows() {
      if (this.temp.encryptedProcess.length > 0) {
        return getProcessByIds({ ids: this.temp.encryptedProcess.join(','), groupIds: '', searchInfo: this.searchInfo })
      } else {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    selectAppEnd(data) {
      if (data && data.length > 0) {
        data.forEach(item => {
          if (this.temp.encryptedProcess.indexOf(item.id) < 0) {
            this.temp.encryptedProcess.unshift(item.id)
          }
        })
        this.handleFilter()
      }
    },
    beforeUpload(file) {
      this.processTemp.processName = file.name
      return false // 屏蔽了action的默认上传
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.$refs.processSelectDlg && this.$refs.processSelectDlg.loadTypeTree()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      if (!row.enableClipboardAudit) {
        this.$set(row, 'enableClipboardAudit', 0)
      }
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.$refs.processSelectDlg && this.$refs.processSelectDlg.loadTypeTree()
      })
    },
    handleShow(row, isGenerateStrategy) {
      this.resetTemp()
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
      this.$nextTick(() => {
        this.$refs.processSelectDlg && this.$refs.processSelectDlg.loadTypeTree()
        setTimeout(() => {
          this.handleFilter()
        }, 500)
      })
    },
    handleImportApp() {
      this.$refs.processSelectDlg.show()
    },
    handleDeleteApp() {
      const toDelIds = this.$refs['checkedAppGrid'].getSelectedKeys()
      if (toDelIds && this.temp.encryptedProcess) {
        this.temp.encryptedProcess = this.temp.encryptedProcess.filter(id => {
          // 过滤要删除的APP，类型不过滤
          return toDelIds.indexOf(id) < 0
        })
        this.handleFilter()
      }
    },
    handleFilter() {
      this.$refs['checkedAppGrid'] && this.$refs['checkedAppGrid'].execRowDataApi()
    },
    handleCreate3() {
      this.processTemp = {
        processName: ''
      }
      this.processDlgVisible = true
      this.$nextTick(() => {
        this.$refs.processForm.clearValidate()
        this.$refs.upload.clearFiles()
      })
    },
    createProcess() {
      this.$refs['processForm'].validate((valid) => {
        if (valid) {
          const index = this.temp.unencryptedProcess.findIndex(item => {
            if (item === this.processTemp.processName) {
              return true
            }
          })
          if (index !== -1) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.PrintSet_Msg13') + this.processTemp.processName + ']',
              type: 'error',
              duration: 2000
            })
            return
          }
          this.temp.unencryptedProcess.push(this.processTemp.processName)
          this.processDlgVisible = false
          this.processChange(this.temp.unencryptedProcess)
        }
      })
    },
    appSelectChangeEnd(rowDatas) {
      this.appDeleteAble = rowDatas && rowDatas.length > 0
    },
    formatRowData(rowData) {

    },
    formatFormData(formData) {
      if (formData.encryptedCode != 2) {
        formData.encryptedMaxLength = 0
      }
      if (formData.unencryptedCode != 1 && formData.unencryptedCode != 3) {
        formData.unencryptedMaxLength = 0
      }
      if (formData.unencryptedCode != 1 && formData.unencryptedCode != 0) {
        formData.unencryptedProcess = []
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    processValidator(rule, value, callback) {
      if (!this.processTemp.processName) {
        callback(new Error(this.$t('pages.clipboard_text4')))
      } else {
        const name = this.processTemp.processName.toLowerCase()
        if (name.endsWith('.exe')) {
          callback()
        } else {
          callback(new Error(this.$t('pages.clipboard_text5')))
        }
      }
    },
    processChange(tags) {
      this.showErrMsg = tags.length == 0
    },
    validateFormData(formData) {
      this.showErrMsg = false
      // 只校验windows的
      if (formData.osType === 1) {
        if ([0, 1].indexOf(formData.unencryptedCode) != -1 && formData.unencryptedProcess.length === 0) {
          this.showErrMsg = true
          // this.$notify({
          //   title: this.$t('text.prompt'),
          //   message: this.$t('pages.noprocessTip'),
          //   type: 'error',
          //   duration: 3000
          // })
          this.activeTab = 'second'
        }
      }
      return !this.showErrMsg
    },
    groupFormatter(row, data) {
      let groupTreeDatas = [];
      if (this.$refs.processSelectDlg) {
        groupTreeDatas = this.$refs.processSelectDlg.getGroupTreeData()
      }
      const node = findNode(groupTreeDatas, data, 'dataId')
      return !node ? data : node.label
    },
    encryptedCodeChange(data) {
      if (data == 0 && !this.temp.encryptedMaxLength) {
        this.temp.encryptedMaxLength = 0
      }
      this.$refs['stgDlg'].clearValidate('encryptedMaxLength')
    }
  }
}
</script>
