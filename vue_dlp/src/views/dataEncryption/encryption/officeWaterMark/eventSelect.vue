<template>
  <div v-if="show && options.length > 0">
    <div v-show="needDivider">
      <el-divider content-position="left">{{ title || label }}</el-divider>
    </div>
    <FormItem :label="label" :label-width="labelWidth">
      <el-select v-model="selectVal" multiple :clearable="clearable" :disabled="disabled" @change="change">
        <el-option v-for="(item, index) in selectOpts" :key="index" :value="item.value" :label="item.label">
          <span style="float: left">{{ item.label }}</span>
          <span v-show="showVal" style="float: right; color: #8492a6; font-size: 13px; padding-right: 15px;">{{ item.value }}</span>
        </el-option>
      </el-select>
    </FormItem>
  </div>
</template>

<script>
export default {
  name: 'EventSelect',
  props: {
    value: {
      type: Array,
      required: true
    },
    show: {
      type: Boolean,
      default: true
    },
    needDivider: {
      type: Boolean,
      default: true
    },
    label: {
      type: String,
      default: ''
    },
    labelWidth: {
      type: String,
      default: ''
    },
    allOpt: {
      type: Object,
      default() {
        return {}
      }
    },
    options: {
      type: Array,
      default() {
        return []
      }
    },
    clearable: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    showVal: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectVal: this.value
    }
  },
  computed: {
    selectOpts() {
      const opts = [...this.options]
      Object.keys(this.allOpt).length > 0 && opts.unshift(this.allOpt)
      return opts
    }
  },
  watch: {
    'value'(newVal, oldVal) {
      if (this.selectVal != newVal) {
        this.change(newVal)
      }
    }
  },
  methods: {
    change(selectedVals) {
      const len = selectedVals.length
      if (len === 0) {
        selectedVals.splice(0, len, this.allOpt.value)
      } else if (len > 1) {
        const index = (selectedVals || []).findIndex(item => item === this.allOpt.value)
        if (index >= 0) {
          if (index === len - 1) {
            selectedVals.splice(0, len, this.allOpt.value)
          } else {
            selectedVals.splice(index, 1)
          }
        } else if (len === this.options.length) {
          selectedVals.splice(0, len, this.allOpt.value)
        }
      }
      this.selectVal = selectedVals
      this.$emit('input', selectedVals)
    }
  }
}
</script>

<style scoped>

</style>
