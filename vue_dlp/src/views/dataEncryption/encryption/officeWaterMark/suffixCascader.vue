<template>
  <el-popover
    ref="popover"
    :key="popoverKey"
    v-model="showPopover"
    v-clickoutside="closePopover"
    placement="bottom-start"
    :width="width"
    :disabled="disabled"
    trigger="manual"
  >
    <el-cascader-panel
      ref="elCascaderPanel"
      :key="cascaderKey"
      v-model="cascaderVals"
      :options="options"
      :props="cProps"
      :show-all-levels="false"
      :placeholder="placeholder"
      @change="cascaderChange"
    ></el-cascader-panel>
    <el-select
      ref="selectRef"
      slot="reference"
      v-model="selectVals"
      :disabled="disabled"
      multiple
      :popper-class="`hidden`"
      @remove-tag="removeTag"
      @click.native="selectClick"
    >
      <el-option v-for="(item, index) in opts" :key="index" :value="item.value" :label="item.label">{{ item.label }}</el-option>
    </el-select>
  </el-popover>
</template>

<script>
import clickoutside from 'element-ui/src/utils/clickoutside';

export default {
  name: 'SuffixCascader',
  directives: { clickoutside },
  props: {
    width: {
      type: Number,
      default: 400
    },
    options: {
      type: Array,
      default() {
        return []
      }
    },
    cascaderProps: {
      type: Object,
      default() {
        return {}
      }
    },
    placeholder: {
      type: String,
      default() {
        return this.$t('text.select')
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showPopover: false,
      cascaderVals: [],
      lastCascaderVal: [],
      selectVals: [],
      cascaderKey: 1,
      popoverKey: 0,
      popperElm: undefined,
      popperId: undefined
    }
  },
  computed: {
    cProps() {
      return Object.assign({ multiple: true, expandTrigger: 'hover', checkStrictly: false }, this.cascaderProps)
    },
    leafOnly() {
      return !this.cProps.checkStrictly;
    },
    opts() {
      const result = []
      function traverse(items) {
        items.forEach(item => {
          // 添加当前项
          result.push({ label: item.label, value: item.value });

          // 如果有子项，递归遍历
          if (item.children && item.children.length > 0) {
            traverse(item.children);
          }
        });
      }

      traverse(this.options);
      return result;
    }
  },
  watch: {
    'cascaderVals'(val) {
      this.allSuffixAble(val)
    }
  },
  methods: {
    cascaderRef() {
      return this.$refs['elCascaderPanel']
    },
    allSuffixAble(vals) {
      const opt = this.options.find(item => item.value === '*.*')
      opt && this.$set(opt, 'disabled', vals.some(item => item[0] === '*.*'))
    },
    initOpt(opts) {
      opts = Array.isArray(opts) ? opts : (opts || '').split('|')
      this.showPopover = false
      this.popoverKey = 'suffix_select_' + Date.now()
      this.selectVals.splice(0)
      if (!opts || opts.length === 0) {
        this.cascaderChange()
      } else {
        const tempCascader = []
        for (const opt of opts) {
          const node = this.$refs['elCascaderPanel'].getFlattedNodes(true).find(item => item.value == opt)
          node && tempCascader.push(node.getPath())
        }
        this.cascaderChange(tempCascader)
      }
    },
    removeTag(tagName) {
      this.showPopover = false
      const cascaderVals = [...this.cascaderVals]
      for (let i = cascaderVals.length - 1; i >= 0; i--) {
        if (cascaderVals[i].includes(tagName)) {
          cascaderVals.splice(i, 1)
        }
      }
      this.$nextTick(() => {
        this.cascaderChange(cascaderVals)
        this.cascaderKey++
      })
    },
    cascaderChange(val) {
      const cascaderVal = val || []
      const selectVals = []
      // 新勾选的选项 包含 *.* 所有
      const onceIncludeAllSuffix = this.selectVals.includes('*.*')
      const currIncludeAllSuffix = cascaderVal.some(item => item[0] === '*.*')
      if (cascaderVal.length === 0 || (!onceIncludeAllSuffix && currIncludeAllSuffix)) {
        // 勾选所有后缀后缀 取消勾选其他后缀
        cascaderVal.splice(0, cascaderVal.length, ['*.*'])
        selectVals.splice(0, selectVals.length, '*.*')
      } else if (onceIncludeAllSuffix && currIncludeAllSuffix) {
        const index = cascaderVal.findIndex(item => item[0] === '*.*')
        cascaderVal.splice(index, 1)
      }
      // 仅处理两级关系
      let firstLevelNum = this.options.length - 1
      for (let i = 0; i < cascaderVal.length; i++) {
        const tempOpt = this.options.find(item => cascaderVal[i][0] == item.value)
        if (tempOpt && tempOpt.children) {
          let j = i
          const size = tempOpt.children.length
          for (; j < Math.min(i + size, cascaderVal.length); j++) {
            if (cascaderVal[j][0] != tempOpt.value) { break }
          }
          if (j === i + size) {
            selectVals.push(tempOpt.value)
            firstLevelNum--
          } else {
            for (let k = i; k < j; k++) {
              selectVals.push(cascaderVal[k][1])
            }
          }
          i = j - 1
        }
      }
      if (firstLevelNum <= 0) {
        cascaderVal.splice(0, cascaderVal.length, ['*.*'])
        selectVals.splice(0, selectVals.length, '*.*')
      }
      this.$nextTick(() => {
        this.cascaderVals.splice(0, this.cascaderVals.length, ...cascaderVal)
        this.selectVals.splice(0, this.selectVals.length, ...selectVals)
        this.$emit('change', this.cascaderVals.map(vals => vals[vals.length - 1]))
      })
    },
    selectClick(status) {
      this.showPopover = !this.showPopover
      // handleClose 是为了实现展示和隐藏popover时，不让右侧箭头有效果
      this.$refs['selectRef'] && this.$refs['selectRef'].handleClose()
      const popoverId = (this.$refs.popover || {}).tooltipId
      if (this.showPopover && this.popperId != popoverId) {
        // 【必须】v-clickoutslide 内部使用包含元素
        this.popperId = popoverId
        this.popperElm = document.querySelector(`#${popoverId}`); // 即当前popover点击弹出弹窗元素
      }
      this.showPopover && this.cascaderKey++
    },
    closePopover() {
      this.$refs.popover.doClose()
    }
  }
}
</script>

<style lang="scss" scoped>
  .hidden {
    display: none;
  }
</style>
