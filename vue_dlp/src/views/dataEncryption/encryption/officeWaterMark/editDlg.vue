<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="dialogTitle"
      :stg-code="stgTypeNumber"
      :active-able="treeable"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStg"
      :update="updateStg"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <el-tabs v-model="activeTab" style="border: 1px solid #aaa !important; height: 320px" @tab-click="changeTab">
          <el-tab-pane :label="$t('pages.termSecurityBaseSettings')" name="base" style="padding: 0">
            <el-divider content-position="left">{{ $t('pages.effectiveWatermark') }}</el-divider>
            <el-card style="width: 98%; margin: auto">
              <template>
                <el-button size="small" :disabled="!formable" @click="addBaseOpts">{{ $t('button.add') }}</el-button>
                <el-button size="small" :disabled="!baseOptsUpdateable || !formable" @click="updateBaseOpts">{{ $t('button.edit') }}</el-button>
                <el-button size="small" :disabled="!baseOptsDeleteable || !formable" @click="deleteBaseOpts">{{ $t('button.delete') }}</el-button>
              </template>
              <grid-table
                ref="baseOptsTable"
                :show-pager="false"
                :height="190"
                row-key="rowKey"
                :col-model="eventColModel"
                :row-datas="temp.baseOpts"
                @selectionChangeEnd="baseOptsSelectionChange"
              />
            </el-card>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.manuallySetWatermark')" name="manual">
            <el-divider content-position="left" class="base-divider">
              <el-checkbox v-model="temp.manualAddOpts.active" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('pages.allowManualAddWatermark') }}</el-checkbox>
            </el-divider>
            <data-editor
              ref="manualAddDataEditor"
              :formable="!!temp.manualAddOpts.active && formable"
              :updateable="updateableForManualAdd"
              :deletable="deleteableForManualAdd"
              :popover-width="580"
              :before-add="manualAddBeforeAdd"
              :before-update="manualAddBeforeUpdate"
              :add-func="manualAddFormFunc"
              :update-func="manualAddFormFunc"
              :delete-func="deleteForManualAddFunc"
            >
              <Form ref="manualAddForm" :model="simpleTemp" label-position="right" label-width="90px">
                <FormItem :label="$t('table.waterMark')" prop="watermark" :rules="{ validator: validateManualAddWatermark, trigger: 'change' }">
                  <watermark-select
                    :key="eventManualWatermarkKey"
                    v-model="simpleTemp.watermarkKeys"
                    :tree-data="treeData"
                    :multiple="manualAddStatus === 'create'"
                    :popover-closeable="!previewDlgVisiable"
                    :handle-view="handleView"
                    @change="(val) => simpleTemp.watermark = val"
                  />
                </FormItem>
                <FormItem :label="$t('table.effectiveSuffix')">
                  <suffix-cascader ref="manualAddSuffixCascader" :options="effectFileSuffixOpts" @change="(val) => simpleTemp.fileExt = val"/>
                </FormItem>
              </Form>
            </data-editor>
            <grid-table
              ref="manualAddTable"
              row-key="libId"
              :selectable="() => !!temp.manualAddOpts.active"
              :auto-height="true"
              :max-height="150"
              :row-datas="temp.manualAddOpts.templates"
              :col-model="simpleTemplateModel"
              :show-pager="false"
              @selectionChangeEnd="manualAddTableSelectChange"
            />
            <el-divider content-position="left" class="base-divider">
              <el-checkbox v-model="temp.manualRemoveOpts.active" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('pages.allowManualRemoveWatermark') }}</el-checkbox>
            </el-divider>

            <el-radio-group v-model="temp.manualRemoveOpts.type" :disabled="!temp.manualRemoveOpts.active || !formable" style="margin-left: 8px" @input="changeRemoveOptType">
              <el-radio :label="1">{{ $t('pages.approvalRemoveWatermark') }}</el-radio>
              <el-radio :label="2">{{ $t('pages.manualRemoveWatermark') }}</el-radio>
            </el-radio-group>
            <div v-show="temp.manualRemoveOpts.type == 1">
              <div style="margin: 4px 8px; font-weight: bold">{{ $t('pages.approvalRemoveWatermarkTip1') }}</div>
              <event-select
                v-model="temp.manualRemoveOpts.approvalEvent"
                :show="activeTab === 'manual' && temp.manualRemoveOpts.type == 1"
                :need-divider="false"
                :disabled="!temp.manualRemoveOpts.active || !formable"
                :label-width="'0'"
                :options="removeEventOpts"
                :all-opt="{ label: $t('pages.allAddWatermarkEvent'), value: 0 }"
              />
            </div>
            <div v-show="temp.manualRemoveOpts.type == 2">
              <FormItem :label="$t('pages.terminalSelectWatermark')" label-width="146px">
                <suffix-cascader ref="manualRemoveSuffixCascader" :disabled="!temp.manualRemoveOpts.active || !formable" :options="effectFileSuffixOpts.filter(opt => opt.value !== 'image_suffix')" @change="(val) => temp.manualRemoveOpts.fileExt = val"/>
              </FormItem>
              <FormItem :label="$t('pages.removeWatermarkRetentionTime')" label-width="146px">
                <el-radio-group v-model="maintainTimeType" :disabled="!temp.manualRemoveOpts.active || !formable">
                  <el-radio :label="1">{{ $t('pages.onceEffective') }}</el-radio>
                  <el-radio :label="2">{{ $t('pages.permanentEffective') }}</el-radio>
                  <el-radio :label="3">
                    <i18n path="pages.withinEffective">
                      <el-input slot="info" v-model="temp.manualRemoveOpts.quietTime" :disabled="maintainTimeType != 3" style="width: 142px" @blur="blurQuietTime">
                        <el-select slot="append" v-model="temp.manualRemoveOpts.unit" :disabled="maintainTimeType != 3" class="suffix-select" @change="changeUnit">
                          <el-option v-for="(timeObj, timeIndex) in timeDict" :key="timeIndex" :label="timeObj.label" :value="timeObj.value">{{ timeObj.label }}</el-option>
                        </el-select>
                      </el-input>
                    </i18n>
                  </el-radio>
                </el-radio-group>
              </FormItem>
              <span style="margin-left: 2px; line-height: 30px">
                <span v-show="maintainTimeType == 1" v-html="$t('pages.removeWatermarkTimeDesc1')"></span>
                <span v-show="maintainTimeType == 2" v-html="$t('pages.removeWatermarkTimeDesc2')"></span>
                <span v-show="maintainTimeType == 3" v-html="$t('pages.removeWatermarkTimeDesc', { time: ` ${temp.manualRemoveOpts.quietTime}${getDictLabel(timeDict, temp.manualRemoveOpts.unit)} ` })"></span>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <div slot="content">{{ $t('pages.removeWatermarkTip') }}</div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
              <event-select
                v-model="temp.manualRemoveOpts.event"
                :show="activeTab === 'manual' && temp.manualRemoveOpts.type == 2 && maintainTimeType >= 2"
                :need-divider="false"
                :disabled="!temp.manualRemoveOpts.active || !formable"
                :label-width="'0'"
                :options="removeEventOpts"
                :all-opt="{ label: $t('pages.allAddWatermarkEvent'), value: 0 }"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>
      <template slot="button">
        <link-button
          btn-type="primary"
          btn-style="float: left"
          :formable="formable"
          :menu-code="'A5A'"
          :link-url="{ path: '/system/baseData/waterMarkLib', query: { tabName: 'officeTab' } }"
          :btn-text="$t('pages.maintainWaterMarkLib')"
        />
      </template>
    </stg-dialog>
    <preview-dlg ref="previewDef" append-to-body @closed="closed"/>

    <!-- 生效水印弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="eventFormVis"
      width="700px"
      :title="i18nConcatText($t('pages.effectiveWatermark'), eventFormStatus)"
      @close="eventFormVis = false"
    >
      <Form ref="eventFormRef" :model="eventForm" :rules="eventFormRules" label-width="120px" lable-position="right">
        <FormItem :label="$t('table.eventType')" prop="eventWay">
          <el-select v-if="eventFormVis" v-model="eventForm.eventWay" :multiple="isCreateEventForm" :clearable="isCreateEventForm" @change="changeEventWay">
            <el-option v-if="isCreateEventForm" :label="$t('pages.allAddWatermarkEvent')" :value="'*.*'"></el-option>
            <el-option
              v-for="(item, index) in addWatermarkOpts"
              :key="index"
              :label="item.label"
              :value="item.value"
            >{{ item.label }}</el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.waterMark')" prop="watermark">
          <watermark-select
            :key="eventWatermarkKey"
            v-model="eventForm.watermarkKeys"
            :tree-data="treeData"
            :width="510"
            :popover-closeable="!previewDlgVisiable"
            :handle-view="handleView"
            @change="eventWatermarkChange"
          />
        </FormItem>
        <FormItem :label="$t('table.effectiveSuffix')">
          <suffix-cascader ref="suffixCascader" :options="formEffectFileSuffixOpts" @change="formEffectFileSuffixChange"/>
        </FormItem>
        <FormItem v-show="formEffectFileSuffixTip">
          <p style="color: blue; margin: 0; line-height: 1.6">{{ `${$t('text.prompt')}: ${formEffectFileSuffixTip}` }}</p>
        </FormItem>
        <div v-if="eventForm.computeMarkWay > 0" class="extend-config">
          <!--下载文件添加水印-->
          <event-select
            v-model="eventForm['extConfigs'][1]['processes']"
            :show="(eventForm.computeMarkWay & 1) > 0"
            :need-divider="isCreateEventForm"
            show-val
            :title="$t('pages.markWayOptions1')"
            :label="$t('pages.browserProcess')"
            :options="browserOptions"
            :all-opt="{ label: $t('pages.allBrowserProcess'), value: '*.*'}"
          />
          <!--解密文件添加水印-->
          <div v-show="(eventForm.computeMarkWay & 4) > 0 && isCreateEventForm">
            <el-divider content-position="left">{{ $t('pages.markWayOptions3') }}</el-divider>
            <FormItem :label="$t('text.insertInfo', { info: $t('pages.extraWatermark') })">
              <watermark-select
                :key="eventDecryptWatermarkKey"
                v-model="decryptExtWatermarkKeys"
                :tree-data="treeData"
                multiple
                :filter-key="decryptFilterKey"
                :popover-closeable="!previewDlgVisiable"
                :handle-view="handleView"
                @change="val => eventForm.extWatermarks[4] = val"
              />
            </FormItem>
          </div>
          <!--IM外发添加水印-->
          <event-select
            v-model="eventForm['extConfigs'][64]['processes']"
            :show="(eventForm.computeMarkWay & 64) > 0"
            :need-divider="isCreateEventForm"
            :title="$t('pages.officeMarkWay64', { desc: $t('pages.addWatermarkDesc1') })"
            :label="$t('pages.controlImTool')"
            :options="imOptions"
            :all-opt="{ label: $t('pages.allImTool'), value: '*.*' }"
          />
          <!--网盘上传添加水印-->
          <event-select
            v-model="eventForm['extConfigs'][512]['processes']"
            :show="(eventForm.computeMarkWay & 512) > 0"
            :need-divider="isCreateEventForm"
            :title="$t('pages.officeMarkWay512', { desc: $t('pages.addWatermarkDesc1') })"
            :label="$t('pages.controlNetDisk')"
            :options="netDiskOptions"
            :all-opt="{ label: $t('pages.allNetDisk'), value: '*.*' }"
          />
          <!--网页上传文件添加水印-->
          <div v-if="(eventForm.computeMarkWay & 256) > 0">
            <el-divider content-position="left">
              {{ $t('pages.officeMarkWay256', { desc: $t('pages.addWatermarkDesc1') }) }}
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.webUploadAddWatermarkTip') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-divider>
            <FormItem :label="$t('pages.controlUrl')">
              <el-radio-group v-model="eventForm['extConfigs'][256]['limitType']">
                <el-radio :label="1">{{ $t('pages.controlUrlOpt1') }}</el-radio>
                <el-radio :label="0">{{ $t('pages.controlUrlOpt2') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <url-import-combination
              v-model="eventForm['extConfigs'][256]['controlIds']"
              style="width: 630px; margin-left: 24px; padding-bottom: 10px;"
            />
          </div>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleEventForm">
          {{ $t('button.confirm2') }}
        </el-button>
        <el-button @click="eventFormVis = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createStrategy, updateStrategy, getStrategyByName
} from '@/api/behaviorManage/hardware/waterMark'
import {
  browserOptions,
  imOptions,
  effectFileSuffixOpts,
  netDiskOptions,
  getAddWatermarkOpts
} from '@/utils/officeWaterMark'
import PreviewDlg from '@/views/system/baseData/waterMarkLib/previewDlg'
import { getWaterMarkTree } from '@/api/dataEncryption/encryption/fileOutgoing'
import EventSelect from '@/views/dataEncryption/encryption/officeWaterMark/eventSelect'
import WatermarkSelect from '@/views/dataEncryption/encryption/officeWaterMark/watermarkSelect'
import UrlImportCombination from '@/views/system/baseData/groupImportList/urlImportCombination'
import SuffixCascader from '@/views/dataEncryption/encryption/officeWaterMark/suffixCascader'
import { getDictLabel } from '@/utils/dictionary'
import { findNodeLabel } from '@/utils/tree'

export default {
  name: 'OfficeWaterMarkStgDlg',
  components: {
    SuffixCascader, UrlImportCombination, WatermarkSelect, EventSelect, PreviewDlg },
  props: {
    // 只显示详情弹窗，策略总览那边查看详情调用
    formable: { type: Boolean, default: true }, // 能否提交表单
    treeable: { type: Boolean, default: true }, // 是否显示对象树
    stgTypeNumber: { type: Number, default: 151 }, // 策略类型编号 2打印水印策略 44屏幕水印策略
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      browserOptions,
      imOptions,
      netDiskOptions,
      effectFileSuffixOpts,
      activeTab: 'base',
      query: { // 查询条件
        page: 1,
        stgTypeNumber: this.stgTypeNumber,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      addWatermarkOpts: [],
      temp: {
        manualAddOpts: {},
        manualRemoveOpts: {}
      },
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        entityType: '',
        entityId: undefined,
        libIds: [],
        baseOpts: [],
        manualAddOpts: {
          active: 0,
          templates: []
        },
        manualRemoveOpts: {
          type: 1,
          active: 0,
          fileExt: [],
          unit: 1,
          quietTime: 5,
          event: [0],
          approvalEvent: [0],
          quietEvent: 0
        }
      },
      preUnit: 1,
      defaultWatermarkParams: {
        markWay: 0,
        name: '',
        fileExt: '*.*',
        processes: '',
        limitType: 0,
        controlIds: ''
      },
      simpleTemp: {},
      defaultSimpleTemp: {
        watermarkKeys: [],
        fileExt: []
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      slotName: undefined,
      dialogTitle: undefined,
      waterMarkType: 1,
      maintainTimeType: 1,
      validError: 0,
      treeData: [],
      decryptFilterKey: [],
      rules: {
        name: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }]
      },
      manualAddStatus: 'create',
      manualAddForUpdateKey: undefined,
      watermarkTreeLoading: false,
      previewDlgVisiable: false,
      eventFormVis: false,
      eventFormStatus: 'create',
      baseOptsUpdateable: false,
      baseOptsDeleteable: false,
      updateableForManualAdd: false,
      deleteableForManualAdd: false,
      eventForm: {},
      defaultForm: {
        eventWay: [],
        computeMarkWay: 0,
        watermarkKeys: [],
        watermark: [],
        fileExt: undefined,
        extConfigs: {
          1: { processes: ['*.*'], splitChar: '|', splitAttribute: 'processes' },
          64: { processes: ['*.*'], splitChar: '|', splitAttribute: 'processes' },
          256: { limitType: 1, controlIds: [], splitChar: ',', splitAttribute: 'controlIds' },
          512: { processes: ['*.*'], splitChar: '|', splitAttribute: 'processes' }
        },
        extWatermarks: {
          4: []
        }
      },
      eventFormRules: {
        eventWay: [{ required: true, message: this.$t('pages.requiredSelectInfo', { info: this.$t('pages.addWatermarkEvents') }), trigger: 'change' }],
        watermark: [{ required: true, message: this.$t('pages.pleaseSelectContent', { content: this.$t('table.waterMark') }), trigger: 'change' }]
      },
      simpleTemplateModel: [
        { prop: 'name', label: 'templateName', width: '100', sort: true, formatter: this.formatLibName },
        { prop: 'fileExt', label: 'effectiveSuffix', width: '120', formatter: this.fileExtFormatter }
      ],
      eventColModel: [
        { prop: 'name', label: 'templateName', width: '100', sort: true, formatter: this.formatLibName },
        { prop: 'markWay', label: 'eventType', width: '100', sort: true, sortOriginal: true, formatter: this.markWayFormatter },
        { prop: 'fileExt', label: 'effectiveSuffix', width: '120', formatter: this.fileExtFormatter },
        { prop: '', label: this.$t('pages.extraInfo'), width: '150', formatter: this.extendInfoFormatter }
      ],
      limitMarkway: 1 + 2 + 16 + 32 + 64 + 128 + 256 + 512,
      removeEventOpts: [],
      timeDict: [
        { label: this.$t('text.minute'), value: 1 },
        { label: this.$t('text.hour1'), value: 60 },
        { label: this.$t('pages.day'), value: 24 * 60 }
      ],
      decryptExtWatermarkKeys: [],
      eventWatermarkKey: Date.now(),
      eventDecryptWatermarkKey: Date.now(),
      eventManualWatermarkKey: Date.now(),
      checkAll: false,
      maxInt32: Math.pow(2, 31) - 1,
      isFirstOpenManual: true   // 解决更新时，如果允许取消水印为手动取消水印时，文档后缀未初始化的问题
    }
  },
  computed: {
    formEffectFileSuffixOpts() {
      // 获取表单可选的事件
      if (this.isCreateEventForm) { return this.effectFileSuffixOpts }
      const obj = this.addWatermarkOpts.find(opt => opt.value == this.eventForm.eventWay)
      if (!obj || (obj.unSupExt || []).length === 0) { return this.effectFileSuffixOpts }
      const opts = JSON.parse(JSON.stringify(this.effectFileSuffixOpts))
      obj.unSupExt.forEach(ext => {
        if (ext.endsWith('_suffix')) {
          const i = opts.findIndex(p => p.value === ext)
          i >= 0 && opts.splice(i, 1)
        } else {
          for (const p of opts) {
            const suffixes = p.suffixes || []
            const i = suffixes.indexOf(ext)
            if (i >= 0) {
              suffixes.splice(i)
              break
            }
          }
        }
      })
      return opts
    },
    formEffectFileSuffixTip() {
      // 当表单为修改状态时，仅能单选事件(typeOf eventWay == number)，因此此时的生效后缀可经过计算，永远可以不出提示
      if (!this.isCreateEventForm) { return '' }
      const eventWay = this.getCreateFormEventWay(this.eventForm.eventWay)
      if (eventWay.length === 0) { return '' }
      const tips = []
      const imageSuffixes = (this.effectFileSuffixOpts.find(opt => opt.value === 'image_suffix') || []).suffixes || []
      const ofdSuffixes = (this.effectFileSuffixOpts.find(opt => opt.value === 'ofd_suffix') || []).suffixes || []
      const fileExt = this.formatRealFileExt(this.eventForm.fileExt)
      const includeImageSuffixes = fileExt.some(ext => imageSuffixes.includes(ext))
      const includeOfdSuffixes = fileExt.some(ext => ofdSuffixes.includes(ext))
      if (eventWay.includes(2) && eventWay.length === 1 && (includeImageSuffixes || includeOfdSuffixes)) {
        return `${this.$t('pages.docWatermarkFormTip3')}; ${this.$t('pages.officeWatermarkSuffixTipDesc')}`
      }
      if (includeImageSuffixes && (!eventWay.includes(4) || eventWay.length > 1)) {
        tips.push(this.$t('pages.docWatermarkFormTip1'))
      }
      if (eventWay.includes(2) && includeOfdSuffixes) {
        tips.push(this.$t('pages.docWatermarkFormTip2'))
      }
      return tips.length > 0 ? `${tips.join('; ')}; ${this.$t('pages.officeWatermarkSuffixTipDesc')}` : ''
    },
    isCreateEventForm() {
      return this.eventFormStatus === 'create'
    }
  },
  watch: {
    stgTypeNumber(val) {
      this.loadWaterMarkTree()
    },
    'eventForm.eventWay'(val) {
      if (Array.isArray(val)) {
        this.eventForm.computeMarkWay = this.getCreateFormEventWay(val).reduce((pre, cur) => pre | cur, 0)
      } else {
        this.eventForm.computeMarkWay = val || 0
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    this.initDataByStgTypeNumber()
    this.loadWaterMarkTree()
    this.$store.dispatch('commonData/setSaleModuleIds')
  },
  activated() {
    const func = this.loadWaterMarkTree();
    func && func.then(res => {
      if (this.eventFormVis) {
        this.eventWatermarkKey++;
        if ((this.eventForm.computeMarkWay & 4) > 0 && this.isCreateEventForm) {
          this.eventDecryptWatermarkKey++
        }
      }
      if (this.$refs['manualAddDataEditor'] && this.$refs['manualAddDataEditor'].formVisiable) {
        this.eventManualWatermarkKey++
      }
    })
  },
  methods: {
    getDictLabel,
    createStrategy,
    updateStrategy,
    getStrategyByName,
    getSortedAddWatermarkOpts() {
      return getAddWatermarkOpts().filter(opt => {
        const moduleIds = [...this.$store.getters.saleModuleIds]
        // 如果没有对应的销售模块，则禁用
        return !opt.moduleId || moduleIds.includes(opt.moduleId)
      })
    },
    createStg(data) {
      if (!this.includeImageFileExt(data[0].baseOpts, data[0].tempMarkWay)) {
        return createStrategy(data)
      }
      return new Promise((resolve, reject) => {
        this.$confirmBox(this.$t('pages.documentWatermarkConfirmTip1'), this.$t('text.prompt')).then(() => {
          createStrategy(data).then(res => resolve(res)).catch(reason => reject(reason))
        }).catch(() => { reject() })
      })
    },
    updateStg(data) {
      if (!this.includeImageFileExt(data[0].baseOpts, data[0].tempMarkWay)) {
        return updateStrategy(data)
      }
      return new Promise((resolve, reject) => {
        this.$confirmBox(this.$t('pages.documentWatermarkConfirmTip1'), this.$t('text.prompt')).then(() => {
          updateStrategy(data).then(res => resolve(res)).catch(reason => reject(reason))
        }).catch(() => { reject() })
      })
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.$nextTick(() => {
        if (this.$refs.waterMarkTree) {
          this.$refs.waterMarkTree.checkSelectedNodes(this.temp.libIds)
        }
      })
    },
    initDataByStgTypeNumber() {
      if (this.stgTypeNumber === 2) {
        this.dialogTitle = this.$t('pages.waterMarkConfig1')
        this.waterMarkType = 2
      } else if (this.stgTypeNumber === 44) {
        this.dialogTitle = this.$t('pages.screenWaterMark')
        this.waterMarkType = 1
      } else {
        this.dialogTitle = this.$t('pages.OfficeWaterMarkStg')
        this.waterMarkType = 3
      }
    },
    waterMarkValidator(rule, value, callback) {
      if (this.temp.libIds == null || this.temp.libIds.length === 0) {
        callback(new Error(this.$t('pages.waterMark_Msg44')))
        this.validError = 1
      } else {
        callback()
        this.validError = 0
      }
    },
    extendInfoFormatter(row, data) {
      if (row.markWay === 1) {
        return this.contentFormatter(row['processes'], '|', this.browserOptions, { label: this.$t('pages.allBrowserProcess'), value: '*.*' }, this.$t('pages.browserProcess'))
      } else if (row.markWay === 64) {
        return this.contentFormatter(row['processes'], '|', this.imOptions, { label: this.$t('pages.allImTool'), value: '*.*' }, this.$t('pages.controlImTool'))
      } else if (row.markWay === 512) {
        return this.contentFormatter(row['processes'], '|', this.netDiskOptions, { label: this.$t('pages.allNetDisk'), value: '*.*' }, this.$t('pages.controlNetDisk'))
      } else if (row.markWay == 256) {
        return row.limitType === 1 ? this.$t('pages.controlUrlOpt1') : this.$t('pages.controlUrlOpt2')
      }
      return ''
    },
    contentFormatter(data, splitChar, options, allOpt, describe) {
      const contents = Array.isArray(data) ? data : data.split(splitChar)
      let info
      if (contents[0] === allOpt.value) {
        info = allOpt.label
      } else {
        info = options.filter(item => contents.includes(item.value)).map(item => item.label).join('，')
      }
      return `${describe}：${info}`
    },
    markWayFormatter(row, data) {
      return (this.addWatermarkOpts.find(opt => (opt.value & data) > 0) || {})['label'] || ''
    },
    fileExtFormatter(row, data) {
      if (!data) { return '' }
      const tempData = Array.isArray(data) ? data : data.split('|')
      if (tempData[0] === this.effectFileSuffixOpts[0].value) {
        return this.effectFileSuffixOpts[0].label
      }
      const names = []
      const filterSets = []
      for (let i = 1; i < this.effectFileSuffixOpts.length; i++) {
        filterSets.push(new Set(this.effectFileSuffixOpts[i].suffixes))
      }

      tempData.forEach(data => {
        for (let i = 0; i < filterSets.length; i++) {
          if (filterSets[i].delete(data)) { break }
        }
      })
      for (let i = 0; i < filterSets.length; i++) {
        const suffixOpt = this.effectFileSuffixOpts[i + 1]
        if (filterSets[i].size === 0) {
          names.push(suffixOpt.label)
          continue
        }
        suffixOpt.suffixes.filter(suffix => !filterSets[i].has(suffix)).forEach(suffix => names.push(suffix))
      }
      return names.join(names.length === 1 ? '' : ', ')
    },
    treeNodeClick: function(checkedNodeIds, data) {
      const wayMap = {}
      data = data || []
      data.forEach(item => {
        if (item.type === 'G') {
          checkedNodeIds.splice(checkedNodeIds.indexOf(item.id), 1)
          return
        }
        if (item.oriData) {
          if (!wayMap.hasOwnProperty(item.oriData.markWay)) {
            wayMap[item.oriData.markWay] = [item]
          } else {
            wayMap[item.oriData.markWay].push(item)
          }
        }
      })
      // 当markWay值为1,2时 一个终端一条策略；当markWay值为 4时，一个终端允许多条策略
      // 到此说明新加的数据导致冲突，不允许添加
      let isConflict = false
      const onlyOneStgMarkWays = [1, 2]
      onlyOneStgMarkWays.forEach(i => {
        if (wayMap[i] && wayMap[i].length > 1) {
          for (let j = 0; j < wayMap[i].length; j++) {
            const item = wayMap[i][j]
            if (this.temp.libIds.findIndex(id => id == item.id) < 0) {
              checkedNodeIds.splice(checkedNodeIds.indexOf(item.id), 1)
              isConflict = true
            }
          }
        }
      })
      if (isConflict) {
        this.$message({
          message: this.$t('pages.officeWaterMark_text2'),
          type: 'error'
        })
      }
      if (checkedNodeIds.length !== data.length) {
        this.$refs.waterMarkTree.clearSelectedNodes()
        this.$refs.waterMarkTree.checkSelectedNodes(checkedNodeIds)
      }
      this.temp.libIds.splice(0, this.temp.libIds.length, ...checkedNodeIds)
    },
    handleView(data) {
      this.previewDlgVisiable = true
      this.$refs['previewDef'].handleUpdate(data.oriData, this.stgTypeNumber)
    },
    loadWaterMarkTree: function() {
      if (this.watermarkTreeLoading) { return undefined }
      this.watermarkTreeLoading = true
      return getWaterMarkTree({ type: this.waterMarkType }).then(respond => {
        /* if (!this.hasPermission('E22')) {
          respond.data.forEach(node => {
            if (node.children) {
              node.children = node.children.filter(childNode => {
                return childNode.oriData.markWay != 4
              })
            }
          })
        } */
        this.treeData.splice(0, this.treeData.length, ...respond.data)
        this.watermarkTreeLoading = false
      }).catch(e => {
        this.watermarkTreeLoading = false
      })
    },
    //  同步加载水印模板数据
    asyncLoadWaterMarkTree() {
      return getWaterMarkTree({ type: this.waterMarkType }).then(respond => {
        this.treeData = respond.data
      })
    },
    handleCreate() {
      this.resetForm()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$refs.waterMarkTree && this.$refs.waterMarkTree.clearFilter()
      this.$nextTick(() => {
      })
    },
    async handleUpdate(row) {
      //  用于策略总览查看详情
      if (!this.formable) {
        this.initDataByStgTypeNumber()
        await this.asyncLoadWaterMarkTree()
      }
      this.resetForm(row)
      Array.from({ length: (row.baseOpts || []).length }, (_, index) => {
        row.baseOpts[index].rowKey = index
      })
      this.$refs['stgDlg'].show(row, this.formable)
      // 处理去除水印的时间单位
      this.changeUnit(this.temp.manualRemoveOpts.unit)
      this.$refs.waterMarkTree && this.$refs.waterMarkTree.clearFilter()
      this.$nextTick(() => {
      })
    },
    resetForm(row) {
      this.checkAll = false
      this.activeTab = 'base'
      this.baseOptsUpdateable = false
      this.baseOptsDeleteable = false
      this.isFirstOpenManual = !!row // 解决更新时，如果允许取消水印为手动取消水印时，文档后缀未初始化的问题
      this.maintainTimeType = 1
      this.simpleTemp = JSON.parse(JSON.stringify(this.defaultSimpleTemp))
      this.preUnit = 1
      this.addWatermarkOpts = this.getSortedAddWatermarkOpts().filter(opt => opt.value !== 8)
      this.initEventParams()
    },
    handleImport() {},
    handleExport() {},
    formatLibName(row, data) {
      return findNodeLabel(this.treeData, row['libId'], 'id')
    },
    formatRowData(rowData) {
      const existManualAdd = rowData.baseOpts.some(baseOpt => baseOpt.markWay == 8)
      if (existManualAdd) {
        const tempBaseOpts = []
        const manualAddTemplates = []
        rowData.baseOpts.forEach(baseOpt => {
          if (baseOpt.markWay == 8) {
            manualAddTemplates.push(baseOpt)
          } else {
            tempBaseOpts.push(baseOpt)
          }
        })
        rowData.baseOpts = [...tempBaseOpts]
        rowData.manualAddOpts.active = (rowData.tempMarkWay & 8) === 0 ? 1 : 0
        rowData.manualAddOpts.templates = [...manualAddTemplates]
      }
      if (!rowData.manualRemoveOpts || Object.keys(rowData.manualRemoveOpts).length == 0) {
        rowData.manualRemoveOpts = JSON.parse(JSON.stringify(this.defaultTemp.manualRemoveOpts))
      } else {
        const removeEvent = this.binaryToList(rowData.manualRemoveOpts.quietEvent)
        if (removeEvent.length == 0) { removeEvent.push(0) }
        if (rowData.manualRemoveOpts.type == 1) {
          rowData.manualRemoveOpts.approvalEvent = [...removeEvent]
          delete rowData.manualRemoveOpts.fileExt
          delete rowData.manualRemoveOpts.quietTime
        } else {
          rowData.manualRemoveOpts.event = [...removeEvent]
          if (rowData.manualRemoveOpts.quietTime == -1) {
            this.maintainTimeType = 2
          } else if (rowData.manualRemoveOpts.quietTime == 0) {
            this.maintainTimeType = 1
          } else {
            this.maintainTimeType = 3
            rowData.manualRemoveOpts.quietTime /= rowData.manualRemoveOpts.unit
          }
          if (this.maintainTimeType <= 2) {
            delete rowData.manualRemoveOpts.quietTime
          }
        }
        Object.keys(this.defaultTemp.manualRemoveOpts).filter(key => !rowData.manualRemoveOpts.hasOwnProperty(key))
          .forEach(key => { rowData.manualRemoveOpts[key] = this.defaultTemp.manualRemoveOpts[key] })
      }
    },
    formatFormData(formData) {
      let tempMarkWay = 0
      formData.stgTypeNumber = this.stgTypeNumber
      formData.baseOpts = formData.baseOpts || []
      for (const extConfig of formData.baseOpts) {
        const attribute = extConfig.splitAttribute
        if (attribute && extConfig.splitChar && Array.isArray(extConfig[attribute])) {
          extConfig[attribute] = extConfig[attribute].join(extConfig.splitChar)
        }
        delete extConfig.splitAttribute
        delete extConfig.splitChar
      }
      formData.manualAddOpts.templates.forEach(template => {
        const fileExt = Array.isArray(template['fileExt']) ? template['fileExt'].join('|') : template['fileExt']
        formData.baseOpts.push(Object.assign({}, this.defaultWatermarkParams, {
          markWay: 8, libId: template['id'] || template['libId'], name: template['name'], fileExt
        }))
      })
      if (formData.manualAddOpts.active == 0) {
        tempMarkWay |= 8
      }
      formData.tempMarkWay = tempMarkWay
      const computeEventAttr = formData.manualRemoveOpts.type == 1 ? 'approvalEvent' : 'event'
      formData.manualRemoveOpts.quietEvent = formData.manualRemoveOpts[computeEventAttr].reduce((total, cur) => total | cur, 0)
      if (formData.manualRemoveOpts.type == 1) {
        const removeOpts = ['type', 'quietEvent', 'active'].reduce((p, c) => {
          p[c] = formData.manualRemoveOpts[c]
          return p
        }, {})
        // 按终端要求，需要补充全字段
        removeOpts.fileExt = ''
        removeOpts.quietTime = 0
        formData.manualRemoveOpts = removeOpts
      } else {
        if (Array.isArray(formData.manualRemoveOpts.fileExt)) {
          formData.manualRemoveOpts.fileExt = formData.manualRemoveOpts.fileExt.join('|')
        }
        if (this.maintainTimeType == 3) {
          formData.manualRemoveOpts.quietTime *= formData.manualRemoveOpts.unit
        } else {
          formData.manualRemoveOpts.quietTime = this.maintainTimeType == 1 ? 0 : -1
          formData.manualRemoveOpts.unit = 1
        }
      }
      delete formData.manualAddOpts
    },
    validateFormData() {
      if (this.temp.baseOpts.length === 0 && !this.temp.manualRemoveOpts.active && (!this.temp.manualAddOpts.active || this.temp.manualAddOpts.templates.length === 0)) {
        this.$message({
          message: this.$t('pages.selectOperateWatermarkAtLeast'),
          type: 'error'
        });
        return false
      }
      return true
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    addBaseOpts() {
      this.resetBaseOptParams()
      this.eventFormVis = true
      this.eventFormStatus = 'create'
      this.$nextTick(() => {
        this.$refs['eventFormRef'] && this.$refs['eventFormRef'].clearValidate()
        this.suffixCascaderInitOpt('suffixCascader')
        this.$refs['importTable'] && this.$refs['importTable'].loadTableData()
      })
    },
    updateBaseOpts() {
      if (this.$refs['baseOptsTable']) {
        const updateData = this.$refs['baseOptsTable'].getSelectedDatas()[0]
        if (updateData) {
          this.resetBaseOptParams()
          this.eventForm.eventWay = updateData.markWay
          this.eventForm.computeMarkWay |= updateData.markWay
          const extConfig = this.eventForm.extConfigs[updateData.markWay]
          if (extConfig) {
            const attribute = updateData[extConfig.splitAttribute]
            extConfig[extConfig.splitAttribute] = Array.isArray(attribute) ? attribute : attribute.split(extConfig.splitChar)
          }
          if (updateData.markWay === 256) {
            this.eventForm.extConfigs[256].limitType = updateData.limitType
          }
          this.eventFormVis = true
          this.eventFormStatus = 'update'
          this.$nextTick(() => {
            this.$refs['eventFormRef'] && this.$refs['eventFormRef'].clearValidate()
            this.eventForm.watermarkKeys.splice(0, this.eventForm.watermarkKeys.length, updateData.libId)
            this.suffixCascaderInitOpt('suffixCascader', updateData.fileExt.split('|'))
          })
        }
      }
    },
    deleteBaseOpts() {
      if (this.$refs['baseOptsTable']) {
        const selectedDatas = this.$refs['baseOptsTable'].getSelectedDatas()
        for (let i = this.temp.baseOpts.length - 1; i >= 0; i--) {
          const opt = this.temp.baseOpts[i]
          if (selectedDatas.some(data => data.rowKey == opt.rowKey)) {
            this.temp.baseOpts.splice(i, 1)
          }
        }
      }
    },
    resetBaseOptParams() {
      this.eventForm = JSON.parse(JSON.stringify(this.defaultForm))
      this.decryptExtWatermarkKeys.splice(0)
    },
    initEventParams() {
      // 初始化生效后缀
      this.effectFileSuffixOpts.forEach(opt => {
        if (opt.suffixes) {
          opt.children = opt.suffixes.map(suffix => { return { label: suffix, value: suffix } })
        }
      })
      this.removeEventOpts.splice(0, this.removeEventOpts.length, ...this.getSortedAddWatermarkOpts())
    },
    blurQuietTime(event) {
      let tempVal = event.target.value || ''
      tempVal = tempVal.replace(/[^\d]/g, '')
      const maxInteger = Math.floor(this.maxInt32 / this.temp.manualRemoveOpts.unit)
      if (Number(tempVal) > maxInteger) {
        tempVal = maxInteger
      }
      this.temp.manualRemoveOpts.quietTime = Number(tempVal) || 1
    },
    changeUnit(val) {
      if (this.temp.manualRemoveOpts.quietTime > 0) {
        const tempVal = Math.floor(this.temp.manualRemoveOpts.quietTime * this.preUnit / val)
        this.temp.manualRemoveOpts.quietTime = tempVal || 1
      }
      this.preUnit = val
    },
    manualAddBeforeAdd() {
      this.manualAddStatus = 'create'
      this.simpleTemp = JSON.parse(JSON.stringify(this.defaultSimpleTemp))
      this.manualAddForUpdateKey = null
      this.$nextTick(() => {
        this.$refs['manualAddForm'] && this.$refs['manualAddForm'].clearValidate()
        this.suffixCascaderInitOpt('manualAddSuffixCascader')
      })
    },
    manualAddBeforeUpdate() {
      this.manualAddStatus = 'update'
      this.simpleTemp = JSON.parse(JSON.stringify(this.defaultSimpleTemp))
      const selectDatas = this.$refs['manualAddTable'].getSelectedDatas()
      if (selectDatas && selectDatas[0]) {
        this.manualAddForUpdateKey = selectDatas[0].libId
        this.$nextTick(() => {
          this.simpleTemp.watermarkKeys = [selectDatas[0].libId]
          this.suffixCascaderInitOpt('manualAddSuffixCascader', selectDatas[0].fileExt)
        })
      }
    },
    validateManualAddWatermark(rule, value, callback) {
      // 检验针对修改，新增时对相同模板进行过滤
      const keys = this.simpleTemp.watermarkKeys || []
      if (keys.length === 0) {
        callback(new Error(this.$t('pages.pleaseSelectContent', { content: this.$t('table.waterMark') })))
      }
      const existData = this.temp.manualAddOpts.templates.find(template => keys.some(key => key == template.libId))
      if (this.manualAddStatus === 'update' && existData && existData.libId !== this.manualAddForUpdateKey) {
        // 判断是否存在相同
        callback(new Error(this.$t('valid.customizeSameName', { obj: this.$t('table.waterMark') })))
      }
      callback()
    },
    manualAddFormFunc() {
      let validate
      this.$refs['manualAddForm'].validate(valid => {
        if (valid) {
          const templates = this.temp.manualAddOpts.templates
          if (this.manualAddStatus === 'create') {
            let includeSameLibId = false;
            (this.simpleTemp.watermark || []).forEach(item => {
              const some = templates.some(t => t.libId == item.id)
              if (some) {
                includeSameLibId = true
                return
              }
              templates.push(
                { libId: item.id, fileExt: this.formatRealFileExt(this.simpleTemp.fileExt, this.effectFileSuffixOpts) }
              )
            })
            if (includeSameLibId) {
              this.$message({
                message: this.$t('valid.filterSameName', { obj: this.$t('table.waterMark') }),
                type: 'warning',
                duration: 2000
              })
            }
          } else if (this.manualAddStatus === 'update') {
            const watermark = (this.simpleTemp.watermark || [])[0] || {}
            const template = templates.find(item => item.libId == this.manualAddForUpdateKey)
            template && (Object.assign(template, { libId: watermark.id, fileExt: this.formatRealFileExt(this.simpleTemp.fileExt, this.effectFileSuffixOpts) }))
            // 去除选择项 防止select-change状态错误
            this.$refs['manualAddTable'].clearSelection()
          }
        }
        validate = valid
      })
      return validate
    },
    deleteForManualAddFunc() {
      const selectDatas = this.$refs['manualAddTable'].getSelectedDatas() || []
      const templates = this.temp.manualAddOpts.templates
      for (let i = templates.length - 1; i >= 0; i--) {
        if (selectDatas.some(data => data.libId == templates[i].libId)) {
          templates.splice(i, 1)
        }
      }
    },
    baseOptsSelectionChange(row) {
      this.baseOptsUpdateable = (row || []).length === 1
      this.baseOptsDeleteable = (row || []).length > 0
    },
    manualAddTableSelectChange(row) {
      this.updateableForManualAdd = (row || []).length === 1
      this.deleteableForManualAdd = (row || []).length > 0
    },
    handleEventForm() {
      this.$refs['eventFormRef'].validate(valid => {
        if (valid && this.eventFormValidate()) {
          const eventForm = this.eventForm
          const watermark = eventForm.watermark[0]
          const eventWay = this.isCreateEventForm ? this.getCreateFormEventWay(this.eventForm.eventWay) : [this.eventForm.eventWay]
          // 用长度作ID赋值至表格数据行对象
          let curMaxKey = (this.temp.baseOpts || []).map(opt => opt.rowKey).reduce((acc, cur) => Math.max(acc, cur), Number.MIN_SAFE_INTEGER)
          // 新增时存在过滤的事件类型
          const filterMarkWays = []
          for (const markWay of eventWay) {
            const commonDatas = Object.assign({}, this.defaultWatermarkParams, {
              markWay, libId: watermark['id'], name: watermark['label'], fileExt: this.filterFileExt(eventForm.fileExt, markWay).join('|'), rowKey: ++curMaxKey
            }, eventForm.extConfigs[markWay] || {})
            if (!this.isCreateEventForm) {
              const updateData = this.$refs['baseOptsTable'].getSelectedDatas()[0]
              delete commonDatas.rowKey
              Object.assign(updateData, commonDatas)
            } else {
              if (commonDatas.fileExt.length === 0) { continue }
              // 当事件可配置多个模板时，添加需要过滤
              const isMultipleTemplate = (this.limitMarkway & commonDatas.markWay) === 0
              if (!isMultipleTemplate || !this.temp.baseOpts.some(opt => opt.markWay == markWay && opt.libId == commonDatas.libId)) {
                this.temp.baseOpts.push(commonDatas)
              } else if (isMultipleTemplate) {
                filterMarkWays.push(markWay)
              }
              if (eventForm.extWatermarks[markWay]) {
                for (const extWatermark of eventForm.extWatermarks[markWay]) {
                  if (extWatermark.id == watermark.id || this.temp.baseOpts.some(opt => opt.markWay == markWay && opt.libId == extWatermark.id)) {
                    if (extWatermark.id != watermark.id && filterMarkWays[filterMarkWays.length - 1] != markWay) {
                      filterMarkWays.push(markWay)
                    }
                    continue
                  }
                  this.temp.baseOpts.push(Object.assign({}, commonDatas, { libId: extWatermark['id'], name: extWatermark['label'], rowKey: ++curMaxKey }))
                }
              }
            }
          }
          if (filterMarkWays.length > 0) {
            this.$message({
              message: `${this.$t('pages.addWatermarkEvents')}: ${filterMarkWays.map(w => this.markWayFormatter({}, w)).join(', ')}${this.$t('valid.filterSameName', { obj: this.$t('table.waterMark') })}`,
              type: 'warning',
              duration: 4000
            })
          }
          this.eventFormVis = false
        }
      })
    },
    eventFormValidate() {
      const eventWay = this.isCreateEventForm ? this.getCreateFormEventWay(this.eventForm.eventWay) : [this.eventForm.eventWay]
      // 获取已有的markWay
      const hasMarkWay = this.temp.baseOpts.map(item => item.markWay).reduce((total, cur) => total | cur, 0)
      const hasExistMarkWay = eventWay.filter(markWay => (markWay & hasMarkWay & this.limitMarkway) > 0).map(markWay => this.markWayFormatter({}, markWay))
      const updateData = this.eventFormStatus === 'update' && this.$refs['baseOptsTable'].getSelectedDatas()[0]
      if (hasExistMarkWay.length != 0 && !(updateData && updateData.markWay == eventWay[0])) {
        this.$message({
          message: this.$t('pages.onlySelectOneWatermark', { events: hasExistMarkWay.join('、') }),
          type: 'error',
          duration: 2000
        })
        return false
      }
      // 事件类型能添加多个的水印模板时，新增不作检验作过滤，而修改时作检验
      if (!this.isCreateEventForm) {
        const multipleMarkWay = Object.keys(this.eventForm.extWatermarks).reduce((p, c) => p | c, 0)
        if ((hasMarkWay & multipleMarkWay) && (eventWay[0] & multipleMarkWay)) {
          const existWatermark = this.temp.baseOpts.find(opt => opt.markWay == eventWay[0] && opt.libId == this.eventForm.watermark.id && opt.rowKey !== (updateData || {}).rowKey)
          if (existWatermark) {
            this.$message({
              message: this.markWayFormatter({}, eventWay[0]) + this.$t('pages.existSameWatermark') + `：${existWatermark.name}`,
              type: 'error',
              duration: 2000
            })
            return false
          }
        }
      }
      return true
    },
    splitArray(array, numColumns) {
      const result = [];
      const chunkSize = Math.ceil(array.length / numColumns);
      for (let i = 0; i < numColumns; i++) {
        result.push(array.slice(i * chunkSize, (i + 1) * chunkSize));
      }
      return result;
    },
    binaryToList(num) {
      const r = []
      let tempI = 1
      while (num > 0) {
        if ((num & 1) === 1) {
          r.push(tempI)
        }
        tempI <<= 1
        num >>= 1
      }
      return r
    },
    changeRemoveOptType() {
      if (this.activeTab === 'manual' && this.temp.manualRemoveOpts.type == 2) {
        const removeOptFileExt = this.temp.manualRemoveOpts.fileExt
        const option = Array.isArray(removeOptFileExt) ? removeOptFileExt : removeOptFileExt.split('|')
        this.suffixCascaderInitOpt('manualRemoveSuffixCascader', option)
      }
    },
    filterFileExt(fileExt, markWay, getUnSupExt) {
      if (!fileExt || !markWay) { return [] }
      const unSupExt = (this.addWatermarkOpts.find(opt => opt.value === markWay) || {}).unSupExt
      if (!unSupExt || unSupExt.length === 0) { return this.formatRealFileExt(fileExt) }
      // 获取所有不支持的后缀
      const filterExts = [];
      unSupExt.forEach(ext => {
        if (ext.endsWith('_suffix')) {
          const suffixes = (this.effectFileSuffixOpts.find(opt => opt.value === ext) || {}).suffixes
          suffixes && (filterExts.push(...suffixes))
        } else {
          filterExts.push(ext)
        }
      })
      return this.formatRealFileExt(fileExt).filter(ext => filterExts.includes(ext) == !!getUnSupExt)
    },
    changeEventWay() {
      if (!this.isCreateEventForm) {
        this.$nextTick(() => {
          this.suffixCascaderInitOpt('suffixCascader', this.eventForm.fileExt)
        })
        return
      }
      const eventWay = this.eventForm.eventWay || []
      const len = eventWay.length
      const index = eventWay.findIndex(item => item === '*.*')
      if (index >= 0) {
        if (index === len - 1) {
          eventWay.splice(0, len, '*.*')
        } else {
          eventWay.splice(index, 1)
        }
      } else if (this.addWatermarkOpts.length === len) {
        eventWay.splice(0, len, '*.*')
      }
    },
    getCreateFormEventWay(eventWay) {
      if (!eventWay) { return [] }
      if (eventWay.includes('*.*')) {
        return this.addWatermarkOpts.map(({ value }) => value)
      }
      return eventWay
    },
    closed() {
      this.previewDlgVisiable = false
    },
    eventWatermarkChange(val) {
      this.eventForm.watermark = val
      if (!this.isCreateEventForm) { return }
      const key = val ? (val[0] || {}).id : undefined
      this.decryptFilterKey.splice(0)
      if (!key) { return }
      this.decryptFilterKey.push(key)
      const index = this.decryptExtWatermarkKeys.findIndex(k => k == key)
      index >= 0 && this.decryptExtWatermarkKeys.splice(index, 1)
      const extIndex = this.eventForm.extWatermarks[4].findIndex(w => w.id == key)
      extIndex >= 0 && this.eventForm.extWatermarks[4].splice(extIndex, 1)
    },
    formEffectFileSuffixChange(val) {
      this.eventForm.fileExt = val;
      // 仅仅做computed的tip属性的更新
      this.isCreateEventForm && ((this.eventForm.eventWay || []).splice(0, 0))
    },
    formatRealFileExt(fileExt, suffixOpts) {
      return (fileExt || []).includes('*.*') ? (suffixOpts || this.formEffectFileSuffixOpts).map(opt => opt.suffixes || []).flat() : (fileExt || [])
    },
    includeImageFileExt(baseOpts, tempMarkWay) {
      const imageOpt = this.effectFileSuffixOpts.find(opt => opt.value === 'image_suffix')
      for (const baseOpt of (baseOpts || [])) {
        if ((baseOpt.markWay & tempMarkWay) > 0) { continue }
        const fileExt = (baseOpt.fileExt || '').split('|')
        if (fileExt.some(ext => imageOpt.suffixes.includes(ext))) {
          return true
        }
      }
      return false
    },
    changeTab() {
      if (this.isFirstOpenManual && this.activeTab === 'manual') {
        this.changeRemoveOptType()
        this.isFirstOpenManual = false
      }
    },
    suffixCascaderInitOpt(ref, option) {
      this.$refs[ref] && this.$refs[ref].initOpt(option)
    }
  }
}
</script>

<style lang='scss' scoped>
.el-dialog__body{
  .el-tabs{
    border: 0;
  }
  .el-tabs__item{
    color: #666;
    &.is-active {
      color: #409EFF;
    }
  }
  .el-main{
    padding: 10px 20px 0 20px;
  }
  .suffix-select {
    width: 70px;
    >>>.el-input__inner {
      height: 30px !important;
    }
  }
}
>>>.el-card__body {
  padding: 2px;
}
>>>.el-cascader__tags {
  top: 10px;
}
.priority-tip {
  display: flex;
  margin: 2px 48px -2px 48px;
  color: rgb(34, 34,241);
  p {
    margin: 4px 0;
  }
}
.checkbox-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 60%; /* 确保容器有足够的宽度 */
  margin-left: 20px;
}

.checkbox-item {
  width: 100%; /* 确保每个复选框占据一列的全部宽度 */
  box-sizing: border-box; /* 确保 padding 和 border 不会影响宽度 */
}

>>>.warn-font {
  color: red;
  font-size: 16px;
  font-weight: bold;
}
</style>
