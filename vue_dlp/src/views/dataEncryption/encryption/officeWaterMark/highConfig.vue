<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.highConfig')"
      :visible.sync="dialogVisible"
      width="650px"
      @dragDialog="handleDrag"
    >
      <Form>
        <FormItem :label="`${$t('pages.setting')}${$t('pages.eventPriority')}`" label-width="120px">
          <el-button size="small" @click="showDragDialog">{{ $t('button.batchServiceConfig') }}</el-button>
          <el-button size="small" @click="resetPriority">{{ $t('button.reset') }}</el-button>
        </FormItem>
        <grid-table
          :height="162"
          :multi-select="false"
          :show-pager="false"
          :default-sort="{ prop: 'priority', order: 'desc' }"
          :row-datas="advanceOpts.eventPriority.eventPriorityEx"
          :col-model="advanceEventColModel"
          style="padding-left: 46px; width: 90%"
        />
        <div class="priority-tip">
          <p>{{ `${$t('text.prompt')}：` }}</p>
          <div style="flex: 1">
            <p>1. {{ $t('pages.priorityTip1') }}</p>
            <p>2. {{ $t('pages.priorityTip2') }}</p>
          </div>
        </div>
        <FormItem :label="`${$t('pages.officeAdvanceOptTip1')}：`" label-width="320px">
          <el-radio-group v-model="advanceOpts.handleDiffEvent">
            <el-radio :label="0">{{ $t('pages.officeAdvanceOpt1', { no: '' }) }}</el-radio>
            <el-radio :label="1">{{ $t('pages.officeAdvanceOpt1', { no: $t('pages.doNot') }) }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="`${$t('pages.officeAdvanceOptTip2')}：`" label-width="320px">
          <el-radio-group v-model="advanceOpts.handleSameEvent">
            <el-radio :label="0">{{ $t('pages.officeAdvanceOpt2', { no: '' }) }}</el-radio>
            <el-radio :label="1">{{ $t('pages.officeAdvanceOpt2', { no: $t('pages.doNot') }) }}</el-radio>
          </el-radio-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleConfig">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      width="500px"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dragVisible"
      :title="$t('pages.dragSetPriority')"
    >
      <tree-menu
        :is-filter="false"
        :data="dragDatas"
        :allow-drag="() => true"
        :allow-drop="allowDrop"
        :render-content="advanceOptRenderContent"
        :width="460"
        @node-drop="dragNodeDrop"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dragSubmit">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dragVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAdvanceOpts, saveOfficeAdvanceOpts } from '@/api/behaviorManage/hardware/waterMark'

export default {
  name: 'DocumentWatermarkHighConfig',
  props: {
    addWatermarkOpts: { type: Array, required: true }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      dragVisible: false,
      isFilteredByModuleIds: false, // 是否已经根据销售模块进行过滤
      dragDatas: [],
      advanceOpts: {},
      defaultAdvanceOpts: {
        // 终端要求，还需再一层获取事件优先级信息
        eventPriority: {
          eventPriorityEx: []
        },
        handleDiffEvent: 0,
        handleSameEvent: 0
      },
      advanceEventColModel: [
        { label: 'lockStyle', prop: 'markWay', width: '100', sort: true, sortOriginal: true, formatter: (row, data) => row['label'] },
        { label: 'priority', prop: 'priority', width: '120', sort: true, type: 'select', options: [] }
      ]
    }
  },
  created() {
    this.initDefaultParams()
  },
  methods: {
    show() {
      this.dialogVisible = true
      this.submitting = false
      if (!this.isFilteredByModuleIds) {
        const moduleIds = [...this.$store.getters.saleModuleIds]
        this.defaultAdvanceOpts.eventPriority.eventPriorityEx = this.defaultAdvanceOpts.eventPriority.eventPriorityEx.filter(opt => {
          return !opt.moduleId || moduleIds.includes(opt.moduleId)
        })
        this.isFilteredByModuleIds = true
      }
      this.advanceOpts = JSON.parse(JSON.stringify(this.defaultAdvanceOpts))
      getAdvanceOpts().then(res => {
        if (res.data) {
          Object.assign(this.advanceOpts, res.data.advanceOpts)
          // 默认参数替换至row， 主要是因为 当新增新的水印事件时，直接使用row覆盖，会造成优先级没有新增水印事件的优先级
          this.defaultAdvanceOpts.eventPriority.eventPriorityEx.forEach(item => {
            const ex = this.advanceOpts.eventPriority.eventPriorityEx
            const rowEventPriority = ex.find(o => o.markWay === item.markWay)
            if (!rowEventPriority) {
              ex.push(item)
            } else {
              const priority = rowEventPriority['priority']
              Object.assign(rowEventPriority, item, { priority })
            }
          })
          this.advanceOpts.eventPriority.eventPriorityEx = this.defaultAdvanceOpts.eventPriority.eventPriorityEx.filter(opt => {
            // 如果没有label属性，说明是因为没权限被隐藏的配置，因此可以过滤不显示
            return opt.hasOwnProperty('label')
          })
        }
      })
    },
    initDefaultParams() {
      // 初始化优先级, 因为优先级是对事件的覆盖处理，而下载水印一定在事件之前
      const eventPriorities = this.defaultAdvanceOpts.eventPriority.eventPriorityEx
      eventPriorities.splice(0, eventPriorities.length, ...this.addWatermarkOpts.map(opt => { return { markWay: Number(opt.value), priority: 1, label: opt.label, moduleId: opt.moduleId } }))
      this.advanceOpts = JSON.parse(JSON.stringify(this.defaultAdvanceOpts))
      // 初始化优先级级别总数
      const rowModelOpts = this.advanceEventColModel.find(item => item['prop'] === 'priority')['options']
      rowModelOpts.splice(0, rowModelOpts.length,
        ...Array.from({ length: eventPriorities.length }, (_, index) => { return { label: this.$t('pages.customLevel', { number: index + 1 }), value: index + 1 } })
      )
    },
    showDragDialog() {
      const eventPriorities = this.advanceOpts.eventPriority.eventPriorityEx
      if (eventPriorities) {
        let length = eventPriorities.length
        const tempDatas = Array.from({ length }, (_, index) => {
          const item = eventPriorities[index]
          return {
            label: item.label,
            priority: item.priority,
            id: index + 1,
            parentId: ''
          }
        })
        tempDatas.sort((a, b) => b['priority'] - a['priority'])
        tempDatas.forEach(data => { data['priority'] = length-- })
        this.dragDatas = tempDatas
      }
      this.dragVisible = true
    },
    resetPriority() {
      this.advanceOpts.eventPriority.eventPriorityEx.forEach(item => { item['priority'] = 1 })
    },
    handleConfig() {
      this.submitting = true
      saveOfficeAdvanceOpts({ advanceOpts: this.advanceOpts }).then(res => {
        this.submitting = false
        this.dialogVisible = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.saveSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(r => {
        this.submitting = false
      })
    },
    allowDrop(draggingNode, dropNode, type) {
      return type !== 'inner'
    },
    dragSubmit() {
      for (const dragData of this.dragDatas) {
        const index = dragData['id'] - 1
        const eventPriority = this.advanceOpts.eventPriority.eventPriorityEx[index]
        eventPriority && (eventPriority['priority'] = dragData['priority'])
      }
      // 更新表格
      this.advanceOpts.eventPriority.eventPriorityEx.splice(0, 0)
      this.dragVisible = false
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.settingSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    advanceOptRenderContent(h, { node, data, store }) {
      const show = false
      return (
        <div style='flex: 1'>
          <span>{data.label}</span>
          <span style='float: right'>
            <span>{this.$t('pages.customLevel', { number: data.priority })}</span>
            <span v-show={!show} class='el-ic' style=''>
              <span class='el-icon-arrow-up' style='padding: 0 10px; font-size: 12px' on-click={e => this.nodeDisplace(e, store, node, data, 1)}>{this.$t('text.topping')}</span>
              <span class='el-icon-top' style='padding: 0 10px; font-size: 12px' on-click={e => this.nodeDisplace(e, store, node, data, 2)}>{this.$t('text.up')}</span>
              <span class='el-icon-bottom' style='padding: 0 10px; font-size: 12px' on-click={e => this.nodeDisplace(e, store, node, data, 3)}>{this.$t('text.down')}</span>
              <span class='el-icon-arrow-down' style='padding: 0 10px; font-size: 12px' on-click={e => this.nodeDisplace(e, store, node, data, 4)}>{this.$t('text.bottoming')}</span>
            </span>
          </span>
        </div>
      )
    },
    nodeDisplace(e, store, node, data, type) {
      e.stopPropagation()
      const parent = node.parent
      const childNodes = parent.childNodes
      const childDatas = parent.data
      let length = childDatas.length
      const index = childDatas.findIndex(item => item.id == data.id)
      if (type === 1) {
        // 置顶
        childDatas.splice(0, 0, ...childDatas.splice(index, 1))
        childNodes.splice(0, 0, ...childNodes.splice(index, 1))
      } else if (type === 2) {
        // 向上
        childDatas.splice(Math.max(index - 1, 0), 0, ...childDatas.splice(index, 1))
        childNodes.splice(Math.max(index - 1, 0), 0, ...childNodes.splice(index, 1))
      } else if (type === 3) {
        // 向下
        childDatas.splice(Math.min(index + 1, length), 0, ...childDatas.splice(index, 1))
        childNodes.splice(Math.min(index + 1, length), 0, ...childNodes.splice(index, 1))
      } else {
        // 置底
        childDatas.splice(length - 1, 0, ...childDatas.splice(index, 1))
        childNodes.splice(length - 1, 0, ...childNodes.splice(index, 1))
      }
      childDatas.forEach(item => {
        item['priority'] = length--
      })
    },
    dragNodeDrop(draggingNode, dropNode, dropType, ev) {
      const parent = dropNode.parent
      const childDatas = parent.data
      let length = childDatas.length
      childDatas.forEach(item => { item['priority'] = length-- })
    },
    handleDrag() {
    }
  }
}
</script>

<style lang="scss" scoped>
.priority-tip {
  display: flex;
  margin: 2px 48px -2px 48px;
  color: rgb(34, 34,241);
  p {
    margin: 4px 0;
  }
}
</style>
