<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="query.stgTypeNumber"/>
        <el-button icon="el-icon-setting" size="mini" @click="handleAdvanced">
          {{ $t('button.highConfig') }}
        </el-button>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <office-water-mark-stg-dlg
      ref="waterMark"
      :stg-type-number="query.stgTypeNumber"
      :formable="formable"
      :treeable="treeable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    />
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importOfficeWaterMarkStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="151"
      @success="importSuccess"
    />
    <document-watermark-high-config ref="highConfig" :add-watermark-opts="addWatermarkOpts"/>
  </div>
</template>

<script>
import { getStrategyList, deleteStrategy } from '@/api/behaviorManage/hardware/waterMark'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { osTypeIconFormatter, stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import OfficeWaterMarkStgDlg from './editDlg'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import DocumentWatermarkHighConfig from '@/views/dataEncryption/encryption/officeWaterMark/highConfig'
import { getAddWatermarkOpts } from '@/utils/officeWaterMark'

export default {
  name: 'OfficeWaterMark',
  components: { DocumentWatermarkHighConfig, OfficeWaterMarkStgDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'templateRemark', width: '300', formatter: this.strategyFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        stgTypeNumber: 151,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {},
      addWatermarkOpts: getAddWatermarkOpts()
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs.waterMark.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.waterMark.handleUpdate(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi(this.query)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: 151 })
    },
    importSuccess() {
      this.handleFilter()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(','), stgTypeNumber: this.query.stgTypeNumber }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    strategyFormatter: function(row, data) {
      let str = ''
      if (row.baseOpts) {
        let formatMarkWay = row.baseOpts.map(opt => opt.markWay).filter(markWay => (markWay & row.tempMarkWay) === 0).reduce((p, c) => p | c, 0)
        let markWay = 1
        while (formatMarkWay > 0) {
          if ((formatMarkWay & 1) === 1) {
            if (str.length > 0) { str += '，' }
            const label = this.addWatermarkOpts.find(opt => opt.value === markWay).label
            str += `${label}${markWay === 8 ? '' : this.$t('pages.addWatermarkDesc')}`
          }
          markWay <<= 1
          formatMarkWay >>= 1
        }
      }
      if ((row.manualRemoveOpts || {}).active) {
        if (str.length > 0) { str += '，' }
        str += (row.manualRemoveOpts.type == 1 ? this.$t('pages.approvalRemoveWatermark') : this.$t('pages.manualRemoveWatermark'))
      }
      return str
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleAdvanced() {
      this.$refs['highConfig'] && this.$refs['highConfig'].show()
    }
  }
}
</script>
