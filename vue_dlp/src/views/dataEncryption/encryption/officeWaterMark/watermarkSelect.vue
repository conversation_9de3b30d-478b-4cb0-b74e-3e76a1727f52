<template>
  <tree-select
    ref="formWatermarkTree"
    default-expand-all
    :clearable="clearable"
    :width="width"
    :data="treeData"
    :checked-keys="value"
    :filter-key="filterKey"
    :multiple="multiple"
    collapse-tags
    :render-content="renderContent"
    :rewrite-node-click-fuc="!multiple"
    :node-click-fuc="nodeClick"
    @change="change"
  />
</template>

<script>

export default {
  name: 'WatermarkSelect',
  props: {
    value: {
      type: Array,
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: <PERSON>olean,
      default: false
    },
    treeData: {
      type: Array,
      required: true
    },
    width: {
      type: Number,
      default: 420
    },
    popoverCloseable: {
      type: Boolean,
      default: true
    },
    handleView: {
      type: Function,
      default: null
    },
    // 传入需要过滤的节点的nodeKey值
    filterKey: {
      type: [Array],
      default() {
        return []
      }
    }
  },
  data() {
    return {
      checkKeys: []
    }
  },
  watch: {
    popoverCloseable(val) {
      this.setPopoverCloseable(val)
    }
  },
  methods: {
    renderContent(h, { node, data, store }) {
      const iconShow = data.type == 'G'
      return (
        <div class='custom-tree-node'>
          <svg-icon v-show={iconShow} icon-class='group' />
          <svg-icon v-show={!iconShow} icon-class='watermark' />
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='eye-open' class='icon-space' on-click={r => { this.viewWatermark(data, r) }} />
          </span>
        </div>
      )
    },
    viewWatermark(data, event) {
      this.handleView(data)
      // 阻止事件冒泡，导致点击预览按钮时，同时选中节点
      event && event.stopPropagation()
      this.setPopoverCloseable(false)
    },
    // 设置 popover 是否可以被关闭（点击非modal范围的位置）
    setPopoverCloseable(boolean) {
      const vm = this.$refs['formWatermarkTree']
      vm && (vm.popoverCloseable = boolean)
    },
    nodeClick(data, node, vm) {
      if ((data || {}).type === 'G') {
        return false
      }
    },
    change(selectedKeys, selectedOptions) {
      console.log(selectedOptions)
      if (Array.isArray(selectedOptions)) {
        for (let i = selectedOptions.length - 1; i >= 0; i--) {
          if (selectedOptions[i].type === 'G') {
            selectedKeys.splice(i, 1)
            selectedOptions.splice(i, 1)
          }
        }
      }
      this.$emit('input', Array.isArray(selectedKeys) ? selectedKeys : [selectedKeys])
      this.$emit('change', Array.isArray(selectedOptions) ? selectedOptions : [selectedOptions])
    }
  }
}
</script>

<style scoped>

</style>
