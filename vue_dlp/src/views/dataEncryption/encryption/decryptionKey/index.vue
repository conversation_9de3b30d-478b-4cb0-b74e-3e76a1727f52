<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button type="primary" icon="el-icon-download" size="mini" @click="downloadPlugin">
          {{ $t('button.pluginDownload') }}
        </el-button>
        <span style="margin-left: 10px;">
          {{ $t('pages.devAccessStatus') }}：
          <el-select v-model="accessState">
            <el-option v-for="(value, key) in accessStateOptions" :key="key" :label="value" :value="parseInt(key)" />
          </el-select>
        </span>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <div>
          <span v-show="pluginOk" style="color: #2b7aac">
            {{ $t('pages.decryptionKey_Msg1', { version: pluginVer }) }}
          </span>
          <span v-show="!pluginOk" style="color: red;">
            {{ $t('pages.decryptionKey_Msg2') }}
          </span>
        </div>
      </div>
      <grid-table
        ref="usbTable"
        :show-pager="false"
        :col-model="colModel"
        :row-datas="usbDiskList"
        :multi-select="multiSelect"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.decKEYSetting')"
      :visible.sync="dialogConfigFormVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="110px"
        style="width: 400px; margin-left: 30px;"
      >
        <FormItem :label="$t('table.usbName')" prop="nickName">
          <el-input v-model="temp.nickName" :maxlength="50"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.usbDevSerialNum')" prop="deviceSerial">
          <el-input v-model="temp.deviceSerial" disabled></el-input>
        </FormItem>
        <FormItem :label="$t('table.enabledTime')" prop="startTime">
          <el-date-picker
            v-model="temp.startTime"
            :clearable="false"
            :editable="false"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            :picker-options="pickerOptionsStart"
            :placeholder="$t('pages.selectDateTime')"
            style="width: 100%;"
          ></el-date-picker>
        </FormItem>
        <FormItem :label="$t('table.disabledTime')" prop="endTime">
          <el-date-picker
            v-model="temp.endTime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :clearable="false"
            :editable="false"
            type="datetime"
            :picker-options="pickerOptionsEnd"
            :placeholder="$t('pages.selectDateTime')"
            style="width: 100%;"
          ></el-date-picker>
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" :rows="2" resize="none" :maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="submitEnd()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogConfigFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.plugInCheck')"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div v-loading="loading">
        <div v-show="pluginOk" class="plugin-check-box plugin-check-ok">
          <div class="plugin-status is-ok" style="color: #0c60a5">
            <i class="el-icon-success" style="color: #0c60a5"></i>
            <span>{{ $t('pages.pluginNormal') }}</span>
          </div>
          <div class="cfg-item">
            <label>{{ $t('pages.pluginVersion') }}</label>
            <span>{{ pluginVer }}</span>
          </div>
          <div v-show="showNewPlugin">
            <span>{{ $t('pages.pluginNewVersionMsg', { info: latestPluginVer }) }}</span>
            <el-button type="text" @click="downloadPlugin">{{ $t('pages.downloadPlugin') }}</el-button>
          </div>
        </div>
        <div v-show="!pluginOk" class="plugin-check-box" >
          <div class="plugin-status is-err" style="padding-bottom: 10px;">
            <i class="el-icon-error" style="color: red"></i>
            <span style="color: red">{{ $t('pages.pluginConnectionFailed') }}</span>
          </div>
          <div>
            <i18n path="pages.pluginRunStatusMsg1">
              <el-button slot="button" type="text" @click="downloadPlugin">{{ $t('pages.downloadPlugin') }}</el-button>
            </i18n>
          </div>
          <div>
            {{ $t('pages.decryptionKey_Msg3') }}
          </div>
          <div class="cfg-item">
            <label style="width: 92px;">{{ $t('pages.decryptionKey_Msg4') }}</label>
            <el-input v-model="pluginPort" style="width: 200px"/>
            <el-button type="primary" size="mini" @click="testConnect">{{ $t('pages.reconnect') }}</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <usb-plug-downloader ref="downloader"/>
  </div>
</template>

<script>
import { UTermPluginClient, Level } from '@/views/system/terminalManage/terminal/uterm/client'
import UsbPlugDownloader from '@/views/system/terminalManage/terminal/uterm/downloader'
import { getDecryptionKeyStrategy, listInfo, saveInfo, deleteInfo } from '@/api/dataEncryption/encryption/decryptionKey'
import { parseTime } from '@/utils/index.js'
import { getPluginVersion } from '@/api/system/terminalManage/uterm'

export default {
  name: 'DecryptionKey',
  components: { UsbPlugDownloader },
  data() {
    return {
      multiSelect: false,
      colModel: [
        { prop: 'isAuthorized', label: 'usbDiskStatus', width: '150', fixed: true, formatter: this.statusFormatter },
        { prop: 'nickName', label: 'usbName', width: '150', sort: true, fixed: true },
        { prop: 'deviceSerial', label: 'pnpDeviceId', width: '150', sort: true, fixed: true },
        { prop: 'diskDrive', label: 'devAccessStatus', width: '150', sort: true, sortOriginal: true, fixed: true, formatter: this.diskDriveFormatter },
        { prop: 'startTime', label: 'enabledTime', width: '150', sort: true },
        { prop: 'endTime', label: 'disabledTime', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '150', sort: true },
        { label: 'operate', width: '150', type: 'button', fixed: 'right',
          buttons: [
            { label: 'grantAuthor', click: this.authorize, formatter: this.authorizeFormatter },
            { label: 'cancelGrantAuthor', click: this.cancelAuthorize, formatter: this.cancelAuthorizeFormatter }
          ]
        }
      ],
      rules: {
        startTime: [{ required: true, message: this.$t('pages.bossCode_Validate1'), trigger: 'blur' }],
        endTime: [
          { required: true, message: this.$t('pages.bossCode_Validate2'), trigger: 'blur' },
          { validator: this.timeValidator, trigger: 'blur' }
        ]
      },
      temp: {},
      defaultTemp: { // 表单字段
        nickName: '',
        diskDrive: undefined,
        deviceSerial: undefined,
        startTime: undefined,
        endTime: undefined,
        remark: ''
      },
      dialogFormVisible: false,
      dialogConfigFormVisible: false,
      submitting: false,
      loading: false,
      pluginOk: false,
      pluginPort: UTermPluginClient.getPort(),
      pluginVer: undefined,
      latezstPluginVer: undefined,
      showNewPlugin: false,
      latestPluginVer: undefined,
      plugin: undefined,
      usbDiskStatusOptions: {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      },
      usbDiskList: [
        // { IsAuthorized: 0, UsbDiskId: 1, UsbDiskName: 'test', StartTime: undefined, EndTime: undefined } // 测试数据
      ],
      authInfos: [],
      pickerOptionsStart: {
        disabledDate: time => {
          const endDateVal = this.temp.endTime
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime()
          } else {
            return time.getTime() < new Date(new Date().setDate(new Date().getDate() - 1)).getTime()
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.temp.startTime
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime())
          } else {
            return time.getTime() < new Date(new Date().setDate(new Date().getDate() - 1)).getTime()
          }
        }
      },
      accessState: 0,
      accessStateOptions: {
        0: this.$t('pages.all'),
        1: this.$t('pages.connected'),
        2: this.$t('pages.notConnected')
      },
      timer: undefined,
      reGetNum: 0
    }
  },
  computed: {
    gridTable() {
      return this.$refs['usbTable']
    }
  },
  created() {
    getPluginVersion().then(ver => {
      this.latestPluginVer = ver
    })
    this.connect()
  },
  activated() {
  },
  destroyed() {
    this.plugin.close()
  },
  methods: {
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    handleDrag() {},
    downloadPlugin() {
      this.$refs.downloader.show(this.latestPluginVer)
    },
    handleFilter() {
      this.usbDiskList = []
      if (!this.plugin || this.plugin.closed) {
        this.connect()
      } else {
        this.listUsbInfo()
      }
    },
    handleRefresh() {
      this.accessState = 0
      this.handleFilter()
    },
    async testConnect() {
      this.plugin.close()
      await this.connect()
    },
    async connect() {
      this.loading = true
      this.plugin = new UTermPluginClient(this, this.pluginPort, Level.INFO, true)
      if (this.plugin.closed) {
        listInfo().then(respond => {
          this.usbDiskList = respond.data
        })
      }
      await this.$nextTick(() => {
        this.plugin.getPlugInfo().then(data => {
          this.pluginOk = data.PlugWorkType === 0
          this.pluginVer = data.PlugVersion
          if (this.pluginOk) {
            // if (this.latezstPluginVer && this.pluginVer !== this.latezstPluginVer) {
            //   this.showNewPlugin = true
            //   this.dialogFormVisible = true
            // }
            this.listUsbInfo()
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.plugInConnectionSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.fail'),
              message: this.$t('pages.pluginConnectionFailed'),
              type: 'error',
              duration: 2000
            })
            this.dialogFormVisible = true
          }
        }).catch(reason => {
          console.log(reason)
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('pages.pluginConnectionFailed'),
            type: 'error',
            duration: 2000
          })
          this.pluginOk = false
          this.dialogFormVisible = true
          this.pluginVer = undefined
        }).finally(() => {
          this.loading = false
        })
      })
    },
    listUsbInfo() {
      this.plugin.listUsbInfo(data => {
        if (data) {
          const deviceSerials = []
          const accessUsb = []
          data.forEach(item => {
            deviceSerials.push(item.UsbDiskSerial)
            const info = {
              isAuthorized: item.IsAuthorized,
              diskDrive: item.UsbDiskId,
              nickName: item.UsbDiskName,
              deviceSerial: item.UsbDiskSerial,
              startTime: item.StartTime,
              endTime: item.EndTime,
              remark: ''
            }
            accessUsb.push(info)
          })
          listInfo().then(respond => {
            if (this.accessState === 0) {
              respond.data.forEach(el => {
                const index = deviceSerials.indexOf(el.deviceSerial)
                if (index < 0) {
                  accessUsb.push(el)
                } else {
                  const temp = { ...accessUsb[index], remark: el.remark, nickName: el.nickName }
                  accessUsb.splice(index, 1, temp)
                }
              })
              this.usbDiskList = accessUsb
            } else if (this.accessState === 1) {
              respond.data.forEach(el => {
                const index = deviceSerials.indexOf(el.deviceSerial)
                if (index > -1) {
                  const temp = { ...accessUsb[index], remark: el.remark, nickName: el.nickName }
                  accessUsb.splice(index, 1, temp)
                }
              })
              this.usbDiskList = accessUsb
            } else {
              this.usbDiskList.splice(0)
              respond.data.forEach(el => {
                const index = deviceSerials.indexOf(el.deviceSerial)
                if (index < 0) {
                  this.usbDiskList.push(el)
                }
              })
            }
          })
        } else if (!data && (this.accessState !== 1)) {
          listInfo().then(respond => {
            this.usbDiskList = respond.data
          })
        }
      }, 1).catch(() => {
        listInfo().then(respond => {
          this.usbDiskList = respond.data
        })
      })
    },
    authorize(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      this.dialogConfigFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    cancelAuthorize(row) {
      this.$confirmBox(this.$t('pages.decryptionKey_Msg5'), this.$t('text.prompt')).then(() => {
        const UsbDiskSerial = row.deviceSerial
        this.plugin.deleteDecryptionKey(UsbDiskSerial).then(({ status }) => {
          if (status === 0) {
            deleteInfo(row.deviceSerial).then(respond => {
              this.handleRefresh()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.decryptionKey_Msg6'),
                type: 'success',
                duration: 2000
              })
            }).catch(res => {
              console.log(res)
            })
          } else {
            this.$notify({
              title: this.$t('text.fail'),
              message: this.$t('pages.decryptionKey_Msg7'),
              type: 'error',
              duration: 2000
            })
          }
        })
      }).catch(() => {})
    },
    submitEnd() {
      this.submitting = true
      const that = this
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, that.temp)
          if (this.timer) {
            clearTimeout(this.timer)
            this.timer = undefined
          }
          this.timer = setInterval(() => {
            that.getDecryptionKeyStrategy(tempData)
          }, 500)
        } else {
          that.submitting = false
        }
      })
    },
    getDecryptionKeyStrategy(tempData) {
      const starTime = parseTime(tempData.startTime, 'y-m-d h:i:s')
      const endTime = parseTime(tempData.endTime, 'y-m-d h:i:s')
      getDecryptionKeyStrategy(starTime, endTime).then(response => {
        if (response.requestId) {
          clearTimeout(this.timer)
          this.$socket.subscribeToAjax(response, 'decryptionKey/result', (respond, handle) => {
            // 得到异步结果
            handle.close()
            // 请求文件下载
            const data = respond.data
            this.plugin.updateDecryptionKey({
              UsbDiskSerial: tempData.deviceSerial,
              UsbKeyFile: data.decryptionKeyData
            }, ({ status }) => {
              this.submitting = false
              if (status === 0) {
                this.dialogConfigFormVisible = false
                const authInfo = {
                  deviceSerial: this.temp.deviceSerial,
                  nickName: this.temp.nickName,
                  startTime: starTime,
                  endTime: endTime,
                  remark: this.temp.remark
                }
                saveInfo(authInfo).then(respond => {
                  this.handleRefresh()
                  this.$notify({
                    title: this.$t('text.success'),
                    message: this.$t('pages.decryptionKey_Msg8'),
                    type: 'success',
                    duration: 2000
                  })
                }).catch(res => {
                  console.log(res)
                  this.$notify({
                    title: this.$t('text.warning'),
                    message: this.$t('pages.decryptionKey_Msg9'),
                    type: 'success',
                    duration: 2000
                  })
                })
              } else {
                let msg
                if (status === 1) {
                  msg = this.$t('pages.protocolValidationError')
                } else if (status === 2) {
                  msg = this.$t('pages.decKeyNotExist')
                } else if (status === 3) {
                  msg = this.$t('pages.decryptionKey_Msg10')
                } else {
                  msg = this.$t('pages.decryptionKey_Msg11')
                }
                this.$notify({
                  title: this.$t('text.fail'),
                  message: msg,
                  type: 'error',
                  duration: 2000
                })
              }
            })
          })
        }
      }).catch(reason => {
        console.log(reason)
        this.submitting = false
      })
    },
    timeValidator(rule, value, callback) {
      if (this.temp.startTime > this.temp.endTime) {
        callback(this.$t('pages.bossCode_Validate3'))
      } else {
        callback()
      }
    },
    statusFormatter: function(row, data) {
      if (!row.diskDrive) {
        return this.$t('pages.authorized')
      }
      return this.usbDiskStatusOptions[data]
    },
    diskDriveFormatter: function(row, data) {
      const msg = row.diskDrive ? this.$t('pages.connected') : this.$t('pages.notConnected')
      return msg
    },
    authorizeFormatter: function(row, data) {
      let btnName = this.$t('pages.authorization')
      if (!row.diskDrive) {
        btnName = ''
      }
      if (row.isAuthorized === 1) {
        btnName = this.$t('pages.reauthorization')
      }
      return btnName
    },
    cancelAuthorizeFormatter: function(row, data) {
      let btnName = this.$t('table.cancelGrantAuthor')
      if (row.isAuthorized === 0 || !row.diskDrive) {
        btnName = ''
      }
      return btnName
    }
  }
}
</script>
