<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.importApp')"
      :visible.sync="appVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <span style="color:blue;padding-bottom: 5px">{{ $t('pages.outgoingProcess_text9') }}</span>
      <el-container>
        <el-aside width="210px">
          <tree-menu ref="appTypeTree" :height="400" :multiple="addGroupBtn" :default-expand-all="true" :data="appTypeTreeData" :render-content="renderContent" @node-click="appTypeTreeNodeCheckChange" />
        </el-aside>
        <el-main>
          <div>
            <div class="toolbar">
              <el-button v-if="showByOsType" type="primary" icon="el-icon-plus" size="mini" @click="handleBatchCreate()">
                {{ $t('button.add') }}
              </el-button>
              <el-button icon="el-icon-delete" size="mini" :disabled="!appDeleteable" @click="deleteApp">
                {{ $t('button.delete') }}
              </el-button>
              <div style="float: right;">
                <el-input v-model="appQuery.searchInfo" clearable :placeholder="$t('pages.exeName')" style="width: 130px;" @keyup.enter.native="handleAppFilter" />
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleAppFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <div style="height: 360px;">
              <grid-table
                ref="appInfoList"
                :height="360"
                pager-small
                :col-model="colModel"
                :row-data-api="loadAppList"
                @selectionChangeEnd="appSelectionChangeEnd"
              />
            </div>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="handleSelectedCheckApp">
          {{ $t('pages.addApp') }}
        </el-button>
        <el-button @click="appVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <app-batch-add-dlg
      ref="batchUpload"
      :os-type="osType"
      :disable-product-md5="disableProductMd5"
      :support-md5="supportMd5"
      :append-to-body="appendToBody"
      :create="batchCreate"
      :create-group="createGroup"
      :update-group="updateGroup"
      :get-group-by-name="getGroupByName"
      :type-tree-data="typeTreeData"
      :verify-model-able="false"
      @submitEnd="batchSubmitEnd"
    />
    <app-group-dlg
      ref="appGroupDlg"
      :os-type="osType"
      :append-to-body="appendToBody"
      :create="createGroup"
      :update="updateGroup"
      :get-by-name="getGroupByName"
      @submitEnd="groupSubmitEnd"
    />

    <app-add-dlg
      ref="appAddDlg"
      :os-type="osType"
      :disable-product-md5="disableProductMd5"
      :support-md5="supportMd5"
      :append-to-body="appendToBody"
      :type-tree-data="typeTreeData"
      :create="create"
      :update="update"
      :create-group="createGroup"
      :update-group="updateGroup"
      :get-group-by-name="getGroupByName"
      @submitEnd="addSubmitEnd"
    />

    <!-- 修改程序 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="this.$t('pages.updateProcess')"
      :visible.sync="appFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="appDataForm" :rules="appRules" :model="appTemp" label-position="right" label-width="120px" style="width: 500px; margin-left: 25px;">
        <FormItem :label="$t('pages.appType')" prop="classId">
          <tree-select v-model="appTemp.classId" :data="typeTreeData" node-key="dataId" :checked-keys="[appTemp.classId]" @change="appTypeNodeSelectChange" />
        </FormItem>
        <FormItem :label="$t('table.fileDescription')" prop="remark">
          <el-input v-model="appTemp.remark" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('table.softSign')" prop="softSign">
          <el-input v-model="appTemp.softSign" :disabled="true"/>
        </FormItem>
        <FormItem :label="$t('table.fileMd5')" prop="fileMd5">
          <el-input v-model="appTemp.fileMd5" :disabled="true"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="updateApp">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="appFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { enableStgDelete } from '@/utils'
import AppBatchAddDlg from './appBatchAddDlg'
import AppGroupDlg from '@/views/system/baseData/appLibrary/appGroupDlg'
import AppAddDlg from '@/views/system/baseData/appLibrary/appAddDlg'

export default {
  name: 'AppImportTable',
  components: { AppBatchAddDlg, AppGroupDlg, AppAddDlg },
  props: {
    showByOsType: { type: Boolean, default: true },
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false },
    typeTreeData: { type: Array, default() { return null } },
    groupRootName: { type: String, default() { return this.$t('pages.appLibrary') } },
    list: { // 查询APP
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    disableProductMd5: { // 是否禁用“产品名称”防伪冒
      type: Boolean,
      default: true
    },
    countByGroup: { // 统计APP
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    create: { // 添加APP
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    batchCreate: { // 批量添加APP
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    update: { // 修改APP
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    delete: { // 删除APP
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    importFunc: { // 导入APP
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    createGroup: { // 添加APP分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    updateGroup: { // 修改APP分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    deleteGroup: { // 删除APP分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    getGroupByName: { // 根据分组名称获取分组
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
      }
    },
    supportMd5: { // 是否支持指纹防伪冒（某些功能终端还不支持，所以加个开关）
      type: Boolean,
      default: true
    },
    addGroupBtn: { // 能否显示添加分组按钮
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'exeName', width: '150', sort: true },
        { prop: 'classId', label: 'typeId', width: '100', sort: true, formatter: this.classIdFormatter },
        { prop: 'remark', label: 'remark', width: '150', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      },
      appQuery: { // 查询条件
        page: 1,
        searchInfo: '',
        classId: undefined,
        classIds: '',
        osType: this.osType
      },
      appDeleteable: false,
      appAddBtnAble: false,
      appVisible: false,
      submitting: false,
      outerShow: false,
      treeNodeType: '',
      appTypeTreeData: [{ id: '0', dataId: '0', label: this.groupRootName, parentId: '', children: [] }],
      selectedClassId: null,   //  鼠标选中的类型
      appRules: {
        processName: [
          { required: true, message: this.$t('pages.appGroup_text25'), trigger: 'blur' }
        ],
        classId: [
          { required: true, message: this.$t('pages.outgoingProcess_text5'), trigger: 'blur' }
        ]
      },
      appTemp: {},
      appFormVisible: false
    }
  },
  computed: {

  },
  watch: {
    typeTreeData(val) {
      this.loadAppTypeTree()
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    show() {
      this.appQuery.classId = undefined
      this.appQuery.searchInfo = ''
      this.appQuery.page = 1
      this.appVisible = true
      this.loadAppTypeTree()
      this.$refs.appTypeTree && this.$refs.appTypeTree.clearFilter()
      this.$refs.appTypeTree && this.$refs.appTypeTree.setCurrent(null)
      this.$nextTick(() => {
        this.appGridTable().execRowDataApi(this.appQuery)
        this.appTypeTree().clearSelectedNodes()
      })
    },
    appGridTable() {
      return this.$refs['appInfoList']
    },
    appTypeTree: function() {
      return this.$refs['appTypeTree']
    },
    loadAppList: function(option) {
      this.appQuery.osType = this.osType
      const optionTemp = Object.assign(this.appQuery, option)
      optionTemp.osType = this.osType
      return this.list(optionTemp)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    appSelectionChangeEnd: function(rowDatas) {
      this.appDeleteable = rowDatas && rowDatas.length > 0
    },
    loadAppTypeTree: function() {
      if (this.typeTreeData) {
        this.appTypeTreeData[0].children = this.typeTreeData
        if (this.typeTreeData.length > 0) {
          this.treeNodeType = this.typeTreeData[0].type
        }
      }
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleAppTypeCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleAppTypeUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    appTypeTreeNodeCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      this.appAddBtnAble = !!checkedNode && checkedNode.data.dataId != '0'
      if (checkedNode) {
        this.appQuery.classId = checkedNode.data.dataId
      } else {
        this.appQuery.classId = undefined
      }
      this.appQuery.page = 1
      this.selectedClassId = checkedNode.data.dataId || null
      if (this.appQuery.classId == 0) {
        this.appQuery.classId = undefined
        this.selectedClassId = null
      }
      this.appGridTable().execRowDataApi(this.appQuery)
    },
    handleAppFilter() {
      this.appQuery.page = 1
      this.appGridTable().execRowDataApi(this.appQuery)
    },
    showCreate() {
      this.outerShow = true
      this.$refs.appAddDlg.show()
    },
    showBatchCreate() {
      this.outerShow = true
      this.$refs.batchUpload.show()
    },
    handleCreate() {
      this.outerShow = false
      this.$refs.appAddDlg.show({ classId: this.appQuery.classId })
    },
    handleBatchCreate() {
      this.outerShow = false
      this.$refs.batchUpload.show({ classId: this.selectedClassId })
    },
    handleUpdate(row) {
      this.outerShow = false
      this.appFormVisible = true
      this.appTemp = Object.assign(JSON.parse(JSON.stringify(row)));
    },
    handleAppTypeCreate(data) {
      this.outerShow = false
      this.$refs.appGroupDlg.show()
    },
    handleAppTypeUpdate: function(data) {
      this.outerShow = false
      this.$refs.appGroupDlg.show({
        id: data.dataId,
        name: data.label
      })
    },
    removeNode(data) {
      this.countByGroup(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.appGroup_text17'), type: 'warning', duration: 2000 })
          return
        }
        this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
          this.deleteGroup({ id: data.dataId }).then(respond => {
            this.appTypeTree().removeNode([data.id])
            const toDeleteItems = [{ id: data.dataId, itemType: 2 }]
            this.$emit('submitEnd', toDeleteItems, 0)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          })
        }).catch(() => {})
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id.toString(),
        label: data.name,
        parentId: '0'
      }
    },
    deleteApp() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.appGridTable().getSelectedIds()
        this.delete({ ids: toDeleteIds.join(',') }).then(respond => {
          this.appGridTable().deleteRowData(toDeleteIds)
          const toDeleteItems = []
          toDeleteIds.forEach(id => toDeleteItems.push({ id: id, itemType: 1 }))
          this.$emit('submitEnd', toDeleteItems, 0)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    handleSelectedCheckApp() {
      const appTypeList = this.appTypeTree().getCheckedNodes() || []
      let processList = this.appGridTable().getSelectedDatas()
      if (appTypeList.length === 0 && processList.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text15_and_text16'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (appTypeList.length > 0) {
        if (this.selectedGroupId === null) {
          processList = processList.filter(item => { return !this.arrayContainsByKey(appTypeList, item.classId, 'dataId') });
          this.$emit('submitEnd', 3, appTypeList, processList)
        } else {
          const t = appTypeList.findIndex(item => { return item.dataId === this.selectedGroupId });
          processList = t > -1 ? null : processList;
          this.$emit('submitEnd', t > -1 ? 2 : 3, appTypeList, processList)
        }
      } else {
        this.$emit('submitEnd', 1, null, processList)
      }
      this.appVisible = false
    },
    arrayContainsByKey(list, value, key) {
      key = key || 'id'
      for (let i = 0; i < list.length; i++) {
        if (list[i][key] == value) {
          return true;
        }
      }
      return false;
    },
    handleCheckApp(type) {
      let array = []
      if (type === 1) {
        array = this.appGridTable().getSelectedDatas() || []
      } else if (type === 2) {
        array = this.appTypeTree().getCheckedNodes()
      } else {
        return;
      }
      if (array.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text15'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (array && array.length > 0) {
        this.$emit('submitEnd', array, type)
      }
      this.appVisible = false
    },
    addSubmitEnd(data, dlgStatus) {
      if (this.outerShow) {
        // 修改程序或者是导致并直接引用程序时
        this.$emit('submitEnd', [data], 1)
      } else {
        if (dlgStatus == 'update') {
          // 当是修改应用程序时，因为防伪冒算法可能也会改变，所以要回调
          this.$emit('submitEnd', [data], 3)
        }
        this.appGridTable().execRowDataApi()
      }
      const msg = dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess')
      this.$notify({ title: this.$t('text.success'), message: msg, type: 'success', duration: 2000 })
      this.outerShow = false
    },
    batchSubmitEnd(data, dlgStatus) {
      if (this.outerShow) {
        this.$emit('submitEnd', data, 1)
      } else {
        this.appGridTable().execRowDataApi()
      }
      this.outerShow = false
    },
    importSubmitEnd(data) {
      if (this.outerShow) {
        this.$emit('submitEnd', data, 1)
      } else {
        this.appGridTable().execRowDataApi()
      }
      this.$notify({ title: this.$t('text.success'), message: this.$t('text.importSuccess'), type: 'success', duration: 2000 })
      this.outerShow = false
    },
    groupSubmitEnd(data, dlgStatus) {
      if (dlgStatus === 'create') {
        this.appTypeTree().addNode(this.dataToTreeNode(data))
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
      } else {
        this.appTypeTree().updateNode(this.dataToTreeNode(data))
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
      }
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    classIdFormatter(row, data) {
      let msg = ''
      if (this.typeTreeData) {
        this.typeTreeData.some(node => {
          if (node.dataId == data) {
            msg = node.label
            return true
          }
        })
      }
      return msg
    },
    appTypeNodeSelectChange: function(data) {
      this.appTemp.classId = data
      this.$refs.appDataForm.validate()
    },
    updateApp() {
      this.submitting = true
      this.$refs['appDataForm'].validate((valid) => {
        if (valid) {
          this.update(this.appTemp).then(res => {
            this.submitting = false
            this.appFormVisible = false
            this.$refs.appInfoList.execRowDataApi()
            this.temp.softList.forEach(soft => {
              if (soft.id == this.appTemp.id) {
                soft.remark = this.appTemp.remark
              }
            })
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    }
  }
}
</script>
