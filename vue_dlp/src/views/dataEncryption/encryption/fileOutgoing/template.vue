<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <download-tool></download-tool>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyTable"
        row-key="id"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :selectable="selectable"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="800px"
      @close="cancel"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 720px; margin-left:20px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <el-row>
          <el-col :span="12">
            <FormItem v-if="treeable" :label="$t('table.enable')">
              <el-switch v-model="temp.active" :disabled="!formable"></el-switch>
            </FormItem>
          </el-col>
          <el-col v-if="false" :span="12">
            <FormItem label-width="0px">
              <el-checkbox v-model="temp.disableRuleCenter" :true-label="1" :false-label="0">{{ $t('pages.templateSet_text1') }}</el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <div style="height: 265px;">
          <import-table
            ref="templateListTable"
            :grid-table-height="240"
            :row-no-label="$t('table.keyId')"
            :search-info-prompt-message="$t('pages.templateSet_text2')"
            :delete-disabled="!deleteable"
            :col-model="listColModel"
            :row-datas="tempElgData"
            :formable="formable"
            :selectable="elgSelectable"
            :handle-create="handleTemplateCreate"
            :handle-delete="handleTemplateDelete"
            :handle-import="handleTemplateImport"
            :handle-search="handleTemplateSearch"
            @selectionChangeEnd="selectionChangeEnd"
          />
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <!--        <link-button btn-type="primary" :formable="formable" btn-style="float: left" :menu-code="'A5D'" :link-url="'/system/baseData/outgoingTemplate'" :btn-text="$t('route.outgoingTemplate')"/>-->
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 新增模板库数据 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="templateTextMap[templateDialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="templateDataForm"
        :rules="templateRules"
        :model="templateTemp"
        label-position="right"
        label-width="170px"
        style="width: 700px;"
      >
        <FormItem :label="$t('pages.templateName')" prop="name" label-width="80px">
          <el-col :span="12">
            <el-input v-model="templateTemp.name" v-trim style="width: 90%" :maxlength="20"></el-input>
          </el-col>
        </FormItem>
        <el-card class="box-card">
          <div slot="header">
            <el-checkbox v-model="templateTemp.canUpdate" :true-label="1" :false-label="0">{{ $t('pages.outgoingTemplate_text1') }}</el-checkbox>
          </div>
          <div style="margin-top: -20px">
            <el-divider content-position="left">{{ $t('pages.ctrlValueList') }}</el-divider>
          </div>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :label="4" @change="ctrlChange">{{ $t('pages.ctrlValueList1') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :disabled="templateTemp.ctrlValueList.indexOf(4)!=-1" :label="1">{{ $t('pages.ctrlValueList2') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :label="2" @change="machineCodeChange">{{ $t('pages.ctrlValueList3') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :label="8" :disabled="templateTemp.noLimitUse == 1">{{ $t('pages.ctrlValueList4') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :label="32">{{ $t('pages.ctrlValueList5') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :label="64">{{ $t('pages.ctrlValueList6') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :label="16">{{ $t('pages.ctrlValueList7') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :label="1024">{{ $t('pages.ctrlValueList19') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem :label="$t('pages.readingCount')">
            <el-radio-group v-model="templateTemp.howToAccumulateUseTimes">
              <el-radio :label="0">{{ $t('pages.readingCount_0') }}</el-radio>
              <el-radio :label="1">{{ $t('pages.readingCount_1') }}</el-radio>
            </el-radio-group>
          </FormItem>
          <FormItem :label=" $t('pages.readTimes') " prop="openTimes">
            <el-col :span="12">
              <el-input-number
                v-model.number="templateTemp.openTimes"
                style="width: 90%"
                :controls="false"
                step-strictly
                :step="1"
                :disabled="templateTemp.noLimitOpen==1"
                :min="1"
                :max="templateTemp.noLimitOpen==1 ? 65535 : 65534"
              />
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.noLimitOpen" :true-label="1" :false-label="0" @change="changeLimitOpen">
                {{ $t('pages.noLimit') }}
              </el-checkbox>
            </el-col>
          </FormItem>
          <FormItem :label="$t('pages.readDays')" prop="openTimes">
            <el-col :span="12">
              <el-input-number
                v-model.number="templateTemp.useDays"
                style="width: 90%"
                :controls="false"
                step-strictly
                :step="1"
                :disabled="templateTemp.noLimitUse==1"
                :min="1"
                :max="templateTemp.noLimitUse==1 ? 65535 : 65534"
              />
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.noLimitUse" :true-label="1" :false-label="0" @change="changeLimitUse">{{ $t('pages.noLimit') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem :label="$t('pages.suffix')" prop="makeType">
            <el-col :span="12">
              <el-select v-model="templateTemp.makeType" style="width: 90%">
                <el-option :label="$t('pages.makeType1')" :value="0"></el-option>
                <el-option :label="$t('pages.makeType2')" :value="1"></el-option>
                <el-option :label="$t('pages.makeType3')" :value="2"></el-option>
              </el-select>
            </el-col>
          </FormItem>
          <div>
            <el-divider content-position="left">{{ $t('pages.accessOptions') }}</el-divider>
          </div>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.validateValueList" :label="65536">{{ $t('pages.validateValue1') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.validateValueList" :label="4194304">{{ $t('pages.validateValue7') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.validateValueList" :label="262144">{{ $t('pages.validateValue3') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.validateValueList" :label="524288">{{ $t('pages.validateValue4') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.validateValueList" :label="1048576">{{ $t('pages.validateValue5') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.validateValueList" :label="2097152">{{ $t('pages.validateValue6') }}</el-checkbox>
            </el-col>
          </FormItem>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.validateValueList" :label="8388608">{{ $t('pages.validateValue2') }}</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.validateValueList" :label="16777216">{{ $t('pages.validateValue15') }}</el-checkbox>
            </el-col>
          </FormItem>
        </el-card>
        <el-card class="box-card">
          <div slot="header">
            <span>{{ $t('pages.watermarkSetting') }}</span>
          </div>
          <FormItem label-width="0px">
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :label="128">
                {{ $t('pages.watermarkText1') }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.watermarkText2') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="templateTemp.ctrlValueList" :label="256" :disabled="templateTemp.validateValueList.indexOf(8388608)==-1 && templateTemp.validateValueList.indexOf(16777216)==-1">
                {{ $t('pages.watermarkText3') }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.watermarkText4">
                      <br slot="br"/>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-checkbox>
            </el-col>
          </FormItem>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="templateDialogStatus==='create'?createTemplateData():updateTemplateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-table-dlg
      ref="templateImportTable"
      :tree-able="false"
      :exits-list-data="elgData"
      :elg-title="$t('pages.templateSet_importTemplate')"
      :search-info-name="$t('pages.templateName')"
      :confirm-button-name="$t('pages.templateSet_addTemplate')"
      :not-selected-prompt-message="$t('pages.templateSet_notSelectedPromptMessage')"
      :col-model="importColModel"
      :check-all-able="true"
      :list="getInfoList"
      :delete="deleteData"
      :handle-create-elg="handleTemplateCreate"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
      @elgCancelAfter="elgCancelAfter"
    />

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importOutgoingTemplateStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import {
  getStrategyPage, createStrategy, updateStrategy, deleteStrategy, getStrategyByName, getTemplateTree
} from '@/api/dataEncryption/encryption/template'
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { isSameTimestamp, initTimestamp } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import {
  getPage, getByName, createData, updateData, deleteData
} from '@/api/system/baseData/outgoingTemplate'

export default {
  name: 'TemplateSet',
  components: { DownloadTool, ImportStg, ImportTable, ImportTableDlg },
  props: {
    listable: {
      type: Boolean,
      default: true
    },
    formable: { // 能否提交表单
      type: Boolean,
      default: true
    },
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 137,
      showTree: true,
      treeable: true,
      addBtnAble: false,
      deleteable: false,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'modelIds', label: 'model1', width: '200', formatter: this.modelNameFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', formatter: (row) => buttonFormatter(row, this), click: this.handleUpdate }
          ]
        }
      ],
      listColModel: [
        { prop: 'name', label: 'templateName', width: '100', sort: true },
        { prop: 'remark', label: 'templateRemark', width: '300', formatter: this.formatterRemark },
        {
          label: 'operate', type: 'button', width: '50', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleTemplateUpdateBase }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'templateName', width: '100', sort: 'custom' },
        { prop: 'remark', label: 'templateRemark', width: '300', formatter: this.formatterRemark },
        {
          label: 'operate', type: 'button', width: '50', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleTemplateUpdate }
          ]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        modelIds: '',
        disableRuleCenter: 1,
        names: []
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      query: {},
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.outgoingTemplateStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.outgoingTemplateStg'), 'create')
      },
      treeData: [
        { id: 0, dataId: 0, label: this.$t('pages.templateLib'), parentId: 0, children: [] }
      ],
      //
      tempElgData: [],
      elgData: [],
      templateTextMap: {
        update: this.i18nConcatText(this.$t('pages.outgoingTemplate'), 'update'),
        create: this.i18nConcatText(this.$t('pages.outgoingTemplate'), 'create')
      },
      dialogFormVisible: false,
      templateDialogStatus: null,
      templateTemp: {
        id: undefined,
        name: '',
        ctrlValue: 0,
        ctrlValueList: [],
        validateValue: 0,
        validateValueList: [],
        noLimitOpen: 1,
        openTimes: 65535,
        noLimitUse: 1,
        useDays: 65535,
        howToAccumulateUseTimes: 0,
        makeType: 0,
        canUpdate: 0,
        remark: ''
      },
      defaultTemplateTemp: { // 表单字段
        id: undefined,
        name: '',
        ctrlValue: 0,
        ctrlValueList: [],
        validateValue: 0,
        validateValueList: [],
        noLimitOpen: 1,
        openTimes: 65535,
        noLimitUse: 1,
        useDays: 65535,
        howToAccumulateUseTimes: 0,
        makeType: 0,
        canUpdate: 0,
        remark: ''
      },
      templateRules: {
        name: [{ required: true, trigger: 'blur', validator: this.templateNameValidator }]
      },
      createFlag: false,
      updateFlag: false,
      needIds: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyTable']
    }
  },
  watch: {
    elgData(val) {
      this.$nextTick(() => {
        // this.$refs['templateListTable'].clearSaveSelection()
      })
      this.handleTemplateSearch()
    },
    needIds(val) {
      if (val.length > 0) {
        getPage({ ids: val.join(',') }).then(res => {
          this.elgData = res.data.items || []
        })
      }
    },
    'templateTemp.openTimes'(val) {
      if (val > 65535) {
        this.templateTemp.openTimes = 65534
      }
    },
    'templateTemp.useDays'(val) {
      if (val > 65535) {
        this.templateTemp.useDays = 65534
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.loadTemplateTree()
  },
  activated() {
    this.loadTemplateTree()
  },
  methods: {
    deleteData,
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      enableStgBtn(null, this)
      this.handleFilter()
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    refresh() {
      this.loadTree()
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    loadTemplateTree() {
      if (!isSameTimestamp(this, 'OutgoingTemplate')) {
        this.loadTree()
        initTimestamp(this)
      }
    },
    loadTree() {
      getTemplateTree().then(respond => {
        this.treeData[0].children.splice(0, this.treeData[0].children.length, ...respond.data)
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.elgData = []
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['templateListTable'].clearSearchInfo()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row))
      this.showDlg = true
      this.elgData = []
      const ids = row.modelIds || ''
      if (ids !== '') {
        getPage({ ids: ids }).then(res => {
          this.elgData = res.data.items || []
        })
      }
      this.$nextTick(() => {
        this.$refs['templateListTable'].clearSearchInfo()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.setTemplateIdsAndName()
          createStrategy(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.setTemplateIdsAndName()
          const tempData = Object.assign({}, this.temp)
          updateStrategy(tempData).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    modelNameFormatter(row, data) {
      const result = []
      const arr = row.modelIds.split(',')
      if (this.treeData[0].children) {
        this.treeData[0].children.forEach(node => {
          if (arr.indexOf(node.id) != -1) {
            result.push(node.label)
          }
        })
      }
      return result.join('、')
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    //
    elgSelectable(row, index) {
      return this.tempElgData && this.tempElgData.length > 0
    },
    getNeedAddIds(needIds) {
      this.loadTree()
      this.needIds = needIds
    },
    //  删除记录
    submitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      this.elgData = this.elgData.filter(item => { return deleteIdsList.filter(i => i === item.id).length === 0 })
    },
    elgCancelAfter() {
      this.getTemplateListByIds();
    },
    getTemplateListByIds() {
      const ids = this.$refs['templateListTable'].getIdsByList(this.elgData) || []
      if (ids.length === 0) {
        this.elgData = []
        return;
      }
      getPage({ ids: ids.join(',') }).then(res => {
        this.elgData = res.data.items || []
      })
    },
    //  新增，修改弹窗
    handleTemplateCreate(selectedGroupId, flag) {
      this.templateTemp = Object.assign({}, this.defaultTemplateTemp)
      this.templateDialogStatus = 'create'
      this.dialogFormVisible = true
      this.createFlag = flag || false
      this.$nextTick(() => {
        this.$refs['templateDataForm'] && this.$refs['templateDataForm'].clearValidate()
      })
    },
    handleTemplateUpdate(row) {
      this.updateFlag = true
      this.handleTemplateUpdateBase(row)
    },
    handleTemplateUpdateBase(row) {
      this.templateTemp = Object.assign({}, row)
      this.$set(this.templateTemp, 'ctrlValueList', this.numToList(this.templateTemp.ctrlValue, 11))
      this.$set(this.templateTemp, 'validateValueList', this.numToList(this.templateTemp.validateValue, 25))
      if (this.templateTemp.openTimes == 0) {  // 如果次数为0，表示不限制
        // 界面输入框置灰，显示65535
        this.templateTemp.openTimes = 65535
        this.templateTemp.noLimitOpen = 1
      }
      if (this.templateTemp.useDays == 0) {  // 如果次数为0，表示不限制
        // 界面输入框置灰，显示65535
        this.templateTemp.useDays = 65535
        this.templateTemp.noLimitUse = 1
      }
      if (this.templateTemp.validateValueList.indexOf(131072) != -1) {
        this.templateTemp.validateValueList.splice(this.templateTemp.validateValueList.indexOf(131072), 1)
        if (this.templateTemp.validateValueList.indexOf(8388608) == -1) {
          this.templateTemp.validateValueList.push(8388608)
        }
        if (this.templateTemp.validateValueList.indexOf(16777216) == -1) {
          this.templateTemp.validateValueList.push(16777216)
        }
      }
      // 处理阅读次数累计方式
      if (this.templateTemp.ctrlValueList.indexOf(512) != -1) {
        this.$set(this.templateTemp, 'howToAccumulateUseTimes', 1)
      } else {
        this.$set(this.templateTemp, 'howToAccumulateUseTimes', 0)
      }
      this.templateDialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['templateDataForm'].clearValidate()
      })
    },
    handleTemplateDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs['templateListTable'].getSelectedIds() || []
        this.elgData = this.$refs['templateListTable'].deleteTableData(this.elgData, toDeleteIds)
      }).catch(() => {})
    },
    handleTemplateSearch(searchInfo) {
      searchInfo = searchInfo || ''
      if (searchInfo === '') {
        this.tempElgData = this.elgData
      } else {
        //  条件查询
        this.tempElgData = this.elgData.filter(item => {
          return item.name && item.name.toLowerCase().indexOf(searchInfo) !== -1
        })
      }
    },
    handleTemplateImport() {
      this.$refs['templateImportTable'].show();
    },
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    ctrlChange(val) {
      if (this.templateTemp.ctrlValueList.indexOf(4) != -1 && this.templateTemp.ctrlValueList.indexOf(1) == -1) {
        this.templateTemp.ctrlValueList.push(1)
      }
      if (this.templateTemp.ctrlValueList.indexOf(4) != -1 && this.templateTemp.ctrlValueList.indexOf(2) != -1) {
        this.templateTemp.ctrlValueList.splice(this.templateTemp.ctrlValueList.indexOf(2), 1)
      }
    },
    machineCodeChange(val) {
      if (this.templateTemp.ctrlValueList.indexOf(2) != -1 && this.templateTemp.ctrlValueList.indexOf(4) != -1) {
        this.templateTemp.ctrlValueList.splice(this.templateTemp.ctrlValueList.indexOf(4), 1)
      }
    },
    changeLimitOpen(val) {
      if (val == 1) {
        this.templateTemp.openTimes = 65535
      } else {
        this.templateTemp.openTimes = 0
      }
    },
    changeLimitUse(val) {
      if (val == 1) {
        this.templateTemp.useDays = 65535
      } else {
        this.templateTemp.useDays = 0
      }
      if (val == 1 && this.templateTemp.ctrlValueList.indexOf(8) != -1) {
        this.templateTemp.ctrlValueList.splice(this.templateTemp.ctrlValueList.indexOf(8), 1)
      }
    },
    templateNameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getByName({ name: value }).then(respond => {
          const timeInfo = respond.data
          if (timeInfo && timeInfo.id != this.templateTemp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createFlag) {
          this.$refs['templateImportTable'].refreshTableData()
        } else {
          this.elgData.push(data)
        }
        this.createFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$refs['templateImportTable'].refreshTableData()
        } else {
          for (let i = 0; i < this.elgData.length; i++) {
            if (this.elgData[i].id === data.id) {
              this.elgData.splice(i, 1)
              this.elgData.push(data)
              break
            }
          }
        }
        this.updateFlag = false
      }
    },
    createTemplateData() {
      this.submitting = true
      this.$refs['templateDataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.templateTemp)
          this.formatterData(tempData)
          createData(tempData).then(respond => {
            this.isImportElg(respond.data, 'create')
            this.submitting = false
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateTemplateData() {
      this.submitting = true
      this.$refs['templateDataForm'].validate((valid) => {
        if (valid) {
          if (this.templateTemp.ctrlValueList.indexOf(2) != -1 && this.templateTemp.ctrlValueList.indexOf(4) != -1) {
            this.$message({
              type: 'error',
              message: this.$t('pages.cloudOutfile_Msg50'),
              duration: 2000
            })
            this.submitting = false
            return
          }
          const tempData = Object.assign({}, this.templateTemp)
          this.formatterData(tempData)
          updateData(tempData).then(respond => {
            this.isImportElg(respond.data, 'update')
            this.submitting = false
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    formatterData(tempObj) {
      if (tempObj.noLimitOpen == 1) {  // 不限制次数
        tempObj.openTimes = 0
      }
      if (tempObj.noLimitUse == 1) {  // 不限制天数
        tempObj.useDays = 0
      }
      // 旧版允许打印文件细分为允许实体打印和允许虚拟打印，新版提交时，需要格式化
      if (tempObj.validateValueList.indexOf(131072) != -1) {
        tempObj.validateValueList.splice(tempObj.validateValueList.indexOf(131072), 1)
        if (tempObj.validateValueList.indexOf(8388608) == -1) {
          tempObj.validateValueList.push(8388608)
        }
        if (tempObj.validateValueList.indexOf(16777216) == -1) {
          tempObj.validateValueList.push(16777216)
        }
      }
      if (tempObj.validateValueList.indexOf(8388608) == -1 && tempObj.validateValueList.indexOf(16777216) == -1 && tempObj.ctrlValueList.indexOf(256) != -1) {
        tempObj.ctrlValueList.splice(tempObj.ctrlValueList.indexOf(256), 1)
      }
      if (tempObj.ctrlValueList.indexOf(8) != -1 && tempObj.noLimitUse == 1) {
        tempObj.ctrlValueList.splice(tempObj.ctrlValueList.indexOf(8), 1)
      }
      // 处理阅读次数累计方式
      if (this.templateTemp.howToAccumulateUseTimes == 1) {
        if (this.templateTemp.ctrlValueList.indexOf(512) == -1) {
          this.templateTemp.ctrlValueList.push(512)
        }
      } else {
        if (this.templateTemp.ctrlValueList.indexOf(512) != -1) {
          this.templateTemp.ctrlValueList.splice(this.templateTemp.ctrlValueList.indexOf(512), 1)
        }
      }
      tempObj.ctrlValue = this.getSum(tempObj.ctrlValueList)
      tempObj.validateValue = this.getSum(tempObj.validateValueList)
      tempObj.remark = this.remarkFormatter(tempObj)
    },
    remarkFormatter(row) {
      let msg = ''
      msg += row.canUpdate == 1 ? `${this.$t('pages.outgoingTemplate_text1')}，` : ''
      msg += row.ctrlValue & 4 ? `${this.$t('pages.ctrlValueList1')}，` : ''
      msg += row.ctrlValue & 1 ? `${this.$t('pages.ctrlValueList2')}，` : ''
      msg += row.ctrlValue & 2 ? `${this.$t('pages.ctrlValueList3')}，` : ''
      msg += row.ctrlValue & 8 ? `${this.$t('pages.ctrlValueList4')}，` : ''
      msg += row.ctrlValue & 32 ? `${this.$t('pages.ctrlValueList5')}，` : ''
      msg += row.ctrlValue & 64 ? `${this.$t('pages.ctrlValueList6')}，` : ''
      msg += row.ctrlValue & 16 ? `${this.$t('pages.ctrlValueList7')}，` : ''
      msg += row.ctrlValue & 1024 ? `${this.$t('pages.ctrlValueList19')}，` : ''
      msg += row.ctrlValue & 512 ? `${this.$t('pages.readingCount1', { info: this.$t('pages.readingCount_1') })}，` : `${this.$t('pages.readingCount1', { info: this.$t('pages.readingCount_0') })}，`
      msg += row.openTimes == 0 ? `${this.$t('pages.openTimes')}，` : `${this.$t('pages.openTimes1', { times: row.openTimes })}，`
      msg += row.useDays == 0 ? `${this.$t('pages.useDays')}，` : `${this.$t('pages.useDays1', { days: row.useDays })}，`
      msg += (row.makeType == 0 ? `${this.$t('pages.makeType1')}，` : row.makeType == 1 ? `${this.$t('pages.makeType2')}，` : `${this.$t('pages.makeType3')}，`)
      msg += row.validateValue & 65536 ? `${this.$t('pages.validateValue1')}，` : ''
      msg += row.validateValue & 4194304 ? `${this.$t('pages.validateValue7')}，` : ''
      msg += row.validateValue & 262144 ? `${this.$t('pages.validateValue3')}，` : ''
      msg += row.validateValue & 524288 ? `${this.$t('pages.validateValue4')}，` : ''
      msg += row.validateValue & 1048576 ? `${this.$t('pages.validateValue5')}，` : ''
      msg += row.validateValue & 2097152 ? `${this.$t('pages.validateValue6')}，` : ''
      msg += row.validateValue & 8388608 ? `${this.$t('pages.validateValue2')}，` : ''
      msg += row.validateValue & 16777216 ? `${this.$t('pages.validateValue15')}，` : ''
      msg += row.ctrlValue & 128 ? `${this.$t('pages.ctrlValueList15')}，` : ''
      msg += row.ctrlValue & 256 ? `${this.$t('pages.ctrlValueList17')}，` : ''
      return msg.substring(0, msg.lastIndexOf('，'))
    },
    //  设置模板库名称和ids
    setTemplateIdsAndName() {
      const names = []
      for (let i = 0; i < this.elgData.length; i++) {
        names.push(this.elgData[i].name)
      }
      this.temp.names = names;
      this.temp.modelIds = this.$refs['templateListTable'] && this.$refs['templateListTable'].getIdsByList(this.elgData, 'id').join(',')
    },
    cancel() {
      this.loadTree()
      this.showDlg = false
    },
    formatterRemark(row, data) {
      const templateTemp = JSON.parse(JSON.stringify(row))
      Object.assign(templateTemp, {
        ctrlValueList: this.numToList(row.ctrlValue, 11),
        validateValueList: this.numToList(row.validateValue, 25)
      })
      if (templateTemp.openTimes == 0) {  // 如果次数为0，表示不限制
        // 界面输入框置灰，显示65535
        templateTemp.openTimes = 65535
        templateTemp.noLimitOpen = 1
      }
      if (templateTemp.useDays == 0) {  // 如果次数为0，表示不限制
        // 界面输入框置灰，显示65535
        templateTemp.useDays = 65535
        templateTemp.noLimitUse = 1
      }
      if (templateTemp.validateValueList.indexOf(131072) != -1) {
        templateTemp.validateValueList.splice(templateTemp.validateValueList.indexOf(131072), 1)
        if (templateTemp.validateValueList.indexOf(8388608) == -1) {
          templateTemp.validateValueList.push(8388608)
        }
        if (templateTemp.validateValueList.indexOf(16777216) == -1) {
          templateTemp.validateValueList.push(16777216)
        }
      }
      this.formatterData(templateTemp)
      return templateTemp.remark || row.remark
    }
  }
}
</script>
