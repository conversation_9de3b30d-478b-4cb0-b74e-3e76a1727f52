<template>
  <el-dropdown :placement="placement">
    <el-button :type="buttonStyle" size="mini">
      {{ btnLabel }}<i class="el-icon-arrow-down el-icon--right"></i>
    </el-button>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="(value, key) in toolOption" :key="key" :command="key">
        <common-downloader
          :name="value.includes('.')? value : value + '.exe'"
          :button-name="value"
          button-type="text"
          button-icon=""
          @download="file => handleDownload(key, file)"
        />
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { downloadTool, getToolMap } from '@/api/dataEncryption/encryption/fileOutgoing'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'DownloadTool',
  components: { CommonDownloader },
  props: {
    // 根据该值控制显示下载工具下拉框内容
    showType: { type: Number, default: 1 },
    placement: { type: String, default: 'bottom-end' },
    btnLabel: { type: String, default() { return this.$t('pages.downloadTool') } },
    buttonStyle: { type: String, default() { return '' } }
  },
  data() {
    return {
      toolOption: {},
      agreementTypeItems: {
        1: this.$t('pages.machineCodeAcquisitionTool'),
        // 2: '计算机码获取工具',
        3: this.$t('pages.softwareTools'),
        4: this.$t('pages.outgoingReader')
      },
      msiTypeItems: {
        8: this.$t('pages.msiMakeTool')
      },
      patchTypeItems: {
        9: '补丁下载工具包'
      },
      dlpPkgTypeItems: { // dlp相关安装包的下载
        1001: '',
        1002: ''
      }
    }
  },
  created() {
    this.loadToolOption()
  },
  methods: {
    loadToolOption() {
      if (this.showType === 1) {
        this.toolOption = this.agreementTypeItems
      } else if (this.showType === 2) {
        this.toolOption = this.msiTypeItems
      } else if (this.showType === 3) {
        getToolMap({ toolType: Object.keys(this.dlpPkgTypeItems).toString() }).then(resp => {
          if (resp.data) {
            this.toolOption = resp.data
          }
        })
      } else if (this.showType === 4) {
        // this.toolOption = this.patchTypeItems
        getToolMap({ toolType: Object.keys(this.patchTypeItems).toString() }).then(resp => {
          if (resp.data) {
            this.toolOption = resp.data
          }
        })
      }
    },
    handleDownload(agreementType, file) {
      downloadTool({ toolType: agreementType }, file)
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-dropdown {
    padding-left: 10px;
  }
  .el-dropdown-menu {
    .el-dropdown-menu__item {
      padding: 0;
      line-height: 0;
    }
    .download-executor {
      width: 100%;
      margin-left: 0;
      >>>.el-button {
        width: 100%;
        line-height: 36px;
        padding: 0 20px;
        text-align: left;
      }
    }
  }
</style>
