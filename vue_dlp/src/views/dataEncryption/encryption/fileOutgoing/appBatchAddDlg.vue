<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="appendToBody"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.addApp')"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @close="dialogClose"
    >
      <Form ref="appDataForm" :rules="rules" :model="appTemp" label-position="right" label-width="80px" :extra-width="{en: 60}">
        <div class="toolbar">
          <div style="display: inline-block; max-width: 210px;">
            <upload-dir v-if="osType==1 && !plugIsInWork" ref="uploadDir" :popover-height="235" :loading="fileSubmitting" style="display: inline-block;" @changeFile="changeFile" />
            <el-upload
              v-show="!plugIsInWork"
              ref="upload"
              name="uploadFile"
              action="1111"
              accept=".exe"
              :limit="1"
              :disabled="fileSubmitting"
              :show-file-list="false"
              :before-upload="beforeUpload"
              style="display: inline-block;"
            >
              <el-button type="primary" :loading="fileSubmitting" size="mini" style="margin: 0 0 5px 0">{{ $t('pages.uploadFile') }}</el-button>
            </el-upload>
            <!-- 打开窗口时，在进行心跳检测 -->
            <app-scan-plug ref="appScanPlug" :heartbeat-enable="dialogFormVisible" @plugIsInWork="(inWork) => plugIsInWork = inWork" @uploadEnd="appendFile"/>
          </div>
          <el-button v-if="osType==1" type="primary" size="mini" @click="showAppSelectDlg">
            {{ $t('pages.software_Msg16') }}
          </el-button>
          <el-button v-if="osType===1" :loading="fileSelecting" type="primary" size="mini" @click="showTermAppSelectDlg">
            {{ $t('pages.selectTerminalSoft') }}
          </el-button>
          <el-button type="primary" size="mini" @click="iniImportDlg">
            {{ $t('pages.software_Msg45') }}
          </el-button>
          <el-row v-if="fileSubmitting">
            <el-col :span="22">
              <el-progress type="line" :percentage="percentage"/>
            </el-col>
            <el-col :span="2">
              <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
            </el-col>
          </el-row>
        </div>
        <grid-table
          ref="fileList"
          :height="250"
          :multi-select="true"
          :show-pager="false"
          row-key="keyId"
          :default-sort="{ prop: 'keyId', sort: 'desc' }"
          :col-model="colModel"
          :row-datas="fileList"
          :is-clear-saved-selected-row-datas-change="false"
          style="margin-bottom: 5px;"
        />
        <FormItem :label="$t('pages.appType')" prop="classId">
          <tree-select
            ref="typeTree"
            v-model="appTemp.classId"
            :data="typeTreeData"
            node-key="dataId"
            :width="296"
            class="input-with-button"
            @change="parentTypeSelectChange"
          />
          <el-button :title="$t('pages.addType')" class="editBtn" @click="handleAppTypeCreate"><svg-icon icon-class="add" /></el-button>
        </FormItem>
        <FormItem v-if="verifyModelAble" :label="$t('table.flagList')" prop="flagList">
          <el-row>
            <el-col :span="24">
              <el-select v-model="appTemp.flagList" multiple clearable>
                <el-option v-for="item in verifyModelTreeData" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-tooltip v-if="verifyModelAble" class="item" effect="dark" :content="$t('pages.outgoingProcess_text10')" placement="top-end">
          <el-button type="primary" :loading="submitting" @click="createData">{{ $t('button.confirm') }}</el-button>
        </el-tooltip>
        <el-button v-else type="primary" :loading="submitting" @click="createData">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogClose">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 新增程序类别 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="i18nConcatText($t('pages.appType'), 'create')"
      :visible.sync="dialogAppTypeFormVisible"
      width="400px"
    >
      <Form ref="appTypeForm" :model="appTypeTemp" :rules="groupRules" label-position="right" label-width="80px" style="width: 300px; margin-left:20px;">
        <FormItem :label="$t('pages.appType')" prop="name">
          <el-input v-model="appTypeTemp.name" maxlength="30"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dialogAppTypeSubmitting" @click="createNode">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="appTypeCancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- ini导入 -->
    <el-dialog
      v-if="iniImportAble"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.importProcess')"
      :visible.sync="uploadVisible"
      width="400px"
    >
      <Form ref="uploadForm" :rules="iniRules" :model="iniTemp" label-position="right" label-width="80px" style="width: 340px;">
        <el-row>
          <el-col :span="21">
            <FormItem :label="$t('pages.iniFileName')" prop="fileName">
              <el-input v-model="iniTemp.fileName" readonly/>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-upload
              ref="upload"
              name="upload"
              action="1111"
              accept=".ini"
              :on-change="fileChange"
              :show-file-list="false"
              :file-list="iniFileList"
              :disabled="iniFileSubmitting"
              :auto-upload="false"
            >
              <el-button type="primary" icon="el-icon-upload" :loading="iniFileSubmitting"></el-button>
            </el-upload>
          </el-col>
        </el-row>
        <el-row v-if="iniFileSubmitting">
          <el-col :span="22">
            <el-progress type="line" :percentage="iniPercentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" :disabled="iniCanDisable" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
        <span style="color: #0c60a5;padding-top: 5px">
          {{ $t('text.prompt') }}：<br/>{{ '&nbsp;&nbsp;&nbsp;' + $t('pages.outgoingProcess_text13') }}<br/>
        </span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="importSubmitting" type="primary" @click="saveFile()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="uploadCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <app-select-dlg ref="appSelectDlg" :append-to-body="appendToBody" @select="appendFile"/>
    <terminal-soft ref="termAppSelectDlg" @select="selectTermFile"/>
  </div>
</template>
<script>
import { changeFiles, loopUploadFiles } from '@/api/behaviorManage/application/appGroup'
import { canSetPropertyMd5, createProcessVersion } from '@/utils/fingerprint'
import UploadDir from '@/components/UploadDir'
import axios from 'axios'
import {
  addAppType,
  addProcess,
  getAppTypeByName,
  getGroupTree,
  importFromExe,
  importProcessReturnList,
  updateProcess
} from '@/api/dataEncryption/encryption/outgoingProcess';
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import AppScanPlug from '@/views/system/baseData/appGroup/appScanPlug'
import TerminalSoft from '@/views/dataEncryption/encryption/processStgLib/TerminalSoft'

export default {
  name: 'AppBatchAddDlg',
  components: { TerminalSoft, AppScanPlug, UploadDir, AppSelectDlg },
  props: {
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false },
    //  是否显示验证码方式
    verifyModelAble: {
      type: Boolean,
      default: true
    },
    rulesGroupLabel: { type: String, default() { return this.$t('pages.validateMsg_enterName') } },
    //  是否支持ini文件导入
    iniImportAble: { type: Boolean, default: false }
  },
  data() {
    return {
      colModel: [
        { prop: 'keyId', label: 'keyId', width: '100', sort: true },
        { prop: 'processName', label: 'processName', width: '120', sort: true },
        { prop: 'remark', label: 'remark', width: '150', sort: true, type: 'input', maxlength: 30, editMode: true },
        { prop: 'fileMd5', label: 'fileMd5', width: '150', sort: true },
        { prop: 'softSign', label: 'softSign', width: '150', sort: true }
      ],
      appTemp: {},
      defaultAppTemp: { // 表单字段
        id: null,
        classId: null,  //  程序类别
        remark: '',
        processName: '',
        productName: '',
        productVersion: '',
        originalFilename: '',
        fileDesc: '',
        companyName: '',
        internalName: '',
        legalCopyright: '',
        fileMd5: '',
        quicklyMd5: '',
        softSign: '',
        flagList: []
      },
      fileList: [],
      dialogFormVisible: false,
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        classId: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        flagList: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      },
      submitting: false,
      fileSubmitting: false,
      fileLimitSize: 1024,
      percentage: 0,
      source: null,
      checkTypeIds: [],
      checkModelIds: [],
      typeTreeData: [], //  程序类型数据
      verifyModelTreeData: [
        { label: this.$t('table.softwareName'), value: 1 },
        { label: this.$t('table.softSign'), value: 2 },
        { label: this.$t('table.fileMd5'), value: 4 }
      ],
      dialogAppTypeFormVisible: false,
      dialogAppTypeSubmitting: false,
      groupRules: {
        name: [
          { required: true, message: this.rulesGroupLabel, trigger: 'blur' },
          { validator: this.appTypeNameValidator, trigger: 'blur' }
        ]
      },
      appTypeTemp: {},
      defaultAppTypeTemp: { // 表单字段
        id: undefined,
        parentId: 0,
        name: ''
      },
      //  ini文件导入相关属性
      iniRules: {
        fileName: { required: true, message: this.$t('pages.chooseFile'), trigger: 'blur' }
      },
      iniTemp: {}, // 表单字段
      defaultIniTemp: {
        id: undefined,
        fileName: ''
      },
      iniFileList: [],
      iniFileSubmitting: false,
      uploadVisible: false,
      iniPercentage: 0,
      iniCanDisable: false,
      importSubmitting: false,
      plugIsInWork: false,
      fileSelecting: false
    }
  },
  computed: {
  },
  watch: {
    'fileList.length'(val) {
      this.$nextTick(() => {
        this.$refs.fileList && this.$refs.fileList.toggleAllSelection()
      })
    }
  },
  created() {
    this.resetTemp()
    this.loadTypeTree()
  },
  methods: {
    resetTemp() {
      this.appTemp = Object.assign({}, this.defaultAppTemp)
      this.appTemp.osType = this.osType
      this.fileList.splice(0)
    },
    showAppSelectDlg() {
      this.$refs['appSelectDlg'].show()
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('button.confirm'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      loopUploadFiles([file], this, false)
      return false // 屏蔽了action的默认上传
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    changeFile(files) {
      changeFiles(files, this, false)
    },
    appendFile(softs) {
      let maxKeyId = this.getFileListMaxKeyId()
      softs.forEach(item => {
        const index = this.fileList.findIndex(item2 => {
          return item2.fileMd5 == item.fileMd5
        })
        if (index == -1) {
          item.keyId = ++maxKeyId;
          this.fileList.push(item)
        }
      })
    },
    handleDrag() {
    },
    show(rowData) {
      this.resetTemp()
      this.loadTypeTree()
      this.dialogFormVisible = true
      this.appTemp.classId = null
      this.checkTypeIds.splice(0)
      this.$nextTick(() => {
        if (rowData && rowData.classId) {
          this.appTemp.classId = rowData.classId
          this.checkTypeIds.push(rowData.classId)
          this.$refs.typeTree.checkSelectedNode(this.checkTypeIds)
        }
        this.$refs['appDataForm'].clearValidate()
        this.$refs.appScanPlug.startPlug()
      })
    },
    parentTypeSelectChange(data) {
      this.appTemp.classId = data
      this.$refs['appDataForm'].clearValidate()
    },
    handleAppTypeCreate() {
      this.dialogAppTypeFormVisible = true
      this.appTypeTemp = Object.assign({}, this.defaultAppTypeTemp)
    },
    createData() {
      this.submitting = true
      this.appTypeTemp.name = ''
      const datas = this.$refs.fileList.getSelectedDatas()
      if (datas.length == 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text7'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      let flag = false
      for (let i = 0; i < datas.length; i++) {
        const process = datas[i]
        if (this.appTemp.checkMd5 == 1 && this.appTemp.md5Level == 3 && !canSetPropertyMd5(process)) {
          flag = true
          break
        }
        if (this.appTemp.checkMd5 == 1 && this.appTemp.md5Level == 4 && (process.productName == '' || process.productName == null)) {
          flag = true
          break
        }
      }
      this.submitting = false
      if (flag) {
        this.$confirmBox(this.appTemp.md5Level == 4 ? this.$t('pages.appGroup_text26') : this.$t('pages.appGroup_text6'), this.$t('text.prompt')).then(() => {
          this.saveProcess(datas)
        }).catch(() => {
          this.submitting = false
        })
      } else {
        this.saveProcess(datas)
      }
    },
    saveProcess(datas) {
      this.submitting = true
      this.$refs['appDataForm'].validate((valid) => {
        if (valid) {
          this.appTemp.processList = datas
          this.save()
        } else {
          this.submitting = false
        }
      })
    },
    toFingerprintData(appData) {
      const obj = {
        processName: appData.processName,
        processVersion: createProcessVersion(appData.processName, appData.productVersion),
        componentVersion: appData.productVersion,
        propertyMd5: appData.propertyMd5,
        fileMd5: appData.fileMd5,
        quicklyMd5: appData.quicklyMd5,
        checkMd5: this.appTemp.md5Level,
        propertyMark: appData.propertyMark,
        productMd5: appData.productMd5,
        remark: ''
      }
      return obj
    },
    save() {
      this.appTemp.processList.forEach(item => {
        item.relaFingerPrint = [this.toFingerprintData(item)]
      })
      const _this = this
      importFromExe(this.appTemp).then(respond => {
        //  设置验证方式
        respond.data.forEach(item => { item.flagList = this.appTemp.flagList })
        _this.$emit('submitEnd', respond.data, 'create')
        this.resetTemp()
        this.submitting = false
        this.dialogFormVisible = false
        this.$refs.appScanPlug.closePlug()
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
      }).catch(() => {
        this.submitting = false
      })
    },
    suffixValidator(rule, value, callback) {
      if (value == '*.*') {
        callback(new Error(this.$t('pages.appGroup_text5')))
      } else {
        callback()
      }
    },
    createNode() {
      this.dialogAppTypeSubmitting = true
      this.$refs['appTypeForm'].validate((valid) => {
        if (valid) {
          this.dialogAppTypeSubmitting = true
          const tempData = Object.assign({}, this.appTypeTemp)
          addAppType(tempData).then(respond => {
            this.dialogAppTypeSubmitting = false
            this.dialogAppTypeFormVisible = false
            this.appTypeTemp = Object.assign(this.defaultAppTypeTemp)
            this.loadTypeTree().then(res => {
              this.appTemp.classId = respond.data.id + ''
              this.$nextTick(() => {
                this.$refs['typeTree'].checkSelectedNode([this.appTemp.classId])
                this.$refs['appDataForm'].clearValidate()
              })
            })
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.$emit('createTypeAfter')
          }).catch(res => {
            this.dialogAppTypeSubmitting = false
          })
        } else {
          this.dialogAppTypeSubmitting = false
        }
      })
    },
    appTypeCancel() {
      this.dialogAppTypeFormVisible = false
      this.appTypeTemp = Object.assign(this.defaultAppTypeTemp)
    },
    //  判断程序类型名称是否已存在
    appTypeNameValidator(rule, value, callback) {
      getAppTypeByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.appTypeTemp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    // 更新软件类别树的数据
    loadTypeTree: function() {
      return getGroupTree().then(respond => {
        this.typeTreeData = respond.data
      })
    },
    //  新增进程
    createApp() {
      this.submitting = true
      this.$refs['appDataForm'].validate((valid) => {
        if (valid) {
          addProcess(this.appTemp).then(res => {
            this.submitting = false
            this.appFormVisible = false
            this.$refs.softTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    //  更新进程
    updateApp() {
      this.submitting = true
      this.$refs['appDataForm'].validate((valid) => {
        if (valid) {
          updateProcess(this.appTemp).then(res => {
            this.submitting = false
            this.appFormVisible = false
            this.$refs.softTable.execRowDataApi()
            this.temp.softList.forEach(soft => {
              if (soft.id == this.appTemp.id) {
                soft.remark = this.appTemp.remark
              }
            })
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    //  关闭弹出
    dialogClose() {
      this.dialogFormVisible = false
      this.resetTemp()
      this.$refs.typeTree && this.$refs.typeTree.clearSelectedNode();
      this.$refs.appScanPlug.closePlug()
    },
    //  ini文件导入
    iniImportDlg() {
      this.iniTemp = Object.assign({}, this.defaultIniTemp)
      this.uploadVisible = true
    },
    fileChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          message: this.$t('pages.outgoingProcess_text6'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const fileName = file.name
      const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      if (ext == 'ini') {
        this.iniTemp.fileName = fileName
        this.iniFileList.splice(0, 1, file)
      } else {
        this.$message({
          message: this.$t('pages.outgoingProcess_text7'),
          type: 'error',
          duration: 2000
        })
        return false
      }
    },
    saveFile() {
      this.importSubmitting = true
      this.iniFileSubmitting = true
      this.iniPercentage = 0
      this.iniCanDisable = false
      this.$refs['uploadForm'].validate((valid) => {
        if (valid) {
          const onUploadProgress = (progressEvent) => {
            const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
            this.iniPercentage = parseInt(percent)
            if (this.iniPercentage == 100) {
              // 如果上传进度到了100，则后台已经在处理保存数据了，因此不允许取消了
              this.iniCanDisable = true
            }
          }
          this.source = this.connectionSource()
          // 这个是上传会话token，取消上传操作需要的参数
          const cacheToken = this.source.token
          // 通过 FormData 对象上传文件
          const fd = this.toFormData(this.iniTemp)
          fd.append('uploadFile', this.iniFileList[0].raw)
          // 发起请求
          importProcessReturnList(fd, onUploadProgress, cacheToken).then(res => {
            this.importSubmitting = false
            this.iniFileSubmitting = false
            this.iniPercentage = 0
            this.uploadCancel()
            this.addSortTableData(res.data)
            this.appendFile(res.data)
          }).catch(res => {
            this.importSubmitting = false
            this.iniFileSubmitting = false
            this.iniPercentage = 0
          })
        } else {
          this.importSubmitting = false
          this.iniFileSubmitting = false
          this.iniPercentage = 0
        }
      })
    },
    uploadCancel() {
      this.uploadVisible = false
      this.cancel()
    },
    addSortTableData(needAddProcessList) {
      let maxKeyId = this.getFileListMaxKeyId()
      needAddProcessList = JSON.parse(JSON.stringify(needAddProcessList)) || []
      const tempArray = JSON.parse(JSON.stringify(this.fileList))
      needAddProcessList.forEach(item => {
        //   过滤掉已经存在的进程
        if (tempArray.find(i => i.processName === item.processName && i.fileMd5 === item.fileMd5 && i.softSign === item.softSign) === undefined) {
          item.keyId = ++maxKeyId
          this.fileList.push(item)
        }
      })
    },
    getFileListMaxKeyId() {
      if (this.fileList.length === 0) {
        return 0;
      }
      return this.fileList[this.fileList.length - 1].keyId || 0;
    },
    showTermAppSelectDlg() {
      this.$refs['termAppSelectDlg'].show()
    },
    selectTermFile(data) {
      this.fileSelecting = true
      // 如果传输完毕
      if (data.allNum == data.hasLoadNum) {
        this.fileSelecting = false
      }
      if (data && data.softList && data.softList.length > 0) {
        this.appendFile(data.softList, false)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.toolbar>div {
  vertical-align: top;
}
</style>
