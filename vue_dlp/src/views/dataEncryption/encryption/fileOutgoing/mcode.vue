<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <download-tool></download-tool>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :selectable="selectable"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 720px; margin-left:20px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" maxlength="100" show-word-limit :disabled="!formable"></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable"></el-switch>
        </FormItem>
        <div style="display: flex;align-items: center;">
          <data-editor
            ref="dataEditor"
            :formable="formable"
            :popover-width="680"
            :updateable="processEditable"
            :deletable="processDeleteable"
            :add-func="createCode"
            :update-func="updateCode"
            :delete-func="deleteCode"
            :cancel-func="resetConfigTemp"
            :before-update="beforeUpdateCode"
          >
            <Form ref="configForm" :rules="configRules" :model="configTemp" label-position="right" label-width="90px">
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('table.customName')" prop="name">
                    <el-input v-model="configTemp.name" resize="none" maxlength="30"></el-input>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('table.mcCode')" prop="code">
                    <el-input v-model="configTemp.code" resize="none" maxlength="8" @input="configTemp.code=configTemp.code.replace(/[^\d]/g,'')"></el-input>
                  </FormItem>
                </el-col>
              </el-row>
              <el-row>
                <el-col v-if="configBtnMode != 'update'" :span="11">
                  <FormItem :label="$t('pages.outgoingMachineCode_Msg2')" prop="classId">
                    <el-select v-model="configTemp.classId" filterable :placeholder="$t('text.select')">
                      <el-option v-for="item in typeTreeData" :key="item.id" :label="item.label" :value="item.dataId"/>
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col v-if="configBtnMode != 'update'" :span="1">
                  <el-button style="margin-left: 5px;margin-top: 2px;" @click="handleCreateGroup()">
                    <svg-icon icon-class="add" />
                  </el-button>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('table.remark')" prop="remark">
                    <el-input v-model="configTemp.remark" resize="none" maxlength="16"></el-input>
                  </FormItem>
                </el-col>
              </el-row>
            </Form>
          </data-editor>
          <el-button size="small" style="margin-left: 7px;" @click="handleWhiteListAppImport">{{ $t('pages.machineCodeWhiteListLibraryImport') }}</el-button>
        </div>
        <grid-table
          ref="processList"
          :height="230"
          :multi-select="true"
          :show-pager="false"
          :col-model="configColModel"
          :row-datas="configRowData"
          @selectionChangeEnd="processSelectionChange"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="showDlg=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importOutgoingCodeWhiteListStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <!-- 机器码白名单新增、修改 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="machineCodeWhiteListFormTitle"
      :visible.sync="dialogMachineCodeWhiteListFormVisible"
      width="600px"
      top="25vh"
    >
      <Form ref="machineCodeWhiteListForm" :rules="machineCodeWhiteListRules" :model="machineCodeWhiteListForm" label-position="right" label-width="100px" >
        <FormItem :label="$t('pages.szMachine')" prop="machineCode">
          <el-input v-model="machineCodeWhiteListForm.machineCode" :maxlength="8"/>
        </FormItem>
        <FormItem :label="$t('pages.szUserName')" prop="customer">
          <el-input v-model="machineCodeWhiteListForm.customer" :maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('pages.outgoingMachineCode_Msg2')" prop="classId">
          <el-select v-model="machineCodeWhiteListForm.classId" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in typeTreeData" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="machineCodeWhiteListForm.remark" :maxlength="32"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="machineCodeWWhiteListSubmitting" type="primary" @click="machineCodeWhiteListOperation == 'edit' ? updateMachineCodeWhiteList() : createMachineCodeWhiteList();">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogMachineCodeWhiteListFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-table-dlg
      ref="appImportTable"
      group-node-key="classId"
      :elg-title="$t('pages.outgoingMachineCode_Msg1')"
      :group-root-name="$t('pages.outgoingMachineCode_Msg7')"
      :search-info-name="$t('pages.outgoingMachineCode_Msg3')"
      :confirm-button-name="$t('pages.outgoingMachineCode_Msg4')"
      :prompt-message="$t('pages.outgoingMachineCode_Msg5')"
      :group-title="$t('pages.outgoingMachineCode_Msg2')"
      :group-label="$t('pages.outgoingMachineCode_Msg2')"
      :rules-group-label="$t('pages.outgoingMachineCode_Msg6')"
      :col-model="importColModel"
      :list="getMachineCodeInfoList"
      :load-group-tree="getGroupTree"
      :create-group="createGroup"
      :update-group="updateGroup"
      :delete-group="deleteGroup"
      :count-by-group="countByGroupId"
      :get-group-by-name="getGroupByName"
      :delete="deleteMachineCodeInfo"
      :get-list-by-group-ids="getListByGroupIds"
      :handle-create-elg="handleMachineCodeWhiteListCreate"
      @groupChange="groupChange"
      @submitEnd="refreshMachineCodeInfo"
      @changeGroupAfter="changeGroupAfter"
    />
    <edit-group-dlg
      ref="createGroupDlg"
      :append-to-body="true"
      :title="$t('pages.outgoingMachineCode_Msg2')"
      :group-tree-data="typeTreeData"
      :edit-valid-func="getGroupByName"
      :add-func="createGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>

<script>
import { getCodePage, createCode, updateCode, deleteCode, getCodeByName } from '@/api/dataEncryption/encryption/fileOutgoing'
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import { getGroupTree, getMachineCodeWhiteListPage,
  deleteGroup, addGroup, updateGroup, getGroupByName, deleteMachineCode,
  countInfoByGroupId, listMachineCodeInfo, machineCodeWhiteListExist, addMachineCodeWhiteList, updateMachineCodeWhiteList
} from '@/api/dataEncryption/encryption/outgoingMachineCodeWhiteList'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'

export default {
  name: 'MachineCodeWhiteList',
  components: { DownloadTool, ImportStg, ImportTableDlg, EditGroupDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 101,
      showTree: true,
      treeable: true,
      addBtnAble: false,
      deleteable: false,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'codes', label: 'mcCode', width: '200', formatter: this.processNameFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      configColModel: [ // 新增界面中使用
        { prop: 'name', label: 'customName', width: '150', sort: true },
        { prop: 'code', label: 'mcCode', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '150', sort: true }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        codes: undefined
      },
      configBtnMode: '',
      configTemp: {},
      configRowData: [],
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      configRules: {
        name: [{ required: true, message: this.$t('pages.machineCode_text1'), trigger: 'blur' }],
        code: [
          { required: true, message: this.$t('pages.machineCode_text2'), trigger: 'blur' },
          { validator: this.processNameValidator, trigger: 'blur' },
          { min: 8, max: 8, message: this.$t('pages.machineCode_text3'), trigger: 'blur' }
        ],
        classId: [
          { required: true, message: this.$t('text.select') + this.$t('pages.outgoingMachineCode_Msg2'), trigger: 'change' }
        ]
      },
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      processEditable: false,
      processDeleteable: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.outgoingCodeWhiteList'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.outgoingCodeWhiteList'), 'create')
      },
      typeTreeData: [],
      importColModel: [
        { prop: 'machineCode', label: 'szMachine', width: '100' },
        { prop: 'customer', label: 'szUserName', width: '100' },
        { prop: 'classId', label: 'sourceGroup', width: '100', formatter: this.classIdFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '180', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleMachineCodeWhiteListUpdate }
          ]
        }
      ],
      machineCodeWhiteListRules: {
        machineCode: [
          // { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szMachine'), trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.machineCodeValidator }
        ],
        customer: [
          { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szUserName'), trigger: 'blur' }
        ],
        classId: [
          { required: true, message: this.$t('text.select') + this.$t('pages.outgoingMachineCode_Msg2'), trigger: 'change' }
        ]
      },
      machineCodeWhiteListFormTitle: '',
      dialogMachineCodeWhiteListFormVisible: false,
      machineCodeWhiteListForm: {},
      machineCodeWhiteListOperation: '',
      machineCodeWWhiteListSubmitting: false,
      importDlgGroupId: ''  // 机器码白名单导入的当前分组id
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    getGroupTree,
    createGroupAddEnd(row) {
      this.loadTypeTree();
      this.configTemp.classId = row.id
    },
    handleCreateGroup() {
      this.loadTypeTree()
      this.$refs['createGroupDlg'].handleCreate();
    },
    updateMachineCodeWhiteList() {
      this.$refs['machineCodeWhiteListForm'].validate(valid => {
        if (valid) {
          this.machineCodeWhiteListSubmitting = true
          updateMachineCodeWhiteList(this.machineCodeWhiteListForm).then(response => {
            this.machineCodeWhiteListSubmitting = false
            this.dialogMachineCodeWhiteListFormVisible = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            this.$refs['appImportTable'].refreshTableData()
          }).catch(() => {
            this.machineCodeWhiteListSubmitting = false
          })
        }
      })
    },
    createMachineCodeWhiteList() {
      this.$refs['machineCodeWhiteListForm'].validate(valid => {
        if (valid) {
          this.machineCodeWhiteListSubmitting = true
          addMachineCodeWhiteList(this.machineCodeWhiteListForm).then(resp => {
            this.machineCodeWhiteListSubmitting = false
            this.dialogMachineCodeWhiteListFormVisible = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
            this.$refs['appImportTable'].refreshTableData()
          }).catch(() => {
            this.machineCodeWhiteListSubmitting = false
          })
        }
      })
    },
    deleteMachineCodeInfo(data) {
      return deleteMachineCode(data.ids);
    },
    handleWhiteListAppImport() {
      this.loadTypeTree()
      this.$refs.appImportTable.show()
    },
    loadTypeTree() {
      getGroupTree().then(res => {
        (res.data || []).forEach(item => {
          item.dataId = parseInt(item.dataId) || null
        });
        this.typeTreeData = res.data
      })
    },
    getListByGroupIds(data) {
      if (data.groupIds) {
        return listMachineCodeInfo({ classIds: data.groupIds })
      }
      return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
    },
    groupChange(data) {
      if (data) {
        this.importDlgGroupId = Number.parseInt(data)
      }
    },
    refreshMachineCodeInfo(data) {
      if (data && data.length != 0) {
        listMachineCodeInfo({ ids: data.join(',') }).then(resp => {
          const machineCodes = []
          this.configRowData.forEach(item => {
            machineCodes.push(item.code)
          })
          var tableData = []
          tableData = Object.assign([], this.configRowData)
          resp.data.forEach(item => {
            if (machineCodes.indexOf(item.machineCode) === -1) {
              // 在表格中不存在，加入
              const obj = {
                id: item.id,
                code: item.machineCode,
                name: item.customer,
                remark: item.remark,
                classId: item.classId
              }
              tableData.push(obj)
            }
          })
          tableData.forEach((item, index) => {
            item.id = index
          })
          this.configRowData = [...tableData]
        })
      }
    },
    getMachineCodeInfoList(data) {
      return getMachineCodeWhiteListPage(data)
    },
    changeGroupAfter() {
      this.loadTypeTree();
    },
    createGroup(data) {
      return addGroup(data);
    },
    updateGroup(data) {
      return updateGroup(data);
    },
    deleteGroup(data) {
      return deleteGroup(data.id);
    },
    countByGroupId(data) {
      return countInfoByGroupId(data);
    },
    getGroupByName(data) {
      return getGroupByName(data);
    },
    handleMachineCodeWhiteListUpdate(row) {
      this.machineCodeWhiteListOperation = 'edit'
      this.machineCodeWhiteListFormTitle = this.$t('button.edit') + this.$t('pages.cloudOutfileMachinaCode')
      this.dialogMachineCodeWhiteListFormVisible = true;
      this.machineCodeWhiteListForm = Object.assign({}, row)
      this.$nextTick(() => {
        this.$refs['machineCodeWhiteListForm'].clearValidate()
      })
    },
    handleMachineCodeWhiteListCreate() {
      this.machineCodeWhiteListOperation = 'add'
      this.machineCodeWhiteListFormTitle = this.$t('button.add') + this.$t('pages.cloudOutfileMachinaCode')
      this.machineCodeWhiteListForm = {}
      if (this.importDlgGroupId && this.importDlgGroupId != '') {
        this.$set(this.machineCodeWhiteListForm, 'classId', this.importDlgGroupId)
      }
      this.dialogMachineCodeWhiteListFormVisible = true
      this.$nextTick(() => {
        this.$refs['machineCodeWhiteListForm'].clearValidate()
      })
    },
    machineCodeValidator(rule, value, callback) {
      if (this.machineCodeWhiteListForm.machineCode === undefined || this.machineCodeWhiteListForm.machineCode === null || this.machineCodeWhiteListForm.machineCode === '') {
        callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szMachine')))
      } else {
        const machineCodePattern = /^\d{8}$/;
        if (!machineCodePattern.test(this.machineCodeWhiteListForm.machineCode)) {
          callback(new Error(this.$t('pages.machineCode_text6')))
        }
        var obj = Object.assign({}, this.machineCodeWhiteListForm)
        machineCodeWhiteListExist(obj).then(resp => {
          if (resp.data) {
            // 存在
            callback(new Error(this.$t('pages.cloudOutfile_Msg23')))
          } else {
            // 不存在
            callback()
          }
        })
      }
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      enableStgBtn(null, this)
      this.handleFilter()
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getCodePage(searchQuery)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.resetConfigTemp()
      this.configRowData = []
    },
    resetConfigTemp() {
      this.configBtnMode = 'add'
      this.configTemp = {
        processName: undefined,
        code: '',
        classId: ''
      }
      const table = this.$refs['processList']
      if (table) {
        table.setCurrentRow()
      }
      this.$nextTick(() => {
        this.$refs['configForm'].clearValidate()
      })
    },
    processSelectionChange(rowDatas) {
      this.processDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.processEditable = true
      } else {
        this.processEditable = false
        this.resetConfigTemp()
      }
      this.$refs['configForm'] && this.$refs['configForm'].clearValidate()
    },
    createCode() {
      let validate
      this.$refs['configForm'].validate((valid) => {
        if (valid) {
          const obj = {
            machineCode: this.configTemp.code,
            customer: this.configTemp.name,
            classId: this.configTemp.classId,
            remark: this.configTemp.remark ? this.configTemp.remark : ''
          }
          this.configRowData.unshift(Object.assign({}, this.configTemp, { id: new Date().getTime() }))
          this.resetConfigTemp()
          validate = valid
          addMachineCodeWhiteList(obj).then(resp => {})
          this.$refs.dataEditor && this.$refs.dataEditor.hiddlenTool()
        }
      })
      return validate
    },
    beforeUpdateCode() {
      this.configBtnMode = 'update'
      this.configTemp = Object.assign({}, this.$refs['processList'].getSelectedDatas()[0])
    },
    updateCode() {
      let validate
      this.$refs['configForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.configTemp)
          for (let i = 0, size = this.configRowData.length; i < size; i++) {
            const data = this.configRowData[i]
            if (rowData.id === data.id) {
              this.configRowData.splice(i, 1, rowData)
              break
            }
          }
          this.resetConfigTemp()
          this.$refs.dataEditor && this.$refs.dataEditor.hiddlenTool()
          validate = valid
        }
      })
      return validate
    },
    deleteCode() {
      const table = this.$refs['processList']
      const toDeleteIds = table.getSelectedIds()
      this.configRowData.splice(0, this.configRowData.length, ...table.deleteRowData(toDeleteIds))
      this.resetConfigTemp()
    },
    handleCreate() {
      this.loadTypeTree()
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.groupId = this.query.groupId
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      if (this.temp.codes) {
        this.temp.codes.forEach((conf, index) => {
          this.configRowData.push(Object.assign({}, conf, { id: index, code: conf.code + '' }))
        })
      }
      this.temp.code = '' + this.temp.code
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteCode({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    formatFrom() {
      const config = []
      this.configRowData.forEach(row => {
        delete row.id
        // row.code = Number.parseInt(row.code)
        config.push(row)
      })
      this.temp.codes = config
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFrom()
          createCode(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFrom()
          const tempData = Object.assign({}, this.temp)
          updateCode(tempData).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    classIdFormatter(row, data) {
      let msg = ''
      if (this.typeTreeData) {
        this.typeTreeData.some(node => {
          if (node.dataId == data) {
            msg = node.label
            return true
          }
        })
      }
      return msg
    },
    nameValidator(rule, value, callback) {
      getCodeByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    processNameValidator(rule, value, callback) {
      if (isNaN(value) || value.indexOf('.') != -1) {
        callback(new Error(this.$t('pages.machineCode_text5')))
      }
      // 兼容旧策略数据，旧策略数据不存在于机器码白名单库
      let exist = false
      for (let i = 0, size = this.configRowData.length; i < size; i++) {
        const rowData = this.configRowData[i]
        if (rowData.code === value && (this.configBtnMode === 'add' || rowData.id !== this.configTemp.id)) {
          exist = true
          break
        }
      }
      if (exist) {
        callback(new Error(this.$t('pages.machineCode_text4')))
      }
      if (this.configBtnMode === 'add') {
        // 只有新增才会加入机器码白名单库里，所以只有新增才需要去判断库里是否有该机器码了
        machineCodeWhiteListExist({ machineCode: value }).then(resp => {
          if (resp.data) {
            // 存在
            callback(new Error(this.$t('pages.machineCode_text4')))
          } else {
            // 不存在
            callback()
          }
        })
      } else {
        callback()
      }
    },
    processNameFormatter(row, data) {
      let result = ''
      if (data) {
        data.forEach(config => {
          result += config.name + '(' + config.code + ')；'
        })
      }
      return result
    },
    groupFormatter(row, data) {
      return this.processStg.label
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>
