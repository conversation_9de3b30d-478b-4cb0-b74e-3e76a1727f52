<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="otherConfigList"
        row-key="id"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :selectable="selectable"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="850px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px" style="width: 750px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" ></el-switch>
        </FormItem>
        <FormItem>
          <el-checkbox v-model="temp.allowMake" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('table.allowMake') }}</el-checkbox>
          <el-checkbox v-model="temp.allowConfigDisplay" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('table.allowConfigDisplay') }}</el-checkbox>
        </FormItem>

        <!--<FormItem label="允许设置阅读的最多次数" label-width="175">
          <el-row>
            <el-col :span="4">
              <el-input v-model="temp.readTimes" :disabled="notReadTimes" maxlength="5"></el-input>
            </el-col>
            <el-col :span="8" style="padding-left: 5px;">
              <el-checkbox v-model="notReadTimes" label="不限制" @change="changeNotReadTimes"></el-checkbox>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem label="允许设置阅读的最大天数" label-width="175">
          <el-row>
            <el-col :span="4">
              <el-input v-model="temp.readDays" :disabled="notReadDays" maxlength="5"></el-input>
            </el-col>
            <el-col :span="8" style="padding-left: 5px;">
              <el-checkbox v-model="notReadDays" label="不限制" @change="changeNotReadDays"></el-checkbox>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem label="允许制作的外发文件类型" label-width="175">
          <el-row>
            <el-col :span="5">
              <el-select v-model="temp.suffix">
                <el-option value="*.*" label="所有类型"></el-option>
                <el-option value=".ldm" label=".ldm"></el-option>
                <el-option value=".exe" label=".exe"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </FormItem>
        <el-checkbox-group v-model="enableCodes" style="border-top: 2px solid #aaa;padding-top: 10px;margin-top: 10px;">
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="1">只允许制作需要联网验证的外发文件</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="2">只允许制作需要验证机器码的外发文件</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="4">只允许制作单台电脑打开的外发文件</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="8">只允许制作由信任软件打开的外发文件</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
        </el-checkbox-group>
        <el-checkbox-group v-model="pwdCodes" style="border-top: 2px solid #aaa;padding-top: 10px;margin-top: 10px;">
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="1">打开外发文件需要密码</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="2">回收外发文件需要密码</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
        </el-checkbox-group>
        <el-checkbox-group v-model="disableCodes" style="border-top: 2px solid #aaa;padding-top: 10px;margin-top: 10px;">
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="1">不允许制作可修改的外发文件</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="2">不允许制作可打印的外发文件</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem label-width="1">
            <el-row>
              <el-col :span="12">
                <el-checkbox :label="4">不允许制作可截屏的外发文件</el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-checkbox :label="8">不允许制作过期自动删除的外发文件</el-checkbox>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem label-width="1">
            <el-checkbox :label="16">不允许制作打开前提示权限信息的外发文件</el-checkbox>
          </FormItem>
        </el-checkbox-group>-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="showDlg=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importOutgoingConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { getConfigPage, createConfig, updateConfig, deleteConfig, getConfigByName } from '@/api/dataEncryption/encryption/fileOutgoing'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'FileOutgoingConfig',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 105,
      showTree: true,
      treeable: true,
      addBtnAble: false,
      deleteable: false,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'allowMake', label: 'allowMake', width: '200', formatter: this.allowFormatter },
        { prop: 'allowConfigDisplay', label: 'allowConfigDisplay', width: '200', formatter: this.allowConfigDisplayFormatter },
        /* { prop: 'readTimes', label: '最多次数（允许设置阅读的）', width: '130', formatter: this.readTimesFormatter },
        { prop: 'readDays', label: '最大天数（允许设置阅读的）', width: '130', formatter: this.readTimesFormatter },
        { prop: 'suffix', label: '外发文件类型（允许制作的）', width: '130', formatter: this.suffixFormatter },
        { prop: 'enableCode', label: '允许制作的外发文件', width: '130', formatter: this.enableCodeFormatter },
        { prop: 'pwdCode', label: '外发文件需要密码', width: '130', formatter: this.pwdCodeFormatter },
        { prop: 'disableCode', label: '不允许制作的外发文件', width: '130', formatter: this.disableCodeFormatter },*/
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', formatter: (row) => buttonFormatter(row, this), click: this.handleUpdate }
          ]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        allowMake: 1,
        allowConfigDisplay: 0
        /* readTimes: 0,
        readDays: 0,
        suffix: '*.*',
        enableCode: undefined,
        disableCode: undefined,
        pwdCode: undefined*/
      },
      notReadTimes: true,
      notReadDays: true,
      enableCodes: [],
      disableCodes: [],
      pwdCodes: [],
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      query: {
        page: 1,
        searchInfo: ''
      },
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      isUpdateFormMode: false,
      processEditable: false,
      processDeleteable: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.advancedSettingsStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.advancedSettingsStg'), 'create')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['otherConfigList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      enableStgBtn(null, this)
      this.handleFilter()
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getConfigPage(searchQuery)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.enableCodes.splice(0)
      this.disableCodes.splice(0)
      this.pwdCodes.splice(0)
      this.notReadTimes = true
      this.notReadDays = true
    },
    changeNotReadTimes(value) {
      if (value) this.temp.readTimes = 0
    },
    changeNotReadDays(value) {
      if (value) this.temp.readDays = 0
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      Object.assign(this.temp, JSON.parse(JSON.stringify(row)))
      if (this.temp.enableCode) this.enableCodes = this.numToList(this.temp.enableCode, 4)
      if (this.temp.disableCode) this.disableCodes = this.numToList(this.temp.disableCode, 5)
      if (this.temp.pwdCode) this.pwdCodes = this.numToList(this.temp.pwdCode, 2)
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteConfig({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    formatFrom() {
      this.temp.enableCode = this.getSum(this.enableCodes)
      this.temp.disableCode = this.getSum(this.disableCodes)
      this.temp.pwdCode = this.getSum(this.pwdCodes)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFrom()
          createConfig(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFrom()
          const tempData = Object.assign({}, this.temp)
          updateConfig(tempData).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      getConfigByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    processNameFormatter(row, data) {
      const result = ''
      return result
    },
    readTimesFormatter(row, data) {
      return data == 0 ? this.$t('pages.noLimit') : data
    },
    allowFormatter(row, data) {
      return data == 0 ? this.$t('pages.notAllow') : this.$t('pages.allow')
    },
    allowConfigDisplayFormatter(row, data) {
      return data == 1 ? this.$t('pages.allow') : this.$t('pages.notAllow')
    },
    suffixFormatter(row, data) {
      return data === '*.*' ? this.$t('pages.allType') : data
    },
    enableCodeFormatter(row, data) {
      let result = ''
      const codes = this.numToList(data, 4)
      if (codes.indexOf(1) >= 0) result += `${this.$t('pages.enableCodeFormatter1')}；`
      if (codes.indexOf(2) >= 0) result += `${this.$t('pages.enableCodeFormatter2')}；`
      if (codes.indexOf(4) >= 0) result += `${this.$t('pages.enableCodeFormatter3')}；`
      if (codes.indexOf(8) >= 0) result += `${this.$t('pages.enableCodeFormatter4')}；`
      return result
    },
    disableCodeFormatter(row, data) {
      let result = ''
      const codes = this.numToList(data, 5)
      if (codes.indexOf(1) >= 0) result += `${this.$t('pages.enableCodeFormatter1')}；`
      if (codes.indexOf(2) >= 0) result += `${this.$t('pages.enableCodeFormatter2')}；`
      if (codes.indexOf(4) >= 0) result += `${this.$t('pages.enableCodeFormatter3')}；`
      if (codes.indexOf(8) >= 0) result += `${this.$t('pages.enableCodeFormatter4')}；`
      if (codes.indexOf(16) >= 0) result += `${this.$t('pages.enableCodeFormatte51')}；`
      return result
    },
    pwdCodeFormatter(row, data) {
      let result = ''
      const codes = this.numToList(data, 2)
      if (codes.indexOf(1) >= 0) result += `${this.$t('pages.pwdCodeFormatter1')}；`
      if (codes.indexOf(2) >= 0) result += `${this.$t('pages.pwdCodeFormatter2')}；`
      return result
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>
