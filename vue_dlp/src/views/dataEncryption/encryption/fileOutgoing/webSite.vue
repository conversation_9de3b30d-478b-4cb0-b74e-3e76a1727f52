<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <!--        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">-->
        <!--          {{ $t('button.addStrategy') }}-->
        <!--        </el-button>-->
        <!--        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">-->
        <!--          {{ $t('button.deleteStrategy') }}-->
        <!--        </el-button>-->
      </div>
      <grid-table
        ref="webSiteTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 500px; margin-left:20px;">
        <FormItem :label="$t('table.webSite')" prop="webSite">
          <el-input v-model="temp.webSite" maxlength="200"></el-input>
        </FormItem>
        <FormItem :label="$t('table.priority')" prop="priority">
          <el-input-number v-model="temp.priority" :min="1" :step="1" :max="99999999" step-strictly/>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" maxlength="20"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="showDlg=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWebSitePage, createWebSite, updateWebSite, deleteWebSite, getByPriority, getMaxPriority } from '@/api/dataEncryption/encryption/fileOutgoing'
export default {
  name: 'WebSite',
  data() {
    return {
      deleteable: false,
      colModel: [
        { prop: 'webSite', label: 'webSite', width: '150' },
        { prop: 'priority', label: 'priority', width: '100' },
        { prop: 'remark', label: 'remark', width: '150' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', click: this.handleUpdate/*, formatter: this.buttonFormatter*/ }
          ]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        webSite: '',
        priority: 1,
        remark: ''
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        priority: [
          { validator: this.priorityValidator, trigger: 'blur' }
        ]
      },
      query: {},
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      textMap: {
        update: this.$t('pages.webSite_update'),
        create: this.$t('pages.webSite_create')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['webSiteTable']
    }
  },
  watch: {

  },
  created() {

  },
  methods: {
    selectable(row, index) {
      return row.id != 1
    },
    buttonFormatter(row) {
      if (row.id === 1) {
        return ''
      } else {
        return this.$t('text.edit')
      }
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    rowDataApi: function(option) {
      return getWebSitePage(option)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      getMaxPriority().then(res => {
        this.temp.priority = res.data + 1
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDrag() {
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteWebSite({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createWebSite(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateWebSite(tempData).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    priorityValidator(rule, value, callback) {
      getByPriority({ priority: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('pages.webSite_text1')))
        } else {
          callback()
        }
      })
    }
  }
}
</script>
