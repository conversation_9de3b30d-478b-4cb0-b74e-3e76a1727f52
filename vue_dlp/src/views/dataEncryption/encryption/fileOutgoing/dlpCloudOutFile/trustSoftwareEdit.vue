<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.trustSoftwareManage')"
      :visible.sync="dialogTrustSoftwareVisible"
    >
      <div style="height: 100%;">
        <div class="toolbar">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreateTrustSoftware">
            {{ $t('button.add') }}
          </el-button>
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreateTrustSoftware">
            {{ $t('pages.accessSoftwareWhitelistLibraryImport') }}
          </el-button>
          <div class="searchCon">
            <el-input v-model="trustSoftwareQuery.szSoftName" v-trim clearable :placeholder="$t('pages.placeholderSzSoftName')" style="width: 200px;" @keyup.enter.native="handleTrustSoftwareFilter"></el-input>
            <el-input v-model="trustSoftwareQuery.szSoftSig" v-trim clearable :placeholder="$t('pages.placeholderSzSoftSig')" style="width: 200px;" @keyup.enter.native="handleTrustSoftwareFilter"></el-input>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleTrustSoftwareFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table ref="outfileTrustSoftwareList" :col-model="colModelTrustSoftware" :multi-select="false" :row-data-api="trustSoftwareRowDataApi" :height="420" />
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="trustSoftwareFormTitle"
      :visible.sync="dialogTrustSoftwareFormVisible"
      width="800px"
      top="25vh"
    >
      <div style="display: flex;align-items: center;">
        <upload-dir ref="uploadDir" :popover-height="180" :loading="fileSubmitting" style="display: inline-block;" @changeFile="changeFile" />
        <div style="margin-bottom: 5px;margin-left: 5px;">
          <el-upload
            ref="upload"
            name="uploadFile"
            action="1111"
            accept=".exe"
            :limit="1"
            :disabled="fileSubmitting"
            :show-file-list="false"
            :before-upload="beforeUpload"
            style="display: inline-block;"
          >
            <el-button type="primary" :loading="fileSubmitting" size="mini">{{ $t('pages.uploadFile') }}</el-button>
          </el-upload>
        </div>
        <el-button type="primary" size="mini" style="margin-left: 5px;" @click="showAppSelectDlg">
          {{ $t('pages.software_Msg16') }}
        </el-button>
        <el-button type="primary" size="mini" style="margin-left: 5px;" @click="iniImportDlg">
          {{ $t('pages.software_Msg45') }}
        </el-button>
      </div>
      <div>
        <el-row v-if="fileSubmitting">
          <el-col :span="22">
            <el-progress type="line" :percentage="percentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
      </div>

      <Form ref="trustSoftwareForm" :rules="trustSoftwareRules" :model="trustSoftwareForm" label-position="right" label-width="120px" >
        <el-row>
          <el-col :span="19" >
            <FormItem :label="$t('pages.szSoftName')" prop="szSoftName">
              <el-input v-model="trustSoftwareForm.szSoftName" :maxlength="64"/>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label-width="25px">
              <el-checkbox v-model="trustSoftwareForm.szSoftNameFlag">
                {{ $t('pages.szSoftNameFlag') }}
              </el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="19" >
            <FormItem :label="$t('pages.szSoftSig')" prop="szSoftSig">
              <el-input v-model="trustSoftwareForm.szSoftSig" :maxlength="128"/>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label-width="25px">
              <el-checkbox v-model="trustSoftwareForm.szSoftSigFlag">
                {{ $t('pages.szSoftSigFlag') }}
              </el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="19" >
            <FormItem :label="$t('pages.szSoftMd5')" prop="szSoftMd5">
              <el-input v-model="trustSoftwareForm.szSoftMd5" :maxlength="33"/>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label-width="25px">
              <el-checkbox v-model="trustSoftwareForm.szSoftMd5Flag" :maxlength="128">
                {{ $t('pages.szSoftMd5Flag') }}
              </el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.szDescriptor')" prop="szDescriptor">
          <el-input v-model="trustSoftwareForm.szDescriptor" type="textarea" rows="5" :maxlength="64"/>
        </FormItem>
        <FormItem v-if="trustSoftwareOperation === 'edit'" :label="$t('table.updateTime')" prop="updateTime">
          <el-input v-model="trustSoftwareUpdateTime" readonly/>
        </FormItem>
        <div v-if="trustSoftwareOperation === 'edit'" style="margin-top: 15px;">
          <span style="color: #0c60a5;">{{ $t('pages.cloudOutfile_Msg14') }}</span>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <download-tool button-style="primary" style="float: left;"></download-tool>
        <el-button :loading="trustSoftwareSubmitting" type="primary" @click="trustSoftwareOperation == 'edit' ? updateTrustSoftware() : createTrustSoftware();">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogTrustSoftwareFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- ini导入 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.importProcess')"
      :visible.sync="uploadVisible"
      width="400px"
    >
      <Form ref="uploadForm" :rules="iniRules" :model="iniTemp" label-position="right" label-width="80px" style="width: 340px;">
        <el-row>
          <el-col :span="21">
            <FormItem :label="$t('pages.iniFileName')" prop="fileName">
              <el-input v-model="iniTemp.fileName" readonly/>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-upload
              ref="upload"
              name="upload"
              action="1111"
              accept=".ini"
              :on-change="fileChange"
              :show-file-list="false"
              :file-list="iniFileList"
              :disabled="iniFileSubmitting"
              :auto-upload="false"
            >
              <el-button type="primary" icon="el-icon-upload" :loading="iniFileSubmitting"></el-button>
            </el-upload>
          </el-col>
        </el-row>
        <el-row v-if="iniFileSubmitting">
          <el-col :span="22">
            <el-progress type="line" :percentage="iniPercentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" :disabled="iniCanDisable" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
        <span style="color: #0c60a5;padding-top: 5px">
          {{ $t('text.prompt') }}：<br/>{{ '&nbsp;&nbsp;&nbsp;' + $t('pages.outgoingProcess_text13') }}<br/>
        </span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="importSubmitting" type="primary" @click="saveFile()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="uploadCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 选择多个软件信息后，只保留一个 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.cloudOutfile_Msg24')"
      :visible.sync="selectSoftVisible"
      width="700px"
    >
      <span style="color: #3296FA;">{{ $t('pages.cloudOutfile_Msg25') }}</span>
      <grid-table ref="selectSoftList" style="margin-top: 5px;" :col-model="colModelSelectSoft" :row-datas="selectSoftData" :show-pager="false" :height="220" />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmSelectSoft()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="selectSoftVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <app-select-dlg ref="appSelectDlg" :append-to-body="false" @select="multipleAppendFile"/>
  </div>
</template>
<script>
import {
  addCloudOutfileTrustSoftware,
  updateCloudOutfileTrustSoftware,
  getTrustSoftware,
  deleteCloudOutfileTrustSoftware
} from '@/api/dataEncryption/encryption/cloudOutfile'
import {
  listTrustSoftware
} from '@/api/dataEncryption/encryption/dlpCloudOutFile'
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool'
import { importProcessReturnList } from '@/api/dataEncryption/encryption/outgoingProcess';
import { upload } from '@/api/behaviorManage/application/appGroup'
import UploadDir from '@/components/UploadDir'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import axios from 'axios'

export default {
  name: 'TrustSoftwareEdit',
  components: { UploadDir, AppSelectDlg, DownloadTool },
  props: {
  },
  data() {
    return {
      dialogTrustSoftwareVisible: false,
      colModelTrustSoftware: [
        { prop: 'szSoftName', label: 'szSoftName', width: '100' },
        { prop: 'szSoftNameFlag', label: 'szSoftNameFlag', width: '100', formatter: this.checkFlagFormatter },
        { prop: 'szSoftSig', label: 'szSoftSig', width: '100' },
        { prop: 'szSoftSigFlag', label: 'szSoftSigFlag', width: '100', formatter: this.checkFlagFormatter },
        { prop: 'szSoftMd5', label: 'szSoftMd5', width: '100' },
        { prop: 'szSoftMd5Flag', label: 'szSoftMd5Flag', width: '100', formatter: this.checkFlagFormatter },
        { prop: 'szDescriptor', label: 'szDescriptor', width: '100' },
        { prop: 'updateTime', label: 'updateTime', width: '200', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleTrustSoftwareUpdate },
            { label: 'delete', click: this.handleTrustSoftwareDelete }
            // { label: 'outfileSyncData', click: this.handleSyncTrustSoftware }
          ]
        }
      ],
      trustSoftwareQuery: {
        uuid: '',
        szSoftName: '',
        szSoftSig: '',
        szSoftMd5: '',
        szDescriptor: ''
      },
      selectSoftVisible: false,
      source: null,
      iniFileSubmitting: false,
      iniFileList: [],
      uploadVisible: false,
      fileSubmitting: false,
      iniCanDisable: false,
      importSubmitting: false,
      trustSoftwareSubmitting: false,
      fileLimitSize: 1024,
      trustSoftwareFormTitle: '',
      trustSoftwareForm: {},
      trustSoftwareUpdateTime: ' ',
      percentage: 0,
      dialogTrustSoftwareFormVisible: false,
      trustSoftwareOperation: '',
      trustSoftwareId: '',
      currentUuid: '',
      iniPercentage: 0,
      selectSoftData: [],
      syncTrustSoftwareSubmitting: false,
      iniTemp: {}, // 表单字段
      defaultIniTemp: {
        id: undefined,
        fileName: ''
      },
      isVerifyModelAble: true,  //  是否有验证方式
      colModelSelectSoft: [
        { prop: 'szSoftName', label: 'szSoftName', width: '200' },
        { prop: 'szSoftSig', label: 'szSoftSig', width: '200' },
        { prop: 'szSoftMd5', label: 'szSoftMd5', width: '200' }
      ],
      iniRules: {
        fileName: { required: true, message: this.$t('pages.chooseFile'), trigger: 'blur' }
      },
      trustSoftwareRules: {
        szDescriptor: [
          { required: true, message: this.$t('pages.cloudOutfile_Msg26'), trigger: 'blur' }
        ],
        szSoftName: [
          {
            validator: (rule, value, callback) => {
              if (this.trustSoftwareForm.szSoftNameFlag && !this.trustSoftwareForm.szSoftName) {
                callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szSoftName')))
              }
              callback()
            }
          }],
        szSoftSig: [
          {
            validator: (rule, value, callback) => {
              if (this.trustSoftwareForm.szSoftSigFlag && !this.trustSoftwareForm.szSoftSig) {
                callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szSoftSig')))
              }
              callback()
            }
          }],
        szSoftMd5: [
          {
            validator: (rule, value, callback) => {
              if (this.trustSoftwareForm.szSoftMd5Flag && !this.trustSoftwareForm.szSoftMd5) {
                callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szSoftMd5')))
              }
              callback()
            }
          }]
      }
    }
  },
  methods: {
    trustSoftwareRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.trustSoftwareQuery, option)
      return listTrustSoftware(searchQuery)
    },
    handleTrustSoftwareFilter() {
      this.trustSoftwareQuery.page = 1
      this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
    },
    handleCreateTrustSoftware() {
      this.show('add', this.currentUuid)
    },
    checkFlagFormatter(row, data) {
      if (data == true) {
        return this.$t('text.yes')
      }
      return this.$t('text.no')
    },
    handleTrustSoftwareDelete(row) {
      const formData = {
        id: row.refId,
        szSoftName: row.szSoftName,
        szSoftSig: row.szSoftSig,
        szSoftMd5: row.szSoftMd5,
        szDescriptor: row.szDescriptor,
        dwCheckFlags: row.dwCheckFlags
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteCloudOutfileTrustSoftware(formData).then(response => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          this.trustSoftwareQuery.page = 1
          this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
        })
      })
    },
    handleTrustSoftwareUpdate(row) {
      getTrustSoftware(row.refId).then(resp => {
        this.show('edit', this.currentUuid, resp.data)
      })
    },
    saveFile() {
      this.importSubmitting = true
      this.iniFileSubmitting = true
      this.iniPercentage = 0
      this.iniCanDisable = false
      this.$refs['uploadForm'].validate((valid) => {
        if (valid) {
          const onUploadProgress = (progressEvent) => {
            const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
            this.iniPercentage = parseInt(percent)
            if (this.iniPercentage == 100) {
              // 如果上传进度到了100，则后台已经在处理保存数据了，因此不允许取消了
              this.iniCanDisable = true
            }
          }
          this.source = this.connectionSource()
          // 这个是上传会话token，取消上传操作需要的参数
          const cacheToken = this.source.token
          // 通过 FormData 对象上传文件
          const fd = this.toFormData(this.iniTemp)
          fd.append('uploadFile', this.iniFileList[0].raw)
          // 发起请求
          importProcessReturnList(fd, onUploadProgress, cacheToken).then(res => {
            this.importSubmitting = false
            this.iniFileSubmitting = false
            this.iniPercentage = 0
            this.uploadCancel()
            this.multipleAppendFile(res.data)
          }).catch(res => {
            this.importSubmitting = false
            this.iniFileSubmitting = false
            this.iniPercentage = 0
          })
        } else {
          this.importSubmitting = false
          this.iniFileSubmitting = false
          this.iniPercentage = 0
        }
      })
    },
    uploadCancel() {
      this.uploadVisible = false
      this.cancel()
    },
    iniImportDlg() {
      this.iniTemp = Object.assign({}, this.defaultIniTemp)
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs['uploadForm'].clearValidate()
      })
    },
    fileChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          message: this.$t('pages.outgoingProcess_text6'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const fileName = file.name
      const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      if (ext == 'ini') {
        this.iniTemp.fileName = fileName
        this.iniFileList.splice(0, 1, file)
      } else {
        this.$message({
          message: this.$t('pages.outgoingProcess_text7'),
          type: 'error',
          duration: 2000
        })
        return false
      }
    },
    confirmSelectSoft() {
      const data = this.$refs.selectSoftList.getSelectedDatas()
      if (data.length === 0) {
        this.$message({
          duration: 2000,
          type: 'error',
          message: this.$t('pages.cloudOutfile_Msg27')
        })
        return
      }
      if (data.length > 1) {
        this.$message({
          duration: 2000,
          type: 'error',
          message: this.$t('pages.cloudOutfile_Msg25')
        })
        return
      }
      this.$set(this.trustSoftwareForm, 'szSoftName', data[0].szSoftName)
      this.$set(this.trustSoftwareForm, 'szSoftSig', data[0].szSoftSig)
      this.$set(this.trustSoftwareForm, 'szSoftMd5', data[0].szSoftMd5)
      this.selectSoftVisible = false
    },
    showAppSelectDlg() {
      this.$refs['appSelectDlg'].show()
    },
    changeFile(files) {
      const fd = new FormData()
      for (let i = 0; i < files.length; i++) {
        fd.append('uploadFile', files[i])// 传文件
      }
      if (files.length > 0) {
        this.uploadFile(fd, 'multiple')
      } else {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text8'),
          type: 'error',
          duration: 2000
        })
      }
      this.$refs.uploadDir.clearValue()
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('button.confirm'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      const fd = new FormData()
      fd.append('uploadFile', file)// 传文件
      this.uploadFile(fd, 'single')
      return false // 屏蔽了action的默认上传
    },
    uploadFile(formData, type) {
      this.fileSubmitting = true
      this.percentage = 0
      // 上传钩子，用来获取进度条
      const onUploadProgress = (progressEvent) => {
        const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
        this.percentage = parseInt(percent)
      }
      this.source = this.connectionSource()
      const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
      // 调用后台接口获取进程windows属性
      upload(formData, onUploadProgress, cacheToken).then(res => {
        this.resetUploadComponent()
        type === 'single' ? this.appendFile(res.data) : this.multipleAppendFile(res.data)
      }).catch(res => {
        this.resetUploadComponent()
        if (axios.isCancel(res)) {
          // 取消上传后的操作，待补充
        }
      })
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    multipleAppendFile(softs) {
      if (softs.length === 1) {
        this.appendFile(softs)
      } else {
        this.resetTrustSoftwareForm()
        this.selectSoftData = []
        softs.forEach((soft, index) => {
          const obj = {
            id: index,
            szSoftName: '',
            szSoftSig: '',
            szSoftMd5: ''
          }
          if (soft.productName) {
            const productName = soft.productName.length > 64 ? soft.productName.substring(0, 64) : soft.productName
            obj.szSoftName = productName
          }
          if (soft.softSign) {
            const softSign = soft.softSign.length > 128 ? soft.softSign.substring(0, 128) : soft.softSign
            obj.szSoftSig = softSign
          }
          if (soft.fileMd5) {
            const fileMd5 = soft.fileMd5.length > 33 ? soft.fileMd5.substring(0, 33) : soft.fileMd5
            obj.szSoftMd5 = fileMd5
          }
          this.selectSoftData.push(obj)
        })
        this.selectSoftVisible = true
      }
    },
    appendFile(softs) {
      this.resetTrustSoftwareForm()
      softs.forEach(soft => {
        if (soft.productName) {
          const productName = soft.productName.length > 64 ? soft.productName.substring(0, 64) : soft.productName
          this.$set(this.trustSoftwareForm, 'szSoftName', productName)
        }
        if (soft.softSign) {
          const softSign = soft.softSign.length > 128 ? soft.softSign.substring(0, 128) : soft.softSign
          this.$set(this.trustSoftwareForm, 'szSoftSig', softSign)
        }
        if (soft.fileMd5) {
          const fileMd5 = soft.fileMd5.length > 33 ? soft.fileMd5.substring(0, 33) : soft.fileMd5
          this.$set(this.trustSoftwareForm, 'szSoftMd5', fileMd5)
        }
      })
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    showTrustSoftware(row) {
      this.currentUuid = row.uuid
      this.trustSoftwareQuery.uuid = row.uuid
      this.trustSoftwareQuery.page = 1
      this.trustSoftwareQuery.szSoftName = ''
      this.trustSoftwareQuery.szSoftSig = ''
      this.trustSoftwareQuery.szSoftMd5 = ''
      this.trustSoftwareQuery.szDescriptor = ''
      this.dialogTrustSoftwareVisible = true
      this.$nextTick(() => {
        this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
      })
    },
    show(ope, uuid, row) {
      this.trustSoftwareOperation = ope
      this.trustSoftwareForm = {}
      this.resetTrustSoftwareForm()
      this.currentUuid = uuid
      if (ope === 'add') {
        this.trustSoftwareFormTitle = this.$t('button.add') + this.$t('pages.cloudOutfileTrustSoftware')
      } else {
        this.trustSoftwareForm = Object.assign({}, row)
        this.trustSoftwareId = row.refId
        this.trustSoftwareUpdateTime = row.updateTime
        this.trustSoftwareFormTitle = this.$t('button.edit') + this.$t('pages.cloudOutfileTrustSoftware')
      }
      this.dialogTrustSoftwareFormVisible = true
      this.$nextTick(() => {
        this.$refs['trustSoftwareForm'].clearValidate()
      })
    },
    resetTrustSoftwareForm() {
      this.trustSoftwareForm = {
        szSoftNameFlag: false,
        szSoftSigFlag: false,
        szSoftMd5Flag: false
      }
    },
    refreshTrustSoftware() {
      getTrustSoftware(this.trustSoftwareId).then(resp => {
        this.trustSoftwareForm = {}
        this.resetTrustSoftwareForm()
        this.trustSoftwareForm = Object.assign({}, resp.data)
        this.trustSoftwareUpdateTime = resp.data.updateTime
        this.$nextTick(() => {
          this.$refs['trustSoftwareForm'].clearValidate()
        })
        this.$message({
          duration: 2000,
          message: this.$t('pages.cloudOutfile_Msg22'),
          type: 'success'
        })
      })
    },
    updateTrustSoftware() {
      this.$refs['trustSoftwareForm'].validate(valid => {
        if (valid) {
          const formData = Object.assign({ 'uuid': this.currentUuid }, this.trustSoftwareForm);
          formData.id = this.trustSoftwareId
          updateCloudOutfileTrustSoftware(formData).then(response => {
            this.trustSoftwareSubmitting = false
            this.dialogTrustSoftwareFormVisible = false
            this.resetTrustSoftwareForm()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            this.handleTrustSoftwareFilter()
          }).catch(err => {
            if (err.response.data.code === 601) {
              this.$confirmBox(this.$t('pages.cloudOutfile_Msg49'), this.$t('text.prompt')).then(() => {
                this.refreshTrustSoftware()
              })
            }
          })
        }
      })
    },
    createTrustSoftware() {
      this.$refs['trustSoftwareForm'].validate(valid => {
        if (valid) {
          const formData = Object.assign({ 'uuid': this.currentUuid }, this.trustSoftwareForm)
          addCloudOutfileTrustSoftware(formData).then(response => {
            this.trustSoftwareSubmitting = false;
            this.dialogTrustSoftwareFormVisible = false
            this.resetTrustSoftwareForm();
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
            this.handleTrustSoftwareFilter()
          })
        }
      })
    }
  }
}
</script>
