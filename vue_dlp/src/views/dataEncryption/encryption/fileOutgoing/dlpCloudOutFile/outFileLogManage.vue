<template>
  <div class="app-container">
    <!-- 组织架构树 -->
    <div class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['user']" @data-change="strategyTargetNodeChange" />
    </div>
    <!-- 查询框+数据列表 -->
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="searchCon">
          <el-date-picker
            v-model="query.dateRange"
            type="daterange"
            range-separator="-"
            value-format="yyyy-MM-dd"
            :start-placeholder="$t('pages.startDate')"
            :end-placeholder="$t('pages.endDate')"
            style="width:250px;margin-top: -3px;"
          >
          </el-date-picker>
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.placeholderSzSoftOutFileName')" style="width: 150px;"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="outfileLogList" :col-model="colModel" :row-data-api="rowDataApi" :multi-select="false"/>
    </div>
  </div>
</template>
<script>
import {
  getOutFileLogList
} from '@/api/dataEncryption/encryption/dlpCloudOutFile'
export default {
  name: 'OutFileLogManage',
  data() {
    return {
      colModel: [
        { prop: 'fileName', label: 'fileName', width: '100' },
        { prop: 'reqIp', label: 'reqIp', width: '80' },
        { prop: 'reqAddress', label: 'reqAddress', width: '60' },
        { prop: 'computerName', label: 'computerName', width: '100' },
        { prop: 'openIp', label: 'openIp', width: '80' },
        { prop: 'openMac', label: 'mac', width: '100' },
        { prop: 'reqTime', label: 'reqTime', width: '150', sort: true },
        { prop: 'ack', label: 'ack', width: '60', formatter: this.ackFormatter }
      ],
      showTree: true,
      query: {
        page: 1,
        dateRange: '',
        beginDate: '',
        endDate: '',
        name: ''
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['outfileLogList']
    }
  },
  created() {

  },
  methods: {
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    ackFormatter(row, data) {
      if (data == 1) {
        return this.$t('text.success')
      }
      if (data == 2) {
        return this.$t('text.expired')
      }
      if (data == 3) {
        return this.$t('text.timesExceed')
      }
      if (data == 4) {
        return this.$t('pages.needPointComputer')
      }
      return data;
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      console.log('searchQuery: ' + JSON.stringify(searchQuery))
      return getOutFileLogList(searchQuery)
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleSearch() {
      if (this.query.dateRange && this.query.dateRange.length == 2) {
        this.query.beginDate = this.query.dateRange[0] + ' 00:00:00'
        this.query.endDate = this.query.dateRange[1] + ' 23:59:59'
      } else {
        this.query.beginDate = ''
        this.query.endDate = ''
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    }
  }
}
</script>
<style>
</style>
