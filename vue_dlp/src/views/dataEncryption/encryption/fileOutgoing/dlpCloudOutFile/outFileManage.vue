<template>
  <div class="app-container">
    <!-- 组织架构树 -->
    <!-- <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" :showed-tree="['user']" @data-change="strategyTargetNodeChange"/> -->
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['user']" @data-change="strategyTargetNodeChange" />
    </div>
    <!-- 查询框+数据列表 -->
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button icon="el-icon-cloudy" :loading="pullSubmit" size="mini" @click="handlePull">
          {{ $t('pages.handleSync') }}
        </el-button>
        <el-button icon="el-icon-cloudy" size="mini" @click="handleOutFileCount">
          {{ $t('pages.cloudOutfile_Msg2') }}
        </el-button>
        <download-tool></download-tool>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.placeholderSzSoftOutFileName')" style="width: 150px;" @keyup.enter.native="handleFilter"></el-input>
          <el-date-picker
            v-model="query.dateRange"
            type="daterange"
            range-separator="-"
            value-format="yyyy-MM-dd"
            :start-placeholder="$t('pages.startDate')"
            :end-placeholder="$t('pages.endDate')"
            style="width:250px;margin-top: 3px;"
          >
          </el-date-picker>
          <el-button type="primary" icon="el-icon-search" size="mini" style="margin-left: 1px;" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="outfileList" :col-model="colModel" :row-data-api="rowDataApi" :multi-select="false" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <!-- 外发文件修改 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.cloudOutfile_Msg3')"
      :visible.sync="dialogEditVisible"
    >
      <Form ref="outfileForm" :rules="rules" :model="outfileForm" label-position="right" label-width="120px" >
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.startTime')" prop="beginDate">
              <el-date-picker v-model="outfileForm.beginDate" type="datetime" :disabled="outfileForm.beginDateFlag" value-format="yyyy-MM-dd HH:mm:ss" style="width: 75%;"></el-date-picker>
              <el-checkbox v-model="outfileForm.beginDateFlag">{{ $t('pages.notLimit') }}</el-checkbox>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.endTime')" prop="endDate">
              <el-date-picker v-model="outfileForm.endDate" type="datetime" :disabled="outfileForm.endDateFlag" value-format="yyyy-MM-dd HH:mm:ss" style="width: 75%;"></el-date-picker>
              <el-checkbox v-model="outfileForm.endDateFlag">{{ $t('pages.notLimit') }}</el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.cloudOutfile_Msg4')" prop="openTimes">
              <el-input-number v-model="outfileForm.openTimes" :precision="0" :max="65535" :min="0" :disabled="outfileForm.openTimesFlag" style="width: 75%;" @blur="openTimesBlur"/>
              <el-checkbox v-model="outfileForm.openTimesFlag">{{ $t('pages.noLimit') }}</el-checkbox>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.cloudOutfile_Msg5')">
              <el-input v-model="outfileForm.szPassword" :maxlength="40"/>
            </FormItem>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <FormItem :label="$t('pages.cloudOutfile_Msg6')" prop="computerCode">
              <el-input v-model="outfileForm.computerCode" :maxlength="32"/>
            </FormItem>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="24">
            <FormItem :label="$t('pages.cloudOutfile_Msg7')">
              <el-input v-model="outfileForm.strSignMsg" type="textarea" :rows="5" :maxlength="50"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.outfileStatus')">
              <el-radio-group v-model="outfileForm.status">
                <el-radio :label="1">{{ $t('table.enable') }}</el-radio>
                <el-radio :label="0">{{ $t('table.stop') }}</el-radio>
              </el-radio-group>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.cloudOutfile_Msg8')">
              <el-radio-group v-model="outfileForm.checkEndWarn">
                <el-radio :label="1">{{ $t('text.yes') }}</el-radio>
                <el-radio :label="0">{{ $t('text.no') }}</el-radio>
              </el-radio-group>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <FormItem :label="$t('pages.cloudOutfile_Msg9')">
            <el-col :span="8">
              <el-checkbox v-model="outfileForm.checkCc" :true-label="1" :false-label="0" @change="handleCheckCcChange">{{ $t('pages.cloudOutfile_Msg10') }}</el-checkbox>
            </el-col>
            <el-col :span="8">
              <div style="display: flex;align-items: center;">
                <el-checkbox v-model="outfileForm.checkMachineCode" :true-label="1" :false-label="0" @change="handleCheckMachineCodeChange">{{ $t('pages.cloudOutfile_Msg11') }}</el-checkbox>  
                <el-button :disabled="machineCodeManageDisabled" icon="el-icon-setting" style="border: none;" :title="$t('table.machineManage')" @click="machineManage"></el-button>
              </div>
            </el-col>
            <el-col :span="8">
              <div>
                <el-checkbox v-model="outfileForm.checkTrustSoft" :true-label="1" :false-label="0" @change="handleCheckTrustSoftwareChange">{{ $t('pages.cloudOutfile_Msg12') }}</el-checkbox>
                <el-button :disabled="trustSoftwareManageDisabled" icon="el-icon-setting" style="border: none;" :title="$t('table.trustSoftwareManage')" @click="trustSoftwareManage"></el-button>
              </div>
            </el-col>
          </FormItem>
        </el-row>
        <FormItem :label="$t('pages.cloudOutfile_Msg13')">
          <el-checkbox-group v-model="outfileForm.openRightList">
            <el-checkbox v-for="item in openRightOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <FormItem :label="$t('table.updateTime')" prop="updateTime">
          <el-input v-model="outFileUpdateTime" readonly/>
        </FormItem>
        <div style="margin-top: 15px;">
          <span style="color: #0c60a5;">{{ $t('pages.cloudOutfile_Msg14') }}</span>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="primary" style="float: left;" @click="refreshOutFile()">
          {{ $t('button.refresh') }}
        </el-button> -->
        <el-button :loading="submitting" type="primary" @click="updateOutfile()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogEditVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 外发记录追踪 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.outfileTrace')"
      width="1100px"
      :visible.sync="dialogRecordVisible"
    >
      <div style="height: 100%;">
        <div class="toolbar">
          <div class="searchCon">
            <el-date-picker
              v-model="recordQuery.dateRange"
              type="daterange"
              range-separator="-"
              value-format="yyyy-MM-dd"
              :start-placeholder="$t('pages.startDate')"
              :end-placeholder="$t('pages.endDate')"
              style="width:300px"
            >
            </el-date-picker>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleRecordFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table ref="outfileRecordList" :col-model="colModelRecord" :row-data-api="recordRowDataApi" :multi-select="false" :height="420" style="padding-top: 10px"/>
      </div>
    </el-dialog>
    <!-- 外发记录条数 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.cloudOutfile_Msg2')"
      width="700px"
      :visible.sync="outFileNumVisible"
    >
      <div style="height: 100%;">
        <div class="toolbar">
          <el-button icon="el-icon-cloudy" size="mini" @click="handleDelete">
            {{ $t('pages.cloudOutfile_Msg1') }}
          </el-button>
          <div class="searchCon">
            <el-date-picker
              v-model="ofNumQuery.dateRange"
              type="daterange"
              range-separator="-"
              value-format="yyyy-MM-dd"
              :start-placeholder="$t('pages.startDate')"
              :end-placeholder="$t('pages.endDate')"
              style="width:250px"
            >
            </el-date-picker>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="ofNumFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table ref="ofNumList" :col-model="colModelOfNum" :row-datas="ofNumData" :show-pager="false" :multi-select="false" :height="120"/>
      </div>
    </el-dialog>
    <!-- 信任软件添加/编辑 Dialog -->
    <!-- <trust-software-edit ref="trustSoftwareEdit"></trust-software-edit> -->
    <trust-software-manage ref="trustSoftwareEdit"></trust-software-manage>
    <!-- 机器码添加/编辑 Dialog -->
    <machine-code-edit ref="machineCodeEdit"></machine-code-edit>
  </div>
</template>

<script>
import axios from 'axios'
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool'
// import TrustSoftwareEdit from './trustSoftwareEdit.vue'
import TrustSoftwareManage from './trustSoftwareManage.vue'
import MachineCodeEdit from './machineCodeEdit.vue'
import {
  listOpenRightOptions,
  updateCloudOutfile,
  getOutFile,
  syncOutFile,
  getOutFileLogCount
} from '@/api/dataEncryption/encryption/cloudOutfile'
import {
  listCloudOutfile, listCloudOutfileRecord, delCloudOutFile, instantlySync
} from '@/api/dataEncryption/encryption/dlpCloudOutFile'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, entityLink, refreshPage } from '@/utils'
export default {
  name: 'OutFileManage',
  components: {
    DownloadTool, /* TrustSoftwareEdit, */TrustSoftwareManage,
    MachineCodeEdit
  },
  props: {
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'name', width: '100', sort: true },
        { prop: 'status', label: 'outfileStatus', width: '100', formatter: this.statusFormatter },
        { prop: 'staffName', label: 'staffName', width: '100' },
        { prop: 'createDate', label: 'createDate', width: '180', sort: true },
        { prop: 'openRightDesc', label: 'openRightDesc', width: '150' },
        { prop: 'openTimes', label: 'openTimes', width: '100', formatter: this.openTimesFormatter },
        { prop: 'beginDate', label: 'useBeginDate', width: '150', sort: true, formatter: this.beginDateFormatter },
        { prop: 'endDate', label: 'useEndDate', width: '150', sort: true, formatter: this.endDateFormatter },
        { prop: 'checkEndWarn', label: 'closeBeforeTip', width: '100', formatter: this.checkEndWarnFormatter },
        { prop: 'checkCc', label: 'checkComputerCode', width: '120', formatter: this.codeFormatter },
        { prop: 'computerCode', label: 'targetComputerCode', width: '120' },
        { prop: 'checkMachineCode', label: 'checkMachineCode', width: '120', formatter: this.machineCodeFormatter },
        { prop: 'checkTrustSoft', label: 'checkTrustSoft', width: '120', formatter: this.trustSoftFormatter },
        { prop: 'openedTimes', label: 'openedTimes', width: '120' },
        { prop: 'successOpenedTimes', label: 'successOpenedTimes', width: '120' },
        { label: 'operate', type: 'button', fixedWidth: '400', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            // { label: 'outfileSyncData', click: this.handleSyncOutFile },
            { label: 'outfileTrace', click: this.showRecordDialog },
            { label: 'machineManage', click: this.showMachineCodeDialog },
            { label: 'trustSoftwareManage', click: this.showTrustSoftwareDialog }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        fileName: '',
        dateRange: '',
        beginDate: '',
        endDate: ''
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      source: null,
      checkedEntityNode: {},
      currentUuid: '',
      // 外发记录参数
      colModelRecord: [
        { prop: 'fileName', label: 'fileName', width: '100' },
        { prop: 'reqIp', label: 'reqIp', width: '80' },
        { prop: 'reqAddress', label: 'reqAddress', width: '60' },
        { prop: 'computerName', label: 'computerName', width: '100' },
        { prop: 'openIp', label: 'openIp', width: '80' },
        { prop: 'openMac', label: 'mac', width: '100' },
        { prop: 'reqTime', label: 'reqTime', width: '150', sort: true },
        { prop: 'ack', label: 'ack', width: '60', formatter: this.ackFormatter }
      ],
      outFileUpdateTime: '',
      // 外发文件参数修改
      dialogEditVisible: false,
      outfileForm: {},
      dialogRecordVisible: false,
      outFileNumVisible: false,
      ofNumQuery: {
        dateRange: '',
        beginDate: '',
        endDate: ''
      },
      colModelOfNum: [
        { prop: 'num', label: 'outfileCount', width: '180' },
        { prop: 'minCreateDate', label: 'minCreateDate', width: '180' }
      ],
      ofNumData: [],
      recordQuery: {
        page: 1,
        uuid: '',
        dateRange: '',
        beginDate: '',
        endDate: ''
      },
      rules: {
        beginDate: [
          { validator: this.validateBeginDate, trigger: 'change' }
        ],
        endDate: [
          { validator: this.validateEndDate, trigger: 'change' }
        ],
        computerCode: [
          { required: true, message: this.$t('pages.cloudOutfile_Msg15'), trigger: 'blur' }
        ],
        openTimes: [
          { validator: this.validateOpenTimes, trigger: 'change' }
        ]
      },
      submitting: false,
      openRightOptions: [],
      machineCodeRules: {
        szMachine: [
          // { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szMachine'), trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.szMachineValidator }
        ],
        szUserName: [
          { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szUserName'), trigger: 'blur' }
        ]
      },
      machineCodeManageDisabled: false,
      trustSoftwareManageDisabled: false,
      pullSubmit: false,
      countQuery: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['outfileList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取外发权限列表
    this.listOpenRightOptions();
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  methods: {
    validateBeginDate(rule, value, callback) {
      if (!this.outfileForm.beginDateFlag && !value) {
        callback(new Error(this.$t('pages.cloudOutfile_Msg16')));
      } else {
        callback();
      }
    },
    validateEndDate(rule, value, callback) {
      if (!this.outfileForm.endDateFlag && !value) {
        callback(new Error(this.$t('pages.cloudOutfile_Msg17')));
      } else {
        callback();
      }
    },
    validateOpenTimes(rule, value, callback) {
      if (!this.outfileForm.openTimesFlag) {
        if (value === undefined || value === null || value === '') {
          callback(new Error(this.$t('pages.cloudOutfile_Msg18')));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    machineManage() {
      this.$refs.machineCodeEdit.show(this.outfileForm)
    },
    trustSoftwareManage() {
      this.$refs.trustSoftwareEdit.showTrustSoftware(this.outfileForm)
    },
    openTimesBlur() {
      if (this.outfileForm.openTimes != undefined || this.outfileForm.openTimes === null || this.outfileForm.openTimes === '') {
        this.$refs.outfileForm.clearValidate('openTimes')
      }
    },
    handleCheckCcChange(value) {
      if (value) {
        this.outfileForm.checkMachineCode = 0;
        this.machineCodeManageDisabled = true
      } else {
        if (this.outfileForm.checkMachineCode === 0) {
          this.machineCodeManageDisabled = true
        } else {
          this.machineCodeManageDisabled = false
        }
      }
    },
    handleCheckMachineCodeChange(value) {
      if (value) {
        this.machineCodeManageDisabled = false
        this.outfileForm.checkCc = 0;
      } else {
        this.machineCodeManageDisabled = true
      }
    },
    handleCheckTrustSoftwareChange(value) {
      if (value) {
        this.trustSoftwareManageDisabled = false
      } else {
        this.trustSoftwareManageDisabled = true
      }
    },
    refreshOutFile() {
      const originalUuid = this.outfileForm.uuid
      this.outfileForm = {}
      getOutFile(originalUuid.replace(/^\{(.*)\}$/, '$1')).then(resp => {
        this.outfileForm = Object.assign({ openRightList: [] }, resp.data)
        this.outFileUpdateTime = this.outfileForm.updateTime
        if (resp.data.openTimesFlag) {
          this.openTimes = 65535
        }
        this.$nextTick(() => {
          this.$refs['outfileForm'].clearValidate()
        })
        this.$notify({
          duration: 2000,
          message: this.$t('pages.cloudOutfile_Msg19'),
          type: 'success'
        })
      })
    },
    updateOutfile() {
      this.$refs['outfileForm'].validate(valid => {
        if (valid) {
          this.submitting = true
          if (this.outfileForm.checkCc === 1 && this.outfileForm.checkMachineCode === 1) {
            this.submitting = false
            this.$message({
              type: 'error',
              message: this.$t('pages.cloudOutfile_Msg50'),
              duration: 2000
            })
            return
          }
          const formData = Object.assign(this.outfileForm);
          updateCloudOutfile(formData).then(response => {
            this.dialogEditVisible = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            this.submitting = false
            this.query.page = 1
            this.$refs['outfileList'].execRowDataApi(this.query)
          }).catch(err => {
            this.submitting = false
            if (err.response.data.code === 601) {
              this.$confirmBox(this.$t('pages.cloudOutfile_Msg49'), this.$t('text.prompt')).then(() => {
                this.refreshOutFile()
              })
            }
          })
        }
      })
    },
    resetForm() {
      this.outfileForm = {}
    },

    handleSyncOutFile(row) {
      this.$confirmBox(this.$t(this.$t('pages.cloudOutfile_Msg20'), this.$t('text.prompt'))).then(() => {
        const originalUuid = row.uuid
        syncOutFile(originalUuid.replace(/^\{(.*)\}$/, '$1')).then(response => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('pages.cloudOutfile_Msg21'), type: 'success', duration: 2000 })
          this.handleFilter()
        })
      })
    },
    handleUpdate(row) {
      this.outfileForm = {}
      const originalUuid = row.uuid
      getOutFile(originalUuid.replace(/^\{(.*)\}$/, '$1')).then(resp => {
        this.outfileForm = Object.assign({ openRightList: [] }, resp.data)
        if (this.outfileForm.checkTrustSoft && this.outfileForm.checkTrustSoft === 1) {
          this.trustSoftwareManageDisabled = false
        } else {
          this.trustSoftwareManageDisabled = true
        }
        if (this.outfileForm.checkMachineCode && this.outfileForm.checkMachineCode === 1) {
          this.machineCodeManageDisabled = false
        } else {
          this.machineCodeManageDisabled = true
        }
        this.outFileUpdateTime = this.outfileForm.updateTime
        if (resp.data.openTimesFlag) {
          this.openTimes = 65535
        }
        this.dialogEditVisible = true;
        this.$nextTick(() => {
          this.$refs['outfileForm'].clearValidate()
        })
      })
    },
    getFormattedDate() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are zero-based
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleCreateTrustSoftware() {
      this.$refs.trustSoftwareEdit.show('add', this.currentUuid)
    },
    handlePull() {
      this.$confirmBox(this.$t('pages.syncConfirmMsg'), this.$t('text.prompt')).then(() => {
        this.pullSubmit = true
        instantlySync().then(resp => {
          this.$message({
            duration: 2000,
            type: 'success',
            message: this.$t('pages.syncSuccess')
          })
          this.pullSubmit = false
          this.gridTable.execRowDataApi(this.query)
        }).catch(() => {
          this.pullSubmit = false
        })
      }).catch(() => {})
      // this.pullDataVisible = true
    },
    handleOutFileCount() {
      this.ofNumQuery = {
        dateRange: '',
        beginDate: '',
        endDate: ''
      }
      this.countQuery = {
        beginDate: '',
        endDate: ''
      }
      getOutFileLogCount(this.countQuery).then(resp => {
        this.outFileNumVisible = true
        const numObj = {
          id: 1,
          num: resp.data.count,
          minCreateDate: resp.data.minCreateDate
        }
        this.ofNumData = []
        this.ofNumData.push(numObj)
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.deleteCloudOutFileData_Msg1'), this.$t('text.prompt')).then(() => {
        delCloudOutFile().then(resp => {
          this.$message({
            duration: 2000,
            type: 'success',
            message: this.$t('text.deleteSuccess')
          })
          this.ofNumFilter()
        })
      }).catch((e) => {
      })
    },
    pullOutFileData() {

    },
    listOpenRightOptions() {
      listOpenRightOptions().then(response => {
        this.openRightOptions = response.data;
      })
    },
    deleteData() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
      })
    },
    connectionSource() {
      return axios.CancelToken.source()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return listCloudOutfile(searchQuery)
    },
    recordRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.recordQuery, option)
      return listCloudOutfileRecord(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    refresh() {
      return refreshPage(this)
    },
    handleFilter() {
      if (this.query.dateRange && this.query.dateRange.length == 2) {
        this.query.beginDate = this.query.dateRange[0] + ' 00:00:00'
        this.query.endDate = this.query.dateRange[1] + ' 23:59:59'
      } else {
        this.query.beginDate = ''
        this.query.endDate = ''
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    ofNumFilter() {
      if (this.ofNumQuery.dateRange && this.ofNumQuery.dateRange.length == 2) {
        this.ofNumQuery.beginDate = this.ofNumQuery.dateRange[0] + ' 00:00:00'
        this.ofNumQuery.endDate = this.ofNumQuery.dateRange[1] + ' 23:59:59'
      } else {
        this.ofNumQuery.beginDate = ''
        this.ofNumQuery.endDate = ''
      }
      getOutFileLogCount(this.ofNumQuery).then(resp => {
        const numObj = {
          id: 1,
          num: resp.data.count,
          minCreateDate: resp.data.minCreateDate
        }
        this.ofNumData = []
        this.ofNumData.push(numObj)
      })
    },
    handleRecordFilter() {
      if (this.recordQuery.dateRange && this.recordQuery.dateRange.length == 2) {
        this.recordQuery.beginDate = this.recordQuery.dateRange[0] + ' 00:00:00'
        this.recordQuery.endDate = this.recordQuery.dateRange[1] + ' 23:59:59'
      } else {
        this.recordQuery.beginDate = ''
        this.recordQuery.endDate = ''
      }
      this.recordQuery.page = 1
      this.$refs['outfileRecordList'].execRowDataApi(this.recordQuery)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    showRecordDialog(row) {
      this.recordQuery.uuid = row.uuid
      this.recordQuery.page = 1
      this.recordQuery.dateRange = ''
      this.recordQuery.beginDate = ''
      this.recordQuery.endDate = ''
      this.dialogRecordVisible = true
      this.$nextTick(() => {
        this.$refs['outfileRecordList'].execRowDataApi(this.recordQuery)
      })
    },
    showMachineCodeDialog(row) {
      this.$refs.machineCodeEdit.show(row)
    },
    showTrustSoftwareDialog(row) {
      this.$refs.trustSoftwareEdit.showTrustSoftware(row)
    },
    statusFormatter(row, data) {
      if (data == 1) {
        return this.$t('table.enable')
      }
      return this.$t('table.stop')
    },
    codeFormatter(row, data) {
      if (data == 1) {
        return this.$t('text.yes')
      }
      return this.$t('text.no')
    },
    machineCodeFormatter(row, data) {
      if (data == 1) {
        return this.$t('text.yes')
      }
      return this.$t('text.no')
    },
    trustSoftFormatter(row, data) {
      if (data == 1) {
        return this.$t('text.yes')
      }
      return this.$t('text.no')
    },
    checkEndWarnFormatter(row, data) {
      if (data == 1) {
        return this.$t('text.yes')
      }
      return this.$t('text.no')
    },
    beginDateFormatter(row, data) {
      if (!data || data === '0001-01-01 00:00:00') {
        return this.$t('pages.noLimit')
      }
      return data;
    },
    endDateFormatter(row, data) {
      if (!data || data === '9999-12-31 23:59:59') {
        return this.$t('pages.noLimit')
      }
      return data;
    },
    openTimesFormatter(row, data) {
      if (data == 65535) {
        return this.$t('pages.noLimit')
      }
      return data;
    },
    ackFormatter(row, data) {
      if (data == 1) {
        return this.$t('text.success')
      }
      if (data == 2) {
        return this.$t('text.expired')
      }
      if (data == 3) {
        return this.$t('text.timesExceed')
      }
      if (data == 4) {
        return this.$t('pages.needPointComputer')
      }
      return data;
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
