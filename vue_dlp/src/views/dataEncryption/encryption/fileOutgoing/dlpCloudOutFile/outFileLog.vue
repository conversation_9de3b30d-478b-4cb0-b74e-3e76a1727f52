<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="handleRefresh">
          <!-- {{ $t('button.refresh') }} -->
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="searchCon">
          <TimeQuery v-model="query" :limit-day="0" />
          <span>
            {{ $t('pages.syncType') }}：
            <el-select v-model="query.syncType" style="width: 150px;" clearable>
              <el-option v-for="item in syncTypes" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
            {{ $t('pages.syncResult') }}：
            <el-select v-model="query.resultType" style="width: 150px;" clearable>
              <el-option v-for="item in resultTypes" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">
              {{ $t('table.search') }}
            </el-button>
          </span>
        </div>
      </div>
      <grid-table ref="outfileLogList" :col-model="colModel" :multi-select="false" :row-data-api="rowDataApi"/>
    </div>
  </div>
</template>
<script>
import {
  getSyncLogList
} from '@/api/dataEncryption/encryption/dlpCloudOutFile'
export default {
  name: 'OutFileLog',
  data() {
    return {
      colModel: [
        { prop: 'syncType', label: 'syncType', width: '100', formatter: this.syncTypeFormatter },
        { prop: 'syncTime', label: 'syncEndTime', width: '180', sort: true },
        { prop: 'resultType', label: 'syncResult', width: '80', formatter: this.resultTypeFormatter },
        { prop: 'reason', label: 'failReason', width: '180' },
        { prop: 'createTime', label: 'createTime', width: '180', sort: true }
      ],
      syncTypes: [
        {
          id: 0,
          label: this.$t('pages.autoSync')
        },
        {
          id: 1,
          label: this.$t('pages.manualSync')
        }
      ],
      resultTypes: [
        {
          id: 0,
          label: this.$t('text.success')
        },
        {
          id: 1,
          label: this.$t('text.fail')
        }
      ],
      query: {
        page: 1,
        syncType: null,
        resultType: null,
        isTimes: false,
        createDate: '',
        startDate: '',
        endDate: ''
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['outfileLogList']
    }
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getSyncLogList(searchQuery)
    },
    syncTypeFormatter: function(row, data) {
      if (data === 0) {
        return this.$t('pages.autoSync')
      } else {
        return this.$t('pages.manualSync')
      }
    },
    resultTypeFormatter: function(row, data) {
      if (data === 0) {
        return this.$t('text.success')
      } else {
        return this.$t('text.fail')
      }
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleSearch() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    }
  }
}
</script>
<style>
</style>
