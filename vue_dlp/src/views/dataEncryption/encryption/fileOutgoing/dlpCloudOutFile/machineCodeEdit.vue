<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.machineManage')"
      :visible.sync="dialogMachineCodeVisible"
    >
      <div style="height: 100%;">
        <div class="toolbar">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreateMachineCode">
            {{ $t('button.add') }}
          </el-button>
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleWhiteListAppImport">
            {{ $t('pages.machineCodeWhiteListLibraryImport') }}
          </el-button>
          <div class="searchCon">
            <el-input v-model="machineCodeQuery.searchInfo" v-trim clearable :placeholder="$t('pages.outgoingMachineCode_Msg3')" style="width: 200px;" @keyup.enter.native="handleMachineCodeFilter"></el-input>
            <!-- <el-input v-model="machineCodeQuery.szUserName" v-trim clearable :placeholder="$t('pages.placeholderSzUserName')" style="width: 200px;" @keyup.enter.native="handleMachineCodeFilter"></el-input> -->
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleMachineCodeFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table ref="outfileMachineCodeList" :col-model="colModelMachineCode" :row-data-api="machineCodeRowDataApi" :multi-select="false" :height="420" />
      </div>
    </el-dialog>
    <!-- 机器码添加/编辑 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="machineCodeFormTitle"
      :visible.sync="dialogMachineCodeFormVisible"
      width="600px"
      top="25vh"
    >
      <Form ref="machineCodeForm" :rules="machineCodeRules" :model="machineCodeForm" label-position="right" label-width="100px" >
        <FormItem :label="$t('pages.szMachine')" prop="szMachine">
          <el-input v-model="machineCodeForm.szMachine" :maxlength="8"/>
        </FormItem>
        <FormItem :label="$t('pages.szUserName')" prop="szUserName">
          <el-input v-model="machineCodeForm.szUserName" :maxlength="30"/>
        </FormItem>
        <FormItem v-if="machineCodeFormTitle === this.$t('button.edit') + this.$t('pages.cloudOutfileMachinaCode')" :label="$t('table.updateTime')" prop="updateTime">
          <el-input v-model="machineCodeUpdateTime" readonly/>
        </FormItem>
        <div v-if="machineCodeFormTitle === this.$t('button.edit') + this.$t('pages.cloudOutfileMachinaCode')" style="margin-top: 15px;">
          <span style="color: #0c60a5;">{{ $t('pages.cloudOutfile_Msg14') }}</span>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <download-tool button-style="primary" style="float: left;"></download-tool>
        <el-button :loading="machineCodeSubmitting" type="primary" @click="machineCodeOperation == 'edit' ? updateMachineCode() : createMachineCode();">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogMachineCodeFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 机器码白名单新增、修改 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="machineCodeWhiteListFormTitle"
      :visible.sync="dialogMachineCodeWhiteListFormVisible"
      width="600px"
      top="25vh"
    >
      <Form ref="machineCodeWhiteListForm" :rules="machineCodeWhiteListRules" :model="machineCodeWhiteListForm" label-position="right" label-width="100px" >
        <FormItem :label="$t('pages.szMachine')" prop="machineCode">
          <el-input v-model="machineCodeWhiteListForm.machineCode" :maxlength="8"/>
        </FormItem>
        <FormItem :label="$t('pages.szUserName')" prop="customer">
          <el-input v-model="machineCodeWhiteListForm.customer" :maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('pages.outgoingMachineCode_Msg2')" prop="classId">
          <el-select v-model="machineCodeWhiteListForm.classId" filterable :placeholder="$t('text.select')">
            <el-option v-for="item in typeTreeData" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="machineCodeWhiteListForm.remark" :maxlength="32"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="machineCodeWWhiteListSubmitting" type="primary" @click="machineCodeWhiteListOperation == 'edit' ? updateMachineCodeWhiteList() : createMachineCodeWhiteList();">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogMachineCodeWhiteListFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-table-dlg
      ref="appImportTable"
      group-node-key="classId"
      :elg-title="$t('pages.outgoingMachineCode_Msg1')"
      :group-root-name="$t('pages.outgoingMachineCode_Msg7')"
      :search-info-name="$t('pages.outgoingMachineCode_Msg3')"
      :confirm-button-name="$t('pages.outgoingMachineCode_Msg4')"
      :prompt-message="$t('pages.outgoingMachineCode_Msg5')"
      :group-title="$t('pages.outgoingMachineCode_Msg2')"
      :group-label="$t('pages.outgoingMachineCode_Msg2')"
      :rules-group-label="$t('pages.outgoingMachineCode_Msg6')"
      :col-model="importColModel"
      :list="getMachineCodeInfoList"
      :load-group-tree="getGroupTree"
      :create-group="createGroup"
      :update-group="updateGroup"
      :delete-group="deleteGroup"
      :count-by-group="countByGroupId"
      :get-group-by-name="getGroupByName"
      :delete="deleteMachineCodeInfo"
      :get-list-by-group-ids="getListByGroupIds"
      :handle-create-elg="handleMachineCodeWhiteListCreate"
      @groupChange="groupChange"
      @submitEnd="refreshMachineCodeInfo"
      @changeGroupAfter="changeGroupAfter"
    />
  </div>
</template>
<script>
import { getGroupTree, getMachineCodeWhiteListPage,
  deleteGroup, addGroup, updateGroup, getGroupByName, deleteMachineCode,
  countInfoByGroupId, listMachineCodeInfo, machineCodeWhiteListExist, addMachineCodeWhiteList, updateMachineCodeWhiteList
} from '@/api/dataEncryption/encryption/outgoingMachineCodeWhiteList'
import {
  addCloudOutfileMachineCode,
  updateCloudOutfileMachineCode,
  getMachineCode,
  syncMachineCode,
  deleteCloudOutfileMachineCode,
  batchAddCloudOutfileMachineCode
} from '@/api/dataEncryption/encryption/cloudOutfile'
import { listMachineCode, machineExist } from '@/api/dataEncryption/encryption/dlpCloudOutFile'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool'
export default {
  name: 'MachineCodeEdit',
  components: { ImportTableDlg, DownloadTool },
  data() {
    return {
      typeTreeData: [],
      machineCodeRules: {
        szMachine: [
          // { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szMachine'), trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.szMachineValidator }
        ],
        szUserName: [
          { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szUserName'), trigger: 'blur' }
        ]
      },
      machineCodeWhiteListRules: {
        machineCode: [
          // { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szMachine'), trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.machineCodeValidator }
        ],
        customer: [
          { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szUserName'), trigger: 'blur' }
        ],
        classId: [
          { required: true, message: this.$t('text.select') + this.$t('pages.outgoingMachineCode_Msg2'), trigger: 'change' }
        ]
      },
      machineCodeUpdateTime: '',  
      machineCodeFormTitle: '',
      machineCodeSubmitting: false,
      dialogMachineCodeVisible: false,
      dialogMachineCodeFormVisible: false,
      // 机器码增/改
      machineCodeOperation: '',
      currentUuid: '',
      machineCodeForm: {},
      machineCodeQuery: {
        uuid: '',
        // szMachine: '',
        // szUserName: ''
        searchInfo: ''
      },
      importColModel: [
        { prop: 'machineCode', label: 'szMachine', width: '100' },
        { prop: 'customer', label: 'szUserName', width: '100' },
        { prop: 'classId', label: 'sourceGroup', width: '100', formatter: this.classIdFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleMachineCodeWhiteListUpdate }
          ]
        }
      ],
      colModelMachineCode: [
        { prop: 'szMachine', label: 'szMachine', width: '100', sort: true },
        { prop: 'szUserName', label: 'szUserName', width: '100', sort: true },
        { prop: 'updateTime', label: 'updateTime', width: '150', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '180', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleMachineCodeUpdate },
            { label: 'delete', click: this.handleMachineCodeDelete }
            // { label: 'outfileSyncData', click: this.handleSyncMachineCode }
          ]
        }
      ],
      machineCodeWhiteListFormTitle: '',
      dialogMachineCodeWhiteListFormVisible: false,
      machineCodeWhiteListForm: {},
      machineCodeWhiteListOperation: '',
      machineCodeWWhiteListSubmitting: false,
      importDlgGroupId: ''
    }
  },
  methods: {
    getGroupTree,
    updateMachineCodeWhiteList() {
      this.$refs['machineCodeWhiteListForm'].validate(valid => {
        if (valid) {
          this.machineCodeWhiteListSubmitting = true
          updateMachineCodeWhiteList(this.machineCodeWhiteListForm).then(response => {
            this.machineCodeWhiteListSubmitting = false
            this.dialogMachineCodeWhiteListFormVisible = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            this.$refs['appImportTable'].refreshTableData()
          }).catch(() => {
            this.machineCodeWhiteListSubmitting = false
          })
        }
      })
    },
    createMachineCodeWhiteList() {
      this.$refs['machineCodeWhiteListForm'].validate(valid => {
        if (valid) {
          this.machineCodeWhiteListSubmitting = true
          addMachineCodeWhiteList(this.machineCodeWhiteListForm).then(resp => {
            this.machineCodeWhiteListSubmitting = false
            this.dialogMachineCodeWhiteListFormVisible = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
            this.$refs['appImportTable'].refreshTableData()
          }).catch(() => {
            this.machineCodeWhiteListSubmitting = false
          })
        }
      })
    },
    changeGroupAfter() {
      this.loadTypeTree();
    },
    groupChange(data) {
      if (data) {
        this.importDlgGroupId = Number.parseInt(data)
      }
    },
    refreshMachineCodeInfo(data) {
      const machineCodes = []
      listMachineCodeInfo({ ids: data.join(',') }).then(resp => {
        resp.data.forEach(item => {
          const obj = {
            szMachine: item.machineCode,
            szUserName: item.customer
          }
          machineCodes.push(obj)
        })
        if (machineCodes.length === 0) {
          return
        }
        const batchMachineCodeParam = {
          uuid: this.currentUuid,
          machineCodes: machineCodes
        }
        batchAddCloudOutfileMachineCode(batchMachineCodeParam).then(resp => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          this.machineCodeQuery.page = 1
          this.$refs.outfileMachineCodeList.execRowDataApi(this.machineCodeQuery)
        })
      })
    },
    getMachineCodeInfoList(data) {
      return getMachineCodeWhiteListPage(data)
    },
    handleMachineCodeWhiteListUpdate(row) {
      this.machineCodeWhiteListOperation = 'edit'
      this.machineCodeWhiteListFormTitle = this.$t('button.edit') + this.$t('pages.cloudOutfileMachinaCode')
      this.dialogMachineCodeWhiteListFormVisible = true;
      this.machineCodeWhiteListForm = Object.assign({}, row)
      this.$nextTick(() => {
        this.$refs['machineCodeWhiteListForm'].clearValidate()
      })
    },
    handleMachineCodeWhiteListCreate() {
      this.machineCodeWhiteListOperation = 'add'
      this.machineCodeWhiteListFormTitle = this.$t('button.add') + this.$t('pages.cloudOutfileMachinaCode')
      this.machineCodeWhiteListForm = {}
      if (this.importDlgGroupId && this.importDlgGroupId != '') {
        this.$set(this.machineCodeWhiteListForm, 'classId', this.importDlgGroupId)
      }
      this.dialogMachineCodeWhiteListFormVisible = true
      this.$nextTick(() => {
        this.$refs['machineCodeWhiteListForm'].clearValidate()
      })
    },
    getListByGroupIds(data) {
      if (data.groupIds) {
        return listMachineCodeInfo({ classIds: data.groupIds })
      }
      return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
    },
    deleteMachineCodeInfo(data) {
      return deleteMachineCode(data.ids);
    },
    handleWhiteListAppImport() {
      this.loadTypeTree()
      this.$refs.appImportTable.show()
    },
    loadTypeTree() {
      getGroupTree().then(res => {
        (res.data || []).forEach(item => {
          item.dataId = parseInt(item.dataId) || null
        });
        this.typeTreeData = res.data
      })
    },
    createGroup(data) {
      return addGroup(data);
    },
    updateGroup(data) {
      return updateGroup(data);
    },
    deleteGroup(data) {
      return deleteGroup(data.id);
    },
    countByGroupId(data) {
      return countInfoByGroupId(data);
    },
    getGroupByName(data) {
      return getGroupByName(data);
    },
    show(row) {
      this.currentUuid = row.uuid
      this.machineCodeQuery.uuid = row.uuid
      this.machineCodeQuery.page = 1
      this.machineCodeQuery.searchInfo = ''
      // this.machineCodeQuery.szMachine = ''
      // this.machineCodeQuery.szUserName = ''
      this.dialogMachineCodeVisible = true
      this.$nextTick(() => {
        this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
      })
    },
    machineCodeRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.machineCodeQuery, option)
      return listMachineCode(searchQuery)
    },
    handleSyncMachineCode(row) {
      this.$confirmBox(this.$t(this.$t('pages.cloudOutfile_Msg20'), this.$t('text.prompt'))).then(() => {
        syncMachineCode(row.refId).then(response => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('pages.cloudOutfile_Msg21'), type: 'success', duration: 2000 })
          this.handleMachineCodeFilter()
        })
      })
    },
    refreshMachineCode() {
      getMachineCode(this.machineCodeForm.id).then(resp => {
        this.machineCodeForm = Object.assign({}, resp.data)
        this.machineCodeUpdateTime = this.machineCodeForm.updateTime
        this.$nextTick(() => {
          this.$refs['machineCodeForm'].clearValidate()
        })
        this.$notify({
          duration: 2000,
          message: this.$t('pages.cloudOutfile_Msg22'),
          type: 'success'
        })
      })
    },
    createMachineCode() {
      this.$refs['machineCodeForm'].validate(valid => {
        if (valid) {
          const formData = Object.assign({ 'uuid': this.currentUuid }, this.machineCodeForm);
          addCloudOutfileMachineCode(formData).then(response => {
            this.machineCodeSubmitting = false
            this.dialogMachineCodeFormVisible = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
            this.machineCodeQuery.page = 1
            this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
          })
        } else {
          this.machineCodeSubmitting = false;
        }
      })
    },
    updateMachineCode() {
      this.$refs['machineCodeForm'].validate(valid => {
        if (valid) {
          const formData = Object.assign({ 'uuid': this.currentUuid }, this.machineCodeForm);
          updateCloudOutfileMachineCode(formData).then(response => {
            this.machineCodeSubmitting = false
            this.dialogMachineCodeFormVisible = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            this.machineCodeQuery.page = 1
            this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
          }).catch(err => {
            if (err.response.data.code === 601) {
              this.$confirmBox(this.$t('pages.cloudOutfile_Msg49'), this.$t('text.prompt')).then(() => {
                this.refreshMachineCode()
              })
            }
          })
        } else {
          this.machineCodeSubmitting = false;
        }
      })
    },
    handleCreateMachineCode() {
      this.machineCodeOperation = 'add'
      this.machineCodeFormTitle = this.$t('button.add') + this.$t('pages.cloudOutfileMachinaCode')
      this.machineCodeForm = {}
      this.dialogMachineCodeFormVisible = true
      this.$nextTick(() => {
        this.$refs['machineCodeForm'].clearValidate()
      })
    },
    handleMachineCodeFilter() {
      this.machineCodeQuery.page = 1
      this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
    },
    handleMachineCodeUpdate(row) {
      this.machineCodeOperation = 'edit'
      this.machineCodeFormTitle = this.$t('button.edit') + this.$t('pages.cloudOutfileMachinaCode')
      getMachineCode(row.refId).then(resp => {
        this.dialogMachineCodeFormVisible = true;
        this.machineCodeForm = Object.assign({}, resp.data)
        this.machineCodeUpdateTime = this.machineCodeForm.updateTime
        this.$nextTick(() => {
          this.$refs['machineCodeForm'].clearValidate()
        })
      })
    },
    handleMachineCodeDelete(row) {
      const formData = { 
        id: row.refId,
        szMachine: row.szMachine,
        szUserName: row.szUserName
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteCloudOutfileMachineCode(formData).then(response => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          this.machineCodeQuery.page = 1
          this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
        })
      })
    },
    szMachineValidator(rule, value, callback) {
      if (this.machineCodeForm.szMachine === undefined || this.machineCodeForm.szMachine === null || this.machineCodeForm.szMachine === '') {
        callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szMachine')))
      } else {
        const machineCodePattern = /^\d{8}$/;
        if (!machineCodePattern.test(this.machineCodeForm.szMachine)) {
          callback(new Error(this.$t('pages.machineCode_text6')))
        }  
        var obj = Object.assign({}, this.machineCodeForm)
        obj.uuid = this.currentUuid
        machineExist(obj).then(resp => {
          if (resp.data) {
            // 存在
            callback(new Error(this.$t('pages.cloudOutfile_Msg23')))
          } else {
            // 不存在
            callback()
          }
        })
      }
    },
    classIdFormatter(row, data) {
      let msg = ''
      if (this.typeTreeData) {
        this.typeTreeData.some(node => {
          if (node.dataId == data) {
            msg = node.label
            return true
          }
        })
      }
      return msg
    },
    machineCodeValidator(rule, value, callback) {
      if (this.machineCodeWhiteListForm.machineCode === undefined || this.machineCodeWhiteListForm.machineCode === null || this.machineCodeWhiteListForm.machineCode === '') {
        callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szMachine')))
      } else {
        const machineCodePattern = /^\d{8}$/;
        if (!machineCodePattern.test(this.machineCodeWhiteListForm.machineCode)) {
          callback(new Error(this.$t('pages.machineCode_text6')))
        }     
        var obj = Object.assign({}, this.machineCodeWhiteListForm)
        machineCodeWhiteListExist(obj).then(resp => {
          if (resp.data) {
            // 存在
            callback(new Error(this.$t('pages.cloudOutfile_Msg23')))
          } else {
            // 不存在
            callback()
          }
        })
      }
    }
  }
}
</script>
