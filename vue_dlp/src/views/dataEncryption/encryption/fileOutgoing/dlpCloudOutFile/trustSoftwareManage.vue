<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.trustSoftwareManage')"
      :visible.sync="dialogTrustSoftwareVisible"
    >
      <div style="height: 100%;">
        <div class="toolbar">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreateTrustSoftware">
            {{ $t('button.add') }}
          </el-button>
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleImportTrustSoftwareWhiteList">
            {{ $t('pages.accessSoftwareWhitelistLibraryImport') }}
          </el-button>
          <div class="searchCon">
            <el-input v-model="trustSoftwareQuery.szSoftName" v-trim clearable :placeholder="$t('pages.placeholderSzSoftName')" style="width: 200px;" @keyup.enter.native="handleTrustSoftwareFilter"></el-input>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleTrustSoftwareFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table ref="outfileTrustSoftwareList" :col-model="colModelTrustSoftware" :multi-select="false" :row-data-api="trustSoftwareRowDataApi" :height="420" />
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="trustSoftwareFormTitle"
      :visible.sync="dialogTrustSoftwareFormVisible"
      width="800px"
      top="25vh"
    >
      <div style="display: flex;align-items: center;">
        <upload-dir ref="uploadDir" :popover-height="180" :loading="fileSubmitting" style="display: inline-block;" @changeFile="changeFile" />
        <div style="margin-bottom: 5px;margin-left: 5px;">
          <el-upload
            ref="upload"
            name="uploadFile"
            action="1111"
            accept=".exe"
            :limit="1"
            :disabled="fileSubmitting"
            :show-file-list="false"
            :before-upload="beforeUpload"
            style="display: inline-block;"
          >
            <el-button type="primary" :loading="fileSubmitting" size="mini">{{ $t('pages.uploadFile') }}</el-button>
          </el-upload>
        </div>
        <el-button type="primary" size="mini" style="margin-left: 5px;" @click="showAppSelectDlg">
          {{ $t('pages.software_Msg16') }}
        </el-button>
        <el-button type="primary" size="mini" style="margin-left: 5px;" @click="iniImportDlg">
          {{ $t('pages.software_Msg45') }}
        </el-button>
      </div>
      <div>
        <el-row v-if="fileSubmitting">
          <el-col :span="22">
            <el-progress type="line" :percentage="percentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
      </div>

      <grid-table
        ref="importTable"
        :height="250"
        :multi-select="true"
        :show-pager="false"
        :col-model="colModel"
        :row-datas="softList"
        :is-clear-saved-selected-row-datas-change="false"
        style="margin-bottom: 5px;"
      />
      <Form ref="checkTypeForm" :rules="checkTypeRules" :model="checkTypeForm" label-position="right" label-width="80px">
        <FormItem :label="$t('table.flagList')" prop="checkType">
          <el-select v-model="checkTypeForm.checkType" multiple clearable style="margin-top: 5px;">
            <el-option v-for="item in verifyModelTreeData" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </FormItem>
      </Form>
      <span style="color: blue;">{{ $t('pages.cloudOutfile_Msg53') }}</span>
      <div slot="footer" class="dialog-footer">
        <download-tool button-style="primary" style="float: left;"></download-tool>
        <el-tooltip class="item" effect="dark" :content="$t('pages.outgoingProcess_text10')" placement="top-end">
          <el-button type="primary" :loading="trustSoftwareSubmitting" @click="createTrustSoftware">{{ $t('button.confirm') }}</el-button>
        </el-tooltip>
        <el-button @click="dialogTrustSoftwareFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 信任软件修改弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="trustSoftwareFormTitle"
      :visible.sync="updateTrustSoftwareVisible"
      width="800px"
      top="25vh"
    >
      <Form ref="trustSoftwareForm" :rules="trustSoftwareRules" :model="trustSoftwareForm" label-position="right" label-width="120px" >
        <el-row>
          <el-col :span="19" >
            <FormItem :label="$t('pages.szSoftName')" prop="szSoftName">
              <el-input v-model="trustSoftwareForm.szSoftName" :maxlength="64"/>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label-width="25px">
              <el-checkbox v-model="trustSoftwareForm.szSoftNameFlag">
                {{ $t('pages.szSoftNameFlag') }}
              </el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="19" >
            <FormItem :label="$t('pages.szSoftSig')" prop="szSoftSig">
              <el-input v-model="trustSoftwareForm.szSoftSig" :maxlength="128"/>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label-width="25px">
              <el-checkbox v-model="trustSoftwareForm.szSoftSigFlag">
                {{ $t('pages.szSoftSigFlag') }}
              </el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="19" >
            <FormItem :label="$t('pages.szSoftMd5')" prop="szSoftMd5">
              <el-input v-model="trustSoftwareForm.szSoftMd5" :maxlength="33"/>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label-width="25px">
              <el-checkbox v-model="trustSoftwareForm.szSoftMd5Flag" :maxlength="128">
                {{ $t('pages.szSoftMd5Flag') }}
              </el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.szDescriptor')">
          <el-input v-model="trustSoftwareForm.szDescriptor" type="textarea" rows="5" :maxlength="64"/>
        </FormItem>
        <FormItem v-if="trustSoftwareOperation === 'edit'" :label="$t('table.updateTime')" prop="updateTime">
          <el-input v-model="trustSoftwareUpdateTime" readonly/>
        </FormItem>
        <div v-if="trustSoftwareOperation === 'edit'" style="margin-top: 15px;">
          <span style="color: #0c60a5;">{{ $t('pages.cloudOutfile_Msg14') }}</span>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <download-tool button-style="primary" style="float: left;"></download-tool>
        <el-button :loading="trustSoftwareSubmitting" type="primary" @click="updateTrustSoftware()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="updateTrustSoftwareVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    
    <!-- 软件白名单修改程序 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="this.$t('pages.updateProcess')"
      :visible.sync="appFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="appDataForm" :rules="appRules" :model="appTemp" label-position="right" label-width="120px" style="width: 500px; margin-left: 25px;">
        <FormItem :label="$t('table.processName')" prop="processName">
          <el-input v-model="appTemp.processName" :disabled="true"/>
        </FormItem>
        <FormItem :label="$t('pages.appType')" prop="classId">
          <el-row>
            <el-col :span="addGroupAble ? 21 : 24">
              <el-select v-model="appTemp.classId">
                <el-option v-for="item in typeTreeData" :key="item.dataId" :value="item.dataId" :label="item.label"/>
              </el-select>
            </el-col>
            <el-col v-show="addGroupAble" style="padding-top:1px" :span="3">
              <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="appTemp.remark" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('table.softSign')" prop="softSign">
          <el-input v-model="appTemp.softSign" :disabled="true"/>
        </FormItem>
        <FormItem :label="$t('table.fileMd5')" prop="fileMd5">
          <el-input v-model="appTemp.fileMd5" :disabled="true"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="updateApp">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="appFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- ini导入 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.importProcess')"
      :visible.sync="uploadVisible"
      width="400px"
    >
      <Form ref="uploadForm" :rules="iniRules" :model="iniTemp" label-position="right" label-width="80px" style="width: 340px;">
        <el-row>
          <el-col :span="21">
            <FormItem :label="$t('pages.iniFileName')" prop="fileName">
              <el-input v-model="iniTemp.fileName" readonly/>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-upload
              ref="upload"
              name="upload"
              action="1111"
              accept=".ini"
              :on-change="fileChange"
              :show-file-list="false"
              :file-list="iniFileList"
              :disabled="iniFileSubmitting"
              :auto-upload="false"
            >
              <el-button type="primary" icon="el-icon-upload" :loading="iniFileSubmitting"></el-button>
            </el-upload>
          </el-col>
        </el-row>
        <el-row v-if="iniFileSubmitting">
          <el-col :span="22">
            <el-progress type="line" :percentage="iniPercentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" :disabled="iniCanDisable" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
        <span style="color: #0c60a5;padding-top: 5px">
          {{ $t('text.prompt') }}：<br/>{{ '&nbsp;&nbsp;&nbsp;' + $t('pages.outgoingProcess_text13') }}<br/>
        </span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="importSubmitting" type="primary" @click="saveFile()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="uploadCancel">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <app-select-dlg ref="appSelectDlg" :append-to-body="false" @select="multipleAppendFile"/>
    <import-table-dlg
      ref="appImportTable"
      group-node-key="classId"
      :elg-title="$t('pages.importApp')"
      :group-root-name="$t('pages.processType')"
      :search-info-name="$t('table.processName')"
      :confirm-button-name="$t('pages.addApp')"
      :prompt-message="$t('pages.outgoingProcess_text9')"
      :group-title="$t('pages.appType')"
      :group-label="$t('pages.appType')"
      :rules-group-label="$t('pages.outgoingProcess_text12')"
      :grid-table-height="300"
      :check-trust-soft-able="true"
      :col-model="importColModel"
      :list="getAppInfoList"
      :load-group-tree="getGroupTree"
      :create-group="createGroup"
      :update-group="updateGroup"
      :delete-group="deleteGroup"
      :count-by-group="countByGroupId"
      :get-group-by-name="getAppTypeByName"
      :delete="deleteAppInfo"
      :get-list-by-group-ids="getListByGroupIds"
      :handle-create-elg="handleAppCreate"
      @submitEnd="getNeedAddIds"
      @trustSoftTypeCheck="trustSoftTypeCheck"
      @changeGroupAfter="changeGroupAfter"
    />
    <app-batch-add-dlg
      ref="batchUpload"
      :verify-model-able="isVerifyModelAble"
      :rules-group-label="$t('pages.outgoingProcess_text12')"
      :ini-import-able="true"
      @createTypeAfter="createTypeAfter"
      @submitEnd="appBatchSubmitEnd"
    />
    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.appType')"
      :group-tree-data="typeTreeData"
      :group-label="$t('pages.appType')"
      :rules-group-label="$t('pages.outgoingProcess_text12')"
      :edit-valid-func="getAppTypeByName"
      :add-func="createGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>
<script>
import {
  updateCloudOutfileTrustSoftware,
  getTrustSoftware,
  deleteCloudOutfileTrustSoftware,
  batchAddCloudOutfileTrustSoftware
} from '@/api/dataEncryption/encryption/cloudOutfile'
import { getGroupTree, listProcess, deleteProcess,
  updateProcess, deleteAppType, addAppType, updateAppType,
  getAppTypeByName, countInfoByGroupId, listProcessPage
} from '@/api/dataEncryption/encryption/outgoingProcess'
import {
  listTrustSoftware
} from '@/api/dataEncryption/encryption/dlpCloudOutFile'
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool'
import {
  importProcessReturnList
} from '@/api/dataEncryption/encryption/outgoingProcess';
import { upload } from '@/api/behaviorManage/application/appGroup'
import UploadDir from '@/components/UploadDir'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import AppBatchAddDlg from '@/views/dataEncryption/encryption/fileOutgoing/appBatchAddDlg'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'
import axios from 'axios'
export default {
  name: 'TrustSoftwareManage',
  components: { UploadDir, AppSelectDlg, DownloadTool, AppBatchAddDlg, ImportTableDlg, EditGroupDlg },
  props: {
  },
  data() {
    return {
      dialogTrustSoftwareVisible: false,
      softList: [],
      checkTypeForm: {
        checkType: []
      },
      verifyModelTreeData: [
        { label: this.$t('table.szSoftName'), value: 1 },
        { label: this.$t('table.szSoftSig'), value: 2 },
        { label: this.$t('table.szSoftMd5'), value: 4 }
      ],
      colModel: [
        { prop: 'szSoftName', label: 'szSoftName', width: '120' },
        { prop: 'szSoftSig', label: 'szSoftSig', width: '150' },
        { prop: 'szSoftMd5', label: 'szSoftMd5', width: '150' },
        { prop: 'szDescriptor', label: 'szDescriptor', width: '150', type: 'input', maxlength: 30, editMode: true }
      ],
      colModelTrustSoftware: [
        { prop: 'szSoftName', label: 'szSoftName', width: '100', sort: true },
        { prop: 'szSoftNameFlag', label: 'szSoftNameFlag', width: '130', formatter: this.checkFlagFormatter, sort: true },
        { prop: 'szSoftSig', label: 'szSoftSig', width: '100', sort: true },
        { prop: 'szSoftSigFlag', label: 'szSoftSigFlag', width: '130', formatter: this.checkFlagFormatter, sort: true },
        { prop: 'szSoftMd5', label: 'szSoftMd5', width: '100', sort: true },
        { prop: 'szSoftMd5Flag', label: 'szSoftMd5Flag', width: '130', formatter: this.checkFlagFormatter, sort: true },
        { prop: 'szDescriptor', label: 'szDescriptor', width: '100', sort: true },
        { prop: 'updateTime', label: 'updateTime', width: '200', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleTrustSoftwareUpdate },
            { label: 'delete', click: this.handleTrustSoftwareDelete }
            // { label: 'outfileSyncData', click: this.handleSyncTrustSoftware }
          ]
        }
      ],
      importColModel: [
        { prop: 'processName', label: 'processName', width: '100', sort: true },
        { prop: 'productName', label: 'productName', width: '100', sort: true },
        { prop: 'softSign', label: 'szSoftSig', width: '100' },
        { prop: 'fileMd5', label: 'szSoftMd5', width: '100' },
        { prop: 'classId', label: 'typeId', width: '80', sort: true, formatter: this.classIdFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdateListData }
          ]
        }
      ],
      appFormVisible: false,
      appRules: {
        processName: [
          { required: true, message: this.$t('pages.appGroup_text25'), trigger: 'blur' }
        ],
        classId: [
          { required: true, message: this.$t('pages.outgoingProcess_text5'), trigger: 'blur' }
        ]
      },
      appTemp: {

      },
      dlgSubmitting: false,
      addGroupAble: false,
      typeTreeData: [],
      checkTypeRules: {
        checkType: [
          { required: true, message: this.$t('components.required'), trigger: 'change' }
        ]
      },
      trustSoftwareQuery: {
        uuid: '',
        szSoftName: '',
        szSoftSig: '',
        szSoftMd5: '',
        szDescriptor: ''
      },
      trustSoftwareForm: {},
      source: null,
      iniFileSubmitting: false,
      iniFileList: [],
      uploadVisible: false,
      fileSubmitting: false,
      iniCanDisable: false,
      importSubmitting: false,
      trustSoftwareSubmitting: false,
      updateTrustSoftwareVisible: false,
      fileLimitSize: 1024,
      trustSoftwareFormTitle: '',
      trustSoftwareUpdateTime: ' ',
      percentage: 0,
      dialogTrustSoftwareFormVisible: false,
      trustSoftwareOperation: '',
      trustSoftwareId: '',
      currentUuid: '',
      iniPercentage: 0,
      iniTemp: {}, // 表单字段
      defaultIniTemp: {
        id: undefined,
        fileName: ''
      },
      importCheckTypes: [],
      iniRules: {
        fileName: { required: true, message: this.$t('pages.chooseFile'), trigger: 'blur' }
      },
      isVerifyModelAble: false,
      trustSoftwareRules: {
        szSoftName: [
          {
            validator: (rule, value, callback) => {
              if (this.trustSoftwareForm.szSoftNameFlag && !this.trustSoftwareForm.szSoftName) {
                callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szSoftName')))
              }
              callback()
            }
          }],
        szSoftSig: [
          {
            validator: (rule, value, callback) => {
              if (this.trustSoftwareForm.szSoftSigFlag && !this.trustSoftwareForm.szSoftSig) {
                callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szSoftSig')))
              }
              callback()
            }
          }],
        szSoftMd5: [
          {
            validator: (rule, value, callback) => {
              if (this.trustSoftwareForm.szSoftMd5Flag && !this.trustSoftwareForm.szSoftMd5) {
                callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szSoftMd5')))
              }
              callback()
            }
          }]
      }
    }
  },
  methods: {
    getGroupTree,
    handleDrag() {

    },
    updateApp() {
      this.dlgSubmitting = true
      this.$refs['appDataForm'].validate((valid) => {
        if (valid) {
          this.updateAppInfo(this.appTemp).then(res => {
            this.dlgSubmitting = false
            this.appFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.$refs['appImportTable'].refreshTableData()
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    updateAppInfo(data) {
      data.classId = data.typeId || data.classId
      return updateProcess(data);
    },
    createGroupAddEnd(row) {
      this.loadTypeTree();
    },
    handleTypeCreate() {
      this.loadTypeTree();
      this.$refs['createGroupDlg'].handleCreate();
    },
    createTypeAfter() {
      this.$refs['appImportTable'].loadGroupTreeData()
    },
    appBatchSubmitEnd() {
      this.loadTypeTree()
      this.$refs['appImportTable'].refreshTableData()
    },
    loadTypeTree() {
      getGroupTree().then(res => {
        (res.data || []).forEach(item => {
          item.dataId = parseInt(item.dataId) || null
        });
        this.typeTreeData = res.data
      })
    },
    handleImportTrustSoftwareWhiteList() {
      this.loadTypeTree()
      this.$refs.appImportTable.show()
    },
    //  分页查找
    getAppInfoList(data) {
      return listProcessPage(data);
    },
    //  改变程序类型
    updateGroup(data) {
      return updateAppType(data);
    },
    //  删除程序类型
    deleteGroup(data) {
      return deleteAppType(data);
    },
    createGroup(data) {
      return addAppType(data);
    },
    countByGroupId(data) {
      return countInfoByGroupId(data);
    },
    getAppTypeByName(data) {
      return getAppTypeByName(data);
    },
    deleteAppInfo(data) {
      return deleteProcess(data);
    },
    getNeedAddIds(needIds) {
      const ids = needIds.join(',')
      if (this.importCheckTypes.length === 0 || ids.length === 0) {
        return
      }
      listProcess({ ids: needIds.join(',') }).then(resp => {
        const selectData = resp.data
        const trustSoftwares = []
        for (let i = 0; i < selectData.length; i++) {
          const obj = {
            uuid: this.currentUuid,
            szSoftName: '',
            szSoftSig: '',
            szSoftMd5: '',
            szDescriptor: '',
            szSoftNameFlag: false,
            sxszSoftSigFlag: false,
            szSoftMd5Flag: false
          }
          if (this.importCheckTypes.includes(1)) {
            // 包含程序名验证
            if (!selectData[i].processName && !selectData[i].productName) {
              continue
            }
            obj.szSoftNameFlag = true
          } else {
            obj.szSoftNameFlag = false
          }
          if (selectData[i].productName) {
            const productName = selectData[i].productName.length > 64 ? selectData[i].productName.substring(0, 64) : selectData[i].productName
            obj.szSoftName = productName
          } else {
            const processName = selectData[i].processName.length > 64 ? selectData[i].processName.substring(0, 64) : selectData[i].processName
            obj.szSoftName = processName
          }
          if (this.importCheckTypes.includes(2)) {
            // 包含程序签名验证
            if (!selectData[i].softSign) {
              continue
            }
            obj.sxszSoftSigFlag = true
          } else {
            obj.sxszSoftSigFlag = false
          }
          if (selectData[i].softSign) {
            const softSign = selectData[i].softSign.length > 128 ? selectData[i].softSign.substring(0, 128) : selectData[i].softSign
            obj.szSoftSig = softSign
          }  
          if (this.importCheckTypes.includes(4)) {
            // 包含程序md5验证
            if (!selectData[i].fileMd5) {
              continue
            }
            obj.szSoftMd5Flag = true
          } else {
            obj.szSoftMd5Flag = false
          }
          if (selectData[i].fileMd5) {
            const fileMd5 = selectData[i].fileMd5.length > 33 ? selectData[i].fileMd5.substring(0, 33) : selectData[i].fileMd5
            obj.szSoftMd5 = fileMd5
          }
          if (selectData[i].remark) {
            const remark = selectData[i].remark.length > 64 ? selectData[i].remark.substring(0, 64) : selectData[i].remark
            obj.szDescriptor = remark
          } else {
            if (selectData[i].fileDescription) {
              const fileDescription = selectData[i].fileDescription.length > 64 ? selectData[i].fileDescription.substring(0, 64) : selectData[i].fileDescription
              obj.szDescriptor = fileDescription
            }
          }
          trustSoftwares.push(obj)
        }
        if (trustSoftwares.length === 0) {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.createSuccess'),
            type: 'success',
            duration: 2000
          })
          return
        }
        const trust = []
        trustSoftwares.forEach(item => {
          const obj = {
            szSoftName: item.szSoftName,
            szSoftSig: item.szSoftSig,
            szSoftMd5: item.szSoftMd5,
            szDescriptor: item.szDescriptor,
            szSoftNameFlag: item.szSoftNameFlag,
            szSoftSigFlag: item.sxszSoftSigFlag,
            szSoftMd5Flag: item.szSoftMd5Flag
          }
          trust.push(obj)
        })
        const batchTrustSoftwareParam = {
          uuid: this.currentUuid,
          trustSoftwares: trust
        }
        batchAddCloudOutfileTrustSoftware(batchTrustSoftwareParam).then(resp => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.createSuccess'),
            type: 'success',
            duration: 2000
          })
          this.handleTrustSoftwareFilter()
        })
      })
    },
    trustSoftTypeCheck(data) {
      this.importCheckTypes = []
      this.importCheckTypes = [...data]
    },
    changeGroupAfter() {
      this.loadTypeTree();
    },
    handleAppCreate() {
      this.isVerifyModelAble = false
      this.$refs.batchUpload.show()
    },
    handleUpdateListData(row) {
      this.loadTypeTree()
      this.appTemp = Object.assign({}, row)
      this.addGroupAble = false
      this.appFormVisible = true
    },
    getListByGroupIds(data) {
      if (data.groupIds) {
        return listProcess({ classIds: data.groupIds })
      }
      return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
    },
    trustSoftwareRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.trustSoftwareQuery, option)
      return listTrustSoftware(searchQuery)
    },
    handleTrustSoftwareFilter() {
      this.trustSoftwareQuery.page = 1
      this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
    },
    handleCreateTrustSoftware() {
      this.show('add', this.currentUuid)
    },
    checkFlagFormatter(row, data) {
      if (data == true) {
        return this.$t('text.yes')
      }
      return this.$t('text.no')
    },
    handleTrustSoftwareDelete(row) {
      const formData = {
        id: row.refId,
        szSoftName: row.szSoftName,
        szSoftSig: row.szSoftSig,
        szSoftMd5: row.szSoftMd5,
        szDescriptor: row.szDescriptor,
        dwCheckFlags: row.dwCheckFlags
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteCloudOutfileTrustSoftware(formData).then(response => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          this.trustSoftwareQuery.page = 1
          this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
        })
      })
    },
    handleTrustSoftwareUpdate(row) {
      getTrustSoftware(row.refId).then(resp => {
        this.show('edit', this.currentUuid, resp.data)
      })
    },
    saveFile() {
      this.importSubmitting = true
      this.iniFileSubmitting = true
      this.iniPercentage = 0
      this.iniCanDisable = false
      this.$refs['uploadForm'].validate((valid) => {
        if (valid) {
          const onUploadProgress = (progressEvent) => {
            const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
            this.iniPercentage = parseInt(percent)
            if (this.iniPercentage == 100) {
              // 如果上传进度到了100，则后台已经在处理保存数据了，因此不允许取消了
              this.iniCanDisable = true
            }
          }
          this.source = this.connectionSource()
          // 这个是上传会话token，取消上传操作需要的参数
          const cacheToken = this.source.token
          // 通过 FormData 对象上传文件
          const fd = this.toFormData(this.iniTemp)
          fd.append('uploadFile', this.iniFileList[0].raw)
          // 发起请求
          importProcessReturnList(fd, onUploadProgress, cacheToken).then(res => {
            this.importSubmitting = false
            this.iniFileSubmitting = false
            this.iniPercentage = 0
            this.uploadCancel()
            this.multipleAppendFile(res.data)
          }).catch(res => {
            this.importSubmitting = false
            this.iniFileSubmitting = false
            this.iniPercentage = 0
          })
        } else {
          this.importSubmitting = false
          this.iniFileSubmitting = false
          this.iniPercentage = 0
        }
      })
    },
    uploadCancel() {
      this.uploadVisible = false
      this.cancel()
    },
    iniImportDlg() {
      this.iniTemp = Object.assign({}, this.defaultIniTemp)
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs['uploadForm'].clearValidate()
      })
    },
    fileChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          message: this.$t('pages.outgoingProcess_text6'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const fileName = file.name
      const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      if (ext == 'ini') {
        this.iniTemp.fileName = fileName
        this.iniFileList.splice(0, 1, file)
      } else {
        this.$message({
          message: this.$t('pages.outgoingProcess_text7'),
          type: 'error',
          duration: 2000
        })
        return false
      }
    },
    showAppSelectDlg() {
      this.$refs['appSelectDlg'].show()
    },
    changeFile(files) {
      const fd = new FormData()
      for (let i = 0; i < files.length; i++) {
        fd.append('uploadFile', files[i])// 传文件
      }
      if (files.length > 0) {
        this.uploadFile(fd)
      } else {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.appGroup_text8'),
          type: 'error',
          duration: 2000
        })
      }
      this.$refs.uploadDir.clearValue()
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
      if (!isLt2M) {
        this.$alert(`<label style="color: red">${this.$t('pages.appGroup_text11', { size: this.fileLimitSize })}<label>`, this.$t('text.prompt'), {
          confirmButtonText: this.$t('button.confirm'),
          dangerouslyUseHTMLString: true
        })
        return false
      }
      const fd = new FormData()
      fd.append('uploadFile', file)// 传文件
      this.uploadFile(fd)
      return false // 屏蔽了action的默认上传
    },
    uploadFile(formData) {
      this.fileSubmitting = true
      this.percentage = 0
      // 上传钩子，用来获取进度条
      const onUploadProgress = (progressEvent) => {
        const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
        this.percentage = parseInt(percent)
      }
      this.source = this.connectionSource()
      const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
      // 调用后台接口获取进程windows属性
      upload(formData, onUploadProgress, cacheToken).then(res => {
        this.resetUploadComponent()
        this.multipleAppendFile(res.data)
      }).catch(res => {
        this.resetUploadComponent()
        if (axios.isCancel(res)) {
          // 取消上传后的操作，待补充
        }
      })
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    multipleAppendFile(softs) {
      const addTableData = [...this.softList]
      softs.forEach((soft, index) => {
        const obj = {
          id: index,
          szSoftName: '',
          szSoftSig: '',
          szSoftMd5: ''
        }
        if (soft.productName) {
          const productName = soft.productName.length > 64 ? soft.productName.substring(0, 64) : soft.productName
          obj.szSoftName = productName
        } else if (soft.processName) {
          const processName = soft.processName.length > 64 ? soft.processName.substring(0, 64) : soft.processName
          obj.szSoftName = processName
        }
        if (soft.softSign) {
          const softSign = soft.softSign.length > 128 ? soft.softSign.substring(0, 128) : soft.softSign
          obj.szSoftSig = softSign
        }
        if (soft.fileMd5) {
          const fileMd5 = soft.fileMd5.length > 33 ? soft.fileMd5.substring(0, 33) : soft.fileMd5
          obj.szSoftMd5 = fileMd5
        }
        const exists = addTableData.some(item => 
          item.szSoftName === obj.szSoftName &&
          item.szSoftSig === obj.szSoftSig &&
          item.szSoftMd5 === obj.szSoftMd5
        );
        if (!exists) {
          addTableData.push(obj)
        }
      })
      addTableData.forEach((item, index) => {
        item.id = index
      })
      this.softList = [...addTableData]
      this.$nextTick(() => {
        this.$refs.importTable.toggleAllSelection()
      })
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    showTrustSoftware(row) {
      this.currentUuid = row.uuid
      this.trustSoftwareQuery.uuid = row.uuid
      this.trustSoftwareQuery.page = 1
      this.trustSoftwareQuery.szSoftName = ''
      this.trustSoftwareQuery.szSoftSig = ''
      this.trustSoftwareQuery.szSoftMd5 = ''
      this.trustSoftwareQuery.szDescriptor = ''
      this.dialogTrustSoftwareVisible = true
      this.$nextTick(() => {
        this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
      })
    },
    classIdFormatter(row, data) {
      let msg = ''
      if (this.typeTreeData) {
        this.typeTreeData.some(node => {
          if (node.dataId == data) {
            msg = node.label
            return true
          }
        })
      }
      return msg
    },
    show(ope, uuid, row) {
      this.trustSoftwareOperation = ope
      this.trustSoftwareForm = {}
      this.currentUuid = uuid
      if (ope === 'add') {
        this.softList = []
        this.checkTypeForm = {
          checkType: []
        }
        this.trustSoftwareFormTitle = this.$t('button.add') + this.$t('pages.cloudOutfileTrustSoftware')
        this.$nextTick(() => {
          this.$refs.checkTypeForm.clearValidate()
        })
        this.dialogTrustSoftwareFormVisible = true
      } else {
        this.trustSoftwareForm = Object.assign({}, row)
        this.trustSoftwareId = row.refId
        this.trustSoftwareUpdateTime = row.updateTime
        this.trustSoftwareFormTitle = this.$t('button.edit') + this.$t('pages.cloudOutfileTrustSoftware')
        this.updateTrustSoftwareVisible = true
        this.$nextTick(() => {
          this.$refs['trustSoftwareForm'].clearValidate()
        })
      }
    },
    refreshTrustSoftware() {
      getTrustSoftware(this.trustSoftwareId).then(resp => {
        this.trustSoftwareForm = {}
        this.trustSoftwareForm = Object.assign({}, resp.data)
        this.trustSoftwareUpdateTime = resp.data.updateTime
        this.$nextTick(() => {
          this.$refs['trustSoftwareForm'].clearValidate()
        })
        this.$message({
          duration: 2000,
          message: this.$t('pages.cloudOutfile_Msg22'),
          type: 'success'
        })
      })
    },
    updateTrustSoftware() {
      this.$refs['trustSoftwareForm'].validate(valid => {
        if (valid) {
          this.trustSoftwareSubmitting = true
          const formData = Object.assign({ 'uuid': this.currentUuid }, this.trustSoftwareForm);
          formData.id = this.trustSoftwareId
          updateCloudOutfileTrustSoftware(formData).then(response => {
            this.trustSoftwareSubmitting = false
            this.updateTrustSoftwareVisible = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            this.handleTrustSoftwareFilter()
          }).catch(err => {
            this.trustSoftwareSubmitting = false
            if (err.response.data.code === 601) {
              this.$confirmBox(this.$t('pages.cloudOutfile_Msg49'), this.$t('text.prompt')).then(() => {
                this.refreshTrustSoftware()
              })
            }
          })
        }
      })
    },
    createTrustSoftware() {
      this.$refs.checkTypeForm.validate(valid => {
        if (valid) {
          const trustSoftwares = []
          const selectData = this.$refs.importTable.getSelectedDatas()
          if (selectData.length === 0) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.cloudOutfile_Msg51'),
              type: 'error',
              duration: 2000
            })
            return
          }
          for (let i = 0; i < selectData.length; i++) {
            const obj = {
              uuid: this.currentUuid,
              szSoftName: '',
              szSoftSig: '',
              szSoftMd5: '',
              szDescriptor: selectData[i].szDescriptor,
              szSoftNameFlag: false,
              sxszSoftSigFlag: false,
              szSoftMd5Flag: false
            }
            if (this.checkTypeForm.checkType.includes(1)) {
              // 包含程序名验证
              if (!selectData[i].szSoftName) {
                continue
              }
              obj.szSoftNameFlag = true
            } else {
              obj.szSoftNameFlag = false
            }
            obj.szSoftName = selectData[i].szSoftName
            if (this.checkTypeForm.checkType.includes(2)) {
              // 包含程序签名验证
              if (!selectData[i].szSoftSig) {
                continue
              }
              obj.sxszSoftSigFlag = true
            } else {
              obj.sxszSoftSigFlag = false
            }
            obj.szSoftSig = selectData[i].szSoftSig
            if (this.checkTypeForm.checkType.includes(4)) {
              // 包含程序md5验证
              if (!selectData[i].szSoftMd5) {
                continue
              }
              obj.szSoftMd5Flag = true
            } else {
              obj.szSoftMd5Flag = false
            }
            obj.szSoftMd5 = selectData[i].szSoftMd5
            trustSoftwares.push(obj)
          }
          const trust = []
          trustSoftwares.forEach(item => {
            const obj = {
              szSoftName: item.szSoftName,
              szSoftSig: item.szSoftSig,
              szSoftMd5: item.szSoftMd5,
              szDescriptor: item.szDescriptor,
              szSoftNameFlag: item.szSoftNameFlag,
              szSoftSigFlag: item.sxszSoftSigFlag,
              szSoftMd5Flag: item.szSoftMd5Flag
            }
            trust.push(obj)
          })
          if (trust.length === 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.dialogTrustSoftwareFormVisible = false
            return
          }
          const batchTrustSoftwareParam = {
            uuid: this.currentUuid,
            trustSoftwares: trust
          }
          batchAddCloudOutfileTrustSoftware(batchTrustSoftwareParam).then(resp => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.handleTrustSoftwareFilter()
            this.dialogTrustSoftwareFormVisible = false
          })
        }
      })
    }
  }
}
</script>
