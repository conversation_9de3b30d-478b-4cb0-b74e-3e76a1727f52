<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane :label="$t('route.FileOutgoingManage')" name="outFileManage">
        <out-file-manage ref="outFileManage">
        </out-file-manage>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.cloudOutfile_Msg48')" name="outFileLogManage">
        <out-file-log-manage ref="outFileLogManage">
        </out-file-log-manage>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.syncLog')" name="outFileLog">
        <out-file-log ref="outFileLog">
        </out-file-log>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import OutFileManage from './outFileManage.vue';
import OutFileLogManage from './outFileLogManage.vue';
import OutFileLog from './outFileLog.vue';
import { cloudEnableMixins } from '@/mixins/cloudEnableMixins';

export default {
  name: 'DlpCloudOutFile',
  components: { OutFileManage, OutFileLog, OutFileLogManage },
  mixins: [cloudEnableMixins],
  data() {
    return {
      activeName: 'outFileManage'
    }
  },
  methods: {
    tabClick() {

    }
  }
}
</script>
  <style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
  </style>

