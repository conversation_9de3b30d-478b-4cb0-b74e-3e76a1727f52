<template>
  <div class="app-container">
    <!-- 组织架构树 -->
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" :showed-tree="['user']" @data-change="strategyTargetNodeChange"/>
    <!-- 查询框+数据列表 -->
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.placeholderOutfileName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-date-picker
            v-model="query.dateRange"
            type="daterange"
            range-separator="-"
            value-format="yyyy-MM-dd"
            :start-placeholder="$t('pages.startDate')"
            :end-placeholder="$t('pages.endDate')"
            style="width:300px"
          >
          </el-date-picker>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="outfileList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <!-- 外发记录追踪 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.cloudOutfileRecord')"
      :visible.sync="dialogRecordVisible"
    >
      <div style="height: 100%;">
        <div class="toolbar">
          <div class="searchCon">
            <el-date-picker
              v-model="recordQuery.dateRange"
              type="daterange"
              range-separator="-"
              value-format="yyyy-MM-dd"
              :start-placeholder="$t('pages.startDate')"
              :end-placeholder="$t('pages.endDate')"
              style="width:300px"
            >
            </el-date-picker>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleRecordFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table ref="outfileRecordList" :col-model="colModelRecord" :row-data-api="recordRowDataApi" :height="420" />
      </div>
    </el-dialog>

    <!-- 外发文件修改 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.cloudOutfileEdit')"
      :visible.sync="dialogEditVisible"
    >
      <Form ref="outfileForm" :rules="rules" :model="outfileForm" label-position="right" label-width="120px" >
        <el-divider content-position="left"> {{ $t('pages.outfileBaseinfo') }} </el-divider>
        <el-row>
          <el-col :span="8">
            <FormItem>
              <el-checkbox v-model="outfileForm.checkCc" :true-label="1" :false-label="0">只允许一台计算机使用</el-checkbox>
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem>
              <el-checkbox v-model="outfileForm.checkMachineCode" :true-label="1" :false-label="0">需要验证机器码后才能打开</el-checkbox>
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem>
              <el-checkbox v-model="outfileForm.checkTrustSoft" :true-label="1" :false-label="0">需要验证信任软件</el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.useCount') + '：'" >
              <el-checkbox v-model="outfileForm.openTimesFlag" @change="changeOpenTimesFlag">不限制</el-checkbox>
              <el-input v-model="outfileForm.openTimes" :disabled="outfileForm.openTimesFlag" style="width: 50%; padding-left: 5px;"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.outfileBeginDate') + '：'">
              <el-checkbox v-model="outfileForm.beginDateFlag">不限制</el-checkbox>
              <el-date-picker v-model="outfileForm.beginDate" type="datetime" :disabled="outfileForm.beginDateFlag" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.outfileEndDate') + '：'">
              <el-checkbox v-model="outfileForm.endDateFlag">不限制</el-checkbox>
              <el-date-picker v-model="outfileForm.endDate" type="datetime" :disabled="outfileForm.endDateFlag" value-format="yyyy-MM-dd HH:mm:ss" ></el-date-picker>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <FormItem>
              <el-checkbox v-model="outfileForm.checkEndWarn" :true-label="1" :false-label="0">到期前显示倒计时提示</el-checkbox>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10" :offset="-2">
            <FormItem :label="$t('pages.szPassword') + '：'">
              <el-input v-model="outfileForm.szPassword"/>
            </FormItem>
          </el-col>
          <el-col :span="10" :offset="2">
            <FormItem :label="$t('pages.szBackPassword') + '：'">
              <el-input v-model="outfileForm.szBackPassword"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10" :offset="-2">
            <FormItem :label="$t('pages.strSignMsg') + '：'">
              <el-input v-model="outfileForm.strSignMsg"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-divider content-position="left"> {{ $t('pages.outfileAuth') }} </el-divider>
        <FormItem>
          <el-checkbox-group v-model="outfileForm.openRightList">
            <el-checkbox v-for="item in openRightOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateOutfile()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogEditVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 机器码列表 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.cloudOutfileMachinaCode')"
      :visible.sync="dialogMachineCodeVisible"
    >
      <div style="height: 100%;">
        <div class="toolbar">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreateMachineCode">
            {{ $t('button.add') }}
          </el-button>
          <div class="searchCon">
            <el-input v-model="machineCodeQuery.szMachine" v-trim clearable :placeholder="$t('pages.placeholderSzMachine')" style="width: 200px;" @keyup.enter.native="handleMachineCodeFilter"></el-input>
            <el-input v-model="machineCodeQuery.szUserName" v-trim clearable :placeholder="$t('pages.placeholderSzUserName')" style="width: 200px;" @keyup.enter.native="handleMachineCodeFilter"></el-input>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleMachineCodeFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table ref="outfileMachineCodeList" :col-model="colModelMachineCode" :row-data-api="machineCodeRowDataApi" :height="420" />
      </div>
    </el-dialog>

    <!-- 机器码添加/编辑 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="machineCodeFormTitle"
      :visible.sync="dialogMachineCodeFormVisible"
      width="500px"
      top="25vh"
    >
      <Form ref="machineCodeForm" :rules="machineCodeRules" :model="machineCodeForm" label-position="right" label-width="120px" >
        <FormItem :label="$t('pages.szMachine') + '：'" prop="szMachine">
          <el-input v-model="machineCodeForm.szMachine"/>
        </FormItem>
        <FormItem :label="$t('pages.szUserName') + '：'" prop="szUserName">
          <el-input v-model="machineCodeForm.szUserName"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="machineCodeSubmitting" type="primary" @click="machineCodeOperation == 'edit' ? updateMachineCode() : createMachineCode();">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogMachineCodeFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 信任软件列表 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.cloudOutfileTrustSoftware')"
      :visible.sync="dialogTrustSoftwareVisible"
    >
      <div style="height: 100%;">
        <div class="toolbar">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreateTrustSoftware">
            {{ $t('button.add') }}
          </el-button>
          <div class="searchCon">
            <el-input v-model="trustSoftwareQuery.szSoftName" v-trim clearable :placeholder="$t('pages.placeholderSzSoftName')" style="width: 200px;" @keyup.enter.native="handleTrustSoftwareFilter"></el-input>
            <el-input v-model="trustSoftwareQuery.szSoftSig" v-trim clearable :placeholder="$t('pages.placeholderSzSoftSig')" style="width: 200px;" @keyup.enter.native="handleTrustSoftwareFilter"></el-input>
            <el-input v-model="trustSoftwareQuery.szSoftMd5" v-trim clearable :placeholder="$t('pages.placeholderSzSoftMd5')" style="width: 200px;" @keyup.enter.native="handleTrustSoftwareFilter"></el-input>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleTrustSoftwareFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table ref="outfileTrustSoftwareList" :col-model="colModelTrustSoftware" :row-data-api="trustSoftwareRowDataApi" :height="420" />
      </div>
    </el-dialog>

    <!-- 信任软件添加/编辑 Dialog -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="trustSoftwareFormTitle"
      :visible.sync="dialogTrustSoftwareFormVisible"
      width="500px"
      top="25vh"
    >
      <Form ref="trustSoftwareForm" :rules="trustSoftwareRules" :model="trustSoftwareForm" label-position="right" label-width="120px" >
        <el-row>
          <el-col :span="16" >
            <FormItem :label="$t('pages.szSoftName') + '：'" prop="szSoftName">
              <el-input v-model="trustSoftwareForm.szSoftName"/>
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem :label="$t('pages.szSoftNameFlag') + '：'">
              <el-checkbox v-model="trustSoftwareForm.szSoftNameFlag"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16" >
            <FormItem :label="$t('pages.szSoftSig') + '：'" prop="szSoftSig">
              <el-input v-model="trustSoftwareForm.szSoftSig"/>
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem :label="$t('pages.szSoftSigFlag') + '：'">
              <el-checkbox v-model="trustSoftwareForm.szSoftSigFlag"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16" >
            <FormItem :label="$t('pages.szSoftMd5') + '：'" prop="szSoftMd5">
              <el-input v-model="trustSoftwareForm.szSoftMd5"/>
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem :label="$t('pages.szSoftMd5Flag') + '：'">
              <el-checkbox v-model="trustSoftwareForm.szSoftMd5Flag"/>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.szDescriptor') + '：'">
          <el-input v-model="trustSoftwareForm.szDescriptor" type="textarea" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="trustSoftwareSubmitting" type="primary" @click="trustSoftwareOperation == 'edit' ? updateTrustSoftware() : createTrustSoftware();">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogTrustSoftwareFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import {
  listCloudOutfile,
  listCloudOutfileRecord,
  listOpenRightOptions,
  updateCloudOutfile,
  listCloudOutfileMachineCode,
  addCloudOutfileMachineCode,
  updateCloudOutfileMachineCode,
  deleteCloudOutfileMachineCode,
  listCloudOutfileTrustSoftware,
  addCloudOutfileTrustSoftware,
  updateCloudOutfileTrustSoftware,
  deleteCloudOutfileTrustSoftware
} from '@/api/dataEncryption/encryption/cloudOutfile'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, entityLink, refreshPage } from '@/utils'

export default {
  name: 'CloudOutfile',
  props: {
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'name', width: '100' },
        { prop: 'staffName', label: 'staffName', width: '60' },
        { prop: 'createDate', label: 'createDate', width: '80' },
        { prop: 'openRightDesc', label: 'openRightDesc', width: '150' },
        { prop: 'openTimes', label: 'openTimes', width: '40', formatter: this.openTimesFormatter },
        { label: 'operate', type: 'button', fixedWidth: '400', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'outfileTrace', click: this.showRecordDialog },
            { label: 'machineManage', click: this.showMachineCodeDialog },
            { label: 'trustSoftwareManage', click: this.showTrustSoftwareDialog }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        name: '',
        dateRange: '',
        beginDate: '',
        endDate: ''
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      submitting: false,
      source: null,
      checkedEntityNode: {},
      currentUuid: '',

      // 外发记录参数
      colModelRecord: [
        { prop: 'fileName', label: 'fileName', width: '100' },
        { prop: 'reqIp', label: 'reqIp', width: '80' },
        { prop: 'reqAddress', label: 'reqAddress', width: '60' },
        { prop: 'reqTime', label: 'reqTime', width: '90' },
        { prop: 'computerName', label: 'computerName', width: '60' },
        { prop: 'openIp', label: 'openIp', width: '80' },
        { prop: 'ack', label: 'ack', width: '60', formatter: this.ackFormatter }
      ],
      dialogRecordVisible: false,
      recordQuery: {
        page: 1,
        uuid: '',
        dateRange: '',
        beginDate: '',
        endDate: ''
      },

      // 外发文件参数修改
      dialogEditVisible: false,
      outfileForm: {},

      openRightOptions: [],
      rules: {

      },
      machineCodeRules: {
        szMachine: [
          { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szMachine'), trigger: 'blur' }
        ],
        szUserName: [
          { required: true, message: this.$t('pages.plzEnter') + this.$t('pages.szUserName'), trigger: 'blur' }
        ]
      },
      trustSoftwareRules: {
        szSoftName: [
          {
            validator: (rule, value, callback) => {
              if (this.trustSoftwareForm.szSoftNameFlag && !this.trustSoftwareForm.szSoftName) {
                callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szSoftName')))
              }
              callback()
            }
          }],
        szSoftSig: [
          {
            validator: (rule, value, callback) => {
              if (this.trustSoftwareForm.szSoftSigFlag && !this.trustSoftwareForm.szSoftSig) {
                callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szSoftSig')))
              }
              callback()
            }
          }
        ],
        szSoftMd5: [
          {
            validator: (rule, value, callback) => {
              if (this.trustSoftwareForm.szSoftMd5Flag && !this.trustSoftwareForm.szSoftMd5) {
                callback(new Error(this.$t('pages.plzEnter') + this.$t('pages.szSoftMd5')))
              }
              callback()
            }
          }
        ]
      },
      // 机器码列表框
      colModelMachineCode: [
        { prop: 'szMachine', label: 'szMachine', width: '100' },
        { prop: 'szUserName', label: 'szUserName', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleMachineCodeUpdate },
            { label: 'delete', click: this.handleMachineCodeDelete }
          ]
        }
      ],
      dialogMachineCodeVisible: false,
      machineCodeQuery: {
        uuid: '',
        szMachine: '',
        szUserName: ''
      },

      // 机器码增/改
      machineCodeOperation: '',
      machineCodeFormTitle: '',
      machineCodeForm: {},
      dialogMachineCodeFormVisible: false,
      machineCodeSubmitting: false,

      // 信任软件列表框
      colModelTrustSoftware: [
        { prop: 'szSoftName', label: 'szSoftName', width: '100' },
        { prop: 'szSoftNameFlag', label: 'szSoftNameFlag', width: '100', formatter: this.checkFlagFormatter },
        { prop: 'szSoftSig', label: 'szSoftSig', width: '100' },
        { prop: 'szSoftSigFlag', label: 'szSoftSigFlag', width: '100', formatter: this.checkFlagFormatter },
        { prop: 'szSoftMd5', label: 'szSoftMd5', width: '100' },
        { prop: 'szSoftMd5Flag', label: 'szSoftMd5Flag', width: '100', formatter: this.checkFlagFormatter },
        { prop: 'szDescriptor', label: 'szDescriptor', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleTrustSoftwareUpdate },
            { label: 'delete', click: this.handleTrustSoftwareDelete }
          ]
        }
      ],
      dialogTrustSoftwareVisible: false,
      trustSoftwareQuery: {
        uuid: '',
        szSoftName: '',
        szSoftSig: '',
        szSoftMd5: '',
        szDescriptor: ''
      },

      // 新人软件增/改
      trustSoftwareOperation: '',
      trustSoftwareFormTitle: '',
      trustSoftwareForm: {},
      dialogTrustSoftwareFormVisible: false,
      trustSoftwareSubmitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['outfileList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取外发权限列表
    this.listOpenRightOptions();
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  methods: {
    listOpenRightOptions() {
      listOpenRightOptions().then(response => {
        this.openRightOptions = response.data;
      })
    },
    connectionSource() {
      return axios.CancelToken.source()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return listCloudOutfile(searchQuery)
    },
    recordRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.recordQuery, option)
      return listCloudOutfileRecord(searchQuery)
    },
    machineCodeRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.machineCodeQuery, option)
      return listCloudOutfileMachineCode(searchQuery)
    },
    trustSoftwareRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.trustSoftwareQuery, option)
      return listCloudOutfileTrustSoftware(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    refresh() {
      return refreshPage(this)
    },
    handleFilter() {
      if (this.query.dateRange && this.query.dateRange.length == 2) {
        this.query.beginDate = this.query.dateRange[0] + ' 00:00:00'
        this.query.endDate = this.query.dateRange[1] + ' 23:59:59'
      } else {
        this.query.beginDate = ''
        this.query.endDate = ''
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleRecordFilter() {
      if (this.recordQuery.dateRange && this.recordQuery.dateRange.length == 2) {
        this.recordQuery.beginDate = this.recordQuery.dateRange[0] + ' 00:00:00'
        this.recordQuery.endDate = this.recordQuery.dateRange[1] + ' 23:59:59'
      } else {
        this.recordQuery.beginDate = ''
        this.recordQuery.endDate = ''
      }
      this.recordQuery.page = 1
      this.$refs['outfileRecordList'].execRowDataApi(this.recordQuery)
    },
    handleMachineCodeFilter() {
      this.machineCodeQuery.page = 1
      this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
    },
    handleTrustSoftwareFilter() {
      this.trustSoftwareQuery.page = 1
      this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleUpdate(row) {
      this.outfileForm = Object.assign({ openRightList: [] }, row)
      this.dialogEditVisible = true;
    },
    changeOpenTimesFlag(value) {
      if (value) {
        this.outfileForm.openTimes = 65535
      }
    },
    showRecordDialog(row) {
      this.recordQuery.uuid = row.uuid
      this.recordQuery.page = 1
      this.dialogRecordVisible = true
      this.$refs['outfileRecordList'].execRowDataApi(this.recordQuery)
    },

    showMachineCodeDialog(row) {
      this.currentUuid = row.uuid
      this.machineCodeQuery.uuid = row.uuid
      this.machineCodeQuery.page = 1
      this.dialogMachineCodeVisible = true
      this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
    },
    resetForm() {
      this.outfileForm = {}
    },
    updateOutfile() {
      this.$refs['outfileForm'].validate(valid => {
        if (valid) {
          const formData = Object.assign(this.outfileForm);
          updateCloudOutfile(formData).then(response => {
            this.submitting = false
            this.dialogEditVisible = false
            this.resetForm()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            this.query.page = 1
            this.$refs['outfileList'].execRowDataApi(this.query)
          })
        } else {
          this.machineCodeSubmitting = false;
        }
      })
    },
    resetMachineCodeForm() {
      this.outfileForm = {}
    },
    handleCreateMachineCode() {
      this.machineCodeOperation = 'add'
      this.machineCodeFormTitle = this.$t('button.add') + this.$t('pages.cloudOutfileMachinaCode')
      this.machineCodeForm = {}
      this.dialogMachineCodeFormVisible = true
      this.$refs['machineCodeForm'].clearValidate()
    },
    createMachineCode() {
      this.$refs['machineCodeForm'].validate(valid => {
        if (valid) {
          const formData = Object.assign({ 'uuid': this.currentUuid }, this.machineCodeForm);
          addCloudOutfileMachineCode(formData).then(response => {
            this.machineCodeSubmitting = false
            this.dialogMachineCodeFormVisible = false
            this.resetMachineCodeForm()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
            this.machineCodeQuery.page = 1
            this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
          })
        } else {
          this.machineCodeSubmitting = false;
        }
      })
    },
    handleMachineCodeUpdate(row) {
      this.machineCodeOperation = 'edit'
      this.machineCodeFormTitle = this.$t('button.edit') + this.$t('pages.cloudOutfileMachinaCode')
      this.machineCodeForm = Object.assign({}, row)
      this.dialogMachineCodeFormVisible = true;
      this.$refs['machineCodeForm'].clearValidate()
    },
    updateMachineCode() {
      this.$refs['machineCodeForm'].validate(valid => {
        if (valid) {
          const formData = Object.assign({ 'uuid': this.currentUuid }, this.machineCodeForm);
          updateCloudOutfileMachineCode(formData).then(response => {
            this.machineCodeSubmitting = false
            this.dialogMachineCodeFormVisible = false
            this.resetMachineCodeForm()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            this.machineCodeQuery.page = 1
            this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
          })
        } else {
          this.machineCodeSubmitting = false;
        }
      })
    },
    handleMachineCodeDelete(row) {
      const formData = { id: row.id }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteCloudOutfileMachineCode(formData).then(response => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          this.machineCodeQuery.page = 1
          this.$refs['outfileMachineCodeList'].execRowDataApi(this.machineCodeQuery)
        })
      })
    },
    showTrustSoftwareDialog(row) {
      this.currentUuid = row.uuid
      this.trustSoftwareQuery.uuid = row.uuid
      this.trustSoftwareQuery.page = 1
      this.dialogTrustSoftwareVisible = true
      this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
    },
    handleCreateTrustSoftware() {
      this.trustSoftwareOperation = 'add'
      this.trustSoftwareFormTitle = this.$t('button.add') + this.$t('pages.cloudOutfileTrustSoftware')
      this.trustSoftwareForm = {}
      this.resetTrustSoftwareForm()
      this.dialogTrustSoftwareFormVisible = true
      this.$refs['trustSoftwareForm'].clearValidate()
    },
    createTrustSoftware() {
      this.$refs['trustSoftwareForm'].validate(valid => {
        if (valid) {
          const formData = Object.assign({ 'uuid': this.currentUuid }, this.trustSoftwareForm)
          addCloudOutfileTrustSoftware(formData).then(response => {
            this.trustSoftwareSubmitting = false;
            this.dialogTrustSoftwareFormVisible = false
            this.resetTrustSoftwareForm();
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
            this.trustSoftwareQuery.page = 1
            this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
          })
        }
      })
    },
    resetTrustSoftwareForm() {
      this.trustSoftwareForm = {
        szSoftNameFlag: false,
        szSoftSigFlag: false,
        szSoftMd5Flag: false
      }
    },
    handleTrustSoftwareUpdate(row) {
      this.trustSoftwareOperation = 'edit'
      this.trustSoftwareFormTitle = this.$t('button.edit') + this.$t('pages.cloudOutfileTrustSoftware')
      this.resetTrustSoftwareForm()
      this.trustSoftwareForm = Object.assign({}, row)
      this.dialogTrustSoftwareFormVisible = true;
    },
    updateTrustSoftware() {
      this.$refs['trustSoftwareForm'].validate(valid => {
        if (valid) {
          const formData = Object.assign({ 'uuid': this.currentUuid }, this.trustSoftwareForm);
          updateCloudOutfileTrustSoftware(formData).then(response => {
            this.trustSoftwareSubmitting = false
            this.dialogTrustSoftwareFormVisible = false
            this.resetTrustSoftwareForm()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            this.trustSoftwareQuery.page = 1
            this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
          })
        }
      })
    },
    handleTrustSoftwareDelete(row) {
      const formData = { id: row.id }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteCloudOutfileTrustSoftware(formData).then(response => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          this.trustSoftwareQuery.page = 1
          this.$refs['outfileTrustSoftwareList'].execRowDataApi(this.trustSoftwareQuery)
        })
      })
    },
    openTimesFormatter(row, data) {
      if (data == 65535) {
        return this.$t('text.unLimit')
      }
      return data;
    },
    ackFormatter(row, data) {
      if (data == 1) {
        return this.$t('text.success')
      }
      if (data == 2) {
        return this.$t('text.expired')
      }
      if (data == 3) {
        return this.$t('text.timesExceed')
      }
      if (data == 4) {
        return '需要在指定的计算机'
      }
      return data;
    },
    checkFlagFormatter(row, data) {
      if (data == true) {
        return this.$t('text.yes')
      }
      return this.$t('text.no')
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
