<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="waterMarkList"
        row-key="id"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :selectable="selectable"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="showDlg"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 720px; margin-left:20px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable"></el-switch>
        </FormItem>
        <div style="height: 250px;">
          <import-table
            ref="screenTable"
            :search-info-prompt-message="$t('pages.watermark_text2')"
            :row-no-label="$t('table.keyId')"
            :delete-disabled="!deleteable"
            :col-model="listColModel"
            :row-datas="tempElgData"
            :formable="formable"
            :selectable="screenSelectable"
            :handle-create="handleScreenCreateImport"
            :handle-delete="handleScreenDelete"
            :handle-import="handleScreenImport"
            :handle-search="handleScreenSearch"
            @selectionChangeEnd="selectionChangeEnd"
          />
        </div>
        <div style="color: #0c60a5;padding-top: 5px">
          {{ $t('pages.watermark_text3') }}
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="formable"
          v-permission="'A5A'"
          type="primary"
          style="float:left;"
          @click="()=>{ this.$router.push({ path: '/system/baseData/waterMarkLib', query: { tabName: 'screenTab' } }) }"
        >{{ $t('route.waterMarkLib') }}</el-button>
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="showDlg=false">{{ $t('button.cancel') }}</el-button>

      </div>
    </el-dialog>

    <screen-water-mark-dlg ref="screenDlg" :group-ids="treeSelectNode" :save-as-able="false" :font-options="fontOptions" :handle-group-create="handleTypeCreate"/>

    <import-table-dlg
      ref="screenImportTable"
      :exits-list-data="elgData"
      :elg-title="$t('pages.importWatermarkModel')"
      :group-root-name="$t('pages.waterMarkLib')"
      :search-info-name="$t('pages.templateName')"
      :confirm-button-name="$t('pages.addWatermarkModel')"
      :prompt-message="$t('pages.watermark_text1')"
      :group-title="$t('pages.waterMarkGroup')"
      :delete-group-prompt-message="deleteGroupPromptMessage"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="getGroupTreeNode"
      :create-group="createGroup"
      :update-group="updateGroup"
      :delete-group="deleteGroup"
      :get-group-by-name="getGroupByName"
      :delete="deleteLib"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteGroup"
      :move-group-to-other="moveGroupToOtherHandler"
      :get-list-by-group-ids="getLibListByGroupIds"
      :handle-create-elg="handleScreenCreate"
      :handle-delete-group="handleDeleteGroup"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
      @elgCancelAfter="elgCancelAfter"
      @changeGroupAfter="changeGroupAfter"
    />

    <preview-dlg ref="previewDef"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importOutgoingScreenWaterMarkStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :edit-valid-func="getGroupByName"
      :add-func="createGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>

<script>
import {
  createWaterMark, deleteWaterMark, getWaterMarkByName, getWaterMarkPage, updateWaterMark
} from '@/api/dataEncryption/encryption/fileOutgoing'
import {
  countByVo, countLibByGroupId, createGroup, createLib, deleteGroup,
  deleteGroupAndData, deleteLib, getGroupByName, getGroupTreeNode, getLibList,
  getLibPage, moveGroup, moveGroupToOther, moveGroupToOtherByType, updateGroup, updateLib
} from '@/api/system/baseData/waterMarkLib';
import {
  buttonFormatter, enableStgBtn, enableStgDelete, entityLink,
  hiddenActiveAndEntity, objectFormatter, refreshPage, selectable
} from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import PreviewDlg from '@/views/system/baseData/waterMarkLib/previewDlg'
import ScreenWaterMarkDlg from '@/views/system/baseData/waterMarkLib/screenWaterMarkDlg'
import { getTypeFont } from '@/utils/dictionary';
import { getDeptPage } from '@/api/system/terminalManage/department';
import EditGroupDlg from '@/views/common/editGroupDlg'
import { formatWatermarkDetail } from '@/utils/waterMark';

export default {
  name: 'OutgoingScreenWaterMark',
  components: { PreviewDlg, ImportStg, ImportTable, ImportTableDlg, ScreenWaterMarkDlg, EditGroupDlg },
  props: {
    type: { // 数据类型：1、屏幕水印；2、打印水印；3、word文档水印
      type: Number,
      default: 1
    },
    listable: {
      type: Boolean,
      default: true
    },
    formable: { // 能否提交表单
      type: Boolean,
      default: true
    },
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 103,
      showTree: true,
      treeable: true,
      addBtnAble: false,
      deleteable: false,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'list', label: 'waterMark', width: '200', formatter: this.modelNameFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', formatter: (row) => buttonFormatter(row, this), click: this.handleUpdate }
          ]
        }
      ],
      listColModel: [
        { prop: 'name', label: 'templateName', width: '120', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '100', sort: true, formatter: this.groupElgFormatter },
        { prop: 'remark', label: 'templateRemark', width: '500', formatter: row => formatWatermarkDetail(row.info, row.type) },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', click: this.handleElgUpdateBase }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'templateName', width: '100', sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', width: '100', sort: true, formatter: this.groupElgFormatter },
        { prop: 'remark', label: 'templateRemark', width: '500', formatter: row => formatWatermarkDetail(row.info, row.type) },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', click: this.handleElgUpdate }
          ]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        list: []
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      query: {},
      showDlg: false,
      dialogStatus: 'create',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.outgoingScreenWaterMarkList'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.outgoingScreenWaterMarkList'), 'create')
      },
      treeSelectNode: [],
      tempElgData: [],
      elgData: [],
      createFlag: false,
      updateFlag: false,
      needIds: [],
      deptList: {},
      //  水印库添加修改弹窗
      fontOptions: getTypeFont(),
      moveFlag: true,
      count: 0,
      deleteGroupPromptMessage: ''  //  删除分组弹窗的提示内容
    }
  },
  computed: {
    gridTable() {
      return this.$refs['waterMarkList']
    }
  },
  watch: {
    elgData(val) {
      val.forEach(item => {
        item.groupName = this.getGroupNameByDataId(this.treeSelectNode, item.groupId)
      })
      this.handleScreenSearch()
    },
    needIds(val) {
      if (val.length > 0) {
        this.getLibListByIds({ ids: val.join(',') }).then(res => {
          this.elgData = res.data || []
        })
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.loadGroupTree()
  },
  activated() {
    this.loadGroupTree()
  },
  methods: {
    createGroup,
    updateGroup,
    deleteGroup,
    getGroupByName,
    moveGroup,
    deleteGroupAndData,
    moveGroupToOther,
    getGroupTreeNode,
    deleteLib,
    countLibByGroupId,
    countLibByVo(groupId) {
      return groupId ? countByVo({ type: 1, groupId: groupId }) : new Promise((resolve, reject) => { resolve({ code: 20000, data: 0 }) })
    },
    getLibListByIds(data) {
      return data.ids ? getLibList({ type: 1, ids: data.ids }) : new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
    },
    getLibListByGroupIds(data) {
      return data.groupIds ? getLibList({ type: 1, groupIds: data.groupIds }) : new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      enableStgBtn(null, this)
      this.handleFilter()
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    getUrlMark() {
      return this.type == 1 ? 'screenWaterMark' : 'printWaterMark'
    },
    handleView(data) {
      const oriData = Object.assign({}, data.oriData)
      if (oriData.waterMarkForm == 1) {
        oriData.waterMarkForm = 0
      }
      this.$refs['previewDef'].handleUpdate(oriData, this.type == 2 ? 2 : 44)
    },
    rowDataApi: function(option) {
      option.mark = this.getUrlMark()
      const searchQuery = Object.assign({}, this.query, option)
      return getWaterMarkPage(searchQuery)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.groupId = this.query.groupId
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.temp.list = []
      this.elgData = []
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      //  获取列表数据
      const ids = []
      row.list && row.list.forEach(item => { ids.push(item.id) })
      ids.length > 0 && this.getLibListByIds({ ids: ids.join(',') }).then(res => {
        this.elgData = res.data || []
      })
      this.showDlg = true
      this.$nextTick(() => {
        this.$refs['screenTable'].clearSearchInfo()
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteWaterMark({ ids: toDeleteIds.join(','), mark: this.getUrlMark() }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.mark = this.getUrlMark()
          this.elgData.forEach(item => { this.temp.list.push(item.info) })
          createWaterMark(this.temp).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.list = []
          this.elgData.forEach(item => { this.temp.list.push(item.info) })
          const tempData = Object.assign({}, this.temp)
          tempData.mark = this.getUrlMark()
          updateWaterMark(tempData).then(respond => {
            this.submitting = false
            this.showDlg = false
            this.gridTable.execRowDataApi()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      getWaterMarkByName({ name: value, mark: this.getUrlMark() }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    modelNameFormatter(row, data) {
      const result = []
      if (row.list) {
        row.list.forEach(config => {
          result.push(config.name)
        })
      }
      return result.join('、')
    },
    groupFormatter(row, data) {
      return this.processStg.label
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    //
    //  加载分组树信息
    loadGroupTree() {
      return getGroupTreeNode().then(respond => {
        this.treeSelectNode = respond.data
      })
    },
    screenSelectable(row, index) {
      return selectable(row, index);
    },
    //  以下方法是点击导入按钮的弹窗方法
    //  导入方法的row-data-api
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.type = 1
      return getLibPage(searchQuery)
    },
    getNeedAddIds(needIds) {
      this.loadGroupTree()
      this.needIds = needIds
    },
    //  删除记录
    submitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      this.elgData = this.elgData.filter(item => { return deleteIdsList.filter(i => i === item.id).length === 0 })
    },
    elgCancelAfter() {
      this.loadGroupTree();
      this.getScreenListByIds();
    },
    getScreenListByIds() {
      const ids = this.$refs['screenTable'].getIdsByList(this.elgData) || []
      if (ids.length === 0) {
        this.elgData = []
        return;
      }
      this.getLibListByIds({ ids: ids.join(',') }).then(res => {
        this.elgData = res.data || []
      })
    },
    //  弹窗分组的数据发送变化时
    changeGroupAfter() {
      this.loadGroupTree()
    },
    handleScreenDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs['screenTable'].getSelectedIds() || []
        this.elgData = this.$refs['screenTable'].deleteTableData(this.elgData, toDeleteIds)
      }).catch(() => {})
    },
    handleScreenSearch(searchInfo) {
      searchInfo = searchInfo || (this.$refs['screenTable'] && this.$refs['screenTable'].getSearchInfo()) || ''
      if (searchInfo === '') {
        this.tempElgData = this.elgData
      } else {
        //  条件查询
        this.tempElgData = this.elgData.filter(item => {
          return (item.name && item.name.toLowerCase().indexOf(searchInfo) !== -1) ||
            (item.remark && item.remark.toLowerCase().indexOf(searchInfo) !== -1) ||
            (item.groupName && item.groupName.toLowerCase().indexOf(searchInfo) !== -1)
        })
      }
    },
    handleScreenImport() {
      this.$refs['screenImportTable'].show();
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createFlag) {
          this.$nextTick(() => {
            this.$refs['screenImportTable'].refreshTableData()
          })
        } else {
          this.elgData.push(data)
        }
        this.createFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$nextTick(() => {
            this.$refs['screenImportTable'].refreshTableData()
          })
        } else {
          for (let i = 0; i < this.elgData.length; i++) {
            if (this.elgData[i].id === data.id) {
              this.elgData.splice(i, 1)
              this.elgData.push(data)
              break
            }
          }
        }
        this.updateFlag = false
      }
    },
    createScreen(formData, callBack) {
      createLib(formData).then(respond => {
        if (callBack) {
          callBack(false)
        }
        this.isImportElg(respond.data, 'create')
        this.$refs['screenDlg'].submitting = false
        this.$refs['screenDlg'].hide()
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
      }).catch(res => {
        if (callBack) {
          callBack(true)
        }
        this.$refs['screenDlg'].submitting = false
      })
    },
    updateScreen(formData, callBack) {
      updateLib(formData).then(respond => {
        if (callBack) {
          callBack(false)
        }
        this.isImportElg(respond.data, 'update')
        this.$refs['screenDlg'].submitting = false
        this.$refs['screenDlg'].hide()
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
      }).catch(res => {
        if (callBack) {
          callBack(true)
        }
        this.$refs['screenDlg'].submitting = false
      })
    },
    getDeptList() {
      const query = { page: 1 }
      getDeptPage(query).then(res => {
        const data = res.data.items
        data.forEach(el => {
          this.deptList[el.id] = el
        })
      })
    },
    groupElgFormatter(row, data) {
      return this.getGroupNameByDataId(this.treeSelectNode, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    handleElgUpdateBase(row, addGroupAble) {
      this.loadGroupTree()
      //  设置修改弹窗的新增分组按钮
      addGroupAble = addGroupAble === undefined || addGroupAble === null ? true : addGroupAble
      row.groupId = row.groupId ? row.groupId + '' : null
      this.$refs['screenDlg'].handleUpdate(row, this.updateScreen, addGroupAble)
    },
    //  水印新增修改弹窗
    handleElgUpdate(row) {
      this.updateFlag = true
      this.handleElgUpdateBase(row, false)
    },
    handleScreenCreateImport() {
      this.handleScreenCreate(null, null, true)
    },
    handleScreenCreate(selectedGroupId, flag, addGroupAble) {
      this.createFlag = flag || false
      addGroupAble = addGroupAble || false
      this.$refs['screenDlg'].handleCreate(selectedGroupId, this.createScreen, addGroupAble)
    },
    //  删除分组弹窗
    handleDeleteGroup(data) {
      const _this = this
      this.countLibByVo(data.dataId).then(res => {
        _this.count = res.data || 0
        this.countLibByGroupId(data.dataId).then(respond => {
          this.moveFlag = true
          this.deleteGroupPromptMessage = ''
          if (_this.count === 0 && respond.data === 0) {  //  在屏幕水印和文档水印中，该分组下都没有数据
            this.$refs.screenImportTable.$refs['editGroupDlg'].handleDelete(data.dataId)
          } else if (_this.count === 0 && respond.data > 0) { //  在屏幕水印的该分组下没有数据， 文档水印中的该分组存在数据
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.waterMarkLib3_text'),
              type: 'warning',
              duration: 5000
            })
          } else if (_this.count > 0 && respond.data - _this.count === 0) {  //  在屏幕水印的该分组下存在数据， 文档水印中的该分组没有数据
            this.$refs.screenImportTable.$refs['deleteGroupDlg'].handleCreate({ groupId: data.dataId })
          } else if (_this.count > 0 && respond.data - _this.count > 0) {  //  在屏幕水印和文档水印中，该分组下都存在数据
            this.moveFlag = false
            this.deleteGroupPromptMessage = this.$t('pages.waterMarkLib3_text1')
            this.$refs.screenImportTable.$refs['deleteGroupDlg'].handleCreate({ groupId: data.dataId })
          }
        });
      })
    },
    moveGroupToOtherHandler(data) {
      if (this.moveFlag) {
        //  移动分组下的数据，同时删除分组
        return moveGroupToOther(data);
      } else {
        //  移动分组下的数据，不删除分组
        return moveGroupToOtherByType(data.parentId, data.groupId, 1);
      }
    },
    //  添加分组事件
    handleTypeCreate() {
      this.$refs['createGroupDlg'].handleCreate();
    },
    //  新分组添加完成后事件
    createGroupAddEnd(row) {
      this.loadGroupTree().then(() => {
        this.$refs['screenDlg'].setGroupId(row.id)
      })
    }
  }
}
</script>
