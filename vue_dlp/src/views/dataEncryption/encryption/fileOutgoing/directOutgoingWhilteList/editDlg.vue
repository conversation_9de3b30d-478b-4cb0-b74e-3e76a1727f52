<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      title="直接外发程序白名单"
      :pane-height="315"
      :stg-code="231"
      :active-able="activeAble"
      :time-able="true"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <label>{{ $t('pages.releaseFunction') }}</label>
        <el-checkbox v-model="temp.allowScreenshot">{{ $t('pages.validateValue3') }}</el-checkbox>
        <el-divider content-position="left">{{ $t('pages.detectionRules') }}
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content">{{ $t('pages.blockTypeFormatter6') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-divider>
        <el-card class="box-card" :body-style="{'padding': '3px'}">
          <div v-if="formable">
            <el-button size="small" @click="handleAppCreate(false)">
              {{ $t('button.insert') }}
            </el-button>
            <el-button size="small" @click="handleApp(false)">
              {{ $t('text.infoImport', { info: $t('pages.blockAppGroup') }) }}
            </el-button>
            <el-button size="small" :disabled="!checkAppDeleteable" @click="handleDeleteCheckedApp(false)">
              {{ $t('button.delete') }}
            </el-button>
          </div>
          <grid-table
            ref="checkedAppGrid"
            :show-pager="false"
            :height="200"
            :multi-select="formable"
            :col-model="checkedColModel"
            :row-datas="checkedPacketList"
            default-expand-all
            :selectable="selectable"
            @selectionChangeEnd="checkAppSelectionChangeEnd"
          />
        </el-card>

      </template>
    </stg-dialog>
    <app-add-dlg
      ref="appAddDlg"
      :os-type="osType==7?1:osType"
      :support-md5="supportMd5"
      :append-to-body="false"
      :type-tree-data="typeTreeData"
      :create="createAppInfo"
      :update="updateAppInfo"
      :create-group="createAppType"
      :update-group="deleteAppType"
      :get-group-by-name="getAppTypeByName"
      @submitEnd="appAddSubmitEnd"
    />
    <app-import-table
      ref="appImportTable"
      :os-type="osType==7?1:osType"
      :support-md5="supportMd5"
      :type-tree-data="typeTreeData"
      :group-root-name="$t('pages.blockAppGroup')"
      :list="getAppInfoList"
      :count-by-group="countInfoByGroupId"
      :create="createAppInfo"
      :batch-create="batchCreateAppInfo"
      :update="updateAppInfo"
      :delete="deleteAppInfo"
      :import-func="importFromLib"
      :create-group="createAppType"
      :update-group="updateAppType"
      :delete-group="deleteAppType"
      :get-group-by-name="getAppTypeByName"
      @submitEnd="appMd5ImportSubmitEnd"
    />
  </div>
</template>

<script>
import {
  getTreeNode, createAppInfo, updateAppInfo, deleteAppInfo, importFromLib, getAppInfoList, countInfoByGroupId,
  createAppType, updateAppType, deleteAppType, getAppTypeByName, batchCreateAppInfo
} from '@/api/system/baseData/appLibrary'
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/dataEncryption/encryption/directOutgoingWhiteList'
import AppImportTable from '@/views/system/baseData/appLibrary/appImportTableDlg'
import AppAddDlg from '@/views/system/baseData/appLibrary/appAddDlg.vue'

export default {
  name: 'DirectOutgoingDlg',
  components: { AppImportTable, AppAddDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      // 进程系统类型：1-windows，2-linux，4-mac
      osType: 1,
      supportMd5: true,
      submitting: false,
      slotName: undefined,
      temp: {},
      packetList: [],
      nowPackageList: [],
      defaultTemp: { // 表单字段
        id: '',
        name: '',
        allowScreenshot: false,
        blockType: 1,
        active: false,
        action: 0,
        // childLimit: 0,
        remark: '',
        timeId: 1,
        classId: '',
        entityType: '',
        entityId: '',
        ruleId: null,
        checkApp: [],
        executeCode: []
      },
      checkedColModel: [
        { prop: 'name', label: 'name', width: '150', formatter: this.nameFormatter },
        { prop: 'checkMd5', label: 'isCheckMd5', hidden: () => !this.supportMd5, width: '150', formatter: this.md5LevelFormatter },
        { prop: 'source', label: 'source', width: '60', formatter: (row) => ({ 1: this.$t('pages.self'), 2: this.$t('table.typeId') })[row.source] },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdateProcess, isShow: row => !row.children }
          ]
        }
        // { prop: 'productVersion', label: '版本号', width: '150' },
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      checkAppDeleteable: false,
      exceptAppDeleteable: false,
      typeTreeDataWindow: [],
      typeTreeDataLinux: [],
      typeTreeDataMac: [],
      timer: null,
      isAddExceptApp: false
    }
  },
  computed: {
    typeTreeData() {
      if (this.osType == 1) {
        return this.typeTreeDataWindow
      } else if (this.osType == 2) {
        return this.typeTreeDataLinux
      } else if (this.osType == 4) {
        return this.typeTreeDataMac
      } else if (this.osType == 7) {
        return this.typeTreeDataWindow
      }
      return null
    },
    checkedPacketList() {
      const rowDatas = JSON.parse(JSON.stringify(this.packetList))
      // 过滤例外规则的程序
      return rowDatas.filter(item => {
        if (!item.isExcept) {
          item.children = item.children.filter(cItem => !cItem.isExcept)
        }
        return !item.isExcept
      })
    },
    exceptPacketList() {
      const rowDatas = JSON.parse(JSON.stringify(this.packetList))
      // 过滤非例外规则的程序
      return rowDatas.filter(item => {
        if (item.isExcept) {
          item.children = item.children.filter(cItem => !!cItem.isExcept)
        }
        return !!item.isExcept
      })
    },
    // 所有程序的map，以分类的id作为key
    nowPackageListMap() {
      const map = {}
      this.nowPackageList.forEach(data => {
        const typeId = data.typeId
        map[typeId] ? map[typeId].push(data) : map[typeId] = [data]
      })
      return map
    }
  },
  watch: {
    'temp.checkApp': function() {
      this.getNowPackageList().then(() => {
        this.formatDataTree()
      })
    }
  },
  created() {
    this.loadAppTypeTreeTree()
    this.resetTemp()
    this.getNowPackageList()
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    createAppInfo,
    updateAppInfo,
    deleteAppInfo,
    importFromLib,
    getAppInfoList,
    countInfoByGroupId,
    createAppType,
    updateAppType,
    deleteAppType,
    getAppTypeByName,
    batchCreateAppInfo,
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    closed() {
      this.resetTemp()
    },
    // 是否禁止
    isDoLimit(data) {
      const executeCode = data.executeCode
      return executeCode.includes(1) || executeCode.includes(3)
    },
    // 是否触发响应规则
    isDoResponse(data) {
      const executeCode = data.executeCode
      return executeCode.includes(2) || executeCode.includes(4)
    },
    handleUpdateProcess(row) {
      const prg = this.nowPackageList.find(v => v.id == row.dataId)
      if (prg) {
        this.$refs.appAddDlg.show(prg)
      }
    },
    selectable(row) {
      return row.source == 1 || row.children
    },
    // 对现有的程序列表重新整理
    formatDataTree() {
      this.packetList = []
      const list = this.temp.checkApp
      // 已添加数据的map，key 为 itemType-id-isExcept
      const existMap = {}
      const appList = {}

      // 遍历已选择的分类、应用程序，并将分类数据添加到 this.packetList
      for (const i in list) {
        const data = list[i]
        // itemType：1.应用程序 2.类别
        const { itemType, id, isExcept } = data
        // typeId 归属分类的id
        const typeId = itemType == 2 ? id : data.typeId
        const key = `${itemType}-${id}-${isExcept}`
        // 是否已添加过的数据
        const isExist = existMap[key]
        // 分类的key，如果数据是应用，则另外设置key
        const typeKey = itemType == 2 ? key : `2-${typeId}-${isExcept}`
        if (!isExist) {
          // 如果分类不存在，则添加
          if (!existMap[typeKey]) {
            const typeData = {
              id: typeId,
              typeId,
              name: typeId, // 通过程序添加的分类，使用 typeId 代替 name
              itemType: 2,
              isExcept
            }
            this.packetList.push(typeData)
            existMap[typeKey] = typeData
          }
          // 应用程序
          if (itemType != 2) {
            const app = this.formatAppData(data, 1, isExcept)
            appList[typeId] ? appList[typeId].push(app) : appList[typeId] = [app]
            
            existMap[key] = data
          }
        }
        if (itemType == 2) {
          // 如果是分类, 添加 fromType 为true，需要另外添加程序到该分类
          existMap[typeKey].fromType = true
        }
      }

      // 遍历分类数据，添加程序
      this.packetList.forEach(data => {
        const { itemType, id, typeId, isExcept } = data
        const key = `${itemType}-${id}-${isExcept}`
        const fromType = existMap[key] ? !!existMap[key].fromType : false
        const apps = fromType ? this.getTypeApps(typeId, isExcept, existMap) : []
        // 分类下的程序，由主动添加的程序和通过分类导入的程序合并
        data.children = (appList[typeId] || []).concat(apps)
      });
    },
    // 通过分类获取程序
    getTypeApps(typeId, isExcept, existMap) {
      const apps = this.nowPackageListMap[typeId] || []
      return apps.filter(data => {
        // 过滤掉已经 主动添加的程序
        const key = `1-${data.id}-${isExcept}`
        return !existMap[key]
      }).map(data => {
        return this.formatAppData(data, 2, isExcept)
      })
    },
    // 对程序的数据进行处理， source：程序来源，1. 主动添加 2. 通过分类导入
    formatAppData(data, source, isExcept) {
      const app = JSON.parse(JSON.stringify(data))
      app.source = source
      app.dataId = app.id
      app.id = `${app.id}${Math.random() * 1000000}`
      app.name = app.processName
      app.isExcept = isExcept
      app.itemType = 1
      return app
    },
    async getNowPackageList() {
      const resp = await getAppInfoList({
        page: 1
      })
      this.nowPackageList = resp.data.items
    },
    slotChange(name, slotTemp) {
      this.osType = name
      this.supportMd5 = (name == 1)
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.$nextTick(() => {
        this.reloadAppRowData()
      })
    },
    appGridTable() {
      return this.isAddExceptApp ? this.$refs['exceptAppGrid'] : this.$refs['checkedAppGrid']
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    reloadAppRowData() {
      ['exceptAppGrid', 'checkedAppGrid'].forEach(item => {
        this.$refs[item] && this.$refs[item].execRowDataApi()
      })
    },
    getAppRows(isExcept) {
      const rowDatas = []
      this.temp.checkApp.forEach(item => {
        if (!!item.isExcept === isExcept) {
          rowDatas.push(item)
        }
      })
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: { total: rowDatas.length, items: rowDatas }})
      })
    },
    checkAppSelectionChangeEnd(rowDatas) {
      this.checkAppDeleteable = rowDatas && rowDatas.length > 0
    },
    exceptAppSelectionChangeEnd(rowDatas) {
      this.exceptAppDeleteable = rowDatas && rowDatas.length > 0
    },
    loadAppTypeTreeTree: function() {
      getTreeNode({ osType: 1 }).then(respond => {
        this.typeTreeDataWindow = respond.data
      })
      getTreeNode({ osType: 2 }).then(respond => {
        this.typeTreeDataLinux = respond.data
      })
      getTreeNode({ osType: 4 }).then(respond => {
        this.typeTreeDataMac = respond.data
      })
    },
    handleApp(isExcept) {
      this.isAddExceptApp = isExcept
      this.$refs.appImportTable.show()
    },
    handleAppCreate(isExcept) {
      this.isAddExceptApp = isExcept
      this.$refs.appImportTable.showBatchCreate(false)
    },
    handleDeleteCheckedApp(isExcept) {
      this.isAddExceptApp = isExcept
      const rows = this.appGridTable().getSelectedDatas()

      // 待删除数据的key的set集合
      const toDeleteKeys = new Set();

      // 预生成待删除标记集合
      rows.forEach(item => {
        // 处理父级元素
        const { typeId, dataId, itemType } = item
        // 生成 typeId 和 dataId 两个可能的删除键
        toDeleteKeys.add(`${typeId}-${itemType}-${isExcept}`);
        toDeleteKeys.add(`${dataId}-${itemType}-${isExcept}`);

        // 生成子级删除标记
        if (item.children) {
          item.children.forEach(child => {
            toDeleteKeys.add(`${child.dataId}-${child.itemType}-${isExcept}`);
          });
        }
      });
      
      // 根据删除标记集合，过滤数据
      const filteredArray = this.temp.checkApp.filter(({ id, itemType, isExcept }) => {
        return !toDeleteKeys.has(`${id}-${itemType}-${!!isExcept}`);
      });
      this.$set(this.temp, 'checkApp', filteredArray)
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.$refs['stgDlg'].show(row, this.formable)
    },
    handleShow(row, isGenerateStrategy) {
      this.resetTemp()
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    formatRowData(rowData) {
      rowData.executeCode = []
      if (rowData.action) {
        rowData.executeCode.push([2].indexOf(rowData.blockType) < 0 ? 1 : 3)
      }
      if (rowData.ruleId) {
        rowData.executeCode.push([2].indexOf(rowData.blockType) < 0 ? 2 : 4)
      }
    },
    formatFormData(formData) {
      if (!this.isDoResponse(formData)) {
        formData.ruleId = null
      }
      formData.action = this.isDoLimit(formData) ? 1 : 0
    },
    submitEnd(dlgStatus) {
      // 更新缓存的所有的程序
      this.getNowPackageList().then(() => {
        this.formatDataTree()
      })
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    appAddSubmitEnd(data, dlgStatus) {
      if (data.innerAdd) {
        return
      }
      const appBean = {
        id: data.id,
        md5Level: data.md5Level,
        checkMd5: data.checkMd5,
        processName: data.processName,
        productVersion: data.productVersion,
        itemType: 1,  // 记录类型：1应用程序2类别
        typeId: data.typeId,
        isExcept: this.isAddExceptApp ? 1 : 0
      }
      // 如果有相同程序的相同版本 就不进行添加
      const sameAppIndex = this.temp.checkApp.findIndex(v => v.processName == appBean.processName && v.productVersion == appBean.productVersion && v.itemType == appBean.itemType)
      if (sameAppIndex != -1) {
        // 要更新现有的数据
        this.$set(this.temp.checkApp, sameAppIndex, appBean)
      } else {
        // 删除已有相同的程序
        const index = this.temp.checkApp.findIndex(v => v.id == data.id && appBean.isExcept == v.isExcept && v.itemType == appBean.itemType)
        if (index != -1) {
          this.$set(this.temp.checkApp, index, appBean)
        } else {
          this.temp.checkApp.unshift(appBean)
        }
      }
    },
    appMd5ImportSubmitEnd(data, editMode) {
      // editMode编辑模式，0-删除，1-添加程序，2-添加类别 3修改程序 4-修改类别
      if (editMode === 0) {
        // 创建待删除键的集合
        const deleteKeys = new Set(data.map(({ id, itemType }) => `${id}-${itemType}`));
        // 使用filter一次性过滤
        this.temp.checkApp = this.temp.checkApp.filter(({ id, itemType }) => 
          !deleteKeys.has(`${id}-${itemType}`)
        );
      } else if (editMode === 4) {
        const id = data.id.replace('G', '')
        const key = `${id}-2`
        const typeData = this.temp.checkApp.find(({ id, itemType }) => `${id}-${itemType}` == key)
        if (typeData) {
          this.$set(typeData, 'processName', data.label)
        }
      } else {
        // 创建一个 Map 用于存储列表中的元素，以方便快速查找
        const listMap = new Map();
        this.temp.checkApp.forEach((item) => {
          const key = `${item.id}-${item.itemType}`;
          listMap.set(key, item);
        });
        // 提前计算 itemType
        const itemType = editMode === 2 ? 2 : 1;
        
        data.forEach(item => {
          const id = itemType === 1 ? item.id : item.dataId;
          const key = `${id}-${itemType}`;
          const existApp = listMap.get(key);
          if (!existApp) {
            if (editMode === 1) {
              this.appAddSubmitEnd(item)
            } else if (editMode === 2 && item.id != '0') {
              const appBean = {
                id: item.dataId,
                processName: item.label,
                itemType: 2,  // 记录类型：1应用程序2类别
                isExcept: this.isAddExceptApp ? 1 : 0
              }
              this.temp.checkApp.unshift(appBean)
              // 同时更新 Map
              listMap.set(`${item.dataId}-2`, appBean);
            }
          } else {
            const { md5Level, checkMd5, typeId } = item
            Object.assign(existApp, { md5Level, checkMd5, typeId })
            // 修改程序不需要变更
            if (editMode != 3) {
              existApp.isExcept = this.isAddExceptApp ? 1 : 0
            }
          }
        })
      }
      this.reloadAppRowData()
      // 更新缓存的所有的程序
      this.getNowPackageList().then(() => {
        this.formatDataTree()
      })
    },
    md5LevelFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      }
      if (row.checkMd5 == 1 || row.isCheckMd5 == 1) {
        return this.$t('text.enable')
      }
      return this.$t('text.disable2')
    },
    nameFormatter(row, data) {
      if (row.itemType == 2) {
        const treeData = this.typeTreeData
        const type = treeData.find(node => node.dataId == row.typeId)
        const label = type ? type.label : ''
        return `<span class='el-icon el-icon-folder'></span> ${label}`
      } else {
        return row.processName
      }
    },
    itemTypeFormatter(row, data) {
      return this.itemTypeMap[data]
    },
    infoFormatter(row, data) {
      return data
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
