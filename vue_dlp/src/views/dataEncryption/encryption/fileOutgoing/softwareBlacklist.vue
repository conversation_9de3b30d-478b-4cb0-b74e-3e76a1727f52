<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <download-tool></download-tool>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :selectable="selectable"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="900px"
      @dragDialog="handleDrag"
      @close="appClose"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 800px; margin-left: 30px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30" />
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit />
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-container>
          <el-main>
            <div class="toolbar">
              <el-button v-if="formable" icon="el-icon-plus" size="small" @click="handleAppCreate1()">
                {{ $t('button.add') }}
              </el-button>
              <el-button v-if="formable" icon="el-icon-delete" size="small" :disabled="!appDeleteable" @click="deleteApp">
                {{ $t('button.delete') }}
              </el-button>
              <el-button v-if="formable" icon="el-icon-plus" size="small" @click="handleBlacklistAppImport">
                {{ $t('pages.accessSoftwareBlacklistLibraryImport') }}
              </el-button>
              <div class="searchCon">
                <el-tooltip placement="top">
                  <div slot="content">{{ $t('pages.outgoingProcess_text1') }}</div>
                  <el-input v-model="softKeyword" v-trim clearable style="width: 150px;height: 27px;" @keyup.enter.native="handleFilterSoft"></el-input>
                </el-tooltip>
                <el-button icon="el-icon-search" size="small" @click="handleFilterSoft">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <grid-table
              ref="softTable"
              :height="240"
              :row-no-label="$t('table.keyId')"
              :show-pager="false"
              :col-model="colModel2"
              :row-datas="sortTableDataTemp"
              @selectionChangeEnd="appSelectionChangeEnd"
            />
          </el-main>
        </el-container>
        <div style="color: #0c60a5;padding-top: 10px">
          {{ $t('pages.outgoingProcess_text8') }}
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createStrategy"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createStrategy():updateStrategy()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="appClose">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 分组弹窗-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="appTypeFormMap[appTypeFormStatus]"
      :visible.sync="dialogAppTypeFormVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="appTypeForm" :rules="grouprules" :model="appTypeTemp" label-position="right" label-width="80px" style="width: 300px; margin-left:20px;">
        <FormItem :label="$t('pages.groupName1')" prop="name">
          <el-input v-model="appTypeTemp.name" maxlength="30"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="appTypeFormStatus==='create'?createNode():updateNode()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogAppTypeFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 修改程序 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="this.$t('pages.updateProcess')"
      :visible.sync="appFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="appDataForm" :rules="appRules" :model="appTemp" label-position="right" label-width="120px" style="width: 500px; margin-left: 25px;">
        <FormItem :label="$t('table.processName')" prop="processName">
          <el-input v-model="appTemp.processName" :disabled="true"/>
        </FormItem>
        <FormItem :label="$t('pages.appType')" prop="classId">
          <el-row>
            <el-col :span="addGroupAble ? 21 : 24">
              <el-select v-model="appTemp.classId">
                <el-option v-for="item in typeTreeData" :key="item.dataId" :value="item.dataId" :label="item.label"/>
              </el-select>
            </el-col>
            <el-col v-show="addGroupAble" style="padding-top:1px" :span="3">
              <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="appTemp.remark" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('table.softSign')" prop="softSign">
          <el-input v-model="appTemp.softSign" :disabled="true"/>
        </FormItem>
        <FormItem :label="$t('table.fileMd5')" prop="fileMd5">
          <el-input v-model="appTemp.fileMd5" :disabled="true"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="updateApp">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="appFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importOutgoingProcessStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />

    <software-blacklist-batch-add-dlg
      ref="batchUpload"
      :verify-model-able="isVerifyModelAble"
      :rules-group-label="$t('pages.outgoingProcess_text12')"
      :ini-import-able="true"
      @createTypeAfter="createTypeAfter"
      @submitEnd="appBatchSubmitEnd"
    />

    <import-table-dlg
      ref="appImportTable"
      group-node-key="classId"
      :exits-list-data="sortTableData"
      :elg-title="$t('pages.importApp')"
      :group-root-name="$t('pages.processType')"
      :search-info-name="$t('table.processName')"
      :confirm-button-name="$t('pages.addApp')"
      :prompt-message="$t('pages.outgoingProcess_text9')"
      :group-title="$t('pages.appType')"
      :group-label="$t('pages.appType')"
      :rules-group-label="$t('pages.outgoingProcess_text12')"
      :col-model="importColModel"
      :delete-col-model="deleteColModel"
      :get-relation-stg-by-ids="getStrategyByOutgoingProcessIds"
      :list="getAppInfoList"
      :load-group-tree="getGroupTree"
      :create-group="createGroup"
      :update-group="updateGroup"
      :delete-group="deleteGroup"
      :count-by-group="countByGroupId"
      :get-group-by-name="getAppTypeByName"
      :delete="deleteAppInfo"
      :get-list-by-group-ids="getListByGroupIds"
      :handle-create-elg="handleAppCreate"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
      @elgCancelAfter="elgCancelAfter"
      @changeGroupAfter="changeGroupAfter"
    />

    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.appType')"
      :group-tree-data="typeTreeData"
      :group-label="$t('pages.appType')"
      :rules-group-label="$t('pages.outgoingProcess_text12')"
      :edit-valid-func="getAppTypeByName"
      :add-func="createGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>
<script>
import { getStrategyPage, getStrategyByName, getGroupTree, listProcess,
  createStrategy, updateStrategy, deleteStrategy, deleteProcess,
  addProcess, updateProcess, deleteAppType, addAppType, updateAppType,
  getAppTypeByName, countInfoByGroupId, listProcessPage, getStrategyByOutgoingProcessIds
} from '@/api/dataEncryption/encryption/softwareBlacklist'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter, deepMerge } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import SoftwareBlacklistBatchAddDlg from './softwareBlacklistBatchAddDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool'

export default {
  name: 'SoftwareBlacklist',
  components: { ImportTableDlg, ImportStg, SoftwareBlacklistBatchAddDlg, EditGroupDlg, DownloadTool },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 275,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'id', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      colModel2: [
        { prop: 'processName', label: 'processName', width: '120', sort: true },
        { prop: 'flagList', label: 'flagList', width: '330', type: 'select', multiple: true, optionDisabled: this.flagDistable, change: this.selectChange,
          options: [
            { label: this.$t('table.softwareName'), value: 1 },
            { label: this.$t('table.softSign'), value: 2 },
            { label: this.$t('table.fileMd5'), value: 4 }
          ]
        },
        { prop: 'remark', label: 'remark', width: '150', sort: true },
        { prop: 'softSign', label: 'softSign', width: '150', sort: true },
        { prop: 'fileMd5', label: 'fileMd5', width: '150', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', click: this.handleUpdateList }
          ]
        }
      ],
      importColModel: [
        { prop: 'processName', label: 'processName', width: '150', sort: true },
        { prop: 'classId', label: 'typeId', width: '100', sort: true, formatter: this.classIdFormatter },
        { prop: 'remark', label: 'remark', width: '150', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdateListData }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      flagOptions: [
        { label: this.$t('table.softwareName'), value: 1 },
        { label: this.$t('table.softSign'), value: 2 },
        { label: this.$t('table.fileMd5'), value: 4 }
      ],
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      softKeyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        remark: '',
        active: false,
        softList: [],
        objectType: undefined,
        objectId: undefined,
        alarmSetupId: 0,
        ruleId: ''
      },
      treeData: [{ id: -1, dataId: -1, label: this.$t('pages.configurated'), parentId: 0 }],
      selectTreeId: -1,
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('route.softwareBlacklist'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('route.softwareBlacklist'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileName: { required: true, message: this.$t('pages.chooseFile'), trigger: 'blur' }
        // classId: { required: true, message: this.$t('components.required'), trigger: 'blur' }
      },
      appAddBtnAble: false,
      appDeleteable: false,
      appFormStatus: '',
      appTypeFormMap: {
        update: this.i18nConcatText(this.$t('pages.appType'), 'update'),
        create: this.i18nConcatText(this.$t('pages.appType'), 'create')
      },
      appTypeFormStatus: '',
      dialogAppTypeFormVisible: false,
      appTypeTemp: {},
      defaultAppTypeTemp: { // 表单字段
        id: undefined,
        parentId: 0,
        name: ''
      },
      grouprules: {
        name: [
          { required: true, message: this.$t('pages.outgoingProcess_text12'), trigger: 'blur' },
          { validator: this.appTypeNameValidator, trigger: 'blur' }
        ]
      },
      sortTableDataTemp: [],  //  临时数据
      sortTableData: [],
      appendToBody: false,
      typeTreeData: [], // 程序类别数据
      deleteProcessFlag: false,
      appRules: {
        processName: [
          { required: true, message: this.$t('pages.appGroup_text25'), trigger: 'blur' }
        ],
        classId: [
          { required: true, message: this.$t('pages.outgoingProcess_text5'), trigger: 'blur' }
        ]
      },
      needIds: [],
      appTemp: {},
      appFormVisible: false,
      updateFlag: false,
      isVerifyModelAble: true,  //  是否有验证方式
      dlgSubmitting: false,
      deleteColModel: [
        { prop: 'processName', label: 'processName', width: '100', sort: true },
        { prop: 'strategyRelations', label: 'relationStgName', width: '100', formatter: this.strategyRationFormatter }
      ],
      addGroupAble: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyTable']
    },
    flagMap() {
      const map = {}
      this.flagOptions.forEach(item => {
        map[item.value] = item.label
      })
      return map
    }
  },
  watch: {
    sortTableData(val) {
      this.handleFilterSoft()
    },
    needIds(val) {
      if (val.length > 0) {
        this.getListByIds({ ids: val.join(',') }).then(res => {
          this.sortTableData = this.oldFlagListToNew(this.temp.softList || [], res.data) || []
        })
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    //  过滤掉修改操作
    this.colModel2 = this.formable ? this.colModel2 : this.colModel2.filter(item => { return item.label !== 'operate'; });
    //  隐藏修改验证方式的按钮
    !this.formable && this.colModel2.forEach(item => {
      if (item.prop === 'flagList') {
        this.$set(item, 'editIconShow', false)
      }
    });
    hiddenActiveAndEntity(this.colModel, this.treeable)
    //  加载分组类型
    this.loadTypeTree();
  },
  methods: {
    getStrategyByOutgoingProcessIds,
    getListByGroupIds(data) {
      if (data.groupIds) {
        return listProcess({ classIds: data.groupIds })
      }
      return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
    },
    getListByIds(data) {
      if (data.ids) {
        return listProcess({ ids: data.ids })
      }
      return new Promise((resolve, reject) => { resolve({ code: 20000, data: [] }) })
    },
    getGroupTree,
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      enableStgBtn(null, this)
      this.handleFilter()
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    appTypeTree: function() {
      return this.$refs['typeTree']
    },
    flagDistable(item, row, col) {
      if (!this.formable) {
        return !this.formable
      }
      // 如果验证方式是进程名，且进程名为空，就不可以设置
      if (item.value == 1 && !row.processName) {
        return true
      }
      // 如果验证方式是数字签名，且数字签名为空，就不可以设置
      if (item.value == 2 && !row.softSign) {
        return true
      }
      // 如果验证方式是软件指纹，且软件指纹为空，就不可以设置
      if (item.value == 4 && !row.fileMd5) {
        return true
      }
      return false
    },
    createNode() {
      this.submitting = true
      this.$refs['appTypeForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const tempData = Object.assign({}, this.appTypeTemp)
          addAppType(tempData).then(respond => {
            this.submitting = false
            this.loadTypeTree()
            this.dialogAppTypeFormVisible = false
            tempData.id = respond.data.id
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateNode() {
      this.submitting = true
      this.$refs['appTypeForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.appTypeTemp)
          updateAppType(tempData).then(respond => {
            this.submitting = false
            this.loadTypeTree()
            this.dialogAppTypeFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    appTypeNameValidator(rule, value, callback) {
      getAppTypeByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.appTypeTemp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    removeNode(data) {
      countInfoByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.appGroup_text17'), type: 'warning', duration: 2000 })
          return
        }
        this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
          deleteAppType({ id: data.dataId }).then(respond => {
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          })
        }).catch(() => {})
      })
    },
    resetAppTypeTemp() {
      this.appTypeTemp = Object.assign({}, this.defaultAppTypeTemp)
    },
    handleAppTypeCreate(data) {
      this.resetAppTypeTemp()
      this.appTypeFormStatus = 'create'
      this.dialogAppTypeFormVisible = true
      this.$nextTick(() => {
        this.$refs['appTypeForm'].clearValidate()
      })
    },
    handleAppTypeUpdate: function(data) {
      this.resetAppTypeTemp()
      this.appTypeTemp.id = data.dataId
      this.appTypeTemp.name = data.label
      this.appTypeFormStatus = 'update'
      this.dialogAppTypeFormVisible = true
      this.$nextTick(() => {
        this.$refs['appTypeForm'].clearValidate()
      })
    },
    deleteApp() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs['softTable'].getSelectedIds()
        this.deleteSortTableData(toDeleteIds)
      }).catch(() => {})
    },
    appSelectionChangeEnd: function(rowDatas) {
      this.appDeleteable = rowDatas && rowDatas.length > 0
    },
    handleAppCreate1() {
      this.isVerifyModelAble = true
      this.$refs['batchUpload'].show();
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    // 修改程序的限制类别时，把程序的数据保存到策略的应用列表里面
    selectChange: function(row, col) {
      const data = row[col.prop] // 下拉框选中的值
      const rows = this.$refs['softTable'].getSelectedDatas()
      if (rows != null && rows.length > 0) {
        rows.forEach((item, index) => {
          if (item.id != row.id) {
            item.flagList.splice(0)
            data.forEach(flag => {
              // 要设置验证该选项，必须要该选项有值才可以设置
              if ((flag == 1 && item.processName) || (flag == 2 && item.softSign) || (flag == 4 && item.fileMd5)) {
                item.flagList.push(flag)
              }
            })
            this.changeData(item.flagList, item)
          }
        })
      }
      this.changeData(data, row)
    },
    // 修改策略限制程序列表数据
    changeData: function(data, row) {
      data = [...data]
      //  保存已选择了验证方式的数据
      const list = this.temp.softList
      if (data.length == 0) {
        // 去除未选中验证方式的数据
        const index = list.findIndex(item => item.id == row.id)
        index > -1 && list.splice(index, 1)
      } else if (data.length > 0) {
        const item = list.find(item => item.id == row.id)
        if (item) {
          // 修改flagList的值为下拉框的值
          item.flagList.splice(0)
          data.forEach(flag => {
            if ((flag == 1 && item.processName) || (flag == 2 && item.softSign) || (flag == 4 && item.fileMd5)) {
              item.flagList.push(flag)
            }
          })
        } else {
          list.push(JSON.parse(JSON.stringify(row)))
        }
      }
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '-1'
      const able = this.formable
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span v-show={able} class='el-ic'>
            <svg-icon icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleAppTypeCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleAppTypeUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    filterSoft(list) {
      if (this.softKeyword != '') {
        list = list.filter(item => {
          return (item.processName && item.processName.toLowerCase().indexOf(this.softKeyword.toLowerCase()) !== -1) ||
            (item.softSign && item.softSign.toLowerCase().indexOf(this.softKeyword.toLowerCase()) !== -1) ||
            (item.remark && item.remark.toLowerCase().indexOf(this.softKeyword.toLowerCase()) !== -1)
        })
      }
      return list
    },
    handleFilterSoft() {
      this.sortTableDataTemp = this.filterSoft(this.sortTableData)
    },
    handleDrag() {
    },
    resetTemp() {
      this.temp = deepMerge({ softList: [] }, this.defaultTemp)
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        // 然后刷新右边表格数据
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.loadTypeTree()
      this.clearSortTable()
      this.temp = JSON.parse(JSON.stringify(row));
      this.temp.softList = this.temp.softList || []
      this.temp.softList.forEach(item => {
        this.setAppDefaultValue(item);
      })
      //  给sortTable列表赋值
      this.sortTableData = [...this.temp.softList]
      this.query.searchInfo = ''
      this.$nextTick(() => {
        // 然后刷新右边表格数据
        this.$refs['dataForm'].clearValidate()
        this.dialogStatus = 'update'
      })
      this.dialogFormVisible = true
    },
    handleUpdateList(row) {
      this.loadTypeTree()
      this.appTemp = Object.assign({}, row)
      this.addGroupAble = true
      this.appFormVisible = true
    },
    handleUpdateListData(row) {
      this.updateFlag = true
      this.handleUpdateList(row)
      this.addGroupAble = false
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    formatData() {
      this.temp.softList.forEach(item => {
        item.validateFlag = this.getSum(item.flagList)
      })
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createFlag) {
          this.$refs['appImportTable'].refreshTableData()
        } else {
          this.sortTableData.push(data)
        }
        this.createFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$refs['appImportTable'].refreshTableData()
        } else {
          for (let i = 0; i < this.sortTableData.length; i++) {
            if (this.sortTableData[i].id === data.id) {
              this.sortTableData.splice(i, 1)
              this.sortTableData.push(data)
              break;
            }
          }
        }
        this.updateFlag = false
      }
    },
    createStrategy() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.validFlagList(this.sortTableData)) {
            this.$confirmBox(this.$t('pages.outgoingProcess_text14'), this.$t('text.prompt')).then(() => {
              this.createStrategying()
            }).catch(() => {});
          } else {
            this.createStrategying()
          }
        } else {
          this.submitting = false
        }
      })
    },
    createStrategying() {
      this.submitting = true
      this.temp.softList = this.sortTableData.filter(item => { return item.flagList && item.flagList.length > 0 })
      this.formatData()
      //  设置进程
      const tempData = Object.assign({}, this.temp)
      createStrategy(tempData).then(respond => {
        this.submitting = false
        this.dialogStatus = ''
        this.dialogFormVisible = false
        this.clearSortTable()
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.createSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(res => {
        this.submitting = false
      })
    },
    //  过滤没有选中验证方式的进程
    filterNotVerifyModel(processList) {
      return (processList || []).filter(item => {
        return item.flagList && item.flagList.length > 0
      })
    },
    updateStrategy() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.validFlagList(this.sortTableData)) {
            this.$confirmBox(this.$t('pages.outgoingProcess_text14'), this.$t('text.prompt')).then(() => {
              this.updateStrategying()
            }).catch(() => {});
          } else {
            this.updateStrategying()
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateStrategying() {
      this.submitting = true
      this.temp.softList = this.sortTableData.filter(item => { return item.flagList && item.flagList.length > 0 })
      this.formatData()
      const tempData = Object.assign({}, this.temp)
      updateStrategy(tempData).then(respond => {
        this.submitting = false
        this.dialogFormVisible = false
        this.dialogStatus = ''
        this.clearSortTable()
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(res => {
        this.submitting = false
      })
    },
    //  校验验证码方式是否存在未配置的
    validFlagList(softList) {
      softList = softList || []
      let flag = false
      softList.forEach(item => {
        if (item.flagList === undefined || item.flagList === null || item.flagList.length === 0) {
          flag = true
        }
      })
      return flag;
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    strategyFormatter(row) {
      const processNames = row.softList.map(item => {
        const names = this.numToList(item.validateFlag, 3).map(flag => {
          return this.flagMap[flag]
        })
        return item.processName + '(' + names.join(',') + ')'
      })
      return processNames.join('，')
    },
    appBatchSubmitEnd(list, option) {
      this.loadTypeTree()
      //  根据验证方式是否存在
      if (this.isVerifyModelAble) {
        //  过滤掉不符合的验证方式
        list.forEach(item => {
          item.flagList = item.flagList.filter(i => {
            return !this.flagDistable({ value: i }, item)
          })
        });
        this.addSortTableData(list)
        //  添加到temp.softList中
        this.sortTableData.forEach(item => { this.temp.softList && this.temp.softList.push(item) })
      } else {
        this.$refs['appImportTable'].refreshTableData()
      }
      this.isVerifyModelAble = true
    },
    //  关闭
    appClose() {
      this.dialogStatus = ''
      this.dialogFormVisible = false
      this.sortTableData = []
      this.clearSortTable()
      if (this.deleteProcessFlag) {
        this.gridTable.execRowDataApi()
      }
    },
    //  访问黑名单程序库导入
    handleBlacklistAppImport() {
      this.loadTypeTree()
      this.$refs.appImportTable.show()
    },
    //  加载类型
    loadTypeTree() {
      getGroupTree().then(res => {
        (res.data || []).forEach(item => {
          item.dataId = parseInt(item.dataId) || null
        });
        this.typeTreeData = res.data
      })
    },
    //  分页查找
    getAppInfoList(data) {
      return listProcessPage(data);
    },
    //  改变程序类型
    updateGroup(data) {
      return updateAppType(data);
    },
    //  删除程序类型
    deleteGroup(data) {
      return deleteAppType(data);
    },
    createGroup(data) {
      return addAppType(data);
    },
    countByGroupId(data) {
      return countInfoByGroupId(data);
    },
    //  新增进程数据
    createAppInfo(data) {
      return addProcess(data);
    },
    //  更新进程数据
    updateAppInfo(data) {
      data.classId = data.typeId || data.classId
      return updateProcess(data);
    },
    //  删除进程数据
    deleteAppInfo(data) {
      this.deleteProcessFlag = true
      this.deleteProcessList(data.ids || null)
      return deleteProcess(data);
    },
    //  删除进程列表的数据
    deleteProcessList(ids) {
      if (ids === null || ids === '') {
        return;
      }
      const idsArray = ids.split(',') || []
      const idsList = []
      idsArray.forEach(item => idsList.push(parseInt(item)));
      this.deleteSortTableData(idsList)
    },
    //  sortTableData添加数据
    addSortTableData(needAddProcessList) {
      needAddProcessList = JSON.parse(JSON.stringify(needAddProcessList)) || []
      const tempArray = JSON.parse(JSON.stringify(this.sortTableData))
      needAddProcessList.forEach(item => {
        //   过滤掉已经存在的进程
        if (tempArray.find(i => i.id === item.id) === undefined) {
          this.setColModel2(item)
          this.sortTableData.push(item)
        }
      })
    },
    //  重新再次设置程序列表值，防止remark为undefined等情况
    setColModel2(item) {
      item.remark = item.remark || ''
      item.processName = item.processName || ''
      item.softSign = item.softSign || ''
      item.fileMd5 = item.fileMd5 || ''
      item.flagList = item.flagList || []
    },
    //  根据ids删除sortTableData的数据
    deleteSortTableData(needDeleteIds) {
      needDeleteIds = needDeleteIds || []
      needDeleteIds.forEach(id => {
        const index = this.sortTableData.findIndex(item => { return item.id == id });
        index > -1 && this.sortTableData.splice(index, 1)
        const softListIndex = this.temp.softList.findIndex(i => { return i.id == id });
        softListIndex > -1 && this.temp.softList.splice(softListIndex, 1)
      })
    },
    getAppTypeByName(data) {
      return getAppTypeByName(data);
    },
    //  清空程序列表数据
    clearSortTable() {
      this.$refs.softTable && this.$refs.softTable.clearSelection();
      this.sortTableData = []
      this.sortTableDataTemp = []
    },
    updateApp() {
      this.dlgSubmitting = true
      this.$refs['appDataForm'].validate((valid) => {
        if (valid) {
          this.updateAppInfo(this.appTemp).then(res => {
            this.dlgSubmitting = false
            this.isImportElg(res.data, 'update')
            this.appFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    classIdFormatter(row, data) {
      let msg = ''
      if (this.typeTreeData) {
        this.typeTreeData.some(node => {
          if (node.dataId == data) {
            msg = node.label
            return true
          }
        })
      }
      return msg
    },
    getNeedAddIds(needIds) {
      this.loadTypeTree()
      this.needIds = needIds
    },
    //  删除记录
    submitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      this.sortTableData = this.sortTableData.filter(item => { return deleteIdsList.filter(i => i === item.id).length === 0 })
      this.temp.softList = this.sortTableData.filter(item => { return item.flagList && item.flagList.length > 0 })
    },
    elgCancelAfter() {
      this.loadTypeTree();
      const ids = []
      this.sortTableData.forEach(item => { ids.push(item.id) })
      if (ids.length === 0) {
        this.sortTableData = []
        return;
      }
      this.getListByIds({ ids: ids.join(',') }).then(res => {
        this.sortTableData = this.oldFlagListToNew(this.temp.softList || [], res.data) || []
      })
    },
    //  将原来列表数据的flagList赋值给新的列表数据
    oldFlagListToNew(oldArray, newArray) {
      let i = null;
      let item = null
      for (let k = 0; k < newArray.length; k++) {
        item = newArray[k]
        i = this.arrayFind(oldArray, item.id)
        this.$set(item, 'flagList', ((i && i.flagList) || []))
        item.flagList = (i && i.flagList) || []
      }
      return newArray;
    },
    arrayFind(array, findId) {
      for (let i = 0; i < array.length; i++) {
        if (array[i].id == findId) {
          return array[i];
        }
      }
      return null;
    },
    //  弹窗分组的数据发送变化时
    changeGroupAfter() {
      this.loadTypeTree();
    },
    handleAppCreate() {
      this.outerShow = false
      this.isVerifyModelAble = false
      this.$refs.batchUpload.show({ classId: this.selectedClassId })
    },
    strategyRationFormatter(row) {
      return row.strategyRelations !== null && row.strategyRelations.length > 0 ? row.strategyRelations.join(',') : this.$t('pages.noEnableStgRel')
    },
    setAppDefaultValue(data) {
      data.processName = data.processName || ''
      this.$set(data, 'flagList', data.flagList || [])
      data.remark = data.remark || ''
      data.fileMd5 = data.fileMd5 || ''
      data.softSign = data.softSign || ''
    },
    handleTypeCreate() {
      this.loadTypeTree();
      this.$refs['createGroupDlg'].handleCreate();
    },
    createGroupAddEnd(row) {
      this.loadTypeTree();
    },
    createTypeAfter() {
      if (!this.isVerifyModelAble) {
        this.$refs['appImportTable'].loadGroupTreeData()
      }
    }
  }
}
</script>
