<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.enDeFileScan1')"
      :stg-code="146"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createData"
      :update="updateData"
      :get-by-name="getByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <data-editor
          :formable="formable"
          :popover-width="740"
          :updateable="suffixEditable"
          :deletable="suffixDeleteable"
          :add-func="createSuffix"
          :update-func="updateSuffix"
          :delete-func="deleteSuffix"
          :cancel-func="resetSuffixTemp"
          :before-update="beforeUpdateSuffix"
          :before-add="beforeAddSuffix"
        >
          <Form ref="suffixForm" :rules="suffixRules" :model="suffixTemp" label-position="right" label-width="100px" style="width: 650px; margin: 0 auto;">
            <FormItem v-if="operationType === 'update'" :label="$t('pages.processName1')" :tooltip-content="$t('pages.enDeFileScan_Msg4')" prop="fileExe" tooltip-placement="bottom-start">
              <el-button type="primary" size="mini" style="margin-bottom: 0px;margin-top: 2px;" @click="showUpdateAppSelectDlg">
                {{ $t('pages.enDeFileScan_appLibImport') }}
              </el-button>
              <el-input v-model="suffixTemp.fileExe" v-trim style="margin-top: 5px" :title="suffixTemp.fileExe" maxlength="50"></el-input>
            </FormItem>

            <FormItem v-if="operationType === 'create'" :label="$t('pages.processName1')" :tooltip-content="$t('pages.enDeFileScan_Msg4')" prop="processNames" tooltip-placement="bottom-start">
              <el-button type="primary" size="mini" style="margin-bottom: 0px;margin-top: 2px;" @click="showAppSelectDlg">
                {{ $t('pages.enDeFileScan_appLibImport') }}
              </el-button>
              <el-button size="mini" style="margin: 2px 0 0;" @click="handleClear">
                {{ $t('button.clear') }}
              </el-button>
              <tag
                v-model="suffixTemp.processNames"
                :border="true"
                :list="suffixTemp.processNames"
                :overflow-able="true"
                max-height="150px"
                :disabled="!formable"
                style="margin-top: 5px;"
                @tagChange="tagChange"
              />
            </FormItem>

            <FormItem :label="$t('pages.enDeFileScan_Msg5')" :tooltip-content="$t('pages.enDeFileScan_Msg6')" prop="fileSuffix" tooltip-placement="bottom-start">
              <el-input v-model="suffixTemp.fileSuffix" v-trim class="input-with-button" :maxlength="suffixMaxLength"></el-input>
              <el-button type="primary" size="small" style="height: 30px; padding: 0;" @click="handleFileSuffixImport('fileSuffix')">
                <svg-icon icon-class="import" />
              </el-button>
            </FormItem>

            <FormItem :label="$t('pages.enDeFileScan_Msg7')" :tooltip-content="$t('pages.enDeFileScan_Msg8')" prop="filterSuffix" tooltip-placement="bottom-start">
              <el-input v-model="suffixTemp.filterSuffix" v-trim class="input-with-button" :maxlength="suffixMaxLength"></el-input>
              <el-button type="primary" size="small" style="height: 30px; padding: 0;" @click="handleFileSuffixImport('filterSuffix')">
                <svg-icon icon-class="import" />
              </el-button>
            </FormItem>
          </Form>
        </data-editor>
        <grid-table
          ref="suffixList"
          :height="230"
          :multi-select="true"
          :show-pager="false"
          :col-model="suffixColModel"
          :row-datas="temp.detailList"
          @selectionChangeEnd="suffixSelectionChange"
          @row-click="rowClick"
        />
      </template>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
    <app-select-dlg ref="processLib" :append-to-body="true" @select="importProcess"/>
    <app-select-dlg ref="updateProcessLib" :multiple="false" :append-to-body="true" @select="updateImportProcess"/>
  </div>
</template>

<script>
import { getByName, createData, updateData } from '@/api/dataEncryption/encryption/enDeFileScan'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg';

export default {
  name: 'EnDeFileScanDlg',
  components: { FileSuffixLibImport, AppSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        type: 1,
        detailList: [],
        entityType: undefined,
        entityId: undefined,
        processNames: []
      },
      suffixTemp: [],
      suffixColModel: [
        { prop: 'fileExe', label: 'processName1', width: '150', sort: true },
        { prop: 'fileSuffix', label: 'monitorSuffix', width: '150', sort: true },
        { prop: 'filterSuffix', label: 'filterSuffix', width: '150', sort: true }

      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      suffixRules: {
        fileSuffix: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        fileExe: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        filterSuffix: [
          { validator: this.suffixValid, trigger: 'blur' }
        ],
        processNames: [
          { required: true, validator: this.processNamesValidator, trigger: 'change' }
        ]
      },
      suffixEditable: false,
      suffixDeleteable: false,
      defaultSuffixDlgVisible: false,
      submitting: false,
      slotName: undefined,
      importFileSuffixType: '',
      suffixMaxLength: 200,

      operationType: ''
    }
  },
  computed: {

  },
  created() {
    this.resetSuffixTemp()
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createData,
    updateData,
    getByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    resetSuffixTemp() {
      this.suffixTemp = { filterSuffix: '', fileSuffix: '', type: 1, fileExe: '', processNames: [] }
      const table = this.$refs['suffixList']
      if (table) {
        table.setCurrentRow()
      }
    },
    handleDrag() {
    },
    suffixValid(rule, value, callback) {
      if (value != '*.*') {
        callback()
      } else {
        callback(new Error(this.$t('pages.enDeFileScan_Msg9')))
      }
    },
    handleImportExt() {
      this.defaultSuffixDlgVisible = true
      this.$refs.suffixTree && this.$refs.suffixTree.$refs.tree.setCheckedKeys([])
    },
    suffixSelectionChange(rowDatas) {
      this.suffixDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.suffixEditable = true
      } else {
        this.suffixEditable = false
        this.resetSuffixTemp()
      }
      this.$refs['suffixForm'] && this.$refs['suffixForm'].clearValidate()
    },
    rowClick(rowData, column, event) {
      if (this.currentRow != rowData) {
        this.currentRow = rowData
      } else {
        this.currentRow = null
        this.resetSuffixTemp()
      }
    },
    exitItem(fileExe) {
      const processArr = this.temp.detailList.map(item => {
        return item.fileExe
      })
      return processArr.includes(fileExe)
    },
    dataRepeatValidator(list, temp) {
      for (let i = 0; i < list.length; i++) {
        const { id, filterSuffix, fileSuffix, fileExe } = list[i]
        if (filterSuffix === temp.filterSuffix &&
          fileSuffix === temp.fileSuffix &&
          fileExe === temp.fileExe && id !== temp.id
        ) {
          return true
        }
      }
      return false
    },
    createSuffix() {
      let validate
      this.$refs['suffixForm'].validate((valid) => {
        if (valid) {
          // 进程名称数据不能重复
          const suffix = JSON.parse(JSON.stringify(this.suffixTemp))
          let flag = true
          let reFlag = false
          this.suffixTemp.processNames = this.suffixTemp.processNames.filter(name => {
            suffix.fileExe = name
            flag = this.dataRepeatValidator(this.temp.detailList, suffix);
            if (flag) {
              reFlag = true
            }
            return !flag;
          });

          const processNames = [...this.suffixTemp.processNames]
          this.suffixTemp.processNames = undefined
          for (let i = 0, len = processNames.length; i < len; i++) {
            this.temp.detailList.unshift(Object.assign({
              id: new Date().getTime() + i,
              type: 1,
              fileSuffix: this.suffixTemp.fileSuffix,
              filterSuffix: this.suffixTemp.filterSuffix,
              fileExe: processNames[i]
            }, { id: new Date().getTime() + i }))
          }

          if (reFlag) {
            this.$message({
              type: 'error',
              message: this.$t('pages.enDeFileScan_dataExisted')
            })
          }

          this.resetSuffixTemp()
          validate = valid
        }
      })
      return validate
    },
    beforeUpdateSuffix() {
      this.operationType = 'update'
      this.suffixTemp = JSON.parse(JSON.stringify(this.$refs['suffixList'].getSelectedDatas()[0]))
    },
    beforeAddSuffix() {
      this.operationType = 'create'
    },
    updateSuffix() {
      let validate
      this.$refs['suffixForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.suffixTemp)

          // 进程名称数据不能重复
          if (this.dataRepeatValidator(this.temp.detailList, this.suffixTemp)) {
            this.$message({
              type: 'error',
              message: this.$t('pages.dataDuplication')
            })
            return
          }
          for (let i = 0, size = this.temp.detailList.length; i < size; i++) {
            const data = this.temp.detailList[i]
            if (rowData.id === data.id) {
              this.temp.detailList.splice(i, 1, rowData)
              break
            }
          }
          this.resetSuffixTemp()
          validate = valid
        }
      })
      return validate
    },
    deleteSuffix() {
      const toDeleteIds = this.$refs['suffixList'].getSelectedIds()
      for (let i = 0; i < this.temp.detailList.length; i++) {
        const data = this.temp.detailList[i]
        if (toDeleteIds.indexOf(data.id) >= 0) {
          this.temp.detailList.splice(i, 1)
          i--
        }
      }
      this.resetSuffixTemp()
    },
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.resetSuffixTemp()
        this.$refs.suffixForm && this.$refs.suffixForm.clearValidate()
      })
    },
    handleUpdate(row) {
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.resetSuffixTemp()
        this.$refs.suffixForm && this.$refs.suffixForm.clearValidate()
      })
    },
    arrUnique(arr) {
      const result = {};
      const finalResult = [];
      for (let i = 0; i < arr.length; i++) {
        // 利用对象的键名无法重复的特点，cpmch_id是唯一区别的属性值
        result[arr[i].fileExe] ? '' : result[arr[i].fileExe] = true && finalResult.push(arr[i]);
      }
      return finalResult;
    },
    formatRowData(rowData) {
      // 列表数据赋值行号
      if (rowData.detailList) {
        rowData.detailList.forEach((conf, index) => {
          conf.id = index
        })

        // 对策略历史重复进程去重
        const resArr = this.arrUnique(rowData.detailList)
        rowData.detailList.splice(0)
        rowData.detailList = resArr
      }
    },
    formatFormData(formData) {
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    handleFileSuffixImport(type) {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
      this.importFileSuffixType = type
    },
    duplicateRemoval(nv, ov) {
      let union_suffix = null
      if (ov == null || ov === '') {
        union_suffix = [...new Set(nv.split('|'))].join('|')
      } else {
        union_suffix = [...new Set((ov + '|' + nv).split('|'))].join('|')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      return union_suffix
    },
    importFileSuffix(suffix) {
      if (this.importFileSuffixType === 'fileSuffix') {
        this.suffixTemp.fileSuffix = this.duplicateRemoval(suffix, this.suffixTemp.fileSuffix)
        this.$refs['suffixForm'].validateField('fileSuffix')
      } else if (this.importFileSuffixType === 'filterSuffix') {
        this.suffixTemp.filterSuffix = this.duplicateRemoval(suffix, this.suffixTemp.filterSuffix)
      }
    },

    //  当列表发生改变时，校验名称是否符合规则
    tagChange(names) {
      names = this.verifyExeNames(names)
      this.suffixTemp.processNames = names;
      this.$refs['suffixForm'].validateField('processNames')
    },
    //  校验进程名集合
    verifyExeNames(names) {
      names = this.filterRepetitionData(names);
      names = this.verifyExeNameExits(names);
      return names;
    },
    //  校验进程名是否已存在
    verifyExeNameExits(names) {
      //  校验进程是否已存在
      const oldLen = names.length
      const exitNames = []    //  已存在的进程名
      names = names.filter(name => {
        const flag = this.temp.detailList.findIndex(item => { return item.processName === name }) === -1;
        if (!flag) {
          exitNames.push(name)
        }
        return flag;
      })
      if (names.length < oldLen) {
        this.$message({
          message: this.$t('pages.enDeFileScan_processNameExisted', { processNames: exitNames.join(',') }),
          type: 'warning',
          duration: 3000
        })
      }
      return names;
    },
    //  过滤重复数据
    filterRepetitionData(list) {
      const tList = []
      list.forEach(item => {
        if (!tList.includes(item)) {
          tList.push(item)
        }
      })
      return tList;
    },
    showAppSelectDlg() {
      this.$refs['processLib'].show()
    },
    showUpdateAppSelectDlg() {
      this.$refs['updateProcessLib'].show()
    },
    processNamesValidator(rule, value, callback) {
      if (this.suffixTemp.processNames.length === 0) {
        callback(new Error(this.$t('pages.enDeFileScan_processNameNotNull')))
      } else {
        callback()
      }
    },
    importProcess(processes) {
      if (this.suffixTemp.processNames === undefined || this.suffixTemp.processNames === null) {
        this.$set(this.suffixTemp, 'processNames', [])
      }
      let processNames = [...this.suffixTemp.processNames];
      (processes || []).forEach(item => {
        processNames.push(item.processName)
      })
      processNames = this.verifyExeNames(processNames)
      this.suffixTemp.processNames = processNames
      this.$refs['suffixForm'].validateField('processNames')
    },
    updateImportProcess(process) {
      if (process) {
        this.suffixTemp.fileExe = process.processName
      }
    },
    handleClear() {
      this.suffixTemp.processNames.splice(0)
      this.$refs['suffixForm'].validateField('processNames')
    }
  }
}
</script>
