<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate()">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="getTab().handleDelete()">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="tabLabel === 'configTab' ? 87 : 73"/>
        <el-button v-show="tabLabel === 'configTab'" v-permission="'143'" icon="el-icon-upload2" size="mini" @click="handleImportFile">{{ $t('button.import') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <el-tabs ref="tabs" v-model="tabLabel" type="card" style="height: calc(100% - 40px);" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.encryptionPolicy')" name="strategyTab">
          <div class="table-container">
            <grid-table
              ref="strategyListTable"
              :col-model="colModel"
              :row-data-api="rowDataApi"
              :selectable="selectable"
              :after-load="afterLoad"
              @selectionChangeEnd="handleSelectionChange"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane v-if="hasPermission('E2B')" :label="$t('pages.processStgLib_Msg57')" name="configTab">
          <process-stg-config
            ref="stgConfig"
            :parent-vm="this"
            :strategy-tree="strategyTree()"
            :handle-update="handleUpdate"
            @handleEntityClick="entityClick"
            @selectChange="selectRowChangeFunc"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <process-stg-dlg ref="strategyTabDlg" :active-able="treeable" :entity-node="checkedEntityNode" @submitEnd="submitEnd"/>
    <process-stg-config-dlg ref="configTabDlg" :active-able="treeable" :entity-node="checkedEntityNode" @submitEnd="submitEnd"/>
    <import-stg ref="importProcessDlg" accept=".tipa" data-type="1" :os-able="true" :term-able="false" :user-able="listable && treeable" @success="importProcessSuccess"/>
  </div>
</template>

<script>
import ImportStg from '@/views/common/importStg'
import ProcessStgConfig from './config'
import ProcessStgDlg from './editDlg'
import ProcessStgConfigDlg from './configEditDlg'
import { getStrategyPage, deleteStrategy } from '@/api/dataEncryption/encryption/processStg'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter, initTimestamp } from '@/utils'
import { stgActiveIconFormatter, osTypeIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'

export default {
  name: 'ProcessStg',
  components: { ImportStg, ProcessStgConfigDlg, ProcessStgDlg, ProcessStgConfig },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    tabName: { type: String, default: 'strategyTab' }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'Data', label: 'stgMessage', width: '200', formatter: this.strategyFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      encModeOptions: { 1: this.$t('pages.processStgMsg'), 3: this.$t('pages.processStgMsg1')/*, 5: '打开文件后加密'*/ },
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      tabLabel: this.tabName,
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: { }, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        encMode: '3',
        processList: [],
        processStgIds: []
      },
      dialogFormVisible: false,
      dialogStatus: '',
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      },
      submitting: false,
      checkedEntityNode: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  created() {
    initTimestamp(this)
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    this.tabLabel = this.$route.query.tabName || this.tabLabel
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
    this.tabLabel = this.$route.query.tabName || this.tabLabel
    this.gridTable && this.gridTable.execRowDataApi()
  },
  methods: {
    getTab() {
      return this.tabLabel === 'strategyTab' ? this : this.$refs['stgConfig']
    },
    strategyTree: function() {
      return this.$refs['strategyTargetTree']
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    tabClick(pane, event) {
      this.handleFilter()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      enableStgBtn(null, this)
      this.handleFilter()
    },
    selectRowChangeFunc(rowDatas) {
      this.deleteable = false
      if (rowDatas && rowDatas.length > 0) {
        this.deleteable = true
      }
    },
    searchData(query) {
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.getTab().searchData(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs[this.tabLabel + 'Dlg'].handleCreate()
    },
    handleUpdate(row) {
      this.$refs[this.tabLabel + 'Dlg'].handleUpdate(row)
    },
    handleImportFile() {
      this.$refs.importProcessDlg.show()
    },
    formatRowData(row) {
      if (row.encMode) row.encMode += ''
    },
    handleImport() {
    },
    handleExport() {
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    submitEnd(dlgStatus) {
      this.getTab().searchData(this.query)
    },
    importProcessSuccess() {
      this.handleFilter()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    encModeFormatter(row, data) {
      return this.encModeOptions[data]
    },
    processNamesFormatter(row, data) {
      let result = ''
      if (data) {
        data.forEach((name) => {
          result += name + '；'
        })
      }
      return result
    },
    strategyFormatter(row) {
      let msg = ''
      msg += `${this.$t('table.encMode')}: ` + this.encModeFormatter(row, row['encMode'])
      msg += ', '
      msg += `${this.$t('table.programControlled')}: ` + this.processNamesFormatter(row, row['processStgNames'])
      return msg
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    }
  }
}
</script>
