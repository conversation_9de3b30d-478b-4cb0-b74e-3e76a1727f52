<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.processStgMsg2')"
      :stg-code="87"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createConfig"
      :update="updateConfig"
      :get-by-name="getConfigByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <div v-if="formable" :body-style="{'padding': '10px'}">
          <data-editor
            :formable="formable"
            :popover-width="680"
            append-to-body
            :updateable="processEditable"
            :deletable="processDeleteable"
            :add-func="createItem"
            :update-func="updateItem"
            :delete-func="deleteItem"
            :cancel-func="cancelItem"
            :before-update="beforeUpdateItem"
            :before-add="beforeAddItem"
          >
            <Form ref="configForm" :rules="configRules" :model="configTemp" label-position="right" label-width="90px">
              <FormItem v-if="operationType === 'update'" :label="$t('pages.executableProgram')" prop="processName">
                <el-upload name="processName" action="1111" accept=".exe" :limit="1" :disabled="isUpdateFormMode" :show-file-list="false" :before-upload="getFileName" style="display: inline-block;">
                  <el-button type="primary" icon="el-icon-upload" :disabled="isUpdateFormMode" style="padding: 7px 13px;"></el-button>
                </el-upload>
                <el-button type="primary" size="mini" @click="updateShowAppSelectDlg">
                  {{ $t('pages.processStg_batchAddImportTitle') }}
                </el-button>
                <el-input v-model="configTemp.processName" class="input-with-button"></el-input>
              </FormItem>

              <FormItem v-if="operationType === 'create'" prop="processNames" :label="$t('pages.executableProgram')">
                <el-upload name="processName" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getFileName" :disabled="isUpdateFormMode" style="display: inline-block;">
                  <el-button type="primary" icon="el-icon-upload" :disabled="isUpdateFormMode" size="mini"></el-button>
                </el-upload>
                <el-button type="primary" size="mini" @click="showAppSelectDlg">
                  {{ $t('pages.processStg_batchAddImportTitle') }}
                </el-button>
                <el-button size="mini" style="margin-left: 0;" @click="handleClear">
                  {{ $t('button.clear') }}
                </el-button>
                <tag
                  v-model="configTemp.processNames"
                  :border="true"
                  :placeholder="$t('pages.process_Msg')"
                  :input-width="200"
                  :overflow-able="true"
                  max-height="150px"
                  :list="configTemp.processNames"
                  :disabled="!formable"
                  style="width: calc(100% - 46px); margin-top: 5px;"
                  @tagChange="tagChange"
                />
              </FormItem>

              <FormItem :label="$t('pages.type')" prop="code">
                <el-select v-model="configTemp.code" class="input-with-button" @change="codeChange">
                  <el-option v-for="(item, index) in codeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </FormItem>

              <FormItem :label="$t('pages.catalogue')" prop="dir">
                <el-input v-model="configTemp.dir" class="input-with-button" type="textarea" rows="2" resize="none" maxlength="100" show-word-limit :placeholder="$t('pages.processStgLib_Msg67')"></el-input>
              </FormItem>

              <FormItem :label="$t('pages.suffixes')" prop="suffix">
                <el-input v-model="configTemp.suffix" class="input-with-button" type="textarea" rows="2" resize="none" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.processStgMsg3')"></el-input>
                <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
                  <el-button type="primary" size="mini" @click="handleFileSuffixImport()">
                    <svg-icon icon-class="import" />
                  </el-button>
                </el-tooltip>
              </FormItem>
            </Form>
          </data-editor>
        </div>
        <grid-table
          ref="processList"
          :height="250"
          :multi-select="formable"
          :show-pager="false"
          :col-model="configColModel"
          :row-datas="temp.config"
          @selectionChangeEnd="selectionChangeEnd"
        />
      </template>
    </stg-dialog>
    <process-import-dlg ref="processLib" :os-type="osType" :append-to-body="true" @importProcess="importProcess"/>
    <process-import-dlg ref="updateProcessLib" :multiple="false" :os-type="osType" :append-to-body="true" @importProcess="updateImportProcess"/>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
  </div>
</template>

<script>
import {
  getConfigByName,
  createConfig,
  updateConfig
} from '@/api/dataEncryption/encryption/processStg'
import { getProcessActionDict, getDictLabel } from '@/utils/dictionary'
import ProcessImportDlg from '@/views/dataEncryption/encryption/processStgLib/processImportDlg'
import fileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg'

export default {
  name: 'ProcessStgConfigDlg',
  components: { ProcessImportDlg, fileSuffixLibImport },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      configColModel: [ // 特殊文件后缀新增界面中使用
        { prop: 'processName', label: 'executableProgram', width: '150' },
        { prop: 'dir', label: 'catalogue', width: '150' },
        { prop: 'suffix', label: 'suffixes', width: '150' },
        { prop: 'code', label: 'type', width: '100', formatter: this.codeFormatter }
      ],
      codeOptionsMap: {
        1: getProcessActionDict(),
        2: [
          { value: 0, label: this.$t('pages.processStgMsg6') },
          { value: 1024, label: this.$t('pages.processStgMsg5') }
        ],
        4: [
          { value: 0, label: this.$t('pages.processStgMsg6') },
          { value: 1024, label: this.$t('pages.processStgMsg5') }
        ]
      },
      codeOptions: [],
      temp: { }, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        config: []
      },
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      },
      configTemp: {},
      configRules: {
        processName: [
          { required: true, message: this.$t('pages.processStgLib_Msg21'), trigger: 'blur' },
          { validator: this.processNameValidator, trigger: 'blur' }
        ],
        code: [
          { required: true, message: this.$t('pages.processStgLib_Msg68'), trigger: 'blur' }
        ],
        dir: [{ required: true, message: this.$t('pages.processStgLib_Msg69'), trigger: 'blur' }],
        suffix: [{ required: true, message: this.$t('pages.processStgLib_Msg70'), trigger: 'blur' }],
        processNames: [
          { required: true, validator: this.processNamesValidator, trigger: 'blur' }
        ]
      },
      processEditable: false,
      processDeleteable: false,
      isUpdateFormMode: false,
      submitting: false,
      slotName: '',
      suffixMaxLength: 100,

      operationType: ''     //  create:创建，update：更新
    }
  },
  computed: {
    osType() {
      if (this.slotName !== '') {
        return parseInt(this.slotName)
      }
      return 1
    }
  },
  created() {

  },
  methods: {
    createConfig,
    updateConfig,
    getConfigByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.codeOptions = this.codeOptionsMap[name]
      if (this.temp.config) {
        // 过滤掉无效的配置，如mac只支持落地加密，其它不支持。但是导入的数据可能包括其它的
        for (let i = 0; i < this.temp.config.length; i++) {
          const label = getDictLabel(this.codeOptions, this.temp.config[i].code)
          if (!label) {
            this.temp.config.splice(i, 1)
            i--
          }
        }
      }
      //  清空选中状态，锁定修改，删除按钮
      this.$nextTick(() => {
        this.$refs['processList'].clearSelection()
        this.processEditable = false
        this.processDeleteable = false
      })
    },
    codeChange(data) {
      this.$refs['configForm'].validateField('code');
      const field = this.operationType === 'create' ? 'processNames' : 'processName';
      this.$refs['configForm'].validateField(field);
    },
    resetConfigTemp() {
      this.configTemp = {
        dir: undefined,
        suffix: undefined,
        processName: undefined,
        code: null,
        processNames: []
      }
      const table = this.$refs['processList']
      if (table) {
        table.setCurrentRow()
      }
      this.operationType = ''
    },
    getFileName(file) {
      if (this.operationType === 'create') {
        let list = [...this.configTemp.processNames]
        list.push(file.name)
        list = this.verifyExeNames(list)
        this.configTemp.processNames = list
        this.$refs['configForm'].validateField('processNames');
      } else if (this.operationType === 'update') {
        this.configTemp.processName = file.name
        this.$refs['configForm'].validateField('processName');
      }
      return false // 屏蔽了action的默认上传
    },
    handleFilter() {
    },
    handleDrag() {
    },
    handleCreate() {
      this.resetConfigTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        if (this.temp.config) {
          this.temp.config.splice(0)
        } else {
          this.temp.config = []
        }
        this.$refs.configForm && this.$refs.configForm.clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetConfigTemp()
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.$refs.configForm && this.$refs.configForm.clearValidate()
      })
    },
    dataRepeatValidator() {
      const list = this.temp.config
      const temp = this.configTemp
      for (let i = 0; i < list.length; i++) {
        const { id, dir, suffix, processName, code } = list[i]
        if (dir === temp.dir &&
          suffix === temp.suffix &&
          processName === temp.processName &&
          code === temp.code && id !== temp.id
        ) {
          return true
        }
      }
      return false
    },
    handleShow: function(row, isGenerateStrategy) {
      this.resetConfigTemp()
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
      this.$nextTick(() => {
        this.$refs.configForm && this.$refs.configForm.clearValidate()
      })
    },
    processCurrentChangeEnd(rowData) {
      if (!rowData) return
      this.configTemp = Object.assign({}, rowData)
      if (!Number.isNaN(Number.parseInt(this.configTemp.code))) {
        this.configTemp.code = Number.parseInt(this.configTemp.code)
      }
      if (rowData) this.processEditable = true
    },
    createItem() {
      let validate
      this.$refs['configForm'].validate((valid) => {
        if (valid) {
          if (this.dataRepeatValidator()) {
            this.$message({
              type: 'error',
              message: this.$t('pages.dataDuplication')
            })
            return false
          }
          //  批量添加
          const rowData = Object.assign({}, this.configTemp)
          rowData.processNames = this.filterExitsData(this.temp.config, rowData)
          for (let i = 0, len = rowData.processNames.length; i < len; i++) {
            const configTemp = JSON.parse(JSON.stringify(rowData))
            configTemp.processNames = undefined
            configTemp.processName = rowData.processNames[i]
            this.temp.config.unshift(Object.assign({}, configTemp, { id: new Date().getTime() + i }))
          }
          this.resetConfigTemp()
        }
        validate = valid
      });
      return validate
    },
    cancelItem: function() {
      this.$refs['processList'].setCurrentRow()
      this.$refs['configForm'].clearValidate()
      this.resetConfigTemp()
    },
    beforeUpdateItem: function() {
      this.configTemp = Object.assign({}, this.$refs['processList'].getSelectedDatas()[0])
      this.operationType = 'update'
    },
    beforeAddItem: function() {
      this.operationType = 'create'
    },
    updateItem() {
      let validate
      this.$refs['configForm'].validate((valid) => {
        if (valid) {
          if (this.dataRepeatValidator()) {
            this.$message({
              type: 'error',
              message: this.$t('pages.dataDuplication')
            })
            return
          }
          const rowData = Object.assign({}, this.configTemp)
          for (let i = 0, size = this.temp.config.length; i < size; i++) {
            const data = this.temp.config[i]
            if (rowData.id === data.id) {
              this.temp.config.splice(i, 1, rowData)
              break
            }
          }
          this.resetConfigTemp()
          validate = valid
        }
      })
      return validate
    },
    deleteItem() {
      const table = this.$refs['processList']
      const toDeleteIds = table.getSelectedIds()
      this.temp.config.splice(0, this.temp.config.length, ...table.deleteRowData(toDeleteIds))
      this.resetConfigTemp()
    },
    formatRowData(rowData) {
      // 列表数据赋值行号
      if (rowData.config) {
        rowData.config.forEach((conf, index) => {
          conf.id = index
        })
      }
    },
    formatFormData(formData) {
      const config = []
      if (formData.config) {
        formData.config.forEach(row => {
          delete row.id
          row.code = Number.parseInt(row.code)
          config.push(row)
        })
      }
      formData.config = config
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    codeFormatter(row, data) {
      return getDictLabel(this.codeOptions, data)
    },
    processNameValidator(rule, value, callback) {
      // 验证只有落地加密和备份过滤支持设置为*.*
      if (value == '*.*' && this.configTemp.code != 1024 && this.configTemp.code != 3) {
        callback(new Error(this.$t('pages.processStgMsg4')))
      }
      callback()
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      let union_suffix
      if (this.configTemp.suffix == null || this.configTemp.suffix === '') {
        union_suffix = [...new Set(suffix.split('|'))].join('|')
      } else {
        union_suffix = [...new Set((this.configTemp.suffix + '|' + suffix).split('|'))].join('|')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      this.configTemp.suffix = union_suffix
      this.$refs['configForm'].validateField('suffix');
    },
    importProcess(processes) {
      if (this.configTemp.processNames === undefined || this.configTemp.processNames === null) {
        this.$set(this.configTemp, 'processNames', [])
      }
      let processNames = [...this.configTemp.processNames];
      (processes || []).forEach(item => {
        processNames.push(item.processName)
      })
      processNames = this.verifyExeNames(processNames)
      this.configTemp.processNames = processNames
      this.$refs['configForm'].validateField('processNames');
    },
    updateImportProcess(process) {
      if (process) {
        this.configTemp.processName = process.processName
      }
    },
    selectionChangeEnd(rowDatas) {
      this.processDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.processEditable = true
      } else {
        this.processEditable = false
      }
    },

    //  当列表发生改变时，校验名称是否符合规则
    tagChange(names) {
      names = this.verifyExeNames(names)
      this.configTemp.processNames = names;
      this.$refs['configForm'].validateField('processNames')
    },
    //  校验进程名集合
    verifyExeNames(names) {
      this.$refs['configForm'] && this.$refs['configForm'].clearValidate('processNames')
      names = this.filterRepetitionData(names);
      // if (names.length === 0) {
      //   this.$refs['configForm'].validate('processNames');
      // }
      return names;
    },
    //  过滤掉列表中已存在的配置信息
    filterExitsData(configs, configTemp) {
      //  已存在的配置信息
      const config = JSON.parse(JSON.stringify(configs));
      //  需要添加的进程配置信息
      configTemp = JSON.parse(JSON.stringify(configTemp));
      if (config.length === 0) {
        return configTemp.processNames || []
      }

      const length = configTemp.processNames.length;
      let processNames = configTemp.processNames || []
      configTemp.processNames = undefined
      configTemp.id = undefined
      processNames = processNames.filter(processName => {
        let isAdd = true
        configTemp.processName = processName
        for (let i = 0; i < config.length; i++) {
          const processRow = config[i]
          processRow.id = undefined
          processRow.processNames = undefined
          if (JSON.stringify(configTemp) === JSON.stringify(processRow)) {
            isAdd = false
            break;
          }
        }
        return isAdd;
      });

      if (length > processNames.length) {
        this.$message({
          message: this.$t('pages.iconRefreshDir_dataExisted'),
          type: 'warning',
          duration: 3000
        })
      }

      return processNames;
    },
    //  过滤重复数据
    filterRepetitionData(list) {
      const tList = []
      list.forEach(item => {
        if (!tList.includes(item)) {
          tList.push(item)
        }
      })
      return tList;
    },
    showAppSelectDlg() {
      this.$refs['processLib'].show()
    },
    updateShowAppSelectDlg() {
      this.$refs['updateProcessLib'].show()
    },
    processNamesValidator(rule, value, callback) {
      if (this.configTemp.processNames.length === 0 && this.operationType === 'create') {
        callback(new Error(this.$t('pages.processStg_processNameNotNull')))
      } else if (this.operationType === 'create' && this.configTemp.processNames.includes('*.*') && this.configTemp.code != 1024 && this.configTemp.code != 3) {
        callback(new Error(this.$t('pages.processStgMsg4')))
      } else {
        callback()
      }
    },
    handleClear() {
      this.configTemp.processNames.splice(0)
      this.$refs['configForm'].validateField('processNames');
    }
  }
}
</script>

<style lang="scss">
.processName .el-form-item__error{
  width:260px
}
</style>
