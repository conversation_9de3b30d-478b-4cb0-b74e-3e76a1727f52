<template>
  <div class="table-container">
    <grid-table
      ref="otherConfigList"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      @selectionChangeEnd="(rowDatas) => {this.$emit('selectChange', rowDatas)}"
    />
  </div>
</template>

<script>
import { getConfigPage, deleteConfig, getConfigByName } from '@/api/dataEncryption/encryption/processStg'
import { buttonFormatter, objectFormatter, hiddenActiveAndEntity } from '@/utils'
import { getProcessActionDict, getDictLabel } from '@/utils/dictionary'
import { stgActiveIconFormatter, osTypeIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'

export default {
  name: 'ProcessStgConfig',
  props: {
    parentVm: { type: Object, default: undefined },
    strategyTree: { type: Object, default: undefined },
    handleUpdate: { type: Function, default: undefined }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this.parentVm),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'config', label: 'stgMessage', width: '200', formatter: this.processNameFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: 100,
          buttons: [
            { label: 'edit', formatter: (row) => buttonFormatter(row, this), click: this.handleUpdate }
          ]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        config: undefined
      },
      codeOptionsMap: {
        1: getProcessActionDict(),
        2: [
          { value: 0, label: this.$t('pages.processStgMsg6') },
          { value: 1024, label: this.$t('pages.processStgMsg5') }
        ],
        4: [
          { value: 0, label: this.$t('pages.processStgMsg6') },
          { value: 1024, label: this.$t('pages.processStgMsg5') }
        ]
      },
      codeOptions: getProcessActionDict(),
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      query: {},
      treeable: true,
      strategyDefType: 0, // 0-应用策略,1-预定义策略
      configBtnMode: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs['otherConfigList']
    }
  },
  watch: {

  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.strategyDefType = 1 // 预定义策略
    } else {
      this.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    rowDataApi: function(option) {
      option.strategyDefType = this.strategyDefType
      return getConfigPage(option)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteConfig({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getConfigByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      this.$emit('handleEntityClick', row, data)
    },
    processNameFormatter(row, data) {
      const that = this
      let result = '';
      (data || []).forEach(config => {
        const label = that.codeFormatter(row, config.code)
        if (label) { // 过滤掉无效的配置，如mac只支持落地加密，其它不支持。但是导入的数据可能包括其它的
          result += config.processName + '(' + label + ')；'
        }
      })
      return result
    },
    codeFormatter(row, data) {
      const ops = this.codeOptionsMap[row.osType]
      return getDictLabel(ops, data)
    },
    groupFormatter(row, data) {
      return this.processStg.label
    }
  }
}
</script>

