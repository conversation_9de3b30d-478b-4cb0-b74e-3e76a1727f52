<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.encryptionPolicy')"
      :stg-code="73"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closeFunc"
    >
      <template :slot="osType">
        <FormItem :label="$t('pages.processStgMsg7')" style="width: 310px;">
          <el-select v-model="temp.encMode" :disabled="!formable">
            <el-option v-for="(label, key) in encModeOptions" :key="key" :label="label" :value="key"></el-option>
          </el-select>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.controlApp') }}</el-divider>
        <div v-if="formable" class="toolbar">
          <el-tooltip class="item" effect="dark" :content="$t('pages.processStgMsg8')" placement="bottom-start">
            <el-button size="small" @click="handleAddApp">
              {{ $t('button.add') }}
            </el-button>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :content="$t('pages.processStgMsg8')" placement="bottom-start">
            <el-button size="small" @click="handleBatchAddApp">
              {{ $t('pages.batchAdd') }}
            </el-button>
          </el-tooltip>
          <el-button size="small" @click="handleImportApp">
            {{ $t('button.import') }}
          </el-button>
          <el-button size="small" :disabled="!appDeleteAble" @click="handleDeleteApp">
            {{ $t('button.delete') }}
          </el-button>
          <span class="searchCon" style="float: right">
            <el-input v-model="searchInfo" clearable :placeholder="$t('pages.processName1')" style="width: 150px;height: 27px;" @keyup.enter.native="handleFilter" />
            <el-button type="primary" icon="el-icon-search" size="small" :title="$t('table.search')" @click="handleFilter"/>
          </span>
        </div>
        <grid-table
          ref="checkedAppGrid"
          :show-pager="false"
          :height="200"
          :col-model="getColModel(osType)"
          :row-data-api="getAppRows"
          @selectionChangeEnd="appSelectChangeEnd"
        />
      </template>
    </stg-dialog>
    <process-select-dlg
      ref="processSelectDlg"
      :os-type="osType"
      @submitEnd="selectAppEnd"
      @selectEnd="selectAppEnd"
      @deleteEnd="deleteAppEnd"
      @selectGroupEnd="selectGroupEnd"
      @deleteGroupEnd="deleteGroupEnd"
      @updateGroupEnd="handleFilter"
    />
  </div>
</template>

<script>
import {
  getStrategyByName, createStrategy, updateStrategy
} from '@/api/dataEncryption/encryption/processStg'
import { getProcessByIds } from '@/api/dataEncryption/encryption/processStgLib'
import ProcessSelectDlg from '@/views/dataEncryption/encryption/processStgLib/processSelectDlg'

export default {
  name: 'ProcessStgDlg',
  components: { ProcessSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      encModeOptions: { 1: this.$t('pages.processStgMsg'), 3: this.$t('pages.processStgMsg1')/*, 5: '打开文件后加密'*/ },
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      temp: { }, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        encMode: '3',
        processList: [],
        processStgIds: []
      },
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      },
      submitting: false,
      appDeleteAble: false,
      searchInfo: undefined,
      osType: undefined
    }
  },
  computed: {

  },
  created() {

  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.osType = name
      this.temp = !slotTemp ? {} : slotTemp
      if (this.$refs['processSelectDlg']) {
        this.$refs['processSelectDlg'].appQuery.groupId = undefined
      }
      // 修改 删除 按钮的状态
      this.appSelectChangeEnd([])
    },
    getColModel(osType) {
      return [
        { prop: 'groupName', label: 'appType', width: '100', sort: true },
        { prop: 'processName', label: 'processName1', width: '200', sort: true },
        { prop: 'decReadSfx', label: 'detailInfo', width: '200', formatter: this.appInfoFormatter },
        { label: 'operate', hidden: !this.formable, type: 'button', fixed: 'right', fixedWidth: '80',
          buttons: [
            { label: 'edit', isShow: (row) => { return row.itemType == 1 }, click: this.handleUpdateApp },
            { label: 'viewProcess', isShow: (row) => { return row.itemType == 2 }, click: this.handleUpdateGroup }
          ]
        }
      ]
    },
    handleFilter() {
      this.$refs['checkedAppGrid'] && this.$refs['checkedAppGrid'].execRowDataApi()
    },
    handleDrag() {
    },
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.searchInfo = ''
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].show(row, this.formable)
      this.searchInfo = ''
      setTimeout(() => {
        this.handleFilter()
      }, 500)
    },
    handleShow: function(row, isGenerateStrategy) {
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
      this.searchInfo = ''
      setTimeout(() => {
        this.handleFilter()
      }, 500)
    },
    handleAddApp() {
      this.$refs.processSelectDlg.showCreate()
    },
    handleBatchAddApp() {
      this.$refs.processSelectDlg.showBatchCreate()
    },
    getProcessListId(processList) {
      const typeIds = []
      const processIds = []
      processList.forEach(item => {
        switch (item.itemType) {
          case 2: typeIds.push(item.id); break;
          case 1: processIds.push(item.id); break;
          default: break;
        }
      })
      var map = new Map()
      // 设置值时进行去重处理
      typeIds.length > 0 ? map.set('type', Array.from(new Set(typeIds))) : null
      processIds.length > 0 ? map.set('process', Array.from(new Set(processIds))) : null
      return map
    },
    handleImportApp() {
      const map = this.getProcessListId(this.temp.processList)
      const typeIds = map.get('type') ? map.get('type').map(id => `G${id}`) : null
      const processIds = map.get('process') || null
      this.$refs.processSelectDlg.show(null, typeIds, processIds)
    },
    handleDeleteApp() {
      const toDelDatas = this.$refs['checkedAppGrid'].getSelectedDatas()
      if (toDelDatas && this.temp.processList) {
        const toDelIdsSet = new Set(toDelDatas.map(({ itemType, id }) => `${itemType}-${id}`))
        this.temp.processList = this.temp.processList.filter(({ itemType, id }) => {
          // 过滤要删除的APP
          return !toDelIdsSet.has(`${itemType}-${id}`)
        })
        this.handleFilter()
      }
    },
    handleUpdateApp(row) {
      if (this.$refs.processSelectDlg) {
        this.$refs.processSelectDlg.showUpdate(row);
      }
    },
    handleUpdateGroup(row) {
      if (this.$refs.processSelectDlg) {
        const map = this.getProcessListId(this.temp.processList)
        const typeIds = map.get('type') ? map.get('type').map(id => `G${id}`) : null
        const processIds = map.get('process') || null
        this.$refs.processSelectDlg.show(row.id, typeIds, processIds);
      }
    },
    closeFunc() {
      this.temp.processList = []
      this.temp.processStgIds = []
      this.$refs['checkedAppGrid'] && this.$refs['checkedAppGrid'].clearRowData()
    },
    appSelectChangeEnd(rowDatas) {
      this.appDeleteAble = rowDatas && rowDatas.length > 0
    },
    // 导入 程序类别
    selectGroupEnd(nodes) {
      // 选中的类别的数据 Map
      const nodeMap = new Map()
      nodes.forEach(node => {
        if (node.id !== '0') {
          const appBean = {
            id: node.dataId,
            processName: node.label,
            itemType: 2  // 记录类型：1应用程序2类别
          }
          nodeMap.set(node.dataId, appBean)
        }
      })

      // 保留的类别的 dataId 的 Set 集合
      const reservedIdsSet = new Set()
      // 类别树数据
      const treeDatas = JSON.parse(JSON.stringify(this.$refs.processSelectDlg.getGroupTreeData()))
      while (treeDatas.length > 0) {
        const item = treeDatas.shift()
        const selectedNode = nodeMap.get(item.dataId);
        // 如果节点是选中的类别，则保留，并且子节点不再遍历
        if (selectedNode) {
          reservedIdsSet.add(item.dataId)
        } else if (item.children && item.children.length) {
          // 如果节点没有被选中，则将子节点添加到遍历列表
          treeDatas.unshift(...item.children)
        }
      }
      // nodeMap 转成 数组
      const nodeDataArray = Array.from(nodeMap, ([key, value]) => value)
      // 合并，过滤
      this.temp.processList = this.temp.processList.concat(nodeDataArray).filter(item => {
        return item.itemType === 1 || (item.itemType === 2 && reservedIdsSet.has(item.id))
      })
      this.handleFilter()
    },
    deleteGroupEnd(id) {
      if (id && this.temp.processList) {
        this.temp.processList = this.temp.processList.filter(item => {
          // 过滤要删除的分组，app不过滤
          return item.id != id || item.itemType == 1
        })
        this.handleFilter()
      }
    },
    deleteAppEnd(ids) {
      if (ids && this.temp.processList) {
        this.temp.processList = this.temp.processList.filter(item => {
          // 过滤要删除的APP，类型不过滤
          return ids.indexOf(item.id) < 0 || item.itemType == 2
        })
        this.handleFilter()
      }
    },
    selectAppEnd(data) {
      if (data && data.length > 0) {
        const dataIds = this.$refs.processSelectDlg.getRemoveCheckedRowKeys()
        this.temp.processList = this.temp.processList.filter(item => {
          return item.itemType === 2 || (item.itemType === 1 && !dataIds.includes(item.id))
        })
        data.forEach(item => {
          const theIndex = this.temp.processList.findIndex(existApp => {
            return existApp.id == item.id && existApp.itemType == 1
          })
          if (theIndex < 0) {
            const appBean = {
              id: item.id,
              md5Level: item.md5Level,
              checkMd5: item.checkMd5,
              isCheckMd5: item.checkMd5,
              processName: item.processName,
              productVersion: item.productVersion,
              itemType: 1,  // 记录类型：1应用程序2类别
              typeId: item.groupId
            }
            this.temp.processList.unshift(appBean)
          }
        })
        this.handleFilter()
      }
    },
    getAppRows() {
      const ids = []
      const groupIds = []
      this.temp.processList.forEach(item => {
        if (item.itemType == 1) {
          ids.push(item.id)
        }
        if (item.itemType == 2) {
          groupIds.push(item.id)
        }
      })
      if (ids.length > 0 || groupIds.length > 0) {
        return getProcessByIds({ ids: ids.join(','), groupIds: groupIds.join(','), searchInfo: this.searchInfo })
      } else {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    formatRowData(row) {
      if (row.encMode) row.encMode += ''
      this.$nextTick(() => {
        if (row.osType == this.osType) {
          this.handleFilter()
        }
      })
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    appInfoFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      }
      let msg = ''
      msg += this.$t('pages.processStgLib_Msg3') + ': ' + (!row.decReadSfx ? this.$t('pages.null') : row.decReadSfx) + ' ; '
      msg += this.$t('pages.processStgLib_Msg4') + ': ' + (!row.encWriteSfx ? this.$t('pages.null') : row.encWriteSfx) + ' ; '
      msg += this.$t('pages.processStgLib_Msg6') + ': ' + (!row.encOpenSfx ? this.$t('pages.null') : row.encOpenSfx) + ' ; '
      if (row.checkMd5 !== 0) {
        msg += this.$t('pages.supportMd5') + ' ; '
      }
      if (row.disableNet !== 0) {
        msg += this.$t('table.restrictInternetAccess') + ' ; '
      }
      if (row.enablePast > 0) {
        msg += this.$t('table.allowPaste') + ' ; '
      }
      return msg
    },
    md5LevelFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      } else if (data === 0) {
        return this.$t('text.disable2')
      } else {
        return this.$t('text.enable')
      }
    },
    enableFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      }
      return data > 0 ? this.$t('pages.allow') : this.$t('pages.notAllow')
    }
  }
}
</script>
<style lang="scss" scoped>
.el-divider--horizontal{
  margin-top: 15px !important;
}
</style>
