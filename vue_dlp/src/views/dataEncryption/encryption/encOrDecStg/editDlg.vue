<template>
  <stg-dialog
    ref="stgDlg"
    :title="$t('pages.encOrDecStg')"
    :width="740"
    :stg-code="113"
    :active-able="activeAble"
    :rules="rules"
    :model="defaultTemp"
    :entity-node="entityNode"
    :create="createStrategy"
    :update="updateStrategy"
    :get-by-name="getStrategyByName"
    :format-row-data="formatRowData"
    @submitEnd="submitEnd"
    @slotChange="slotChange"
  >
    <template :slot="slotName">
      <FormItem>
        <el-switch v-model="temp.activeEnc" :active-value="1" :inactive-value="0" :disabled="!formable" :active-text="$t('pages.batchEncryptionPermission')" @change="() => { temp.activeEncSens = 0 }" />
      </FormItem>
      <!-- <FormItem>
        <el-switch v-model="temp.activeEncSens" :active-value="1" :inactive-value="0" :disabled="!temp.activeEnc" active-text="批量加密启用敏感内容扫描" />
      </FormItem> -->
      <FormItem>
        <el-switch v-model="temp.activeDec" :active-value="1" :inactive-value="0" :disabled="!formable" :active-text="$t('pages.batchDecryptionPermission')" @change="() => { temp.activeDecSens = 0 }" />
      </FormItem>
      <!--<FormItem>
        <el-switch v-model="temp.activeDecSens" :active-value="1" :inactive-value="0" :disabled="!temp.activeDec" active-text="批量解密启用敏感内容扫描" />
      </FormItem>-->
    </template>
  </stg-dialog>
</template>

<script>
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/dataEncryption/encryption/encOrDec'

export default {
  name: 'EncOrDecStgDlg',
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: { }, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        activeEnc: 0,
        activeDec: 0,
        activeEncSens: 0,
        activeDecSens: 0
      },
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      }
    }
  },
  computed: {

  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    handleFilter() {
    },
    handleDrag() {
    },
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(row) {
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    }
  }
}
</script>
