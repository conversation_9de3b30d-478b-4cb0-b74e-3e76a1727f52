<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-permission="'145'" icon="el-icon-download" size="mini" @click="handleExportFunc">{{ $t('button.export') }}</el-button>
        <el-button v-permission="'146'" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <common-downloader
          v-permission="'145'"
          :disabled="!deleteable"
          :name="title + '.xlsx'"
          :button-name="$t('button.export')"
          button-size="mini"
          @download="handleExport"
        />
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <enc-or-dec-stg-dlg
      ref="stgDlg"
      :active-able="treeable"
      :formable="formable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    />
    <import-dlg
      ref="importDlg"
      accept=".xls,.xlsx,.et"
      :title="title"
      template="encOrDec"
      data-type="-3"
      :strategy-type-number="stgCode"
      :object-multiple="true"
      :object-check-strictly="true"
      :user-able="listable && treeable"
      :term-able="false"
      :file-name="title"
      :prop-template-type="2"
      @success="importEndFunc"
    />
    <export-dlg ref="exportDlg" :stg-export="true" :object-type="2" :export-func="exportFunc"/>
  </div>
</template>

<script>
import EncOrDecStgDlg from './editDlg'
import { getStrategyPage, deleteStrategy, exportExcel } from '@/api/dataEncryption/encryption/encOrDec'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportDlg from '@/views/common/import'
import ExportDlg from '@/views/common/export'

export default {
  name: 'EncOrDecStg',
  components: { ExportDlg, EncOrDecStgDlg, ImportDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      stgCode: 113,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'activeEnc', label: 'batchEncryptionPermission', width: '100', formatter: this.activeEncFormatter },
        { prop: 'activeDec', label: 'batchDecryptionPermission', width: '100', formatter: this.activeDecFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {},
      usbGroupTreeData: this.$store.getters.userTree || [],
      deptTreeData: this.$store.getters.deptTree || [],
      searchQuery: null
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    title() {
      return (this.listable && this.treeable) ? this.$t('route.' + this.$route.meta.title) : this.$route.meta.title
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      this.searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(this.searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    importSuccess() {
      this.handleFilter()
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    activeEncFormatter: function(row, data) {
      let result = data && data > 0 ? this.$t('text.yes') : ''
      if (row.activeEncSens) {
        result += this.$t('pages.encOrDecStg_Msg')
      }
      return result
    },
    activeDecFormatter: function(row, data) {
      let result = data && data > 0 ? this.$t('text.yes') : ''
      if (row.activeDecSens) {
        result += this.$t('pages.encOrDecStg_Msg')
      }
      return result
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleExportFunc() {
      this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
    },
    //  导入
    handleImport() {
      this.$refs.importDlg.show()
    },
    exportFunc(formData, opts) {
      opts.topic = 'encOrDecStg'
      if (formData.type == 1) {
        const stgIds = this.gridTable.getSelectedIds()
        return exportExcel({ exportType: formData.type, strategyIds: stgIds.join(','), objectType: this.query.objectType, objectId: this.query.objectId, remark: opts.file.name }, opts)
      } else if (formData.type == 4) {  //  导出分组下所有批量加解密策略
        return exportExcel({ exportType: formData.type, isAll: formData.isAll, stgObjectIds: formData.objectIds, stgObjectGroupIds: formData.objectGroupIds, objectType: formData.objectType }, opts)
      } else {
        const q = Object.assign({}, this.searchQuery, {
          groupId: formData.groupId,
          updateQuery: true,
          exportType: formData.type
        })
        return exportExcel(q, opts)
      }
    },
    //  导出
    handleExport(file) {
      const stgIds = this.gridTable.getSelectedIds()
      const opts = { file, jwt: true, topic: 'encOrDecStg' }
      exportExcel({ strategyIds: stgIds.join(','), objectType: this.query.objectType, objectId: this.query.objectId, remark: file.name }, opts)
    },
    importEndFunc() {
      this.handleFilter()
    }
  }
}
</script>
