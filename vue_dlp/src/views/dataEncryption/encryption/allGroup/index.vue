<template>
  <strategy-group
    ref="group"
    :group-id="0"
    :stg-type-ops="stgTypeOps"
    :is-set-target="false"
    :group-name="$t('table.stgGroup')"
    :target-option="targetOption"
  />
</template>
<script>
import StrategyGroup from '@/views/strategyGroup'
import { stgTypeOps } from '@/views/common/stgTypeOps'

export default {
  name: 'AllGroup',
  components: { StrategyGroup },
  data() {
    const thisStgTypeOps = stgTypeOps.getAll()
    // 排除策略编码
    // const exceptStgCode = []
    // if (exceptStgCode.length > 0) {
    //   for (let i = 0; i < thisStgTypeOps.length; i++) {
    //     if (exceptStgCode.indexOf(thisStgTypeOps[i].number) > -1) {
    //       thisStgTypeOps.splice(i, 1)
    //       i--
    //     }
    //   }
    // }
    return {
      stgTypeOps: thisStgTypeOps,
      targetOption: [
        { value: 1, label: this.$t('pages.terminal') },
        { value: 2, label: this.$t('pages.user') }
      ]
    }
  }
}
</script>

