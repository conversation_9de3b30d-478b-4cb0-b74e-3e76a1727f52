<template>
  <div class="app-container">
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-edit" size="mini" @click="handleConfig">
          {{ $t('pages.mailWhiteList_text1') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.mailWhiteList_text2')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="senderListTable" :multi-select="false" :col-model="colModel" :row-data-api="rowDataApi" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textTitle"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="closed"
    >
      <Form
        ref="dataForm"
        label-position="right"
        label-width="80px"
        :hide-required-asterisk="true"
        style="width: 700px; margin-left: 30px;"
      >
        <el-divider content-position="left">{{ formable ? $t('pages.mailWhiteList_text3') : $t('pages.mailWhiteList_text20') }}</el-divider>
        <div style="height:300px;">
          <import-table
            ref="mailTable"
            :grid-table-height="250"
            :search-info-prompt-message="$t('pages.mailWhiteList_text19')"
            :delete-disabled="!deleteable"
            :col-model="emailColModel"
            :row-datas="tempEmailElgData"
            :row-no-label="$t('table.keyId')"
            :formable="formable"
            :selectable="emailSelectable"
            :handle-create="handleEmailCreateImport"
            :handle-delete="handleEmailDelete"
            :handle-import="handleEmailImport"
            :handle-search="handleEmailSearch"
            @selectionChangeEnd="selectionChangeEnd"
          />
        </div>
        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
        <FormItem label-width="0px">
          <div style="padding-top: 15px; display: flex; flex-direction: row;">
            <div style="margin-right: 15px; flex-shrink: 0; display: inline-block;">
              <el-checkbox v-model="temp.isChecked" :disabled="!formable" >{{ $t('pages.triggerResponseRule') }}</el-checkbox>
            </div>
            <div style="display: inline-block;">
              <ResponseContent
                :show-select="true"
                read-only
                :editable="temp.isChecked && formable"
                :prop-check-rule="temp.isChecked"
                :show-check-rule="false"
                :prop-rule-id="temp.ruleId"
                @getRuleId="getRuleId"
                @validate="(val) => { responseValidate = val }"
              />
            </div>
          </div>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'A52'" :link-url="'/system/baseData/mailLibrary'" :btn-text="$t('pages.maintainMail')"/>
        <el-button v-if="formable" :loading="submitting" type="primary" @click="saveData()">{{ $t('button.save') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 新增，修改邮箱弹窗  - 邮箱信息 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[emailDialogStatus]"
      :visible.sync="emailDialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="emailForm"
        :rules="emailRules"
        :model="emailTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('form.emailName')" prop="name">
          <el-input v-model="emailTemp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.mailLibrary_text3')" prop="address" style="white-space:nowrap; ">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.mailLibrary_address_description">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
          <el-input v-model="emailTemp.address" :maxlength="60" suffix-icon="el-icon-message"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="addGroupAble ? 21 : 24">
              <el-select v-model="emailTemp.groupId" filterable :placeholder="$t('text.select')">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
            </el-col>
            <el-col v-if="addGroupAble" style="padding-top:1px" :span="3">
              <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('form.unit')" prop="unit">
          <el-input v-model="emailTemp.unit" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('form.office')" prop="office">
          <el-input v-model="emailTemp.office" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="emailTemp.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="emailDialogStatus==='emailCreate'?createEmail(): emailDialogStatus==='emailUpdate' ? updateEmail() : {}">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="emailDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-table-dlg
      ref="emailImportTable"
      :exits-list-data="emailElgData"
      :elg-title="$t('pages.importMailLibrary')"
      :group-root-name="$t('pages.mailLibrary')"
      :search-info-name="$t('form.emailName')"
      :confirm-button-name="$t('pages.addMail')"
      :prompt-message="$t('pages.mailWhiteList_text18')"
      :group-title="$t('pages.mailGroup')"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="getGroupTreeNode"
      :create-group="createMailLibGroup"
      :update-group="updateMailLibGroup"
      :delete-group="deleteMailLibGroup"
      :count-by-group="countChildByGroupId"
      :get-group-by-name="getMailLibGroupByName"
      :delete="deleteMailLib"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteMailLibGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleEmailCreate"
      :get-list-by-group-ids="getMailLibraryByGroupIds"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
      @elgCancelAfter="elgCancelAfter"
      @changeGroupAfter="changeGroupAfter"
    />

    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :edit-valid-func="getMailLibGroupByName"
      :add-func="createMailLibGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>

</template>

<script>
import {
  countChildByGroupId, createMailLib, createMailLibGroup, deleteGroupAndData, deleteMailLib, deleteMailLibGroup,
  getGroupTreeNode, getMailLibByAddress, getMailLibGroupByName, getMailLibPage, getMailLibraryByGroupIds, getMailLibraryByIds,
  getTreeNode, moveGroup, moveGroupToOther, saveMailSender, updateMailLib, updateMailLibGroup
} from '@/api/system/baseData/mailLibrary'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import { findNodeLabel } from '@/utils/tree';
import { enableStgDelete, selectable } from '@/utils';
import EditGroupDlg from '@/views/common/editGroupDlg'

export default {
  name: 'MailSender',
  components: { ResponseContent, ImportTable, ImportTableDlg, EditGroupDlg },
  props: {
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'userName', width: '150', sort: 'custom', fixed: true },
        { prop: 'address', label: 'mailAddr', width: '150', sort: 'custom', formatter: this.mailFormatter },
        { prop: 'office', label: 'office', width: '150', sort: 'custom' },
        { prop: 'unit', label: 'unit', width: '150', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '200', sort: 'custom' }
      ],
      query: { // 查询条件
        page: 1,
        type: 1,
        searchInfo: ''
      },
      temp: {},
      defaultTemp: { // 表单字段
        mailIds: [],
        ruleId: undefined,
        isChecked: false
      },
      submitting: false,
      receiverTreeData: [],
      mailTreeData: [],
      checkedTreeIds: [],
      checkedTreeNames: [],
      dialogFormVisible: false,
      textTitle: this.formable ? this.$t('pages.mailWhiteList_text4') : this.$t('pages.mailWhiteList_text21'),
      emailColModel: [
        { prop: 'name', label: 'emailName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'emailAddress', width: '150', sort: true, formatter: this.addressFormatter },
        { prop: 'unit', label: 'unit', width: '150', sort: true },
        { prop: 'office', label: 'office', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleEmailListUpdate }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'emailName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'emailAddress', width: '150', sort: true, formatter: this.addressFormatter },
        { prop: 'unit', label: 'unit', width: '150', sort: true },
        { prop: 'office', label: 'office', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleEmailUpdate }
          ]
        }
      ],
      emailRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'change' }
        ]
      },
      treeSelectNode: [],
      tempEmailElgData: [],
      emailElgData: [],
      emailDialogFormVisible: false,
      emailDialogStatus: '',
      createFlag: false,
      updateFlag: false,
      emailTemp: {},
      emailDefaultTemp: { // 表单字段
        id: undefined,
        name: '',
        address: '',
        office: '',
        unit: '',
        state: 0,
        groupId: undefined,
        type: 0
      },
      deleteable: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.mailWhiteListStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.mailWhiteListStg'), 'create'),
        emailCreate: this.i18nConcatText(this.$t('pages.mailInfo'), 'create'),
        emailUpdate: this.i18nConcatText(this.$t('pages.mailInfo'), 'update')
      },
      needIds: [],
      dlgSubmitting: false,
      addGroupAble: false,
      // 左侧邮箱信息库选择的分组id
      mailGroupSearchId: undefined
    }
  },
  computed: {
  },
  watch: {
    emailElgData(val) {
      this.handleEmailSearch()
    },
    needIds(val) {
      if (val.length > 0) {
        getMailLibraryByIds({ ids: val.join(',') }).then(res => {
          this.emailElgData = res.data || []
        })
      }
    }
  },
  created() {
    this.loadTree()
    this.resetTemp()
  },
  activated() {
    this.loadTree()
    this.gridTable().execRowDataApi()
  },
  methods: {
    getMailLibraryByGroupIds,
    //  加载分组树
    getGroupTreeNode,
    //  创建分组
    createMailLibGroup,
    //  修改分组
    updateMailLibGroup,
    //  删除分组
    deleteMailLibGroup,
    getMailLibGroupByName,
    countChildByGroupId,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    createMailLib,
    updateMailLib,
    deleteMailLib,
    gridTable() {
      return this.$refs['senderListTable']
    },
    tabClick(pane, event) {
      this.handleFilter()
    },
    loadTree() {
      this.loadMailTree()
      this.loadGroupTree()
    },
    loadMailTree: function() {
      getTreeNode().then(respond => {
        respond.data.forEach(node => {
          if (node.children != undefined) {
            node.children.forEach(child => {
              if (child.label.indexOf('$') === child.label.indexOf('<') + 1) {
                child.label = child.label.replace('$', '#').slice(0, child.label.length - 1).concat('#>')
              }
            })
          }
        });
        this.mailTreeData.splice(0, this.mailTreeData.length, ...respond.data)
      })
    },
    closed() {
      this.resetTemp()
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getMailLibPage(searchQuery)
    },
    handleDrag() {},
    handleFilter() {
      this.gridTable().execRowDataApi(this.query)
    },
    handleConfig: function() {
      this.emailElgData = []
      this.$nextTick(() => {
        this.dialogFormVisible = true
        this.$refs['mailTable'] && this.$refs['mailTable'].clearSearchInfo();
        getMailLibPage({ page: 1, type: 1, searchInfo: '' }).then(respond => {
          respond.data.items.forEach(mailData => {
            this.temp.isChecked = mailData.isChecked
            this.temp.ruleId = mailData.ruleId
          })
          this.emailElgData = respond.data.items
        })
      })
    },
    getLabel(id) {
      return this.getGroupNameByDataId(this.mailTreeData, id, 'id') || '';
    },
    formatSubmitParams: function() {
      if (!this.temp.isChecked) {
        this.temp.ruleId = undefined
      }
      this.temp.mailIds = []
      this.checkedTreeIds.splice(0)
      this.checkedTreeNames.splice(0)   // 这个用来保存节点名称，为了后台保存日志用的，无其他作用
      this.emailElgData.forEach((node) => {
        node = Object.assign({}, node)
        this.checkedTreeIds.push(node.id)
        node.name = this.getLabel(node.id || '');
        this.checkedTreeNames.push(node.name)
        if (this.temp.ruleId != undefined) {
          node.ruleId = this.temp.ruleId
        } else {
          delete node.ruleId
        }
        this.temp.mailIds.push(node)
      })
      if (this.temp.mailIds.length == 0) {
        this.temp.isChecked = false
        this.temp.ruleId = undefined
      }
    },
    validateFormData() {
      return this.responseValidate
    },
    saveData() {
      this.loadMailTree()
      this.submitting = true
      if (this.validateFormData()) {
        this.formatSubmitParams()
        saveMailSender({ ids: this.checkedTreeIds.join(','), names: this.checkedTreeNames.join(','), mailIds: this.temp.mailIds, isChecked: this.temp.isChecked, ruleId: this.temp.ruleId }).then(() => {
          this.submitting = false
          this.dialogFormVisible = false
          this.gridTable().execRowDataApi()
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.configureSucceed'), type: 'success', duration: 2000 })
        }).catch(res => {
          this.submitting = false
        })
      } else {
        this.submitting = false
      }
    },
    mailFormatter: function(row, data) {
      let result = data
      if (data.indexOf('$') == 0) {
        result = data.replace('$', '#').concat('#')
      }
      return result
    },

    addressValidator(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      const reg1 = /^#{1}[a-zA-Z0-9_@.]+#{1}$/
      const len = value.split('#')
      if (!(reg.test(value) || reg1.test(value) || len.length === 2)) {
        callback(new Error(this.$t('pages.validateMsg_email2')))
      }
      getMailLibByAddress({ address: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.emailTemp.id) {
          callback(new Error(this.$t('pages.validateMsg_email3')))
        } else {
          callback()
        }
      })
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId, nodeKey) {
      nodeKey = nodeKey || 'dataId'
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData[nodeKey] == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId, nodeKey)
          if (result) return result
        }
      }
      return ''
    },
    handleEmailCreateImport() {
      this.handleEmailCreate();
      this.addGroupAble = true
    },
    //  新增，修改弹窗
    handleEmailCreate(selectedGroupId, flag) {
      this.addGroupAble = false
      this.emailDialogStatus = 'emailCreate'
      this.emailTemp = Object.assign({}, this.emailDefaultTemp)
      // 如果传参没有selectedGroupId，则获取左侧邮箱信息库选择的分组id
      this.emailTemp.groupId = selectedGroupId ? selectedGroupId + '' : this.mailGroupSearchId
      this.createFlag = flag || false
      this.emailDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['emailForm'].clearValidate()
      })
    },
    handleEmailDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs['mailTable'].getSelectedIds() || []
        this.emailElgData = this.$refs['mailTable'].deleteTableData(this.emailElgData, toDeleteIds)
      }).catch(() => {})
    },
    handleEmailSearch(searchInfo) {
      searchInfo = searchInfo || ''
      if (searchInfo === '') {
        this.tempEmailElgData = this.emailElgData
      } else {
        //  条件查询
        this.tempEmailElgData = this.emailElgData.filter(item => {
          return (item.name && item.name.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1) ||
            (item.address && item.address.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1) ||
            (item.groupName && item.groupName.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1)
        })
      }
    },
    handleEmailImport() {
      this.$refs['emailImportTable'].show();
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createFlag) {
          this.$refs['emailImportTable'].refreshTableData()
        } else {
          this.emailElgData.push(data)
        }
        this.createFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$refs['emailImportTable'].refreshTableData()
        } else {
          for (let i = 0; i < this.emailElgData.length; i++) {
            if (this.emailElgData[i].id === data.id) {
              this.emailElgData.splice(i, 1)
              this.emailElgData.push(data)
              break
            }
          }
        }
        this.updateFlag = false
      }
    },
    createEmail() {
      this.dlgSubmitting = true
      this.$refs['emailForm'].validate((valid) => {
        if (valid) {
          this.emailTemp.groupName = findNodeLabel(this.mailTreeData, this.emailTemp.groupId, 'dataId')
          createMailLib(this.emailTemp).then(respond => {
            this.dlgSubmitting = false
            this.isImportElg(respond.data, 'create')
            this.emailDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    updateEmail() {
      this.dlgSubmitting = true
      this.$refs['emailForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.emailTemp)
          tempData.groupName_new = findNodeLabel(this.mailTreeData, this.emailTemp.groupId, 'dataId')
          updateMailLib(tempData).then(respond => {
            this.dlgSubmitting = false
            this.isImportElg(respond.data, 'update')
            this.emailDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    emailSelectable(row, index) {
      return selectable(row, index);
    },
    //  以下方法是点击导入按钮的弹窗方法
    //  导入方法的row-data-api
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.type = undefined
      this.mailGroupSearchId = searchQuery.groupId;
      return getMailLibPage(searchQuery)
    },
    getNeedAddIds(needIds) {
      this.loadGroupTree()
      this.needIds = needIds
    },
    //  删除记录
    submitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      this.emailElgData = this.emailElgData.filter(item => { return deleteIdsList.filter(i => i === item.id).length === 0 })
    },
    elgCancelAfter() {
      this.loadTree();
      this.getEmailListByIds();
    },
    //  弹窗分组的数据发送变化时
    changeGroupAfter() {
      this.loadGroupTree()
      this.changeGroupNotify()
    },
    handleEmailUpdate(row) {
      this.updateFlag = true
      this.handleEmailListUpdate(row)
      //  导入中的列表  修改不显示新增分组按钮
      this.addGroupAble = false
    },
    handleEmailListUpdate(row) {
      //  列表中显示新增分组按钮
      this.addGroupAble = true
      this.emailDialogStatus = 'emailUpdate'
      this.emailTemp = Object.assign({}, row)
      this.emailTemp.groupId = this.emailTemp.groupId ? this.emailTemp.groupId + '' : '';
      this.emailDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['emailForm'].clearValidate()
      })
    },
    loadGroupTree: function() {
      return getGroupTreeNode().then(res => {
        this.treeSelectNode = res.data
      })
    },
    getEmailListByIds() {
      const ids = this.$refs['mailTable'].getIdsByList(this.emailElgData) || []
      if (ids.length === 0) {
        this.emailElgData = []
        return;
      }
      getMailLibraryByIds({ ids: ids.join(',') }).then(res => {
        this.emailElgData = res.data || []
      })
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    //  添加分组事件
    handleTypeCreate() {
      this.$refs['createGroupDlg'].handleCreate();
    },
    //  新分组添加完成后事件
    createGroupAddEnd(row) {
      this.loadGroupTree().then(() => {
        this.emailTemp.groupId = row.id + ''
      })
      this.changeGroupNotify()
    },
    changeGroupNotify() {
      this.$emit('changeGroup', 'MailSender')
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
</style>
