<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.mailReceiver')" name="MailReceiver">
        <Mail-receiver ref="MailReceiver" @changeGroup="notifyUpdateGroup"></Mail-receiver>
      </el-tab-pane>
      <el-tab-pane v-if="!hideMailSenderTab" :label="$t('pages.mailSender')" name="MailSender">
        <Mail-sender ref="MailSender" @changeGroup="notifyUpdateGroup"></Mail-sender>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MailReceiver from '@/views/dataEncryption/encryption/mailWhiteList/mailReceiver'
import MailSender from '@/views/dataEncryption/encryption/mailWhiteList/mailSender'

export default {
  name: 'MailWhiteList',
  components: { Mail<PERSON><PERSON><PERSON>ver, Mail<PERSON>ender },
  props: {
    tabName: { type: String, default: 'MailReceiver' },
    isPrepare: { // 预定义策略隐藏发件人Tab
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeName: this.tabName,
      hideMailSenderTab: false
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.activeName = this.$route.query.tabName || this.activeName
    this.hideMailSenderTab = this.$route.query.isPrepare || this.isPrepare
  },
  activated() {
    this.activeName = this.$route.query.tabName || this.activeName
    this.hideMailSenderTab = this.$route.query.isPrepare || this.isPrepare
    // this.loadMailTree()
    // this.gridTable().execRowDataApi()
  },
  methods: {
    tabClick(pane, event) {
    },
    notifyUpdateGroup(refName) {
      if (refName === 'MailReceiver') {
        this.$refs['MailSender'].loadGroupTree()
      } else if (refName === 'MailSender') {
        this.$refs['MailReceiver'].loadGroupTree()
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
  .app-container{
    padding: 10px 15px;
  }
</style>
