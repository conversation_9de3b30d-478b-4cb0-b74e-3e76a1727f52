<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="copyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <!-- 新增，修改邮箱弹窗  - 邮箱信息 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[emailDialogStatus]"
      :visible.sync="emailDialogFormVisible"
      width="600px"
    >
      <Form
        ref="emailForm"
        :rules="emailRules"
        :model="emailTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('form.emailName')" prop="name">
          <el-input v-model="emailTemp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.mailLibrary_text3')" prop="address">
          <el-tooltip v-if="!addGroupAble" slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.mailLibrary_address_description">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
          <el-input v-model="emailTemp.address" :maxlength="60" suffix-icon="el-icon-message"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="addGroupAble ? 21 : 24">
              <el-select v-model="emailTemp.groupId" filterable :placeholder="$t('text.select')">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
            </el-col>
            <el-col v-if="addGroupAble" style="padding-top:1px" :span="3">
              <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('form.unit')" prop="unit">
          <el-input v-model="emailTemp.unit" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('form.office')" prop="office">
          <el-input v-model="emailTemp.office" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="emailTemp.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="emailDialogStatus==='emailCreate'?createEmail(): emailDialogStatus==='emailUpdate' ? updateEmail() : {}">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="emailDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="800px"
      @closed="closed"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ textMap[dialogStatus] }}
        <el-tooltip effect="dark" :content="$t('pages.emailKeyWord_test9', {content: $t('route.EmailCopy')} )" placement="bottom-start">
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>

        <el-divider content-position="left">{{ $t('pages.mailWhiteList_text3') }}</el-divider>
        <div style="height:250px;">
          <import-table
            ref="mailTable"
            :search-info-prompt-message="$t('pages.mailWhiteList_text19')"
            :delete-disabled="!deleteable"
            :col-model="emailColModel"
            :row-datas="tempEmailElgData"
            :row-no-label="$t('table.keyId')"
            :formable="formable"
            :selectable="selectable"
            :handle-create="handleEmailCreateImport"
            :handle-delete="handleEmailDelete"
            :handle-import="handleEmailImport"
            :handle-search="handleEmailSearch"
            @selectionChangeEnd="selectionChangeEnd"
          />
          <!-- <tree-menu ref="mailTree" :disabled-all-nodes="!formable" multiple :data="mailTreeData" :icon-option="iconOption" :checked-keys="checkedTreeIds"></tree-menu> -->
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'A52'" :link-url="'/system/baseData/mailLibrary'" :btn-text="$t('pages.maintainMail')"/>
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus.startsWith('create')?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <import-table-dlg
      ref="emailImportTable"
      :exits-list-data="emailElgData"
      :elg-title="$t('pages.importMailLibrary')"
      :group-root-name="$t('pages.mailLibrary')"
      :search-info-name="$t('pages.emailCopy_nameOrAddress')"
      :confirm-button-name="$t('pages.addMail')"
      :prompt-message="$t('pages.mailImportText1')"
      :group-title="$t('pages.mailGroup')"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="getGroupTreeNode"
      :create-group="createMailLibGroup"
      :update-group="updateMailLibGroup"
      :delete-group="deleteMailLibGroup"
      :count-by-group="countChildByGroupId"
      :get-group-by-name="getMailLibGroupByName"
      :delete="deleteMailLib"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteMailLibGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleEmailCreate"
      :get-list-by-group-ids="getMailLibraryByGroupIds"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
      @elgCancelAfter="elgCancelAfter"
      @changeGroupAfter="changeGroupAfter"
    />

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importMailCarbonCopyStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />

    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :edit-valid-func="getMailLibGroupByName"
      :add-func="createMailLibGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>

<script>
import {
  createStrategy,
  deleteStrategy,
  getStrategyByName,
  getStrategyPage,
  updateStrategy
} from '@/api/dataEncryption/encryption/mailCarbonCopy'
import {
  countChildByGroupId,
  createMailLib,
  createMailLibGroup,
  deleteGroupAndData,
  deleteMailLib,
  deleteMailLibGroup,
  getGroupTreeNode,
  getMailLibByAddress,
  getMailLibGroupByName,
  getMailLibPage,
  getMailLibraryByGroupIds,
  getMailLibraryByIds,
  moveGroup,
  moveGroupToOther,
  updateMailLib,
  updateMailLibGroup
} from '@/api/system/baseData/mailLibrary'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import { exportStg } from '@/api/stgCommon'
import { findNodeLabel } from '@/utils/tree';
import EditGroupDlg from '@/views/common/editGroupDlg'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'

export default {
  name: 'EmailCopy',
  components: { ImportStg, ImportTable, EditGroupDlg, ImportTableDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 208,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'mailAddress', label: 'carbonCopyMail', width: '200', formatter: this.mailFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      emailColModel: [
        { prop: 'name', label: 'emailName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'emailAddress', width: '150', sort: true, formatter: this.addressFormatter },
        { prop: 'unit', label: 'unit', width: '150', sort: true },
        { prop: 'office', label: 'office', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleEmailListUpdate }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'emailName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'emailAddress', width: '150', sort: true, formatter: this.addressFormatter },
        { prop: 'unit', label: 'unit', width: '150', sort: true },
        { prop: 'office', label: 'office', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleEmailUpdate }
          ]
        }
      ],
      emailRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'change' }
        ]
      },
      emailDialogStatus: '',
      emailDialogFormVisible: false,
      emailTemp: {},
      emailDefaultTemp: { // 表单字段
        id: undefined,
        name: '',
        address: '',
        office: '',
        unit: '',
        state: 0,
        groupId: undefined,
        type: 0
      },
      addGroupAble: false,
      treeSelectNode: [],
      tempEmailElgData: [],
      emailElgData: [],
      createFlag: false,
      updateFlag: false,
      dlgSubmitting: false,
      needIds: [],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        active: false,
        name: '',
        remark: '',
        mailIds: [],
        entityId: undefined
      },
      treeable: true,
      addBtnAble: false,
      deleteable: false,
      showTree: true,
      submitting: false,
      mailTreeData: [],
      iconOption: { G: 'group', '': 'email2' },
      checkedTreeIds: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.mailCopyStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.mailCopyStg'), 'create'),
        emailCreate: this.i18nConcatText(this.$t('pages.mailInfo'), 'create'),
        emailUpdate: this.i18nConcatText(this.$t('pages.mailInfo'), 'update')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      sendType: 0,
      // 左侧邮箱信息库选择的分组id
      mailGroupSearchId: undefined
    }
  },
  computed: {
  },
  watch: {
    emailElgData(val) {
      //  设置序号
      (val || []).forEach(item => {
        item.groupName = this.getGroupNameByDataId(this.treeSelectNode, item.groupId)
      });
      this.handleEmailSearch()
    },
    needIds(val) {
      if (val.length > 0) {
        getMailLibraryByIds({ ids: val.join(',') }).then(res => {
          let datas = res.data || []
          const beforeSize = datas.length
          datas = datas.filter(item => item.address && !item.address.includes('#'))
          if (beforeSize !== datas.length) {
            this.$notify({
              title: this.$t('text.warning'),
              message: this.$t('pages.mailImportWarnTip1'),
              type: 'warning',
              duration: 3000
            })
          }
          this.emailElgData = datas
        })
      }
    },
    treeSelectNode(val) {
      if (this.emailDialogFormVisible) {
        const groupInfo = this.treeSelectNode.find(node => node.dataId == this.emailTemp.groupId)
        !groupInfo && (this.emailTemp.groupId = undefined)
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // this.loadMailTree()
    this.loadGroupTree()
  },
  activated() {
    this.loadGroupTree().then(() => {
      if (this.emailDialogFormVisible) {
        const groupInfo = this.treeSelectNode.find(node => node.dataId == this.emailTemp.groupId)
        !groupInfo && (this.emailTemp.groupId = undefined)
      }
    })
    // this.loadMailTree()
  },
  methods: {
    getMailLibraryByGroupIds,
    //  加载分组树
    getGroupTreeNode,
    //  创建分组
    createMailLibGroup,
    //  修改分组
    updateMailLibGroup,
    //  删除分组
    deleteMailLibGroup,
    getMailLibGroupByName,
    countChildByGroupId,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    createMailLib,
    updateMailLib,
    deleteMailLib,
    gridTable() {
      return this.$refs['copyListTable']
    },
    getNeedAddIds(needIds) {
      this.loadGroupTree()
      this.needIds = needIds
    },
    closed() {
      this.checkedTreeIds = []
    },
    loadGroupTree: function() {
      return getGroupTreeNode().then(res => {
        this.treeSelectNode = res.data
      })
    },
    handleEmailUpdate(row) {
      this.updateFlag = true
      this.handleEmailListUpdate(row)
      //  导入中的列表  修改不显示新增分组按钮
      this.addGroupAble = false
    },
    updateEmail() {
      this.dlgSubmitting = true
      this.$refs['emailForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.emailTemp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.emailTemp.groupId, 'dataId')
          updateMailLib(tempData).then(respond => {
            this.dlgSubmitting = false
            this.isImportElg(respond.data, 'update')
            this.emailDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.getEmailListByIds()
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.mailGroupSearchId = searchQuery.groupId;
      return getMailLibPage(searchQuery)
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    handleEmailListUpdate(row) {
      //  列表中显示新增分组按钮
      this.addGroupAble = true
      this.emailDialogStatus = 'emailUpdate'
      this.emailTemp = Object.assign({}, row)
      this.emailTemp.groupId = this.emailTemp.groupId ? this.emailTemp.groupId + '' : '';
      this.emailDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['emailForm'].clearValidate()
      })
    },
    elgCancelAfter() {
      this.loadGroupTree();
      this.getEmailListByIds();
    },
    changeGroupAfter() {
      this.getEmailListByIds()
      this.loadGroupTree()
    },
    submitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      this.emailElgData = this.emailElgData.filter(item => { return deleteIdsList.filter(i => i === item.id).length === 0 })
    },
    handleEmailSearch(searchInfo) {
      searchInfo = searchInfo || ''
      if (searchInfo === '') {
        this.tempEmailElgData = this.emailElgData
      } else {
        //  条件查询
        this.tempEmailElgData = this.emailElgData.filter(item => {
          return (item.name && item.name.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1) ||
            (item.address && item.address.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1) ||
            (item.groupName && item.groupName.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1)
        })
      }
    },
    handleEmailDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs['mailTable'].getSelectedIds() || []
        this.emailElgData = this.$refs['mailTable'].deleteTableData(this.emailElgData, toDeleteIds)
      }).catch(() => {})
    },
    handleEmailImport() {
      this.$refs['emailImportTable'].show();
    },
    addressValidator(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      const reg1 = /^#{1}[a-zA-Z0-9_@.]+#{1}$/
      const len = value.split('#')
      if (!(reg.test(value) || reg1.test(value) || len.length === 2)) {
        callback(new Error(this.$t('pages.validateMsg_email2')))
      }
      getMailLibByAddress({ address: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.emailTemp.id) {
          callback(new Error(this.$t('pages.validateMsg_email3')))
        } else {
          // addGroupAble === true 意味着 该窗口是由策略配置窗口打开， === false 由导入窗口打开新增
          if (this.addGroupAble && value.includes('#')) {
            callback(new Error(this.$t('pages.validaEmail')))
            return
          }
          callback()
        }
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    // loadMailTree: function() {
    //   getTreeNode().then(respond => {
    //     respond.data.forEach(node => {
    //       if (node.children != undefined) {
    //         node.children = node.children.filter(child => {
    //           const label = child.label.substring(child.label.indexOf('<'), child.label.length - 1)
    //           return label.indexOf('#') < 0
    //         }).filter(child => {
    //           const label = child.label.substring(child.label.indexOf('<'), child.label.length - 1)
    //           return label.indexOf('$') < 0
    //         })
    //       }
    //     });
    //     this.mailTreeData.splice(0, this.mailTreeData.length, ...respond.data)
    //   })
    // },
    receiverTreeNodeCheckChange: function(keys, datas) {
      keys.forEach(function(id, i) {
        if (id.indexOf('G') > -1) {
          keys.splice(i, 1)
        }
      })
      this.temp.receiverIds = keys
    },
    handleEmailCreateImport() {
      this.handleEmailCreate();
      this.addGroupAble = true
    },
    //  新增，修改弹窗
    handleEmailCreate(selectedGroupId, flag) {
      this.addGroupAble = false
      this.emailDialogStatus = 'emailCreate'
      this.emailTemp = Object.assign({}, this.emailDefaultTemp)
      // 如果传参没有selectedGroupId，则获取左侧邮箱信息库选择的分组id
      this.emailTemp.groupId = selectedGroupId ? selectedGroupId + '' : this.mailGroupSearchId
      this.createFlag = flag || false
      this.emailDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['emailForm'].clearValidate()
      })
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createFlag) {
          this.$refs['emailImportTable'].refreshTableData()
        } else {
          this.emailElgData.push(data)
        }
        this.createFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$refs['emailImportTable'].refreshTableData()
        } else {
          for (let i = 0; i < this.emailElgData.length; i++) {
            if (this.emailElgData[i].id === data.id) {
              this.emailElgData.splice(i, 1)
              this.emailElgData.push(data)
              break
            }
          }
        }
        this.updateFlag = false
      }
    },
    getEmailListByIds() {
      const ids = this.$refs['mailTable'].getIdsByList(this.emailElgData) || []
      if (ids.length === 0) {
        this.emailElgData = []
        return;
      }
      getMailLibraryByIds({ ids: ids.join(',') }).then(res => {
        this.emailElgData = res.data || []
      })
    },
    createEmail() {
      this.dlgSubmitting = true
      this.$refs['emailForm'].validate((valid) => {
        if (valid) {
          this.emailTemp.groupName = findNodeLabel(this.treeSelectNode, this.emailTemp.groupId, 'dataId')
          createMailLib(this.emailTemp).then(respond => {
            this.dlgSubmitting = false
            this.isImportElg(respond.data, 'create')
            this.emailDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    handleTypeCreate() {
      this.$refs['createGroupDlg'].handleCreate();
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, { page: 1 }, this.query, option)
      return getStrategyPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.tempEmailElgData = []
      this.emailElgData = []
    },
    handleDrag() {},
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable().getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.checkedTreeIds = []
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogFormVisible = true
      this.$refs.mailTree && this.$refs.mailTree.clearFilter()
      this.$nextTick(() => {
        this.$refs['mailTable'] && this.$refs['mailTable'].clearSearchInfo();
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.dialogStatus = 'update'
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.getMailListByIds(row.mailIds.map(v => v.id))
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['mailTable'] && this.$refs['mailTable'].clearSearchInfo();
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable().getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable().deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    getMailListByIds(ids) {
      ids = ids || []
      if (ids.length === 0) {
        this.emailElgData = []
        return;
      }
      getMailLibPage({ ids: ids.join(',') }).then(res => {
        this.emailElgData = res.data.items || []
      })
    },
    formatSubmitParams: function() {
      if (!this.temp.isChecked) {
        this.temp.ruleId = undefined
      }
      this.temp.mailIds = this.$refs['mailTable'].getIdsByList(this.emailElgData) || []
      this.temp.mailIds = this.temp.mailIds.map(v => { return { id: v } })
      if (this.temp.mailIds.length === 0) {
        this.temp.isChecked = false
        this.temp.ruleId = undefined
      }
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatSubmitParams()
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable().execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatSubmitParams()
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable().execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    mailFormatter: function(row, data) {
      let result = ''
      if (data) {
        data.forEach(address => {
          result += address + '; '
        })
      }
      return result
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    importSuccess() {
      this.handleFilter()
    },
    createGroupAddEnd(row) {
      this.loadGroupTree();
      this.emailTemp.groupId = row.id + ''
    }
  }
}
</script>
<style lang='scss' scoped>
</style>
