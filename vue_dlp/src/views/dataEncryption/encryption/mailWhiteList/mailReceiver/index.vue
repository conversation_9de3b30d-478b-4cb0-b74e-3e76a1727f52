<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" @data-change="strategyTargetNodeChange" />
    </div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="treeable" icon="el-icon-setting" size="mini" @click="handleConfig">
          {{ $t('pages.systemConfig') }}
        </el-button>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="receiverListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <!-- 新增,修改弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="closed"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.mailWhiteList_text3') }}</el-divider>
        <div style="height:250px;">
          <import-table
            ref="mailTable"
            :search-info-prompt-message="$t('pages.mailWhiteList_text19')"
            :delete-disabled="!deleteable"
            :col-model="emailColModel"
            :row-datas="tempEmailElgData"
            :row-no-label="$t('table.keyId')"
            :formable="formable"
            :selectable="emailSelectable"
            :handle-create="handleEmailCreateImport"
            :handle-delete="handleEmailDelete"
            :handle-import="handleEmailImport"
            :handle-search="handleEmailSearch"
            @selectionChangeEnd="selectionChangeEnd"
          />
        </div>
        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
        <FormItem label-width="0px">
          <div style="padding-top: 15px; display: flex; flex-direction: row;">
            <div style="margin-right: 15px; flex-shrink: 0; display: inline-block;">
              <el-checkbox v-model="temp.isChecked" :disabled="!formable">{{ $t('pages.triggerResponseRule') }}</el-checkbox>
            </div>
            <div style="display: inline-block;">
              <ResponseContent
                :status="dialogStatus"
                :show-select="true"
                :editable="formable"
                read-only
                :prop-check-rule="temp.isChecked"
                :show-check-rule="false"
                :prop-rule-id="temp.ruleId"
                @getRuleId="getRuleId"
                @validate="(val) => { responseValidate = val }"
              />
            </div>
          </div>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'A52'" :link-url="'/system/baseData/mailLibrary'" :btn-text="$t('pages.maintainMail')"/>
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus.startsWith('create')?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 系统设置弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textTitle"
      :visible.sync="dialogFormCVisible"
      width="750px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataFormC"
        :rules="rules"
        :model="tempC"
        label-position="top"
        label-width="100px"
        :hide-required-asterisk="true"
        style="width: 680px;"
      >
        <el-tabs ref="sslTabs" v-model="activeSslName" type="card" style="height: 395px;">
          <el-tab-pane :label="$t('pages.whitelistSetting')" name="notSsl" >
            <fieldset style="margin-top: 10px;">
              <legend>{{ $t('pages.supportAllAgreement') }}</legend>
              <el-row>
                <el-checkbox v-model="tempC.penetrate" :true-label="1" :false-label="0">
                  {{ $t('pages.mailWhiteList_text22') }}
                </el-checkbox>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.mailWhiteList_text23') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-row>
            </fieldset>
            <fieldset style="margin-top: 10px;">
              <legend>{{ $t('pages.mailWhiteListSupportAgreement') }}</legend>
              <FormItem>
                <el-radio-group v-model="tempC.decryptType" @change="decryptTypeChange">
                  <el-radio :label="0">{{ $t('pages.mailWhiteList_text5') }}</el-radio>
                  <el-radio :label="1">{{ $t('pages.mailWhiteList_text6') }}</el-radio>
                </el-radio-group>
              </FormItem>
              <FormItem :label="$t('pages.mailWhiteList_text7')">
                <el-select v-model="tempC.resend" :disabled="tempC.decryptType==1">
                  <el-option :label="$t('pages.mailWhiteList_text8')" :value="0"></el-option>
                  <el-option :label="$t('pages.mailWhiteList_text9')" :value="1"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.mailWhiteList_text10')">
                <el-input v-model="tempC.decryptFlag" maxlength="50" show-word-limit/>
              </FormItem>
              <div style="color: #0c60a5;padding-top: 15px">
                {{ $t('pages.mailWhiteList_text11') }}
              </div>
            </fieldset>
          </el-tab-pane>
          <el-tab-pane :label="`${$t('pages.SSLWhiteList')}（Outlook）`" name="ssl">
            <fieldset style="margin-top: 10px;">
              <legend>{{ $t('pages.mailWhiteList_text12') }}</legend>
              <FormItem>
                <el-radio-group v-model="allowChange">
                  <el-radio :label="0">{{ $t('pages.notAllow') }}</el-radio>
                  <el-radio :label="1">{{ $t('pages.allow') }}</el-radio>
                </el-radio-group>
              </FormItem>
            </fieldset>

            <fieldset>
              <legend>{{ $t('pages.mailWhiteList_text13') }}</legend>
              <el-row>
                <el-col :span="4">
                  <el-radio-group v-model="sendType">
                    <el-radio :label="0">{{ $t('pages.mode1') }}</el-radio>
                    <el-radio :label="1" style="margin-top: 27px;">{{ $t('pages.mode2') }}</el-radio>
                  </el-radio-group>
                </el-col>
                <el-col :span="20">
                  <span style="display: inline-block; margin-top: 5px;">
                    <i18n path="pages.mailWhiteList_text14">
                      <br slot="br"/>
                    </i18n>
                  </span>
                  <span style="display: inline-block; margin-top: 10px;">
                    <i18n path="pages.mailWhiteList_text16">
                      <br slot="br"/>
                    </i18n>
                  </span>
                </el-col>
              </el-row>
            </fieldset>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.SSLEnterpriseMailSetting')" name="sslEn">
            <div slot="header" class="clearfix">
              <span>{{ $t('pages.enterpriseMailConfigList') }}</span>
            </div>
            <div class="toolbar">
              <el-button type="primary" size="mini" @click="handleSslEnCreate">
                {{ $t('button.insert') }}
              </el-button>
              <el-button type="primary" size="mini" :disabled="!deleteable1" @click="handleSslEnDelete">
                {{ $t('button.delete') }}
              </el-button>
            </div>
            <grid-table
              ref="sslEnGrid"
              :show-pager="false"
              :height="250"
              :col-model="sslEnColModel"
              :row-datas="tempC.enterpriseMailConfigList"
              :selectable="selectable"
              :after-load="afterLoad"
              @selectionChangeEnd="sslEnSelectionChangeEnd"
            />
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :loading="submitting" type="primary" @click="saveData()">{{ $t('button.save') }}</el-button>
        <el-button @click="dialogFormCVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 新增，修改邮箱弹窗  - 邮箱信息 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[emailDialogStatus]"
      :visible.sync="emailDialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="emailForm"
        :rules="emailRules"
        :model="emailTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('form.emailName')" prop="name">
          <el-input v-model="emailTemp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.mailLibrary_text3')" prop="address" style="white-space:nowrap; ">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.mailLibrary_address_description">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
          <el-input v-model="emailTemp.address" :maxlength="60" suffix-icon="el-icon-message"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="addGroupAble ? 21 : 24">
              <el-select v-model="emailTemp.groupId" filterable :placeholder="$t('text.select')">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
            </el-col>
            <el-col v-if="addGroupAble" style="padding-top:1px" :span="3">
              <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('form.unit')" prop="unit">
          <el-input v-model="emailTemp.unit" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('form.office')" prop="office">
          <el-input v-model="emailTemp.office" :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="emailTemp.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="emailDialogStatus==='emailCreate'?createEmail(): emailDialogStatus==='emailUpdate' ? updateEmail() : {}">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="emailDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!--SSL企业邮箱配置-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[sslEnDialogStatus]"
      :visible.sync="sslEnDialogFormVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="sslEnForm"
        :rules="sslEnRules"
        :model="tempE"
        label-position="right"
        label-width="130px"
        style="width: 460px;"
      >
        <FormItem :label="$t('pages.enterpriseMailDomainNameAddress')" prop="mailSuffix">
          <el-input v-model="tempE.mailSuffix" v-trim maxlength="64"/>
        </FormItem>
        <FormItem :label="$t('pages.outgoingServerAddress')" prop="mailSendServer">
          <el-input v-model="tempE.mailSendServer" v-trim maxlength="64"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="tempE.remark" type="textarea" rows="2" resize="none" maxlength="64" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="updateSslEn">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="sslEnDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-table-dlg
      ref="emailImportTable"
      :exits-list-data="emailElgData"
      :elg-title="$t('pages.importMailLibrary')"
      :group-root-name="$t('pages.mailLibrary')"
      :search-info-name="$t('form.emailName')"
      :confirm-button-name="$t('pages.addMail')"
      :prompt-message="$t('pages.mailWhiteList_text18')"
      :group-title="$t('pages.mailGroup')"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="getGroupTreeNode"
      :create-group="createMailLibGroup"
      :update-group="updateMailLibGroup"
      :delete-group="deleteMailLibGroup"
      :count-by-group="countChildByGroupId"
      :get-group-by-name="getMailLibGroupByName"
      :delete="deleteMailLib"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteMailLibGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleEmailCreate"
      :get-list-by-group-ids="getMailLibraryByGroupIds"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
      @elgCancelAfter="elgCancelAfter"
      @changeGroupAfter="changeGroupAfter"
    />

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importMailWhiteListStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />

    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :edit-valid-func="getMailLibGroupByName"
      :add-func="createMailLibGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>

<script>
import {
  getStrategyPage, getStrategyByName, createStrategy, updateStrategy, deleteStrategy, getMailConfig, saveMailConfig
} from '@/api/dataEncryption/encryption/mailWhiteList'
import {
  countChildByGroupId, createMailLib, createMailLibGroup, deleteGroupAndData, deleteMailLib, deleteMailLibGroup, getGroupTreeNode,
  getMailLibByAddress, getMailLibGroupByName, getMailLibPage, getMailLibraryByGroupIds, getMailLibraryByIds,
  moveGroup, moveGroupToOther, updateMailLib, updateMailLibGroup
} from '@/api/system/baseData/mailLibrary'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import { findNodeLabel } from '@/utils/tree';
import EditGroupDlg from '@/views/common/editGroupDlg'

export default {
  name: 'MailReceiver',
  components: { ResponseContent, ImportStg, ImportTable, ImportTableDlg, EditGroupDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 60,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        /* { prop: 'mailAddress', label: 'whiteListMail', width: '200', formatter: this.mailFormatter }, */
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        active: true,
        name: '',
        remark: '',
        mailIds: [],
        entityId: undefined,
        isChecked: false,
        ruleId: undefined
      },
      tempC: {
        penetrate: 0,
        delayTime: 0,
        decryptType: 0,
        resend: 0,
        decryptFlag: '',
        sslSetup: 0,
        notSslSetup: [],
        enterpriseMailConfigList: []
      },
      tempE: {},
      defaultTempE: { // 表单字段
        id: undefined,
        mailSuffix: '',
        mailSendServer: '',
        remark: ''
      },
      treeable: true,
      addBtnAble: false,
      deleteable: false,
      deleteable1: false,
      editable: false,
      settable: false,
      showTree: true,
      submitting: false,
      stateOptions: { 0: this.$t('text.normal'), 1: this.$t('text.disable') },
      receiverTreeData: [],
      receiverCheckedKeys: [],
      tempChecked: [],
      dialogFormVisible: false,
      dialogFormCVisible: false,
      dialogStatus: '',
      activeSslName: 'notSsl',
      textTitle: this.$t('pages.mailWhiteList'),
      textMap: {
        update: this.i18nConcatText(this.$t('pages.mailWhiteListStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.mailWhiteListStg'), 'create'),
        emailCreate: this.i18nConcatText(this.$t('pages.mailInfo'), 'create'),
        emailUpdate: this.i18nConcatText(this.$t('pages.mailInfo'), 'update'),
        sslEnCreate: this.i18nConcatText(this.$t('pages.corporateMailConfig'), 'create'),
        sslEnUpdate: this.i18nConcatText(this.$t('pages.corporateMailConfig'), 'update')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        ruleId: [
          { required: true, message: this.$t('pages.respond_text2'), trigger: 'blur' }
        ]
      },
      sslEnRules: {
        mailSuffix: [
          { required: true, validator: this.validateMailSuffix, trigger: 'blur' }
        ],
        mailSendServer: [{ required: true, validator: this.validateMailSendServer, trigger: 'blur' }]
      },
      allowChange: 0,
      sendType: 0,
      emailColModel: [
        { prop: 'name', label: 'emailName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'emailAddress', width: '150', sort: true, formatter: this.addressFormatter },
        { prop: 'unit', label: 'unit', width: '150', sort: true },
        { prop: 'office', label: 'office', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleEmailListUpdate }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'emailName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'emailAddress', width: '150', sort: true, formatter: this.addressFormatter },
        { prop: 'unit', label: 'unit', width: '150', sort: true },
        { prop: 'office', label: 'office', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleEmailUpdate }
          ]
        }
      ],
      emailRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'change' }
        ]
      },
      treeSelectNode: [],
      tempEmailElgData: [],
      emailElgData: [],
      emailDialogFormVisible: false,
      sslEnDialogFormVisible: false,
      emailDialogStatus: '',
      sslEnDialogStatus: '',
      createFlag: false,
      updateFlag: false,
      emailTemp: {},
      emailDefaultTemp: { // 表单字段
        id: undefined,
        name: '',
        address: '',
        office: '',
        unit: '',
        state: 0,
        groupId: undefined,
        type: 0
      },
      needIds: [],
      dlgSubmitting: false,
      addGroupAble: false,
      sslEnColModel: [
        { prop: 'mailSuffix', label: this.$t('pages.enterpriseMailDomainNameAddress'), width: '160', sort: true },
        { prop: 'mailSendServer', label: this.$t('pages.outgoingServerAddress'), width: '160', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleSslEnUpdate }
          ]
        }
      ],
      // 左侧邮箱信息库选择的分组id
      mailGroupSearchId: undefined
    }
  },
  computed: {
  },
  watch: {
    emailElgData(val) {
      //  设置序号
      (val || []).forEach(item => {
        item.groupName = this.getGroupNameByDataId(this.treeSelectNode, item.groupId)
      });
      this.handleEmailSearch()
    },
    needIds(val) {
      if (val.length > 0) {
        getMailLibraryByIds({ ids: val.join(',') }).then(res => {
          this.emailElgData = res.data || []
        })
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.loadGroupTree()
  },
  activated() {
    this.loadGroupTree()
  },
  methods: {
    getMailLibraryByGroupIds,
    //  加载分组树
    getGroupTreeNode,
    //  创建分组
    createMailLibGroup,
    //  修改分组
    updateMailLibGroup,
    //  删除分组
    deleteMailLibGroup,
    getMailLibGroupByName,
    countChildByGroupId,
    deleteGroupAndData,
    moveGroupToOther,
    moveGroup,
    createMailLib,
    updateMailLib,
    deleteMailLib,
    gridTable() {
      return this.$refs['receiverListTable']
    },
    sslEnGridTable() {
      return this.$refs['sslEnGrid']
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    loadGroupTree: function() {
      return getGroupTreeNode().then(res => {
        this.treeSelectNode = res.data
      })
    },
    closed() {
      this.resetTemp()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, { page: 1 }, this.query, option)
      return getStrategyPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    resetTempE() {
      this.tempE = Object.assign({}, this.defaultTempE)
    },
    handleDrag() {},
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable().getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    sslEnSelectionChangeEnd(rows) {
      this.deleteable1 = rows.length > 0
    },
    handleSslEnCreate() {
      this.resetTempE()
      this.sslEnDialogFormVisible = true
      this.sslEnDialogStatus = 'sslEnCreate'
      this.$nextTick(() => {
        this.$refs['sslEnForm'].clearValidate()
      })
    },
    handleSslEnUpdate(row) {
      this.resetTempE()
      this.tempE = Object.assign({}, row)
      this.sslEnDialogFormVisible = true
      this.sslEnDialogStatus = 'sslEnUpdate'
      this.$nextTick(() => {
        this.$refs['sslEnForm'].clearValidate()
      })
    },
    handleSslEnDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.sslEnGridTable().getSelectedIds()
        const enterpriseMailConfigList = this.tempC.enterpriseMailConfigList
        this.tempC.enterpriseMailConfigList = enterpriseMailConfigList.filter(item => toDeleteIds.indexOf(item.id) == -1)
      })
    },
    validateSslEn() {
      if (this.tempC.enterpriseMailConfigList && this.tempC.enterpriseMailConfigList.length > 0) {
        const index = this.tempC.enterpriseMailConfigList.findIndex(item => {
          return item.id !== this.tempE.id && item.mailSuffix == this.tempE.mailSuffix && item.mailSendServer == this.tempE.mailSendServer
        })
        if (index == -1) {
          return true
        } else {
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.backUpConfig_text13'), type: 'error', duration: 2000 })
          return false
        }
      }
      return true
    },
    updateSslEn() {
      this.$refs['sslEnForm'].validate((valid) => {
        if (valid && this.validateSslEn()) {
          if (this.tempE.id) {
            this.tempC.enterpriseMailConfigList.forEach(config => {
              if (config.id === this.tempE.id) {
                config.mailSuffix = this.tempE.mailSuffix
                config.mailSendServer = this.tempE.mailSendServer
                config.remark = this.tempE.remark
              }
            })
          } else {
            if (!this.tempC.enterpriseMailConfigList) {
              this.tempC.enterpriseMailConfigList = []
            }
            this.tempE.id = new Date().getTime()
            this.tempC.enterpriseMailConfigList.push(this.tempE)
          }
          this.sslEnDialogFormVisible = false
        }
      })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.emailElgData = []
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.dialogStatus = 'update'
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.getMailListByIds(row.mailIds)
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['mailTable'] && this.$refs['mailTable'].clearSearchInfo();
        this.$refs['dataForm'].clearValidate()
      })
    },
    getMailListByIds(ids) {
      ids = ids || []
      if (ids.length === 0) {
        this.emailElgData = []
        return;
      }
      getMailLibPage({ ids: ids.join(',') }).then(res => {
        this.emailElgData = res.data.items || []
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable().getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable().deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    handleConfig() {
      this.dialogFormCVisible = true
      getMailConfig().then(respond => {
        if (respond.data) {
          this.tempC.penetrate = respond.data.penetrate
          this.tempC.decryptFlag = respond.data.decryptFlag
          this.tempC.decryptType = respond.data.decryptType
          this.tempC.resend = respond.data.resend
          this.tempC.delayTime = respond.data.delayTime
          this.tempC.sslSetup = respond.data.sslSetup
          this.tempC.enterpriseMailConfigList = respond.data.enterpriseMailConfigList
          if (this.tempC.sslSetup & 1) {
            this.allowChange = 1
          } else {
            this.allowChange = 0
          }
          if (this.tempC.sslSetup & 2) {
            this.sendType = 1
          } else {
            this.sendType = 0
          }
        }
      })
    },
    decryptTypeChange(value) {
      if (value === 1) {
        this.tempC.resend = 0
      }
    },
    validateFormData() {
      return this.responseValidate
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    saveData() {
      this.submitting = true
      this.$refs['dataFormC'].validate((valid) => {
        if (valid) {
          this.formatSSLSetup()
          saveMailConfig(this.tempC).then(() => {
            this.submitting = false
            this.dialogFormCVisible = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.settingSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    formatSSLSetup: function() {
      this.tempC.sslSetup = this.allowChange + this.sendType * 2
      this.tempC.notSslSetup = [this.tempC.decryptType, this.tempC.resend, this.tempC.decryptFlag]
    },
    formatSubmitParams: function() {
      if (!this.temp.isChecked) {
        this.temp.ruleId = undefined
      }
      this.temp.mailIds = this.$refs['mailTable'].getIdsByList(this.emailElgData) || []
      if (this.temp.mailIds.length === 0) {
        this.temp.isChecked = false
        this.temp.ruleId = undefined
      }
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatSubmitParams()
          if (this.validateFormData()) {
            //  设置邮箱id
            createStrategy(this.temp).then(() => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable().execRowDataApi(this.query)
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validateFormData()) {
          this.formatSubmitParams()
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable().execRowDataApi(this.query)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    stateFormatter: function(row, data) {
      return this.stateOptions[data]
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    mailFormatter: function(row, data) {
      let result = ''
      if (data) {
        data.forEach(address => {
          const index = address.indexOf('$')
          if (index == 0) {
            address = address.replace('$', '#').concat('#')
          }
          result += address + '; '
        })
      }
      return result
    },
    strategyFormatter: function(row, data) {
      let result = ''
      if (row.mailAddress.length > 0) {
        result = this.$t('table.whiteListMail')
        row.mailAddress.forEach(address => {
          const index = address.indexOf('$')
          if (index == 0) {
            address = address.replace('$', '#').concat('#')
          }
          result += address + '; '
        })
      }
      if (row.ruleId) {
        result += this.$t('table.EmailAlarm')
      }
      return result
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    //  邮箱校验
    addressValidator(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      const reg1 = /^#{1}[a-zA-Z0-9_@.]+#{1}$/
      const len = value.split('#')
      if (!(reg.test(value) || reg1.test(value) || len.length === 2)) {
        callback(new Error(this.$t('pages.validateMsg_email2')))
      }
      getMailLibByAddress({ address: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.emailTemp.id) {
          callback(new Error(this.$t('pages.validateMsg_email3')))
        } else {
          callback()
        }
      })
    },
    validateMailSuffix(rule, value, callback) {
      const re = /^[@][0-9a-zA-Z_.-]+([.][a-zA-Z]+){1,2}$/
      let msg = ''
      if (value) {
        if (re.test(value)) {
          msg = undefined
        } else {
          msg = this.$t('pages.errorPleaseCheckAndModify', { info: this.$t('pages.enterpriseMailDomainNameAddress') })
        }
      } else {
        msg = this.$t('pages.infoRequired', { info: this.$t('pages.enterpriseMailDomainNameAddress') })
      }
      if (msg) callback(msg)
      else callback()
    },
    validateMailSendServer(rule, value, callback) {
      const reg = /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;
      let msg = ''
      if (value) {
        if (!reg.test(value)) {
          msg = this.$t('pages.errorPleaseCheckAndModify', { info: this.$t('pages.outgoingServerAddress') })
        } else {
          callback()
        }
      } else {
        msg = this.$t('text.pleaseEnterInfo', { info: this.$t('pages.outgoingServerAddress') })
      }
      if (msg) callback(msg)
      else callback()
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    handleEmailCreateImport() {
      this.handleEmailCreate();
      this.addGroupAble = true
    },
    //  新增，修改弹窗
    handleEmailCreate(selectedGroupId, flag) {
      this.addGroupAble = false
      this.emailDialogStatus = 'emailCreate'
      this.emailTemp = Object.assign({}, this.emailDefaultTemp)
      // 如果传参没有selectedGroupId，则获取左侧邮箱信息库选择的分组id
      this.emailTemp.groupId = selectedGroupId ? selectedGroupId + '' : this.mailGroupSearchId
      this.createFlag = flag || false
      this.emailDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['emailForm'].clearValidate()
      })
    },
    handleEmailDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs['mailTable'].getSelectedIds() || []
        this.emailElgData = this.$refs['mailTable'].deleteTableData(this.emailElgData, toDeleteIds)
      }).catch(() => {})
    },
    handleEmailSearch(searchInfo) {
      searchInfo = searchInfo || ''
      if (searchInfo === '') {
        this.tempEmailElgData = this.emailElgData
      } else {
        //  条件查询
        this.tempEmailElgData = this.emailElgData.filter(item => {
          return (item.name && item.name.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1) ||
            (item.address && item.address.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1) ||
            (item.groupName && item.groupName.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1)
        })
      }
    },
    handleEmailImport() {
      this.$refs['emailImportTable'].show();
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createFlag) {
          this.$refs['emailImportTable'].refreshTableData()
        } else {
          this.emailElgData.push(data)
        }
        this.createFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$refs['emailImportTable'].refreshTableData()
        } else {
          for (let i = 0; i < this.emailElgData.length; i++) {
            if (this.emailElgData[i].id === data.id) {
              this.emailElgData.splice(i, 1)
              this.emailElgData.push(data)
              break
            }
          }
        }
        this.updateFlag = false
      }
    },
    createEmail() {
      this.dlgSubmitting = true
      this.$refs['emailForm'].validate((valid) => {
        if (valid) {
          this.emailTemp.groupName = findNodeLabel(this.treeSelectNode, this.emailTemp.groupId, 'dataId')
          createMailLib(this.emailTemp).then(respond => {
            this.dlgSubmitting = false
            this.isImportElg(respond.data, 'create')
            this.emailDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    updateEmail() {
      this.dlgSubmitting = true
      this.$refs['emailForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.emailTemp)
          tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.emailTemp.groupId, 'dataId')
          updateMailLib(tempData).then(respond => {
            this.dlgSubmitting = false
            this.isImportElg(respond.data, 'update')
            this.emailDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    emailSelectable(row, index) {
      return selectable(row, index);
    },
    //  以下方法是点击导入按钮的弹窗方法
    //  导入方法的row-data-api
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.mailGroupSearchId = searchQuery.groupId;
      return getMailLibPage(searchQuery)
    },
    getNeedAddIds(needIds) {
      this.loadGroupTree()
      this.needIds = needIds
    },
    //  删除记录
    submitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      this.emailElgData = this.emailElgData.filter(item => { return deleteIdsList.filter(i => i === item.id).length === 0 })
    },
    elgCancelAfter() {
      this.loadGroupTree();
      this.getEmailListByIds();
    },
    //  弹窗分组的数据发送变化时
    changeGroupAfter() {
      this.loadGroupTree()
      this.getEmailListByIds();
      this.changeGroupNotify()
    },
    handleEmailUpdate(row) {
      this.updateFlag = true
      this.handleEmailListUpdate(row)
      //  导入中的列表  修改不显示新增分组按钮
      this.addGroupAble = false
    },
    handleEmailListUpdate(row) {
      //  列表中显示新增分组按钮
      this.addGroupAble = true
      this.emailDialogStatus = 'emailUpdate'
      this.emailTemp = Object.assign({}, row)
      this.emailTemp.groupId = this.emailTemp.groupId ? this.emailTemp.groupId + '' : '';
      this.emailDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['emailForm'].clearValidate()
      })
    },
    getEmailListByIds() {
      const ids = this.$refs['mailTable'].getIdsByList(this.emailElgData) || []
      if (ids.length === 0) {
        this.emailElgData = []
        return;
      }
      getMailLibraryByIds({ ids: ids.join(',') }).then(res => {
        this.emailElgData = res.data || []
      })
    },
    //  添加分组事件
    handleTypeCreate() {
      this.$refs['createGroupDlg'].handleCreate();
    },
    //  新分组添加完成后事件
    createGroupAddEnd(row) {
      this.loadGroupTree().then(() => {
        this.emailTemp.groupId = row.id + ''
      })
      this.changeGroupNotify()
    },
    changeGroupNotify() {
      this.$emit('changeGroup', 'MailReceiver')
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
  >>>fieldset{
    border: 1px solid #bbb;
    margin-bottom: 10px;
  }
</style>
