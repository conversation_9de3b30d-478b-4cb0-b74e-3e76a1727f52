<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="130px">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :entity-type-span="11"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100"></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <FormItem :label="$t('pages.pcOffline')">
          <el-input-number v-model="PCHour" :disabled="!formable" style="width:80px" :controls="false" :min="0" :max="4319" @blur="e=>timeChange(e,'PCHour')"></el-input-number>{{ this.$t('text.hour') }}
          <el-input-number v-model="PCMin" :disabled="!formable" style="width:80px" :controls="false" :min="0" :max="59" @blur="e=>timeChange(e,'PCMin')"></el-input-number>{{ this.$t('text.minute') }}
        </FormItem>
        <FormItem :label="$t('pages.mobileOffline')">
          <el-input-number v-model="MobileHour" :disabled="!formable" style="width:80px" :controls="false" :min="0" :max="4319" @blur="e=>timeChange(e,'MobileHour')"></el-input-number>{{ this.$t('text.hour') }}
          <el-input-number v-model="MobileMin" :disabled="!formable" style="width:80px" :controls="false" :min="0" :max="59" @blur="e=>timeChange(e,'MobileMin')"></el-input-number>{{ this.$t('text.minute') }}
        </FormItem>
        <span style="color:#409EFF">
          {{ $t('pages.shortOffline_text1') }}
        </span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importShortOfflineStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { getStrategyList, getStrategyByName, createStrategy, updateStrategy, deleteStrategy } from '@/api/dataEncryption/encryption/shortOfflineStrategy'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'ShortOfflineStrategy',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 116,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'offlineTime', label: 'offlineTime', width: '200', formatter: this.PCOfflineTypeFormatter },
        { prop: 'mobileOfflineTime', label: 'mobileOfflineTime', width: '200', formatter: this.MobileOfflineTypeFormatter },
        { prop: 'remark', label: 'remark', width: '200', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        offlineTime: undefined,
        mobileOfflineTime: undefined,
        entityType: undefined,
        entityId: undefined
      },
      PCHour: 72,
      PCMin: 0,
      MobileHour: 0,
      MobileMin: 30,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.shortOfflineStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.shortOfflineStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.PCHour = 72
      this.PCMin = 0
      this.MobileHour = 0
      this.MobileMin = 30
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.PCHour = Math.floor(this.temp.offlineTime / 60)
      this.PCMin = this.temp.offlineTime % 60
      this.MobileHour = Math.floor(this.temp.mobileOfflineTime / 60)
      this.MobileMin = this.temp.mobileOfflineTime % 60
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    formatFormParam() {
      if (this.PCHour == 0 && this.PCMin == 0) {
        this.temp.offlineTime = 30
      } else {
        this.temp.offlineTime = this.PCHour * 60 + this.PCMin
      }
      if (this.MobileHour == 0 && this.MobileMin == 0) {
        this.temp.mobileOfflineTime = 30
      } else {
        this.temp.mobileOfflineTime = this.MobileHour * 60 + this.MobileMin
      }
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormParam()
          createStrategy(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormParam()
          const tempData = Object.assign({}, this.temp)
          updateStrategy(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    timeChange(e, model) {
      if (this[model] == undefined) {
        this[model] = 0
      }
    },
    PCOfflineTypeFormatter: function(row, data) {
      let result = ''
      let hour = ''
      let min = ''
      hour = Math.floor(row.offlineTime / 60)
      min = row.offlineTime % 60
      result = this.$t('pages.hourAndMinute', { hour: hour, minute: min })
      return result
    },
    MobileOfflineTypeFormatter: function(row, data) {
      let result = ''
      let hour = ''
      let min = ''
      hour = Math.floor(row.mobileOfflineTime / 60)
      min = row.mobileOfflineTime % 60
      result = this.$t('pages.hourAndMinute', { hour: hour, minute: min })
      return result
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    exeNameFormatter(row, data) {
      if (this.exeNameOptions[data]) {
        return this.exeNameOptions[data]
      } else {
        return data
      }
    },
    strategyNameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    }
  }
}
</script>
