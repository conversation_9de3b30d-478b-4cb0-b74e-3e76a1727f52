<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('text.details')"
    :modal="false"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="800px"
  >
    <div class="toolbar" style="margin-bottom: 5px;">
      <el-select v-model="query.statusType" :style="`width: ${ lang == 'en' ? '304' : '200'}px`" :disabled="!query.guid" @change="filterLogRowData">
        <el-option v-for="status in statusList" :key="status.value" :value="status.value" :label="status.name"></el-option>
      </el-select>
      <el-select v-model="query.guid" style="width: 220px" :disabled="!query.guid" @change="filterLogRowData">
        <el-option v-for="task in taskList" :key="task.id" :value="task.guid" :label="`${task.name}(${task.startTime})`">{{ task.name }} ({{ task.startTime }})</el-option>
      </el-select>
      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
        {{ $t('table.search') }}
      </el-button>
    </div>
    <grid-table ref="logList" row-key="logId" :col-model="colModel" :autoload="false" :multi-select="false" :row-data-api="rowDataApi" :height="400"/>
  </el-dialog>
</template>

<script>
import { getTaskList, getLogList } from '@/api/dataEncryption/encryption/diskScan'
export default {
  name: 'DiskScanDetail',
  data() {
    return {
      visible: false,
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: 'custom' },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'opType', label: 'operateType', width: '150', formatter: this.opTypeFormatter },
        { prop: 'status', label: 'status', width: '150', formatter: this.statusFormatter }
      ],
      taskList: [],
      opTypeOptions: {
        1: this.$t('pages.fullDecryption'),                           // 全盘解密
        2: this.$t('pages.globalEncryption'),                         // 全盘加密
        5: this.$t('pages.diskScan_Msg3'),                            // 全盘扫描敏感文件并加密
        3: this.$t('pages.diskScan_Msg4'),                             // 全盘扫描敏感文件
        7: this.$t('pages.diskScan_Msg47'),                             // 全盘扫描加标签
        8: this.$t('pages.diskScan_Msg48'),                             // 全盘扫描获取标签
        9: this.$t('pages.diskScan_Msg49')                              // 全盘扫描清除标签
      },
      statusList: [
        { value: 2, name: this.$t('pages.allScanTerminal') },         // 所有终端
        { value: -1, name: this.$t('pages.unstatedScanTerminal') },   // 未开始扫描的终端
        { value: 0, name: this.$t('pages.statedScanTerminal') },      // 开始扫描的终端
        { value: 1, name: this.$t('pages.endScanTerminal') }          // 结束扫描的终端
      ],
      query: {
        guid: '',
        statusType: -1
      }
    }
  },
  computed: {
    lang() {
      return this.$store.getters.language
    }
  },
  created() {
  },
  methods: {
    rowDataApi(params) {
      Object.assign(this.query, params)
      return getLogList(this.query);
    },
    opTypeFormatter(row, data) {
      return this.opTypeOptions[data]
    },
    statusFormatter(row, data) {
      const statusMap = {
        '-1': this.$t('pages.notStarted'),
        '0': this.$t('pages.startScan'),
        '1': this.$t('pages.endScan'),
        '2': this.$t('pages.strategyExpire'),
        '3': this.$t('pages.stopScan')
      }
      return statusMap[data] || ''
    },
    filterLogRowData() {
      this.$refs.logList && this.$refs.logList.execRowDataApi()
    },
    show(row) {
      this.visible = true
      this.query.guid = ''
      // 查询所有终端
      this.query.statusType = 2
      getTaskList(row.id).then(resp => {
        if (resp.data.length > 0) {
          // 只显示最新的一次扫描
          this.taskList = resp.data.slice(0, 1)
          this.query.guid = this.taskList[0].guid
        }
        this.handleFilter()
      })
    },
    handleFilter() {
      this.$refs.logList && this.$refs.logList.execRowDataApi()
    }
  }

}
</script>

<style lang="scss" scoped></style>
