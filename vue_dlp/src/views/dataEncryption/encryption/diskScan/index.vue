<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div v-if="treeable" class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button icon="el-icon-video-play" size="mini" :disabled="!startScanBtnAble" @click="handleStart">
          {{ $t('pages.startScan') }}
        </el-button>
        <!--<el-button icon="el-icon-video-pause" size="mini" :disabled="!pauseScanBtnAble" @click="handlePause">
          暂停扫描
        </el-button>-->
        <el-button icon="el-icon-switch-button" size="mini" :disabled="!stopScanBtnAble" @click="handleStop">
          {{ $t('pages.stopScan') }}
        </el-button>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>

        <label v-show="!isStgTab">{{ $t('pages.operateType') }}：</label>
        <el-select v-show="!isStgTab" v-model="query.keyword1" style="width: 150px" clearable>
          <el-option v-for="(label, key) in opTypeOptions" :key="key" :label="label" :value="key"></el-option>
        </el-select>
        <label v-show="!isStgTab">{{ $t('pages.taskStatus') }}：</label>
        <el-select v-show="!isStgTab" v-model="query.keyword2" style="width: 150px" clearable>
          <el-option v-for="(label, key) in detailStatusOptions" :key="key" :label="label" :value="key"></el-option>
        </el-select>
        <el-button v-show="!isStgTab" type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
      <grid-table ref="diskScanList" :col-model="colModel" :row-data-api="rowDataApi" :row-datas="tableData" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
    <disk-scan-dlg
      ref="stgDlg"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    />
    <detail ref="detail"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importDiskScanStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="false"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import DiskScanDlg from './editDlg'
import { getScanPage, deleteScan, startScan, stopScan } from '@/api/dataEncryption/encryption/diskScan'
import { enableStgBtn, enableStgDelete, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, containStgObject, selectable, buttonFormatter } from '@/utils'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import { osTypeIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import Detail from './detail'

export default {
  name: 'DiskScan',
  components: { DiskScanDlg, ImportStg, Detail },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 95,
      colModel: [
        { prop: 'name', label: 'stgName', fixedWidth: '150' },
        { prop: 'entityName', label: 'source', type: 'button', width: '150', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [{ formatter: this.entityFormatter, click: this.entityClick }]
        },
        { prop: 'opType', label: 'operateType', width: '100', formatter: this.opTypeFormatter },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.stgFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'endDate', label: 'endDate', fixedWidth: '100' },
        { prop: 'status', label: 'scanStatus', fixedWidth: '120', fixed: 'right', iconFormatter: this.activeFormatter, formatter: this.statusFormatter },
        { label: 'operate', type: 'button', fixedWidth: '160', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleUpdate },
            { label: 'details', click: this.handleDetail }
            // { label: 'details', click: this.handleDetail, disabledFormatter: this.buttonDetailFormatter }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      isStgTab: true,
      showTree: true,
      treeable: true,
      editable: true,
      deleteable: false,
      addBtnAble: false,
      startScanBtnAble: false,
      pauseScanBtnAble: false,
      stopScanBtnAble: false,
      scanModeOptions: [{ id: 0, label: this.$t('pages.shapeLeftOptions4') }, { id: 1, label: this.$t('pages.diskScan_Msg') }, { id: 2, label: this.$t('pages.diskScan_Msg1') }, { id: 3, label: this.$t('pages.diskScan_Msg2') }],
      statusOptions: { 0: this.$t('pages.startScan'), 1: this.$t('pages.stopScan'), 2: this.$t('pages.pauseScan') },
      tableStatusOptions: { 0: this.$t('pages.scanning'), 1: this.$t('pages.stopScan'), 2: this.$t('pages.pauseScan') },
      detailStatusOptions: { 0: this.$t('pages.startScan'), 1: this.$t('pages.endScan') },
      opTypeOptions: { 5: this.$t('pages.diskScan_Msg3'), 3: this.$t('pages.diskScan_Msg4'), 1: this.$t('pages.fullDecryption'), 2: this.$t('pages.globalEncryption'), 7: this.$t('pages.diskScan_Msg47'), 8: this.$t('pages.diskScan_Msg48'), 9: this.$t('pages.diskScan_Msg49')/*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/ },
      zipSuffixes: ['.zip', '.rar', '.7z'],
      checkedEntityNode: {},
      tableData: [],
      option: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs['diskScanList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    // this.opTypeOptions = { 1: this.$t('pages.fullDecryption'), 2: this.$t('pages.globalEncryption') }
    // if (this.hasPermission('113')) {
    //   Object.assign(this.opTypeOptions, { 5: this.$t('pages.diskScan_Msg3'), 3: this.$t('pages.diskScan_Msg4')/*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/ })
    // }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.changeTableData()
  },
  activated() {
  },
  methods: {
    changeTableData() {
      this.$socket.subscribe({ url: '/topic/diskScanStatus', callback: (respond, handle) => {
        this.tableData = []
        const searchQuery = Object.assign({}, this.query, this.option)
        getScanPage(searchQuery).then(resp => {
          this.tableData = resp.data.data
        })
      } })
    },
    selectable(row, index) {
      if (containStgObject(row, this.query.objectType, this.query.objectId)) {
        return true
      }
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      this.option = option
      const searchQuery = Object.assign({}, this.query, option)
      return getScanPage(searchQuery)
    },
    stgFormatter(row, data) {
      let msg = ''
      const scanDir = this.pathFormatter(row, row.scanDir)
      msg += this.$t('table.scanDir') + ':&ensp;' + (scanDir.trim().length > 0 ? scanDir : this.$t('pages.collectAll')) + ',&ensp;'
      const exceptDir = this.pathFormatter(row, row.exceptDir)
      msg += this.$t('table.exceptionDirectory') + ':&ensp;' + (exceptDir.trim().length > 0 ? exceptDir : this.$t('pages.null')) + ',&ensp;'
      const suffix = this.pathFormatter(row, row.suffix)
      msg += this.$t('table.suffixes') + ':&ensp;' + (suffix.trim().length > 0 ? suffix : this.$t('pages.collectAll')) + ',&ensp;'
      if (row.penetrateSuffix.length > 0) {
        msg += this.$t('pages.diskScan_Msg15') + ':&ensp;'
        const suffix = row.penetrateSuffix.split('|')
        for (var i = 0; i < suffix.length; i++) {
          if (i == suffix.length - 1) {
            msg += suffix[i] + ',&ensp;'
          } else {
            msg += suffix[i] + '、'
          }
        }
      }
      msg += this.$t('table.scanMode') + ':&ensp;' + this.scanModeFormatter(row, row.scanMode)
      if (row.scanMode == 0) {
        const mode = row.cpuMem.split('|')
        msg += '(' + this.$t('pages.diskScan_Msg20') + ':&ensp;' + mode[0] + '、' + this.$t('pages.diskScan_Msg21') + ':&ensp;' + mode[1] + ')'
      }
      msg += ',&ensp;'
      if (row.showProgress != 0) {
        msg += this.$t('pages.diskScan_Msg29')
      }
      // 结尾是',&ensp;'
      if (msg[msg.length - 1] == ';') {
        msg = msg.substring(0, msg.length - 7)
      }
      return msg
    },
    isStopTask(rowData) {
      return rowData && rowData.status === 1
    },
    isStartTask(rowData) {
      return rowData && rowData.status === 0
    },
    selectionChangeEnd: function(rowDatas) {
      this.startScanBtnAble = false
      this.pauseScanBtnAble = false
      this.stopScanBtnAble = false
      if (rowDatas.length > 0) {
        for (let i = 0; i < rowDatas.length; i++) {
          const rowData = rowDatas[i]
          this.startScanBtnAble = this.startScanBtnAble ? this.startScanBtnAble : !this.isStartTask(rowData)
          this.pauseScanBtnAble = this.pauseScanBtnAble ? this.pauseScanBtnAble : this.isStartTask(rowData)
          this.stopScanBtnAble = this.stopScanBtnAble ? this.stopScanBtnAble : !this.isStopTask(rowData)
        }
      }
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.searchData(this.query)
    },
    searchData(query) {
      this.gridTable.execRowDataApi(query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    handleDetail(row) {
      this.$refs['detail'].show(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    changeScanStatus(status, targetIds) {
      if (targetIds.length > 0) {
        const statusName = this.statusOptions[status]
        this.$confirmBox(this.$t('pages.diskScan_Msg5', { statusName: statusName }), this.$t('text.prompt')).then(() => {
          const func = status === 0 ? startScan : stopScan
          func({ ids: targetIds.join(',') }).then(respond => {
            this.gridTable.execRowDataApi()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          })
        }).catch(() => {})
      }
    },
    handleStart() {
      const selectedDatas = this.gridTable.getSelectedDatas()
      if (selectedDatas.length > 0) {
        const targetIds = []
        for (let i = 0; i < selectedDatas.length; i++) {
          const rowData = selectedDatas[i]
          if (!this.isStartTask(rowData)) {
            targetIds.push(rowData.id)
          }
        }
        this.changeScanStatus(0, targetIds)
      }
    },
    handleStop() {
      const selectedDatas = this.gridTable.getSelectedDatas()
      if (selectedDatas.length > 0) {
        const targetIds = []
        for (let i = 0; i < selectedDatas.length; i++) {
          const rowData = selectedDatas[i]
          if (!this.isStopTask(rowData)) {
            targetIds.push(rowData.id)
          }
        }
        this.changeScanStatus(1, targetIds)
      }
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    handleView(row) {
      // const queryTemp = Object.assign({}, undefined, { treeable: false, taskId: row.id })
      // const metaTemp = Object.assign({}, undefined, { title: '扫描信息（' + row.name + '）' })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteScan({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    scanModeFormatter: function(row, data) {
      const op = this.scanModeOptions[data]
      return !op ? data : op.label
    },
    opTypeFormatter(row, data) {
      row.opType = row.opType + ''
      return this.opTypeOptions[data]
    },
    autoShutdownFormatter(row, data) {
      return data > 0 ? this.$t('text.yes') : ''
    },
    pathFormatter: function(row, data) {
      return !data ? '' : data
    },
    activeFormatter: function(row, data) {
      return row.status == 0 ? 'active' : ''
    },
    statusFormatter: function(row, data) {
      let status = this.tableStatusOptions[data]
      if (data === 0 || data === '0') {
        const stopSize = !row.stopScanTermSize ? 0 : row.stopScanTermSize
        status += '（' + stopSize + '/' + row.scanTermSize + '）'
      }
      return !status ? this.$t('pages.notStarted') : status
    },
    scanTimeFormatter: function(row, data) {
      return row.startTime + '-' + row.endTime
    },
    // 未开始扫描策略查看详情按钮置灰
    buttonDetailFormatter: function(row, btn) {
      let disabled = false
      if (row.status == undefined || row.status == '' || row.status == null) {
        disabled = true
      }
      return disabled
    },
    buttonFormatter: function(row, btn) {
      let disabled = true
      if (btn.label === 'edit') {
        if (buttonFormatter(row, this) !== '') {
          disabled = false
        }
      }
      if (row.active) {
        if (btn.label == this.$t('pages.startUp') && row.status == 0) {
          disabled = false
        } else if (btn.label == this.$t('pages.suspend') && (row.status == 1 || row.status == 4)) {
          disabled = false
        } else if (btn.label == this.$t('pages.cancel') && (row.status == 1 || row.status == 2 || row.status == 4)) {
          disabled = false
        } else if (btn.label == this.$t('pages.restart') && row.status != 0) {
          disabled = false
        } else if (btn.label == this.$t('pages.diskScan_Msg7')) {
          disabled = false
        }
      }
      return disabled
    }
  }
}
</script>

