<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.diskScanStg')"
      :title-tip="$t('pages.diskAddTagUseTips')"
      :stg-code="95"
      :active-able="false"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createScan"
      :update="updateScan"
      :get-by-name="getScanByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @submitFailEnd="submitFailEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-row>
          <el-col :span="10">
            <FormItem :label="$t('pages.operateType')" prop="opType">
              <el-select v-model="opType" :disabled="!editable" @change="opTypeChange">
                <el-option v-for="(label, key) in opTypeOptions" :key="key" :label="label" :value="key"></el-option>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="1">
            <link-button
              v-if="opType == 7"
              btn-class="editBtn"
              :formable="editable"
              :menu-code="'C99'"
              :link-url="{ name: 'BaseLabelConfig', params: { tabName: 'LabelDetectebrary' } }"
              style="margin-left: 30px;"
            />
          </el-col>
          <el-col v-if="opType > 2 && opTypeOptions[opType] && opType != 7 && opType != 8 && opType != 9" :span="12">
            <FormItem :label="$t('pages.sensitiveStrategy')" class="content-flex" prop="contentStgId">
              <el-select v-model="temp.contentStgId" class="fixed-height" :disabled="!editable">
                <el-option v-for="(value, key) in contentStgOptions" :key="key" :label="value" :value="Number.parseInt(key)"></el-option>
              </el-select>
              <link-button btn-class="editBtn" :formable="editable" :menu-code="'E28'" :always-show="true" :link-url="{ path: toPath, query: { stgTypeNumber: 125, treeable: false } }"/>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.scanDir')" prop="scanDir">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.diskScan_Msg8') }}<br/>
              {{ $t('pages.diskScan_Msg9') }}<br/>
              {{ $t('pages.diskScan_Msg10') }}<br/>
              {{ $t('pages.diskScan_Msg37') }}<br/>
              {{ $t('pages.diskScan_Msg38') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>

          <tag
            :list="temp.scanDir"
            border
            :disabled="!formable || !editable"
            :class="formable ? 'input-with-button' : ''"
            style="min-height: 30px; margin-top: 2px; background-color:#f5f5f5;"
          />
          <el-tooltip v-if="formable" class="item" effect="dark" :content="$t('pages.configScanDir')" placement="top">
            <el-button type="primary" :disabled="!formable" size="mini" @click="openConfigDir()">
              <svg-icon icon-class="setting" />
            </el-button>
          </el-tooltip>
        </FormItem>
        <FormItem :label="$t('pages.exceptionDirectory')" prop="exceptDir">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.diskScan_Msg11') }}<br/>
              {{ $t('pages.diskScan_Msg12') }}<br/>
              {{ $t('pages.diskScan_Msg13') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag :list="temp.exceptDir" border :disabled="!formable || !editable" style="min-height: 30px; background-color:#f5f5f5;"/>
        </FormItem>
        <FormItem :label="$t('pages.process_Msg5')" prop="suffix">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.diskScan_Msg14') }}<br/>
              {{ $t('pages.diskScan_Msg40') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag
            :list="temp.suffix"
            border
            :disabled="!formable || !editable"
            :class="formable ? 'input-with-button' : ''"
            style="width: calc(100% - 94px); min-height: 30px; background-color:#f5f5f5;"
            @tagChange="suffixChange"
          />
          <el-tooltip v-if="formable" class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" :disabled="!formable" size="mini" @click="handleFileSuffixImport()">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
          <el-button v-if="formable" :disabled="!formable" size="small" class="clear-btn" @click="handleClear">
            {{ $t('button.clear') }}
          </el-button>
        </FormItem>
        <FormItem :label="$t('pages.diskScan_Msg15')" prop="penetrateSuffix">
          <el-checkbox-group v-model="temp.penetrateSuffix" :disabled="!editable">
            <el-checkbox v-for="(suffix, index) in zipSuffixes" :key="index" :label="suffix">{{ suffix }}</el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.scanMode')" prop="scanMode">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.diskScan_Msg16') }}<br/>
                  {{ $t('pages.diskScan_Msg17') }}<br/>
                  {{ $t('pages.diskScan_Msg18') }}<br/>
                  {{ $t('pages.diskScan_Msg19') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-select v-model="temp.scanMode" :disabled="!editable" clearable>
                <el-option v-for="item in scanModeOptions" :key="item.id" :label="item.label" :value="item.id"></el-option>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem v-show="temp.scanMode === 0">
          <el-row style="text-align: right;">
            <el-col :span="6">
              <span>{{ $t('pages.diskScan_Msg20') }}</span>
            </el-col>
            <el-col :span="4">
              <el-input-number v-model="temp.cpuMem.cpu" controls-position="right" :min="1" :max="100" :disabled="!editable"></el-input-number>
            </el-col>
            <el-col :span="7">
              <span>{{ $t('pages.diskScan_Msg21') }}</span>
            </el-col>
            <el-col :span="4">
              <el-input-number v-model="temp.cpuMem.mem" controls-position="right" :min="1" :max="100" :disabled="!editable"></el-input-number>
            </el-col>
          </el-row>
        </FormItem>
        <!-- <FormItem>
          <el-checkbox v-model="temp.autoShutdown" :true-label="1" :false-label="0" :disabled="!editable">扫描结束后关机</el-checkbox>
        </FormItem>
        <FormItem>
          <el-checkbox v-model="temp.autoShutdown" :true-label="2" :false-label="0" :disabled="!editable">上报扫描状态</el-checkbox>
        </FormItem> -->
        <FormItem :label="$t('pages.diskScan_Msg28')">
          <el-checkbox v-model="temp.showProgress" :true-label="4" :false-label="0" :disabled="!editable">{{ $t('pages.diskScan_Msg29') }}</el-checkbox>
        </FormItem>

        <!-- <el-checkbox-group v-model="shutdownList">
          <FormItem :label="$t('pages.diskScan_Msg28')">
            <el-checkbox :label="4" :disabled="!editable">{{ $t('pages.diskScan_Msg29') }}</el-checkbox>
          </FormItem>
          <FormItem v-if="opType > 2 && opTypeOptions[opType] && opType != 7 && opType != 8 && opType != 9 && temp.scanCycle.length > 0" label="记录统计">
            <el-checkbox :label="16" :disabled="!editable">敏感检测记录统计分析</el-checkbox>
          </FormItem>
        </el-checkbox-group> -->
        <!-- <FormItem v-if="opType > 2 && opTypeOptions[opType] && opType != 7 && opType != 8 && opType != 9" label="周期配置">
          <el-radio-group v-model="scanTimeConfig">
            <el-radio :label="1">周期</el-radio>
            <el-radio :label="2">间隔天数</el-radio>
          </el-radio-group>
        </FormItem> -->
        <FormItem v-if="opType > 2 && opTypeOptions[opType] && opType != 7 && opType != 8 && opType != 9 && scanTimeConfig == 1" :label="$t('pages.backupScanTime')">
          <el-cascader
            v-model="temp.scanCycle"
            clearable
            class="scan-cycle"
            :disabled="!formable || !editable"
            :options="scanCycleOpts"
            :props="{ expandTrigger: 'hover' }"
            @change="handleDateChange"
          />
        </FormItem>
        <!-- <FormItem v-if="opType > 2 && opTypeOptions[opType] && opType != 7 && opType != 8 && opType != 9 && scanTimeConfig == 2" label="间隔天数">
          <el-input-number v-model="temp.intervalDays" :min="0" :max="365" :precision="0" :controls="false" :disabled="!editable" style="width: 100px" @change="changeIntervalDay"/>
        </FormItem>
        <FormItem v-if="opType > 2 && opTypeOptions[opType] && opType != 7 && opType != 8 && opType != 9" label="敏感分析">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              敏感文件详情记录敏感文件在当前已执行任务周期内出现的次数。eg:周期任务首次扫描文件a为敏感文件(首次新增)，将文件a删除或改为非敏感进行二次扫描不上报，将文件a调整为敏感文件进行第三次扫描(因第二次非敏感，故首次新增)
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-checkbox v-model="temp.recordCount" :true-label="16" :false-label="0" :disabled="!editable || !(temp.scanCycle.length > 0 || temp.intervalDays > 0)">敏感检测记录历史分析</el-checkbox>
        </FormItem>
        <FormItem v-if="opType > 2 && opTypeOptions[opType] && opType != 7 && opType != 8 && opType != 9" label="增量扫描">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              增量扫描：首次扫描后，文件创建时间及文件修改时间未发生变化，则不在增量扫描的扫描对象范围内
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-switch v-model="temp.scanType" :disabled="!formable || !editable || !(temp.scanCycle.length > 0 || temp.intervalDays > 0)" :active-value="1" :inactive-value="0"/>
        </FormItem>
        <FormItem v-if="opType > 2 && opTypeOptions[opType] && opType != 7 && opType != 8 && opType != 9" label="备份配置">
          <i18n style="font-weight: 500;" path="pages.diskScan_Msg45">
            <el-input slot="size" v-model="temp.backupSst" :disabled="!formable || !editable" class="input-file-size" @input="handleInput">
              <el-select slot="append" v-model="temp.backupUnit" style="width: 80px;margin-bottom: 4px" @change="handleChange">
                <el-option label="KB" :value="1"/>
                <el-option label="MB" :value="2"/>
                <el-option label="GB" :value="3"/>
              </el-select>
            </el-input>
          </i18n>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 480px;">{{ $t('pages.diskScan_Msg45') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem> -->
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.endDate')" prop="endDate">
              <el-date-picker v-model="temp.endDate" type="date" value-format="yyyy-MM-dd" :disabled="!editable" :picker-options="pickerOptions" style="width: 100%;" :placeholder="$t('pages.diskScan_Msg22')"></el-date-picker>
            </FormItem>
          </el-col>
        </el-row>
      </template>
      <template slot="button">
        <el-button v-if="editable" type="primary" :loading="submitting" @click="saveAndStartScan">
          {{ $t('pages.startScan') }}
        </el-button>
        <el-button v-if="formable && !editable" type="primary" :loading="submitting" @click="stopScanFunc">
          {{ $t('pages.stopScan') }}
        </el-button>
      </template>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>

    <el-dialog
      ref="configDir"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.configScanDir')"
      :visible.sync="dirVisible"
      width="650px"
    >
      <el-card class="box-card" style="margin: 5px;">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.commonDir') }}</span>
        </div>
        <div style="line-height: 22px;">
          <el-button type="text" @click="selectAll(1)">{{ $t('button.selectAll') }}</el-button>
          <el-button type="text" @click="selectAll(2)">{{ $t('button.cancelSelectAll') }}</el-button>
          <el-checkbox-group v-model="checkedCommonDir" style="margin-top: 6px;">
            <el-row>
              <el-col v-for="(item, index) in commonsDir" :key="index" :span="8">
                <el-checkbox :label="item.name" @change="commonDirChange($event)">
                  <span :title="item.title" class="ellipsis label-text">{{ item.name }}</span>
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </div>
      </el-card>
      <el-card class="box-card" style="margin: 5px;">
        <div slot="header" class="clearFix">
          <span>{{ $t('pages.specifyDir') }}</span>
          <span style="color: #3296FA;font-weight: normal;">
            ({{ $t('pages.diskScan_Msg44') }})
          </span>
        </div>
        <div style="display: flex;align-items: center; line-height: 22px;">
          <span style="width: 70px;">
            {{ $t('pages.scanDir') }}
          </span>
          <el-tooltip class="item" effect="dark" placement="bottom-start" style="margin-left: -9px;">
            <div slot="content">
              {{ $t('pages.diskScan_Msg33') }}<br>
              {{ $t('pages.diskScan_Msg37') }}<br>
              {{ $t('pages.diskScan_Msg35') }}<br>
              {{ $t('pages.diskScan_Msg36') }}<br>
              示例：目录(含子目录)：C:\test\ 只针对目录：C:\test
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag :list="specifyDir" border class="input-with-button" style="min-height: 30px;background-color:#f5f5f5;margin-left: 5px;"/>
        </div>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveConfigDir()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancelConfigDir()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getScanByName, createScan, updateScan, stopScan, getScanContentStgIdName } from '@/api/dataEncryption/encryption/diskScan'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import { countNum } from '@/api/dataEncryption/encryption/autoLabeling'

export default {
  name: 'DiskScanDlg',
  components: { FileSuffixLibImport },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      editable: true,
      submitting: false,
      slotName: undefined,
      scanModeOptions: [
        { id: 0, label: this.$t('pages.userDefined') },
        { id: 1, label: this.$t('pages.diskScan_Msg') },
        { id: 2, label: this.$t('pages.diskScan_Msg1') },
        { id: 3, label: this.$t('pages.diskScan_Msg2') }
      ],
      opTypeOptions: {
        1: this.$t('pages.fullDecryption'),
        2: this.$t('pages.globalEncryption'),
        3: this.$t('pages.diskScan_Msg4'),
        // 4: '全盘解密非敏感文件',
        5: this.$t('pages.diskScan_Msg23'),
        7: this.$t('pages.diskScan_Msg47'),
        8: this.$t('pages.diskScan_Msg48'),
        9: this.$t('pages.diskScan_Msg49')
      },
      opType: undefined,
      zipSuffixes: ['.zip', '.rar', '.7z'],
      contentStgOptions: {},
      temp: {},
      shutdownList: [],
      scanTimeConfig: 0,
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        opType: '',
        scanDir: [],
        exceptDir: ['Windows', 'Program Files', 'Local Settings'],
        suffix: [],
        penetrateSuffix: [],
        scanMode: null,
        cpuMem: { cpu: 50, mem: 50 },
        startTime: '',
        endTime: '',
        endDate: '',
        autoShutdown: 0,  // 字段按位计算，0-不配置；1-关机；2-上报状态；4-终端显示进度；5-敏感文件记录统计分析
        showProgress: 0, // 终端显示扫描进度,用于显示管理员日志
        recordCount: 0, // 敏感文件记录统计分析,用于显示管理员日志
        status: 1, // 0-开始扫描，1-停止扫描，2-暂停扫描
        entityType: '',
        entityId: '',
        contentStgId: '', // 内容检测策略ID
        scanType: 0,
        scanCycle: [],
        backupSst: 0,
        intervalDays: null,
        backupUnit: 2
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        scanMode: [{ required: true, validator: this.scanModeValidator, trigger: 'change' }],
        opType: [{ required: true, validator: this.opTypeValidator, trigger: ['blur', 'change'] }],
        suffix: [{ required: false, validator: this.suffixValidator, trigger: 'blur' }],
        contentStgId: [{ required: true, validator: this.contentRuleValidator, trigger: 'change' }],
        scanDir: [{ validator: this.scanDirValidator, trigger: 'blur' }],
        exceptDir: [{ validator: this.exceptDirValidator, trigger: 'blur' }],
        endDate: [{ required: true, validator: this.endDateValidator, trigger: 'blur' }],
        scanCycle: [{ required: true, validator: this.scanCycleValidator, trigger: ['blur', 'change'] }],
        intervalDays: [{ required: true, validator: this.intervalDaysValidator, trigger: 'blur' }]
      },
      scanStatus: 1,
      dealIds: [],
      pickerOptions: { // 时间设置今天以及今天之后
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      suffixMaxLength: 300,
      dirVisible: false,
      checkedCommonDir: [],
      checkedRealDirValue: [],
      specifyDir: [],
      commonsDir: [
        {
          name: this.$t('pages.scanDesktop'),
          value: '#DESKTOP#',
          title: this.$t('pages.diskScan_Desktop')
        },
        {
          name: this.$t('pages.scanDocument'),
          value: '#MYDOCUMENTS#',
          title: this.$t('pages.diskScan_Documents')
        },
        {
          name: this.$t('pages.scanFavorites'),
          value: '#FAVORITES#',
          title: this.$t('pages.diskScan_Favorites')
        },
        {
          name: this.$t('pages.scanMusic'),
          value: '#MYMUSICS#',
          title: this.$t('pages.diskScan_Musics')
        },
        {
          name: this.$t('pages.scanPictures'),
          value: '#MYPICTURES#',
          title: this.$t('pages.diskScan_Pictures')
        },
        {
          name: this.$t('pages.scanVideo'),
          value: '#MYVIDEOS#',
          title: this.$t('pages.diskScan_Videos')
        },
        {
          name: this.$t('pages.scanAppData'),
          value: '#APPDATA#',
          title: this.$t('pages.diskScan_AppData')
        }
      ],
      scanCycleOpts: []
    }
  },
  computed: {
    toPath() {
      let path = '/dataEncryption/encryption/diskScanSens'
      if (!this.hasPermission('124')) {
        path = '/contentStrategy/contentStg/diskScanSens'
      }
      return path;
    }
  },
  created() {
    this.resetTemp()
    this.loadContentStgOption()
    this.scanCycleOpts = this.buildScanCycleOptions()
    this.opTypeOptions = {}
    if (this.hasPermission('124')) {
      Object.assign(this.opTypeOptions, { 1: this.$t('pages.fullDecryption'), 2: this.$t('pages.globalEncryption') });
    }
    if (this.hasPermission('113')) {
      Object.assign(this.opTypeOptions, { 3: this.$t('pages.diskScan_Msg4') })
    }
    if (this.hasPermission('124 & 113')) {
      Object.assign(this.opTypeOptions, { 5: this.$t('pages.diskScan_Msg3')/*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/ })
    }
    if (this.hasPermission('125 & 113')) {
      Object.assign(this.opTypeOptions, { 7: this.$t('pages.diskScan_Msg47') })
    }
    if (this.hasPermission('125')) {
      Object.assign(this.opTypeOptions, { 8: this.$t('pages.diskScan_Msg48'), 9: this.$t('pages.diskScan_Msg49') })
    }
  },
  activated() {
    this.loadContentStgOption()
  },
  methods: {
    createScan,
    updateScan,
    getScanByName,
    buildScanCycleOptions() {
      const opts = []
      opts.push({ value: 1, label: this.$t('pages.everyDay') })
      const weekDays = [
        { value: 2, label: this.$t('pages.monday1') },
        { value: 4, label: this.$t('pages.tuesday1') },
        { value: 8, label: this.$t('pages.wednesday1') },
        { value: 16, label: this.$t('pages.Thursday1') },
        { value: 32, label: this.$t('pages.friday1') },
        { value: 64, label: this.$t('pages.saturday1') },
        { value: 1, label: this.$t('pages.sunday1') }
      ]
      opts.push({ value: 7, label: this.$t('pages.weekly'), children: weekDays })
      const monthDays = []
      for (let i = 1; i <= 31; i++) {
        monthDays.push({ value: i, label: this.$t('pages.dateNum', { date: i }) })
      }
      opts.push({ value: 31, label: this.$t('pages.monthly'), children: monthDays })
      return opts
    },
    saveConfigDir() {
      if (this.checkedRealDirValue.length == 0 && this.specifyDir.length == 0) {
        this.$message({
          title: this.$t('text.fail'),
          message: this.$t('pages.diskScan_Msg32'),
          type: 'error',
          duration: 3000
        })
        return
      }
      // 正则表达式，检测是否是带盘符的windows目录或正确的linux、mac目录
      const reg = /^(([a-zA-Z]:\\((((?! )[^/:*?<>\""|\\]+)+\\?)|(\\)?))|(\/))[\s\S]*$/
      // 检测是否是$:\test风格的window相对目录
      const specifyReg = /^(([$]:\\((((?! )[^/:*?<>\""|\\]+)+\\?)|(\\)?))|(\/))[\s\S]*$/
      if (this.specifyDir.length > 0) {
        for (const item of this.specifyDir) {
          if (!reg.test(item)) {
            // 不符合带盘符的windows目录，且不是linux或mac目录
            // 检测是否是$:\test风格的windos相对目录
            if (!specifyReg.test(item)) {
              this.$message({
                title: this.$t('text.fail'),
                message: this.$t('pages.diskScan_Msg30', { scanDir: item }),
                type: 'error',
                duration: 3000
              })
              return
            }
          }
        }
      }
      // 所有目录都通过了扫描
      const finalDir = []
      let num = 0
      if (this.checkedRealDirValue.length > 0) {
        this.checkedRealDirValue.forEach((item, index) => {
          finalDir[num] = item
          num++
        })
      }
      if (this.specifyDir.length > 0) {
        this.specifyDir.forEach((item, index) => {
          finalDir[num] = item
          num++
        })
      }
      // 将格式化好的目录赋值给外层弹窗的扫描目录
      finalDir.forEach((item, index) => {
        if (!this.temp.scanDir.includes(item)) {
          this.temp.scanDir.push(item)
        }
      })
      this.dirVisible = false
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    cancelConfigDir() {
      this.dirVisible = false
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
    },
    commonDirChange(event) {
      this.checkedRealDirValue = []
      if (this.checkedCommonDir.length > 0) {
        this.checkedCommonDir.forEach((res, pos) => {
          this.commonsDir.forEach((item, index) => {
            if (item.name == res) {
              this.checkedRealDirValue.push(item.value)
            }
          })
        })
      }
    },
    selectAll(type) {
      // type == 1 全选
      if (type == 1) {
        this.checkedCommonDir = []
        this.checkedCommonDir = this.commonsDir.map(item => item.name)
        this.checkedRealDirValue = []
        this.checkedRealDirValue = this.commonsDir.map(item => item.value)
      } else {
        this.checkedCommonDir = []
        this.checkedRealDirValue = []
      }
    },
    openConfigDir() {
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
      // if (this.temp.scanDir.length > 0) {
      //   this.temp.scanDir.forEach((item, index) => {
      //     this.commonsDir.forEach((res, pos) => {
      //       if (item == res.value) {
      //         this.checkedCommonDir.push(res.name)
      //         this.checkedRealDirValue.push(res.value)
      //       }
      //     })
      //   })
      // }
      this.dirVisible = true
    },
    suffixRuleValidator(value) {
      const suffix = value.trim()
      const t = suffix.split('');
      //  末尾为 .的禁止输入
      return !(t.length > 0 && t[t.length - 1] === '.');
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.shutdownList = []
      this.scanTimeConfig = 0
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.opTypeChange(this.temp.opType)
    },
    closed() {
      this.resetTemp()
    },
    loadContentStgOption() {
      getScanContentStgIdName(125).then(respond => {
        this.contentStgOptions = respond.data
      })
    },
    opTypeChange(val) {
      if (val) {
        this.temp.opType = val
        this.rules.suffix[0].required = val == 2
        this.$refs['stgDlg'].clearValidate()
      }
    },
    handleCreate() {
      this.editable = true
      this.scanStatus = 1
      this.dealIds.splice(0)
      this.rules.suffix[0].required = false
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.editable = this.formable
      this.scanStatus = 1
      this.dealIds.splice(0)
      this.opType = row.opType + ''
      if (!this.hasPermission('124') && (row.opType == 1 || row.opType == 2)) {
        this.opType = undefined
      }
      if (!this.hasPermission('113') && row.opType == 3) {
        this.opType = undefined
      }
      if (this.hasPermission('!124 | !113') && row.opType == 5) {
        this.opType = undefined
      }
      if (!this.hasPermission('125') && (row.opType == 7 || row.opType == 8 || row.opType == 9)) {
        this.opType = undefined
      }
      if (typeof row.scanCycle === 'string' && row.scanCycle) {
        const array = row.scanCycle.split('|')
        if (Number(array[0]) == 365) {
          this.scanTimeConfig = 2
        } else {
          this.scanTimeConfig = 1
        }
      }
      // if (row.autoShutdown) {
      //   this.shutdownList = this.numToList(row.autoShutdown, 5)
      // }
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(rowData) {
      this.dealIds.push(rowData.id)
      if (rowData.scanMode === 0 && rowData.cpuMem) {
        const array = rowData.cpuMem.split('|')
        rowData.cpuMem = { cpu: array[0], mem: array[1] }
      } else {
        rowData.cpuMem = { cpu: 50, mem: 50 }
      }
      if (rowData.penetrateSuffix && !Array.isArray(rowData.penetrateSuffix)) {
        rowData.penetrateSuffix = rowData.penetrateSuffix.split('|')
      } else {
        rowData.penetrateSuffix = []
      }
      rowData.opType += ''
      // this.rules.suffix[0].required = this.temp.opType == 2
      rowData.scanDir = !rowData.scanDir ? [] : rowData.scanDir.split('|')
      rowData.exceptDir = !rowData.exceptDir ? [] : rowData.exceptDir.split('|')
      rowData.suffix = !rowData.suffix ? [] : rowData.suffix.split('|')
      if (this.editable) {
        this.editable = rowData.status != 0
        this.$refs['stgDlg'].formable = this.editable
      }
      if (rowData.autoShutdown) {
        const shutdownList = this.numToList(rowData.autoShutdown, 5)
        rowData.showProgress = shutdownList.indexOf(4) > -1 ? 4 : 0
        rowData.recordCount = shutdownList.indexOf(16) > -1 ? 16 : 0
        // this.$set(rowData, 'recordCount', shutdownList.indexOf(16) > -1 ? 16 : 0)
      }
      if (typeof rowData.scanCycle === 'string' && rowData.scanCycle) {
        const array = rowData.scanCycle.split('|')
        if (array.length === 1) {
          rowData.scanCycle = [Number(array[0])]
        } else {
          if (Number(array[0]) == 365) {
            rowData.intervalDays = Number(array[0]) == 365 ? Number(array[1]) : null
          } else {
            rowData.scanCycle = [Number(array[0]), Number(array[1])]
          }
        }
      } else {
        rowData.scanCycle = []
      }
      if (rowData.backupUnit == 1) {
        rowData.backupSst = rowData.backupSst / 1024
      } else if (rowData.backupUnit == 2) {
        rowData.backupSst = rowData.backupSst / 1024 / 1024
      } else if (rowData.backupUnit == 3) {
        rowData.backupSst = rowData.backupSst / 1024 / 1024 / 1024
      }
    },
    formatFormData(formData) {
      formData.status = this.scanStatus
      formData.cpuMem = formData.cpuMem.cpu + '|' + formData.cpuMem.mem
      formData.penetrateSuffix = formData.penetrateSuffix.join('|')
      formData.scanDir = formData.scanDir.join('|')
      formData.exceptDir = formData.exceptDir.join('|')
      formData.suffix = formData.suffix.join('|')
      this.scanStatus = 1 // 置为默认值
      const autoShutdown = []
      // formData.autoShutdown = formData.showProgress
      // if (this.shutdownList && this.shutdownList.length > 0) {
      //   formData.showProgress = this.shutdownList.indexOf('4') > -1 ? 1 : 0
      //   formData.recordCount = this.shutdownList.indexOf('16') > -1 ? 1 : 0
      // }
      formData.opType = this.opType
      formData.scanCycle = (typeof formData.scanCycle === 'string' && formData.scanCycle) ? formData.scanCycle : formData.scanCycle ? formData.scanCycle.join('|') : ''
      if (!formData.scanCycle && !formData.intervalDays) {
        formData.scanType = 0
        formData.recordCount = 0
      }
      if (formData.recordCount) {
        autoShutdown.push(formData.recordCount)
      }
      if (formData.showProgress) {
        autoShutdown.push(formData.showProgress)
      }
      formData.autoShutdown = this.getSum(autoShutdown)
      if (formData.intervalDays) {
        formData.scanCycle = '365|' + formData.intervalDays
      }
      if (formData.backupUnit == 1) {
        formData.backupSst = formData.backupSst * 1024
      } else if (formData.backupUnit == 2) {
        formData.backupSst = formData.backupSst * 1024 * 1024
      } else if (formData.backupUnit == 3) {
        formData.backupSst = formData.backupSst * 1024 * 1024 * 1024
      }
    },
    async validateFormData(formData) {
      await countNum().then(res => {
        if (res.data == 0 && formData.opType == 7) {
          this.$message({
            message: '标签检测规则库未进行相应配置，策略无实际生效效果',
            type: 'warning',
            duration: 4000
          })
        }
      })
      return true
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.opType = undefined
      this.submitting = false
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    submitFailEnd() {
      this.submitting = false
    },
    saveAndStartScan() {
      this.submitting = true
      this.scanStatus = 0 // 开始扫描
      this.$refs['stgDlg'].saveData()
    },
    stopScanFunc() {
      this.$confirmBox(this.$t('pages.diskScan_Msg24'), this.$t('text.prompt')).then(() => {
        this.submitting = true
        stopScan({ ids: this.dealIds.join(',') }).then(respond => {
          this.submitEnd('update')
          this.$refs['stgDlg'].hide()
          this.submitting = false
        }).catch(() => { this.submitting = false })
      }).catch(() => {})
    },
    opTypeValidator(rule, value, callback) {
      const optypeName = this.opTypeOptions[this.opType]
      if (!optypeName) {
        callback(new Error(this.$t('pages.sysAlarmConfig_text7')))
      } else {
        callback()
      }
    },
    suffixValidator(rule, value, callback) {
      if (this.temp.suffix.length > 0) {
        const appendSuffix = this.temp.suffix.join('|')
        if (appendSuffix.length > 300) {
          callback(new Error(this.$t('pages.diskScan_Msg43')))
        }
      }
      if (this.temp.opType && this.temp.opType == 2 && this.temp.suffix.length === 0) {
        callback(new Error(this.$t('pages.sysAlarmConfig_text8')))
      } else if (this.temp.opType && this.temp.opType == 2 && this.temp.suffix.length !== 0) {
        let fileSuffixVisible = false
        this.temp.suffix.forEach((item, pos) => {
          if (item === '*.*') {
            fileSuffixVisible = true
          }
        })
        if (fileSuffixVisible) {
          callback(new Error(this.$t('pages.diskScan_Msg40')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    contentRuleValidator(rule, value, callback) {
      if (!this.temp.contentStgId) {
        callback(new Error(this.$t('pages.sysAlarmConfig_text9')))
      } else {
        callback()
      }
    },
    exceptDirValidator(rule, value, callback) {
      if (this.temp.exceptDir) {
        if (this.temp.exceptDir.length > 0) {
          const appendExceptDir = this.temp.exceptDir.join('|')
          if (appendExceptDir.length > 300) {
            callback(new Error(this.$t('pages.diskScan_Msg42')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    scanDirValidator(rule, value, callback) {
      const reg = /^(([a-zA-Z]:\\((((?! )[^/:*?<>\""|\\]+)+\\?)|(\\)?))|(\/))[\s\S]*$/
      if (this.temp.scanDir) {
        // const scanDirs = this.temp.scanDir.split('|')
        if (this.temp.scanDir.length > 0) {
          const appendScanDir = this.temp.scanDir.join('|')
          if (appendScanDir.length > 300) {
            callback(new Error(this.$t('pages.diskScan_Msg41')))
          }
        }
        const scanDirs = this.temp.scanDir
        let flag
        let dir
        for (let i = 0; i < scanDirs.length; i++) {
          let commonValue = false
          let specifyValue = false
          dir = scanDirs[i]
          for (let j = 0; j < this.commonsDir.length; j++) {
            if (dir == this.commonsDir[j].value) {
              // 当前值属于常用目录，可以不用输入完整盘符
              commonValue = true
            }
          }
          if (commonValue) {
            continue
          }
          if (dir.charAt(0) == '$' && dir.charAt(1) == ':') {
            // 全盘扫描dir目录，也可以不用输入完整盘符
            specifyValue = true
          }
          if (specifyValue) {
            continue
          }
          if (dir && !reg.test(dir)) {
            flag = true
            break
          }
        }
        if (flag && flag === true) {
          callback(new Error(this.$t('pages.diskScan_Msg25', { path: dir })))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    endDateValidator(rule, value, callback) {
      if (!this.temp.endDate) {
        callback(new Error(this.$t('pages.sysAlarmConfig_text12')))
      } else {
        callback()
      }
    },
    scanCycleValidator(rule, value, callback) {
      debugger
      if (this.scanTimeConfig == 1 && this.temp.scanCycle.length == 0) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    intervalDaysValidator(rule, value, callback) {
      debugger
      if (this.scanTimeConfig == 2 && (this.temp.intervalDays == 0 || this.temp.intervalDays == null)) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    scanModeValidator(rule, value, callback) {
      if (this.temp.scanMode === null || this.temp.scanMode === undefined || this.temp.scanMode === '') {
        callback(new Error(this.$t('pages.diskScan_Msg27')))
      } else {
        callback()
      }
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      let union_suffix = [...new Set(this.temp.suffix.concat(new_suffix))].join('|')
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      this.temp.suffix = union_suffix.split('|')
    },
    suffixChange(list) {
      let errFlag = false
      const newMap = new Map()
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        //  校验是否符合规则
        if (this.suffixRuleValidator(item)) {
          //  *.*时不在前面自动添加.
          if (item !== '*.*') {
            if (!item.startsWith('.')) {
              item = '.' + item
            }
          }
          if (item.length >= 2) {
            newMap[item] = ''
          }
        } else {
          errFlag = true;
        }
      })
      this.temp.suffix = Object.keys(newMap) || [];

      if (errFlag) {
        this.$message({
          message: this.$t('pages.diskScanFileSuffixFormatterError') + '',
          type: 'warning',
          duration: 2000
        })
      }
    },
    handleClear() {
      this.temp.suffix.splice(0)
    },
    handleScanChange(val) {
      if (val == 0) {
        this.temp.scanCycle = []
        this.temp.intervalDays = null
        this.temp.scanType = 0
        this.temp.recordCount = 0
      }
      this.$nextTick(() => {
        this.$refs['stgDlg'].clearValidate('scanCycle')
        this.$refs['stgDlg'].clearValidate('intervalDays')
      })
    },
    handleDateChange(val) {
      if (val.length == 0 && !this.temp.intervalDays) {
        this.temp.scanType = 0
        this.temp.recordCount = 0
      }
      if (val && val.length > 0 && this.temp.intervalDays) {
        this.temp.intervalDays = 0
      }
    },
    changeIntervalDay(val) {
      if (!val && this.temp.scanCycle.length == 0) {
        this.temp.scanType = 0
        this.temp.recordCount = 0
      }
      if (val && this.temp.scanCycle && this.temp.scanCycle.length > 0) {
        this.temp.scanCycle = []
      }
    },
    handleInput(value) {
      // this.temp.backupSst = onlyInt(value)
      this.temp.backupSst = this.temp.backupSst.replace(/^0|[^0-9]/g, '')
      if (this.temp.backupUnit == 1 && this.temp.backupSst > 10485760) {
        this.temp.backupSst = 10485760
      } else if (this.temp.backupUnit == 2 && this.temp.backupSst > 10240) {
        this.temp.backupSst = 10240
      } else if (this.temp.backupUnit == 3 && this.temp.backupSst > 10) {
        this.temp.backupSst = 10
      }
    },
    handleChange() {
      if (this.temp.backupUnit == 1 && this.temp.backupSst > 10485760) {
        this.temp.backupSst = 10485760
      } else if (this.temp.backupUnit == 2 && this.temp.backupSst > 10240) {
        this.temp.backupSst = 10240
      } else if (this.temp.backupUnit == 3 && this.temp.backupSst > 10) {
        this.temp.backupSst = 10
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .clear-btn {
    width: 42px;
    height: 30px;
    margin: 2px 0 0;
    vertical-align: top;
    text-align: center;
  }
  .fixed-height >>>.el-input__inner {
    height: 30px !important;
  }
  .scan-cycle {
    width: 180px;
    line-height: 30px;
    >>>.el-input__inner:read-only {
      color: #666;
      background-color: #f5f5f5;
    }
  }
  .input-file-size {
    width: 160px;
    vertical-align: middle;
  }
</style>
