<template>
  <content-stg
    ref="contentStg"
    :stg-code="125"
    :drip-able="false"
    :respond-able="false"
    :active-able="false"
    :advanced-rule-able="false"
    :import-able="false"
    :export-able="false"
    :create-stg-func="createStg"
    :update-stg-func="updateStg"
    :delete-stg-func="deleteScanContentStg"
    :stg-type-label="$t('route.diskScanSens')"
    :rel-stg-type-label="$t('route.diskScan')"
  />
</template>

<script>
// content是html标签，组件作为标签不能使用该名称
import ContentStg from '@/views/contentStrategy/strategy/content'
import { createStg, updateStg, deleteScanContentStg } from '@/api/dataEncryption/encryption/diskScan'
export default {
  name: 'DiskScanSens',
  components: { ContentStg },
  data() {
    return {
    }
  },
  methods: {
    createStg,
    updateStg,
    deleteScanContentStg
  }
}
</script>
