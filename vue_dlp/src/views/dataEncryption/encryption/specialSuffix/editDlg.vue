<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.specialSuffixStg')"
      :stg-code="74"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createSpecialFileExtStrategy"
      :update="updateSpecialFileExtStrategy"
      :get-by-name="getSpecialFileExtStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <div>
          <!-- <el-button style="float:left" size="small" @click="handleImportExt">{{ $t('button.import') }}</el-button> -->
          <div class="toolbar" style="display: flex; padding-left: 5px">
            <data-editor
              :formable="formable"
              :popover-width="680"
              :updateable="suffixEditable"
              :deletable="suffixDeleteable"
              :importable="formable"
              :add-func="createSuffix"
              :update-func="updateSuffix"
              :delete-func="deleteSuffix"
              :cancel-func="resetSuffixTemp"
              :before-update="beforeUpdateSuffixTemp"
              :before-add="beforeAddSuffixtemp"
            >
              <Form ref="suffixForm" :rules="suffixRules" :model="suffixTemp" label-position="right" label-width="90px" style="width: 620px;">
                <FormItem v-if="operationType === 'update'" :label="$t('table.suffixes')" prop="suffix">
                  <el-input v-model="suffixTemp.suffix"></el-input>
                </FormItem>
                <FormItem v-if="operationType === 'create'" prop="suffixes" :label="$t('table.suffixes')">
                  <div>
                    <el-button v-if="formable" size="mini" @click="handleFileSuffixImport">
                      {{ $t('pages.specialSuffix_import') }}
                    </el-button>
                    <el-button v-if="formable" size="mini" @click="handleClear">
                      {{ $t('button.clear') }}
                    </el-button>
                  </div>
                  <tag
                    v-model="suffixTemp.suffixes"
                    :border="true"
                    :list="suffixTemp.suffixes"
                    :input-width="300"
                    :overflow-able="true"
                    max-height="150px"
                    :placeholder="$t('pages.specialSuffix_text5')"
                    :disabled="!formable"
                    style="margin-top: 5px;"
                    @tagChange="tagChange"
                  />
                </FormItem>

                <FormItem>
                  <el-checkbox-group v-model="suffixTemp.codes" :disabled="!formable" @change="() => { this.$forceUpdate() }">
                    <el-checkbox :label="1">{{ $t('table.suffixCode1') }}</el-checkbox>
                    <el-checkbox :label="8">{{ $t('table.suffixCode2') }}</el-checkbox>
                  </el-checkbox-group>
                </FormItem>
              </Form>
            </data-editor>
          </div>
        </div>
        <grid-table
          ref="suffixList"
          :height="230"
          :multi-select="true"
          :show-pager="false"
          :col-model="suffixColModel"
          :row-datas="temp.suffixes"
          @selectionChangeEnd="suffixSelectionChange"
        />
      </template>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>

  </div>
</template>

<script>
import { getSpecialFileExtStrategyByName, createSpecialFileExtStrategy, updateSpecialFileExtStrategy } from '@/api/dataEncryption/encryption/specialSuffix'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'

export default {
  name: 'SpecialSuffixDlg',
  components: { FileSuffixLibImport },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        suffixes: [],
        entityType: undefined,
        entityId: undefined
      },
      suffixTemp: {
        suffixes: [],
        suffix: undefined,
        codes: []
      },
      suffixColModel: [ // 特殊文件后缀新增界面中使用
        { prop: 'suffix', label: 'suffixes', width: '150', sort: true },
        { label: 'suffixCode1', renderHeader: this.renderHeader, width: '170', formatter: (row, data) => { return this.suffixCodeFormatter(row, data, 1) } },
        { label: 'suffixCode2', renderHeader: this.renderHeader, width: '130', formatter: (row, data) => { return this.suffixCodeFormatter(row, data, 2) } }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      suffixRules: {
        suffix: [
          { required: true, message: this.$t('pages.process_Msg12'), trigger: 'blur' },
          { validator: this.suffixValidator, trigger: 'blur' }
        ],
        suffixes: [
          { required: true, message: this.$t('pages.process_Msg12'), trigger: 'blur' },
          { validator: this.fileSuffixesValidator, trigger: 'blur' }]
      },
      suffixEditable: false,
      suffixDeleteable: false,
      defaultSuffixList: [
        { label: this.$t('pages.builtFileSuffix'), id: 0, children: [
          { label: '.doc', id: 2 },
          { label: '.docx', id: 3 },
          { label: '.pdf', id: 7 },
          { label: '.ppt', id: 8 },
          { label: '.pptx', id: 9 },
          { label: '.txt', id: 10 },
          { label: '.rtf', id: 11 },
          { label: '.xls', id: 12 },
          { label: '.xlsx', id: 13 },
          { label: '.png', id: 14 },
          { label: '.jpg', id: 15 },
          { label: '.bmp', id: 16 },
          { label: '.gif', id: 17 }
        ] }
      ],
      defaultSuffixDlgVisible: false,
      submitting: false,
      slotName: undefined,

      operationType: ''
    }
  },
  computed: {

  },
  created() {
    this.resetSuffixTemp()
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createSpecialFileExtStrategy,
    updateSpecialFileExtStrategy,
    getSpecialFileExtStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    resetSuffixTemp() {
      this.suffixBtnMode = 'add'
      this.suffixTemp = { suffix: undefined, codes: [], suffixes: [] }
      const table = this.$refs['suffixList']
      if (table) {
        table.setCurrentRow()
      }
      this.$refs['suffixForm'] && this.$refs['suffixForm'].clearValidate()
      this.operationType = ''
    },
    // handleImportExt() {
    //   this.defaultSuffixDlgVisible = true
    //   this.$refs.suffixTree && this.$refs.suffixTree.$refs.tree.setCheckedKeys([])
    // },
    // importExt() {
    //   const checkNodes = this.$refs.suffixTree.$refs.tree.getCheckedNodes()
    //   const existList = []
    //   checkNodes.forEach(item => {
    //     this.temp.suffixes.forEach(item2 => {
    //       if (item2.suffix == item.label) {
    //         existList.push(item.label)
    //       }
    //     })
    //   })
    //   if (existList.length > 0) {
    //     this.$message({ type: 'error', duration: 2000, message: this.$t('pages.specialSuffix_text7') + existList.join(',') })
    //     return
    //   }
    //   checkNodes.forEach(item => {
    //     if (item.id != 0) {
    //       this.temp.suffixes.unshift({ id: item.id, suffix: item.label, code: 9 })
    //     }
    //   })
    //   this.defaultSuffixDlgVisible = false
    // },
    suffixSelectionChange(rowDatas) {
      this.suffixDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.suffixEditable = true
      } else {
        this.suffixEditable = false
        this.resetSuffixTemp()
      }
    },
    createSuffix() {
      let validate
      this.$refs['suffixForm'].validate((valid) => {
        if (valid) {
          let totalCode = 0
          this.suffixTemp.codes.forEach(code => { totalCode += code })
          //  批量添加
          for (let i = 0, len = this.suffixTemp.suffixes.length; i < len; i++) {
            this.temp.suffixes.unshift({
              id: new Date().getTime() + i,
              suffix: this.suffixTemp.suffixes[i],
              code: totalCode
            })
          }

          // this.temp.suffixes.unshift({
          //   id: new Date().getTime(),
          //   suffix: this.suffixTemp.suffix,
          //   code: totalCode
          // })
          this.resetSuffixTemp()
          validate = valid
        }
      })
      return validate
    },
    beforeUpdateSuffixTemp() {
      this.suffixBtnMode = 'update'
      const select = this.$refs['suffixList'].getSelectedDatas()[0]
      this.suffixTemp = Object.assign({}, select)
      if (select.code === 9) {
        this.suffixTemp.codes = [1, 8]
      } else {
        this.suffixTemp.codes = [select.code]
      }
      this.operationType = 'update'
    },
    beforeAddSuffixtemp() {
      this.operationType = 'create'
    },
    updateSuffix() {
      let validate
      this.$refs['suffixForm'].validate((valid) => {
        if (valid) {
          let totalCode = 0
          this.suffixTemp.codes.forEach(code => { totalCode += code })
          const rowData = Object.assign({}, this.suffixTemp, { code: totalCode })
          for (let i = 0, size = this.temp.suffixes.length; i < size; i++) {
            const data = this.temp.suffixes[i]
            if (rowData.id === data.id) {
              this.temp.suffixes.splice(i, 1, rowData)
              break
            }
          }
          this.resetSuffixTemp()
          validate = valid
        }
      })
      return validate
    },
    deleteSuffix() {
      const toDeleteIds = this.$refs['suffixList'].getSelectedIds()
      for (let i = 0; i < this.temp.suffixes.length; i++) {
        const data = this.temp.suffixes[i]
        if (toDeleteIds.indexOf(data.id) >= 0) {
          this.temp.suffixes.splice(i, 1)
          i--
        }
      }
      this.resetSuffixTemp()
    },
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.resetSuffixTemp()
        this.$refs.suffixForm && this.$refs.suffixForm.clearValidate()
      })
    },
    handleUpdate(row) {
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.resetSuffixTemp()
        this.$refs.suffixForm && this.$refs.suffixForm.clearValidate()
      })
    },
    formatRowData(rowData) {
      rowData.suffixes.forEach((item, index) => {
        item.id = index
      })
    },
    formatFormData(formData) {
      formData.suffixes.forEach(item => {
        delete item.id
      })
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    renderHeader(h, { column, $index }) {
      const codeMap = { 2: 1, 3: 8 }
      const code = codeMap[$index]
      const label = column.label
      let indeterminate = false
      let hasCheck = false
      let allCheck = this.temp.suffixes.length > 0
      this.temp.suffixes.forEach(item => {
        if (item.code & code) {
          hasCheck = true
        } else {
          allCheck = false
        }
      })
      indeterminate = hasCheck && !allCheck
      return (
        <span title={label}><el-checkbox v-model={allCheck} indeterminate={indeterminate} disabled={!this.formable} on-change={v => this.headerCheckboxChange(v, $index)}></el-checkbox> {label}</span>
      )
    },
    headerCheckboxChange(val, colIndex) {
      const codeMap = { 2: 1, 3: 8 }
      const code = codeMap[colIndex]
      this.temp.suffixes.forEach(item => {
        const isExist = item.code & code
        const add = (val && isExist) || (!val && !isExist) ? 0 : code
        item.code += val ? add : -add
      })
    },
    suffixCodeFormatter(row, data, index) {
      const codes = [1, 8]
      const showCode = codes[index - 1]
      return showCode === row.code || row.code === 9 ? this.$t('text.yes') : this.$t('text.no')
    },
    suffixValidator(rule, value, callback) {
      let isExist = false
      for (let i = 0, size = this.temp.suffixes.length; i < size; i++) {
        const row = this.temp.suffixes[i]
        if (row.suffix.toLowerCase() === value.toLowerCase()) {
          if (this.suffixBtnMode === 'add' || row.id !== this.suffixTemp.id) {
            callback(new Error(this.$t('pages.specialSuffix_text8')))
            isExist = true
            break
          }
        }
      }
      if (!isExist) callback()
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      let new_suffix = suffix.split('|');
      if (this.suffixTemp.suffixes === undefined || this.suffixTemp.suffixes === null) {
        this.suffixTemp.suffixes = []
      }
      new_suffix = this.validAddData(new_suffix);
      (new_suffix || []).forEach(item => {
        //  过滤已添加的文件后缀名
        if (!this.suffixTemp.suffixes.includes(item)) {
          this.suffixTemp.suffixes.push(item)
        }
      })
    },
    handleClear() {
      this.suffixTemp.suffixes.splice(0)
    },
    //  当文件后缀列表发送改变时，校验文件后缀名是否符合规则
    tagChange(list) {
      list = this.validAddData(list)
      this.suffixTemp.suffixes = list;
    },
    validAddData(list) {
      list = this.filterRepetitionFileSuffix(list);
      list = this.validAddDotMark(list);
      list = this.verifyFileSuffixExits(list)
      return list;
    },
    //  过滤重复文件后缀库
    filterRepetitionFileSuffix(list) {
      const uniqueSet = new Set()
      //  过滤掉相同文件后缀名(不区分大小写）
      return list.filter(item => {
        const lowerCase = item.toLowerCase()
        const isUnique = !uniqueSet.has(lowerCase)
        isUnique && uniqueSet.add(lowerCase)
        return isUnique
      })
    },
    //  校验后缀名是否正确，若首个文件后缀名称不是.  自动添加
    validAddDotMark(list) {
      return list.map(item => {
        return item[0] === '.' ? item : `.${item}`
      })
    },
    //  校验后缀是否已存在
    verifyFileSuffixExits(suffixes) {
      // 已添加的后缀Set
      const suffixesSet = new Set(this.temp.suffixes.map(item => item.suffix))
      // 重复的后缀
      const repeatSuffixes = []
      suffixes = suffixes.filter(suffix => {
        const isRepeat = suffixesSet.has(suffix)
        if (isRepeat) {
          repeatSuffixes.push(suffix)
        }
        return !isRepeat;
      })
      if (repeatSuffixes.length > 0) {
        this.$message({
          message: this.$t('pages.specialSuffix_suffixNamesExisted', { suffixNames: repeatSuffixes.join(',') }),
          type: 'error',
          duration: 2000
        })
      }
      return suffixes;
    },
    fileSuffixesValidator(rule, value, callback) {
      if (this.operationType === 'create') {
        if (this.suffixTemp.suffixes.length === 0) {
          callback(new Error(this.$t('pages.specialSuffix_suffixNamesNotNull')))
        } else {
          callback()
        }
      }
    }
  }
}
</script>
