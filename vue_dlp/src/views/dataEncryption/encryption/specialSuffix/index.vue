<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button v-if="tabName == 'specialFileExt'" icon="el-icon-delete" size="mini" :disabled="!fileExtDeleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <el-button v-if="tabName == 'autoBackupFilter'" icon="el-icon-delete" size="mini" :disabled="!filterDeleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!fileExtDeleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="specialFileExtList"
        :col-model="fileExtColModel"
        :row-data-api="fileExtRowDataApi"
        :after-load="afterLoad"
        :selectable="selectable"
        @selectionChangeEnd="fileExtSelectionChangeEnd"
      />
      <!--<el-tabs ref="tabs" v-model="tabName" type="card" style="height: calc(100% - 40px);" @tab-click="tabClick">
        <el-tab-pane label="文件后缀策略" name="specialFileExt">
          <div class="table-container">
            <grid-table
              ref="specialFileExtList"
              :col-model="fileExtColModel"
              :row-data-api="fileExtRowDataApi"
              :after-load="afterLoad"
              :selectable="selectable"
              @selectionChangeEnd="fileExtSelectionChangeEnd"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="备份过滤策略" name="autoBackupFilter">
          <div class="table-container">
            <grid-table
              ref="autoBackupFilterList"
              :col-model="filterColModel"
              :row-data-api="filterRowDataApi"
              :after-load="afterLoad"
              :selectable="selectable"
              @selectionChangeEnd="autoBackupFilterSelectionChangeEnd"
            />
          </div>
        </el-tab-pane>
      </el-tabs>-->
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="filterDialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="filterForm" :rules="rules" :model="temp" label-position="right" label-width="90px">
        <stg-target-form-item
          ref="formItem"
          :stg-code="24"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" :disabled="!formable" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit />
        </FormItem>
        <FormItem :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <FormItem :label="$t('table.dirName')">
          <el-input v-model="temp.dir" type="textarea" rows="3" :disabled="!formable" :placeholder="$t('pages.specialSuffix_text1')"></el-input>
        </FormItem>
        <FormItem :label="$t('table.processName1')">
          <el-input v-model="temp.process" type="textarea" rows="3" :disabled="!formable" :placeholder="$t('pages.specialSuffix_text2')"></el-input>
        </FormItem>
        <FormItem :label="$t('table.maxFileSize')">
          <el-input v-model="temp.maxFileSize" :disabled="!formable" :placeholder="$t('pages.specialSuffix_text3')"></el-input>
        </FormItem>
        <div style="color: #0c60a5;padding-top: 10px">
          {{ $t('pages.specialSuffix_text4') }}
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="filterDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <special-suffix-dlg
      ref="stgDlg"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    />
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importSpecialSuffixStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import SpecialSuffixDlg from './editDlg'
import { getSpecialFileExtStrategyList, deleteSpecialFileExtStrategy } from '@/api/dataEncryption/encryption/specialSuffix'
import { getAutoBackupFilterStrategyList, getAutoBackupFilterStrategyByName, createAutoBackupFilterStrategy, updateAutoBackupFilterStrategy, deleteAutoBackupFilterStrategy } from '@/api/dataEncryption/encryption/autoBackupFilterStrategy'
import { enableStgBtn, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { osTypeIconFormatter, stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import { validatePolicy } from '@/utils/validate'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'SpecialSuffix',
  components: { SpecialSuffixDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    tabName: { type: String, default: 'specialFileExt' },
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 74,
      fileExtColModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'suffixes', label: 'stgMessage', width: '200', formatter: this.suffixesFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      filterColModel: [
        { prop: 'name', label: 'stgName', width: '150', fixed: true },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'dirName', width: '200', formatter: (row, data) => { return this.arrayFormatter(row, data, 'dir') } },
        { label: 'processName1', width: '200', formatter: (row, data) => { return this.arrayFormatter(row, data, 'process') } },
        { label: 'maxFileSize', width: '200', formatter: this.maxFileSizeFormatter },
        { prop: 'remark', label: 'remark', width: '200' },
        { prop: 'active', label: 'enable', fixedWidth: '80', type: 'icon', iconClass: 'active' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      fileExtDeleteable: false,
      filterDeleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 自动备份过滤表单字段
        id: undefined,
        entityType: undefined,
        entityId: undefined,
        name: '',
        active: false,
        dir: '',
        process: '',
        maxFileSize: undefined,
        remark: ''
      },
      filterDialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.autoBackupStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.autoBackupStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      checkedEntityNode: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['specialFileExtList']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.fileExtColModel, this.treeable)
    hiddenActiveAndEntity(this.filterColModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    filterTable() {
      return this.$refs['autoBackupFilterList']
    },
    tabClick(pane, event) {
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    fileExtRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getSpecialFileExtStrategyList(searchQuery)
    },
    filterRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getAutoBackupFilterStrategyList(searchQuery)
    },
    fileExtSelectionChangeEnd: function(rowDatas) {
      if (rowDatas.length > 0) {
        this.fileExtDeleteable = true
      }
    },
    autoBackupFilterSelectionChangeEnd: function(rowDatas) {
      if (rowDatas.length > 0) {
        this.filterDeleteable = true
      }
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      const _this = this
      if (_this.tabName == 'specialFileExt') {
        _this.gridTable.execRowDataApi(_this.query)
      } else {
        _this.filterTable().execRowDataApi(_this.query)
      }
    },
    handleFilter() {
      this.query.page = 1
      if (this.tabName == 'specialFileExt') {
        this.gridTable.execRowDataApi(this.query)
      } else {
        this.filterTable().execRowDataApi(this.query)
      }
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      if (this.tabName == 'specialFileExt') {
        this.$refs['stgDlg'].handleCreate()
      } else {
        this.resetTemp()
        this.temp.entityType = this.query.objectType
        this.temp.entityId = this.query.objectId
        this.dialogStatus = 'create'
        this.filterDialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['filterForm'].clearValidate()
        })
      }
    },
    handleUpdate: function(row) {
      if (this.tabName == 'specialFileExt') {
        this.$refs['stgDlg'].handleUpdate(row)
      } else {
        this.resetTemp()
        this.temp = JSON.parse(JSON.stringify(row)) // copy obj
        let processes = ''
        let dirs = ''
        let maxFileSize = 0
        this.temp.filter.forEach(obj => {
          if (obj.dir) {
            if (dirs.length > 0) dirs += '\n'
            dirs += obj.dir
          }
          if (obj.process) {
            if (processes.length > 0) processes += '\n'
            processes += obj.process
          }
          if (obj.maxFileSize) {
            maxFileSize = obj.maxFileSize
          }
        })
        // this.temp.dir = dirs
        // this.temp.process = processes
        this.temp = Object.assign({}, this.temp, { dir: dirs, process: processes, maxFileSize: maxFileSize })

        this.dialogStatus = 'update'
        this.filterDialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['filterForm'].clearValidate()
        })
      }
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    formatFilterForm() {
      const filters = []
      let dirs = this.temp.dir
      dirs = dirs && dirs.length > 0 ? dirs.split('\n') : []
      dirs.forEach(dir => {
        filters.push({ dir: dir, process: '', maxFileSize: 0 })
      })
      let processes = this.temp.process
      processes = processes && processes.length > 0 ? processes.split('\n') : []
      processes.forEach(processe => {
        filters.push({ dir: '', process: processe, maxFileSize: 0 })
      })
      if (this.temp.maxFileSize) {
        filters.push({ dir: '', process: '', maxFileSize: Number.parseInt(this.temp.maxFileSize) })
      }
      this.temp.filter = filters
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['filterForm'].validate((valid) => {
        if (valid) {
          this.formatFilterForm()
          createAutoBackupFilterStrategy(this.temp).then(respond => {
            this.submitting = false
            this.filterDialogFormVisible = false
            this.filterTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['filterForm'].validate((valid) => {
        if (valid) {
          this.formatFilterForm()
          const tempData = Object.assign({}, this.temp)
          updateAutoBackupFilterStrategy(tempData).then(respond => {
            this.submitting = false
            this.filterDialogFormVisible = false
            this.filterTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        if (this.tabName == 'specialFileExt') {
          const toDeleteIds = this.gridTable.getSelectedIds()
          deleteSpecialFileExtStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
            this.gridTable.deleteRowData(toDeleteIds)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        } else {
          const toDeleteIds = this.filterTable().getSelectedIds()
          deleteAutoBackupFilterStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
            this.filterTable().deleteRowData(toDeleteIds)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    arrayFormatter(row, data, fieldName) {
      let result = ''
      row.filter.forEach(f => {
        const fvalue = f[fieldName]
        if (fvalue) result += fvalue + ';'
      })
      return result
    },
    maxFileSizeFormatter(row, data) {
      let result = 0
      row.filter.forEach(f => { result += Number.parseInt(f.maxFileSize) })
      return result > 0 ? result : ''
    },
    suffixesFormatter(row, data) {
      let result = ''
      data.forEach(s => { result += s.suffix + ';' })
      return result
    },
    strategyNameValidator(rule, value, callback) {
      getAutoBackupFilterStrategyByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>
<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  // .el-table el-table--fit el-table--striped el-table--border el-table--scrollable-x el-table--enable-row-transition{
  //   height: 534px;
  // }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
</style>

