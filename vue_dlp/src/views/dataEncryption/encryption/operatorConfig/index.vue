<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="deleteData">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
        class="module-form"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-card v-if="otherConfig.enable" class="box-card">
          <div slot="header">
            <span>{{ otherConfig.label }}</span>
          </div>
          <div style="line-height: 22px;">
            <el-checkbox-group v-model="otherConfig.checked">
              <el-row>
                <el-col v-for="(item, index) in otherConfig.options" :key="index" :span="!item.colSpan ? 6 : item.colSpan">
                  <el-checkbox v-if="!item.format" :label="item.id" :disabled="!formable">{{ item.label }}</el-checkbox>
                  <el-checkbox v-if="item.format === 1" :label="item.id" :disabled="!formable">
                    {{ item.label }}：
                  </el-checkbox>
                  <el-select
                    v-if="item.format === 1"
                    v-model="item.type"
                    :disabled="!formable || otherConfig.checked.indexOf(item.id) < 0"
                    size="mini"
                    style="width: 180px; top: -11px"
                  >
                    <el-option v-for="(val, key) in oleInsertOpts" :key="key" :value="parseInt(key)" :label="val"/>
                  </el-select>
                  <FormItem v-if="item.format==1 && (item.type==2 || item.type==3) && otherConfig.checked.indexOf(item.id) >= 0" prop="processList" label-width="0">
                    <label>{{ $t('table.processName') }}</label>
                    <tag v-model="temp.processList" :disabled="!formable || otherConfig.checked.indexOf(item.id)==-1" :border="true" :list="temp.processList"/>
                  </FormItem>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </div>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importOperatorConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="false"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { addConfig, updateConfig, deleteConfig, getConfigPage, getConfigByName } from '@/api/dataEncryption/encryption/operatorConfig'
import { enableStgBtn, enableStgDelete, selectable, objectFormatter, entityLink, refreshPage, buttonFormatter, hiddenActiveAndEntity } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'OperatorConfig',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 150,
      otherConfig: {
        enable: true, // 是否启用此配置
        label: this.$t('pages.advancedConfiguration'),
        checked: [],
        options: [
          { id: 4013, label: this.$t('pages.configOptions5') },
          { id: 4071, label: this.$t('pages.configOptions6'), format: 1, colSpan: 24, type: 0,
            valueFormatter: this.valueFormatter4071,
            formatValue: this.formatValue4071
          },
          { id: 4101, label: this.$t('pages.configOptions7') },
          { id: 4051, label: this.$t('pages.configOptions8') },
          { id: 4076, label: this.$t('pages.configOptions9') },
          { id: 4077, label: this.$t('pages.configOptions10') }
        ]
      },
      oleInsertOpts: {
        0: this.$t('pages.configOptions1'),
        1: this.$t('pages.configOptions2'),
        2: this.$t('pages.configOptions3'),
        3: this.$t('pages.configOptions4')
      },
      configMap: undefined,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ] },
        { prop: 'option', label: 'config', width: '250', formatter: this.optionFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        entityType: undefined,
        entityId: undefined
      },
      temp: {},
      oldTemp: {},
      entityName: '',
      showTree: true,
      submitting: false,
      isSelfConfig: false,
      validate: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.operatorConfigStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.operatorConfigStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        processList: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyTable']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    valueFormatter4071(o, isChecked) { // 界面数据格式化为策略数据
      return o.type
    },
    formatValue4071(o, value, isChecked) { // 策略数据格式化为界面数据
      o.type = !isChecked ? 0 : value
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getConfigPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = {
        name: undefined,
        id: undefined,
        active: false,
        entityType: undefined,
        entityId: undefined,
        option: [],
        strOption: [],
        processList: []
      }
    },
    handleCreate() {
      this.resetTemp()
      this.resetFormItem()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.resetFormItem()
      this.temp = JSON.parse(JSON.stringify(row))
      this.oldTemp = JSON.parse(JSON.stringify(row))
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.bean2FromItem(this.temp)
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    resetFormItem() { // 重置选择框
      const editConfigs = [this.otherConfig]
      editConfigs.forEach(config => {
        config.checked.splice(0)
        config.options.forEach(option => {
          if (option.formatStrValue) option.formatStrValue(option, '', false)
          if (option.formatValue) option.formatValue(option, '', false)
        })
      })
    },
    bean2FromItem(bean) { // 把策略转换为界面需要的格式
      this.isSelfConfig = bean.entityType == this.query.entityType && bean.entityId == this.query.entityId
      this.entityName = this.isSelfConfig ? this.$t('pages.self') : bean.entityName
      this.temp.id = bean.id
      // option和strOption合并在一起，这两个都是需要进行格式化
      const editOptions = bean.option.concat(bean.strOption)
      // 策略里面保存的数据整合成id：option的格式
      const optionMap = {}
      editOptions.forEach(option => { optionMap[option.id] = option })
      const editConfigs = [this.otherConfig]
      editConfigs.forEach(config => {
        config.checked.splice(0)
        config.options.forEach(option => {
          const checkedOption = optionMap[option.id]
          // 如果策略的option列表中，有这个配置，且值不为空，说明这个配置要选中
          const isChecked = checkedOption && checkedOption.value !== undefined
          if (isChecked) config.checked.push(option.id)
          // 这里是策略数据转换为界面数据时，进行一些自定义格式化
          if (option.formatStrValue) option.formatStrValue(option, checkedOption.strValue, isChecked)
          if (option.formatValue) option.formatValue(option, checkedOption.value, isChecked)
        })
      })
    },
    fromItem2Bean() { // 把界面数据转换为策略格式
      this.validate = true
      const stg = {
        name: this.temp.name,
        remark: this.temp.remark,
        active: this.temp.active,
        id: this.temp.id,
        entityType: this.query.objectType,
        entityId: this.query.objectId,
        objectType: this.temp.objectType,
        objectIds: this.temp.objectIds,
        objectGroupIds: this.temp.objectGroupIds,
        option: [],
        strOption: [],
        processList: this.temp.processList
      }
      const editConfigs = [this.otherConfig]
      for (let i = 0, size = editConfigs.length; i < size; i++) {
        const config = editConfigs[i]
        for (let j = 0, opSize = config.options.length; j < opSize; j++) {
          const option = config.options[j]
          const bean = { id: option.id }
          const isChecked = config.checked.indexOf(option.id) >= 0
          if (isChecked) {
            if (option.value) bean.value = option.value
            if (option.strValue) bean.strValue = option.strValue
            // 自定义格式化
            if (option.valueFormatter) bean.value = option.valueFormatter(option, isChecked)
            if (option.strValueFormatter) bean.strValue = option.strValueFormatter(option, isChecked)
          }
          if (this.validate) {
            if (isChecked && bean.value === undefined) {
              bean.value = 1
            }
            if (bean.strValue !== undefined) {
              stg.strOption.push(bean)
            } else {
              stg.option.push(bean)
            }
          } else {
            return null // 验证失败，直接返回
          }
        }
      }
      return stg
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const stg = this.fromItem2Bean()
          if (this.validate) {
            stg.entityId = this.temp.entityId
            stg.entityType = this.temp.entityType
            stg.description = this.optionFormatter(stg)
            addConfig(stg).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
            }).catch(res => {
              this.submitting = false
            })
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const stg = this.fromItem2Bean()
          stg.description = this.optionFormatter(this.oldTemp)
          stg.description_new = this.optionFormatter(stg)
          if (this.validate) {
            updateConfig(stg).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
            }).catch(res => {
              this.submitting = false
            })
          }
        } else {
          this.submitting = false
        }
      })
    },
    deleteData() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteConfig({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getConfigByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    optionFormatter: function(row, data) {
      if (!this.configMap) {
        this.configMap = {}
        this.otherConfig.options.forEach(item => { this.configMap[item.id] = item.label })
      }
      const result = []
      row.option.forEach(item => {
        if ('value' in item) {
          let desc = this.configMap[item.id]
          if (item.id === 4071) {
            desc += ':' + this.oleInsertOpts[item.value]
          }
          result.push(desc)
        }
      })
      /* row.strOption.forEach(item => {
        if (item.value) result.push(this.configMap[item.id])
      })*/
      return result.join('；')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>

<style lang="scss" scoped>
  .module-form{
    .el-col-6{
      min-width: 320px;
    }
    .el-col-12{
      min-width: 600px;
    }
  }
</style>
