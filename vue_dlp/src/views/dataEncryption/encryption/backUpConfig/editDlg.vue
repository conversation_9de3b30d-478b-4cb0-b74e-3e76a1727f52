<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      type="drawer"
      :title="$t('pages.backUpConfigStg')"
      :stg-code="114"
      :active-able="activeAble"
      label-w="125px"
      os-label-w="125px"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createData"
      :update="updateData"
      :get-by-name="getByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :body-scroll="scroll"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <FormItem :label="$t('pages.backUpType')">
          <!-- 终端备份文件设置文档描述备份类型去掉 不备份选项，因为加密文件不备份存在“文件损坏无法找回”的风险 -->
          <!-- <el-radio :label="0">{{ $t('pages.backupRule1') }}</el-radio> -->
          <el-col :span="6">
            <span style="margin-left: 12px">{{ $t('pages.backupRule3') }}</span>
            <el-popover
              v-model="popoverVisible1"
              :append-to-body="false"
              placement="top"
              width="400"
              trigger="click"
            >
              <i18n path="pages.backupRule8">
                <el-input
                  slot="size"
                  v-model="temp.fileSize"
                  :disabled="!formable"
                  class="input-box"
                  maxlength=""
                  @change="(val) => temp.fileSize = justInputNumber(val, -1, 1024 * 1024)"
                />
              </i18n>
              <el-tooltip :content="$t('pages.backUpConfig_text11')" effect="dark" placement="bottom">
                <i class="el-icon-info" />
              </el-tooltip>
              <el-button slot="reference" type="text" style="margin-bottom: 0;"><i class="el-icon-s-tools"/></el-button>
            </el-popover>
          </el-col>
          <el-col :span="8" style="display: flex; justify-content: center; align-items: center">
            <el-checkbox v-model="temp.isRemote" :true-label="2" :false-label="0" :disabled="!formable">
              {{ $t('pages.backupRule6') }}
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content">{{ $t('pages.serverBackupTip') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-checkbox>
            <el-popover
              v-model="popoverVisible2"
              :append-to-body="false"
              placement="top"
              width="422"
              trigger="click"
            >
              <i18n path="pages.backupRule9">
                <el-input
                  slot="size"
                  v-model="temp.serverFileSize"
                  :disabled="!formable"
                  class="input-box"
                  maxlength=""
                  @change="(val) => temp.serverFileSize = justInputNumber(val, -1, 1024 * 1024)"
                />
              </i18n>
              <el-tooltip :content="$t('pages.backUpConfig_text11')" effect="dark" placement="bottom">
                <i class="el-icon-info" />
              </el-tooltip>
              <el-button slot="reference" type="text" :disabled="temp.isRemote == 0" style="margin-bottom: 1px"><i class="el-icon-s-tools"/></el-button>
            </el-popover>
          </el-col>
        </FormItem>

        <el-divider content-position="left" class="divider">{{ $t('pages.backUpConfigSet') }}</el-divider>
        <el-card class="box-card" :body-style="{'padding': '10px'}">
          <p>
            <i18n path="pages.backUpConfigSaveFile">
              <el-input
                slot="day"
                v-model="temp.retentionDay"
                :disabled="!formable"
                class="input-box"
                @change="(val) => temp.retentionDay = justInputNumber(val, 7, 365)"
              />
            </i18n>
          </p>
          <p>
            <i18n path="pages.backUpConfigThresholdRule">
              <el-input
                slot="sys"
                v-model="temp.sysFreeSpace"
                :disabled="!formable"
                class="input-box"
                @change="(val) => temp.sysFreeSpace = justInputNumber(val, 2, 9999)"
              />
              <el-input
                slot="nonSys"
                v-model="temp.freeSpace"
                :disabled="!formable"
                class="input-box"
                @change="(val) => temp.freeSpace = justInputNumber(val, 2, 9999)"
              />
            </i18n>
          </p>
          <!-- <el-row>
            <el-col :span="12">
              <FormItem :label="$t('pages.backUpConfig_text8')" :tooltip-content="$t('pages.backUpConfig_text9')" prop="backupCopies">
                <el-input-number v-model="temp.backupCopies" style="width: 70%" :min="1" :max="3" :step="1" :controls="false" :disabled="!formable"/>
              </FormItem>
            </el-col>
            <el-col :span="8">
              <FormItem label="本地备份保留天数" tooltip-content="0表示不清除，其他为保留天数" prop="retentionDays">
                <el-input-number v-model="temp.retentionDays" style="width: 70%" :min="0" :step="1" :controls="false" :disabled="!formable"/>
              </FormItem>
            </el-col>
          </el-row> -->
        </el-card>

        <el-divider content-position="left" class="divider">{{ $t('pages.exceptSet') }}</el-divider>
        <el-card class="box-card" :body-style="{'padding': '10px'}">
          <data-editor
            :formable="formable"
            :popover-width="680"
            :updateable="ipPortEditable"
            :deletable="ipPortDeleteable"
            :add-func="createItem"
            :update-func="updateItem"
            :delete-func="deleteItem"
            :cancel-func="cancelItem"
            :before-update="beforeUpdateItem"
          >
            <Form ref="ipPortForm" :rules="itemRuls" :model="tempItem" label-position="right" label-width="120px">
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('table.filePath')" :tooltip-content="$t('pages.filePathContent')" prop="filePath" tooltip-placement="bottom-start">
                    <el-input v-model="tempItem.filePath" maxlength="" :disabled="!formable" />
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('table.suffixes')" :tooltip-content="$t('pages.suffixesContent')" prop="fileExt" tooltip-placement="bottom-start">
                    <el-input v-model="tempItem.fileExt" class="input-with-button" maxlength="" :disabled="!formable"/>
                    <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="bottom-start">
                      <el-button type="primary" size="mini" @click="handleFileSuffixImport()">
                        <svg-icon icon-class="import" />
                      </el-button>
                    </el-tooltip>
                  </FormItem>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('table.processName')" :tooltip-content="$t('pages.processContent')" prop="processName" tooltip-placement="bottom-start">
                    <el-input v-model="tempItem.processName" v-trim :disabled="!formable" :maxlength="255"/>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('table.maxFileSize')" :tooltip-content="$t('pages.fileSizeContent')" prop="fileSize" tooltip-placement="bottom-start">
                    <el-input-number v-model="tempItem.fileSize" class="cz-input-number" :min="0" :max="500" :step="1" step-strictly :controls="false"/>
                  </FormItem>
                </el-col>
              </el-row>
              <!--<el-row>
                  <el-col :span="12">
                    <FormItem label="控制类型" tooltip-content="满足前面条件时，才进行的操作" prop="ctrlcode">
                      <el-radio-group v-model="tempItem.ctrlcode">
                        <el-radio :label="0">不备份</el-radio>
                        <el-radio :label="1">指定备份</el-radio>
                      </el-radio-group>
                    </FormItem>
                  </el-col>
                  <el-col :span="12">
                  </el-col>
                </el-row>-->
            </Form>
          </data-editor>
          <div style="color: #0c60a5;padding: 5px">
            {{ $t('pages.backUpConfig_text12') }}
          </div>
          <grid-table
            ref="ipPortList"
            :height="200"
            :show-pager="false"
            :multi-select="formable"
            :col-model="itemColModel"
            :row-datas="temp.backUpFilterSets"
            @selectionChangeEnd="ipPortSelectionChange"
          />
        </el-card>
      </template>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
  </div>
</template>

<script>
import { getByName, createData, updateData } from '@/api/dataEncryption/encryption/backUpConfig'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'

export default {
  name: 'BackupConfigDlg',
  components: { FileSuffixLibImport },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        backType: 1,
        isBackUp: 1,
        isRemote: 2,
        freeSpace: 10,
        sysFreeSpace: 5,
        backupRule: 2,
        backupCopies: 3,
        retentionDays: 0,
        // 删除加密备份文件设置配置
        retentionDay: 90,
        fileSize: 1024,
        serverFileSize: 512,
        backUpFilterSets: [],
        entityType: undefined,
        entityId: undefined
      },
      tempItem: {},
      defaultTempItem: { // 表单字段
        id: undefined,
        filePath: '',
        fileExt: '',
        fileSize: 0,
        processName: '',
        ctrlcode: 0
      },
      itemColModel: [
        { prop: 'filePath', label: 'filePath', width: '20', sort: true },
        { prop: 'fileExt', label: 'suffixes', width: '20', sort: true },
        { prop: 'processName', label: 'processName', width: '20', sort: true },
        { prop: 'fileSize', label: 'maxFileSize', width: '30', sort: true }
        /*, { prop: 'ctrlcode', label: '控制类型', width: '20', formatter: this.ctrlcodeFormat }*/
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        freeSpace: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }],
        // backupCopies: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }],
        retentionDays: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }],
        fileSize: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }]
      },
      itemRuls: {
        filePath: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }],
        fileExt: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }],
        fileSize: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }],
        processName: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }]
      },
      activeName: 'backUpLimit',
      itemRowData: [],
      ipPortEditable: false,
      ipPortDeleteable: false,
      popoverVisible1: false,
      popoverVisible2: false
    }
  },
  computed: {

  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createData,
    updateData,
    getByName,
    scroll() {
      if (this.popoverVisible1) {
        this.popoverVisible1 = false
      }
      if (this.popoverVisible2) {
        this.popoverVisible2 = false
      }
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    ipPortTable: function() {
      return this.$refs['ipPortList']
    },
    resetTempItem() {
      this.activeName = 'backUpLimit'
      this.tempItem = Object.assign({}, this.defaultTempItem)
      if (this.$refs['ipPortForm']) this.$refs['ipPortForm'].clearValidate()
    },
    ipPortSelectionChange(rowDatas) {
      this.ipPortDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.ipPortEditable = true
      } else {
        this.ipPortEditable = false
        this.cancelItem()
      }
    },
    createItem() {
      let validate
      this.$refs['ipPortForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempItem)
          let isRepeat = false
          for (let i = 0, size = this.temp.backUpFilterSets.length; i < size; i++) {
            const data = this.temp.backUpFilterSets[i]
            if (rowData.filePath == data.filePath &&
                rowData.fileExt == data.fileExt &&
                rowData.processName == data.processName) {
              isRepeat = true
              break
            }
          }
          if (isRepeat) {
            this.$message({ type: 'error', message: this.$t('pages.backUpConfig_text13') })
          } else {
            rowData.id = new Date().getTime()
            this.temp.backUpFilterSets.unshift(rowData)
            this.cancelItem()
          }
          validate = valid
        }
      })
      return validate
    },
    beforeUpdateItem() {
      this.tempItem = Object.assign({}, this.ipPortTable().getSelectedDatas()[0])
    },
    updateItem() {
      let validate
      this.$refs['ipPortForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempItem)
          let index = 0
          let isRepeat = false

          for (let i = 0, size = this.temp.backUpFilterSets.length; i < size; i++) {
            const data = this.temp.backUpFilterSets[i]
            if (rowData.id == data.id) index = i
            if (rowData.id != data.id &&
                rowData.filePath == data.filePath &&
                rowData.fileExt == data.fileExt &&
                rowData.processName == data.processName) {
              isRepeat = true
              break
            }
          }
          if (isRepeat) {
            this.$message({ type: 'error', message: this.$t('pages.backUpConfig_text13') })
          } else {
            this.temp.backUpFilterSets.splice(index, 1, rowData)
            this.cancelItem()
          }
          validate = valid
        }
      })
      return validate
    },
    deleteItem() {
      const toDeleteIds = this.ipPortTable().getSelectedIds()
      this.ipPortTable().deleteRowData(toDeleteIds, this.temp.backUpFilterSets)
      this.cancelItem()
    },
    cancelItem() {
      // this.ipPortTable().clearSelection()
      this.ipPortTable().setCurrentRow()
      this.resetTempItem()
    },
    handleCreate() {
      this.resetTempItem()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.resetTempItem()
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(rowData) {
      rowData.backUpFilterSets.forEach(function(data, index) {
        data.id = index
      })
      if (!(rowData.backType & 2)) {
        rowData.serverFileSize = rowData.oldServerFileSize || this.defaultTemp.serverFileSize
      }
      this.$set(rowData, 'isBackUp', rowData.backType & 1)
      this.$set(rowData, 'isRemote', rowData.backType & 2)
    },
    formatFormData(formData) {
      if (formData.isBackUp == 0) {
        formData.backType = 0
        formData.backUpFilterSets = []
      } else {
        formData.backType = formData.isBackUp + formData.isRemote
        formData.backUpFilterSets.forEach(function(data, index) {
          delete data.id
        })
      }
      if (!(formData.backType & 2)) {
        formData.serverFileSize = 0
      }
      // 兼容旧策略，新策略之后是默认备份转移的（当磁盘空间不足freeSpace值时，备份到其他盘）,且备份份数为3份
      formData.backupRule = 2
      formData.backupCopies = 3
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      let union_suffix
      if (this.tempItem.fileExt == null || this.tempItem.fileExt === '') {
        union_suffix = [...new Set(suffix.split('|'))]
      } else {
        union_suffix = [...new Set((this.tempItem.fileExt + '|' + suffix).split('|'))]
      }
      this.tempItem.fileExt = union_suffix.join('|')
      this.$refs['ipPortForm'] && this.$refs['ipPortForm'].validateField('fileExt')
    },
    justInputNumber(value, minNumber, maxNumber) {
      // 不会出现负数
      if (Number.isNaN(Number(value))) {
        if (typeof (value) == 'string') value = value.replace(/[^\d]/g, '')
      }
      value = Number(value)
      if (minNumber && maxNumber && minNumber > maxNumber) {
        const temp = minNumber
        minNumber = maxNumber
        maxNumber = temp
      }
      if (minNumber && value < minNumber) {
        value = minNumber
      } else if (maxNumber && value > maxNumber) {
        value = maxNumber
      }
      return value
    }
  }
}
</script>
<style lang="scss" scoped>
.cz-input-number {
  ::v-deep .el-input__inner {
    text-align: left;
  }
}
.free-space >>>.el-form-item__error {
  margin-left: 102px;
}
.file-size >>>.el-form-item__error {
  margin-left: 32px;
}
.del-checkbox {
  display: block;
  margin: 10px;
}
.divider {
  width: calc(100% - 25px);
  margin-left: 25px !important;
}
.box-card {
  margin-left: 25px
}
.input-box {
  width: 80px;
  text-align: center;
}
.del-checkbox-content {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
