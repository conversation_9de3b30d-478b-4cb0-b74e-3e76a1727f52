<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="90px"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :entity-type-span="11"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')" >
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-card>
          <FormItem label-width="0px">
            <el-checkbox v-model="temp.enableDense" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('table.enableDense') }}</el-checkbox>
          </FormItem>
          <FormItem :label="$t('table.denseLevel')" prop="denseLevel">
            <el-select v-model="temp.denseLevel" :disabled="!formable">
              <el-option v-for="(value, key) in denseLevelOptions" v-show="key <= activeDenseLevel" :key="key" :label="value" :value="key"></el-option>
            </el-select>
          </FormItem>
          <FormItem label-width="0px">
            <el-checkbox v-model="temp.changeDense" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('table.changeDense') }}</el-checkbox>
          </FormItem>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importUserDenseStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import {
  getStrategyPage, getStrategyByName, createStrategy, updateStrategy, deleteStrategy
} from '@/api/dataEncryption/encryption/userDense'
import { getSetting } from '@/api/dataEncryption/docPemission/denseSet'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter, isSameTimestamp, initTimestamp } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'UserDense',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 107,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'enableDense', label: 'enableDense', width: '120', formatter: (row) => { return row.enableDense === 1 ? this.$t('text.enable') : this.$t('text.disable') } },
        { prop: 'denseLevel', label: 'denseLevel', width: '100', iconFormatter: this.iconClassFormat, formatter: this.denseLevelFormatter },
        { prop: 'changeDense', label: 'changeDense', width: '100', formatter: row => { return row.changeDense == 1 ? this.$t('text.enable') : this.$t('text.disable') } },
        { prop: 'remark', label: this.$t('text.remark'), width: '120', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      denseLevelOptions: {},
      activeDenseLevel: 4,
      activeDenseIconNum: 0,
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.operatorClassificationSettingsStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.operatorClassificationSettingsStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        denseLevel: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.loadUserDense()
    initTimestamp(this)
  },
  activated() {
    if (!isSameTimestamp(this, 'DenseSet')) {
      this.loadUserDense()
    }
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    loadUserDense() {
      getSetting().then(respond => {
        const configData = respond.data
        if (configData) {
          this.activeDenseLevel = configData.visibleCount
          this.activeDenseIconNum = configData.icoNum
          configData.denseInfoList.forEach(item => (
            this.$set(this.denseLevelOptions, item.encryptLevel, item.denseName)
          ))
        }
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        denseLevel: undefined,
        enableDense: 1,
        changeDense: 1
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.denseLevel += ''
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    denseLevelFormatter: function(row, data) {
      return this.denseLevelOptions[data]
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    iconClassFormat(row) {
      const iconMap = [
        ['lock', 'lock1-1', 'lock1-2', 'lock1-3', 'lock1-4'],
        ['lock', 'lock2-1', 'lock2-2', 'lock2-3', 'lock2-4']
      ]
      return iconMap[this.activeDenseIconNum][row.denseLevel]
    }
  }
}
</script>
