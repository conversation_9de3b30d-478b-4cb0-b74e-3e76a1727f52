<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <el-button type="primary" size="mini" @click="handleCreateAdvanced">
          {{ $t('pages.advancedConfiguration') }}
        </el-button>
        <label v-if="noOutSend" style="color:#fd2222;font-size:13px;;margin-left:80px">{{ $t('pages.emailClosed') }}</label>
      </div>
      <grid-table
        ref="baseList"
        :multi-select="true"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      class="stg-dlg"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="baseTemp"
        label-position="right"
        label-width="80px"
        style="width: 670px;"
      >
        <el-row>
          <el-col :span="2">
            <label>{{ $t('pages.effectiveObject') }}</label>
          </el-col>
          <el-col :span="6">
            <el-select v-model="objectType" :disabled="!formable || dialogStatus == 'update'">
              <el-option :value="2" :label="$t('components.userG')"/>
            </el-select>
          </el-col>
          <el-col :span="16">
            <tree-select
              ref="objectTree"
              node-key="id"
              is-filter
              check-strictly
              multiple
              :local-search="false"
              :leaf-key="'user'"
              :width="400"
              :disabled="!formable || dialogStatus == 'update'"
              :checked-keys="objectCheckedKeys"
              @change="objectIdSelectChange"
            />
          </el-col>
        </el-row>
        <br/>
        <!-- <el-divider content-position="left" class="first-divider">{{ $t('pages.stgInherit') }}</el-divider>
        <el-checkbox v-model="baseTemp.isInherit" style="margin-bottom:10px" :disabled="!formable">{{ $t('pages.approvalProcess_Msg') }}</el-checkbox> -->
        <el-divider content-position="left" class="first-divider">{{ $t('pages.mailOutSendConfig') }}</el-divider>
        <el-checkbox v-model="baseTemp.mailOutSend" style="margin-bottom:10px" :disabled="!formable">{{ $t('pages.mailOutSend') }}
          <el-tooltip effect="dark" content="" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.mailOutSendZ') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-checkbox>
        <br/>
        <el-checkbox v-model="baseTemp.decrypFileLand" class="mailOutSends" :disabled="!formable || !baseTemp.mailOutSend">{{ $t('pages.decrypFileLand') }}</el-checkbox>
        <el-checkbox v-model="baseTemp.successSendMail" class="mailOutSends" :disabled="!formable || !baseTemp.mailOutSend">{{ $t('pages.successSendMail') }}</el-checkbox>
        <!-- <el-checkbox v-model="baseTemp.limitFileSize" class="mailOutSends" :disabled="baseTemp.isInherit || !baseTemp.mailOutSend">{{ $t('pages.limitFileSizeTip') }}</el-checkbox> -->
        <FormItem prop="limitFileSize" class="limitFileSizeCheck" style="margin-left:-80px">
          <el-checkbox v-model="limitFileSizeCheck" style="margin-left:20px" :disabled="!formable || !baseTemp.mailOutSend" @change="limitFileSizeCheckChange">
            <i18n v-if="limitFileSizeCheck" path="pages.limitFileSizeTip">
              <el-input-number slot="limitFileSizeTip" v-model="baseTemp.limitFileSize" :disabled="!formable || !baseTemp.mailOutSend || !limitFileSizeCheck" :controls="false" :min="1" :max="9999" style="width:75px;margin: 5px;" size="mini"></el-input-number>
              <el-select slot="selectType" v-model="baseTemp.limitOrRemind" :disabled="!formable || !baseTemp.mailOutSend || !limitFileSizeCheck" size="mini" style="width: 110px;">
                <el-option :value="true" :label="$t('pages.limitSubmit')"></el-option>
                <el-option :value="false" :label="$t('pages.forbridenSubmit')"></el-option>
              </el-select>
            </i18n>
            <i18n v-else path="pages.limitFileSizeTip">
              <el-input-number slot="limitFileSizeTip" :disabled="true" :controls="false" :min="1" :max="4000" style="width:75px;margin: 5px;"></el-input-number>
              <el-select slot="selectType" v-model="baseTemp.limitOrRemind" :disabled="true" size="mini" style="width: 110px;">
                <el-option :value="true" :label="$t('pages.limitSubmit')"></el-option>
                <el-option :value="false" :label="$t('pages.forbridenSubmit')"></el-option>
              </el-select>
            </i18n>
            <el-tooltip effect="dark" content="" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.overSize') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
        </FormItem>
        <el-checkbox v-model="baseTemp.switchSendMail" class="mailOutSends" :disabled="!formable || !baseTemp.mailOutSend">{{ $t('pages.switchSendMail') }}</el-checkbox>
        <el-divider content-position="left" class="first-divider">{{ $t('pages.allowMailEdit') }}</el-divider>
        <el-checkbox v-model="baseTemp.addMail" style="margin-bottom:10px" :disabled="!formable || !baseTemp.mailOutSend">{{ $t('pages.allowAddMail') }}</el-checkbox>
        <el-checkbox v-model="baseTemp.updMail" style="margin-bottom:10px" :disabled="!formable || !baseTemp.mailOutSend">{{ $t('pages.allowUpdateMail') }}</el-checkbox>
        <el-checkbox v-model="baseTemp.delMail" style="margin-bottom:10px" :disabled="!formable || !baseTemp.mailOutSend">{{ $t('pages.allowDelMail') }}</el-checkbox>
        <el-divider content-position="left" class="first-divider">{{ $t('pages.emailFailSet') }}</el-divider>
        <i18n path="pages.emailFailSetConfig">
          <el-input-number slot="mailErrorNum" v-model="baseTemp.mailErrorNum" :disabled="!formable" :controls="false" :min="1" :max="100" style="width:80px;margin: 5px;"/>
        </i18n>
        <br/>
        <span>{{ $t('pages.mamagerEmail') + '：' }}</span><el-input v-model="baseTemp.warnMailAccounts" :disabled="!formable" style="width: 500px;" maxlength="500"/>
        <br/>
        <span style="margin-left:20px;margin-bottom:10px;color: #0c60a5" class="zhuTip">{{ $t('pages.addEmailTip') }}</span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable && dialogStatus == 'update'" type="primary" :loading="submitting" @click="saveAs">
          {{ $t('components.saveAs') }}
        </el-button>
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.advancedConfiguration')"
      :visible.sync="superDialogFormVisible"
      width="800px"
      class="stg-dlg2"
      @dragDialog="handleDrag"
    >
      <Form
        ref="superDataForm"
        :rules="superRules"
        :model="superTemp"
        label-position="right"
        label-width="100px"
        style="width: 670px;"
      >
        <el-divider content-position="left" class="first-divider">{{ $t('pages.superSysMailConfig') }}</el-divider>
        <el-checkbox v-model="superTemp.ldFiledecryptMailSaveSentmessages" style="margin-bottom:10px" :disabled="false">{{ $t('pages.ldFiledecryptMailSaveSentmessages') }}</el-checkbox>
        <el-divider content-position="left" class="first-divider">{{ $t('pages.superAttachMailDown') }}</el-divider>
        <FormItem prop="ldFiledecryptSendmailFilesize" class="ldFiledecryptSendmailFilesize" style="margin-left:-80px">
          <i18n path="pages.ldFiledecryptSendmailFilesize">
            <el-input-number slot="ldFiledecryptSendmailFilesize" v-model="superTemp.ldFiledecryptSendmailFilesize" :disabled="!formable" :controls="false" :min="1" :max="9999" style="width:80px;margin: 5px;"/>
          </i18n>
        </FormItem>
        <FormItem prop="ldFiledecryptSendmailDownloadValiddays" class="ldFiledecryptSendmailDownloadValiddays" style="margin-left:-80px">
          <i18n path="pages.ldFiledecryptSendmailDownloadValiddays">
            <el-input-number slot="ldFiledecryptSendmailDownloadValiddays" v-model="superTemp.ldFiledecryptSendmailDownloadValiddays" :disabled="!formable" :controls="false" :min="1" :max="9999" style="width:80px;margin: 5px;"/>
          </i18n>
        </FormItem>
        <span>{{ $t('pages.emailDownloadServer') }}</span><el-input v-model="superTemp.ldFiledecryptSendmailInternetlink" :disabled="!formable" style="width: 500px;" maxlength="500"/>
        <br/>
        <span style="margin-left:20px;margin-bottom:10px;color: #0c60a5" class="zhuTip">{{ $t('pages.emailDownloadServerTip') }}</span>
        <br/>
        <span style="margin-left:20px;margin-bottom:10px;color: #0c60a5" class="zhuTip">{{ $t('pages.emailDownloadServerTip2') }}</span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :loading="submitting" type="primary" @click="updateSuperData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="superDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <save-as-dlg ref="saveAsDlg" :format-form-data="formatFormData" :save-data="addBaseInfo" @submitEnd="saveAsSubmit"/>
  </div>
</template>

<script>
import { getPage, addBaseInfo, updateBaseInfo, getSuperConfig, updateSuperConfig, deleteBaseInfo } from '@/api/dataEncryption/encryption/emailOutSend'
import { enableStgBtn, enableStgDelete, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import SaveAsDlg from '@/views/system/configManage/approveOutgoingEmailSetting/dlg/saveAsDlg';
import { getApprovalConfig } from '@/api/system/configManage/approvalConfig.js'
export default {
  name: 'EmailOutSend',
  components: { SaveAsDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      colModel: [
        // { prop: 'entityName', label: 'source', type: 'button', width: '100',
        //   buttons: [
        //     { formatter: this.entityFormatter, click: this.entityClick }
        //   ]
        // },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'mailOutSend', label: 'mailOutSendLabel', width: '100', formatter: this.mailOutSendFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'details', isShow: (data) => { return this.query.objectType }, click: this.handleShow },
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      deletedAble: false,
      formable: true,
      submitting: false,
      baseTemp: {},
      objectType: 2,           // 应用对象类型
      objectIds: null,            // 应用对象ID，包括终端ID或操作员ID
      objectGroupIds: null,       // 应用对象ID，包括终端分组ID或操作员分组ID
      defaultBaseTemp: {
        isInherit: true,
        entityId: undefined,
        objectType: 2,           // 应用对象类型
        objectIds: null,            // 应用对象ID，包括终端ID或操作员ID
        objectGroupIds: null,       // 应用对象ID，包括终端分组ID或操作员分组ID
        userId: '',
        deptId: '',
        // 是否通过邮件外发
        mailOutSend: false,
        // 解密后是否可以下载
        decrypFileLand: false,
        // 成功是否发送邮件，通知申请人
        successSendMail: false,
        // 文件超过多少M，是限制申请还是提醒 true:是提醒申请人  false是禁止申请
        limitOrRemind: false,
        // 限制文件大小 单位M 0代表不限制
        limitFileSize: 200,
        // 申请人是否可以切换发送邮件 默认false
        switchSendMail: false,
        // 申请人是否能新增邮箱信息 默认false
        addMail: false,
        // 申请人是否能修改邮箱信息 默认false
        updMail: false,
        // 申请人是否能删除邮箱信息 默认false
        delMail: false,
        // 发送错误次数 默认5
        mailErrorNum: 5,
        // 告警的邮箱 多个以分号(;)隔开
        warnMailAccounts: ''
      },
      superTemp: {},
      defaultSuperTemp: {
        // 申请解密文件通过邮件自动外发后，邮件不保存到发件箱
        ldFiledecryptMailSaveSentmessages: false,
        // 邮件附件大小超过{ldFiledecryptSendmailFilesize}MB时,将以超链接形式支持收件人进行下载
        ldFiledecryptSendmailFilesize: 20,
        // 邮件附件在发送成功后，{}天内可支持下载
        ldFiledecryptSendmailDownloadValiddays: 30,
        ldFiledecryptSendmailInternetlink: ''
      },
      limitFileSizeCheck: false,
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        userId: '',
        deptId: '',
        sortName: 'createTime'
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {},
      dialogFormVisible: false,
      superDialogFormVisible: false,
      textMap: {
        update: this.$t('pages.updateConfig'),
        create: this.$t('pages.addConfig'),
        detail: this.$t('pages.configurationDetails')
      },
      dialogStatus: '',
      notNewAble: false, // 新增按钮是否可用
      objectCheckedKeys: [],  //  选中的生效对象
      noOutSend: false, // 是否关闭了邮件外发
      rules: {
        limitFileSize: [
          { validator: this.limitFileSizeValidator, trigger: 'blur' }
        ]
      },
      superRules: {
        ldFiledecryptSendmailFilesize: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ],
        ldFiledecryptSendmailDownloadValiddays: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['baseList']
    }
  },
  watch: {
    'baseTemp.mailOutSend'(value) {
      if (!value) {
        this.baseTemp.decrypFileLand = false
        this.baseTemp.successSendMail = false
        this.baseTemp.switchSendMail = false
        this.limitFileSizeCheck = false
        this.baseTemp.limitOrRemind = false
        this.baseTemp.addMail = false
        this.baseTemp.updMail = false
        this.baseTemp.delMail = false
      }
    }
  },
  created() {
    // hiddenActiveAndEntity(this.colModel, this.treeable)
    getApprovalConfig().then(res => {
      if (res.data.ldFiledecryptSendmail == true) {
        this.noOutSend = true
      } else {
        this.noOutSend = false
      }
    })
  },
  methods: {
    limitFileSizeValidator(rule, value, callback) {
      if (this.limitFileSizeCheck) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    addBaseInfo,
    afterLoad(rowData, grid) {
      if (rowData.length > 0) {
        if (!rowData[0].isInherit) {
          this.notNewAble = true
        } else {
          this.notNewAble = false
        }
      } else {
        this.notNewAble = false
      }
      enableStgBtn(rowData, this)
    },
    selectable(row, index) {
      return (this.query.objectType == 2 && this.query.objectId === row.userId) || (this.query.objectType == 4 && this.query.objectId === row.deptId)
    },
    rowDataApi: function(option) {
      option.sortName = 'createTime'
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
        if (checkedNode.type == 4) {
          this.query.deptId = checkedNode.dataId
          this.query.userId = ''
        } else if (checkedNode.type == 2) {
          this.query.userId = checkedNode.dataId
          this.query.deptId = ''
        }
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.query.userId = ''
        this.query.deptId = ''
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.formable = true
      this.dialogFormVisible = true
      this.dialogStatus = 'create'
      this.baseTemp = Object.assign({}, this.defaultBaseTemp)
      this.baseTemp.objectIds = []
      this.baseTemp.objectGroupIds = []
      this.objectCheckedKeys = []
      if (this.checkedEntityNode.id) {
        this.objectCheckedKeys.splice(0, this.objectCheckedKeys.length, this.checkedEntityNode.id)
        if (this.checkedEntityNode.dataType === 'G') {
          this.baseTemp.objectGroupIds.push(this.checkedEntityNode.dataId)
        } else if ((this.checkedEntityNode.id && this.checkedEntityNode.id.substr(0, 1) === 'U')) {
          this.baseTemp.objectIds.push(this.checkedEntityNode.dataId)
        }
      }
      this.limitFileSizeCheck = false
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCreateAdvanced() {
      this.formable = true
      this.superDialogFormVisible = true
      this.superTemp = Object.assign({}, this.defaultSuperTemp)
      getSuperConfig().then(respond => {
        this.superTemp = respond.data
      })
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.formable = true
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.objectCheckedKeys = []
      this.baseTemp = Object.assign({}, row)
      this.baseTemp.objectIds = this.baseTemp.userId ? (this.baseTemp.userId + '').split(',') : []
      this.baseTemp.objectGroupIds = this.baseTemp.deptId !== undefined && this.baseTemp.deptId !== null ? (this.baseTemp.deptId + '').split(',') : []
      //  设置
      this.baseTemp.objectGroupIds && this.baseTemp.objectGroupIds.forEach(id => {
        this.objectCheckedKeys.push('G' + id)
      })
      this.baseTemp.objectIds && this.baseTemp.objectIds.forEach(id => {
        this.objectCheckedKeys.push('U' + id)
      })
      if (row.limitFileSize > 0) {
        this.limitFileSizeCheck = true
      } else {
        this.limitFileSizeCheck = false
      }
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleShow: function(row) {
      this.formable = false
      this.dialogStatus = 'detail'
      this.dialogFormVisible = true
      this.objectCheckedKeys = []
      this.baseTemp = Object.assign({}, row)
      this.baseTemp.objectIds = this.baseTemp.userId ? (this.baseTemp.userId + '').split(',') : []
      this.baseTemp.objectGroupIds = this.baseTemp.deptId !== undefined && this.baseTemp.deptId !== null ? (this.baseTemp.deptId + '').split(',') : []
      //  设置
      this.baseTemp.objectGroupIds && this.baseTemp.objectGroupIds.forEach(id => {
        this.objectCheckedKeys.push('G' + id)
      })
      this.baseTemp.objectIds && this.baseTemp.objectIds.forEach(id => {
        this.objectCheckedKeys.push('U' + id)
      })
      if (row.limitFileSize > 0) {
        this.limitFileSizeCheck = true
      } else {
        this.limitFileSizeCheck = false
      }
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleDrag() {

    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validData()) {
          const rowdata = this.formatFormData(this.baseTemp)
          addBaseInfo(rowdata).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
      this.submitting = false
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validData()) {
          const rowdata = this.formatFormData(this.baseTemp)
          updateBaseInfo(rowdata).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.editSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
      this.submitting = false
    },
    updateSuperData() {
      this.submitting = true
      this.$refs['superDataForm'].validate((valid) => {
        if (valid) {
          updateSuperConfig(this.superTemp).then(() => {
            this.submitting = false
            this.superDialogFormVisible = false
            this.dialogStatus = ''
            // this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.editSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    formatFormData(temp) {
      if (!this.limitFileSizeCheck) {
        this.$set(temp, 'limitFileSize', 0)
      }
      const rowData = JSON.parse(JSON.stringify(temp));
      rowData.userId = temp.objectIds.join(',')
      rowData.deptId = temp.objectGroupIds.join(',')
      return rowData
    },
    mailOutSendFormatter(row, data) {
      if (data) {
        return this.$t('text.yes')
      } else {
        return this.$t('text.no')
      }
    },
    /**
     * 另存为
     */
    saveAs() {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return
        }
        const rowTemp = JSON.parse(JSON.stringify(this.baseTemp));
        rowTemp.objectIds.splice(0)
        rowTemp.objectGroupIds.splice(0)
        rowTemp.userId = ''
        rowTemp.deptId = ''
        this.$refs.saveAsDlg.show(rowTemp)
      })
    },
    /**
     * 另存为成功回调方法
     * @param data
     */
    saveAsSubmit(data) {
      this.dialogFormVisible = false
    },
    limitFileSizeCheckChange(val) {
      if (val == false) {
        this.$refs['dataForm'].clearValidate('limitFileSize')
      }
    },
    /**
     * 操作员选中事件
     * @param datas
     * @param node
     * @param vm
     */
    objectIdSelectChange(datas, nodes, vm) {
      this.baseTemp.objectIds = []
      this.baseTemp.objectGroupIds = []
      nodes && Array.isArray(nodes) && nodes.forEach(node => {
        if (node.dataType === 'G') {
          this.baseTemp.objectGroupIds.push(node.dataId);
        } else if (node.id && node.id.substr(0, 1) === 'U') {
          this.baseTemp.objectIds.push(node.dataId);
        }
      })
    },
    /**
     * 校验数据是否符合要求
     */
    validData() {
      let result = true;
      if (!this.baseTemp.objectIds.length && !this.baseTemp.objectGroupIds.length) {
        this.$message({
          message: this.$t('pages.effectObjectIsNotNull') + '',
          type: 'error',
          duration: 2000
        })
        result = false;
      }
      return result;
    },
    /**
     * 删除配置信息
     */
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteDatas = this.gridTable.getSelectedDatas() || []
        if (toDeleteDatas.length > 1) {
          this.$message({
            message: this.$t('pages.approvalConfigException14'),
            type: 'error',
            duration: 2000
          })
          return
        }
        const deptIds = []
        const userIds = []
        toDeleteDatas.forEach(item => {
          item.deptId && deptIds.push(item.deptId)
          item.userId && userIds.push(item.userId)
        })
        const deleteInfo = {}
        if (deptIds.length > 0) {
          deleteInfo.deptId = deptIds[0]
        } else {
          deleteInfo.userId = userIds[0]
        }
        deleteBaseInfo(deleteInfo).then(respond => {
          this.handleFilter()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
  .stg-dlg {
    >>>.first-divider {
      margin-top: 10px;
    }
  }
  .mailOutSends {
    margin-bottom:10px;
    margin-left: 20px;
  }
  .limitFileSizeCheck {
    >>>.el-form-item__error {
      margin-left: 165px;
      padding-top: 0px;
    }
  }
  .ldFiledecryptSendmailFilesize {
    >>>.el-form-item__error {
      margin-left: 134px;
      padding-top: 0px;
    }
  }
  .ldFiledecryptSendmailDownloadValiddays {
    >>>.el-form-item__error {
      margin-left: 163px;
      padding-top: 0px;
    }
  }
  
</style>
