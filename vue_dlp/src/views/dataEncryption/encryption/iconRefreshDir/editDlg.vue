<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :stg-code="214"
      :time-able="false"
      :title="$t('pages.iconRefreshDir_title')"
      :title-tip="$t('pages.iconRefreshDir_titleTip')"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="listByName"
      :format-row-data="formatRowData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">

        <!--        <el-tooltip effect="dark" placement="bottom-start">-->
        <!--          <div slot="content">解决win10部分加密图标无法及时刷新</div>-->
        <!--          <i class="el-icon-question" />-->
        <!--        </el-tooltip>-->
        <data-editor
          :formable="formable"
          :popover-width="750"
          :updateable="processTableSelectionData.length === 1"
          :deletable="processTableSelectionData.length > 0"
          :add-func="addProcess"
          :update-func="updateProcess"
          :delete-func="deleteProcess"
          :cancel-func="cancelProcess"
          :before-update="beforeUpdateProcess"
          :before-add="beforeAddProcess"
        >
          <Form ref="processForm" :model="processTemp" :rules="processTempRules" label-width="90px" style="margin-bottom: 30px;">
            <FormItem v-if="operationType === 'update'" :label="$t('pages.processName1')" prop="processName">
              <el-upload name="processFile" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getFileName" style="display: inline-block;">
                <el-button type="primary" icon="el-icon-upload" style="padding: 7px 13px;"></el-button>
              </el-upload>
              <el-button type="primary" size="mini" @click="showUpdateAppSelectDlg">
                {{ $t('pages.iconRefreshDir_appLibImport') }}
              </el-button>
              <el-input v-model="processTemp.processName" style="margin-top: 5px;" class="input-with-button"></el-input>
            </FormItem>

            <FormItem v-if="operationType === 'create'" prop="processNames" :label="$t('pages.processName1')">
              <el-upload name="processFile" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getFileName" style="display: inline-block;">
                <el-button type="primary" icon="el-icon-upload" :disabled="!formable" size="mini"></el-button>
              </el-upload>
              <el-button type="primary" size="mini" @click="showAppSelectDlg">
                {{ $t('pages.iconRefreshDir_appLibImport') }}
              </el-button>
              <el-button size="mini" style="margin-left: 0;" @click="handleClear">
                {{ $t('button.clear') }}
              </el-button>
              <tag
                v-model="processTemp.processNames"
                :border="true"
                :placeholder="$t('pages.process_Msg')"
                :input-width="200"
                :overflow-able="true"
                max-height="150px"
                :list="processTemp.processNames"
                :disabled="!formable"
                style="width: calc(100% - 46px); margin-top: 5px;"
                @tagChange="tagChange"
              />
            </FormItem>

            <FormItem :label="$t('pages.catalogue')" :tooltip-content="$t('pages.iconRefreshDir_text1')" prop="dirPathArr" :disabled="!formable" tooltip-placement="bottom-start">
              <el-select
                v-model="processTemp.dirPathArr"
                multiple
                filterable
                allow-create
                size="small"
                :placeholder="this.$t('pages.iconRefreshDir_text2')"
                default-first-option
                :disabled="!formable"
                style="width: calc(100% - 170px);"
                @change="dirPathArrChange"
              >
                <el-option v-for="item in dirPathOptions" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
              <el-checkbox v-model="processTemp.controlCode" :label="$t('pages.iconRefreshDir_includeDir')" true-label="0" false-label="1" :disabled="!formable"></el-checkbox>
            </FormItem>

            <FormItem :label="$t('pages.suffixes')" prop="fileExt">
              <el-input v-model="processTemp.fileExt" v-trim type="textarea" rows="2" resize="none" class="input-with-button" :maxlength="suffixMaxLength" show-word-limit :placeholder="$t('pages.processStgMsg3')"></el-input>
              <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
                <el-button type="primary" size="mini" @click="handleFileSuffixImport()">
                  <svg-icon icon-class="import" />
                </el-button>
              </el-tooltip>
            </FormItem>
            <FormItem>
              <span style="color: #ff0000;"> {{ $t('pages.docTrackServer_text3') }} </span>
            </FormItem>
          </Form>
        </data-editor>

        <FormItem label-width="0">
          <div style="height: 250px;" >
            <grid-table
              ref="customTable"
              :height="250"
              :row-datas="temp.iconList"
              :col-model="colModel"
              :show-pager="false"
              @selectionChangeEnd="processTableSelectionChangeEnd"
              @currentChange="processCurrentChangeEnd"
            />
          </div>
        </FormItem>

      </template>

    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
    <app-select-dlg ref="processLib" :append-to-body="true" @select="importProcess"/>
    <app-select-dlg ref="updateProcessLib" :multiple="false" :append-to-body="true" @select="updateImportProcess"/>
  </div>
</template>

<script>
import { listByName, createStrategy, updateStrategy } from '@/api/dataEncryption/encryption/iconRefreshDir'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg';

export default {
  name: 'IconRefreshDirDlg',
  components: { FileSuffixLibImport, AppSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      temp: {}, // 接收并显示数据
      defaultTemp: {
        id: undefined,
        timeId: 1,
        name: '',
        remark: '',
        active: false,
        objectType: '',
        objectId: undefined,
        iconList: []
      }, // 修改后的数据向 stg-dialog 组件传值
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateStgName'), trigger: 'blur' }
        ]
      },
      submitting: false,
      slotName: undefined,
      defaultProcessTemp: {
        id: undefined,
        processName: '',
        dirPath: '',
        dirPathArr: [],
        fileExt: '',
        controlCode: 1,
        processNames: []
      },
      processTemp: {
        id: undefined,
        processName: '',
        dirPath: '',
        dirPathArr: [],
        fileExt: '',
        controlCode: 1,
        processNames: []
      },
      processTempRules: {
        processName: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        dirPathArr: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        fileExt: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        processNames: [
          { required: true, validator: this.processNamesValidator, trigger: 'change' }
        ]
      },
      dirPathOptions: [{
        value: '$DESKTOP$',
        label: this.$t('pages.matchDesktop')
      }, {
        value: '$VOLUMEROOT$',
        label: this.$t('pages.matchRootDir')
      }],
      processTableSelectionData: [],
      controlCodeMap: {
        1: this.$t('text.yes'),
        0: this.$t('text.no')
      },
      colModel: [
        { prop: 'processName', label: 'processName1', width: '150', sort: true },
        { prop: 'fileExt', label: 'fileSuffix', width: '150', sort: true },
        { prop: 'dirPathArr', label: 'catalogue', width: '150', sort: true },
        { label: 'includeDir', fixedWidth: '150', iconFormatter: this.stgIncludeDirFormatter }
      ],
      suffixMaxLength: 100,

      operationType: ''
    }
  },
  computed: {
    customTable() {
      return this.$refs['customTable']
    }
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
    // this.temp = Object.assign({}, this.defaultTemp)
  },
  methods: {
    createStrategy,
    updateStrategy,
    listByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      console.log('slotChange', slotTemp)
      this.temp = !slotTemp ? {} : slotTemp
      // this.temp.iconList.forEach((data, index) => {
      //   console.log('iconList', data)
      //   const res = Object.assign(data, { id: index })
      //   this.temp.iconList.splice(index, 1, res)
      // })
    },
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.resetProcessTemp()
        this.$refs.processForm && this.$refs.processForm.clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.resetProcessTemp()
        this.$refs.processForm && this.$refs.processForm.clearValidate()
      })
    },
    formatRowData(row) {
    },
    submitEnd(dlgStatus) {
      this.resetProcessTemp()
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    dataRepeatValidator(list, temp) {
      for (let i = 0; i < list.length; i++) {
        const { id, processName, fileExt, controlCode } = list[i]
        if (processName === temp.processName &&
          fileExt === temp.fileExt &&
          controlCode === temp.controlCode && id !== temp.id
        ) {
          return true
        }
      }
      return false
    },
    resetProcessTemp() {
      this.processTemp = Object.assign({}, this.defaultProcessTemp);
      this.processTemp.processNames = []
    },
    controlCodeFormatter: function(row, data) {
      return row.controlCode !== 0 ? { class: 'active', title: this.$t('pages.online') } : { class: 'offline', title: this.$t('pages.offline'), style: 'color: #888;' }
      // return this.controlCodeMap[data]
    },
    processTableSelectionChangeEnd(val) {
      this.processTableSelectionData = val
    },
    processCurrentChangeEnd: function(row) {
      if (row !== null) {
        this.processTemp = Object.assign({}, row)
      }
    },
    getFileName(file) {
      if (this.operationType === 'create') {
        const list = [...this.processTemp.processNames]
        list.push(file.name)
        this.processTemp.processNames = this.verifyExeNames(list)
        this.$refs['processForm'].validateField('processNames')
      } else if (this.operationType === 'update') {
        this.processTemp.processName = file.name
        this.$refs['processForm'].validateField('processName')
      }
      return false // 屏蔽了action的默认上传
    },
    addProcess() {
      let validate
      this.processTemp = Object.assign({}, this.processTemp, {
        dirPath: this.processTemp.dirPathArr[0]
      })
      // if (this.dataRepeatValidator()) {
      //   this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.dataDuplication'), type: 'error', duration: 2000 })
      //   return
      // }
      this.$refs['processForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.processTemp)
          rowData.processNames = this.filterExitsData(this.temp.iconList, rowData)
          for (let i = 0, len = rowData.processNames.length; i < len; i++) {
            const data = JSON.parse(JSON.stringify(rowData))
            data.processName = rowData.processNames[i]
            data.processNames = undefined
            this.temp.iconList.unshift(Object.assign(data, { id: new Date().getTime() + i }))
          }

          this.resetProcessTemp()
          validate = valid
        }
      })
      return validate
    },
    stgIncludeDirFormatter(row) {
      return row.controlCode === '0' ? [{ class: 'active' }] : [{ class: 'offline', style: 'color: #888;' }]
    },
    cancelProcess: function() {
      this.$refs['customTable'].setCurrentRow()
      this.$refs['processForm'].clearValidate()
      this.resetProcessTemp()
    },
    beforeUpdateProcess: function() {
      this.operationType = 'update'
      this.processTemp = Object.assign({}, this.$refs['customTable'].getSelectedDatas()[0])
    },
    beforeAddProcess() {
      this.operationType = 'create'
    },
    updateProcess() {
      let validate
      const len = this.temp.iconList.length;
      for (let i = 0; i < len; i++) {
        if (this.temp.iconList[i].id === this.processTemp.id) {
          this.processTemp = Object.assign({}, this.processTemp, {
            dirPath: this.processTemp.dirPathArr[0]
          })
          if (this.dataRepeatValidator(this.temp.iconList, this.processTemp)) {
            this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.dataDuplication'), type: 'error', duration: 2000 })
            return
          }
          this.$refs['processForm'].validate((valid) => {
            if (valid) {
              this.temp.iconList.splice(i, 1, this.processTemp)
              this.resetProcessTemp()
              validate = valid
            }
          })
        }
      }
      return validate
    },
    deleteProcess() {
      if (this.processTableSelectionData.length > 0) {
        this.processTableSelectionData.forEach(data => {
          const index = this.temp.iconList.findIndex(c => c.id === data.id)
          this.temp.iconList.splice(index, 1)
        })
        this.processTableSelectionData.splice(0) // 删除customTable列表勾选
      }
    },
    dirPathArrChange(val) {
      this.$refs['processForm'].validateField('dirPathArr')
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      let union_suffix
      if (this.processTemp.fileExt === '') {
        union_suffix = [...new Set(suffix.split('|'))].join('|')
      } else {
        union_suffix = [...new Set((this.processTemp.fileExt + '|' + suffix).split('|'))].join('|')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      this.processTemp.fileExt = union_suffix
      this.$refs['processForm'].validateField('fileExt')
    },

    //  当列表发生改变时，校验名称是否符合规则
    tagChange(names) {
      names = this.verifyExeNames(names)
      this.processTemp.processNames = names;
      this.$refs['processForm'].validateField('processNames')
    },
    //  校验进程名集合
    verifyExeNames(names) {
      names = this.filterRepetitionData(names);
      return names;
    },
    //  过滤掉列表中已存在的配置信息
    filterExitsData(iconLists, processTemp) {
      //  已存在的配置信息
      const iconList = JSON.parse(JSON.stringify(iconLists));
      //  需要添加的进程配置信息
      processTemp = JSON.parse(JSON.stringify(processTemp));
      if (iconList.length === 0) {
        return processTemp.processNames || []
      }

      const length = processTemp.processNames.length;
      let processNames = processTemp.processNames || []
      processTemp.processNames = undefined
      processTemp.id = undefined
      processNames = processNames.filter(processName => {
        let isAdd = true
        processTemp.processName = processName
        for (let i = 0; i < iconList.length; i++) {
          const processRow = iconList[i]
          processRow.id = undefined
          processRow.processNames = undefined
          if (JSON.stringify(processTemp) === JSON.stringify(processRow)) {
            isAdd = false
            break;
          }
        }
        return isAdd;
      });

      if (length > processNames.length) {
        this.$message({
          message: this.$t('pages.iconRefreshDir_dataExisted'),
          type: 'warning',
          duration: 3000
        })
      }

      return processNames;
    },
    //  过滤重复数据
    filterRepetitionData(list) {
      const tList = []
      list.forEach(item => {
        if (!tList.includes(item)) {
          tList.push(item)
        }
      })
      return tList;
    },
    showAppSelectDlg() {
      this.$refs['processLib'].show()
    },
    showUpdateAppSelectDlg() {
      this.$refs['updateProcessLib'].show()
    },
    processNamesValidator(rule, value, callback) {
      if (this.processTemp.processNames.length === 0) {
        callback(new Error(this.$t('pages.iconRefreshDir_processNameNotNull')))
      } else {
        callback()
      }
    },
    importProcess(processes) {
      if (this.processTemp.processNames === undefined || this.processTemp.processNames === null) {
        this.$set(this.processTemp, 'processNames', [])
      }
      let processNames = [...this.processTemp.processNames];
      (processes || []).forEach(item => {
        processNames.push(item.processName)
      })
      processNames = this.verifyExeNames(processNames)
      this.processTemp.processNames = processNames
      this.$refs['processForm'].validateField('processNames')
    },
    updateImportProcess(process) {
      if (process) {
        this.processTemp.processName = process.processName
      }
    },
    handleClear() {
      this.processTemp.processNames.splice(0)
      this.$refs['processForm'].validateField('processNames')
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.noBold label{
    font-weight: 400;
  }
</style>
