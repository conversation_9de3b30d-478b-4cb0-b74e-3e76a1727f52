<template>
  <stg-dialog
    ref="stgDlg"
    :title="$t('pages.specialPathStg')"
    :stg-code="61"
    :active-able="activeAble"
    :rules="rules"
    :model="defaultTemp"
    :entity-node="entityNode"
    :create="createStrategy"
    :update="updateStrategy"
    :get-by-name="getStrategyByName"
    :format-row-data="formatRowData"
    :format-form-data="formatFormData"
    @submitEnd="submitEnd"
    @slotChange="slotChange"
  >
    <template :slot="slotName">
      <el-card class="box-card" :body-style="{'padding': '10px'}">
        <div slot="header">
          <span>{{ $t('pages.specialPath_text1') }}</span>
        </div>
        <tag :list="temp.path" :disabled="!formable"/>
      </el-card>
    </template>
  </stg-dialog>
</template>

<script>
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/dataEncryption/encryption/specialPath'

export default {
  name: 'SpecialPathDlg',
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        path: [],
        entityType: undefined,
        entityId: undefined
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      }
    }
  },
  computed: {

  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(rowData) {

    },
    formatFormData(formData) {

    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    }
  }
}
</script>
