<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ i18nConcatText(this.$t('pages.chargePlug_Msg2'), 'create') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ i18nConcatText(this.$t('pages.chargePlug_Msg2'), 'delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateMsg_enterName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="authInfoList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="650px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 550px; margin-left: 30px;"
      >
        <FormItem :label="$t('table.name')" prop="name">
          <el-input v-model="temp.name" maxlength="60"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.permanentEffect')">
          <el-switch v-model="temp.permanent" :active-value="1" :inactive-value="0" @change="permanentChange"/>
        </FormItem>
        <FormItem v-show="!temp.permanent" :label="$t('text.effectTime')">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            :picker-options="pickerOptions"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :range-separator="$t('pages.till')"
            :start-placeholder="$t('pages.startDate')"
            :end-placeholder="$t('pages.endDate')"
            style="width: 100%"
          >
          </el-date-picker>
        </FormItem>
        <FormItem :label="$t('pages.interface_auth_msg19')">
          <el-radio-group v-model="temp.authType">
            <el-radio v-show="isShowEncryAuthorize" :label="0">{{ this.$t('pages.interface_auth_type1') }}</el-radio>
            <el-radio v-show="isShowEncrySvrAuthorize" :label="1">{{ this.$t('pages.interface_auth_type2') }}</el-radio>
            <el-radio v-show="isShowEncryAnyDataAuthorize" :label="2">{{ this.$t('pages.interface_auth_type3') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.interface_auth_msg12')">
          <el-radio-group v-model="temp.decryptVersion">
            <el-radio :label="8">{{ this.$t('pages.interface_auth_msg9') }}</el-radio>
            <el-radio :label="9" :disabled="closeEncVer4 == 1">
              {{ this.$t('pages.interface_auth_msg11') }}
              <el-tooltip v-if="closeEncVer4 == 1" class="item" effect="dark" placement="right">
                <i class="el-icon-info" />
                <div slot="content">{{ this.$t('pages.interface_auth_msg17') }}</div>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-show="temp.decryptVersion == 9" :label="$t('table.fileEncryptLevel')" prop="encryptLevel">
          <el-select v-model="temp.encryptLevel">
            <el-option v-for="(val, key) in denseLevelOptions" :key="Number(key)" :label="val" :value="Number(key)"></el-option>
          </el-select>
        </FormItem>
        <FormItem v-show="temp.decryptVersion == 9" :label="$t('pages.readPermission')" prop="groupId">
          <tree-select :data="groupTreeSelectData" node-key="dataId" :checked-keys="checkedKeys" is-filter :width="296" @change="parentIdObjChange" />
        </FormItem>
        <el-card class="box-card" :body-style="{'padding': '10px'}" style="height: calc(100% - 100px); border: 1px solid #878787;" >
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.machineCodeListMsg') }}</span>
          </div>
          <div class="toolbar">
            <el-button type="primary" size="mini" @click="handleMcodeCreate">
              {{ $t('button.insert') }}
            </el-button>
            <el-button type="primary" size="mini" :disabled="!deleteable1" @click="handleMcodeDelete">
              {{ $t('button.delete') }}
            </el-button>
          </div>
          <div>
            <grid-table
              ref="mcodeGrid"
              :show-pager="false"
              :height="200"
              :col-model="colModel1"
              :row-datas="temp.mcodes"
              :selectable="selectable"
              :after-load="afterLoad"
              @selectionChangeEnd="mcodesSelectionChangeEnd"
            />
          </div>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData(true):updateData(true)">{{ $t('button.saveAndDownloadAuthFile') }}</el-button>
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.save') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus1]"
      :visible.sync="dialogMcodeFormVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="mcodeForm"
        :rules="mcodeRules"
        :model="tempM"
        label-position="right"
        label-width="70px"
        style="width: 400px; margin-left: 25px;"
      >
        <FormItem :label="$t('table.machineNumber')" prop="mcode">
          <el-input v-model="tempM.mcode" type="text" maxlength="32"></el-input>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="tempM.remark" type="textarea" rows="2" resize="none" maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMcodeEnd()">{{ $t('button.save') }}</el-button>
        <el-button @click="dialogMcodeFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getInterfaceAuthInfoList,
  createInterfaceAuthInfo,
  updateInterfaceAuthInfo,
  deleteInterfaceAuthInfo,
  getByName,
  startDownload,
  downloadFile
} from '@/api/dataEncryption/encryption/interfaceAuth'
import { getSetting } from '@/api/dataEncryption/docPemission/denseSet'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { getConfigByKey } from '@/api/system/configManage/globalConfig'
import { enableStgBtn, selectable } from '@/utils'
import { downloadTool } from '@/api/dataEncryption/encryption/fileOutgoing'
import moment from 'moment'
import { buildDownloadFileByName } from '@/utils/download/helper'
import { isShowEncryAuthorize, isShowEncrySvrAuthorize, isShowEncryAnyDataAuthorize } from '@/api/grantAuth'

export default {
  name: 'InterfaceAuth',
  data() {
    return {
      multiSelect: true,
      colModel: [
        { prop: 'name', label: 'name', width: '150', fixed: true },
        { prop: '', label: 'effectiveDate', width: '150', formatter: this.effectiveDateFormatter },
        { label: 'operate', width: '150', type: 'button', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'authFileDownload', click: this.downloadClick }
          ]
        }
      ],
      colModel1: [
        { prop: 'mcode', label: 'machineNumber', width: '150', fixed: true },
        { prop: 'remark', label: 'remark', width: '150', fixed: true },
        { label: 'operate', width: '70', type: 'button', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleMcodeUpdate }
          ]
        }
      ],
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: this.$t('pages.date_Validate1'), trigger: 'blur' },
          { validator: this.timeValidator, trigger: 'blur' }
        ],
        endDate: [
          { required: true, message: this.$t('pages.date_Validate2'), trigger: 'blur' },
          { validator: this.timeValidator, trigger: 'blur' }
        ]
      },
      mcodeRules: {
        mcode: [
          { required: true, message: this.$t('pages.interface_auth_msg5'), trigger: 'blur' },
          { validator: this.mcodeValidator, trigger: 'blur' }
        ]
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        permanent: 0,
        // version: 2, // 版本： 1 二代加密 2 三代加密 3 四代加密
        // type: 1, // 解密类型: 0 普通加密 1 三代加密 2 四代加密
        decryptVersion: 8, // 加密版本：0 普通加密 8 三代加密 9 四代加密
        groupId: undefined, // 阅读权限：部门
        encryptLevel: 0, // 文件密级
        authType: 0, // 授权类型：0 文件版本 1 服务版本 2 爱数版本
        startDate: undefined,
        endDate: undefined,
        effectiveTime: '', // 生效时间，记录管理员日志时使用
        mcodes: [] // 机器码列表
      },
      tempM: {},
      defaultTempM: { // 表单字段
        id: undefined,
        mcode: '',
        remark: ''
      },
      query: { // 查询条件
        page: 1,
        searchInfo: undefined
      },
      dialogFormVisible: false,
      dialogMcodeFormVisible: false,
      submitting: false,
      dialogStatus: 'create',
      dialogStatus1: 'createM',
      textMap: {
        create: this.$t('pages.interface_auth_msg1'),
        update: this.$t('pages.interface_auth_msg2'),
        createM: this.$t('pages.interface_auth_msg3'),
        updateM: this.$t('pages.interface_auth_msg4')
      },
      deleteable: false,
      deleteable1: false,
      loading: false,
      usbDiskStatusOptions: {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      },
      dateRange: [],
      pickerOptions: {
        disabledDate: time => {
          return moment(time).isAfter(moment('2188-01-01')) || moment(moment(new Date()).add(-1, 'days')).isAfter(moment(time))
        }
      },
      timer: undefined,
      closeEncVer4: 0,
      denseLevelOptions: {},
      groupTreeSelectData: [],
      checkedKeys: [],
      isShowEncryAuthorize: false,
      isShowEncrySvrAuthorize: false,
      isShowEncryAnyDataAuthorize: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['authInfoList']
    }
  },
  watch: {
    '$store.getters.deptTree'() {
      this.initGroupTreeNode()
    }
  },
  created() {
    isShowEncryAuthorize().then(res => {
      this.isShowEncryAuthorize = res.data
    })
    isShowEncrySvrAuthorize().then(res => {
      this.isShowEncrySvrAuthorize = res.data
    })
    isShowEncryAnyDataAuthorize().then(res => {
      this.isShowEncryAnyDataAuthorize = res.data
    })
    this.getConfig()
    this.initGroupTreeNode()
    this.loadDense()
  },
  activated() {
    this.getConfig()
    this.initGroupTreeNode()
    this.loadDense()
  },
  destroyed() {
  },
  methods: {
    initGroupTreeNode: function() {
      getDeptTreeFromCache().then(respond => {
        this.groupTreeSelectData = respond.data
      })
    },
    loadDense() {
      getSetting().then(respond => {
        const configData = respond.data
        if (configData) {
          configData.denseInfoList.forEach(item => (
            this.$set(this.denseLevelOptions, item.encryptLevel, item.denseName)
          ))
        }
      })
    },
    parentIdObjChange(data) {
      this.checkedKeys.splice(0, this.checkedKeys.length, data)
      this.temp.groupId = data
    },
    getConfig() {
      getConfigByKey({ key: '3039' }).then(respond => {
        this.closeEncVer4 = respond.data ? Number(respond.data.value) : 0
      })
    },
    permanentChange(val) {
      this.dateRange = []
      if (val) {
        const date = new Date()
        const startDate = moment(date).format('YYYY-MM-DD')
        let endDate = moment(date).add(100, 'years').format('YYYY-MM-DD')
        if (moment(endDate).isAfter(moment('2188-01-01'))) {
          endDate = moment('2188-01-01').add(-1, 'days').format('YYYY-MM-DD')
        }
        this.dateRange.push(startDate)
        this.dateRange.push(endDate)
      }
    },
    handleDowload() {
      downloadTool({ toolType: 1 })
    },
    resetTemp() {
      this.dateRange = []
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      if (this.isShowEncryAuthorize) {
        this.temp.authType = 0
      } else if (this.isShowEncrySvrAuthorize) {
        this.temp.authType = 1
      } else {
        this.temp.authType = 2
      }
      this.temp.mcodes = []
    },
    resetTempM() {
      this.tempM = JSON.parse(JSON.stringify(this.defaultTempM))
    },
    mcodeGridTable() {
      return this.$refs['mcodeGrid']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getInterfaceAuthInfoList(searchQuery)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    handleDrag() {},
    handleRefresh() {
      this.query.searchInfo = undefined
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd(val) {
      this.deleteable = val.length > 0
    },
    mcodesSelectionChangeEnd(val) {
      this.deleteable1 = val.length > 0
    },
    formatterData() {
      this.temp.startDate = this.dateRange[0]
      this.temp.endDate = this.dateRange[1]
      if (this.temp.permanent) {
        this.temp.effectiveTime = this.$t('pages.permanentEffect')
      } else {
        this.temp.effectiveTime = this.temp.startDate + this.$t('pages.till') + this.temp.endDate
      }
      // this.temp.mcodes = this.mcodeGridTable().getDatas()
    },
    validateData() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.effectiveTimeNotConfig'),
          type: 'error',
          duration: 2000
        })
        return false
      } else if (this.temp.mcodes.length === 0) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.machineCodeMessage1'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    createData(isDownload) {
      if (this.closeEncVer4 == 1 && this.temp.decryptVersion == 9) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.interface_auth_msg17'),
          type: 'error',
          duration: 2000
        })
      } else {
        this.submitting = true
        this.$refs['dataForm'].validate((valid) => {
          if (valid && this.validateData()) {
            this.formatterData()
            createInterfaceAuthInfo(this.temp).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
              if (isDownload) {
                this.downloadClick(respond.data)
              }
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.submitting = false
          }
        })
      }
    },
    updateData(isDownload) {
      if (this.closeEncVer4 == 1 && this.temp.decryptVersion == 9) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.interface_auth_msg17'),
          type: 'error',
          duration: 2000
        })
      } else {
        this.submitting = true
        this.$refs['dataForm'].validate((valid) => {
          if (valid && this.validateData()) {
            this.formatterData()
            const tempData = Object.assign({}, this.temp)
            updateInterfaceAuthInfo(tempData).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
              if (isDownload) {
                this.downloadClick(respond.data)
              }
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.submitting = false
          }
        })
      }
    },
    formatDateRange(row) {
      this.dateRange = [row.startDate, row.endDate]
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.formatDateRange(row)
      this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        if (this.temp.groupId !== undefined && this.temp.groupId !== null && this.temp.groupId !== ' ') {
          this.checkedKeys.splice(0, 0, this.temp.groupId)
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteInterfaceAuthInfo({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.collectService_notifyMsg8'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    startDownload(row) {
      const that = this
      const file = buildDownloadFileByName(this.$t('route.' + this.$route.meta.title))
      file.steps = 3
      startDownload(row.id).then(response => {
        file.active = 1
        if (that.timer) {
          clearInterval(that.timer)
        }
        that.timer = setInterval(() => {
          if (file.percent < 90) {
            file.percent = file.percent + 10
          } else if (file.percent === 90) {
            file.percent = 99
            clearInterval(that.timer)
            that.timer = null
          }
        }, 100)
        this.$socket.subscribeToAjax(response, 'dkeyfile/result', (respond, handle) => {
          // 得到异步结果
          handle.close()
          if (that.timer) {
            clearInterval(that.timer)
            that.timer = null
          }
          file.active = 2
          // 请求文件下载
          const opts = { file, jwt: true, topic: 'InterfaceAuth' }
          downloadFile({ files: respond.data.fileName }, opts)
        })
      }).catch(reason => {
        console.log(reason)
        that.visible = false
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.interface_auth_msg6'),
          type: 'error',
          duration: 2000
        })
      })
    },
    downloadClick(row) {
      if (this.closeEncVer4 == 1 && row.decryptVersion == 9) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.interface_auth_msg16'),
          type: 'error',
          duration: 2000
        })
      } else {
        this.startDownload(row)
      }
    },
    handleMcodeCreate() {
      this.resetTempM()
      this.dialogStatus1 = 'createM'
      this.dialogMcodeFormVisible = true
      this.$nextTick(() => {
        this.$refs['mcodeForm'].clearValidate()
      })
    },
    handleMcodeUpdate(row) {
      this.resetTempM()
      this.tempM = JSON.parse(JSON.stringify(row)) // copy obj
      this.dialogStatus1 = 'updateM'
      this.dialogMcodeFormVisible = true
      this.$nextTick(() => {
        this.$refs['mcodeForm'].clearValidate()
      })
    },
    handleMcodeDelete() {
      const toDeleteIds = this.mcodeGridTable().getSelectedIds()
      this.mcodeGridTable().deleteRowData(toDeleteIds, this.temp.mcodes)
    },
    submitMcodeEnd() {
      this.$refs['mcodeForm'].validate((valid) => {
        if (valid) {
          if (this.dialogStatus1 === 'updateM') {
            const tempMData = JSON.parse(JSON.stringify(this.tempM))
            this.mcodeGridTable().updateRowData(tempMData, this.temp.mcodes)
          } else {
            this.tempM.id = new Date().getTime()
            this.mcodeGridTable().addRowData(this.tempM, this.temp.mcodes)
          }
          this.dialogMcodeFormVisible = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    timeValidator(rule, value, callback) {
      if (this.temp.startDate > this.temp.endDate) {
        callback(this.$t('pages.bossCode_Validate3'))
      } else {
        callback()
      }
    },
    mcodeValidator(rule, value, callback) {
      const mcodes = this.mcodeGridTable().getDatas()
      if (mcodes) {
        const re = /^[A-Za-z0-9]+$/
        if (!re.test(value)) {
          callback(this.$t('pages.interface_auth_msg10'))
        }
        let flag = false
        mcodes.forEach(item => {
          if (this.tempM.mcode === item.mcode && this.tempM.id !== item.id) {
            flag = true
          }
        })
        if (flag) {
          callback(this.$t('pages.interface_auth_msg7'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    versionFormatter: function(row, data) {
      return data === 1 ? this.$t('pages.interface_auth_msg9') : this.$t('pages.interface_auth_msg8')
    },
    effectiveDateFormatter: function(row, data) {
      if (row.permanent) {
        return this.$t('pages.permanentEffect')
      }
      return this.$t('text.beginToEnd', { startDate: moment(row.startDate).format('YYYY-MM-DD'), endDate: moment(row.endDate).format('YYYY-MM-DD') })
    }
  }
}
</script>
