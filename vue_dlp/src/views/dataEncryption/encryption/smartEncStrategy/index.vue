<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <smart-enc-stg-dlg
      ref="stgDlg"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      :content-stg-id-name="contentStgIdName"
      @submitEnd="submitEnd"
    />
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importSmartEncStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="false"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { exportStg } from '@/api/stgCommon'
import { getContentStgIdNameMap } from '@/api/contentStrategy/strategy/content'
import { getStrategyList, deleteStrategy } from '@/api/dataEncryption/encryption/smartEncStrategy'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, osTypeIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import SmartEncStgDlg from './editDlg'

export default {
  name: 'SmartEncStrategy',
  components: { SmartEncStgDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 96,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.stgFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {},
      contentStgIdName: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.loadContentStgOption()
  },
  activated() {
    this.loadContentStgOption()
  },
  methods: {
    stgFormatter(row, data) {
      let msg = ''
      const programName = this.processNameFormatter(row)
      msg += this.$t('table.programName') + ' : ' + (!programName ? this.$t('pages.null') : programName) + ' ; '
      const exceptionDir = this.exceptionDirFormatter(row)
      msg += this.$t('table.exceptionDirectory') + ' : ' + (!exceptionDir ? this.$t('pages.null') : exceptionDir) + ' ; '
      switch (row.sensitiveType) {
        case 1: msg += this.$t('pages.useSelfStg', { stg: this.$t('route.Content') }) + '(' + this.contentStgFormatter(row) + '); '; break;
        default: msg += this.$t('pages.useAllStg', { stg: this.$t('route.Content') }) + '; '; break;
      }
      msg += this.$t('pages.effectivechooseRuleGroup') + row.timeOut + this.$t('text.second') + '; '
      if (row.autoDecrypt) {
        msg += this.$t('pages.smart_Msg') + ' ; ';
      }
      if (row.closeAnalyse) {
        msg += this.$t('pages.smartMsg7') + ' ; ';
      }
      if (row.timeOutCtrl) {
        msg += this.$t('pages.smartMsg11') + ' ; ';
      }
      if (row.pwdErrFileCtrl) {
        msg += this.$t('pages.smartMsg10') + ' ; ';
      }
      return msg
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    loadContentStgOption() {
      this.$nextTick(() => {
        getContentStgIdNameMap(269).then(resp => {
          this.contentStgIdName = resp.data
        })
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    processNameFormatter: function(row, data) {
      const names = []
      if (row.config) {
        row.config.forEach(function(bean) {
          names.push(bean.processName)
        })
      }
      return names.join(',')
    },
    exceptionDirFormatter: function(row, data) {
      const names = []
      if (row.exceptDir) {
        row.exceptDir.forEach(function(bean) {
          names.push(bean.dir)
        })
      }
      return names.join(',')
    },
    contentStgFormatter: function(row, data) {
      if (row.contentStgId) {
        const stgName = this.contentStgIdName[row.contentStgId + '']
        if (stgName) {
          return stgName
        }
      }
      return ''
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>
