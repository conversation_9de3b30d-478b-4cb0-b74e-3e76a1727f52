<template>
  <content-stg
    ref="contentStg"
    :stg-code="269"
    :drip-able="false"
    :respond-able="false"
    :active-able="false"
    :advanced-rule-able="false"
    :import-able="false"
    :export-able="false"
    :create-stg-func="createContentStg"
    :update-stg-func="updateContentStg"
    :delete-stg-func="deleteContentStg"
    :stg-type-label="$t('route.smartEncStrategySensitive')"
    :rel-stg-type-label="$t('route.smartEncStrategy')"
  />
</template>

<script>
// content是html标签，组件作为标签不能使用该名称
import ContentStg from '@/views/contentStrategy/strategy/content'
import { createContentStg, updateContentStg, deleteContentStg } from '@/api/dataEncryption/encryption/smartEncStrategy'
export default {
  name: 'SmartEncStrategySensitive',
  components: { ContentStg },
  data() {
    return {
    }
  },
  methods: {
    createContentStg,
    updateContentStg,
    deleteContentStg
  }
}
</script>
