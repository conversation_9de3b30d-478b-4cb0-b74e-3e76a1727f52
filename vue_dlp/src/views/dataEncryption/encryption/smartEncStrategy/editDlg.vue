<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.smartEncStrategy')"
      :stg-code="96"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
        <el-card class="box-card" :body-style="{'padding': '5px'}" style="margin-left: 40px;">
          <data-editor
            :formable="formable"
            :popover-width="680"
            :updateable="controlledProcessEditable"
            :deletable="controlledProcessDeleteable"
            :add-func="createControlledProcess"
            :update-func="updateControlledProcess"
            :delete-func="deleteControlledProcess"
            :cancel-func="cancelControlledProcess"
            :before-update="beforeUpdateControlledProcess"
            :before-add="beforeAddControlledProcess"
          >
            <Form ref="smartEncForm" :model="tempP" :rules="tempPrules" label-position="right" label-width="110px" style="width: 600px; margin: auto;">
              <FormItem v-if="operationType === 'update'" :label="$t('pages.executableProgram')" :tooltip-content="$t('pages.executableProgramTip')" prop="processName" tooltip-placement="bottom-start">
                <el-upload name="processFile" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getFileName" style="display: inline-block;">
                  <el-button type="primary" icon="el-icon-upload" style="padding: 7px 13px;"></el-button>
                </el-upload>
                <el-button type="primary" size="mini" style="margin: 0 0 0 2px;" @click="showUpdateAppSelectDlg">
                  {{ $t('pages.smart_appLibImport') }}
                </el-button>
                <el-input v-model="tempP.processName" style="margin-top: 5px" :title="tempP.processName"></el-input>
              </FormItem>

              <FormItem v-if="operationType === 'create'" prop="processNames" :label="$t('pages.executableProgram')" :tooltip-content="$t('pages.executableProgramTip')" label-width="110px" tooltip-placement="bottom-start">
                <el-upload name="processFile" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getFileName" style="display: inline-block;">
                  <el-button type="primary" icon="el-icon-upload" style="padding: 7px 11px;margin: 1px 0;"></el-button>
                </el-upload>
                <el-button type="primary" size="mini" style="margin: 0 0 0 2px;" @click="showAppSelectDlg">
                  {{ $t('pages.smart_appLibImport') }}
                </el-button>
                <el-button size="mini" style="margin: 0;" @click="handleClear">
                  {{ $t('button.clear') }}
                </el-button>
                <tag
                  v-model="tempP.processNames"
                  :border="true"
                  :list="tempP.processNames"
                  :overflow-able="true"
                  max-height="150px"
                  style="margin-top: 5px"
                  :disabled="!formable"
                  @tagChange="tagChange"
                />
              </FormItem>

              <FormItem :label="$t('pages.catalogue')" :tooltip-content="$t('pages.smart_Msg1')" prop="dir" tooltip-placement="bottom-start">
                <el-input v-model="tempP.dir" maxlength=""></el-input>
              </FormItem>

              <FormItem :label="$t('pages.process_Msg5')" :tooltip-content="$t('pages.smart_Msg2')" label-width="110px" prop="suffix" tooltip-placement="bottom-start">
                <el-input v-model="tempP.suffix" type="text" maxlength="99" class="input-with-button"></el-input>
                <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
                  <el-button type="primary" size="mini" @click="handleFileSuffixImport()">
                    <svg-icon icon-class="import" />
                  </el-button>
                </el-tooltip>
              </FormItem>

              <FormItem :label="$t('table.triggerCondition')" :tooltip-content="$t('pages.smartMsg8')" tooltip-placement="bottom-start">
                <el-select v-model="tempP.activeType" style="width: 220px">
                  <el-option v-for="(item, index) in activeTypeOpts" :key="index" :label="item.label" :value="item.value"/>
                </el-select>
              </FormItem>
            </Form>
          </data-editor>
          <grid-table
            ref="processList"
            auto-height
            :max-height="300"
            :multi-select="true"
            :show-pager="false"
            :col-model="controlledColModel"
            :row-datas="temp.config"
            @selectionChangeEnd="processSelectionChange"
          />
          <FormItem :label="$t('pages.sensitiveStrategy')" label-width="70px" prop="contentStgId">
            <el-radio-group v-model="temp.sensitiveType" @change="clearStgFormValidate">
              <el-radio :label="0">
                <i18n path="pages.useAllStg">
                  <span slot="stg">{{ $t('route.Content') }}</span>
                </i18n>
                <link-button btn-class="editBtn" :formable="formable" :menu-code="'F12'" :always-show="true" :link-url="{ path: '/contentStrategy/contentStg/Content' }" style="height: 20px;line-height: 10px;margin: 5px;"/>
              </el-radio>
              <el-radio :label="1">
                <i18n path="pages.useSelfStg">
                  <span slot="stg">{{ $t('route.Content') }}</span>
                </i18n>
                <link-button btn-class="editBtn" :formable="formable" :menu-code="'E30'" :always-show="true" :link-url="{ path: '/dataEncryption/encryption/smartEncStrategySensitive', query: { stgTypeNumber: 269, treeable: false } }" style="height: 20px;line-height: 10px;margin: 5px;"/>
              </el-radio>
            </el-radio-group>
            <el-select v-if="temp.sensitiveType === 1" v-model="temp.contentStgId" clearable :placeholder="$t('pages.sysAlarmConfig_text9')" @change="clearStgFormValidate">
              <el-option v-for="(stgName, stgId) in contentStgOpts" :key="stgId" :label="stgName" :value="parseInt(stgId)"/>
            </el-select>
          </FormItem>
        </el-card>
        <el-card class="box-card" :body-style="{'padding': '5px'}" style="margin-left: 40px;">
          <div slot="header">
            <span>{{ $t('pages.exceptionDirectory') }}</span>
            <el-tooltip class="item" effect="dark" :content="$t('pages.smart_Msg3')" placement="bottom-start">
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <tag :list="temp.exceptDir" :disabled="!formable"/>
        </el-card>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <div style="margin-left: 30px;">
          <label>终端使用检测规则的操作方式操作文件后，根据敏感策略识别并加密敏感文件</label>
          <br/>
          <el-checkbox v-model="temp.closeAnalyse" :true-label="1" :false-label="0" :disabled="!formable" style="padding: 5px 0px;">{{ $t('pages.smartMsg7') }}</el-checkbox>
          <br/>
          <el-checkbox v-model="temp.autoDecrypt" :true-label="1" :false-label="0" :disabled="!formable" style="padding: 5px 0px;">{{ $t('pages.smart_Msg') }}</el-checkbox>
        </div>
        <div v-if="showOldConfig" style="margin-left: 30px;">
          <FormItem :label="$t('pages.effectivechooseRuleGroup')" label-width="90px">
            <el-input-number v-model="temp.timeOut" :max="999" :min="1" step-strictly :step="1" :controls="false" :disabled="!formable" style="width: 100px;"/>
            <span>{{ $t('text.second') }}</span>
          </FormItem>
          <el-checkbox v-model="temp.timeOutCtrl" :true-label="1" :false-label="0" :disabled="!formable" style="padding: 5px 0px;">{{ $t('pages.smartMsg11') }}</el-checkbox>
          <br/>
          <el-checkbox v-model="temp.pwdErrFileCtrl" :true-label="1" :false-label="0" :disabled="!formable" style="padding: 5px 0px;">
            {{ $t('pages.smartMsg10') }}
            <el-tooltip class="item" effect="dark" :content="$t('pages.respondActions14')" placement="bottom-start">
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
        </div>
      </template>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
    <app-select-dlg ref="processLib" :append-to-body="true" @select="importProcess"/>
    <app-select-dlg ref="updateProcessLib" :multiple="false" :append-to-body="true" @select="updateImportProcess"/>
  </div>
</template>

<script>
import { getContentStgIdNameMap } from '@/api/contentStrategy/strategy/content'
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/dataEncryption/encryption/smartEncStrategy'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg';

export default {
  name: 'SmartEncStgDlg',
  components: { AppSelectDlg, FileSuffixLibImport },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } },
    contentStgIdName: { type: Object, default: null } // 内容检测策略ID和名称映射
  },
  data() {
    return {
      controlledColModel: [
        { prop: 'processName', label: 'programName', width: '100', sort: true },
        { prop: 'suffix', label: 'suffixes', width: '80', sort: true },
        { prop: 'dir', label: 'catalogue', width: '80', sort: true },
        { prop: 'activeType', label: 'triggerCondition', width: '80', sort: true, sortOriginal: true, formatter: this.activeTypeFormatter }
      ],
      exceptionColModel: [
        { prop: 'dir', label: 'encryptionType', width: '100' }
      ],
      controlledProcessEditable: false,
      controlledProcessDeleteable: false,
      exceptionEditable: false,
      exceptionDeleteable: false,
      contentStgOpts: {},
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        exceptDir: [],
        config: [],
        autoDecrypt: 0,
        closeAnalyse: 0,
        timeOut: 10,
        timeOutCtrl: 1,
        pwdErrFileCtrl: 1,
        entityType: undefined,
        entityId: undefined,
        sensitiveType: 0,
        contentStgId: undefined
      },
      tempP: {},
      defaultTempP: {
        id: undefined,
        processName: '',
        suffix: undefined,
        dir: undefined,
        processNames: [],
        activeType: 0 // 触发条件
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        contentStgId: [
          { required: true, validator: this.contentStgIdValidator, trigger: 'blur' }
        ]
      },
      tempPrules: {
        processName: [
          { required: true, message: this.$t('pages.process_Msg10'), trigger: 'blur' },
          { validator: this.processNameValidator, trigger: 'blur' }
        ],
        suffix: [
          { required: true, validator: this.suffixValidator, trigger: 'blur' }
        ],
        dir: [
          { required: true, message: this.$t('pages.smart_Msg6'), trigger: 'blur' }
        ],
        processNames: [
          { required: true, validator: this.processNamesValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      slotName: '',
      activeTypeOpts: [
        { label: this.$t('pages.encryptTriggerOpt1'), value: 0 },
        { label: this.$t('pages.encryptTriggerOpt2'), value: 1 },
        { label: this.$t('pages.encryptTriggerOpt3'), value: 2 }
      ],
      operationType: '',
      errorMessage: '',
      showOldConfig: false // 是否显示特殊旧配置，旧版有，新版已废弃的配置
    }
  },
  computed: {
  },
  watch: {
    contentStgIdName() {
      this.loadContentStgOption()
    }
  },
  created() {
    this.resetTemp()
    this.loadContentStgOption()
    this.listenKeyToShowOldConfig()
  },
  activated() {
    this.loadContentStgOption()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    listenKeyToShowOldConfig() {
      // 监听快捷键显示旧配置，为了兼容3.53版本终端
      document.addEventListener('keydown', (event) => {
        if (event.ctrlKey && event.shiftKey && event.altKey && event.key === '?') {
          this.showOldConfig = !this.showOldConfig
          event.preventDefault();
          console.log('Ctrl + Shift + Alt + ? 快捷键被触发');
        }
      })
    },
    activeTypeFormatter(row, data) {
      return (this.activeTypeOpts.find(opt => opt.value === data) || this.activeTypeOpts[0]).label
    },
    getFileName(file) {
      if (this.operationType === 'create') {
        const list = [...this.tempP.processNames]
        list.push(file.name)
        this.tempP.processNames = this.verifyExeNames(list)
        this.$refs['smartEncForm'].validateField('processNames');
      } else if (this.operationType === 'update') {
        this.tempP.processName = file.name
        this.$refs['smartEncForm'].validateField('processName');
      }
      this.$refs['smartEncForm'].clearValidate()
      return false // 屏蔽了action的默认上传
    },
    processTable() {
      return this.$refs['processList']
    },
    processSelectionChange(rowDatas) {
      this.controlledProcessDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.controlledProcessEditable = true
      } else {
        this.controlledProcessEditable = false
        this.cancelControlledProcess()
      }
    },
    loadContentStgOption() {
      this.$nextTick(() => {
        if (this.contentStgIdName) {
          this.contentStgOpts = this.contentStgIdName
        } else {
          getContentStgIdNameMap(269).then(resp => {
            this.contentStgOpts = resp.data
          })
        }
      })
    },
    resetTemp() {
      this.showOldConfig = false
      this.temp = Object.assign({}, this.defaultTemp)
      this.resetTempP()
    },
    resetTempP() {
      this.operationType = 'create'
      this.tempP = Object.assign({}, this.defaultTempP)
      this.tempP.processNames = []
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.cancelControlledProcess()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.cancelControlledProcess()
      })
    },
    formatRowData(rowData) {
      rowData.config.forEach((item, index) => { item.id = index })
      const exceptDirArr = []
      rowData.exceptDir.forEach((item, index) => {
        exceptDirArr.push(item.dir)
      })
      rowData.exceptDir = exceptDirArr
    },
    formatFormData(formData) {
      const exceptDir = []
      formData.exceptDir.forEach(item => {
        exceptDir.push({ dir: item })
      })
      formData.exceptDir = exceptDir
      formData.config.forEach(item => delete item.id)
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    createControlledProcess() {
      let validate
      this.$refs['smartEncForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempP)
          const tempList = []
          const sameList = []
          for (let i = 0, len = this.tempP.processNames.length; i < len; i++) {
            const data = JSON.parse(JSON.stringify(rowData))
            data.id = new Date().getTime() + i
            data.processName = this.tempP.processNames[i]
            data.processNames = undefined
            const sameData = (this.temp.config || []).find(item => item.processName == data.processName && item.suffix == data.suffix && item.dir == data.dir)
            if (!sameData) { tempList.push(data) }
            if (sameData && sameData.activeType != data.activeType) {
              sameList.push(sameData)
            }
          }
          if (sameList.length > 0) {
            valid = false
            const message = sameList.reduce((a, b) =>
              a + `${this.$t('table.processName')}: ${b.processName} ${this.$t('table.suffixes')}: ${b.suffix} ${this.$t('table.catalogue')}: ${b.dir} ${this.$t('pages.smart_Msg5')}</br>`, ''
            )
            this.$message({
              message,
              type: 'error',
              duration: 4000,
              dangerouslyUseHTMLString: true
            })
          } else {
            this.temp.config.unshift(...tempList)
            this.cancelControlledProcess()
          }
          validate = valid
        }
      })
      return validate
    },
    beforeUpdateControlledProcess() {
      this.operationType = 'update'
      this.tempP = Object.assign(JSON.parse(JSON.stringify(this.defaultTempP)), this.processTable().getSelectedDatas()[0])
      this.operationType = 'update'
    },
    beforeAddControlledProcess() {
      this.operationType = 'create'
    },
    updateControlledProcess() {
      let validate
      this.$refs['smartEncForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempP)
          for (let i = 0, size = this.temp.config.length; i < size; i++) {
            const data = this.temp.config[i]
            if (rowData.id === data.id) {
              this.temp.config.splice(i, 1, rowData)
              break
            }
          }
          this.cancelControlledProcess()
          validate = valid
        }
      })
      return validate
    },
    deleteControlledProcess() {
      const toDeleteIds = this.processTable().getSelectedIds()
      this.temp.config.splice(0, this.temp.config.length, ...this.processTable().deleteRowData(toDeleteIds))
      this.cancelControlledProcess()
    },
    cancelControlledProcess() {
      this.processTable() && this.processTable().setCurrentRow()
      this.$refs['smartEncForm'] && this.$refs['smartEncForm'].clearValidate()
      this.resetTempP()
    },
    processNameValidator(rule, value, callback) {
      const reg = /^.*?\.(exe)|\*\.\*$/
      if (reg.test(value.toLowerCase())) {
        const size = this.temp.config.length
        for (let i = 0; i < size; i++) {
          const item = this.temp.config[i]
          // processName 在 update 才需验证 this.operationType === 'create' 恒为 false， 去掉该判断
          if (item.processName === value && item.id !== this.tempP.id) {
            // 重复性检验  还需要检验目录、后缀、触发条件  这些都相同才算重复数据
            if (item.suffix != this.tempP.suffix || item.dir != this.tempP.dir) {
              continue
            }
            callback(new Error(this.$t('pages.smart_Msg5')))
            return
          }
        }
        callback()
      } else {
        callback(new Error(this.$t('pages.smart_Msg4')))
      }
    },
    suffixValidator(rule, value, callback) {
      if (value === undefined || value === null || value.length === 0) {
        callback(new Error(this.$t('pages.process_Msg12')));
      } else if (value && value.length > 99) {
        callback(new Error(this.$t('pages.processMonitor_suffixOutMaxLength')));
      } else {
        callback();
      }
    },
    contentStgIdValidator(rule, value, callback) {
      if (this.temp.sensitiveType === 1 && (this.temp.contentStgId === undefined || this.temp.contentStgId === null || this.temp.contentStgId.length === 0)) {
        callback(new Error(this.$t('pages.sysAlarmConfig_text9')));
      } else {
        callback();
      }
    },
    clearStgFormValidate() {
      this.$refs['stgDlg'].clearValidate()
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      let union_suffix
      if (this.tempP.suffix == null || this.tempP.suffix === '') {
        union_suffix = [...new Set(suffix.split('|'))]
      } else {
        union_suffix = [...new Set((this.tempP.suffix + '|' + suffix).split('|'))]
      }
      this.tempP.suffix = union_suffix.join('|')
      this.$refs['smartEncForm'].validateField('suffix');
    },

    showAppSelectDlg() {
      this.$refs['processLib'].show()
    },
    showUpdateAppSelectDlg() {
      this.$refs['updateProcessLib'].show()
    },
    //  当列表发生改变时，校验名称是否符合规则
    tagChange(names) {
      names = this.verifyExeNames(names)
      this.tempP.processNames = names;
      this.$refs['smartEncForm'].validateField('processNames');
    },
    //  校验进程名集合
    verifyExeNames(names) {
      if (names.find(item => item === '*.*')) {
        names = ['*.*']
      }
      //  校验是否符合规则
      names = this.verifyRule(names)
      names = this.filterRepetitionData(names);
      // names = this.verifyExeNameExits(names);
      if (this.errorMessage !== '') {
        this.$message({
          message: this.errorMessage + this.$t('pages.smart_batchAddMsg1'),
          type: 'warning',
          duration: 3000
        })
      }
      this.errorMessage = ''
      return names;
    },
    verifyRule(names) {
      const reg = /^.*?\.(exe)|\*\.\*$/
      const oldLen = names.length
      names = names.filter(name => {
        return reg.test(name.toLowerCase());
      })
      if (names.length < oldLen) {
        this.errorMessage = this.errorMessage + (this.errorMessage !== '' ? ',' : '') + this.$t('pages.smart_batchAddMsg2');
      }
      return names;
    },
    //  校验进程名是否在列表中已存在
    verifyExeNameExits(names) {
      //  校验进程是否已存在
      const oldLen = names.length
      const exitNames = []    //  已存在的进程名
      names = names.filter(name => {
        const flag = this.temp.config.findIndex(item => {
          return item.processName.toLowerCase() === name.toLowerCase()
        }) === -1;
        if (!flag) {
          exitNames.push(name)
        }
        return flag;
      })
      if (names.length < oldLen) {
        this.errorMessage = this.errorMessage + (this.errorMessage !== '' ? ',' : '') + this.$t('pages.smart_processNameExisted', { processNames: exitNames.join(',') });
      }
      return names;
    },
    //  过滤重复数据(不区分大小写）
    filterRepetitionData(list) {
      const exitSizeNames = []  //  不区分大小写
      list = list.filter(name => {
        // const flag = this.temp.config.findIndex(item => { return item.processName === name }) === -1;
        const lowerCaseName = name.toLowerCase()
        const flag = exitSizeNames.findIndex(n => { return n === lowerCaseName }) === -1
        if (flag) {
          exitSizeNames.push(lowerCaseName)
        }
        return flag;
      })
      return list;
    },
    processNamesValidator(rule, value, callback) {
      if (this.tempP.processNames.length === 0) {
        callback(new Error(this.$t('pages.smart_pleaseEnterControlledProcess')))
      } else {
        callback()
      }
    },
    importProcess(processes) {
      if (this.tempP.processNames === undefined || this.tempP.processNames === null) {
        this.$set(this.tempP, 'processNames', [])
      }
      let processNames = [...this.tempP.processNames];
      (processes || []).forEach(item => {
        processNames.push(item.processName)
      })
      processNames = this.verifyExeNames(processNames)
      this.tempP.processNames = processNames
      this.$refs['smartEncForm'].validateField('processNames');
    },
    updateImportProcess(process) {
      if (process) {
        this.tempP.processName = process.processName
      }
    },
    handleClear() {
      this.tempP.processNames.splice(0)
      this.$refs['smartEncForm'].validateField('processNames');
    }
  }
}
</script>
