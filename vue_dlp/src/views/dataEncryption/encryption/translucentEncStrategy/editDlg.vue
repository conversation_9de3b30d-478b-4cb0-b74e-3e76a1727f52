<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.translucentEncStrategy')"
      :stg-code="97"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <el-tabs ref="tabs" v-model="activeName">
          <el-tab-pane :label="$t('pages.encryptionType')" name="encryptionType" style="padding: 5px;">
            <div class="button-container">
              <data-editor
                :formable="formable"
                :popover-width="800"
                append-to-body
                :updateable="encTypeEditable"
                :deletable="encTypeDeleteable"
                :add-func="createEncType"
                :update-func="updateEncType"
                :delete-func="deleteEncType"
                :cancel-func="cancelEncType"
                :before-update="beforeUpdateEncType"
                :before-add="beforeAddEncType"
              >
                <Form
                  ref="encTypeForm"
                  :model="encTypeTemp"
                  :rules="encTypeRules"
                  label-position="right"
                  label-width="90px"
                  style="width: 750px;"
                >
                  <FormItem :label="$t('pages.controlApp')" prop="processName">
                    <el-upload name="processFile" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getFileName" style="display: inline-block; position: absolute; right: 0;">
                      <el-button type="primary" icon="el-icon-upload" style="padding: 7px 13px 8px;" :disabled="!formable" size="small"></el-button>
                    </el-upload>
                    <!-- 半透明加密新增、修改程序名弹窗不建议批量导入 -->
                    <!-- <el-button type="primary" size="mini" @click="showMultipleChoiceAppSelectDlg">
                      {{ $t('pages.translucent_importProcessName') }}
                    </el-button> -->
                    <el-input v-model="encTypeTemp.processName" maxlength="" type="text" style="margin-top: 2px; padding-right: 50px; position: static;" :disabled="true"></el-input>
                  </FormItem>
                  <FormItem :label="$t('pages.encryptionType')">
                    <el-select v-model="encTypeTemp.encType" style="width: 100%;">
                      <el-option :label="$t('pages.translucent_Msg')" :value="11"></el-option>
                      <el-option :label="$t('pages.translucent_Msg1')" :value="12"></el-option>
                    </el-select>
                  </FormItem>

                  <el-card class="suffix-card">
                    <div slot="header" class="clearfix">
                      <span>{{ $t('pages.translucent_Msg2') }}</span>
                    </div>
                    <el-row v-for="(item, index) in encTypeTemp.suffixConfigTemp" :key="index">
                      <el-col :span="24">
                        <FormItem
                          :label="$t('pages.process_Msg5')"
                          :prop="getPropPath(index, 'suffix')"
                          :tooltip-content="$t('pages.translucent_Msg3')"
                          :rules="{ validator: suffixValidator, trigger: 'blur' }"
                        >
                          <el-input v-model="item.suffix" maxlength="" class="input-with-button"></el-input>
                          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
                            <el-button type="primary" size="mini" @click="handleFileSuffixImport(index)">
                              <svg-icon icon-class="import" />
                            </el-button>
                          </el-tooltip>
                        </FormItem>
                      </el-col>
                      <el-col :span="12">
                        <FormItem
                          :label="$t('pages.catalogue')"
                          :prop="getPropPath(index, 'dir')"
                          :rules="{ validator: dirValidator, trigger: 'blur' }"
                          :tooltip-content="$t('pages.translucent_Msg4')"
                        >
                          <el-input v-model="item.dir" maxlength=""></el-input>
                        </FormItem>
                      </el-col>
                      <el-col :span="12">
                        <FormItem
                          :label="$t('pages.type')"
                          :prop="getPropPath(index, 'code')"
                          :rules="{ validator: codeValidator, trigger: ['blur', 'change'] }"
                        >
                          <el-select v-model="item.code" clearable style="width: 100%;">
                            <el-option :label="$t('pages.translucent_Msg5')" :value="13"></el-option>
                            <el-option :label="$t('pages.translucent_Msg6')" :value="14"></el-option>
                            <el-option :label="$t('pages.translucent_Msg7')" :value="15"></el-option>
                          </el-select>
                        </FormItem>
                      </el-col>
                      <div class="icon-btn">
                        <i class="el-icon-circle-plus-outline" @click="addEmptySuffixRow( index + 1)"></i>
                        <i class="el-icon-remove-outline" style="color: red;" @click="deleteSuffixRow(index)"></i>
                      </div>
                    </el-row>
                  </el-card>
                </Form>
              </data-editor>
              <el-button v-if="formable" size="small" style="margin-left: 3px;" @click="showMultipleChoiceAppSelectDlg">
                {{ $t('pages.importProcessStg') }}
              </el-button>
            </div>
            <grid-table
              ref="encTypeList"
              :height="230"
              :multi-select="formable"
              :show-pager="false"
              :col-model="encTypeColModel"
              :row-datas="temp.encType"
              @selectionChangeEnd="encTypeSelectionChange"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.fileOutgoingConfig')" name="advanced" style="padding: 5px;">
            <Form :model="temp.config" label-position="right" label-width="90px" style="width: 696px;">
              <el-checkbox-group v-model="temp.config.option" @change="forceUpdate">
                <FormItem >
                  <el-checkbox :disabled="!formable" :label="2">{{ $t('pages.translucent_Msg8') }}</el-checkbox>
                </FormItem>
                <FormItem >
                  <el-checkbox :disabled="!formable" :label="4">{{ $t('pages.translucent_Msg9') }}</el-checkbox>
                </FormItem>
                <FormItem >
                  <el-checkbox :disabled="!formable" :label="1">{{ $t('pages.translucent_Msg10') }}</el-checkbox>
                </FormItem>
                <FormItem >
                  <span>{{ $t('pages.translucent_Msg11') }}</span>
                  <el-select v-model="temp.config.encLevel" :disabled="!formable" style="width: 250px;">
                    <el-option :label="$t('pages.translucent_Msg12')" :value="255"></el-option>
                    <el-option :label="$t('pages.publicDocuments')" :value="0"></el-option>
                  </el-select>
                </FormItem>
              </el-checkbox-group>
            </Form>
          </el-tab-pane>
        </el-tabs>
      </template>
    </stg-dialog>
    <translucent-process-name-import ref="multipleChoiceProcessLib" :multiple="true" :append-to-body="true" @select="importSingleProcess"/>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
  </div>
</template>

<script>
import {
  getStrategyByName,
  createStrategy,
  updateStrategy
} from '@/api/dataEncryption/encryption/translucentEncStrategy'
import TranslucentProcessNameImport from '@/views/dataEncryption/encryption/translucentEncStrategy/translucentProcessNameImport'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'

export default {
  name: 'TranslucentEncStgDlg',
  components: { TranslucentProcessNameImport, FileSuffixLibImport },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      encTypeColModel: [
        { prop: 'processName', label: 'programName', width: '80' },
        { prop: 'encType', label: 'encryptionType', width: '80', formatter: this.encTypeFormatter },
        { prop: 'suffixConfig', label: 'suffixConfiguration', width: '100', formatter: this.suffixConfigFormatter }
      ],
      encModeOptions: { 1: this.$t('pages.processStgMsg'), 3: this.$t('pages.processStgMsg1')/*, 5: '打开文件后加密'*/ },
      encTypeOptions: { '11': this.$t('pages.translucent_Msg'), '12': this.$t('pages.translucent_Msg1') },
      controlledTypeOptions: { '13': this.$t('pages.translucent_Msg5'), '14': this.$t('pages.translucent_Msg6'), '15': this.$t('pages.translucent_Msg7') },
      temp: { }, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        config: {
          option: [],
          encLevel: 255,
          processName: '*.*'
        },
        encType: [],
        enc: []
      },
      encTypeTemp: {},
      encTypeDefaultTemp: {
        id: null,
        processName: '',
        encType: 11,
        suffixConfigTemp: [{ dir: '', suffix: '', code: null }],
        suffix: null
      },
      rules: {
        name: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      },
      encTypeRules: {
        processName: [
          { required: true, message: this.$t('pages.translucent_Msg13'), trigger: 'blur' },
          { validator: this.processNameValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      slotName: undefined,
      encTypeEditable: false,
      encTypeDeleteable: false,
      activeName: 'encryptionType',
      importFileSuffixIndex: 0,

      operationType: '',
      processImportVisible: false
    }
  },
  computed: {
    getPropPath() {
      return (index, prop) => `suffixConfigTemp.${index}.${prop}`;
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    encTypeTable() {
      return this.$refs['encTypeList']
    },
    getFileName(file) {
      this.encTypeTemp.processName = file.name
      this.$refs['encTypeForm'].validateField('processName');
      return false // 屏蔽了action的默认上传
    },
    forceUpdate() {
      this.$forceUpdate()
    },
    resetTemp() {
      this.activeName = 'encryptionType'
      this.temp = Object.assign({}, this.defaultTemp)
      this.resetEncTypeTemp()
    },
    resetEncTypeTemp() {
      this.encTypeTemp = JSON.parse(JSON.stringify(this.encTypeDefaultTemp))
    },
    addEmptySuffixRow(index) {
      this.encTypeTemp.suffixConfigTemp.splice(index, 0, { dir: '', suffix: '', code: null })
    },
    deleteSuffixRow(index) {
      if (this.encTypeTemp.suffixConfigTemp.length === 1) {
        this.$set(this.encTypeTemp.suffixConfigTemp, 0, { dir: '', suffix: '', code: null })
      } else {
        this.encTypeTemp.suffixConfigTemp.splice(index, 1)
      }
    },
    encTypeSelectionChange(rowDatas) {
      this.encTypeDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.encTypeEditable = true
      } else {
        this.encTypeEditable = false
        this.cancelEncType()
      }
      this.$refs['encTypeForm'] && this.$refs['encTypeForm'].clearValidate()
    },
    handleFilter() {
    },
    handleDrag() {
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.$refs.encTypeForm && this.$refs.encTypeForm.clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.$refs.encTypeForm && this.$refs.encTypeForm.clearValidate()
      })
    },
    formatRowData(rowData) {
      const encMap = {}
      if (rowData.enc) {
        rowData.enc.forEach(item => {
          if (!encMap.hasOwnProperty(item.processName)) encMap[item.processName] = []
          encMap[item.processName].push(item)
        })
      }
      if (rowData.encType) {
        rowData.encType.forEach(function(item, index) {
          item.id = index
          item.suffix = {}
          const encList = encMap[item.processName]
          if (encList) {
            encList.forEach(e => {
              const key = e.processName + e.dir + e.suffix + e.code
              item.suffix[key] = e
            })
          }
        })
      }
      if (rowData.config && Object.keys(rowData.config).length > 0) {
        const config = Array.isArray(rowData.config) ? rowData.config[0] : rowData.config
        const optionArr = this.numToList(config.option, 3)
        config.option = this.formatAdvancedOptionValue(optionArr)
        rowData.config = config
      }
    },
    formatFormData(formData) {
      formData.config.option = this.getSum(this.formatAdvancedOptionValue(formData.config.option))
      const enc = []
      const encType = []
      if (formData.encType) {
        formData.encType.forEach(item => {
          encType.push({ processName: item.processName, encType: item.encType })
          if (item.suffix) {
            Object.values(item.suffix).forEach(s => { enc.push(s) })
          }
        })
        formData.enc = enc
        formData.encType = encType
        formData.config = [formData.config]
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    createEncType() {
      let validate
      this.$refs['encTypeForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.encTypeTemp)
          const data = JSON.parse(JSON.stringify(rowData))
          const suffixConfigTemp = JSON.parse(JSON.stringify(this.encTypeTemp.suffixConfigTemp))
          data.id = new Date().getTime()
          data.processName = rowData.processName
          data.suffix = {}
          suffixConfigTemp.forEach(item => {
            item.processName = rowData.processName
            if (item.code) {
              const key = item.processName + item.dir + item.suffix + item.code
              data.suffix[key] = item
            }
          })
          this.temp.encType.unshift(data)
          this.cancelEncType()
          validate = valid
        }
      })
      return validate
    },
    beforeUpdateEncType() {
      this.encTypeTemp = JSON.parse(JSON.stringify(this.encTypeTable().getSelectedDatas()[0]))
      if (!this.encTypeTemp.suffixConfigTemp) {
        this.$set(this.encTypeTemp, 'suffixConfigTemp', [{ dir: '', suffix: '', code: null }])
      }
      const suffixConfigTemp = []
      if (this.encTypeTemp.suffix) {
        Object.values(this.encTypeTemp.suffix).forEach(item => {
          item.code = item.code * 1
          suffixConfigTemp.push(item)
        })
      }
      if (suffixConfigTemp.length == 0) {
        suffixConfigTemp.push({ dir: '', suffix: '', code: null })
      }
      this.encTypeTemp.suffixConfigTemp.splice(0, this.encTypeTemp.suffixConfigTemp.length, ...suffixConfigTemp)
      this.operationType = 'update'
    },
    beforeAddEncType() {
      this.operationType = 'create'
    },
    updateEncType() {
      let validate
      this.$refs['encTypeForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.encTypeTemp)
          rowData.suffix = {}
          this.encTypeTemp.suffixConfigTemp.forEach(item => {
            item.processName = rowData.processName
            const key = item.processName + item.dir + item.suffix + item.code
            rowData.suffix[key] = item
          })
          for (let i = 0, size = this.temp.encType.length; i < size; i++) {
            const data = this.temp.encType[i]
            if (rowData.id === data.id) {
              this.temp.encType.splice(i, 1, rowData)
              break
            }
          }
          this.cancelEncType()
          validate = valid
        }
      })
      return validate
    },
    deleteEncType() {
      const toDeleteIds = this.encTypeTable().getSelectedIds()
      this.encTypeTable().deleteRowData(toDeleteIds, this.temp.encType)
      this.cancelEncType()
    },
    cancelEncType() {
      this.encTypeTable().setCurrentRow()
      this.$refs['encTypeForm'] && this.$refs['encTypeForm'].clearValidate()
      this.resetEncTypeTemp()
    },
    formatAdvancedOptionValue(optionArr) {
      const advancedOptionArrTemp = JSON.parse(JSON.stringify(optionArr))
      if (advancedOptionArrTemp.indexOf(1) >= 0) { // 由于1代表的选项的值对于终端来说是要取相反的值，即存在1，则去除1，不存在1，则添加1
        const index = advancedOptionArrTemp.indexOf(1)
        advancedOptionArrTemp.splice(index, 1)
      } else { // 不存在1，则添加1
        advancedOptionArrTemp.push(1)
      }
      return advancedOptionArrTemp
    },
    processNameValidator(rule, value, callback) {
      const size = this.temp.encType.length
      for (let i = 0; i < size; i++) {
        const item = this.temp.encType[i]
        if (item.processName === value && (this.operationType === 'create' || item.id !== this.encTypeTemp.id)) {
          callback(new Error(this.$t('pages.process_Validate4')))
          return
        }
      }
      callback()
    },
    suffixValidator(rule, value, callback) {
      const index = rule.field.split('.')[1]
      const { suffix, dir, code } = this.encTypeTemp.suffixConfigTemp[index]
      if (!suffix && (dir || code)) {
        callback(new Error(this.$t('pages.translucent_Msg14')))
      }
      callback()
    },
    dirValidator(rule, value, callback) {
      const index = rule.field.split('.')[1]
      const { suffix, dir, code } = this.encTypeTemp.suffixConfigTemp[index]
      if (!dir && (suffix || code)) {
        callback(new Error(this.$t('pages.translucent_Msg15')))
      }
      callback()
    },
    codeValidator(rule, value, callback) {
      const index = rule.field.split('.')[1]
      const { suffix, dir, code } = this.encTypeTemp.suffixConfigTemp[index]
      if (!code && (suffix || dir)) {
        callback(new Error(this.$t('pages.translucent_Msg16')))
      }
      callback()
    },
    encTypeFormatter: function(row, data) {
      return this.encTypeOptions[data]
    },
    suffixConfigFormatter: function(row, data) {
      const that = this
      let result = ''
      if (row.suffix) {
        Object.values(row.suffix).forEach(item => {
          const { code, dir, suffix } = item
          const codeName = that.controlledTypeOptions[code]
          if (codeName) {
            result += `${codeName}(${dir}, ${suffix});`
          }
        })
      }
      return result
    },
    handleFileSuffixImport(index) {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
      this.importFileSuffixIndex = index
    },
    importFileSuffix(suffix) {
      const index = this.importFileSuffixIndex
      let union_suffix
      if (this.encTypeTemp.suffixConfigTemp[index].suffix === '') {
        union_suffix = [...new Set(suffix.split('|'))]
      } else {
        union_suffix = [...new Set((suffix + '|' + this.encTypeTemp.suffixConfigTemp[index].suffix).split('|'))]
      }
      this.encTypeTemp.suffixConfigTemp[index].suffix = union_suffix.join('|')
      this.$refs['encTypeForm'].validateField('suffix');
    },

    //  校验进程名集合
    verifyExeNames(names) {
      this.$refs['encTypeForm'] && this.$refs['encTypeForm'].clearValidate()
      names = this.filterRepetitionData(names);
      names = this.verifyExeNameExits(names);
      if (names.length === 0) {
        this.$refs['encTypeForm'] && this.$refs['encTypeForm'].validate('processNames')
      }
      return names;
    },
    //  校验进程名是否已存在
    verifyExeNameExits(names) {
      //  校验进程是否已存在
      const oldLen = names.length
      const exitNames = []    //  已存在的进程名
      names = names.filter(name => {
        const flag = this.temp.encType.findIndex(item => { return item.processName === name }) === -1;
        if (!flag) {
          exitNames.push(name)
        }
        return flag;
      })
      if (names.length < oldLen) {
        this.$message({
          message: this.$t('pages.translucent_processNameExisted', { processNames: exitNames.join(',') }),
          type: 'warning',
          duration: 3000
        })
      }
      return names;
    },
    //  过滤重复数据
    filterRepetitionData(list) {
      const tList = []
      list.forEach(item => {
        if (!tList.includes(item)) {
          tList.push(item)
        }
      })
      return tList;
    },
    showMultipleChoiceAppSelectDlg() {
      this.$refs['multipleChoiceProcessLib'].show()
    },
    generateRandomNumber(length) {
      let result = '';
      const characters = '0123456789';
      const charactersLength = characters.length;
      for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }
      return result;
    },
    importSingleProcess(process) {
      var data = { id: '', processName: '', suffix: {}, encType: '' }
      var suffixTemp = { dir: '', suffix: '', code: null, processName: '' }
      var processNameList = this.temp.encType.map(item => item.processName)
      process.forEach((item, pos) => {
        if (!processNameList.includes(item.label)) {
          data.processName = item.label
          data.encType = item.encryptType
          const suffixConfig = item.suffixConfig
          if (suffixConfig.length > 0) {
            suffixConfig.forEach((res, index1) => {
              suffixTemp.processName = item.label
              const [suffix, dir, code] = res.split('??')
              Object.assign(suffixTemp, { suffix, dir, code })
              const key = suffixTemp.processName + dir + suffix + code
              data.suffix[key] = suffixTemp
              suffixTemp = { dir: '', suffix: '', code: null, processName: '' }
            })
          }
          data.id = new Date().getTime() + '-' + pos
          this.temp.encType.unshift(data)
          data = { id: '', processName: '', suffix: {}, encType: '' }
        }
      })
      // if (process && this.encTypeTemp.processName !== process.label) {
      //   const processNameList = this.verifyExeNames([process.label])
      //   if (processNameList.length > 0) {
      //     this.encTypeTemp.processName = processNameList[0]
      //   }
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
  .suffix-card {
    margin-top: 20px;
    >>>.el-card__body {
      max-height: 315px;
      overflow: auto;
    }
    .el-row {
      padding: 5px 40px 5px 5px;
      margin-bottom: 5px;
      position: relative;
      border: 1px solid #eee;
      border-radius: 5px;
    }
    .icon-btn {
      width: 20px;
      position: absolute;
      right: 0;
      color: #68a8d0;
      cursor: pointer;
    }
  }
  .button-container {
    width: 100%;
    text-align: left; /* 按钮靠左对齐 */
  }

  .button-container > * {
    display: inline-block; /* 按钮并排显示 */
    vertical-align: middle; /* 确保按钮垂直对齐 */
  }
</style>
