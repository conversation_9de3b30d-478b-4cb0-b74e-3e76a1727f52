<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="title"
      :visible.sync="dialogVisible"
      width="500px"
    >
      <tree-menu
        v-show="dialogVisible"
        ref="processTree"
        node-key="dataId"
        :data="dataTree"
        :default-expanded-keys="selfLibExpandKeys"
        :multiple="multiple"
        :is-filter="false"
        :local-search="false"
        :height="450"
        @node-click="nodeClick"
        @check-change="checkChange"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmImport()">
          {{ $t('pages.translucent_addSelectedAppName') }}
        </el-button>
        <el-button @click="hide">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'TranslucentProcessNameImport',
  props: {
    title: { type: String, default() { return this.$t('pages.importProcessStg') } },
    appendToBody: { type: Boolean, default: false },
    multiple: { type: Boolean, default: true },
    height: { type: Number, default: 420 }
  },
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'programName', width: '80', fixed: true },
        { prop: 'desc', label: 'fileDescription', width: '80', fixed: true }
      ],
      dialogVisible: false,
      dataTree: [{ id: 'G0', dataId: '0', label: this.$t('pages.all'), parentId: '', children: [] }],
      //  软件类型
      types: [
        //  WPS 软件
        { label: this.$t('pages.softWps'), type: 'Type1', children: [] },
        //  Office 软件
        { label: this.$t('pages.softOffice'), type: 'Type2', children: [] },
        //  Adobe PDF阅读器
        { label: this.$t('pages.softAdobePdf'), type: 'Type3', children: [] },
        //  极速PDF阅读器
        { label: this.$t('pages.softJiSuPdf'), type: 'Type4', children: [] },
        //  迅捷PDF
        { label: this.$t('pages.softQuickPdf'), type: 'Type5', children: [] },
        //  福昕阅读器
        { label: this.$t('pages.softFoxit'), type: 'Type6', children: [] },
        //  记事本
        { label: this.$t('pages.softNotepad'), type: 'Type7', children: [] },
        //  写字板
        { label: this.$t('pages.softClipboard'), type: 'Type8', children: [] },
        //  普米/白板(ActivInspire)
        { label: this.$t('pages.softActivInspire'), type: 'Type9', children: [] }
      ],
      softWares: [
        // encryptType: 11 - 不允许模式切换  12 - 允许模式切换, 在editDlg页面会进行转换
        // .keys??office6\\cache??15  3个参数分别代表后缀、目录、类型，3个参数之间以??进行区分，如果有哪个参数为空则以null代替'
        //  3个参数只有1、全部为空 2、全部不为空两种情况
        //  WPS 软件
        { label: 'WPS.exe', type: 'software', parentId: this.$t('pages.softWps'), encryptType: 11, suffixConfig: ['.keys??office6\\cache??15', '.wpt|.dotm??kingsoft\\office6\\templates??15', '.wpt??office6\\mui\\zh_CN\\templates??15', '.xml??office6\\mui\\zh_CN\\templates\\Wpp Default Object??15', '.html??office6\\addons\\knewdocs\\res??15', '.html??office6\\addons\\kusercenter\\mui??15'] }, //  WPS 软件
        { label: 'Et.exe', type: 'software', parentId: this.$t('pages.softWps'), encryptType: 11, suffixConfig: ['.keys??office6\\cache??15'] },
        { label: 'Wpp.exe', type: 'software', parentId: this.$t('pages.softWps'), encryptType: 11, suffixConfig: [] },
        { label: 'Wpspdf.exe', type: 'software', parentId: this.$t('pages.softWps'), encryptType: 11, suffixConfig: [] },
        //  Office 软件
        { label: 'Winword.exe', type: 'software', parentId: this.$t('pages.softOffice'), encryptType: 11, suffixConfig: ['.dotm??*??15'] },  //  Office 软件
        { label: 'Excel.exe', type: 'software', parentId: this.$t('pages.softOffice'), encryptType: 11, suffixConfig: ['.xlb??Roaming\\Microsoft\\??15'] },
        { label: 'Powerpnt.exe', type: 'software', parentId: this.$t('pages.softOffice'), encryptType: 11, suffixConfig: [] },
        //  Adobe PDF阅读器
        { label: 'AcroRd32.exe', type: 'software', parentId: this.$t('pages.softAdobePdf'), encryptType: 11, suffixConfig: [] }, //  Adobe PDF阅读器
        { label: 'Acrobat.exe', type: 'software', parentId: this.$t('pages.softAdobePdf'), encryptType: 11, suffixConfig: [] },
        //  极速PDF阅读器
        { label: 'JisuPdf.exe', type: 'software', parentId: this.$t('pages.softJiSuPdf'), encryptType: 11, suffixConfig: [] }, //  极速PDF阅读器
        //  迅捷PDF
        { label: 'XunjiePDFEditor.exe', type: 'software', parentId: this.$t('pages.softQuickPdf'), encryptType: 11, suffixConfig: [] },  //  迅捷PDF
        //  福昕阅读器
        { label: 'FoxitReader.exe', type: 'software', parentId: this.$t('pages.softFoxit'), encryptType: 11, suffixConfig: [] },
        { label: 'FoxitReaderPlus.exe', type: 'software', parentId: this.$t('pages.softFoxit'), encryptType: 11, suffixConfig: [] },
        { label: 'FoxitPDFReader.exe', type: 'software', parentId: this.$t('pages.softFoxit'), encryptType: 11, suffixConfig: [] },
        //  记事本
        { label: 'Notepad.exe', type: 'software', parentId: this.$t('pages.softNotepad'), encryptType: 11, suffixConfig: [] },
        //  写字板
        { label: 'Wordpad.exe', type: 'software', parentId: this.$t('pages.softClipboard'), encryptType: 11, suffixConfig: [] },
        //  普米/白板(ActivInspire)
        { label: 'Inspire.exe', type: 'software', parentId: this.$t('pages.softActivInspire'), encryptType: 11, suffixConfig: [] }
      ],
      selfLibExpandKeys: ['0'], //  默认展示
      checkNodes: []  //  选中的节点
    }
  },
  computed: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    loadDataTree() {
      //  给每个软件添加dataId
      this.softWares.forEach(item => {
        item.dataId = item.parentId + item.label
      })
      this.types.forEach(type => {
        type.dataId = type.type
        type.children = this.softWares.filter(item => { return item.parentId === type.label })
      })
      this.dataTree[0].children = this.types
    },
    show() {
      this.loadDataTree()
      this.dialogVisible = true
    },
    hide() {
      this.checkNodes = []
      this.$refs.processTree && this.$refs.processTree.clearSelectedNodes()
      this.dialogVisible = false
    },
    confirmImport() {
      const list = this.checkNodes.filter(item => { return item.type === 'software' })
      if (list == null || list.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.processStgLib_addSelectedProcessNameHint'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.$emit('select', this.multiple ? list : list[0])
      this.hide()
    },
    checkChange(keys, nodes) {
      this.checkNodes = nodes;
    },
    nodeClick(data, node, el) {
      this.checkNodes = data ? [data] : []
    }
  }
}
</script>

<style lang='scss' scoped>
.module-form{
  margin-left: 210px;
  height: 100%;
  overflow: auto;
  .el-tabs{
    height: calc(100% - 40px);
  }
  .el-tab-pane{
    padding: 0 10px 10px;
  }
}
.app-container .tree-container.hidden+.module-form{
  margin-left: 0;
}
</style>
