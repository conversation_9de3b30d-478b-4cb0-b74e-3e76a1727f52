<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('route.filePermissionControl')"
      :stg-code="283"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      label-w="80px"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <import-table
          ref="importTable"
          :show-import="false"
          :show-search="false"
          :delete-disabled="!deleteable"
          :col-model="colModel"
          :row-datas="rowDatas"
          :formable="formable"
          :selectable="() => true"
          :handle-create="handleFilePermCtrlCreate"
          :handle-delete="handleFilePermCtrlDelete"
          @selectionChangeEnd="selectionChangeEnd"
        />
      </template>
    </stg-dialog>

    <!-- 新增、修改，文件权限控制配置-->
    <el-dialog
      v-el-drag-dialog
      :title="textMap[dialogStatus]"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="filePermCtrlRules"
        :model="filePermCtrlTemp"
        label-position="right"
        label-width="75px"
      >
        <el-divider content-position="left">{{ $t('pages.ctrlObjSetting') }}</el-divider>
        <FormItem :label="$t('table.processGroup')" prop="processGroupId">
          <el-select v-model="filePermCtrlTemp.processGroupId" style="width: calc(100% - 35px)" :placeholder="$t('text.select')">
            <el-option v-for="item in processGroupOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <link-button :menu-code="'E3G'" :click-func="'handleJump'" style="margin-top: 0" @handleJump="handleJump('processTab')"/>
        </FormItem>
        <FormItem :label="$t('table.pathGroup')" prop="pathGroupId">
          <el-select v-model="filePermCtrlTemp.pathGroupId" style="width: calc(100% - 35px)" :placeholder="$t('text.select')">
            <el-option v-for="item in pathGroupOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <link-button :menu-code="'E3G'" :click-func="'handleJump'" style="margin-top: 0" @handleJump="handleJump('pathTab')"/>
        </FormItem>
        <FormItem :label="$t('table.suffixGroup')" prop="suffixGroupId">
          <el-select v-model="filePermCtrlTemp.suffixGroupId" style="width: calc(100% - 35px)" :placeholder="$t('text.select')">
            <el-option v-for="item in suffixGroupOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <link-button :menu-code="'E3G'" :click-func="'handleJump'" style="margin-top: 0" @handleJump="handleJump('suffixTab')"/>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.ctrlParamSetting') }}</el-divider>
        <FormItem :label="$t('pages.controlType')" prop="controlType">
          <el-select v-model="filePermCtrlTemp.controlType" :placeholder="$t('text.select')">
            <el-option v-for="item in controlTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.priority')" prop="level">
          <el-select v-model="filePermCtrlTemp.level" :placeholder="$t('text.select')">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('table.controlPerm')" prop="controlPermList">
          <el-checkbox-group v-model="filePermCtrlTemp.controlPermList">
            <el-checkbox v-for="item in controlPermOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <FormItem :label="$t('table.driverType')" class="last-form-item" prop="diskTypeList">
          <el-checkbox-group v-model="filePermCtrlTemp.diskTypeList">
            <el-checkbox v-for="item in diskTypeOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="dialogStatus === 'create' ? createData() : dialogStatus === 'update' ? updateData() : {}">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createStrategy, getStrategyByName, updateStrategy } from '@/api/dataEncryption/encryption/filePermissionControl'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'

export default {
  name: 'FilePermissionControlDlg',
  components: { ImportTable },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } },
    processGroupOptions: { type: Array, default() { return [] } },
    pathGroupOptions: { type: Array, default() { return [] } },
    suffixGroupOptions: { type: Array, default() { return [] } },
    controlTypeOptions: { type: Array, default() { return [] } },
    levelOptions: { type: Array, default() { return [] } },
    controlPermOptions: { type: Array, default() { return [] } },
    diskTypeOptions: { type: Array, default() { return [] } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      colModel: [
        { prop: 'processGroupId', label: 'processGroup', width: '150', formatter: this.processGroupIdFormatter },
        { prop: 'pathGroupId', label: 'pathGroup', width: '150', formatter: this.pathGroupIdFormatter },
        { prop: 'suffixGroupId', label: 'suffixGroup', width: '150', formatter: this.suffixGroupIdFormatter },
        { prop: 'controlType', label: this.$t('pages.controlType'), width: '100', formatter: this.controlTypeFormatter },
        { prop: 'level', label: 'priority', width: '100', formatter: this.levelFormatter },
        { prop: 'controlPermList', label: 'controlPerm', width: '150', formatter: this.controlPermListFormatter },
        { prop: 'diskTypeList', label: 'driverType', width: '150', formatter: this.diskTypeListFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', click: this.handleFilePermCtrlUpdate
          }]
        }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: undefined,
        entityId: undefined
      },
      filePermCtrlTemp: {},
      defaultFilePermCtrlTemp: {
        id: undefined,
        processGroupId: undefined,
        pathGroupId: undefined,
        suffixGroupId: undefined,
        controlType: undefined,
        controlPermList: [],
        diskTypeList: [],
        level: undefined
      },
      dialogStatus: 'create',
      dialogFormVisible: false,
      dlgSubmitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('table.config'), 'update'),
        create: this.i18nConcatText(this.$t('table.config'), 'create')
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      filePermCtrlRules: {
        processGroupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'change' }],
        pathGroupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'change' }],
        suffixGroupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'change' }],
        controlType: [{ required: true, message: this.$t('text.select'), trigger: 'change' }],
        level: [{ required: true, message: this.$t('text.select'), trigger: 'change' }],
        controlPermList: [{ required: true, message: this.$t('text.select'), trigger: 'change' }],
        diskTypeList: [{ required: true, message: this.$t('text.select'), trigger: 'change' }]
      },
      deleteable: false,
      rowDatas: []
    }
  },
  computed: {
    importTable() {
      return this.$refs['importTable']
    }
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    handleDrag() {
    },
    handleCreate() {
      this.rowDatas = []
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.rowDatas = []
      this.$refs['stgDlg'].show(row, this.formable)
    },
    handleFilePermCtrlCreate() {
      this.resetFilePermCtrlTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    handleFilePermCtrlUpdate(row) {
      this.resetFilePermCtrlTemp()
      this.dialogStatus = 'update'
      this.filePermCtrlTemp = JSON.parse(JSON.stringify(row))
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.filePermCtrlTemp))
          formData.id = new Date().getTime()
          const flag = this.validConfig(formData)
          if (flag === 'conflictConfig') {
            this.$message({
              message: this.$t('pages.notAllowConflictConfig'),
              type: 'error',
              duration: 2000
            })
            return
          } else if (flag === 'sameConfig') {
            this.$message({
              message: this.$t('valid.customizeSameName', { obj: this.$t('table.config') }),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.rowDatas.unshift(formData)
          this.dialogFormVisible = false
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.filePermCtrlTemp))
          const flag = this.validConfig(formData)
          if (flag === 'conflictConfig') {
            this.$message({
              message: this.$t('pages.notAllowConflictConfig'),
              type: 'error',
              duration: 2000
            })
            return
          } else if (flag === 'sameConfig') {
            this.$message({
              message: this.$t('valid.customizeSameName', { obj: this.$t('table.config') }),
              type: 'error',
              duration: 2000
            })
            return
          }
          const index = this.rowDatas.findIndex(item => item.id === formData.id)
          if (index > -1) {
            this.rowDatas.splice(index, 1, formData)
          }
          this.dialogFormVisible = false
        }
      })
    },
    handleFilePermCtrlDelete() {
      const ids = this.importTable.getSelectedIds()
      ids.forEach((id) => {
        const index = this.rowDatas.findIndex(data => data.id === id)
        if (index > -1) {
          this.rowDatas.splice(index, 1)
        }
      })
    },
    formatFormData(formData) {
      formData.configList = this.rowDatas.map(data => {
        data.controlPerm = this.getSum(data.controlPermList)
        data.diskType = this.getSum(data.diskTypeList)
        return data
      })
    },
    formatRowData(rowData) {
      this.rowDatas = (rowData.configList || []).map(config => {
        config.controlPermList = this.numToList(config.controlPerm, 3)
        config.diskTypeList = this.numToList(config.diskType, 4)
        return config
      })
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    resetFilePermCtrlTemp() {
      this.filePermCtrlTemp = Object.assign({}, this.defaultFilePermCtrlTemp)
    },
    processGroupIdFormatter(row, data) {
      for (const group of this.processGroupOptions) {
        if (group.value == data) {
          return group.label
        }
      }
    },
    pathGroupIdFormatter(row, data) {
      for (const group of this.pathGroupOptions) {
        if (group.value == data) {
          return group.label
        }
      }
    },
    suffixGroupIdFormatter(row, data) {
      for (const group of this.suffixGroupOptions) {
        if (group.value == data) {
          return group.label
        }
      }
    },
    controlTypeFormatter(row, data) {
      for (const controlOpt of this.controlTypeOptions) {
        if (controlOpt.value == data) {
          return controlOpt.label
        }
      }
    },
    levelFormatter(row, data) {
      for (const levelOpt of this.levelOptions) {
        if (levelOpt.value == data) {
          return levelOpt.label
        }
      }
    },
    controlPermListFormatter(row, data) {
      const copyObj = JSON.parse(JSON.stringify(data))
      const sortData = copyObj.sort((a, b) => a - b);
      const resList = []
      for (const ctrlPermVal of sortData) {
        for (const controlPerm of this.controlPermOptions) {
          if (controlPerm.value == ctrlPermVal) {
            resList.push(controlPerm.label)
          }
        }
      }
      return resList.join('、')
    },
    diskTypeListFormatter(row, data) {
      const copyObj = JSON.parse(JSON.stringify(data))
      const sortData = copyObj.sort((a, b) => a - b);
      const resList = []
      for (const diskTypeVal of sortData) {
        for (const diskOpt of this.diskTypeOptions) {
          if (diskOpt.value == diskTypeVal) {
            resList.push(diskOpt.label)
          }
        }
      }
      return resList.join('、')
    },
    handleJump(tabName) {
      this.$router.push({ path: '/dataEncryption/FileSecuritySet/filePermissionControlLib', query: { tabName }})
    },
    validConfig(formData) {
      const id = formData.id
      const processGroupId = formData.processGroupId
      const pathGroupId = formData.pathGroupId
      const suffixGroupId = formData.suffixGroupId
      const level = formData.level
      const controlType = formData.controlType
      const controlPerm = this.getSum(formData.controlPermList)
      const diskType = this.getSum(formData.diskTypeList)
      const existList = JSON.parse(JSON.stringify(this.rowDatas))
      for (const row of existList) {
        // 控制对象相同（进程组，路径组，后缀组相同），优先级相同，但是控制类型不同，此时终端无法区分要用什么控制类型，所以提示用户:不允许配置控制对象相同，优先级相同，但是控制类型不同的冲突配置
        if (row.processGroupId == processGroupId && row.pathGroupId == pathGroupId &&
          row.suffixGroupId == suffixGroupId && row.level == level && row.controlType != controlType) {
          if (row.id !== id) {
            return 'conflictConfig'
          }
        }
        // 是否存在相同配置
        if (row.processGroupId == processGroupId && row.pathGroupId == pathGroupId &&
          row.suffixGroupId == suffixGroupId && row.level == level && row.controlType == controlType &&
          this.getSum(row.controlPermList) == controlPerm && this.getSum(row.diskTypeList) == diskType) {
          if (row.id !== id) {
            return 'sameConfig'
          }
        }
      }
      return ''
    }
  }
}
</script>

<style lang='scss' scoped>
.el-dialog__body .el-form .last-form-item {
  margin-bottom: 0;
}
</style>
