<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend :scope="1"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <file-permission-control-dlg
      ref="stgDlg"
      :formable="formable"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      :process-group-options="processGroupOptions"
      :path-group-options="pathGroupOptions"
      :suffix-group-options="suffixGroupOptions"
      :control-type-options="controlTypeOptions"
      :level-options="levelOptions"
      :control-perm-options="controlPermOptions"
      :disk-type-options="diskTypeOptions"
      @submitEnd="submitEnd"
    />
  </div>
</template>

<script>
import { getStrategyPage, deleteStrategy } from '@/api/dataEncryption/encryption/filePermissionControl'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { osTypeIconFormatter, stgActiveIconFormatter } from '@/utils/formatter'
import { exportStg } from '@/api/stgCommon'
import FilePermissionControlDlg from './editDlg'
import { listAllGroup } from '@/api/system/baseData/filePermissionControlLib'

export default {
  name: 'FilePermissionControl',
  components: { FilePermissionControlDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '300', formatter: this.strategyFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {},
      processGroupOptions: [],
      pathGroupOptions: [],
      suffixGroupOptions: [],
      controlTypeOptions: [
        { value: 0, label: this.$t('pages.allow') },
        { value: 1, label: this.$t('pages.forbid') }
      ],
      levelOptions: [
        { value: 2, label: this.$t('text.high') },
        { value: 1, label: this.$t('text.middle') },
        { value: 0, label: this.$t('text.low') }
      ],
      controlPermOptions: [
        { value: 1, label: this.$t('pages.read1') },
        { value: 2, label: this.$t('pages.write1') },
        { value: 4, label: this.$t('button.delete') }
      ],
      diskTypeOptions: [
        { value: 1, label: this.$t('pages.localDisk') },
        { value: 2, label: this.$t('pages.shareNetworkDisk') },
        { value: 4, label: this.$t('pages.networkDisk2') },
        { value: 8, label: this.$t('pages.cd2') }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.loadGroupData()
  },
  activated() {
    this.loadGroupData()
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    loadGroupData() {
      listAllGroup().then(resp => {
        this.processGroupOptions = []
        this.pathGroupOptions = []
        this.suffixGroupOptions = []
        const groupList = resp.data
        groupList.forEach(item => {
          if (item.type === 0) {
            this.processGroupOptions.push({ value: item.id, label: item.name })
          } else if (item.type === 1) {
            this.pathGroupOptions.push({ value: item.id, label: item.name })
          } else if (item.type === 2) {
            this.suffixGroupOptions.push({ value: item.id, label: item.name })
          }
        })
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: 283 })
    },
    importSuccess() {
      this.handleFilter()
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const configs = []
      const configList = row.configList
      for (const config of configList) {
        const processGroupName = this.processGroupIdFormatter(config.processGroupId)
        const pathGroupName = this.pathGroupIdFormatter(config.pathGroupId)
        const suffixGroupName = this.suffixGroupIdFormatter(config.suffixGroupId)
        const controlTypeText = this.controlTypeFormatter(config.controlType)
        const levelText = this.levelFormatter(config.level)
        const controlPermText = this.controlPermFormatter(config.controlPerm)
        const diskTypeText = this.diskTypeFormatter(config.diskType)
        const item = '{ ' + this.$t('table.processGroup') + '：' + processGroupName + '，' + this.$t('table.pathGroup') + '：' + pathGroupName + '，' +
          this.$t('table.suffixGroup') + '：' + suffixGroupName + '，' + this.$t('pages.controlType') + '：' + controlTypeText + '，' +
          this.$t('table.priority') + '：' + levelText + '，' + this.$t('table.controlPerm') + '：' + controlPermText + '，' +
          this.$t('table.driverType') + '：' + diskTypeText + ' }'
        configs.push(item)
      }
      return configs.join('，')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    processGroupIdFormatter(data) {
      for (const group of this.processGroupOptions) {
        if (group.value == data) {
          return group.label
        }
      }
    },
    pathGroupIdFormatter(data) {
      for (const group of this.pathGroupOptions) {
        if (group.value == data) {
          return group.label
        }
      }
    },
    suffixGroupIdFormatter(data) {
      for (const group of this.suffixGroupOptions) {
        if (group.value == data) {
          return group.label
        }
      }
    },
    controlTypeFormatter(data) {
      for (const controlOpt of this.controlTypeOptions) {
        if (controlOpt.value == data) {
          return controlOpt.label
        }
      }
    },
    levelFormatter(data) {
      for (const levelOpt of this.levelOptions) {
        if (levelOpt.value == data) {
          return levelOpt.label
        }
      }
    },
    controlPermFormatter(data) {
      const controlPermList = this.numToList(data, 3)
      const sortData = controlPermList.sort((a, b) => a - b);
      const resList = []
      for (const ctrlPermVal of sortData) {
        for (const controlPerm of this.controlPermOptions) {
          if (controlPerm.value == ctrlPermVal) {
            resList.push(controlPerm.label)
          }
        }
      }
      return resList.join('、')
    },
    diskTypeFormatter(data) {
      const diskTypeList = this.numToList(data, 4)
      const sortData = diskTypeList.sort((a, b) => a - b);
      const resList = []
      for (const diskTypeVal of sortData) {
        for (const diskOpt of this.diskTypeOptions) {
          if (diskOpt.value == diskTypeVal) {
            resList.push(diskOpt.label)
          }
        }
      }
      return resList.join('、')
    }
  }
}
</script>
