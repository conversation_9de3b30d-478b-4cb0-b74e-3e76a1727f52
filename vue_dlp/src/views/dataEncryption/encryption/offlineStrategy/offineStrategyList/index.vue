<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgFileName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <app-select-dlg ref="processSelectDlg" @select="processDataTable"/>

  </div>
</template>

<script>
import {
  getCloudOfflineStrategyFile,
  deleteCloudOfflineStrategyFile,
  downloadFile
} from '@/api/dataEncryption/encryption/offlineStrategy'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter } from '@/utils/formatter'
// import { timeInfoFormatter } from '@/utils/formatter'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'

export default {
  name: 'OfflineStrategyList',
  components: { AppSelectDlg },
  props: {
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      visible: false,
      colModel: [
        { prop: 'strategyName', label: 'offlineStgFileName', width: '150', iconFormatter: stgActiveIconFormatter },
        // { prop: 'groupId', label: 'group', width: '100', formatter: this.groupFormatter },
        { prop: 'termId', label: 'terminal', width: '100', formatter: this.termFormatter },
        { prop: 'needPwd', label: 'needPwd', formatter: this.yesNoFormat, width: '100' },
        { prop: 'needAllEnc', label: 'needAllEnc', formatter: this.yesNoFormat, width: '100' },
        { prop: 'startTime', label: 'effectTime', width: '100' },
        { prop: 'endTime', label: 'endEffectTime', width: '100' },
        { prop: 'createTime', label: 'createTime', width: '100' },
        /* { prop: 'timeId', label: 'effectTime', width: '100', formatter: timeInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'remark', label: 'remark', width: '100' },*/
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'download', click: this.handleDownload
          }, {
            label: 'delete', click: this.handleDelete
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      activeTab: 'Windows'
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.clearCheckProcess()
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable,
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getCloudOfflineStrategyFile(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.selectedNodeData = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.selectedNodeData = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      // this.gridTable.execRowDataApi(this.query)

      //  现仅支持选中终端查询
      if (this.query.objectType == 1) {
        this.gridTable.execRowDataApi(this.query)
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },

    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },

    handleDownload(row) {
      downloadFile({ id: row.id, fileName: row.fileName || '' })
    },
    handleDelete(row) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteCloudOfflineStrategyFile(row.id).then(res => {
          this.$message({
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          this.handleFilter()
        })
      }).catch(() => {})
    },

    processDataTable(datas) {
      if (!datas || datas.length === 0) return
      const list = this.temp.fileFilterApp
      datas.forEach(item => {
        const theIndex = list.findIndex(existApp => {
          return existApp === item.processName
        })
        if (theIndex > -1) {
          list.splice(theIndex, 1)
        }
        list.unshift(item.processName)
      })
    },
    yesNoFormat(row, data) {
      if (data == 0) { return this.$t('text.no') }
      if (data == 1) { return this.$t('text.yes') }
      return this.$t('pages.unknown')
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    groupFormatter(row) {
      const node = this.$refs['strategyTargetTree'].findNodeUndifferentiatedString(3, row.groupId)
      return node ? node.label : row.groupId ? '分组Id =>' + row.groupId : '';
    },
    termFormatter(row) {
      let termNodes = this.$store.getters.termNodes || {}
      termNodes = termNodes[1] || []
      for (let i = 0; i < termNodes.length; i++) {
        if (termNodes[i].dataId == row.termId) {
          return termNodes[i].label;
        }
      }
      return row.termId ? '终端Id =>' + row.termId : '';
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      this.handleFilter()
    },

    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    }
  }
}
</script>
