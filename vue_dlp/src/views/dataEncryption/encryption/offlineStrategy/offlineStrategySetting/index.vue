<template>
  <div class="app-container">
    <div class="tree-container">
      <strategy-target-tree
        ref="strategyTargetTree"
        :showed-tree="['terminal']"
        :terminal-filter-key="terminalFilter"
        refresh-offline-terminal-status
        @data-change="strategyTargetNodeChange"
      />
    </div>
    <div class="table-container" style="overflow: auto;">
      <Form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        :hide-required-asterisk="true"
        style="width: 700px;"
        label-position="left"
        label-width="100px"
      >
        <label style="padding-top: 10px">{{ $t('pages.termSource') }}<span :class="{ red: query.objectName=='' }">{{ query.objectName==''?$t('pages.selectTerm'):query.objectName }}</span></label>
        <el-card v-if="!isOTerm" class="box-card">
          <div slot="header">
            <span>{{ $t('pages.termValidity') }}</span>
            <span v-if="maxOfflineDay" style="font-size: 14px;">( {{ $t('pages.configDayRange', { min: 0, max: maxOfflineDay }) }} )</span>
          </div>
          <FormItem :label="$t('pages.startTime')">
            <el-date-picker
              v-model="temp.beginTime"
              style="width: 60%"
              :clearable="false"
              :editable="false"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              :picker-options="pickerOptionsStart"
              :placeholder="$t('pages.selectDateTime')"
              @change="startLessthanEnd2"
            ></el-date-picker>
          </FormItem>
          <FormItem :label="$t('pages.endTime')">
            <el-date-picker
              v-model="temp.endTime"
              style="width: 60%"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              :clearable="false"
              :editable="false"
              type="datetime"
              :picker-options="pickerOptionsEnd"
              :placeholder="$t('pages.selectDateTime')"
              @change="startLessthanEnd1"
            ></el-date-picker>
          </FormItem>
        </el-card>
        <el-card v-if="!isUTerm" class="box-card">
          <div slot="header">
            <span>{{ $t('pages.protect') }}</span>
          </div>
          <el-checkbox v-model="temp.isJsonEncry" :true-label="1" :false-label="0">
            {{ $t('pages.enterPwd') }}
            <el-tooltip class="item" effect="dark" placement="bottom-start" :content="$t('pages.passwordNotInputSpace')">
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
          <FormItem v-if="temp.isJsonEncry==1" :label="$t('form.password')" prop="password">
            <el-input v-model="temp.password" :disabled="temp.isJsonEncry==0" type="password" show-password :maxlength="20" style="width: 60%" @input="handlePasswordInput"></el-input>
          </FormItem>
          <FormItem v-if="temp.isJsonEncry==1" :label="$t('form.confirmPassword')" prop="mainKey2">
            <el-input v-model="temp.mainKey2" :disabled="temp.isJsonEncry==0" type="password" show-password style="width: 60%" @input="handleConfirmPasswordInput"></el-input>
          </FormItem>
        </el-card>
        <el-card class="box-card">
          <div slot="header">
            <span>{{ $t('pages.stgSource') }}</span>
          </div>
          <el-radio v-model="temp.strategySource" :label="1"> {{ $t('pages.curStg') }}</el-radio>
          <br>
          <el-checkbox v-show="temp.strategySource==1" v-model="temp.includeOperator" :true-label="1" :false-label="0" style="margin-top: 20px;" >{{ $t('pages.additionalUserStg') }}</el-checkbox>
          <FormItem v-show="temp.includeOperator==1 && temp.strategySource==1" :label="$t('pages.selectUser')" prop="operatorIds">
            <tree-select
              ref="userTreeSelect"
              node-key="id"
              :width="310"
              :loading="dataLoading"
              :data="userTreeData"
              is-filter
              multiple
              collapse-tags
              check-strictly
              :placeholder="$t('pages.selectStgUser')"
              style="width: 60%"
              @clickSelect="clickSelect"
              @change="userTreeNodeCheckChange"
            ></tree-select>
          </FormItem>
          <br>
          <el-tooltip class="item" effect="dark" :content="$t('pages.customStgText')" placement="right">
            <el-checkbox v-model="temp.isAppendCustom" :true-label="1" :false-label="0" style="margin-top: 20px;">{{ $t('pages.additionalCustomStg') }}</el-checkbox>
          </el-tooltip>
          <div v-if="temp.isAppendCustom==1" class="toolbar">
            <Form
              ref="itemForm"
              :rules="itemRule"
              :model="itemTemp"
              label-position="right"
              label-width="100px"
              :hide-required-asterisk="true"
            >
              <el-row >
                <el-col :span="12">
                  <FormItem :label="$t('pages.objType')" prop="objectType">
                    <el-select v-model="itemTemp.objectType" style="width: 60%" @change="changeObjectType">
                      <el-option :label="$t('pages.terminal')" :value="1"></el-option>
                      <el-option :label="$t('pages.user')" :value="2"></el-option>
                    </el-select>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('pages.applicationObj')" prop="objectIds">
                    <tree-select
                      v-if="itemTemp.objectType==2"
                      ref="userTreeSelectItem"
                      node-key="id"
                      :data="userTreeData"
                      :checked-keys="checkedIds"
                      is-filter
                      multiple
                      collapse-tags
                      check-strictly
                      :placeholder="$t('pages.selectUser')"
                      @clickSelect="clickSelect"
                      @change="objectIdsChange"
                    ></tree-select>
                    <el-input v-if="itemTemp.objectType==1" v-model="query.objectName" disabled :placeholder="$t('pages.selectTerm')"></el-input>
                    <!-- <span v-if="itemTemp.objectType==1">{{ query.objectName==''?$t('pages.selectTerm'):query.objectName }}</span> -->
                  </FormItem>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <FormItem :label="$t('pages.selectStgGroup')" prop="packageId">
                    <el-select v-model="itemTemp.packageId" style="width: 60%; float: left" @change="packageIdChange">
                      <el-option v-for="item in showPacketList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                    &nbsp;<el-button size="mini" @click="() => { this.$router.push('/terminalManage/terminalManage/allGroup') }">E</el-button>
                  </FormItem>
                </el-col>
                <el-col :span="3">
                  <FormItem label-width="0">
                    <el-button size="mini" :disabled="query.objectId == undefined" :loading="submitting" @click="add()">
                      {{ $t('button.insert') }}
                    </el-button>
                  </FormItem>
                </el-col>
              </el-row>
            </Form>
            <div class="table-container" style="margin-top: 20px;">
              <grid-table
                ref="objectPackageTable"
                :height="180"
                :multi-select="false"
                :show-pager="false"
                :row-datas="temp.targetObjects"
                :col-model="colModel"
              />
            </div>
          </div>
        </el-card>
        <!--<el-card>
          <el-checkbox v-model="temp.isEncry" :true-label="1" :false-label="0">导入离线策略后启用全盘加密</el-checkbox>
        </el-card>-->
        <el-card v-if="temp.isEncry == 1" class="box-card">
          <div slot="header">
            <el-tooltip class="item" effect="dark" :content="$t('pages.dirFilter_tip')" placement="top">
              <span>{{ $t('pages.dirFilter') }}</span>
            </el-tooltip>
          </div>
          <tag :list="temp.filterPath"/>
        </el-card>
        <el-card v-if="temp.isEncry == 1" class="box-card">
          <div slot="header">
            <el-tooltip class="item" effect="dark" :content="$t('pages.fileTypeDefine_tip')" placement="top">
              <span>{{ $t('pages.fileTypeDefine') }}</span>
            </el-tooltip>
          </div>
          <tag :list="temp.suffix"/>
        </el-card>
        <div class="save-btn-container">
          <common-downloader
            :loading="submitting"
            :disabled="query.objectId === undefined"
            :name="getFileName"
            :button-name="$t('button.export')"
            button-type="primary"
            button-size="mini"
            button-icon=""
            :before-download="beforeDownload"
            @download="createData"
          />
          <!-- 云离线策略Q4不上--暂时注释 v-if="false" -->
          <el-button v-if="false" :disabled="query.objectId === undefined" type="primary" size="mini" @click="uploadFileElg()">
            {{ $t('pages.offline_uploadToOline') }}
          </el-button>
        </div>
      </Form>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.offline_uploadFile')"
      :visible.sync="uploadFileVisible"
      width="500px"
      @close="closeUploadFile"
    >
      <Form
        ref="uploadFileForm"
        :rules="uploadRules"
        :model="uploadTemp"
        label-position="left"
        label-width="100px"
      >
        <FormItem prop="strategyName" :label="$t('pages.offline_strategyFileName')">
          <el-input v-model="uploadTemp.strategyName" style="width: 300px"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="uploadSubmitting" type="primary" @click="uploadFile()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="closeUploadFile">
          {{ $t('button.cancel') }}
        </el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import { exportOfflineStrategyFile, uploadStrategyFile, getStartTime } from '@/api/dataEncryption/encryption/offlineStrategy'
import { getRegisterInfo } from '@/api/system/register/reg'
import { getGroupByGroupType } from '@/api/behaviorManage/behaviorGroup'
import moment from 'moment'
import { listTreeNodeByTermId } from '@/api/system/terminalManage/user'
import { exportStrategy } from '@/api/system/terminalManage/uterm'
import CommonDownloader from '@/components/DownloadManager/common'
import { notSpace, visibleAsciiWithoutSpace } from '@/utils/inputLimit'
import { aesEncode, formatAesKey } from '@/utils/encrypt'
import { getPropertyValueByCode } from '@/api/property'
import { parseTime } from '@/utils'

export default {
  name: 'OfflineStrategySetting',
  components: { CommonDownloader },
  data() {
    return {
      colModel: [
        { prop: 'objectType', label: 'applicationObjType', width: '20', sort: true, formatter: (row) => { return row.objectType == 1 ? this.$t('pages.terminal') : this.$t('pages.user') } },
        { prop: 'objectNames', label: 'applicationObj', width: '20', sort: true },
        { prop: 'packageName', label: 'stgGroup', width: '20', sort: true, formatter: this.packageNameFormatter },
        { prop: 'createdTime', label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [{
            label: 'delete', click: this.handleDelete }
          ]
        }
      ],
      maxOfflineMilliseconds: null,
      maxOfflineDay: null,
      dataLoading: false,
      userTreeData: [],
      checkedIds: [],
      itemTemp: {
        objectType: 1,
        objectIds: [],
        groupIds: [],
        objectNames: '',
        packageId: null,
        packageName: '',
        options: []
      },
      // 基于操作方便，不再限制日期的选择范围
      pickerOptionsStart: {
        // disabledDate: time => {
        //   const endDateVal = this.temp.endTime
        //   if (endDateVal) {
        //     return time.getTime() > new Date(endDateVal).getTime()
        //   }
        // }
      },
      pickerOptionsEnd: {
        // disabledDate: time => {
        //   const beginDateVal = this.temp.beginTime
        //   if (beginDateVal) {
        //     return (time.getTime() < new Date(beginDateVal).getTime() - 0 * 24 * 60 * 60 * 1000)
        //   }
        // }
      },
      dialogFormVisible: false,
      packetOptions: [],
      submitting: false,
      uploadSubmitting: false,
      temp: {
        isAllTerm: 0,
        beginTime: '',
        endTime: '',
        isEncry: 0,
        isJsonEncry: 0,
        password: '',
        mainKey2: '',
        strategySource: 1,
        isAppendCustom: 0,  // 附加自定义策略
        entityId: null,
        packetId: null,
        includeOperator: 0,
        operatorIds: [],
        operatorGroupIds: [], // 附加操作员分组id
        filterPath: ['Windows', 'Program Files', 'Local Settings'],
        suffix: [],
        objectIds: [],
        objectGroupIds: [],
        targetObjects: [],   // 自定义策略包对象列表
        strategyName: '',  //  策略名称(文件前缀名称）
        objectType: null,
        objectId: null,
        objectName: '',
        type: null
      },
      rules: {
        password: [
          { required: true, message: this.$t('pages.pwdCantNull'), trigger: 'blur' },
          { min: 5, max: 20, message: this.$t('pages.pwdText'), trigger: 'blur' }
        ],
        mainKey2: [
          { validator: this.passwordRightValid, trigger: 'blur' }
        ],
        entityId: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        packetId: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        operatorIds: [
          { validator: this.operatorListValid, trigger: 'blur' }
        ]
      },
      itemRule: {
        objectIds: [
          { validator: this.objectIdsValid, trigger: 'change' }
        ],
        packageId: [
          { validator: this.packageIdValid, trigger: 'blur' }
        ]
      },
      selectedTerminalData: [],
      checkedUserKeys: [],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        // isAllTerm: 1,
        objectName: ''
      },
      isUTerm: false,
      isOTerm: false,
      exportFunc: undefined,
      uploadFileVisible: false,
      uploadRules: {
        strategyName: [
          { required: true, message: this.$t('pages.validateStgName'), trigger: 'blur' }
        ]
      },
      uploadTemp: {
        strategyName: ''
      }
    }
  },
  computed: {
    showPacketList() {
      return this.packetOptions.filter(item => {
        return this.itemTemp.objectType == item.objectType
      })
    }
  },
  watch: {
  },
  activated() {
    this.getPacketOptions()
    this.getMaxOfflineDay()
  },
  created() {
    this.exportFunc = exportOfflineStrategyFile
    this.getTime()
    this.getPacketOptions()
    this.getMaxOfflineDay()
  },
  methods: {
    getTime() {
      getStartTime().then(res => {
        this.temp.beginTime = res.data + ' 00:00:00'
        this.temp.endTime = moment(res.data).add(7, 'days').format('YYYY-MM-DD') + ' 23:59:59'
      })
    },
    getMaxOfflineDay() {
      getPropertyValueByCode('max.offline.day').then(res => {
        this.maxOfflineDay = (!res.data ? 180 : Number.parseInt(res.data))
        this.maxOfflineMilliseconds = this.maxOfflineDay * 24 * 60 * 60 * 1000
      })
    },
    handlePasswordInput(value) {
      this.temp.password = visibleAsciiWithoutSpace(value)
    },
    handleConfirmPasswordInput(value) {
      this.temp.mainKey2 = notSpace(value)
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return true
    },
    startLessthanEnd1(vm) {
      const startTime = new Date(this.temp.beginTime).getTime()
      const endTime = new Date(this.temp.endTime).getTime()
      if (startTime > endTime) {
        // 将开始时间设置成和结束时间一样
        this.temp.beginTime = this.temp.endTime
      } else if (this.maxOfflineMilliseconds !== null && endTime - startTime > this.maxOfflineMilliseconds) {
        this.temp.beginTime = parseTime(endTime - this.maxOfflineMilliseconds, 'y-m-d h:i:s')
      }
    },
    startLessthanEnd2(vm) {
      const startTime = new Date(this.temp.beginTime).getTime()
      const endTime = new Date(this.temp.endTime).getTime()
      if (startTime > endTime) {
        // 将结束时间设置成和开始时间一样
        this.temp.endTime = this.temp.beginTime
      } else if (this.maxOfflineMilliseconds !== null && endTime - startTime > this.maxOfflineMilliseconds) {
        this.temp.endTime = parseTime(startTime + this.maxOfflineMilliseconds, 'y-m-d h:i:s')
      }
    },
    changeObjectType(val) {
      this.itemTemp.packageId = null
      this.itemTemp.objectIds.splice(0)
      this.itemTemp.groupIds.splice(0)
      this.checkedIds.splice(0)
      if (val == 1 && this.query.objectId) {
        this.itemTemp.objectIds.push(this.query.objectId)
        this.itemTemp.objectNames = this.query.objectName
      }
      this.$refs['itemForm'].clearValidate('objectIds')
    },
    packageIdChange(val) {
      this.$refs.itemForm.validate()
      this.packetOptions.forEach(item => {
        if (val == item.id) {
          this.itemTemp.packageName = item.name
        }
      })
    },
    resetItemTemp() {
      this.itemTemp = {
        objectType: 1,
        objectIds: [],
        groupIds: [],
        objectNames: '',
        packageId: null,
        packageName: ''
      }
      this.checkedIds.splice(0)
      if (this.query.objectId) {
        this.itemTemp.objectIds.push(this.query.objectId)
        this.itemTemp.objectNames = this.query.objectName
      }
    },
    objectIdsChange: function(datas, options) { // options 是要显示到输入框的数组
      // 剔除不需要显示到输入框的选项，也就是分组数据
      // var i = options.length
      // while (i--) {
      //   const key = options[i].value
      //   if (key.indexOf('G') >= 0) {
      //     options.splice(i, 1)
      //     datas.splice(i, 1)
      //   }
      // }
      // 处理一下选中的值
      this.itemTemp.objectIds.splice(0)
      this.itemTemp.groupIds.splice(0)
      this.checkedIds.splice(0)
      const objectNames = []
      options.forEach(item => {
        const key = item.value
        if (key.indexOf('G') < 0) {
          this.itemTemp.objectIds.push(key.replace('U', ''))
        } else {
          this.itemTemp.groupIds.push(key.replace('G', ''))
        }
        const label = item.label
        objectNames.push(label)
        this.checkedIds.push(key)
      })
      this.itemTemp.objectNames = objectNames.join(',')
      this.itemTemp.options = options
      this.$nextTick(() => {
        // this.$refs.itemForm.validate()
      })
    },
    getExistNames() {
      // 验证添加的对象是否已经配置了策略包
      const existNames = []
      this.temp.targetObjects.forEach(item => {
        if (this.itemTemp.objectType == item.objectType) {
          if (this.itemTemp.objectType == 1) {
            // 如果是终端就直接可以判断已存在了，因为终端就只有一个
            existNames.push(this.itemTemp.objectNames)
          } else {
            // 如果是操作员的话，要判断options里面是否包含了
            this.itemTemp.options.forEach(option => {
              item.objectIds.forEach(objectId => {
                if (objectId == (option.value.replace('U', ''))) {
                  existNames.push(option.label)
                }
              })
              item.groupIds.forEach(groupId => {
                if (groupId == (option.value.replace('G', ''))) {
                  existNames.push(option.label)
                }
              })
            })
          }
        }
      })
      return existNames
    },
    add() {
      this.submitting = true
      this.$refs['itemForm'].validate((valid) => {
        if (valid) {
          const names = this.getExistNames()
          if (names.length > 0) {
            this.$message({
              message: `${this.$t('pages.object')}${names.join('、')}${this.$t('pages.stgGroupText')}`,
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          this.itemTemp.id = new Date().getTime()
          this.itemTemp.options = undefined
          this.temp.targetObjects.unshift(this.itemTemp)
          this.resetItemTemp()
          this.submitting = false
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete(row) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        var i = this.temp.targetObjects.length
        while (i--) {
          if (this.temp.targetObjects[i].id == row.id) {
            this.temp.targetObjects.splice(i, 1)
          }
        }
      }).catch(() => {})
    },
    getFileName() {
      const title = this.$t('route.offlineStrategy')
      const date = moment().format('YYYYMMDD')
      if (this.isUTerm) {
        return `LdUTerm_${this.query.objectName}_${date}.luf`
      }
      return `${title}_${this.query.objectName}_${date}.lof`
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      const termType = parseInt(checkedNode.dataType) & 0xf0
      const isUTerm = termType === 32
      if (isUTerm && !checkedNode.dataCode) {
        this.$message({ duration: 2000, message: this.$t('pages.termTypeNotSupport') })
        return
      }
      this.isUTerm = isUTerm
      this.isOTerm = termType === 128
      this.exportFunc = this.isUTerm ? exportStrategy : exportOfflineStrategyFile
      // 清空自定义策略列表
      this.temp.targetObjects.splice(0)
      // 取消之前选的操作员
      this.$refs.userTreeSelect.clearSelectedNode()
      this.temp.operatorIds = []
      this.temp.operatorGroupIds = []
      if (checkedNode && checkedNode.type == 1) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
        this.query.objectName = checkedNode.label
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.query.objectName = ''
      }
      this.temp.type = checkedNode.type || null

      // 重新刷新用户选择下拉框的数据
      this.userTreeData.splice(0)
      this.resetItemTemp()
      this.$refs.itemForm && this.$refs.itemForm.clearValidate()
      this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
    },
    getPacketOptions() {
      getGroupByGroupType(0).then(res => {
        this.packetOptions = res.data
      })
    },
    passwordRightValid(rule, value, callback) {
      if (value != this.temp.password) {
        callback(new Error(this.$t('pages.validateMsg_diffPassword')))
      } else {
        callback()
      }
    },
    operatorListValid(rule, value, callback) {
      if ((value.length == 0 && this.temp.operatorGroupIds.length == 0) && this.temp.includeOperator == 1) {
        callback(new Error(this.$t('components.required')))
      } else {
        callback()
      }
    },
    objectIdsValid(rule, value, callback) {
      this.$nextTick(() => {
        if ((this.itemTemp.objectIds && this.itemTemp.objectIds.length > 0) || (this.itemTemp.groupIds && this.itemTemp.groupIds.length > 0)) {
          callback()
        } else {
          callback(this.$t('components.required'))
        }
      })
    },
    packageIdValid(rule, value, callback) {
      if (this.itemTemp.packageId) {
        callback()
      } else {
        callback(this.$t('components.required'))
      }
    },
    clickSelect() {
      if (!this.query.objectName) return
      if (this.userTreeData.length == 0) {
        this.dataLoading = true
        listTreeNodeByTermId({ termId: this.query.objectId }).then(respond => {
          this.dataLoading = false
          const nodes = respond.data || []
          if (nodes.length > 0) {
            this.userTreeData = nodes
          } else {
            this.$message({
              message: this.$t('pages.offLineStrategyMsg1') + '',
              type: 'warning',
              duration: 2000
            })
          }
        }).catch(() => {
          this.dataLoading = false
        })
      }
    },
    userTreeNodeCheckChange(data, options) {
      this.temp.operatorIds.splice(0)
      this.temp.operatorGroupIds.splice(0)
      data.forEach(item => {
        if (item.indexOf('G') < 0) {
          this.temp.operatorIds.push(item.replace('U', ''))
        } else {
          this.temp.operatorGroupIds.push(item.replace('G', ''))
        }
      })
      this.$refs['dataForm'].validateField('operatorIds')
    },
    beforeDownload() {
      if (this.temp.beginTime === this.temp.endTime) {
        this.$message({
          message: this.$t('pages.offline_message2'),
          type: 'error',
          duration: 2000
        })
        return Promise.reject()
      }
      if (this.temp.isAppendCustom == 1 && this.temp.targetObjects.length == 0) {
        this.$message({
          message: this.$t('pages.offline_message3'),
          type: 'error',
          duration: 2000
        })
        return Promise.reject()
      }
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate(valid => {
          if (valid && this.formatSubmitData()) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    createData(file) {
      this.submitting = true
      const data = JSON.parse(JSON.stringify(this.temp))
      // 加密密码
      if (data.password) {
        data.password = aesEncode(data.password, formatAesKey('tr838408', ''))
        delete data['mainKey2']
      }
      if (this.isOTerm) {
        data.beginTime = '2023-02-10 00:00:00'
        data.endTime = '2999-12-31 23:59:59'
      }
      const opts = { file, jwt: true, topic: this.$route.name }
      this.exportFunc(data, opts).then(res => {
        this.submitting = false
      }).catch(() => {
        this.submitting = false
      })
    },
    uploadFileElg() {
      if (this.temp.beginTime === this.temp.endTime) {
        this.$message({
          message: this.$t('pages.offline_message1'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.uploadFileVisible = true
        }
      })
    },
    uploadFile() {
      this.uploadSubmitting = true
      this.$refs['uploadFileForm'].validate((valid) => {
        if (valid) {
          if (!this.formatSubmitData()) {
            this.uploadSubmitting = false
            return
          }
          const data = { ...this.temp }
          if (this.isOTerm) {
            data.beginTime = '2023-02-10 00:00:00'
            data.endTime = '2999-12-31 23:59:59'
          }
          uploadStrategyFile(data).then(res => {
            this.$message({
              message: this.$t('text.uploadSuccess'),
              type: 'success',
              duration: 2000
            })
            this.uploadSubmitting = false
            this.uploadFileVisible = false
          }).catch(() => {
            this.uploadSubmitting = false
          })
        } else {
          this.uploadSubmitting = false
        }
      })
      // this.uploadSubmitting = true
      // if (this.temp.beginTime === this.temp.endTime) {
      //   this.$message({
      //     message: '结束时间和开始时间相同，策略无法上传！',
      //     type: 'error',
      //     duration: 2000
      //   })
      //   this.uploadSubmitting = false
      //   return
      // }
      // this.$refs['dataForm'].validate((valid) => {
      //   if (valid) {
      //     if (!this.formatSubmitData()) {
      //       this.uploadSubmitting = false
      //       return
      //     }
      //     this.temp.objectType = this.query.objectType
      //     this.temp.objectId = this.query.objectId
      //     this.temp.objectName = this.query.objectName
      //     uploadStrategyFile(this.temp).then(res => {
      //       this.$message({
      //         message: this.$t('text.uploadSuccess'),
      //         type: 'success',
      //         duration: 2000
      //       })
      //       this.uploadSubmitting = false
      //     }).catch(() => {
      //       this.uploadSubmitting = false
      //     })
      //   } else {
      //     this.uploadSubmitting = false
      //   }
      // })
    },
    // 刷新注册信息
    reloadRegisterInfo: function(data) {
      getRegisterInfo().then(response => {
        this.$message({
          message: this.$t('pages.refreshInfoText'),
          type: 'success',
          duration: 2000
        })
      })
    },
    formatSubmitData() { // 格式化数据
      if (this.temp.strategySource == 2 && this.temp.targetObjects.length == 0) {
        this.$message({
          message: this.$t('pages.customStgRequire'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      /* if (this.temp.isAllTerm == 0) {
        if (this.selectedTerminalData.length == 0) {
          this.$message({
            message: '请选择绑定终端',
            type: 'error',
            duration: 2000
          })
          return false
        }
        this.selectedTerminalData.forEach(nodeData => {
          if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
            this.temp.objectIds.push(nodeData.dataId)
          } else {
            this.temp.objectGroupIds.push(nodeData.dataId)
          }
        })
      }*/
      // 指定分组（qrh）
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      this.temp.objectId = this.query.objectId
      this.temp.objectType = this.query.objectType
      this.temp.objectName = this.query.objectName
      this.temp.entityId = this.query.objectId
      if (this.query.objectType == 3) {
        this.temp.objectGroupIds.push(this.query.objectId)
      } else if (this.query.objectType == 1) {
        this.temp.objectIds.push(this.query.objectId)
      }
      if (this.isUTerm) {
        this.temp.termId = this.query.objectId
      }
      this.temp.strategyName = this.uploadTemp.strategyName
      return true
    },
    packageNameFormatter(row, data) {
      let packName = ''
      this.packetOptions.forEach(item => {
        if (row.packageId == item.id) {
          packName = item.name
        }
      })
      return packName
    },
    closeUploadFile() {
      this.uploadFileVisible = false
      this.uploadTemp.strategyName = ''
      this.$nextTick(() => {
        this.$refs['uploadFileForm'] && this.$refs['uploadFileForm'].clearValidate()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .app-container{
    overflow: auto;
    padding: 5px 0 20px 0;
  }
  .el-tab-pane .app-container {
    padding: 5px 5px 15px 10px;
  }
  .el-card {
    margin-top: 10px;
    background-color: transparent;
  }
  >>>.el-card__body {
    padding: 10px 40px;
  }
  >>>.el-form-item{
    margin: 10px 0 0;
    &.is-error{
      margin-bottom: 15px;
    }
  }
  >>>.el-form-item__label{
    line-height: 30px;
    color: #ccc;
  }
  >>>.el-row, >>>.el-form-item__content{
    line-height: 30px;
  }
  .save-btn-container{
    width: 700px;
    margin-top: 10px;
    text-align: right;
  }
  .red{
    color: red;
  }
</style>
