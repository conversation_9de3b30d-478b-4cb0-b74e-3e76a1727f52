<template>
  <div class="app-container">
    <div class="tree-container">
      <div class="tree-container" :class="showTree?'':'hidden'">
        <strategy-target-tree
          ref="strategyTargetTree"
          :showed-tree="['terminal']"
          :terminal-filter-key="terminalFilter"
          @data-change="strategyTargetNodeChange"
        />
      </div>
    </div>
    <div class="table-container" style="overflow: auto;">
      <div class="toolbar">
        <!-- <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button> -->
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">{{ $t('button.extendOfflineStrategyExpiration') }}</el-button>
        <el-button type="primary" icon="el-icon-delete" size="mini" @click="handleDelete">{{ $t('button.clearExpired') }}</el-button>
        <div class="searchCon">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleResetAndQuery">
            {{ $t('table.resetAndQuery') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyExpirationTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
      />
    </div>

    <add-offline-strategy-expiration ref="add" @add="refresh"/>
  </div>
</template>
<script>

import { getPage, deleteExpired } from '@/api/dataEncryption/encryption/offlineStrategyExpiration';
import AddOfflineStrategyExpiration from './add'
export default {
  name: 'OfflineStrategyExpiration',
  components: {
    AddOfflineStrategyExpiration
  },
  data() {
    return {
      colModel: [
        { prop: 'id', label: 'version', width: '150' },
        { prop: 'bizName', label: 'termIdOrGroupName', width: '200' },
        { prop: 'type', label: 'type', width: '150', formatter: this.typeFormatter },
        { prop: 'expirationTime', label: 'expireTo', width: '200' },
        { prop: 'registrationTime', label: 'createTime', width: '200' }
      ],
      query: {
        page: 1,
        limit: 15
      },
      showTree: true
    }
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    typeFormatter(row) {
      return row.type === 1 ? '分组' : '终端'
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleResetAndQuery() {
      this.query.page = 1
      this.query.type = undefined
      this.query.bizId = undefined
      this.$refs['strategyExpirationTable'].execRowDataApi(this.query)
    },
    handleAdd() {
      this.$refs.add.show()
    },
    handleDelete() {
      this.$confirmBox(this.$t('text.clearExpiredConfirm'), this.$t('text.prompt')).then(() => {
        deleteExpired().then(res => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.clearSuccess'),
            type: 'success',
            duration: 5000
          })
          this.refresh()
        })
      })
    },
    terminalFilter(node) {
      if (node.type == 3 && node.id == 'G-2') {
        return false
      }
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !!node.dataCode
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      // 将type对标云平台的类型字段
      // DLP本地 1-终端，3-分组
      // 云平台 1-分组，2-终端
      if (checkedNode.type === '1') {
        this.query.type = 2
      } else if (checkedNode.type === '3') {
        this.query.type = 1
      }
      this.query.bizId = checkedNode.dataId
      this.$refs['strategyExpirationTable'].execRowDataApi(this.query)
    },
    refresh() {
      this.query.page = 1
      this.query.type = undefined
      this.query.bizId = undefined
      this.$refs['strategyExpirationTable'].execRowDataApi(this.query)
    }
  }
}

</script>
