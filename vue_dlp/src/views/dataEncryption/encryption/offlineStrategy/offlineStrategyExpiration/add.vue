<template>
  <el-dialog
    v-el-drag-dialog
    :title="i18nConcatText(this.$t('pages.offlineStrategyExpiration'), 'create')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="visible"
    width="550px"
  >
    <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="105px" style="width: 480px;">
      <FormItem :label="$t('pages.offlineStrategyEndTime')">
        <el-date-picker
          v-model="temp.endTime"
          style="width: 60%"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          default-time="23:59:59"
          :picker-options="{ disabledDate }"
          :clearable="false"
          :editable="false"
          type="datetime"
          :placeholder="$t('pages.selectDateTime')"
        ></el-date-picker>
      </FormItem>
      <FormItem :label="$t('pages.terminalOrGroup')" prop="bizId">
        <tree-select
          ref="objectTree"
          node-key="id"
          :height="350"
          :width="468"
          multiple
          check-strictly
          is-filter
          :local-search="false"
          leaf-key="terminal"
          @change="checkedIdChange"
        />
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="handleAdd">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { register } from '@/api/dataEncryption/encryption/offlineStrategyExpiration';

export default {
  name: 'AddOfflineStrategyExpiration',
  props: {
  },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {
        endTime: undefined,
        objs: []
      },
      defaultTemp: {
        endTime: undefined,
        objs: []
      },
      rules: {
        endTime: [{ required: true, trigger: 'change', message: this.$t('text.cantNullInfo', { info: this.$t('pages.offlineStrategyExpiration') }) }]
      }
    }
  },
  methods: {
    show() {
      Object.assign(this.temp, this.defaultTemp, {})
      this.visible = true
      if (this.$refs.objectTree) {
        this.$refs.objectTree.clearSelectedNode()
      }
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    terminalFilter(node) {
      if (node.type == 3 && node.id == 'G-2') {
        return false
      }
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !!node.dataCode
    },
    disabledDate(time) {
      return time.getTime() < Date.now() - 8.64e7
    },
    checkedIdChange(selectedKey, nodes) {
      this.temp.objs = []
      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i]
        const obj = {}
        obj.bizId = node.dataId
        if (node.type === '1') {
          obj.type = 2
        } else if (node.type === '3') {
          obj.type = 1
        }
        obj.bizName = node.label
        this.temp.objs.push(obj)
      }
    },
    handleAdd() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.submitting = true
          const data = this.temp
          register(data).then(res => {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.addOfflineStrategyExpirationSuccess'),
              type: 'success',
              duration: 5000
            })
            this.submitting = false
            this.$emit('add', {})
            this.visible = false
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    }
  }
}
</script>
