<template>
  <div class="app-container">
    <div class="tree-container">
      <strategy-target-tree
        ref="strategyTargetTree"
        :showed-tree="['terminal']"
        :terminal-filter-key="terminalFilter"
        refresh-offline-terminal-status
        @data-change="strategyTargetNodeChange"
      />
    </div>
    <div class="table-container" style="overflow: auto;">
      <!-- 搜索条件区域 -->
      <div class="search-container">
        <div class="search-filters">
          <div v-if="selectedtarget" class="filter-item">
            <label>生效目标：</label>
            <div class="target-display">
              <el-tag
                closable
                @close="clearTarget"
              >
                <svg-icon :icon-class="selectedTargetIcon" style="margin-right: 5px;"/>
                {{ selectedTargetName }}
              </el-tag>
            </div>
          </div>
          <div class="filter-item">
            <label>类型：</label>
            <el-select v-model="query.targetType" placeholder="全部" size="mini" style="width: 120px;">
              <el-option label="全部" value=""></el-option>
              <el-option label="终端" value="2"></el-option>
              <el-option label="分组" value="1"></el-option>
            </el-select>
          </div>
          <div class="filter-item">
            <label>状态：</label>
            <el-select v-model="query.status" placeholder="全部" size="mini" style="width: 150px;">
              <el-option label="全部" value=""></el-option>
              <el-option label="未同步至云服务" value="unsynced"></el-option>
              <el-option label="还有x天过期" value="expiringByXDays"></el-option>
              <el-option label="即将过期" value="expiring"></el-option>
              <el-option label="已过期" value="expired"></el-option>
            </el-select>
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                <div>• 未同步至云服务：离线策略延长配置暂未同步至云服务</div>
                <div>• 还有x天过期：距离过期还有x天（x>3天）</div>
                <div>• 即将过期：距离过期还有1-3天</div>
                <div>• 已过期：离线策略已超过过期时间</div>
              </div>
              <i class="el-icon-info" style="margin-left: 5px; color: #909399; cursor: help;"></i>
            </el-tooltip>
          </div>
          <div class="filter-item">
            <label>同步状态：</label>
            <el-select v-model="query.syncStatus" placeholder="全部" size="mini" style="width: 100px;">
              <el-option label="全部" value=""></el-option>
              <el-option label="已同步" value="1"></el-option>
              <el-option label="未同步" value="0"></el-option>
            </el-select>
          </div>
          <el-button size="mini" @click="clearFilters">{{ $t('button.reset') }}</el-button>
          <el-button type="primary" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>

      <!-- 表格工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">
            新增延长配置
          </el-button>
          <el-button
            v-if="selectedRows.length > 0"
            type="danger"
            icon="el-icon-delete"
            size="mini"
            @click="handleBatchDelete"
          >
            批量删除({{ selectedRows.length }})
          </el-button>
          <el-button
            v-if="selectedRows.length > 0"
            type="warning"
            icon="el-icon-upload2"
            size="mini"
            @click="handleBatchSync"
          >
            同步至云服务({{ selectedRows.length }})
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-popover
            placement="bottom"
            width="550"
            trigger="hover"
            popper-class="quota-usage-popover"
          >
            <div>
              <div style="margin-bottom: 8px; font-size: 13px; font-weight: 600; color: #303133;">
                配额使用详情 ({{ quotaInfo.used }}/{{ quotaInfo.total }})
              </div>
              <el-table
                :data="quotaConfigList"
                size="mini"
                style="width: 100%;"
                max-height="250"
              >
                <el-table-column prop="target" label="生效目标" min-width="100">
                  <template slot-scope="scope">
                    {{ getTargetDisplayName(scope.row.target, scope.row.targetType) }}
                  </template>
                </el-table-column>
                <el-table-column prop="targetType" label="类型" width="70">
                  <template slot-scope="scope">
                    {{ getTypeDisplayName(scope.row.targetType) }}
                  </template>
                </el-table-column>
                <el-table-column prop="effectiveTime" label="生效时间" width="140">
                  <template slot-scope="scope">
                    {{ scope.row.effectiveTime }}
                  </template>
                </el-table-column>
                <el-table-column prop="expireTime" label="过期时间" width="140">
                  <template slot-scope="scope">
                    {{ scope.row.expireTime }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="50" align="center">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      style="color: #f56c6c;"
                      @click="handleDeleteQuotaConfig(scope.row)"
                    />
                  </template>
                </el-table-column>

                <template slot="empty">
                  <div style="padding: 20px; color: #909399; font-size: 12px;">
                    <i class="el-icon-info" style="font-size: 20px; display: block; margin-bottom: 4px;"></i>
                    暂无配额使用记录
                  </div>
                </template>
              </el-table>
            </div>
            <div slot="reference" class="quota-info">
              <span>配额使用：</span>
              <span class="quota-used">{{ quotaInfo.used || 0 }}</span>
              <span>/</span>
              <span class="quota-total">{{ quotaInfo.total || 10 }}</span>
              <el-progress
                :percentage="quotaPercentage"
                :stroke-width="6"
                :show-text="false"
                style="width: 100px; margin-left: 10px;"
              />
            </div>
          </el-popover>
        </div>
      </div>

      <!-- 数据表格 -->
      <grid-table
        ref="dataTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectableFunction"
        :multi-select="true"
        @selectionChangeEnd="handleSelectionChange"
      >

      </grid-table>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :modal="true"
      :lock-scroll="false"
      append-to-body
      custom-class="extension-dialog"
      @close="handleDialogClose"
    >
      <Form
        ref="editForm"
        :model="editForm"
        :rules="editRules"
        label-width="0px"
        class="extension-form"
      >
        <!-- 生效目标 -->
        <div class="protection-wrapper">
          <div class="protection-section">
            <div class="section-header">
              <div class="section-title">
                <span class="title-text">生效目标</span>
              </div>
            </div>

            <div class="section-content">
              <FormItem label="" prop="target" class="target-form-item">
                <tree-select
                  ref="targetTreeSelect"
                  v-model="editForm.target"
                  :get-search-list="getSearchList"
                  node-key="dataId"
                  leaf-key="terminal"
                  placeholder="请选择终端或分组"
                  :height="400"
                  :checked-keys="targetCheckedKeys"
                  :disabled="!!editForm.id"
                  class="target-tree-select"
                  @change="handleTargetSelectChange"
                />
              </FormItem>
            </div>
          </div>
        </div>

        <!-- 时间设置 -->
        <div class="protection-wrapper">
          <div class="protection-section">
            <div class="section-header">
              <div class="section-title">
                <span class="title-text">策略过期时间设置</span>
              </div>
            </div>

            <div class="section-content">
              <FormItem label="" prop="expireTime" class="time-form-item">
                <div class="time-input-wrapper">
                  <el-date-picker
                    v-model="editForm.expireTime"
                    type="datetime"
                    placeholder="选择过期时间"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    class="time-picker"
                    prefix-icon="el-icon-time"
                    :picker-options="expireTimePickerOptions"
                    @change="validateTimeRange"
                  />
                  <el-popover
                    ref="quickSetPopover"
                    v-model="quickSetVisible"
                    placement="bottom-end"
                    width="280"
                    trigger="hover"
                    popper-class="quick-set-popover"
                  >
                    <div class="quick-set-content">
                      <div class="quick-set-title">快速设置</div>
                      <div class="quick-buttons">
                        <el-button size="mini" @click="setQuickDaysAndClose(3)">3天</el-button>
                        <el-button size="mini" @click="setQuickDaysAndClose(5)">5天</el-button>
                        <el-button size="mini" @click="setQuickDaysAndClose(7)">7天</el-button>
                        <el-button size="mini" @click="setQuickDaysAndClose(30)">30天</el-button>
                      </div>
                      <div class="custom-section">
                        <span class="custom-label">自定义：</span>
                        <el-input-number
                          v-model="quickDays"
                          :min="1"
                          :max="365"
                          size="mini"
                          :controls="false"
                          style="width: 60px;"
                        />
                        <span class="custom-unit">天</span>
                        <el-button size="mini" type="primary" @click="setQuickDaysAndClose(quickDays)">
                          确定
                        </el-button>
                      </div>
                    </div>
                    <span slot="reference" class="quick-set-text">
                      <i class="el-icon-magic-stick"></i>
                      快速设置
                    </span>
                  </el-popover>
                </div>
              </FormItem>
            </div>
          </div>
        </div>

        <!-- 保护设置 -->
        <div class="protection-wrapper">
          <div class="protection-section">
            <div class="section-header">
              <div class="section-title">
                <span class="title-text">保护</span>
              </div>
            </div>

            <div class="section-content">
              <div class="checkbox-row">
                <el-checkbox
                  v-model="editForm.isPasswordProtected"
                  :true-label="true"
                  :false-label="false"
                  class="protection-checkbox"
                  @change="handlePasswordProtectionChange"
                >
                  <span class="checkbox-label">是否需要密码</span>
                </el-checkbox>
                <el-tooltip effect="dark" placement="top" content="密码请注意保管，一旦丢失将无法找回！">
                  <i class="el-icon-question help-icon"></i>
                </el-tooltip>
              </div>

              <transition name="slide-fade">
                <!-- 显示密码输入框的条件：新增时启用密码保护 或 编辑时从无密码改为有密码 -->
                <div v-if="editForm.isPasswordProtected === true && shouldShowPasswordInput" class="password-section">
                  <FormItem label="" prop="password" class="password-form-item">
                    <el-input
                      v-model="editForm.password"
                      type="password"
                      show-password
                      :maxlength="20"
                      placeholder="请输入密码"
                      class="password-input"
                      @input="handlePasswordInput"
                    />
                  </FormItem>

                  <FormItem label="" prop="confirmPassword" class="password-form-item">
                    <el-input
                      v-model="editForm.confirmPassword"
                      type="password"
                      show-password
                      placeholder="请再次输入密码"
                      class="password-input"
                      @input="handleConfirmPasswordInput"
                    />
                  </FormItem>
                </div>

                <!-- 编辑时的密码提示 -->
                <div v-if="editForm.id && editForm.isPasswordProtected === true && !shouldShowPasswordInput" class="password-edit-tip">
                  <i class="el-icon-info"></i>
                  <span>当前配置已启用密码保护，编辑时密码保持不变。如需修改密码请重新创建配置。</span>
                </div>

                <div v-if="editForm.id && editForm.isPasswordProtected === false" class="password-edit-tip">
                  <i class="el-icon-info"></i>
                  <span>当前配置未启用密码保护，导入时无需输入密码。</span>
                </div>
              </transition>
            </div>
          </div>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          保存
        </el-button>
        <el-button
          type="primary"
          :loading="submittingAndSync"
          style="background: #00c853; border-color: #00c853; margin-left: 8px;"
          @click="handleSubmitAndSync"
        >
          保存并同步至云服务
        </el-button>
      </div>
    </el-dialog>

    <!-- 同步提示气泡框 -->
    <div
      v-show="syncTooltipVisible"
      id="syncTooltip"
      class="sync-tooltip"
      @mouseenter="handleTooltipMouseEnter"
      @mouseleave="handleTooltipMouseLeave"
    >
      <div class="sync-tooltip-content">
        是否同步此延长配置至云服务？
      </div>
      <div class="sync-tooltip-buttons">
        <button
          class="sync-tooltip-btn yes"
          @click="handleSyncConfirm"
          @click.stop
        >
          是
        </button>
        <button
          class="sync-tooltip-btn no"
          @click="hideSyncTooltip"
          @click.stop
        >
          否
        </button>
      </div>
    </div>

    <!-- 日志查看弹窗 -->
    <el-dialog
      title="操作日志"
      :visible.sync="logDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      :modal="true"
      :lock-scroll="false"
      append-to-body
      @close="handleLogDialogClose"
    >
      <div class="log-container">
        <div v-if="logLoading" class="log-loading">
          <i class="el-icon-loading"></i>
          <span>加载中...</span>
        </div>
        <div v-else-if="logList.length === 0" class="log-empty">
          <i class="el-icon-document"></i>
          <span>暂无日志记录</span>
        </div>
        <div v-else class="log-timeline">
          <div
            v-for="(log, index) in logList"
            :key="index"
            class="timeline-item"
          >
            <div class="timeline-dot"></div>
            <div class="timeline-content">
              <div class="timeline-title">{{ log.operationType }}</div>
              <div class="timeline-info">
                <div v-if="log.expireTime" class="time-info">
                  <span class="label">策略过期时间：</span>
                  <span class="value">{{ log.expireTime }}</span>
                </div>
                <!-- 终端同步类型显示表格 -->
                <div v-if="log.logType === 1" class="sync-details">
                  <div class="sync-table-container">
                    <el-table
                      :data="getSyncTableData(log)"
                      size="mini"
                      border
                      style="width: 100%"
                      max-height="240"
                    >
                      <el-table-column label="序号" width="80" align="center">
                        <template slot-scope="scope">
                          {{ scope.$index + 1 }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="terminalNo" label="终端编号" min-width="120" />
                      <el-table-column prop="IP" label="IP地址" min-width="130">
                        <template slot-scope="scope">
                          {{ scope.row.IP || '-' }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="IPAddress" label="IP归属地" min-width="180">
                        <template slot-scope="scope">
                          {{ scope.row.IPAddress || '-' }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="syncTime" label="同步时间" min-width="160">
                        <template slot-scope="scope">
                          {{ formatSyncTime(scope.row.syncTime) }}
                        </template>
                      </el-table-column>

                      <!-- 使用表格的append插槽实现无限滚动 -->
                      <template slot="append">
                        <div v-if="getSyncTableLoading(log)" class="table-loading">
                          <i class="el-icon-loading"></i> 加载中...
                        </div>
                        <div
                          v-else-if="getSyncTableHasMore(log)"
                          class="load-more-btn"
                          @click="loadMoreSyncData(log)"
                        >
                          <i class="el-icon-arrow-down"></i> 点击加载更多 (已显示 {{ getSyncTableDisplayCount(log) }} / {{ getSyncTableTotalCount(log) }} 条)
                        </div>
                      </template>
                    </el-table>
                  </div>
                </div>
                <!-- 其他类型显示原始内容 -->
                <div v-else-if="log.logType !== 0" class="sync-info">
                  <span class="value status-success">{{ log.logContent }}</span>
                </div>
              </div>
              <!-- 终端同步类型不显示操作员和时间信息 -->
              <div v-if="log.logType !== 1" class="timeline-meta">
                <div class="operator">操作员：{{ getUserName(log) }}</div>
                <div class="time">时间：{{ log.operateTime }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="logDialogVisible = false">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTimeExtensionList,
  createTimeExtension,
  updateTimeExtension,
  deleteTimeExtension,
  getQuotaInfo,
  syncToCloud
} from '@/api/dataEncryption/encryption/offlineStrategyTimeExtension'
import { getLogList, getTerminalSyncDetails } from '@/api/dataEncryption/encryption/offlineStrategyExtensionLog'
import { getTermTypeDict } from '@/utils/dictionary'
import { getChildTerminalTree } from '@/api/tree'
import moment from 'moment'
import { aesEncode, formatAesKey } from '@/utils/encrypt';

export default {
  name: 'OfflineStrategyTimeExtension',
  props: {
    // 从父组件接收共享数据
    sharedData: {
      type: Object,
      default: () => ({
        userInfoCache: new Map(),
        adminInfoCache: new Map(),
        termTypeDict: [],
        termTreeList: [],
        deptTree: [],
        cacheStatus: {}
      })
    },
    // 父组件实例引用
    parentComponent: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 查询条件
      query: {
        page: 1,
        limit: 15,
        targetType: '',
        syncStatus: '',
        target: '',
        status: '' // 逻辑状态
      },
      // 选中的目标
      selectedTargetName: '',
      selectedtarget: null,
      selectedTargetType: null,
      selectedTargetIcon: '',
      // 选中的行
      selectedRows: [],
      // 配额信息
      quotaInfo: {
        used: 0,
        total: 10
      },
      // 配额配置列表
      quotaConfigList: [],
      // 对话框
      dialogVisible: false,
      dialogTitle: '',
      submitting: false,
      submittingAndSync: false, // 保存并同步的loading状态
      // 树形数据
      treeData: [],
      treeProps: {
        children: 'children',
        label: 'label'
      },
      // 编辑表单
      editForm: {
        id: null,
        target: null,
        targetName: '', // 用于显示的目标名称
        targetType: null, // 1=分组, 2=终端，由选择的目标自动确定
        effectiveTime: '',
        expireTime: '',
        remark: '',
        // 密码保护相关
        isPasswordProtected: false, // false=不启用密码保护, true=启用密码保护
        password: '',
        confirmPassword: ''
      },
      // 表单验证规则
      editRules: {
        target: [
          { required: true, message: '请选择生效目标', trigger: 'change' }
        ],
        expireTime: [
          { required: true, message: '请选择策略过期时间', trigger: 'change' },
          { validator: this.expireTimeValidator, trigger: 'change' }
        ],
        password: [
          { validator: this.passwordRequiredValidator, trigger: 'blur' },
          { min: 5, max: 20, message: '密码长度为5-20位', trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: this.passwordValidator, trigger: 'blur' }
        ]
      },
      // 快速设置天数
      quickDays: 3,
      // 快速设置弹窗显示状态
      quickSetVisible: false,
      // 记录编辑时原始的密码保护状态
      originalPasswordProtected: false,
      // 日志弹窗
      logDialogVisible: false,
      logLoading: false,
      logList: [],
      currentLogConfigId: null,
      currentLogTargetName: '',
      // 同步气泡相关
      syncTooltipVisible: false,
      currentHoverRow: null,
      hoverTimeout: null,
      // 终端同步表格相关 - 每个日志独立维护状态
      syncTableStates: {} // key为logId，value为该日志的表格状态
    }
  },
  computed: {
    quotaPercentage() {
      if (!this.quotaInfo.total) return 0
      return Math.min((this.quotaInfo.used / this.quotaInfo.total) * 100, 100)
    },
    // tree-select组件的数据源
    treeSelectData() {
      // 使用store中的终端树数据，如果没有则返回空数组
      const termTreeList = this.termTreeList()
      // 合并终端和分组数据
      return [...termTreeList]
    },
    // tree-select组件的选中keys
    targetCheckedKeys() {
      if (!this.editForm.target) {
        return []
      }

      // 返回简单的key值数组，tree-select组件会根据这个key在数据中查找对应的节点
      return [this.editForm.target]
    },
    // 过期时间选择器配置
    expireTimePickerOptions() {
      return {
        disabledDate: (time) => {
          // 允许选择当前时间往前推1个月到未来的时间范围
          const oneMonthAgo = new Date()
          oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
          oneMonthAgo.setHours(0, 0, 0, 0)
          return time.getTime() < oneMonthAgo.getTime()
        }
      }
    },

    // 判断是否应该显示密码输入框
    shouldShowPasswordInput() {
      // 新增时：启用密码保护就显示
      if (!this.editForm.id) {
        return true
      }

      // 编辑时：只有当原来没有密码保护，现在要启用时才显示
      // 这里需要记录原始的密码保护状态
      return this.originalPasswordProtected === false && this.editForm.isPasswordProtected === true
    },

    // 判断是否显示密码安全提示
    showPasswordSecurityTip() {
      // 只要启用了密码保护就显示提示
      return this.editForm.isPasswordProtected === true
    },
    // 动态生成列配置
    colModel() {
      return [
        { prop: 'targetTypeDesc', label: '类型', width: '80' },
        { prop: 'target', label: '生效目标', width: '150', formatter: this.targetFormatter },
        { prop: 'expireTime', label: '策略过期时间', width: '150' },
        {
          prop: 'status',
          label: '状态',
          width: '120',
          formatter: this.statusFormatter
        },
        { prop: 'terminalSyncTime', label: '终端同步时间', width: '150' },
        { prop: 'updateTime', label: '修改时间', width: '150' },
        {
          prop: 'actions',
          label: '操作',
          type: 'button',
          fixedWidth: '200',
          fixed: 'right',
          buttons: [
            { label: '编辑', click: this.handleEdit, type: 'text' },
            { label: '查看日志', click: this.handleViewLog, type: 'text' },
            { label: '删除', click: this.handleDelete, type: 'text' }
          ]
        }
      ]
    }
  },
  watch: {
    // 监听对话框显示状态变化
    dialogVisible(newVal, oldVal) {
      if (newVal && !oldVal) {
        // 对话框从隐藏变为显示时，清除验证状态
        this.$nextTick(() => {
          if (this.$refs.editForm && this.$refs.editForm.clearValidate) {
            this.$refs.editForm.clearValidate()
          }
        })
      }
    }
  },
  created() {
    this.loadQuotaInfo()
  },
  mounted() {
    // 页面加载完成后执行一次搜索
    this.$nextTick(() => {
      this.handleFilter()
    })
  },
  methods: {
    // 终端过滤器
    terminalFilter(node) {
      if (node.type == 3) {
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      return !isNaN(type);
    },
    // 策略目标节点变化
    strategyTargetNodeChange(tabName, checkedNode) {
      this.selectedTargetName = checkedNode.label
      this.selectedtarget = checkedNode.dataId
      this.selectedTargetType = checkedNode.type
      // 获取节点的图标，优先使用dataType对应的图标
      this.selectedTargetIcon = this.getNodeIcon(checkedNode)
      this.query.targetType = this.frontendToBackendType(checkedNode.type)
      this.query.target = checkedNode.dataId
      this.handleFilter()
    },
    // 清空目标选择
    clearTarget() {
      this.selectedTargetName = ''
      this.selectedtarget = null
      this.selectedTargetType = null
      this.selectedTargetIcon = ''
      this.query.targetType = undefined
      this.query.target = undefined
      this.handleFilter()
    },
    // 数据API
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTimeExtensionList(searchQuery)
    },
    // 搜索过滤
    handleFilter() {
      this.$refs.dataTable.execRowDataApi()
    },
    // 清空筛选条件
    clearFilters() {
      this.query.targetType = ''
      this.query.status = ''
      this.query.syncStatus = ''
      this.query.target = ''
      this.query.keyword = ''
      // 清空目标选择
      this.selectedTargetName = ''
      this.selectedtarget = null
      this.selectedTargetType = null
      this.selectedTargetIcon = ''
      this.query.objectType = undefined
      this.query.objectId = undefined
      // 清空后自动执行一次搜索
      this.handleFilter()
    },
    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    // 新增配置
    handleAdd() {
      this.dialogTitle = '新增离线策略时间延长配置'
      this.resetEditForm()
      // 确保清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.editForm && this.$refs.editForm.clearValidate) {
          this.$refs.editForm.clearValidate()
        }
      })
      this.dialogVisible = true
    },
    // 编辑配置
    handleEdit(row) {
      this.dialogTitle = '编辑离线策略时间延长配置'
      this.editForm = {
        id: row.id,
        target: row.target,
        targetName: this.getTargetDisplayName(row.target, row.targetType),
        targetType: row.targetType, // 从数据中获取类型
        effectiveTime: row.effectiveTime,
        expireTime: row.expireTime,
        remark: row.remark || '',
        // 编辑时密码保护相关字段
        isPasswordProtected: row.isPasswordProtected || false,
        password: '', // 编辑时不显示原密码
        confirmPassword: ''
      }

      // 记录原始的密码保护状态
      this.originalPasswordProtected = row.isPasswordProtected || false

      // 确保清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.editForm && this.$refs.editForm.clearValidate) {
          this.$refs.editForm.clearValidate()
        }
      })
      this.dialogVisible = true
    },
    // 删除配置
    handleDelete(row) {
      const targetName = this.getTargetDisplayName(row.target, row.targetType)
      this.$confirmBox(
        `确定要删除"${targetName}"的延长配置吗？`,
        '删除确认'
      ).then(() => {
        this.deleteConfigs([row.id])
      }).catch(() => {})
    },
    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message({
          message: '请选择要删除的延长配置',
          type: 'warning'
        })
        return
      }
      const names = this.selectedRows.map(row => this.getTargetDisplayName(row.target, row.targetType)).join('、')
      this.$confirmBox(
        `确定要删除选中的${this.selectedRows.length}个延长配置吗？\n${names}`,
        '批量删除确认'
      ).then(() => {
        const ids = this.selectedRows.map(row => row.id)
        this.deleteConfigs(ids)
      }).catch(() => {})
    },
    // 批量同步
    handleBatchSync() {
      if (this.selectedRows.length === 0) {
        this.$message({
          message: '请选择要同步的延长配置',
          type: 'warning'
        })
        return
      }
      const names = this.selectedRows.map(row => this.getTargetDisplayName(row.target, row.targetType)).join('、')
      this.$confirmBox(
        `确定要将选中的${this.selectedRows.length}个延长配置同步至云服务吗？\n${names}`,
        '同步确认'
      ).then(() => {
        const ids = this.selectedRows.map(row => row.id)
        this.syncConfigs(ids)
      }).catch(() => {})
    },
    // 查看日志
    handleViewLog(row) {
      this.currentLogConfigId = row.id
      this.currentLogTargetName = this.getTargetDisplayName(row.target, row.targetType)
      this.logDialogVisible = true
      this.loadLogData()
    },
    // 执行删除
    deleteConfigs(ids) {
      deleteTimeExtension(ids).then(() => {
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        this.handleFilter()
        this.loadQuotaInfo()
      }).catch(error => {
        this.$message({
          message: '删除失败：' + (error.message || '未知错误'),
          type: 'error'
        })
      })
    },
    // 执行同步
    syncConfigs(ids) {
      syncToCloud(ids).then(() => {
        this.$message({
          message: '同步成功',
          type: 'success'
        })
        this.handleFilter()
        this.loadQuotaInfo()
      })
    },
    // 快速同步单个配置
    handleQuickSync(row) {
      const targetName = this.getTargetDisplayName(row.target, row.targetType)
      this.$confirmBox(
        `确定要将"${targetName}"的延长配置同步至云服务吗？`,
        '同步确认'
      ).then(() => {
        this.syncConfigs([row.id])
      }).catch(() => {})
    },
    // 创建同步气泡
    createSyncBubble(elementId, row) {
      // 使用多次尝试确保元素存在
      const tryBindEvents = (attempts = 0) => {
        if (attempts > 10) return // 最多尝试10次

        const element = document.getElementById(elementId)
        if (element && !element.hasAttribute('data-bubble-created')) {
          // 标记已创建气泡，避免重复创建
          element.setAttribute('data-bubble-created', 'true')

          // 添加点击事件
          element.addEventListener('click', (e) => {
            e.preventDefault()
            e.stopPropagation()
            this.handleQuickSync(row)
          })

          // 添加鼠标悬停事件 - 参考原型实现
          element.addEventListener('mouseenter', (e) => {
            this.handleUnsyncedHover(e, row)
          })

          element.addEventListener('mouseleave', (e) => {
            this.handleUnsyncedLeave(e)
          })
        } else if (!element) {
          // 如果元素还不存在，延迟重试
          setTimeout(() => tryBindEvents(attempts + 1), 100)
        }
      }

      this.$nextTick(() => {
        tryBindEvents()
      })
    },

    // 处理未同步状态的悬停事件 - 参考原型
    handleUnsyncedHover(event, row) {
      const statusCell = event.target
      this.currentHoverRow = row

      // 延迟显示气泡框，避免快速划过时闪烁
      clearTimeout(this.hoverTimeout)
      this.hoverTimeout = setTimeout(() => {
        if (this.currentHoverRow === row) {
          this.showSyncTooltip(statusCell, row)
        }
      }, 300)
    },

    // 处理鼠标离开事件 - 参考原型
    handleUnsyncedLeave(event) {
      clearTimeout(this.hoverTimeout)

      // 延迟隐藏，允许鼠标移到气泡框上
      setTimeout(() => {
        const tooltip = document.getElementById('syncTooltip')
        if (tooltip && !this.isHoveringTooltip(tooltip)) {
          this.hideSyncTooltip()
        }
      }, 100)
    },

    // 检查是否在悬停气泡框
    isHoveringTooltip(tooltip) {
      return tooltip.matches(':hover')
    },

    // 显示同步提示气泡 - 参考原型实现
    showSyncTooltip(statusCell, row) {
      this.currentHoverRow = row
      this.syncTooltipVisible = true

      this.$nextTick(() => {
        const tooltip = document.getElementById('syncTooltip')
        if (tooltip && statusCell) {
          const rect = statusCell.getBoundingClientRect()
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop
          const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

          // 计算气泡位置
          const tooltipWidth = 200
          const tooltipHeight = 80

          let left = rect.left + scrollLeft + rect.width / 2 - tooltipWidth / 2
          let top = rect.top + scrollTop - tooltipHeight - 10

          // 边界检查
          if (left < 10) left = 10
          if (left + tooltipWidth > window.innerWidth - 10) {
            left = window.innerWidth - tooltipWidth - 10
          }
          if (top < 10) {
            top = rect.bottom + scrollTop + 10
          }

          tooltip.style.left = left + 'px'
          tooltip.style.top = top + 'px'
        }
      })
    },

    // 隐藏同步提示气泡
    hideSyncTooltip() {
      this.syncTooltipVisible = false
      // 延迟清空currentHoverRow，确保点击事件能正常执行
      setTimeout(() => {
        this.currentHoverRow = null
      }, 100)
    },

    // 确认同步操作
    handleSyncConfirm() {
      if (this.currentHoverRow && this.currentHoverRow.id) {
        const rowData = { ...this.currentHoverRow } // 保存行数据的副本
        this.hideSyncTooltip()
        // 直接调用同步接口，不再弹出确认框
        this.syncConfigs([rowData.id])
      } else {
        this.hideSyncTooltip()
      }
    },

    // 气泡框鼠标进入事件
    handleTooltipMouseEnter() {
      // 清除隐藏定时器，保持气泡显示
      clearTimeout(this.hoverTimeout)
    },

    // 气泡框鼠标离开事件
    handleTooltipMouseLeave() {
      // 延迟隐藏气泡
      this.hoverTimeout = setTimeout(() => {
        this.hideSyncTooltip()
      }, 100)
    },
    // tree-select组件选择变化
    handleTargetSelectChange(selectedKeys, selectedData) {
      console.log('handleTargetSelectChange:', selectedKeys, selectedData)
      if (selectedData) {
        // 处理数组格式的 selectedData
        const nodeData = Array.isArray(selectedData) ? selectedData[0] : selectedData
        if (nodeData) {
          this.editForm.target = nodeData.dataId || nodeData.id
          this.editForm.targetName = nodeData.label
          // 使用转换方法将前端类型转为后端类型
          this.editForm.targetType = this.frontendToBackendType(nodeData.type)
          console.log('设置目标值:', this.editForm.target, this.editForm.targetName, this.editForm.targetType)
          // 手动触发表单验证
          this.$nextTick(() => {
            this.$refs.editForm.validateField('target')
          })
        }
      } else {
        console.log('清空目标选择')
        this.editForm.target = null
        this.editForm.targetName = ''
        this.editForm.targetType = null
      }
    },
    // 加载树形数据
    loadTreeData() {
      getChildTerminalTree().then(res => {
        if (res.data && res.data.length > 0) {
          this.treeData = this.transformTreeData(res.data)
        }
      }).catch(error => {
        console.error('加载组织树数据失败:', error)
        this.$message.error('加载组织树数据失败')
      })
    },
    // 转换树形数据格式
    transformTreeData(data) {
      return data.map(node => {
        const transformedNode = {
          id: node.id,
          label: this.formatNodeLabel(node),
          type: node.type,
          dataType: node.dataType,
          dataId: node.dataId || node.id,
          name: node.name, // 保留原始名称
          originalLabel: node.label // 保留原始标签
        }

        // 递归处理子节点
        if (node.children && node.children.length > 0) {
          const filteredChildren = this.transformTreeData(node.children)
          if (filteredChildren.length > 0) {
            transformedNode.children = filteredChildren
          }
        }

        return transformedNode
      }).filter(node => {
        // 应用与terminalFilter相同的过滤逻辑
        return this.shouldIncludeNode(node)
      })
    },
    // 格式化节点标签
    formatNodeLabel(node) {
      // 如果是终端节点，显示为"终端名称（终端编号）"
      if (node.type === 1) {
        const terminalName = node.name || node.label
        const terminalCode = node.id
        if (terminalName && terminalCode && terminalName !== terminalCode) {
          return `${terminalName}（${terminalCode}）`
        }
        return terminalName || terminalCode || node.label
      }
      // 分组和其他节点直接使用label
      return node.label
    },
    // 判断是否应该包含节点（应用terminalFilter逻辑）
    shouldIncludeNode(node) {
      // 分组节点
      if (node.type === 3) {
        // 排除特定分组
        return node.id !== 'G-2'
      }

      // 终端节点
      if (node.type === 1) {
        const type = parseInt(node.dataType)
        if (isNaN(type)) {
          return false
        }
        return true
      }

      // 其他类型节点，如果有子节点则保留
      return node.children && node.children.length > 0
    },
    // 验证时间范围
    validateTimeRange() {
      // 触发表单验证
      this.$nextTick(() => {
        if (this.$refs.editForm) {
          this.$refs.editForm.validateField('expireTime')
        }
      })
    },

    // 过期时间验证器
    expireTimeValidator(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      const expire = new Date(value)
      const oneMonthAgo = new Date()
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
      oneMonthAgo.setHours(0, 0, 0, 0)

      if (expire.getTime() < oneMonthAgo.getTime()) {
        callback(new Error('过期时间不能早于一个月前'))
        return
      }

      if (this.editForm.effectiveTime) {
        const effective = new Date(this.editForm.effectiveTime)
        if (expire <= effective) {
          callback(new Error('过期时间必须大于生效时间'))
          return
        }
      }

      callback()
    },

    // 手动校验过期时间
    validateExpireTime() {
      if (!this.editForm.expireTime) {
        this.$message({
          message: '请选择策略过期时间',
          type: 'warning'
        })
        return false
      }

      const expire = new Date(this.editForm.expireTime)
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      if (expire.getTime() < today.getTime()) {
        this.$message({
          message: '过期时间不能小于今天',
          type: 'warning'
        })
        return false
      }

      if (this.editForm.effectiveTime) {
        const effective = new Date(this.editForm.effectiveTime)
        if (expire <= effective) {
          this.$message({
            message: '过期时间必须大于生效时间',
            type: 'warning'
          })
          return false
        }
      }

      return true
    },

    // 密码输入处理
    handlePasswordInput(value) {
      // 移除空格
      this.editForm.password = value.replace(/\s/g, '')
    },

    // 确认密码输入处理
    handleConfirmPasswordInput(value) {
      // 移除空格
      this.editForm.confirmPassword = value.replace(/\s/g, '')
    },

    // 处理密码保护状态变化
    handlePasswordProtectionChange(value) {
      // 如果是编辑模式，且从启用密码保护改为不启用
      if (this.editForm.id && value === false) {
        this.$confirm(
          '取消密码保护后，导入此配置将不再需要输入密码。确定要取消密码保护吗？',
          '确认取消密码保护',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
        }).catch(() => {
          // 用户取消，恢复原状态
          this.editForm.isPasswordProtected = true
        })
      }

      // 如果是新增模式或者从不启用改为启用，不需要特殊处理
      // 清空密码字段（如果有的话）
      if (value === false) {
        this.editForm.password = ''
        this.editForm.confirmPassword = ''
      }
    },

    // 密码必填验证器
    passwordRequiredValidator(rule, value, callback) {
      // 如果不需要显示密码输入框，不验证
      if (!this.shouldShowPasswordInput) {
        callback()
        return
      }

      // 需要显示密码输入框时，如果启用密码保护，密码必填
      if (this.editForm.isPasswordProtected === true) {
        if (!value) {
          callback(new Error('密码不能为空'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },

    // 确认密码验证器
    passwordValidator(rule, value, callback) {
      // 如果不需要显示密码输入框，不验证
      if (!this.shouldShowPasswordInput) {
        callback()
        return
      }

      // 需要显示密码输入框时验证确认密码
      if (this.editForm.isPasswordProtected === true) {
        if (!value) {
          callback(new Error('请输入确认密码'))
        } else if (value !== this.editForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    // 快速设置天数
    setQuickDays(days) {
      let baseDate

      if (this.editForm.expireTime) {
        // 如果已经有过期时间，在当前过期时间基础上延长xx天
        baseDate = new Date(this.editForm.expireTime)
      } else {
        // 如果没有过期时间，以今天为基准延长xx天
        baseDate = new Date()
      }

      const expireDate = new Date(baseDate.getTime() + days * 24 * 60 * 60 * 1000)
      this.editForm.expireTime = moment(expireDate).format('YYYY-MM-DD HH:mm:ss')

      // 验证时间范围
      this.validateTimeRange()
    },
    // 快速设置天数并关闭弹窗
    setQuickDaysAndClose(days) {
      if (!days || days <= 0) {
        this.$message({
          message: '请输入有效的天数',
          type: 'warning'
        })
        return
      }
      this.setQuickDays(days)
      // 立即关闭弹窗
      this.quickSetVisible = false
      this.$message({
        message: `已延长 ${days} 天`,
        type: 'success'
      })
    },
    // 提交表单
    handleSubmit() {
      this.submitForm(false)
    },
    // 提交并同步至云服务
    handleSubmitAndSync() {
      this.submitForm(true)
    },
    // 统一的提交逻辑
    submitForm(syncToCloud = false) {
      // 使用表单验证，错误会显示在输入框下方
      this.$refs.editForm.validate(valid => {
        if (valid) {
          const loadingField = syncToCloud ? 'submittingAndSync' : 'submitting'
          this[loadingField] = true

          const data = { ...this.editForm }

          // 移除不需要的字段
          delete data.targetName

          // 处理密码加密
          if (data.isPasswordProtected === true) {
            if (data.id && this.originalPasswordProtected === true) {
              // 编辑时原来就有密码保护，不发送密码字段，保持原有密码不变
              delete data.password
              delete data.confirmPassword
            } else if (data.password) {
              // 新增时 或 编辑时从无密码改为有密码，加密密码和确认密码
              const { aesEncode, formatAesKey } = require('@/utils/encrypt')
              const key = formatAesKey('tr838408', '')
              data.password = aesEncode(data.password, key)
              data.confirmPassword = aesEncode(data.confirmPassword, key)
            }
          } else {
            // 如果不启用密码保护，清空密码字段
            delete data.password
            delete data.confirmPassword
            delete data.isPasswordProtected
          }

          // 如果是保存并同步，添加syncToCloud字段
          if (syncToCloud) {
            data.syncToCloud = true
          }

          const apiCall = data.id ? updateTimeExtension(data) : createTimeExtension(data)

          apiCall.then(() => {
            const action = data.id ? '更新' : '新增'
            const syncText = syncToCloud ? '并同步至云服务' : ''
            this.$message({
              message: `${action}${syncText}成功`,
              type: 'success'
            })
            this.dialogVisible = false
            this.clearFilters()
            this.handleFilter()
            this.loadQuotaInfo()
          }).finally(() => {
            this[loadingField] = false
          })
        }
      })
    },
    // 对话框关闭
    handleDialogClose() {
      this.resetEditForm()
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.editForm && this.$refs.editForm.clearValidate) {
          this.$refs.editForm.clearValidate()
        }
      })
      // 重置loading状态
      this.submitting = false
      this.submittingAndSync = false
    },
    // 重置编辑表单
    resetEditForm() {
      this.editForm = {
        id: null,
        target: null,
        targetName: '',
        targetType: null,
        effectiveTime: '',
        expireTime: '',
        remark: '',
        // 重置密码保护相关字段
        isPasswordProtected: false,
        password: '',
        confirmPassword: ''
      }
      // 清空tree-select组件的内部状态
      this.$nextTick(() => {
        if (this.$refs.targetTreeSelect) {
          // 调用tree-select组件的清空方法
          this.$refs.targetTreeSelect.clearSelectedNode()
        }
      })
    },
    // 加载配额信息
    loadQuotaInfo() {
      getQuotaInfo().then(res => {
        if (res.data) {
          // 适配后端返回的数据结构
          this.quotaInfo = {
            used: res.data.usedQuota || 0,
            total: res.data.totalQuota || 10
          }
          // 保存配额配置列表
          this.quotaConfigList = res.data.configVOList || []
        } else {
          this.quotaInfo = { used: 0, total: 10 }
          this.quotaConfigList = []
        }
      }).catch(() => {
        // 静默处理错误，使用默认值
        this.quotaInfo = { used: 0, total: 10 }
        this.quotaConfigList = []
      })
    },
    // 目标格式化
    targetFormatter(row) {
      return this.getTargetDisplayName(row.target, row.targetType)
    },

    // 状态格式化
    statusFormatter(row, column, cellValue, index) {
      if (!row.syncStatus || row.syncStatus === '0') {
        // 为未同步状态创建气泡组件
        const uniqueId = `sync-status-${row.id}`
        this.$nextTick(() => {
          this.createSyncBubble(uniqueId, row)
        })
        return `
          <span
            id="${uniqueId}"
            class="sync-status-bubble"
            title="点击同步至云服务"
            onmouseenter="this.style.transform='translateY(-1px)'"
            onmouseleave="this.style.transform='translateY(0)'"
          >
            未同步至云服务
          </span>
        `
      } else {
        return row.status
      }
    },

    // 表格行选择函数
    selectableFunction(row, index) {
      // 可以根据行数据决定是否可选择
      return true
    },
    // 获取节点图标
    getNodeIcon(node) {
      // 根据组织树的图标配置获取图标
      const iconTypes = {
        typeKey: 'dataType',
        'G': 'terminalGroup',
        3: 'terminalGroup',
        1: 'terminal',
        2: 'user',
        4: 'userGroup'
      }

      // 使用共享的终端类型字典
      if (this.sharedData.termTypeDict && this.sharedData.termTypeDict.length > 0) {
        this.sharedData.termTypeDict.forEach(item => {
          iconTypes[item.value] = item.icon
        })
      } else {
        // 如果共享数据还没加载，使用原来的方法
        getTermTypeDict().forEach(item => {
          iconTypes[item.value] = item.icon
        })
      }

      // 优先使用dataType，其次使用type
      const type = node.dataType !== undefined ? node.dataType : node.type
      let iconClass = iconTypes[type]

      // 如果没有找到对应图标，根据节点类型使用默认图标
      if (!iconClass) {
        if (node.type === 1) {
          iconClass = 'terminal'
        } else if (node.type === 3 || node.dataType === 'G') {
          iconClass = 'terminalGroup'
        } else if (node.type === 2) {
          iconClass = 'user'
        } else if (node.type === 4) {
          iconClass = 'userGroup'
        } else {
          iconClass = 'terminal'
        }
      }

      // 处理未使用的USB终端或离线终端（添加-x后缀）
      if ((iconClass.indexOf('usb-') === 0 || iconClass.indexOf('offline-') === 0) && !node.dataCode) {
        iconClass += '-x'
      }

      return iconClass
    },
    // 获取目标显示名称
    getTargetDisplayName(target, targetType) {
      if (!target) return '未知目标'

      try {
        const TERMINAL_CONFIG = 2  // 终端配置
        const GROUP_CONFIG = 1     // 分组配置

        if (targetType === TERMINAL_CONFIG) {
          // 终端：从终端树中查找
          const termTreeList = this.termTreeList()
          if (termTreeList && termTreeList.length > 0) {
            // 查找前端类型为1的终端节点
            const frontendTerminalType = this.backendToFrontendType(targetType)
            const termNode = termTreeList.find(node => node.dataId == target && node.type == frontendTerminalType)
            if (termNode) {
              // 返回格式：终端名称（终端编号）
              const terminalName = termNode.name || termNode.label
              return terminalName || target
            }
          }
        } else if (targetType === GROUP_CONFIG) {
          // 分组：从部门树中查找
          const deptTree = this.deptTree()
          if (deptTree && deptTree.length > 0) {
            const { findNodeLabel } = require('@/utils/tree')
            const groupName = findNodeLabel(deptTree, target, 'dataId')
            return groupName || target
          }
        }
      } catch (error) {
        console.warn('获取目标显示名称失败:', error)
      }

      return target
    },
    // 获取终端树列表
    termTreeList() {
      return this.sharedData.termTreeList || this.$store.getters.termTreeList || []
    },
    // 获取部门树
    deptTree() {
      return this.sharedData.deptTree || this.$store.getters.deptTree || []
    },

    /**
     * 前端类型转后端类型
     * 前端：type=1(终端), type=3(分组) -> 后端：targetType=2(终端配置), targetType=1(分组配置)
     * @param {number} frontendType 前端类型值
     * @returns {number} 后端类型值
     */
    frontendToBackendType(frontendType) {
      const typeMap = {
        1: 2, // 终端：前端type=1 -> 后端targetType=2
        3: 1  // 分组：前端type=3 -> 后端targetType=1
      }
      return typeMap[frontendType] || null
    },

    /**
     * 后端类型转前端类型
     * 后端：targetType=2(终端配置), targetType=1(分组配置) -> 前端：type=1(终端), type=3(分组)
     * @param {number} backendType 后端类型值
     * @returns {number} 前端类型值
     */
    backendToFrontendType(backendType) {
      const typeMap = {
        2: 1, // 终端配置：后端targetType=2 -> 前端type=1
        1: 3  // 分组配置：后端targetType=1 -> 前端type=3
      }
      return typeMap[backendType] || null
    },

    /**
     * 获取类型显示名称
     * @param {number} type 类型值（前端或后端）
     * @param {string} typeSource 类型来源：'frontend' 或 'backend'
     * @returns {string} 类型显示名称
     */
    getTypeDisplayName(type, typeSource = 'backend') {
      if (typeSource === 'frontend') {
        // 前端类型
        const frontendTypeNames = {
          1: '终端',
          3: '分组'
        }
        return frontendTypeNames[type] || '未知类型'
      } else {
        // 后端类型
        const backendTypeNames = {
          2: '终端配置',
          1: '分组配置'
        }
        return backendTypeNames[type] || '未知类型'
      }
    },
    getSearchList() {
      // 获取store中的扁平列表数据
      const termTreeList = this.termTreeList()
      const deptTreeList = this.deptTree()

      // 合并扁平数据
      return [...deptTreeList, ...termTreeList]
    },
    // 加载日志数据
    async loadLogData() {
      if (!this.currentLogConfigId) {
        return
      }

      this.logLoading = true
      this.logList = []

      try {
        const res = await getLogList({ offlineStrategyExtendConfigId: this.currentLogConfigId, pageSize: -1 })
        if (res.data && Array.isArray(res.data)) {
          // TODO 日志内容重构，由后端完成大部分翻译

          // 处理日志数据，解析logContent
          const processedLogs = res.data.map(log => {
            const processedLog = { ...log }

            // 解析logContent JSON字符串
            if (log.logContent) {
              try {
                const logContentObj = JSON.parse(log.logContent)
                processedLog.parsedContent = logContentObj

                // 格式化时间
                if (logContentObj.effectiveTime) {
                  processedLog.effectiveTime = this.formatTimestamp(logContentObj.effectiveTime)
                }
                if (logContentObj.expireTime) {
                  processedLog.expireTime = this.formatTimestamp(logContentObj.expireTime)
                }
              } catch (error) {
                console.warn('解析日志内容失败:', error)
                processedLog.parsedContent = {}
              }
            }

            // 根据logType确定操作类型
            processedLog.operationType = this.getOperationTypeByLogType(log.logType)

            return processedLog
          }).sort((a, b) => new Date(b.operateTime) - new Date(a.operateTime))

          // 加载管理员信息并设置到日志数据中
          await this.loadUserInfoForLogs(processedLogs)

          this.logList = processedLogs

          // 初始化所有终端同步类型日志的表格数据
          const syncLogs = processedLogs.filter(log => log.logType === 1)
          syncLogs.forEach(log => {
            this.initSyncTableStateFromLogContent(log)
          })
        } else {
          this.logList = []
        }
      } catch (error) {
        this.$message.error('加载日志失败')
        this.logList = []
      } finally {
        this.logLoading = false
      }
    },
    // 日志弹窗关闭
    handleLogDialogClose() {
      this.logList = []
      this.currentLogConfigId = null
      this.currentLogTargetName = ''
      // 清理所有同步表格状态
      this.syncTableStates = {}
    },
    // 根据logType获取操作类型
    getOperationTypeByLogType(logType) {
      const typeMap = {
        0: '配置离线策略延长时间',
        1: '终端同步',
        2: '同步至云服务',
        3: '删除离线策略延长配置'
      }
      return typeMap[logType] || '未知操作'
    },

    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },

    // 加载管理员信息并设置到日志数据中
    async loadUserInfoForLogs(logs) {
      // 收集所有需要查询的管理员ID
      const userIds = [...new Set(logs.map(log => log.userId).filter(id => id))]

      if (userIds.length === 0) {
        return
      }

      // 使用父组件的共享缓存
      try {
        let userInfoMap = {}
        if (this.parentComponent && this.parentComponent.requestUserInfo) {
          userInfoMap = await this.parentComponent.requestUserInfo(userIds)
        }

        // 将用户信息设置到对应的日志对象中
        logs.forEach(log => {
          if (log.userId && userInfoMap && userInfoMap[log.userId]) {
            log.adminName = userInfoMap[log.userId].name
          } else if (log.userId) {
            log.adminName = `管理员${log.userId}`
          }
        })
      } catch (error) {
        console.warn('获取管理员信息失败:', error)
        // 设置默认值
        logs.forEach(log => {
          if (log.userId) {
            log.adminName = `管理员${log.userId}`
          }
        })
      }
    },

    // 根据日志对象获取管理员姓名
    getUserName(log) {
      if (!log || !log.userId) {
        return '未知管理员'
      }

      // 直接从日志对象中获取管理员姓名
      return log.adminName || `管理员${log.userId}`
    },

    // 解析终端同步数据
    parseSyncData(logContent) {
      if (!logContent) return null

      try {
        const data = JSON.parse(logContent)
        // 确保是数组格式
        if (Array.isArray(data)) {
          return data
        }
        return null
      } catch (e) {
        return null
      }
    },

    // 格式化同步时间
    formatSyncTime(timestamp) {
      if (!timestamp) return '未知时间'

      try {
        // 如果是毫秒时间戳，转换为日期格式
        const date = new Date(timestamp)
        return moment(date).format('YYYY-MM-DD HH:mm:ss')
      } catch (e) {
        return timestamp.toString()
      }
    },

    // 从logContent初始化同步表格状态
    initSyncTableStateFromLogContent(log) {
      if (!log.logContent) return

      try {
        const logContentObj = JSON.parse(log.logContent)

        this.$set(this.syncTableStates, log.id, {
          displayData: logContentObj.tableData || [], // 当前显示的数据
          totalCount: logContentObj.totalCount || 0,   // 总数量
          displayCount: logContentObj.displayCount || 0, // 当前显示数量
          hasMore: logContentObj.hasMore || false,     // 是否有更多数据
          loading: false,
          page: 1
        })
      } catch (error) {
        console.warn('解析终端同步日志内容失败:', error)
        this.$set(this.syncTableStates, log.id, {
          displayData: [],
          totalCount: 0,
          displayCount: 0,
          hasMore: false,
          loading: false,
          page: 1
        })
      }
    },

    // 获取同步表格数据
    getSyncTableData(log) {
      const state = this.syncTableStates[log.id]
      return state ? state.displayData : []
    },

    // 获取同步表格加载状态
    getSyncTableLoading(log) {
      const state = this.syncTableStates[log.id]
      return state ? state.loading : false
    },

    // 获取同步表格总数
    getSyncTableTotalCount(log) {
      const state = this.syncTableStates[log.id]
      return state ? state.totalCount : 0
    },

    // 获取当前显示数量
    getSyncTableDisplayCount(log) {
      const state = this.syncTableStates[log.id]
      return state ? state.displayCount : 0
    },

    // 获取是否有更多数据
    getSyncTableHasMore(log) {
      const state = this.syncTableStates[log.id]
      return state ? state.hasMore : false
    },
    // 删除配额配置 - 与主表格删除方法保持一致
    handleDeleteQuotaConfig(config) {
      const targetName = this.getTargetDisplayName(config.target, config.targetType)
      this.$confirmBox(
        `确定要删除"${targetName}"的延长配置吗？`,
        '删除确认'
      ).then(() => {
        // 使用与主表格相同的删除方法
        this.deleteConfigs([config.id || config.target])
      }).catch(() => {})
    },
    // 加载更多终端同步数据
    async loadMoreSyncData(log) {
      if (!log || !log.id) return

      const state = this.syncTableStates[log.id]
      if (!state || state.loading || !state.hasMore) return

      state.loading = true

      try {
        // 调用后端API获取更多数据
        const res = await getTerminalSyncDetails({
          page: state.page + 1,
          size: 6, // 每次加载6条
          offlineStrategyExtendConfigId: log.offlineStrategyExtendConfigId,
          version: log.version || 1
        })

        if (res.data && res.data.items) {
          const newData = res.data.items || []

          // 追加新数据
          state.displayData = [...state.displayData, ...newData]
          state.displayCount = state.displayData.length
          state.page++

          // 更新是否还有更多数据
          state.hasMore = state.displayData.length < state.totalCount
        }
      } catch (error) {
        this.$message.error('加载更多数据失败：' + (error.message || '未知错误'))
      } finally {
        state.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  overflow: auto;
  padding: 5px 0 20px 0;
}

.search-container {
  background: #0a111a;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;

  label {
    color: #a8b5c5;
    font-size: 13px;
    white-space: nowrap;
  }

  .target-display {
    .el-tag {
      background: #12202e;
      border-color: #1a2636;
      color: #00a4ff;

      .el-tag__close {
        color: #a8b5c5;

        &:hover {
          background: #1a2636;
          color: #ff4d4f;
        }
      }
    }
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #0a111a;
  border-radius: 4px;
  margin-bottom: 15px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.quota-info {
  display: flex;
  align-items: center;
  color: #a8b5c5;
  gap: 8px;

  .quota-used, .quota-total {
    color: #00a4ff;
    font-weight: bold;
  }
}

// 过期时间表单项
.expire-time-form-item {
  position: relative;
}

// 快速设置触发区域
.quick-set-trigger {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  z-index: 10;
  cursor: pointer;
}

// 快速设置弹窗样式
>>>.quick-set-popover {
  padding: 16px !important;

  .quick-set-content {
    .quick-set-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 12px;
      text-align: center;
    }

    .quick-buttons {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      margin-bottom: 16px;

      .el-button {
        margin: 0;
        font-size: 12px;
        padding: 6px 8px;
        border-radius: 4px;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }
      }
    }

    .custom-section {
      display: flex;
      align-items: center;
      gap: 8px;
      padding-top: 12px;
      border-top: 1px solid #e4e7ed;

      .custom-label {
        color: #606266;
        font-size: 13px;
        white-space: nowrap;
      }

      .custom-unit {
        color: #606266;
        font-size: 13px;
        white-space: nowrap;
      }

      .el-button {
        margin: 0;
        font-size: 12px;
        padding: 6px 12px;
      }
    }
  }
}

>>>.el-form-item__label {
  color: #ccc;
}

>>>.el-card {
  margin-top: 10px;
  background-color: transparent;
}

>>>.el-card__body {
  padding: 10px 40px;
}

.dialog-footer {
  text-align: right;
}

/* 通用对话框样式 - 不影响extension-dialog */
>>>.el-dialog__body:not(.extension-dialog .el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.tree-select-dropdown {
  width: 100%;
  height: 300px;
  overflow-y: auto;
  padding: 8px;
  margin: 0;
}

>>>.el-select-dropdown__empty {
  padding: 0 !important;
  margin: 0 !important;
  color: inherit;
  height: 300px;
  line-height: normal;
}

>>>.el-select-dropdown {
  background: #fff;
  border: 1px solid #dcdfe6;
  min-width: 100%;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;

  .svg-icon {
    margin-right: 8px;
    font-size: 14px;
  }

  .node-label {
    flex: 1;
    font-size: 14px;
    color: #606266;
  }
}

>>>.tree-select-dropdown .el-tree {
  background: transparent;
  color: inherit;
  width: 100%;
}

>>>.tree-select-dropdown .el-tree-node__content {
  height: 32px;
  line-height: 32px;
  padding-left: 10px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }
}

>>>.tree-select-dropdown .el-tree-node__expand-icon {
  color: #c0c4cc;
  font-size: 12px;
}

>>>.tree-select-dropdown .el-tree-node__expand-icon.expanded {
  transform: rotate(90deg);
}

>>>.tree-select-dropdown .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
}

/* 日志弹窗样式 */
.log-container {
  max-height: 500px;
  overflow-y: auto;

  .log-loading, .log-empty {
    text-align: center;
    padding: 40px 0;
    color: #999;

    i {
      font-size: 24px;
      margin-right: 8px;
    }
  }

  .log-timeline {
    position: relative;
    padding-left: 20px;

    &::before {
      content: '';
      position: absolute;
      left: 8px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #ddd;
    }

    .timeline-item {
      position: relative;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .timeline-dot {
        position: absolute;
        left: -16px;
        top: 8px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #409eff;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #ddd;
      }

      .timeline-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 12px;
        transition: border-color 0.2s ease;

        &:hover {
          border-color: #409eff;
        }

        .timeline-title {
          font-weight: 600;
          color: #303133;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .timeline-info {
          margin-bottom: 8px;
          color: #606266;
          font-size: 13px;
          line-height: 1.5;

          .time-info, .sync-info {
            display: flex;
            margin-bottom: 4px;

            .label {
              color: #909399;
              min-width: 100px;
              font-size: 12px;
            }

            .value {
              color: #606266;
              font-size: 12px;

              &.status-success {
                color: #67c23a;
              }

              &.status-error {
                color: #f56c6c;
              }

              &.status-processing {
                color: #e6a23c;
              }
            }
          }
        }

        .timeline-meta {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #909399;
          border-top: 1px solid #e9ecef;
          padding-top: 8px;
          margin-top: 8px;
        }
      }
    }
  }
}

/* 同步状态气泡样式 */
.sync-status-bubble {
  display: inline-block;
  padding: 4px 8px;
  background: linear-gradient(135deg, #e6a23c, #f39c12);
  color: #fff;
  border-radius: 12px;
  font-size: 12px;
  cursor: pointer !important;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(230, 162, 60, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(230, 162, 60, 0.4);
    background: linear-gradient(135deg, #f39c12, #e67e22);
    cursor: pointer !important;
  }
}

/* 同步提示气泡样式 - 参考原型 */
.sync-tooltip {
  position: fixed;
  z-index: 9999;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 12px;
  font-size: 14px;
  min-width: 200px;
  animation: tooltipFadeIn 0.2s ease-out;

  .sync-tooltip-content {
    margin-bottom: 10px;
    color: #606266;
    line-height: 1.4;
  }

  .sync-tooltip-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .sync-tooltip-btn {
      padding: 4px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 3px;
      background: #fff;
      color: #606266;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #c0c4cc;
        background: #f5f7fa;
      }

      &.yes {
        background: #409eff;
        border-color: #409eff;
        color: #fff;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }

      &.no {
        &:hover {
          color: #409eff;
          border-color: #c6e2ff;
          background: #ecf5ff;
        }
      }
    }
  }
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 终端同步表格样式 - 与系统整体风格保持一致 */
.sync-details {
  .sync-table-title {
    color: #a8b5c5;
    font-size: 12px;
    margin-bottom: 6px;
    font-weight: normal;
  }

  .sync-table-container {
    margin-top: 8px;

    >>>.el-table {
      background: transparent;
      width: 100%;

      .el-table__header {
        th {
          background: rgba(42, 52, 65, 0.5);
          color: #ffffff;
          font-weight: bold;
          font-size: 12px;
          border-bottom: 1px solid #2a3441;
        }
      }

      .el-table__body {
        tr {
          background: transparent;

          &:nth-child(even) {
            background: rgba(42, 52, 65, 0.1);
          }

          &:hover {
            background: rgba(42, 52, 65, 0.2) !important;
          }

          td {
            border-bottom: 1px solid #2a3441;
            color: #8a9aa9;
            font-size: 11px;
          }
        }
      }
    }

    .table-loading, .load-more-btn, .no-more {
      text-align: center;
      padding: 8px 12px;
      color: #8a9aa9;
      font-size: 11px;
      border-top: 1px solid #2a3441;
      line-height: 1.2;
    }

    .table-loading {
      i {
        margin-right: 5px;
      }
    }

    .load-more-btn {
      cursor: pointer;
      transition: all 0.3s ease;
      color: #00a4ff;

      &:hover {
        background: rgba(42, 52, 65, 0.2);
        color: #66b1ff;
      }

      i {
        margin-right: 5px;
        transition: transform 0.3s ease;
      }

      &:hover i {
        transform: translateY(2px);
      }
    }

    .no-more {
      color: #606266;
    }
  }
}

/* 配额使用弹出框样式 - 使用Element UI表格 */
>>>.quota-usage-popover {
  .el-table {
    .el-table__header {
      th {
        background: #f5f7fa;
        color: #303133;
        font-weight: 600;
        font-size: 12px;
      }
    }

    .el-table__body {
      tr:hover {
        background: #f0f9ff;
      }

      td {
        font-size: 12px;
      }
    }
  }
}

.quota-info {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  color: #a8b5c5;

  &:hover {
    background: rgba(42, 52, 65, 0.3);
    color: #ffffff;
  }

  .quota-used {
    color: #00a4ff;
    font-weight: 600;
  }

  .quota-total {
    color: #a8b5c5;
    font-weight: 500;
  }
}

/* 对话框样式优化 */
.extension-dialog {
  .el-dialog__body {
    padding: 20px;
    max-height: none;
    overflow: visible;
  }
}

/* 表单样式优化 */
.extension-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
    font-size: 14px;
  }

  .el-input__inner {
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    transition: all 0.3s;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }

    &:hover:not(:focus) {
      border-color: #c0c4cc;
    }
  }
}

/* 字段包装器 */
.field-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-label {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin: 0;

  &::before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
}

/* 生效目标样式 */
.target-tree-select {
  width: 100%;

  .el-input__inner {
    border: 1.5px solid #e4e7ed;
    border-radius: 6px;
    padding: 10px 12px;
    height: 40px;
    font-size: 14px;
    background-color: #ffffff;
    transition: all 0.2s;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
    }

    &:hover:not(:focus) {
      border-color: #c0c4cc;
    }
  }
}

.time-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-picker {
  flex: 1;

  .el-input__inner {
    border: 1.5px solid #e4e7ed;
    border-radius: 6px;
    padding: 10px 12px;
    height: 40px;
    font-size: 14px;
    background-color: #ffffff;
    transition: all 0.2s;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
    }

    &:hover:not(:focus) {
      border-color: #c0c4cc;
    }
  }

  .el-input__prefix {
    left: 12px;

    .el-input__icon {
      color: #c0c4cc;
    }
  }
}

.quick-set-text {
  color: #409eff;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 4px;

  i {
    font-size: 14px;
  }

  &:hover {
    color: #66b1ff;
  }

  &:active {
    color: #3a8ee6;
  }
}

/* 保护区域样式 */
.protection-wrapper {
  margin-top: 16px;

  &:first-child {
    margin-top: 0;
  }
}

.protection-section {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.section-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
}

.section-title {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  position: relative;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border-radius: 2px;
  margin-right: 10px;
}

.title-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.section-content {
  padding: 16px;
}

.checkbox-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}

.protection-checkbox {
  .el-checkbox__input {
    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      border-radius: 3px;
      border: 1.5px solid #dcdfe6;
      transition: all 0.2s;

      &:hover {
        border-color: #409eff;
      }

      &::after {
        width: 4px;
        height: 8px;
        left: 4px;
        top: 1px;
        border-width: 2px;
      }
    }

    &.is-checked .el-checkbox__inner {
      background-color: #409eff;
      border-color: #409eff;
    }
  }

  .el-checkbox__label {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
  }
}

.help-icon {
  color: #c0c4cc;
  font-size: 16px;
  cursor: help;
  transition: color 0.2s;

  &:hover {
    color: #909399;
  }
}

/* 密码字段动画 */
.slide-fade-enter-active {
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.755, 0.05, 0.855, 0.06);
}

.slide-fade-enter, .slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-15px);
}

.password-section {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.password-form-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .el-form-item__label {
    font-size: 14px;
    font-weight: 600;
    color: #606266;
    padding-bottom: 8px;
  }

  .el-form-item__error {
    padding-top: 4px;
    font-size: 12px;
  }
}

.password-edit-tip {
  margin-top: 16px;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  color: #0369a1;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;

  i {
    font-size: 16px;
    color: #0284c7;
  }
}

.password-input {
  width: 100%;

  .el-input__inner {
    border: 1.5px solid #e4e7ed;
    border-radius: 6px;
    padding: 10px 12px;
    height: 40px;
    font-size: 14px;
    background-color: #ffffff;
    transition: all 0.2s;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
    }

    &:hover:not(:focus) {
      border-color: #c0c4cc;
    }

    &::placeholder {
      color: #c0c4cc;
      font-size: 13px;
    }
  }

  .el-input__suffix {
    right: 12px;

    .el-input__icon {
      color: #c0c4cc;
      transition: color 0.2s;

      &:hover {
        color: #909399;
      }
    }
  }
}
/* 快速设置弹出框样式 */
.quick-set-popover {
  border-radius: 6px !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e4e7ed !important;
  padding: 0 !important;
}

.quick-set-content {
  padding: 16px;

  .quick-set-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #303133;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .quick-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;

    .el-button {
      margin: 0;
      border-radius: 4px;
      font-size: 12px;
      padding: 6px 12px;
      border-color: #dcdfe6;
      color: #606266;
      transition: all 0.2s;

      &:hover {
        background-color: #ecf5ff;
        border-color: #b3d8ff;
        color: #409eff;
      }
    }
  }

  .custom-section {
    display: flex;
    align-items: center;
    gap: 8px;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;

    .custom-label {
      font-size: 13px;
      color: #606266;
    }

    .custom-unit {
      font-size: 13px;
      color: #909399;
    }

    .el-input-number {
      .el-input__inner {
        border-radius: 4px;
        text-align: center;
        height: 28px;
        line-height: 28px;
      }
    }

    .el-button--primary {
      border-radius: 4px;
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}

/* 皮肤适配会由系统自动处理 */

/* 响应式优化 */
@media (max-width: 768px) {
  .extension-dialog {
    width: 90% !important;

    .el-dialog__body {
      padding: 16px;
    }
  }

  .protection-content {
    padding: 12px;
  }
}
</style>
