<template>
  <div class="app-container">
    <!--    <offline-strategy-setting/>-->
    <!-- 云离线策略Q4不上--暂时注释 -->
    <!-- 去掉v-if="false" -->
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.offline_offlineStrategySetting')" name="offlineStrategySetting">
        <offline-strategy-setting/>
      </el-tab-pane>
      <!--      <el-tab-pane label="离线策略时间延长配置" name="offlineStrategyTimeExtension">
        <offline-strategy-time-extension
          ref="timeExtensionComponent"
          :shared-data="sharedData"
          :parent-component="this"
        />
      </el-tab-pane>
      <el-tab-pane label="离线策略延长日志" name="offlineStrategyExtensionLog">
        <offline-strategy-extension-log
          ref="extensionLogComponent"
          :shared-data="sharedData"
          :parent-component="this"
        />
      </el-tab-pane>-->
      <!-- <el-tab-pane :label="$t('pages.offline_offlineStrategyList')" name="offlineStrategyList">
        <offline-strategy-list/>
      </el-tab-pane> -->
      <!-- 25D1不上 -->
      <!-- <el-tab-pane :label="$t('pages.offline_offlineStrategyExpiration')" name="offlineStrategyExpiration">
        <offline-strategy-expiration/>
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import offlineStrategySetting from '@/views/dataEncryption/encryption/offlineStrategy/offlineStrategySetting'
// import offlineStrategyTimeExtension from '@/views/dataEncryption/encryption/offlineStrategy/offlineStrategyTimeExtension'
// import offlineStrategyExtensionLog from '@/views/dataEncryption/encryption/offlineStrategy/offlineStrategyExtensionLog'
import { getDetail } from '@/api/system/organizational/sysUser'
import { getTermTypeDict } from '@/utils/dictionary'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
// import OfflineStrategyList from '@/views/dataEncryption/encryption/offlineStrategy/offineStrategyList';
// import OfflineStrategyExpiration from '@/views/dataEncryption/encryption/offlineStrategy/offlineStrategyExpiration';

export default {
  name: 'OfflineStrategy',
  components: {
    /* OfflineStrategyList, */
    offlineStrategySetting
    /* offlineStrategyTimeExtension,
    offlineStrategyExtensionLog,
    OfflineStrategyExpiration */
  },
  props: {
    tabName: { type: String, default: 'offlineStrategySetting' }
  },
  data() {
    return {
      activeName: this.tabName,
      // 共享数据缓存
      sharedData: {
        // 用户信息缓存 - Map结构，key为userId，value为用户信息
        userInfoCache: new Map(),
        // 管理员信息缓存 - Map结构，key为adminId，value为管理员信息
        adminInfoCache: new Map(),
        // 终端类型字典缓存
        termTypeDict: [],
        // 组织树数据缓存
        termTreeList: [],
        deptTree: [],
        // 缓存状态
        cacheStatus: {
          userInfo: 'idle', // idle, loading, loaded, error
          termTypeDict: 'idle',
          orgTree: 'idle'
        }
      }
    }
  },
  created() {
    this.activeName = this.$route.query.tabName || this.activeName
    // 预加载通用数据
    this.preloadSharedData()
  },
  activated() {
    this.activeName = this.$route.query.tabName || this.activeName
  },
  methods: {
    tabClick() {
      // 切换标签页时可以预加载对应数据
    },

    // 预加载共享数据
    async preloadSharedData() {
      // 加载终端类型字典
      this.loadTermTypeDict()
      // 组织树数据从store获取
      this.loadOrgTreeData()
    },

    // 加载终端类型字典
    async loadTermTypeDict() {
      if (this.sharedData.cacheStatus.termTypeDict === 'loaded') {
        return
      }

      try {
        this.sharedData.cacheStatus.termTypeDict = 'loading'
        const termTypeDict = getTermTypeDict()
        this.sharedData.termTypeDict = termTypeDict
        this.sharedData.cacheStatus.termTypeDict = 'loaded'
      } catch (error) {
        console.warn('加载终端类型字典失败:', error)
        this.sharedData.cacheStatus.termTypeDict = 'error'
      }
    },

    // 加载组织树数据
    async loadOrgTreeData() {
      try {
        this.sharedData.cacheStatus.orgTree = 'loading'

        // 使用getDeptTreeFromCache获取部门树数据
        const deptTreeResponse = await getDeptTreeFromCache()
        const deptTree = deptTreeResponse.data || []

        // 获取终端树数据
        const termTreeList = this.$store.getters.termTreeList || []

        // 如果终端树数据为空，尝试触发加载
        if (termTreeList.length === 0) {
          // 等待一段时间让store数据加载完成
          await new Promise(resolve => setTimeout(resolve, 500))
          const newTermTreeList = this.$store.getters.termTreeList || []
          this.sharedData.termTreeList = newTermTreeList
        } else {
          this.sharedData.termTreeList = termTreeList
        }

        this.sharedData.deptTree = deptTree
        this.sharedData.cacheStatus.orgTree = 'loaded'
      } catch (error) {
        console.warn('加载组织树数据失败:', error)
        this.sharedData.cacheStatus.orgTree = 'error'
      }
    },

    // 处理子组件请求用户信息
    async handleRequestUserInfo(userIds) {
      if (!Array.isArray(userIds)) {
        userIds = [userIds]
      }

      // 过滤出未缓存的用户ID
      const uncachedUserIds = userIds.filter(id => !this.sharedData.userInfoCache.has(id))

      if (uncachedUserIds.length > 0) {
        // 批量加载未缓存的用户信息
        const promises = uncachedUserIds.map(userId => this.loadSingleUserInfo(userId))
        await Promise.allSettled(promises)
      }

      // 返回所有请求的用户信息
      return this.getUserInfoFromCache(userIds)
    },

    // 提供给子组件调用的方法
    async requestUserInfo(userIds) {
      return await this.handleRequestUserInfo(userIds)
    },

    // 加载单个用户信息
    async loadSingleUserInfo(userId) {
      if (!userId) return null

      // 检查缓存
      if (this.sharedData.userInfoCache.has(userId)) {
        return this.sharedData.userInfoCache.get(userId)
      }

      // 检查是否正在加载中，避免重复请求
      const loadingKey = `loading_${userId}`
      if (this.sharedData.userInfoCache.has(loadingKey)) {
        // 如果正在加载，等待加载完成
        return new Promise((resolve) => {
          const checkInterval = setInterval(() => {
            if (this.sharedData.userInfoCache.has(userId)) {
              clearInterval(checkInterval)
              resolve(this.sharedData.userInfoCache.get(userId))
            }
          }, 50)
          // 最多等待5秒
          setTimeout(() => {
            clearInterval(checkInterval)
            resolve({
              id: userId,
              name: `用户${userId}`,
              account: userId
            })
          }, 5000)
        })
      }

      // 标记正在加载
      this.sharedData.userInfoCache.set(loadingKey, true)

      try {
        const res = await getDetail(userId)
        if (res.data) {
          const userInfo = {
            id: userId,
            name: res.data.name || res.data.account || `用户${userId}`,
            account: res.data.account,
            ...res.data
          }
          this.sharedData.userInfoCache.set(userId, userInfo)
          this.sharedData.userInfoCache.delete(loadingKey)
          return userInfo
        }
      } catch (error) {
        console.warn(`获取用户信息失败 (ID: ${userId}):`, error)
        // 设置默认用户信息
        const defaultUserInfo = {
          id: userId,
          name: `用户${userId}`,
          account: userId
        }
        this.sharedData.userInfoCache.set(userId, defaultUserInfo)
        this.sharedData.userInfoCache.delete(loadingKey)
        return defaultUserInfo
      }
    },

    // 从缓存获取用户信息
    getUserInfoFromCache(userIds) {
      const result = {}
      userIds.forEach(userId => {
        if (this.sharedData.userInfoCache.has(userId)) {
          result[userId] = this.sharedData.userInfoCache.get(userId)
        }
      })
      return result
    },

    // 获取用户名称（便捷方法）
    getUserName(userId) {
      if (!userId) return '未知用户'

      const userInfo = this.sharedData.userInfoCache.get(userId)
      if (userInfo) {
        return userInfo.name
      }

      // 如果缓存中没有，异步加载
      this.loadSingleUserInfo(userId)
      return `用户${userId}`
    }
  }
}
</script>
