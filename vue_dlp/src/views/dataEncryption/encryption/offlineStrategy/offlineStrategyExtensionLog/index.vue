<template>
  <div class="app-container">
    <div class="tree-container">
      <strategy-target-tree
        ref="strategyTargetTree"
        :showed-tree="['terminal']"
        :terminal-filter-key="terminalFilter"
        refresh-offline-terminal-status
        @data-change="strategyTargetNodeChange"
      />
    </div>
    <div class="table-container" style="overflow: auto;">
      <!-- 搜索条件区域 -->
      <div class="search-container">
        <div class="search-filters">
          <div v-if="selectedTargetName" class="filter-item">
            <label>配置目标：</label>
            <div class="target-display">
              <el-tag
                closable
                @close="clearTarget"
              >
                <svg-icon :icon-class="selectedTargetIcon" style="margin-right: 5px;"/>
                {{ selectedTargetName }}
              </el-tag>
            </div>
          </div>
          <div v-if="selectedOperatorName" class="filter-item">
            <label>操作员：</label>
            <div class="target-display">
              <el-tag
                closable
                @close="clearOperator"
              >
                {{ selectedOperatorName }}
              </el-tag>
            </div>
          </div>
          <div class="filter-item">
            <label>操作类型：</label>
            <el-select v-model="query.logType" placeholder="全部" size="mini" style="width: 150px;" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="配置离线策略延长时间" :value="0"></el-option>
              <el-option label="终端同步" :value="1"></el-option>
              <el-option label="同步至云服务" :value="2"></el-option>
              <el-option label="删除离线策略延长配置" :value="3"></el-option>
            </el-select>
          </div>

          <div class="filter-item">
            <TimeQuery
              ref="timeQuery"
              :is-clearable="true"
              :auto-current-value="true"
              @getTimeParams="getTimeParams"
            />
          </div>
          <el-button size="mini" @click="clearFilters">{{ $t('button.reset') }}</el-button>
          <el-button type="primary" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button
            type="primary"
            icon="el-icon-refresh"
            size="mini"
            :loading="syncLoading"
            @click="handleSyncLogs"
          >
            同步日志
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <grid-table
        ref="dataTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @row-click="handleLogClick"
      />
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      title="日志详情"
      :visible.sync="detailDialogVisible"
      width="700px"
    >
      <div v-if="selectedLog" class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item label="日志ID">
            {{ selectedLog.id }}
          </el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getOperationTypeTagType(selectedLog.logType)" size="small">
              {{ getOperationTypeText(selectedLog.logType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedLog.target" label="配置目标">
            {{ getTargetDisplayName(selectedLog.target, selectedLog.targetType) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedLog.targetType" label="配置类型">
            {{ getTypeDisplayName(selectedLog.targetType, 'backend') }}
          </el-descriptions-item>
          <el-descriptions-item label="操作员">
            {{ getOperatorName(selectedLog) }}
          </el-descriptions-item>
          <el-descriptions-item label="操作时间">
            {{ selectedLog.operateTime }}
          </el-descriptions-item>
          <el-descriptions-item label="详细内容" :span="2">
            <div class="log-content">{{ selectedLog.logContent }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getExtensionLogList,
  syncLogsFromCloud
} from '@/api/dataEncryption/encryption/offlineStrategyExtensionLog'
import { getTermTypeDict } from '@/utils/dictionary'
import { findNodeLabel } from '@/utils/tree'

export default {
  name: 'OfflineStrategyExtensionLog',
  props: {
    // 从父组件接收共享数据
    sharedData: {
      type: Object,
      default: () => ({
        userInfoCache: new Map(),
        adminInfoCache: new Map(),
        termTypeDict: [],
        termTreeList: [],
        deptTree: [],
        cacheStatus: {}
      })
    },
    // 父组件实例引用
    parentComponent: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 查询条件
      query: {
        page: 1,
        limit: 20,
        objectType: undefined,
        objectId: undefined,
        configTarget: '',
        logType: '',
        operator: '',
        startTime: '',
        endTime: ''
      },
      // 选中的目标
      selectedTargetName: '',
      selectedTargetId: null,
      selectedTargetType: null,
      selectedTargetIcon: '',
      // 选中的操作员
      selectedOperatorName: '',
      selectedOperatorId: null,
      selectedOperatorIcon: '',
      // 表格列配置
      colModel: [
        { prop: 'logType', label: '操作类型', width: '150', formatter: this.operationTypeFormatter },
        { prop: 'target', label: '配置目标', width: '200', formatter: this.targetFormatter },
        { prop: 'targetType', label: '配置类型', width: '120', formatter: this.typeFormatter },
        { prop: 'logContent', label: '操作详情', width: '300', formatter: this.operationDetailFormatter },
        { prop: 'userId', label: '操作员', width: '120', formatter: this.operatorFormatter },
        { prop: 'operateTime', label: '操作时间', width: '150' },
        {
          prop: 'actions',
          label: '操作',
          type: 'button',
          fixedWidth: '100',
          fixed: 'right',
          buttons: [
            { label: '详情', click: this.handleLogClick }
          ]
        }
      ],
      // 总数
      totalLogs: 0,
      // 选中的日志
      selectedLog: null,
      detailDialogVisible: false,
      // 操作员信息缓存
      operatorCache: new Map(),
      // 同步日志加载状态
      syncLoading: false
    }
  },
  computed: {
    // 解析选中日志的logContent
    parsedLogContent() {
      if (!this.selectedLog || !this.selectedLog.logContent) {
        return null
      }
      try {
        return JSON.parse(this.selectedLog.logContent)
      } catch (e) {
        return null
      }
    }
  },
  created() {
    // 初始化时不自动加载，等待用户操作
  },
  mounted() {
    // 设置TimeQuery组件的默认时间范围
    this.$nextTick(() => {
      this.initializeTimeQuery()
    })
  },
  methods: {
    // 初始化TimeQuery组件的默认时间
    initializeTimeQuery() {
      if (this.$refs.timeQuery) {
        const today = new Date()
        const todayStr = this.formatDate(today)

        // 计算上个月的今天
        const lastMonth = new Date(today)
        lastMonth.setMonth(today.getMonth() - 1)
        const lastMonthStr = this.formatDate(lastMonth)

        // 设置默认时间：单个日期模式默认今天，时间段模式默认上个月到今天
        const defaultDateObj = {
          createDate: todayStr,        // 单个日期默认今天
          startDate: lastMonthStr,     // 时间段开始日期默认上个月
          endDate: todayStr,           // 时间段结束日期默认今天
          isTimes: false               // 默认单个日期模式
        }

        this.$refs.timeQuery.setDate(defaultDateObj)
      }
    },
    // 格式化日期为 YYYY-MM-DD 格式
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    // 终端过滤器
    terminalFilter(node) {
      if (node.type == 3) {
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return true
    },
    // 策略目标节点变化
    strategyTargetNodeChange(tabName, checkedNode) {
      // 组织树（terminal）：设置配置目标筛选条件
      this.selectedTargetName = checkedNode.label
      this.selectedTargetId = checkedNode.dataId
      this.selectedTargetType = checkedNode.type
      this.selectedTargetIcon = this.getNodeIcon(checkedNode)
      this.query.targetType = this.frontendToBackendType(checkedNode.type)
      this.query.target = checkedNode.dataId
      this.handleFilter()
    },
    // 清空目标选择
    clearTarget() {
      this.clearTargetSelection()
      this.handleFilter()
    },
    // 清空目标选择（不触发搜索）
    clearTargetSelection() {
      this.selectedTargetName = ''
      this.selectedTargetId = null
      this.selectedTargetType = null
      this.selectedTargetIcon = ''
      this.query.targetType = undefined
      this.query.target = undefined
    },
    // 清空操作员选择
    clearOperator() {
      this.clearOperatorSelection()
      this.handleFilter()
    },
    // 清空操作员选择（不触发搜索）
    clearOperatorSelection() {
      this.selectedOperatorName = ''
      this.selectedOperatorId = null
      this.selectedOperatorIcon = ''
      this.query.userId = ''
    },
    // 数据API
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getExtensionLogList(searchQuery).then(async res => {
        this.totalLogs = res.data.total || 0

        // 批量预加载用户信息
        if (res.data.items && res.data.items.length > 0) {
          await this.preloadUserInfoForLogs(res.data.items)
        }

        return res
      })
    },

    // 批量预加载用户信息
    async preloadUserInfoForLogs(logs) {
      // 收集所有需要查询的用户ID
      const userIds = [...new Set(logs.map(log => log.userId).filter(id => id))]

      if (userIds.length === 0) {
        return
      }

      try {
        if (this.parentComponent && this.parentComponent.requestUserInfo) {
          await this.parentComponent.requestUserInfo(userIds)
        }
      } catch (error) {
        console.warn('日志组件批量预加载用户信息失败:', error)
      }
    },
    // 时间参数变化
    getTimeParams(timeParams) {
      if (timeParams) {
        // 处理时间段查询
        if (timeParams.isTimes && timeParams.startDate && timeParams.endDate) {
          this.query.startTime = timeParams.startDate
          this.query.endTime = timeParams.endDate
        } else if (!timeParams.isTimes && timeParams.createDate) {
          this.query.startTime = timeParams.createDate
          this.query.endTime = timeParams.createDate
        } else {
          this.query.startTime = ''
          this.query.endTime = ''
        }
      } else {
        this.query.startTime = ''
        this.query.endTime = ''
      }
      // 移除自动搜索，只有点击搜索按钮才搜索
    },
    // 搜索过滤
    handleFilter() {
      this.$refs.dataTable.execRowDataApi()
    },
    // 清空筛选条件
    clearFilters() {
      this.query.configTarget = ''
      this.query.logType = ''
      this.query.startTime = ''
      this.query.endTime = ''
      // 清空目标选择
      this.clearTargetSelection()
      // 清空操作员选择
      this.clearOperatorSelection()
      // 重新设置默认时间
      this.$nextTick(() => {
        this.initializeTimeQuery()
      })
      this.handleFilter()
    },

    // 同步日志
    handleSyncLogs() {
      this.$confirmBox(
        '确定要从云服务同步最新的离线策略延长日志吗？\n同步过程可能需要一些时间，请耐心等待。',
        '同步日志确认',
        {
          confirmButtonText: '确定同步',
          cancelButtonText: '取消',
          type: 'info'
        }
      ).then(() => {
        this.syncLogsFromCloud()
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 执行同步日志
    async syncLogsFromCloud() {
      this.syncLoading = true
      try {
        const res = await syncLogsFromCloud()

        // 根据返回结果显示成功消息
        let successMessage = '日志同步成功'
        if (res.data && typeof res.data.syncCount !== 'undefined') {
          successMessage = `日志同步成功，共同步了 ${res.data.syncCount} 条日志`
        } else if (res.data && typeof res.data === 'number') {
          successMessage = `日志同步成功，共同步了 ${res.data} 条日志`
        } else if (res.message) {
          successMessage = res.message
        }

        this.$message({
          message: successMessage,
          type: 'success',
          duration: 3000
        })

        // 同步成功后刷新页面数据
        this.handleFilter()
      } finally {
        this.syncLoading = false
      }
    },

    // 日志点击
    handleLogClick(row) {
      this.selectedLog = row
      this.detailDialogVisible = true
    },
    // 操作类型格式化
    operationTypeFormatter(row) {
      return this.getOperationTypeText(row.logType)
    },
    // 操作详情格式化
    operationDetailFormatter(row) {
      // 根据logType处理不同的显示内容
      switch (row.logType) {
        case 0: // 配置离线策略延长时间
          if (row.logContent) {
            try {
              const content = JSON.parse(row.logContent)
              const expireTime = content.expireTime ? this.formatTimestamp(content.expireTime) : ''
              return `过期时间: ${expireTime}`
            } catch (e) {
              return row.logContent || ''
            }
          }
          return ''
        case 1: // 终端同步
          return row.logContent || '终端同步成功'
        case 2: // 同步至云服务
          return row.logContent || '同步至云服务'
        case 3: // 删除离线策略延长配置
          return row.logContent || '删除配置'
        default:
          return row.logContent || ''
      }
    },
    // 获取操作类型文本
    getOperationTypeText(logType) {
      const typeMap = {
        0: '配置离线策略延长时间',
        1: '终端同步',
        2: '同步至云服务',
        3: '删除离线策略延长配置'
      }
      return typeMap[logType] || '未知操作'
    },
    // 获取操作类型标签类型
    getOperationTypeTagType(logType) {
      const tagTypeMap = {
        0: 'primary',   // 配置离线策略延长时间 - 蓝色
        1: 'success',   // 终端同步 - 绿色
        2: 'warning',   // 同步至云服务 - 橙色
        3: 'danger'     // 删除离线策略延长配置 - 红色
      }
      return tagTypeMap[logType] || 'info'
    },
    // 操作员格式化
    operatorFormatter(row) {
      return this.getOperatorName(row)
    },
    // 目标格式化
    targetFormatter(row) {
      return this.getTargetDisplayName(row.target, row.targetType)
    },
    // 类型格式化
    typeFormatter(row) {
      return this.getTypeDisplayName(row.targetType, 'backend')
    },
    // 获取操作员名称
    getOperatorName(log) {
      if (!log || !log.userId) {
        return '未知操作员'
      }

      // 先从共享缓存中查找
      if (this.sharedData.userInfoCache.has(log.userId)) {
        const userInfo = this.sharedData.userInfoCache.get(log.userId)
        return userInfo.name
      }

      // 如果缓存中没有，异步加载（但不阻塞渲染）
      this.loadUserInfoAsync(log.userId)

      return `操作员${log.userId}`
    },

    // 异步加载用户信息（防止重复调用）
    async loadUserInfoAsync(userId) {
      if (!userId || this.sharedData.userInfoCache.has(userId)) {
        return
      }

      // 防止重复加载
      if (this._loadingUsers && this._loadingUsers.has(userId)) {
        return
      }

      if (!this._loadingUsers) {
        this._loadingUsers = new Set()
      }
      this._loadingUsers.add(userId)

      try {
        if (this.parentComponent && this.parentComponent.requestUserInfo) {
          await this.parentComponent.requestUserInfo([userId])
          // 触发视图更新
          this.$forceUpdate()
        }
      } catch (error) {
        console.warn(`获取操作员信息失败 (ID: ${userId}):`, error)
      } finally {
        this._loadingUsers.delete(userId)
      }
    },
    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    // 获取节点图标
    getNodeIcon(node) {
      // 根据组织树的图标配置获取图标
      const iconTypes = {
        typeKey: 'dataType',
        'G': 'terminalGroup',
        3: 'terminalGroup',
        1: 'terminal',
        2: 'user',
        4: 'userGroup'
      }

      // 使用共享的终端类型字典
      if (this.sharedData.termTypeDict && this.sharedData.termTypeDict.length > 0) {
        this.sharedData.termTypeDict.forEach(item => {
          iconTypes[item.value] = item.icon
        })
      } else {
        // 如果共享数据还没加载，使用原来的方法
        getTermTypeDict().forEach(item => {
          iconTypes[item.value] = item.icon
        })
      }

      // 优先使用dataType，其次使用type
      const type = node.dataType !== undefined ? node.dataType : node.type
      let iconClass = iconTypes[type]

      // 如果没有找到对应图标，根据节点类型使用默认图标
      if (!iconClass) {
        if (node.type === 1) {
          iconClass = 'terminal'
        } else if (node.type === 3 || node.dataType === 'G') {
          iconClass = 'terminalGroup'
        } else if (node.type === 2) {
          iconClass = 'user'
        } else if (node.type === 4) {
          iconClass = 'userGroup'
        } else {
          iconClass = 'terminal'
        }
      }

      // 处理未使用的USB终端或离线终端（添加-x后缀）
      if ((iconClass.indexOf('usb-') === 0 || iconClass.indexOf('offline-') === 0) && !node.dataCode) {
        iconClass += '-x'
      }

      return iconClass
    },
    // 获取目标显示名称
    getTargetDisplayName(target, targetType) {
      if (!target) return '未知目标'

      try {
        const TERMINAL_CONFIG = 2  // 终端配置
        const GROUP_CONFIG = 1     // 分组配置

        if (targetType === TERMINAL_CONFIG) {
          // 终端：从终端树中查找
          const termTreeList = this.termTreeList()
          if (termTreeList && termTreeList.length > 0) {
            // 查找前端类型为1的终端节点
            const frontendTerminalType = this.backendToFrontendType(targetType)
            const termNode = termTreeList.find(node => node.dataId == target && node.type == frontendTerminalType)
            if (termNode) {
              // 返回格式：终端名称（终端编号）
              const terminalName = termNode.name || termNode.label
              return terminalName || target
            }
          }
        } else if (targetType === GROUP_CONFIG) {
          // 分组：从部门树中查找
          const deptTree = this.deptTree()
          if (deptTree && deptTree.length > 0) {
            const groupName = findNodeLabel(deptTree, target, 'dataId')
            return groupName || target
          }
        }
      } catch (error) {
        console.warn('获取目标显示名称失败:', error)
      }

      return target
    },
    // 获取终端树列表
    termTreeList() {
      return this.sharedData.termTreeList || this.$store.getters.termTreeList || []
    },
    // 获取部门树
    deptTree() {
      return this.sharedData.deptTree || this.$store.getters.deptTree || []
    },
    /**
     * 后端类型转前端类型
     * 后端：targetType=2(终端配置), targetType=1(分组配置) -> 前端：type=1(终端), type=3(分组)
     * @param {number} backendType 后端类型值
     * @returns {number} 前端类型值
     */
    backendToFrontendType(backendType) {
      const typeMap = {
        2: 1, // 终端配置：后端targetType=2 -> 前端type=1
        1: 3  // 分组配置：后端targetType=1 -> 前端type=3
      }
      return typeMap[backendType] || null
    },
    /**
     * 获取类型显示名称
     * @param {number} type 类型值（前端或后端）
     * @param {string} typeSource 类型来源：'frontend' 或 'backend'
     * @returns {string} 类型显示名称
     */
    getTypeDisplayName(type, typeSource = 'backend') {
      if (typeSource === 'frontend') {
        // 前端类型
        const frontendTypeNames = {
          1: '终端',
          3: '分组'
        }
        return frontendTypeNames[type] || '未知类型'
      } else {
        // 后端类型
        const backendTypeNames = {
          2: '终端配置',
          1: '分组配置'
        }
        return backendTypeNames[type] || '未知类型'
      }
    },
    /**
     * 前端类型转后端类型
     * 前端：type=1(终端), type=3(分组) -> 后端：targetType=2(终端配置), targetType=1(分组配置)
     * @param {number} frontendType 前端类型值
     * @returns {number} 后端类型值
     */
    frontendToBackendType(frontendType) {
      const typeMap = {
        1: 2, // 终端：前端type=1 -> 后端targetType=2
        3: 1  // 分组：前端type=3 -> 后端targetType=1
      }
      return typeMap[frontendType] || null
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  overflow: auto;
  padding: 5px 0 20px 0;
}

.search-container {
  background: #0a111a;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.search-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;

  label {
    color: #a8b5c5;
    font-size: 13px;
    white-space: nowrap;
  }

  .target-display {
    .el-tag {
      background: #12202e;
      border-color: #1a2636;
      color: #00a4ff;

      .el-tag__close {
        color: #a8b5c5;

        &:hover {
          background: #1a2636;
          color: #ff4d4f;
        }
      }
    }
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #0a111a;
  border-radius: 4px;
  margin-bottom: 15px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.log-count {
  color: #a8b5c5;
  font-size: 13px;
}

.show-detail-panel {
  .log-content {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #606266;
    word-break: break-all;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
  }

  >>>.el-descriptions-item__label {
    min-width: 120px;
  }

  >>>.el-descriptions-item__content {
    min-width: 120px;
    max-width: 100%;
  }
}

.sync-details {
  margin-top: 10px;
}

.sync-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 8px 12px;
    border: 1px solid #1a2636;
    text-align: left;
    font-size: 12px;
  }

  th {
    background: #12202e;
    color: #00a4ff;
  }

  td {
    color: #a8b5c5;
  }
}

.clean-tip {
  margin-top: 8px;
  color: #a8b5c5;
  font-size: 12px;
}

>>>.el-form-item__label {
  color: #ccc;
}

.dialog-footer {
  text-align: right;
}
</style>
