<template>
  <div class="app-container">
    <div style="height: calc(100% - 60px); overflow: auto;">
      <Form
        ref="temp"
        class="sensitive-config"
        :model="temp"
        :rules="rules"
        label-position="left"
        :label-width="$store.getters.language === 'en' ? '200px' : '145px'"
      >
        <FormItem :label="$t('pages.outgoingTimes')" prop="outSendTimes">
          <el-input-number v-model="temp.outSendTimes" style="width:120px" :min="1" :max="9999" :controls="false" :disabled="outSendTimes" /> {{ $t('pages.require_Msg7') }}
          <el-checkbox v-model="outSendTimes" style="margin-left:10px" @change="temp.outSendTimes=undefined">{{ $t('pages.noLimit') }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.sensitiveConfig_Msg') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
        </FormItem>
        <FormItem :label="$t('pages.maximumInterval')" prop="maxDay">
          <el-input-number v-model="temp.maxDay" style="width:120px" :min="1" :max="9999" :controls="false" :disabled="maxDay"/> {{ $t('pages.day1') }}
          <el-checkbox v-model="maxDay" style="margin-left:10px" @change="temp.maxDay=undefined">{{ $t('pages.notLimit') }}</el-checkbox>      </FormItem>
        <FormItem :label="$t('pages.sensitiveFileRules')">
          <el-radio-group v-model="temp.matchType">
            <el-radio :label="1">{{ $t('pages.programFingerMatching') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.pathMatching') }}</el-radio>        </el-radio-group>
        </FormItem>
        <FormItem v-if="temp.matchType===0" :label="$t('pages.outsendConfig')">
          <div class="outsend">
            <div v-for="item in alarmType" :key="item.value">
              <el-checkbox v-model="item.active" :false-label="0" :true-label="1">
                {{ item.alarmDesc }}
              </el-checkbox>
            </div>
          </div>
        </FormItem>
        <FormItem v-else :label="$t('pages.outsendConfig')">
          <div class="outsend">
            <div v-for="item in alarmType" :key="item.value">
              <el-checkbox v-model="item.active" :false-label="0" :true-label="1">
                {{ item.alarmDesc }}
              </el-checkbox>
            </div>
          </div>
        </FormItem>
        <FormItem :label="$t('pages.otherParam')">
          <el-checkbox v-model="temp.notLimitFile" :true-label="1" :false-label="0">
            {{ $t('pages.unlimitedFiles') }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.sensitiveConfig_Msg1') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox><br/>
        </FormItem>
        <!-- <FormItem v-permission="'E22'">
          <el-checkbox v-model="temp.isAutoDec" :true-label="1" :false-label="0">
            {{ $t('pages.sensitiveConfig_Msg2') }}
          </el-checkbox><br/>
        </FormItem> -->
        <!-- <FormItem>
          <el-checkbox v-model="temp.isShowTip" :true-label="1" :false-label="0">
            {{ $t('pages.sensitiveConfig_Msg3') }}
          </el-checkbox><br/>
        </FormItem> -->
      </Form>
    </div>
    <el-button type="primary" size="mini" style="position: absolute; left: 500px; bottom: 30px;" :loading="submitting" @click="updateConfig">{{ $t('button.save') }}</el-button>
  </div>
</template>

<script>
import { getConfig, updateConfig, getLossType, updateLossType } from '@/api/dataEncryption/encryption/sensitiveFileConfig'

export default {
  name: 'SensitiveConfig',
  data() {
    return {
      temp: {
        outSendTimes: undefined,
        maxDay: undefined,
        matchType: 0,
        notLimitFile: 0,
        isAutoDec: 0,
        isShowTip: 0
      },
      outSendTimes: false,
      maxDay: false,
      submitting: false,
      alarmOptions: []
    }
  },
  computed: {
    rules() {
      return {
        outSendTimes: [
          { required: !this.outSendTimes, message: this.$t('pages.sensitiveConfig_Msg4'), trigger: 'blur' }
        ],
        maxDay: [
          { required: !this.maxDay, message: this.$t('pages.sensitiveConfig_Msg5'), trigger: 'blur' }
        ]
      }
    },
    alarmType() {
      if (this.temp.matchType === 0) {
        return this.alarmOptions.filter(item => item.isSupportPathMatch === 1)
      } else {
        return this.alarmOptions.filter(item => item.isSupportMd5Match === 1)
      }
    }
  },
  mounted() {
    this.getConfig()
  },
  methods: {
    getConfig() {
      getConfig().then(res => {
        getLossType().then(res => {
          this.alarmOptions = res.data
        })
        this.temp = res.data
        if (this.temp.outSendTimes === 0) {
          this.temp.outSendTimes = undefined
          this.outSendTimes = true
        }
        if (this.temp.maxDay === 0) {
          this.temp.maxDay = undefined
          this.maxDay = true
        }
      })
    },
    updateConfig() {
      this.$refs['temp'].validate(valid => {
        if (valid) {
          this.submitting = true
          const tempData = Object.assign({}, this.temp)
          tempData.outSendTimes = this.outSendTimes ? 0 : tempData.outSendTimes
          tempData.maxDay = this.maxDay ? 0 : tempData.maxDay
          updateConfig(tempData).then(res => {
            updateLossType(this.alarmType).then(res => {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
              this.getConfig()
              this.submitting = false
            }).catch(err => {
              console.log(err)
              this.submitting = false
            })
          }).catch(err => {
            console.log(err)
            this.submitting = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .sensitive-config{
    margin: 20px 0 0 30px;
    width: 725px;
    >>>.el-form-item__label{
      color: #fff;
      font-size: 15px;
    }
    .outsend{
      display: flex;
      flex-wrap:wrap;
      div{
        width:220px;
      }
      div:nth-child(2n){
        width:220px;
      }
    }
  }
  .el-form-item{
    margin-bottom: 15px;
  }

</style>
