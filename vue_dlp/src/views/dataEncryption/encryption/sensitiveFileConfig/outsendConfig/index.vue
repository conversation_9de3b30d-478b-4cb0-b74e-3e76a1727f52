<template>
  <div class="sensitive-config">
    <label>{{ $t('pages.outsendConfig') }}</label>
    <div class="config">
      <div v-for="item in alarmType" :key="item.value" >
        <el-switch
          v-model="item.active"
          :inactive-value="0"
          :active-value="1"
          active-color="#409EFF"
          inactive-color="#909399"
          :active-text="item.alarmDesc"
        >
        </el-switch>
      </div>
    </div>
    <el-button type="primary" size="mini" :loading="submitting" @click="updateConfig">{{ $t('button.save') }}</el-button>
  </div>
</template>

<script>
import { getLossType, updateLossType } from '@/api/dataEncryption/encryption/sensitiveFileConfig'
export default {
  name: 'OutsendConfig',
  data() {
    return {
      alarmType: [],
      submitting: false
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    getConfig() {
      getLossType().then(res => {
        this.alarmType = res.data
      })
    },
    updateConfig() {
      this.submitting = true
      updateLossType(this.alarmType).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.getConfig()
        this.submitting = false
      }).catch(err => {
        console.log(err)
        this.submitting = false
      })
    }

  }
}
</script>

<style lang="scss">
  .sensitive-config{
    .el-button{
      width: 80px;
      margin-top: 20px;
      float: right;
    }
    width: 360px;
    margin: 20px 0 0 80px;
    .el-switch__label{
        color: #fff;
    }
    .config{
      display: flex;
      flex-wrap:wrap;
      div{
        margin: 10px 0 10px 0;
        width:220px;
      }
      div:nth-child(2n){
        width:140px;
      }
    }
  }

</style>
