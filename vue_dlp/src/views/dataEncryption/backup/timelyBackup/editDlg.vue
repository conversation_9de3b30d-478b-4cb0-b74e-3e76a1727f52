<!-- 即时备份策略 -->
<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('route.timelyBackupStrategy')"
      :stg-code="270"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <FormItem :label="$t('pages.includeFile')">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.includeFileTips">
                <br slot="br"/>
                <span slot="space" style="margin-left:12px" />
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-row v-for="(value, index) in rowTemp.includeFileList" :key="index" style="padding-bottom: 1px">
            <el-col :span="22" :title="`${value.includeDir}${value.includeDir && value.includeSuffix ? ';': ''}${value.includeSuffix}`" style="white-space: nowrap;overflow:hidden; text-overflow: ellipsis;display: inline-block;background-color: #f5f5f5;border: 1px solid #aaa;padding: 0 15px;border-radius: 4px;">
              {{ `${value.includeDir}${value.includeDir && value.includeSuffix ? ';': ''}${value.includeSuffix}` }}
            </el-col>
            <el-col v-if="formable" :span="1" style="float:right;width: 50px;cursor: pointer;color: #68a8d0;">
              <i v-show="rowTemp.includeFileList.length < 50" class="el-icon-circle-plus-outline" @click="addFileItemView(1)"></i>
              <i class="el-icon-edit-outline" @click="uptFileItemView(index, 1)"></i>
              <i class="el-icon-remove-outline" @click="deleteFileItem(index)"></i>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('pages.excludeFile')">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.excludeFileTips">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-row v-for="(value, index) in rowTemp.excludeFileList" :key="index" style="padding-bottom: 1px">
            <el-col :span="22" :title="`${value.excludeDir}${value.excludeDir && value.excludeSuffix ? ';': ''}${value.excludeSuffix}`" style="white-space: nowrap;overflow:hidden; text-overflow: ellipsis;display: inline-block;background-color: #f5f5f5;border: 1px solid #aaa;padding: 0 15px;border-radius: 4px;">
              <template v-if="!value.excludeDir && !value.excludeSuffix">&nbsp;</template>
              <template v-else>{{ `${value.excludeDir}${value.excludeDir && value.excludeSuffix ? ';': ''}${value.excludeSuffix}` }}</template>
            </el-col>
            <el-col v-if="formable" :span="1" style="float:right;width: 50px;cursor: pointer;color: #68a8d0;">
              <i v-show="rowTemp.excludeFileList.length < 50" class="el-icon-circle-plus-outline" @click="addFileItemView(2)"></i>
              <i class="el-icon-edit-outline" @click="uptFileItemView(index, 2)"></i>
              <i class="el-icon-remove-outline" @click="deleteExcludeFileItem(index)"></i>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('pages.driverType')" prop="diskType">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.backupDriverTypeTips') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-select v-model="rowTemp.diskType" :disabled="!formable" clearable multiple style="width: 92%">
            <el-option v-for="item in diskTypeOptions" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.backupFilterProcess')" prop="process">
          <tag :list="rowTemp.excludeProc" trim-able border class="input-with-button" input-length="200" :limit-size="procMaxLength" :disabled="!formable" min-height="30px" style="display: inline-table;width: calc(100% - 115px);"/>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importAppFromPublicLib')" placement="top">
            <el-button v-if="formable" type="primary" size="mini" @click="showAppImport">
              <svg-icon icon-class="import"/>
            </el-button>
          </el-tooltip>
          <el-button v-if="formable" :disabled="!formable" size="mini" class="clear-btn" @click="handleAppClear">
            {{ $t('button.clear') }}
          </el-button>
        </FormItem>
        <FormItem :label="$t('pages.backupFileSizeSimple')">
          <backup-size-input :value="rowTemp.fileSize" :disabled="!formable"/>
        </FormItem>
        <FormItem :label="$t('pages.regularBackup')">
          <el-switch v-model="rowTemp.regularBackup" :disabled="!formable" style="margin-left: 10px" :active-value="1" :inactive-value="0" @change="cleanAbleChange"/>
        </FormItem>
        <template v-if="rowTemp.regularBackup">
          <FormItem :label="$t('pages.scanMode')">
            <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.diskScan_Msg16') }}<br/>
                {{ $t('pages.diskScan_Msg17') }}<br/>
                {{ $t('pages.diskScan_Msg18') }}<br/>
                {{ $t('pages.diskScan_Msg19') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-select v-model="rowTemp.scanMode" :disabled="!formable" style="width: 150px">
              <el-option v-for="item in scanModeOptions" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
          </FormItem>
          <FormItem v-show="rowTemp.scanMode === 0" prop="scanMode">
            <el-row style="text-align: right;">
              <el-col :span="6">
                <span>{{ $t('pages.diskScan_Msg20') }}</span>
              </el-col>
              <el-col :span="4">
                <el-input-number v-model="rowTemp.cpuMem.cpu" :disabled="!formable" prop="cpu" controls-position="right" :min="1" :max="100" ></el-input-number>
              </el-col>
              <el-col :span="7">
                <span>{{ $t('pages.diskScan_Msg21') }}</span>
              </el-col>
              <el-col :span="4">
                <el-input-number v-model="rowTemp.cpuMem.mem" :disabled="!formable" prop="mem" controls-position="right" :min="1" :max="100" ></el-input-number>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem :label="$t('pages.backupScanTime')" prop="scanCycle" >
            <el-cascader
              v-model="rowTemp.scanCycle"
              class="scan-cycle"
              :disabled="!formable"
              :options="scanCycleOpts"
              :props="{ expandTrigger: 'hover' }"
              @change="handleScanCycleChange"
            />
          </FormItem>
          <FormItem :label="$t('pages.scanPeriod')" prop="scanTime" >
            <el-time-picker
              v-model="rowTemp.scanTime"
              :disabled="!formable"
              style="width: 260px"
              is-range
              :clearable="false"
              format="HH:mm"
              value-format="HH:mm"
              :range-separator="$t('pages.till')"
            />
          </FormItem>
        </template>
      </template>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
    <el-dialog
      ref="configDir"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.configScanDir')"
      :visible.sync="dirVisible"
      width="650px"
    >
      <el-card class="box-card" style="margin: 5px;">
        <div slot="header" class="clearFix">
          <span>{{ $t('table.pointDirectory') }}</span>
        </div>
        <div style="line-height: 15px;margin-top: -10px">
          {{ $t('pages.commonDir') }}：
          <el-button type="text" @click="selectAll(1)">{{ $t('button.selectAll') }}</el-button>
          <el-button type="text" @click="selectAll(2)">{{ $t('button.cancelSelectAll') }}</el-button>
          <el-checkbox-group v-model="checkedCommonDir" style="margin-top: 6px;">
            <el-row>
              <el-col v-for="(item, index) in (dirType === 1 ? commonsDir : exCommonsDir)" :key="index" :span="8">
                <el-checkbox :label="item.name" @change="commonDirChange($event)">
                  <span :title="item.title" class="ellipsis label-text">{{ item.name }}</span>
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </div>
        <div style="display: flex;align-items: center; line-height: 22px;">
          <span style="width: 70px;">
            {{ $t('pages.specifyDir') }}
          </span>
          <el-tooltip class="item" effect="dark" placement="bottom-start" style="margin-left: -9px;">
            <div slot="content">
              {{ $t('pages.diskScan_Msg33') }}<br>
              {{ $t('pages.backupScanDirRelativePathMsg') }}<br>
              {{ $t('pages.diskScan_Msg37') }}<br>
              {{ $t('pages.backupScanDirTips') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag :list="specifyDir" trim-able border class="input-with-button" input-length="256" :limit-size="50" style="min-height: 30px;background-color:#f5f5f5;margin-left: 5px;"/>
        </div>
        <span style="font-size: 12px;color: #0c60a5">{{ $t('pages.backupScanDirExample') }}</span>
      </el-card>
      <el-card class="box-card" style="margin: 5px;">
        <div slot="header" class="clearFix">
          <span>{{ $t('pages.backupSpecifySuffix') }}</span>
        </div>
        <div style="display: flex;align-items: center; line-height: 22px;">
          <span style="width: 70px;">
            {{ $t('pages.suffixes') }}
          </span>
          <el-tooltip class="item" effect="dark" placement="bottom-start" style="margin-left: -9px;">
            <div slot="content">
              {{ $t('pages.backupSuffixTips') }}<br/>
              {{ $t('pages.inputMaxLength', { num: 256 }) }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag :list="rowTemp.suffix" trim-able border class="input-with-button" input-length="256" :limit-size="suffixMaxLength" style="width: calc(100% - 115px); background-color:#f5f5f5; min-height: 30px;" @tagChange="suffixChange"/>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" :disabled="!formable" size="mini" @click="handleFileSuffixImport()">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
          <el-button v-if="formable" :disabled="!formable" size="small" class="clear-btn" style="height: 30px;margin-top: 2px" @click="handleClear">
            {{ $t('button.clear') }}
          </el-button>
        </div>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveConfigDir(dirType)">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancelConfigDir ()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <app-select-dlg ref="appSelectDlg" :append-to-body="true" @select="handleAppImport"/>
  </div>
</template>

<script>
import BackupSizeInput from '../component/BackupSizeInput'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import { createStrategy, updateStrategy, getStrategyByName } from '@/api/dataEncryption/smartBackup/timelyBackup'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg';

export default {
  name: 'TimelyBackupDlg',
  components: { BackupSizeInput, FileSuffixLibImport, AppSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: undefined,
        entityId: undefined,
        backupStrg: [],
        scanCycle: [1],
        scanMode: 1,
        cpuMem: { cpu: 50, mem: 50 },
        scanTime: ['00:00', '23:59'],
        fileSize: {
          minSize: 0,
          minSizeUnit: 1,
          maxSize: 500,
          maxSizeUnit: 1
        },
        diskType: [1],
        suffix: [],
        includeFileList: [
          {
            includeDir: '',
            includeSuffix: '.doc|.docx|.xls|.xlsx|.ppt|.pptx|.wps|.pdf|.odt|.odp|.ods|.odf|.odg|.vsd|.rtf|.xps|.epub|.et|.dps'
          }
        ],
        excludeFileList: [
          {
            excludeDir: '$:\\Windows\\|$:\\Program Files\\|$:\\Program Files (x86)\\|$:\\Boot\\',
            excludeSuffix: ''
          },
          {
            excludeDir: '\\$RECYCLE.BIN\\|\\Tencent\\QQWubi\\|\\Microsoft\\Ime\\|\\SogouWB\\|\\SogouPY\\|\\.svn\\|#APPDATA#|#OFFICETMP#',
            excludeSuffix: ''
          },
          {
            excludeDir: '',
            excludeSuffix: '.tmp|.temp'
          }
        ],
        excludeProc: []
      },
      rules: {
        diskType: [{ required: true, validator: this.diskTypeValid, trigger: 'blur,change' }],
        scanMode: [{ required: true, validator: this.scanModeValid, trigger: 'blur,change' }],
        scanCycle: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }],
        scanTime: [{ required: true, validator: this.scanTimeValid, trigger: 'blur,change' }]
      },
      showAble: false,
      addable: true,
      slotName: undefined,
      rowTemp: {},
      regularBackup: 0,
      selection: [],
      suffixMaxLength: 50,
      procMaxLength: 200,
      checkedCommonDir: [],
      checkedRealDirValue: [],
      specifyDir: [],
      dirVisible: false,
      dirType: 1,
      singleRowTemp: {},
      // 包含的常用目录
      commonsDir: [
        {
          name: this.$t('pages.scanDesktop'),
          value: '#DESKTOP#',
          title: this.$t('pages.diskScan_Desktop')
        },
        {
          name: this.$t('pages.scanDocument'),
          value: '#MYDOCUMENTS#',
          title: this.$t('pages.diskScan_Documents')
        },
        {
          name: this.$t('pages.scanFavorites'),
          value: '#FAVORITES#',
          title: this.$t('pages.diskScan_Favorites')
        },
        {
          name: this.$t('pages.scanMusic'),
          value: '#MYMUSICS#',
          title: this.$t('pages.diskScan_Musics')
        },
        {
          name: this.$t('pages.scanPictures'),
          value: '#MYPICTURES#',
          title: this.$t('pages.diskScan_Pictures')
        },
        {
          name: this.$t('pages.scanVideo'),
          value: '#MYVIDEOS#',
          title: this.$t('pages.diskScan_Videos')
        },
        {
          name: this.$t('pages.scanAppData'),
          value: '#APPDATA#',
          title: this.$t('pages.diskScan_AppData') + '(' + this.$t('pages.sysDisk') + ':\\ProgramData\\)'
        }
      ],
      // 排除的常用目录
      exCommonsDir: [
        {
          name: this.$t('pages.scanDesktop'),
          value: '#DESKTOP#',
          title: this.$t('pages.diskScan_Desktop')
        },
        {
          name: this.$t('pages.scanDocument'),
          value: '#MYDOCUMENTS#',
          title: this.$t('pages.diskScan_Documents')
        },
        {
          name: this.$t('pages.scanFavorites'),
          value: '#FAVORITES#',
          title: this.$t('pages.diskScan_Favorites')
        },
        {
          name: this.$t('pages.scanMusic'),
          value: '#MYMUSICS#',
          title: this.$t('pages.diskScan_Musics')
        },
        {
          name: this.$t('pages.scanPictures'),
          value: '#MYPICTURES#',
          title: this.$t('pages.diskScan_Pictures')
        },
        {
          name: this.$t('pages.scanVideo'),
          value: '#MYVIDEOS#',
          title: this.$t('pages.diskScan_Videos')
        },
        {
          name: this.$t('pages.scanAppData'),
          value: '#APPDATA#',
          title: this.$t('pages.diskScan_AppData') + '(' + this.$t('pages.sysDisk') + ':\\ProgramData\\)'
        },
        {
          name: this.$t('pages.recycleBin'),
          value: '\\$RECYCLE.BIN\\',
          title: this.$t('pages.backupRecycleBinTips')
        },
        {
          name: this.$t('pages.svnFile'),
          value: '\\.svn\\',
          title: this.$t('pages.svnFileTips')
        },
        {
          name: this.$t('pages.backupOfficeTempFile'),
          value: '#OFFICETMP#',
          title: this.$t('pages.backupOfficeTempFileTips')
        }
      ],
      scanCycleOpts: [],
      timeOptions: {
        start: '00:00',
        step: '00:01',
        end: '23:59'
      },
      scanModeOptions: [{ id: 0, label: this.$t('pages.userDefined') }, { id: 1, label: this.$t('pages.diskScan_Msg') }, { id: 2, label: this.$t('pages.diskScan_Msg1') }, { id: 3, label: this.$t('pages.diskScan_Msg2') }],
      diskTypeOptions: [
        { value: 1, label: this.$t('pages.localDisk') },
        { value: 4, label: this.$t('pages.removableDisk') }
        // { value: 8, label: this.$t('pages.networkDisk') },
        // { value: 16, label: this.$t('pages.cd') }
      ]
    }
  },
  created() {
    this.rowTemp = Object.assign({}, this.defaultTemp)
    this.scanCycleOpts = this.buildScanCycleOptions()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    resetTemp() {
      this.rowTemp = JSON.parse(JSON.stringify(this.defaultTemp))
      // this.rowTemp.backupStrg = []
    },
    handleCreate() {
      this.resetTemp();
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.$refs['stgDlg'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      console.log('row', row)
      if (row.backupStrg && row.backupStrg.length > 0) {
        const stg = row.backupStrg[0];
        row.includeFileList = stg.includeFileList;
        row.excludeFileList = stg.excludeFileList;
        row.excludeProc = stg.excludeProc ? stg.excludeProc.split('|') : [];
      } else {
        row.includeFileList = [{ includeDir: '', includeSuffix: '*.*' }]
        row.excludeFileList = [{ excludeDir: '', excludeSuffix: '' }]
        row.excludeProc = []
      }
      this.$refs['stgDlg'].show(row, this.formable)
    },
    handleShow(row, isGenerateStrategy) {
      //  将内容展示出来
      this.showAble = true
      //  将新增按钮隐藏
      this.addable = false
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    // 计算文件大小和对应单位，每1024（能被1024整除的）向上进一位（例：1024KB -> 1MB； 1025KB ->1025KB；）
    formatSizeAndUnit(size) {
      if (!size) {
        return {
          size: size,
          unit: 0
        }
      }
      // 转换成数值
      size = Number(size)
      let unitIndex = 0;
      while (size >= 1024) {
        if (size % 1024 === 0) {
          size /= 1024;
          unitIndex++;
        } else {
          break;
        }
      }
      if (size > 1024 && size % 1024 === 0) {
        size /= 1024;
        unitIndex++;
      }
      // 如果unitIndex>4，单位还是unitIndex =4，size为输入的值
      if (unitIndex > 4) {
        size = size * (unitIndex - 4) * 1024
        unitIndex = 4
      }
      return {
        size: Math.floor(size),
        unit: unitIndex
      };
    },
    formatRowData(row) {
      if (row.scanMode === 0 && typeof row.cpuMem === 'string' && row.cpuMem) {
        const array = row.cpuMem.split('|')
        row.cpuMem = { cpu: array[0], mem: array[1] }
      } else {
        row.cpuMem = { cpu: 50, mem: 50 }
      }
      if (typeof row.scanTime === 'string' && row.scanTime) {
        const array = row.scanTime.split('-')
        row.scanTime = [array[0], array[1]]
      } else {
        row.scanTime = []
      }
      if (typeof row.fileSize === 'string' && row.fileSize) {
        const array = row.fileSize.split('-')
        // 这里处理该文件大小对应的单位（可被1024整除的才换算，否则都算B）
        const min = this.formatSizeAndUnit(array[0]);
        const max = this.formatSizeAndUnit(array[1]);
        row.fileSize = {
          minSize: min.size,
          minSizeUnit: min.unit,
          maxSize: max.size,
          maxSizeUnit: max.unit
        }
      } else {
        row.fileSize = {}
      }
      if (typeof row.scanCycle === 'string' && row.scanCycle) {
        const array = row.scanCycle.split('|')
        if (array.length === 1) {
          row.scanCycle = [Number(array[0])]
        } else {
          row.scanCycle = [Number(array[0]), Number(array[1])]
        }
      } else {
        row.scanCycle = []
      }
      if (typeof row.diskType === 'number' && row.diskType) {
        // 十进制转成数组（根据diskTypeOptions来，1,4,8,16）
        row.diskType = this.diskTypeOptions
          .filter(option => (row.diskType & option.value) === option.value)
          .map(option => option.value);
      } else {
        row.diskType = []
      }
      row.backupStrg = row.backupStrg ? row.backupStrg.strgList : []
    },
    formatFormData(formData) {
      formData.cpuMem = formData.cpuMem.cpu + '|' + formData.cpuMem.mem
      formData.scanTime = formData.scanTime[0] + '-' + formData.scanTime[1]
      // 这里单位换算（根据配置的size以及对应的单位，转换成字节存库）
      const minSize = formData.fileSize.minSizeUnit === 0 ? formData.fileSize.minSize : formData.fileSize.minSize * Math.pow(1024, formData.fileSize.minSizeUnit)
      const maxSize = formData.fileSize.maxSizeUnit === 0 ? formData.fileSize.maxSize : formData.fileSize.maxSize * Math.pow(1024, formData.fileSize.maxSizeUnit)
      // formData.minSizeUnit、 formData.maxSizeUnit 仅用于记录管理员日志
      formData.minSizeUnit = formData.fileSize.minSizeUnit
      formData.maxSizeUnit = formData.fileSize.maxSizeUnit
      formData.fileSize = minSize + '-' + maxSize
      formData.scanCycle = formData.scanCycle ? formData.scanCycle.join('|') : ''
      formData.diskType = formData.diskType ? formData.diskType.reduce((acc, val) => acc | val, 0) : undefined
      formData.includeFileList = undefined
      formData.excludeFileList = undefined
      formData.suffix = undefined
      if (formData.excludeProc) {
        formData.backupStrg[0].excludeProc = formData.excludeProc ? formData.excludeProc.join('|') : undefined
      }
      formData.excludeProc = undefined
      formData.stgMessage = undefined
      if (formData.backupStrg) {
        formData.backupStrg = { strgList: formData.backupStrg }
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    slotChange(slotName, slotTemp) {
      this.slotName = slotName
      this.rowTemp = slotTemp || {}
      if (slotTemp.backupStrg && slotTemp.backupStrg.length > 0) {
        const stg = slotTemp.backupStrg[0];
        this.rowTemp.includeFileList = stg.includeFileList;
        this.rowTemp.excludeFileList = stg.excludeFileList;
        this.rowTemp.excludeProc = stg.excludeProc ? stg.excludeProc.split('|') : [];
      } else {
        this.rowTemp.backupStrg = [
          {
            includeFileList: this.rowTemp.includeFileList,
            excludeFileList: this.rowTemp.excludeFileList,
            excludeProc: this.rowTemp.excludeProc
          }
        ]
      }
    },
    selectionChangeEnd(selection) {
      this.selection = selection
    },
    sizeFormatter(row) {
      return row.minSize + ' ~ ' + row.maxSize + ' MB'
    },
    openConfigDir(type) {
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
      this.dirVisible = true
      this.dirType = type;
      this.rowTemp.includeDir = [];
      this.rowTemp.excludeDir = [];
      this.rowTemp.suffix = [];
      this.singleRowTemp = undefined
    },
    selectAll(type) {
      // type == 1 全选
      if (type === 1) {
        this.checkedCommonDir = []
        this.checkedCommonDir = (this.dirType === 1 ? this.commonsDir : this.exCommonsDir).map(item => item.name)
        this.checkedRealDirValue = []
        this.checkedRealDirValue = (this.dirType === 1 ? this.commonsDir : this.exCommonsDir).map(item => item.value)
      } else {
        this.checkedCommonDir = []
        this.checkedRealDirValue = []
      }
    },
    cancelConfigDir() {
      this.dirVisible = false
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
    },
    commonDirChange(event) {
      this.checkedRealDirValue = []
      if (this.checkedCommonDir.length > 0) {
        this.checkedCommonDir.forEach((res, pos) => {
          (this.dirType === 1 ? this.commonsDir : this.exCommonsDir).forEach((item, index) => {
            if (item.name == res) {
              this.checkedRealDirValue.push(item.value)
            }
          })
        })
      }
    },
    saveConfigDir(dirType) {
      if (dirType === 1 && this.checkedRealDirValue.length == 0 && this.specifyDir.length == 0 && this.rowTemp.suffix.length == 0) {
        this.$message({
          title: this.$t('text.fail'),
          message: this.$t('pages.backupFileAddPathTips'),
          type: 'error',
          duration: 3000
        })
        return
      }
      // 正则表达式，检测是否是带盘符的windows目录（目录和文件名都不能包含特殊字符/ : * ? < > " | \）
      const reg = /^([a-zA-Z]:\\(([^/:*?"<>|\\]+\\)*[^/:*?"<>|\\]*))$/;
      // 检测是否是$:\test风格的window相对目录（目录和文件名都不能包含特殊字符/ : * ? < > " | \）
      const specifyReg = /^([$]:\\(([^/:*?"<>|\\]+\\)*[^/:*?"<>|\\]*))$/;
      // 检测是否是\test风格的window相对目录（目录和文件名都不能包含特殊字符/ : * ? < > " | \）
      const relativeReg = /^(\\(([^/:*?"<>|\\]+\\)*[^/:*?"<>|\\]*))$/;
      console.log('this.specifyDir:', this.specifyDir)
      if (this.specifyDir.length > 0) {
        for (const item of this.specifyDir) {
          if (!reg.test(item)) {
            // 不符合带盘符的windows目录
            // 检测是否是$:\test风格的windos相对目录
            if (!specifyReg.test(item)) {
              // 检测是否是\test风格的windos相对目录
              if (!relativeReg.test(item)) {
                this.$message({
                  title: this.$t('text.fail'),
                  message: this.$t('pages.diskScan_Msg30', { scanDir: item }),
                  type: 'error',
                  duration: 3000
                })
                return
              }
            }
          }
        }
      }
      // 所有目录都通过了扫描
      const finalDir = []
      let num = 0
      if (this.checkedRealDirValue.length > 0) {
        this.checkedRealDirValue.forEach((item, index) => {
          finalDir[num] = item
          num++
        })
      }
      if (this.specifyDir.length > 0) {
        this.specifyDir.forEach((item, index) => {
          finalDir[num] = item
          num++
        })
      }
      // 判断是否是更新还是新增
      if (this.singleRowTemp) {
        const checkDir = []
        finalDir.forEach((item, index) => {
          if (!checkDir.includes(item)) {
            checkDir.push(item)
          }
        })
        const finalSuffix = this.rowTemp.suffix.map(file => `${file}`).join('|');
        const fileObj = {};
        if (dirType === 1) {
          fileObj.includeDir = checkDir.join('|');
          fileObj.includeSuffix = finalSuffix;
          this.rowTemp.includeFileList[this.singleRowTemp.index] = fileObj;
          this.rowTemp.backupStrg[0].includeFileList = this.rowTemp.includeFileList
        } else {
          fileObj.excludeDir = checkDir.join('|');
          fileObj.excludeSuffix = finalSuffix;
          this.rowTemp.excludeFileList[this.singleRowTemp.index] = fileObj;
          this.rowTemp.backupStrg[0].excludeFileList = this.rowTemp.excludeFileList
        }
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.dirVisible = false
        this.checkedCommonDir = []
        this.checkedRealDirValue = []
        this.specifyDir = []
        return;
      }
      // 将格式化好的目录赋值给外层弹窗的扫描目录
      this.rowTemp.includeDir = []
      this.rowTemp.excludeDir = []
      finalDir.forEach((item, index) => {
        if (dirType === 1) {
          if (!this.rowTemp.includeDir.includes(item)) {
            this.rowTemp.includeDir.push(item)
          }
        } else {
          if (!this.rowTemp.excludeDir.includes(item)) {
            this.rowTemp.excludeDir.push(item)
          }
        }
      })
      const finalSuffix = this.rowTemp.suffix.map(file => `${file}`).join('|');
      const fileObj = {};
      if (dirType === 1) {
        fileObj.includeDir = this.rowTemp.includeDir.join('|');
        fileObj.includeSuffix = finalSuffix;
        if (this.rowTemp.includeFileList.length === 1 &&
          this.rowTemp.includeFileList[0].includeDir === '' && this.rowTemp.includeFileList[0].includeSuffix === '*.*'
        ) {
          this.rowTemp.includeFileList = [fileObj];
        } else {
          this.rowTemp.includeFileList.push(fileObj);
        }
        this.rowTemp.backupStrg[0].includeFileList = this.rowTemp.includeFileList
      } else {
        fileObj.excludeDir = this.rowTemp.excludeDir.join('|');
        fileObj.excludeSuffix = finalSuffix;
        if (this.rowTemp.excludeFileList.length === 1 &&
          this.rowTemp.excludeFileList[0].excludeDir === '' && this.rowTemp.excludeFileList[0].excludeSuffix === ''
        ) {
          this.rowTemp.excludeFileList = [fileObj];
        } else {
          if (!this.rowTemp.excludeFileList.some(item =>
            item.excludeDir === fileObj.excludeDir && item.excludeSuffix === fileObj.excludeSuffix
          )) {
            this.rowTemp.excludeFileList.push(fileObj);
          }
        }
        this.rowTemp.backupStrg[0].excludeFileList = this.rowTemp.excludeFileList
      }
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.createSuccess'),
        type: 'success',
        duration: 2000
      })
      this.dirVisible = false
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      let union_suffix = [...new Set(this.rowTemp.suffix.concat(new_suffix))]
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength)
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.fileSuffixOutnumberErrorMsg2', { number: this.suffixMaxLength }),
          type: 'warning',
          duration: 2000
        })
      }
      this.rowTemp.suffix = union_suffix
    },
    handleClear() {
      this.rowTemp.suffix.splice(0)
    },
    handleScanCycleChange(value) {

    },
    buildScanCycleOptions() {
      const opts = []
      opts.push({ value: 1, label: this.$t('pages.everyDay') })
      const weekDays = [
        { value: 1, label: this.$t('pages.monday1') },
        { value: 2, label: this.$t('pages.tuesday1') },
        { value: 3, label: this.$t('pages.wednesday1') },
        { value: 4, label: this.$t('pages.Thursday1') },
        { value: 5, label: this.$t('pages.friday1') },
        { value: 6, label: this.$t('pages.saturday1') },
        { value: 0, label: this.$t('pages.sunday1') }
      ]
      opts.push({ value: 7, label: this.$t('pages.weekly'), children: weekDays })
      const monthDays = []
      for (let i = 1; i <= 31; i++) {
        monthDays.push({ value: i, label: this.$t('pages.dateNum', { date: i }) })
      }
      opts.push({ value: 31, label: this.$t('pages.monthly'), children: monthDays })
      return opts
    },
    regularBackupAbleChange(value) {
      if (!value) {
        this.$nextTick(() => {
          this.$refs['stgDlg'] && this.$refs['stgDlg'].clearValidate();
        })
      }
    },
    uptFileItemView(index, type) {
      const row = type === 1 ? this.rowTemp.includeFileList[index] : this.rowTemp.excludeFileList[index]
      // 打开录入页面
      this.openConfigDir(type);
      this.singleRowTemp = Object.assign({}, row)
      this.singleRowTemp.index = index
      // 这里面有两种情况（常用目录 or 指定目录， 常用目录固定## 其他归为指定目录）
      let rowDir = [];
      let rowSuffix = [];
      if (type === 1) {
        rowDir = row.includeDir
        rowSuffix = row.includeSuffix;
      } else {
        rowDir = row.excludeDir
        rowSuffix = row.excludeSuffix;
      }
      const dirArr = rowDir.split('|');
      if (rowDir && rowDir.length !== 0) {
        // 常用目录
        this.checkedCommonDir = (this.dirType === 1 ? this.commonsDir : this.exCommonsDir)
          .filter(item => dirArr.includes(item.value))
          .map(item => item.name);
        this.checkedRealDirValue = (this.dirType === 1 ? this.commonsDir : this.exCommonsDir)
          .filter(item => this.checkedCommonDir.includes(item.name))
          .map(item => item.value);
        // 指定目录
        this.specifyDir = dirArr.filter((item) => {
          return !this.checkedRealDirValue.includes(item)
        })
      }
      if (rowSuffix) {
        this.rowTemp.suffix = rowSuffix.split('|');
      }
    },
    addFileItemView(type) {
      // 打开录入页面
      this.openConfigDir(type);
    },
    deleteFileItem(index) {
      this.rowTemp.includeFileList.splice(index, 1)
      if (this.rowTemp.includeFileList.length === 0) {
        this.rowTemp.includeFileList.splice(index, 0, { includeDir: '', includeSuffix: '*.*' })
      }
      this.rowTemp.backupStrg[0].includeFileList = this.rowTemp.includeFileList
    },
    deleteExcludeFileItem(index) {
      this.rowTemp.excludeFileList.splice(index, 1)
      if (this.rowTemp.excludeFileList.length === 0) {
        this.rowTemp.excludeFileList.splice(index, 0, { excludeDir: '', excludeSuffix: '' })
      } else if (this.rowTemp.excludeFileList.length === 1 && this.rowTemp.excludeFileList[0] === { excludeDir: '', excludeSuffix: '' }) {
        this.rowTemp.excludeFileList[0] = { excludeDir: '', excludeSuffix: '' };
      }
      this.rowTemp.backupStrg[0].excludeFileList = this.rowTemp.excludeFileList
    },
    closed() {

    },
    diskTypeValid(rule, value, callback) {
      if (!this.rowTemp.diskType || this.rowTemp.diskType.length === 0) {
        callback(new Error(this.$t('pages.pleaseSelectContent', { content: this.$t('pages.driverType') })))
      } else {
        callback()
      }
    },
    scanModeValid(rule, value, callback) {
      if (this.rowTemp.scanMode === 0 && !this.rowTemp.cpuMem.cpu) {
        callback(new Error(this.$t('text.cantNullInfo', { info: this.$t('pages.diskScan_Msg20') })))
      } else if (this.rowTemp.scanMode === 0 && !this.rowTemp.cpuMem.mem) {
        callback(new Error(this.$t('text.cantNullInfo', { info: this.$t('pages.diskScan_Msg21') })))
      } else {
        callback()
      }
    },
    scanTimeValid(rule, value, callback) {
      if (!this.rowTemp.scanTime[0]) {
        this.rowTemp.scanTime[0] = this.timeOptions.start;
      }
      if (!this.rowTemp.scanTime[1]) {
        this.rowTemp.scanTime[1] = this.timeOptions.end;
      }
      callback()
    },
    cleanAbleChange(value) {
      if (!value) {
        this.$nextTick(() => {
          this.$refs['stgDlg'] && this.$refs['stgDlg'].clearValidate();
        })
      }
    },
    suffixChange(list) {
      const newMap = new Map()
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
      })
      this.rowTemp.suffix = Object.keys(newMap) || [];
    },
    showAppImport() {
      if (this.rowTemp.excludeProc && this.rowTemp.excludeProc.length >= this.procMaxLength) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.procOutnumberErrorMsg1', { number: this.procMaxLength }),
          type: 'warning',
          duration: 2000
        })
        return
      }
      this.$refs['appSelectDlg'].show()
    },
    handleAppImport(selection) {
      if (selection && selection.length) {
        if (selection.length >= this.procMaxLength) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.procOutnumberErrorMsg2', { number: this.procMaxLength }),
            type: 'warning',
            duration: 2000
          })
        }
        selection.forEach(item => {
          if (this.rowTemp.excludeProc.length < this.procMaxLength && this.rowTemp.excludeProc.indexOf(item.processName) < 0) {
            this.rowTemp.excludeProc.push(item.processName)
          }
        })
      }
    },
    handleAppClear() {
      this.rowTemp.excludeProc.splice(0)
    }
  }
}
</script>

<style lang="scss" scoped>
.scan-cycle {
  width: 180px;
  line-height: 30px;
  >>>.el-input__inner:read-only {
    color: #666;
    background-color: #f5f5f5;
  }
}
>>>.el-tab-pane {
  max-height: calc(100% - 40px) !important;
}
</style>
