<!-- 服务器存储配置 -->
<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('route.serverBackupConfig')"
      :stg-code="272"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <Form ref="form" :model="rowTempP" label-width="105px" label-position="right">
          <FormItem :label="$t('pages.includeFile')">
            <el-row v-for="(value, index) in rowTempP.includeFileList" :key="index" style="padding-bottom: 1px">
              <el-col :span="22" :title="`${value.includeDir}${value.includeDir && value.includeSuffix ? ';': ''}${value.includeSuffix}`" style="white-space: nowrap;overflow:hidden; text-overflow: ellipsis;display: inline-block;background-color: #f5f5f5;border: 1px solid #aaa;padding: 0 15px;border-radius: 4px;">
                {{ `${value.includeDir}${value.includeDir && value.includeSuffix ? ';': ''}${value.includeSuffix}` }}
              </el-col>
              <el-col :span="2" style="float:right;width: 50px;cursor: pointer;color: #68a8d0;">
                <i v-show="rowTempP.includeFileList.length < 50" class="el-icon-circle-plus-outline" @click="addFileItemView(1)"></i>
                <i class="el-icon-edit-outline " @click="uptFileItemView(index, 1)"></i>
                <i class="el-icon-remove-outline" @click="deleteFileItem(index)"></i>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem :label="$t('pages.excludeFile')">
            <el-row v-for="(value, index) in rowTempP.excludeFileList" :key="index" style="padding-bottom: 1px">
              <el-col :span="22" :title="`${value.excludeDir}${value.excludeDir && value.excludeSuffix ? ';': ''}${value.excludeSuffix}`" style="white-space: nowrap;overflow:hidden; text-overflow: ellipsis;display: inline-block;background-color: #f5f5f5;border: 1px solid #aaa;padding: 0 15px;border-radius: 4px;">
                <template v-if="!value.excludeDir && !value.excludeSuffix">&nbsp;</template>
                <template v-else>{{ `${value.excludeDir}${value.excludeDir && value.excludeSuffix ? ';': ''}${value.excludeSuffix}` }}</template>
              </el-col>
              <el-col :span="2" style="float:right;width: 50px;cursor: pointer;color: #68a8d0;">
                <i class="el-icon-circle-plus-outline" @click="addFileItemView(2)"></i>
                <i class="el-icon-edit-outline" @click="uptFileItemView(index, 2)"></i>
                <i class="el-icon-remove-outline" @click="deleteExcludeFileItem(index)"></i>
              </el-col>
            </el-row>
          </FormItem>
          <FormItem :label="$t('pages.keepVerType')">
            <el-radio v-model="rowTempP.keepVerType" :disabled="!formable" :label="1">
              <i18n path="pages.serverBackupKeepVerType1">
                <el-input-number slot="day" v-model="rowTempP.keepDays" v-trim style="width: 80px" :controls="false" :min="1" :max="999999" :step="1" step-strictly :disabled="!formable" />
                <el-input-number slot="num" v-model="rowTempP.dayVerNum" v-trim style="width: 80px" :controls="false" :min="1" :max="999999" :step="1" step-strictly :disabled="!formable" />
              </i18n>
            </el-radio>
            <br/>
            <el-radio v-model="rowTempP.keepVerType" :disabled="!formable" :label="2">
              <i18n path="pages.serverBackupKeepVerType2">
                <el-input-number slot="num" v-model="rowTempP.maxVerNum" v-trim style="width: 80px" :controls="false" :min="1" :max="999999" :step="1" step-strictly :disabled="!formable" />
              </i18n>
            </el-radio>
            <br/>
            <el-radio v-model="rowTempP.keepVerType" :disabled="!formable" :label="3">{{ `不保留（全部清理）` }}</el-radio>
          </FormItem>
        </Form>
      </template>
      <div v-if="formable" slot="button">
        <link-button btn-type="primary" :title="$t('pages.backupServerStoreConfigTips')" btn-style="float: left" :menu-code="'E85'" :link-url="'/dataEncryption/smartBackup/highConfig'" :btn-text="$t('pages.backupServerStoreCycle')" :before-click="beforeClick"/>
      </div>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
    <el-dialog
      ref="configDir"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.configScanDir')"
      :visible.sync="dirVisible"
      width="650px"
    >
      <el-card class="box-card" style="margin: 5px;">
        <div slot="header" class="clearFix">
          <span>指定文件/路径</span>
        </div>
        <div hidden style="line-height: 15px;margin-top: -10px">
          {{ $t('pages.commonDir') }}：
          <el-button type="text" @click="selectAll(1)">{{ $t('button.selectAll') }}</el-button>
          <el-button type="text" @click="selectAll(2)">{{ $t('button.cancelSelectAll') }}</el-button>
          <el-checkbox-group v-model="checkedCommonDir" style="margin-top: 6px;">
            <el-row>
              <el-col v-for="(item, index) in commonsDir" :key="index" :span="8">
                <el-checkbox :label="item.name" @change="commonDirChange($event)">
                  <span :title="item.title" class="ellipsis label-text">{{ item.name }}</span>
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </div>
        <div style="display: flex;align-items: center; line-height: 22px;">
          <span style="width: 75px;">
            文件/路径
          </span>
          <el-tooltip class="item" effect="dark" placement="bottom-start" style="margin-left: -9px;">
            <div slot="content">
              可针对文件/路径（支持通配符）<br/>
              &nbsp;&nbsp;全盘路径：如“C:\test\record\”、“C:\test\record\*”(包含子目录)<br/>
              &nbsp;&nbsp;盘符路径：如“$:\test\record\”<br/>
              &nbsp;&nbsp;&nbsp;&nbsp;$代表通配盘符，当策略下发时可以转换为C、D等磁盘，如$:\test\可以转换为C:\test\、D:\test\等<br/>
              &nbsp;&nbsp;相对路径：如“*\test\*”<br/>
              &nbsp;&nbsp;针对文件：如“C:\test\record”<br/>
              &nbsp;&nbsp;模糊匹配：如“C:\*\test\*” -- C盘下任意路径下的test目录（包含子目录）<br/>
              {{ $t('pages.backupScanDirTips') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag :list="specifyDir" trim-able border class="input-with-button" input-length="256" :limit-size="50" style="min-height: 30px;background-color:#f5f5f5;margin-left: 5px;" @tagChange="dirChange" />
        </div>
        <span style="font-size: 14px;color: #0c60a5">
          示例：<br/>
          &nbsp;&nbsp;C盘下的test文件： C:\test     （指定后缀为空时，匹配C盘下的test无后缀文件）<br/>
          &nbsp;&nbsp;C盘下的test文件（所有文件后缀）：C:\test.*<br/>
          &nbsp;&nbsp;C盘下的text开头的任意文件： C:\test*<br/>
          &nbsp;&nbsp;C盘的test目录（不包含子目录）： C:\test\<br/>
          &nbsp;&nbsp;C盘下test目录（包含子目录）： C:\test\*<br/>
          &nbsp;&nbsp;相对路径： *\test\*<br/>
          &nbsp;&nbsp;C盘下模糊匹配文件： C:\*test* 、C:\*t*e*s*t<br/>
          &nbsp;&nbsp;C盘下模糊匹配目录： C:\*test*\ 、C:\*t*e*s*t\<br/>
          &nbsp;&nbsp;C盘任意路径下的test目录（不包含子目录）： C:\*\test\<br/>
          &nbsp;&nbsp;C盘任意路径下的test目录（包含子目录）： C:\*\test\*
        </span>
      </el-card>
      <el-card class="box-card" style="margin: 5px;">
        <div slot="header" class="clearFix">
          <span>{{ $t('pages.backupSpecifySuffix') }}&nbsp;</span>
        </div>
        <div style="display: flex;align-items: center; line-height: 22px;">
          <span style="width: 75px;">
            {{ $t('pages.suffixes') }}
          </span>
          <el-tooltip class="item" effect="dark" placement="bottom-start" style="margin-left: -9px;">
            <div slot="content">
              {{ $t('pages.backupSuffixTips') }}<br/>
              {{ $t('pages.inputMaxLength', { num: 256 }) }}<br/>
              “文件/路径”配置 以*或\结尾时，“文件后缀”为空，表示匹配任意后缀文件；其他配置时，“文件后缀”为空，表示匹配无后缀文件
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag :list="suffix" trim-able border class="input-with-button" input-length="256" :valid-rule="suffixValidRule" :limit-size="suffixMaxLength" style="width: calc(100% - 115px); background-color:#f5f5f5; min-height: 30px;" @tagChange="suffixChange"/>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" :disabled="!formable" size="mini" @click="handleFileSuffixImport()">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
          <el-button v-if="formable" :disabled="!formable" size="small" class="clear-btn" style="height: 30px;margin-top: 2px" @click="handleClear">
            {{ $t('button.clear') }}
          </el-button>
        </div>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveConfigDir(dirType)">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancelConfigDir ()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import { createStrategy, updateStrategy, getStrategyByName } from '@/api/dataEncryption/smartBackup/serverBackupConfig'

export default {
  name: 'ServerBackupConfigDlg',
  components: { FileSuffixLibImport },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      rowTemp: {},
      rowTempP: {},
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: undefined,
        entityId: undefined,
        backupStrg: []
      },
      defaultTempP: {
        keepVerType: 1,
        keepDays: 7,
        dayVerNum: 10,
        maxVerNum: 10,
        includeFileList: [
          {
            includeDir: '',
            includeSuffix: '*.*'
          }
        ],
        excludeFileList: [
          {
            excludeDir: '',
            excludeSuffix: ''
          }
        ]
      },
      rules: {

      },
      showAble: false,
      addable: true,
      slotName: undefined,
      colModel: [
        { label: this.$t('pages.targetFile'), width: '80', formatter: this.targetFileFormatter },
        { prop: 'keepVerType', label: this.$t('pages.keepVerType'), width: '80', formatter: this.retainFormatter }
      ],
      suffix: [],
      selection: [],
      suffixMaxLength: 50,
      checkedCommonDir: [],
      checkedRealDirValue: [],
      specifyDir: [],
      dirVisible: false,
      dirType: 1,
      singleRowTemp: {},
      commonsDir: [
        {
          name: this.$t('pages.scanDesktop'),
          value: '#DESKTOP#',
          title: this.$t('pages.diskScan_Desktop')
        },
        {
          name: this.$t('pages.scanDocument'),
          value: '#MYDOCUMENTS#',
          title: this.$t('pages.diskScan_Documents')
        },
        {
          name: this.$t('pages.scanFavorites'),
          value: '#FAVORITES#',
          title: this.$t('pages.diskScan_Favorites')
        },
        {
          name: this.$t('pages.scanMusic'),
          value: '#MYMUSICS#',
          title: this.$t('pages.diskScan_Musics')
        },
        {
          name: this.$t('pages.scanPictures'),
          value: '#MYPICTURES#',
          title: this.$t('pages.diskScan_Pictures')
        },
        {
          name: this.$t('pages.scanVideo'),
          value: '#MYVIDEOS#',
          title: this.$t('pages.diskScan_Videos')
        },
        {
          name: this.$t('pages.scanAppData'),
          value: '#APPDATA#',
          title: this.$t('pages.diskScan_AppData') + '(' + this.$t('pages.sysDisk') + ':\\ProgramData\\)'
        }
      ],
      suffixValidRule: [
        { validator: this.suffixValidator, trigger: 'blur' }
      ]
    }
  },
  created() {
    this.rowTemp = Object.assign({}, this.defaultTemp)
    this.rowTemp.backupStrg[0] = Object.assign({}, this.defaultTempP);
    this.rowTempP = Object.assign({}, this.defaultTempP);
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.$refs['stgDlg'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.$refs['stgDlg'] && this.$refs['stgDlg'].clearValidate()
      })
    },
    handleShow(row, isGenerateStrategy) {
      //  将内容展示出来
      this.showAble = true
      //  将新增按钮隐藏
      this.addable = false
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
      this.$nextTick(() => {
        this.$refs['stgDlg'] && this.$refs['stgDlg'].clearValidate()
      })
    },
    formatRowData(row) {
      if (row.backupStrg && row.backupStrg.length > 0) {
        row.backupStrg.forEach((item, index) => {
          item.id = index
        })
        this.rowTempP = Object.assign({}, this.defaultTempP, row.backupStrg[0]);
      }
    },
    formatFormData(formData) {
      formData.backupStrg[0] = Object.assign({}, this.defaultTempP, this.rowTempP);
      formData.stgMessage = undefined
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    slotChange(slotName, slotTemp) {
      this.slotName = slotName
      this.rowTemp = slotTemp || {}
    },
    selectionChangeEnd(selection) {
      this.selection = selection
    },
    retainFormatter(row) {
      if (row.keepVerType === 1) {
        return this.$t('pages.serverBackupKeepVerType1', { day: row.keepDays, num: row.dayVerNum })
      }
      return this.$t('pages.serverBackupKeepVerType2', { num: row.maxVerNum })
    },
    selectAll(type) {
      // type == 1 全选
      if (type === 1) {
        this.checkedCommonDir = []
        this.checkedCommonDir = this.commonsDir.map(item => item.name)
        this.checkedRealDirValue = []
        this.checkedRealDirValue = this.commonsDir.map(item => item.value)
      } else {
        this.checkedCommonDir = []
        this.checkedRealDirValue = []
      }
    },
    cancelConfigDir() {
      this.dirVisible = false
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
    },
    commonDirChange(event) {
      this.checkedRealDirValue = []
      if (this.checkedCommonDir.length > 0) {
        this.checkedCommonDir.forEach((res, pos) => {
          this.commonsDir.forEach((item, index) => {
            if (item.name == res) {
              this.checkedRealDirValue.push(item.value)
            }
          })
        })
      }
    },
    saveConfigDir(dirType) {
      if (dirType === 1 && this.checkedRealDirValue.length == 0 && this.specifyDir.length == 0 && this.suffix.length == 0) {
        this.$message({
          title: this.$t('text.fail'),
          message: this.$t('pages.backupFileAddPathTips'),
          type: 'error',
          duration: 3000
        })
        return
      }
      // 正则表达式，仅判断不能包含特殊字符 ? < > " |
      const reg = /^[^?"<>|]+$/;
      if (this.specifyDir.length > 0) {
        for (const item of this.specifyDir) {
          if (!reg.test(item)) {
            this.$message({
              title: this.$t('text.fail'),
              message: this.$t('pages.diskScan_Msg30', { scanDir: item }),
              type: 'error',
              duration: 3000
            })
            return
          }
        }
      }
      // 所有目录都通过了扫描
      const finalDir = []
      let num = 0
      if (this.checkedRealDirValue.length > 0) {
        this.checkedRealDirValue.forEach((item, index) => {
          finalDir[num] = item
          num++
        })
      }
      if (this.specifyDir.length > 0) {
        this.specifyDir.forEach((item, index) => {
          finalDir[num] = item
          num++
        })
      }
      // 判断是否是更新还是新增
      if (this.singleRowTemp) {
        const checkDir = []
        finalDir.forEach((item, index) => {
          if (!checkDir.includes(item)) {
            checkDir.push(item)
          }
        })
        const finalSuffix = this.suffix.map(file => `${file}`).join('|');
        const fileObj = {};
        if (dirType === 1) {
          fileObj.includeDir = checkDir.join('|');
          fileObj.includeSuffix = finalSuffix;
          this.rowTempP.includeFileList[this.singleRowTemp.index] = fileObj;
        } else {
          fileObj.excludeDir = checkDir.join('|');
          fileObj.excludeSuffix = finalSuffix;
          this.rowTempP.excludeFileList[this.singleRowTemp.index] = fileObj;
        }
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        this.dirVisible = false
        this.checkedCommonDir = []
        this.checkedRealDirValue = []
        this.specifyDir = []
        return;
      }
      // 将格式化好的目录赋值给外层弹窗的扫描目录
      this.rowTempP.includeDir = []
      this.rowTempP.excludeDir = []
      finalDir.forEach((item, index) => {
        if (dirType === 1) {
          if (!this.rowTempP.includeDir.includes(item)) {
            this.rowTempP.includeDir.push(item)
          }
        } else {
          if (!this.rowTempP.excludeDir.includes(item)) {
            this.rowTempP.excludeDir.push(item)
          }
        }
      })
      const finalSuffix = this.suffix.map(file => `.${file}`).join('|');
      const fileObj = {};
      if (dirType === 1) {
        fileObj.includeDir = this.rowTempP.includeDir.join('|');
        fileObj.includeSuffix = finalSuffix;
        if (this.rowTempP.includeFileList.length === 1 &&
          this.rowTempP.includeFileList[0].includeDir === '' && this.rowTempP.includeFileList[0].includeSuffix === '*.*'
        ) {
          this.rowTempP.includeFileList = [fileObj];
        } else {
          this.rowTempP.includeFileList.push(fileObj);
        }
      } else {
        fileObj.excludeDir = this.rowTempP.excludeDir.join('|');
        fileObj.excludeSuffix = finalSuffix;
        if (this.rowTempP.excludeFileList.length === 1 &&
          this.rowTempP.excludeFileList[0].excludeDir === '' && this.rowTempP.excludeFileList[0].excludeSuffix === ''
        ) {
          this.rowTempP.excludeFileList = [fileObj];
        } else {
          if (!this.rowTempP.excludeFileList.some(item =>
            item.excludeDir === fileObj.excludeDir && item.excludeSuffix === fileObj.excludeSuffix
          )) {
            this.rowTempP.excludeFileList.push(fileObj);
          }
        }
      }
      this.dirVisible = false
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.createSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      let union_suffix = [...new Set(this.suffix.concat(new_suffix))]
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength)
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.fileSuffixOutnumberErrorMsg2', { number: this.suffixMaxLength }),
          type: 'warning',
          duration: 2000
        })
      }
      // 删除*.*
      if (union_suffix && union_suffix.length > 0 && union_suffix[0] === '*.*') {
        union_suffix = union_suffix.filter(item => item !== '*.*')
      }
      this.suffix = union_suffix
    },
    handleClear() {
      this.suffix.splice(0)
    },
    openConfigDir(type) {
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
      this.dirVisible = true
      this.dirType = type;
      this.rowTempP.includeDir = [];
      this.rowTempP.excludeDir = [];
      this.suffix = [];
      this.singleRowTemp = undefined
    },
    uptFileItemView(index, type) {
      const row = type === 1 ? this.rowTempP.includeFileList[index] : this.rowTempP.excludeFileList[index]
      // 打开录入页面
      this.openConfigDir(type);
      this.singleRowTemp = Object.assign({}, row)
      this.singleRowTemp.index = index
      // 这里面有两种情况（常用目录 or 指定目录， 常用目录固定## 其他归为指定目录）
      let rowDir = [];
      let rowSuffix = [];
      if (type === 1) {
        rowDir = row.includeDir
        rowSuffix = row.includeSuffix;
      } else {
        rowDir = row.excludeDir
        rowSuffix = row.excludeSuffix;
      }
      const dirArr = rowDir.split('|');
      if (rowDir && rowDir.length !== 0) {
        // 常用目录
        this.checkedCommonDir = this.commonsDir
          .filter(item => dirArr.includes(item.value))
          .map(item => item.name);
        this.checkedRealDirValue = this.commonsDir
          .filter(item => this.checkedCommonDir.includes(item.name))
          .map(item => item.value);
        // 指定目录
        this.specifyDir = dirArr.filter((item) => {
          return !this.checkedRealDirValue.includes(item)
        })
      }
      if (rowSuffix) {
        this.suffix = rowSuffix.split('|');
      }
    },
    addFileItemView(type) {
      // 打开录入页面
      this.openConfigDir(type);
    },
    deleteFileItem(index) {
      this.rowTempP.includeFileList.splice(index, 1)
      if (this.rowTempP.includeFileList.length === 0) {
        this.rowTempP.includeFileList.splice(index, 0, { includeDir: '', includeSuffix: '*.*' })
      }
    },
    deleteExcludeFileItem(index) {
      this.rowTempP.excludeFileList.splice(index, 1)
      if (this.rowTempP.excludeFileList.length === 0) {
        this.rowTempP.excludeFileList.splice(index, 0, { excludeDir: '', excludeSuffix: '' })
      }
    },
    closed() {
      this.resetTempP();
    },
    resetTempP() {
      this.rowTempP = Object.assign({}, this.defaultTempP)
    },
    portTable() {
      return this.$refs['ruleTable']
    },
    cancelBackupConfig() {
      this.portTable() && this.portTable().setCurrentRow()
      this.$refs['form'] && this.$refs['form'].clearValidate()
      this.resetTempP()
    },
    addBackupConfig(rowData) {
      let isAdd = true
      this.rowTemp.backupStrg.forEach(item => {
        const data = JSON.parse(JSON.stringify(item))
        const data1 = JSON.parse(JSON.stringify(rowData))
        data.id = undefined
        data1.id = undefined
        if (JSON.stringify(data) === JSON.stringify(data1)) {
          isAdd = false
        }
      })
      //  存在时，将不添加
      if (isAdd) {
        rowData.id = new Date().getTime()
        this.rowTemp.backupStrg.unshift(JSON.parse(JSON.stringify(rowData)));
      }
    },
    createBackupConfig() {
      let validate
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.rowTempP)
          this.addBackupConfig(rowData)
          this.resetTempP()
          this.cancelBackupConfig()
          validate = valid
        }
      })
      return validate
    },
    //  校验单条数据是否已存在
    validUpdateDataExist(rowData) {
      let isAdd = false
      for (let i = 0, len = this.rowTemp.backupStrg.length; i < len; i++) {
        if (this.validDataEquals(this.rowTemp.backupStrg[i], rowData)) {
          isAdd = true
          break;
        }
      }
      return isAdd;
    },
    //  校验两个数据是否相同
    validDataEquals(data, data1) {
      if (data.id === data1.id) {
        return false
      }
      data = JSON.parse(JSON.stringify(data))
      data1 = JSON.parse(JSON.stringify(data1))
      data.id = undefined
      data1.id = undefined
      return JSON.stringify(data) === JSON.stringify(data1);
    },
    updateBackupConfig() {
      let validate
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.rowTempP)
          //  校验是否存在相同数据
          if (!this.validUpdateDataExist(rowData)) {
            this.updateOneData(rowData, false)
          } else {
            this.$message({
              message: this.$t('pages.dataDuplication'),
              type: 'warning',
              duration: 3000
            })
            return;
          }
          this.cancelBackupConfig()
          validate = valid
        }
      })
      return validate
    },
    //  更新数据
    updateOneData(rowData) {
      for (let i = 0, size = this.rowTemp.backupStrg.length; i < size; i++) {
        const data = this.rowTemp.backupStrg[i];
        if (data.id === rowData.id) {
          this.rowTemp.backupStrg.splice(i, 1, rowData)
          break
        }
      }
    },
    targetFileFormatter(row) {
      // 包含文件:(1)D:\\a|D:\\b;.rar|.zip|.mkv (2)#DESKTOP#|#MYDOCUMENTS#;.mp4|.doc|.xls (3).cab|.dmg|.exe|.win|.iso|.7z|.tar|.jar|.zip|.rar
      // 排除文件:(1)$:\\Windows|$:\\User|$:\\AppData|$:\\ProgramData|C:\\Program Files; (2).jar|.jpg|.png (3)D:\\a\\b;.rar
      // 备份文件大小:0-100 扫描方式:自定义 结束日期:2024-10-31
      let result;
      if (row.includeFileList) {
        result = '包含文件：';
        row.includeFileList.forEach((item, index) => {
          result += `(${index + 1})${item.includeDir ? item.includeDir + '|' : ''}${item.includeSuffix} `
        })
      }
      if (row.excludeFileList && row.excludeFileList.length > 0 && (row.excludeFileList[0].excludeDir || row.excludeFileList[0].excludeSuffix)) {
        result += '排除文件：';
        row.excludeFileList.forEach((item, index) => {
          result += `(${index + 1})${item.excludeDir ? item.excludeDir + '|' : ''}|${item.excludeSuffix} `
        })
      }
      return result
    },
    deleteBackupConfig() {
      const toDeleteIds = this.portTable().getSelectedIds()
      this.rowTemp.backupStrg.splice(0, this.rowTemp.backupStrg.length, ...this.portTable().deleteRowData(toDeleteIds))
      this.cancelBackupConfig()
    },
    beforeUpdateBackupConfig() {
      this.resetTempP()
      const selected = this.portTable().getSelectedDatas()
      if (selected.length === 1) {
        this.rowTempP = Object.assign(this.rowTempP, selected[0])
      }
    },
    beforeClick() {
      this.dlgVisible = false
    },
    suffixChange(list) {
      const newMap = new Map()
      // 删除*.*
      if (list && list.length > 0 && list[0] === '*.*') {
        list = list.filter(item => item !== '*.*')
      }
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
      })
      this.suffix = Object.keys(newMap) || [];
    },
    dirChange(list) {
      if (list && list.length > 0 && this.suffix && this.suffix[0] === '*.*') {
        this.suffix = this.suffix.filter(item => item !== '*.*')
      }
    },
    validateForm() {
      if (this.rowTempP.keepVerType === 1) {
        if (!this.rowTempP.keepDays && this.rowTempP.keepDays !== 0 || !this.rowTempP.dayVerNum && this.rowTempP.dayVerNum !== 0) {
          this.$message({
            message: this.$t('text.cantNullInfo', { info: this.$t('pages.serverBackupKeepVerType') }),
            type: 'warning',
            duration: 2000
          })
          return false
        }
      } else if (this.rowTempP.keepVerType === 2) {
        if (!this.rowTempP.maxVerNum && this.rowTempP.maxVerNum !== 0) {
          this.$message({
            message: this.$t('text.cantNullInfo', { info: this.$t('pages.serverBackupKeepVerType') }),
            type: 'warning',
            duration: 2000
          })
          return false
        }
      }
      return true
    },
    suffixValidator(rule, value, callback) {
      // / : * ? < > " | \
      if (value.includes('/') ||
        value.includes(':') ||
        value.includes('*') ||
        value.includes('?') ||
        value.includes('<') ||
        value.includes('>') ||
        value.includes('"') ||
        value.includes('|') ||
        value.includes('\\')) {
        callback(new Error('不允许输入特殊字符/ : * ? < > " | \\'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-tab-pane {
  max-height: calc(100% - 40px) !important;
}
>>>.itemDisabled {
  opacity: 0.5;
  color: #606266;
  cursor: not-allowed;
}
</style>
