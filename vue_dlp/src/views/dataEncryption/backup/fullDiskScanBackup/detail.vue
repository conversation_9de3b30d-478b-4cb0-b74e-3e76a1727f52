<!-- 全盘扫描备份详情 -->
<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('text.details')"
    :modal="false"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="940px"
  >
    <div class="show-detail-panel">
      <el-divider content-position="left">{{ $t('pages.taskBaseInfo') }}</el-divider>
      <el-descriptions class="margin-top" :column="4" size="small" border>
        <el-descriptions-item :label="$t('pages.taskNum')">
          {{ taskId }}
        </el-descriptions-item>
        <el-descriptions-item :span="3" :label="$t('pages.taskName')">
          {{ taskName }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.taskStatus')">
          {{ taskStatusFormatter(taskStatus) }}
        </el-descriptions-item>
        <el-descriptions-item>
          {{ rowDetail.backingUpTermNum }}
          <template slot="label">
            {{ $t('pages.backupStatusOverview') }}
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                {{ $t('pages.backupStatusOverviewTips') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
        </el-descriptions-item>
        <el-descriptions-item>
          {{ rowDetail.backUpFileProgress }}
          <template slot="label">
            {{ $t('pages.backupStatusAllProgress') }}
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                {{ $t('pages.backupStatusAllProgressTips') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.backupSuccessNum')">
          {{ rowDetail.backupNum }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.backupFailNum')">
          {{ rowDetail.failedNum }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.backupTotalNum')">
          {{ rowDetail.backupTotalNum }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.backupSize')">
          {{ rowDetail.backupSize }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.backUpTotalSize')">
          {{ rowDetail.backupTotalSize }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider content-position="left">{{ $t('pages.backupTerminalList') }}</el-divider>
      <div style="margin-bottom: 5px;">
        <el-select v-model="query.statusType" :style="`width: ${ lang == 'en' ? '304' : '200'}px; margin-bottom: 5px`">
          <el-option v-for="status in statusList" :key="status.value" :value="status.value" :label="status.name"></el-option>
        </el-select>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :multi-select="false" :row-data-api="rowDataApi" :height="380">
        <template slot="popoverContent" slot-scope="props">
          <ul class="detail-list">
            <li v-for="(item, index) in props.detail" :key="index">{{ item }}</li>
          </ul>
        </template>
      </grid-table>
    </div>
  </el-dialog>
</template>

<script>

import { Tooltip } from 'element-ui'
import { getTermStatusList, getTaskDetail } from '@/api/dataEncryption/smartBackup/fullDiskScanBackup'
import { formatFileSize, formatCount } from '@/utils'
import moment from 'moment';

export default {
  name: 'BackupDiskScanDetail',
  data() {
    return {
      visible: false,
      colModel: [
        { prop: 'scanStartTime', label: this.$t('pages.scanStartTime'), width: '150', sort: 'custom' },
        { prop: 'termName', label: 'terminalName', width: '100', type: 'showDetail', searchType: 'terminal', searchParam: 'termId' },
        { prop: 'userName', label: 'user', width: '100', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'taskState', label: 'status', width: '100', formatter: this.statusFormatter },
        { prop: 'backupProgress', type: 'popover', childData: 'backupProgressDetail', renderHeader: this.renderProgressHeader, label: this.$t('pages.backupStatusProgress'), width: '100' },
        { label: 'backupSize', width: '150', renderHeader: this.renderFileSizeHeader, formatter: this.formatBackupFileSize },
        { label: this.$t('pages.fileRecord'), type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('506'),
          buttons: [
            { label: 'details', click: this.handleDetail }
          ]
        }
      ],
      opTypeOptions: [],
      statusList: [
        { value: -1, name: this.$t('pages.allScanTerminal') },
        { value: 0, name: this.$t('pages.unstatedScanTerminal') },
        { value: 1, name: this.$t('pages.statedScanTerminal') },
        { value: 2, name: this.$t('pages.endScanTerminal') },
        { value: 3, name: this.$t('pages.pauseScanTerminal') },
        { value: 4, name: this.$t('pages.stopScanTerminal') }
      ],
      query: {
        statusType: -1,
        stgGuid: undefined
      },
      taskId: undefined,
      taskName: undefined,
      taskStatus: undefined,
      taskEndDate: undefined,
      rowDetail: {},
      defaultRowDetail: {
        backingUpTermNum: undefined,
        backupNum: undefined,
        backupTotalNum: undefined,
        failedNum: undefined,
        backUpFileProgress: undefined,
        backupSize: undefined,
        backupTotalSize: undefined,
        backUpFileSizeAndTotal: undefined
      },
      statusOptions: { 0: this.$t('pages.executing'), 1: this.$t('pages.stopInvoke'), 2: this.$t('pages.pauseInvoke'), 3: this.$t('pages.expired'), 4: this.$t('components.completed') }
    }
  },
  computed: {
    lang() {
      return this.$store.getters.language
    }
  },
  watch: {
    'query.statusType'() {
      this.reloadTableData()
    }
  },
  created() {

  },
  methods: {
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTermStatusList(searchQuery);
    },
    statusFormatter: function(row, data) {
      if (data <= 0) {
        return this.$t('pages.notStarted')
      }
      if (data === 1) {
        return this.$t('pages.startScan')
      }
      if (data === 2) {
        return this.$t('pages.endScan')
      }
      if (data === 3) {
        return this.$t('pages.pauseScan')
      }
      if (data === 4) {
        return this.$t('pages.stopScan')
      }
    },
    show(row) {
      this.query.taskId = row.id
      this.query.stgGuid = row.stgGuid
      this.taskId = row.id
      this.taskName = row.name;
      this.taskStatus = row.completed === 1 ? 4 : row.runType
      this.taskEndDate = row.endDate
      this.query.statusType = -1
      this.visible = true
      this.$nextTick(() => {
        this.initBaseInfo();
        this.reloadTableData()
      })
    },
    reloadTableData() {
      const tableRef = this.$refs.logList
      if (tableRef) {
        tableRef.execRowDataApi()
      }
    },
    initBaseInfo() {
      this.rowDetail = Object.assign({}, this.defaultRowDetail)
      const searchQuery = Object.assign({}, this.query)
      getTaskDetail(searchQuery).then(resp => {
        if (resp.data) {
          this.rowDetail = resp.data
        } else {
          this.rowDetail.taskState = 0
        }
      }).catch(() => {})
    },
    renderHeader(h, column, tooltip) {
      return h('span', [
        h('span', { attrs: { title: column.label }}, column.label),
        h(Tooltip,
          {
            props: {
              effect: 'dark',
              placement: 'bottom-start',
              content: tooltip
            }
          },
          [h('i', { class: 'el-icon-info' })]
        )
      ])
    },
    renderProgressHeader(h, { column, $index }) {
      return this.renderHeader(h, column, this.$t('pages.backUpFileProgressSimpleTips'))
    },
    renderFileSizeHeader(h, { column, $index }) {
      return this.renderHeader(h, column, this.$t('pages.backUpFileTotalSizeSimpleTips2'))
    },
    progressFormat(row, data) {
      if (!row.backupTotalNum) {
        return '-'
      }
      // 备份任务进度=(已备份数/全盘扫描备份总数)
      return formatCount(row.backupNum) + '/' + formatCount(row.backupTotalNum);
    },
    taskStatusFormatter(data) {
      if (data === undefined) {
        return this.$t('pages.noExecute')
      }
      // 当runType=1，判断是否已过期，若已过期，则状态更改为3 已过期
      if (data === 1 && this.taskEndDate) {
        if (moment(this.taskEndDate).isBefore(moment(moment(new Date()).add(1, 'days').format('YYYY-MM-DD')))) {
          return this.statusOptions[3]
        }
      }
      return this.statusOptions[data]
    },
    formatBackupFileSize(row) {
      if (!row.backupTotalSize && row.backupTotalSize !== 0 || !row.backupSize && row.backupSize !== 0) {
        return '-'
      }
      return formatFileSize(row.backupSize) + '/' + formatFileSize(row.backupTotalSize)
    },
    handleFilter() {
      this.initBaseInfo()
      this.$refs.logList && this.$refs.logList.execRowDataApi()
    },
    handleDetail(row) {
      this.$router.push({
        path: '/behaviorAuditing/encryptionLog/smartBackupLog',
        query: {
          entityId: row.termId,
          entityType: '1',
          stgGuid: row.taskId,
          bussType: 1,
          createDate: moment(row.scanStartTime).format('YYYY-MM-DD')
        }
      })
    }
  }

}
</script>

<style lang="scss" scoped>
  >>>.el-dialog__body {
    padding: 0 15px 15px 15px;
    max-height: none;
  }
  >>>.el-divider.el-divider--horizontal {
    margin: 15px 0 15px;
  }
  >>>.el-dialog {
    margin-top: 5vh!important;
  }
</style>
