<!-- 全盘扫描备份任务状态 -->
<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    </div>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <!--<el-button type="primary" size="mini" title="优先备份" :disabled="!!!termId" @click="firstBackup">
          <svg-icon icon-class="register" />
        </el-button> -->
        <div class="searchCon">
          <span>
            <!-- 备份状态 -->
            {{ $t('pages.backupStatus') }}
            <el-select v-model="query.statusType" clearable>
              <el-option :value="null" :label="$t('pages.all')"/>
              <el-option v-for="item in backupStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </span>
          <el-input v-model="query.taskName" v-trim clearable :placeholder="$t('pages.softwareTask_Validate4')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="statusList"
        :multi-select="false"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="() => false"
      >
        <template slot="popoverContent" slot-scope="props">
          <ul class="detail-list">
            <li v-for="(item, index) in props.detail" :key="index">{{ item }}</li>
          </ul>
        </template>
      </grid-table>
    </div>
  </div>
</template>

<script>
import { refreshPage } from '@/utils'
import { Tooltip } from 'element-ui'
import { getBackupTaskStatusPage } from '@/api/dataEncryption/smartBackup/fullDiskScanBackup'
import { firstBackup } from '@/api/dataEncryption/smartBackup/timelyBackup';
import { formatFileSize } from '@/utils'
import moment from 'moment';

export default {
  name: 'FullDiskScanBackupStatus',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      colModel: [
        // { prop: 'taskId', label: 'taskNum', width: '60' },
        { prop: 'taskName', label: 'taskName2', width: '100' },
        // { prop: 'objectName', label: '任务对象', width: '100' },
        { prop: 'termName', label: 'terminalName', width: '80', type: 'showDetail', searchType: 'terminal', searchParam: 'termId' },
        { prop: 'deptName', label: 'terminalGroup', width: '80', type: 'showDetail', searchType: 'department', searchParam: 'termGroupId' },
        { prop: 'userName', label: 'user', width: '80', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'scanStartTime', label: this.$t('pages.scanStartTime'), width: '100' },
        { prop: 'scanEndTime', label: this.$t('pages.scanEndTime'), width: '100' },
        { prop: 'taskState', label: this.$t('pages.backupStatus'), width: '80', formatter: this.backupStateFormat },
        { prop: 'backupSize', label: this.$t('pages.backupSize'), width: '100', formatter: (item) => formatFileSize(item.backupSize) },
        { prop: 'backupTotalSize', label: this.$t('pages.backUpTotalSize'), width: '100', formatter: (item) => formatFileSize(item.backupTotalSize) },
        { prop: 'backupProgress', label: this.$t('pages.backupStatusProgress'), width: '80', type: 'popover', childData: 'backupProgressDetail', renderHeader: this.renderProgressHeader },
        { label: this.$t('pages.fileRecord'), type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('506'),
          buttons: [
            { label: 'details', click: this.handleDetail }
          ]
        }
        // { label: '备份文件大小', width: '80', renderHeader: this.renderBackupSizeHeader, formatter: this.backupSizeFormat }
      ],
      checkedEntityNode: {},
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        statusType: undefined
      },
      showTree: true,
      treeable: true,
      termId: undefined,
      backupStatusOptions: [
        // { value: 0, label: this.$t('pages.notStarted') },
        { value: 1, label: this.$t('pages.inProgress') },
        { value: 2, label: this.$t('components.completed') },
        { value: 3, label: this.$t('pages.paused') },
        { value: 4, label: this.$t('pages.stopped') }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['statusList']
    },
    isGroup() {
      const objectType = this.query.objectType
      return !objectType || this.query.dataType === 'G' || objectType === 3 || objectType === '3'
    }
  },
  methods: {
    strategyTargetNodeChange(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.termId = this.isGroup ? '' : checkedNode.dataId
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    refresh() {
      return refreshPage(this)
    },
    renderProgressHeader(h, { column, $index }) {
      return this.renderHeader(h, column, this.$t('pages.backUpFileProgressSimpleTips'))
    },
    renderBackupSizeHeader(h, { column, $index }) {
      return this.renderHeader(h, column, this.$t('pages.backUpFileTotalSizeSimpleTips2'))
    },
    renderHeader(h, column, tooltip) {
      return h('span', [
        h('span', { attrs: { title: column.label }}, column.label),
        h(Tooltip,
          {
            props: {
              effect: 'dark',
              placement: 'bottom-start',
              content: tooltip
            }
          },
          [h('i', { class: 'el-icon-info' })]
        )
      ])
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getBackupTaskStatusPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    backupSizeFormat(row, data) {
      return row.backupSize + '/' + row.backupTotalSize
    },
    backupStateFormat(row, data) {
      if (!data) {
        return '-'
      }
      let label = '-';
      this.backupStatusOptions.forEach((item) => {
        if (item.value === data) {
          label = item.label;
        }
      })
      return label
    },
    firstBackup() {
      firstBackup(this.termId).then(() => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.priorityBackupSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch((err) => {
        console.log(err)
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.priorityBackupFailure'),
          type: 'error',
          duration: 2000
        })
      })
    },
    handleDetail(row) {
      this.$router.push({
        path: '/behaviorAuditing/encryptionLog/smartBackupLog',
        query: {
          entityId: row.termId,
          entityType: '1',
          stgGuid: row.taskId,
          bussType: 1,
          createDate: moment(row.scanStartTime).format('YYYY-MM-DD')
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
