<!-- 全盘扫描备份 -->
<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="tabName" type="card">
      <el-tab-pane :label="$t('pages.trcbrFullDiskScan')" name="bakStg">
        <full-disk-scan-backup-stg :listable="listable" :formable="formable" :export-able="exportAble" :import-able="importAble"/>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.taskPerformance')" name="bakStatus">
        <full-disk-scan-backup-status :listable="listable"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import FullDiskScanBackupStg from './strategy'
import FullDiskScanBackupStatus from './status'

export default {
  name: 'FullDiskScanBackup',
  components: { FullDiskScanBackupStg, FullDiskScanBackupStatus },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      tabName: 'bakStg'
    }
  }
}
</script>

<style scoped>

</style>
