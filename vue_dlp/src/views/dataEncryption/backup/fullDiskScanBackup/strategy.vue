<!-- 全盘扫描备份策略页面 -->
<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    </div>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addTask1') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable || !deleteBtnAble" @click="handleDelete">
          {{ $t('pages.deleteTask') }}
        </el-button>
        <el-button icon="el-icon-video-play" size="mini" :disabled="!startScanBtnAble" @click="handleStart">
          {{ $t('pages.backupExecuteTask') }}
        </el-button>
        <el-button icon="el-icon-switch-button" size="mini" :disabled="!pauseScanBtnAble" @click="handlePause">
          {{ $t('pages.backupPauseTask') }}
        </el-button>
        <el-button icon="el-icon-switch-button" size="mini" :disabled="!stopScanBtnAble" @click="handleStop">
          {{ $t('pages.backupStopTask') }}
        </el-button>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareTask_Validate4')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <full-disk-scan-backup-dlg ref="stgDlg" :formable="formable" :active-able="treeable" :entity-node="checkedEntityNode" @submitEnd="submitEnd"/>
    <detail ref="detail"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importDocumentTrackStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgTypeNumber"
      @success="importSuccess"
    />
  </div>
</template>

<script>
// import { osTypeFormatter } from '@/utils/formatter'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete, entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils';
import { exportStg } from '@/api/stgCommon'
import ImportStg from '@/views/common/importStg'
import FullDiskScanBackupDlg from './editDlg'
import Detail from './detail'
import moment from 'moment'

import { getScanPage, deleteScan, startScan, stopScan, pauseScan } from '@/api/dataEncryption/smartBackup/fullDiskScanBackup'

export default {
  name: 'FullDiskScanBackupStg',
  components: { ImportStg, FullDiskScanBackupDlg, Detail },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgTypeNumber: 256,
      colModel: [
        // { prop: 'id', label: 'taskNum', width: '100', fixed: true },
        { prop: 'name', label: 'taskName2', width: '150', fixed: true },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'stgMessage', label: 'stgMessage', width: '150' },
        // { prop: 'osType', label: 'osType', formatter: osTypeFormatter },
        { prop: 'endDate', label: 'endDate', width: '100' },
        { prop: 'runType', label: 'taskStatus', iconFormatter: this.activeFormatter, formatter: this.statusFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate },
            { label: 'details', click: this.handleDetail, isShow: (row) => row.runType === 0 || row.runType === 1 || row.runType === 2 || row.runType === 3 }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {},
      startScanBtnAble: false,
      pauseScanBtnAble: false,
      stopScanBtnAble: false,
      deleteBtnAble: false,
      statusOptions: { 0: this.$t('pages.executing'), 1: this.$t('pages.stopInvoke'), 2: this.$t('pages.pauseInvoke'), 3: this.$t('pages.expired') },
      statusNameOptions: { 0: this.$t('pages.backupExecuteTask'), 1: this.$t('pages.backupStopTask'), 2: this.$t('pages.backupPauseTask') }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    strategyTargetNodeChange(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    refresh() {
      return refreshPage(this)
    },
    handleShow(row) {
      this.$refs['stgDlg'].handleShow(row)
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteScan({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgTypeNumber })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getScanPage(searchQuery);
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    selectionChangeEnd(rowDatas) {
      this.startScanBtnAble = false
      this.pauseScanBtnAble = false
      this.stopScanBtnAble = false
      this.deleteBtnAble = true
      if (rowDatas.length > 0) {
        for (let i = 0; i < rowDatas.length; i++) {
          const rowData = rowDatas[i]
          this.startScanBtnAble = this.startScanBtnAble ? this.startScanBtnAble : !this.isStartTask(rowData) && !this.isExpiredTask(rowData)
          this.deleteBtnAble = !this.deleteBtnAble ? this.deleteBtnAble : !this.isStartTask(rowData) || this.isCompletedTask(rowData)
          this.pauseScanBtnAble = this.pauseScanBtnAble ? this.pauseScanBtnAble : this.isStartTask(rowData)
          this.stopScanBtnAble = this.stopScanBtnAble ? this.stopScanBtnAble : !this.isStopTask(rowData)
        }
      }
      enableStgDelete(rowDatas, this)
    },
    entityFormatter(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick(row, data) {
      return entityLink(row, data, this)
    },
    buttonFormatter(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    isStopTask(rowData) {
      return rowData && (!rowData.runType && rowData.runType !== 0 || rowData.runType === 1)
    },
    isPauseTask(rowData) {
      return rowData && rowData.runType === 2
    },
    isStartTask(rowData) {
      return rowData && rowData.runType === 0
    },
    // 是否是过期任务
    isExpiredTask(rowData) {
      if (rowData && rowData.runType === 1 && rowData.endDate) {
        if (moment(rowData.endDate).isBefore(moment(moment(new Date()).add(1, 'days').format('YYYY-MM-DD')))) {
          return true;
        }
      }
      return false;
    },
    isCompletedTask(rowData) {
      return rowData.completed === 1;
    },
    changeScanStatus(status, targetIds) {
      if (targetIds.length > 0) {
        const statusName = this.statusNameOptions[status]
        this.$confirmBox(this.$t('pages.diskScan_Msg5', { statusName: statusName }), this.$t('text.prompt')).then(() => {
          const func = status === 0 ? startScan : status === 1 ? stopScan : pauseScan
          func({ ids: targetIds.join(',') }).then(respond => {
            this.gridTable.execRowDataApi()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          })
        }).catch(() => {})
      }
    },
    handleStart() {
      const selectedDatas = this.gridTable.getSelectedDatas()
      if (selectedDatas.length > 0) {
        const targetIds = []
        for (let i = 0; i < selectedDatas.length; i++) {
          const rowData = selectedDatas[i]
          if (!this.isStartTask(rowData)) {
            targetIds.push(rowData.id)
          }
        }
        this.changeScanStatus(0, targetIds)
      }
    },
    handleStop() {
      const selectedDatas = this.gridTable.getSelectedDatas()
      if (selectedDatas.length > 0) {
        const targetIds = []
        for (let i = 0; i < selectedDatas.length; i++) {
          const rowData = selectedDatas[i]
          if (!this.isStopTask(rowData)) {
            targetIds.push(rowData.id)
          }
        }
        this.changeScanStatus(1, targetIds)
      }
    },
    handlePause() {
      const selectedDatas = this.gridTable.getSelectedDatas()
      if (selectedDatas.length > 0) {
        const targetIds = []
        for (let i = 0; i < selectedDatas.length; i++) {
          const rowData = selectedDatas[i]
          if (!this.isPauseTask(rowData)) {
            targetIds.push(rowData.id)
          }
        }
        this.changeScanStatus(2, targetIds)
      }
    },
    activeFormatter: function(row, data) {
      return row.runType === 0 ? 'active' : ''
    },
    statusFormatter: function(row, data) {
      if (data === undefined) {
        return this.$t('pages.noExecute')
      }
      if (row.completed === 1) {
        return this.$t('components.completed')
      }
      // 当runType=1，判断是否已过期，若已过期，则状态更改为3 已过期
      if (row.runType === 1 && row.endDate) {
        if (moment(row.endDate).isBefore(moment(moment(new Date()).add(1, 'days').format('YYYY-MM-DD')))) {
          return this.statusOptions[3]
        }
      }
      return this.statusOptions[data]
    },
    handleDetail(row) {
      this.$refs['detail'].show(row)
    }
  }
}
</script>

<style scoped>

</style>
