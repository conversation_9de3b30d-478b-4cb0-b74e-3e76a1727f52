<!--生成缓存清理验证码-->
<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.backupClearCacheVerificationCode')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="vcVisible"
    :width="$store.getters.language === 'en' ? '455px' : '400px'"
    @dragDialog="()=>{}"
  >
    <Form ref="vcForm" :model="vcForm" label-position="right" label-width="58px">
      <FormItem :label="$t('pages.randomCode')" label-width="58px" :extra-width="{en: 75}">
        <el-input v-model="vcForm.random" maxlength="" @input="vcForm.random=vcForm.random.replace(/[^\w\.\/]/ig,'')"></el-input>
      </FormItem>
      <FormItem :label="$t('pages.captcha')" label-width="58px" :extra-width="{en: 75}">
        <el-input v-model="vcForm.verifyCode" maxlength=""></el-input>
      </FormItem>
    </Form>
    <span slot="footer" class="dialog-footer" >
      <el-button
        type="primary"
        style="float:left;"
        :loading="vcLoading"
        :disabled="!vcForm.random"
        @click="getVerificationCode"
      >{{ $t('pages.generateCaptcha') }}</el-button>
      <el-button @click="vcVisible = false">{{ $t('pages.exit') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>

import { getVerificationCode } from '@/api/dataEncryption/smartBackup/timelyBackup';

export default {
  name: 'BackupVerificationCode',
  props: {
  },
  data() {
    return {
      vcVisible: false,
      vcLoading: false,
      vcForm: {
        random: '',
        verifyCode: ''
      }
    }
  },
  methods: {
    show(termId) {
      this.vcForm = {
        random: '',
        verifyCode: '',
        termId: termId
      }
      this.vcVisible = true
    },
    getVerificationCode() {
      this.vcLoading = true
      const data = {
        random: this.vcForm.random,
        termId: this.vcForm.termId
      }
      getVerificationCode(data).then(res => {
        this.vcForm.verifyCode = res.data
        this.vcLoading = false
      }).catch(() => {
        this.vcLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
