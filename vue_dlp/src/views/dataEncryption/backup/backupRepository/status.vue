<!--
  备份状态（包含即时和全盘）
-->
<template>
  <div class="app-container">
    <div v-if="listable && treeAble" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" :os-type-filter="7" @data-change="strategyTargetNodeChange"/>
    </div>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeAble" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button v-show="!!termId && hasPermission('530')" type="primary" size="mini" :title="$t('pages.backupClearCacheTips')" :disabled="!!!termIdOnline" @click="showClearCache">
          <svg-icon icon-class="setting" />
          {{ $t('pages.backupClearCache') }}
        <!--{{ formatClearStatus(termData) }}-->
        </el-button>

        <el-button v-show="!!termId" type="primary" size="mini" :title="$t('pages.backupClearCacheVerificationCodeTips')" @click="showVerifyCode">
          <svg-icon icon-class="setting" />
          {{ $t('pages.backupClearCacheVerificationCode') }}
        </el-button>
        <!--        <el-button type="primary" size="mini" title="优先备份" :disabled="!!!termId" @click="firstBackup">
          优先备份
        </el-button> -->
      </div>
      <div v-if="!query.objectType"></div>
      <div v-else-if="isGroup" class="backup-status-description">
        <el-descriptions class="margin-top" :column="4" size="small" border>
          <el-descriptions-item :label="$t('table.deptName')">
            {{ checkedEntityNode.label }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('pages.backupStatusTerminalOverview') }}
              <el-tooltip :content="$t('pages.backupStatusTerminalOverviewTips')">
                <i class="el-icon-info"/>
              </el-tooltip>
            </template>
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ groupData.backingUpTermNum ? groupData.backingUpTermNum : '-' }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('pages.backupStatusProgress') }}
              <el-tooltip :content="$t('pages.backupStatusProgressTips')">
                <i class="el-icon-info"/>
              </el-tooltip>
            </template>
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ groupData.backUpFileProgress ? groupData.backUpFileProgress : '-' }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('pages.backUpFileTotalSize') }}
              <el-tooltip :content="$t('pages.backUpFileTotalSizeSimpleTips')">
                <i class="el-icon-info"/>
              </el-tooltip>
            </template>
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ groupData.backUpFileTotalSize ? formatFileSize(groupData.backUpFileTotalSize) : '-' }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.backupNum')">
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ groupData.backupNum ? formatCount(groupData.backupNum) : '-' }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.unbackupNum')">
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ groupData.unbackupNum ? formatCount(groupData.unbackupNum) : '-' }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.backupSize')">
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ groupData.backupSize ? formatFileSize(groupData.backupSize) : '-' }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.unbackupSize')">
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ groupData.unbackupSize ? formatFileSize(groupData.unbackupSize) : '-' }}
            </template>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-else class="backup-status-description">
        <el-descriptions class="margin-top" :column="4" size="small" border>
          <el-descriptions-item :label="$t('pages.terminalName')">
            {{ checkedEntityNode.label }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.backupLinkServerId')">
            <i v-if="loading || topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ termData.serverId ? termData.serverId : $t('pages.unconnected') }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.onlineStatus')">
            {{ checkedEntityNode.online ? $t('pages.online') : $t('pages.offline') }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.backupStatus')">
            <i v-if="loading || topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ termData.backupState === 1 ? $t('pages.backuping') : $t('pages.waitBackup') }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('pages.backupStatusProgress') }}
              <el-tooltip :content="$t('pages.backupStatusProgressTips')">
                <i class="el-icon-info"/>
              </el-tooltip>
            </template>
            <i v-if="loading || topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ termBackupNumProgress }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.backupNum')">
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ formatCount(termData.backupNum) }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.unbackupNum')">
            <i v-if="loading || topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ formatCount(termData.unbackupNum) }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.backupSize')">
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ formatFileSize(termData.backupSize) }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.unbackupSize')">
            <i v-if="loading || topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ formatFileSize(termData.unbackupSize) }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('pages.backUpFileTotalSize') }}
              <el-tooltip :content="$t('pages.backUpFileTotalSizeSimpleTips')">
                <i class="el-icon-info"/>
              </el-tooltip>
            </template>
            <i v-if="loading || topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ termBackupSize }}
            </template>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.finalBackupTime')">
            <i v-if="topLoading" class="el-icon-loading"/>
            <template v-else>
              {{ termData.lastBackupTime ? termData.lastBackupTime : '-' }}
            </template>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <grid-table
        ref="statusList"
        v-loading="loading"
        :show-pager="false"
        :multi-select="false"
        :col-model="colModel"
        :row-datas="scanBackupFileList"
      />
      <clear-cache ref="clearCache"/>
      <backup-verification-code ref="backupVerificationCode" />
    </div>
  </div>
</template>

<script>
import {
  enableTermStatus,
  firstBackup,
  getGroupTerminalIds,
  getTermDetail
} from '@/api/dataEncryption/smartBackup/timelyBackup';
import { formatCount, formatFileSize } from '@/utils'
import ClearCache from './clearCache';
import BackupVerificationCode from './backupVerificationCode';

export default {
  name: 'BackupStatus',
  components: { ClearCache, BackupVerificationCode },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      colModel1: [
        { prop: 'termName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'termId' },
        // { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'backupState', label: this.$t('pages.backupStatus'), width: '80', formatter: this.backupStateFormat },
        { prop: 'progress', label: this.$t('pages.backupStatusProgress'), width: '80', formatter: this.formatPercent },
        { prop: 'backupNum', label: this.$t('pages.backupNum'), width: '80', formatter: (item) => formatCount(item.backupNum) },
        { prop: 'unbackupNum', label: this.$t('pages.unbackupNum'), width: '80', formatter: (item) => formatCount(item.unbackupNum) },
        { prop: 'backupSize', label: this.$t('pages.backupSize'), width: '80', formatter: (item) => formatFileSize(item.backupSize) },
        // { prop: 'backupTotalSize', label: '备份总大小', width: '80', formatter: (item) => formatFileSize(item.backupTotalSize) },
        { prop: 'unbackupSize', label: this.$t('pages.unbackupSize'), width: '80', formatter: (item) => formatFileSize(item.unbackupSize) },
        { prop: 'lastBackupTime', label: this.$t('pages.lastBackupTime'), width: '80' }
      ],
      colModel2: [
        { prop: 'fileName', label: 'fileName', width: '200' },
        { prop: 'createTime', label: 'createDate', width: '80' },
        { prop: 'modifyTime', label: 'updateTime', width: '80' },
        { prop: 'fileSize', label: 'maxFileSize2', width: '80', formatter: (item) => formatFileSize(item.fileSize) },
        { prop: 'backupState', label: this.$t('pages.backupStatus'), width: '80', formatter: this.backupStateFormat },
        { prop: 'backupProgress', label: this.$t('pages.backupStatusProgress'), width: '80', formatter: this.backupProgressFormat }
      ],
      checkedEntityNode: {},
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeAble: true,
      groupId: undefined,
      termId: undefined,
      termIdOnline: false,
      scanBackupFileList: [],
      topLoading: false,
      loading: false,
      groupData: {},
      termData: {},
      timer: undefined,
      clearStatusOptions: [
        { value: 0, label: this.$t('pages.backupClearCache') },
        { value: 1, label: this.$t('pages.cacheClearing') },
        { value: 2, label: this.$t('pages.cacheClearSuccess') },
        { value: 3, label: this.$t('pages.cacheClearFail') }
      ],
      currentRequestId: null,
      currentGroupId: null
    }
  },
  computed: {
    gridTable() {
      return this.$refs['statusList']
    },
    isGroup() {
      const objectType = this.query.objectType
      return !objectType || this.query.dataType === 'G' || objectType === 3 || objectType === '3'
    },
    colModel() {
      return this.isGroup ? this.colModel1 : this.colModel2
    },
    isBackup() {
      // scanBackupFileList中若有存在backupState=1，则返回true
      return this.scanBackupFileList.some(item => item.backupState === 1)
    },
    // 备份数量百分比
    termBackupNumProgress() {
      const backupNum = this.termData.backupNum;
      const unbackupNum = this.termData.unbackupNum || 0;
      if (!backupNum) {
        return '-'
      }
      return Number(((backupNum * 100 / (backupNum + unbackupNum)).toFixed(2))) + '%';
    },
    // 备份总大小
    termBackupSize() {
      if (!this.termData.backupSize && !this.termData.unbackupSize) {
        return '-'
      }
      return formatFileSize(Number(this.termData.backupSize + this.termData.unbackupSize));
    }
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    this.clearTimer();
  },
  deactivated() {
    // 组件停用时--清除监听
    this.clearTimer();
  },
  methods: {
    formatCount,
    formatFileSize,
    async strategyTargetNodeChange(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.scanBackupFileList = [];
      this.termData = {}
      this.groupData = {}
      this.termId = this.isGroup ? '' : checkedNode.dataId
      this.termIdOnline = this.isGroup ? '' : checkedNode.online
      if (this.isGroup) {
        this.groupId = checkedNode ? checkedNode.dataId : undefined
      }
      if (!this.isGroup && !checkedNode.online) {
        clearInterval(this.timer)
        this.termData = {}
        this.groupData = {}
        this.scanBackupFileList = []
        // 如果没有模块，直接返回
        if (!await this.checkEnableTermStatus(this.termId)) {
          return;
        } else {
          // 如果没有上线，那么直接拿数据库数据
          this.initTermData()
          return;
        }
      }
      await this.refresh();
    },
    async initGroupData() {
      if (!this.isGroup && this.topLoading) {
        return;
      }
      // 如果当前已经是同一个 groupId 的请求，直接返回
      if (this.topLoading && this.groupId === this.currentGroupId) {
        return;
      }
      this.groupData = {}
      this.scanBackupFileList = []
      // 请求ID
      this.currentRequestId = new Date().getTime();
      this.currentGroupId = this.groupId
      this.topLoading = true;

      try {
        // Step 1: 获取当前分组下的终端ID列表
        const groupResp = await getGroupTerminalIds({ groupId: this.groupId });
        const data = groupResp.data
        const terminalIds = data.termList.map(item => item.termId);
        const onlineTerminalIds = data.onlineTerminalIds
        const totalSize = data.totalSize;
        const requestId = this.currentRequestId;
        if (!terminalIds.length) {
          this.scanBackupFileList = [];
          this.groupData = {};
          this.topLoading = false;
          return;
        }
        const termMap = data.termList.reduce((acc, item) => {
          // 以 termId 为 key，item 自身为 value
          acc[item.termId] = item;
          return acc;
        }, {});

        // Step 2: 清空旧数据
        // 🚫 非当前请求，终止
        if (this.currentRequestId !== requestId) {
          this.scanBackupFileList = [];
          this.groupData = {};
          return;
        }
        this.scanBackupFileList = [];

        let completedCount = 0;
        const allTermData = {};

        // Step 3: 遍历每个终端，发送请求
        terminalIds.forEach(termId => {
          // 如果不在线的，取数据库的值
          if (!onlineTerminalIds.includes(termId)) {
            allTermData[termId] = termMap[termId];
            const index = this.scanBackupFileList.findIndex(
              item => String(item.termId) === String(termId)
            );
            if (index > -1) {
              this.scanBackupFileList.splice(index, 1, termMap[termId]);
            } else {
              this.scanBackupFileList.push(termMap[termId]);
            }
            completedCount++;
            if (completedCount === terminalIds.length) {
              this.calculateGroupStats(allTermData, totalSize);
              this.topLoading = false;
            }
            return
          }
          this.$socket.sendToUser(termId, '/getBackupStatus/' + termId, termId, (respond, handle) => {
            handle.close();
            // 🚫 非当前请求，终止
            if (this.currentRequestId !== requestId) {
              this.scanBackupFileList = [];
              this.groupData = {};
              return;
            }
            console.log('respond.data：', respond.data)
            if (respond.data) {
              const backupData = respond.data.backupStatus || {};
              const status = termMap[termId];
              const termData = {
                termId,
                termName: backupData.termName || status.termName || `终端${termId}`,
                backupState: backupData.backupState || 0,
                backupTotalNum: backupData.backupTotalNum || status.backupTotalNum || 0,
                unbackupNum: backupData.unbackupNum || 0,
                unbackupSize: backupData.unbackupSize || 0,
                progress: status.backupProgress || 0,
                backupNum: status.backupNum || 0,
                backupSize: status.backupSize || 0,
                lastBackupTime: status.lastBackupTime || '-'
              };
              console.log('termData:', termData)
              // 存储单个终端数据用于后续统计
              allTermData[termId] = termData;

              // 插入或更新到 scanBackupFileList
              const index = this.scanBackupFileList.findIndex(
                item => String(item.termId) === String(termId)
              );
              if (index > -1) {
                this.scanBackupFileList.splice(index, 1, termData);
              } else {
                this.scanBackupFileList.push(termData);
              }
              // 更新视图
              // this.$refs.statusList.refresh();

              // 统计完成数量
              completedCount++;
              if (completedCount === terminalIds.length) {
                this.calculateGroupStats(allTermData, totalSize);
                this.topLoading = false;
              }
            }
          }, (handle) => {
            handle.close();
            console.error('超时', termId);
            // 如果超时，那么也取数据库里的值
            allTermData[termId] = termMap[termId];
            const index = this.scanBackupFileList.findIndex(
              item => String(item.termId) === String(termId)
            );
            if (index > -1) {
              this.scanBackupFileList.splice(index, 1, termMap[termId]);
            } else {
              this.scanBackupFileList.push(termMap[termId]);
            }
            completedCount++;
            if (completedCount === terminalIds.length) {
              this.calculateGroupStats(allTermData, totalSize);
              this.topLoading = false;
            }
          });
        });
      } catch (error) {
        console.error('获取终端列表失败:', error);
        this.topLoading = false;
      }
    },
    calculateGroupStats(termDataMap, totalSize) {
      const stats = {
        backUpFileTotalSize: 0,
        backingUpTermNum: 0,
        backUpFileProgress: 0,
        backupNum: 0,
        unbackupNum: 0,
        backupSize: 0,
        unbackupSize: 0
      };
      const termNum = Object.keys(termDataMap).length;
      Object.values(termDataMap).forEach(data => {
        stats.backUpFileTotalSize += data.backupSize + data.unbackupSize;
        stats.backupNum += data.backupNum;
        stats.unbackupNum += data.unbackupNum;
        stats.backupSize += data.backupSize;
        stats.unbackupSize += data.unbackupSize;
      });
      stats.backingUpTermNum = termNum + '/' + totalSize;

      stats.backUpFileProgress = stats.backupNum + stats.unbackupNum
        ? ((stats.backupNum * 100) / (stats.backupNum + stats.unbackupNum)).toFixed(2) + '%' : '0%';

      this.groupData = stats;
    },
    initTermData() {
      if (this.isGroup && this.topLoading) {
        return;
      }
      this.topLoading = true
      getTermDetail({ termId: this.termId }).then(resp => {
        if (resp.data) {
          // 如果协议已经获取终端的数据，那么不要覆盖 unbackupNum
          if (!this.loading) {
            this.termData = {
              ...resp.data,
              ...this.termData
            }
          } else {
            this.termData = resp.data
          }
        } else {
          this.termData = {}
        }
        this.topLoading = false
      }).catch(() => {
        this.topLoading = false
      })
    },
    initTermFileListData() {
      if (this.loading) {
        return
      }
      this.loading = true
      this.$socket.sendToUser(this.termId, '/getBackupFileList', this.termId, (respond, handle) => {
        handle.close()
        if (!respond.data || !respond.data.backupFile || respond.data.backupFile.termId != this.termId) {
          this.loading = false
          return
        }
        if (respond.data && respond.data.backupFile && respond.data.backupFile.fileList) {
          this.loading = false
          respond.data.backupFile.fileList.forEach((item, index) => {
            const p = {
              id: Date.now() + index,
              fileName: item.fileName,
              fileSize: item.fileSize,
              createTime: item.createTime,
              modifyTime: item.modifyTime,
              backupState: item.backupState,
              backupProgress: item.backupProgress
            }
            this.scanBackupFileList.push(p)
          })
          console.log('this.termData:', this.termData, respond.data.backupFile);
          this.termData.unbackupNum = respond.data.backupFile.unbackupNum
          this.termData.unbackupSize = respond.data.backupFile.unbackupSize
          this.termData.serverId = respond.data.backupFile.serverId
          this.termData.backupState = respond.data.backupFile.backupState
        }
      }, (handle) => {
        this.loading = false
        handle.close()
        this.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    clearTimer() {
      clearInterval(this.timer)
    },
    async refresh() {
      clearInterval(this.timer)
      this.termData = {}
      this.groupData = {}
      if (!this.isGroup && !await this.checkEnableTermStatus(this.termId)) {
        return;
      }
      if (this.isGroup) {
        this.initGroupData();
        // 30秒自动刷新一次
        this.timer = setInterval(() => {
          this.initGroupData();
        }, 30000)
      } else {
        if (!this.termIdOnline) {
          // 如果没有上线，那么直接拿数据库数据
          this.initTermData()
          return;
        }
        this.initTermData();
        this.initTermFileListData();
        // 30秒自动刷新一次
        this.timer = setInterval(() => {
          this.termData = {}
          this.groupData = {}
          this.scanBackupFileList = []
          this.initTermData();
          this.initTermFileListData();
        }, 30000)
      }
    },
    async checkEnableTermStatus(termId) {
      try {
        const resp = await enableTermStatus(termId);
        if (resp.data) {
          return true;
        } else {
          this.$message({ duration: 2000, message: this.$t('pages.termNotUsedModule') });
          return false;
        }
      } catch {
        return false;
      }
    },
    rowDataApi(option) {
      // const searchQuery = Object.assign({}, this.query, option)
      return Promise.resolve({
        code: 20000,
        data: {
          items: [],
          count: 0
        }
      })
    },
    backupStateFormat(row, data) {
      return data === 1 ? this.$t('pages.backuping') : data === 0 ? this.$t('pages.waitBackup') : this.$t('pages.waitBackup')
    },
    backupProgressFormat(row, data) {
      return data + '%'
    },
    formatPercent(row) {
      if (!row.backupTotalNum) {
        return '-'
      }
      return Number(((row.backupNum * 100 / row.backupTotalNum).toFixed(2))) + '%';
    },
    calcNumber(num1, num2) {
      if (!num1 || !num2) {
        return '-'
      }
      return num1 - num2
    },
    formatBackupFileSize(row) {
      if (!row.backupSize || !row.backupTotalSize) {
        return '-'
      }
      return formatFileSize(row.backupSize) + '/' + formatFileSize(row.backupTotalSize)
    },
    firstBackup() {
      firstBackup(this.termId).then(() => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.priorityBackupSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch((err) => {
        console.log(err)
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.priorityBackupFailure'),
          type: 'error',
          duration: 2000
        })
      })
    },
    formatClearStatus(row, data) {
      if (!row.clearStatus) {
        return this.clearStatusOptions[0].label
      }
      return this.clearStatusOptions[row.clearStatus].label
    },
    showClearCache() {
      this.$refs.clearCache.show(this.termId)
    },
    showVerifyCode() {
      this.$refs.backupVerificationCode.show(this.termId)
    }
  }
}
</script>

<style lang="scss" scoped>
.backup-status-description {
  >>>.el-descriptions-item__cell{
    border: 1px solid #089ba2!important;;
  }
  >>>.el-descriptions-item__label{
    background: unset;
    color: #68A8D0;
    font-weight: 800;
  }
}
</style>
