<!--备份库管理-->
<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="tabName" type="card" @tab-click="refreshTab">
      <!--备份库-->
      <el-tab-pane lazy :label="$t('pages.backupLibrary')" name="reps">
        <file-repository ref="fileRepository"/>
      </el-tab-pane>
      <!--备份状态-->
      <el-tab-pane :label="$t('pages.backupStatus')" name="bakStatus">
        <timely-backup-status ref="bakStatus" :listable="listable"/>
      </el-tab-pane>
      <!--任务库-->
      <el-tab-pane lazy :label="$t('pages.taskLibrary')" name="taskLib">
        <task-lib ref="taskLib"/>
      </el-tab-pane>
      <!--回收站-->
      <el-tab-pane lazy :label="$t('pages.recycleBin')" name="rclb">
        <recycle-bin ref="recycleBin"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import FileRepository from './repository'
import RecycleBin from './recycleBin'
import TaskLib from './taskLib'
import TimelyBackupStatus from './status'

export default {
  name: 'SmartBackupRepository',
  components: { FileRepository, RecycleBin, TaskLib, TimelyBackupStatus },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      tabName: 'reps'
    }
  },
  methods: {
    refreshTab(tab) {
      if (tab.name === 'reps') {
        this.$refs['fileRepository'] && this.$refs['fileRepository'].refresh();
      } else if (tab.name === 'taskLib') {
        this.$refs['taskLib'] && this.$refs['taskLib'].refresh();
      } else if (tab.name === 'rclb') {
        this.$refs['recycleBin'] && this.$refs['recycleBin'].refresh();
      }
      // 如果不是 备份状态 的tab页，清空定时器，以免一直刷新
      if (tab.name !== 'bakStatus') {
        this.$refs['bakStatus'] && this.$refs['bakStatus'].clearTimer();
      }
      // 如果不是 备份库 的tab页，清空路径监听，以免其他tab页页面变化重新计算路径长度为0导致显示路径为空
      if (tab.name !== 'reps') {
        this.$refs['fileRepository'] && this.$refs['fileRepository'].removeResize();
      }
    }
  }
}
</script>

<style scoped>

</style>
