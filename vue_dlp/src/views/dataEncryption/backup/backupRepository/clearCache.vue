<!--终端清除缓存-->
<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.backupClearCache')"
    :append-to-body="appendToBody"
    :visible.sync="visible"
    width="750px"
    @close="handleCloseDlg"
  >
    <el-divider content-position="left">{{ $t('pages.cacheStatusInfo') }}</el-divider>
    <el-descriptions class="margin-top" :column="2" size="small" border>
      <el-descriptions-item :label="$t('pages.backupClearStatus')">
        {{ formatClearStatus(temp) }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.lastClearTime')">
        {{ temp.clearTime ? temp.clearTime : '-' }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.cleanTotalFileNum')">
        {{ temp.clearTime ? formatCount(temp.fileCount) : '-' }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.cleanTotalFileSize')">
        {{ temp.clearTime ? formatFileSize(temp.clearSize) : '-' }}
      </el-descriptions-item>
    </el-descriptions>
    <el-divider content-position="left">{{ $t('pages.setClearCondition') }}</el-divider>
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="118px">
      <el-radio-group v-model="temp.config.clearConditions" @change="checkChange">
        <el-radio :label="0">{{ $t('pages.backupClearAllCache') }}</el-radio><br/>
        <el-radio :label="1">{{ $t('pages.clearFlags') }}</el-radio><br/>
      </el-radio-group>
      <el-checkbox-group v-if="temp.config.clearConditions === 1" v-model="temp.config.clearFlags" style="font-size: 16px">
        <el-row style="margin-left: 20px">
          <el-col :span="24">
            <el-checkbox :label="1">{{ $t('pages.cacheDate') }}</el-checkbox>
            <el-date-picker v-model="temp.config.clearFlagsConfig.startDate" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsStart" :placeholder="$t('pages.chooseDate')" style="width:140px;"></el-date-picker>
            -
            <el-date-picker v-model="temp.config.clearFlagsConfig.endDate" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsEnd" :placeholder="$t('pages.chooseDate')" style="width:140px;"></el-date-picker>
          </el-col>
          <el-col :span="24">
            <el-checkbox :label="2">{{ $t('table.maxFileSize2') }}</el-checkbox>
            <backup-size-input :value="temp.config.clearFlagsConfig.fileSize"/>
          </el-col>
          <el-col :span="24">
            <el-checkbox :label="4" >{{ $t('pages.version') }}</el-checkbox>
            <el-select v-model="temp.config.clearFlagsConfig.versionConfig" clearable multiple style="margin-left: 28px; width: 350px;" @click.native.prevent>
              <el-option :value="1" :label="$t('pages.versionConfigLast')"/>
              <el-option :value="2" :label="$t('pages.versionConfigDay')"/>
              <el-option :value="4" :label="$t('pages.versionConfigWeek')"/>
              <el-option :value="8" :label="$t('pages.versionConfigMonth')"/>
            </el-select>
          </el-col>
        </el-row>
      </el-checkbox-group>
    </Form>

    <div slot="footer" class="dialog-footer">
      <el-button v-show="!temp.status || temp.status !== 1" type="primary" :loading="submitting" @click="clearStart">
        {{ (!temp.status || temp.status === 0) ? $t('pages.performCleanup') : $t('pages.backupReissue') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { clearCache, getClearCache } from '@/api/dataEncryption/smartBackup/timelyBackup';
import { formatFileSize, formatCount } from '@/utils'
import BackupSizeInput from '../component/BackupSizeInput'
import moment from 'moment';

export default {
  name: 'ClearCache',
  components: { BackupSizeInput },
  props: {
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        termId: undefined,
        status: 0,
        clearTime: undefined,
        config: {}
      },
      defaultConfig: {
        clearConditions: 1,
        clearFlags: [],
        clearFlagsConfig: {
          startDate: moment(new Date()).format('YYYY-MM-DD'),
          endDate: moment(new Date()).format('YYYY-MM-DD'),
          fileSize: {
            minSize: 0,
            minSizeUnit: 1,
            maxSize: 500,
            maxSizeUnit: 1
          },
          versionConfig: []
        }
      },
      rules: {
      },
      clearStatusOptions: [
        { value: 0, label: this.$t('pages.unSendClearReq') },
        { value: 1, label: this.$t('pages.cacheClearing') },
        { value: 2, label: this.$t('pages.cacheClearSuccess') },
        { value: 3, label: this.$t('pages.cacheClearFail') }
      ],
      termId: undefined,
      pickerOptionsStart: {
        disabledDate: time => {
          const endDateVal = this.temp.config.clearFlagsConfig.endDate
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime()
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.temp.config.clearFlagsConfig.startDate
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime() - 1 * 24 * 60 * 60 * 1000)
          }
        }
      }
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    formatCount,
    formatFileSize,
    show(termId) {
      this.visible = true
      this.termId = termId
      this.$nextTick(() => {
        this.resetTemp();
        this.initClearCache();
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, JSON.parse(JSON.stringify(this.defaultTemp)), { config: JSON.parse(JSON.stringify(this.defaultConfig)) })
    },
    initClearCache() {
      getClearCache(this.termId).then(res => {
        if (res.data) {
          this.temp = Object.assign({}, res.data)
        } else {
          this.resetTemp()
        }
      }).catch(e => {
        this.resetTemp()
      })
    },
    handleCloseDlg() {},
    validateForm() {
      // 如果条件是 “条件设置”
      if (this.temp.config.clearConditions === 1) {
        // 判断是否勾选了
        if (!this.temp.config.clearFlags || this.temp.config.clearFlags.length === 0) {
          this.$message({
            message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.clearFlags') }),
            type: 'warning',
            duration: 2000
          })
          return false
        }
        // 校验日期是否填写
        if (this.temp.config.clearFlags.includes(1)) {
          if (!this.temp.config.clearFlagsConfig.startDate || !this.temp.config.clearFlagsConfig.endDate) {
            this.$message({
              message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.cacheDate') }),
              type: 'warning',
              duration: 2000
            })
            return false
          }
        }
        // 校验文件大小是否填写
        if (this.temp.config.clearFlags.includes(2)) {
          if (!this.temp.config.clearFlagsConfig.fileSize.minSize && this.temp.config.clearFlagsConfig.fileSize.minSize !== 0 ||
            !this.temp.config.clearFlagsConfig.fileSize.maxSize && this.temp.config.clearFlagsConfig.fileSize.maxSize !== 0) {
            this.$message({
              message: this.$t('pages.pleaseSelectContent', { content: this.$t('table.maxFileSize2') }),
              type: 'warning',
              duration: 2000
            })
            return false
          }
        }
        // 校验版本是否选择
        if (this.temp.config.clearFlags.includes(4)) {
          if (!this.temp.config.clearFlagsConfig.versionConfig || this.temp.config.clearFlagsConfig.versionConfig.length === 0) {
            this.$message({
              message: this.$t('pages.pleaseSelectContent', { content: this.$t('pages.version') }),
              type: 'warning',
              duration: 2000
            })
            return false
          }
        }
      }
      return true;
    },
    clearStart() {
      this.$refs['dataForm'].validate(valid => {
        if (valid && this.validateForm()) {
          this.$confirmBox(this.$t('pages.confirmClearTermCacheDataTips'), this.$t('text.prompt')).then(() => {
            this.submitting = true
            this.temp.termId = this.termId
            const data = Object.assign({}, this.temp)
            clearCache(data).then(res => {
              this.$message({
                message: this.$t('pages.startExecuteTermClearCache'),
                type: 'success',
                duration: 2000
              })
              this.submitting = false
              this.visible = false
            }).catch(e => {
              this.initClearCache();
              this.submitting = false
            })
          });
        }
      })
      this.submitting = false
    },
    checkChange(selected, options) {
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate();
      })
    },
    formatClearStatus(row, data) {
      if (!row.status) {
        return this.clearStatusOptions[0].label
      }
      return this.clearStatusOptions[row.status].label
    },
    clearCache() {
      const h = this.$createElement
      const msg = h('div', [h('p', this.$t('pages.confirmClearTermCacheListTips')), h('p', { style: 'color: #68a8d0;' }, this.$t('pages.manualClearTermBackupFile'))])
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        clearCache(this.termId).then(() => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.sendClearCacheSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch((err) => {
          console.log(err)
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('pages.sendClearCacheFail'),
            type: 'error',
            duration: 2000
          })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-dialog__body {
  padding: 0 15px 15px 15px;
  max-height: none;
}
</style>
