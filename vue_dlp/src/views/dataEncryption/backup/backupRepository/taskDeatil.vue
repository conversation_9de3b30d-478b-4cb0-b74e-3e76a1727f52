<!--恢复任务详情-->
<template>
  <el-dialog
    id="taskDetail"
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="taskName + ' ' + $t('pages.taskDetail')"
    :visible.sync="visible"
    width="940px"
    @close="handleCloseDlg"
  >
    <div class="show-detail-panel">
      <el-divider content-position="left">{{ $t('pages.taskBaseInfo') }}</el-divider>
      <el-descriptions class="margin-top" :column="4" size="small" border>
        <el-descriptions-item span="1" :label="$t('pages.taskNum')">
          {{ taskId }}
        </el-descriptions-item>
        <el-descriptions-item span="3" :label="$t('pages.taskName')">
          {{ taskName }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.taskStatus')">
          {{ backupStateFormat(rowDetail, rowDetail.taskState) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.dlStartTime')">
          {{ rowDetail.dlStartTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.dlEndTime')">
          {{ rowDetail.dlEndTime }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            {{ $t('pages.restoreProgress') }}
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                {{ $t('pages.restoreProgressTips') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
          <el-popover placement="top-start" :open-delay="200" :title="$t('pages.restoreProgress')" trigger="hover">
            <ul class="detail-list">
              <li v-for="(item, index) in rowDetail.backupProgressDetail" :key="index">{{ item }}</li>
            </ul>
            <span slot="reference" v-html="progressFormat(rowDetail)"></span>
          </el-popover>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.restoreTimeNode')">
          {{ targetRstTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.rstFailedNum')">
          {{ formatCount(rowDetail.failedNum) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.rstSize')">
          {{ formatFileSize(rowDetail.rstSize) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.rstTotalSize')">
          {{ formatFileSize(rowDetail.rstTotalSize) }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider content-position="left">{{ $t('pages.restoreFileList') }}
        <el-button v-show="hasPermission('514')" class="restore-search-icon" :title="$t('pages.restoreFileRecord')" type="primary" icon="el-icon-search" size="mini" @click="handleDetail(rowDetail)" />
      </el-divider>
      <grid-table
        ref="detailList"
        row-key="id"
        :height="350"
        :multi-select="false"
        :show-pager="false"
        :col-model="colModel"
        :row-data-api="rowDataApi"
      />
    </div>
    <backup-recover ref="recover" append-to-body :type="2"/>
  </el-dialog>
</template>

<script>
import { getTaskDetail, getTaskFileList } from '@/api/dataEncryption/smartBackup/backupRepository';
import BackupRecover from '@/views/dataEncryption/backup/backupRepository/recoverDlg';
import { formatFileSize, formatCount } from '@/utils'
import moment from 'moment';

export default {
  name: 'TaskDetail',
  components: { BackupRecover },
  data() {
    return {
      visible: false,
      taskName: undefined,
      taskId: undefined,
      termId: undefined,
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      colModel: [
        { prop: 'rstPath', label: this.$t('pages.backupRestorePath'), width: '120' },
        { prop: 'rstFileMd5', label: 'fileMd5_transliterate', width: '120' }
      ],
      selection: [],
      rowDetail: {},
      defaultRowDetail: {
        taskName: '',
        taskState: undefined,
        rstNum: 0,
        rstTotalNum: 0,
        rstSize: 0,
        rstTotalSize: 0,
        backupProgressDetail: [],
        createTime: undefined
      },
      targetRstTime: undefined
    }
  },
  computed: {
    gridTable() {
      return this.$refs['detailList']
    }
  },
  methods: {
    formatFileSize,
    formatCount,
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option, { termId: this.termId, taskId: this.taskId })
      return getTaskFileList(searchQuery)
    },
    show(row, termId) {
      this.taskName = undefined
      this.rowDetail = Object.assign({}, this.defaultRowDetail, row.taskStrg)
      this.rowDetail.rstTotalSize = row.taskStrg.fileTotalSize
      this.rowDetail.rstTotalNum = row.taskStrg.fileTotal
      this.rowDetail.createTime = row.createTime
      this.rowDetail.taskId = row.id
      this.targetRstTime = row.taskStrg.targetRstTime
      this.taskName = row.name
      this.visible = true
      this.termId = termId;
      this.taskId = row.id
      this.$nextTick(() => {
        this.reloadTableData();
      })
    },
    reloadTableData() {
      if (this.gridTable) {
        this.gridTable.execRowDataApi()
        this.initBaseInfo();
      }
    },
    initBaseInfo() {
      getTaskDetail({ termId: this.termId, taskId: this.taskId }).then(resp => {
        if (resp.data) {
          this.rowDetail = resp.data
        } else {
          this.rowDetail.taskName = this.taskName
          this.rowDetail.taskState = 0
        }
        console.log('resp.data:', this.rowDetail)
      }).catch(() => {})
    },
    refresh() {
      this.initBaseInfo();
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleCloseDlg() {
      this.dlgVisible = false
    },
    backupStateFormat(row, data) {
      if (row.completed === 1) {
        if (data === 4) {
          return this.$t('pages.aborted')
        } else {
          return this.$t('components.completed')
        }
      } else {
        return data === 1 ? this.$t('pages.inRecovery') : data === 0 ? this.$t('pages.notStarted') : this.$t('pages.recovered')
      }
    },
    progressFormat(row, data) {
      if (!row.rstTotalNum && row.rstTotalNum !== 0) {
        return '-'
      }
      const num = (row.rstNum || 0) + (row.failedNum || 0)
      if (num === 0 && row.rstTotalNum === 0) {
        return row.completed === 1 ? '100%(0/0)' : '0%(0/0)'
      }
      // 恢复进度=(已恢复数+恢复失败个数/恢复总数)
      return Number(((num * 100 / row.rstTotalNum).toFixed(2))) + '%' +
        ' (' + formatCount(num) + '/' + formatCount(row.rstTotalNum) + ')';
    },
    statusFormatter(row, deleted) {
      const deletedMap = { 0: this.$t('pages.waitRecover'), 1: this.$t('pages.recovered') }
      return deletedMap[deleted]
    },
    handleDetail(row) {
      this.$router.push({
        path: '/behaviorAuditing/encryptionLog/smartBackupRestoreLog',
        query: {
          entityId: this.termId,
          entityType: '1',
          taskId: row.taskId,
          createDate: moment(row.dlStartTime ? row.dlStartTime : row.createTime).format('YYYY-MM-DD')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-dialog__body {
  padding: 0 15px 15px 15px;
  max-height: none;
}
>>>.el-divider.el-divider--horizontal {
  margin: 15px 0 15px;
}
>>>.el-dialog {
  margin-top: 5vh!important;
}
.restore-search-icon{
  padding: 0 8px 0 8px;
  margin: 0;
}
</style>
