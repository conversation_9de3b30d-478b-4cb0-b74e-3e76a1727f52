<!--回收站-->
<template>
  <file-manager :selection="formatSelection(selection)" @refresh="refresh">
    <strategy-target-tree
      slot="tree"
      ref="strategyTargetTree"
      :showed-tree="['terminal']"
      :os-type-filter="7"
      @data-change="strategyTargetNodeChange"
    />
    <template slot="toolbar">
      <span style="line-height:30px; margin-left:10px;">{{ curFilePath }}</span>
      <el-button icon="el-icon-refresh-left" size="mini" :disabled="!batchOpAble || loading" @click="handleRevert">
        {{ $t('button.restore') }}
      </el-button>
      <el-button v-show="hasPermission('529')" icon="el-icon-delete" size="mini" :disabled="!batchDeleteAble || loading" @click="handleDelete">
        {{ $t('button.delete') }}
      </el-button>
      <div class="searchCon">
        <el-input v-model="query.fileName" v-trim clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('table.fileName1')})" style="width: 200px;" @keyup.enter.native="handleFilter" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </template>
    <grid-table
      ref="tableList"
      v-loading="loading"
      row-key="uuid"
      :col-model="colModel"
      multi-select
      :show-pager="true"
      :row-data-api="rowDataApi"
      @row-dblclick="rowDblclickFunc"
      @selectionChangeEnd="selectionChangeEnd"
    />
  </file-manager>
</template>

<script>
import FileManager from '@/components/FileManager'
import { getFileIcon, getFileTypeBySuffix } from '@/icons/extension'
import {
  getRecyclePage,
  recoverFileFromRecycle,
  deleteFileFromRecycle
} from '@/api/dataEncryption/smartBackup/backupRepository'
import { formatFileSize } from '@/utils'

export default {
  name: 'RecycleBin',
  components: { FileManager },
  data() {
    return {
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      treeData: [],
      selectedData: [],
      treeIconOption: {
        typeKey: 'dataType',
        'G': 'terminalGroup',
        127: 'error'
      },
      // 支持的终端操作系统类型，1-win,2-linux,4-mac,8-移动设备
      osType: 7,
      selection: [],
      tableData: [],
      colModel: [
        { prop: 'pathName', label: 'name', width: '200', sort: true, iconFormatter: this.iconClassFormatter },
        { prop: 'path', label: 'path', width: '300', sort: true },
        { prop: 'fileExt', label: 'fileType', width: '100', sort: true, sortOriginal: true, sortArr: ['ifFile', 'fileExt'], formatter: this.fileTypeFormatter },
        { prop: 'fileSize', label: 'size', width: '100', sort: true, sortOriginal: true, formatter: this.fileSizeFormatter },
        { prop: 'modifyTime', label: 'backupTime', width: '100', sort: true },
        { prop: 'deleted', label: 'status', width: '100', sort: true, formatter: (row) => { return row.deleted === 3 ? this.$t('pages.backupDeleting') : '-' } }
      ],
      curFilePath: undefined,
      loading: false
    }
  },
  computed: {
    batchOpAble() {
      return this.selection.length > 0 && this.selection.every(item => item.deleted === 2)
    },
    batchDeleteAble() {
      return this.selection.length > 0 && this.selection.every(item => item.deleted === 2)
    },
    gridTable() {
      return this.$refs['tableList']
    }
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRecyclePage(searchQuery)
    },
    refresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    async strategyTargetNodeChange(tabName, checkedNode) {
      if (!checkedNode || checkedNode.dataType === 'G') {
        this.curFilePath = checkedNode.label
        this.gridTable.clearRowData()
        this.gridTable.clearPageData()
        this.termId = undefined
        this.query.termId = undefined
        return
      }
      if (this.curFilePath === checkedNode.label) {
        return
      }
      this.curFilePath = checkedNode.label
      this.query.page = 1
      const dataId = Number(checkedNode.dataId);
      this.termId = dataId
      this.query.termId = dataId
      await this.gridTable.execRowDataApi(this.query)
    },
    iconClassFormatter(row) {
      const icons = []
      // 0：硬盘 1：文件夹 2. 文件
      let title
      let iconName
      if (row.uuid === '0') {
        title = this.$t('pages.disk')
        iconName = 'disk1'
      } else if (row.isFile) {
        title = row.fileExt
        iconName = getFileIcon(title)
      } else if (!row.isFile) {
        title = this.$t('pages.folder')
        iconName = 'dir1'
      }
      icons.push({ class: iconName, title: title })
      return icons
    },
    fileTypeFormatter(row, isfFile) {
      if (!isfFile) {
        return this.$t('pages.folder')
      }
      return getFileTypeBySuffix(row.fileExt)
    },
    fileSizeFormatter(row, fileSize) {
      if (!fileSize && fileSize !== 0) {
        return '-'
      }
      if (row.uuid === '0') { // 磁盘
        return '-'
      }
      if (!row.isFile) { // 文件夹
        return '-'
      }
      return formatFileSize(fileSize)
    },
    handleRevert() {
      this.$confirmBox(this.$t('pages.restoreDataConfirm'), this.$t('text.prompt')).then(() => {
        const dataS = this.gridTable.getSelectedDatas()
        const toDeleteUuids = dataS.map(item => item.uuid);
        const toDeleteIds = dataS.map(item => item.id);
        this.loading = true
        recoverFileFromRecycle({ termId: this.termId, uuids: toDeleteUuids }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.restoreSuccess'),
            type: 'success',
            duration: 2000
          })
          this.loading = false
        }).catch(() => {
          this.gridTable.execRowDataApi(this.query)
          this.loading = false
        })
      })
    },
    handleDelete() {
      // 此操作将永久删除该记录，是否继续?
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const dataS = this.gridTable.getSelectedDatas()
        const toDeleteUuids = dataS.map(item => item.uuid);
        const toDeleteIds = dataS.map(item => item.id);
        this.loading = true
        deleteFileFromRecycle({ termId: this.termId, uuids: toDeleteUuids }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.operateSuccessAndWait'),
            type: 'success',
            duration: 2000
          })
          this.loading = false
        }).catch(() => {
          this.gridTable.execRowDataApi(this.query)
          this.loading = false
        })
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd(selection) {
      this.selection = selection
    },
    rowDblclickFunc(row) {
      if (row.isFile) {
        return
      }
      this.$message({
        message: this.$t('pages.recycleBinNodeNotSupportDetail'),
        type: 'warning'
      })
    },
    formatSelection(data) {
      if (data) {
        data.map(item => {
          item.type = item.isFile ? 2 : 1
          if (item.isFile) {
            item.size = item.fileSize
          }
        })
      }
      return data
    }
  }
}
</script>

<style scoped>

</style>
