<!--备份库-->
<template>
  <file-manager :selection="formatSelection(selection)" @refresh="refresh">
    <strategy-target-tree
      slot="tree"
      ref="strategyTargetTree"
      :showed-tree="['terminal']"
      :os-type-filter="7"
      @data-change="strategyTargetNodeChange"
    />
    <template slot="toolbar">
      <div ref="breadcrumbCon" class="breadcrumb-container" :style="`width: calc(100% - ${containerW}px)`">
        <el-dropdown v-show="hiddenFilePath.length > 0" :class="defaultTheme ? 'show-hidden' : 'show-hidden-blue'" placement="bottom" trigger="click" @command="handleHiddenFile">
          <i class="el-icon-d-arrow-left icon"></i>
          <el-dropdown-menu slot="dropdown" class="hidden-item" style="max-height: 300px; overflow-y: auto;">
            <el-dropdown-item v-for="(item, index) in hiddenFilePath" :key="item.id" :title="item.label" :command="index">{{ item.label }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-breadcrumb v-show="curFilePath.length > 0" ref="fileBreadcrumb" separator="/">
          <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
            <a href="javascript:void(0);" :title="item.label" @click="curFilePathClick(index, showFilePath, true)">{{ item.label }}</a>
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </template>
    <div class="toolbar">
      <el-button v-show="hasPermission('527')" size="mini" :disabled="!batchOpAble" @click="handleRecovery">
        <svg-icon icon-class="recover2"/>
        {{ $t('pages.restoreToTerminal') }}
      </el-button>
      <audit-file-downloader v-show="hasPermission('528')" ref="auditFileDownloader" :disabled="!downloadAble" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
      <el-button v-show="hasPermission('529')" icon="el-icon-delete" size="mini" :title="(batchDeleteAble || !batchOpAble) ? $t('text.delete'):$t('pages.backupFileNotAllowDelete')" :disabled="!batchDeleteAble" @click="handleDelete">
        {{ $t('button.delete') }}
      </el-button>
      <el-button size="mini" :disabled="!historyAble" @click="handleHistory">
        <svg-icon icon-class="history3"/>
        {{ $t('pages.historyVersion') }}
      </el-button>
      <el-button v-show="hasPermission('522')" size="mini" :title="$t('pages.priorityClearTips')" :loading="clearLoading" :disabled="batchOpAble || !termId || showTipsTimer" @click="handleClear">
        <svg-icon icon-class="taskPush"/>
        {{ $t('pages.priorityClear') }}
        <span v-show="showTipsTimer">({{ showTipsNumber }})</span>
      </el-button>
      <div class="searchCon">
        <!--
          支持文件名查找、文件大小查找、修改时间查找备份文档
          文件大小不好输入，暂不做
        -->
        <el-tooltip slot="reference" class="item" effect="light" placement="bottom-end">
          <div slot="content">{{ $t('pages.terminal_explorer_search_tips') }}</div>
          <div>
            <el-input v-model="query.fileName" v-trim clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('table.fileName1')})" style="width: 200px;" @keyup.enter.native="handleFilter" />
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </el-tooltip>
      </div>
    </div>
    <grid-table
      ref="tableList"
      row-key="uuid"
      :col-model="colModel"
      multi-select
      :show-pager="true"
      :row-data-api="rowDataApi"
      :selectable="selectable"
      :cell-style="cellStyle"
      @row-dblclick="rowDblclickFunc"
      @selectionChangeEnd="selectionChangeEnd"
    />
    <backup-recover ref="recover" :type="1" @submitEnd="submitEndFun"/>
    <version-history ref="history" @submitEnd="submitEndFun"/>
  </file-manager>
</template>

<script>
import BackupRecover from './recoverDlg'
import VersionHistory from './history'
import FileManager from '@/components/FileManager'
import { getFileTypeBySuffix, getFileIcon } from '@/icons/extension'
import {
  getBackupRepositoryNode,
  deleteFile,
  doBackupClear,
  validateNodeStatus
} from '@/api/dataEncryption/smartBackup/backupRepository'
import { formatFileSize } from '@/utils'
import { debounce } from '@/utils'

export default {
  name: 'FileRepository',
  components: { BackupRecover, VersionHistory, FileManager },
  data() {
    return {
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      treeData: [],
      selectedData: [],
      treeIconOption: {
        typeKey: 'dataType',
        'G': 'terminalGroup',
        127: 'error'
      },
      // 支持的终端操作系统类型，1-win,2-linux,4-mac,8-移动设备
      osType: 7,
      selection: [],
      tableData: [],
      colModel: [
        { prop: 'pathName', label: 'name', width: '200', sort: true, iconFormatter: this.iconClassFormatter },
        { prop: 'isFile', label: 'fileType', width: '80', sort: true, sortOriginal: true, sortArr: ['isFile', 'fileExt'], formatter: this.fileTypeFormatter },
        { prop: 'fileSize', label: 'size', width: '80', sort: true, sortOriginal: true, formatter: this.fileSizeFormatter },
        { prop: 'fileVerNum', label: this.$t('pages.fileVersionNum'), width: '80', sort: true, sortOriginal: true, formatter: this.fileVerNumFormat },
        { prop: 'modifyTime', label: 'fileUpdateDate', width: '100', sort: true, formatter: this.modifyTimeFormat },
        { prop: 'backupTime', label: 'backupTime', width: '100', sort: true, formatter: this.backupTimeFormat }
      ],
      curFilePath: [],
      uuid: undefined,
      termId: undefined,
      tempTask: {},
      defaultTempTask: {
        backType: 32,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      hiddenFilePath: [],
      showFilePath: [],
      showTipsTimer: false,
      showTipsNumber: 10,
      clearLoading: false,
      debouncedHandleResize: null
    }
  },
  computed: {
    batchOpAble() {
      return this.selection.length > 0
    },
    batchDeleteAble() {
      return this.selection.length > 0 && this.selection.every(item => item.deleted === 0)
    },
    historyAble() {
      return this.selection.length === 1 && !!this.selection[0].isFile
    },
    gridTable() {
      return this.$refs['tableList']
    },
    downloadAble() {
      // 只针对文件下载
      return this.selection.length > 0 && this.selection.every(item => item.isFile)
    },
    containerW() {
      const downloadW = 190
      const marginW = 100
      return downloadW + marginW
    },
    defaultTheme() {
      const theme = this.$store.getters.theme || this.$store.getters.sysResources.theme
      return theme === 'default'
    }
  },
  watch: {
    curFilePath(val) {
      this.resetBreadcrumb(val)
    }
  },
  mounted() {
    // 防抖：使用 debounce 包装 handleResize 方法（以免频繁放大缩小导致请求过于频繁）
    this.debouncedHandleResize = debounce(this.handleResize, 100)
    // 若放大或者缩小，重新计算
    window.addEventListener('resize', this.debouncedHandleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.debouncedHandleResize);
  },
  deactivated() {
    window.removeEventListener('resize', this.debouncedHandleResize);
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getBackupRepositoryNode(searchQuery)
    },
    selectable(row, index) {
      return row.uuid !== '0' && row.deleted !== 5
    },
    refresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    async strategyTargetNodeChange(tabName, checkedNode) {
      if (!checkedNode || checkedNode.dataType === 'G') {
        this.curFilePath = [{
          id: undefined,
          label: checkedNode.label
        }]
        this.query.uuid = undefined;
        this.termId = undefined
        this.query.termId = undefined
        await this.gridTable.execRowDataApi(this.query)
        return
      }
      if (this.curFilePath.length > 0 && this.curFilePath[0].label === checkedNode.label && this.termId == checkedNode.dataId) {
        return
      }
      if (this.curFilePath.length === 0 || this.termId !== checkedNode.dataId) {
        this.curFilePath = [{
          id: undefined,
          label: checkedNode.label
        }]
        this.query.uuid = undefined;
      }
      this.query.page = 1
      const dataId = Number(checkedNode.dataId);
      this.termId = dataId
      this.query.termId = dataId
      await this.gridTable.execRowDataApi(this.query)
    },
    iconClassFormatter(row) {
      const icons = []
      // 0：硬盘 1：文件夹 2. 文件
      let title
      let iconName
      if (row.uuid === '0') {
        title = this.$t('pages.disk')
        iconName = 'disk1'
      } else if (row.isFile) {
        title = row.fileExt
        iconName = getFileIcon(title)
      } else if (!row.isFile) {
        title = this.$t('pages.folder')
        iconName = 'dir1'
      }
      icons.push({ class: iconName, title: title })
      if (row.deleted === 4 || row.deleted === 5) {
        icons.push({ class: 'back', title: this.$t('pages.inRecovery'), style: 'font-size: smaller; color: #68a8d0;position: absolute; margin: 5px 0px 0px -21px;' })
      }
      return icons
    },
    fileTypeFormatter(row, isFile) {
      if (row.uuid === '0') {
        return this.$t('pages.localDisk')
      }
      if (!isFile) {
        return this.$t('pages.folder')
      }
      // 如果是文件但没有文件后缀，则返回为"文件"字样
      if (isFile && !row.fileExt) {
        return this.$t('table.file')
      }
      return getFileTypeBySuffix(row.fileExt)
    },
    fileSizeFormatter(row, fileSize) {
      if (!fileSize && fileSize !== 0) {
        return '-'
      }
      if (row.uuid === '0') { // 磁盘
        return '-'
      }
      if (!row.isFile) { // 文件夹
        return '-'
      }
      return formatFileSize(fileSize)
    },
    handleRecovery() {
      const dataS = this.$refs.tableList.getSelectedDatas()
      const uuids = dataS.map(item => {
        return item.uuid
      })
      const obj = {
        termId: this.termId,
        uuids: uuids
      }
      this.$refs.recover.show(obj)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.recycleBinDeleteDataConfirm'), this.$t('text.prompt')).then(() => {
        const dataS = this.gridTable.getSelectedDatas()
        const toDeleteUuids = dataS.map(item => item.uuid);
        const toDeleteIds = dataS.map(item => item.id);
        deleteFile({ termId: this.termId, uuids: toDeleteUuids }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(() => {})
      })
    },
    handleHistory() {
      this.$refs.history.show(this.selection[0], this.termId)
    },
    handleClear() {
      this.$confirmBox(this.$t('pages.backupSendPriorityClearConfirm'), this.$t('text.prompt')).then(() => {
        this.clearLoading = true
        doBackupClear(this.termId).then(respond => {
          this.clearLoading = false
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.backupPriorityClearMsg'),
            type: 'success',
            duration: 2000
          })
          this.initTimerTips();
        }).catch(() => {
          this.clearLoading = false
          this.initTimerTips();
        })
      })
    },
    initTimerTips() {
      this.showTipsTimer = true
      this.showTipsNumber = 10
      const timer = setInterval(() => {
        this.showTipsNumber--
        if (this.showTipsNumber <= 0) {
          clearInterval(timer)
          this.showTipsTimer = false
        }
      }, 1000);
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd(selection) {
      this.selection = selection
    },
    curFilePathClick(index, filePath, breadcrumb) {
      const uuid = filePath[index].id;
      // 查询curFilePath中这个uuid所在的位置
      const indexInCurFilePath = this.curFilePath.findIndex(item => item.id === uuid)
      // 删除该位置之后的数据
      this.curFilePath.splice(indexInCurFilePath + 1)
      this.query.page = 1
      this.query.uuid = uuid;
      this.gridTable.execRowDataApi(this.query)
    },
    rowDblclickFunc(row) {
      if (row.isFile) {
        return
      }
      validateNodeStatus({ termId: this.termId, uuid: row.uuid }).then(respond => {
        this.curFilePath.push({
          id: row.uuid,
          label: row.pathName
        })
        this.query.page = 1
        this.query.uuid = row.uuid;
        this.gridTable.execRowDataApi(this.query)
        // TODO 更改左边的树
      }).catch(() => {})
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.serverId
      this.tempTask.fileGuid = row.maxFileMd5 ? row.maxFileMd5 : row.fileMd5
      this.tempTask.fileName = row.pathName
      return this.tempTask
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (row.isFile) {
        return ''
      }
      let cellStyle = '';
      this.colModel.forEach(item => {
        cellStyle = 'cursor: pointer;'
      })
      return cellStyle;
    },
    resetBreadcrumb(path) {
      this.showFilePath.splice(0, this.showFilePath.length, ...path)
      this.$nextTick(() => {
        const containerW = this.$refs.breadcrumbCon.offsetWidth
        const itemsW = this.$refs.fileBreadcrumb.$children.map(item => item.$el.offsetWidth)
        let totalW = itemsW.reduce((total, cur, i) => total + cur + 10, 0)
        let index
        for (let i = 1; totalW > containerW; i++) {
          totalW -= itemsW.shift()
          index = i
        }
        this.hiddenFilePath.splice(0, this.hiddenFilePath.length, ...this.showFilePath.splice(0, index).reverse())
      })
    },
    handleHiddenFile(index) {
      this.curFilePathClick(index, this.hiddenFilePath)
    },
    formatSelection(data) {
      if (data) {
        data.map(item => {
          item.type = item.isFile ? 2 : 1
          if (item.isFile) {
            item.size = item.fileSize
          }
        })
      }
      return data
    },
    fileVerNumFormat(row, data) {
      return data || '-'
    },
    modifyTimeFormat(row) {
      if (row.isFile) {
        return row.modifyTime
      } else {
        return '-';
      }
    },
    backupTimeFormat(row) {
      if (row.isFile) {
        return row.backupTime
      } else {
        return row.modifyTime;
      }
    },
    handleResize() {
      this.resetBreadcrumb(this.curFilePath);
    },
    removeResize() {
      window.removeEventListener('resize', this.debouncedHandleResize);
    },
    submitEndFun() {
      this.gridTable.execRowDataApi()
    }
  }
}
</script>

<style lang="scss" scoped>
  .breadcrumb-container {
    height: 30px;
    line-height: 28px;
    padding-left: 5px;
    margin-left: 100px;
    position: relative;
  }
  .el-breadcrumb {
    width: 90%;
    height: 29px;
    line-height: 28px;
    margin-left: 20px;
    position: absolute;
  }
  >>>.el-breadcrumb__inner a {
    max-width: 200px;
    display: inline-block;
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  >>>.el-breadcrumb__separator {
    vertical-align: top;
  }
  .show-hidden {
    height: 100%;
    float: left;
    .icon {
      height: 100%;
      vertical-align: middle;
      cursor: pointer;
      color: white;
      &:before{
        margin-top: 8px;
        display: inline-block;
      }
    }
  }
  .show-hidden-blue {
    height: 100%;
    float: left;
    .icon {
      height: 100%;
      vertical-align: middle;
      cursor: pointer;
      &:before{
        margin-top: 8px;
        display: inline-block;
      }
    }
  }
  .hidden-item {
    .el-dropdown-menu__item{
      max-width: 200px;
      word-break: keep-all;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
</style>
