<!--备份文件历史版本-->
<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="filename + ' ' + $t('pages.historyVersion')"
    :visible.sync="visible"
    width="600px"
    @close="handleCloseDlg"
  >
    <el-button type="primary" size="mini" style="margin-top: -10px" :title="$t('button.refresh')" @click="refresh">
      <svg-icon icon-class="refresh" />
    </el-button>
    <audit-file-downloader
      ref="auditFileDownloader"
      :button="$t('table.download')"
      :show="false"
      :selection="selection"
      :before-download="beforeDownload"
    />
    <grid-table
      ref="verList"
      v-loading="loading"
      row-key="uuid"
      :height="300"
      :multi-select="false"
      :show-pager="false"
      :col-model="colModel"
      :row-data-api="rowDataApi"
    />
    <backup-recover ref="recover" append-to-body :type="2" @submitEnd="submitEndFun"/>
  </el-dialog>
</template>

<script>
import { getFileHistory, deleteFileFromHistory } from '@/api/dataEncryption/smartBackup/backupRepository';
import BackupRecover from '@/views/dataEncryption/backup/backupRepository/recoverDlg';
import { formatFileSize } from '@/utils'

export default {
  name: 'VersionHistory',
  components: { BackupRecover },
  data() {
    return {
      visible: false,
      filename: undefined,
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      colModel: [
        { prop: 'modifyTime', label: 'fileUpdateDate', width: '150' },
        { prop: 'backupTime', label: 'backupTime', width: '150' },
        { prop: 'fileSize', label: 'maxFileSize2', width: '120', formatter: (row) => formatFileSize(row.fileSize) },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('527,528,529'),
          buttons: [
            { label: 'download', click: this.handleDownload, disabledFormatter: (row) => row.deleted === 3, isShow: () => this.hasPermission('528') },
            { label: 'recover', click: this.handledRecover, disabledFormatter: (row) => row.deleted === 3, isShow: () => this.hasPermission('527') }/*,
            { label: 'delete', click: this.handledDelete, disabledFormatter: (row) => row.deleted === 3 || !this.deleteAble, formatter: (row) => row.deleted === 3 ? this.$t('pages.backupDeleting') : this.$t('pages.delete'), isShow: () => this.hasPermission('529') }*/
          ]
        }
      ],
      selection: [],
      tempTask: {},
      defaultTempTask: {
        backType: 32,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      termId: undefined,
      uuid: undefined,
      loading: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['verList']
    },
    deleteAble() {
      return this.gridTable && this.gridTable.getDatas().filter(item => item.deleted === 0).length > 1
    }
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option, { uuid: this.uuid, termId: this.termId })
      return getFileHistory(searchQuery)
    },
    show(row, termId) {
      this.filename = row.pathName
      this.visible = true
      this.termId = termId;
      this.uuid = row.uuid
      this.$nextTick(() => {
        this.reloadTableData();
      })
    },
    reloadTableData() {
      if (this.gridTable) {
        this.gridTable.execRowDataApi()
      }
    },
    refresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleCloseDlg() {},
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.serverId
      this.tempTask.fileName = this.filename
      this.tempTask.fileGuid = row.fileMd5
      return this.tempTask
    },
    handleDownload(row) {
      this.selection = [row]
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    handledRecover(row) {
      const obj = {
        termId: this.termId,
        uuids: [row.uuid]
      }
      this.$refs.recover.show(obj)
    },
    handledDelete(row) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteUuids = [row.uuid];
        const toDeleteIds = [row.id];
        this.loading = true
        deleteFileFromHistory({ termId: this.termId, uuids: toDeleteUuids }).then(respond => {
          this.loading = false
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.operateSuccessAndWait'),
            type: 'success',
            duration: 2000
          })
        }).catch(() => {
          this.loading = false
          this.gridTable.execRowDataApi()
        })
      })
    },
    submitEndFun() {
      this.$emit('submitEnd')
    }
  }
}
</script>

<style scoped>

</style>
