<!--备份文件恢复-->
<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.backupRestore')"
    :append-to-body="appendToBody"
    :visible.sync="visible"
    width="500px"
    @close="handleCloseDlg"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="128px">
      <FormItem :label="$t('pages.restoreToDestTerminal')" prop="targetTerminalType">
        <el-radio v-model="temp.targetTerminalType" :label="1" @change="checkChange">{{ $t('pages.sourceBackupTerminal') }}</el-radio>
      </FormItem>
      <FormItem prop="customTerm">
        <el-radio v-model="temp.targetTerminalType" :label="2">{{ $t('pages.otherTerminal') }}</el-radio>
        <tree-select
          v-if="temp.targetTerminalType === 2"
          ref="objectTree"
          node-key="id"
          :height="350"
          :width="410"
          check-strictly
          :checked-keys=" [temp.customTerm ? { key: 'T' + temp.customTerm, label: temp.customTermName } : {}]"
          is-filter
          :local-search="false"
          :leaf-key="'terminal'"
          :rewrite-node-click-fuc="true"
          :node-click-fuc="termTreeNodeCheckChange"
        />
      </FormItem>
      <FormItem :label="$t('pages.restoreToPath')" prop="targetDirType">
        <el-radio v-model="temp.targetDirType" :label="1" @change="checkChange">{{ $t('pages.sourceBackupPath') }}</el-radio>
      </FormItem>
      <FormItem prop="customDir">
        <el-radio v-model="temp.targetDirType" :label="2">{{ $t('pages.otherPath') }}</el-radio>
        <el-input v-if="temp.targetDirType === 2" v-model="temp.customDir" v-trim :placeholder="`C:\\test`" maxlength="260" style="width: calc(100% - 114px);"/>
      </FormItem>
      <FormItem :label="$t('pages.conflictHandleType')" prop="conflict">
        <el-radio-group v-model="temp.conflict" style="margin-left: -9px">
          <el-radio :label="0">{{ $t('pages.cover') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.rename') }}</el-radio>
          <el-radio :label="2">{{ $t('pages.ignore') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem v-show="type === 1" label-width="0" prop="timeSpecified">
        <el-checkbox v-model="temp.timeSpecified" :true-label="1" :false-label="0">{{ $t('pages.restoreToTime') }}</el-checkbox>
        <span>
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.restoreToTimeTips') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </span>
      </FormItem>
      <FormItem v-if="temp.timeSpecified" :label="$t('pages.restoreVersionTimePoint')" prop="targetRstTime">
        <el-date-picker
          v-model="temp.targetRstTime"
          style="width: 100%;"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :placeholder="$t('pages.selectDateTime')"
          align="right"
          :picker-options="pickerOptions"
        />
      </FormItem>
    </Form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="recovery">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { recoverFile, recoverFileHistory } from '@/api/dataEncryption/smartBackup/backupRepository';

export default {
  name: 'BackupRecover',
  props: {
    // 类型： 1 文件恢复 2 历史版本恢复
    type: {
      type: Number,
      default: 0
    },
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        termId: undefined,
        uuids: [],
        targetTerminalType: 1,
        targetDirType: 1,
        customTerm: undefined,
        customTermName: undefined,
        customDir: undefined,
        timeSpecified: 0,
        targetRstTime: undefined,
        conflict: undefined
      },
      rules: {
        customTerm: [{ required: true, validator: this.customTermValid, trigger: 'blur,change' }],
        customDir: [{ required: true, validator: this.customDirValid, trigger: 'blur,change' }],
        conflict: [{ required: true, message: this.$t('text.cantNull'), trigger: 'blur' }],
        targetRstTime: [{ required: true, validator: this.targetRstTimeValid, trigger: 'blur,change' }]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [{
          text: this.$t('text.today'),
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: this.$t('text.yesterday'),
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: this.$t('text.aWeekAgo'),
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }]
      }
    }
  },
  methods: {
    show(data) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate();
      })
      this.temp = Object.assign({}, this.defaultTemp, data)
    },
    handleCloseDlg() {},
    recovery() {
      this.submitting = true
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          (1 === this.type ? recoverFile : recoverFileHistory)(this.temp).then(res => {
            this.$message({
              message: this.$t('pages.restoreTaskCreateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.submitting = false
            this.$emit('submitEnd', res)
            this.visible = false
          }).catch(e => {
            this.$message({
              message: this.$t('text.fail'),
              type: 'error',
              duration: 2000
            })
            this.$emit('submitEnd', e)
            this.submitting = false
          })
        }
      })
      this.submitting = false
    },
    checkChange(selected, options) {
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate();
      })
    },
    termTreeNodeCheckChange(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.temp.customTerm = data.dataId
        this.temp.customTermName = data.name
      } else {
        return false
      }
    },
    customTermValid(rule, value, callback) {
      if (this.temp.targetTerminalType === 2 && !this.temp.customTerm) {
        callback(new Error(this.$t('pages.restorePleaseSelect', { info: this.$t('table.terminal') })))
      } else {
        callback()
      }
    },
    customDirValid(rule, value, callback) {
      if (this.temp.targetDirType === 2 && !this.temp.customDir) {
        callback(new Error(this.$t('pages.restorePleaseSelect', { info: this.$t('table.path') })))
      } else if (this.temp.targetDirType === 2 && this.temp.customDir) {
        // 正则表达式，检测是否是带盘符的windows目录
        const reg = /^([a-zA-Z]:\\(([^/:*?"<>|\\]+\\)*[^/:*?"<>|\\]*))$/;
        if (!reg.test(this.temp.customDir)) {
          callback(new Error(this.$t('pages.restorePathErrorMsg')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    targetRstTimeValid(rule, value, callback) {
      if (this.temp.timeSpecified && !this.temp.targetRstTime) {
        callback(new Error(this.$t('pages.pleaseSelectContent', { content: this.$t('pages.restoreVersionTimePoint') })))
      } else {
        callback()
      }
    }
  }
}
</script>

<style scoped>

</style>
