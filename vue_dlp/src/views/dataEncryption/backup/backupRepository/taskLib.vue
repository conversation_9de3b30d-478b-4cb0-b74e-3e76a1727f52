<!--任务库-->
<template>
  <file-manager @refresh="refresh">
    <strategy-target-tree
      slot="tree"
      ref="strategyTargetTree"
      :showed-tree="['terminal']"
      :os-type-filter="7"
      @data-change="strategyTargetNodeChange"
    />
    <template slot="toolbar">
      <span style="line-height:30px; margin-left:10px;">{{ curFilePath }}</span>
      <el-button icon="el-icon-switch-button" size="mini" :disabled="!pauseBtnAble" @click="handleBtn(1)">
        {{ $t('pages.suspend') }}
      </el-button>
      <el-button icon="el-icon-switch-button" size="mini" :disabled="!continueBtnAble" @click="handleBtn(2)">
        {{ $t('pages.continue') }}
      </el-button>
      <el-button icon="el-icon-delete" size="mini" :disabled="!deleteBtnAble" @click="handleBtn(3)">
        {{ $t('pages.delete') }}
      </el-button>
      <div class="searchCon">
        <el-select v-model="query.searchType" clearable style="width: 160px;">
          <el-option :value="null" :label="$t('pages.all')"/>
          <el-option :value="0" :label="$t('pages.notStarted')"/>
          <el-option :value="1" :label="$t('pages.inRecovery')"/>
          <el-option :value="3" :label="$t('pages.paused')"/>
          <el-option :value="2" :label="$t('components.completed')"/>
        </el-select>
        <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('text.pleaseEnterInfo', { info: $t('table.taskName')})" style="width: 200px;" @keyup.enter.native="handleFilter" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </template>
    <grid-table
      ref="tableList"
      v-loading="loading"
      row-key="id"
      :col-model="colModel"
      multi-select
      :show-pager="true"
      :row-data-api="rowDataApi"
      @row-dblclick="rowDblclickFunc"
      @selectionChangeEnd="selectionChangeEnd"
    />
    <task-deatil ref="taskDetail"/>
  </file-manager>
</template>

<script>
import FileManager from '@/components/FileManager'
import TaskDeatil from './taskDeatil';
import {
  getTaskLibPage,
  deleteTask,
  updateTaskStatus,
  pauseTaskByIds,
  continueTaskByIds,
  deleteTaskByIds
} from '@/api/dataEncryption/smartBackup/backupRepository'

export default {
  name: 'TaskLib',
  components: { FileManager, TaskDeatil },
  data() {
    return {
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        searchType: null
      },
      treeData: [],
      selectedData: [],
      treeIconOption: {
        typeKey: 'dataType',
        'G': 'terminalGroup',
        127: 'error'
      },
      // 支持的终端操作系统类型，1-win,2-linux,4-mac,8-移动设备
      osType: 7,
      selection: [],
      tableData: [],
      colModel: [
        // { prop: 'id', label: 'taskNum', width: '60' },
        { prop: 'name', label: 'taskName2', width: '100', sort: true },
        { prop: 'createTime', label: 'createTime', width: '50', sort: true },
        { prop: 'status', label: 'status', width: '50', formatter: this.statusFormatter },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right', hidden: !this.hasPermission('527'),
          buttons: [
            { label: 'details', click: this.handleDetail },
            { label: 'pause', click: this.handlePause, isShow: (row) => row.status === 0 || row.status === 1, formatter: (row) => (!row.taskStrg.runType || row.taskStrg.runType === 0) ? this.$t('table.pause') : this.$t('pages.continue') },
            { label: 'delete', click: this.handleDelete, isShow: (row) => row.status === 0 || row.status === 1 || row.status === 2 }
          ]
        }
      ],
      curFilePath: undefined,
      pauseBtnAble: false,
      continueBtnAble: false,
      deleteBtnAble: false,
      operateOption: {
        1: this.$t('pages.suspend'),
        2: this.$t('pages.continue'),
        3: this.$t('pages.delete')
      },
      loading: false
    }
  },
  computed: {
    batchOpAble() {
      return this.selection.length > 0
    },
    gridTable() {
      return this.$refs['tableList']
    }
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTaskLibPage(searchQuery)
    },
    refresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    async strategyTargetNodeChange(tabName, checkedNode) {
      if (!checkedNode || checkedNode.dataType === 'G') {
        this.curFilePath = checkedNode.label
        this.gridTable.clearRowData()
        this.gridTable.clearPageData()
        this.query.termId = undefined
        return
      }
      if (this.curFilePath === checkedNode.label) {
        return
      }
      this.curFilePath = checkedNode.label
      this.query.page = 1
      const dataId = Number(checkedNode.dataId);
      this.termId = dataId
      this.query.termId = dataId
      await this.gridTable.execRowDataApi(this.query)
    },
    statusFormatter(row, status) {
      if (row.completed === 1) {
        if (status === 4) {
          return this.$t('pages.aborted')
        } else {
          return this.$t('components.completed')
        }
      } else {
        if (row.taskStrg && row.taskStrg.runType === 2 && status !== 2) {
          return this.$t('pages.paused');
        }
        return status === 1 ? this.$t('pages.inRecovery') : status === 0 ? this.$t('pages.notStarted') : this.$t('components.completed')
      }
    },
    handleDetail(row) {
      this.$refs.taskDetail.show(row, this.termId)
    },
    handlePause(row) {
      const h = this.$createElement
      const runType = row.taskStrg.runType || 0
      const msg = runType === 0 ? h('div', [h('p', this.$t('pages.restoreTaskPauseConfirm')), h('p', { style: 'color: #68a8d0;' }, this.$t('pages.restoreTaskPauseConfirmTips'))]) : h('div', [h('p', this.$t('pages.restoreTaskContinueConfirm'))])
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        updateTaskStatus({ taskId: row.id, runType: runType === 0 ? 2 : 0 }).then(respond => {
          this.gridTable.deleteRowData(row.id)
          this.$notify({
            title: this.$t('text.success'),
            message: runType === 0 ? this.$t('pages.pauseSuccess') : this.$t('pages.restoreTaskSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(() => {})
      })
    },
    handleDelete(row) {
      const h = this.$createElement
      const msg = h('div', [h('p', this.$t('pages.restoreTaskDeleteConfirm')), h('p', { style: 'color: red;' }, this.$t('pages.restoreTaskDeleteConfirmTips'))])
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        deleteTask({ taskId: row.id, taskName: row.name }).then(respond => {
          this.gridTable.deleteRowData(row.id)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(() => {})
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    selectionChangeEnd(selection) {
      this.selection = selection
      this.pauseBtnAble = selection.length > 0 && this.selection.some(item => item.taskStrg && item.taskStrg.runType === 0 && item.status !== 2)
      this.continueBtnAble = selection.length > 0 && this.selection.some(item => item.taskStrg && item.taskStrg.runType === 2 && item.status !== 2)
      this.deleteBtnAble = selection.length > 0 && this.selection.some(item => item.status === 0 || item.status === 1 || item.status === 2)
    },
    rowDblclickFunc(row) {
    },
    handleBtn(type) {
      const statusName = this.operateOption[type]
      this.$confirmBox(this.$t('pages.diskScan_Msg5', { statusName: statusName }), this.$t('text.prompt')).then(() => {
        let func;
        switch (type) {
          // 暂停
          case 1:
            func = pauseTaskByIds
            break
          // 继续
          case 2:
            func = continueTaskByIds
            break
          // 删除
          case 3:
            func = deleteTaskByIds
            break
        }
        this.loading = true
        const targetIds = this.selection.map(item => item.id)
        func({ ids: targetIds.join(',') }).then(respond => {
          this.gridTable.execRowDataApi()
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.operateSuccess'), type: 'success', duration: 2000 })
          this.loading = false
        })
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>

</style>
