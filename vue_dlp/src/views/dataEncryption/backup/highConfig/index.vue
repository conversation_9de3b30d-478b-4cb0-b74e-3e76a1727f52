<!-- 高级配置 -->
<template>
  <div class="app-container">
    <Form ref="dataForm" :rules="rules" :model="temp" class="server-form" label-position="right" label-width="250px">
      <el-divider class="server-label" content-position="left">{{ $t('pages.termFileConfig') }}:</el-divider>
      <el-row>
        <el-col :span="8">
          <FormItem :label="$t('pages.backupDuration')" prop="duration">
            <el-input-number v-model="temp.term.duration" v-trim style="width: 80px" :controls="false" :min="3" :max="999999" :step="1" step-strictly :disabled="!formable" /> min
          </FormItem>
        </el-col>
        <el-col :span="16">
          <FormItem :label="$t('button.highConfig')" style="position:absolute;" prop="highConf">
            <el-switch v-model="temp.term.retainVersionAble" style="margin-left: 10px" :active-value="1" :inactive-value="0" @change="cleanRetainVersionAbleChange"/>
            <el-checkbox-group v-show="temp.term.retainVersionAble" v-model="temp.term.retainVersion">
              <el-row>
                <el-col :span="24">
                  <el-checkbox :label="1">
                    <!-- 保留过去{num}天内的所有版本 -->
                    <i18n path="pages.allVersionDays">
                      <el-input-number slot="num" v-model="temp.term.retainVersionConfig.allVersionDays" v-trim style="width: 80px" :controls="false" :min="0" :max="999999" :step="1" step-strictly :disabled="!formable" />
                    </i18n>
                  </el-checkbox>
                </el-col>
                <el-col :span="24">
                  <el-checkbox :label="2">
                    <!-- 每天只保留一份最新版本，保留{num}天 -->
                    <i18n path="pages.onlyVersionDay">
                      <el-input-number slot="num" v-model="temp.term.retainVersionConfig.onlyVersionDay" v-trim style="width: 80px" :controls="false" :min="0" :max="999999" :step="1" step-strictly :disabled="!formable" />
                    </i18n>
                  </el-checkbox>
                </el-col>
                <el-col :span="24">
                  <el-checkbox :label="4">
                    <!-- 每周只保留一份最新版本，保留{num}周 -->
                    <i18n path="pages.onlyVersionWeek">
                      <el-input-number slot="num" v-model="temp.term.retainVersionConfig.onlyVersionWeek" v-trim style="width: 80px" :controls="false" :min="0" :max="999999" :step="1" step-strictly :disabled="!formable" />
                    </i18n>
                  </el-checkbox>
                </el-col>
                <el-col :span="24">
                  <el-checkbox :label="8">
                    <!-- 每月只保留一份最新版本，保留{num}月 -->
                    <i18n path="pages.onlyVersionMonth">
                      <el-input-number slot="num" v-model="temp.term.retainVersionConfig.onlyVersionMonth" v-trim style="width: 80px" :controls="false" :min="0" :max="999999" :step="1" step-strictly :disabled="!formable" />
                    </i18n>
                  </el-checkbox>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </FormItem>
        </el-col>
      </el-row>
      <el-row style="width: 40%">
        <el-col :span="24">
          <FormItem :label="$t('pages.backupVersions')" prop="versions">
            <el-input-number v-model="temp.term.versions" v-trim style="width: 80px" :controls="false" :min="1" :max="999999" :step="1" step-strictly :disabled="!formable" />
          </FormItem>
          <FormItem :label="$t('pages.backuCacheDiskSize1')" prop="cacheModel">
            <div>
              <el-input-number v-model="temp.term.cacheDiskSize" v-trim style="width: 80px" :controls="false" :min="1" :max="999999" :step="1" step-strictly :disabled="!formable" />{{ $t('pages.backuCacheDiskSize2') }}
              <el-radio-group v-model="temp.term.cacheModel" :disabled="!formable" >
                <el-radio :label="2" style="margin-left: -100px" >
                  <!-- 自动缓存到当前最大空间的磁盘（如还不足则暂停缓存拷贝）-->
                  {{ $t('pages.backuCacheModel1') }}
                </el-radio><br/>
                <el-radio :label="3" style="margin-left: -100px" >
                  <!-- 暂停本地缓存拷贝 -->
                  {{ $t('pages.backuCacheModel2') }}
                </el-radio>
              </el-radio-group>
            </div>
          </FormItem>
          <FormItem :label="$t('pages.cacheSwitch')" prop="fullScanCache">
            <el-checkbox v-model="temp.term.fullScanCache" :true-label="1" :false-label="0">{{ $t('pages.openFullScanBackupCache') }}</el-checkbox>
          </FormItem>
        </el-col>
      </el-row>
      <!-- 服务器全局配置 -->
      <el-divider class="server-label" content-position="left">{{ $t('pages.backupServerGlobalConfig') }}:</el-divider>
      <FormItem :label="$t('pages.regularlyClearBackupFile')">
        <el-switch v-model="temp.server.cleanAble" style="margin-left: 10px" :active-value="1" :inactive-value="0" @change="cleanAbleChange"/>
        <el-button type="primary" size="small" :title="$t('pages.immediatelyClearTips')" style="left: 0" :loading="clearLoading" :disabled="showTipsTimer" @click="doBackupClear">
          {{ $t('pages.immediatelyClear') }}
          <span v-show="showTipsTimer">({{ showTipsNumber }})</span>
        </el-button>
        <span style="color: #71afef;font-size: 14px; margin-left: 8px">
          <i18n path="pages.backupClearStgTips">
            <span v-if="hasPermission('E83')" slot="stg" style="color: #68a8d0;font-size: 17px; text-decoration: underline; cursor: pointer">
              <router-link to="/dataEncryption/smartBackup/serverBackupConfig" tag="span">
                {{ $t('route.serverBackupConfig') }}
              </router-link>
            </span>
            <span v-else slot="stg" style="color: #68a8d0;">
              {{ $t('route.serverBackupConfig') }}
            </span>
          </i18n>
        </span>
      </FormItem>
      <FormItem v-show="temp.server.cleanAble" :label="$t('pages.cleanCycle')" prop="cleanCycle">
        <el-cascader
          v-model="temp.server.cleanCycle"
          class="scan-cycle"
          :disabled="!temp.server.cleanAble"
          :options="cleanCycleOpts"
          :props="{ expandTrigger: 'hover' }"
          @change="()=>{}"
        />
      </FormItem>
      <FormItem v-show="temp.server.cleanAble" :label="$t('pages.cleanPeriod')" prop="cleanTime">
        <el-time-picker
          v-model="temp.server.cleanTime"
          :disabled="!temp.server.cleanAble"
          style="width: 260px"
          is-range
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          :range-separator="$t('pages.till')"
        />
      </FormItem>
      <FormItem :label="$t('pages.backupSwitch')">
        <el-switch v-model="temp.server.serverBakAble" style="margin-left: 10px" :active-value="1" :inactive-value="0" @change="serverBakAbleChange"/>
      </FormItem>
      <FormItem v-show="temp.server.serverBakAble" :label="$t('pages.backupPeriod')" prop="backupTime">
        <el-time-picker
          v-model="temp.server.backupTime"
          :disabled="!temp.server.serverBakAble"
          style="width: 260px"
          is-range
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          :range-separator="$t('pages.till')"
        />
      </FormItem>
      <div class="server-footer">
        <el-button type="primary" size="mini" :loading="submitting" @click="updateConfig">
          {{ $t('button.save') }}
        </el-button>
      </div>
    </Form>
  </div>
</template>

<script>

import { updateHighConfig, getHighConfig } from '@/api/system/deviceManage/smartBackupServer';
import { doBackupClear } from '@/api/dataEncryption/smartBackup/backupRepository';

export default {
  name: 'BackupHighConfig',
  components: { },
  props: {
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      temp: {},
      defaultTemp: {
        term: {
          duration: 20,
          versions: 3,
          retainVersionAble: 0,
          retainVersion: [],
          retainVersionConfig: {
            allVersionDays: 30,
            onlyVersionDay: 30,
            onlyVersionWeek: 4,
            onlyVersionMonth: 12
          },
          cacheDiskSize: 10,
          cacheModel: 2,
          cacheRetainVersion: 3,
          fullScanCache: 0
        },
        server: {
          cleanAble: 0,
          serverBakAble: 1,
          cleanCycle: [],
          backupTime: ['00:00', '23:59'],
          cleanTime: ['00:00', '05:59']
        }
      },
      rules: {
        cleanCycle: [{ required: true, validator: this.cleanCycleValid, trigger: 'blur,change' }],
        cleanTime: [{ required: true, validator: this.cleanTimeValid, trigger: 'blur,change' }],
        backupTime: [{ required: true, validator: this.backupTimeValid, trigger: 'blur,change' }],
        highConf: [{ required: false, validator: this.highConfValid, trigger: 'blur,change' }],
        duration: [{ required: true, validator: this.durationValid, trigger: 'blur' }],
        versions: [{ required: true, validator: this.versionsValid, trigger: 'blur' }],
        cacheModel: [{ required: true, validator: this.cacheModelValid, trigger: 'blur' }]
      },
      submitting: false,
      showTipsTimer: false,
      showTipsNumber: 10,
      clearLoading: false,
      timeOptions: {
        start: '00:00',
        step: '00:01',
        end: '23:59'
      },
      cleanCycleOpts: []
    }
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
    this.cleanCycleOpts = this.buildCleanCycleOptions()
    this.$nextTick(() => {
      this.getHighConfig();
    })
  },
  methods: {
    updateConfig() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.submitting = true
          const data = this.formatReqData();
          updateHighConfig(data).then(res => {
            this.$message({
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            this.submitting = false
          }).catch(e => {
            this.$message({
              message: this.$t('text.fail'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
          })
        }
      })
    },
    formatReqData() {
      const data = JSON.parse(JSON.stringify(Object.assign({}, this.defaultTemp, this.temp)));
      data.server.cleanCycle = data.server.cleanCycle ? data.server.cleanCycle.join('|') : ''
      data.server.backupTime = data.server.backupTime[0] + '-' + data.server.backupTime[1]
      data.server.cleanTime = data.server.cleanTime[0] + '-' + data.server.cleanTime[1]
      data.term.retainVersion = data.term.retainVersionAble === 1 && data.term.retainVersion.length > 0 ? data.term.retainVersion.reduce((pre, cur) => pre + cur) : undefined
      data.term.cacheRetainVersion = data.term.cacheModel === 1 ? data.term.cacheRetainVersion : 0;
      // 这里处理高级配置中的值
      if (data.term.retainVersionConfig) {
        if (data.term.retainVersionAble === 1) {
          if ((data.term.retainVersion & 1) === 0) {
            data.term.retainVersionConfig.allVersionDays = 0;
          }
          if ((data.term.retainVersion & 2) === 0) {
            data.term.retainVersionConfig.onlyVersionDay = 0;
          }
          if ((data.term.retainVersion & 4) === 0) {
            data.term.retainVersionConfig.onlyVersionWeek = 0;
          }
          if ((data.term.retainVersion & 8) === 0) {
            data.term.retainVersionConfig.onlyVersionMonth = 0;
          }
        } else {
          data.term.retainVersionConfig.allVersionDays = 0
          data.term.retainVersionConfig.onlyVersionDay = 0
          data.term.retainVersionConfig.onlyVersionWeek = 0
          data.term.retainVersionConfig.onlyVersionMonth = 0
        }
      }
      return data;
    },
    formatResData(data) {
      if (data.server.cleanCycle) {
        const array = data.server.cleanCycle.split('|')
        if (array.length === 1) {
          data.server.cleanCycle = [Number(array[0])]
        } else {
          data.server.cleanCycle = [Number(array[0]), Number(array[1])]
        }
      }
      if (data.server.cleanTime) {
        const array = data.server.cleanTime.split('-')
        data.server.cleanTime = [array[0], array[1]]
      } else {
        data.server.cleanTime = ['00:00', '05:59']
      }
      if (data.server.backupTime) {
        const array = data.server.backupTime.split('-')
        data.server.backupTime = [array[0], array[1]]
      } else {
        data.server.backupTime = ['00:00', '23:59']
      }
      if (data.term.retainVersion && data.term.retainVersion > 0) {
        data.term.retainVersionAble = 1;
        const retainVersionArray = [];
        for (let i = 0; i < 4; i++) {
          if (data.term.retainVersion & (1 << i)) {
            retainVersionArray.push(1 << i);
          }
        }
        data.term.retainVersion = retainVersionArray;
      } else {
        data.term.retainVersionAble = 0;
        data.term.retainVersion = [];
        data.term.retainVersionConfig = {
          allVersionDays: 0,
          onlyVersionDay: 0,
          onlyVersionWeek: 0,
          onlyVersionMonth: 0
        }
      }
      return data
    },
    getHighConfig() {
      getHighConfig().then(res => {
        let data = res.data;
        if (data) {
          // 处理data以满足form
          data = this.formatResData(data);
          this.temp = Object.assign({}, data)
        } else {
          this.temp = Object.assign({}, this.defaultTemp)
        }
      })
    },
    cleanAbleChange(value) {
      if (!value) {
        this.$nextTick(() => {
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate();
        })
      }
    },
    serverBakAbleChange(value) {
      if (!value) {
        this.$nextTick(() => {
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate();
        })
      }
    },
    cleanRetainVersionAbleChange(value) {
      if (!value) {
        this.$nextTick(() => {
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate();
        })
      }
    },
    cleanCycleValid(rule, value, callback) {
      if (this.temp.server.cleanAble && (!this.temp.server.cleanCycle || this.temp.server.cleanCycle.length === 0)) {
        callback(new Error(this.$t('pages.pleaseSelectContent', { content: this.$t('pages.cleanCycle') })))
      } else {
        callback()
      }
    },
    cleanTimeValid(rule, value, callback) {
      if (this.temp.server.cleanAble && (!this.temp.server.cleanTime || !this.temp.server.cleanTime[0] || !this.temp.server.cleanTime[1])) {
        callback(new Error(this.$t('pages.pleaseSelectContent', { content: this.$t('pages.cleanPeriod') })))
      } else {
        callback()
      }
    },
    backupTimeValid(rule, value, callback) {
      if (this.temp.server.serverBakAble && (!this.temp.server.backupTime || !this.temp.server.backupTime[0] || !this.temp.server.backupTime[1])) {
        callback(new Error(this.$t('pages.pleaseSelectContent', { content: this.$t('pages.backupPeriod') })))
      } else {
        callback()
      }
    },
    highConfValid(rule, value, callback) {
      if (this.temp.term.retainVersionAble && (!this.temp.term.retainVersion || this.temp.term.retainVersion.length === 0)) {
        callback(new Error(this.$t('pages.backupTermHighConfigMsg')))
      } else if (this.temp.term.retainVersionAble && this.temp.term.retainVersion) {
        const retainVersion = this.temp.term.retainVersion.length > 0 ? this.temp.term.retainVersion.reduce((pre, cur) => pre + cur) : undefined
        if ((retainVersion & 1) === 1 && !this.temp.term.retainVersionConfig.allVersionDays && this.temp.term.retainVersionConfig.allVersionDays !== 0) {
          callback(new Error(this.$t('text.cantNull')))
        } else if ((retainVersion & 2) === 2 && !this.temp.term.retainVersionConfig.onlyVersionDay && this.temp.term.retainVersionConfig.onlyVersionDay !== 0) {
          callback(new Error(this.$t('text.cantNull')))
        } else if ((retainVersion & 4) === 4 && !this.temp.term.retainVersionConfig.onlyVersionWeek && this.temp.term.retainVersionConfig.onlyVersionWeek !== 0) {
          callback(new Error(this.$t('text.cantNull')))
        } else if ((retainVersion & 8) === 8 && !this.temp.term.retainVersionConfig.onlyVersionMonth && this.temp.term.retainVersionConfig.onlyVersionMonth !== 0) {
          callback(new Error(this.$t('text.cantNull')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    durationValid(rule, value, callback) {
      if (!this.temp.term.duration) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    versionsValid(rule, value, callback) {
      if (!this.temp.term.versions) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    cacheModelValid(rule, value, callback) {
      if (!this.temp.term.cacheDiskSize) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    fullScanCacheValid(rule, value, callback) {
      // 必填但无需校验
      callback()
    },
    buildCleanCycleOptions() {
      const opts = []
      const weekDays = [
        { value: 1, label: this.$t('pages.monday1') },
        { value: 2, label: this.$t('pages.tuesday1') },
        { value: 4, label: this.$t('pages.wednesday1') },
        { value: 8, label: this.$t('pages.Thursday1') },
        { value: 16, label: this.$t('pages.friday1') },
        { value: 32, label: this.$t('pages.saturday1') },
        { value: 64, label: this.$t('pages.sunday1') }
      ]
      opts.push({ value: 7, label: this.$t('pages.weekly'), children: weekDays })
      const monthDays = []
      for (let i = 1; i <= 31; i++) {
        // 按位存值，int(32)刚好可以存下
        monthDays.push({ value: 1 << (i - 1), label: this.$t('pages.dateNum', { date: i }) })
      }
      opts.push({ value: 31, label: this.$t('pages.monthly'), children: monthDays })
      return opts
    },
    doBackupClear() {
      this.$confirmBox(this.$t('pages.immediatelyClearConfirm'), this.$t('text.prompt')).then(() => {
        this.clearLoading = true
        doBackupClear().then(() => {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.immediatelyClearSendSuccess'),
            type: 'success',
            duration: 2000
          })
          this.clearLoading = false
          this.initTimerTips();
        }).catch((err) => {
          console.log(err)
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('pages.immediatelyClearSendFailed'),
            type: 'error',
            duration: 2000
          })
          this.clearLoading = false
          this.initTimerTips();
        })
      });
    },
    initTimerTips() {
      this.showTipsTimer = true
      this.showTipsNumber = 10
      const timer = setInterval(() => {
        this.showTipsNumber--
        if (this.showTipsNumber <= 0) {
          clearInterval(timer)
          this.showTipsTimer = false
        }
      }, 1000);
    }
  }
}
</script>

<style lang="scss" scoped>
  .app-container{
    overflow: auto;
  }
  .el-input {
    max-width: 200px;
  }
  .el-button {
    position: relative;
    left: 465px;
  }
  .server-form {
    height: calc(100% - 60px);
    overflow: auto;
    font-size: 14px;
    padding-left: 50px;
    margin: 20px 0 0 20px;
    .server-label {
      display: inline-block;
      width: 40%;
    }
    .server-footer {
      //position: absolute;
      //left: 572px;
      //bottom: 20px;
      margin-bottom: 25px;
    }
    >>>.el-divider__text {
      font-size: 15px;
      font-weight: 700;
    }
    /*.el-form-item {*/
    /*  padding-left: 32px;*/
    /*}*/
    >>>.el-form-item__label {
      span {
        font-size: 12px;
        font-weight: 400;
        &:before {
          content: ' ';
        }
      }
    }
    .el-input, .el-input-number {
      width: 200px;
      >>>.el-input__inner {
        position: relative;
        text-align: left;
      }
    }
    .el-button {
      min-width: 90px;
      margin-left: 20px;
    }
  }
  .scan-cycle {
    line-height: 30px;
  }
</style>
