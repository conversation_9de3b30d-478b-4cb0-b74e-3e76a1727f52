<!-- 备份文件范围组件（最小值-最大值 带单位） -->
<template>
  <div class="backup-size-input">
    <el-input-number v-model="value.minSize" :disabled="disabled" :controls="false" step-strictly :min="min" :max="max" :step="1" @blur="countsBlur" />
    <el-select slot="unit" v-model="value.minSizeUnit" :disabled="disabled" @change="countsBlur">
      <el-option v-for="item in unitOptions" :key="item.key" :label="item.value" :value="item.key"/>
    </el-select>
    ~
    <el-input-number v-model="value.maxSize" :disabled="disabled" :controls="false" step-strictly :min="1" :max="max" :step="1" @blur="countsBlur" />
    <el-select slot="unit" v-model="value.maxSizeUnit" :disabled="disabled" @change="countsBlur">
      <el-option v-for="item in unitOptions" :key="item.key" :label="item.value" :value="item.key"/>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'BackupSizeInput',
  props: {
    value: {
      type: Object,
      required: true,
      default() {
        return {
          minSize: 0,
          minSizeUnit: 1,
          maxSize: 100,
          maxSizeUnit: 1
        }
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      // 改成数字
      default: 9999
    }
  },
  data() {
    return {
      // 单位
      unitOptions: [
        { key: 0, value: 'B' },
        { key: 1, value: 'KB' },
        { key: 2, value: 'MB' },
        { key: 3, value: 'GB' },
        { key: 4, value: 'TB' }
      ]
    }
  },
  methods: {
    countsBlur() {
      if (!this.value.minSize && this.value.minSize !== 0) {
        this.value.minSize = 0
        this.value.minSizeUnit = 1
      }
      if (!this.value.maxSize && this.value.maxSize !== 0) {
        this.value.maxSize = this.max
        this.value.maxSizeUnit = 1
      }
      if (this.value.minSize * Math.pow(1024, this.value.minSizeUnit) > this.value.maxSize * Math.pow(1024, this.value.maxSizeUnit)) {
        this.value.minSize = this.value.maxSize
        this.value.minSizeUnit = this.value.maxSizeUnit
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .backup-size-input {
    display: inline-block;
    .el-input-number {
      min-width: 120px;
      width: 30%;
    }
    .el-select {
      width: 60px;
      margin-top: -4px;
    }
  }
</style>
