<!-- 扫描周期组件（多选） -->
<template>
  <el-cascader
    v-model="value[`${field}`]"
    :disabled="disabled"
    :collapse-tags="true"
    :options="scanCycleOpts"
    :props="{ expandTrigger: 'hover', multiple: true }"
    @change="handleScanCycleChange"
    @expand-change="handleNodeChange"
  />
</template>

<script>
export default {
  name: 'BackupScanCycle',
  props: {
    value: {
      type: Object,
      required: true,
      default() {
        return {}
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    field: {
      type: String,
      default: 'cleanCycle'
    }
  },
  data() {
    return {
      scanCycleOpts: [],
      currentParentNode: undefined
    }
  },
  created() {
    this.scanCycleOpts = this.buildCycleOptions()
  },
  methods: {
    buildCycleOptions() {
      const opts = []
      opts.push({ value: 1, label: this.$t('pages.everyDay') })
      const weekDays = [
        { value: 1, label: this.$t('pages.monday1') },
        { value: 2, label: this.$t('pages.tuesday1') },
        { value: 4, label: this.$t('pages.wednesday1') },
        { value: 8, label: this.$t('pages.Thursday1') },
        { value: 16, label: this.$t('pages.friday1') },
        { value: 32, label: this.$t('pages.saturday1') },
        { value: 64, label: this.$t('pages.sunday1') }
      ]
      opts.push({ value: 7, label: this.$t('pages.weekly'), children: weekDays })
      const monthDays = []
      for (let i = 1; i <= 31; i++) {
        // 按位存值，int(32)刚好可以存下
        monthDays.push({ value: 1 << (i - 1), label: this.$t('pages.dateNum', { date: i }) })
      }
      opts.push({ value: 31, label: this.$t('pages.monthly'), children: monthDays })
      return opts
    },
    handleNodeChange(value) {
      this.currentParentNode = value[0] ? value[0] : 1
    },
    handleScanCycleChange(value) {
      // 遍历value[]数组，第一个数若不相同，则说明是切换其他选项
      let sameType = true;
      value.map(item => {
        if (item[0] !== value[0][0]) {
          sameType = false;
        }
      })
      if (!sameType) {
        const arr = []
        value.map(item => {
          if (item[0] === this.currentParentNode) {
            arr.push(item)
          }
        })
        this.value[this.field] = arr;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
