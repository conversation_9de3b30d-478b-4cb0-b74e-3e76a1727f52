<template>
  <div class="app-container">
    <Form
      ref="dataForm"
      :model="temp"
      :hide-required-asterisk="true"
      style="width: 700px;"
      label-position="right"
      label-width="100px"
    >
      <el-card class="box-card" style="background-color: transparent;">
        <div slot="header">
          <span>{{ $t('pages.basicSetting') }}</span>
        </div>
        <FormItem :label="$t('pages.language')" label-width="45px" :extra-width="{ en: 35 }" style="margin-top: 0px;margin-bottom: 10px;">
          <el-select v-model="i18nTabName" @change="reloadAllTable">
            <el-option value="zh_CN" :label="$t('pages.zh_CN')"></el-option>
            <el-option value="zh_TW" :label="$t('pages.zh_TW')"></el-option>
            <el-option value="en_US" :label="$t('pages.English')"></el-option>
          </el-select>
        </FormItem>
        <div class="table-container">
          <grid-table
            ref="encTable"
            :height="182"
            :show-pager="false"
            :col-model="colModel"
            :multi-select="false"
            :row-data-api="() => { return getRows(i18nTabName) }"
          />
        </div>
      </el-card>
      <el-card class="box-card" style="background-color: transparent;">
        <div slot="header">
          <span>{{ $t('pages.iconSetting') }}</span>
        </div>
        <el-row>
          <el-col :span="5">
            <el-radio v-model="temp.icoNum" :label="0">{{ $t('pages.icoNum1') }}</el-radio>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock"/>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock1-1"/>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock1-2"/>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock1-3"/>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock1-4"/>
          </el-col>
        </el-row>
        <el-row style="padding-top: 20px">
          <el-col :span="5">
            <el-radio v-model="temp.icoNum" :label="1">{{ $t('pages.icoNum2') }}</el-radio>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock"/>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock2-1"/>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock2-2"/>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock2-3"/>
          </el-col>
          <el-col :span="3">
            <svg-icon icon-class="lock2-4"/>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card" style="background-color: transparent;color: inherit;">
        <div slot="header">
          <span>{{ $t('pages.paramsSetting') }}</span>
        </div>
        <FormItem :label="$t('pages.enableEncryptLevel')" label-width="100px" :extra-width="{ en: 60 }" prop="encryptLevel" style="margin: 0;">
          <el-radio v-model="temp.visibleCount" :label="1">{{ $t('pages.encryptLevel1') }}</el-radio>
          <el-radio v-model="temp.visibleCount" :label="2">{{ $t('pages.encryptLevel2') }}</el-radio>
          <el-radio v-model="temp.visibleCount" :label="3">{{ $t('pages.encryptLevel3') }}</el-radio>
          <el-radio v-model="temp.visibleCount" :label="4">{{ $t('pages.encryptLevel4') }}</el-radio>
        </FormItem>
      </el-card>
      <div class="save-btn-container">
        <el-button :loading="submitting" type="primary" size="mini" @click="createData()">
          {{ $t('button.save') }}
        </el-button>
      </div>
    </Form>
  </div>
</template>

<script>
import { getI18nSetting, saveSetting } from '@/api/dataEncryption/docPemission/denseSet'

export default {
  name: 'DenseSet',
  data() {
    return {
      colModel: [
        { prop: 'label', label: 'icon', fixedWidth: '80', iconFormatter: this.iconClassFormat },
        { prop: 'encryptLevel', label: 'encryptLevel', fixedWidth: '180' },
        { prop: 'denseName', label: 'denseName', type: 'input', maxlength: 50, editMode: true, clearable: false, width: '150' }
      ],
      itemRowData: [],
      dialogFormVisible: false,
      submitting: false,
      editable: true,
      i18nTabName: 'zh_CN',
      temp: {
        icoNum: 0,
        visibleCount: 4,
        language: 'zh_CN',
        denseInfoList: []
      },
      rules: {}
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    getI18nSetting().then(res => {
      this.temp = Object.assign(this.temp, res.data)
      this.temp.denseInfoList.forEach(data => {
        this.itemRowData.push(Object.assign({}, data))
      })
      this.reloadAllTable()
    })
  },
  methods: {
    reloadAllTable() {
      if (this.$refs.encTable) {
        this.$refs.encTable.execRowDataApi()
      }
    },
    getRows(lang) {
      const rowDatas = this.itemRowData.filter(item => item.language == lang)
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: { total: rowDatas.length, items: rowDatas }})
      })
    },
    createData() {
      if (!this.validateDenseInfoList()) return
      this.submitting = true
      this.temp.denseInfoList = this.itemRowData
      saveSetting(this.temp).then(res => {
        this.submitting = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.saveSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(e => {
        this.submitting = false
      })
    },
    validateDenseInfoList() {
      let valid = true
      for (let i = 0; i < this.itemRowData.length; i++) {
        const { denseName, language } = this.itemRowData[i];
        this.itemRowData[i].denseName = denseName.trim()
        if (!denseName.trim()) {
          this.$message({
            message: this.$t('pages.validDesc'),
            type: 'error'
          })
          this.i18nTabName = language
          this.reloadAllTable()
          valid = false
          break
        }
      }
      return valid
    },
    iconClassFormat(row) {
      const iconMap = [
        ['lock', 'lock1-1', 'lock1-2', 'lock1-3', 'lock1-4'],
        ['lock', 'lock2-1', 'lock2-2', 'lock2-3', 'lock2-4']
      ]
      return iconMap[this.temp.icoNum][row.encryptLevel]
    }
  }
}
</script>

<style lang="scss" scoped>
  .app-container{
    overflow: auto;
  }
  >>>.el-form-item{
    margin: 20px 0 0;
  }
  >>>.el-form-item__label{
    line-height: 30px;
    color: #ccc;
  }
  >>>.el-row, >>>.el-form-item__content{
    line-height: 30px;
  }
  .save-btn-container{
    width: 700px;
    margin-top: 10px;
    text-align: right;
  }
</style>
