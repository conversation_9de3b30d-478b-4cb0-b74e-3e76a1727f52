<template>
  <div class="page-container">
    <div class="topbar">
      <img :src="logoSrc" class="topbar-logo">
      <h1 class="topbar-title">{{ title }}</h1> | {{ $t('pages.retrievePassword') }}
    </div>
    <div class="fp-container">
      <Form ref="fpForm" :model="temp" :rules="rules" class="fp-form" auto-complete="off" label-position="right" label-width="110px">
        <!-- 处理自动填充账号密码的问题 -->
        <input type="text" class="autocomplete">
        <input type="password" class="autocomplete">

        <FormItem :label="$t('table.account')" prop="account">
          <el-input v-model="temp.account"/>
        </FormItem>
        <FormItem :label="validEmail ? $t('form.email') : $t('table.phoneNumber')" class="flex-item" :prop="validEmail ? 'email' : 'phone'">
          <el-input v-if="validEmail" v-model="temp.email" />
          <el-input v-if="cloudEnv && !validEmail" v-model="temp.phone" />
          <el-button v-if="cloudEnv" type="primary" size="mini" style="width: 40px; padding: 0" :title="validEmail ? $t('pages.switchToSMS') : $t('pages.switchToEmail')" @click="switchClick">
            <svg-icon icon-class="import" />
          </el-button>
        </FormItem>
        <FormItem :label="$t('pages.captcha')" class="flex-item" prop="code">
          <el-input v-model="temp.code" />
          <el-button v-if="validEmail" type="primary" size="mini" class="ellipsis" :title="getCodeText" :loading="emailCodeLoading" :disabled="validCodeDisabled" style="width: 100px; padding: 0;" @click="getCode">{{ getCodeText }}</el-button>
          <el-button v-if="!validEmail" type="primary" size="mini" class="ellipsis" :title="getSMSCodeText" :loading="smsCodeLoading" :disabled="validCodeSMSDisabled" style="width: 100px; padding: 0;" @click="getCode">{{ getSMSCodeText }}</el-button>
        </FormItem>
        <FormItem :label="$t('pages.sysUserPasswordNew')" encrypt prop="password">
          <el-popover
            placement="top-end"
            class="pop"
            width="300"
            trigger="focus"
          >
            <div v-show="dataList.passwordLength">
              <i v-show="dataList.failLength" class="el-icon-close red bolder" />
              <i v-show="dataList.successLength" class="el-icon-check green bolder" />
              {{ $t('pages.passwordLength', { length: passwordLength }) }}<br/>
            </div>
            <div v-show="dataList.passwordLevel">
              <i v-show="dataList.failLevel" class="el-icon-close red bolder" />
              <i v-show="dataList.successLevel" class="el-icon-check green bolder" />
              {{ $t('pages.passwordLevel', { type: dataList.passwordLevel }) }}<br/>
            </div>
            <i v-show="dataList.failPassword" class="el-icon-close red bolder" />
            <i v-show="dataList.successPassword" class="el-icon-check green bolder" />
            {{ $t('pages.sysUserPasswordMsg7') }}
            <el-input slot="reference" v-model="temp.password" type="password" show-password :maxlength="64" @keyup.native="strengthShow" @input="temp.password=temp.password.replace(/[\u4E00-\u9FA5]/g,'').trim()" >
              <!-- <el-button slot="suffix" class="btn" icon="el-icon-info"></el-button> -->
            </el-input>
          </el-popover>
        </FormItem>
        <FormItem :label="$t('form.confirmPassword')" no-submit prop="confirmPassword">
          <el-input v-model="temp.confirmPassword" type="password" show-password maxlength="64"/>
        </FormItem>
        <div class="btn-container">
          <el-button size="small" class="ellipsis" :title="$t('pages.returnLogin')" @click="returnLogin">{{ $t('pages.returnLogin') }}</el-button>
          <el-button :loading="submitting" size="small" :disabled="modifiable" @click="update">{{ $t('button.edit') }}</el-button>
        </div>
      </Form>
    </div>
  </div>
</template>

<script>
import { sendFindPwdMail, sendFindPwdSms, updateFindPwd } from '@/api/user'
import { getPartConfig } from '@/api/system/configManage/globalConfig'
import { valiPassword } from '@/api/system/organizational/threeUser'
import { getSystemResources } from '@/utils/i18n'
import { validatePassword } from '@/utils/validate'
import { validCloudEnv } from '@/api/system/deviceManage/cloudServer'
import { base64DecodeSpe } from '@/utils/encrypt'
import { getFactoryPwd } from '@/utils/dictionary'

export default {
  name: 'FindPwd',
  data() {
    return {
      temp: {},
      dataList: {
        passwordLevel: '',
        passwordLength: '',
        successLength: false,
        failLength: false,
        successLevel: false,
        failLevel: false,
        failPassword: false,
        successPassword: false
      },
      passwordLength: '',
      rules: {
        account: [{ required: true, validator: this.accountValidator, trigger: 'blur' }],
        email: [{ required: true, validator: this.emailValidator, trigger: ['blur'] }],
        phone: [{ required: true, validator: this.phoneValidator, trigger: ['blur'] }],
        password: [{ required: true, trigger: 'blur', validator: this.passwordValidator }],
        confirmPassword: [{ required: false, trigger: 'blur', validator: this.confirmPasswordValidator }]
      },
      cloudEnv: false,
      validEmail: true,
      validCodeDisabled: false,
      validCodeSMSDisabled: false,
      emailCodeLoading: false,
      smsCodeLoading: false,
      timer: null,
      timerSms: null,
      countdown: 59,
      countdownSms: 59,
      submitting: false,
      factoryPwd: getFactoryPwd()
    }
  },
  computed: {
    getCodeText() {
      return this.validCodeDisabled ? this.$t('pages.reacquire', { second: this.countdown }) : this.$t('pages.getVerificationCode')
    },
    getSMSCodeText() {
      return this.validCodeSMSDisabled ? this.$t('pages.reacquire', { second: this.countdownSms }) : this.$t('pages.getVerificationCode')
    },
    modifiable() {
      return !(this.temp.account && this.temp.code && this.temp.password)
    },
    title() {
      return getSystemResources('title')
    },
    logoSrc() {
      return getSystemResources('topLogo')
    }
  },
  watch: {
    'temp.account'(newValue, oldValue) {
      if (newValue && this.temp.password) {
        valiPassword({
          account: newValue,
          password: this.temp.password,
          encryptProps: ['password']
        }).then(respond => {
          if (respond.data) {
            this.dataList.failPassword = true
            this.dataList.successPassword = false
          } else {
            this.dataList.failPassword = false
            this.dataList.successPassword = true
          }
        })
      } else {
        this.dataList.failPassword = false
        this.dataList.successPassword = false
      }
    },
    'temp.password'(newValue, oldValue) {
      if (newValue && this.temp.account) {
        valiPassword({
          account: this.temp.account,
          password: newValue,
          encryptProps: ['password']
        }).then(respond => {
          if (respond.data) {
            this.dataList.failPassword = true
            this.dataList.successPassword = false
          } else {
            this.dataList.failPassword = false
            this.dataList.successPassword = true
          }
        })
      } else if (newValue && !this.temp.account) {
        this.dataList.failPassword = false
        this.dataList.successPassword = true
      } else {
        this.dataList.failPassword = false
        this.dataList.successPassword = false
      }
    }
  },
  created() {
    this.resetTemp()
    this.getConfig()
    this.validCloudEnv()
  },
  methods: {
    resetTemp() {
      this.temp = {
        account: '',
        email: '',
        code: '',
        password: '',
        confirmPassword: '',
        upPwdTime: null
      }
    },
    getCode() {
      this.temp.password = '11'
      this.temp.confirmPassword = '11'
      this.$refs['fpForm'].clearValidate()
      this.$refs['fpForm'].validate((valid) => {
        if (valid) {
          const isMail = this.validEmail
          this.temp.password = ''
          this.temp.confirmPassword = ''
          if (isMail) {
            this.emailCodeLoading = true
            sendFindPwdMail(this.temp).then(respond => {
              if (respond.data === 99) {
                this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.validateMessage1'), type: 'error', duration: 2000 })
              } else if (respond.data === 1) {
                this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.validateMessage2'), type: 'error', duration: 2000 })
              } else if (respond.data === 500) {
                this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.validateMessage4'), type: 'error', duration: 2000 })
              } else {
                this.getCodeSetTimer(isMail)
                this.$notify({ title: this.$t('text.success'), message: this.$t('pages.validateMessage5'), type: 'success', duration: 2000 })
              }
              this.emailCodeLoading = false
            }).catch(() => {
              this.emailCodeLoading = false
            })
          } else {
            this.smsCodeLoading = true
            sendFindPwdSms(this.temp).then(respond => {
              const status = respond.data
              if (status === 1) {
                this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.validateMessage3'), type: 'error', duration: 2000 })
              } else if (status === 3) {
                this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.validateMessage9'), type: 'error', duration: 2000 })
              } else if (status === 99) {
                this.$message({ title: this.$t('text.prompt'), message: this.$t('pages.validateMessage7'), type: 'error', duration: 2000 })
              } else {
                this.getCodeSetTimer(isMail)
                this.$notify({ title: this.$t('text.success'), message: this.$t('pages.validateMessage8'), type: 'success', duration: 2000 })
              }
              this.smsCodeLoading = false
            }).catch(() => {
              this.smsCodeLoading = false
            })
          }
        } else {
          this.temp.password = ''
          this.temp.confirmPassword = ''
        }
      })
    },
    getCodeSetTimer(isMail) {
      if (isMail) {
        this.validCodeDisabled = true
        clearInterval(this.timer)
        this.timer = setInterval(() => {
          this.countdown--
          if (this.countdown == 0) {
            clearInterval(this.timer)
            this.validCodeDisabled = false
            this.countdown = 59
          }
        }, 1000)
      } else {
        this.validCodeSMSDisabled = true
        clearInterval(this.timerSms)
        this.timerSms = setInterval(() => {
          this.countdownSms--
          if (this.countdownSms == 0) {
            clearInterval(this.timerSms)
            this.validCodeSMSDisabled = false
            this.countdownSms = 59
          }
        }, 1000)
      }
    },
    returnLogin() {
      this.$router.push({ path: '/login' })
    },
    strengthShow() {
      this.dataList = validatePassword(undefined, this.temp.password, this.dataList)
    },
    valiPasswordLength(data) {
      let flag = false
      if (data.passwordLength && data.successLength) {
        flag = true
      } else if (data.passwordLength && data.failLength) {
        flag = false
      } else {
        flag = true
      }
      return flag
    },
    validCloudEnv() {
      validCloudEnv().then(res => {
        if (res.data) {
          this.cloudEnv = true
        }
      })
    },
    accountValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterAccount')))
      } else {
        callback()
      }
    },
    emailValidator(rule, value, callback) {
      if (this.validEmail && (!value || !/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/.test(value))) {
        callback(new Error(this.$t('pages.validaEmail')))
      } else {
        callback()
      }
    },
    phoneValidator(rule, value, callback) {
      if (!this.validEmail && (!value || !(/^1[3456789]\d{9}$/.test(value)))) {
        callback(new Error(this.$t('pages.validateMsg_sms')))
      } else {
        callback()
      }
    },
    switchClick() {
      this.validEmail = !this.validEmail
      this.$refs['fpForm'].clearValidate(['email', 'phone'])
    },
    update() {
      this.submitting = true
      this.$refs['fpForm'].validate((valid) => {
        if (valid) {
          this.temp.upPwdTime = new Date()
          if (this.validEmail) {
            this.temp.findPasswordType = 1
          } else {
            this.temp.findPasswordType = 2
          }
          const tempData = JSON.parse(JSON.stringify(this.temp))
          updateFindPwd(tempData).then(respond => {
            this.submitting = false
            if (respond.data) {
              this.$store.dispatch('user/resetUpPwdTime', this.temp.upPwdTime)
              this.$notify({ title: this.$t('text.success'), message: this.$t('pages.pwdEditMsg1'), type: 'success', duration: 2000 })
              setTimeout(this.returnLogin, 1000)
            } else {
              this.$notify({ title: this.$t('text.prompt'), message: this.$t('pages.pwdEditMsg2'), type: 'error', duration: 2000 })
            }
          }).catch(r => { this.submitting = false })
        } else {
          this.submitting = false
        }
      })
    },
    getConfig() {
      getPartConfig().then(respond => {
        const data = respond.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'passwordLevel') {
            this.dataList.passwordLevel = item.value
          } else if (item.key === 'passwordLength') {
            this.dataList.passwordLength = item.value
            if (item.value.split('-')[0] === item.value.split('-')[1]) {
              this.passwordLength = item.value.split('-')[0]
            } else {
              this.passwordLength = item.value
            }
          }
        }
      })
    },
    passwordValidator(rule, value, callback) {
      let error
      if (!value) {
        this.rules.confirmPassword[0].required = false
        if (rule.required) {
          error = new Error(this.$t('pages.validateMsg_password'))
        }
      } else if (value && this.temp.account && this.temp.account.toLowerCase() && value == base64DecodeSpe(this.factoryPwd[this.temp.account.toLowerCase()])) {
        callback(new Error(this.$t('pages.validateMsg_factoryPassword')))
      } else if (value && this.factoryPwd[''] && value == base64DecodeSpe(this.factoryPwd[''])) {
        callback(new Error(this.$t('pages.validateMsg_factoryPassword')))
      } else {
        /* if (this.temp.account) {
          valiPassword({
            account: this.temp.account,
            password: this.temp.password
          }).then(respond => {
            if (respond.data) {
              this.dataList.failPassword = true
              this.dataList.successPassword = false
            } else {
              callback()
            }
          })
        } */
        if (this.dataList.failLength || this.dataList.failLevel || this.dataList.failPassword) {
          callback(new Error(this.$t('pages.validateMsg_password_NotMatch')))
        } else {
          callback()
        }
        this.rules.confirmPassword[0].required = true
      }
      callback(error)
    },
    confirmPasswordValidator(rule, value, callback) {
      if (rule.required && !value) {
        callback(new Error(this.$t('pages.validateMsg_password')))
      } else if (rule.required && value !== this.temp.password) {
        callback(new Error(this.$t('pages.validateMsg_diffPassword')))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .page-container {
    height: 100%;
    border: 1px solid transparent;
  }
  .fp-container {
    padding: 200px 0 0;
    text-align: center;
  }
  .fp-form {
    width: 400px;
    display: inline-block;
    >>>.el-form-item__label, >>>.el-form-item__content{
      line-height: 30px;
      color: #eeeeee;
    }
    .flex-item {
      >>>.el-form-item__content {
        display: flex;
      }
      >>>.el-button {
        flex-shrink: 0;
      }
    }
    .el-form-item.is-error {
      margin-bottom: 16px;
    }
    .el-input{
      height: 30px;
      input {
        height: 100%;
      }
    }
    .el-form-item {
      margin-bottom: 10px;
    }
    .btn {
      border: none;
    }
    .btn-container {
      margin-top: 40px;
      text-align: right;
      padding-right: 40px;
      button{
        width: 100px;
        height: 30px;
        border: none;
        padding: 0;
        border-radius: 15px;
      }
    }
  }
</style>
