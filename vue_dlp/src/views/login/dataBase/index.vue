<template>
  <div class="form-container db-container">
    <h4 class="db-title">初始化配置</h4>
    <Form
      ref="dbForm"
      :model="temp"
      :rules="dbRules"
      class="login-form"
      auto-complete="off"
      label-position="left"
      label-width="100px"
    >
      <FormItem label="驱动类" prop="driverClassName">
        <el-input v-model="temp.driverClassName" />
      </FormItem>
      <FormItem label="JDBC URL" prop="jdbcUrl">
        <el-input v-model="temp.jdbcUrl" />
      </FormItem>
      <FormItem label="用户名" prop="username">
        <el-input v-model="temp.username" />
      </FormItem>
      <FormItem label="密码" prop="password">
        <el-input v-model="temp.password" />
      </FormItem>
    </Form>
    <div class="btn-container">
      <el-button :loading="submitting" size="small" @click="returnLogin">返回登录</el-button>
      <el-button :loading="submitting" size="small" @click="update">保存配置</el-button>
    </div>
  </div>
</template>

<script>
import { initDB } from '@/api/user'
export default {
  name: 'DataBase',
  data() {
    return {
      temp: {},
      dbRules: {
        driverClassName: [{ required: true, message: '驱动类不能为空', trigger: 'blur' }],
        jdbcUrl: [{ required: true, message: 'JDBC URL不能为空', trigger: 'blur' }],
        username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }]
      },
      submitting: false
    }
  },
  created() {
    // existDB().then(respond => {
    //   if (respond.data) {
    //     this.returnLogin()
    //   }
    // })
  },
  methods: {
    returnLogin() {
      this.$emit('returnLogin')
    },
    update() {
      this.submitting = true
      this.$refs['dbForm'].validate((valid) => {
        if (valid) {
          initDB(this.temp).then(respond => {
            this.submitting = false
            this.returnLogin()
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.createSuccess'), type: 'success', duration: 2000 })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .db-container{
    .db-title{
      margin-top: 10px;
    }
    >>>.el-form-item__label, >>>.el-form-item__content{
      line-height: 30px;
      color: #eeeeee;
    }
    .el-input{
      height: 30px;
      input {
        height: 100%;
      }
    }
    .el-form-item {
      margin-bottom: 10px;
    }
    .btn-container {
      margin-top: 40px;
      text-align: center;
      button{
        width: 100px;
        height: 30px;
        background: #295576;
        border: none;
        padding: 0;
        color: #fff;
        border-radius: 15px;
      }
    }
  }
</style>
