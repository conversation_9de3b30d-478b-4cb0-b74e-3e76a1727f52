<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    title="选择常见浏览器进程"
    :visible.sync="dlgVisible"
    width="400px"
    @dragDialog="handleDrag"
  >
    <grid-table ref="browserList" :height="200" :col-model="browserColModel" :show-pager="false" :row-data-api="browserRowDataApi" />
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="addBrowser()">
        添加
      </el-button>
      <el-button @click="dlgVisible = false">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSysBrowserPage } from '@/api/system/baseData/serverLibrary'

export default {
  name: 'BrowserSelectDlg',
  data() {
    return {
      browserColModel: [
        { prop: 'name', label: '浏览器名称', width: '150' },
        { prop: 'processName', label: '浏览器进程名', width: '150' }
      ],
      dlgVisible: false
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    show() {
      if (this.browserTable()) this.browserTable().clearSelection()
      this.dlgVisible = true
    },
    browserTable() {
      return this.$refs['browserList']
    },
    browserRowDataApi: function(option) {
      const searchQuery = Object.assign({}, { page: 1 }, option)
      return getSysBrowserPage(searchQuery)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    addBrowser() {
      const selectedDatas = this.browserTable().getSelectedDatas()
      this.$emit('change', selectedDatas)
      this.dlgVisible = false
    }
  }
}
</script>
