<template>
  <div class="login-container" :style="loginConStyle">
    <!-- 左上角提示信息 -->
    <div v-if="trial" class="trial-version">
      <span>{{ regErrMsg }}</span>
      <p v-if="expirationTime">{{ expirationTime }}</p>
    </div>
    <!-- 正上方提示信息 -->
    <div class="prompt">
      <div v-if="lowVersion" class="browser-prompt">
        {{ $t('login.lowVersion_title') }}
        <el-tooltip class="item" effect="dark" placement="bottom">
          <div slot="content">{{ $t('login.lowVersion_tip') }}</div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <div v-if="registerPrompt" class="register-prompt">
        {{ registerPrompt }}
      </div>
    </div>
    <!-- 登录面板上方提示信息 -->
    <p class="login-error-msg">{{ loginErrorMsg }}</p>
    <!-- 登录面板 -->
    <Form ref="loginForm" :model="loginForm" :rules="loginRules" :class="'login-form'" :style="{ 'height': showCaptchaCode ? '420px' : '350px' }" auto-complete="off">
      <input type="text" class="autocomplete">
      <input type="password" class="autocomplete">
      <!-- 左侧标题 -->
      <div class="title-container">
        <img :src="logoSrc" alt="logo">
        <h3 class="title">{{ title }}</h3>
      </div>
      <!-- 登录信息面板 -->
      <div v-if="showLogin" class="form-container">
        <!-- 右上角功能按钮 -->
        <div class="icon-container">
          <span v-if="showReg" :title="$t('login.register')">
            <svg-icon icon-class="register" class="register" @click="register" />
          </span>
          <!--<span :title="$t('login.dbSetting')">
            <svg-icon icon-class="setting" class="db-setting" @click="dbSetting" />
          </span>-->
          <lang-select class="set-language" />
        </div>
        <h4 class="login-title">{{ $t('login.loginTitle') }}</h4>
        <FormItem v-show="accountMode" prop="username">
          <span class="svg-container">
            <svg-icon icon-class="user" />
          </span>
          <el-input
            ref="username"
            v-model="loginForm.username"
            :placeholder="$t('login.usernamePlaceholder')"
            type="text"
            auto-complete="off"
            maxlength=""
            @change="usernameChange"
            @keyup.enter.native="login"
          />
        </FormItem>

        <FormItem v-if="verifyCodeMode" style="border-bottom: none">
          <span class="svg-container">
            <svg-icon icon-class="user" />
          </span>
          <span style="margin-left: 15px">{{ loginForm.username }}</span>
        </FormItem>

        <FormItem v-show="accountMode" prop="password" style="margin-bottom: 22px;">
          <span class="svg-container">
            <svg-icon icon-class="password" />
          </span>
          <encrypt-input
            :key="passwordType"
            ref="password"
            v-model="loginForm.password"
            :type="passwordType"
            :show-password="false"
            :placeholder="$t('login.passwordPlaceholder')"
            auto-complete="off"
            maxlength=""
            @keyup.enter.native="login"
          />
          <!-- 不显示查看密码按钮
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span> -->
        </FormItem>

        <!-- 图行验证码 -->
        <FormItem v-show="showCaptchaCode" prop="captcha" style="position: relative; margin-bottom: 0">
          <span class="svg-container">
            <svg-icon icon-class="verify-code1" style="font-size: 17px;"></svg-icon>
          </span>
          <el-input
            ref="username"
            v-model="loginForm.captcha"
            :placeholder="$t('pages.multiLoginAuth_pleasePrintCode')"
            type="text"
            auto-complete="off"
            maxlength="10"
            @keyup.enter.native="login"
          />
          <img v-show="captchaUrl" :src="captchaUrl" style="height: 40px; position: absolute; right: 10px" alt="验证码" @click="refreshCaptcha"/>
        </FormItem>

        <FormItem v-if="verifyCodeMode" prop="code">
          <el-row style="position: relative;">
            <el-col :span="3" >
              <span class="svg-container">
                <svg-icon style="font-size: 18px" icon-class="verify-code" />
              </span>
            </el-col>
            <el-col :span="12" >
              <el-input
                ref="code"
                v-model="loginForm.code"
                :placeholder="$t('pages.multiLoginAuth_pleasePrintCode')"
                type="text"
                auto-complete="off"
                maxlength=""
                @input="codeInputEvent"
              />
            </el-col>
            <el-col :span="9">
              <div>
                <el-button style="background-color: whitesmoke; border-radius: 30px; width: 115px" :loading="loginForm.isSendCode" :disabled="codeBtn !== codeBtnName" @click="getCode">{{ codeBtn }}</el-button>
              </div>
            </el-col>
          </el-row>
        </FormItem>

        <p v-show="accountMode">
          <el-checkbox v-model="loginForm.rememberPassword" class="remember-password">{{ $t('login.rememberPassword') }}</el-checkbox>
          <el-button type="text" class="find-back-password" style="padding: 0;" @click.native.prevent="findPassword">{{ $t('login.findPassword') }}</el-button>
        </p>

        <div class="loin-btn-div">
          <el-button v-show="accountMode" :loading="loading" class="login-btn" :disabled="lowVersion" @click.native.prevent="login">{{ $t('login.handleLogin') }}</el-button>
          <el-button-group v-show="verifyCodeMode" style="width: 100%" >
            <el-button class="login-btn-group left" @click="btnReturn">{{ $t('pages.multiLoginAuth_return') }}</el-button>
            <el-button class="login-btn-group right" :loading="loading" :disabled="lowVersion" @click.native.prevent="verifyMultiLoginAuth">{{ $t('login.handleLogin') }}</el-button>
          </el-button-group>
          <div v-if="verifyCodeMode" class="msg-text">
            {{ multiLoginYzmType === 6 ? $t('pages.multiLoginAuth_message16', { email }) : $t('pages.multiLoginAuth_message3', {verifyCode}) }}
          </div>
          <div v-if="verifyCodeMode && multiLoginYzmType === 6 && email != null && !enLang" class="msg-text">
            {{ email }}
          </div>
        </div>
      </div>
      <!-- 初始化配置面板 -->
      <data-base v-else @returnLogin="showLogin = true"/>

    </Form>
    <div class="copyright">{{ copyright }}</div>

    <register-dialog ref="registerDialog" @regEnd="regEnd"/>
    <plug-port-dlg ref="plugPortDlg" @connectionSuccess="plugConnectionSuccess"></plug-port-dlg>
    <plug-update-dlg ref="plugUpdateDlg" :re-operator="$t('pages.login')"/>
  </div>
</template>

<script>
import LangSelect from '@/components/LangSelect'
import { getLeftDaysIn, getRegisterStatus, resetPassword } from '@/api/system/register/reg'
import { Message } from 'element-ui'
import Cookies from 'js-cookie'
import DataBase from './dataBase'
import RegisterDialog from '@/views/register/registerDialog'
import { cookieGetPwd } from '@/utils/auth'
import { aesDecode, aesEncode, base64DecodeSpe } from '@/utils/encrypt';
import { getPartConfig } from '@/api/system/configManage/globalConfig'
import { isMatchPasswordLevel } from '@/utils/validate'
import { getCurrentDataBaseServerInfo } from '@/api/system/terminalManage/manageLibrary';
import { verifyMultiLoginAuth } from '@/api/loginAuth';
import { Level, UTermPluginClient } from '@/views/system/terminalManage/terminal/uterm/client';
import PlugPortDlg from '@/views/loginAuth/plugPortDlg'
import PlugUpdateDlg from '@/views/loginAuth/plugUpdateDlg'
import { getSystemResources } from '@/utils/i18n'
import { getFactoryPwd } from '@/utils/dictionary'
import EncryptInput from '@/components/EncryptInput'
import request from '@/utils/request'

export default {
  name: 'Login',
  components: { EncryptInput, LangSelect, DataBase, RegisterDialog, PlugPortDlg, PlugUpdateDlg },
  data() {
    const lowVersion = this.isIE()
    return {
      trial: false, // 是否试用版
      regErrMsg: this.$t('login.data_regErrMsg'),
      expirationTime: undefined, // 试用到期时间
      registerPrompt: '', // 注册到期提醒消息
      lowVersion: lowVersion,
      showLogin: true,   // 是否显示登录信息面板，为false时，显示初始化配置面板
      loginForm: {
        username: '',
        password: '',
        type: 'normal',
        rememberPassword: false,
        code: '',       //  验证码
        codeType: null,  //  验证码类型：1：短信验证码，2：邮箱验证码
        isSendCode: false,
        clientIpAddresses: [],  //  客户端Ip地址
        clientMacAddresses: [],  //  客户端物理地址
        plugAble: false,
        captcha: ''  //  图形验证码
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: this.accountValidator }],
        password: [{ required: true, trigger: 'blur', validator: this.passwordValidator }],
        captcha: [{ required: true, trigger: 'blur', validator: this.captchaValidator }],
        code: [
          { required: true, validator: this.codeValidator, trigger: 'blur' }
        ]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      showReg: false,
      loginErrorMsg: '',
      level: 1,
      forceValiPwdLevel: false,
      loginError: {
        50014: this.$t('login.data_loginError_50014'),
        500141: this.$t('login.data_loginError_500141')
      },
      messageInstance: null, // 登录过期消息的实例，用于手动关闭该消息使用
      multiLoginYzmType: null,  // 验证码类型，5是短信验证码，6是邮箱验证码
      sendTimeInterval: null,
      codeBtn: this.$t('pages.multiLoginAuth_getCode'),
      codeBtnName: this.$t('pages.multiLoginAuth_getCode'),
      codeInterval: 60,  //  每隔60秒可获取验证码一次
      uPlugin: null,   //  mac地址插件
      // 可重置密码的超管账号
      resetPasswordAccount: undefined,
      // 重置密码标志位
      resetPasswordFlag: false,
      factoryPwd: getFactoryPwd(),
      email: null,   //  邮箱地址
      captchaUrl: null,  //  图形验证码路径
      captchaId: null,    //  保存图形验证码Id
      isValidCode: false  //  是否支持校验图形验证码
    }
  },
  computed: {
    title() {
      const title = getSystemResources('title')
      document.title = title || ''
      return title
    },
    copyright() {
      return getSystemResources('copyright')
    },
    logoSrc() {
      return getSystemResources('loginLogo')
    },
    loginConStyle() {
      const bgSrc = getSystemResources('loginBackground') || require('@/assets/login/loginBg.jpg')
      return `background: url(${bgSrc})`
    },
    // 账号登录的模式
    accountMode() {
      return this.multiLoginYzmType == null
    },
    // 验证码登录的模式
    verifyCodeMode() {
      return this.multiLoginYzmType != null
    },
    verifyCode() {
      const typeOption = {
        5: this.$t('pages.multiLoginAuth_note'),
        6: this.$t('pages.multiLoginAuth_email')
      }
      return typeOption[this.multiLoginYzmType] || this.$t('pages.multiLoginAuth_email')
    },
    enLang() {
      return this.$store.getters.language === 'en'
    },
    //  展示图形验证码
    showCaptchaCode() {
      return this.accountMode && this.isValidCode
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.refreshCaptcha()
    this.clearTime()
    this.setSendTime()
    this.validateRegister()
    const code = Cookies.get('loginErrorCode')
    const msg = this.loginError[code] || ''
    Cookies.remove('loginErrorCode')
    if (msg) {
      this.messageInstance = this.$message({
        message: msg,
        type: 'error',
        duration: 0
      })
    }
    const userPwd = cookieGetPwd()
    const isRememberPassword = userPwd != null && userPwd.hasOwnProperty('rememberPassword') && userPwd.rememberPassword
    if (isRememberPassword) {
      Object.assign(this.loginForm, userPwd)
    }
    // 清除特殊配置页面的访问状态
    this.removeMenuAccessStatus()
    this.validateAdminPasswordAuth()
  },
  methods: {
    validateRegister() {
      getRegisterStatus().then(respond => {
        this.trial = false
        this.showReg = false
        if (respond.data.status === 'error') {
          this.trial = true
          this.regErrMsg = ''
          this.expirationTime = respond.data.desc
          if (respond.data.code < 5500) {
            this.showReg = true
            this.register()
          }
        }
        if (respond.data.status !== 'error' || [5103, 5104].indexOf(respond.data.code) > -1) {
          getLeftDaysIn().then(respond => {
            const { daysLeftin, isTrialVersion, termOfflineDay } = respond.data
            let msg = ''
            if (daysLeftin > 0 && daysLeftin < 7) {
              msg = this.$t('login.validateMsg_expire1', { day: daysLeftin })
            } else if (isTrialVersion && daysLeftin <= 0) { // 试用版时，剩余使用天数小于0，则提醒终端是否还能工作。正式版无需提醒，终端一直可以工作
              if (daysLeftin + termOfflineDay > 0) {
                msg = this.$t('login.validateMsg_expire2', { day: daysLeftin + termOfflineDay })
              } else {
                msg = this.$t('login.validateMsg_expire3')
              }
            }
            this.registerPrompt = msg
          })
        }
      })
    },
    validateAdminPasswordAuth() {
      resetPassword().then(respond => {
        if (respond.data) {
          this.resetPasswordAccount = aesDecode(respond.data, 'TR_RESETPASSWORD');
        } else {
          this.resetPasswordAccount = undefined;
        }
      }).catch(() => {
        this.resetPasswordAccount = undefined;
      })
    },
    register() {
      this.$refs.registerDialog.handleRegister()
    },
    regEnd() {
      window.setTimeout(this.validateRegister, 600)
    },
    dbSetting() {
      this.showLogin = false
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      if (this.trial) {
        Message({ message: this.expirationTime, type: 'error', showClose: true, duration: 5 * 1000 })
        return
      }
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          const tempData = this.loginForm
          this.$store.dispatch('user/login', tempData).then(async(res) => {
            await getPartConfig().then(res => {
              const data = res.data
              for (let i = 0; i < data.length; i++) {
                const item = data[i]
                if (item.key === 'passwordLevel') {
                  this.level = item.value
                }
                if (item.key === 'forceValiPwdLevel') {
                  this.forceValiPwdLevel = item.value == 'true' ? Boolean(true) : false
                }
              }
            })
            localStorage.setItem('isMatchLevel', isMatchPasswordLevel(tempData.password, this.level, this.forceValiPwdLevel))
            localStorage.setItem('resetPasswordFlag', this.resetPasswordFlag)

            // 内置管理员账号的密码是否修改的标识
            const factoryPwdV1 = this.factoryPwd['']
            let factoryPwdFlag = factoryPwdV1 ? base64DecodeSpe(factoryPwdV1) == this.loginForm.password : false
            if (!factoryPwdFlag) {
              const factoryPwd = this.factoryPwd[this.loginForm.username.toLowerCase()]
              factoryPwdFlag = factoryPwd ? base64DecodeSpe(factoryPwd) == this.loginForm.password : false
            }
            localStorage.setItem('factoryPwdFlag', factoryPwdFlag)

            this.messageInstance && this.messageInstance.close()
            this.$router.push({ path: this.redirect || '/' })
            this.loading = false
            this.loadOsType();
            Cookies.set('isLogin', true)
            Cookies.remove('multiLoginTime')
          }).catch(() => {
            this.loading = false
          }).finally(() => {
            // 此处代码无论账号是否登录成功，都会执行。请确保此处代码不需要登录也可以执行
            this.$store.dispatch('commonData/setTrialDlp')
            this.$store.dispatch('commonData/isWindows')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    findPassword() {
      this.messageInstance && this.messageInstance.close()
      this.$router.push({ path: '/findPassword' })
    },
    accountValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('login.validateMsg_account1')))
      } else if (value.indexOf('<script>') > -1 && value.indexOf('<\/script>') > -1) {
        callback(new Error(this.$t('login.validateMsg_account2')))
      } else {
        callback()
      }
    },
    passwordValidator(rule, value, callback) {
      this.resetPasswordFlag = false;
      // 判断当前账号是否是超管，并且有重置密码权限的，可免密码登录
      if (this.resetPasswordAccount && this.loginForm.username.toLowerCase() === this.resetPasswordAccount) {
        this.resetPasswordFlag = true;
        callback()
      } else if (!value) {
        callback(new Error(this.$t('login.data_loginRules_password')))
      } else {
        callback()
      }
    },
    codeValidator(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error(this.$t('pages.multiLoginAuth_pleasePrintCode')));
      } else {
        callback()
      }
    },
    //  图形验证码校验
    captchaValidator(rule, value, callback) {
      if (this.showCaptchaCode && value.length === 0) {
        callback(new Error(this.$t('pages.multiLoginAuth_pleasePrintCode')));
      } else {
        callback()
      }
    },
    //  加载引擎所在的服务器的操作系统类型
    loadOsType() {
      getCurrentDataBaseServerInfo().then(res => {
        const data = res.data || {}
        const systemName = (data.serverInfo && data.serverInfo.serverBaseInfo && data.serverInfo.serverBaseInfo.systemName || '').trim().toLowerCase().toString()
        const masterDbType = res.data.masterDbType
        let osType = null
        if (systemName.indexOf('windows') > -1) {
          osType = 'windows'
        } else if (systemName.indexOf('linux') > -1) {
          osType = 'linux'
        } else if (systemName.indexOf('mac') > -1) {
          osType = 'mac'
        }
        if (osType != null) {
          this.$store.dispatch('commonData/setEngineOsType', osType)
        }
        if (masterDbType != null) {
          this.$store.dispatch('commonData/setMasterDbType', masterDbType);
        }
      })
    },
    usernameChange() {
      this.multiLoginYzmType = null
      this.loginForm.code = ''
      this.loginForm.codeType = null
      this.loginForm.isSendCode = false
      this.loginForm.clientIpAddresses = []
      this.loginForm.clientMacAddresses = []
      this.loginForm.plugAble = false
      this.clearTime()
    },
    login() {
      this.loginForm.clientIpAddresses = []
      this.loginForm.clientMacAddresses = []
      this.loginForm.plugAble = false
      this.verifyMultiLoginAuth()
    },
    //  多重登录认证
    verifyMultiLoginAuth() {
      const language = this.$store.getters.language
      const i18nData = this.$i18n.getLocaleMessage(language)
      // 验证多语言资源是否从后台请求完毕
      if (!(i18nData && i18nData['buildIn'])) {
        Message({ message: this.$t('login.languageResource'), type: 'error', showClose: true, duration: 3 * 1000 })
        return
      }
      if (this.trial) {
        Message({ message: this.expirationTime, type: 'error', showClose: true, duration: 5 * 1000 })
        return
      }
      //  获取短信或者邮箱验证码
      if (this.loginForm.isSendCode) {
        this.verifyMultiLoginAuthData()
      } else {
        this.$refs.loginForm.validate(async valid => {
          if (valid) {
            this.verifyMultiLoginAuthData()
          }
        })
      }
    },
    verifyMultiLoginAuthData() {
      this.loading = !this.loginForm.isSendCode
      if ((this.accountMode && this.loginForm.codeType === null) || (this.verifyCodeMode && this.loginForm.codeType != null)) {
        const form = JSON.parse(JSON.stringify(this.loginForm))
        form.username = aesEncode(JSON.stringify({ username: form.username, password: form.password }), 'tr838408userpass')
        delete form.password
        delete form.rememberPassword
        //  设置图形验证码
        if (!this.loginForm.isSendCode && this.showCaptchaCode) {
          form.captchaUUid = this.captchaId;
          form.captchaCode = this.loginForm.captcha
        }
        verifyMultiLoginAuth(form).then((res) => {
          const type = res.data.type
          // 2:此账号不能在当前IP地址环境登录, 3:此账号不能在当前MAC地址环境登录
          // 7:验证码错误，请输入正确的验证码, 8:验证码发送频繁，30分钟内不再发送消息, 10:请联系管理员配置邮箱服务器, 11:图形验证码错误,12:图形验证码过期
          if ([2, 3, 7, 8, 10, 11, 12].includes(type)) {
            this.loading = false
            res.data.message && this.$message({
              title: this.$t('text.prompt'),
              message: res.data.message,
              type: 'error',
              duration: 3000
            })
            //  刷新图形验证码
            if (type === 12) {
              this.refreshCaptcha()
            }
          } else if ([5, 6].includes(type)) {
            //  因在login页面加载时，语言包存在未加载完的可能性，固在此重新赋值
            this.codeBtnName = this.$t('pages.multiLoginAuth_getCode')
            if (type === 6) {
              this.email = res.data.data || null
            }
            if (this.loginForm.isSendCode) {
              this.getCodeTime()
              this.loginForm.isSendCode = false
            }
            //  5：短信验证码，6：邮箱验证码
            this.loading = false
            this.multiLoginYzmType = type
            this.loginForm.codeType = type === 5 ? 1 : 2
          } else if (type == 9) {
            //  9:尝试连接插件
            //  尝试进行插件连接
            this.connectionPlug()
          } else {
            this.handleLogin()
          }
        }).catch(() => {
          this.loginForm.isSendCode = false
          this.loading = false
        });
      } else {
        this.handleLogin()
      }
    },
    getCode() {
      this.loginForm.isSendCode = true
      // this.getCodeTime()
      this.verifyMultiLoginAuth()
    },
    getCodeTime() {
      let time = Cookies.get('multiLoginTime')
      time = time === undefined || time == 'NaN' ? this.codeInterval : time
      Cookies.set('multiLoginTime', time)
      this.setSendTime()
    },
    setSendTime() {
      if (this.sendTimeInterval == null) {
        const task = () => {
          let time = Cookies.get('multiLoginTime')
          time = time === undefined || time == 'NaN' ? 0 : time
          time = parseInt(time) - 1
          if (time <= 0) {
            this.clearTime()
            return;
          }
          this.codeBtn = time + 's'
          Cookies.set('multiLoginTime', time)
        };
        //  立刻执行一次
        task();
        //  之后每个1秒执行一次
        this.sendTimeInterval = setInterval(task, 1000)
      } else {
        let time = Cookies.get('multiLoginTime')
        time = time === undefined || time == 'NaN' ? this.$t('pages.multiLoginAuth_getCode') : time
        this.codeBtn = time + 's'
      }
    },
    clearTime() {
      this.codeBtn = this.$t('pages.multiLoginAuth_getCode')
      Cookies.remove('multiLoginTime')
      if (this.sendTimeInterval !== null) {
        clearInterval(this.sendTimeInterval)
        this.sendTimeInterval = null
      }
    },
    btnReturn() {
      this.loginForm.code = ''
      this.loginForm.codeType = null
      this.loginForm.isSendCode = false
      this.multiLoginYzmType = null
    },
    async getAddress() {
      const result = await this.validVersion();
      if (result) {
        await this.listMacAddress();
        await this.listIpAddress();
      }
      return result;
    },
    //  插件版本校验，低于版本时更新版本
    async validVersion() {
      if (this.uPlugin !== null) {
        const res = await this.uPlugin.getPlugInfo();
        //  当前插件版本号
        const plugVersion = res.PlugVersion || ''
        //  去除版本号的逗号
        const version = plugVersion.split('.')
        let pVersion = ''
        for (let i = 0, len = version.length; i < len; i++) {
          pVersion += version[i];
        }
        //  校验插件版本号,最低版本支持101230824SC
        if (pVersion < '101230824SC') {
          this.$refs['plugUpdateDlg'].show();
          this.loading = false
          return false;
        }
        return true;
      }
      return false;
    },
    async listMacAddress() {
      if (this.uPlugin !== null) {
        return this.uPlugin.listMacAddress().then(res => {
          this.loginForm.clientMacAddresses = [];
          (res || []).forEach(item => {
            this.loginForm.clientMacAddresses.push(item.MacAddress)
          })
        }).catch((err) => {
          console.log(err)
          this.loading = false
        })
      }
    },
    async listIpAddress() {
      if (this.uPlugin !== null) {
        return this.uPlugin.listIpAddress().then(res => {
          this.loginForm.clientIpAddresses = [];
          (res || []).forEach(item => {
            item.IPv4Address && this.loginForm.clientIpAddresses.push(item.IPv4Address)
            item.IPv6Address && this.loginForm.clientIpAddresses.push(item.IPv6Address)
          })
        }).catch((err) => {
          console.log(err)
          this.loading = false
        });
      }
    },
    /**
     * 连接插件
     * @returns {Promise<void>}
     */
    async connectionPlug() {
      this.uPlugin = new UTermPluginClient(this, UTermPluginClient.getPort(), Level.DEBUG, true)
      try {
        await this.uPlugin.connectedPromise
      } catch (e) {
        this.$refs['plugPortDlg'].show()
        this.loading = false
        return
      }
      this.getAddress().then(res => {
        if (res) {
          this.loginForm.plugAble = true
          this.verifyMultiLoginAuth()
          this.loading = false
        }
      }).catch((err) => {
        console.log(err)
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.pluginConnectionFailed'),
          type: 'error',
          duration: 2000
        })
      })
    },
    //  插件连接成功
    plugConnectionSuccess(plug) {
      this.getAddress().then(res => {
        if (res) {
          this.loginForm.plugAble = true
        }
      })
    },
    sleep(second) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve();
        }, second
        );
      });
    },
    codeInputEvent(value) {
      const parent = /^[0-9]+.?[0-9]*$/
      let str = ''
      if (value) {
        if (value.length > 6) {
          this.loginForm.code = value.substr(0, 6)
          return;
        }
        const arr = value.split('')
        for (let i = 0; i < arr.length; i++) {
          if (parent.test(arr[i])) {
            str += arr[i];
          }
        }
        this.loginForm.code = str
      }
    },
    /**
     * 刷新图形验证码
     */
    refreshCaptcha() {
      const res = {
        url: `/captcha/getCaptcha?timestamp=${new Date().getTime()}`,
        method: 'post',
        responseType: 'blob'
      }
      if (this.captchaId) {
        res.headers = { 'x-captcha-id': this.captchaId }
      }
      request(res).then(response => {
        // 从响应头中获取 ID
        this.captchaId = response.headers['x-captcha-id'];
        const captchaValid = response.headers['x-captcha-valid']
        this.isValidCode = captchaValid && captchaValid === 'true';
        return response.data;
      }).then(blob => {
        // 创建图片的临时 URL
        this.captchaUrl = window.URL.createObjectURL(blob);
      }).catch((error) => {
        this.captchaId = null
        this.captchaUrl = null
        console.error('获取图片和ID失败:', error);
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:#fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 37px;
    width: 80%;

    input {
      background: transparent;
      border: 0px;
      appearance: none;
      -webkit-appearance: none;
      border-radius: 0px;
      color: $light_gray;
      height: 100%;
      caret-color: $cursor;
      &:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 1000px #517b9c inset!important;
        box-shadow: 0 0 0px 1000px #517b9c inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }
  .el-dropdown{
    color: #fff;
    cursor: pointer;
  }

  .el-form-item {
    border: none;
    border-bottom: 1px solid rgb(196, 199, 201);
    background: transparent;
    color: #fff;
  }

  span.el-checkbox__label{
    color: #ccc;
  }
  .el-checkbox, .el-checkbox__input.is-checked+.el-checkbox__label{
    color: #fff;
  }
  .el-form-item__error{
    padding: 6px 0 0 6px;
    color: #ff2e2e;
  }

  input::-webkit-input-placeholder {
    color: #bbb !important;
  }
  input::-moz-placeholder {
    color: #bbb !important;
  }
  input:-ms-input-placeholder {
    color: #bbb !important;
  }
}
@media screen and (max-width: 720px){
  .login-container .el-input{
    width: 70%;
  }
}
</style>

<style lang="scss" scoped>
$bg:#262c37;
$dark_gray:#889aa4;
$light_gray:#eee;

.login-container {
  min-height: 100%;
  width: 100%;
  // background-image: url('~@/assets/login/loginBg.jpg');
  overflow: hidden;

  .login-form {
    position: relative;
    width: 720px;
    max-width: 100%;
    //height: 350px;
    height: 420px;
    margin: 200px auto 180px;
    overflow: hidden;
    background: #517b9c;
    border-radius: 8px;
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: #fff;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    line-height: 30px;
    font-size: 16px;
  }

  .title-container {
    width: 300px;
    height: 100%;
    position: relative;
    top: 0;
    padding-top: 90px;
    background: #396585;
    margin-right: -3px;
    display: inline-block;
    vertical-align: top;

    img{
      height: 80px;
      width: 80px;
      margin: 0 auto 20px;
      display: block;
    }

    .title {
      font-size: 22px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      padding: 0 16px;
      text-align: center;
      font-weight: bold;
    }
  }
  .icon-container{
    display: inline-block;
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
    .svg-icon{
      color: #fff;
      cursor: pointer;
    }
    .register{
      font-size: 14px;
    }
  }

  .form-container{
    width:420px;
    max-width: 60%;
    height: 100%;
    position: relative;
    top: 0;
    right: 0;
    margin-left: -3px;
    padding: 50px 60px;
    display: inline-block;
    .login-title{
      color: #fff;
      font-size: 19px;
      margin: 0 0 20px ;
    }
    .loin-btn-div {
      margin-bottom:20px;
    }
    .login-btn{
      width:100%;
      height: 34px;
      background: #295576;
      border: none;
      padding: 0;
      color: #fff;
      border-radius: 15px;
    }
    .login-btn-group {
      width:50%;
      height: 34px;
      padding: 0;
      color: #fff;
      background: #295576;
      border-color: #807f7f;
      &.left {
        border-width: 0 1px 0 0;
        border-radius: 15px 0 0 15px;
      }
      &.right {
        margin: 0;
        border-width: 0 0 0 1px;
        border-radius: 0 15px 15px 0;
      }
    }
  }
  .msg-text {
    width: 100%;
    margin-top: 5px;
    font-size: 14px;
    color: white;
    text-align: center;
  }
  .db-container{
    padding: 40px 60px;
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .find-back-password{
    color: #fff;
    float: right;
    text-decoration: none;
    font-size: 14px;
  }

  .copyright{
    margin-bottom: 10px;
    color: #fff;
    text-align: center;
  }

  >>>.el-dialog__body {
    .el-form{
      margin: 0 auto;
      .el-form-item{
        margin-bottom: 5px;
        border-bottom: 0;
        color: #666;
        &.is-error{
          margin-bottom: 16px;
        }
      }
      .el-form-item__label, label:not(.el-checkbox){
        line-height: 30px;
        color: #666;
      }
      .el-form-item__label{
        padding: 0 8px 0 0;
      }
      .el-form-item__content{
        line-height: 30px;
        .el-input__inner{
          line-height: 30px;
        }
        .el-form-item__error{
          padding-top: 2px;
        }
      }
    }
    .el-input{
      height: 30px;
      vertical-align: middle;
      .el-input__inner{
        height: 100%;
        line-height: 100%;
        background-color: #f5f5f5;
        border: 1px solid #aaa;
        color: #666;
        caret-color: #666;
        &:focus, &:hover{
          border: 1px solid #888;
        }
      }
      &.is-disabled .el-input__inner{
        background-color: #e4e7e9;
        border-color: #aaa;
        color: #888;
      }
    }
    .el-checkbox__label{
      color: #666;
    }
    .el-checkbox__inner{
      background-color: #fff;
      border: 1px solid #888;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #409eff;
      border-color: #409eff;
    }
    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #409eff;
    }
  }
}
.prompt{
  line-height: 36px;
  position: absolute;
  text-align: center;
  width: 100%;
  font-size: 18px;
}
.register-prompt{
  color: red;
}
.login-error-msg{
  position: absolute;
  width: 700px;
  margin: 0 0 0 -350px;
  top: 170px;
  left: 50%;
  text-align: center;
  line-height: 28px;
  font-size: 20px;
  color: red;
}
.trial-version{
  position: absolute;
  width: 0;
  height: 0;
  border-top: 100px solid red;
  border-right: 100px solid transparent;
  font-weight: bold;
  color: #eee;
  cursor: default;
  span{
    position: absolute;
    top: -95px;
    left: 12px;
  }
  p{
    width: 300px;
    position: absolute;
    top: -75px;
    left: 75px;
  }
}
</style>

