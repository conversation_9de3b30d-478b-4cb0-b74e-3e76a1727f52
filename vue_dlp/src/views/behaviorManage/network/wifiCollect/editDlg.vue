<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.wifiCollectStg')"
      :stg-code="201"
      :active-able="activeAble"
      :time-able="false"
      :rules="rules"
      :model="temp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-row >
          <el-col :span="12">
            <FormItem>
              <el-checkbox v-model="collectAll" :disabled="!formable" @change="collectAllChange">{{ $t('pages.collectTypeOptions1') }}</el-checkbox>
            </FormItem>
          </el-col>
          <el-col v-show="!temp.collectType" :span="12" >
            <Form ref="checkForm" :rules="checkRules" label-width="80px">
              <FormItem :label="$t('pages.groupType')" prop="groupId">
                <el-select v-model="temp.groupId" class="input-with-button" :disabled="!formable" filterable :placeholder="$t('text.select')">
                  <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="Number(item.dataId)"/>
                </el-select>
                <el-button :title="$t('pages.addType')" :disabled="!formable" class="editBtn" @click="handleWifiGroupCreate"><svg-icon icon-class="add" /></el-button>
              </FormItem>
            </Form>
          </el-col>
        </el-row>
        <el-divider v-if="temp.collectType" content-position="left">{{ $t('pages.wifiCollectRules') }}</el-divider>
        <div v-if="formable && temp.collectType">
          <el-button size="small" @click="handleCreateWifiRule()">
            {{ $t('button.insert') }}
          </el-button>
          <el-button size="small" :disabled="!ruleDeleteable" @click="handleDeleteWifiRule()">
            {{ $t('button.delete') }}
          </el-button>
        </div>
        <grid-table
          v-show="temp.collectType"
          ref="ruleGrid"
          :show-pager="false"
          :height="200"
          :multi-select="formable"
          :selectable="selectable"
          :col-model="ruleColModel"
          :row-datas="temp.rules"
          @selectionChangeEnd="ruleSelectionChangeEnd"
        />
      </template>
    </stg-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[ruleDialogStatus]"
      :visible.sync="wifiCollectRuleVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm2" :hide-required-asterisk="true" :model="tempW" :rules="tempWRules" label-width="80px" style="width: 100%; margin-top: 10px;">
        <FormItem :label="$t('table.wifiName')" prop="wifiName">
          <el-input v-model="tempW.wifiName" no-limit maxlength=""></el-input>
        </FormItem>
        <FormItem :label="$t('pages.matchType')" prop="matchType">
          <el-select v-model="tempW.matchType">
            <el-option :value="0" :label="$t('pages.matchTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.matchTypeOptions2')"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="20">
              <el-select v-model="tempW.groupId" filterable :placeholder="$t('text.select')" @change="groupChange">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="Number(item.dataId)"/>
              </el-select>
            </el-col>
            <el-col style="padding-top:1px" :span="4">
              <el-button style="padding-top: 1px; margin-bottom: 0;" :title="$t('pages.addType')" class="editBtn" @click="handleWifiGroupCreate"><svg-icon icon-class="add" /></el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="tempW.remark" type="textarea" :rows="3" resize="none" :maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="wifiCollectRuleVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.wifiGroup')"
      :group-tree-data="treeSelectNode"
      :add-func="createWifiGroup"
      :edit-valid-func="getWifiGroupByName"
      @addEnd="createNode"
    />
  </div>
</template>

<script>
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/behaviorManage/network/wifiCollect'
import { getGroupTreeNode, createWifiGroup, getWifiGroupByName } from '@/api/system/baseData/wifiLib'
import EditGroupDlg from '@/views/common/editGroupDlg'

export default {
  name: 'WifiCollectDlg',
  components: { EditGroupDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      // 进程系统类型：1-windows，2-linux，4-mac
      osType: 1,
      supportMd5: true,
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: '',
        name: '',
        remark: '',
        entityType: undefined,
        entityId: undefined,
        collectType: 1,
        groupId: undefined,
        rules: []
      },
      tempW: {},
      defaultTempW: { // WIFI信息表单字段
        id: '',
        wifiName: '',
        matchType: 0,
        groupId: undefined,
        remark: ''
      },
      ruleColModel: [
        { prop: 'wifiName', label: 'wifiName', width: '150', sort: true },
        { prop: 'matchType', label: 'matchType', width: '100', sort: true, sortOriginal: true, formatter: this.matchTypeFormatter },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, sortOriginal: true, formatter: this.groupFormatter },
        { prop: 'remark', label: 'remark', width: '150', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleUpdateWifiRule }
          ]
        }
      ],
      rowDatas: [],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        groupId: [{ required: true, validator: this.groupIdValidator, trigger: 'blur' }]
      },
      checkRules: {
        groupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'blur' }]
      },
      tempWRules: {
        wifiName: [{ required: true, message: this.$t('pages.wifiBlock_text2'), trigger: 'blur' }],
        groupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: 'blur' }]
      },
      propRuleId: undefined,
      ruleDeleteable: false,
      dialogStatus: '',
      ruleDialogStatus: '',
      timer: null,
      isAddExceptApp: false,
      wifiCollectRuleVisible: false,
      termId: undefined,
      termName: '',
      termWifis: [],  // 终端获取的WIFI
      inputWifis: [],  // 用户输入的WIFI
      loading: true,
      collectAll: false,
      treeSelectNode: [], // wifi分组树
      textMap: {
        update: this.i18nConcatText(this.$t('pages.wifiCollectRules'), 'update'),
        create: this.i18nConcatText(this.$t('pages.wifiCollectRules'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.wifiCollectRules'), 'delete')
      },
      matchTypeOptions: {
        0: this.$t('pages.matchTypeOptions1'),
        1: this.$t('pages.matchTypeOptions2')
      }
    }
  },
  computed: {
    allWifis() {
      return [...this.termWifis, ...this.inputWifis]
    },
    // 是否禁止
    isDoLimit() {
      const executeCode = this.temp.executeCode
      return executeCode.indexOf(1) > -1 || executeCode.indexOf(3) > -1
    },
    // 是否触发响应规则
    isDoResponse() {
      const executeCode = this.temp.executeCode
      return executeCode.indexOf(2) > -1 || executeCode.indexOf(4) > -1
    }
  },
  watch: {
    wifiAddVisible() {
      this.loading = false
    }
  },
  created() {
    this.resetTemp()
    this.resetTempW()
    this.loadWifiGroupTree()
  },
  activated() {
    this.loadWifiGroupTree().then(() => {
      if (this.wifiCollectRuleVisible && this.tempW.groupId) {
        if (!this.treeSelectNode.some(node => node.dataId == this.tempW.groupId)) {
          this.tempW.groupId = ''
        }
      }
    })
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    createWifiGroup,
    getWifiGroupByName,
    handleWifiGroupCreate() {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    groupChange(data) {
      if (data) {
        this.$refs['dataForm2'].clearValidate(['groupId'])
      }
    },
    createNode(data) {
      this.loadWifiGroupTree().then(() => {
        if (!this.temp.collectType) {
          this.$set(this.temp, 'groupId', data.id)
        } else {
          this.tempW.groupId = data.id
          this.$refs.dataForm2.clearValidate('groupId')
        }
      })
    },
    selectable(row, index) {
      if (this.formable == false) {
        return false
      } else {
        return true
      }
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.temp.rules = []
      this.collectAll = false
    },
    resetTempW() {
      this.tempW = Object.assign({}, this.defaultTempW)
    },
    closed() {
      this.resetTemp()
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      if (this.temp.ruleId) {
        this.propRuleId = this.temp.ruleId
      }
    },
    loadWifiGroupTree: function() {
      return getGroupTreeNode().then(respond => {
        this.treeSelectNode = respond.data
      })
    },
    ruleGrid() {
      return this.$refs['ruleGrid']
    },
    collectAllChange(data) {
      if (data) {
        this.temp.collectType = 0
      } else {
        this.temp.collectType = 1
      }
    },
    saveData() {
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          let warn = false
          if (this.tempW.id) {
            const index = this.temp.rules.findIndex(item => {
              return item.wifiName == this.tempW.wifiName && item.groupId == this.tempW.groupId && this.tempW.id !== item.id
            })
            if (index == -1) {
              this.temp.rules.forEach(r => {
                if (this.tempW.id === r.id) {
                  r = Object.assign(r, this.tempW)
                }
              })
            } else {
              warn = true
            }
          } else {
            const index = this.temp.rules.findIndex(item => {
              return item.wifiName == this.tempW.wifiName && item.groupId == this.tempW.groupId
            })
            if (index == -1) {
              this.tempW.id = this.tempW.id ? this.tempW.id : new Date().getTime()
              this.temp.rules.push(this.tempW)
            } else {
              warn = true
            }
          }
          if (warn) {
            this.$message({
              message: this.$t('pages.wifiCollectMsg1'),
              type: 'error',
              duration: 2000
            })
          } else {
            this.wifiCollectRuleVisible = false
          }
        }
      })
    },
    ruleSelectionChangeEnd(rowDatas) {
      this.ruleDeleteable = rowDatas && rowDatas.length > 0
    },
    handleDeleteWifiRule() {
      const toDeleteKeys = this.ruleGrid().getSelectedKeys()
      this.ruleGrid().deleteRowData(toDeleteKeys)
      const ruleArray = []
      this.temp.rules.forEach(rule => {
        if (!(toDeleteKeys.join(',').indexOf(rule.id) > -1)) {
          ruleArray.push(rule)
        }
      })
      this.temp.rules.splice(0, this.temp.rules.length, ...ruleArray)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.temp.entityType = this.entityNode.type
      this.temp.entityId = this.entityNode.dataId
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.dialogStatus = 'update'
      this.$refs['stgDlg'].show(row, this.formable)
      this.temp.entityType = row.entityType
      this.temp.entityId = row.entityId
    },
    handleCreateWifiRule(row) {
      this.resetTempW()
      this.loadWifiGroupTree()
      this.ruleDialogStatus = 'create'
      this.wifiCollectRuleVisible = true
      this.$nextTick(() => {
        this.$refs['ruleGrid'].clearSelection()
        this.$refs['dataForm2'].clearValidate()
      })
    },
    buttonFormatter(row) {
      return !this.formable
    },
    handleUpdateWifiRule(row) {
      this.resetTempW()
      this.tempW = Object.assign(this.tempW, row)
      this.ruleDialogStatus = 'update'
      this.wifiCollectRuleVisible = true
      this.$nextTick(() => {
        this.$refs['ruleGrid'].clearSelection()
        this.$refs['dataForm2'].clearValidate()
      })
    },
    formatRowData(rowData) {
      this.collectAll = !rowData.collectType
    },
    formatFormData(formData) {
      if (formData.collectType === 0) {
        formData.rules = null
      } else {
        formData.groupId = undefined
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$refs['checkForm'].validate(v => {
        if (v) {
          this.$notify({
            title: this.$t('text.success'),
            message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    validateForm(formData) {
      if (formData.collectType && formData.rules.length === 0) {
        this.$message({
          message: this.$t('pages.wifiCollectMsg2'),
          type: 'error'
        });
        return false
      }
      if (!formData.collectType && !this.temp.groupId) {
        this.$message({
          message: this.$t('pages.chooseGroup'),
          type: 'error'
        });
        return false
      }
      return true
    },
    macValidator(rule, value, callback) {
      const re = /^[A-Fa-f0-9]+$/
      const val = value.split('-').join('')
      if (val.length != 12 || !re.test(val)) {
        callback(new Error(this.$t('pages.ipMac_Msg7')))
      } else {
        callback()
      }
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    groupIdValidator(rule, value, callback) {
      if (!this.temp.collectType && !this.temp.groupId) {
        callback(new Error(this.$t('pages.validaGroup')))
      } else {
        callback()
      }
    },
    matchTypeFormatter(row, data) {
      return this.matchTypeOptions[row.matchType]
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.treeSelectNode, data)
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
