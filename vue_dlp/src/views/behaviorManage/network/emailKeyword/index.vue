<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="emailKeywordTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="800px"
      class="email-stg-dlg"
      @dragDialog="handleDrag"
      @closed="resetTemp()"
    >
      <div slot="title" class="el-dialog__title">
        {{ textMap[dialogStatus] }}
      </div>
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="92px"
        style="margin-left:6px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeTab" class="email-tab">
          <el-tab-pane name="keyword">
            <span slot="label">
              {{ $t('route.EmailKeyword') }}
              <el-tooltip effect="dark" :content="$t('pages.emailKeyWord_test9', {content: $t('route.EmailKeyword')} )" placement="bottom-start">
                <i class="el-icon-info" />
              </el-tooltip>
            </span>
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}
            </el-divider>
            <FormItem :label="$t('table.keyword')">
              <el-input v-model="temp.keyword" :disabled="!formable" type="textarea" rows="4" resize="none" maxlength="" @input="keywordChangeEnd"/>
              <div style="line-height: 24px; color: #2b7aac;">{{ $t('pages.emailKeyword_text1') }}</div>
            </FormItem>
            <FormItem :label="$t('pages.mailSender1')">
              <div :class="{'select-box': true}">
                <el-popover
                  placement="right"
                  width="350"
                  class="select-button"
                  trigger="click"
                  @after-leave="sendMailTreeHide"
                >
                  <tree-menu
                    ref="sendMailTree"
                    style="height: 320px"
                    :data="mailTreeData"
                    multiple
                    :icon-option="iconOption"
                    :checked-keys="sendCheckedTreeIds"
                    @node-click="handleNodeClick"
                    @check-change="sendCheckChange"
                  />
                  <el-button v-if="formable" slot="reference" class="choose-btn" @click="handleSendClick">{{ $t('pages.selectEmail') }}</el-button>
                </el-popover>
                <el-tag
                  v-for="(item, index) in temp.sendCheckMailIds"
                  :key="index"
                  class="select-tag"
                  type="info"
                  :closable="formable"
                  @close="sendCheckMailClose(item, temp.sendCheckMailIds, 'sendMailTree')"
                >
                  <span>{{ item.length > 1 ? item.substring(0,1) + '...' : item.label }}</span>
                </el-tag>
              </div>
            </FormItem>
            <FormItem :label="$t('pages.mailRecipient')">
              <div :class="{'select-box': true}">
                <el-popover
                  placement="right"
                  width="350"
                  class="select-button"
                  trigger="click"
                  @after-leave="receiverMailTreeHide"
                >
                  <tree-menu
                    ref="receiverMailTree"
                    style="height: 320px"
                    :data="mailTreeData"
                    multiple
                    :icon-option="iconOption"
                    :checked-keys="receiverCheckedTreeIds"
                    @node-click="handleNodeClick"
                    @check-change="receiverCheckChange"
                  />
                  <el-button v-if="formable" slot="reference" class="choose-btn" @click="handleReceiverClick">{{ $t('pages.selectEmail') }}</el-button>
                </el-popover>
                <el-tag
                  v-for="(item, index) in temp.receiverCheckMailIds"
                  :key="index"
                  class="select-tag"
                  type="info"
                  :closable="formable"
                  @close="sendCheckMailClose(item, temp.receiverCheckMailIds, 'receiverMailTree')"
                >
                  <span>{{ item.length > 1 ? item.substring(0,1) + '...' : item.label }}</span>
                </el-tag>
              </div>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label="" label-width="17px">
              <el-row>
                <el-col :span="24">
                  <el-radio-group v-model="temp.executeRule" :disabled="!formable" class="execution-rule" @change="changeExecuteRule()">
                    <el-radio :label="0">{{ $t('pages.emailKeyword_text6_1') }}
                      <el-tooltip effect="dark" placement="bottom-start">
                        <div slot="content">
                          {{ $t('pages.emailKeyword_text5') }}
                        </div>
                        <i class="el-icon-info" />
                      </el-tooltip>
                    </el-radio>
                    <el-radio :label="1">{{ $t('pages.emailKeyword_text7_1') }}
                      <el-tooltip effect="dark" placement="bottom-start">
                        <div slot="content">
                          {{ $t('pages.emailKeyword_text8') }}
                        </div>
                        <i class="el-icon-info" />
                      </el-tooltip>
                    </el-radio>
                  </el-radio-group>
                </el-col>
                <el-col :span="24">
                  <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.executeRuleTip', {info: $t('pages.limit'), info1: $t('pages.emailContent')}) }}</label>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      <i18n path="pages.withinDetectionRuleTip">
                        <span slot="info">{{ $t('pages.allowEmailContentOutgoing') }}</span>
                      </i18n>
                      <br/>
                      <i18n path="pages.outsideDetectionRuleTip1">
                        <span slot="info">{{ $t('pages.forbidEmailContentOutgoing') }}</span>
                      </i18n>
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-col>
              </el-row>
            </FormItem>
            <div style="margin-top: 30px">
              <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
              <FormItem label-width="0px" prop="executeCode">
                <el-checkbox-group v-model="temp.executeCode" :disabled="!formable" @change="changeExecuteCode($event)">
                  <el-checkbox v-if="dialogStatus == 'create'" :label="temp.executeRule === 0 && temp.blockMail ? 1 : 3" class="prohibit">{{ $t('pages.forbiddenSendMail') }}</el-checkbox>
                  <el-checkbox v-if="dialogStatus == 'update'" :label="temp.executeRule === 0 && temp.blockMail ? 1 : 3" class="prohibit">{{ $t('pages.forbiddenSendMail') }}</el-checkbox>
                </el-checkbox-group>
                <ResponseContent
                  ref="resContent"
                  style="line-height: 15px !important;"
                  :select-style="{ 'margin-top': 0 }"
                  :show-select="true"
                  :editable="formable"
                  read-only
                  :prop-check-rule="isDoResponse(temp)"
                  :show-check-rule="true"
                  :prop-rule-id="temp.ruleId"
                  @getRuleId="getRuleId"
                  @ruleIsCheck="ruleIsCheck"
                  @validate="(val) => { responseValidate = val }"
                />
              </FormItem>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('route.emailRecordLimit')" name="backup">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <FormItem :label="$t('pages.mailSender1')">
              <div :class="{'select-box': true}">
                <el-popover
                  placement="right"
                  width="350"
                  class="select-button"
                  trigger="click"
                  @after-leave="sendMailRecordTreeHide"
                >
                  <tree-menu
                    ref="sendMailRecordTree"
                    style="height: 320px"
                    :data="mailTreeData"
                    multiple
                    :icon-option="iconOption"
                    :checked-keys="sendCheckedRecordTreeIds"
                    @node-click="handleNodeClick"
                    @check-change="sendCheckRecordChange"
                  />
                  <el-button v-if="formable" slot="reference" class="choose-btn" @click="handleSendRecordClick">{{ $t('pages.selectEmail') }}</el-button>
                </el-popover>
                <el-tag
                  v-for="(item, index) in temp.sendCheckRecordMailIds"
                  :key="index"
                  class="select-tag"
                  type="info"
                  :closable="formable"
                  @close="sendCheckMailClose(item, temp.sendCheckRecordMailIds)"
                >
                  <span>{{ item.length > 1 ? item.substring(0,1) + '...' : item.label }}</span>
                </el-tag>
              </div>
            </FormItem>
            <FormItem :label="$t('pages.mailRecipient')">
              <div :class="{'select-box': true}">
                <el-popover
                  placement="right"
                  width="350"
                  class="select-button"
                  trigger="click"
                  @after-leave="receiverMailRecordTreeHide"
                >
                  <tree-menu
                    ref="receiverMailRecordTree"
                    style="height: 320px"
                    :data="mailTreeData"
                    multiple
                    :icon-option="iconOption"
                    :checked-keys="receiverCheckedRecordTreeIds"
                    @node-click="handleNodeClick"
                    @check-change="receiverCheckRecordChange"
                  />
                  <el-button v-if="formable" slot="reference" class="choose-btn" @click="handleReceiverRecordClick">{{ $t('pages.selectEmail') }}</el-button>
                </el-popover>
                <el-tag
                  v-for="(item, index) in temp.receiverCheckRecordMailIds"
                  :key="index"
                  class="select-tag"
                  type="info"
                  :closable="formable"
                  @close="sendCheckMailClose(item, temp.receiverCheckRecordMailIds)"
                >
                  <span>{{ item.length > 1 ? item.substring(0,1) + '...' : item.label }}</span>
                </el-tag>
              </div>
            </FormItem>

            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label="" label-width="17px">
              <el-row>
                <el-col :span="24">
                  <el-radio-group v-model="temp.monitorWay" :disabled="!formable">
                    <el-radio :label="1">{{ $t('pages.emailRecordInDetectionRule') }}</el-radio>
                    <el-radio :label="0">{{ $t('pages.emailRecordOutDetectionRule') }}</el-radio>
                  </el-radio-group>
                </el-col>
              </el-row>
              <!--            <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.executeRuleTip', {info: $t('pages.identifierRule_regular'), info1: $t('pages.successSendEmail')}) }}</label>-->
              <!--            <el-tooltip effect="dark" placement="bottom-start">-->
              <!--              <div slot="content">-->
              <!--                <i18n path="pages.outsideDetectionRuleTip">-->
              <!--                  <span slot="info">{{ $t('pages.allowAllEmailRecord') }}</span>-->
              <!--                </i18n>-->
              <!--                <br/>-->
              <!--                <i18n path="pages.withinDetectionRuleTip">-->
              <!--                  <span slot="info">{{ $t('pages.forbidAllEmailRecord') }}</span>-->
              <!--                </i18n>-->
              <!--              </div>-->
              <!--              <i class="el-icon-info"/>-->
              <!--            </el-tooltip>-->
            </FormItem>

            <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
            <FormItem label-width="20px">
              <el-checkbox v-model="temp.isRecord" :disabled="!formable" :true-label="1" :false-label="0" @change="recordChange">{{ $t('pages.recordEmailLog') }}</el-checkbox>
            </FormItem>
            <FormItem class="backup-limit" label-width="20px" prop="backupSize">
              <el-checkbox v-model="temp.isBackup" :disabled="!formable || !temp.isRecord" :true-label="1" :false-label="0" @change="backupChange">
                <i18n path="pages.emailRecordLimitMsg">
                  <file-size-input
                    ref="fileSize"
                    slot="size"
                    :disabled="!formable || !temp.isRecord || !temp.isBackup"
                    :max="10240 * 1024"
                    :maxlength="10"
                    :display-units="{ KB: 1, MB: 1024 }"
                    :auto-validate="false"
                    @change="handleFileSizeChange"
                  />
                </i18n>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content" style="width: 480px;">{{ $t('pages.emailRecordLimitTips') }}</div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-checkbox>
            </FormItem>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <link-button v-if="formable" btn-type="primary" btn-style="float: left" :menu-code="'A52'" :link-url="'/system/baseData/mailLibrary'" :btn-text="$t('pages.maintainMail')"/>
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importEmailKeywordBlockStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import {
  fetchList, getEmailKeywordByName, createEmailKeyword, updateEmailKeyword, deleteEmailKeyword
} from '@/api/behaviorManage/network/emailKeyword'
import { getTreeNode } from '@/api/system/baseData/mailLibrary'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy, timeIdValidator } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import { isSameTimestamp, initTimestamp } from '@/utils'
import FileSizeInput from '@/components/DownloadManager/setting/fileSizeInput'

export default {
  name: 'EmailKeyword',
  components: { ResponseContent, ImportStg, FileSizeInput },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 17,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'keyword', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { prop: 'createdTime', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      activeTab: 'keyword',
      mailTreeData: [],
      mailTreeMap: null,
      sendCheckedTreeIds: [],
      receiverCheckedTreeIds: [],
      sendCheckedRecordTreeIds: [],
      receiverCheckedRecordTreeIds: [],
      iconOption: { G: 'group', '': 'email2' },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        keyword: '',
        isRecord: 1,
        isBackup: 1,
        backupSize: 20,
        backupUnit: 'MB',
        timeId: 1,
        entityType: undefined,
        entityId: undefined,
        ruleId: null,
        executeRule: 0,
        executeCode: [],
        mailAccount: [],
        sendCheckMailIds: [],
        receiverCheckMailIds: [],
        blockMail: undefined,
        alarmWork: undefined,
        monitorWay: 0,
        sendCheckRecordMailIds: [],
        receiverCheckRecordMailIds: [],
        mailRecordAccount: []
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.emailKeywordStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.emailKeywordStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.timeRequired'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ],
        executeCode: [
          { required: true, validator: this.executeCodeValidator, trigger: 'blur' }
        ],
        backupSize: [
          { validator: this.backupSizeValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      codeStatus: undefined
      // tempExecuteCode: []
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    gridTable() {
      return this.$refs['emailKeywordTable']
    }
  },
  created() {
    initTimestamp(this)
    this.resetTemp()
    this.loadMailTree()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
    if (!isSameTimestamp(this, 'MailLibrary')) {
      this.loadMailTree()
    }
  },
  methods: {
    changeExecuteRule() {
      if (this.dialogStatus == 'update') {
        // 修改状态下
        if (this.codeStatus) {
          if (!this.temp.executeCode.includes(1)) {
            this.temp.executeCode.push(1)
          }
          if (!this.temp.executeCode.includes(3)) {
            this.temp.executeCode.push(3)
          }
        } else {
          if (this.temp.executeCode.includes(1)) {
            this.temp.executeCode.splice(this.temp.executeCode.indexOf(1), 1)
          }
          if (this.temp.executeCode.includes(3)) {
            this.temp.executeCode.splice(this.temp.executeCode.indexOf(3), 1)
          }
        }
      }
    },
    changeExecuteCode(event) {
      if (this.dialogStatus == 'update') {
        // 修改状态下
        if (this.codeStatus) {
          this.codeStatus = false
        } else {
          this.codeStatus = true
        }
      }
      this.$refs.dataForm.validateField('executeCode')
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return fetchList(searchQuery).then(respond => {
        respond.data.items.forEach(item => {
          if (item.isRecord == null) {
            item.isRecord = 1
            item.backupSize = this.defaultTemp.backupSize
            item.backupUnit = this.defaultTemp.backupUnit
          }
          if (item.backupSize == null && item.emailRecordLimit) {
            const size = item.emailRecordLimit.webEmlFileBackupSize
            if (size >= 1024) {
              const mb = Math.floor(size / 1024)
              if (mb * 1024 === size) {
                item.backupSize = mb
                item.backupUnit = 'MB'
                return
              }
            }
            item.backupSize = size
            item.backupUnit = 'KB'
          }
        })
        return respond
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    loadMailTree() {
      getTreeNode().then(respond => {
        this.mailTreeData = JSON.parse(JSON.stringify(respond.data))
        this.mailTreeMap = this.mailTreeData.reduce((map, cur) => {
          if (cur.children) {
            cur.children.reduce((data, cur) => {
              data[cur.id] = cur
              return data
            }, map)
          }
          return map
        }, {})
        this.updateSelectedData()
      })
    },
    handleNodeClick(data, node, el) {
    },
    handleSendClick() {
      if (this.temp.sendCheckMailIds) {
        this.sendCheckedTreeIds = this.temp.sendCheckMailIds.map(mail => mail.id)
      }
    },
    handleSendRecordClick() {
      if (this.temp.sendCheckRecordMailIds) {
        this.sendCheckedRecordTreeIds = this.temp.sendCheckRecordMailIds.map(mail => mail.id)
      }
    },
    updateSelectedData() {
      this.$set(this.temp, 'sendCheckMailIds', this.filterSelectedData(this.temp.sendCheckMailIds))
      this.$set(this.temp, 'receiverCheckMailIds', this.filterSelectedData(this.temp.receiverCheckMailIds))
      this.$set(this.temp, 'sendCheckRecordMailIds', this.filterSelectedData(this.temp.sendCheckRecordMailIds))
      this.$set(this.temp, 'receiverCheckRecordMailIds', this.filterSelectedData(this.temp.receiverCheckRecordMailIds))
    },
    filterSelectedData(data) {
      if (this.mailTreeMap) {
        return data ? data.map(d => this.mailTreeMap[d.id]).filter(item => item !== undefined) : []
      }
      return data
    },
    handleReceiverClick() {
      if (this.temp.receiverCheckMailIds) {
        this.receiverCheckedTreeIds = this.temp.receiverCheckMailIds.map(mail => mail.id)
      }
    },
    handleReceiverRecordClick() {
      if (this.temp.receiverCheckRecordMailIds) {
        this.receiverCheckedRecordTreeIds = this.temp.receiverCheckRecordMailIds.map(mail => mail.id)
      }
    },
    sendCheckChange(keys, datas) {
      if (datas) {
        const data = datas.filter(data => data.id.indexOf('G') < 0)
        this.$set(this.temp, 'sendCheckMailIds', data)
      }
    },
    receiverCheckChange(keys, datas) {
      if (datas) {
        const data = datas.filter(data => data.id.indexOf('G') < 0)
        this.$set(this.temp, 'receiverCheckMailIds', data)
      }
    },
    sendCheckRecordChange(keys, datas) {
      if (datas) {
        const data = datas.filter(data => data.id.indexOf('G') < 0)
        this.$set(this.temp, 'sendCheckRecordMailIds', data)
      }
    },
    receiverCheckRecordChange(keys, datas) {
      if (datas) {
        const data = datas.filter(data => data.id.indexOf('G') < 0)
        this.$set(this.temp, 'receiverCheckRecordMailIds', data)
      }
    },
    sendMailTreeHide() {
      this.$refs.sendMailTree && this.$refs.sendMailTree.clearFilter()
    },
    receiverMailTreeHide() {
      this.$refs.receiverMailTree && this.$refs.receiverMailTree.clearFilter()
    },
    sendMailRecordTreeHide() {
      this.$refs.sendMailRecordTree && this.$refs.sendMailRecordTree.clearFilter()
    },
    receiverMailRecordTreeHide() {
      this.$refs.receiverMailRecordTree && this.$refs.receiverMailRecordTree.clearFilter()
    },
    keywordChangeEnd(val) {
      const words = val.split('\n')
      if (words) {
        for (let i = 0; i < words.length; i++) {
          if (words[i].length > 64) {
            words[i] = words[i].substring(0, 64)
          }
        }
        this.temp.keyword = words.join('\n')
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.temp.ruleId = null
      this.activeTab = 'keyword'
    },
    // 是否禁止
    isDoLimit(data) {
      const executeCode = data.executeCode
      return executeCode.includes(1) || executeCode.includes(3)
    },
    // 是否触发响应规则
    isDoResponse(data) {
      const executeCode = data.executeCode
      return executeCode.includes(2) || executeCode.includes(4)
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.$refs['fileSize'].setSizeUnit(this.temp.backupSize, this.temp.backupUnit)
      })
    },
    handleUpdate: function(row) {
      row.executeCode = []
      if (row.blockMail && row.executeRule === 0) {
        row.executeCode.push(1)
      } else if (row.blockMail && row.executeRule === 1) {
        row.executeCode.push(3)
      }
      if (row.ruleId && row.executeRule === 0) {
        row.executeCode.push(2)
      } else if (row.ruleId && row.executeRule === 1) {
        row.executeCode.push(4)
      }
      if (row.executeCode.length == 0 || row.blockMail == undefined) {
        this.$set(row, 'executeRule', 0)
        this.$set(row, 'executeCode', [1])
      }
      this.resetTemp()
      const backupSize = !row.isRecord ? undefined : row.backupSize
      const isBackup = !backupSize ? 0 : 1
      Object.assign(this.temp, row, { isBackup, backupSize }) // copy obj
      if (this.temp.blockMail == 1) {
        this.codeStatus = true
      } else {
        this.codeStatus = false
      }
      this.updateSelectedData()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.$refs['fileSize'].setSizeUnit(this.temp.backupSize, this.temp.backupUnit)
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    formatFormData(formData) {
      if (!this.isDoResponse(formData)) {
        formData.ruleId = null
      }
      formData.blockMail = this.isDoLimit(formData) ? 1 : 0
      formData.alarmWork = this.isDoResponse(formData) ? 1 : 0
      const mailAccount = [
        ...this.formatMail(formData.sendCheckMailIds, 2),
        ...this.formatMail(formData.receiverCheckMailIds, 1)
      ]
      this.$set(formData, 'mailAccount', mailAccount)

      const mailRecordAccount = [
        ...this.formatMail(formData.sendCheckRecordMailIds, 2),
        ...this.formatMail(formData.receiverCheckRecordMailIds, 1)
      ]
      this.$set(formData, 'mailRecordAccount', mailRecordAccount)
      if (!formData.isBackup) {
        formData.backupSize = 0
      }
    },
    formatMail(data, type) {
      const mailAccount = []
      if (data && data.length) {
        data.forEach(mail => {
          mailAccount.push({ mailType: type, mailId: parseInt(mail.id) })
        })
      }
      return mailAccount
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.responseValidate) {
          const tempData = Object.assign({}, this.temp)
          this.formatFormData(tempData)
          createEmailKeyword(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.responseValidate) {
          if (!this.codeStatus) {
            if (this.temp.executeCode.includes(1)) {
              this.temp.executeCode.splice(this.temp.executeCode.indexOf(1), 1)
            }
            if (this.temp.executeCode.includes(3)) {
              this.temp.executeCode.splice(this.temp.executeCode.indexOf(3), 1)
            }
          }
          const tempData = Object.assign({}, this.temp)
          this.formatFormData(tempData)
          updateEmailKeyword(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteEmailKeyword({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyNameValidator(rule, value, callback) {
      getEmailKeywordByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    executeCodeValidator(rule, value, callback) {
      console.log('executeCode %o', this.temp.executeCode)
      if (this.temp.executeCode.length === 0) {
        callback(new Error(this.$t('pages.install_Msg51')))
      } else {
        callback()
      }
    },
    backupSizeValidator(rule, value, callback) {
      if (this.temp && this.temp.isRecord && this.temp.isBackup && !value) {
        callback(new Error(this.$t('pages.required1')))
      } else {
        callback()
      }
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    strategyFormatter: function(row, data) {
      const msgArr = []
      const controlArr = []
      const checkRule = []
      if (row.keyword) {
        checkRule.push(this.$t('pages.keywords') + '：' + row.keywordList.map(item => item.keyword))
      }
      if (row.sendNames) {
        checkRule.push(this.$t('pages.mailSender1') + '：' + row.sendNames)
      }
      if (row.receiverNames) {
        checkRule.push(this.$t('pages.mailRecipient') + '：' + row.receiverNames)
      }
      if (checkRule.length > 0) {
        controlArr.push(this.$t('table.detectionRules') + '：' + checkRule.join('、'))
      }
      if (row.executeRule == 0) {
        controlArr.push(this.$t('pages.executionRules') + '：' + this.$t('pages.emailKeyword_text6'))
      } else if (row.executeRule == 1) {
        controlArr.push(this.$t('pages.executionRules') + '：' + this.$t('pages.emailKeyword_text7'))
      }
      const respondRule = []
      if (row.blockMail) {
        respondRule.push(this.$t('pages.forbiddenSendMail'))
      }
      if (row.ruleId) {
        respondRule.push(this.$t('pages.triggerResponseRule'))
      }
      controlArr.push(this.$t('pages.responseRule') + '：' + respondRule.join('、'))
      msgArr.push(`${this.$t('route.EmailKeyword')}: { ${controlArr.join(', ')} }`)

      const recordArr = []
      const recordCheckRule = []
      if (row.sendRecordNames) {
        recordCheckRule.push(this.$t('pages.mailSender1') + '：' + row.sendRecordNames)
      }
      if (row.receiverRecordNames) {
        recordCheckRule.push(this.$t('pages.mailRecipient') + '：' + row.receiverRecordNames)
      }
      if (recordCheckRule.length > 0) {
        recordArr.push(this.$t('table.detectionRules') + '：' + recordCheckRule.join('、'))
      }
      if (row.monitorWay == 0) {
        recordArr.push(this.$t('pages.executionRules') + '：' + this.$t('pages.emailRecordOutDetectionRule'))
      } else if (row.monitorWay == 1) {
        recordArr.push(this.$t('pages.executionRules') + '：' + this.$t('pages.emailRecordInDetectionRule'))
      }
      const recordRespondRule = []
      if (row.isRecord) {
        recordRespondRule.push(this.$t('pages.recordEmailLog'))
      } else {
        recordRespondRule.push(this.$t('pages.noRecordEmailLog'))
      }
      if (row.backupSize) {
        recordRespondRule.push(this.$t('pages.emailRecordLimitMsg', { size: row.backupSize + ' ' + row.backupUnit }))
      }
      recordArr.push(this.$t('pages.responseRule') + '：' + recordRespondRule.join('、'))
      msgArr.push(`${this.$t('route.emailRecordLimit')}: { ${recordArr.join(', ')} }`)
      return msgArr.join('; ')
    },
    importSuccess() {
      this.handleFilter()
    },
    sendCheckMailClose(tag, mailIds, ref, event) {
      mailIds = mailIds || []
      const index = mailIds.findIndex(item => { return item.id === tag.id });
      index > -1 && mailIds.splice(index, 1)
    },
    ruleIsCheck(data) {
      if (data === 1) {
        this.temp.executeCode.unshift(this.temp.executeRule === 0 ? 2 : 4);
      } else {
        const executeCode = this.temp.executeCode.filter(code => code != 2 && code != 4)
        this.$set(this.temp, 'executeCode', executeCode)
      }
      this.$refs.dataForm.validateField('executeCode')
    },
    recordChange(val) {
      if (!val && this.temp.isBackup === 1) {
        this.backupChange(0)
      }
    },
    backupChange(val) {
      if (!val) {
        const dataForm = this.$refs['dataForm']
        if (dataForm) {
          dataForm.clearValidate('backupSize')
        }
      }
      this.temp.isBackup = val
    },
    handleFileSizeChange() {
      const { size: backupSize, unit: backupUnit } = this.$refs['fileSize'].getSizeUnit()
      Object.assign(this.temp, { backupSize, backupUnit })
    }
  }
}
</script>
<style lang="scss" scoped>
.email-tab {
  .el-tab-pane {
    padding: 10px;
  }
}
.execution-rule {
  width: 100%;
  >>>.el-radio {
    width: 100%;
  }
  >>>.el-radio__label {
    height: 30px;
    width: 100%;
    display: inline-block;
    vertical-align: bottom;
  }
  .ellipsis {
    max-width: calc(100% - 20px);
    display: inline-block;
  }
  .el-tooltip {
    margin-left: 3px;
    position: absolute;
    top: 8px;
  }
}
.prohibit >>> .el-checkbox__inner {
  margin-left: 17px;
}
.backup-limit>>>.el-form-item__content .el-form-item__error {
  margin-left: 198px;
}
.email-stg-dlg {
  >>>.first-divider {
    margin-top: 10px;
  }
}
</style>
