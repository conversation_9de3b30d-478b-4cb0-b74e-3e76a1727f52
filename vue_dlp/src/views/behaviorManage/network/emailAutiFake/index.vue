<template>
  <hook-process-auti-fake :business-type="businessType" :req-route="reqRoute"/>
</template>

<script>
import HookProcessAutiFake from '@/views/common/hookProcessAutiFake'

export default {
  name: 'EmailAutiFake',
  components: { HookProcessAutiFake },
  data() {
    return {
      businessType: 3,
      reqRoute: 'emailAutiFake'
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>

</style>
