<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="740px"
      @closed="resetTemp"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 670px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeName">
          <el-tab-pane name="control" :label="$t('pages.aiFileControlSetting')">
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="18px" prop="action">
              <!--通过AI上传文件时触发响应规则-->
              {{ $t('pages.aiFileTriggerResponseRule') }}
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="18px">
              <el-row>
                <el-col :span="16">
                  <el-checkbox-group v-model="approvalTypeList" style="display: inline-block" :disabled="!formable" @change="handleApprovalTypeChange">
                    <el-checkbox :label="1">{{ $t('pages.burnMsg5') }}</el-checkbox>
                    <el-checkbox :disabled="allowSendOut" :label="2">{{ $t('pages.burnMsg7') }}</el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
            </FormItem>
            <ResponseContent
              :status="dialogStatus"
              :select-style="{ 'margin-top': '5px' }"
              :show-select="true"
              :rule-type-name="$t('table.violationName')"
              :editable="formable && !allowSendOut"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="temp.ruleId"
              @ruleIsCheck="getRuleIsCheck"
              @getRuleId="getRuleId"
              @validate="getRuleIsValidate"
            />
            <el-divider content-position="left">{{ $t('pages.exceptRule') }}</el-divider>
            <FormItem class="except-limit" label-width="18px" prop="allowFileSize">
              <el-checkbox v-model="temp.isExcept" :disabled="!formable || allowSendOut" @change="exceptChange">
                <i18n path="pages.fileSmallerThanAllowSend">
                  <el-input-number v-show="!temp.isExcept" slot="input" style="width:110px;height: 30px;" disabled size="mini"/>
                  <el-input
                    v-show="temp.isExcept"
                    slot="input"
                    v-model="temp.allowFileSize"
                    style="width:110px;height: 30px;"
                    :disabled="!formable"
                    size="mini"
                    @input="handleSizeInput"
                  />
                  <el-select slot="unit" v-model="temp.fileSizeUnit" :disabled="!formable || !temp.isExcept" style="width: 80px;height: 30px;" @change="handleUnitChange">
                    <el-option :key="0" label="MB" :value="0"/>
                    <el-option :key="1" label="KB" :value="1"/>
                  </el-select>
                </i18n>
              </el-checkbox>
            </FormItem>
          </el-tab-pane>
          <el-tab-pane name="record" :label="$t('pages.aiFileRecordSetting')">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <FormItem :label="$t('pages.aiModel')" label-width="65px">
              <el-select
                v-model="selectedAiModels"
                class="multi-select"
                multiple
                clearable
                size="mini"
                :disabled="!formable"
                :placeholder="$t('pages.aiModelAll')"
              >
                <el-option v-for="item in aiModelDict" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="8px">
              <el-radio-group v-model="temp.exceptRule" :disabled="!formable">
                <!--终端监测到检测规则之内的AI模型时触发响应规则-->
                <el-radio :label="0">{{ $t('pages.aiModelLimitInDetectionRule') }}</el-radio>
                <!--终端监测到检测规则之外的AI模型时触发响应规则-->
                <el-radio :label="1">{{ $t('pages.aiModelLimitOutDetectionRule') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="18px">
              <el-checkbox v-model="temp.recordLog" :disabled="!formable" :true-label="1" :false-label="0" @change="recordLogChange">
                {{ $t('pages.aiFileRecordAudit') }}
              </el-checkbox>
            </FormItem>
            <FormItem class="backup-limit" label-width="18px" prop="fileBackUpLimit">
              <el-checkbox v-model="temp.isBackup" :disabled="!formable || !temp.recordLog" :true-label="1" :false-label="0" @change="needBackupChange">
                <i18n path="pages.blueTooth_Msg1">
                  <el-input-number
                    slot="size"
                    v-model="temp.fileBackUpLimit"
                    :disabled="!temp.isBackup || !formable"
                    :controls="false"
                    :min="1"
                    :max="10240"
                    step-strictly
                    style="width: 80px;"
                    size="mini"
                  />
                </i18n>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.ftpControl_text3') }}<br/>
                    {{ $t('pages.ftpControl_text4') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-checkbox>
            </FormItem>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importBrowserFileStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import ImportStg from '@/views/common/importStg'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils';
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter';
import { timeIdValidator, validatePolicy } from '@/utils/validate';
import { exportStg } from '@/api/stgCommon';
import { getStrategyList, getByName, createStrategy, updateStrategy, deleteStrategy } from '@/api/behaviorManage/network/aiUploadFile';
import { getAiNameDict } from '@/utils/dictionary';

export default {
  name: 'AiUploadFile',
  components: { ResponseContent, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 286,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: undefined,
        entityId: undefined,
        timeId: 1,
        fileOutApprovalType: 0,
        isExcept: false, // 是否触发例外规则
        allowFileSize: 20,
        fileSizeUnit: 0,
        isAlarm: 0, // 是否触发违规响应规则
        ruleId: undefined, // 违规响应规则ID
        exceptRule: 0,
        recordLog: 0, // 是否记录日志
        isBackup: 0, // 是否备份
        fileBackUpLimit: 20 // 备份文件阈值（MB）
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.aiFileStrategy'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.aiFileStrategy'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ],
        allowFileSize: [
          { validator: this.allowFileSizeValidator, trigger: 'blur' }
        ],
        fileBackUpLimit: [
          { validator: this.fileBackUpLimitValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      approvalTypeList: [],
      aiModelDict: [],
      selectedAiModels: [],
      validRuleId: false,
      activeName: 'control'   // 策略tabs页
    }
  },
  computed: {
    allowSendOut() {
      return this.approvalTypeList.indexOf(1) < 0
    },
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.aiModelDict = getAiNameDict()
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp(source) {
      this.temp = Object.assign({}, this.defaultTemp, source)
      this.approvalTypeList.splice(0)
      this.selectedAiModels.splice(0)
      this.activeName = 'control'
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      const tempData = JSON.parse(JSON.stringify(row))
      const aiModelList = tempData.aiModelList || []
      delete tempData.aiModelList
      this.resetTemp(tempData)
      if (row.fileOutApprovalType > 0) {
        this.approvalTypeList = this.numToList(row.fileOutApprovalType, 4)
      }
      if (aiModelList.length > 0 && aiModelList[0].aiName > 0) {
        aiModelList.forEach((item) => {
          this.selectedAiModels.push(item.aiName)
        })
      }
      if (row.fileBackUpLimit === 0) {
        this.temp.fileBackUpLimit = 20
      } else {
        this.temp.isBackup = 1
      }
      if (row.allowFileSize > 0) {
        this.temp.isExcept = true
        // 存储例外规则中的文件大小时值存的是kb
        if (this.temp.fileSizeUnit === 0) {
          this.temp.allowFileSize = row.allowFileSize / 1024
        }
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    importSuccess() {
      this.handleFilter()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    nameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    allowFileSizeValidator(rule, value, callback) {
      if (this.temp.isExcept) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.temp.isBackup === 1) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else if (value > 10240) {
          return callback(new Error(this.$t('pages.blueTooth_Msg4')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const recordMsgArr = []
      if (row.fileOutApprovalType > 0) {
        const approvalTypes = this.numToList(row.fileOutApprovalType, 4)
        if (approvalTypes.includes(1)) {
          recordMsgArr.push(this.$t('pages.burnMsg5'))
        }
        if (approvalTypes.includes(2)) {
          recordMsgArr.push(this.$t('pages.burnMsg7'))
        }
        if (row.ruleId) {
          // 触发响应规则
          recordMsgArr.push(this.$t('pages.triggerResponseRule'))
        }
      }
      if (row.allowFileSize > 0) {
        const text = this.$t('pages.fileSmallerThanAllowSend', {
          input: row.fileSizeUnit === 0 ? row.allowFileSize / 1024 : row.allowFileSize,
          unit: row.fileSizeUnit === 0 ? 'MB' : 'KB'
        })
        recordMsgArr.push(text)
      }
      if (row.recordLog) {
        recordMsgArr.push(this.$t('pages.aiFileRecordAudit'))
      }
      if (row.fileBackUpLimit) {
        // 文件备份限制{row.fileBackUpLimit}MB
        recordMsgArr.push(this.$t('pages.burnMsg13', { BackupSize: row.fileBackUpLimit }))
      }
      return recordMsgArr.join(',')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    submit() {
      this.handleFilter()
    },
    formatFormData() {
      const tempData = Object.assign({}, this.temp)
      tempData.fileOutApprovalType = this.getSum(this.approvalTypeList)
      if (!tempData.isAlarm) {
        tempData.ruleId = undefined
      }
      if (tempData.isExcept) {
        if (tempData.fileSizeUnit === 0) {
          tempData.allowFileSize = tempData.allowFileSize * 1024
        }
      } else {
        tempData.allowFileSize = undefined
        tempData.fileSizeUnit = undefined
      }
      if (this.selectedAiModels.length > 0) {
        tempData.aiModelList = this.selectedAiModels.map(aiName => ({ aiName }))
      } else {
        tempData.aiModelList = [{ aiName: 0 }]
      }
      if (!tempData.isBackup && tempData.fileBackUpLimit > 0) {
        tempData.fileBackUpLimit = 0
      }
      return tempData
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid, fields) => {
        if (valid && this.validRuleId) {
          createStrategy(this.formatFormData()).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid, fields) => {
        if (valid && this.validRuleId) {
          updateStrategy(this.formatFormData()).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleApprovalTypeChange(value) {
      if (this.allowSendOut) {
        this.temp.isAlarm = 0
        this.temp.isExcept = false
        if (value.indexOf(2) > -1) {
          this.approvalTypeList.splice(0)
        }
      }
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    getRuleIsValidate(value) {
      this.validRuleId = value
    },
    exceptChange(val) {
      if (!val) {
        this.$refs['dataForm'].clearValidate('allowFileSize')
      }
    },
    handleSizeInput(value) {
      const sizeStr = value.trim().replace(/^0|[^0-9]/g, '')
      this.temp.allowFileSize = sizeStr === '' ? undefined : parseInt(sizeStr)
      if (this.temp.fileSizeUnit === 1 && this.temp.allowFileSize > 10485760) {
        this.temp.allowFileSize = 10485760
      } else if (this.temp.fileSizeUnit === 0 && this.temp.allowFileSize > 10240) {
        this.temp.allowFileSize = 10240
      }
    },
    handleUnitChange(unit) {
      if (unit === 1 && this.temp.allowFileSize > 10485760) {
        this.temp.allowFileSize = 10485760
      } else if (unit === 0 && this.temp.allowFileSize > 10240) {
        this.temp.allowFileSize = 10240
      }
    },
    recordLogChange(val) {
      if (!val && this.temp.isBackup === 1) {
        this.temp.isBackup = 0
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    },
    needBackupChange(val) {
      if (!val) {
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.except-limit>>>.el-form-item__error {
  padding-left: 78px;
}
.backup-limit>>>.el-form-item__error {
  padding-left: 148px;
}
.multi-select>>>input {
  &::placeholder, &::-webkit-input-placeholder, &::-moz-placeholder, &:-ms-input-placeholder {
    color: #666 !important;
  }
}
</style>
