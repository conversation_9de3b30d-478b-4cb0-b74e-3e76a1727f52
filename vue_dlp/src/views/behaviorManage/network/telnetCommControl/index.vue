<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button style="margin-left: 10px;" icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">
          {{ $t('button.import') }}
        </el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <telnet-comm-control-dlg
      ref="telnetCommControlDlg"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    ></telnet-comm-control-dlg>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importTelnetCommControlStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink, hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils';
import TelnetCommControlDlg from './editDlg'
import ImportStg from '@/views/common/importStg.vue';
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import { getStrategyList, deleteStrategy } from '@/api/behaviorManage/network/telnetCommControl';
import { exportStg } from '@/api/stgCommon';

export default {
  name: 'TelnetCommControl',
  components: { TelnetCommControlDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入
    exportAble: { type: Boolean, default: false } // 是否支持导出
  },
  data() {
    return {
      stgCode: 252,
      treeable: true,
      showTree: true,
      deleteable: false,
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'blockType', label: 'stgMessage', width: '200', formatter: this.telnetMsgFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      checkedEntityNode: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    refresh() {
      return refreshPage(this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleCreate() {
      this.$refs['telnetCommControlDlg'].handleCreate()
    },
    handleUpdate(row) {
      this.$refs['telnetCommControlDlg'].handleUpdate(row)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    telnetMsgFormatter(row, data) {
      const exeRule = { 0: this.$t('pages.telnetOutExecRule1'), 1: this.$t('pages.telnetInExecRule1') }
      // 管控执行规则
      const controlExeRule = row.controlDetectionRule
      // 管控响应规则
      const action = row.action
      const ruleId = row.ruleId
      // 记录执行规则
      const recordExeRule = row.recordDetectionRule
      // 记录响应规则
      const record = row.record
      let controlSettings = ''
      if (action && ruleId) {
        controlSettings = exeRule[controlExeRule] + '，' + this.$t('pages.stopTelnetComm') + '、' + this.$t('pages.triggerViolationResponseRules')
      } else if (action && !ruleId) {
        controlSettings = exeRule[controlExeRule] + '，' + this.$t('pages.stopTelnetComm')
      } else if (!action && ruleId) {
        controlSettings = exeRule[controlExeRule] + '，' + this.$t('pages.triggerViolationResponseRules')
      } else if (!action && !ruleId) {
        controlSettings = exeRule[controlExeRule]
      }
      let recordSettings = ''
      if (record) {
        recordSettings = exeRule[recordExeRule] + '，' + this.$t('pages.recordTelnetComm')
      } else {
        recordSettings = exeRule[recordExeRule]
      }
      return this.$t('pages.telnetStrategyMsg', { controlSettings: controlSettings, recordSettings: recordSettings })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    importSuccess() {
      this.handleFilter()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    }
  }
}
</script>
