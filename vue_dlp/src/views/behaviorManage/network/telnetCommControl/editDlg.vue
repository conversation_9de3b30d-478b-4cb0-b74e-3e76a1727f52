<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      type="drawer"
      :title="$t('pages.telnetCommControlStrategy')"
      :stg-code="252"
      :active-able="activeAble"
      :time-able="true"
      :rules="rules"
      :model="temp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      class="telnet-stg-dlg"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('pages.telnetCommControl')" name="first">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <div v-if="formable">
                <el-button size="small" @click="handleCreateTelRule()">
                  {{ $t('button.insert') }}
                </el-button>
                <el-button size="small" :disabled="!telControlRuleDeletable" @click="handleDeleteCheckedTelRule()">
                  {{ $t('button.delete') }}
                </el-button>
                <el-button size="small" @click="handleClearTelRule()">
                  {{ $t('button.clear') }}
                </el-button>
              </div>
              <grid-table
                ref="telControlRuleGrid"
                :show-pager="false"
                auto-height
                :selectable="selectable"
                :col-model="telRuleColModel"
                :row-datas="temp.telnetControlRuleList"
                @selectionChangeEnd="telControlRuleSelectionChangeEnd"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label="" label-width="23px">
              <el-row>
                <el-col :span="24">
                  <el-radio-group v-model="temp.controlDetectionRule" :disabled="!formable" style="width: 100%;">
                    <el-radio :label="1" class="ellipsis">
                      <span :title="$t('pages.telnetInExecRule')">{{ $t('pages.telnetInExecRule') }}</span>
                    </el-radio>
                    <el-radio :label="0" class="ellipsis" style="width: 100%; margin-top:4px;">
                      <span :title="$t('pages.telnetOutExecRule')">{{ $t('pages.telnetOutExecRule') }}</span>
                    </el-radio>
                  </el-radio-group>
                </el-col>
                <el-col :span="24">
                  <label style="font-weight: 500;color: #409eff;font-size: small;">
                    {{ $t('pages.executeRuleTip', { info: this.$t('pages.identifierRule_regular'), info1: this.$t('pages.telnetCommunication') }) }}
                  </label>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      <i18n path="pages.withinDetectionRuleTip">
                        <span slot="info">{{ $t('pages.allowTelnetComm') }}</span>
                      </i18n>
                      <br/>
                      <i18n path="pages.outsideDetectionRuleTip1">
                        <span slot="info">{{ $t('pages.forbidTelnetComm') }}</span>
                      </i18n>
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-col>
              </el-row>
            </FormItem>
            <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
            <FormItem label="" label-width="7px" prop="action">
              <div>
                <el-checkbox v-model="temp.action" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable">
                  {{ $t('pages.stopTelnetComm') }}
                </el-checkbox>
                <el-tooltip slot="tooltip" effect="dark" placement="top-start">
                  <div slot="content">
                    <label>{{ $t('pages.stopTelnetCommTip') }}</label><br/>
                    <label>{{ $t('pages.stopTelnetCommTip1') }}</label>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </div>
              <ResponseContent
                :show-select="true"
                style="line-height: 15px !important;"
                :editable="formable"
                :select-style="{ 'margin-top': 0 }"
                read-only
                :prop-check-rule="checkRule"
                :show-check-rule="true"
                :prop-rule-id="temp.ruleId"
                @getRuleId="getRuleId"
                @ruleIsCheck="ruleIsCheck"
                @validate="(val) => { responseValidate = val }"
              />
            </FormItem>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.telnetCommControlRecord')" name="second">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <div v-if="formable">
                <el-button size="small" @click="handleCreateTelRule()">
                  {{ $t('button.insert') }}
                </el-button>
                <el-button size="small" :disabled="!telRecordRuleDeletable" @click="handleDeleteCheckedTelRule()">
                  {{ $t('button.delete') }}
                </el-button>
                <el-button size="small" @click="handleClearTelRule()">
                  {{ $t('button.clear') }}
                </el-button>
              </div>
              <grid-table
                ref="telRecordRuleGrid"
                :show-pager="false"
                auto-height
                :selectable="selectable"
                :col-model="telRuleColModel"
                :row-datas="temp.telnetRecordRuleList"
                @selectionChangeEnd="telRecordRuleSelectionChangeEnd"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label="" label-width="23px">
              <el-row>
                <el-col :span="24">
                  <el-radio-group v-model="temp.recordDetectionRule" :disabled="!formable" style="width: 100%;">
                    <el-radio :label="1" class="ellipsis">
                      <span :title="$t('pages.telnetInExecRule')">{{ $t('pages.telnetInExecRule') }}</span>
                    </el-radio>
                    <el-radio :label="0" class="ellipsis" style="width: 100%; margin-top:4px;">
                      <span :title="$t('pages.telnetOutExecRule')">{{ $t('pages.telnetOutExecRule') }}</span>
                    </el-radio>
                  </el-radio-group>
                </el-col>
                <el-col :span="24">
                  <label style="font-weight: 500;color: #409eff;font-size: small;">
                    {{ $t('pages.executeRuleTip', { info: this.$t('pages.identifierRule_regular'), info1: this.$t('pages.telnetCommunication') }) }}
                  </label>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      <i18n path="pages.withinDetectionRuleTip">
                        <span slot="info">{{ $t('pages.forbidRecordTelnetComm') }}</span>
                      </i18n>
                      <br/>
                      <i18n path="pages.outsideDetectionRuleTip">
                        <span slot="info">{{ $t('pages.allowRecordTelnetComm') }}</span>
                      </i18n>
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-col>
              </el-row>
            </FormItem>
            <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
            <FormItem label="" label-width="7px" prop="record">
              <div>
                <el-checkbox v-model="temp.record" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable">
                  {{ $t('pages.recordTelnetComm') }}
                </el-checkbox>
              </div>
            </FormItem>
          </el-tab-pane>
          <!--          <el-tab-pane :label="$t('pages.otherSettings')" name="third">-->
          <!--            <FormItem :label="$t('pages.limitTabInput')">-->
          <!--              <el-row>-->
          <!--                <el-col :span="4">-->
          <!--                  <el-radio v-model="temp.otherTelnetSettings.limitKey" :disabled="!formable" :label="1">{{ this.$t('pages.limit') }}</el-radio>-->
          <!--                </el-col>-->
          <!--                <el-col :span="3">-->
          <!--                  <el-radio v-model="temp.otherTelnetSettings.limitKey" :disabled="!formable" :label="0">{{ this.$t('pages.noLimit') }}</el-radio>-->
          <!--                </el-col>-->
          <!--              </el-row>-->
          <!--            </FormItem>-->
          <!--          </el-tab-pane>-->
        </el-tabs>
      </template>
    </stg-dialog>

    <!--单个添加/修改Telnet检测规则-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[ruleDialogStatus]"
      :visible.sync="telRuleVisible"
      width="380px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm2" :model="tempTelRule" :rules="telRules">
        <FormItem :label="$t('table.limitType')" label-width="70px" prop="limitType">
          <el-select v-model="tempTelRule.limitType" @change="handleLimitTypeChange">
            <el-option v-for="(label, key) in limitTypeOptions" :key="key" :label="label" :value="Number(key)"/>
          </el-select>
        </FormItem>

        <FormItem label-width="60px">
          <el-radio-group v-show="tempTelRule.limitType == 0 || tempTelRule.limitType == 1" v-model="tempTelRule.isCustomIp" @change="handleCustomIpChange">
            <el-radio :label="0">{{ $t('pages.allIP') }}</el-radio>
            <el-radio :label="1">{{ $t('route.custom') }}</el-radio>
          </el-radio-group>
        </FormItem>

        <FormItem :label="ipRangeOrDomainOrCommandLabel" label-width="70px" prop="checkData">
          <el-input v-model="tempTelRule.checkData" v-trim :disabled="(tempTelRule.limitType == 0 || tempTelRule.limitType == 1) && !tempTelRule.isCustomIp" maxlength="255"/>
        </FormItem>

        <FormItem v-if="tempTelRule.limitType == 0 || tempTelRule.limitType == 1" :label="endIpv4OrEndIpV6Label" label-width="70px" prop="endIp">
          <el-input v-model="tempTelRule.endIp" v-trim :disabled="!tempTelRule.isCustomIp" maxlength="40"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="telRuleVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createStrategy, getStrategyByName, updateStrategy } from '@/api/behaviorManage/network/telnetCommControl'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { isIPv4, isIPv6 } from '@/utils/validate';

export default {
  name: 'TelnetCommControlDlg',
  components: { ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: {
      type: Object, default() {
        return {}
      }
    }
  },
  data() {
    return {
      slotName: undefined,
      activeName: 'first',
      temp: {},
      defaultTemp: { // 表单字段
        id: '',
        name: '',
        entityType: undefined,
        entityId: undefined,
        active: false,
        telnetControlRuleList: [],
        controlDetectionRule: 1,
        action: 0,
        ruleId: null,
        telnetRecordRuleList: [],
        recordDetectionRule: 0, // 版本兼容，默认选择规则之外触发响应规则
        record: 1,  // 版本兼容，默认勾选审计选项
        remark: ''
        // otherTelnetSettings: {
        //   limitKey: 1
        // }
      },
      tempTelRule: {},
      defaultTempTelRule: { // Telnet检测规则表单字段
        id: '',
        limitType: 0,
        checkData: '0.0.0.0',
        endIp: '***************',
        isCustomIp: 0,
        ipRangeDomainCommand: ''
      },
      telRuleColModel: [
        { prop: 'limitType', label: 'limitType', width: '150', sort: true, sortOriginal: true, formatter: this.limitTypeFormatter },
        { prop: 'ipRangeDomainCommand', label: 'ipRangeDomainCommand', width: '300' },
        {
          label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleUpdateTelRule }
          ]
        }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      telRules: {
        checkData: [
          { validator: this.checkDataValidator, trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        endIp: [
          { validator: this.endIpValidator, trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ]
      },
      propRuleId: undefined,
      telControlRuleDeletable: false,
      telRecordRuleDeletable: false,
      dialogStatus: '',
      telRuleVisible: false,
      limitTypeOptions: {
        0: this.$t('pages.telnetLimitType1'),
        1: this.$t('pages.telnetLimitType2'),
        2: this.$t('pages.telnetLimitType3'),
        3: this.$t('pages.telnetLimitType4')
      },
      ruleDialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.detectionRules'), 'update'),
        create: this.i18nConcatText(this.$t('pages.detectionRules'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.detectionRules'), 'delete')
      },
      checkRule: false,
      ipRangeOrDomainOrCommandLabel: '',
      endIpv4OrEndIpV6Label: ''
    }
  },
  computed: {
    telControlRuleGrid() {
      return this.$refs['telControlRuleGrid']
    },
    telRecordRuleGrid() {
      return this.$refs['telRecordRuleGrid']
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
    this.resetTempTelRule()
  },
  activated() {

  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    handleLimitTypeChange(val) {
      val = val || this.tempTelRule.limitType
      if (val == 0) { // 按ipv4地址匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('pages.beginIpv4')
        this.endIpv4OrEndIpV6Label = this.$t('pages.endIpv4')
        this.tempTelRule.isCustomIp = 0
        if (this.$refs['dataForm2']) {
          this.$refs['dataForm2'].clearValidate()
        }
        this.tempTelRule.checkData = '0.0.0.0'
        this.tempTelRule.endIp = '***************'
      } else if (val == 1) { // 按ipv6地址匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('pages.beginIpv6')
        this.endIpv4OrEndIpV6Label = this.$t('pages.endIpv6')
        this.tempTelRule.isCustomIp = 0
        if (this.$refs['dataForm2']) {
          this.$refs['dataForm2'].clearValidate()
        }
        this.tempTelRule.checkData = '0:0:0:0:0:0:0:0'
        this.tempTelRule.endIp = 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff'
      } else if (val == 2) { // 按域名匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('pages.domain')
        this.endIpv4OrEndIpV6Label = ''
        if (this.$refs['dataForm2']) {
          this.$refs['dataForm2'].clearValidate()
        }
        this.tempTelRule.checkData = ''
        this.tempTelRule.endIp = ''
      } else if (val == 3) { // 按命令匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('table.command')
        this.endIpv4OrEndIpV6Label = ''
        if (this.$refs['dataForm2']) {
          this.$refs['dataForm2'].clearValidate()
        }
        this.tempTelRule.checkData = ''
        this.tempTelRule.endIp = ''
      }
    },
    handleCustomIpChange(val) {
      if (this.tempTelRule.limitType == 0) { // 按ipv4地址匹配
        if (this.$refs['dataForm2']) {
          this.$refs['dataForm2'].clearValidate()
        }
        this.tempTelRule.checkData = '0.0.0.0'
        this.tempTelRule.endIp = '***************'
      } else if (this.tempTelRule.limitType == 1) { // 按ipv6地址匹配
        if (this.$refs['dataForm2']) {
          this.$refs['dataForm2'].clearValidate()
        }
        this.tempTelRule.checkData = '0:0:0:0:0:0:0:0'
        this.tempTelRule.endIp = 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff'
      }
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.checkRule = false
      this.propRuleId = undefined
    },
    resetTempTelRule() {
      this.tempTelRule = Object.assign({}, this.defaultTempTelRule)
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.activeName = 'first'
      this.temp.entityType = this.entityNode.type
      this.temp.entityId = this.entityNode.dataId
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.dialogStatus = 'update'
      this.activeName = 'first'
      this.$refs['stgDlg'].show(row, this.formable)
      this.temp.entityType = row.entityType
      this.temp.entityId = row.entityId
    },
    closed() {
      this.resetTemp()
    },
    handleCreateTelRule() {
      this.resetTempTelRule()
      this.handleLimitTypeChange()
      this.telRuleVisible = true
      this.ruleDialogStatus = 'create'
      this.$nextTick(() => {
        this.$refs['dataForm2'].clearValidate()
      })
    },
    handleUpdateTelRule(row) {
      this.resetTempTelRule()
      this.tempTelRule = Object.assign(this.tempTelRule, row)
      const limitType = this.tempTelRule.limitType
      if (limitType == 0) { // 按ipv4地址匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('pages.beginIpv4')
        this.endIpv4OrEndIpV6Label = this.$t('pages.endIpv4')
      } else if (limitType == 1) { // 按ipv6地址匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('pages.beginIpv6')
        this.endIpv4OrEndIpV6Label = this.$t('pages.endIpv6')
      } else if (limitType == 2) { // 按域名匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('pages.domain')
        this.endIpv4OrEndIpV6Label = ''
      } else if (limitType == 3) { // 按命令匹配
        this.ipRangeOrDomainOrCommandLabel = this.$t('table.command')
        this.endIpv4OrEndIpV6Label = ''
      }
      this.telRuleVisible = true
      this.ruleDialogStatus = 'update'
      this.$nextTick(() => {
        this.$refs['dataForm2'].clearValidate()
      })
    },
    saveData() {
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          if (this.activeName == 'first') {
            const index = this.temp.telnetControlRuleList.findIndex(item => {
              if ((this.tempTelRule.limitType == 0 || this.tempTelRule.limitType == 1) &&
                item.limitType == this.tempTelRule.limitType &&
                item.checkData == this.tempTelRule.checkData &&
                item.endIp == this.tempTelRule.endIp) {
                // 按ipv4或ipv6匹配，起始ip和结束ip相同代表同一个数据，不进行添加
                return true
              } else if ((this.tempTelRule.limitType == 2 || this.tempTelRule.limitType == 3) &&
                item.limitType == this.tempTelRule.limitType &&
                item.checkData == this.tempTelRule.checkData) {
                // 按域名或命令匹配，域名或命令数据相同代表同一个数据，不进行添加
                return true
              } else {
                return false
              }
            })
            if (index == -1) {
              if (this.tempTelRule.id) {
                this.formatTempTelRule()
                this.telControlRuleGrid.updateRowData(this.tempTelRule)
              } else {
                this.tempTelRule.id = new Date().getTime()
                this.formatTempTelRule()
                this.temp.telnetControlRuleList.push(this.tempTelRule)
              }
              this.telRuleVisible = false
            } else {
              this.$message({
                message: this.$t('pages.wifiBlock_text5'),
                type: 'error',
                duration: 2000
              })
            }
          } else if (this.activeName == 'second') {
            const index = this.temp.telnetRecordRuleList.findIndex(item => {
              if ((this.tempTelRule.limitType == 0 || this.tempTelRule.limitType == 1) &&
                item.limitType == this.tempTelRule.limitType &&
                item.checkData == this.tempTelRule.checkData &&
                item.endIp == this.tempTelRule.endIp) {
                // 按ipv4或ipv6匹配，起始ip和结束ip相同代表同一个数据，不进行添加
                return true
              } else if ((this.tempTelRule.limitType == 2 || this.tempTelRule.limitType == 3) &&
                item.limitType == this.tempTelRule.limitType &&
                item.checkData == this.tempTelRule.checkData) {
                // 按域名或命令匹配，域名或命令数据相同代表同一个数据，不进行添加
                return true
              } else {
                return false
              }
            })
            if (index == -1) {
              if (this.tempTelRule.id) {
                this.formatTempTelRule()
                this.telRecordRuleGrid.updateRowData(this.tempTelRule)
              } else {
                this.tempTelRule.id = new Date().getTime()
                this.formatTempTelRule()
                this.temp.telnetRecordRuleList.push(this.tempTelRule)
              }
              this.telRuleVisible = false
            } else {
              this.$message({
                message: this.$t('pages.wifiBlock_text5'),
                type: 'error',
                duration: 2000
              })
            }
          }
        }
      })
    },
    formatTempTelRule() {
      if (this.tempTelRule.limitType == 0 || this.tempTelRule.limitType == 1) {
        this.tempTelRule.ipRangeDomainCommand = this.tempTelRule.checkData + ' ~ ' + this.tempTelRule.endIp
      } else if (this.tempTelRule.limitType == 2 || this.tempTelRule.limitType == 3) {
        this.tempTelRule.ipRangeDomainCommand = this.tempTelRule.checkData
      }
    },
    handleDeleteCheckedTelRule() {
      if (this.activeName == 'first') {
        const toDeleteKeys = this.telControlRuleGrid.getSelectedKeys()
        this.telControlRuleGrid.deleteRowData(toDeleteKeys)
      } else if (this.activeName == 'second') {
        const toDeleteKeys = this.telRecordRuleGrid.getSelectedKeys()
        this.telRecordRuleGrid.deleteRowData(toDeleteKeys)
      }
    },
    handleClearTelRule() {
      if (this.activeName == 'first') {
        this.telControlRuleGrid.clearRowData()
        this.temp.telnetControlRuleList.splice(0, this.temp.telnetControlRuleList.length)
      } else if (this.activeName == 'second') {
        this.telRecordRuleGrid.clearRowData()
        this.temp.telnetRecordRuleList.splice(0, this.temp.telnetRecordRuleList.length)
      }
    },
    telControlRuleSelectionChangeEnd(rowDatas) {
      this.telControlRuleDeletable = rowDatas && rowDatas.length > 0
    },
    telRecordRuleSelectionChangeEnd(rowDatas) {
      this.telRecordRuleDeletable = rowDatas && rowDatas.length > 0
    },
    selectable(row, index) {
      return this.formable
    },
    formatRowData(rowData) {
      if (rowData.ruleId) {
        this.checkRule = true
      }
    },
    formatFormData(formData) {
      if (!this.checkRule) {
        formData.ruleId = null
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    buttonFormatter(row) {
      return !this.formable
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    validateForm(formData) {
      return this.responseValidate
    },
    limitTypeFormatter: function(row, data) {
      return this.limitTypeOptions[row.limitType]
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      if (this.temp.ruleId) {
        this.propRuleId = this.temp.ruleId
      }
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    ruleIsCheck(data) {
      this.checkRule = !!data
    },
    endIpValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('text.pleaseEnterInfo', { info: this.endIpv4OrEndIpV6Label })))
      } else {
        callback()
      }
    },
    checkDataValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('text.pleaseEnterInfo', { info: this.ipRangeOrDomainOrCommandLabel })))
      } else {
        callback()
      }
    },
    ipValidator(rule, value, callback) {
      if (this.tempTelRule.limitType == 2 || this.tempTelRule.limitType == 3) { // 域名、命令
        callback()
        return
      }
      if (this.tempTelRule.limitType == 0) { // ipv4
        if (value && isIPv4(value)) {
          if (this.tempTelRule.checkData && this.tempTelRule.endIp) {
            const temp1 = this.tempTelRule.checkData.split('.')
            const temp2 = this.tempTelRule.endIp.split('.')
            let flag = false
            for (let i = 0; i < 4; i++) {
              if (temp1[i] - temp2[i] == 0) {
                continue
              } else if (temp1[i] - temp2[i] > 0) {
                flag = true
              }
              break
            }
            if (flag) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      } else if (this.tempTelRule.limitType == 1) { // ipv6
        if (value && isIPv6(value)) {
          if (this.tempTelRule.checkData && this.tempTelRule.endIp) {
            const fullbeginIpv6 = this.getFullIPv6(this.tempTelRule.checkData)
            const fullendIpv6 = this.getFullIPv6(this.tempTelRule.endIp)
            if (fullbeginIpv6 > fullendIpv6) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      }
    },
    getFullIPv6(src) {
      const temp = src.replace('::', '*')
      const secs = temp.split(':')
      let ret = ''
      for (let i = 0; i < secs.length; i++) {
        const t = secs[i]
        if (t.indexOf('*') >= 0) {
          const ss = t.split('*')
          ret += ss[0].padStart(4, '0')
          for (let j = 1; j < (8 - secs.length); j++) {
            ret += '0000'
          }
          ret += ss[1].padStart(4, '0');
        } else {
          ret += t.padStart(4, '0');
        }
      }
      return ret.toLocaleLowerCase();
    }
  }
}
</script>
<style lang="scss" scoped>
.telnet-stg-dlg {
  >>> .first-divider {
    margin-top: 5px;
  }
}

.prohibit >>> .el-checkbox__inner {
  margin-left: 17px;
}
</style>
