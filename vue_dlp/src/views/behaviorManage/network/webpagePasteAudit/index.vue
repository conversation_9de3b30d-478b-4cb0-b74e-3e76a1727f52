<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;">{{ strategyFormatter(props.detail) }}</span>
          </div>
        </template>
      </grid-table>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 20px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <FormItem :label="$t('pages.monitorWay')" prop="remark">
          <el-row>
            <el-col v-for="(value, key) in monitorOptions" :key="key" :span="9">
              <el-radio v-model="temp.monitorWay" :disabled="!formable" :label="parseInt(value)">
                {{ key }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ monitorOptTips[value] }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-radio>
            </el-col>
          </el-row>
        </FormItem>

        <import-table
          ref="elgTable"
          :search-info-prompt-message="$t('pages.webpageMatchRulePlaceholder')"
          :add-disabled="false"
          :import-disabled="false"
          :input-disabled="false"
          :delete-disabled="!webpageMatchRuleDeleteable"
          :col-model="elgColModel"
          :row-datas="tempElgData"
          :row-no-label="$t('table.keyId')"
          :formable="formable"
          :selectable="selectableElg"
          :handle-create="handleElgCreate"
          :handle-delete="deleteElgData"
          :handle-import="handleWebpageMatchRuleImport"
          :handle-search="handleSearch"
          @selectionChangeEnd="webpageMatchRuleTableSelectionChangeEnd"
        />

      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'A5J'" :link-url="'/system/baseData/webpageMatchRule'" :btn-text="$t('pages.maintainWebpageMatchRuleLib')"/>
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>

        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 网页匹配规则的新增、修改-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="elgDialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t(textMap[elgDialogStatus]) }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('pages.matchSupport') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form
        ref="elgDataForm"
        :rules="elgRules"
        :model="elgTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.name')" prop="name">
          <el-input v-model="elgTemp.name" v-trim :maxlength="60"/>
        </FormItem>

        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="addGroupAble ? 21 : 24">
              <el-select v-model="elgTemp.groupId" filterable :placeholder="$t('text.select')">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
            </el-col>
            <el-col v-show="addGroupAble" style="padding-top:1px" :span="3">
              <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </el-col>
          </el-row>
        </FormItem>

        <FormItem :label="$t('table.ipOrDomain')" prop="host">
          <el-input v-model="elgTemp.host" v-trim :maxlength="60"/>
        </FormItem>

        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.beginPort')" prop="beginPort">
              <el-input v-model.number="elgTemp.beginPort" v-trim :maxlength="5"/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.endPort')" prop="endPort">
              <el-input v-model.number="elgTemp.endPort" v-trim :maxlength="5"/>
            </FormItem>
          </el-col>
        </el-row>

        <!--        <FormItem :label="$t('table.matchKind')" prop="matchKind">-->
        <!--          <el-select v-model="elgTemp.matchKind">-->
        <!--            <el-option v-for="(value, key) in matchKindOptions" :key="value" :label="key" :value="value" ></el-option>-->
        <!--          </el-select> 根据终端调整，匹配模式写死为模糊匹配-->
        <!--        </FormItem>-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="elgDialogStatus==='createElg'?createElgData(): elgDialogStatus==='updateElg'? updateElgData() : {}">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="elgDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-table-dlg
      ref="elgImportTable"
      :exits-list-data="elgData"
      :not-selected-prompt-message="$t('pages.webpageMatchRuleText')"
      :elg-title="$t('pages.importWebpageMatchRule')"
      :group-root-name="$t('route.webpageMatchRuleLib')"
      :search-info-name="$t('pages.webpageMatchRulePlaceholder')"
      :confirm-button-name="$t('pages.addWebpageMatchRule')"
      :group-title="$t('pages.webpageMatchRuleGroup')"
      :col-model="importColModel"
      :list="getWebpageMatchRuleList"
      :load-group-tree="getTreeNode"
      :create-group="createWebpageMatchRuleGroup"
      :update-group="updateWebpageMatchRuleGroup"
      :delete-group="deleteWebpageMatchRuleGroup"
      :count-by-group="countWebpageMatchRuleByGroupId"
      :get-group-by-name="getWebpageMatchRuleGroupByName"
      :delete="deleteWebpageMatchRule"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteWebpageMatchRuleGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleCreateElg"
      :get-list-by-group-ids="listWebpageMatchRuleByGroupId"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
      @elgCancelAfter="elgCancelAfter"
      @changeGroupAfter="changeGroupAfter"
    />

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importWebpagePasteAuditStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :edit-valid-func="getWebpageMatchRuleGroupByName"
      :add-func="createWebpageMatchRuleGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>
<script>
import {
  getWebpagePasteAuditStrategyPage, createWebpagePasteAudit, updateWebpagePasteAudit, deleteWebpagePasteAudit, getWebpagePasteAuditByName
} from '@/api/behaviorManage/network/webpagePasteAudit'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter
} from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import {
  getTreeNode,
  getWebpageMatchRuleGroupByName,
  createWebpageMatchRuleGroup,
  updateWebpageMatchRuleGroup,
  deleteWebpageMatchRuleGroup,
  countWebpageMatchRuleByGroupId,
  deleteGroupAndData,
  moveGroupToOther,
  listWebpageMatchRuleByGroupId,
  getByIds,
  getByName,
  createWebpageMatchRule,
  updateWebpageMatchRule,
  deleteWebpageMatchRule,
  getWebpageMatchRuleList
} from '@/api/system/baseData/webpageMatchRule';
import { findNodeLabel } from '@/utils/tree';
import EditGroupDlg from '@/views/common/editGroupDlg'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon';

export default {
  name: 'WebpagePasteAudit',
  components: { ImportTable, EditGroupDlg, ImportTableDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 238,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', ellipsis: false, type: 'popover', originData: true, width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      monitorOptions: { [this.$t('pages.monitorUrl')]: 1, [this.$t('pages.notMonitorUrl')]: 0 },
      monitorOptTips: {
        1: this.$t('pages.listenURLTip2', { obj: this.$t('pages.urlList'), action: this.$t('pages.webpagePasting') }),
        0: this.$t('pages.listenURLTip1', { obj: this.$t('pages.urlList'), action: this.$t('pages.webpagePasting') })
      },
      // matchKindOptions: {
      //   [this.$t('pages.blueTooth_blueTooth_Msg11')]: 0,
      //   [this.$t('pages.leftLikeMatch')]: 1,
      //   [this.$t('pages.rightLikeMatch')]: 2,
      //   [this.$t('pages.blueTooth_blueTooth_Msg12')]: 3
      // },
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        monitorWay: 1,
        webpageMatchRuleIds: [],
        entityType: undefined,
        entityId: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.webpagePasteAuditStrategy'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.webpagePasteAuditStrategy'), 'create'),
        createElg: this.i18nConcatText(this.$t('table.webpageMatchRule'), 'create'),
        updateElg: this.i18nConcatText(this.$t('table.webpageMatchRule'), 'update')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.stgNameRequired'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      webpageMatchRuleDeleteable: false,
      elgColModel: [
        { prop: 'name', label: 'name', width: '120', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '120', sort: true, sortOriginal: true, formatter: this.groupFormatter },
        { prop: 'host', label: 'ipOrDomain', width: '120', sort: true },
        { prop: 'beginPort', label: 'beginPort', width: '100', sort: true },
        { prop: 'endPort', label: 'endPort', width: '100', sort: true },
        // { prop: 'matchKind', label: 'matchKind', width: '120', formatter: this.matchKindFormatter },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: row => {
              this.updateFlag = false
              this.handleUpdateImportBase(row)
            } }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'name', width: '120', sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', width: '120', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'host', label: 'ipOrDomain', width: '120', sort: 'custom' },
        { prop: 'beginPort', label: 'beginPort', width: '100', sort: 'custom' },
        { prop: 'endPort', label: 'endPort', width: '100', sort: 'custom' },
        // { prop: 'matchKind', label: 'matchKind', width: '120', formatter: this.matchKindFormatter },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdateImport }
          ]
        }
      ],
      treeSelectNode: [],
      elgData: [],
      tempElgData: [], // 临时列表数据,
      addGroupAble: false,  // 添加数据时，是否显示添加分组按钮
      elgTemp: {},
      defaultElgTemp: {
        id: undefined,
        name: '',
        groupId: '',
        host: '',
        beginPort: undefined,
        endPort: undefined,
        matchKind: 3 // 默认模糊匹配
      },
      elgDialogStatus: '',
      elgDialogFormVisible: false,
      elgRules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        groupId: [{ required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }],
        host: [{ required: true, message: this.$t('pages.ipOrDomainNotEmpty'), trigger: 'blur' }],
        beginPort: [
          { required: true, message: this.$t('pages.validateMsg_beginPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        endPort: [
          { required: true, message: this.$t('pages.validateMsg_endPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ]
      },
      dlgSubmitting: false,
      elgQuery: {
        searchInfo: ''
      },
      createFlag: false,
      updateFlag: false,
      needIds: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
    elgData(val) {
      (val || []).forEach(item => {
        item.groupName = this.getGroupNameByDataId(this.treeSelectNode, item.groupId)
      })
      this.handleSearch()
    },
    needIds(val) {
      if (val.length > 0) {
        getByIds({ ids: val.join(',') }).then(res => {
          this.elgData = res.data || []
        })
      }
    }
  },
  activated() {
    this.listTreeNode().then(() => {
      if (this.elgDialogFormVisible && this.elgTemp.groupId) {
        if (!this.treeSelectNode.some(node => node.dataId == this.elgTemp.groupId)) {
          this.elgTemp.groupId = ''
        }
      }
    })
  },
  created() {
    this.resetTemp()
    this.listTreeNode()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    getTreeNode,
    deleteGroupAndData,
    moveGroupToOther,
    getWebpageMatchRuleGroupByName,
    createWebpageMatchRuleGroup,
    updateWebpageMatchRuleGroup,
    deleteWebpageMatchRuleGroup,
    countWebpageMatchRuleByGroupId,
    deleteWebpageMatchRule,
    listWebpageMatchRuleByGroupId,
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getWebpagePasteAuditStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.elgData = []
    },
    handleCreate() {
      // 将表单字段所’绑定的数据temp‘重置
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      // dialogFormVisible数据变化为true，要等到该方法结束，:visible.sync=dialogFormVisible 才能打开弹窗，存在于dom节点
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['elgTable'].clearSearchInfo();
        // 弹窗以及弹窗子组件form表单 存在于dom节点后 把form校验清空
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      this.getWebpageMatchRuleListByIds(row.webpageMatchRuleIds && row.webpageMatchRuleIds.join(',') || '')
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['elgTable'].clearSearchInfo();
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.webpageMatchRuleIds = this.getWebpageMatchRuleIds(this.elgData)
          createWebpagePasteAudit(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.webpageMatchRuleIds = this.getWebpageMatchRuleIds(this.elgData)
          const tempData = Object.assign({}, this.temp)
          updateWebpagePasteAudit(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteWebpagePasteAudit({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyNameValidator(rule, value, callback) {
      getWebpagePasteAuditByName({ name: value }).then(respond => {
        const stg = respond.data
        if (stg && stg.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    strategyFormatter: function(row) {
      let msg = ''
      if (row.monitorWay === 0) {
        msg += `${this.$t('pages.monitorWay')}: ${this.$t('pages.notMonitorUrl')};`
      } else if (row.monitorWay === 1) {
        msg += `${this.$t('pages.monitorWay')}: ${this.$t('pages.monitorUrl')};`
      }
      // if (row.webpageMatchRuleIds) {
      //   msg += `${this.$t('pages.webpageMatchRuleIds')}: [${row.webpageMatchRuleIds}];`
      // }

      return msg
    },
    webpageMatchRuleTableSelectionChangeEnd: function(rowDatas) {
      this.webpageMatchRuleDeleteable = rowDatas.length > 0
    },
    // 获取分组数据
    listTreeNode() {
      return getTreeNode().then(res => {
        this.treeSelectNode = res.data || []
      })
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    selectableElg(row, index) {
      return selectable(row, index)
    },
    handleElgCreate() {
      this.addGroupAble = true
      this.createFlag = false
      this.elgResetTemp()
      this.listTreeNode();
      this.elgDialogStatus = 'createElg'
      this.$nextTick(() => {
        this.$refs['elgDataForm'].clearValidate()
      })
      this.elgDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['elgDataForm'].clearValidate()
      })
    },
    elgResetTemp() {
      this.elgTemp = Object.assign({}, this.defaultElgTemp)
    },
    portValidator(rule, value, callback) {
      if (isNaN(Number(value)) || Number(value) != parseInt(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text5')))
      } else {
        if (value < 1 || value > 65535) {
          callback(new Error(this.$t('pages.serverLibrary_text6')))
        } else {
          const beginPort = Number(this.elgTemp.beginPort)
          const endPort = Number(this.elgTemp.endPort)
          if (beginPort && endPort) {
            if (beginPort > endPort) {
              callback(new Error(this.$t('pages.serverLibrary_text7')))
            } else {
              this.$refs['elgDataForm'].clearValidate(['beginPort', 'endPort'])
              callback()
            }
          } else {
            callback()
          }
        }
      }
    },
    nameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getByName({ name: value }).then(respond => {
          const webpageMatchRule = respond.data
          if (webpageMatchRule && webpageMatchRule.id != this.elgTemp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    createElgData() {
      this.dlgSubmitting = true
      this.$refs['elgDataForm'].validate((valid) => {
        if (valid) {
          this.elgTemp.groupName = findNodeLabel(this.treeSelectNode, this.elgTemp.groupId, 'dataId')
          createWebpageMatchRule(this.elgTemp).then(respond => {
            this.dlgSubmitting = false
            this.isImportElg(respond.data, 'create')
            this.elgDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    handleUpdateImportBase(row) {
      this.addGroupAble = true
      this.elgDialogStatus = 'updateElg'
      this.elgTemp = Object.assign({}, row)
      this.elgTemp.groupId = this.elgTemp.groupId ? this.elgTemp.groupId + '' : '';
      this.listTreeNode()
      this.$nextTick(() => {
        this.$refs['elgDataForm'].clearValidate()
      })
      this.elgDialogFormVisible = true
    },
    updateElgData() {
      this.dlgSubmitting = true
      this.$refs['elgDataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.elgTemp)
          updateWebpageMatchRule(tempData).then(respond => {
            this.isImportElg(respond.data, 'update')
            this.dlgSubmitting = false
            this.elgDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createFlag) {
          this.$refs['elgImportTable'].justRefreshTableData()
        } else {
          this.elgData.push(data)
        }
        this.createFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$refs['elgImportTable'].justRefreshTableData()
        } else {
          for (let i = 0; i < this.elgData.length; i++) {
            if (this.elgData[i].id === data.id) {
              this.elgData.splice(i, 1)
              this.elgData.push(data)
              break
            }
          }
        }
        this.updateFlag = false
      }
    },
    //  弹窗列表查询
    handleSearch(searchInfo) {
      searchInfo = searchInfo === undefined ? this.elgQuery.searchInfo && this.elgQuery.searchInfo.trim() : searchInfo && searchInfo.trim() || ''
      searchInfo = searchInfo.toLowerCase()
      if (searchInfo === '') {
        this.tempElgData = this.elgData
      } else {
        this.tempElgData = this.elgData.filter(item => {
          return (item.name && item.name.toLowerCase().indexOf(searchInfo) !== -1)
        })
      }
    },
    deleteElgData() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs['elgTable'].getSelectedIds() || []
        this.elgData = this.$refs['elgTable'].deleteTableData(this.elgData, toDeleteIds)
      }).catch(() => {})
    },
    handleTypeCreate() {
      this.listTreeNode()
      this.$refs['createGroupDlg'].handleCreate();
    },
    createGroupAddEnd(row) {
      this.listTreeNode();
      this.elgTemp.groupId = row.id + ''
    },
    handleWebpageMatchRuleImport() {
      this.$nextTick(() => {
        this.$refs['elgImportTable'].show()
      })
    },
    // 导入方法的row-data-api
    getWebpageMatchRuleList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getWebpageMatchRuleList(searchQuery)
    },
    handleCreateElg(selectedGroupId, flag) {
      this.elgDialogStatus = 'createElg'
      this.listTreeNode()
      this.createFlag = flag || false
      this.elgTemp = Object.assign({}, this.defaultElgTemp)
      this.elgTemp.groupId = selectedGroupId
      this.$nextTick(() => {
        this.$refs['elgDataForm'].clearValidate()
      })
      //  隐藏新增网址时的新增分组按钮
      this.addGroupAble = false
      this.elgDialogFormVisible = true
    },
    getNeedAddIds(needIds) {
      this.listTreeNode()
      this.needIds = needIds
    },
    // 根据webpageMatchRuleIds获取webpageMatchRule数据
    getWebpageMatchRuleListByIds(ids) {
      getByIds({ ids: ids }).then(res => {
        this.elgData = res.data || []
      });
    },
    //  删除记录
    submitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      this.elgData = this.elgData.filter(item => { return deleteIdsList.filter(i => i === item.id).length === 0 })
    },
    elgCancelAfter() {
      this.listTreeNode();
      this.getWebpageMatchRuleByIds();
    },
    getWebpageMatchRuleByIds() {
      const ids = this.getWebpageMatchRuleIds(this.elgData);
      this.getWebpageMatchRuleListByIds(ids.join(','));
    },
    //  获取ids
    getWebpageMatchRuleIds(array) {
      const arr = [];
      (array || []).forEach(item => {
        arr.push(item.id)
      })
      return arr;
    },
    //  弹窗分组发送变化时
    changeGroupAfter() {
      this.listTreeNode()
    },
    handleUpdateImport(row) {
      this.updateFlag = true
      this.handleUpdateImportBase(row)
      this.addGroupAble = false
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    importSuccess() {
      this.handleFilter()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    }
    // matchKindFormatter(row, val) {
    //   const matchKindOptions = { 0: this.$t('pages.blueTooth_blueTooth_Msg11'), 1: this.$t('pages.leftLikeMatch'),
    //     2: this.$t('pages.rightLikeMatch'), 3: this.$t('pages.blueTooth_blueTooth_Msg12') }
    //   const matchKind = matchKindOptions[val]
    //   if (matchKind) {
    //     return matchKind
    //   } else {
    //     return ''
    //   }
    // }
  }
}
</script>
