<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="ftpControlConfigList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />

      <ftp-control-config-dlg
        ref="ftpDlg"
        :active-able="treeable"
        :entity-node="checkedEntityNode"
        @submitEnd="submitEnd"
      />
    </div>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importFtpControllerConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { deleteStrategy, getStrategyPage } from '@/api/behaviorManage/network/ftpControlConfig'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import FtpControlConfigDlg from './editDlg'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'FtpControlConfig',
  components: { FtpControlConfigDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 205,
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        /* { prop: 'limitFileSize', label: 'limitFileSize', width: '120' },
        { prop: 'limitType', label: 'idDisableFTP', width: '120', formatter: this.limitTypeFormatter },
        { prop: 'isRecord', label: 'idAudit', width: '120', formatter: this.isRecordFormatter },
        { prop: 'isAlarm', label: 'isAlarm', width: '120', formatter: this.isAlarmFormatter }, */
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: {
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      hasEncPermision: true,  // 是包含加解密模块
      checkedEntityNode: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['ftpControlConfigList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取注册模块
    this.listModule()
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    selectionChangeEnd(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = resp.data
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    refresh() {
      return refreshPage(this)
    },
    submitEnd() {
      this.gridTable.execRowDataApi()
    },
    handleCreate() {
      this.$refs['ftpDlg'].handleCreate(this.hasEncPermision)
    },
    handleUpdate(row) {
      this.$set(row, 'hasEncPermision', this.hasEncPermision)
      this.$refs['ftpDlg'].handleUpdate(row)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    limitTypeFormatter: function(row, data) {
      let limitType = ''
      if (row.limitType == 1) {
        limitType = this.$t('pages.prohibit')
      } else {
        limitType = this.$t('pages.noProhibit')
      }
      return limitType;
    },
    isRecordFormatter: function(row, data) {
      let isRecord = ''
      if (row.isRecord == 1) {
        isRecord = this.$t('pages.audit')
      } else {
        isRecord = this.$t('pages.noAudit')
      }
      return isRecord;
    },
    isAlarmFormatter: function(row, data) {
      let isAlarm = ''
      if (row.isAlarm == 1) {
        isAlarm = this.$t('pages.alarm')
      } else {
        isAlarm = this.$t('pages.noAlarm')
      }
      return isAlarm;
    },
    strategyFormatter: function(row, data) {
      const msgArr = []
      if (row.approvalType >= 0) {
        const approvalList = this.numToList(row.approvalType, 4)
        if (this.hasEncPermision) {
          if (approvalList.indexOf(1) > -1) {
            // 明文文件禁止外发
            msgArr.push(this.$t('pages.burnMsg4'))
          }
          if (approvalList.indexOf(2) > -1) {
            // 密文文件禁止外发
            msgArr.push(this.$t('pages.burnMsg8'))
          }
          if (approvalList.indexOf(4) > -1) {
            // 允许明文文件外发审批
            msgArr.push(this.$t('pages.burnMsg6'))
          }
          if (approvalList.indexOf(8) > -1) {
            // 允许密文文件外发审批
            msgArr.push(this.$t('pages.burnMsg9'))
          }
        } else {
          if (approvalList.indexOf(1) > -1 || approvalList.indexOf(4) > -1) {
            // 禁止文件外发
            msgArr.push(this.$t('pages.burnMsg5'))
          }
          if (approvalList.indexOf(4) > -1 || approvalList.indexOf(8) > -1) {
            // 允许文件外发审批
            msgArr.push(this.$t('pages.burnMsg7'))
          }
        }
      }
      if (this.hasEncPermision) {
        if (row.ruleId) {
          // 触发明文文件响应规则
          msgArr.push(this.$t('pages.burnMsg10'))
        }
        if (row.encRuleId) {
          // 触发密文文件响应规则
          msgArr.push(this.$t('pages.burnMsg11'))
        }
      } else {
        if (row.ruleId) {
          // 触发响应规则
          msgArr.push(this.$t('pages.triggerResponseRule'))
        }
      }
      if (row.isRecord && row.limitFileSize) {
        // FTP文件传输记录：文件备份限制{row.limitFileSize}MB
        msgArr.push(`${this.$t('pages.ftpFileTransferRecord')}：${this.$t('pages.burnMsg13', { BackupSize: row.limitFileSize })}`)
      } else if (row.isRecord && !row.limitFileSize) {
        // FTP文件传输记录：不备份文件
        msgArr.push(`${this.$t('pages.ftpFileTransferRecord')}：${this.$t('pages.burnMsg12')}`)
      }
      if (row.detectionRule == 0) {
        msgArr.push(this.$t('pages.ftpControl_text12'))
      } else if (row.detectionRule == 1) {
        msgArr.push(this.$t('pages.ftpControl_text13'))
      }
      if (row.addressList && row.addressList.length > 0) {
        msgArr.push(`${this.$t('pages.ftpControl_text11')}${row.addressList.map(item => item.address)}`)
      }
      return msgArr.join('; ')
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>
