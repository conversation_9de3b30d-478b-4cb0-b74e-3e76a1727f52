<template>
  <div>
    <stg-dialog
      ref="ftpControlDlg"
      type="drawer"
      :title="$t('pages.ftpControlStg')"
      :title-tip="$t('pages.ftp_msg', {content: $t('route.FtpControlConfig')} )"
      :width="800"
      :stg-code="205"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      time-able
      class="ftp-stg-dlg"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('pages.ftpBlockConfig')" name="first">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
              <div slot="header">
                <span>{{ $t('pages.ftpControl_text10') }}</span>
              </div>
              <tag ref="addressTag" :border="true" :list="temp.addressList" :valid-rule="addressRule" :disabled="!formable" min-height="40px"/>
            </el-card>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <el-radio-group v-model="temp.detectionRule" :disabled="!formable" style="margin-left: 18px;">
              <el-radio :label="1">{{ $t('pages.ftpControl_text8') }}</el-radio>
              <el-radio :label="0">{{ $t('pages.ftpControl_text9') }}</el-radio>
            </el-radio-group>
            <el-row style="margin-left: 18px">
              <el-col :span="24">
                <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.executeRuleTip', {info: $t('pages.ftpControl_text10'), info1: $t('pages.ftpControl_text10')}) }}</label>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.withinDetectionRuleTip">
                      <span slot="info">{{ $t('pages.allowFileToFtp') }}</span>
                    </i18n>
                    <br/>
                    <i18n path="pages.outsideDetectionRuleTip1">
                      <span slot="info">{{ $t('pages.forbidFileToFtp') }}</span>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-col>
            </el-row>
            <!--<FormItem>
              <label slot="label">{{ $t('pages.processMonitor_Msg1') }}<el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.ftpControl_text3') }}<br/>
                  {{ $t('pages.ftpControl_text4') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>:</label>
              <el-row>
                <label style="font-weight: 500;">{{ $t('pages.ftpControl_text5') }}</label>
                <el-input-number v-model="temp.limitFileSize" :disabled="!formable || disable" :controls="false" :min="0" :max="10240" size="mini" style="width: 80px;"/>
                <label style="font-weight: 500;">{{ $t('pages.ftpControl_text6') }}</label>
                <label style="font-weight: 500;color: rgb(43, 122, 172);margin-left:20px">{{ $t('pages.ImFile_text1') }}</label>
                <el-button style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
              </el-row>
            </FormItem>-->
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <el-row style="margin-left: 18px;">
              <el-col :span="16">
                <el-checkbox-group v-model="approvalTypeList" style="display: inline-block" :disabled="!formable" @change="handleApprovalTypeChange">
                  <el-checkbox :label="1">{{ hasEncPermision ? $t('pages.burnMsg4') : $t('pages.burnMsg5') }}</el-checkbox>
                  <el-checkbox :disabled="approvalTypeList.indexOf(1) == -1" :label="4">{{ hasEncPermision ? $t('pages.burnMsg6') : $t('pages.burnMsg7') }}</el-checkbox>
                  <el-checkbox v-if="hasEncPermision" style="margin-top: 15px;" :label="2">{{ $t('pages.burnMsg8') }}</el-checkbox>
                  <el-checkbox v-if="hasEncPermision" style="margin-top: 15px;" :disabled="approvalTypeList.indexOf(2) == -1" :label="8">{{ $t('pages.burnMsg9') }}</el-checkbox>
                </el-checkbox-group>
              </el-col>
            </el-row>
            <ResponseContent
              ref="resContent"
              :select-style="{ 'margin-top': '10px' }"
              :show-select="true"
              :rule-type-name="ruleTypeName"
              :editable="formable && ruleDisable"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="propRuleId"
              @ruleIsCheck="getRuleIsCheck"
              @getRuleId="getRuleId"
            />
            <ResponseContent
              v-if="hasEncPermision"
              ref="resContentEnc"
              :show-select="true"
              :rule-type-name="encRuleTypeName"
              :editable="formable && encRuleDisable"
              read-only
              :prop-check-rule="!!temp.isEncAlarm"
              :show-check-rule="true"
              :prop-rule-id="encPropRuleId"
              @ruleIsCheck="getEncRuleIsCheck"
              @getRuleId="getEncRuleId"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.ftpRecordConfig')" name="second">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <data-editor
                :formable="formable"
                :popover-width="400"
                :append-to-body="true"
                :updateable="recordAddressEditable"
                :deletable="recordAddressDeleteable"
                :add-func="createRecordAddress"
                :update-func="updateRecordAddress"
                :delete-func="deleteRecordAddress"
                :cancel-func="cancelRecordAddress"
                :before-update="beforeupdateRecordAddress"
              >
                <Form ref="recordAddressForm" :model="tempA" :rules="tempARules" label-position="right" label-width="90px" style="width: 330px;">
                  <FormItem :label="$t('table.auditAddress')" prop="address">
                    <el-input v-model="tempA.address" maxlength="254"/>
                  </FormItem>
                  <FormItem :label="$t('table.matchType')" prop="matchType">
                    <el-select v-model="tempA.matchType" style="width: 100%;">
                      <el-option :value="0" :label="$t('pages.matchTypeOptions2')"/>
                      <el-option :value="1" :label="$t('pages.matchTypeOptions1')"/>
                    </el-select>
                  </FormItem>
                  <FormItem :label="$t('table.remark')" prop="remark">
                    <el-input v-model="tempA.remark" type="textarea" show-word-limit maxlength="128"/>
                  </FormItem>
                </Form>
              </data-editor>
              <grid-table
                ref="recordAddressList"
                auto-height
                :multi-select="formable"
                :show-pager="false"
                :col-model="addressColModel"
                :row-datas="temp.recordAddresses"
                @selectionChangeEnd="recordAddressSelectionChange"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <el-radio-group v-model="temp.recordDetectionRule" :disabled="!formable" style="margin-left: 18px;">
              <el-radio :label="1">{{ $t('pages.ftpControl_text8') }}</el-radio>
              <el-radio :label="0">{{ $t('pages.ftpControl_text9') }}</el-radio>
            </el-radio-group>
            <el-row style="margin-left: 18px">
              <el-col :span="24">
                <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.executeRuleTip', {info: $t('pages.ftpControl_text10'), info1: $t('pages.ftpControl_text10')}) }}</label>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.withinDetectionRuleTip">
                      <span slot="info">{{ $t('pages.forbidRecordFileToFtp') }}</span>
                    </i18n>
                    <br/>
                    <i18n path="pages.outsideDetectionRuleTip">
                      <span slot="info">{{ $t('pages.allowRecordFileToFtp') }}</span>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-col>
            </el-row>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <!-- <FormItem label-width="80px" :label="$t('pages.ftpAudit')">
              <el-radio-group v-model="temp.isRecord" :disabled="!formable" @change="recordChange">
                <el-radio :label="1">{{ $t('pages.enable') }}</el-radio>
                <el-radio :label="0">{{ $t('pages.notEnabled') }}</el-radio>
              </el-radio-group>
            </FormItem> -->
            <!-- <FormItem>
              <label slot="label">{{ $t('pages.processMonitor_Msg1') }}<el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.ftpControl_text3') }}<br/>
                  {{ $t('pages.ftpControl_text4') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>:</label>
              <el-row>
                <i18n style="font-weight: 500;" path="pages.ftpControl_text5">
                  <el-input-number slot="size" v-model="temp.limitFileSize" :disabled="!formable || disable" :controls="false" :min="0" :max="10240" size="mini" style="width: 80px;"/>
                </i18n>
                <label style="font-weight: 500;color: rgb(43, 122, 172);margin-left:20px">{{ $t('pages.ImFile_text1') }}</label>
                <el-button style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
              </el-row>
            </FormItem> -->
            <div :class="inputClass">
              <FormItem label-width="22px">
                <el-checkbox v-model="temp.isRecord" :disabled="!formable" :true-label="1" :false-label="0" @change="recordChange">{{ $t('pages.recordFtpLog') }}</el-checkbox>
              </FormItem>
              <FormItem label-width="22px" prop="limitFileSize">
                <el-checkbox v-model="temp.isBackup" :disabled="!formable || !temp.isRecord" :true-label="1" :false-label="0" @change="backupchange">
                  <i18n style="font-weight: 500;" path="pages.ftpControl_text20">
                    <el-input-number slot="size" v-model="temp.limitFileSize" :disabled="!formable || disable || temp.isBackup === 0" :controls="false" step-strictly :min="1" :max="10240" size="mini" style="width: 80px;"/>
                  </i18n>
                  <el-tooltip effect="dark" placement="bottom">
                    <div slot="content" style="width: 480px;">{{ $t('pages.ftpControl_text21') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-checkbox>
                <el-button :disabled="!formable || !temp.isRecord" style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
              </FormItem>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>
    </stg-dialog>
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
  </div>
</template>

<script>
import { createStrategy, updateStrategy } from '@/api/behaviorManage/network/ftpControlConfig'
import { isIPv4, isIPv6 } from '@/utils/validate'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig';
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'

export default {
  name: 'FtpControlConfigDlg',
  components: { ResponseContent, BackupRuleContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      activeName: 'first',
      addressColModel: [
        { prop: 'address', label: 'auditAddress', width: '100', sort: true },
        { prop: 'matchType', label: 'matchType', width: '100', sort: true, sortOriginal: true, formatter: this.matchTypeFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: true }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        isBackup: 0,
        entityType: '',
        entityId: undefined,
        limitType: 0, // 1-限制外发 0-不限制外发
        isRecord: 0,  // FTP传输记录
        limitFileSize: 20,
        isAlarm: 0, // 明文文件触发响应规则
        isEncAlarm: 0, // 密文文件触发响应规则
        ruleId: undefined, // 明文文件响应规则id
        encRuleId: undefined, // 密文文件响应规则id
        approvalType: 0, // 位与 审批外发 1-明文文件禁止外发, 2-密文文件禁止外发, 4-允许明文文件外发, 8-允许密文文件外发,
        addressList: [], // 服务器ip
        detectionRule: 0,
        address: '',
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined, // 备份过滤规则id
        recordDetectionRule: 0, // 审计-检测条件
        recordAddresses: []  // 审计地址列表
      },
      tempA: {},
      defaultTempA: {
        id: undefined,
        address: '',
        matchType: 0 // 地址匹配类型: 0-完全匹配，1-模糊匹配
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: ' ' }],
        addressList: [{ validator: this.addressListValidator, trigger: 'blur' }],
        action: [{ validator: this.actionValidator, trigger: 'blur' }],
        limitFileSize: [{ validator: this.fileBackUpLimitValidator, trigger: 'blur' }]
      },
      tempARules: {
        address: [
          { required: true, message: this.$t('pages.ftpControl_text19'), trigger: 'blur' },
          { validator: this.addressValidator1, trigger: 'blur' }
        ]
      },
      addressRule: [
        { validator: this.addressValidator, trigger: 'blur' }
      ],
      matchTypeOptions: {
        0: this.$t('pages.matchTypeOptions2'),
        1: this.$t('pages.matchTypeOptions1')
      },
      typeTreeData: [],
      propRuleId: undefined,
      encPropRuleId: undefined, // 密文文件响应规则id
      approvalTypeList: [], // 审批外发
      hasEncPermision: true,  // 是包含加解密模块
      // 触发明文文件响应规则
      ruleTypeName: this.$t('pages.burnMsg10'),
      // 触发密文文件响应规则
      encRuleTypeName: this.$t('pages.burnMsg11'),
      validRuleId: true,
      validEncRuleId: true,
      ruleDisable: false, // 当明文执行规则没有配置时,明文响应规则要置灰
      encRuleDisable: false, // 当密文执行规则没有配置时,明文响应规则要置灰
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined, // 备份过滤规则id
      recordAddressEditable: false,
      recordAddressDeleteable: false,
      inputClass: 'zh-tw',
      ftpAddressError: false  // 标记正在输入的FTP服务器IP是否有错误
    }
  },
  computed: {
    disable() {
      return !this.temp.isRecord
    }
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
    /* // 获取注册模块
    this.listModule() */
    this.getLanguage()
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
      // if (this.temp.ruleId) {
      //   this.propRuleId = this.temp.ruleId
      // }
    },
    /* async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = false
      })
    }, */
    // 获取当前语言系统，设置文件备份阈值输入框的class
    getLanguage() {
      const currentLang = this.$store.getters.language
      if (currentLang != undefined || currentLang != null || currentLang != '') {
        if (currentLang == 'zh' || currentLang == 'tw') {
          this.inputClass = 'zh-tw'
        } else {
          this.inputClass = 'en'
        }
      }
    },
    closed() {
      this.resetTemp()
    },
    recordAddressTable() {
      return this.$refs['recordAddressList']
    },
    recordAddressSelectionChange(rowDatas) {
      this.recordAddressDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.recordAddressEditable = true
      } else {
        this.recordAddressEditable = false
        this.cancelRecordAddress()
      }
    },
    createRecordAddress() {
      let validate
      this.$refs['recordAddressForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempA))
          rowData.id = new Date().getTime()
          this.temp.recordAddresses.unshift(rowData)
          this.cancelRecordAddress()
          validate = valid
        }
      })
      return validate
    },
    updateRecordAddress() {
      let validate
      this.$refs['recordAddressForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempA))
          for (let i = 0, size = this.temp.recordAddresses.length; i < size; i++) {
            const data = this.temp.recordAddresses[i]
            if (rowData.id === data.id) {
              this.temp.recordAddresses.splice(i, 1, rowData)
              break
            }
          }
          this.cancelRecordAddress()
          validate = valid
        }
      })
      return validate
    },
    deleteRecordAddress() {
      this.cancelRecordAddress()
      const toDeleteIds = this.recordAddressTable().getSelectedIds()
      this.recordAddressTable().deleteRowData(toDeleteIds, this.temp.recordAddresses)
    },
    cancelRecordAddress() {
      this.$refs['recordAddressForm'] && this.$refs['recordAddressForm'].clearValidate()
      this.resetTempA()
    },
    beforeupdateRecordAddress() {
      Object.assign(this.tempA, this.recordAddressTable().getSelectedDatas()[0])
    },
    resetTempA() {
      this.tempA = Object.assign({}, this.defaultTempA)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.propRuleId = undefined
      this.encPropRuleId = undefined
      this.approvalTypeList.splice(0)
      this.validRuleId = true
      this.validEncRuleId = true
      this.ruleDisable = false
      this.encRuleDisable = false
      this.$refs['addressTag'] && this.$refs['addressTag'].clearInputValue()
      this.resetTempA()
    },
    handleCreate(data) {
      this.resetTemp()
      this.activeName = 'first'
      this.hasEncPermision = data
      this.ruleTypeName = this.hasEncPermision ? this.$t('pages.burnMsg10') : this.$t('pages.triggerResponseRule')
      this.$refs['ftpControlDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      }, undefined, () => {
        this.$nextTick(() => { // 新增弹框打开时，清除响应规则的选中情况
          if (this.$refs['resContent']) {
            this.$refs['resContent'].ruleId = undefined
          }
          if (this.$refs['resContentEnc']) {
            this.$refs['resContentEnc'].ruleId = undefined
          }
        })
      })
    },
    async handleUpdate(row) {
      this.resetTemp()
      this.activeName = 'first'
      //  若hasEncPermision为未定义，重新获取加解密模块，兼容策略总览查看时没有hasEncPermision指
      if (row.hasEncPermision === undefined) {
        await existSaleModule(51).then(resp => {
          row.hasEncPermision = resp.data
        })
      }
      this.hasEncPermision = row.hasEncPermision
      this.ruleTypeName = this.hasEncPermision ? this.$t('pages.burnMsg10') : this.$t('pages.triggerResponseRule')
      this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row)))
      // 统一样式，当不勾选备份复选框时，备份的阀值设置为0，此时前端界面展示空的输入框
      if (!row.limitFileSize) {
        this.temp.limitFileSize = undefined
        this.temp.isBackup = 0
      } else { // 旧策略没有这个字段
        this.temp.isBackup = 1
      }
      // 兼容旧版本，旧版本原先就有禁止传输的功能（根据是否有approvalType这个字段来判断是否是旧版本）
      if (row.approvalType == null || row.approvalType == undefined) {
        if (this.hasEncPermision) {
          if (this.temp.limitType === 1) {
            this.approvalTypeList.push(1)
            this.approvalTypeList.push(2)
          }
          if (row.ruleId) {
            this.propRuleId = row.ruleId
            this.encPropRuleId = row.ruleId
            this.temp.isAlarm = 1
            this.temp.isEncAlarm = 1
          }
        } else {
          if (this.temp.limitType === 1) {
            this.approvalTypeList.push(1)
          }
          if (row.ruleId) {
            this.propRuleId = row.ruleId
          }
        }
      } else {
        if (this.temp.approvalType > 0) {
          this.approvalTypeList = this.numToList(this.temp.approvalType, 4)
          // 当勾选的有允许文件外发审批时，approvalType是没有保存禁止外发的值的，所以这边要做下处理
          if (this.approvalTypeList.indexOf(4) > -1) {
            this.approvalTypeList.push(1)
          }
          if (this.approvalTypeList.indexOf(8) > -1) {
            this.approvalTypeList.push(2)
          }
        }
        this.propRuleId = row.ruleId
        this.encPropRuleId = row.encRuleId
      }
      // 当明文执行规则没配置时,无法配置明文响应规则
      if (this.approvalTypeList.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      if (row.addressList) {
        this.temp.addressList = row.addressList.map(item => item.address)
      }
      this.$refs['ftpControlDlg'].show(this.temp, this.formable)
    },
    handleShow(row) {
      this.resetTemp()
      this.activeName = 'first'
      this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row)))
      // 统一样式，当不勾选备份复选框时，备份的阀值设置为0，此时前端界面展示空的输入框
      if (this.temp.limitFileSize == 0) {
        this.temp.limitFileSize = undefined
        this.temp.isBackup = 0
      } else { // 旧策略没有这个字段
        this.temp.isBackup = 1
      }
      // 兼容旧版本，旧版本原先就有禁止传输的功能（根据是否有approvalType这个字段来判断是否是旧版本）
      if (row.approvalType == null || row.approvalType == undefined) {
        if (this.hasEncPermision) {
          if (this.temp.limitType === 1) {
            this.approvalTypeList.push(1)
            this.approvalTypeList.push(2)
          }
          if (row.ruleId) {
            this.propRuleId = row.ruleId
            this.encPropRuleId = row.ruleId
          }
        } else {
          if (this.temp.limitType === 1) {
            this.approvalTypeList.push(1)
          }
          if (row.ruleId) {
            this.propRuleId = row.ruleId
          }
        }
      } else {
        if (this.temp.approvalType > 0) {
          this.approvalTypeList = this.numToList(this.temp.approvalType, 4)
          // 当勾选的有允许文件外发审批时，approvalType是没有保存禁止外发的值的，所以这边要做下处理
          if (this.approvalTypeList.indexOf(4) > -1) {
            this.approvalTypeList.push(1)
          }
          if (this.approvalTypeList.indexOf(8) > -1) {
            this.approvalTypeList.push(2)
          }
        }
        this.propRuleId = row.ruleId
        this.encPropRuleId = row.encRuleId
      }
      this.$refs['ftpControlDlg'].isGeneralStrategy = false
      this.$refs['ftpControlDlg'].show(this.temp, false)
    },
    validateFormData(formData) {
      this.validRuleId = true
      this.validEncRuleId = true
      if (formData.isAlarm === 1 && !this.propRuleId) {
        return false
      }
      if (formData.isEncAlarm === 1 && !this.encPropRuleId) {
        return false
      }
      if (this.ftpAddressError) {
        return false
      }
      return true
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    formatFormData(data) {
      data.ruleId = this.propRuleId
      data.encRuleId = this.encPropRuleId
      // 当勾选了允许审批外发时，要去除掉禁止的值比如禁止审批外发为1允许审批外发为4两个都勾选时approvalType只保存4
      data.approvalType = this.getSum(this.approvalTypeList)
      if (this.approvalTypeList.indexOf(4) > -1) {
        data.approvalType = data.approvalType - 1
      }
      if (this.approvalTypeList.indexOf(8) > -1) {
        data.approvalType = data.approvalType - 2
      }
      // 处理禁止传输的值,兼容旧版本
      if (this.hasEncPermision) {
        // 有加解密模块当禁止明文传输跟禁止密文传输都禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(2) > -1 && this.approvalTypeList.indexOf(4) == -1 && this.approvalTypeList.indexOf(8) == -1) {
          data.limitType = 1
        } else {
          data.limitType = 0
        }
      } else {
        // 无加解密模块，当禁止明文传输为禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(4) == -1) {
          data.limitType = 1
        } else {
          data.limitType = 0
        }
      }
      this.validRuleId = true
      this.validEncRuleId = true
      if (data.isAlarm === 1 && !data.ruleId) {
        this.validRuleId = false
      }
      if (data.isEncAlarm === 1 && !data.encRuleId) {
        this.validEncRuleId = false
      }
      if (data.addressList) {
        data.address = data.addressList.join(',')
        const addressList = []
        data.addressList.forEach(item => {
          const info = { address: undefined }
          info.address = item
          addressList.push(info)
        })
        data.addressList.splice(0, data.addressList.length, ...addressList)
      }
      if (!data.isBackup) {
        data.limitFileSize = 0
      }
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    getEncRuleId(value) {
      this.encPropRuleId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    getEncRuleIsCheck(value) {
      this.temp.isEncAlarm = value
    },
    handleApprovalTypeChange(value) {
      if (value.indexOf(4) > -1 && value.indexOf(1) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(4), 1)
      }
      if (value.indexOf(8) > -1 && value.indexOf(2) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(8), 1)
      }
      // 勾选了执行规则，响应规则才能勾选
      if (value.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (value.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
    },
    async addressListValidator(rule, value, callback) {
      const addressValid = await this.$refs.addressTag.validateForm()
      if (!addressValid) {
        callback(new Error(' '))
      } else {
        callback()
      }
    },
    actionValidator(rule, value, callback) {
      if (!this.temp.isRecord && this.approvalTypeList.length === 0) {
        callback(new Error(this.$t('pages.install_Msg14')))
      } else {
        callback()
      }
    },
    addressValidator(rule, value, callback) {
      this.ftpAddressError = false
      if (!value) {
        callback()
      } else if (isIPv4(value) || isIPv6(value)) {
        callback()
      } else {
        this.ftpAddressError = true
        callback(new Error(this.$t('pages.ftpControl_address_validator')))
      }
    },
    addressValidator1(rule, value, callback) {
      const ipList = this.recordAddressTable().getDatas()
      const index = ipList.findIndex(item => {
        if (item.id != this.tempA.id && item.address == this.tempA.address) {
          return true
        } else {
          return false
        }
      })
      if (index !== -1) {
        callback(new Error(this.$t('pages.validateMsg_sameServerAddress')))
      } else {
        callback()
      }
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.temp.isBackup == 1) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else if (value > 10240) {
          return callback(new Error(this.$t('pages.blueTooth_Msg4')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    },
    matchTypeFormatter(row, data) {
      return this.matchTypeOptions[row.matchType]
    },
    recordChange(val) {
      if (!val && this.temp.isBackup === 1) {
        this.backupchange(0)
      }
    },
    backupchange(val) {
      if (!val) {
        const ftpControl = this.$refs['ftpControlDlg']
        if (ftpControl) {
          const formDlg = ftpControl.$refs['dataForm4StgDialog' + this.slotName]
          formDlg && formDlg.clearValidate('limitFileSize')
        }
      }
      this.temp.isBackup = val
    }
  }
}
</script>
<style lang="scss" scoped>
.ftp-stg-dlg {
  >>>.first-divider {
    margin-top: 15px;
  }
}
.zh-tw  {
  >>>.el-form-item__error {
    margin-left: 163px;
  }
}
.en  {
  >>>.el-form-item__error {
    margin-left: 285px;
  }
}
</style>
