<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 20px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <FormItem :label="$t('table.stgLimit')" prop="remark">
          <el-radio v-for="(value, key) in modeOptions" :key="key" v-model="temp.mode" :disabled="!formable" :label="parseInt(key)" class="ellipsis">
            <span :title="value">{{ value }}</span>
          </el-radio>
        </FormItem>
        <assign-except-url-table
          ref="assignExceptUrlTable"
          :tree-select-node="treeSelectNode"
          style="margin-left: 20px"
          :formable="formable"
          :mode-disabled="modeDisabled"
          @handle-add="handleUrlCreate"
          @handle-import="handleUrlImport"
          @handle-update="handleUrlUpdate"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'A53'" :link-url="'/system/baseData/urlLibrary'" :btn-text="$t('pages.maintainUrl')"/>
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData(): dialogStatus==='update'? updateData() : {}">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 网址的新增修改-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[urlDialogStatus]"
      :visible.sync="urlDialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="urlDataForm"
        :rules="urlRules"
        :model="urlTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('pages.websiteName')" prop="name">
          <el-input v-model="urlTemp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.websiteUrl')" prop="address">
          <el-input v-model="urlTemp.address" v-trim :maxlength="60" :placeholder="$t('pages.urlLibrary_text1')"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="addGroupAble ? 21 : 24">
              <el-select v-model="urlTemp.groupId" filterable :placeholder="$t('text.select')">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
            </el-col>
            <el-col v-show="addGroupAble" style="padding-top:1px" :span="3">
              <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="urlTemp.remark" type="textarea" rows="3" resize="none" maxlength="100" show-word-limit/>
        </FormItem>
        <FormItem v-if="!hiddenDealWay" :label="$t('pages.treatment')">
          <el-radio-group v-model="dealWay">
            <el-radio :label="0">{{ this.$t('pages.assignUrl') }}</el-radio>
            <el-radio :label="1">{{ this.$t('pages.exceptUrl') }}</el-radio>
          </el-radio-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="urlDialogStatus==='createUrl'?createUrlData(): urlDialogStatus==='updateUrl'? updateUrlData() : {}">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="urlDialogFormVisible = false;">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-table-dlg
      ref="urlImportTable"
      :group-tree-height="430"
      tree-root-data-id="0"
      :not-selected-prompt-message="$t('pages.url_text2')"
      :elg-title="$t('pages.importUrlLibrary')"
      :group-root-name="$t('pages.urlLibrary')"
      :search-info-name="$t('pages.websiteNameOrURL')"
      :confirm-button-name="$t('pages.addUrl')"
      :group-title="$t('pages.url_group_title')"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="getTreeNode"
      :create-group="createUrlGroup"
      :update-group="updateUrlGroup"
      :delete-group="deleteUrlGroup"
      :count-by-group="countUrlByGroupId"
      :get-group-by-name="getUrlGroupByName"
      :delete="deleteUrl"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteUrlGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleCreateElg"
      :get-list-by-group-ids="listUrlByGroupId"
      @submitEnd="getNeedAddIds"
      @submitDeleteEnd="submitDeleteEnd"
      @changeGroupAfter="changeGroupAfter"
    >
      <template>
        <div style="margin-top: 5px">
          <label style="margin-right: 10px;">{{ $t('pages.treatment') }}</label>
          <el-radio-group v-model="importDealWay" style="display: inline-block;">
            <el-radio :label="0">{{ this.$t('pages.assignUrl') }}</el-radio>
            <el-radio :label="1">{{ this.$t('pages.exceptUrl') }}</el-radio>
          </el-radio-group>
        </div>
      </template>
    </import-table-dlg>

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importUrlStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />

    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :edit-valid-func="getUrlGroupByName"
      :add-func="createUrlGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>

<script>
import {
  createStrategy, deleteStrategy, getByName, getStrategyList, updateStrategy
} from '@/api/behaviorManage/network/url'
import {
  countUrlByGroupId, createUrl, createUrlGroup, deleteGroupAndData, deleteUrl, deleteUrlGroup, getByIds,
  getTreeNode, getUrlByAdress, getUrlGroupByName, getUrlList,
  listUrlByGroupId, moveGroupToOther, updateUrl, updateUrlGroup
} from '@/api/system/baseData/urlLibrary'
import {
  buttonFormatter, enableStgBtn, enableStgDelete, entityLink,
  hiddenActiveAndEntity, objectFormatter, refreshPage, selectable
} from '@/utils'
import { exportStg } from '@/api/stgCommon'
import { validatePolicy, timeIdValidator } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import { findNodeLabel } from '@/utils/tree'

import ImportStg from '@/views/common/importStg'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'
import AssignExceptUrlTable from '@/views/behaviorManage/network/url/assignExceptUrlTable.vue'

export default {
  name: 'Url',
  components: { AssignExceptUrlTable, ImportTableDlg, ImportStg, EditGroupDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 12,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '100', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'mode', label: 'limitWay', width: '200', formatter: this.modeFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'websiteName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: this.$t('pages.websiteUrl'), width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdateElg }
          ]
        }
      ],
      modeOptions: { 1: this.$t('pages.modeOptions1'), 2: this.$t('pages.modeOptions2'), 3: this.$t('pages.modeOptions3') },
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      treeable: true,
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        mode: 1,
        active: false,
        remark: '',
        timeId: 1,
        urlIds: [],
        exceptUrlIds: [],
        entityType: '',
        entityId: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.urlStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.urlStg'), 'create'),
        createUrl: this.i18nConcatText(this.$t('table.websiteUrl'), 'create'),
        updateUrl: this.i18nConcatText(this.$t('table.websiteUrl'), 'update')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ]
      },
      submitting: false,
      iconOption: { G: 'group', '': 'ie' },
      tempUrlElgData: [], // 临时列表数据
      urlDialogFormVisible: false,
      urlRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_enterUrl'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }
        ]
      },
      treeSelectNode: [],
      urlTemp: {},
      defaultUrlTemp: { // 表单字段
        id: undefined,
        name: '',
        remark: '',
        address: '',
        groupId: ''
      },
      urlQuery: {
        searchInfo: ''
      },
      urlDeleteable: false,
      urlDialogStatus: '',
      importCreateFlag: false,
      importUpdateFlag: false,
      dlgSubmitting: false,
      addGroupAble: false,  //  添加数据时，是否显示添加分组按钮
      hiddenDealWay: false,  //  添加数据时，是否显示处理方式
      dealWay: 0, // 0指定网址  1例外网址
      importDealWay: 0, // 0指定网址  1例外网址
      needValidateAddress: true
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    gridTable() {
      return this.$refs['strategyList']
    },
    modeDisabled() {
      return this.temp.mode === 3
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
    this.listTreeNode()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
    this.listTreeNode().then(() => {
      if (this.urlDialogFormVisible) {
        const groupInfo = this.treeSelectNode.find(node => node.dataId == this.urlTemp.groupId)
        !groupInfo && (this.urlTemp.groupId = undefined)
      }
    })
  },
  methods: {
    listUrlByGroupId,
    createUrlGroup,
    updateUrlGroup,
    deleteUrlGroup,
    getUrlGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    countUrlByGroupId,
    getTreeNode,
    deleteUrl,
    selectable(row, index) {
      return selectable(row, index)
    },
    selectableElg(row, index) {
      return selectable(row, index) && !this.modeDisabled
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['assignExceptUrlTable'].showTableData()
        this.$refs['assignExceptUrlTable'].clearSearchInfo()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUrlCreate() {
      this.dealWay = 0
      this.addGroupAble = true
      this.hiddenDealWay = false
      this.importCreateFlag = false
      this.needValidateAddress = false
      this.resetUrlTemp()
      this.listTreeNode();
      this.urlDialogStatus = 'createUrl'
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
      this.urlDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      const { urlIds = [], exceptUrlIds = [] } = row
      const ids = urlIds.toString()
      const exceptIds = exceptUrlIds.toString()
      Promise.all([
        getByIds({ ids: ids }),
        getByIds({ ids: exceptIds })
      ]).then(datas => {
        const [urlData, exceptData] = datas
        const assignDatas = urlData.data.map(data => {
          data.dealWay = 0
          return data
        })
        const exceptDatas = exceptData.data.map(data => {
          data.dealWay = 1
          return data
        })
        this.$refs['assignExceptUrlTable'].showTableData(assignDatas, exceptDatas)
      })
      this.$nextTick(() => {
        this.$refs['assignExceptUrlTable'].clearSearchInfo()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUrlUpdate(row) {
      this.addGroupAble = true
      this.hiddenDealWay = false
      this.needValidateAddress = true
      this.urlDialogStatus = 'updateUrl'
      this.resetUrlTemp()
      this.urlTemp = Object.assign(this.urlTemp, row)
      this.dealWay = row.dealWay
      this.urlTemp.groupId = this.urlTemp.groupId ? this.urlTemp.groupId + '' : '';
      this.listTreeNode()
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
      this.urlDialogFormVisible = true
    },
    handleUpdateElg(row) {
      this.importUpdateFlag = true
      this.handleUrlUpdate(row)
      this.addGroupAble = false
      this.hiddenDealWay = true
      this.needValidateAddress = true
    },
    handleCreateElg(selectedGroupId, flag) {
      this.urlDialogStatus = 'createUrl'
      this.listTreeNode()
      this.importCreateFlag = flag || false
      this.urlTemp = Object.assign({}, this.defaultUrlTemp)
      this.urlTemp.groupId = selectedGroupId
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
      // 隐藏新增网址时的新增分组按钮
      this.addGroupAble = false
      // 隐藏处理方式
      this.hiddenDealWay = true
      this.urlDialogFormVisible = true
      this.needValidateAddress = true
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const rowDatas = this.$refs['assignExceptUrlTable'].getRowDatas()
          this.temp.urlIds = rowDatas[0].children.map(item => item.id)
          this.temp.exceptUrlIds = rowDatas[1].children.map(item => item.id)
          this.verifyMode()
          createStrategy(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const rowDatas = this.$refs['assignExceptUrlTable'].getRowDatas()
          this.temp.urlIds = rowDatas[0].children.map(item => item.id)
          this.temp.exceptUrlIds = rowDatas[1].children.map(item => item.id)
          this.verifyMode()
          const tempData = Object.assign({}, this.temp)
          updateStrategy(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    modeFormatter: function(row, data) {
      return this.modeOptions[data]
    },
    strategyNameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    resetUrlTemp() {
      this.urlTemp = Object.assign({}, this.defaultUrlTemp)
    },
    //  获取分组数据
    listTreeNode() {
      return getTreeNode().then(res => {
        this.treeSelectNode = res.data || []
      })
    },
    handleUrlImport() {
      this.$nextTick(() => {
        this.importDealWay = 0
        this.$refs['urlImportTable'].show()
      })
    },
    addOrUpdateToAssignExceptTable(data, type) {
      if (type === 'create') {
        if (this.importCreateFlag) {
          // 如果在导入的弹窗中点击的新增按钮
          this.$refs['urlImportTable'].justRefreshTableData()
        } else {
          // 如果在添加策略的弹窗中点击的新增按钮
          this.$refs['assignExceptUrlTable'].createTableData([data])
        }
        this.importCreateFlag = false
      } else if (type === 'update') {
        if (this.importUpdateFlag) {
          // 如果在导入的弹窗中点击的修改按钮
          this.$refs['urlImportTable'].justRefreshTableData()
          // 导入弹窗中修改的网址，策略界面的网址需同步修改
          this.$refs['assignExceptUrlTable'].refreshRowDatas()
        } else {
          this.$refs['assignExceptUrlTable'].updateTableData([data])
        }
        this.importUpdateFlag = false
      }
    },
    createUrlData() {
      this.dlgSubmitting = true
      this.$refs['urlDataForm'].validate((valid) => {
        if (valid) {
          getUrlByAdress({ url: this.urlTemp.address }).then(respond => {
            const url = respond.data
            if (url !== undefined) {
              this.dlgSubmitting = false
              url.dealWay = this.dealWay
              this.addOrUpdateToAssignExceptTable(url, 'create')
              this.urlDialogFormVisible = false
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.urlExistDirectAddToStg'),
                type: 'success',
                duration: 2000
              })
            } else {
              // 网址不存在，先将网址新增到网址信息库，再将网址添加到策略
              this.urlTemp.groupName = findNodeLabel(this.treeSelectNode, this.urlTemp.groupId, 'dataId')
              createUrl(this.urlTemp).then(respond => {
                const url = respond.data
                url.dealWay = this.dealWay
                this.dlgSubmitting = false
                this.addOrUpdateToAssignExceptTable(url, 'create')
                this.urlDialogFormVisible = false
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.createSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(() => {
                this.dlgSubmitting = false
              })
            }
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    updateUrlData() {
      this.dlgSubmitting = true
      this.$refs['urlDataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.urlTemp)
          updateUrl(tempData).then(respond => {
            const url = respond.data
            url.dealWay = this.dealWay
            this.addOrUpdateToAssignExceptTable(url, 'update')
            this.dlgSubmitting = false
            this.urlDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    //  校验mode
    verifyMode() {
      if (this.modeDisabled) {
        this.temp.urlIdStr = '';
        this.temp.urlIds = []
        this.temp.exceptUrlIds = []
      }
    },
    //  导入方法的row-data-api
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getUrlList(searchQuery)
    },
    getNeedAddIds(needIds) {
      this.listTreeNode()
      const ids = needIds.toString()
      getByIds({ ids: ids }).then(res => {
        const datas = res.data.map(data => {
          data.dealWay = this.importDealWay
          return data
        })
        this.$refs['assignExceptUrlTable'].createTableData(datas)
      })
    },
    addressValidator(rule, value, callback) {
      if (this.needValidateAddress) {
        getUrlByAdress({ url: value }).then(respond => {
          const strategy = respond.data
          if (strategy && strategy.id != this.urlTemp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      } else {
        callback()
      }
    },
    //  弹窗分组树改变时
    changeGroupAfter() {
      this.listTreeNode()
      this.$refs['assignExceptUrlTable'].refreshRowDatas()
    },
    handleTypeCreate() {
      this.listTreeNode()
      this.$refs['createGroupDlg'].handleCreate();
    },
    createGroupAddEnd(row) {
      this.listTreeNode();
      this.urlTemp.groupId = row.id + ''
    },
    submitDeleteEnd(deleteIdsList) {
      this.$refs['assignExceptUrlTable'].refreshRowDatas()
    }
  }
}
</script>
