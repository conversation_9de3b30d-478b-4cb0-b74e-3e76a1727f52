<template>
  <div>
    <div class="toolbar">
      <el-button size="small" :disabled="!formable || modeDisabled" icon="el-icon-plus" @click.native="handleAdd">{{ $t('button.add') }}</el-button>
      <el-button size="small" :disabled="!formable || modeDisabled" icon="el-icon-plus" @click.native="handleImport">{{ $t('button.import') }}</el-button>
      <el-button size="small" :disabled="!deletable || !formable" icon="el-icon-edit" @click.native="handleEdit">{{ $t('button.edit') }}</el-button>
      <el-button size="small" :disabled="!deletable || !formable" icon="el-icon-delete" @click.native="handleDelete">{{ $t('button.delete') }}</el-button>
      <div class="searchCon">
        <el-input v-model="searchInfo" clearable :placeholder="$t('pages.url_text1')" style="width: 205px;" @keyup.enter.native="handleSearch(searchInfo)" />
        <el-button size="mini" type="primary" icon="el-icon-search" @click="handleSearch(searchInfo)">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="assignExceptUrlList"
      :stripe="false"
      default-expand-all
      :height="235"
      :col-model="colModel"
      :multi-select="formable && !modeDisabled"
      :check-strictly="true"
      :selectable="() => { return formable && !modeDisabled }"
      :cell-style="cellStyle"
      :row-data-api="rowDataApi"
      :show-pager="false"
      @selectionChangeEnd="handleSelectionChange"
    />

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.batchUpdateDealWay')"
      :visible.sync="dealWayDialogVisible"
      width="700px"
    >
      <grid-table
        ref="dealWayUrlList"
        :stripe="false"
        default-expand-all
        :height="235"
        :col-model="dealWayColModel"
        :row-data-api="dealWayRowDataApi"
        :multi-select="false"
        :selectable="() => { return false }"
        :cell-style="cellStyle"
        :show-pager="false"
      />
      <div>
        <FormItem style="margin-top: 10px" :label="$t('pages.treatment')">
          <el-radio-group v-model="dealWay">
            <el-radio :label="0">{{ this.$t('pages.assignUrl') }}</el-radio>
            <el-radio :label="1">{{ this.$t('pages.exceptUrl') }}</el-radio>
          </el-radio-group>
        </FormItem>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="updateDealWay">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dealWayDialogVisible = false;">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getByIds } from '@/api/system/baseData/urlLibrary';
export default {
  name: 'AssignExceptUrlTable',
  props: {
    formable: {
      type: Boolean,
      default: true
    },
    modeDisabled: {
      type: Boolean,
      default: true
    },
    rowData: {
      type: Array,
      default() {
        return [];
      }
    },
    treeSelectNode: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'websiteName', width: '150' },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.groupFormatter },
        { prop: 'address', label: this.$t('pages.websiteUrl'), width: '150' },
        { prop: 'remark', label: 'remark', width: '200' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [{
            label: 'edit',
            disabledFormatter: () => { return !this.formable || this.modeDisabled },
            formatter: this.buttonFormatter,
            click: (row) => { this.handleUpdate(row) }
          }]
        }
      ],
      dealWayColModel: [
        { prop: 'name', label: 'websiteName', width: '150' },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.groupFormatter },
        { prop: 'address', label: this.$t('pages.websiteUrl'), width: '150' },
        { prop: 'remark', label: 'remark', width: '200' }
      ],
      searchInfo: '',
      tempRowDatas: [], // 临时数据（通过搜索会变化）
      rowDatas: [], // 列表实际数据
      deletable: false,
      assignExceptNameMap: { 0: this.$t('pages.assignUrl'), 1: this.$t('pages.exceptUrl') },
      dealWayDialogVisible: false,
      dealWayRowDatas: [],
      dealWay: 0,
      submitting: false
    }
  },
  computed: {
    assignExceptUrlList() {
      return this.$refs['assignExceptUrlList']
    }
  },
  watch: {
    rowDatas: {
      handler(rowDatas) {
        const assignList = rowDatas[0].children || []
        const exceptList = rowDatas[1].children || []
        assignList.forEach(item => {
          item.groupName = this.getGroupNameByDataId(this.treeSelectNode, item.groupId)
        })
        exceptList.forEach(item => {
          item.groupName = this.getGroupNameByDataId(this.treeSelectNode, item.groupId)
        })
        this.handleSearch()
      },
      deep: true
    }
  },
  created() {
  },
  activated() {
    this.refreshRowDatas()
  },
  methods: {
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (row.type === 'group' && (!row.children || (row.children && row.children.length === 0))) {
        return 'display: none'
      }
      if (row.type === 'group') {
        return 'font-weight: bold'
      }
      return ''
    },
    rowDataApi() {
      return new Promise((resolve, reject) => { resolve({ data: this.tempRowDatas }) })
    },
    dealWayRowDataApi() {
      return new Promise((resolve, reject) => { resolve({ data: this.dealWayRowDatas }) })
    },
    handleAdd() {
      this.$emit('handle-add')
    },
    handleImport() {
      this.$emit('handle-import')
    },
    handleUpdate(row) {
      this.$emit('handle-update', row)
    },
    handleEdit() {
      this.dealWay = 0
      this.submitting = false
      this.dealWayRowDatas = [
        { name: this.assignExceptNameMap[0], type: 'group', id: 'assignGroup', children: [] },
        { name: this.assignExceptNameMap[1], type: 'group', id: 'exceptGroup', children: [] }
      ]
      const selectedDatas = this.assignExceptUrlList.getSelectedDatas()
      selectedDatas.forEach(item => {
        if (item.type === 'group') {
          if (item.id === 'assignGroup') {
            this.dealWayRowDatas[0].children.push(...item.children)
          } else if (item.id === 'exceptGroup') {
            this.dealWayRowDatas[1].children.push(...item.children)
          }
        } else {
          const dealWayList = this.dealWayRowDatas[item.dealWay].children
          const index = dealWayList.findIndex(data => data.id === item.id)
          if (index > -1) {
            dealWayList.splice(index, 1, item)
          } else {
            dealWayList.push(item)
          }
        }
      })
      this.dealWayDialogVisible = true
      this.$nextTick(() => {
        this.$refs['dealWayUrlList'].execRowDataApi()
      })
    },
    handleSelectionChange(rows) {
      this.deletable = rows.length > 0
    },
    showTableData(assignDatas, exceptDatas) {
      this.rowDatas = [
        { name: this.assignExceptNameMap[0], type: 'group', id: 'assignGroup', children: [] },
        { name: this.assignExceptNameMap[1], type: 'group', id: 'exceptGroup', children: [] }
      ]
      this.rowDatas[0].children = assignDatas || []
      this.rowDatas[1].children = exceptDatas || []
    },
    createTableData(datas) {
      datas.forEach(data => {
        const { group, index } = this.deleteDataExitsInTable(data)
        // 0指定网址  1例外网址
        const dealWay = data.dealWay
        const allList = this.rowDatas[dealWay].children || []
        if ((group === 'assignGroup' && dealWay === 0) || (group === 'exceptGroup' && dealWay === 1)) {
          if (index !== -1) {
            allList.splice(index, 0, data)
          } else {
            allList.push(data)
          }
        } else {
          allList.push(data)
        }
      })
    },
    updateTableData(datas) {
      for (const data of datas) {
        // 0指定网址  1例外网址
        const dealWay = data.dealWay
        const allAssignList = this.rowDatas[0].children || []
        const allExceptList = this.rowDatas[1].children || []
        if (dealWay === 0) {
          for (let i = 0; i < allExceptList.length; i++) {
            if (allExceptList[i].id === data.id) {
              allExceptList.splice(i, 1)
              break
            }
          }
          const filterList = allAssignList.filter(item => item.id === data.id)
          if (filterList.length > 0) {
            for (let i = 0; i < allAssignList.length; i++) {
              if (allAssignList[i].id === data.id) {
                allAssignList.splice(i, 1, data)
                break
              }
            }
          } else {
            allAssignList.push(data)
          }
        } else if (dealWay === 1) {
          for (let i = 0; i < allAssignList.length; i++) {
            if (allAssignList[i].id === data.id) {
              allAssignList.splice(i, 1)
              break
            }
          }
          const filterList = allExceptList.filter(item => item.id === data.id)
          if (filterList.length > 0) {
            for (let i = 0; i < allExceptList.length; i++) {
              if (allExceptList[i].id === data.id) {
                allExceptList.splice(i, 1, data)
                break
              }
            }
          } else {
            allExceptList.push(data)
          }
        }
      }
    },
    handleDelete() {
      const selectedDatas = this.assignExceptUrlList.getSelectedDatas();
      selectedDatas.forEach(item => {
        if (item.type === 'group') {
          if (item.id === 'assignGroup') {
            this.rowDatas[0].children = []
          } else if (item.id === 'exceptGroup') {
            this.rowDatas[1].children = []
          }
        } else {
          const allList = this.rowDatas[item.dealWay].children
          const index = allList.findIndex(data => data.id === item.id)
          allList.splice(index, 1)
        }
      })
      this.assignExceptUrlList.clearSelection()
    },
    refreshRowDatas() {
      const assignList = this.rowDatas[0] ? this.rowDatas[0].children : []
      const exceptList = this.rowDatas[1] ? this.rowDatas[1].children : []
      const ids = assignList.map(item => item.id).toString()
      const exceptIds = exceptList.map(item => item.id).toString()
      Promise.all([
        getByIds({ ids: ids }),
        getByIds({ ids: exceptIds })
      ]).then(datas => {
        const [urlData, exceptData] = datas
        const assignDatas = urlData.data.map(data => {
          data.dealWay = 0
          return data
        })
        const exceptDatas = exceptData.data.map(data => {
          data.dealWay = 1
          return data
        })
        this.showTableData(assignDatas, exceptDatas)
      })
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    buttonFormatter: function(row, data) {
      if (row.type === 'group') {
        return ''
      } else {
        return this.$t('table.edit')
      }
    },
    deleteDataExitsInTable(data) {
      const allAssignList = this.rowDatas[0] ? this.rowDatas[0].children : []
      const allExceptList = this.rowDatas[1] ? this.rowDatas[1].children : []
      for (let i = 0; i < allAssignList.length; i++) {
        if (allAssignList[i].id === data.id) {
          allAssignList.splice(i, 1)
          return { group: 'assignGroup', index: i }
        }
      }
      for (let i = 0; i < allExceptList.length; i++) {
        if (allExceptList[i].id === data.id) {
          allExceptList.splice(i, 1)
          return { group: 'exceptGroup', index: i }
        }
      }
      return { group: '', index: -1 }
    },
    handleSearch(searchInfo) {
      searchInfo = searchInfo === undefined ? this.searchInfo && this.searchInfo.trim() : searchInfo && searchInfo.trim() || ''
      searchInfo = searchInfo.toLowerCase()
      if (searchInfo === '') {
        this.tempRowDatas = JSON.parse(JSON.stringify(this.rowDatas))
      } else {
        this.tempRowDatas[0].children = this.rowDatas[0].children.filter(item => {
          return (item.name && item.name.toLowerCase().indexOf(searchInfo) !== -1) ||
            (item.address && item.address.toLowerCase().indexOf(searchInfo) !== -1) ||
            (item.groupName && item.groupName.toLowerCase().indexOf(searchInfo) !== -1)
        })
        this.tempRowDatas[1].children = this.rowDatas[1].children.filter(item => {
          return (item.name && item.name.toLowerCase().indexOf(searchInfo) !== -1) ||
            (item.address && item.address.toLowerCase().indexOf(searchInfo) !== -1) ||
            (item.groupName && item.groupName.toLowerCase().indexOf(searchInfo) !== -1)
        })
      }
      this.assignExceptUrlList.execRowDataApi()
    },
    getRowDatas() {
      return this.rowDatas
    },
    clearSearchInfo() {
      this.searchInfo = ''
    },
    updateDealWay() {
      this.submitting = true
      const datas = []
      this.dealWayRowDatas[0].children.forEach((url) => {
        url.dealWay = this.dealWay
        datas.push(url)
      })
      this.dealWayRowDatas[1].children.forEach((url) => {
        url.dealWay = this.dealWay
        datas.push(url)
      })
      this.updateTableData(datas)
      this.submitting = false
      this.dealWayDialogVisible = false
      this.assignExceptUrlList.clearSelection()
    }
  }
}
</script>

<style scoped>

</style>
