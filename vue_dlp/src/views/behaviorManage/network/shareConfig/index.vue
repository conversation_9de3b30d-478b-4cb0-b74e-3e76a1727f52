<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      class="share-stg-dlg"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-width="100px"
        style="width: 730px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <FormItem :label="$t('table.shareStatus')">
          <el-radio-group v-model="temp.shareStatus" :disabled="!formable">
            <el-radio :label="1">{{ $t('pages.allow') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.prohibit') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <el-tabs v-if="temp.shareStatus == 1" v-model="activeName" class="process-tab">
          <el-tab-pane :label="$t('pages.localShareConfig')" name="first">
            <FormItem v-if="temp.shareStatus == 1" :label="$t('table.localShareAudit')">
              <el-radio-group v-model="temp.localFileAuditStatus" :disabled="!formable" @change="localFileAuditStatusChange">
                <el-radio :label="1">{{ $t('pages.enable') }}</el-radio>
                <el-radio :label="0">{{ $t('pages.notEnabled') }}</el-radio>
              </el-radio-group>
            </FormItem>

            <el-divider content-position="left">
              {{ $t('pages.shareConfig_text10') }}
              <el-tooltip class="item" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.shareConfig_text11') }}<br>
                  {{ $t('pages.shareConfig_text12') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-divider>

            <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
              <div slot="header">
                <span>{{ $t('pages.monitor_suffix') }}</span>
                <el-popover
                  v-if="!tagDisabled"
                  v-model="visible1"
                  placement="right"
                  width="400"
                  trigger="click"
                  @after-leave="popoverLeave"
                >
                  <tree-menu ref="suffixList" style="height: 350px" multiple :data="localShareExtBaseDatas" :default-checked-keys="checkedKeys" :is-filter="true" :default-expand-all="true"/>
                  <div style="text-align: right; margin-top: 10px">
                    <el-button type="primary" size="mini" @click="handleCheckedSuffix">{{ $t('button.confirm') }}</el-button>
                    <el-button size="mini" @click="handleCancelCheck">{{ $t('button.cancel') }}</el-button>
                  </div>
                  <el-button v-if="formable" slot="reference" size="small">
                    {{ $t('button.localShareExtBaseImport') }}
                  </el-button>
                  <el-button v-if="formable" slot="reference" size="small" @click="handleClearSuffix">
                    {{ $t('button.clear') }}
                  </el-button>
                </el-popover>
              </div>
              <el-radio-group v-model="temp.fileExtSetup" :disabled="tagDisabled">
                <el-radio :label="1">
                  {{ $t('pages.processMonitor_Msg9') }}
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.processMonitor_Msg34', { type: $t('pages.process_Msg5') }) }}
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-radio>
                <el-radio :label="0">
                  {{ $t('pages.processMonitor_Msg10') }}
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.processMonitor_Msg36') }}
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
              <FormItem label-width="0px">
                <tag :list="temp.fileExtList" input-length="255" :add-able="addAble" :disabled="tagDisabled" @tagChange="fileExtListExtChange"/>
              </FormItem>
            </el-card>

            <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
              <div slot="header">
                <span>{{ $t('pages.monitor_dir') }}</span>
              </div>
              <el-radio-group v-model="temp.dirNameSetup" :disabled="tagDisabled">
                <el-radio :label="1">
                  {{ $t('pages.processMonitor_Msg13') }}
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.processMonitor_Msg34', { type: $t('pages.serverlog_file_directory') }) }}
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-radio>
                <el-radio :label="0">
                  {{ $t('pages.processMonitor_Msg32') }}
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.processMonitor_Msg35', { type: $t('pages.serverlog_file_directory') }) }}
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
              <FormItem label-width="0px">
                <tag :list="temp.dirNameList" input-length="255" :disabled="tagDisabled"/>
              </FormItem>
            </el-card>

            <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
              <div slot="header">
                <span>{{ $t('pages.monitor_fileName') }}</span>
              </div>
              <el-radio-group v-model="temp.fileNameSetup" :disabled="tagDisabled">
                <el-radio :label="1">
                  {{ $t('pages.processMonitor_Msg16') }}
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.processMonitor_Msg34', { type: $t('pages.fileName') }) }}
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-radio>
                <el-radio :label="0">
                  {{ $t('pages.processMonitor_Msg33') }}
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.processMonitor_Msg35', { type: $t('pages.fileName') }) }}
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
              <FormItem label-width="0px">
                <tag :list="temp.fileNameList" input-length="255" :disabled="tagDisabled"/>
              </FormItem>
            </el-card>

            <el-divider content-position="left">{{ $t('pages.processMonitor_Msg38') }}</el-divider>
            <FormItem :label="$t('table.localFileBackupType')" label-width="184px">
              <el-checkbox-group v-model="temp.opLocalCodeList" :disabled="tagDisabled">
                <el-checkbox :label="1">{{ $t('pages.create') }}</el-checkbox>
                <el-checkbox :label="2" >{{ $t('pages.edit') }}</el-checkbox>
                <el-checkbox :label="4">{{ $t('pages.rename') }}</el-checkbox>
              </el-checkbox-group>
            </FormItem>
            <div :class="inputClass">
              <FormItem label-width="12px" prop="localFileBackupSize">
                <el-checkbox
                  v-model="temp.isLocalFileBackup"
                  :true-label="1"
                  :false-label="0"
                  :disabled="tagDisabled"
                  @change="isLocalFileBackupChange"
                >
                  <i18n path="pages.blueTooth_Msg1">
                    <el-input-number
                      slot="size"
                      v-model="temp.localFileBackupSize"
                      :disabled="!formable || temp.shareStatus != 1 || temp.localFileAuditStatus != 1 || temp.isLocalFileBackup === 0"
                      :controls="false"
                      :min="1"
                      :step="1"
                      :step-strictly="true"
                      :max="10240"
                      size="mini"
                      style="width: 100px;"
                    /></i18n>
                  <el-tooltip effect="dark" placement="bottom">
                    <div slot="content" style="width: 480px;">{{ $t('pages.localShareFile_msg') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-checkbox>
                <el-button v-if="!tagDisabled" style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
              </FormItem>
            </div>
            <!-- <FormItem :label="$t('table.localFileShareBackupLimit')" prop="localFileBackupSize" label-width="160px">
              &lt;!&ndash; <el-col :span="6">
                <el-input-number v-model="temp.localFileBackupSize" :disabled="!formable || temp.shareStatus != 1 || temp.localFileAuditStatus != 1" :controls="false" :min="0" :step="1" :step-strictly="true" :max="102400" size="mini"/>
              </el-col>
              <el-col :span="3">
                <span>&nbsp;MB</span>
              </el-col>
              <el-col :span="9">
                <span style="color: #2b7aac;">{{ $t('pages.sensitiveBackup_text3') }}</span>
                <el-button style="margin-left: 10px" :disabled="!formable || temp.shareStatus != 1 || temp.localFileAuditStatus != 1" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
              </el-col> &ndash;&gt;
              将FormItem的备份阈值的页面配置转成checkbox的，实现如上,以下先注释
              <i18n path="pages.blueTooth_Msg1">
                <el-input-number slot="size" v-model="temp.localFileBackupSize" :disabled="!formable || temp.shareStatus != 1 || temp.localFileAuditStatus != 1" :controls="false" :min="0" :step="1" :step-strictly="true" :max="102400" size="mini" style="width: 100px;"/>
              </i18n>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 480px;">{{ $t('pages.localShareFile_msg') }}{{ $t('pages.ImFile_text1') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-button style="margin-left: 10px" :disabled="!formable || temp.shareStatus != 1 || temp.localFileAuditStatus != 1" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
            </FormItem> -->
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.netShareBlockConfig')" name="second">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <data-editor
                :formable="formable"
                :popover-width="400"
                :append-to-body="true"
                :updateable="blockRuleEditable"
                :deletable="blockRuleDeleteable"
                :add-func="createBlockRule"
                :update-func="updateBlockRule"
                :delete-func="deleteBlockRule"
                :cancel-func="cancelBlockRule"
                :before-update="beforeupdateBlockRule"
              >
                <Form ref="blockRuleForm" :model="tempB" :rules="tempBRules" label-position="right" label-width="100px" style="width: 330px;">
                  <FormItem :label="$t('table.addressType')" prop="ipType">
                    <el-select v-model="tempB.ipType" style="width: 100%;" @change="ipTypeChange">
                      <el-option v-for="item in ipOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                  <FormItem :label="$t('table.netShareAddress')" prop="ipValue">
                    <el-input v-model="tempB.ipValue" no-limit maxlength="300"/>
                  </FormItem>
                  <FormItem :label="$t('table.remark')" prop="remark">
                    <el-input v-model="tempB.remark" type="textarea" show-word-limit maxlength="128"/>
                  </FormItem>
                </Form>
              </data-editor>
              <grid-table
                ref="blockRuleList"
                auto-height
                :multi-select="formable"
                :show-pager="false"
                :col-model="ipColModel"
                :row-datas="temp.netShareBlockCheckRules"
                @selectionChangeEnd="blockRuleSelectionChange"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <el-radio-group v-model="temp.checkType" :disabled="!formable" style="margin-left: 18px;">
              <el-radio :label="1">{{ $t('pages.shareConfig_text1') }}</el-radio>
              <el-radio :label="0">{{ $t('pages.shareConfig_text2') }}</el-radio>
            </el-radio-group>
            <el-row style="margin-left: 18px">
              <el-col :span="24">
                <label style="font-weight: 500;color: #409eff;font-size: small;">
                  <i18n path="pages.executeRuleTip">
                    <span slot="info">{{ $t('table.netShareAddress') }}</span>
                    <span slot="info1">{{ $t('table.netShareAddress') }}</span>
                    <br slot="br"/>
                  </i18n>
                </label>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.withinDetectionRuleTip">
                      <span slot="info">{{ $t('pages.allowFileToSharedNet') }}</span>
                    </i18n>
                    <br/>
                    <i18n path="pages.outsideDetectionRuleTip1">
                      <span slot="info">{{ $t('pages.forbidFileToSharedNet') }}</span>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-col>
            </el-row>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <el-row style="margin-left: 18px;">
              <el-col :span="14">
                <el-checkbox-group v-model="approvalTypeList" style="display: inline-block" :disabled="!formable" @change="handleApprovalTypeChange">
                  <el-checkbox :label="1">{{ hasEncPermision ? $t('pages.burnMsg4') : $t('pages.burnMsg5') }}</el-checkbox>
                  <el-checkbox :disabled="approvalTypeList.indexOf(1) == -1" :label="4">{{ hasEncPermision ? $t('pages.burnMsg6') : $t('pages.burnMsg7') }}</el-checkbox>
                  <el-checkbox v-if="hasEncPermision" style="margin-top: 15px;" :label="2">{{ $t('pages.burnMsg8') }}</el-checkbox>
                  <el-checkbox v-if="hasEncPermision" style="margin-top: 15px;" :disabled="approvalTypeList.indexOf(2) == -1" :label="8">{{ $t('pages.burnMsg9') }}</el-checkbox>
                </el-checkbox-group>
              </el-col>
            </el-row>
            <ResponseContent
              style="margin-top: 10px"
              :status="dialogStatus"
              :show-select="true"
              :rule-type-name="ruleTypeName"
              :editable="formable && ruleDisable"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="propRuleId"
              @ruleIsCheck="getRuleIsCheck"
              @getRuleId="getRuleId"
            />
            <ResponseContent
              v-if="hasEncPermision"
              :status="dialogStatus"
              :show-select="true"
              :rule-type-name="encRuleTypeName"
              :editable="formable && encRuleDisable"
              read-only
              :prop-check-rule="!!temp.isEncAlarm"
              :show-check-rule="true"
              :prop-rule-id="encPropRuleId"
              @ruleIsCheck="getEncRuleIsCheck"
              @getRuleId="getEncRuleId"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.netShareRecordConfig')" name="third">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <data-editor
                :formable="formable"
                :popover-width="400"
                :append-to-body="true"
                :updateable="recordRuleEditable"
                :deletable="recordRuleDeleteable"
                :add-func="createRecordRule"
                :update-func="updateRecordRule"
                :delete-func="deleteRecordRule"
                :cancel-func="cancelRecordRule"
                :before-update="beforeupdateRecordRule"
              >
                <Form ref="recordRuleForm" :model="tempB" :rules="tempBRules" label-position="right" label-width="100px" style="width: 330px;">
                  <FormItem :label="$t('table.addressType')" prop="ipType">
                    <el-select v-model="tempB.ipType" style="width: 100%;" @change="ipTypeChange">
                      <el-option v-for="item in ipOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </FormItem>
                  <FormItem :label="$t('table.netShareAddress')" prop="ipValue">
                    <el-input v-model="tempB.ipValue" no-limit maxlength="300"/>
                  </FormItem>
                  <FormItem :label="$t('table.remark')" prop="remark">
                    <el-input v-model="tempB.remark" type="textarea" show-word-limit maxlength="128"/>
                  </FormItem>
                </Form>
              </data-editor>
              <grid-table
                ref="recordRuleList"
                auto-height
                :multi-select="formable"
                :show-pager="false"
                :col-model="ipColModel"
                :row-datas="temp.netShareRecordCheckRules"
                @selectionChangeEnd="recordRuleSelectionChange"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <el-radio-group v-model="temp.ipCheckType" :disabled="!formable" style="margin-left: 12px;">
              <el-radio :label="1">{{ $t('pages.shareConfig_text1') }}</el-radio>
              <el-radio :label="0">{{ $t('pages.shareConfig_text2') }}</el-radio>
            </el-radio-group>
            <el-row style="margin-left: 13px">
              <el-col :span="24">
                <label style="font-weight: 500;color: #409eff;font-size: small;">
                  <i18n path="pages.executeRuleTip">
                    <span slot="info">{{ $t('table.netShareAddress') }}</span>
                    <span slot="info1">{{ $t('table.netShareAddress') }}</span>
                    <br slot="br"/>
                  </i18n>
                </label>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.withinDetectionRuleTip">
                      <span slot="info">{{ $t('pages.forbidRecordFileToSharedNet') }}</span>
                    </i18n>
                    <br/>
                    <i18n path="pages.outsideDetectionRuleTip">
                      <span slot="info">{{ $t('pages.allowRecordFileToSharedNet') }}</span>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-col>
            </el-row>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem v-if="temp.shareStatus == 1" :label="$t('table.netShareAudit')">
              <el-radio-group v-model="temp.netFileAuditStatus" :disabled="!formable" @change="netFileAuditStatusChange">
                <el-radio :label="1">{{ $t('pages.enable') }}</el-radio>
                <el-radio :label="0">{{ $t('pages.notEnabled') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <FormItem :label="$t('table.netFileBackupType')" label-width="184px">
              <el-checkbox-group v-model="temp.opNetCodeList" :disabled="!formable || temp.shareStatus == 0 || temp.netFileAuditStatus == 0">
                <el-checkbox :label="1">{{ $t('pages.copy') }}</el-checkbox>
                <el-checkbox :label="2">{{ $t('pages.cut') }}</el-checkbox>
                <el-checkbox :label="4" >{{ $t('pages.saveAs') }}</el-checkbox>
              </el-checkbox-group>
            </FormItem>
            <div :class="inputClass">
              <FormItem label-width="12px" prop="netFileBackupSize">
                <el-checkbox
                  v-model="temp.isNetFileBackup"
                  :true-label="1"
                  :false-label="0"
                  :disabled="!formable || temp.shareStatus == 0 || temp.netFileAuditStatus == 0"
                  @change="isNetFileBackupChange"
                >
                  <i18n path="pages.blueTooth_Msg1">
                    <el-input-number
                      slot="size"
                      v-model="temp.netFileBackupSize"
                      :disabled="!formable || temp.shareStatus == 0 || temp.netFileAuditStatus == 0 || temp.isNetFileBackup === 0"
                      :controls="false"
                      :step="1"
                      :step-strictly="true"
                      :min="1"
                      :max="10240"
                      size="mini"
                      style="width: 100px;"
                    /></i18n>
                  <el-tooltip effect="dark" placement="bottom">
                    <div slot="content" style="width: 480px;">{{ $t('pages.netShareFile_msg') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-checkbox>
                <el-button style="margin-left: 10px" :disabled="!formable || temp.shareStatus == 0 || temp.netFileAuditStatus == 0" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
              </FormItem>
            </div>
            <!-- <FormItem :label="$t('table.netFileShareBackupLimit')" prop="netFileBackupSize" label-width="160px">
              &lt;!&ndash; <el-col :span="6">
                <el-input-number v-model="temp.netFileBackupSize" :disabled="!formable || temp.shareStatus == 0 || temp.netFileAuditStatus == 0" :controls="false" :step="1" :step-strictly="true" :min="0" :max="102400" size="mini"/>
              </el-col>
              <el-col :span="3">
                <span>&nbsp;MB</span>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <div slot="content">{{ $t('pages.shareConfig_text9') }}</div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-col>
              <el-col :span="9">
                <span style="color: #2b7aac;">{{ $t('pages.sensitiveBackup_text3') }}</span>
                <el-button style="margin-left: 10px" :disabled="!formable || temp.shareStatus == 0 || temp.netFileAuditStatus == 0" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
              </el-col> &ndash;&gt;
              <i18n path="pages.blueTooth_Msg1">
                <el-input-number slot="size" v-model="temp.netFileBackupSize" :disabled="!formable || temp.shareStatus == 0 || temp.netFileAuditStatus == 0" :controls="false" :step="1" :step-strictly="true" :min="0" :max="102400" size="mini" style="width: 100px;"/>
              </i18n>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 480px;">{{ $t('pages.netShareFile_msg') }}{{ $t('pages.ImFile_text1') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-button style="margin-left: 10px" :disabled="!formable || temp.shareStatus == 0 || temp.netFileAuditStatus == 0" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
            </FormItem> -->
          </el-tab-pane>
        </el-tabs>
      </Form>

      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importShareConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="activeName === 'first' ? temp.localBackupRuleId : temp.netBackupRuleId"
      :prop-check-rule="activeName === 'first' ? temp.localIsBackupRule : temp.netIsBackupRule"
      @setBackupRule="setBackupRule"
    />
  </div>
</template>
<script>
import {
  createStrategy, deleteStrategy, getStrategyByName, getStrategyPage, updateStrategy
} from '@/api/assets/systemMaintenance/shareConfig'
import {
  buttonFormatter, enableStgBtn, enableStgDelete, entityLink,
  hiddenActiveAndEntity, objectFormatter, refreshPage, selectable
} from '@/utils'
import { exportStg } from '@/api/stgCommon'
import { isIPv4, isIPv6, validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import ImportStg from '@/views/common/importStg'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import { isSameTimestamp, initTimestamp } from '@/utils'
import { listSuffixTree } from '@/api/system/baseData/localShareExtBase'

export default {
  name: 'ShareConfig',
  components: { ResponseContent, ImportStg, BackupRuleContent, FileSuffixLibImport },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 46,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '150', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'shareStatus', label: 'shareStatus', width: '200', formatter: this.strategyFormatter },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.stgFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      ipColModel: [
        { prop: 'ipType', label: 'addressType', width: '100', sort: true, sortOriginal: true, formatter: this.ipTypeFormatter },
        { prop: 'ipValue', label: 'netShareAddress', width: '100', sort: true },
        { prop: 'remark', label: 'remark', width: '100', sort: true }
      ],
      query: { // 查询条件
        page: 1,
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        shareStatus: 1,                 // 是否允许共享
        localFileAuditStatus: 0,        // 本地共享审计状态
        netFileAuditStatus: 0,          // 网络共享审计状态
        isLocalFileBackup: 0,           // 是否备份本地共享文件  0 - 不备份 1 - 备份
        localFileBackupSize: 20,        // 本地共享文件备份阈值
        netFileBackupSize: 20,          // 网络共享文件备份阈值
        isNetFileBackup: 0,             // 是否备份网络共享文件  0 - 不备份 1 - 备份
        entityType: '',
        entityId: undefined,
        opLocalCodeList: [],
        opNetCodeList: [],
        localFileBackupType: 0,         // 本地共享文件备份操作类型
        netFileBackupType: 0,           // 网络共享文件备份操作类型
        // isBackup: 0,                 // 是否备份 1-备份 0-不备份
        isAlarm: 0,                     // 明文文件触发响应规则
        isEncAlarm: 0,                  // 密文文件触发响应规则
        ruleId: undefined,              // 明文文件响应规则id
        encRuleId: undefined,           // 密文文件响应规则id
        isLimit: 0,                     // 1-限制外发 0-不限制外发
        approvalType: 0,                // 位与 审批外发 1-明文文件禁止外发, 2-密文文件禁止外发, 4-允许明文文件外发, 8-允许密文文件外发
        localIsBackupRule: 0,           // 是否配置备份过滤规则
        netIsBackupRule: 0,             // 是否配置备份过滤规则
        localBackupRuleId: undefined,   // 备份过滤规则id
        netBackupRuleId: undefined,     // 备份过滤规则id
        checkType: 0,                   // 网络共享阻断检测条件 0-检测规则之外 1-检测I规则之内
        netShareBlockCheckRules: [],    // 网络共享阻断检测规则
        ipCheckType: 0,                 // 网络共享记录检测条件 0-检测规则之外 1-检测规则之内
        netShareRecordCheckRules: [],    // 网络共享记录检测规则
        fileExtSetup: 0,                 // 文件指定后缀监视类型 0：表示不监视指定后缀文件，没有配置后缀时相当于监视所有；1：表示只监视指定后缀文件，没有配置后缀时相当于都不监视
        fileExtList: [],                 // 文件后缀列表
        fileNameSetup: 0,                // 文件指定名称监视类型 0：表示不监视指定名称文件，没有配置文件名时相当于监视所有；1：表示只监视指定名称文件，没有配置文件名时相当于都不监视
        fileNameList: [],                // 文件名称列表
        dirNameSetup: 0,                 // 目录指定名称监视类型 0：表示不监视指定名称目录，没有配置目录名时相当于监视所有；1：表示只监视指定名称目录，没有配置目录名时相当于都不监视
        dirNameList: [],                 // 目录名称列表
        localShareAuditCheckValueList: []   // 本地共享文件监视列表
      },
      tempB: {},
      defaultTempB: {
        id: undefined,
        ipType: 1,
        ipValue: '',
        remark: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.shareConfigStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.shareConfigStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        localFileBackupSize: [
          { validator: this.localFilBackupValidator, trigger: 'blur' }
        ],
        netFileBackupSize: [
          { validator: this.netFilBackupValidator, trigger: 'blur' }
        ]
      },
      tempBRules: {
        ipValue: [
          { required: true, message: this.$t('pages.validateMsg_shareAddr'), trigger: 'blur' },
          { validator: this.ipValueValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      activeName: 'first',
      monitorActiveName: 'first',
      propRuleId: undefined,  // 明文文件响应规则id
      encPropRuleId: undefined, // 密文文件响应规则id
      approvalTypeList: [], // 审批外发
      hasEncPermision: true,  // 是包含加解密模块
      // 触发明文文件响应规则
      ruleTypeName: this.$t('pages.burnMsg10'),
      // 触发密文文件响应规则
      encRuleTypeName: this.$t('pages.burnMsg11'),
      validRuleId: true,
      validEncRuleId: true,
      ruleDisable: false, // 当明文执行规则没有配置时,明文响应规则要置灰
      encRuleDisable: false, // 当密文执行规则没有配置时,明文响应规则要置灰
      blockRuleEditable: false,
      blockRuleDeleteable: false,
      recordRuleEditable: false,
      recordRuleDeleteable: false,
      exceptRuleEditable: false,
      exceptRuleDeleteable: false,
      ipOptions: [
        { label: this.$t('table.ipv4'), value: 1 },
        { label: this.$t('table.ipv6'), value: 2 },
        { label: this.$t('table.computerName'), value: 3 }
      ],
      inputClass: 'zh-tw',
      localShareExtBaseDatas: [],
      checkedKeys: [],
      visible1: false,
      addAble: false,
      fileExtMap1: {},
      fileExtMap2: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    tagDisabled() {
      return !this.formable || this.temp.shareStatus != 1 || this.temp.localFileAuditStatus != 1
    }
  },
  watch: {
    visible1(val) {
      if (val) {
        if (!isSameTimestamp(this, 'LocalShareExtBase')) {
          this.loadLocalShareExtBase()
        }
      }
    }
  },
  created() {
    initTimestamp(this)
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取注册模块
    this.listModule()
    this.getLanguage()
    this.loadLocalShareExtBase()
  },
  activated() {
    if (!isSameTimestamp(this, 'LocalShareExtBase')) {
      this.loadLocalShareExtBase()
    }
  },
  methods: {
    loadLocalShareExtBase() {
      listSuffixTree().then(resp => {
        this.localShareExtBaseDatas = [
          { label: this.$t('route.localShareExtBase'), id: 0, children: resp.data }
        ]
        this.fileExtMap1 = {}
        this.fileExtMap2 = {}
        resp.data.forEach(item => {
          this.fileExtMap1[item.id] = item.label
          this.fileExtMap2[item.label] = item.id
        })
        this.$nextTick(() => {
          this.$refs['suffixList'] && this.$refs['suffixList'].setCheckedKeys(this.checkedKeys)
        })
      })
    },
    clearCheckSuffix() {
      this.checkedKeys.splice(0)
      this.$refs['suffixList'] && this.$refs['suffixList'].setCheckedKeys(this.checkedKeys)
    },
    popoverLeave() {
      this.$refs['suffixList'].clearFilter()
    },
    handleCheckedSuffix() {
      this.temp.fileExtList.splice(0)
      this.checkedKeys.splice(0)
      const checkedNodes = this.$refs['suffixList'].getCheckedNodes()
      checkedNodes.forEach(item => {
        if (item.id != 0 && this.temp.fileExtList.indexOf(item.label) == -1) {
          this.temp.fileExtList.push(item.label)
          this.checkedKeys.push(item.id)
        }
      })
      this.visible1 = false
    },
    handleCancelCheck() {
      this.visible1 = false
      this.checkedKeys.splice(0)
      //  自动添加前缀
      this.temp.fileExtList.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        this.checkedKeys.push(this.fileExtMap2[item])
      })
      this.$refs['suffixList'] && this.$refs['suffixList'].setCheckedKeys(this.checkedKeys)
    },
    handleClearSuffix() {
      this.temp.fileExtList.splice(0)
      this.clearCheckSuffix()
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      this.temp.fileExtList = [...new Set(this.temp.fileExtList.concat(new_suffix))]
    },
    fileExtListExtChange(list) {
      this.checkedKeys.splice(0)
      const newMap = new Map()
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
        this.checkedKeys.push(this.fileExtMap2[item])
      })
      this.$refs['suffixList'] && this.$refs['suffixList'].setCheckedKeys(this.checkedKeys)
      this.temp.fileExtList = Object.keys(newMap) || []
    },
    // 获取当前语言系统，设置文件备份阈值输入框的class
    getLanguage() {
      const currentLang = this.$store.getters.language
      if (currentLang != undefined || currentLang != null || currentLang != '') {
        if (currentLang == 'zh' || currentLang == 'tw') {
          this.inputClass = 'zh-tw'
        } else {
          this.inputClass = 'en'
        }
      }
    },
    stgFormatter(row, data) {
      const { shareStatus, localFileAuditStatus, localFileBackupType, isLocalFileBackup, localFileBackupSize,
        checkType, approvalType, isAlarm, isEncAlarm, ipCheckType, netFileBackupType,
        netFileAuditStatus, isNetFileBackup, netFileBackupSize } = row
      const msgArr = []
      // shareStatus = 1表示允许共享
      if (shareStatus == 1) {
        // 本地共享设置
        const localShareText = `${this.$t('pages.localShareConfig')}`
        const localText = []
        if (localFileAuditStatus == 1) {
          // 启用
          const activeText = `${this.$t('table.localShareAudit')}: ${this.$t('pages.enable')}`
          localText.push(activeText)
          // 操作类型
          const typeArr = [];
          (localFileBackupType & 1) && typeArr.push(this.$t('pages.create'));
          (localFileBackupType & 2) && typeArr.push(this.$t('pages.edit'));
          (localFileBackupType & 4) && typeArr.push(this.$t('pages.rename'));
          if (typeArr.length > 0) {
            const backupTypeText = `${this.$t('table.localFileBackupType')}: ${typeArr.join('、')}`
            localText.push(backupTypeText)
          }
          // 备份阈值
          if (isLocalFileBackup) {
            const limitText = `${this.$t('table.localFileShareBackupLimit')}: ${localFileBackupSize}MB`
            localText.push(limitText)
          }
        } else {
          // 不启用
          const activeText = `${this.$t('table.localShareAudit')}: ${this.$t('pages.notEnabled')}`
          localText.push(activeText)
        }
        msgArr.push(`${localShareText}: { ${localText.join(', ')} }`)

        // 网络共享管控设置
        const netShareText = `${this.$t('pages.netShareBlockConfig')}`
        const netText = []
        // 执行规则
        if (checkType == 0) {
          netText.push(`${this.$t('pages.executionRules')}: ${this.$t('pages.outDetectionRule')}`)
        } else if (checkType == 1) {
          netText.push(`${this.$t('pages.executionRules')}: ${this.$t('pages.inDetectionRule')}`)
        }
        // 响应规则
        if (approvalType !== 0) {
          const respondArr = [];
          (approvalType & 1) && respondArr.push(this.$t('pages.burnMsg4'));
          (approvalType & 4) && respondArr.push(this.$t('pages.burnMsg6'));
          (approvalType & 2) && respondArr.push(this.$t('pages.burnMsg8'));
          (approvalType & 8) && respondArr.push(this.$t('pages.burnMsg9'));
          if (respondArr.length > 0) {
            const respondText = `${this.$t('route.respond')}: ${respondArr.join('、')}`
            netText.push(respondText)
          }
        }
        if (isAlarm) {
          netText.push(this.$t('pages.burnMsg10'))
        }
        if (isEncAlarm) {
          netText.push(this.$t('pages.burnMsg11'))
        }
        msgArr.push(`${netShareText}: { ${netText.join(', ')} }`)

        // 网络共享记录设置
        const netShareRecordText = `${this.$t('pages.netShareRecordConfig')}`
        const recordText = []
        // 执行规则
        if (ipCheckType == 0) {
          recordText.push(`${this.$t('pages.executionRules')}: ${this.$t('pages.outDetectionRule')}`)
        } else if (ipCheckType == 1) {
          recordText.push(`${this.$t('pages.executionRules')}: ${this.$t('pages.inDetectionRule')}`)
        }
        // 响应规则
        if (netFileAuditStatus == 1) {
          // 启用
          const shareText = []
          const activeText = `${this.$t('table.netShareAudit')}: ${this.$t('pages.enable')}`
          shareText.push(activeText)
          // 操作类型
          const typeArr = [];
          (netFileBackupType & 1) && typeArr.push(this.$t('pages.copy'));
          (netFileBackupType & 2) && typeArr.push(this.$t('pages.cut'));
          (netFileBackupType & 4) && typeArr.push(this.$t('pages.saveAs'));
          if (typeArr.length > 0) {
            const backupTypeText = `${this.$t('table.netFileBackupType')}: ${typeArr.join('、')}`
            shareText.push(backupTypeText)
          }
          // 备份阈值
          if (isNetFileBackup) {
            const limitText = `${this.$t('table.netFileShareBackupLimit')}: ${netFileBackupSize}MB`
            shareText.push(limitText)
          }
          recordText.push(shareText.join(', '))
        } else {
          // 不启用
          const activeText = `${this.$t('table.netShareAudit')}: ${this.$t('pages.notEnabled')}`
          recordText.push(activeText)
        }
        msgArr.push(`${netShareRecordText}: { ${recordText.join(', ')} }`)
      }
      return msgArr.join('; ')
    },
    ipTypeChange(val) {
      this.tempB.ipValue = ''
      this.$refs['blockRuleForm'] && this.$refs['blockRuleForm'].clearValidate()
      this.$refs['recordRuleForm'] && this.$refs['recordRuleForm'].clearValidate()
    },
    blockRuleTable() {
      return this.$refs['blockRuleList']
    },
    blockRuleSelectionChange(rowDatas) {
      this.blockRuleDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.blockRuleEditable = true
      } else {
        this.blockRuleEditable = false
        this.cancelBlockRule()
      }
    },
    recordRuleTable() {
      return this.$refs['recordRuleList']
    },
    recordRuleSelectionChange(rowDatas) {
      this.recordRuleDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.recordRuleEditable = true
      } else {
        this.recordRuleEditable = false
        this.cancelRecordRule()
      }
    },
    createRecordRule() {
      let validate
      this.$refs['recordRuleForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempB))
          rowData.id = new Date().getTime()
          this.temp.netShareRecordCheckRules.unshift(rowData)
          this.cancelRecordRule()
          validate = valid
        }
      })
      return validate
    },
    updateRecordRule() {
      let validate
      this.$refs['recordRuleForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempB))
          for (let i = 0, size = this.temp.netShareRecordCheckRules.length; i < size; i++) {
            const data = this.temp.netShareRecordCheckRules[i]
            if (rowData.id === data.id) {
              this.temp.netShareRecordCheckRules.splice(i, 1, rowData)
              break
            }
          }
          this.cancelRecordRule()
          validate = valid
        }
      })
      return validate
    },
    deleteRecordRule() {
      this.cancelRecordRule()
      const toDeleteIds = this.recordRuleTable().getSelectedIds()
      this.recordRuleTable().deleteRowData(toDeleteIds, this.temp.netShareRecordCheckRules)
    },
    cancelRecordRule() {
      this.$refs['recordRuleForm'] && this.$refs['recordRuleForm'].clearValidate()
      this.resetTempB()
    },
    beforeupdateRecordRule() {
      Object.assign(this.tempB, this.recordRuleTable().getSelectedDatas()[0])
    },
    createBlockRule() {
      let validate
      this.$refs['blockRuleForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempB))
          rowData.id = new Date().getTime()
          this.temp.netShareBlockCheckRules.unshift(rowData)
          this.cancelBlockRule()
          validate = valid
        }
      })
      return validate
    },
    updateBlockRule() {
      let validate
      this.$refs['blockRuleForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempB))
          for (let i = 0, size = this.temp.netShareBlockCheckRules.length; i < size; i++) {
            const data = this.temp.netShareBlockCheckRules[i]
            if (rowData.id === data.id) {
              this.temp.netShareBlockCheckRules.splice(i, 1, rowData)
              break
            }
          }
          this.cancelBlockRule()
          validate = valid
        }
      })
      return validate
    },
    deleteBlockRule() {
      this.cancelBlockRule()
      const toDeleteIds = this.blockRuleTable().getSelectedIds()
      this.blockRuleTable().deleteRowData(toDeleteIds, this.temp.netShareBlockCheckRules)
    },
    cancelBlockRule() {
      this.$refs['blockRuleForm'] && this.$refs['blockRuleForm'].clearValidate()
      this.resetTempB()
    },
    beforeupdateBlockRule() {
      Object.assign(this.tempB, this.blockRuleTable().getSelectedDatas()[0])
    },
    async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = resp.data
        this.ruleTypeName = this.hasEncPermision ? this.$t('pages.burnMsg10') : this.$t('pages.triggerResponseRule')
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempB() {
      this.tempB = Object.assign({}, this.defaultTempB)
    },
    resetTemp() {
      this.activeName = 'first'
      this.temp = Object.assign({}, JSON.parse(JSON.stringify(this.defaultTemp)))
      this.monitorActiveName = 'first'
      this.temp.opLocalCodeList.splice(0)
      this.temp.opNetCodeList.splice(0)
      this.propRuleId = undefined
      this.encPropRuleId = undefined
      this.approvalTypeList.splice(0)
      this.validRuleId = true
      this.validEncRuleId = true
      this.ruleDisable = false
      this.encRuleDisable = false
      this.resetTempB()
      this.clearCheckSuffix()
    },
    // 处理temp数据，设置localFileBackupSize跟netFileBackupSize的值避免出现null的情况
    formatFormData() {
      this.temp.localFileBackupType = this.getSum(this.temp.opLocalCodeList)
      this.temp.netFileBackupType = this.getSum(this.temp.opNetCodeList)
      this.temp.ruleId = this.propRuleId
      this.temp.encRuleId = this.encPropRuleId
      this.temp.approvalType = this.getSum(this.approvalTypeList)
      // 当勾选了允许审批外发时，要去除掉禁止的值比如禁止审批外发为1允许审批外发为4两个都勾选时approvalType只保存4
      this.temp.approvalType = this.getSum(this.approvalTypeList)
      if (this.approvalTypeList.indexOf(4) > -1) {
        this.temp.approvalType = this.temp.approvalType - 1
      }
      if (this.approvalTypeList.indexOf(8) > -1) {
        this.temp.approvalType = this.temp.approvalType - 2
      }
      // 处理禁止传输的值,兼容旧版本
      if (this.hasEncPermision) {
        // 有加解密模块当禁止明文传输跟禁止密文传输都禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(2) > -1 && this.approvalTypeList.indexOf(4) == -1 && this.approvalTypeList.indexOf(8) == -1) {
          this.temp.isLimit = 1
        } else {
          this.temp.isLimit = 0
        }
      } else {
        // 无加解密模块，当禁止明文传输为禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(4) == -1) {
          this.temp.isLimit = 1
        } else {
          this.temp.isLimit = 0
        }
      }
      this.validRuleId = true
      this.validEncRuleId = true
      if (this.temp.isAlarm === 1 && !this.temp.ruleId) {
        this.validRuleId = false
      }
      if (this.temp.isEncAlarm === 1 && !this.temp.encRuleId) {
        this.validEncRuleId = false
      }

      // 本地共享文件监视数据
      this.temp.localShareAuditCheckValueList.splice(0)
      if (this.temp.localFileAuditStatus == 1) {
        if (this.temp.fileExtList.length > 0) {
          // 文件后缀列表
          this.temp.fileExtList.forEach(item => {
            this.temp.localShareAuditCheckValueList.push({
              type: 1,
              value: this.fileExtMap2[item]
            })
          })
        }
        if (this.temp.fileNameList.length > 0) {
          // 文件名称列表
          this.temp.fileNameList.forEach(item => {
            this.temp.localShareAuditCheckValueList.push({
              type: 2,
              value: item
            })
          })
        }
        if (this.temp.dirNameList.length > 0) {
          // 目录名称列表
          this.temp.dirNameList.forEach(item => {
            this.temp.localShareAuditCheckValueList.push({
              type: 3,
              value: item
            })
          })
        }
      }
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj

      // 本地共享文件后缀树默认选中
      if (this.temp.localShareAuditCheckValueList.length > 0) {
        this.checkedKeys.splice(0)
        this.temp.localShareAuditCheckValueList.forEach(item => {
          if (item.type == 1 && this.fileExtMap1[item.value]) {
            this.checkedKeys.push(item.value)
          }
        })
        this.$refs['suffixList'] && this.$refs['suffixList'].setCheckedKeys(this.checkedKeys)
      }

      // 未配置检测规则，后端默认保存一条数据：[{type: 1}],前端展示时，需要清空，避免出现冗余数据
      if (this.temp.netShareRecordCheckRules.length == 1 && !this.temp.netShareRecordCheckRules[0].ipValue) {
        this.temp.netShareRecordCheckRules = []
      }
      // 统一样式，当不勾选备份复选框时，备份的阀值设置显示为默认值
      if (this.temp.localFileBackupSize == 0) {
        this.temp.localFileBackupSize = this.defaultTemp.localFileBackupSize
        this.temp.isLocalFileBackup = 0
      } else { // 旧策略没有这个字段
        this.temp.isLocalFileBackup = 1
      }
      if (this.temp.netFileBackupSize == 0) {
        this.temp.netFileBackupSize = undefined
        this.temp.isNetFileBackup = 0
      } else { // 旧策略没有这个字段
        this.temp.isNetFileBackup = 1
      }
      if (this.temp.approvalType > 0) {
        this.approvalTypeList = this.numToList(this.temp.approvalType, 4)
        // 当勾选的有允许文件外发审批时，approvalType是没有保存禁止外发的值的，所以这边要做下处理
        if (this.approvalTypeList.indexOf(4) > -1) {
          this.approvalTypeList.push(1)
        }
        if (this.approvalTypeList.indexOf(8) > -1) {
          this.approvalTypeList.push(2)
        }
      }
      this.propRuleId = row.ruleId
      this.encPropRuleId = row.encRuleId
      // 兼容旧终端，当未返回localFileBackupType则全选所有的备份选项
      if (!row.localFileBackupType && row.localFileBackupType !== 0) {
        this.temp.localFileBackupType = 7
        this.temp.netFileBackupType = 7
      }

      this.$set(this.temp, 'opLocalCodeList', this.numToList(this.temp.localFileBackupType, 3))
      this.$set(this.temp, 'opNetCodeList', this.numToList(this.temp.netFileBackupType, 3))
      this.dialogStatus = 'update'
      // 当明文执行规则没配置时,无法配置明文响应规则
      if (this.approvalTypeList.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSuffixSetting() {
      this.$router.push({ name: 'LocalShareExtBase' })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      // 处理temp数据，设置localFileBackupSize跟netFileBackupSize的值避免出现null的情况
      this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId) {
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      // 处理temp数据，设置localFileBackupSize跟netFileBackupSize的值避免出现null的情况
      this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId) {
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    ipValueValidator(rule, value, callback) {
      if (this.tempB.ipType == 1 && !isIPv4(value)) { // 校验ipv4格式
        callback(new Error(this.$t('pages.manageLibrary_ipAddressNotRules')))
      } else if (this.tempB.ipType == 2 && !isIPv6(value)) { // 校验ipv6格式
        callback(new Error(this.$t('pages.manageLibrary_ipAddressNotRules')))
      } else {
        let ipList
        if (this.activeName == 'second') {
          ipList = this.blockRuleTable().getDatas()
        } else if (this.activeName == 'third') {
          ipList = this.recordRuleTable().getDatas()
        }
        const index = ipList.findIndex(item => {
          if (item.id != this.tempB.id && item.ipType == this.tempB.ipType && item.ipValue == this.tempB.ipValue) {
            return true
          } else {
            return false
          }
        })
        if (index !== -1) {
          callback(new Error(this.$t('pages.validateMsg_sameIp')))
        } else {
          callback()
        }
      }
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    localFilBackupValidator(rule, value, callback) {
      if (this.temp.isLocalFileBackup === 1 && !value) {
        this.activeName = 'first'
        callback(new Error(this.$t('pages.required1')))
      }
      callback()
    },
    netFilBackupValidator(rule, value, callback) {
      if (this.temp.isNetFileBackup === 1 && !value) {
        this.activeName = 'third'
        callback(new Error(this.$t('pages.required1')))
      }
      callback()
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      let msg = ''
      if (data == 1) {
        msg = this.$t('pages.shareStatus1')
      } else {
        msg = this.$t('pages.shareStatus2')
      }
      return msg
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    ipTypeFormatter: function(row, data) {
      let label
      this.ipOptions.forEach(op => {
        if (data == op.value) {
          label = op.label
        }
      })
      return label
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    getEncRuleId(value) {
      this.encPropRuleId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    getEncRuleIsCheck(value) {
      this.temp.isEncAlarm = value
    },
    handleApprovalTypeChange(value) {
      if (value.indexOf(4) > -1 && value.indexOf(1) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(4), 1)
      }
      if (value.indexOf(8) > -1 && value.indexOf(2) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(8), 1)
      }
      // 勾选了执行规则，响应规则才能勾选
      if (value.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (value.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
    },
    importSuccess() {
      this.handleFilter()
    },
    handleBackupRule() {
      this.$refs.backupRuleContent.show()
    },
    setBackupRule(backupRuleId, checkRule) {
      if (this.activeName === 'first') {
        this.temp.localBackupRuleId = backupRuleId
        this.temp.localIsBackupRule = checkRule
      } else {
        this.temp.netBackupRuleId = backupRuleId
        this.temp.netIsBackupRule = checkRule
      }
    },
    localFileAuditStatusChange(val) {
      if (!val && this.temp.isLocalFileBackup === 1) {
        this.isLocalFileBackupChange(0)
      }
    },
    netFileAuditStatusChange(val) {
      if (!val && this.temp.isNetFileBackup === 1) {
        this.isNetFileBackupChange(0)
      }
    },
    isLocalFileBackupChange(val) {
      if (!val) {
        this.$refs['dataForm'].clearValidate('localFileBackupSize')
      }
      this.temp.isLocalFileBackup = val
    },
    isNetFileBackupChange(val) {
      if (!val) {
        this.$refs['dataForm'].clearValidate('netFileBackupSize')
      }
      this.temp.isNetFileBackup = val
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-tab-pane {
    padding: 10px;
  }
  .share-stg-dlg {
    >>>.first-divider {
      margin-top: 0px;
    }
  }
  .zh-tw  {
    >>>.el-form-item__error {
      margin-left: 170px;
    }
  }
  .en  {
    >>>.el-form-item__error {
      margin-left: 295px;
    }
  }
</style>
