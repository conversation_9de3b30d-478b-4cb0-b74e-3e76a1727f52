<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="'解除流量阈值限制'"
      :visible.sync="visible"
      width="800px"
      @closed="close"
    >
      <div class="toolbar">
        <el-row>
          <div style="display: inline-flex; float: right">
            <tree-select
              ref="objectTree"
              node-key="id"
              is-filter
              check-strictly
              :width="300"
              style="width: 200px; margin-left: 10px"
              :local-search="false"
              :collapse-tags="true"
              :leaf-key="'dept'"
              placeholder="终端分组"
              clearable
              @change="objectTreeChange"
            />
            <el-input v-model="searchQuery.searchInfo" v-trim clearable :placeholder="'终端编号或终端名称'" style="width: 200px;" @keyup.enter.native="handleFilter" />
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
            <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
              <svg-icon icon-class="refresh" />
            </el-button>
          </div>
        </el-row>

      </div>

      <div>
        <!-- 展示在线终端 -->
        <el-checkbox v-if="isSupportAllPageSelected" v-model="checkAll" style="margin-bottom: 10px" @change="checkAllChange">{{ '勾选所有页面的数据' }}</el-checkbox>
        <div style="margin-bottom: 10px" >支持解除在线终端的流量限制，解除后终端当天内不再触发流量阈值限制；流量限制策略修改后，终端将重新累计流量，触发阈值规则后，会再次被限制流量。</div>
      </div>
      <grid-table
        ref="termGridTable"
        row-key="termId"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :multi-select="true"
        :height="350"
        @updateLimit="updateLimit"
        @updatePage="updatePage"
        @select="selectRemoteData"
        @select-all="selectAllRemoteData"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="unLimit">
          解除限制
        </el-button>
        <el-button @click="close">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTermPage } from '@/api/behaviorManage/network/webFlow'
import { enableStgDelete, selectable } from '@/utils'
import { unLimit } from '@/api/behaviorManage/network/webFlow';
export default {
  name: 'UnfreezeDlg',
  comments: {},
  props: {},
  data() {
    return {
      visible: false,
      colModel: [
        { prop: 'termName', label: 'terminalName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'id', label: 'terminalCode', width: '150', sort: 'custom' },
        { prop: 'groupName', label: 'terminalGroup', width: '150' },
        { prop: 'computerName', label: 'computerName', width: '150' }
        // { prop: 'limitStatus', label: '限制状态', formatter: this.limitStatusFormatter }
        // { prop: '', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
      ],
      searchQuery: {},
      temp: {},
      defaultTemp: {
        includeTermIds: [],   //  包括的终端编号
        notIncludeTermIds: [],    //  不包括的终端编号
        checkAll: false,    //  是否全选所有页面数据
        searchInfo: null,   //  终端编号模糊查询
        groupIds: []    //  分组ID集
      },
      socketHandler: null,
      checkAll: false,
      backupSelectedIds: [],            // 备份当前选中的id
      backupSelectedDatas: [],
      backupUnSelectedIds: [],          // 备份当前未选中的id
      isSupportAllPageSelected: false,   //  支持选中所有分页数据库
      isSupportSocketGetStatus: false     //  支持通过Socket获取状态信息，改成true后，后端对应代码也需要支持
    }
  },
  created() {
  },
  deactivated() {
    this.isSupportSocketGetStatus && this.closeSocket();
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.backupUnSelectedIds = []
      this.backupSelectedDatas = []
      this.backupSelectedIds = []
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.searchQuery = {}
      this.checkAll = false
    },
    /**
     * 初始化Socket
     */
    initSocket() {
      this.$socket.subscribeToUser('flowLimit', '/webFlowTerm/getTermLimitStatus', (resp, handle) => {
        const list = resp.data || []
        list.length && this.$refs.termGridTable && this.$refs.termGridTable.execRowDataApi(this.query)
        this.socketHandler = handle;
        // handle.close();
      }, false);
    },
    /**
     * 关闭Socket
     */
    closeSocket() {
      if (this.socketHandler) {
        this.socketHandler.close()
        this.socketHandler = null
      }
    },
    show() {
      this.isSupportSocketGetStatus && this.initSocket();
      this.initData()
      this.visible = true;
      this.handleFilter()
    },
    /**
     * 关闭数据
     */
    close() {
      this.isSupportSocketGetStatus && this.closeSocket();
      this.$refs.termGridTable.clearSelection()
      this.$refs.objectTree.clearSelectedNode();
      this.visible = false;
    },
    rowDataApi: function(option) {
      this.searchQuery = Object.assign({}, this.query, option)
      return getTermPage(this.searchQuery)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    selectRemoteData(selection, row) {
      enableStgDelete(row, this)

      if (this.isSupportAllPageSelected) {
        this.$nextTick(() => {
          const id = row['id']
          const selectedIds = this.$refs.termGridTable.getSelectedIds()
          const isSelect = selectedIds.indexOf(id) >= 0
          // 选中全部的情况下，缓存取消勾选的数据
          if (this.checkAll) {
            // 当前 row 在缓存的未勾选的数据ids中的位置
            const index = this.backupUnSelectedIds.indexOf(id)
            if (isSelect) {
              // 如果是勾选，则移除
              index >= 0 && this.backupUnSelectedIds.splice(index, 1)
            } else {
              // 如果是取消勾选，则添加
              index == -1 && this.backupUnSelectedIds.push(id)
            }
          } else {
            // 未选中的情况下，缓存已勾选的数据
            // 当前 row 在缓存的勾选的数据ids中的位置
            const index = this.backupSelectedIds.indexOf(id)
            if (isSelect) {
              // 如果是勾选，且未添加到数组，则添加
              if (index == -1) {
                this.backupSelectedIds.push(id)
                this.backupSelectedDatas.push(row)
              }
            } else {
              // 如果是取消勾选，且已添加到数组，则移除
              if (index >= 0) {
                this.backupSelectedIds.splice(index, 1)
                this.backupSelectedDatas.splice(index, 1)
              }
            }
          }
        })
      }
    },
    updateLimit(val) {
      this.isSupportAllPageSelected && this.closeSaveDatas()
    },
    updatePage(val) {
      if (this.isSupportAllPageSelected) {
        if (this.checkAll) {
          let ids = this.$refs.termGridTable.getIds();
          if (this.backupUnSelectedIds.length) {
            ids = ids.filter(id => this.backupUnSelectedIds.includes(id));
            ids.length && this.$refs.termGridTable.checkSelectedRows(ids)
          } else {
            this.$refs.termGridTable.checkSelectedRows(ids)
          }
        } else if (this.backupSelectedIds.length) {
          let ids = this.$refs.termGridTable.getIds();
          ids = ids.filter(id => this.backupSelectedIds.includes(id));
          ids.length && this.$refs.termGridTable.checkSelectedRows(ids)
        }
      }
    },
    selectAllRemoteData(selection) {
      if (this.isSupportAllPageSelected) {
        this.$nextTick(() => {
          const rowDatas = this.$refs.termGridTable.rowData
          // 当前页面选中数据的长度为0，证明是取消全选
          const isUnselectAll = this.$refs.termGridTable.getSelectedIds().length === 0
          // 选中全部的情况下，缓存取消勾选的数据
          if (this.checkAll) {
            const backupUnSelectedIdsSet = new Set(this.backupUnSelectedIds)
            rowDatas.forEach(r => {
              const id = r['id']
              const isExist = backupUnSelectedIdsSet.has(id)
              // 取消全选且未缓存，则添加
              if (isUnselectAll && !isExist) {
                backupUnSelectedIdsSet.add(id)
              }
              // 全选且已缓存，则删除
              if (!isUnselectAll && isExist) {
                backupUnSelectedIdsSet.delete(id)
              }
            })
            // 根据 Set 更新 this.backupUnSelectedIds
            this.backupUnSelectedIds = Array.from(backupUnSelectedIdsSet)
          } else {
            // 未选中全部的情况下，缓存已勾选的数据
            const backupSelectedMap = new Map()
            this.backupSelectedIds.forEach((id, i) => { backupSelectedMap.set(id, this.backupSelectedDatas[i]) })
            rowDatas.forEach(r => {
              const id = r['id']
              const isExist = backupSelectedMap.has(id)
              // 全选且已未缓存，则添加
              if (!isUnselectAll && !isExist) {
                backupSelectedMap.set(id, r)
              }
              // 取消全选且已缓存，则删除
              if (isUnselectAll && isExist) {
                backupSelectedMap.delete(id)
              }
            })
            // 根据 Map 更新 this.backupSelectedIds, this.backupSelectedDatas
            this.backupSelectedIds = Array.from(backupSelectedMap.keys())
            this.backupSelectedDatas = Array.from(backupSelectedMap.values())
          }
        })
      }
    },
    unLimit() {
      if (this.isSupportAllPageSelected) {
        this.batchAllPageUnfreezeLimit();
      } else {
        this.batchUnfreezeLimit();
      }
    },
    batchUnfreezeLimit() {
      const termIds = this.$refs.termGridTable.getSelectedIds() || []
      if (!termIds.length) {
        this.$message({
          message: '请选择需要解除限制的终端！',
          type: 'warning',
          duration: 2000
        })
        return
      }
      this.$confirmBox('确定解除阈值限制？', this.$t('text.prompt')).then(() => {
        unLimit({ checkAll: false, includeTermIds: termIds }).then(res => {
          this.$message({
            message: '解除限制发送成功',
            type: 'success',
            duration: 2000
          })
          this.close()
        });
      }).catch(() => {})
    },
    //  批量限制-支持同时下发给指定分组下的所有终端信息
    batchAllPageUnfreezeLimit() {
      this.temp.notIncludeTermIds = this.backupUnSelectedIds
      this.temp.includeTermIds = this.backupSelectedIds
      this.temp.checkAll = this.checkAll;
      const total = this.$refs.termGridTable.getTotal()
      if (this.checkAll) {
        this.temp.includeTermIds = []
        this.temp.groupIds = this.searchQuery.groupIds
        this.temp.searchInfo = this.searchQuery.searchInfo
      } else {
        this.temp.notIncludeTermIds = []
      }
      if ((!this.checkAll && !this.temp.includeTermIds.length) || (this.checkAll && total === 0)) {
        this.$message({
          message: '请选择终端',
          type: 'warning',
          duration: 2000
        })
        return
      }
      this.$confirmBox('确定解除阈值限制？', this.$t('text.prompt')).then(() => {
        unLimit(this.temp).then(res => {
          this.$message({
            message: '解除限制发送成功',
            type: 'success',
            duration: 2000
          })
          this.close()
        });
      }).catch(() => {})
    },
    //  刷新
    refresh() {
      this.searchQuery = {}
      this.$nextTick(() => {
        this.$refs.objectTree.clearFilter()
        this.$refs.objectTree.clearSelectedNode()
      })
      this.searchQuery.page = 1
      this.handleFilter()
    },
    handleFilter() {
      this.searchQuery.page = 1
      this.$refs.termGridTable && this.$refs.termGridTable.execRowDataApi(this.searchQuery)
      this.isSupportAllPageSelected && this.closeSaveDatas()
    },
    //  选中分组
    objectTreeChange(groupId) {
      this.searchQuery.groupIds = []
      if (groupId.length > 0 && groupId !== 'G0') {
        this.searchQuery.groupIds = [parseInt(groupId.substr(1, groupId.length - 1))];
      }
    },
    //  全选
    checkAllChange(val) {
      this.backupSelectedIds.splice(0) // 备份已选中的行的id
      this.backupSelectedDatas.splice(0)
      this.backupUnSelectedIds.splice(0)
      if (val) {
        this.$refs.termGridTable.toggleAllSelection()
      } else {
        if (this.$refs.termGridTable.getSelectedDatas().length > 0) {
          this.$refs.termGridTable.clearSelection()
        }
      }
    },
    closeSaveDatas() {
      this.checkAll = false
      this.backupSelectedIds.splice(0)
      this.backupSelectedDatas.splice(0)
      this.backupUnSelectedIds.splice(0)
    },
    limitStatusFormatter(row) {
      if (row.limitStatus === undefined || row.limitStatus === null) {
        return '未限制';
      }
      return row.limitStatus === 2 ? '阈值限制' : '未限制'
    }
  }
}
</script>

<style scoped>

</style>
