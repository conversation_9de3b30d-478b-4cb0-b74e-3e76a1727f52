<template>
  <div>
    <data-editor
      :formable="formable"
      :append-to-body="true"
      :popover-width="800"
      :addable="formable"
      :updateable="updateStrategyAble"
      :deletable="selectedAble"
      :add-func="createStrategy"
      :update-func="updateStrategy"
      :delete-func="deleteStrategy"
      :cancel-func="cancelStrategy"
      :before-add="beforeAddStrategy"
      :before-update="beforeUpdateStrategy"
    >
      <div style=" max-height: 500px; overflow-y: auto">
        <Form
          ref="ruleForm"
          :rules="rules"
          :model="temp"
          :hide-required-asterisk="true"
          label-width="120px"
          style="width: 750px; margin: 20px 0 0 6px;"
        >
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('table.configName')" prop="name">
                <el-input v-model="temp.name" maxlength/>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.webFlow')" prop="flowType">
                <el-select v-model="temp.flowType" :disabled="!formable" style="width: 100%;">
                  <el-option v-for="(value, key) in flowTypeOptions" :key="key" :label="value" :value="parseInt(key)" />
                </el-select>
              </FormItem>
            </el-col>
          </el-row>

          <el-row v-if="temp.flowType === 1">
            <el-col :span="12">
              <FormItem :label="$t('pages.flowType2')" :tooltip-content="$t('pages.webFlow_text1')" prop="totalFlow" tooltip-placement="bottom-start">
                <el-input-number
                  v-model="temp.totalFlow"
                  :disabled="!formable"
                  :min="-1"
                  :max="1000000000"
                  :controls="false"
                  step-strictly
                  :step="1"
                  class="flow-input"
                /> (KB/S)
              </FormItem>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <FormItem v-if="temp.flowType==0" :label="$t('pages.uploadLimit')" :tooltip-content="$t('pages.webFlow_text1')" prop="upFlow" style="width: 100%" tooltip-placement="bottom-start">
                <el-input-number
                  v-model="temp.upFlow"
                  :disabled="!formable"
                  :min="-1"
                  :max="10000000"
                  :controls="false"
                  step-strictly
                  :step="1"
                  class="flow-input"
                /> (KB/S)
              </FormItem>
            </el-col>

            <el-col :span="12">
              <FormItem v-if="temp.flowType===0" :label="$t('pages.downloadLimit')" :tooltip-content="$t('pages.webFlow_text1')" prop="downFlow" style="width: 100%" tooltip-placement="bottom-start">
                <el-input-number
                  v-model="temp.downFlow"
                  :disabled="!formable"
                  :min="-1"
                  :max="10000000"
                  :controls="false"
                  step-strictly
                  :step="1"
                  class="flow-input"
                /> (KB/S)
              </FormItem>
            </el-col>
          </el-row>

          <!-- 按IP/域名/端口限速  按进程限速 -->
          <el-row>
            <el-tabs v-model="activeName" style="margin-left: 10px" @tab-click="handleClick">
              <el-tab-pane :label="$t('pages.speedMode4')" name="ipPortLimit" style="padding: 1px;">
                <data-editor
                  :formable="formable"
                  :append-to-body="true"
                  :popover-width="700"
                  :updateable="ipportEditable"
                  :deletable="ipportDeleteable"
                  :add-func="createIpport"
                  :update-func="updateIpport"
                  :delete-func="deleteIpport"
                  :cancel-func="cancelIpport"
                  :before-add="beforeAddIpport"
                  :before-update="beforeUpdate"
                  class="editData"
                >
                  <Form
                    ref="ipportForm"
                    :rules="ipportrules"
                    :model="tempIpport"
                    label-width="110px"
                    style="width: 650px; margin: 20px 0px 0 6px;"
                  >
                    <el-row>
                      <el-col :span="12">
                        <FormItem :label="$t('table.protocol')" prop="protocol">
                          <el-select v-model="tempIpport.protocol" style="width: 100%">
                            <el-option v-for="(value, key) in protocolOptions" :key="key" :label="value" :value="parseInt(key)"></el-option>
                          </el-select>
                        </FormItem>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <FormItem :label="$t('table.ipRange')" prop="ipType">
                          <el-select v-model="tempIpport.ipType" style="width: 100%" @change="ipTypeChange">
                            <el-option v-for="(item, key) in ipTypeOptions" :key="key" :label="item.label" :value="parseInt(item.value)"></el-option>
                          </el-select>
                        </FormItem>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col v-if="tempIpport.ipType === 0 || tempIpport.ipType === 1" :span="12" >
                        <FormItem :label="$t('table.startIP')" prop="beginIp">
                          <el-input v-model="tempIpport.beginIp" :disabled="tempIpport.ipType === 0" maxlength="15" @focus="focus" @blur="inputBlur('endIp')"></el-input>
                        </FormItem>
                      </el-col>
                      <el-col v-if="tempIpport.ipType === 0 || tempIpport.ipType === 1" :span="12">
                        <FormItem :label="$t('table.endIp')" prop="endIp">
                          <el-input v-model="tempIpport.endIp" :disabled="tempIpport.ipType === 0" maxlength="15" @focus="focus" @blur="inputBlur('beginIp')"></el-input>
                        </FormItem>
                      </el-col>
                      <el-col v-if="tempIpport.ipType === 2" :span="12">
                        <FormItem :label="$t('pages.domain')" prop="domainAddr">
                          <el-input v-model="tempIpport.domainAddr" v-trim @focus="focus"></el-input>
                        </FormItem>
                      </el-col>
                    </el-row>
                    <el-row v-if="tempIpport.ipType === 3 || tempIpport.ipType === 4">
                      <el-col :span="12">
                        <FormItem :label="$t('table.startIP')" prop="beginIpv6">
                          <el-input v-model="tempIpport.beginIpv6" :disabled="tempIpport.ipType === 3" maxlength="39" @focus="focus" @blur="inputBlur('endIpv6')"></el-input>
                        </FormItem>
                      </el-col>
                      <el-col :span="12">
                        <FormItem :label="$t('table.endIp')" prop="endIpv6">
                          <el-input v-model="tempIpport.endIpv6" :disabled="tempIpport.ipType === 3" maxlength="39" @focus="focus" @blur="inputBlur('beginIpv6')"></el-input>
                        </FormItem>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <FormItem :label="$t('table.portRange')" prop="portType">
                          <el-select v-model="tempIpport.portType" style="width: 100%;" @change="portTypeChange">
                            <el-option v-for="(value, key) in portTypeOptions" :key="key" :label="value" :value="parseInt(key)"></el-option>
                          </el-select>
                        </FormItem>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12" >
                        <FormItem :label="$t('table.beginPort')" prop="beginPort">
                          <el-input-number
                            v-model.number="tempIpport.beginPort"
                            :controls="false"
                            step-strictly
                            :step="1"
                            :disabled="tempIpport.portType === 0"
                            :min="1"
                            :max="65535"
                            style="text-align: center;"
                          ></el-input-number>
                        </FormItem>
                      </el-col>
                      <el-col :span="12">
                        <FormItem :label="$t('table.endPort')" prop="endPort" >
                          <el-input-number
                            v-model.number="tempIpport.endPort"
                            :controls="false"
                            step-strictly
                            :step="1"
                            :disabled="tempIpport.portType === 0"
                            :min="1"
                            :max="65535"
                            style="text-align:left"
                          ></el-input-number>
                        </FormItem>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <FormItem :label="$t('table.fileDescription')" prop="remark">
                          <el-input v-model="tempIpport.remark" v-trim maxlength></el-input>
                        </FormItem>
                      </el-col>
                    </el-row>
                  </Form>
                </data-editor>
                <grid-table
                  ref="ipportList"
                  :height="250"
                  :show-pager="false"
                  :multi-select="true"
                  :col-model="ipportColModel"
                  :row-datas="temp.flowIPPorts"
                  @selectionChangeEnd="ipportSelectionChange"
                />
              </el-tab-pane>

              <el-tab-pane :label="$t('pages.speedMode3')" name="processLimit" style="padding: 1px;">
                <el-card class="box-card" :body-style="{'padding': '0'}">
                  <div slot="header">
                    <div v-if="!!formable" class="btn-box">
                      <el-button class="editDataButton" size="small" @click="createProcess">{{ $t('button.insert') }}</el-button>
                      <el-button class="editDataButton" size="small" @click="importProcessHandle">{{ $t('pages.importAppFromPublicLib') }}</el-button>
                      <el-button class="editDataButton" size="small" :disabled="!processDeleteable" @click="deleteProcess">{{ $t('button.delete') }}</el-button>
                    </div>
                    <!-- <span>{{ $t('pages.speedLimitProcess') }}</span> -->
                    <span style="color:red">{{ ipValidMsg }}</span>

                  </div>
                  <grid-table
                    ref="processList"
                    :height="250"
                    row-no-label=" "
                    :show-pager="false"
                    :multi-select="true"
                    :col-model="processColModel"
                    :row-datas="temp.flowProcesses"
                    @selectionChangeEnd="processSelectionChange"
                  />
                </el-card>
                <!-- 阈值限速 -->
                <div>
                  <flow-threshold-setting
                    ref="flowThresholdSetting"
                    :temp="flowThresholdTemp"
                    :able-tip="true"
                    enable-title="进程总流量阈值限速"
                    enable-title-width="140px"
                    :formable="formable"
                    :able="flowThresholdAble"
                  />
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-row>

        </Form>
      </div>
      <!-- 自定义按钮 -->
      <span slot="self-btn">
        <el-button size="small" :disabled="!selectedAble || !formable" @click="batchSetFlowLimitConfig">{{ '批量设置阈值限速' }}</el-button>
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            {{ '批量设置阈值限速：批量给有设置进程限速的配置信息配置阈值限速，自动过滤只设置按IP/域名/端口限速的配置信息。' }}<br>
            {{ `流量统计模式为总流量统计时：` }}<br>
            {{ '&nbsp;&nbsp;&nbsp;当相同进程配置了不同的总流量阈值限速规则，以阈值较大的作为阈值限速规则。' }}<br>
            {{ '&nbsp;&nbsp;&nbsp;当相同进程配置了相同阈值但限速规则不一样，以第一个作为阈值限速规则。' }}<br>
            {{ '流量统计模式为上传/下载流量统计时：' }}<br>
            {{ '&nbsp;&nbsp;&nbsp;当相同进程配置了不同的上传总流量阈值限速规则，以阈值较大的作为阈值限速规则。' }}<br>
            {{ '&nbsp;&nbsp;&nbsp;当相同进程配置了相同上传总流量阈值但限速规则不一样，以第一个作为阈值限速规则。' }}<br>
            {{ '&nbsp;&nbsp;&nbsp;当相同进程配置了一个上传总流量阈值限速，一个下载总流量阈值限速时，以上传总流量阈值限速规则为准。' }}<br>
            {{ '举例：' }}<br>
            {{ '&nbsp;&nbsp;&nbsp;例1：进程QQ.exe 配置了当天总流量达到1000MB时触发响应规则和当天总流量达到500MB时触发响应规则，以总流量达到1000MB的限速规则为准。' }}<br>
            {{ '&nbsp;&nbsp;&nbsp;例2：进程QQ.exe 配置了当天上传总流量达到1000MB时触发响应规则和当天上传总流量达到500MB时触发响应规则，以上传总流量达到1000MB的限速规则为准。' }}<br>
            {{ '&nbsp;&nbsp;&nbsp;例3：进程QQ.exe 配置了当天上传总流量达到1000MB时触发响应规则和当天下载总流量达到2000MB时触发响应规则，以上传总流量达到1000MB的限速规则为准。' }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </span>
    </data-editor>
    <grid-table
      ref="ruleTable"
      :height="240"
      :show-pager="false"
      :selectable="() => { return formable }"
      :col-model="colModel"
      :row-datas="rowDatas"
      @selectionChangeEnd="strategySelectionChange"
    />
    <app-select-dlg ref="appSelectDlg" :append-to-body="true" @select="importSubmitEnd" @toAppLibraryFlag="toAppLibraryFlag"/>

    <batch-flow-limit-dlg ref="batchFlowLimitDlg" @success="batchFlowLimitSubmit"/>
  </div>
</template>

<script>
import FlowThresholdSetting from '@/views/behaviorManage/network/webFlow/flowThresholdSetting';
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import { isIPv4, isIPv6 } from '@/utils/validate';
import BatchFlowLimitDlg from '@/views/behaviorManage/network/webFlow/batchFlowLimitDlg';

export default {
  name: 'CustomLimit',
  components: { BatchFlowLimitDlg, AppSelectDlg, FlowThresholdSetting },
  props: {
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      originTemp: [],
      rowDatas: [],
      colModel: [
        { prop: 'name', label: 'configName', width: '100' },
        { prop: 'flowType', label: 'webFlow', width: '100', formatter: this.flowTypeFormatter },
        { prop: 'flowTypeRule', label: 'limitRule', width: '100', formatter: this.flowTypeRuleFormatter },
        { prop: 'flowIPPorts', label: 'speedMode4Limit', width: '100', formatter: this.ipportRuleFormatter },
        { prop: 'flowProcesses', label: 'processLimit', width: '100', formatter: this.flowProcessesRuleFormatter },
        { prop: 'flowLimit', label: '阈值限速', width: '100', formatter: this.flowLimitFormatter }
      ],
      temp: {},
      activeName: 'ipPortLimit',
      defaultTemp: { // 表单字段
        id: undefined,
        flowId: undefined,
        flowType: 0,
        name: '',
        upFlow: undefined,
        downFlow: undefined,
        totalFlow: undefined,
        mode: 0,
        flowIPPorts: [],
        flowProcesses: [],
        timeId: 1
      },
      rules: {
        name: [{ required: true, validator: this.nameValidator, trigger: 'blur' }],
        upFlow: [{ required: true, validator: this.upFlowValidator, trigger: 'blur' }],
        downFlow: [{ required: true, validator: this.downFlowValidator, trigger: 'blur' }],
        totalFlow: [{ required: true, validator: this.totalFlowValidator, trigger: 'blur' }]
      },
      updateStrategyAble: false,
      selectedAble: false,
      ipportEditable: false,
      ipportDeleteable: false,
      processDeleteable: false,
      ipValidMsg: '',
      flowTypeOptions: { 0: this.$t('pages.flowType1'), 1: this.$t('pages.flowType2') },
      protocolOptions: { 0: this.$t('pages.all'), 1: 'TCP', 2: 'UDP' },
      portTypeOptions: { 0: this.$t('pages.allPort'), 1: this.$t('pages.userDefined') },
      // ipTypeOptions: { 0: '全部IPv4', 1: '自定义IPv4', 2: '域名', 3: '全部IPv6', 4: '自定义IPv6' },
      ipTypeOptions: [{ label: this.$t('pages.allIPv4'), value: 0 }, { label: this.$t('pages.customIPv4'), value: 1 },
        { label: this.$t('pages.allIPv6'), value: 3 }, { label: this.$t('pages.customIPv6'), value: 4 }, { label: this.$t('pages.domain'), value: 2 }],
      ipportrules: {
        beginIp: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        endIp: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        beginIpv6: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipv6Validator, trigger: 'blur' }
        ],
        endIpv6: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipv6Validator, trigger: 'blur' }
        ],
        domainAddr: [
          { required: true, message: this.$t('pages.validateMsg_domainName'), trigger: 'blur' }
        ],
        beginPort: [
          { required: true, message: this.$t('pages.validateMsg_beginPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        endPort: [
          { required: true, message: this.$t('pages.validateMsg_endPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ]
      },
      tempIpport: {},
      defaultTempIpport: {
        id: undefined,
        flowId: undefined,
        remark: '',
        protocol: 0,
        portType: 0,
        ipType: 0,
        beginPort: 1,
        endPort: 65535,
        beginIp: '0.0.0.0',
        endIp: '***************',
        beginIpv6: '0:0:0:0:0:0:0:0',
        endIpv6: 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff',
        domainAddr: ''
      },
      ipportColModel: [
        { prop: 'protocol', label: 'protocol', width: '100', formatter: this.protocolFormatter },
        { label: 'speedMode4Limit', width: '170', formatter: this.ipFormatter },
        { label: 'portRange', width: '130', formatter: this.portFormatter },
        { prop: 'remark', label: 'fileDescription', width: '150' }
      ],
      processColModel: [
        { prop: 'name', label: 'processName', type: this.formable ? 'input' : 'text', attributes: { maxlength: 25 }, width: '150' },
        { prop: 'remark', label: 'processDesc', type: this.formable ? 'input' : 'text', width: '150' }
      ],
      modeStatus: '', // 这个是用来在添加或者修改ip端口限速设置时，判断描述是否重复用的
      updateId: null,  //  表示流量限制策略的id，仅有修改策略时才有值
      optionStatus: '',  //  操作类型，create,  update
      updateSettingName: '',      //  表示流量限制弹窗中的配置列表中某个元素的配置名称
      toAppFlag: false,
      tempData: {},  //  临时保存IpPort 和 Process
      flowThresholdTemp: {},   //  接收流量阈值限制数据
      flowThresholdDefaultTemp: {
        advanceEnable: false, //  开关
        allLimit: 100000, //  合计流量阈值
        upLimit: 100000,   //  上传总流量阈值
        downLimit: 100000, //  下载总流量阈值
        action: 0,    //  是否限制
        ruleId: null,  //  响应规则
        limitFlowType: 0,  // 超过流量阈值限速-流量限制类型
        limitUpFlow: -1,   //   超过流量阈值限速-上传限速（KB/S)
        limitDownFlow: -1,   //   超过流量阈值限速-下周限速（KB/S)
        limitTotalFlow: -1,   //   超过流量阈值限速-总体限速（KB/S)
        limitMode: 0,      //  限制方式 0-上传/下载  1-总流量
        consoleConfig: {
          downLimitUnit: 1, //  1-MB 2-GB
          allLimitUnit: 1, //  1-MB 2-GB
          upLimitUnit: 1 //  1-MB 2-GB
        }
      },
      flowThresholdMap: new Map(),
      strategy: null    //   存储策略信息
    }
  },
  computed: {
    //  若未配置进程，不运行设置进程阈值限速
    flowThresholdAble() {
      return this.temp.flowProcesses && this.temp.flowProcesses.length > 0
    }
  },
  created() {
  },
  activated() {
    if (this.toAppFlag) {
      this.$refs.appSelectDlg.show()
      this.toAppFlag = false
    }
  },
  methods: {
    /**
     * 构建数据
     * @param data
     * @param mode
     */
    buildData(data, mode) {
      this.strategy = data || {}
      if (!this.strategy.flowRestrict) {
        this.strategy.flowRestrict = []
      }
      this.initData();
      this.activeName = 'ipPortLimit'
      this.updateId = data.id || null
      //  兼容旧版本数据，若mode=1,即限制模式为自定义，并且不存在flowRestrict时，表示该数据为第一版本的数据（第一版本仅支持总体限速）
      if (mode == 1 && data.id !== undefined && data.id !== null &&
        (data.flowRestrict === undefined || data.flowRestrict === null || data.flowRestrict.length === 0) &&
        ((data.flowProcesses && data.flowProcesses.length > 0) || (data.flowIPPorts && data.flowIPPorts.length > 0))) {
        data.flowRestrict = []
        data.flowRestrict.push({
          flowId: data.id,
          name: data.name,
          timeId: data.timeId,
          flowType: data.flowType,
          totalFlow: data.totalFlow,
          upFlow: data.upFlow,
          downFlow: data.downFlow,
          mode: data.mode
        });
      }
      this.rowDatas = [];
      data.flowRestrict = data.flowRestrict ? data.flowRestrict : []

      const mode8List = data.flowRestrict.filter(t => t.mode === 8) || []
      //  保存mode=8的自定义-阈值限速的配置信息
      mode8List.forEach(t => {
        this.flowThresholdMap.set(t.flowId, t);
      })
      //  构建列表数据
      this.buildListData(data);
    },
    /**
     * 构建列表数据
     * @param data
     */
    buildListData(data) {
      //  过滤自定义-阈值限速的配置，即mode=8
      const list = data.flowRestrict.filter(t => t.mode !== 8) || []
      let length = list.length;
      list.forEach(item => {
        if (item.mode !== 0) {
          item = Object.assign({}, this.defaultTemp, item)
          item.id = ++length;
          if (!item.flowId) {
            item.flowId = item.id;
          }
          const ips = data.flowIPPorts.filter(ip => { return ip.flowId === item.flowId })
          const processes = data.flowProcesses.filter(process => { return process.flowId === item.flowId });
          item.flowIPPorts = ips || []
          //  设置进程的id
          let id = processes.length
          processes.forEach(process => {
            process.id = id
            id--;
          });
          item.flowProcesses = processes || []
          this.rowDatas.push(item);
        }
      })
    },
    /**
     * 初始化数据
     */
    initData() {
      this.flowThresholdMap = new Map();
    },
    stgForm() {
      return this.$refs['ruleForm'];
    },
    ruleTable() {
      return this.$refs['ruleTable'];
    },
    ipportTable() {
      return this.$refs['ipportList']
    },
    processTable() {
      return this.$refs['processList']
    },
    nameValidator(rule, value, callback) {
      if (value === '') {
        callback(new Error(this.$t('pages.webFlow_text7') + ''))
      } else if (this.optionStatus === 'create') {
        if (this.rowDatas.findIndex(item => { return item.name === value }) > -1) {
          callback(new Error(this.$t('pages.webFlow_text8') + ''));
        }
      } else if (this.optionStatus === 'update') {
        if (this.rowDatas.findIndex(item => { return this.updateSettingName !== item.name && item.name === value }) > -1) {
          callback(new Error(this.$t('pages.webFlow_text8') + ''));
        }
      }
      callback();
    },
    upFlowValidator(rule, value, callback) {
      this.flowValidator(rule, this.temp.upFlow, callback)
    },
    downFlowValidator(rule, value, callback) {
      this.flowValidator(rule, this.temp.downFlow, callback)
    },
    totalFlowValidator(rule, value, callback) {
      this.flowValidator(rule, this.temp.totalFlow, callback)
    },
    flowValidator(rule, value, callback) {
      if (!/^-?\d+$/.test(value) || parseInt(value) < -1) {
        callback(this.$t('pages.webFlow_text4'))
      }
      callback()
    },
    ipportUniqueValidator(rule, value, callback) {
      for (let i = 0, size = this.temp.flowIPPorts.length; i < size; i++) {
        const rowData = this.temp.flowIPPorts[i]
        if (rowData.remark === value && (this.modeStatus == 'create' || (this.tempIpport.id != rowData.id && this.modeStatus == 'update'))) {
          callback(new Error(this.$t('valid.sameName')))
          return
        }
      }
      callback()
    },
    ipValidator(rule, value, callback) {
      if (value && isIPv4(value)) {
        if (this.tempIpport.beginIp && this.tempIpport.endIp) {
          const temp1 = this.tempIpport.beginIp.split('.')
          const temp2 = this.tempIpport.endIp.split('.')
          let flag = false
          for (var i = 0; i < 4; i++) {
            if (temp1[i] - temp2[i] == 0) {
              continue
            } else if (temp1[i] - temp2[i] > 0) {
              flag = true
            }
            break
          }
          if (flag) {
            callback(new Error(this.$t('pages.serverLibrary_text3')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback(new Error(this.$t('pages.serverLibrary_text4')))
      }
    },
    ipv6Validator(rule, value, callback) {
      if (isIPv6(value)) {
        if (this.tempIpport.beginIpv6 && this.tempIpport.endIpv6) {
          const fullbeginIpv6 = this.getFullIPv6(this.tempIpport.beginIpv6)
          const fullendIpv6 = this.getFullIPv6(this.tempIpport.endIpv6)
          if (fullbeginIpv6 > fullendIpv6) {
            callback(new Error(this.$t('pages.serverLibrary_text3')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback(new Error(this.$t('pages.ipv6_text1')))
      }
    },
    getFullIPv6(src) {
      const temp = src.replace('::', '*')
      const secs = temp.split(':')
      let ret = ''
      for (let i = 0; i < secs.length; i++) {
        const t = secs[i]
        if (t.indexOf('*') >= 0) {
          const ss = t.split('*')
          ret += ss[0].padStart(4, '0')
          for (let j = 1; j < (8 - secs.length); j++) {
            ret += '0000'
          }
          ret += ss[1].padStart(4, '0');
        } else {
          ret += t.padStart(4, '0');
        }
      }
      return ret.toLocaleLowerCase();
    },
    portValidator(rule, value, callback) {
      if (isNaN(Number(value)) || Number(value) != parseInt(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text5')))
      }
      if (value < 1 || value > 65535) {
        callback(new Error(this.$t('pages.serverLibrary_text6')))
      }
      const beginPort = Number(this.tempIpport.beginPort)
      const endPort = Number(this.tempIpport.endPort)
      if (beginPort && endPort) {
        if (beginPort > endPort) {
          callback(new Error(this.$t('pages.serverLibrary_text7')))
        } else {
          this.$refs['ipportForm'].clearValidate(['beginPort', 'endPort'])
          callback()
        }
      } else {
        callback()
      }
    },
    ipFormatter(row, data) {
      if ((row.ipType == 0 || row.ipType == 1) && (row.beginIp || row.endIp)) {
        return row.beginIp + ' - ' + row.endIp
      } else if ((row.ipType == 2) && row.domainAddr) {
        return row.domainAddr
      } else if ((row.ipType == 3 || row.ipType == 4) && (row.beginIpv6 || row.endIpv6)) {
        return row.beginIpv6 + ' - ' + row.endIpv6
      } else {
        return ''
      }
    },
    flowTypeFormatter(row, data) {
      return this.flowTypeOptions[data] || ''
    },
    flowTypeRuleFormatter(row, data) {
      let result = ''
      if (row.flowType == 0) {
        if (row.upFlow !== undefined && row.upFlow !== null) {
          result += this.$t('pages.uploadLimit') + '：' + row.upFlow + '(KB/S)'
        }
        if (row.downFlow !== undefined && row.downFlow !== null) {
          if (result !== '') {
            result += '，'
          }
          result += this.$t('pages.downloadLimit') + '：' + row.downFlow + '(KB/S)'
        }
      } else if (row.flowType == 1) {
        result += this.$t('pages.flowType2') + '：' + row.totalFlow + '(KB/S)'
      }
      return result;
    },
    ipportRuleFormatter(row, data) {
      const names = []
      row.flowIPPorts.forEach(item => {
        const desc = this.ipFormatter(item)
        if (desc) {
          names.push(desc)
        }
      })
      return names.length > 0 ? names.join(',') : ''
    },
    flowProcessesRuleFormatter(row, data) {
      const names = []
      row.flowProcesses.forEach(item => { item.name && names.push(item.name) })
      return names.length > 0 ? names.join(',') : ''
    },
    /**
     * 阈值限速
     * @param row
     * @param data
     */
    flowLimitFormatter(row, data) {
      const t = this.flowThresholdMap.get(row.flowId);
      if (t && t.advanceEnable) {
        return '已开启'
      }
      return '未开启'
    },
    portFormatter(row, data) {
      return row.beginPort + ' - ' + row.endPort
    },
    protocolFormatter(row, data) {
      const statusMap = {
        0: this.$t('pages.all'),
        1: 'TCP',
        2: 'UDP'
      }
      return statusMap[data]
    },

    ipTypeChange(value) {
      if (value !== 2) {
        this.$refs['ipportForm'].clearValidate('domainAddr')
      }
      if (value === 0) {
        this.$refs['ipportForm'].clearValidate('beginIp')
        this.$refs['ipportForm'].clearValidate('endIp')
        this.tempIpport.beginIp = '0.0.0.0'
        this.tempIpport.endIp = '***************'
      } else if (value === 3) {
        this.$refs['ipportForm'].clearValidate('beginIpv6')
        this.$refs['ipportForm'].clearValidate('endIpv6')
        this.tempIpport.beginIpv6 = '0:0:0:0:0:0:0:0'
        this.tempIpport.endIpv6 = 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff'
      } else if (value === 2) {
        this.$refs['ipportForm'].clearValidate('beginIp')
        this.$refs['ipportForm'].clearValidate('endIp')
        this.$refs['ipportForm'].clearValidate('beginIpv6')
        this.$refs['ipportForm'].clearValidate('endIpv6')
      }
    },
    portTypeChange(value) {
      if (value === 0) {
        this.$refs['ipportForm'].clearValidate('beginPort')
        this.$refs['ipportForm'].clearValidate('endPort')
        this.tempIpport.beginPort = 1
        this.tempIpport.endPort = 65535
      }
    },
    strategySelectionChange(rowDatas) {
      this.selectedAble = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.updateStrategyAble = true
      } else {
        this.updateStrategyAble = false
      }
    },
    ipportSelectionChange(rowDatas) {
      this.ipportDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.ipportEditable = true
      } else {
        this.ipportEditable = false
        this.cancelIpport()
      }
    },
    processSelectionChange(rowDatas) {
      this.processDeleteable = rowDatas.length > 0
    },
    handleClick(tab, event) {
      if (tab.name == 'ipPortLimit') {
        this.activeName = 'ipPortLimit'
      } else {
        this.activeName = 'processLimit'
      }
    },
    importSubmitEnd(val) {
      const flowProcesses = this.temp.flowProcesses
      const newId = flowProcesses[0] && (flowProcesses[0].id + 1) || 1
      val.forEach((item, i) => {
        this.temp.flowProcesses.unshift({
          id: newId + i,
          name: item.processName,
          remark: ''
        })
      })
    },

    resetBaseData() {
      this.optionStatus = ''
      this.updateSettingName = ''
      this.activeName = 'ipPortLimit'
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    restFlowThresholdTemp() {
      this.flowThresholdTemp = JSON.parse(JSON.stringify(this.flowThresholdDefaultTemp))
    },
    resetIPPortTemp() {
      this.modeStatus = 'create'
      this.tempIpport = JSON.parse(JSON.stringify(this.defaultTempIpport))
    },
    formatterIpPort(data) {
      if (data.ipType === 2) {
        data.beginIp = undefined
        data.endIp = undefined
        data.beginIpv6 = undefined
        data.endIpv6 = undefined
      } else if (data.ipType === 0 || data.ipType === 1) {
        data.beginIpv6 = undefined
        data.endIpv6 = undefined
        data.domainAddr = undefined
      } else if (data.ipType === 3 || data.ipType === 4) {
        data.beginIp = undefined
        data.endIp = undefined
        data.domainAddr = undefined
      }
    },
    setFlowId(rowData, id) {
      if (id === undefined || id === null) {
        id = 1
      }
      const flowId = id;
      rowData.flowId = flowId;
      (rowData.flowIPPorts).forEach(item => {
        item.flowId = flowId;
      });
      (rowData.flowProcesses || []).forEach(item => {
        item.flowId = flowId;
      })
      return flowId;
    },
    //  获取最大的flowId
    getMaxFlowId(rowDatas) {
      let id = 1;
      (rowDatas || []).forEach(item => {
        if (item.flowId > id) {
          id = item.flowId
        }
      })
      return id + 1;
    },
    //  修改数据之前的校验
    validStrategyData() {
      //  ip/域名 或 进程 至少需要配置一个
      const flowProcesses = this.temp.flowProcesses.filter(process => { return process.name !== null && process.name !== '' })
      if (this.temp.flowIPPorts.length === 0 && flowProcesses.length === 0) {
        this.$message({
          message: this.$t('pages.webFlow_text9'),
          type: 'error',
          duration: 2000
        })
        return false;
      }
      return this.$refs.flowThresholdSetting.validData()
    },
    createStrategy() {
      let validate
      this.stgForm().validate(valid => {
        if (valid) {
          //  ip/域名 或 进程 至少需要配置一个
          if (!this.validStrategyData()) {
            return;
          }
          const row = Object.assign({}, this.temp)
          this.formatFormData(row)
          let id;
          //  若rowDatas没有值时，使用
          if (this.rowDatas.length === 0) {
            id = this.updateId ? this.updateId : 1;
          } else {
            id = this.getMaxFlowId(this.rowDatas);
          }
          row.id = id
          //  设置flowId
          const flowId = this.setFlowId(row, id);
          this.setFlowThresholdTemp(flowId);
          this.rowDatas.push(row)
          this.$emit('submitEnd', [...this.rowDatas], this.toSelfLimitList())
          this.resetIPPortTemp()
          this.resetTemp()
          this.restFlowThresholdTemp()
          this.resetBaseData()
        }
        validate = valid;
      })
      return validate;
    },
    updateStrategy() {
      let validate
      this.stgForm().validate(valid => {
        if (valid) {
          //  ip/域名 或 进程 至少需要配置一个
          if (!this.validStrategyData()) {
            return;
          }
          const row = Object.assign({}, this.temp)
          this.formatFormData(row)
          for (let i = 0, size = this.rowDatas.length; i < size; i++) {
            const data = this.rowDatas[i]
            if (row.id === data.id) {
              const flowId = this.setFlowId(row, row.flowId);
              this.setFlowThresholdTemp(flowId);
              this.rowDatas.splice(i, 1, row)
              break
            }
          }
          //  设置flowId
          this.$emit('submitEnd', [...this.rowDatas], this.toSelfLimitList())
          this.resetIPPortTemp()
          this.resetTemp()
          this.restFlowThresholdTemp()
          this.resetBaseData()
        }
        validate = valid;
      })
      return validate;
    },
    /**
     * 提交前的流量阈值限制数据处理
     * @param formData
     */
    setFlowThresholdTemp(flowId) {
      this.$refs.flowThresholdSetting.formatFormData();
      //  当前配置有启动时，添加数据
      if (this.flowThresholdTemp.advanceEnable) {
        const newData = this.toFlowLimitMapEntry(flowId, this.flowThresholdTemp)
        const oldData = this.strategy.flowRestrict.find(t => t.flowId === flowId && t.mode === 8);
        if (!oldData) {
          this.strategy.flowRestrict.push(newData)
        } else {
          Object.assign(oldData, newData);
        }
        this.flowThresholdMap.set(flowId, newData);
      } else {
        //   去除当前配置的自定义-流量阈值限制配置
        this.strategy.flowRestrict = this.strategy.flowRestrict.filter(t => !(t.flowId === flowId && t.mode === 8))
        this.flowThresholdMap.delete(flowId);
      }
    },
    deleteStrategy() {
      const toDeleteIds = this.ruleTable().getSelectedIds()
      //  删除自定义-流量阈值限制配置
      toDeleteIds.length && toDeleteIds.forEach(id => this.flowThresholdMap.delete(id))
      this.ruleTable().deleteRowData(toDeleteIds, this.rowDatas)
      this.$emit('submitEnd', [...this.rowDatas], this.toSelfLimitList())
      this.resetTemp()
      this.resetIPPortTemp()
      this.activeName = 'ipPortLimit'
    },
    cancelStrategy() {
      this.activeName = 'ipPortLimit'
      if (this.optionStatus === 'update') {
        this.temp.flowIPPorts.splice(0);
        this.temp.flowProcesses.splice(0);
        (this.tempData.flowIPPorts || []).forEach(item => {
          this.temp.flowIPPorts.push(item);
        });
        (this.tempData.flowProcesses || []).forEach(item => {
          this.temp.flowProcesses.push(item);
        })
        this.tempData = {}
      }
      this.resetIPPortTemp()
      this.resetTemp()
      this.stgForm().clearValidate()
    },
    beforeAddStrategy() {
      this.optionStatus = 'create'
      this.resetTemp()
      this.restFlowThresholdTemp()
      this.$nextTick(() => {
        this.$refs.flowThresholdSetting.initTemp('create');
      })
    },
    beforeUpdateStrategy() {
      //  临时保存 flowIpPorts和FlowProcesses，用于取消时回退数据
      this.optionStatus = 'update'
      this.resetTemp()
      this.restFlowThresholdTemp()
      this.temp = Object.assign(this.temp, this.ruleTable().getSelectedDatas()[0])
      this.tempData.flowIPPorts = this.temp.flowIPPorts ? [...this.temp.flowIPPorts] : [];
      this.tempData.flowProcesses = [];
      (this.temp.flowProcesses || []).forEach(item => { this.tempData.flowProcesses.push(JSON.parse(JSON.stringify(item))); })
      this.formatRowData(this.temp)
      if (this.temp.flowIPPorts) {
        for (let i = 0, len = this.temp.flowIPPorts.length; i < len; i++) {
          this.temp.flowIPPorts[i].id = i + 1;
        }
      }
      if (this.temp.flowProcesses) {
        for (let i = 0, len = this.temp.flowProcesses.length; i < len; i++) {
          this.temp.flowProcesses[i].id = i + 1;
        }
      }
      this.updateSettingName = this.temp.name
      const flowId = this.temp.flowId ? this.temp.flowId : this.temp.id
      const t = this.flowThresholdMap.get(flowId)
      if (t) {
        Object.assign(this.flowThresholdTemp, JSON.parse(JSON.stringify(t)));
        this.flowThresholdTemp.limitUpFlow = this.flowThresholdTemp.upFlow
        this.flowThresholdTemp.limitDownFlow = this.flowThresholdTemp.downFlow
        this.flowThresholdTemp.limitTotalFlow = this.flowThresholdTemp.totalFlow
        this.flowThresholdTemp.limitFlowType = this.flowThresholdTemp.flowType
      }
      this.$nextTick(() => {
        this.$refs.flowThresholdSetting.initTemp('update');
      })
    },
    formatRowData(rowData) {
      if (rowData.mode > 1) {
        rowData.mode = 1
      }
    },
    formatFormData(formData) {
      if (formData.flowType === 1) {
        formData.upFlow = -1
        formData.downFlow = -1
      } else {
        formData.totalFlow = -1
      }

      formData.flowIPPorts.forEach(data => {
        delete data.id
      })
      //  去除相同进程名
      const keyMap = {}
      formData.flowProcesses = formData.flowProcesses.map(data => {
        const name = data.name
        if (name && !keyMap[name]) {
          keyMap[name] = true
          delete data.id
          return data
        }
      }).filter(data => data)

      //  设置限制模式
      //  默认设置为1
      formData.mode = 1
      if (formData.flowIPPorts.length) {
        formData.mode = 1
      }
      if (formData.flowProcesses.length) {
        formData.mode = 2
      }
      if (formData.flowIPPorts.length && formData.flowProcesses.length) {
        formData.mode = 3
      }
    },
    beforeAddIpport() {
      this.tempIpport = Object.assign({}, this.defaultTempIpport)
    },
    createIpport() {
      let validate
      this.$refs['ipportForm'].validate((valid) => {
        if (valid) {
          this.formatterIpPort(this.tempIpport)
          const rowData = Object.assign({}, this.tempIpport)
          rowData.id = this.temp.flowIPPorts.length + 1;
          this.temp.flowIPPorts.unshift(rowData)
          this.resetIPPortTemp()
          this.cancelIpport()
          validate = valid
        }
      })
      return validate
    },
    updateIpport() {
      let validate
      this.$refs['ipportForm'].validate((valid) => {
        if (valid) {
          this.formatterIpPort(this.tempIpport)
          const rowData = Object.assign({}, this.tempIpport)
          for (let i = 0, size = this.temp.flowIPPorts.length; i < size; i++) {
            const data = this.temp.flowIPPorts[i]
            if (rowData.id === data.id) {
              this.temp.flowIPPorts.splice(i, 1, rowData)
              break
            }
          }
          this.resetIPPortTemp()
          this.cancelIpport()
          validate = valid
        }
      })
      return validate
    },
    deleteIpport() {
      const toDeleteIds = this.ipportTable().getSelectedIds()
      this.ipportTable().deleteRowData(toDeleteIds, this.temp.flowIPPorts)
      this.resetIPPortTemp()
      this.cancelIpport()
    },
    cancelIpport() {
      // this.ipportTable().clearSelection()
      this.ipportTable() && this.ipportTable().setCurrentRow()
      this.$refs['ipportForm'] && this.$refs['ipportForm'].clearValidate()
      this.resetIPPortTemp()
    },
    createProcess() {
      this.ipValidMsg = ''
      const flowProcesses = this.temp.flowProcesses
      const newId = flowProcesses[0] && (flowProcesses[0].id + 1) || 1
      flowProcesses.unshift({
        id: newId,
        name: '',
        remark: ''
      })
    },
    deleteProcess() {
      this.ipValidMsg = ''
      const toDeleteIds = this.processTable().getSelectedIds()
      for (let i = 0; i < this.temp.flowProcesses.length; i++) {
        const item = this.temp.flowProcesses[i]
        if (toDeleteIds.indexOf(item.id) > -1) {
          this.temp.flowProcesses.splice(i, 1)
          i--
        }
      }
    },
    beforeUpdate() {
      this.resetIPPortTemp()
      this.modeStatus = 'update'
      this.tempIpport = Object.assign(this.tempIpport, this.ipportTable().getSelectedDatas()[0])
    },

    importProcessHandle() {
      this.$refs.appSelectDlg.show()
    },
    number(field) {
      if (this.tempIpport[field] == 0) {
        this.tempIpport[field] = 1
      } else if (isNaN(this.tempIpport[field])) {
        this.tempIpport[field] = this.tempIpport[field].replace(/[^\d]/g, '')
      }
    },
    focus(event) {
      event.currentTarget.select()
    },
    inputBlur(validateProp) {
      this.$refs['ipportForm'].validateField(validateProp)
    },
    clearSelectioned() {
      this.$nextTick(() => {
        this.ruleTable() && this.ruleTable().clearSelection()
      })
    },
    toAppLibraryFlag() {
      this.toAppFlag = true
    },
    /**
     * 自定义-阈值限制配置转换成list
     * @returns {*[]}
     */
    toSelfLimitList() {
      const list = []
      if (this.flowThresholdMap && this.flowThresholdMap.size) {
        this.flowThresholdMap.forEach((value, key) => {
          list.push(JSON.parse(JSON.stringify(value)));
        })
      }
      return list;
    },
    /**
     * 批量更新阈值限速配置信息
     */
    batchSetFlowLimitConfig() {
      const selectedDatas = this.getSelectSetFlowLimitDatas()
      if (selectedDatas.length === 0) {
        this.$message({
          message: '自动过滤后，不存在支持阈值限速的配置项！',
          type: 'warning',
          duration: 2000
        })
        return;
      }
      this.$refs.batchFlowLimitDlg.show()
    },
    /**
     * 批量设置阈值限制配置
     * @param config
     */
    batchFlowLimitSubmit(config) {
      const selectedDatas = this.getSelectSetFlowLimitDatas() || []
      selectedDatas.length && selectedDatas.forEach(data => {
        const id = data.flowId ? data.flowId : data.id
        const oldConfig = this.flowThresholdMap.get(id)
        const newConfig = JSON.parse(JSON.stringify(config))
        newConfig.flowId = oldConfig ? oldConfig.flowId : id
        if (newConfig.flowId) {
          if (newConfig.advanceEnable) {
            const newData = this.toFlowLimitMapEntry(newConfig.flowId, newConfig)
            const oldData = this.strategy.flowRestrict.find(t => t.flowId === newConfig.flowId && t.mode === 8);
            if (!oldData) {
              this.strategy.flowRestrict.push(newData)
            } else {
              Object.assign(oldData, newData);
            }
            this.flowThresholdMap.set(newConfig.flowId, this.toFlowLimitMapEntry(newConfig.flowId, newConfig))
          } else {
            this.strategy.flowRestrict = this.strategy.flowRestrict.filter(t => !(t.mode === 8 && t.flowId === newConfig.flowId))
            this.flowThresholdMap.delete(newConfig.flowId)
          }
        }
      });
      this.$refs.ruleTable.clearSaveSelection()
      //  刷新列表，加载变化
      this.$refs.ruleTable.execRowDataApi();
    },
    /**
     * 获取待批量设置阈值限制的配置项
     * @returns {*[]}
     */
    getSelectSetFlowLimitDatas() {
      let selectedDatas = this.ruleTable().getSelectedDatas() || []
      selectedDatas = selectedDatas.filter(t => t.flowProcesses && t.flowProcesses.length > 0);
      return selectedDatas;
    },
    /**
     * 将flowThresholdTemp转换成flowThresholdMap的子元素
     * @param flowId
     * @param timeId
     * @param flowThresholdTemp
     * @returns {{downLimit: (number|[{validator: *, trigger: string}]|*), totalFlow: (number|*), timeId, allLimit: (number|[{validator: *, trigger: string}]|*), limitMode: (number|*), mode: number, advanceEnable: (boolean|number|*), upLimit: (number|[{validator: *, trigger: string}]|*), downFlow: (number|*), action, upFlow: (number|*), ruleId, flowId, flowType: (number|*)}}
     */
    toFlowLimitMapEntry(flowId, flowThresholdTemp) {
      return {
        flowId: flowId,
        flowType: flowThresholdTemp.limitFlowType,
        totalFlow: flowThresholdTemp.limitTotalFlow,
        upFlow: flowThresholdTemp.limitUpFlow,
        downFlow: flowThresholdTemp.limitDownFlow,
        mode: 8,
        advanceEnable: flowThresholdTemp.advanceEnable,
        allLimit: flowThresholdTemp.allLimit,
        upLimit: flowThresholdTemp.upLimit,
        downLimit: flowThresholdTemp.downLimit,
        action: flowThresholdTemp.action,
        ruleId: flowThresholdTemp.ruleId,
        limitMode: flowThresholdTemp.limitMode
      };
    }
  }
}
</script>

<style lang="scss" scoped>
  .box-card{
    position: relative;
    >>>.el-card__header{
      padding: 20px 20px;
      background-color: transparent;
    }
  }
  .btn-box{
    position: absolute;
    top: 5px;
    left: 0;
  }
  .editData /deep/ .el-button {
    margin-left: 10px;
    padding: 8px;
    margin-bottom: 5px;
  }
  .editDataButton {
    margin-left: 10px;
    padding: 8px;
    margin-bottom: 5px;
  }
  .el-input-number.is-without-controls >>>input {
    text-align: left;
  }
  .flow-input {
    width: calc(100% - 50px);
  }
</style>
