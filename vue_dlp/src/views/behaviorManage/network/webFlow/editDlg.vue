<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.webFlowStg')"
      :stg-code="15"
      :width="860"
      :active-able="activeAble"
      label-w="110px"
      os-label-w="110px"
      time-able
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      @submitEnd="submitEnd"
      @open="handleOpen"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <FormItem :label="$t('table.speedLimit')" :tooltip-content="$t('pages.webFlow_text5')" tooltip-placement="bottom-start">
          <template v-for="(value, key) in modeOptions">
            <el-radio :key="key" v-model="temp.mode" :disabled="!formable" :label="parseInt(key)" @input="modeClick">
              {{ value }}
            </el-radio>
          </template>
        </FormItem>
        <div v-if="temp.mode === 0">
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('pages.webFlow')" prop="flowType">
                <el-select v-model="temp.flowType" :disabled="!formable">
                  <el-option v-for="(value, key) in flowTypeOptions" :key="key" :label="value" :value="parseInt(key)" />
                </el-select>
              </FormItem>
            </el-col>
            <el-col v-if="temp.flowType==1" :span="12">
              <FormItem :label="$t('pages.flowType2')" :tooltip-content="$t('pages.webFlow_text1')" prop="totalFlow" tooltip-placement="bottom-start">
                <el-input-number
                  v-model="temp.totalFlow"
                  :disabled="!formable"
                  :min="-1"
                  :max="1000000000000"
                  :controls="false"
                  step-strictly
                  :step="1"
                  class="flow-input"
                /> (KB/S)
              </FormItem>
            </el-col>
          </el-row>
          <el-row v-if="temp.flowType==0">
            <el-col :span="12">
              <FormItem :label="$t('pages.uploadLimit')" :tooltip-content="$t('pages.webFlow_text1')" prop="upFlow" tooltip-placement="bottom-start">
                <el-input-number
                  v-model="temp.upFlow"
                  :disabled="!formable"
                  :min="-1"
                  :max="1000000000"
                  :controls="false"
                  step-strictly
                  :step="1"
                  class="flow-input"
                /> (KB/S)
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.downloadLimit')" :tooltip-content="$t('pages.webFlow_text1')" prop="downFlow" tooltip-placement="bottom-start">
                <el-input-number
                  v-model="temp.downFlow"
                  :disabled="!formable"
                  :min="-1"
                  :max="1000000000"
                  :controls="false"
                  step-strictly
                  :step="1"
                  class="flow-input"
                /> (KB/S)
              </FormItem>
            </el-col>
          </el-row>
        </div>
        <div v-if="temp.mode == 1">
          <FormItem prop="flowRestrict" label-width="0px">
            <custom-limit ref="customLimit" :formable="formable" @submitEnd="customLimitSubmitEnd"/>
          </FormItem>
        </div>

        <div v-if="temp.mode == 0">
          <flow-threshold-setting v-if="visible" ref="flowThresholdSetting" style="margin-left: 20px;" :temp="flowThresholdTemp" :formable="formable" @initDataAfter="flowTempInitDataAfter"/>
        </div>
      </template>
    </stg-dialog>
  </div>
</template>

<script>
import { createStrategy, getStrategyByName, updateStrategy } from '@/api/behaviorManage/network/webFlow'
import CustomLimit from '@/views/behaviorManage/network/webFlow/customLimit';
import FlowThresholdSetting from '@/views/behaviorManage/network/webFlow/flowThresholdSetting';

export default {
  name: 'WebFlowDlg',
  components: { FlowThresholdSetting, CustomLimit },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      slotName: undefined,
      modeStatus: '', // 这个是用来在添加或者修改ip端口限速设置时，判断描述是否重复用的
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        flowType: 0,
        name: '',
        active: false,
        upFlow: undefined,
        downFlow: undefined,
        totalFlow: undefined,
        mode: 0,
        flowIPPorts: [],
        flowProcesses: [],
        remark: '',
        timeId: 1,
        entityType: undefined,
        entityId: undefined,
        flowRestrict: [],
        allLimit: 100000, //  合计流量阈值
        upLimit: 100000,   //  上传总流量阈值
        downLimit: 100000, //  下载总流量阈值
        action: 0,    //  是否限制
        ruleId: null,  //  响应规则
        limitFlowType: 0,  // 超过流量阈值限速-流量限制类型
        limitUpFlow: -1,   //   超过流量阈值限速-上传限速（KB/S)
        limitDownFlow: -1,   //   超过流量阈值限速-下周限速（KB/S)
        limitTotalFlow: -1,   //   超过流量阈值限速-总体限速（KB/S)
        limitMode: 0      //  限制方式 0-上传/下载  1-总流量
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        upFlow: [{ required: true, validator: this.upFlowValidator, trigger: 'blur' }],
        downFlow: [{ required: true, validator: this.downFlowValidator, trigger: 'blur' }],
        totalFlow: [{ required: true, validator: this.totalFlowValidator, trigger: 'blur' }],
        flowRestrict: [{ required: true, validator: this.flowRestrictValidator, trigger: 'blur' }]
      },
      flowTypeOptions: { 0: this.$t('pages.flowType1'), 1: this.$t('pages.flowType2') },
      limitFlowTypeOptions: { 0: '上传/下载流量统计', 1: '总流量统计' },
      modeOptions: { 0: this.$t('pages.speedMode1'), 1: this.$t('pages.userDefined') },

      rowData: [],
      mode: null,  //  保存更新时的mode
      defaultLimit: 100000,   //  默认限制流量   默认100G
      visible: false,  //  阈值限速加载开关
      flowThresholdTemp: {},   //  接收流量阈值限制数据
      flowThresholdDefaultTemp: {
        advanceEnable: false, //  开关
        allLimit: 0, //  合计流量阈值
        upLimit: 0,   //  上传总流量阈值
        downLimit: 0, //  下载总流量阈值
        action: 0,    //  是否限制
        ruleId: null,  //  响应规则
        limitFlowType: 0,  // 超过流量阈值限速-流量限制类型
        limitUpFlow: -1,   //   超过流量阈值限速-上传限速（KB/S)
        limitDownFlow: -1,   //   超过流量阈值限速-下周限速（KB/S)
        limitTotalFlow: -1,   //   超过流量阈值限速-总体限速（KB/S)
        limitMode: 0,      //  限制方式 0-上传/下载  1-总流量
        consoleConfig: {
          downLimitUnit: 1, //  1-MB 2-GB
          allLimitUnit: 1, //  1-MB 2-GB
          upLimitUnit: 1 //  1-MB 2-GB
        }
      }
    }
  },
  computed: {
  },
  watch: {
    'temp.mode'(val) {
      if (val == 1) {
        this.$nextTick(() => {
          this.$refs['customLimit'] && this.$refs['customLimit'].buildData(this.temp, this.mode)
        })
      }
    }
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      if (name !== this.slotName) {
        this.cancelIpport()
      }
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    resetIPPortTemp() {
      this.modeStatus = 'create'
    },
    cancelIpport() {
      this.resetIPPortTemp()
    },
    /**
     * 初始化数据
     */
    initData() {
      this.flowThresholdTemp = JSON.parse(JSON.stringify(this.flowThresholdDefaultTemp))
      this.visible = false;
    },
    /**
     * 外部调用，触发创建策略弹窗
     * @param row
     */
    handleCreate() {
      this.resetTemp()
      this.initData()
      this.cancelIpport()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    /**
     * 外部调用，触发更新策略弹窗
     * @param row
     */
    handleUpdate(row) {
      this.resetTemp()
      this.initData()
      this.cancelIpport()
      this.$refs['stgDlg'].show(row, this.formable)
    },
    /**
     * 策略总览-查看详情方法
     * @param row
     * @param isGenerateStrategy
     */
    handleShow(row, isGenerateStrategy) {
      this.resetTemp()
      this.cancelIpport()
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    /**
     * 打开弹窗时的数据处理
     * @param rowData
     */
    formatRowData(rowData) {
      //  从数据库中获取的mode存在大于1，对于1，2，3都表示自定义的，0表示总体限速
      if (rowData.mode > 1) {
        rowData.mode = 1
      }
      this.mode = rowData.mode
      //  从flowRestrict获取流量统计阈值设置下发信息
      const t = rowData.flowRestrict.find(t => t.mode === 4) || {}
      Object.assign(this.flowThresholdTemp, t)
      this.flowThresholdTemp.advanceEnable = !!rowData.advanceEnable
      this.flowThresholdTemp.limitFlowType = rowData.limitFlowType
      this.flowThresholdTemp.limitMode = rowData.limitMode
    },
    /**
     * 提交表单时的数据处理
     * @param formData
     */
    formatFormData(formData) {
      if (formData.flowType === 1) {
        formData.upFlow = -1
        formData.downFlow = -1
      } else {
        formData.totalFlow = -1
      }
      if (formData.mode === 0) {
        formData.flowRestrict = []
        formData.flowProcesses = []
        formData.flowIPPorts = []
        this.$refs.flowThresholdSetting.formatFormData();
        this.$refs.flowThresholdSetting.copyProperty(this.flowThresholdTemp, formData);
      } else if (formData.mode === 1) {
        //  兼容第一版本的旧终端，旧终端使用列表第一个策略数据，
        if (formData.flowRestrict && formData.flowRestrict.length === 0) {
          formData.flowType = undefined
          formData.totalFlow = undefined
          formData.upFlow = undefined
          formData.downFlow = undefined
        } else if (formData.flowRestrict && formData.flowRestrict.length > 0) {
          //  过滤掉mode=8的数据。即：过滤自定义-阈值限速配置
          const list = formData.flowRestrict.filter(t => t.mode !== 8)
          if (list.length) {
            formData.flowType = list[0].flowType
            formData.totalFlow = list[0].totalFlow
            formData.upFlow = list[0].upFlow
            formData.downFlow = list[0].downFlow
          }

          //  去除flowRestrict中的 id、flowProcesses 和 flowIPPorts
          formData.flowRestrict.forEach(item => {
            //  给自定义-阈值限制设置生效时间
            if (item.mode === 8) {
              item.timeId = formData.timeId
            }
            delete item.id
            delete item.flowProcesses
            delete item.flowIPPorts
          })
        }
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$refs['customLimit'] && this.$refs['customLimit'].clearSelectioned()
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    upFlowValidator(rule, value, callback) {
      this.flowValidator(rule, this.temp.upFlow, callback)
    },
    downFlowValidator(rule, value, callback) {
      this.flowValidator(rule, this.temp.downFlow, callback)
    },
    totalFlowValidator(rule, value, callback) {
      this.flowValidator(rule, this.temp.totalFlow, callback)
    },
    flowRestrictValidator(rule, value, callback) {
      if (this.temp.mode === 1 && (!value || value.filter(item => item.mode >= 1).length === 0)) {
        callback(new Error(this.$t('pages.webFlow_text6') + ''));
      }
      callback();
    },
    flowValidator(rule, value, callback) {
      if (!/^-?\d+$/.test(value) || parseInt(value) < -1) {
        callback(this.$t('pages.webFlow_text4'))
      }
      callback()
    },
    flowNumber(field) {
      if (!(this.temp[field] === '-' || this.temp[field] === '-1')) {
        if (this.temp[field].startsWith('-1')) {
          this.temp[field] = '-1'
        } else {
          this.temp[field] = this.temp[field].replace(/[^\d]/g, '')
        }
      }
    },
    focus(event) {
      event.currentTarget.select()
    },
    customLimitSubmitEnd(rowDatas, flowThresholdList) {
      const flowIPPorts = []
      const flowProcesses = []
      const flowRestrict = [];
      (rowDatas || []).forEach(item => {
        (item.flowIPPorts || []).forEach(ip => { flowIPPorts.push(ip) });
        (item.flowProcesses || []).forEach(process => { flowProcesses.push(process) });
        flowRestrict.push(item);
      });
      this.temp.flowIPPorts = flowIPPorts
      this.temp.flowProcesses = flowProcesses
      this.temp.flowRestrict = flowRestrict

      //  设置自定义-阈值限制
      flowThresholdList && flowThresholdList.length && flowThresholdList.forEach((value, key) => this.temp.flowRestrict.push(value))
      this.$nextTick(() => {
        this.$refs['stgDlg'] && this.$refs['stgDlg'].validFormData(null)
      })
    },
    //  切换限速模式
    modeClick(value) {
      if (value == 0) {
        this.temp.upFlow = undefined
        this.temp.downFlow = undefined
      }
    },
    validateForm(formData) {
      let flag = false;
      if (formData.mode === 0) {
        flag = this.$refs.flowThresholdSetting.validData()
      } else {
        flag = true;
      }
      return flag;
    },
    //  弹窗打开后
    handleOpen() {
      this.$nextTick(() => {
        this.visible = true
      });
    },
    //  流量阈值限制数据初始化后的处理
    flowTempInitDataAfter() {
      //  该处理方式仅在总体限速时过滤掉
      //  过滤掉mode=4的超过阈值限制规则，在前端rowData.flowRestrict属于流量限制设置中的自定义存储属性
      this.temp.flowRestrict = this.temp.flowRestrict.filter(t => t.mode !== 4) || []
    }
  }
}
</script>
<style lang="scss" scoped>
  .box-card {
    position: relative;
    >>>.el-card__header{
      padding: 10px 20px;
    }
  }
  .btn-box {
    position: absolute;
    top: 5px;
    right: 20px;
  }
  .flow-input {
    width: calc(100% - 50px);
  }
  .prohibit >>> .el-checkbox__inner {
    margin-left: 17px;
  }
</style>
