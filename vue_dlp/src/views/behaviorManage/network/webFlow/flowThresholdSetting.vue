<template>
  <div style="margin-top: 5px">
    <FormItem :label-width="enableTitleWidth" :label="enableTitle">
      <el-radio-group v-model="advanceEnable">
        <el-radio :key="0" :label="0" :value="0">关闭</el-radio>
        <el-radio :key="1" :label="1" :value="1" :disabled="!able">开启<span v-if="ableTip" style="color: #409EFF">（需配置进程限速）</span></el-radio>
      </el-radio-group>
    </FormItem>

    <el-card v-if="advanceEnable">
      <div>
        <!-- 流量统计模式 -->
        <FormItem style="margin-top: 10px" label-width="100px" :label="'流量统计模式'" prop="limitFlowType">
          <el-radio-group v-model="temp.limitFlowType" :disabled="!formable" @change="limitFlowTypeChange">
            <el-radio v-for="(value, key) in limitFlowTypeOptions" :key="key" :label="parseInt(key)">
              {{ value }}
            </el-radio>
          </el-radio-group>
        </FormItem>

        <!-- 执行规则 -->
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <div v-if="temp.limitFlowType === 1" style="margin-left: 20px">
          <i18n path="pages.flowLimitMsg2" style="display: flex; height: 35px; line-height: 35px;">
            <FormItem slot="totalLimit" label-width="0" prop="allLimit" style="margin: 0 5px">
              <el-input-number
                ref="allLimitRef"
                v-model="temp.allLimit"
                :disabled="!formable"
                :controls="false"
                step-strictly
                :step="1"
                :min="1"
                :max="allLimitUnit === 1 ? maxMB : maxGm"
                style="width:180px;"
                @input="allLimitChange"
              />
              <el-select
                v-model="allLimitUnit"
                :disabled="!formable"
                :placeholder="$t('text.select')"
                class="unitClass"
                :style="$store.getters.language === 'en'? 'width: 101px':'width: 70px'"
                @change="checkLimitOutMax('allLimit', allLimitUnit)"
              >
                <el-option :label="'MB'" :value="1"/>
                <el-option :label="'GB'" :value="2"/>
              </el-select>
            </FormItem>
          </i18n>
        </div>
        <div v-if="temp.limitFlowType === 0" style="margin-left: 20px">
          <el-checkbox v-model="upLimitCheckbox" :disabled="!formable" @change="limitCheckboxChange">
            <i18n path="pages.flowLimitMsg1" style="display: inline; height: 35px; line-height: 35px;">
              <FormItem slot="upLimit" label-width="0" prop="upLimit" style="display: inline-block; vertical-align: middle; margin: 0 5px">
                <el-input-number
                  ref="upLimitRef"
                  v-model="temp.upLimit"
                  :disabled="!formable || !upLimitCheckbox"
                  :controls="false"
                  step-strictly
                  :step="1"
                  :min="1"
                  :max="upLimitUnit === 1 ? maxMB : maxGm"
                  style="width:180px;"
                  @input="upLimitChange"
                />
                <el-select
                  v-model="upLimitUnit"
                  :disabled="!formable || !upLimitCheckbox"
                  :placeholder="$t('text.select')"
                  class="unitClass"
                  :style="$store.getters.language === 'en'? 'width: 101px':'width: 70px'"
                  @change="checkLimitOutMax('upLimit', upLimitUnit)"
                >
                  <el-option :label="'MB'" :value="1"/>
                  <el-option :label="'GB'" :value="2"/>
                </el-select>
              </FormItem>
            </i18n>
          </el-checkbox>
          <el-checkbox v-model="downLimitCheckbox" :disabled="!formable" @change="limitCheckboxChange">
            <i18n path="pages.flowLimitMsg3" style="display: inline; height: 35px; line-height: 35px;">
              <FormItem slot="downLimit" label-width="0" prop="downLimit" style="display: inline-block; vertical-align: middle; margin: 0 5px">
                <el-input-number
                  ref="downLimitRef"
                  v-model="temp.downLimit"
                  :disabled="!formable || !downLimitCheckbox"
                  :controls="false"
                  step-strictly
                  :step="1"
                  :min="1"
                  :max="downLimitUnit === 1 ? maxMB : maxGm"
                  style="width:180px;"
                  @input="downLimitChange"
                />
                <el-select
                  v-model="downLimitUnit"
                  :disabled="!formable || !downLimitCheckbox"
                  :placeholder="$t('text.select')"
                  class="unitClass"
                  :style="$store.getters.language === 'en'? 'width: 101px':'width: 70px'"
                  @change="checkLimitOutMax('downLimit', downLimitUnit)"
                >
                  <el-option :label="'MB'" :value="1"/>
                  <el-option :label="'GB'" :value="2"/>
                </el-select>
              </FormItem>
            </i18n>
          </el-checkbox>
        </div>
        <FormItem v-if="limitTip" label-width="20">
          <span style="color: red">
            {{ !temp.limitFlowType ? '上传总流量或下载总流量至少配置一项' : '总流量不能设置为0' }}
          </span>
        </FormItem>

        <FormItem label-width="20px">
          <span style="color: #409eff">
            {{ '注：终端解除阈值限制后，当天不再触发流量限制，但若有重新下发策略，当达到阈值时终端将重新触发响应规则' }}
          </span>
        </FormItem>

        <!-- 响应规则 -->
        <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
        <FormItem label-width="10" prop="action">
          <el-checkbox v-model="temp.action" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable" @change="actionChange">{{ '限制网速' }}</el-checkbox>

          <!-- 流量限制方式 -->
          <FormItem v-if="temp.action" style="width: 100%;" label-width="100px" :label="'限制方式'" prop="limitFlowType">
            <el-radio-group v-model="temp.limitMode" :disabled="!formable">
              <el-radio v-for="(value, key) in flowTypeOptions" :key="key" :label="parseInt(key)">
                {{ value }}
              </el-radio>
            </el-radio-group>
          </FormItem>

          <div>
            <div v-if="!temp.limitMode && temp.action">
              <FormItem :label="$t('pages.uploadLimit')" prop="limitUpFlow" :tooltip-content="$t('pages.webFlow_text1')" tooltip-placement="bottom-start" style="display: flex; align-items: center;">
                <el-input-number
                  ref="limitUpFlowRef"
                  v-model="temp.limitUpFlow"
                  :disabled="!formable"
                  :min="-1"
                  :max="1000000000"
                  :controls="false"
                  step-strictly
                  :step="1"
                  class="flow-input"
                  @input="flowChangeSetValue(temp.limitUpFlow, 'limitUpFlowRef')"
                /> (KB/S)
              </FormItem>
            </div>
            <div v-if="!temp.limitMode && temp.action">
              <FormItem :label="$t('pages.downloadLimit')" prop="limitDownFlow" style="display: flex; align-items: center;" :tooltip-content="$t('pages.webFlow_text1')" tooltip-placement="bottom-start">
                <el-input-number
                  ref="limitDownFlowRef"
                  v-model="temp.limitDownFlow"
                  :disabled="!formable"
                  :min="-1"
                  :max="1000000000"
                  :controls="false"
                  step-strictly
                  :step="1"
                  class="flow-input"
                  @input="flowChangeSetValue(temp.limitDownFlow, 'limitDownFlowRef')"
                /> (KB/S)
              </FormItem>
            </div>
            <div v-if="temp.limitMode && temp.action">
              <FormItem :label="$t('pages.flowType2')" prop="limitDownFlow" style="display: flex; align-items: center;" :tooltip-content="$t('pages.webFlow_text1')" tooltip-placement="bottom-start">
                <el-input-number
                  ref="limitTotalFlowRef"
                  v-model="temp.limitTotalFlow"
                  :disabled="!formable"
                  :min="-1"
                  :max="1000000000"
                  :controls="false"
                  step-strictly
                  :step="1"
                  class="flow-input"
                  @input="flowChangeSetValue(temp.limitTotalFlow, 'limitTotalFlowRef')"
                /> (KB/S)
              </FormItem>
            </div>
          </div>
          <ResponseContent
            style="line-height: 15px !important;"
            :select-style="{ 'margin-top': 0 }"
            :show-select="true"
            :editable="formable"
            :prop-check-rule="!!isRuleId"
            :show-check-rule="true"
            read-only
            :prop-rule-id="temp.ruleId"
            @getRuleId="getRuleId"
            @ruleIsCheck="ruleIsCheck"
            @validate="ruleIdValid"
          />
        </FormItem>
        <div v-if="actionError" style="color: red; margin-left: 20px">{{ $t('pages.install_Msg51') }}</div>
      </div>

    </el-card>

  </div>
</template>

<script>
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent';

export default {
  name: 'FlowThresholdSetting',
  components: { ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    temp: { type: Object, default() { return {} } },
    enableTitle: { type: String, default() { return '总流量阈值限速' } },
    enableTitleWidth: { type: String, default() { return '120px' } },
    able: { type: Boolean, default: true },  //  是否支持开启阈值限速
    ableTip: { type: Boolean, default: false }  //  给开启按钮添加提示信息
  },
  data() {
    return {
      limitFlowTypeOptions: { 0: '上传/下载流量统计', 1: '总流量统计' },
      flowTypeOptions: { 0: this.$t('pages.flowType1'), 1: this.$t('pages.flowType2') },
      isChangeResponse: false, //  是否有改变响应规则
      limitTip: false,   //  上传或下载流量是否提示
      upLimitCheckbox: false,
      downLimitCheckbox: false,
      defaultLimit: 100000,   //  默认限制流量   默认100G
      isChange: false,
      advanceEnable: 0, //  阈值开关
      isRuleId: false,
      defaultTemp: {
        advanceEnable: false, //  开关
        allLimit: 0, //  合计流量阈值
        upLimit: 0,   //  上传总流量阈值
        downLimit: 0, //  下载总流量阈值
        action: 0,    //  是否限制
        ruleId: null,  //  响应规则
        limitFlowType: 0,  // 超过流量阈值限速-流量限制类型
        limitUpFlow: -1,   //   超过流量阈值限速-上传限速（KB/S)
        limitDownFlow: -1,   //   超过流量阈值限速-下周限速（KB/S)
        limitTotalFlow: -1,   //   超过流量阈值限速-总体限速（KB/S)
        limitMode: 0,      //  限制方式 0-上传/下载  1-总流量
        consoleConfig: {
          downLimitUnit: 1, //  1-MB 2-GB
          allLimitUnit: 1, //  1-MB 2-GB
          upLimitUnit: 1 //  1-MB 2-GB
        }
      },
      maxGm: 100000,    //  阈值限制单位为GB最大可输入的值
      maxMB: 1000000000,  //  阈值限制单位为MB最大可输入的值
      downLimitUnit: 1, //  1-MB 2-GB
      allLimitUnit: 1, //  1-MB 2-GB
      upLimitUnit: 1 //  1-MB 2-GB
    }
  },
  computed: {
    actionError() {
      return this.advanceEnable && this.isChangeResponse && !this.temp.action && !this.isRuleId && !this.temp.ruleId
    },
    limitError() {
      return this.limitTip
    }
  },
  watch: {
    able(value) {
      if (!value) {
        this.advanceEnable = 0
      }
    }
  },
  mounted() {
    //  父组件需实现子组件重新加载
    this.initTemp()
  },
  created() {
  },
  methods: {
    initData() {
      this.upLimitCheckbox = false
      this.downLimitCheckbox = false
      this.limitTip = false
      this.isChangeResponse = false
      this.downLimitUnit = 1 //  1-MB 2-GB
      this.allLimitUnit = 1 //  1-MB 2-GB
      this.upLimitUnit = 1 //  1-MB 2-GB
    },
    initTemp(type) {
      this.initData();
      const rowData = this.temp;
      this.upLimitCheckbox = type && type === 'create' ? false : !!rowData.upLimit
      this.downLimitCheckbox = type && type === 'create' ? false : !!rowData.downLimit
      //  兼容旧数据没有，上传流量阈值，下载流量阈值，合计流量阈值，响应规则配置信息
      rowData.advanceEnable = !!rowData.advanceEnable
      this.advanceEnable = rowData.advanceEnable ? 1 : 0
      rowData.limitMode = !rowData.limitMode ? this.defaultTemp.limitMode : rowData.limitMode
      rowData.limitFlowType = !rowData.limitFlowType ? this.defaultTemp.limitFlowType : rowData.limitFlowType
      this.downLimitUnit = rowData.consoleConfig && rowData.consoleConfig.downLimitUnit ? rowData.consoleConfig.downLimitUnit : 1
      this.upLimitUnit = rowData.consoleConfig && rowData.consoleConfig.upLimitUnit ? rowData.consoleConfig.upLimitUnit : 1
      this.allLimitUnit = rowData.consoleConfig && rowData.consoleConfig.allLimitUnit ? rowData.consoleConfig.allLimitUnit : 1
      this.initLimit(rowData, 'upLimit')
      this.initLimit(rowData, 'downLimit')
      this.initLimit(rowData, 'allLimit')
      rowData.action = !rowData.action ? 0 : rowData.action
      rowData.ruleId = !rowData.ruleId ? null : rowData.ruleId
      rowData.limitTotalFlow = this.isEmpty(rowData.totalFlow) ? -1 : rowData.totalFlow
      rowData.limitUpFlow = this.isEmpty(rowData.upFlow) ? -1 : rowData.upFlow
      rowData.limitDownFlow = this.isEmpty(rowData.downFlow) ? -1 : rowData.downFlow
      this.isRuleId = !!rowData.ruleId
      this.$emit('initDataAfter')
    },
    /**
     * 初始化upLimit downLimit allLimit 数据，以MB为单位存储，若单位为GB时需要转换
     * @param rowData
     * @param field
     */
    initLimit(rowData, field) {
      rowData[field] = !rowData[field] ? this.defaultLimit : (rowData.consoleConfig[field + 'Unit'] === 2 ? rowData[field] / 1024 : rowData[field])
    },
    /**
     * export方法，
     * 用于外部校验内部数据合规性
     * @returns {boolean}
     */
    validData() {
      this.validLimitTip()
      this.isChangeResponse = true
      return !this.advanceEnable || !(this.actionError || this.limitError || !this.responseValidate)
    },
    /**
     * 设置限流
     * @param source
     * @param target
     * @param type
     * @param value
     */
    setLimit(source, target, type, value) {
      if (!source.consoleConfig) {
        source.consoleConfig = {}
      }
      if (!target.consoleConfig) {
        target.consoleConfig = {}
      }
      if (type === 'downLimit') {
        target.downLimit = value
        if (!value) {
          target.consoleConfig.downLimitUnit = null
        } else {
          target.downLimit = this.downLimitUnit === 2 ? source.downLimit * 1024 : source.downLimit
          target.consoleConfig.downLimitUnit = this.downLimitUnit
        }
      } else if (type === 'upLimit') {
        target.upLimit = value
        if (!value) {
          target.consoleConfig.upLimitUnit = null
        } else {
          target.upLimit = this.upLimitUnit === 2 ? source.upLimit * 1024 : source.upLimit
          target.consoleConfig.upLimitUnit = this.upLimitUnit
        }
      } else if (type === 'allLimit') {
        target.allLimit = value
        if (!value) {
          target.consoleConfig.allLimitUnit = null
        } else {
          target.allLimit = this.allLimitUnit === 2 ? source.allLimit * 1024 : source.allLimit
          target.consoleConfig.allLimitUnit = this.allLimitUnit
        }
      }
    },
    /**
     * 提交前的数据处理
     * @returns {{limitMode}|*}
     */
    formatFormData() {
      const formData = this.temp
      formData.advanceEnable = !!this.advanceEnable
      if (this.advanceEnable) {
        //  当流量限制为总流量时，
        if (formData.limitFlowType === 1) {
          this.setLimit(formData, formData, 'upLimit', 0);
          this.setLimit(formData, formData, 'downLimit', 0);
          this.setLimit(formData, formData, 'allLimit', formData.allLimit);
          formData.allLimit = this.allLimitUnit === 2 ? formData.allLimit * 1024 : formData.allLimit
        } else if (formData.limitFlowType === 0) {
          this.setLimit(formData, formData, 'allLimit', 0);
          this.setLimit(formData, formData, 'downLimit', !this.downLimitCheckbox ? 0 : formData.downLimit);
          this.setLimit(formData, formData, 'upLimit', !this.upLimitCheckbox ? 0 : formData.upLimit);
          //  当都没勾选时，响应规则清空
          if (!this.upLimitCheckbox && !this.downLimitCheckbox) {
            formData.action = 0
            formData.ruleId = null
          }
        }
        //  限速规则
        if (formData.action) {
          //  限速设置
          if (formData.limitMode) {
            formData.limitUpFlow = -1
            formData.limitDownFlow = -1
          } else {
            formData.limitTotalFlow = -1
          }
        } else {
          formData.limitTotalFlow = -1
          formData.limitUpFlow = -1
          formData.limitDownFlow = -1
        }
      } else {
        formData.limitMode = 0
        formData.limitFlowType = 0
        formData.upLimit = 0
        formData.downLimit = 0
        formData.allLimit = 0
        formData.limitTotalFlow = -1
        formData.limitUpFlow = -1
        formData.limitDownFlow = -1
        formData.action = 0
        formData.ruleId = null
        formData.consoleConfig = null
      }
    },
    /**
     * 拷贝数据
     * @param source
     * @param target
     */
    copyProperty(source, target) {
      target.advanceEnable = source.advanceEnable;
      target.action = source.action;
      target.ruleId = source.ruleId;
      target.limitFlowType = source.limitFlowType;
      target.limitUpFlow = source.limitUpFlow;
      target.limitDownFlow = source.limitDownFlow;
      target.limitTotalFlow = source.limitTotalFlow;
      target.limitMode = source.limitMode;
      target.allLimit = source.allLimit
      target.upLimit = source.upLimit
      target.downLimit = source.downLimit
      target.consoleConfig = source.consoleConfig
    },
    /**
     * 判断对象是否为空
     * @param data
     * @returns {boolean}
     */
    isEmpty(data) {
      return data === undefined || data === null
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    ruleIsCheck(data) {
      this.isRuleId = !!data
      this.actionChange();
    },
    ruleIdValid(val) {
      this.responseValidate = val
    },
    /**
     * 改变input-number数据时，确保最新为-1
     * @param val
     * @param ref
     */
    flowChangeSetValue(val, ref) {
      if (val === undefined || val === null) {
        this.$refs[ref] && this.$refs[ref].setCurrentValue(-1)
      }
    },
    //   选中响应规则后，清空红色提示
    actionChange() {
      this.isChangeResponse = true
    },
    allLimitChange(val) {
      if (val === undefined || val === null) {
        this.$refs.allLimitRef && this.$refs.allLimitRef.setCurrentValue(this.defaultLimit)
      }
    },
    downLimitChange(val) {
      if (val === undefined || val === null) {
        this.$refs.downLimitRef && this.$refs.downLimitRef.setCurrentValue(this.defaultLimit)
      }
    },
    upLimitChange(val) {
      if (val === undefined || val === null) {
        this.$refs.upLimitRef && this.$refs.upLimitRef.setCurrentValue(this.defaultLimit)
      }
    },
    limitFlowTypeChange() {
      if (this.temp.limitFlowType === 1 && !this.temp.allLimit) {
        this.temp.allLimit = this.defaultLimit
      }
      this.validLimitTip()
    },
    /**
     * 校验是否有需要提示
     */
    validLimitTip() {
      if (this.temp.limitFlowType === 0) {
        this.limitTip = !this.upLimitCheckbox && !this.downLimitCheckbox
      } else if (this.temp.limitFlowType === 1) {
        this.limitTip = !this.temp.allLimit
      }
    },
    /**
     * 流量统计模式为-上传/下载流量统计时，在选中执行规则时，触发校验
     */
    limitCheckboxChange() {
      this.validLimitTip()
    },
    /**
     * 检测是否超出最大值
     */
    checkLimitOutMax(type, unit) {
      if (unit === 2) {
        if (type === 'allLimit' && this.temp.allLimit > this.maxGm) {
          this.temp.allLimit = this.maxGm
        } else if (type === 'upLimit' && this.temp.upLimit > this.maxGm) {
          this.temp.upLimit = this.maxGm
        } else if (type === 'downLimit' && this.temp.downLimit > this.maxGm) {
          this.temp.downLimit = this.maxGm
        }
      }
    }
  }
}
</script>

<style  lang="scss" scoped>
.el-divider--horizontal {
  width: 90%;
}

.box-card {
  position: relative;
  >>>.el-card__header{
    padding: 10px 20px;
  }
}
.btn-box {
  position: absolute;
  top: 5px;
  right: 20px;
}
.flow-input {
  width: calc(100% - 50px);
}
.prohibit >>> .el-checkbox__inner {
  margin-left: 17px;
}

.el-divider .el-divider__text {
  color: inherit;
}

.el-card {
  >>>.el-card__body {
    padding: 10px 20px;
  }
}
.unitClass {
  height: 30px;
}
</style>
