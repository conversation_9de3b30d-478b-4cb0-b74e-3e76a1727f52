<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal="false"
      :title="'批量设置阈值限速配置'"
      :visible.sync="visible"
      width="800px"
      @closed="close"
    >
      <div>自动过滤只设置按IP/域名/端口限速的配置信息</div>
      <Form label-width="120px">
        <flow-threshold-setting v-if="visible" ref="flowThresholdSetting" :temp="temp"/>
      </Form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">
          确认
        </el-button>
        <el-button @click="close">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FlowThresholdSetting from '@/views/behaviorManage/network/webFlow/flowThresholdSetting';
export default {
  name: 'BatchFlowLimitDlg',
  components: { FlowThresholdSetting },
  data() {
    return {
      visible: false,
      temp: {},
      defaultTemp: {
        advanceEnable: false, //  开关
        allLimit: 10000, //  合计流量阈值
        upLimit: 10000,   //  上传总流量阈值
        downLimit: 10000, //  下载总流量阈值
        action: 0,    //  是否限制
        ruleId: null,  //  响应规则
        limitFlowType: 0,  // 超过流量阈值限速-流量限制类型
        limitUpFlow: -1,   //   超过流量阈值限速-上传限速（KB/S)
        limitDownFlow: -1,   //   超过流量阈值限速-下周限速（KB/S)
        limitTotalFlow: -1,   //   超过流量阈值限速-总体限速（KB/S)
        limitMode: 0      //  限制方式 0-上传/下载  1-总流量
      }
    }
  },
  created() {
  },
  methods: {
    initData() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    show() {
      this.initData()
      this.visible = true;
    },
    confirm() {
      if (this.$refs.flowThresholdSetting.validData()) {
        this.$refs.flowThresholdSetting.formatFormData();
        this.$emit('success', this.temp)
        this.close();
      }
    },
    cancel() {
      this.close();
    },
    close() {
      this.visible = false
    }
  }
}
</script>

<style scoped>

</style>
