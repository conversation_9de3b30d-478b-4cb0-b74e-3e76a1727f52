<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="'高级设置'"
      :visible.sync="visible"
      width="500px"
      @closed="close"
    >
      <Form ref="dataForm" :model="temp" :rules="rules">
        <FormItem>
          <el-checkbox v-model="temp.recordMode" :true-label="1" :false-label="0" @change="recordModeChange">
            支持按进程名方式统计流量
          </el-checkbox>
        </FormItem>
        <FormItem>
          <i18n path="pages.webFlowConfigReportTimeIntervalMsg" style="display: inline-flex; line-height: 35px;">
            <FormItem slot="interval" prop="showReportTime" style="margin: 0 5px">
              <el-input v-model="temp.showReportTime" :disabled="!formable" style="width: 170px;">
                <el-select
                  slot="append"
                  v-model="temp.showReportTimeUnit"
                  :disabled="!formable"
                  :placeholder="$t('text.select')"
                  :style="$store.getters.language === 'en'? 'width: 101px':'width: 70px'"
                  @click.native.prevent="() => {}"
                >
                  <el-option :label="$t('text.minute')" :value="1"/>
                  <el-option :label="$t('text.hour')" :value="2"/>
                </el-select>
              </el-input>
            </FormItem>
          </i18n>
        </FormItem>
        <div>
          <span style="color: #409EFF">
            说明：终端至少每隔15分钟上报一次流量统计，但若支持按进程名方式统计流量时，终端至少每隔1小时上报一次流量统计。终端默认15分钟上报一次总流量统计。
          </span>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">
          确认
        </el-button>
        <el-button @click="close">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWebFlowConfig, updateConfig } from '@/api/behaviorManage/network/webFlow';

export default {
  name: 'HighSettingDlg',
  props: {
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      visible: false,
      temp: {},
      defaultTemp: {
        showReportTime: 15,  //  展示流量统计上报时间间隔
        showReportTimeUnit: 1,   // 展示的流量统计上报时间间隔单位，1：分钟，2：小时
        recordMode: 0      //  流量统计模式，0：仅流量统计，1：额外支持进程名方式统计
      },
      rules: {
        showReportTime: [
          { validator: this.showReportTimeValidator, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 展示
     */
    async show() {
      this.initData();
      await this.getConfigByKey();
      this.visible = true;
    },
    /**
     * 获取高级配置信息
     */
    getConfigByKey() {
      return getWebFlowConfig().then(resp => {
        resp.data && Object.assign(this.temp, resp.data);
      })
    },
    /**
     * 单位转换成秒
     * @param unit
     * @returns {number}
     */
    toSecond(unit) {
      if (unit === 1) {
        return 60;
      } else if (unit === 2) {
        return 60 * 60
      }
      return 60 * 60;
    },
    /**
     * 数据转换
     * @param row
     * @returns {*}
     */
    formData(row) {
      return JSON.parse(JSON.stringify(row));
    },
    /**
     * 确认
     */
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const rowData = this.formData(this.temp);
          updateConfig(rowData).then(res => {
            this.close();
          })
        }
      });
    },
    /**
     * 关闭
     */
    close() {
      this.$nextTick(() => {
        this.$refs.dataForm && this.$refs.dataForm.clearValidate()
      })
      this.visible = false;
    },
    /**
     * 展示的上报时间间隔-校验
     * @param rule
     * @param value
     * @param callback
     */
    showReportTimeValidator(rule, value, callback) {
      if (!value) {
        callback(new Error('不允许为空！'));
      } else if (this.temp.recordMode === 0 && this.temp.showReportTimeUnit === 1 && this.temp.showReportTime < 15) {
        callback(new Error('间隔至少15分钟'));
      } else if (this.temp.recordMode === 1 && this.temp.showReportTimeUnit === 1 && this.temp.showReportTime < 60) {
        callback(new Error('间隔至少60分钟'));
      }
      callback();
    },
    recordModeChange() {
      if (this.temp.recordMode && this.temp.showReportTimeUnit === 1 && this.temp.showReportTime < 60) {
        this.temp.showReportTimeUnit = 2
        this.temp.showReportTime = 1
        this.$nextTick(() => {
          this.$refs.dataForm && this.$refs.dataForm.validateField('showReportTime')
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
