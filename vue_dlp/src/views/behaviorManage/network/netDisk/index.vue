<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="850px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 800px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('pages.netDisk_text2')" name="first">
            <FormItem :label="$t('pages.uploadLimit2')">
              <el-row>
                <el-col v-for="(value, key) in uploadTypeOptions" :key="key" :span="6" style="height: 30px">
                  <el-radio v-model="temp.uploadType" :disabled="!formable" :label="key" @change="uploadTypeChange">{{ value }}</el-radio>
                </el-col>
              </el-row>
            </FormItem>
            <FormItem :label="$t('pages.netDisk_text5')" prop="fileUploadProcess">
              <tree-select
                :data="treeData"
                node-key="id"
                :checked-keys="temp.fileUploadProcess"
                :width="296"
                :height="190"
                check-strictly
                :disabled="!formable"
                multiple
                :placeholder="$t('pages.netDisk_text4')"
                @change="fileUploadProcSelectChange"
              />
            </FormItem>
            <FormItem v-show="!temp.uploadDisable" prop="fileUploadLimit" :label="$t('pages.fileUploadLimit')">
              <el-row>
                <el-col :span="4">
                  <el-input-number v-model="temp.fileUploadLimit" :disabled="temp.uploadDisable || !formable" :step="1" step-strictly :controls="false" :min="0" :max="65535" size="mini"/>
                </el-col>
                <el-col :span="2">
                  <span style="padding-left: 5px">MB</span>
                </el-col>
                <el-col :span="18">
                  <span style="color: #2b7aac;">{{ $t('pages.processMonitor_Msg2') }}</span>
                </el-col>
              </el-row>
            </FormItem>
            <span style="color: #2b7aac; padding-top: 10px; padding-left: 20px">{{ $t('pages.netDisk_text1') }}</span>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.netDisk_text3')" name="second">
            <div :class="inputClass">
              <FormItem :label="$t('pages.netDisk_text7')" prop="monitoringProcess" label-width="110px">
                <tree-select
                  :data="treeData"
                  node-key="id"
                  :checked-keys="temp.monitoringProcess"
                  :width="296"
                  :height="190"
                  check-strictly
                  :disabled="!formable"
                  multiple
                  :placeholder="$t('pages.netDisk_text4')"
                  @change="monitoringProcSelectChange"
                />
              </FormItem>
              <FormItem label-width="22px" prop="isRecord">
                <el-checkbox v-model="temp.isRecord" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('pages.netDisk_text8') }}</el-checkbox>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div slot="content">{{ $t('pages.netDisk_text9') }}</div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </FormItem>
              <FormItem label-width="22px" prop="fileBackUpLimit">
                <el-checkbox
                  v-model="temp.isLocalFileBackup"
                  :true-label="1"
                  :false-label="0"
                  :disabled="!formable"
                  @change="isLocalFileBackupChange()"
                >
                  <i18n path="pages.blueTooth_Msg1">
                    <el-input-number slot="size" v-model="temp.fileBackUpLimit" :disabled="!formable || temp.isLocalFileBackup ==0" step-strictly :controls="false" :min="1" :max="10240" size="mini" style="width: 100px;"/>
                  </i18n>
                  <!-- <span style="color: #0c60a5;padding-left: 10px">{{ $t('pages.usbFileConfig_text1') }}</span>
                  <el-button style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button> -->
                  <el-tooltip effect="dark" placement="bottom">
                    <!-- <div slot="content" style="width: 480px;">{{ $t('pages.netDiskTrans_msg') }}{{ $t('pages.ImFile_text1') }}</div> -->
                    <div slot="content" style="width: 480px;">{{ $t('pages.netDiskTrans_msg') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-checkbox>
                <el-button style="margin-left: 10px" :disabled="!formable" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
              </FormItem>
            </div>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importNetDiskStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
  </div>
</template>
<script>
import {
  getDataPage, getDataByName, createData, updateData, deleteData
} from '@/api/behaviorManage/network/netDisk'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'

export default {
  name: 'NetDiskStrategy',
  components: { ImportStg, BackupRuleContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 134,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        fileBackUpLimit: 20,
        entityType: '',
        entityId: undefined,
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined, // 备份过滤规则id
        isLocalFileBackup: 1,
        isRecord: 1,
        uploadDisable: false,
        fileUploadLimit: 0, // 允许上传文件大小
        uploadType: '1' // 0禁止上传 1允许上传
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.netDiskStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.netDiskStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileBackUpLimit: [
          { validator: this.fileBackUpLimitValidator, trigger: 'blur' }
        ],
        fileUploadLimit: [
          { required: true, message: this.$t('pages.netDisk_text6'), trigger: 'blur' }
        ]
      },
      submitting: false,
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined, // 备份过滤规则id
      inputClass: 'zh-tw',
      activeName: 'first',
      uploadTypeOptions: { 0: this.$t('pages.forbidUpload'), 1: this.$t('pages.allowUpload') },
      treeData: [
        { id: 'BAIDUNETDISK', label: this.$t('pages.softBaiDuNetDisk') },
        { id: 'WxDrive', label: this.$t('pages.QYWXMicroDisk') }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.getLanguage()
  },
  methods: {
    isLocalFileBackupChange(val) {
      if (val == 0) {
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
        if (this.temp.fileBackUpLimit == '' || this.temp.fileBackUpLimit == undefined || this.temp.fileBackUpLimit == null) {
          this.temp.fileBackUpLimit = 20
        }
      } else {
        if (this.temp.fileBackUpLimit == 0) {
          this.temp.fileBackUpLimit = 20
        }
      }
    },
    // 获取当前语言系统，设置文件备份阈值输入框的class
    getLanguage() {
      const currentLang = this.$store.getters.language
      if (currentLang != undefined || currentLang != null || currentLang != '') {
        if (currentLang == 'zh' || currentLang == 'tw') {
          this.inputClass = 'zh-tw'
        } else {
          this.inputClass = 'en'
        }
      }
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getDataPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.fileUploadProcess = [] // 允许上传的进程名
      this.temp.monitoringProcess = [] // 监控的进程名
    },
    handleCreate() {
      this.resetTemp()
      this.activeName = 'first'
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      const uploadType = row.uploadType == 0 ? '0' : '1'
      this.activeName = 'first'
      this.temp = Object.assign(this.temp, row) // copy obj
      this.temp.fileUploadProcess = this.temp.fileUploadProcess.filter(p => p != '*.*')
      this.temp.monitoringProcess = this.temp.monitoringProcess.filter(p => p != '*.*')
      this.uploadTypeChange(uploadType)
      if (this.temp.isLocalFileBackup == 0) {
        this.temp.fileBackUpLimit = 20
      }
      if (this.temp.isRecord == undefined) {
        // 旧数据没有isRecord字段
        this.$set(this.temp, 'isRecord', 1)
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    allFileUploadProcessFormatter(assignObj) {
      const obj = assignObj || this.temp
      if (obj.fileUploadProcess.length == 0) {
        obj.fileUploadProcess = ['*.*'] // 允许上传的进程名
      }
    },
    allMonitoringProcessFormatter(assignObj) {
      const obj = assignObj || this.temp
      if (obj.monitoringProcess.length == 0) {
        obj.monitoringProcess = ['*.*'] // 监控的进程名
      }
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = JSON.parse(JSON.stringify(this.temp))
          this.allFileUploadProcessFormatter(tempData);
          this.allMonitoringProcessFormatter(tempData);
          // 是否启用文件备份的判断的后台接口进行
          createData(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = JSON.parse(JSON.stringify(this.temp))
          this.allFileUploadProcessFormatter(tempData);
          this.allMonitoringProcessFormatter(tempData);
          updateData(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      let msg = ''
      let process = ''
      if (row.fileUploadProcess) {
        process = this.treeData.filter(p => row.fileUploadProcess.indexOf(p.id) >= 0).map(p => p.label).join('、')
      }
      process = process.length ? process : this.$t('pages.netDisk_text4')
      msg += (row.uploadType == 0 ? this.$t('pages.forbidUpload2', { processNames: process }) : this.$t('pages.allowUpload2', { processNames: process })) + ';'
      if (row.uploadType && row.fileUploadLimit != 0) {
        msg += this.$t('pages.netDisk_text10', { size: row.fileUploadLimit }) + ';'
      }
      if (row.monitoringProcess) {
        process = this.treeData.filter(p => row.monitoringProcess.indexOf(p.id) >= 0).map(p => p.label).join('、')
        process = process.length ? process : this.$t('pages.netDisk_text4')
      }
      if (row.isRecord !== 0) {
        msg += this.$t('pages.netDisk_text11', { processNames: process }) + ';'
      }
      if (row.isLocalFileBackup) {
        msg += this.$t('pages.usbFileConfig_text2', { size: row.fileBackUpLimit }) + ';'
      }

      return msg
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    },
    uploadTypeChange: function(value) {
      this.temp.uploadType = value
      if (value === 1 || value === '1') {
        // 允许发送文件的话，初始化为不限制
        this.temp.uploadDisable = false
      } else {
        this.temp.fileUploadLimit = 0
        this.temp.uploadDisable = true
      }
    },
    fileUploadProcSelectChange(data) {
      console.log('fileUploadProcSelectChange')
      this.temp.fileUploadProcess.splice(0, this.temp.fileUploadProcess.length, ...data)
    },
    monitoringProcSelectChange(data) {
      this.temp.monitoringProcess.splice(0, this.temp.monitoringProcess.length, ...data)
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.temp.isLocalFileBackup === 1 && !value) {
        callback(new Error(this.$t('pages.required1')))
      }
      callback()
    }
  }
}
</script>
<style lang="scss" scoped>
.zh-tw  {
  >>>.el-form-item__error {
    margin-left: 170px;
  }
}
.en  {
  >>>.el-form-item__error {
    margin-left: 289px;
  }
}
.el-tab-pane {
  padding: 10px;
}
</style>
