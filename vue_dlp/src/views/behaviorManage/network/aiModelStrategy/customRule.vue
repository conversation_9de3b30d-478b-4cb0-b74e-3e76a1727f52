<template>
  <LocalZoomIn parent-tag="el-dialog">
    <data-editor
      :formable="formable"
      :append-to-body="true"
      :popover-width="740"
      :updateable="updatable"
      :deletable="deletable"
      :add-func="addData"
      :update-func="updateData"
      :delete-func="deleteData"
      :cancel-func="cancelData"
      :before-add="resetTemp"
      :before-update="beforeUpdateData"
    >
      <Form ref="form" :rules="rules" :model="temp" label-position="right" label-width="120px" :extra-width="{ en: 0 }" style="width: 650px;">
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.aiName')" prop="aiName">
              <el-input v-model="temp.aiName" v-trim maxlength="100" @focus="focus"/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.processName')" prop="processName">
              <el-input v-model="temp.processName" v-trim maxlength="100" @focus="focus"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.ipRange')">
              <el-select v-model="temp.urlType" style="width: 100%" @change="urlTypeChange">
                <el-option
                  v-for="([key, val]) in urlTypeOptions"
                  :key="key"
                  :label="val"
                  :value="key"
                />
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <el-row v-if="temp.urlType === 1">
          <el-col :span="12">
            <FormItem :label="$t('pages.domain')" prop="domain">
              <el-input v-model="temp.domain" v-trim @focus="focus"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row v-else-if="temp.urlType === 2 || temp.urlType === 4">
          <el-col :span="12">
            <FormItem :label="$t('table.startIP')" prop="beginIp">
              <el-input v-model="temp.beginIp" :disabled="temp.urlType === 4" @focus="focus" @blur="inputBlur('endIp')"/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.endIp')" prop="endIp">
              <el-input v-model="temp.endIp" :disabled="temp.urlType === 4" @focus="focus" @blur="inputBlur('beginIp')"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row v-else>
          <el-col :span="12">
            <FormItem :label="$t('table.startIP')" prop="beginIpv6">
              <el-input v-model="temp.beginIpv6" :disabled="temp.urlType === 5" maxlength="39" @focus="focus" @blur="inputBlur('endIpv6')"/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.endIp')" prop="endIpv6">
              <el-input v-model="temp.endIpv6" :disabled="temp.urlType === 5" maxlength="39" @focus="focus" @blur="inputBlur('beginIpv6')"/>
            </FormItem>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.portRange')">
              <el-select v-model="temp.portType" style="width: 100%" @change="portTypeChange">
                <el-option
                  v-for="([key, value]) in portTypeOptions"
                  :key="key"
                  :label="value"
                  :value="key"
                />
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.beginPort')" prop="beginPort">
              <el-input
                v-model.number="temp.beginPort"
                maxlength="5"
                :disabled="temp.portType === 1"
                style="width: 100%"
                @blur="beginPortBlur"
                @keyup.native="number('beginPort')"
              />
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.endPort')" prop="endPort">
              <el-input
                v-model.number="temp.endPort"
                maxlength="5"
                :disabled="temp.portType === 1"
                style="width: 100%"
                @blur="endPortBlur"
                @keyup.native="number('endPort')"
              />
            </FormItem>
          </el-col>
        </el-row>
      </Form>
    </data-editor>
    <grid-table
      ref="table"
      :min-height="160"
      :multi-select="true"
      :show-pager="false"
      :selectable="selectable"
      :col-model="colModel"
      :row-datas="tableData"
      @selectionChangeEnd="selectionChangeEnd"
    />
  </LocalZoomIn>
</template>

<script>

import { isIPv4, isIPv6 } from '@/utils/validate';

export default {
  name: 'AiCustomRule',
  components: {},
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    tableData: { type: Array, default: () => [] } // 表格数据
  },
  data() {
    return {
      updatable: false,
      deletable: false,
      colModel: [
        { prop: 'aiName', label: 'aiName', width: '90', sort: true },
        { prop: 'processName', label: 'processName', width: '90', sort: true },
        { label: 'ipRange', width: '145', formatter: this.ipFormatter },
        { label: 'portRange', width: '100', sort: true, formatter: this.portFormatter }
      ],
      temp: {},
      defaultTemp: {
        aiName: undefined,
        processName: undefined,
        urlType: 4,
        domain: undefined,
        beginIp: '0.0.0.0',
        endIp: '***************',
        beginIpv6: '0:0:0:0:0:0:0:0',
        endIpv6: 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff',
        portType: 1,
        beginPort: 1,
        endPort: 65535
      },
      rules: {
        aiName: [
          { required: true, message: this.$t('pages.aiNameNotNull'), trigger: 'blur' }
        ],
        processName: [
          { required: true, message: this.$t('pages.processNameNotNull'), trigger: 'blur' }
        ],
        domain: [
          { required: true, message: this.$t('pages.validateMsg_domainName'), trigger: 'blur' }
        ],
        beginIp: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        endIp: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        beginIpv6: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipv6Validator, trigger: 'blur' }
        ],
        endIpv6: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipv6Validator, trigger: 'blur' }
        ],
        beginPort: [
          { required: true, message: this.$t('pages.validateMsg_beginPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        endPort: [
          { required: true, message: this.$t('pages.validateMsg_endPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ]
      },
      urlTypeOptions: [
        [4, this.$t('pages.allIPv4')],
        [2, this.$t('pages.customIPv4')],
        [5, this.$t('pages.allIPv6')],
        [3, this.$t('pages.customIPv6')],
        [1, this.$t('pages.domain')]
      ],
      portTypeOptions: [
        [1, this.$t('pages.allPort')],
        [2, this.$t('pages.userDefined')]
      ]
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    formRef() {
      return this.$refs.form
    },
    tableRef() {
      return this.$refs.table
    },
    selectable(row, index) {
      return this.formable;
    },
    selectionChangeEnd(selection) {
      this.deletable = selection.length > 0
      if (selection.length === 1) {
        this.updatable = true
      } else {
        this.updatable = false
        this.cancelData()
      }
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    addData() {
      return this.editData(row => {
        this.tableData.unshift(row)
      })
    },
    updateData() {
      return this.editData(row => {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].id === row.id) {
            this.tableData.splice(i, 1, row)
          }
        }
      })
    },
    editData(callback) {
      return this.formRef().validate().then(valid => {
        if (valid) {
          const { id, aiName, processName, urlType, domain, beginIp, endIp, beginIpv6, endIpv6, beginPort, endPort } = this.temp
          const row = { id: id || Date.now(), aiName, processName, urlType: urlType > 3 ? urlType - 2 : urlType, beginPort, endPort }
          if (row.urlType === 1) {
            row.domain = domain
          } else if (row.urlType === 2) {
            Object.assign(row, { beginIp, endIp })
          } else {
            row.beginIp = beginIpv6
            row.endIp = endIpv6
          }
          //  校验是否存在相同数据
          if (this.checkRowExists(row)) {
            this.$message({
              message: this.$t('pages.dataDuplication'),
              type: 'warning',
              duration: 3000
            })
            return false
          }
          callback && callback(row)
          this.cancelData()
        }
        return valid
      })
    },
    deleteData() {
      const toDeleteIds = this.tableRef().getSelectedIds()
      this.tableData.splice(0, this.tableData.length, ...this.tableRef().deleteRowData(toDeleteIds))
      this.cancelData()
    },
    cancelData() {
      const tableRef = this.tableRef();
      tableRef && tableRef.setCurrentRow()
      this.clearValidations()
    },
    beforeUpdateData() {
      this.resetTemp()
      const selected = this.tableRef().getSelectedDatas()
      if (selected.length === 1) {
        const { id, aiName, processName, urlType, domain, beginIp, endIp, beginPort, endPort } = selected[0]
        Object.assign(this.temp, { id, aiName, processName, urlType, beginPort, endPort, portType: 2 })
        if (urlType === 1) {
          this.temp.domain = domain
        } else if (urlType === 2) {
          Object.assign(this.temp, { beginIp, endIp })
        } else {
          this.temp.beginIpv6 = beginIp
          this.temp.endIpv6 = endIp
        }
      }
    },
    checkRowExists(row) {
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.rowEquals(this.tableData[i], row)) {
          return true
        }
      }
      return false
    },
    rowEquals(row1, row2) {
      if (row1.id === row2.id) {
        return false
      }
      return this.rowToString(row1) === this.rowToString(row2)
    },
    rowToString(row) {
      let str = row.aiName + '|' + row.processName + '|' + row.urlType + '|'
      if (row.urlType === 1) {
        str += row.domain
      } else {
        str += row.beginIp + '~' + row.endIp
      }
      str += '|' + row.beginPort + '~' + row.endPort
      return str
    },
    // 封装 clearValidate 方法，用于批量清除验证
    clearValidations(fields) {
      const formRef = this.formRef()
      formRef && formRef.clearValidate(fields)
    },
    urlTypeChange(value) {
      if (value !== 1) {
        this.clearValidations(['domain'])
      }
      if (value === 4) {
        this.clearValidations(['beginIp', 'endIp'])
        this.temp.beginIp = '0.0.0.0'
        this.temp.endIp = '***************'
      } else if (value === 5) {
        this.clearValidations(['beginIpv6', 'endIpv6'])
        this.temp.beginIpv6 = '0:0:0:0:0:0:0:0'
        this.temp.endIpv6 = 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff'
      } else if (value === 1) {
        this.clearValidations(['beginIp', 'endIp', 'beginIpv6', 'endIpv6'])
      }
    },
    portTypeChange(value) {
      if (value === 1) {
        this.clearValidations(['beginPort', 'endPort'])
        this.temp.beginPort = 1
        this.temp.endPort = 65535
      }
    },
    beginPortBlur() {
      const { beginPort } = this.temp
      if (!beginPort) {
        this.temp.beginPort = 1
      } else if (beginPort > 65535) {
        this.temp.beginPort = 65535
      }
    },
    endPortBlur() {
      const { endPort } = this.temp
      if (!endPort || endPort > 65535) {
        this.temp.endPort = 65535
      }
    },
    number(field) {
      const val = this.temp[field]
      if (val.toString().length > 0 && val == 0) {
        this.temp[field] = 1
      } else if (isNaN(val)) {
        this.temp[field] = val.replace(/[^\d]/g, '')
      }
    },
    focus(event) {
      event.currentTarget.select()
    },
    inputBlur(validateProp) {
      this.formRef().validateField(validateProp)
    },
    ipValidator(rule, value, callback) {
      if (this.temp.urlType !== 2 && this.temp.urlType !== 4) {
        callback()
        return
      }
      if (!value || !isIPv4(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text4')))
        return
      }
      if (this.temp.beginIp && this.temp.endIp) {
        const temp1 = this.temp.beginIp.split('.')
        const temp2 = this.temp.endIp.split('.')
        for (let i = 0; i < 4; i++) {
          const cpr = temp1[i] - temp2[i]
          if (cpr > 0) {
            callback(new Error(this.$t('pages.serverLibrary_text3')))
            return
          }
          if (cpr < 0) {
            break
          }
        }
      }
      callback()
    },
    ipv6Validator(rule, value, callback) {
      if (isIPv6(value)) {
        if (this.temp.beginIpv6 && this.temp.endIpv6) {
          const fullbeginIpv6 = this.getFullIPv6(this.temp.beginIpv6)
          const fullendIpv6 = this.getFullIPv6(this.temp.endIpv6)
          if (fullbeginIpv6 > fullendIpv6) {
            callback(new Error(this.$t('pages.serverLibrary_text3')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback(new Error(this.$t('pages.ipv6_text1')))
      }
    },
    getFullIPv6(src) {
      const temp = src.replace('::', '*')
      const secs = temp.split(':')
      let ret = ''
      for (let i = 0; i < secs.length; i++) {
        const t = secs[i]
        if (t.indexOf('*') >= 0) {
          const ss = t.split('*')
          ret += ss[0].padStart(4, '0')
          for (let j = 1; j < (8 - secs.length); j++) {
            ret += '0000'
          }
          ret += ss[1].padStart(4, '0');
        } else {
          ret += t.padStart(4, '0');
        }
      }
      return ret.toLocaleLowerCase();
    },
    portValidator(rule, value, callback) {
      if (isNaN(Number(value)) || Number(value) !== parseInt(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text5')))
      }
      if (value < 1 || value > 65535) {
        callback(new Error(this.$t('pages.serverLibrary_text6')))
      }
      const beginPort = Number(this.temp.beginPort)
      const endPort = Number(this.temp.endPort)
      if (beginPort && endPort) {
        if (beginPort > endPort) {
          callback(new Error(this.$t('pages.serverLibrary_text7')))
        } else {
          this.clearValidations(['beginPort', 'endPort'])
          callback()
        }
      } else {
        callback()
      }
    },
    ipFormatter(row) {
      if (row.urlType === 1) {
        return row.domain
      }
      return row.beginIp + ' - ' + row.endIp
    },
    portFormatter(row) {
      return row.beginPort + ' - ' + row.endPort
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
