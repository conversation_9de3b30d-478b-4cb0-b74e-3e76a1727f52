<template>
  <div class="ai-model-box">
    <el-table :data="tableRows">
      <el-table-column :label="$t('table.aiName')" prop="name" min-width="120"/>
      <el-table-column>
        <template slot="header" slot-scope="{}">
          <el-checkbox
            v-model="useWayHeader[0].checked"
            :indeterminate="useWayHeader[0].indeterminate"
            :disabled="!formable"
            @change="handleHeaderCheck(0)"
          >
            {{ $t('table.useWay') }}
          </el-checkbox>
        </template>
        <el-table-column v-for="(label, key) in useWayOpts" :key="key" min-width="120">
          <template slot="header" slot-scope="{}">
            <el-checkbox
              v-model="useWayHeader[key].checked"
              :indeterminate="useWayHeader[key].indeterminate"
              :disabled="!formable"
              @change="handleHeaderCheck(key)"
            >
              {{ label }}
            </el-checkbox>
          </template>
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.ways[key]" :disabled="!formable" @change="handleRowUseWayChange(scope.row, key)"/>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getAiNameDict } from '@/utils/dictionary'

export default {
  name: 'AiModelLimit',
  components: {},
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    tableData: { type: Array, default: () => [] } // 表格数据
  },
  data() {
    return {
      useWayOpts: {
        1: this.$t('pages.browser'),
        2: this.$t('pages.client'),
        4: this.$t('pages.plugin')
      },
      useWayHeader: {},
      useWayChanged: false,
      tableRows: []
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if (this.useWayChanged) {
          this.useWayChanged = false
          return
        }
        if (!val || !val.length) {
          this.resetTemp()
          return
        }
        const tableDataMap = {}
        val.forEach(item => {
          tableDataMap[item.aiName] = item
        })
        const useWayKeys = Object.keys(this.useWayOpts)
        const useWayHeader = {}
        useWayKeys.forEach(key => {
          useWayHeader[key] = { checkedCount: 0, checked: true }
        })
        this.tableRows = getAiNameDict().map(({ value: id, label: name }) => {
          const ways = {}
          const row = tableDataMap[id]
          if (row) {
            const useWays = this.numToList(row.useWay, 4)
            useWayKeys.forEach(key => {
              const checked = useWays.includes(parseInt(key))
              ways[key] = checked
              useWayHeader[key].checked = useWayHeader[key].checked && checked
              if (checked) {
                useWayHeader[key].checkedCount++
              }
            })
          } else {
            useWayKeys.forEach(key => {
              ways[key] = false
              useWayHeader[key].checked = false
            })
          }
          return { id, name, ways }
        })
        let indeterminate = false
        let preChecked = null
        useWayKeys.forEach(key => {
          const checkedCount = useWayHeader[key].checkedCount
          useWayHeader[key].indeterminate = checkedCount > 0 && checkedCount < this.tableRows.length
          delete useWayHeader[key].checkedCount
          if (useWayHeader[key].indeterminate || (preChecked != null && preChecked !== useWayHeader[key].checked)) {
            indeterminate = true
          }
          preChecked = useWayHeader[key].checked
        })
        useWayHeader[0] = { indeterminate, checked: indeterminate ? false : preChecked }
        this.useWayHeader = useWayHeader
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    resetTemp() {
      const useWayKeys = Object.keys(this.useWayOpts)
      useWayKeys.concat(['0']).forEach(key => {
        this.$set(this.useWayHeader, key, { indeterminate: false, checked: false })
      })
      this.tableRows = getAiNameDict().map(({ value: id, label: name }) => {
        const ways = {}
        useWayKeys.forEach(key => {
          ways[key] = false
        })
        return { id, name, ways }
      })
    },
    handleHeaderCheck(key) {
      this.useWayHeader[key].indeterminate = false
      const { checked } = this.useWayHeader[key]
      // console.log('handleHeaderCheck(key)', key, { ...this.useWayHeader[key] })
      const useWayKeys = Object.keys(this.useWayOpts)
      if (key === 0) {
        useWayKeys.forEach(k => {
          this.useWayHeader[k].checked = checked
          this.useWayHeader[k].indeterminate = false
          this.tableRows.forEach(row => {
            row.ways[k] = checked
          })
        })
      } else {
        this.tableRows.forEach(row => {
          row.ways[key] = checked
        })
        this.correctGlobalCheckedStatus(checked, key)
      }
      this.updateTableData()
    },
    handleRowUseWayChange(row, key) {
      // console.log('handleRowUseWayChange(row, key)', { ...row }, key)
      const cellChecked = row.ways[key]
      for (let i = 0; i < this.tableRows.length; i++) {
        const row = this.tableRows[i]
        if (row.ways[key] !== cellChecked) {
          this.useWayHeader[key].checked = false
          this.useWayHeader[key].indeterminate = true

          this.useWayHeader[0].checked = false
          this.useWayHeader[0].indeterminate = true
          this.updateTableData()
          return
        }
      }
      this.useWayHeader[key].checked = cellChecked
      this.useWayHeader[key].indeterminate = false
      this.correctGlobalCheckedStatus(cellChecked, key)
      this.updateTableData()
    },
    correctGlobalCheckedStatus(checked, key) {
      const useWayKeys = Object.keys(this.useWayOpts)
      let indeterminate = false
      for (let i = 0; i < useWayKeys.length; i++) {
        const useWayKey = useWayKeys[i]
        if (key === useWayKey) {
          continue
        }
        const useWayOpt = this.useWayHeader[useWayKey]
        if (useWayOpt.indeterminate || useWayOpt.checked !== checked) {
          indeterminate = true
          break
        }
      }
      this.useWayHeader[0].indeterminate = indeterminate
      if (indeterminate) {
        this.useWayHeader[0].checked = false
      } else {
        this.useWayHeader[0].checked = checked
      }
    },
    updateTableData() {
      this.useWayChanged = true
      const useWayKeys = Object.keys(this.useWayOpts)
      const tableData = []
      this.tableRows.forEach(row => {
        let useWay = 0
        useWayKeys.forEach(key => {
          if (row.ways[key]) {
            useWay += parseInt(key)
          }
        })
        if (useWay) {
          tableData.push({ aiName: parseInt(row.id), useWay })
        }
      })
      this.tableData.splice(0, this.tableData.length, ...tableData)
    }
  }
}
</script>

<style lang="scss" scoped>
  .ai-model-box {
    margin: 10px;
    .el-table {
      >>>.el-table__cell>.cell {
        text-align: center;
      }
      >>>th .el-checkbox__label {
        font-weight: bold;
      }
      >>>.el-checkbox__input.is-disabled {
        &.is-checked, &.is-indeterminate {
          .el-checkbox__inner {
            background-color: #888;
            border-color: #888;
          }
        }
      }
    }
  }
</style>
