<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />
        <el-tabs v-model="activeName">
          <el-tab-pane name="control" :label="$t('pages.aiModelLimit')">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <ai-model-limit :formable="formable" :table-data="temp.modelLimits"/>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="8px">
              <el-radio-group v-model="temp.exceptRule" :disabled="!formable">
                <!--终端监测到检测规则之内的AI模型时触发响应规则-->
                <el-radio :label="0">{{ $t('pages.aiModelLimitInDetectionRule') }}</el-radio>
                <!--终端监测到检测规则之外的AI模型时触发响应规则-->
                <el-radio :label="1">{{ $t('pages.aiModelLimitOutDetectionRule') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <resp-rule
              :formable="formable"
              :dialog-status="dialogStatus"
              :block-flag="temp.blockFlag"
              :is-alarm="temp.isAlarm"
              :rule-id="temp.ruleId"
              :alarm-mode="temp.alarmMode"
              :alarm-interval="temp.alarmInterval"
              @blockChange="value => temp.blockFlag = value"
              @ruleIsCheck="value => temp.isAlarm = value"
              @getRuleId="value => temp.ruleId = value"
              @validate="value => validRuleId = value"
              @alarmModeChange="value => temp.alarmMode = value"
              @alarmIntervalInput="value => temp.alarmInterval = value"
            />
            <!--<el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="18px">
              <el-checkbox v-model="temp.blockFlag" :disabled="!formable" :true-label="1" :false-label="0">
                {{ $t('pages.aiModuleUseLimit') }}
              </el-checkbox>
            </FormItem>
            <ResponseContent
              :status="dialogStatus"
              :select-style="{ 'margin-top': '5px' }"
              :show-select="true"
              :rule-type-name="$t('table.violationName')"
              :editable="formable"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="temp.ruleId"
              @ruleIsCheck="value => temp.isAlarm = value"
              @getRuleId="value => temp.ruleId = value"
              @validate="value => validRuleId = value"
            />-->
          </el-tab-pane>
          <el-tab-pane name="custom" :label="$t('pages.customRule')">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <ai-custom-rule :formable="formable" :table-data="temp.customLimits"/>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="18px">
              {{ $t('pages.aiModelLimitInDetectionRule') }}
            </FormItem>
            <resp-rule
              :formable="formable"
              :dialog-status="dialogStatus"
              :block-flag="temp.customBlockFlag"
              :is-alarm="temp.isCustomAlarm"
              :rule-id="temp.customRuleId"
              :alarm-mode="temp.customAlarmMode"
              :alarm-interval="temp.customAlarmInterval"
              @blockChange="value => temp.customBlockFlag = value"
              @ruleIsCheck="value => temp.isCustomAlarm = value"
              @getRuleId="value => temp.customRuleId = value"
              @validate="value => validCustomRuleId = value"
              @alarmModeChange="value => temp.customAlarmMode = value"
              @alarmIntervalInput="value => temp.customAlarmInterval = value"
            />
            <!--<el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="18px">
              <el-checkbox v-model="temp.customBlockFlag" :disabled="!formable" :true-label="1" :false-label="0">
                {{ $t('pages.aiModuleUseLimit') }}
              </el-checkbox>
            </FormItem>
            <ResponseContent
              :status="dialogStatus"
              :select-style="{ 'margin-top': '5px' }"
              :show-select="true"
              :rule-type-name="$t('table.violationName')"
              :editable="formable"
              read-only
              :prop-check-rule="!!temp.isCustomAlarm"
              :show-check-rule="true"
              :prop-rule-id="temp.customRuleId"
              @ruleIsCheck="value => temp.isCustomAlarm = value"
              @getRuleId="value => temp.customRuleId = value"
              @validate="value => validCustomRuleId = value"
            />-->
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </tr-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importWebPostStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import ImportStg from '@/views/common/importStg'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import AiModelLimit from './modelLimit'
import AiCustomRule from './customRule'
import RespRule from './respRule'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import { timeIdValidator, validatePolicy } from '@/utils/validate'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import {
  createStrategy,
  deleteStrategy,
  getByName,
  getStrategyList,
  updateStrategy
} from '@/api/behaviorManage/network/aiModelStrategy'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'AiModelStrategy',
  components: { ImportStg, ResponseContent, AiModelLimit, AiCustomRule, RespRule },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 287,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '100', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      activeName: 'control',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: undefined,
        entityId: undefined,
        timeId: 1,
        exceptRule: 0,
        blockFlag: 1,
        isAlarm: 0, // 是否触发违规响应规则
        ruleId: undefined, // 违规响应规则ID
        alarmMode: 1,
        alarmInterval: 0,
        customBlockFlag: 0,
        isCustomAlarm: 0,
        customRuleId: undefined,
        customAlarmMode: 1,
        customAlarmInterval: 0
      },
      validRuleId: false,
      validCustomRuleId: false,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.aiModelStrategy'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.aiModelStrategy'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp(source) {
      this.temp = Object.assign({ modelLimits: [], customLimits: [] }, this.defaultTemp, source)
      this.activeName = 'control'
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp(JSON.parse(JSON.stringify(row)))
      this.temp.customLimits.forEach((item, index) => {
        item.id = index + 1
      })
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    formatFormData() {
      const tempData = Object.assign({}, this.temp)
      // if (!tempData.isBackup && tempData.fileBackUpLimit > 0) {
      //   tempData.fileBackUpLimit = 0
      // }
      return tempData
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validCustomRuleId) {
          createStrategy(this.formatFormData()).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validCustomRuleId) {
          updateStrategy(this.formatFormData()).then(() => {
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter(row, data) {
      let str = ''
      if (row.exceptRule) {
        str += this.$t('pages.aiModelLimitOutDetectionRule')
      } else {
        str += this.$t('pages.aiModelLimitInDetectionRule')
      }
      if (row.customRuleFlag) {
        str += '; ' + this.$t('pages.enableCustomRule')
      }
      return str
    },
    buttonFormatter(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
