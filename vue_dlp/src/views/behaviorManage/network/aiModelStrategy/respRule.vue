<template>
  <div>
    <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
    <FormItem label-width="18px">
      <el-checkbox :value="blockFlag" :disabled="!formable" :true-label="1" :false-label="0" @change="blockChange">
        {{ $t('pages.aiModuleUseLimit') }}
      </el-checkbox>
    </FormItem>
    <ResponseContent
      :status="dialogStatus"
      :select-style="{ 'margin-top': '5px' }"
      :show-select="true"
      :rule-type-name="$t('table.violationName')"
      :editable="formable"
      read-only
      :prop-check-rule="!!isAlarm"
      :show-check-rule="true"
      :prop-rule-id="ruleId"
      @ruleIsCheck="ruleIsCheck"
      @getRuleId="getRuleId"
      @validate="validate"
    />
    <!--<FormItem label-width="8px">
      <el-radio-group v-if="isAlarm" :value="alarmMode" :disabled="!formable" @input="alarmModeChange">
        <el-radio :label="1">{{ $t('pages.repeatAlarmOpt1') }}</el-radio>
        <el-radio :label="2">
          <i18n path="pages.repeatAlarmOpt2">
            <el-input-number
              slot="interval"
              :value="alarmInterval"
              :disabled="!formable || alarmMode !== 2"
              :min="1"
              :max="120"
              size="mini"
              controls-position="right"
              @input="alarmIntervalInput"
            />
          </i18n>
        </el-radio>
        <el-radio :label="3">{{ $t('pages.repeatAlarmOpt3') }}</el-radio>
      </el-radio-group>
    </FormItem>-->
  </div>
</template>

<script>
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'

export default {
  name: 'RespRule',
  components: { ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    dialogStatus: { type: String, default: '' }, // 对话框状态
    blockFlag: { type: Number, default: 0 },
    isAlarm: { type: Number, default: 0 },
    ruleId: { type: Number, default: null },
    alarmMode: { type: Number, default: 1 },
    alarmInterval: { type: Number, default: 1 }
  },
  methods: {
    blockChange(value) {
      this.$emit('blockChange', value)
    },
    ruleIsCheck(value) {
      this.$emit('ruleIsCheck', value)
    },
    getRuleId(value) {
      this.$emit('getRuleId', value)
    },
    validate(value) {
      this.$emit('validate', value)
    },
    alarmModeChange(value) {
      this.$emit('alarmModeChange', value)
    },
    alarmIntervalInput(value) {
      this.$emit('alarmIntervalInput', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.el-radio-group {
  .el-radio {
    min-width: 400px;
    .el-input-number {
      width: 100px;
    }
  }
}
</style>
