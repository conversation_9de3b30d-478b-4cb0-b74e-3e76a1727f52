<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="isolationStrategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="strategySelectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="dialogStatus=''"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 700px; margin-left: 30px;"
      >
        <stg-target-form-item
          ref="stgTargetItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.stgName')" prop="name">
              <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('text.effectTime')" class="content-flex" prop="timeId">
              <el-select v-model="temp.timeId" :disabled="!formable">
                <el-option
                  v-for="item in timeInfoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <link-button btn-class="editBtn" :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('text.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit class="word-limit-3digits" />
        </FormItem>
        <FormItem v-if="treeable" :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <!--<FormItem label="断开违规网络">
          <el-switch
            v-model="temp.controlCmd"
            :disabled="!formable"
            :active-value="2"
            :inactive-value="0"
          />
        </FormItem>-->
        <el-card class="box-card" :body-style="{'padding': '0'}">
          <div slot="header">
            <span>网络白名单</span>
            <span style="color:red">{{ ipValidMsg }}</span>
            <div v-if="formable" class="btn-box">
              <el-button @click="createDefaultItem">添加默认值</el-button>
              <el-button @click="createItem">{{ $t('button.insert') }}</el-button>
              <el-button :disabled="!ipPortDeleteable" @click="deleteItem">{{ $t('button.delete') }}</el-button>
            </div>
          </div>
          <grid-table
            ref="ipPortList"
            :height="200"
            :show-pager="false"
            row-no-label=" "
            :multi-select="true"
            :col-model="itemColModel"
            :row-datas="itemRowData"
            @selectionChangeEnd="ipPortSelectionChange"
          />
        </el-card>
        <!-- <FormItem label-width="0">
          <el-switch
            v-model="temp.controlCmd"
            :disabled="!formable"
            :active-value="2"
            :inactive-value="0"
            active-text="断开违规网络"
          />
        </FormItem> -->
        <ResponseContent
          :status="dialogStatus"
          :show-select="true"
          :editable="formable"
          read-only
          :prop-check-rule="propCheckRule"
          :prop-rule-id="propRuleId"
          @getRuleId="getRuleId"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-mode="1"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="()=>{dialogFormVisible = false;}">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import {
  fetchList, getByName, createData, updateData, deleteData
} from '@/api/behaviorManage/network/netCtrl'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { isIPv4, isPort, compareIP, validatePolicy, timeIdValidator } from '@/utils/validate'
import { stgEntityIconFormatter, timeInfoFormatter } from '@/utils/formatter'

export default {
  name: 'Isolation',
  components: { ResponseContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      stgCode: 16,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'timeId', label: 'effectTime', width: '100', formatter: timeInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'keyword', label: 'stgMessage', width: '200', formatter: this.formatStrateMsg },
        { prop: 'remark', label: 'remark', width: '200' },
        { prop: 'active', label: 'enable', fixedWidth: '80', type: 'icon', iconClass: 'active' },
        {
          prop: 'createdTime',
          label: 'operate',
          type: 'button',
          fixed: 'right',
          fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      itemColModel: [
        { prop: 'beginIp', label: 'startIP', type: 'input', width: '40', disabled: !this.formable },
        { prop: 'endIp', label: 'endIp', type: 'input', width: '40', disabled: !this.formable },
        { prop: 'beginPort', label: 'beginPort', type: 'input', width: '25', disabled: !this.formable },
        { prop: 'endPort', label: 'endPort', type: 'input', width: '25', disabled: !this.formable }
      ],
      query: {
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      ipPortDeleteable: false,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        timeId: 1,
        controlCmd: 2,
        notice: '',
        whiteList: [],
        entityType: undefined,
        entityId: undefined,
        ruleId: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.formable ? '修改网络隔离设置策略' : '网络隔离设置策略详情',
        create: '新增网络隔离设置策略'
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.infoRequired', { info: this.$t('pages.stgName') }), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.timeRequired'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ]
      },
      itemRowData: [],
      submitting: false,
      propCheckRule: false,
      propRuleId: undefined,
      ipValidMsg: ''
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    gridTable() {
      return this.$refs['isolationStrategyList']
    },
    allRules() {
      return this.$store.getters.alarmRules.map(item => {
        return {
          label: item.name,
          value: item.id
        }
      })
    }
  },
  watch: {
    'temp.alarm'(newValue, oldValue) {
      if (!newValue) {
        this.temp.notice = false
      }
    }
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    ipPortTable: function() {
      return this.$refs['ipPortList']
    },
    formatStrateMsg: function(row) {
      var msg = ''
      var ipmsg = 'ip范围：'
      var portmsg = '端口范围：'
      if (row.whiteList != null && row.whiteList.length > 0) {
        for (const ipObj of row.whiteList) {
          ipmsg += ipObj.beginIp + '-' + ipObj.endIp + ','
          portmsg += ipObj.beginPort + '-' + ipObj.endPort + ','
        }
        ipmsg = (ipmsg.substring(ipmsg.length - 1) === ',') ? ipmsg.substring(0, ipmsg.length - 1) : ipmsg
        portmsg = (portmsg.substring(portmsg.length - 1) === ',') ? portmsg.substring(0, portmsg.length - 1) : portmsg
      }
      msg = ipmsg + ' ' + portmsg
      if (row.ruleId) {
        this.allRules.forEach(item => {
          if (item.value == row.ruleId) {
            msg += '，违规响应方式：' + item.label
          }
        })
      }
      /* if (row.discNet) {
        msg += '断开违规网络,'
      }
      if (row.alarm) {
        msg += '告警管理端（管理人员）'
        if (!row.notice) {
          msg += '并弹出警告'
        }
      }*/
      return msg
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return fetchList(searchQuery)
    },
    strategySelectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.itemRowData.splice(0, this.itemRowData.length)
      this.temp = Object.assign({}, this.defaultTemp)
      this.ipValidMsg = ''
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.propCheckRule = false
      this.propRuleId = undefined
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      const that = this
      this.temp.whiteList.forEach(function(data, index) {
        that.itemRowData.push(Object.assign(data, { id: index }))
      })
      this.propCheckRule = !!row.ruleId
      this.propRuleId = row.ruleId
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.dialogStatus = 'update'
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
    },
    handleExport() {
    },
    ipPortSelectionChange(rowDatas) {
      this.ipPortDeleteable = rowDatas.length > 0
    },
    validateWhiteList() {
      this.ipValidMsg = ''
      if (this.itemRowData.length === 0) {
        this.ipValidMsg = '请至少配置一条网络白名单！'
      }
      for (let i = 0; this.ipValidMsg.length === 0 && i < this.itemRowData.length; i++) {
        const rowData = this.itemRowData[i]
        if (!isIPv4(rowData.beginIp)) {
          this.ipValidMsg = this.$t('pages.validateCollect_ip', { row: i + 1 })
        } else if (!isIPv4(rowData.endIp)) {
          this.ipValidMsg = this.$t('pages.validateCollect_ip1', { row: i + 1 })
        } else if (compareIP(rowData.beginIp, rowData.endIp) > 0) {
          this.ipValidMsg = this.$t('pages.validateCollect_ip2', { row: i + 1 })
        } else if (!isPort(rowData.beginPort)) {
          this.ipValidMsg = '第 ' + (i + 1) + ' 行的起始端口不正确，端口范围1-65535，请修正！'
        } else if (!isPort(rowData.endPort)) {
          this.ipValidMsg = '第 ' + (i + 1) + ' 行的截止端口不正确，端口范围1-65535，请修正！'
        } else if (parseInt(rowData.endPort) < parseInt(rowData.beginPort)) {
          this.ipValidMsg = '第 ' + (i + 1) + ' 行的截止端口不能小于起始端口，请修正！'
        }
      }
      return this.ipValidMsg.length === 0
    },
    formatSubmitData() {
      const ipSegments = []
      const keys = []
      this.itemRowData.forEach(ips => {
        const ipPort = ips.beginIp + '-' + ips.endIp + '-' + ips.beginPort + '-' + ips.endPort
        if (keys.indexOf(ipPort) < 0) {
          keys.push(ipPort)
          delete ips.id
          ipSegments.push(ips)
        }
      })
      this.temp.whiteList = ipSegments
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validateWhiteList()) {
          this.formatSubmitData()
          createData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.$refs.isolationStrategyList.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validateWhiteList()) {
          this.formatSubmitData()
          updateData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.$refs.isolationStrategyList.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    createDefaultItem() {
      this.ipValidMsg = ''
      let tempData = null
      if (this.itemRowData && this.itemRowData.length > 0) {
        tempData = this.itemRowData[0]
      }
      if (tempData && !tempData.beginIp && !tempData.endIp && !tempData.beginPort && !tempData.endPort) {
        this.itemRowData.splice(0, 1)
      }
      this.itemRowData.splice(0, 0, {
        id: new Date().getTime(),
        beginIp: '0.0.0.0',
        endIp: '***************',
        beginPort: 1,
        endPort: 65535
      })
    },
    createItem() {
      this.ipValidMsg = ''
      let tempData = null
      if (this.itemRowData && this.itemRowData.length > 0) {
        tempData = this.itemRowData[0]
      }
      if (!tempData || tempData.beginIp || tempData.endIp || tempData.beginPort || tempData.endPort) {
        this.itemRowData.splice(0, 0, {
          id: new Date().getTime(),
          beginIp: '',
          endIp: '',
          beginPort: undefined,
          endPort: undefined
        })
      }
    },
    deleteItem() {
      this.ipValidMsg = ''
      const toDeleteIds = this.ipPortTable().getSelectedIds()
      for (let i = 0; i < this.itemRowData.length; i++) {
        const item = this.itemRowData[i]
        if (toDeleteIds.indexOf(item.id) > -1) {
          this.itemRowData.splice(i, 1)
          i--
        }
      }
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyNameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    getRuleId(value) {
      this.temp.ruleId = value
    }
  }
}
</script>
<style lang="scss" scoped>
  .box-card{
    position: relative;
    >>>.el-card__header{
      padding: 10px 20px;
    }
  }
  .btn-box{
    position: absolute;
    top: 5px;
    right: 20px;
  }
</style>
