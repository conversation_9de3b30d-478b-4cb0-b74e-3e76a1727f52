<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.webPort_text1')"
      :stg-code="14"
      :width="860"
      :allow-all-os="false"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      time-able
      :time-mode="2"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <data-editor
          :formable="formable"
          :append-to-body="true"
          :popover-width="740"
          :updateable="webPortEditable"
          :deletable="webPortDeleteable"
          :add-func="createWebPort"
          :update-func="updateWebPort"
          :delete-func="deleteWebPort"
          :cancel-func="cancelWebPort"
          :before-update="beforeUpdateWebPort"
        >
          <Form ref="portForm" :rules="tempPrules" :model="tempP" label-position="right" label-width="120px" :extra-width="{ en: 0 }" style="width: 650px;">
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('table.programName2')">
                  <el-select v-model="tempP.exeName" is-filter allow-create :title="tempP.exeName" style="width: calc(100% - 1px);" filterable>
                    <el-option
                      v-for="(value, key) in exeNameOptions"
                      :key="key"
                      :label="value"
                      :value="key"
                    />
                    <el-option v-if="operation !== 'update'" :key="selfUuid" :value="selfUuid" :label="$t('pages.webPort_self')"/>
                  </el-select>
                </FormItem>
              </el-col>
              <el-col :span="2" style="text-align: center">
                <el-tooltip class="item" effect="dark" placement="top-start">
                  <span slot="content">{{ $t('pages.webPort_batchInsertHint', { process: $t('pages.webPort_self') }) }}<br/></span>
                  <i class="el-icon-question" style="color: #409EFF;"></i>
                </el-tooltip>
                <el-upload
                  ref="uploadProcess"
                  name="processFile"
                  action="1111"
                  accept=".exe"
                  :limit="1"
                  :show-file-list="false"
                  :before-upload="beforeUpload"
                  class="inline-block"
                >
                  <el-button type="primary" size="mini" icon="el-icon-upload" style="padding: 0 8px; margin: 2px 0;"></el-button>
                </el-upload>
              </el-col>
              <el-col v-if="(batchAddShow && operation !== 'update')" :span="10">
                <el-button type="primary" size="mini" style="margin-bottom: 0px;margin-top: 2px;" @click="showAppSelectDlg">
                  {{ $t('pages.software_Msg16') }}
                </el-button>
                <el-button size="mini" style="margin: 2px 0 0;" @click="handleClear">
                  {{ $t('button.clear') }}
                </el-button>
              </el-col>

              <el-col v-if="operation === 'update'" :span="10">
                <el-button type="primary" size="mini" style="margin-bottom: 0px;margin-top: 2px;" @click="showUpdateAppSelectDlg">
                  {{ $t('pages.software_Msg16') }}
                </el-button>
              </el-col>
            </el-row>

            <el-row v-if="(batchAddShow && operation !== 'update')">
              <el-col :span="24">
                <FormItem prop="exeNames">
                  <tag v-model="tempP.exeNames" :border="true" :overflow-able="true" max-height="150px" :list="tempP.exeNames" :disabled="!formable" @tagChange="tagChange"/>
                </FormItem>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('table.behavior')" prop="mode">
                  <el-select v-model="tempP.mode" style="width: 100%">
                    <el-option
                      v-for="(value, key) in modeOptions"
                      :key="key"
                      :label="value"
                      :value="key"
                    />
                  </el-select>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('table.protocol')" prop="protocol">
                  <el-select v-model="tempP.protocol" style="width: 100%">
                    <el-option
                      v-for="(value, key) in protocolOptions"
                      :key="key"
                      :label="value"
                      :value="key"
                    />
                  </el-select>
                </FormItem>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('table.ipRange')">
                  <el-select v-model="tempP.ipType" style="width: 100%" @change="ipTypeChange">
                    <el-option
                      v-for="(item, key) in ipTypeOptions"
                      :key="key"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </FormItem>
              </el-col>
            </el-row>
            <el-row>
              <el-col v-if="tempP.ipType == 1 || tempP.ipType == 2" :span="12">
                <FormItem :label="$t('table.startIP')" prop="beginIp">
                  <el-input v-model="tempP.beginIp" :disabled="tempP.ipType === '1'" @focus="focus" @blur="inputBlur('endIp')"/>
                </FormItem>
              </el-col>
              <el-col v-if="tempP.ipType == 1 || tempP.ipType == 2" :span="12">
                <FormItem :label="$t('table.endIp')" prop="endIp">
                  <el-input v-model="tempP.endIp" :disabled="tempP.ipType === '1'" @focus="focus" @blur="inputBlur('beginIp')"/>
                </FormItem>
              </el-col>
              <el-col v-if="tempP.ipType == 3" :span="12">
                <FormItem :label="$t('pages.domain')" prop="domainAddr" :rules="(tempP.ipType === '3')? rules.domainAddr : [{ required: false}]">
                  <el-input v-model="tempP.domainAddr" v-trim @focus="focus"/>
                </FormItem>
              </el-col>
            </el-row>
            <el-row v-if="tempP.ipType == 4 || tempP.ipType == 5">
              <el-col :span="12">
                <FormItem :label="$t('table.startIP')" prop="beginIpv6">
                  <el-input v-model="tempP.beginIpv6" :disabled="tempP.ipType == 4" maxlength="39" @focus="focus" @blur="inputBlur('endIpv6')"></el-input>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('table.endIp')" prop="endIpv6">
                  <el-input v-model="tempP.endIpv6" :disabled="tempP.ipType == 4" maxlength="39" @focus="focus" @blur="inputBlur('beginIpv6')"></el-input>
                </FormItem>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('table.portRange')">
                  <el-select v-model="tempP.portType" style="width: 100%" @change="portTypeChange">
                    <el-option
                      v-for="(value, key) in portTypeOptions"
                      :key="key"
                      :label="value"
                      :value="key"
                    />
                  </el-select>
                </FormItem>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('table.beginPort')" prop="beginPort">
                  <el-input
                    v-model.number="tempP.beginPort"
                    maxlength="5"
                    :disabled="tempP.portType === '1'"
                    style="width: 100%"
                    @blur="beginPortBlur"
                    @keyup.native="number('beginPort')"
                  />
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('table.endPort')" prop="endPort">
                  <el-input
                    v-model.number="tempP.endPort"
                    maxlength="5"
                    :disabled="tempP.portType === '1'"
                    style="width: 100%"
                    @blur="endPortBlur"
                    @keyup.native="number('endPort')"
                  />
                </FormItem>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('table.effectTime')" class="content-flex" prop="timeId">
                  <el-select v-model="tempP.timeId" :disabled="!formable" :placeholder="$t('text.select')">
                    <el-option
                      v-for="item in timeInfoOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <link-button btn-class="editBtn" :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
                </FormItem>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <FormItem prop="lan">
                  <el-checkbox v-model="tempP.lan" :false-label="0" :true-label="1">{{ $t('pages.webPort_text2') }}</el-checkbox>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem label-width="20px" prop="process">
                  <el-checkbox v-model="tempP.process" :false-label="0" :true-label="2" :disabled="tempP.exeName == '*.*' || tempP.exeName == 'other.*'">{{ $t('pages.thisRuleIsEffectiveForChildProcesses') }}</el-checkbox>
                </FormItem>
              </el-col>
            </el-row>
          </Form>
        </data-editor>
        <grid-table
          ref="portList"
          :height="250"
          :multi-select="true"
          :show-pager="false"
          :selectable="selectable"
          :col-model="portColModel"
          :row-datas="temp.webPort"
          :autoload="false"
          @selectionChangeEnd="webPortSelectionChange"
        />
      </template>
    </stg-dialog>

    <!-- 应用程序库 -->
    <app-select-dlg ref="appLib" :append-to-body="true" @select="appendFile" />
    <app-select-dlg ref="updateAppLib" :append-to-body="true" :multiple-group="false" :multiple="false" @select="appendFile" />
  </div>
</template>

<script>
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/behaviorManage/network/webPort'
import { isIPv4, isIPv6, timeIdValidator } from '@/utils/validate'
import { timeInfoFormatter } from '@/utils/formatter'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'

export default {
  name: 'WebPortDlg',
  components: { AppSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },

  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        active: false,
        remark: '',
        webPort: [],
        webDomain: [],
        entityType: null,
        entityId: null
      },
      tempP: {},
      defaultTempP: {
        id: undefined,
        exeName: '*.*',
        mode: '1',
        protocol: '1',
        portType: '1',
        ipType: '1',
        beginPort: 1,
        endPort: 65535,
        beginIp: '0.0.0.0',
        endIp: '***************',
        beginIpv6: '0:0:0:0:0:0:0:0',
        endIpv6: 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff',
        domainAddr: '',
        timeId: 1,
        includeLan: 0,
        lan: 0,
        process: 0,
        exeNames: []
      },
      portColModel: [
        { prop: 'exeName', label: 'programName', width: '90', sort: true, formatter: this.exeNameFormatter },
        { prop: 'mode', label: 'behavior', width: '70', sort: true, formatter: this.modeFormatter },
        { prop: 'protocol', label: 'protocol', width: '100', sort: true, formatter: this.protocolFormatter },
        { prop: 'timeId', label: 'effectTime', width: '100', sort: true, sortOriginal: true, formatter: timeInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { label: 'ipRange', width: '145', formatter: this.ipFormatter },
        { label: 'portRange', width: '100', sort: true, formatter: this.portFormatter },
        { prop: 'lan', label: 'includeLanl', width: '140', sort: true, formatter: this.lanFormatter },
        { prop: 'process', label: 'childProcessIsEffective', width: '140', sort: true, formatter: this.processFormatter }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      tempPrules: {
        beginIp: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        endIp: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        beginIpv6: [
          { required: true, message: this.$t('pages.validateMsg_beginIp'), trigger: 'blur' },
          { validator: this.ipv6Validator, trigger: 'blur' }
        ],
        endIpv6: [
          { required: true, message: this.$t('pages.validateMsg_endIp'), trigger: 'blur' },
          { validator: this.ipv6Validator, trigger: 'blur' }
        ],
        domainAddr: [
          { required: true, message: this.$t('pages.validateMsg_domainName'), trigger: 'blur' }
        ],
        beginPort: [
          { required: true, message: this.$t('pages.validateMsg_beginPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        endPort: [
          { required: true, message: this.$t('pages.validateMsg_endPort'), trigger: 'blur' },
          { validator: this.portValidator, trigger: 'blur' }
        ],
        exeNames: [
          { validator: this.exeNamesValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ]
      },
      exeNameOptions: {},
      exeNameOptionsMap: {
        1: {
          '*.*': this.$t('pages.allProgram'),
          'IEXPLORE.EXE': 'IEXPLORE.EXE',
          'MAXTHON.EXE': 'MAXTHON.EXE',
          'FIREFOX.EXE': 'FIREFOX.EXE',
          '360SE.EXE': '360SE.EXE',
          'TTRAVELER.EXE': 'TTRAVELER.EXE',
          'FOXMAIL.EXE': 'FOXMAIL.EXE',
          'OUTLOOK.EXE': 'OUTLOOK.EXE',
          'QQ.EXE': 'QQ.EXE',
          'MSN.EXE': 'MSN.EXE',
          'other.*': this.$t('pages.otherProgram')
        },
        2: {
          '*.*': this.$t('pages.allProgram'),
          'firefox': 'firefox',
          'firefox-bin': 'firefox-bin',
          'chrome': 'chrome',
          'bash': 'bash',
          'ps': 'ps',
          'other.*': this.$t('pages.otherProgram')
        },
        4: {
          '*.*': this.$t('pages.allProgram'),
          'com.apple.WebKi': 'Safari(旧)',
          'com.apple.Safar': 'Safari(新)',
          'firefox': 'Firefox',
          'Google Chrome H': 'Chrome',
          'other.*': this.$t('pages.otherProgram')
        }
      },
      modeOptions: { '1': this.$t('pages.allow'), '2': this.$t('pages.prohibit') },
      protocolOptions: {},
      protocolOptionsMap: {
        1: { '1': 'TCP', '2': 'UDP' },
        2: { '1': 'TCP', '2': 'UDP' },
        4: { '1': 'TCP' }
      },
      portTypeOptions: { '1': this.$t('pages.allPort'), '2': this.$t('pages.userDefined') },
      // ipTypeOptions: { '1': '全部IPv4', '2': '自定义IPv4', '3': '域名', '4': '全部IPv6', '5': '自定义IPv6' },
      ipTypeOptions: [{ label: this.$t('pages.allIPv4'), value: '1' }, { label: this.$t('pages.customIPv4'), value: '2' },
        { label: this.$t('pages.allIPv6'), value: '4' }, { label: this.$t('pages.customIPv6'), value: '5' }, { label: this.$t('pages.domain'), value: '3' }],
      webPortEditable: false,
      webPortDeleteable: false,

      selfUuid: 'self' + new Date().getTime(),
      operation: '',   // 处于更新文件
      updateTempExeName: ''   //  临时执行文件
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    batchAddShow() {
      return this.tempP.exeName === this.selfUuid
    }
  },
  watch: {
    'tempP.exeName'(newVal, oldVal) {
      if (newVal === '*.*' || newVal === 'other.*') {
        this.tempP.process = 0
      }
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    selectable(row, index) {
      if (this.formable == false) {
        return false
      } else {
        return true
      }
    },
    closed() {
      this.resetTemp()
    },
    slotChange(name, slotTemp) {
      this.exeNameOptions = Object.assign({}, this.exeNameOptionsMap[name > 4 ? 1 : name])
      if (name !== this.slotName) {
        this.cancelWebPort()
      }
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.protocolOptions = this.protocolOptionsMap[this.slotName > 4 ? 1 : this.slotName]
      this.$nextTick(() => {
        // 处理首次打开弹窗，table中的记录不显示的问题
        this.temp.webPort && this.temp.webPort.splice(0, 0)
        // 修改 编辑、删除 按钮的状态
        this.webPortSelectionChange([])
      })
    },
    portTable() {
      return this.$refs['portList']
    },
    resetTempP() {
      this.tempP = Object.assign({}, this.defaultTempP)
      this.tempP.exeNames = []
      this.operation = ''
      this.updateTempExeName = ''
    },
    beforeUpload(file) {
      if (this.batchAddShow) {
        if (this.tempP.exeNames === null) {
          this.tempP.exeNames = []
        }
        let list = [...this.tempP.exeNames]
        list.push(file.name)
        list = this.filterRepetitionData(list);
        this.tempP.exeNames = list
      } else {
        this.tempP.exeName = file.name || ''
      }
      return false // 屏蔽了action的默认上传
    },
    portTypeChange(value) {
      if (value === '1') {
        this.clearValidations(this.$refs['portForm'], ['beginPort', 'endPort'])
        this.tempP.beginPort = 1
        this.tempP.endPort = 65535
      }
    },
    ipTypeChange(value) {
      if (value !== '3') {
        this.clearValidations(this.$refs['portForm'], ['domainAddr'])
      }
      if (value === '1') {
        this.clearValidations(this.$refs['portForm'], ['beginIp', 'endIp'])
        this.tempP.beginIp = '0.0.0.0'
        this.tempP.endIp = '***************'
      } else if (value === '4') {
        this.clearValidations(this.$refs['portForm'], ['beginIpv6', 'endIpv6'])
        this.tempP.beginIpv6 = '0:0:0:0:0:0:0:0'
        this.tempP.endIpv6 = 'ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff'
      } else if (value === '3') {
        this.clearValidations(this.$refs['portForm'], ['beginIp', 'endIp', 'beginIpv6', 'endIpv6'])
      }
    },
    // 封装 clearValidate 方法，用于批量清除验证
    clearValidations(formRef, fields) {
      formRef && formRef.clearValidate(fields)
    },
    webPortSelectionChange(rowDatas) {
      this.webPortDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.webPortEditable = true
      } else {
        this.webPortEditable = false
        this.cancelWebPort()
      }
    },
    formatterData(data) {
      if (data.lan !== 0 || data.process !== 0) {
        data.includeLan = data.lan + data.process
      }
    },
    formatterWebPort(data) {
      // 使用对象映射优化条件判断
      const clearRules = {
        '3': ['beginIp', 'endIp', 'beginIpv6', 'endIpv6'],
        '1': ['beginIpv6', 'endIpv6', 'domainAddr'],
        '2': ['beginIpv6', 'endIpv6', 'domainAddr'],
        '4': ['beginIp', 'endIp', 'domainAddr'],
        '5': ['beginIp', 'endIp', 'domainAddr']
      };

      // 获取当前ipType对应的清理规则
      const fieldsToClear = clearRules[data.ipType];

      // 批量清理字段
      if (fieldsToClear) {
        fieldsToClear.forEach(field => {
          data[field] = undefined;
        });
      }
    },
    addWebPort(rowData) {
      if (this.batchAddShow) {
        let exitsFlag = false //  批量添加的数据中是否存在已添加过的数据
        // todo 减少遍历次数，空间换时间
        // const dataMap = {}
        // const exitsDatas = this.temp.webPort
        // const newDatas = rowData.exeNames
        // console.log(JSON.parse(JSON.stringify(exitsDatas)), JSON.parse(JSON.stringify(rowData)))
        for (let i = 0; i < rowData.exeNames.length; i++) {
          let isAdd = true
          const data = JSON.parse(JSON.stringify(rowData))
          data.id = undefined
          data.exeNames = undefined
          data.exeName = rowData.exeNames[i]
          for (let j = 0; j < this.temp.webPort.length; j++) {
            const webPort = JSON.parse(JSON.stringify(this.temp.webPort[j]))
            webPort.id = undefined
            webPort.exeNames = undefined

            if (JSON.stringify(data) === JSON.stringify(webPort)) {
              isAdd = false
              exitsFlag = true
              break;
            }
          }
          if (isAdd) {
            const data = JSON.parse(JSON.stringify(rowData))
            data.id = new Date().getTime() + i
            data.exeName = rowData.exeNames[i]
            data.exeNames = undefined
            this.temp.webPort.unshift(data);
          }
        }
        if (exitsFlag) {
          this.$message({
            message: this.$t('pages.webPort_exitsDataAutoFilter'),
            type: 'warning',
            duration: 3000
          })
        }
      } else {
        //  单个程序名添加
        //  判断是否存在重复数据
        let isAdd = true
        this.temp.webPort.forEach(item => {
          const data = JSON.parse(JSON.stringify(item))
          const data1 = JSON.parse(JSON.stringify(rowData))
          data.id = undefined
          data.exeNames = undefined
          data1.id = undefined
          data1.exeNames = undefined
          if (JSON.stringify(data) === JSON.stringify(data1)) {
            isAdd = false
          }
        })

        //  存在时，将不添加
        if (isAdd) {
          rowData.id = new Date().getTime()
          rowData.exeNames = undefined
          this.temp.webPort.unshift(JSON.parse(JSON.stringify(rowData)));
        }
      }
    },
    createWebPort() {
      let validate
      this.$refs['portForm'].validate((valid) => {
        if (valid) {
          this.formatterWebPort(this.tempP)
          const rowData = Object.assign({}, this.tempP)
          this.addWebPort(rowData)
          this.resetTempP()
          this.cancelWebPort()
          validate = valid
        }
      })
      return validate
    },
    // beforeUpdateWebPort() {
    //   this.resetTempP()
    //   this.tempP = Object.assign(this.tempP, this.portTable().getSelectedDatas()[0])
    //   this.updateTempExeName = this.tempP.exeName
    //   this.tempP.exeNames = []
    //   //  隐藏tag
    //   this.operation = 'update'
    // },
    beforeUpdateWebPort() {
      this.resetTempP()
      const selected = this.portTable().getSelectedDatas()
      if (selected.length === 1) {
        this.tempP = Object.assign(this.tempP, selected[0])
        this.updateTempExeName = this.tempP.exeName
        this.tempP.exeNames = undefined
      }
      //  隐藏tag
      this.operation = 'update'
    },
    updateWebPort() {
      let validate
      this.$refs['portForm'].validate((valid) => {
        if (valid) {
          this.formatterWebPort(this.tempP)
          const rowData = Object.assign({}, this.tempP)
          //  校验是否存在相同数据
          if (!this.validUpdateDataExist(rowData)) {
            this.updateOneData(rowData, false)
          } else {
            this.$message({
              message: this.$t('pages.dataDuplication'),
              type: 'warning',
              duration: 3000
            })
            return;
          }
          this.cancelWebPort()
          validate = valid
        }
      })
      return validate
    },
    //  更新数据
    updateOneData(rowData) {
      for (let i = 0, size = this.temp.webPort.length; i < size; i++) {
        const data = this.temp.webPort[i];
        if (data.id === rowData.id) {
          this.temp.webPort.splice(i, 1, rowData)
          break
        }
      }
    },
    //  校验单条数据是否已存在
    validUpdateDataExist(rowData) {
      let isAdd = false
      for (let i = 0, len = this.temp.webPort.length; i < len; i++) {
        if (this.validDataEquals(this.temp.webPort[i], rowData)) {
          isAdd = true
          break;
        }
      }
      return isAdd;
    },
    //  校验两个数据是否相同
    validDataEquals(data, data1) {
      if (data.id == data1.id) {
        return false
      }
      data = JSON.parse(JSON.stringify(data))
      data1 = JSON.parse(JSON.stringify(data1))
      data.id = undefined
      data.exeNames = undefined
      data1.id = undefined
      data1.exeNames = undefined
      return JSON.stringify(data) === JSON.stringify(data1);
    },
    deleteWebPort() {
      const toDeleteIds = this.portTable().getSelectedIds()
      this.temp.webPort.splice(0, this.temp.webPort.length, ...this.portTable().deleteRowData(toDeleteIds))
      this.cancelWebPort()
    },
    cancelWebPort() {
      this.portTable() && this.portTable().setCurrentRow()
      this.clearValidations(this.$refs['portForm'])
      this.resetTempP()
    },
    handleCreate() {
      this.cancelWebPort()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.clearValidations(this.$refs.portForm)
      })
    },
    handleUpdate(row) {
      this.cancelWebPort()
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.clearValidations(this.$refs.portForm)
      })
    },
    handleShow(row, isGenerateStrategy) {
      this.cancelWebPort()
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
      this.$nextTick(() => {
        this.clearValidations(this.$refs.portForm)
      })
    },
    formatRowData(rowData) {
      // 根据域名配置的数据合并到 webPort
      rowData.webPort.push(...rowData.webDomain)
      rowData.webPort.forEach((data, index) => {
        data.id = index
        // 局域网是否生效
        data.lan = 1 & data.includeLan
        // 子进程是否生效
        data.process = 2 & data.includeLan
      })
    },
    formatFormData(formData) {
      formData.webDomain.splice(0)
      // 拆分数据，分别存入 webPort 、webDomain
      formData.webPort = formData.webPort.filter((data, index) => {
        delete data.id
        // 配置 所有程序 或 其他程序 的记录，将 对子进程生效 去掉
        if (data.exeName === '*.*' || data.exeName === 'other.*') {
          data.process = 0
        }
        // 局域网是否生效、子进程是否生效
        data.includeLan = data.lan + data.process
        // 根据域名配置的数据，添加到 webDomain
        if (data.domainAddr) {
          data.beginIp = undefined
          data.endIp = undefined
          data.beginIpv6 = undefined
          data.endIpv6 = undefined
          formData.webDomain.push(data)
        }
        // 根据 ip 配置的数据，返回 webPort
        return !data.domainAddr
      })
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    exeNameFormatter(row, data) {
      if (this.exeNameOptions[data]) {
        return this.exeNameOptions[data]
      }
      for (const osType in this.exeNameOptionsMap) {
        if (this.exeNameOptionsMap[osType][data]) {
          return this.exeNameOptionsMap[osType][data]
        }
      }
      return data
    },
    modeFormatter: function(row, data) {
      return this.modeOptions[data]
    },
    protocolFormatter: function(row, data) {
      return this.protocolOptions[data]
    },
    ipFormatter(row, data) {
      if (row.beginIp || row.endIp) {
        return row.beginIp + ' - ' + row.endIp
      } else if (row.domainAddr) {
        return row.domainAddr
      } else if (row.beginIpv6 || row.endIpv6) {
        return row.beginIpv6 + ' - ' + row.endIpv6
      }
    },
    portFormatter(row, data) {
      return row.beginPort + ' - ' + row.endPort
    },
    lanFormatter(row, data) {
      return data == 1 ? this.$t('pages.takeEffect') : this.$t('pages.inoperative')
    },
    processFormatter(row, data) {
      return data == 2 ? this.$t('pages.takeEffect') : this.$t('pages.inoperative')
    },
    ipValidator(rule, value, callback) {
      if (this.tempP.ipType == '1' || this.tempP.ipType == '2') {
        if (value && isIPv4(value)) {
          if (this.tempP.beginIp && this.tempP.endIp) {
            const temp1 = this.tempP.beginIp.split('.')
            const temp2 = this.tempP.endIp.split('.')
            let flag = false
            for (var i = 0; i < 4; i++) {
              if (temp1[i] - temp2[i] == 0) {
                continue
              } else if (temp1[i] - temp2[i] > 0) {
                flag = true
              }
              break
            }
            if (flag) {
              callback(new Error(this.$t('pages.serverLibrary_text3')))
            } else {
              callback()
            }
          } else {
            callback()
          }
        } else {
          callback(new Error(this.$t('pages.serverLibrary_text4')))
        }
      } else {
        callback()
      }
    },
    ipv6Validator(rule, value, callback) {
      if (isIPv6(value)) {
        if (this.tempP.beginIpv6 && this.tempP.endIpv6) {
          const fullbeginIpv6 = this.getFullIPv6(this.tempP.beginIpv6)
          const fullendIpv6 = this.getFullIPv6(this.tempP.endIpv6)
          if (fullbeginIpv6 > fullendIpv6) {
            callback(new Error(this.$t('pages.serverLibrary_text3')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback(new Error(this.$t('pages.ipv6_text1')))
      }
    },
    getFullIPv6(src) {
      const temp = src.replace('::', '*')
      const secs = temp.split(':')
      let ret = ''
      for (let i = 0; i < secs.length; i++) {
        const t = secs[i]
        if (t.indexOf('*') >= 0) {
          const ss = t.split('*')
          ret += ss[0].padStart(4, '0')
          for (let j = 1; j < (8 - secs.length); j++) {
            ret += '0000'
          }
          ret += ss[1].padStart(4, '0');
        } else {
          ret += t.padStart(4, '0');
        }
      }
      return ret.toLocaleLowerCase();
    },
    portValidator(rule, value, callback) {
      // if (rule.field === 'beginPort') {
      //   const port = this.tempP.endPort
      //   this.$refs['portForm'].fields.forEach((e) => {
      //     if (e.prop == 'endPort') {
      //       e.resetField()
      //     }
      //   })
      //   this.tempP.endPort = port
      // } else {
      //   const port = this.tempP.beginPort
      //   this.$refs['portForm'].fields.forEach((e) => {
      //     if (e.prop == 'beginPort') {
      //       e.resetField()
      //     }
      //   })
      //   this.tempP.beginPort = port
      // }
      if (isNaN(Number(value)) || Number(value) != parseInt(value)) {
        callback(new Error(this.$t('pages.serverLibrary_text5')))
      }
      if (value < 1 || value > 65535) {
        callback(new Error(this.$t('pages.serverLibrary_text6')))
      }
      const beginPort = Number(this.tempP.beginPort)
      const endPort = Number(this.tempP.endPort)
      if (beginPort && endPort) {
        if (beginPort > endPort) {
          callback(new Error(this.$t('pages.serverLibrary_text7')))
        } else {
          this.clearValidations(this.$refs['portForm'], ['beginPort', 'endPort'])
          callback()
        }
      } else {
        callback()
      }
    },
    beginPortBlur(event) {
      const { beginPort } = this.tempP
      if (!beginPort) {
        this.tempP.beginPort = 1
      } else if (beginPort > 65535) {
        this.tempP.beginPort = 65535
      }
    },
    endPortBlur(event) {
      const { endPort } = this.tempP
      if (!endPort || endPort > 65535) {
        this.tempP.endPort = 65535
      }
    },
    number(field) {
      const val = this.tempP[field]
      if (val.toString().length > 0 && val == 0) {
        this.tempP[field] = 1
      } else if (isNaN(val)) {
        this.tempP[field] = val.replace(/[^\d]/g, '')
      }
    },
    focus(event) {
      event.currentTarget.select()
    },
    inputBlur(validateProp) {
      this.$refs['portForm'].validateField(validateProp)
    },
    exeNamesValidator(rule, value, callback) {
      if (this.batchAddShow) {
        if (this.tempP.exeNames.length === 0) {
          callback(new Error(this.$t('pages.webPort_processNameNotNull')))
        } else {
          callback()
        }
      }
    },

    //  当列表发生改变时，校验名称是否符合规则
    tagChange(list) {
      list = this.filterRepetitionData(list);
      this.tempP.exeNames = list;
    },
    //  过滤重复数据
    filterRepetitionData(list) {
      const tList = []
      list.forEach(item => {
        if (!tList.includes(item)) {
          tList.push(item)
        }
      })
      return tList;
    },
    showAppSelectDlg() {
      this.$refs['appLib'].show()
    },
    showUpdateAppSelectDlg() {
      this.$refs['updateAppLib'].show()
    },
    appendFile(softs) {
      if (this.operation !== 'update') {
        softs = softs || []
        let list = [...this.tempP.exeNames]
        softs.forEach(item => {
          list.push(item.processName)
        })
        list = this.filterRepetitionData(list);
        this.tempP.exeNames = list;
      } else { //  若为更新操作，导入的是程序对象
        softs = softs || null
        if (softs !== null) {
          this.tempP.exeName = softs.processName || ''
        }
      }
    },
    handleClear() {
      this.tempP.exeNames.splice(0)
    }
  }
}
</script>
<style lang="scss" scoped>
  .inline-block {
    display: inline-block;
  }
</style>
