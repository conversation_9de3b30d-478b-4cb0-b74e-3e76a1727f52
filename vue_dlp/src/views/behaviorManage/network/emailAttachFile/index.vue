<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="740px"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ textMap[dialogStatus] }}
        <el-tooltip effect="dark" :content="$t('pages.emailKeyWord_test9', {content: $t('route.EmailAttachFile')} )" placement="bottom-start">
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 670px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <FormItem label-width="40px" prop="action">
          {{ $t('pages.emailAttachMessageExecute') }}
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
        <FormItem label-width="30px">
          <el-row>
            <el-col :span="16">
              <el-checkbox-group v-model="approvalTypeList" style="display: inline-block" :disabled="!formable" @change="handleApprovalTypeChange">
                <el-checkbox :label="1">{{ hasEncPermision ? $t('pages.burnMsg4') : $t('pages.burnMsg5') }}</el-checkbox>
                <el-checkbox :disabled="approvalTypeList.indexOf(1) == -1" :label="4">{{ hasEncPermision ? $t('pages.burnMsg6') : $t('pages.burnMsg7') }}</el-checkbox>
                <el-checkbox v-if="hasEncPermision" :label="2">{{ $t('pages.burnMsg8') }}</el-checkbox>
                <el-checkbox v-if="hasEncPermision" :disabled="approvalTypeList.indexOf(2) == -1" :label="8">{{ $t('pages.burnMsg9') }}</el-checkbox>
              </el-checkbox-group>
            </el-col>
          </el-row>
        </FormItem>
        <ResponseContent
          :status="dialogStatus"
          :select-style="{ 'margin-top': '5px', 'margin-left': '13px' }"
          :show-select="true"
          :rule-type-name="ruleTypeName"
          :editable="formable && ruleDisable"
          read-only
          :prop-check-rule="!!temp.isAlarm"
          :show-check-rule="true"
          :prop-rule-id="propRuleId"
          @ruleIsCheck="getRuleIsCheck"
          @getRuleId="getRuleId"
        />
        <ResponseContent
          v-if="hasEncPermision"
          :select-style="{ 'margin': '5px 0 5px 13px' }"
          :status="dialogStatus"
          :show-select="true"
          :rule-type-name="encRuleTypeName"
          :editable="formable && encRuleDisable"
          read-only
          :prop-check-rule="!!temp.isEncAlarm"
          :show-check-rule="true"
          :prop-rule-id="encPropRuleId"
          @ruleIsCheck="getEncRuleIsCheck"
          @getRuleId="getEncRuleId"
        />
        <el-divider content-position="left">{{ $t('pages.exceptRule') }}</el-divider>
        <div class="response-rule">
          <FormItem prop="fileSendLimit" label-width="30px">
            <el-checkbox v-model="temp.disable" :disabled="!formable || (!ruleDisable && !encRuleDisable)">
              <i18n path="pages.fileSmallerThanAllowSend">
                <el-input-number v-show="!temp.disable" slot="input" style="width:110px;height: 30px;" :disabled="true" size="mini">
                </el-input-number>
                <el-input
                  v-show="temp.disable"
                  slot="input"
                  v-model="temp.fileSendLimit"
                  style="width:110px;height: 30px;"
                  :disabled="!formable"
                  size="mini"
                  @input="handleInput"
                >
                </el-input>
                <el-select slot="unit" v-model="temp.sendLimitUnit" :disabled="!formable || !temp.disable" style="width: 80px;height: 30px;" @change="handleChange">
                  <el-option :key="0" label="MB" :value="0"/>
                  <el-option :key="1" label="KB" :value="1" />
                </el-select>

              </i18n>
            </el-checkbox>
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getDataPage, getDataByName, createData, updateData, deleteData
} from '@/api/behaviorManage/network/emailAttachFile'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'EmailAttachFile',
  components: { ResponseContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      stgCode: 221,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        // fileBackUpLimit: 20,
        entityType: '',
        entityId: undefined,
        // isBackup: 0,  // 是否备份 1-备份 0-不备份
        disable: false,  // 控制发送文件发送规则是否可用
        isAlarm: 0, // 明文文件触发响应规则
        isEncAlarm: 0, // 密文文件触发响应规则
        ruleId: undefined, // 明文文件响应规则id
        encRuleId: undefined, // 密文文件响应规则id
        isLimit: 0, // 1-限制外发 0-不限制外发
        approvalType: 0, // 位与 审批外发 1-明文文件禁止外发, 2-密文文件禁止外发, 4-允许明文文件外发, 8-允许密文文件外发
        fileSendLimit: 20,                      // 文件发送限制
        sendLimitUnit: 0                       // FileSendLimit 值的单位大小 0：MB （为了兼容旧版）1：KB
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.emailAttachFileStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.emailAttachFileStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileSendLimit: [
          { validator: this.fileSendLimitValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      propRuleId: undefined,  // 明文文件响应规则id
      encPropRuleId: undefined, // 密文文件响应规则id
      approvalTypeList: [], // 审批外发
      hasEncPermision: true,  // 是包含加解密模块
      // 触发明文文件响应规则
      ruleTypeName: this.$t('pages.burnMsg10'),
      // 触发密文文件响应规则
      encRuleTypeName: this.$t('pages.burnMsg11'),
      validRuleId: true,
      validEncRuleId: true,
      ruleDisable: false, // 当明文执行规则没有配置时,明文响应规则要置灰
      encRuleDisable: false // 当密文执行规则没有配置时,明文响应规则要置灰
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取注册模块
    this.listModule()
  },
  methods: {
    getDataByName,
    async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = resp.data
        this.ruleTypeName = this.hasEncPermision ? this.$t('pages.burnMsg10') : this.$t('pages.triggerResponseRule')
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getDataPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.propRuleId = undefined
      this.encPropRuleId = undefined
      this.approvalTypeList.splice(0)
      this.validRuleId = true
      this.validEncRuleId = true
      this.ruleDisable = false
      this.encRuleDisable = false
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.temp = Object.assign({}, JSON.parse(JSON.stringify(row))) // copy obj
      this.$set(this.temp, 'disable', false)
      if (this.temp.approvalType > 0) {
        this.approvalTypeList = this.numToList(this.temp.approvalType, 4)
        // 当勾选的有允许文件外发审批时，approvalType是没有保存禁止外发的值的，所以这边要做下处理
        if (this.approvalTypeList.indexOf(4) > -1) {
          this.approvalTypeList.push(1)
        }
        if (this.approvalTypeList.indexOf(8) > -1) {
          this.approvalTypeList.push(2)
        }
      }
      if (this.temp.fileSendLimit && this.temp.fileSendLimit > 0) {
        this.temp.disable = true
      }
      if (this.temp.sendLimitUnit == undefined) {
        this.$set(this.temp, 'sendLimitUnit', 0)
      }
      this.propRuleId = row.ruleId
      this.encPropRuleId = row.encRuleId
      this.dialogStatus = 'update'
      // 当明文执行规则没配置时,无法配置明文响应规则
      if (this.approvalTypeList.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      // 存储例外规则中的文件大小时值存的是kb
      if (this.temp.sendLimitUnit == 0 && this.temp.fileSendLimit) {
        this.temp.fileSendLimit = this.temp.fileSendLimit / 1024
      }
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId) {
          createData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId) {
          const tempData = Object.assign({}, this.temp)
          updateData(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    fileSendLimitValidator(rule, value, callback) {
      if (this.temp.disable) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const msgArr = []
      if (row.approvalType >= 0) {
        const approvalList = this.numToList(row.approvalType, 4)
        if (this.hasEncPermision) {
          if (approvalList.indexOf(1) > -1) {
            // 禁止明文文件传输
            msgArr.push(this.$t('pages.burnMsg14'))
          }
          if (approvalList.indexOf(2) > -1) {
            // 禁止密文文件传输
            msgArr.push(this.$t('pages.burnMsg15'))
          }
          if (approvalList.indexOf(4) > -1) {
            // 允许明文文件外发审批
            msgArr.push(this.$t('pages.burnMsg6'))
          }
          if (approvalList.indexOf(8) > -1) {
            // 允许密文文件外发审批
            msgArr.push(this.$t('pages.burnMsg9'))
          }
        }
      }
      if (this.hasEncPermision) {
        if (row.ruleId) {
          // 触发明文文件响应规则
          msgArr.push(this.$t('pages.burnMsg10'))
        }
        if (row.encRuleId) {
          // 触发密文文件响应规则
          msgArr.push(this.$t('pages.burnMsg11'))
        }
      } else {
        if (row.encRuleId) {
          // 触发响应规则
          msgArr.push(this.$t('pages.triggerResponseRule'))
        }
      }
      if (row.fileSendLimit > 0) {
        const text = this.$t('pages.fileSmallerThanAllowSend', {
          input: row.sendLimitUnit == 0 ? row.fileSendLimit / 1024 : row.fileSendLimit,
          unit: row.sendLimitUnit == 0 ? 'MB' : 'KB'
        })
        msgArr.push(text)
      }
      return msgArr.join('; ')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    formatFormData() {
      this.temp.ruleId = this.propRuleId
      this.temp.encRuleId = this.encPropRuleId
      this.temp.approvalType = this.getSum(this.approvalTypeList)
      // 当勾选了允许审批外发时，要去除掉禁止的值比如禁止审批外发为1允许审批外发为4两个都勾选时approvalType只保存4
      this.temp.approvalType = this.getSum(this.approvalTypeList)
      if (this.approvalTypeList.indexOf(4) > -1) {
        this.temp.approvalType = this.temp.approvalType - 1
      }
      if (this.approvalTypeList.indexOf(8) > -1) {
        this.temp.approvalType = this.temp.approvalType - 2
      }
      // 处理禁止传输的值,兼容旧版本
      if (this.hasEncPermision) {
        // 有加解密模块当禁止明文传输跟禁止密文传输都禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(2) > -1 && this.approvalTypeList.indexOf(4) == -1 && this.approvalTypeList.indexOf(8) == -1) {
          this.temp.isLimit = 1
        } else {
          this.temp.isLimit = 0
        }
      } else {
        // 无加解密模块，当禁止明文传输为禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(4) == -1) {
          this.temp.isLimit = 1
        } else {
          this.temp.isLimit = 0
        }
      }
      this.validRuleId = true
      this.validEncRuleId = true
      if (this.temp.isAlarm === 1 && !this.temp.ruleId) {
        this.validRuleId = false
      }
      if (this.temp.isEncAlarm === 1 && !this.temp.encRuleId) {
        this.validEncRuleId = false
      }
      if (!this.temp.disable) {
        this.temp.fileSendLimit = undefined
      }
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    getEncRuleId(value) {
      this.encPropRuleId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    getEncRuleIsCheck(value) {
      this.temp.isEncAlarm = value
    },
    handleApprovalTypeChange(value) {
      if (value.indexOf(4) > -1 && value.indexOf(1) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(4), 1)
      }
      if (value.indexOf(8) > -1 && value.indexOf(2) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(8), 1)
      }
      // 勾选了执行规则，响应规则才能勾选
      if (value.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (value.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      // 只有勾选了执行规则才能勾选例外规则
      if (value.indexOf(1) === -1 && value.indexOf(2) === -1) {
        this.temp.disable = false
      }
    },
    handleInput() {
      this.temp.fileSendLimit = this.temp.fileSendLimit.replace(/^0|[^0-9]/g, '')
      if (this.temp.sendLimitUnit == 1 && this.temp.fileSendLimit > 10485760) {
        this.temp.fileSendLimit = 10485760
      } else if (this.temp.sendLimitUnit == 0 && this.temp.fileSendLimit > 10240) {
        this.temp.fileSendLimit = 10240
      }
    },
    handleChange() {
      if (this.temp.sendLimitUnit == 1 && this.temp.fileSendLimit > 10485760) {
        this.temp.fileSendLimit = 10485760
      } else if (this.temp.sendLimitUnit == 0 && this.temp.fileSendLimit > 10240) {
        this.temp.fileSendLimit = 10240
      }
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.response-rule .el-form-item__error {
  padding-left: 93px;
}
</style>
