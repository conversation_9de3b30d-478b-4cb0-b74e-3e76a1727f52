<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="emailKeywordTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;">{{ strategyFormatter(props.detail) }}</span>
          </div>
        </template>
      </grid-table>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      class="stg-dlg"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
        <FormItem label-width="80px">
          <el-button size="small" :disabled="!formable" @click="importUrlHandle">
            {{ $t('text.infoImport', { info: $t('pages.urlLibrary') }) }}
          </el-button>
        </FormItem>
        <FormItem :label="$t('table.forumWebsite')" label-width="80px">
          <el-input v-model="temp.keyword" :disabled="!formable" type="textarea" rows="6" resize="none" maxlength=""/>
        </FormItem>
        <FormItem label-width="20px">
          <div style="color: blue">{{ $t('pages.emailKeyword_text2') }}</div>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.matchRule')">
          {{ $t('pages.keyword_text11') }}
        </FormItem>

        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <FormItem label-width="10px">
          <el-radio-group v-model="temp.filterType" :disabled="!formable">
            <el-radio :label="2">
              {{ this.$t('pages.forumWebsiteInDetection') }}
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ modeOptionTips[2] }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-radio>
            <el-radio :label="1">
              {{ this.$t('pages.forumWebsiteOutDetection') }}
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ modeOptionTips[1] }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </FormItem>

        <el-divider content-position="left" style="margin: 0 15px">{{ $t('pages.responseRule') }}</el-divider>
        <FormItem label-width="20px">
          <el-checkbox v-model="temp.isRecord" :disabled="!formable" :true-label="1" :false-label="0" @change="recordChange">{{ $t('pages.recordForumPostLog') }}</el-checkbox>
        </FormItem>
        <FormItem class="backup-limit" label-width="20px" prop="backupSize">
          <el-checkbox v-model="temp.isBackup" :disabled="!formable || !temp.isRecord" :true-label="1" :false-label="0" @change="backupChange">
            <i18n path="pages.forumPostingRecordLimitMsg">
              <file-size-input
                ref="fileSize"
                slot="size"
                :disabled="!formable || !temp.isRecord || !temp.isBackup"
                :max="10240 * 1024"
                :maxlength="10"
                :display-units="{ KB: 1, MB: 1024 }"
                :auto-validate="false"
                @change="handleFileSizeChange"
              />
            </i18n>
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content" style="width: 480px;">{{ $t('pages.forumPostingRecordLimitTips') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importForumURLFilterStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <url-select-dlg ref="urlSelectDlg" @select="urlSelectData"/>
  </div>
</template>
<script>
import {
  fetchList, getForumURLFilterByName, createForumURLFilter, updateForumURLFilter, deleteForumURLFilter
} from '@/api/behaviorManage/network/forumURLFilter'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import UrlSelectDlg from '@/views/system/baseData/urlLibrary/urlSelectDlg'
import FileSizeInput from '@/components/DownloadManager/setting/fileSizeInput'

export default {
  name: 'ForumUrlFilter',
  components: { ImportStg, UrlSelectDlg, FileSizeInput },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 206,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', ellipsis: false, type: 'popover', originData: true, width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { prop: 'createdTime', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: {},
      modeOptions: { 1: this.$t('pages.forumWebsiteOutDetection'), 2: this.$t('pages.forumWebsiteInDetection') },
      modeOptionTips: {
        1: this.$t('pages.listenURLTip1', { obj: this.$t('table.forumWebsite'), action: this.$t('pages.forumPosting') }),
        2: this.$t('pages.listenURLTip2', { obj: this.$t('table.forumWebsite'), action: this.$t('pages.forumPosting') })
      },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        isRecord: 1,
        isBackup: 1,
        filterType: 1,
        keyword: '',
        backupSize: 20,
        backupUnit: 'MB',
        timeId: 1,
        entityType: undefined,
        entityId: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.forumPostStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.forumPostStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        backupSize: [
          { validator: this.backupSizeValidator, trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['emailKeywordTable']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return fetchList(searchQuery).then(respond => {
        respond.data.items.forEach(item => {
          if (item.isRecord == null) {
            item.isRecord = 1
            item.backupSize = this.defaultTemp.backupSize
            item.backupUnit = this.defaultTemp.backupUnit
          }
          if (item.backupSize == null && item.forumPostingRecordLimit) {
            const size = item.forumPostingRecordLimit.postingBackupSize
            if (size >= 1024) {
              const mb = Math.floor(size / 1024)
              if (mb * 1024 === size) {
                item.backupSize = mb
                item.backupUnit = 'MB'
                return
              }
            }
            item.backupSize = size
            item.backupUnit = 'KB'
          }
        })
        return respond
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.$refs['fileSize'].setSizeUnit(this.temp.backupSize, this.temp.backupUnit)
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      const backupSize = !row.isRecord ? undefined : row.backupSize
      const isBackup = !backupSize ? 0 : 1
      Object.assign(this.temp, row, { isBackup, backupSize }) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.$refs['fileSize'].setSizeUnit(this.temp.backupSize, this.temp.backupUnit)
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    importUrlHandle() {
      this.$refs['urlSelectDlg'].show()
    },
    urlSelectData(data) {
      let original = [];
      if (this.temp.keyword.length > 0) {
        original = this.temp.keyword.split('\n');
      }
      data.forEach(item => {
        if (original.indexOf(item.address) < 0) {
          original.push(item.address)
        }
      })
      if (original.length > 0) {
        this.temp.keyword = original.join('\n')
      }
      this.$refs['dataForm'].validateField('keyword')
    },
    wipeBlank() {
      if (this.temp) {
        this.temp.keyword = (this.temp.keyword || '').trim()
      }
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.wipeBlank()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          this.formatFormData(tempData)
          createForumURLFilter(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.wipeBlank()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          this.formatFormData(tempData)
          updateForumURLFilter(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    formatFormData(formData) {
      if (!formData.isBackup) {
        formData.backupSize = 0
      }
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteForumURLFilter({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyNameValidator(rule, value, callback) {
      getForumURLFilterByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    backupSizeValidator(rule, value, callback) {
      if (this.temp && this.temp.isRecord && this.temp.isBackup && !value) {
        callback(new Error(this.$t('pages.required1')))
      } else {
        callback()
      }
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    strategyFormatter: function(row) {
      let msg = ''
      if (row.keyword) {
        msg += `${this.$t('pages.detectionRules')}：${this.$t('table.forumWebsite')}: ${row.keyword.replaceAll('\n', ', ')}；`
      }
      if (row.filterType === 1) {
        msg += `${this.$t('pages.executionRules')}：${this.$t('pages.forumWebsiteOutDetection')}；`
      } else if (row.filterType === 2) {
        msg += `${this.$t('pages.executionRules')}：${this.$t('pages.forumWebsiteInDetection')}；`
      }
      const responseText = []
      if (row.isRecord) {
        responseText.push(this.$t('pages.recordForumPostLog'))
      }
      if (row.backupSize) {
        responseText.push(this.$t('pages.forumPostingRecordLimitMsg', { size: row.backupSize + ' ' + row.backupUnit }))
      }
      msg += responseText.length > 0 ? `${this.$t('pages.responseRule')}：${responseText.join('、')}` : ``
      return msg
    },
    importSuccess() {
      this.handleFilter()
    },
    recordChange(val) {
      if (!val && this.temp.isBackup === 1) {
        this.backupChange(0)
      }
    },
    backupChange(val) {
      if (!val) {
        const dataForm = this.$refs['dataForm']
        if (dataForm) {
          dataForm.clearValidate('backupSize')
        }
      }
      this.temp.isBackup = val
    },
    handleFileSizeChange() {
      const { size: backupSize, unit: backupUnit } = this.$refs['fileSize'].getSizeUnit()
      Object.assign(this.temp, { backupSize, backupUnit })
    }
  }
}
</script>
<style lang="scss" scoped>
  .backup-limit>>>.el-form-item__content .el-form-item__error {
    margin-left: 186px;
  }
  .stg-dlg {
    >>>.first-divider {
      margin-top: 10px;
    }
  }
</style>
