<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button
          icon="el-icon-delete"
          size="mini"
          :disabled="!deleteable"
          @click="handleDelete"
        >
          {{ $t('button.delete') }}
        </el-button>
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">
            <i18n path="pages.lansegment_text1">
              <br slot="br"/>
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <grid-table
        ref="lansegmentTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="141px"
        style="width: 500px;"
      >
        <FormItem :label="$t('table.IPv4Addr')" prop="ip">
          <el-input v-model="temp.ip" :maxlength="64" @blur="inputBlur('ipv6')"/>
        </FormItem>
        <FormItem :label="$t('table.maskAddr')" prop="mask">
          <el-input v-model="temp.mask" :maxlength="64" @blur="inputBlur('ip')"/>
        </FormItem>
        <FormItem :label="$t('table.IPv6Addr')" prop="ipv6">
          <el-input v-model="temp.ipv6" :maxlength="64" @blur="inputBlur('ip')"/>
        </FormItem>
        <FormItem :label="$t('pages.prefixLengthIPv6Address')" prop="ipv6Pre">
          <el-input v-model="temp.ipv6Pre" :maxlength="11"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import {
  fetchList, createData, updateData, deleteData, listAll
} from '@/api/behaviorManage/network/lansegment'
import { isIPv4, isIPv6 } from '@/utils/validate'

export default {
  name: 'Lansegment',
  data() {
    return {
      colModel: [
        { prop: 'ip', label: 'IPv4Addr', width: '200', sort: 'custom' },
        { prop: 'mask', label: 'maskAddr', width: '200', sort: 'custom' },
        { prop: 'ipv6', label: 'IPv6Addr', width: '200', sort: 'custom' },
        { prop: 'ipv6Pre', label: 'IPv6AddressPrefix', width: '200', sort: 'custom' },
        // { prop: 'active', label: 'enable', fixedWidth: '80', type: 'icon', iconClass: 'active' },
        {
          prop: 'createdTime',
          label: 'operate',
          type: 'button',
          fixed: 'right',
          fixedWidth: '100',
          buttons: [{
            label: 'edit', click: this.handleUpdate
          }
          ]
        }
      ],
      deleteable: false,
      temp: { // 表单字段
        id: undefined,
        ip: '',
        mask: '',
        ipv6: '',
        ipv6Pre: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.networkSegment'), 'update'),
        create: this.i18nConcatText(this.$t('pages.networkSegment'), 'create')
      },
      rules: {
        ip: [
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        mask: [
          { validator: this.maskValidator, trigger: 'blur' }
        ],
        ipv6: [
          { validator: this.ipv6Validator, trigger: 'blur' }
        ],
        ipv6Pre: [
          { validator: this.ipv6PreValidator, trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['lansegmentTable']
    }
  },
  methods: {
    rowDataApi: function(option) {
      return fetchList(option)
    },
    handleFilter() {
      this.gridTable.execRowDataApi()
    },
    handleDrag() {
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        ip: '',
        mask: '',
        ipv6: '',
        ipv6Pre: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
    },
    handleExport() {
    },
    handleSelectionChange(val) {
      this.deleteable = val.length > 0
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const ip = this.temp.ip
          const mask = this.temp.mask
          const ipv6 = this.temp.ipv6
          const ipv6Pre = this.temp.ipv6Pre
          listAll().then((resp) => {
            const datas = resp.data
            let filter = []
            if (ip && ipv6) {
              filter = datas.filter(lanSegment => {
                return lanSegment.ip == ip && lanSegment.mask == mask && lanSegment.ipv6 == ipv6 && lanSegment.ipv6Pre == ipv6Pre
              })
            } else if (ip) {
              filter = datas.filter(lanSegment => {
                return lanSegment.ip == ip && lanSegment.mask == mask && lanSegment.ipv6 == '' && lanSegment.ipv6Pre == undefined
              })
            } else if (ipv6) {
              filter = datas.filter(lanSegment => {
                return lanSegment.ip == '' && lanSegment.mask == '' && lanSegment.ipv6 == ipv6 && lanSegment.ipv6Pre == ipv6Pre
              })
            }
            if (filter.length > 0) {
              this.$message({
                message: this.$t('pages.alreadyExistsInfo', { info: this.$t('pages.networkSegmentTip') }),
                type: 'error',
                duration: 2000
              })
              this.submitting = false
            } else {
              createData(this.temp).then(() => {
                this.submitting = false
                this.gridTable.execRowDataApi()
                this.dialogFormVisible = false
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.createSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(reason => {
                this.submitting = false
              })
            }
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const ip = this.temp.ip
          const mask = this.temp.mask
          const ipv6 = this.temp.ipv6
          const ipv6Pre = this.temp.ipv6Pre
          listAll().then((resp) => {
            const datas = resp.data
            let filter = []
            if (ip && ipv6) {
              filter = datas.filter(lanSegment => {
                return lanSegment.ip == ip && lanSegment.mask == mask && lanSegment.ipv6 == ipv6 && lanSegment.ipv6Pre == ipv6Pre
              })
            } else if (ip) {
              filter = datas.filter(lanSegment => {
                return lanSegment.ip == ip && lanSegment.mask == mask && lanSegment.ipv6 == '' && lanSegment.ipv6Pre == undefined
              })
            } else if (ipv6) {
              filter = datas.filter(lanSegment => {
                return lanSegment.ip == '' && lanSegment.mask == '' && lanSegment.ipv6 == ipv6 && lanSegment.ipv6Pre == ipv6Pre
              })
            }
            if (filter.length > 0) {
              this.$message({
                message: this.$t('pages.alreadyExistsInfo', { info: this.$t('pages.networkSegmentTip') }),
                type: 'error',
                duration: 2000
              })
              this.submitting = false
            } else {
              updateData(this.temp).then(() => {
                this.submitting = false
                this.gridTable.execRowDataApi()
                this.dialogFormVisible = false
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.updateSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(reason => {
                this.submitting = false
              })
            }
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    inputBlur(validateProp) {
      this.$refs['dataForm'].validateField(validateProp)
    },
    ipValidator(rule, value, callback) {
      if (!this.temp.ip && !this.temp.ipv6) {
        callback(new Error(this.$t('pages.ipValidate', {})))
      } else if (this.temp.mask && !value) {
        callback(new Error(this.$t('pages.whenMaskAddressIsNotEmptyIPv4AddressNeedsToBeConfigured')))
      } else if (value && !isIPv4(value)) {
        callback(new Error(this.$t('pages.incorrectIPv4AddressFormat')))
      } else {
        callback()
      }
    },
    maskValidator(rule, value, callback) {
      const re = /^(?:\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])(?:\.(?:\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])){3}$/
      if (this.temp.ip && !value) {
        callback(new Error(this.$t('pages.maskAddressRequiredWhenIPv4AddressIsConfigured')))
      } else if (value && !re.test(value)) {
        callback(new Error(this.$t('pages.incorrectMaskAddressFormat')))
      } else {
        callback()
      }
    },
    ipv6Validator(rule, value, callback) {
      if (!this.temp.ip && !this.temp.ipv6) {
        callback(new Error(this.$t('pages.ipValidate', {})))
      } else if (this.temp.ipv6Pre && !value) {
        callback(new Error(this.$t('pages.whenIPv6AddressPrefixLengthIsNotEmptyIPv6AddressNeedsToBeConfigured')))
      } else if (value && !isIPv6(value)) {
        callback(new Error(this.$t('pages.incorrectIPv6AddressFormat')))
      } else {
        callback()
      }
    },
    ipv6PreValidator(rule, value, callback) {
      const re = /^[1-9]\d*$/
      if (this.temp.ipv6 && !value) {
        callback(new Error(this.$t('pages.IPV6AddressPrefixLengthRequiredWhenIPv6AddressIsConfigured')))
      } else if (value && (!re.test(value) || value < 1 || value > 128)) {
        callback(new Error(this.$t('pages.IPv6AddressPrefixLengthRange')))
      } else {
        callback()
      }
    }
  }
}
</script>
