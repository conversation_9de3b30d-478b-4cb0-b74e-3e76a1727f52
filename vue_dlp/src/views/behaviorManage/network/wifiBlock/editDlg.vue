<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      type="drawer"
      :title="$t('pages.wifiBlockStg')"
      :stg-code="201"
      :active-able="activeAble"
      :time-able="true"
      :rules="rules"
      :model="temp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('pages.wifiConfig')" name="first">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <div v-if="formable">
                <el-button size="small" @click="handleCreateWifi()">
                  {{ $t('button.insert') }}
                </el-button>
                <el-button size="small" @click="importWifiFromTerm()">
                  {{ $t('pages.importFromTerm') }}
                </el-button>
                <el-button size="small" @click="importWifiFromLib()">
                  {{ $t('pages.importWifi') }}
                </el-button>
                <!--<el-button size="mini" :disabled="!checkWifiDeleteable" @click="handleBatchEdit">
                  {{ $t('pages.batchModification') }}
                </el-button>-->
                <el-button size="small" :disabled="!copyAble" @click="handleCopy()">
                  {{ $t('pages.wifiBlock_text9') }}
                </el-button>
                <el-button size="small" :disabled="!checkWifiDeleteable" @click="handleDeleteCheckedWifi()">
                  {{ $t('button.delete') }}
                </el-button>
                <el-button size="small" @click="handleClearCheckedWifi()">
                  {{ $t('button.clear') }}
                </el-button>
              </div>
              <grid-table
                ref="checkedWifiGrid"
                :show-pager="false"
                auto-height
                :selectable="selectable"
                :col-model="checkedColModel"
                :row-datas="temp.wifiList"
                @selectionChangeEnd="checkWifiSelectionChangeEnd"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label="" label-width="25px">
              <el-row >
                <el-col :span="24">
                  <el-radio-group v-model="temp.blockType" :disabled="!formable" style="width: 100%;">
                    <!--<el-radio :label="0">终端连接任何一个WIFI时：</el-radio>-->
                    <el-radio :label="2" class="ellipsis" style="width: 98%;"><span :title="$t('pages.blockTypeOptions3')">{{ $t('pages.blockTypeOptions3_1') }}</span></el-radio>
                    <el-radio :label="1" class="ellipsis" style="width: 98%; margin-top:4px;"><span :title="$t('pages.blockTypeOptions2')">{{ $t('pages.blockTypeOptions2_1') }}</span></el-radio>
                  </el-radio-group>
                </el-col>
                <el-col :span="24">
                  <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.wifiBlock_text1') }}</label>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      <i18n path="pages.wifiBlock_text6">
                        <br slot="br"/>
                      </i18n>
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-col>
              </el-row>
            </FormItem>
            <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
            <FormItem label="" label-width="25px" prop="action">
              <div class="selectionCheckBox">
                <el-checkbox v-model="temp.action" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable" >{{ $t('pages.wifiBlock1') }}</el-checkbox>
                <!-- <el-checkbox v-model="temp.record" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable">{{ $t('pages.wifiBlock2') }}</el-checkbox> -->
              </div>
              <ResponseContent
                :show-select="true"
                style="line-height: 15px !important;"
                :editable="formable"
                :select-style="{ 'margin-top': 0 }"
                read-only
                :prop-check-rule="checkRule"
                :show-check-rule="true"
                :prop-rule-id="temp.ruleId"
                @getRuleId="getRuleId"
                @ruleIsCheck="ruleIsCheck"
                @validate="(val) => { responseValidate = val }"
              />
            </FormItem>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.wifiRecord')" name="second">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <div v-if="formable">
                <el-button size="small" @click="handleCreateWifi()">
                  {{ $t('button.insert') }}
                </el-button>
                <el-button size="small" @click="importWifiFromTerm()">
                  {{ $t('pages.importFromTerm') }}
                </el-button>
                <el-button size="small" @click="importWifiFromLib()">
                  {{ $t('pages.importWifi') }}
                </el-button>
                <!--<el-button size="mini" :disabled="!checkRecordWifiDeleteable" @click="handleBatchEdit">
                  {{ $t('pages.batchModification') }}
                </el-button>-->
                <el-button size="small" :disabled="!copyRecordAble" @click="handleCopy()">
                  {{ $t('pages.wifiBlock_text10') }}
                </el-button>
                <el-button size="small" :disabled="!checkRecordWifiDeleteable" @click="handleDeleteCheckedWifi()">
                  {{ $t('button.delete') }}
                </el-button>
                <el-button size="small" @click="handleClearCheckedWifi()">
                  {{ $t('button.clear') }}
                </el-button>
              </div>
              <grid-table
                ref="checkedRecordWifiGrid"
                :show-pager="false"
                auto-height
                :selectable="selectable"
                :col-model="checkedColModel"
                :row-datas="temp.recordWifiList"
                @selectionChangeEnd="checkRecordWifiSelectionChangeEnd"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label="" label-width="25px">
              <el-row >
                <el-col :span="24">
                  <el-radio-group v-model="temp.recordBlockType" :disabled="!formable" style="width: 100%;">
                    <el-radio :label="1" class="ellipsis" style="width: 98%;"><span :title="$t('pages.blockTypeOptions3')">{{ $t('pages.blockTypeOptions3_1') }}</span></el-radio>
                    <el-radio :label="0" class="ellipsis" style="width: 98%; margin-top:4px;"><span :title="$t('pages.blockTypeOptions2')">{{ $t('pages.blockTypeOptions2_1') }}</span></el-radio>
                  </el-radio-group>
                </el-col>
                <el-col :span="24">
                  <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.wifiBlock_text1') }}</label>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      <i18n path="pages.wifiBlock_text11">
                        <br slot="br"/>
                      </i18n>
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-col>
              </el-row>
            </FormItem>
            <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
            <FormItem label="" label-width="25px" prop="action">
              <div class="selectionCheckBox">
                <el-checkbox v-model="temp.record" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable">{{ $t('pages.wifiBlock2') }}</el-checkbox>
              </div>
            </FormItem>
          </el-tab-pane>
        </el-tabs>
      </template>
    </stg-dialog>

    <!--批量修改限制规则类型-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.updateLimitRuleType')"
      :visible.sync="ruleBatchEditVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm3" label-position="right" label-width="80px" style="width: 700px;">
        <FormItem :label="$t('table.limitType')" >
          <el-select v-model="limitRuleType" style="width: 270px;" @change="(val) => { matchType = 0 }">
            <el-option :value="0" :label="$t('pages.limitRuleTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.limitRuleTypeOptions2')"/>
          </el-select>
        </FormItem>
        <FormItem v-if="limitRuleType==1" :label="$t('table.matchType')" >
          <el-select v-model="matchType" style="width: 270px;">
            <el-option :value="0" :label="$t('pages.matchTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.matchTypeOptions2')"/>
          </el-select>
        </FormItem>
        <grid-table ref="ruleList" :height="350" :col-model="checkedColMode2" :multi-select="false" :row-datas="rowDatas" :show-pager="false"/>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="batchEditData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="ruleBatchEditVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!--从终端实时收集导入-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.addWifi')"
      :visible.sync="wifiAddVisible"
      width="850px"
      @dragDialog="handleDrag"
    >
      <div class="tree-container" style="height: 370px;">
        <strategy-target-tree ref="wifiTargetTree" :showed-tree="['terminal']" @data-change="wifiTargetNodeChange" />
      </div>
      <div class="table-container-dialog">
        <grid-table
          ref="wifiSelectList"
          row-key="macAddress"
          :height="370"
          :show-pager="false"
          :col-model="wifiColModel"
          :multi-select="true"
          :row-datas="termWifis"
          :loading="loading"
          @selectionChangeEnd="termWifiSelectionChangeEnd"
        />
      </div>
      <!-- <p v-show="warnMsgVisible1" style="font-size:12px;color:red;margin:5px 0 0 18px">{{ $t('pages.wifiBlock_text4') }}</p> -->
      <Form ref="dataForm3" label-width="100px" style="margin-top: 10px; float: left;">
        <FormItem :label="$t('table.limitType')" >
          <el-select v-model="limitRuleType" style="width: 270px;" @change="(val) => { matchType = 0 }">
            <el-option :value="0" :label="$t('pages.limitRuleTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.limitRuleTypeOptions2')"/>
          </el-select>
        </FormItem>
        <FormItem v-if="limitRuleType==1" :label="$t('table.matchType')" >
          <el-select v-model="matchType" style="width: 270px;">
            <el-option :value="0" :label="$t('pages.matchTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.matchTypeOptions2')"/>
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importByTerm()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="wifiAddVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!--从WiFi信息库导入-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.addWifi')"
      :visible.sync="wifiLibVisible"
      width="850px"
      @dragDialog="handleDrag"
    >
      <el-container>
        <el-aside width="200px">
          <tree-menu
            ref="groupTree"
            v-model="query.groupId"
            multiple
            :height="370"
            node-key="dataId"
            :data="wifiGroupTreeData"
            :default-expand-all="true"
            :width="200"
            @node-click="treeNodeClick"
            @check="wifiGroupCkeckFunc"
          />
        </el-aside>
        <el-main>
          <div class="toolbar">
            <div class="searchCon">
              <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.wifiLibrary_text1')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </div>
          <grid-table
            ref="wifiLibList"
            :height="340"
            :col-model="wifiLibColModel"
            :multi-select="true"
            pager-small
            :row-data-api="rowDataApi"
            is-saved-selected
            :selected-data-label-formatter="(row, prop) => `${row.name} (${row.macAddress})`"
            @selectionChangeEnd="wifiLibSelectionChangeEnd"
          />
        </el-main>
      </el-container>
      <!-- <p v-show="warnMsgVisible" style="font-size:12px;color:red;margin:5px 0 0 18px">{{ $t('pages.wifiBlock_text4') }}</p> -->
      <Form ref="dataForm3" label-width="100px" style="margin-top: 10px; float: left;">
        <FormItem :label="$t('table.limitType')" >
          <el-select v-model="limitRuleType" style="width: 270px;" @change="(val) => { matchType = 0 }">
            <el-option :value="0" :label="$t('pages.limitRuleTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.limitRuleTypeOptions2')"/>
          </el-select>
        </FormItem>
        <FormItem v-if="limitRuleType==1" :label="$t('table.matchType')" >
          <el-select v-model="matchType" style="width: 270px;">
            <el-option :value="0" :label="$t('pages.matchTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.matchTypeOptions2')"/>
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'A5H'" :link-url="'/system/baseData/wifiLibrary'" :btn-text="$t('pages.maintainWifi')"/>
        <el-button :loading="addWifiLoading" type="primary" @click="importByLib()">{{ $t('pages.addWifi') }}</el-button>
        <el-button @click="wifiLibVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!--导入弹窗，限制规则类型配置-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.limitRuleTypeConfig')"
      :visible.sync="limitRuleTypeVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm2" label-width="80px" style="width: 100%; margin-top: 10px;">
        <FormItem :label="$t('table.limitType')" >
          <el-select v-model="limitRuleType" @change="(val) => { matchType = 0 }">
            <el-option :value="0" :label="$t('pages.limitRuleTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.limitRuleTypeOptions2')"/>
          </el-select>
        </FormItem>
        <FormItem v-if="limitRuleType==1" :label="$t('table.matchType')" >
          <el-select v-model="matchType">
            <el-option :value="0" :label="$t('pages.matchTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.matchTypeOptions2')"/>
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="addWifiLoading" type="primary" @click="importData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="limitRuleTypeVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!--单个添加/修改wifi限制规则-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[ruleDialogStatus]"
      :visible.sync="wifiBlockRuleVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm2" :hide-required-asterisk="true" :model="tempW" :rules="wifiRules" label-width="80px" style="width: 100%; margin-top: 10px;">
        <FormItem :label="$t('table.wifiName')" prop="name">
          <el-input v-model="tempW.name" no-limit maxlength=""></el-input>
        </FormItem>
        <FormItem :label="$t('table.macAddr')" prop="macAddress">
          <el-input v-model="tempW.macAddress" maxlength=""></el-input>
        </FormItem>
        <FormItem :label="$t('table.limitType')" prop="type">
          <el-select v-model="tempW.limitRuleType" @change="(val) => { tempW.matchType = 0 }">
            <el-option :value="0" :label="$t('pages.limitRuleTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.limitRuleTypeOptions2')"/>
          </el-select>
        </FormItem>
        <FormItem v-if="tempW.limitRuleType == 1" :label="$t('table.matchType')" prop="type">
          <el-select v-model="tempW.matchType">
            <el-option :value="0" :label="$t('pages.matchTypeOptions1')"/>
            <el-option :value="1" :label="$t('pages.matchTypeOptions2')"/>
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="wifiBlockRuleVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!--  已存在规则提示窗口  -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.prompt')"
      :visible.sync="dialogFormWarnVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <el-card :body-style="{'max-height': '320px', 'overflow-y': 'auto'}" class="box-card" style="max-height: 300px;">
        <grid-table
          ref="transTaskList"
          :col-model="checkedColMode2"
          :row-datas="updateRuleList"
          :height="250"
          :multi-select="false"
          :show-pager="false"
        />
      </el-card>
      <div class="clearfix" style="color: #68a8d0; padding-left: 0px;">
        <span>{{ $t('pages.wifiBlock_text7') }}</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateRules()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormWarnVisible=false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createStrategy, getStrategyByName, updateStrategy } from '@/api/behaviorManage/network/wifiBlock'
import { getGroupTreeNode, getWifiPage, listWifiByGroupIds } from '@/api/system/baseData/wifiLib'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'

export default {
  name: 'WifiBlockDlg',
  components: { ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      // 进程系统类型：1-windows，2-linux，4-mac
      osType: 1,
      supportMd5: true,
      submitting: false,
      slotName: undefined,
      activeName: 'first',
      temp: {},
      defaultTemp: { // 表单字段
        id: '',
        name: '',
        blockType: 2,
        recordBlockType: 1,
        active: false,
        action: 0,
        remark: '',
        entityType: undefined,
        entityId: undefined,
        ruleId: null,
        record: 0,
        wifiList: [],
        recordWifiList: []
      },
      tempW: {},
      defaultTempW: { // WIFI信息表单字段
        id: '',
        name: '',
        macAddress: '',
        limitRuleType: 0,
        matchType: 0
      },
      checkedColModel: [
        { prop: 'name', label: 'wifiName', width: '150', sort: true },
        { prop: 'macAddress', label: 'macAddr', width: '150', sort: true },
        { prop: 'limitRuleType', label: 'limitType', width: '150', sort: true, sortOriginal: true, formatter: this.limitRuleTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleUpdateWifi }
          ]
        }
      ],
      checkedColMode2: [
        { prop: 'name', label: 'wifiName', width: '150', sort: true },
        { prop: 'macAddress', label: 'macAddr', width: '150', sort: true },
        { prop: 'limitRuleType', label: 'limitType', width: '150', sort: true, sortOriginal: true, formatter: this.limitRuleTypeFormatter }
      ],
      wifiColModel: [
        { prop: 'computer', label: 'source', width: '150', sort: true },
        { prop: 'name', label: 'wifiName', width: '150', sort: true },
        { prop: 'macAddress', label: 'macAddr', width: '150', sort: true }
      ],
      wifiLibColModel: [
        { prop: 'name', label: 'wifiName', width: '150', sort: 'custom', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'macAddress', label: 'macAddr', width: '150', sort: 'custom' }
      ],
      // rowDatas: [],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        action: [{ required: true, validator: this.actionValidator, trigger: 'blur' }]
      },
      wifiRules: {
        name: [{ required: true, message: this.$t('pages.wifiBlock_text2'), trigger: 'blur' }],
        macAddress: [
          { required: true, message: this.$t('pages.wifiBlock_text3'), trigger: 'blur' },
          { validator: this.macValidator, trigger: 'blur' }
        ]
      },
      propRuleId: undefined,
      checkWifiDeleteable: false,
      copyAble: false,
      copyRecordAble: false,
      checkRecordWifiDeleteable: false,
      dialogStatus: '',
      timer: null,
      isAddExceptApp: false,
      wifiAddVisible: false,
      wifiLibVisible: false,
      limitRuleTypeVisible: false,
      wifiBlockRuleVisible: false,
      ruleBatchEditVisible: false,
      dialogFormWarnVisible: false,
      warnMsgVisible: false,
      warnMsgVisible1: false,
      checkedWifiGroup: [],
      addWifiLoading: false,
      termId: undefined,
      termName: '',
      termWifis: [],  // 终端获取的WIFI
      inputWifis: [],  // 用户输入的WIFI
      loading: true,
      limitRuleTypeOptions: {
        0: this.$t('pages.limitRuleTypeOptions1'),
        1: this.$t('pages.limitRuleTypeOptions2')
      },
      matchTypeOptions: {
        0: this.$t('pages.matchTypeOptions1'),
        1: this.$t('pages.matchTypeOptions2')
      },
      ruleDialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.detectionRules'), 'update'),
        create: this.i18nConcatText(this.$t('pages.detectionRules'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.detectionRules'), 'delete')
      },
      toDeleteRuleList: [], // 检测规则列表与选中wifi配置规则相同的id，覆盖时，通过该数组删除检测规则列表中对应的数据
      updateRuleList: [], // 与检测规则列表相同的规则
      addRuleList: [], // 所有需要添加的规则
      rowDatas: [], // 选中的wifi限制规则
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      wifiGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.wifiLibrary'), parentId: '', children: [] }],
      limitRuleType: 0,
      matchType: 0,
      checkRule: false
    }
  },
  computed: {
    allWifis() {
      return [...this.termWifis, ...this.inputWifis]
    }
  },
  watch: {
    wifiAddVisible() {
      this.loading = false
    }
  },
  created() {
    this.resetTemp()
    this.resetTempW()
    this.loadWifiGroupTree()
  },
  activated() {
    this.loadWifiGroupTree()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    selectable(row, index) {
      if (this.formable == false) {
        return false
      } else {
        return true
      }
    },
    wifiLibSelectionChangeEnd(rowDatas) {
      this.warnMsgVisible = !rowDatas || rowDatas.length == 0
    },
    async wifiGroupCkeckFunc(data) {
      const wifiList = this.getCheckedGroupWifi()
      this.warnMsgVisible = !wifiList || wifiList.length == 0
    },
    async getCheckedGroupWifi() {
      // 针对wifi导入界面还没有的情况
      if (!this.$refs.groupTree) {
        return []
      }
      let ids = this.$refs.groupTree.getCheckedKeys()
      if (ids && ids.length != 0) {
        if (ids[ids.length - 1] == 0) {
          ids = ids.splice(ids.length - 2, 1)
        }
        const resp = await listWifiByGroupIds({
          ids: ids.join(',')
        })
        return resp.data
      }
      return []
    },
    addType() {
      this.limitRuleType = 0
      this.matchType = 0
      this.limitRuleTypeVisible = true
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.temp.ruleId = null
      this.checkRule = false
      this.propRuleId = undefined
    },
    resetTempW() {
      this.tempW = Object.assign({}, this.defaultTempW)
    },
    closed() {
      this.resetTemp()
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      if (this.temp.ruleId) {
        this.propRuleId = this.temp.ruleId
      }
    },
    checkedWifiGrid() {
      return this.$refs['checkedWifiGrid']
    },
    checkedRecordWifiGrid() {
      return this.$refs['checkedRecordWifiGrid']
    },
    wifiLibList() {
      return this.$refs['wifiLibList']
    },
    showByOsType() {
      const isShow = [2, 4].indexOf(this.osType) < 0
      if (!isShow) {
        // 不显示时，一些参数应该赋于默认值
        this.temp.blockType = 2
        this.temp.recordBlockType = 1
        this.temp.action = 1
      }
      return isShow
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    wifiTargetNodeChange: function(tabName, data) {
      this.loading = false
      if (data) {
        if (data.type == 1) {
          this.termId = data.dataId
          this.termName = data.label
          if (!data.online) {
            this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.wifiBlock_text8'), type: 'warning', duration: 2000 })
          } else {
            this.loading = true
            this.$socket.sendToUser(this.termId, '/listWifi', this.termId, (respond, handle) => {
              this.loading = false
              handle.close()
              this.termWifis.splice(0)
              // 判断终端id是否等于当前请求的终端id，防止用户连续点击不同终端导致数据加载出错
              if (respond.data) {
                respond.data.forEach(item => {
                  const p = {
                    computer: this.termName,
                    name: item.name,
                    macAddress: item.macAddress
                  }
                  this.termWifis.push(p)
                })
                var macAddressList = []
                this.activeName == 'first' ? macAddressList = this.initWifi(this.temp.wifiList) : macAddressList = this.initWifi(this.temp.recordWifiList)
                this.$nextTick(() => {
                  if (macAddressList.length > 0) {
                    this.$refs.wifiSelectList.checkSelectedRows(macAddressList)
                  }
                })
              }
            }, (handle) => {
              this.loading = false
              handle.close()
              this.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
            })
          }
        }
      }
    },
    termWifiSelectionChangeEnd(rowDatas) {
      this.warnMsgVisible1 = !rowDatas || rowDatas.length == 0
    },
    loadWifiGroupTree: function() {
      getGroupTreeNode().then(respond => {
        this.wifiGroupTreeData[0].children = respond.data
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getWifiPage(searchQuery)
    },
    handleFilter() {
      this.wifiLibList().execRowDataApi(this.query)
    },
    treeNodeClick: function(data, node, element) {
      this.query.groupId = Number(data.dataId)
      this.wifiLibList().execRowDataApi(this.query)
    },
    batchEditData() {
      this.addRuleList = []
      this.updateRuleList = []
      this.toDeleteRuleList = []
      this.addRuleList = this.rowDatas

      this.addRuleList.forEach(item => {
        item['limitRuleType'] = this.limitRuleType
        item['matchType'] = this.matchType
        this.temp.wifiList.forEach(wifi => {
          if (item.id !== wifi.id && item.limitRuleType == wifi.limitRuleType) {
            if (item.limitRuleType == 0 &&
              item.macAddress == wifi.macAddress) {
              this.toDeleteRuleList.push(wifi.id)
              this.updateRuleList.push(item)
            } else if (item.limitRuleType == 1 &&
              item.matchType == wifi.matchType &&
              item.name == wifi.name) {
              this.toDeleteRuleList.push(wifi.id)
              this.updateRuleList.push(item)
            }
          }
        })
      })

      if (this.updateRuleList.length > 0) {
        this.dialogFormWarnVisible = true
      } else {
        this.temp.wifiList.unshift(...this.addRuleList)
        this.ruleBatchEditVisible = false
      }
    },
    async importData() {
      this.addRuleList = []
      this.updateRuleList = []
      this.toDeleteRuleList = []

      this.addWifiLoading = true
      const checkedWifiGroup = await this.getCheckedGroupWifi()
      this.addWifiLoading = false

      console.log(checkedWifiGroup)

      if (this.wifiLibVisible) {
        this.addRuleList = JSON.parse(JSON.stringify(this.$refs.wifiLibList.getSelectedDatas())) // copy obj
      } else {
        this.addRuleList = JSON.parse(JSON.stringify(this.$refs.wifiSelectList.getSelectedDatas())) // copy obj
      }
      // 根据wifi名称去重
      if (this.limitRuleType == 1) {
        const map = {}
        for (const i of this.addRuleList) {
          map[i.name] = i;
        }
        const dif = this.addRuleList.length - Object.values(map).length
        this.addRuleList = Object.values(map)
        if (dif != 0) {
          this.$message({
            message: this.$t('pages.sameNameDistinct'),
            type: 'warning'
          })
        }
      }
      if (checkedWifiGroup.length != 0) {
        const groupList = JSON.parse(JSON.stringify(checkedWifiGroup))
        this.addRuleList = [...this.addRuleList]
        for (const i of groupList) {
          if (this.addRuleList.some(v => v.name == i.name && v.macAddress == i.macAddress)) {
            continue
          }
          this.addRuleList.push(i)
        }
      }

      const baseId = new Date().getTime()
      let i = 0
      this.addRuleList.forEach(item => {
        item['limitRuleType'] = this.limitRuleType
        item['matchType'] = this.matchType
        item.id = baseId + i
        i++
        if (this.activeName == 'first') {
          this.temp.wifiList.forEach(wifi => {
            if (item.limitRuleType === 0 &&
              item.limitRuleType === wifi.limitRuleType &&
              item.macAddress === wifi.macAddress) {
              this.toDeleteRuleList.push(wifi.id)
              this.updateRuleList.push(item)
            } else if (item.limitRuleType === 1 &&
              item.limitRuleType === wifi.limitRuleType &&
              item.matchType === wifi.matchType &&
              item.name === wifi.name) {
              this.toDeleteRuleList.push(wifi.id)
              this.updateRuleList.push(item)
            }
          })
        } else if (this.activeName == 'second') {
          this.temp.recordWifiList.forEach(wifi => {
            if (item.limitRuleType === 0 &&
              item.limitRuleType === wifi.limitRuleType &&
              item.macAddress === wifi.macAddress) {
              this.toDeleteRuleList.push(wifi.id)
              this.updateRuleList.push(item)
            } else if (item.limitRuleType === 1 &&
              item.limitRuleType === wifi.limitRuleType &&
              item.matchType === wifi.matchType &&
              item.name === wifi.name) {
              this.toDeleteRuleList.push(wifi.id)
              this.updateRuleList.push(item)
            }
          })
        }
      })
      if (this.updateRuleList.length > 0) {
        this.dialogFormWarnVisible = true
      } else {
        if (this.activeName == 'first') {
          this.temp.wifiList.unshift(...this.addRuleList)
        } else if (this.activeName == 'second') {
          this.temp.recordWifiList.unshift(...this.addRuleList)
        }
        this.limitRuleTypeVisible = false
        if (this.wifiLibVisible) {
          this.wifiLibVisible = false
        } else if (this.wifiAddVisible) {
          this.wifiAddVisible = false
        }
      }
    },
    validateCheckedWiFi() {
      const rowDatas = this.$refs.wifiLibList.getSelectedDatas()
      const checkedGroup = this.$refs.groupTree.getCheckedKeys()
      if (checkedGroup.length == 0 && (!rowDatas || rowDatas.length == 0)) {
        this.warnMsgVisible = true
        this.$message({
          message: this.$t('pages.wifiBlock_text4'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    importByLib() {
      if (this.validateCheckedWiFi()) {
        this.importData()
      }
    },
    importByTerm() {
      const rowDatas = this.$refs.wifiSelectList.getSelectedDatas()
      if (rowDatas.length === 0) {
        this.warnMsgVisible1 = true
        this.$message({
          message: this.$t('pages.wifiBlock_text4'),
          type: 'error',
          duration: 2000
        })
      } else {
        this.importData()
      }
    },
    saveData() {
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          if (this.activeName == 'first') {
            const index = this.temp.wifiList.findIndex(item => {
              if (this.tempW.limitRuleType == 0 &&
                this.tempW.limitRuleType == item.limitRuleType &&
                item.macAddress == this.tempW.macAddress) {
                return true
              } else if (this.tempW.limitRuleType == 1 &&
                this.tempW.limitRuleType == item.limitRuleType &&
                this.tempW.matchType == item.matchType &&
                item.name == this.tempW.name) {
                return true
              } else {
                return false
              }
            })
            if (index == -1) {
              if (this.tempW.id) {
                this.checkedWifiGrid().updateRowData(this.tempW)
              } else {
                this.tempW.id = new Date().getTime()
                this.temp.wifiList.push(this.tempW)
              }
              this.wifiBlockRuleVisible = false
            } else {
              this.$message({
                message: this.$t('pages.wifiBlock_text5'),
                type: 'error',
                duration: 2000
              })
            }
          } else if (this.activeName == 'second') {
            const index = this.temp.recordWifiList.findIndex(item => {
              if (this.tempW.limitRuleType == 0 &&
                this.tempW.limitRuleType == item.limitRuleType &&
                item.macAddress == this.tempW.macAddress) {
                return true
              } else if (this.tempW.limitRuleType == 1 &&
                this.tempW.limitRuleType == item.limitRuleType &&
                this.tempW.matchType == item.matchType &&
                item.name == this.tempW.name) {
                return true
              } else {
                return false
              }
            })
            if (index == -1) {
              if (this.tempW.id) {
                this.checkedRecordWifiGrid().updateRowData(this.tempW)
              } else {
                this.tempW.id = new Date().getTime()
                this.temp.recordWifiList.push(this.tempW)
              }
              this.wifiBlockRuleVisible = false
            } else {
              this.$message({
                message: this.$t('pages.wifiBlock_text5'),
                type: 'error',
                duration: 2000
              })
            }
          }
        }
      })
    },
    handleCopy() {
      if (this.activeName == 'first') {
        const selectDatas = this.checkedWifiGrid().getSelectedDatas()
        if (selectDatas.length == 0) {
          return;
        }
        selectDatas.forEach(data => {
          const index = this.temp.recordWifiList.findIndex(item => {
            if (data.limitRuleType == 0 && data.limitRuleType == item.limitRuleType && item.macAddress == data.macAddress) {
              return true
            } else if (data.limitRuleType == 1 && data.limitRuleType == item.limitRuleType && data.matchType == item.matchType && item.name == data.name) {
              return true
            } else {
              return false
            }
          })
          if (index == -1) {
            this.temp.recordWifiList.push(data)
          }
        })
      } else if (this.activeName == 'second') {
        const selectDatas = this.checkedRecordWifiGrid().getSelectedDatas()
        if (selectDatas.length == 0) {
          return;
        }
        selectDatas.forEach(data => {
          const index = this.temp.wifiList.findIndex(item => {
            if (data.limitRuleType == 0 && data.limitRuleType == item.limitRuleType && item.macAddress == data.macAddress) {
              return true
            } else if (data.limitRuleType == 1 && data.limitRuleType == item.limitRuleType && data.matchType == item.matchType && item.name == data.name) {
              return true
            } else {
              return false
            }
          })
          if (index == -1) {
            this.temp.wifiList.push(data)
          }
        })
      }
    },
    updateRules() {
      this.limitRuleType = 0
      this.limitRuleTypeVisible = false
      this.dialogFormWarnVisible = false
      if (this.wifiLibVisible) {
        this.wifiLibVisible = false
      } else if (this.wifiAddVisible) {
        this.wifiAddVisible = false
      } else {
        this.ruleBatchEditVisible = false
      }
      if (this.activeName == 'first') {
        this.toDeleteRuleList.forEach(id => {
          this.removeArray(this.temp.wifiList, id)
        })
        this.temp.wifiList.unshift(...this.addRuleList)
      } else if (this.activeName == 'second') {
        this.toDeleteRuleList.forEach(id => {
          this.removeArray(this.temp.recordWifiList, id)
        })
        this.temp.recordWifiList.unshift(...this.addRuleList)
      }
    },
    removeArray(arr, val) {
      for (let i = 0, size = arr.length; i < size; i++) {
        if (arr[i].id == val) {
          arr.splice(i, 1)
          break
        }
      }
    },
    checkWifiSelectionChangeEnd(rowDatas) {
      this.checkWifiDeleteable = rowDatas && rowDatas.length > 0
      this.copyAble = rowDatas && rowDatas.length > 0
      this.rowDatas = rowDatas
    },
    checkRecordWifiSelectionChangeEnd(rowDatas) {
      this.checkRecordWifiDeleteable = rowDatas && rowDatas.length > 0
      this.copyRecordAble = rowDatas && rowDatas.length > 0
      this.rowDatas = rowDatas
    },
    handleBatchEdit() {
      this.limitRuleType = 0
      this.matchType = 0
      this.ruleBatchEditVisible = true
    },
    handleDeleteCheckedWifi() {
      if (this.activeName == 'first') {
        const toDeleteKeys = this.checkedWifiGrid().getSelectedKeys()
        this.checkedWifiGrid().deleteRowData(toDeleteKeys)
        const wifiArray = []
        this.temp.wifiList.forEach(wifi => {
          if (!(toDeleteKeys.join(',').indexOf(wifi.macAddress) > -1)) {
            wifiArray.push(wifi)
          }
        })
        this.temp.wifiList.splice(0, this.temp.wifiList.length, ...wifiArray)
      } else if (this.activeName == 'second') {
        const toDeleteKeys = this.checkedRecordWifiGrid().getSelectedKeys()
        this.checkedRecordWifiGrid().deleteRowData(toDeleteKeys)
        const wifiArray = []
        this.temp.recordWifiList.forEach(wifi => {
          if (!(toDeleteKeys.join(',').indexOf(wifi.macAddress) > -1)) {
            wifiArray.push(wifi)
          }
        })
        this.temp.recordWifiList.splice(0, this.temp.recordWifiList.length, ...wifiArray)
      }
    },
    handleClearCheckedWifi() {
      if (this.activeName == 'first') {
        this.checkedWifiGrid().clearRowData()
        this.temp.wifiList.splice(0, this.temp.wifiList.length)
      } else if (this.activeName == 'second') {
        this.checkedRecordWifiGrid().clearRowData()
        this.temp.recordWifiList.splice(0, this.temp.recordWifiList.length)
      }
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.activeName = 'first'
      this.temp.entityType = this.entityNode.type
      this.temp.entityId = this.entityNode.dataId
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.dialogStatus = 'update'
      this.activeName = 'first'
      if (row.recordBlockType == undefined) {
        row.recordBlockType = 0
      }
      // 旧版本配置的策略中可能缺少id字段，修改时，列表id按下标重新赋值
      for (let i = 0; i < (row.wifiList || []).length; i++) {
        const item = row.wifiList[i]
        item.id = i + 1;
      }
      for (let i = 0; i < (row.recordWifiList || []).length; i++) {
        const item = row.recordWifiList[i]
        item.id = i + 1;
      }
      this.$refs['stgDlg'].show(row, this.formable)
      this.temp.entityType = row.entityType
      this.temp.entityId = row.entityId
    },
    resetLimitRuleType() {
      this.limitRuleType = 0
      this.matchType = 0
    },
    importWifiFromLib() {
      this.wifiLibVisible = true
      this.warnMsgVisible = false
      this.query.groupId = undefined
      this.resetLimitRuleType()
      this.loadWifiGroupTree()
      this.$nextTick(() => {
        this.$refs.wifiLibList && this.$refs.wifiLibList.clearSelection()
      })
    },
    initWifi(data) {
      var macAddressList = []
      data.forEach(item => {
        if (!macAddressList.includes(item.macAddress)) {
          macAddressList.push(item.macAddress)
        }
      })
      return macAddressList
    },
    importWifiFromTerm() {
      this.wifiAddVisible = true
      this.warnMsgVisible1 = false
      this.resetLimitRuleType()
      this.$refs.wifiTargetTree && this.$refs.wifiTargetTree.clearFilter()
      this.$nextTick(() => {
        this.$refs.wifiSelectList.clearSelection()
        if (this.activeName == 'first') {
          const macAddressList = this.initWifi(this.temp.wifiList)
          if (macAddressList.length > 0) {
            this.$refs.wifiSelectList.checkSelectedRows(macAddressList)
          }
        } else {
          const macAddressList = this.initWifi(this.temp.recordWifiList)
          if (macAddressList.length > 0) {
            this.$refs.wifiSelectList.checkSelectedRows(macAddressList)
          }
        }
      })
    },
    handleCreateWifi() {
      this.resetTempW()
      this.wifiBlockRuleVisible = true
      this.ruleDialogStatus = 'create'
      this.$nextTick(() => {
        this.$refs['dataForm2'].clearValidate()
      })
    },
    buttonFormatter(row) {
      return !this.formable
    },
    handleUpdateWifi(row) {
      this.resetTempW()
      this.tempW = Object.assign(this.tempW, row)
      this.wifiBlockRuleVisible = true
      this.ruleDialogStatus = 'update'
      this.$nextTick(() => {
        this.$refs['dataForm2'].clearValidate()
      })
    },
    submitWifiEnd(datas) {
      const macAddresses = datas.map((item, index) => {
        return item.macAddress
      })
      const wifiInfos = this.temp.wifiList.filter(item => macAddresses.indexOf(item.macAddresses) < 0)
      wifiInfos.push(...datas)
      this.temp.wifiList = wifiInfos
    },
    formatRowData(rowData) {
      if (rowData.ruleId) {
        this.checkRule = true
      }
    },
    formatFormData(formData) {
      if (!this.checkRule) {
        formData.ruleId = null
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    actionValidator(rule, value, callback) {
      if (!this.showByOsType()) {
        callback()
      } else if (this.temp.action == 0 && this.temp.record == 0 && !this.temp.ruleId) {
        callback(new Error(this.$t('pages.install_Msg51')))
      } else {
        callback()
      }
    },
    validateForm(formData) {
      return this.responseValidate
    },
    macValidator(rule, value, callback) {
      const re = /^[A-Fa-f0-9]+$/
      const val = value.split('-').join('')
      if (val.length != 12 || !re.test(val)) {
        callback(new Error(this.$t('pages.ipMac_Msg7')))
      } else {
        callback()
      }
    },
    matchTypeValidator(rule, value, callback) {
      if (this.limitRuleType == 1 && this.matchType != 0 && !this.matchType) {
        callback(new Error(this.$t('valid.requireMatchType')))
      } else {
        callback()
      }
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    limitRuleTypeFormatter: function(row, data) {
      const msg = row.limitRuleType == 0 ? this.limitRuleTypeOptions[row.limitRuleType] : this.limitRuleTypeOptions[row.limitRuleType] + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.matchTypeOptions[row.matchType]
      return msg
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.wifiGroupTreeData, data)
    },
    ruleIsCheck(data) {
      this.checkRule = !!data
    }
  }
}
</script>
<style lang="scss" scoped>
  .prohibit >>> .el-checkbox__inner {
    margin-left: 17px;
  }
  .selectionCheckBox {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  >>>.el-tab-pane{
    padding: 0 20px;
  }
</style>
