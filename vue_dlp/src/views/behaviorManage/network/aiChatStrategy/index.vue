<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @closed="resetTemp"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeName">
          <el-tab-pane name="control" :label="$t('pages.aiSendContentLimit')">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <FormItem :label="$t('table.keyword')" label-width="65px">
              <el-input
                v-model="temp.keyword"
                :disabled="!formable"
                type="textarea"
                maxlength="100"
                rows="5"
                show-word-limit
                resize="none"
                style="margin-bottom: 10px;"
              />
              <div style="line-height: 24px; color: #2b7aac;">{{ $t('pages.emailKeyword_text1') }}</div>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="18px">
              <!--用户向AI发送的内容中出现检测规则之内的关键字时触发响应规则-->
              <label for="keyword">{{ $t('pages.aiSendContentLimitTips') }}</label>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="18px">
              <el-checkbox v-model="temp.blockFlag" :disabled="!formable" :true-label="1" :false-label="0">
                {{ $t('pages.forbidSendContent') }}
              </el-checkbox>
            </FormItem>
            <ResponseContent
              :status="dialogStatus"
              :select-style="{ 'margin-top': '5px' }"
              :show-select="true"
              :rule-type-name="$t('table.violationName')"
              :editable="formable"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="temp.ruleId"
              @ruleIsCheck="value => temp.isAlarm = value"
              @getRuleId="value => temp.ruleId = value"
              @validate="value => validRuleId = value"
            />
          </el-tab-pane>
          <el-tab-pane name="record" :label="$t('pages.aiChatRecordSetting')">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <FormItem :label="$t('pages.aiModel')" label-width="65px">
              <el-select
                v-model="selectedAiModels"
                class="multi-select"
                multiple
                clearable
                size="mini"
                :disabled="!formable"
                :placeholder="$t('pages.aiModelAll')"
              >
                <!--<el-option :label="$t('pages.all')" :value="0"/>-->
                <el-option v-for="item in aiModelDict" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="8px">
              <el-radio-group v-model="temp.exceptRule" :disabled="!formable">
                <!--终端监测到检测规则之内的AI模型时触发响应规则-->
                <el-radio :label="0">{{ $t('pages.aiModelLimitInDetectionRule') }}</el-radio>
                <!--终端监测到检测规则之外的AI模型时触发响应规则-->
                <el-radio :label="1">{{ $t('pages.aiModelLimitOutDetectionRule') }}</el-radio>
              </el-radio-group>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="18px">
              <el-checkbox v-model="temp.recordLog" :disabled="!formable" :true-label="1" :false-label="0" @change="recordLogChange">
                {{ $t('pages.aiChatRecordAudit') }}
              </el-checkbox>
            </FormItem>
            <!--
            <FormItem class="backup-limit" label-width="18px" prop="fileBackUpLimit">
              <el-checkbox v-model="temp.isBackup" :disabled="!formable || !temp.recordLog" :true-label="1" :false-label="0" @change="needBackupChange">
                <i18n path="pages.blueTooth_Msg1">
                  <el-input-number
                    slot="size"
                    v-model="temp.fileBackUpLimit"
                    :disabled="temp.isBackup === 0 || !formable"
                    :controls="false"
                    :min="1"
                    :max="10240"
                    step-strictly
                    style="width: 80px;"
                    size="mini"
                  />
                </i18n>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.ftpControl_text3') }}<br/>
                    {{ $t('pages.ftpControl_text4') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-checkbox>
            </FormItem>
            -->
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </tr-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importWebPostStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import ImportStg from '@/views/common/importStg'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import { timeIdValidator, validatePolicy } from '@/utils/validate'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import {
  createStrategy,
  deleteStrategy,
  getByName,
  getStrategyList,
  updateStrategy
} from '@/api/behaviorManage/network/aiChatStrategy'
import { exportStg } from '@/api/stgCommon'
import { getAiNameDict } from '@/utils/dictionary'

export default {
  name: 'AiChatStrategy',
  components: { ResponseContent, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 285,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '100', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'keyword', label: 'keyword', width: '200' },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      activeName: 'control',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: undefined,
        entityId: undefined,
        timeId: 1,
        keyword: '',
        blockFlag: 1,
        isAlarm: 0, // 是否触发违规响应规则
        ruleId: undefined, // 违规响应规则ID
        exceptRule: 0,
        recordLog: 0,
        isBackup: 0, // 是否备份
        fileBackUpLimit: 20 // 备份文件阈值（MB）
      },
      selectedAiModels: [],
      validRuleId: false,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.aiChatStrategy'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.aiChatStrategy'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ],
        fileBackUpLimit: [
          { validator: this.fileBackUpLimitValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      aiModelDict: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.aiModelDict = getAiNameDict()
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery).then(resp => {
        (resp.data.items || []).forEach((item) => {
          if (item.keywordList) {
            item.keyword = item.keywordList.map(each => each.keyword).join('\n')
          }
        })
        return resp
      })
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp(source) {
      this.temp = Object.assign({}, this.defaultTemp, source)
      this.selectedAiModels.splice(0)
      this.activeName = 'control'
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      const tempData = JSON.parse(JSON.stringify(row))
      const aiModelList = tempData.aiModelList || []
      delete tempData.aiModelList
      this.resetTemp(tempData)
      if (aiModelList.length > 0 && aiModelList[0].aiName > 0) {
        aiModelList.forEach((item) => {
          this.selectedAiModels.push(item.aiName)
        })
      }
      if (row.fileBackUpLimit === 0) {
        this.temp.fileBackUpLimit = 20
      } else {
        this.temp.isBackup = 1
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    formatFormData() {
      const tempData = Object.assign({}, this.temp)
      if (this.selectedAiModels.length > 0) {
        tempData.aiModelList = this.selectedAiModels.map(aiName => ({ aiName }))
      } else {
        tempData.aiModelList = [{ aiName: 0 }]
      }
      // if (!tempData.isBackup && tempData.fileBackUpLimit > 0) {
      //   tempData.fileBackUpLimit = 0
      // }
      tempData.fileBackUpLimit = !tempData.recordLog ? 0 : 20
      return tempData
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createStrategy(this.formatFormData()).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          updateStrategy(this.formatFormData()).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.temp.isBackup === 1) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else if (value > 10240) {
          return callback(new Error(this.$t('pages.blueTooth_Msg4')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    recordLogChange(val) {
      if (!val && this.temp.isBackup === 1) {
        this.temp.isBackup = 0
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    },
    needBackupChange(val) {
      if (!val) {
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.backup-limit .el-form-item__error {
  padding-left: 160px;
}
.multi-select>>>input {
  &::placeholder, &::-webkit-input-placeholder, &::-moz-placeholder, &:-ms-input-placeholder {
    color: #666 !important;
  }
}
</style>
