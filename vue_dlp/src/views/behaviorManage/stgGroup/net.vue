<template>
  <strategy-group ref="strategyGroup" :group-id="groupId" :stg-type-ops="stgTypeOps" :group-name="groupName" />
</template>
<script>
import StrategyGroup from '@/views/strategyGroup'
import { stgTypeOps } from '@/views/common/stgTypeOps';

export default {
  name: 'NetGroup',
  components: { StrategyGroup },
  data() {
    return {
      groupName: this.$t('route.NetGroup'),
      stgTypeOps: stgTypeOps.netGroup,
      groupId: 4
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      const { searchInfo } = to.params
      if (searchInfo) {
        const strategyGroup = vm.$refs.strategyGroup
        strategyGroup.query.searchInfo = searchInfo
        strategyGroup.handleFilter()
        to.params.searchInfo = ''
      }
    })
  }
}
</script>

