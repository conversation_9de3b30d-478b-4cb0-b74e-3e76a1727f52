<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textTitle"
    :visible.sync="dialogFormVisible"
    width="800px"
  >
    <div class="show-detail-panel">
      <el-descriptions class="margin-top" :column="2" size="" border>
        <el-descriptions-item :label="$t('table.recordingTime')">
          {{ rowDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.terminalName')">
          <terminal-detail
            :label="rowDetail.terminalName"
            :search-id="rowDetail.terminalId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.user')">
          <user-detail
            :label="rowDetail.userName"
            :search-id="rowDetail.userId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.recordingDriverName')">
          {{ rowDetail.burner }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.discName')">
          {{ rowDetail.cd }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.action')">
          {{ actionFormatter(rowDetail, rowDetail.actionType) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.burnFileSize')">
          {{ rowDetail.burnSize }}
        </el-descriptions-item>
      </el-descriptions>
      <audit-file-downloader
        ref="auditFileDownloader"
        v-permission="'217'"
        style="margin: 8px 0 0;"
        :name="$t('pages.burningFile')"
        topic="burnFile"
        :selection="fileSelection"
        :before-download="beforeDownload"
      />
      <Form ref="detailForm" label-position="right" label-width="100px" style="margin-top:2px;">
        <grid-table
          ref="burnFileList"
          :height="280"
          :show-pager="true"
          :multi-select="true"
          :selectable="selectable"
          :col-model="fileColModel"
          :row-data-api="fileRowDataApi"
          @selectionChangeEnd="fileSelectionChangeEnd"
        />
      </Form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.close') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'BurnLogDetail',
  data() {
    return {
      dialogFormVisible: false,
      textTitle: this.$t('pages.recordingFileDetails'),
      fileColModel: [
        { label: 'fileName1', width: '100', formatter: this.fileNameFormatter },
        { prop: 'filePath', label: 'filePath', width: '250' },
        { prop: 'fileSize', label: 'maxFileSize', width: '100' },
        { prop: 'opType', label: 'operateType', width: '100', formatter: this.opTypeFormatter },
        // 是否有备份，通过能否下载就能够判断，因此去掉此列{ prop: 'fileMd5', label: 'backup', width: '80', formatter: this.backupFormatter },
        { label: 'operate', type: 'button', width: '100', fixed: 'right', hidden: !this.hasPermission('217'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.disabledFormatter }
          ]
        }
      ]
    }
  },
  methods: {
    selectable(row, index) {
      return !!(row.fileGuid && row.devId)
    }
  }
}
</script>
