<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.burnName" :value="query.burnName">
          <span>{{ $t('pages.recordingDriverName') }}：</span>
          <el-input v-model="query.burnName" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.actionType" :value="query.actionType">
          <span>{{ $t('table.action') }}：</span>
          <el-select v-model="query.actionType" style="width: 150px">
            <el-option :label="$t('pages.all')" :value="null"/>
            <el-option :label="$t('pages.forbid')" :value="1"></el-option>
            <el-option :label="$t('pages.allow')" :value="0"></el-option>
          </el-select>
        </SearchItem>
        
        <audit-log-exporter slot="append" v-permission="'247'" :multi-dataset="hasPermission('216')" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'433'" :selection="selection" :delete-log="deleteBurnLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="burnLogList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('433')"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :autoload="autoload"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textTitle"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.recordingTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.recordingDriverName')">
            {{ rowDetail.burner }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.discName')">
            {{ rowDetail.cd }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.burnFileSize')">
            {{ rowDetail.burnSize }}
          </el-descriptions-item>
        </el-descriptions>
        <audit-file-downloader
          ref="auditFileDownloader"
          v-permission="'217'"
          style="margin: 8px 0 0;"
          :name="$t('pages.burningFile')"
          topic="burnFile"
          :selection="fileSelection"
          :before-download="beforeDownload"
        />
        <Form ref="detailForm" label-position="right" label-width="100px" style="margin-top:2px;">
          <grid-table
            ref="burnFileList"
            :height="280"
            :show-pager="true"
            :multi-select="true"
            :selectable="selectable"
            :col-model="fileColModel"
            :row-data-api="fileRowDataApi"
            @selectionChangeEnd="fileSelectionChangeEnd"
          />
        </Form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getBurnLogList, deleteBurnLog, getBurnFileList, exportExcel } from '@/api/behaviorManage/burner/burnLog'
import { objectFormatter } from '@/utils'
import moment from 'moment'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'BurnLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'recordingTime', width: '100', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'burner', label: 'recordingDriverName', width: '150' },
        { prop: 'cd', label: 'discName', width: '150' },
        { prop: 'actionType', label: 'action', width: '130', formatter: this.actionFormatter },
        { prop: 'burnSize', label: 'burnFileSize', width: '130' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('216'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      fileColModel: [
        { label: 'fileName1', width: '100', formatter: this.fileNameFormatter },
        { prop: 'filePath', label: 'filePath', width: '250' },
        { prop: 'fileSize', label: 'maxFileSize', width: '100' },
        { prop: 'opType', label: 'operateType', width: '100', formatter: this.opTypeFormatter },
        // 是否有备份，通过能否下载就能够判断，因此去掉此列{ prop: 'fileMd5', label: 'backup', width: '80', formatter: this.backupFormatter },
        { label: 'operate', type: 'button', width: '100', fixed: 'right', hidden: !this.hasPermission('217'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.disabledFormatter }
          ]
        }
      ],
      selection: [],
      fileSelection: [],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '', // 结束日期，显示在弹框里的
        isTimes: false,
        burnName: null,
        actionType: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'burnLogList'
      },
      fileQuery: { // 查询条件
        page: 1,
        burnId: '',
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false
      },
      buttonStatus: true,
      showTree: true,
      addBtnAble: false,
      protocolOptions: { '1': 'TCP', '2': 'UDP' },
      portTypeOptions: { '1': this.$t('pages.allPort'), '2': this.$t('pages.userDefined') },
      ipTypeOptions: { '1': this.$t('pages.allIP'), '2': this.$t('pages.userDefined') },
      dialogFormVisible: false,
      dialogStatus: '',
      textTitle: this.$t('pages.recordingFileDetails'),
      multipleSelection: [], // 选中行数组集合
      submitting: false,
      portRowData: [],
      percentOptions: [
        { status: 0, percent: 0 },
        { status: 1, percent: 1 },
        { status: 5, percent: 50 },
        { status: 6, percent: 100 }
      ],
      tempTask: {},
      defaultTempTask: {
        backType: 3,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteBurnLog,
    gridTable() {
      return this.$refs['burnLogList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getBurnLogList(searchQuery)
    },
    fileRowDataApi: function(option) {
      option.burnId = this.fileQuery.burnId
      option.createDate = this.fileQuery.createDate
      option.startDate = this.fileQuery.startDate
      option.endDate = this.fileQuery.endDate
      option.isTimes = this.fileQuery.isTimes
      option.searchReport = this.query.searchReport
      return getBurnFileList(option)
    },
    selectionChangeEnd(selection) {
      this.selection = selection
    },
    fileSelectionChangeEnd(selection) {
      this.fileSelection = selection
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi()
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      if (row.filePath != null && row.filePath != '') {
        if (row.filePath.lastIndexOf('\\') >= 0) {
          this.tempTask.fileName = row.filePath.substr(row.filePath.lastIndexOf('\\') + 1, row.filePath.length)
        } else {
          this.tempTask.fileName = row.filePath
        }
      }
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    handleView(row) {
      this.dialogFormVisible = true
      this.fileQuery.burnId = row.burnId
      this.fileQuery.createDate = moment(row.createTime).format('YYYY-MM-DD')
      this.fileQuery.startDate = moment(row.createTime).format('YYYY-MM-DD')
      this.fileQuery.endDate = moment(row.createTime).format('YYYY-MM-DD')
      if (this.$refs['burnFileList']) this.$refs['burnFileList'].execRowDataApi({ page: 1 })
      this.rowDetail = row;
    },
    handleImport() {},
    handleExport(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    selectable(row, index) {
      return !!(row.fileGuid && row.devId)
    },
    disabledFormatter(data, btn) {
      return !data.fileGuid
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    backupFormatter: function(row, data) {
      if (data) {
        return this.$t('text.yes')
      }
      return this.$t('text.no')
    },
    fileNameFormatter: function(row, data) {
      const urlArr = row.filePath.split('\\')
      return urlArr[urlArr.length - 1]
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '216', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    opTypeFormatter(row) {
      return row.opType === 1 ? this.$t('button.upload') : row.opType === 2 ? this.$t('button.download') : ''
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-table .cell span{
    white-space: pre;
  }
  >>>.el-descriptions-item__label {
    min-width: 150px;
  }
  >>>.el-descriptions-item__content {
    min-width: 100px;
    max-width: 180px;
  }
</style>
