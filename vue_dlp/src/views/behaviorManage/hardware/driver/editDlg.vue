<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      time-able
      :time-mode="2"
      :title="$t('pages.driverStg')"
      :width="810"
      :pane-height="600"
      :stg-code="50"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      class="driver-stg-dlg"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-tabs ref="tabs" v-model="activeName" class="inner-tabs" type="card">
          <el-tab-pane v-if="slotName == 1 || slotName == 2" :label="$t('pages.diskDriverManage')" class="first-pane" name="first">
            <div v-if="slotName == 1">
              <FormItem :label="$t('pages.removableDisk')" label-width="145px" style="margin-top: 7px;">
                <!-- <el-col :span="24">
                  <span class="option-label ellipsis" :title=" $t('pages.executableFile')">{{ $t('pages.executableFile') }}</span>
                  <el-radio v-model="temp.diskBlock[0].canExeRun" :disabled="!formable" :label="0">{{ $t('pages.canMap1') }}</el-radio>
                  <el-radio v-model="temp.diskBlock[0].canExeRun" :disabled="!formable" :label="1">{{ $t('pages.canMap2') }}</el-radio>
                </el-col>
                <el-col :span="24">
                  <span class="option-label ellipsis" :title=" $t('pages.autoOperation')">{{ $t('pages.autoOperation') }}</span>
                  <el-radio v-model="temp.diskBlock[0].canAutoRun" :disabled="!formable" :label="0">{{ $t('pages.canMap1') }}</el-radio>
                  <el-radio v-model="temp.diskBlock[0].canAutoRun" :disabled="!formable" :label="1">{{ $t('pages.canMap2') }}</el-radio>
                </el-col> -->
                <el-col :span="24">
                  <el-checkbox v-model="removeDisk.canExeRun" :disabled="!formable" @change="changeCheckBox($event, 'temp.diskBlock[0].canExeRun')">{{ $t('pages.stopExecFileRun') }}</el-checkbox>
                  <el-checkbox v-model="removeDisk.canAutoRun" :disabled="!formable" @change="changeCheckBox($event, 'temp.diskBlock[0].canAutoRun')">{{ $t('pages.stopDevAutoRun') }}</el-checkbox>
                </el-col>
              </FormItem>
              <FormItem :label="$t('pages.networkDisk')" label-width="145px">
                <!-- <el-col :span="24">
                  <span class="option-label ellipsis" :title=" $t('pages.executableFile')">{{ $t('pages.executableFile') }}</span>
                  <el-radio v-model="temp.diskBlock[1].canExeRun" :disabled="!formable" :label="0">{{ $t('pages.canMap1') }}</el-radio>
                  <el-radio v-model="temp.diskBlock[1].canExeRun" :disabled="!formable" :label="1">{{ $t('pages.canMap2') }}</el-radio>
                </el-col>
                <el-col :span="24">
                  <span class="option-label ellipsis" :title=" $t('pages.autoOperation')">{{ $t('pages.autoOperation') }}</span>
                  <el-radio v-model="temp.diskBlock[1].canAutoRun" :disabled="!formable" :label="0">{{ $t('pages.canMap1') }}</el-radio>
                  <el-radio v-model="temp.diskBlock[1].canAutoRun" :disabled="!formable" :label="1">{{ $t('pages.canMap2') }}</el-radio>
                </el-col> -->
                <el-col :span="24">
                  <el-checkbox v-model="netDisk.canExeRun" :disabled="!formable" @change="changeCheckBox($event, 'temp.diskBlock[1].canExeRun')">{{ $t('pages.stopExecFileRun') }}</el-checkbox>
                  <el-checkbox v-model="netDisk.canAutoRun" :disabled="!formable" @change="changeCheckBox($event, 'temp.diskBlock[1].canAutoRun')">{{ $t('pages.stopDevAutoRun') }}</el-checkbox>
                </el-col>
              </FormItem>
              <FormItem :label="$t('pages.cd')" label-width="145px">
                <!-- <el-col :span="24">
                  <span class="option-label ellipsis" :title=" $t('pages.executableFile')">{{ $t('pages.executableFile') }}</span>
                  <el-radio v-model="temp.diskBlock[2].canExeRun" :disabled="!formable" :label="0">{{ $t('pages.canMap1') }}</el-radio>
                  <el-radio v-model="temp.diskBlock[2].canExeRun" :disabled="!formable" :label="1">{{ $t('pages.canMap2') }}</el-radio>
                </el-col>
                <el-col :span="24">
                  <span class="option-label ellipsis" :title=" $t('pages.autoOperation')">{{ $t('pages.autoOperation') }}</span>
                  <el-radio v-model="temp.diskBlock[2].canAutoRun" :disabled="!formable" :label="0">{{ $t('pages.canMap1') }}</el-radio>
                  <el-radio v-model="temp.diskBlock[2].canAutoRun" :disabled="!formable" :label="1">{{ $t('pages.canMap2') }}</el-radio>
                </el-col> -->
                <el-col :span="24">
                  <el-checkbox v-model="lightDisk.canExeRun" :disabled="!formable" @change="changeCheckBox($event, 'temp.diskBlock[2].canExeRun')">{{ $t('pages.stopExecFileRun') }}</el-checkbox>
                  <el-checkbox v-model="lightDisk.canAutoRun" :disabled="!formable" @change="changeCheckBox($event, 'temp.diskBlock[2].canAutoRun')">{{ $t('pages.stopDevAutoRun') }}</el-checkbox>
                </el-col>
              </FormItem>
              <FormItem :label="$t('pages.floppyDriveLimit')" :rules="timeRule" label-width="145px" prop="0">
                <el-select v-model="temp.driverBlock[0].timeId" :disabled="!formable" :placeholder="$t('text.select')" style="width: 180px;">
                  <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <link-button :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
              </FormItem>
              <FormItem label-width="145px">
                <el-radio v-model="temp.driverBlock[0].controlCode" :disabled="!formable" :label="0">{{ $t('pages.controlMap1') }}</el-radio>
                <el-radio v-model="temp.driverBlock[0].controlCode" :disabled="!formable" :label="1">{{ $t('pages.controlMap2') }}</el-radio>
                <el-radio v-model="temp.driverBlock[0].controlCode" :disabled="!formable" :label="2">{{ $t('pages.controlMap3') }}</el-radio>
              </FormItem>
            </div>

            <div v-if="slotName == 1 || slotName == 2">
              <FormItem :label="$t('pages.cdDriverLimit')" :rules="timeRule" label-width="145px" prop="1">
                <el-select v-model="temp.driverBlock[1].timeId" :disabled="!formable" :placeholder="$t('text.select')" style="width: 180px;">
                  <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <link-button :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
              </FormItem>
              <FormItem label-width="145px">
                <el-radio v-model="temp.driverBlock[1].controlCode" :disabled="!formable" :label="0">{{ $t('pages.controlMap1') }}</el-radio>
                <el-radio v-model="temp.driverBlock[1].controlCode" :disabled="!formable" :label="1">{{ $t('pages.controlMap2') }}</el-radio>
                <el-radio v-model="temp.driverBlock[1].controlCode" :disabled="!formable" :label="2">{{ $t('pages.controlMap3') }}</el-radio>
              </FormItem>
            </div>
          </el-tab-pane>

          <el-tab-pane :label="$t('pages.usbManage')" name="second" style="margin-top: 10px">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <FormItem label-width="100px" :rules="timeRule" :label="$t('pages.effectTime')" prop="2">
              <el-row>
                <el-col :span="8">
                  <el-select v-model="temp.driverBlock[2].timeId" :disabled="!formable" :placeholder="$t('text.select')">
                    <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="2">
                  <link-button :formable="formable" btn-style="margin-left: 3px" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
                </el-col>
                <el-col :span="14">
                  <div style="float:right;">
                    <el-button size="small" icon="el-icon-plus" :disabled="!formable" @click="showUSBSelectDlg">{{ $t('button.add') }}</el-button>
                    <el-button size="small" icon="el-icon-delete" :disabled="!deleteable || !formable" @click="removeSelectUSB">{{ $t('button.delete') }}</el-button>
                  </div>
                </el-col>
              </el-row>
            </FormItem>
            <grid-table
              ref="usbTable"
              :height="200"
              :show-pager="false"
              :col-model="colModel"
              :row-datas="temp.usbList"
              @selectionChangeEnd="selectChange"
            />
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="40px" prop="action">
              {{ $t('pages.driver_text1_1') }}
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <label style="font-weight: 500;color: #409eff;font-size: small; margin-left: 15px">{{ ruleText }}</label>
            <el-checkbox-group v-model="temp.executeCode" :disabled="!formable" style="margin-bottom: 5px" @change="executeCodeChangeEnd">
              <el-checkbox class="prohibit" :label="1" :disabled="!formable">{{ $t('pages.controlMap2') }}</el-checkbox>
              <el-checkbox class="prohibit" :label="2" :disabled="!formable">{{ $t('pages.controlMap3') }}</el-checkbox>
            </el-checkbox-group>
            <ResponseContent
              ref="rulecomp1"
              :select-style="{ 'margin-top': 0 }"
              :rule-type-name="$t('pages.driver_text6')"
              :show-select="true"
              :editable="formable"
              read-only
              :prop-check-rule="temp.executeCode.includes(3)"
              :show-check-rule="true"
              :prop-rule-id="temp.ruleId"
              style="margin-bottom:10px"
              @getRuleId="getRuleId"
              @ruleIsCheck="ruleIsCheck"
              @validate="(val) => { rulecomp1Validate = val }"
            />
          </el-tab-pane>
          <el-tab-pane v-if="slotName == 1" :label="$t('pages.driver_text9')" name="third" style="margin-top: 10px">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem prop="formatAction" label-width="40px">
              {{ $t('pages.driver_text7_1') }}
            </FormItem>
            <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.driver_text8') }}</label>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.driver_text13') }}</label>
            <ResponseContent
              ref="rulecomp2"
              :rule-type-name="$t('pages.driver_text10')"
              :show-select="true"
              :editable="formable"
              read-only
              :prop-check-rule="formatRuleChecked"
              :show-check-rule="true"
              :prop-rule-id="temp.formatRule.ruleId"
              style="margin-bottom:10px"
              @getRuleId="getFormatRuleId"
              @ruleIsCheck="formatRuleIsCheck"
              @validate="(val) => { rulecomp2Validate = val }"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </stg-dialog>

    <edit-time-dlg ref="editTimeDlg" @update="updateData" />
    <usb-select-dlg ref="usbSelectDlg" @select="selectUSBEnd"/>
  </div>
</template>
<script>
import { createStrategy, getStrategyByName, updateStrategy } from '@/api/behaviorManage/hardware/driver'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { getUsbTypeDict } from '@/utils/dictionary'
import { timeIdValidator } from '@/utils/validate'
import UsbSelectDlg from '@/views/system/baseData/usbDevice/usbSelectDlg'
import editTimeDlg from './editTimeDlg'

export default {
  name: 'DriverDlg',
  components: { UsbSelectDlg, ResponseContent, editTimeDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      activeName: 'first',
      colModel: [
        { prop: 'usbName', label: 'volumeName', width: '100', sort: true },
        { prop: 'usbCode', label: 'pnpDeviceId', width: '100', sort: true },
        { prop: 'usbType', label: 'devType', width: '100', sort: true, formatter: this.usbTypeOptionFormatter },
        { prop: 'exceptionTime', label: 'exceptionTime', width: '100', sort: true },
        { prop: 'rwPermission', label: 'rwPermission', width: '130', sort: true, formatter: this.rwPermissionTypeOptionFormatter },
        { prop: 'vid', label: 'vid', width: '90', sort: true },
        { prop: 'manufacturer', label: 'manufacturer', width: '80', sort: true },
        { prop: 'pid', label: 'pid', width: '90', sort: true },
        { prop: 'srcPnpDeviceId', label: 'deviceSerialNumber', width: '120', sort: true },
        { prop: 'usbSize', label: 'usbSize', width: '120', sort: true },
        { prop: '', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '70',
          buttons: [
            { label: 'edit', disabledFormatter: () => !this.formable, click: this.handleConfigTime }
          ]
        }
      ],
      pickerOptions: { // 时间设置今天以及今天之后
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      usbTypeOptions: getUsbTypeDict(),
      rwPermissionTypeOptions: [{ value: 0, label: this.$t('pages.readAndWrite') }, { value: 1, label: this.$t('pages.onlyRead') }, { value: 2, label: this.$t('pages.forbid') }],
      temp: {},
      removeDisk: {
        canExeRun: false,
        canAutoRun: false
      },
      netDisk: {
        canExeRun: false,
        canAutoRun: false
      },
      lightDisk: {
        canExeRun: false,
        canAutoRun: false
      },
      defaultTemp: {
        id: null,
        name: '',
        active: false,
        remark: '',
        diskBlock: [
          { diskType: 2, canExeRun: 0, canAutoRun: 0 },
          { diskType: 3, canExeRun: 0, canAutoRun: 0 },
          { diskType: 4, canExeRun: 0, canAutoRun: 0 }
        ],
        driverBlock: [
          { driverType: 1, controlCode: 0, timeId: 1 },
          { driverType: 2, controlCode: 0, timeId: 1 },
          { driverType: 3, controlCode: 0, timeId: 1 }
        ],
        formatRule: {
          ruleId: null,
          checkRule: false
        },
        usbList: [],
        entityType: '',
        entityId: null,
        ruleId: null,
        action: 0,
        executeCode: [],
        startTime: null,
        endTime: null,
        rwPermission: 0
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.stgNameRequired'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        usbSize: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        usbName: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        usbCode: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        action: [{ validator: this.actionValidator, trigger: 'blur' }],
        formatAction: [{ validator: this.formatActionValidator, trigger: 'blur' }]
      },
      timeRule: { validator: this.timeIdValidator, trigger: 'change' },
      submitting: false,
      slotName: undefined,
      usbDeviceTreeData: [],
      deleteable: false,
      formatRuleChecked: false,
      dialogFormVisible: false
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    ruleText() {
      const text = {
        true: this.$t('pages.driver_text11'),
        false: this.$t('pages.driver_text11')
      }
      return text[this.slotName == 4]
    }
  },
  watch: {},
  created() {
    this.resetTemp()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    changeCheckBox(event, modelName) {
      // event 复选框当前状态
      // v-model 绑定的参数名
      if (modelName == 'temp.diskBlock[0].canExeRun') {
        if (event) {
          this.temp.diskBlock[0].canExeRun = 1
        } else {
          this.temp.diskBlock[0].canExeRun = 0
        }
      } else if (modelName == 'temp.diskBlock[0].canAutoRun') {
        if (event) {
          this.temp.diskBlock[0].canAutoRun = 1
        } else {
          this.temp.diskBlock[0].canAutoRun = 0
        }
      } else if (modelName == 'temp.diskBlock[1].canExeRun') {
        if (event) {
          this.temp.diskBlock[1].canExeRun = 1
        } else {
          this.temp.diskBlock[1].canExeRun = 0
        }
      } else if (modelName == 'temp.diskBlock[1].canAutoRun') {
        if (event) {
          this.temp.diskBlock[1].canAutoRun = 1
        } else {
          this.temp.diskBlock[1].canAutoRun = 0
        }
      } else if (modelName == 'temp.diskBlock[2].canExeRun') {
        if (event) {
          this.temp.diskBlock[2].canExeRun = 1
        } else {
          this.temp.diskBlock[2].canExeRun = 0
        }
      } else {
        if (event) {
          this.temp.diskBlock[2].canAutoRun = 1
        } else {
          this.temp.diskBlock[2].canAutoRun = 0
        }
      }
    },
    closed() {
      this.resetTemp()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.formatRuleChecked = false
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.activeName = this.slotName == 1 || this.slotName == 2 ? 'first' : 'second'
    },
    executeCodeChangeEnd(val) {
      const valMap = { 1: 2, 2: 1 }
      const deleteValue = valMap[[...val].pop()]
      this.temp.executeCode = val.filter(v => v != deleteValue)
    },
    showUSBSelectDlg() {
      if (this.formable) {
        this.$refs.usbSelectDlg.show()
      }
    },
    selectChange(val) {
      this.deleteable = val.length > 0
    },
    removeSelectUSB() {
      const selectedIds = this.$refs['usbTable'].getSelectedIds()
      if (selectedIds.length > 0) {
        for (let i = 0; i < this.temp.usbList.length; i++) {
          const tempId = this.temp.usbList[i].id
          if (selectedIds.indexOf(tempId) > -1) {
            this.temp.usbList.splice(i, 1)
            i--
          }
        }
      }
    },
    selectUSBEnd(datas) {
      if (!datas) {
        return;
      }
      if (this.temp.usbList.length == 0) {
        this.temp.usbList.push(...datas)
      } else {
        datas.forEach((data) => {
          data.startTime = data.exceptionTime ? data.exceptionTime.split('~')[0] : ''
          data.endTime = data.exceptionTime ? data.exceptionTime.split('~')[1] : ''
          const index = this.temp.usbList.findIndex(item => item.id == data.id)
          if (index > -1) {
            this.temp.usbList.splice(index, 1, data)
          } else {
            this.temp.usbList.push(data)
          }
        })
      }
      // this.temp.usbList.forEach((item, i) => { item.rowId = i + 1 })
    },
    handleCreate() {
      this.removeDisk = {
        canExeRun: false,
        canAutoRun: false
      }
      this.netDisk = {
        canExeRun: false,
        canAutoRun: false
      }
      this.lightDisk = {
        canExeRun: false,
        canAutoRun: false
      }
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    initCheckBox(data) {
      // this.removeDisk = {}
      // this.netDisk = {}
      // this.lightDisk = {}
      if (data.diskBlock[0].canExeRun == 0) {
        this.removeDisk.canExeRun = false
      } else {
        this.removeDisk.canExeRun = true
      }
      if (data.diskBlock[0].canAutoRun == 0) {
        this.removeDisk.canAutoRun = false
      } else {
        this.removeDisk.canAutoRun = true
      }
      if (data.diskBlock[1].canExeRun == 0) {
        this.netDisk.canExeRun = false
      } else {
        this.netDisk.canExeRun = true
      }
      if (data.diskBlock[1].canAutoRun == 0) {
        this.netDisk.canAutoRun = false
      } else {
        this.netDisk.canAutoRun = true
      }
      if (data.diskBlock[2].canExeRun == 0) {
        this.lightDisk.canExeRun = false
      } else {
        this.lightDisk.canExeRun = true
      }
      if (data.diskBlock[2].canAutoRun == 0) {
        this.lightDisk.canAutoRun = false
      } else {
        this.lightDisk.canAutoRun = true
      }
    },
    handleUpdate: function(row) {
      this.initCheckBox(row)
      this.$refs['stgDlg'].show(row, this.formable)
    },
    handleShow: function(row, isGenerateStrategy) {
      this.checkIds.splice(0)
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    formatRowData(rowData) {
      const code = []
      const { action, ruleId } = rowData
      action && code.push(action)
      ruleId && code.push(3)
      rowData.executeCode = code
      if (rowData.formatRule && rowData.formatRule.ruleId) {
        this.formatRuleChecked = true
      }
      // 兼容旧策略usb设备的读写权限默认读写(即为0)
      if (rowData.usbList && rowData.usbList.length > 0) {
        rowData.usbList.forEach(row => {
          if (row.rwPermission == undefined) {
            row.rwPermission = 0
          }
        })
      }
    },
    formatFormData(formData) {
      if (formData.executeCode.indexOf(3) < 0) {
        formData.ruleId = null
      }
      if (formData.formatRule && !this.formatRuleChecked) {
        formData.formatRule.ruleId = null
      }
      if (formData.executeCode.length === 0) {
        // 表示允许使用
        formData.driverBlock[2].controlCode = 0
      } else if (formData.executeCode.indexOf(2) > -1) {
        // 表示只读
        formData.driverBlock[2].controlCode = 2
      } else {
        // 其它情况，1、只勾选“触发响应规则”；2、只勾选禁止使用；3、两个都勾选，则赋值禁止使用
        formData.driverBlock[2].controlCode = 1
      }
      // 传递executeCode到后端，判断如果只勾选了“触发响应规则”,管理员日志记录限制方式为允许使用
      formData.driverBlock[2].executeCode = JSON.parse(JSON.stringify(formData.executeCode))
      formData.action = formData.executeCode.indexOf(2) > -1 ? 2 : (formData.executeCode.indexOf(1) > -1 ? 1 : 0)
      // 策略外层JSON不需要timeId，内部某些成员变量需要
      delete formData['timeId']
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    getFormatRuleId(value) {
      if (!this.temp.formatRule) {
        this.temp.formatRule = {}
      }
      this.temp.formatRule.ruleId = value
    },
    usbTypeOptionFormatter(row, data) {
      let msg = ''
      this.usbTypeOptions.forEach(item => {
        if (data == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    rwPermissionTypeOptionFormatter(row, data) {
      let msg = ''
      this.rwPermissionTypeOptions.forEach(item => {
        if (data == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    exceptionTimeFormatter(row, data) {
    },
    handleConfigTime(row) {
      this.$refs.editTimeDlg.show(row)
    },
    updateData(newData) {
      if (this.temp.usbList && this.temp.usbList.length > 0) {
        this.temp.usbList.forEach((usbInfo, index) => {
          if (newData.id === usbInfo.id) {
            usbInfo.startTime = newData.startTime == null ? '' : newData.startTime
            usbInfo.endTime = newData.endTime == null ? '' : newData.endTime
            usbInfo.exceptionTime = newData.startTime && newData.endTime ? newData.startTime + '~' + newData.endTime : ''
            if (newData.checkRw == 1) {
              usbInfo.rwPermission = newData.rwPermission
            }
          }
        })
        this.temp.usbList.splice(0, this.temp.usbList.length, ...this.temp.usbList)
      }
    },
    actionValidator(rule, value, callback) {
      if (!this.rulecomp1Validate) {
        this.activeName = 'second'
      }
      callback()
    },
    formatActionValidator(rule, value, callback) {
      if (!this.rulecomp2Validate) {
        this.activeName = 'third'
      }
      callback()
    },
    // timeId 的验证方法，由于有多个，根据prop获取timeId的值后，再传入验证方法中
    timeIdValidator(rule, value, callback) {
      const index = rule.field
      const timeId = this.temp.driverBlock[index].timeId
      return timeIdValidator(rule, timeId, callback, (result) => {
        if (!result) {
          this.activeName = ['0', '1'].includes(index) ? 'first' : 'second'
        }
      })
    },
    validateForm(formData) {
      if ((this.activeName === 'second' && !this.rulecomp1Validate) || (this.activeName === 'third' && !this.rulecomp2Validate)) {
        return false
      } else {
        return true
      }
    },
    ruleIsCheck(data) {
      if (data === 1) {
        this.temp.executeCode.unshift(3);
      } else {
        const executeCode = this.temp.executeCode.filter(code => code != 3)
        this.$set(this.temp, 'executeCode', executeCode)
      }
    },
    formatRuleIsCheck(data) {
      this.formatRuleChecked = data === 1
    }
  }
}
</script>
<style lang="scss" scoped>
.el-tabs.el-tabs--top{
  border: none;
  .el-tabs__header{
    margin: 0;
    border: 1px solid #aaaaaa;
    border-bottom: none;
    .el-tabs__nav{
      border-top: 0;
      border-left: 0;
    }
  }
}
.first-pane {
  padding: 5px 0;
}
.el-tab-pane {
  height: auto;
}
.inner-tabs{
  >>>.el-tabs__content {
    overflow: auto;
    border-width: 1px 0px 0px 0px;
  }
  >>>.el-tabs__header {
    padding: 3px 0 0 3px;
  }
  >>>.el-tabs__item {
    height: 37px;
  }
}
.driver-stg-dlg {
  >>>#pane-windows {
    padding: 0px;
  }
  >>>#pane-linux {
    padding: 0px;
  }
  >>>#pane-mac {
    padding: 0px;
  }
  >>>.first-divider {
    margin-top: 0px;
  }
}
.el-checkbox {
  margin-right: 15px;
}
.el-checkbox:last-of-type {
  margin-right: 0;
}
>>>.el-dialog__body .el-button {
  margin: 2px 0;
}
.option-label {
  width: 150px;
  padding-right: 15px;
  display: inline-block;
  text-align: right;
  vertical-align: bottom;
}
.el-col-24 {
  padding: 0 15px;
}
.prohibit >>> .el-checkbox__inner {
  margin-left: 17px;
}
</style>
