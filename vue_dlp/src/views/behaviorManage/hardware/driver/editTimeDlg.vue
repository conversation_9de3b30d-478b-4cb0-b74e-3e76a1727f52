<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.driver_text14')"
    :visible.sync="dialogFormVisible"
    width="600px"
  >
    <Form
      ref="dataForm"
      :model="temp"
      label-position="right"
      label-width="80px"
      style="width: 500px"
    >
      <el-row v-if="showName">
        <el-col :span="11">
          <FormItem :label="$t('table.volumeName')" style="margin-left: -30px; width: 280px">
            <el-input v-model="temp.usbName" :disabled="true" :maxlength="150"></el-input>
          </FormItem>
        </el-col>
        <el-col :span="11" style="margin-left: 21px">
          <FormItem :label="$t('table.pnpDeviceId')" style="width: 280px">
            <el-input v-model="temp.usbCode" :disabled="true" :maxlength="150"></el-input>
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('pages.driver_text15')" style="margin-left: -30px">
            <el-date-picker
              v-model="temp.startTime"
              :picker-options="pickerOptions"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('pages.manageLibrary_encryptionKey_text3')"
              style="width: 200px"
            >
            </el-date-picker>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.driver_text16')">
            <el-date-picker
              v-model="temp.endTime"
              :picker-options="pickerOptions"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              :placeholder="$t('pages.manageLibrary_encryptionKey_text3')"
              :default-time="'23:59:59'"
              style="width: 200px"
            >
            </el-date-picker>
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="5">
          <el-checkbox v-model="temp.checkRw" :true-label="1" :false-label="0" style="margin-left: -10px; margin-top: 10px">{{ $t('table.rwPermission') }}</el-checkbox>
        </el-col>
        <el-col :span="12">
          <FormItem style="margin-left: -115px; margin-top: 4px" label-width="100px">
            <el-radio-group v-model="temp.rwPermission" :disabled="temp.checkRw == 0">
              <el-radio :label="0">{{ $t('pages.readAndWrite') }}</el-radio>
              <el-radio :label="1">{{ $t('pages.onlyRead') }}</el-radio>
              <el-radio :label="2">{{ $t('pages.forbid') }}</el-radio>
            </el-radio-group>
          </FormItem>
        </el-col>
      </el-row>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="saveTime">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'UsbReleaseTimeDlg',
  props: {
    showName: { type: Boolean, default: true }
  },
  data() {
    return {
      dialogFormVisible: false,
      submitting: false,
      pickerOptions: { // 时间设置今天以及今天之后
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      temp: {
        usbName: '',
        usbCode: '',
        startTime: '',
        endTime: '',
        checkRw: 1,
        rwPermission: 0
      },
      defaultTemp: {
        usbName: '',
        usbCode: '',
        startTime: '',
        endTime: '',
        checkRw: 1,
        rwPermission: 0
      },
      oldRwPermission: null
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      if (data) {
        this.formatTemp(data)
      } else {
        this.resetTemp()
      }
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    formatTemp(data) {
      this.$set(data, 'checkRw', 1)
      this.oldRwPermission = data.rwPermission
      if (data.rwPermission) {
        data.rwPermission = parseInt(data.rwPermission)
      }
      if (data.exceptionTime && (data.startTime == undefined || data.endTime == undefined)) {
        this.$set(data, 'startTime', data.exceptionTime.split('~')[0])
        this.$set(data, 'endTime', data.exceptionTime.split('~')[1])
      }
      this.temp = Object.assign({}, data)
    },
    saveTime() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.startTime && !this.temp.endTime) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.driver_text19'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          if (!this.temp.startTime && this.temp.endTime) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.driver_text18'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          if (this.compareDate(this.temp.startTime, this.temp.endTime) != 1) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.driver_text17'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          if (this.temp.checkRw == 0) {
            this.temp.rwPermission = parseInt(this.oldRwPermission)
          }
          this.$emit('update', this.temp)
          this.dialogFormVisible = false
          this.submitting = false
        } else {
          this.submitting = false
        }
      })
    },
    compareDate(date1, date2) {
      if (date1 && date2) {
        const startDate = date1.replace(/\:|\s|-/g, '')
        const endDate = date2.replace(/\:|\s|-/g, '')
        if (startDate >= endDate) {
          return -1
        } else if (startDate < endDate) {
          return 1
        }
        return 0
      } else {
        return 1
      }
    },
    timeValidator(rule, value, callback) {
      if (!value && rule.field == 'startTime') {
        callback(new Error(this.$t('pages.driver_text18')))
      } else if (!value && rule.field == 'endTime') {
        callback(new Error(this.$t('pages.driver_text19')))
      } else {
        callback()
      }
    }
  }
}
</script>
