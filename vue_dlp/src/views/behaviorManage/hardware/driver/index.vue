<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <common-downloader
          v-permission="'555'"
          :disabled="!deleteable"
          :name="$t('route.Driver') + '.xlsx'"
          :button-name="$t('button.export')"
          button-size="mini"
          @download="execExport"
        />
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;" v-html="strategyFormatter(props.detail)"></span>
          </div>
        </template>
      </grid-table>
    </div>
    <driver-dlg ref="stgDlg" :active-able="treeable" :formable="formable" :entity-node="checkedEntityNode" @submitEnd="submitEnd"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importDriverStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import { getStrategyPage, deleteStrategy, exportStrategy } from '@/api/behaviorManage/hardware/driver'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { osTypeIconFormatter, stgActiveIconFormatter, stgEntityIconFormatter, timePeriodFormatter } from '@/utils/formatter'
import DriverDlg from './editDlg'
import ImportStg from '@/views/common/importStg'
import CommonDownloader from '@/components/DownloadManager/common'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'Driver',
  components: { DriverDlg, ImportStg, CommonDownloader },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 50,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectiveDate', width: '100', formatter: timePeriodFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', ellipsis: false, type: 'popover', originData: true, formatter: this.strategyFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      submitting: false,
      checkedEntityNode: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  watch: {
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    handleShow: function(row) {
      this.$refs['stgDlg'].handleShow(row)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data, c) {
      const controlMap = {
        0: this.$t('pages.controlMap1'),
        1: this.$t('pages.controlMap2'),
        2: this.$t('pages.controlMap3')
      }
      const canMap = {
        0: this.$t('pages.canMap1'),
        1: this.$t('pages.canMap2')
      }
      let usbStg = ''
      if (row.action !== undefined) {
        usbStg += controlMap[row.action]
      }
      if (row.ruleId) {
        if (usbStg) {
          usbStg += this.$t('pages.and')
        }
        usbStg += this.$t('pages.triggerResponseRule')
      }
      if (usbStg) {
        usbStg = this.$t('pages.driver_text1') + '：' + usbStg
      }
      if (row.osType == 1) {
        let usbAdvancedStg = ''
        if (row.formatRule && row.formatRule.ruleId) {
          usbAdvancedStg = this.$t('pages.driver_text2')
        }
        return `${this.$t('pages.diskDriverManage') + '：'}{ ${this.$t('pages.removableDisk')}（${canMap[row.diskBlock[0].canExeRun]}${this.$t('pages.executableFile')}，${canMap[row.diskBlock[0].canAutoRun]}${this.$t('pages.autoOperation')}）
        、${this.$t('pages.networkDisk1')}（${canMap[row.diskBlock[1].canExeRun]}${this.$t('pages.executableFile')}，${canMap[row.diskBlock[1].canAutoRun]}${this.$t('pages.autoOperation')}）
        、${this.$t('pages.cd1')}（${canMap[row.diskBlock[2].canExeRun]}${this.$t('pages.executableFile')}，${canMap[row.diskBlock[2].canAutoRun]}${this.$t('pages.autoOperation')}）
        、${this.$t('pages.floppyDrive')}${controlMap[row.driverBlock[0].controlCode]}
        、${this.$t('pages.cdDriver')}${controlMap[row.driverBlock[1].controlCode]} }；
         ${this.$t('pages.usbManage') + '：'}{ ${usbStg} }； ${this.$t('pages.driver_text9') + '：'}{ ${usbAdvancedStg} }`
      } else if (row.osType == 2) {
        return `${this.$t('pages.diskDriverManage') + '：'}{ ${this.$t('pages.cdDriver')}${controlMap[row.driverBlock[1].controlCode]} }；${this.$t('pages.usbManage') + '：'}{ ${usbStg} }；`
      } else {
        return usbStg
      }
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    execExport(file) {
      const stgIds = this.gridTable && this.gridTable.getSelectedIds()
      const opts = { file, jwt: true, topic: this.$route.name }
      exportStrategy({ strategyIds: stgIds.join(',') }, opts)
    }
  }
}
</script>
