<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('text.stgTitleSuffix', { title: $t('route.manualLabel') })"
      :active-able="activeAble"
      :stg-code="278"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <div style="padding-left: 30px;">
          <el-divider content-position="left">{{ $t('pages.labelGradeConfig') }}</el-divider>
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('pages.highLevel')">
                <tree-select
                  :checked-keys="checkedKeys"
                  :data="labelGradeTree"
                  :width="200"
                  :disabled="!formable"
                  style="width: 200px;"
                  clearable
                  @change="treeSelectChange"
                />
              </FormItem>
            </el-col>
          </el-row>
          <el-divider content-position="left">{{ $t('pages.labelInfoConfig') }}</el-divider>
          <div style="height:250px;">
            <import-table
              ref="labelInfoGrid"
              :search-info-prompt-message="$t('table.labelName')"
              :delete-disabled="!deleteable"
              :col-model="colModel"
              :row-datas="tempLabelElgData"
              :row-no-label="$t('table.keyId')"
              :formable="formable"
              :selectable="labelSelectable"
              :handle-create="handleLabelCreate"
              :handle-delete="handleDelete"
              :handle-import="handleLabelImport"
              :handle-search="handleLabelSearch"
              @selectionChangeEnd="(rowDatas) => deleteable = rowDatas.length > 0"
            />
            <!-- :show-batch-update="true" -->
          </div>
        </div>
      </template>
    </stg-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="tempLRules"
        :model="tempL"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.labelName')" prop="name">
          <el-input v-model="tempL.name" v-trim :maxlength="60" @input="handleInput" @change="handelChange"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-col :span="addGroupAble ? 21 : 24">
              <el-select v-model="tempL.groupId" :placeholder="$t('text.select')">
                <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
              </el-select>
            </el-col>
            <el-col v-if="addGroupAble" style="padding-top:1px" :span="3">
              <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem v-if="showOptType" :label="$t('table.optType')" prop="optType">
          <el-checkbox-group v-model="optTypeList" @change="handleTypeChange">
            <el-checkbox v-for="(item, index) in optTypeOptions" :key="index" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="tempL.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap.updateOpType"
      :visible.sync="dialogTypeFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="updateRules"
        :model="tempL"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.optType')" prop="optType">
          <el-checkbox-group v-model="optTypeList" @change="handleTypeChange">
            <el-checkbox v-for="(item, index) in optTypeOptions" :key="index" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </FormItem>
      </Form>
    </el-dialog>

    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :edit-valid-func="getLabelLibGroupByName"
      :add-func="createLabelLibGroup"
      @addEnd="createGroupAddEnd"
    />

    <import-table-dlg
      ref="labelImportTable"
      :exits-list-data="labelElgData"
      :elg-title="textMap.import"
      :group-root-name="$t('pages.labelLibrary')"
      :search-info-name="$t('table.labelName')"
      :confirm-button-name="$t('pages.addLabel')"
      :group-title="$t('pages.labelGroup')"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="getGroupTreeNode"
      :create-group="createLabelLibGroup"
      :update-group="updateLabelLibGroup"
      :delete-group="deleteLabelLibGroup"
      :count-by-group="countChildByGroupId"
      :get-group-by-name="getLabelLibGroupByName"
      :delete="deleteLabelLib"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteLabelLibGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleLabelCreate"
      :get-list-by-group-ids="getlabelLibraryByGroupIds"
      :opt-type-options="optTypeOptions"
      :show-opt="true"
      @submitEnd="getNeedAddIds"
      @elgCancelAfter="elgCancelAfter"
      @changeGroupAfter="changeGroupAfter"
    />
  </div>
</template>

<script>
import {
  getGroupTreeNode, createLabelLibGroup, updateLabelLibGroup, deleteLabelLibGroup, deleteGroupAndData,
  moveGroupToOther, getLabelLibGroupByName, countChildByGroupId, getLabelLibPage, createLabelLib,
  updateLabelLib, deleteLabelLib, getlabelLibraryByGroupIds, getLabelLibraryByIds, countByLabel
} from '@/api/system/baseData/labelLibrary'
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/behaviorManage/hardware/manualLabel'
import { selectable } from '@/utils'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'
import { getList } from '@/api/system/baseData/labelGradeLibrary'

export default {
  name: 'ManualLabelDlg',
  components: { ImportTable, ImportTableDlg, EditGroupDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        gradeId: null,
        labelIds: [],
        labelInfoList: [],
        allowChangeGrade: 1
      },
      tempL: {},
      defaultTempL: {
        id: undefined,
        name: '',
        groupId: undefined,
        optType: null
      },
      colModel: [
        { prop: 'name', label: 'labelName', width: '150' },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.groupFormatter },
        { prop: 'optType', label: 'optType', width: '150', formatter: this.optTypeFormatter },
        { prop: 'remark', label: 'remark', width: '150' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleLabelUpdate }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'labelName', width: '150', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.groupFormatter },
        { prop: 'remark', label: 'remark', width: '200' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleImportUpdate }
          ]
        }
      ],
      rules: {
      },
      tempLRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_labelName') },
          { validator: this.nameValidator, trigger: ['blur', 'change'] }
        ],
        groupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: ['blur', 'change'] }],
        optType: [
          { required: true, validator: this.optValidator, trigger: ['blur', 'change'] }
        ]
      },
      updateRules: {
        optType: [
          { required: true, validator: this.optValidator, trigger: ['blur', 'change'] }
        ]
      },
      labelDeleteable: false,
      labelGradeTree: [],
      tempLabelElgData: [],
      treeSelectNode: [],
      addGroupAble: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.labelInfo'), 'update'),
        create: this.i18nConcatText(this.$t('pages.labelInfo'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.labelGroup'), 'delete'),
        updateOpType: this.i18nConcatText(this.$t('table.optType'), 'update'),
        import: this.i18nConcatText(this.$t('table.labelContent'), 'import')
      },
      showOptType: true,
      dialogStatus: 'create',
      dialogFormVisible: false,
      dialogTypeFormVisible: false,
      deleteable: false,
      labelElgData: [],
      createFlag: false,
      updateFlag: false,
      optTypeOptions: [{ value: 1, label: this.$t('pages.insert') }, { value: 2, label: this.$t('pages.delete') }, { value: 4, label: this.$t('text.disable') }],
      optTypeList: [],
      needIds: [],
      importData: [],
      checkedKeys: [],
      exsitLabelElgData: [],
      // 导入信息库左侧选择的分组id
      labelGroupSearchId: undefined
    }
  },
  computed: {

  },
  watch: {
    labelElgData(val) {
      //  设置序号
      (val || []).forEach(item => {
        item.groupName = this.getGroupNameByDataId(this.treeSelectNode, item.groupId)
      });
      this.handleLabelSearch()
    },
    needIds(val) {
      if (val.length > 0) {
        getLabelLibraryByIds({ ids: val.join(',') }).then(res => {
          const oldLabelElgData = this.labelElgData
          this.labelElgData = res.data || []
          this.labelElgData.forEach(item => {
            if (oldLabelElgData && oldLabelElgData.length > 0) {
              oldLabelElgData.forEach(old => {
                if (item.id == old.id) {
                  item.optType = old.optType
                }
                //  else {
                //   item.optType = this.getSum(this.optTypeList)
                // }
              })
            }
            this.importData.forEach(importItem => {
              if (item.id == importItem.id) {
                item.optType = this.getSum(this.optTypeList)
              }
            })
          })
        })
      }
    },
    '$store.state.commonData.notice.updateFileGrade'() {
      this.loadGradeList()
    }
  },
  created() {
    this.loadGroupTree()
    this.loadGradeList()
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
    this.loadGroupTree()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    getGroupTreeNode,
    createLabelLibGroup,
    updateLabelLibGroup,
    deleteLabelLibGroup,
    deleteGroupAndData,
    moveGroupToOther,
    getLabelLibGroupByName,
    countChildByGroupId,
    getLabelLibPage,
    createLabelLib,
    updateLabelLib,
    deleteLabelLib,
    getlabelLibraryByGroupIds,
    handleDrag() {},
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
    },
    loadGroupTree: function() {
      return getGroupTreeNode().then(res => {
        this.treeSelectNode = res.data
      })
    },
    loadGradeList() {
      getList().then(res => {
        const data = res.data
        if (data) {
          data.forEach(item => {
            const index = item.content.indexOf('zh:')
            item.label = index > -1 ? item.content.slice(index + 3, item.content.length) : item.content
          })
          this.labelGradeTree = data
        }
      })
    },
    closed() {
      this.resetTemp()
      this.resetTempL()
    },
    gridTable() {
      return this.$refs['labelInfoGrid']
    },
    checkSoftwareSelectionChangeEnd(rowDatas) {
      this.labelDeleteable = rowDatas && rowDatas.length > 0
    },
    handleImportLabel() {
      this.$refs.hardAssetTable.show()
    },
    buttonFormatter(row) {
      return !this.formable
    },
    treeSelectChange(data, node, vm) {
      const checkNode = Array.isArray(node) ? node[0] : node
      if (checkNode) {
        this.temp.gradeId = checkNode.id
      } else {
        this.temp.gradeId = null
      }
    },
    //  新增，修改弹窗
    handleLabelCreate(selectedGroupId, flag) {
      this.resetTempL()
      this.dialogStatus = 'create'
      this.createFlag = flag || false
      this.addGroupAble = !flag
      if (flag) {
        this.showOptType = false
      }
      // 如果传参没有selectedGroupId，则获取左侧选择的分组id
      this.tempL.groupId = selectedGroupId ? selectedGroupId + '' : this.labelGroupSearchId
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleLabelUpdate(row) {
      this.resetTempL()
      this.updateFlag = false
      this.dialogStatus = 'update'
      this.tempL = Object.assign({}, row)
      this.tempL.groupId = this.tempL.groupId ? this.tempL.groupId + '' : '';
      this.optTypeList = this.numToList(this.tempL.optType, 4)
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImportUpdate(row) {
      this.resetTempL()
      this.updateFlag = true
      this.dialogStatus = 'update'
      this.addGroupAble = false
      this.tempL = Object.assign({}, row)
      this.tempL.groupId = this.tempL.groupId ? this.tempL.groupId + '' : '';
      this.dialogFormVisible = true
      this.showOptType = false
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete() {
      const toDeleteIds = this.gridTable().getSelectedIds() || []
      this.labelElgData = this.gridTable().deleteTableData(this.labelElgData, toDeleteIds)
      const labelIdList = this.temp.labelIds.filter(id => toDeleteIds.indexOf(id) == -1)
      this.temp.labelIds = labelIdList
    },
    handleLabelImport() {
      this.addGroupAble = false
      this.$refs['labelImportTable'].show();
    },
    handleLabelSearch(searchInfo) {
      searchInfo = searchInfo || ''
      if (searchInfo === '') {
        this.tempLabelElgData = this.labelElgData
      } else {
        //  条件查询
        this.tempLabelElgData = this.labelElgData.filter(item => {
          return item.name && item.name.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1
        })
      }
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.labelElgData = []
      this.checkedKeys = []
    },
    resetTempL() {
      this.tempL = Object.assign({}, this.defaultTempL)
      this.optTypeList = []
      this.showOptType = true
    },
    labelSelectable(row, index) {
      return selectable(row, index);
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$nextTick(() => {
        this.$refs['labelInfoGrid'] && this.$refs['labelInfoGrid'].clearSearchInfo()
      })
    },
    async handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.defaultTemp, JSON.parse(JSON.stringify(row))) // copy obj
      this.checkedKeys.splice(0, this.checkedKeys.length, row.gradeId)
      if (!this.formable) {
        await getList().then(res => {
          const data = res.data
          if (data) {
            data.forEach(item => {
              const index = item.content.indexOf('zh:')
              item.label = index > -1 ? item.content.slice(index + 3, item.content.length) : item.content
            })
            this.labelGradeTree = data
          }
        })
      }
      this.getLabelListByIds(row.labelIds)
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.$refs['labelInfoGrid'] && this.$refs['labelInfoGrid'].clearSearchInfo()
      })
    },
    getLabelListByIds(ids) {
      ids = ids || []
      if (ids.length === 0) {
        this.labelElgData = []
        return;
      }
      getLabelLibraryByIds({ ids: ids.join(',') }).then(res => {
        const labelData = res.data || []
        const exsitId = labelData.map(item => item.id)
        this.labelElgData = this.labelElgData.filter(data => exsitId.indexOf(data.id) > -1)
        if (this.labelElgData && this.labelElgData.length > 0) {
          const newLabelList = []
          this.labelElgData.forEach(label => {
            labelData.forEach(item => {
              if (label.id == item.id) {
                label.name = item.name
                label.gruopId = item.groupId
                label.groupName = item.groupName
                newLabelList.push(label)
              }
            })
          })
          this.labelElgData = newLabelList
        }
      })
    },
    formatRowData(rowData) {
      if (rowData.labelIds && rowData.labelIds.length > 0) {
        getLabelLibraryByIds({ ids: rowData.labelIds.join(',') }).then(res => {
          const labelData = res.data || []
          if (rowData.labelInfoList && rowData.labelInfoList.length > 0) {
            const newLabelList = []
            rowData.labelInfoList.forEach(label => {
              labelData.forEach(item => {
                if (label.id == item.id) {
                  label.name = item.name
                  label.gruopId = item.groupId
                  label.groupName = item.groupName
                  newLabelList.push(label)
                }
              })
            })
            rowData.labelInfoList = newLabelList
          }
        })
      }
      if (rowData && rowData.labelInfoList && rowData.labelInfoList.length > 0) {
        this.labelElgData = rowData.labelInfoList
      }
    },
    formatFormData(formData) {
      if (this.labelElgData && this.labelElgData.length > 0) {
        formData.labelInfoList = this.labelElgData
      }
      formData.labelIds = this.$refs['labelInfoGrid'].getIdsByList(this.labelElgData) || []
    },
    validateFormData(formData) {
      return true
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // this.tempL.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          createLabelLib(this.tempL).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.isImportElg(respond.data, 'create')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            // 通知应用到该配置的标签树重新请求，更新数据
            this.$store.dispatch('commonData/changeNotice', 'updateLabelInfo')
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.tempL)
          // tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          updateLabelLib(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            respond.data.optType = tempData.optType
            this.isImportElg(respond.data, 'update')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            // 通知应用到该配置的标签树重新请求，更新数据
            this.$store.dispatch('commonData/changeNotice', 'updateLabelInfo')
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createFlag) {
          this.$refs['labelImportTable'].refreshTableData()
        } else {
          this.labelElgData.push(data)
        }
        this.createFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$refs['labelImportTable'].refreshTableData()
        } else {
          for (let i = 0; i < this.labelElgData.length; i++) {
            if (this.labelElgData[i].id === data.id) {
              this.labelElgData.splice(i, 1)
              this.labelElgData.push(data)
              break
            }
          }
        }
        this.updateFlag = false
      }
    },
    //  以下方法是点击导入按钮的弹窗方法
    //  导入方法的row-data-api
    getInfoList(option) {
      this.exsitLabelElgData = this.labelElgData
      const searchQuery = Object.assign({}, this.query, option)
      this.labelGroupSearchId = searchQuery.groupId
      return getLabelLibPage(searchQuery)
    },
    optTypeFormatter(row, data) {
      let desc = ''
      const dataList = this.numToList(data, 4)
      for (let i = 0, size = this.optTypeOptions.length; i < size; i++) {
        const value = this.optTypeOptions[i].value
        if (dataList.indexOf(value) > -1) {
          desc += this.optTypeOptions[i].label + ';'
        }
      }
      return desc
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    handleTypeChange() {
      if (!this.optTypeList || this.optTypeList.length === 0) {
        this.tempL.optType = 0
      } else {
        this.tempL.optType = this.getSum(this.optTypeList)
      }
    },
    getNeedAddIds(data, list, opt) {
      this.optTypeList = opt
      this.needIds = data
      this.importData = list
    },
    elgCancelAfter() {
      this.loadGroupTree()
      this.getLabelLibraryByIds();
    },
    //  弹窗分组的数据发送变化时
    changeGroupAfter() {
      this.loadGroupTree()
      this.getLabelLibraryByIds();
      // this.changeGroupNotify()
    },
    dlgClosed() {
      this.getLabelLibraryByIds();
    },
    getLabelLibraryByIds() {
      const ids = this.$refs['labelInfoGrid'].getIdsByList(this.labelElgData) || []
      if (ids.length === 0) {
        this.labelElgData = []
        return;
      }
      getLabelLibraryByIds({ ids: ids.join(',') }).then(res => {
        const labelData = res.data || []
        if (this.exsitLabelElgData && this.exsitLabelElgData.length > 0) {
          const newLabelList = []
          this.exsitLabelElgData.forEach(label => {
            labelData.forEach(item => {
              if (label.id == item.id) {
                label.name = item.name
                label.gruopId = item.groupId
                label.groupName = item.groupName
                newLabelList.push(label)
              }
            })
          })
          this.labelElgData = newLabelList
        }
      })
    },
    handleInput(value) {
      // 正则表达式匹配所有标点符号（这里使用了 Unicode 属性 \p{P} 来匹配标点）
      const punctuationRegex = /[|,'，]/g
      if (punctuationRegex.test(value)) {
        // 如果输入不是纯标点符号，则清除非标点符号的内容
        this.tempL.name = value.replace(/[|,'，]/g, '')
      } else {
        // 如果是纯标点符号，则直接赋值（可选，根据需求决定是否需要）
        this.tempL.name = value;
      }
    },
    handelChange(value) {
      // 正则表达式匹配所有标点符号（这里使用了 Unicode 属性 \p{P} 来匹配标点）
      const punctuationRegex = /[|,'，]/g
      if (punctuationRegex.test(value)) {
        // 如果输入不是纯标点符号，则清除非标点符号的内容
        this.tempL.name = value.replace(/[|,'，]/g, '')
      } else {
        // 如果是纯标点符号，则直接赋值（可选，根据需求决定是否需要）
        this.tempL.name = value;
      }
    },
    nameValidator(rule, value, callback) {
      countByLabel({ name: value }).then(respond => {
        const data = respond.data
        if (data && data.id !== this.tempL.id) {
          callback(new Error(this.$t('pages.labelLibrary_confirmMsg')))
        } else {
          callback()
        }
      })
    },
    optValidator(rule, value, callback) {
      if (this.optTypeList && this.optTypeList.length > 0) {
        callback()
      } else {
        callback(new Error(this.$t('pages.valiOpTypeMsg')))
      }
    },
    //  添加分组事件
    handleTypeCreate() {
      this.$refs['createGroupDlg'].handleCreate();
    },
    //  新分组添加完成后事件
    createGroupAddEnd(row) {
      this.loadGroupTree().then(() => {
        this.tempL.groupId = row.id + ''
      })
      this.$store.dispatch('commonData/changeNotice', 'updateLabelInfo')
    }
  }
}
</script>
