<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button style="margin-left: 10px;" icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <label-permission-dlg
      ref="stgDlg"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      :label-grade-tree="labelGradeTree"
      @submitEnd="submitEnd"
      @updateData="updateData"
    />
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importSoftwareLimitStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import axios from 'axios'
import LabelPermissionDlg from './editDlg'
import { getStrategyPage, deleteStrategy } from '@/api/behaviorManage/hardware/labelPermission'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'
import { getList } from '@/api/system/baseData/labelGradeLibrary'

export default {
  name: 'LabelPermissionControl',
  components: { LabelPermissionDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 280,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'limitType', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      limitTypeOptions: {
        1: this.$t('pages.interfaceMsg1'),
        2: this.$t('pages.interfaceMsg2')
      },
      violationOptions: [
        { value: 1, label: this.$t('pages.netInterfaceMsg3') }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      appQuery: { // 查询条件
        page: 1,
        searchInfo: '',
        typeId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      submitting: false,
      source: null,
      checkedEntityNode: {},
      lossTypeOptions: [],
      lossTypeAll: -1,
      labelGradeTree: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
    '$store.state.commonData.notice.updateFileGrade'() {
      this.loadGradeList()
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.getSensitiveLossType()
    this.loadGradeList()
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  methods: {
    loadGradeList() {
      getList().then(res => {
        const data = res.data
        if (data) {
          data.forEach(item => {
            const index = item.content.indexOf('zh:')
            item.label = index > -1 ? item.content.slice(index + 3, item.content.length) : item.content
          })
          this.labelGradeTree = data
        }
      })
    },
    connectionSource() {
      return axios.CancelToken.source()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    refresh() {
      return refreshPage(this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    getSensitiveLossType() {
      const supportList = [0, 2, 3, 7, 9, 13, 15, 18, 20, 29, 32]
      getSensitiveLossType({ type: 2 }).then(res => {
        const data = res.data.filter(item => supportList.indexOf(item.lossType) > -1).map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
        this.lossTypeOptions = [{ label: this.$t('pages.lossTypeOptions'), value: this.lossTypeAll }, ...data]
      })
    },
    strategyFormatter: function(row, data) {
      // 标签信息配置
      const labelInfoConfig = this.$t('pages.labelInfoConfig')
      const labelInfoArr = []
      row.labelInfoList.forEach(info => {
        const labelName = []
        const labelIds = info.labelIds
        if (labelIds && labelIds.length > 0) {
          labelIds.forEach(labelId => {
            if (row.labelNameMap[labelId]) {
              labelName.push(row.labelNameMap[labelId])
            }
          })
        }
        const detecteRule = info.detecteRule
        const executeRule = info.executeRule
        const hitLabelContentCount = info.hitLabelContentCount
        const responseRule = info.responseRule
        const labelInfoAlarm = info.labelInfoAlarm
        let labelInfo = '{ ' + this.$t('table.labelName') + '：' + labelName.join(',') + '，' +
        this.$t('pages.detectionRules') + '：' + (detecteRule == 0 ? this.$t('pages.allMatch1') : this.$t('pages.labelPermissionConfig', { n: hitLabelContentCount })) + '，' +
        this.$t('pages.executionRules') + '：' + (executeRule == 0 ? this.$t('pages.executeInRule') : this.$t('pages.executeOutRule'))
        if (responseRule.length > 0) {
          labelInfo += '，' + this.$t('pages.responseRule') + '：' + (responseRule.indexOf(12) > -1 ? `${this.$t('pages.burnMsg5') + '、' + this.$t('pages.burnMsg7')}` : responseRule.indexOf(3) > -1 ? this.$t('pages.burnMsg5') : '')
          if (labelInfoAlarm) {
            labelInfo += '、' + this.$t('pages.alarmOfOutgoing') + ' }'
          } else {
            labelInfo += ' }'
          }
        } else {
          labelInfo += '，' + this.$t('pages.responseRule') + '：'
          if (labelInfoAlarm) {
            labelInfo += this.$t('pages.alarmOfOutgoing') + ' }'
          } else {
            labelInfo += ' }'
          }
        }
        labelInfoArr.push(labelInfo)
      })
      // 文档等级配置
      const gradeConfig = this.$t('pages.labelGradeConfig')
      const gradeArray = []
      if (row.gradeId) {
        let gradeName = ''
        const gradeNode = this.labelGradeTree.filter(item => item.id == row.gradeId)[0]
        if (gradeNode) {
          gradeName = gradeNode.label
        }
        gradeArray.push(`${this.$t('pages.lowGradeConfig')}：` + gradeName)
      }
      if (row.gradeResList && row.gradeResList.length > 0) {
        let gradeResDesc = row.gradeResList.indexOf(12) > -1 ? `${this.$t('pages.burnMsg5') + '、' + this.$t('pages.burnMsg7')}` : row.gradeResList.indexOf(3) > -1 ? this.$t('pages.burnMsg5') : null
        if (row.gradeAlarm) {
          gradeResDesc += '、' + this.$t('pages.alarmOfOutgoing')
        }
        gradeArray.push(`${this.$t('pages.serverAlarmResponseRule')}：` + gradeResDesc)
      } else {
        if (row.gradeAlarm) {
          gradeArray.push(`${this.$t('pages.serverAlarmResponseRule')}：` + this.$t('pages.alarmOfOutgoing'))
        }
      }
      // 高级配置
      const highConfig = this.$t('table.highConfig')
      const highConfigArray = []
      const emptyLabelFile = row.emptyLabelFile == 1 ? this.$t('pages.burnMsg5') : row.emptyLabelFile == 2 ? `${this.$t('pages.burnMsg5') + '、' + this.$t('pages.burnMsg7')}` : ''
      if (emptyLabelFile) {
        if (row.dealEmptyLabelFileAlarm) {
          highConfigArray.push(`${this.$t('pages.noLabelFile')}：` + emptyLabelFile + '、' + this.$t('pages.alarmOfOutgoing'))
        } else {
          highConfigArray.push(`${this.$t('pages.noLabelFile')}：` + emptyLabelFile)
        }
      } else {
        if (row.dealEmptyLabelFileAlarm) {
          highConfigArray.push(`${this.$t('pages.noLabelFile')}：` + this.$t('pages.alarmOfOutgoing'))
        }
      }
      const dealUnFileType = row.dealUnFileType == 1 ? this.$t('pages.burnMsg5') : row.dealUnFileType == 2 ? `${this.$t('pages.burnMsg5') + '、' + this.$t('pages.burnMsg7')}` : ''
      if (row.dealUnFileType) {
        if (row.dealUnFileTypeAlarm) {
          highConfigArray.push(`${this.$t('pages.labelFile')}：` + dealUnFileType + '、' + this.$t('pages.alarmOfOutgoing'))
        } else {
          highConfigArray.push(`${this.$t('pages.labelFile')}：` + dealUnFileType)
        }
      } else {
        if (row.dealUnFileTypeAlarm) {
          highConfigArray.push(`${this.$t('pages.labelFile')}：` + this.$t('pages.alarmOfOutgoing'))
        }
      }
      const labelFileError = row.dealLabelFileError == 1 ? this.$t('pages.burnMsg5') : row.dealLabelFileError == 2 ? `${this.$t('pages.burnMsg5') + '、' + this.$t('pages.burnMsg7')}` : ''
      if (row.dealLabelFileError) {
        if (row.dealLabelFileErrorAlarm) {
          highConfigArray.push(`${this.$t('pages.labelErrorFile')}：` + labelFileError + '、' + this.$t('pages.alarmOfOutgoing'))
        } else {
          highConfigArray.push(`${this.$t('pages.labelErrorFile')}：` + labelFileError)
        }
      } else {
        if (row.dealLabelFileErrorAlarm) {
          highConfigArray.push(`${this.$t('pages.labelErrorFile')}：` + this.$t('pages.alarmOfOutgoing'))
        }
      }
      const recoreLog = row.recordLog == 1 ? this.$t('pages.labelPermissionMsgTips3') : ''
      if (row.recordLog) {
        if (row.limitFileSize) {
          highConfigArray.push(`${this.$t('pages.labelPermissionMsgTips2')}：` + recoreLog + '、' + this.$t('pages.ftpControl_text20', { size: row.limitFileSize }))
        } else {
          highConfigArray.push(`${this.$t('pages.labelPermissionMsgTips2')}：` + recoreLog)
        }
      }
      // 泄露方式
      let lossTypeText = ''
      if (row.lossTypeFilter && row.lossTypeFilter.length > 0) {
        const lossTypeNames = []
        let all = row.lossTypeFilter.length == 0 || row.lossTypeFilter.length == this.lossTypeOptions.length
        for (let i = 0, size = this.lossTypeOptions.length; i < size; i++) {
          const id = this.lossTypeOptions[i].value
          if (row.lossTypeFilter.indexOf(id) >= 0) {
            lossTypeNames.push(this.lossTypeOptions[i].label)
          } else {
            all = false
          }
        }
        lossTypeText = (all ? this.$t('pages.lossTypeOptions') : lossTypeNames.join(','))
      }
      // 触发响应规则
      let ruleName = ''
      if (row.ruleId) {
        const ruleOption = this.$store.getters.alarmRules.map(item => { return { label: item.name, id: item.id } })
        for (const option of ruleOption) {
          if (option.id == row.ruleId) {
            ruleName = option.label
          }
        }
      }
      return `${labelInfoConfig}：[ ${labelInfoArr.join('，')} ]` + '；' +
        `${gradeConfig}：{ ${gradeArray.join('，')} }` + '；' +
        `${highConfig}：{ ${highConfigArray.join('，')} }` + '；' +
        (lossTypeText !== '' ? this.$t('pages.lossType') + '：' + lossTypeText : '') + '；' +
        (ruleName !== '' ? this.$t('table.violationName') + '：' + ruleName : '')
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), strategyType: 'softwareLimitStrategy' })
    },
    importSuccess() {
      this.handleFilter()
      this.$store.dispatch('commonData/setAlarmRules')
    },
    updateData() {
      this.handleFilter()
    }
  }
}
</script>
