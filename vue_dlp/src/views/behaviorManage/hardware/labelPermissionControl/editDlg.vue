<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('text.stgTitleSuffix', { title: $t('pages.labelPermissionController') })"
      :active-able="activeAble"
      :stg-code="280"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <div style="padding-left: 30px;">
          <el-tabs ref="tabs" v-model="activeName" type="card" :before-leave="changeTab">
            <el-tab-pane :label="$t('pages.labelInfoConfig')" style="padding-top: 0" name="label">
              <div style="height:200px;">
                <import-table
                  ref="labelInfoGrid"
                  :search-info-prompt-message="$t('table.labelName')"
                  :delete-disabled="!deleteable"
                  :col-model="colModel"
                  :row-datas="tempLabelElgData"
                  :row-no-label="$t('table.keyId')"
                  :formable="formable"
                  :selectable="labelSelectable"
                  :show-import="false"
                  :handle-create="handleLabelImport"
                  :handle-delete="handleDelete"
                  :handle-search="handleLabelSearch"
                  @selectionChangeEnd="(rowDatas) => deleteable = rowDatas.length > 0"
                />
              </div>
            </el-tab-pane>
            <el-tab-pane :label="$t('pages.labelGradeConfig')" style="padding-top: 0" name="grade">
              <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
              <FormItem :label="$t('pages.lowGradeConfig')">
                <tree-select
                  :checked-keys="checkedKeys"
                  node-key="id"
                  :data="labelGradeTree"
                  :width="200"
                  :disabled="!formable"
                  clearable
                  style="width: 200px;"
                  @change="handleCheckChange"
                />
              </FormItem>
              <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
              <label style="margin-left: 10px">{{ $t('pages.labelPermissionMsg') }}
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <div slot="content">{{ $t('pages.gradeTips') }}</div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </label>
              <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
              <el-checkbox-group v-model="temp.gradeResList" style="display: inline-block;margin-left: 10px" :disabled="!formable" @change="handleResTypeChange">
                <el-checkbox :label="3">{{ $t('pages.burnMsg5') }}</el-checkbox>
                <el-checkbox :disabled="temp.gradeResList.indexOf(3) == -1" :label="12">{{ $t('pages.burnMsg7') }}</el-checkbox>
              </el-checkbox-group>
              <el-checkbox v-model="temp.gradeAlarm" :true-label="1" :false-label="0" style="margin-left: 30px">{{ $t('pages.alarmOfOutgoing') }}</el-checkbox>
            </el-tab-pane>
            <el-tab-pane :label="$t('table.highConfig')" style="padding-top: 8px" name="highConfig">
              <FormItem :label="$t('pages.noLabelFile')" label-width="120px">
                <el-checkbox-group v-model="emptyLabelFileList" style="display: inline-block" :disabled="!formable" @change="handleEmptyLabelFileChange">
                  <el-checkbox :label="1">{{ $t('pages.burnMsg5') }}</el-checkbox>
                  <el-checkbox :label="2" :disabled="emptyLabelFileList.indexOf(1) == -1">{{ $t('pages.burnMsg7') }}</el-checkbox>
                </el-checkbox-group>
              </FormItem>
              <FormItem label="" label-width="120px">
                <el-checkbox v-model="temp.dealEmptyLabelFileAlarm" :true-label="1" :false-label="0" :disabled="!formable">{{ $t('pages.alarmOfOutgoing') }}</el-checkbox>
              </FormItem>
              <FormItem :label="$t('pages.labelFile')" label-width="120px">
                <el-checkbox-group v-model="dealUnFileTypeList" style="display: inline-block" :disabled="!formable" @change="handleDealUnFileTypeChange">
                  <el-checkbox :label="1">{{ $t('pages.burnMsg5') }}</el-checkbox>
                  <el-checkbox :label="2" :disabled="dealUnFileTypeList.indexOf(1) == -1">{{ $t('pages.burnMsg7') }}</el-checkbox>
                </el-checkbox-group>
              </FormItem>
              <FormItem label="" label-width="120px">
                <el-checkbox v-model="temp.dealUnFileTypeAlarm" :true-label="1" :false-label="0" :disabled="!formable">{{ $t('pages.alarmOfOutgoing') }}</el-checkbox>
              </FormItem>
              <FormItem :label="$t('pages.labelErrorFile')" label-width="120px">
                <el-checkbox-group v-model="dealLabelFileErrorList" style="display: inline-block" :disabled="!formable" @change="handleDealLabelFileErrorChange">
                  <el-checkbox :label="1">{{ $t('pages.burnMsg5') }}</el-checkbox>
                  <el-checkbox :label="2" :disabled="dealLabelFileErrorList.indexOf(1) == -1">{{ $t('pages.burnMsg7') }}</el-checkbox>
                </el-checkbox-group>
              </FormItem>
              <FormItem label="" label-width="120px">
                <el-checkbox v-model="temp.dealLabelFileErrorAlarm" :true-label="1" :false-label="0" :disabled="!formable">{{ $t('pages.alarmOfOutgoing') }}</el-checkbox>
              </FormItem>
              <FormItem :label="$t('pages.labelPermissionMsgTips2')" label-width="140px" style="margin-left: -20px">
                <el-checkbox v-model="temp.recordLog" :true-label="1" :false-label="0" :disabled="!formable" @change="recordChange">{{ $t('pages.labelPermissionMsgTips3') }}</el-checkbox>
              </FormItem>
              <FormItem prop="limitFileSize" style="margin-left: 20px">
                <el-checkbox v-model="temp.backup" :disabled="!formable || !temp.recordLog" :true-label="1" :false-label="0" @change="backupchange">
                  <i18n style="font-weight: 500;" path="pages.ftpControl_text20">
                    <el-input-number slot="size" v-model="temp.limitFileSize" :disabled="!formable || temp.backup === 0" :controls="false" step-strictly :min="1" :max="10240" size="mini" style="width: 80px;"/>
                  </i18n>
                  <el-tooltip effect="dark" placement="bottom">
                    <div slot="content" style="width: 480px;">{{ $t('pages.labelPermissionMsgTips') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-checkbox>
                <el-button :disabled="!formable || !temp.recordLog" style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
              </FormItem>
            </el-tab-pane>
          </el-tabs>
          <FormItem :label="$t('pages.lossType')" style="margin-left: -30px; margin-top: 10px" prop="lossType">
            <el-select v-model="temp.lossTypeFilter" filterable multiple size="small" :placeholder="$t('pages.group_text5')" :disabled="!formable" @change="lossTypeChange">
              <el-option v-for="item in lossTypeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </FormItem>
          <span style="font-weight: bold;">{{ $t('table.violationName') }}</span>
          <div style="display: inline-block;width: calc(100% - 200px);">
            <ResponseContent
              ref="resContent"
              :select-style="{ 'margin-top': '10px' }"
              :show-select="true"
              :rule-type-name="$t('pages.alarmSetup')"
              :editable="formable"
              read-only
              :prop-check-rule="true"
              :check-empty-rule="false"
              :show-check-rule="false"
              :prop-rule-id="propRuleId"
              @getRuleId="getRuleId"
            />
          </div>
        </div>
      </template>
    </stg-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogLabelFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="tempLRules"
        :model="tempL"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('table.labelName')" prop="name">
          <el-input v-model="tempL.name" v-trim :maxlength="60" @input="handleInput" @change="handelChange"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-select v-model="tempL.groupId" :placeholder="$t('text.select')">
            <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.dataId"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('text.remark')">
          <el-input v-model="tempL.remark" type="textarea" rows="3" :maxlength="100" show-word-limit></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogLabelFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap.updatePermission"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="configRules"
        :model="tempC"
        label-position="right"
        label-width="30px"
        style="width: 500px; margin-left:20px;"
      >
        <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
        <FormItem>
          <el-radio-group v-model="tempC.detecteRule" style="margin-left: 0">
            <el-radio :label="0">{{ $t('pages.allMatch1') }}</el-radio>
            <el-radio :label="1">
              <i18n path="pages.labelPermissionConfig">
                <span slot="n">
                  <el-input-number
                    v-model="tempC.hitLabelContentCount"
                    :controls="false"
                    :step="1"
                    :precision="0"
                    :min="1"
                    :max="999"
                    :disabled="tempC.detecteRule === 0"
                    step-strictly
                    style="width: 60px; margin: 0 4px"
                  />
                </span>
              </i18n>
              <div v-if="tempC.detecteRule === 1 && !tempC.hitLabelContentCount" style="position: absolute; left: 100px" class="error_style">不允许为空</div>
            </el-radio>
          </el-radio-group>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <FormItem>
          <el-radio-group v-model="tempC.executeRule" style="margin-left: 0">
            <el-radio :label="0">{{ $t('pages.executeInRule') }}</el-radio>
            <br>
            <el-radio :label="1">{{ $t('pages.executeOutRule') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
        <FormItem>
          <el-checkbox-group v-model="tempC.responseRule" style="display: inline-block" @change="handleLabelTypeChange">
            <el-checkbox :label="3">{{ $t('pages.burnMsg5') }}</el-checkbox>
            <el-checkbox :disabled="tempC.responseRule.indexOf(3) == -1" :label="12">{{ $t('pages.burnMsg7') }}</el-checkbox>
          </el-checkbox-group>
          <el-checkbox v-model="tempC.labelInfoAlarm" :true-label="1" :false-label="0" style="margin-left: 30px">{{ $t('pages.alarmOfOutgoing') }}</el-checkbox>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="handleUpdatePermission">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-table-dlg
      ref="labelImportTable"
      :exits-list-data="labelElgData"
      :elg-title="textMap.create"
      :group-root-name="$t('pages.labelLibrary')"
      :search-info-name="$t('table.labelName')"
      :confirm-button-name="$t('pages.addLabel')"
      :group-title="$t('pages.labelGroup')"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="getGroupTreeNode"
      :create-group="createLabelLibGroup"
      :update-group="updateLabelLibGroup"
      :delete-group="deleteLabelLibGroup"
      :count-by-group="countChildByGroupId"
      :get-group-by-name="getLabelLibGroupByName"
      :delete="deleteLabelLib"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteLabelLibGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleLabelCreate"
      :get-list-by-group-ids="getlabelLibraryByGroupIds"
      :show-rule="true"
      :group-tree-height="360"
      :grid-table-height="330"
      @submitEnd="getNeedAddIds"
      @elgCancelAfter="elgCancelAfter"
      @changeGroupAfter="changeGroupAfter"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
  </div>
</template>

<script>
import { getList } from '@/api/system/baseData/labelGradeLibrary'
import {
  getGroupTreeNode, createLabelLibGroup, updateLabelLibGroup, deleteLabelLibGroup, deleteGroupAndData,
  moveGroupToOther, getLabelLibGroupByName, countChildByGroupId, getLabelLibPage, createLabelLib,
  updateLabelLib, deleteLabelLib, getlabelLibraryByGroupIds, countByLabel, getLabelLibraryByIds
} from '@/api/system/baseData/labelLibrary'
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/behaviorManage/hardware/labelPermission'
import { selectable } from '@/utils'
import ImportTable from '@/views/system/baseData/groupImportList/importTable'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'

export default {
  name: 'LabelPermissionDlg',
  components: { ImportTable, ImportTableDlg, ResponseContent, BackupRuleContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        gradeId: null,
        grade: null,
        labelInfoList: [],
        labelIds: [],
        gradeResList: [],
        gradeAlarm: 0,
        emptyLabelFile: 0,
        dealUnFileType: 0,
        dealLabelFileError: 0,
        dealUnFileTypeAlarm: 0,
        dealEmptyLabelFileAlarm: 0,
        dealLabelFileErrorAlarm: 0,
        labelResList: [],
        hitRule: 0,
        gradeHitRule: 0,
        lossTypeFilter: [],
        gradeLangKye: '',
        lossType: null,
        labelOutSendList: [],
        ruleId: undefined, // 响应规则id
        backup: 0,
        recordLog: 0,  // 带文档标签外发审计
        limitFileSize: 20,
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined // 备份过滤规则id
      },
      emptyLabelFileList: [],
      dealUnFileTypeList: [],
      dealLabelFileErrorList: [],
      lossTypeFilter: [],
      tempL: {},
      defaultTempL: {
        id: undefined,
        name: '',
        groupId: undefined,
        optType: null
      },
      tempC: {},
      defaultTempC: {
        id: undefined,
        detecteRule: 0,
        executeRule: 0,
        hitLabelContentCount: 0,
        responseRule: [],
        labelInfoAlarm: 0
      },
      colModel: [
        { prop: 'name', label: 'labelName', width: '150' },
        { prop: 'detecteRule', label: 'detectionRules', width: '150', formatter: this.ruleFormatter },
        { prop: 'executeRule', label: 'executeRule', width: '150', formatter: this.executeRuleFormatter },
        { prop: 'responseRule', label: 'respond', width: '150', formatter: this.responseRuleFormatter },
        // { prop: 'remark', label: 'remark', width: '150' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleLabelUpdate }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'labelName', width: '150', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', formatter: this.groupFormatter },
        { prop: 'remark', label: 'remark', width: '200' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleImportUpdate }
          ]
        }
      ],
      rules: {
        lossType: [{ required: true, validator: this.lossTypeValidator, trigger: ['blur', 'change'] }],
        responseRule: [{ required: true, validator: this.responseRuleValidator, trigger: ['blur', 'change'] }],
        limitFileSize: [{ validator: this.fileBackUpLimitValidator, trigger: 'blur' }]
      },
      tempLRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_labelName') },
          { validator: this.nameValidator, trigger: ['blur', 'change'] }
        ],
        groupId: [{ required: true, message: this.$t('pages.validaGroup'), trigger: ['blur', 'change'] }]
      },
      configRules: {
        responseRule: [{ required: true, validator: this.responseRuleValidator, trigger: ['blur', 'change'] }]
      },
      labelDeleteable: false,
      tempLabelElgData: [],
      treeSelectNode: [],
      textMap: {
        update: this.i18nConcatText(this.$t('pages.labelInfo'), 'update'),
        create: this.i18nConcatText(this.$t('pages.labelInfo'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.labelGroup'), 'delete'),
        updatePermission: this.i18nConcatText(this.$t('pages.labelPermission'), 'update')
      },
      labelResList: [],
      hitLabelContentCount: 1,
      labelInfoAlarm: 0,
      hitRule: null,
      labelDec: null,
      dialogStatus: 'create',
      dialogFormVisible: false,
      dialogLabelFormVisible: false,
      deleteable: false,
      labelElgData: [],
      importDataList: [],
      needIds: [],
      checkedKeys: [],
      activeName: 'label',
      lossTypeAll: -1,
      lossTypeOptions: [],
      propRuleId: undefined,
      disableRule: true,
      createFlag: false,
      updateFlag: false,
      oldHitLabelContentCount: null,
      labelGradeTree: [],
      exsitLabelElgData: [],
      // 导入信息库左侧选择的分组id
      labelGroupSearchId: undefined,
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined // 备份过滤规则id
    }
  },
  computed: {

  },
  watch: {
    labelElgData(val) {
      this.handleLabelSearch()
    },
    needIds(val) {
      if (val.length > 0 && this.importDataList.length > 0) {
        const name = this.importDataList.map(item => item.name).join(',')
        const data = { labelIds: this.importDataList.map(item => item.id), name, detecteRule: this.labelDec, executeRule: this.hitRule, responseRule: this.labelResList, hitLabelContentCount: this.hitLabelContentCount, labelInfoAlarm: this.labelInfoAlarm }
        this.labelElgData.push(data)
        this.labelElgData.forEach((item, index) => {
          if (item) {
            item.id = index + 1
          }
        })
      }
    },
    '$store.state.commonData.notice.updateFileGrade'() {
      this.loadGradeList()
    }
  },
  created() {
    this.loadGroupTree()
    this.temp = Object.assign({}, this.defaultTemp)
    this.resetTempC()
    this.getSensitiveLossType()
    this.loadGradeList()
  },
  activated() {
    this.loadGroupTree()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    getGroupTreeNode,
    createLabelLibGroup,
    updateLabelLibGroup,
    deleteLabelLibGroup,
    deleteGroupAndData,
    moveGroupToOther,
    getLabelLibGroupByName,
    countChildByGroupId,
    getLabelLibPage,
    createLabelLib,
    updateLabelLib,
    deleteLabelLib,
    getlabelLibraryByGroupIds,
    changeTab(activeName, oldActiveName) {
      this.activeName = activeName
    },
    loadGradeList() {
      getList().then(res => {
        const data = res.data
        if (data) {
          data.forEach(item => {
            const index = item.content.indexOf('zh:')
            item.label = index > -1 ? item.content.slice(index + 3, item.content.length) : item.content
          })
          this.labelGradeTree = data
        }
      })
    },
    handleDrag() {},
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
    },
    loadGroupTree: function() {
      return getGroupTreeNode().then(res => {
        this.treeSelectNode = res.data
      })
    },
    closed() {
      this.resetTemp()
      this.resetTempC()
    },
    gridTable() {
      return this.$refs['labelInfoGrid']
    },
    checkSoftwareSelectionChangeEnd(rowDatas) {
      this.labelDeleteable = rowDatas && rowDatas.length > 0
    },
    handleImportLabel() {
      this.$refs.hardAssetTable.show()
    },
    buttonFormatter(row) {
      return !this.formable
    },
    handleCheckChange(data, node, vm) {
      const checkNode = Array.isArray(node) ? node[0] : node
      if (checkNode) {
        this.temp.gradeId = checkNode.id
        this.temp.grade = checkNode.grade
        this.temp.gradeLangKye = checkNode.langKey
      } else {
        this.temp.gradeId = null
        this.temp.grade = null
        this.temp.gradeLangKye = ''
      }
    },
    //  新增，修改弹窗
    handleLabelCreate(selectedGroupId, flag) {
      this.resetTempL()
      this.dialogStatus = 'create'
      this.createFlag = true
      this.dialogLabelFormVisible = true
      // 如果传参没有selectedGroupId，则获取左侧选择的分组id
      this.tempL.groupId = selectedGroupId ? selectedGroupId + '' : this.labelGroupSearchId
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImportUpdate(row) {
      this.resetTempL()
      this.dialogStatus = 'update'
      this.updateFlag = true
      this.tempL = Object.assign({}, row)
      this.tempL.groupId = this.tempL.groupId ? this.tempL.groupId + '' : '';
      this.dialogLabelFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleLabelUpdate(row) {
      this.oldHitLabelContentCount = row.name.split(',').length
      this.tempC = Object.assign({}, row)
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete() {
      const toDeleteIds = this.gridTable().getSelectedIds() || []
      this.labelElgData = this.gridTable().deleteTableData(this.labelElgData, toDeleteIds)
      for (let i = 0; i < this.temp.labelInfoList.length; i++) {
        if (toDeleteIds.indexOf(this.temp.labelInfoList[i].id) > -1) {
          this.temp.labelInfoList.splice(i, 1)
          i--
        }
      }
    },
    handleLabelImport() {
      this.$refs['labelImportTable'].show();
    },
    handleLabelSearch(searchInfo) {
      searchInfo = searchInfo || ''
      if (searchInfo === '') {
        this.tempLabelElgData = this.labelElgData
      } else {
        //  条件查询
        this.tempLabelElgData = this.labelElgData.filter(item => {
          return (item.name && item.name.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1) ||
            (item.groupName && item.groupName.toLowerCase().indexOf(searchInfo.toLowerCase()) !== -1)
        })
      }
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.labelElgData = []
      this.checkedKeys = []
      this.dealUnFileTypeList = []
      this.emptyLabelFileList = []
      this.dealLabelFileErrorList = []
      this.propRuleId = undefined
    },
    resetTempL() {
      this.tempL = Object.assign({}, this.defaultTempL)
    },
    resetTempC() {
      this.tempC = Object.assign({}, this.defaultTempC)
    },
    labelSelectable(row, index) {
      return selectable(row, index);
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      }, undefined, () => {
        this.$nextTick(() => { // 新增弹框打开时，清除响应规则的选中情况
          if (this.$refs['resContent']) {
            this.$refs['resContent'].ruleId = undefined
          }
        })
      })
      this.$nextTick(() => {
        this.$refs['labelInfoGrid'] && this.$refs['labelInfoGrid'].clearSearchInfo()
      })
    },
    async handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.defaultTemp, JSON.parse(JSON.stringify(row))) // copy obj
      if (row.ruleId) {
        this.propRuleId = row.ruleId
        this.temp.isAlarm = 1
      }
      // 统一样式，当不勾选备份复选框时，备份的阀值设置为0，此时前端界面展示空的输入框
      if (!row.limitFileSize) {
        this.temp.limitFileSize = undefined
        this.temp.backup = 0
      } else { // 旧策略没有这个字段
        this.temp.backup = 1
      }
      if (!this.formable) {
        await getList().then(res => {
          const data = res.data
          if (data) {
            data.forEach(item => {
              const index = item.content.indexOf('zh:')
              item.label = index > -1 ? item.content.slice(index + 3, item.content.length) : item.content
            })
            this.labelGradeTree = data
          }
        })
      }
      this.getLabelListByIds(row.labelIds)
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.checkedKeys.splice(0, this.checkedKeys.length, row.gradeId)
        this.$refs['labelInfoGrid'] && this.$refs['labelInfoGrid'].clearSearchInfo()
      })
    },
    getLabelListByIds(ids) {
      ids = ids || []
      if (ids.length === 0) {
        this.labelElgData = []
        return;
      }
      getLabelLibraryByIds({ ids: ids.join(',') }).then(res => {
        const labelData = res.data || []
        // const exsitId = labelData.map(item => item.id)
        // this.labelElgData = this.labelElgData.filter(data => exsitId.indexOf(data.id) > -1)
        if (this.labelElgData && this.labelElgData.length > 0) {
          const labelList = []
          this.labelElgData.forEach(label => {
            const labelInfo = label
            const labelIds = label.labelIds
            const newLabelIds = []
            const newLabelName = []
            labelData.forEach(item => {
              if (labelIds.indexOf(item.id) > -1) {
                newLabelIds.push(item.id)
                newLabelName.push(item.name)
              }
            })
            labelInfo.labelIds = newLabelIds
            labelInfo.name = newLabelName.join(',')
            labelList.push(labelInfo)
          })
          this.labelElgData = labelList
        }
      })
    },
    getLabelLibraryByIds() {
      const labelIds = [...new Set(this.labelElgData.map(data => data.labelIds))]
      if (labelIds.length === 0) {
        this.labelElgData = []
        return;
      }
      getLabelLibraryByIds({ ids: labelIds.join(',') }).then(res => {
        const labelData = res.data || []
        if (this.exsitLabelElgData && this.exsitLabelElgData.length > 0) {
          const labelList = []
          this.exsitLabelElgData.forEach(label => {
            const labelInfo = label
            const labelIds = label.labelIds
            const newLabelIds = []
            const newLabelName = []
            labelData.forEach(item => {
              if (labelIds.indexOf(item.id) > -1) {
                newLabelIds.push(item.id)
                newLabelName.push(item.name)
              }
            })
            labelInfo.labelIds = newLabelIds
            labelInfo.name = newLabelName.join(',')
            labelList.push(labelInfo)
          })
          this.labelElgData = labelList
        }
      })
    },
    formatRowData(rowData) {
      const labelData = []
      if (rowData.labelIds && rowData.labelIds.length > 0) {
        getLabelLibraryByIds({ ids: rowData.labelIds.join(',') }).then(res => {
          labelData.push(...res.data)
          if (rowData.labelInfoList && rowData.labelInfoList.length > 0) {
            const labelList = []
            rowData.labelInfoList.forEach(label => {
              const labelInfo = label
              const labelIds = label.labelIds
              const newLabelIds = []
              const newLabelName = []
              labelData.forEach(item => {
                if (labelIds.indexOf(item.id) > -1) {
                  newLabelIds.push(item.id)
                  newLabelName.push(item.name)
                }
              })
              labelInfo.labelIds = newLabelIds
              labelInfo.name = newLabelName.join(',')
              if (newLabelIds && newLabelIds.length > 0) {
                labelList.push(labelInfo)
              }
            })
            rowData.labelInfoList = labelList
          }
          rowData.labelIds = [...new Set(labelData.map(data => data.id))]
        })
      }
      if (rowData && rowData.labelInfoList && rowData.labelInfoList.length > 0) {
        this.labelElgData = rowData.labelInfoList
      }
      if (rowData.dealUnFileType == 1) {
        this.dealUnFileTypeList = [1]
      } else if (rowData.dealUnFileType == 2) {
        this.dealUnFileTypeList = [1, 2]
      } else {
        this.dealUnFileTypeList = []
      }
      if (rowData.emptyLabelFile == 1) {
        this.emptyLabelFileList = [1]
      } else if (rowData.emptyLabelFile == 2) {
        this.emptyLabelFileList = [1, 2]
      } else {
        this.emptyLabelFileList = []
      }
      if (rowData.dealLabelFileError == 1) {
        this.dealLabelFileErrorList = [1]
      } else if (rowData.dealLabelFileError == 2) {
        this.dealLabelFileErrorList = [1, 2]
      } else {
        this.dealLabelFileErrorList = []
      }
    },
    formatFormData(formData) {
      formData.ruleId = this.propRuleId
      let checkRuleType
      let checkRuleValue
      let hitRule
      let controlCode
      let lossType
      if (formData.lossTypeFilter.indexOf(-1) > -1) {
        lossType = this.lossTypeOptions.filter(item => item.value != -1).map(item => item.value).join('|')
      } else {
        lossType = formData.lossTypeFilter.join('|')
      }
      if (!formData.backup) {
        formData.limitFileSize = 0
      }
      formData.labelIds = []
      const ruleId = formData.ruleId
      const data = []
      if (this.labelElgData && this.labelElgData.length > 0) {
        formData.labelInfoList = this.labelElgData
        checkRuleType = 0
        this.labelElgData.forEach(item => {
          formData.labelIds.push(...item.labelIds)
          checkRuleValue = { 'hitLabelContentCount': item.hitLabelContentCount, 'labelInfoAlarm': item.labelInfoAlarm, 'labelContentId': item.labelIds }
          if (item.detecteRule == 0 && item.executeRule == 0) {
            hitRule = 0
          } else if (item.detecteRule == 1 && item.executeRule == 0) {
            hitRule = 1
          } else if (item.detecteRule == 0 && item.executeRule == 1) {
            hitRule = 2
          } else if (item.detecteRule == 1 && item.executeRule == 1) {
            hitRule = 3
          }
          controlCode = 0
          if (item.responseRule && item.responseRule.length > 0) {
            controlCode = item.responseRule.indexOf(3) > -1 && item.responseRule.indexOf(12) > -1 ? 12 : item.responseRule.indexOf(3) > -1 ? 3 : 0
          }
          data.push({ checkRuleType, checkRuleValue, hitRule, controlCode, lossType, ruleId })
        })
        formData.labelIds = [...new Set(formData.labelIds)]
      }
      if (formData.grade > -1) {
        checkRuleType = 1
        checkRuleValue = { 'minLevel': formData.grade, 'gradeAlarm': this.temp.gradeAlarm, 'i18nKey': this.temp.gradeLangKye }
        hitRule = formData.gradeHitRule
        controlCode = 0
        if (formData.gradeResList && formData.gradeResList.length > 0) {
          controlCode = formData.gradeResList.indexOf(3) > -1 && formData.gradeResList.indexOf(12) > -1 ? 12 : formData.gradeResList.indexOf(3) > -1 ? 3 : 0
        }
        data.push({ checkRuleType, checkRuleValue, hitRule, controlCode, lossType, ruleId })
      }
      if (formData.emptyLabelFile != null || formData.dealUnFileType != null || formData.dealLabelFileError != null || formData.dealUnFileTypeAlarm != null || formData.dealEmptyLabelFileAlarm != null || formData.dealLabelFileErrorAlarm != null || formData.recordLog != null || formData.limitFileSize != null) {
        checkRuleType = 2
        checkRuleValue = {
          'dealEmptyLabelFile': formData.emptyLabelFile,
          'dealUnFileType': formData.dealUnFileType,
          'dealLabelFileError': formData.dealLabelFileError,
          'dealUnFileTypeAlarm': formData.dealUnFileTypeAlarm,
          'dealEmptyLabelFileAlarm': formData.dealEmptyLabelFileAlarm,
          'dealLabelFileErrorAlarm': formData.dealLabelFileErrorAlarm,
          'isRecordLog': formData.recordLog,
          'backUpFileSize': formData.limitFileSize,
          'backupRuleId': formData.backupRuleId
        }
        hitRule = 0
        controlCode = 0
        data.push({ checkRuleType, checkRuleValue, hitRule, controlCode, lossType, ruleId })
      }
      formData.labelOutSendList = data
      if (formData.lossTypeFilter && formData.lossTypeFilter.length > 0) {
        const code = formData.lossTypeFilter.join('|')
        formData.lossType = code
      }
    },
    validateFormData(formData) {
      if (formData.lossTypeFilter && formData.lossTypeFilter.length == 0) {
        return false
      }
      return true
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // this.tempL.groupName = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          createLabelLib(this.tempL).then(respond => {
            this.submitting = false
            this.dialogLabelFormVisible = false
            this.isImportElg(respond.data, 'create')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            // 通知应用到该配置的标签树重新请求，更新数据
            this.$store.dispatch('commonData/changeNotice', 'updateLabelInfo')
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.tempL)
          // tempData.groupName_new = findNodeLabel(this.treeSelectNode, this.temp.groupId, 'dataId')
          updateLabelLib(tempData).then(respond => {
            this.submitting = false
            this.dialogLabelFormVisible = false
            this.isImportElg(respond.data, 'update')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            // 策略信息界面更新，重新请求界面数据
            this.$emit('updateData')
            // 通知应用到该配置的标签树重新请求，更新数据
            this.$store.dispatch('commonData/changeNotice', 'updateLabelInfo')
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    isImportElg(data, type) {
      if (type === 'create') {
        //  如果在添加策略的弹窗中点击的新增按钮
        if (this.createFlag) {
          this.$refs['labelImportTable'].refreshTableData()
        }
        this.createFlag = false
      } else if (type === 'update') {
        if (this.updateFlag) {
          this.$refs['labelImportTable'].refreshTableData()
        }
        this.updateFlag = false
      }
    },
    //  以下方法是点击导入按钮的弹窗方法
    //  导入方法的row-data-api
    getInfoList(option) {
      this.exsitLabelElgData = this.labelElgData
      const searchQuery = Object.assign({}, this.query, option)
      this.labelGroupSearchId = searchQuery.groupId
      return getLabelLibPage(searchQuery)
    },
    handleInput(value) {
      // 正则表达式匹配所有标点符号（这里使用了 Unicode 属性 \p{P} 来匹配标点）
      const punctuationRegex = /[|,'，]/g
      if (punctuationRegex.test(value)) {
        // 如果输入不是纯标点符号，则清除非标点符号的内容
        this.tempL.name = value.replace(/[|,'，]/g, '')
      } else {
        // 如果是纯标点符号，则直接赋值（可选，根据需求决定是否需要）
        this.tempL.name = value;
      }
    },
    handelChange(value) {
      // 正则表达式匹配所有标点符号（这里使用了 Unicode 属性 \p{P} 来匹配标点）
      const punctuationRegex = /[|,'，]/g
      if (punctuationRegex.test(value)) {
        // 如果输入不是纯标点符号，则清除非标点符号的内容
        this.tempL.name = value.replace(/[|,'，]/g, '')
      } else {
        // 如果是纯标点符号，则直接赋值（可选，根据需求决定是否需要）
        this.tempL.name = value;
      }
    },
    ruleFormatter(row, data) {
      if (data == 0) {
        return this.$t('pages.allMatch1')
      } else if (data == 1) {
        return this.$t('pages.labelPermissionConfig', { n: row.hitLabelContentCount })
      }
    },
    executeRuleFormatter(row, data) {
      if (data == 0) {
        return '规则之内'
      } else if (data == 1) {
        return '规则之外'
      }
    },
    responseRuleFormatter(row, data) {
      let resText = ''
      if (data.indexOf(3) > -1 && data.indexOf(12) > -1) {
        resText = this.$t('pages.burnMsg5') + '、' + this.$t('pages.burnMsg7')
      } else if (data.indexOf(3) > -1 && data.indexOf(12) == -1) {
        resText = this.$t('pages.burnMsg5')
      } else {
        resText = ''
      }
      if (resText === '' && row.labelInfoAlarm) {
        resText += this.$t('pages.alarmOfOutgoing')
      } else if (resText !== '' && row.labelInfoAlarm) {
        resText += '、' + this.$t('pages.alarmOfOutgoing')
      }
      return resText
    },
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.treeSelectNode, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    handleLabelTypeChange(value) {
      if (this.tempC.responseRule.indexOf(3) == -1 && this.tempC.responseRule.indexOf(12) > -1) {
        this.tempC.responseRule = []
      }
    },
    handleResTypeChange(value) {
      if (this.temp.gradeResList.indexOf(3) == -1 && this.temp.gradeResList.indexOf(12) > -1) {
        this.temp.gradeResList = []
      }
    },
    handleEmptyLabelFileChange(value) {
      if (value.indexOf(1) === -1 && value.indexOf(2) > -1) {
        this.emptyLabelFileList = []
      }
      if (value.indexOf(1) > -1 && value.indexOf(2) === -1) {
        this.temp.emptyLabelFile = 1
      } else if (value.indexOf(1) > -1 && value.indexOf(2) > -1) {
        this.temp.emptyLabelFile = 2
      } else {
        this.temp.emptyLabelFile = 0
      }
    },
    handleDealUnFileTypeChange(value) {
      if (value.indexOf(1) === -1 && value.indexOf(2) > -1) {
        this.dealUnFileTypeList = []
      }
      if (value.indexOf(1) > -1 && value.indexOf(2) === -1) {
        this.temp.dealUnFileType = 1
      } else if (value.indexOf(1) > -1 && value.indexOf(2) > -1) {
        this.temp.dealUnFileType = 2
      } else {
        this.temp.dealUnFileType = 0
      }
    },
    handleDealLabelFileErrorChange(value) {
      if (value.indexOf(1) === -1 && value.indexOf(2) > -1) {
        this.dealLabelFileErrorList = []
      }
      if (value.indexOf(1) > -1 && value.indexOf(2) === -1) {
        this.temp.dealLabelFileError = 1
      } else if (value.indexOf(1) > -1 && value.indexOf(2) > -1) {
        this.temp.dealLabelFileError = 2
      } else {
        this.temp.dealLabelFileError = 0
      }
    },
    handleUpdatePermission() {
      this.submitting = true
      if (this.tempC.detecteRule === 1 && !this.tempC.hitLabelContentCount) {
        this.submitting = false
        return
      }
      if (this.tempC.detecteRule === 1 && this.tempC.hitLabelContentCount > this.oldHitLabelContentCount) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.conditionTips1'),
          type: 'warning',
          duration: 3000
        })
        this.submitting = false
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.labelElgData.forEach((data, index) => {
            if (data.id == this.tempC.id) {
              data.detecteRule = this.tempC.detecteRule
              data.executeRule = this.tempC.executeRule
              data.responseRule = this.tempC.responseRule
              data.hitLabelContentCount = this.tempC.hitLabelContentCount
              data.labelInfoAlarm = this.tempC.labelInfoAlarm
            }
          })
          this.submitting = false
          this.dialogFormVisible = false
        } else {
          this.submitting = false
        }
      })
    },
    getNeedAddIds(data, list, optTypeList, labelDec, hitRule, labelResList, hitLabelContentCount, labelInfoAlarm) {
      this.needIds = data
      this.importDataList = list
      this.labelDec = labelDec
      this.hitRule = hitRule
      this.labelResList = labelResList
      this.hitLabelContentCount = hitLabelContentCount
      this.labelInfoAlarm = labelInfoAlarm
    },
    elgCancelAfter() {
      this.loadGroupTree();
      this.getLabelLibraryByIds();
    },
    // 弹窗分组的数据发送变化时
    changeGroupAfter() {
      this.loadGroupTree()
      this.getLabelLibraryByIds();
    },
    lossTypeChange(selections) {
      const selectAllIndex = selections.indexOf(this.lossTypeAll)
      if (selections.length > 1) {
        if (selectAllIndex === selections.length - 1 || selections.length === this.lossTypeOptions.length - 1) {
          // 所有类型都选中，则显示为所有泄露方式
          selections.splice(0, selections.length, this.lossTypeAll)
        } else if (selectAllIndex > -1) {
          selections.splice(selectAllIndex, 1)
        }
      }
    },
    getSensitiveLossType() {
      const supportList = [0, 2, 3, 7, 9, 13, 15, 18, 20, 29, 32]
      getSensitiveLossType({ type: 2 }).then(res => {
        const data = res.data.filter(item => supportList.indexOf(item.lossType) > -1).map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
        this.lossTypeOptions = [{ label: this.$t('pages.lossTypeOptions'), value: this.lossTypeAll }, ...data]
      })
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    nameValidator(rule, value, callback) {
      countByLabel({ name: value }).then(respond => {
        const data = respond.data
        if (data && data.id !== this.tempL.id) {
          callback(new Error(this.$t('pages.labelLibrary_confirmMsg')))
        } else {
          callback()
        }
      })
    },
    lossTypeValidator(rule, value, callback) {
      if (this.temp.lossTypeFilter && this.temp.lossTypeFilter.length == 0) {
        callback(new Error(this.$t('pages.group_text5')))
      }
      callback()
    },
    responseRuleValidator(rule, value, callback) {
      if (this.tempC.responseRule && this.tempC.responseRule.length == 0) {
        callback(new Error(this.$t('pages.install_Msg15')))
      }
      callback()
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.temp.backup == 1) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else if (value > 10240) {
          return callback(new Error(this.$t('pages.blueTooth_Msg4')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    recordChange(val) {
      if (!val && this.temp.backup == 1) {
        this.backupchange(0)
      }
    },
    backupchange(val) {
      if (!val) {
        const labelPermission = this.$refs['stgDlg']
        if (labelPermission) {
          const formDlg = labelPermission.$refs['dataForm4StgDialog' + this.slotName]
          formDlg && formDlg.clearValidate('limitFileSize')
        }
      }
      this.temp.backup = val
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    }
  }
}
</script>
<style lang="scss" scoped>
  .error_style {
    line-height: 20px; color: #F56C6C; font-size: 12px
  }
</style>
