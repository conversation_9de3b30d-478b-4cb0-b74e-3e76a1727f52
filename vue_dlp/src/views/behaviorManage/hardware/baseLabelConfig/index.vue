<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.labelLibrary')" name="LabelLibrary">
        <Label-library ref="MailReceiver"></Label-library>
      </el-tab-pane>
      <el-tab-pane :lazy="true" :label="$t('pages.fileLevel')" name="LabelGradeLibrary">
        <Label-grade-library ref="MailSender"></Label-grade-library>
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission('113')" :lazy="true" :label="$t('pages.labelDetectionLibrary')" name="LabelDetectebrary">
        <Auto-labeling ref="MailSender"></Auto-labeling>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import AutoLabeling from '@/views/contentStrategy/strategy/autoLabelingStrategy'
import LabelLibrary from '@/views/system/baseData/labelLibrary'
import LabelGradeLibrary from '@/views/system/baseData/labelGradeLibrary'

export default {
  name: 'BaseLabelConfig',
  components: { AutoLabeling, LabelLibrary, LabelGradeLibrary },
  props: {
    tabName: { type: String, default: 'LabelLibrary' }
  },
  data() {
    return {
      activeName: this.tabName
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    const { query, params } = this.$route
    this.activeName = (query && query.tabName) || (params && params.tabName) || this.activeName
  },
  activated() {
    const { query, params } = this.$route
    this.activeName = (query && query.tabName) || (params && params.tabName) || this.activeName
  },
  methods: {
    tabClick(pane, event) {
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
  .app-container{
    padding: 10px 15px;
  }
</style>
