<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="appendToBody"
      :modal="false"
      :title="$t('pages.usbDevice_create')"
      :remark="$t('pages.usbDevice_text7')"
      :visible.sync="dlgVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div class="toolbar">
        <el-row>
          <el-col :span="10">
            <div style="display: flex">
              <div style="font-weight: 700;padding-top: 7px; margin-right: 5px">
                {{ $t('pages.groupType') }}
              </div>
              <tree-select ref="groupTree" v-model="query.groupId" :data="treeData" :width="200" @change="treeNodeClick" />
            </div>
          </el-col>
          <el-col :span="14">
            <div style="float: right;">
              <el-input v-model="query.usbName" v-trim clearable :placeholder="$t('pages.deviceName_1')" style="width: 200px;"/>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <grid-table
        ref="appInfoList"
        :height="300"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        saved-selected-prop="usbName"
        :default-sort="{ prop: 'usbName' }"
        :page-sizes="[ 20, 50, 100, 500, 1000 ]"
        @selectionChangeEnd="selectionChangeEnd"
      />
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :menu-code="'A5B'" :link-url="'/system/baseData/usbDevice'" :btn-text="$t('pages.maintainUsb')"/>
        <el-button type="primary" :loading="submitting" @click="handleSelect()">
          {{ $t('pages.addSelectedDevice') }}
        </el-button>
        <el-button @click="dlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { findUsbGroupTree, findUsbDeviceList } from '@/api/behaviorManage/hardware/usbConfig'
import { getUsbTypeDict } from '@/utils/dictionary'

export default {
  name: 'UsbSelectDlg',
  components: {},
  props: {
    appendToBody: { type: Boolean, default: false }
  },
  data() {
    return {
      colModel: [
        { prop: 'usbName', label: 'volumeName', width: '100', sort: 'custom' },
        { prop: 'groupName', label: 'sourceGroup', width: '100', sort: 'custom' },
        { prop: 'usbCode', label: 'pnpDeviceId', width: '100', sort: 'custom' },
        { prop: 'usbType', label: 'devType', width: '100', sort: 'custom', formatter: this.usbTypeOptionFormatter },
        { prop: 'vid', label: 'vid', width: '90', sort: 'custom' },
        { prop: 'manufacturer', label: 'manufacturer', width: '80', sort: 'custom' },
        { prop: 'pid', label: 'pid', width: '90', sort: 'custom' },
        { prop: 'srcPnpDeviceId', label: 'deviceSerialNumber', width: '120', sort: 'custom' },
        { prop: 'usbSize', label: 'usbSize', width: '120', sort: 'custom' }
      ],
      usbTypeOptions: getUsbTypeDict(),
      query: { // 查询条件
        page: 1,
        usbName: '',
        groupId: 0
      },
      dlgVisible: false,
      submitting: false,
      selectTreeId: null,
      treeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.usbDevice'), parentId: '', children: [] }],
      showName: true,
      updateable: false
    }
  },
  computed: {
    groupTree: function() {
      return this.$refs['groupTree']
    },
    softTable: function() {
      return this.$refs['appInfoList']
    }
  },
  created() {
  },
  activated() {
    if (this.dlgVisible) {
      this.handleFilter()
    }
  },
  methods: {
    show() {
      this.dlgVisible = true
      this.selectTreeId = null
      this.query.groupId = 0
      this.query.usbName = null
      this.loadGroupTree()
      this.$nextTick(() => {
        this.softTable && this.softTable.clearSaveSelection()
        this.handleFilter()
        this.groupTree && this.groupTree.clearFilter()
        this.groupTree && this.groupTree.clearSelectedNode()
      })
    },
    treeNodeClick(data, node) {
      this.selectTreeId = node.dataId
      this.handleFilter()
    },
    selectionChangeEnd(rowDatas) {
      this.updateable = rowDatas && rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.softTable && this.softTable.execRowDataApi(this.query)
    },
    // 加载某个软件类别下的应用程序
    rowDataApi: function(option) {
      if (!this.selectTreeId) {
        this.selectTreeId = 0
      }
      this.query.groupId = this.selectTreeId
      const newOption = Object.assign(this.query, option)
      newOption.groupId = this.selectTreeId
      return findUsbDeviceList(newOption)
    },
    loadGroupTree: function() {
      findUsbGroupTree().then(respond => {
        this.treeData[0].children = respond.data
        this.$nextTick(() => {

        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleSelect() {
      if (!(this.softTable.getSelectedDatas() && this.softTable.getSelectedDatas().length > 0)) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.selectTips'),
          type: 'error',
          duration: 2000
        })
        return
      }
      const array = []
      const selected = this.softTable.getSelectedDatas() || []
      selected.forEach(item => {
        array.push(Object.assign({}, item));
      })

      this.$emit('select', array)
      this.dlgVisible = false
    },
    usbTypeOptionFormatter(row, data) {
      let msg = ''
      this.usbTypeOptions.forEach(item => {
        if (data == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    updateData(newData) {
      if (newData.startTime && newData.endTime) {
        if (this.showName) {
          newData.exceptionTime = newData.startTime + '~' + newData.endTime
          this.softTable.updateRowData(newData)
        } else {
          const datas = this.softTable.getSelectedDatas()
          datas.forEach((data) => {
            data.exceptionTime = newData.startTime + '~' + newData.endTime
            this.softTable.updateRowData(data)
          })
        }
      }
    }
  }
}
</script>
