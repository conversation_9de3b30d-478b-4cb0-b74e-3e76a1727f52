<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="740px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 670px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('pages.usbOutFileSetting')" name="usbSetting">
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="40px" prop="action">
              {{ $t('pages.usbFileConfigMessageExecute') }}
            </FormItem>

            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="30px">
              <el-row>
                <el-col :span="18">
                  <el-checkbox-group v-model="approvalTypeList" style="display: inline-block" :disabled="!formable" @change="handleApprovalTypeChange">
                    <el-checkbox :label="1">{{ hasEncPermision ? $t('pages.burnMsg4') : $t('pages.burnMsg5') }}</el-checkbox>
                    <el-checkbox :disabled="approvalTypeList.indexOf(1) == -1" :label="4">{{ hasEncPermision ? $t('pages.burnMsg6') : $t('pages.burnMsg7') }}</el-checkbox>
                    <el-checkbox v-if="hasEncPermision" :label="2">{{ $t('pages.burnMsg8') }}</el-checkbox>
                    <el-checkbox v-if="hasEncPermision" :disabled="approvalTypeList.indexOf(2) == -1" :label="8">{{ $t('pages.burnMsg9') }}</el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
            </FormItem>

            <ResponseContent
              :status="dialogStatus"
              :select-style="{ 'margin-top': '5px', 'margin-left': '13px' }"
              :show-select="true"
              :rule-type-name="ruleTypeName"
              :editable="formable && ruleDisable"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="propRuleId"
              @ruleIsCheck="getRuleIsCheck"
              @getRuleId="getRuleId"
            />

            <ResponseContent
              v-if="hasEncPermision"
              :status="dialogStatus"
              :select-style="{ 'margin': '5px 0 5px 13px' }"
              :show-select="true"
              :rule-type-name="encRuleTypeName"
              :editable="formable && encRuleDisable"
              read-only
              :prop-check-rule="!!temp.isEncAlarm"
              :show-check-rule="true"
              :prop-rule-id="encPropRuleId"
              @ruleIsCheck="getEncRuleIsCheck"
              @getRuleId="getEncRuleId"
            />
          </el-tab-pane>

          <el-tab-pane :label="$t('pages.usbOutFileRecordSetting')" name="usbRecord">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <div style="margin-left: 30px">
              <div v-if="formable">
                <el-button size="small" @click="handleCreateUsb()">
                  {{ $t('button.insert') }}
                </el-button>
                <el-button size="small" :disabled="!checkUsbDeleteAble" @click="handleDeleteCheckedUsb()">
                  {{ $t('button.delete') }}
                </el-button>
                <el-button size="small" @click="handleClearCheckedUsb()">
                  {{ $t('button.clear') }}
                </el-button>
              </div>
              <grid-table
                ref="checkedUsbGrid"
                row-key="usbCode"
                :show-pager="false"
                :height="200"
                :selectable="selectable"
                :col-model="checkedColModel"
                :row-datas="usbList"
                @selectionChangeEnd="checkUsbSelectionChangeEnd"
              />
            </div>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <el-radio-group v-model="temp.recordCheckType" :disabled="!formable" style="margin-left: 18px;">
              <el-radio key="1" :value="1" :label="1">{{ $t('pages.usbFileConfigMsg1') }}</el-radio>
              <el-radio key="0" :value="0" :label="0">{{ $t('pages.usbFileConfigMsg2') }}</el-radio>
            </el-radio-group>
            <div style="margin-left: 18px; margin-top: 5px; color: #409EFF">{{ $t('pages.usbFileConfigMsg3') }}
              <span>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content" style="width: 480px;">
                    {{ $t('pages.usbFileConfigMsg4') }}<br>
                    {{ $t('pages.usbFileConfigMsg5') }}<br>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
            </div>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem style="margin-left: -87px" prop="action">
              <el-checkbox v-model="temp.record" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable" @change="recordChange">{{ $t('pages.addUsbRecord') }}</el-checkbox>
            </FormItem>
            <FormItem label-width="30px" prop="fileBackUpLimit">
              <el-checkbox v-model="temp.isBackup" :disabled="!formable || !temp.record" :true-label="1" :false-label="0" @change="backupchange">
              </el-checkbox>
              <i18n path="pages.blueTooth_Msg1">
                <el-input-number slot="size" v-model="temp.fileBackUpLimit" :disabled="!formable || temp.isBackup==0" :controls="false" step-strictly :min="1" :max="10240" size="mini" style="width: 100px;"/>
              </i18n>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content" style="width: 480px;">{{ $t('pages.usbFileConfig_text3') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-button style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
            </FormItem>
          </el-tab-pane>
        </el-tabs>

      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importUsbFileConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
    <usb-select-dlg ref="usbSelectDlg" @select="selectUSBEnd"/>
  </div>
</template>
<script>
import {
  getDataPage, getDataByName, createData, updateData, deleteData
} from '@/api/behaviorManage/hardware/usbFileConfig'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'
import UsbSelectDlg from './usbSelectDlg'
import { listDeviceById } from '@/api/behaviorManage/hardware/usbConfig';
import { getUsbTypeDict } from '@/utils/dictionary';

export default {
  name: 'UsbFileConfig',
  components: { ResponseContent, ImportStg, BackupRuleContent, UsbSelectDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 200,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      checkedColModel: [
        { prop: 'usbName', label: 'volumeName', width: '100', sort: true },
        { prop: 'usbCode', label: 'pnpDeviceId', width: '100', sort: true, formatter: this.usbCodeFormatter },
        { prop: 'usbType', label: 'devType', width: '100', sort: true, formatter: this.usbTypeOptionFormatter },
        { prop: 'vid', label: 'vid', width: '90', sort: true },
        { prop: 'manufacturer', label: 'manufacturer', width: '80', sort: true },
        { prop: 'pid', label: 'pid', width: '90', sort: true },
        { prop: 'srcPnpDeviceId', label: 'deviceSerialNumber', width: '120', sort: true },
        { prop: 'usbSize', label: 'usbSize', width: '120', sort: true }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        isBackup: 0,
        fileBackUpLimit: 20,
        entityType: '',
        entityId: undefined,
        // isBackup: 0,  // 是否备份 1-备份 0-不备份
        isAlarm: 0, // 明文文件触发响应规则
        isEncAlarm: 0, // 密文文件触发响应规则
        ruleId: undefined, // 明文文件响应规则id
        encRuleId: undefined, // 密文文件响应规则id
        isLimit: 0, // 1-限制外发 0-不限制外发
        approvalType: 0, // 位与 审批外发 1-明文文件禁止外发, 2-密文文件禁止外发, 4-允许明文文件外发, 8-允许密文文件外发
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined, // 备份过滤规则id
        objectGroupIds: [],
        objectIds: [],
        usbRecordCheckRule: [],
        recordCheckType: 0, //  检测类型 0-检测规则之外， 1-检测规则之内
        record: 0 //  是否记录 0-不记录，1-记录
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.usbFileConfigStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.usbFileConfigStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileBackUpLimit: [
          { validator: this.fileBackUpLimitValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      propRuleId: undefined,  // 明文文件响应规则id
      encPropRuleId: undefined, // 密文文件响应规则id
      approvalTypeList: [], // 审批外发
      hasEncPermision: true,  // 是包含加解密模块
      // 触发明文文件响应规则
      ruleTypeName: this.$t('pages.burnMsg10'),
      // 触发密文文件响应规则
      encRuleTypeName: this.$t('pages.burnMsg11'),
      validRuleId: true,
      validEncRuleId: true,
      ruleDisable: false, // 当明文执行规则没有配置时,明文响应规则要置灰
      encRuleDisable: false, // 当密文执行规则没有配置时,明文响应规则要置灰
      backupDisable: false, // 备份文件是否置灰
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined, // 备份过滤规则id
      usbList: [], //  检测规则列表中的数据
      checkUsbDeleteAble: false,
      activeName: 'usbSetting',
      usbTypeOptions: getUsbTypeDict()
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取注册模块
    this.listModule()
  },
  methods: {
    async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = resp.data
        this.ruleTypeName = this.hasEncPermision ? this.$t('pages.burnMsg10') : this.$t('pages.triggerResponseRule')
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getDataPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.usbList = []
      this.propRuleId = undefined
      this.encPropRuleId = undefined
      this.approvalTypeList.splice(0)
      this.validRuleId = true
      this.validEncRuleId = true
      this.ruleDisable = false
      this.encRuleDisable = false
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      if (this.temp.entityType == 1 || this.temp.entityType == 2) {
        this.temp.objectIds.push(this.temp.entityId)
      } else if (this.temp.entityType == 3 || this.temp.entityType == 4) {
        this.temp.objectGroupIds.push(this.temp.entityId)
      }
      this.dialogStatus = 'create'
      this.activeName = 'usbSetting'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      // 统一样式，当不勾选备份复选框时，备份的阀值设置显示为默认值
      if (this.temp.fileBackUpLimit == 0) {
        this.temp.fileBackUpLimit = this.defaultTemp.fileBackUpLimit
        this.temp.isBackup = 0
      } else { // 旧策略没有这个字段
        this.temp.isBackup = 1
      }
      if (this.temp.approvalType > 0) {
        this.approvalTypeList = this.numToList(this.temp.approvalType, 4)
        // 当勾选的有允许文件外发审批时，approvalType是没有保存禁止外发的值的，所以这边要做下处理
        if (this.approvalTypeList.indexOf(4) > -1) {
          this.approvalTypeList.push(1)
        }
        if (this.approvalTypeList.indexOf(8) > -1) {
          this.approvalTypeList.push(2)
        }
      }
      this.propRuleId = row.ruleId
      this.encPropRuleId = row.encRuleId
      // 当明文执行规则没配置时,无法配置明文响应规则
      if (this.approvalTypeList.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(2) > -1 && this.approvalTypeList.indexOf(4) == -1 && this.approvalTypeList.indexOf(8) == -1) {
        this.backupDisable = true
      } else {
        this.backupDisable = false
      }

      this.usbList = []
      //  兼容旧数据，
      this.temp.recordCheckType = this.temp.recordCheckType ? this.temp.recordCheckType : 0
      //  检测规则
      if (row.usbRecordCheckRule && row.usbRecordCheckRule.length > 0) {
        const usbIds = []
        row.usbRecordCheckRule.forEach(item => {
          if (item.id !== null) {
            usbIds.push(item.id)
          }
        })
        if (usbIds.length > 0) {
          listDeviceById(usbIds).then(res => {
            res.data = res.data || [];
            this.usbList = res.data
            //  若设备编码为空时，将usbCode设置为'empty'
            this.usbList.forEach(usb => {
              if (usb.usbCode === undefined || usb.usbCode === null || usb.usbCode === '') {
                usb.usbCode = 'empty'
              }
            })
          })
        }
      } else {
        row.usbRecordCheckRule = []
      }
      //  记录USB外发文件记录， 兼容旧策略,旧策略中默认是记录USB外发文件记录的，固record默认值为1
      if (row.record === undefined || row.record === null) {
        this.temp.record = 1
      }

      this.dialogStatus = 'update'
      this.activeName = 'usbSetting'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId) {
          createData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId) {
          const tempData = Object.assign({}, this.temp)
          updateData(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const msgArr = []
      if (row.approvalType >= 0) {
        const approvalList = this.numToList(row.approvalType, 4)
        if (this.hasEncPermision) {
          if (approvalList.indexOf(1) > -1) {
            // 禁止明文文件传输
            msgArr.push(this.$t('pages.burnMsg14'))
          }
          if (approvalList.indexOf(2) > -1) {
            // 禁止密文文件传输
            msgArr.push(this.$t('pages.burnMsg15'))
          }
          if (approvalList.indexOf(4) > -1) {
            // 允许明文文件外发审批
            msgArr.push(this.$t('pages.burnMsg6'))
          }
          if (approvalList.indexOf(8) > -1) {
            // 允许密文文件外发审批
            msgArr.push(this.$t('pages.burnMsg9'))
          }
        }
      }
      if (this.hasEncPermision) {
        if (row.ruleId) {
          // 触发明文文件响应规则
          msgArr.push(this.$t('pages.burnMsg10'))
        }
        if (row.encRuleId) {
          // 触发密文文件响应规则
          msgArr.push(this.$t('pages.burnMsg11'))
        }
      } else {
        if (row.encRuleId) {
          // 触发响应规则
          msgArr.push(this.$t('pages.triggerResponseRule'))
        }
      }
      const recordMsgArr = []
      if (row.record !== undefined && row.record === 1) {
        recordMsgArr.push(this.$t('pages.addUsbRecord') + '：' + this.$t('text.yes'))
      } else if (row.record !== undefined && row.record === 0) {
        recordMsgArr.push(this.$t('pages.addUsbRecord') + '：' + this.$t('text.no'))
      }
      if (row.fileBackUpLimit) {
        // 文件备份限制{row.fileBackUpLimit}MB
        recordMsgArr.push(this.$t('pages.fileBackUpLimit1') + '：' + row.fileBackUpLimit + 'MB')
      } else {
        recordMsgArr.push(this.$t('pages.fileBackUpLimit1') + '：' + this.$t('pages.backupRule1'))
      }
      return `${this.$t('pages.usbOutFileSetting')}：{ ${msgArr.length === 0 ? '' : this.$t('pages.usbFileConfigMessageExecute_1') + '：'}${msgArr.join('、')} }；
              ${this.$t('pages.usbOutFileRecordSetting')}：{ ${recordMsgArr.length === 0 || row.recordCheckType === undefined ? '' : row.recordCheckType === 1 ? this.$t('pages.usbFileConfigMsg1_1') + '：' : this.$t('pages.usbFileConfigMsg2_1') + '：'}${recordMsgArr.join('、')} }`
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    usbCodeFormatter(row) {
      return row.usbCode === 'empty' ? '' : row.usbCode;
    },
    usbTypeOptionFormatter(row, data) {
      let msg = ''
      this.usbTypeOptions.forEach(item => {
        if (data == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    formatFormData() {
      this.temp.ruleId = this.propRuleId
      this.temp.encRuleId = this.encPropRuleId
      this.temp.approvalType = this.getSum(this.approvalTypeList)
      // 当勾选了允许审批外发时，要去除掉禁止的值比如禁止审批外发为1允许审批外发为4两个都勾选时approvalType只保存4
      this.temp.approvalType = this.getSum(this.approvalTypeList)
      if (this.approvalTypeList.indexOf(4) > -1) {
        this.temp.approvalType = this.temp.approvalType - 1
      }
      if (this.approvalTypeList.indexOf(8) > -1) {
        this.temp.approvalType = this.temp.approvalType - 2
      }
      // 处理禁止传输的值,兼容旧版本
      if (this.hasEncPermision) {
        // 有加解密模块当禁止明文传输跟禁止密文传输都禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(2) > -1 && this.approvalTypeList.indexOf(4) == -1 && this.approvalTypeList.indexOf(8) == -1) {
          this.temp.isLimit = 1
        } else {
          this.temp.isLimit = 0
        }
      } else {
        // 无加解密模块，当禁止明文传输为禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(4) == -1) {
          this.temp.isLimit = 1
        } else {
          this.temp.isLimit = 0
        }
      }
      this.validRuleId = true
      this.validEncRuleId = true
      if (this.temp.isAlarm === 1 && !this.temp.ruleId) {
        this.validRuleId = false
      }
      if (this.temp.isEncAlarm === 1 && !this.temp.encRuleId) {
        this.validEncRuleId = false
      }
      //  检测规则  若检测规则列表中未配置策略时，下发一个元素{type: this.temp.recordCheckType, usbCode: ''}
      const usbCode = []
      if (this.usbList && this.usbList.length > 0) {
        this.usbList.forEach(usb => {
          usbCode.push({ type: this.temp.recordCheckType, usbCode: usb.usbCode, id: usb.id || null })
        })
      } else {
        usbCode.push({ type: this.temp.recordCheckType, usbCode: '' })
      }
      this.temp.usbRecordCheckRule = usbCode
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    getEncRuleId(value) {
      this.encPropRuleId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    getEncRuleIsCheck(value) {
      this.temp.isEncAlarm = value
    },
    handleApprovalTypeChange(value) {
      if (value.indexOf(4) > -1 && value.indexOf(1) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(4), 1)
      }
      if (value.indexOf(8) > -1 && value.indexOf(2) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(8), 1)
      }
      // 勾选了执行规则，响应规则才能勾选
      if (value.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (value.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(2) > -1 && this.approvalTypeList.indexOf(4) == -1 && this.approvalTypeList.indexOf(8) == -1) {
        this.backupDisable = true
      } else {
        this.backupDisable = false
      }
    },
    importSuccess() {
      this.handleFilter()
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.temp.isBackup == 1 && !value) {
        return callback(new Error(this.$t('components.required')))
      } else {
        callback()
      }
    },
    recordChange(data) {
      if (!data) {
        this.temp.isBackup = 0
      }
    },
    backupchange(data) {
      if (data == 0) {
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    },
    checkUsbSelectionChangeEnd(rowDatas) {
      this.checkUsbDeleteAble = rowDatas && rowDatas.length > 0
    },
    handleCreateUsb() {
      this.$refs['usbSelectDlg'].show()
    },
    handleDeleteCheckedUsb() {
      const keys = this.$refs.checkedUsbGrid.getSelectedKeys() || []
      this.usbList = this.usbList.filter(item => !keys.includes(item.usbCode))
    },
    handleClearCheckedUsb() {
      this.usbList.splice(0)
    },
    selectUSBEnd(array) {
      //  过滤相同设备编码的数据
      const list = [];
      //  为空设备编码设置empty
      array.forEach(item => {
        if (item.usbCode === undefined || item.usbCode === null || item.usbCode === '') {
          item.usbCode = 'empty'
        }
      })
      array.forEach(item => {
        if (list.findIndex(usb => { return usb.usbCode === item.usbCode }) === -1) {
          list.push(item);
        }
      })
      const listLength = list.length;
      const usbListLength = this.usbList.length;

      list.forEach(item => {
        if (this.usbList.findIndex(usb => { return usb.usbCode === item.usbCode }) === -1) {
          this.usbList.push(item)
        }
      })
      if ((listLength + usbListLength) > this.usbList.length) {
        this.$message({
          message: this.$t('pages.usbFileConfigFilterSameData'),
          type: 'warning',
          duration: 2000
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.prohibit >>> .el-checkbox__inner {
  margin-left: 17px;
}
</style>
