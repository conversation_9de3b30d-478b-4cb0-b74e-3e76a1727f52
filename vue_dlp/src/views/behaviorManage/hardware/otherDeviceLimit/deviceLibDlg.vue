<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.otherDeviceLimit_deviceConfigurationLibrary')"
      :visible.sync="dlgVisible"
      width="800px"
    >
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add_manually') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.otherDeviceLimit_msg10')" :title="$t('pages.otherDeviceLimit_msg10')" style="width: 270px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="deviceLibList" :col-model="colModel" :row-data-api="rowDataApi" :height="showEffectiveType ? 370 : 400" @selectionChangeEnd="selectionChangeEnd" />

      <Form v-if="showEffectiveType" ref="updateEffectTypeForm" label-width="100px" style="width: 750px; margin-top: 20px;">
        <FormItem :label="$t('table.effectType')">
          <el-radio-group v-model="effectTypeRadio">
            <el-radio :label="1">{{ $t('pages.otherDeviceLimitOption1') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.otherDeviceLimitOption2') }}</el-radio>
            <el-radio :label="4">
              {{ $t('pages.otherDeviceLimitOption3') }}
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.otherDeviceLimit_msg2') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </FormItem>
      </Form>
      <div v-if="showEffectiveType" slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="addStgData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>

    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="titleMap[createOrUpdateStatus]"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="deviceVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="130px" style="width: 460px;">
        <FormItem :label="$t('pages.otherDeviceLimitOption1')" :tooltip-content="$t('pages.otherDeviceLimit_msg4')" prop="devName" tooltip-placement="bottom-start">
          <el-input v-model="temp.devName" clearable :maxlength="100"/>
        </FormItem>
        <FormItem :label="$t('pages.otherDeviceLimitOption2')" :tooltip-content="$t('pages.otherDeviceLimit_msg5')" prop="devInstanceId" tooltip-placement="bottom-start">
          <el-input v-model="temp.devInstanceId" clearable :maxlength="500"/>
        </FormItem>
        <FormItem :label="$t('pages.otherDeviceLimitOption3')" :tooltip-content="$t('pages.otherDeviceLimit_msg6')" tooltip-placement="bottom-start">
          <el-autocomplete
            v-model="temp.classGuid"
            popper-class="my-autocomplete"
            class="inline-input"
            :fetch-suggestions="querySearch"
            :maxlength="500"
            :placeholder="$t('pages.otherDeviceLimit_msg7')"
            clearable
            @select="handleSelect"
          >
            <template slot-scope="{ item }">
              <div class="name">{{ item.name }}</div>
              <span class="value">{{ item.value }}</span>
            </template>
          </el-autocomplete>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" clearable :maxlength="100"/>
        </FormItem>
        <div v-if="createOrUpdateStatus==='update'" >
          <p style="color: #409eff;">{{ $t('pages.otherDeviceLimit_msg8') }}</p>
        </div>

      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="createOrUpdateStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="deviceVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getDeviceLibPage,
  addDevice,
  updateDevice,
  deleteDevice,
  getDevByInstanceId
} from '@/api/behaviorManage/hardware/otherDeviceLimit'

export default {
  name: 'DeviceLibDlg',
  props: {
    useFun: { type: String, default: 'deviceLib' }
  },
  data() {
    return {
      submitting: false,
      dlgVisible: false, // 列表页是否显示
      selectedData: [],
      colModel: [
        { prop: 'devName', label: 'deviceName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'devInstanceId', label: 'devInstanceId', width: '150', sort: 'custom' },
        { prop: 'classGuid', label: 'classGuid', width: '150', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '150', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        devName: ''
      },
      deviceVisible: false, // 新增修改页面dlg
      titleMap: {
        update: this.$t('pages.modifyDevice'),
        create: this.$t('pages.createDevice')
      },
      createOrUpdateStatus: 'create',
      deleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        devName: '',
        devInstanceId: '',
        classGuid: '',
        remark: ''
      },
      rules: {
        devName: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        devInstanceId: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.devInstanceIdValidator, trigger: 'blur' }
        ]
      },
      effectTypeRadio: 1
    }
  },
  computed: {
    gridTable() {
      return this.$refs['deviceLibList']
    },
    showEffectiveType() {
      return this.useFun == 'custom' || this.useFun == 'exception'
    }
  },
  watch: {

  },
  created() {
  },
  activated() {
  },
  methods: {
    show() {
      this.dlgVisible = true
      this.$nextTick(() => {
        this.handleFilter()
      })
    },
    hide() {
      this.dlgVisible = false
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getDeviceLibPage(searchQuery)
    },
    selectionChangeEnd(val) {
      this.selectedData = val
      this.deleteable = val.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    // 功能
    resetTemp() {
      this.submitting = false
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.deviceVisible = true
      this.createOrUpdateStatus = 'create'
      this.resetTemp()
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.createOrUpdateStatus = 'update'
      this.deviceVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    // 表单-Guid
    loadAllGuid() {
      return [
        { name: 'DVD/CD-ROM 驱动器', value: '{4d36e965-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice1' },
        { name: 'IDE ATA/ATAPI 控制器', value: '{4d36e96a-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice2' },
        { name: '声音、视频和游戏控制器', value: '{4d36e96c-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice3' },
        { name: 'WSD 打印提供程序', value: '{c30ecea0-11ef-4ef9-b02e-6af81e6e65c0}', i18n: 'terminalDevice4' },
        { name: '便携设备', value: '{eec5ad98-8080-425f-922a-dabf3de3f69a}', i18n: 'terminalDevice5' },
        { name: '磁盘驱动器', value: '{4d36e967-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice6' },
        { name: '打印队列 ', value: '{1ed2bbf9-11f0-4084-b21f-ad83a8e6dcdc}', i18n: 'terminalDevice7' },
        { name: '打印机', value: '{4d36e979-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice8' },
        { name: '端口 (COM 和 LPT)', value: '{4d36e978-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice9' },
        { name: '监视器', value: '{4d36e96e-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice10' },
        { name: '键盘', value: '{4d36e96b-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice11' },
        { name: '蓝牙', value: '{e0cbf06c-cd8b-4647-bb8a-263b43f0f974}', i18n: 'terminalDevice12' },
        { name: '人机接口设备', value: '{745a17a0-74d3-11d0-b6fe-00a0c90f57da}', i18n: 'terminalDevice13' },
        { name: '软件设备', value: '{62f9c741-b25a-46ce-b54c-9bccce08b6f2}', i18n: 'terminalDevice14' },
        { name: '鼠标和其他指针设备', value: '{4d36e96f-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice15' },
        { name: '通用串行总线控制器', value: '{36fc9e60-c465-11cf-8056-444553540000}', i18n: 'terminalDevice16' },
        { name: '通用串行总线设备', value: '{88bae032-5a81-49f0-bc3d-a4ff138216d6}', i18n: 'terminalDevice17' },
        { name: '图像设备', value: '{6bdd1fc6-810f-11d0-bec7-08002be2092f}', i18n: 'terminalDevice18' },
        { name: '网络适配器', value: '{4d36e972-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice19' },
        { name: '系统设备', value: '{4d36e97d-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice20' },
        { name: '显示适配器', value: '{4d36e968-e325-11ce-bfc1-08002be10318}', i18n: 'terminalDevice21' },
        { name: '音频输入和输出', value: '{c166523c-fe0c-4a94-a586-f1a80cfbbf3e}', i18n: 'terminalDevice22' },
        { name: '电池', value: '{72631e54-78a4-11d0-bcf7-00aa00b8b32a}', i18n: 'terminalDevice23' }
      ]
    },
    querySearch(queryString, cb) {
      const resolutions = this.loadAllGuid()
      resolutions.forEach(item => {
        const name = this.$t('pages.' + item.i18n)
        if (name) { item.name = name }
      });
      const results = queryString ? resolutions.filter(this.createFilter(queryString)) : resolutions;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (resolution) => {
        return (resolution.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    handleSelect(item) {
      // console.log(item);
    },
    devInstanceIdValidator(rule, value, callback) {
      getDevByInstanceId({ devInstanceId: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('pages.otherDeviceLimit_msg9')))
        } else {
          callback()
        }
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          addDevice(this.temp).then(respond => {
            this.submitting = false
            this.deviceVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.notifySuccess(this.$t('text.createSuccess'))
            this.$emit('submitEnd')
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const tempData = Object.assign({}, this.temp)
          updateDevice(tempData).then(respond => {
            this.submitting = false
            this.deviceVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.notifySuccess(this.$t('text.updateSuccess'))
            this.$emit('submitEnd')
          })
        }
      })
    },
    notifySuccess(msg) {
      this.$notify({
        title: this.$t('text.success'),
        message: msg,
        type: 'success',
        duration: 2000
      })
    },
    handleDelete() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      deleteDevice({ ids: toDeleteIds.join(',') }).then(respond => {
        this.gridTable.deleteRowData(toDeleteIds)
        this.notifySuccess(this.$t('text.deleteSuccess'))
      })
    },
    handleDrag() {

    },
    // 数据添加到策略
    addStgData() {
      const datas = this.gridTable.getSelectedDatas();
      console.log('datas', datas)
      const emptyGuidArr = []
      datas.forEach((d) => {
        d.effectType = this.effectTypeRadio
        if (this.effectTypeRadio === 4 && d.classGuid === '') {
          emptyGuidArr.push(d.id)
        }
      })
      if (emptyGuidArr.length > 0) {
        this.$confirmBox('选中的数据中存在类Guid为空的数据是否过滤?', this.$t('text.prompt')).then(() => {
          const addData = datas.filter(d => !emptyGuidArr.includes(d.id))
          this.$emit('add-data', addData)
        }).catch((e) => {
          if (e === 'cancel') {
            this.$emit('add-data', datas)
          }
        })
      } else {
        this.$emit('add-data', datas)
      }
    }
  }

}
</script>
<style lang="scss" scoped>
  >>>.el-radio__inner{
    border: 1px solid #999999;
  }
  >>>.el-radio__label{
    color: #666666;
  }
  .inline-input {
    width: 100%;
  }
  .my-autocomplete {
    li {
      line-height: normal;
      padding: 7px;

      .name {
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .value {
        font-size: 12px;
        color: #b4b4b4;
      }

      .highlighted .value {
        color: #ddd;
      }
    }
  }
</style>
