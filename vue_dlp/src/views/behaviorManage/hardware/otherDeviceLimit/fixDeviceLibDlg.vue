<template>
  <el-dialog
    v-el-drag-dialog
    :title="'新增固定设备'"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="visible"
    width="600px"
  >
    <span style="color: #369de7;">{{ $t('pages.otherDeviceLimit_text1') }}</span>
    <div style="height: auto">
      <tree-menu
        ref="tree"
        :data="treeData"
        :disabled-all-nodes="!formable"
        multiple
        style="height: 350px"
        :checked-keys="checkedKeys"
        @check-change="handleCheckChange"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="confirm">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="cancel">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'FixDeviceLibDlg',
  props: {
    formable: { type: Boolean, default: true },
    treeData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      submitting: false,
      visible: false,
      checkedKeys: [],
      selectedList: []  //  选中的数据
    }
  },
  created() {
  },
  methods: {
    show() {
      this.checkedKeys = []
      this.selectedList = []
      this.visible = true
    },
    confirm() {
      this.$emit('submit', this.selectedList)
      this.visible = false
    },
    cancel() {
      this.visible = false
    },
    handleCheckChange(checkedKey, checkedData, checkedInfo) {
      this.selectedList = Array.isArray(checkedData)
        ? checkedData.filter(item => '0' !== item.parentId).map(item => { return { id: item.dataId, label: item.label }; })
        : checkedData;
    }
  }
}
</script>

<style scoped>

</style>
