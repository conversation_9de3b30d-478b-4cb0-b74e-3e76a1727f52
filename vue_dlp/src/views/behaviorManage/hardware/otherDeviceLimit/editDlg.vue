<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      type="drawer"
      :title="$t('pages.otherDeviceLimit')"
      :stg-code="70"
      :time-able="true"
      :active-able="activeAble"
      :pane-height="900"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="listByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <el-divider v-if="slotName === 1" content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
        <FormItem v-if="slotName !== 1" label-width="0">
          <span style="color: #369de7;">{{ $t('pages.otherDeviceLimit_text1') }}</span>
          <div :style="slotName === 1 ? 'height: 170px;' : 'height: 250px;'">
            <tree-menu
              ref="tree"
              :data="treeData"
              :disabled-all-nodes="!formable"
              multiple
              :checked-keys="checkedKeys"
              @check-change="handleCheckChange"
            />
          </div>
        </FormItem>
        <div v-show="slotName === 1">
          <div v-if="activeAble" label-width="80" style="color: red" >
            <p>{{ $t('pages.otherDeviceLimit_msg1') }}</p>
          </div>
          <FormItem label-width="0">
            <LocalZoomIn parent-tag="el-dialog">
              <div v-if="formable">
                <el-button size="small" :disabled="!formable" @click="addCustom">
                  {{ $t('button.insert') }}
                </el-button>
                <el-button size="small" :disabled="!formable" @click="addFixCustom">
                  {{ $t('pages.insertDeviceCategory') }}
                </el-button>
                <el-button size="small" :disabled="customTableUpdateDisabled" @click="handleUpdateEffectType">
                  {{ i18nConcatText($t('table.effectType'), 'update') }}
                </el-button>
                <el-button size="small" :disabled="!formable || !customTableSelectionData.length>0" @click="deleteCustom">
                  {{ $t('button.delete') }}
                </el-button>
              </div>
              <grid-table
                ref="customTable"
                auto-height
                node-key="dataId"
                :row-datas="customLimitList"
                :selectable="selectable"
                :col-model="colModel"
                :show-pager="false"
                @selectionChangeEnd="customTableSelectionChangeEnd"
              />
            </LocalZoomIn>
          </FormItem>
          <el-divider content-position="left">{{ $t('table.exceptExpr') }}</el-divider>
          <FormItem label-width="0">
            <LocalZoomIn parent-tag="el-dialog">
              <div v-if="formable">
                <el-button size="small" :disabled="!formable" @click="addException">
                  {{ $t('button.insert') }}
                </el-button>
                <el-button size="small" :disabled="!formable || !exceptionTableSelectionData.length>0" @click="exceptionUpdateEffectType">
                  {{ i18nConcatText($t('table.effectType'), 'update') }}
                </el-button>
                <el-button size="small" :disabled="!formable || !exceptionTableSelectionData.length>0" @click="deleteException">
                  {{ $t('button.delete') }}
                </el-button>
              </div>
              <grid-table
                ref="exceptionTable"
                auto-height
                :row-datas="exceptionList"
                :col-model="excelColModel"
                :show-pager="false"
                @selectionChangeEnd="exceptionTableSelectionChangeEnd"
              />
            </LocalZoomIn>
          </FormItem>
          <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
          <FormItem label-width="40px" prop="action">
            {{ $t('pages.otherDeviceMsg') }}
          </FormItem>
          <!-- 响应规则 -->
          <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
          <ResponseContent
            :select-style="{ 'margin-top': '5px', 'margin-left': '13px', 'margin-top': '20px' }"
            :show-select="true"
            :editable="formable"
            read-only
            :prop-check-rule="!!temp.isAlarm"
            :show-check-rule="true"
            :prop-rule-id="temp.alarmStgId"
            @ruleIsCheck="getRuleIsCheck"
            @getRuleId="getRuleId"
          />
        </div>
      </template>
    </stg-dialog>
    <el-dialog
      v-el-drag-dialog
      :title="i18nConcatText(this.$t('table.effectType'), 'update')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="updateEffectTypeVisible"
      width="500px"
    >
      <Form ref="updateEffectTypeForm" label-position="right" label-width="90px">
        <FormItem :label="$t('table.effectType')">
          <el-radio-group v-model="effectTypeRadio">
            <el-radio :label="1">{{ $t('pages.otherDeviceLimitOption1') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.otherDeviceLimitOption2') }}</el-radio>
            <el-radio :label="4">
              {{ $t('pages.otherDeviceLimitOption3') }}
              <el-tooltip effect="dark" placement="top">
                <div slot="content">{{ $t('pages.otherDeviceLimit_msg2') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateEffectType()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="updateEffectTypeVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <device-lib-dlg ref="deviceLibDlg" :use-fun="addOption" @add-data="addData"/>
    <fix-device-lib-dlg ref="fixDeviceLibDlg" :tree-data="treeData" :formable="formable" @submit="fixDeviceLibSubmit"/>
  </div>
</template>

<script>
import { createStrategy, getTree, listByName, updateStrategy } from '@/api/behaviorManage/hardware/otherDeviceLimit'
import DeviceLibDlg from './deviceLibDlg'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import FixDeviceLibDlg from './fixDeviceLibDlg'

export default {
  name: 'OtherDeviceDlg',
  components: { DeviceLibDlg, ResponseContent, FixDeviceLibDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      temp: {}, // 接收并显示数据
      defaultTemp: {
        id: undefined,
        timeId: 1,
        name: '',
        remark: '',
        active: false,
        objectType: '',
        objectId: undefined,
        limitFunctionList: [],
        selectedIdList: [],
        // customLimitStatus: false,
        // exceptionConfigStatus: false,
        customAndException: [],
        alarmStgId: undefined,
        isAlarm: 0
      }, // 修改后的数据向 stg-dialog 组件传值
      customLimitList: [], // 显示使用
      exceptionList: [], // 显示使用
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateStgName'), trigger: 'blur' }
        ]
      },
      submitting: false,
      slotName: undefined,
      checkedKeys: [],
      treeData: [],
      treeDataMap: {},
      updateEffectTypeVisible: false,
      updateEffectTypeDlg: 'custom', // custom 自定义， exception 例外
      addOption: 'custom', // custom 自定义， exception 例外
      limitType: {
        'custom': 2,
        'exception': 1
      }, //  custom 自定义--->2 限制，exception 例外--->1 放行
      effectTypeRadio: 1,
      customTableSelectionData: [],
      exceptionTableSelectionData: [],
      colModel: [
        { prop: 'effectType', label: 'effectType', width: '150', sort: true, formatter: this.effectTypeFormatter },
        { prop: 'devName', label: 'deviceNameOrCategoryName', width: '150', sort: true },
        { prop: 'devInstanceId', label: 'devInstanceId', width: '150', sort: true },
        { prop: 'classGuid', label: 'classGuid', width: '150', sort: true }
      ],
      excelColModel: [
        { prop: 'effectType', label: 'effectType', width: '150', sort: true, formatter: this.effectTypeFormatter },
        { prop: 'devName', label: 'deviceName', width: '150', sort: true },
        { prop: 'devInstanceId', label: 'devInstanceId', width: '150', sort: true },
        { prop: 'classGuid', label: 'classGuid', width: '150', sort: true }
      ],
      effectTypes: {
        1: this.$t('pages.otherDeviceLimitOption1'),
        2: this.$t('pages.otherDeviceLimitOption2'),
        4: this.$t('pages.otherDeviceLimitOption3')
      }
    }
  },
  computed: {
    customTable() {
      return this.$refs['customTable']
    },
    exceptionTable() {
      return this.$refs['exceptionTable']
    },
    customTableUpdateDisabled() {
      //  若选中的数据都是类型为：设备分类的数据时，修改按钮禁用
      return !this.formable || !this.customTableSelectionData.filter(t => !t.type).length > 0;
    }
  },
  created() {
    this.loadTree()
    this.splitData()
    this.temp = Object.assign({}, this.defaultTemp)
  },
  methods: {
    createStrategy,
    updateStrategy,
    listByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.treeData = this.treeDataMap[name]
      this.checkedKeys.splice(0)
      this.$nextTick(() => {
        if (this.temp.selectedIdList) {
          this.checkedKeys.push(...this.temp.selectedIdList)
        }
        this.splitData() // 拆分数据
        this.mergeData()  //  合并数据
      })
    },
    handleCheckChange(checkedKey, checkedData, checkedInfo) {
      this.temp.selectedIdList = checkedInfo.checkedKeys
      this.temp.selectedLabelList = Array.isArray(checkedData) ? checkedData.filter(item => '0' !== item.parentId).map(item => item.label) : []
    },
    loadTree() {
      getTree().then(res => {
        if (res.data) {
          this.treeData = res.data
          this.treeDataMap = {}
          res.data.forEach(nodeData => {
            if (nodeData.dataType) {
              const osTypes = this.numToArr(nodeData.dataType)
              for (let i = 0; i < osTypes.length; i++) {
                const osType = osTypes[i]
                const cloneNodeData = JSON.parse(JSON.stringify(nodeData))
                if (cloneNodeData.children && cloneNodeData.children.length > 0) {
                  cloneNodeData.children = this.filterChildTreeData(cloneNodeData.children, osType)
                }
                if (!this.treeDataMap[osType]) {
                  this.treeDataMap[osType] = [cloneNodeData]
                } else {
                  this.treeDataMap[osType].push(cloneNodeData)
                }
              }
            }
          })
        }
      })
    },
    filterChildTreeData(nodeDatas, osType) {
      const childNodeData = []
      nodeDatas.forEach(nodeData => {
        const osTypes = this.numToArr(nodeData.dataType)
        if (osTypes.indexOf(osType) > -1) {
          if (nodeData.children && nodeData.children.length > 0) {
            nodeData.children = this.filterChildTreeData(nodeData.children, osType)
          }
          childNodeData.push(nodeData)
        }
      })
      return childNodeData
    },
    handleFilter() {
    },
    handleDrag() {
    },
    handleCreate() {
      this.checkedKeys.splice(0)
      this.$refs['tree'] && this.$refs['tree'].clearFilter()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate: function(row) {
      this.$refs['tree'] && this.$refs['tree'].clearFilter()
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(row) {
    },
    formatFormData(row) {
      return row;
    },
    validateForm(row) {
      if (row.isAlarm && !row.alarmStgId) {
        return false;
      }
      return true;
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    // 自定义限制
    effectTypeFormatter: function(row, data) {
      return row.type && row.type === 'otherDev' ? this.$t('pages.deviceCategory') : this.effectTypes[data]
    },
    customTableSelectionChangeEnd(val) {
      this.customTableSelectionData = val
      // 修改生效类型数据回显
      if (val && val.length > 0) {
        this.effectTypeRadio = val[val.length - 1].effectType
      }
    },
    addCustom() {
      this.addOption = 'custom'
      this.$refs['deviceLibDlg'].show();
    },
    addFixCustom() {
      this.$refs['fixDeviceLibDlg'].show()
    },
    handleUpdateEffectType() {
      this.updateEffectTypeVisible = true
      this.updateEffectTypeDlg = 'custom'
    },
    //  改变生效类型，仅类型为：设备的允许修改，类型为：设备分类 的不允许修改
    updateEffectType() {
      if ('custom' === this.updateEffectTypeDlg) {
        const customTableSelectionFixData = this.customTableSelectionData.filter(c => !c.type);
        if (customTableSelectionFixData.length > 0) {
          const selectIds = customTableSelectionFixData.map(c => c.dataId);
          this.customLimitList.forEach(custon => {
            if (selectIds.includes(custon.dataId)) {
              custon.effectType = this.effectTypeRadio
              this.customTable.updateRowData(custon, this.customLimitList)
            }
          })
          this.updateEffectTypeVisible = false
          this.unionData()
          this.customTableSelectionData.splice(0) // 删除customTable列表勾选
        }
      }
      if ('exception' === this.updateEffectTypeDlg) {
        if (this.exceptionTableSelectionData.length > 0) {
          const selectIds = this.exceptionTableSelectionData.map(c => c.id);
          this.exceptionList.forEach(exeception => {
            if (selectIds.includes(exeception.id)) {
              exeception.effectType = this.effectTypeRadio
              this.exceptionTable.updateRowData(exeception, this.exceptionList)
            }
          })
          this.updateEffectTypeVisible = false
          this.unionData()
          this.exceptionTableSelectionData.splice(0) // 删除exceptionTable列表勾选
        }
      }
    },
    deleteCustom() {
      if (this.customTableSelectionData.length > 0) {
        this.customTableSelectionData.forEach(custom => {
          const index = this.customLimitList.findIndex(c => c.id === custom.id)
          this.customLimitList.splice(index, 1)
        })
        this.unionData()
        this.customTableSelectionData.splice(0) // 删除customTable列表勾选
      }
    },
    addData(datas) {
      const data = []
      datas.forEach(d => {
        delete d.createTime
        delete d.modifyTime
        delete d.remark
        d.dataId = d.id + ''
        data.push(d)
      })
      if (this.existUnionData(data)) {
        // 添加的数据已存在限制或例外中
        this.$notify({
          title: this.$t('text.prompt'),
          message: this.$t('pages.otherDeviceLimit_msg3'),
          type: 'warning',
          duration: 2000
        })
      }
      // 选中数据和所有数据(去重)
      const customAndExceptionIds = this.temp.customAndException.map(item => item.dataId)
      const dataIds = data.map(item => item.dataId)
      const resIds = dataIds.filter(id => customAndExceptionIds.includes(id));
      const arr = data.filter(item => !resIds.includes(item.dataId))
      if ('custom' === this.addOption) {
        // 1 放行 2 限制
        data.forEach((item) => {
          item.limitType = 2
        })
        // 选中数据和所有数据(合并)
        const union = [...this.customLimitList, ...arr]
        this.customLimitList = union
      }
      if ('exception' === this.addOption) {
        // 1 放行 2 限制
        data.forEach((item) => {
          item.limitType = 1
        })
        // 选中数据和所有数据(合并)
        const union = [...this.exceptionList, ...arr]
        this.exceptionList = union
      }
      this.unionData() // 合并限制+例外数据
      this.$refs['deviceLibDlg'].hide()
    },
    // 例外配置
    exceptionTableSelectionChangeEnd(val) {
      this.exceptionTableSelectionData = val
      // 修改生效类型数据回显
      if (val && val.length > 0) {
        this.effectTypeRadio = val[val.length - 1].effectType
      }
    },
    addException() {
      this.addOption = 'exception'
      this.$refs['deviceLibDlg'].show();
    },
    exceptionUpdateEffectType() {
      this.updateEffectTypeVisible = true
      this.updateEffectTypeDlg = 'exception'
    },
    deleteException() {
      if (this.exceptionTableSelectionData.length > 0) {
        this.exceptionTableSelectionData.forEach(exception => {
          const index = this.exceptionList.findIndex(c => c.id === exception.id)
          this.exceptionList.splice(index, 1)
        })
        this.unionData()
        this.exceptionTableSelectionData.splice(0) // 删除列表勾选
      }
    },
    unionData() {
      // 合并数据
      this.temp.customAndException.splice(0)
      //  customLimitList 过滤掉“其他设备”即：type='otherDev'的数据
      const customLimits = this.customLimitList.filter(item => !item.type)
      this.temp.customAndException = [...customLimits, ...this.exceptionList]
      //  设置被选中的其他设备id
      const otherDevs = this.customLimitList.filter(item => item.type && item.type === 'otherDev')
      this.temp.selectedIdList = otherDevs.map(item => item.otherDeviceId)
      this.temp.selectedLabelList = otherDevs.map(item => item.devName)
    },
    //  将temp.customAndException 的数据拆分到customLimitList 和 exceptionList中
    splitData() {
      // temp ---> this.customLimitList、this.exceptionList
      if (this.temp.customAndException) {
        this.customLimitList.splice(0)
        this.exceptionList.splice(0)
        this.temp.customAndException.forEach(item => {
          item.dataId = item.id + ''
          if (item.limitType === 2) { // 1 放行 2 限制
            this.customLimitList.push(item)
          }
          if (item.limitType === 1) { // 1 放行 2 限制
            this.exceptionList.push(item)
          }
        })
      }
    },
    //  将 “其他设备”合并到自定义限制（customLimitList）中
    mergeData() {
      //  根据Id构建“其他设备”数据
      const otherDevices = this.buildFixDevice(this.generateDataById(this.temp.selectedIdList))
      if (otherDevices) {
        let maxId = new Date().getTime()
        otherDevices.forEach(data => {
          ++maxId
          this.customLimitList.push(Object.assign({ id: maxId, dataId: 'T' + maxId }, data))
        })
      }
    },
    //  根据{id, label} 生成“其他设备”数据
    buildFixDevice(selectList) {
      const list = []
      if (selectList) {
        selectList.forEach(item => {
          list.push({
            id: 'otherDev' + item.id,
            dataId: 'otherDev' + item.id,
            devName: item.label,
            effectType: 1,  //  设备名称
            type: 'otherDev',  //  其他设备
            otherDeviceId: item.id  //  其他设备Id
          })
        })
      }
      return list;
    },
    generateDataById(ids) {
      const list = []
      if (ids) {
        ids.forEach(id => {
          const data = this.treeFindIndex(this.treeData, id);
          if (data) {
            list.push({ id: data.dataId, label: data.label })
          }
        })
      }
      return list;
    },
    //  treeData树节点，id：需要查找的Id
    treeFindIndex(treeData, id) {
      let result = null
      for (let i = 0; i < treeData.length; i++) {
        const data = treeData[i]
        if (data.dataId == id) {
          return data
        }
        if (data.children) {
          result = this.treeFindIndex(data.children, id)
          if (result) {
            return result;
          }
        }
      }
      return result
    },
    existUnionData(data) {
      // 添加的data 是否存在this.temp.customAndException
      // 存在更新数据
      for (let i = 0, len = this.temp.customAndException.length; i < len; i++) {
        for (let j = 0, length = data.length; j < length; j++) {
          if (this.temp.customAndException[i].id === data[j].id) {
            this.temp.customAndException[i].devName = data[j].devName
            this.temp.customAndException[i].devInstanceId = data[j].devInstanceId
            this.temp.customAndException[i].classGuid = data[j].classGuid
            if (data[j].effectType != null) {
              if (this.limitType[this.addOption] === this.temp.customAndException[i].limitType) {
                this.temp.customAndException[i].effectType = data[j].effectType
              }
            }
            return true
          }
        }
      }
      return false
    },
    getRuleId(value) {
      this.temp.alarmStgId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    fixDeviceLibSubmit(selectedList) {
      //  检查是否存在已配置的“其他设备”，存在则过滤，且提示
      if (selectedList) {
        //  查询列表中已存在的“其他设备”的Id
        const otherSelectedIds = this.customLimitList.filter(item => item.type && item.type === 'otherDev').map(item => item.otherDeviceId)
        //  需要增加的“其他设备”
        const needAddList = otherSelectedIds.length === 0 ? selectedList : selectedList.filter(item => !otherSelectedIds.includes(item.id));
        this.customLimitList.push(...this.buildFixDevice(needAddList))
        if (selectedList.length !== needAddList.length) {
          this.$message({
            type: 'warning',
            message: this.$t('pages.otherDeviceReErrorMsg'),
            duration: 2000
          })
        }
        this.unionData()
      }
    },
    //  “其他设备”不支持选中, "其他设备”代表之前的 “通讯接口设备、USB设备、网络设备、其他”的子节点
    selectable(row) {
      return true
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.noBold label{
    font-weight: 400;
  }
</style>
