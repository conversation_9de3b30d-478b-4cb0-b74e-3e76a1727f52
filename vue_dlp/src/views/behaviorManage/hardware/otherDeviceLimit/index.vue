<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button icon="el-icon-setting" size="mini" @click="handleConfigLib">
          {{ $t('button.deviceConfigurationLibrary') }}
        </el-button>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd">
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;" v-html="stgFormatter(props.detail)"></span>
          </div>
        </template>
      </grid-table>
    </div>
    <other-device-dlg ref="stgDlg" :formable="formable" :active-able="treeable" :entity-node="checkedEntityNode" @submitEnd="submitEnd"/>
    <device-lib-dlg ref="deviceLibDlg" :use-fun="'deviceLib'" @submitEnd="submitEnd"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importOtherDeviceLimitStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { getStrategyList, deleteStrategy } from '@/api/behaviorManage/hardware/otherDeviceLimit'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import OtherDeviceDlg from './editDlg'
import DeviceLibDlg from './deviceLibDlg'
import { osTypeIconFormatter, stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

// 设备使用控制
export default {
  name: 'OtherDeviceLimit',
  components: { OtherDeviceDlg, DeviceLibDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 70,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '300', ellipsis: false, type: 'popover', originData: true, formatter: this.stgFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true, // 是否显示树
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    stgFormatter(row, data) {
      let msg = ''
      let msg1 = ''
      let msg2 = ''
      if (row.osType == 1) {
        // windows系统的策略
        if (row.selectedLabelList != undefined && row.selectedLabelList.length > 0) {
          let limit = ''
          for (var i = 0; i < row.selectedLabelList.length; i++) {
            limit += limit.length > 0 ? '、' + row.selectedLabelList[i] : row.selectedLabelList[i]
          }
          msg += limit
        }
        if (row.customAndException != undefined && row.customAndException.length > 0) {
          for (var j = 0; j < row.customAndException.length; j++) {
            if (row.customAndException[j].limitType == 2) {
              // 获取自定义禁用设备
              msg += msg.length ? '、' + row.customAndException[j].checkValue : row.customAndException[j].checkValue
            } else {
              // 不禁用的设备
              msg1 += msg1.length ? '、' + row.customAndException[j].checkValue : row.customAndException[j].checkValue
            }
          }
        }
      } else {
        // linux或mac类型
        if (row.selectedLabelList != undefined && row.selectedLabelList.length > 0) {
          let limit1 = ''
          for (var k = 0; k < row.selectedLabelList.length; k++) {
            limit1 += limit1.length > 0 ? '、' + row.selectedLabelList[k] : row.selectedLabelList[k]
          }
          msg += limit1
        }
      }
      msg = msg.length ? this.$t('pages.otherDeviceLimit_text2') + '：' + msg : msg;
      msg1 = msg1.length ? this.$t('pages.otherDeviceLimit_text3') + '：' + msg1 : msg1;
      msg2 = msg;
      msg2 += msg2.length && msg1.length ? '，' + msg1 : msg1;

      //  设置响应规则
      if (row.osType === 1 && ((row.selectedLabelList != undefined && row.selectedLabelList.length > 0) || (row.customAndException != undefined && row.customAndException.length > 0)) &&
        row.alarmStgId && row.isAlarm) {
        msg2 += msg2.length ? '，' + this.$t('pages.triggerResponseRule') : this.$t('pages.triggerResponseRule');
      }
      return msg2
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].handleUpdate(row, this.formable)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleConfigLib() {
      this.$refs['deviceLibDlg'].show()
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>
