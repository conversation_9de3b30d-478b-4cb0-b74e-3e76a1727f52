<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.eventType" :value="query.eventType">
          <span>{{ $t('pages.operateTypeOptions1') }}：</span>
          <el-select v-model="query.eventType" style="width: 150px">
            <el-option :label="$t('pages.collectAll')" value=""/>
            <el-option :label="$t('pages.process_Msg2')" :value="1"></el-option>
            <el-option :label="$t('pages.programStart')" :value="2"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.processName" :value="query.processName">
          <span>{{ $t('pages.programName') }}：</span>
          <el-input v-model="query.processName" v-trim clearable style="width: 150px;"/>
        </SearchItem>
        <SearchItem model-key="query.responseType" :value="query.responseType">
          <span>{{ $t('table.responseType') }}：</span>
          <el-select v-model="query.responseType" style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null"/>
            <el-option :value="1" :label="$t('pages.screenshot')"/>
            <el-option :value="2" :label="$t('pages.recordingScreen')"/>
          </el-select>
        </SearchItem>

        <audit-log-exporter slot="append" v-permission="'324'" :request="exportFunc"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'436'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('436')"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        :autoload="autoload"
        @handleIcon="(rowDetail,item)=>handleIcon(rowDetail,item)"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <MonitorDetail
      ref="detail"
      :detail-dialog-visible="detailDialogVisible"
      :query-detail="queryDetail"
      :type="'sensitiveOp'"
      @handleClose="detailDialogVisible=false"
    />
    <RecordDetail
      ref="record"
      :detail-dialog-visible="recordDialogVisible"
      :query-detail="queryRecord"
      @handleClose="recordDialogVisible=false"
    />
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.programActionDetails')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.eventTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.eventType')">
            {{ eventTypeFormatter(rowDetail, rowDetail.eventType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.responseType')">
            {{ actionTypeFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.treatmentMeasure')">
            <svg-icon
              :icon-class="iconObj[rowDetail.actionType]"
              :title="1 === rowDetail.actionType ? $t('pages.screenshot') : $t('pages.recordingScreen')"
              :style="$store.getters.desensitizeContentAble && rowDetail.eventType === 1 ? '' : 'cursor:pointer;color: #68a8d0'"
              @click="handleIcon(rowDetail,iconObj[rowDetail.actionType])"
            />
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.programName')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.path')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, exportSensitiveOpLog, deleteLog } from '@/api/behaviorManage/hardware/sensitiveOpLog'
import MonitorDetail from './MonitorDetail'
import RecordDetail from './RecordDetail'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'SensitiveOpLog',
  components: { MonitorDetail, RecordDetail },
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'eventTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '130', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '100', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'processName', label: 'programName', width: '150' },
        { prop: 'eventGuid', label: 'eventGuid', width: '150', hidden: true },
        { prop: 'eventType', label: 'eventType', width: '150', formatter: this.eventTypeFormatter },
        { prop: 'filePath', label: 'path', width: '150' },
        { prop: 'actionType', label: 'responseType', width: '70', formatter: this.actionTypeFormatter },
        { prop: '', label: 'treatmentMeasure', width: '100', iconFormatter: this.iconClassFormat },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('229'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      iconObj: {
        1: 'alarm-06',
        2: 'alarm-07'
      },
      query: { // sensitiveOpLog 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '', // 结束日期，显示在弹框里的
        eventType: '',
        processName: '',
        isTimes: false,
        responseType: null,  //  响应类型
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      queryDetail: { // sensitive_op_detail_log 查询条件
        page: 1,
        createDate: '',
        eventGuid: '',
        sortName: 'createTime',
        sortOrder: 'asc',
        isTimes: false
      },
      queryRecord: { // 录屏 查询条件
        page: 1,
        sortName: 'createTime',
        sortOrder: 'asc',
        isTimes: false,
        objectId: '',
        recScreenTime: '',
        eventGuid: '',
        searchReport: 1
      },
      showTree: true,
      detailDialogVisible: false, // 截屏 显示|隐藏
      recordDialogVisible: false, // 录屏 显示|隐藏
      eventTypes: { // 事件类型 1 读取敏感文件 | 2 程序启动
        1: this.$t('pages.process_Msg2'),
        2: this.$t('pages.programStart')
      },
      actionTypes: { // 应对操作类型 1 截屏 | 2 录屏 | 3 截屏+录屏
        1: this.$t('pages.screenshot'),
        2: this.$t('pages.recordingScreen')
      },
      detailDatas: [],
      monitorDetailName: this.$t('pages.programActionDetails'), // 程序动作监控详情
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      selection: [],
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  created() {
    addViewVideoBtn(this, (row) => {
      if (this.$store.getters.desensitizeContentAble && row.eventType === 1) {
        // 读敏感文件的操作，并且是脱敏的话，则不允许查看录屏
        return false
      }
      return this.hasPermission('229')
    })
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
      this.queryRecord.searchReport = this.query.searchReport
    },
    // 截屏 详情
    handleDetail(row) {
      this.queryDetail.page = 1
      this.queryDetail.eventGuid = row.eventGuid
      this.queryDetail.createDate = row.createTime
      this.queryDetail.searchReport = this.query.searchReport
      this.detailDialogVisible = true
      this.$nextTick(() => {
        const detailGrid = this.$refs.detail.$refs.detailGrid
        detailGrid.execRowDataApi(this.queryDetail)
      })
    },
    // 录屏 详情
    recordDetail(row) {
      this.queryRecord.objectId = row.terminalId
      this.queryRecord.eventGuid = row.eventGuid
      this.queryRecord.recScreenTime = row.createTime
      this.recordDialogVisible = true
      this.$nextTick(() => {
        const detailGrid = this.$refs.record.$refs.detailGrid
        detailGrid.execRowDataApi(this.queryRecord)
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    eventTypeFormatter: function(row, data) {
      return this.eventTypes[data]
    },
    actionTypeFormatter: function(row, data) {
      return this.actionTypes[data]
    },
    // 图标显示
    iconClassFormat(row) {
      const actionType = row.actionType
      const iconArr = []
      if (actionType === 1) {
        const iconClass = this.iconObj[actionType]
        const iconObj = { class: iconClass, title: this.$t('pages.screenshot') }
        iconObj.className = 'text-button-icon'
        iconArr.push(iconObj)
      }
      if (actionType === 2) {
        const iconClass = this.iconObj[actionType]
        const iconObj = { class: iconClass, title: this.$t('pages.recordingScreen') }
        iconObj.className = 'text-button-icon'
        iconArr.push(iconObj)
      }
      if (this.$store.getters.desensitizeContentAble && row.eventType === 1) {
        // 读敏感文件的操作，并且是脱敏的话，则图标颜色置灰
        iconArr.forEach(item => { item['css'] = {} })
      }
      return iconArr
    },
    // 图片 事件
    handleIcon(row, iconClass) {
      if (!this.hasPermission('229')) {
        return
      }
      if (this.$store.getters.desensitizeContentAble && row.eventType === 1) {
        // 读敏感文件的操作，并且是脱敏的话，则不允许点击查看
        return false
      }
      const actionType = row.actionType
      if (actionType === 1) {
        this.handleDetail(row)
      } else if (actionType === 2) {
        this.recordDetail(row)
      }
    },
    exportFunc(exportType) {
      return exportSensitiveOpLog({ exportType, ...this.query })
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '229', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
