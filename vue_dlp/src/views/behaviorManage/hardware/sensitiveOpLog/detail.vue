<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.programActionDetails')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.eventTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.eventType')">
            {{ eventTypeFormatter(rowDetail, rowDetail.eventType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.responseType')">
            {{ actionTypeFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.treatmentMeasure')">
            <svg-icon
              :icon-class="iconObj[rowDetail.actionType]"
              :title="1 === rowDetail.actionType ? $t('pages.screenshot') : $t('pages.recordingScreen')"
              :style="$store.getters.desensitizeContentAble && rowDetail.eventType === 1 ? '' : 'cursor:pointer;color: #68a8d0'"
              @click="handleIcon(rowDetail,iconObj[rowDetail.actionType])"
            />
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.programName')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.path')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <MonitorDetail
      ref="detail"
      :detail-dialog-visible="detailDialogVisible"
      :query-detail="queryDetail"
      :type="'sensitiveOp'"
      @handleClose="detailDialogVisible=false"
    />
    <RecordDetail
      ref="record"
      :detail-dialog-visible="recordDialogVisible"
      :query-detail="queryRecord"
      @handleClose="recordDialogVisible=false"
    />
  </div>
</template>
<script>

import { formatFileSize } from '@/utils';
import MonitorDetail from './MonitorDetail'
import RecordDetail from './RecordDetail'

export default {
  name: 'SensitiveOpLogDetail',
  components: { RecordDetail, MonitorDetail },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      eventTypes: { // 事件类型 1 读取敏感文件 | 2 程序启动
        1: this.$t('pages.process_Msg2'),
        2: this.$t('pages.programStart')
      },
      actionTypes: { // 应对操作类型 1 截屏 | 2 录屏 | 3 截屏+录屏
        1: this.$t('pages.screenshot'),
        2: this.$t('pages.recordingScreen')
      },
      iconObj: {
        1: 'alarm-06',
        2: 'alarm-07'
      },
      query: { // sensitiveOpLog 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '', // 结束日期，显示在弹框里的
        eventType: '',
        processName: '',
        isTimes: false,
        responseType: null,  //  响应类型
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      queryDetail: { // sensitive_op_detail_log 查询条件
        page: 1,
        createDate: '',
        eventGuid: '',
        sortName: 'createTime',
        sortOrder: 'asc',
        isTimes: false
      },
      queryRecord: { // 录屏 查询条件
        page: 1,
        sortName: 'createTime',
        sortOrder: 'asc',
        isTimes: false,
        objectId: '',
        recScreenTime: '',
        eventGuid: '',
        searchReport: 1
      },
      detailDialogVisible: false, // 截屏 显示|隐藏
      recordDialogVisible: false // 录屏 显示|隐藏
    }
  },
  computed: {
  },
  methods: {
    formatFileSize,
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    handleIcon(row, iconClass) {
      if (!this.hasPermission('229')) {
        return
      }
      if (this.$store.getters.desensitizeContentAble && row.eventType === 1) {
        // 读敏感文件的操作，并且是脱敏的话，则不允许点击查看
        return false
      }
      const actionType = row.actionType
      if (actionType === 1) {
        this.handleDetail(row)
      } else if (actionType === 2) {
        this.recordDetail(row)
      }
    },
    // 截屏 详情
    handleDetail(row) {
      this.queryDetail.page = 1
      this.queryDetail.eventGuid = row.eventGuid
      this.queryDetail.createDate = row.createTime
      this.queryDetail.searchReport = this.query.searchReport
      this.detailDialogVisible = true
      this.$nextTick(() => {
        const detailGrid = this.$refs.detail.$refs.detailGrid
        detailGrid.execRowDataApi(this.queryDetail)
      })
    },
    // 录屏 详情
    recordDetail(row) {
      this.queryRecord.objectId = row.terminalId
      this.queryRecord.eventGuid = row.eventGuid
      this.queryRecord.recScreenTime = row.createTime
      this.recordDialogVisible = true
      this.$nextTick(() => {
        const detailGrid = this.$refs.record.$refs.detailGrid
        detailGrid.execRowDataApi(this.queryRecord)
      })
    },
    eventTypeFormatter: function(row, data) {
      return this.eventTypes[data]
    },
    actionTypeFormatter: function(row, data) {
      return this.actionTypes[data]
    },
    handleDownload: function(row) {
      this.$emit('downloadAuditLogFile', row)
    }
  }
}
</script>

