<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :modal="false"
      :title="monitorDetailName"
      :visible.sync="detailDialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <Form ref="detailForm" label-position="right" label-width="100px" style="width: 700px; margin-left:30px;">
        <grid-table
          ref="detailGrid"
          :height="400"
          :multi-select="false"
          :col-model="detailColModel"
          :row-data-api="rowDataApi"
        />
      </Form>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.picture')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="imgDialogVisible"
      width="800px"
      custom-class="sensitive-dialog"
    >
      <el-carousel trigger="click" :arrow="imgUrlList.length>1?'always':'never'" height="450px" :loop="true" :autoplay="false">
        <el-carousel-item v-for="(imgUrl, index) in imgUrlList" :key="index">
          <el-image
            :src="imgUrl"
            fit="fill"
            style="width: 100%; height: 100%;"
          ></el-image>
        </el-carousel-item>
      </el-carousel>
      <el-tooltip class="item" effect="dark" :content="$t('pages.fullScreen')" placement="top-start">
        <i class="el-icon-full-screen full-screen" @click="showViewer=true"></i>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" :content="$t('pages.next')" placement="top-start">
        <i class="el-icon-caret-bottom arrow-down" @click="handleArrow(2)"></i>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" :content="$t('pages.previous')" placement="top-start">
        <i class="el-icon-caret-top arrow-up" @click="handleArrow(1)"></i>
      </el-tooltip>
    </el-dialog>

    <image-viewer
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="imgUrlList"
      :z-index="9999"
      style="position:absolute"
    />

    <audit-file-downloader
      ref="auditFileDownloader"
      :show="false"
      view-image
      :append-to-body="false"
      :before-download="beforeDownload"
      :really-download="reallyDownloadImage"
    />
  </div>
</template>

<script>
import { downloadFile } from '@/api/behaviorManage/hardware/sensitiveOpLog'
import { getLogDetailPage } from '@/api/behaviorManage/hardware/sensitiveOpLog'
import { getScreenshotLogPage } from '@/api/behaviorAuditing/alarmDetailLog'
import ImageViewer from '@/components/ImageViewer'

export default {
  name: 'MonitorDetail',
  components: { ImageViewer },
  props: {
    detailDialogVisible: {
      type: Boolean,
      default: false
    },
    queryDetail: {
      type: Object,
      default() {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    },
    monitorDetailName: {
      type: String,
      default: function() {
        return this.$t('pages.screenshotDetails')
      }
    }

  },
  data() {
    return {
      imgDialogVisible: false,
      imgTypes: { 2: 'data:image/gif;base64', 3: 'data:image/jpeg;base64', 4: 'data:image/png;base64' },
      imgUrlList: [],
      curRow: undefined,
      actionTypes: {
        1: this.$t('pages.screenshot'),
        2: this.$t('pages.recordingScreen'),
        3: this.$t('pages.shotRecordingScreen')
      },
      showViewer: false,
      tempTask: {},
      defaultTempTask: {
        backType: 17,
        type: 2,
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      }
    }
  },
  computed: {
    detailColModel() {
      return this.type === 'sensitiveOp' ? [
        { prop: 'fileIndex', label: 'fileIndex', width: '30' },
        { prop: 'eventGuid', label: 'eventGuid', width: '150', hidden: true },
        { prop: 'actionType', label: 'responseType', width: '150', formatter: this.actionTypeFormatter },
        { prop: 'actionGuid', label: 'responseGuid', width: '150', hidden: true },
        { label: 'operate', type: 'button', fixedWidth: '100',
          buttons: [
            { label: 'viewPicture', click: this.handleLoadDown, disabledFormatter: this.showImgFormatter }
          ]
        }
      ] : [
        { prop: 'fileIndex', label: 'fileIndex', width: '150' },
        { prop: 'capscreenGuid', label: 'screenshotGuid', width: '150', hidden: true },
        { label: 'operate', type: 'button', fixedWidth: '100',
          buttons: [
            { label: 'viewPicture', click: this.handleLoadDown, disabledFormatter: this.showImgFormatter }
          ]
        }
      ]
    }
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.queryDetail, option)
      return this.type === 'sensitiveOp' ? getLogDetailPage(searchQuery) : getScreenshotLogPage(searchQuery)
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.backupServerId
      this.tempTask.fileGuid = row.uploadFileGuid
      this.tempTask.fileName = 'screenshot.jpg'
      this.curRow = row
      return this.tempTask
    },
    handleLoadDown(row) {
      this.imgUrlList = []
      this.$refs['auditFileDownloader'] && this.$refs['auditFileDownloader'].handleDownload(row)
    },
    reallyDownloadImage(tempTasks, taskFile, closeProgress) {
      downloadFile(tempTasks[0]).then(res => {
        taskFile.percent = 100
        closeProgress()
        const imgType = this.imgTypes[res.data.imgType]
        this.imgUrlList = res.data.pictureData.map(item => imgType + ',' + item)
        this.imgDialogVisible = true
      }).catch(() => {
        closeProgress()
        this.imgDialogVisible = false
      })
    },
    showImgFormatter(data, btn) {
      return !data.uploadFileGuid
    },
    actionTypeFormatter: function(row, data) {
      return this.actionTypes[data]
    },
    closeViewer() {
      this.showViewer = false
    },
    handleArrow(type) {
      const detailDatas = this.$refs.detailGrid.getDatas()
      const index = detailDatas.findIndex(item => item.id === this.curRow.id)
      if (type === 1) {
        const lastRow = detailDatas[index - 1]
        lastRow ? this.handleLoadDown(lastRow) : this.$message({ message: this.$t('pages.sensitiveOpLog_Msg') })
      } else {
        const nextRow = detailDatas[index + 1]
        nextRow ? this.handleLoadDown(nextRow) : this.$message({ message: this.$t('pages.sensitiveOpLog_Msg1') })
      }
    },
    handleClose() {
      this.$emit('handleClose')
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-carousel__arrow{
  background-color: rgba(31,45,61,.77);
}
.sensitive-dialog{
  >>>.el-dialog__body{
    padding: 0;
  }
}
.full-screen{
  position: absolute;
  right:45px;
  top:10px;
  font-size:15px;
  color: #333;
  cursor: pointer;
}
.arrow-up{
  position: absolute;
  right:70px;
  top:7px;
  font-size:20px;
  color: #333;
  cursor: pointer;
}
.arrow-down{
  position: absolute;
  right:95px;
  top:7px;
  font-size:20px;
  color: #333;
  cursor: pointer;
}
</style>
