<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button icon="el-icon-setting" size="mini" @click="handleConfigLib">
          {{ $t('button.pictureLibrary') }}
        </el-button>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <personalize-policy-dlg ref="personalizePolicyDlg" :formable="formable" :active-able="treeable" :entity-click="entityClick" :entity-node="checkedEntityNode" @submitEnd="submitEnd"/>
    <picture-lib-dlg ref="deviceLibDlg" :use-fun="'pictureLib'" @submitEnd="submitEnd"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importPersonalizePolicyStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="false"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import { getStrategyList, deleteStrategy } from '@/api/behaviorManage/hardware/personalizePolicy'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import PersonalizePolicyDlg from './editDlg'
import PictureLibDlg from './pictureLibDlg'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'PersonalizePolicy',
  components: { PersonalizePolicyDlg, PictureLibDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 211,
      colModel: [
        { prop: 'name', label: 'stgName', fixedWidth: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'style', label: 'stgMessage', width: '200', formatter: this.styleFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true, // 是否显示树
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {},
      // 选择契合度：1 平铺 2 居中 3 拉伸 4 适应 5 填充
      styles: {
        0: this.$t('pages.personalizePolicy_nothing'),
        1: this.$t('pages.personalizePolicy_tile'),
        2: this.$t('pages.personalizePolicy_center'),
        3: this.$t('pages.personalizePolicy_stretching'),
        4: this.$t('pages.personalizePolicy_adapt'),
        5: this.$t('pages.personalizePolicy_fill')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['personalizePolicyDlg'].handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs['personalizePolicyDlg'].handleUpdate(row, this.formable)
    },
    handleShow: function(row, isGenerateStrategy) {
      this.$refs['personalizePolicyDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleConfigLib() {
      this.$refs['deviceLibDlg'].show()
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    styleFormatter: function(row, data) {
      let message = ''
      if (row.screenSaverStauts === 0) {
        message += this.$t('pages.personalizePolicy_text9')
      } else {
        message += this.$t('pages.personalizePolicy_wait1')
        if (row.interval != -1) {
          message += row.interval / 60
        } else {
          message += 1
        }
        if (row.showLogin === 2) {
          message += this.$t('pages.personalizePolicy_text10')
        } else {
          message += this.$t('pages.personalizePolicy_text14')
        }
      }
      if (row.wallPaperStauts === 0) {
        message += this.$t('pages.personalizePolicy_text15')
      } else {
        message += this.$t('pages.personalizePolicy_text11') + this.styles[data] + '；'
      }
      if (row.lockScreenStauts == 1) {
        message += this.$t('pages.personalizePolicy_text19')
      } else {
        message += this.$t('pages.personalizePolicy_text18')
      }
      return message
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>
