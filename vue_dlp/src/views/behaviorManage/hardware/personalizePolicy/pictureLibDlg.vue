<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.pictureLibrary')"
      :visible.sync="dlgVisible"
      width="1000px"
    >
      <div style="height: 400px">
        <div class="tree-container">
          <tree-menu
            ref="pictureGroupTree"
            resizeable
            :default-expand-all="true"
            :data="pictureGroupTreeData"
            :render-content="renderContent"
            @node-click="pictureGroupTreeNodeCheckChange"
          />
        </div>
        <div class="table-container">
          <div class="toolbar">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
              {{ $t('button.add') }}
            </el-button>
            <el-button type="primary" icon="el-icon-plus" :disabled="query.groupId==null || query.groupId==0" size="mini" @click="handlePicBatchAdd">
              {{ $t('pages.batchAdd') }}
              <el-tooltip effect="dark" placement="bottom-end">
                <div slot="content">{{ $t('pages.pictureLibTip') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-button>
            <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
              {{ $t('button.delete') }}
            </el-button>
            <div class="searchCon">
              <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('table.pictureName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </div>
          <div class="">
            <grid-table
              ref="picLibList"
              :col-model="colModel"
              :height="360"
              :row-data-api="rowDataApi"
              @selectionChangeEnd="selectionChangeEnd"
            />
          </div>
        </div>

      </div>
      <div v-if="useFun !== 'pictureLib'" slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="addStgData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="titleMap[dialogStatus]"
      :close-on-click-modal="false"
      :modal="false"
      destroy-on-close
      :visible.sync="deviceVisible"
      width="450px"
      @dragDialog="handleDrag"
      @close="handleRemove"
    >
      <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" style="width: 380px;">
        <FormItem :label="$t('form.group')" prop="groupId">
          <tree-select ref="picGroupTreeSelect" :data="treeSelectNode" node-key="dataId" class="input-with-button" :placeholder="$t('pages.validaGroup')" :checked-keys="[temp.groupId]" :width="296" @change="picGroupNodeSelectChange" />
          <el-button :title="$t('pages.addType')" class="editBtn" @click="handlePicGroupCreate({dataId: 0}, true)"><svg-icon icon-class="add" /></el-button>
        </FormItem>
        <FormItem :label="$t('table.resolution')" prop="resolution">
          <el-autocomplete
            v-model="temp.resolution"
            class="inline-input"
            :fetch-suggestions="querySearch"
            :placeholder="$t('pages.personalizePolicy_text4')"
            :disabled="true"
            @select="handleSelect"
          ></el-autocomplete>
        </FormItem>
        <FormItem :label="$t('table.pictureName')" prop="name">
          <el-input v-model="temp.name" :placeholder="$t('pages.personalizePolicy_text5')" maxlength="30" @input="handleInput" />
        </FormItem>
        <FormItem label-width="0" >
          <el-upload
            ref="upload"
            accept=".jpg,.jpeg,.bmp,.png"
            list-type="picture"
            :disabled="temp.isNonEnv==0"
            class="upload-demo"
            name="uploadFile"
            action="aaaaaa"
            :show-file-list="true"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :file-list="fileList"
          >
            <el-button size="small" :disabled="temp.isNonEnv===0" type="primary">{{ $t('button.pictureUpload') }}</el-button>
            <div slot="tip" class="el-upload__tip">{{ $t('pages.personalizePolicy_text6') }}</div>
          </el-upload>
        </FormItem>

      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="deviceVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <upload-tip-dlg ref="uploadTipDlg" > </upload-tip-dlg>

    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="treeSelectNode"
      :add-func="createPictureLibGroup"
      :update-func="updatePictureGroup"
      :delete-func="deletePictureLibGroup"
      :edit-valid-func="getUrlGroupByName"
      @addEnd="createGroup"
      @updateEnd="updateGroup"
      @deleteEnd="removeGroupEnd"
    />
    <pic-batch-add-dlg ref="picBatchAddDlg" :group-tree-data="pictureGroupTreeData"></pic-batch-add-dlg>
  </div>
</template>
<script type="text/jsx">
import UploadTipDlg from '@/views/behaviorManage/hardware/personalizePolicy/uploadTipDlg'
import PicBatchAddDlg from '@/views/behaviorManage/hardware/personalizePolicy/picBatchAddDlg'
import {
  getTreeNode, getPictureLibList, createPictureLib, createPictureLibGroup, updatePictureLib,
  updatePictureGroup, deletePicture, deletePictureLibGroup, countPictureLibByGroupId, uploadFile, getUrlGroupByName
} from '@/api/behaviorManage/hardware/pictureLib'
import EditGroupDlg from '@/views/common/editGroupDlg'

export default {
  name: 'PictureLibDlg',
  components: { UploadTipDlg, EditGroupDlg, PicBatchAddDlg },
  props: {
    useFun: { type: String, default: 'pictureLib' }
  },
  data() {
    return {
      // 公共参数
      submitting: false,
      dlgVisible: false, // 列表页是否显示
      dialogStatus: 'create', // create、update、createGroup、updateGroup
      titleMap: {
        update: this.$t('text.edit'),
        create: this.$t('text.add'),
        createGroup: this.i18nConcatText(this.$t('table.group'), 'create'),
        updateGroup: this.i18nConcatText(this.$t('table.group'), 'update')
      },
      resolutions: [], // 常量：分辨率
      pictureGroupTreeData: [{ id: 'G0', dataId: '0', label: this.$t('button.pictureLibrary'), parentId: '', children: [] }],
      // 图片库列表
      selectedData: [],
      deleteable: false, // 删除按钮
      colModel: [
        { prop: 'name', label: 'pictureName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'resolution', label: 'resolution', width: '120', sort: 'custom' },
        { prop: 'groupName', label: 'group', width: '120', sort: 'custom' },
        { prop: 'resultCode', label: 'uploadResult', width: '170', sort: 'custom', formatter: this.statusFormatter },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: this.$t('text.edit'), click: this.handleUpdate },
            { label: 'resultDetail', click: this.handleDetail }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        name: '',
        groupId: undefined,
        resultCode: undefined
      },
      // 图片库表单 新增、修改
      deviceVisible: false, // 新增修改页面dlg
      temp: {}, // 图片表单
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        resolution: '',
        md5: '',
        ext: '',
        guid: undefined,
        ftpId: undefined,
        groupId: undefined
      },
      treeSelectNode: [], // 表单 分组树
      fileList: [], // 表单 图片列表
      uploadHandle: undefined,
      rules: {
        name: [
          { required: true, message: this.$t('pages.validaPicture'), trigger: 'change' }
        ],
        resolution: [
          { required: true, message: this.$t('pages.validaResolution'), trigger: 'change' },
          { required: true, validator: this.resolutionValidator, trigger: 'change' }
        ],
        groupId: [
          { required: true, message: this.$t('pages.validaGroup'), trigger: 'blur' }
        ]
      },
      addGroupFlag: false
    }
  },
  computed: {
    pictureGroupTree() {
      return this.$refs['pictureGroupTree']
    },
    gridTable() {
      return this.$refs['picLibList']
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
  },
  activated() {
    this.resetTemp()
  },
  mounted() {
    this.resolutions = this.loadAllResolution();
  },
  methods: {
    createPictureLibGroup,
    updatePictureGroup,
    deletePictureLibGroup,
    getUrlGroupByName,
    // 通用方法
    handleDrag() {
    },
    resetTemp() {
      this.submitting = false
      this.temp = Object.assign({}, this.defaultTemp)
    },
    isValidJSON(text) {
      try {
        JSON.parse(text);
        return true;
      } catch (error) {
        return false;
      }
    },
    // 页面显示|隐藏
    // 图片库dlg
    show() {
      this.dlgVisible = true
      this.loadPicLibTree()
      this.$nextTick(() => {
        this.handleFilter()
      })
    },
    hide() {
      this.dlgVisible = false
    },
    // 左边-分组主题 树
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handlePicGroupCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handlePicGroupUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
          </span>
        </div>
      )
    },
    pictureGroupTreeNodeCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedNode && checkedNode.data.dataId != '0'
      if (checkedNode) {
        this.query.groupId = checkedNode.data.dataId
      } else {
        this.query.groupId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    loadPicLibTree: function() {
      getTreeNode().then(respond => {
        this.pictureGroupTreeData[0].children = respond.data
        this.$nextTick(() => {
          if (this.pictureGroupTree.$refs['tree'] != undefined) {
            this.pictureGroupTree.$refs['tree'].setCurrentKey('G0')
            this.query.groupId = '0'
            this.gridTable.execRowDataApi()
          }
        })
      })
    },
    changeTreeSelectNode() {
      this.treeSelectNode = this.pictureGroupTreeData[0].children
    },
    // 图片分组-页面：handle**
    handlePicGroupCreate(data, isAdd) {
      this.$refs['editGroupDlg'].handleCreate(data.dataId)
      this.addGroupFlag = !!isAdd
    },
    handlePicGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: data.parentId.replace('G', '')
      })
    },
    dataToTreeNode(data) {
      return {
        id: 'G' + data.id,
        type: 'G',
        dataId: data.id + '',
        label: data.name,
        parentId: 'G0'
      }
    },
    // 图片分组-删除
    removeNode(data) {
      countPictureLibByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.$notify({ title: this.$t('text.warning'), message: this.$t('pages.personalizePolicy_text7'), type: 'warning', duration: 2000 })
          return
        }
        this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
          deletePictureLibGroup({ id: data.dataId }).then(respond => {
            this.pictureGroupTree.removeNode([data.id])
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          })
        }).catch(() => {})
      })
    },
    // 列表
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPictureLibList(searchQuery)
    },
    selectionChangeEnd(val) {
      this.selectedData = val
      this.deleteable = val.length > 0
    },
    handleFilter() {
      this.query.page = 1

      if (this.useFun !== 'pictureLib') {
        // 计算机个性化策略只能添加成功的图片
        // 5: '上传成功[至少一台文件服务器]'
        this.query.resultCode = 5
      }
      this.gridTable.execRowDataApi(this.query)
    },
    // 图片上传状态
    statusFormatter(row, data) {
      const status = {
        1: this.$t('pages.picUploadStatus1'),
        2: this.$t('pages.picUploadStatus2'),
        3: this.$t('pages.picUploadStatus3'),
        4: this.$t('pages.picUploadStatus4'),
        5: this.$t('pages.picUploadStatus5')
      }
      return status[row.resultCode]
    },
    handleDetail(row) {
      if ('resultContent' in row && row.resultContent && row.resultContent !== '') {
        if (this.isValidJSON(row.resultContent)) {
          // 上传文件服务器的提示是JSON格式，同时row.resultCode 等于4 或 5
          const resultArr = JSON.parse(row.resultContent)
          this.$refs['uploadTipDlg'].showBatchUpload(resultArr)
        } else {
          // 上传到文件服务器的提示是普通字符串，同时row.resultCode 等于1 或 2
          this.$confirmBox(row.resultContent, this.$t('pages.imageUploadResults'), { confirmButtonText: false })
        }
      } else {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.noResult'),
          type: 'success',
          duration: 2000
        })
      }
    },
    // 按钮功能
    // 新增、修改图片表单
    // 表单-主题分组
    picGroupNodeSelectChange: function(data) {
      this.temp.groupId = data
    },
    // 表单-分辨率
    loadAllResolution() {
      return [
        { value: '1928*1080' },
        { value: '1680*1050' },
        { value: '1600*900' },
        { value: '1440*900' },
        { value: '1400*1050' },
        { value: '1366*768' },
        { value: '1360*768' },
        { value: '1280*1024' },
        { value: '1280*960' },
        { value: '1280*800' },
        { value: '1280*768' },
        { value: '1280*720' },
        { value: '800*600' }
      ]
    },
    querySearch(queryString, cb) {
      const resolutions = this.resolutions;
      const results = queryString ? resolutions.filter(this.createFilter(queryString)) : resolutions;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (resolution) => {
        return (resolution.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    handleSelect(item) {
    },
    resolutionValidator(rule, value, callback) {
      const regex = /^([1-9]\d{2,3})\*([1-9]\d{2,3})$/
      if (regex.test(value)) {
        callback()
      } else {
        callback(new Error(this.$t('pages.validaResolution2')))
      }
    },
    // 图片表单 - 上传图片.jpg,.bmp,.png
    hidePicForm() {
      if (this.uploadHandle !== undefined) {
        this.uploadHandle.close()
      }
      this.uploadHandle = undefined
      this.deviceVisible = false
    },
    beforeUpload(file) {
      if (this.uploadHandle !== undefined) {
        // 原上传图片的订阅socket未关闭时，将它关闭
        this.uploadHandle.close()
        this.uploadHandle = undefined
      }
      const testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      if (testmsg !== '') {
        const extension = testmsg.toLowerCase() === 'jpg'
        const extension2 = testmsg.toLowerCase() === 'bmp'
        const extension3 = testmsg.toLowerCase() === 'png'
        // const isLt2M = file.size / 1024 / 1024 < 10
        if (!extension && !extension2 && !extension3) {
          this.$message({
            message: this.$t('pages.pictureLibDlgUploadFileMsg'),
            type: 'error'
          });
          return
        }
      } else {
        this.$message({
          message: this.$t('pages.pictureLibDlgUploadFileMsg'),
          type: 'error'
        });
        return
      }

      this.submitting = true
      const fd = new FormData()
      fd.append('uploadFile', file)// 传文件
      // fd.append('controlCode', this.productMap[this.temp.competitorId].controlCode)
      const loading = this.$loading({
        lock: true,
        text: this.$t('pages.fileFp_Msg19'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      uploadFile(fd).then(res => {
        this.submitting = false
        // resultCode: 4:未有上传成功的文件服务器 5 至少一台文件服务器传输成功
        this.temp.resultCode = 4
        console.log('res.data.md5', res.data.md5)
        // 订阅文件上传情况
        this.$socket.subscribeToUser('uploadResult', res.data.md5, (respond, handle) => {
          // 缓存订阅关闭
          this.uploadHandle = handle;
          // 此订阅当作所有消息的通知接口
          // if (respond.data && (respond.data.status == 6 || respond.data.status > 999)) {
          // resultCode: 4:未有上传成功的文件服务器 5 至少一台文件服务器传输成功
          if (respond.data.status === 6) {
            this.temp = Object.assign({}, this.temp, {
              guid: respond.data.guid,
              ftpId: res.data.ftpId,
              name: res.data.name.substring(0, 30),
              resolution: res.data.resolution,
              md5: res.data.md5,
              ext: res.data.ext.toLowerCase(),
              resultCode: 5
            })
            this.fileList.splice(0)
            const img = {
              url: process.env.VUE_APP_BASE_API + '/TRDLP/file/pictureLib/' + this.temp.md5 + '.' + this.temp.ext.toLowerCase()
            }
            this.fileList.push(img)
          }
          loading.close()
          // 收集上传结果->保存入库
          this.uploadResult(respond.data)
          // 弹窗提示
          this.$refs['uploadTipDlg'].show(respond.data)
        }, false)
      }).catch(res => {
        loading.close()
        this.submitting = false
      })
      return false // 屏蔽了action的默认上传
    },
    uploadResult(resultData) {
      // 收集上传结果->保存入库
      let resultArr = []
      console.log('uploadResult', resultData)
      let resultContentArr = []
      if (this.temp.resultContent && this.temp.resultContent !== '' && this.isValidJSON(this.temp.resultContent)) {
        resultContentArr = JSON.parse(this.temp.resultContent)
        resultArr = resultContentArr.filter(res => res.devId !== resultData.devId)
        resultArr.push(resultData)
        resultArr.sort((a, b) => a.devId - b.devId)
      } else {
        resultArr.push(resultData)
      }
      console.log('resultArr', resultArr.length)
      this.temp.resultContent = JSON.stringify(resultArr)
    },
    handleRemove(file, fileList) {
      this.fileList.splice(0)
      this.temp = Object.assign({}, this.temp, {
        guid: '',
        name: '',
        md5: '',
        ext: ''
      })
      this.$refs.upload.clearFiles()
    },
    fileChange(file, fileList) {
      // 图片显示前做一下判断
      const IMG_ALLOWD = ['jpg', 'bmp', 'jpeg']
      const imgType = file.raw.type.split('/')[1]
      const imgSize = file.size / 1024
      // 判断图片格式
      if (IMG_ALLOWD.indexOf(imgType) === -1) {
        this.$message({
          message: this.$t('pages.waterMarkLib_text18'),
          type: 'error',
          duration: 2000
        })
        if (this.fileList.length === 0) {
          this.fileList = []
        } else {
          this.fileList = fileList.slice(0, 1)
        }
        return
      } else if (imgSize >= 200) {
        this.$message({
          message: this.$t('pages.waterMarkLib_text19'),
          type: 'error',
          duration: 2000
        })
        if (this.fileList.length === 0) {
          this.fileList = []
        } else {
          this.fileList = fileList.slice(0, 1)
        }
        return
      } else {
        this.fileList = fileList.slice(-1)
      }
    },
    // 图片表单 - 新增、修改、删除
    handleCreate() {
      this.dialogStatus = 'create'
      this.temp = Object.assign({}, { 'groupId': this.query.groupId })
      this.fileList.splice(0)
      this.changeTreeSelectNode()
      this.deviceVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    handlePicBatchAdd() {
      this.$refs.picBatchAddDlg.show(this.query.groupId)
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.fileList.splice(0)
      const img = {
        url: process.env.VUE_APP_BASE_API + '/TRDLP/file/pictureLib/' + this.temp.md5 + '.' + this.temp.ext
      }
      this.fileList.push(img)
      this.dialogStatus = 'update'
      this.deviceVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
      this.changeTreeSelectNode()
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.name === '' || this.temp.name === null || this.fileList.length == 0) {
            this.$message({
              message: this.$t('pages.validaPicture')
            })
            this.submitting = false
            return
          }
          createPictureLib(this.temp).then(respond => {
            this.submitting = false
            this.hidePicForm()
            this.gridTable.execRowDataApi(this.query)
            this.notifySuccess(this.$t('text.createSuccess'))
            this.$emit('submitEnd')
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.name === '' || this.temp.name === null || this.fileList.length == 0) {
            this.$message({
              message: this.$t('pages.validaPicture')
            })
            return
          }
          this.submitting = true
          console.log('this.temp', JSON.stringify(this.temp))

          const tempData = Object.assign({}, this.temp)
          updatePictureLib(tempData).then(respond => {
            this.submitting = false
            this.hidePicForm()
            this.gridTable.execRowDataApi(this.query)
            this.notifySuccess(this.$t('text.updateSuccess'))
            this.$emit('submitEnd')
          })
        }
      })
    },
    notifySuccess(msg) {
      this.$notify({
        title: this.$t('text.success'),
        message: msg,
        type: 'success',
        duration: 2000
      })
    },
    handleDelete() {
      const toDeleteIds = this.gridTable.getSelectedIds()
      deletePicture({ ids: toDeleteIds.join(',') }).then(respond => {
        this.gridTable.deleteRowData(toDeleteIds)
        this.notifySuccess(this.$t('text.deleteSuccess'))
      })
    },
    // 数据添加到策略
    addStgData() {
      const datas = this.gridTable.getSelectedDatas();
      if (datas.length === 0) {
        this.$message({
          message: this.$t('pages.atLeastOneImage')
        })
        return
      } else {
        this.$emit('add-data', datas)
      }
    },
    handleInput(value) {
    },
    createGroup(data) {
      this.pictureGroupTree.addNode(this.dataToTreeNode(data))
      this.changeTreeSelectNode()
      if (this.addGroupFlag) {
        this.$nextTick(() => {
          this.temp.groupId = data.id + ''
        })
        this.addGroupFlag = false
      }
    },
    updateGroup(data) {
      this.pictureGroupTree.updateNode(this.dataToTreeNode(data))
    },
    removeGroupEnd(dataId) {
      const nodeData = this.pictureGroupTree.findNode(this.pictureGroupTreeData, dataId, 'dataId')
      if (nodeData) {
        this.pictureGroupTree.removeNode([nodeData.id])
      }
    }
  }

}
</script>
<style lang="scss" scoped>
  >>>.el-radio__inner{
    border: 1px solid #999999;
  }
  >>>.el-radio__label{
    color: #666666;
  }
  .upload-demo{
    padding-left: 40px;
  }
  .inline-input {
    width: 100%;
  }
</style>
