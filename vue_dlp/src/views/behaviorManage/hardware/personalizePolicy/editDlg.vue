<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <stg-target-form-item
          ref="stgTargetItem"
          :stg-code="211"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <!-- <el-divider content-position="left">{{ $t('pages.screensaver') }}</el-divider> -->
        <el-tabs ref="tabs" v-model="tabName" :before-leave="changeTab" style="height: 320px;">
          <el-tab-pane :label="$t('pages.screensaver')" name="screensaverTab" style="padding: 5px; overflow: auto;">
            <FormItem label-width="0" >
              <el-checkbox v-model="temp.screenSaverStauts" label-width="120px" :label="$t('pages.personalizePolicy_text12')" :true-label="1" :false-label="0" :disabled="!formable">
              </el-checkbox>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.personalizePolicy_text20') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </FormItem>
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('pages.personalizePolicy_wait')" label-width="80px" :tooltip-content="$t('pages.personalizePolicy_text2')" :extra-width="{en: -6}" class="noBold">
                  <el-input-number v-model="temp.interval" :disabled="!formable || temp.screenSaverStauts===0" :controls="false" :min="1" :max="1440" :precision="0" style="width: 120px;" ></el-input-number>
                  <label style="display: inline-block">{{ $t('text.minuteLower') }}</label>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem label-width="0" style="margin-left: 20px">
                  <el-checkbox v-model="temp.showLogin" label-width="120px" :label="$t('pages.personalizePolicy_showLogin')" :true-label="2" :false-label="1" :disabled="!formable || temp.screenSaverStauts===0"></el-checkbox>
                  <!--              <el-tooltip effect="dark" placement="bottom-start">-->
                  <!--                <div slot="content">{{ $t('pages.personalizePolicy_text1') }}</div>-->
                  <!--                <i class="el-icon-info" />-->
                  <!--              </el-tooltip>-->
                </FormItem>
              </el-col>
            </el-row>
            <FormItem label-width="80" :label="$t('pages.screensaver_style')" :tooltip-content="$t('pages.personalizePolicy_text16')" :extra-width="{en: -10}" class="noBold">
              <el-select v-model="temp.screenSaverType" :disabled="!formable || temp.screenSaverStauts===0" style="width: 120px" >
                <el-option v-for="item in screenSaverTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </FormItem>
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('pages.projectionSpeed')" :tooltip-content="$t('pages.personalizePolicy_text21')" :extra-width="{en: 34}" label-width="80px" class="noBold">
                  <el-select v-model="temp.speed" :disabled="!formable || temp.screenSaverStauts===0 || temp.screenSaverType != 6" style="width: 120px;" >
                    <el-option v-for="item in speedList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                  <label style="display: inline-block"></label>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem label-width="0" style="margin-left: 20px">
                  <el-checkbox v-model="temp.shuffle" label-width="120px" :label="$t('pages.shufflePictures')" :true-label="1" :false-label="0" :disabled="!formable || temp.screenSaverStauts===0 || temp.screenSaverType != 6"></el-checkbox>
                </FormItem>
              </el-col>
            </el-row>
            <FormItem label-width="0">
              <LocalZoomIn parent-tag="el-dialog">
                <div>
                  <el-button size="small" :disabled="!formable || temp.screenSaverStauts===0 || temp.screenSaverType != 6" @click="addScreenSaver">
                    {{ $t('button.insert') }}
                  </el-button>
                  <!--            <el-button size="small" :disabled="this.picTableSelectionData.length!=1" @click="handleUpdateDefault">-->
                  <!--              默认壁纸-->
                  <!--            </el-button>-->
                  <el-button size="small" :disabled="!formable" @click="deleteScreenSaver">
                    {{ $t('button.delete') }}
                  </el-button>
                </div>
                <grid-table
                  ref="screenSaverTable"
                  auto-height
                  :row-datas="temp.screenSaverImgPolicy"
                  :col-model="screenSaverColModel"
                  :show-pager="false"
                  @selectionChangeEnd="screenSaverSelectionChangeEnd"
                />
              </LocalZoomIn>
            </FormItem>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.wallpaper')" name="wallpaperTab" style="padding: 5px; overflow: auto;">
            <!-- <el-divider content-position="left">{{ $t('pages.wallpaper') }}</el-divider> -->
            <el-row>
              <el-col :span="8">
                <FormItem label-width="0">
                  <el-checkbox v-model="temp.wallPaperStauts" label-width="120px" :label="$t('pages.personalizePolicy_text13')" :true-label="1" :false-label="0" :disabled="!formable"></el-checkbox>
                </FormItem>
              </el-col>
              <el-col :span="10">
                <FormItem :label="$t('pages.personalizePolicy_fit')" label-width="110" class="noBold">
                  <!--              <el-tooltip effect="dark" placement="bottom-start">-->
                  <!--                <div slot="content">{{ $t('pages.personalizePolicy_text3') }}</div>-->
                  <!--                <i class="el-icon-info" />-->
                  <!--              </el-tooltip>-->
                  <el-select v-model="temp.fit" :disabled="!formable || temp.wallPaperStauts===0" style="width: 150px;" >
                    <el-option v-for="item in styleOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </FormItem>
              </el-col>
              <!--          <el-col :span="6">-->
              <!--            <FormItem>-->
              <!--              <el-checkbox v-model="temp.termPicOption" label-width="120px" label="终端图片可选功能" :true-label="1" :false-label="0" :disabled="!formable"></el-checkbox>-->
              <!--            </FormItem>-->
              <!--          </el-col>-->
            </el-row>
            <FormItem label-width="0">
              <LocalZoomIn :height="220" parent-tag="el-dialog">
                <div>
                  <el-button size="small" :disabled="!formable || temp.wallPaperStauts===0" @click="addPic">
                    {{ $t('button.insert') }}
                  </el-button>
                  <!--            <el-button size="small" :disabled="this.picTableSelectionData.length!=1" @click="handleUpdateDefault">-->
                  <!--              默认壁纸-->
                  <!--            </el-button>-->
                  <el-button size="small" :disabled="!formable" @click="deletePic">
                    {{ $t('button.delete') }}
                  </el-button>
                </div>
                <grid-table
                  ref="picTable"
                  auto-height
                  :min-height="170"
                  :row-datas="temp.pictureList"
                  :col-model="colModel"
                  :show-pager="false"
                  @selectionChangeEnd="picTableSelectionChangeEnd"
                />
              </LocalZoomIn>
            </FormItem>
          </el-tab-pane>
          <!--          测试发现仅支持企业版本操作系统，专业版本操作系统不支持，支持比较有限；终端还需要话时间研究下-->
          <el-tab-pane :label="$t('pages.lockScreen')" name="lockScreenTab" style="padding: 5px; overflow: auto;">
            <el-row>
              <el-col :span="8">
                <FormItem label-width="0">
                  <el-checkbox v-model="temp.lockScreenStauts" label-width="120px" :label="$t('pages.enableLockScreen')" :true-label="1" :false-label="0" :disabled="!formable"></el-checkbox>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">{{ $t('pages.lockScreenPrompt') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </FormItem>
              </el-col>
            </el-row>
            <FormItem label-width="0">
              <LocalZoomIn :height="220" parent-tag="el-dialog">
                <div>
                  <el-button size="small" :disabled="!formable || temp.lockScreenStauts===0" @click="addLockScreen">
                    {{ $t('button.insert') }}
                  </el-button>
                  <el-button size="small" :disabled="!formable" @click="deleteLockScreenImg">
                    {{ $t('button.delete') }}
                  </el-button>
                </div>
                <grid-table
                  ref="lockScreenTable"
                  auto-height
                  :min-height="170"
                  :row-datas="temp.lockScreenImg"
                  :col-model="lockScreenColModel"
                  :show-pager="false"
                  @selectionChangeEnd="lockScreenImgSelectionChangeEnd"
                />
              </LocalZoomIn>
            </FormItem>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="211"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :treeable="treeable"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button
          v-if="formable"
          :loading="submitting"
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <picture-lib-dlg ref="pictureLibDlg" :use-fun="useFun" @add-data="addData"/>
  </div>
</template>

<script>
import { createStrategy, updateStrategy } from '@/api/behaviorManage/hardware/personalizePolicy'
import PictureLibDlg from './pictureLibDlg'

export default {
  name: 'PersonalizePolicyDlg',
  components: { PictureLibDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } },
    entityClick: { type: Function, default() { } }
  },
  data() {
    return {
      dialogFormVisible: false,
      dialogStatus: undefined,
      submitting: false,
      treeable: true,
      tabName: 'screensaver',
      useFun: 'addWallpaper', // addScreenSaver 添加屏保
      textMap: {
        update: this.i18nConcatText(this.$t('pages.personalizePolicy'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.personalizePolicy'), 'create')
      },
      // 选择契合度：1 平铺 2 居中 3 拉伸 4 适应 5 填充 0 终端代表不启用策略    { label: this.$t('pages.personalizePolicy_nothing'), value: 0 },
      styleOptions: [
        { label: this.$t('pages.personalizePolicy_tile'), value: 1 },
        { label: this.$t('pages.personalizePolicy_center'), value: 2 },
        { label: this.$t('pages.personalizePolicy_stretching'), value: 3 },
        { label: this.$t('pages.personalizePolicy_adapt'), value: 4 },
        { label: this.$t('pages.personalizePolicy_fill'), value: 5 }
      ],
      // 屏保风格 0 无 1 变换线 2 气泡 3 彩带 4 空白 5 3D文字 6 照片
      screenSaverTypeList: [
        { label: this.$t('pages.null'), value: 0 },
        { label: this.$t('pages.screenSaverType_transformation'), value: 1 },
        { label: this.$t('pages.screenSaverType_bubble'), value: 2 },
        { label: this.$t('pages.screenSaverType_ribbon'), value: 3 },
        { label: this.$t('pages.screenSaverType_blank'), value: 4 },
        { label: this.$t('pages.screenSaverType_3D'), value: 5 },
        { label: this.$t('pages.screenSaverType_photo'), value: 6 }
      ],
      // 幻灯片播放速度 0 慢速 1中速 2 快速
      speedList: [
        { label: this.$t('pages.screenSaverSpeed_low'), value: 0 },
        { label: this.$t('pages.screenSaverSpeed_medium'), value: 1 },
        { label: this.$t('pages.screenSaverSpeed_fast'), value: 2 }
      ],
      query: {
        page: 1,
        searchInfo: '',
        objectType: undefined,
        objectId: undefined
      },
      // grid-table
      picTableSelectionData: [],
      screenSaverTableSelectionData: [],
      lockScreenTableSelectionData: [],
      colModel: [
        { prop: 'pictureName', label: 'pictureName', width: '120', sort: true },
        { prop: 'resolution', label: 'resolution', width: '120', sort: true },
        { prop: 'groupName', label: 'group', width: '120', sort: true },
        { prop: '', label: 'defaultWallpaper', fixedWidth: '120', iconFormatter: this.iconFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'defaultWallpaper', disabled: !this.formable, click: this.handleUpdateDefault }
          ]
        }
      ],
      screenSaverColModel: [
        { prop: 'pictureName', label: 'pictureName', width: '120', sort: true },
        { prop: 'resolution', label: 'resolution', width: '120', sort: true },
        { prop: '', label: 'defaultScreensaver', fixedWidth: '120', iconFormatter: this.iconFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'defaultScreensaver', disabled: !this.formable, click: this.handleDefaultScreenSaver }
          ]
        }
      ],
      lockScreenColModel: [
        { prop: 'pictureName', label: 'pictureName', width: '120', sort: true },
        { prop: 'resolution', label: 'resolution', width: '120', sort: true },
        { prop: '', label: 'defaultLockScreenImage', fixedWidth: '120', iconFormatter: this.iconFormatter },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right',
          buttons: [
            { label: 'defaultLockScreenImage', disabled: !this.formable, click: this.handleDefaultLockScreen }
          ]
        }
      ],
      temp: {}, // 接收并显示数据
      defaultTemp: {
        id: undefined,
        name: '',
        remark: '',
        active: false,
        entityType: '',
        entityId: undefined,
        wallPaperStauts: 0,
        screenSaverStauts: 0,
        lockScreenStauts: 0,
        fit: 1,
        termPicOption: 0,
        pictureList: [],
        screenSaverImgPolicy: [], // 屏保
        lockScreenImg: [], // 锁屏图片
        interval: 20,
        showLogin: 1,
        screenSaverType: 0,
        speed: 0,
        shuffle: 0
      }, // 修改后的数据向 stg-dialog 组件传值
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ]
      }

    }
  },
  computed: {
    screenSaverTable() {
      return this.$refs['screenSaverTable']
    },
    picTable() {
      return this.$refs['picTable']
    },
    lockScreenTableTable() {
      return this.$refs['lockScreenTable']
    },
    picLibDlg() {
      return this.$refs['pictureLibDlg']
    }
  },
  watch: {
    'temp.screenSaverStauts'(val) {
      if (val === 0) {
        this.temp = Object.assign(this.temp, { interval: 20, showLogin: 1 })
      }
    }
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
  },
  activated() {
  },
  methods: {
    iconFormatter(row) {
      console.log('iconFormatter', row.defaultConfig)
      return row.defaultConfig === 1 ? [{
        class: 'active',
        title: '所选图片没有适合当前终端分辨率情况时,则设置默认图片'
      }] : null;
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    // 页面操作方法 （显示隐藏方法）
    handleCreate() {
      this.dialogFormVisible = true
      this.dialogStatus = 'create'
      this.resetTemp()
      this.temp.entityType = this.entityNode.type
      this.temp.entityId = this.entityNode.dataId
      this.tabName = 'screensaverTab'
    },
    handleUpdate: function(row) {
      this.dialogFormVisible = true
      this.dialogStatus = 'update'
      console.log('handleUpdate', JSON.parse(JSON.stringify(row)))
      this.temp = Object.assign({}, this.defaultTemp, JSON.parse(JSON.stringify(row)))
      this.temp.interval = this.temp.interval / 60 // 显示时：秒 转 分钟，修改是 分钟 转 秒
      if (this.temp.screenSaverStauts === 0) {
        this.temp = Object.assign(this.temp, { interval: 20, showLogin: 1 })
      }
      if (!this.temp.screenSaverImgPolicy) {
        this.$set(this.temp, 'screenSaverImgPolicy', [])
      }
      if (!this.temp.pictureList) {
        this.$set(this.temp, 'pictureList', [])
      }
      if (!this.temp.lockScreenImg) {
        this.$set(this.temp, 'lockScreenImg', [])
      }
      // 按类型imgType拆分屏保和锁屏图片
      this.temp.lockScreenImg = this.temp.screenSaverImgPolicy.filter(item => item.imgType !== undefined && item.imgType === 2)
      // 开发锁屏增加imgType字段，处理历史版本策略字段缺失。
      this.temp.screenSaverImgPolicy = this.temp.screenSaverImgPolicy
        .filter(item => !item.hasOwnProperty('imgType') || item.imgType === 1)
        .map(item => ({ ...item, imgType: 1 }))
      this.tabName = 'screensaverTab'
    },
    handleShow(row, formable, isGenerateStrategy) {
      this.query.strategyDefType = isGenerateStrategy !== undefined ? (isGenerateStrategy ? 0 : 1) : 0
      this.formable = formable
      this.handleUpdate(row);
    },
    changeTab(activeName, oldActiveName) {
    },
    // 组件回调方法（changeXXX）
    handleDrag() {
    },
    picTableSelectionChangeEnd(val) {
      console.log('picTableSelectionChangeEnd', val)
      this.picTableSelectionData = val
    },
    screenSaverSelectionChangeEnd(val) {
      this.screenSaverTableSelectionData = val
    },
    lockScreenImgSelectionChangeEnd(val) {
      this.lockScreenTableSelectionData = val
    },
    // rules 验证规则方法（XXValidator ）
    valid() {
      if (!this.$refs['stgTargetItem'].valid()) {
        return false
      }
      if (this.temp.screenSaverStauts === 1 && this.temp.interval == undefined) {
        this.$message({
          message: this.$t('pages.waitingTimeCannotEmpty'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      // 桌面壁纸不能包含相同分辨率提示
      const resolution = []
      let resolutionRepeat = false
      this.temp.pictureList.forEach(pic => {
        if (!resolution.includes(pic.resolution)) {
          resolution.push(pic.resolution)
        } else {
          resolutionRepeat = true
        }
      })

      if (resolutionRepeat) {
        this.$message({
          message: this.$t('pages.picturesNotSameResolution', { info: this.$t('pages.wallpaper') }),
          type: 'error',
          duration: 2000
        })
        return false
      }
      // 锁屏不能包含相同分辨率提示
      const lockScreenResolution = []
      let lockScreenResolutionRepeat = false
      this.temp.lockScreenImg.forEach(pic => {
        if (!lockScreenResolution.includes(pic.resolution)) {
          lockScreenResolution.push(pic.resolution)
        } else {
          lockScreenResolutionRepeat = true
        }
      })
      if (lockScreenResolutionRepeat) {
        this.$message({
          message: this.$t('pages.picturesNotSameResolution', { info: this.$t('pages.lockScreenImage') }),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    // 功能方法：增删改查
    createStrategy,
    updateStrategy,
    addPic() {
      this.useFun = 'addWallpaper'
      this.picLibDlg.show();
    },
    addScreenSaver() {
      this.useFun = 'addScreenSaver'
      this.picLibDlg.show();
    },
    addLockScreen() {
      this.useFun = 'addLockScreen'
      this.picLibDlg.show();
    },
    addData(data) {
      if (this.useFun === 'addScreenSaver') {
        console.log('data', data)
        console.log('this.temp.screenSaverImgPolicy', this.temp.screenSaverImgPolicy)
        const repeatData = []
        for (let i = 0, len = this.temp.screenSaverImgPolicy.length; i < len; i++) {
          for (let j = 0, length = data.length; j < length; j++) {
            if (this.temp.screenSaverImgPolicy[i].id === data[j].id) {
              repeatData.push(data[j].id)
            }
          }
        }
        const resArr = data.filter(item => !repeatData.includes(item.id))
        const arr = resArr.map((item) => {
          return {
            id: item['id'],
            md5: item['md5'],
            ext: item['ext'],
            resolution: item['resolution'],
            pictureName: item['name'],
            defaultConfig: 0,
            guid: item['guid'],
            ftpId: item['ftpId'],
            groupId: item['groupId'],
            groupName: item['groupName'],
            imgType: 1
          }
        })
        // 存在的数据更新为新的数据
        for (let i = 0, len = this.temp.screenSaverImgPolicy.length; i < len; i++) {
          for (let j = 0, length = data.length; j < length; j++) {
            if (this.temp.screenSaverImgPolicy[i].id === data[j].id) {
              this.temp.screenSaverImgPolicy[i].ext = data[j].ext
              this.temp.screenSaverImgPolicy[i].resolution = data[j].resolution
              this.temp.screenSaverImgPolicy[i].pictureName = data[j].name
              this.temp.screenSaverImgPolicy[i].groupId = data[j].groupId
              this.temp.screenSaverImgPolicy[i].groupName = data[j].groupName
              this.temp.screenSaverImgPolicy[i].imgType = 1
            }
          }
        }
        const union = [...this.temp.screenSaverImgPolicy, ...arr]
        const countDefaultConfig = union.filter(item => item.defaultConfig === 1);
        // 没有默认分辨率的屏保，则设置第一张的分辨率为默认分辨率
        if (union.length > 0 && countDefaultConfig.length < 1) {
          // union[0].defaultConfig = 1
          union.forEach(item => {
            if (union[0].resolution == item.resolution) {
              item.defaultConfig = 1
            }
          })
        }
        // 有默认分辨率的屏保，则后续添加相同分辨率的图片，也设置为默认屏保
        if (union.length > 0 && countDefaultConfig.length >= 1) {
          union.forEach(item => {
            if (countDefaultConfig[0].resolution == item.resolution) {
              item.defaultConfig = 1
            }
          })
        }
        this.temp.screenSaverImgPolicy = union
        this.picLibDlg.hide()
      }
      if (this.useFun === 'addWallpaper') {
        const repeatData = []
        for (let i = 0, len = this.temp.pictureList.length; i < len; i++) {
          for (let j = 0, length = data.length; j < length; j++) {
            if (this.temp.pictureList[i].id === data[j].id) {
              repeatData.push(data[j].id)
            }
          }
        }
        const resArr = data.filter(item => !repeatData.includes(item.id))
        const arr = resArr.map((item) => {
          return {
            id: item['id'],
            md5: item['md5'],
            ext: item['ext'],
            resolution: item['resolution'],
            pictureName: item['name'],
            defaultConfig: 0,
            guid: item['guid'],
            ftpId: item['ftpId'],
            groupId: item['groupId'],
            groupName: item['groupName']
          }
        })
        // 存在的数据更新为新的数据
        for (let i = 0, len = this.temp.pictureList.length; i < len; i++) {
          for (let j = 0, length = data.length; j < length; j++) {
            if (this.temp.pictureList[i].id === data[j].id) {
              this.temp.pictureList[i].ext = data[j].ext
              this.temp.pictureList[i].resolution = data[j].resolution
              this.temp.pictureList[i].pictureName = data[j].name
              this.temp.pictureList[i].groupId = data[j].groupId
              this.temp.pictureList[i].groupName = data[j].groupName
            }
          }
        }
        const union = [...this.temp.pictureList, ...arr]
        const countDefaultConfig = union.filter(item => item.defaultConfig === 1);
        if (union.length > 0 && countDefaultConfig.length < 1) {
          union[0].defaultConfig = 1
        }
        this.temp.pictureList = union
        this.picLibDlg.hide()
      }
      if (this.useFun === 'addLockScreen') {
        console.log('data', data)
        console.log('this.temp.lockScreenImg', this.temp.lockScreenImg)
        const repeatData = []
        for (let i = 0, len = this.temp.lockScreenImg.length; i < len; i++) {
          for (let j = 0, length = data.length; j < length; j++) {
            if (this.temp.lockScreenImg[i].id === data[j].id) {
              repeatData.push(data[j].id)
            }
          }
        }
        const resArr = data.filter(item => !repeatData.includes(item.id))
        const arr = resArr.map((item) => {
          return {
            id: item['id'],
            md5: item['md5'],
            ext: item['ext'],
            resolution: item['resolution'],
            pictureName: item['name'],
            defaultConfig: 0,
            guid: item['guid'],
            ftpId: item['ftpId'],
            groupId: item['groupId'],
            groupName: item['groupName'],
            imgType: 2
          }
        })
        // 存在的数据更新为新的数据
        for (let i = 0, len = this.temp.lockScreenImg.length; i < len; i++) {
          for (let j = 0, length = data.length; j < length; j++) {
            if (this.temp.lockScreenImg[i].id === data[j].id) {
              this.temp.lockScreenImg[i].ext = data[j].ext
              this.temp.lockScreenImg[i].resolution = data[j].resolution
              this.temp.lockScreenImg[i].pictureName = data[j].name
              this.temp.lockScreenImg[i].groupId = data[j].groupId
              this.temp.lockScreenImg[i].groupName = data[j].groupName
              this.temp.lockScreenImg[i].imgType = 2
            }
          }
        }
        const union = [...this.temp.lockScreenImg, ...arr]
        const countDefaultConfig = union.filter(item => item.defaultConfig === 1);
        if (union.length > 0 && countDefaultConfig.length < 1) {
          // union[0].defaultConfig = 1
          union.forEach(item => {
            if (union[0].resolution == item.resolution) {
              item.defaultConfig = 1
            }
          })
        }
        this.temp.lockScreenImg = union
        this.picLibDlg.hide()
      }
    },
    handleUpdateDefault(data) {
      this.temp.pictureList.forEach((item) => {
        if (data.id === item.id) {
          item.defaultConfig = 1
        } else {
          item.defaultConfig = 0
        }
      })
    },
    handleDefaultScreenSaver(data) {
      console.log('handleDefaultScreenSaver', data)
      this.temp.screenSaverImgPolicy.forEach((item) => {
        if (data.resolution === item.resolution) {
          item.defaultConfig = 1
        } else {
          item.defaultConfig = 0
        }
      })
    },
    handleDefaultLockScreen(data) {
      console.log('lockScreenImg', data)
      this.temp.lockScreenImg.forEach((item) => {
        if (data.resolution === item.resolution) {
          item.defaultConfig = 1
        } else {
          item.defaultConfig = 0
        }
      })
    },
    deletePic() {
      if (this.picTableSelectionData.length > 0) {
        this.picTableSelectionData.forEach(pic => {
          const index = this.temp.pictureList.findIndex(c => c.id === pic.id)
          this.temp.pictureList.splice(index, 1)
        })
        this.picTableSelectionData.splice(0) // 删除customTable列表勾选
        // TRDLP-8429	当删除的是默认背景，则默认第一张图片为默认背景
        const defaultPicList = this.temp.pictureList.filter(pic => pic.defaultConfig === 1);
        if (this.temp.pictureList.length > 0 && defaultPicList.length === 0) {
          const size = this.temp.pictureList.length;
          for (let i = 0; i < size; i++) {
            if (i === 0) {
              const pic = this.temp.pictureList[0]
              pic.defaultConfig = 1
              this.temp.pictureList[0] = pic
            }
          }
        }
      }
    },
    deleteScreenSaver() {
      if (this.screenSaverTableSelectionData.length > 0) {
        this.screenSaverTableSelectionData.forEach(pic => {
          const index = this.temp.screenSaverImgPolicy.findIndex(c => c.id === pic.id)
          this.temp.screenSaverImgPolicy.splice(index, 1)
        })
        this.screenSaverTableSelectionData.splice(0) // 删除customTable列表勾选
        // TRDLP-10215 需保证至少有一张默认屏保
        const defaultScreenSaverList = this.temp.screenSaverImgPolicy.filter(img => img.defaultConfig === 1);
        if (defaultScreenSaverList.length === 0 && this.temp.screenSaverImgPolicy.length > 0) {
          const size = this.temp.screenSaverImgPolicy.length;
          let resolutionTemp = ''
          for (let i = 0; i < size; i++) {
            const pic = this.temp.screenSaverImgPolicy[i]
            if (i === 0) {
              pic.defaultConfig = 1
              this.temp.screenSaverImgPolicy[0] = pic
              resolutionTemp = pic.resolution
            }
            if (resolutionTemp === pic.resolution) {
              pic.defaultConfig = 1
              this.temp.screenSaverImgPolicy[i] = pic
            }
          }
        }
      }
    },
    deleteLockScreenImg() {
      if (this.lockScreenTableSelectionData.length > 0) {
        this.lockScreenTableSelectionData.forEach(pic => {
          const index = this.temp.lockScreenImg.findIndex(c => c.id === pic.id)
          this.temp.lockScreenImg.splice(index, 1)
        })
        this.lockScreenTableSelectionData.splice(0) // 删除customTable列表勾选
        // TRDLP-10215 需保证至少有一张默认屏保
        const defaultScreenSaverList = this.temp.lockScreenImg.filter(img => img.defaultConfig === 1);
        if (defaultScreenSaverList.length === 0 && this.temp.lockScreenImg.length > 0) {
          const size = this.temp.lockScreenImg.length;
          let resolutionTemp = ''
          for (let i = 0; i < size; i++) {
            const pic = this.temp.lockScreenImg[i]
            if (i === 0) {
              pic.defaultConfig = 1
              this.temp.lockScreenImg[0] = pic
              resolutionTemp = pic.resolution
            }
            if (resolutionTemp === pic.resolution) {
              pic.defaultConfig = 1
              this.temp.lockScreenImg[i] = pic
            }
          }
        }
      }
    },
    openOrCloseData(data) {
      // 屏保关闭-屏保数据清空
      if (!data.screenSaverStauts) {
        data = Object.assign(data, {
          interval: -1,
          showLogin: 0,
          screenSaverType: 0,
          speed: 0,
          shuffle: 0,
          screenSaverImgPolicy: []
        })
      }
      // 屏保开启-风格不是图片时清空图片
      if (data.screenSaverStauts && data.screenSaverType !== 6) {
        data = Object.assign(data, {
          speed: 0,
          shuffle: 0,
          screenSaverImgPolicy: []
        })
      }
      // 壁纸关闭-壁纸数据清空 style: 0,
      if (!data.wallPaperStauts) {
        data = Object.assign(data, {
          termPicOption: 0,
          pictureList: []
        })
      }

      // 锁屏关闭-锁屏数据清空
      if (!data.lockScreenStauts) {
        data = Object.assign(data, {
          lockScreenImg: []
        })
      }
      // 屏保图片和锁屏图片合并存储屏保策略，清空锁屏图片的数组，避免策略重复数据
      data.screenSaverImgPolicy = [...data.screenSaverImgPolicy, ...data.lockScreenImg]
      // data.lockScreenImg.splice(0, data.lockScreenImg.length)
    },
    createData() {
      if (!this.valid()) {
        return
      }
      if (this.temp.screenSaverStauts === 1 && this.temp.screenSaverType === 6 && this.temp.screenSaverImgPolicy.length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.atLeastUploadOneScreenSaver'),
          type: 'warning',
          duration: 2000
        })
        return;
      }
      if (this.temp.wallPaperStauts === 1 && this.temp.pictureList.length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.atLeastUploadOneDesktopBackground'),
          type: 'warning',
          duration: 2000
        })
        return;
      }
      if (this.temp.lockScreenStauts === 1 && this.temp.lockScreenImg.length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.uploadAtLeastOneLockScreenImage'),
          type: 'warning',
          duration: 2000
        })
        return;
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const data = Object.assign({}, this.temp);
          data.interval = this.temp.interval * 60 // 显示时：秒 转 分钟，修改是 分钟 转 秒
          this.openOrCloseData(data)
          createStrategy(data).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      if (!this.valid()) {
        return
      }
      if (this.temp.screenSaverStauts === 1 && this.temp.screenSaverType === 6 && this.temp.screenSaverImgPolicy.length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.atLeastUploadOneScreenSaver'),
          type: 'warning',
          duration: 2000
        })
        return;
      }
      if (this.temp.wallPaperStauts === 1 && this.temp.pictureList.length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.atLeastUploadOneDesktopBackground'),
          type: 'warning',
          duration: 2000
        })
        return;
      }
      if (this.temp.lockScreenStauts === 1 && this.temp.lockScreenImg.length === 0) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.uploadAtLeastOneLockScreenImage'),
          type: 'warning',
          duration: 2000
        })
        return;
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const data = Object.assign({}, this.temp);
          data.interval = this.temp.interval * 60 // 显示时：秒 转 分钟，修改是 分钟 转 秒
          this.openOrCloseData(data)
          updateStrategy(data).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.noBold label{
    font-weight: 400;
  }
  >>>.el-select .el-input .el-input__inner{
    height: 30px !important;
  }
  >>>.el-input-number .el-input__inner {
    padding: 0 10px;
  }
</style>
