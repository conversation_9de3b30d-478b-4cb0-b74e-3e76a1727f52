<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="appendToBody"
      :close-on-click-modal="false"
      :modal="false"
      :title="i18nConcatText(this.$t('pages.picture'), 'batchAdd')"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @close="close"
    >
      <Form ref="dataForm2" :rules="rules" :model="tempF" label-position="right" label-width="140px">
        <div class="toolbar">
          <upload-dir ref="uploadDir" :file-suffix="['jpg','jpeg','bmp','png']" :text="$t('pages.scanDir')" :popover-height="235" style="padding-top: 2px" @changeFile="changeFile" />
        </div>
        <!--        <el-button v-if="osType==1" type="primary" size="mini" @click="showAppSelectDlg">-->
        <!--          {{ $t('pages.software_Msg16') }}-->
        <!--        </el-button>-->
        <el-row v-if="fileSubmitting">
          <el-col :span="22">
            <el-progress type="line" :percentage="percentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.uploadToGroup')" prop="groupId" label-width="80px">
          <tree-select ref="picGroupTreeSelect1" :data="pictureGroupTreeData" node-key="dataId" class="input-with-button" :placeholder="$t('pages.validaGroup')" :checked-keys="[tempF.groupId]" :width="400" @change="picGroupNodeSelectChange" />
          <el-button :title="$t('pages.addType')" class="editBtn" @click="handlePicGroupCreate({dataId: 0}, true)"><svg-icon icon-class="add" /></el-button>
        </FormItem>
        <grid-table
          ref="fileList"
          :height="300"
          :multi-select="false"
          :show-pager="false"
          :col-model="colModel"
          :row-datas="fileList"
          row-key="guid"
          :checked-row-keys="checkedRowKeys"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <!--        <el-button type="primary" :loading="submitting" @click="createData1">{{ $t('button.confirm') }}</el-button>-->
        <!--        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>-->
      </div>
    </el-dialog>
    <upload-tip-dlg ref="uploadTipDlg" > </upload-tip-dlg>
    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="pictureGroupTreeData"
      :add-func="createPictureLibGroup"
      :edit-valid-func="getUrlGroupByName"
      @addEnd="createGroupEnd"
    />
  </div>
</template>
<script>
import UploadDir from '@/components/UploadDir';
import { createPictureLibGroup, getUrlGroupByName, uploadBatchFile } from '@/api/behaviorManage/hardware/pictureLib';
import axios from 'axios';
import GridTable from '@/components/GridTable';
import UploadTipDlg from './uploadTipDlg';
import EditGroupDlg from '@/views/common/editGroupDlg';

export default {
  name: 'PicBatchAddDlg',
  components: { EditGroupDlg, UploadTipDlg, GridTable, UploadDir },
  props: {
    // 进程系统类型：1-windows，2-linux，4-mac
    osType: { type: Number, default: 1 },
    appendToBody: { type: Boolean, default: false },
    groupTreeData: { type: Array, default() { return [] } }   // 外部传入的分组树数据
  },
  data() {
    return {
      dialogFormVisible: false,
      colModel: [
        { prop: 'name', label: 'pictureName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'resolution', label: 'resolution', width: '120', sort: 'custom' },
        { prop: 'groupName', label: 'group', width: '80', sort: 'custom' },
        { prop: 'resultCode', label: 'uploadResult', width: '170', sort: 'custom', formatter: this.statusFormatter },
        { prop: 'resultContent', label: 'details', hidden: true, width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'resultDetail', click: this.handleDetail }
          ]
        }
      ],
      tempF: {
      },
      defaultTempF: { // 表单字段
        osType: this.osType,
        groupId: 0,
        processList: []
      },
      fileList: [],
      checkedRowKeys: [],
      uploadHandle: undefined,
      fileSubmitting: false,
      fileLimitSize: 1024,
      percentage: 0,
      // 分组树
      pictureGroupTreeData: [], // 表单 分组树
      rules: {

      }
    }
  },
  created() {
  },
  methods: {
    show(groupId) {
      // 去掉根节点
      this.pictureGroupTreeData = this.groupTreeData[0].children
      this.tempF.groupId = groupId
      this.dialogFormVisible = true
      this.fileList.splice(0, this.fileList.length)
    },
    handleDrag() {
      // 移动弹窗时回调
    },
    close() {
      if (this.uploadHandle !== undefined) {
        this.uploadHandle.close()
      }
      this.uploadHandle = undefined
    },
    // 图片上传状态
    statusFormatter(row, data) {
      const status = {
        1: this.$t('pages.picUploadStatus1'),
        2: this.$t('pages.picUploadStatus2'),
        3: this.$t('pages.picUploadStatus3'),
        4: this.$t('pages.picUploadStatus4'),
        5: this.$t('pages.picUploadStatus5')
      }
      return status[row.resultCode]
    },
    // 扫描目录
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.percentage = 0
    },
    changeFile(files) {
      console.log('changeFile', files)
      const fd = new FormData()
      for (let i = 0; i < files.length; i++) {
        fd.append('uploadFile', files[i])// 传文件
      }
      fd.append('groupId', this.tempF.groupId)
      if (files.length > 0) {
        // 调用后台接口获取进程windows属性
        this.fileSubmitting = true
        this.percentage = 0
        // 上传钩子，用来获取进度条
        const onUploadProgress = (progressEvent) => {
          const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
          this.percentage = parseInt(percent)
        }
        this.source = this.connectionSource()
        const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
        uploadBatchFile(fd, onUploadProgress, cacheToken).then(res => {
          this.resetUploadComponent()
          const picList = res.data
          this.appendFile(picList)
          // 订阅文件上传情况
          this.$socket.subscribeToUser('uploadResult', picList[0].groupId, (respond, handle) => {
            // 缓存订阅关闭
            this.uploadHandle = handle;
            const pic = respond.data;
            for (const item of this.fileList) {
              if (item.guid === pic.guid) {
                item.resultCode = pic.resultCode
                item.resultContent = pic.resultContent
              }
            }
          }, false)
        }).catch(res => {
          if ('Error: Network Error' == res) {
            this.$message({
              message: '图片的绝对路径太长',
              type: 'error',
              duration: 2000
            })
          }
          console.log('catch', res)
          console.log('reject1', 'Error: Network Error' == res)
          this.resetUploadComponent()
        })
      } else {
        this.$message({
          message: this.$t('pages.notPicFind'),
          type: 'error',
          duration: 2000
        })
      }
      this.$refs.uploadDir.clearValue()
    },
    appendFile(picList) {
      const checkedKeys = this.$refs.fileList.getSelectedKeys()
      picList.forEach(item => {
        const index = this.fileList.findIndex(item2 => {
          return item2.guid == item.guid
        })
        if (index == -1) {
          this.fileList.push(item)
          checkedKeys.push(item.guid)
        }
      })
      this.$nextTick(() => {
        this.checkedRowKeys = [...checkedKeys]
      })
    },
    isValidJSON(text) {
      try {
        JSON.parse(text);
        return true;
      } catch (error) {
        return false;
      }
    },
    handleDetail(row) {
      if ('resultContent' in row && row.resultContent && row.resultContent !== '') {
        if (this.isValidJSON(row.resultContent)) {
          // 上传文件服务器的提示是JSON格式，同时row.resultCode 等于4 或 5
          const resultArr = JSON.parse(row.resultContent)
          this.$refs['uploadTipDlg'].showBatchUpload(resultArr)
        } else {
          // 上传到文件服务器的提示是普通字符串，同时row.resultCode 等于1 或 2
          this.$confirmBox(row.resultContent, this.$t('pages.imageUploadResults'), { confirmButtonText: false })
        }
      } else {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.noResult'),
          type: 'success',
          duration: 2000
        })
      }
    },
    // 图片分组
    // 表单-主题分组
    createPictureLibGroup,
    getUrlGroupByName,
    picGroupNodeSelectChange: function(data) {
      this.tempF.groupId = data
    },
    handlePicGroupCreate(data, isAdd) {
      this.$refs['editGroupDlg'].handleCreate(data.dataId)
    },
    dataToTreeNode(data) {
      return {
        id: 'G' + data.id,
        type: 'G',
        dataId: data.id + '',
        label: data.name,
        parentId: 'G0'
      }
    },
    createGroupEnd(data) {
      this.pictureGroupTreeData.push(this.dataToTreeNode(data))
      this.$nextTick(() => {
        this.tempF = Object.assign({}, this.tempF, { groupId: data.id })
      })
    }

  }
}
</script>
<style scoped lang="scss">

</style>
