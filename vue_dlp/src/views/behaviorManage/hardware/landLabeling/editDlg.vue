<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('text.stgTitleSuffix', { title: $t('route.landLabeling') })"
      :title-tip="$t('pages.landLabelingUseTips')"
      :active-able="activeAble"
      :stg-code="279"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <LocalZoomIn
          :min-height="200"
          parent-tag="el-dialog"
        >
          <data-editor
            :formable="formable"
            :popover-width="680"
            :updateable="controlledProcessEditable"
            :deletable="controlledProcessDeleteable"
            :add-func="createControlledProcess"
            :update-func="updateControlledProcess"
            :delete-func="deleteControlledProcess"
            :cancel-func="cancelControlledProcess"
            :before-update="beforeUpdateControlledProcess"
            :before-add="beforeAddControlledProcess"
          >
            <Form ref="smartEncForm" :model="tempP" :rules="tempPrules" label-position="right" label-width="110px" style="width: 600px; margin: auto;">
              <FormItem v-if="operationType === 'update'" :label="$t('pages.executableProgram')" :tooltip-content="$t('pages.executableProgramTip')" prop="processName" tooltip-placement="bottom-start">
                <el-upload name="processFile" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getFileName" style="display: inline-block;">
                  <el-button type="primary" icon="el-icon-upload" style="padding: 7px 13px;"></el-button>
                </el-upload>
                <el-button type="primary" size="mini" style="margin: 0 0 0 2px;" @click="showUpdateAppSelectDlg">
                  {{ $t('pages.smart_appLibImport') }}
                </el-button>
                <el-input v-model="tempP.processName" style="margin-top: 5px" :title="tempP.processName"></el-input>
              </FormItem>

              <FormItem v-if="operationType === 'create'" prop="processNames" :label="$t('pages.executableProgram')" :tooltip-content="$t('pages.executableProgramTip')" label-width="110px" tooltip-placement="bottom-start">
                <el-upload name="processFile" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getFileName" style="display: inline-block;">
                  <el-button type="primary" icon="el-icon-upload" style="padding: 7px 11px;margin: 1px 0;"></el-button>
                </el-upload>
                <el-button type="primary" size="mini" style="margin: 0 0 0 2px;" @click="showAppSelectDlg">
                  {{ $t('pages.smart_appLibImport') }}
                </el-button>
                <el-button size="mini" style="margin: 0;" @click="handleClear">
                  {{ $t('button.clear') }}
                </el-button>
                <tag
                  v-model="tempP.processNames"
                  :border="true"
                  :list="tempP.processNames"
                  :overflow-able="true"
                  max-height="150px"
                  style="margin-top: 5px"
                  :disabled="!formable"
                  @tagChange="tagChange"
                />
              </FormItem>

              <FormItem :label="$t('pages.catalogue')" :tooltip-content="$t('pages.landMsgTip')" prop="pathList" tooltip-placement="bottom-start">
                <el-input v-model="tempP.pathList" maxlength=""></el-input>
              </FormItem>

              <FormItem :label="$t('pages.process_Msg5')" :tooltip-content="$t('pages.smart_Msg9')" label-width="110px" prop="suffix" tooltip-placement="bottom-start">
                <el-input v-model="tempP.suffix" type="text" maxlength="99" class="input-with-button"></el-input>
                <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
                  <el-button type="primary" size="mini" @click="handleFileSuffixImport()">
                    <svg-icon icon-class="import" />
                  </el-button>
                </el-tooltip>
              </FormItem>

              <FormItem :label="$t('table.triggerCondition')" :tooltip-content="$t('pages.conditionTips')" tooltip-placement="bottom-start">
                <el-select v-model="tempP.ruleType" style="width: 220px">
                  <el-option v-for="(item, index) in activeTypeOpts" :key="index" :label="item.label" :value="item.value"/>
                </el-select>
              </FormItem>
              <FormItem>
                <span style="color: #ff0000;"> {{ $t('pages.landLabelingTips') }} </span>
              </FormItem>
            </Form>
          </data-editor>
          <grid-table
            ref="processList"
            auto-height
            :multi-select="true"
            :show-pager="false"
            :col-model="controlledColModel"
            :row-datas="temp.configList"
            @selectionChangeEnd="processSelectionChange"
          />
        </LocalZoomIn>
      </template>
      <template slot="button">
        <link-button
          btn-type="primary"
          btn-style="float: left"
          :formable="formable"
          :menu-code="'C99'"
          :link-url="{ name: 'BaseLabelConfig', params: { tabName: 'LabelDetectebrary' } }"
          :btn-text="$t('pages.labelDetectionLibrary')"
        />
      </template>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
    <app-select-dlg ref="processLib" :append-to-body="true" @select="importProcess"/>
    <app-select-dlg ref="updateProcessLib" :multiple="false" :append-to-body="true" @select="updateImportProcess"/>
  </div>
</template>

<script>
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/behaviorManage/hardware/landLabeling'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg';
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg'
import { countNum } from '@/api/dataEncryption/encryption/autoLabeling'

export default {
  name: 'LandLabelingDlg',
  components: { AppSelectDlg, FileSuffixLibImport },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      controlledColModel: [
        { prop: 'processName', label: 'programName', width: '100' },
        { prop: 'suffix', label: 'suffixes', width: '80' },
        { prop: 'pathList', label: 'catalogue', width: '80' },
        { prop: 'ruleType', label: 'triggerCondition', width: '80', formatter: this.activeTypeFormatter }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        configList: []
      },
      tempP: {},
      defaultTempP: {
        id: undefined,
        processName: '',
        suffix: undefined,
        pathList: undefined,
        processNames: [],
        ruleType: 0 // 触发条件
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      tempPrules: {
        processName: [
          { required: true, message: this.$t('pages.process_Msg10'), trigger: 'blur' },
          { validator: this.processNameValidator, trigger: 'blur' }
        ],
        suffix: [
          { required: true, validator: this.suffixValidator, trigger: 'blur' }
        ],
        pathList: [
          { required: true, message: this.$t('pages.smart_Msg6'), trigger: 'blur' }
        ],
        processNames: [
          { required: true, validator: this.processNamesValidator, trigger: 'blur' }
        ]
      },
      textMap: {
        update: this.i18nConcatText(this.$t('pages.mailBox'), 'update'),
        create: this.i18nConcatText(this.$t('pages.mailBox'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.mailGroup'), 'delete')
      },
      controlledProcessEditable: false,
      dialogStatus: 'create',
      dialogFormVisible: false,
      deleteable: false,
      controlledProcessDeleteable: false,
      operationType: '',
      activeTypeOpts: [
        { label: this.$t('pages.include'), value: 0 },
        { label: this.$t('pages.exclude'), value: 1 }
      ],
      errorMessage: ''
    }
  },
  computed: {

  },
  watch: {
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    handleDrag() {},
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
    },
    closed() {
      this.resetTemp()
    },
    buttonFormatter(row) {
      return !this.formable
    },
    activeTypeFormatter(row, data) {
      return (this.activeTypeOpts.find(opt => opt.value === data) || this.activeTypeOpts[0]).label
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg0'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable().getSelectedIds() || []
        this.temp.labelInfoList = this.gridTable().deleteTableData(this.temp.labelInfoList, toDeleteIds)
      }).catch(() => {})
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.resetTempP()
    },
    resetTempP() {
      this.operationType = 'create'
      this.tempP = Object.assign({}, this.defaultTempP)
      this.tempP.processNames = []
    },
    handleClear() {
      this.tempP.processNames.splice(0)
      this.$refs['smartEncForm'].validateField('processNames');
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.defaultTemp, JSON.parse(JSON.stringify(row))) // copy obj
      this.$refs['stgDlg'].show(row, this.formable)
    },
    showAppSelectDlg() {
      this.$refs['processLib'].show()
    },
    getFileName(file) {
      if (this.operationType === 'create') {
        const list = [...this.tempP.processNames]
        list.push(file.name)
        this.tempP.processNames = this.verifyExeNames(list)
        this.$refs['smartEncForm'].validateField('processNames');
      } else if (this.operationType === 'update') {
        this.tempP.processName = file.name
        this.$refs['smartEncForm'].validateField('processName');
      }
      this.$refs['smartEncForm'].clearValidate()
      return false // 屏蔽了action的默认上传
    },
    processTable() {
      return this.$refs['processList']
    },
    showUpdateAppSelectDlg() {
      this.$refs['updateProcessLib'].show()
    },
    formatRowData(rowData) {
      rowData.configList.forEach((item, index) => { item.id = index })
    },
    formatFormData(formData) {
      formData.configList.forEach(item => delete item.id)
    },
    async validateFormData(formData) {
      await countNum().then(res => {
        if (res.data == 0) {
          this.$message({
            message: '标签检测规则库未进行相应配置，策略无实际生效效果',
            type: 'warning',
            duration: 4000
          })
        }
      })
      return true
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    processSelectionChange(rowDatas) {
      this.controlledProcessDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.controlledProcessEditable = true
      } else {
        this.controlledProcessEditable = false
        this.cancelControlledProcess()
      }
    },
    createControlledProcess() {
      let validate
      this.formatSuffix()
      this.$refs['smartEncForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempP)
          const tempList = []
          const sameList = []
          for (let i = 0, len = this.tempP.processNames.length; i < len; i++) {
            const data = JSON.parse(JSON.stringify(rowData))
            data.id = new Date().getTime() + i
            data.processName = this.tempP.processNames[i]
            data.processNames = undefined
            const sameData = (this.temp.configList || []).find(item => item.processName == data.processName && item.suffix == data.suffix && item.pathList == data.pathList)
            if (!sameData) { tempList.push(data) }
            if (sameData && sameData.ruleType != data.ruleType) {
              sameList.push(sameData)
            }
          }
          if (sameList.length > 0) {
            valid = false
            const message = sameList.reduce((a, b) =>
              a + `${this.$t('table.processName')}: ${b.processName} ${this.$t('table.suffixes')}: ${b.suffix} ${this.$t('table.catalogue')}: ${b.pathList} ${this.$t('pages.smart_Msg5')}</br>`, ''
            )
            this.$message({
              message,
              type: 'error',
              duration: 4000,
              dangerouslyUseHTMLString: true
            })
          } else {
            this.temp.configList.unshift(...tempList)
            this.cancelControlledProcess()
          }
          validate = valid
        }
      })
      return validate
    },
    formatSuffix() {
      const supportSuffix = ['*.*', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', '.wps', '.wpt', '.et', '.ett', '.dps', '.dpt', '.jpg', '.jpeg', '.bmp', '.png']
      if (this.tempP.suffix) {
        const suffixList = this.tempP.suffix.split('|')
        if (suffixList && suffixList.length > 0) {
          const filterSuffix = suffixList.filter(suffix => supportSuffix.indexOf(suffix) > -1)
          this.tempP.suffix = filterSuffix.join('|')
        }
      }
    },
    beforeUpdateControlledProcess() {
      this.operationType = 'update'
      this.tempP = Object.assign(JSON.parse(JSON.stringify(this.defaultTempP)), this.processTable().getSelectedDatas()[0])
      this.operationType = 'update'
    },
    beforeAddControlledProcess() {
      this.operationType = 'create'
    },
    updateControlledProcess() {
      let validate
      this.formatSuffix()
      this.$refs['smartEncForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempP)
          for (let i = 0, size = this.temp.configList.length; i < size; i++) {
            const data = this.temp.configList[i]
            if (rowData.id === data.id) {
              this.temp.configList.splice(i, 1, rowData)
              break
            }
          }
          this.cancelControlledProcess()
          validate = valid
        }
      })
      return validate
    },
    deleteControlledProcess() {
      const toDeleteIds = this.processTable().getSelectedIds()
      this.temp.configList.splice(0, this.temp.configList.length, ...this.processTable().deleteRowData(toDeleteIds))
      this.cancelControlledProcess()
    },
    cancelControlledProcess() {
      this.processTable() && this.processTable().setCurrentRow()
      this.$refs['smartEncForm'] && this.$refs['smartEncForm'].clearValidate()
      this.resetTempP()
    },
    importProcess(processes) {
      if (this.tempP.processNames === undefined || this.tempP.processNames === null) {
        this.$set(this.tempP, 'processNames', [])
      }
      let processNames = [...this.tempP.processNames];
      (processes || []).forEach(item => {
        processNames.push(item.processName)
      })
      processNames = this.verifyExeNames(processNames)
      this.tempP.processNames = processNames
      this.$refs['smartEncForm'].validateField('processNames');
    },
    updateImportProcess(process) {
      if (process) {
        this.tempP.processName = process.processName
      }
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    processNameValidator(rule, value, callback) {
      const reg = /^.*?\.(exe)|\*\.\*$/
      if (reg.test(value.toLowerCase())) {
        const size = this.temp.configList.length
        for (let i = 0; i < size; i++) {
          const item = this.temp.configList[i]
          // processName 在 update 才需验证 this.operationType === 'create' 恒为 false， 去掉该判断
          if (item.processName === value && item.id !== this.tempP.id) {
            // 重复性检验  还需要检验目录、后缀、触发条件  这些都相同才算重复数据
            if (item.suffix != this.tempP.suffix || item.pathList != this.tempP.pathList) {
              continue
            }
            callback(new Error(this.$t('pages.smart_Msg5')))
            return
          }
        }
        callback()
      } else {
        callback(new Error(this.$t('pages.smart_Msg4')))
      }
    },
    //  当列表发生改变时，校验名称是否符合规则
    tagChange(names) {
      names = this.verifyExeNames(names)
      this.tempP.processNames = names;
      this.$refs['smartEncForm'].validateField('processNames');
    },
    //  校验进程名集合
    verifyExeNames(names) {
      if (names.find(item => item === '*.*')) {
        names = ['*.*']
      }
      //  校验是否符合规则
      names = this.verifyRule(names)
      names = this.filterRepetitionData(names);
      // names = this.verifyExeNameExits(names);
      if (this.errorMessage !== '') {
        this.$message({
          message: this.errorMessage + this.$t('pages.smart_batchAddMsg1'),
          type: 'warning',
          duration: 3000
        })
      }
      this.errorMessage = ''
      return names;
    },
    //  过滤重复数据(不区分大小写）
    filterRepetitionData(list) {
      const exitSizeNames = []  //  不区分大小写
      list = list.filter(name => {
        // const flag = this.temp.configList.findIndex(item => { return item.processName === name }) === -1;
        const lowerCaseName = name.toLowerCase()
        const flag = exitSizeNames.findIndex(n => { return n === lowerCaseName }) === -1
        if (flag) {
          exitSizeNames.push(lowerCaseName)
        }
        return flag;
      })
      return list;
    },
    verifyRule(names) {
      const reg = /^.*?\.(exe)|\*\.\*$/
      const oldLen = names.length
      names = names.filter(name => {
        return reg.test(name.toLowerCase());
      })
      if (names.length < oldLen) {
        this.errorMessage = this.errorMessage + (this.errorMessage !== '' ? ',' : '') + this.$t('pages.smart_batchAddMsg2');
      }
      return names;
    },
    suffixValidator(rule, value, callback) {
      if (value) {
        const supportSuffix = ['*.*', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', '.wps', '.wpt', '.et', '.ett', '.dps', '.dpt', '.jpg', '.jpeg', '.bmp', '.png']
        const suffixList = value.split('|')
        const filterSuffix = suffixList.filter(suffix => supportSuffix.indexOf(suffix) == -1)
        if (filterSuffix && filterSuffix.length > 0) {
          const names = filterSuffix.join(',')
          callback(new Error(this.$t('pages.unSupportFileType', { name: names })));
        } else {
          callback()
        }
      } else if (value === undefined || value === null || value.length === 0) {
        callback(new Error(this.$t('pages.process_Msg12')));
      } else if (value && value.length > 99) {
        callback(new Error(this.$t('pages.processMonitor_suffixOutMaxLength')));
      } else {
        callback()
      }
    },
    processNamesValidator(rule, value, callback) {
      if (this.tempP.processNames.length === 0) {
        callback(new Error(this.$t('pages.smart_pleaseEnterControlledProcess')))
      } else {
        callback()
      }
    },
    importFileSuffix(suffix) {
      let union_suffix
      if (this.tempP.suffix == null || this.tempP.suffix === '') {
        union_suffix = [...new Set(suffix.split('|'))]
      } else {
        union_suffix = [...new Set((this.tempP.suffix + '|' + suffix).split('|'))]
      }
      this.tempP.suffix = union_suffix.join('|')
      this.$refs['smartEncForm'].validateField('suffix');
    }
  }
}
</script>
