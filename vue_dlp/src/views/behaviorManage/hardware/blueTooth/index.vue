<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      class="blue-tooth-stg-dlg"
      @dragDialog="handleDrag"
      @closed="dialogStatus=''"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 700px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeName" class="process-tab">
          <el-tab-pane :label="$t('pages.blueTooth_blueTooth_Msg19')" name="first">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <div v-if="formable">
                <el-button size="small" @click="handleCreateBluetooth()">
                  {{ $t('button.insert') }}
                </el-button>
                <el-button size="small" :disabled="!checkBluetoothDeleteable" @click="handleDeleteCheckedBluetooth()">
                  {{ $t('button.delete') }}
                </el-button>
                <el-button size="small" @click="handleClearCheckedBluetooth()">
                  {{ $t('button.clear') }}
                </el-button>
              </div>
              <grid-table
                ref="checkedBluetoothGrid"
                row-key="id"
                :show-pager="false"
                auto-height
                :selectable="selectable"
                :col-model="checkedColModel"
                :row-datas="tableBluetoothList"
                @selectionChangeEnd="checkBluetoothSelectionChangeEnd"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="40px">
              {{ $t('pages.burnMsgToothExecuteMessage') }}
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem prop="action" label-width="30px">
              <el-row>
                <el-col :span="16">
                  <el-checkbox-group v-model="approvalTypeList" style="display: inline-block;" :disabled="!formable" @change="handleApprovalTypeChange">
                    <el-checkbox :label="1">{{ hasEncPermision ? $t('pages.burnMsg4') : $t('pages.burnMsg5') }}</el-checkbox>
                    <el-checkbox :disabled="approvalTypeList.indexOf(1) == -1" :label="4">{{ hasEncPermision ? $t('pages.burnMsg6') : $t('pages.burnMsg7') }}</el-checkbox>
                    <el-checkbox v-if="hasEncPermision" :label="2">{{ $t('pages.burnMsg8') }}</el-checkbox>
                    <el-checkbox v-if="hasEncPermision" :disabled="approvalTypeList.indexOf(2) == -1" :label="8">{{ $t('pages.burnMsg9') }}</el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
            </FormItem>
            <ResponseContent
              :select-style="{ 'margin-top': '5px', 'margin-left': '13px' }"
              :status="dialogStatus"
              :show-select="true"
              :rule-type-name="ruleTypeName"
              :editable="formable && ruleDisable"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="propRuleId"
              @ruleIsCheck="getRuleIsCheck"
              @getRuleId="getRuleId"
            />
            <ResponseContent
              v-if="hasEncPermision"
              :select-style="{ 'margin': '5px 0 5px 13px' }"
              :status="dialogStatus"
              :show-select="true"
              :rule-type-name="encRuleTypeName"
              :editable="formable && encRuleDisable"
              read-only
              :prop-check-rule="!!temp.isEncAlarm"
              :show-check-rule="true"
              :prop-rule-id="encPropRuleId"
              @ruleIsCheck="getEncRuleIsCheck"
              @getRuleId="getEncRuleId"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.blueTooth_blueTooth_Msg20')" name="second">
            <el-divider content-position="left" class="first-divider">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <div v-if="formable">
                <el-button size="small" @click="handleCreateRecordBluetooth()">
                  {{ $t('button.insert') }}
                </el-button>
                <el-button size="small" :disabled="!checkRuleDeleteable" @click="handleDeleteRecordBluetooth()">
                  {{ $t('button.delete') }}
                </el-button>
                <el-button size="small" @click="handleClearRecordBluetooth()">
                  {{ $t('button.clear') }}
                </el-button>
              </div>
              <grid-table
                ref="checkRuleList"
                auto-height
                :multi-select="formable"
                :show-pager="false"
                :col-model="recordCheckColModel"
                :row-datas="temp.recordBluetoothList"
                @selectionChangeEnd="checkRuleSelectionChange"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <el-radio-group v-model="temp.recordCheckType" :disabled="!formable" style="margin-left: 18px;">
              <el-radio :label="0">{{ $t('pages.blueTooth_blueTooth_Msg22') }}</el-radio>
              <el-radio :label="1">{{ $t('pages.blueTooth_blueTooth_Msg21') }}</el-radio>
            </el-radio-group>
            <div style="margin-left: 18px; margin-top: 5px; color: #409EFF">{{ $t('pages.blueTooth_blueTooth_Msg24') }}
              <span>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content" style="width: 480px;">
                    {{ $t('pages.blueTooth_blueTooth_Msg25') }}<br>
                    {{ $t('pages.blueTooth_blueTooth_Msg26') }}<br>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
            </div>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem prop="fileBackUpLimit" label-width="30px">
              <el-checkbox v-model="temp.disable" :disabled="!formable" :true-label="0" :false-label="1" style="display: inline;" @change="backupchange">
                <i18n path="pages.blueTooth_Msg1">
                  <el-input-number slot="size" v-model="temp.fileBackUpLimit" :disabled="!formable || temp.disable==1" :controls="false" step-strictly :min="1" :max="10240" size="mini" style="width: 100px;"/>
                </i18n>
              </el-checkbox>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 480px;">{{ $t('pages.blueTooth_Msg3') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-button style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
            </FormItem>
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="()=>{dialogFormVisible = false;}">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>

    <!-- 添加蓝牙检测规则 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.insertInfo', { info: $t('pages.bluetooth') })"
      :visible.sync="bluetoothAddVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div class="tree-container" style="height: 450px;">
        <strategy-target-tree ref="bluetoothTargetTree" :showed-tree="['terminal']" @data-change="bluetoothTargetNodeChange" />
      </div>
      <div class="table-container-dialog" style="height: 450px; padding-right: 5px; overflow: auto;">
        <grid-table
          ref="bluetoothSelectList"
          row-key="id"
          :height="290"
          :show-pager="false"
          :col-model="scanColModel"
          :multi-select="true"
          :row-datas="allDevTypes"
          :loading="loading"
        />
        <Form ref="dataForm2" :hide-required-asterisk="true" :model="tempW" :rules="bluetoothRules" label-width="100px" style="width: 100%; margin-top: 10px;">
          <FormItem :label="$t('pages.blueTooth_name')" prop="blueName">
            <el-input v-model="tempW.blueName" no-limit @blur="resetNameValid"></el-input>
          </FormItem>
          <FormItem :label="$t('pages.blueTooth_blueAddress')" prop="blueAddress">
            <el-input v-model="tempW.blueAddress" @blur="resetNameValid"></el-input>
          </FormItem>
          <FormItem :label="$t('pages.blueTooth_devType')" prop="devType">
            <el-select v-model="tempW.devType" style="width: 100%;">
              <el-option v-for="item in devTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </FormItem>
          <el-button style="float: right;" @click="createData2">{{ $t('pages.PrintSet_Msg7') }}</el-button>
        </Form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createBlueData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="bluetoothAddVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!--单个修改蓝牙管控规则-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="blueToothTextMap[ruleDialogStatus]"
      :visible.sync="blueToothRuleVisible"
      width="500px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm3" :hide-required-asterisk="true" :model="tempW" :rules="bluetoothRules" label-width="120px" style="width: 100%; margin-top: 10px;">
        <FormItem :label="$t('pages.blueTooth_name')" prop="blueName">
          <el-input v-model="tempW.blueName" no-limit @blur="validateActiveType"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.blueTooth_blueAddress')" prop="blueAddress">
          <el-input v-model="tempW.blueAddress" @blur="validateActiveType"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.blueTooth_activeType')" prop="activeType">
          <el-select v-model="tempW.activeType" style="width: 100%;" @change="activeTypeChange">
            <el-option :disabled="!tempW.blueName" :label="$t('pages.blueTooth_name')" :value="1" />
            <el-option :disabled="!tempW.blueAddress" :label="$t('pages.blueTooth_blueAddress')" :value="2" />
          </el-select>
        </FormItem>
        <FormItem v-if="updateType == 1" :label="$t('pages.blueTooth_matchType')" prop="matchType">
          <el-select v-model="tempW.matchType" style="width: 100%;" >
            <el-option :label="$t('pages.blueTooth_blueTooth_Msg11')" :value="1" />
            <el-option :disabled="tempW.activeType == 2" :label="$t('pages.blueTooth_blueTooth_Msg12')" :value="2" />
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.blueTooth_devType')" prop="devType">
          <el-select v-model="tempW.devType" style="width: 100%;">
            <el-option v-for="item in devTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="blueToothRuleVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importBlueToothConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />

    <bluetooth-add-dlg ref="bluetoothAddDlg" :add-type="addType" @submitEnd="submitEnd"/>
  </div>
</template>
<script>
import {
  createStrategy,
  deleteStrategy,
  getStrategyByName,
  getStrategyPage,
  updateStrategy
} from '@/api/behaviorManage/hardware/blueTooth'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'
import BluetoothAddDlg from './bluetoothAddDlg'

export default {
  name: 'BlueTooth',
  components: { BluetoothAddDlg, ResponseContent, ImportStg, BackupRuleContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 48,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      checkedColModel: [
        { prop: 'blueName', label: this.$t('pages.blueTooth_name'), width: '150', sort: true },
        { prop: 'blueAddress', label: this.$t('pages.blueTooth_blueAddress'), width: '150', sort: true },
        { prop: 'devType', label: this.$t('pages.blueTooth_devType'), width: '130', sort: true, formatter: this.devTypeFormatter },
        { prop: 'activeType', label: this.$t('pages.blueTooth_activeType'), width: '130', sort: true, formatter: this.activeTypeFormatter },
        { prop: 'matchType', label: this.$t('pages.blueTooth_matchType'), width: '130', sort: true, formatter: this.matchTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: this.editorFormatter, click: this.handleUpdateBlueTooth }
          ]
        }
      ],
      scanColModel: [
        { prop: 'blueName', label: this.$t('pages.blueTooth_name'), width: '150' },
        { prop: 'blueAddress', label: this.$t('pages.blueTooth_blueAddress'), width: '150' },
        { prop: 'devType', label: this.$t('pages.blueTooth_devType'), width: '120', formatter: this.devTypeFormatter }
      ],
      recordCheckColModel: [
        { prop: 'blueName', label: this.$t('pages.blueTooth_name'), width: '150', sort: true },
        { prop: 'blueAddress', label: this.$t('pages.blueTooth_blueAddress'), width: '150', sort: true },
        { prop: 'devType', label: this.$t('pages.blueTooth_devType'), width: '130', sort: true, formatter: this.devTypeFormatter },
        { prop: 'activeType', label: this.$t('pages.blueTooth_activeType'), width: '130', sort: true, formatter: this.activeTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', disabledFormatter: this.editorFormatter, click: this.handleUpdateRecordBlueTooth }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      blueToothRuleVisible: false,
      temp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        fileBackUpLimit: 20,
        entityType: '',
        entityId: undefined,
        disable: 1,  // 是否备份 1-表示不备份 0-备份
        isAlarm: 0, // 明文文件触发响应规则
        isEncAlarm: 0, // 密文文件触发响应规则
        ruleId: undefined, // 明文文件响应规则id
        encRuleId: undefined, // 密文文件响应规则id
        action: 0,  // 是否禁止 1-表示禁止 0-不禁止
        approvalType: 0,
        bluetoothList: [],  // 蓝牙文件管控设置-检测规则里的蓝牙设备
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined, // 备份过滤规则id
        recordCheckType: 1,
        recordBluetoothList: []  // 蓝牙文件记录设置-检测规则里的蓝牙设备
      },
      tempC: {},
      defaultTempC: {
        id: undefined,
        blueType: 1,
        blueValue: '',
        remark: ''
      },
      tableBluetoothList: [], // 检测规则里表格中显示的
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.blueToothStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.blueToothStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileBackUpLimit: [
          { validator: this.fileBackUpLimitValidator, trigger: 'blur' }
        ]
      },
      bluetoothRules: {
        blueName: [
          { validator: this.blueNameValidator, trigger: 'blur' }],
        blueAddress: [
          { validator: this.blueAddressValidator, trigger: 'blur' }
        ],
        devType: [
          { required: true, message: this.$t('components.required'), trigger: 'change' }
        ],
        activeType: [
          { validator: this.activeTypeValidator, trigger: 'change' }
        ]
      },
      tempCRules: {
        blueValue: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.blueValueValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      propRuleId: undefined,  // 明文文件响应规则id
      encPropRuleId: undefined, // 密文文件响应规则id
      approvalTypeList: [], // 审批外发
      hasEncPermision: true,  // 是包含加解密模块
      // 触发明文文件响应规则
      ruleTypeName: this.$t('pages.burnMsg10'),
      // 触发密文文件响应规则
      encRuleTypeName: this.$t('pages.burnMsg11'),
      validRuleId: true,
      validEncRuleId: true,
      ruleDisable: false, // 当明文执行规则没有配置时,明文响应规则要置灰
      encRuleDisable: false, // 当密文执行规则没有配置时,明文响应规则要置灰
      checkBluetoothDeleteable: false,
      bluetoothAddVisible: false,
      checkRuleEditable: false,
      checkRuleDeleteable: false,
      scanBluetoothDevs: [], // 扫描出来的蓝牙信息
      inputBluetoothDevs: [], // 输入的蓝牙信息
      tempW: {},
      defaultTempW: { // 蓝牙信息表单字段
        id: '',
        blueName: '',
        blueAddress: '',
        devType: '',
        matchType: 1,  // 1-完全匹配 2-模糊匹配
        activeType: 2   // 1-蓝牙名称 2-蓝牙地址
      },
      ruleDialogStatus: '',
      loading: false,
      devTypeOptions: [
        { label: this.$t('pages.computer'), value: '1' },
        { label: this.$t('pages.phone'), value: '2' },
        { label: this.$t('pages.networkAccessPoint'), value: '3' },
        { label: this.$t('pages.audioAndVideo'), value: '4' },
        { label: this.$t('pages.parts'), value: '5' },
        { label: this.$t('pages.imaging'), value: '6' },
        { label: this.$t('pages.wearable'), value: '7' },
        { label: this.$t('pages.toys'), value: '8' },
        { label: this.$t('pages.healthy'), value: '9' },
        { label: this.$t('pages.other'), value: '0' }
        // { label: this.$t('pages.softwareTypes5'), value: 'F' }
      ],
      blueTypeOptions: [
        { label: '蓝牙名称', value: 1 },
        { label: '蓝牙设备地址', value: 2 }
      ],
      blueToothTextMap: {
        update: this.i18nConcatText(this.$t('pages.bluetoothInfo'), 'update')
      },
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined, // 备份过滤规则id
      recordCheckType: 1, // 执行规则类型: 0-监视指定名称   1-例外指定名称
      addType: 1, // 1-蓝牙文件管控设置添加，2-蓝牙文件记录设置添加
      updateType: 1,  // 1-蓝牙文件管控设置-检测规则修改，2-蓝牙文件记录设置-检测规则修改
      activeName: 'first'
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    blueMap() {
      const map = {}
      this.devTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    },
    allDevTypes() {
      const devs = [...this.inputBluetoothDevs, ...this.scanBluetoothDevs]
      return devs
    }
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取注册模块
    this.listModule()
  },
  methods: {
    submitEnd(datas) {
      // 把选中的蓝牙添加到设置里面,相同的蓝牙信息会直接覆盖
      datas.forEach(item => {
        let index = -1
        if (this.addType == 2) {
          index = this.temp.recordBluetoothList.findIndex(bluetooth => {
            if (bluetooth.blueAddress == item.blueAddress && bluetooth.blueName == item.blueName) {
              return true
            }
          })
        } else {
          index = this.tableBluetoothList.findIndex(bluetooth => {
            if (bluetooth.blueAddress == item.blueAddress && bluetooth.blueName == item.blueName) {
              return true
            }
          })
        }

        if (index > -1) {
          if (this.addType == 2) {
            this.temp.recordBluetoothList.splice(index, 1, item)
          } else {
            this.tableBluetoothList.splice(index, 1, item)
          }
        } else {
          if (this.addType == 2) {
            this.temp.recordBluetoothList.push(item)
          } else {
            this.tableBluetoothList.push(item)
          }
        }
      })
    },
    checkRuleTable() {
      return this.$refs['checkRuleList']
    },
    checkRuleSelectionChange(rowDatas) {
      this.checkRuleDeleteable = rowDatas && rowDatas.length > 0
    },
    blueTypeChange(val) {
      this.tempC.blueValue = ''
      this.$refs['checkRuleForm'] && this.$refs['checkRuleForm'].clearValidate()
    },
    createCheckRule() {
      let validate
      this.$refs['checkRuleForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempC))
          rowData.id = new Date().getTime()
          this.temp.bluethRecordCheckRules.unshift(rowData)
          this.cancelCheckRule()
          validate = valid
        }
      })
      return validate
    },
    updateCheckRule() {
      let validate
      this.$refs['checkRuleForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempC))
          for (let i = 0, size = this.temp.bluethRecordCheckRules.length; i < size; i++) {
            const data = this.temp.bluethRecordCheckRules[i]
            if (rowData.id === data.id) {
              this.temp.bluethRecordCheckRules.splice(i, 1, rowData)
              break
            }
          }
          this.cancelCheckRule()
          validate = valid
        }
      })
      return validate
    },
    deleteCheckRule() {
      this.cancelCheckRule()
      const toDeleteIds = this.checkRuleTable().getSelectedIds()
      this.checkRuleTable().deleteRowData(toDeleteIds, this.temp.bluethRecordCheckRules)
    },
    cancelCheckRule() {
      this.$refs['checkRuleForm'] && this.$refs['checkRuleForm'].clearValidate()
      this.resetTempC()
    },
    beforeupdateCheckRule() {
      Object.assign(this.tempC, this.checkRuleTable().getSelectedDatas()[0])
    },
    blueTypeFormatter: function(row, data) {
      let label
      this.blueTypeOptions.forEach(op => {
        if (data == op.value) {
          label = op.label
        }
      })
      return label
    },
    async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = resp.data
        this.ruleTypeName = this.hasEncPermision ? this.$t('pages.burnMsg10') : this.$t('pages.triggerResponseRule')
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempC() {
      this.tempC = Object.assign({}, this.defaultTempC)
    },
    resetTemp() {
      this.activeName = 'first'
      this.propRuleId = undefined
      this.encPropRuleId = undefined
      this.approvalTypeList.splice(0)
      this.validRuleId = true
      this.validEncRuleId = true
      this.ruleDisable = false
      this.encRuleDisable = false
      this.tableBluetoothList.splice(0)
      this.scanBluetoothDevs.splice(0)
      this.inputBluetoothDevs.splice(0)
      this.temp = { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        fileBackUpLimit: 20,
        entityType: '',
        entityId: undefined,
        disable: 1,
        isAlarm: 0,
        ruleId: undefined,
        isEncAlarm: 0,
        encRuleId: undefined,
        action: 0,
        approvalType: 0,
        bluetoothList: [],
        recordCheckType: 1,
        recordBluetoothList: []
      }
      this.resetTempC()
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      // 统一样式，当不勾选备份复选框时，备份的阀值设置显示为默认值
      if (this.temp.disable == 1 || this.temp.fileBackUpLimit == 0) {
        this.temp.fileBackUpLimit = 20
      }
      this.tableBluetoothList = this.temp.bluetoothList.slice()
      // 兼容旧版本，旧版本原先就有禁止传输的功能（根据是否有approvalType这个字段来判断是否是旧版本）
      if (row.approvalType == null || row.approvalType == undefined) {
        if (this.hasEncPermision) {
          if (this.temp.action === 1) {
            this.approvalTypeList.push(1)
            this.approvalTypeList.push(2)
          }
          if (row.ruleId) {
            this.propRuleId = row.ruleId
            this.encPropRuleId = row.ruleId
            this.temp.isAlarm = 1
            this.temp.isEncAlarm = 1
          }
        } else {
          if (this.temp.action === 1) {
            this.approvalTypeList.push(1)
          }
          if (row.ruleId) {
            this.propRuleId = row.ruleId
          }
        }
      } else {
        if (this.temp.approvalType > 0) {
          this.approvalTypeList = this.numToList(this.temp.approvalType, 4)
          // 当勾选的有允许文件外发审批时，approvalType是没有保存禁止外发的值的，所以这边要做下处理
          if (this.approvalTypeList.indexOf(4) > -1) {
            this.approvalTypeList.push(1)
          }
          if (this.approvalTypeList.indexOf(8) > -1) {
            this.approvalTypeList.push(2)
          }
        }
        this.propRuleId = row.ruleId
        this.encPropRuleId = row.encRuleId
      }
      // 当明文执行规则没配置时,无法配置明文响应规则
      if (this.approvalTypeList.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    formatFormData() {
      this.temp.ruleId = this.propRuleId
      this.temp.encRuleId = this.encPropRuleId
      // 当勾选了允许审批外发时，要去除掉禁止的值比如禁止审批外发为1允许审批外发为4两个都勾选时approvalType只保存4
      this.temp.approvalType = this.getSum(this.approvalTypeList)
      if (this.approvalTypeList.indexOf(4) > -1) {
        this.temp.approvalType = this.temp.approvalType - 1
      }
      if (this.approvalTypeList.indexOf(8) > -1) {
        this.temp.approvalType = this.temp.approvalType - 2
      }
      // 处理禁止传输的值,兼容旧版本
      if (this.hasEncPermision) {
        // 有加解密模块当禁止明文传输跟禁止密文传输都禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(2) > -1 && this.approvalTypeList.indexOf(4) == -1 && this.approvalTypeList.indexOf(8) == -1) {
          this.temp.action = 1
        } else {
          this.temp.action = 0
        }
      } else {
        // 无加解密模块，当禁止明文传输为禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(4) == -1) {
          this.temp.action = 1
        } else {
          this.temp.action = 0
        }
      }
      this.validRuleId = true
      this.validEncRuleId = true
      if (this.temp.isAlarm === 1 && !this.temp.ruleId) {
        this.validRuleId = false
      }
      if (this.temp.isEncAlarm === 1 && !this.temp.encRuleId) {
        this.validEncRuleId = false
      }
      this.temp.bluetoothList.splice(0, this.temp.bluetoothList.length, ...this.tableBluetoothList)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.formatFormData()
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId) {
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.formatFormData()
      this.$refs['dataForm'].validate((valid) => {
        if (valid && valid && this.validRuleId && this.validEncRuleId) {
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    //  校验mac
    validMac(mac) {
      const reg = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
      return reg.test(mac)
    },
    blueValueValidator(rule, value, callback) {
      if (this.tempC.blueType == 2 && !this.validMac(value)) {
        callback(new Error(this.$t('pages.multiLoginAuth_macValidatorMessage') + ''))
      } else {
        callback()
      }
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const msgArr = []
      if (row.approvalType >= 0) {
        const approvalList = this.numToList(row.approvalType, 4)
        if (this.hasEncPermision) {
          if (approvalList.indexOf(1) > -1) {
            // 禁止明文文件传输
            msgArr.push(this.$t('pages.burnMsg14'))
          }
          if (approvalList.indexOf(2) > -1) {
            // 禁止密文文件传输
            msgArr.push(this.$t('pages.burnMsg15'))
          }
          if (approvalList.indexOf(4) > -1) {
            // 允许明文文件外发审批
            msgArr.push(this.$t('pages.burnMsg6'))
          }
          if (approvalList.indexOf(8) > -1) {
            // 允许密文文件外发审批
            msgArr.push(this.$t('pages.burnMsg9'))
          }
        } else {
          if (approvalList.indexOf(1) > -1) {
            // 禁止文件传输
            msgArr.push(this.$t('pages.prohibitFileTransfer'))
          }
          if (approvalList.indexOf(4) > -1) {
            // 允许文件外发审批
            msgArr.push(this.$t('pages.burnMsg7'))
          }
        }
      } else if (row.action === 1) {
        if (this.hasEncPermision) {
          // 禁止明文文件传输
          msgArr.push(this.$t('pages.burnMsg14'))
          // 禁止密文文件传输
          msgArr.push(this.$t('pages.burnMsg15'))
        } else {
          // 禁止文件传输
          msgArr.push(this.$t('pages.prohibitFileTransfer'))
        }
      }
      if (this.hasEncPermision) {
        if (row.ruleId) {
          // 触发明文文件响应规则
          msgArr.push(this.$t('pages.burnMsg10'))
        }
        if (row.encRuleId) {
          // 触发密文文件响应规则
          msgArr.push(this.$t('pages.burnMsg11'))
        }
      } else {
        if (row.encRuleId) {
          // 触发响应规则
          msgArr.push(this.$t('pages.triggerResponseRule'))
        }
      }
      const recordMsgArr = []
      if (row.fileBackUpLimit) {
        // 文件备份限制{row.fileBackUpLimit}MB
        recordMsgArr.push(this.$t('pages.fileBackUpLimit1') + '：' + row.fileBackUpLimit + 'MB')
      } else {
        recordMsgArr.push(this.$t('pages.fileBackUpLimit1') + '：' + this.$t('pages.backupRule1'))
      }
      return `${this.$t('pages.blueTooth_blueTooth_Msg19')}：{ ${msgArr.length === 0 ? '' : this.$t('pages.burnMsgToothExecuteMessage_1') + '：'}${msgArr.join('、')} }；
              ${this.$t('pages.blueTooth_blueTooth_Msg20')}：{ ${recordMsgArr.length === 0 || row.recordCheckType === undefined ? '' : row.recordCheckType === 1 ? this.$t('pages.blueTooth_blueTooth_Msg21_1') + '：' : this.$t('pages.blueTooth_blueTooth_Msg22_1') + '：'}${recordMsgArr.join('、')} }`
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    editorFormatter(row) {
      return !this.formable
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    getEncRuleId(value) {
      this.encPropRuleId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    getEncRuleIsCheck(value) {
      this.temp.isEncAlarm = value
    },
    handleApprovalTypeChange(value) {
      // 当取消了勾选禁止明文文件外发，允许密文文件外发也要跟着取消
      if (value.indexOf(4) > -1 && value.indexOf(1) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(4), 1)
      }
      if (value.indexOf(8) > -1 && value.indexOf(2) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(8), 1)
      }
      // 勾选了执行规则，响应规则才能勾选
      if (value.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (value.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
    },
    handleCreateRecordBluetooth() {
      this.addType = 2
      this.$refs['bluetoothAddDlg'].show()
    },
    handleDeleteRecordBluetooth() {
      const toDeleteKeys = this.checkRuleTable().getSelectedKeys()
      this.checkRuleTable().deleteRowData(toDeleteKeys)
      const bluetoothArray = []
      this.temp.recordBluetoothList.forEach(bluetooth => {
        if (!(toDeleteKeys.join(',').indexOf(bluetooth.id) > -1)) {
          bluetoothArray.push(bluetooth)
        }
      })
      this.temp.recordBluetoothList.splice(0, this.temp.recordBluetoothList.length, ...bluetoothArray)
    },
    handleClearRecordBluetooth() {
      this.checkRuleTable().clearRowData()
      this.temp.recordBluetoothList.splice(0, this.temp.recordBluetoothList.length)
    },
    handleCreateBluetooth() {
      this.addType = 1
      this.$refs['bluetoothAddDlg'].show()
    },
    checkBluetoothSelectionChangeEnd(rowDatas) {
      this.checkBluetoothDeleteable = rowDatas && rowDatas.length > 0
    },
    handleDeleteCheckedBluetooth() {
      const toDeleteKeys = this.checkedBluetoothGrid().getSelectedKeys()
      this.checkedBluetoothGrid().deleteRowData(toDeleteKeys)
      const bluetoothArray = []
      this.tableBluetoothList.forEach(bluetooth => {
        if (!(toDeleteKeys.join(',').indexOf(bluetooth.id) > -1)) {
          bluetoothArray.push(bluetooth)
        }
      })
      this.tableBluetoothList.splice(0, this.tableBluetoothList.length, ...bluetoothArray)
    },
    handleClearCheckedBluetooth() {
      this.checkedBluetoothGrid().clearRowData()
      this.tableBluetoothList.splice(0, this.tableBluetoothList.length)
    },
    resetTempW() {
      this.tempW = Object.assign({}, this.defaultTempW)
    },
    checkedBluetoothGrid() {
      return this.$refs['checkedBluetoothGrid']
    },
    createData2() {
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          const index = this.allDevTypes.findIndex(item => {
            return item.blueAddress == this.tempW.blueAddress
          })
          const index1 = this.allDevTypes.findIndex(item => {
            return item.blueName == this.tempW.blueName
          })
          if (index > -1 && !!this.tempW.blueAddress) {
            this.$message({
              message: this.$t('pages.blueTooth_blueTooth_Msg15', { info: this.tempW.blueAddress }),
              type: 'error',
              duration: 2000
            })
            return
          }
          if (index1 > -1 && !!this.tempW.blueName) {
            this.$message({
              message: this.$t('pages.blueTooth_blueTooth_Msg16', { info: this.tempW.blueName }),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.tempW.id = Date.now()
          this.inputBluetoothDevs.push(this.tempW)
          this.resetTempW()
          this.$nextTick(() => {
            this.$refs['dataForm2'].clearValidate()
          })
        }
      })
    },
    bluetoothTargetNodeChange: function(tabName, data) {
      this.loading = false
      if (data) {
        if (data.type == 1) {
          this.loading = true
          this.termId = data.dataId
          this.termName = data.label
          this.scanBluetoothDevs.splice(0)
          this.$socket.sendToUser(this.termId, '/listBluetooth', this.termId, (respond, handle) => {
            this.loading = false
            handle.close()
            // 判断终端id是否等于当前请求的终端id，防止用户连续点击不同终端导致数据加载出错
            if (respond.data) {
              respond.data.forEach((item, index) => {
                const p = {
                  id: Date.now() + index,
                  blueName: item.blueName,
                  blueAddress: item.blueAddress,
                  devType: item.devType,
                  matchType: 1,
                  activeType: 2
                }
                this.scanBluetoothDevs.push(p)
              })
            }
          }, (handle) => {
            this.loading = false
            handle.close()
            this.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
          })
        }
      }
    },
    blueNameValidator(rule, value, callback) {
      if (!value && !this.tempW.blueAddress) {
        // callback(new Error(this.$t('pages.ipMac_Msg7')))
        callback(new Error(this.$t('pages.blueTooth_blueTooth_Msg17')))
      } else {
        callback()
      }
    },
    blueAddressValidator(rule, value, callback) {
      if (!value && !this.tempW.blueName) {
        // callback(new Error(this.$t('pages.ipMac_Msg7')))
        callback(new Error(this.$t('pages.blueTooth_blueTooth_Msg17')))
      } else {
        callback()
      }
    },
    activeTypeValidator(rule, value, callback) {
      if (value == 1 && !this.tempW.blueName) {
        // callback(new Error(this.$t('pages.ipMac_Msg7')))
        callback(new Error(this.$t('pages.blueTooth_blueTooth_Msg18')))
      } else if (value == 2 && !this.tempW.blueAddress) {
        callback(new Error(this.$t('pages.blueTooth_blueTooth_Msg18')))
      } else {
        callback()
      }
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.temp.disable == 0) {
        if (!value) {
          return callback(new Error(this.$t('components.required')))
        } else if (value > 10240) {
          return callback(new Error(this.$t('pages.blueTooth_Msg4')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    devTypeFormatter: function(row, data) {
      return this.blueMap[data]
    },
    activeTypeFormatter: function(row, data) {
      return data == 1 ? this.$t('pages.blueTooth_name') : this.$t('pages.blueTooth_blueAddress')
    },
    matchTypeFormatter: function(row, data) {
      return data == 1 ? this.$t('pages.blueTooth_blueTooth_Msg11') : this.$t('pages.blueTooth_blueTooth_Msg12')
    },
    createBlueData() {
      const datas = this.$refs.bluetoothSelectList.getSelectedDatas()
      if (datas.length === 0) {
        this.$message({
          message: this.$t('pages.blueTooth_blueTooth_Msg14'),
          type: 'error',
          duration: 2000
        })
        return
      }
      // 把选中的蓝牙添加到设置里面,相同的蓝牙信息会直接覆盖
      datas.forEach(item => {
        const index = this.tableBluetoothList.findIndex(bluetooth => {
          if (bluetooth.blueAddress == item.blueAddress && bluetooth.blueName == item.blueName) {
            return true
          }
        })
        if (index == -1) {
          item.matchType = 1
          if (item.blueAddress) {
            item.activeType = 2
          } else {
            item.activeType = 1
          }
          this.tableBluetoothList.push(item)
        }
      })
      this.bluetoothAddVisible = false
    },
    saveData() {
      this.$refs['dataForm3'].validate((valid) => {
        if (valid) {
          let index = -1
          if (this.updateType == 1) {
            index = this.tableBluetoothList.findIndex(item => {
              if (this.tempW.blueAddress == item.blueAddress &&
                this.tempW.blueName == item.blueName && this.tempW.id != item.id) {
                return true
              }
            })
          } else {
            index = this.temp.recordBluetoothList.findIndex(item => {
              if (this.tempW.blueAddress == item.blueAddress &&
                this.tempW.blueName == item.blueName && this.tempW.id != item.id) {
                return true
              }
            })
          }

          if (index == -1) {
            if (this.tempW.id) {
              if (this.updateType == 1) {
                this.checkedBluetoothGrid().updateRowData(this.tempW)
              } else {
                this.checkRuleTable().updateRowData(this.tempW)
              }
            }
            this.blueToothRuleVisible = false
          } else {
            this.$message({
              message: this.$t('pages.wifiBlock_text5'),
              type: 'error',
              duration: 2000
            })
          }
        }
      })
    },
    activeTypeDisable(row, col) {
      if (!this.formable) {
        return !this.formable
      }
      if (!row.blueName || !row.blueAddress) {
        return true
      }
      return false
    },
    matchTypeDisable(row, col) {
      if (!this.formable) {
        return !this.formable
      }
      if (row.activeType == 2) {
        return true
      }
      return false
    },
    activeTypeChange(value) {
      if (value == 2) {
        this.tempW.matchType = 1
      }
    },
    handleUpdateBtInfo(row) {
      this.resetTempW()
      this.tempW = Object.assign(this.tempW, row)
      this.tempW.devType = this.tempW.devType + ''
      this.blueToothRuleVisible = true
      this.ruleDialogStatus = 'update'
      this.$nextTick(() => {
        this.$refs['dataForm3'].clearValidate()
      })
    },
    handleUpdateBlueTooth(row) {
      this.updateType = 1
      this.handleUpdateBtInfo(row)
    },
    handleUpdateRecordBlueTooth(row) {
      this.updateType = 2
      this.handleUpdateBtInfo(row)
    },
    validateActiveType() {
      this.$refs['dataForm3'].validateField(['activeType', 'blueName', 'blueAddress'])
    },
    resetNameValid() {
      this.$refs['dataForm2'].validateField(['blueName', 'blueAddress'])
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    },
    backupchange(data) {
      if (data == 1) {
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.blue-tooth-stg-dlg {
  >>>.first-divider {
    margin-top: 10px;
  }
}
</style>
