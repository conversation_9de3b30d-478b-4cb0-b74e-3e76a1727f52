<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('text.insertInfo', { info: $t('pages.bluetooth') })"
    :visible.sync="bluetoothAddVisible"
    width="980px"
    @dragDialog="handleDrag"
  >
    <div class="tree-container" style="height: 580px;">
      <strategy-target-tree ref="bluetoothTargetTree" :showed-tree="['terminal']" @data-change="bluetoothTargetNodeChange" />
    </div>
    <div class="table-container-dialog">
      <Form ref="dataForm" :hide-required-asterisk="true" :model="temp" :rules="rules" label-width="100px" style="width: 100%; margin-top: 10px;">
        <div>
          <el-row>
            <el-col :span="6">
              <FormItem label-width="1">
                <el-checkbox v-model="allMultiUpdate" :disabled="!multiUpdate">{{ $t('pages.blueTooth_blueTooth_Msg23') }}</el-checkbox>
              </FormItem>
            </el-col>
            <el-col v-if="allMultiUpdate" :span="8">
              <FormItem :label="$t('pages.blueTooth_activeType')" label-width="80px">
                <el-select v-model="activeType" style="width:150px" @change="activeTypeChange">
                  <el-option v-for="item in activeTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </FormItem>
            </el-col>
            <el-col v-if="allMultiUpdate && addType == 1" :span="8">
              <FormItem :label="$t('pages.blueTooth_matchType')" label-width="85px">
                <el-select v-model="matchType" style="width:150px" @visible-change="matchTypeChange">
                  <el-option v-for="item in matchTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </FormItem>
            </el-col>
          </el-row>
        </div>
        <grid-table
          ref="bluetoothSelectList"
          row-key="id"
          :height="300"
          :show-pager="false"
          :col-model="colModel"
          :multi-select="true"
          :row-datas="allDevTypes"
          :loading="loading"
          @selectionChangeEnd="selectionChangeEnd"
        />
        <FormItem :label="$t('pages.blueTooth_name')" prop="blueName" style="margin-top: 5px;">
          <el-col :span="12">
            <el-input v-model="temp.blueName" no-limit maxlength="" @blur="resetNameValid"></el-input>
          </el-col>
        </FormItem>
        <FormItem :label="$t('pages.blueTooth_blueAddress')" prop="blueAddress">
          <el-col :span="12">
            <el-input v-model="temp.blueAddress" maxlength="" @blur="resetNameValid"></el-input>
          </el-col>
        </FormItem>
        <FormItem :label="$t('pages.blueTooth_devType')" prop="devType">
          <el-select v-model="temp.devType" style="width:225px">
            <el-option v-for="item in devTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.blueTooth_activeType')" prop="activeType">
          <el-select v-model="temp.activeType" style="width:225px" @change="activeTypeChange1">
            <el-option v-for="item in activeTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </FormItem>
        <FormItem v-if="addType == 1" :label="$t('pages.blueTooth_matchType')" prop="matchType">
          <el-select v-model="temp.matchType" style="width:225px">
            <el-option v-for="item in matchTypeOptions" :key="item.value" :label="item.label" :value="item.value" :disabled="temp.activeType == 2 && item.value == 2"></el-option>
          </el-select>
        </FormItem>
        <FormItem>
          <el-button @click="createData">{{ $t('pages.PrintSet_Msg7') }}</el-button>
        </FormItem>
      </Form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="createBlueData()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="bluetoothAddVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'BluetoothAddDlg',
  props: {
    addType: { type: Number, default: 0 }  // 1-蓝牙文件管控设置添加，2-蓝牙文件记录设置添加
  },
  data() {
    return {
      colModel: [],
      matchTypeCol: { prop: 'matchType', label: this.$t('pages.blueTooth_matchType'), width: '120', sort: true, type: 'select', optionDisabled: this.matchTypeDisabled,
        options: [{ value: 1, label: 'completeMatch' }, { value: 2, label: 'fuzzyMatch' }] },
      activeTypeCol: { prop: 'activeType', label: this.$t('pages.blueTooth_activeType'), width: '120', sort: true, type: 'select', optionDisabled: this.activeTypeDisabled, change: this.activeTypeSelectChange,
        options: [{ value: 1, label: 'blueTooth_name' }, { value: 2, label: 'blueTooth_blueAddress' }] },
      temp: {},
      defaultTemp: { // 蓝牙信息表单字段
        id: '',
        blueName: '',
        blueAddress: '',
        devType: '',
        matchType: undefined,  // 1-完全匹配 2-模糊匹配
        activeType: undefined   // 1-蓝牙名称 2-蓝牙地址
      },
      rules: {
        blueName: [{ validator: this.blueNameValidator, trigger: 'blur' }],
        blueAddress: [{ validator: this.blueAddressValidator, trigger: 'blur' }],
        devType: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }],
        activeType: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.activeTypeValidator, trigger: 'blur' }
        ],
        matchType: [{ required: true, message: this.$t('components.required'), trigger: 'blur' }]
      },
      bluetoothAddVisible: false,
      loading: false,
      multiUpdate: false, // 是否可开启批量配置
      allMultiUpdate: false,
      scanBluetoothDevs: [], // 扫描出来的蓝牙信息
      inputBluetoothDevs: [], // 输入的蓝牙信息
      devTypeOptions: [
        { label: this.$t('pages.computer'), value: '1' },
        { label: this.$t('pages.phone'), value: '2' },
        { label: this.$t('pages.networkAccessPoint'), value: '3' },
        { label: this.$t('pages.audioAndVideo'), value: '4' },
        { label: this.$t('pages.parts'), value: '5' },
        { label: this.$t('pages.imaging'), value: '6' },
        { label: this.$t('pages.wearable'), value: '7' },
        { label: this.$t('pages.toys'), value: '8' },
        { label: this.$t('pages.healthy'), value: '9' },
        { label: this.$t('pages.other'), value: '0' }
        // { label: this.$t('pages.softwareTypes5'), value: 'F' }
      ],
      activeTypeOptions: [
        { label: this.$t('pages.blueTooth_name'), value: 1 },
        { label: this.$t('pages.blueTooth_blueAddress'), value: 2 }
      ],
      matchTypeOptions: [
        { label: this.$t('pages.blueTooth_blueTooth_Msg11'), value: 1 },
        { label: this.$t('pages.blueTooth_blueTooth_Msg12'), value: 2 }
      ],
      activeType: undefined,
      matchType: undefined
    }
  },
  computed: {
    blueMap() {
      const map = {}
      this.devTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    },
    allDevTypes() {
      const devs = [...this.inputBluetoothDevs, ...this.scanBluetoothDevs]
      return devs
    }
  },
  methods: {
    activeTypeSelectChange(row, col) {
      if (row[col.prop] == 2) {
        row['matchType'] = 1
      }
    },
    matchTypeDisabled(item, row, col) {
      if (row.activeType == 2 && col.prop == 'matchType' && item.value == 2) {
        return true
      }
      return false
    },
    activeTypeDisabled(item, row, col) {
      if (row.blueAddress == '' && col.prop == 'activeType' && item.value == 2) {
        return true
      } else if (row.blueName == '' && col.prop == 'activeType' && item.value == 1) {
        return true
      }
      return false
    },
    handleDrag() {},
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    selectionChangeEnd(rowDatas) {
      this.multiUpdate = rowDatas && rowDatas.length > 0
    },
    activeTypeChange1(val) {
      if (val == 2) {
        this.temp.matchType = 1
      }
    },
    show() {
      this.resetTemp()
      this.colModel = [
        { prop: 'blueName', label: this.$t('pages.blueTooth_name'), width: '150', sort: true },
        { prop: 'blueAddress', label: this.$t('pages.blueTooth_blueAddress'), width: '150', sort: true },
        { prop: 'devType', label: this.$t('pages.blueTooth_devType'), width: '130', sort: true, formatter: this.devTypeFormatter }
      ]
      this.scanBluetoothDevs.splice(0)
      this.inputBluetoothDevs.splice(0)
      this.$refs.bluetoothTargetTree && this.$refs.bluetoothTargetTree.clearFilter()
      this.$refs.bluetoothTargetTree && this.$refs.bluetoothTargetTree.setCurrentKey()
      this.resetMulti()
      this.bluetoothAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        if (this.addType == 1) {
          this.colModel.push(this.activeTypeCol)
          this.colModel.push(this.matchTypeCol)
        } else if (this.addType == 2) {
          this.colModel.push(this.activeTypeCol)
        }
      })
    },
    // 重置批量配置
    resetMulti() {
      this.multiUpdate = false
      this.allMultiUpdate = false
      this.activeType = undefined
      this.matchType = undefined
    },
    bluetoothTargetNodeChange: function(tabName, data) {
      this.loading = false
      if (data) {
        if (data.type == 1) {
          this.loading = true
          this.termId = data.dataId
          // this.termName = data.label
          this.resetMulti()
          this.scanBluetoothDevs.splice(0)
          this.$socket.sendToUser(this.termId, '/listBluetooth', this.termId, (respond, handle) => {
            this.loading = false
            handle.close()
            // 判断终端id是否等于当前请求的终端id，防止用户连续点击不同终端导致数据加载出错
            if (respond.data) {
              respond.data.forEach((item, index) => {
                const activeType = item.blueAddress ? 2 : 1
                const p = {
                  id: Date.now() + index,
                  blueName: item.blueName,
                  blueAddress: item.blueAddress,
                  devType: item.devType,
                  matchType: 1,
                  activeType: activeType
                }
                this.scanBluetoothDevs.push(p)
              })
            }
          }, (handle) => {
            this.loading = false
            handle.close()
            this.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
          })
        }
      }
    },
    resetNameValid() {
      this.$refs['dataForm'].validateField(['blueName', 'blueAddress'])
    },
    activeTypeChange(data) {
      const datas = this.$refs.bluetoothSelectList.getSelectedDatas()
      if (datas && datas.length > 0) {
        datas.forEach(d => {
          if ((data == 1 && d.blueName) || (data == 2 && d.blueAddress)) {
            d.activeType = data
            if (data == 2) {
              d.matchType = 1
            }
          }
        })
      }
    },
    matchTypeChange(data) {
      if (!data) {
        const datas = this.$refs.bluetoothSelectList.getSelectedDatas()
        if (datas && datas.length > 0) {
          datas.forEach(d => {
            // 当生效类型为蓝牙地址时，匹配类型只支持完全匹配，不允许修改
            if (d.activeType != 2) {
              d.matchType = this.matchType
            }
          })
        }
      }
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const index = this.allDevTypes.findIndex(item => {
            return item.blueAddress == this.temp.blueAddress
          })
          const index1 = this.allDevTypes.findIndex(item => {
            return item.blueName == this.temp.blueName
          })
          if (index > -1 && !!this.temp.blueAddress) {
            this.$message({
              message: this.$t('pages.blueTooth_blueTooth_Msg15', { info: this.temp.blueAddress }),
              type: 'error',
              duration: 2000
            })
            return
          }
          if (index1 > -1 && !!this.temp.blueName) {
            this.$message({
              message: this.$t('pages.blueTooth_blueTooth_Msg16', { info: this.temp.blueName }),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.temp.id = Date.now()
          this.inputBluetoothDevs.push(this.temp)
          this.resetTemp()
        }
      })
    },
    createBlueData() {
      const datas = this.$refs.bluetoothSelectList.getSelectedDatas()
      if (datas.length === 0) {
        this.$message({
          message: this.$t('pages.blueTooth_blueTooth_Msg14'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.bluetoothAddVisible = false
      this.$emit('submitEnd', datas)
    },
    devTypeFormatter: function(row, data) {
      return this.blueMap[data]
    },
    activeTypeFormatter: function(row, data) {
      return data == 1 ? this.$t('pages.blueTooth_name') : this.$t('pages.blueTooth_blueAddress')
    },
    matchTypeFormatter: function(row, data) {
      return data == 1 ? this.$t('pages.blueTooth_blueTooth_Msg11') : this.$t('pages.blueTooth_blueTooth_Msg12')
    },
    blueNameValidator(rule, value, callback) {
      if (!value && !this.temp.blueAddress) {
        // callback(new Error(this.$t('pages.ipMac_Msg7')))
        callback(new Error(this.$t('pages.blueTooth_blueTooth_Msg17')))
      } else {
        callback()
      }
    },
    blueAddressValidator(rule, value, callback) {
      if (!value && !this.temp.blueName) {
        // callback(new Error(this.$t('pages.ipMac_Msg7')))
        callback(new Error(this.$t('pages.blueTooth_blueTooth_Msg17')))
      } else {
        callback()
      }
    },
    activeTypeValidator(rule, value, callback) {
      if (value == 1 && !this.temp.blueName) {
        callback(new Error(this.$t('pages.blueTooth_blueTooth_Msg18')))
      } else if (value == 2 && !this.temp.blueAddress) {
        callback(new Error(this.$t('pages.blueTooth_blueTooth_Msg18')))
      } else {
        callback()
      }
    }
  }
}
</script>
