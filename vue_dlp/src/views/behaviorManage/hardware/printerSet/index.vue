<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;">{{ strategyFormatter(props.detail) }}</span>
          </div>
        </template>
      </grid-table>
    </div>

    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="dialogStatus=''"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="margin-left:20px; margin-right: 20px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
        <LocalZoomIn parent-tag="el-dialog">
          <el-row v-if="formable">
            <el-col :span="15">
              <el-button size="small" @click="handleCreate2()">{{ $t('button.insert') }}</el-button>
              <el-button size="small" @click="handlePrinterImport()">{{ $t('pages.printerLibrary') }}{{ $t('button.import') }}</el-button>
              <el-button size="small" :disabled="!deleteable1" @click="handleDelete2()">{{ $t('button.delete') }}</el-button>
              <el-button size="small" @click="clearTable()">{{ $t('button.clear') }}</el-button>
            </el-col>
          </el-row>
          <grid-table
            ref="printerList"
            auto-height
            :show-pager="false"
            :col-model="colModel2"
            :multi-select="true"
            :row-data-api="loadPrinterList"
            @selectionChangeEnd="selectionChangeEnd1"
          />
        </LocalZoomIn>
        <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}" style="margin-top: 5px;">
          <div slot="header">
            <span>{{ $t('pages.programSettings') }}</span>
            <el-tooltip class="item" effect="dark" :content="$t('pages.PrintSet_Msg')" placement="bottom-start">
              <i class="el-icon-info" />
            </el-tooltip>
            <el-popover v-model="visible2" style="height: 200px" :disabled="!formable" placement="right" width="400" trigger="click">
              <tree-menu ref="exeList" style="height: 270px" multiple :data="defaultExeList" :is-filter="false" :default-expand-all="true"/>
              <div style="text-align: right; margin-top: 10px">
                <el-button type="primary" size="mini" @click="handleCheckedExe">{{ $t('button.confirm') }}</el-button>
                <el-button size="mini" @click="visible2 = false">{{ $t('button.cancel') }}</el-button>
              </div>
              <el-button slot="reference" size="small" :disabled="!formable" style="margin: 0;padding: 3px;">{{ $t('button.import') }}</el-button>
              <el-button v-if="formable" slot="reference" size="small" style="margin: 0 10px;" @click="handleAppImport()">
                {{ $t('button.applicationLibraryImport') }}
              </el-button>
            </el-popover>
          </div>
          <tag :list="temp.processList" :disabled="!formable"/>
        </el-card>
        <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.documentSuffix') }}</span>
            <el-tooltip class="item" effect="dark" :content="$t('pages.PrintSet_Msg1')" placement="bottom-start">
              <i class="el-icon-info" />
            </el-tooltip>
            <el-popover v-model="visible" style="height: 200px" :disabled="!formable" placement="right" width="400" trigger="click">
              <tree-menu ref="suffixList" style="height: 270px" multiple :data="defaultSuffixList" :is-filter="false" :default-expand-all="true"/>
              <div style="text-align: right; margin-top: 10px">
                <el-button type="primary" size="mini" @click="handleCheckedSuffix">{{ $t('button.confirm') }}</el-button>
                <el-button size="mini" @click="visible = false">{{ $t('button.cancel') }}</el-button>
              </div>
              <el-button slot="reference" size="small" :disabled="!formable" style="margin: 0;padding: 3px;">{{ $t('button.import') }}</el-button>
              <el-button v-if="formable" slot="reference" size="small" style="margin: 0 10px;" @click="handleFileSuffixImport()">
                {{ $t('button.FileSuffixLibImport') }}
              </el-button>
            </el-popover>
          </div>
          <tag :list="temp.fileSuffixName" :disabled="!formable" @tagChange="fileSuffixNameChange"/>
        </el-card>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>

        <FormItem label-width="0" prop="actionCheck">
          <FormItem label-width="30px">
            <el-checkbox v-model="doResponseCheck" :disabled="!formable" @change="allowAllPrinterChange(1)">{{ $t('pages.PrintSet_Msg2_1') }} </el-checkbox>
          </FormItem>
          <FormItem label-width="30px">
            <el-checkbox v-model="doNetResponseCheck" :disabled="!formable" @change="allowAllPrinterChange(2)">{{ $t('pages.PrintSet_Msg3_1') }}</el-checkbox>
          </FormItem>
        </FormItem>

        <label style="font-weight: 500;color: #409eff;font-size: small;margin-left: 30px">{{ $t('pages.PrintSet_Msg4') }}</label>
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            <i18n path="pages.PrintSet_Msg5">
              <br slot="br"/>
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
        <FormItem label-width="0" prop="action">
          <FormItem label-width="30px">
            <el-checkbox v-model="allowAllPrinter" :disabled="!formable || (!doResponseCheck && !doNetResponseCheck)" @change="allowAllPrinterChange(3)">{{ $t('pages.prohibitPrinting') }}</el-checkbox>
          </FormItem>
          <ResponseContent
            :status="dialogStatus"
            style="line-height: 15px !important;"
            :select-style="{ 'margin-top': '5px', 'margin-left': '13px' }"
            :show-select="true"
            :editable="formable && (!!doResponseCheck || !!doNetResponseCheck)"
            read-only
            :prop-check-rule="doResponse || doNetResponse"
            :show-check-rule="true"
            :prop-rule-id="propRuleId"
            @getRuleId="getRuleId"
            @ruleIsCheck="getRuleIsCheck"
            @validate="(val) => { responseValidate = val }"
          />
          <FormItem label-width="30px">
            <el-checkbox v-model="approveTypeCheck" :disabled="!formable || !allowAllPrinter">{{ $t('pages.PrintSet_Msg36') }}</el-checkbox>
          </FormItem>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.manageLibrary_backupRule') }}</el-divider>
        <FormItem prop="fileBackUpLimit" label-width="30px">
          <el-checkbox v-model="allowBackUpFile" :disabled="!formable" @change="backupchange" />
          <i18n path="pages.PrintSet_Msg33">
            <el-input slot="size" v-model="fileBackUpLimit" :disabled="!formable || !allowBackUpFile" style="width: 180px;" @input="handleInput">
              <el-select slot="append" v-model="unit" style="width: 80px;" :disabled="!formable || !allowBackUpFile" @change="handleChange">
                <el-option label="KB" :value="1"/>
                <el-option label="MB" :value="2"/>
                <el-option label="GB" :value="3"/>
              </el-select>
            </el-input>
          </i18n>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 480px;">{{ $t('pages.PrintSet_Msg34') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>

      </Form>

      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="()=>{dialogFormVisible = false;}">{{ $t('button.cancel') }}</el-button>
      </div>
    </tr-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.addPrint')"
      :visible.sync="dialogFormVisible2"
      :width="dislogWidth"
      @dragDialog="handleDrag"
    >
      <div class="tree-container" style="height: 450px;">
        <strategy-target-tree ref="printerTargetTree" :showed-tree="['terminal']" @data-change="printerTargetNodeChange" />
      </div>
      <div class="table-container-dialog">
        <grid-table
          ref="printerSelectList"
          :height="300"
          :show-pager="false"
          :col-model="colModel4"
          :multi-select="true"
          :row-datas="allPrinters"
          @row-click="clickTempTable"
        />
        <div style="display: flex;align-items: center;margin-top: 20px;">
          <span>{{ $t('pages.groupType') }}：</span>
          <el-select v-model="printerGroupName" filterable :placeholder="$t('text.select')" style="width: 200px;margin-left: 5px;">
            <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="item.label"/>
          </el-select>
          <el-button :title="$t('pages.addType')" :disabled="!formable" class="editBtn" style="margin-left: 5px;margin-top: 2px;" @click="handlePrinterGroupCreate"><svg-icon icon-class="add" /></el-button>
        </div>
        <span style="color: #3296FA;margin-top: 20px">{{ $t('pages.printer_Msg11') }}</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <link-button
          btn-type="primary"
          btn-style="float: left; height: 30px; padding: 0 10px;"
          :formable="formable"
          :menu-code="'A5P'"
          :link-url="'/system/baseData/printerLibrary'"
          :btn-text="$t('pages.maintainPrinter')"
          :before-click="beforeClick"
        />
        <el-button :loading="libraryLoading" type="primary" @click="createToLibrary()">{{ $t('pages.printer_Msg10') }}</el-button>
        <el-button type="primary" @click="createToStg()">{{ $t('pages.printer_Msg9') }}</el-button>
        <el-button @click="dialogFormVisible2 = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.addApp')"
      :visible.sync="dialogFormVisible3"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm3" :hide-required-asterisk="true" :rules="rules" :model="temp3" label-width="80px">
        <FormItem :label="$t('pages.programName')" prop="processName">
          <el-col :span="16">
            <el-input v-model="temp3.processName"></el-input>
          </el-col>
          <el-upload
            ref="upload"
            class="upload-demo"
            name="uploadFile"
            action="1111"
            :limit="1"
            :show-file-list="false"
            :before-upload="beforeUpload"
          >
            <el-button size="mini" type="primary" style="margin-top: 1px;">{{ $t('pages.selectFile') }}</el-button>
          </el-upload>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createData4()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible3 = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <app-select-dlg ref="appSelectDlg" @select="appSelectEnd"/>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importPrinterSetStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <import-table-dlg
      ref="printerImportTable"
      :exits-list-data="printerElgData"
      :not-selected-prompt-message="$t('pages.printer_text2')"
      :elg-title="$t('pages.importPrinterLibrary')"
      :group-root-name="$t('pages.printerLibrary')"
      :search-info-name="$t('pages.printerNameOrDriverName')"
      :confirm-button-name="$t('pages.addPrint')"
      :group-title="$t('pages.printerGroup')"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="getTreeNode"
      :count-by-group="countPrinterByGroupId"
      :get-group-by-name="getPrinterGroupByName"
      :delete-elg-able="true"
      :group-edit="false"
      :add-and-edit-able="false"
      :get-list-by-group-ids="listPrinterByGroupId"
      @submitEnd="getNeedAddIds"
      @elgCancelAfter="elgCancelAfter"
    />
    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.printerGroup')"
      :group-tree-data="treeSelectNode"
      :add-func="createPrinterGroup"
      :edit-valid-func="getPrinterGroupByName"
      @addEnd="createNode"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
  </div>
</template>

<script>
import { createStrategy, deleteStrategy, getStrategyList, updateStrategy } from '@/api/behaviorManage/hardware/printerSet'
import { listPrinterByGroupId, getTreeNode, countPrinterByGroupId, getPrinterGroupByName, getPrinterList, getByIds, getPrinterByName, createPrinterGroup, createOrUpdatePrinter } from '@/api/system/baseData/printerLibrary'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'

export default {
  name: 'PrintSet',
  components: { ResponseContent, ImportStg, FileSuffixLibImport, AppSelectDlg, ImportTableDlg, EditGroupDlg, BackupRuleContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 1,
      visible: false,
      visible2: false,
      defaultExeList: [
        { label: 'Windows', id: 0, children: [
          { label: 'wps.exe', id: 1 },
          { label: 'et.exe', id: 2 },
          { label: 'wpp.exe', id: 3 },
          { label: 'winword.exe', id: 4 },
          { label: 'excel.exe', id: 5 },
          { label: 'powerpnt.exe', id: 6 },
          { label: 'pptview.exe', id: 7 },
          { label: 'uedit32.exe', id: 8 },
          { label: 'notepad.exe', id: 9 }
        ]
        },
        {
          label: 'Mac', id: 10, children: [
            { label: 'TextEdit', id: 11 },
            { label: 'Preview', id: 12 },
            { label: 'Pages', id: 13 },
            { label: 'Numbers', id: 14 },
            { label: 'Keynote', id: 15 },
            { label: 'wpsoffice', id: 16 },
            { label: 'Microsoft Word', id: 17 },
            { label: 'Microsoft PowerPoint', id: 18 },
            { label: 'Microsoft Excel', id: 19 }
          ]
        },
        {
          label: 'Linux', id: 20, children: [
            { label: 'wps', id: 21 },
            { label: 'et', id: 22 },
            { label: 'wpp', id: 23 },
            { label: 'wpspdf', id: 24 },
            { label: 'wpsoffice', id: 25 },
            { label: 'gedit', id: 26 },
            { label: 'deepin-editor', id: 27 },
            { label: 'soffice.bin', id: 28 },
            { label: 'vim', id: 29 }
          ]
        }
      ],
      defaultSuffixList: [
        { label: this.$t('pages.builtFileSuffix'), id: 0, children: [
          { label: '.doc', id: 1 },
          { label: '.docx', id: 2 },
          { label: '.pdf', id: 3 },
          { label: '.ppt', id: 4 },
          { label: '.pptx', id: 5 },
          { label: '.txt', id: 6 },
          { label: '.xls', id: 7 },
          { label: '.xlsx', id: 8 },
          { label: '.rtf', id: 9 }
        ]
        }
      ],
      colModel: [
        { prop: 'name', label: 'stgName', fixedWidth: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'ctrlSet', label: 'stgMessage', width: '200', ellipsis: false, type: 'popover', originData: true, formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      colModel2: [
        { prop: 'printerName', label: 'printName', width: '1', sort: true },
        { prop: 'driverName', label: 'driverName', width: '1', sort: true }
      ],
      colModel3: [
        { prop: 'processName', label: 'programName', width: '1' }
      ],
      colModel4: [
        { prop: 'computer', label: 'source', width: '1', sort: true },
        { prop: 'printerName', label: 'printName', width: '1', sort: true },
        { prop: 'driverName', label: 'driverName', width: '1', sort: true }
      ],
      importColModel: [
        { prop: 'printName', label: 'printName', width: '150', sort: 'custom' },
        { prop: 'driverName', label: 'driverName', width: '150', sort: 'custom' },
        { prop: 'groupName', label: 'sourceGroup', width: '130', sort: 'custom' }
      ],
      selectPrinterList: [
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true, // 是否显示树
      deleteable: false,
      deleteable1: false,
      deleteable2: false,
      addBtnAble: false,
      temp: {
        id: undefined,
        name: '',
        remark: '',
        active: false,
        ctrlSet: 0,
        allowPrinter: 0,
        allowProcess: 0,
        allowNetPrinter: 0,
        allowSuffix: 0,
        fileSuffixName: [],
        printerList: [],
        processList: [],
        entityType: '',
        entityId: undefined,
        alarmSetupId: 0,
        ruleId: '',
        action: 0,
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined, // 备份过滤规则id
        allowBackUpFile: true,
        fileBackUpLimit: 10240,
        unit: 2,
        fileBackUpLimitDesc: '',
        approveType: 0 // 使用审批组件调起， 0使用 1不使用
      },
      limitType: 2,
      temp2: {
        computer: this.$t('pages.other'),
        ctrlCode: 1,
        printerName: '',
        driverName: ''
      },
      temp3: {
        processName: ''
      },
      dialogFormVisible: false,
      dialogFormVisible2: false,
      dialogFormVisible3: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.printSetStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.printSetStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ],
        printerName: [
          { required: true, message: this.$t('pages.PrintSet_Msg8'), trigger: 'blur' }
        ],
        driverName: [
          { required: true, message: this.$t('pages.PrintSet_Msg9'), trigger: 'blur' },
          { validator: this.driverNameValidator, trigger: 'blur' }
        ],
        actionCheck: [{ required: true, validator: this.actionCheckValidator, trigger: 'blur' }],
        action: [{ validator: this.actionValidator, trigger: 'blur' }],
        fileBackUpLimit: [
          { validator: this.fileBackUpLimitValidator, trigger: 'blur' }
        ]
      },
      termPrinters: [], // 终端的打印机
      inputPrinters: [],  // 用户输入的打印机
      termId: null,
      termName: '',
      propRuleId: undefined,
      doResponse: false,
      doNetResponse: false,
      disableAllowNetPrinter: false,
      disableAllowPrinter: false,
      disableDoResponse: false,
      disableDoNetResponse: false,
      enableActionMsgs: [],
      disableActionMsgs: [],
      ruleMsgs: [],
      treeSelectNode: [],
      importPrinterData: [],
      dislogWidth: undefined,
      initPrinterList: [],
      needIds: [],
      printAndDriveList: [],
      printerElgData: [],
      printerGroupName: undefined,
      allowAllPrinter: false,
      doNetResponseCheck: false,
      doResponseCheck: false,
      approveTypeCheck: false,
      allowBackUpFile: true,
      fileBackUpLimit: 10240,
      libraryLoading: false,
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined, // 备份过滤规则id
      unit: 2,
      unitOptions: { 1: 'KB', 2: 'MB', 3: 'GB' }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    },
    allPrinters() {
      const printers = [...this.termPrinters]
      return printers.map((item, index) => { item.id = index + 1; return item })
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.clearCheckSuffix()
      }
    },
    visible2(val) {
      if (val) {
        this.$refs.exeList.$refs.tree.setCheckedKeys([])
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.getLanguage()
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  methods: {
    getTreeNode,
    listPrinterByGroupId,
    createPrinterGroup,
    countPrinterByGroupId,
    getPrinterGroupByName,
    handlePrinterGroupCreate() {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    createNode(data) {
      getTreeNode().then(res => {
        this.treeSelectNode = res.data || []
        this.printerGroupName = data.name
      })
    },
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPrinterList(searchQuery)
    },
    getLanguage() {
      const currentLang = this.$store.getters.language
      if (currentLang != undefined || currentLang != null || currentLang != '') {
        if (currentLang == 'zh' || currentLang == 'tw') {
          this.dislogWidth = '800px'
        } else {
          this.dislogWidth = '900px'
        }
      }
    },
    async clickTempTable(row, column, event) {
      const obj = {
        printName: row.printerName,
        driverName: row.driverName
      }
      const res = await getPrinterByName(obj)
      let printerGroupId = ''
      if (res.data != undefined) {
        printerGroupId = res.data.groupId
      } else {
        this.printerGroupName = undefined
      }
      this.treeSelectNode.forEach(item => {
        if (item.dataId == printerGroupId) {
          this.printerGroupName = item.label
        }
      })
    },
    listTreeNode() {
      getTreeNode().then(res => {
        this.treeSelectNode = res.data || []
      })
    },
    beforeClick() {
      this.dialogFormVisible2 = false
    },
    async getNeedAddIds(needIds) {
      this.listTreeNode()
      const res = await getByIds({ ids: needIds.join(',') })
      this.initPrinterList.forEach(item => {
        let printName = '';
        if (item.printName) {
          printName = item.printName;
        }
        let driverName = '';
        if (item.driverName) {
          driverName = item.driverName;
        }
        if (!this.printAndDriveList.includes(printName + 'tipray-dlp-printset' + driverName)) {
          this.printAndDriveList.push(printName + 'tipray-dlp-printset' + driverName)
        }
      })
      const tempList = []
      res.data.forEach(item => {
        let printName = '';
        if (item.printName) {
          printName = item.printName;
        }
        let driverName = '';
        if (item.driverName) {
          driverName = item.driverName;
        }
        if (!this.printAndDriveList.includes(printName + 'tipray-dlp-printset' + driverName)) {
          tempList.push(printName + 'tipray-dlp-printset' + driverName)
        }
      })
      tempList.forEach(item => {
        this.printAndDriveList.push(item)
      })

      const tempPrintList = []

      this.printAndDriveList.forEach(item => {
        const splitObj = item.split('tipray-dlp-printset')
        const tempObject = {
          printerName: '',
          driverName: ''
        }
        if (splitObj.length > 1) {
          tempObject.printerName = splitObj[0]
          tempObject.driverName = splitObj[1]
        } else {
          tempObject.printerName = splitObj[0]
        }
        tempPrintList.push(tempObject)
      })
      this.temp.printerList = [...tempPrintList]
      this.$refs.printerList.execRowDataApi()
    },
    elgCancelAfter() {
    },
    printerTargetNodeChange: function(tabName, data) {
      if (data) {
        if (data.type == 1) {
          this.termId = data.dataId
          this.termName = data.label
          this.$socket.sendToUser(this.termId, '/getPrinter', this.termId, (respond, handle) => {
            handle.close()
            this.termPrinters.splice(0)
            // 判断终端id是否等于当前请求的终端id，防止用户连续点击不同终端导致数据加载出错
            if (respond.data && respond.data.termId == this.termId && respond.data.printList) {
              respond.data.printList.forEach(item => {
                const p = {
                  computer: this.termName,
                  ctrlCode: 1,
                  printerName: item.printName,
                  driverName: item.driverName
                }
                this.termPrinters.push(p)
              })
            }
          }, (handle) => {
            handle.close()
            this.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
          })
        }
      }
    },
    allowChange() {
      if (this.temp.allowPrinter && this.doNetResponse) {
        // 如果勾选禁止打印，并且网络打印只勾选响应
        if (!this.temp.allowNetPrinter) {
          this.temp.allowNetPrinter = 8
          this.disableAllowNetPrinter = true
        }
        if (!this.doResponse) {
          this.doResponse = true
          this.disableDoResponse = true
        }
      } else {
        if (this.temp.allowNetPrinter && this.disableAllowNetPrinter) {
          this.temp.allowNetPrinter = 0
          this.disableAllowNetPrinter = false
        }
        if (this.doResponse && this.disableDoResponse) {
          this.doResponse = false
          this.disableDoResponse = false
        }
        if (this.temp.allowNetPrinter && this.doResponse) {
          this.allowNetChange()
        }
      }
      this.changeActionMsgs()
    },
    allowNetChange() {
      if (this.temp.allowNetPrinter && this.doResponse) {
        // 如果勾选禁止打印，并且网络打印只勾选响应
        if (!this.temp.allowPrinter) {
          this.temp.allowPrinter = 1
          this.disableAllowPrinter = true
        }
        if (!this.doNetResponse) {
          this.doNetResponse = true
          this.disableDoNetResponse = true
        }
      } else {
        if (this.temp.allowPrinter && this.disableAllowPrinter) {
          this.temp.allowPrinter = 0
          this.disableAllowPrinter = false
        }
        if (this.doNetResponse && this.disableDoNetResponse) {
          this.doNetResponse = false
          this.disableDoNetResponse = false
        }
        if (this.temp.allowPrinter && this.doNetResponse) {
          this.allowChange()
        }
      }
      this.changeActionMsgs()
    },
    changeActionMsgs() {
      this.enableActionMsgs.splice(0)
      this.disableActionMsgs.splice(0)
      this.ruleMsgs.splice(0)
      const ctrlCode = this.formatCtrlSet()
      let action = 0
      if (this.temp.allowPrinter || this.temp.allowNetPrinter) { // 禁止打印和禁止网络打印机打印。都需要触发禁止的阻断
        action = 1
      }
      if (ctrlCode === 0) {
        this.enableActionMsgs.push(this.$t('pages.allowAllInfo', { info: this.$t('table.printer') }))
      } else if (ctrlCode === 1) {
        this.disableActionMsgs.push(this.$t('pages.prohibitedAllInfo', { info: this.$t('pages.printerIncludeNetworkPrinter') }))
      } else {
        const ctrlCodes = this.numToArr(ctrlCode)
        if (action) {
          if (ctrlCodes.indexOf(2) > -1) {
            // this.enableActionMsgs.push('允许使用检测规则配置的打印机进行打印，禁止使用其他打印机' + (this.doResponse ? '，其他打印机打印时触发响应规则' : ''))
            if (this.doResponse) {
              this.enableActionMsgs.push(this.$t('pages.printerAllowConfigPrinterMsg'))
            } else {
              this.enableActionMsgs.push(this.$t('pages.printerAllowConfigPrinterMsg1'))
            }
          }
          if (ctrlCodes.indexOf(4) > -1) {
            // this.enableActionMsgs.push('允许使用检测规则配置的程序进行打印，禁止使用其他程序打印' + (this.doResponse ? '，其他程序打印时触发响应规则' : ''))
            if (this.doResponse) {
              this.enableActionMsgs.push(this.$t('pages.printerAllowConfigProgramMsg'))
            } else {
              this.enableActionMsgs.push(this.$t('pages.printerAllowConfigProgramMsg1'))
            }
          }
          if (ctrlCodes.indexOf(16) > -1) {
            // this.enableActionMsgs.push('允许打印检测规则配置的后缀文档，禁止打印其他文档' + (this.doResponse ? '，打印其他文档时触发响应规则' : ''))
            if (this.doResponse) {
              this.enableActionMsgs.push(this.$t('pages.printerAllowConfigSuffixDocumentMsg'))
            } else {
              this.enableActionMsgs.push(this.$t('pages.printerAllowConfigSuffixDocumentMsg1'))
            }
          }
          if (ctrlCodes.indexOf(8) > -1) {
            action ? this.disableActionMsgs.push(this.$t('pages.prohibitedAllInfo', { info: this.$t('pages.approvalLog_Msg32') })) : this.enableActionMsgs.push(this.$t('pages.allowAllInfo', { info: this.$t('pages.approvalLog_Msg32') }))
          }
          if (this.doNetResponse) {
            this.ruleMsgs.push(this.$t('pages.netWorkPrinterPrintTriggerResponseRrule'))
          }
        } else {
          this.enableActionMsgs.push(this.$t('pages.allowAllInfo', { info: this.$t('table.printer') }))
          if (ctrlCodes.indexOf(2) > -1) {
            this.ruleMsgs.push(this.$t('pages.notDetectionRulePrinterMsg'))
          }
          if (ctrlCodes.indexOf(4) > -1) {
            this.ruleMsgs.push(this.$t('pages.notDetectionRuleProgramMsg'))
          }
          if (ctrlCodes.indexOf(16) > -1) {
            this.ruleMsgs.push(this.$t('pages.notDetectionRuleSuffixDocumentMsg'))
          }
          if (ctrlCodes.indexOf(8) > -1) {
            this.ruleMsgs.push(this.$t('pages.useNetWorkPrinterPrintTriggerResponseRrule'))
          }
        }
        return
      }
      if (this.doResponse) {
        this.ruleMsgs.push(this.$t('pages.anyPrinterPrintTriggerResponseRrule'))
      }
    },
    handlePrinterImport() {
      this.$nextTick(() => {
        this.$refs['printerImportTable'].show()
      })
    },
    handleCheckedSuffix() {
      const checkNodes = this.$refs.suffixList.$refs.tree.getCheckedNodes()
      checkNodes.forEach(item => {
        if (item.id != 0 && this.temp.fileSuffixName.indexOf(item.label) == -1) {
          this.temp.fileSuffixName.push(item.label)
        }
      })
      this.visible = false
    },
    handleCheckedExe() {
      const checkNodes = this.$refs.exeList.$refs.tree.getCheckedNodes()
      checkNodes.forEach(item => {
        if (item.id != 0 && item.id != 10 && item.id != 20 && this.temp.processList.indexOf(item.label) == -1) {
          this.temp.processList.push(item.label)
        }
      })
      this.visible2 = false
    },
    clearCheckSuffix() {
      this.$refs.suffixList.$refs.tree.setCheckedKeys([])
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    loadProcessList: function(option) {
      return new Promise((resolve, reject) => {
        resolve({
          code: 20000,
          data: {
            total: this.temp.processList.length,
            items: this.temp.processList
          }
        })
      })
    },
    loadPrinterList: function(option) {
      return new Promise((resolve, reject) => {
        resolve({
          code: 20000,
          data: {
            total: this.temp.printerList.length,
            items: this.temp.printerList
          }
        })
      })
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    selectionChangeEnd1: function(rowDatas) {
      this.deleteable1 = rowDatas && rowDatas.length > 0
    },
    selectionChangeEnd2: function(rowDatas) {
      this.deleteable2 = rowDatas && rowDatas.length > 0
    },
    urlTreeNodeCheckChange: function(keys, datas) {
      keys.forEach(function(id, i) {
        if (id.indexOf('G') > -1) {
          keys.splice(i, 1)
        }
      })
      this.temp.urlIds = this.temp.urlIds = JSON.stringify(keys)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.doResponse = false
      this.doNetResponse = false
      this.disableAllowNetPrinter = false
      this.disableAllowPrinter = false
      this.disableDoResponse = false
      this.disableDoNetResponse = false
      this.propRuleId = undefined
      this.allowBackUpFile = true
      this.fileBackUpLimit = 10240
      this.unit = 2
      this.temp = {
        id: undefined,
        name: '',
        remark: '',
        active: false,
        ctrlSet: 0,
        allowPrinter: 0,
        isAllPrinter: 0,
        allowProcess: 0,
        allowNetPrinter: 0,
        allowSuffix: 0,
        fileSuffixName: [],
        printerList: [],
        processList: [],
        entityType: '',
        entityId: undefined,
        alarmSetupId: 0,
        ruleId: '',
        action: 0,
        allowBackUpFile: true,
        fileBackUpLimit: 10240,
        unit: 2,
        approveType: 0
      }
    },
    resetTemp2(type) {
      this.temp2 = {
        ctrlCode: 1,
        computer: this.$t('pages.other'),
        printerName: '',
        driverName: ''
      }
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    clearTable(type) {
      this.temp.printerList.splice(0, this.temp.printerList.length)
      this.printAndDriveList.splice(0, this.printAndDriveList.length)
      this.initPrinterList.splice(0, this.initPrinterList.length)
    },
    beforeUpload(file) {
      this.temp3.processName = file.name
      return false // 屏蔽了action的默认上传
    },
    handleDelete2() {
      this.$refs['printerList'].getSelectedDatas().forEach(item => {
        const i = this.temp.printerList.indexOf(item)
        this.temp.printerList.splice(i, 1)
        this.printAndDriveList.splice(this.printAndDriveList.indexOf(item.printerName + 'tipray-dlp-printset' + item.driverName), 1)
        this.initPrinterList.forEach((res, index) => {
          if (res.printName == item.printerName && res.driverName == item.driverName) {
            this.initPrinterList.splice(index, 1)
          }
        })
      })
    },
    handleDelete3() {
      this.$refs['processList'].getSelectedDatas().forEach(item => {
        const i = this.temp.processList.indexOf(item)
        this.temp.processList.splice(i, 1)
      })
    },
    handleCreate() {
      this.resetTemp()
      this.approveTypeCheck = false
      this.initPrinterList = []
      this.printAndDriveList = []
      this.allowAllPrinter = false
      this.doNetResponseCheck = false
      this.doResponseCheck = false
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.printerList.execRowDataApi()
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleCreate2() {
      this.resetTemp2()
      this.termPrinters = []
      this.printerGroupName = undefined
      const res = await getTreeNode()
      this.treeSelectNode = res.data || []
      this.dialogFormVisible2 = true
      this.$refs.printerTargetTree && this.$refs.printerTargetTree.clearFilter()
      this.$nextTick(() => {
        this.$refs.printerSelectList.clearSelection()
        // this.$refs['dataForm2'].clearValidate()
      })
    },
    handleCreate3() {
      this.temp3 = {
        processName: ''
      }
      this.dialogFormVisible3 = true
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      const hasActionProp = row.hasOwnProperty('action')      // this.temp = row
      // 把对象转json再转为对象，复制出一个新的对象，防止页面修改但不点击保存时影响到了原来的数据
      this.temp = Object.assign({}, this.defaultTemp, JSON.parse(JSON.stringify(row)))
      this.printAndDriveList = []
      this.initPrinterList = []
      if (this.temp.printerList.length > 0) {
        const tempList = []
        this.temp.printerList.forEach(item => {
          const tempObj = {
            printName: item.printerName,
            driverName: item.driverName
          }
          tempList.push(tempObj)
        })
        this.initPrinterList = [...tempList]
      }
      if (!hasActionProp) { // 兼容历史数据
        this.temp.allowPrinter = 0
        this.temp.allowNetPrinter = 0
      }
      this.dialogFormVisible = true
      // 新版使用doNetPrinterRespond和doPrinterRespond
      this.doResponse = !!row.doPrinterRespond || (!!row.ruleId && (row.ctrlSet !== 8 || !!row.allowPrinter))
      this.doNetResponse = !!row.doNetPrinterRespond || (!!row.ruleId && ((row.ctrlSet | 8) === row.ctrlSet || !!row.allowNetPrinter))

      this.propRuleId = row.ruleId

      //  设置doResponseCheck、 doNetResponseCheck
      this.doResponseCheck = this.doResponse || !!this.temp.allowPrinter
      this.doNetResponseCheck = this.doNetResponse || !!this.temp.allowNetPrinter
      //  设置allowAllPrinter
      this.allowAllPrinter = !!this.temp.allowPrinter || !!this.temp.allowNetPrinter
      this.approveTypeCheck = this.allowAllPrinter ? (this.temp.approveType === undefined ? false : this.temp.approveType === 1) : false
      this.allowBackUpFile = row.allowBackUpFile == undefined ? true : row.allowBackUpFile
      this.unit = row.unit == undefined ? 2 : row.unit
      this.fileBackUpLimit = row.fileBackUpLimit == undefined ? 10240 : row.fileBackUpLimit
      if (row.unit != undefined || row.fileBackUpLimit != undefined) {
        if (this.unit == 1) {
          this.fileBackUpLimit = this.fileBackUpLimit
        } else if (this.unit == 2) {
          this.fileBackUpLimit = this.fileBackUpLimit / 1024
        } else if (this.unit == 3) {
          this.fileBackUpLimit = this.fileBackUpLimit / 1024 / 1024
        }
      }
      if (!row.allowBackUpFile) {
        this.fileBackUpLimit = 10240
        this.unit = 2
      }
      this.$nextTick(() => {
        this.$refs.printerList && this.$refs.printerList.execRowDataApi()
        this.$refs['dataForm'].clearValidate()
        this.dialogStatus = 'update'
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleAppImport() {
      this.$refs.appSelectDlg.show()
    },
    appSelectEnd(datas) {
      if (!datas || datas.length === 0) return
      const list = this.temp.processList
      datas.forEach(item => {
        if (list.indexOf(item.processName) < 0) {
          list.unshift(item.processName)
        }
      })
    },
    formatCtrlSet() {
      let ctrlSet = 0
      ctrlSet = ctrlSet | this.temp.allowNetPrinter // 禁止网络打印机
      const isEmptyCheckInfo = this.temp.printerList.length === 0 && this.temp.processList.length === 0 && this.temp.fileSuffixName.length === 0
      if (this.temp.allowPrinter && isEmptyCheckInfo) { // 禁止所有打印机
        ctrlSet = 1
      } else {
        if (this.doResponse || this.temp.allowPrinter) {
          if (this.temp.printerList.length > 0) { // 打印机列表不为空，说明允许如下打印机
            ctrlSet = ctrlSet | 2
          }
          if (this.temp.processList.length > 0) { // 进程列表不为空，说明允许如下进程
            ctrlSet = ctrlSet | 4
          }
          if (this.temp.fileSuffixName.length > 0) { // 后缀名称不为空，说明允许指定文档类型
            ctrlSet = ctrlSet | 16
          }
        }
        if ((!this.doResponse || ctrlSet > 0) && this.doNetResponse && !this.temp.allowNetPrinter) { // 如果网络打印机配置了响应规则，但是没有禁止
          ctrlSet = ctrlSet | 8
        }
      }
      // 按终端的意思，不需要强制设置成1
      // if (ctrlSet === 0) { // 如果没有勾选禁止打印，也没有禁止网络打印机，也没有配置任何打印机、进程、后缀
      //   ctrlSet = 1
      // }
      return ctrlSet
    },
    formatData() {
      this.temp.ctrlSet = this.formatCtrlSet()
      this.temp.action = 0
      if (this.temp.allowPrinter || this.temp.allowNetPrinter) { // 禁止打印和禁止网络打印机打印。都需要触发禁止的阻断
        this.temp.action = 1
      }
      if (this.temp.ctrlSet === 1) {
        this.temp.allowProcess = 0
        this.temp.allowSuffix = 0
        this.temp.fileSuffixName = []
        this.temp.printerList = []
        this.temp.processList = []
      }
      if (!this.doResponse && !this.doNetResponse) {
        this.temp.ruleId = null
      } else {
        this.temp.ruleId = this.propRuleId
      }
      this.temp.allowBackUpFile = this.allowBackUpFile
      this.temp.doNetPrinterRespond = this.doNetResponse
      this.temp.doPrinterRespond = this.doResponse
      this.temp.approveType = this.approveTypeCheck ? 1 : 0
      this.temp.unit = this.unit
      if (this.unit == 1) {
        this.temp.fileBackUpLimit = this.fileBackUpLimit
      } else if (this.unit == 2) {
        this.temp.fileBackUpLimit = this.fileBackUpLimit * 1024
      } else if (this.unit == 3) {
        this.temp.fileBackUpLimit = this.fileBackUpLimit * 1024 * 1024
      }
      this.temp.fileBackUpLimitDesc = this.fileBackUpLimit + this.unitOptions[this.unit]
      if (this.allowBackUpFile == 0) {
        this.temp.fileBackUpLimit = 0
      }
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.responseValidate) {
          this.formatData()
          this.temp.printerList.forEach((item, index) => {
            item.id = index + 1
          })
          const tempData = Object.assign({}, this.temp)
          createStrategy(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    async createToLibrary() {
      this.libraryLoading = true
      const datas = this.$refs.printerSelectList.getSelectedDatas()
      if (datas.length === 0) {
        this.$message({
          message: this.$t('pages.PrintSet_Msg10'),
          type: 'error',
          duration: 2000
        })
        this.libraryLoading = false
        return
      }
      if (this.printerGroupName == undefined) {
        this.$message({
          message: this.$t('pages.printer_Msg5'),
          type: 'error',
          duration: 2000
        })
        this.libraryLoading = false
        return
      }
      let groupId = '';
      this.treeSelectNode.forEach(item => {
        if (item.label == this.printerGroupName) {
          groupId = item.dataId
        }
      })
      for (let i = 0; i < datas.length; i++) {
        const printerObj = {
          printName: datas[i].printerName,
          driverName: datas[i].driverName,
          groupId: groupId
        };
        try {
          await createOrUpdatePrinter(printerObj)
        } catch (error) {
          this.libraryLoading = false
          return
        }
      }
      this.libraryLoading = false
      this.$message({
        message: this.$t('text.insertSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    createToStg() {
      const datas = this.$refs.printerSelectList.getSelectedDatas()
      if (datas.length === 0) {
        this.$message({
          message: this.$t('pages.PrintSet_Msg10'),
          type: 'error',
          duration: 2000
        })
        return
      }
      // 验证打印机列表是否存在选中的打印机，根据打印机名称、驱动名称判断
      const existPrinter = []
      datas.forEach(item => {
        const index = this.temp.printerList.findIndex(printer => {
          if (printer.printerName === item.printerName && printer.driverName === item.driverName) {
            return true
          }
        })
        if (index !== -1) {
          existPrinter.push(item)
        }
      })
      datas.forEach(item => {
        // 判断在策略中是否已经存在
        let existVisible = false;
        existPrinter.forEach(result => {
          if (result.printerName == item.printerName && result.driverName == item.driverName) {
            existVisible = true
          }
        })
        if (!existVisible) {
          // 在策略中不存在，添加前在判断一次，避免用户勾选了两个相同的数据
          const index = this.temp.printerList.findIndex(printer => {
            if (printer.printerName === item.printerName && printer.driverName === item.driverName) {
              return true
            }
          })
          if (index == -1) {
            this.temp.printerList.push(item)
          }
        }
      })
      const tempInitPrinterList = [];
      this.temp.printerList.forEach(item => {
        const tempObj = {
          printName: item.printerName,
          driverName: item.driverName
        };
        tempInitPrinterList.push(tempObj)
      })
      this.initPrinterList = [...tempInitPrinterList]
      this.dialogFormVisible2 = false
      this.$refs.printerList.execRowDataApi()
    },
    createData4() {
      this.$refs['dataForm3'].validate((valid) => {
        if (valid) {
          const index = this.temp.processList.findIndex(item => {
            if (item === this.temp3.processName) {
              return true
            }
          })
          if (index !== -1) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.PrintSet_Msg13', { processName: this.temp3.processName }),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.temp.processList.push(this.temp3.processName)
          this.dialogFormVisible3 = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.responseValidate) {
          this.formatData()
          this.temp.printerList.forEach((item, index) => {
            item.id = index + 1
          })
          const tempData = Object.assign({}, this.temp)
          updateStrategy(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row) {
      /*  "0允许所有打印机
      1禁止所有打印机
      2允许如下打印机打印
      4 允许如下进程打印
      8 禁止所有网络打印机打印
      16只允许指定后缀文档"
        */
      let msg = ''
      if (row.ctrlSet === 1) {
        msg += this.$t('pages.PrintSet_Msg14')
        if (row.action && row.ruleId) {
          msg += this.$t('pages.PrintSet_Msg26')
        } else {
          msg += row.action ? this.$t('pages.PrintSet_Msg27') : ''
          msg += row.ruleId ? this.$t('pages.PrintSet_Msg28') : ''
        }
        if (row.approveType !== undefined && row.approveType === 1) {
          msg += this.$t('pages.PrintSet_Msg36') + '；'
        }
      } else {
        const isEmptyCheckInfo = row.printerList.length === 0 && row.processList.length === 0 && row.fileSuffixName.length === 0
        if (!isEmptyCheckInfo) {
          if (row.processList != null && row.processList.length > 0) {
            msg += this.$t('pages.PrintSet_Msg18', { info: row.processList })
          } else {
            msg += this.$t('pages.PrintSet_Msg19')
          }
          if (row.printerList != null && row.printerList.length > 0) {
            const list = row.printerList.map(item => {
              return item.printerName
            })
            msg += this.$t('pages.PrintSet_Msg20', { info: list })
          } else {
            msg += this.$t('pages.PrintSet_Msg21')
          }
          if (row.fileSuffixName != null && row.fileSuffixName.length > 0) {
            msg += this.$t('pages.PrintSet_Msg22', { info: row.fileSuffixName })
          } else {
            msg += this.$t('pages.PrintSet_Msg23')
          }
          if (msg.length > 0) {
            msg = this.$t('pages.allowInfo', { info: msg })
          }
          msg += this.$t('pages.PrintSet_Msg25')
          if (row.action && row.ruleId) {
            msg += this.$t('pages.PrintSet_Msg26')
          } else {
            msg += row.action ? this.$t('pages.PrintSet_Msg27') : ''
            msg += row.ruleId ? this.$t('pages.PrintSet_Msg28') : ''
          }
        } else {
          if (row.doPrinterRespond && !row.allowPrinter) {
            msg += this.$t('pages.PrintSet_Msg31')
          } else {
            if (row.doNetPrinterRespond && !row.allowNetPrinter) {
              msg += this.$t('pages.PrintSet_Msg32')
            }
          }
        }
        if (row.allowNetPrinter) {
          msg += this.$t('pages.PrintSet_Msg29')
          msg += row.ruleId ? this.$t('pages.PrintSet_Msg15') : '；'
        }
        if (row.approveType !== undefined && row.approveType === 1) {
          msg += this.$t('pages.PrintSet_Msg36') + '；'
        }
      }
      if (row.allowBackUpFile && row.fileBackUpLimit && row.unit) {
        const unit = this.unitOptions[row.unit]
        const BackupSize = row.unit == 2 ? row.fileBackUpLimit / 1024 : row.unit == 3 ? row.fileBackUpLimit / 1024 / 1024 : row.fileBackUpLimit
        msg += this.$t('pages.PrintSet_Msg35', { BackupSize, unit }) + ';'
      } else if (row.allowBackUpFile == undefined && row.fileBackUpLimit == undefined && row.unit == undefined) {
        msg += this.$t('pages.PrintSet_Msg35', { BackupSize: 10240, unit: 'MB' }) + ';'
      }
      return msg
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    driverNameValidator(rule, value, callback) {
      const index = this.selectPrinterList.findIndex(item => {
        if (this.temp2.driverName === item.driverName) {
          return true
        }
      });
      if (index !== -1) {
        callback(new Error(this.$t('pages.PrintSet_Msg30')))
      } else {
        callback()
      }
    },
    actionCheckValidator(rule, value, callback) {
      if (!this.doResponseCheck && !this.doNetResponseCheck && !this.allowBackUpFile) {
        callback(new Error(this.$t('pages.install_Msg52')))
      } else {
        callback()
      }
    },
    actionValidator(rule, value, callback) {
      if ((this.doResponseCheck || this.doNetResponseCheck) && (!this.allowAllPrinter && !this.doResponse && !this.doNetResponse)) {
        callback(new Error(this.$t('pages.install_Msg51')))
      } else {
        callback()
      }
    },
    backupchange(data) {
      if (data) {
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    },
    handleInput() {
      this.fileBackUpLimit = this.fileBackUpLimit.replace(/^0|[^0-9]/g, '')
      if (this.unit == 1 && this.fileBackUpLimit > 10485760) {
        this.fileBackUpLimit = 10485760
      } else if (this.unit == 2 && this.fileBackUpLimit > 10240) {
        this.fileBackUpLimit = 10240
      } else if (this.unit == 3 && this.fileBackUpLimit > 10) {
        this.fileBackUpLimit = 10
      }
    },
    handleChange(data) {
      if (data == 1 && this.fileBackUpLimit > 10485760) {
        this.fileBackUpLimit = 10485760
      } else if (data == 2 && this.fileBackUpLimit > 10240) {
        this.fileBackUpLimit = 10240
      } else if (data == 3 && this.fileBackUpLimit > 10) {
        this.fileBackUpLimit = 10
      }
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (!this.doResponseCheck && !this.doNetResponseCheck && !this.allowBackUpFile) {
        callback(new Error(this.$t('pages.install_Msg52')))
      }
      if (this.allowBackUpFile) {
        if (!this.fileBackUpLimit) {
          return callback(new Error(this.$t('components.required')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    importSuccess() {
      this.handleFilter()
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      this.temp.fileSuffixName = [...new Set(this.temp.fileSuffixName.concat(new_suffix))]
    },
    fileSuffixNameChange(list) {
      const newMap = new Map()
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
      })
      this.temp.fileSuffixName = Object.keys(newMap) || [];
    },
    getRuleIsCheck(data) {
      this.doResponse = data === 1 ? this.doResponseCheck : false
      this.doNetResponse = data === 1 ? this.doNetResponseCheck : false
      this.validReposeRule()
    },
    allowAllPrinterChange(type) {
      //  遵循：选中禁止打印/allowAllPrinter=true后，若doResponseCheck=true时，temp.allowPrinter=1; 若doNetResponseCheck=true时，temp.allowNetPrinter=8
      //                                         若doResponseCheck=false时，temp.allowPrinter=0; 若doNetResponseCheck=false时，temp.allowNetPrinter=0
      //       选中违规响应规则后，若doResponseCheck=true时，doResponse=true; 若doNetResponseCheck=true时，doNetResponse=true
      //                        若doResponseCheck=false时，doResponse=false; 若doNetResponseCheck=false时，doNetResponse=false
      this.temp.allowPrinter = this.doResponseCheck ? this.allowAllPrinter ? 1 : 0 : 0
      this.temp.allowNetPrinter = this.doNetResponseCheck ? this.allowAllPrinter ? 8 : 0 : 0
      if (type === 1 && (this.doNetResponse || this.doResponse)) {
        this.doResponse = this.doResponseCheck
      }
      if (type === 2 && (this.doResponse || this.doNetResponse)) {
        this.doNetResponse = this.doNetResponseCheck
      }
      if (!this.doResponseCheck && !this.doNetResponseCheck) {
        this.allowAllPrinter = false
        this.doResponse = false
        this.doNetResponse = false
        this.propRuleId = null
      }
      if (!this.allowAllPrinter) {
        this.approveTypeCheck = false
      }
      if (type === 1 || type === 2) {
        this.validExecuteRule();
      }
      if (type === 3 || type === 4) {
        this.validReposeRule()
      }
    },
    validExecuteRule() {
      this.$refs.dataForm.validateField('actionCheck');
    },
    validReposeRule() {
      this.$refs.dataForm.validateField('action')
    }
  }
}
</script>
