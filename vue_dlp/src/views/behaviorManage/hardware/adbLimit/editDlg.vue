<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      type="drawer"
      time-able
      :title="$t('pages.adbLimitStg')"
      :stg-code="218"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('pages.adbLimitStgConfig')" name="first">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
              <div slot="header">
                <span>{{ $t('pages.adb_limit_Msg1') }}</span>
                <el-button v-if="formable" size="small" @click="handleFileSuffixImport()">
                  {{ $t('button.FileSuffixLibImport') }}
                </el-button>
                <el-button v-if="formable" size="small" @click="handleClear">
                  {{ $t('button.clear') }}
                </el-button>
              </div>
              <FormItem prop="suffixs" label-width="0px">
                <!-- 数据库suffix长度为60，但是文件后缀会自动加.，所以限制输入59个 -->
                <tag v-model="temp.suffixs" :list="temp.suffixs" :disabled="!formable" input-length="59" @tagChange="suffixsChange"/>
              </FormItem>
            </el-card>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label="" label-width="0">
              <el-row style="margin-left: 31px;">
                <el-col :span="24">
                  <el-radio-group v-model="temp.limitType" :disabled="!formable" style="width: 100%;" @change="limitTypeChange">
                    <el-radio :label="1" class="ellipsis" style="width: 98%;"><span :title="$t('pages.adb_limit_Msg2')">{{ $t('pages.adb_limit_Msg2_1') }}</span></el-radio>
                    <el-radio :label="2" class="ellipsis" style="width: 98%; margin-top:4px;"><span :title="$t('pages.adb_limit_Msg3')">{{ $t('pages.adb_limit_Msg3_1') }}</span></el-radio>
                  </el-radio-group>
                </el-col>
                <el-col :span="24">
                  <label style="font-weight: 500;color: #409eff;font-size: small;">
                    {{ $t('pages.executeRuleTip', { info: this.$t('pages.identifierRule_regular'), info1: this.$t('table.suffixes') }) }}
                  </label>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      <i18n path="pages.withinDetectionRuleTip">
                        <span slot="info">{{ $t('pages.allowADBOutgoing') }}</span>
                      </i18n>
                      <br/>
                      <i18n path="pages.outsideDetectionRuleTip1">
                        <span slot="info">{{ $t('pages.forbidADBOutgoing') }}</span>
                      </i18n>
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-col>
              </el-row>
            </FormItem>
            <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
            <FormItem label-width="10px" prop="action" style="margin-bottom: 0">
              <el-checkbox-group v-model="temp.executeCode" :disabled="!formable">
                <el-checkbox :label="temp.limitType === 1 ? 1 : 3" class="prohibit">{{ $t('pages.adb_limit_Msg4') }}</el-checkbox>
              </el-checkbox-group>
              <ResponseContent
                :select-style="{ 'margin-top': 0 }"
                style="line-height: 15px !important;"
                :show-select="true"
                :editable="formable"
                read-only
                :prop-check-rule="isDoResponse(temp)"
                :show-check-rule="true"
                :prop-rule-id="temp.ruleId"
                @getRuleId="getRuleId"
                @ruleIsCheck="ruleIsCheck"
                @validate="(val) => { responseValidate = val }"
              />
            </FormItem>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.adbLimitLogConfig')" name="second">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <LocalZoomIn parent-tag="el-dialog">
              <data-editor
                :formable="formable"
                :popover-width="400"
                :append-to-body="true"
                :updateable="recordEditable"
                :deletable="recordDeleteable"
                :add-func="createRecordRule"
                :update-func="updateRecordRule"
                :delete-func="deleteRecordRule"
                :cancel-func="cancelRecordRule"
                :before-update="beforeupdateRecordRule"
              >
                <Form ref="dataForm2" :hide-required-asterisk="true" :model="tempW" :rules="tempWRules" label-width="80px" style="width: 100%; margin-top: 10px;">
                  <FormItem :label="$t('pages.manufacturer1')" prop="devManufacturer">
                    <el-input v-model="tempW.devManufacturer" maxlength="100"></el-input>
                  </FormItem>
                  <FormItem :label="$t('pages.serialNum')" prop="devNum">
                    <el-input v-model="tempW.devNum" maxlength="100"></el-input>
                  </FormItem>
                  <FormItem :label="$t('table.limitType')">
                    <el-select v-model="tempW.limitRuleType" @change="(val) => { tempW.matchType = 0 }">
                      <el-option :value="0" :label="$t('pages.serialNum')"/>
                      <el-option :value="1" :label="$t('pages.manufacturer1')"/>
                    </el-select>
                  </FormItem>
                  <FormItem v-if="tempW.limitRuleType == 1" :label="$t('table.matchType')">
                    <el-select v-model="tempW.matchType">
                      <el-option :value="0" :label="$t('pages.matchTypeOptions1')"/>
                      <el-option :value="1" :label="$t('pages.matchTypeOptions2')"/>
                    </el-select>
                  </FormItem>
                </Form>
              </data-editor>
              <grid-table
                ref="adbTableList"
                :show-pager="false"
                auto-height
                :selectable="selectable"
                :col-model="checkedColModel"
                :row-datas="temp.recordAdbList"
                @selectionChangeEnd="checkSelectionChangeEnd"
              />
            </LocalZoomIn>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label="" label-width="0">
              <el-row style="margin-left: 31px;">
                <el-col :span="24">
                  <el-radio-group v-model="temp.recordLimitType" :disabled="!formable" style="width: 100%;">
                    <el-radio :label="1" class="ellipsis" style="width: 98%;">{{ $t('pages.adb_limit_Msg12') }}</el-radio>
                    <el-radio :label="0" class="ellipsis" style="width: 98%; margin-top:4px;">{{ $t('pages.adb_limit_Msg13') }}</el-radio>
                  </el-radio-group>
                </el-col>
                <el-col :span="24">
                  <label style="font-weight: 500;color: #409eff;font-size: small;">
                    {{ $t('pages.executeRuleTip', { info: this.$t('pages.identifierRule_regular'), info1: this.$t('pages.device') }) }}
                  </label>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      <i18n path="pages.withinDetectionRuleTip">
                        <span slot="info">{{ $t('pages.forbidRecordADBOutgoing') }}</span>
                      </i18n>
                      <br/>
                      <i18n path="pages.outsideDetectionRuleTip">
                        <span slot="info">{{ $t('pages.allowRecordADBOutgoing') }}</span>
                      </i18n>
                    </div>
                    <i class="el-icon-info"/>
                  </el-tooltip>
                </el-col>
              </el-row>
            </FormItem>
            <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
            <FormItem style="margin-left: -87px" prop="action">
              <el-checkbox v-model="temp.record" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable" @change="recordChange">{{ $t('pages.adbRecord') }}</el-checkbox>
            </FormItem>
            <FormItem label-width="30px" prop="fileBackupLimitSize">
              <el-checkbox v-model="temp.isBackup" :true-label="1" :false-label="0" :disabled="!formable || !temp.record" @change="backupchange"></el-checkbox>
              <i18n path="pages.blueTooth_Msg1">
                <el-input-number slot="size" v-model="temp.fileBackupLimitSize" :disabled="!formable || temp.isBackup==0" :controls="false" step-strictly :min="1" :max="10240" size="mini" style="width: 100px;"/>
                <br slot="br"/>
              </i18n>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content" style="width: 480px;">{{ $t('pages.adb_limit_Msg9') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-button style="margin-left: 10px" :disabled="!formable" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
            </FormItem>
          </el-tab-pane>
        </el-tabs>
      </template>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
  </div>
</template>
<script>
import {
  getStrategyByName, createStrategy, updateStrategy, getTreeNode
} from '@/api/behaviorManage/hardware/adbLimit'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'

export default {
  name: 'AdbLimitDlg',
  components: { ResponseContent, FileSuffixLibImport, BackupRuleContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      activeName: 'first',
      colModel: [
        { prop: 'usbName', label: 'volumeName', width: '30' },
        { prop: 'groupName', label: 'groupId', width: '20', formatter: this.groupNameFormatter },
        { prop: 'usbCode', label: 'pnpDeviceId', width: '30' },
        { prop: 'manufacturer', label: 'model', width: '30' },
        { prop: 'usbType', label: 'devType', width: '30', formatter: this.usbTypeOptionFormatter },
        { prop: 'usbSize', label: 'usbSize', width: '30' }
      ],
      checkedColModel: [
        { prop: 'devManufacturer', label: 'devManufacturer', width: '150', sort: true },
        { prop: 'devNum', label: 'devNum', width: '150', sort: true },
        { prop: 'limitRuleType', label: 'limitType', width: '150', sort: true, formatter: this.limitRuleTypeFormatter }
      ],
      temp: {},
      defaultTemp: {
        id: null,
        name: '',
        active: false,
        remark: '',
        suffixs: [],
        entityType: '',
        entityId: null,
        ruleId: null,
        limitType: 1,
        action: 0,
        record: 0,
        isAlarm: 0,
        fileBackupLimitSize: 20,
        isBackup: 0, // 是否备份
        executeCode: [],
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined, // 备份过滤规则id
        recordAdbList: [],
        recordLimitType: 1
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.stgNameRequired'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileBackupLimitSize: [{ validator: this.fileBackUpLimitValidator, trigger: 'change' }]
      },
      tempWRules: {
        devNum: [{ validator: this.devNumValidator, trigger: 'blur' }],
        devManufacturer: [{ validator: this.devManufacturerValidator, trigger: 'blur' }]
      },
      submitting: false,
      slotName: undefined,
      deleteable: false,
      propRuleId: undefined,
      visible: false,
      backupVisible: false,
      defaultFileSuffixList: [
        { id: '0', dataId: '0', label: this.$t('pages.fileSuffix'), parentId: '', children: [] }
      ],
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined, // 备份过滤规则id
      adbRuleVisible: false,
      ruleDialogStatus: 'create',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.detectionRules'), 'update'),
        create: this.i18nConcatText(this.$t('pages.detectionRules'), 'create')
      },
      tempW: {},
      defaultTempW: {
        id: undefined,
        devNum: '',
        devManufacturer: '',
        limitRuleType: 1,
        matchType: 0 // 地址匹配类型: 0-完全匹配，1-模糊匹配
      },
      recordEditable: false,
      recordDeleteable: false,
      limitRuleTypeOptions: {
        0: this.$t('pages.limitRuleTypeOptions1'),
        1: this.$t('pages.limitRuleTypeOptions2')
      }
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
    this.resetTempW()
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    selectable(row, index) {
      if (this.formable == false) {
        return false
      } else {
        return true
      }
    },
    closed() {
      this.resetTemp()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.propRuleId = undefined
      this.backupVisible = false
    },
    adbTableList() {
      return this.$refs['adbTableList']
    },
    // 是否禁止
    isDoLimit(data) {
      const executeCode = data.executeCode || []
      return executeCode.includes(1) || executeCode.includes(3)
    },
    // 是否触发响应规则
    isDoResponse(data) {
      const executeCode = data.executeCode || []
      return executeCode.includes(2) || executeCode.includes(4)
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      if (this.temp.ruleId) {
        this.propRuleId = this.temp.ruleId
      }
    },
    handleCheckedFileSuffix() {
      const checkNodes = this.$refs.fileSuffixList.$refs.tree.getCheckedNodes()
      checkNodes.forEach(item => {
        if (item.id != 0 && this.temp.suffixs.indexOf(item.label) < 0) {
          this.temp.suffixs.push(item.label)
        }
      })
      this.visible = false
    },
    handleFilter() {
    },
    handleDrag() {
    },
    createRecordRule() {
      let validate
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          // 增加判重，避免数据重复添加
          const index = this.temp.recordAdbList.findIndex(item => {
            if (this.tempW.limitRuleType == 0 &&
              this.tempW.limitRuleType == item.limitRuleType &&
              this.tempW.devNum == item.devNum) {
              return true
            } else if (this.tempW.limitRuleType == 1 &&
              this.tempW.limitRuleType == item.limitRuleType &&
              this.tempW.matchType == item.matchType &&
              item.devManufacturer == this.tempW.devManufacturer) {
              return true
            } else {
              return false
            }
          })
          if (index == -1) {
            this.tempW.id = new Date().getTime()
            this.temp.recordAdbList.push(this.tempW)
            this.cancelRecordRule()
            validate = valid
          } else {
            this.$message({
              message: this.$t('pages.wifiBlock_text5'),
              type: 'error',
              duration: 2000
            })
          }
        }
      })
      return validate
    },
    updateRecordRule() {
      let validate
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          // 增加判重，避免数据重复添加
          const index = this.temp.recordAdbList.findIndex(item => {
            if (this.tempW.id != item.id && this.tempW.limitRuleType == 0 &&
              this.tempW.limitRuleType == item.limitRuleType &&
              this.tempW.devNum == item.devNum) {
              return true
            } else if (this.tempW.id != item.id && this.tempW.limitRuleType == 1 &&
              this.tempW.limitRuleType == item.limitRuleType &&
              this.tempW.matchType == item.matchType &&
              item.devManufacturer == this.tempW.devManufacturer) {
              return true
            } else {
              return false
            }
          })
          if (index == -1) {
            if (this.tempW.id) {
              this.adbTableList().updateRowData(this.tempW)
            } else {
              this.tempW.id = new Date().getTime()
              this.temp.recordAdbList.push(this.tempW)
            }
            this.cancelRecordRule()
            validate = valid
          } else {
            this.$message({
              message: this.$t('pages.wifiBlock_text5'),
              type: 'error',
              duration: 2000
            })
          }
        }
      })
      return validate
    },
    deleteRecordRule() {
      this.cancelRecordRule()
      const toDeleteIds = this.adbTableList().getSelectedIds()
      this.adbTableList().deleteRowData(toDeleteIds, this.temp.recordAdbList)
    },
    checkSelectionChangeEnd(rowDatas) {
      this.recordDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.recordEditable = true
      } else {
        this.recordEditable = false
        this.cancelRecordRule()
      }
    },
    handleCreate() {
      this.getFileSuffixTree()
      this.resetTemp()
      this.dialogStatus = 'create'
      this.temp.entityType = this.entityNode.type
      this.temp.entityId = this.entityNode.dataId
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.getFileSuffixTree()
      this.dialogStatus = 'update'
      this.temp = Object.assign(this.temp, row)
      // this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      // 统一样式，当不勾选备份复选框时，备份的阀值设置显示为默认值
      if (this.temp.fileBackupLimitSize == 0) {
        this.temp.fileBackupLimitSize = this.defaultTemp.fileBackupLimitSize
        this.temp.isBackup = 0
      } else { // 旧策略没有这个字段
        this.temp.isBackup = 1
      }
      // 兼容旧策略的审计记录管控，旧策略应为规则外，默认记录
      if (row.recordLimitType == undefined) {
        this.temp.recordLimitType = 0
        this.temp.record = 1
      }
      this.$refs['stgDlg'].show(this.temp, this.formable)
      this.temp.entityType = row.entityType
      this.temp.entityId = row.entityId
    },
    cancelRecordRule() {
      this.$refs['dataForm2'] && this.$refs['dataForm2'].clearValidate()
      this.resetTempW()
    },
    beforeupdateRecordRule() {
      Object.assign(this.tempW, this.adbTableList().getSelectedDatas()[0])
    },
    resetTempW() {
      this.tempW = Object.assign({}, this.defaultTempW)
    },
    handleShow: function(row, isGenerateStrategy) {
      this.resetTemp()
      this.getFileSuffixTree()
      this.dialogStatus = 'update'
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      // 统一样式，当不勾选备份复选框时，备份的阀值设置显示为默认值
      if (this.temp.fileBackupLimitSize == 0) {
        this.temp.fileBackupLimitSize = this.defaultTemp.fileBackupLimitSize
        this.temp.isBackup = 0
      } else { // 旧策略没有这个字段
        this.temp.isBackup = 1
      }
      this.$refs['stgDlg'].handleShow(this.temp, this.formable, isGenerateStrategy)
      this.temp.entityType = row.entityType
      this.temp.entityId = row.entityId
    },
    recordChange(data) {
      if (!data) {
        this.temp.isBackup = 0
      }
    },
    formatRowData(rowData) {
      rowData.executeCode = []
      if (rowData.action) {
        rowData.executeCode.push([1].indexOf(rowData.limitType) >= 0 ? 1 : 3)
      }
      if (rowData.isAlarm) {
        rowData.executeCode.push([1].indexOf(rowData.limitType) >= 0 ? 2 : 4)
      }
    },
    formatFormData(formData) {
      if (!this.isDoResponse(formData)) {
        formData.ruleId = null
        formData.isAlarm = 0
      } else {
        formData.isAlarm = 1
      }
      formData.action = this.isDoLimit(formData) ? 1 : 0
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    getFileSuffixTree() {
      getTreeNode().then(res => {
        this.defaultFileSuffixList[0].children = res.data
      })
    },
    limitTypeChange() {
      if (this.temp.limitType == 1) {
        const executeCode = this.temp.executeCode.map(code => {
          return code == 3 ? 1 : code == 4 ? 2 : code
        })
        this.$set(this.temp, 'executeCode', executeCode)
      } else {
        const executeCode = this.temp.executeCode.map(code => {
          return code == 1 ? 3 : code == 2 ? 4 : code
        })
        this.$set(this.temp, 'executeCode', executeCode)
      }
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    validateForm(formData) {
      return this.responseValidate
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      this.temp.suffixs = [...new Set(this.temp.suffixs.concat(new_suffix))]
    },
    handleClear() {
      this.temp.suffixs.splice(0)
    },
    suffixsChange(list) {
      const newMap = new Map()
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
      })
      this.temp.suffixs = Object.keys(newMap) || [];
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.temp.isBackup == 1 && !value) {
        return callback(new Error(this.$t('components.required')))
      } else {
        callback()
      }
    },
    devNumValidator(rule, value, callback) {
      if (!value && this.tempW.limitRuleType == 0) {
        callback(new Error(this.$t('pages.adbRecord_Msg4')))
      } else {
        callback()
      }
    },
    devManufacturerValidator(rule, value, callback) {
      if (!value && this.tempW.limitRuleType == 1) {
        callback(new Error(this.$t('pages.adbRecord_Msg5')))
      } else {
        callback()
      }
    },
    backupchange(data) {
      if (data == 0) {
        this.$refs['stgDlg'].clearValidate('fileBackupLimitSize')
      }
    },
    ruleIsCheck(data) {
      if (data === 1) {
        this.temp.executeCode.unshift(this.temp.limitType === 1 ? 2 : 4);
      } else {
        const executeCode = this.temp.executeCode.filter(code => code != 2 && code != 4)
        this.$set(this.temp, 'executeCode', executeCode)
      }
    },
    limitRuleTypeFormatter(row, data) {
      if (row.limitRuleType == 1 && row.matchType == 0) {
        return this.$t('pages.adbRecord_Msg1')
      } else if (row.limitRuleType == 1 && row.matchType == 1) {
        return this.$t('pages.adbRecord_Msg2')
      } else if (row.limitRuleType == 0) {
        return this.$t('pages.adbRecord_Msg3')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.el-tabs.el-tabs--top{
  .el-tabs__header{
    margin: 0;
    border: 1px solid #aaaaaa;
    border-bottom: none;
    .el-tabs__nav{
      border-top: 0;
      border-left: 0;
    }
  }
}
.el-tab-pane {
  padding: 0 20px;
}
.prohibit >>> .el-checkbox__inner {
  margin-left: 17px;
}
</style>
