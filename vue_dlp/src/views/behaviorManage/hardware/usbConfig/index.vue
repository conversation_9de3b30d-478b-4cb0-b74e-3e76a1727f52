<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>

        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
      >
        <stg-target-form-item
          :target-tree="strategyTree()"
          :is-disabled="!formable"
          :strategy-def-type="query.strategyDefType"
        />
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.stgName')" prop="name">
              <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('text.effectTime')" class="content-flex" prop="timeId">
              <el-select v-model="temp.timeId" :disabled="!formable" :placeholder="$t('text.select')">
                <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <link-button btn-class="editBtn" :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('text.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <FormItem :label="$t('table.limitType')">
          <el-select v-model="temp.controlCode" :disabled="!formable" :placeholder="$t('text.select')">
            <el-option
              v-for="item in controlTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </FormItem>
        <div :class="{'hidden': temp.controlCode!==1}">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>Usb设备白名单</span>
              <label style="color: #2b7aac">（勾选设置白名单，不勾选表示禁止所有设备）</label>
            </div>
            <el-row v-if="formable">
              <el-button size="mini" @click="handleCreate2">
                扫描Usb设备
              </el-button>
              <el-button size="mini" :disabled="!deleteable2" @click="handleDelete2">
                删除Usb设备
              </el-button>
            </el-row>
            <grid-table
              ref="usbTable"
              :show-pager="false"
              :height="300"
              :col-model="colModel2"
              :row-data-api="rowDataApi2"
              :after-load="afterLoad2"
              @selectionChangeEnd="handleSelectionChange2"
            />
          </el-card>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="dialogStatus2==='create'?'添加U盘':'修改U盘'"
      :visible.sync="dialogFormVisible2"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="usbForm"
        :rules="rules"
        :model="temp2"
        label-position="right"
        label-width="80px"
      >
        <FormItem label="usb名称">
          <el-input v-model="temp2.usbName" maxlength="100" />
        </FormItem>
        <FormItem label="usb编码" prop="usbCode">
          <el-input v-model="temp2.usbCode" maxlength="100" />
        </FormItem>
        <FormItem label="制作商">
          <el-input v-model="temp2.manufacturer" maxlength="100" />
        </FormItem>
        <FormItem label="usb容量" prop="usbSize">
          <el-input v-model.number="temp2.usbSize" maxlength="8" @keyup.native="number" />
        </FormItem>
        <FormItem label="usb类型" prop="usbType">
          <el-select v-model="temp2.usbType">
            <el-option v-for="item in usbTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('text.remark')">
          <el-input v-model="temp2.memo" type="textarea" maxlength="300" show-word-limit resize="none" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus2==='create'?createData2():updateData2()">
          {{ dialogStatus2==='create'?'注册U盘':'修改U盘' }}
        </el-button>
        <el-button @click="dialogFormVisible2 = false">
          {{ $t('pages.exit') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getStrategyList, createStrategy, updateStrategy, deleteStrategy,
  findUsbDeviceList, addUsbDevice, updateUsbDevice, deleteUsbDevice
} from '@/api/behaviorManage/hardware/usbConfig'
import {
  enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity,
  objectFormatter, entityLink, refreshPage, buttonFormatter
} from '@/utils'
import { timeIdValidator } from '@/utils/validate'
import { timeInfoFormatter } from '@/utils/formatter'

export default {
  name: 'UsbConfig',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'controlCode', label: 'stgMessage', width: '200', formatter: this.controlTypeFormatter },
        { prop: 'timeId', label: 'stgApproveTime', width: '100', formatter: timeInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'remark', label: 'remark', width: '200' },
        { prop: 'active', label: 'enable', fixedWidth: '80', type: 'icon', iconClass: 'active' },
        { label: 'operate', type: 'button', width: '100',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      colModel2: [
        { prop: 'usbName', label: 'Usb名称', width: '20' },
        { prop: 'usbCode', label: '编码', width: '20' },
        { prop: 'manufacturer', label: '制作商', width: '20' },
        { prop: 'usbType', label: 'Usb类别', width: '20', formatter: this.usbTypeOptionFormatter },
        { prop: 'usbSize', label: 'usbSize', width: '25' },
        { prop: 'memo', label: 'remark', width: '20' },
        { label: 'operate', type: 'button', width: '20',
          buttons: [
            { label: 'edit', formatter: () => { return !this.formable ? '' : this.$t('button.edit') }, click: this.handleUpdate2 },
            { label: 'delete', formatter: () => { return !this.formable ? '' : this.$t('button.delete') }, click: this.deleteUsb }
          ]
        }
      ],
      controlTypeOptions: [
        { value: 0, label: '允许所有' },
        { value: 1, label: '允许如下' },
        { value: 2, label: '设备只读' },
        { value: 3, label: '断网使用' }
      ],
      usbTypeOptions: [
        { value: 0, label: '普通U盘' },
        { value: 1, label: '定制专用U盘' },
        { value: 2, label: '外发U盘' },
        { value: 3, label: '安全U盘' },
        { value: 4, label: '解密Key' },
        { value: 5, label: '普通专用U盘' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      deleteable2: false,
      addBtnAble: false,
      keyword: '',
      keyword2: '',
      temp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        timeId: 1,
        controlCode: 0,
        entityType: undefined,
        entityId: '',
        usbList: []
      },
      temp2: { // 白名单表单字段
        id: undefined,
        usbName: '',
        usbCode: '',
        manufacturer: '',
        usbType: 0,
        usbSize: undefined,
        memo: ''
      },
      selectTreeId: null,
      dialogFormVisible: false,
      dialogFormVisible2: false,
      dialogStatus: '',
      dialogStatus2: '',
      textMap: {
        update: this.formable ? '修改USB存储设备设置' : 'USB存储设备设置详情',
        create: '新增USB存储设备设置'
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.infoRequired', { info: this.$t('pages.stgName') }), trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.timeRequired'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ],
        usbCode: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        usbSize: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    gridTable() {
      return this.$refs['strategyTable']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    usbTable() {
      return this.$refs['usbTable']
    },
    strategyTree: function() {
      return this.$refs['strategyTargetTree']
    },
    afterLoad2: function(rowData, grid) {
      var selectMap = {}
      this.temp.usbList && this.temp.usbList.forEach((item, index) => {
        selectMap[item.id] = item.id
      })
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          if (selectMap[item.id] != null) {
            grid.toggleRowSelection(item)
          }
        })
      })
    },
    selectFirstNode: function() {
      this.tree.setCurrentKey('0')
    },
    // 树节点的点击方法
    treeNodeClick: function(data, node, element) {
      const selectNode = this.tree.getCurrentNode()
      this.selectTreeId = selectNode.dataId
      this.$refs['usbTable'].execRowDataApi()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    // 加载某个软件类别下的应用程序
    rowDataApi2: function(option) {
      return findUsbDeviceList(option)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter2() {
      this.usbTable().execRowDataApi()
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        remark: '',
        active: false,
        timeId: 1,
        controlCode: 0,
        entityType: this.query.objectType,
        entityId: this.query.objectId,
        usbList: []
      }
    },
    resetTemp2() {
      this.temp2 = {
        id: undefined,
        usbName: '',
        usbCode: '',
        manufacturer: '',
        usbType: 0,
        usbSize: undefined,
        memo: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.gridTable.execRowDataApi()
        this.$refs['dataForm'].clearValidate()
        this.$refs['usbTable'].clearSelection()
      })
    },
    handleCreate2() {
      this.resetTemp2()
      this.dialogStatus2 = 'create'
      this.dialogFormVisible2 = true
      this.$nextTick(() => {
        this.$refs['usbForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.usbTable().execRowDataApi()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate2: function(row) {
      this.temp2 = Object.assign({}, row) // copy obj
      this.dialogStatus2 = 'update'
      this.dialogFormVisible2 = true
      this.$nextTick(() => {
        this.$refs['usbForm'].clearValidate()
      })
    },
    handleImport() {
    },
    handleExport() {
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleSelectionChange2(val) {
      this.deleteable2 = val.length > 0
      // 先删除当前所选类别的已选白名单
      this.temp.usbList = val
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.controlCode !== 1) {
            this.temp.usbList = []
          }
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    createData2() {
      this.submitting = true
      this.$refs['usbForm'].validate((valid) => {
        if (valid) {
          addUsbDevice(this.temp2).then(() => {
            this.submitting = false
            this.dialogFormVisible2 = false
            this.usbTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.controlCode !== 1) {
            this.temp.usbList = []
          }
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData2() {
      this.submitting = true
      this.$refs['usbForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp2)
          updateUsbDevice(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible2 = false
            this.usbTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    deleteUsb(row) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const obj = {
          ids: row.id
        }
        deleteUsbDevice(obj).then(() => {
          this.usbTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleDelete2() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const idArr = this.usbTable().getSelectedIds()
        if (idArr.length > 0) {
          const obj = {
            ids: idArr.join(',')
          }
          deleteUsbDevice(obj).then(() => {
            this.usbTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    controlTypeFormatter: function(row, data) {
      for (let i = 0, size = this.controlTypeOptions.length; i < size; i++) {
        const id = this.controlTypeOptions[i].value
        if (id === data || id.toString() === data) {
          let msg = this.controlTypeOptions[i].label
          if (id === 1) {
            if (row.usbList && row.usbList.length > 0) {
              msg += '('
              row.usbList.forEach((item, index) => {
                msg += item.usbCode
                if (index !== row.usbList.length - 1) {
                  msg += '、'
                }
              })
              msg += ')'
            } else {
              msg = '禁止所有USB设备'
            }
          }
          return msg
        }
      }
      return ''
    },
    usbTypeOptionFormatter(row, data) {
      let msg = ''
      this.usbTypeOptions.forEach(item => {
        if (row.usbType == item.value) {
          msg = item.label
        }
      })
      return msg
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    number() {
      if (isNaN(this.temp2.usbSize)) {
        this.temp2.usbSize = this.temp2.usbSize.replace(/[^\.\d]/g, '')
      }
    }
  }
}
</script>
