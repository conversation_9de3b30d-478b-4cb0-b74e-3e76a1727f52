<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="dialogTitle"
      :stg-code="stgTypeNumber"
      :active-able="treeable"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :width="$store.getters.language === 'en' ? 900 : 800"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
    >
      <template :slot="slotName">
        <div v-if="stgTypeNumber == 44" style="display: inline-block; width: 400px; height: 0; position: relative;" :style="{ left: isPrepare ? `1${isEnglish() ? 4 : 0}0px` : '180px', top: isPrepare ? 0 : '-34px' }">
          <FormItem v-if="stgTypeNumber == 44" label-width="148px" :label="$t('pages.enableDesktopWaterMark')" :tooltip-content="$t('pages.desktopWaterMarkTip')">
            <el-switch v-model="activeDesktopWatermark" :disabled="!formable" @change="desktopWatermarkChange"/>
          </FormItem>
        </div>
        <el-tabs v-model="activeTab" :style="{ marginTop: (isPrepare || stgTypeNumber === 2) ? 0 : '-28px' }">
          <el-tab-pane :label="$t('pages.watermarkSet')" name="first">
            <FormItem label-width="0" prop="waterMark">
              <!-- <span v-model="temp.waterMark"></span> -->
            </FormItem>
            <div style="height: 240px;">
              <tree-menu
                ref="waterMarkTree"
                v-model="temp.waterMark"
                default-expand-all
                check-strictly
                :data="treeData"
                :checked-keys="checkIds"
                multiple
                :disabled-all-nodes="!formable"
                :render-content="renderContent"
                @check-change="treeNodeClick"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane name="second" :label="stgTypeNumber == 2 ? $t('pages.controlApp') : $t('pages.processWatermark')" :disabled="stgTypeNumber == 44 && activeDesktopWatermark">
            <div v-if="stgTypeNumber==2" style="padding-left: 20px">
              <el-checkbox v-model="waterType" :disabled="!formable" :false-label="0" :true-label="1" @change="waterTypeChange">{{ $t('pages.waterMark_Msg') }}</el-checkbox>
              <div style="margin: 5px 0 5px 24px; color: #20a0ff;">{{ $t('pages.waterMark_Msg1') }}</div>
            </div>
            <el-card class="box-card" :body-style="{'padding': '0px'}" style="margin-bottom: 0px;">
              <div class="button-group">
                <el-button size="small" :disabled="waterType === 1 || !formable" @click="handleCreate2('create')">
                  {{ $t('button.add') }}
                </el-button>
                <el-button size="small" :disabled="waterType === 1 || !deleteable1 || !formable" @click="handleDelete2">
                  {{ $t('button.delete') }}
                </el-button>
                <el-button size="small" :disabled="waterType === 1 || !formable" @click="handleCreateCommonSoft">
                  {{ $t('pages.addCommonControlledProcedures') }}
                </el-button>
                <el-button size="small" :disabled="waterType === 1 || !formable" @click="handleImportApp">
                  {{ $t('pages.waterMark_importApplication') }}
                </el-button>
              </div>
              <FormItem label-width="0" prop="processList"/>
              <grid-table
                ref="softList"
                :height="180"
                :show-pager="false"
                :col-model="colModel2"
                :selectable="selectable"
                :row-data-api="rowDataApi2"
                :after-load="afterLoad"
                @selectionChangeEnd="selectionChangeEnd1"
              />
            </el-card>
          </el-tab-pane>
          <el-tab-pane v-if="stgTypeNumber == 44" name="third" :disabled="activeDesktopWatermark">
            <template slot="label">
              {{ $t('pages.webPageWatermark') }}
              <el-tooltip class="item" effect="dark" placement="top-start" :disabled="activeDesktopWatermark">
                <div slot="content">{{ $t('pages.waterMark_Msg53') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </template>
            <FormItem label-width="100" :label="$t('pages.rangeOfControl')">
              <el-radio-group v-model="waterType" :disabled="!formable" style="padding-left: 20px" @input="waterTypeChange">
                <el-radio :label="5">{{ $t('pages.noLimitWebpage') }}</el-radio>
                <el-radio :label="4">{{ $t('pages.allWebPage') }}</el-radio>
                <el-radio :label="2">{{ $t('pages.followPage') }}
                  <el-tooltip class="item" effect="dark" placement="bottom-end">
                    <div slot="content">{{ $t('pages.waterMark_Msg50') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-radio>
                <el-radio :label="3">{{ $t('pages.besideFollowPage') }}
                  <el-tooltip class="item" effect="dark" placement="bottom-end">
                    <div slot="content">{{ $t('pages.waterMark_Msg50') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
            </FormItem>
            <el-card class="box-card" :body-style="{'padding': '0px'}" style="margin-bottom: 0">
              <div class="button-group">
                <div style="display: inline-flex; margin: 3px 1px">
                  <el-button size="small" :disabled="disableWebWaterEditor || !formable" @click="handleCreate4('create')">
                    {{ $t('button.add') }}
                  </el-button>
                  <el-button size="small" :disabled="disableWebWaterEditor || !deleteable4 || !formable" @click="handleDelete4">
                    {{ $t('button.delete') }}
                  </el-button>
                  <el-button size="small" :disabled="disableWebWaterEditor || !formable" @click="handleCreateCommonURL">
                    {{ $t('pages.addCommonUrl') }}
                  </el-button>
                  <el-button size="small" :disabled="disableWebWaterEditor || !formable" @click="handleCreateImportUrl">
                    {{ $t('pages.createImportUrl') }}
                  </el-button>
                </div>
                <div style="float: right;">
                  <el-tooltip placement="top">
                    <div slot="content">{{ $t('pages.searchWebUrlInfo') }}</div>
                    <el-input v-model="searchInfo" v-trim clearable style="width: 130px;" :disabled="disableWebWaterEditor" maxlength="" @keyup.enter.native="handleSearchFun" />
                  </el-tooltip>
                  <el-button type="primary" icon="el-icon-search" size="mini" :disabled="disableWebWaterEditor" style="margin: 0" @click="handleSearchFun">
                    {{ $t('table.search') }}
                  </el-button>
                </div>
              </div>
              <FormItem label-width="0" prop="urlList"/>
              <grid-table
                ref="webList"
                :height="200"
                :show-pager="false"
                :col-model="colModel4"
                :row-no-label="$t('table.keyId')"
                :selectable="selectable4"
                :row-data-api="rowDataApiForUrl"
                :after-load="afterLoad"
                @selectionChangeEnd="selectionChangeEnd4"
              />
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </template>
      <template slot="button">
        <link-button
          btn-type="primary"
          btn-style="float: left"
          :formable="formable"
          :menu-code="'A5A'"
          :link-url="{ path: '/system/baseData/waterMarkLib', query: { tabName: tabName } }"
          :btn-text="$t('pages.waterMark_Msg2')"
        />
      </template>
    </stg-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="i18nConcatText($t('table.websiteUrl'), dialogStatus4 ? (dialogStatus4.indexOf('create') >= 0 ? 'create' : 'update') : 'create')"
      :visible.sync="dialogFormVisible4"
      width="400px"
    >
      <Form ref="dataForm4" :hide-required-asterisk="true" :rules="rules4" :model="temp4" label-width="80px">
        <FormItem :label="$t('table.websiteUrl')" prop="urlKeyword">
          <el-input v-model="temp4.urlKeyword" maxlength="100"/>
        </FormItem>
        <FormItem :label="$t('table.websiteDesp')" prop="urlRemark">
          <el-input v-model="temp4.urlRemark" maxlength="255"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting4" @click="dialogStatus4.indexOf('update') >= 0 ?updateData4():createData4()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible4 = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.commonWebsiteInfo')"
      :visible.sync="dialogFormVisible5"
      width="800px"
    >
      <el-button size="primary" @click="handleCreate4('createCustom')">{{ $t('button.add') }}</el-button>
      <el-button size="primary" :disabled="!updateable5" @click="handleUpdate5">{{ $t('button.edit') }}</el-button>
      <el-button type="primary" :disabled="!deleteable5" @click="handleDelete5">{{ $t('button.delete') }}</el-button>
      <div style="float: right;">
        <el-input v-model="urlQuery.searchInfo" clearable :placeholder="$t('table.websiteUrl')" style="width: 130px; margin-bottom: 3px" @keyup.enter.native="urlHandleFilter" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="urlHandleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
      <grid-table
        ref="commonUrl"
        row-key="id"
        :height="400"
        :col-model="colModel5"
        :row-data-api="rowDataApi5"
        @selectionChangeEnd="selectionChangeEnd5"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="importUrlToStg()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible5 = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="dialogStatus2==='update' ? $t('pages.updateControlledProcedures') : $t('pages.addControlledProcedures')"
      :visible.sync="dialogFormVisible2"
      width="400px"
    >
      <Form ref="dataForm2" :hide-required-asterisk="true" :rules="rules" :model="temp2" label-width="80px">
        <FormItem :label="$t('pages.programName')" prop="processName">
          <el-input v-model="temp2.processName" class="input-with-button" style="width: calc(100% - 90px);" maxlength=""/>
          <el-upload
            ref="uploadProcess"
            name="processFile"
            action="1111"
            accept=".exe"
            :limit="1"
            :show-file-list="false"
            :before-upload="beforeUpload"
            style="display: inline-block;"
          >
            <el-button size="mini" type="primary" style="width: 86px; margin: 0 !important;">{{ $t('pages.selectFile') }}</el-button>
          </el-upload>
        </FormItem>
        <FormItem :label="$t('pages.exeDesc')" prop="processRemark">
          <el-input v-model="temp2.processRemark" maxlength="300" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submittingProcess" @click="dialogStatus2==='update'?updateData2():createData2()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogStatusClose">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.commonControlledProcedures')"
      :visible.sync="dialogFormVisible3"
      width="800px"
    >
      <el-tabs ref="tabs" v-model="activeTab2" type="card" style="height: 40px;" @tab-click="tabClickFunc2">
        <el-tab-pane v-if="showTabs2.indexOf('win') > -1" label="Windows" name="winTab">
        </el-tab-pane>
        <el-tab-pane v-if="showTabs2.indexOf('mac') > -1" label="Mac" name="macTab">
        </el-tab-pane>
        <el-tab-pane v-if="showTabs2.indexOf($t('pages.userDefined')) > -1" :label="$t('pages.userDefined')" name="customTab">
        </el-tab-pane>
      </el-tabs>
      <div v-if="'customTab' === activeTab2" class="toolbar" style="margin-top: 7px">
        <el-button size="primary" @click="handleCreate2('createCustom')">{{ $t('button.add') }}</el-button>
        <el-button size="primary" :disabled="!updateable2" @click="handleUpdate2()">{{ $t('button.edit') }}</el-button>
        <el-button type="primary" :disabled="!deleteable2" @click="handleDelete3()">{{ $t('button.delete') }}</el-button>
        <div style="float: right;">
          <el-input v-model="processSearchInfo" clearable :placeholder="$t('pages.programName')" style="width: 130px; margin-bottom: 3px" @keyup.enter.native="loadCustomSoftList" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="loadCustomSoftList">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="commonSoft"
        row-key="processName"
        :height="'customTab' === activeTab2 ? 364 : 400"
        :show-pager="false"
        :col-model="colModel3"
        :row-datas="rowDatas"
        style="margin-top: 7px"
        @selectionChangeEnd="selectionChangeEnd2"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createData3()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="clearData3">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <preview-dlg ref="previewDef"/>
    <app-select-dlg ref="appSelectDlg" :column-show="['processName', 'name', 'productName', 'fileDescription']" @select="handleAddAppAfter"/>

    <!-- 导入网址信息对话框-->
    <import-table-dlg
      ref="urlImportTable"
      :not-selected-prompt-message="$t('pages.url_text2')"
      :elg-title="$t('pages.importUrlLibrary')"
      :group-root-name="$t('pages.urlLibrary')"
      :search-info-name="$t('pages.websiteName')"
      :confirm-button-name="$t('pages.addUrl')"
      :group-title="$t('pages.url_group_title')"
      :col-model="importColModel"
      :list="getInfoList"
      :load-group-tree="loadGroupTree"
      :create-group="createUrlGroup"
      :update-group="updateUrlGroup"
      :delete-group="deleteUrlGroup"
      :count-by-group="countUrlByGroupId"
      :get-group-by-name="getUrlGroupByName"
      :delete="deleteUrl"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteUrlGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleCreateElg"
      :get-list-by-group-ids="listUrlByGroupId"
      @submitEnd="submitImport"
    />

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[urlDialogStatus]"
      :visible.sync="urlDialogFormVisible"
      width="600px"
    >
      <Form
        ref="urlDataForm"
        :rules="urlRules"
        :model="urlTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="$t('pages.websiteName')" prop="name">
          <el-input v-model="urlTemp.name" v-trim :maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.websiteUrl')" prop="address">
          <el-input v-model="urlTemp.address" v-trim :maxlength="60" :placeholder="$t('pages.urlLibrary_text1')"/>
        </FormItem>
        <FormItem :label="$t('pages.groupType')" prop="groupId">
          <el-row>
            <el-select v-model="urlTemp.groupId" filterable :placeholder="$t('text.select')">
              <el-option v-for="item in groupTreeList" :key="item.id" :label="item.label" :value="item.dataId"/>
            </el-select>
          </el-row>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="urlTemp.remark" type="textarea" rows="3" resize="none" maxlength="100" show-word-limit/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="urlDlgSubmit" @click="urlDialogStatus==='createUrl'?createUrlData(): urlDialogStatus==='updateUrl'? updateUrlData() : {}">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="urlDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  createStrategy,
  updateStrategy,
  getStrategyByName,
  findPrintAppList,
  savePrintApp,
  savePrintAppBatch,
  editPrintApp,
  removePrintAppBatch,
  getBatchPrintAppList,
  getPrintAppByName,
  saveWatermarkUrl,
  getWatermarkUrlByIds,
  updateWatermarkUrl,
  getWatermarkUrlPage,
  deleteWatermarkUrl,
  getWatermarkUrlByName,
  getPrintAppByNames, fromLibToWatermarkUrl
} from '@/api/behaviorManage/hardware/waterMark'
import {
  createUrl, getUrlList, updateUrl, getTreeNode, listUrlByGroupId,
  getByIds, getUrlByAdress, createUrlGroup, updateUrlGroup, deleteUrlGroup,
  getUrlGroupByName, deleteGroupAndData, moveGroupToOther, countUrlByGroupId, deleteUrl
} from '@/api/system/baseData/urlLibrary';
import PreviewDlg from '@/views/system/baseData/waterMarkLib/previewDlg'
import { getWaterMarkTree } from '@/api/dataEncryption/encryption/fileOutgoing'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import AppSelectDlg from '@/views/system/baseData/appGroup/appSelectDlg'
import { findNodeLabel } from '@/utils/tree';

export default {
  name: 'WaterMarkDialog',
  components: { AppSelectDlg, PreviewDlg, ImportTableDlg },
  props: {
    // 只显示详情弹窗，策略总览那边查看详情调用
    formable: { type: Boolean, default: true }, // 能否提交表单
    treeable: { type: Boolean, default: true }, // 是否显示对象树
    stgTypeNumber: { type: Number, default: 2 }, // 策略类型编号 2打印水印策略 44屏幕水印策略
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      colModel2: [
        { prop: 'processName', label: 'programName', width: '10', sort: true },
        { prop: 'processRemark', label: 'exeDesc', width: '10' },
        { label: 'operate', type: 'button', width: '5', hidden: !this.formable,
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate2 }
          ]
        }
      ],
      colModel3: [
        { prop: 'processName', label: 'programName', width: '10', sort: true },
        { prop: 'commonName', label: 'commonNouns', width: '10', sort: true, hidden: () => this.isHiddenCommen },
        { prop: 'processRemark', label: 'exeDesc', width: '20' }
      ],
      colModel4: [
        { prop: 'urlKeyword', label: 'websiteUrl', width: '10', sort: true },
        { prop: 'urlRemark', label: 'websiteDesp', width: '10', sort: true },
        { label: 'operate', type: 'button', width: '5', hidden: !this.formable,
          buttons: [
            { label: 'edit', disabledFormatter: () => this.disableWebWaterEditor, formatter: this.buttonFormatter, click: this.handleUpdate4 }
          ]
        }
      ],
      colModel5: [
        { prop: 'urlKeyword', label: 'websiteUrl', width: '10', sort: true },
        { prop: 'urlRemark', label: 'websiteDesp', width: '20', sort: true }
      ],
      rowDatas: [],
      showTabs2: ['win', 'mac', this.$t('pages.userDefined')],
      commonSoftList: [
        { processName: 'wps.exe', commonName: this.$t('pages.waterMark_Msg3'), processRemark: this.$t('pages.waterMark_Msg4') },
        { processName: 'et.exe', commonName: this.$t('pages.waterMark_Msg5'), processRemark: this.$t('pages.waterMark_Msg6') },
        { processName: 'wpp.exe', commonName: this.$t('pages.waterMark_Msg7'), processRemark: this.$t('pages.waterMark_Msg8') },
        { processName: 'winword.exe', commonName: this.$t('pages.waterMark_Msg9'), processRemark: this.$t('pages.waterMark_Msg10') },
        { processName: 'excel.exe', commonName: this.$t('pages.waterMark_Msg11'), processRemark: this.$t('pages.waterMark_Msg12') },
        { processName: 'powerpnt.exe', commonName: this.$t('pages.waterMark_Msg13'), processRemark: this.$t('pages.waterMark_Msg14') },
        { processName: 'pptview.exe', commonName: this.$t('pages.waterMark_Msg15'), processRemark: this.$t('pages.waterMark_Msg16') },
        { processName: 'uedit32.exe', commonName: this.$t('pages.waterMark_Msg17'), processRemark: this.$t('pages.waterMark_Msg18') },
        { processName: 'notepad.exe', commonName: this.$t('pages.waterMark_Msg19'), processRemark: this.$t('pages.waterMark_Msg20') },
        { processName: 'photoshop.exe', commonName: this.$t('pages.waterMark_Msg21'), processRemark: this.$t('pages.waterMark_Msg22') }
      ],
      commonMacSoftList: [
        { processName: 'TextEdit', commonName: this.$t('pages.waterMark_Msg23'), processRemark: this.$t('pages.waterMark_Msg24') },
        { processName: 'Preview', commonName: this.$t('pages.waterMark_Msg25'), processRemark: this.$t('pages.waterMark_Msg26') },
        { processName: 'QuickTime Player', commonName: this.$t('pages.waterMark_Msg27'), processRemark: this.$t('pages.waterMark_Msg28') },
        { processName: 'Pages', commonName: this.$t('pages.waterMark_Msg29'), processRemark: this.$t('pages.waterMark_Msg30') },
        { processName: 'Numbers', commonName: this.$t('pages.waterMark_Msg31'), processRemark: this.$t('pages.waterMark_Msg32') },
        { processName: 'Keynote', commonName: this.$t('pages.waterMark_Msg33'), processRemark: this.$t('pages.waterMark_Msg34') },
        { processName: 'wpsoffice', commonName: this.$t('pages.waterMark_Msg35'), processRemark: this.$t('pages.waterMark_Msg36') },
        { processName: 'Microsoft Word', commonName: this.$t('pages.waterMark_Msg37'), processRemark: this.$t('pages.waterMark_Msg38') },
        { processName: 'Microsoft PowerPoint', commonName: this.$t('pages.waterMark_Msg39'), processRemark: this.$t('pages.waterMark_Msg40') },
        { processName: 'Microsoft Excel', commonName: this.$t('pages.waterMark_Msg41'), processRemark: this.$t('pages.waterMark_Msg42') }
      ],
      importColModel: [
        { prop: 'name', label: 'websiteName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'websiteUrl', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdateImport }
          ]
        }
      ],
      customSoftList: [],
      isHiddenCommen: false,
      activeDesktopWatermark: true,
      query: { // 查询条件
        page: 1,
        stgTypeNumber: this.stgTypeNumber,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      importTableQuery: {
        page: 1
      },
      urlQuery: {
        page: 1,
        searchInfo: ''
      },
      waterType: 1,
      controlRange: 4,
      temp: {},
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        entityType: '',
        entityId: undefined,
        waterType: 1,
        processList: [],
        urlList: [],
        waterMark: null
      },
      temp2: {
        id: undefined,
        processName: '',
        processRemark: '',
        appType: this.stgTypeNumber
      },
      temp3: {
        processName: '',
        ctrlCode: 1
      },
      temp4: {
        id: undefined,
        urlKeyword: '',
        urlRemark: ''
      },
      searchInfo: '',
      // 新增网址信息库网址时的表单
      urlTemp: {},
      activeTab: 'first',
      activeTab2: 'winTab',
      dialogFormVisible: false,
      dialogFormVisible2: false,
      dialogFormVisible3: false,
      dialogFormVisible4: false,
      dialogFormVisible5: false,
      deleteable1: false,
      deleteable2: false,
      deleteable4: false,
      deleteable5: false,
      updateable2: false,
      updateable5: false,
      dialogStatus: '',
      dialogStatus2: '',
      dialogStatus4: '',
      submitting: false,
      submitting4: false,
      submittingProcess: false,
      slotName: undefined,
      dialogTitle: undefined,
      waterMarkType: 1,
      validError: 0,  // 持续校验码
      treeData: [],
      checkIds: [],
      rules: {
        name: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }],
        processName: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.customSoftValidator, trigger: 'change' }
        ],
        waterMark: [{ required: true, validator: this.waterMarkValidator, trigger: 'change' }],
        processList: [{ required: true, validator: this.processListValidator, trigger: 'change' }],
        urlList: [{ required: true, validator: this.urlListValidator, trigger: 'change' }]
      },
      rules4: {
        urlKeyword: [{ required: true, message: this.$t('pages.required1'), trigger: 'blur' }, { validator: this.urlKeywordValidator, trigger: 'blur' }]
      },
      processSearchInfo: '',
      //  保存选中的数据
      selected: {
        winTab: [],
        macTab: [],
        customTab: []
      },
      // 导入对话框分组节点
      groupTreeList: [],
      urlDlgSubmit: false,
      urlDialogFormVisible: false,
      urlDialogStatus: 'createUrl',
      urlRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_enterUrl'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }
        ]
      },
      textMap: {
        createUrl: this.i18nConcatText(this.$t('table.websiteUrl'), 'create'),
        updateUrl: this.i18nConcatText(this.$t('table.websiteUrl'), 'update')
      },
      updateProcessName: '',
      isPrepare: false
    }
  },
  computed: {
    disableWebWaterEditor() {
      return this.waterType === 4 || this.waterType === 5
    },
    tabName() {
      // 策略类型编号 2打印水印策略 44屏幕水印策略
      const tab = { 2: 'printTab', 44: 'screenTab' }
      return tab[this.stgTypeNumber]
    }
  },
  watch: {
    stgTypeNumber(val) {
      this.loadWaterMarkTree()
    }
  },
  created() {
    this.rowDatas = this.commonSoftList
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    this.isPrepare = this.$route.path.includes('/prepare')
    this.initDataByStgTypeNumber()
    this.loadWaterMarkTree()
  },
  activated() {
    this.loadWaterMarkTree()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    getTreeNode,
    createUrlGroup,
    updateUrlGroup,
    deleteUrlGroup,
    countUrlByGroupId,
    getUrlGroupByName,
    deleteUrl,
    deleteGroupAndData,
    moveGroupToOther,
    listUrlByGroupId,
    getByIds,
    webList() {
      return this.$refs.webList
    },
    commonUrl() {
      return this.$refs.commonUrl
    },
    urlImportTable() {
      return this.$refs.urlImportTable
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      this.checkIds.splice(0)
      this.$nextTick(() => {
        const id = this.temp.waterMark && this.temp.waterMark.id
        if (id && this.checkIds.indexOf(id) == -1) {
          this.checkIds.push(id)
        }
      })
    },
    initDataByStgTypeNumber() {
      if (this.stgTypeNumber === 2) {
        this.dialogTitle = this.$t('pages.waterMarkConfig1')
        this.waterMarkType = 2
      } else if (this.stgTypeNumber === 44) {
        this.dialogTitle = this.$t('pages.screenWaterMark')
        this.waterMarkType = 1
      }
    },
    processValidator(rule, value, callback) {
      var fileContentType = value.match(/^(.*)(\.)(.{1,8})$/)[3]
      if (fileContentType && fileContentType.toLowerCase() == 'exe') {
        callback()
      } else {
        callback(new Error(this.$t('pages.waterMark_Msg43')))
      }
    },
    waterMarkValidator(rule, value, callback) {
      if (this.temp.waterMark == null) {
        callback(new Error(this.$t('pages.waterMark_Msg44')))
        this.activeTab = 'first'
        this.validError = 1
      } else {
        callback()
        this.validError = 0
      }
    },
    processListValidator(rule, value, callback) {
      if (this.validError === 0) {
        const datas = this.temp.processList || []
        if (this.waterType === 0 && datas.length === 0 && this.stgTypeNumber === 2) {
          callback(new Error(this.$t('pages.waterMark_Msg45')))
          this.activeTab = 'second'
          this.validError = 2
        } else {
          callback()
          this.validError = 0
        }
      } else {
        callback()
      }
    },
    urlListValidator(rule, value, callback) {
      if (this.validError === 0) {
        const datas = this.temp.urlList || []
        if (!this.activeDesktopWatermark && !this.disableWebWaterEditor && datas.length === 0) {
          callback(new Error(this.$t('pages.waterMark_Msg52')))
          this.activeTab = 'third'
          this.validError = 2
        } else {
          callback()
          this.validError = 0
        }
      } else {
        callback()
      }
    },
    //  判断进程名称是否在windows获取mac下存在
    validatorSoftNameIsExistWindowsAndMac(processName) {
      processName = processName || ''
      //  windows下不区分大小写
      const lowerCaseProcessName = processName.toLowerCase()
      const winExits = this.commonSoftList.filter(item => item.processName.toLowerCase() === lowerCaseProcessName) || []
      if (winExits.length > 0) {
        return true;
      }
      //  (由于mysql表结构不区分大小写，当前mac是不区分大小写）
      const macExits = this.commonMacSoftList.filter(item => item.processName.toLowerCase() === lowerCaseProcessName) || []
      if (macExits.length > 0) {
        return true;
      }
      return false;
    },
    customSoftValidator(rule, value, callback) {
      if (this.dialogStatus2 === 'create' || (this.dialogStatus2 === 'update' && this.updateProcessName !== '' && this.updateProcessName === value)) {
        callback()
      } else if (this.validatorSoftNameIsExistWindowsAndMac(value)) {
        callback(new Error(this.$t('pages.alreadyExistsProgramMsg') + ''))
      } else {
        getPrintAppByName({ appType: this.stgTypeNumber, processName: value }).then(respond => {
          if (respond.data != null && respond.data.processName === value) {
            callback(new Error(this.$t('pages.theProgramAlreadyExists') + ''))
          }
          callback()
        })
      }
    },
    urlKeywordValidator(rule, value, callback) {
      // 验证网址库
      if (this.dialogStatus4.indexOf('Custom') >= 0 || this.dialogStatus4.indexOf('update') >= 0) {
        getWatermarkUrlByName({ name: value }).then(res => {
          const bean = res.data
          if (bean && this.temp4.id != bean.id) {
            callback(new Error(this.$t('pages.theUrlAlreadyExists')))
          }
          callback()
        }).catch(reason => {})
      } else {
        // 走到当前方法说明进入到了策略的预定义增加web网址
        // 判断策略预定义的web网址列表是否存在当前的网址，存在则抛出异常，不存在加入
        const index = (this.temp.urlList || []).findIndex(url => url.urlKeyword == value)
        if (index >= 0) {
          callback(new Error(this.$t('pages.theUrlAlreadyExists')))
        }
        callback()
      }
    },
    treeNodeClick: function(checkedNodeIds, data) {
      const oldId = this.checkIds[0]
      let flag = false
      if (data) {
        data.forEach(item => {
          if (item.type !== 'G' && oldId != item.id) {
            flag = true
            item.oriData.id = item.dataId
            this.temp.waterMark = item.oriData
            this.checkIds.splice(0, 1, item.id)
          }
        })
      }
      if (!flag && oldId != null) {
        this.checkIds.splice(0, 1, oldId)
      } else if (!flag && oldId == null) {
        this.checkIds.splice(0)
      }
      if (this.validError === 1) {
        this.validError = 0
        // 单独校验是否选中了水印
        if (this.$refs['stgDlg']) {
          const form = this.$refs['stgDlg'].$refs['dataForm4StgDialog' + this.slotName]
          if (form) {
            form.validateField('waterMark')
          }
        }
        // this.$refs['stgDlg'].validFormData()
        // this.$refs['stgDlg'].validCurSlotForm()
      }
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.type == 'G'
      return (
        <div class='custom-tree-node'>
          <svg-icon v-show={iconShow} icon-class='group' />
          <svg-icon v-show={!iconShow} icon-class='watermark' />
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='eye-open' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleView(data)} />
          </span>
        </div>
      )
    },
    handleView(data) {
      this.$refs['previewDef'].handleUpdate(data.oriData, this.stgTypeNumber)
    },
    loadWaterMarkTree: function() {
      getWaterMarkTree({ type: this.waterMarkType }).then(respond => {
        this.treeData = respond.data
      })
    },
    numberLimit(value, type, min, max) {
      this.temp[type] = value > max ? max : value < min ? min : parseInt(value)
    },
    selectable() {
      return this.formable && this.waterType !== 1
    },
    selectable4() {
      return this.formable && !this.disableWebWaterEditor
    },
    buttonFormatter: function(row) {
      if (this.formable) {
        return this.$t('text.edit')
      } else {
        return ''
      }
    },
    rowDataApi2: function(option) {
      const ids = (this.temp.processList || []).map(item => item.id).join(',')
      if (ids.length == 0) { return this.defaultApi() }
      const result = getBatchPrintAppList({ ids: ids });
      result.then(res => {
        (res.data.items || []).forEach(item => {
          const t = this.temp.processList.filter(process => { return process.id === item.id })
          if (t && t.length > 0) {
            item.processRemark = t[0].processRemark
          }
        });
      })
      return result;
    },
    // 策略url表格查询函数，并带有过滤相同网址的作用
    rowDataApiForUrl(option) {
      let urlList = this.temp.urlList || []
      let isLimit = false
      const searchInfo = this.searchInfo.toLowerCase()
      if (urlList.length == 0) {
        return this.defaultApi()
      } else if (urlList.length > 200) {
        isLimit = true
        urlList = urlList.slice(0, 200)
      }
      if (option && option.search) {
        return this.defaultApi(
          this.temp.urlList.filter(
            item => item.urlKeyword.toLowerCase().includes(searchInfo) ||
              item.urlRemark.toLowerCase().includes(searchInfo)
          )
        )
      }
      const ids = urlList.map(item => item.id).join(',')
      const apiRequest = getWatermarkUrlByIds({ ids: ids })
      apiRequest.then(res => {
        this.temp.urlList = ([...res.data] || [])
        if (this.searchInfo) {
          res.data = res.data.filter(data =>
            data.urlKeyword.toLowerCase().includes(searchInfo) ||
            data.urlRemark.toLowerCase().includes(searchInfo)
          )
        }
        if (isLimit) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.waterMark_Msg54'),
            type: 'warning',
            duration: 4000
          })
        }
      })
      return apiRequest
    },
    defaultApi(dataItems) {
      return new Promise((resolve, reject) => {
        resolve({
          code: 20000,
          data: {
            total: 0,
            items: dataItems || []
          }
        })
      })
    },
    // loadCommonSoft: function() {
    //   return new Promise((resolve, reject) => {
    //     resolve({
    //       code: 20000,
    //       data: {
    //         total: this.commonSoftList.length,
    //         items: this.commonSoftList
    //       }
    //     })
    //   })
    // },
    tabClickFunc2: function() {
      const activeTab2 = this.activeTab2;
      if ('winTab' === activeTab2) {
        this.rowDatas = this.commonSoftList;
        this.isHiddenCommen = false
      } else if ('macTab' === activeTab2) {
        this.rowDatas = this.commonMacSoftList;
        this.isHiddenCommen = false
      } else {
        this.isHiddenCommen = true
        this.rowDatas = this.customSoftList;
      }
      this.$nextTick(() => {
        this.selected[this.activeTab2].forEach(item => {
          this.$refs['commonSoft'].toggleRowSelection(item, true)
        })
      })
    },
    urlHandleFilter() {
      this.urlQuery.page = 1
      this.commonUrl().execRowDataApi(this.urlQuery)
    },
    handleUpdate4(row) {
      this.resetTemp4()
      this.dialogStatus4 = 'update'
      this.temp4 = JSON.parse(JSON.stringify(row))
      this.dialogFormVisible4 = true
      this.$nextTick(() => {
        this.$refs.dataForm4.clearValidate()
      })
    },
    loadCustomSoftList: function() {
      findPrintAppList({ appType: this.stgTypeNumber, searchInfo: this.processSearchInfo }).then(res => {
        const list = res.data.items.filter(item => !this.validatorSoftNameIsExistWindowsAndMac(item.processName))
        this.customSoftList.splice(0, this.customSoftList.length, ...list)
      })
    },
    selectionChangeEnd1: function(rowDatas) {
      this.deleteable1 = rowDatas && rowDatas.length > 0
      // this.temp.processList = rowDatas
      if (this.validError === 2) {
        this.validError = 0
        // this.$refs['stgDlg'].validCurSlotForm()
      }
    },
    selectionChangeEnd2: function(rowDatas) {
      this.selected[this.activeTab2].splice(0);
      (rowDatas || []).forEach(item => {
        this.selected[this.activeTab2].push(item)
      })
      this.updateable2 = rowDatas && rowDatas.length === 1
      this.deleteable2 = rowDatas && rowDatas.length > 0
    },
    selectionChangeEnd5(rowDatas) {
      this.updateable5 = rowDatas && rowDatas.length === 1
      this.deleteable5 = rowDatas && rowDatas.length > 0
    },
    printerTargetNodeChange: function(tabName, checkedNode) {
      this.$refs.printerSelectList.execRowDataApi()
    },
    resetTemp() {
      this.activeTab = 'first'
      this.activeDesktopWatermark = true
      this.remark = ''
      this.searchInfo = ''
      this.checkIds.splice(0)
    },
    resetTemp2(type) {
      this.temp2 = {
        id: undefined,
        processName: '',
        processRemark: '',
        appType: this.stgTypeNumber
      }
    },
    clearTable(type) {
      this.temp.printerList.splice(0, this.temp.printerList.length)
    },
    clearTable2() {
      this.temp.processList.splice(0, this.temp.processList.length)
    },
    handleCreate() {
      this.resetTemp()
      this.waterType = 1
      this.temp = this.defaultTemp
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
      this.$refs.waterMarkTree && this.$refs.waterMarkTree.clearFilter()
      this.$nextTick(() => {
        this.$refs['softList'] && this.$refs['softList'].execRowDataApi()
        this.webList() && this.webList().execRowDataApi()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp.processList = row.processList
      this.temp.urlList = row.urlList
      this.waterType = row.waterType
      this.activeDesktopWatermark = row.waterType === 1
      // Q3版本 新增进程水印和WEB页面水印同时生效 原策略仅单选 所以会出现waterType == 0的情况，仅需要进程水印 默认值需要改 5（不限制任何WEB页面）
      if (this.waterType === 0 && this.stgTypeNumber === 44) { this.waterType = 5 }
      this.$refs['stgDlg'].show(row, this.formable)
      this.$refs.waterMarkTree && this.$refs.waterMarkTree.clearFilter()
      this.$nextTick(() => {
        this.$refs['softList'] && this.$refs['softList'].execRowDataApi()
        this.webList() && this.webList().execRowDataApi()
      })
    },
    handleCreate2(status) {
      this.resetTemp2()
      this.dialogStatus2 = status
      this.dialogFormVisible2 = true
      this.submittingProcess = false
      this.$nextTick(() => {
        this.$refs['dataForm2'].clearValidate()
      })
    },
    handleUpdate2(row) {
      if (row == null) {
        const selectDatas = this.$refs.commonSoft.getSelectedDatas()
        this.temp2 = Object.assign({}, selectDatas.length > 0 ? selectDatas[0] : {})
        this.temp2.appType = this.stgTypeNumber
      } else {
        this.temp2 = Object.assign({}, row) // copy obj
      }
      this.dialogStatus2 = 'update'
      this.updateProcessName = this.temp2.processName
      this.dialogFormVisible2 = true
      this.submittingProcess = false
    },
    handleCreateCommonSoft() {
      this.dialogFormVisible3 = true
      this.loadCustomSoftList()
      this.$nextTick(() => {
        this.tabClickFunc2()
        this.$refs.commonSoft.clearSelection()
      })
    },
    handleCreateCommonURL() {
      this.dialogFormVisible5 = true
      this.$nextTick(() => {
        this.commonUrl() && this.commonUrl().execRowDataApi()
      })
    },
    handleImport() {},
    handleExport() {},
    beforeUpload(file) {
      this.temp2.processName = file.name
      return false // 屏蔽了action的默认上传
    },
    formatRowData(rowData) {
      rowData.waterType = this.getRealWaterType()
    },
    formatFormData(formData) {
      formData.waterType = this.getRealWaterType()
      if (formData.processList) {
        formData.processList.forEach(item => {
          delete item.createTime
          delete item.modifyTime
        })
      }
      if (formData.urlList) {
        formData.urlList.forEach(item => {
          delete item.createTime
          delete item.modifyTime
        })
      }
      formData.waterMark.waterType = formData.waterType
      formData.stgTypeNumber = this.stgTypeNumber
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    createData2() {
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          const datas = this.$refs.softList.rowData
          const duplicate = datas.some(item => {
            return item.processName === this.temp2.processName
          })
          if (duplicate) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.waterMark_Msg46'),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.submittingProcess = true
          savePrintApp(this.temp2).then(respond => {
            this.dialogFormVisible2 = false
            if (this.dialogStatus2 === 'create') {
              this.temp.processList.push(respond.data)
              this.$refs.softList.execRowDataApi()
            }
            this.loadCustomSoftList()
            this.submittingProcess = false
          }).catch(() => {
            this.submittingProcess = false
          })
        }
      })
    },
    createData3() {
      //  将windows，mac，自定义中选中的数据合并，同时过滤掉重复进程名称的数据
      const selectDatas = []
      const existProcessName = []
      for (const key in this.selected) {
        this.selected[key].forEach(item => {
          if (!existProcessName.includes(item.processName)) {
            selectDatas.push(item)
            existProcessName.push(item.processName)
          }
        })
      }
      // const selectDatas = this.$refs.commonSoft.getSelectedDatas()
      if (selectDatas.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.waterMark_Msg47'),
          type: 'error',
          duration: 2000
        })
        return
      }
      const existList = this.$refs.softList.rowData // 已添加的进程
      const addDatas = [] // 勾选常用的进程中，过滤掉已添加的进程后，剩余的进程
      selectDatas.forEach(item1 => {
        item1.appType = this.stgTypeNumber
        const index = existList.findIndex(item2 => {
          if (item1.processName === item2.processName) {
            return true
          }
        })
        if (index === -1) {
          addDatas.push(item1)
        }
      })
      if (addDatas.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.waterMark_Msg48'),
          type: 'error',
          duration: 2000
        })
        return
      }
      // 验证通过后，调用后台方法，保存
      savePrintAppBatch(addDatas).then(res => {
        this.clearData3()
        res.data.forEach(item => {
          if (this.temp.processList.findIndex(n => n.id === item.id) === -1) {
            const index = addDatas.findIndex(data => { return item.processName === data.processName })
            if (index > -1) {
              item.processRemark = addDatas[index].processRemark
            }
            this.temp.processList.push(item)
          }
        })
        this.$refs.softList.execRowDataApi()
      })
    },
    updateData2() {
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          const datas = this.$refs.softList.rowData
          const duplicate = datas.some(item => {
            return item.processName === this.temp2.processName && item.id != this.temp2.id
          })
          if (duplicate) {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.waterMark_Msg46'),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.submittingProcess = true
          editPrintApp(this.temp2).then(respond => {
            this.updateProcessName = ''
            this.dialogFormVisible2 = false
            //  修改备注
            this.temp.processList.forEach(item => {
              if (item.id === this.temp2.id) {
                item.processName = this.temp2.processName
                item.processRemark = this.temp2.processRemark
                item.appType = this.temp2.appType
              }
            })
            this.$refs.softList.execRowDataApi()
            this.loadCustomSoftList()
            this.submittingProcess = false
          }).catch(() => {
            this.submittingProcess = false
          })
        }
      })
    },
    handleDelete2() {
      // this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
      const toDeleteIds = this.$refs['softList'].getSelectedIds()
      const processList = this.temp.processList
      this.temp.processList = processList.filter(item => toDeleteIds.indexOf(item.id) == -1)
      this.$refs['softList'].execRowDataApi()
      // }).catch(() => {})
    },
    handleDelete3() {
      this.$confirmBox(this.$t('pages.delControlProcess'), this.$t('text.prompt')).then(() => {
        const selectDatas = this.$refs.commonSoft.getSelectedDatas()
        removePrintAppBatch({ ids: selectDatas.map(t => t.id).join(',') }).then(respond => {
          this.loadCustomSoftList()
          this.$refs.softList.execRowDataApi()
        })
      }).catch(() => {})
    },
    waterTypeChange() {
      // this.$refs['stgDlg'] && this.$refs['stgDlg'].validFormData()
    },
    afterLoad() {
      // const stgDlg = this.$refs['stgDlg']
      // if (stgDlg && stgDlg.temp.name) {
      //   stgDlg.validFormData()
      // }
    },
    clearData3() {
      this.dialogFormVisible3 = false;
      this.selected = { winTab: [], macTab: [], customTab: [] };
    },
    dialogStatusClose() {
      this.updateProcessName = ''
      this.dialogFormVisible2 = false
    },
    handleImportApp() {
      this.$refs['appSelectDlg'].show()
    },
    handleAddAppAfter(selectedDatas) {
      const datas = []
      selectedDatas.forEach(item => {
        datas.push({ processName: item.processName, appType: this.stgTypeNumber, processRemark: item.fileDescription || '', commonName: item.productName || '' })
      })
      const processNames = [];
      //  获取导入的进程名称
      (datas || []).forEach(item => {
        processNames.push(item.processName)
      });

      //  未添加到内置表的进程数组
      let notAddDatas = datas;
      //  查询受控进程是否已存在
      getPrintAppByNames(this.stgTypeNumber, processNames.join(',')).then(res => {
        if (res.data.length > 0) {
          notAddDatas = datas.filter(item => { return res.data.findIndex(n => n.processName === item.processName) === -1 })
        }
        //  若所有进程都已存在
        if (notAddDatas.length === 0) {
          //  过滤掉重复的数据
          (res.data || []).forEach(i => this.temp.processList.findIndex(process => { return process.id === i.id }) === -1 && this.temp.processList.push(i))
          this.$refs.softList.execRowDataApi()
          this.loadCustomSoftList()
        } else {
          //  保存未添加的受控进程
          savePrintAppBatch(notAddDatas).then(s => {
            //  根据id查询所有受控进程
            getPrintAppByNames(this.stgTypeNumber, processNames.join(',')).then(t => {
              //  过滤掉重复的进程
              (t.data || []).forEach(i => this.temp.processList.findIndex(process => { return process.id === i.id }) === -1 && this.temp.processList.push(i))
              this.$refs.softList.execRowDataApi()
            })
            this.loadCustomSoftList()
          })
        }
      })
    },
    resetTemp4() {
      this.temp4 = {
        id: undefined,
        urlKeyword: '',
        urlRemark: ''
      }
    },
    handleCreate4(status) {
      this.resetTemp4()
      this.dialogStatus4 = status
      this.dialogFormVisible4 = true
      this.$nextTick(() => {
        this.$refs.dataForm4.clearValidate()
      })
    },
    handleDelete4() {
      const toDeleteIds = this.webList().getSelectedIds()
      const urlList = this.temp.urlList
      this.temp.urlList = urlList.filter(item => toDeleteIds.indexOf(item.id) == -1)
      this.webList().execRowDataApi()
    },
    handleUpdate5() {
      const data = this.commonUrl().getSelectedDatas()[0];
      if (data) {
        this.resetTemp4()
        this.dialogStatus4 = 'updateCustom'
        this.temp4 = JSON.parse(JSON.stringify(data))
        this.dialogFormVisible4 = true
        this.$nextTick(() => {
          this.$refs.dataForm4.clearValidate()
        })
      }
    },
    handleDelete5() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const keys = this.commonUrl().getSelectedIds()
        if (keys) {
          const toDeleteIds = keys.join(',')
          deleteWatermarkUrl({ ids: toDeleteIds }).then(res => {
            this.commonUrl().deleteRowData(toDeleteIds)
            this.webList().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {})
        }
      }).catch(reason => {})
    },
    createData4() {
      this.$refs.dataForm4.validate((valid) => {
        if (valid) {
          this.submitting4 = true
          saveWatermarkUrl(this.temp4).then(res => {
            if (res.data) {
              if (this.dialogStatus4 === 'create') {
                const index = this.temp.urlList.findIndex(data => data.id == res.data.id)
                if (index < 0) {
                  this.temp.urlList.push(res.data)
                }
                this.webList().execRowDataApi()
              } else {
                this.commonUrl() && this.commonUrl().execRowDataApi()
              }
            }
            this.dialogFormVisible4 = false
            this.submitting4 = false
          }).catch(reason => {
            this.submitting4 = false
          })
        }
      })
    },
    updateData4() {
      this.$refs.dataForm4.validate((valid) => {
        if (valid) {
          this.submitting4 = true
          updateWatermarkUrl(this.temp4).then(res => {
            if (res.data) {
              if (this.dialogStatus4 === 'updateCustom') {
                this.commonUrl().execRowDataApi()
              }
              this.webList().execRowDataApi()
            }
            this.dialogFormVisible4 = false
            this.submitting4 = false
          }).catch(reason => {
            this.submitting4 = false
          })
        }
      })
    },
    selectionChangeEnd4(rows) {
      this.deleteable4 = rows && rows.length > 0
    },
    rowDataApi5(option) {
      const data = Object.assign({}, this.urlQuery, option)
      return getWatermarkUrlPage(data)
    },
    // 导入常用网址信息至策略
    importUrlToStg() {
      const selectDatas = this.commonUrl().getSelectedDatas()
      if (selectDatas.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.waterMark_Msg51'),
          type: 'error',
          duration: 2000
        })
        return
      }
      if (selectDatas.length > 0) {
        selectDatas.forEach(data => {
          if ((this.temp.urlList || []).findIndex(item => item.id == data.id) < 0) {
            this.temp.urlList.push(data)
          }
        })
        this.$nextTick(() => {
          this.webList().execRowDataApi()
        })
      }
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.insertSuccess'),
        type: 'success',
        duration: 2000
      })
      this.dialogFormVisible5 = false
    },
    getRealWaterType() {
      // 如果组件作用与屏幕水印设置策略，且开启桌面水印时， waterType == 1
      return this.stgTypeNumber === 44 && this.activeDesktopWatermark ? 1 : this.waterType
    },
    getGroupList() {
      return getTreeNode().then(res => {
        this.groupTreeList = res.data
      })
    },
    loadGroupTree() {
      return new Promise(resolve => {
        resolve({
          code: 20000,
          data: this.groupTreeList || []
        })
      })
    },
    //  导入方法的row-data-api
    getInfoList(option) {
      const searchQuery = Object.assign({}, this.importTableQuery, option)
      return getUrlList(searchQuery)
    },
    // 导入对话框中 url分组格式化方法
    groupFormatter(row, data) {
      row.groupName = this.getGroupNameByDataId(this.groupTreeList, data)
      return row.groupName
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    // 网址验证
    addressValidator(rule, value, callback) {
      getUrlByAdress({ url: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.urlTemp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    createUrlData() {
      this.urlDlgSubmit = true
      this.$refs['urlDataForm'].validate((valid) => {
        if (valid) {
          this.urlTemp.groupName = findNodeLabel(this.groupTreeList, this.urlTemp.groupId, 'dataId')
          createUrl(this.urlTemp).then(respond => {
            this.urlDlgSubmit = false
            this.$refs['urlImportTable'].justRefreshTableData()
            this.urlDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.urlDlgSubmit = false
          })
        } else {
          this.urlDlgSubmit = false
        }
      })
    },
    updateUrlData() {
      this.urlDlgSubmit = true
      this.$refs['urlDataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.urlTemp)
          tempData.groupName_new = findNodeLabel(this.groupTreeList, this.temp.groupId, 'dataId')
          updateUrl(tempData).then(respond => {
            this.$refs['urlImportTable'].justRefreshTableData()
            this.urlDlgSubmit = false
            this.urlDialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.urlDlgSubmit = false
          })
        } else {
          this.urlDlgSubmit = false
        }
      })
    },
    // 导入网址信息库对话框新增网址
    handleCreateElg(selectedGroupId) {
      this.urlDialogStatus = 'createUrl'
      this.urlTemp = Object.assign({}, { id: undefined, name: '', remark: '', address: '', groupId: '' })
      this.urlTemp.groupId = selectedGroupId
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
      this.urlDialogFormVisible = true
    },
    // 导入网址信息库对话框修改网址
    handleUpdateImport(row) {
      this.urlDialogStatus = 'updateUrl'
      this.urlTemp = Object.assign({}, row)
      this.urlTemp.groupId = this.urlTemp.groupId ? this.urlTemp.groupId + '' : '';
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
      this.urlDialogFormVisible = true
    },
    submitImport(ids) {
      if (ids && ids.length > 0) {
        getByIds({ ids: ids.join(',') }).then(res => {
          const list = res.data
          if (list) {
            const insertList = []
            list.forEach(data => {
              if (this.temp.urlList.findIndex(urlData => urlData.urlKeyword == data.address) < 0) {
                insertList.push(data)
              }
            })
            if (insertList.length > 0) {
              fromLibToWatermarkUrl(insertList).then(res => {
                const insertDatas = res.data;
                if (insertDatas) {
                  this.temp.urlList.push(...insertDatas)
                }
                this.$nextTick(() => {
                  this.webList() && this.webList().execRowDataApi()
                })
              })
            } else {
              this.$nextTick(() => {
                this.webList() && this.webList().execRowDataApi()
              })
            }
          }
        })
      }
    },
    // 创建导入网址信息库的对话框
    handleCreateImportUrl() {
      this.getGroupList().then(() => {
        this.$nextTick(() => {
          this.urlImportTable().show()
        })
      })
    },
    handleSearchFun() {
      this.webList().execRowDataApi({ search: true })
    },
    desktopWatermarkChange(val) {
      this.activeDesktopWatermark = val
      if (val && this.activeTab != 'first') {
        this.activeTab = 'first'
      }
      if (!val && this.waterType == 1) {
        this.waterType = 5
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .el-dialog__body{
    .el-tabs{
      border: 0;
    }
    .el-tabs__item{
      color: #666;
      &.is-active {
        color: #409EFF;
      }
    }
    .el-main{
      padding: 10px 20px 0 20px;
    }
    .button-group {
      margin: 4px 4px 0;
      >>>.el-button {
        margin: 0 3px 0 0;
      }
    }
  }
</style>
