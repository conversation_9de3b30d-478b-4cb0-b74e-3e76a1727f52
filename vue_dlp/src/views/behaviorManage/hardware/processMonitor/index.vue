<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;">{{ msgFormatter(props.detail) }}</span>
          </div>
        </template>
      </grid-table>
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 740px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit />
        </FormItem>
        <FormItem v-if="treeable" :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
        <LocalZoomIn parent-tag="el-dialog">
          <data-editor
            ref="dataEditor"
            :formable="formable"
            :popover-width="650"
            placement="bottom-end"
            :updateable="processEditable"
            :deletable="processDeleteable"
            :add-func="createProcess"
            :update-func="updateProcess"
            :delete-func="deleteProcess"
            :cancel-func="cancelProcess"
            :before-update="beforeUpdateProcess"
            :before-add="beforeAddProcess"
          >
            <Form ref="processForm" :model="processTemp" :rules="processTemprules" label-position="right" label-width="105px" style="width: 580px; margin: 0 auto;">
              <FormItem v-if="operationType === 'update'" :label="$t('table.processName')" prop="processName">
                <el-upload name="processFile" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getFileName" style="display: inline-block;">
                  <el-button type="primary" icon="el-icon-upload" style="padding: 7px 11px; margin: 2px 0;"></el-button>
                </el-upload>
                <el-button type="primary" size="mini" style="margin-bottom: 0px;" @click="showUpdateAppSelectDlg">
                  {{ $t('pages.software_Msg16') }}
                </el-button>
                <el-input v-model="processTemp.processName" maxlength="255" style="margin-top: 5px;" :placeholder="processTemp.operation.includes(1) ? '' : $t('pages.process_Msg')"/>
              </FormItem>

              <FormItem v-if="operationType === 'create'" prop="processNames" :label="$t('table.processName')" >
                <div>
                  <el-upload name="processFile" action="1111" accept=".exe" :limit="1" :show-file-list="false" :before-upload="getBatchFileName" style="display: inline-block;">
                    <el-button type="primary" icon="el-icon-upload" style="padding: 7px 11px; margin: 2px 0;"></el-button>
                  </el-upload>
                  <el-button type="primary" size="mini" style="margin-bottom: 0px;" @click="showAppSelectDlg">
                    {{ $t('pages.software_Msg16') }}
                  </el-button>
                  <el-button size="mini" style="margin: 2px 0 0;" @click="handleClear">
                    {{ $t('button.clear') }}
                  </el-button>
                </div>
                <tag
                  v-model="processTemp.processNames"
                  :border="true"
                  :input-width="215"
                  :overflow-able="true"
                  max-height="150px"
                  :placeholder="processTemp.operation.includes(1) ? '' : $t('pages.process_Msg')"
                  :list="processTemp.processNames"
                  :disabled="!formable"
                  style="margin-top: 5px;"
                  @tagChange="tagChange"
                />
              </FormItem>
              <FormItem :label="$t('table.monitorOpenConditions')" prop="operation">
                <el-checkbox-group v-model="processTemp.operation" class="checkbox-group">
                  <el-checkbox style="width: 120px" :label="2">{{ $t('pages.programStart') }}</el-checkbox>
                  <el-checkbox v-if="showable" :label="1">{{ $t('pages.process_Msg2') }}</el-checkbox>
                </el-checkbox-group>
              </FormItem>
              <FormItem :label="$t('table.monitoringMode')" prop="action">
                <el-checkbox-group v-model="processTemp.action" class="checkbox-group">
                  <el-checkbox style="width: 120px" :label="1">{{ $t('pages.screenshot') }}</el-checkbox>
                  <el-checkbox :label="2">{{ $t('pages.recordingScreen') }}</el-checkbox>
                </el-checkbox-group>
              </FormItem>
            </Form>
          </data-editor>
          <grid-table
            ref="processList"
            auto-height
            :multi-select="true"
            :show-pager="false"
            :col-model="processColModel"
            :row-datas="processRowData"
            @selectionChangeEnd="processSelectionChange"
          />
        </LocalZoomIn>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <FormItem label-width="15px">
          <div>{{ $t('pages.processMonitorExecuteMsg') }}</div>
        </FormItem>
        <el-row>
          <el-col :span="7">
            <FormItem :label="$t('pages.screenCaptureTimes')" prop="capTimes" label-width="100px">
              <el-input-number v-model="temp.capTimes" :controls="false" :step="1" step-strictly :disabled="!formable" style="width: 70px;" :min="1" :max="10"></el-input-number>
            </FormItem>
          </el-col>
          <el-col :span="9">
            <FormItem :label="$t('pages.intervalTime')" prop="spanTime">
              <el-input-number v-model="temp.spanTime" :controls="false" :disabled="!formable" style="width: 70px;" :min="0.2" :max="600"></el-input-number><label style="margin-left: 5px">{{ $t('text.secondLower') }}</label>
            </FormItem>
          </el-col>
          <el-col :span="8">
            <FormItem :label="$t('pages.delayTime')" prop="delayTime" label-width="80px">
              <el-input-number v-model="temp.delayTime" :controls="false" :disabled="!formable" style="width: 70px;" :min="0.1" :max="100"></el-input-number><label style="margin-left: 5px">{{ $t('text.secondLower') }}</label>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.screenRecordingDuration')" prop="recordTime" label-width="100px">
          <el-input-number v-model="temp.recordTime" :controls="false" :step="1" step-strictly :disabled="!formable" style="width: 70px;" :min="0" :max="600"></el-input-number>
          <label>{{ $t('text.secondLower') }}</label>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.screenIntervalTip', { second: recordInterval }) }}
              <i18n v-permission="'A68'" path="pages.clickTip">
                <a slot="link" style="color:#148ff1;" @click="()=>{this.$router.push('/system/configManage/globalConfig')}">{{ $t('route.globalConfig') }}</a>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.process_Msg3')"
      :visible.sync="sensitiveFileFormVisible"
      width="800px"
      @close="closeSensitiveFileDialog"
      @dragDialog="handleDrag"
    >
      <data-editor
        v-if="formable"
        ref="dataEditor"
        :formable="formable"
        :popover-width="650"
        :updateable="sensitiveFileEditable"
        :deletable="sensitiveFileDeleteable"
        :add-func="createSensitiveFile"
        :update-func="updateSensitiveFile"
        :delete-func="deleteSensitiveFile"
        :cancel-func="cancelSensitiveFile"
        :before-update="beforeUpdateSensitiveFile"
      >
        <Form ref="sensitiveFileForm" :model="sensitiveFileOpTemp" :rules="sensitiveFileOpTemprules" label-position="right" label-width="90px" style="width: 580px; margin: 0 auto;">
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('pages.programControlled')" prop="processName">
                <el-input v-model="sensitiveFileOpTemp.processName" disabled></el-input>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.path')" :tooltip-content="$t('pages.process_Msg4')" prop="path">
                <el-input v-model="sensitiveFileOpTemp.path" maxlength="800" :placeholder="$t('pages.enterPath')"></el-input>
              </FormItem>
            </el-col>
            <el-col :span="24">
              <FormItem :label="$t('pages.process_Msg5')" prop="fileExt">
                <el-input v-model="sensitiveFileOpTemp.fileExt" class="input-with-button" type="textarea" rows="2" maxlength="1000" :placeholder="$t('pages.process_Msg6')" @keydown.native="listenKey($event)"></el-input>
                <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
                  <el-button type="primary" size="mini" @click="handleFileSuffixImport()">
                    <svg-icon icon-class="import" />
                  </el-button>
                </el-tooltip>
              </FormItem>
            </el-col>
          </el-row>
        </Form>
      </data-editor>
      <grid-table
        ref="sensitiveFileOpList"
        :height="260"
        :multi-select="true"
        :show-pager="false"
        :col-model="sensitiveFileOpColModel"
        :row-datas="sensitiveFileRowDataTemp"
        @selectionChangeEnd="sensitiveFileOpChange"
      />
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :loading="submitting" type="primary" @click="addSensitiveFiles()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="sensitiveFileFormVisible=false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importProcessMonitorStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />

    <!-- 应用程序库 -->
    <app-select-dlg ref="appLib" :append-to-body="true" @select="appendFile"/>
    <app-select-dlg ref="updateAppLib" :append-to-body="true" :multiple="false" @select="appendFile"/>
  </div>
</template>

<script>
import {
  createStrategy,
  deleteStrategy,
  getStrategyByName,
  getStrategyList,
  updateStrategy
} from '@/api/behaviorManage/hardware/processMonitor'
import {
  buttonFormatter,
  deepMerge,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import { getConfigByKey } from '@/api/system/configManage/globalConfig'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import { getRecordTimeConfig } from '@/api/system/configManage/globalConfig';

export default {
  name: 'ProcessMonitor',
  components: { FileSuffixLibImport, ImportStg, AppSelectDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 129,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', fixed: true, iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'stgMessage', width: '200', ellipsis: false, type: 'popover', originData: true, formatter: this.msgFormatter },
        // { label: 'programName', width: '200', formatter: this.processNameFormatter },
        // { label: '例外目录', width: '100', formatter: this.exceptionDirFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'operate', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      sensitiveFileOpColModel: [
        { prop: 'processName', label: 'programName', width: '100' },
        { prop: 'path', label: 'path', width: '80' },
        { prop: 'fileExt', label: 'fileSuffixList', width: '80' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      processEditable: false,
      processDeleteable: false,
      sensitiveFileEditable: false,
      sensitiveFileDeleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        delayTime: 1,
        capTimes: 3,
        spanTime: 2,
        recordInterval: 0,
        recordTime: 10,
        processOpAction: [],
        sensitiveFileOp: [],
        entityType: undefined,
        entityId: undefined
      },
      processTemp: {},
      defaultProcessTemp: {
        id: undefined,
        processName: '',
        operation: [2],
        action: [1],
        processNames: []
      },
      sensitiveFileOpTemp: {},
      sensitiveFileOpDefaultTemp: {
        id: undefined,
        processName: '',
        path: '',
        fileExt: ''
      },
      dialogFormVisible: false,
      sensitiveFileFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.processMonitorStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.processMonitorStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        delayTime: [
          { required: true, message: this.$t('pages.process_Msg7'), trigger: 'blur' },
          { validator: this.delayTimeValidator, trigger: 'blur' }
        ],
        capTimes: [
          { required: true, message: this.$t('pages.process_Msg8'), trigger: 'blur' },
          { validator: this.capTimesValidator, trigger: 'blur' }
        ],
        spanTime: [
          { required: true, message: this.$t('pages.process_Msg9'), trigger: 'blur' },
          { validator: this.spanTimeValidator, trigger: 'blur' }
        ],
        recordInterval: [
          { required: true, message: this.$t('pages.process_Msg16'), trigger: 'blur' },
          { validator: this.recordIntervalValidator, trigger: 'blur' }
        ],
        recordTime: [
          { required: true, message: this.$t('pages.process_Msg17'), trigger: 'blur' },
          { validator: this.recordTimeValidator, trigger: 'blur' }
        ]
      },
      processTemprules: {
        processName: [{ required: true, validator: this.processNameValidator, trigger: 'blur' }],
        operation: [{ required: true, validator: this.operationValidator, trigger: 'change' }],
        action: [{ required: true, validator: this.actionValidator, trigger: 'change' }],
        processNames: [{ required: true, validator: this.processNamesValidator, trigger: 'blur' }]
      },
      sensitiveFileOpTemprules: {
        processName: [
          { required: true, message: this.$t('pages.appGroup_text22'), trigger: 'blur' }
        ],
        path: [
          { required: true, message: this.$t('pages.process_Msg11'), trigger: 'blur' },
          { validator: this.sameValueValidator, trigger: 'blur' }
        ],
        fileExt: [
          { required: true, message: this.$t('pages.process_Msg12'), trigger: 'blur' },
          { validator: this.sameValueValidator, trigger: 'blur' }
        ]
      },
      multipleSelection: [], // 选中行数组集合
      submitting: false,
      processRowData: [],
      sensitiveFileRowData: [],
      sensitiveFileRowDataCahe: [],
      sensitiveFileRowDataTemp: [],
      operationMap: { 1: this.$t('pages.process_Msg2'), 2: this.$t('pages.programStart') },
      actionMap: { 1: this.$t('pages.screenshot'), 2: this.$t('pages.recordingScreen') },
      showable: true,
      recordInterval: 0,

      //  批量新增
      operationType: '', //  操作类型： create, update
      processNamesMessage: '',
      currentRecordInterval: 0    //  当前【全局配置】中的时间间隔
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    },
    processColModel() {
      return [
        { prop: 'processName', label: 'programName', width: '100', sort: true },
        { prop: 'operation', label: 'monitorOpenConditions', width: '80', sort: true, formatter: this.operationFormatter, ImportStg },
        { prop: 'action', label: 'monitoringMode', width: '60', sort: true, formatter: this.actionFormatter },
        { label: 'sensitiveFileOperate', type: 'button', fixedWidth: '140', fixed: 'right', hidden: !this.showable,
          buttons: [
            { label: 'config', formatter: this.btnFormatter, click: this.sensitiveConfig }
          ]
        }
      ]
    },
    //  校验录屏时长是否小于录屏间隔时间
    validRecordTime() {
      return this.temp.recordTime < this.currentRecordInterval;
    }
  },
  created() {
    this.resetTemp()
    this.listModule()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    async listModule() {
      await existSaleModule(80).then(resp => {
        this.showable = resp.data
      })
    },
    getConfigByKey() {
      getConfigByKey({ key: 'recordInterval' }).then(resp => {
        if (resp.data) {
          this.recordInterval = resp.data.value
        }
      })
    },
    listenKey(e) {
      if (e.keyCode === 13) {
        e.preventDefault() // 阻止浏览器默认换行操作
        return false
      }
    },
    getFileName(file) {
      this.processTemp.processName = file.name
      this.clearProcessFormValidate()
      return false // 屏蔽了action的默认上传
    },
    getBatchFileName(file) {
      let list = [...this.processTemp.processNames]
      list.push(file.name)
      list = this.verifyExeNames(list)
      this.processTemp.processNames = list
      // this.$refs['processForm'].clearValidate()
      return false // 屏蔽了action的默认上传
    },
    clearProcessFormValidate() {
      this.$refs['processForm'] && this.$refs['processForm'].clearValidate()
    },
    // 清除敏感文件监控表单的验证信息
    clearSensitiveFileFormValidate(prop) {
      this.$refs['sensitiveFileForm'] && this.$refs['sensitiveFileForm'].clearValidate(prop)
    },
    beforeUpload(file) {
      this.processTemp.exeName = file.name
      return false // 屏蔽了action的默认上传
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    processTable() {
      return this.$refs['processList']
    },
    sensitiveFileOpTable() {
      return this.$refs['sensitiveFileOpList']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    processSelectionChange(rowDatas) {
      this.processDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.processEditable = true
      } else {
        this.processEditable = false
        this.cancelProcess()
      }
    },
    sensitiveFileOpChange(rowDatas) {
      this.sensitiveFileDeleteable = rowDatas.length > 0
      this.sensitiveFileEditable = rowDatas.length == 1
      if (!this.sensitiveFileEditable) {
        const processName = this.sensitiveFileOpTemp.processName
        this.resetSensitiveFileOpTemp()
        this.sensitiveFileOpTemp.processName = processName
      }
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.processRowData.splice(0, this.processRowData.length)
      this.sensitiveFileRowData.splice(0, this.sensitiveFileRowData.length)
      this.temp = Object.assign({}, this.defaultTemp)
      this.resetProcessTemp()
      this.resetSensitiveFileOpTemp()
      this.clearProcessFormValidate()
      this.submitting = false
    },
    resetProcessTemp() {
      this.configBtnMode = 'add'
      this.processTemp = Object.assign({}, this.defaultProcessTemp)
      this.operationType = ''
    },
    resetSensitiveFileOpTemp() {
      this.sensitiveFileOpTemp = Object.assign({}, this.sensitiveFileOpDefaultTemp)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    async handleCreate() {
      this.resetTemp()
      this.getConfigByKey()
      await this.getMinRecordTimeInterval()
      //  当默认值小于【全局配置】中的录屏间隔时间时，使用【全局配置】中的录屏间隔时间
      if (this.temp.recordTime < this.currentRecordInterval) {
        this.temp.recordTime = this.currentRecordInterval;
      }
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.getMinRecordTimeInterval()
      this.getConfigByKey()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.delayTime = this.temp.delayTime / 10 // 显示时：原单位百毫秒 转 秒，修改是 秒 转 百毫秒
      this.temp.spanTime = this.temp.spanTime / 10 // 显示时：原单位百毫秒 转 秒，修改是 秒 转 百毫秒
      if (row.recordTime === undefined) {
        this.temp.recordTime = 10
      }
      const that = this
      const processMap = {}
      this.temp.processOpAction.forEach(function(data, index) {
        if (!processMap[data.processName]) processMap[data.processName] = {}
        const existObj = processMap[data.processName][data.operation]
        if (!existObj) {
          processMap[data.processName][data.operation] = Object.assign({ id: index }, data, { operation: [data.operation], action: [data.action] })
        } else {
          existObj.action.push(data.action)
        }
      })
      for (const procssName in processMap) {
        const map = {}
        for (const operation in processMap[procssName]) {
          const obj = processMap[procssName][operation]
          if (!map[obj.action.sort().join()]) {
            map[obj.action.sort().join()] = obj
          } else {
            map[obj.action.sort().join()].operation.splice(0, 0, ...obj.operation)
          }
        }
        that.processRowData.splice(0, 0, ...Object.values(map))
      }
      this.temp.sensitiveFileOp.forEach(function(data, index) {
        that.sensitiveFileRowData.push(Object.assign({ id: index }, data))
      })
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.cancelProcess()
        this.cancelSensitiveFile()
      })
    },
    sensitiveConfig: function(row) {
      this.clearSensitiveFileFormValidate()
      this.resetSensitiveFileOpTemp()
      this.sensitiveFileOpTemp.processName = row.processName
      this.sensitiveFileRowDataCahe = JSON.parse(JSON.stringify(this.sensitiveFileRowData))
      this.sensitiveFileRowDataTemp = this.sensitiveFileRowData.filter(item => item.processName === row.processName)
      this.sensitiveFileFormVisible = true
    },
    formatDataFormParam() {
      this.temp.processOpAction = []
      const processOperations = {}
      this.processTable().getDatas().forEach(rowData => {
        const item = deepMerge({}, rowData)
        delete item.id
        const operations = item.operation
        if (Array.isArray(item.action)) {
          item.action.forEach(ac => {
            item.action = ac
            operations.forEach(op => {
              item.operation = op
              this.temp.processOpAction.push(deepMerge({}, item))
            })
          })
        } else {
          this.temp.processOpAction.push(item)
        }
        if (!processOperations[item.processName]) {
          processOperations[item.processName] = []
        }
        if (operations) {
          processOperations[item.processName].push(...operations)
        }
      })
      this.sensitiveFileRowData.filter(item => {
        const operations = processOperations[item.processName]
        return operations && operations.indexOf(1) > -1
      })
      // this.temp.processOpAction = this.processTable().getDatas()
      // this.temp.processOpAction.forEach(item => delete item.id)
      this.temp.sensitiveFileOp = this.sensitiveFileRowData
      this.temp.sensitiveFileOp.forEach(item => delete item.id)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }

      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatDataFormParam()
          var validValue = this.validOperation()
          if (validValue === '') {
            this.cancelProcess()
            const data = Object.assign({}, this.temp);
            data.delayTime = this.temp.delayTime * 10 // 显示时：原单位百毫秒 转 秒，修改是 秒 转 百毫秒
            data.spanTime = this.temp.spanTime * 10 // 显示时：原单位百毫秒 转 秒，修改是 秒 转 百毫秒
            data.recordInterval = this.recordInterval
            createStrategy(data).then(respond => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(reason => {
              this.submitting = false
            })
          } else {
            this.$message({
              message: validValue + this.$t('pages.process_Msg13'),
              type: 'error'
            })
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }

      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatDataFormParam()
          var validValue = this.validOperation()
          if (validValue === '') {
            const tempData = Object.assign({}, this.temp)
            tempData.delayTime = tempData.delayTime * 10 // 显示时：原单位百毫秒 转 秒，修改是 秒 转 百毫秒
            tempData.spanTime = tempData.spanTime * 10 // 显示时：原单位百毫秒 转 秒，修改是 秒 转 百毫秒
            tempData.recordInterval = this.recordInterval
            updateStrategy(tempData).then(respond => {
              this.cancelProcess()
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(reason => {
              this.submitting = false
            })
          } else {
            this.$message({
              message: validValue + this.$t('pages.process_Msg13'),
              type: 'error'
            })
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    createProcess() {
      let validate
      this.$refs['processForm'].validate((valid) => {
        if (valid) {
          const rowData = deepMerge({}, this.processTemp)
          //  如果为批量添加
          if (this.operationType === 'create') {
            rowData.processNames = rowData.processNames || []
            for (let i = 0; i < rowData.processNames.length; i++) {
              const data = JSON.parse(JSON.stringify(rowData))
              data.processName = this.processTemp.processNames[i]
              data.id = new Date().getTime() + i
              data.processNames = undefined
              this.processRowData.unshift(data)
            }
          } else {
            rowData.id = new Date().getTime()
            this.processRowData.unshift(rowData)
          }
          this.resetProcessTemp()
          this.cancelProcess()
          validate = valid
        }
      })
      return validate
    },
    createSensitiveFile() {
      let validate
      this.configBtnMode = 'add'
      this.$refs['sensitiveFileForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.sensitiveFileOpTemp)
          rowData.id = new Date().getTime()
          this.sensitiveFileRowData.unshift(rowData)
          this.cancelSensitiveFile()
          this.sensitiveFileRowDataTemp = this.sensitiveFileRowData.filter(item => item.processName === rowData.processName)
          validate = valid
        }
      })
      return validate
    },
    beforeUpdateProcess() {
      this.configBtnMode = 'update'
      this.processTemp = Object.assign({}, this.processTable().getSelectedDatas()[0])
      this.processTemp.processNames = undefined
      this.operationType = 'update'
    },
    beforeUpdateSensitiveFile() {
      Object.assign(this.sensitiveFileOpTemp, this.sensitiveFileOpTable().getSelectedDatas()[0])
      this.clearSensitiveFileFormValidate()
    },
    beforeAddProcess() {
      this.operationType = 'create'
      this.processTemp.processNames = []
    },
    updateProcess() {
      let validate
      this.$refs['processForm'].validate((valid) => {
        if (valid) {
          const rowData = deepMerge({}, this.processTemp)
          for (let i = 0, size = this.processRowData.length; i < size; i++) {
            const data = this.processRowData[i]
            if (rowData.id === data.id) {
              this.processRowData.splice(i, 1, rowData)
              break
            }
          }
          this.resetProcessTemp()
          this.cancelProcess()
          validate = valid
        }
      })
      return validate
    },
    updateSensitiveFile() {
      let validate
      this.configBtnMode = 'update'
      this.$refs['sensitiveFileForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.sensitiveFileOpTemp)
          for (let i = 0, size = this.sensitiveFileRowData.length; i < size; i++) {
            const data = this.sensitiveFileRowData[i]
            if (rowData.id === data.id) {
              this.sensitiveFileRowData.splice(i, 1, rowData)
              break
            }
          }
          this.cancelSensitiveFile()
          this.sensitiveFileRowDataTemp = this.sensitiveFileRowData.filter(item => item.processName === rowData.processName)
          validate = valid
        }
      })
      return validate
    },
    deleteProcess() {
      this.cancelProcess()
      const toDeleteIds = this.processTable().getSelectedIds()
      this.processTable().deleteRowData(toDeleteIds, this.processRowData)
      this.deleteByProcessRowData()
    },
    deleteSensitiveFile() {
      const toDeleteIds = this.sensitiveFileOpTable().getSelectedIds()
      this.sensitiveFileRowData = this.sensitiveFileRowData.filter(item => toDeleteIds.indexOf(item.id) == -1)
      const processName = this.sensitiveFileOpTemp.processName
      this.sensitiveFileRowDataTemp = this.sensitiveFileRowData.filter(item => item.processName === processName)
      this.cancelSensitiveFile()
    },
    deleteByProcessRowData() {
      for (let i = 0; i < this.sensitiveFileRowData.length; i++) {
        let isDel = 1
        const sensitiveFile = this.sensitiveFileRowData[i]
        this.processRowData.forEach(function(process) {
          if (sensitiveFile.processName === process.processName) {
            isDel = 0
          }
        })
        if (isDel === 1) {
          this.sensitiveFileRowData.splice(i, 1)
          i--
        }
      }
    },
    deleteByProcessName(processName) {
      this.sensitiveFileRowData = this.sensitiveFileRowData.filter(item => item.processName !== processName)
    },
    cancelProcess() {
      this.processTable() && this.processTable().setCurrentRow()
      this.resetProcessTemp()
      this.resetSensitiveFileOpTemp()
      this.$nextTick(() => {
        this.clearProcessFormValidate()
      })
    },
    cancelSensitiveFile() {
      this.sensitiveFileOpTable() && this.sensitiveFileOpTable().clearSelection()
      this.clearSensitiveFileFormValidate()
      const processName = this.sensitiveFileOpTemp.processName
      this.resetSensitiveFileOpTemp()
      this.sensitiveFileOpTemp.processName = processName
      this.sensitiveFileEditable = false
    },
    closeSensitiveFileDialog() {
      this.sensitiveFileRowData = JSON.parse(JSON.stringify(this.sensitiveFileRowDataCahe))
      this.sensitiveFileFormVisible = false
    },
    addSensitiveFiles() {
      this.sensitiveFileRowDataCahe = JSON.parse(JSON.stringify(this.sensitiveFileRowData))
      this.sensitiveFileFormVisible = false
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    btnFormatter: function(data, btn) {
      const val = 1
      return (Array.isArray(data.operation) ? data.operation.indexOf(val) > -1 : data.operation === val) ? this.$t('table.config') : ''
    },
    msgFormatter: function(row, data) {
      const times = row.capTimes
      const interval = row.spanTime / 10  // 百毫秒转成秒
      const delay = row.delayTime / 10    // 百毫秒转成秒
      const screenRecord = row.recordTime || row.recordTime == 0 ? row.recordTime : 10
      const names = []
      if (row.processOpAction) {
        names.push(...new Set(row.processOpAction.map(item => item.processName)))
      }
      const processMsg = names.length > 0 ? `${this.$t('table.programName')}：${names.join(', ')}` : ''
      return this.$t('pages.processMonitorMsg', { times, interval, delay, screenRecord, processMsg })
    },
    processNameFormatter: function(row, data) {
      const names = []
      if (row.processOpAction) {
        row.processOpAction.forEach(function(bean) {
          if (names.indexOf(bean.processName) < 0) {
            names.push(bean.processName)
          }
        })
      }
      return names.join(',')
    },
    strategyNameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    capTimesValidator(rule, value, callback) {
      const y = String(value).indexOf('.')
      if (y > -1) {
        callback(new Error(this.$t('pages.notAllowedDecimalPoint')))
      } else {
        callback()
      }
    },
    spanTimeValidator(rule, value, callback) {
      const y = String(value).indexOf('.')
      // 小数点位数 = 字符串长度 - 一位小数点 - 小数点位置
      const count = String(value).length - 1 - y
      if (y > -1 && count > 1) {
        callback(new Error(this.$t('pages.onlyAllowedOneDecimalPlace')))
      } else {
        callback()
      }
    },
    delayTimeValidator(rule, value, callback) {
      const y = String(value).indexOf('.')
      // 小数点位数 = 字符串长度 - 一位小数点 - 小数点位置
      const count = String(value).length - 1 - y
      if (y > -1 && count > 1) {
        callback(new Error(this.$t('pages.onlyAllowedOneDecimalPlace')))
      } else {
        callback()
      }
    },
    recordIntervalValidator(rule, value, callback) {
      const y = String(value).indexOf('.')
      // 小数点位数 = 字符串长度 - 一位小数点 - 小数点位置
      const count = String(value).length - 1 - y
      if (y > -1 && count > 1) {
        callback(new Error(this.$t('pages.onlyAllowedOneDecimalPlace')))
      } else {
        callback()
      }
    },
    recordTimeValidator(rule, value, callback) {
      const y = String(value).indexOf('.')
      if (y > -1) {
        callback(new Error(this.$t('pages.notAllowedDecimalPoint')))
      } else if (this.validRecordTime) {
        callback(new Error(this.$t('pages.recordTimeMsg2', { currentRecordInterval: this.currentRecordInterval })))
      } else {
        callback()
      }
    },
    processNameValidator(rule, value, callback) {
      if (this.processTemp.operation.includes(1) && value === '*.*') {
        callback(new Error(this.$t('pages.processMsg19')))
        return;
      }
      if (!value) {
        callback(new Error(this.$t('pages.process_Msg10')))
      } else if (!this.processNameReg(value)) {
        callback(new Error(this.$t('pages.process_Msg14')))
      }
      const size = this.processRowData.length
      for (let i = 0; i < size; i++) {
        const item = this.processRowData[i]
        let isContainOperation = false
        item.operation.forEach(op => {
          if (this.processTemp.operation.indexOf(op) > -1) {
            isContainOperation = true
          }
        })
        if (item.processName === value && isContainOperation && (this.configBtnMode === 'add' || item.id !== this.processTemp.id)) {
          callback(new Error(this.$t('pages.process_Validate5')))
          return
        }
      }
      callback()
    },
    operationValidator(rule, value, callback) {
      if (!value || value.length === 0) {
        callback(new Error(this.$t('pages.requiredSelectInfo', { info: this.$t('pages.process_Msg1') })))
      } else {
        callback()
      }
    },
    actionValidator(rule, value, callback) {
      if (!value || value.length === 0) {
        callback(new Error(this.$t('pages.requiredSelectInfo', { info: this.$t('pages.monitoringMode') })))
      } else {
        callback()
      }
    },
    sameValueValidator(rule, value, callback) {
      if (this.sensitiveFileOpTemp.processName === '*.*' && this.sensitiveFileOpTemp.path === '*.*' && this.sensitiveFileOpTemp.fileExt === '*.*') {
        callback(new Error(this.$t('pages.process_Msg15')))
      }
      callback()
    },
    validOperation() {
      const processOpAction = this.temp.processOpAction
      for (var i = 0; i < processOpAction.length; i++) {
        var k = true
        for (var j = 0; j < this.temp.sensitiveFileOp.length; j++) {
          if (processOpAction[i].processName === this.temp.sensitiveFileOp[j].processName) {
            k = false
            break
          }
        }
        if (k === true && processOpAction[i].operation === 1) {
          return processOpAction[i].processName
        }
      }
      return ''
    },
    processNameReg(value) {
      if (value === '*.*' || value.substr(-4) == '.exe' || value.substr(-4) == '.EXE') {
        return true
      }
    },
    operationFormatter: function(row, data) {
      if (Array.isArray(data)) {
        const result = []
        data.forEach(item => {
          const val = this.operationMap[item]
          if (val) {
            result.push(val)
          }
        })
        return result.join(',')
      }
      return this.operationMap[data]
    },
    actionFormatter: function(row, data) {
      if (Array.isArray(data)) {
        const result = []
        data.forEach(item => {
          const val = this.actionMap[item]
          if (val) {
            result.push(val)
          }
        })
        return result.join(',')
      }
      return this.actionMap[data]
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      // let union_suffix
      // const oldSuffix = this.sensitiveFileOpTemp.fileExt
      // if (oldSuffix === '') {
      //   union_suffix = [...new Set(suffix.split('|'))]
      // } else {
      //   union_suffix = [...new Set(oldSuffix.split('|').concat(suffix.split('|')))]
      // }
      this.sensitiveFileOpTemp.fileExt = [...new Set(suffix.split('|'))].join('|')
      this.clearSensitiveFileFormValidate('fileExt')
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },

    //  当列表发生改变时，校验名称是否符合规则
    tagChange(names) {
      names = this.verifyExeNames(names)
      this.processTemp.processNames = names;
    },
    handleClear() {
      this.processTemp.processNames.splice(0)
    },
    //  校验进程名集合
    verifyExeNames(names) {
      this.$refs['processForm'].clearValidate()
      this.processNamesMessage = ''
      names = this.verifyFileSuffix(names)
      names = this.filterRepetitionData(names);
      names = this.verifyExeNameExits(names);
      if (this.processNamesMessage !== '' || names.length === 0) {
        this.$refs['processForm'].validate('processNames');
      }
      return names;
    },
    //  校验进程名是否符合规则
    verifyFileSuffix(names) {
      //  校验进程名是否符合名字规则
      const oldLen = names.length
      names = names.filter(item => { return this.processNameReg(item) })
      if (names.length < oldLen) {
        this.$message({
          message: this.$t('pages.process_Msg14'),
          type: 'warning',
          duration: 3000
        })
      }
      return names;
    },
    //  校验进程名是否已存在
    verifyExeNameExits(names) {
      //  校验进程是否已存在
      const oldLen = names.length
      const exitNames = []    //  已存在的进程名
      names = names.filter(name => {
        const flag = this.processRowData.findIndex(item => { return item.processName === name }) === -1;
        if (!flag) {
          exitNames.push(name)
        }
        return flag;
      })
      if (names.length < oldLen) {
        this.$message({
          message: this.$t('pages.processMonitor_processNameExisted', { processNames: exitNames.join(',') }),
          type: 'warning',
          duration: 3000
        })
      }
      return names;
    },
    //  过滤重复数据
    filterRepetitionData(list) {
      const tList = []
      list.forEach(item => {
        if (!tList.includes(item)) {
          tList.push(item)
        }
      })
      return tList;
    },
    showAppSelectDlg() {
      this.$refs['appLib'].show()
    },
    showUpdateAppSelectDlg() {
      this.$refs['updateAppLib'].show()
    },
    appendFile(softs) {
      if (this.operationType !== 'update') {
        softs = softs || []
        let list = [...this.processTemp.processNames]
        softs.forEach(item => {
          list.push(item.processName)
        })
        list = this.verifyExeNames(list)
        this.processTemp.processNames = list
      } else {
        softs = softs || null
        //  若为更新操作，导入的是程序对象
        if (softs !== null) {
          this.processTemp.processName = softs.processName || ''
        }
      }
    },
    processNamesValidator(rule, value, callback) {
      if (this.processTemp.operation.includes(1) && value.includes('*.*')) {
        callback(new Error(this.$t('pages.processMsg19')))
        return;
      }
      if (this.operationType === 'create') {
        if (this.processNamesMessage !== '') {
          callback(new Error(this.processNamesMessage))
        } if (this.processTemp.processNames.length === 0) {
          callback(this.$t('pages.processMonitor_processNameNotNull'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    //  加载录屏间时长可设置的最小值
    getMinRecordTimeInterval() {
      return getRecordTimeConfig().then(res => {
        this.currentRecordInterval = res.data.recordCurrentInterval || 0
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .process {
    width: 290px;
    display: inline-block;
    vertical-align: top;
  }
  .action {
    width: 290px;
    display: inline-block;
  }
  .monitor {
    width: 325px;
    display: inline-block;
  }
  .checkbox-group {
    height: 31px;
    padding-left: 4px;
    //background: #f5f5f5;
    //border: 1px solid #aaa;
    //border-radius: 4px;
  }
</style>
