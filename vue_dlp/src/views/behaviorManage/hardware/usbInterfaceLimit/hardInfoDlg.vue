<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :height="600"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="130px">
        <FormItem :label="$t('table.interfaceKey')" prop="interfaceKey">
          <el-input v-model="temp.interfaceKey" maxlength=""></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" maxlength=""></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="submitEnd">
          {{ $t('pages.addUsbInfo') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'HardInfoDlg',
  props: {
  },
  data() {
    return {
      multiSelect: true,
      rules: {
        interfaceKey: [{ required: true, message: this.$t('pages.interfaceMsg6'), trigger: 'blur' }]
      },
      temp: {}, // 表单字段
      defaultTemp: {
        id: null,
        interfaceKey: '',
        remark: ''
      },
      submitting: false,
      dialogFormVisible: false,
      submitFlag: true,
      validate: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.interfaceMsg5'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.interfaceMsg5'), 'create')
      },
      dialogStatus: '',
      visible: false
    }
  },
  created() {
  },
  methods: {
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    submitEnd() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.id = this.temp.id || new Date().getTime();
          this.submitting = false
          this.dialogFormVisible = false
          this.$emit('submitEnd', this.temp, this.dialogStatus)
        } else {
          this.submitting = false
        }
      })
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    show() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.dialogStatus = 'create'
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row, type) {
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj.
      this.dialogFormVisible = true
      this.dialogStatus = 'update'
    }
  }
}
</script>
