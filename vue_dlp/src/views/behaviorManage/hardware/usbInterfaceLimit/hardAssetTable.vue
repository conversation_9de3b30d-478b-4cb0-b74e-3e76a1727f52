<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :height="600"
    :title="$t('pages.interfaceMsg5')"
    :visible.sync="dialogFormVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div>
      <div class="tree-container" style="height: 370px;">
        <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" @data-change="targetNodeChange" />
      </div>
      <div class="table-container-dialog">
        <div class="toolbar">
          <div class="searchCon">
            <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.uninstallManage_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>
          </div>
        </div>
        <grid-table
          ref="hardAssetTable"
          :height="370"
          :col-model="colModel"
          is-saved-selected
          saved-selected-prop="id"
          :row-data-api="rowDataApi"
          row-key="value"
          :multi-select="multiSelect"
          :default-sort="{ prop: 'id' }"
          retain-pages
        />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="submitEnd">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="clearSelection">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listHardAssetInfo } from '@/api/assets/assetsConfig/assetsView'

export default {
  name: 'HardAssertTable',
  props: {
    propId: { type: Number, default: null }
  },
  data() {
    return {
      multiSelect: true,
      colModel: [
        { prop: 'terminalName', label: 'terminalName', width: '150' },
        // { prop: 'name', label: 'name', width: '200', formatter: this.infoFormatter },
        { prop: 'value', label: 'interfaceKey', width: '200' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined,
        propId: null,
        softwareName: ''
      },
      submitting: false,
      installPath: '',
      dialogFormVisible: false,
      submitFlag: true,
      validate: false
    }
  },
  computed: {},
  created() {},
  methods: {
    gridTable() {
      return this.$refs['hardAssetTable']
    },
    show(validate) {
      this.dialogFormVisible = true
      this.validate = validate
      this.installPath = ''
      if (this.gridTable()) {
        this.query.page = 1
        this.handleRefresh()
      }
    },
    targetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable() && this.gridTable().execRowDataApi(this.query)
    },
    submitEnd() {
      const selectedDatas = this.gridTable().getSelectedDatas()
      if (selectedDatas && selectedDatas.length > 0) {
        const datas = []
        selectedDatas.forEach(item => {
          const data = { interfaceKey: item.value }
          datas.push(data)
        })
        this.$emit('submitEnd', datas)
        this.clearSelection()
      } else {
        this.$message({
          message: this.$t('pages.interfaceMsg7'),
          type: 'error',
          duration: 2000
        })
      }
    },
    clearSelection() {
      this.gridTable().selectedDatasDelete()
      this.dialogFormVisible = false
    },
    rowDataApi: function(option) {
      this.query.propId = this.propId
      const searchQuery = Object.assign({}, this.query, option)
      return listHardAssetInfo(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    handleDrag() {
      // this.$refs.select.blur()
    }
  }
}
</script>
