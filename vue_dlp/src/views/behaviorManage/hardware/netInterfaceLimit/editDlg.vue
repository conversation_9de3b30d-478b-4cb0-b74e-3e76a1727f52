<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.netInterface')"
      :title-tip="$t('pages.netInterfaceTip', {content: $t('pages.netInterface')} )"
      :active-able="activeAble"
      :stg-code="245"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <div style="padding-left: 30px;">
          <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
          <LocalZoomIn parent-tag="el-dialog">
            <div v-if="formable" class="toolbar">
              <el-button size="small" @click="handleHardInfo">
                {{ $t('button.insert') }}
              </el-button>
              <el-button size="small" @click="handleAddHardInfo">
                {{ $t('pages.importBtn') }}
              </el-button>
              <el-button size="small" :disabled="!checkSoftwareDeleteable" @click="handleDelete">
                {{ $t('button.delete') }}
              </el-button>
            </div>
            <grid-table
              ref="checkedAppGrid"
              row-key="interfaceKey"
              :show-pager="false"
              auto-height
              :multi-select="formable"
              :col-model="checkedColModel"
              :row-datas="temp.hardAssetList"
              @selectionChangeEnd="checkSoftwareSelectionChangeEnd"
            />
          </LocalZoomIn>
          <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
          <FormItem label="" label-width="0">
            <el-row style="margin-left: 31px;">
              <el-col :span="10">
                <el-radio-group v-model="temp.limitType" :disabled="!formable" style="width: 100%;">
                  <el-radio :label="1" class="ellipsis" style="width: 98%;">{{ $t('pages.netInterfaceMsg1') }}</el-radio>
                  <el-radio :label="2" class="ellipsis" style="width: 98%; margin-top:4px;">{{ $t('pages.netInterfaceMsg2') }}</el-radio>
                </el-radio-group>
              </el-col>
              <el-col :span="24">
                <label style="font-weight: 500;color: #409eff;font-size: small;">
                  {{ $t('pages.executeRuleTip', { info: this.$t('pages.identifierRule_regular'), info1: this.$t('pages.interfaceMsg4') }) }}
                </label>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.withinDetectionRuleTip">
                      <span slot="info">{{ $t('pages.allowNetInterfaceAccess') }}</span>
                    </i18n>
                    <br/>
                    <i18n path="pages.outsideDetectionRuleTip1">
                      <span slot="info">{{ $t('pages.forbidNetInterfaceAccess') }}</span>
                    </i18n>
                  </div>
                  <i class="el-icon-info"/>
                </el-tooltip>
              </el-col>
            </el-row>
          </FormItem>
          <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
          <FormItem label-width="10px" prop="action">
            <el-checkbox v-model="temp.action" :disabled="!formable" class="prohibit" :false-label="0" :true-label="1" style="margin-left: 17px">{{ $t('pages.netInterfaceMsg3') }}</el-checkbox>
            <ResponseContent
              ref="resContent"
              :select-style="{ 'margin-top': 0 }"
              :show-select="true"
              :editable="formable"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="temp.ruleId"
              @getRuleId="getRuleId"
              @ruleIsCheck="ruleIsCheck"
              @validate="(val) => { responseValidate = val }"
            />
          </FormItem>
        </div>
      </template>
    </stg-dialog>

    <hard-info-dlg ref="hardInfoDlg" @submitEnd="hardInfoSubmitEnd" />

    <hard-asset-table ref="hardAssetTable" :prop-id="23101" @submitEnd="importHardInfoSubmitEnd" />
  </div>
</template>

<script>
import { getStrategyByName, createStrategy, updateStrategy } from '@/api/behaviorManage/hardware/netInterfaceLimit'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import HardInfoDlg from '@/views/behaviorManage/hardware/usbInterfaceLimit/hardInfoDlg'
import HardAssetTable from '@/views/behaviorManage/hardware/usbInterfaceLimit/hardAssetTable'

export default {
  name: 'SoftwareBlockDlg',
  components: { ResponseContent, HardInfoDlg, HardAssetTable },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        limitType: 1,
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        ruleId: null,
        hardAssetList: [],
        action: 0,
        isAlarm: 0
      },
      defaultTempS: {
        softwareName: '',
        softwareVersion: '',
        appInfos: []
      },
      checkSoftware: [],
      checkedColModel: [
        { prop: 'interfaceKey', label: 'interfaceKey', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '150', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleUpdateHardInfo }
          ]
        }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        action: [{ required: true, validator: this.actionValidator, trigger: 'blur' }]
      },
      propRuleId: undefined,
      ruleChecked: false,
      checkSoftwareDeleteable: false
    }
  },
  computed: {

  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
    },
    closed() {
      this.resetTemp()
    },
    appGridTable() {
      return this.$refs['checkedAppGrid']
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    checkSoftwareSelectionChangeEnd(rowDatas) {
      this.checkSoftwareDeleteable = rowDatas && rowDatas.length > 0
    },
    handleAddHardInfo() {
      this.$refs.hardAssetTable.show()
    },
    buttonFormatter(row) {
      return !this.formable
    },
    handleUpdateHardInfo(row) {
      this.$refs.hardInfoDlg.handleUpdate(row)
    },
    handleHardInfo() {
      this.$refs.hardInfoDlg.show()
    },
    handleDelete() {
      const toDeleteKeys = this.appGridTable().getSelectedDatas().map(data => data.interfaceKey)
      this.appGridTable().deleteRowData(toDeleteKeys, this.temp.hardAssetList)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.propRuleId = undefined
    },
    resetTempS() {
      return JSON.parse(JSON.stringify(this.defaultTempS))
    },
    handleCreate() {
      this.resetTemp()
      this.ruleChecked = false
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      }, undefined, () => {
        this.$nextTick(() => { // 新增弹框打开时，清除响应规则的选中情况
          this.$refs['resContent'].ruleId = undefined
          this.temp.ruleId = null
        })
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      this.propRuleId = row.ruleId
      if (this.propRuleId) {
        this.ruleChecked = true
      } else {
        this.ruleChecked = false
      }
      this.$refs['stgDlg'].show(row, this.formable)
    },
    propCheckRuleChange(val) {
      if (!val) {
        this.propRuleId = undefined
        this.temp.ruleId = null
        this.ruleChecked = false
      } else {
        this.ruleChecked = true
      }
    },
    formatRowData(rowData) {

    },
    formatFormData(formData) {

    },
    validateFormData(formData) {
      if (formData.isAlarm === 1 && !this.temp.ruleId) {
        return false
      }
      return true
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    hardInfoSubmitEnd(data, dialogStatus) {
      if (this.temp.hardAssetList && this.temp.hardAssetList.length > 0) {
        let updateIndex = ''
        let addIndex = ''
        updateIndex = this.temp.hardAssetList.findIndex(existAsset => {
          return existAsset.id == data.id
        })
        addIndex = this.temp.hardAssetList.findIndex(existAsset => {
          return existAsset.interfaceKey == data.interfaceKey
        })
        if ((addIndex > -1 && dialogStatus == 'create') || (updateIndex > -1 && addIndex > -1)) {
          this.$message({
            message: '已存在此接口网络mac地址，请勿重复添加',
            type: 'warning',
            duration: 3000
          })
        } else if (updateIndex > -1 && dialogStatus == 'update') {
          this.temp.hardAssetList.splice(updateIndex, 1, data)
        } else {
          this.temp.hardAssetList.push(data)
        }
      } else {
        this.temp.hardAssetList.push(data)
      }
    },
    actionValidator(rule, value, callback) {
      if (!this.temp.action && !this.temp.isAlarm) {
        callback(new Error(this.$t('pages.install_Msg51')))
      } else {
        callback()
      }
    },
    importHardInfoSubmitEnd(data) {
      if (!data) return
      if (this.temp.hardAssetList && this.temp.hardAssetList.length > 0) {
        data.forEach((item, index) => {
          const theIndex = this.temp.hardAssetList.findIndex(existAsset => {
            return existAsset.interfaceKey == item.interfaceKey
          })
          if (theIndex > -1) {
            this.temp.hardAssetList.splice(theIndex, 1, item)
          } else {
            this.temp.hardAssetList.push(item)
          }
        })
      } else {
        this.temp.hardAssetList.push(...data)
      }
    },
    ruleIsCheck(value) {
      this.temp.isAlarm = value
    }
  }
}
</script>
