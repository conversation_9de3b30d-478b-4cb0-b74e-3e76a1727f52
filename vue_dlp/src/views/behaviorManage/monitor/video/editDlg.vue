<template>
  <stg-dialog
    ref="stgDlg"
    time-able
    :title="$t('pages.screenRecordingPolicy')"
    :stg-code="19"
    :width="650"
    :entity-type-span="10"
    :active-able="activeAble"
    :rules="rules"
    :model="defaultTemp"
    :entity-node="entityNode"
    :create="createStrategy"
    :update="updateStrategy"
    :get-by-name="getByName"
    :format-row-data="formatRowData"
    @submitEnd="submitEnd"
    @slotChange="slotChange"
  >
    <template :slot="slotName">
      <FormItem :label="$t('pages.recordingInterval')" prop="interval">
        <el-input-number v-model="temp.interval" :disabled="!formable" :min="1" :max="3600" :controls="false" style="width: 100px;" />
        {{ $t('text.secondLower') }}
      </FormItem>
      <FormItem label-width="90px">
        <span style="color: #2b7aac;">{{ $t('pages.video_Msg') }}</span>
      </FormItem>
      <FormItem :label="$t('pages.forcedRecordingInterval')" prop="forceInterval">
        <el-input-number v-model="temp.forceInterval" :disabled="!formable" :min="0" :max="3600" :controls="false" style="width: 100px;" />
        {{ $t('text.secondLower') }}
      </FormItem>
      <FormItem label-width="90px">
        <span style="color: #2b7aac;">{{ $t('pages.video_Msg2') }}</span>
      </FormItem>
      <FormItem :label="$t('pages.imageQuality')" prop="imageQuality">
        <el-radio-group v-model="temp.imageQuality" :disabled="!formable" style="margin-left: 18px;">
          <el-radio :label="0">{{ $t('pages.imageQuality1') }}</el-radio>
          <el-radio :label="2">{{ $t('pages.imageQuality3') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </template>
  </stg-dialog>
</template>

<script>
import { createStrategy, getByName, updateStrategy } from '@/api/behaviorManage/monitor/video'

export default {
  name: 'VideoDlg',
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        interval: 120,
        active: false,
        forceInterval: 120,
        imageQuality: 0,
        remark: '',
        entityType: undefined,
        entityId: undefined
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        interval: [
          { validator: this.intervalValidator, trigger: 'blur' }
        ],
        forceInterval: [
          { validator: this.forceIntervalValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      slotName: undefined
    }
  },
  created() {
    this.temp = Object.assign({}, this.defaultTemp)
  },
  methods: {
    createStrategy,
    updateStrategy,
    getByName,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    intervalValidator(rule, value, callback) {
      if (value === undefined || value === null) {
        callback(new Error(this.$t('text.cantNull')))
        return
      }
      if (this.temp.forceInterval !== 0 && this.temp.interval > this.temp.forceInterval) {
        this.temp.forceInterval = this.temp.interval
      }
      callback()
    },
    forceIntervalValidator(rule, value, callback) {
      if (value === undefined || value === null) {
        callback(new Error(this.$t('text.cantNull')))
        return
      }
      if (this.temp.forceInterval !== 0 && this.temp.interval > this.temp.forceInterval) {
        this.temp.interval = this.temp.forceInterval
      }
      callback()
    },
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate: function(row) {
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(row) {
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    }
  }
}
</script>
