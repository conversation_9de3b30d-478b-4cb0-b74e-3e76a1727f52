<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode" :scope="1"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="776px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 696px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :label-width="labelWidth"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name" label-width="110px">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')" label-width="110px">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')" label-width="110px">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>

        <el-divider content-position="left">{{ $t('pages.energySaving_energySavingSetting') }}</el-divider>

        <div style="margin-left: 50px">
          <el-row>
            <el-checkbox v-model="temp.displaysAble" :true-label="1" :false-label="0" :disabled="!formable" @change="displaysAbleChange">{{ $t('pages.energySaving_closeMonitor') + $t('pages.energySaving_supportWin7AndAbove') }}</el-checkbox>
          </el-row>
          <i18n path="pages.energySaving_message4" style="display: inline-flex; line-height: 30px; margin-left: 30px">
            <FormItem slot="time" label-width="0" prop="monitorOffTime">
              <el-input-number v-model="temp.monitorOffTime" style="width: 90px; margin: 0 3px" :step="1" :disabled="!formable || !temp.displaysAble" step-strictly :controls="false" :min="1" :max="9999" size="mini"/>
            </FormItem>
            <span slot="operator">{{ $t('pages.energySaving_closeMonitor').toLocaleLowerCase() }}</span>
          </i18n>
        </div>

        <FormItem label-width="50px" style="margin-bottom: 10px" prop="executeAble">
          <el-row style="display:flex;">
            <el-checkbox v-model="temp.executeAble" :true-label="1" :false-label="0" :disabled="!formable" @change="executeAbleChange">{{ $t('pages.energySaving_computeStatusSetting') }}</el-checkbox>
            <el-radio-group v-model="temp.timingType" style="margin-left: 10px" :disabled="!formable || !temp.executeAble">
              <el-radio :label="8">{{ $t('pages.energySaving_sleep') }}</el-radio>
              <el-radio :label="16">{{ $t('pages.energySaving_dormancy') }}</el-radio>
              <el-radio :label="1">{{ $t('pages.energySaving_shutdown') }}</el-radio>
              <el-radio :label="2">{{ $t('pages.energySaving_restart') }}</el-radio>
            </el-radio-group>
          </el-row>
          <el-row>
            <span v-if="temp.timingType === 1 && temp.executeAble && formable" style="margin-left: 10px; color: #ec0f0f">{{ $t('pages.energySaving_message5') }}</span>
            <span v-if="temp.timingType === 2 && temp.executeAble && formable" style="margin-left: 10px; color: #ec0f0f"> {{ $t('pages.energySaving_message2') }}</span>
          </el-row>

          <div style="margin-left: 30px">
            <!-- 指定时间监测 -->
            <el-row>
              <el-radio v-model="detectionMode" :label="0" :disabled="!temp.executeAble" @change="detectionModeClick(false)">
                <i18n path="pages.energySaving_message3" style="display: inline-flex">
                  <FormItem slot="time" prop="executeTime">
                    <el-time-picker v-model="temp.executeTime" :disabled="!formable || !temp.timingType || !temp.executeAble || !!detectionMode" style="width: 120px; margin: 0 3px" format="HH:mm" value-format="HH:mm" size="mini"/>
                  </FormItem>
                  <span slot="operator">{{ executeOperator.toLocaleLowerCase() }}</span>
                  <br slot="br"/>
                  <FormItem slot="intervalTime" prop="executeInterval">
                    <el-input-number v-model="temp.executeInterval" style="width: 90px; margin: 0 3px" :step="1" :disabled="!formable || !temp.timingType || !temp.executeAble || !!detectionMode" step-strictly :controls="false" :min="1" :max="9999" size="mini"/>
                  </FormItem>
                </i18n>
              </el-radio>
            </el-row>
            <!-- 指定时间片监测 -->
            <el-row style="margin-top: 5px">
              <el-radio v-model="detectionMode" :label="1" :disabled="!temp.executeAble" @change="detectionModeClick(true)">
                <i18n path="pages.energySaving_message7" style="display: inline-flex">
                  <span slot="operator">{{ executeOperator.toLocaleLowerCase() }}</span>
                  <FormItem slot="intervalTime" prop="cycleTimeExecuteInterval">
                    <el-input-number v-model="temp.cycleTimeExecuteInterval" style="width: 90px; margin: 0 3px" :step="1" :disabled="!formable || !temp.timingType || !temp.executeAble || !detectionMode" step-strictly :controls="false" :min="1" :max="9999" size="mini"/>
                  </FormItem>
                </i18n>
              </el-radio>
            </el-row>
          </div>
        </FormItem>
        <el-card v-if="temp.executeAble && detectionMode" class="cycleTimeCard">
          <div slot="header">
            <el-button size="small" @click="deleteCyctime">清空</el-button>
          </div>
          <div style="max-height: 150px; width: 100%; overflow-y: auto">
            <el-row>
              <el-col v-for="(item, index) in rangTimeDatas" :key="index" :span="12" style="text-align: center;">
                <div style="width: 100%; text-align: center">
                  <div style="display: inline-flex; align-items: center; justify-content: space-between; margin: 5px 10px; width: 200px; text-align: left;">
                    <!-- 时间选择器 -->
                    <el-time-picker v-model="item.value" :is-range="true" :disabled="!formable" format="HH:mm" value-format="HH:mm" size="mini"/>
                    <!-- 按钮区域 -->
                    <div style="display: inline-flex;">
                      <el-button v-if="index === rangTimeDatas.length - 1" type="text" icon="el-icon-circle-plus" class="add_btn" :disabled="!formable" @click="addRangTime"/>
                      <el-button type="text" icon="el-icon-remove" :disabled="!formable" class="del_btn" @click="deleteRangTime(item.id)"/>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <FormItem label-width="50px" style="margin-bottom: 10px" >
          <el-row>
            <el-checkbox v-model="temp.isOpenPrompt" :true-label="1" :false-label="2" :disabled="!formable || !temp.executeAble" @change="isOpenPromptChange">{{ $t('pages.energySaving_toolTipBoxSetting') }}</el-checkbox>
          </el-row>
          <el-row style="margin-left: 30px">
            <i18n path="pages.energySaving_message1" style="display: inline-flex">
              <FormItem slot="time" prop="isOpenPrompt">
                <el-input-number v-model="temp.promptTime" style="width: 90px; margin: 0 3px" :step="1" :disabled="!formable || temp.isOpenPrompt !== 1" step-strictly :controls="false" :min="10" :max="60" size="mini"/>
              </FormItem>
              <br slot="br"/>
            </i18n>
            <span style="color: rgb(43, 122, 172); margin-left: 10px">{{ $t('pages.energySaving_messageHint') }}</span>
          </el-row>
        </FormItem>
      </Form>

      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importLogFilterStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import {
  getStrategyPage, getStrategyByName, createStrategy, updateStrategy, deleteStrategy
} from '@/api/behaviorManage/monitor/energySaving'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'EnergySaving',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 233,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'style', label: 'stgMessage', width: '200', formatter: this.styleFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      labelWidth: '110px',
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        displaysAble: 0,     //  显示器是否开启
        monitorOffTime: undefined,      //  关闭显示器时间,   保存单位为分钟（设定时间内无操作执行）
        executeAble: 0,        //  是否开启 关机/睡眠/休眠/重启
        timingType: 8,        //  1-关机 2-重启 4-关闭显示器 8-睡眠 16-休眠
        executeTime: '',           //  （关机/睡眠/休眠/重启）时间, 格式：xx:xx（24小时制）
        executeInterval: undefined,     //  （关机/睡眠/休眠/重启）时间间隔,  单位分钟
        isOpenPrompt: 2,     //  是否开启关机/重启 时间提醒   1-是    2-否
        promptTime: undefined,          //  关机/睡眠/休眠/重启倒计时, 单位秒（定时关机/重启前进行弹窗提示)
        cycleTime: [],         // 监测周期 里面每个元素代表一个监测时间片段，不可与执行时间（executeTime）同时配置，配置此项则默认重复执行
        cycleTimeExecuteInterval: undefined // 与executeInterval相同功能，若设置的是指定时间片监测，最终上报给后端时，需要将cycleTimeExecuteInterval值给到executeInterval
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('route.energySaving'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('route.energySaving'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        monitorOffTime: [
          { validator: this.monitorOffTimeValidator, trigger: 'blur' }
        ],
        executeTime: [
          { validator: this.executeTimeValidator, trigger: 'blur' }
        ],
        executeInterval: [
          { validator: this.executeIntervalValidator, trigger: 'blur' }
        ],
        cycleTimeExecuteInterval: [
          { validator: this.cycleTimeExecuteIntervalValidator, trigger: 'blur' }
        ],
        isOpenPrompt: [
          { validator: this.isOpenPromptValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      rangTimeDatas: [],  //  保持所配置的时间段集
      detectionMode: 0,  //  计算机设置状态的检测方式
      detectionMaxCount: 10  //  监测时间段最大可设置10个
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    executeOperator() {
      let result = '';
      switch (this.temp.timingType) {
        case 1: result = this.$t('pages.energySaving_shutdown'); break;
        case 2: result = this.$t('pages.energySaving_restart'); break;
        case 8: result = this.$t('pages.energySaving_sleep'); break;
        case 16: result = this.$t('pages.energySaving_dormancy'); break;
      }
      return result;
    }
  },
  watch: {
    'temp.executeAble'(val) {
      if (!val) {
        this.temp.isOpenPrompt = 2
      }
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    reValidator(able, flag) {
      if (!flag) {
        this.$nextTick(() => {
          this.$refs['dataForm'].validateField(able);
        })
      }
    },
    monitorOffTimeValidator(rule, value, callback) {
      if (!!this.temp.displaysAble && !this.temp.monitorOffTime) {
        callback(new Error(this.$t('pages.energySaving_validMessage1') + ''))
      }
      callback();
    },
    sleepValidator(rule, value, callback) {
      if (value) {
        if ((!this.detectionMode && (!this.temp.executeTime || !this.temp.executeTime.length || !this.temp.executeInterval)) ||
          (!!this.detectionMode && (!this.temp.cycleTimeExecuteInterval || !this.rangTimeDatas.length))) {
          callback(new Error(this.$t('pages.energySaving_validMessage1') + ''))
        }
      }
      callback();
    },
    executeTimeValidator(rule, value, callback) {
      if (this.temp.executeAble && !this.detectionMode && (!this.temp.executeTime || !this.temp.executeTime.length)) {
        callback(new Error(this.$t('pages.energySaving_validMessage1') + ''))
      }
      callback();
    },
    executeIntervalValidator(rule, value, callback) {
      if (this.temp.executeAble && !this.detectionMode && !this.temp.executeInterval) {
        callback(new Error(this.$t('pages.energySaving_validMessage1') + ''))
      }
      callback();
    },
    cycleTimeExecuteIntervalValidator(rule, value, callback) {
      if (this.temp.executeAble && !!this.detectionMode && !this.temp.cycleTimeExecuteInterval) {
        callback(new Error(this.$t('pages.energySaving_validMessage1') + ''))
      }
      callback();
    },
    isOpenPromptValidator(rule, value, callback) {
      if (value === 1 && !this.temp.promptTime) {
        callback(new Error(this.$t('pages.energySaving_validMessage1') + ''))
      }
      callback();
    },
    styleFormatter: function(row, data) {
      let message = ''
      const timingType = row.timingType || 0
      const displayFlag = (timingType >>> 2) % 2 === 1;
      const shutdownFlag = timingType % 2 === 1;
      const restartFlag = (timingType >>> 1) % 2 === 1;
      const sleepFlag = (timingType >>> 3) % 2 === 1;
      const dormantFlag = (timingType >>> 4) % 2 === 1;

      if (displayFlag) {
        message += this.$t('pages.energySaving_message4', { time: row.monitorOffTime, operator: this.$t('pages.energySaving_closeMonitor').toLocaleLowerCase() + '；' })
      }
      if (shutdownFlag || sleepFlag || dormantFlag || restartFlag) {
        message += shutdownFlag ? this.$t('pages.energySaving_message3', { time: row.executeTime, operator: this.$t('pages.energySaving_shutdown').toLocaleLowerCase(), intervalTime: row.executeInterval }) + '；' : '';
        message += sleepFlag ? this.$t('pages.energySaving_message3', { time: row.executeTime, operator: this.$t('pages.energySaving_sleep').toLocaleLowerCase(), intervalTime: row.executeInterval }) + '；' : '';
        message += dormantFlag ? this.$t('pages.energySaving_message3', { time: row.executeTime, operator: this.$t('pages.energySaving_dormancy').toLocaleLowerCase(), intervalTime: row.executeInterval }) + '；' : '';
        message += restartFlag ? this.$t('pages.energySaving_message3', { time: row.executeTime, operator: this.$t('pages.energySaving_restart').toLocaleLowerCase(), intervalTime: row.executeInterval }) + '；' : '';
      }
      if (row.isOpenPrompt === 1) {
        message += this.$t('pages.energySaving_message1', { time: row.promptTime })
      }
      return message
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    initData() {
      this.resetTemp()
      this.rangTimeDatas = []
      this.detectionMode = 0;
    },
    handleCreate() {
      this.initData()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    formatRowData(row) {
      this.temp = this.initTempData(row);
      if (this.temp.executeAble) {
        if (this.temp.cycleTime) {
          let timeId = new Date().getTime()
          //  构建rangTimeDatas格式
          this.temp.cycleTime && this.temp.cycleTime.length && this.temp.cycleTime.forEach(time => {
            const t = time.split('-') || []
            t.length === 2 && this.rangTimeDatas.push({
              id: timeId++,
              value: t
            })
          })
          this.detectionMode = 1
          this.temp.cycleTimeExecuteInterval = this.temp.executeInterval
          this.temp.executeInterval = undefined
        } else {
          this.detectionMode = 0
        }
      }
    },
    handleUpdate: function(row) {
      this.initData()
      this.formatRowData(row);
      // this.temp = Object.assign(this.temp, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: 51 })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    executeAbleChange() {
      this.reValidator('executeAble')
    },
    initTempData(temp) {
      const rowTemp = JSON.parse(JSON.stringify(temp));
      const timingType = rowTemp.timingType || 0
      if (timingType % 2 === 1) {
        rowTemp.timingType = 1
        rowTemp.executeAble = 1
      } else if ((timingType >> 1) % 2 === 1) {
        rowTemp.timingType = 2
        rowTemp.executeAble = 1
      } else if ((timingType >> 3) % 2 === 1) {
        rowTemp.timingType = 8
        rowTemp.executeAble = 1
      } else if ((timingType >> 4) % 2 === 1) {
        rowTemp.timingType = 16
        rowTemp.executeAble = 1
      } else {
        rowTemp.timingType = 8
        rowTemp.executeAble = 0
      }
      rowTemp.displaysAble = (timingType >> 2) % 2 === 1 ? 1 : 0;
      return rowTemp;
    },
    formatterTempData(temp) {
      const rowTemp = Object.assign({}, this.defaultTemp, temp);
      rowTemp.timingType = 0;
      if (temp.displaysAble) {
        rowTemp.timingType += 4;
      }
      if (temp.executeAble) {
        rowTemp.timingType += temp.timingType || 0
        //  detectionMode = 1设置监测周期, 监测周期cycleTime  和 executeTime不可同时配置,  detectionMode = 0, 指定时间监测
        if (this.detectionMode) {
          rowTemp.executeTime = null
          rowTemp.cycleTime = []
          this.rangTimeDatas.length && this.rangTimeDatas.forEach(t => {
            t.value && t.value.length === 2 && rowTemp.cycleTime.push(t.value[0] + '-' + t.value[1]);
          })
          rowTemp.executeInterval = rowTemp.cycleTimeExecuteInterval
        } else {
          rowTemp.cycleTime = null
        }
      } else {
        rowTemp.cycleTime = null
        rowTemp.executeTime = null
      }
      return rowTemp;
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }

      this.submitting = true
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          if (!this.validRangeTime()) {
            this.$message({
              message: '存在时间区间为空的，请选择时间区间！',
              type: 'error'
            })
            this.submitting = false
            return;
          }
          let flag = true
          const rowTemp = this.formatterTempData(this.temp);
          if (rowTemp.active && (rowTemp.timingType === 1 || rowTemp.timingType === 2)) {
            const operator = rowTemp.timingType === 1 ? this.$t('pages.energySaving_shutdown') : this.$t('pages.energySaving_restart')
            await this.$confirmBox(this.$t('pages.energySaving_message6', { operator: operator }), this.$t('text.prompt')).then(() => {
              flag = true
            }).catch(() => {
              flag = false
            })
          }

          if (flag) {
            createStrategy(rowTemp).then(() => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          if (!this.validRangeTime()) {
            this.$message({
              message: '存在时间区间为空的，请选择时间区间！',
              type: 'error'
            })
            this.submitting = false
            return;
          }

          let flag = true
          const rowData = this.formatterTempData(this.temp)
          if (rowData.active && (rowData.timingType === 1 || rowData.timingType === 2)) {
            const operator = rowData.timingType === 1 ? this.$t('pages.energySaving_shutdown') : this.$t('pages.energySaving_restart')
            await this.$confirmBox(this.$t('pages.energySaving_message6', { operator: operator }), this.$t('text.prompt')).then(() => {
              flag = true
            }).catch(() => {
              flag = false
            })
          }
          if (flag) {
            updateStrategy(rowData).then(() => {
              this.submitting = false
              this.dialogFormVisible = false
              this.gridTable.execRowDataApi()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    addRangTime() {
      if (this.rangTimeDatas.length >= this.detectionMaxCount) {
        this.$message({
          message: `最多可设置${this.detectionMaxCount}个监测时间区间`,
          type: 'warning',
          duration: 2000
        })
        return;
      }
      this.rangTimeDatas.push({ id: new Date().getTime(), value: ['00:00', '00:00'] })
    },
    deleteRangTime(key) {
      if (this.rangTimeDatas.length === 1) {
        this.$message({
          message: '最后一个监测时间区间不允许删除！',
          type: 'warning',
          duration: 2000
        })
        return;
      }
      this.rangTimeDatas = this.rangTimeDatas.filter(t => t.id !== key);
    },
    detectionModeClick(flag) {
      //   若rangTimeDatas为空时，自动添加一个值
      if (!this.rangTimeDatas.length && flag) {
        this.rangTimeDatas.push({ id: new Date().getTime(), value: ['18:00', '18:30'] })
      }
      this.$nextTick(() => {
        this.$refs.dataForm.validateField(['executeTime', 'executeInterval', 'cycleTimeExecuteInterval'])
      })
    },
    //  保留第一个监测时间区间，其他都删除
    deleteCyctime() {
      this.rangTimeDatas = []
      this.rangTimeDatas.push({ id: new Date().getTime(), value: ['18:00', '18:30'] })
    },
    isOpenPromptChange() {
      //  未设置时，给设置成30s
      if (!this.temp.promptTime) {
        this.temp.promptTime = 30
      }
      this.reValidator('isOpenPrompt', this.temp.isOpenPrompt === 1);
    },
    displaysAbleChange() {
      //  未设置时，给设置成15分钟
      if (!this.temp.monitorOffTime) {
        this.temp.monitorOffTime = 15
      }
      this.reValidator('monitorOffTime', this.temp.displaysAble)
    },
    validRangeTime() {
      let flag = true;
      if (this.temp.executeAble && this.detectionMode) {
        if (this.rangTimeDatas.length) {
          this.rangTimeDatas.forEach(item => {
            if (!item.value || item.value.length < 2) {
              flag = false;
            }
          })
        }
      }
      return flag;
    }
  }
}
</script>

<style lang='scss' scoped>
.cycleTimeCard {
  margin-left: 50px;
  >>>.el-card__body {
    padding: 5px;
  }
}
.add_btn {
  color: #409EFF!important;
  font-size: 18px; padding: 0 0 0 2px; margin-bottom: 0;
}
.del_btn {
  color: red!important;
  font-size: 18px; padding: 0 0 0 2px; margin-bottom: 0;
}
</style>
