<template>
  <div v-loading="allNodesAble" class="app-container">
    <MessageBox ref="messageBox" />
    <div class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree
        ref="terminalTree"
        multiple
        :check-strictly="true"
        :check-on-click-node="false"
        :showed-tree="['terminal']"
        :disabled-all-nodes="treeClickAble"
        :os-type-filter="7"
        :terminal-filter-key="terminalFilter"
        @check-change="checkedNodeChange"
      />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <label>{{ $t('pages.trackingInterval') }}</label>
        <el-input-number v-model="trackTime" controls-position="right" :min="1" :max="60" style="width:120px;" @blur="handleBlur" @change="changeMultipleTimeTrack"></el-input-number>
        <label style="margin-left: 3px;">{{ $t('pages.screenQuality') }}</label>
        <el-select v-model="qualityType" :popper-append-to-body="false" style="width: 100px;" @change="qualityChange">
          <el-option
            v-for="item in qualityOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-button v-append-tooltip type="primary" :icon="startOrPauseBtnIcon" size="mini" :disabled="!addBtnAble" @click="changeScreenTrack">
          {{ startOrPauseBtnLabel }}
          <el-tooltip class="item" effect="dark" placement="bottom" :append-to-body="false">
            <div slot="content">
              {{ $t('pages.track_Msg10') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
        <el-button v-if="!aspectRatioDisabled" type="primary" size="mini" @click="switchAspectRatio">
          <svg-icon :icon-class="aspectRatioOption.icon"></svg-icon>{{ aspectRatioOption.label }}
        </el-button>
        <el-button v-if="!originalDisabled" type="primary" size="mini" @click="switchOriginal">
          <svg-icon :icon-class="originalOption.icon"></svg-icon>{{ originalOption.label }}
        </el-button>
        <el-button type="primary" :icon="fullScreenOption.icon" size="mini" @click="switchFullScreen">
          {{ fullScreenOption.label }}
        </el-button>
      </div>
      <div style="width: 100%; height: calc(100% - 40px); padding: 5px; border: 1px solid #666; overflow: auto;">
        <el-canvas
          ref="trackCanvas"
          :show-toolbar="false"
          :show-mask="false"
          :class="cursorStyle"
          @drawEnd="trackDrawEnd"
          @enlarge="enlarge"
          @original="original"
          @aspectRatio="aspectRatio"
        ></el-canvas>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import elCanvas from '@/components/Canvas'
import { getScreenTrack, getMultiPleTrack } from '@/api/assets/systemMaintenance/system'
import { enableCtrlTerm, ctrlErrorMap, enableCtrlMultipleTerm } from '@/api/system/terminalManage/moduleConfig'
import { getPropertyByCode } from '@/api/property'
import MessageBox from '@/components/MessageBox'

export default {
  name: 'Track',
  components: { elCanvas, MessageBox },
  data() {
    return {
      height: '100%',
      width: '100%',
      trackTime: 1,
      startBtnIcon: 'el-icon-video-play',
      startBtnLabel: this.$t('pages.startTrack'),
      pauseBtnIcon: 'el-icon-video-pause',
      pauseBtnLabel: this.$t('pages.pauseTrack'),
      startOrPauseBtnIcon: null,
      startOrPauseBtnLabel: null,
      isEnlarge: false,
      isOriginal: false,
      isAspectRatio: false,
      isMultiple: false,
      isFullScreen: false,
      rowData: [],
      showTree: true,
      flag: true, // 轮询获取图片判断上一张图片是否以获取完
      screenMap: {},
      screenType: {},
      screenCount: 0,
      imageTypeMap: {
        2: 'gif',
        3: 'jpg',
        4: 'png'
      },
      lastTrackTerminalId: null,
      mill: 0,
      currenTermId: null,
      isLog: false, // 是否记录管理员日志，自动刷新不应该重复记录日志
      addBtnAble: false,
      ctrlAble: false,
      ctrlAble1: false,
      screenImages: [], // 存储追踪的屏幕的信息，具体内容结构为：{data:'',tid:''}
      trackScreenMaxSize: 12, // 追踪屏幕的上限，修改需到系统高级配置
      allNodesAble: false, // 页面是否显示加载框
      treeClickAble: false,
      checkedNodesTerminal: [], // 选中的终端节点，用来控制页面的追踪按钮是否可用
      oriSizeWidth: '',
      oriSizeHeight: '',
      stracktermId: '',
      strackRandomCode: '',
      currentScreenData: [],
      trackNodes: [], // 用于记录追踪节点
      effectiveTrackIds: [], // 有效的追踪节点id
      cursorStyle: '',
      qualityType: 1,
      qualityOptions: [
        { id: 1, label: this.$t('pages.imageQuality1'), value: 1 },
        { id: 2, label: this.$t('pages.imageQuality3'), value: 2 }
      ],
      multipleScreen: [],  // 记录多屏的终端和初始屏幕数量，具体结构为 { termId: 10001, num: 1 }
      screenNumArray: [],  // 每个终端的屏幕数量，具体结构为 { termId: 10001, num: 1 }
      errorScreen: []     // 终端未返回图片信息的次数，具体结构为 {id: 10001, num: 1},num为返回的次数
    }
  },
  computed: {
    terminalTree() {
      return this.$refs['terminalTree']
    },
    canvas() {
      return this.$refs['trackCanvas']
    },
    // 禁用 固定宽高比/自适应 按钮
    aspectRatioDisabled() {
      // 放大 或者 没有追踪多个屏幕 时，禁用
      return this.isEnlarge || !this.isMultiple
    },
    // 禁用 原始尺寸/自适应 按钮
    originalDisabled() {
      // 追踪多个屏幕 且 没有放大 时, 或者 screenImages 没有数据时，禁用
      return this.isMultiple && !this.isEnlarge || this.screenImages.length == 0
    },
    // 固定宽高比/自适应
    aspectRatioOption() {
      return this.isAspectRatio ? {
        label: this.$t('components.selfAdaptation'),
        icon: 'screen-self-adaptation'
      } : {
        label: this.$t('components.fixedAspectRatio'),
        icon: 'screen-scale-down'
      }
    },
    // 原始尺寸/自适应
    originalOption() {
      return this.isOriginal ? {
        label: this.$t('components.selfAdaptation'),
        icon: 'screen-self-adaptation'
      } : {
        label: this.$t('components.originalSize'),
        icon: 'screen-original-size'
      }
    },
    // 全屏/退出全屏
    fullScreenOption() {
      return this.isFullScreen ? {
        label: this.$t('pages.exitFullScreen'),
        icon: 'el-icon-copy-document'
      } : {
        label: this.$t('pages.fullScreen'),
        icon: 'el-icon-full-screen'
      }
    },
    ...mapState({
      // 获取侧边栏打开状态
      sidebarOpened: state => state.app.sidebar.opened
    })
  },
  watch: {
    screenImages(val) {
      if (val.length == 0) {
        this.cursorStyle = 'cursor_default'
      } else {
        this.cursorStyle = 'cursor_pointer'
      }
      this.isMultiple = val.length > 1
    },
    sidebarOpened(newVal, oldVal) {
      // 监听侧边栏打开状态
      this.$nextTick(() => {
        this.canvas.changeCanvasSize()
      })
    }
  },
  created() {
    this.startOrPauseBtnIcon = this.startBtnIcon
    this.startOrPauseBtnLabel = this.startBtnLabel
    this.getTrackScreenSize()
    this.$nextTick(() => {
      this.addScreenListener()
    })
  },
  activated() {
    // 进入当前页面时，如果处于追踪状态时，重新开始追踪
    if (this.startOrPauseBtnIcon == this.pauseBtnIcon) {
      this.startScreenTrack(this.checkedNodesTerminal)
    }
  },
  deactivated() {
    // 离开当前页面时，停止追踪，但不改变按钮状态
    this.stopTrack(false)
  },
  beforeDestroy() {
    this.stopTrack(true)
    // 移除监听事件
    this.removeScreenListener()
  },
  methods: {
    // 显示提示信息，全屏和非全屏调用不同的组件
    showMessage(option) {
      this.isFullScreen ? this.$refs.messageBox.showMessage(option) : this.$message(option)
    },
    // 添加监听全屏变化事件
    addScreenListener() {
      // 全屏变化
      document.addEventListener('webkitfullscreenchange', this.handleFullScreenChange);
      document.addEventListener('mozfullscreenchange', this.handleFullScreenChange);
      document.addEventListener('msfullscreenchange', this.handleFullScreenChange);
      document.addEventListener('fullscreenchange', this.handleFullScreenChange);
      // 全屏错误
      document.addEventListener('webkitfullscreenerror', this.handleFullScreenError);
      document.addEventListener('mozfullscreenerror', this.handleFullScreenError);
      document.addEventListener('msfullscreenerror', this.handleFullScreenError);
      document.addEventListener('fullscreenerror', this.handleFullScreenError);
    },
    // 移除监听全屏变化事件
    removeScreenListener() {
      // 全屏变化
      document.removeEventListener('webkitfullscreenchange', this.handleFullScreenChange);
      document.removeEventListener('mozfullscreenchange', this.handleFullScreenChange);
      document.removeEventListener('msfullscreenchange', this.handleFullScreenChange);
      document.removeEventListener('fullscreenchange', this.handleFullScreenChange);
      // 全屏错误
      document.removeEventListener('webkitfullscreenerror', this.handleFullScreenError);
      document.removeEventListener('mozfullscreenerror', this.handleFullScreenError);
      document.removeEventListener('msfullscreenerror', this.handleFullScreenError);
      document.removeEventListener('fullscreenerror', this.handleFullScreenError);
    },
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement;
    },
    handleFullScreenError() {
      console.error('全屏错误');
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      return true
    },
    getTrackScreenSize() {
      getPropertyByCode('screen.track.max').then(resp => {
        const maxSize = resp.data.value
        this.trackScreenMaxSize = !maxSize || maxSize < 1 ? 12 : Number.parseInt(maxSize)
      })
    },
    base64ToArrayBuffer(base64) {
      var binary_string = window.atob(base64)
      var len = binary_string.length
      var bytes = []
      for (var i = 0; i < len; i++) {
        bytes.push(binary_string.charCodeAt(i))
      }
      return bytes
    },
    arrayBufferToBase64(buffer) {
      var binary = ''
      var bytes = new Uint8Array(buffer)
      var len = bytes.byteLength
      for (var i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[ i ])
      }
      return window.btoa(binary)
    },
    isTrack() {
      // 按钮显示'启动'，说明，未启用追踪
      return this.startOrPauseBtnIcon !== this.startBtnIcon
    },
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('screenTrack', termId, [3]).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          // this.addBtnAble = false
          this.showMessage({ duration: 2000, message: ctrlErrorMap(resp.data) })
        } else {
          // this.addBtnAble = true
          this.ctrlAble = true
        }
      })
    },
    async checkCtrlAble1(termId) {
      this.ctrlAble1 = false
      await enableCtrlTerm('screenTrack', termId, [3]).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble1 = false
        } else {
          this.ctrlAble1 = true
        }
      })
      return this.ctrlAble1
    },
    changeTimeTrack: function() {
      const curNodeData = this.terminalTree.getCurrentNode()
      if (curNodeData && this.isTrack()) {
        this.stopTrack(true)
        this.startTrack(curNodeData)
      }
    },
    changeTrack: function() {
      const curNodeData = this.terminalTree.getCurrentNode()
      if (curNodeData) {
        if (this.isTrack()) {
          this.stopTrack(true)
        } else {
          this.isLog = true
          this.startTrack(curNodeData)
        }
      } else {
        this.showMessage({
          message: this.$t('pages.track_Msg'),
          type: 'error',
          duration: 2000
        })
      }
    },
    startTrack(nodeData) {
      if (nodeData.id.indexOf('G' + nodeData.dataId) >= 0) {
        this.showMessage({
          message: this.$t('pages.track_Msg'),
          type: 'error',
          duration: 2000
        })
        return // 终端组不能追踪
      }
      this.startOrPauseBtnIcon = this.pauseBtnIcon
      this.startOrPauseBtnLabel = this.pauseBtnLabel

      const termId = nodeData.dataId
      clearInterval(this.timer)
      this.currenTermId = termId
      this.mill = new Date().getTime()
      this.getScreenData(termId)
      this.timer = setInterval(() => { this.getScreenData(termId) }, this.trackTime * 1000)
      this.lastTrackTerminalId = termId // 记录最新一次追踪的终端ID
    },
    getScreenData(termId) {
      if (!this.flag) {
        // 与上次请求时间判断，如果长时间不返回的话，重新请求数据
        const nowTime = new Date().getTime()
        if ((nowTime - this.mill) > (this.trackTime * 1000 + 60000)) {
          this.flag = true
          this.mill = nowTime
          console.log('screen data response timeout, request data again！')
        }
      }
      // 轮询必须是上一张图片接受完才能继续请求下一张图片
      // 所以要怎么判断上一张图片接受完了呢
      // 首先发一次请求，会返回总屏幕，当前屏幕，总帧数，当前帧数
      // 必须所有屏幕的数据接收好了，屏幕数据是否接收好，要根据当前帧数是否等于总帧数
      if (this.flag) {
        this.flag = false
        this.screenMap = {} // 存放屏幕数据，每个屏幕对应一个属性
        this.screenCount = 0 // 数据接收完整的屏幕数量
        getScreenTrack(termId, this.isLog).then(respond => {
          this.isLog = false
          if (this.currenTermId == termId) {
            this.$socket.subscribeToAjax(respond, '/getTrack', (resp, handle) => {
              this.mill = new Date().getTime()
              const isDraw = this.drawScreen(resp.data)
              if (isDraw) {
                // 因为一次请求，需要多次返回，全部返回后才完成绘画，此时才能关闭
                this.flag = true
                handle.close()
              }
            })
          }
        }).catch(e => {
          this.isLog = false
        })
      }
    },
    drawScreen(data) {
      // 或取当前屏幕已缓存的数据
      let screenCurren = this.screenMap[data.screenCurren]
      if (screenCurren == undefined || screenCurren == null) {
        // 为空说明第一次或取该屏幕数据，初始化一个对象给它
        screenCurren = {}
      }
      // 把当前帧的数据放入屏幕中
      screenCurren[data.currenFrame] = data.base64Data
      // 再存回去
      this.screenMap[data.screenCurren] = screenCurren
      this.screenType[data.screenCurren] = data.imageType
      // 当前已接收屏幕的帧数
      const frameCount = Object.keys(screenCurren)
      if (frameCount.length == data.totalFrame) { // 如果总帧数等于已接收帧数，说明该屏幕数据接收完成
        // 已完成屏幕数量加1
        this.screenCount++
      }
      if (this.screenCount == data.screenCount) { // 如果已完成的屏幕等于总屏幕，说明接收完整
        // 终端屏幕图片数据。如果终端是多屏显示，则会返回多张图片，因此要用数组来保存。
        const images = []
        // 获取屏幕下标（注：终端如果是多屏显示，则每次请求返回的数据会返回多个屏幕的图片）
        const screenKey = Object.keys(this.screenMap).sort()
        // 这边是遍历每个屏幕key，获取屏幕图片数据
        screenKey.forEach(screenNo => {
          const screenImage = this.screenMap[screenNo]
          let imageData = []
          // 每张屏幕图片如果太大，还会把图片拆分为多帧返回，因此还要把很多帧的数据合并为一张图片
          const frameKeys = Object.keys(screenImage).sort((a, b) => {
            // 这边是对帧下标进行排序，然后按照帧顺序合并图片数据才会是正确的图片
            return parseInt(a) - parseInt(b)
          })
          // 拼接图片数据
          frameKeys.forEach(frameNo => {
            // 拼接前要使用base64ToArrayBuffer方法吧base64格式的数据变成字节数据，如果直接拼接base64数据是不对的。
            imageData = imageData.concat(this.base64ToArrayBuffer(screenImage[frameNo]))
          })
          // 然后再把二进制字节数据转换成base64数据
          const base64 = this.arrayBufferToBase64(imageData)
          // 存入追踪图片数组
          const imgType = this.imageTypeMap[this.screenType[screenNo]]
          images.push(`data:image/${imgType};base64,` + base64)
        })
        // this.canvas.newDrawImageList(images)
        this.canvas.drawImageList(images)
        return true
      } else {
        return false
      }
    },
    stopTrack(changeIcon) {
      if (changeIcon) {
        this.startOrPauseBtnIcon = this.startBtnIcon
        this.startOrPauseBtnLabel = this.startBtnLabel
      }
      clearInterval(this.timer)
      this.flag = true
    },
    trackDrawEnd() {
      this.allNodesAble = false
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
      this.$nextTick(() => {
        this.canvas.changeCanvasSize()
      })
    },
    changeScreenTrack() {
      if (this.isTrack()) {
        // 启动追踪的情况下，则停止追踪
        this.allNodesAble = false
        // 停止追踪下，如果选择的终端数 == 0，禁止再次追踪
        if (this.checkedNodesTerminal.length == 0) {
          this.addBtnAble = false
        }
        this.stopTrack(true)
      } else {
        // this.allNodesAble = true
        // 第一次开启追踪， 才记录追踪日志
        this.isLog = true
        // 如果开始追踪时，是显示原始尺寸，则切换回自适应
        if (this.isOriginal) {
          this.switchOriginal()
        }
        // 记录当前追踪的终端具体有哪些，当开启屏幕追踪的情况下，切换追踪间隔时间需要传入
        this.clearCanvas()
        // 先将屏幕划分成多个区域
        this.separateScreen()
        this.startScreenTrack(this.checkedNodesTerminal)
      }
    },
    startScreenTrack(data) {
      this.startOrPauseBtnIcon = this.pauseBtnIcon
      this.startOrPauseBtnLabel = this.pauseBtnLabel
      // 开始追踪之前先清除定时器
      clearInterval(this.timer)
      this.mill = new Date().getTime()
      // 设置定时器之前先请求一次屏幕数据， data为请求节点
      this.getScreenTrackData(data)
      this.timer = setInterval(() => {
        this.getScreenTrackData(this.checkedNodesTerminal)
      }, this.trackTime * 1000)
    },
    switchAspectRatio() {
      this.canvas.switchAspectRatio()
    },
    enlarge(val) {
      this.isEnlarge = val
    },
    aspectRatio(val) {
      this.isAspectRatio = val
    },
    switchOriginal() {
      this.canvas.switchImgSize()
    },
    original(val) {
      this.isOriginal = val
    },
    switchFullScreen() {
      const $el = this.$el
      if (!$el) return
      if (!this.isFullScreen) {
        this.enterFullScreen($el)
      } else {
        this.exitFullScreen()
      }
      setTimeout(() => {
        this.canvas.reDrawImageList()
      }, 10);
    },
    // 让元素 dom 进入全屏模式，如果你想让整个网页进入全屏模式，你可以使用document作为这个元素。
    enterFullScreen(dom) {
      if (dom.documentElement && dom.documentElement.requestFullscreen) {
        dom.documentElement.requestFullscreen();
      } else if (dom.mozRequestFullScreen) { /* Firefox */
        dom.mozRequestFullScreen();
      } else if (dom.webkitRequestFullscreen) { /* Chrome, Safari & Opera */
        dom.webkitRequestFullscreen();
      } else if (dom.msRequestFullscreen) { /* IE/Edge */
        dom.msRequestFullscreen();
      }
    },
    // 退出全屏模式
    exitFullScreen() {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) { /* Firefox */
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) { /* Chrome, Safari and Opera */
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) { /* IE/Edge */
        document.msExitFullscreen();
      }
    },
    // 清空画布,清空图像
    clearCanvas() {
      this.canvas.clean()
      this.screenImages = []
      this.isOriginal = false
    },
    async getScreenTrackData(data) {
      if (data.length == 0) {
        this.mill = new Date().getTime()
        return
      }
      if (!this.flag) {
        // 与上次请求时间判断，长时间不返回的话， 重新请求数据
        const nowTime = new Date().getTime()
        const trackTimeout = this.trackTime * 1000 + 15000
        const nowTracktime = nowTime - this.mill
        if (nowTracktime > trackTimeout) {
          this.checkTrackConnection()
          this.flag = true
          this.mill = nowTime
          console.log('screen data response timeout, request data again!')
        }
      }
      // 轮询必须是上一张图片接受完才能继续请求下一张图片
      // 所以要怎么判断上一张图片接受完了呢
      // 首先发一次请求，会返回总屏幕，当前屏幕，总帧数，当前帧数
      // 必须所有屏幕的数据接收好了，屏幕数据是否接收好，要根据当前帧数是否等于总帧数
      if (this.flag) {
        this.checkScreenNum()
        this.flag = false
        getMultiPleTrack(data, this.qualityType, this.isLog).then(respond => {
          console.log('screen tack start request time： ' + JSON.stringify(this.formatDate(new Date())))
          this.screenMap = {} // 存放屏幕数据，每个屏幕对应一个属性，每一次接收之前都需要先清空
          this.screenCount = 0 // 数据接收完整的屏幕数量，每一次接收之前都需要先清空
          this.screenType = {}
          this.isLog = false
          this.currentScreenData = []
          this.effectiveTrackIds = []
          var screenLengh = this.checkedNodesTerminalNum
          var num = 0
          this.$socket.subscribeToAjax(respond, '/getTrack', (resp, handle) => {
            console.log('webSocket start request time： ' + resp.data.termId + '--' + JSON.stringify(this.formatDate(new Date())))
            this.screenMap = {}
            this.screenType = {}
            this.screenCount = 0
            this.mill = new Date().getTime()
            // 接收终端返回的信息
            const isDraw = this.multipleDrawScreenTrack(resp.data)
            // 绘画
            this.canvas.drawImageList(this.screenImages)
            // this.canvas.newDrawImageList(this.screenImages)
            if (isDraw) {
              if (!this.effectiveTrackIds.includes(resp.data.termId)) {
                this.effectiveTrackIds.push(resp.data.termId)
              }
              this.errorScreen = this.errorScreen.filter(data => Number.parseInt(data.id) != Number.parseInt(resp.data.termId))
              this.currentScreenData.forEach((item, index) => {
                if (item.id == resp.data.termId) {
                  this.currentScreenData.splice(index, 1)
                }
              })
              num = num + 1
              this.allNodesAble = false
            }
            if (num == screenLengh) {
              this.flag = true
            }
            console.log('webSocket end request time： ' + resp.data.termId + '--' + JSON.stringify(this.formatDate(new Date())))
          })
          console.log('screen tack end request time： ' + JSON.stringify(this.formatDate(new Date())))
        }).catch(e => {
          this.isLog = false
        })
      }
    },
    checkScreenNum() {
      // 如果某台终端的屏幕数量大于1且在multipleScreen不存在，说明这台终端最开始屏幕数量为1，后面在追踪过程中增加了所以需要将这个多屏终端加入multipleScreen
      var multipleChangeFlag = false
      this.screenNumArray.forEach(screen => {
        if (screen.num > 1 && !this.multipleScreen.find(data => data.termId === screen.termId)) {
          this.multipleScreen.push(screen)
          multipleChangeFlag = true
        }
      })
      var removeMultipleIds = []
      this.multipleScreen.forEach(item => {
        var obj = null
        this.screenNumArray.forEach(data => {
          if (data.termId === item.termId && data.num != item.num) {
            obj = Object.assign({}, data)
          }
        })
        // 终端数量发生改变
        if (obj) {
          multipleChangeFlag = false
          if (obj.num < item.num) {
            // 屏幕数量减少,更新存储屏幕图像的数组
            // 假设原本有5个屏幕，则index为0，1，2，3，4  num = 5 现在屏幕为3个，则index为0，1，2 num = 3
            const index = obj.num - 1
            this.screenImages = this.screenImages.filter(img => img.tid != obj.termId || (img.tid === obj.termId && img.index <= index))
            // 只是屏幕数量减少不是取消追踪，不需要改变终端树，只需要重新绘制画布并更新屏幕数量
            this.canvas.drawImageList(this.screenImages)
            if (obj.num > 1) {
              // 减少后仍是多屏则更新屏幕数量
              item.num = obj.num
            } else {
              // 减少后是单屏，则从multipleScreen数组移除
              removeMultipleIds.push(obj.termId)
            }
          } else {
            // 这里obj.num只会大于item.num  因为item.num = obj.num的话obj为null到不了该方法体
            // 屏幕数量增多，重新绘制画布并更新屏幕数量
            this.canvas.drawImageList(this.screenImages)
            item.num = obj.num
          }
        }
      })
      if (removeMultipleIds.length > 0) {
        this.multipleScreen.filter(screen => removeMultipleIds.indexOf(screen.termId) < 0)
      }
      if (multipleChangeFlag) {
        this.canvas.drawImageList(this.screenImages)
      }
    },
    checkTrackConnection() {
      const stopNodes = []
      this.trackNodes.forEach((node, index) => {
        if (!this.effectiveTrackIds.includes(Number.parseInt(node.dataId))) {
          stopNodes.push(node)
        }
      })
      if (stopNodes.length > 0) {
        stopNodes.forEach(node => {
          var errorScreenFlag = false
          this.errorScreen.forEach(data => {
            if (Number.parseInt(node.dataId) === Number.parseInt(data.id)) {
              errorScreenFlag = true
              data.num = Number.parseInt(data.num) + 1
            }
          })
          if (!errorScreenFlag) {
            const obj = {
              id: node.dataId,
              num: 1
            }
            this.errorScreen.push(obj)
          }
        })
        const nodeName = []
        stopNodes.forEach(item => {
          this.errorScreen.forEach(data => {
            if (Number.parseInt(data.id) === Number.parseInt(item.dataId)) {
              // 连续两次未返回才认为是连接异常
              if (data.num > 2) {
                nodeName.push(item.label)
              }
            }
          })
        })
        if (nodeName && nodeName.length > 0) {
          this.showMessage({
            message: nodeName.join(',') + this.$t('pages.screenTrack_Msg'),
            type: 'warning',
            duration: 2000
          })
        }
      }
    },
    formatDate(date) {
      var year = date.getFullYear();
      var month = ('0' + (date.getMonth() + 1)).slice(-2);
      var day = ('0' + date.getDate()).slice(-2);
      var hours = ('0' + date.getHours()).slice(-2);
      var minutes = ('0' + date.getMinutes()).slice(-2);
      var seconds = ('0' + date.getSeconds()).slice(-2);
      var milliseconds = ('00' + date.getMilliseconds()).slice(-3);
      // 拼接成字符串
      return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds + ':' + milliseconds;
    },
    // 获取终端分组下上线的终端
    getTerminalAndChildrenNodes(tree) {
      var nodes = [];
      function traverse(node) {
        // 获取在线windows、mac、linux终端节点
        if (node.id && node.id.includes('T') && node.online && (Number.parseInt(node.dataType) === 0 || Number.parseInt(node.dataType) === 1 || Number.parseInt(node.dataType) === 2)) {
          nodes.push(node);
        }
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => traverse(child));
        }
      }
      traverse(tree); // 从根节点开始遍历
      return nodes;
    },
    // 获取可追踪的终端节点
    async getEnableTrackGroupTerminalNode(nodes) {
      const ids = []
      nodes.forEach(node => { ids.push(Number.parseInt(node.dataId)) })
      const resp = await enableCtrlMultipleTerm('screenTrack', ids, [3])
      const enableStatusMap = resp.data
      const enableTermIds = []
      for (const termId in enableStatusMap) {
        const status = enableStatusMap[termId]
        if (status > 0) {
          enableTermIds.push(Number.parseInt(termId))
        }
      }
      nodes = nodes.filter(node => enableTermIds.includes(Number.parseInt(node.dataId)))
      return nodes
    },
    // 勾选分组节点时过滤操作
    async checkedGroupNode(curNodes) {
      for (let i = 0; i < curNodes.length; i++) {
        const node = curNodes[i]
        if (node.id.indexOf('G') > -1) {
          this.treeClickAble = true
          const data = [...this.getTerminalAndChildrenNodes(node)]
          if (data.length === 0) {
            this.showMessage({ duration: 2000, message: this.$t('pages.track_Msg7') })
          } else {
            if (this.checkedNodesTerminal.length >= this.trackScreenMaxSize) {
              this.showMessage({ duration: 2000, message: this.$t('pages.track_Msg3', { upSize: this.trackScreenMaxSize }) })
            }
          }
        }
        if (node.id.indexOf('G') > -1 && this.checkedNodesTerminal.length < this.trackScreenMaxSize) {
          this.terminalTree.checkNode(node, false)
          var childrenTerminals = [...this.getTerminalAndChildrenNodes(node)]
          childrenTerminals = await this.getEnableTrackGroupTerminalNode(childrenTerminals)
          const remainNum = this.trackScreenMaxSize - this.checkedNodesTerminal.length
          var num = 1
          var terminalNames = []
          childrenTerminals.forEach(terminal => {
            if (this.checkedNodesTerminal.indexOf(Number.parseInt(terminal.dataId)) < 0 && num <= remainNum) {
              this.terminalTree.checkNode(terminal, true)
              terminalNames.push(terminal.label)
              num++
            }
          })
          if (terminalNames.length > 0) {
            this.showMessage({ duration: 2000, type: 'success', message: terminalNames.join(',  ') + '  ' + this.$t('pages.track_Msg9') })
          }
        }
      }
      this.treeClickAble = false
    },
    // 过滤支持追踪的终端节点
    async filterEnableNode(curNodes, oldTermIds) {
      if (curNodes.length > 0) {
        await this.checkedGroupNode(curNodes)
      }
      const checkedTermIds = []
      for (let i = 0; i < curNodes.length; i++) {
        const node = curNodes[i]
        if (node.id.indexOf('G') < 0) {
          // 只记录终端节点的数量
          checkedTermIds.push(node.dataId)
        }
      }
      // 先过滤得到所有能够进行屏幕追踪的终端
      const resp = await enableCtrlMultipleTerm('screenTrack', checkedTermIds, [3])
      const enableStatusMap = resp.data
      const enableTermIds = []
      const disableTermIds = []
      for (const termId in enableStatusMap) {
        const status = enableStatusMap[termId]
        if (status > 0) {
          enableTermIds.push(Number.parseInt(termId))
        } else {
          disableTermIds.push(termId)
        }
      }
      // 提示不支持追踪的终端
      if (disableTermIds.length > 0) {
        const msg = []
        disableTermIds.forEach(t => {
          msg.push(ctrlErrorMap(enableStatusMap[t]))
        })
        this.showMessage({ duration: 2000, message: msg.join('; ') })
      }
      // 将不支持追踪的节点设置成禁用
      for (let i = 0; i < curNodes.length; i++) {
        const node = curNodes[i]
        if (node.id.indexOf('G') >= 0 || enableTermIds.indexOf(Number.parseInt(node.dataId)) < 0) {
          this.terminalTree.checkNode(node, false)
        }
      }
      // 只支持同时追踪trackScreenMaxSize个终端的屏幕
      if (enableTermIds.length > this.trackScreenMaxSize) {
        this.showMessage({ duration: 2000, message: this.$t('pages.track_Msg3', { upSize: this.trackScreenMaxSize }) })
        const toDisableTermId = []
        for (let i = 0; enableTermIds.length > this.trackScreenMaxSize; i++) {
          if (oldTermIds.indexOf(enableTermIds[i]) < 0 || enableTermIds.length - i === 1) {
            toDisableTermId.push(enableTermIds[i])
            enableTermIds.splice(i, 1)
            i--
          }
        }
        for (let i = 0; i < curNodes.length; i++) {
          const node = curNodes[i]
          if (toDisableTermId.indexOf(Number.parseInt(node.dataId)) >= 0) {
            this.terminalTree.checkNode(node, false)
          }
        }
      }
      this.trackNodes = []
      curNodes.forEach(node => {
        if (enableTermIds.indexOf(Number.parseInt(node.dataId)) >= 0) {
          if (!this.trackNodes.includes(node)) {
            this.trackNodes.push(node)
          }
        }
      })
      return enableTermIds
    },
    async checkedNodeChange(data, node) {
      // 选中节点发生变化时，此方法为了记录当前选中的终端节点数量，用于控制启动追踪按钮是否可用
      // 第一个参数为：所有选中的节点ID，第二个参数为：所有选中的节点数据
      const oldNodeSie = this.checkedNodesTerminal.length
      const enableTermIds = await this.filterEnableNode(node, this.checkedNodesTerminal)
      this.checkedNodesTerminalNum = enableTermIds.length
      if (enableTermIds.length === 0) {
        this.addBtnAble = this.isTrack()
        this.checkedNodesTerminal = [...enableTermIds]
        this.errorScreen.forEach(screen => {
          screen.num = 1
        })
        this.multipleScreen = []
        this.screenNumArray = []
        if (this.isTrack()) {
          this.flag = true
          this.separateScreen()
        }
        return
      } else if (enableTermIds.length > 1) {
        // 追踪多个终端 且 当前是显示原始尺寸，则切换回自适应
        if (this.isOriginal) {
          this.switchOriginal()
          this.$nextTick(() => {
            this.canvas.changeCanvasSize()
          })
        }
      }
      this.checkedNodesTerminal = enableTermIds
      this.errorScreen.forEach(screen => {
        if (this.checkedNodesTerminal.indexOf(Number.parseInt(screen.id)) < 0) {
          screen.num = 1
        }
      })
      this.multipleScreen = this.multipleScreen.filter(screen => this.checkedNodesTerminal.indexOf(Number.parseInt(screen.termId)) > -1)
      this.screenNumArray = this.screenNumArray.filter(screen => this.checkedNodesTerminal.indexOf(Number.parseInt(screen.termId)) > -1)
      this.addBtnAble = true
      if (this.isTrack() && oldNodeSie !== this.checkedNodesTerminal.length) {
        this.allNodesAble = true
        // 先将屏幕划分成多个区域
        this.separateScreen()
        if (this.isTrack()) {
          this.flag = true
          this.stopTrack(true)
          this.startScreenTrack(this.checkedNodesTerminal)
        }
      }
    },
    // 记录多屏的终端和当前追踪的终端的屏幕数量
    recordMultiplrTerminal(data) {
      if (data.screenCount > 1 && !this.multipleScreen.find(item => item.termId === Number.parseInt(data.termId))) {
        // 如果终端是多屏的，且该终端是自追踪后第一次返回图像信息的，则进行记录
        // 用于判断某个终端在追踪的过程中屏幕数量是否发生了变化(多屏变为单屏)
        const obj = {
          termId: Number.parseInt(data.termId),
          num: Number.parseInt(data.screenCount)
        }
        this.multipleScreen.push(obj)
      }
      if (this.screenNumArray.find(item => item.termId === Number.parseInt(data.termId))) {
        this.screenNumArray.forEach(item => {
          if (item.termId === Number.parseInt(data.termId)) {
            item.num = Number.parseInt(data.screenCount)
          }
        })
      } else {
        const screenNumObj = {
          termId: Number.parseInt(data.termId),
          num: Number.parseInt(data.screenCount)
        }
        this.screenNumArray.push(screenNumObj)
      }
    },
    multipleDrawScreenTrack(data) {
      if (this.currentScreenData.length > 0) {
        this.currentScreenData.forEach(item => {
          if (item.id == data.termId) {
            this.screenCount = item.screenCount
            this.screenMap = item.screenMap
            this.screenType = item.screenType
          }
        })
      }
      // 或取当前屏幕已缓存的数据
      let screenCurren = this.screenMap[data.screenCurren]
      if (screenCurren == undefined || screenCurren == null) {
        // 为空说明第一次或取该屏幕数据，初始化一个对象给它
        screenCurren = {}
      }
      // 把当前帧的数据放入屏幕中
      screenCurren[data.currenFrame] = data.base64Data
      // 再存回去
      this.screenMap[data.screenCurren] = screenCurren
      this.screenType[data.screenCurren] = data.imageType
      // 当前已接收屏幕的帧数
      const frameCount = Object.keys(screenCurren)
      if (frameCount.length == data.totalFrame) { // 如果总帧数等于已接收帧数，说明该屏幕数据接收完成
        // 已完成屏幕数量加1
        this.screenCount++
      }
      if (this.screenCount == data.screenCount) { // 如果已完成的屏幕等于总屏幕，说明接收完整
        this.recordMultiplrTerminal(data)
        // 终端屏幕图片数据。如果终端是多屏显示，则会返回多张图片，因此要用数组来保存。
        // 获取屏幕下标（注：终端如果是多屏显示，则每次请求返回的数据会返回多个屏幕的图片）
        const screenKey = Object.keys(this.screenMap).sort()
        // 这边是遍历每个屏幕key，获取屏幕图片数据
        screenKey.forEach((screenNo, index) => {
          const screenImage = this.screenMap[screenNo]
          let imageData = []
          // 每张屏幕图片如果太大，还会把图片拆分为多帧返回，因此还要把很多帧的数据合并为一张图片
          const frameKeys = Object.keys(screenImage).sort((a, b) => {
            // 这边是对帧下标进行排序，然后按照帧顺序合并图片数据才会是正确的图片
            return parseInt(a) - parseInt(b)
          })
          // 拼接图片数据
          frameKeys.forEach(frameNo => {
            // 拼接前要使用base64ToArrayBuffer方法吧base64格式的数据变成字节数据，如果直接拼接base64数据是不对的。
            imageData = imageData.concat(this.base64ToArrayBuffer(screenImage[frameNo]))
          })
          // 然后再把二进制字节数据转换成base64数据
          const base64 = this.arrayBufferToBase64(imageData)
          // 存入追踪图片数组
          const imgType = this.imageTypeMap[this.screenType[screenNo]]
          const tempImageObject = this.toImgObj(imgType, base64, data, index)
          if (this.checkedNodesTerminal.findIndex(termId => termId == data.termId) >= 0) {
            let pos = null
            for (let k = 0; k < this.screenImages.length; k++) {
              const item = this.screenImages[k]
              if (item.tid == tempImageObject.tid && item.index == tempImageObject.index) {
                pos = k
              }
            }
            if (pos !== null) {
              // 替换
              this.screenImages[pos] = tempImageObject
            } else {
              // 追加
              this.screenImages.push(tempImageObject)
            }
          }
        })
        return true
      } else {
        const obj1 = {
          id: data.termId,
          screenCount: this.screenCount,
          screenMap: this.screenMap,
          screenType: this.screenType
        }
        this.currentScreenData.push(obj1)
        return false
      }
    },
    // 将屏幕划分成多个区域
    separateScreen() {
      const oldSize = this.screenImages.length
      const existTermIds = []
      for (let k = 0; k < this.screenImages.length; k++) {
        const item = this.screenImages[k]
        if (this.checkedNodesTerminal.findIndex(termId => termId == item.tid) < 0) {
          // 如果节点删除，那么需要删除缓存中的图片
          this.screenImages.splice(k, 1)
          k--
          continue
        }
        existTermIds.push(item.tid)
      }
      for (let k = 0; k < this.checkedNodesTerminal.length; k++) {
        const tid = this.checkedNodesTerminal[k]
        if (existTermIds.indexOf(tid) < 0 && existTermIds.indexOf(tid + '') < 0) {
          // 模拟节点进行展示
          const tempImgObj = this.toImgObj('png', '', { termId: tid, termName: tid }, 0)
          this.screenImages.push(tempImgObj)
        }
      }
      if (oldSize !== this.screenImages.length) {
        // 屏幕个数发生变更，先将屏幕划分成多个区域
        this.canvas.drawImageList(this.screenImages)
      }
    },
    // 将图片数据转成图片对象
    toImgObj(imgType, imgBase64, imgData, index) {
      const { termId, termName, width, height } = imgData
      // 存入追踪图片数组, index的主要作用是因为终端可能是多屏显示，用于标明该图像属于哪个终端的哪个屏幕
      return {
        data: `data:image/${imgType};base64,` + imgBase64,
        tid: termId,
        name: termName,
        width,
        height,
        index
      }
    },
    changeMultipleTimeTrack() {
      if (this.isTrack()) {
        this.stopTrack(true)
        this.startScreenTrack(this.checkedNodesTerminal)
      }
    },
    qualityChange() {
      if (this.isTrack()) {
        this.stopTrack(true)
        this.startScreenTrack(this.checkedNodesTerminal)
      }
    },
    handleBlur(event) {
      const val = event.target.value.trim()
      if (!val) {
        this.trackTime = 1
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .cursor_pointer {
    cursor: pointer;
  }
  .cursor_default {
    cursor: default;
  }
</style>
