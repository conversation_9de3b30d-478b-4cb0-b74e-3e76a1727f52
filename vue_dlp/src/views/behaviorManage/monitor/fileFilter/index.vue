<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;">{{ strategyFormatter(props.detail) }}</span>
          </div>
        </template>
      </grid-table>
    </div>
    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="110px"
        style="width: 700px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <FormItem :label="$t('pages.fileBackupParameters')">
          <el-checkbox-group v-model="temp.opCodeList" :disabled="!formable">
            <el-tooltip class="item" effect="dark" :content="$t('pages.processMonitor_Msg')" placement="bottom-start">
              <el-checkbox :label="1">{{ $t('pages.create') }}</el-checkbox>
            </el-tooltip>
            <el-checkbox :label="2">{{ $t('pages.rename') }}</el-checkbox>
            <!--<el-checkbox :label="4">删除</el-checkbox>-->
            <el-checkbox :label="8" >{{ $t('pages.copy') }}</el-checkbox>
            <el-checkbox :label="16">{{ $t('pages.open') }}</el-checkbox>
            <el-checkbox :label="32">{{ $t('pages.edit') }}</el-checkbox>
          </el-checkbox-group>
        </FormItem>

        <FormItem :label="$t('pages.backupFile')" prop="newBackupSize" style="margin-top:5px">
          <i18n path="pages.processMonitor_Msg29">
            <el-input-number slot="size" v-model="temp.newBackupSize" :disabled="!formable" step-strictly :controls="false" :min="0" :max="10240" size="mini" style="width: 100px;"/>
          </i18n>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 480px;">{{ $t('pages.processMonitor_Msg30') }}{{ $t('pages.processMonitor_Msg28') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
          <!-- <span style="color: #0c60a5;margin-left: 10px">&nbsp;{{ $t('pages.processMonitor_Msg28') }}</span> -->
          <el-button :disabled="!formable" style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
        </FormItem>
        <FormItem :label="$t('pages.processMonitor_Msg6')">
          <el-radio-group v-model="temp.dirSetup" :disabled="!formable">
            <el-radio :label="0">{{ $t('pages.processMonitor_Msg3') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.processMonitor_Msg4') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.processMonitor_Msg5') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="temp.dirSetup == 1" :label="$t('pages.driverType')" prop="fileFilterDiskType">
          <el-checkbox-group v-model="temp.fileFilterDiskType" :disabled="!formable" @change="validateItem('fileFilterDiskType')">
            <el-checkbox :label="0">{{ $t('pages.localDisk') }}</el-checkbox>
            <el-checkbox :label="2">{{ $t('pages.removableDisk') }}</el-checkbox>
            <el-checkbox :label="3">{{ $t('pages.networkDisk') }}</el-checkbox>
            <el-checkbox :label="4" >{{ $t('pages.cd') }}</el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <div v-if="temp.dirSetup!=2">
          <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
            <div slot="header">
              <span>{{ $t('pages.processMonitor_Msg7') }}</span>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.controlProcess_tip') }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-popover
                v-model="visible"
                :disabled="!formable"
                style="margin-left: 20px"
                placement="right"
                width="400"
                trigger="click"
              >
                <el-tabs v-model="activeTab">
                  <el-tab-pane label="Windows" name="Windows">
                    <tree-menu ref="processList" style="height: 350px" multiple :data="defaultProcessList" :is-filter="false" :default-expand-all="true"/>
                  </el-tab-pane>
                  <el-tab-pane label="Mac" name="Mac">
                    <tree-menu ref="processMacList" style="height: 350px" multiple :data="defaultMacProcessList" :is-filter="false" :default-expand-all="true"/>
                  </el-tab-pane>
                </el-tabs>
                <div style="text-align: right; margin-top: 10px">
                  <el-button type="primary" size="mini" @click="handleCheckedProcess">{{ $t('button.confirm') }}</el-button>
                  <el-button size="mini" @click="visible = false">{{ $t('button.cancel') }}</el-button>
                </div>
                <el-button v-if="formable" slot="reference" size="small">{{ $t('button.import') }}</el-button>
                <el-button v-if="formable" slot="reference" size="small" @click="handleProcessImport()">
                  {{ $t('button.applicationLibraryImport') }}
                </el-button>
                <el-button v-if="formable" slot="reference" size="small" @click="handleClear">
                  {{ $t('button.clear') }}
                </el-button>
              </el-popover>
            </div>
            <!-- <el-tabs v-model="activeTab">
              <el-tab-pane label="Windows" name="Windows">-->
            <FormItem prop="fileFilterApp" label-width="0px">
              <tag v-model="temp.fileFilterApp" :list="temp.fileFilterApp" input-length="30" :disabled="!formable"/>
            </FormItem>
            <!-- </el-tab-pane>
              <el-tab-pane label="Mac" name="Mac">
                <FormItem prop="fileFilterMacApp" label-width="0px">
                  <tag v-model="temp.fileFilterMacApp" :list="temp.fileFilterMacApp" :disabled="!formable"/>
                </FormItem>
              </el-tab-pane>
            </el-tabs>-->

          </el-card>

          <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
            <div slot="header">
              <span>{{ $t('pages.processMonitor_Msg8') }}</span>
              <el-popover
                v-if="temp.fileExtSetup==1 || temp.fileExtSetup==2"
                v-model="visible1"
                :disabled="!formable"
                style="margin-left: 20px"
                placement="right"
                width="400"
                trigger="click"
              >
                <tree-menu ref="suffixList" style="height: 350px" multiple :data="defaultSuffixList" :is-filter="false" :default-expand-all="true"/>
                <div style="text-align: right; margin-top: 10px">
                  <el-button type="primary" size="mini" @click="handleCheckedSuffix">{{ $t('button.confirm') }}</el-button>
                  <el-button size="mini" @click="visible1 = false">{{ $t('button.cancel') }}</el-button>
                </div>
                <el-button v-if="formable" slot="reference" size="small">{{ $t('button.import') }}</el-button>
                <el-button v-if="formable" slot="reference" size="small" @click="handleFileSuffixImport()">
                  {{ $t('button.FileSuffixLibImport') }}
                </el-button>
                <el-button v-if="formable" slot="reference" size="small" @click="handleClearSuffix">
                  {{ $t('button.clear') }}
                </el-button>
              </el-popover>
            </div>
            <el-radio-group v-model="temp.fileExtSetup" :disabled="!formable">
              <el-radio :label="2">{{ $t('pages.processMonitor_Msg9') }}</el-radio>
              <el-radio :label="1">{{ $t('pages.processMonitor_Msg10') }}</el-radio>
            </el-radio-group>
            <FormItem prop="fileFilterFileExt" label-width="0px">
              <tag v-model="temp.fileFilterFileExt" :list="temp.fileFilterFileExt" input-length="10" :disabled="!formable" @tagChange="fileFilterFileExtChange"/>
            </FormItem>
          </el-card>
          <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
            <div slot="header">
              <span>{{ $t('pages.processMonitor_Msg11') }}</span>
            </div>
            <el-radio-group v-model="temp.filepathSetup" :disabled="!formable" @change="pathRadioChange">
              <el-radio :label="0">{{ $t('pages.processMonitor_Msg12') }}</el-radio>
              <el-radio :label="2">{{ $t('pages.processMonitor_Msg13') }}</el-radio>
            </el-radio-group>
            <FormItem v-if="temp.filepathSetup==2" :label="$t('table.pointDirectory')" prop="fileFilterPath" label-width="85px" style="margin-top: 10px">
              <tag :list="temp.fileFilterPath" input-length="255" :disabled="!formable" style="margin-left: 5px;"/>
            </FormItem>
            <FormItem :label="$t('table.exceptionDirectory')" style="margin-top: 10px;" label-width="85px">
              <tag :list="temp.fileExceptFilterPath" input-length="255" :disabled="!formable" style="margin-left: 5px;"/>
            </FormItem>
          </el-card>
          <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
            <div slot="header">
              <span>{{ $t('pages.processMonitor_Msg14') }}</span>
            </div>
            <el-radio-group v-model="temp.filenameSetup" :disabled="!formable" @change="nameRadioChange">
              <el-radio :label="0">{{ $t('pages.processMonitor_Msg15') }}</el-radio>
              <el-radio :label="4">{{ $t('pages.processMonitor_Msg16') }}</el-radio>
            </el-radio-group>
            <FormItem v-if="temp.filenameSetup==4" :label="$t('table.pointFileName')" prop="fileFilterFileName" label-width="85px" style="margin-top: 10px">
              <tag :list="temp.fileFilterFileName" input-length="255" :disabled="!formable" style="margin-left: 5px;"/>
            </FormItem>
            <FormItem v-if="temp.filenameSetup == 0" :label="$t('table.exceptFileName')" prop="fileExceptFilterFileName" style="margin-top: 10px;" label-width="85px">
              <tag :list="temp.fileExceptFilterFileName" input-length="255" :disabled="!formable" style="margin-left: 5px;"/>
            </FormItem>
          </el-card>
        </div>

        <span style="color: #0e7beb; padding-right: 5%; display: inline-block;">
          <i18n path="pages.processMonitor_Msg17">
            <br slot="br"/>
          </i18n>
        </span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <app-select-dlg ref="processSelectDlg" @select="processDataTable"/>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importFileFilterSetupStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
  </div>
</template>
<script>
import {
  getStrategyPage, getStrategyByName,
  createStrategy, updateStrategy, deleteStrategy
} from '@/api/behaviorManage/monitor/fileFilter'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy, timeIdValidator } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'

export default {
  name: 'FileFilter',
  components: { AppSelectDlg, ImportStg, FileSuffixLibImport, BackupRuleContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 45,
      visible: false,
      visible1: false,
      defaultProcessList: [
        { label: this.$t('pages.builtProcess'), id: 0, children: [
          { label: 'acad.exe', id: 1 },
          { label: 'arcobat.exe', id: 2 },
          { label: 'client99se.exe', id: 3 },
          { label: 'et.exe', id: 4 },
          { label: 'excel.exe', id: 5 },
          { label: 'explorer.exe', id: 6 },
          { label: 'notepad.exe', id: 7 },
          { label: 'powerPnt.exe', id: 8 },
          { label: 'userinit.exe', id: 9 },
          { label: 'winword.exe', id: 10 },
          { label: 'wordpad.exe', id: 11 },
          { label: 'wps.exe', id: 12 },
          { label: 'wpp.exe', id: 13 }
        ]
        }
      ],
      defaultMacProcessList: [
        { label: this.$t('pages.builtProcess'), id: 0, children: [
          { label: 'Finder', id: 1 },
          { label: 'DesktopServices', id: 2 },
          { label: 'Microsoft Word', id: 3 },
          { label: 'Microsoft Excel', id: 4 },
          { label: 'Microsoft PowerPoint', id: 5 },
          { label: 'Pages', id: 6 },
          { label: 'Keynote', id: 7 },
          { label: 'Numbers', id: 8 },
          { label: 'wpsoffice', id: 9 },
          { label: 'TextEdit', id: 10 }
        ]
        }
      ],
      defaultSuffixList: [
        { label: this.$t('pages.builtFileSuffix'), id: 0, children: [
          { label: '.ddb', id: 1 },
          { label: '.doc', id: 2 },
          { label: '.docx', id: 3 },
          { label: '.dwg', id: 4 },
          { label: '.html', id: 5 },
          { label: '.pcb', id: 6 },
          { label: '.pdf', id: 7 },
          { label: '.ppt', id: 8 },
          { label: '.pptx', id: 9 },
          { label: '.txt', id: 10 },
          { label: '.rtf', id: 11 },
          { label: '.xls', id: 12 },
          { label: '.xlsx', id: 13 },
          { label: '.key', id: 14 },
          { label: '.numbers', id: 15 },
          { label: '.pages', id: 16 }
        ]
        }
      ],
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', ellipsis: false, type: 'popover', originData: true, formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        timeId: 1,
        appSetup: 0,
        fileExtSetup: 2,
        dirSetup: 0,
        otherSetup: 0,
        filepathSetup: 0,
        filenameSetup: 0,
        fileFilterApp: [],
        fileFilterMacApp: [],
        fileFilterFileExt: [],
        fileFilterDiskType: [],
        opCode: 0,
        opCodeList: [],
        maxSize: 0,
        newBackupSize: 20,
        fileFilterPath: [],
        fileExceptFilterPath: [],
        fileFilterFileName: [],
        fileExceptFilterFileName: [],
        entityType: '',
        entityId: undefined,
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined // 备份过滤规则id
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.fileMonitorFilterStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.fileMonitorFilterStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        // maxSize: [
        //   { required: true, message: this.$t('components.required'), trigger: 'blur' }
        // ],
        newBackupSize: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        fileFilterApp: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        fileFilterFileExt: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        fileFilterPath: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        fileFilterFileName: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        fileFilterDiskType: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.timeRequired'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ]
      },
      submitting: false,
      activeTab: 'Windows',
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined // 备份过滤规则id
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  watch: {
    'temp.fileFilterApp'(val) {
      this.$refs.dataForm && this.$refs.dataForm.validateField('fileFilterApp')
    },
    'temp.fileFilterFileExt'(val) {
      this.$refs.dataForm && this.$refs.dataForm.validateField('fileFilterFileExt')
    },
    'temp.fileFilterPath'(val) {
      this.$refs.dataForm && this.$refs.dataForm.validateField('fileFilterPath')
    },
    'temp.fileFilterFileName'(val) {
      this.$refs.dataForm && this.$refs.dataForm.validateField('fileFilterFileName')
    },
    visible(val) {
      if (val) {
        this.clearCheckProcess()
      }
    },
    visible1(val) {
      if (val) {
        this.clearCheckSuffix()
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    pathRadioChange() {
      this.$refs.dataForm.clearValidate()
    },
    nameRadioChange() {
      this.$refs.dataForm.clearValidate()
    },
    handleCheckedProcess() {
      const checkNodes = this.$refs.processList.$refs.tree.getCheckedNodes()
      checkNodes.forEach(item => {
        if (item.id != 0 && this.temp.fileFilterApp.indexOf(item.label) < 0) {
          this.temp.fileFilterApp.push(item.label)
        }
      })
      const checkMacNodes = this.$refs.processMacList.$refs.tree.getCheckedNodes()
      checkMacNodes.forEach(item => {
        // if (item.id != 0 && this.temp.fileFilterMacApp.indexOf(item.label) < 0) {
        //   this.temp.fileFilterMacApp.push(item.label)
        // }
        // window和MAC暂时保存在统一字段
        if (item.id != 0 && this.temp.fileFilterApp.indexOf(item.label) < 0) {
          this.temp.fileFilterApp.push(item.label)
        }
      })
      this.visible = false
    },
    clearCheckProcess() {
      this.$refs.processList.$refs.tree.setCheckedKeys([])
      this.$refs.processMacList.$refs.tree.setCheckedKeys([])
    },
    handleCheckedSuffix() {
      const checkNodes = this.$refs.suffixList.$refs.tree.getCheckedNodes()
      checkNodes.forEach(item => {
        if (item.id != 0 && this.temp.fileFilterFileExt.indexOf(item.label) == -1) {
          this.temp.fileFilterFileExt.push(item.label)
        }
      })
      this.visible1 = false
    },
    clearCheckSuffix() {
      this.$refs.suffixList.$refs.tree.setCheckedKeys([])
    },

    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        timeId: 1,
        appSetup: 1,
        fileExtSetup: 2,
        dirSetup: 0,
        otherSetup: 0,
        filepathSetup: 0,
        filenameSetup: 0,
        fileFilterApp: [],
        fileFilterMacApp: [],
        fileFilterFileExt: [],
        fileFilterDiskType: [],
        fileExceptFilterPath: [],
        fileExceptFilterFileName: [],
        opCode: 0,
        opCodeList: [],
        maxSize: 0,
        newBackupSize: 20,
        fileFilterPath: [],
        fileFilterFileName: [],
        entityType: '',
        entityId: undefined
      }
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      if (!row.fileExceptFilterPath) {
        this.temp.fileExceptFilterPath = []
      }
      if (!row.fileExceptFilterFileName) {
        this.temp.fileExceptFilterFileName = []
      }
      this.$set(this.temp, 'opCodeList', this.numToList(this.temp.opCode, 6))
      this.$set(this.temp, 'filepathSetup', this.temp.otherSetup & 2)
      this.$set(this.temp, 'filenameSetup', this.temp.otherSetup & 4)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    dealData() {
      // 统一备份阈值
      if (this.temp.newBackupSize > 0) {
        this.temp.maxSize = this.temp.newBackupSize
      } else {
        this.temp.maxSize = -1
      }

      if (this.temp.fileFilterApp.length > 0) { // 监视进程app列表不为空，表示监视指定
        this.temp.appSetup = 1
      } else {
        this.temp.appSetup = 2
      }
      if (this.temp.dirSetup != 1) { // 如果不监视指定类型的磁盘，把磁盘类型数组设置为空
        this.temp.fileFilterDiskType = []
      }
      // 其他设置，按位算，第一位：代表是否备份操作的文件；第二位：代表是否监视指定目录；第三位：代表是否监视指定文件名；第四位：代表是否不监视指定目录(也就是是否配置了例外目录)；第五位：代表是否不监视指定文件名(也就是是否配置了例外文件名)
      this.temp.otherSetup = 0
      this.temp.opCode = this.getSum(this.temp.opCodeList)
      if (this.temp.opCodeList.length > 0) {
        this.temp.otherSetup += 1
      }
      // otherSetup为位运算
      var pathOtherSetUp = ''
      this.temp.fileExceptFilterPath && this.temp.fileExceptFilterPath.length > 0 ? pathOtherSetUp = 8 : pathOtherSetUp = 0
      var nameOtherSetUp = ''
      this.temp.fileExceptFilterFileName && this.temp.fileExceptFilterFileName.length > 0 ? nameOtherSetUp = 16 : nameOtherSetUp = 0
      this.temp.otherSetup = this.temp.otherSetup + this.temp.filepathSetup + this.temp.filenameSetup + pathOtherSetUp + nameOtherSetUp

      if (this.temp.filepathSetup == 0) {
        this.temp.fileFilterPath.splice(0)
      }
      if (this.temp.filenameSetup == 0) {
        this.temp.fileFilterFileName.splice(0)
      } else {
        this.temp.fileExceptFilterFileName = []
      }
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.dealData()
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.dealData()
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleProcessImport() {
      this.$refs.processSelectDlg.show()
    },
    processDataTable(datas) {
      if (!datas || datas.length === 0) return
      const list = this.temp.fileFilterApp
      datas.forEach(item => {
        const theIndex = list.findIndex(existApp => {
          return existApp === item.processName
        })
        if (theIndex > -1) {
          list.splice(theIndex, 1)
        }
        list.unshift(item.processName)
      })
    },
    handleClear() {
      this.temp.fileFilterApp.splice(0)
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: this.temp.name }).then(respond => {
        const bean = respond.data
        if (bean && this.temp && bean.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    validateItem(prop) {
      this.$refs.dataForm.validateField(prop)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      // 磁盘类型
      const diskTypeMap = {
        0: this.$t('pages.localDisk'),
        2: this.$t('pages.removableDisk'),
        3: this.$t('pages.networkDisk'),
        4: this.$t('pages.cd')
      }
      // 操作类型
      const opTypeMap = {
        1: this.$t('pages.create'),
        2: this.$t('pages.rename'),
        4: this.$t('pages.delete'),
        8: this.$t('pages.copy'),
        16: this.$t('pages.open'),
        32: this.$t('pages.edit')
      }
      const { opCode, dirSetup, fileFilterDiskType, fileFilterApp,
        fileFilterFileExt, fileExtSetup, fileFilterPath, fileFilterFileName, fileExceptFilterPath, fileExceptFilterFileName } = row

      const msgArray = []
      // 文件备份参数
      if (opCode != null && opCode != 0) {
        const opCodeList = this.numToList(opCode, 6)
        const opTypeText = opCodeList.map(item => opTypeMap[item])
        msgArray.push(this.$t('pages.fileBackupParameters') + `(${opTypeText})`)
      }
      // 2 不监视所有类型的磁盘
      if (dirSetup == 2) {
        msgArray.push(this.$t('pages.processMonitor_Msg22'))
      } else {
        // 0 监视所有类型的磁盘
        if (dirSetup == 0) {
          msgArray.push(this.$t('pages.processMonitor_Msg20'))
        } else {
          //  1 监视指定类型的磁盘
          const diskText = fileFilterDiskType.map(item => diskTypeMap[item])
          msgArray.push(this.$t('pages.processMonitor_Msg21') + `(${diskText})`)
        }
        // 监视的进程名
        msgArray.push(this.$t('pages.processMonitor_Msg23') + `(${[...fileFilterApp]})`)
        // 监视指定后缀的文件
        const type = { 1: this.$t('pages.processMonitor_Msg24'), 2: this.$t('pages.processMonitor_Msg25') }[fileExtSetup]
        msgArray.push(type + `(${fileFilterFileExt})`)
        // 监视指定文件目录
        if (fileFilterPath.length > 0) {
          msgArray.push(this.$t('pages.processMonitor_Msg26') + `(${fileFilterPath})`)
        }
        // 例外文件目录
        if (fileExceptFilterPath && fileExceptFilterPath.length > 0) {
          msgArray.push(this.$t('pages.exceptionDirectory') + `(${fileExceptFilterPath})`)
        }
        // 监视指定文件名
        if (fileFilterFileName.length > 0) {
          msgArray.push(this.$t('pages.processMonitor_Msg27') + `(${fileFilterFileName})`)
        }
        // 例外文件名
        if (fileExceptFilterFileName && fileExceptFilterFileName.length > 0) {
          msgArray.push(this.$t('table.exceptFileName') + `(${fileExceptFilterFileName})`)
        }
      }
      return msgArray.join('; ')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      this.temp.fileFilterFileExt = [...new Set(this.temp.fileFilterFileExt.concat(new_suffix))]
    },
    handleClearSuffix() {
      this.temp.fileFilterFileExt.splice(0)
    },
    fileFilterFileExtChange(list) {
      const newMap = new Map()
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
      })
      this.temp.fileFilterFileExt = Object.keys(newMap) || [];
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    }
  }
}
</script>
