<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="776px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="110px"
        style="width: 696px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :label-width="labelWidth"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.extractRule') }}</el-divider>
        <div style="margin-left: 30px">
          <FormItem v-for="(item, index) in relLogTypeInfo" :key="index">
            <span slot="label">
              {{ item.formLabel }}
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  <span v-html="item.formTip"></span>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </span>
            <el-checkbox-group v-model="temp[item['listKey']]" :disabled="!formable" >
              <div style="display: flex; flex-wrap: wrap">
                <el-checkbox v-for="(opt, i) in item['checkOption']" :key="i" :label="opt.value" style="min-width: 110px; margin-right: 8px">
                  {{ opt.label }}
                  <el-tooltip v-if="opt.explain" effect="dark" placement="bottom-start">
                    <div slot="content">{{ opt.explain }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </FormItem>
          <FormItem :label="$t('pages.everyDayLimitReadNum')" label-width="120px" style="margin-left: -10px">
            <el-row style="padding: 0 100px 0 16px">
              <el-col v-for="(item, index) in relLogTypeInfo" :key="index" :span="item.limitKey ? 12 : 0" style="margin-bottom: 6px">
                <i18n v-if="item.limitKey" path="pages.logNumOfRow">
                  <template slot="logType">
                    {{ item.formLabel }}
                  </template>
                  <template slot="num">
                    <el-input
                      v-model="temp[item.limitKey]"
                      class="limit-input"
                      :disabled="!formable || !existLogDrawObj[item.id]"
                      maxlength="7"
                      @change="val => temp[item.limitKey] = justInputNumber(val, 1, 1000000)"
                    >
                    </el-input>
                  </template>
                </i18n>
              </el-col>
            </el-row>
          </FormItem>
          <!-- 告警相关的配置 -->
          <!-- <el-checkbox v-model="temp.useAlarm" :disabled="!formable || (existLogDrawObj && Object.values(existLogDrawObj).every(val => !val))" style="margin: 0 110px">
            {{ $t('pages.termLogRuleTip') }}
          </el-checkbox> -->
        </div>
        <!-- 当前策略不再添加告警，暂时进行保留 -->
        <div v-show="temp.useAlarm" class="rule-class">
          <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
          <div style="margin-left: 34px">
            <FormItem :label="$t('pages.require_Msg4')" label-width="180px">
              <el-input v-model="temp.termSampTime" style="width: 80px" type="Number" maxlength="4" min="1" :disabled="!formable" @input="numberLimit(arguments[0], temp, 'termSampTime', 1, 1440)"></el-input>
              {{ $t('pages.require_Msg5') }}
            </FormItem>
            <FormItem :label="$t('pages.require_Msg6')" label-width="180px">
              <el-input v-model="temp.alarmTimes" style="width: 80px" type="Number" maxlength="4" min="1" :disabled="!formable" @input="numberLimit(arguments[0], temp, 'alarmTimes', 1, 1440)"></el-input>
              {{ $t('pages.require_Msg7') }}
            </FormItem>
          </div>
          <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
          <FormItem label-width="60px" prop="ruleId">
            <response-content
              show-select
              read-only
              prop-check-rule
              :show-check-rule="false"
              :check-empty-rule="false"
              :editable="formable"
              :prop-rule-id="temp.ruleId"
              :select-style="{ 'margin-top': '0px' }"
              @getRuleId="getRuleId"
            />
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importLogFilterStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import {
  getStrategyPage, getStrategyByName, createStrategy, updateStrategy, deleteStrategy
} from '@/api/behaviorManage/monitor/logFIlter'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent';
import { getDictLabel } from '@/utils/dictionary'

export default {
  name: 'LogFilter',
  components: { ResponseContent, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 51,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      labelWidth: '110px',
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        termSampTime: 10,
        alarmTimes: 3,
        useAlarm: false,
        ruleId: undefined,
        logTypeList: [1, 2, 4],
        logType: 7,
        entityType: '',
        entityId: undefined
      },
      // 策略配置log信息
      // id仅仅作唯一表示
      // listKey 为策略配置窗口中那些checkBox的v-model在temp对象中的key
      // limitKey 为各日志类型每天限制读取的条数；当此值为undefined时，无限制每天读取条数的次数
      // binaryTypeKey 为二进制值在temp对象中的key
      // binaryDigit 二进制位数
      // nullBinaryVal 为兼容旧策略，当字段为null时，也就是旧策略没有存储这个字段的时候，默认选中的二进制位值
      // formLabel 提取日志类型的form表单的label
      // formTip   提取日志类型的form表单的解释说明
      // stgInfoPrefix 策略信息中 各日志类型描述的前缀
      // checkOption 各日志类型后的checkBox的属性值
      relLogTypeInfo: [
        {
          id: 1,
          listKey: 'logLevelList',
          limitKey: 'systemLogLimitNum',
          binaryTypeKey: 'logLevel',
          binaryDigit: 6,
          formLabel: this.$t('pages.sysLog'),
          formTip: this.$t('pages.logFilter_text1'),
          stgInfoPrefix: this.$t('pages.sysLogLevel'),
          checkOption: [
            { label: this.$t('text.error'), value: 1 },
            { label: this.$t('text.warning'), value: 2 },
            { label: this.$t('pages.info'), value: 4 },
            { label: this.$t('pages.keyword'), value: 32 }
          ]
        },
        {
          id: 2,
          listKey: 'appEventLevelList',
          limitKey: 'appLogLimitNum',
          binaryTypeKey: 'appEventLevel',
          binaryDigit: 3,
          formLabel: this.$t('pages.appLog'),
          formTip: this.$t('pages.logFilter_text2'),
          stgInfoPrefix: this.$t('pages.appLogLevel'),
          checkOption: [
            { label: this.$t('text.error'), value: 1 },
            { label: this.$t('text.warning'), value: 2 },
            { label: this.$t('pages.info'), value: 4 }
          ]
        },
        {
          id: 3,
          listKey: 'loginLogTypeList',
          limitKey: 'loginLogLimitNum',
          binaryTypeKey: 'loginLogType',
          binaryDigit: 4,
          nullBinaryVal: 15,
          formLabel: this.$t('pages.loginLogBrief'),
          formTip: this.$t('pages.logFilter_text3'),
          stgInfoPrefix: this.$t('table.loginType'),
          checkOption: [
            { label: this.$t('pages.interactiveLogon'), value: 1, explain: this.$t('pages.interactiveLogonTip') },
            { label: this.$t('pages.netLogin'), value: 2, explain: this.$t('pages.netLoginTip') },
            { label: this.$t('pages.remoteDesktopLogin'), value: 4 },
            { label: this.$t('pages.loginLog_unlock'), value: 8 }
          ]
        },
        {
          id: 4,
          listKey: 'statusTypeList',
          limitKey: undefined,
          binaryTypeKey: 'statusType',
          binaryDigit: 6,
          nullBinaryVal: 63,
          formLabel: this.$t('pages.statusChangeLog'),
          formTip: `${this.$t('pages.logFilter_text4')}<div style="margin-top: 3px"><span style="color: red">${this.$t('pages.care')}：</span>${this.$t('pages.logFilter_text5')}</div>`,
          stgInfoPrefix: this.$t('pages.statusChangeType'),
          checkOption: [
            { label: this.$t('pages.statusSwitchOnOff'), value: 1 },
            { label: this.$t('pages.statusSleepOnOff'), value: 2 },
            { label: this.$t('pages.statusLoginOnOff'), value: 4 },
            { label: this.$t('pages.statusChangeUser'), value: 8 },
            { label: this.$t('pages.statusScreensaverOnOff'), value: 16 },
            { label: this.$t('pages.statusLockScreenOnOff'), value: 32 }
          ]
        }
      ],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.logFilterStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.logFilterStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        limitFileSize: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ]
        // ruleId: [
        //   { validator: this.ruleIdValidator, trigger: 'blur' }
        // ]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    // 存在日志提取
    existLogDrawObj() {
      let existDraw = 0
      const that = this
      const dict = {}
      this.traversalRelListHandle((obj, listKey) => {
        dict[obj.id] = (that.temp[listKey] || []).length > 0
        existDraw |= dict[obj.id]
      })
      if (existDraw == 0) {
        that.temp.useAlarm = false
      }
      return dict
    }
  },
  created() {
    this.initDefaultTemp()
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      this.temp.logTypeList = this.numToList(this.temp.logType, 3)
      this.traversalRelListHandle(opt => {
        const binaryTypeVal = row[opt.binaryTypeKey]
        if (!binaryTypeVal && binaryTypeVal !== 0 && opt.nullBinaryVal > 0) {
          this.temp[opt.listKey] = this.numToList(opt.nullBinaryVal, opt.binaryDigit)
        } else {
          this.temp[opt.listKey] = this.numToList(binaryTypeVal, opt.binaryDigit)
        }
      })
      if (row.ruleId) { this.temp.useAlarm = true }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    logTypeChange(arr) {
      if (arr.indexOf(1) == -1) {
        this.temp.logLevelList.splice(0)
      }
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const formData = this.formatPostData()
          createStrategy(formData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const formData = this.formatPostData()
          updateStrategy(formData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    // ruleIdValidator(rule, value, callback) {
    //   if (this.temp.useAlarm && !value) {
    //     callback(new Error(this.$t('pages.alarmSetup_text5')))
    //   }
    //   callback()
    // },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      let msg = ''
      let limitStr = ''
      this.traversalRelListHandle(opt => {
        let binaryVal = row[opt.binaryTypeKey]
        binaryVal = (!binaryVal && binaryVal != 0) ? opt.nullBinaryVal : binaryVal
        if (binaryVal > 0) {
          if (msg.length > 0) { msg += '，' }
          const content = this.numToList(binaryVal, opt.binaryDigit).map(item => getDictLabel(opt.checkOption, item)).filter(val => !!val).join(',')
          msg += `${opt.stgInfoPrefix}（${content}）`
          // 当旧策略没有限制条数时，默认限制提取1000
          const limitVal = opt.limitKey ? (row[opt.limitKey] || 1000) : 0
          if (limitVal && limitVal > 0) {
            if (limitStr === '') {
              limitStr += this.$t('pages.everyDayLimitReadNum') + '（'
            } else {
              limitStr += ','
            }
            limitStr += opt.formLabel + ': ' + limitVal
          }
        }
      })
      if (limitStr.length > 0) {
        msg += '，' + limitStr + '）'
      }
      return msg
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    initDefaultTemp() {
      const defaultTemp = this.defaultTemp
      this.traversalRelListHandle((obj, listKey, limitKey, binaryTypeKey) => {
        defaultTemp[listKey] = []
        defaultTemp[limitKey] = 1000
        defaultTemp[binaryTypeKey] = 0
      })
    },
    // 格式化提交的策略信息
    formatPostData() {
      const temp = this.temp
      temp.logType = 7
      this.traversalRelListHandle((obj, listKey, limitKey, binaryTypeKey) => {
        temp[binaryTypeKey] = this.getSum(temp[listKey])
      })
      // 防止抛异常后对原temp的影响
      const formData = JSON.parse(JSON.stringify(temp))
      let existLimit = 0
      this.traversalRelListHandle((obj, listKey, limitKey, binaryTypeKey) => {
        if (formData[binaryTypeKey] == 0) {
          delete formData[limitKey]
        }
        existLimit |= formData[binaryTypeKey]
      })
      if (!temp.useAlarm || existLimit == 0) {
        delete formData.termSampTime
        delete formData.alarmTimes
        delete formData.ruleId
      }
      return formData
    },
    /**
     * 递归处理 relLogTypeInfo 列表
     * @param func function 处理单条日志类型的信息
     */
    traversalRelListHandle(func) {
      if (func instanceof Function) {
        for (const obj of this.relLogTypeInfo) {
          func(obj, obj.listKey, obj.limitKey, obj.binaryTypeKey)
        }
      }
    },
    getRuleId(ruleId) {
      this.temp.ruleId = ruleId
      this.$refs['dataForm'] && this.$refs['dataForm'].validateField('ruleId')
    },
    justInputNumber(value, minNumber, maxNumber) {
      // 不会出现负数
      if (Number.isNaN(Number(value))) {
        if (typeof (value) == 'string') value = value.replace(/[^\d]/g, '')
      }
      value = Number(value)
      if (minNumber && maxNumber && minNumber > maxNumber) {
        const temp = minNumber
        minNumber = maxNumber
        maxNumber = temp
      }
      if (minNumber && value < minNumber) {
        value = minNumber
      } else if (maxNumber && value > maxNumber) {
        value = maxNumber
      }
      return value
    },
    // sysAlarmConfig -> editDlg组件中的方法
    numberLimit(value, item, type, min, max) {
      if (type === 'alarmTimes') {
        max = parseInt(1440 / item.termSampTime)
        item.alarmTimes = !value ? min : value > max ? max : value < min ? min : parseInt(value)
      } else if (type === 'termSampTime') {
        item.termSampTime = value > max ? max : value < min ? min : parseInt(value)
        const alarmMaxTimes = parseInt(1440 / item.termSampTime)
        item.alarmTimes = item.alarmTimes > alarmMaxTimes ? alarmMaxTimes : item.alarmTimes < 0 ? 0 : parseInt(item.alarmTimes)
      } else {
        if (max) {
          item.threshold = value > max ? max : value < min ? min : parseInt(value)
        } else {
          item.threshold = value < min ? min : parseInt(value)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .limit-input {
    width: 90px;
  }
  .rule-class {
    >>>.el-form-item__error {
      margin-left: 25px;
    }
  }
</style>
