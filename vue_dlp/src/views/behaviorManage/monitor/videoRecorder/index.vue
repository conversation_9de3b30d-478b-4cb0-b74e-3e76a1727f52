<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <TimeQuery v-show="activeTab==='logTab'" @getTimeParams="getTimeParams"/>
        <TimeQuery v-show="activeTab==='taskTab'" @getTimeParams="getTimeParams1"/>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <template v-if="activeTab==='logTab'">
          <el-button v-show="hasPermission('277')" ref="taskPushBtn" size="mini" :loading="submitting" :disabled="!transferable" @click="transformToMP4">
            <svg-icon icon-class="taskPush"></svg-icon>
            {{ $t('pages.convertMp4Video') }}
          </el-button>
          <audit-log-delete
            v-if="$store.getters.auditingDeleteAble"
            v-permission="'438'"
            :selection="selection"
            :date-range="dateRange"
            :delete-log="deleteLog"
            :table-getter="gridTable"
            :confirm-message="getDeleteConfirmMsg"
          />
        </template>
        <template v-else>
          <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
            <svg-icon icon-class="refresh" />
          </el-button>
          <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable1" @click="handleTaskDelete">
            {{ $t('pages.deleteTask') }}
          </el-button>
          <common-downloader
            :disabled="mp4Selection.length === 0"
            :name="getFilename"
            :button-name="$t('table.downloadVideo')"
            button-size="mini"
            @download="handleBatchDownload"
          />
        </template>
        <!-- <el-button icon="el-icon-download" size="mini" @click="handleExport">
          {{ $t('button.export') }}
        </el-button> -->
      </div>
      <el-tabs ref="tabs" v-model="activeTab" type="card" style="height: calc(100% - 40px);" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.screenVideoRecording')" name="logTab">
          <div class="table-container">
            <grid-table
              ref="logList"
              row-key="logId"
              :col-model="colModel"
              :selectable="selectable"
              :default-sort="{ prop: 'create_time' }"
              :row-data-api="rowDataApi"
              :sortable="sortable"
              :after-load="afterLoad"
              @selectionChangeEnd="selectionChangeEnd"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane v-if="hasPermission('277')" :label="$t('pages.screenMP4Task_msg2')" name="taskTab">
          <div class="table-container">
            <grid-table
              ref="taskList"
              :col-model="colModel1"
              :row-data-api="rowDataApi1"
              :after-load="afterLoad"
              @selectionChangeEnd="selectionChangeEnd1"
            />
          </div>
        </el-tab-pane>
      </el-tabs>

    </div>

    <el-dialog
      id="transformWarnDlg"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.prompt')"
      :visible.sync="dialogFormVisible1"
      :before-close="cancel"
      width="45%"
    >
      <el-card :body-style="{'max-height': '320px', 'overflow-y': 'auto'}" class="box-card" style="max-height: 300px;">
        <grid-table
          ref="transTaskList"
          :col-model="colModel2"
          :row-datas="transTaskList"
          :height="250"
          :multi-select="false"
          :show-pager="false"
        />
      </el-card>
      <div class="clearfix" style="color: #68a8d0; padding-left: 0px;">
        <span>{{ $t('pages.screenMP4Task_msg1') }}</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="reTransform()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      id="videoRecorderDlg"
      v-el-drag-dialog
      width="90%"
      top="10px"
      :modal="false"
      :close-on-click-modal="false"
      :title="$t('pages.screenVideoPlayback')"
      :visible.sync="dialogFormVisible"
      :before-close="stopFunc"
      :append-to-body="true"
    >
      <el-canvas
        ref="videoCanvas"
        :total="imageSize"
        switch-size
        :open-db="openDb"
        :db-name="dbName"
        :store-name="videoPlayId"
        @start="startFunc"
        @pause="pauseFunc"
        @missingIndex="missingIndex"
      ></el-canvas>
    </el-dialog>
  </div>
</template>

<script>
import {
  deleteLog,
  deleteMP4Task,
  downloadFile,
  exportVideoRecord,
  getLogPage,
  getMP4TaskPage,
  getTaskProgress,
  getVideoDataInfo,
  listTransformRecorder,
  playVideo,
  refleshVideo,
  resendFailedImage,
  reTransformMP4File,
  stopVideo,
  transformMP4File
} from '@/api/behaviorManage/monitor/videoRecorder'
import ElCanvas from '@/components/Canvas'
import CommonDownloader from '@/components/DownloadManager/common'
import moment from 'moment'
import { enableStgBtn } from '@/utils'
import { buildDownloadFileByName } from '@/utils/download/helper'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { alertError } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'VideoRecorder',
  components: { ElCanvas, CommonDownloader },
  data() {
    return {
      colModel: [
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getLogTabTermsInfo }},
        { prop: 'fileName', label: 'videoFileName', width: '150' },
        { prop: 'createTime', label: 'screenRecordTime', width: '150', formatter: (row, data) => logSourceFormatter(row, this.recordTimeFormatter(row, data)) },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'playVideo', click: this.handlePlay
          }]
        }
      ],
      colModel1: [
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTaskTabTermsInfo }},
        { prop: 'recorderFileName', label: 'videoFileName', width: '150' },
        { prop: 'recorderTime', label: 'screenRecordTime', width: '150', formatter: this.recordTimeFormatter },
        { prop: 'status', label: 'status', width: '150', formatter: this.statusFormatter },
        /** hidden：按钮权限控制，对应后台permissionConfig.xml对应菜单配置中的me-code属性 */
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right', hidden: !this.hasPermission('277'),
          buttons: [
            { label: 'reTransform', click: this.reTransformToMP4, disabledFormatter: this.reTransformBtnFormatter },
            { code: 277, label: 'downloadVideo', click: this.handleDownload, disabledFormatter: this.downloadBtnFormatter }
          ]
        }
      ],
      colModel2: [
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'recorderFileName', label: 'videoFileName', width: '150' },
        { prop: 'recorderTime', label: 'screenRecordTime', width: '150', formatter: (row, data) => {
          try {
            return moment(data).format('YYYY-MM-DD')
          } catch (e) {
            return ''
          }
        } },
        { prop: 'status', label: 'status', width: '150', formatter: this.statusFormatter }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '', // 结束日期，显示在弹框里的
        isTimes: false,
        searchReport: 1
      },
      query1: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '', // 结束日期，显示在弹框里的
        isTimes: false
      },
      dateRange: undefined,
      showTree: true,
      playable: false,
      transferable: false,
      submitting: false,
      selection: [],
      mp4Selection: [],
      deleteable1: false,
      dialogFormVisible: false,
      dialogFormVisible1: false,
      imageSize: 0,
      selectedCurRowData: undefined,
      videoPlayId: undefined, // 视频播放的ID
      videoPlaySubscribeHandle: undefined, // 视频播放的订阅句柄,
      visible: false,
      taskVisible: false,
      timer: undefined,
      timer1: undefined,
      taskCount: 0,
      percent: 0,
      activeTab: 'logTab',
      transfromedList: [],
      transTaskList: [],
      transfromedMsg: '',
      statusOptions: {
        0: this.$t('pages.converting'),
        1: this.$t('pages.convertFail'),
        2: this.$t('pages.convertSuccess')
      },
      sortable: true,
      logTabTermsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      taskTabTermsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      delayNo: 0, // 延时编号
      index: 0, // 当前请求的索引
      lastGetVideoDataInfoTime: 0, // 最近一次调用getVideoDataInfo方法的时间戳
      openDb: true, // 是否开启浏览器indexdb数据库存储
      dbName: 'videoRecorderDB',  // 数据库名称
      imagesNum: 0 // 已获取的图片数量
    }
  },
  computed: {
  },
  created() {
    clearInterval(this.timer)
    clearInterval(this.timer1)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    this.imagesNum = 0
  },
  beforeDestroy() {
    clearInterval(this.timer)
    clearInterval(this.timer1)
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    getFilename() {
      return `screenrecords_${this.mp4Selection.length}_${moment().format('YYYY-MM-DD HH-mm-ss')}.zip`
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    taskGridTable() {
      return this.$refs['taskList']
    },
    tabClick(pane, event) {
      const that = this
      setTimeout(function() {
        clearInterval(that.timer)
        if (that.activeTab == 'logTab') {
          if (pane.$children[0] && pane.$children[0].$refs.logList) {
            pane.$children[0].$refs.logList.execRowDataApi()
          }
        } else {
          that.taskGridTable().execRowDataApi(that.query1)
          that.resetTimer()
        }
      }, 0)
    },
    selectable(row, index) {
      return !!row.dataFileGuid || (this.$store.getters.auditingDeleteAble && this.hasPermission('438'))
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
      if (this.activeTab === 'logTab') {
        this.logTabTermsInfo = []
        for (const row of rowData) {
          const termId = row.terminalId
          const termName = row.terminalName
          const logTabTermsInfo = { id: termId, name: termName }
          this.logTabTermsInfo.push(logTabTermsInfo)
        }
      }
      if (this.activeTab === 'taskTab') {
        this.taskTabTermsInfo = []
        for (const row of rowData) {
          const termId = row.terminalId
          const termName = row.terminalName
          const taskTabTermsInfo = { id: termId, name: termName }
          this.taskTabTermsInfo.push(taskTabTermsInfo)
        }
      }
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    getTimeParams1(timeObj) {
      this.query1 = Object.assign(this.query1, timeObj)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.changeSortable()
      const { createDate, startDate, endDate, isTimes } = this.query
      this.dateRange = { createDate, startDate, endDate, isTimes }
      return getLogPage(searchQuery)
    },
    rowDataApi1: function(option) {
      const searchQuery = Object.assign({}, this.query1, option)
      return getMP4TaskPage(searchQuery)
    },
    currentChange: function(rowData) {
      this.playable = !!rowData && !!rowData.dataFileGuid
      this.selectedCurRowData = rowData
    },
    selectionChangeEnd: function(rowData) {
      this.currentChange(rowData.length === 1 ? rowData[0] : null)
      this.selection = rowData
      this.transferable = !!rowData && rowData.length > 0
    },
    selectionChangeEnd1: function(rowDatas) {
      this.deleteable1 = rowDatas && rowDatas.length > 0
      this.mp4Selection = (rowDatas || []).filter(row => row.status === 2)
    },
    strategyTargetNodeChange(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
        this.query1.objectType = checkedNode.type
        this.query1.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.query1.objectType = undefined
        this.query1.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
      this.taskGridTable().execRowDataApi(this.query1)
    },
    handleFilter() {
      let months = ''
      if (this.activeTab === 'logTab') {
        this.playable = false
        this.query.page = 1
        if (this.query.searchReport != 1) {
          backupLogList(this.query).then(res => {
            if (res.data) {
              res.data.forEach(element => {
                months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
              });
              this.$notify({
                title: this.$t('text.prompt'),
                dangerouslyUseHTMLString: true,
                message: this.$t('pages.logBackupTip') + months,
                type: 'warning',
                duration: 2000
              })
            }
          })
        }
        this.gridTable().execRowDataApi(this.query)
      } else {
        this.query1.page = 1
        this.taskGridTable().execRowDataApi(this.query1)
        this.resetTimer()
      }
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getDeleteConfirmMsg(data) {
      const today = moment(new Date()).format('YYYY-MM-DD')
      for (let i = 0; i < data.length; i++) {
        const createTime = data[i].createTime
        if (createTime && createTime.startsWith(today)) {
          return this.$t('pages.validateMsg_deleteMsg4')
        }
      }
      return this.$t('pages.validateMsg_deleteMsg')
    },
    handlePlay(row, data) {
      // 每次播放延时编号和索引归零
      this.delayNo = 0
      this.index = 0
      this.imagesNum = 0
      this.lastGetVideoDataInfoTime = 0
      this.dialogFormVisible = true
      this.imageSize = 10
      if (this.$refs['videoCanvas']) {
        this.$refs['videoCanvas'].clean()
      }
      if (this.videoPlayId) { this.stopFunc() }
      // row.searchReport = this.query.searchReport
      row.playVer = 2
      playVideo(row).then(respond => {
        this.videoPlayId = respond.data
        respond.requestId = this.videoPlayId
        this.refleshHandle = window.setInterval(() => {
          refleshVideo({ taskId: this.videoPlayId })
        }, 5000)
        this.$socket.subscribeToAjax(respond, '/play', (respond, handle) => {
          this.videoPlaySubscribeHandle = handle
          const imgInfo = respond.data
          if (imgInfo.status) {
            // 说明服务端还在下载视频文件中

          } else if (imgInfo.size) {
            // 后台统计的帧数
            this.imageSize = imgInfo.frameCount
            // 索引数据返回后代表录像数据已经缓存，那么心跳不再需要维持
            if (this.refleshHandle) {
              clearInterval(this.refleshHandle)
              this.refleshHandle = null
            }
            this.convertVideoImg()
          } else {
            if (!this.$refs['videoCanvas']) {
              this.stopFunc()
            }
            if (!imgInfo) {
              return
            }
            if (imgInfo instanceof Array) {
              // 当返回的数据为数组，说明该帧图片是差异帧，会返回屏幕有变化的区域图片数组
              this.imageSize = imgInfo[0].total
            } else {
              // 当查看录屏出现异常时，将会以数字的枚举值传送至前端
              // 前端判断枚举值后再弹窗提示
              if (typeof imgInfo === 'number') {
                alertError(imgInfo)
                this.stopFunc(false)
                return
              }
              // 否则的话返回的就是一整个屏幕截图
              this.imageSize = imgInfo.total
            }
            this.appendImageToCanvas(imgInfo)
          }
        })
      })
    },
    appendImageToCanvas(imgInfo, taskId) {
      if (!imgInfo) {
        console.log('append image to canvas error: image is empty')
        return
      }
      if (imgInfo instanceof Array) {
        // 当返回的数据为数组，说明该帧图片是差异帧，会返回屏幕有变化的区域图片数组
        imgInfo.forEach(obj => {
          obj.data = 'data:image/png;base64,' + obj.data
        })
      } else if (imgInfo.data) {
        // 否则的话返回的就是一整个屏幕截图, 像素差异帧返回的数据没有data字段
        imgInfo.data = 'data:image/png;base64,' + imgInfo.data
      }
      this.$refs['videoCanvas'].appendImage(imgInfo, taskId)
    },
    getVideoDataInfo() {
      if (this.videoPlayId) {
        getVideoDataInfo({ taskId: this.videoPlayId, index: this.index }).then(resp => {
          const taskId = resp.data.taskId
          // 响应回来的数据为非当前录像播放任务的数据，则直接返回
          if (taskId !== this.videoPlayId) {
            return
          }
          const lastIndex = resp.data.index // 后台返回最新使用解析的索引数据的索引位置
          if (!resp.data.images) {
            // 说明已经解析到最后， 删除定时器
            if (this.reGetVideoHandle) {
              clearInterval(this.reGetVideoHandle)
              this.reGetVideoHandle = null
            }
            return
          }
          if (resp.data.images.length == 0) {
            console.warn('server not exist video data for taskId=' + this.videoPlayId)
            // 索引集大于数据集，则重置
            this.imageSize = this.imagesNum
            if (this.reGetVideoHandle) {
              clearInterval(this.reGetVideoHandle)
              this.reGetVideoHandle = null
            }
            return
          }
          resp.data.images.forEach(img => {
            if (taskId !== this.videoPlayId) {
              return
            }
            this.appendImageToCanvas(img, taskId)
            this.imagesNum++
          })
          this.index = lastIndex + 1
          this.convertVideoImg()
        })
      }
    },
    convertVideoImg() {
      if (this.videoPlayId && this.index < this.imageSize) {
        if (!this.reGetVideoHandle) {
          // 定时每分钟检查一次getVideoDataInfo获取图片信息是否超时，重新获取
          this.reGetVideoHandle = window.setInterval(() => {
            if (this.index < this.imageSize && new Date().getTime() - this.lastGetVideoDataInfoTime > 60000) {
              this.getVideoDataInfo()
            }
          }, 120000)
        }
        this.lastGetVideoDataInfoTime = new Date().getTime()
        this.getVideoDataInfo()
      }
    },
    missingIndex(val) {
      // 当播放任务取消时，无需再进行补偿机制
      if (!this.videoPlayId) {
        return
      }
      if (this.selectedCurRowData && this.selectedCurRowData.playVer && this.selectedCurRowData.playVer === 2) {
        // playVer=2，说明使用新的方式播放，无法调用补偿机制
        return
      }
      resendFailedImage({ taskId: this.videoPlayId, indexes: val.join(',') }).then(response => {
        const taskId = response.data ? response.data.taskId : null
        if (this.videoPlayId && taskId === this.videoPlayId && response.data.images && response.data.images.length > 0) {
          response.data.images.forEach(imgInfo => {
            if (imgInfo instanceof Array) {
              // 当返回的数据为数组，说明该帧图片是差异帧，会返回屏幕有变化的区域图片数组
              this.imageSize = imgInfo[0].total
              imgInfo.forEach(obj => {
                obj.data = 'data:image/png;base64,' + obj.data
              })
            } else {
              // 当查看录屏出现异常时，将会以数字的枚举值传送至前端
              // 前端判断枚举值后再弹窗提示
              if (typeof imgInfo === 'number') {
                alertError(imgInfo)
                this.stopFunc(false)
                return
              }
              // 否则的话返回的就是一整个屏幕截图
              this.imageSize = imgInfo.total
              imgInfo.data = 'data:image/png;base64,' + imgInfo.data
            }
            this.$refs['videoCanvas'].appendImage(imgInfo, taskId)
          })
        }
      })
    },
    cancel() {
      this.dialogFormVisible1 = false
      this.submitting = false
      clearInterval(this.timer1)
    },
    reTransform() {
      this.dialogFormVisible1 = false
      clearInterval(this.timer1)
      const selectedData = this.gridTable().getSelectedDatas()
      this.addMP4FileTask(selectedData)
    },
    reTransformToMP4(row) {
      row.status = 0
      row.progress = 0
      row['disabled'] = true // 该字段主要用于置灰请求转换，还未响应的任务数据的【重新转换按钮】
      row['searchReport'] = this.query.searchReport
      const that = this
      clearInterval(this.timer)
      reTransformMP4File(row).then(res => {
        that.submitting = false
        that.taskGridTable().execRowDataApi()
        that.resetTimer()
        that.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.screenMP4Task_msg4'),
          type: 'success',
          duration: 2000
        })
      }).catch(res => {
        console.log(res)
        that.resetTimer()
        that.submitting = false
        that.$notify({
          title: this.$t('text.error'),
          message: this.$t('pages.screenMP4Task_msg5'),
          type: 'error',
          duration: 2000
        })
      })
    },
    resetTimer() {
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        const datas = this.taskGridTable().getDatas()
        if (datas) {
          datas.forEach(data => {
            if (data.status == 0 && data.progress != 100) {
              getTaskProgress(data.id).then(res => {
                const taskIds = this.taskGridTable().getIds()
                if (taskIds.indexOf(res.data.id) > -1) {
                  if (res.data.progress == 100 || res.data.progress == -1) {
                    this.taskGridTable().execRowDataApi()
                  } else {
                    data.progress = res.data.progress
                  }
                }
              })
            }
          })
        }
      }, 1000)
    },
    transTask() {
      return this.$refs['transTaskList']
    },
    resetTimer1() {
      clearInterval(this.timer1)
      this.timer1 = setInterval(() => {
        if (this.transTaskList) {
          this.transTaskList.forEach(data => {
            if (data.status == 0 && data.progress != 100) {
              getTaskProgress(data.id).then(res => {
                const taskIds = this.transTask().getIds()
                if (taskIds.indexOf(res.data.id) > -1) {
                  if (res.data.progress == 100 || res.data.progress == -1) {
                    const selectedData = this.gridTable().getSelectedDatas()
                    listTransformRecorder(selectedData).then(response => {
                      if (response.data && response.data.length > 0) {
                        this.transfromedList = []
                        this.transTaskList = []
                        response.data.forEach(el => {
                          this.transfromedList.push(el.dataFileGuid)
                          this.transTaskList.push(el)
                        })
                      }
                    })
                  } else {
                    data.progress = res.data.progress
                  }
                }
              })
            }
          })
        }
      }, 1000)
    },
    transformToMP4() {
      this.submitting = true
      const selectedData = this.gridTable().getSelectedDatas()
      this.transfromedList = []
      this.transTaskList = []
      listTransformRecorder(selectedData).then(response => {
        if (response.data && response.data.length > 0) {
          response.data.forEach(el => {
            this.transfromedList.push(el.dataFileGuid)
            this.transTaskList.push(el)
          })
          this.dialogFormVisible1 = true
          this.resetTimer1()
        } else {
          this.addMP4FileTask(selectedData)
        }
      })
    },
    addMP4FileTask(row) {
      if (row && row.length > 0) {
        row[0]['searchReport'] = this.query.searchReport
        transformMP4File(row).then(res => {
          this.submitting = false
          this.gridTable().execRowDataApi(this.query)
          this.taskGridTable().execRowDataApi(this.query1)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.screenMP4Task_msg6'),
            type: 'success',
            duration: 2000
          })
        }).catch(res => {
          this.submitting = false
        })
      }
    },
    refresh() {
      this.taskGridTable().execRowDataApi(this.query1)
      this.resetTimer()
    },
    handleTaskDelete() {
      const toDelete = this.taskGridTable().getSelectedDatas()
      this.$confirmBox(this.$t('pages.screenMP4Task_msg7'), this.$t('text.prompt')).then(() => {
        deleteMP4Task(toDelete).then(respond => {
          this.taskGridTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.screenMP4Task_msg8'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    startFunc(data) { // 开始播放
    },
    pauseFunc() { // 暂停播放
      this.$socket.sendToUser(this.selectedCurRowData.terminalId, '/stopPlay', this.selectedCurRowData.id)
    },
    stopFunc: function() {
      if (this.videoPlaySubscribeHandle) {
        this.videoPlaySubscribeHandle.close()
        this.videoPlaySubscribeHandle = null
      }
      if (this.refleshHandle) {
        clearInterval(this.refleshHandle)
        this.refleshHandle = null
      }
      if (this.reGetVideoHandle) {
        clearInterval(this.reGetVideoHandle)
        this.reGetVideoHandle = null
      }
      stopVideo({ taskId: this.videoPlayId }).then(respond => {
        this.videoPlayId = null
        this.dialogFormVisible = false
      })
      this.$refs['videoCanvas'].dropDB()
      this.$refs['videoCanvas'].clean()
    },
    handleBatchDownload(file) {
      this.downloadTask(this.mp4Selection, file)
    },
    handleDownload(row) {
      const filename = `${row.recorderFileName}_${row.terminalName}_${row.recorderTime.split(' ')[0]}.mp4`
      const file = buildDownloadFileByName(filename)
      file.steps = 1
      this.downloadTask([row], file)
    },
    downloadTask(data, file) {
      const opts = { file, jwt: true, topic: 'ScreenVideo' }
      downloadFile(data, opts).then(() => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('components.fileDownload'),
          type: 'success',
          duration: 3000
        })
      })
    },
    handleDrag() {

    },
    handleExport() {
      return this.exportFunc(this.query)
    },
    exportFunc(formData) {
      exportVideoRecord(formData)
    },
    recordTimeFormatter(row, data) {
      try {
        return moment(data).format('YYYY-MM-DD')
      } catch (e) {
        if (!data || data.length < 10) {
          return ''
        }
        return data.slice(0, 10)
      }
    },
    statusFormatter: function(row, data) {
      return data == 0 ? this.statusOptions[data] + this.$t('pages.screenMP4Task_msg3') + row.progress + '%' : this.statusOptions[data]
    },
    reTransformBtnFormatter: function(row, data) {
      return row.disabled
    },
    downloadBtnFormatter: function(row, data) {
      return row.status !== 2
    },
    getLogTabTermsInfo() {
      return JSON.parse(JSON.stringify(this.logTabTermsInfo))
    },
    getTaskTabTermsInfo() {
      return JSON.parse(JSON.stringify(this.taskTabTermsInfo))
    }
  }
}
</script>
<style lang="scss" scoped>
  #videoRecorderDlg >>>.el-dialog{
    height: calc(100vh - 20px);
    .el-dialog__body{
      height: calc(100% - 35px);
      max-height: none;
      overflow: hidden;
    }
  }
</style>
