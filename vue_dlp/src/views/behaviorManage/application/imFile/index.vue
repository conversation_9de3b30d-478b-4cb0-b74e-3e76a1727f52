<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="140px"
        style="width: 700px; margin-left: 30px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          label-width="100px"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeName" type="card" class="process-tab">
          <el-tab-pane :label="$t('pages.sendConfig')" name="first">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <!-- 发送文件windows端支持所有（QQ、微信、企业微信、钉钉、飞秋、旺旺、飞书,skype,tim,shiyeline）的聊天类型 -->
            <FormItem :label="$t('pages.ImFile_text9')" prop="fileSendProcess">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.ImFile_text23') }}<br/>
                  <i18n path="pages.ImFile_text24">
                    <br slot="br"/>
                  </i18n>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-select
                ref="fileSendProcTree"
                v-model="temp.fileSendProcess"
                popper-append-to-body
                clearable
                multiple
                size="mini"
                :disabled="!formable"
                :placeholder="$t('pages.ImFile_text22')"
                class="im-tree-select"
              >
                <el-option v-for="item in processData.filter(r => !r.supportType || (r.supportType & 1) > 0)" :key="item.id" :label="item.label" :value="item.id">
                  <span style="float: left">{{ item.label }}</span>
                  <span v-if="item.osType" class="option-tip">{{ item.osType }}</span>
                </el-option>
              </el-select>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="40px" prop="action">
              {{ $t('pages.ImFile_text30') }}
            </FormItem>

            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="30px" prop="action">
              <el-row>
                <el-col :span="16">
                  <el-checkbox-group v-model="approvalTypeList" style="display: inline-block" :disabled="!formable" @change="handleApprovalTypeChange">
                    <el-checkbox :label="1">{{ hasEncPermision ? $t('pages.burnMsg4') : $t('pages.burnMsg5') }}</el-checkbox>
                    <el-checkbox :disabled="approvalTypeList.indexOf(1) == -1" :label="4">{{ hasEncPermision ? $t('pages.burnMsg6') : $t('pages.burnMsg7') }}</el-checkbox>
                    <el-checkbox v-if="hasEncPermision" :label="2">{{ $t('pages.burnMsg8') }}</el-checkbox>
                    <el-checkbox v-if="hasEncPermision" :disabled="approvalTypeList.indexOf(2) == -1" :label="8">{{ $t('pages.burnMsg9') }}</el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
            </FormItem>
            <ResponseContent
              :select-style="{ 'margin-top': '5px', 'margin-left': '13px' }"
              :status="dialogStatus"
              :show-select="true"
              :rule-type-name="ruleTypeName"
              :editable="formable && ruleDisable"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="propRuleId"
              @ruleIsCheck="getRuleIsCheck"
              @getRuleId="getRuleId"
            />
            <ResponseContent
              v-if="hasEncPermision"
              :select-style="{ 'margin': '5px 0 5px 13px' }"
              :status="dialogStatus"
              :show-select="true"
              :rule-type-name="encRuleTypeName"
              :editable="formable && encRuleDisable"
              read-only
              :prop-check-rule="!!temp.isEncAlarm"
              :show-check-rule="true"
              :prop-rule-id="encPropRuleId"
              @ruleIsCheck="getEncRuleIsCheck"
              @getRuleId="getEncRuleId"
            />
            <el-divider content-position="left">{{ $t('pages.exceptRule') }}</el-divider>
            <FormItem prop="fileSendLimit" label-width="30px">
              <el-checkbox v-model="temp.disable" :disabled="!formable || (!ruleDisable && !encRuleDisable)">
                <i18n path="pages.fileSmallerThanAllowSend">
                  <el-input-number v-show="!temp.disable" slot="input" style="width:110px;height: 30px;" :disabled="true" size="mini"/>
                  <el-input
                    v-show="temp.disable"
                    slot="input"
                    v-model="temp.fileSendLimit"
                    style="width:110px;height: 30px;"
                    :disabled="!formable"
                    @input="handleInput"
                  />
                  <el-select slot="unit" v-model="temp.sendLimitUnit" :disabled="!formable || !temp.disable" style="width: 80px;height: 30px;" @change="handleChange">
                    <el-option :key="0" label="MB" :value="0"/>
                    <el-option :key="1" label="KB" :value="1" />
                  </el-select>
                </i18n>
              </el-checkbox>
              <span v-show="temp.disable && !temp.fileSendLimit" style="color:red;margin-left: 10px">{{ $t('text.pleaseEnterInfo', { info: $t('table.maxFileSize2') }) }}</span>
            </FormItem>
            <FormItem label-width="30px">
              <el-checkbox v-show="ruleDisable || encRuleDisable" v-model="temp.chatFileExceptionStatus" :disabled="!formable" :true-label="1" :false-label="0" >{{ $t('pages.formatSupportedByPictureOutgoing') }}</el-checkbox>
              <el-checkbox v-show="!ruleDisable && !encRuleDisable" :disabled="true">{{ $t('pages.formatSupportedByPictureOutgoing') }}</el-checkbox>
            </FormItem>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.downloadConfig')" name="second">
            <FormItem :label="$t('pages.ImFile_text10')">
              <el-row>
                <el-col v-for="(value, key) in downloadTypeOptions" :key="key" :span="8">
                  <el-radio v-model="temp.downloadType" :disabled="!formable" :label="key" @change="downloadTypeChange">{{ value }}</el-radio>
                </el-col>
              </el-row>
            </FormItem>
            <FormItem :label="$t('pages.ImFile_text11')" prop="fileDownloadProcess">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.ImFile_text25') }}<br/>
                  <i18n path="pages.ImFile_text24">
                    <br slot="br"/>
                  </i18n>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-select
                ref="fileDownloadProcTree"
                v-model="temp.fileDownloadProcess"
                popper-append-to-body
                clearable
                multiple
                size="mini"
                :disabled="!formable"
                :placeholder="$t('pages.ImFile_text22')"
                class="im-tree-select"
              >
                <el-option v-for="item in processData.filter(({ businessType, supportType }) => businessType == 1 || (supportType & 2) > 0)" :key="item.id" :label="item.label" :value="item.id">
                  <span style="float: left">{{ item.label }}</span>
                  <span v-if="item.osType" class="option-tip">{{ item.osType }}</span>
                </el-option>
              </el-select>
            </FormItem>
            <FormItem v-show="!temp.downloadDisable" prop="fileDownloadLimit" :label="$t('pages.fileDownloadLimit')">
              <el-row>
                <el-col :span="4">
                  <el-input-number ref="downloadLimitInput" v-model="temp.fileDownloadLimit" :disabled="temp.downloadDisable || !formable" :step="1" step-strictly :controls="false" :min="downloadLimitMin" :max="65535" size="mini"/>
                </el-col>
                <el-col :span="2">
                  <span style="padding-left: 5px">MB</span>
                </el-col>
                <el-col :span="18">
                  <span style="color: #2b7aac;">{{ $t('pages.processMonitor_Msg2') }}</span>
                </el-col>
              </el-row>
            </FormItem>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.otherConfig')" name="third">
            <FormItem :label-width="isEnglish() ? '0px' : '80px'" prop="fileBackUpLimit">
              <label>
                <i18n path="pages.backupAtSizeMB">
                  <el-input-number slot="size" v-model="temp.fileBackUpLimit" style="width: 100px" :step="1" :disabled="!formable" step-strictly :controls="false" :min="0" :max="10240" size="mini"/>
                </i18n>
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.ImFile_text0') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </label>
              <span style="color: #2b7aac;margin-left: 10px">{{ $t('pages.ImFile_text1') }}</span>
              <el-button style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
            </FormItem>
            <FormItem :label="$t('pages.loginMonitorProcess')" prop="loginMonitorProcess">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.ImFile_text26') }}<br/>
                  <i18n path="pages.ImFile_text24">
                    <br slot="br"/>
                  </i18n>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-select
                ref="loginProcTree"
                v-model="temp.loginMonitorProcess"
                popper-append-to-body
                clearable
                multiple
                size="mini"
                :disabled="!formable"
                :placeholder="$t('pages.ImFile_text22')"
                class="im-tree-select"
              >
                <el-option v-for="item in processData.filter(r => !r.supportType || (r.supportType & 4) > 0)" :key="item.id" :label="item.label" :value="item.id">
                  <span style="float: left">{{ item.label }}</span>
                  <span v-if="item.osType" class="option-tip">{{ item.osType }}</span>
                </el-option>
              </el-select>
            </FormItem>
            <FormItem :label="$t('pages.contentMonitorProcess')" prop="contentMonitorProcess">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.ImFile_text23') }}<br/>
                  <i18n path="pages.ImFile_text24">
                    <br slot="br"/>
                  </i18n>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-select
                ref="chatProcTree"
                v-model="temp.contentMonitorProcess"
                popper-append-to-body
                clearable
                multiple
                size="mini"
                :disabled="!formable"
                :placeholder="$t('pages.ImFile_text22')"
                class="im-tree-select"
              >
                <el-option v-for="item in processData.filter(r => !r.supportType || (r.supportType & 8) > 0)" :key="item.id" :label="item.label" :value="item.id">
                  <span style="float: left">{{ item.label }}</span>
                  <span v-if="item.osType" class="option-tip">{{ item.osType }}</span>
                </el-option>
              </el-select>
            </FormItem>
            <FormItem :label="$t('pages.chatDownloadMonitor')" prop="chatDownloadMonitor">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.ImFile_text25') }}<br/>
                  <i18n path="pages.ImFile_text24">
                    <br slot="br"/>
                  </i18n>
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-select
                ref="chatDownloadTree"
                v-model="temp.fileDownloadMonitorProcName"
                popper-append-to-body
                clearable
                multiple
                size="mini"
                :disabled="!formable"
                :placeholder="$t('pages.ImFile_text22')"
                class="im-tree-select"
              >
                <el-option v-for="item in processData.filter(({ businessType, supportType }) => businessType == 1 || (supportType & 16) > 0)" :key="item.id" :label="item.label" :value="item.id">
                  <span style="float: left">{{ item.label }}</span>
                  <span v-if="item.osType" class="option-tip">{{ item.osType }}</span>
                </el-option>
              </el-select>
            </FormItem>
          </el-tab-pane>
        </el-tabs>
        <br/>
        <div style="display: flex">
          <div style="white-space: pre-wrap;color: #2b7aac;width: 50px">{{ $t('text.prompt') }}：</div>
          <div>
            <span style="white-space: pre-wrap;color: #2b7aac;">{{ activeName == 'first' ? $t('pages.ImFile_text23') : activeName == 'second' ? $t('pages.ImFile_text25') : $t('pages.ImFile_text27') }}</span><br/>
            <div style="white-space: pre-wrap;color: #2b7aac;">
              <i18n path="pages.ImFile_text24">
                <br slot="br"/>
              </i18n>
            </div>
          </div>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importImFileStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      :formable="formable"
      @setBackupRule="setBackupRule"
    />
  </div>
</template>
<script>
import {
  getDataPage, getDataByName, createData, updateData, deleteData
} from '@/api/behaviorManage/application/imFile'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'

export default {
  name: 'ImFile',
  components: { ResponseContent, ImportStg, BackupRuleContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 5,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      downloadTypeOptions: { 1: this.$t('pages.ImFile_text12'), 0: this.$t('pages.ImFile_text13') },
      downloadLimitMin: 0,
      processData: [
        // osType 支持的终端类型，All：支持 Windows、Linux、Mac 终端，Windows：仅支持 Windows 终端
        // businessType: 1 为下载进程业务
        // supportType: 为进程支持的配置项(二进制位) supportType == null 全部支持, 1 - 发送进程, 2 - 下载进程, 4 - 登录进程监控, 8 - 聊天内容监控, 16 - 聊天文件下载监控
        { id: 'qq.exe', label: 'QQ', osType: 'All', businessType: 1 },
        { id: 'QIDIAN.EXE', label: this.$t('pages.enterpriseQQ'), osType: 'Windows', businessType: 1 }, // 新版企业QQ的进程名； 旧版进程名： QQEIM.EXE 上传到后端时，如果存在，添加一下
        { id: 'WeChat.exe', label: this.$t('pages.wechat'), osType: 'All', businessType: 1 },
        { id: 'WXWork.exe', label: this.$t('pages.enterpriseWeChat'), osType: 'Windows', businessType: 1 },
        { id: 'DingTalk.exe', label: this.$t('pages.dingTalk'), osType: 'All', businessType: 1 },
        { id: 'FeiQ.exe', label: this.$t('pages.feiQ'), osType: 'Windows' },
        { id: 'AliIM.exe', label: this.$t('pages.aliTalk'), osType: 'Windows' },
        { id: 'FeiShu.exe', label: this.$t('pages.feiShu'), osType: 'All', businessType: 1 },
        { id: 'skype.exe', label: 'Skype', osType: 'Windows' },
        { id: 'tim.exe', label: this.$t('pages.tim'), osType: 'Windows', businessType: 1 },
        { id: 'SHIYELINE.EXE', label: this.$t('pages.shiyeLine'), osType: 'Windows' },
        { id: 'EMOBILE10.EXE', label: this.$t('pages.emobile10'), osType: 'Windows', supportType: 3 }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { },
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        active: false,
        remark: '',
        disable: false,                         // 控制发送文件发送规则是否可用
        fileSendLimit: 20,                      // 文件发送限制
        sendLimitUnit: 0,                       // FileSendLimit 值的单位大小 0：MB （为了兼容旧版）1：KB
        chatFileExceptionStatus: 0,
        chatFileExceptionSuffix: [
          { fileMonType: 1, suffixCheckType: 1, suffix: '.png' },
          { fileMonType: 1, suffixCheckType: 1, suffix: '.jpg' },
          { fileMonType: 1, suffixCheckType: 1, suffix: '.bmp' },
          { fileMonType: 1, suffixCheckType: 1, suffix: '.gif' },
          { fileMonType: 1, suffixCheckType: 1, suffix: '.tif' }
        ],
        fileDownloadLimit: 0,                   // 文件下载限制(MB)，0-65535，0表示不限制
        fileBackUpLimit: 20,                    // 文件备份限制
        downloadType: '1',                      // 下载限制 是否允许下载（后台无对应字段禁止下载时fileDownloadLimit值为-1）
        downloadDisable: false,
        loginMonitorProcess: [],
        contentMonitorProcess: [],
        fileSendProcess: [],
        fileDownloadProcess: [],
        entityType: '',
        entityId: null,
        isAlarm: 0,                             // 明文文件触发响应规则
        isEncAlarm: 0,                          // 密文文件触发响应规则
        ruleId: null,                           // 明文文件响应规则id
        encRuleId: null,                        // 密文文件响应规则id
        isLimit: 0,                             // 1-限制外发 0-不限制外发
        approvalType: 0,                        // 位与 审批外发 1-明文文件禁止外发, 2-密文文件禁止外发, 4-允许明文文件外发, 8-允许密文文件外发
        isBackupRule: 0,                        // 是否配置备份过滤规则
        backupRuleId: null,                     // 备份过滤规则id
        fleDownloadMonitoryType: 1,             // 监控聊天工具下载文件文件功能配置参数  1:下载文件监控，2：下载文件不监控
        fileDownloadMonitorProcName: []         // 监控聊天工具下载文件文件功能配置参数  进程
      },
      monitorItems: ['loginMonitorProcess', 'contentMonitorProcess', 'fileDownloadMonitorProcName', 'fileSendProcess', 'fileDownloadProcess'],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.ImFileStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.ImFileStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        // fileSendLimit: [{ required: true, message: this.$t('pages.ImFile_text15'), trigger: 'blur' }],
        fileBackUpLimit: [{ required: true, message: this.$t('pages.ImFile_text16'), trigger: 'blur' }],
        fileDownloadLimit: [{ required: true, message: this.$t('pages.ImFile_text17'), trigger: 'blur' }]
      },
      submitting: false,
      limitMin: 0,
      activeName: 'first',
      propRuleId: undefined,                                // 明文文件响应规则id
      encPropRuleId: undefined,                             // 密文文件响应规则id
      approvalTypeList: [],                                 // 审批外发
      hasEncPermision: true,                                // 是包含加解密模块
      ruleTypeName: this.$t('pages.burnMsg10'),             // 触发明文文件响应规则
      encRuleTypeName: this.$t('pages.burnMsg11'),          // 触发密文文件响应规则
      validRuleId: true,
      validEncRuleId: true,
      ruleDisable: false,                                   // 当明文执行规则没有配置时,明文响应规则要置灰
      encRuleDisable: false,                                // 当密文执行规则没有配置时,明文响应规则要置灰
      isBackupRule: 0,                                      // 是否配置备份过滤规则
      backupRuleId: undefined                               // 备份过滤规则id
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    processMap() {
      return this.processData.reduce((map, data) => {
        const key = data.id
        map[key] = true
        return map
      }, {})
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取注册模块
    this.listModule()
  },
  methods: {
    async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = resp.data
        this.ruleTypeName = this.hasEncPermision ? this.$t('pages.burnMsg10') : this.$t('pages.triggerResponseRule')
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    downloadTypeChange: function(value) {
      this.temp.downloadType = value
      if (value === 1 || value === '1') {
        // 允许发送文件的话，初始化为不限制
        this.temp.downloadDisable = false
        this.temp.fileDownloadLimit = 0
        this.downloadLimitMin = 0
      } else {
        this.temp.downloadDisable = true
        this.temp.fileDownloadLimit = -1
        this.downloadLimitMin = -1
      }
    },
    number() {
      if (isNaN(this.temp.fileSendLimit)) {
        this.temp.fileSendLimit = this.temp.fileSendLimit.replace(/[^\.\d]/g, '')
        this.temp.fileSendLimit = this.temp.fileSendLimit.replace('.', '')
      }
    },
    nullToZero(event) {
      if (this.temp.fileSendLimit === '') {
        this.temp.fileSendLimit = 0
      }
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getDataPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.propRuleId = undefined
      this.encPropRuleId = undefined
      this.approvalTypeList.splice(0)
      this.validRuleId = true
      this.validEncRuleId = true
      this.ruleDisable = false
      this.encRuleDisable = false
      this.activeName = 'first'
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      // { sendLimitUnit: 0 } 新增发送限制的单位 兼容旧版本没有这个字段默认为MB
      this.temp = Object.assign(this.temp, row) // copy obj

      // 下载文件大小限制(-1表示禁止下载)
      const downloadDisable = row.fileDownloadLimit == -1
      const downloadType = downloadDisable ? '0' : '1'
      this.$set(this.temp, 'downloadDisable', downloadDisable)
      this.$set(this.temp, 'downloadType', downloadType)
      // fileDownlimit是新增的属性，对于以前的策略没有这个字段
      const fileDownloadLimit = row.fileDownloadLimit == undefined || row.fileDownloadLimit == null
      if (!downloadDisable && fileDownloadLimit) {
        this.$set(this.temp, 'fileDownloadLimit', 0)
      }
      // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
      // 兼容旧版本，旧版本原先就有禁止传输的功能（根据是否有approvalType这个字段来判断是否是旧版本）
      if (row.approvalType == null || row.approvalType == undefined) {
        const { fileSendLimit } = this.temp
        // 如果是旧版本那么原先允许且有允许多大的文件外发的  改为  禁用  然后例外配置允许外发的大小
        if (fileSendLimit === -1 || fileSendLimit > 0) {
          const approvalType = this.hasEncPermision ? [1, 2] : [1]
          this.approvalTypeList.push(...approvalType)
        }
        if (row.ruleId) {
          this.propRuleId = row.ruleId
          if (this.hasEncPermision) {
            this.encPropRuleId = row.ruleId
          }
        }
      } else {
        if (this.temp.approvalType > 0) {
          this.approvalTypeList = this.numToList(this.temp.approvalType, 4)
          // 当勾选的有允许文件外发审批时，approvalType是没有保存禁止外发的值的，所以这边要做下处理
          if (this.approvalTypeList.indexOf(4) > -1) {
            this.approvalTypeList.push(1)
          }
          if (this.approvalTypeList.indexOf(8) > -1) {
            this.approvalTypeList.push(2)
          }
        }
        this.propRuleId = row.ruleId
        this.encPropRuleId = row.encRuleId
      }
      // 当明文执行规则没配置时,无法配置明文响应规则
      if (this.approvalTypeList.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      if (this.approvalTypeList.length == 0) {
        this.$set(this.temp, 'disable', false)
        this.temp.disable = false
      } else if (row.fileSendLimit > 0) {
        this.$set(this.temp, 'disable', true)
        this.temp.disable = true
      }
      // 处理文件发送限制的值（从未勾选的状态改为勾选的状态显示为20）
      if (!this.temp.disable) {
        this.temp.fileSendLimit = 20
        this.$set(this.temp, 'fileSendLimit', 20)
      }
      if (this.temp.fileDownloadMonitorProcName.length === 1 && this.temp.fileDownloadMonitorProcName[0] === '*.*') {
        this.temp.fileDownloadMonitorProcName.splice(0)
      }
      this.formatSelectedProcess(this.temp)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId && ((this.temp.disable && this.temp.fileSendLimit > 0) || !this.temp.disable)) {
          // 当允许放行图片时，默认放行图片格式：png、bmp、jpg、gif、tif。
          // 传值数组格式目的：方便客户要求自定义的场景
          let chatFileExceptionSuffix = []
          if (this.temp.chatFileExceptionStatus === 1) {
            chatFileExceptionSuffix = this.defaultTemp.chatFileExceptionSuffix
          }
          const tempData = Object.assign(JSON.parse(JSON.stringify(this.temp)), { chatFileExceptionSuffix: chatFileExceptionSuffix })
          this.formatTempFormData(tempData);
          createData(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId && !(this.temp.disable && !this.temp.fileSendLimit)) {
          let chatFileExceptionSuffix = []
          if (this.temp.chatFileExceptionStatus === 1) {
            chatFileExceptionSuffix = this.defaultTemp.chatFileExceptionSuffix
          }
          const tempData = Object.assign(JSON.parse(JSON.stringify(this.temp)), { chatFileExceptionSuffix: chatFileExceptionSuffix })
          this.formatTempFormData(tempData)
          updateData(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const msgArr = []
      if (row.approvalType >= 0) {
        const approvalList = this.numToList(row.approvalType, 4)
        if (this.hasEncPermision) {
          if (approvalList.indexOf(1) > -1) {
            // 禁止明文文件传输
            msgArr.push(this.$t('pages.burnMsg14'))
          }
          if (approvalList.indexOf(2) > -1) {
            // 禁止密文文件传输
            msgArr.push(this.$t('pages.burnMsg15'))
          }
          if (approvalList.indexOf(4) > -1) {
            // 允许明文文件外发审批
            msgArr.push(this.$t('pages.burnMsg6'))
          }
          if (approvalList.indexOf(8) > -1) {
            // 允许密文文件外发审批
            msgArr.push(this.$t('pages.burnMsg9'))
          }
        } else {
          if (approvalList.indexOf(1) > -1) {
            // 禁止文件传输
            msgArr.push(this.$t('pages.prohibitFileTransfer'))
          }
          if (approvalList.indexOf(4) > -1) {
            // 允许文件外发审批
            msgArr.push(this.$t('pages.burnMsg7'))
          }
        }
      } else if (row.fileDownloadLimit === -1) {
        if (this.hasEncPermision) {
          // 禁止明文文件传输
          msgArr.push(this.$t('pages.burnMsg14'))
          // 禁止密文文件传输
          msgArr.push(this.$t('pages.burnMsg15'))
        } else {
          // 禁止文件传输
          msgArr.push(this.$t('pages.prohibitFileTransfer'))
        }
      }
      // 例外规则
      if (row.fileSendLimit > 0) {
        const text = this.$t('pages.fileSmallerThanAllowSend', {
          input: row.fileSendLimit,
          unit: row.sendLimitUnit == 0 ? 'MB' : 'KB'
        })
        msgArr.push(text)
      }
      if (row.chatFileExceptionStatus == 1) {
        msgArr.push(this.$t('pages.formatSupportedByPictureOutgoing'))
      }

      if (this.hasEncPermision) {
        if (row.ruleId) {
          // 触发明文文件响应规则
          msgArr.push(this.$t('pages.burnMsg10'))
        }
        if (row.encRuleId) {
          // 触发密文文件响应规则
          msgArr.push(this.$t('pages.burnMsg11'))
        }
      } else {
        if (row.encRuleId) {
          // 触发响应规则
          msgArr.push(this.$t('pages.triggerResponseRule'))
        }
      }
      // if (row.fileSendLimit === 0) {
      //   msg += this.$t('pages.ImFile_text3') + (row.fileBackUpLimit == 0 ? `，${this.$t('pages.noBackup')}` : `，${this.$t('pages.ImFile_text4')}${row.fileBackUpLimit}${this.$t('pages.ImFile_text5')}`) + '；'
      // } else if (row.fileSendLimit != -1) {
      //   msg += this.$t('pages.ImFile_text6') + row.fileSendLimit + this.$t('pages.ImFile_text7') + (row.fileBackUpLimit == 0 ? `，${this.$t('pages.noBackup')}` : `，${this.$t('pages.ImFile_text4')}${row.fileBackUpLimit}${this.$t('pages.ImFile_text5')}`) + ' '
      // }
      if (row.fileDownloadLimit === 0) {
        msgArr.push(this.$t('pages.ImFile_text18'))
      } else if (row.fileDownloadLimit == undefined || row.fileDownloadLimit == null) {
        msgArr.push(this.$t('pages.ImFile_text18'))
      } else if (row.fileDownloadLimit === -1) {
        msgArr.push(this.$t('pages.ImFile_text21'))
      } else {
        msgArr.push(this.$t('pages.ImFile_text19', { size: row.fileDownloadLimit }))
      }

      if (row.fileBackUpLimit == 0) {
        msgArr.push(`${this.$t('pages.noBackup')}${this.$t('table.file')}`)
      } else {
        msgArr.push(this.$t('pages.ImFile_text28', { size: row.fileBackUpLimit }))
      }

      return msgArr.join('; ')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    formatFormData() {
      this.temp.ruleId = this.propRuleId
      this.temp.encRuleId = this.encPropRuleId
      this.temp.approvalType = this.getSum(this.approvalTypeList)
      // 当勾选了允许审批外发时，要去除掉禁止的值比如禁止审批外发为1允许审批外发为4两个都勾选时approvalType只保存4
      this.temp.approvalType = this.getSum(this.approvalTypeList)
      if (this.approvalTypeList.indexOf(4) > -1) {
        this.temp.approvalType = this.temp.approvalType - 1
      }
      if (this.approvalTypeList.indexOf(8) > -1) {
        this.temp.approvalType = this.temp.approvalType - 2
      }
      // 处理禁止传输的值,兼容旧版本   旧终端  fileSendLimit -1代表禁止传输 0代表不限制文件外发的大小
      if (this.hasEncPermision) {
        // 有加解密模块当禁止明文传输跟禁止密文传输都禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(2) > -1 && this.approvalTypeList.indexOf(4) == -1 && this.approvalTypeList.indexOf(8) == -1) {
          this.temp.isLimit = 1
          if (!this.temp.disable) {
            this.temp.fileSendLimit = -1
          }
        } else {
          if (!this.temp.disable) {
            this.temp.fileSendLimit = 0
          }
          this.temp.isLimit = 0
        }
      } else {
        // 无加解密模块，当禁止明文传输为禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(4) == -1) {
          this.temp.isLimit = 1
          if (!this.temp.disable) {
            this.temp.fileSendLimit = -1
          }
        } else {
          if (!this.temp.disable) {
            this.temp.fileSendLimit = 0
          }
          this.temp.isLimit = 0
        }
      }
      this.validRuleId = true
      this.validEncRuleId = true
      if (this.temp.isAlarm === 1 && !this.temp.ruleId) {
        this.validRuleId = false
      }
      if (this.temp.isEncAlarm === 1 && !this.temp.encRuleId) {
        this.validEncRuleId = false
      }
    },
    // 提交数据前，对部分字段进行处理
    formatTempFormData(rowData) {
      rowData.fleDownloadMonitoryType = 1
      //  聊天工具下载文件监控-进程名称
      if (rowData.fileDownloadMonitorProcName.length === 0) {
        rowData.fileDownloadMonitorProcName = ['*.*']
      }
      this.handleSelectedProcess(rowData)
    },
    // 提交数据前，对选中的进程进行处理，新旧进程名兼容等
    handleSelectedProcess(rowData) {
      this.monitorItems.forEach(prop => {
        const selected = rowData[prop] || []
        // 企业QQ 旧进程名也添加到选中数组
        if (selected.indexOf('QIDIAN.EXE') != -1 && selected.indexOf('QQEIM.EXE') == -1) {
          selected.push('QQEIM.EXE')
        }
      })
    },
    // 对已选择的进程进行去重、过滤
    formatSelectedProcess(rowData) {
      // 遍历指定属性
      this.monitorItems.forEach(prop => {
        const selected = rowData[prop] || []
        // 去重，过滤掉页面没有选项的数据
        const newSelected = Array.from(new Set(selected)).filter(data => this.processMap[data])
        rowData[prop] = newSelected
      })
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    getEncRuleId(value) {
      this.encPropRuleId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    getEncRuleIsCheck(value) {
      this.temp.isEncAlarm = value
    },
    handleApprovalTypeChange(value) {
      if (value.indexOf(4) > -1 && value.indexOf(1) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(4), 1)
      }
      if (value.indexOf(8) > -1 && value.indexOf(2) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(8), 1)
      }
      // 勾选了执行规则，响应规则才能勾选
      if (value.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (value.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      if (value.indexOf(1) === -1 && value.indexOf(2) === -1) {
        this.temp.disable = false
        this.temp.chatFileExceptionStatus = 0
      }
    },
    importSuccess() {
      this.handleFilter()
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    },
    handleInput() {
      this.temp.fileSendLimit = this.temp.fileSendLimit.replace(/^0|[^0-9]/g, '')
      if (this.temp.sendLimitUnit == 1 && this.temp.fileSendLimit > 10485760) {
        this.temp.fileSendLimit = 10485760
      } else if (this.temp.sendLimitUnit == 0 && this.temp.fileSendLimit > 10240) {
        this.temp.fileSendLimit = 10240
      }
    },
    handleChange() {
      if (this.temp.sendLimitUnit == 1 && this.temp.fileSendLimit > 10485760) {
        this.temp.fileSendLimit = 10485760
      } else if (this.temp.sendLimitUnit == 0 && this.temp.fileSendLimit > 10240) {
        this.temp.fileSendLimit = 10240
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-tab-pane {
  padding: 10px;
}
.im-tree-select >>>input::-webkit-input-placeholder {
  color: #666 !important;
}
.im-tree-select >>>input::-moz-placeholder {
  color: #666 !important;
}
.im-tree-select >>>input:-ms-input-placeholder {
  color: #666 !important;
}
>>> .el-input__inner {
  height: 30px;
}
.option-tip {
  padding-right: 10px;
  float: right;
  color: #8492a6;
  font-size: 13px;
}
</style>
