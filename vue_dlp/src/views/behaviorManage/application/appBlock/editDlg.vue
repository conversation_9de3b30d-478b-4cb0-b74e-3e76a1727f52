<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      type="drawer"
      :title="$t('pages.appBlockStg')"
      :pane-height="800"
      :stg-code="6"
      :active-able="activeAble"
      :time-able="true"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-divider v-if="showByOsType" content-position="left">{{ $t('table.detectObj') }}</el-divider>
        <el-radio-group v-if="showByOsType" v-model="temp.blockType" style="margin-left: 30px" :disabled="!formable" @change="detectionCodeChange">
          <el-radio :label="1">{{ '检测规则之内且例外规则之外的程序' }}</el-radio>
          <el-radio :label="2" style="position: relative;">
            {{ '检测规则之外或例外规则之内的程序' }}
          </el-radio>
        </el-radio-group>

        <el-divider content-position="left">{{ $t('pages.detectionRules') }}
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.blockTypeFormatter6') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-divider>
        <LocalZoomIn parent-tag="el-dialog">
          <div v-if="formable">
            <el-button size="small" @click="handleAppCreate(false)">
              {{ $t('button.insert') }}
            </el-button>
            <el-button size="small" @click="handleAppImport(false)">
              {{ $t('text.infoImport', { info: $t('pages.blockAppGroup') }) }}
            </el-button>
            <el-button size="small" :disabled="!checkAppDeleteable" @click="handleDeleteCheckedApp(false)">
              {{ $t('button.delete') }}
            </el-button>
            <el-button type="primary" icon="el-icon-search" size="small" :title="$t('table.search')" style="margin-left: 1px;height: 26px;float: right;" @click="handleFilter(false)"/>
            <el-input v-model="searchInfo" v-trim clearable :placeholder="$t('pages.inputExeName')" style="width: 200px;height: 26px;float: right;" @keyup.enter.native="handleFilter(false)"></el-input>

            <el-button v-if="showByOsType && temp.blockType == 1" size="small" :disabled="!checkAppDeleteable" style="margin-right: 5px; float:right;" @click="handleSettingResponse(1)">
              {{ '批量设置响应规则' }}
            </el-button>
          </div>
          <grid-table
            ref="checkedAppGrid"
            v-loading="loadingApp"
            :show-pager="false"
            auto-height
            :max-height="'calc(100% - 30px)'"
            :multi-select="formable"
            :col-model="detectionColModel"
            :row-datas="checkedPacketList"
            default-expand-all
            :selectable="selectable"
            @selectionChangeEnd="checkAppSelectionChangeEnd"
          />
        </LocalZoomIn>
        <el-divider v-if="showByOsType" content-position="left">{{ $t('pages.exceptRule') }}</el-divider>
        <template v-if="showByOsType">
          <LocalZoomIn parent-tag="el-dialog">
            <div v-if="formable">
              <el-button size="small" @click="handleAppCreate(true)">
                {{ $t('button.insert') }}
              </el-button>
              <el-button size="small" @click="handleAppImport(true)">
                {{ $t('text.infoImport', { info: $t('pages.blockAppGroup') }) }}
              </el-button>
              <el-button size="small" :disabled="!exceptAppDeleteable" @click="handleDeleteCheckedApp(true)">
                {{ $t('button.delete') }}
              </el-button>
              <el-button type="primary" icon="el-icon-search" size="small" :title="$t('table.search')" style="margin-left: 1px;height: 26px;float: right;" @click="handleFilter(true)"/>
              <el-input v-model="searchExceptInfo" v-trim clearable :placeholder="$t('pages.inputExeName')" style="width: 200px;height: 26px;float: right;" @keyup.enter.native="handleFilter(true)"></el-input>
              <el-button v-if="temp.blockType == 2" size="small" :disabled="!exceptAppDeleteable" style="margin-right: 5px; float:right;" @click="handleSettingResponse(2)">
                {{ '批量设置响应规则' }}
              </el-button>
            </div>
            <grid-table
              ref="exceptAppGrid"
              v-loading="loadingApp"
              :show-pager="false"
              auto-height
              :max-height="'calc(100% - 30px)'"
              :multi-select="formable"
              :col-model="exceptColModel"
              :row-datas="exceptPacketList"
              default-expand-all
              :selectable="selectable"
              @selectionChangeEnd="exceptAppSelectionChangeEnd"
            />
          </LocalZoomIn>
        </template>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <FormItem label="" label-width="0" style="margin-bottom: 20px;">
          <el-row v-if="showByOsType" style="padding-left: 10px">
            <el-col :span="24">
              <span>
                {{ temp.blockType == 1 ? $t('pages.appBlock_text1_1') : $t('pages.appBlock_text2_1') }}
              </span>
            </el-col>
            <el-col v-if="temp.blockType === 2 && !!temp.action" :span="24">
              <label style="font-weight: 500;font-size: small;color:red;">{{ checkedPacketList.length == 0 ? $t('pages.appBlock_text5') : $t('pages.appBlock_text3') }}</label>
            </el-col>
            <el-col :span="24">
              <label style="font-weight: 500;color: #409eff;font-size: small;white-space: normal">{{ $t('pages.appBlock_text4') }}</label>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ '禁止所有程序运行：勾选检测规则之外或例外规则之内的程序，检测规则配置为空且勾选禁止程序运行时，此时整个操作系统除了系统进程可以运行，其它所有程序都禁止运行' }}
                  <br>
                  {{ '允许所有程序运行：勾选检测规则之内且例外规则之外的程序，且检测规则配置为空时。' }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </el-col>
            <div v-if="showByOsType">
              <div>
                <label style="font-weight: 500;font-size: small;color:#409eff;">{{ `V5.02版本及以上的终端：${temp.blockType === 1 ? '检测规则之内' : '例外规则之内'}的程序响应规则以程序内设置的为准，若未设置以默认响应规则为准；${temp.blockType === 2 ? '检测规则之外的程序响应规则以默认响应规则为准；': ''}` }}</label>
                <label style="font-weight: 500;font-size: small;color:#409eff;">{{ `V5.02版本以下的终端以默认响应规则为准；` }}</label>
              </div>
            </div>
          </el-row>
          <label v-if="!showByOsType">
            {{ $t('pages.blockTypeFormatter5') }}
          </label>
        </FormItem>

        <div>
          <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
          <!-- 检测规则为空 -->
          <FormItem label-width="0" prop="action">
            <!-- 例外规则有配置程序，且程序有配置响应规则 -->
            <div v-if="showByOsType" style="display: flex">
              {{ '默认响应规则：' }}
              <el-checkbox v-model="temp.action" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable" @change="actionChange">{{ $t('pages.blockTypeFormatter1') }}</el-checkbox>
              <el-checkbox v-model="setExceptResponse" :disabled="!formable" @change="setExceptResponseChange">设置违规响应规则</el-checkbox>
            </div>
          </FormItem>
          <ResponseContent
            style="line-height: 15px !important;"
            :select-style="{ 'margin-top': 0 }"
            :show-select="true"
            :editable="formable"
            read-only
            :prop-check-rule="isDoResponse(temp)"
            :show-check-rule="!showByOsType"
            :prop-rule-id="temp.ruleId"
            @getRuleId="getRuleId"
            @ruleIsCheck="ruleIsCheck"
            @validate="(val) => {
              if (showByOsType) {
                setResponseValidate()
              }
              responseValidate = val
            }"
          />
        </div>
      </template>
    </stg-dialog>
    <app-add-dlg
      ref="appAddDlg"
      :os-type="osType==7?1:osType"
      :support-md5="supportMd5"
      :append-to-body="false"
      :type-tree-data="typeTreeData"
      :create="createAppInfo"
      :update="updateAppInfo"
      :create-group="createAppType"
      :update-group="deleteAppType"
      :get-group-by-name="getAppTypeByName"
      @submitEnd="appAddSubmitEndAndReloadTable"
    />
    <app-import-table
      ref="appImportTable"
      :os-type="osType==7?1:osType"
      :support-md5="supportMd5"
      :type-tree-data="typeTreeData"
      :group-root-name="$t('pages.blockAppGroup')"
      :list="getAppInfoList"
      :count-by-group="countInfoByGroupId"
      :create="createAppInfo"
      :batch-create="batchCreateAppInfo"
      :update="updateAppInfo"
      :delete="deleteAppInfo"
      :import-func="importFromLib"
      :create-group="createAppType"
      :update-group="updateAppType"
      :delete-group="deleteAppType"
      :get-group-by-name="getAppTypeByName"
      support-select-terminal-soft
      @submitEnd="appMd5ImportSubmitEnd"
      @refreshList="refreshList"
    />

    <response-select-dlg ref="responseSelectDlg" @submit="responseSelectSubmit"/>
  </div>
</template>

<script>
import {
  batchCreateAppInfo,
  countInfoByGroupId,
  createAppInfo,
  createAppType,
  deleteAppInfo,
  deleteAppType, getAppIdsByTypeIds,
  getAppInfoList,
  getAppTypeByName,
  getTreeNode,
  importFromLib,
  updateAppInfo,
  updateAppType
} from '@/api/system/baseData/appLibrary'
import { createStrategy, getStrategyByName, updateStrategy } from '@/api/behaviorManage/application/appBlock'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import AppImportTable from '@/views/system/baseData/appLibrary/appImportTableDlg'
import AppAddDlg from '@/views/system/baseData/appLibrary/appAddDlg.vue'
import ResponseSelectDlg from '@/views/behaviorManage/application/appBlock/responseSelectDlg';

/**
 * 程序访问限制优化，各程序支持设置不同的响应规则 ， 响应规则分为 【默认响应规则】-【程序内响应规则】，程序内响应规则即为程序内部设置的响应规则，若添加的程序没有配置响应规则，即会用默认响应规则
 * V5.0之前的终端的响应规则以默认响应规则为准，当blockType=2时，检测规则之外的程序响应规则以默认响应规则为准。
 * - 删除原来的executeCode判断，用temp.ruleIdFlag 取代是否配置违规响应规则;
 * - 是否配置限制规则 => 通过temp.action | responseConfigData中是否存在value配置限制运行规则（1：限制运行规则，2：违规告警规则）
 * - 由responseConfigData保存各程序设置的响应规则，在保存之前（即：formatFormData方法）进行封装处理。
 * - 数据返显事项：若策略中windows配置了程序分类时，会在打开windows配置（windows-tag）时获取程序分类下的所有程序Id，用于计算responseConfigData和setRuleConfigData
 */
export default {
  name: 'AppBlockDlg',
  components: { AppImportTable, ResponseContent, AppAddDlg, ResponseSelectDlg },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      // 进程系统类型：1-windows，2-linux，4-mac
      osType: 1,
      supportMd5: true,
      submitting: false,
      slotName: undefined,
      packetList: [],       // 添加到列表中的数据
      filterPacketList: [], // 过滤后的列表
      nowPackageList: [],   // 所有访问限制程序库的程序列表
      loadingApp: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: '',
        name: '',
        blockType: 1,
        active: false,
        action: 0,
        // childLimit: 0,
        remark: '',
        timeId: 1,
        classId: '',
        entityType: '',
        entityId: '',
        ruleId: null,
        checkApp: [],
        ruleIdFlag: false  //  是否触发违规响应规则
      },
      colModel: [
        { prop: 'typeId', label: 'typeId', width: '80', sort: true, formatter: this.typeIdFormatter },
        { prop: 'name', label: 'processName', width: '150', sort: true, formatter: this.nameFormatter },
        { prop: 'checkMd5', label: 'isCheckMd5', hidden: () => !this.supportMd5, width: '100', sort: true, formatter: this.md5LevelFormatter },
        { prop: 'response', label: '响应规则', width: '100', type: 'button', buttons: [
          { formatter: this.responseFormatter, disabledFormatter: () => !this.formable, click: this.setResponseClick }
        ]
        },
        { label: 'operate', type: 'button', fixedWidth: '100', hidden: !this.formable,
          buttons: [
            { label: 'edit', isShow: (row) => { return row.itemType == 1 }, click: this.handleUpdateProcess },
            { label: 'viewProcess', isShow: (row) => { return row.itemType == 2 }, click: this.handleViewProcess }
          ]
        }
      ],
      exceptModel: [
        { prop: 'typeId', label: 'typeId', width: '80', sort: true, formatter: this.typeIdFormatter },
        { prop: 'name', label: 'processName', width: '150', sort: true, formatter: this.nameFormatter },
        { prop: 'checkMd5', label: 'isCheckMd5', hidden: () => !this.supportMd5, width: '100', formatter: this.md5LevelFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.formable,
          buttons: [
            { label: 'edit', isShow: (row) => { return row.itemType == 1 }, click: this.handleUpdateProcess },
            { label: 'viewProcess', isShow: (row) => { return row.itemType == 2 }, click: this.handleViewProcess }
          ]
        }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        action: [{ required: true, validator: this.actionValidator, trigger: 'blur' }]
      },
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      checkAppDeleteable: false,
      exceptAppDeleteable: false,
      typeTreeDataWindow: [],
      typeTreeDataLinux: [],
      typeTreeDataMac: [],
      timer: null,
      isAddExceptApp: false,
      exceptFlag: 0,
      searchInfo: '',
      searchExceptInfo: '',

      responseConfigData: new Map(), //  保存每个进程的响应规则配置信息  key：id，value：[1, 2]   1:禁止程序运行   2：设置响应规则
      setRuleConfigData: [],  // 保存有设置告警的进程
      appMap: {}, //  windows，保存所有程序类别下的程序Id，仅在windows配置有用
      setExceptResponse: false,  //  blockType=2时，是否设置检测规则之外的违规响应规则
      formatRowDataCount: 0 //  formatRowData方法执行计数器（只统计windows系统执行的）
    }
  },
  computed: {
    typeTreeData() {
      const treeDataMap = {
        1: this.typeTreeDataWindow,
        2: this.typeTreeDataLinux,
        4: this.typeTreeDataMac,
        7: this.typeTreeDataWindow
      }
      return treeDataMap[this.osType] || []
    },
    checkedPacketList() {
      const rowDatas = JSON.parse(JSON.stringify(this.filterPacketList))
      // 过滤例外规则的程序
      return rowDatas.filter(item => {
        return !item.isExcept
      })
    },
    exceptPacketList() {
      const rowDatas = JSON.parse(JSON.stringify(this.filterPacketList))
      // 过滤非例外规则的程序
      return rowDatas.filter(item => {
        return !!item.isExcept
      })
    },
    showByOsType() {
      // osType 是Linux、Mac 时，不显示
      const isShow = ![2, 4].includes(this.osType)
      return isShow
    },
    /** 检测规则ColModel **/
    detectionColModel() {
      return this.showByOsType ? this.temp.blockType == 1 ? this.colModel : this.exceptModel : this.exceptModel
    },
    /** 例外规则ColModel **/
    exceptColModel() {
      return this.temp.blockType == 1 ? this.exceptModel : this.colModel
    }
  },
  watch: {
    showByOsType(val) {
      if (!val) {
        // 不显示时，一些参数应该赋与默认值
        this.temp.blockType = 1
        this.temp.action = 1
      }
    },
    'temp.checkApp'(val) {
      this.loadingApp = true
      setTimeout(() => {
        // 数据太多的时候，浏览器会有短暂的卡顿现象，设置延时使 loadingApp 可以正常生效
        this.formatDataTree()
        this.loadingApp = false
      }, 200);
    }
  },
  created() {
    this.loadAppTypeTreeTree()
    this.resetTemp()
    this.getNowPackageList()
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    createAppInfo,
    updateAppInfo,
    deleteAppInfo,
    importFromLib,
    getAppInfoList,
    countInfoByGroupId,
    createAppType,
    updateAppType,
    deleteAppType,
    getAppTypeByName,
    batchCreateAppInfo,
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    closed() {
      this.resetTemp()
    },
    // 是否禁止
    isDoLimit(data) {
      return !!(data.action || this.ruleIsDoLimit())
    },
    //  检测规则或例外规则里的程序是否配置了禁止
    ruleIsDoLimit() {
      let appAction = 0;
      this.responseConfigData.size && this.responseConfigData.forEach((value, key) => {
        if (value.includes(1)) {
          appAction = 1;
        }
      });
      return !!appAction;
    },
    // 是否触发响应规则
    isDoResponse(data) {
      return data.ruleIdFlag
    },
    handleUpdateProcess(row) {
      this.isAddExceptApp = !!row.isExcept
      const prg = this.nowPackageList.find(v => v.id == row.dataId)
      if (prg) {
        this.$refs.appAddDlg.show(prg)
      }
    },
    handleViewProcess(row) {
      if (this.$refs.appImportTable) {
        const map = this.getProcessListId(this.packetList)
        const typeIds = map.get('type') ? map.get('type').map(id => `G${id}`) : null
        const processIds = map.get('process') || null
        this.$refs.appImportTable.show(typeIds, processIds, row.typeId);
      }
    },
    selectable(row) {
      return true
    },
    // 对现有的程序列表重新整理
    formatDataTree() {
      this.packetList.splice(0)
      const list = this.temp.checkApp

      // 遍历已选择的分类、应用程序，并将分类数据添加到 this.packetList
      for (const i in list) {
        const data = list[i]
        // itemType：1.应用程序 2.类别
        const { itemType, id, isExcept } = data
        // typeId 归属分类的id
        const typeId = itemType == 2 ? id : data.typeId
        if (itemType == 2) {
          const typeData = {
            id: typeId,
            typeId,
            name: typeId, // 通过程序添加的分类，使用 typeId 代替 name
            itemType: 2,
            isExcept,
            fromType: true
          }
          this.packetList.push(typeData)
        } else {
          // 应用程序
          const app = this.formatAppData(data, 1, isExcept)
          this.packetList.push(app)
        }
      }

      //  重新加载数据
      if (this.showByOsType && this.temp.osType && this.temp.osType === 1) {
        this.reloadResponseConfigData();
      }

      this.handleFilter()
    },
    // 对程序的数据进行处理， source：程序来源，1. 主动添加 2. 通过分类导入
    formatAppData(data, source, isExcept) {
      const app = JSON.parse(JSON.stringify(data))
      app.source = source
      app.dataId = app.id
      app.id = `${app.id}${Math.random() * 1000000}`
      app.name = app.processName
      app.isExcept = isExcept
      app.itemType = 1
      return app
    },
    async getNowPackageList() {
      //  获取所有app
      const resp = await getAppInfoList({
        limit: null,
        page: 1
      })
      this.nowPackageList = resp.data.items
    },
    async slotChange(name, slotTemp) {
      this.clearAppRowData()
      this.osType = name
      this.supportMd5 = (name == 1)
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
      await this.getAppIdsByTypeIds(this.temp)
      this.$nextTick(() => {
        this.reloadAppRowData()
      })
    },
    appGridTable() {
      return this.isAddExceptApp ? this.$refs['exceptAppGrid'] : this.$refs['checkedAppGrid']
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    reloadAppRowData() {
      ['exceptAppGrid', 'checkedAppGrid'].forEach(item => {
        this.$refs[item] && this.$refs[item].execRowDataApi()
      })
    },
    clearAppRowData() {
      ['exceptAppGrid', 'checkedAppGrid'].forEach(item => {
        this.$refs[item] && this.$refs[item].clearRowData()
      })
    },
    getAppRows(isExcept) {
      const rowDatas = []
      this.temp.checkApp.forEach(item => {
        if (!!item.isExcept === isExcept) {
          rowDatas.push(item)
        }
      })
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: { total: rowDatas.length, items: rowDatas }})
      })
    },
    checkAppSelectionChangeEnd(rowDatas) {
      this.checkAppDeleteable = rowDatas && rowDatas.length > 0
    },
    exceptAppSelectionChangeEnd(rowDatas) {
      this.exceptAppDeleteable = rowDatas && rowDatas.length > 0
    },
    loadAppTypeTreeTree: function() {
      getTreeNode({ osType: 1 }).then(respond => {
        this.typeTreeDataWindow = respond.data
      })
      getTreeNode({ osType: 2 }).then(respond => {
        this.typeTreeDataLinux = respond.data
      })
      getTreeNode({ osType: 4 }).then(respond => {
        this.typeTreeDataMac = respond.data
      })
    },
    // 获取访问限制程序id
    getProcessListId(processList) {
      const typeIds = []
      const processIds = []
      processList.forEach(item => {
        if (item.itemType == 2) {
          // 通过类别添加，需要将类别id加入
          typeIds.push(item.typeId)
        } else {
          // 非 通过类别添加的程序（即手动添加的程序），需要将程序id加入
          processIds.push(item.dataId)
        }
      })
      const map = new Map()
      typeIds.length > 0 ? map.set('type', typeIds) : null
      processIds.length > 0 ? map.set('process', processIds) : null
      return map
    },
    // 访问限制程序库导入， isExcept：true 例外规则，false 检测规则
    handleAppImport(isExcept) {
      this.isAddExceptApp = isExcept
      const map = !isExcept ? this.getProcessListId(this.checkedPacketList) : this.getProcessListId(this.exceptPacketList)
      // 添加类别、程序时区分是检测规则还是例外规则
      this.exceptFlag = !isExcept ? 0 : 1
      const typeIds = map.get('type') ? map.get('type').map(id => `G${id}`) : null
      const processIds = map.get('process') || null
      this.$refs.appImportTable.show(typeIds, processIds)
    },
    handleAppCreate(isExcept) {
      this.isAddExceptApp = isExcept
      this.$refs.appImportTable.showBatchCreate(false)
    },
    handleDeleteCheckedApp(isExcept) {
      this.isAddExceptApp = isExcept
      const rows = this.appGridTable().getSelectedDatas()

      // 待删除数据的key的set集合
      const toDeleteKeys = new Set();

      // 预生成待删除标记集合
      rows.forEach(item => {
        // 生成父级删除标记
        const parentKey = getDeleteKey(item, true);
        if (parentKey) toDeleteKeys.add(parentKey);

        // 生成子级删除标记
        if (item.children) {
          item.children.forEach(child => {
            const childKey = getDeleteKey(child);
            if (childKey) toDeleteKeys.add(childKey);
          });
        }
      });

      // 根据删除标记集合，过滤数据
      const filteredArray = this.temp.checkApp.filter(({ id, itemType, isExcept }) => {
        return !toDeleteKeys.has(`${id}-${itemType}-${!!isExcept}`);
      });
      this.$set(this.temp, 'checkApp', filteredArray)

      // 辅助函数：生成待删除键
      function getDeleteKey(target, isParent = false) {
        const id = isParent
          ? (target.fromType ? target.typeId : target.dataId)
          : target.dataId;
        if (!id) return null;
        return `${id}-${target.itemType}-${isExcept}`;
      }
    },
    handleCreate() {
      this.resetTemp()
      this.initResponseConfigData()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    async handleUpdate(row) {
      this.resetTemp()
      this.initResponseConfigData()
      await this.getAppIdsByTypeIds(row);
      this.$refs['stgDlg'].show(row, this.formable)
    },
    handleShow(row, isGenerateStrategy) {
      this.resetTemp()
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    // 模糊查询
    handleFilter(isExcept) {
      const isFilter = this.searchInfo.trim() != ''
      const isFilterExcept = this.searchExceptInfo.trim() != ''
      if (!isFilter && !isFilterExcept) {
        // 空格字符或者空搜索，还原数据
        this.filterPacketList = this.packetList
        return this.packetList
      }
      const searchInfo = this.searchInfo.trim().toLowerCase()
      const searchExceptInfo = this.searchExceptInfo.trim().toLowerCase()
      const targetTypeIdMap = {}
      const targetExceptTypeIdMap = {}
      this.nowPackageList.forEach(item => {
        if (isFilter && item.processName.toLowerCase().includes(searchInfo)) {
          if (!targetTypeIdMap[item.typeId]) {
            targetTypeIdMap[item.typeId] = [item.processName]
          } else if (targetTypeIdMap[item.typeId].indexOf(item.processName) < 0) {
            targetTypeIdMap[item.typeId].push(item.processName)
          }
        }
        if (isFilterExcept && item.processName.toLowerCase().includes(searchExceptInfo)) {
          if (!targetExceptTypeIdMap[item.typeId]) {
            targetExceptTypeIdMap[item.typeId] = [item.processName]
          } else if (targetExceptTypeIdMap[item.typeId].indexOf(item.processName) < 0) {
            targetExceptTypeIdMap[item.typeId].push(item.processName)
          }
        }
      })
      const tempPacketList = JSON.parse(JSON.stringify(this.packetList))
      // 表格数据分为两层：第一层为程序所属的分组，第二层才是具体的数据
      const packetList = tempPacketList.filter(item => {
        if (item.itemType == 1) {
          if (item.isExcept && (!isFilterExcept || item.name.toLowerCase().includes(searchExceptInfo))) {
            return true
          } else if (!item.isExcept && (!isFilter || item.name.toLowerCase().includes(searchInfo))) {
            return true
          }
        } else if (!item.isExcept && (!isFilter || targetTypeIdMap[item.typeId])) {
          item.childrenNames = targetTypeIdMap[item.typeId]
          return true
        } else if (item.isExcept && (!isFilterExcept || targetExceptTypeIdMap[item.typeId])) {
          item.childrenNames = targetExceptTypeIdMap[item.typeId]
          return true
        }
        return false
      })
      this.filterPacketList = packetList
    },
    /**
     * 当为windows
     * @returns {Promise<void>}
     */
    async getAppIdsByTypeIds(rowData) {
      this.appMap = {}
      if (rowData.osType == 1) {
        const appTypeIds = rowData.checkApp.filter(t => t.itemType == 2).map(t => t.id) || [];
        if (appTypeIds.length) {
          const res = await getAppIdsByTypeIds(appTypeIds)
          this.appMap = res.data || {}
          //  重新计算 计算responseConfigData 和 setRuleConfigData值，若从linux或mac的策略数据打开修改界面时，因typeId数据未加载到，formatRowData方法中计算的数据有问题，
          //  固在点击windows配置时重新计算（仅有配置程序分类的响应规则时需要重新计算）
          if (res.data) {
            this.computeRelResponseConfigData(rowData, this.appMap);
          }
        }
      }
    },
    //  计算responseConfigData 值和 setRuleConfigData值
    computeRelResponseConfigData(rowData, appMap) {
      this.responseConfigData = new Map();
      this.setRuleConfigData = []
      if (rowData.checkApp && rowData.checkApp.length) {
        let tRuleIdFlag = false;
        let tRuleIdExId = null  //  默认响应规则和程序内获取响应规则Id都是同一个，记录响应规则Id
        rowData.checkApp.forEach(data => {
          const key = this.getResponseKey(Object.assign({}, data, { name: data.processName }))
          const needSetResponseConfig = rowData.blockType === 1 ? 0 : 1 //  blockType=1时，检测规则中的程序需要会设置响应规则
          let pkgApp;
          //  若为应用程序时，直接从packetList找到对于程序的响应规则信息
          if (data.itemType == 1) {
            pkgApp = rowData.packetList ? rowData.packetList.find(t => t.id == data.id && t.isExcept === needSetResponseConfig) : null
          } else {  //  通过程序类别获取程序Id，找到属于该程序分类的程序Id的配置信息
            pkgApp = rowData.packetList ? rowData.packetList.find(t => appMap[data.id] && appMap[data.id].includes(parseInt(t.id))) : null
          }
          if (pkgApp) {
            const config = []
            // 旧版本数据兼容，  若actionEx 不存在，表示是旧版本数据，旧版本数据读取action和ruleId
            if (pkgApp.actionEx === undefined || pkgApp.actionEx === null) {
              pkgApp.action && config.push(1);
              pkgApp.ruleId && config.push(2);
            } else {
              pkgApp.actionEx && config.push(1);
              pkgApp.ruleIdEx && config.push(2);
            }
            if (pkgApp.ruleId || pkgApp.ruleIdEx) {
              tRuleIdFlag = true;
              tRuleIdExId = pkgApp.ruleIdEx
            }
            config.length && this.responseConfigData.set(key, config);
            config.includes(2) && this.setRuleConfigData.push(key);
          }
        });
        //  若默认响应规则未设置时，使用程序内的响应规则Id
        if (!rowData.ruleId) {
          rowData.ruleId = tRuleIdExId;
        }
        rowData.ruleIdFlag = tRuleIdFlag || !!rowData.ruleId;
      } else {  //  若没有设置程序时，ruleIdFlag 根据ruleId判断
        rowData.ruleIdFlag = !!rowData.ruleId;
      }
      this.setResponseValidate();
    },
    //  打开弹窗时，会默认执行两次
    formatRowData(rowData) {
      if (rowData.osType == 1) {
        //  打开弹窗会执行两次此方法，导致this.setExceptResponse 值不准确，设置计数器，方法只执行一次
        if (!this.formatRowDataCount++) {
          //  过滤掉无效配置，即id=0的数据，该数据有后端生成，用于终端映射作用，在前端展示上无意义
          rowData.packetList = rowData.packetList.filter(t => t.id != 0);
          this.setExceptResponse = !!rowData.ruleId
          this.computeRelResponseConfigData(rowData, this.appMap)
        }
      } else {
        //  linux或mac
        rowData.ruleIdFlag = !!rowData.ruleId;
      }
    },
    formatFormData: function(formData) {
      if (!this.isDoResponse(formData)) {
        formData.ruleId = null
      }
      if (formData.osType == 1) {
        // 给各程序设置响应规则
        const appBlockRepDatas = [];
        //  获取需要设置响应规则的程序
        const apps = formData.checkApp.filter(t => t.isExcept + 1 === formData.blockType) || []
        apps.forEach(app => {
          //  判断此设备是否有配置响应规则，有则使用配置的响应规则，没有使用默认的响应规则
          const config = this.responseConfigData.get(this.getResponseKey(Object.assign({}, app, { name: app.processName })))
          const ruleIdEx = config && config.length ? (config.includes(2) ? formData.ruleId : null) : (this.setExceptResponse ? formData.ruleId : null)
          const actionEx = config && config.length ? (config.includes(1) ? 1 : 0) : formData.action || 0
          appBlockRepDatas.push({
            id: app.id,
            typeId: app.typeId,
            ruleIdEx,
            actionEx
          })
        })
        formData.appBlockRepDatas = appBlockRepDatas
        //  若默认配置未设置告警时，ruleId = null
        if (!this.setExceptResponse) {
          formData.ruleId = null
        }
      }
    },
    refreshList() {
      this.getNowPackageList().then(() => {
        this.formatDataTree()
      })
    },
    submitEnd(dlgStatus) {
      // 更新缓存的所有的程序
      this.getNowPackageList().then(() => {
        this.formatDataTree()
      })
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    appAddSubmitEndAndReloadTable(data, dlgStatus) {
      this.appAddSubmitEnd(data, dlgStatus)
      this.getNowPackageList()
    },
    appAddSubmitEnd(data, dlgStatus) {
      if (data.innerAdd) {
        return
      }
      const appBean = {
        id: data.id,
        md5Level: data.md5Level,
        checkMd5: data.checkMd5,
        processName: data.processName,
        productVersion: data.productVersion,
        itemType: 1,  // 记录类型：1应用程序2类别
        typeId: data.typeId,
        isExcept: this.isAddExceptApp ? 1 : 0
      }
      // 如果有相同程序的相同版本 就不进行添加
      const sameAppIndex = this.temp.checkApp.findIndex(v => v.processName == appBean.processName && v.productVersion == appBean.productVersion && v.itemType == appBean.itemType)
      if (sameAppIndex != -1) {
        // 要更新现有的数据(但不更新isExcept #TRDLP-12505)
        appBean.isExcept = this.temp.checkApp[sameAppIndex].isExcept
        this.$set(this.temp.checkApp, sameAppIndex, appBean)

        //  windows， 判断是否更改了程序类别，确保
        if (this.showByOsType) {
          this.updateTypeIdFromConfigData(appBean);
        }
      } else {
        // 删除已有相同的程序
        const index = this.temp.checkApp.findIndex(v => v.id == data.id && appBean.isExcept == v.isExcept && v.itemType == appBean.itemType)
        if (index != -1) {
          this.$set(this.temp.checkApp, index, appBean)
        } else {
          this.temp.checkApp.unshift(appBean)
        }
      }
    },
    appMd5ImportSubmitEnd(data, editMode) {
      // editMode编辑模式，0-删除，1-添加程序，2-添加类别 3修改程序 4-修改类别
      if (editMode == 2) {
        // 判断是否有取消勾选原有的类别
        const dataIdSet = new Set(data.map(item => item.dataId))
        this.temp.checkApp = this.temp.checkApp.filter(item => dataIdSet.has[item.id] || item.itemType !== 2 || item.isExcept !== this.exceptFlag);
      }
      if (editMode == 1) {
        // 获取取消勾选的原有的程序
        const dataIdSet = new Set(this.$refs.appImportTable.getRemoveCheckedRowKeys().map(item => item.dataId))
        // 从已有列表中移除取消勾选的程序
        this.temp.checkApp = this.temp.checkApp.filter(item => !(dataIdSet.has[item.id] && item.itemType === 1 && item.isExcept === this.exceptFlag));
      }
      if (editMode === 0) {
        // 0-删除
        // 创建待删除键的集合
        const deleteKeys = new Set(data.map(({ dataId, id, itemType }) => `${dataId || id}-${itemType}`));
        // 使用filter一次性过滤
        this.temp.checkApp = this.temp.checkApp.filter(({ dataId, id, itemType }) =>
          !deleteKeys.has(`${dataId || id}-${itemType}`)
        );
      } else if (editMode === 4) {
        // 4-修改类别名称
        const { label, dataId: id } = data
        for (let i = 0; i < this.temp.checkApp.length; i++) {
          const item = this.temp.checkApp[i]
          if (item.itemType == 2 && item.id == id) {
            item.processName = label
            break
          }
        }
      } else {
        // 1-添加程序，2-添加类别 3修改程序

        // 创建一个 Map 用于存储列表中的元素，以方便快速查找
        const listMap = new Map();
        this.temp.checkApp.forEach((item) => {
          const { id, dataId, itemType } = item
          const key = `${dataId || id}-${itemType}`;
          listMap.set(key, item);
        });

        // 遍历新数据
        data.forEach(item => {
          const { id, dataId, typeId } = item
          const itemType = item.type === 'G' ? 2 : 1
          const key = (itemType == 1 && !typeId) ? '' : `${dataId || id}-${itemType}`;
          const existApp = listMap.get(key);
          if (!existApp) {
            // 数据不存在则添加，1 程序 2 类别
            if (editMode === 1) {
              this.appAddSubmitEnd(item)
            } else if (editMode === 2 && item.id != '0') {
              const appBean = {
                id: item.dataId,
                processName: item.label,
                itemType: 2,  // 记录类型：1应用程序2类别
                isExcept: this.isAddExceptApp ? 1 : 0
              }
              this.temp.checkApp.unshift(appBean)
              // 同时更新 Map
              listMap.set(`${item.dataId}-2`, appBean);
            }
          } else {
            const { md5Level, checkMd5, typeId } = item
            Object.assign(existApp, { md5Level, checkMd5, typeId })
            // 修改程序不需要变更
            if (editMode != 3) {
              existApp.isExcept = this.isAddExceptApp ? 1 : 0
            }
          }
        })
      }
      this.reloadAppRowData()
      // 更新缓存的所有的程序
      this.getNowPackageList().then(() => {
        this.formatDataTree()
      })
    },
    md5LevelFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      }
      if (row.checkMd5 == 1 || row.isCheckMd5 == 1) {
        return this.$t('text.enable')
      }
      return this.$t('text.disable2')
    },
    nameFormatter(row, data) {
      if (row.itemType == 2) {
        return this.$t('pages.collectAll') + (!row.childrenNames ? '' : `(${this.$t('pages.include')}: ${row.childrenNames.join(', ')})`)
      } else {
        return row.processName
      }
    },
    typeIdFormatter(row, data) {
      let msg = ''
      if (this.typeTreeData) {
        this.typeTreeData.some(node => {
          if (node.dataId == data) {
            msg = node.label
            return true
          }
        })
      }
      return msg
    },
    actionValidator(rule, value, callback) {
      if (!this.showByOsType) {
        callback()
      } else if (!this.temp.action && !this.setExceptResponse) {
        callback(new Error(this.$t('pages.install_Msg51')))
      } else {
        callback()
      }
    },
    validateForm(formData) {
      //  windows, 所有程序必须设置只少配置一个响应规则，且responseValidate=true
      return this.responseValidate && (formData.osType != 1 || (((formData.ruleIdFlag && formData.ruleId) || this.isDoLimit(formData))))
    },
    ruleIsCheck(data) {
      this.setRuleIdFlag(!!data, 'ruleIsCheck')
      if (this.showByOsType) {
        this.setExceptResponse = this.temp.ruleIdFlag
      }
      this.actionChange();
    },
    //   选中响应规则后，清空红色提示
    actionChange() {
      const ref = this.$refs.stgDlg.getSlotFormRef();
      if (ref) {
        ref.validateField('action')
      }
    },
    /**
     * 响应规则格式化
     * @param row
     * @param data
     */
    responseFormatter(row, data) {
      let result = '';
      const responseConfig = this.responseConfigData.get(this.getResponseKey(row));
      if (responseConfig) {
        if (responseConfig.includes(1)) {
          result += '禁止程序运行；'
        }
        if (responseConfig.includes(2)) {
          result += '违规响应规则；'
        }
      }
      return result.length ? result : '未设置'
    },
    /**
     * 单项设置
     */
    setResponseClick(row) {
      this.$refs.responseSelectDlg.show([row], this.responseConfigData.get(this.getResponseKey(row)) || []);
    },
    getResponseKey(data) {
      let name = data.name
      let typeId = data.typeId
      //  若为程序分类时，name为程序Id
      if (data.itemType == 2) {
        name = data.id
        typeId = data.id
      }
      return data.itemType + '-%-' + name + '-%-' + typeId + '-%-' + data.isExcept
    },
    /**
     * 解析Key
     * @param key
     */
    analyzeResponseKey(key) {
      const datas = key.split('-%-');
      if (datas.length > 3) {
        return {
          itemType: parseInt(datas[0]),
          name: datas[1],
          typeId: parseInt(datas[2]),
          isExcept: parseInt(datas[3])
        }
      }
      return null;
    },
    /**
     * 批量设置响应规则
     * @param type  1：检测规则，2：例外规则
     */
    handleSettingResponse(type) {
      this.isAddExceptApp = type == 2;
      const datas = this.appGridTable().getSelectedDatas();
      this.$refs.responseSelectDlg.show(datas);
    },
    //  设置响应规则回调
    responseSelectSubmit(datas, stopAppRun, isResponse) {
      if (datas.length) {
        const config = [];
        stopAppRun && config.push(1);
        isResponse && config.push(2);
        datas.forEach(data => {
          if (data) {
            const key = this.getResponseKey(data);
            if (config.length) {
              this.responseConfigData.set(key, JSON.parse(JSON.stringify(config)))
            } else {
              this.responseConfigData.delete(key)
            }
            if (isResponse) {
              !this.setRuleConfigData.find(t => t === key) && this.setRuleConfigData.push(key);
            } else {
              this.setRuleConfigData = this.setRuleConfigData.filter(t => t !== key);
            }
            this.reloadAppRowData();
          }
        });
      }
      this.setRuleIdFlag(!!this.setExceptResponse || !!this.setRuleConfigData.length, 'responseSelectSubmit')
      this.setResponseValidate();
      this.appGridTable().clearSelection();
      this.actionChange();
    },
    /** 初始化响应规则数据 **/
    initResponseConfigData() {
      this.responseConfigData = new Map();
      this.setRuleConfigData = []
      this.setExceptResponse = false
      this.formatRowDataCount = 0
    },
    /** 重新加载数据, responseConfigData中同时存在检测规则和例外规则中应用程序的响应规则配置， （目的：要确保仅保留其中一个规则中的应用程序的响应规则配置)
     * 当检测规则允许配置响应规则时，仅保留检测规则的响应规则，当例外规则允许配置响应规则时，仅保留例外规则的响应规则 **/
    reloadResponseConfigData() {
      //  仅在responseConfigData有值才需要重新加载数据
      if (this.responseConfigData.size) {
        this.responseConfigData.forEach((value, key) => {
          const keyObj = this.analyzeResponseKey(key);
          if (keyObj) {
            //  目前不允许检测规则，例外规则存在名称相同的进程，固不判断typeId   itemType=2时，keyObj.name=程序Id而非程序名称
            const packetApp = this.packetList.find(t => t.itemType === keyObj.itemType && (keyObj.itemType === 1 ? t.name === keyObj.name : t.id == keyObj.name));
            //  keyObj.isExcept 1-为例外规则,0-检测规则
            if (!packetApp || keyObj.isExcept !== packetApp.isExcept) {
              this.responseConfigData.delete(key);
            }
          }
        });
        this.setRuleConfigData = this.setRuleConfigData.filter(key => {
          const keyObj = this.analyzeResponseKey(key);
          if (keyObj) {
            //  目前不允许检测规则，例外规则存在名称相同的进程，固不判断typeId
            const packetApp = this.packetList.find(t => t.itemType === keyObj.itemType && (keyObj.itemType === 1 ? t.name === keyObj.name : t.id == keyObj.name));
            //  keyObj.isExcept 1-为例外规则,0-检测规则
            return packetApp && keyObj.isExcept === packetApp.isExcept;
          }
          return false;
        })
        this.setRuleIdFlag(!!this.setExceptResponse || !!this.setRuleConfigData.length, 'reloadResponseConfigData 1')
      } else {
        //  在刚打开弹窗加载数据时，因temp.checkApp
        this.setRuleIdFlag(!!this.temp.ruleId, 'reloadResponseConfigData 2')
      }
      this.setResponseValidate();
    },
    //  由于在更新程序时，可能会更改程序的分组类型，这样会导致setRuleConfigData和responseConfigData里面的key对不上，固在此处更key（key的组成包含typeId)
    updateTypeIdFromConfigData(data) {
      const map = new Map();
      const list = [];
      this.responseConfigData.forEach((value, key) => {
        const objKey = this.analyzeResponseKey(key);
        if (data.processName === objKey.name && data.isExcept === objKey.isExcept && data.itemType === objKey.itemType) {
          key = this.getResponseKey(Object.assign({}, data, { name: data.processName }));
        }
        if (value.includes(2)) {
          list.push(key);
        }
        map.set(key, value);
      })
      this.responseConfigData = map;
      this.setRuleConfigData = list;
    },
    /** 设计响应规则校验值responseValidate **/
    setResponseValidate() {
      if (this.isDoLimit(this.temp)) {
        //  因未设置响应规则，responseValidate = true 确保validateForm校验通过
        this.responseValidate = true
      }
    },
    /** 检测模式发生改变时，每个进程重新设置响应规则 **/
    detectionCodeChange() {
      this.setRuleIdFlag(false, 'detectionCodeChange')
      this.temp.action = 0
      this.initResponseConfigData()
    },
    //  统一设置ruleIdFlag,管理响应规则
    setRuleIdFlag(val, tag) {
      console.log('setRuleIdFlag  %o  %o %o', val, tag, JSON.parse(JSON.stringify(this.temp)))
      this.temp.ruleIdFlag = !!val
      if (!this.temp.ruleIdFlag) {
        this.temp.ruleId = null
      }
    },
    setExceptResponseChange(val) {
      this.setRuleIdFlag(!!val || !!this.setRuleConfigData.length, 'setExceptResponseChange')
      this.actionChange()
    }
  }
}
</script>
<style lang="scss" scoped>
.prohibit >>> .el-checkbox__inner {
  margin-left: 17px;
}
.selectionCheckBox {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.black_label {
  >>>.el-form-item__label {
    color: #303133 !important;
  }
}
</style>
