<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="'响应规则配置'"
      :visible.sync="visible"
      width="450px"
    >
      <div>
        <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="60px">
          <FormItem prop="responseArr">
            <el-checkbox-group v-model="temp.responseArr">
              <el-checkbox :key="1" :label="1">{{ '禁止程序运行' }}</el-checkbox>
              <el-checkbox :key="2" :label="2">{{ '设置违规响应规则' }}</el-checkbox>
            </el-checkbox-group>
          </FormItem>
        </Form>

      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ResponseSelectDlg',
  data() {
    return {
      submitting: false,
      visible: false,
      temp: {
        responseArr: []
      },
      rules: {},
      datas: [] //  暂存待设置的Ids
    }
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      this.temp.responseArr = []
      this.datas = []
    },
    /**
     * 展示
     * @param datas 待设置的进程信息
     * @param responseConfig 单点设置响应规则时，当前进程的响应规则设置
     */
    show(datas, responseConfig) {
      this.initData();
      this.datas = datas || [];
      responseConfig && this.temp.responseArr.push(...responseConfig);
      this.visible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate();
      })
    },
    /**
     * 确认
     */
    confirm() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const stopAppRun = this.temp.responseArr.includes(1)
          const isResponse = this.temp.responseArr.includes(2)
          this.$emit('submit', this.datas, stopAppRun, isResponse)
          this.visible = false;
        }
      });
    }
  }
}
</script>

<style scoped>

</style>
