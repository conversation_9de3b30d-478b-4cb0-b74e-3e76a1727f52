<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button style="margin-left: 10px;" icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="90px">
              <FormItem :label="$t('table.stgName')">
                <el-input v-model="query.searchInfo" v-trim clearable :maxlength="255"/>
              </FormItem>
              <FormItem :label="$t('table.programControlled')">
                <el-input v-model="query.controlledProcess" v-trim clearable :maxlength="255"/>
              </FormItem>
              <FormItem :label="$t('pages.operatingSystem')">
                <el-select v-model="query.osType" clearable>
                  <el-option label="Windows" :value="1"></el-option>
                  <el-option label="Linux" :value="2"></el-option>
                  <el-option label="Mac" :value="4"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('table.violationName')">
                <el-select
                  ref="ruleSelect"
                  v-model="query.ruleId"
                  size="mini"
                  clearable
                  filterable
                >
                  <el-option v-for="item in allRules" :key="item.value" :label="item.label" :value="item.value"/>
                </el-select>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;" v-html="blockTypeFormatter(props.detail)"></span>
          </div>
        </template>
      </grid-table>
    </div>
    <app-block-dlg
      ref="appBlockDlg"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    />
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importAppBlockStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import axios from 'axios'
import AppBlockDlg from './editDlg'
import { getStrategyList, deleteStrategy } from '@/api/behaviorManage/application/appBlock'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, osTypeIconFormatter, timeDateInfoFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'AppBlock',
  components: { AppBlockDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 6,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'blockType', label: 'stgMessage', width: '300', ellipsis: false, type: 'popover', placement: 'top-start', originData: true, formatter: this.blockTypeFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        controlledProcess: '',
        osType: undefined,
        ruleId: undefined,
        entityType: undefined,
        entityId: undefined
      },
      appQuery: { // 查询条件
        page: 1,
        searchInfo: '',
        typeId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      submitting: false,
      source: null,
      checkedEntityNode: {}
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    },
    // 响应规则数据
    ruleData() {
      return this.$store.getters.alarmRules
    },
    // 响应规则下拉框中的选项options
    allRules() {
      return this.ruleData.map(item => {
        return {
          label: item.name,
          value: item.id
        }
      })
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  methods: {
    connectionSource() {
      return axios.CancelToken.source()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    refresh() {
      return refreshPage(this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['appBlockDlg'].handleCreate()
    },
    handleUpdate(row) {
      this.$refs['appBlockDlg'].handleUpdate(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg2'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    blockTypeFormatter: function(row, data, c) {
      if (row.osType == 1) { // win
        const brTag = data ? '' : '<br>'
        const detectArr = []
        const exceptArr = []
        const limitRunArr = []  //  有配置禁止运行的程序名称
        const responseArr = []  //  有配置违规响应规则的程序名称
        const needSetResponseConfig = row.blockType == 1 ? 0 : 1
        row.packetList.forEach(item => {
          if (item.id !== 0) {
            if (item.isExcept === 0) {
              item.processName && detectArr.push(item.processName)
            } else if (item.isExcept === 1) {
              item.processName && exceptArr.push(item.processName)
            }
            if (needSetResponseConfig === item.isExcept) {
              item.actionEx && limitRunArr.push(item.processName)
              item.ruleIdEx && responseArr.push(item.processName);
            }
          }
        })
        if (row.blockType == 2 && detectArr.length == 0 && row.action) {
          if (row.ruleId) {
            return `<b>${this.$t('pages.forbidAllAppRun')}、${this.$t('pages.triggerResponseRule')}</b>`
          } else {
            return `<b>${this.$t('pages.forbidAllAppRun')}</b>`
          }
        }
        if (row.blockType == 1 && detectArr.length == 0) {
          return `<b>${this.$t('pages.allowAllAppRun')}</b>`
        }
        const detectStr = detectArr.length > 0 ? `<b>${this.$t('table.detectionRules')}</b>：${detectArr.join(', ')}；<br>` : ''
        const exceptStr = exceptArr.length > 0 ? `<b>${this.$t('table.exceptExpr')}</b>：${exceptArr.join(', ')}；<br>` : ''
        const actions = []
        if (row.action) { actions.push(this.$t('pages.blockTypeFormatter1')) }
        if (row.ruleId) { actions.push(this.$t('pages.triggerResponseRule')) }
        const actionStr = row.blockType == 1 ? `<b>${this.$t('pages.blockTypeFormatter3')}</b>：${actions.join('、')}；`
          : `<b>${this.$t('pages.blockTypeFormatter4')}</b>：${actions.join('、')}；`

        //  支持显示 禁止程序运行 有哪些程序配置了，触发违规响应规则 有哪些程序配置了
        const limitRunStr = limitRunArr.length ? `<br><b>禁止程序运行：</b>${limitRunArr.join(',')}；` : '';
        const responseStr = responseArr.length ? `<br><b>触发违规响应规则：</b>${responseArr.join(',')}；` : ''
        return `${detectStr}${exceptStr}${actionStr}${limitRunStr}${responseStr}`
      } else if (row.osType == 2 || row.osType == 4) { // linux、mac
        const brTag = data ? '' : '<br>'
        const detectArr = []
        row.packetList.forEach(item => {
          if (item.isExcept === 0) {
            item.processName && detectArr.push(item.processName)
          }
        })
        if (detectArr.length == 0) {
          return `<b>${this.$t('pages.allowAllAppRun')}</b>`
        }
        const detectStr = `<b>${this.$t('table.detectionRules')}</b>：${detectArr.join(', ')}；${brTag}`
        const actions = [this.$t('pages.blockTypeFormatter1')]
        if (row.ruleId) { actions.push(this.$t('pages.triggerResponseRule')) }
        const actionStr = `<b>${this.$t('pages.blockTypeFormatter5_1')}</b>：${actions.join('、')}`
        return `${detectStr}${actionStr}`
      }
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    resetQuery() {
      this.query.page = 1
      this.query.searchInfo = ''
      this.query.controlledProcess = ''
      this.query.osType = undefined
      this.query.ruleId = undefined
    }
  }
}
</script>
