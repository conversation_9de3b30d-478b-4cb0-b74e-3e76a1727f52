<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="gridTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="strategySelectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="closed"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 700px; margin-left: 30px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.stgName')" prop="name">
              <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.effectTime')" class="content-flex" prop="timeId">
              <el-select v-model="temp.timeId" :disabled="!formable" :placeholder="$t('text.select')">
                <el-option v-for="item in timeInfoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <link-button :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit class="word-limit-3digits" />
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <FormItem :label="$t('pages.baseFunLimit')">
          <span style="color: #369de7;">{{ $t('pages.sysBaseConfig_text1') }}</span>
          <div style="height: 250px;">
            <tree-menu
              ref="tree"
              :data="treeData"
              :disabled-all-nodes="!formable"
              :default-expand-all="true"
              multiple
              check-on-click-node
              :default-checked-keys="checkedKeys"
              :render-content="renderContent"
              @check-change="handleCheckChange"
            />
          </div>
        </FormItem>
        <!--<ResponseContent
          :status="dialogStatus"
          :show-select="true"
          :editable="formable"
          read-only
          :prop-check-rule="propCheckRule"
          :prop-rule-id="propRuleId"
          @getRuleId="getRuleId"
        />-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-mode="1"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="()=>{dialogFormVisible = false;}">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importSysBaseConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import {
  createData,
  deleteData,
  getDataByName,
  getDataPage,
  getTree,
  loadFuncOptions,
  updateData
} from '@/api/behaviorManage/application/sysBaseConfig'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import { validatePolicy, timeIdValidator } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeInfoFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'SysBaseConfig',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 135,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTime', width: '100', formatter: timeInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'keyword', label: 'stgMessage', width: '200', formatter: this.formatStrateMsg },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        {
          prop: 'createdTime',
          label: 'operate',
          type: 'button',
          fixed: 'right',
          fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      itemColModel: [
        { prop: 'funcId', label: 'fun', width: '40', formatter: this.funcIdFormatter },
        { prop: 'limit', label: 'limitType', width: '40', formatter: row => { return row.limit == 1 ? this.$t('pages.allow') : this.$t('pages.prohibit') } },
        { prop: 'timeId', label: 'effectTime', width: '40', formatter: timeInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' }
      ],
      options: [
        { value: 1, label: this.$t('pages.allDay') },
        { value: 2, label: this.$t('pages.morning') }
      ],
      query: {
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      ipPortEditable: false,
      ipPortDeleteable: false,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        configs: [],
        timeId: 1,
        timeName: '',   // 时间段名称，记录管理员日志用的
        entityType: undefined,
        entityId: undefined,
        ruleId: ''
      },
      tempItem: {},
      defaultTempItem: { // 表单字段
        id: undefined,
        funcId: 1,
        limit: 1,
        timeId: 1
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.sysBaseConfigStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.sysBaseConfigStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.timeRequired'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ]
      },
      itemRowData: [],
      submitting: false,
      propCheckRule: false,
      propRuleId: undefined,
      funcOptions: [],
      treeData: [],
      checkedKeys: []
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    gridTable() {
      return this.$refs['gridTable']
    },
    funcOptionsMap() {
      const map = {}
      this.funcOptions.forEach(item => {
        map[item.value] = item.label
      })
      return map
    },
    timeInfoOptionMap() {
      const map = {}
      this.$store.getters.timeOptions.forEach(item => {
        map[item.value] = item.label
      })
      return map
    }
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.getFuncOptions()
    this.loadTree()
  },

  methods: {
    async loadTree() {
      await getTree().then(res => {
        this.treeData = res.data
      })
    },
    handleCheckChange(keys, datas) {
    },
    renderContent(h, { node, data, store }) {
      let title = ''
      if (data.id == '3') {
        title = this.$t('pages.sysBaseConfig_text2')
      }
      if (data.id == '50' || data.id == '52') {
        title = this.$t('pages.sysBaseConfigSystemSupport1');
      }
      return (
        <div class='custom-tree-node' title={title}>
          <span>{data.label}</span>
        </div>
      )
    },
    getFuncOptions() {
      loadFuncOptions().then(res => {
        this.funcOptions.push(...res.data)
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    ipPortTable: function() {
      return this.$refs['ipPortList']
    },
    formatStrateMsg: function(row) {
      var msg = ''
      row.configs.forEach(item => {
        if (msg != '') {
          msg += '，'
        }
        msg += (item.limit == 1 ? this.$t('pages.allow') : this.$t('pages.prohibit')) + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.funcOptionsMap[item.funcId]
      })
      return msg
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getDataPage(searchQuery)
    },
    itemRowDataApi: function(option) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve({
            code: 20000,
            data: {
              items: this.temp.whiteList,
              total: 1
            }
          })
        }, 1)
      })
    },
    strategySelectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    closed() {
      this.dialogStatus = ''
      this.checkedKeys.splice(0)
      this.$refs.tree.checkSelectedNode([])
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.itemRowData.splice(0, this.itemRowData.length)
      this.temp = Object.assign({}, this.defaultTemp)
      this.tempItem = Object.assign({}, this.defaultTempItem)
      this.propCheckRule = false
      this.propRuleId = undefined
    },
    resetTempItem() {
      this.tempItem = Object.assign({}, this.defaultTempItem)
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        this.$refs['tree'] && this.$refs['tree'].clearFilter()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      this.propCheckRule = !!row.ruleId
      this.propRuleId = row.ruleId
      this.dialogStatus = 'update'
      this.checkedKeys = this.temp.configs.map(config => config.limit == 0 && config.funcId).filter(id => id)
      this.$nextTick(() => {
        this.dialogFormVisible = true
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        this.$refs['tree'] && this.$refs['tree'].clearFilter()
      })
    },
    async handleView(row) {
      await this.loadTree();
      this.handleUpdate(row);
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    ipPortSelectionChange(rowDatas) {
      this.ipPortDeleteable = rowDatas.length > 0
    },
    handleSelectionChange(rowData) {
      this.tempItem = Object.assign({}, rowData)
      if (rowData) this.ipPortEditable = true
    },
    // 格式化数据
    formatData() {
      this.temp.timeName = this.timeInfoOptionMap[this.temp.timeId]
      this.temp.configs.splice(0)
      this.$refs['tree'].getCheckedNodes().forEach(item => {
        if (item.type == 'func') {
          this.temp.configs.push({ funcId: item.id, timeId: this.temp.timeId, limit: 0 })
        }
      })
      this.temp.funcNames = this.temp.configs.map(item => {
        return this.funcOptionsMap[item.funcId]
      }).join('，')
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatData()
          createData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatData()
          updateData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    funcIdFormatter: function(row, data) {
      return this.funcOptionsMap[row.funcId]
    },
    strategyNameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>
