<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-divider content-position="left">{{ $t('pages.detectionRules') }}
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.blockTypeFormatter6') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-divider>
        <LocalZoomIn parent-tag="el-dialog">
          <div v-if="formable">
            <el-button size="small" @click="handleAdd">
              {{ $t('button.insert') }}
            </el-button>
            <el-button size="small" :disabled="!updateable" @click="handleAppUpdate">
              {{ $t('button.edit') }}
            </el-button>
            <el-button size="small" :disabled="!appDeleteable" @click="handleAppDelete">
              {{ $t('button.delete') }}
            </el-button>
          </div>
          <grid-table
            ref="checkedAppGrid"
            :show-pager="false"
            auto-height
            :row-datas="tableSoftList"
            :multi-select="formable"
            :col-model="colModel2"
            :after-load="afterLoad"
            @selectionChangeEnd="selectionAppChangeEnd"
          />
          <span style="color:red">{{ validErrorMsg }}</span>
        </LocalZoomIn>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <FormItem label-width="40px">
          <div>{{ $t('pages.appVersionText1_1') }}</div>
        </FormItem>

        <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
        <div style="color:red; margin-bottom: 5px">{{ validExecuteRuleMsg }}</div>
        <div style="display: flex">
          <el-checkbox v-model="temp.action" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable" @change="actionChange">{{ $t('pages.limitAppRun') }}</el-checkbox>
          <el-checkbox v-model="temp.emptyVersionLimit" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable || temp.action == 0">{{ $t('pages.appVersionText19') }}</el-checkbox>
        </div>
        <ResponseContent
          :select-style="{ 'margin-top': 0 }"
          :status="dialogStatus"
          :show-select="true"
          :editable="formable"
          read-only
          :show-check-rule="true"
          :prop-check-rule="doResponse"
          :prop-rule-id="temp.ruleId"
          @ruleIsCheck="ruleIsCheck"
          @getRuleId="getRuleId"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createStrategy"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createStrategy():updateStrategy()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="()=>{dialogFormVisible = false;}">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="createAppStatus==='updateApp' ? $t('pages.appVersionText23') : $t('pages.appVersionText3')"
      :visible.sync="dialogVisible"
      width="750px"
      @dragDialog="handleDrag"
      @close="close"
    >
      <Form
        ref="appDataForm"
        :rules="tempRules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px"
      >
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.appInfoName')" prop="processName">
              <el-input v-model="temp.processName" :placeholder="$t('pages.appVersionText22')" clearable maxlength=""/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.appLimitType')" prop="limitCode">
              <el-select v-model="temp.limitCode" :placeholder="$t('pages.appVersionText4')" @change="limitCodeChange">
                <el-option
                  v-for="item in limitCodeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.startVersion')" prop="startAppVersion">
              <el-input v-model="temp.startAppVersion" :disabled="fileEndSubmitting || uploadStartFile" style="width: calc(100% - 108px);" />
              <el-upload
                ref="uploadStartApp"
                name="uploadStartApp"
                action="1111"
                accept=".exe"
                :show-file-list="false"
                :disabled="fileStartSubmitting || uploadStartFile"
                :http-request="uploadFile"
                style="height: 30px; display: inline-block;"
              >
                <el-button type="primary" icon="el-icon-upload" :loading="fileStartSubmitting" :disabled="fileEndSubmitting || uploadStartFile" style="margin: 0; padding: 7px 12px;"></el-button>
              </el-upload>
              <el-button type="primary" :disabled="fileEndSubmitting || uploadStartFile" style="width: 60px; margin: 0; padding: 7px 5px;" @click="handleImportStartApp">{{ $t('button.import') }}</el-button>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.endVersion')" prop="endAppVersion">
              <el-input v-model="temp.endAppVersion" :disabled="fileStartSubmitting || uploadEndFile" style="width: calc(100% - 108px);" />
              <el-upload
                ref="uploadEndApp"
                name="uploadEndApp"
                action="1111"
                accept=".exe"
                :show-file-list="false"
                :disabled="fileEndSubmitting || uploadEndFile"
                :http-request="uploadFile"
                style="height: 30px; display: inline-block;"
              >
                <el-button type="primary" icon="el-icon-upload" :loading="fileEndSubmitting" :disabled="fileStartSubmitting || uploadEndFile" style="margin: 0; padding: 7px 12px;"></el-button>
              </el-upload>
              <el-button type="primary" :disabled="fileStartSubmitting || uploadEndFile" style="width: 60px; margin: 0; padding: 7px 5px;" @click="handleImportEndApp">{{ $t('button.import') }}</el-button>
            </FormItem>
          </el-col>
        </el-row>
        <el-row v-if="fileStartSubmitting">
          <el-col :span="22">
            <el-progress type="line" :percentage="startPercentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
        <el-row v-if="fileEndSubmitting">
          <el-col :span="22">
            <el-progress type="line" :percentage="endPercentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
      </Form>
      <span style="color: #2674b2; margin-left: 20px" >{{ $t('pages.appVersionText21') }}</span>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="operationType === 1 ? addData() : updateData()" >
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <app-select-dlg ref="appSelectDlg" :multiple-group="multipleGroup" :add-type="addType" @select="appSelectEnd" />
  </div>
</template>
<script>
import {
  createStrategy,
  deleteStrategy,
  getStrategyByName,
  getStrategyPage,
  updateStrategy,
  upload
} from '@/api/behaviorManage/application/appVersion'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import { validatePolicy, timeIdValidator } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import axios from 'axios'

export default {
  name: 'AppVersion',
  components: { AppSelectDlg, ResponseContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      stgCode: 8,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '300', formatter: this.stgFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      colModel2: [
        { prop: 'processName', label: 'processName', width: '150', sort: true },
        { prop: 'startAppVersion', label: 'startVersion', width: '150', sort: true },
        { prop: 'endAppVersion', label: 'endVersion', width: '150', sort: true },
        { prop: 'limitCodeLabel', label: 'versionLimitType', width: '180', sort: true }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      rowData: [],
      showTree: true,
      treeable: true,
      deleteable: false,
      appDeleteable: false,
      updateable: false,
      addBtnAble: false,
      operationType: undefined, // 1新增，2修改
      temp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        remark: '',
        active: false,
        timeId: 1,
        softList: [],
        entityType: '',
        entityId: undefined,
        processName: null,
        limitCode: null,
        startAppVersion: null,
        endAppVersion: null,
        ruleId: null,
        action: 0,
        emptyVersionLimit: 0
      },
      tableSoftList: [],
      treeData: [{ id: -1, dataId: -1, label: this.$t('pages.configurated'), parentId: 0 }, { id: 0, dataId: 0, label: this.$t('pages.softwareTypes5') }],
      selectTreeId: 1,
      dialogFormVisible: false,
      dialogVisible: false,
      dialogStatus: '',
      submitting: false,
      startPercentage: 0,
      endPercentage: 0,
      fileStartSubmitting: false,
      uploadStartFile: true,
      fileEndSubmitting: false,
      uploadEndFile: true,
      doResponse: false,
      createAppStatus: '',
      source: null,
      multipleGroup: false,
      addType: null,
      textMap: {
        update: this.formable ? this.$t('pages.updateAppVersionStrategy') : this.$t('pages.appVersionStrategyDetail'),
        create: this.$t('pages.addAppVersionStrategy')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.timeRequired'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ]
      },
      propCheckRule: false,
      validErrorMsg: undefined,
      validExecuteRuleMsg: undefined,
      limitCodeOptions: [{ label: '<' + this.$t('pages.startVersion'), value: 2 }, { label: '=' + this.$t('pages.startVersion'), value: 3 }, { label: '>' + this.$t('pages.startVersion'), value: 1 }, { label: '!=' + this.$t('pages.startVersion'), value: 4 }, { label: '<=' + this.$t('pages.startVersion'), value: 6 },
        { label: '>=' + this.$t('pages.startVersion'), value: 5 }, { label: this.$t('pages.appVersionText5'), value: 7 }, { label: this.$t('pages.appVersionText6'), value: 8 }
      ]
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    gridTable() {
      return this.$refs['strategyTable']
    },
    tempRules() {
      return {
        processName: [{ required: true, validator: this.processNameValidator, trigger: 'blur' }],
        limitCode: [{ required: true, message: this.$t('pages.appVersionText7'), trigger: 'blur' }],
        startAppVersion: [{ required: true, message: this.$t('pages.appVersionText8'), trigger: 'blur' }],
        endAppVersion: [{ required: !this.uploadEndFile, message: this.$t('pages.appVersionText9'), trigger: 'blur' }]
      }
    },
    appList() {
      const appList = [...this.rowData]
      return appList.map((item, index) => { item.id = index + 1; return item })
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    resetButtonStatus() {
      this.fileStartSubmitting = false
      this.uploadStartFile = true
      this.fileEndSubmitting = false
      this.uploadEndFile = true
      this.$nextTick(() => {
        this.$refs['appDataForm'].clearValidate()
      })
    },
    resetAppInfo() {
      this.temp.processName = null
      this.temp.limitCode = null
      this.temp.startAppVersion = null
      this.temp.endAppVersion = null
    },
    close() {
      this.resetButtonStatus()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.tableSoftList = []
      this.validErrorMsg = ''
      this.validExecuteRuleMsg = ''
      this.doResponse = false
      this.updateable = false
      this.appDeleteable = false
      this.createAppStatus = ''
    },
    showAppSelectDlg() {
      this.$refs.appSelectDlg.show()
    },
    getLimitLabel(limitCode) {
      let label = ''
      this.limitCodeOptions.forEach(item => {
        if (item.value == limitCode) {
          label = item.label
        }
      })
      return label
    },
    addData() {
      this.submitting = true
      this.$refs['appDataForm'].validate((valid) => {
        if (valid && this.validApp()) {
          if (this.temp.limitCode < 7 && this.temp.endAppVersion) {
            this.temp.endAppVersion = null
          }
          const label = this.getLimitLabel(this.temp.limitCode)
          const data = { processName: this.temp.processName, startAppVersion: this.temp.startAppVersion, endAppVersion: this.temp.endAppVersion, limitCode: this.temp.limitCode, limitCodeLabel: label }
          const filterData = this.tableSoftList.filter(appInfo => {
            return appInfo.processName == data.processName && appInfo.startAppVersion == data.startAppVersion && appInfo.endAppVersion == data.endAppVersion && appInfo.limitCode == data.limitCode
          })
          if (filterData.length == 0) {
            this.tableSoftList.push(data)
            this.tableSoftList.map((item, index) => { item.id = index + 1; return item })
          }
          this.submitting = false
          this.dialogVisible = false
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['appDataForm'].validate((valid) => {
        if (valid && this.validApp()) {
          if (this.temp.limitCode < 7 && this.temp.endAppVersion) {
            this.temp.endAppVersion = null
          }
          const label = this.getLimitLabel(this.temp.limitCode)
          const data = { id: null, processName: this.temp.processName, startAppVersion: this.temp.startAppVersion, endAppVersion: this.temp.endAppVersion, limitCode: this.temp.limitCode, limitCodeLabel: label }
          const selectedId = this.$refs['checkedAppGrid'].getSelectedIds()
          let index
          for (let i = 0; i < this.tableSoftList.length; i++) {
            const tempId = this.tableSoftList[i].id
            if (selectedId.indexOf(tempId) > -1) {
              data.id = tempId
              index = i
            }
          }
          this.tableSoftList.splice(index, 1, data)
          this.submitting = false
          this.dialogVisible = false
        } else {
          this.submitting = false
        }
      })
    },
    validApp() {
      if (this.temp.limitCode > 6) {
        const result = this.compareVersions(this.temp.startAppVersion, this.temp.endAppVersion)
        if (result != -1) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.appVersionText20'),
            type: 'error',
            duration: 2000
          })
          return false
        }
      }
      return true
    },
    compareVersions(version1, version2) {
      // 将版本号拆分成数字数组
      const arr1 = version1.split('.');
      const arr2 = version2.split('.');
      // 遍历数字数组进行逐段比较
      for (var i = 0; i < Math.max(arr1.length, arr2.length); i++) {
        const num1 = parseInt(arr1[i] || 0); // 如果数组长度不够，则将缺失部分补0
        const num2 = parseInt(arr2[i] || 0);
        if (num1 < num2) {
          return -1; // 版本1小于版本2
        } else if (num1 > num2) {
          return 1; // 版本1大于版本2
        }
      }
      return 0; // 版本1等于版本2
    },
    limitCodeChange(limitCode) {
      if (limitCode < 7) {
        this.uploadStartFile = false
        this.uploadEndFile = true
      } else if (limitCode >= 7) {
        this.uploadStartFile = false
        this.uploadEndFile = false
      }
    },
    handleImportStartApp() {
      this.addType = 'start'
      this.$refs.appSelectDlg.show()
    },
    handleImportEndApp() {
      this.addType = 'end'
      this.$refs.appSelectDlg.show()
    },
    selectAppEnd(datas) {
      if (datas && datas.length > 0) {
        const existIds = []
        this.tableSoftList.forEach(item => existIds.push(item.id))
        datas.forEach(item => {
          if (existIds.indexOf(item.id) < 0) {
            this.tableSoftList.splice(0, 0, item)
          }
        })
        this.tableSoftList.forEach((item, i) => { item.rowId = i + 1 })
      }
    },
    resetUploadComponent(data) {  // 重置上传组件状态
      if (data == 'uploadStartApp') {
        this.fileStartSubmitting = false
        this.startPercentage = 0
      } else if (data == 'uploadEndApp') {
        this.fileEndSubmitting = false
        this.endPercentage = 0
      }
    },
    handleAdd() {
      this.createAppStatus = 'addApp'
      this.operationType = 1
      this.resetAppInfo()
      this.dialogVisible = true
    },
    handleAppUpdate() {
      this.createAppStatus = 'updateApp'
      this.operationType = 2
      const selectedData = this.$refs['checkedAppGrid'].getSelectedDatas()
      if (selectedData.length == 1) {
        this.dialogVisible = true
        this.temp.processName = selectedData[0].processName
        this.temp.limitCode = selectedData[0].limitCode
        if (selectedData[0].limitCode < 7) {
          this.temp.startAppVersion = selectedData[0].startAppVersion
          this.temp.endAppVersion = null
          this.uploadStartFile = false
          this.uploadEndFile = true
        } else if (selectedData[0].limitCode >= 7) {
          this.temp.startAppVersion = selectedData[0].startAppVersion
          this.temp.endAppVersion = selectedData[0].endAppVersion
          this.uploadStartFile = false
          this.uploadEndFile = false
        }
      }
    },
    handleAppDelete() {
      const selectedIds = this.$refs['checkedAppGrid'].getSelectedIds()
      if (selectedIds.length > 0) {
        this.appDeleteable = true
        for (let i = 0; i < this.tableSoftList.length; i++) {
          const tempId = this.tableSoftList[i].id
          if (selectedIds.indexOf(tempId) > -1) {
            this.tableSoftList.splice(i, 1)
            i--
          }
        }
      }
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    uploadFile(data) {
      const name = data.filename
      if (name == 'uploadStartApp') {
        this.fileStartSubmitting = true
        this.startPercentage = 0
      } else if (name == 'uploadEndApp') {
        this.fileEndSubmitting = true
        this.endPercentage = 0
      }
      // 上传钩子，用来获取进度条
      const onUploadProgress = (progressEvent) => {
        const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
        if (name == 'uploadStartApp') {
          this.startPercentage = parseInt(percent)
        } else if (name == 'uploadEndApp') {
          this.endPercentage = parseInt(percent)
        }
      }
      this.source = this.connectionSource()
      const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
      const fd = new FormData()
      fd.append('uploadFile', data.file)
      // 调用后台接口获取进程windows属性
      upload(fd, onUploadProgress, cacheToken).then(res => {
        if (name == 'uploadStartApp') {
          this.resetUploadComponent(name)
          this.appendFile(name, res.data)
        } else if (name == 'uploadEndApp') {
          this.resetUploadComponent(name)
          this.appendFile(name, res.data)
        }
      }).catch(res => {
        this.resetUploadComponent(name)
        if (axios.isCancel(res)) {
          // 取消上传后的操作，待补充
        }
      })
      return false // 屏蔽了action的默认上传
    },
    appendFile(name, data) {
      const fn = () => {
        this.temp.processName = data.processName
        if (name == 'uploadStartApp') {
          this.temp.startAppVersion = data.productVersion
        } else if (name == 'uploadEndApp') {
          this.temp.endAppVersion = data.productVersion
        }
        if (!data.productVersion) {
          this.$alert(this.$t('pages.appVersionText24'), this.$t('text.prompt'))
        }
      }
      if (this.temp.processName && this.temp.processName != data.processName) {
        this.$confirmBox(this.$t('pages.appVersionText10'), this.$t('text.prompt')).then(fn).catch(() => {})
      } else {
        fn()
      }
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    actionChange(val) {
      if (val == 0) {
        this.temp.emptyVersionLimit = 0
      }
      this.validRuleMsg()
    },
    appSelectEnd(datas) {
      if (!datas) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.appVersionText11'),
          type: 'warning',
          duration: 2000
        })
        return
      } else if (datas.info.length > 1) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.appVersionText12'),
          type: 'warning',
          duration: 2000
        })
        return
      } else {
        if (datas.type == 'start') {
          if (!this.temp.processName || (this.temp.processName && this.temp.processName == datas.info[0].processName)) {
            this.temp.processName = datas.info[0].processName
            this.temp.startAppVersion = datas.info[0].productVersion
          } else if (this.temp.processName && this.temp.processName != datas.info[0].processName) {
            this.$confirmBox(this.$t('pages.appVersionText10'), this.$t('text.prompt')).then(() => {
              this.temp.processName = datas.info[0].processName
              this.temp.startAppVersion = datas.info[0].productVersion
            }).catch(() => {})
          }
        } else if (datas.type == 'end') {
          if (!this.temp.processName || (this.temp.processName && this.temp.processName == datas.info[0].processName)) {
            this.temp.processName = datas.info[0].processName
            this.temp.endAppVersion = datas.info[0].productVersion
          } else if (this.temp.processName && this.temp.processName != datas.info[0].processName) {
            this.$confirmBox(this.$t('pages.appVersionText10'), this.$t('text.prompt')).then(() => {
              this.temp.processName = datas.info[0].processName
              this.temp.endAppVersion = datas.info[0].productVersion
            }).catch(() => {})
          }
        }
        return
      }
    },
    validateProcesName(name) {
      let flag = true
      if (this.temp.processName && this.temp.processName != name) {
        this.$confirmBox(this.$t('pages.appVersionText10'), this.$t('text.prompt')).then(() => {
          flag = true
        }).catch(() => {
          flag = false
        })
      }
      return flag
    },
    stgFormatter(row, data) {
      let msg = ''
      if (row.action) {
        msg += this.$t('pages.limitAppRun');
        // msg += this.$t('pages.appVersionText17')
      }
      if (row.ruleId) {
        msg += msg.length > 0 ? '、' : ''
        msg += this.$t('pages.triggerResponseRule')
      }
      if (row.emptyVersionLimit) {
        msg += msg.length > 0 ? '、' : ''
        msg += this.$t('pages.appVersionText19')
      }
      return msg.length > 0 ? this.$t('pages.appVersionText1') + '：' + msg : '';
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.propCheckRule = false
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      this.formatRowData(this.temp)
      this.propCheckRule = !!row.ruleId
      this.doResponse = !!row.ruleId
      this.dialogFormVisible = true
      this.dialogStatus = ''
      this.$nextTick(() => {
        this.dialogStatus = 'update'
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      })
    },
    handleImport() {
    },
    handleExport() {
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    selectionAppChangeEnd(rowDatas) {
      if (rowDatas.length > 0) {
        this.appDeleteable = true
      } else {
        this.appDeleteable = false
      }
      if (rowDatas.length == 1) {
        this.updateable = true
      } else {
        this.updateable = false
      }
    },
    formatFormData() {
      if (!this.doResponse && this.temp.ruleId) {
        this.temp.ruleId = null
      }
      this.temp.softList.splice(0, this.temp.softList.length, ...this.tableSoftList)
    },
    formatRowData(rowData) {
      if (rowData.softList) {
        rowData.softList.map(item => {
          item.limitCodeLabel = this.getLimitLabel(item.limitCode)
          return item
        })
        this.tableSoftList.splice(0, rowData.softList.length, ...rowData.softList)
      }
    },
    validateFormData() {
      if (this.tableSoftList.length === 0) {
        this.validErrorMsg = this.$t('pages.appVersionText13')
        return false
      }
      if (!this.temp.action && !this.temp.emptyVersionLimit && !this.doResponse) {
        this.validExecuteRuleMsg = this.$t('pages.install_Msg15')
        return false
      }
      if (this.doResponse && !this.temp.ruleId) {
        return false
      }
      return true
    },
    createStrategy() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validateFormData()) {
          this.formatFormData()
          const tempData = JSON.parse(JSON.stringify(this.temp))
          createStrategy(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateStrategy() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validateFormData()) {
          this.formatFormData()
          const tempData = JSON.parse(JSON.stringify(this.temp))
          updateStrategy(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    processNameValidator(rule, value, callback) {
      if (!this.temp.processName) {
        callback(new Error(this.$t('pages.appVersionText14')))
      } else if (this.temp.processName) {
        const suffix = this.temp.processName.substr(this.temp.processName.lastIndexOf('.') + 1)
        if (suffix == 'exe' || suffix == 'EXE') {
          callback()
        } else {
          callback(new Error(this.$t('pages.appVersionText15')))
        }
      } else {
        callback()
      }
    },
    ruleIsCheck(data) {
      this.doResponse = data === 1
      this.validRuleMsg()
    },
    //   校验响应规则是否有选中
    validRuleMsg() {
      if (!!this.temp.action || !!this.temp.emptyVersionLimit || !!this.doResponse) {
        this.validExecuteRuleMsg = ''
      } else {
        this.validExecuteRuleMsg = this.$t('pages.install_Msg15')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.prohibit >>> .el-checkbox__inner {
  margin-left: 17px;
}
</style>
