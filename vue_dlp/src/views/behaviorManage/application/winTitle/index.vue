<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="WinTitleTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="dialogStatus=''"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />
        <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
        <FormItem :label="$t('table.keyword')" prop="winTitle">
          <el-input v-model="temp.winTitle" :disabled="!formable" type="textarea" rows="8" resize="none" maxlength=""></el-input>
        </FormItem>
        <FormItem>
          <div style="line-height: 18px; color: #409eff; font-size: small; font-weight: bold;">{{ $t('pages.winTitle_text1') }}</div>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <FormItem label-width="40px">
          <div>{{ $t('pages.winTitle_text2_1') }}</div>
        </FormItem>

        <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
        <FormItem label-width="0px" prop="action">
          <el-checkbox v-model="temp.action" class="prohibit" :true-label="1" :false-label="0" :disabled="!formable" @change="actionChange">{{ $t('pages.limitAppRun') }}</el-checkbox>
          <ResponseContent
            :select-style="{ 'margin-top': 0 }"
            style="line-height: 15px !important;"
            :status="dialogStatus"
            :show-select="true"
            :editable="formable"
            read-only
            :prop-check-rule="doResponse"
            :show-check-rule="true"
            :prop-rule-id="propRuleId"
            @getRuleId="getRuleId"
            @ruleIsCheck="ruleIsCheck"
            @validate="(val) => { responseValidate = val }"
          />
        </FormItem>

      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="()=>{dialogFormVisible = false;}">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importWinTitleBlockStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import {
  getWinTitlePage, getWinTitleByName, createWinTitle, updateWinTitle, deleteWinTitle
} from '@/api/behaviorManage/application/winTitle'
import {
  enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity,
  objectFormatter, entityLink, refreshPage, buttonFormatter
} from '@/utils'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { validatePolicy, timeIdValidator } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'WinTitle',
  components: { ResponseContent, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 7,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [{ formatter: this.entityFormatter, click: this.entityClick }]
        },
        { prop: '', label: 'stgMessage', width: '300', formatter: this.stgFormatter },
        // { prop: 'winTitle', label: 'keyword', width: '200' },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        winTitle: '',
        timeId: 1,
        entityType: undefined,
        entityId: undefined,
        alarmSetupId: 0,
        ruleId: '',
        action: 0
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.winTitleStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.winTitleStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        timeId: [
          { required: true, message: this.$t('pages.timeRequired'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ],
        winTitle: [{ required: true, message: this.$t('pages.keywordRequired'), trigger: 'blur' }],
        action: [{ required: true, validator: this.actionValidator, trigger: 'blur' }]
      },
      propRuleId: undefined,
      doResponse: false
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    gridTable() {
      return this.$refs['WinTitleTable']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  methods: {
    stgFormatter(row, data) {
      const brTag = data ? '' : '<br>'
      let msg = ''
      if (row.action) {
        msg += this.$t('pages.limitAppRun');
        // msg += this.$t('pages.appVersionText17')
      }
      if (row.ruleId) {
        msg += msg.length > 0 ? '、' : ''
        msg += this.$t('pages.triggerResponseRule')
      }
      const checkMsg = msg.length > 0 ? `<b>${this.$t('pages.executionRules')}</b>：${this.$t('pages.winTitle_text2') + '：' + msg}；${brTag}` : ''
      const keyword = `<b>${this.$t('table.keyword')}</b>：${row.winTitle}；${brTag}`
      return `${keyword}  ${checkMsg}`
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getWinTitlePage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.doResponse = false
      this.propRuleId = undefined
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.defaultTemp, JSON.parse(JSON.stringify(row)))
      this.doResponse = !!row.ruleId
      this.propRuleId = row.ruleId
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        this.dialogStatus = 'update'
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    formatFormData() {
      if (!this.doResponse) {
        this.temp.ruleId = null
      } else {
        this.temp.ruleId = this.propRuleId
      }
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.responseValidate) {
          this.formatFormData()
          const tempData = Object.assign({}, this.temp)
          createWinTitle(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.responseValidate) {
          this.formatFormData()
          const tempData = Object.assign({}, this.temp)
          updateWinTitle(tempData).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.dialogStatus = ''
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteWinTitle({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getWinTitleByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    actionValidator(rule, value, callback) {
      if (!this.temp.action && !this.doResponse) {
        callback(new Error(this.$t('pages.install_Msg51')))
      } else {
        callback()
      }
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    importSuccess() {
      this.handleFilter()
    },
    ruleIsCheck(data) {
      this.doResponse = data === 1
      this.actionChange()
    },
    //   选中响应规则后，清空红色提示
    actionChange() {
      this.$refs.dataForm.validateField('action')
    }
  }
}
</script>
<style lang="scss" scoped>
.prohibit >>> .el-checkbox__inner {
  margin-left: 17px;
}
</style>
