<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('route.groupPolicy')"
      :stg-code="158"
      :active-able="activeAble"
      :time-able="false"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-tabs v-model="activeName" @tab-click="tabClick">
          <el-tab-pane v-for="(value, key) in functionMap" :key="key" :label="key" :name="key">
            <el-card v-for="item in value" :key="item.id" :ref="item.name" :body-style="{ 'padding': '3px' }" class="box-card">
              <FormItem :label="item.name" label-width="300px" >
                <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                  <div slot="content">
                    <span v-if="item.platform">{{ $t('pages.supportPlatform') }}{{ item.platform }}<br/></span>
                    <span>{{ $t('pages.instructions') }}{{ item.helpInfo }}</span> <br/>
                    <span v-if="item.reminder" style="color: red">{{ $t('pages.attenion') }}{{ item.reminder }}</span>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                <el-radio-group v-model="item.strategy.settingOption" size="small" style="margin-left: 20px;" :disabled="((item.type == 11 || item.type == 13) && !editable) || !formable2" @change="changedVal => settingOptionChange(changedVal, value, item)">
                  <el-radio :label="2">
                    {{ $t('pages.groupPolicy_text31') }}
                    <el-tooltip v-if="reminderFunctions.indexOf(item.type) > -1" effect="dark" placement="bottom-start">
                      <div slot="content">
                        <span v-show="item.type == 6">{{ $t('pages.groupPolicy_text3') }}</span>
                        <span v-show="item.type == 7">{{ $t('pages.groupPolicy_text5') }}</span>
                        <span v-show="item.type == 8">{{ $t('pages.groupPolicy_text7') }}</span>
                        <span v-show="item.type == 9">{{ $t('pages.groupPolicy_text10') }}</span>
                        <span v-show="item.type == 11">{{ $t('pages.groupPolicy_text12') }}</span>
                        <span v-show="item.type == 12">{{ $t('pages.groupPolicy_text15') }}</span>
                      </div>
                      <i class="el-icon-info" />
                    </el-tooltip>
                  </el-radio>
                  <el-radio :label="0">
                    {{ $t('pages.disable') }}
                    <el-tooltip v-if="reminderFunctions.indexOf(item.type) > -1" effect="dark" placement="bottom-start">
                      <div slot="content">
                        <span v-show="item.type == 6">{{ $t('pages.groupPolicy_text3') }}</span>
                        <span v-show="item.type == 7">{{ $t('pages.groupPolicy_text5') }}</span>
                        <span v-show="item.type == 8">{{ $t('pages.groupPolicy_text7') }}</span>
                        <span v-show="item.type == 9">{{ $t('pages.groupPolicy_text10') }}</span>
                        <span v-show="item.type == 11">{{ $t('pages.groupPolicy_text12') }}</span>
                        <span v-show="item.type == 12">{{ $t('pages.groupPolicy_text15') }}</span>
                      </div>
                      <i class="el-icon-info" />
                    </el-tooltip>
                  </el-radio>
                  <el-radio :label="1">{{ $t('pages.enable') }}</el-radio>
                </el-radio-group>
              </FormItem>
              <FormItem v-if="item.type == 6">
                <el-row v-show="item.strategy.settingOption == 1">
                  <el-col style="margin-left: 60px">
                    <i18n path="pages.groupPolicy_text1">
                      <el-input slot="num" v-model="item.strategy.settingValue" style="width: 100px" type="number" maxlength="2" :min="0" :max="20" :disabled="!formable2" @input="numberLimit(arguments[0], value, item, 'settingValue', 0, 20)"></el-input>
                    </i18n>
                  </el-col>
                </el-row>
              </FormItem>
              <FormItem v-if="item.type == 7">
                <el-row v-show="item.strategy.settingOption == 1">
                  <el-col style="margin-left: 150px">
                    <i18n path="pages.groupPolicy_text4">
                      <el-input slot="num" v-model="item.strategy.settingValue" style="width: 100px" type="number" maxlength="3" :min="0" :max="998" :disabled="!formable2" @input="numberLimit(arguments[0], value, item, 'settingValue', 0, 998)"></el-input>
                    </i18n>
                  </el-col>
                </el-row>
              </FormItem>
              <FormItem v-if="item.type == 8">
                <el-row v-show="item.strategy.settingOption == 1">
                  <el-col style="margin-left: 80px">
                    <i18n path="pages.groupPolicy_text6">
                      <el-input slot="num" v-model="item.strategy.settingValue" style="width: 100px" type="number" maxlength="3" :min="0" :max="999" :disabled="!formable2" @input="numberLimit(arguments[0], value, item, 'settingValue', 0, 999)"></el-input>
                    </i18n>
                  </el-col>
                </el-row>
              </FormItem>
              <FormItem v-if="item.type == 9">
                <el-row v-show="item.strategy.settingOption == 1">
                  <el-col style="margin-left: 80px">
                    <i18n path="pages.groupPolicy_text8">
                      <el-input slot="num" v-model="item.strategy.settingValue" style="width: 100px" type="number" :min="0" :max="24" :disabled="!formable2" maxlength="" @input="numberLimit(arguments[0], value, item, 'settingValue', 0, 24)"></el-input>
                    </i18n>
                  </el-col>
                </el-row>
              </FormItem>
              <FormItem v-if="item.type == 11">
                <el-row v-show="item.strategy.settingOption == 1">
                  <el-col style="margin-left: 80px">
                    <i18n path="pages.groupPolicy_text11">
                      <el-input slot="min" v-model="item.strategy.settingValue" style="width: 100px" type="number" :min="0" :max="99999" :disabled="!formable2" maxlength="" @input="numberLimit(arguments[0], value, item, 'settingValue', 0, 99999)" @change="numberChange(arguments[0], value, item, 'settingValue', 0, 99999)"></el-input>
                    </i18n>
                  </el-col>
                </el-row>
              </FormItem>
              <FormItem v-if="item.type == 12">
                <el-row v-show="item.strategy.settingOption == 1">
                  <el-col style="margin-left: 80px">
                    <i18n path="pages.groupPolicy_text32">
                      <i slot="icon" class="el-icon-info" />
                      <el-input slot="num" v-model="item.strategy.settingValue" style="width: 100px" type="number" :min="0" :max="999" maxlength="" :disabled="!formable2" @input="numberLimit(arguments[0], value, item, 'settingValue', 0, 999)" @change="numberChange(arguments[0], value, item, 'settingValue', 0, 999)"></el-input>
                    </i18n>
                  </el-col>
                </el-row>
              </FormItem>
              <FormItem v-if="item.type == 13">
                <el-row v-show="item.strategy.settingOption === 1">
                  <el-col style="margin-left: 80px">
                    <i18n path="pages.groupPolicy_text16">
                      <el-input slot="min" v-model="item.strategy.settingValue" style="width: 100px" type="number" maxlength="" :min="1" :max="99999" :disabled="!formable2" @input="numberLimit(arguments[0], value, item, 'settingValue', 1, 99999)" @change="numberChange(arguments[0], value, item, 'settingValue', 1, 99999)"></el-input>
                    </i18n>
                  </el-col>
                </el-row>
              </FormItem>
              <FormItem v-if="item.id == 12">
                <el-row v-show="item.strategy.settingOption == 1">
                  <el-col style="margin-left: 80px">
                    <i18n path="pages.groupPolicy_text16">
                      <el-input slot="second" v-model="item.strategy.settingValue" style="width: 100px" type="number" :min="0" :max="86400" :disabled="!formable2" @input="numberLimit(arguments[0], value, item, 'settingValue', 0, 86400)"></el-input>
                    </i18n>
                  </el-col>
                </el-row>
              </FormItem>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </template>
    </stg-dialog>
  </div>
</template>

<script>
import {
  createStrategy,
  getStrategyByName,
  listFunctionMap,
  updateStrategy
} from '@/api/behaviorManage/application/groupPolicy'

export default {
  name: 'GroupPolicyDlg',
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      // 进程系统类型：1-windows，2-li0.00nux，4-mac
      osType: 1,
      submitting: false,
      slotName: undefined,
      temp: {},
      defaultTemp: { // 表单字段
        id: '',
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: '',
        strategy: []
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        action: [{ required: true, validator: this.actionValidator, trigger: 'blur' }]
      },
      propRuleId: undefined,
      dialogStatus: '',
      editable: false,
      isEdit: true,
      activeName: undefined,
      functionMap: undefined,
      tempS: {
        funcId: undefined,
        settingOption: 2,
        settingValue: undefined,
        regKey: undefined,
        active: true,
        sstType: undefined,
        alarmType: undefined,
        ruleId: undefined,
        entityType: 1,
        entityId: undefined
      },
      reminderFunctions: [6, 7, 8, 9, 11, 12],
      accountLockThreshold: 0
    }
  },
  computed: {
    typeTreeData() {
      if (this.osType == 1) {
        return this.typeTreeDataWindow
      } else if (this.osType == 2) {
        return this.typeTreeDataLinux
      } else if (this.osType == 4) {
        return this.typeTreeDataMac
      } else if (this.osType == 7) {
        return this.typeTreeDataWindow
      }
      return null
    },
    formable2() {
      return this.formable && this.isEdit
    }
  },
  created() {
    // this.loadFunctionMap()
    // this.reset
    // Temp()
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.activeName = undefined
    },
    closed() {
      this.resetTemp()
    },
    slotChange(name, slotTemp) {
      this.slotName = name
    },
    tabClick(tab, event) {
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    numberChange(event, group, item, field, min, max) {
      if (item.id == 39) {
        if (item.strategy.settingValue == 0) {
          this.editable = false
          group.forEach(func => {
            if (func.id == 38 || func.id == 40) {
              func.strategy.settingValue = 0
              func.strategy.settingOption = 2
            }
          })
        } else {
          this.editable = true
          if (this.accountLockThreshold == 0) {
            group.forEach(func => {
              if (func.id == 38 || func.id == 40) {
                func.strategy.settingValue = 30
                func.strategy.settingOption = 1
              }
            })
          }
        }
        this.accountLockThreshold = item.strategy.settingValue
      } else if (item.id == 38) {
        group.forEach(func => {
          if (func.id == 40 && item.strategy.settingValue < func.strategy.settingValue) {
            func.strategy.settingValue = item.strategy.settingValue
          }
        })
      } else if (item.id == 40) {
        group.forEach(func => {
          if (func.id == 38 && item.strategy.settingValue > func.strategy.settingValue) {
            func.strategy.settingValue = item.strategy.settingValue
          }
        })
      }
    },
    numberLimit(value, group, item, field, min, max) {
      let limitNumber
      let settingOption
      if (value == undefined || value == null || value == '') {
        value = min
      }
      if (item.id == 34) {
        group.forEach(func => {
          if (func.id == 35) {
            limitNumber = func.strategy[field]
            settingOption = func.strategy['settingOption']
          }
        })
        if (settingOption == 1) {
          item.strategy[field] = value > limitNumber ? limitNumber == 0 ? limitNumber : limitNumber - 1 : value < min ? min : parseInt(value)
        } else {
          item.strategy[field] = value > max ? max : value < min ? min : parseInt(value)
        }
      } else if (item.id == 35) {
        group.forEach(func => {
          if (func.id == 34) {
            limitNumber = func.strategy[field]
            settingOption = func.strategy['settingOption']
          }
        })
        if (settingOption == 1) {
          if (typeof (value) == 'object') {
            value = item.strategy.settingValue
          }
          item.strategy[field] = value > max ? max : value < limitNumber ? limitNumber == 0 ? limitNumber : limitNumber + 1 : parseInt(value)
        } else {
          item.strategy[field] = value > max ? max : value < min ? min : parseInt(value)
        }
      } else {
        item.strategy[field] = value > max ? max : value < min ? min : parseInt(value)
      }

      // if (item.id === 39) {
      //   if (value == 0) {
      //     this.editable = false
      //     group.forEach(func => {
      //       if (func.id === 38 || func.id === 40) {
      //         func.strategy.settingValue = 0
      //         func.strategy.settingOption = 2
      //       }
      //     })
      //   } else {
      //     this.editable = true
      //     if (this.accountLockThreshold == 0) {
      //       group.forEach(func => {
      //         if (func.id === 38 || func.id === 40) {
      //           func.strategy.settingValue = 30
      //           func.strategy.settingOption = 1
      //         }
      //       })
      //     }
      //   }
      //   this.accountLockThreshold = value
      // } else if (item.id === 38) {
      //   group.forEach(func => {
      //     if (func.id === 40 && value < func.strategy.settingValue) {
      //       func.strategy.settingValue = value
      //     }
      //   })
      // } else if (item.id === 40) {
      //   group.forEach(func => {
      //     if (func.id === 38 && value > func.strategy.settingValue) {
      //       func.strategy.settingValue = value
      //     }
      //   })
      // }
    },
    settingOptionChange(val, group, func) {
      func.strategy.settingValue = val
      if (val == 1) {
        if (func.type == 5 || func.type == 10) {
          func.strategy.settingValue = 1
        } else if (func.type == 6) {
          func.strategy.settingValue = 0
        } else if (func.type == 7) {
          func.strategy.settingValue = 0
        } else if (func.type == 8) {
          let limitNumber
          group.forEach(func => {
            if (func.id == 34) {
              limitNumber = func.strategy.settingValue
            }
          })
          func.strategy.settingValue = limitNumber < 42 || !limitNumber ? 42 : limitNumber + 1
        } else if (func.type == 9) {
          func.strategy.settingValue = 0
        } else if (func.type == 11) {
          func.strategy.settingValue = 30
        } else if (func.type == 12) {
          this.accountLockThreshold = 5
          func.strategy.settingValue = 5
        } else if (func.type == 13) {
          func.strategy.settingValue = 30
        }
        // 账户锁定阈值变更会影响账户锁定时间和重置账户锁定计数器的配置
        if (func.type == 12) {
          this.editable = true
          group.forEach(el => {
            if (el.type == 11 || el.type == 13) {
              el.strategy.settingOption = 1
              el.strategy.settingValue = 30
            }
          })
        }
      } else {
        if (func.type > 4) {
          func.strategy.settingValue = val == 2 ? undefined : 0
        }
        // 账户锁定阈值变更会影响账户锁定时间和重置账户锁定计数器的配置
        if (func.type == 12) {
          this.editable = false
          group.forEach(el => {
            if (el.type == 11 || el.type == 13) {
              el.strategy.settingValue = 0
              el.strategy.settingOption = 2
            }
          })
        }
      }
    },
    loadFunctionMap: function(row) {
      listFunctionMap().then(respond => {
        this.functionMap = respond.data
        for (var key in this.functionMap) {
          const functions = this.functionMap[key]
          if (row) {
            functions.forEach(func => {
              func.strategy = Object.assign({}, this.tempS, func.strategy)
              row.strategy.forEach(data => {
                if (data.funcId == func.id) {
                  func.strategy = Object.assign(func.strategy, data)
                  if (data.settingValue == -1) {
                    func.strategy.settingValue = data.regKey
                  }
                  if (!func.strategy.settingValue) {
                    this.$set(func.strategy, 'settingValue', 0);
                  }
                  if (func.type == 12) {
                    if (func.strategy.settingOption == 1 && func.strategy.settingValue > 0) {
                      this.editable = true
                    } else {
                      this.editable = false
                    }
                  }
                }
              })
            })
          } else {
            functions.forEach(func => {
              func.strategy = Object.assign({}, this.tempS)
              if (func.type == 12) {
                if (func.strategy.settingOption == 1 && func.strategy.settingValue > 0) {
                  this.editable = true
                } else {
                  this.editable = false
                }
              }
            })
          }
        }
        this.activeName = Object.keys(this.functionMap)[0]
      })
    },
    handleCreate() {
      this.resetTemp()
      this.loadFunctionMap()
      this.isEdit = true
      this.dialogStatus = 'create'
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.loadFunctionMap(row)
      this.isEdit = true
      this.dialogStatus = 'update'
      this.$refs['stgDlg'].show(row, this.formable2)
    },
    handleView(row) {
      this.resetTemp()
      this.loadFunctionMap(row)
      this.isEdit = false
      this.dialogStatus = 'view'
      this.$refs['stgDlg'].show(row, false)
    },
    formatRowData(rowData) {
    },
    formatFormData(formData) {
      formData.strategy.splice(0)
      for (var key in this.functionMap) {
        const funcList = this.functionMap[key]
        funcList.forEach(func => {
          func.strategy.funcId = func.id
          if (func.type == 2) {
            func.strategy.regKey = func.strategy.settingValue
          }
          // 更改时区和更改系统时间，settingValue的值默认为0
          if (func.id == 42 || func.id == 43) {
            func.strategy.settingValue = 0
          }
          formData.strategy.push(func.strategy)
        })
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus == 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
