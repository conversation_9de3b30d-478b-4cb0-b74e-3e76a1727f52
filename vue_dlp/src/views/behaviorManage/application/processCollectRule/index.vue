<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <link-button size="mini" btn-style="margin: 0px 0px 3px 10px;" :menu-code="'A58'" :link-url="'/system/baseData/appGroup'" :btn-text="$t('pages.processCollect_text1')"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <stg-target-form-item
          ref="stgTargetItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('table.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <data-editor
          :formable="formable"
          :popover-width="700"
          :updateable="updateable"
          :deletable="deleteable2"
          :add-func="createData2"
          :update-func="updateData2"
          :delete-func="handleDelete2"
          :cancel-func="cancel"
          :before-update="beforeUpdateData"
          :before-add="beforeAddData"
        >
          <Form
            ref="ruleForm"
            :rules="tempRules"
            :model="temp2"
            label-position="right"
            label-width="100px"
            :extra-width="{en: 45}"
            :hide-required-asterisk="true"
            style="width: 650px;"
          >
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('pages.processCollect_action')">
                  <el-select v-model="temp2.setFilter" style="width: 100%;" @change="setFilterChange">
                    <el-option v-for="item in setFilterOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </FormItem>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <FormItem :label="$t('pages.processCollect_matchingRule')">
                  <el-select v-model="temp2.type" style="width: 100%;" @change="$refs.ruleForm.clearValidate()">
                    <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('pages.processCollect_relation')">
                  <el-select v-model="temp2.ruleCondition" style="width: 100%;">
                    <el-option v-for="item in conditionOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </FormItem>
              </el-col>
            </el-row>

            <FormItem v-if="temp2.type !== 1" :label="ruleParamLabel" prop="ruleParam">
              <el-input v-model="temp2.ruleParam" :maxlength="100" clearable/>
            </FormItem>

            <FormItem v-if="temp2.type === 1 && operationType === 'update'" :label="ruleParamLabel" prop="ruleParam2">
              <el-input v-model="temp2.ruleParam" :maxlength="100" clearable/>
            </FormItem>
            <FormItem v-if="temp2.type === 1 && operationType !== 'update'" :label="ruleParamLabel" prop="exeNames" >
              <tag v-model="temp2.exeNames" :border="true" :list="temp2.exeNames" :overflow-able="true" input-length="100" max-height="150px" :disabled="!formable" @tagChange="tagChange"/>
            </FormItem>
            <FormItem v-if="temp2.setFilter === 0" :label="$t('pages.collectToLibrary')" prop="classId">
              <tree-select
                ref="treeSelect"
                v-model="temp2.classId"
                is-filter
                :disabled="!!temp2.setFilter"
                :data="treeData"
                node-key="dataId"
                :checked-keys="selectIds"
                class="input-with-button"
                @change="parentIdChange"
              />
              <el-button class="editBtn" @click="handleTypeCreate">
                <svg-icon icon-class="add" />
              </el-button>
            </FormItem>
          </Form>
        </data-editor>
        <grid-table
          ref="ruleTable"
          :height="250"
          :show-pager="false"
          :selectable="() => { return formable }"
          :col-model="colModel2"
          :row-datas="rowData2"
          @selectionChangeEnd="handleSelectionChange2"
          @row-click="rowClick"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button
          v-if="formable"
          :loading="dataSubmitting"
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 分组弹窗-->
    <el-dialog
      v-el-drag-dialog
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.processCollect_addGroup')"
      :visible.sync="dialogGroupFormVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="appTypeForm" :rules="groupRules" :model="groupTemp" label-position="right" label-width="80px" style="width: 300px; margin-left:20px;">
        <FormItem :label="$t('pages.groupName1')" prop="name">
          <el-input v-model="groupTemp.name" maxlength="30"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="groupSubmitting" @click="createGroupData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="closeGroupName">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importProcessCollectRuleStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import {
  getDataPage, getDataByName, createData, updateData, deleteData,
  getRuleList, listByStgId, updateRule, createBatchRule
} from '@/api/behaviorManage/application/processCollectRule'
import { getFormTree } from '@/api/behaviorManage/application/appVersion'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import { getGroupByName, createAppGroup } from '@/api/behaviorManage/application/appGroup';

export default {
  name: 'ProcessCollectRule',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 94,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'controlType', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      colModel2: [
        { prop: 'type', label: 'matchRule', width: '80', sort: true, formatter: this.typeFormatter },
        { prop: 'ruleCondition', label: 'ruleCondition', width: '50', sort: true, formatter: this.conditionFormatter },
        { prop: 'ruleParam', label: 'ruleParam', width: '100', sort: true },
        { prop: 'setFilter', label: 'setFilter', width: '50', sort: true, formatter: this.setFilterFormatter },
        { prop: 'className', label: 'appGroup', width: '80', sort: true, formatter: this.classNameFormatter }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      deleteable2: false,
      updateable: false,
      addBtnAble: false,
      keyword: '',
      setFilterOptions: [
        { label: this.$t('pages.include'), value: 0 },
        { label: this.$t('pages.exclude'), value: 1 }
      ],
      typeOptions: [
        { label: this.$t('pages.typeOptions1'), value: 1 },
        { label: this.$t('pages.typeOptions2'), value: 2 },
        { label: this.$t('pages.typeOptions3'), value: 3 },
        { label: this.$t('pages.typeOptions4'), value: 4 }
      ],
      conditionOptions: [
        { label: this.$t('pages.conditionOptions1'), value: 1 },
        { label: this.$t('pages.conditionOptions2'), value: 2 },
        { label: this.$t('pages.conditionOptions3'), value: 3 },
        { label: this.$t('pages.conditionOptions4'), value: 4 }
      ],
      treeData: [],
      selectIds: [],
      temp: {}, // 表单字段
      defaultTemp: {
        id: null,
        name: '',
        active: false,
        remark: '',
        rules: [],
        ruleIds: [],
        exeNames: []
      },
      temp2: {},
      defaultTemp2: {
        id: null,
        setFilter: 1,
        type: 1,
        ruleCondition: 1,
        ruleParam: '',
        classId: 0,
        exeNames: []    //  批量导入程序名称
      },
      dialogFormVisible: false,
      dialogStatus: '',
      dialogStatus2: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.processCollectStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.processCollectStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        ruleParam: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ]
      },
      tempRules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        ruleParam: [
          { validator: this.ruleParamValidator, trigger: 'blur' }
        ],
        ruleParam2: [
          { validator: this.ruleParamValidator, trigger: 'blur' }
        ],
        exeNames: [
          { validator: this.exeNamesValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      dataSubmitting: false,
      rowData2: [],
      appLabelName: '',

      //  添加分组
      groupTemp: {
        name: ''
      },
      groupRules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.groupNameValidator, trigger: 'blur' }
        ]
      },
      dialogGroupFormVisible: false,
      groupSubmitting: false,

      //  批量新增相关属性
      operationType: ''   //  操作类型  create, update
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyTable']
    },
    typeMap() {
      const map = {}
      this.typeOptions.forEach(item => {
        map[item.value] = item.label
      })
      return map
    },
    conditionMap() {
      const map = {}
      this.conditionOptions.forEach(item => {
        map[item.value] = item.label
      })
      return map
    },
    setFilterMap() {
      const map = {}
      this.setFilterOptions.forEach(item => {
        map[item.value] = item.label
      })
      return map
    },
    ruleParamLabel() {
      const type = this.temp2.type == null ? 1 : this.temp2.type
      const t = this.typeOptions.filter(item => { return item.value === type })
      return t !== null && t.length > 0 ? t[0].label : '值'
    },
    formatterSplitChar() {
      return this.$store.getters.language === 'en' ? ' ' : ''
    }
  },
  activated() {
    // this.loadTypeTree()
    this.getRealGroup();
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.loadTypeTree()
  },
  methods: {
    getGroupByName,
    // 更新软件类别树的数据
    loadTypeTree: function() {
      return getFormTree().then(respond => {
        // 未分组label 多语言化
        this.treeData = respond.data.map(item => {
          if (item.dataId == 0) {
            item.label = this.$t('pages.ungrouped')
          }
          return item
        })
      })
    },
    parentIdChange(data, row) {
      this.temp2.classId = data.replace('G', '');
      if (Array.isArray(row) && row.length > 0) {
        this.temp2.className = row[0].label;
      } else if (row instanceof Object) {
        this.temp2.className = row.label;
      }
    },
    // currentChange(currentRow, oldCurrentRow) {
    //   if (currentRow === null || currentRow === undefined) {
    //     this.dialogStatus2 = 'create'
    //   } else {
    //     this.temp2 = JSON.parse(JSON.stringify(currentRow)) // copy obj
    //     this.dialogStatus2 = 'update'
    //     this.selectIds.splice(0, 1, this.temp2.classId)
    //   }
    //   this.$refs['ruleForm'] && this.$refs['ruleForm'].clearValidate()
    // },
    rowClick(rowData, column, event) {
      if (this.currentRow != rowData) {
        this.currentRow = rowData
      } else {
        this.currentRow = null
        this.cancel()
      }
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    afterLoad2: function(rowData, grid) {
      const idArr = JSON.parse(JSON.stringify(this.temp.ruleIds))
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          if (idArr.indexOf(item.id) != -1) {
            grid.toggleRowSelection(item)
          }
        })
      })
    },
    handleSelectionChange2(rowDatas) {
      this.deleteable2 = rowDatas.length > 0
      // this.temp.ruleIds = rowDatas.map(item => {
      //   return item.id
      // })
      // this.temp.rules = rowDatas
      if (rowDatas.length === 1) {
        this.updateable = true
      } else {
        this.updateable = false
        this.cancel()
      }
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getDataPage(searchQuery)
    },
    rowDataApi2: function(option) {
      return getRuleList(option)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    resetTemp2() {
      this.temp2 = JSON.parse(JSON.stringify(this.defaultTemp2))
      this.selectIds.splice(0, 1, this.temp2.classId)
    },
    handleCreate() {
      this.resetTemp()
      this.resetTemp2()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.reloadTable()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.rowData2 = [];
    },
    handleUpdate: function(row) {
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.rowData2 = [];
      this.resetTemp2()
      this.reloadTable()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.cancel()
      })
      // 获取rowData2数据
      this.getRowData2();
    },
    getRowData2() {
      listByStgId({ stgId: this.temp.id }).then((res) => {
        if (res) {
          this.rowData2 = res.data;
        }
      });
    },
    reloadTable() {
      this.$nextTick(() => {
        if (this.$refs.ruleTable) {
          this.$refs.ruleTable.clearSelection()
          this.$refs.ruleTable.execRowDataApi()
          this.$refs['ruleForm'] && this.$refs['ruleForm'].clearValidate()
        }
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    valid() {
      if (!this.$refs['stgTargetItem'].valid()) {
        return false
      }
      if (this.rowData2.length === 0) {
        this.$message({
          message: this.$t('pages.addLeastOneRule'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    cancel() {
      this.resetTemp2()
      this.dialogStatus2 = 'create'
      this.$nextTick(() => {
        this.$refs.ruleTable.setCurrentRow()
        this.$refs['treeSelect'] && this.$refs['treeSelect'].clearFilter()
        this.$refs['ruleForm'] && this.$refs['ruleForm'].clearValidate()
      })
    },
    createData() {
      if (!this.valid()) {
        return
      }
      // 重新计算rules和ruleIds
      this.formatTemp();
      this.dataSubmitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createData(this.temp).then(() => {
            this.dataSubmitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dataSubmitting = false
          })
        } else {
          this.dataSubmitting = false
        }
      })
    },
    createData2() {
      let validate
      this.submitting = true
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          //  批量添加
          createBatchRule(this.temp2).then((res) => {
            const datas = res.data;
            datas.forEach(item => {
              this.temp.ruleIds.push(item.id);
              this.temp.rules.push(item);
              this.rowData2.push(item);
            })
            this.$refs.ruleTable.setCurrentRow(this.rowData2);
            this.cancel()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })

          validate = valid
        } else {
          this.submitting = false
        }
      })
      return validate
    },
    updateData() {
      if (!this.valid()) {
        return
      }
      // 重新计算rules和ruleIds
      this.formatTemp();
      this.dataSubmitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          updateData(this.temp).then(() => {
            this.dataSubmitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dataSubmitting = false
          })
        } else {
          this.dataSubmitting = false
        }
      })
    },
    beforeAddData() {
      this.$set(this.temp2, 'exeNames', [])
      this.operationType = 'create'
    },
    beforeUpdateData() {
      this.$set(this.temp2, 'exeNames', [])
      // 记录下修改之前className的值
      this.appLabelName = this.$refs.ruleTable.selectedData[0].className;
      // 这里temp2解构赋值时会丢失className
      this.temp2 = JSON.parse(JSON.stringify(this.$refs.ruleTable.selectedData[0]))
      this.selectIds.splice(0, 1, this.temp2.classId)

      this.operationType = 'update'
    },
    updateData2() {
      let validate
      this.submitting = true
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          updateRule(this.temp2).then(() => {
            this.submitting = false
            this.rowData2.map((p, index) => {
              if (p.id === this.temp2.id) {
                this.rowData2[index] = this.temp2;
                // 若className为空，则填入beforeUpdateData()之前的className
                // 原因是在解构赋值时丢失className，只有classId，若此时点击修改，不改动应用数据库的话，保存后会丢失，造成列表缺失。
                if (!!this.temp2.classId && !this.temp2.className) {
                  this.rowData2[index].className = this.appLabelName;
                }
              }
            })
            this.reloadTable();
            this.gridTable.execRowDataApi()
            this.cancel();
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
          validate = valid
        } else {
          this.submitting = false
        }
      })
      return validate
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleDelete2() {
      const toDeleteIds = this.$refs.ruleTable.getSelectedIds();
      // 采用列表删除，不从数据删，保存时去保存留下的列表即可，以免影响其他配置的策略
      this.$refs.ruleTable.deleteRowData(toDeleteIds, this.rowData2)
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const c = this.formatterSplitChar
      const showList = row.rules.map(item => {
        const { setFilter, type, ruleCondition, ruleParam } = item
        return this.setFilterMap[setFilter] + c + this.typeMap[type] + c + this.conditionMap[ruleCondition] + c + ruleParam
      })
      return showList.join('、')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    typeFormatter: function(row, data) {
      let msg = ''
      this.typeOptions.forEach(item => {
        if (item.value == data) {
          msg = item.label
        }
      })
      return msg
    },
    conditionFormatter: function(row, data) {
      let msg = ''
      this.conditionOptions.forEach(item => {
        if (item.value == data) {
          msg = item.label
        }
      })
      return msg
    },
    setFilterFormatter: function(row, data) {
      let msg = ''
      this.setFilterOptions.forEach(item => {
        if (item.value == data) {
          msg = item.label
        }
      })
      return msg
    },
    classNameFormatter(row, data) {
      return this.getTypeTreeLabel(this.treeData, row.classId) || this.$t('pages.ungrouped')
    },
    formatTemp() {
      const rules = [];
      const ruleIds = [];
      const rowData2 = JSON.parse(JSON.stringify(this.rowData2))
      rowData2.map(p => {
        ruleIds.push(p.id);
        // 清除className
        p.className = undefined;
        rules.push(p);
      });
      this.temp.rules = rules;
      this.temp.ruleIds = ruleIds;
    },
    importSuccess() {
      this.handleFilter()
    },

    ruleParamValidator(rule, value, callback) {
      if (this.temp2.ruleParam.length === 0) {
        callback(new Error(this.$t('pages.processCollect_processNameNotNull', { type: this.ruleParamLabel })))
      } else {
        callback()
      }
    },
    exeNamesValidator(rule, value, callback) {
      if (this.temp2.exeNames.length === 0) {
        callback(new Error(this.$t('pages.processCollect_processNameNotNull', { type: this.ruleParamLabel })))
      } else {
        callback()
      }
    },
    //  当列表发生改变时，校验名称是否符合规则
    tagChange(list) {
      list = this.filterRepetitionData(list);
      this.temp2.exeNames = list;
    },
    //  过滤重复数据
    filterRepetitionData(list) {
      const tList = []
      list.forEach(item => {
        if (!tList.includes(item) && item.length <= 100) {
          tList.push(item)
        }
      })
      return tList;
    },
    //  新增分组
    handleTypeCreate() {
      this.groupTemp = { name: '' }
      this.dialogGroupFormVisible = true
    },
    closeGroupName() {
      this.dialogGroupFormVisible = false
      this.groupTemp = { name: '' }
    },
    groupNameValidator(rule, value, callback) {
      getGroupByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    createGroupData() {
      this.$refs['appTypeForm'].validate((valid) => {
        if (valid) {
          this.groupSubmitting = true
          this.groupTemp.parentId = 0
          createAppGroup(this.groupTemp).then(res => {
            this.loadTypeTree().then(r => {
              if (this.temp2.setFilter === 0) {
                this.temp2.classId = res.data.id
                // this.temp2.className = res.data.name;
                this.selectIds = []
                this.selectIds.push(res.data.id + '')
              }
            })
            this.closeGroupName()
            this.groupSubmitting = false
          }).catch(() => { this.groupSubmitting = false })
        }
      });
    },
    getRealGroup() {
      getFormTree().then(respond => {
        // 未分组label 多语言化
        this.treeData = respond.data.map(item => {
          if (item.dataId == 0) {
            item.label = this.$t('pages.ungrouped')
          }
          return item
        })
        const isExits = this.getTypeTreeLabel(respond.data, this.temp2.classId)
        if (!isExits) {
          this.temp2.classId = 0
          this.temp2.className = undefined
          this.selectIds = []
          this.selectIds.push(this.temp2.classId + '')
        } else {
          this.$nextTick(() => {
            this.$refs.treeSelect && this.$refs.treeSelect.checkSelectedNode(this.temp2.classId + '')
          })
        }
        this.rowData2.forEach(item => {
          const label = this.getTypeTreeLabel(this.treeData, item.classId)
          if (!label) {
            item.classId = 0
            item.className = ''
          } else {
            item.className = label
          }
        })
      })
    },
    getTypeTreeLabel(datas, id) {
      if (datas) {
        const ret = []
        for (let i = 0; i < datas.length; i++) {
          if (datas[i].dataId == id) {
            ret.push(datas[i].label || '')
          }
          if (datas[i].children) {
            ret.push(this.getTypeTreeLabel(datas[i].children, id))
          }
        }
        return ret.filter(v => v)[0] || ''
      }
      return null
    },
    setFilterChange(value) {
      if (value === 1) {
        this.temp2.classId = 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .imTab >>>.el-tabs__item.is-top:nth-child(2) {
    padding-left: 20px;
  }
</style>
