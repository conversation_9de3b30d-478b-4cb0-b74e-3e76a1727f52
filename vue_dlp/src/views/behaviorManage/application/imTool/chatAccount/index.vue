<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div slot="title" class="el-dialog__title">
        <span>{{ $t('pages.ImTool_text8') }}</span>
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            {{ $t('pages.ImTool_text10') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <div class="tree-container" style="height: 360px;">
        <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" :terminal-filter-key="terminalFilter" @data-change="targetNodeChange" />
      </div>
      <div class="table-container-dialog">
        <grid-table
          ref="tableList"
          v-loading="tableLoading"
          row-key="userName"
          :height="360"
          :show-pager="false"
          :col-model="colModel"
          :multi-select="true"
          :row-datas="accountList"
        />
      </div>
      <div>
        <Form ref="dataForm" label-width="120px">
          <FormItem :label="$t('pages.repeatNumberDealType')" prop="importType">
            <el-radio-group v-model="temp.importType" >
              <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
              <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
              <!--              <el-radio :label="2">{{ $t('pages.importAndRename') }}</el-radio>-->
            </el-radio-group>
          </FormItem>
          <FormItem :label="$t('pages.importGroupDisposal')" prop="objectType">
            <el-radio-group v-model="temp.groupType">
              <el-radio :label="1" style="display: block">
                <span>{{ $t('pages.scanWechatTip1') }}</span>
              </el-radio>
              <div style="display: flex">
                <el-radio :label="0" style="margin-right: 10px;">
                  <i18n path="pages.importGroupDisposalType2">
                    <template slot="type">{{ $t('pages.wechatInfo') }}</template>
                  </i18n>
                </el-radio>
                <tree-select
                  v-model="temp.targetGroupId"
                  :checked-keys="[temp.targetGroupId]"
                  :data="groupTree"
                  :disabled="temp.groupType == 1"
                  node-key="dataId"
                  :width="296"
                  style="width: 200px; margin-right: 5px;"
                  class="input-with-button"
                  @change="groupSelectChange"
                />
                <el-button :disabled="temp.groupType == 1" class="editBtn" style="margin: 0 !important;" @click="handleCreateGroup">
                  <svg-icon icon-class="add" />
                </el-button>
              </div>
            </el-radio-group>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="createData()">{{ $t('button.import') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import { createWhiteBatch } from '@/api/behaviorManage/application/imTool';

export default {
  name: 'ChatAccount',
  props: {
    chatType: { // 聊天工具类型 3qq 23微信
      type: Number,
      default: 3
    },
    func: {
      type: Function,
      default: function() {
      }
    },
    groupTree: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      tableLoading: false,
      colModel: [
        { prop: 'computer', label: 'source', width: '1', sort: true },
        { prop: 'userName', label: 'account', width: '1', sort: true },
        { prop: 'nickName', label: 'nickName', width: '1', sort: true },
        { prop: 'loginStatus', label: 'loginStatus', width: '1', sort: true, formatter: (row) => { return row.loginStatus == 1 ? this.$t('pages.online') : this.$t('pages.offline') } }
      ],
      dialogFormVisible: false,
      submitting: false,
      termId: null,
      termName: '',
      accountList: [],
      temp: {},
      defaultTemp: {
        importType: 1,
        groupType: 1,
        targetGroupId: -1
      }
    }
  },
  computed: {
  },
  methods: {
    show() {
      this.dialogFormVisible = true
      this.temp = Object.assign({}, this.defaultTemp)
      this.$refs.strategyTargetTree && this.$refs.strategyTargetTree.clearFilter()
      this.$refs['tableList'] && this.$refs['tableList'].clearSelection()
    },
    close() {
      this.dialogFormVisible = false
    },
    startLoading() {
      this.submitting = true
    },
    stopLoading() {
      this.submitting = false
    },
    targetNodeChange: function(tabName, data) {
      this.accountList.splice(0)
      if (data && data.type == 1 && data.online) {
        this.tableLoading = true
        this.termId = data.dataId
        this.termName = data.label
        this.$socket.sendToUser(this.termId, '/listChatAccount', { terminalId: Number(this.termId), chatType: 1 }, (respond, handle) => {
          handle.close()
          this.tableLoading = false
          // 判断终端id是否等于当前请求的终端id，防止用户连续点击不同终端导致数据加载出错
          if (respond.data && respond.data.terminalId == this.termId && respond.data.accountList) {
            this.accountList.push(...respond.data.accountList)
            this.accountList.forEach(item => {
              item.computer = this.termName
            })
          }
        }, (handle) => {
          handle.close()
          this.tableLoading = false
          this.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
        })
      }
    },
    createData() {
      const datas = this.$refs.tableList.getSelectedDatas()
      const temp = this.temp
      this.submitting = true
      if (datas.length == 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.ImTool_text3'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return;
      }
      if (temp.groupType == 0 && (typeof temp.targetGroupId == 'undefined' || temp.targetGroupId == '' || temp.targetGroupId == -1)) {
        this.$message({
          message: this.$t('pages.validaGroup'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      const list = datas.map(data => {
        const obj = { chatNumber: data.userName, remark: data.nickName, type: this.chatType, groupName: data.computer }
        return obj
      })
      const reqParam = Object.assign({}, { list, type: this.chatType }, this.temp)
      if (reqParam['groupType'] == 0) {
        reqParam.groupName = (this.groupTree.find(item => item['dataId'] == reqParam['targetGroupId']) || {})['label']
      }
      createWhiteBatch(reqParam)
        .then(res => {
          if (res.data) {
            this.func(res.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            this.submitting = false
            this.dialogFormVisible = false
          }
        }).catch(reason => {
          this.submitting = false
        })
    },
    groupSelectChange(data) {
      this.temp.targetGroupId = data
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !(type === 128 || type === 32 || type === 3 || type === 2)
    },
    handleCreateGroup() {
      this.$emit('handleTypeCreate')
    }
  }
}
</script>
