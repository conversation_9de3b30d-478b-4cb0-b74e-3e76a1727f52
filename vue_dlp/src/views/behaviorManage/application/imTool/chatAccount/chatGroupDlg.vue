<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="titleMap[dlgStatus]"
    :visible.sync="dlgVisible"
    width="400px"
  >
    <Form ref="dataFrom" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 300px; margin-left:20px;">
      <FormItem :label="$t('pages.groupNames')" prop="name">
        <el-input v-model="temp.name" v-trim maxlength="30"/>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="dlgStatus==='create'?createNode():updateNode()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createChatGroup, updateChatGroup, getTypeByName } from '@/api/behaviorManage/application/imTool';
export default {
  name: 'ChatGroupDlg',
  props: {
    groupType: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      dlgVisible: false,
      titleMap: {
        create: this.i18nConcatText(this.$t('pages.ImToolGroup'), 'create'),
        update: this.i18nConcatText(this.$t('pages.ImToolGroup'), 'update')
      },
      dlgStatus: 'create',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        parentId: 0,
        name: ''
      },
      submitting: false,
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.groupNameValidator, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    show(rowData) {
      this.dlgVisible = true
      this.submitting = false
      if (rowData && rowData.id) {
        this.dlgStatus = 'update'
        this.temp = JSON.parse(JSON.stringify(rowData))
      } else {
        this.dlgStatus = 'create'
        this.temp = Object.assign({}, this.defaultTemp)
      }
      this.temp.groupType = this.groupType
      this.$nextTick(() => {
        this.$refs['dataFrom'].clearValidate()
      })
    },
    resetData() {

    },
    createNode() {
      this.submitting = true
      this.$refs['dataFrom'].validate((valid) => {
        if (valid) {
          this.submitting = true
          createChatGroup(this.temp).then(resp => {
            this.submitting = false
            this.dlgVisible = false
            this.$emit('submitEnd', resp.data, this.dlgStatus)
          }).catch(res => { this.submitting = false })
        } else { this.submitting = false }
      })
    },
    updateNode() {
      this.submitting = true
      this.$refs['dataFrom'].validate((valid) => {
        if (valid) {
          updateChatGroup(this.temp).then(resp => {
            this.submitting = false
            this.dlgVisible = false
            this.$emit('submitEnd', resp.data, this.dlgStatus)
          }).catch(res => { this.submitting = false })
        } else { this.submitting = false }
      })
    },
    groupNameValidator(rule, value, callback) {
      getTypeByName({ name: value }).then(respond => {
        const data = respond.data
        if (data && data.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    }
  }
}
</script>
