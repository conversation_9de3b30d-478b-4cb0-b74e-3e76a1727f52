<template>
  <div>
    <FormItem :label="$t('text.effectTime')" label-width="100px" :extra-width="{en: 40}" prop="timeId">
      <el-select v-model="temp.timeId" :disabled="!formable" :placeholder="$t('text.select')" style="width: 200px;">
        <el-option
          v-for="item in timeInfoOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <link-button :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
    </FormItem>
    <FormItem :label="$t('table.stgLimit')" label-width="100px" :extra-width="{en: 40}">
      <el-radio v-for="item in controlTypeOptions" :key="item.value" v-model="temp.controlType" :disabled="!formable" :label="item.value" @change="radioClick">
        <span :title="item.label">{{ item.label }}</span>
      </el-radio>
    </FormItem>
    <div v-if="temp.controlType == 2">
      <LocalZoomIn parent-tag="el-dialog">
        <div v-if="formable" class="toolbar" style="padding-left: 5px;">
          <data-editor
            ref="dataEditor"
            :formable="formable"
            :popover-width="675"
            :updateable="updateable2"
            :deletable="deleteable2"
            :add-func="createData2"
            :update-func="updateData2"
            :delete-func="handleDelete2"
            :import-func="handleImport"
            :cancel-func="resetData"
            :before-update="beforeUpdate2"
            style="display: inline-block;"
          >
            <Form
              ref="whiteForm"
              :rules="rules"
              :model="temp2"
              label-width="90px"
              label-position="right"
              :hide-required-asterisk="true"
              style="width: 600px; margin: 0 auto;"
            >
              <el-row>
                <el-col :span="12">
                  <FormItem :label="chatTypeLabel" class="required" prop="chatNumber">
                    <el-input v-model="temp2.chatNumber" v-trim maxlength="20" @input="temp2.chatNumber=temp2.chatNumber.replace(/[\u4E00-\u9FA5\s]/g,'')"/>
                  </FormItem>
                </el-col>
                <el-col :span="12">
                  <FormItem :label="$t('table.remark')">
                    <el-input v-model="temp2.remark" maxlength="300" />
                  </FormItem>
                </el-col>
              </el-row>
              <FormItem :label="$t('pages.ImToolGroup')">
                <tree-select
                  ref="selectGroupTree"
                  :width="280"
                  node-key="dataId"
                  :data="groupTreeData"
                  :checked-keys="checkedKeys"
                  class="input-with-button"
                  @change="treeSelectChange"
                />
                <el-button class="editBtn" @click="handleTypeCreate">
                  <svg-icon icon-class="add" />
                </el-button>
              </FormItem>
            </Form>
            <!-- <el-col :span="10" >
                  <div class="searchCon" style="padding-right: 20px">
                    <el-button :loading="submitting" @click="dialogStatus2==='update'?updateData2():createData2()">
                      {{ dialogStatus2==='update'?$t('button.edit'):$t('button.add') }}
                    </el-button>
                    <el-button @click="cancel">{{ $t('button.cancel') }}</el-button>
                    <el-tooltip class="item" effect="dark" :content="$t('pages.ImTool_text1')" placement="top">
                      <el-button :disabled="dialogStatus2!=='update'" @click="handleDelete2">{{ $t('button.delete') }}</el-button>
                    </el-tooltip>
                    <el-button @click="handleClear">{{ $t('button.clear') }}</el-button>
                    <el-button v-if="chatType==23" @click="handleScan">{{ $t('pages.scan') }}</el-button>
                  </div>
                </el-col> -->
          </data-editor>
          <el-button v-if="chatType==23" style="margin-left: 2px" size="small" @click="handleScan">{{ $t('pages.scan') }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">
                <i18n path="pages.ImTool_text9">
                  <br slot="br"/>
                </i18n>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-button>
        </div>
        <grid-table
          ref="softTable"
          auto-height
          :show-pager="false"
          :col-model="colModel2"
          :row-datas="rowDatas"
          :selectable="selectable"
          style="padding: 0 5px; margin-bottom: 5px;"
          @selectionChangeEnd="handleSelectionChange2"
        />
      </LocalZoomIn>
    </div>
    <chat-account ref="chatAccount" :chat-type="chatType" :group-tree="groupTreeData" :func="saveAccount" @handleTypeCreate="handleTypeCreate"/>
  </div>
</template>
<script>
import {
  clearWhiteList,
  createWhite,
  getChatsByType,
  getWhiteListInfo,
  updateWhite
} from '@/api/behaviorManage/application/imTool'
import { timeIdValidator } from '@/utils/validate'
import ChatAccount from '@/views/behaviorManage/application/imTool/chatAccount'

export default {
  name: 'ControlParam',
  components: { ChatAccount },
  props: {
    formable: { // 能否提交表单
      type: Boolean,
      default: true
    },
    chatType: { // 聊天工具类型 3qq 23微信
      type: Number,
      default: 3
    },
    typeTreeData: {
      type: Array,
      default() {
        return []
      }
    },
    updateTree: {
      type: Function,
      default: null
    },
    temp: {
      type: Object,
      default: function() {
        return {
          timeId: 1,
          controlType: 0,
          chatType: 3,
          whiteListId: ''
        }
      }
    },
    groupIdFormatter: {
      type: Function,
      default: null
    },
    chatTypeLabel: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      colModel2: [
        { prop: 'chatNumber', label: this.chatTypeLabel, width: '20', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '20', sort: true, sortOriginal: true, formatter: this.localGroupIdFormatter },
        { prop: 'remark', label: 'remark', width: '20', sort: true }
      ],
      controlTypeOptions: [
        { value: 1, label: this.$t('pages.controlTypeOptions1') },
        { value: 2, label: this.$t('pages.controlTypeOptions2') },
        { value: 3, label: this.$t('pages.controlTypeOptions3') }
      ],
      deleteable2: false,
      updateable2: false,
      keyword2: '',
      temp2: {}, // 白名单表单字段
      groupTreeData: [],
      checkedKeys: ['0'],
      rowDatas: [],
      dialogStatus2: '',
      radio: undefined,
      rules: {
        timeId: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: timeIdValidator, trigger: 'change' }
        ],
        chatNumber: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.checkRepeat, trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    },
    gridTable() {
      return this.$refs['strategyTable']
    },
    tree: function() {
      return this.$refs['typeTree'].$refs['tree']
    }
  },
  created() {
    this.groupTreeData = this.typeTreeData
  },
  methods: {
    selectable(row, index) {
      if (this.formable == false) {
        return false
      } else {
        return true
      }
    },
    softTable: function() {
      return this.$refs['softTable']
    },
    reloadTable() {
      this.rowDatas = []
      this.resetTemp2()
      // this.softTable() && this.softTable().execRowDataApi()
    },
    selectFirstNode: function() {
      this.tree.setCurrentKey('0')
    },
    handleFilter2() {
      this.softTable().execRowDataApi()
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp2() {
      this.temp2 = Object.assign({}, {
        id: undefined,
        chatNumber: '',
        remark: '',
        type: this.chatType,
        groupId: 0
      })
      this.checkedKeys = ['0']
    },
    resetData() {
      this.resetTemp2()
      this.dialogStatus2 = 'create'
      this.$nextTick(() => {
        this.softTable().setCurrentRow()
        this.$refs['whiteForm'].clearValidate()
      })
    },
    beforeUpdate2: function() {
      this.dialogStatus2 = 'update'
      this.temp2 = JSON.parse(JSON.stringify(this.softTable().getSelectedDatas()[0]))
      const groupId = this.temp2.groupId
      this.checkedKeys = [groupId]
    },
    handleUpdate2: function(row) {
      this.temp2 = Object.assign({}, row) // copy obj
      this.dialogStatus2 = 'update'
      this.$nextTick(() => {
        this.$refs['whiteForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.accountImportDlg.show()
    },
    handleSelectionChange2(rowDatas) {
      this.deleteable2 = rowDatas.length > 0
      this.temp.whiteListId = rowDatas.map(item => {
        return item.id
      }).join(',')
      if (rowDatas.length === 1) {
        this.updateable2 = true
      } else {
        this.updateable2 = false
        this.resetData()
      }
    },
    createData2() {
      return new Promise(resolve => {
        this.$refs['whiteForm'].validate((valid) => {
          if (valid) {
            this.temp2.type = this.chatType
            getWhiteListInfo(this.temp2).then(respond => {
              const bean = respond.data
              if (bean && bean.id !== this.temp2.id) {
                if (bean.groupId == this.temp2.groupId) {
                  if (this.rowDatas.findIndex(datas => datas.id == bean.id) < 0) {
                    this.rowDatas.push(bean)
                  }
                  this.resetData()
                }
                resolve(valid)
              } else {
                createWhite(this.temp2).then((res) => {
                  resolve(valid)
                  this.submitting = false
                  if (res.data) this.rowDatas.push(res.data)
                  this.resetData()
                  this.$notify({
                    title: this.$t('text.success'),
                    message: this.$t('text.createSuccess'),
                    type: 'success',
                    duration: 2000
                  })
                })
              }
            })
          }
          !valid && resolve(valid)
        })
      })
    },
    updateData2() {
      return new Promise(resolve => {
        this.$refs['whiteForm'].validate((valid) => {
          if (valid) {
            const tempData = Object.assign({}, this.temp2)
            updateWhite(tempData).then((res) => {
              resolve(valid)
              this.submitting = false
              this.resetData()
              const data = res.data
              this.rowDatas.splice(this.rowDatas.findIndex(item => item.id == data.id), 1, data)
              // this.softTable().execRowDataApi()
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
            })
          }
          !valid && resolve(valid)
        })
      })
    },
    saveAccount(datas) {  // 保存终端扫描的聊天账号
      if (datas && datas.length > 0) {
        this.updateTree()
        datas.forEach(data => {
          const index = this.rowDatas.findIndex(row => row.id == data.id)
          if (index < 0) {
            this.rowDatas.push(data)
          } else {
            this.rowDatas.splice(index, 1, data)
          }
        })
      }
    },
    checkRepeat(rule, value, callback) {
      getWhiteListInfo(Object.assign({}, this.temp2, { type: this.chatType })).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp2.id) {
          if (this.dialogStatus2 == 'update' || bean.groupId != Number(this.temp2.groupId)) {
            callback(new Error(this.$t('pages.validateMsg_sameAccount')))
          } else if (bean.groupId === Number(this.temp2.groupId) && this.rowDatas.findIndex(datas => datas.id == bean.id) >= 0) {
            callback(new Error(this.$t('pages.validateMsg_sameAccount')))
          }
        }
        callback()
      })
    },
    handleDelete2() {
      this.deleteTable(this.softTable().getSelectedIds() || [])
    },
    deleteTable(toDeleteIds) {
      const right = this.rowDatas.length - 1;
      for (let i = right; i >= 0; i--) {
        if (toDeleteIds.findIndex(item => item == this.rowDatas[i].id) >= 0) {
          this.rowDatas.splice(i, 1)
        }
      }
    },
    handleClear() {
      const map = {
        3: 'QQ',
        23: this.$t('pages.wechat')
      }
      this.$confirmBox(this.$t('pages.ImTool_text6', { chat: map[this.chatType] }), this.$t('text.prompt')).then(() => {
        clearWhiteList({ type: this.chatType }).then(() => {
          this.softTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.clearSuccess1'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    createData3() {

    },
    pushImportData(data) {
      if (data) {
        data.forEach(item => {
          const index = this.rowDatas.findIndex(val => val.id == item.id)
          if (index < 0) {
            this.rowDatas.push(item)
          } else {
            this.rowDatas.splice(index, 1, item)
          }
        })
      }
    },
    handleScan() {
      this.$refs.chatAccount.show()
    },
    treeSelectChange(data, row) {
      this.temp2.groupId = data
      this.checkedKeys.splice(0, this.checkedKeys.length, data)
    },
    radioClick(val) {
      this.radio = val
      const ids = this.temp.whiteListId
      if (this.radio == 2 && ids && ids.length >= 0) {
        if (!this.rowDatas || this.rowDatas.length == 0) {
          getChatsByType({ ids: this.temp.whiteListId }).then(res => {
            this.rowDatas = res.data
          })
        }
      }
    },
    refreshTable() {
      if (this.radio == 2 && (this.rowDatas && this.rowDatas.length > 0)) {
        getChatsByType({ ids: this.rowDatas.map(data => data.id).join(',') }).then(res => {
          this.rowDatas = res.data
        })
      }
    },
    localGroupIdFormatter(row, data) {
      return this.groupIdFormatter(this.groupTreeData, String(data))
    },
    changeGroupTree(data) {
      this.groupTreeData = data
    },
    handleTypeCreate() {
      this.$emit('createGroup')
    }
  }
}
</script>
