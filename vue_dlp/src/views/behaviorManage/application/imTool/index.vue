<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-permission="'316'" icon="el-icon-upload2" size="mini" @click="importFromExcel">
          {{ $t('button.import') }}
        </el-button>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :time-mode="2"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeTab" class="imTab" @tab-click="tabClick">
          <el-tab-pane v-for="(option, index) in chatTypeOption" :key="index" :lazy="false" :label="option.label" :name="option.relRef">
            <control-param
              :ref="option.relRef"
              :formable="formable"
              :chat-type="option.value"
              :chat-type-label="option.formLabel"
              :type-tree-data="typeTreeData[option.value]"
              :temp="temp.list[option.listIndex]"
              :update-tree="scanUpdateGroupTree"
              :group-id-formatter="getGroupNameByDataId"
              @createGroup="createGroup"
            />
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-mode="2"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button
          v-if="formable"
          :loading="submitting"
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importImToolStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <import-table-dlg
      ref="importTableDlg"
      :elg-title="$t('text.importInfo', { info: chatTypeObj['formLabel']})"
      :group-root-name="$t('text.someLibrary', { info: chatTypeObj['formLabel'] })"
      :search-info-name="chatTypeObj['formLabel']"
      :confirm-button-name="$t('pages.ImTool_import3')"
      :group-title="$t('pages.ImToolGroup')"
      :col-model="importColModel"
      tree-root-data-id="-1"
      not-group-data-id="0"
      :list="loadAccountList"
      :invoke-group-param="{groupType: chatType}"
      :load-group-tree="loadImportTree"
      :create-group="createChatGroup"
      :update-group="updateChatGroup"
      :delete-group="deleteChatGroup"
      :count-by-group="countByGroupId"
      :get-group-by-name="getTypeByName"
      :handle-create-elg="importHandleCreate"
      :delete="deleteWhite"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteChatGroup"
      :move-group-to-other="moveGroupToOther"
      :get-list-by-group-ids="getChatsByType"
      @submitEnd="importData"
      @submitDeleteEnd="submitDeleteEnd"
      @changeGroupAfter="changeGroupAfter"
    />
    <!--    通讯账号库导入号码的新增号码操作   -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="numberDialogTitle"
      :visible.sync="numberDialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="whiteForm"
        :rules="numberRules"
        :model="numberTemp"
        label-position="right"
        label-width="100px"
        style="width: 500px; margin-left:20px;"
      >
        <FormItem :label="chatTypeObj['formLabel']" prop="chatNumber">
          <el-input v-model="numberTemp.chatNumber" v-trim maxlength="20" @input="numberTemp.chatNumber=numberTemp.chatNumber.replace(/[\u4E00-\u9FA5\s]/g,'')"/>
        </FormItem>
        <FormItem :label="$t('pages.ImToolGroup')">
          <tree-select
            ref="selectGroupTree"
            :width="280"
            node-key="dataId"
            :data="typeTreeData[chatType]"
            :checked-keys="numberCheckedKeys"
            @change="treeCheckChange"
          />
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="numberTemp.remark" type="textarea" rows="3" resize="none" maxlength="100" show-word-limit/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="dlgSubmitting" @click="numberDialogStatus==='importCreate'? createNumberData(): numberDialogStatus==='importUpdate'? updateNumberData() : {}">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="numberDialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :append-to-body="true"
      :title="$t('pages.group')"
      :group-tree-data="typeTreeData[chatType]"
      :edit-valid-func="getTypeByName"
      :extra-param="{groupType: chatType}"
      :add-func="createChatGroup"
      @addEnd="createGroupAfter"
    />
    <import-term-im-tool ref="importExcelDlg" :chat-type-opts="chatTypeOption" :chat-group-tree-datas="typeTreeData" @createGroup="createGroup" @importSubmitEnd="importSubmitEnd"/>
  </div>
</template>
<script>
import {
  getDataPage, getDataByName, createData, updateData,
  deleteData, getTypeTree, getWhitListPage, countByGroupId,
  deleteWhite, deleteGroupAndData, moveGroupToOther,
  getWhiteListInfo, createWhite, updateWhite, getChatsByType, deleteChatGroup
} from '@/api/behaviorManage/application/imTool'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { createChatGroup, updateChatGroup, getTypeByName } from '@/api/behaviorManage/application/imTool';
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timePeriodFormatter } from '@/utils/formatter'
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg'
import ControlParam from '@/views/behaviorManage/application/imTool/controlParam'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import EditGroupDlg from '@/views/common/editGroupDlg';
import request from '@/utils/request'
import ImportTermImTool from '@/views/behaviorManage/application/imTool/importTermImTool'

export default {
  name: 'ImTool',
  components: { ImportTermImTool, EditGroupDlg, ImportTableDlg, ControlParam, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  provide() {
    return {
      showImportAccDlg: this.importAccountFunc,
      importDlgBtnName: this.$t('pages.ImTool_import')
    }
  },
  data() {
    return {
      stgCode: 4,
      activeTab: 'qqRef',
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectiveDate', width: '100', formatter: timePeriodFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'controlType', label: 'stgLimit', width: '200', formatter: this.controlTypeFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      importColModel: [
        { prop: 'chatNumber', label: 'chatNumber', width: '20', sort: 'custom' },
        { prop: 'groupId', label: 'sourceGroup', width: '20', sort: 'custom', formatter: this.groupIdFormatter },
        { prop: 'remark', label: 'remark', width: '20', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.importHandleUpdate }
          ]
        }
      ],
      importFromExcelTitle: '终端通讯工具账号信息',
      numberDialogStatus: 'importCreate',
      numberTemp: {},
      numberDialogFormVisible: false,
      controlTypeOptions: {
        1: this.$t('pages.controlTypeOptions1'),
        2: this.$t('pages.controlTypeOptions2'),
        3: this.$t('pages.controlTypeOptions3')
      },
      // 新增新的通讯工具时，在此对象数据中再新增对象即可；
      // value: chatType的值，唯一！！！
      // label: chatType的聊天工具类型名
      // relRef: controlParam组件的ref名以及el-tab-pane名，唯一！！！
      // listIndex: 对象在数组的下标，递增
      // formLabel: 对应策略、通讯账号库中GridTable的账号类型名 如 QQ号码、微信原始号、TIM号码等;
      //            同时也作为通讯账号库导入窗口的窗口标题名 `导入${formLabel}`
      //            同时也作为通讯账号库导入窗口中，左侧分组的根节点名 `${formLabel}库`
      chatTypeOption: [
        { value: 3, label: 'QQ', relRef: 'qqRef', listIndex: 0, formLabel: this.$t('table.chatNumber') },
        { value: 23, label: this.$t('pages.wechat'), relRef: 'wechatRef', listIndex: 1, formLabel: this.$t('table.weChatNumber') },
        { value: 31, label: this.$t('pages.tim'), relRef: 'timRef', listIndex: 2, formLabel: this.$t('table.timNumber') },
        { value: 29, label: this.$t('pages.feiShu'), relRef: 'feiShuRef', listIndex: 3, formLabel: this.$t('table.feiShuNumber') },
        { value: 25, label: this.$t('pages.dingTalk'), relRef: 'dingTalkRef', listIndex: 4, formLabel: this.$t('table.dingTalkNumber') }
      ],
      chatType: 3,
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      keyword2: '',
      temp3: [],
      numberCheckedKeys: ['0'],
      temp: {},
      dlgSubmitting: false,
      dialogFormVisible: false,
      dialogStatus: '',
      // 待处理引用
      pendingRef: null,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.ImToolStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.ImToolStg'), 'create')
      },
      typeTreeData: {},
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      numberRules: {
        chatNumber: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.checkRepeat, trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyTable']
    },
    importTableDlg() {
      return this.$refs['importTableDlg']
    },
    chatTypeObj() {
      return this.traversalChatTypeOption('value', this.chatType) || this.chatTypeOption[0]
    },
    numberDialogTitle() {
      const concatType = this.numberDialogStatus == 'importCreate' ? 'create' : 'update'
      return this.i18nConcatText(this.chatTypeObj['formLabel'], concatType)
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    this.loadTypeTree()
    this.resetTemp()
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    createChatGroup,
    updateChatGroup,
    getTypeByName,
    countByGroupId,
    deleteWhite,
    deleteGroupAndData,
    moveGroupToOther,
    getChatsByType,
    deleteChatGroup,
    checkRepeat(rule, value, callback) {
      getWhiteListInfo(this.numberTemp).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.numberTemp.id) {
          callback(new Error(this.$t('pages.validateMsg_sameAccount')))
        } else {
          callback()
        }
      })
    },
    tabClick(pane, event) {
      const activeTab = this.activeTab;
      const option = this.traversalChatTypeOption('relRef', activeTab)
      if (option) {
        this.chatType = option.value
      }
    },
    // 遍历chatTypeOption，取对象中的key属性，然后用keyVal进行比较，得到返回
    traversalChatTypeOption(key, keyVal) {
      const option = this.chatTypeOption.find(dict => dict[key] == keyVal)
      this.initOptionRef(option)
      return option
    },
    // 遍历chatTypeOption，取每个对象，然后进行处理
    chatTypeOptionHandle(func) {
      if (func instanceof Function) {
        for (const option of this.chatTypeOption) {
          this.initOptionRef(option)
          func(option, option.ref)
        }
      }
    },
    initOptionRef(option) {
      if (option && !option.ref) {
        const relRef = this.$refs[option.relRef]
        if (relRef) {
          option.ref = relRef[0]
        }
      }
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    afterLoad2: function(rowData, grid) {
      var selectMap = {}
      this.temp.whiteList.forEach((item, index) => {
        selectMap['white' + item.id] = item.id
      })
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          if (selectMap['white' + item.id] != null) {
            grid.toggleRowSelection(item)
          }
        })
      })
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getDataPage(searchQuery)
    },
    loadAccountList(option) {
      const searchQuery = Object.assign({}, { type: this.chatType }, option)
      return getWhitListPage(searchQuery)
    },
    groupIdFormatter(row, data) {
      return this.getGroupNameByDataId(this.typeTreeData[this.chatType], data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        list: []
      }
      this.chatTypeOptionHandle((option) => {
        this.temp.list.push({
          timeId: 1,
          controlType: 1,
          chatType: option.value,
          whiteListId: ''
        })
      })
      this.keyword2 = ''
    },
    reloadSelect() {
      this.activeTab = 'qqRef'
      this.chatType = 3
      this.$nextTick(() => {
        this.chatTypeOptionHandle((option, relRef) => {
          relRef && relRef.reloadTable()
        })
      })
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.reloadSelect()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      // list 和 chatTypeOption 顺序强一致
      const list = this.temp.list || [];
      // 补充策略中未包含通讯工具的策略限制信息
      // 同时调整list位置与chatTypeOption一致
      const orderList = []
      for (const chatTypeDict of this.chatTypeOption) {
        const item = list.find(item => item.chatType == chatTypeDict.value)
        orderList.push(item || {
          timeId: 1,
          controlType: 1,
          chatType: chatTypeDict.value,
          whiteListId: ''
        })
      }
      this.temp.list = [...orderList]
      this.reloadSelect()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.keyword2 = ''
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.chatTypeOptionHandle((option, ref) => {
          if (ref) {
            ref.$refs['whiteForm'] && ref.$refs['whiteForm'].clearValidate()
            ref.radioClick(this.temp.list[option.listIndex].controlType)
          }
        })
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    formatData() {
      this.temp.list.forEach(item => {
        if (item.controlType != 2) {
          item.whiteListId = ''
        }
      })
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatData()
          this.setNumberList()
          createData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatData()
          this.setNumberList()
          updateData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    controlTypeFormatter: function(row, data) {
      let str = ''
      this.chatTypeOptionHandle((option) => {
        const rowData = row.list.find(item => item.chatType == option.value)
        str && (str += ', ');
        if (rowData) {
          // 现有的策略信息就是  label + controlType, 如果有需要更改时，再另外处理
          const controlType = rowData.controlType || 1
          str += `${option.label}：${this.controlTypeOptions[controlType]}`
        } else {
          str += `${option.label}：${this.controlTypeOptions[1]}`
        }
      })
      return str
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    importAccountFunc() {
      this.$nextTick(() => {
        const label = this.chatTypeObj.formLabel
        this.importColModel.splice(0, 1, { prop: 'chatNumber', label, width: '20', sort: 'custom' },)
        this.$refs.importTableDlg.show()
      })
    },
    importData(ids) {
      if (ids && ids.length > 0) {
        getChatsByType({ ids: ids.join(',') }).then(res => {
          this.chatTypeObj['ref'].pushImportData(res.data)
        })
      }
    },
    setNumberList() {
      const data = this.temp.list
      this.chatTypeOptionHandle((option, ref) => {
        if (ref) {
          data[option.listIndex].whiteListId = ref.rowDatas.map(item => item.id).join(',')
        }
      })
    },
    loadTypeTree: function() {
      this.chatTypeOption.forEach(data => {
        this.typeTreeData[data.value] = [{ id: '0', dataId: '0', label: this.$t('pages.ungrouped'), type: 'G' }]
      })
      return getTypeTree().then(res => {
        if (res.data && res.data.length > 0) {
          res.data.forEach(data => {
            const dataType = Number(data.dataType)
            this.typeTreeData[dataType].push(data)
          })
        }
      })
    },
    loadImportTree() {
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: this.typeTreeData[this.chatType] })
      })
    },
    scanUpdateGroupTree() {
      return this.loadTypeTree().then(() => {
        this.chatTypeOption.forEach(data => {
          this.updateGroupTree(data.value, this.typeTreeData[data.value])
        })
      })
    },
    updateGroupTree(chatType, data) {
      this.$nextTick(() => {
        this.typeTreeData[chatType] = data
        const option = this.traversalChatTypeOption('value', chatType)
        option && option['ref'] && option['ref'].changeGroupTree(data)
      })
    },
    importHandleCreate(selectedGroupId) {
      this.numberDialogFormVisible = true
      this.numberTemp = { type: this.chatType }
      this.numberDialogStatus = 'importCreate'
      this.numberCheckedKeys = ['0']
      this.$nextTick(() => {
        this.treeCheckChange(selectedGroupId || '0')
        this.$refs['whiteForm'].clearValidate()
      })
    },
    importHandleUpdate(data) {
      this.numberDialogFormVisible = true
      this.numberDialogStatus = 'importUpdate'
      this.numberTemp = JSON.parse(JSON.stringify(data))
      this.numberCheckedKeys = [data.groupId]
      this.$nextTick(() => {
        this.$refs['whiteForm'].clearValidate()
      })
    },
    createNumberData() {
      this.$refs['whiteForm'].validate((valid) => {
        if (valid) {
          this.dlgSubmitting = true
          this.numberTemp.type = this.chatType
          const checkKey = this.numberCheckedKeys[0]
          this.numberTemp.groupId = checkKey
          createWhite(this.numberTemp).then(() => {
            this.dlgSubmitting = false
            this.numberDialogFormVisible = false
            this.importTableDlg.justRefreshTableData()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.dlgSubmitting = false
          })
        }
      })
    },
    updateNumberData() {
      this.$refs['whiteForm'].validate((valid) => {
        if (valid) {
          this.dlgSubmitting = true
          const tempData = Object.assign({}, this.numberTemp)
          const checkKey = this.numberCheckedKeys[0]
          tempData.groupId = checkKey
          updateWhite(tempData).then(() => {
            this.dlgSubmitting = false
            this.numberDialogFormVisible = false
            this.importTableDlg.justRefreshTableData()
            this.chatTypeObj['ref'].refreshTable()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.dlgSubmitting = false
          })
        }
      })
    },
    submitDeleteEnd(deleteIdsList) {
      deleteIdsList = deleteIdsList || []
      this.chatTypeObj['ref'].deleteTable(deleteIdsList)
    },
    treeCheckChange(key, data) {
      this.numberCheckedKeys = [key]
    },
    changeGroupAfter() {
      this.chatTypeOption.forEach(data => {
        this.updateGroupTree(data.value, this.typeTreeData[data.value])
      })
      this.chatTypeObj['ref'].refreshTable()
    },
    createGroup(chatType, ref) {
      // 这行代码 主要用于导入终端通讯账号时，创建分组；
      chatType && (this.chatType = chatType)
      this.pendingRef = ref
      this.$refs['createGroupDlg'].handleCreate(-1)
    },
    createGroupAfter(data) {
      this.scanUpdateGroupTree().then(() => {
        this.$nextTick(() => {
          const option = this.traversalChatTypeOption('value', this.chatType)
          if (option) {
            const relRef = option.relRef
            const ref = option.ref
            if (relRef === 'wechatRef' && ref) {
              const chatAccount = ref.$refs['chatAccount']
              if (chatAccount && chatAccount.dialogFormVisible) {
                chatAccount.groupSelectChange(data.id)
                return
              }
            }
            ref && ref.treeSelectChange(data.id)
          }
          this.pendingRef && this.pendingRef.treeSelectChange(data.id, this.chatType)
        })
      })
    },
    importFromExcel() {
      this.$refs.importExcelDlg.show()
    },
    uploadFromExcel(data) {
      return request.post('/imToolStrategy/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    importSubmitEnd() {
      this.gridTable.execRowDataApi()
    }
  }
}
</script>

<style lang="scss" scoped>
.imTab >>>.el-tabs__item.is-top:nth-child(2) {
  padding-left: 20px;
}
</style>
