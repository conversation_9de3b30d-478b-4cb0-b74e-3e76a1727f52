<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :title="$t('text.importInfo', { info: title })"
      :visible.sync="dlgVisible"
      width="600px"
    >
      <div class="upload-div">
        <el-upload
          ref="upload"
          action="1111"
          :limit="1"
          list-type="text"
          :file-list="fileList"
          :accept="accept"
          :auto-upload="false"
          :on-exceed="onFileExceed"
          :on-change="onFileChange"
          :on-remove="onFileRemove"
          :http-request="onUpload"
          :disabled="showUploadInfo"
          class="upload-demo"
          style="display:inline-block;max-width: 670px;"
        >
          <el-button v-show="!showUploadInfo" size="small" type="primary">{{ $t('pages.processStgLib_Msg94', { format: accept }) }}</el-button>
          <div slot="tip" class="el-upload__tip" style="color: red; margin-bottom: 4px">
            <span v-show="errorMsg">{{ errorMsg }}</span>
          </div>
        </el-upload>

        <common-downloader
          v-show="!showUploadInfo"
          class="tpl-download"
          :name="fileName"
          :button-name="$t('pages.importTemplateDownload')"
          button-type="text"
          button-icon=""
          @download="downloadTemplate"
        />
        <el-radio-group v-show="!showUploadInfo" v-model="templateType" style="display: inline-block">
          <el-radio :label="1">{{ '.xls' }}</el-radio>
          <el-radio :label="2">{{ '.xlsx' }}</el-radio>
          <el-radio :label="3">{{ '.et' }}</el-radio>
        </el-radio-group>

        <Form>
          <FormItem :label="$t('pages.matchPriority')" :tooltip-content="$t('pages.matchPriorityTip')" label-width="92px">
            <div style="display: flex">
              <tree-menu
                :width="180"
                :node-key="'dataId'"
                :data="priorityTree"
                :is-filter="false"
                :allow-drag="() => true"
                :allow-drop="allowDrop"
              />
              <div style="flex: 1; padding: 0 10px 0 8px">
                <el-checkbox v-model="fuzzyMatchName" :true-label="1" :false-label="0" style="margin-right: 0">{{ $t('pages.useNameFuzzyMatchSysName') }}</el-checkbox>
                <el-checkbox v-model="fuzzyMatchUserName" :true-label="1" :false-label="0">{{ $t('pages.useNameFuzzyMatchSysUserName') }}</el-checkbox>
              </div>
            </div>
          </FormItem>
        </Form>
      </div>
      <el-card v-if="showUploadInfo" :body-style="{'padding': '0'}" class="box-card">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.processStgLib_Msg86') }}</span>
        </div>
        <ul style="margin: 0">
          <li>{{ $t('pages.parsingFile') }}</li>
          <li v-if="uploadStatus.hasOwnProperty('percent') && uploadStatus.percent < 100"> {{ $t('pages.parsingProgress',{ percent: uploadStatus.percent }) }}</li>
          <li v-show="uploadStatus.addSize"> {{ $t('pages.parsingEffectiveDataNum',{ number: uploadStatus.addSize }) }}</li>
          <li v-show="uploadStatus.hasOwnProperty('failSize')"> {{ $t('pages.parsingFailDataNum',{ number: uploadStatus.failSize }) }}</li>
          <li v-show="uploadStatus.failSize">
            <common-downloader
              class="err-download"
              :name="$t('pages.abnormalData') + fileType"
              :button-name="$t('pages.abnormalData') + fileType"
              button-type="text"
              button-icon=""
              @download="downloadErrorFile"
            />
          </li>
          <li v-show="uploadStatus.percent >= 100">{{ $t('pages.parsingEnd') }}</li>
          <template v-if="Object.keys(importStatus).length > 0">
            <li>{{ $t('pages.importingData') }}</li>
            <li v-if="importStatus.hasOwnProperty('percent') && importStatus.percent < 100"> {{ $t('pages.importProgress',{ percent: importStatus.percent }) }}</li>
            <li v-show="importStatus.addSize">{{ $t('pages.generateStgNum', { number: importStatus.addSize }) }}</li>
            <li v-show="importStatus.updateSize">{{ $t('pages.updateStgNum', { number: importStatus.updateSize }) }}</li>
            <li v-show="importStatus.percent >= 100">{{ $t('pages.processStgLib_Msg88') }}</li>
          </template>
        </ul>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button v-show="!importTaskId" type="primary" :loading="submitting" @click="handleUpload">{{ $t('button.confirm') }}</el-button>
        <el-button @click="hide">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dataDlgVisible"
      width="1000px"
      @close="closeDataDlg"
    >
      <div style="margin-left: 20px">
        <div style="display: flex; align-items: center; height: 30px; margin-bottom: 4px">
          <el-checkbox v-model="checkAll" @change="changeSelected">{{ $t('pages.checkAllPagesData') }}</el-checkbox>
          <div style="margin-left: auto">
            <el-input
              v-model="terminalImtoolQuery.searchInfo"
              v-trim
              clearable
              :placeholder="`${$t('table.terminalCode')}/${$t('table.terminalName')}/${$t('table.userAccount')}/${$t('table.userName1')}`"
              style="width: 218px;"
              @keyup.enter.native="handleFilter"
            />
            <el-button type="primary" icon="el-icon-search" size="mini" style="margin: 0" @click="handleFilter">
              {{ $t('table.search') }}
            </el-button>

            <el-popover
              placement="bottom"
              width="350"
              trigger="click"
            >
              <Form ref="searchForm" label-position="right" :model="terminalImtoolQuery" label-width="100px">
                <FormItem :label="$t('table.terminalCode')">
                  <el-input v-model="terminalImtoolQuery.terminalId" v-trim clearable :maxlength="60"/>
                </FormItem>
                <FormItem :label="$t('table.terminalName')">
                  <el-input v-model="terminalImtoolQuery.terminalName" v-trim clearable :maxlength="60"/>
                </FormItem>
                <FormItem :label="$t('table.userAccount')">
                  <el-input v-model="terminalImtoolQuery.userAccount" v-trim clearable :maxlength="60"/>
                </FormItem>
                <FormItem :label="$t('table.userName1')">
                  <el-input v-model="terminalImtoolQuery.userName" v-trim clearable :maxlength="60"/>
                </FormItem>
                <FormItem :label="$t('table.imToolType')">
                  <el-select v-model="terminalImtoolQuery.chatType" clearable style="width: 100%;">
                    <el-option v-for="(item, key) in (temp.chatTypeLimitInfos || [])" :key="key" :label="chatTypeFormatter(null, item['chatType'])" :value="item['chatType']"/>
                  </el-select>
                </FormItem>
                <FormItem :label="$t('table.matchRule')">
                  <el-select v-model="terminalImtoolQuery.matchRule" clearable style="width: 100%;">
                    <el-option v-for="(item, key) in queryMatchRuleDict" :key="key" :label="item.label" :value="parseInt(item.value)"></el-option>
                  </el-select>
                </FormItem>
              </Form>
              <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
                <el-button type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
              </div>
              <el-button slot="reference" type="primary" size="mini" style="margin: 0">
                {{ $t('button.advancedSearch') }}
              </el-button>
            </el-popover>
          </div>
        </div>
        <grid-table
          ref="terminalImtoolTable"
          custom-col
          :height="300"
          :col-model="colModel"
          :row-data-api="terminalImtoolDataApi"
          :autoload="false"
          :row-key="'index'"
          :after-load="afterLoad"
          :checked-row-keys="tempCheckKeys"
          @select="selectData"
          @select-all="selectAll"
        />
      </div>
      <div style="height: 5px"></div>
      <Form :model="temp" label-position="right" label-width="100px">
        <FormItem label-width="40px">
          <el-checkbox v-model="temp.importToStg" disabled>{{ $t('pages.imtoolImportTip1') }}</el-checkbox>
        </FormItem>
        <FormItem label-width="225px" :label="$t('pages.relTerminalHasSelfStg')">
          <el-radio-group v-model="temp.existStgHandler">
            <el-radio :label="1">{{ $t('pages.relTerminalHasSelfStgOpt1') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.relTerminalHasSelfStgOpt2') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="temp.existStgHandler === 2" label-width="235px">
          <el-checkbox v-model="temp.coverTimeId">{{ $t('pages.coverEffectiveTime') }}</el-checkbox>
          <el-checkbox v-model="temp.changeStgLimit">{{ $t('pages.updateLimitToAllowAccountLogin') }}</el-checkbox>
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.updateLimitToAllowAccountLoginTip') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <div v-for="(item, index) in temp.chatTypeLimitInfos || []" :key="index">
          <el-divider v-if="item.checkNums > 0" content-position="left">{{ `${chatTypeFormatter(null, item['chatType'])}${$t('table.stgLimit')}` }}</el-divider>
          <div v-if="item.checkNums > 0" style="padding: 8px 15px 0">
            <el-row>
              <el-col :span="8">
                <FormItem :label="$t('table.effectTime')" prop="timeId" label-width="65px">
                  <div style="display: flex">
                    <el-select v-model="item['timeId']" class="input-with-button" :disabled="!formable">
                      <el-option
                        v-for="timeInfo in timeInfoOptions"
                        :key="timeInfo.value"
                        :label="timeInfo.label"
                        :value="parseInt(timeInfo.value)"
                      />
                    </el-select>
                    <link-button btn-class="editBtn" :formable="formable" :menu-code="'A51'" :link-url="'/system/baseData/timeInfo'"/>
                  </div>
                </FormItem>
              </el-col>
              <el-col :span="9">
                <FormItem :label="$t('pages.importGroup')" label-width="75px">
                  <div style="display: flex">
                    <el-select v-model="item['groupId']">
                      <el-option
                        v-for="chatGroupItem in chatGroupTreeDatas[item['chatType']]"
                        :key="chatGroupItem.dataId"
                        :label="chatGroupItem.label"
                        :value="chatGroupItem.dataId"
                      />
                    </el-select>
                    <el-button class="editBtn" @click="createGroup(item['chatType'])">
                      <svg-icon icon-class="add"/>
                    </el-button>
                  </div>
                </FormItem>
              </el-col>
              <el-col :span="7" style="line-height: 26px;padding-left: 16px">
                <el-checkbox v-model="item['coverLib']">{{ $t('pages.coverLibSameAccount') }}</el-checkbox>
              </el-col>
            </el-row>
          </div>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="importSubmitting" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button @click="hideDataDlg">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchFile } from '@/utils/download/helper'
import request from '@/utils/request'
import CommonDownloader from '@/components/DownloadManager/common'
import {
  getTerminalImtool,
  getTypeByName,
  createChatGroup,
  keepDataCache, dropDataCache, importTerminalImtool, validImportDatas
} from '@/api/behaviorManage/application/imTool'
import { getDictLabel } from '@/utils/dictionary'
import { mapGetters } from 'vuex'

export default {
  name: 'ImportTermImTool',
  components: { CommonDownloader },
  props: {
    formable: { type: Boolean, default: true },
    chatTypeOpts: { type: Array, default() { return [] } },
    chatGroupTreeDatas: { type: Object, default() { return {} } }
  },
  data() {
    return {
      dlgVisible: false,
      showUploadInfo: false,
      uploadFailed: false,
      submitting: false,
      importSubmitting: false,
      dataDlgVisible: false,
      errorMsg: '',
      taskId: '',
      importTaskId: undefined,
      timer: undefined,
      fuzzyMatchName: 1,
      fuzzyMatchUserName: 1,
      fileType: '.xls',
      temp: {},
      defaultTemp: {
        importToStg: true,
        existStgHandler: 1,
        coverTimeId: true,
        changeStgLimit: true
      },
      // [ { chatType: 通讯工具类型, total: 对应总数 }, ..... ]
      chatTypeInfos: [],
      colModel: [
        { prop: 'terminalId', label: 'terminalCode', width: 80 },
        { prop: 'terminalName', label: 'terminalName', width: 80 },
        { prop: 'userAccount', label: 'userAccount', width: 100 },
        { prop: 'userName', label: 'userName1', width: 100 },
        { prop: 'chatType', label: 'imToolType', width: 60, formatter: this.chatTypeFormatter },
        { prop: 'chatNumber', label: 'imToolNumber', width: 100 },
        { prop: 'chatRemark', label: 'imToolNumberRemark', width: 80 },
        { prop: 'sysTerminal', label: 'relSysTerminal', width: 130, type: 'treeSelect', alwaysEdit: false, isFilter: true,
          leafKey: 'terminal', checkedKeysFieldName: 'checkedKeys', checkStrictly: true, showContent: row => row.relTerminalName || '', rewriteNodeClickFuc: true,
          clearable: true, disabled: (col, row) => !!row.relUserId, nodeChange: this.relSysObjectChange, changeSelectedNodes: this.relSysTerminalValChange
        },
        { prop: 'sysUser', label: 'relSysUser', width: 130, type: 'treeSelect', alwaysEdit: false, isFilter: true,
          leafKey: 'user', checkedKeysFieldName: 'userCheckedKeys', checkStrictly: true, showContent: row => row.relUserName || '', rewriteNodeClickFuc: true,
          clearable: true, disabled: (col, row) => !!row.relTerminalId, nodeChange: this.relSysObjectChange, changeSelectedNodes: this.relSysUserValChange
        },
        { prop: 'matchRule', label: 'matchRule', width: 120, formatter: this.matchRuleFormatter }
      ],
      matchRuleDict: [
        { label: this.$t('pages.terminalIdMatch'), value: 1 },
        { label: this.$t('pages.terminalNameMatch'), value: 2 },
        { label: this.$t('pages.terminalNameFuzzyMatch'), value: 3 },
        { label: this.$t('pages.userIdMatch'), value: 4 },
        { label: this.$t('pages.userNameMatch'), value: 5 },
        { label: this.$t('pages.userNameFuzzyMatch'), value: 6 }
      ],
      priorityTree: [],
      defaultPriorityTree: [
        { dataId: 1, label: this.$t('pages.terminalIdMatch') },
        { dataId: 4, label: this.$t('pages.userIdMatch') },
        { dataId: 2, label: this.$t('pages.terminalNameMatch') },
        { dataId: 5, label: this.$t('pages.userNameMatch') },
        { dataId: 3, label: this.$t('pages.terminalNameFuzzyMatch') },
        { dataId: 6, label: this.$t('pages.userNameFuzzyMatch') }
      ],
      queryMatchRuleDict: [],
      uploadStatus: {},
      importStatus: {},
      backupCheckIds: new Set(),
      updateRelMap: {},
      addGroupChatType: '',
      terminalImtoolQuery: {},
      defaultTerminalImtoolQuery: {
        searchInfo: '',
        terminalId: '',
        terminalName: '',
        userAccount: '',
        userName: '',
        chatType: '',
        matchRule: undefined
      },
      tempCheckKeys: [],
      title: this.$t('pages.terminalImTool'),
      fileName: this.$t('pages.terminalImTool'),
      uploadFileName: '',
      accept: '.xls,.xlsx,.et',
      fileList: [],
      checkAll: true,
      templateType: 1
    }
  },
  computed: {
    ...mapGetters(['termTreeList']),
    timeInfoOptions() {
      return this.$store.getters.timeOptions
    }
  },
  methods: {
    getTypeByName,
    createChatGroup,
    resetTemp() {
      this.errorMsg = ''
      this.uploadFailed = false
      this.showUploadInfo = false
      this.submitting = false
      this.fuzzyMatchName = 1
      this.fuzzyMatchUserName = 1
      this.fileList.splice(0)
      this.importStatus = {}
      this.importTaskId = undefined
    },
    show() {
      this.resetTemp()
      this.dlgVisible = true
      this.priorityTree = [...this.defaultPriorityTree]
    },
    hide() {
      this.resetTemp()
      this.dlgVisible = false
    },
    hideDataDlg() {
      this.importSubmitting = false
      this.dataDlgVisible = false
    },
    chatTypeFormatter(row, data) {
      return getDictLabel(this.chatTypeOpts, data) || ''
    },
    matchRuleFormatter(row, data) {
      return getDictLabel(this.matchRuleDict, data)
    },
    // 文件超出个数限制时的钩子，实现后上传的文件替换掉先上传的文件
    onFileExceed(files, fileList) {
      // 删除所有上传的文件
      this.$refs.upload.clearFiles();
      // handleStart()指的是手动选择文件，Element Plus 的el-upload有说明
      this.$refs.upload.handleStart(files[0])
    },
    onFileChange(file, fileList) {
      window.uploadFile = file.raw
      this.fileList = fileList
      this.errorMsg = ''
    },
    onFileRemove(file, fileList) {
      this.fileList = fileList
      this.errorMsg = ''
    },
    terminalImtoolDataApi(opt) {
      const searchQuery = Object.assign({}, this.terminalImtoolQuery, opt)
      // return getTerminalImtool(searchQuery)
      return new Promise((resolve, reject) => {
        getTerminalImtool(searchQuery).then(res => {
          ((res.data || {}).items || []).forEach(item => {
            const dataId = this.updateRelMap[item.index]
            if (dataId) {
              item.checkedKeys = [`T${dataId}`]
              item.relTerminalName = (this.termTreeList.find(item => item.dataId == dataId) || {}).label
            } else if (item.relTerminalId) {
              item.checkedKeys = [`T${item.relTerminalId}`]
            } else if (item.relUserId) {
              item.userCheckedKeys = [`U${item.relUserId}`]
            }
          })
          resolve(res)
        }).catch(reason => reject(reason))
      })
    },
    afterLoad(rowData, table) {
      const checkKeys = rowData.filter(data => this.checkAll ^ this.backupCheckIds.has(data['index'])).map(data => data['index'])
      this.tempCheckKeys.splice(0, this.tempCheckKeys.length, ...checkKeys)
    },
    selectData(selection, row) {
      const index = selection.findIndex(item => item.index === row.index)
      this.handleCheckInfo(row.chatType, index !== -1)
      if (this.checkAll) {
        if (index === -1) {
          this.backupCheckIds.add(row.index)
        } else {
          this.backupCheckIds.delete(row.index)
        }
      } else {
        if (index >= 0) {
          this.backupCheckIds.add(row.index)
        } else {
          this.backupCheckIds.delete(row.index)
        }
      }
      if (index >= 0) {
        this.tempCheckKeys.push(row.index)
      } else {
        const tIndex = this.tempCheckKeys.findIndex(key => key === row.index)
        tIndex >= 0 && this.tempCheckKeys.splice(tIndex, 1)
      }
    },
    selectAll(selection) {
      if (this.checkAll) {
        if (selection.length === 0) {
          this.$refs['terminalImtoolTable'].rowData.forEach(item => {
            this.backupCheckIds.add(item.index)
            this.handleCheckInfo(item.chatType, false)
          })
        } else {
          selection.forEach(item => {
            const has = this.backupCheckIds.delete(item.index)
            has && this.handleCheckInfo(item.chatType, true)
          })
        }
      } else {
        if (selection.length > 0) {
          selection.forEach(item => {
            if (!this.backupCheckIds.has(item.index)) {
              this.backupCheckIds.add(item.index)
              this.handleCheckInfo(item.chatType, true)
            }
          })
        } else {
          this.$refs['terminalImtoolTable'].rowData.forEach(item => {
            const has = this.backupCheckIds.delete(item.index)
            has && this.handleCheckInfo(item.chatType, false)
          })
        }
      }
      this.tempCheckKeys.splice(0, this.tempCheckKeys.length, ...selection.map(item => item.index))
    },
    relSysObjectChange(nodeData, node, vm, rowData, col) {
      // 选中终端再赋值
      if (nodeData.type == 1 || nodeData.type == 2) {
        rowData[nodeData.type == 1 ? 'checkedKeys' : 'userCheckedKeys'] = [nodeData.id]
        rowData[nodeData.type == 1 ? 'relTerminalId' : 'relUserId'] = nodeData.dataId
        rowData[nodeData.type == 1 ? 'relTerminalName' : 'relUserName'] = nodeData.label
        this.updateRelMap[rowData.index] = nodeData.id
      }
    },
    relSysTerminalValChange(rowData, index, keys, options) {
      if (!keys) {
        rowData.checkedKeys.splice(0)
        rowData.relTerminalId = undefined
        rowData.relTerminalName = undefined
        this.updateRelMap[rowData.index] = ''
      }
    },
    relSysUserValChange(rowData, index, keys, options) {
      if (!keys) {
        rowData.userCheckedKeys.splice(0)
        rowData.relUserId = undefined
        rowData.relUserName = undefined
        this.updateRelMap[rowData.index] = ''
      }
    },
    onUpload(data) {
      // 选择文件后，去修改文件，然后上传会报 network error 错误。
      // 浏览器比较注意文件安全，你修改并保存那就是新的文件（文件修改内容后 file 的本地文件已经丢失），
      // 浏览器是不具有新文件的访问权限的，除非你再走一遍选择文件这个流程
      // 上传文件前，验证是否被修改
      data.file.slice(0, 1) // only the first byte
        .arrayBuffer() // try to read
        .then(() => {
          // 文件没改变，在这里可以发请求了
          this.showUploadInfo = true
          this.submitting = true
          const fileName = data.file.name
          this.uploadFileName = fileName

          const matchPriorities = this.priorityTree.map(p => p.dataId)
            .filter(dataId => (dataId == 3 && this.fuzzyMatchName) || (dataId == 6 && this.fuzzyMatchUserName) || !(dataId == 3 || dataId == 6))
            .join(',')

          const fd = new FormData()

          fd.append('file', data.file)// 传文件
          fd.append('fileType', this.fileType)
          fd.append('matchPriorities', matchPriorities)
          fd.append('taskId', this.taskId)
          const execFunc = (data) => request.post('/imToolStrategy/parseTerminalImtool', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })

          execFunc(fd).then(res => {
            // this.$notify({ title: this.$t('text.success'), message: fileName + '文件导入成功', type: 'success', duration: 2000 })
            // 传递importGroupId数据，当且仅当excel表的所有数据分组都相同时，才存在importGroupId
            if (!res.data) { return }
            if (res.data.total > 0) {
              this.showDataDlg(res.data)
            } else {
              this.$notify.error({
                title: this.$t('text.importFail'),
                message: this.$t('valid.noGetEffectiveData') + ', ' + this.$t('pages.pleaseUploadFileAgain')
              })
              this.showUploadInfo = false
            }
            this.submitting = false
          }).catch(res => {
            this.uploadFailed = true
            this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.processStgLib_Msg80', { name: fileName }), type: 'error', duration: 2000 })
          })
          return false // 屏蔽了action的默认上传
        })
        .catch((err) => {
          // 文件有问题，在这里终止掉
          console.error('failed to read', err);
          this.$message({
            message: this.$t('pages.fileModifyChooseAgain'),
            type: 'error',
            duration: 2000
          })
          return false
        })
    },
    // 提交数据前，验证文件是否正常
    validate() {
      let validateFile = true
      if (this.fileList.length === 0) {
        this.errorMsg = this.$t('pages.processStgLib_Msg98')
        validateFile = false
      } else {
        // 验证文件
        const file = this.fileList[0]
        const fileName = file.name
        const fileType = fileName.substring(fileName.lastIndexOf('.'))

        if (this.accept && this.accept.indexOf(fileType) < 0) {
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.importSupportFile', { format: this.accept }),
            type: 'warning',
            duration: 2000
          })
          validateFile = false
        } else {
          this.fileType = fileType
        }
      }
      return validateFile
    },
    handleUpload() {
      if (!this.validate()) { return }
      this.uploadStatus = {}
      this.isUploadEndStatus = false
      this.taskId = new Date().getTime()
      //  todo 上传不符合要求的文件，不会报错
      this.$socket.subscribeToUser(this.taskId, '/import/data', (resp, handle) => {
        if (this.uploadFailed) {
          handle.close()
          return
        }
        const uploadedPercent = this.uploadStatus.percent || 0
        if (uploadedPercent <= resp.data.percent) {
          this.uploadStatus = resp.data
        }
        if (this.uploadStatus.percent >= 100) {
          handle.close()
        }
      }, false)
      this.$refs.upload.submit()
    },
    downloadTemplate(file) {
      return fetchFile({
        file,
        jwt: true,
        topic: 'ImportTemplate',
        url: '/importData/downloadTemplate',
        method: 'post',
        responseType: 'blob',
        data: {
          fileName: this.fileName + (this.templateType === 1 ? '.xls' : this.templateType === 2 ? '.xlsx' : '.et'),
          template: 'terminalImtool',
          templateType: this.templateType
        }
      })
    },
    downloadErrorFile(file) {
      return fetchFile({
        file,
        jwt: true,
        topic: 'ImportErrorData',
        url: '/importData/downloadCache',
        method: 'post',
        responseType: 'blob',
        data: {
          template: this.uploadStatus.failFile,
          fileName: file.name
        }
      })
    },
    showDataDlg(response) {
      const chatTypeLimitInfos = response.chatTypeInfos.map(info => { return { chatType: info['chatType'], timeId: 1, groupId: '0', coverLib: true, checkNums: info['total'], total: info['total'] } })
      this.temp = Object.assign({}, this.defaultTemp, { chatTypeLimitInfos })
      this.terminalImtoolQuery = Object.assign({ taskId: this.taskId }, this.defaultTerminalImtoolQuery)
      const matchRuleTotals = response.matchRuleTotals
      this.queryMatchRuleDict.splice(0);
      (matchRuleTotals[0] > 0) && this.queryMatchRuleDict.push({ label: this.$t('pages.identifierRule_text5'), value: 0 });
      for (let i = 1; i < matchRuleTotals.length; i++) {
        (matchRuleTotals[i] > 0) && this.queryMatchRuleDict.push(this.matchRuleDict[i - 1])
      }
      this.checkAll = true
      this.backupCheckIds.clear()
      this.tempCheckKeys.splice(0)
      this.updateRelMap = {}
      this.dataDlgVisible = true
      // 每半分钟维持一次缓存
      console.log('begin keep data cache --- ' + this.taskId)
      this.timer = setInterval(() => {
        keepDataCache({ taskId: this.taskId })
      }, 30000)
      this.$nextTick(() => {
        this.$refs['terminalImtoolTable'] && this.$refs['terminalImtoolTable'].execRowDataApi({ page: 1, limit: 20 })
      })
    },
    handleImport() {
      const requestBody = Object.assign({}, this.temp, {
        checkAll: this.checkAll,
        backupCheckIds: [...this.backupCheckIds],
        updateRelMap: this.updateRelMap,
        taskId: this.taskId,
        supportChatTypes: this.chatTypeOpts.map(item => item.value),
        fileName: this.uploadFileName
      })
      this.importSubmitting = true
      validImportDatas(requestBody).then(res => {
        this.importSubmitting = false
        if (res.data > 0) {
          this.dataDlgVisible = false
          this.importTaskId = new Date().getTime()
          this.$socket.subscribeToUser(this.importTaskId, '/import/data', (resp, handle) => {
            const uploadedPercent = this.importStatus.percent || 0
            if (uploadedPercent <= resp.data.percent) {
              this.importStatus = resp.data
            }
            if (this.importStatus.percent >= 100) {
              this.dropDataCache()
              this.$emit('importSubmitEnd')
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.processStgLib_Msg88'),
                type: 'success',
                duration: 2000
              })
              handle.close()
            }
          }, false)

          importTerminalImtool(Object.assign({}, requestBody, { listenProgressTaskId: this.importTaskId }))
            .then(res => {}).catch(() => {})

          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.importingPleaseWait'),
            type: 'success',
            duration: 2000
          })
        }
      }).catch(error => {
        this.importSubmitting = false
        console.log(error)
      })
    },
    handleFilter() {
      this.terminalImtoolQuery.page = 1
      this.$refs['terminalImtoolTable'].execRowDataApi(this.terminalImtoolQuery)
    },
    resetQuery() {
      Object.assign(this.terminalImtoolQuery, this.defaultTerminalImtoolQuery)
    },
    createGroup(chatType) {
      this.$emit('createGroup', chatType, this)
    },
    treeSelectChange(dataId, chatType) {
      // 创建分组后，默认选中分组
      const item = this.temp.chatTypeLimitInfos.find(info => info['chatType'] == chatType)
      this.$nextTick(() => {
        item && (item['groupId'] = dataId + '')
      })
    },
    closeDataDlg() {
      if (!this.importTaskId) {
        this.dropDataCache()
      }
    },
    dropDataCache() {
      clearInterval(this.timer)
      dropDataCache({ taskId: this.taskId })
      console.log('drop data cache --- ' + this.taskId)
    },
    changeSelected() {
      this.backupCheckIds.clear()
      if (this.checkAll) {
        const keys = this.$refs['terminalImtoolTable'].rowData.map(data => data['index'])
        this.tempCheckKeys.splice(0, this.tempCheckKeys.length, ...keys)
        this.temp.chatTypeLimitInfos.forEach(info => { info.checkNums = info.total })
      } else {
        this.tempCheckKeys.splice(0, this.tempCheckKeys.length)
        this.temp.chatTypeLimitInfos.forEach(info => { info.checkNums = 0 })
      }
    },
    handleCheckInfo(chatType, isAdd) {
      const limitInfo = this.temp.chatTypeLimitInfos.find(info => info['chatType'] == chatType)
      if (!limitInfo) { return }
      // 勾选
      limitInfo.checkNums += (isAdd ? 1 : -1)
    },
    allowDrop(draggingNode, dropNode, type) {
      return type !== 'inner'
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-div {
  padding: 5px 10px 5px 20px;
}
.editBtn {
  margin: 0 2px !important;
}
>>>.err-download > button {
  margin: 0;
}
ul > li {
  margin: 5px;
}
</style>
