<template>
  <stg-dialog
    ref="stgDlg"
    :title="$t('pages.sysAlarmConfig')"
    :stg-code="163"
    :active-able="activeAble"
    :time-able="true"
    :time-mode="1"
    :rules="rules"
    :model="defaultTemp"
    :cur-form-data="temp"
    :entity-node="entityNode"
    :create="createStrategy"
    :update="updateStrategy"
    :get-by-name="getStrategyByName"
    :format-row-data="formatRowData"
    :format-form-data="formatFormData"
    :validate-form="validateFormData"
    @submitEnd="submitEnd"
    @slotChange="slotChange"
    @closed="closed"
  >
    <template :slot="slotName">
      <el-tabs v-model="activeName" tab-position="left" type="card" @tab-click="tabClick">
        <el-tab-pane v-for="item in sysAlarmConfigList" :key="item.value" :name="item.name">
          <span slot="label" :title="item.name">
            <el-checkbox v-model="item.active" :true-label="1" :false-label="0" :disabled="!formable || !editable"></el-checkbox>
            <svg-icon :icon-class="item.icon" />
          </span>
          <Form
            :ref="'configForm4StgDialog' + item.value"
            :model="item"
            :rules="rules"
            label-position="right"
            :style="{'width': '100%', 'min-height': '330px'}"
            style="margin-top: 30px;"
          >
            <!-- 检测规则 -->
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>

            <i18n v-if="item.detectOption === 1" style="padding-left: 30px;" path="pages.sysAlarmConfigDetectionRule2">
              <template slot="hardwareType">{{ item.value === 4 ? enNameToLowerCase(item.name) : item.name }}</template>
              <el-input slot="minutes" v-model="item.termSampTime" type="Number" maxlength="4" min="1" max="1440" :disabled="!formable || !editable || !item.active" style="width:  60px;" @input="numberLimit(arguments[0], item, 'termSampTime', 1, 1440)"></el-input>
              <el-input slot="percent" v-model="item.threshold" type="Number" maxlength="3" min="1" max="100" :disabled="!formable || !editable || !item.active" style="width:  60px;" @input="numberLimit(arguments[0], item, 'threshold', 1, 100)"></el-input>
              <template v-if="item.threshold < 70" slot="promptMessage">
                <span style="color: red;">{{ $t('pages.sysAlarmConfigPromptMessage') }}</span>
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content">
                    <i18n path="pages.sysAlarmConfigTooltip">
                      <template slot="info">{{ item.value === 4 ? enNameToLowerCase(item.name) : item.name }}</template>
                      <br slot="br"/>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </template>
            </i18n>

            <i18n v-if="item.detectOption === 5" style="padding-left: 30px;" path="pages.sysAlarmConfigDetectionRule1">
              <template slot="hardwareType">{{ enNameToLowerCase(item.name) }}</template>
              <el-input slot="percent" v-model="item.threshold" type="Number" maxlength="3" min="1" max="100" :disabled="!formable || !editable || !item.active" style="width:  60px;" @input="numberLimit(arguments[0], item, 'threshold', 1, 100)"></el-input>
              <template v-if="item.threshold < 70" slot="promptMessage">
                <span style="color: red;">{{ $t('pages.sysAlarmConfigPromptMessage') }}</span>
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content">
                    <i18n path="pages.sysAlarmConfigTooltip">
                      <template slot="info">{{ enNameToLowerCase(item.name) }}</template>
                      <br slot="br"/>
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </template>
            </i18n>

            <template v-if="item.detectOption === 2">
              <span v-if="item.value !== 7" style="padding-left: 30px;">{{ $t('pages.sysAlarmConfigDetectionRule3', { address: item.name }) }}</span>
              <span v-else style="padding-left: 30px;">{{ $t('pages.sysAlarmConfigDetectionRule4') }}</span>
            </template>

            <i18n v-if="item.detectOption === 3" style="padding-left: 30px;" path="pages.sysAlarmConfigDetectionRule5">
              <el-input slot="time" v-model="item.threshold" type="Number" :maxlength="5" min="1" :disabled="!formable || !editable || !item.active" style="width:  70px;" @input="numberLimit(arguments[0], item, 'threshold', 1, 99999)"></el-input>
            </i18n>

            <i18n v-if="item.detectOption === 4" style="padding-left: 30px;" path="pages.sysAlarmConfigDetectionRule6">
              <el-input slot="times" v-model="item.threshold" type="Number" :maxlength="8" min="1" :disabled="!formable || !editable || !item.active" style="width:  95px;" @input="numberLimit(arguments[0], item, 'threshold', 1, 99999999)"></el-input>
            </i18n>

            <!-- 执行规则 -->
            <el-divider v-if="item.detectOption === 1 || item.detectOption === 5" content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <template v-if="item.detectOption === 1 || item.detectOption === 5">
              <i18n v-if="item.detectOption === 5" style="padding-left: 30px;" path="pages.sysAlarmConfigExecutiveRule1">
                <el-input slot="minutes" v-model="item.termSampTime" type="Number" maxlength="4" min="1" :disabled="!formable || !editable || !item.active" style="width: 70px;" @input="numberLimit(arguments[0], item, 'termSampTime', 1, 1440)"></el-input>
              </i18n>
              <div v-if="item.detectOption === 5" style="margin-bottom: 5px;"></div>
              <i18n style="padding-left: 30px;" path="pages.sysAlarmConfigExecutiveRule2">
                <el-input slot="times" v-model="item.alarmTimes" type="Number" maxlength="4" min="0" :disabled="!formable || !editable || !item.active" style="width: 70px;" @input="numberLimit(arguments[0], item, 'alarmTimes', 1, 1440)"></el-input>
              </i18n>
            </template>

            <!-- 响应规则 -->
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <ResponseContent
              :status="respondStatus"
              :show-select="true"
              :editable="formable && editable"
              read-only
              :prop-check-rule="!!item.active"
              :show-check-rule="false"
              :prop-rule-id="item.ruleId"
              style="width: 90%; padding-left: 30px;"
              @getRuleId="(ruleId) => { item.ruleId = ruleId }"
            />
          </Form>
        </el-tab-pane>
      </el-tabs>
    </template>
  </stg-dialog>
</template>

<script>
import {
  createStrategy, updateStrategy, getStrategyByName
} from '@/api/behaviorManage/application/sysAlarmConfig'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { deepMerge } from '@/utils'

export default {
  name: 'SysAlarmConfigDlg',
  components: { ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    editable: { type: Boolean, default: true }, // 能否编辑
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      slotName: undefined,
      activeName: this.$t('pages.sysDisk'),
      temp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        timeId: undefined,
        configs: [],
        remark: '',
        active: false,
        entityType: '',
        entityId: ''
      },
      sysAlarmConfigList: [],
      defaultSysAlarmConfigList: [
        { value: 1, detectOption: 5, name: this.$t('pages.sysDisk'), icon: 'hardware', title: `${this.$t('pages.computerAlarmTypeOptions1')}`, computerAlarmType: 1, termSampTime: 10, alarmTimes: 3, threshold: 90, ruleId: null, active: 0 },
        { value: 2, detectOption: 5, name: this.$t('pages.diskAll'), icon: 'disk', title: `${this.$t('pages.computerAlarmTypeOptions2')}`, computerAlarmType: 2, termSampTime: 10, alarmTimes: 3, threshold: 90, ruleId: null, active: 0 },
        { value: 3, detectOption: 1, name: this.$t('table.cpu'), icon: 'cpu', title: `${this.$t('pages.computerAlarmTypeOptions3')}`, computerAlarmType: 3, termSampTime: 10, alarmTimes: 5, threshold: 90, ruleId: null, active: 0 },
        { value: 4, detectOption: 1, name: this.$t('table.memory'), icon: 'memory', title: `${this.$t('pages.computerAlarmTypeOptions4')}`, computerAlarmType: 4, termSampTime: 10, alarmTimes: 5, threshold: 90, ruleId: null, active: 0 },
        { value: 5, detectOption: 2, name: this.$t('table.ip'), icon: 'ip', title: `${this.$t('pages.computerAlarmTypeOptions5')}`, computerAlarmType: 5, ruleId: null, active: 0 },
        { value: 6, detectOption: 2, name: this.$t('table.macAddr'), icon: 'mac-address', title: `${this.$t('pages.computerAlarmTypeOptions6')}`, computerAlarmType: 6, ruleId: null, active: 0 },
        { value: 7, detectOption: 2, name: this.$t('table.computerName'), icon: 'computerManage', title: `${this.$t('pages.computerAlarmTypeOptions7')}`, computerAlarmType: 7, ruleId: null, active: 0 },
        { value: 8, detectOption: 3, name: this.$t('pages.diskTime'), icon: 'hardware', title: `${this.$t('pages.computerAlarmTypeOptions8')}`, computerAlarmType: 8, threshold: 35000, ruleId: null, active: 0 },
        { value: 9, detectOption: 4, name: this.$t('pages.diskUsage'), icon: 'hardware', title: `${this.$t('pages.computerAlarmTypeOptions9')}`, computerAlarmType: 9, threshold: 90000, ruleId: null, active: 0 }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      respondStatus: ''
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.resetConfigs()
    },
    resetConfigs() {
      this.sysAlarmConfigList = JSON.parse(JSON.stringify(this.defaultSysAlarmConfigList))
      this.activeName = this.sysAlarmConfigList[0].name
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      if (slotTemp) Object.assign(this.temp, slotTemp)
      this.respondStatus = ''
      this.$nextTick(() => {
        this.respondStatus = 'edit'
      })
    },
    closed() {
      this.resetTemp()
    },
    tabClick(pane, event) {
    },
    handleCreate() {
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.mergeData(this.sysAlarmConfigList, row.configs)
      this.$refs['stgDlg'].show(row, this.formable)
    },
    handleShow(row, isGenerateStrategy) {
      this.mergeData(this.sysAlarmConfigList, row.configs)
      this.$refs['stgDlg'].handleShow(row, this.formable, isGenerateStrategy)
    },
    handleView(row) {
      this.mergeData(this.sysAlarmConfigList, row.configs)
      this.$refs['stgDlg'].show(row, false)
    },
    mergeData(target, sources) {
      deepMerge(target, sources)
      this.activeName = target[0].name
    },
    formatRowData(rowData) {
    },
    // 格式化数据
    formatFormData(formData) {
      formData.configs = this.sysAlarmConfigList
      delete formData.isPermanent
      delete formData.beginTime
      delete formData.endTime
    },
    validateFormData(formData) {
      const canSubmit = !this.sysAlarmConfigList.some(config => {
        const hasNotRuleId = config.active == 1 && !config.ruleId
        if (hasNotRuleId) {
          this.activeName = config.name
        }
        return hasNotRuleId
      })
      return canSubmit
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    numberLimit(value, item, type, min, max) {
      if (type === 'alarmTimes') {
        max = parseInt(1440 / item.termSampTime)
        item.alarmTimes = !value ? min : value > max ? max : value < min ? min : parseInt(value)
      } else if (type === 'termSampTime') {
        item.termSampTime = value > max ? max : value < min ? min : parseInt(value)
        const alarmMaxTimes = parseInt(1440 / item.termSampTime)
        item.alarmTimes = item.alarmTimes > alarmMaxTimes ? alarmMaxTimes : item.alarmTimes < 0 ? 0 : parseInt(item.alarmTimes)
      } else {
        if (max) {
          item.threshold = value > max ? max : value < min ? min : parseInt(value)
        } else {
          item.threshold = value < min ? min : parseInt(value)
        }
      }
    },
    enNameToLowerCase(name) {
      return this.isEnglish() && name ? name.toLowerCase() : name
    }
  }
}
</script>
<style lang="scss" scoped>
  .el-tab-pane{
    padding: 0 20px;
    overflow-wrap: break-word;
  }
  .el-divider--horizontal{
    margin: 19px 0;
    .el-divider__text{
      background-color: #e4e7e9;
      font-weight: 800;
    }
  }
  >>>.el-input__inner,
  >>>.el-textarea__inner{
    padding: 5px;
  }
</style>
