<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="gridTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="strategySelectionChangeEnd"
      />
    </div>
    <sys-alarm-config-dlg
      ref="sysAlarmConfigDlg"
      :formable="formable"
      :editable="editable"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    />
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importSysAlarmConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import { getStrategyPage, deleteStrategy } from '@/api/behaviorManage/application/sysAlarmConfig'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeInfoFormatter } from '@/utils/formatter'
import SysAlarmConfigDlg from './editDlg'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'SysAlarmConfig',
  components: { SysAlarmConfigDlg, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 163,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTime', width: '100', formatter: timeInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', type: 'button', width: '200',
          buttons: [
            { formatter: this.formatStrateMsg, click: this.handleView }
          ]
        },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        {
          prop: 'createdTime',
          label: 'operate',
          type: 'button',
          fixed: 'right',
          fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      options: [
        { value: 1, label: this.$t('pages.allDay') },
        { value: 2, label: this.$t('pages.morning') }
      ],
      computerAlarmTypeOptions: {
        1: this.$t('pages.computerAlarmTypeOptions1'),
        2: this.$t('pages.computerAlarmTypeOptions2'),
        3: this.$t('pages.computerAlarmTypeOptions3'),
        4: this.$t('pages.computerAlarmTypeOptions4'),
        5: this.$t('pages.computerAlarmTypeOptions5'),
        6: this.$t('pages.computerAlarmTypeOptions6'),
        7: this.$t('pages.computerAlarmTypeOptions7'),
        8: this.$t('pages.computerAlarmTypeOptions8'),
        9: this.$t('pages.computerAlarmTypeOptions9')
      },
      query: {
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      ipPortEditable: false,
      ipPortDeleteable: false,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        configs: [],
        timeId: 1,
        timeName: '',   // 时间段名称，记录管理员日志用的
        entityType: undefined,
        entityId: undefined,
        ruleId: ''
      },
      tempItem: {},
      defaultTempItem: { // 表单字段
        id: undefined,
        funcId: 1,
        limit: 1,
        timeId: 1
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.sysBaseConfigStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.sysBaseConfigStg'), 'create')
      },
      itemRowData: [],
      submitting: false,
      propCheckRule: false,
      propRuleId: undefined,
      funcOptions: [],
      checkedKeys: [],
      checkedEntityNode: {},
      editable: true
    }
  },
  computed: {
    gridTable() {
      return this.$refs['gridTable']
    },
    funcOptionsMap() {
      const map = {}
      this.funcOptions.forEach(item => {
        map[item.value] = item.label
      })
      return map
    },
    timeInfoOptionMap() {
      const map = {}
      this.$store.getters.timeOptions.forEach(item => {
        map[item.value] = item.label
      })
      return map
    }
  },
  watch: {
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },

  methods: {
    handleCheckChange(keys, datas) {
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    ipPortTable: function() {
      return this.$refs['ipPortList']
    },
    formatStrateMsg: function(row) {
      const activeConfigs = row.configs.filter(config => config.active).map(config => this.computerAlarmTypeOptions[config.computerAlarmType])
      return `${this.$t('text.open')}：${activeConfigs.join('、')}`
    },
    handleView: function(row, data) {
      this.editable = false
      this.$refs['sysAlarmConfigDlg'].handleView(row)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    itemRowDataApi: function(option) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve({
            code: 20000,
            data: {
              items: this.temp.whiteList,
              total: 1
            }
          })
        }, 1)
      })
    },
    strategySelectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.itemRowData.splice(0, this.itemRowData.length)
      this.temp = Object.assign({}, this.defaultTemp)
      this.tempItem = Object.assign({}, this.defaultTempItem)
      this.propCheckRule = false
      this.propRuleId = undefined
    },
    resetTempItem() {
      this.tempItem = Object.assign({}, this.defaultTempItem)
    },
    handleCreate() {
      this.editable = true
      this.$refs['sysAlarmConfigDlg'].handleCreate()
    },
    handleUpdate: function(row) {
      this.editable = true
      this.$refs['sysAlarmConfigDlg'].handleUpdate(row)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    submitEnd() {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    handleRefresh() {
      return refreshPage(this)
    },
    funcIdFormatter: function(row, data) {
      return this.funcOptionsMap[row.funcId]
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    importSuccess() {
      this.handleFilter()
    }
  }
}
</script>
