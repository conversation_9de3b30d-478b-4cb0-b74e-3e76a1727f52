<template xmlns:el="http://www.w3.org/1999/html">
  <span>
    <el-badge :value="softNum" :max="99" :hidden="softNum===0" class="item">
      <el-button size="mini" @click="show">{{ $t('pages.terminalReport') }}</el-button>
    </el-badge>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.terminalReport')"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <FormItem :label="$t('pages.className')">
              <tree-select :data="typeTreeData" node-key="dataId" size="mini" :checked-keys="[1]" @change="parentIdChange" />
            </FormItem>
          </el-col>
          <el-col :span="16">
            <el-button size="mini" @click="refreshTable">
              {{ $t('button.refresh') }}
            </el-button>
            <el-button size="mini" :disabled="!deleteable" @click="approvalSoft">
              {{ $t('button.insert') }}
            </el-button>
            <el-button size="mini" :disabled="!deleteable" @click="deleteSoft">
              {{ $t('button.delete') }}
            </el-button>
          </el-col>
        </el-row>
        <div style="height: 400px">
          <grid-table
            ref="approvalSoftList"
            :show-pager="false"
            :col-model="colModel"
            :row-data-api="rowDataApi"
            @selectionChangeEnd="selectionChangeEnd"
          />
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
  </span>
</template>
<script>
import { findApprovalSoftList, getTypeTree, approvalSoft, deleteApprovalSoft, getApprovalSoftCount } from '@/api/behaviorManage/application/install'

export default {
  name: 'ApproveSoft',
  data() {
    return {
      deleteable: false,
      dialogFormVisible: false,
      colModel: [
        { prop: 'terminalName', label: 'reportTerminal', width: '10' },
        { prop: 'processName', label: 'programName', width: '10' },
        { prop: 'signature', label: 'softSign', width: '10' },
        { prop: 'productName', label: 'productName', width: '10' },
        { prop: 'reason', label: 'reason1', width: '10' },
        { prop: 'typeName', label: 'programType', width: '10' }
      ],
      softNum: 0,
      typeTreeData: [],
      type: 1
    }
  },
  created() {
    this.loadTypeTree()
  },
  beforeDestroy() {
    this.stopSearch()
  },
  methods: {
    starSearch: function() {
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        getApprovalSoftCount().then(respond => {
          this.softNum = respond.data
        })
      }, 2000)
    },
    stopSearch: function() {
      clearInterval(this.timer)
    },
    handleDrag: function() {

    },
    refreshTable: function() {
      this.$refs.approvalSoftList.execRowDataApi()
    },
    deleteSoft: function() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs.approvalSoftList.getSelectedIds()
        deleteApprovalSoft({ ids: toDeleteIds.join(',') }).then(respond => {
          this.refreshTable()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      })
    },
    approvalSoft: function() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg1'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs.approvalSoftList.getSelectedIds()
        approvalSoft({ ids: toDeleteIds.join(',') }).then(respond => {
          this.refreshTable()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.insertSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      })
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    rowDataApi: function(option) {
      if (option != null && option != undefined) {
        option.type = this.type
      }
      return findApprovalSoftList(option)
    },
    show: function() {
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.refreshTable()
      })
    },
    loadTypeTree: function() {
      getTypeTree().then(respond => {
        this.typeTreeData = respond.data
      })
    },
    parentIdChange(data) {
      this.type = data
      this.refreshTable()
    }
  }
}
</script>
