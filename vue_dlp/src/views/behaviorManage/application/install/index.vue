<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="() => { showTree = !showTree }">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgType"/>
        <common-downloader
          v-permission="'160'"
          :disabled="!deleteable"
          :name="title + '.xlsx'"
          :button-name="$t('button.export')"
          button-size="mini"
          @download="execExport"
        />
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <el-tabs ref="tabs" v-model="activeTabName" type="card" style="height: calc( 100% - 40px);" :before-leave="changeTab" @tab-click="tabClick" >
        <el-tab-pane :label="$t('pages.installLimit')" name="InstallPacketStrategy">
          <Install-packet
            ref="InstallPacketStrategy"
            :parent-vm="this"
            :load-table-end="afterLoad"
            :select-table-end="afterSelect"
            :entity-click="entityClick"
            :button-formatter="buttonFormatter"
            :limit-type="1"
            @handleCreate="handleStgCreate"
            @handleUpdate="handleStgUpdate"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.uninstallLimit')" name="uninstallPacketStrategy">
          <Install-packet
            ref="uninstallPacketStrategy"
            :parent-vm="this"
            :load-table-end="afterLoad"
            :select-table-end="afterSelect"
            :entity-click="entityClick"
            :button-formatter="buttonFormatter"
            :limit-type="2"
            @handleCreate="handleStgCreate"
            @handleUpdate="handleStgUpdate"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.specialRelease')" name="SpecialPathStrategy">
          <special-path
            ref="SpecialPathStrategy"
            :parent-vm="this"
            :load-table-end="afterLoad"
            :select-table-end="afterSelect"
            :entity-click="entityClick"
            :button-formatter="buttonFormatter"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <install-packet-dlg
      ref="InstallPacketStrategyDlg"
      :default-limit-type="limitType"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    />
    <install-packet-dlg
      ref="uninstallPacketStrategyDlg"
      :default-limit-type="limitType"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    />
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="titleMap[activeTabName]"
      data-type="-1"
      :auto-name="true"
      :term-able="treeable"
      :user-able="treeable"
      :strategy-type-number="strategyTypeNumber[activeTabName]"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import { exportStg } from '@/api/stgCommon'
import { enableStgBtn, enableStgDelete, refreshPage, entityLink, buttonFormatter } from '@/utils'
import InstallPacket from '@/views/behaviorManage/application/install/installPacket'
import SpecialPath from '@/views/behaviorManage/application/install/specialPath'
import InstallPacketDlg from './installPacket/editDlg'
import ImportStg from '@/views/common/importStg'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'InstallPackage',
  components: { InstallPacketDlg, InstallPacket, SpecialPath, ImportStg, CommonDownloader },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      showTree: true,
      treeable: true,
      activeTabName: 'InstallPacketStrategy',
      addBtnAble: false,
      deleteable: false,
      selectedNode: {},
      query: { // 查询条件
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      checkedEntityNode: {},
      limitType: 1,
      titleMap: {
        InstallPacketStrategy: this.$t('pages.importInstallStg'),
        uninstallPacketStrategy: this.$t('pages.importUninstallStg'),
        SpecialPathStrategy: this.$t('pages.importSpecialPathStg')
      },
      strategyTypeNumber: {
        InstallPacketStrategy: 9,
        uninstallPacketStrategy: 10,
        SpecialPathStrategy: 11
      },
      relNameStgType: {
        'InstallPacketStrategy': 9,
        'uninstallPacketStrategy': 10,
        'SpecialPathStrategy': 11
      },
      stgType: 9
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.activeTabName = to.query.tabName || vm.activeTabName
      vm.$router.push({ query: {}})
    })
  },
  computed: {
    title() {
      return this.activeTabName === 'InstallPacketStrategy' ? this.$t('pages.limitType1')
        : this.activeTabName === 'uninstallPacketStrategy' ? this.$t('pages.limitType2')
          : this.activeTabName === 'SpecialPathStrategy' ? this.$t('pages.specialRelease') : ''
    }
  },
  watch: {
    activeTabName(val) {
      this.limitType = { InstallPacketStrategy: 1, uninstallPacketStrategy: 2 }[val] || this.limitType
      this.stgType = this.relNameStgType[val] || this.stgType
    }

  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
  },
  methods: {
    getTab() {
      return this.$refs[this.activeTabName]
    },
    tabClick(pane, event) {
      this.query.searchInfo = ''
      this.deleteable = false
      this.handleFilter()
    },
    changeTab(activeTabName, oldActiveTabName) {
      // this.handleFilter()
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.addBtnAble = checkedNode
      this.selectedNode = this.addBtnAble ? checkedNode.data : {}
      if (this.addBtnAble) {
        this.handleFilter()
      }
    },
    refresh() {
      refreshPage(this)
      this.handleFilter()
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    afterSelect(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    entityClick(row, data) {
      return entityLink(row, data, this)
    },
    buttonFormatter(row) {
      return buttonFormatter(row, this)
    },
    handleFilter() {
      this.query.page = 1
      this.getTab().handleFilter(this.query)
    },
    handleCreate() {
      this.getTab().handleCreate()
    },
    handleStgCreate() {
      this.$refs[this.activeTabName + 'Dlg'].handleCreate()
    },
    handleStgUpdate(row) {
      this.$refs[this.activeTabName + 'Dlg'].handleUpdate(row)
    },
    handleDelete() {
      this.getTab().handleDelete()
    },
    submitEnd(dlgStatus) {
      this.getTab().submitEnd(dlgStatus)
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.getTab().gridTable.getSelectedIds()
      let stgType = null
      switch (this.activeTabName) {
        case 'InstallPacketStrategy': stgType = 9; break;
        case 'uninstallPacketStrategy' : stgType = 10; break;
        case 'SpecialPathStrategy' : stgType = 11; break;
      }
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: stgType })
    },
    importSuccess() {
      this.handleFilter()
    },
    execExport(file) {
      const stgIds = this.getTab() && this.getTab().gridTable && this.getTab().gridTable.getSelectedIds()
      const opts = { file, jwt: true, topic: this.$route.name }
      this.getTab().handleExport({ strategyIds: stgIds.join(',') }, opts)
    }
  }
}
</script>
