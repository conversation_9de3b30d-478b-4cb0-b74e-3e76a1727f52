<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dlgVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div v-if="formable">
        <el-button v-if="showByOsType" type="primary" size="mini" @click="handleAppCreate(true)">
          {{ $t('button.insert') }}
        </el-button>
        <el-button type="primary" size="mini" @click="handleApp">
          {{ $t('text.infoImport', { info :$t('pages.installApp') }) }}
        </el-button>
        <el-button type="primary" size="mini" :disabled="!checkAppDeleteable" @click="handleDeleteCheckedApp">
          {{ $t('button.delete') }}
        </el-button>
        <div style="float: right;">
          <span style="color:red">{{ $t('pages.install_Msg29') }}</span>
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('table.processName')" style="width: 165px;" @keyup.enter.native="handleAppFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleAppFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="checkedAppGrid"
        :show-pager="showPager"
        :height="height"
        :multi-select="formable"
        :col-model="checkedColModel"
        :row-data-api="getPkgRows"
        :default-sort="{ prop: 'processName' }"
        @selectionChangeEnd="checkAppSelectionChangeEnd"
      />
    </el-dialog>
    <app-import-table
      ref="appImportTable"
      :os-type="osType==7?1:osType"
      :add-group-btn="false"
      :disable-product-md5="false"
      :type-tree-data="typeTreeData"
      :group-root-name="$t('pages.installApp')"
      :list="getPackegeList"
      :count-by-group="countChildByGroupId"
      :create="createSoftPacket"
      :batch-create="batchCreateSoftPacket"
      :update="updateSoftPacket"
      :delete="deleteSoftPacket"
      :import-func="importFromLib"
      :create-group="createType"
      :update-group="updateType"
      :delete-group="deleteType"
      :show-by-os-type="showByOsType"
      :support-md5="supportMd5"
      :get-group-by-name="getTypeByName"
      :install-not-repeat-addition="false"
      support-select-terminal-soft
      @submitEnd="appMd5ImportSubmitEnd"
    />
  </div>
</template>

<script>
import {
  getTreeNode, getPackegeList, importFromLib,
  createType, updateType, deleteType, getTypeByName, batchCreateSoftPacket,
  createSoftPacket, updateSoftPacket, deleteSoftPacket, countChildByGroupId,
  getSpecialPage, deleteSpecial, addSpecial
} from '@/api/system/baseData/softPacket'
import AppImportTable from '@/views/system/baseData/appLibrary/appImportTableDlg'

export default {
  name: 'SpecialPkgDlg',
  components: { AppImportTable },
  props: {
    osType: { type: Number, default: 1 }, // 进程系统类型：1-windows，2-linux，4-mac
    showByOsType: { type: Boolean, default: true },
    supportMd5: { // 是否支持指纹防伪冒（某些功能终端还不支持，所以加个开关）
      type: Boolean,
      default: true
    },
    formable: { type: Boolean, default: true }, // 能否提交表单
    func: { // 删除、修改类别，删除、修改程序时，执行的操作
      type: Function,
      default: function() {
      }
    },
    title: {
      type: String,
      default: function() {
        return this.$t('pages.install_Msg30')
      }
    },
    height: { type: Number, default: 400 },
    showPager: { type: Boolean, default: true }
  },
  data() {
    return {
      dlgVisible: false,
      limitType: 1,  // 1安装包，2卸载包
      checkedColModel: [
        { prop: 'processName', label: 'processName', width: '150', formatter: this.nameFormatter },
        { prop: 'checkMd5', label: 'isCheckMd5', hidden: () => !this.supportMd5, width: '150', formatter: this.md5LevelFormatter }
      ],
      query: {
        page: 1,
        searchInfo: '',
        typeId: this.limitType,
        osType: this.osType
      },
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      },
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      checkAppDeleteable: false,
      typeTreeData: []
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    /* this.loadAppTypeTree() */
  },
  activated() {
  },
  methods: {
    createType,
    updateType,
    deleteType,
    getPackegeList,
    createSoftPacket,
    batchCreateSoftPacket,
    updateSoftPacket,
    deleteSoftPacket,
    countChildByGroupId,
    importFromLib,
    getTypeByName,
    checkedAppGrid() {
      return this.$refs['checkedAppGrid']
    },
    show(limitType) {
      this.dlgVisible = true
      this.limitType = limitType
      this.query.typeId = this.limitType
      this.reloadGrid()
    },
    reloadGrid() {  // 刷新选中的数据
      this.checkedAppGrid() && this.checkedAppGrid().execRowDataApi()
    },
    checkAppSelectionChangeEnd(rowDatas) {
      this.checkAppDeleteable = rowDatas && rowDatas.length > 0
    },
    loadAppTypeTree: function() {
      getTreeNode({ osType: this.osType == 7 ? 1 : this.osType }).then(respond => {
        this.typeTreeData = respond.data
      })
    },
    getPkgRows(option) {
      this.query.osType = this.osType
      const searchQuery = Object.assign({}, this.query, option)
      return getSpecialPage(searchQuery)
    },
    handleAppFilter() {
      this.query.page = 1
      this.checkedAppGrid().execRowDataApi(this.query)
    },
    handleDeleteCheckedApp() {
      this.$confirmBox(this.$t('pages.install_Msg28'), this.$t('text.prompt')).then(() => {
        const rows = this.checkedAppGrid().getSelectedDatas()
        this.appMd5ImportSubmitEnd(rows, 0)
      }).catch(() => {})
    },
    handleApp() {
      this.loadAppTypeTree()
      this.$refs.appImportTable.show()
    },
    handleAppCreate(flag) {
      this.loadAppTypeTree()
      this.$refs.appImportTable.showBatchCreate(null, true)
    },
    async getAllSpecial(limitType) {
      const datas = []
      await getSpecialPage({
        page: 1,
        limit: 10000,
        sortName: 'id',
        sortOrder: 'desc',
        typeId: limitType,
        osType: this.osType
      }).then(resp => {
        if (resp.data) {
          resp.data.items.forEach(item => datas.push(this.formatRowData(item)))
        }
      })
      return datas
    },
    formatRowData(data) {
      return {
        id: data.id,
        md5Level: data.md5Level,
        isCheckMd5: data.checkMd5,
        processName: data.processName,
        productVersion: data.productVersion,
        itemType: 1,  // 记录类型：1应用程序2类别
        typeId: data.typeId
      }
    },
    appMd5ImportSubmitEnd(data, editMode) {
      // editMode编辑模式，0-删除，1-添加程序，2-添加类别
      if (editMode === 0) {
        const ids = data.map(item => item.id)
        const processNames = data.map(item => item.processName)
        deleteSpecial({ bizType: this.limitType, ids: ids.join(','), processNames }).then(resp => {
          this.reloadGrid()
        })
      } else if (editMode === 3) {
        this.reloadGrid()
      } else {
        const formData = data.map(item => { return { bizType: this.limitType, id: item.id, osType: this.osType } })
        addSpecial(formData).then(resp => {
          this.reloadGrid()
        })
      }
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    md5LevelFormatter(row, data) {
      return this.$refs.appImportTable.md5LevelFormatter(row, data)
    },
    nameFormatter(row, data) {
      let msg = ''
      if (row.itemType == 2) {
        this.typeTreeData.some(node => {
          if (node.dataId == row.id) {
            msg = node.label
            return true
          }
        })
      } else {
        msg = row.processName
      }
      return msg
    },
    itemTypeFormatter(row, data) {
      return this.itemTypeMap[data]
    }
  }
}
</script>
