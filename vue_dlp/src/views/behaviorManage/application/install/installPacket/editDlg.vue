<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.limitStrategy', { info: keyTitle })"
      :stg-code="stgCode"
      :pane-height="600"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createData"
      :update="updateData"
      :get-by-name="getByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateForm"
      time-able
      os-label-w="110px"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <div :style="{ 'margin-left': limitType === 1 ? '0' : '15px' }">
          <el-divider content-position="left">{{ $t('pages.detectionRules') }}
            <el-tooltip v-if="osType != 1" effect="dark" placement="bottom-start">
              <div slot="content">{{ $t('pages.install_Msg46') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-divider>
          <LocalZoomIn parent-tag="el-dialog">
            <div v-if="formable">
              <el-button v-if="showByOsType" size="small" @click="handleAppCreate">
                {{ $t('button.insert') }}
              </el-button>
              <el-button size="small" @click="handleAppImport">
                {{ $t('text.infoImport', { info :$t('pages.installApp') }) }}
              </el-button>
              <el-button size="small" :disabled="!checkAppDeleteable" @click="handleDeleteApp">
                {{ $t('button.delete') }}
              </el-button>
              <el-button type="primary" icon="el-icon-search" size="small" :title="$t('table.search')" style="margin-left: 1px;height: 26px;float: right;" @click="handleFilter"/>
              <el-input v-model="searchInfo" v-trim clearable :placeholder="$t('pages.inputExeName')" style="width: 200px;height: 26px;float: right;" @keyup.enter.native="handleFilter"></el-input>
            </div>
            <grid-table
              ref="checkedAppGrid"
              :show-pager="false"
              auto-height
              :multi-select="formable"
              :col-model="getColModule()"
              :row-datas="filterPacketList"
              default-expand-all
              :selectable="selectable"
              @selectionChangeEnd="checkAppSelectEnd"
            />
          </LocalZoomIn>
          <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
          <FormItem v-if="showFilterSoftmgr" label-width="0">
            <el-checkbox v-model="filterSoftmgr" :disabled="!formable" class="prohibit" :true-label="1" :false-label="0">{{ $t('pages.filterSoftmgr') }}</el-checkbox>
          </FormItem>

          <FormItem label-width="0">
            <el-radio-group v-model="temp.installLimitType" :disabled="!formable" @change="installLimitTypeChange">
              <el-row >
                <el-col :span="24">
                  <el-radio :label="limitType===1?2:5" style="margin-right: 0">{{ $t('pages.install_Msg_1', { info: keyTitle }) }}</el-radio>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-radio :label="limitType===1?3:6" style="margin-top:4px; margin-right: 0">{{ $t('pages.install_Msg2_1', { info: keyTitle }) }}</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </FormItem>
          <el-row v-if="[3, 6].indexOf(temp.installLimitType) > -1">
            <el-col :span="24">
              <label style="font-weight: 500;color: #409eff;font-size: small;color:red;">{{ $t('pages.install_Msg3', { info: keyTitle }) }}</label>
              <link-button btn-class="editBtn" formable always-show click-func="show" btn-style="float: none;" @show="showSpecialDlg"/>
            </el-col>
          </el-row>
          <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.install_Msg6', { info: keyTitle }) }}</label>
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.install_Msg9">
                <template slot="info">{{ keyTitle }}</template>
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>

          <FormItem label-width="0" prop="action">
            <p v-show="validMsg.length > 0" style="margin: 0 16px;"> <span style="color:red;">{{ validMsg }}</span></p>
            <div class="selectionCheckBox">
              <el-checkbox v-model="temp.report" class="prohibit" :disabled="!formable" :true-label="1" :false-label="0">{{ $t('pages.install_Msg50', { package: keyTitle }) }}</el-checkbox>
              <el-checkbox-group v-model="temp.executeCode" :disabled="!formable">
                <el-checkbox class="prohibit" :label="[2, 5].includes(temp.installLimitType) ? 1 : 3">{{ $t('pages.prohibit') }}{{ keyTitle }}</el-checkbox>
              </el-checkbox-group>
            </div>
            <ResponseContent
              style="line-height: 15px !important;"
              :select-style="{ 'margin-top': 0 }"
              :show-select="true"
              :editable="formable"
              read-only
              :prop-check-rule="isDoResponse(temp)"
              :show-check-rule="true"
              :prop-rule-id="temp.ruleId"
              @getRuleId="getRuleId"
              @ruleIsCheck="ruleIsCheck"
              @validate="(val) => { responseValidate = val }"
            />
          </FormItem>
        </div>
      </template>
    </stg-dialog>
    <special-pkg-dlg ref="specialPkgDlg" :title="$t('pages.packageList1', { package: keyTitle })" :show-by-os-type="showByOsType" :support-md5="supportMd5" :os-type="osType" :formable="formable"/>
    <app-add-dlg
      ref="appAddDlg"
      :os-type="osType==7?1:osType"
      :disable-product-md5="false"
      :support-md5="supportMd5"
      :append-to-body="false"
      :type-tree-data="appTypeTreeData || typeTreeData"
      :create="createSoftPacket"
      :update="updateSoftPacket"
      :create-group="createType"
      :update-group="updateType"
      :get-group-by-name="getTypeByName"
      :install-not-repeat-addition="false"
      @submitEnd="appAddSubmitEnd"
    />
    <app-import-table
      ref="appImportTable"
      :os-type="osType==7?1:osType"
      :disable-product-md5="false"
      :type-tree-data="typeTreeData"
      :group-root-name="$t('pages.installApp')"
      :list="getPackegeList"
      :count-by-group="countChildByGroupId"
      :create="createSoftPacket"
      :batch-create="batchCreateSoftPacket"
      :batch-update="batchUpdateSoftPacket"
      :update="updateSoftPacket"
      :delete="deleteSoftPacket"
      :import-func="importFromLib"
      :create-group="createType"
      :update-group="updateType"
      :delete-group="deleteType"
      :get-group-by-name="getTypeByName"
      :show-by-os-type="showByOsType"
      :support-md5="supportMd5"
      :delete-col-model="deleteColModel"
      :get-relation-stg-by-ids="getStrategyBySoftProcessIds"
      :install-not-repeat-addition="false"
      support-select-terminal-soft
      @submitEnd="appMd5ImportSubmitEnd"
      @updateGroup="updateInstallAppLog"
      @refreshList="refreshList"
    >
      <template #extra>
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'C28'" :link-url="'/behaviorAuditing/systemLog/softLimitLog'" :btn-text="$t('route.softLimitLog')"/>
      </template>
      <template slot="export" slot-scope="scope">
        <el-button icon="el-icon-download" size="mini" @click="handleExport( scope.selectedIds, scope.query)">
          {{ $t('button.export') }}
        </el-button>
      </template>
    </app-import-table>

    <export-dlg ref="exportDlg" :title="this.$t('pages.installApp')" :group-tree-data="[{ id: 'G0', dataId: '0', label: this.$t('pages.installApp'), parentId: '', children: [...typeTreeData] }]" :export-func="exportFunc" :group-tree-id="query.typeId"/>
  </div>
</template>

<script>
import { createData, getByName, updateData, getById } from '@/api/behaviorManage/application/install'
import {
  batchCreateSoftPacket, countChildByGroupId, createSoftPacket, createType, deleteSoftPacket,
  deleteType, getPackegeList, getStrategyBySoftProcessIds, getTypeTreeNodeMap,
  getTypeByName, importFromLib, updateSoftPacket, updateType, exportSoftPacket, batchUpdateSoftPacket
} from '@/api/system/baseData/softPacket'
import SpecialPkgDlg from './specialPkgDlg'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import AppImportTable from '@/views/system/baseData/appLibrary/appImportTableDlg'
import AppAddDlg from '@/views/system/baseData/appLibrary/appAddDlg.vue'
import ExportDlg from '@/views/common/export'

export default {
  name: 'InstallPacketDlg',
  components: { AppImportTable, SpecialPkgDlg, ResponseContent, AppAddDlg, ExportDlg },
  props: {
    defaultLimitType: { type: Number, default: 1 },
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      // 进程系统类型：1-windows，2-linux，4-mac
      osType: 1,
      submitting: false,
      slotName: undefined,
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      },
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      supportMd5: true,
      showByOsType: true,
      limitType: 1,
      temp: {},
      packetList: [],
      packetListMap: {},
      filterPacketList: [],
      nowPackageList: [],
      nowPackageListChange: 0,
      appTypeTreeData: [],
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        active: false,
        remark: '',
        installLimitType: null,
        packetList: [],
        entityType: '',
        entityId: null,
        ruleId: null,
        action: 0,
        report: 0,
        executeCode: []
      },
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
        // action: [{ required: true, validator: this.actionValidator, trigger: 'blur' }]
      },
      dialogStatus: '',
      propRuleId: undefined,
      typeTreeDataWindow: [],
      typeTreeDataLinux: [],
      typeTreeDataMac: [],
      specialPkgs: [],
      timer: null,
      checkAppDeleteable: false,
      deleteColModel: [
        { prop: 'processName', label: 'processName', width: '100', sort: true },
        { prop: 'strategyRelations', label: 'relationStgName', width: '100', formatter: this.strategyRationFormatter }
      ],
      responseValidate: true,
      searchInfo: '',
      validMsg: '',
      filterSoftmgr: 0,
      query: {}
    }
  },
  computed: {
    showFilterSoftmgr() {
      return this.defaultLimitType == 1 && this.osType == 1
    },
    typeTreeData() {
      if (this.osType == 2) {
        return this.typeTreeDataLinux
      } else if (this.osType == 4) {
        return this.typeTreeDataMac
      } else {
        return this.typeTreeDataWindow
      }
    },
    // 所有程序的map，以分类的id作为key
    nowPackageListMap() {
      const map = {}
      this.nowPackageList.forEach(data => {
        const typeId = data.typeId
        map[typeId] ? map[typeId].push(data) : map[typeId] = [data]
      })
      return map
    },
    keyTitle() {
      return this.limitType === 1 ? this.$t('pages.install') : this.$t('pages.uninstall')
    },
    stgCode() {
      return this.limitType === 1 ? 9 : 10
    }
  },
  watch: {
    defaultLimitType() {
      this.limitType = this.defaultLimitType
    },
    'temp.packetList': function() {
      this.formatDataTree()
    },
    '$store.state.commonData.notice.installAppLibGroup'(val) {
      this.loadAppTypeTree()
    },
    nowPackageListChange() {
      this.formatDataTree()
    }
  },
  created() {
    this.loadAppTypeTree()
    this.resetTemp()
    this.getNowPackageList()
    this.limitType = this.defaultLimitType
    this.handleFilter()
  },
  activated() {
  },
  methods: {
    getStrategyBySoftProcessIds,
    createType,
    updateType,
    deleteType,
    getPackegeList,
    createSoftPacket,
    batchCreateSoftPacket,
    updateSoftPacket,
    deleteSoftPacket,
    countChildByGroupId,
    importFromLib,
    getTypeByName,
    batchUpdateSoftPacket,
    createData(data) {
      this.setFilterSoftmgr(data)
      return createData(data, this.limitType)
    },
    updateData(data) {
      this.setFilterSoftmgr(data)
      return updateData(data, this.limitType)
    },
    getByName(data) {
      return getByName(data, this.limitType)
    },
    setFilterSoftmgr(data) {
      if (!this.showFilterSoftmgr) {
        return
      }
      if (Array.isArray(data)) {
        data.forEach(item => {
          item.filterSoftmgr = this.filterSoftmgr
        })
      } else {
        data.filterSoftmgr = this.filterSoftmgr
      }
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.validMsg = ''
      this.filterSoftmgr = 0
    },
    closed() {
      this.searchInfo = ''
      this.resetTemp()
    },
    // 是否禁止
    isDoLimit(data) {
      const executeCode = data.executeCode
      return executeCode.includes(1) || executeCode.includes(3)
    },
    refreshList() {
      this.getNowPackageList()
    },
    // 模糊查询
    handleFilter() {
      if (this.searchInfo.trim() == '') {
        // 空格字符或者空搜索，还原数据
        this.filterPacketList = this.packetList
        return this.packetList
      }
      const searchInfo = this.searchInfo.toLowerCase()
      const targetTypeIdMap = {}
      this.nowPackageList.forEach(item => {
        if (item.processName.toLowerCase().includes(searchInfo)) {
          if (!targetTypeIdMap[item.typeId]) {
            targetTypeIdMap[item.typeId] = [item.processName]
          } else if (targetTypeIdMap[item.typeId].indexOf(item.processName) < 0) {
            targetTypeIdMap[item.typeId].push(item.processName)
          }
        }
      })
      const tempPacketList = JSON.parse(JSON.stringify(this.packetList))
      // 表格数据分为两层：第一层为程序所属的分组，第二层才是具体的数据
      const packetList = tempPacketList.filter(item => {
        if (item.itemType == 1) {
          return item.name.toLowerCase().includes(searchInfo)
        } else if (targetTypeIdMap[item.typeId]) {
          item.childrenNames = targetTypeIdMap[item.typeId]
          return true
        }
        return false
      })
      this.filterPacketList = packetList
    },
    // 是否触发响应规则
    isDoResponse(data) {
      const executeCode = data.executeCode
      return executeCode.includes(2) || executeCode.includes(4)
    },
    async handleUpdateProcess(row) {
      const prg = this.nowPackageList.find(v => v.id == row.dataId)
      if (prg) {
        this.appTypeTreeData = this.$refs.appImportTable.appTypeTreeData[0].children
        this.$refs.appAddDlg.show(prg)
      }
    },
    async handleViewProcess(row) {
      if (this.$refs.appImportTable) {
        const map = this.getProcessListId(this.packetList)
        const typeIds = map.get('type') ? map.get('type').map(id => `G${id}`) : null
        const processIds = map.get('process') || null
        this.$refs.appImportTable.show(typeIds, processIds, row.typeId);
      }
    },
    selectable(row) {
      // return row.source == 1 || row.children
      return true
    },
    // 对现有的程序列表重新整理
    formatDataTree() {
      this.packetList.splice(0)
      const list = this.temp.packetList

      // 遍历已选择的分类、应用程序，并将分类数据添加到 this.packetList
      for (const i in list) {
        const data = list[i]
        // itemType：1.应用程序 2.类别
        const { itemType, id } = data
        // typeId 归属分类的id
        const typeId = itemType == 2 ? id : data.typeId
        if (itemType == 2) {
          const typeData = {
            id: typeId,
            typeId,
            name: typeId, // 通过程序添加的分类，使用 typeId 代替 name
            typeName: data.typeName,
            itemType: 2,
            fromType: true
          }
          this.packetList.push(typeData)
        } else {
          // 应用程序
          const app = this.formatAppData(data, 1)
          this.packetList.push(app)
        }
      }
    },
    // 对程序的数据进行处理， source：程序来源，1. 主动添加 2. 通过分类导入
    formatAppData(data, source) {
      const app = JSON.parse(JSON.stringify(data))
      app.source = source
      app.dataId = app.id
      app.id = `${app.id}${Math.random() * 1000000}`
      app.name = app.processName
      app.itemType = 1
      return app
    },
    async getNowPackageList() {
      const resp = await getPackegeList({
        page: 1
      })
      this.nowPackageList = resp.data.items
      this.nowPackageListChange++
    },
    slotChange(name, slotTemp) {
      this.osType = name
      this.slotName = name
      this.supportMd5 = true
      this.showByOsType = !(name == 2 || name == 4)
      if (slotTemp) {
        slotTemp.packetList = (slotTemp.packetList || []).filter(packet => ('itemType' in packet) && packet.itemType != null)
        this.temp = slotTemp
      } else {
        this.temp = {}
      }
    },
    getColModule() {
      return [
        { prop: 'typeId', label: 'typeId', width: '80', sort: true, formatter: this.typeIdFormatter },
        { prop: 'name', label: 'processName', width: '150', sort: true, formatter: this.nameFormatter },
        { prop: 'isCheckMd5', label: 'checkMd5', hidden: () => !this.supportMd5, width: '80', formatter: this.md5LevelFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.formable,
          buttons: [
            { label: 'edit', isShow: (row) => { return row.itemType == 1 }, click: this.handleUpdateProcess },
            { label: 'viewProcess', isShow: (row) => { return row.itemType == 2 }, click: this.handleViewProcess }
          ]
        }
      ]
    },
    getTypeTreeData() {
      if (this.osType == 1) {
        return this.typeTreeDataWindow
      } else if (this.osType == 2) {
        return this.typeTreeDataLinux
      } else if (this.osType == 4) {
        return this.typeTreeDataMac
      } else {
        return this.typeTreeDataWindow
      }
    },
    // 加载 安装/卸载程序库 数据
    loadAppTypeTree: function() {
      getTypeTreeNodeMap().then(respond => {
        this.typeTreeDataWindow = respond.data['1'] || []
        this.typeTreeDataLinux = respond.data['2'] || []
        this.typeTreeDataMac = respond.data['4'] || []
      })
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    checkAppSelectEnd(rowDatas) {
      this.checkAppDeleteable = rowDatas && rowDatas.length > 0
    },
    showSpecialDlg() {
      this.$refs.specialPkgDlg.show(this.limitType)
    },
    handleAppCreate() {
      this.$refs.appImportTable.showBatchCreate(null, true)
    },
    getProcessListId(processList) {
      const typeIds = []
      const processIds = []
      processList.forEach(item => {
        if (item.itemType == 2) {
          // 通过类别添加，需要将类别id加入
          typeIds.push(item.typeId)
        } else {
          // 非 通过类别添加的程序（即手动添加的程序），需要将程序id加入
          processIds.push(item.dataId)
        }
      })
      const resultMap = new Map()
      typeIds.length > 0 && resultMap.set('type', typeIds)
      processIds.length > 0 && resultMap.set('process', processIds)
      return resultMap;
    },
    handleAppImport() {
      const map = this.getProcessListId(this.packetList)
      const typeIds = map.get('type') ? map.get('type').map(id => `G${id}`) : null
      const processIds = map.get('process') || null
      this.$refs.appImportTable.show(typeIds, processIds)
    },
    handleDeleteApp() {
      this.$confirmBox(this.$t('pages.install_Msg28'), this.$t('text.prompt')).then(() => {
        const rows = this.$refs['checkedAppGrid'].getSelectedDatas()

        // 创建待删除键的集合
        const deleteKeys = new Set();

        // 一次性收集所有需要删除的键
        rows.forEach(item => {
          // 处理父级元素
          const itemType = item.itemType;
          // 生成 typeId 和 dataId 两个可能的删除键
          deleteKeys.add(`${item.typeId}:${itemType}`);
          deleteKeys.add(`${item.dataId}:${itemType}`);

          // 处理子级元素
          if (item.children) {
            item.children.forEach(child => {
              deleteKeys.add(`${child.dataId}:${child.itemType}`);
            });
          }
        });

        // 使用filter一次性过滤
        this.temp.packetList = this.temp.packetList.filter(item =>
          !deleteKeys.has(`${item.id}:${item.itemType}`)
        );

        this.$refs['checkedAppGrid'].execRowDataApi()
        this.handleFilter()
      }).catch(() => {})
    },
    handleCreate(limitType) {
      this.limitType = limitType || this.defaultLimitType
      this.dialogStatus = 'create'
      this.defaultTemp.installLimitType = this.limitType === 1 ? 2 : 5
      this.$nextTick(() => {
        this.$refs['stgDlg'].show({
          entityType: this.entityNode.type,
          entityId: this.entityNode.dataId
        })
      })
    },
    handleUpdate(row, limitType) {
      this.getNowPackageList()
      this.filterSoftmgr = row.filterSoftmgr || 0
      this.limitType = limitType || this.defaultLimitType
      this.dialogStatus = 'update'
      this.defaultTemp.installLimitType = this.limitType === 1 ? 2 : 5
      getById(row.id).then(resp => {
        // 由于后台对这个数据进行了格式化，策略总览的时候没有格式化，因此查看详情的时候显示有误，因此先根据ID查询
        this.$refs['stgDlg'].show(resp.data, this.formable)
      })
    },
    formatRowData(rowData) {
      const install = [2, 5].includes(rowData.installLimitType)
      if (rowData.action) {
        const code = install ? 1 : 3
        !rowData.executeCode.includes(code) && rowData.executeCode.push(code)
      }
      if (rowData.ruleId) {
        const code = install ? 2 : 4
        !rowData.executeCode.includes(code) && rowData.executeCode.push(code)
      }
    },
    formatFormData(formData) {
      if (!this.isDoResponse(formData)) {
        formData.ruleId = null
      }
      formData.action = this.isDoLimit(formData) ? 1 : 0
      // this.formatData(formData.allowSpecialList, this.limitType === 1 ? 8 : 10) // 如果设置允许的特殊安装（卸载）包
      // this.formatData(formData.banSpecialList, this.limitType === 1 ? 7 : 9) // 如果设置禁止的特殊安装（卸载）包
    },
    formatData(list, installLimitType) {
      list.forEach(item => {
        item.softLimitProcessId = item.id
        item.installLimitType = installLimitType
      })
    },
    submitEnd(dlgStatus) {
      // 更新缓存的所有的程序
      this.getNowPackageList()
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
      this.searchInfo = ''
    },
    // actionValidator(rule, value, callback) {
    //   if (this.temp.executeCode.length === 0 && this.temp.report === 0) {
    //     callback(new Error(this.$t('pages.install_Msg51')))
    //   } else {
    //     callback()
    //   }
    // },
    validateForm(formData) {
      this.validMsg = ''
      if (this.temp.executeCode.length === 0 && this.temp.report === 0) {
        this.validMsg = this.$t('pages.install_Msg51')
      }
      return this.validMsg.length === 0 && this.responseValidate
    },
    md5LevelFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      }
      if (row.checkMd5 == 1 || row.isCheckMd5 == 1) {
        return this.$t('text.enable')
      }
      return this.$t('text.disable2')
    },
    nameFormatter(row, data) {
      if (row.itemType == 2) {
        return this.$t('pages.collectAll') + (!row.childrenNames ? '' : `(${this.$t('pages.include')}: ${row.childrenNames.join(', ')})`)
      } else {
        return row.processName
      }
    },
    typeIdFormatter(row, data) {
      // let msg = ''
      // if (this.getTypeTreeData()) {
      //   this.getTypeTreeData().some(node => {
      //     if (node.dataId == data) {
      //       msg = node.label
      //       return true
      //     }
      //   })
      // }
      // return msg
      return row.typeName
    },
    appAddSubmitEnd(data, dlgStatus) {
      if (data.innerAdd) {
        return
      }
      const appBean = {
        id: data.id,
        md5Level: data.md5Level,
        isCheckMd5: data.checkMd5,
        processName: data.processName,
        productVersion: data.productVersion,
        itemType: 1,  // 记录类型：1应用程序2类别
        typeId: data.typeId,
        typeName: data.typeName
      }
      // 删除已有相同的程序
      const index = this.temp.packetList.findIndex(v => v.id == data.id && appBean.itemType == v.itemType && v.itemType == appBean.itemType)
      if (index != -1) {
        this.$set(this.temp.packetList, index, appBean)
      } else {
        this.temp.packetList.unshift(appBean)
      }
      this.handleFilter()
      this.refreshList()
    },
    appMd5ImportSubmitEnd(data, editMode) {
      // editMode编辑模式，0-删除，1-添加程序，2-添加类别
      if (editMode === 2) {
        // 从已有列表中移除取消勾选的类别
        const dataIds = data.map(item => item.dataId)
        this.temp.packetList = this.temp.packetList.filter(item => dataIds.includes(item.id) || item.itemType !== 2);
      }
      if (editMode === 1) {
        // 获取取消勾选的原有的程序
        const dataIds = this.$refs.appImportTable.getRemoveCheckedRowKeys()
        // 从已有列表中移除取消勾选的程序
        this.temp.packetList = this.temp.packetList.filter(item => !(dataIds.includes(item.id) && item.itemType === 1));
      }
      if (editMode === 0) {
        // 创建待删除键的集合
        const deleteKeys = new Set(data.map(({ id, itemType }) => `${id}-${itemType}`));
        // 使用filter一次性过滤
        this.temp.packetList = this.temp.packetList.filter(({ id, itemType }) =>
          !deleteKeys.has(`${id}-${itemType}`)
        );
      } else {
        if (!Array.isArray(data)) {
          data = [data]
        }
        // 创建一个 Map 用于存储列表中的元素，以方便快速查找
        const listMap = new Map();
        this.temp.packetList.forEach((item) => {
          const key = `${item.id}-${item.itemType}`;
          listMap.set(key, item);
        });
        // 提前计算 itemType
        const itemType = editMode === 2 ? 2 : 1;

        data.forEach(item => {
          const id = itemType === 1 ? item.id : item.dataId;
          const key = `${id}-${itemType}`;
          const existApp = listMap.get(key);
          if (!existApp) {
            if (editMode === 1) {
              this.appAddSubmitEnd(item)
            } else if (editMode === 2 && item.id !== '0') {
              const appBean = {
                id: item.dataId,
                typeId: item.dataId,
                typeName: item.label,
                processName: item.label,
                itemType: 2  // 记录类型：1应用程序2类别
              }
              this.temp.packetList.unshift(appBean)
              // 同时更新 Map
              listMap.set(`${item.dataId}-2`, appBean);
            }
          } else {
            const { md5Level, checkMd5: isCheckMd5, typeId } = item
            Object.assign(existApp, { md5Level, isCheckMd5, typeId })
          }
        })
      }
      this.getNowPackageList()
      this.handleFilter()
    },
    updateInstallAppLog() {
      // 通知安装/卸载库树刷新
      this.$store.dispatch('commonData/changeNotice', 'updateInstallAppLog')
    },
    strategyRationFormatter(row) {
      let result = ''
      if (row.strategyRelations !== null && row.strategyRelations.length > 0) {
        result = row.strategyRelations.join(',')
      } else {
        result = this.$t('pages.noEnableStgRel')
      }
      return result;
    },
    installLimitTypeChange(val) {
      this.temp.report = 0
      this.temp.executeCode = []
      this.temp.ruleId = null
    },
    ruleIsCheck(data) {
      if (data) {
        this.temp.executeCode.unshift([2, 5].includes(this.temp.installLimitType) ? 2 : 4);
      } else {
        const executeCode = this.temp.executeCode.filter(code => code != 2 && code != 4)
        this.$set(this.temp, 'executeCode', executeCode)
      }
    },
    handleExport(selectedIds, query) {
      this.query = query
      this.$refs.exportDlg.show(selectedIds)
    },
    exportFunc(formData, opts) {
      if (formData.type === 3) {
        const q = Object.assign({}, this.query)
        return exportSoftPacket(q, opts)
      } else {
        return exportSoftPacket({
          ids: formData.type === 1 ? formData.dataIds.join(',') : null,
          typeId: formData.type === 2 ? formData.groupId : null
        }, opts)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
>>>.el-form-item__content>.el-radio-group {
  margin-left: 18px;
}
.prohibit >>> .el-checkbox__inner {
  margin-left: 18px;
}
>>>.el-checkbox .el-checkbox__label {
  padding-left: 10px;
}
.selectionCheckBox {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}
</style>
