<template>
  <div>
    <div v-if="formable">
      <el-button v-if="showByOsType" size="small" @click="handleAppCreate(true)">
        {{ $t('button.insert') }}
      </el-button>
      <el-button size="small" @click="handleApp">
        {{ $t('text.infoImport', { info :$t('pages.installApp') }) }}
      </el-button>
      <el-button size="small" :disabled="!checkAppDeleteable" @click="handleDeleteCheckedApp">
        {{ $t('button.delete') }}
      </el-button>
    </div>
    <grid-table
      ref="checkedAppGrid"
      :show-pager="false"
      :height="200"
      :multi-select="formable"
      :col-model="checkedColModel"
      :row-datas="checkedAppData"
      @selectionChangeEnd="checkAppSelectionChangeEnd"
    />
    <app-import-table
      ref="appImportTable"
      :os-type="osType==7?1:osType"
      :disable-product-md5="false"
      :type-tree-data="typeTreeData"
      :group-root-name="$t('pages.installApp')"
      :list="getPackegeList"
      :count-by-group="countChildByGroupId"
      :create="createSoftPacket"
      :batch-create="batchCreateSoftPacket"
      :update="updateSoftPacket"
      :delete="deleteSoftPacket"
      :import-func="importFromLib"
      :create-group="createType"
      :update-group="updateType"
      :delete-group="deleteType"
      :get-group-by-name="getTypeByName"
      :show-by-os-type="showByOsType"
      :support-md5="supportMd5"
      @submitEnd="appMd5ImportSubmitEnd"
    />
  </div>
</template>

<script>
import {
  getTreeNode, getPackegeList, importFromLib, getProcessByIds,
  createType, updateType, deleteType, getTypeByName, batchCreateSoftPacket,
  createSoftPacket, updateSoftPacket, deleteSoftPacket, countChildByGroupId
} from '@/api/system/baseData/softPacket'
import AppImportTable from '@/views/system/baseData/appLibrary/appImportTableDlg'

export default {
  name: 'SoftTable',
  components: { AppImportTable },
  props: {
    showByOsType: { type: Boolean, default: true },
    osType: { type: Number, default: 1 }, // 进程系统类型：1-windows，2-linux，4-mac
    formable: { type: Boolean, default: true }, // 能否提交表单
    checkedAppData: {
      type: Array,
      default: function() {
        return []
      }
    },
    supportMd5: { // 是否支持指纹防伪冒（某些功能终端还不支持，所以加个开关）
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      checkedColModel: [
        { prop: 'processName', label: 'processName', width: '150', formatter: this.nameFormatter },
        { prop: 'isCheckMd5', label: 'isCheckMd5', hidden: () => !this.supportMd5, width: '150', formatter: this.md5LevelFormatter },
        { prop: 'itemType', label: 'type', width: '150', formatter: this.itemTypeFormatter }
      ],
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      },
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      checkAppDeleteable: false,
      typeTreeDataWindow: [],
      typeTreeDataLinux: [],
      typeTreeDataMac: []
    }
  },
  computed: {
    typeTreeData() {
      if (this.osType == 1) {
        return this.typeTreeDataWindow
      } else if (this.osType == 2) {
        return this.typeTreeDataLinux
      } else if (this.osType == 4) {
        return this.typeTreeDataMac
      } else if (this.osType == 7) {
        return this.typeTreeDataWindow
      }
      return null
    }
  },
  created() {
    this.loadAppTypeTree()
  },
  activated() {
  },
  methods: {
    createType,
    updateType,
    deleteType,
    getPackegeList,
    createSoftPacket,
    batchCreateSoftPacket,
    updateSoftPacket,
    deleteSoftPacket,
    countChildByGroupId,
    importFromLib,
    getTypeByName,
    checkedAppGrid() {
      return this.$refs['checkedAppGrid']
    },
    reloadGrid() {  // 刷新选中的数据
      this.checkedAppGrid().execRowDataApi()
    },
    loadCheckTable: function(option) {
      const ids = []
      const groupIds = []
      this.checkedAppData.forEach(item => {
        if (item.itemType == 1) {
          ids.push(item.id)
        }
        if (item.itemType == 2) {
          groupIds.push(item.id)
        }
      })
      return getProcessByIds({ ids: ids.join(','), groupIds: groupIds.join(',') })
    },
    checkAppSelectionChangeEnd(rowDatas) {
      this.checkAppDeleteable = rowDatas && rowDatas.length > 0
    },
    loadAppTypeTree: function() {
      getTreeNode({ osType: 1 }).then(respond => {
        this.typeTreeDataWindow = respond.data
      })
      getTreeNode({ osType: 2 }).then(respond => {
        this.typeTreeDataLinux = respond.data
      })
      getTreeNode({ osType: 4 }).then(respond => {
        this.typeTreeDataMac = respond.data
      })
    },
    handleDeleteCheckedApp() {
      this.$confirmBox(this.$t('pages.install_Msg28'), this.$t('text.prompt')).then(() => {
        const rows = this.checkedAppGrid().getSelectedDatas()
        // 创建待删除键的集合
        const deleteKeys = new Set(rows.map(({ id, itemType }) => `${id}-${itemType}`));
        // 使用filter一次性过滤
        this.checkedAppData = this.checkedAppData.filter(({ id, itemType }) => 
          !deleteKeys.has(`${id}-${itemType}`)
        );
        this.reloadGrid()
      }).catch(() => {})
    },
    handleApp() {
      this.$refs.appImportTable.show()
    },
    handleAppCreate(flag) {
      this.$refs.appImportTable.showBatchCreate()
    },
    appAddSubmitEnd(data, dlgStatus) {
      const appBean = {
        id: data.id,
        md5Level: data.md5Level,
        isCheckMd5: data.checkMd5,
        processName: data.processName,
        productVersion: data.productVersion,
        itemType: 1,  // 记录类型：1应用程序2类别
        typeId: data.typeId
      }
      this.checkedAppData.unshift(appBean)
    },
    appMd5ImportSubmitEnd(data, editMode) {
      // editMode编辑模式，0-删除，1-添加程序，2-添加类别
      if (editMode === 0) {
        // 创建待删除键的集合
        const deleteKeys = new Set(data.map(({ id, itemType }) => `${id}-${itemType}`));
        // 使用filter一次性过滤
        this.checkedAppData = this.checkedAppData.filter(({ id, itemType }) => 
          !deleteKeys.has(`${id}-${itemType}`)
        );
      } else {
        // 创建一个 Map 用于存储列表中的元素，以方便快速查找
        const listMap = new Map();
        this.checkedAppData.forEach((item) => {
          const key = `${item.id}-${item.itemType}`;
          listMap.set(key, item);
        });
        // 提前计算 itemType
        const itemType = editMode === 2 ? 2 : 1;

        data.forEach(item => {
          const id = itemType === 1 ? item.id : item.dataId;
          const key = `${id}-${itemType}`;
          const existApp = listMap.get(key);
          if (!existApp) {
            if (editMode === 1) {
              this.appAddSubmitEnd(item)
            } else if (editMode === 2 && item.id != '0') {
              const appBean = {
                id: item.dataId,
                processName: item.label,
                itemType: 2  // 记录类型：1应用程序2类别
              }
              this.checkedAppData.unshift(appBean)
              // 同时更新 Map
              listMap.set(`${item.dataId}-2`, appBean);
            }
          } else {
            const { md5Level, checkMd5: isCheckMd5, typeId } = item
            Object.assign(existApp, { md5Level, isCheckMd5, typeId })
          }
        })
      }
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    md5LevelFormatter(row, data) {
      return this.$refs.appImportTable.md5LevelFormatter(row, data)
    },
    nameFormatter(row, data) {
      let msg = ''
      if (row.itemType == 2) {
        this.typeTreeData.some(node => {
          if (node.dataId == row.id) {
            msg = node.label
            return true
          }
        })
      } else {
        msg = row.processName
      }
      return msg
    },
    itemTypeFormatter(row, data) {
      return this.itemTypeMap[data]
    }
  }
}
</script>
