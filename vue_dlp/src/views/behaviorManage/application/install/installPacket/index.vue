<template>
  <div class="table-container">
    <grid-table
      v-if="listable"
      ref="strategyListTable"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :selectable="selectable"
      :after-load="afterLoad"
      @selectionChangeEnd="handleSelectionChange"
    >
      <template slot="popoverContent" slot-scope="props">
        <div style="max-height: 500px; max-width: 600px; overflow: auto;">
          <span style="padding: 5px 10px; display: inline-block;" v-html="strategyFormatter(props.detail)"></span>
        </div>
      </template>
    </grid-table>
  </div>
</template>
<script>
import { getInstallList, deleteData, exportData } from '@/api/behaviorManage/application/install'
import { timeDateInfoFormatter, stgActiveIconFormatter, osTypeIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import { selectable, hiddenActiveAndEntity, objectFormatter, entityLink } from '@/utils'

export default {
  name: 'InstallPacket',
  props: {
    parentVm: { type: Object, default: undefined },
    limitType: { type: Number, default: 1 },
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    loadTableEnd: { type: Function, default: (rowData, grid) => {} },
    selectTableEnd: { type: Function, default: (rowData, grid) => {} },
    entityClick: { type: Function, default: (row, data) => {} },
    buttonFormatter: { type: Function, default: (row, data) => {} },
    tableSelectable: { type: Boolean, default: false }  //  表是否可选
  },
  data() {
    return {
      addBtnAble: false,
      query: { // 查询条件
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this.parentVm),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'Data', label: 'stgMessage', width: '200', ellipsis: false, type: 'popover', originData: true, formatter: this.strategyFormatter, iconFormatter: osTypeIconFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        {
          prop: 'createdTime', label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      showTree: true,
      treeable: true,
      map: {
        1: this.$t('pages.install_Msg16'),
        2: this.$t('pages.install_Msg17'),
        3: this.$t('pages.install_Msg18'),
        4: this.$t('pages.install_Msg19'),
        5: this.$t('pages.install_Msg20'),
        6: this.$t('pages.install_Msg21'),
        7: this.$t('pages.install_Msg17'),
        8: this.$t('pages.install_Msg18'),
        9: this.$t('pages.install_Msg20'),
        10: this.$t('pages.install_Msg21')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    keyText() {
      return this.limitType === 1 ? this.$t('pages.install') : this.$t('pages.uninstall')
    },
    installTypes() {
      // 限制类型：1.禁止所有安装包2.禁止以下安装包3.允许以下安装包7.禁止特殊安装包8.允许特殊安装包
      // 4.禁止所有卸载包5.禁止以下卸载包6.允许以下卸载包9.禁止特殊卸载包10.允许特殊卸载包")
      if (this.limitType == 1) {
        return [1, 2, 3, 7, 8]
      } else {
        return [4, 5, 6, 9, 10]
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
    const { objectType, objectId } = this.$route.query
    if (this.$route.query.objectId) {
      entityLink({ entityType: objectType, entityId: objectId }, {}, this)
    }
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      this.loadTableEnd(rowData, grid)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.limitType = this.limitType
      return getInstallList(searchQuery)
    },
    handleFilter(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$emit('handleCreate', this.limitType)
    },
    handleUpdate: function(row) {
      this.$emit('handleUpdate', row, this.limitType)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    handleSelectionChange(rowDatas) {
      this.selectTableEnd(rowDatas)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }, this.limitType).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleExport(data, opts) {
      exportData(data, this.limitType, opts)
    },
    strategyFormatter(row) {
      let msg = ''
      if (row.action && row.action === 1) {
        msg = `${this.$t('pages.forbidMsg', { msg: this.keyText })}`
      }
      if (row.ruleId) {
        msg += msg ? `、${this.$t('pages.triggerResponseRule')}` : this.$t('pages.triggerResponseRule')
      }
      if (row.report && row.report === 1) {
        const packageMsg = this.limitType === 1 ? this.$t('pages.install') : this.$t('pages.uninstall')
        msg += msg ? `、${this.$t('pages.install_Msg50', { package: packageMsg })}` : this.$t('pages.install_Msg50', { package: packageMsg })
      }

      const filterSoftMsg = row.filterSoftmgr !== undefined && row.filterSoftmgr === 1 ? this.$t('pages.filterSoftmgr') + '；' : ''
      let limitMsg = ''
      if ([2, 5].indexOf(row.installLimitType) > -1) {
        limitMsg = this.$t('pages.install_Msg', { info: this.keyText })
      } else {
        limitMsg = this.$t('pages.install_Msg2', { info: this.keyText })
      }
      return filterSoftMsg + limitMsg + '：' + msg
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    }
  }
}
</script>
