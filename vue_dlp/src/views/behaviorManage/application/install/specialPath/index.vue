<template>
  <div class="table-container">
    <grid-table v-if="listable" ref="strategyListTable" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    <el-dialog
      v-el-drag-dialog
      append-to-body
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 700px; margin-left: 30px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="11"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit />
        </FormItem>
        <FormItem v-if="treeable" :label="$t('pages.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-card class="box-card" :body-style="{'padding': '0', 'padding-top': '5px'}">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.install_Msg31') }}</span>
            <el-tooltip class="item" effect="dark" :content="$t('pages.install_Msg32')" placement="bottom-start">
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <tag :list="installSpecialList" :disabled="!formable"/>
        </el-card>
        <el-card class="box-card" :body-style="{'padding': '0', 'padding-top': '5px'}">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.install_Msg33') }}</span>
            <el-tooltip class="item" effect="dark" :content="$t('pages.install_Msg34')" placement="bottom-start">
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <tag :list="uninstallSpecialList" :disabled="!formable"/>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="11"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSpecialPath, saveSpecialPath, updateSpecialPath, deleteSpecialPath, exportSpecialPath } from '@/api/behaviorManage/application/install'
import { validatePolicy } from '@/utils/validate'
import { selectable, hiddenActiveAndEntity, objectFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'

export default {
  name: 'SpeicialPath',
  props: {
    parentVm: { type: Object, default: undefined },
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    loadTableEnd: { type: Function, default: (rowData, grid) => {} },
    selectTableEnd: { type: Function, default: (rowData, grid) => {} },
    entityClick: { type: Function, default: (row, data) => {} },
    buttonFormatter: { type: Function, default: (row, data) => {} },
    tableSelectable: { type: Boolean, default: false }  //  表是否可选
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this.parentVm),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'mode', label: 'stgMessage', width: '200', formatter: this.modeFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: {
        id: null,
        name: '',
        active: false,
        remark: '',
        isInherit1: 0,
        isInherit2: 0,
        specialList: [],
        entityType: '',
        entityId: null
      },
      installSpecialList: [],
      uninstallSpecialList: [],
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.specialDirStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.specialDirStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ],
        specialPath: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      this.loadTableEnd(rowData, grid)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getSpecialPath(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selectTableEnd(rowDatas)
    },
    handleFilter(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.installSpecialList = []
      this.uninstallSpecialList = []
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      this.temp.specialList.forEach(item => {
        if (item.type === 1) {
          if (this.temp.isInherit1 !== item.isInherit) {
            this.temp.isInherit1 = item.isInherit
          }
          this.installSpecialList.push(item.specialPath)
        } else if (item.type === 2) {
          if (this.temp.isInherit2 !== item.isInherit) {
            this.temp.isInherit2 = item.isInherit
          }
          this.uninstallSpecialList.push(item.specialPath)
        }
      })
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {},
    handleExport(data, opts) {
      exportSpecialPath(data, opts)
    },
    formatSubmitData() {
      this.temp.specialList = []
      this.installSpecialList.forEach(item => {
        this.temp.specialList.push({
          specialPath: item,
          type: 1,
          isInherit: this.temp.isInherit1
        })
      })
      this.uninstallSpecialList.forEach(item => {
        this.temp.specialList.push({
          specialPath: item,
          type: 2,
          isInherit: this.temp.isInherit2
        })
      })
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatSubmitData()
          saveSpecialPath(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatSubmitData()
          updateSpecialPath(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteSpecialPath({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    modeFormatter: function(row) {
      let msg = ''
      let msg1 = row.specialList.filter(item => { return item.type === 1 }).map(item => { return item.specialPath }).join(',')
      msg1 = msg1 ? (this.$t('pages.install_Msg38') + msg1) : ''
      let msg2 = row.specialList.filter(item => { return item.type === 2 }).map(item => { return item.specialPath }).join(',')
      msg2 = msg2 ? (this.$t('pages.install_Msg39') + msg2) : ''
      msg = msg1 ? (msg2 ? (msg1 + '；' + msg2) : msg1) : msg2
      return msg
    }
  }
}
</script>
