<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.advancedConfiguration1')"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    width="650px"
    @dragDialog="handleDrag"
  >
    <Form ref="dataForm" :model="temp" :rules="rules" label-position="top" label-width="120px">
      <el-divider content-position="left">{{ $t('pages.processSetting') }}</el-divider>
      <FormItem label="">
        <el-radio-group v-model="temp.option1001">
          <el-radio :label="0">{{ $t('pages.processSetting1') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.processSetting2') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <el-divider content-position="left">{{ $t('pages.logReport') }}</el-divider>
      <FormItem prop="option1002" label="">
        <el-checkbox v-model="temp.option1004" :true-label="1" :false-label="0" style="margin-left: 10px" @change="change1004"></el-checkbox>
        <i18n path="pages.logReportText">
          <el-input-number
            slot="second"
            v-model="temp.option1002"
            :disabled="temp.option1004==0"
            :max="60"
            :min="1"
            step-strictly
            :step="1"
            :controls="false"
            style="width: 60px;"
          />
          <el-input-number
            slot="times"
            v-model="temp.option1003"
            :disabled="temp.option1004==0"
            :max="999999"
            :min="1"
            step-strictly
            :step="1"
            :controls="false"
            style="width: 90px;"
          />
        </i18n>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="saveSetting()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { saveOptions, listOptions } from '@/api/behaviorManage/application/appLogConfig'

export default {
  name: 'AppLogOptionDlg',
  data() {
    return {
      submitting: false,
      dialogVisible: false,
      temp: {},
      defaultTemp: {
        option1001: 0,
        option1002: 60,
        option1003: 30,
        option1004: 0
      },
      rules: {
        option1002: [
          { validator: this.numberRequied, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    numberRequied(rule, value, callback) {
      if (!this.temp.option1002 || !this.temp.option1003) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    change1004(val) {
      // 如果取消勾选时，一些有非空验证的输入框如果为空，则默认一个值给他
      if (val == 0) {
        if (!this.temp.option1002) {
          this.temp.option1002 = this.defaultTemp.option1002
        }
        if (!this.temp.option1003) {
          this.temp.option1003 = this.defaultTemp.option1003
        }
        this.$refs.dataForm.clearValidate()
      }
    },
    show() {
      this.resetTemp()
      listOptions().then(res => {
        if (res.data) {
          res.data.forEach(item => {
            this.temp['option' + item.type] = item.value
          })
        }
      })
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleSelectionChange(val) {
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    saveSetting() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const optList = [
            { type: 1001, value: this.temp.option1001 },
            { type: 1002, value: this.temp.option1002 },
            { type: 1003, value: this.temp.option1003 },
            { type: 1004, value: this.temp.option1004 }
          ]
          saveOptions({ appLogOptionsList: optList }).then(res => {
            this.submitting = false
            this.dialogVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.saveSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    }
  }
}
</script>
