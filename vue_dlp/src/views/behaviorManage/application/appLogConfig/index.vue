<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button icon="el-icon-s-tools" size="mini" @click="handleOptionSet">
          {{ $t('pages.advancedConfiguration') }}
        </el-button>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;">{{ strategyFormatter(props.detail) }}</span>
          </div>
        </template>
      </grid-table>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 720px"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-radio-group v-model="temp.allowAll" :disabled="!formable" style="margin-left: 6px">
          <el-radio :label="1">{{ $t('pages.allowAll1') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.allowAll2') }}</el-radio>
        </el-radio-group>

        <div v-if="temp.allowAll == 0">
          <el-divider content-position="left">
            {{ $t('pages.allowList') }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.allowList_text1') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-divider>
          <LocalZoomIn parent-tag="el-dialog">
            <div v-if="formable" style="display: inline-block">
              <el-button size="small" icon="el-icon-plus" @click.native="handleCreate2('allowList')">{{ $t('button.add') }}</el-button>
              <el-button size="small" icon="el-icon-plus" @click.native="handleAssignImport">{{ $t('button.applicationLibraryImport') }}</el-button>
              <el-button size="small" :disabled="!deletable" icon="el-icon-delete" @click.native="handleDelete2('allowList')">{{ $t('button.delete') }}</el-button>
            </div>
            <grid-table
              ref="allowList"
              :show-pager="false"
              :col-model="colModel2"
              :multi-select="true"
              auto-height
              :row-datas="temp.allowList"
              @selectionChangeEnd="handleSelectionChange2"
            />
          </LocalZoomIn>
        </div>

        <el-divider content-position="left">
          {{ $t('pages.limitList') }}
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.limitList_text') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-divider>
        <LocalZoomIn parent-tag="el-dialog">
          <div v-if="formable" style="display: inline-block">
            <el-button size="small" icon="el-icon-plus" @click.native="handleCreate2('limitList')">{{ $t('button.add') }}</el-button>
            <el-button size="small" icon="el-icon-plus" @click.native="handleExceptImport">{{ $t('button.applicationLibraryImport') }}</el-button>
            <el-button size="small" :disabled="!deletable2" icon="el-icon-delete" @click.native="handleDelete2('limitList')">{{ $t('button.delete') }}</el-button>
          </div>
          <grid-table
            ref="limitList"
            :show-pager="false"
            :col-model="colModel3"
            :multi-select="true"
            auto-height
            :row-datas="temp.limitList"
            @selectionChangeEnd="handleSelectionChange3"
          />
        </LocalZoomIn>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="elgClose">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <app-log-option-dlg ref="logOptionDlg"/>
    <app-select-dlg ref="appSelectDlg" :validate-func="validateFunc" @select="appendDataTable">
      <template slot="additionalConfig" >
        <div style="float: left;margin-left: 216px;">
          <el-radio-group v-model="monitorType" style="display: inline-block;margin-bottom: 4px">
            <el-radio :label="2">{{ $t('pages.monitorProgram') }}</el-radio>
            <el-radio :label="3" style="margin-right: 2px">{{ $t('pages.monitorDirAndProgram') }}</el-radio>
          </el-radio-group>
          <el-input v-model="path" v-trim :disabled="monitorType === 2" clearable maxlength="260" :placeholder="this.$t('pages.processStgLib_Msg69')" style="display: inline-block;width: 200px;"/>
        </div>
      </template>
    </app-select-dlg>
    <app-select-dlg ref="exceptSelectDlg" :validate-func="validateFunc" @select="appendExceptDataTable">
      <template slot="additionalConfig">
        <div style="float: left;margin-left: 216px;">
          <el-radio-group v-model="monitorType" style="display: inline-block;margin-bottom: 4px">
            <el-radio :label="2">{{ $t('pages.monitorProgram') }}</el-radio>
            <el-radio :label="3" style="margin-right: 2px">{{ $t('pages.monitorDirAndProgram') }}</el-radio>
          </el-radio-group>
          <el-input v-model="path" v-trim :disabled="monitorType === 2" clearable maxlength="260" :placeholder="this.$t('pages.processStgLib_Msg69')" style="display: inline-block;width: 200px;"/>
        </div>
      </template>
    </app-select-dlg>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importAppLogConfigStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />

    <!-- 新增修改程序配置弹窗-->
    <el-dialog
      v-el-drag-dialog
      :title="processDialogStatus === 'create' ? $t('pages.addConfig') : $t('pages.updateConfig')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="processConfigVisible"
      width="450px"
    >
      <Form ref="processConfigForm" :rules="rules2" :model="processConfigTemp" label-position="right" label-width="80px">
        <FormItem :label="$t('pages.configType')" prop="ipType">
          <template slot="tooltip">
            <el-tooltip placement="top">
              <div slot="content">
                <i18n path="pages.monitorTypeTip">
                  <template slot="info">{{ addType == 'allowList' ? this.$t('pages.monitor') : this.$t('pages.processMonitor_Msg5') }}</template>
                  <br slot="br"/>
                </i18n>
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
          </template>
          <el-select v-model="processConfigTemp.monitorType" style="width: 100%;" @change="monitorTypeChange">
            <el-option v-for="item in monitorTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </FormItem>
        <FormItem v-show="processConfigTemp.monitorType != 2" :label="$t('pages.directoryName')" prop="path">
          <el-input
            v-model="processConfigTemp.path"
            v-trim
            clearable
            maxlength="260"
          />
        </FormItem>
        <FormItem v-show="processConfigTemp.monitorType != 1" :label="$t('table.processExeName')" :tooltip-content="$t('pages.allowList_text')" prop="processName">
          <el-input
            v-model="processConfigTemp.processName"
            v-trim
            clearable
            maxlength="64"
          />
        </FormItem>
      </Form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="processDialogStatus === 'create' ? createProcessConfig() : updateProcessConfig()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="processConfigVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  createData,
  deleteData,
  getDataByName,
  getDataPage,
  updateData
} from '@/api/behaviorManage/application/appLogConfig'
import {
  buttonFormatter,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import AppLogOptionDlg from '@/views/behaviorManage/application/appLogConfig/AppLogOptionDlg'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import AppSelectDlg from '@/views/system/baseData/appLibrary/appSelectDlg'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'AppLogConfig',
  components: { AppLogOptionDlg, AppSelectDlg, ImportStg },
  props: {
    listable: { type: Boolean, default: true },       // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    formable: { type: Boolean, default: true },       // 能否提交表单
    importAble: { type: Boolean, default: false },    // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false }     // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 159,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', ellipsis: false, type: 'popover', originData: true, width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      colModel2: [
        { prop: 'path', label: this.$t('pages.directoryName'), width: '150', sort: true, formatter: this.formatPath },
        { prop: 'processName', label: 'processExeName', width: '150', sort: true, formatter: this.formatProcessName },
        { label: 'operate', type: 'button', width: '50',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, disabledFormatter: () => !this.formable, click: (row) => this.handleUpdate2(row, 'allowList') }
          ]
        }
      ],
      colModel3: [
        { prop: 'path', label: this.$t('pages.directoryName'), width: '150', sort: true, formatter: this.formatPath },
        { prop: 'processName', label: 'processExeName', width: '150', sort: true, formatter: this.formatProcessName },
        { label: 'operate', type: 'button', width: '50',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, disabledFormatter: () => !this.formable, click: (row) => this.handleUpdate2(row, 'limitList') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        allowAll: 1,
        allowList: [],
        limitList: [],
        configs: [] // 最终传给后台的结果
      },
      dialogFormVisible: false,
      dialogStatus: '',
      checkProcessDeleteable: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.appLogConfigStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.appLogConfigStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        allowList: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      rules2: {
        path: [
          { validator: this.pathValidator, trigger: 'blur' }
        ],
        processName: [
          { validator: this.processNameValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      processConfigVisible: false,
      processDialogStatus: 'create',
      processConfigTemp: {
        id: '',
        monitorType: undefined, // 1-只监视目录 2-只监视程序 3-监视目录和程序
        path: '',
        processName: ''
      },
      deletable: false,
      deletable2: false,
      addType: 'allowList',
      updateType: 'allowList',
      monitorTypeOptions: [
        { label: this.$t('pages.monitorDir'), value: 1 },
        { label: this.$t('pages.monitorProgram'), value: 2 },
        { label: this.$t('pages.monitorDirAndProgram'), value: 3 }
      ],
      monitorType: 2,
      path: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    number() {
      if (isNaN(this.temp.fileSendLimit)) {
        this.temp.fileSendLimit = this.temp.fileSendLimit.replace(/[^\.\d]/g, '')
        this.temp.fileSendLimit = this.temp.fileSendLimit.replace('.', '')
      }
    },
    nullToZero(event) {
      if (this.temp.fileSendLimit === '') {
        this.temp.fileSendLimit = 0
      }
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getDataPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleExceptImport() {
      this.monitorType = 2
      this.path = ''
      this.$refs.exceptSelectDlg.show()
    },
    handleAssignImport() {
      this.monitorType = 2
      this.path = ''
      this.$refs.appSelectDlg.show()
    },
    appendDataTable(datas) {
      if (!datas || datas.length === 0) return
      if (this.monitorType === 2) {
        datas = datas.map(item => { return { monitorType: 2, processName: item.processName, path: '' } })
      } else if (this.monitorType === 3) {
        datas = datas.map(item => { return { monitorType: 3, processName: item.processName, path: this.path } })
      }
      const list = this.temp.allowList
      for (let i = 0; i < datas.length; i++) {
        const item = datas[i]
        const theIndex = list.findIndex(existApp => {
          return (existApp.processName || '') === (item.processName || '') && (existApp.path || '') === (item.path || '')
        })
        if (theIndex > -1) {
          list.splice(theIndex, 1)
        }
        list.unshift({ id: new Date().getTime() + i, monitorType: item.monitorType || undefined, path: item.path || '', processName: item.processName || '' })
      }
    },
    appendExceptDataTable(datas) {
      if (!datas || datas.length === 0) return
      if (this.monitorType === 2) {
        datas = datas.map(item => { return { monitorType: 2, processName: item.processName, path: '' } })
      } else if (this.monitorType === 3) {
        datas = datas.map(item => { return { monitorType: 3, processName: item.processName, path: this.path } })
      }
      const list = this.temp.limitList
      for (let i = 0; i < datas.length; i++) {
        const item = datas[i]
        const theIndex = list.findIndex(existApp => {
          return (existApp.processName || '') === (item.processName || '') && (existApp.path || '') === (item.path || '')
        })
        if (theIndex > -1) {
          list.splice(theIndex, 1)
        }
        list.unshift({ id: new Date().getTime() + i, monitorType: item.monitorType || undefined, path: item.path || '', processName: item.processName || '' })
      }
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.allowList = []
      this.temp.limitList = []
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    formatRowData() { // 修改数据前，需要格式化数据
      this.$set(this.temp, 'allowAll', 0)
      this.$set(this.temp, 'allowList', [])
      this.$set(this.temp, 'limitList', [])
      for (let i = 0; i < this.temp.configs.length; i++) {
        const item = this.temp.configs[i]
        if (item.type == 0 && item.processName == '*.*') {
          this.temp.allowAll = 1
        }
        if (item.type == 0 && item.processName != '*.*') {
          this.temp.allowList.push({ id: new Date().getTime() + i, monitorType: item.monitorType, path: item.path || '', processName: item.processName || '' })
        }
        if (item.type == 1) {
          // 25D1版本，item.type == 1的情况包括四种：例外进程typeEx==2、 指定路径typeEx==4、 例外路径typeEx==8、 例外路径下的例外进程typeEx==10
          if (item.typeEx && item.typeEx == 4) {
            // 指定路径
            this.temp.allowList.push({ id: new Date().getTime() + i, monitorType: item.monitorType, path: item.path || '', processName: item.processName || '' })
          } else {
            // 例外指定进程、例外路径、例外路径下的例外进程
            this.temp.limitList.push({ id: new Date().getTime() + i, monitorType: item.monitorType, path: item.path || '', processName: item.processName || '' })
          }
        }
      }
    },
    handleUpdate: function(row) {
      this.temp = JSON.parse(JSON.stringify(row))
      this.formatRowData()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    formatFormData() {  // 保存表单之前格式化数据
      this.temp.configs.splice(0)
      if (this.temp.allowAll == 1) {
        // 如果监视所有程序，则配置一条*.*
        this.temp.configs.push({ type: 0, typeEx: 1, processName: '*.*' })
      } else {
        // 只监视下列程序，把指定程序加进去
        this.temp.allowList.forEach(item => {
          if (item.processName && item.path) {
            // 同时配置了目录和程序
            this.temp.configs.push({ type: 0, typeEx: 5, monitorType: item.monitorType, processName: item.processName, path: item.path })
          } else if (item.processName) {
            // 只配置程序
            this.temp.configs.push({ type: 0, typeEx: 1, monitorType: item.monitorType, processName: item.processName, path: '' })
          } else if (item.path) {
            // 只配置目录
            this.temp.configs.push({ type: 1, typeEx: 4, monitorType: item.monitorType, processName: '', path: item.path })
          }
        })
      }
      // 加入例外配置
      this.temp.limitList.forEach(item => {
        if (item.processName && item.path) {
          // 同时配置了目录和程序
          this.temp.configs.push({ type: 1, typeEx: 10, monitorType: item.monitorType, processName: item.processName, path: item.path })
        } else if (item.processName) {
          // 只配置程序
          this.temp.configs.push({ type: 1, typeEx: 2, monitorType: item.monitorType, processName: item.processName, path: '' })
        } else if (item.path) {
          // 只配置目录
          this.temp.configs.push({ type: 1, typeEx: 8, monitorType: item.monitorType, processName: '', path: item.path })
        }
      })
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormData()
          createData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.elgClose()
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormData()
          const tempData = Object.assign({}, this.temp)
          updateData(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.elgClose()
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleOptionSet() {
      this.$refs.logOptionDlg.show()
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id != this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    pathValidator(rule, value, callback) {
      if (!value) {
        if (this.processConfigTemp.monitorType == 2) {
          callback()
        } else {
          callback(new Error(this.$t('pages.required1')))
        }
      } else {
        callback()
      }
    },
    processNameValidator(rule, value, callback) {
      if (!value) {
        if (this.processConfigTemp.monitorType == 1) {
          callback()
        } else {
          callback(new Error(this.$t('pages.required1')))
        }
      } else if (value == '*.*' || value == '.' || value == '.exe' || value == '*.exe*') {
        callback(new Error(this.$t('pages.appLogConfig_text1') + value))
      } else {
        callback()
      }
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const allowList = []
      const limitList = []
      let allowAll = 0

      row.configs.forEach(item => {
        if (item.type == 0 && item.processName == '*.*') {
          allowAll = 1
        }
        if (item.type == 0 && item.processName != '*.*') {
          allowList.push({ path: item.path || '', processName: item.processName || '' })
        }
        if (item.type == 1) {
          // 25D1版本，item.type == 1的情况包括三种：例外指定进程typeEx==2、 监视指定路径typeEx==4、 例外指定路径typeEx==8
          if (item.typeEx && item.typeEx == 4) {
            // 监视指定路径
            allowList.push({ path: item.path || '', processName: item.processName || '' })
          } else {
            // 例外指定进程、 例外指定路径
            limitList.push({ path: item.path || '', processName: item.processName || '' })
          }
        }
      })

      let allowMsg = allowList.map(item => {
        if (item.path && item.processName) {
          return this.$t('pages.dirProgramText', { dir: item.path, program: item.processName })
        } else if (item.path) {
          return this.$t('pages.dirProgramText', { dir: item.path, program: this.$t('pages.collectAll') })
        } else if (item.processName) {
          return this.$t('pages.dirProgramText', { dir: this.$t('pages.collectAll'), program: item.processName })
        }
      }).join('，')

      let limitMsg = limitList.map(item => {
        if (item.path && item.processName) {
          return this.$t('pages.dirProgramText', { dir: item.path, program: item.processName })
        } else if (item.path) {
          return this.$t('pages.dirProgramText', { dir: item.path, program: this.$t('pages.collectAll') })
        } else if (item.processName) {
          return this.$t('pages.dirProgramText', { dir: this.$t('pages.collectAll'), program: item.processName })
        }
      }).join('，')
      const allowAllMsg = allowAll === 1 ? `${this.$t('pages.allowAll1')}` : `${this.$t('pages.allowAll2')}`
      allowMsg = allowList.length > 0 ? `；${this.$t('pages.allowList')}：` + allowMsg : ''
      limitMsg = limitList.length > 0 ? `；${this.$t('pages.limitList')}：` + limitMsg : ''
      return allowAllMsg + allowMsg + limitMsg
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    importSuccess() {
      this.handleFilter()
    },
    elgClose() {
      this.dialogFormVisible = false
      this.temp.allowList.splice(0, this.temp.allowList.length)
      this.temp.limitList.splice(0, this.temp.limitList.length)
    },
    handleSelectionChange2(rows) {
      this.deletable = rows.length > 0
    },
    handleSelectionChange3(rows) {
      this.deletable2 = rows.length > 0
    },
    handleCreate2(addType) {
      this.addType = addType
      this.resetProcessConfigTemp()
      this.processConfigTemp.monitorType = 2
      this.processDialogStatus = 'create'
      this.processConfigVisible = true
      this.$nextTick(() => {
        this.$refs['processConfigForm'].clearValidate()
      })
    },
    handleUpdate2(row, updateType) {
      this.updateType = updateType
      this.resetProcessConfigTemp()
      this.processConfigTemp.id = row.id
      this.processConfigTemp.monitorType = row.monitorType || 2 // 监视类型不存在（25D1之前旧版本默认只监视程序），则默认赋值2，只监视程序
      this.processConfigTemp.path = row.path
      this.processConfigTemp.processName = row.processName
      this.processDialogStatus = 'update'
      this.processConfigVisible = true
      this.$nextTick(() => {
        this.$refs['processConfigForm'].clearValidate()
      })
    },
    handleDelete2(deleteType) {
      if (deleteType === 'allowList') {
        const selectedIds = this.$refs['allowList'].getSelectedIds()
        for (const selectedId of selectedIds) {
          const index = this.temp.allowList.findIndex(data => data.id === selectedId)
          this.temp.allowList.splice(index, 1)
        }
        this.$refs.allowList.clearSelection()
      } else if (deleteType === 'limitList') {
        const selectedIds = this.$refs['limitList'].getSelectedIds()
        for (const selectedId of selectedIds) {
          const index = this.temp.limitList.findIndex(data => data.id === selectedId)
          this.temp.limitList.splice(index, 1)
        }
        this.$refs.limitList.clearSelection()
      }
    },
    updateProcessConfig() {
      this.$refs['processConfigForm'].validate((valid) => {
        if (valid) {
          if (this.updateType == 'allowList') {
            const id = this.processConfigTemp.id || ''
            const monitorType = this.processConfigTemp.monitorType
            const path = this.processConfigTemp.path || ''
            const processName = this.processConfigTemp.processName || ''

            const rowDatas = this.$refs['allowList'] && this.$refs['allowList'].getDatas()
            const oldRowIndex = rowDatas.findIndex(item => item.id == id)

            const index = rowDatas.findIndex(item => item.path == path && item.processName == processName && item.id != id)
            if (index > -1) {
              // 修改的配置已存在
              this.$message({
                message: this.$t('valid.customizeSameName', { obj: this.$t('table.config') }),
                type: 'error',
                duration: 2000
              })
              return
            } else {
              rowDatas.splice(oldRowIndex, 1, { id, monitorType, path, processName })
            }
            this.temp.allowList = rowDatas
            this.processConfigVisible = false
          } else if (this.updateType == 'limitList') {
            const id = this.processConfigTemp.id || ''
            const monitorType = this.processConfigTemp.monitorType
            const path = this.processConfigTemp.path || ''
            const processName = this.processConfigTemp.processName || ''

            const rowDatas = this.$refs['limitList'] && this.$refs['limitList'].getDatas()
            const oldRowIndex = rowDatas.findIndex(item => item.id == id)

            const index = rowDatas.findIndex(item => item.path == path && item.processName == processName && item.id != id)
            if (index > -1) {
              // 修改的配置已存在
              this.$message({
                message: this.$t('valid.customizeSameName', { obj: this.$t('table.config') }),
                type: 'error',
                duration: 2000
              })
              return
            } else {
              rowDatas.splice(oldRowIndex, 1, { id, monitorType, path, processName })
            }
            this.temp.limitList = rowDatas
            this.processConfigVisible = false
          }
        }
      })
    },
    createProcessConfig() {
      this.$refs['processConfigForm'].validate((valid) => {
        if (valid) {
          if (this.addType === 'allowList') {
            const id = new Date().getTime()
            const monitorType = this.processConfigTemp.monitorType
            const path = this.processConfigTemp.path || ''
            const processName = this.processConfigTemp.processName || ''

            const rowDatas = this.$refs['allowList'] && this.$refs['allowList'].getDatas()
            const index = rowDatas.findIndex(item => item.path == path && item.processName == processName)
            if (index > -1) {
              // 新增的配置已存在
              this.$message({
                message: this.$t('valid.customizeSameName', { obj: this.$t('table.config') }),
                type: 'error',
                duration: 2000
              })
              return
            } else {
              rowDatas.push({ id, monitorType, path, processName })
            }
            this.temp.allowList = rowDatas
            this.processConfigVisible = false
          } else if (this.addType === 'limitList') {
            const id = new Date().getTime()
            const monitorType = this.processConfigTemp.monitorType
            const path = this.processConfigTemp.path || ''
            const processName = this.processConfigTemp.processName || ''

            const rowDatas = this.$refs['limitList'] && this.$refs['limitList'].getDatas()
            const index = rowDatas.findIndex(item => item.path == path && item.processName == processName)
            if (index > -1) {
              // 新增的配置已存在
              this.$message({
                message: this.$t('valid.customizeSameName', { obj: this.$t('table.config') }),
                type: 'error',
                duration: 2000
              })
              return
            } else {
              rowDatas.push({ id, monitorType, path, processName })
            }
            this.temp.limitList = rowDatas
            this.processConfigVisible = false
          }
        }
      })
    },
    resetProcessConfigTemp() {
      this.processConfigTemp.id = ''
      this.processConfigTemp.monitorType = undefined
      this.processConfigTemp.path = ''
      this.processConfigTemp.processName = ''
    },
    formatPath(row, data) {
      if (data === undefined || data === '') {
        return `<span style="font-style: italic">${this.$t('pages.allDir')}</span>`
      } else {
        return data
      }
    },
    formatProcessName(row, data) {
      if (data === undefined || data === '') {
        return `<span style="font-style: italic">${this.$t('pages.allProgram')}</span>`
      } else {
        return data
      }
    },
    monitorTypeChange(val) {
      if (val == 1) {
        this.processConfigTemp.processName = ''
        this.$refs['processConfigForm'] && this.$refs['processConfigForm'].clearValidate()
      } else if (val == 2) {
        this.processConfigTemp.path = ''
        this.$refs['processConfigForm'] && this.$refs['processConfigForm'].clearValidate()
      } else if (val == 3) {
        this.$refs['processConfigForm'] && this.$refs['processConfigForm'].clearValidate()
      }
    },
    validateFunc() {
      if (this.monitorType === 3 && this.path === '') {
        this.$message({
          message: this.$t('pages.processStgLib_Msg69'),
          type: 'error',
          duration: 2000
        })
        return false;
      } else {
        return true;
      }
    }
  }
}
</script>
