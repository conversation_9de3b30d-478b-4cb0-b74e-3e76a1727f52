<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.alarmType" :value="query.alarmType">
          <span>{{ $t('pages.violationType') }}：</span>
          <el-select v-model="query.alarmType" clearable is-filter filterable :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(item, index) in lossTypeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.alarmLevel" :value="query.alarmLevel">
          <span>{{ $t('pages.level') }}：</span>
          <el-select v-model="query.alarmLevel" clearable :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(value, key) in levelOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.action" :value="query.action">
          <span>{{ $t('pages.action') }}：</span>
          <el-select v-model="query.action" clearable :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(item, key) in actionOptions" :key="key" :label="item.alarmActionDesc" :value="item.alarmAction"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'305'" :request="exportFunc"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'413'" :selection="selection" :delete-log="deleteSensitiveLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :default-sort="{ prop: 'createTime' }"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="hasPermission('413')"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @handleIcon="handleIcon"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.eventDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.action')">
            {{ rowDetail.actionDescribe }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.level')">
            {{ rowDetail.alarmLevelDescribe }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.violationType')">
            {{ rowDetail.alarmTypeDescribe }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.treatmentMeasure')">
            <el-tooltip
              v-for="(item,index) in rowDetail.alarmLimits"
              :key="item"
              effect="dark"
              :content="rowDetail.alarmLimitDescribes[index]"
              placement="top-start"
            >
              <svg-icon :icon-class="iconObj[item]" :style="(item === 128 || item === 256 ) && !$store.getters.desensitizeContentAble ? 'margin-right:10px;cursor: pointer;color: #68a8d0' : 'margin-right:10px;'" @click="handleIcon(rowDetail,iconObj[item])"/>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.filePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
        <el-card :body-style="{'max-height': '320px', 'overflow-y': 'auto'}" class="box-card" style="max-height: 390px;">
          <div slot="header" class="clearfix" style="padding-left: 0px;">
            <span>{{ $t('pages.alarmDetails') }}</span>
          </div>
          <el-tag v-if="rowDetail.createTime">{{ rowDetail.createTime }}</el-tag>
          <p>{{ $t('pages.violationDetails') }}：{{ rowDetail.hitStrg }}</p>
          <p v-if="!$store.getters.desensitizeContentAble">{{ $t('pages.violationContent') }}：{{ rowDetail.content }}</p>
        </el-card>
      </div>
    </el-dialog>
    <MonitorDetail
      ref="detail"
      :detail-dialog-visible="screenshotDialogVisible"
      :query-detail="queryScreenshot"
      :type="'screenshot'"
      @handleClose="screenshotDialogVisible=false"
    />
    <RecordDetail
      ref="record"
      :detail-dialog-visible="recordDialogVisible"
      :query-detail="queryRecord"
      @handleClose="recordDialogVisible=false"
    />
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getSensitiveLogPage, getSensitiveLossType, getAlarmAction, exportSensitiveAlarmLog, deleteSensitiveLog } from '@/api/behaviorAuditing/alarmDetailLog'
import MonitorDetail from '@/views/behaviorManage/hardware/sensitiveOpLog/MonitorDetail'
import RecordDetail from '@/views/behaviorAuditing/alarmDetailLog/RecordDetail'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'SensitiveAlarmLog',
  components: { MonitorDetail, RecordDetail },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '170', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '130', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'alarmTypeDescribe', label: 'violationType', width: '130' },
        { prop: 'alarmLevelDescribe', label: 'level', width: '100' },
        { prop: '', label: 'treatmentMeasure', ellipsis: false, width: '200', iconFormatter: this.iconClassFormat },
        { prop: 'actionDescribe', label: 'action', width: '100' },
        { prop: 'filePath', label: 'terFilePath', width: '210' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('224'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      levelOptions: {
        1: this.$t('pages.severityOptions1'),
        2: this.$t('pages.severityOptions2'),
        3: this.$t('pages.severityOptions3'),
        4: this.$t('pages.severityOptions4')
      },
      actionOptions: [],
      iconObj: {
        2: 'alarm-01',
        4: 'alarm-02',
        8: 'alarm-03',
        // 16: '',
        32: 'alarm-04',
        64: 'alarm-05',
        128: 'alarm-06',
        256: 'alarm-07'
      },
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        alarmLevel: undefined,
        action: undefined,
        alarmType: undefined,
        searchReport: 1,
        guid: undefined,
        taskGuid: undefined,
        hasChild: undefined,
        taskId: undefined,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      queryScreenshot: { // 查询条件
        page: 1,
        createDate: '',
        capscreenGuid: '',
        sortName: 'createTime',
        sortOrder: 'asc',
        isTimes: false
      },
      queryRecord: { // 查询条件
        objectId: '',
        recScreenTime: '',
        recscreenGuid: ''
      },
      rowDetail: {},
      dialogFormVisible: false,
      recordDialogVisible: false,
      showTree: true,
      lossTypeOptions: [
        { value: 10001, label: this.$t('pages.doDrip') },
        { value: 0, label: this.$t('pages.stgLabelFtpUploadFile') },
        { value: 1, label: this.$t('pages.stgLabelEmailTransferContent') },
        { value: 2, label: this.$t('pages.usbCopyFile') },
        { value: 3, label: this.$t('pages.usbCutFile') },
        { value: 4, label: this.$t('pages.usbSaveFile') },
        { value: 5, label: this.$t('pages.stgLabelFilePrintContent') },
        { value: 7, label: this.$t('pages.stgLabelRemoteShareFile') },
        { value: 8, label: this.$t('pages.stgLabelImSendContent') },
        { value: 9, label: this.$t('pages.stgLabelImSendFile') },
        { value: 11, label: this.$t('pages.stgLabelForumPostContent') },
        { value: 13, label: this.$t('pages.stgLabelCdBurnFile') },
        { value: 14, label: this.$t('pages.stgLabelWebAccessContent') },
        { value: 15, label: this.$t('pages.stgLabelWebUploadFile') },
        { value: 16, label: this.$t('pages.stgLabelWebPasteContent') },
        { value: 17, label: this.$t('pages.stgLabelLocalShareFile') },
        { value: 18, label: this.$t('pages.stgLabelMail') },
        { value: 20, label: this.$t('pages.stgLabelBluetooth') },
        { value: 23, label: this.$t('pages.stgLabelWebDiskUploadFile') },
        { value: 24, label: this.$t('pages.stgLabelWebDiskDownloadFile') },
        { value: 25, label: this.$t('pages.stgLabelImDownloadFile') },
        { value: 26, label: this.$t('pages.stgLabelImReceiveFile') },
        { value: 29, label: this.$t('pages.stgLabelMtpSendFile') }
      ],
      screenshotDialogVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      selection: [],
      queryVideoMethod: undefined,
      paramData: {}
    }
  },
  watch: {
    '$store.state.commonData.notice.sensitiveAlarmDetail'() {
      this.paramData = Object.assign({}, this.$store.getters.alarmMsg)
      this.query.guid = this.$store.getters.alarmMsg.guid
      this.query.taskGuid = this.$store.getters.alarmMsg.taskGuid
      this.query.hasChild = this.$store.getters.alarmMsg.hasChild
      this.query.taskId = this.$store.getters.alarmMsg.taskId
      this.$refs.searchToolbar.setDate(this.$store.getters.alarmMsg.createDate)
      this.gridTable().execRowDataApi(this.query)
    }
  },
  created() {
    this.getSensitiveLossType()
    getAlarmAction({ type: 2 }).then(res => {
      this.actionOptions = res.data
    })
    addViewVideoBtn(this, () => !this.$store.getters.desensitizeContentAble)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteSensitiveLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      if (this.$route.params && this.$route.params.guid) {
        this.paramData = Object.assign({}, this.$route.params)
      }
      if (this.$route.params.guid && this.$route.params.createDate) {
        searchQuery.guid = this.$route.params.guid
        this.$refs.searchToolbar.setDate(this.$route.params.createDate)
        searchQuery.createDate = this.$route.params.createDate
        searchQuery.taskGuid = this.$route.params.taskGuid
        searchQuery.hasChild = this.$route.params.hasChild
        searchQuery.taskId = this.$route.params.taskId
      }
      if (option.page * option.limit > option.dlpTotal) {
        this.$route.params.guid = ''
        this.$route.params.createDate = ''
        this.$route.params.taskGuid = ''
        this.$route.params.hasChild = ''
        this.$route.params.taskId = ''
      }
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getSensitiveLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    getSensitiveLossType() {
      getSensitiveLossType({ type: 1 }).then(res => {
        // 邮件传输文本内容、邮件附件内容两种来源合并为邮件传输内容，界面只展示其中一种，查询时查询出两种来源(前端过滤展示，后台做查询处理)
        const data = res.data.filter(data => data.lossType != 18)
        this.lossTypeOptions = data.map(item => {
          return {
            value: item.lossType,
            label: (item.lossType == 1 || item.lossType == 18) ? this.$t('pages.stgLabelMailSendContent') : item.lossDesc
          }
        })
      })
    },
    handleFilter() {
      this.query.page = 1
      this.paramData = {}
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            // 使用 yyyy-mm 的形式
            const months = res.data.map(date => {
              const d = date.toString()
              return `${d.substr(0, 4)}-${d.substr(4, 6)}`
            })
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months.join(', '),
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    iconClassFormat(row) {
      const iconArr = []
      for (let i = 0; i < row.alarmLimits.length; i++) {
        const limit = row.alarmLimits[i]
        const label = row.alarmLimitDescribes[i]
        const iconClass = this.iconObj[limit]
        const iconObj = { class: iconClass, title: label }
        if (limit === 128 || limit === 256) {
          if (!this.$store.getters.desensitizeContentAble) {
            iconObj.className = 'text-button-icon'
          }
        }
        iconArr.push(iconObj)
      }
      return iconArr
    },
    handleIcon(row, iconClass) {
      if (this.hasPermission('224') && !this.$store.getters.desensitizeContentAble) {
        let item;
        for (const limit in this.iconObj) {
          const temp = this.iconObj[limit]
          if (temp === iconClass) {
            item = Number.parseInt(limit)
            break;
          }
        }
        if (item === 128) {
          this.screenshotDetail(row)
        } else if (item === 256) {
          this.recordDetail(row)
        }
      }
    },
    screenshotDetail(row) {
      this.queryScreenshot.page = 1
      this.queryScreenshot.capscreenGuid = row.capscreenGuid
      this.queryScreenshot.createDate = row.createTime
      this.queryScreenshot.searchReport = this.query.searchReport
      this.screenshotDialogVisible = true
      this.$nextTick(() => {
        const detailGrid = this.$refs.detail.$refs.detailGrid
        detailGrid.execRowDataApi(this.queryScreenshot)
      })
    },
    recordDetail(row) {
      this.queryRecord.objectId = row.terminalId
      this.queryRecord.recscreenGuid = row.recscreenGuid
      this.queryRecord.recScreenTime = row.createTime
      this.queryRecord.searchReport = this.query.searchReport
      this.recordDialogVisible = true
      this.$nextTick(() => {
        const detailGrid = this.$refs.record.$refs.detailGrid
        detailGrid.execRowDataApi(this.queryRecord)
      })
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      const queryData = Object.assign({}, this.query, this.paramData)
      return exportSensitiveAlarmLog({ exportType, ...queryData })
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.query.guid = undefined
      this.query.taskGuid = undefined
      this.query.hasChild = undefined
      this.query.taskId = undefined
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '224', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
