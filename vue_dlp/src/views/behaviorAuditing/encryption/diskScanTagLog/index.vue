<template>
  <disk-scan-log
    ref="diskScanLogRef"
    :show-tag="true"
    :show-sens="false"
    :show-enc-dec="false"
    permission-view="9B1"
    permission-export="9B2"
    permission-delete="9B3"
  />
</template>
<script>
import DiskScanLog from '@/views/behaviorAuditing/encryption/diskScanLog'

export default {
  name: 'DiskScanTagLog',
  components: { DiskScanLog },
  data() {
    return {}
  }
}
</script>

