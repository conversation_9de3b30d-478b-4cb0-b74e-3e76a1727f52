<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('pages.fileName') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.makeType" :value="query.makeType">
          <span>{{ $t('table.productionType') }}：</span>
          <el-select v-model="query.makeType">
            <el-option :value="null" :label="$t('pages.all')"/>
            <el-option :key="0" :value="0" :label="$t('pages.fileOutgoingLog_Msg')"/>
            <el-option :key="1" :value="1" :label="$t('pages.fileOutgoingLog_Msg1')"/>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.outSendFileType" :value="query.outSendFileType">
          <span>{{ $t('table.outgoingFileType') }}：</span>
          <el-select v-model="query.outSendFileType">
            <el-option :value="null" :label="$t('pages.all')"/>
            <el-option :key="0" :value="0" :label="$t('pages.fileOutgoingLog_Msg2')"/>
            <el-option :key="1" :value="1" :label="$t('pages.fileOutgoingLog_Msg3')"/>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'356'" :request="handleExport"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" v-permission="'211'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'467'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        :autoload="autoload"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.fileOutgoingLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.productionTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fileName')">
            <el-button
              v-permission="'205'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.uploadFileGuid"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!205'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize1')">
            {{ rowDetail.fileSize }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.productionType')">
            {{ makeTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.controlCode')">
            {{ controlCodeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.rightRules')">
            {{ rightRulesFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.readTimes')">
            {{ readTimesFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.readingCount')">
            {{ readCountFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.beginReadTime')">
            {{ beginReadTimeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.endReadTime')">
            {{ endReadTimeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.labelScreen')">
            {{ screenFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.labelPrint')">
            {{ printFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.outgoingFileType')">
            {{ outSendFileTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.msgTip')">
            {{ rowDetail.msgTip }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/behaviorAuditing/encryption/fileOutgoingLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'FileOutgoingLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'productionTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'fileSize', label: 'maxFileSize1', width: '150' },
        { prop: 'makeType', label: 'productionType', width: '150', formatter: this.makeTypeFormatter },
        { prop: 'outSendFileType', label: 'outgoingFileType', width: '150', formatter: this.outSendFileTypeFormatter },
        { prop: 'controlCode', label: 'controlCode', width: '300', formatter: this.controlCodeFormatter },
        { prop: 'rightRules', label: 'rightRules', width: '300', formatter: this.rightRulesFormatter },
        { prop: 'readTimes', label: 'readTimes', width: '150', formatter: this.readTimesFormatter },
        { prop: '', label: this.$t('pages.readingCount'), width: '150', formatter: this.readCountFormatter },
        { prop: 'beginReadTime', label: 'beginReadTime', width: '150', formatter: this.beginReadTimeFormatter },
        { prop: 'endReadTime', label: 'endReadTime', width: '150', formatter: this.endReadTimeFormatter },
        { prop: 'msgTip', label: 'msgTip', width: '150' },
        { prop: 'labelScreen', label: 'labelScreen', width: '150', formatter: this.screenFormatter },
        { prop: 'labelPrint', label: 'labelPrint', width: '150', formatter: this.printFormatter },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('211,301'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, title: this.$t('pages.fileOutgoingLog_Msg10'), isShow: () => this.hasPermission('211') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('301') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        fileName: null,
        makeType: null,
        outSendFileType: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      // reqDownloadNum: 0,
      makeTypeMap: {
        0: this.$t('pages.fileOutgoingLog_Msg'),
        1: this.$t('pages.fileOutgoingLog_Msg1')
      },
      outSendFileTypeMap: {
        0: this.$t('pages.fileOutgoingLog_Msg2'),
        1: this.$t('pages.fileOutgoingLog_Msg3')
      },
      controlCodeMap: {
        1: this.$t('pages.ctrlValueList2'),
        2: this.$t('pages.ctrlValueList3'),
        4: this.$t('pages.ctrlValueList1'),
        8: this.$t('pages.ctrlValueList4'),
        16: this.$t('pages.ctrlValueList7'),
        32: this.$t('pages.ctrlValueList5'),
        64: this.$t('pages.ctrlValueList6'),
        1024: this.$t('pages.ctrlValueList19')
      },
      rightRulesMap: {
        1: this.$t('pages.validateValue1'),
        2: this.$t('pages.fileOutgoingLog_Msg8'),
        4: this.$t('pages.validateValue3'),
        8: this.$t('pages.validateValue4'),
        16: this.$t('pages.validateValue5'),
        32: this.$t('pages.validateValue6'),
        64: this.$t('pages.validateValue7'),
        128: this.$t('pages.validateValue2'),
        256: this.$t('pages.validateValue15')
      },
      tempTask: {},
      defaultTempTask: {
        backType: 16,
        type: 2,
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    selectable(row, index) {
      return !this.downloadFormatter(row) || (this.$store.getters.auditingDeleteAble && this.hasPermission('467'))
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.backupServerId
      this.tempTask.fileGuid = row.uploadFileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    screenFormatter: function(row) {
      return row.screenWaterTemplateName ? row.screenWaterTemplateName : this.$t('text.dontOpen1')
    },
    printFormatter: function(row) {
      return row.printWaterTemplateName ? row.printWaterTemplateName : this.$t('text.dontOpen1')
    },
    readCountFormatter: function(row, data) {
      if ((row.controlCode & 512) > 0) {
        return this.$t('pages.readingCount_1')
      } else {
        return this.$t('pages.readingCount_0')
      }
    },
    readTimesFormatter: function(row, data) {
      if (row.readTimes === 65535) {
        return this.$t('pages.notLimit')
      } else {
        return row.readTimes
      }
    },
    downloadFormatter(data, btn) {
      return !data.uploadFileGuid
    },
    makeTypeFormatter: function(row, data) {
      return this.makeTypeMap[row.makeType]
    },
    outSendFileTypeFormatter: function(row, data) {
      return this.outSendFileTypeMap[row.outSendFileType]
    },
    controlCodeFormatter: function(row, data) {
      const result = []
      const controlCodes = this.numToList(row.controlCode, 11)
      console.log('controlCodes = ', controlCodes)
      for (const controlCode of controlCodes) {
        if (controlCode == 512) { // 查看详情时，使用次数累计不在控制项显示，使用次数累计和使用次数单独一个字段显示
          continue
        }
        result.push(this.controlCodeMap[controlCode])
      }
      return result.join('，')
    },
    rightRulesFormatter: function(row, data) {
      const result = []
      let rightRulesS = this.numToList(row.rightRules, 9)
      //  旧终端上报中2：允许实体打印、允许虚拟打印，   新终端上报数据结果为：2+128：允许实体打印，2+256：允许虚拟打印
      if (rightRulesS.includes(2)) {
        if (rightRulesS.includes(128) || (rightRulesS.includes(256))) {
          //  去除2
          rightRulesS = rightRulesS.filter(item => item !== 2);
        }
      }
      rightRulesS.forEach(op => {
        result.push(this.rightRulesMap[op])
      })
      return result.join('，')
    },
    beginReadTimeFormatter: function(row, data) {
      if (row.beginReadTime === '0001-01-01 00:00:00') {
        return this.$t('pages.notLimit')
      } else {
        return row.beginReadTime
      }
    },
    endReadTimeFormatter: function(row, data) {
      if (row.endReadTime === '9999-12-31 23:59:59') {
        return this.$t('pages.notLimit')
      } else {
        return row.endReadTime
      }
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '301', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
