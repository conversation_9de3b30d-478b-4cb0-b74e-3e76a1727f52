<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.fileOutgoingLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.productionTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fileName')">
            <el-button
              v-permission="'205'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.uploadFileGuid"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!205'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize1')">
            {{ rowDetail.fileSize }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.productionType')">
            {{ makeTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.outgoingFileType')">
            {{ outSendFileTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.controlCode')">
            {{ controlCodeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.rightRules')">
            {{ rightRulesFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.readingCount')">
            {{ readCountFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.readTimes')">
            {{ readTimesFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.beginReadTime')">
            {{ beginReadTimeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.endReadTime')">
            {{ endReadTimeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.labelScreen')">
            {{ screenFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.labelPrint')">
            {{ printFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.msgTip')">
            {{ rowDetail.msgTip }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: 'FileOutgoingLogDetail',
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      makeTypeMap: {
        0: this.$t('pages.fileOutgoingLog_Msg'),
        1: this.$t('pages.fileOutgoingLog_Msg1')
      },
      outSendFileTypeMap: {
        0: this.$t('pages.fileOutgoingLog_Msg2'),
        1: this.$t('pages.fileOutgoingLog_Msg3')
      },
      controlCodeMap: {
        1: this.$t('pages.ctrlValueList2'),
        2: this.$t('pages.ctrlValueList3'),
        4: this.$t('pages.fileOutgoingLog_Msg4'),
        8: this.$t('pages.ctrlValueList4'),
        16: this.$t('pages.ctrlValueList7'),
        32: this.$t('pages.fileOutgoingLog_Msg5'),
        64: this.$t('pages.fileOutgoingLog_Msg6'),
        1024: this.$t('pages.ctrlValueList19')
      },
      rightRulesMap: {
        1: this.$t('pages.fileOutgoingLog_Msg7'),
        2: this.$t('pages.fileOutgoingLog_Msg8'),
        4: this.$t('pages.validateValue3'),
        8: this.$t('pages.validateValue4'),
        16: this.$t('pages.validateValue5'),
        32: this.$t('pages.fileOutgoingLog_Msg9'),
        64: this.$t('pages.validateValue7'),
        128: this.$t('pages.validateValue2'),
        256: this.$t('pages.validateValue15')
      }
    }
  },
  computed: {

  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    makeTypeFormatter: function(row, data) {
      return this.makeTypeMap[row.makeType]
    },
    screenFormatter: function(row) {
      return row.screenWaterTemplateName ? row.screenWaterTemplateName : this.$t('text.dontOpen1')
    },
    printFormatter: function(row) {
      return row.printWaterTemplateName ? row.printWaterTemplateName : this.$t('text.dontOpen1')
    },
    readCountFormatter: function(row, data) {
      if ((row.controlCode & 512) > 0) {
        return this.$t('pages.readingCount_1')
      } else {
        return this.$t('pages.readingCount_0')
      }
    },
    readTimesFormatter: function(row, data) {
      if (row.readTimes === 65535) {
        return this.$t('pages.notLimit')
      } else {
        return row.readTimes
      }
    },
    downloadFormatter(data, btn) {
      return !data.uploadFileGuid
    },
    outSendFileTypeFormatter: function(row, data) {
      return this.outSendFileTypeMap[row.outSendFileType]
    },
    controlCodeFormatter: function(row, data) {
      let result = ''
      const controlCodes = this.numToList(row.controlCode, 11)
      controlCodes.forEach(op => {
        result += this.controlCodeMap[op] + ' '
      })
      return result
    },
    rightRulesFormatter: function(row, data) {
      let result = ''
      let rightRulesS = this.numToList(row.rightRules, 9)
      //  旧终端上报中2：允许实体打印、允许虚拟打印，   新终端上报数据结果为：2+128：允许实体打印，2+256：允许虚拟打印
      if (rightRulesS.includes(2)) {
        if (rightRulesS.includes(128) || (rightRulesS.includes(256))) {
          //  去除2
          rightRulesS = rightRulesS.filter(item => item !== 2);
        }
      }
      rightRulesS.forEach(op => {
        result += this.rightRulesMap[op] + ' '
      })
      return result
    },
    beginReadTimeFormatter: function(row, data) {
      if (row.beginReadTime === '0001-01-01 00:00:00') {
        return this.$t('pages.notLimit')
      } else {
        return row.beginReadTime
      }
    },
    endReadTimeFormatter: function(row, data) {
      if (row.endReadTime === '9999-12-31 23:59:59') {
        return this.$t('pages.notLimit')
      } else {
        return row.endReadTime
      }
    },
    handleDownload: function(row) {
      this.$emit('downloadAuditLogFile', row)
    }
  }
}
</script>
