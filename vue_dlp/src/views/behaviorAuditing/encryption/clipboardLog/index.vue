<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.clipboardDataStream" :value="query.clipboardDataStream">
          <span>{{ $t('table.clipboardDataStream') }}：</span>
          <el-input v-model="query.clipboardDataStream" v-trim clearable style="width: 200px;" @paste.native="handlePaste"/>
        </SearchItem>
        <SearchItem model-key="query.searchInfo" :value="query.searchInfo">
          <span>{{ $t('table.sourceProcessName') }}/{{ $t('table.destinationProcessName') }}：</span>
          <el-input v-model="query.searchInfo" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'518'" :request="exportFunc"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'519'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.clipboardLog')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.clipboardDataSize')">
            {{ rowDetail.clipboardDataSize }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.clipboardDataStream')">
            <pre @copy="handleCopy">{{ rowDetail.clipboardDataStream }}</pre>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.sourceProcessName')">
            {{ rowDetail.srcProcName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.destinationProcessName')">
            {{ rowDetail.dstProcName }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deleteLog, exportLog, getLogPage } from '@/api/behaviorAuditing/encryption/clipboardLog'
import { logSourceFormatter } from '@/utils/formatter'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { mapGetters } from 'vuex'
export default {
  name: 'ClipboardLog',
  data() {
    return {
      copyFormat: 'text/copied',
      showTree: true,
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        clipboardDataStream: '',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      dialogFormVisible: false,
      sortable: true,
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'clipboardDataStream', label: 'clipboardDataStream', width: '200', type: 'copiable', copy: this.handleCopy },
        { prop: 'clipboardDataSize', label: 'clipboardDataSize', width: '150' },
        { prop: 'srcProcName', label: 'sourceProcessName', width: '200' },
        { prop: 'dstProcName', label: 'destinationProcessName', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right',
          buttons: [
            { label: 'detail', click: this.handleView, disabledFormatter: this.handViewFormatter }
          ]
        }
      ],
      rowDetail: {

      },
      selection: []
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'isSuperRole',
      'isSysRole',
      'isSuperMode',
      'userId'
    ])
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    handlePaste(e) {
      e.preventDefault();
      const clipboardData = e.clipboardData || window.clipboardData
      const format = clipboardData.types.indexOf(this.copyFormat) < 0 ? 'text' : this.copyFormat
      const pasteData = clipboardData.getData(format)
      console.log('pasteData: ', JSON.stringify({ method: 'handlePaste', format, pasteData }))
      this.query.clipboardDataStream = pasteData
    },
    handleCopy(e) {
      e.preventDefault();
      const selection = document.getSelection();
      const isPreSelection = node => {
        if (node.nodeType === Node.TEXT_NODE) {
          const parentNode = node.parentNode
          return parentNode.nodeType === Node.ELEMENT_NODE && parentNode.nodeName === 'PRE'
        }
        return false
      }
      let format
      let copyData
      if (isPreSelection(selection.anchorNode) && isPreSelection(selection.focusNode)) {
        const start = Math.min(selection.anchorOffset, selection.focusOffset)
        const end = Math.max(selection.anchorOffset, selection.focusOffset)
        format = this.copyFormat
        copyData = selection.anchorNode.data.slice(start, end)
      } else {
        format = 'text/plain'
        copyData = selection.toString()
      }
      console.log('handleCopy: ', JSON.stringify({ method: 'handleCopy', format, copyData }))
      e.clipboardData.setData(format, copyData)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    handViewFormatter() {
      if (this.isSuperRole) {
        return false
      } else {
        return !this.hasPermission('517')
      }
    },
    handleView(row) {
      this.rowDetail = row
      const tempDate = row.createTime.split(' ')[0] + ' 00:00:00'
      this.query.createDate = new Date(tempDate)
      this.dialogFormVisible = true
    },
    selectionChangeEnd: function(rowDatas) {
      // 表格数据选中后
      this.selection = rowDatas
    },
    afterLoad(rowData) {
      // 表格加载后执行
    },
    exportFunc(exportType) {
      return exportLog({ exportType, ...this.query })
    },
    selectable(row, index) {
      // 表格数据是否可以选中
      return this.$store.getters.auditingDeleteAble
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-table__body td.el-table__cell {
  padding: 0;
  .cell.el-tooltip {
    padding: 0 5px;
    pre {
      &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
      }
      margin: 0;
      max-height: 60px;
      overflow: auto;
    }
  }
}
.el-descriptions {
  pre {
    margin: 0;
  }
}
</style>
