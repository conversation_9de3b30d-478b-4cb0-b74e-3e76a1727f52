<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.mailWhiteType" :value="query.mailWhiteType">
          <span>{{ $t('pages.mailWhiteLog_2') }}：</span>
          <el-select v-model="query.mailWhiteType" clearable :placeholder="$t('pages.mailWhiteLog_2')" style="width: 150px;">
            <el-option
              v-for="item in whiteTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.mailSender" :value="query.mailSender">
          <span>{{ $t('table.mailWhite_sender') }}：</span>
          <el-input v-model="query.mailSender" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <SearchItem model-key="query.mailReceiver" :value="query.mailReceiver">
          <span>{{ $t('table.mailWhite_receipt') }}：</span>
          <el-input v-model="query.mailReceiver" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'493'" :request="exportFunc"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'498'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.mailWhiteLog_7')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.mailWhite_type')">
            {{ mailWhiteTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.mailWhite_sender')">
            {{ rowDetail.mailSender }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.mailWhite_receipt')">
            {{ rowDetail.mailReceiver }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.mailWhiteLog_6')">
            {{ rowDetail.attachNum }}
          </el-descriptions-item>
        </el-descriptions>
        <fieldset style="margin-top:20px">
          <legend>{{ $t('pages.mailWhiteLog_5') }}</legend>
          <audit-file-downloader ref="auditFileDownloader" :button="$t('table.download')" :show="false" :selection="downloadData" :before-download="beforeDownload"/>
          <grid-table
            ref="attachTable"
            :height="120"
            :row-datas="tableData"
            :multi-select="false"
            :show-pager="false"
            :sortable="false"
            :col-model="attachColModel"
          />
        </fieldset>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { getLogPage, exportLog, deleteLog, getPageAttach } from '@/api/behaviorAuditing/encryption/mailWhiteListLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'MailWhiteLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },

        { prop: 'mailSender', label: 'mailWhite_sender', width: '150' },
        { prop: 'mailReceiver', label: 'mailWhite_receipt', width: '200' },
        { prop: 'mailWhiteType', label: 'mailWhite_type', width: '150', formatter: this.mailWhiteTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right',
          buttons: [
            { label: 'detail', click: this.handleView, disabledFormatter: this.handViewFormatter }
          ]
        }
      ],
      attachColModel: [
        { prop: 'mailAttach', label: 'mailWhiteAttach', width: '200' },
        { prop: 'attachSize', label: 'mailWhiteAttachSize', width: '180' },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right',
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFileFormatter }
          ]
        }
      ],
      showTree: true,
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        mailWhiteType: null,
        mailSender: '',
        mailReceiver: '',
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        guid: '',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      whiteTypeOption: [
        { label: this.$t('pages.mailWhiteLog_1'), value: null },
        { label: this.$t('pages.mailWhiteLog_3'), value: 0 },
        { label: this.$t('pages.mailWhiteLog_4'), value: 1 }

      ],
      sortable: true,
      selection: [],
      dialogFormVisible: false,
      queryVideoMethod: undefined,
      rowDetail: {},
      attachDetail: [],
      tableData: [],
      tempTask: {},
      defaultTempTask: {
        backType: 9, // 业务类型，9-邮件监控
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      downloadData: []
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'isSuperRole',
      'isSysRole',
      'isSuperMode',
      'userId'
    ])
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this, () => !this.handViewFormatter())
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    handleLoadDown(row) {
      this.downloadData.push(row)
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.mailAttach
      return this.tempTask
    },
    handleView(row) {
      this.rowDetail = row
      const tempDate = row.createTime.split(' ')[0] + ' 00:00:00'
      this.query.createDate = new Date(tempDate)
      this.query.guid = row.guid
      // getAttachNum(this.query).then(res => {
      //   this.rowDetail.attachNum = res.data
      // })
      getPageAttach(this.query).then(res => {
        this.rowDetail.attachNum = res.data.items.length
        this.tableData = res.data.items
        this.dialogFormVisible = true
      })
    },
    selectionChangeEnd: function(rowDatas) {
      // 表格数据选中后
      this.selection = rowDatas
    },
    afterLoad(rowData) {
      // 表格加载后执行
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '559')
    },
    selectable(row, index) {
      // 表格数据是否可以选中
      return this.$store.getters.auditingDeleteAble
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          console.log('res:' + JSON.stringify(res))
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    exportFunc(exportType) {
      return exportLog({ exportType, ...this.query })
    },
    mailWhiteTypeFormatter: function(row) {
      if (row.mailWhiteType == 0) {
        return this.$t('pages.mailWhiteLog_3')
      }
      return this.$t('pages.mailWhiteLog_4')
    },
    handViewFormatter() {
      if (this.isSuperRole) {
        return false
      } else {
        return !this.hasPermission('559')
      }
    },
    downloadFileFormatter() {
      if (this.isSuperRole) {
        return false
      } else {
        return !this.hasPermission('539')
      }
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    }
  }
}
</script>
