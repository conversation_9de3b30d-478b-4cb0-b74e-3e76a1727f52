<template>
  <div>

  </div>
</template>
<script>
export default {
  name: 'HttpWhiteListDecLogDetail',
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {}
    }
  },
  computed: {
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    handleDownload: function(row) {
      this.$emit('downloadAuditLogFile', row)
    }
  }
}
</script>
