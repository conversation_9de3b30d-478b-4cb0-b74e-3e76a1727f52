<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['user']" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @handleFilter="handleFilter"
      >
        <el-select slot="time" v-model="query.timeOperationType" style="width: 150px;">
          <el-option
            v-for="item in timeOperationType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <span slot="time">
          <el-date-picker
            v-model="query.beginTime"
            :clearable="false"
            type="datetime"
            :picker-options="pickerOptionsStart"
            :placeholder="$t('pages.startDate')"
            style="width:200px"
          ></el-date-picker>
          ->
          <el-date-picker
            v-model="query.endTime"
            :clearable="false"
            type="datetime"
            :picker-options="pickerOptionsEnd"
            :placeholder="$t('pages.endDate')"
            style="width:200px"
          ></el-date-picker>
        </span>
        <SearchItem model-key="query.category" :value="query.category">
          <span>{{ $t('pages.funName') }}：</span>
          <el-select v-model="query.category" style="width: 150px;" @change="handleChange">
            <el-option
              v-for="item in categoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.userOperationType" :value="query.userOperationType">
          <span>{{ $t('pages.userType') }}：</span>
          <el-select v-model="query.userOperationType" style="width: 150px;">
            <el-option
              v-for="item in userOperationType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.approvalStatus" :value="query.approvalStatus">
          <span>{{ $t('table.status') }}：</span>
          <el-select v-model="query.approvalStatus" clearable :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(value, key) in statusOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span style="margin-left:10px">{{ $t('pages.fileName') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 200px;"></el-input>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'367'" multi-dataset :request="handleExport"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'497'" icon="el-icon-delete" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <audit-file-downloader
          ref="auditFileDownloader"
          slot="append"
          v-permission="'230'"
          :button="$t('table.download')"
          :show="query.category !== 'sensitiveFileOutSend' && query.category !== 'filePrint' && query.category !== 'offline' && query.category !== 'behaviorControl' && query.category !== 'fileDecrypt'"
          :selection="selection"
          :before-download="beforeDownload"
        />
      </SearchToolbar>
      <grid-table
        ref="logList"
        :col-model="colModel"
        :default-sort="{ prop: 'startTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :custom-col="true"
        :custom-table-key="query.category"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.approvalLog_Msg')"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <grid-table ref="logerList" :height="400" :col-model="logerModel" :row-datas="logerRowData" :default-sort="{ prop: 'id', order: 'ascending' }" :multi-select="false" :show-pager="false"/>
    </el-dialog>

    <el-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.processSteps')"
      :visible.sync="dialogStepsVisible"
      :append-to-body="true"
      width="600px"
    >
      <ApprovalChart v-if="dialogStepsVisible === true" ref="chart" :approver-list="approverList" :initiator-list="initiatorList" :editable="false"/>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogStepsVisible=false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 文件外发审批流程参数 -->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.approvalLog_Msg1')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogDetail"
      width="800px"
    >
      <!-- 流程参数 -->
      <grid-table v-if="!hasAttachmentDetailFtpId" :col-model="detailColModel" :multi-select="false" :show-pager="false" :row-datas="fileDatas" />
      <!-- <grid-table
        ref="attachmentFileList"
        :col-model="detailColModel"
        :multi-select="true"
        :row-data-api="attachmentFileRowDataApi"
        :height="315"
        :show-pager="true"
      /> -->
      <grid-table v-if="hasAttachmentDetailFtpId" ref="attachmentFileList" :height="200" :col-model="detailColModel" :multi-select="false" :autoload="false" :row-data-api="attachmentFileRowDataApi"/>
      <el-dialog
        v-el-drag-dialog
        :title="$t('pages.sensitiveMsg')"
        :close-on-click-modal="false"
        :modal="false"
        :visible.sync="sensitiveVisible"
        width="600px"
      >
        <!-- <h4><i class="el-icon-caret-right"></i>敏感信息：</h4> -->
        <el-descriptions class="margin-top" :column="2" size="small" border>
          <el-descriptions-item>
            <template slot="label">
              {{ $t('pages.sensitivitySeverity') }}
            </template>
            {{ levelOptions[sensitiveObj.AlarmLevel] }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
            <template slot="label">
              {{ $t('pages.sensitivePolicyName') }}
            </template>
            {{ sensitiveObj.StrategyName }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
            <template slot="label">
              {{ $t('pages.sensitiveRuleName') }}
            </template>
            {{ sensitiveObj.RuleName }}
          </el-descriptions-item>
          <!-- <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
            <template slot="label">
              敏感内容概要
            </template>
            {{ sensitiveObj.Contents }}
          </el-descriptions-item> -->
        </el-descriptions>
        <el-descriptions v-if="!$store.getters.desensitizeContentAble" class="margin-top" :column="1" size="small" border>
          <el-descriptions-item label-class-name="my-label1" content-class-name="my-content1">
            <template slot="label">
              {{ $t('pages.sensitiveContentProfile') }}
            </template>
            {{ sensitiveObj.Contents }}
          </el-descriptions-item>
        </el-descriptions>
      </el-dialog>
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.approvalLog_Msg2') }}：</h4>
      <el-descriptions class="margin-top" :column="2" size="small" border>
        <el-descriptions-item>
          <template slot="label">
            {{ $t('pages.approvalLog_Msg3') }}
          </template>
          <span v-if="detailObj.sendTimes==0">{{ $t('pages.noLimit') }}</span><span v-else>{{ detailObj.sendTimes }}</span>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg4') }}
          </template>
          <span v-if="detailObj.notLimitFile==1">{{ $t('pages.noLimit') }}</span><span v-else>{{ $t('pages.limit') }}</span>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg5') }}
          </template>
          <span v-if="detailObj.autoDecrypt==1">{{ $t('text.yes') }}</span><span v-else>{{ $t('text.no') }}</span>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg6') }}
          </template>
          {{ detailObj.sendBeginTime }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label" :span="2" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg7') }}
          </template>
          {{ detailObj.sendEndTime }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label1" span="2" content-class-name="my-content1">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg8') }}
          </template>
          <el-tag v-for="(item,index) in detailObj.lossTypes" :key="index" size="small" style="margin:0 3px 3px 0">{{ item }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.approvalLog_Msg9') }}：</h4>
      <el-input
        v-model="detailObj.reqMsg"
        readonly=""
        type="textarea"
        :rows="3"
      >
      </el-input>
    </el-dialog>
    <!-- 解密审批流程参数 -->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.approvalLog_Msg1')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="fileDecryptDialog"
      width="800px"
    >
      <grid-table ref="fileDecryptAttachmentFileList" :height="200" :col-model="fileDecryptColModel" :multi-select="false" :autoload="false" :row-data-api="attachmentFileRowDataApi"/>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.approvalLog_Msg1')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="printVisible"
      width="600px"
    >
      <el-tabs v-model="activeName" type="card" style="padding-bottom:5px" @tab-click="handleClick">
        <el-checkbox v-model="noLimit" disabled :true-label="'true'" :false-label="'false'" style="margin-bottom:5px">{{ $t('pages.noLimit') }}</el-checkbox>
        <el-tab-pane :label="$t('pages.approvalLog_Msg10')" name="first">
          <grid-table :col-model="printColModel" :selectable="() => { return false }" :after-load="afterLoad4" :show-pager="false" :row-datas="printDatas" :min-height="200"/>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.approvalLog_Msg11')" name="second">
          <grid-table :col-model="printColModel" :selectable="() => { return false }" :after-load="afterLoad3" :show-pager="false" :row-datas="printDatas" :min-height="200"/>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.approvalLog_Msg12')" name="third">
          <grid-table :col-model="printColModel" :selectable="() => { return false }" :after-load="afterLoad3" :show-pager="false" :row-datas="printDatas" :min-height="200"/>
        </el-tab-pane>
      </el-tabs>
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.approvalLog_Msg2') }}：</h4>
      <el-descriptions class="margin-top" :column="1" size="small" border>
        <el-descriptions-item label-class-name="my-label1" content-class-name="my-content1">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg13') }}
          </template>
          <span>{{ printApplyFilter(detailObj.printApplyFlag) }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions v-show="detailObj.printApplyFlag!=='1'" class="margin-top" :column="2" size="small" border>
        <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg14') }}
          </template>
          <span v-if="detailObj.printTimes==='16777215'">{{ $t('pages.noLimit') }}</span><span v-else>{{ detailObj.printTimes }}{{ $t('pages.openTimes2') }}</span>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg16') }}
          </template>
          <span v-if="detailObj.printPages==='16777215'">{{ $t('pages.noLimit') }}</span><span v-else>{{ detailObj.printPages }}{{ $t('pages.approvalLog_Msg15') }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions class="margin-top" :column="1" size="small" border>
        <el-descriptions-item label-class-name="my-label1=" content-class-name="my-content1">
          <template slot="label">
            {{ $t('pages.approvalProcess_Msg24') }}
          </template>
          <div v-if="detailObj.printMinutes">
            <span v-if="detailObj.printMinutes==='16777215'">{{ $t('pages.noLimit') }}</span>
            <span v-else-if="detailObj.printMinutes==='0'">{{ $t('pages.approvalProcess_Msg28', { begin: detailObj.printBeginTime, end: detailObj.printEndTime }) }}</span>
            <span v-else>{{ $t('pages.approvalProcess_Msg25', { minutes: detailObj.printMinutes }) }}</span>
          </div>
          <div v-else><span></span></div>
        </el-descriptions-item>
      </el-descriptions>
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.approvalLog_Msg9') }}：</h4>
      <el-input
        v-model="detailObj.reqMsg"
        readonly=""
        type="textarea"
        :rows="3"
      >
      </el-input>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.approvalLog_Msg1')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="usbVisible"
      width="600px"
    >
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.deviceInfo') }}：</h4>
      <grid-table
        :col-model="usbColModel"
        row-key="USbId"
        :selectable="() => { return false }"
        :show-pager="false"
        :row-datas="usbDatas"
        :after-load="afterLoad2"
        :min-height="200"
      />
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.approvalLog_Msg2') }}：</h4>
      <el-descriptions class="margin-top" :column="1" size="small" border>
        <el-descriptions-item label-class-name="my-label1=" content-class-name="my-content1">
          <template slot="label">
            {{ $t('pages.applyType') }}
          </template>
          <div>
            <span >{{ $t('pages.behaviorApply') }} - {{ $t('pages.usbApply') }}</span>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions class="margin-top" :column="1" size="small" border>
        <el-descriptions-item label-class-name="my-label1=" content-class-name="my-content1">
          <template slot="label">
            {{ $t('pages.approvalProcess_Msg24') }}
          </template>
          <div v-if="detailObj.minutes">
            <span v-if="detailObj.minutes==='16777215'">{{ $t('pages.noLimit') }}</span>
            <span v-else-if="detailObj.minutes==='0'">{{ $t('pages.approvalProcess_Msg28', { begin: detailObj.behaviorBeginTime, end: detailObj.behaviorEndTime }) }}</span>
            <span v-else>{{ $t('pages.approvalProcess_Msg25', { minutes: detailObj.minutes }) }}</span>
          </div>
          <div v-else><span></span></div>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions class="margin-top" :column="1" size="small" border>
        <el-descriptions-item label-class-name="my-label1=" content-class-name="my-content1">
          <template slot="label">
            {{ $t('pages.approvalProcess_Msg82') }}
          </template>
          <div v-if="detailObj.usbRWAuth == '1'">
            {{ $t('pages.approvalProcess_Msg84') }}
          </div>
          <div v-else>
            {{ $t('pages.approvalProcess_Msg83') }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.approvalLog_Msg9') }}：</h4>
      <el-input
        v-model="detailObj.reqMsg"
        readonly=""
        type="textarea"
        :rows="3"
      >
      </el-input>
    </el-dialog>
  </div>
</template>

<script>
import { getLogPage, getLogDetail, exportExcel, deleteLog } from '@/api/behaviorAuditing/encryption/approvalLog'
import { getProcessById } from '@/api/dataEncryption/encryption/approvalProcess'
import moment from 'moment'
import { getCategory } from '@/api/dataEncryption/encryption/approvalProcess'
import { getFiles, getAttachFilePage, getAttachFileByRow } from '@/api/behaviorAuditing/encryption/sensitiveFileOutLog'
import ApprovalChart from '@/components/ApprovalChart'
import { getSetting } from '@/api/dataEncryption/docPemission/denseSet'

// const userOperationType = [
//   { value: 0, label: '申请人' },
//   { value: 1, label: '审批者' }
// ]

// const statusList = [
//   { value: 0, label: '正在审批' },
//   { value: 1, label: '审批通过' },
//   { value: 2, label: '审批拒绝' },
//   { value: 3, label: '流程撤销' },
//   { value: 4, label: '流程作废' }
// ]

// const levelList = [
//   { value: 0, label: '公开文件' },
//   { value: 1, label: '内部资料文件' },
//   { value: 2, label: '秘密文件' },
//   { value: 3, label: '机密文件' },
//   { value: 4, label: '绝密文件' }
// ]

// const printType = [
//   { value: 1, label: '本地打印机' },
//   { value: 2, label: '网络打印机' },
//   { value: 3, label: '共享打印机' },
//   { value: 4, label: '虚拟打印机' }
// ]

const keyValueFilter = function(options) {
  const typeKeyValue = options.reduce((acc, cur) => {
    acc[cur.value] = cur.label
    return acc
  }, {})
  return typeKeyValue
}

export default {
  name: 'ApprovalLog',
  components: { ApprovalChart },
  // filters: {
  //   printApplyFilter(status, that) {
  //     const statusMap = {
  //       0: that.$t('pages.approvalLog_Msg18'),
  //       1: that.$t('pages.approvalLog_Msg19'),
  //       2: that.$t('pages.approvalLog_Msg20')
  //     }
  //     return statusMap[status]
  //   }
  // },
  data() {
    return {
      pickerOptionsStart: {
        disabledDate: time => {
          const endDateVal = this.query.endTime
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime()
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.query.beginTime
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime()
            )
          }
        }
      },
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        beginTime: new Date(moment().startOf('day')),
        endTime: new Date(moment().endOf('day')),
        category: '',
        fileName: '',
        userId: '',
        deptId: '',
        userOperationType: 0,
        approvalStatus: '',
        timeOperationType: 0,  // 时间类型0-申请日期1-审批结束日期
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      queryAttachmentFile: { // 查询条件
        page: 1,
        processInstanceId: undefined,
        path: '/',
        childrenInclude: true
      },
      showTree: true,
      deleteable: false,
      categoryList: [],
      userOperationType: [
        { value: 0, label: this.$t('pages.approvalLog_Msg21') },
        { value: 1, label: this.$t('pages.approvalLog_Msg22') }
      ],
      timeOperationType: [
        { value: 0, label: this.$t('table.applyDate') },
        { value: 1, label: this.$t('table.applyFinishedDate') }
      ],
      logerModel: [
        { label: 'step', width: '50', formatter: this.stepFormatter },
        { prop: 'authorName', label: 'approver', width: '110' },
        { prop: 'time', label: 'pprocessTime', width: '150', formatter: (row, data) => this.timeFormatter(row, data, 'time') },
        { prop: 'message', label: 'comments', width: '150' },
        { prop: 'type', label: 'result', width: '100', formatter: this.resultFormatter },
        { prop: 'agencyApprovalAccountName', label: 'delegateStatus', width: '160', formatter: this.agencyFormatter }
      ],
      detailColModel: [
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'filePath', label: 'filePath', width: '150' },
        { prop: 'fileSize', label: 'maxFileSize2', width: '120' },
        { label: 'operate', type: 'button', width: '100', hidden: !this.hasPermission('230,321'),
          buttons: [
            { label: 'download', isShow: this.isShowDetailDownloadBtn, click: this.handleLoadDown, disabledFormatter: this.multiDownloadFormatter },
            { label: 'sensitiveMsg', isShow: data => this.hasPermission('321'), disabledFormatter: this.sensitiveFormatter, click: this.handleSensitive }
          ]
        }
      ],
      fileDecryptColModel: [
        { prop: 'fileName', label: 'fileName', width: '150' },
        // { prop: 'filePath', label: 'filePath', width: '150' },
        { prop: 'fileSize', label: 'maxFileSize2', width: '120' },
        { label: 'operate', type: 'button', width: '100', hidden: !this.hasPermission('230'),
          buttons: [
            { label: 'download', isShow: data => this.hasPermission('230'), click: this.handleLoadDown, disabledFormatter: this.fileDecryptDownloadFormatter }
          ]
        }
      ],
      usbColModel: [
        { prop: 'UsbDriveLetter', label: 'drive', width: '80' }, // 盘符
        { prop: 'UsbName', label: 'volumeName', width: '150' }, // 设备昵称
        { prop: 'UsbNumber', label: 'pnpDeviceId', width: '150' }, // 设备编码
        { prop: 'UsbType', label: 'devType', width: '100', formatter: this.devTypeFormatter }, // 设备类型
        { prop: 'UsbDiskType', label: 'driverType', width: '100', formatter: this.driverTypeFormatter }, // 磁盘类型
        { prop: 'UsbmakerID', label: 'vid', width: '100', formatter: this.driverTypeFormatter }, // 厂商ID
        { prop: 'Usbmaker', label: 'model', width: '150' }, // 设备厂商
        { prop: 'ProductID', label: 'pid', width: '150' }, // 产品ID
        { prop: 'UsbSrcNumber', label: 'deviceSerialNumber', width: '150' }, // 设备序列号
        { prop: 'UsBCapacity', label: 'devSizeMB', width: '100' } // 容量
      ],
      dialogVisible: false,
      dialogDetail: false,
      printVisible: false,
      usbVisible: false,
      fileDecryptDialog: false, // 解密审批流程详情弹窗
      logerRowData: [],
      dialogStepsVisible: false,
      tempTask: {},
      defaultTempTask: {
        backType: 13,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      fileDatas: [],
      printDatas: [],
      usbDatas: [],
      detailObj: {},
      sensitiveObj: {},
      sensitiveObjTemp: {
        RuleName: '',
        Contents: '',
        StrategyName: '',
        AlarmLevel: undefined,
        ContStatus: undefined
      },
      activeName: 'first',
      noLimit: false,
      hasAttachmentDetailFtpId: true, // 附件明细上传至目标ftp的id  可用来判断版本2022-08-02第三季度版本才有文件外发审批的申请文件这个值存在即代表是2022-08-02后的版本
      sensitiveVisible: false,
      levelOptions: {
        1: this.$t('pages.severityOptions1'),
        2: this.$t('pages.severityOptions2'),
        3: this.$t('pages.severityOptions3'),
        4: this.$t('pages.severityOptions4')
      },
      selection: [],
      statusOptions: {
        'completed': this.$t('pages.approvalLog_Msg27'),
        'true': this.$t('pages.approvalLog_Msg26'),
        'false': this.$t('pages.approvalLog_Msg28'),
        'back': this.$t('pages.approvalLog_return'),
        'revoke': this.$t('pages.approvalLog_Msg29'),
        'cancel': this.$t('pages.approvalLog_Msg30')
      },
      denseList: []
    }
  },
  computed: {
    printApplyFilter() {
      const statusMap = {
        0: this.$t('pages.approvalLog_Msg18'),
        1: this.$t('pages.approvalLog_Msg19'),
        2: this.$t('pages.approvalLog_Msg20')
      }
      return function(status) {
        return statusMap[status]
      }
    },
    gridTable() {
      return this.$refs['logList']
    },
    colModel() {
      const defaultModel = [
        { prop: 'startUserName', label: 'applicant', width: '120', type: 'showDetail', searchParam: 'startUserId', searchType: 'user' },
        { prop: 'groupName', label: 'subordinateGroup1', width: '120', type: 'showDetail', searchParam: 'groupId', searchType: 'department' },
        { prop: 'processDefinitionName', label: 'floatName', width: '150' },
        { prop: 'startTime', label: 'applyDate', width: '160', formatter: (row, data) => this.timeFormatter(row, data, 'startTime') },
        { prop: 'endTime', label: 'applyFinishedDate', width: '160', formatter: (row, data) => this.timeFormatter(row, data, 'endTime') },
        { prop: 'duringTime', label: 'duringTime', width: '150' },
        { prop: 'reqMsg', label: 'reason', width: '180' },
        { prop: 'status', label: 'status', width: '100', formatter: this.statusFormatter },
        {
          label: 'operate',
          type: 'button',
          fixed: 'right',
          fixedWidth: '250',
          hidden: !this.hasPermission('208,230'),
          buttons: [
            {
              label: 'applyComments', // 审批人及批语
              click: this.handleDetail,
              isShow: data => this.hasPermission('208') && data.status != null || data.status != undefined
            },
            {
              label: 'processSteps',
              click: this.processSteps,
              isShow: data => this.hasPermission('208') && data.status != null || data.status != undefined
            },
            {
              label: 'download',
              click: this.handleLoadDown,
              isShow: data => this.hasPermission('230') && this.query.category !== 'sensitiveFileOutSend' && this.query.category !== 'filePrint' && this.query.category !== 'offline' && this.query.category !== 'behaviorControl' && this.query.category !== 'fileDecrypt',
              disabledFormatter: this.downloadFormatter
            },
            {
              label: 'processParameters',
              click: this.handleFileDetail,
              isShow: data => this.hasPermission('208') && (this.query.category === 'sensitiveFileOutSend' || this.query.category === 'filePrint' || this.query.category === 'behaviorControl' || this.query.category == 'fileDecrypt')
            }
          ]
        }
      ]
      const defaultModel2 = [
        { prop: 'fileName', label: 'fileName', width: '170' },
        { prop: 'fileSuffix', label: 'fileSuffix', width: '80' },
        { prop: 'fileSize', label: 'maxFileSize2', width: '120' }
      ]
      const categoryOpt = {
        'offline': [
          { prop: 'offlineBeginTime', label: 'startTime', width: '160' },
          { prop: 'offlineEndTime', label: 'endTime', width: '160' }
        ],
        'changeFileLevel': [
          { prop: 'maxSrcLevel', label: 'originalSecretLevel', width: '100', formatter: this.maxSrcLevelFormatter },
          { prop: 'dstLevel', label: 'reclassify', width: '100', formatter: this.dstLevelFormatter },
          { prop: 'lastModifyTime', label: 'lastModifyTime', width: '160' },
          ...defaultModel2
        ],
        'filePrint': [],
        'sensitiveFileOutSend': [
          { prop: 'fileName', label: 'fileName', width: '170', formatter: this.fileNameFormatter },
          { prop: 'fileSize', label: 'maxFileSize2', width: '120' }
        ],
        'behaviorControl': [],
        'fileRelieveJurisdiction': [
          { prop: 'lastModifyTime', label: 'lastModifyTime', width: '160' },
          { prop: 'srcEncryptGroup', label: 'srcEncryptGroup', width: '100' },
          { prop: 'dstEncryptGroup', label: 'dstEncryptGroup', width: '100' },
          { prop: 'hasAllUserRights', label: 'hasAllUserRights', width: '160' },
          ...defaultModel2
        ]
      }
      const model = categoryOpt[this.query.category] || [
        { prop: 'lastModifyTime', label: 'lastModifyTime', width: '160' },
        ...defaultModel2
      ]
      defaultModel.splice(6, 0, ...model)
      return defaultModel
    },
    printColModel() {
      let colModel
      if (this.activeName === 'first') {
        colModel = [
          { prop: 'fileName', label: 'printFileName', width: '150' },
          { prop: 'filePath', label: 'path', width: '150' },
          { label: 'operate', type: 'button', width: '80', hidden: !this.hasPermission('230'),
            buttons: [
              { label: 'download', click: this.handleLoadDown }
            ]
          }
        ]
      } else if (this.activeName === 'second') {
        colModel = [
          { prop: 'PrintAppName', label: 'programProcessName', width: '150' },
          { prop: 'PrintAppMemo', label: 'remark', width: '150' }
        ]
      } else {
        colModel = [
          { prop: 'PrinterName', label: 'printName', width: '150' },
          { prop: 'PrinterType', label: 'printType', width: '150', formatter: this.printerTypeFormatter }
        ]
      }
      return colModel
    }
  },
  created() {
    this.getCategory()
    this.getDenseSet()
  },
  methods: {
    getDenseSet() {
      getSetting().then(res => {
        this.denseList = res.data.denseInfoList.map(item => {
          return { value: item.encryptLevel, label: item.denseName }
        })
        this.denseList.push({ value: 255, label: this.$t('pages.commomDocuments') })
      })
    },
    selectable(row, index) {
      return !this.downloadFormatter(row) || (this.$store.getters.auditingDeleteAble && this.hasPermission('497'))
    },
    getCategory() {
      getCategory().then(res => {
        this.categoryList = res.data.map(item => {
          return {
            value: item.category,
            label: item.remark
          }
        })
        this.query.category = this.categoryList[0].value
      })
    },
    timeFormatter(row, data, timeType) {
      return !row[timeType] ? '' : row[timeType].substr(0, 10) + ' ' + row[timeType].substr(-8, 10)
    },
    resultFormatter(row, data) {
      if (row.type == 'back') {
        return this.$t('pages.backlnit')
      } else if (row.type == 'true') {
        return this.$t('pages.pass')
      } else if (row.type == 'false') {
        return this.$t('pages.refuse')
      } else if (row.type == 'autoFinish') {
        return this.$t('pages.passAndEnd')
      } else {
        return ''
      }
    },
    agencyFormatter(row, data) {
      return row.trusteed ? `${this.$t('pages.approvalLog_Msg24')}${row.agencyApprovalAccountName})` : this.$t('pages.approvalLog_Msg25')
    },
    stepFormatter(row, data) {
      return this.logerRowData.findIndex(item => item.id === row.id) + 1
    },
    statusFormatter(row, data) {
      if (row.status == undefined || row.status == null) {
        return this.$t('pages.approvalLog_Msg26')
      }
      return keyValueFilter([
        { value: 0, label: this.$t('pages.approvalLog_Msg27') },
        { value: 1, label: this.$t('pages.approvalLog_Msg26') },
        { value: 2, label: this.$t('pages.approvalLog_Msg28') },
        { value: 3, label: this.$t('pages.approvalLog_Msg29') },
        { value: 4, label: this.$t('pages.approvalLog_Msg30') },
        { value: 5, label: this.$t('pages.approvalLog_return') }
      ])[row.status]
    },
    maxSrcLevelFormatter(row, data) {
      return keyValueFilter(this.denseList)[row.maxSrcLevel]
    },
    fileNameFormatter(row, data) {
      return data || this.$t('pages.anyFiles')
    },
    dstLevelFormatter(row, data) {
      return keyValueFilter(this.denseList)[row.dstLevel]
    },
    printerTypeFormatter(row, data) {
      return keyValueFilter([
        { value: 1, label: this.$t('pages.approvalLog_Msg31') },
        { value: 2, label: this.$t('pages.approvalLog_Msg32') },
        { value: 3, label: this.$t('pages.approvalLog_Msg33') },
        { value: 4, label: this.$t('pages.approvalLog_Msg34') }
      ])[row.PrinterType]
    },
    driverTypeFormatter(row, data) {
      if (data == '1') {
        return this.$t('pages.driverType1')
      } else if (data == '2') {
        return this.$t('pages.driverType2')
      } else {
        return data
      }
    },
    devTypeFormatter(row, data) {
      if (data == '0') {
        return this.$t('pages.dictionary_Msg19')
      } else if (data == '1') {
        return this.$t('pages.dictionary_Msg21')
      } else if (data == '2') {
        return this.$t('pages.decryptKey')
      } else if (data == '3') {
        return this.$t('pages.outFileUDisk')
      } else if (data == '4') {
        return this.$t('pages.safeUDisk')
      } else if (data == '5') {
        return this.$t('pages.customProfessionalUDisk')
      } else {
        return data
      }
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getLogPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (!checkedNode) return
      if (checkedNode.type == '4') {
        this.query.deptId = checkedNode.dataId
        this.query.userId = ''
      } else {
        this.query.userId = checkedNode.dataId
        this.query.deptId = ''
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      const searchQuery = Object.assign({}, this.query)
      searchQuery.page = 1
      this.gridTable.execRowDataApi(searchQuery)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleChange(value) {
      this.gridTable.execRowDataApi(this.query)
    },
    handleDetail(row) {
      getLogDetail(row.id).then(res => {
        this.logerRowData = res.data
        this.dialogVisible = true
      })
    },
    // 流程参数查询
    handleFileDetail(row) {
      this.detailObj = row
      this.activeName = 'first'
      if (this.query.category === 'sensitiveFileOutSend') {
        if (row.attachmentDetailFtpId) {
          this.hasAttachmentDetailFtpId = true
          // const searchQuery = Object.assign({}, this.queryAttachmentFile)
          this.queryAttachmentFile.page = 1
          this.queryAttachmentFile.processInstanceId = row.id
          this.dialogDetail = true
          this.$nextTick(() => {
            this.attachmentFileGrid().execRowDataApi(this.queryAttachmentFile)
          })
        } else {
          this.hasAttachmentDetailFtpId = false
          this.detailObj = row
          this.activeName = 'first'
          getFiles({ processSerialId: row.id }).then(res => {
            this.fileDatas = res.data.approvalFileVos
            this.dialogDetail = true
          })
        }
      } else if (this.query.category === 'filePrint') {
        getFiles({ processSerialId: row.id }).then(res => {
          this.printDatas = res.data.approvalFileVos
          this.noLimit = row.fileNotLimit
          this.printVisible = true
        })
      } else if (this.query.category === 'behaviorControl') {
        this.usbDatas = this.detailObj.usbList
        this.usbVisible = true
        // this.usbChecked.push('1')
      } else if (this.query.category === 'fileDecrypt') {
        if (row.attachmentDetailFtpId) {
          this.hasAttachmentDetailFtpId = true
          // const searchQuery = Object.assign({}, this.queryAttachmentFile)
          this.queryAttachmentFile.page = 1
          this.queryAttachmentFile.processInstanceId = row.id
          this.fileDecryptDialog = true
          this.$nextTick(() => {
            this.fileDecryptAttachmentFileGrid().execRowDataApi(this.queryAttachmentFile)
          })
        } else {
          this.hasAttachmentDetailFtpId = false
          this.fileDatas.splice(0)
          this.fileDatas.push(row)
          this.activeName = 'first'
          this.fileDecryptDialog = true
          this.queryAttachmentFile.page = 1
          this.$nextTick(() => {
            this.fileDecryptAttachmentFileGrid().execRowDataApi(this.queryAttachmentFile)
          })
        }
      }
    },
    // handleFileDetail(row) {
    //   this.detailObj = row
    //   this.activeName = 'first'
    //   if (this.query.category === 'sensitiveFileOutSend') {
    //     getFiles({ processSerialId: row.id }).then(res => {
    //       this.fileDatas = res.data
    //       this.dialogDetail = true
    //     })
    //   } else {
    //     getFiles({ processSerialId: row.id }).then(res => {
    //       this.printDatas = res.data
    //       this.noLimit = row.fileNotLimit
    //       this.printVisible = true
    //     })
    //   }
    // },
    handleClick(tab) {
      if (tab.name === 'first') {
        getFiles({ processSerialId: this.detailObj.id }).then(res => {
          this.printDatas = res.data.approvalFileVos
          this.noLimit = this.detailObj.fileNotLimit
        })
      } else if (tab.name === 'second') {
        this.printDatas = this.detailObj.printApp.data
        this.noLimit = this.detailObj.printApp.isNotLimit
      } else {
        this.printDatas = this.detailObj.printer.data
        this.noLimit = this.detailObj.printer.isNotLimit
      }
    },
    // download(row) {
    //   // 表单提交
    //   const form = document.createElement('form')
    //   form.action = '/api/log/approval/file/download'
    //   form.method = 'post'
    //   // form.target = "blank";
    //   Object.keys(row).forEach(item => {
    //     const input = document.createElement('input')
    //     input.type = 'hidden'
    //     input.name = item
    //     input.value = row[item]
    //     form.appendChild(input)
    //   })
    //   document.body.appendChild(form)
    //   form.submit()
    //   document.body.removeChild(form)
    // },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.ftpId
      if (this.query.category == 'sensitiveFileOutSend' || this.query.category == 'filePrint') {
        this.tempTask.fileGuid = row.uploadFileGuid
      } else if (this.query.category == 'fileDecrypt' && this.hasAttachmentDetailFtpId) { // 兼容旧版本，旧版本的文件id为savePath,用attachmentDetailFtpId来区分旧版本
        this.tempTask.fileGuid = row.uploadFileGuid
      } else {
        this.tempTask.fileGuid = row.savePath
      }
      // this.tempTask.fileGuid = this.query.category !== 'sensitiveFileOutSend' && this.query.category !== 'filePrint' ? row.savePath : row.uploadFileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    downloadFormatter(data, btn) {
      return !data.fileState || data.fileState == 1
    },
    isShowDetailDownloadBtn(data) {
      if (!this.hasPermission('230')) {
        return false
      }
      if (this.$store.getters.desensitizeContentAble) {
        const notExistSensitiveInfo = this.sensitiveFormatter(data)
        // 脱敏显示的时候
        return notExistSensitiveInfo
      }
      return true
    },
    multiDownloadFormatter(data, btn) {
      return !data.uploadFileGuid
    },
    fileDecryptDownloadFormatter(data, btn) {
      if (data.fileState == 0 || data.uploadFileGuid) {
        return false
      } else {
        return true
      }
    },
    processSteps(row) {
      getProcessById(row.processDefinitionId).then(res => {
        this.dialogStepsVisible = true
        const data = res.data
        this.approverList = data.steps
        this.initiatorList = data.assignedUserOrDeptVOs
      })
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
      const q = Object.assign({ exportType }, this.query)
      q.limit = this.$refs['logList'].total
      q.tableKey += '-' + q.category
      return exportExcel(q)
    },
    attachmentFileGrid() {
      return this.$refs['attachmentFileList']
    },
    fileDecryptAttachmentFileGrid() {
      return this.$refs['fileDecryptAttachmentFileList']
    },
    attachmentFileRowDataApi: function(option) {
      if (this.hasAttachmentDetailFtpId) {
        const searchQuery = Object.assign({}, this.queryAttachmentFile, option)
        return getAttachFilePage(searchQuery)
      } else { 
        return getAttachFileByRow(this.fileDatas)
      }
    },
    handleSensitive(row) {
      this.sensitiveObj = Object.assign({}, this.sensitiveObjTemp, row.sensitiveDataInfo)
      this.sensitiveVisible = true
    },
    sensitiveFormatter(data, btn) {
      return !data.sensitiveDataInfo || !data.sensitiveDataInfo.ContStatus || ((data.sensitiveDataInfo.ContStatus & 1) == 0)
    },
    selectionChangeEnd(rowDatas) {
      this.selection = rowDatas
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    afterLoad2: function(rowData, grid) {
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          if (item.IsUsed == 'true') {
            grid.toggleRowSelection(item)
          }
        })
      })
    },
    afterLoad3: function(rowData, grid) {
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          if (item.PrintIsUsed == 'true') {
            grid.toggleRowSelection(item)
          }
        })
      })
    },
    afterLoad4: function(rowData, grid) {
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          if (item.isSelected == 'true') {
            grid.toggleRowSelection(item)
          }
        })
      })
    },
    handleDelete() {
      let tipMsg = this.$t('pages.validateMsg_deleteMsg')
      if (this.gridTable.getSelectedDatas().filter(item => item.status == 0).length > 0) {
        tipMsg = this.$t('pages.approvalProcess_Msg88')
      }
      this.$confirmBox(tipMsg, this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.tableBox{
  height: calc(100% - 38px);
}
</style>

<style lang="scss">
.my-label{
  width: 23%;
}
.my-content{
  width: 27%;
}
.my-label1{
  width: 23%;
}
.my-content1{
  width: 77%;
}
</style>

