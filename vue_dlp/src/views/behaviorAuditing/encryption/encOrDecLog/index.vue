<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.opType" :value="query.opType">
          <span>{{ $t('pages.encryptionDecryptionType') }}：</span>
          <el-select v-model="query.opType" style="width: 150px">
            <el-option :label="$t('pages.collectAll')" value=""/>
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'353'" :multi-dataset="hasPermission('209')" :request="handleExport"/>
        <audit-log-delete
          v-if="$store.getters.auditingDeleteAble"
          slot="append"
          v-permission="'464'"
          :selection="selection"
          :delete-log="deleteLog"
          :table-getter="gridTable"
        />
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="$store.getters.auditingDeleteAble && hasPermission('464')" :row-data-api="rowDataApi" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.encOrDecLogDetails')"
      :visible.sync="detailDialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.encryptionDecryption')">
            {{ typeFormatter(rowDetail, rowDetail.opType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fileAllNum')">
            {{ rowDetail.fileTotal }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.successCount')">
            {{ rowDetail.successCount }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.errorCount')">
            {{ rowDetail.errorCount }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <Form ref="detailForm" label-position="right" label-width="100px" style="margin-top:2px;">
        {{ $t('pages.filePath') }}：
        <el-input v-model="queryDetail.fileName" style="width: 200px;"/>
        {{ $t('pages.opResult') }}：
        <!-- <el-select v-model="queryDetail.opResult" style="width: 183px">
          <el-option :label="$t('pages.collectAll')" value=""/>
          <el-option v-for="item in opResultOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select> -->
        <el-cascader
          ref="cascade"
          clearable
          :show-all-levels="false"
          :props="{ checkStrictly: true, expandTrigger: 'hover', emitPath: false }"
          :options="opResultCascaderOptions"
          @change="handleOpResultChange"
          @visible-change="handleChange"
          @expand-change="handleChange"
        >
        </el-cascader>
        <el-button type="primary" icon="el-icon-search" size="mini" style="margin-bottom: 0" @click="handleDetailFilter">
          {{ $t('table.search') }}
        </el-button>
        <!--<el-button icon="el-icon-delete" size="mini" :disabled="!detailDeleteable" @click="detailDelete">
          {{ $t('button.delete') }}
        </el-button>-->
        <grid-table
          ref="detailGrid"
          :height="240"
          :col-model="detailColModel"
          :row-data-api="detailRowApi"
          @selectionChangeEnd="detailSelectionChangeEnd"
        />
      </Form>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/behaviorAuditing/encryption/encOrDecLog'
import { getLogDetailPage, deleteLogDetail } from '@/api/behaviorAuditing/encryption/encOrDecLogDetail'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'EncOrDecLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'opType', label: 'encryptionDecryption', width: '150', formatter: this.typeFormatter },
        { prop: 'fileTotal', label: 'fileAllNum', width: '150' },
        { prop: 'successCount', label: 'successCount', width: '150' },
        { prop: 'errorCount', label: 'errorCount', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('209'),
          buttons: [
            { label: 'detail', click: this.handleDetail }
          ]
        }
      ],
      detailColModel: [
        { prop: 'opResult', label: 'opResult', width: '150', formatter: this.opResultFormatter },
        { label: 'fileName', width: '150', formatter: this.fileNameFormatter },
        { prop: 'fileName', label: 'filePath', width: '150' }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        opType: '',
        isTimes: false,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      queryDetail: { // 查询条件
        page: 1,
        mainGuid: '',
        fileName: '',
        opResult: '',
        createTime: '',
        isTimes: false,
        searchReport: 1
      },
      showTree: true,
      selection: [],
      detailDeleteable: false,
      detailDialogVisible: false,
      types: {
        1: this.$t('pages.loadTypeOptions3'),
        2: this.$t('pages.loadTypeOptions2')
      },
      typeOptions: [
        { value: '1', label: this.$t('pages.loadTypeOptions3') },
        { value: '2', label: this.$t('pages.loadTypeOptions2') }
      ],
      // 作了统一修改的处理，详情见DLP加解密错误码调整方案,方案指出调整后，不做原状态码和现状态码的兼容，先保留encResultOptions和decResultOptions，以防后续可能兼容使用
      opResultOptions: [
        { value: '0', label: this.$t('text.success') },
        { value: '1', label: this.$t('pages.diskScanSens_Msg2') },
        { value: '2', label: this.$t('pages.diskScanSens_Msg20') },
        { value: '3', label: this.$t('pages.diskScanSens_Msg21') },
        { value: '4', label: this.$t('pages.diskScanSens_Msg22') },
        { value: '5', label: this.$t('pages.diskScanSens_Msg23') },
        { value: '6', label: this.$t('pages.diskScanSens_Msg7') },
        { value: '7', label: this.$t('pages.diskScanSens_Msg24') },
        { value: '8', label: this.$t('pages.diskScanSens_Msg25') },
        { value: '9,10,11', label: this.$t('pages.encOrDecLog_Msg5') },
        { value: '12', label: this.$t('pages.diskScanSens_Msg26') },
        { value: '100', label: this.$t('pages.diskScanSens_Msg33') },
        { value: '1000', label: this.$t('pages.diskScanSens_Msg27') },
        { value: '1001', label: this.$t('pages.diskScanSens_Msg28') },
        { value: '1002', label: this.$t('pages.diskScanSens_Msg29') },
        { value: '1003', label: this.$t('pages.diskScanSens_Msg30') },
        { value: '1004', label: this.$t('pages.diskScanSens_Msg31') },
        { value: '1005', label: this.$t('pages.diskScanSens_Msg32') }
      ],
      opResultCascaderOptions: [
        { value: '-1', label: this.$t('pages.collectAll') },
        { value: '0', label: this.$t('text.success') },
        { value: '1,2,3,4,5,6,7,8,9,10,11,12,100,1000,1001,1002,1003,1004,1005',
          label: this.$t('text.fail'),
          children: [
            { value: '1', label: this.$t('pages.diskScanSens_Msg2') },
            { value: '2', label: this.$t('pages.diskScanSens_Msg20') },
            { value: '3', label: this.$t('pages.diskScanSens_Msg21') },
            { value: '4', label: this.$t('pages.diskScanSens_Msg22') },
            { value: '5', label: this.$t('pages.diskScanSens_Msg23') },
            { value: '6', label: this.$t('pages.diskScanSens_Msg7') },
            { value: '7', label: this.$t('pages.diskScanSens_Msg24') },
            { value: '8', label: this.$t('pages.diskScanSens_Msg25') },
            { value: '9,10,11', label: this.$t('pages.encOrDecLog_Msg5') },
            { value: '12', label: this.$t('pages.diskScanSens_Msg26') },
            { value: '100', label: this.$t('pages.diskScanSens_Msg33') },
            { value: '1000', label: this.$t('pages.diskScanSens_Msg27') },
            { value: '1001', label: this.$t('pages.diskScanSens_Msg28') },
            { value: '1002', label: this.$t('pages.diskScanSens_Msg29') },
            { value: '1003', label: this.$t('pages.diskScanSens_Msg30') },
            { value: '1004', label: this.$t('pages.diskScanSens_Msg31') },
            { value: '1005', label: this.$t('pages.diskScanSens_Msg32') }
          ]
        }
      ],
      encResultOptions: [
        { value: '0', label: this.$t('text.success') },
        { value: '1', label: this.$t('pages.diskScanSens_Msg2') },
        { value: '2', label: this.$t('pages.encOrDecLog_Msg') },
        { value: '3', label: this.$t('pages.encOrDecLog_Msg1') },
        { value: '4', label: this.$t('pages.diskScanSens_Msg5') },
        { value: '5', label: this.$t('pages.encOrDecLog_Msg2') },
        { value: '6', label: this.$t('pages.encOrDecLog_Msg3') },
        { value: '7,8,9', label: this.$t('pages.encOrDecLog_Msg5') },
        { value: '20', label: this.$t('pages.encOrDecLog_Msg6') },
        { value: '21', label: this.$t('pages.encOrDecLog_Msg10') },
        { value: '100', label: this.$t('pages.encOrDecLog_Msg12') },
        { value: '101', label: this.$t('pages.encOrDecLog_Msg13') },
        { value: '65535', label: this.$t('pages.encOrDecLog_Msg14') }
      ],
      decResultOptions: [
        { value: '0', label: this.$t('text.success') },
        { value: '1', label: this.$t('pages.diskScanSens_Msg2') },
        { value: '2', label: this.$t('pages.encOrDecLog_Msg') },
        { value: '4', label: this.$t('pages.diskScanSens_Msg5') },
        { value: '5', label: this.$t('pages.encOrDecLog_Msg4') },
        { value: '7,8,9', label: this.$t('pages.encOrDecLog_Msg5') },
        { value: '11', label: this.$t('pages.encOrDecLog_Msg7') },
        { value: '15', label: this.$t('pages.encOrDecLog_Msg8') },
        { value: '17', label: this.$t('pages.encOrDecLog_Msg9') },
        { value: '20', label: this.$t('pages.encOrDecLog_Msg6') },
        { value: '21', label: this.$t('pages.encOrDecLog_Msg11') },
        { value: '100', label: this.$t('pages.encOrDecLog_Msg12') },
        { value: '101', label: this.$t('pages.encOrDecLog_Msg13') },
        { value: '65535', label: this.$t('pages.encOrDecLog_Msg14') }
      ],
      rowDetail: {},
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    detailTable() {
      return this.$refs['detailGrid']
    },
    detailSelectionChangeEnd: function(rowDatas) {
      this.detailDeleteable = rowDatas && rowDatas.length > 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleDetailFilter() {
      this.queryDetail.page = 1
      this.detailTable().execRowDataApi(this.queryDetail)
    },
    handleDetail(row) {
      this.queryDetail.page = 1
      this.queryDetail.fileName = ''
      this.queryDetail.opResult = ''
      this.queryDetail.mainGuid = row.guid
      this.queryDetail.createDate = row.createTime
      this.queryDetail.searchReport = this.query.searchReport
      this.detailDialogVisible = true
      this.$nextTick(() => {
        this.$refs.cascade.handleClear()
        this.detailTable().execRowDataApi(this.queryDetail)
      });
      this.rowDetail = row;
    },
    detailRowApi: function(option) {
      const searchQuery = Object.assign({}, this.queryDetail, option)
      return getLogDetailPage(searchQuery)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    detailDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.detailTable().getSelectedIds()
        deleteLogDetail({ ids: toDeleteIds.join(',') }).then(respond => {
          this.detailTable().deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    typeFormatter: function(row, data) {
      return this.types[data]
    },
    opResultFormatter: function(row, data) {
      for (let i = 0; i < this.opResultOptions.length; i++) {
        const option = this.opResultOptions[i]
        if (option.value.match('^' + data + '$|^' + data + ',|,' + data + ',|,' + data + '$')) {
          return option.label
        }
      }
      return ''
    },
    fileNameFormatter: function(row, data) {
      if (row.fileName != null && row.fileName != '') {
        if (row.fileName.lastIndexOf('\\') >= 0) {
          return row.fileName.substr(row.fileName.lastIndexOf('\\') + 1, row.fileName.length)
        } else if (row.fileName.startsWith('/')) {
          // linux/mac 路径适配
          return row.fileName.split('/').pop();
        }
        return row.fileName
      }
      return ''
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return exportExcel({ exportType, ...this.query })
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '209', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    handleChange() {
      const that = this;
      setTimeout(function() {
        document.querySelectorAll('.el-cascader-node__label').forEach((el) => {
          el.onclick = function() {
            // el-cascader默认选中后不关闭popover，
            // el-cascader选项自带单选按钮，通过样式display:none隐藏,
            // 拿到被隐藏的el-cascader自带的单选按钮，点击后会选中，
            // 选中后关闭popover
            this.previousElementSibling.click();
            that.$refs.cascade.dropDownVisible = false;
          };
        });
      }, 10);
    },
    handleOpResultChange(value) {
      if (value === '-1') {
        this.queryDetail.opResult = ''
      } else {
        this.queryDetail.opResult = value
      }
    }
  }
}
</script>

<style lang="scss">
/* el-cascader父子节点取消关联（:props="{ checkStrictly: true }），
   可选择任意一级选项时，隐藏单选radio样式 */
.el-cascader-panel .el-radio {
  display: none;
}
</style>

<style lang="scss" scoped>
.el-cascader {
  >>>.el-input {
    .el-input__inner:read-only {
      background-color: #f5f5f5
    }
  }
}
</style>
