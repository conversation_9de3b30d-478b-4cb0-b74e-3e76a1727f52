<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree
        ref="strategyTargetTree"
        :showed-tree="['terminal', 'user', 'flow']"
        :other-tab-panes="flowPanes"
        is-log-terminal-tree
        @data-change="strategyTargetNodeChange"
      />
    </div>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.lossType" :value="query.lossType">
          <span>{{ $t('pages.outwardRoute') }}：</span>
          <el-select v-model="query.lossType" clearable is-filter :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(val,key,i) in lossMap" :key="i" :label="val" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('pages.fileName') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'368'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'471'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" :col-model="colModel" row-key="logId" :default-sort="{ prop: 'createTime' }" :row-data-api="rowDataApi" :multi-select="$store.getters.auditingDeleteAble && hasPermission('471')" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.approvalDetails')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogDetail"
      width="600px"
    >
      <h4 style="margin-top:0"><i class="el-icon-caret-right"></i>{{ $t('pages.approvalLog_Msg21') }}：{{ detailObj.startUserName }}</h4>
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.approver') }}：{{ detailObj.auditNames }}</h4>
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.sensitiveFileOutLog_Msg') }}：
        <audit-file-downloader
          ref="auditFileDownloader"
          v-permission="'215'"
          style="margin: -6px 0 0 0"
          :name="$t('pages.approvalOutFiles')"
          topic="outgoingFile"
          :selection="fileSelection"
          :before-download="beforeDownload"
        />
      </h4>
      <grid-table
        :col-model="detailColModel"
        :height="100"
        :multi-select="true"
        :selectable="selectable"
        :show-pager="false"
        :row-datas="fileDatas"
        @selectionChangeEnd="fileSelectionChangeEnd"
      />
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.approvalLog_Msg2') }}：</h4>
      <el-descriptions class="margin-top" :column="2" size="small" border>
        <el-descriptions-item>
          <template slot="label">
            {{ $t('pages.approvalLog_Msg3') }}
          </template>
          <span v-if="detailObj.sendTimes==0">{{ $t('pages.noLimit') }}</span><span v-else>{{ detailObj.sendTimes }}</span>
        </el-descriptions-item>

        <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg4') }}
          </template>
          <span v-if="detailObj.notLimitFile==1">{{ $t('pages.noLimit') }}</span><span v-else>{{ $t('pages.limit') }}</span>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg5') }}
          </template>
          <span v-if="detailObj.autoDecrypt==1">{{ $t('text.yes') }}</span><span v-else>{{ $t('text.no') }}</span>
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg6') }}
          </template>
          {{ detailObj.sendBeginTime }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label" :span="2" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg7') }}
          </template>
          {{ detailObj.sendEndTime }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="my-label" :span="2" content-class-name="my-content">
          <template slot="label">
            {{ $t('pages.approvalLog_Msg8') }}
          </template>
          <el-tag v-for="(item,index) in detailObj.lossTypes" :key="index" size="small" style="margin:0 3px 3px 0">{{ item }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
      <h4><i class="el-icon-caret-right"></i>{{ $t('pages.approvalLog_Msg9') }}：</h4>
      <el-input
        v-model="detailObj.reqMsg"
        readonly=""
        type="textarea"
        :rows="3"
      >

      </el-input>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getFlowTree, getLogPage, getDetail, getFiles, exportExcel, deleteLog } from '@/api/behaviorAuditing/encryption/sensitiveFileOutLog'
import { listSaleModuleId } from '@/api/system/terminalManage/moduleConfig'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

function getProcessDefinitionIds(children) {
  let temp = []
  const rev = (data) => {
    data.forEach(item => {
      if (item.children) {
        rev(item.children)
      }
      temp.push(item.dataId)
    })
    return temp
  }
  temp = rev(children)
  return temp
}

export default {
  name: 'SensitiveFileOutLog',
  data() {
    // const lossMap = {
    //   2: this.$t('pages.usbCopyFile'),
    //   3: this.$t('pages.usbCutFile'),
    //   // 4: this.$t('pages.usbSaveFile'),
    //   // 5: this.$t('pages.stgLabelFilePrintContent'),
    //   7: this.$t('pages.stgLabelRemoteShareFile'),
    //   9: this.$t('pages.stgLabelImSendFile'),
    //   13: this.$t('pages.stgLabelCdBurnFile'),
    //   15: this.$t('pages.stgLabelWebUploadFile'),
    //   // 17: this.$t('pages.stgLabelLocalShareFile'),
    //   18: this.$t('pages.stgLabelMail'),
    //   20: this.$t('pages.stgLabelBluetooth'),
    //   0: this.$t('pages.stgLabelFtpUploadFile')
    // }
    return {
      flowPanes: [
        {
          name: 'flow',
          label: this.$t('components.process'),
          type: '', // 操作员组的类型编号，与服务端一一对应
          treeData: [], //
          getTreeData: () => {
            return []
          },
          expandedKeys: [], // 默认展开的节点
          iconOption: {
            'sensitiveFileOutSend': 'process'
          }
        }
      ],
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'filePath', label: 'filePath', width: '150' },
        { prop: 'lossType', label: 'outwardRoute', width: '120', formatter: this.lossTypeFormatter },
        { label: 'operate', type: 'button', width: '80', fixed: 'right', hidden: !this.hasPermission('214,215'),
          buttons: [
            { label: 'approvalDetails', click: this.handleDetail, isShow: () => this.hasPermission('214') },
            // 下载在审批详情里面
            { label: 'download', click: this.handleLoadDown, isShow: () => false, disabledFormatter: this.downloadFormatter, title: this.$t('pages.fileOutgoingLog_Msg10') }
          ]
        }
      ],
      detailColModel: [
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'filePath', label: 'filePath', width: '150' },
        { prop: 'fileSize', label: 'maxFileSize2', width: '120' },
        { label: 'operate', type: 'button', fixedWidth: '50', hidden: !this.hasPermission('215'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter }
          ]
        }
      ],
      selection: [],
      fileSelection: [],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        processDefinitionIds: undefined,
        processDefinitionId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        fileName: '',
        lossType: undefined,
        sortName: 'createTime',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      dialogDetail: false,
      percentOptions: [
        { status: 0, percent: 0 },
        { status: 1, percent: 1 },
        { status: 5, percent: 50 },
        { status: 6, percent: 100 }
      ],
      tempTask: {},
      defaultTempTask: {
        backType: 14,
        type: 2,
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      detailObj: {},
      fileDatas: [],
      lossMap: {},
      moduleIds: [],
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    this.listModule()
    getFlowTree().then(res => {
      const pane = this.flowPanes[0]
      this.$set(pane, 'treeData', res.data)
    })
    addViewVideoBtn(this, () => this.hasPermission('214'))
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    async listModule() {
      // 获取开通的销售模块
      await listSaleModuleId().then(resp => {
        this.moduleIds.splice(0, this.moduleIds.length, ...resp.data)
      })
      // 模块映射校验
      // U盘管控
      if (this.isInArray(this.moduleIds, 16)) {
        this.$set(this.lossMap, 2, this.$t('pages.usbCopyFile'))
        this.$set(this.lossMap, 3, this.$t('pages.usbCutFile'))
      }
      // 打印管控
      // if (this.isInArray(this.moduleIds, 35)) {
      // }
      // 共享管控
      if (this.isInArray(this.moduleIds, 19)) {
        this.$set(this.lossMap, 7, this.$t('pages.stgLabelRemoteShareFile'))
      }
      // 即时通讯管控
      if (this.isInArray(this.moduleIds, 36)) {
        this.$set(this.lossMap, 9, this.$t('pages.stgLabelImSendFile'))
      }
      // 刻录机使用管控
      if (this.isInArray(this.moduleIds, 18)) {
        this.$set(this.lossMap, 13, this.$t('pages.stgLabelCdBurnFile'))
      }
      // 网页上传文件管控
      if (this.isInArray(this.moduleIds, 31)) {
        this.$set(this.lossMap, 15, this.$t('pages.stgLabelWebUploadFile'))
      }
      // 邮件管控
      if (this.isInArray(this.moduleIds, 34)) {
        this.$set(this.lossMap, 18, this.$t('pages.stgLabelMail'))
      }
      // 蓝牙文件管控
      if (this.isInArray(this.moduleIds, 17)) {
        this.$set(this.lossMap, 20, this.$t('pages.stgLabelBluetooth'))
      }
      // 网络管控
      if (this.isInArray(this.moduleIds, 32)) {
        this.$set(this.lossMap, 0, this.$t('pages.stgLabelFtpUploadFile'))
        this.$set(this.lossMap, 32, this.$t('pages.stgLabelRemoteOutgoingFile'))
      }
      // 蓝牙文件管控 MTP外发文件
      if (this.isInArray(this.moduleIds, 17)) {
        this.$set(this.lossMap, 29, this.$t('pages.stgLabelMtpSendFile'))
      }
    },
    // 校验数组中是否存在某个元素
    isInArray(arr, value) {
      for (let i = 0; i < arr.length; i++) {
        if (value == arr[i]) {
          return true
        }
      }
      return false
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.query.processDefinitionId = undefined
        this.query.processDefinitionIds = undefined
        if (tabName === 'flow') {
          if (checkedNode.children) {
            this.query.processDefinitionIds = getProcessDefinitionIds(checkedNode.children).toString()
          } else {
            this.query.processDefinitionId = checkedNode.dataId
          }
        } else {
          this.query.objectType = checkedNode.type
          this.query.objectId = checkedNode.dataId
        }
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.query.processDefinitionId = undefined
        this.query.processDefinitionIds = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    selectionChangeEnd(selection) {
      this.selection = selection
    },
    fileSelectionChangeEnd(selection) {
      this.fileSelection = selection
    },
    selectable(row, index) {
      return !!(row.uploadFileGuid && (row.devId || row.ftpId))
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId ? row.devId : row.ftpId
      this.tempTask.fileGuid = row.uploadFileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    downloadFormatter(data, btn) {
      return !data.uploadFileGuid
    },
    lossTypeFormatter: function(row, data) {
      return this.lossMap[data]
    },
    handleDetail(row) {
      const detailQuery = {
        processDefinitionId: row.processDefinitionId,
        processSerialId: row.processSerialId ? row.processSerialId : null,
        includeAuditDetail: true,
        userOperationType: 0,
        category: 'sensitiveFileOutSend'
      }
      const fileQuery = {
        processSerialId: row.processSerialId ? row.processSerialId : null,
        infoId: row.processGuid
      }
      getFiles(fileQuery).then(res => {
        let i = 1;
        (res.data.approvalFileVos || []).forEach(item => {
          item.id = i++;
        });
        this.fileDatas = res.data.approvalFileVos
        if (!detailQuery.processSerialId) {
          detailQuery.processSerialId = res.data.processInstanceId
        }
        getDetail(detailQuery).then(res => {
          // 可能出现的情况 独立审批卸载重装了，以前的记录都不在了
          if (!res.data || res.data.total == 0) {
            this.$message({
              message: this.$t('pages.noApprovalInfo'),
              type: 'error',
              duration: 2000
            })
            return
          }
          this.detailObj = res.data.items[0]
          this.dialogDetail = true
        })
      }).catch(e => {})
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '214', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss">
.my-label{
  width: 130px
}
.my-content{
  width: 150px;
}
</style>
