<template>
  <div class="table-container">
    <grid-table ref="scanDetailList" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="false" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
  </div>
</template>

<script>
import { getScanDetailPage } from '@/api/behaviorAuditing/encryption/diskScanSelfCheckLog'
import { enableStgDelete, objectFormatter } from '@/utils'

export default {
  name: 'DiskScanSelfCheckDetail',
  data() {
    return {
      colModel: [
        { prop: 'guid', label: 'taskNum', fixedWidth: '150', sort: true },
        { prop: 'terminalName', label: 'terminalName', width: '150' },
        { prop: 'userName', label: 'userName1', width: '150' },
        /* { prop: 'opType', label: 'operateType', width: '150', formatter: this.opTypeFormatter }, */
        { prop: 'createTime', label: 'startTime', width: '180' },
        { prop: 'endTime', label: 'endTime', width: '180' },
        { prop: 'status', label: 'taskStatus', width: '100', formatter: this.statusFormatter },
        { prop: 'fileTotal', label: 'totalNumberScan', width: '110' },
        { prop: 'dealSuc', label: 'numberSuccessProcess', width: '100' },
        { prop: 'dealFail', label: 'numberFailProcess', width: '100' },
        { prop: 'sensFile', label: 'numberSensitive', width: '100' },
        { prop: 'nonSensFile', label: 'numberNonSensitive', width: '110' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      statusOptions: { 0: this.$t('pages.startScan'), 1: this.$t('pages.endScan') },
      opTypeOptions: { 5: this.$t('pages.diskScan_Msg3'), 3: this.$t('pages.diskScan_Msg4'), 1: this.$t('pages.fullDecryption'), 2: this.$t('pages.globalEncryption')/*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/ }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['scanDetailList']
    }
  },
  created() {
  },
  activated() {

  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getScanDetailPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    opTypeFormatter(row, data) {
      row.opType = row.opType + ''
      return this.opTypeOptions[data]
    },
    statusFormatter: function(row, data) {
      return this.statusOptions[data]
    }
  }
}
</script>
