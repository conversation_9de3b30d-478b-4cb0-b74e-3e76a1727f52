<template>
  <div class="table-container">
    <grid-table ref="sensitiveList" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="false" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
  </div>
</template>

<script>
import { getSensitivePage } from '@/api/behaviorAuditing/encryption/diskScanSelfCheckLog'
import { enableStgDelete, objectFormatter } from '@/utils'
import { getDictLabel } from '@/utils/dictionary'

export default {
  name: 'DiskScanSelfCheckSens',
  props: {
    severityOptions: {
      type: Object,
      default() {
        return {}
      }
    },
    lossTypeOptions: {
      type: Array,
      default() {
        return []
      }
    },
    opTypeOptions: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'guid', label: 'taskNum', fixedWidth: '150', sort: true },
        { prop: 'createTime', label: 'operateTime', width: '150' },
        { prop: 'terminalName', label: 'terminalName', width: '150' },
        { prop: 'userName', label: 'userName1', width: '150' },
        /* { prop: 'opType', label: 'operateType', width: '150', formatter: this.opTypeFormatter }, */
        // { prop: 'lossType', label: '泄露方式', width: '150', formatter: this.lossTypeFormatter },
        { prop: 'severity', label: 'severity', width: '150', formatter: this.severityFormatter },
        { prop: 'content', label: 'sensitiveContent', width: '150' },
        { prop: 'fileName', label: 'fileName', width: '150' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['sensitiveList']
    }
  },
  created() {
  },
  activated() {

  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getSensitivePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    searchData(query) {
      this.query = query
      this.gridTable.execRowDataApi(this.query)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    opTypeFormatter(row, data) {
      row.opType = row.opType + ''
      return this.opTypeOptions[data]
    },
    lossTypeFormatter: function(row, data) {
      return getDictLabel(this.lossTypeOptions, data)
    },
    severityFormatter: function(row, data) {
      return this.severityOptions[data]
    }
  }
}
</script>
