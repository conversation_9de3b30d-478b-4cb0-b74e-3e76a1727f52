<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <TimeQuery @getTimeParams="getTimeParams"/>
        <!-- <span>
          <label>{{ $t('pages.operateType') }}：</label>
          <el-select v-model="query.opType" style="width: 150px" clearable>
            <el-option v-for="(label, value) in opTypeOptions" :key="value" :label="label" :value="value"></el-option>
          </el-select>
        </span> -->
        <span>
          <label v-show="showOnTab('logTab')">{{ $t('pages.taskStatus') }}：</label>
          <el-select v-show="showOnTab('logTab')" v-model="query.keyword2" style="width: 150px" clearable>
            <el-option v-for="(label, key) in scanStatusOptions" :key="key" :label="label" :value="key"></el-option>
          </el-select>
        </span>
        <span>
          <label v-show="showOnTab('encOrDecTab')">{{ $t('pages.result') }}：</label>
          <el-select v-show="showOnTab('encOrDecTab')" v-model="query.result" style="width: 150px" clearable>
            <el-option v-for="(label, value) in encOrDecResults" :key="value" :label="label" :value="value"></el-option>
          </el-select>
        </span>
        <span>
          <label v-show="showOnTab('sensTab')">{{ $t('pages.severity') }}：</label>
          <el-select v-show="showOnTab('sensTab')" v-model="query.severity" style="width: 150px" clearable>
            <el-option v-for="(label, value) in severityOptions" :key="value" :label="label" :value="value"></el-option>
          </el-select>
        </span>
        <!-- <span>
          <label v-show="showOnTab('sensTab')">泄露方式：</label>
          <el-select v-show="showOnTab('sensTab')" v-model="query.lossType" style="width: 150px" clearable>
            <el-option v-for="(item, index) in lossTypeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </span> -->

        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <audit-log-exporter multi-dataset :request="handleExport"/>
        <!--<audit-log-delete v-if="$store.getters.auditingDeleteAble" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>-->
      </div>
      <el-tabs ref="tabs" v-model="tabName" type="card" style="height: calc(100% - 40px);" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.scanLog')" name="logTab">
          <disk-scan-self-check-detail ref="scanLog"></disk-scan-self-check-detail>
        </el-tab-pane>
        <!-- <el-tab-pane :label="$t('pages.diskScanSens_Msg')" name="encOrDecTab">
          <div class="table-container">
            <grid-table ref="logList" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="false" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
          </div>
        </el-tab-pane> -->
        <el-tab-pane v-if="hasPermission('113')" :label="$t('pages.diskScanSens_Msg1')" name="sensTab">
          <disk-scan-sens ref="sensitive" :severity-options="severityOptions" :loss-type-options="lossTypeOptions" :op-type-options="opTypeOptions"></disk-scan-sens>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/behaviorAuditing/encryption/diskScanSelfCheckLog'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'
import DiskScanSens from './senSelfCheckSitive'
import DiskScanSelfCheckDetail from './scanSelfCheckDetail'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'DiskScanSelfCheckLog',
  components: { DiskScanSelfCheckDetail, DiskScanSens },
  data() {
    return {
      colModel: [
        { prop: 'guid', label: 'taskNum', fixedWidth: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true },
        { prop: 'terminalName', label: 'terminalName', width: '150' },
        { prop: 'userName', label: 'user', width: '150' },
        { prop: 'opType', label: 'operateType', width: '150', formatter: this.typeFormatter },
        { prop: 'result', label: 'result', width: '150', formatter: this.resultFormatter },
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'filePath', label: 'filePath', width: '150' }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        opType: '',
        result: '',
        isTimes: false
      },
      showTree: true,
      selection: [],
      detailDeleteable: false,
      detailDialogVisible: false,
      tabName: 'logTab',
      scanStatusOptions: { 0: this.$t('pages.startScan'), 1: this.$t('pages.endScan') },
      encOrDecResults: { 0: this.$t('text.success'), 1: this.$t('pages.diskScanSens_Msg2'), 2: this.$t('pages.diskScanSens_Msg3'), 3: this.$t('pages.diskScanSens_Msg4'), 4: this.$t('pages.diskScanSens_Msg5'),
        5: this.$t('pages.diskScanSens_Msg6'), 6: this.$t('pages.diskScanSens_Msg7'), 7: this.$t('pages.diskScanSens_Msg8'), 8: this.$t('pages.diskScanSens_Msg9'), '9,10,11': this.$t('pages.diskScanSens_Msg10'), 12: this.$t('pages.diskScanSens_Msg11'),
        '-1': this.$t('pages.diskScanSens_Msg12'), '-2': this.$t('pages.diskScanSens_Msg13'), '-3': this.$t('pages.diskScanSens_Msg14'), '-4': this.$t('pages.diskScanSens_Msg15'), '-5': this.$t('pages.diskScanSens_Msg16'), '-6': this.$t('pages.diskScanSens_Msg17')
      },
      opTypeOptions: { 5: this.$t('pages.diskScan_Msg3'), 3: this.$t('pages.diskScan_Msg4'), 1: this.$t('pages.fullDecryption'), 2: this.$t('pages.globalEncryption')/*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/ },
      severityOptions: { 4: this.$t('pages.severityOptions5'), 3: this.$t('pages.severityOptions3'), 2: this.$t('pages.severityOptions2'), 1: this.$t('pages.severityOptions1') },
      // lossTypeOptions: getLossTypeDict(),
      lossTypeOptions: []
    }
  },
  created() {
    this.getSensitiveLossType()
    // this.opTypeOptions = { 1: this.$t('pages.fullDecryption'), 2: this.$t('pages.globalEncryption') }
    // if (this.hasPermission('113')) {
    //   Object.assign(this.opTypeOptions, { 5: this.$t('pages.diskScan_Msg3'), 3: this.$t('pages.diskScan_Msg4')/*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/ })
    // }
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        this.lossTypeOptions = res.data.map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
      })
    },
    getTab() {
      if (this.tabName === 'logTab') return this.$refs['scanLog']
      if (this.tabName === 'sensTab') return this.$refs['sensitive']
      return this
    },
    showOnTab(tabName) {
      return this.tabName === tabName
    },
    tabClick(pane, event) {
      this.$nextTick(() => {
        this.handleFilter()
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    detailTable() {
      return this.$refs['detailGrid']
    },
    detailSelectionChangeEnd: function(rowDatas) {
      if (rowDatas && rowDatas.length > 0) {
        this.detailDeleteable = true
      }
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.handleFilter()
    },
    handleFilter() {
      this.query.page = 1
      this.getTab().searchData(this.query)
    },
    searchData(query) {
      this.gridTable().execRowDataApi(query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    typeFormatter: function(row, data) {
      return this.opTypeOptions[data]
    },
    resultFormatter: function(row, data) {
      const result = this.encOrDecResults[data]
      if (!result) {
        for (const key in this.encOrDecResults) {
          if (key.indexOf(data) > -1 && (key.startsWith(data + ',') || key.endsWith(',' + data) || key.indexOf(',' + data + ',') > 0)) {
            return this.encOrDecResults[key]
          }
        }
      }
      return result
    },
    fileNameFormatter: function(row, data) {
      if (row.fileName != null && row.fileName != '') {
        if (row.fileName.lastIndexOf('\\') >= 0) {
          var fileName = row.fileName.substr(row.fileName.lastIndexOf('\\') + 1, row.fileName.length)
          return fileName
        }
        return row.fileName
      }
      return ''
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      this.query.exportDataType = { logTab: 1, encOrDecTab: 2, sensTab: 3 }[this.tabName]
      return exportExcel({ exportType, ...this.query })
    }
  }
}
</script>
