<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        :condition-btn="false"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <audit-log-exporter slot="append" v-permission="'352'" :multi-dataset="hasPermission('212')" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'463'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :multi-select="$store.getters.auditingDeleteAble && hasPermission('463')" :default-sort="{ prop: 'createTime' }" :row-data-api="rowDataApi" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.secretLevelLogDetails')"
      :visible.sync="detailDialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fileAllNum')">
            {{ rowDetail.fileTotal }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.successCount')">
            {{ rowDetail.successCount }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.errorCount')">
            {{ errorCountFormatter(rowDetail) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <Form ref="detailForm" label-position="right" label-width="100px" style="margin-top:2px;">
        <!--<el-button icon="el-icon-delete" size="mini" :disabled="!detailDeleteable" @click="detailDelete">
          {{ $t('button.delete') }}
        </el-button>-->
        <grid-table
          ref="detailGrid"
          :height="320"
          :col-model="detailColModel"
          :default-sort="{ prop: 'createTime' }"
          :row-data-api="detailRowApi"
          @selectionChangeEnd="detailSelectionChangeEnd"
        />
      </Form>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { deleteLog, exportExcel, getLogPage } from '@/api/behaviorAuditing/encryption/secretLevelLog'
import { deleteLogDetail, getLogDetailPage } from '@/api/behaviorAuditing/encryption/secretLevelLogDetail'
import { getSetting } from '@/api/dataEncryption/docPemission/denseSet'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'SecretLevelLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'fileTotal', label: 'fileAllNum', width: '150' },
        { prop: 'successCount', label: 'successCount', width: '150' },
        { prop: 'errorCount', label: 'errorCount', width: '150', formatter: this.errorCountFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('212'),
          buttons: [
            { label: 'detail', click: this.handleDetail }
          ]
        }
      ],
      detailColModel: [
        { prop: 'srcLevel', label: 'srcLevel', width: '100', iconFormatter: this.srcLevelIconClassFormat, formatter: this.srcDenseLevelFormatter },
        { prop: 'desLevel', label: 'desLevel', width: '100', iconFormatter: this.desLevelIconClassFormat, formatter: this.denseLevelFormatter },
        { prop: 'opResult', label: 'opResult', width: '150', formatter: this.opResultFormatter },
        { prop: 'createTime', label: '', width: '0', hidden: true, sort: 'custom' },
        { prop: 'filePath', label: 'fileName', width: '150' }
      ],
      denseLevelOptions: {},
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      queryDetail: { // 查询条件
        page: 1,
        mainGuid: '',
        createDate: '',
        sortName: 'createTime',
        sortOrder: 'asc',
        isTimes: false
      },
      showTree: true,
      selection: [],
      detailDeleteable: false,
      detailDialogVisible: false,
      activeDenseIconNum: 0,
      opResults: { 0: this.$t('text.success'), 1: this.$t('text.fail'), 2: this.$t('pages.secretLevelLog_Msg'), 3: this.$t('pages.secretLevelLog_Msg1'), 4: this.$t('pages.secretLevelLog_Msg2'), 5: this.$t('pages.secretLevelLog_Msg3') },
      rowDetail: {},
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    this.loadUserDense()
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    detailTable() {
      return this.$refs['detailGrid']
    },
    detailSelectionChangeEnd: function(rowDatas) {
      if (rowDatas && rowDatas.length > 0) {
        this.detailDeleteable = true
      }
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleDetail(row) {
      this.queryDetail.mainGuid = row.guid
      this.queryDetail.createDate = row.createTime
      this.detailDialogVisible = true
      this.$nextTick(() => {
        this.detailTable().execRowDataApi(this.queryDetail)
      })
      this.rowDetail = row;
    },
    detailRowApi: function(option) {
      const searchQuery = Object.assign({}, this.queryDetail, option)
      searchQuery.searchReport = this.query.searchReport
      return getLogDetailPage(searchQuery)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    detailDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.detailTable().getSelectedIds()
        deleteLogDetail({ ids: toDeleteIds.join(',') }).then(respond => {
          this.detailTable().deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    errorCountFormatter: function(row, data) {
      return row.fileTotal - row.successCount
    },
    loadUserDense() {
      getSetting().then(respond => {
        const configData = respond.data
        if (configData) {
          // this.activeDenseLevel = configData.visibleCount
          // this.activeDenseIconNum = configData.icoNum
          configData.denseInfoList.forEach(item => (this.denseLevelOptions[item.encryptLevel] = item.denseName))
        }
      })
    },
    iconClassFormat(row, data) {
      const iconMap = [
        ['lock', 'lock1-1', 'lock1-2', 'lock1-3', 'lock1-4'],
        ['lock', 'lock2-1', 'lock2-2', 'lock2-3', 'lock2-4']
      ]
      return iconMap[this.activeDenseIconNum][data]
    },
    srcLevelIconClassFormat(row) {
      return this.iconClassFormat(row, row.srcLevel)
    },
    desLevelIconClassFormat(row) {
      return this.iconClassFormat(row, row.desLevel)
    },
    srcDenseLevelFormatter: function(row, data) {
      if (row.opResult && row.opResult === 2) {
        // 2: '获取文件密级失败',则密级设置为未知
        row.srcLevel = null
        return this.$t('pages.unknown1')
      }
      return this.denseLevelFormatter(row, data)
    },
    denseLevelFormatter: function(row, data) {
      return this.denseLevelOptions[data]
    },
    opResultFormatter: function(row, data) {
      return this.opResults[data]
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '212', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
