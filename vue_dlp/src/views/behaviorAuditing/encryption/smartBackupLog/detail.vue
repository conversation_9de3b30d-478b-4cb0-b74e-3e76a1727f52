<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.detailsInfo', { info: $t('route.smartBackupLog') })"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :span="2" :label="$t('table.createTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.bizType')">
            {{ bussTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.backupResult')">
            {{ formatResult(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!!rowDetail.resultFailedReason" :span="2" :label="$t('table.failReason')">
            {{ rowDetail.resultFailedReason }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!!rowDetail.taskName" :span="2" :label="$t('pages.taskName')">
            {{ rowDetail.taskName }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!!rowDetail.taskName" :label="$t('table.taskNum')">
            {{ rowDetail.stgGuid }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!!rowDetail.taskName" :label="$t('pages.taskBatchNumber')">
            {{ rowDetail.taskGuid }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize2')">
            {{ formatFileSize(rowDetail.fileSize) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.suffixes')">
            {{ rowDetail.fileExt }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" :label="$t('table.fileUpdateDate')">
            {{ rowDetail.fileModifyTime }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" :label="$t('table.localFilePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import { formatFileSize } from '@/utils';

export default {
  name: 'SmartBackupLogDetail',
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      bussTypeMap: {
        1: this.$t('pages.fullScan'),
        2: this.$t('pages.timedScan'),
        3: this.$t('pages.activeBackup'),
        4: this.$t('pages.instantBackupCreate'),
        5: this.$t('pages.instantBackupModify'),
        6: this.$t('pages.instantBackupRename')
      }
    }
  },
  computed: {
  },
  methods: {
    formatFileSize,
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    bussTypeFormatter: function(row, data) {
      return this.bussTypeMap[row.bussType]
    },
    formatResult(row, data) {
      return row.backupResult == 0 ? this.$t('text.success') : this.$t('text.fail')
    },
    handleDownload: function(row) {
      this.$emit('downloadAuditLogFile', row)
    }
  }
}
</script>
