<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.fileBackupLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize2')">
            {{ rowDetail.fileSizeStr }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileName')">
            <el-button
              v-permission="'210'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!210'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.localFileName')">
            {{ rowDetail.localFileName }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: 'FileBackupLogDetail',
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {}
    }
  },
  computed: {

  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    handleDownload: function(row) {
      this.$emit('downloadAuditLogFile', row)
    }
  }
}
</script>
