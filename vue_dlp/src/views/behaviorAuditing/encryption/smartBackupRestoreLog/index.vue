<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.restoreResult" :value="query.restoreResult">
          <!--恢复结果-->
          <span>{{ $t('pages.restoreResult') }}</span>
          <el-select v-model="query.restoreResult">
            <el-option :value="null" :label="$t('pages.all')"/>
            <el-option :key="0" :value="0" :label="$t('text.success')"/>
            <el-option :key="1" :value="1" :label="$t('text.fail')"/>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.exceOpType" :value="query.exceOpType">
          <!--冲突执行操作-->
          <span>{{ $t('pages.conflictHandleType') }}</span>
          <el-select v-model="query.exceOpType">
            <el-option :value="null" :label="$t('pages.all')"/>
            <el-option :key="0" :value="0" :label="$t('pages.cover')"/>
            <el-option :key="1" :value="1" :label="$t('pages.rename')"/>
            <el-option :key="2" :value="2" :label="$t('pages.ignore')"/>
          </el-select>
        </SearchItem>
        <br>
        <SearchItem model-key="query.taskId" :value="query.taskId">
          <!-- 任务名称 -->
          <span>{{ $t('pages.taskName') }}</span>
          <el-select v-model="query.taskId" clearable filterable >
            <el-option v-for="item in taskNameMap" :key="item.taskId" :label="item.task.name" :value="item.taskId" :title="item.taskId">
              <div style="display: flex; align-items: center;">
                <span class="taskNameEllipsis" :title="item.task.name">{{ item.task.name }}</span>
                <span v-if="item.task.deleted === 1" style="margin-left: 20px; color: #8492a6; font-size: 13px">{{ $t('pages.wasDeleted') }}</span>
              </div>
            </el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.taskId" :value="query.taskId">
          <!-- 任务编号查询 -->
          <span>{{ $t('pages.taskNum') }}</span>
          <el-select v-model="query.taskId" clearable filterable >
            <el-option v-for="item in taskNameMap" :key="item.taskId" :label="item.taskId" :value="item.taskId" :title="item.task.name"/>
          </el-select>
        </SearchItem>
        <SearchItem v-show="query.bussType === 1" model-key="query.taskGuid" :value="query.taskGuid">
          <!-- 任务批次编号查询 -->
          <span>{{ $t('pages.taskBatchNumber') }}</span>
          <el-input
            v-model="query.taskGuid"
            v-trim
            :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.taskBatchNumber') })"
            clearable
            style="width: 225px;"
          />
        </SearchItem>
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <!-- 文件路径 -->
          <span>{{ $t('table.localFilePath') }}</span>
          <el-input
            v-model="query.fileName"
            v-trim
            :placeholder="$t('pages.pleaseEnterFileNamePath')"
            clearable
            style="width: 225px;"
          />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'515'" :request="handleExport"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'516'" icon="el-icon-delete" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :autoload="false"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.detailsInfo', { info: $t('route.smartBackupRestoreLog') })"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.createTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.restoreResult')">
            {{ formatResult(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!!rowDetail.resultFailedReason" :span="2" :label="$t('table.failReason')">
            {{ rowDetail.resultFailedReason }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" :label="$t('pages.taskNum')">
            {{ rowDetail.taskId }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!!rowDetail.taskName" :span="2" :label="$t('pages.taskName')">
            {{ rowDetail.taskName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize2')">
            {{ formatFileSize(rowDetail.fileSize) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.conflictHandleType')">
            {{ formatExceOpType(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fileUpdateDate')">
            {{ rowDetail.fileModifyTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.suffixes')">
            {{ rowDetail.fileExt }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" :label="$t('table.localFilePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/behaviorAuditing/encryption/smartBackupRestoreLog'
import AuditLogExporter from '@/components/AuditFileDownloader/exporter'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { formatFileSize } from '@/utils'
import { getCurrentTaskMap } from '@/api/dataEncryption/smartBackup/backupRepository'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'SmartBackupRestoreLog',
  components: { AuditLogExporter },
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: row => logSourceFormatter(row, row.createTime) },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'taskName', label: 'taskName', width: '100', custom: true },
        { prop: 'taskId', label: 'taskNum', width: '100', custom: true },
        { prop: 'fileSize', label: 'maxFileSize2', width: '150', formatter: (item) => formatFileSize(item.fileSize) },
        { prop: 'filePath', label: 'localFilePath', width: '150' },
        { prop: 'fileModifyTime', label: 'fileUpdateDate', width: '150' },
        { prop: 'exceOpType', label: this.$t('pages.conflictHandleType'), width: '150', formatter: this.formatExceOpType },
        { prop: 'restoreResult', label: this.$t('pages.restoreResult'), width: '150', formatter: this.formatResult },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('514'),
          buttons: [
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('514') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        taskId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        fileName: null,
        restoreResult: null,
        exceOpType: null,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      deleteable: false,
      // reqDownloadNum: 0,
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      taskNameMap: []
    }
  },
  beforeRouteEnter(to, from, next) {
    if (to.query) {
      next(vm => {
        const searchInfo = to.query
        if (searchInfo) {
          vm.query = Object.assign(vm.query, searchInfo)
          vm.$refs.timeQuery.setDate(searchInfo.createDate)
        }
        vm.$router.push({ query: {}})
      })
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    this.queryTask();
  },
  activated() {
  },
  methods: {
    formatFileSize,
    selectable(row, index) {
      return this.$store.getters.auditingDeleteAble && this.hasPermission('516')
    },
    objectTree: function() {
      return this.$refs['objectTree']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
      this.queryTask();
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      backupLogList(this.query).then(res => {
        if (res.data) {
          res.data.forEach(element => {
            months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
          });
          this.$notify({
            title: this.$t('text.prompt'),
            dangerouslyUseHTMLString: true,
            message: this.$t('pages.logBackupTip') + months,
            type: 'warning',
            duration: 2000
          })
        }
      })
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable.getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    formatResult(row, data) {
      return row.restoreResult == 0 ? this.$t('text.success') : this.$t('text.fail')
    },
    formatExceOpType(row, data) {
      const typeMap = {
        0: this.$t('pages.cover'),
        1: this.$t('pages.rename'),
        2: this.$t('pages.ignore')
      }
      return typeMap[row.exceOpType]
    },
    queryTask() {
      const searchQuery = Object.assign({}, this.query)
      getCurrentTaskMap(searchQuery).then(resp => {
        if (resp.data) {
          this.taskNameMap = resp.data
          if (this.query.taskId) {
            // 如果查询条件有带任务id但没有实际数据时，则清空选项
            const flag = this.taskNameMap.some(item => item.taskId === this.query.taskId);
            if (!flag) {
              this.query.taskId = null
            }
          }
        } else {
          this.taskNameMap = []
        }
      })
    },
    resetAdvancedQuery() {
      this.query.taskId = null
      this.query.fileName = null
    }
  }
}
</script>

<style lang='scss' scoped>
  .taskNameEllipsis {
    display: inline-block;
    max-width: 500px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
