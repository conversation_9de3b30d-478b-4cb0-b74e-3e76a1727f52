<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.detailsInfo', { info: $t('route.smartBackupRestoreLog') })"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.createTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.restoreResult')">
            {{ formatResult(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!!rowDetail.resultFailedReason" :span="2" :label="$t('table.failReason')">
            {{ rowDetail.resultFailedReason }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" :label="$t('pages.taskNum')">
            {{ rowDetail.taskId }}
          </el-descriptions-item>
          <el-descriptions-item v-if="!!rowDetail.taskName" :span="2" :label="$t('pages.taskName')">
            {{ rowDetail.taskName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize2')">
            {{ formatFileSize(rowDetail.fileSize) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.conflictHandleType')">
            {{ formatExceOpType(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fileUpdateDate')">
            {{ rowDetail.fileModifyTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.suffixes')">
            {{ rowDetail.fileExt }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" :label="$t('table.localFilePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import { formatFileSize } from '@/utils';

export default {
  name: 'SmartBackupRestoreLogDetail',
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {}
    }
  },
  computed: {
  },
  methods: {
    formatFileSize,
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    formatResult(row, data) {
      return row.restoreResult == 0 ? this.$t('text.success') : this.$t('text.fail')
    },
    formatExceOpType(row, data) {
      const typeMap = {
        0: this.$t('pages.cover'),
        1: this.$t('pages.rename'),
        2: this.$t('pages.ignore')
      }
      return typeMap[row.exceOpType]
    },
    handleDownload: function(row) {
      this.$emit('downloadAuditLogFile', row)
    }
  }
}
</script>
