<template>
  <div class="table-container">
    <grid-table
      :ref="tableRef"
      row-key="logId"
      :col-model="customColModel"
      :multi-select="$store.getters.auditingDeleteAble && hasPermission(permissionDelete)"
      :row-data-api="rowDataApi"
      :sortable="sortable"
      :after-load="afterLoad"
      :custom-col="true"
      no-page-total
      @selectionChangeEnd="selectionChangeEnd"
    />

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="detailDialogTitle"
      :visible.sync="visible"
      :width="detailDialogWidth"
    >
      <div class="show-detail-panel">
        <slot :row="rowDetail"/>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.labelContentDetail')"
      :visible.sync="detailDialogFormVisible"
      width="600px"
    >
      <grid-table ref="detailList" row-key="logId" :col-model="detailColModel" :multi-select="false" :height="200" :default-sort="{ prop: 'createTime' }" :row-data-api="detailDataApi"/>
    </el-dialog>
    <audit-file-downloader ref="auditFileDownloader" v-permission="'930'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
    <sen-file-record ref="senFileRecord"/>
  </div>
</template>

<script>
import { logSourceFormatter } from '@/utils/formatter'
import { asyncGetLogVideoInfo, disabledLogViewerBtn } from '@/utils/logVideo'
import SenFileRecord from './senFileRecord'

export default {
  name: 'DiskScanLogCommon',
  components: { SenFileRecord },
  props: {
    tableRef: {
      type: String,
      required: true
    },
    queryParams: {
      type: Object,
      required: true
    },
    colModel: {
      type: Array,
      required: true
    },
    getLogPage: {
      type: Function,
      required: true
    },
    detailDialogTitle: {
      type: String,
      required: true
    },
    detailDialogWidth: {
      type: String,
      default: '600px'
    },
    getTagDetailLogPage: {
      type: Function,
      default: function() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    isShowFileDetail: {
      type: Boolean,
      default: false
    },
    isShowTagType: {
      type: Boolean,
      default: true
    },
    permissionDelete: { type: String, default: '465' }, // 删除权限
    permissionView: { type: String, default: '299' } // 查看权限
  },
  data() {
    return {
      detailColModel: [
        { prop: 'createTime', label: 'identificationTime', width: '150', sort: true },
        { prop: 'tagContent', label: 'labelContent', width: '150' },
        { prop: 'tagType', label: 'labelStatus', width: '100', formatter: this.tagTypeFormatter, hidden: this.isShowTagType } // 标签状态{0：新增 1：删除 2：失效}
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        sortName: 'createTime',
        sortOrder: 'desc',
        searchReport: 1
      },
      detailQuery: {
        page: 1,
        createDate: '',
        recordGuid: '',
        isTimes: false
      },
      sortable: true,
      visible: false,
      detailDialogFormVisible: false,
      rowDetail: {},
      queryVideoMethod: undefined,
      tagTypeOptions: {
        0: this.$t('button.add'),
        1: this.$t('button.delete'),
        2: this.$t('button.tagExpired')
      },
      selection: [],
      tempTask: {},
      defaultTempTask: {
        backType: 21,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      termsInfo: [] // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
    }
  },
  computed: {
    customColModel() {
      const custom = [...this.colModel]
      const first = custom[0]
      if (first.formatter) {
        const fmt = first.formatter
        first.formatter = (row, data) => logSourceFormatter(row, fmt(row, data))
      } else {
        first.formatter = logSourceFormatter
      }
      for (let i = 0; i < custom.length; i++) {
        if (custom[i].searchType === 'terminal') {
          custom[i].attributes = { getTermsInfo: this.getTermsInfo }
          break
        }
      }
      // 查看录屏权限与查看详情权限一致
      const detail = [
        { label: 'detail', click: this.handleView },
        { label: 'download', disabledFormatter: this.downloadFormatter, click: this.handleDownload, isShow: row => this.showDownload(row) },
        { label: '历史分析', click: this.handleFileDetail, isShow: row => this.showFileDetail() },
        { label: this.$t('pages.viewScreenRecord'), click: this.playVideo, isShow: row => !disabledLogViewerBtn(row) },
        { label: this.$t('table.labelContentDetail'), click: this.handleTagView, isShow: this.disableTagView }
      ]
      const last = custom[custom.length - 1]
      if (last.label === 'operate' && last.type === 'button') {
        if (last.buttons) {
          last.buttons.push(...detail)
        } else {
          last.buttons = [...detail]
        }
      } else {
        custom.push({
          label: 'operate',
          type: 'button',
          fixedWidth: '260',
          fixed: 'right',
          hidden: () => !this.hasPermission(this.permissionView),
          buttons: [...detail]
        })
      }
      return custom
    }
  },
  watch: {
    queryParams: {
      deep: true,
      handler(value) {
        this.query = { ...value }
      }
    }
  },
  created() {
    this.query = { ...this.queryParams }
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    gridTable() {
      return this.$refs[this.tableRef]
    },
    handleView(row) {
      this.rowDetail = row
      this.visible = true
    },
    handleTagView(row) {
      this.detailDialogFormVisible = true
      this.detailQuery.createDate = row.createTime.slice(0, 10)
      this.detailQuery.recordGuid = row.recordGuid || row.tagDetailGuid
      this.$nextTick(() => {
        this.$refs['detailList'].execRowDataApi(this.detailQuery)
      })
    },
    showFileDetail() {
      return this.isShowFileDetail
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      const { createDate, startDate, endDate, isTimes } = this.query
      this.$emit('dateRangeChange', { createDate, startDate, endDate, isTimes })
      return this.getLogPage(searchQuery)
    },
    detailDataApi: function(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      this.detailQuery.sortName = option.sortName
      this.detailQuery.sortOrder = option.sortOrder
      return this.getTagDetailLogPage(searchQuery)
    },
    selectionChangeEnd(selection) {
      this.selection = selection
      this.$emit('selectionChangeEnd', selection)
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, this.permissionView)
    },
    playVideo(row) {
      this.$emit('playVideo', row)
    },
    handleDownload(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    tagTypeFormatter(row, data) {
      return this.tagTypeOptions[data]
    },
    disableTagView(row) {
      return row.recordGuid || row.tagDetailGuid
    },
    downloadFormatter(data) {
      return !data.fileGuid
    },
    handleFileDetail(row) {
      this.$refs['senFileRecord'].show(row)
    },
    showDownload(row) {
      return row.fileGuid
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      console.log('row1111', row);
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    }
  }
}
</script>
