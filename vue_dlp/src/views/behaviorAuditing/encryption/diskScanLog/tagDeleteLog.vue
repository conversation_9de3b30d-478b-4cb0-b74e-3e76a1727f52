<!--
  全盘扫描记录 - 加解密详情
-->
<template>
  <disk-scan-log-common
    ref="tagDeleteList"
    table-ref="tagDeleteList"
    :query-params="queryParams"
    :permission-delete="permissionDelete"
    :col-model="colModel"
    :get-log-page="getLogPage"
    :detail-dialog-title="$t('pages.tagDeleteDetail')"
    detail-dialog-width="700px"
    @dateRangeChange="dateRangeChange"
    @selectionChangeEnd="selectionChangeEnd"
    @playVideo="row => $emit('playVideo', row)"
  >
    <el-descriptions slot-scope="{ row }" class="margin-top" :column="2" size="" border>
      <el-descriptions-item :label="$t('table.taskNum')">
        {{ row.guid }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.operateTime')">
        {{ row.createTime }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.terminalName')">
        <terminal-detail
          :label="row.terminalName"
          :search-id="row.terminalId"
        />
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.user')">
        <user-detail
          :label="row.userName"
          :search-id="row.userId"
        />
      </el-descriptions-item>
      <el-descriptions-item span="2" :label="$t('table.operateType')">
        {{ opTypeFormatter(row, row.opType) }}
      </el-descriptions-item>
      <el-descriptions-item span="2" :label="$t('table.result')">
        {{ resultFormatter(row, row.result) }}
      </el-descriptions-item>
      <el-descriptions-item span="2" :label="$t('table.fileName')">
        {{ row.fileName }}
      </el-descriptions-item>
      <el-descriptions-item span="2" :label="$t('table.filePath')">
        {{ row.filePath }}
      </el-descriptions-item>
    </el-descriptions>
  </disk-scan-log-common>
</template>

<script>
import DiskScanLogCommon from './common'
import { deleteLog, getLogPage } from '@/api/behaviorAuditing/encryption/diskScanDeleteTagLog'

export default {
  name: 'TagDeleteLog',
  components: { DiskScanLogCommon },
  props: {
    permissionDelete: { type: String, default: '465' }, // 删除权限
    queryParams: {
      type: Object,
      required: true
    },
    opTypeOptions: {
      type: Object,
      default() {
        return {}
      }
    },
    tagResults: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dateRange: undefined,
      colModel: [
        { prop: 'guid', label: 'taskNum', fixedWidth: '150', sort: 'custom' },
        { prop: 'createTime', label: 'operateTime', width: '150', sort: 'custom' },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'opType', label: 'operateType', width: '150', formatter: this.opTypeFormatter },
        { prop: 'result', label: 'result', width: '150', formatter: this.resultFormatter },
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'filePath', label: 'filePath', width: '150' }
      ]
    }
  },
  methods: {
    deleteLog,
    getLogPage,
    gridTable() {
      return this.$refs['tagDeleteList'].gridTable()
    },
    dateRangeChange(dateRange) {
      this.dateRange = dateRange
    },
    selectionChangeEnd(selection) {
      this.$emit('selectionChangeEnd', selection)
    },
    opTypeFormatter(row, data) {
      row.opType = row.opType + ''
      return this.opTypeOptions[data]
    },
    resultFormatter: function(row, data) {
      const result = this.tagResults[data]
      if (!result) {
        for (const key in this.tagResults) {
          if (key.indexOf(data) > -1) {
            return this.tagResults[key]
          } else {
            return this.$t('pages.otherReason')
          }
        }
      }
      return result
    }
  }
}
</script>
