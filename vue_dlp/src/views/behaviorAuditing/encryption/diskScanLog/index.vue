<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        :tab-name="tabName"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.opType" :value="query.opType">
          <span>{{ $t('pages.operateType') }}：</span>
          <el-select v-model="query.opType" style="width: 150px" clearable>
            <el-option v-for="(label, value) in opTypeOptions" :key="value" :label="label" :value="value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem v-show="taskStatusShow" :visible="taskStatusShow" model-key="query.statusType" :value="query.statusType">
          <span>{{ $t('pages.taskStatus') }}：</span>
          <el-select v-model="query.statusType" style="width: 150px" clearable>
            <el-option v-for="(label, key) in scanStatusOptions" :key="key" :label="label" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem v-show="sentiveTypeShow" :visible="sentiveTypeShow" model-key="query.sentiveType" :value="query.sentiveType">
          <span>敏感次数：</span>
          <el-select v-model="query.sentiveType" style="width: 150px" clearable>
            <el-option v-for="(label, key) in senNumberOptions" :key="key" :label="label" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem v-show="resultShow" :visible="resultShow" model-key="resultValue" :value="resultValue">
          <span>{{ $t('pages.result') }}：</span>
          <!-- <el-select v-show="tabName === 'encOrDecTab'" v-model="query.result" style="width: 150px" clearable>
              <el-option v-for="(label, value) in encOrDecResults" :key="value" :label="label" :value="value"></el-option>
            </el-select> -->
          <el-cascader
            ref="cascade"
            :key="cascadeKey"
            v-model="resultValue"
            clearable
            class="myCascader"
            :show-all-levels="false"
            :props="{ checkStrictly: true, expandTrigger: 'hover', multiple: true, emitPath: false }"
            :options="cascaderResults"
            collapse-tags
          >
          </el-cascader>
        </SearchItem>
        <SearchItem v-show="severityShow" :visible="severityShow" model-key="query.severity" :value="query.severity">
          <span>{{ $t('pages.severity') }}：</span>
          <el-select v-model="query.severity" style="width: 150px" clearable>
            <el-option v-for="(label, value) in severityOptions" :key="value" :label="label" :value="value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.guid" :value="query.guid">
          <span>{{ $t('pages.taskNum') }}：</span>
          <el-input v-model="query.guid" v-trim clearable :placeholder="this.$t('pages.diskScanSens_Msg18')" style="width: 200px;" />
        </SearchItem>
        <!--
        <SearchItem v-show="lossTypeShow" model-key="query.lossType" :value="query.lossType">
          <span>泄露方式：</span>
          <el-select v-model="query.lossType" style="width: 150px" clearable>
            <el-option v-for="(item, index) in lossTypeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem> -->
        <audit-log-exporter slot="append" v-permission="permissionExport" multi-dataset :request="handleExport"/>
        <audit-log-delete
          v-if="$store.getters.auditingDeleteAble"
          slot="append"
          v-permission="permissionDelete"
          :selection="selection"
          :date-range="getDateRange"
          :delete-log="deleteLog"
          :table-getter="gridTable"
        />
      </SearchToolbar>

      <el-tabs ref="tabs" v-model="tabName" type="card" class="disk-scan-tabs" style="height: calc(100% - 40px);" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.scanLog')" name="logTab">
          <scan-log
            ref="logTab"
            :query-params="query"
            :permission-delete="permissionDelete"
            @selectionChangeEnd="selectionChangeEnd"
            @playVideo="playVideo"
            @changeTab="changeTab"
          />
        </el-tab-pane>
        <el-tab-pane v-if="(showEncDec || showSens) && hasPermission('124')" :label="$t('pages.diskScanSens_Msg')" name="encOrDecTab">
          <enc-dec-detail
            ref="encOrDecTab"
            :query-params="query"
            :permission-delete="permissionDelete"
            :op-type-options="opTypeOptions"
            :enc-or-dec-results="encOrDecResults"
            @selectionChangeEnd="selectionChangeEnd"
            @playVideo="playVideo"
          />
        </el-tab-pane>
        <el-tab-pane v-if="showSens && hasPermission('113')" :label="$t('pages.diskScanSens_Msg1')" name="sensTab">
          <sensitive-file-detail
            ref="sensTab"
            :query-params="query"
            :permission-delete="permissionDelete"
            :severity-options="severityOptions"
            :op-type-options="opTypeOptions"
            :content-aware-results="contentAwareObjectResults"
            @selectionChangeEnd="selectionChangeEnd"
            @playVideo="playVideo"
          />
        </el-tab-pane>
        <el-tab-pane v-if="showTag && hasPermission('125 & 113')" :label="$t('pages.tagAddLog')" name="addTagTab">
          <tag-log
            ref="addTagTab"
            :query-params="query"
            :permission-delete="permissionDelete"
            :tag-results="tagResults"
            :op-type-options="opTypeOptions"
            @selectionChangeEnd="selectionChangeEnd"
            @playVideo="playVideo"
          />
        </el-tab-pane>
        <el-tab-pane v-if="showTag && hasPermission('125')" :label="$t('pages.tagGetLog')" name="tagDetailTab">
          <tag-detail-log
            ref="tagDetailTab"
            :query-params="query"
            :permission-delete="permissionDelete"
            :tag-results="tagResults"
            :op-type-options="opTypeOptions"
            @selectionChangeEnd="selectionChangeEnd"
            @playVideo="playVideo"
          />
        </el-tab-pane>
        <el-tab-pane v-if="showTag && hasPermission('125')" :label="$t('pages.tagDeleteLog')" name="tagDeleteTab">
          <tag-delete-log
            ref="tagDeleteTab"
            :query-params="query"
            :permission-delete="permissionDelete"
            :tag-results="tagResults"
            :op-type-options="opTypeOptions"
            @selectionChangeEnd="selectionChangeEnd"
            @playVideo="playVideo"
          />
        </el-tab-pane>
      </el-tabs>
      <video-viewer ref="videoViewer"/>
    </div>
  </div>
</template>

<script>
import { exportExcel } from '@/api/behaviorAuditing/encryption/diskScanLog'
// import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'
import ScanLog from './scanLog'
import EncDecDetail from './encDecDetail'
import SensitiveFileDetail from './sensitiveFileDetail'
import TagLog from './addTagLog'
import TagDetailLog from './tagDetailLog'
import TagDeleteLog from './tagDeleteLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { handleViewLogVideo } from '@/utils/logVideo'

export default {
  name: 'DiskScanLog',
  components: { ScanLog, EncDecDetail, SensitiveFileDetail, TagLog, TagDetailLog, TagDeleteLog },
  props: {
    showTag: { type: Boolean, default: false }, // 显示标签相关功能
    showSens: { type: Boolean, default: false }, // 显示敏感文件相关功能
    showEncDec: { type: Boolean, default: true }, // 显示加密解密相关功能
    permissionView: { type: String, default: '299' }, // 查看权限
    permissionExport: { type: String, default: '354' }, // 导出权限
    permissionDelete: { type: String, default: '465' } // 删除权限
  },
  data() {
    return {
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        opType: '',
        opTypes: '',
        result: '',
        isTimes: false,
        guid: '',
        sortName: 'createTime',
        sortOrder: 'desc',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKeys: ['scanDetailList', 'logList', 'sensitiveList', 'addTagList', 'tagDetailList', 'tagDeleteList'],
        sentiveType: null
      },
      showTree: true,
      selection: [],
      tabName: 'logTab',
      scanStatusOptions: {
        0: this.$t('pages.startScan'),
        1: this.$t('pages.endScan'),
        2: this.$t('pages.strategyExpire'),
        3: this.$t('pages.stopScan')
      },
      senNumberOptions: {
        1: '首次新增',
        2: '二次出现',
        3: '三次及三次以上出现'
      },
      encOrDecResults: {
        0: this.$t('text.success'),
        1: this.$t('pages.diskScanSens_Msg2'),
        2: this.$t('pages.diskScanSens_Msg3'),
        3: this.$t('pages.diskScanSens_Msg4'),
        4: this.$t('pages.diskScanSens_Msg5'),
        5: this.$t('pages.diskScanSens_Msg6'),
        6: this.$t('pages.diskScanSens_Msg7'),
        7: this.$t('pages.diskScanSens_Msg8'),
        8: this.$t('pages.diskScanSens_Msg9'),
        '9,10,11': this.$t('pages.diskScanSens_Msg10'),
        12: this.$t('pages.diskScanSens_Msg11'),
        13: this.$t('pages.diskScanSens_Msg19'),
        '-1': this.$t('pages.diskScanSens_Msg12'),
        '-2': this.$t('pages.diskScanSens_Msg13'),
        '-3': this.$t('pages.diskScanSens_Msg14'),
        '-4': this.$t('pages.diskScanSens_Msg15'),
        '-5': this.$t('pages.diskScanSens_Msg16'),
        '-6': this.$t('pages.diskScanSens_Msg17')
      },
      tagResults: {
        0: this.$t('text.success'),
        '-1': this.$t('pages.noSignInfo'),
        '-500': this.$t('pages.fileUsed'),
        '-501': this.$t('pages.fileNoSupport'),
        '-2': this.$t('pages.otherReason'),
        10001: this.$t('pages.noDeal')
      },
      addTagResults: {
        0: this.$t('text.success'),
        '-1': this.$t('pages.alreadyExsitLabel'),
        '-500': this.$t('pages.fileUsed'),
        '-501': this.$t('pages.fileNoSupport'),
        '-2': this.$t('pages.otherReason'),
        10001: this.$t('pages.terminal_text22'),
        10002: this.$t('pages.exportReasonTip1'),
        10003: this.$t('pages.exportReasonTip2')
      },
      cascadeKey: 1,
      resultValue: null,
      resultValueMap: {},
      // 加解密详情 结果
      encOrDecCascaderResults: [
        { value: 'all', label: this.$t('pages.collectAll') },
        { value: '0,2,12',
          label: this.$t('text.success'),
          children: [
            { value: 0, label: this.$t('text.success') },
            { value: 2, label: this.$t('pages.diskScanSens_Msg3') },
            { value: 12, label: this.$t('pages.diskScanSens_Msg11') }
          ]
        },
        { value: '1,3,4,5,6,7,8,9,10,11,13,-1,-2,-3,-4,-5,-6',
          label: this.$t('text.fail'),
          children: [
            { value: 1, label: this.$t('pages.diskScanSens_Msg2') },
            { value: 3, label: this.$t('pages.diskScanSens_Msg4') },
            { value: 4, label: this.$t('pages.diskScanSens_Msg5') },
            { value: 5, label: this.$t('pages.diskScanSens_Msg6') },
            { value: 6, label: this.$t('pages.diskScanSens_Msg7') },
            { value: 7, label: this.$t('pages.diskScanSens_Msg8') },
            { value: 8, label: this.$t('pages.diskScanSens_Msg9') },
            { value: '9,10,11', label: this.$t('pages.diskScanSens_Msg10') },
            { value: 13, label: this.$t('pages.diskScanSens_Msg19') },
            { value: '-1', label: this.$t('pages.diskScanSens_Msg12') },
            { value: '-2', label: this.$t('pages.diskScanSens_Msg13') },
            { value: '-3', label: this.$t('pages.diskScanSens_Msg14') },
            { value: '-4', label: this.$t('pages.diskScanSens_Msg15') },
            { value: '-5', label: this.$t('pages.diskScanSens_Msg16') },
            { value: '-6', label: this.$t('pages.diskScanSens_Msg17') }
          ]
        }
      ],
      tagCascaderResults: [
        { value: 'all', label: this.$t('pages.collectAll') },
        { value: '0', label: this.$t('text.success') },
        { value: '-500,-2',
          label: this.$t('text.fail'),
          children: [
            { value: '-500', label: this.$t('pages.fileUsed') },
            { value: '-2', label: this.$t('pages.otherReason') }
          ]
        },
        { value: '10001,-1,-501',
          label: this.$t('pages.noDeal'),
          children: [
            { value: '-1', label: this.$t('pages.noSignInfo') },
            { value: '-501', label: this.$t('pages.fileNoSupport') }
          ]
        }
      ],
      addTagCascaderResults: [
        { value: 'all', label: this.$t('pages.collectAll') },
        { value: '0', label: this.$t('text.success') },
        { value: '-500,-2',
          label: this.$t('text.fail'),
          children: [
            { value: '-500', label: this.$t('pages.fileUsed') },
            { value: '-2', label: this.$t('pages.otherReason') }
          ]
        },
        { value: '10001,10002,10003,-1,-501',
          label: this.$t('pages.noDeal'),
          children: [
            { value: '10001', label: this.$t('pages.terminal_text22') },
            { value: '10002', label: this.$t('pages.exportReasonTip1') },
            { value: '10003', label: this.$t('pages.exportReasonTip2') },
            { value: '-1', label: this.$t('pages.alreadyExsitLabel') },
            { value: '-501', label: this.$t('pages.fileNoSupport') }
          ]
        }
      ],
      contentAwareObjectResults: {
        0: this.$t('text.success'),
        '-1': this.$t('pages.otherError'),
        '-2': this.$t('pages.fileNotExistError'),
        '-3': this.$t('pages.fileTooLargeNotRecognizeError'),
        '-4': this.$t('pages.fileHasPasswordError'),
        '-5': this.$t('pages.strategyNotFoundError'),
        '-6': this.$t('pages.detectionTimeoutError'),
        '-7': this.$t('pages.hasPwdFileNotDecryptError')
      },
      // 敏感文件详情 结果
      contentAwareResults: [
        { value: 'all', label: this.$t('pages.collectAll') },
        { value: '0', label: this.$t('text.success') },
        { value: '-1,-2,-3,-4,-5',
          label: this.$t('text.fail'),
          children: [
            { value: '-1', label: this.$t('pages.otherError') },
            { value: '-2', label: this.$t('pages.fileNotExistError') },
            { value: '-4', label: this.$t('pages.fileHasPasswordError') },
            { value: '-5', label: this.$t('pages.strategyNotFoundError') }
          ]
        }
      ],
      // lossTypeOptions: [],
      opTypeOptions: {},
      severityOptions: {
        4: this.$t('pages.severityOptions4'),
        3: this.$t('pages.severityOptions3'),
        2: this.$t('pages.severityOptions2'),
        1: this.$t('pages.severityOptions1')
      },
      tempCondition: null,     // 存放跳转之前的条件，用于还原条件查询数据
      oldResultValue: null,    // 保存跳转之前的结果，用于还原条件查询数据
      oldTabName: null         // 保存跳转之前的tabName
    }
  },
  computed: {
    cascaderResults() {
      let results = [];
      if (this.tabName === 'encOrDecTab') {
        results = this.encOrDecCascaderResults;
      } else if (this.tabName === 'sensTab') {
        results = this.contentAwareResults;
      } else if (this.showOnTab('addTagTab')) {
        results = this.addTagCascaderResults
      } else {
        results = this.tagCascaderResults
      }
      return results;
    },
    taskStatusShow() {
      return this.tabName === 'logTab'
    },
    resultShow() {
      return ['encOrDecTab', 'addTagTab', 'tagDetailTab', 'tagDeleteTab', 'sensTab'].includes(this.tabName)
    },
    sentiveTypeShow() {
      return this.tabName === 'sensTab'
    },
    severityShow() {
      return this.tabName === 'sensTab'
    },
    lossTypeShow() {
      return this.tabName === 'sensTab'
    }
  },
  watch: {
    resultValue: {
      deep: true,
      handler(val) {
        this.handleResultChange(val)
      }
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    // this.getSensitiveLossType()
    if (this.showEncDec && this.hasPermission('124')) {
      Object.assign(this.opTypeOptions, { 1: this.$t('pages.fullDecryption'), 2: this.$t('pages.globalEncryption') });
    }
    if (this.showSens && this.hasPermission('113')) {
      Object.assign(this.opTypeOptions, { 3: this.$t('pages.diskScan_Msg4') })
    }
    if (this.showSens && this.hasPermission('124 & 113')) {
      Object.assign(this.opTypeOptions, { 5: this.$t('pages.diskScan_Msg3')/*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/ })
    }
    if (this.showTag && this.hasPermission('125 & 113')) {
      Object.assign(this.opTypeOptions, { 7: this.$t('pages.diskScan_Msg47') })
    }
    if (this.showTag && this.hasPermission('125')) {
      Object.assign(this.opTypeOptions, { 8: this.$t('pages.diskScan_Msg48'), 9: this.$t('pages.diskScan_Msg49') })
    }
    const opTypes = Object.keys(this.opTypeOptions)
    if (opTypes && opTypes.length > 0) {
      this.query.opTypes = opTypes.join(',')
    }
  },
  mounted() {
    this.searchData()
  },
  methods: {
    getTabRef() {
      return this.$refs[this.tabName]
    },
    gridTable() {
      const tabRef = this.getTabRef()
      return tabRef && tabRef.gridTable()
    },
    getDateRange() {
      const tabRef = this.getTabRef()
      return tabRef && tabRef.dateRange
    },
    updateCascadeKey() {
      this.cascadeKey++
    },
    deleteLog(data, headers) {
      const tabRef = this.getTabRef()
      if (tabRef) {
        return tabRef.deleteLog(data, headers)
      }
      return Promise.reject('Tab [' + this.tabName + '] has not completed initialization yet.')
    },
    searchData() {
      const table = this.gridTable()
      if (table) {
        table.execRowDataApi(this.query)
      }
    },
    // getSensitiveLossType() {
    //   getSensitiveLossType({ type: 2 }).then(res => {
    //     this.lossTypeOptions = res.data.map(item => {
    //       return {
    //         value: item.lossType,
    //         label: item.lossDesc
    //       }
    //     })
    //   })
    // },
    showOnTab(tabName) {
      return this.tabName === tabName
    },
    tabClick(pane, event) {
      this.resultValue = this.resultValueMap[pane.name] || []
      if (this.tempCondition && this.tabName !== this.oldTabName) {
        //  当跳转回扫描日志时，还原原来的查询信息，如果是其他tab，直接清空查询条件
        if (this.tabName === 'logTab') {
          this.query = this.tempCondition
          this.resultValue = this.oldResultValue
          this.oldTabName = ''
          this.tempCondition = null
          this.oldResultValue = []
        } else {
          this.query.result = null
          this.resultValue = []
        }
      }
      this.selection = []
      this.updateCascadeKey()
      this.$nextTick(() => {
        this.handleFilter()
      })
    },
    selectionChangeEnd(selection) {
      this.selection = selection
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.handleFilter()
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.searchData()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      this.query.exportDataType = { logTab: 1, encOrDecTab: 2, sensTab: 3, addTagTab: 4, tagDetailTab: 5, tagDeleteTab: 6 }[this.tabName]
      return exportExcel({ exportType, ...this.query })
    },
    handleResultChange(valueArr) {
      this.query.result = this.getSelectResult(valueArr);
      this.resultValueMap[this.tabName] = valueArr
    },
    /**
     * 获取选中结果的字符串
     * @param arr
     */
    getSelectResult(arr) {
      // 将选中的结果转成字符串
      const selectResult = arr.join(',')
      // 如果包含 all 返回空字符串，否则返回去重后的 selectResult
      return selectResult.includes('all') ? '' : [...new Set(selectResult.split(','))].join(',')
    },
    playVideo(row) {
      handleViewLogVideo(row, this)
    },
    /**
     * 从扫描日志页 跳转到 详情页
     * @param row   条件
     * @param type
     *  1: 处理成功数
     *  2： 处理失败数
     *  3： 敏感文件数
     * @param result  结果，0-所有，1-成功，2-失败，10001-不处理
     */
    changeTab: function(row, type, result) {
      if (!this.tempCondition) {
        this.tempCondition = JSON.parse(JSON.stringify(this.query));
        this.oldResultValue = this.resultValue;
        this.oldTabName = 'preOldTabName$'
      }
      this.query.guid = row.guid
      this.selection = []
      if (row.opType == 7) {
        this.tabName = 'addTagTab'
        this.resultValue = [this.addTagCascaderResults[type].value]
      } else if (row.opType == 8) {
        this.tabName = 'tagDetailTab'
        this.resultValue = [this.tagCascaderResults[type].value]
      } else if (row.opType == 9) {
        this.tabName = 'tagDeleteTab'
        this.resultValue = [this.tagCascaderResults[type].value]
      } else if (type == 1 || type == 2) {
        this.tabName = 'encOrDecTab'
        this.resultValue = [this.encOrDecCascaderResults[type].value]
      } else {
        this.tabName = 'sensTab'
        this.resultValue = [this.contentAwareResults[result].value]
      }
      this.query.result = this.getSelectResult(this.resultValue);
      if (this.oldTabName === 'preOldTabName$') {
        this.oldTabName = this.tabName
      }
      //  设置终端Id
      this.query.objectId = row.termianlId
      this.query.objectType = 1
      // 因为全盘扫描日志是根据销售模块决定显示位置，因此不能直接赋值'/behaviorAuditing/encryptionLog/diskScanLog'
      let path = window.location.href
      if (path.indexOf('#') > -1) {
        path = path.split('#')[1]
        if (path.indexOf('?') > -1) {
          path = path.split('?')[0]
        }
      }
      this.$router.push({ path: path, query: { entityId: row.terminalId, entityType: 1, autoload: false, timestamp: new Date().getTime() }})
    }
  }
}
</script>
<style lang="scss" scoped>
  .disk-scan-tabs {
    >>>.el-tabs__content {
      padding: 0 5px 5px;
    }
  }
  .myCascader {
    width: 180px;
    line-height: 30px;
    >>> .el-input .el-input__inner {
      height: 30px !important;
    }
    >>> .el-cascader__tags {
      top: 1px!important;
    }
  }
</style>
