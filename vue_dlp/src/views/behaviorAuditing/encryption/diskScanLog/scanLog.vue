<!--
  全盘扫描记录 - 扫描日志
-->
<template>
  <disk-scan-log-common
    ref="logList"
    table-ref="scanDetailList"
    :query-params="queryParams"
    :permission-delete="permissionDelete"
    :col-model="colModel"
    :get-log-page="getScanDetailPage"
    :detail-dialog-title="$t('pages.scanLogDetails')"
    detail-dialog-width="700px"
    @dateRangeChange="dateRangeChange"
    @selectionChangeEnd="selectionChangeEnd"
    @playVideo="row => $emit('playVideo', row)"
  >
    <el-descriptions slot-scope="{ row }" class="margin-top" :column="2" size="" border>
      <el-descriptions-item :label="$t('table.taskNum')">
        {{ row.guid }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.operateType')">
        {{ opTypeFormatter(row, row.opType) }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.terminalName')">
        <terminal-detail
          :label="row.terminalName"
          :search-id="row.terminalId"
        />
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.user')">
        <user-detail
          :label="row.userName"
          :search-id="row.userId"
        />
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.startTime')">
        {{ row.createTime }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.endTime')">
        {{ row.endTime }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.taskStatus')">
        {{ statusFormatter(row, row.status) }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.totalNumberScan')">
        {{ row.fileTotal }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.numberSuccessProcess')">
        {{ row.dealSuc }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.numberFailProcess')">
        {{ row.dealFail }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.numberSensitive')">
        {{ row.sensFile }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.numberNonSensitive')">
        {{ row.nonSensFile }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.noDealNumber')">
        {{ row.nonProc }}
      </el-descriptions-item>
    </el-descriptions>
  </disk-scan-log-common>
</template>

<script>
import DiskScanLogCommon from './common'
import { getScanDetailPage, deleteLog } from '@/api/dataEncryption/encryption/diskScan'

export default {
  name: 'ScanLog',
  components: { DiskScanLogCommon },
  props: {
    permissionDelete: { type: String, default: '465' }, // 删除权限
    queryParams: { type: Object, required: true }
  },
  data() {
    return {
      dateRange: undefined,
      colModel: [
        { prop: 'guid', label: 'taskNum', fixedWidth: '150', sort: 'custom' },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'opType', label: 'operateType', width: '150', formatter: this.opTypeFormatter },
        { prop: 'createTime', label: 'startTime', width: '180' },
        { prop: 'endTime', label: 'endTime', width: '180', formatter: this.endTimeFormatter },
        { prop: 'status', label: 'taskStatus', width: '100', formatter: this.statusFormatter },
        { prop: 'fileTotal', label: 'totalNumberScan', width: '110' },
        { prop: 'dealSuc', label: 'numberSuccessProcess', width: '100' },
        { prop: 'dealFail', label: 'numberFailProcess', width: '100', type: 'button',
          buttons: [
            { formatter: (row, data) => { return row.dealFail }, click: this.dealFailClick }
          ]
        },
        { prop: 'sensFile', label: 'numberSensitive', width: '100', hidden: !this.hasPermission('113'), type: 'button',
          buttons: [
            { formatter: (row, data) => { return row.sensFile }, click: this.sensFileClick }
          ]
        },
        { prop: 'nonSensFile', label: 'numberNonSensitive', width: '120', hidden: !this.hasPermission('113') },
        { prop: 'nonProc', label: 'noDealNumber', width: '100', hidden: !this.hasPermission('C99'), type: 'button',
          buttons: [
            { formatter: (row, data) => { return row.nonProc }, click: this.nonProcClick }
          ]
        }
      ],
      statusOptions: {
        0: this.$t('pages.startScan'),
        1: this.$t('pages.endScan'),
        2: this.$t('pages.strategyExpire'),
        3: this.$t('pages.stopScan')
      },
      opTypeOptions: {
        5: this.$t('pages.diskScan_Msg3'),
        3: this.$t('pages.diskScan_Msg4'),
        1: this.$t('pages.fullDecryption'),
        2: this.$t('pages.globalEncryption'),
        7: this.$t('pages.diskScan_Msg47'),
        8: this.$t('pages.diskScan_Msg48'),
        9: this.$t('pages.diskScan_Msg49')
        /*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/
      }
    }
  },
  methods: {
    deleteLog,
    getScanDetailPage,
    gridTable() {
      return this.$refs['logList'].gridTable()
    },
    dateRangeChange(dateRange) {
      this.dateRange = dateRange
    },
    selectionChangeEnd(selection) {
      this.$emit('selectionChangeEnd', selection)
    },
    opTypeFormatter(row, data) {
      row.opType = row.opType + ''
      return this.opTypeOptions[data]
    },
    endTimeFormatter(row, data) {
      // 批量对终端下发全盘扫描敏感文件任务时，终端扫描耗时会比较久，有时候需要阶段性查询或阶段性导出扫描结果，当状态是开始扫描，却有一个扫描结束时间相悖，客户觉得不严谨。
      // 希望当在开始扫描状态时结束时间直接放空白
      if (data && row.status == 0) {
        return ''
      }
      return data
    },
    statusFormatter: function(row, data) {
      return this.statusOptions[data]
    },
    /**
     * 操作类型字典：
     * 1：全盘解密
     * 2：全盘加密
     * 3：全盘扫描敏感文件
     * 5：全盘扫描敏感文件并加密
     */
    /**
     * 处理成功数跳转
     * @param row
     */
    dealSucClick(row) {
      //  当操作类型为全盘扫描敏感文件时，跳转到敏感文件详情
      if (row.dealSuc) {
        //  参数1：此条扫描记录数据，参数2：操作类型，参数3：1-成功，0-失败
        this.$emit('changeTab', row, row.opType == 3 ? 3 : 1, 0)
      }
    },
    /**
     * 处理失败数跳转
     * @param row
     */
    dealFailClick(row) {
      //  当操作类型为全盘扫描敏感文件时，跳转到敏感文件详情
      if (row.dealFail) {
        this.$emit('changeTab', row, row.opType == 3 ? 3 : 2, 2)
      }
    },
    /**
     * 敏感文件数跳转
     * @param row
     */
    sensFileClick(row) {
      if (row.sensFile) {
        this.$emit('changeTab', row, 0, 1)
      }
    },
    /**
     * 不处理数跳转
     * @param row
     */
    nonProcClick(row) {
      if (row.nonProc) {
        this.$emit('changeTab', row, 3)
      }
    }
  }
}
</script>
