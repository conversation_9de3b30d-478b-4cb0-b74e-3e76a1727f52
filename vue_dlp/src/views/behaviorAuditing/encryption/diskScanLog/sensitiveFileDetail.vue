<!--
  全盘扫描记录 - 敏感文件详情
-->
<template>
  <disk-scan-log-common
    ref="logList"
    table-ref="sensitiveList"
    :query-params="queryParams"
    :permission-delete="permissionDelete"
    :col-model="colModel"
    :get-log-page="getSensitivePage"
    :is-show-file-detail="true"
    :detail-dialog-title="$t('pages.sensitiveFileDetails')"
    detail-dialog-width="700px"
    @dateRangeChange="dateRangeChange"
    @selectionChangeEnd="selectionChangeEnd"
    @playVideo="row => $emit('playVideo', row)"
  >
    <el-descriptions slot-scope="{ row }" class="margin-top" :column="2" size="" border>
      <el-descriptions-item :label="$t('table.taskNum')">
        {{ row.guid }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.operateTime')">
        {{ row.createTime }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.terminalName')">
        <terminal-detail
          :label="row.terminalName"
          :search-id="row.terminalId"
        />
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.user')">
        <user-detail
          :label="row.userName"
          :search-id="row.userId"
        />
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.operateType')">
        {{ opTypeFormatter(row, row.opType) }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('table.severity')">
        {{ severityFormatter(row, row.severity) }}
      </el-descriptions-item>
      <el-descriptions-item span="2" :label="$t('table.fileName')">
        {{ row.fileName }}
      </el-descriptions-item>
      <el-descriptions-item span="2" :label="$t('table.filePath')">
        {{ row.filePath }}
      </el-descriptions-item>
      <el-descriptions-item v-if="!$store.getters.desensitizeContentAble" span="2" :label="$t('table.sensitiveContent')">
        {{ row.content }}
      </el-descriptions-item>
      <el-descriptions-item v-if="!$store.getters.desensitizeContentAble" span="2" :label="$t('pages.violationRules')">
        {{ formatRuleStg(row.rules) }}
      </el-descriptions-item>
      <el-descriptions-item span="2" :label="$t('table.remark')">
        {{ row.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </disk-scan-log-common>
</template>

<script>
import DiskScanLogCommon from './common'
import { getSensitivePage, deleteSensitiveLog } from '@/api/behaviorAuditing/encryption/diskScanLog'
// import { getDictLabel } from '@/utils/dictionary'

export default {
  name: 'SensitiveFileDetail',
  components: { DiskScanLogCommon },
  props: {
    permissionDelete: { type: String, default: '465' }, // 删除权限
    queryParams: {
      type: Object,
      required: true
    },
    severityOptions: {
      type: Object,
      default() {
        return {}
      }
    },
    // lossTypeOptions: {
    //   type: Array,
    //   default() {
    //     return []
    //   }
    // },
    opTypeOptions: {
      type: Object,
      default() {
        return {}
      }
    },
    contentAwareResults: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dateRange: undefined,
      colModel: [
        { prop: 'guid', label: 'taskNum', fixedWidth: '150', sort: 'custom' },
        { prop: 'createTime', label: 'operateTime', width: '150' },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'opType', label: 'operateType', width: '150', formatter: this.opTypeFormatter },
        { prop: 'result', label: 'result', width: '150', formatter: this.resultFormatter },
        // { prop: 'lossType', label: '泄露方式', width: '150', formatter: this.lossTypeFormatter },
        { prop: 'severity', label: 'severity', width: '150', formatter: this.severityFormatter },
        // { prop: 'content', label: 'sensitiveContent', width: '150', hidden: () => { return this.$store.getters.desensitizeContentAble } },
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'filePath', label: 'filePath', width: '150' },
        { prop: 'sstTimes', label: '敏感次数', width: '150', formatter: this.sstTimesFormatter }
      ]
    }
  },
  methods: {
    getSensitivePage,
    deleteLog(data, headers) {
      return deleteSensitiveLog(data, headers)
    },
    gridTable() {
      return this.$refs['logList'].gridTable()
    },
    dateRangeChange(dateRange) {
      this.dateRange = dateRange
    },
    selectionChangeEnd(selection) {
      this.$emit('selectionChangeEnd', selection)
    },
    searchData(query) {
      this.gridTable().execRowDataApi(query)
    },
    opTypeFormatter(row, data) {
      row.opType = row.opType + ''
      return this.opTypeOptions[data]
    },
    // lossTypeFormatter: function(row, data) {
    //   return getDictLabel(this.lossTypeOptions, data)
    // },
    severityFormatter: function(row, data) {
      return this.severityOptions[data]
    },
    sstTimesFormatter(row, data) {
      if (data == 1) {
        return '首次新增'
      } else if (data == 2) {
        return '二次出现'
      } else if (data > 2) {
        return '三次及三次以上出现'
      } else {
        return ''
      }
    },
    /**
     * 违法的敏感内容检测策略名称
     * @param row
     * @param data
     */
    formatRuleStg(rules) {
      if (rules) {
        const data = JSON.parse(rules);
        return this.getRuleName(data.ruletype) + data.name
      }
      return null
    },
    /**
     * 根据规则类型获取规则类型名称
     * @param ruleType
     * 1：文件属性规则
     * 2：关键字规则
     * 3：数据标识符规则/正则规则
     * 4：数据库指纹规则
     * 12：源代码识别规则
     * 224：内容检测超时规则
     * 225：带密码文件敏感检测规则
     */
    getRuleName(ruleType) {
      let name = '';
      if (parseInt(ruleType)) {
        switch (ruleType) {
          case 1: name = this.$t('table.fileAttrSumAll'); break;
          case 2: name = this.$t('table.keywordSumAll'); break;
          case 3: name = this.$t('table.dataIdentifierSumAll'); break;
          case 4: name = this.$t('table.dbFingerprintSumAll'); break;
          case 12: name = this.$t('table.sourceCodeSumAll'); break;
          case 224: name = this.$t('pages.contentDetectionTimeoutRule'); break;
          case 225: name = this.$t('pages.fileWithPasswordSensitiveDetectionRule'); break;
        }
      }
      return name + (name.length ? '：' : '');
    },
    /**
     * 结果
     * @param row
     * @param data
     * @returns {*}
     */
    resultFormatter: function(row, data) {
      const result = this.contentAwareResults[data]
      if (!result) {
        for (const key in this.contentAwareResults) {
          if (key.indexOf(data) > -1 && (key.startsWith(data + ',') || key.endsWith(',' + data) || key.indexOf(',' + data + ',') > 0)) {
            return this.contentAwareResults[key]
          }
        }
      }
      return result
    }
  }
}
</script>
