<template>
  <el-dialog
    v-el-drag-dialog
    title="敏感文件扫描记录"
    width="800px"
    :modal="false"
    :append-to-body="appendToBody"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
  >
    <div class="list-panel">
      <TimeQuery ref="timeQuery" :limit-day="15" @getTimeParams="getTimeParams"/>
      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
        {{ $t('table.search') }}
      </el-button>
      <!-- <div v-for="(item, index) in detailItem" :key="index" class="list-item" :class="{ highlight: item.id === curId }" @click="handleClickItem(item, index)">
        <span class="list-item-key">{{ item.createTime }}</span>
      </div> -->
    </div>
    <grid-table
      ref="gridTableList"
      row-key="logId"
      :multi-select="false"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :height="400"
      :after-load="afterLoad"
    />
  </el-dialog>
</template>

<script>
import { getSensFileLogPage } from '@/api/behaviorAuditing/encryption/diskScanLog'

export default {
  name: 'SenFileRecord',
  components: {},
  props: {
    appendToBody: { type: Boolean, default: false }
    // query: {
    //   type: Object,
    //   default() {
    //     return {}
    //   }
    // }
  },
  data() {
    return {
      visible: false,
      colModel: [
        { prop: 'createTime', label: 'time', width: '150', sort: true },
        { prop: 'fileName', label: '文件名称', width: '60' },
        { prop: 'filePath', label: '文件路径', width: '120' },
        { prop: 'sstTimes', label: '命中次数', width: '120', formatter: this.sstTimesFormatter }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        guid: '',
        filePath: ''
      }
    }
  },
  created() {
  },
  methods: {
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    show(data) {
      this.visible = true
      this.query.guid = data.guid
      this.query.filePath = data.filePath
      this.query.createDate = data.createTime.split(' ')[0]
      this.$nextTick(() => {
        this.$refs['gridTableList'].execRowDataApi()
      })
    },
    rowDataApi(option) {
      const newOption = Object.assign(this.query, option)
      const searchQuery = Object.assign(newOption)
      return getSensFileLogPage(searchQuery)
    },
    afterLoad(rowData, grid) {
      console.log('rowData', rowData, grid);
    },
    handleFilter() {
      this.$refs['gridTableList'].execRowDataApi()
    },
    sstTimesFormatter(row, data) {
      if (data == 0 || !data) {
        return '非敏感文件'
      } else if (data == 1) {
        return '首次新增'
      } else if (data == 2) {
        return '二次出现'
      } else if (data > 2) {
        return '三次及三次以上出现'
      } else {
        return ''
      }
    }
  }
}
</script>

