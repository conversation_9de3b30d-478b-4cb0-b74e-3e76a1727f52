<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('pages.fileOrDirName') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 200px;"/>
        </SearchItem>
        <SearchItem model-key="query.operateType" :value="query.operateType">
          <span>{{ $t('table.operateType') }}：</span>
          <el-select v-model="query.operateType" clearable :placeholder="$t('table.operateType')" style="width: 130px;">
            <el-option
              v-for="item in operateTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'478'" multi-dataset :request="exportFunc"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" v-permission="'480'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'479'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        :autoload="autoload"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.httpWhiteListDecLogDetails')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize2')">
            {{ rowDetail.fileSizeStr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.operateType')">
            {{ formatOperateType(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.isDir')">
            {{ formatIsDir(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileOrDirName')">
            <el-button
              v-permission="'480'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.uploadFileGuid"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!480'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>
<script>
import { getLogPage, exportLog, deleteLog } from '@/api/behaviorAuditing/encryption/httpWhiteListDecLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'HttpWhiteListDecLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'devId', label: 'fileServerID', width: '150', hidden: true },
        { prop: 'isDir', label: 'isDir', width: '100', formatter: this.formatIsDir },
        { prop: 'webUrl', label: 'fileUploadURL', width: '100' },
        { prop: 'fileName', label: 'fileOrDirName', width: '150' },
        { prop: 'fileSizeStr', label: 'maxFileSize2', width: '100' },
        { prop: 'localFilePath', label: 'backupFilePath', width: '150', hidden: true },
        { prop: 'operateType', label: 'operateType', width: '100', formatter: this.formatOperateType },
        { prop: 'fileType', label: 'fileType', width: '100', formatter: this.formatFileType },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('480,481'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, isShow: () => this.hasPermission('480') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('481') }
          ]
        }
      ],
      showTree: true,
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList',
        fileName: null,
        operateType: null
      },
      tempTask: {},
      defaultTempTask: {
        backType: 1,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      sortable: true,
      selection: [],
      dialogFormVisible: false,
      rowDetail: {},
      termsInfo: [],
      operateTypeOption: [
        { label: this.$t('pages.downloadFile'), value: 2 },
        { label: this.$t('pages.uploadFile1'), value: 0 }
      ],
      queryVideoMethod: undefined
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.uploadFileGuid
      this.tempTask.fileName = row.fileName
      // 根据file_type来判断分享的是文件还是文件夹
      if ((row.extendParam & 1) === 1) {
        this.tempTask.fileName = this.tempTask.fileName + '.zip'
      }
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    selectionChangeEnd: function(rowDatas) {
      // 表格数据选中后
      this.selection = rowDatas
    },
    selectable(row, index) {
      // 表格数据是否可以选中
      return !this.downloadFormatter(row) || (this.$store.getters.auditingDeleteAble && this.hasPermission('479'))
    },
    downloadFormatter(data, btn) {
      return !data.uploadFileGuid
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          console.log('res:' + JSON.stringify(res))
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    exportFunc() {
      return exportLog(this.query)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    formatIsDir(row, data) {
      return (row.extendParam & 1) === 1 ? this.$t('text.yes') : this.$t('text.no')
    },
    formatOperateType(row, data) {
      return (row.extendParam & 2) === 2 ? this.$t('pages.downloadFile') : this.$t('pages.uploadFile1')
    },
    formatFileType(row, data) {
      if ((row.extendParam & 4) == 4) {
        return this.$t('pages.cipherText')
      } else if ((row.extendParam & 8) == 8) {
        return this.$t('pages.plainText')
      }
      return this.$t('pages.unknown')
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '481', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
