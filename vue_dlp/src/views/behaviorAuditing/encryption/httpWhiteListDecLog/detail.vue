<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.httpWhiteListDecLogDetails')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize2')">
            {{ rowDetail.fileSizeStr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.operateType')">
            {{ formatOperateType(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.isDir')">
            {{ formatIsDir(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileOrDirName')">
            <el-button
              v-permission="'480'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.uploadFileGuid"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!480'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: 'HttpWhiteListDecLogDetail',
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {}
    }
  },
  computed: {
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    formatIsDir(row, data) {
      return (row.extendParam & 1) === 1 ? this.$t('text.yes') : this.$t('text.no')
    },
    formatOperateType(row, data) {
      return (row.extendParam & 2) === 2 ? this.$t('pages.downloadFile') : this.$t('pages.uploadFile1')
    },
    handleDownload: function(row) {
      this.$emit('downloadAuditLogFile', row)
    }
  }
}
</script>
