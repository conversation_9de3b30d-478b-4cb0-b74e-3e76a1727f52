<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="tree"
        :data="treeData"
        resizeable
        default-expand-all
        :expand-on-click-node="false"
        :render-content="renderContent"
        @node-click="serverTreeNodeClick"
      />
    </div>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.alarmTypes" :value="query.alarmTypes">
          <span>{{ $t('table.bizType') }}:</span>
          <el-select v-model="query.alarmTypes" clearable is-filter :placeholder="$t('pages.all')" style="width: 160px;">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'490'" :request="handleExport"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'491'" icon="el-icon-delete" :disabled="disableDel" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table
        ref="logList"
        :col-model="colModel"
        :default-sort="{ prop: 'timestamp', order: 'desc' }"
        :row-data-api="rowDataApi"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      ></grid-table>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.eventDetails')"
      :visible.sync="detailVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.serviceType')">
            {{ rowDetail.devTypeName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.serviceNickName')">
            {{ rowDetail.detectObj }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.bizType')">
            {{ alarmTypeFormatter(undefined, rowDetail.alarmType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.treatmentMeasure')">
            <el-tooltip
              v-for="(item,index) in (rowDetail.alarmLimits || splitBitToArr(rowDetail.alarmLimits))"
              :key="index"
              effect="dark"
              placement="top-start"
            >
              <template slot="content">
                {{ getDictLabel(alarmLimitDict, item) }}
              </template>
              <svg-icon :icon-class="iconOptions[item]"/>
            </el-tooltip>
          </el-descriptions-item>
        </el-descriptions>
        <el-card :body-style="{'max-height': '320px', 'overflow-y': 'auto'}" class="box-card" style="max-height: 390px;">
          <div slot="header" class="clearfix" style="padding-left: 0;">
            <span>{{ $t('pages.alarmDetails') }}</span>
          </div>
          <el-tag v-if="rowDetail.timestamp">{{ parseTime(rowDetail.timestamp, 'y-m-d h:i:s') }}</el-tag>
          <p style="white-space: pre-wrap;">{{ rowDetail.eventDetail }}</p>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {
  deleteServiceAlarmLog,
  exportServiceAlarmLog,
  getPage
} from '@/api/behaviorAuditing/serviceAlarmLog/serviceAlarmLog'
import { parseTime } from '@/utils'
import { getAlarmLimitDict, getDict, getDictLabel } from '@/utils/dictionary';
import { getServerAlarmTree } from '@/api/system/deviceManage/serverAlarm'

export default {
  name: 'ServiceAlarmLog',
  data() {
    return {
      colModel: [
        { prop: 'timestamp', label: 'alarmTime', width: 20, formatter: this.timestampFormatter, sort: true },
        { prop: 'alarmType', label: 'bizType', width: 20, formatter: this.alarmTypeFormatter },
        { prop: 'devTypeName', label: 'serviceType', width: 20 },
        { prop: 'detectObj', label: 'serviceNickName', width: 20 },
        { prop: '', label: 'treatmentMeasure', width: 20, iconFormatter: this.iconAlarmLimitFormatter, formatter: this.dealStatusFormatter },
        { prop: 'eventDetail', label: 'details', width: 60 },

        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('492'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: {
        alarmTypes: undefined
      },
      treeData: [
        { id: 0, dataId: 0, label: this.$t('table.serverList'), children: [] }
      ],
      showTree: true,
      alarmLimitDict: getAlarmLimitDict().filter(dict => dict.value === 4 || dict.value === 8),
      iconOptions: {
        4: 'alarm-02',
        8: 'alarm-03'
      },
      typeOptions: [
        { value: 5, label: this.$t('pages.serverProgramSpaceAlarm') },
        { value: 6, label: this.$t('pages.serverDataSpaceAlarm') },
        { value: 7, label: this.$t('pages.serverSpaceAlarm') },
        { value: 3, label: this.$t('pages.serverCpuAlarm') },
        { value: 4, label: this.$t('pages.serverMemoryAlarm') }
        // { typeId: 11, remark: this.$t('pages.serverOfflineAlarm') }
      ],
      rowDetail: {},
      detailVisible: false,
      disableDel: true
    }
  },
  watch: {
    '$store.state.commonData.notice.serviceAlarmDetail'() {
      const alarmMsg = this.$store.getters.alarmMsg
      const ids = alarmMsg.id
      const createDate = parseTime(alarmMsg.createTime, 'y-m-d')
      this.query.alarmTypes = ''
      this.$refs['logList'] && this.$refs['logList'].execRowDataApi({ ids, isTimes: false, createDate, devType: '', devId: '' })
    }
  },
  created() {
    this.initServerTree()
  },
  methods: {
    parseTime,
    getDictLabel,
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    timestampFormatter(row, data) {
      return parseTime(data, 'y-m-d h:i:s')
    },
    dealStatusFormatter(row, data) {
      return ((!row.dealStatus && row.dealStatus != 0) || row.dealStatus == -1) ? this.$t('pages.maintNotAlarm') : ''
    },
    alarmTypeFormatter(row, data) {
      return getDictLabel(this.typeOptions, data) || ''
    },
    iconAlarmLimitFormatter(row) {
      if ((!row.dealStatus && row.dealStatus != 0) || row.dealStatus == -1) { return '' }
      const resultBits = row.alarmLimits || this.splitBitToArr(row.alarmLimit)
      return resultBits.map(alarmLimit => getDict(this.alarmLimitDict, alarmLimit)).filter(r => !!r).map(r => {
        return { class: this.iconOptions[r.value], title: r.label }
      })
    },
    rowDataApi(option) {
      if (this.$route.params.id) {
        this.query.ids = this.$route.params.id
        this.query.createDate = parseTime(this.$route.params.createTime, 'y-m-d')
        this.$route.params.id = ''
        this.$route.params.createTime = ''
      }
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery).then(res => {
        res.data.items.forEach(row => {
          if ((!row.dealStatus && row.dealStatus != 0) || row.dealStatus == -1) { return '' }
          row.alarmLimits = this.splitBitToArr(row.alarmLimit)
        })
        return res
      })
    },
    serverTreeNodeClick(data, node, el) {
      const opts = { devType: data['type'], devId: data.id > 0 ? data.id : undefined }
      Object.assign(this.query, opts)
      this.$refs['logList'].execRowDataApi()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleFilter() {
      this.query.page = 1
      this.query.ids = ''
      this.$refs['logList'].execRowDataApi(this.query)
    },
    handleExport() {
      return exportServiceAlarmLog(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.$refs['logList'].getSelectedDatas() || []
        if (toDelete.length == 0) { return }
        deleteServiceAlarmLog(toDelete).then(respond => {
          this.$refs['logList'].deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleView(row) {
      this.rowDetail = Object.assign({}, row)
      this.detailVisible = true
    },
    renderContent(h, { node, data, store }) {
      const iconClass = data.id <= 0 ? '' : 'server';
      return (
        <div>
          <svg-icon v-show={!!iconClass} icon-class={!iconClass ? '' : iconClass} />
          <span>{data.label}</span>
        </div>
      )
    },
    splitBitToArr(alarmLimit) {
      const resultBits = []
      let temp = alarmLimit
      let r = 1
      while (temp > 0) {
        if ((temp & 1) === 1) {
          resultBits.push(r)
        }
        temp >>= 1
        r <<= 1
      }
      return resultBits
    },
    selectionChangeEnd(rowDatas) {
      this.disableDel = !(rowDatas && rowDatas.length > 0)
    },
    initServerTree() {
      getServerAlarmTree().then(res => {
        this.treeData[0].children.splice(0, this.treeData.length, ...res.data)
      })
    }
  }
}
</script>

<style scoped>

</style>
