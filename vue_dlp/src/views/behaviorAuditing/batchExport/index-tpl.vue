<!--
  此文件为批量导出审计日志的模板文件，请不要随意修改
-->
<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <TimeQuery @getTimeParams="getTimeParams"/>
        <audit-log-exporter batch :name="getFileName" :request="handleExport"/>
        <el-button type="text" @click="handleCheckAllChange(true)">{{ $t('button.selectAll') }}</el-button>
        <el-button type="text" @click="handleCheckAllChange(false)">{{ $t('button.cancelSelectAll') }}</el-button>
      </div>
      <div class="tableBox">
        <div v-permission="'#groupPermissions'" class="export-group">
          <el-divider class="export-group-label" content-position="left">{{ formatLogTitle('#groupTitle') }}</el-divider>
          <div class="export-group-items">
            <!-- #itemComponentPath -->
            <div v-permission="'#permission'" class="export-group-item">
              <el-checkbox v-model="checkedMenus['#permission']" class="export-group-item-label">{{ formatLogTitle('#itemTitle') }}</el-checkbox>
              <el-button class="export-group-item-route" @click="$router.push({name:'#itemName'})">E</el-button>
              <div class="export-group-item-content">

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { exportAuditLogs } from '@/api/behaviorAuditing/batchExport'
import moment from 'moment'

export default {
  name: 'BatchExportLogTpl',
  data() {
    return {
      showTree: true,
      checkedMenus: {},
      query: {
        common: { // 查询条件
          objectType: undefined,
          objectId: undefined,
          createDate: '',
          startDate: '',
          endDate: '',
          isTimes: false
        }
      }
    }
  },
  computed: {

  },
  created() {

  },
  methods: {
    formatLogTitle(title) {
      if (this.$te(`route.${title}`)) {
        return this.$t(`route.${title}`)
      }
      return title
    },
    strategyTargetNodeChange(tabName, checkedNode) {
      if (checkedNode) {
        this.query.common.objectType = checkedNode.type
        this.query.common.objectId = checkedNode.dataId
      } else {
        this.query.common.objectType = undefined
        this.query.common.objectId = undefined
      }
    },
    getTimeParams(timeObj) {
      Object.assign(this.query.common, timeObj)
    },
    getFileName() {
      let name = this.$t('route.' + this.$route.meta.title) + '(';
      if (this.query.common.isTimes) {
        name += this.query.common.startDate + '~' + this.query.common.endDate
      } else {
        name += this.query.common.createDate
      }
      return name + ')_' + moment().format('YYYY-MM-DD HH-mm-ss')
    },
    handleExport(exportType, fileName) {
      const data = { exportType, fileName, selected: {}, ...this.query.common }
      Object.entries(this.checkedMenus).forEach(([menu, checked]) => {
        if (checked && this.hasPermission(menu)) {
          data.selected[menu] = this.query[menu] || {}
        }
      })
      return exportAuditLogs(data)
    },
    handleCheckAllChange(checked) {
      for (const key in this.checkedMenus) {
        if (this.checkedMenus.hasOwnProperty(key)) {
          this.checkedMenus[key] = checked
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .tableBox {
    overflow: auto;
    .export-group {
      .export-group-label {
        font-size: 16px;
        font-weight: 700;
        color: #2d8cf0;
      }
      .export-group-items {
        font-size: 14px;
        padding-left: 10px;
        line-height: 50px;
        .export-group-item {
          .export-group-item-label {
            display: inline-block;
            letter-spacing: 2px;
            font-size: 15px;
            font-weight: 700;
          }
          .export-group-item-route {
            margin-right: 20px;
            line-height: 28px;
            padding: 0 9px;
          }
          .export-group-item-content {
            display: inline-block;
            label {
              font-weight: 500;
            }
            /* 全盘扫描日志 */
            .myCascader {
              width: 180px;
              line-height: 30px;
              >>> .el-input .el-input__inner {
                height: 30px !important;
              }
              >>> .el-cascader__tags {
                top: 1px!important;
              }
            }
          }
        }
      }
    }
  }
</style>
