<template>
  <div class="app-container">

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        :tree-menu-btn="false"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.kw" :value="query.kw">
          <span>{{ $t('table.signReportName') }}：</span>
          <el-input v-model="query.kw" clearable style="width: 200px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'488'" multi-dataset :request="handleExport"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'489'" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table
        ref="logList"
        :col-model="colModel"
        :default-sort="defaultSort"
        :row-data-api="rowDataApi"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
  </div>
</template>

<script>
import { getAlarmDetail } from '@/api/behaviorAuditing/systemLog/sysAlarmLog'
import { list, exportExcel, deleteLog } from './signReportPushLogApi'

export default {
  name: 'SignReportPushLog',
  props: {
    logType: {
      type: Number,
      default: 1  // 1系统日志，2终端操作日志
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'logStartTime', width: '120', sort: true },
        { prop: 'signReportName', label: 'signReportName', width: '100' },
        { prop: 'alarmType', label: 'alarmType', width: '100', formatter: this.alarmTypeFormatter },
        { prop: 'receiver', label: 'alarmObject', width: '80', formatter: this.alarmObjectFormatter },
        { prop: 'alarmJson', label: 'alarmDesc', width: '150', formatter: this.alarmDescFormatter }
        // { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('398'),
        //   buttons: [
        //     { label: 'detail', click: this.handleView }
        //   ]
        // }
      ],
      alarmTypeOptions: {
        1: this.$t('pages.popAlarm'),
        2: this.$t('pages.emailAlarm')
      },
      defaultSort: { prop: 'createTime', order: 'descending' },
      query: { // 查询条件
        page: 1,
        kw: '',
        createDate: '',
        startDate: '',
        menuCode: this.$route.meta.code,
        tableKey: 'logList',
        endDate: ''
      },
      dateTypeOptions: [
        { value: 5, label: this.$t('pages.dateTypeOptions1') },
        { value: 2, label: this.$t('pages.dateTypeOptions2') },
        { value: 1, label: this.$t('pages.dateTypeOptions3') },
        { value: 3, label: this.$t('pages.dateTypeOptions4') },
        { value: 4, label: this.$t('pages.dateTypeOptions5') },
        { value: 6, label: this.$t('pages.countByTimeRange') }
      ],
      showTree: true,
      deleteable: false,
      rowDetail: {},
      dialogFormVisible: false
    }
  },
  computed: {
    // 系统管理员列表
    sysUserList() {
      return this.$store.getters.sysUserList
    },
    gridTable() {
      return this.$refs['logList']
    }
  },
  watch: {
    '$store.state.commonData.notice.generalAlarmDetail'() {
      this.query.id = this.$store.getters.alarmMsg.id
      this.gridTable.execRowDataApi(this.query)
    }
  },
  created() {
    if (this.$route.params.id) {
      this.query.id = this.$route.params.id
      this.query.createDate = ''
      this.$route.params.id = ''
    }
    list(this.query).then(console.log)
  },
  methods: {
    handleNodeClickFunc(tabName, data) {
      if (data) {
        this.query.objectType = data.type
        this.query.objectId = data.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return list(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.query.id = ''
      console.log(this.query)
      this.gridTable.execRowDataApi(this.query)
    },
    handleView(row) {
      getAlarmDetail(row.id).then(respond => {
        this.rowDetail = respond.data
        this.dialogFormVisible = true
      })
    },
    alarmTypeFormatter: function(row, data) {
      return this.alarmTypeOptions[data]
    },
    alarmObjectFormatter: function(row, data) {
      if (row.alarmType == 2) {
        return data
      }
      if (row.alarmType == 1 && data == 0) {
        return this.$t('pages.allSysUsers')
      }
      const idSet = new Set(data.split(',').map(Number))
      return this.sysUserList.filter(user => idSet.has(user.id)).map(user => user.name).join(', ') || this.$t('text.unknown')
    },
    alarmDescFormatter: function(row, data) {
      const json = JSON.parse(data)
      return `
        ${this.$t('pages.dimBaseType')}: ${this.dateTypeOptions.find(v => v.value == json.timeDim).label},
        ${this.$t('table.normal')}: ${json.normal},
        ${this.$t('table.important')}: ${json.important},
        ${this.$t('table.serious')}: ${json.serious}
      `
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    }
  }
}
</script>
