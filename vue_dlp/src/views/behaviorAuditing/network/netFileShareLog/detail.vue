<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.netFileShareLogDetails')"
    :visible.sync="dialogFormVisible"
    width="700px"
  >
    <div class="show-detail-panel">
      <el-descriptions class="margin-top" :column="2" size="" border>
        <el-descriptions-item :label="$t('table.operateTime')">
          {{ rowDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.terminalName')">
          <terminal-detail
            :label="rowDetail.terminalName"
            :search-id="rowDetail.terminalId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.user')">
          <user-detail
            :label="rowDetail.userName"
            :search-id="rowDetail.userId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.operateType')">
          {{ fileOpTypeFormatter(rowDetail, rowDetail.fileOpType) }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.maxFileSize2')">
          {{ rowDetail.fileSizeStr }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.fileName')">
          <el-button
            v-permission="'242'"
            type="text"
            style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
            :disabled="!rowDetail.fileGuid"
            @click="handleDownload(rowDetail)"
          >
            {{ rowDetail.fileName }}
          </el-button>
          <span v-permission="'!242'">{{ rowDetail.fileName }}</span>
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.action')">
          {{ actionFormatter(rowDetail, rowDetail.actionType) }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.filePath')">
          {{ rowDetail.filePath }}
        </el-descriptions-item>

      </el-descriptions>
    </div>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'242'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
  </el-dialog>
</template>
<script>

import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'NetFileShareRecordDetail',
  components: { AuditFileDownloader },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      selection: [],
      defaultTempTask: {
        backType: 1,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      opTypes: {
        1: this.$t('pages.copyFile'),
        2: this.$t('pages.cutFile'),
        4: this.$t('pages.saveFile')
      }
    }
  },
  computed: {
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    handleDownload: function(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      tempTask.fileName = row.allFileName ? row.allFileName : row.fileName
      return tempTask
    },
    fileOpTypeFormatter: function(row, data) {
      if (data !== null && data !== '' && data !== undefined) {
        return this.opTypes[data]
      }
      return ''
    }
  }
}
</script>
