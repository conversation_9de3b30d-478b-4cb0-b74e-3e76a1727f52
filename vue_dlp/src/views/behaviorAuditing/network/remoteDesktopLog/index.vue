<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.cmdType" :value="query.cmdType">
          <span>{{ $t('table.remoteOperateType') }}：</span>
          <el-select v-model="query.cmdType" style="width: 150px;">
            <el-option v-for="item in cmdTypeOpts" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('pages.fileName') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <SearchItem model-key="query.actionType" :value="query.actionType">
          <span>{{ $t('table.isBlock') }}：</span>
          <el-select v-model="query.actionType" style="width: 150px" @change="changeActionType">
            <el-option :label="$t('pages.all')" :value="null"/>
            <el-option :label="$t('pages.ifTimeout2')" :value="1"></el-option>
            <el-option :label="$t('pages.ifTimeout1')" :value="0"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'486'" :request="handleExport"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" v-permission="'494'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'487'" :selection="tableSelects" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :selectable="selectable"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :autoload="autoload"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.mstscLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.remoteComputerIp')">
            {{ rowDetail.connectIp }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.isBlock')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.remoteOperateType')">
            {{ cmdTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="showFile" span="2" :label="$t('table.fileName')">
            <el-button
              v-if="hasPermission('494')"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-else>{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item v-if="showFile" span="2" :label="$t('table.maxFileSize1')">
            {{ rowDetail.fileSize }}
          </el-descriptions-item>
          <el-descriptions-item v-if="showFile" span="2" :label="$t('table.filePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { exportExcel, getLogPage, deleteLog } from '@/api/behaviorAuditing/network/remoteDesktopLog'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { getDictLabel } from '@/utils/dictionary'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'RemoteDesktopLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      query: {
        page: 1,
        objectType: undefined,
        objectId: undefined,
        fileName: '',
        cmdType: null,
        actionType: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150' },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '100', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'connectIp', label: 'remoteComputerIp', width: '150' },
        { prop: 'actionType', label: 'isBlock', width: '120', formatter: this.actionFormatter },
        { prop: 'cmdType', label: 'remoteOperateType', width: '100', formatter: this.cmdTypeFormatter },
        { prop: 'fileName', label: 'fileName1', width: '100' },
        { prop: 'fileSize', label: 'maxFileSize1', width: '100', formatter: this.fileSizeFormatter },
        { prop: 'filePath', label: 'filePath', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('485,494'),
          buttons: [
            { label: 'download', disabledFormatter: this.downloadFormatter, click: this.handleDownload, isShow: (row) => row.cmdType != 3 && row.cmdType != 4 && this.hasPermission('494') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('485') }
          ]
        }
      ],
      defaultTempTask: {
        backType: 31,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      showTree: true,
      sortable: true,
      dialogFormVisible: false,
      selection: [],
      tableSelects: [],
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined,
      cmdTypeOpts: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.outgoingFile'), value: 1 },
        // { label: this.$t('pages.downloadFile'), value: 2 },
        { label: this.$t('pages.connectDesktop'), value: 3 }
        // { label: this.$t('pages.disconnect'), value: 4 }
      ]
    }
  },
  computed: {
    showFile() {
      const cmdType = (this.rowDetail || {}).cmdType
      return cmdType && cmdType != 3 && cmdType != 4
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    selectable(row, index) {
      return !this.downloadFormatter(row) || (this.$store.getters.auditingDeleteAble && this.hasPermission('487'))
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    changeActionType(val) {
      if (val == 1) {
        this.query.fileName = ''
      }
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    actionFormatter(row, data) {
      return data & 1 > 0 ? this.$t('pages.ifTimeout2') : this.$t('pages.ifTimeout1')
    },
    cmdTypeFormatter: function(row, data) {
      return getDictLabel(this.cmdTypeOpts, row.cmdType)
    },
    fileSizeFormatter(row, data) {
      return (row.cmdType == 3 || row.cmdType == 4) ? '' : data
    },
    rowDataApi(options) {
      const searchQuery = Object.assign({}, this.query, options)
      this.query.sortName = options.sortName
      this.query.sortOrder = options.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    handleExport(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      // 如果备份文件存在后缀，以备份文件的后缀为主
      if (row.localFilePath.lastIndexOf('\\') >= 0) {
        const backFileName = row.localFilePath.substr(row.localFilePath.lastIndexOf('\\') + 1, row.localFilePath.length)
        if (backFileName.indexOf('.') != -1) {
          const suffix = backFileName.substr(backFileName.lastIndexOf('.') + 1, backFileName.length)
          tempTask.fileName = row.fileName + '.' + suffix
        } else {
          tempTask.fileName = row.fileName
        }
      } else {
        tempTask.fileName = row.fileName
      }
      return tempTask
    },
    handleDownload(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '485', this.query.searchReport)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = (rowDatas || []).filter(item => item.cmdType != 3 && item.cmdType != 4)
      this.tableSelects.splice(0, this.tableSelects.length, ...rowDatas)
    }
  }
}
</script>

<style scoped>

</style>
