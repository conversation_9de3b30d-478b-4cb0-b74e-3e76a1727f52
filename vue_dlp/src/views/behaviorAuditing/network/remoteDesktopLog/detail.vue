<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.mstscLogDetails')"
    :visible.sync="dialogFormVisible"
    width="700px"
  >
    <div class="show-detail-panel">
      <el-descriptions class="margin-top" :column="2" size="" border>
        <el-descriptions-item :label="$t('table.time')">
          {{ rowDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.terminalName')">
          <terminal-detail
            :label="rowDetail.terminalName"
            :search-id="rowDetail.terminalId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.user')">
          <user-detail
            :label="rowDetail.userName"
            :search-id="rowDetail.userId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.remoteComputerIp')">
          {{ rowDetail.connectIp }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.isBlock')">
          {{ actionFormatter(rowDetail, rowDetail.actionType) }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.remoteOperateType')">
          {{ cmdTypeFormatter(rowDetail) }}
        </el-descriptions-item>
        <el-descriptions-item v-if="showFile" span="2" :label="$t('table.fileName')">
          <el-button
            v-if="hasPermission('494')"
            type="text"
            style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
            :disabled="!rowDetail.fileGuid"
            @click="handleDownload(rowDetail)"
          >
            {{ rowDetail.fileName }}
          </el-button>
          <span v-else>{{ rowDetail.fileName }}</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="showFile" span="2" :label="$t('table.maxFileSize1')">
          {{ rowDetail.fileSize }}
        </el-descriptions-item>
        <el-descriptions-item v-if="showFile" span="2" :label="$t('table.filePath')">
          {{ rowDetail.filePath }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'494'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
  </el-dialog>
</template>
<script>

import { getDictLabel } from '@/utils/dictionary';
import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'RemoteDesktopLogDetail',
  components: { AuditFileDownloader },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      selection: [],
      defaultTempTask: {
        backType: 31,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      cmdTypeOpts: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.outgoingFile'), value: 1 },
        // { label: this.$t('pages.downloadFile'), value: 2 },
        { label: this.$t('pages.connectDesktop'), value: 3 }
        // { label: this.$t('pages.disconnect'), value: 4 }
      ]
    }
  },
  computed: {
    showFile() {
      const cmdType = (this.rowDetail || {}).cmdType
      return cmdType && cmdType != 3 && cmdType != 4
    }
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    actionFormatter: function(row, data) {
      return data & 1 > 0 ? this.$t('pages.ifTimeout2') : this.$t('pages.ifTimeout1')
    },
    handleDownload: function(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      // 如果备份文件存在后缀，以备份文件的后缀为主
      if (row.localFilePath.lastIndexOf('\\') >= 0) {
        const backFileName = row.localFilePath.substr(row.localFilePath.lastIndexOf('\\') + 1, row.localFilePath.length)
        if (backFileName.indexOf('.') != -1) {
          const suffix = backFileName.substr(backFileName.lastIndexOf('.') + 1, backFileName.length)
          tempTask.fileName = row.fileName + '.' + suffix
        } else {
          tempTask.fileName = row.fileName
        }
      } else {
        tempTask.fileName = row.fileName
      }
      return tempTask
    },
    cmdTypeFormatter: function(row, data) {
      return getDictLabel(this.cmdTypeOpts, row.cmdType)
    }
  }
}
</script>
