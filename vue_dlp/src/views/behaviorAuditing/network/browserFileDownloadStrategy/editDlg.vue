<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @close="close"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 748px;"
      >
        <StgBaseFormItem
          :stg-code="63"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="strategyDefType"
          :formable="formable"
          :active-able="!strategyDefType"
        />

        <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
        <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.adb_limit_Msg1') }}</span>
            <el-button v-if="formable" size="small" @click="fileLibClick()">
              {{ $t('pages.importFileSuffixLib') }}
            </el-button>
            <el-button v-if="formable" size="small" @click="closeFileLibClick">
              {{ $t('button.clear') }}
            </el-button>
            <span>{{ $t('pages.fileSuffixMaxNumberTip', { number: suffixMaxNum }) }}</span>
          </div>
          <tag v-model="fileSuffixes" :border="true" max-height="100px" :disabled="!formable" :overflow-able="true" :list="fileSuffixes" :limit-size="suffixMaxNum" @tagChange="tagChange"></tag>
        </el-card>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <FormItem label-width="28px">
          <el-radio-group v-model="temp.recordDetectionRule" :disabled="!formable" style="margin-left: 0">
            <el-radio :label="1">{{ $t('pages.browserDownloadLoadInMsg') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.browserDownloadLoadOutMsg') }}</el-radio>
          </el-radio-group>
          <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.executeRuleTip', {info: $t('table.suffixes'), info1: $t('table.suffixes')}) }}</label>
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.withinDetectionRuleTip">
                <span slot="info">{{ $t('pages.forbidRecordFileDownloadToWebpage') }}</span>
              </i18n>
              <br/>
              <i18n path="pages.outsideDetectionRuleTip">
                <span slot="info">{{ $t('pages.allowRecordFileDownloadToWebpage') }}</span>
              </i18n>
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
        </FormItem>
        <el-divider content-position="left" style="margin: 0 15px">{{ $t('pages.responseRule') }}</el-divider>
        <div class="response-rule">
          <FormItem label-width="28px" prop="fileBackUpLimit">
            <el-checkbox v-model="temp.recordLog" :disabled="!formable" :false-label="0" :true-label="1" @change="recordLogChange">
              <span style="margin-left: 8px">{{ $t('pages.recordBrowserDownloadLog') }}</span>
            </el-checkbox>
            <div style="height: 2px"/>
            <el-checkbox v-model="needBackup" :disabled="!formable || temp.recordLog === 0" @change="needBackupChange">
              <i18n path="pages.blueTooth_Msg1" style="margin-left: 8px">
                <el-input-number
                  slot="size"
                  v-model="temp.fileBackUpLimit"
                  :disabled="!needBackup || !formable"
                  :controls="false"
                  :min="1"
                  :max="10240"
                  :step="1"
                  step-strictly
                  style="width: 80px;"
                  size="mini"
                />
              </i18n>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.ftpControl_text3') }}<br/>
                  {{ $t('pages.ftpControl_text4') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-button :disabled="!formable || temp.recordLog === 0" style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
            </el-checkbox>
          </FormItem>
        </div>
        <div style="white-space: pre-wrap;color: #2b7aac;width: 50px">{{ $t('text.prompt') }}：</div>
        <span style="white-space: pre-wrap;color: #2b7aac;">
          <i18n path="pages.webpageDownloadFileTip">
            <br slot="br"/>
          </i18n>
        </span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="256"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="strategyDefType"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="close">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
    <file-suffix-lib-import ref="fileSuffixLibImport" :append-to-body="true" @importfilesuffix="importFileSuffix"/>
  </div>
</template>
<script>
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent';
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import { entityLink } from '@/utils';
import { validatePolicy } from '@/utils/validate';
import { createData, updateData, getDataByName } from '@/api/behaviorAuditing/network/browserFileDownloadStrategy';

export default {
  name: 'BrowserFileDownloadStrategyEditDlg',
  components: { BackupRuleContent, FileSuffixLibImport },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    strategyDefType: { type: Number, default: 0 } //  1-预定义策略，0-应用策略
  },
  data() {
    return {
      submitting: false,
      temp: { },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        fileBackUpLimit: 20,  //  文件备份阈值, 当为0时，文件备份关闭
        entityType: '',
        entityId: undefined,
        encBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined, // 备份过滤规则id
        // monitorWay 取值规则如下：
        //  执行规则配置规则之外 并且勾选了文件记录且有配置文件后缀 ---0
        //  执行规则配置规则之内 并且勾选文件记录且有配置文件后缀   --- 1
        //  执行规则配置规则之外 并且勾选了文件记录且没有配置文件后缀 ---2
        //  执行规则配置规则之内 并且勾选文件记录且没有配置文件后缀   --- 3
        //  只要没有勾选网页下载文件记录 --- 3
        monitorWay: 1,
        recordLog: 0,  //  是否记录日志，仅前端使用，不下发给终端
        recordDetectionRule: 0, //  1-检测规则之内，0-检测规则之外
        downloadAuditSuffixes: [] // 配置的文件后缀 元素格式：{ suffix: '', matchKind: 1 } suffix:文件后缀， matchKind：文件后缀匹配规则 0-模糊匹配，1-完全匹配， 默认为完全匹配
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileBackUpLimit: [
          { validator: this.fileBackUpLimitValidator, trigger: 'blur' }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.browserFileDownloadStrategy'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.browserFileDownloadStrategy'), 'create')
      },
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined, // 备份过滤规则id
      needBackup: false,  //  是否备份
      fileSuffixes: [],  //  保存指定的文件后缀
      suffixMaxNum: 20 //  允许输入的文件后缀最大数量
    }
  },
  created() {
  },
  methods: {
    //  初始化数据
    initData() {
      this.temp = Object.assign({}, this.defaultTemp);
      this.fileSuffixes = []
      this.needBackup = false
    },
    //  创建
    handleCreate(query) {
      this.dialogStatus = 'create'
      this.initData()
      this.temp.entityType = query.objectType
      this.temp.entityId = query.objectId
      this.dialogFormVisible = true
    },
    //  修改
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.initData()
      this.formatInitData(row);
      this.dialogFormVisible = true
    },
    //  更新操作时格式化数据
    formatInitData(row) {
      this.temp = Object.assign(this.temp, row)
      //  备份文件限制
      this.needBackup = !!row.fileBackUpLimit
      //  当fileBackUpLimit==0时，设置默认值20
      if (!this.temp.fileBackUpLimit) {
        this.temp.fileBackUpLimit = 20
      }
      this.fileSuffixes = []
      //  文件后缀转换
      if (row.downloadAuditSuffixes && row.downloadAuditSuffixes.length > 0) {
        row.downloadAuditSuffixes.forEach(info => {
          this.fileSuffixes.push(info.suffix);
        })
      }
    },
    close() {
      this.$refs.dataForm.clearValidate()
      this.dialogFormVisible = false
      this.initData()
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this.$parent)
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.encBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    //  设备备份过滤规则
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.encBackupRule = checkRule
    },
    //  记录-change
    recordLogChange(val) {
      if (!val) {
        this.needBackup = false
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    },
    //  文件备份-change
    needBackupChange(val) {
      if (!val) {
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    //  备份文件大小阈值校验
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.needBackup) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else if (value > 10240) {
          return callback(new Error(this.$t('pages.blueTooth_Msg4')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    formatFormData() {
      const formatData = JSON.parse(JSON.stringify(this.temp));
      //  文件后缀转换
      formatData.downloadAuditSuffixes = []
      if (this.fileSuffixes.length > 0) {
        this.fileSuffixes.forEach(suffix => {
          //  matchKind：文件后缀匹配规则，0-模糊匹配，1-完全匹配，现阶段仅支持完全匹配
          formatData.downloadAuditSuffixes.push({ suffix: suffix, matchKind: 1 });
        })
      }
      formatData.monitorWay = null
      //  设置  monitorWay
      if (this.temp.recordLog === 1 && this.temp.recordDetectionRule === 1 && this.fileSuffixes.length > 0) {
        formatData.monitorWay = 1
      } else if (this.temp.recordLog === 1 && this.temp.recordDetectionRule === 0 && this.fileSuffixes.length === 0) {
        formatData.monitorWay = 2
      } else if (this.temp.recordLog === 0 || (this.temp.recordDetectionRule === 1 && this.fileSuffixes.length === 0)) {
        formatData.monitorWay = 3
      } else if (this.temp.recordLog === 1 && this.temp.recordDetectionRule === 0) {
        formatData.monitorWay = 0
      }
      //  未开启文件备份阈值时， fileBackUpLimit = 0
      if (!this.needBackup) {
        formatData.fileBackUpLimit = 0
      }
      return formatData;
    },
    /**
     * 校验数据是否合法
     */
    validData() {
      //  不允许文件后缀的数量超过最大值
      if (this.fileSuffixes.length > this.suffixMaxNum) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.fileSuffixOutnumberErrorMsg1', { number: this.suffixMaxNum }),
          type: 'error',
          duration: 2000
        });
        return false;
      }
      return true;
    },
    //  创建策略
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.strategyDefType) || !this.validData()) {
        return
      }
      const formData = this.formatFormData()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          createData(formData).then(() => {
            this.submitting = false
            this.initData()
            this.dialogFormVisible = false
            this.$emit('submit')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        }
      })
    },
    //  更新策略
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.strategyDefType) || !this.validData()) {
        return
      }
      const formData = this.formatFormData()
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          updateData(formData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.initData()
            this.$emit('submit')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        }
      })
    },
    fileLibClick() {
      this.$refs.fileSuffixLibImport.show()
    },
    //  清空
    closeFileLibClick() {
      this.fileSuffixes.splice(0)
    },
    //  文件后缀导入
    importFileSuffix(suffix) {
      // 将原有后缀与导入后缀合并
      const newSuffix = this.fileSuffixes.concat(suffix.split('|'))
      // 过滤重复数据
      const filterSuffix = this.validAddData(newSuffix);
      // 是否超出最大数量
      const isOverRange = filterSuffix.length > this.suffixMaxNum
      if (isOverRange) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.fileSuffixOutnumberErrorMsg2', { number: this.suffixMaxNum }),
          type: 'warning',
          duration: 2000
        })
        // 截止至最大数量
        this.fileSuffixes = filterSuffix.slice(0, this.suffixMaxNum)
      } else {
        this.fileSuffixes = filterSuffix
      }
    },
    //  当文件后缀列表发送改变时，校验文件后缀名是否符合规则
    tagChange(list) {
      this.fileSuffixes = this.validAddData(list)
    },
    // 过滤重复后缀并规范后缀格式
    validAddData(list) {
      list = this.filterRepetitionFileSuffix(list);
      list = this.validAddDotMark(list);
      return list;
    },
    //  过滤重复文件后缀库
    filterRepetitionFileSuffix(list) {
      const uniqueSet = new Set()
      //  过滤掉相同文件后缀名(不区分大小写）
      return list.filter(item => {
        const lowerCase = item.toLowerCase()
        const isUnique = !uniqueSet.has(lowerCase)
        isUnique && uniqueSet.add(lowerCase)
        return isUnique
      })
    },
    //  校验后缀名是否正确，若首个文件后缀名称不是.  自动添加
    validAddDotMark(list) {
      return list.map(item => {
        return item[0] === '.' ? item : `.${item}`
      })
    }
  }
}
</script>
