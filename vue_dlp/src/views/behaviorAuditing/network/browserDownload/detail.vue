<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.browserDownloadDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize1')">
            {{ rowDetail.fileSize }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileName1')">
            <el-button
              v-if="hasPermission('502')"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-else>{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.browserType')">
            {{ browserTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.fileDownloadPath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'502'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
  </div>
</template>
<script>

import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'BrowserDownloadLogDetail',
  components: { AuditFileDownloader },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      selection: [],
      defaultTempTask: {
        backType: 3,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      browserTypeOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.browserOptions2'), value: 1 },
        { label: this.$t('pages.browserOptions5'), value: 2 },
        { label: this.$t('pages.browserOptions7'), value: 3 },
        { label: this.$t('pages.browserOptions3'), value: 4 },
        { label: this.$t('pages.browserOptions8'), value: 5 },
        { label: this.$t('pages.browserOptions1'), value: 6 },
        { label: this.$t('pages.browserOptions4'), value: 7 },
        { label: this.$t('pages.browserOptions6'), value: 8 },
        { label: this.$t('pages.browserOptions9'), value: 9 }
      ]
    }
  },
  computed: {
    browserTypeMap() {
      const map = {}
      this.browserTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    browserTypeFormatter: function(row) {
      return this.browserTypeMap[row.browserType]
    },
    handleDownload: function(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      // 如果备份文件存在后缀，以备份文件的后缀为主
      if (row.localFilePath.lastIndexOf('\\') >= 0) {
        const backFileName = row.localFilePath.substr(row.localFilePath.lastIndexOf('\\') + 1, row.localFilePath.length)
        if (backFileName.indexOf('.') != -1) {
          const suffix = backFileName.substr(backFileName.lastIndexOf('.') + 1, backFileName.length)
          tempTask.fileName = row.fileName + '.' + suffix
        } else {
          tempTask.fileName = row.fileName
        }
      } else {
        tempTask.fileName = row.fileName
      }
      return tempTask
    }
  }
}
</script>
