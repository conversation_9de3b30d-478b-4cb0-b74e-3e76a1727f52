<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.ftpLogDetails')"
    :visible.sync="dialogFormVisible"
    width="700px"
  >
    <div class="show-detail-panel">
      <el-descriptions class="margin-top" :column="2" size="" border>
        <el-descriptions-item :label="$t('table.time')">
          {{ rowDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.terminalName')">
          <terminal-detail
            :label="rowDetail.terminalName"
            :search-id="rowDetail.terminalId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.user')">
          <user-detail
            :label="rowDetail.userName"
            :search-id="rowDetail.userId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.terminalOperateType')">
          {{ cmdTypeFormatter(rowDetail) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.processName1')">
          {{ rowDetail.processName }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.maxFileSize1')">
          {{ rowDetail.fileSize }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.serverIp1')">
          {{ rowDetail.host }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.fileName')">
          <el-button
            v-permission="'231'"
            type="text"
            style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
            :disabled="!rowDetail.fileGuid"
            @click="handleDownload(rowDetail)"
          >
            {{ rowDetail.fileName }}
          </el-button>
          <span v-permission="'!231'">{{ rowDetail.fileName }}</span>
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.action')">
          {{ actionFormatter(rowDetail, rowDetail.actionType) }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.filePath')">
          {{ rowDetail.filePath }}
        </el-descriptions-item>

      </el-descriptions>
    </div>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'231'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
  </el-dialog>
</template>
<script>

import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'FtpFileLogDetail',
  components: { AuditFileDownloader },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      selection: [],
      defaultTempTask: {
        backType: 21,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      cmdTypeOptions: [
        { label: this.$t('pages.allLinux'), value: null },
        { label: this.$t('pages.downloadFile'), value: 1 },
        { label: this.$t('pages.uploadFile1'), value: 2 }
      ]
    }
  },
  computed: {
    cmdTypeMap() {
      const map = {}
      this.cmdTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    handleDownload: function(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      tempTask.fileName = row.fileName
      return tempTask
    },
    cmdTypeFormatter: function(row, data) {
      return this.cmdTypeMap[row.cmdType]
    }
  }
}
</script>
