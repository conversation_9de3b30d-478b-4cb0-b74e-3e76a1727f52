<template>
  <div @mouseenter="isCheck=true" @mouseleave="isCheck=false" @click="checkImage">
    <!-- image组件 -->
    <el-image
      v-show="!loading"
      style="width: 100px; height: 75px;"
      :src="url"
      :preview-src-list="srcList"
      fit="scale-down"
      :title="fileData.msgText"
      @click="download"
    >
      <div slot="error" class="el-image__error" :title="$t('pages.chatImageLoadFailedMsg')">{{ $t('components.loadFailed') }}</div>
    </el-image>
    <!-- 加载图片loading -->
    <div v-show="loading" style="width: 100px; height: 75px; position: relative" class="el-loading-mask">
      <div class="el-loading-spinner">
        <svg viewBox="25 25 50 50" class="circular">
          <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
        </svg>
      </div>
    </div>
    <!-- 下载按钮 -->
    <el-button v-show="!loading && isCheck" type="text" icon="el-icon-download" class="download-btn" :title="$t('button.download')" @click="downloadFile"></el-button>
    <!-- 预览图下方文字 -->
    <div v-show="!isOnloadPreView" class="thediv" @click="download">
      <span>{{ $t('pages.clickViewPhoto') }}</span>
    </div>
    <!-- <div v-if="fileData.actionType == 1 || fileData.actionType == 0" class="theBlockdiv">
      {{ $t('table.action') }}：{{ fileData.actionType == 1 ? $t('pages.forbid') : fileData.actionType == 0 ? $t('pages.allow') : '' }}
    </div> -->
  </div>
</template>

<script>

import { downloadFile, checkLocalFileExist } from '@/api/behaviorAuditing/network/chatAllLog'
import { getDownloadTask } from '@/api/behaviorAuditing/download'
const Tiff = require('tiff.js')

// 放大图片后增加"下载"按钮调用的方法
const exploreDownload = function(fileName) {
  const imgUrl = document.getElementsByClassName(
    'el-image-viewer__canvas'
  )[0].children[0].src;
  window.URL = window.URL || window.webkitURL
  const xhr = new XMLHttpRequest()
  xhr.open('get', imgUrl, true)
  xhr.responseType = 'blob'
  xhr.onload = function() {
    if (this.status === 200) {
      // 得到一个blob对象
      const blob = this.response
      const fileUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a');
      (document.body || document.documentElement).appendChild(a)
      a.href = fileUrl
      if ('download' in a) {
        a.download = fileName
      } else {
        a.setAttribute('download', fileName)
      }
      a.target = '_self'
      a.click()
      a.remove()
    }
  }
  xhr.send()
}
export default {
  name: 'ChatImage',
  components: { },
  props: {
    fileData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data() {
    return {
      // 初始图片(预览前)
      url: require('@/assets/chatImage/default.png'),
      loading: false,
      // 预览图片
      srcList: [],
      // 进度
      percent: 0,
      // 计时
      timer: undefined,
      // div框是否鼠标悬停状态（true：是 false：否）
      isCheck: false,
      currentImg: '',
      // 预览图是否加载完成
      isOnloadPreView: false
    }
  },
  watch: {

  },
  created() {
  },
  methods: {
    // 下载图片方法（如果图片没有加载过才执行）
    download() {
      const temp = this.fileData;
      clearInterval(this.timer)
      if (this.srcList.length === 0) {
        this.loading = true;
        this.checkLocalFileExist(temp);
        this.isOnloadPreView = true
      }
    },
    // 判断本地是否已经下载图片了，若是，则直接从本地下载，若不是则从文件服务器下载
    checkLocalFileExist(temp) {
      checkLocalFileExist(temp).then(res => {
        if (res.data === true) {
          clearInterval(this.timer)
          this.downloadImage(temp);
        } else {
          clearInterval(this.timer)
          const task = this.$parent.beforeDownload(temp);
          const tasks = [task];
          this.timer = setInterval(() => {
            this.getDownloadTask(temp, tasks)
          }, 1000)
        }
      }).catch((e) => {
        this.loading = false;
        console.log(e);
      })
    },
    // 从文件服务器下载图片
    getDownloadTask(temp, tasks) {
      getDownloadTask(tasks).then(responds => {
        if (null === responds.data || 0 === responds.data.size) {
          // 如果为空，则关闭定时并return
          clearInterval(this.timer)
          return;
        }
        const data = responds.data[0];
        temp.reDownload = false
        tasks.forEach(p => { p.reDownload = false })
        const status = data.status;
        if (status === 100 || status === 5 || status === 4 || status < 2) {
          this.percent = Number(data.percent)
          return;
        }
        if (status === 6 || status === 9999) {
          this.percent = 100
          clearInterval(this.timer)
          this.downloadImage(temp);
          return;
        }
        if (status !== 100 && status !== 5 && status !== 4 && status >= 2) {
          clearInterval(this.timer)
          this.url = undefined;
          this.srcList = [];
          this.loading = false;
        }
      })
    },
    // 从本地下载图片
    downloadImage(temp) {
      downloadFile(temp).then(res => {
        const imgType = res.data.imgType
        const imgData = res.data.pictureData
        const msgText = temp.msgText.toString().toLowerCase()
        if (temp.msgText &&
          imgType === 'image/png' &&
          (msgText.endsWith('.tif') || msgText.endsWith('.tiff'))) {
          // TIFF特殊处理，将base64转成file文件后再通过Tiff.toDataURL转成png格式的base64
          this.tiffImage(imgData);
        } else {
          const previewImg = `data:${imgType};base64,${imgData}`
          this.url = previewImg;
          this.srcList.push(previewImg);
          this.loading = false;
        }
      }).catch(() => {
        // 如果加载失败，则显示加载失败的图片
        this.url = undefined;
        this.srcList = [];
        this.loading = false;
      })
    },
    // 调用父组件的下载文件方法
    downloadFile() {
      this.$parent.handleLoadDown(this.fileData);
    },
    // 查看大图时底下按钮增加“下载”按钮
    checkImage() {
      const fileName = this.fileData.msgText;
      const a = document.querySelector('.el-image-viewer__actions__inner');
      if (a) {
        var htmlElement = document.createElement('i');
        htmlElement.className = 'el-icon-download';
        htmlElement.onclick = function() {
          // 调用下载图片方法
          exploreDownload(fileName);
          // htmlElement.remove();
        }
        a.appendChild(htmlElement);
      }
    },
    // TIFF特殊处理，将base64转成file文件后再通过Tiff.toDataURL转成png格式的base64
    tiffImage(imgData) {
      const fileContent = 'data:image/png;base64,' + imgData;
      const file = this.dataURLtoBlob(fileContent);
      file.arrayBuffer().then((arrayBuffer) => {
        const tiff = new Tiff({
          buffer: arrayBuffer
        });
        // 转成png格式的base64图片
        const value = tiff.toDataURL('image/png');
        this.url = value;
        this.srcList.push(value);
        this.loading = false;
      });
    },
    // 将base64转成file文件
    dataURLtoBlob(dataurl) {
      const arr = dataurl.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bStr = atob(arr[1]);
      let n = bStr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bStr.charCodeAt(n);
      }
      return new Blob([u8arr], {
        type: mime
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.thediv {
  position: relative;
  margin-top: -4px;
  cursor: pointer;
}
.thediv span {
  width: 100px;
  display: block;
  line-height: 22px;
  background-color: rgba(255,255,255,0.7);
  position: absolute;
  bottom: 0;
  color: red;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word
}
.theBlockdiv {
  position: relative;
  cursor: pointer;
  width: 250px;
  border-radius: 5px; /* 设置边框的圆角大小 */
  display: block;
  line-height: 22px;
  // background-color: rgba(255,255,255,0.7);
  // position: absolute;
  left: 300px;
  bottom: 50px;
  // font-size: 14px;
  font-weight: 500;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
}
.download-btn {
  padding: 0;
  line-height: 16px;
  font-size: 16px;
}
</style>
