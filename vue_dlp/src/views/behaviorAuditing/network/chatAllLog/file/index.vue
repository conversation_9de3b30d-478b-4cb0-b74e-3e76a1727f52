<template>
  <div>
    <div :class="fileData.fileGuid ? `rounded-rectangle rounded-rectangle` : `rounded-rectangle rounded-rectangle rounded-rectangle-empty`">
      <!--文件左上方 根据文件类型选择哪个缩略图-->
      <svg-icon class="svg-icon" :icon-class="suffixName"/>
      <!--文件左上方 缩略图右侧跟着文件名-->
      <div :title="fileName" class="img-file-name">{{ fileName }}</div>
      <!--文件左上方 缩略图右侧文件名下方显示文件大小-->
      <div class="img-file-size">{{ calcFileSize(fileData.fileSize) }}</div>
    </div>
    <!--文件下方下载按钮 -->
    <div v-show="fileData.fileGuid" class="thediv" @click="download">
      <span class="thediv-span">{{ $t('table.download') }}</span>
    </div>
    <!-- <div v-if="fileData.actionType == 1 || fileData.actionType == 0" class="theBlockdiv">
      {{ $t('table.action') }}：{{ fileData.actionType == 1 ? $t('pages.forbid') : fileData.actionType == 0 ? $t('pages.allow') : '' }}
    </div> -->
  </div>
</template>

<script>
import SvgIcon from '@/components/SvgIcon/index.vue'
import { convert } from '@/utils';
import { getFileIcon } from '@/icons/extension'

export default {
  name: 'ChatFile',
  components: { SvgIcon },
  props: {
    fileData: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data() {
    return {
    }
  },
  computed: {
    fileName() {
      return this.$parent.getFileName(this.fileData);
    },
    suffixName() {
      return getFileIcon(this.subSuffixName(this.fileName))
    },
    theme() {
      return this.$store.getters.theme || this.$store.getters.sysResources.theme
    }
  },
  watch: {

  },
  created() {

  },
  methods: {
    // 直接调用下载方法
    download() {
      this.$parent.handleLoadDown(this.fileData);
    },
    // 截取只保留后缀名（不包含.）
    subSuffixName(filename) {
      if (!filename) {
        return ''
      }
      const index = filename.lastIndexOf('.')
      if (index < 0) {
        return ''
      }
      return filename.substring(index + 1, filename.length).toLowerCase();
    },
    // 计算文件大小(自动转换字节单位)
    calcFileSize(value) {
      if (!value) {
        return '0KB'
      }
      return convert(value);
    }
  }
}
</script>

<style lang="scss" scoped>
.rounded-rectangle {
  width: 250px; /* 设置矩形的宽度 */
  height: 80px; /* 设置矩形的高度 */
  border-radius: 8px; /* 设置边框的圆角大小 */
  margin-top: 2px;
  margin-bottom: 2px;
}
.rounded-rectangle-empty {
  height: 58px; /* 设置矩形的高度 */
}

.thediv {
  position: relative;
  margin-top: -4px;
  cursor: pointer;
}
.thediv-span {
  width: 250px;
  border-radius: 5px; /* 设置边框的圆角大小 */
  display: block;
  line-height: 22px;
  position: absolute;
  bottom: -1px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
}
.img-icon {
  width: 35px;
  height: 50px;
  margin-left: 9px;
  transform: scale(2);
}
.svg-icon {
  width: 35px;
  height: 50px;
  margin-left: 9px
}
.img-file-name {
  margin-left: 50px;
  margin-top: -45px;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.img-file-size {
  color: #919193;
  margin-left: 50px;
  margin-top: 6px;
  font-size: 12px
}
.theBlockdiv {
  position: relative;
  cursor: pointer;
  width: 250px;
  border-radius: 5px; /* 设置边框的圆角大小 */
  display: block;
  line-height: 22px;
  // background-color: rgba(255,255,255,0.7);
  // position: absolute;
  left: 300px;
  bottom: 50px;
  // font-size: 14px;
  font-weight: 500;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
}
</style>
