<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @handleFilter="handleFilter"
      >
        <span slot="time">
          <span>{{ $t('pages.timeQuery') }}：</span>
          <el-date-picker
            v-model="dateRange"
            style="width: 400px"
            :clearable="false"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="defaultTime"
            :picker-options="pickerOptions"
            :placeholder="$t('pages.startDate')"
            @blur="blur"
          />
        </span>
        <SearchItem model-key="query.chatType" :value="query.chatType">
          <span>{{ $t('pages.chatAllLog_Msg') }}：</span>
          <el-select v-model="query.chatType" style="width: 130px">
            <el-option v-for="item in chatToolOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.msgType" :value="query.msgType">
          <span>{{ $t('pages.chatAllLog_Msg1') }}：</span>
          <el-select v-model="query.msgType" style="width: 100px">
            <el-option v-for="item in msgTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.keyword" :value="query.keyword">
          <span>{{ $t('pages.chatAllLog_Msg2') }}：</span>
          <el-input v-model="query.keyword" v-trim clearable style="width: 180px;"/>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'343'" :request="handleExport"/>

        <!--<el-dropdown style="padding-left: 10px;" @command="handleMoreClick">
          <el-button size="mini">
            {{ $t('pages.chatAllLog_Msg3') }}<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1">{{ $t('pages.chatAllLog_Msg4') }}</el-dropdown-item>
            <el-dropdown-item :command="2">{{ $t('pages.chatAllLog_Msg5') }}</el-dropdown-item>
            <el-dropdown-item :command="3">{{ $t('pages.chatAllLog_Msg6') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>-->
      </SearchToolbar>

      <div class="tableBox">
        <div v-loading="sessionLoading" class="tree-container">
          <tree-menu
            ref="sessionTree"
            :data="treeData2"
            resizeable
            :icon-option="iconOption"
            :is-filter="false"
            lazy
            expand-on-click-node
            :load="loadChildren"
            @node-click="treeNodeClick"
          />
        </div>
        <div v-loading="msgLoading" class="table-container">
          <div class="msg-box">
            <div v-for="(msg, index) in msgList" :key="index">
              <!-- 图片 -->
              <span v-if="msg.msgType == 1">
                <!-- {{ msg.senderName + ' ' + msg.createTime }} <i v-show="msg.actionType == 1" class="el-icon-circle-close" style="color:red" :title="$t('table.action') + ':' +$t('pages.forbid') "></i><br/> -->
                {{ msg.senderName + ' ' + msg.createTime }}
                <span v-show="msg.actionType == 1" class="round" :title="$t('table.action') + ':' +$t('pages.forbid') ">{{ $t('pages.forbid') }}</span>
                <el-button v-if="!disabledLogViewerBtn(msg)" type="text" style="padding: 0" @click="() => playVideo(msg)">
                  {{ $t('pages.viewScreenRecord') }}
                </el-button>
                <br/>
                <chat-image v-if="msg.devId && msg.fileGuid && hasPermission('221')" :file-data="msg" />
                <chat-file v-else :file-data="msg" />
              </span>
              <!-- 文件 -->
              <span v-if="msg.msgType == 2">
                {{ msg.senderName + ' ' + msg.createTime }}
                <span v-show="msg.actionType == 1" class="round" :title="$t('table.action') + ':' +$t('pages.forbid') ">{{ $t('pages.forbid') }}</span>
                <el-button v-if="!disabledLogViewerBtn(msg)" type="text" style="padding: 0" @click="() => playVideo(msg)">
                  {{ $t('pages.viewScreenRecord') }}
                </el-button>
                <br/>
                <chat-image v-if="msg.devId && msg.fileGuid && hasPermission('221') && isImage(msg.msgText)" :file-data="msg" />
                <chat-file v-else :file-data="msg" />
              </span>
              <!-- 文字 -->
              <span v-if="msg.msgType == 0">
                <div v-if="msg.msgText" class="text-box">
                  <div v-for="(item, i) in html2Escape(msg.msgText.trim()).split(/\n/g)" :key="i">
                    {{ item }}
                    <el-button v-if="i === 0 && !disabledLogViewerBtn(msg)" type="text" style="padding: 0" @click="() => playVideo(msg)">
                      {{ $t('pages.viewScreenRecord') }}
                    </el-button>
                  </div>
                </div>
              </span>
            </div>
          </div>
          <pagination
            :total="total?total:0"
            :page="page"
            :limit="limit"
            :page-sizes="pageSizes"
            :layout="layout"
            style="width: 100%; flex-shrink: 0;"
            @update:limit="updateLimit"
            @update:page="updatePage"
            @pagination="reloadMsg"
          >
          </pagination>
        </div>
      </div>
    </div>
    <audit-file-downloader ref="auditFileDownloader" :show="false" :before-download="beforeDownload"/>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getMsgList, exportChatAllLog, /* getRootNode, */getChildren } from '@/api/behaviorAuditing/network/chatAllLog'
import moment from 'moment'
import Pagination from '@/components/Pagination'
import ChatImage from './image'
import ChatFile from './file'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { asyncGetLogVideoInfo, handleViewLogVideo, disabledLogViewerBtn } from '@/utils/logVideo'
import { entityLink } from '@/utils';
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'ChatAllLog',
  components: { Pagination, ChatImage, ChatFile },
  mixins: [auditLogRouterMixin],
  data() {
    return {
      choiceDate: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.choiceDate = moment(minDate).format('YYYY-MM-DD') + ' 00:00:00'
          if (maxDate) {
            this.choiceDate = ''
          }
        },
        disabledDate: (date) => {
          if (this.choiceDate) {
            const choiceDate = new Date(this.choiceDate)
            const minDate = new Date(this.choiceDate).setDate(choiceDate.getDate() - 30) // 选中的时间前N天
            const maxDate = new Date(this.choiceDate).setDate(choiceDate.getDate() + 30) // 选中的时间后N天
            return date < minDate || date > maxDate || date > new Date()
          } else {
            return new Date(moment(new Date(date)).format('YYYY-MM-DD') + ' 00:00:00') > new Date()
          }
        }
      },
      dateRange: [],
      defaultTime: ['00:00:00', '23:59:59'],
      query: { // 查询条件
        objectType: undefined,
        objectId: undefined,
        startDate: '',
        endDate: '',
        keyword: '',
        chatType: null,
        msgType: null,
        chatSessionInfo: '',
        searchReport: 1,
        dlpTotal: 0
      },
      treeChooseNode: undefined,  // 当前选中的聊天记录节点数据
      chatToolOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.qq'), value: 3 },
        { label: this.$t('pages.wechat'), value: 23 },
        { label: this.$t('pages.dingTalk'), value: 25 },
        { label: this.$t('pages.enterpriseQQ'), value: 26 },
        { label: this.$t('pages.enterpriseWeChat'), value: 27 },
        { label: this.$t('pages.aliTalk'), value: 13 },
        { label: this.$t('pages.feiQ'), value: 20 },
        { label: this.$t('pages.feiShu'), value: 29 },
        { label: 'Skype', value: 10 },
        { label: this.$t('pages.tim'), value: 31 },
        { label: this.$t('pages.shiyeLine'), value: 32 }
      ],
      msgTypeOption: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.writtenWords'), value: 0 },
        { label: this.$t('pages.picture'), value: 1 },
        { label: this.$t('pages.file1'), value: 2 }
      ],
      treeData: [], // ?
      treeData2: [], // ?
      treePageSize: 10,  // 树分页每页记录数
      treeMaxPage: 3, // 树分页最多时可留存页数
      msgLoading: false,
      sessionLoading: false,
      msgList: [],
      showTree: true,
      deleteable: false,
      iconOption: {
        typeKey: 'nodeType',
        1: 'im',
        2: 'terminal',
        3: 'user',
        4: 'message',
        'pre': 'arrowUp',
        'next': 'arrowDown'
      },
      preNode: { id: 'pre', nodeType: 'pre', label: this.$t('pages.previousPage'), isLeaf: true },
      nextNode: { id: 'next', nodeType: 'next', label: this.$t('pages.nextPage'), isLeaf: true },
      total: 0,
      page: 1,
      limit: 100,
      pageSizes: [100, 200, 500, 1000],
      layout: 'slot, total, sizes, prev, pager, next, jumper',
      tempTask: {},
      defaultTempTask: {
        backType: 11, // 11：聊天文件 10：聊天图片
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      }
    }
  },
  computed: {
    queryData() {
      const { chatType, msgType, keyword } = this.query
      return { chatType, msgType, keyword }
    }
  },
  watch: {
    dateRange: {
      deep: true,
      handler(val) {
        if (val) {
          // dateRange有值，更新query参数
          this.query.startDate = val[0]
          this.query.endDate = val[1]
        } else {
          // dateRange没有值，设置默认值
          this.setDefaultDateRange()
        }
      }
    },
    queryData: {
      deep: true,
      handler(val) {
        this.treeChooseNode = undefined
      }
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    this.setDefaultDateRange()
  },
  methods: {
    disabledLogViewerBtn,
    navigate(vm, queryParam) {
      if (queryParam.createDate) {
        this.query = Object.assign(this.query, queryParam)
        const { objectType, objectId } = queryParam
        const startDate = queryParam.createDate + ' 0:00:00'
        const endDate = queryParam.createDate + ' 23:59:59'
        this.dateRange = [startDate, endDate]
        this.query.startDate = startDate
        this.query.endDate = endDate
        this.$nextTick(() => {
          entityLink({ entityType: objectType, entityId: objectId }, {}, this)
        })
      }
    },
    // 设置默认日期范围，当天 0:00:00 - 23:59:59
    setDefaultDateRange() {
      const date = moment(new Date()).format('YYYY-MM-DD')
      const startDate = date + ' 0:00:00'
      const endDate = date + ' 23:59:59'
      this.dateRange = [startDate, endDate]
      this.query.startDate = startDate
      this.query.endDate = endDate
    },
    blur() {
      // 更新默认选中的起止时间
      const start = this.query.startDate.split(' ')[1]
      const end = this.query.endDate.split(' ')[1]
      this.defaultTime.splice(0, 2, start, end)
    },
    // 重置聊天记录显示内容
    resetMsgBox() {
      this.msgLoading = false
      this.msgList.splice(0)
      this.total = 0
      this.page = 1
    },
    updateLimit(val) {
      this.limit = val
    },
    updatePage(val) {
      this.page = val
    },
    // 聊天记录文字转义、格式化
    formatStr(info) {
      let str = info.msgText
      if (str != null && str != undefined) {
        str = str.trim()
        str = this.html2Escape(str)
        str = str.replace(/\n/g, '<br/>')
      }
      return str
    },
    // 节点数据、查询数据、其他数据 合并成新的查询参数
    formatParam(data, query, option) {
      const { nodeType = '', chatType = '', termId = '', localUserAccount = '', chatSessionAccount = '', localUserName } = data
      const { objectType, objectId, startDate, endDate, keyword, msgType, searchReport } = query
      const param = {
        nodeType, chatType, termId, localUserAccount, chatSessionAccount, localUserName,
        objectType, objectId, startDate, endDate, keyword, msgType, searchReport,
        ...option
      }
      this.checkBackUpLogList(param)
      return param
    },
    buildParam(type, data, node) {
      const parentNode = node.parent
      const pageInfo = parentNode.pageInfo
      const { firstPage, lastPage, pageSize } = pageInfo
      const page = type == 'pre' ? firstPage - 1 : type == 'next' ? lastPage + 1 : 1
      return this.formatParam(parentNode.data, this.query, { page, limit: pageSize })
    },
    doTreePage(data, node, el) {
      var param = {}
      if (data.nodeType == 'pre') {
        // 向上加载数据
        param = this.buildParam('pre', data, node)
      } else if (data.nodeType == 'next') {
        // 向下加载数据
        param = this.buildParam('next', data, node)
      }
      this.sessionLoading = true
      getChildren(param).then(res => {
        this.sessionLoading = false
        this.updateAndRenderTree(data.nodeType, res.data, node.parent.pageInfo);
      }).catch(res => {
        console.log(res)
        this.sessionLoading = false
      })
    },
    doLoadMsg(data, node, el) {
      if (!data) return
      this.query.chatSessionInfo = data.label
      this.query.dlpTotal = 0
      this.loadMsgList(data)
    },
    reloadMsg() {
      const currentData = this.$refs.sessionTree.getCurrentNode()
      this.query.chatSessionInfo = currentData.label
      this.loadMsgList(currentData)
    },
    // 点击聊天记录树节点
    treeNodeClick(data, node, el) {
      this.resetMsgBox()
      if (data.nodeType == 'pre' || data.nodeType == 'next') {
        this.doTreePage(data, node, el);
        return
      } else if (data.nodeType == 4) {
        // 会话节点，加载具体内容
        this.doLoadMsg(data, node, el);
      }
      this.treeChooseNode = node
    },
    updateAndRenderTree(event, data, pageInfo) {
      var list = data.items
      if (event == 'pre') {
        // 上一页逻辑
        for (let i = list.length - 1; i >= 0; i--) {
          pageInfo.pageData.unshift(list[i]);
        }
        pageInfo.firstPage--;
        if (pageInfo.firstPage <= 1) {
          pageInfo.preBtnShow = false;
        }
        if (pageInfo.lastPage - pageInfo.firstPage >= 3) {
          // 要去掉最后一页
          // 这里不能直接去掉，可能最后一页并没有10个，而是使用保留3 * pageSize的方式 （弹出的个数为 pageData.length - 3 * pageSize）
          var length = pageInfo.pageData.length - 3 * pageInfo.pageSize;
          for (let i = 0; i < length; i++) {
            pageInfo.pageData.pop();
          }
          pageInfo.lastPage = pageInfo.lastPage - 1;
          pageInfo.nextBtnShow = true;
        }
      } else if (event == 'next') {
        // 下一页
        list.forEach(item => {
          pageInfo.pageData.push(item);
        })
        pageInfo.lastPage++;
        pageInfo.nextBtnShow = data.hasMore;
        if (pageInfo.lastPage - pageInfo.firstPage >= 3) {
          // 去掉第一页
          for (let i = 0; i < pageInfo.pageSize; i++) {
            pageInfo.pageData.shift();
          }
          pageInfo.firstPage = pageInfo.firstPage + 1;
          pageInfo.preBtnShow = true;
        }
      }
      list = pageInfo.pageData;
      list = list.map(item => {
        item.isLeaf = item.leaf
        return item
      })
      // 这里判断是否有下一页
      if (pageInfo.preBtnShow) {
        list.unshift(this.preNode)
      }
      if (pageInfo.nextBtnShow) {
        list.push(this.nextNode)
      }

      pageInfo.resolveFunc(list)
    },
    handleFilter() {
      this.resetMsgBox()
      this.loadRootNode()
    },
    loadRootNode() {
      // const temp = {
      //   objectType: this.query.objectType,
      //   objectId: this.query.objectId,
      //   startDate: this.query.startDate,
      //   endDate: this.query.endDate,
      //   keyword: this.query.keyword,
      //   chatType: this.query.chatType,
      //   msgType: this.query.msgType
      //   searchReport: this.query.searchReport
      // }
      // this.sessionLoading = true
      this.treeChooseNode = undefined
      this.treeData2 = []
      this.chatToolOptions.forEach((t, index) => {
        if (t.value === null || t.value === undefined || this.query.chatType && t.value != this.query.chatType) {
          return
        }
        this.treeData2.push({
          id: index + 1,
          chatType: t.value,
          chatTypeId: t.value,
          label: t.label,
          leaf: false,
          nodeType: 1
        })
      })
      // 按领导的说法没必要从审计日志表统计有多少聊天工具，直接显示支持的聊天工具即可
      // getRootNode(temp).then(res => {
      //   this.msgList = []
      //   this.total = 0
      //   this.sessionLoading = false
      //   this.treeData2 = res.data;
      // }).catch(res => {
      //   this.sessionLoading = false;
      // })
    },
    checkBackUpLogList(temp) {
      let months = ''
      const queryTemp = JSON.parse(JSON.stringify(temp))
      queryTemp.startDate = temp.startDate.substr(0, temp.startDate.lastIndexOf(' '))
      queryTemp.endDate = temp.endDate.substr(0, temp.endDate.lastIndexOf(' '))
      if (this.query.searchReport != 1) {
        backupLogList(queryTemp).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
    },
    loadChildren(node, resolve) {
      const param = this.formatParam(node.data, this.query)
      this.sessionLoading = true
      this.resetMsgBox()

      getChildren(param).then(res => {
        this.sessionLoading = false
        var list = res.data.items

        var hasMore = res.data.hasMore;
        var pageInfo = {
          resolveFunc: resolve,
          firstPage: 1,
          lastPage: 1,
          preBtnShow: false,
          nextBtnShow: hasMore,
          pageData: list,
          pageSize: list.length
        }
        node.pageInfo = pageInfo;
        list = list.map(item => {
          item.isLeaf = item.leaf
          return item
        })
        // 这里判断是否有下一页
        if (pageInfo.preBtnShow) {
          list.unshift(this.preNode)
        }
        if (pageInfo.nextBtnShow) {
          list.push(this.nextNode)
        }
        resolve(list);
      }).catch(res => {
        this.sessionLoading = false
      })
      resolve([]);
    },
    // 加载聊天信息
    loadMsgList(data) {
      const { msgType, startDate, endDate, keyword, searchReport, dlpTotal } = this.query
      const { termId, chatType, localUserAccount } = data
      const temp = {
        page: this.page, limit: this.limit, objectType: 1, objectId: termId,
        msgType, startDate, endDate, keyword, chatType, localUserAccount, searchReport, dlpTotal
      }
      // 如果聊天会话账号不为空就查询聊天会话账号，如果聊天会话账号为空，就查询会话信息
      if (data.chatSessionAccount) {
        temp.chatSessionAccount = data.chatSessionAccount
      }
      if (data.label) {
        temp.chatSessionInfo = data.label
      }
      if (data.localUserAccount) {
        temp.localUserAccount = data.localUserAccount
      } else {
        temp.localUserName = data.localUserName
      }
      this.msgLoading = true
      getMsgList(temp).then(res => {
        if (res.data) {
          this.msgList = res.data.items
          asyncGetLogVideoInfo(this.msgList)
          this.total = res.data.total
          this.query.dlpTotal = res.data.dlpTotal
        } else {
          this.msgList = []
          this.total = 0
        }
        this.msgLoading = false
      }).catch(e => {
        this.msgLoading = false
      })
    },
    strategyTargetNodeChange(tabName, checkedNode) {
      // 导出聊天记录时，防止先前选中的聊天软件参数的信息还保留着的问题
      this.treeChooseNode = null
      if (checkedNode) {
        this.deleteable = true
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
        // 选中终端或者租，加载这个终端或组下的所有会话记录
        this.loadRootNode();
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.treeData = []
        this.msgList = []
        this.deleteable = true
      }
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = this.getFileName(row)
      if (row.msgType == 1) {
        this.tempTask.backType = 10
      } else {
        this.tempTask.backType = 11
      }
      return this.tempTask
    },
    getFileName(row) {
      if (!row.msgType) {
        return ''
      }
      // 是否是windows终端，无则默认是windows终端
      const isWindowsTerminal = !row.terminalType || row.terminalType % 16 === 0;
      if (isWindowsTerminal) {
        let idx = row.localFilePath.indexOf('FileBackup')
        // C:\\$Eis$Bak\\DLPBackUp\\2023-11-01\\71b5dea3-120c-47b7-935b-9f8e35b8f296.zip
        if (idx < 1) {
          idx = row.localFilePath.lastIndexOf('.')
          if (idx < 0) {
            return row.msgText
          }
          return row.msgText + row.localFilePath.slice(idx)
        }

        const sep = row.localFilePath.charAt(idx - 1)
        const lastSep = row.localFilePath.lastIndexOf(sep)
        // C:\\DLP\\Application\\Repository\\2023-11-02\\FileBackup\\d82d1b72-2782-423e-b22a-9cdd08150d98.zip
        if (idx + 10 === lastSep) {
          idx = row.localFilePath.lastIndexOf('.')
          if (idx < 0) {
            return row.msgText
          }
          return row.msgText + row.localFilePath.slice(idx)
        }

        const ext = row.localFilePath.slice(idx + 11, lastSep)
        // C:\\$Eis$Bak\\DLPBackUp\\2023-10-24\\FileBackup\\default\\5f428d3515f7cbc920db9b78be3ffd28 （无后缀文件）
        if (ext === 'default') {
          return row.msgText
        }
        // C:\\DLP\\Application\\Repository\\2023-10-08\\FileBackup\\txt\\23751439c6ca801d155990f757fb5e45
        const lastIdx = row.msgText.lastIndexOf('.')
        if (lastIdx < 0) {
          return row.msgText + '.' + ext
        }
        const suffix = row.msgText.slice(lastIdx + 1)
        if (ext === suffix) {
          return row.msgText
        }
        return row.msgText + '.' + ext
      } else {
        // 非windows类型终端的，直接将msgText当作下载的文件名
        return row.msgText;
      }
    },
    handleLoadDown(row) {
      if (this.hasPermission('221')) {
        this.$refs['auditFileDownloader'] && this.$refs['auditFileDownloader'].handleDownload(row)
      }
    },
    handleMoreClick(type) {
      // 日志审计
      const nameMap1 = { 1: 'ChatTextLogP', 2: 'ChatImageLogP', 3: 'ChatFileLogP' }
      // 网路行为
      const nameMap2 = { 1: 'ChatTextLog', 2: 'ChatImageLog', 3: 'ChatFileLog' }
      const name = this.$route.meta.isLog ? nameMap2[type] : nameMap1[type]
      this.$router.push({ name: name })
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    formatExportParam(nodePath) {
      let chatType, termId, chatSessionAccount, chatSessionInfo, localUserAccount, localUserName
      // 不能直接选中获取数据是因为，企业微信节点的情况下，获取的节点不全 nodeType == 4
      if (nodePath && nodePath.length > 0) {
        for (let i = 0; i < nodePath.length; i++) {
          const data = nodePath[i]
          if (data.nodeType === 1) {
            chatType = data.chatType
          } else if (data.nodeType === 2) {
            termId = data.termId
          } else if (data.nodeType === 3) {
            localUserAccount = data.localUserAccount
            if (!localUserAccount) {
              localUserName = data.localUserName
            }
          } else if (data.nodeType === 4) {
            chatSessionAccount = data.chatSessionAccount
            if (!chatSessionAccount) {
              chatSessionInfo = data.label
            }
          }
        }
      }
      return { chatType, termId, chatSessionAccount, chatSessionInfo, localUserAccount, localUserName }
    },
    exportFunc(formData) {
      if (this.treeChooseNode) {
        const nodePath = []
        let node = this.treeChooseNode
        if (node) {
          nodePath.push(node.data)
          while (node.parent) {
            node = node.parent
            if (node.level > 0) nodePath.push(node.data)
          }
        }
        Object.assign(formData, this.formatExportParam(nodePath))
      } else {
        Object.assign(formData, { termId: null, chatSessionAccount: null, chatSessionInfo: null, localUserAccount: null, localUserName: null })
      }
      return exportChatAllLog(formData)
    },
    isImage(msgText) {
      if (!msgText) {
        return false;
      }
      const lowerCase = msgText.toString().toLowerCase();
      if (
        lowerCase.endsWith('.png') ||
        lowerCase.endsWith('.jpg') ||
        lowerCase.endsWith('.jpeg') ||
        lowerCase.endsWith('.bmp') ||
        lowerCase.endsWith('.icon') ||
        lowerCase.endsWith('.gif') ||
        lowerCase.endsWith('.tif') ||
        lowerCase.endsWith('.tiff')) {
        return true;
      }
    },
    playVideo(row) {
      handleViewLogVideo(row, this)
    }
  }
}
</script>

<style lang="scss" scoped>
.download-span {
  color: #2b7aac;
  cursor: pointer;
}
.msg-box {
  height: 0;
  padding: 10px 5px;
  overflow-y: auto;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  &>div {
    margin-bottom: 5px;
    padding: 8px;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
.text-box {
  word-break: break-all;
}
.round {
    padding:3px; width:300px; height:50px;
    background:red;
    -moz-border-radius: 15px;
    -webkit-border-radius: 15px;
    border-radius:15px;
    font-size: 10px;
    text-align: center;
    cursor: pointer;
    margin-left: 5px;
    color: #fff;
}
</style>
