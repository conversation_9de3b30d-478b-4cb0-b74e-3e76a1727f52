<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.webTitle" :value="query.webTitle">
          <span>{{ $t('pages.pageTitle') }}：</span>
          <el-input v-model="query.webTitle" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <SearchItem model-key="query.host" :value="query.host">
          <span>{{ $t('pages.website') }}：</span>
          <el-input v-model="query.host" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <SearchItem model-key="query.hostGroupId" :value="query.hostGroupId">
          <span>{{ $t('table.hostSourceGroup') }}：</span>
          <el-select v-model="query.hostGroupId" clearable>
            <el-option v-for="(item, index) in urlGroupData" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <el-button slot="append" icon="el-icon-plus" size="mini" :disabled="selection.length === 0" @click="handleBatchAddUrl">
          {{ $t('button.addToUrlLibrary') }}
        </el-button>
        <audit-log-exporter slot="append" v-permission="'336'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'447'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="true" :row-data-api="rowDataApi" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.urlLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.timeStr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.hostSourceGroup')">
            {{ rowDetail.hostGroupName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.website')">
            {{ rowDetail.host }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.webTitle')">
            {{ rowDetail.webTitle }}
          </el-descriptions-item>
          <el-descriptions-item span="2" label="URL">
            <a :href="rowDetail.url + rowDetail.urlEx1" style="color: #68a8d0;" target="_blank">{{ rowDetail.url + rowDetail.urlEx1 }}</a>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.addToUrlLibrary')"
      :visible.sync="dialogFormVisible2"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="urlForm" label-position="right" style="width: 750px;">
        <div style="margin-bottom: 10px; margin-left: 11px">
          <el-checkbox v-model="batchSetSourceGroup" :false-label="0" :true-label="1">{{ $t('pages.batchSetSourceGroup') }}</el-checkbox>
          <el-select v-model="groupId" :disabled="batchSetSourceGroup === 0" style="width: 170px;" clearable @change="handleBatchSetSourceGroup">
            <el-option v-for="(item, index) in urlGroupData" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-button :disabled="batchSetSourceGroup === 0" style="margin-bottom: 0;" @click="handleTypeCreate">
            <svg-icon icon-class="add" />
          </el-button>
        </div>
        <grid-table
          ref="urlList"
          :multi-select="true"
          :height="300"
          :col-model="colModel2"
          :row-datas="rowDatas"
          :show-pager="false"
          @selectionChangeEnd="selectionChangeEnd2"
        />
        <div style="color: rgb(43, 122, 172); margin-top: 5px;">{{ $t('pages.autoLogin_text3') + $t('pages.autoDuplicateUrl') }}</div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :menu-code="'A53'" :link-url="'/system/baseData/urlLibrary'" :btn-text="$t('pages.maintainWeb')"/>
        <el-button :loading="submitting" type="primary" @click="saveToUrlLibrary">
          {{ $t('button.addToUrlLibrary') }}
        </el-button>
        <el-button @click="dialogFormVisible2 = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 添加分组 -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :edit-valid-func="getUrlGroupByName"
      :add-func="createUrlGroup"
      @addEnd="createGroupAddEnd"
    />
  </div>
</template>

<script>
import { deleteLog, exportWebBrowseLog, getLogPage } from '@/api/behaviorAuditing/network/url'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { createBatchUrl, getTreeNode, getUrlByAdress, getUrlGroupByName, createUrlGroup } from '@/api/system/baseData/urlLibrary'
import EditGroupDlg from '@/views/common/editGroupDlg.vue'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'UrlLog',
  components: { EditGroupDlg },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '150', sort: true, formatter: row => logSourceFormatter(row, row.timeStr) },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        // { prop: 'groupId', label: '所属分组', width: '100' },
        { prop: 'host', label: 'website', width: '150' },
        { prop: 'hostGroupName', label: 'hostSourceGroup', width: '150' },
        { prop: 'webTitle', label: 'webTitle', width: '200' },
        { label: 'URL', width: '200', formatter: this.urlLink },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('284'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      colModel2: [
        { prop: 'name', label: 'websiteName', width: '150', type: 'input', maxlength: 60 },
        { prop: 'address', label: this.$t('pages.websiteUrl'), width: '160' },
        { prop: 'groupId', label: 'sourceGroup', width: '180', type: 'select', options: [], alwaysEdit: true, attributes: { clearable: true }},
        { prop: 'remark', label: 'remark', width: '150', type: 'input', maxlength: 100 }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        webTitle: '',
        host: '',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList',
        hostGroupId: undefined
      },
      showTree: true,
      selection: [],
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined,
      dialogFormVisible2: false,
      rowDatas: [],
      submitting: false,
      urlGroupData: [],
      groupId: '',
      batchSetSourceGroup: 0
    }
  },
  watch: {
    urlGroupData(val) {
      this.colModel2[2].options = val
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
    this.loadUrlGroupData()
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
    this.loadUrlGroupData()
    this.gridTable().execRowDataApi()
  },
  methods: {
    getUrlGroupByName,
    createUrlGroup,
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    urlTable() {
      return this.$refs['urlList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas || []
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    async handleBatchAddUrl() {
      this.groupId = ''
      this.batchSetSourceGroup = 0
      const toAddData = JSON.parse(JSON.stringify(this.gridTable().getSelectedDatas()))
      let filterData = [];
      // 从勾选数据中过滤重复的网站url
      toAddData.forEach(el => {
        if (!filterData.some(e => e.host == el.host)) {
          filterData.push(el);
        }
      })
      filterData = filterData.filter((el) => {
        if (el == undefined || el.host == '') {
          return false
        }
        return true
      }).map((el) => {
        let webTitle = el.webTitle
        if (webTitle.length > 60) {
          // 网页标题长度要小于等60，否则报错
          webTitle = webTitle.slice(0, 60)
        }
        // 按时间段查询的记录，仅仅以id作为唯一标识可能导致相同，所以以el.id + el.createTime作为唯一标识
        return { id: el.id + el.createTime, name: webTitle, address: el.host, groupId: '', remark: '' }
      })
      // 过滤网址信息库已存在的网站url
      const rowDatas = []
      for (let i = 0; i < filterData.length; i++) {
        const el = filterData[i]
        const { data } = await getUrlByAdress({ url: el.address })
        if (!data) {
          rowDatas.push(el)
        }
      }
      this.rowDatas = rowDatas
      this.dialogFormVisible2 = true
      this.$nextTick(() => {
        this.urlTable() && this.urlTable().toggleAllSelection()
      })
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportWebBrowseLog(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    urlLink(cellvalue, options, rowObject) {
      return "<a style='color: #68A8D0' href = '" + cellvalue.url + cellvalue.urlEx1 + "' target='_blank'>" + '' + cellvalue.url + cellvalue.urlEx1 + '' + '</a>';
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '284', this.query.searchReport, undefined, 'timeStr')
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    async loadUrlGroupData() {
      const { data } = await getTreeNode()
      this.urlGroupData = []
      const groupData = JSON.parse(JSON.stringify(data))
      groupData.forEach((item) => {
        const group = { value: item.dataId, label: item.label }
        this.urlGroupData.push(group)
      })
    },
    selectionChangeEnd2(rowDatas) {

    },
    handleBatchSetSourceGroup(groupId) {
      const toUpdateGroupDatas = this.urlTable().getDatas()
      const updatedGroupDatas = toUpdateGroupDatas.forEach((item) => { item.groupId = groupId })
      this.urlTable().updateRowData(updatedGroupDatas)
    },
    saveToUrlLibrary() {
      const urls = JSON.parse(JSON.stringify(this.urlTable().getSelectedDatas()))
      if (!urls || urls.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.checkToAddUrl'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.submitting = true
      // 添加url时,去除id
      urls.forEach((el) => { el.id = '' })
      createBatchUrl(urls).then(respond => {
        this.submitting = false
        this.dialogFormVisible2 = false
        this.gridTable().execRowDataApi()
        if (respond.code == 20000) {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.addBatchUrlSuccess', { 'count': respond.data }),
            type: 'success',
            duration: 2000
          })
        }
      }).catch(() => {
        this.submitting = false
      })
    },
    handleTypeCreate() {
      this.$refs['createGroupDlg'].handleCreate();
    },
    createGroupAddEnd(row) {
      this.loadUrlGroupData()
      this.groupId = row.id + ''
      this.handleBatchSetSourceGroup(row.id + '')
    }
  }
}
</script>
