<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.trankContent" :value="query.trankName">
          <span>{{ $t('table.gradeName') }}：</span>
          <el-input v-model="query.trankName" v-trim clearable style="width: 100px;" />
        </SearchItem>
        <SearchItem model-key="query.tagContent" :value="query.tagContent">
          <span>{{ $t('table.labelName') }}：</span>
          <el-input v-model="query.tagContent" v-trim clearable style="width: 100px;" />
        </SearchItem>
        <SearchItem model-key="query.lossType" :value="query.lossType">
          <span>{{ $t('table.lossType') }}：</span>
          <el-select v-model="query.lossType" style="width: 150px" clearable>
            <el-option v-for="item in lossTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.block" :value="query.block">
          <span>{{ $t('table.limitType') }}：</span>
          <el-select v-model="query.block" style="width: 150px" clearable>
            <el-option v-for="item in blockOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'724'" :request="handleExport"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" v-permission="'727'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'726'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="true" :row-data-api="rowDataApi" :sortable="sortable" :custom-col="true" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.details')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileName')">
            {{ rowDetail.fileName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.lossType')">
            {{ lossTypeFormatter(rowDetail, rowDetail.lossType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.labelGrade')">
            {{ trankFormatter(rowDetail, rowDetail.trank) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.gradeName')">
            {{ rowDetail.trankName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.labelContent')">
            {{ rowDetail.tagContent }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.limitType')">
            {{ blockFormatter(rowDetail, rowDetail.isBlock) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.filePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.encryFlag')">
            {{ encryFormatter(rowDetail, rowDetail.encryFlag) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, exportExcel, deleteLog } from '@/api/behaviorAuditing/terminal/tagFileOutSendLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'TagFileOutSendLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'fileName', label: 'fileName', width: '120' },
        { prop: 'lossType', label: 'lossType', width: '100', formatter: this.lossTypeFormatter },
        { prop: 'trank', label: 'labelGrade', width: '100', formatter: this.trankFormatter },
        { prop: 'trankName', label: 'gradeName', width: '150' },
        { prop: 'tagContent', label: 'labelContent', width: '150' },
        { prop: 'isBlock', label: 'limitType', width: '150', formatter: this.blockFormatter },
        { prop: 'filePath', label: 'filePath', width: '100' },
        { prop: 'encryFlag', label: 'encryFlag', width: '150', formatter: this.encryFormatter },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
          buttons: [
            { label: 'download', disabledFormatter: this.downloadFormatter, click: this.handleDownload, isShow: () => this.hasPermission('727') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('724') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        deleteRecords: [],
        trankName: null,
        tagContent: null,
        fileName: null,
        lossType: null,
        block: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      selection: [],
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      queryVideoMethod: undefined,
      tagTypeOptions: {
        0: this.$t('button.add'),
        1: this.$t('button.delete'),
        2: this.$t('button.tagExpired')
      },
      blockOptions: [
        { value: null, label: this.$t('pages.all') },
        { value: 0, label: this.$t('pages.labelPermissionMsgTips4') },
        { value: 1, label: this.$t('pages.labelPermissionMsgTips5') }
      ],
      encryFlagOptions: {
        0: this.$t('pages.plainText'),
        1: this.$t('pages.cipherText')
      },
      lossTypeOptions: [
        { value: null, label: this.$t('pages.all') },
        { value: 0, label: this.$t('pages.stgLabelFtpUploadFile') },
        { value: 2, label: this.$t('pages.usbCopyFile') },
        { value: 3, label: this.$t('pages.usbCutFile') },
        { value: 7, label: this.$t('pages.stgLabelRemoteShareFile') },
        { value: 9, label: this.$t('pages.stgLabelImSendFile') },
        { value: 13, label: this.$t('pages.stgLabelCdBurnFile') },
        { value: 15, label: this.$t('pages.stgLabelWebUploadFile') },
        { value: 18, label: this.$t('pages.stgLabelMail') },
        { value: 20, label: this.$t('pages.stgLabelBluetooth') },
        { value: 29, label: this.$t('pages.stgLabelMtpSendFile') },
        { value: 32, label: this.$t('pages.stgLabelRemoteOutgoingFile') }
      ],
      tempTask: {},
      defaultTempTask: {
        backType: 21,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      }
    }
  },
  computed: {
    lossTypeMap() {
      const map = {}
      this.lossTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    },
    blockMap() {
      const map = {}
      this.blockOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    handleDownload(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    },
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    encryFormatter(row, data) {
      return this.encryFlagOptions[data]
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '601', this.query.searchReport)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleExport(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    lossTypeFormatter(row, data) {
      return this.lossTypeMap[data]
    },
    trankFormatter(row, data) {
      return data == 0 && (row.trank == '' || row.trank == null) ? '' : data
    },
    blockFormatter(row, data) {
      return this.blockMap[data]
    }
  }
}
</script>
