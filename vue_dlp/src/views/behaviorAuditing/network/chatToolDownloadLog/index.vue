<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.chatType" :value="query.chatType">
          <span>{{ $t('table.chatTools') }}：</span>
          <el-select v-model="query.chatType" style="width: 130px">
            <el-option v-for="item in chatToolOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.actionType" :value="query.actionType">
          <span>{{ $t('table.action') }}：</span>
          <el-select v-model="query.actionType" style="width: 150px">
            <el-option :label="$t('pages.all')" :value="null"/>
            <el-option :label="$t('pages.forbid')" :value="1"></el-option>
            <el-option :label="$t('pages.allow')" :value="0"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'377'" :request="handleExport"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" v-permission="'376'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'454'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="true"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :selectable="selectable"
        :after-load="afterLoad"
        :custom-col="true"
        :autoload="autoload"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('route.chatToolDownloadLog')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize1')">
            {{ rowDetail.fileSize }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.accountNick')">
            {{ rowDetail.accountNick }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.chatType')">
            {{ softTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.processName')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileName1')">
            <el-button
              v-permission="'376'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!376'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, exportExcel, deleteLog } from '@/api/behaviorAuditing/network/chatToolDownloadLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'ChatToolDownloadLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'recordTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'processName', label: 'processName', width: '150' },
        { prop: 'softType', label: 'chatType', width: '150', formatter: this.softTypeFormatter },
        { prop: 'fileName', label: 'fileName1', width: '150' },
        { prop: 'actionType', label: 'action', width: '130', formatter: this.actionFormatter },
        { prop: 'fileSize', label: 'maxFileSize1', width: '150', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '140', fixed: 'right', hidden: !this.hasPermission('376,383'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, isShow: () => this.hasPermission('376') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('383') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        chatType: null,
        actionType: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      rowDetail: {},
      dialogFormVisible: false,
      chatToolOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.qq'), value: 3 },
        { label: this.$t('pages.wechat'), value: 23 },
        { label: this.$t('pages.dingTalk'), value: 25 },
        { label: this.$t('pages.enterpriseQQ'), value: 26 },
        { label: this.$t('pages.enterpriseWeChat'), value: 27 },
        // { label: this.$t('pages.aliTalk'), value: 13 },
        // { label: this.$t('pages.feiQ'), value: 20 },
        { label: this.$t('pages.feiShu'), value: 29 },
        // { label: 'skype', value: 10 },
        { label: this.$t('pages.tim'), value: 31 }
      ],
      selection: [],
      tempTask: {},
      defaultTempTask: {
        backType: 3,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  computed: {
    auditFileDownloader() {
      return this.$refs['auditFileDownloader']
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    selectable(row, index) {
      return !this.downloadFormatter(row) || (this.$store.getters.auditingDeleteAble && this.hasPermission('454'))
    },
    softTypeFormatter(row, data) {
      let result = ''
      for (let i = 0; i < this.chatToolOptions.length; i++) {
        if (this.chatToolOptions[i].value == row.softType) {
          result = this.chatToolOptions[i].label || ''
          break
        }
      }
      return result;
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    afterLoad(rowDatas) {
      rowDatas = rowDatas || []
      this.termsInfo = []
      for (const row of rowDatas) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowDatas, undefined, this.query.searchReport)
      return rowDatas;
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    handleLoadDown(row) {
      this.auditFileDownloader && this.auditFileDownloader.handleDownload(row)
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      // 如果备份文件存在后缀，以备份文件的后缀为主
      if (row.localFilePath && row.localFilePath.lastIndexOf('\\') >= 0) {
        const backFileName = row.localFilePath.substr(row.localFilePath.lastIndexOf('\\') + 1, row.localFilePath.length)
        if (backFileName.indexOf('.') != -1) {
          const suffix = backFileName.substr(backFileName.lastIndexOf('.') + 1, backFileName.length)
          this.tempTask.fileName = row.fileName + '.' + suffix
        } else {
          this.tempTask.fileName = row.fileName
        }
      } else {
        this.tempTask.fileName = row.fileName
      }
      return this.tempTask
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 180px;
}
</style>
