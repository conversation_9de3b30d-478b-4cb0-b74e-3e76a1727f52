<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('route.chatToolDownloadLog')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize1')">
            {{ rowDetail.fileSize }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.accountNick')">
            {{ rowDetail.accountNick }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.chatType')">
            {{ softTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.processName')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileName1')">
            <el-button
              v-permission="'376'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!376'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </el-dialog>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'376'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
  </div>
</template>
<script>

import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'ChatToolDownloadLogDetail',
  components: { AuditFileDownloader },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      selection: [],
      defaultTempTask: {
        backType: 3,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      chatToolOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.qq'), value: 3 },
        { label: this.$t('pages.wechat'), value: 23 },
        { label: this.$t('pages.dingTalk'), value: 25 },
        { label: this.$t('pages.enterpriseQQ'), value: 26 },
        { label: this.$t('pages.enterpriseWeChat'), value: 27 },
        // { label: this.$t('pages.aliTalk'), value: 13 },
        // { label: this.$t('pages.feiQ'), value: 20 },
        { label: this.$t('pages.feiShu'), value: 29 },
        // { label: 'skype', value: 10 },
        { label: this.$t('pages.tim'), value: 31 }
      ]
    }
  },
  computed: {
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    softTypeFormatter(row, data) {
      let result = ''
      for (let i = 0; i < this.chatToolOptions.length; i++) {
        if (this.chatToolOptions[i].value == row.softType) {
          result = this.chatToolOptions[i].label || ''
          break
        }
      }
      return result;
    },
    handleDownload: function(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      // 如果备份文件存在后缀，以备份文件的后缀为主
      if (row.localFilePath && row.localFilePath.lastIndexOf('\\') >= 0) {
        const backFileName = row.localFilePath.substr(row.localFilePath.lastIndexOf('\\') + 1, row.localFilePath.length)
        if (backFileName.indexOf('.') != -1) {
          const suffix = backFileName.substr(backFileName.lastIndexOf('.') + 1, backFileName.length)
          tempTask.fileName = row.fileName + '.' + suffix
        } else {
          tempTask.fileName = row.fileName
        }
      } else {
        tempTask.fileName = row.fileName
      }
      return tempTask
    }
  }
}
</script>
<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 200px;
}
</style>
