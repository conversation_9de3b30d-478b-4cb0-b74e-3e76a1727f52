<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <TimeQuery @getTimeParams="getTimeParams"/>
        <span>
          {{ $t('pages.industryTypes3') }}：
          <el-select v-model="query.keyword1" style="width: 150px">
            <el-option v-for="item in chatToolOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </span>
        <span>
          {{ $t('pages.fileName1') }}：
          <el-input v-model="query.keyword2" clearable style="width: 150px;" />
        </span>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <!--<el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>-->
      </div>
      <grid-table ref="logList" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="false" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <audit-file-downloader ref="auditFileDownloader" :show="false" :before-download="beforeDownload"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog } from '@/api/behaviorAuditing/network/chatFileLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'

export default {
  name: 'ChatFileLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: '发送时间', width: '150', sort: 'custom', formatter: (row) => { return row.msgTime } },
        { prop: 'terminalName', label: 'terminalName', width: '150' },
        { prop: 'userName', label: 'user', width: '150' },
        // { prop: 'groupId', label: '所属分组', width: '100' },
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'fileSize', label: 'maxFileSize1', width: '200' },
        { prop: 'chatSessionInfo', label: '接收人', width: '200' },
        { prop: 'chatType', label: 'chatTools', width: '200', formatter: this.chatTypeFormatter },
        { label: 'operate', type: 'button', width: '100', fixed: 'right', hidden: !this.hasPermission('221'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter }
          ]
        }
      ],
      chatToolOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: 'QQ', value: 0 },
        // { label: 'SKYPE', value: 1 },
        // { label: 'TM', value: 2 },
        { label: this.$t('pages.dingTalk'), value: 3 },
        // { label: '企业QQ', value: 4 },
        { label: this.$t('pages.wechat'), value: 5 },
        { label: this.$t('pages.aliTalk'), value: 6 },
        { label: this.$t('pages.enterpriseWeChat'), value: 7 },
        // { label: '千牛', value: 8 },
        { label: this.$t('pages.feiQ'), value: 9 }
        // ,
        // { label: '其他', value: 100 }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        keyword1: null,
        keyword2: null,
        searchReport: 1
      },
      showTree: true,
      deleteable: false,
      tempTask: {},
      defaultTempTask: {
        backType: 11,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    },
    chatTypeMap() {
      const map = {}
      this.chatToolOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  methods: {
    chatTypeFormatter: function(row) {
      return this.chatTypeMap[row.chatType]
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      if (row.localFilePath.lastIndexOf('\\') >= 0) {
        const backFileName = row.localFilePath.substr(row.localFilePath.lastIndexOf('\\') + 1, row.localFilePath.length)
        if (backFileName.indexOf('.') != -1) {
          const suffix = backFileName.substr(backFileName.lastIndexOf('.') + 1, backFileName.length)
          this.tempTask.fileName = row.fileName + '.' + suffix
        } else {
          this.tempTask.fileName = row.fileName
        }
      } else {
        this.tempTask.fileName = row.fileName
      }
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteLog({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    }
  }
}
</script>
