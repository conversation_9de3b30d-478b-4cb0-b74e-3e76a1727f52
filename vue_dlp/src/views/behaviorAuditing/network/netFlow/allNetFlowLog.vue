<template>
  <div class="app-container">
    <div class="table-container">
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('456')"
        :after-load="afterLoad"
        :sortable="sortable"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.netFlowLogDetails')"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="1" size="" border>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
              style="max-width: 270px;"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.totalFlowNum')">
            {{ rowDetail.totalFlowNum }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.sendFlowNum')">
            {{ rowDetail.sendFlowNum }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.receiveFlowNum')">
            {{ rowDetail.receiveFlowNum }}
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getLogPage, exportExcel, deleteLog } from '@/api/behaviorAuditing/network/netFlow'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'AllNetFlowLog',
  props: {
    query: { type: Object, default: null }
  },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'totalFlowNum', label: 'totalFlowNum', width: '200', sort: true },
        { prop: 'sendFlowNum', label: 'sendFlowNum', width: '180', sort: true },
        { prop: 'receiveFlowNum', label: 'receiveFlowNum', width: '180', sort: true },
        { prop: 'guid', label: 'GUID', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right', hidden: !this.hasPermission('292'),
          buttons: [
            // { label: 'detail', click: this.handleView },
            { label: '查看进程流量', isShow: (row) => !!row.guid, click: this.viewProcessFlow }
          ]
        }
      ],
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      queryVideoMethod: undefined,
      termsInfo: [] // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
    }
  },
  created() {
    //  若有购买报表模块时传3，表示查询范围 3：DLP+报表 1：DLP
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    //  给ColModel添加查看录屏的操作按钮
    addViewVideoBtn(this)
  },
  activated() {

  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    /**
     * 改变排序
     */
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd(selection) {
      this.$emit('selectionChangeEnd', selection)
    },
    /**
     * 查询
     */
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    /**
     * 导出
     * @param formData
     * @returns {AxiosPromise}
     */
    exportFunc(formData) {
      return exportExcel(formData)
    },
    /**
     * 查看详情
     * @param row
     */
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    /**
     * 查询后处理
     * @param rowData
     */
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      //  每次查询时，查询当前展示的数据的录屏信息
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '292')
    },
    /**
     * 获取终端信息
     * @returns {any}
     */
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    /**
     * 查看进程流量
     * @param row
     */
    viewProcessFlow(row) {
      //  条件
      const condition = {
        guid: row.guid
      }
      this.$emit('viewProcessFlow', condition)
    },
    /**
     * 刷新录屏信息
     */
    refreshVideoData() {
      //  刷新录屏信息
      this.queryVideoMethod && this.queryVideoMethod()
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 200px;
}
>>>.el-descriptions-item__content {
  min-width: 200px;
  max-width: 250px;
}
</style>
