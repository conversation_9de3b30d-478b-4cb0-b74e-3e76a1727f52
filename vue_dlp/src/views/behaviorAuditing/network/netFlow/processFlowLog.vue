<template>
  <div class="app-container">
    <div class="table-container">
      <grid-table
        ref="processLogList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('456')"
        :sortable="sortable"
        :custom-col="false"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
  </div>
</template>

<script>
import { deleteLog, exportExcel, getLogPage } from '@/api/behaviorAuditing/network/processNetFlow'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'ProcessFlowLog',
  props: {
    query: { type: Object, default() { return {} } }
  },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '150', formatter: logSourceFormatter },
        { prop: 'guid', label: 'GUID', width: '150' },
        { prop: 'procName', label: 'processName', width: '150' },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'totalFlowNum', label: 'totalFlowNum', width: '200', formatter: this.flowToKBFormatter },
        { prop: 'sendFlowNum', label: 'sendFlowNum', width: '180', formatter: this.flowToKBFormatter },
        { prop: 'receiveFlowNum', label: 'receiveFlowNum', width: '180', formatter: this.flowToKBFormatter }
      ],
      sortable: true
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['processLogList']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getLogPage(searchQuery)
    },
    selectionChangeEnd(selection) {
      this.$emit('selectionChangeEnd', selection)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    //  字节转换成KB
    flowToKBFormatter(row, data) {
      if (data) {
        return this.bytesToKB(data);
      }
      return data;
    },
    bytesToKB(flowNum) {
      if (flowNum === 0) {
        return '0';
      }
      const v = flowNum / 1024;
      return v.toFixed(5);
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 200px;
}
>>>.el-descriptions-item__content {
  min-width: 200px;
  max-width: 250px;
}
</style>
