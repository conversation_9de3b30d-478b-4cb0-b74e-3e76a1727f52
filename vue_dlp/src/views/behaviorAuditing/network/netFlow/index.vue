<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        :condition-btn="true"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.guid" :value="query.guid">
          <span>{{ 'GUID' }}：</span>
          <el-input v-model="query.guid" style="width: 150px;"></el-input>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'346'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'456'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
        <el-button slot="append" size="mini" @click="highSetting">
          {{ '高级设置' }}
        </el-button>
      </SearchToolbar>

      <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
        <!-- 总流量统计 -->
        <el-tab-pane :label="$t('pages.allNetFlowLog')" name="allNetFlowLog">
          <all-net-flow-log ref="allNetFlowLog" :query="query" @selectionChangeEnd="selectionChangeEnd" @viewProcessFlow="viewProcessFlow"/>
        </el-tab-pane>
        <!-- 进程流量统计 -->
        <el-tab-pane :label="$t('pages.processNetFlowLog')" name="processFlowLog">
          <process-flow-log ref="processFlowLog" :query="query" @selectionChangeEnd="selectionChangeEnd"/>
        </el-tab-pane>
      </el-tabs>
      <high-setting-dlg ref="highSettingDlg"/>
    </div>
  </div>
</template>

<script>
import HighSettingDlg from '@/views/behaviorManage/network/webFlow/highSettingDlg'
import AllNetFlowLog from './allNetFlowLog'
import ProcessFlowLog from '@/views/behaviorAuditing/network/netFlow/processFlowLog';
import { exportExcel } from '@/api/behaviorAuditing/network/netFlow';

export default {
  name: 'NetFlowLog',
  components: { ProcessFlowLog, AllNetFlowLog, HighSettingDlg },
  data() {
    return {
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList',
        guid: ''
      },
      showTree: true,
      sortable: true,
      selection: [],
      activeName: 'allNetFlowLog'
    }
  },
  created() {
  },
  activated() {
    this.activeName === 'allNetFlowLog' && this.$refs[this.activeName].refreshVideoData()
  },
  methods: {
    gridTable() {
      return this.$refs[this.activeName].gridTable()
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    /**
     * 选中事件
     * @param selection
     */
    selectionChangeEnd(selection) {
      this.selection = selection
    },
    /**
     * 查询
     */
    handleFilter() {
      this.selection = []
      this.$refs[this.activeName].handleFilter()
    },
    /**
     * 部门树是否展示
     */
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    /**
     * 时间条件回调
     * @param timeObj
     */
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    /**
     * 导出
     * @param exportType
     * @returns {*}
     */
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    /**
     * 删除日志
     * @param data
     * @param headers
     * @returns {*}
     */
    deleteLog(data, headers) {
      return this.$refs[this.activeName].deleteLog(data, headers);
    },
    /**
     * 跳转到进程流量统计
     * @param condition
     */
    viewProcessFlow(condition) {
      this.query.guid = condition.guid
      this.activeName = 'processFlowLog'
      this.handleFilter()
    },
    /**
     * 点击Tab事件
     */
    tabClick() {
      this.handleFilter();
    },
    /**
     * 高级设置
     */
    highSetting() {
      this.$refs.highSettingDlg.show();
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 200px;
}
>>>.el-descriptions-item__content {
  min-width: 200px;
  max-width: 250px;
}
</style>
