<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.netDiskLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize2')">
            {{ rowDetail.fileSizeStr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.netDiskType')">
            {{ appTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.operateType')">
            {{ signFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileOrDirName')">
            <el-button
              v-permission="'223'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!223'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.filePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: 'NetDiskLogDetail',
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      signOption: [
        { label: this.$t('pages.uploadFile1'), value: 0, show: false },
        { label: this.$t('pages.downloadFile'), value: 1, show: false },
        { label: this.$t('pages.uploadFile1'), value: 4, show: true },
        { label: this.$t('pages.downloadFile'), value: 5, show: true },
        { label: this.$t('components.uploadFolder'), value: 6, show: true },
        { label: this.$t('components.downloadFolder'), value: 7, show: false }
      ],
      appTypeOption: [
        { label: this.$t('pages.softBaiDuNetDisk'), value: 0 },
        { label: this.$t('pages.QYWXMicroDisk'), value: 1 }
      ]
    }
  },
  computed: {
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    appTypeFormatter: function(row, data) {
      if (row.appType != null) {
        return this.appTypeOption[row.appType].label
      }
      return ''
    },
    signFormatter: function(row, data) {
      const obj = this.signOption.find(item => {
        return item.value == row.sign
      })
      return obj && obj.label
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    handleDownload: function(row) {
      this.$emit('downloadAuditLogFile', row)
    }
  }
}
</script>
