<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('table.fileOrDirName') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 180px;" />
        </SearchItem>
        <SearchItem model-key="query.sign" :value="query.sign">
          <span>{{ $t('table.operateType') }}：</span>
          <el-select v-model="query.sign">
            <el-option :value="null" :label="$t('pages.all')"/>
            <el-option v-for="item in signOption" v-show="item.show" :key="item.value" :value="item.value" :label="item.label"/>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.actionType" :value="query.actionType">
          <span>{{ $t('table.action') }}：</span>
          <el-select v-model="query.actionType" style="width: 150px">
            <el-option :label="$t('pages.all')" :value="null"/>
            <el-option :label="$t('pages.forbid')" :value="1"></el-option>
            <el-option :label="$t('pages.allow')" :value="0"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'350'" :request="handleExport"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" v-permission="'223'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'461'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        :autoload="autoload"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.netDiskLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize2')">
            {{ rowDetail.fileSizeStr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.netDiskType')">
            {{ appTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.operateType')">
            {{ signFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileOrDirName')">
            <el-button
              v-permission="'223'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!223'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.filePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, exportExcel, deleteLog } from '@/api/behaviorAuditing/network/netDiskLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'NetDiskLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: 'custom', formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'fileName', label: 'fileOrDirName', width: '150' },
        { prop: 'actionType', label: 'action', width: '130', formatter: this.actionFormatter },
        { prop: 'filePath', label: 'filePath', width: '250' },
        { prop: 'fileSizeStr', label: 'maxFileSize2', width: '150' },
        { prop: 'appType', label: 'netDiskType', width: '100', formatter: this.appTypeFormatter },
        { prop: 'sign', label: 'operateType', width: '100', formatter: this.signFormatter },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('223,297'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, isShow: () => this.hasPermission('223') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('297') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        fileName: null,
        sign: null,
        actionType: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      percentOptions: [
        { status: 0, percent: 0 },
        { status: 1, percent: 1 },
        { status: 5, percent: 50 },
        { status: 6, percent: 100 }
      ],
      appTypeOption: [
        { label: this.$t('pages.softBaiDuNetDisk'), value: 0 },
        { label: this.$t('pages.QYWXMicroDisk'), value: 1 }
      ],
      signOption: [
        { label: this.$t('pages.uploadFile1'), value: 0, show: false },
        { label: this.$t('pages.downloadFile'), value: 1, show: false },
        { label: this.$t('pages.uploadFile1'), value: 4, show: true },
        { label: this.$t('pages.downloadFile'), value: 5, show: true },
        { label: this.$t('components.uploadFolder'), value: 6, show: true },
        { label: this.$t('components.downloadFolder'), value: 7, show: false }
      ],
      tempTask: {},
      defaultTempTask: {
        backType: 12,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  computed: {
    chatTypeMap() {
      const map = {}
      this.chatToolOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    selectable(row, index) {
      return !this.downloadFormatter(row) || (this.$store.getters.auditingDeleteAble && this.hasPermission('461'))
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    chatTypeFormatter: function(row) {
      return this.chatTypeMap[row.chatType]
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = this.getFileName(row)
      return this.tempTask
    },
    getFileName(row) {
      // "D:\\$Eis$Bak\\DLPBackUp\\2024-05-11\\FileBackup\\zip\\0537fd51-ff92-4484-9c5f-03e1e56ec8d8.zip",
      if (row.localFilePath.indexOf('.zip') >= 0 && row.fileName.indexOf('.zip') < 0) {
        return row.fileName + '.zip'
      }
      return row.fileName
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    appTypeFormatter: function(row, data) {
      if (row.appType != null) {
        return this.appTypeOption[row.appType].label
      }
      return ''
    },
    signFormatter: function(row, data) {
      const obj = this.signOption.find(item => {
        return item.value == row.sign
      })
      return obj && obj.label
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '297', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
