<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.wifiName" :value="query.wifiName">
          <span>{{ $t('pages.wifiName') }}：</span>
          <el-input v-model="query.wifiName" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <SearchItem model-key="query.wifiMac" :value="query.wifiMac">
          <span>{{ $t('pages.wifiMac') }}：</span>
          <el-input v-model="query.wifiMac" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'344'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'455'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="$store.getters.auditingDeleteAble && hasPermission('455')" :row-data-api="rowDataApi" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.WIFILogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.logTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.wifiName1')">
            {{ rowDetail.wifiName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.wifiMac')">
            {{ rowDetail.wifiMac }}
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, exportExcel, deleteLog } from '@/api/behaviorAuditing/network/wifiLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'WifiLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: row => logSourceFormatter(row, row.logTime) },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        // { prop: 'groupId', label: '所属分组', width: '100' },
        { prop: 'wifiName', label: 'wifiName1', width: '150' },
        { prop: 'wifiMac', label: 'wifiMac', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('291'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        wifiName: '',
        wifiMac: '',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      selection: [],
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '291', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 180px;
}
</style>
