<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.eventType" :value="query.eventType">
          <span>{{ $t('table.eventType') }}:</span>
          <el-select v-model="query.eventType" clearable style="width: 150px;">
            <el-option :key="1" :value="1" :label="$t('pages.insert1')"/>
            <el-option :key="2" :value="2" :label="$t('pages.pullOut')"/>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.devType" :value="query.devType">
          <span>{{ $t('pages.devType') }}： </span>
          <el-select v-model="query.devType" filterable clearable allow-create style="width: 150px;">
            <el-option v-for="item in devTypeOpt" :key="item.value" :label="item.value" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'396'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'474'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="true" :row-data-api="rowDataApi" :sortable="sortable" :custom-col="true" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.netEventDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.identificationTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.eventType')">
            {{ eventFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.devType')">
            {{ rowDetail.deviceType }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.interfaceKey')">
            {{ rowDetail.netInterfaceKey }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.devDesc')">
            {{ rowDetail.deviceDesc }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, exportExcel, deleteLog } from '@/api/behaviorAuditing/terminal/netEvent'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'NetInterfaceLimitLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'identificationTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        // { prop: 'groupId', label: '所属分组', width: '100' },
        { prop: 'eventType', label: 'eventType', width: '100', formatter: this.eventFormatter },
        { prop: 'deviceType', label: 'devType', width: '100' },
        { prop: 'netInterfaceKey', label: 'interfaceKey', width: '120' },
        { prop: 'deviceDesc', label: 'devDesc', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('388'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      eventTypeOptions: [
        { label: this.$t('pages.unknownType'), value: 0 },
        { label: this.$t('pages.insert1'), value: 1 },
        { label: this.$t('pages.pullOut'), value: 2 }
      ],
      devTypeOpt: [
        { 'value': this.$t('pages.netEventType1') },
        { 'value': this.$t('pages.netEventType2') },
        { 'value': this.$t('pages.netEventType3') },
        { 'value': this.$t('pages.netEventType4') },
        { 'value': this.$t('pages.netEventType5') },
        { 'value': this.$t('pages.netEventType6') }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        devType: null,
        eventType: null,
        deleteRecords: [],
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      selection: [],
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      queryVideoMethod: undefined
    }
  },
  computed: {
    eventTypeMap() {
      const map = {}
      this.eventTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    eventFormatter: function(row) {
      return this.eventTypeMap[row.eventType]
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '388', this.query.searchReport)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleExport(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    }
  }
}
</script>
