<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.trankContent" :value="query.trankContent">
          <span>{{ $t('table.gradeName') }}：</span>
          <el-input v-model="query.trankContent" v-trim clearable style="width: 100px;" />
        </SearchItem>
        <SearchItem model-key="query.tagContent" :value="query.tagContent">
          <span>{{ $t('table.labelName') }}：</span>
          <el-input v-model="query.tagContent" v-trim clearable style="width: 100px;" />
        </SearchItem>
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('table.fileName') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 100px;" />
        </SearchItem>
        <SearchItem model-key="query.addType" :value="query.addType">
          <span>{{ $t('pages.operateType') }}：</span>
          <el-select v-model="query.addType" style="width: 150px" clearable>
            <el-option v-for="(label, value) in addTypeOptions" :key="value" :label="label" :value="value"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'720'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'722'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="true" :row-data-api="rowDataApi" :sortable="sortable" :custom-col="true" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.details')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.labelGrade')">
            {{ trankFormatter(rowDetail, rowDetail.trank) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.gradeName')">
            {{ trankContentFormatter(rowDetail, rowDetail.trankContent) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.labelContent')">
            {{ tagContentFormatter(rowDetail, rowDetail.tagContent) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileName')">
            {{ rowDetail.fileName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.filePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
          <!--<el-descriptions-item span="2" :label="$t('table.processName')">
            {{ rowDetail.processName }}
          </el-descriptions-item>-->
          <el-descriptions-item span="2" :label="$t('pages.type')">
            {{ addTypeFormatter(rowDetail, rowDetail.addType) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.labelContentDetail')"
      :visible.sync="detailDialogFormVisible"
      width="600px"
    >
      <grid-table ref="detailList" row-key="logId" :col-model="detailColModel" :height="200" :default-sort="{ prop: 'createTime' }" :row-data-api="detailDataApi"/>
    </el-dialog>
  </div>
</template>

<script>
import { getLogPage, exportExcel, deleteLog } from '@/api/behaviorAuditing/terminal/addTagLog'
import { getTagContentPage } from '@/api/behaviorAuditing/terminal/tagContentLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'TagingLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'trank', label: 'labelGrade', width: '100', formatter: this.trankFormatter },
        { prop: 'trankContent', label: 'gradeName', width: '150', formatter: this.trankContentFormatter },
        { prop: 'tagContent', label: 'labelContent', width: '150', formatter: this.tagContentFormatter },
        { prop: 'fileName', label: 'fileName', width: '120' },
        { prop: 'filePath', label: 'filePath', width: '100' },
        // { prop: 'processName', label: 'processName', width: '100' },
        { prop: 'addType', label: 'operateType', width: '100', formatter: this.addTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
          buttons: [
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('721') },
            { label: 'labelContentDetail', click: this.handleTagView, isShow: this.isShowButtonFormat }
          ]
        }
      ],
      detailColModel: [
        { prop: 'createTime', label: 'time', width: '150', sort: true },
        { prop: 'tagContent', label: 'labelContent', width: '150' },
        { prop: 'tagType', label: 'labelStatus', width: '100', formatter: this.tagTypeFormatter } // 标签状态{0：新增 1：删除 2：失效}
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        deleteRecords: [],
        trankContent: null,
        tagContent: null,
        fileName: null,
        addType: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      detailQuery: {
        page: 1,
        createDate: '',
        recordGuid: '',
        isTimes: false
      },
      showTree: true,
      selection: [],
      rowDetail: {},
      dialogFormVisible: false,
      detailDialogFormVisible: false,
      sortable: true,
      queryVideoMethod: undefined,
      addTypeOptions: {},
      tagTypeOptions: {
        0: this.$t('button.add'),
        1: this.$t('button.delete'),
        2: this.$t('button.tagExpired')
      }
    }
  },
  computed: {
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    if (this.hasPermission('125')) {
      Object.assign(this.addTypeOptions, { 0: this.$t('pages.addTypeMsg'), 2: this.$t('pages.addTypeMsg2') })
    }
    if (this.hasPermission('113')) {
      Object.assign(this.addTypeOptions, { 3: this.$t('pages.addTypeMsg3') })
    }
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    isShowButtonFormat(row) {
      if (!this.hasPermission('721')) {
        return false
      }
      // if (row && (row.clearFlag == 1 || row.clearFlag == 2 || row.tagContent == '' || row.tagContent == null)) {
      //   return false
      // }
      return row.recordGuid
    },
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    detailDataApi: function(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      this.detailQuery.sortName = option.sortName
      this.detailQuery.sortOrder = option.sortOrder
      return getTagContentPage(searchQuery)
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '601', this.query.searchReport)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleExport(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    handleTagView(row) {
      this.detailDialogFormVisible = true
      this.detailQuery.createDate = row.createTime.slice(0, 10)
      this.detailQuery.recordGuid = row.recordGuid
      this.$nextTick(() => {
        this.$refs['detailList'].execRowDataApi(this.detailQuery)
      })
    },
    addTypeFormatter(row, data) {
      return this.addTypeOptions[data]
    },
    tagTypeFormatter(row, data) {
      return this.tagTypeOptions[data]
    },
    trankFormatter(row, data) {
      return data == 0 && (row.trankContent == '' || row.trankContent == null) ? '' : data
    },
    trankContentFormatter(row, data) {
      return row.clearFlag == 1 ? this.$t('pages.noSignInfo') : data
    },
    tagContentFormatter(row, data) {
      return row.clearFlag == 1 ? this.$t('pages.noSignInfo') : row.clearFlag == 2 ? this.$t('report.tagReportMsg33') : data
    }
  }
}
</script>
