<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.host" :value="query.host">
          <span>{{ $t('pages.website') }}：</span>
          <el-input v-model="query.host" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <SearchItem model-key="query.hostGroupId" :value="query.hostGroupId">
          <span>{{ $t('table.hostSourceGroup') }}：</span>
          <el-select v-model="query.hostGroupId" clearable>
            <el-option v-for="(item, index) in urlGroupData" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'340'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'451'" :selection="selection" :date-range="dateRange" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'accessTime' }" :row-data-api="rowDataApi" :multi-select="$store.getters.auditingDeleteAble && hasPermission('451')" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd"/>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.webBrowseTimeDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
              style="max-width:210px"
            />
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.hostSourceGroup')">
            {{ rowDetail.hostGroupName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.website')">
            {{ rowDetail.host }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.browseDuration')">
            {{ rowDetail.totalTimeStr }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.accessTime')">
            {{ rowDetail.accessTime }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, exportWebBrowseTimeLog, deleteLog } from '@/api/behaviorAuditing/network/webBrowseTime'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { getTreeNode } from '@/api/system/baseData/urlLibrary';

export default {
  name: 'WebBrowseTime',
  data() {
    return {
      colModel: [
        // { prop: 'createTime', label: 'time', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'accessTime', label: 'accessTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'host', label: 'website', width: '200' },
        { prop: 'hostGroupName', label: 'hostSourceGroup', width: '150' },
        { prop: 'totalTimeStr', label: 'browseDuration', sort: true, width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('288'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList',
        host: '',
        hostGroupId: undefined
      },
      dateRange: undefined,
      showTree: true,
      tableLoading: false,
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      selection: [],
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined,
      urlGroupData: []
    }
  },
  created() {
    this.loadUrlGroupData()
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.loadUrlGroupData()
    this.gridTable().execRowDataApi()
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      const { createDate, startDate, endDate, isTimes } = this.query
      this.dateRange = { createDate, startDate, endDate, isTimes }
      return getLogPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportWebBrowseTimeLog(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '288', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    async loadUrlGroupData() {
      const { data } = await getTreeNode()
      this.urlGroupData = []
      const groupData = JSON.parse(JSON.stringify(data))
      groupData.forEach((item) => {
        const group = { value: item.dataId, label: item.label }
        this.urlGroupData.push(group)
      })
    }
  }
}
</script>
