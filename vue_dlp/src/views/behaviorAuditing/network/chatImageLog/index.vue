<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        {{ $t('pages.timeQuery') }}：
        <el-date-picker v-model="query.startDate" style="width: 200px" :clearable="false" type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsStart" placeholder="开始日期"></el-date-picker>
        -->
        <el-date-picker v-model="query.endDate" style="width: 200px" :clearable="false" type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsEnd" placeholder="结束日期"></el-date-picker>
        {{ $t('pages.chatAllLog_Msg7') }}
        <el-select v-model="query.chatType" style="width: 150px">
          <el-option v-for="item in chatToolOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <!--<el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>-->
      </div>

      <div style="height: calc(100% - 40px);">
        <div class="tree-container">
          <tree-menu ref="sessionTree" :data="treeData" :icon-option="iconOption" :is-filter="false" @node-click="treeNodeClick" />
        </div>
        <div class="table-container">
          <div style="border: 1px solid #666;height: 100%;overflow-y: auto">
            <div v-for="msg in msgList" :key="msg.id+msg.localFilePath+msg.createTime">
              <div style="padding-top: 10px;padding-left: 5px">
                <el-row>
                  <el-col :span="20">
                    <span >{{ msg.senderInfo }}&nbsp;&nbsp;&nbsp;{{ msg.createTime }}</span><br>
                    <span>{{ msg.localFilePath }}</span>
                  </el-col>
                  <el-col :span="4">
                    <el-button :disabled="!msg.fileGuid" @click="handleLoadDown(msg)">{{ $t('components.download') }}</el-button>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <audit-file-downloader ref="auditFileDownloader" :show="false" :before-download="beforeDownload"/>
  </div>
</template>

<script>
import { getSessionTree, getMsgList/*, deleteLog*/ } from '@/api/behaviorAuditing/network/chatImageLog'
import moment from 'moment'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'

export default {
  name: 'ChatImageLog',
  data() {
    return {
      pickerOptionsStart: {
        disabledDate: time => {
          const endDateVal = this.query.endDate
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime()
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.query.startDate
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime() - 1 * 24 * 60 * 60 * 1000)
          }
        }
      },
      query: { // 查询条件
        objectType: undefined,
        objectId: undefined,
        startDate: '',
        endDate: '',
        chatType: null,
        keyword: '',
        searchReport: 1
      },
      chatToolOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.qq'), value: 0 },
        { label: this.$t('pages.wechat'), value: 3 },
        { label: this.$t('pages.dingTalk'), value: 1 },
        { label: this.$t('pages.enterpriseWeChat'), value: 5 },
        { label: this.$t('pages.aliTalk'), value: 4 },
        { label: this.$t('pages.feiQ'), value: 7 }
      ],
      treeData: [],
      msgList: [],
      showTree: true,
      deleteable: false,
      iconOption: { 'telminate': 'terminal' },
      tempTask: {},
      defaultTempTask: {
        backType: 10,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      }
    }
  },
  computed: {
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    }
  },
  created() {
    const date = new Date()
    this.query.startDate = (moment(date).format('YYYY-MM-DD')) + ' 0:00:00'
    this.query.endDate = (moment(date).format('YYYY-MM-DD')) + ' 23:59:59'
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  methods: {
    // 点击会话节点加载聊天信息
    treeNodeClick: function(data, node, el) {
      if (data.type === 'session') {
        this.query.keyword = data.label
        this.loadMsgList(data)
      }
    },
    // 加载会话信息树
    loadSessionTree: function() {
      const tempQuery = { // 查询条件
        objectType: this.query.objectType,
        objectId: this.query.objectId,
        startDate: this.query.startDate,
        endDate: this.query.endDate,
        chatType: this.query.chatType,
        searchReport: this.query.searchReport
      }
      getSessionTree(tempQuery).then(res => {
        this.treeData = res.data
        this.msgList = []
      })
    },
    // 加载聊天信息
    loadMsgList: function(data) {
      const tempQuery = { // 查询条件
        objectType: 1,
        objectId: data.oriData.terminalId,
        startDate: this.query.startDate,
        endDate: this.query.endDate,
        chatType: data.oriData.chatType,
        keyword: this.query.keyword,
        searchReport: this.query.searchReport
      }
      getMsgList(tempQuery).then(res => {
        this.msgList = res.data
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.deleteable = true
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
        // 选中终端或者租，加载这个终端或组下的所有会话记录
        this.loadSessionTree()
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.treeData = []
        this.msgList = []
        this.deleteable = true
      }
    },
    handleFilter() {
      let months = ''
      const queryTemp = { // 查询条件
        objectType: this.query.objectType,
        objectId: this.query.objectId,
        startDate: moment(this.query.startDate).format('YYYY-MM-DD'),
        endDate: moment(this.query.endDate).format('YYYY-MM-DD'),
        chatType: this.query.chatType,
        searchReport: this.query.searchReport
      }
      if (this.query.searchReport != 1) {
        backupLogList(queryTemp).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.loadSessionTree()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      if (row.localFilePath != null && row.localFilePath != '') {
        if (row.localFilePath.lastIndexOf('\\') >= 0) {
          this.tempTask.fileName = row.localFilePath.substr(row.localFilePath.lastIndexOf('\\') + 1, row.localFilePath.length)
        } else {
          this.tempTask.fileName = row.localFilePath
        }
      }
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    }
    // handleDelete() {
    //   let chatTool = ''
    //   if (this.query.chatType) {
    //     this.chatToolOptions.forEach(item => {
    //       if (item.value == this.query.chatType) {
    //         chatTool = item.label
    //       }
    //     })
    //   }
    //   const node = this.strategyTargetTree.$refs.terminalTree[0].getCurrentNode()
    //   const msg = `确认要删除(${node.label})<${moment(this.query.startDate).format('YYYY-MM-DD')}至${moment(this.query.endDate).format('YYYY-MM-DD')}>的${chatTool}聊天记录吗?`
    //   this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
    //     deleteLog(this.query).then(respond => {
    //       this.loadSessionTree()
    //       this.$notify({
    //         title: this.$t('text.success'),
    //         message: this.$t('text.deleteSuccess'),
    //         type: 'success',
    //         duration: 2000
    //       })
    //     })
    //   }).catch(() => {})
    // }
  }
}
</script>
