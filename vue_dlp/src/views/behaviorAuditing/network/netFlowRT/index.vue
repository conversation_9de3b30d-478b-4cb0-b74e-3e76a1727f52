<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        :time-query="false"
        :condition-btn="false"
        :search-btn="false"
        @toggleTreeMenu="toggleTreeMenu"
      >
        <el-button slot="append" type="primary" icon="el-icon-refresh" :disabled="btnDisable" size="mini" @click="sendToUser">
          {{ $t('button.refresh') }}
        </el-button>
        <audit-log-exporter slot="append" v-permission="'499'" :request="handleExport" :disabled="btnDisable"/>
        <el-checkbox slot="append" v-model="autoRefresh" :true-label="1" :false-label="0" style="margin-left: 10px;font-weight: bold" @change="changeIntervalTime">
          {{ $t('pages.automaticRefreshTime') }}
        </el-checkbox>
        <el-input-number
          slot="append"
          v-model="intervalTime"
          controls-position="right"
          :precision="0"
          :min="1"
          :max="60"
          :disabled="autoRefresh === 0"
          style="width:120px;"
          @blur="handleBlur"
          @change="changeIntervalTime"
        />
      </SearchToolbar>
      <grid-table
        ref="fileTable"
        v-loading="tableLoading"
        :col-model="colModel"
        :row-datas="rowData"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        :show-pager="false"
      />
    </div>
  </div>
</template>

<script>
import { getSystemFlow, exportSystemFlow } from '@/api/assets/systemMaintenance/flow'
import { ctrlErrorMap, enableCtrlTerm } from '@/api/system/terminalManage/moduleConfig';

export default {
  name: 'NetFlowLogRT',
  data() {
    return {
      multiSelect: false,
      defaultSort: { prop: 'name', order: 'asc' },
      colModel: [
        { prop: 'name', label: 'processName1', width: '200', fixed: true, sort: true },
        { prop: 'id', label: 'processID', width: '200', sort: true },
        // totalSize 字段为手动添加，sendSize与recvSize之和
        { prop: 'totalSize', label: 'totalFlow', width: '150', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'sendSize', label: 'sendingVolume', width: '100', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'recvSize', label: 'receivingVolume', width: '110', sort: true, sortOriginal: true, formatter: this.sizeFormatter }
      ],
      query: { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: ''
      },
      rowData: [],
      btnDisable: true,
      ctrlAble: false,
      showTree: true,
      timer: undefined,             // 请求数据的定时器
      termId: undefined,            // 当前选中节点的
      tableLoading: false,          // 列表加载中的动画，仅切换节点后首次加载数据时显示，后续更新数据不显示
      activated: true,              // 是否在当前页面
      completeLoading: true,         // 请求数据是否完成的标识
      intervalTime: 5,
      autoRefresh: 1
    }
  },
  computed: {
    gridTable() {
      return this.$refs['fileTable']
    },
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    }
  },
  mounted() {
    clearInterval(this.timer)
    if (this.autoRefresh) {
      this.timer = setInterval(() => {
        this.sendToUser()
      }, this.intervalTime * 1000)
    }
  },
  activated() {
    this.activated = true
  },
  deactivated() {
    this.activated = false
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  created() {
  },
  methods: {
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('flowObservation', termId).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    strategyTargetNodeChange(tabName, data) {
      this.rowData.splice(0)
      this.termId = undefined
      this.btnDisable = true
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.checkCtrlAble(data.dataId).then(() => {
          if (!this.ctrlAble) return
          this.btnDisable = false
          this.tableLoading = true
          this.completeLoading = true
          this.termId = data.dataId
          this.sendToUser()
        })
      }
    },
    sendToUser() {
      // 离开当前页面 或 未选中终端节点 或 上次请求数据未完成，不请求数据
      if (!this.activated || !this.termId || !this.completeLoading) {
        return
      }
      this.completeLoading = false
      getSystemFlow(this.termId, this.tableLoading).then(respond => {
        this.completeLoading = true
        this.tableLoading = false
        if (this.termId) {
          // 增加totalSize字段，值为 sendSize 和 recvSize 之和
          const rowDatas = respond.data.map(data => {
            this.$set(data, 'totalSize', data.sendSize + data.recvSize)
            return data
          });
          this.rowData.splice(0, this.rowData.length, ...rowDatas)
        } else {
          // 接收到数据的时候，若选中节点已变更为分组，则清空数据
          this.rowData.splice(0)
        }
      })
    },
    handleExport(exportType) {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      return exportSystemFlow({ exportType, termId: curNodeData.dataId })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    sizeFormatter: function(row, data) {
      // 根据大小值，转换为KB、M、G
      let size = ''
      if (data < 0.1 * 1024) { // 小于0.1KB，则转化成B
        size = data.toFixed(2) + 'B'
      } else if (data < 0.1 * 1024 * 1024) { // 小于0.1MB，则转化成KB
        size = (data / 1024).toFixed(2) + 'KB'
      } else if (data < 0.1 * 1024 * 1024 * 1024) { // 小于0.1GB，则转化成MB
        size = (data / (1024 * 1024)).toFixed(2) + 'MB'
      } else { // 其他转化成GB
        size = (data / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
      }
      const sizeStr = size + '' // 转成字符串
      const index = sizeStr.indexOf('.') // 获取小数点处的索引
      const dou = sizeStr.substr(index + 1, 2) // 获取小数点后两位的值
      if (dou == '00') { // 判断后两位是否为00，如果是则删除00
        return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2)
      }
      return size
    },
    handleBlur(event) {
      const val = event.target.value.trim()
      if (!val) {
        this.intervalTime = 1
      }
    },
    changeIntervalTime() {
      clearInterval(this.timer)
      if (this.autoRefresh) {
        this.timer = setInterval(() => {
          this.sendToUser()
        }, this.intervalTime * 1000)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-checkbox__label {
  padding-left: 0;
}
</style>
