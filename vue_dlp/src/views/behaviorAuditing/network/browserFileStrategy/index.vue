<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="100px"
        style="width: 748px;"
      >
        <StgBaseFormItem
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus !== 'create'"
          :strategy-def-type="query.strategyDefType"
          :formable="formable"
          :active-able="treeable"
        />

        <el-tabs v-model="activeName">
          <el-tab-pane name="control" :label="$t('pages.browserUploadControl')">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <div style="padding: 0 10px">
              <import-table
                ref="urlConfigTable"
                auto-height
                :max-height="188"
                :search-info-prompt-message="$t('pages.url_text1')"
                :delete-disabled="!urlConfigDeleteable"
                :col-model="elgColModel"
                :row-datas="tempUrlConfigElgData"
                :row-no-label="$t('table.keyId')"
                :loading="importConfigTableLoading"
                :formable="formable"
                :selectable="() => true"
                :handle-create="() => relUrlDlg().createUrlDlg()"
                :handle-delete="deleteUrlData"
                :handle-import="handleUrlImport"
                :handle-search="handleSearch"
                @selectionChangeEnd="(rowDatas) => urlConfigDeleteable = rowDatas.length > 0"
              />
            </div>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="28px">
              <el-radio-group v-model="temp.detectionRule" :disabled="!formable" style="margin-left: 0">
                <el-radio :label="1">{{ $t('pages.browserUploadRule1') }}</el-radio>
                <el-radio :label="0">{{ $t('pages.browserUploadRule2') }}</el-radio>
              </el-radio-group>
              <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.executeRuleTip', {info: $t('table.websiteUrl'), info1: $t('table.websiteUrl')}) }}</label>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  <i18n path="pages.withinDetectionRuleTip">
                    <span slot="info">{{ $t('pages.allowFileToWebpage') }}</span>
                  </i18n>
                  <br/>
                  <i18n path="pages.outsideDetectionRuleTip1">
                    <span slot="info">{{ $t('pages.forbidFileToWebpage') }}</span>
                  </i18n>
                </div>
                <i class="el-icon-info"/>
              </el-tooltip>
            </FormItem>
            <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
            <FormItem label-width="30px">
              <el-row>
                <el-col :span="16">
                  <el-checkbox-group v-model="approvalTypeList" :disabled="!formable" @change="handleApprovalTypeChange">
                    <el-checkbox :label="1">{{ hasEncPermision ? $t('pages.burnMsg4') : $t('pages.burnMsg5') }}</el-checkbox>
                    <el-checkbox :disabled="approvalTypeList.indexOf(1) == -1" :label="4">{{ hasEncPermision ? $t('pages.burnMsg6') : $t('pages.burnMsg7') }}</el-checkbox>
                    <br>
                    <el-checkbox v-if="hasEncPermision" :label="2">{{ $t('pages.burnMsg8') }}</el-checkbox>
                    <el-checkbox v-if="hasEncPermision" :disabled="approvalTypeList.indexOf(2) == -1" :label="8">{{ $t('pages.burnMsg9') }}</el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
            </FormItem>
            <ResponseContent
              :select-style="{ 'margin-top': '5px', 'margin-left': '13px' }"
              :status="dialogStatus"
              :show-select="true"
              :rule-type-name="ruleTypeName"
              :editable="formable && ruleDisable"
              read-only
              :prop-check-rule="!!temp.isAlarm"
              :show-check-rule="true"
              :prop-rule-id="propRuleId"
              @ruleIsCheck="getRuleIsCheck"
              @getRuleId="getRuleId"
            />
            <ResponseContent
              v-if="hasEncPermision"
              :select-style="{ 'margin': '5px 0 5px 13px' }"
              :status="dialogStatus"
              :show-select="true"
              :rule-type-name="encRuleTypeName"
              :editable="formable && encRuleDisable"
              read-only
              :prop-check-rule="!!temp.isEncAlarm"
              :show-check-rule="true"
              :prop-rule-id="encPropRuleId"
              @ruleIsCheck="getEncRuleIsCheck"
              @getRuleId="getEncRuleId"
            />
          </el-tab-pane>
          <el-tab-pane name="record" :label="$t('pages.browserUploadRecord')">
            <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
            <div style="padding: 0 10px">
              <import-table
                ref="urlTable"
                auto-height
                :max-height="188"
                :search-info-prompt-message="$t('pages.url_text1')"
                :delete-disabled="!urlDeleteable"
                :col-model="elgColModel"
                :row-datas="tempUrlElgData"
                :row-no-label="$t('table.keyId')"
                :loading="importTableLoading"
                :formable="formable"
                :selectable="() => true"
                :handle-create="() => relUrlDlg().createUrlDlg()"
                :handle-delete="deleteUrlData"
                :handle-import="handleUrlImport"
                :handle-search="handleSearch"
                @selectionChangeEnd="(rowDatas) => urlDeleteable = rowDatas.length > 0"
              />
            </div>
            <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
            <FormItem label-width="28px">
              <el-radio-group v-model="temp.recordDetectionRule" :disabled="!formable" style="margin-left: 0">
                <el-radio :label="1">{{ $t('pages.browserUploadRule1') }}</el-radio>
                <el-radio :label="0">{{ $t('pages.browserUploadRule2') }}</el-radio>
              </el-radio-group>
              <label style="font-weight: 500;color: #409eff;font-size: small;">{{ $t('pages.executeRuleTip', {info: $t('table.websiteUrl'), info1: $t('table.websiteUrl')}) }}</label>
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">
                  <i18n path="pages.withinDetectionRuleTip">
                    <span slot="info">{{ $t('pages.forbidRecordFileToWebpage') }}</span>
                  </i18n>
                  <br/>
                  <i18n path="pages.outsideDetectionRuleTip">
                    <span slot="info">{{ $t('pages.allowRecordFileToWebpage') }}</span>
                  </i18n>
                </div>
                <i class="el-icon-info"/>
              </el-tooltip>
            </FormItem>
            <el-divider content-position="left" style="margin: 0 15px">{{ $t('pages.responseRule') }}</el-divider>
            <div class="response-rule">
              <FormItem label-width="28px" prop="fileBackUpLimit">
                <el-checkbox v-model="temp.recordLog" :disabled="!formable" :false-label="0" :true-label="1" @change="recordLogChange">
                  <span style="margin-left: 8px">{{ $t('pages.recordBrowserUploadLog') }}</span>
                </el-checkbox>
                <div style="height: 2px"/>
                <el-checkbox v-model="temp.needBackup" :disabled="!formable || temp.recordLog === 0" :false-label="0" :true-label="1" @change="needBackupChange">
                  <i18n path="pages.blueTooth_Msg1" style="margin-left: 8px">
                    <el-input-number
                      slot="size"
                      v-model="temp.fileBackUpLimit"
                      :disabled="temp.needBackup === 0 || !formable"
                      :controls="false"
                      :min="1"
                      :max="10240"
                      step-strictly
                      style="width: 80px;"
                      size="mini"
                    />
                  </i18n>
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.ftpControl_text3') }}<br/>
                      {{ $t('pages.ftpControl_text4') }}
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                  <!-- <span style="color: #2b7aac;margin-left: 10px">{{ $t('pages.processMonitor_Msg28') }}</span> -->
                  <el-button :disabled="!formable || temp.recordLog === 0" style="margin-left: 10px" @click="handleBackupRule"><i class="el-icon-setting"/></el-button>
                </el-checkbox>
              </FormItem>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div style="white-space: pre-wrap;color: #2b7aac;">{{ $t('text.prompt') }}：</div>
        <span style="white-space: pre-wrap;color: #2b7aac;">
          <i18n path="pages.webpageUploadFileTip">
            <br slot="br"/>
          </i18n>
        </span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'A53'" :link-url="'/system/baseData/urlLibrary'" :btn-text="$t('pages.maintainUrl')"/>
        <el-button v-if="formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importBrowserFileStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
    <backup-rule-content
      ref="backupRuleContent"
      :prop-rule-id="backupRuleId"
      :prop-check-rule="isBackupRule"
      @setBackupRule="setBackupRule"
    />
    <!-- 新增/修改网址 (检测规则 URL过滤) -->
    <rel-url-dlg ref="relUrlDlg" :url-tree-node="urlTreeNode" @updateInfo="updateRelInfo" @createGroup="createGroupDlg().handleCreate()"/>
    <!--导入网址库 （检测规则 URL过滤）-->
    <import-table-dlg
      ref="urlImportTable"
      :not-selected-prompt-message="$t('pages.url_text2')"
      :elg-title="$t('pages.importUrlLibrary')"
      :group-root-name="$t('pages.urlLibrary')"
      :search-info-name="$t('pages.websiteNameOrURL')"
      :confirm-button-name="$t('pages.addUrl')"
      :group-title="$t('pages.url_group_title')"
      :col-model="importColModel"
      :list="getUrlList"
      :load-group-tree="getTreeNode"
      :create-group="createUrlGroup"
      :update-group="updateUrlGroup"
      :delete-group="deleteUrlGroup"
      :count-by-group="countUrlByGroupId"
      :get-group-by-name="getUrlGroupByName"
      :delete="deleteUrl"
      :delete-elg-able="true"
      :delete-group-and-data="deleteGroupAndData"
      :delete-group-func="deleteUrlGroup"
      :move-group-to-other="moveGroupToOther"
      :handle-create-elg="handleCreateElg"
      :get-list-by-group-ids="listUrlByGroupId"
      @submitEnd="importSubmitEnd"
      @submitDeleteEnd="importDeleteEnd"
      @changeGroupAfter="changeGroupAfter"
    />
    <!-- 添加分组 （检测规则 URL过滤） -->
    <edit-group-dlg
      ref="createGroupDlg"
      :title="$t('pages.group')"
      :group-tree-data="urlTreeNode"
      :edit-valid-func="getUrlGroupByName"
      :add-func="createUrlGroup"
      @addEnd="(val) => {
        listTreeNode()
        relUrlDlg().setGroupId(val.id + '')
      }"
    />
  </div>
</template>
<script>
import {
  getDataPage, getDataByName, createData, updateData, deleteData
} from '@/api/behaviorAuditing/network/browserFileStrategy'
import { validatePolicy } from '@/utils/validate'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter, timeDateInfoFormatter } from '@/utils/formatter'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import BackupRuleContent from '@/views/system/baseData/backupRule/backupRuleContent'
import ImportTable from '@/views/system/baseData/groupImportList/importTable';
import RelUrlDlg from '@/views/behaviorAuditing/network/browserFileStrategy/relUrlDlg';
import {
  createUrl, updateUrl, getTreeNode, listUrlByGroupId, getByIds,
  createUrlGroup, updateUrlGroup, deleteUrlGroup, getUrlGroupByName,
  deleteGroupAndData, moveGroupToOther, countUrlByGroupId, deleteUrl, getUrlList
} from '@/api/system/baseData/urlLibrary';
import EditGroupDlg from '@/views/common/editGroupDlg';
import ImportTableDlg from '@/views/system/baseData/groupImportList/importTableDlg';

export default {
  name: 'BrowserFileStrategy',
  components: { ImportTableDlg, EditGroupDlg, RelUrlDlg, ImportTable, ResponseContent, ImportStg, BackupRuleContent },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 63,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'timeId', label: 'effectTimeDate', width: '120', formatter: timeDateInfoFormatter, type: 'showDetail', searchType: 'timeInfo', searchParam: 'timeId' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: { },
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        fileBackUpLimit: 20,
        entityType: '',
        entityId: undefined,
        // isBackup: 0,  // 是否备份 1-备份 0-不备份
        isAlarm: 0, // 明文文件触发响应规则
        isEncAlarm: 0, // 密文文件触发响应规则
        ruleId: undefined, // 明文文件响应规则id
        encRuleId: undefined, // 密文文件响应规则id
        isLimit: 0, // 1-限制外发 0-不限制外发
        approvalType: 0, // 位与 审批外发 1-明文文件禁止外发, 2-密文文件禁止外发, 4-允许明文文件外发, 8-允许密文文件外发
        isBackupRule: 0, // 是否配置备份过滤规则
        backupRuleId: undefined, // 备份过滤规则id
        monitorWay: 1, // 检测规则类型，受控范围  2 - 所有网页， 1 - 包括以下网页， 0 - 除以下网页
        monitorConfigWay: 1,
        relUrls: [], // 记录设置的检测规则关联的Url的信息（网址信息库）  对象类型： { id: '', matchKind: '' 枚举值 0 - 模糊匹配； 1 - 完全匹配 }
        relConfigUrls: [], // 管控设置的检测规则关联的Url的信息（网址信息库）  对象类型： { id: '', matchKind: '' 枚举值 0 - 模糊匹配； 1 - 完全匹配 }
        recordDetectionRule: 0, // 审计检测规则 0 - 除列表之外   1 - 包括列表之内   当 === 0 时，列表为空，则是所有网页
        detectionRule: 0, // 管控检测规则 0 - 除列表之外   1 - 包括列表之内   当 === 0 时，列表为空，则是所有网页
        recordLog: 1, // 是否记录日志
        needBackup: 1 // 是否备份
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.browserFileStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.browserFileStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileBackUpLimit: [
          { validator: this.fileBackUpLimitValidator, trigger: 'blur' }
        ]
      },
      elgColModel: [
        { prop: 'name', label: 'websiteName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '110', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'websiteUrl', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '110', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: (row) => this.relUrlDlg().updateUrlDlg(row) }
          ]
        }
      ],
      importColModel: [
        { prop: 'name', label: 'websiteName', width: '150', sort: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: true, formatter: this.groupFormatter },
        { prop: 'address', label: 'websiteUrl', width: '150', sort: true },
        { prop: 'remark', label: 'remark', width: '200', sort: true },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdateImport }
          ]
        }
      ],
      urlDeleteable: false,
      urlConfigDeleteable: false,
      importTableLoading: false,
      importConfigTableLoading: false,
      tempUrlElgData: [],
      tempUrlConfigElgData: [],
      urlTreeNode: [],
      submitting: false,
      propRuleId: undefined,
      encPropRuleId: undefined, // 密文文件响应规则id
      approvalTypeList: [], // 审批外发
      hasEncPermision: true,  // 是包含加解密模块
      // 触发明文文件响应规则
      ruleTypeName: this.$t('pages.burnMsg10'),
      // 触发密文文件响应规则
      encRuleTypeName: this.$t('pages.burnMsg11'),
      validRuleId: true,
      validEncRuleId: true,
      ruleDisable: false, // 当明文执行规则没有配置时,明文响应规则要置灰
      encRuleDisable: false, // 当密文执行规则没有配置时,明文响应规则要置灰
      isBackupRule: 0, // 是否配置备份过滤规则
      backupRuleId: undefined, // 备份过滤规则id
      activeName: 'control'   // 策略tabs页
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    }
  },
  watch: {
    'temp.relUrls': {
      handler(newVal, oldVal) {
        this.tempUrlElgData = newVal
      },
      deep: true
    },
    'temp.relConfigUrls': {
      handler(newVal, oldVal) {
        this.tempUrlConfigElgData = newVal
      },
      deep: true
    }
    // 'temp.sendType'(newValue, oldValue) {
    //   this.sendTypeChange(newValue)
    // }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 获取注册模块
    this.listModule()
    // 获取url分组数据
    this.listTreeNode()
  },
  activated() {
    this.listTreeNode().then(() => {
      if (this.relUrlDlg() && this.relUrlDlg().dialogVis) {
        const groupId = (this.relUrlDlg().urlTemp || {}).groupId
        if (groupId && !this.urlTreeNode.some(node => node.dataId == groupId)) {
          this.relUrlDlg().setGroupId('')
        }
      }
    })
    this.formatRelUrls()
    this.formatRelConfigUrls()
  },
  methods: {
    createUrl,
    updateUrl,
    getTreeNode,
    listUrlByGroupId,
    createUrlGroup,
    updateUrlGroup,
    deleteUrlGroup,
    getUrlGroupByName,
    deleteGroupAndData,
    moveGroupToOther,
    countUrlByGroupId,
    deleteUrl,
    getUrlList,
    async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = resp.data
        this.ruleTypeName = this.hasEncPermision ? this.$t('pages.burnMsg10') : this.$t('pages.triggerResponseRule')
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    // sendTypeChange: function(value) {
    //   this.temp.sendType = value
    //   if (value === 1 || value === '1') {
    //     // 允许发送文件的话，初始化为不限制
    //     this.temp.disable = false
    //     this.temp.fileSendLimit = 0
    //   } else {
    //     this.temp.disable = true
    //     this.temp.fileSendLimit = -1
    //   }
    // },
    number() {
      if (isNaN(this.temp.fileSendLimit)) {
        this.temp.fileSendLimit = this.temp.fileSendLimit.replace(/[^\.\d]/g, '')
        this.temp.fileSendLimit = this.temp.fileSendLimit.replace('.', '')
      }
    },
    nullToZero(event) {
      if (this.temp.fileSendLimit === '') {
        this.temp.fileSendLimit = 0
      }
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getDataPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.temp.relUrls.splice(0)
      this.temp.relConfigUrls.splice(0)
      this.propRuleId = undefined
      this.propRuleId = undefined
      this.encPropRuleId = undefined
      this.approvalTypeList.splice(0)
      this.validRuleId = true
      this.validEncRuleId = true
      this.ruleDisable = false
      this.encRuleDisable = false
      this.activeName = 'control'
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['urlConfigTable'].clearSearchInfo();
        this.$refs['urlTable'].clearSearchInfo();
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      // 兼容旧策略： 旧策略不存在网页上传文件记录设置，但是默认是记录审计日志的
      this.resetTemp()
      this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      this.formatRelUrls()
      this.formatRelConfigUrls()
      // 统一样式，当不勾选备份复选框时，备份的阀值设置显示为默认值
      // 旧策略默认审计且fileBackUpLimit为空时，是不备份的
      if (row.recordLog === undefined) {
        this.temp.recordLog = 1
      }
      if (row.fileBackUpLimit > 0) {
        this.temp.needBackup = 1
      }
      if (!this.temp.needBackup || row.fileBackUpLimit === 0) {
        this.temp.fileBackUpLimit = 20
      }
      if (this.temp.approvalType > 0) {
        this.approvalTypeList = this.numToList(this.temp.approvalType, 4)
        // 当勾选的有允许文件外发审批时，approvalType是没有保存禁止外发的值的，所以这边要做下处理
        if (this.approvalTypeList.indexOf(4) > -1) {
          this.approvalTypeList.push(1)
        }
        if (this.approvalTypeList.indexOf(8) > -1) {
          this.approvalTypeList.push(2)
        }
      }
      this.propRuleId = row.ruleId
      this.encPropRuleId = row.encRuleId
      this.dialogStatus = 'update'
      // 当明文执行规则没配置时,无法配置明文响应规则
      if (this.approvalTypeList.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (this.approvalTypeList.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
      this.dialogFormVisible = true
      this.$nextTick(() => {
        // 多此一举再设置一次是因为为了修复监听属性sendType改变fileSendLimit的值
        this.$set(this.temp, 'fileSendLimit', row.fileSendLimit)
        this.$refs['urlConfigTable'].clearSearchInfo();
        this.$refs['urlTable'].clearSearchInfo();
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleBackupRule() {
      this.isBackupRule = this.temp.isBackupRule
      this.backupRuleId = this.temp.backupRuleId
      this.$refs.backupRuleContent.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      const formData = this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId) {
          createData(formData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      const formData = this.formatFormData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validRuleId && this.validEncRuleId) {
          updateData(formData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getDataByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    fileBackUpLimitValidator(rule, value, callback) {
      if (this.temp.needBackup === 1) {
        if (!value) {
          return callback(new Error(this.$t('pages.required1')))
        } else if (value > 10240) {
          return callback(new Error(this.$t('pages.blueTooth_Msg4')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyFormatter: function(row, data) {
      const msgArr = []
      if (row.detectionRule) {
        msgArr.push(this.$t('pages.browserUploadRule3'))
      } else {
        msgArr.push(this.$t('pages.browserUploadRule4'))
      }
      if (row.approvalType >= 0) {
        const approvalList = this.numToList(row.approvalType, 4)
        if (this.hasEncPermision) {
          if (approvalList.indexOf(1) > -1) {
            // 禁止明文文件传输
            msgArr.push(this.$t('pages.burnMsg14'))
          }
          if (approvalList.indexOf(2) > -1) {
            // 禁止密文文件传输
            msgArr.push(this.$t('pages.burnMsg15'))
          }
          if (approvalList.indexOf(4) > -1) {
            // 允许明文文件外发审批
            msgArr.push(this.$t('pages.burnMsg6'))
          }
          if (approvalList.indexOf(8) > -1) {
            // 允许密文文件外发审批
            msgArr.push(this.$t('pages.burnMsg9'))
          }
        }
      }
      if (this.hasEncPermision) {
        if (row.ruleId) {
          // 触发明文文件响应规则
          msgArr.push(this.$t('pages.burnMsg10'))
        }
        if (row.encRuleId) {
          // 触发密文文件响应规则
          msgArr.push(this.$t('pages.burnMsg11'))
        }
      } else {
        if (row.encRuleId) {
          // 触发响应规则
          msgArr.push(this.$t('pages.triggerResponseRule'))
        }
      }
      const recordMsgArr = []
      if (row.recordDetectionRule) {
        recordMsgArr.push(this.$t('pages.browserUploadRule3'))
      } else {
        recordMsgArr.push(this.$t('pages.browserUploadRule4'))
      }
      if (row.recordLog) {
        recordMsgArr.push(this.$t('pages.recordBrowserUploadLog'))
      }
      if (row.fileBackUpLimit) {
        // 文件备份限制{row.fileBackUpLimit}MB
        recordMsgArr.push(this.$t('pages.burnMsg13', { BackupSize: row.fileBackUpLimit }))
      }
      return this.$t('pages.telnetStrategyMsg', { controlSettings: msgArr.join(','), recordSettings: recordMsgArr.join(',') })
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    formatFormData() {
      const formData = JSON.parse(JSON.stringify(this.temp))
      formData.ruleId = this.propRuleId
      formData.encRuleId = this.encPropRuleId
      formData.approvalType = this.getSum(this.approvalTypeList)
      // 当勾选了允许审批外发时，要去除掉禁止的值比如禁止审批外发为1允许审批外发为4两个都勾选时approvalType只保存4
      formData.approvalType = this.getSum(this.approvalTypeList)
      if (this.approvalTypeList.indexOf(4) > -1) {
        formData.approvalType = formData.approvalType - 1
      }
      if (this.approvalTypeList.indexOf(8) > -1) {
        formData.approvalType = formData.approvalType - 2
      }
      // 处理禁止传输的值,兼容旧版本
      if (this.hasEncPermision) {
        // 有加解密模块当禁止明文传输跟禁止密文传输都禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(2) > -1 && this.approvalTypeList.indexOf(4) == -1 && this.approvalTypeList.indexOf(8) == -1) {
          formData.isLimit = 1
        } else {
          formData.isLimit = 0
        }
      } else {
        // 无加解密模块，当禁止明文传输为禁止时为禁止
        if (this.approvalTypeList.indexOf(1) > -1 && this.approvalTypeList.indexOf(4) == -1) {
          formData.isLimit = 1
        } else {
          formData.isLimit = 0
        }
      }
      this.validRuleId = true
      this.validEncRuleId = true
      if (formData.isAlarm === 1 && !formData.ruleId) {
        this.validRuleId = false
      }
      if (formData.isEncAlarm === 1 && !formData.encRuleId) {
        this.validEncRuleId = false
      }
      if (formData.recordDetectionRule === 0 && formData.relUrls.length === 0) {
        formData.monitorWay = 2
      } else {
        formData.monitorWay = formData.recordDetectionRule
      }
      if (formData.detectionRule === 0 && formData.relConfigUrls.length === 0) {
        formData.monitorConfigWay = 2
      } else {
        formData.monitorConfigWay = formData.detectionRule
      }
      if (formData.needBackup === 0) {
        formData.fileBackUpLimit = 0
      }
      return formData
    },
    getRuleId(value) {
      this.propRuleId = value
    },
    getEncRuleId(value) {
      this.encPropRuleId = value
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    getEncRuleIsCheck(value) {
      this.temp.isEncAlarm = value
    },
    handleApprovalTypeChange(value) {
      if (value.indexOf(4) > -1 && value.indexOf(1) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(4), 1)
      }
      if (value.indexOf(8) > -1 && value.indexOf(2) === -1) {
        this.approvalTypeList.splice(this.approvalTypeList.indexOf(8), 1)
      }
      // 勾选了执行规则，响应规则才能勾选
      if (value.indexOf(1) > -1) {
        this.ruleDisable = true
      } else {
        this.ruleDisable = false
        this.temp.isAlarm = 0
        this.propRuleId = undefined
      }
      if (value.indexOf(2) > -1) {
        this.encRuleDisable = true
      } else {
        this.encRuleDisable = false
        this.temp.isEncAlarm = 0
        this.encPropRuleId = undefined
      }
    },
    importSuccess() {
      this.handleFilter()
    },
    setBackupRule(backupRuleId, checkRule) {
      this.temp.backupRuleId = backupRuleId
      this.temp.isBackupRule = checkRule
    },
    //  获取Url分组数据
    listTreeNode() {
      return getTreeNode().then(res => {
        this.urlTreeNode = res.data || []
      })
    },
    relUrlDlg() {
      return this.$refs['relUrlDlg']
    },
    createGroupDlg() {
      return this.$refs['createGroupDlg']
    },
    deleteUrlData() {
      if (this.activeName == 'control') {
        const toDeleteIds = this.$refs['urlConfigTable'].getSelectedIds() || []
        this.temp.relConfigUrls = this.$refs['urlConfigTable'].deleteTableData(this.temp.relConfigUrls, toDeleteIds)
      } else if (this.activeName == 'record') {
        const toDeleteIds = this.$refs['urlTable'].getSelectedIds() || []
        this.temp.relUrls = this.$refs['urlTable'].deleteTableData(this.temp.relUrls, toDeleteIds)
      }
    },
    //  弹窗列表查询
    handleSearch(searchInfo) {
      searchInfo = searchInfo || ''
      searchInfo = searchInfo.trim().toLowerCase()
      if (this.activeName == 'control') {
        this.$refs['urlConfigTable'].$refs['table'].exec
        if (searchInfo === '') {
          this.tempUrlConfigElgData = this.temp.relConfigUrls
        } else {
          this.tempUrlConfigElgData = this.temp.relConfigUrls.filter(item => {
            return (item.name && item.name.toLowerCase().indexOf(searchInfo) !== -1) ||
              (item.address && item.address.toLowerCase().indexOf(searchInfo) !== -1) ||
              (item.groupId && this.getGroupNameByDataId(this.urlTreeNode, item.groupId).toLowerCase().indexOf(searchInfo) !== -1)
          })
        }
      } else if (this.activeName == 'record') {
        this.$refs['urlTable'].$refs['table'].exec
        if (searchInfo === '') {
          this.tempUrlElgData = this.temp.relUrls
        } else {
          this.tempUrlElgData = this.temp.relUrls.filter(item => {
            return (item.name && item.name.toLowerCase().indexOf(searchInfo) !== -1) ||
              (item.address && item.address.toLowerCase().indexOf(searchInfo) !== -1) ||
              (item.groupId && this.getGroupNameByDataId(this.urlTreeNode, item.groupId).toLowerCase().indexOf(searchInfo) !== -1)
          })
        }
      }
    },
    updateRelInfo(data, status, beInImportDlg) {
      if (beInImportDlg) {
        this.$refs['urlImportTable'].justRefreshTableData()
      }
      if (this.activeName == 'control') {
        if (data && data.id) {
          const relConfigUrls = this.temp.relConfigUrls
          if (status == 'update') {
            const index = relConfigUrls.findIndex(url => url.id == data.id)
            if (index >= 0) { relConfigUrls.splice(index, 1, data) }
          } else {
            if (!beInImportDlg) {
              relConfigUrls.push(data)
            }
          }
        }
      } else if (this.activeName == 'record') {
        if (data && data.id) {
          const relUrls = this.temp.relUrls
          if (status == 'update') {
            const index = relUrls.findIndex(url => url.id == data.id)
            if (index >= 0) { relUrls.splice(index, 1, data) }
          } else {
            if (!beInImportDlg) {
              relUrls.push(data)
            }
          }
        }
      }
    },
    handleUrlImport() {
      this.$nextTick(() => {
        this.$refs['urlImportTable'].show()
      })
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.urlTreeNode, data)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    importSubmitEnd(idList) {
      if (this.activeName == 'control') {
        const tempSet = new Set(this.temp.relConfigUrls.map(obj => obj.id))
        const ids = (idList || []).filter(id => !tempSet.has(id)).join(',')
        if (ids.length > 0) {
          getByIds({ ids }).then(res => {
            (res.data || []).forEach(data => this.temp.relConfigUrls.push(Object.assign(data, { matchKind: 0 })))
          })
        }
      } else if (this.activeName == 'record') {
        const tempSet = new Set(this.temp.relUrls.map(obj => obj.id))
        const ids = (idList || []).filter(id => !tempSet.has(id)).join(',')
        if (ids.length > 0) {
          getByIds({ ids }).then(res => {
            (res.data || []).forEach(data => this.temp.relUrls.push(Object.assign(data, { matchKind: 0 })))
          })
        }
      }
    },
    importDeleteEnd(idList) {
      if (this.activeName == 'control') {
        const tempSet = new Set((idList || []))
        const relConfigUrls = this.temp.relConfigUrls
        for (let i = relConfigUrls.length - 1; i >= 0; i--) {
          const relUrl = relConfigUrls[i]
          if (tempSet.has(relUrl.id)) {
            relConfigUrls.splice(i, 1)
          }
        }
      } else if (this.activeName == 'record') {
        const tempSet = new Set((idList || []))
        const relUrls = this.temp.relUrls
        for (let i = relUrls.length - 1; i >= 0; i--) {
          const relUrl = relUrls[i]
          if (tempSet.has(relUrl.id)) {
            relUrls.splice(i, 1)
          }
        }
      }
    },
    changeGroupAfter() {
      this.listTreeNode()
    },
    // 导入窗口新增网址; 参数1：选择的分组Id
    handleCreateElg(selectGroupId, flag) {
      this.relUrlDlg().handleImportDlg({ groupId: selectGroupId }, 'create')
    },
    handleUpdateImport(row) {
      this.relUrlDlg().handleImportDlg(row, 'update')
    },
    // 将信息库的信息赋值到列表中，同时会去除掉库分组没有的数据
    formatRelUrls() {
      const relUrls = this.temp.relUrls
      const ids = (relUrls || []).map(item => item.id).join(',')
      if (ids.length > 0) {
        this.importTableLoading = true
        getByIds({ ids }).then(res => {
          const tempMap = (res.data || []).reduce((acc, curr) => {
            acc[curr.id] = curr;
            return acc;
          }, {})
          for (let i = relUrls.length - 1; i >= 0; i--) {
            const relUrl = relUrls[i]
            if (!relUrl.id) {
              relUrls.splice(i, 1)
            } else {
              const tempItem = tempMap[relUrl.id]
              if (tempItem) {
                Object.assign(relUrl, tempItem)
              } else {
                relUrls.splice(i, 1)
              }
            }
          }
          relUrls.splice(0, 0)
          this.importTableLoading = false
        }).catch(() => {
          relUrls.splice(0)
          this.importTableLoading = false
        })
      } else {
        relUrls.splice(0)
      }
    },
    formatRelConfigUrls() {
      const relConfigUrls = this.temp.relConfigUrls
      const ids = (relConfigUrls || []).map(item => item.id).join(',')
      if (ids.length > 0) {
        this.importConfigTableLoading = true
        getByIds({ ids }).then(res => {
          console.log('ddd', res);
          const tempMap = (res.data || []).reduce((acc, curr) => {
            acc[curr.id] = curr;
            return acc;
          }, {})
          for (let i = relConfigUrls.length - 1; i >= 0; i--) {
            const relUrl = relConfigUrls[i]
            if (!relUrl.id) {
              relConfigUrls.splice(i, 1)
            } else {
              const tempItem = tempMap[relUrl.id]
              if (tempItem) {
                Object.assign(relUrl, tempItem)
              } else {
                relConfigUrls.splice(i, 1)
              }
            }
          }
          relConfigUrls.splice(0, 0)
          this.importConfigTableLoading = false
        }).catch(() => {
          relConfigUrls.splice(0)
          this.importConfigTableLoading = false
        })
      } else {
        relConfigUrls.splice(0)
      }
    },
    recordLogChange(val) {
      if (!val && this.temp.needBackup === 1) {
        this.temp.needBackup = 0
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    },
    needBackupChange(val) {
      if (!val) {
        this.$refs['dataForm'].clearValidate('fileBackUpLimit')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 防止默认style 造成网页上传文件记录设置中响应规则看不到的情况
 .el-divider.el-divider--horizontal {
   margin: 20px 0 15px;
 }
 >>>.response-rule .el-form-item__error {
   padding-left: 160px;
 }
</style>
