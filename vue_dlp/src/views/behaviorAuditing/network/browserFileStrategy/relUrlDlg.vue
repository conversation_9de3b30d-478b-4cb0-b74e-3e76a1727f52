<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[urlDialogStatus]"
    :visible.sync="dialogVis"
    width="600px"
  >
    <Form
      ref="urlDataForm"
      :rules="urlRules"
      :model="urlTemp"
      label-position="right"
      label-width="100px"
      style="width: 500px; margin-left:20px;"
    >
      <FormItem :label="$t('pages.websiteName')" prop="name">
        <el-input v-model="urlTemp.name" v-trim :maxlength="60"/>
      </FormItem>
      <FormItem :label="$t('pages.websiteUrl')" prop="address">
        <el-input v-model="urlTemp.address" v-trim :maxlength="60" :placeholder="$t('pages.urlLibrary_text1')"/>
      </FormItem>
      <FormItem :label="$t('pages.groupType')" prop="groupId">
        <el-row>
          <el-col :span="!beInImportDlg ? 21 : 24">
            <el-select v-model="urlTemp.groupId" filterable :placeholder="$t('text.select')">
              <el-option v-for="item in urlTreeNode" :key="item.id" :label="item.label" :value="item.dataId"/>
            </el-select>
          </el-col>
          <el-col v-show="!beInImportDlg" style="padding-top:1px" :span="3">
            <el-button style="padding-top: 1px; margin-bottom: 0;" class="editBtn" @click="handleTypeCreate">
              <svg-icon icon-class="add" />
            </el-button>
          </el-col>
        </el-row>
      </FormItem>
      <FormItem :label="$t('pages.remark')" prop="remark">
        <el-input v-model="urlTemp.remark" type="textarea" rows="3" resize="none" maxlength="100" show-word-limit/>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="dlgSubmitting" @click="urlDialogStatus==='createUrl'?createUrlData(): urlDialogStatus==='updateUrl'? updateUrlData() : {}">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogVis = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createUrl, getUrlByAdress, updateUrl } from '@/api/system/baseData/urlLibrary';

export default {
  name: 'RelUrlDlg',
  props: {
    urlTreeNode: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      dialogVis: false,
      urlTemp: {},
      defaultTemp: {
        id: '',
        name: '',
        address: '',
        groupId: '',
        remark: '',
        matchKind: 0
      },
      beInImportDlg: false,
      urlRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('pages.validateMsg_enterUrl'), trigger: 'blur' },
          { validator: this.addressValidator, trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: this.$t('valid.requireGroupName'), trigger: 'blur' }
        ]
      },
      textMap: {
        createUrl: this.i18nConcatText(this.$t('table.websiteUrl'), 'create'),
        updateUrl: this.i18nConcatText(this.$t('table.websiteUrl'), 'update')
      },
      urlDialogStatus: 'createUrl',
      dlgSubmitting: false
    }
  },
  methods: {
    resetTemp() {
      this.urlTemp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    addressValidator(rule, value, callback) {
      getUrlByAdress({ url: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id != this.urlTemp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    createUrlDlg() {
      this.dialogVis = true
      this.beInImportDlg = false
      this.urlDialogStatus = 'createUrl'
      this.resetTemp()
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
    },
    updateUrlDlg(row) {
      this.dialogVis = true
      this.beInImportDlg = false
      this.urlDialogStatus = 'updateUrl'
      this.urlTemp = JSON.parse(JSON.stringify(row))
      this.urlTemp.groupId = this.urlTemp.groupId ? this.urlTemp.groupId + '' : ''
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
    },
    handleImportDlg(row, status) {
      this.dialogVis = true
      this.beInImportDlg = true
      this.urlDialogStatus = status === 'create' ? 'createUrl' : 'updateUrl'
      this.urlTemp = JSON.parse(JSON.stringify(row))
      this.urlTemp.groupId = this.urlTemp.groupId ? this.urlTemp.groupId + '' : ''
      this.$nextTick(() => {
        this.$refs['urlDataForm'].clearValidate()
      })
    },
    handleTypeCreate() {
      this.$emit('createGroup')
    },
    createUrlData() {
      this.dlgSubmitting = true
      this.$refs['urlDataForm'].validate(valid => {
        if (valid) {
          createUrl(this.urlTemp).then(respond => {
            this.dlgSubmitting = false
            this.$emit('updateInfo', Object.assign({}, respond.data, { matchKind: this.urlTemp.matchKind }), 'create', this.beInImportDlg)
            this.dialogVis = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    updateUrlData() {
      this.dlgSubmitting = true
      this.$refs['urlDataForm'].validate(valid => {
        if (valid) {
          updateUrl(this.urlTemp).then(respond => {
            this.$emit('updateInfo', Object.assign({}, respond.data, { matchKind: this.urlTemp.matchKind }), 'update', this.beInImportDlg)
            this.dlgSubmitting = false
            this.dialogVis = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.dlgSubmitting = false
          })
        } else {
          this.dlgSubmitting = false
        }
      })
    },
    setGroupId(groupId) {
      this.urlTemp.groupId = groupId
    }
  }
}
</script>

<style scoped>

</style>
