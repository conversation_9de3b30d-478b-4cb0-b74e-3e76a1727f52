<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange" />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        {{ $t('pages.timeQuery') }}：
        <el-date-picker v-model="query.startDate" style="width: 200px" :clearable="false" type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsStart" :placeholder="$t('pages.startDate')"></el-date-picker>
        -->
        <el-date-picker v-model="query.endDate" style="width: 200px" :clearable="false" type="datetime" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsEnd" :placeholder="$t('pages.endDate')"></el-date-picker>
        {{ $t('pages.chatAllLog_Msg7') }}
        <el-select v-model="query.chatType" style="width: 150px">
          <el-option v-for="item in chatToolOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        {{ $t('pages.chatAllLog_Msg2') }}<el-input v-model="query.keyword" clearable style="width: 180px;" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
        <!--<el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>-->
      </div>

      <div style="height: calc(100% - 40px);">
        <div class="tree-container">
          <tree-menu ref="sessionTree" :data="treeData" :icon-option="iconOption" :is-filter="false" @node-click="treeNodeClick" />
        </div>
        <div class="table-container">
          <div style="border: 1px solid #666;height: 100%;overflow-y: auto">
            <div v-for="(msg, index) in msgList" :key="index" style="padding-top: 10px;padding-left: 5px">
              <div v-html="formatStr(msg.msgText)"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSessionTree, getMsgList/*, deleteLog*/ } from '@/api/behaviorAuditing/network/chatTextLog'
import moment from 'moment'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'

export default {
  name: 'ChatTextLog',
  data() {
    return {
      pickerOptionsStart: {
        disabledDate: time => {
          const endDateVal = this.query.endDate
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime()
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.query.startDate
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime() - 1 * 24 * 60 * 60 * 1000)
          }
        }
      },
      query: { // 查询条件
        objectType: undefined,
        objectId: undefined,
        startDate: '',
        endDate: '',
        keyword: '',
        chatType: null,
        chatSessionInfo: '',
        searchReport: 1
      },
      chatToolOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.qq'), value: 3 },
        { label: this.$t('pages.wechat'), value: 23 },
        { label: this.$t('pages.dingTalk'), value: 25 },
        { label: this.$t('pages.enterpriseWeChat'), value: 27 },
        { label: this.$t('pages.aliTalk'), value: 13 },
        { label: this.$t('pages.feiQ'), value: 20 }
        /*, { label: 'QQ企业版', value: 26 },
        { label: 'MSN', value: 1 },
        { label: 'ICQ', value: 2 },
        { label: '网易泡泡', value: 4 },
        { label: 'YMsg', value: 5 },
        { label: '新浪UC', value: 6 },
        { label: 'Skype', value: 10 },
        { label: '贸易通', value: 11 },
        { label: 'E话通', value: 7 },
        { label: 'PP点点通', value: 8 },
        { label: 'MSN网络', value: 80 },
        { label: 'AOL', value: 12 },

        { label: 'GoogleTalk', value: 14 },
        { label: '飞信', value: 16 },
        { label: 'UUCall', value: 17 },
        { label: '飞鸽', value: 18 },
        { label: 'MsnLite', value: 21 },
        { label: 'IMO', value: 22 },
        { label: 'Rtx', value: 24 },
        { label: '千牛', value: 28 }*/
      ],
      treeData: [],
      msgList: [],
      showTree: true,
      deleteable: false,
      iconOption: { 'telminate': 'terminal' }
    }
  },
  computed: {
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    }
  },
  created() {
    const date = new Date()
    this.query.startDate = (moment(date).format('YYYY-MM-DD')) + ' 0:00:00'
    this.query.endDate = (moment(date).format('YYYY-MM-DD')) + ' 23:59:59'
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  methods: {
    formatStr(str) {
      if (str != null && str != undefined) {
        str = str.trim()
        str = this.html2Escape(str)
        str = str.replace(/\n/g, '<br/>')
        return str
      }
    },
    // 点击会话节点加载聊天信息
    treeNodeClick: function(data, node, el) {
      if (data.type === 'session') {
        this.query.chatSessionInfo = data.label
        this.loadMsgList(data)
      }
    },
    // 加载会话信息树
    loadSessionTree: function() {
      const temp = {
        objectType: this.query.objectType,
        objectId: this.query.objectId,
        startDate: this.query.startDate,
        endDate: this.query.endDate,
        keyword: this.query.keyword,
        chatType: this.query.chatType,
        searchReport: this.query.searchReport
      }
      getSessionTree(temp).then(res => {
        this.treeData = res.data
        this.msgList = []
      })
    },
    // 加载聊天信息
    loadMsgList: function(data) {
      const temp = {
        objectType: 1,
        objectId: data.oriData,
        startDate: this.query.startDate,
        endDate: this.query.endDate,
        keyword: this.query.keyword,
        chatType: this.query.chatType,
        chatSessionInfo: this.query.chatSessionInfo,
        searchReport: this.query.searchReport
      }
      getMsgList(temp).then(res => {
        this.msgList = res.data
      })
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.deleteable = true
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
        // 选中终端或者租，加载这个终端或组下的所有会话记录
        this.loadSessionTree()
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.treeData = []
        this.msgList = []
        this.deleteable = true
      }
    },
    handleFilter() {
      const queryTemp = {
        objectType: this.query.objectType,
        objectId: this.query.objectId,
        startDate: this.query.startDate,
        endDate: this.query.endDate,
        keyword: this.query.keyword,
        chatType: this.query.chatType,
        searchReport: this.query.searchReport
      }
      let months = ''
      if (this.query.searchReport != 1) {
        queryTemp.startDate = moment(queryTemp.startDate).format('YYYY-MM-DD')
        queryTemp.endDate = moment(queryTemp.endDate).format('YYYY-MM-DD')
        backupLogList(queryTemp).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.loadSessionTree()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    }
    // handleDelete() {
    //   const node = this.strategyTargetTree.$refs.terminalTree[0].getCurrentNode()
    //   const msg = `确认要删除(${node.label})<${moment(this.query.startDate).format('YYYY-MM-DD')}至${moment(this.query.endDate).format('YYYY-MM-DD')}>的聊天记录吗?`
    //   this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
    //     const temp = {
    //       objectType: this.query.objectType,
    //       objectId: this.query.objectId,
    //       startDate: this.query.startDate,
    //       endDate: this.query.endDate,
    //       keyword: this.query.keyword,
    //       chatType: this.query.chatType
    //     }
    //     deleteLog(temp).then(respond => {
    //       this.loadSessionTree()
    //       this.$notify({
    //         title: this.$t('text.success'),
    //         message: this.$t('text.deleteSuccess'),
    //         type: 'success',
    //         duration: 2000
    //       })
    //     })
    //   }).catch(() => {})
    // }
  }
}
</script>
