<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('pages.fileName1') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.actionType" :value="query.actionType">
          <span>{{ $t('table.action') }}：</span>
          <el-select v-model="query.actionType" clearable style="width: 150px">
            <el-option v-for="(value, key) in actionTypes" :key="key" :label="value" :value="key"/>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'545'" :request="handleExport"/>
        <audit-file-downloader
          ref="auditFileDownloader"
          slot="append"
          v-permission="'543'"
          :button="$t('table.download')"
          :selection="selection"
          :before-download="beforeDownload"
        />
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'546'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'uploadTime' }"
        :multi-select="true"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.aiUploadFileLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.uploadTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.aiName')">
            {{ aiNameOpts[rowDetail.aiName] }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.winTitle')">
            {{ rowDetail.winTitle }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.processName')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileName1')">
            <el-button
              v-permission="'536'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!536'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.maxFileSize1')">
            {{ rowDetail.fileSize }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.filePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.uploadUrl')">
            <template v-if="!rowDetail.uploadUrl || !rowDetail.uploadUrl.startsWith('http')">
              {{ rowDetail.uploadUrl }}
            </template>
            <a v-else :href="rowDetail.uploadUrl" style="color: #68a8d0;" target="_blank">{{ rowDetail.uploadUrl }}</a>
          </el-descriptions-item>
          <!--<el-descriptions-item span="2" :label="$t('关联文件GUID')">
            {{ rowDetail.refFileGuid }}
          </el-descriptions-item>-->
          <el-descriptions-item span="2" :label="$t('table.action')">
            {{ actionTypes[rowDetail.actionType] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportLog } from '@/api/behaviorAuditing/network/aiUploadFileLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { getAiNameDict } from '@/utils/dictionary'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'AiUploadFileLog',
  data() {
    return {
      colModel: [
        { prop: 'uploadTime', label: 'time', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'winTitle', label: 'winTitle', width: '200' },
        { prop: 'processName', label: 'processName', width: '150' },
        { prop: 'aiName', label: 'aiName', width: '150', formatter: (row, data) => this.aiNameOpts[data] },
        { prop: 'fileName', label: 'fileName1', width: '150' },
        { prop: 'fileSize', label: 'maxFileSize1', width: '150' },
        { prop: 'filePath', label: 'filePath', width: '200' },
        { prop: 'uploadUrl', label: 'uploadUrl', width: '200', formatter: this.urlLink },
        { prop: 'actionType', label: 'action', width: '100', formatter: (row, data) => this.actionTypes[data] },
        { label: 'operate', type: 'button', fixedWidth: '130', fixed: 'right', hidden: !this.hasPermission('543|544'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, isShow: () => this.hasPermission('543') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('544') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        fileName: '',
        actionType: undefined,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList',
        hostGroupId: undefined
      },
      showTree: true,
      selection: [],
      tempTask: {},
      defaultTempTask: {
        backType: 33,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined,
      aiNameOpts: {},
      actionTypes: {
        0: this.$t('pages.allow'),
        1: this.$t('pages.forbid')
      }
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
    getAiNameDict().forEach(item => {
      this.aiNameOpts[item.value] = item.label
    })
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
    this.gridTable().execRowDataApi()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas || []
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportLog(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    urlLink(row, data) {
      if (!data || !data.startsWith('http')) {
        return data
      }
      return "<a style='color: #68A8D0' href = '" + data + "' target='_blank'>" + data + '</a>';
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '544', this.query.searchReport, undefined, 'timeStr')
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
