<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('pages.fileOrDirName') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 200px;"/>
        </SearchItem>
        <SearchItem model-key="query.fileOpType" :value="query.fileOpType">
          <span>{{ $t('pages.operateType') }}：</span>
          <el-select v-model="query.fileOpType" style="width: 150px;">
            <el-option :label="$t('pages.allLinux')" :value="null" />
            <el-option v-for="(val, key) in opTypes" :key="key" :label="val" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.fileType" :value="query.fileType">
          <span>{{ $t('pages.isFileFolder') }}：</span>
          <el-select v-model="query.fileType" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option v-for="(label, value) in fileFolderOptions" :key="value" :label="label" :value="value" />
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'348'" :request="handleExport"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" v-permission="'241'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'459'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :autoload="autoload"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.localFileShareLogDetails')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize2')">
            {{ rowDetail.fileSizeStr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.operateType')">
            {{ fileOpTypeFormatter(rowDetail, rowDetail.fileOpType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.isDir')">
            {{ fileTypeFormatter(rowDetail, rowDetail.fileType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileOrDirName')">
            <el-button
              v-permission="'241'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!241'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.filePath')">
            {{ rowDetail.fileFullPath }}
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/behaviorAuditing/network/localFileShareLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'LocalFileShareLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'fileName', label: 'fileOrDirName', width: '150' },
        { prop: 'fileFullPath', label: 'filePath', width: '150' },
        { prop: 'fileSizeStr', label: 'maxFileSize2', width: '150' },
        { prop: 'fileOpType', label: 'operateType', width: '150', formatter: this.fileOpTypeFormatter },
        { prop: 'fileType', label: 'isDir', width: '150', formatter: this.fileTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('241,295'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, isShow: () => this.hasPermission('241') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('295') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        fileName: null, // 文件名
        fileType: null, // 是否是文件夹
        fileOpType: null, // 操作类型
        isTimes: false,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      tempTask: {},
      defaultTempTask: {
        backType: 1,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      fileFolderOptions: {
        0: this.$t('pages.File'), 1: this.$t('pages.FileFolder')
      },
      opTypes: {
        1: this.$t('pages.fileCreate'),
        2: this.$t('pages.fileEdit'),
        4: this.$t('pages.fileRename')
      },
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  fileTypeFormatter: function(row, data) {
    if (data !== null && data !== '' && data !== undefined) {
      if (data == 1) {
        return this.$t('text.yes')
      } else {
        return this.$t('text.no')
      }
    }
    return ''
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    selectable(row, index) {
      return !this.downloadFormatter(row) || (this.$store.getters.auditingDeleteAble && this.hasPermission('459'))
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.fileName
      // // 根据localFilePath来判断分享的是否是文件夹
      // if (row.localFilePath != null && row.localFilePath != '') {
      //   if (row.localFilePath.indexOf('.zip') >= 0 && this.tempTask.fileName.indexOf('.zip') < 0) {
      //     this.tempTask.fileName = this.tempTask.fileName + '.zip'
      //   }
      // }
      // 根据file_type来判断分享的是文件还是文件夹
      if (row.fileType == 1) {
        this.tempTask.fileName = this.tempTask.fileName + '.zip'
      }
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    fileTypeFormatter: function(row, data) {
      if (data !== null && data !== '' && data !== undefined) {
        if (data == 1) {
          return this.$t('text.yes')
        } else {
          return this.$t('text.no')
        }
      }
      return ''
    },
    fileOpTypeFormatter: function(row, data) {
      if (data !== null && data !== '' && data !== undefined) {
        return this.opTypes[data]
      }
      return ''
    },
    filePathFormatter: function(row, data) {
      if (row.fileName != null && row.fileName != '') {
        if (row.fileName.lastIndexOf('\\') >= 0) {
          const filePath = row.fileName.substr(0, row.fileName.lastIndexOf('\\'))
          return filePath
        }
        return row.fileName
      }
      return ''
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '295', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 180px;
}
</style>
