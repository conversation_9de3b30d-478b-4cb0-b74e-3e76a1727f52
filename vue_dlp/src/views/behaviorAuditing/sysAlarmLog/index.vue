<template>
  <div class="app-container">
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        :tree-menu-btn="false"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <!--<SearchItem v-if="logType==2" model-key="query.eventType" :value="query.eventType">
          <span>{{ $t('pages.operateEvent') }}：</span>
          <el-select v-model="query.eventType" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option v-for="(label, value) in eventTypeOptions" :key="value" :label="label" :value="value" />
          </el-select>
        </SearchItem>-->
        <SearchItem model-key="query.bizType" :value="query.bizType">
          <span>{{ $t('table.alarmSource') }}：</span>
          <el-select v-model="query.bizType" clearable is-filter :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(val, key) in bizTypeOptions" :key="key" :label="val" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.alarmType" :value="query.alarmType">
          <span>{{ $t('table.treatmentMeasure') }}：</span>
          <el-select v-model="query.alarmType" clearable is-filter :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(item, index) in alarmLimitDict" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'399'" multi-dataset :request="handleExport"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'472'" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table
        ref="logList"
        :col-model="colModel"
        :default-sort="defaultSort"
        :row-data-api="rowDataApi"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      >
        <template v-slot:popoverContent="row">
          <div class="show-detail-panel">
            <el-descriptions class="margin-top" :column="1" size="" border :label-style="{ width: '120px' }" :content-style="{ minWidth: '280px', maxWidth: '580px' }">
              <el-descriptions-item :label="$t('table.treatmentMeasure')">
                <el-tooltip
                  v-for="(item,index) in iconAlarmLimitFormatter(row.detail)"
                  :key="index"
                  effect="dark"
                  placement="top-start"
                  style="margin: 0 2px"
                >
                  <template slot="content">
                    {{ item.title }}
                  </template>
                  <svg-icon :icon-class="item.class"/>
                </el-tooltip>
              </el-descriptions-item>
              <el-descriptions-item v-for="(item,index) in iconAlarmLimitFormatter(row.detail)" :key="index" :label="item.title + $t('pages.object')">
                <span v-if="row.detail.alarmObjectMap" class="desc-content" :title="row.detail.alarmObjectMap[item.value]">
                  {{ row.detail.alarmObjectMap[item.value] }}
                </span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </template>
      </grid-table>
    </div>

    <!--告警详情-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.alarmDetails')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div style="height: 400px;">
        <grid-table
          ref="detailTable"
          :height="360"
          :multi-select="false"
          :show-pager="false"
          :row-datas="rowDetail"
          :col-model="detailColModel"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 服务状态告警详情-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.eventDetails')"
      :visible.sync="descDetailVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.alarmTime')">
            {{ rowDetail2.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.alarmDevice')">
            {{ rowDetail2.alarmDevice }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.alarmSource')">
            {{ bizTypeFormatter(null, rowDetail2.bizType) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="rowDetail2.subBizType" :label="$t('layout.alarmType')">
            {{ getDictLabel(serviceTypeOpts, rowDetail2.subBizType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.treatmentMeasure')" :span="rowDetail2.subBizType ? 2 : 1">
            <el-tooltip
              v-for="(item,index) in iconAlarmLimitFormatter(rowDetail2)"
              :key="index"
              effect="dark"
              placement="top-start"
              style="margin: 0 2px"
            >
              <template slot="content">
                {{ item.title }}
              </template>
              <svg-icon :icon-class="item.class"/>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item v-for="(item,index) in iconAlarmLimitFormatter(rowDetail2)" :key="index" :label="item.title + $t('pages.object')" span="2">
            <span v-if="rowDetail2.alarmObjectMap" class="desc-content" :title="rowDetail2.alarmObjectMap[item.value]">
              {{ rowDetail2.alarmObjectMap[item.value] }}
            </span>
          </el-descriptions-item>
        </el-descriptions>
        <el-card :body-style="{'max-height': '320px', 'overflow-y': 'auto', 'padding': '10px 20px'}" class="box-card" style="max-height: 390px;">
          <div slot="header" class="clearfix" style="padding-left: 0;">
            <span>{{ $t('pages.alarmDetails') }}</span>
          </div>
          <p style="white-space: pre-wrap; line-height: 24px">{{ rowDetail2.alarmDesc }}</p>
        </el-card>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLogPage, exportSysAlarmLog, getAlarmDetail, deleteLog } from '@/api/behaviorAuditing/systemLog/sysAlarmLog'
import { getAlarmLimitDict, getDict, getDictLabel } from '@/utils/dictionary'
import { getModuleInfoByProdId } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'SysAlarmLog',
  props: {
    logType: {
      type: Number,
      default: 1  // 1系统日志，2终端操作日志
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'alarmTime', width: '120', sort: true },
        { prop: 'alarmDevice', label: 'alarmDevice', width: '100' },
        { prop: 'bizType', label: 'alarmSource', width: '100', formatter: this.bizTypeFormatter },
        { prop: 'alarmType', label: 'treatmentMeasure', width: '100', type: 'popover', placement: 'bottom', title: '', originData: true, formatter: this.treatmentMeasureFormatter },
        { prop: 'alarmDesc', label: 'alarmDesc', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('398'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      detailColModel: [
        { prop: 'createTime', label: 'logStartTime', width: '120', sort: true },
        { prop: 'bizType', label: 'alarmSource', width: '100', sort: true, formatter: this.bizTypeFormatter },
        { prop: 'alarmType', label: 'treatmentMeasure', sort: true, width: '100', iconFormatter: this.iconAlarmLimitFormatter, formatter: () => '' },
        { prop: 'alarmObject', label: 'alarmObject', width: '80', formatter: this.alarmObjectFormatter },
        { prop: 'alarmDesc', label: 'alarmDesc', width: '150' }
      ],
      defaultSort: { prop: 'createTime', order: 'descending' },
      bizTypeOptions: {
        3: this.$t('pages.msgType2'),
        4: this.$t('pages.syncThirdPartAlarm')
      },
      alarmTypeOptions: {
        0: this.$t('pages.popAlarm'),
        1: this.$t('pages.emailAlarm')
      },
      query: { // 查询条件
        page: 1,
        bizType: undefined,
        alarmType: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        id: undefined,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      alarmLimitDict: getAlarmLimitDict().filter(dict => dict.value === 4 || dict.value === 8),
      serviceTypeOpts: [
        { value: 5, label: this.$t('pages.serverProgramSpaceAlarm') },
        { value: 6, label: this.$t('pages.serverDataSpaceAlarm') },
        { value: 7, label: this.$t('pages.serverSpaceAlarm') },
        { value: 3, label: this.$t('pages.serverCpuAlarm') },
        { value: 4, label: this.$t('pages.serverMemoryAlarm') }
        // { typeId: 11, remark: this.$t('pages.serverOfflineAlarm') }
      ],
      iconOptions: {
        4: 'alarm-02',
        8: 'alarm-03'
      },
      showTree: true,
      deleteable: false,
      rowDetail: {},
      rowDetail2: {},
      dialogFormVisible: false,
      descDetailVisible: false,
      paramData: {}
    }
  },
  computed: {
    // 系统管理员列表
    sysUserList() {
      return this.$store.getters.sysUserList
    },
    gridTable() {
      return this.$refs['logList']
    }
  },
  watch: {
    '$store.state.commonData.notice.sysAlarmDetail'() {
      this.paramData = Object.assign({}, this.$store.getters.alarmMsg)
      this.query.id = this.$store.getters.alarmMsg.id
      this.$refs.searchToolbar.setDate(this.$store.getters.alarmMsg.createDate)
      const searchInfo = Object.assign({}, this.query, { unionSql: this.paramData.unionSql, bizType: this.paramData.bizType })
      this.gridTable.execRowDataApi(searchInfo)
    }
  },
  created() {
    this.initBizTypeOptions()
  },
  methods: {
    getDictLabel,
    async initBizTypeOptions() {
      await getModuleInfoByProdId(511).then(res => {
        res.data.forEach(moduleInfo => {
          if (moduleInfo.moduleId == 351) { // 移动终端权限
            this.$set(this.bizTypeOptions, 1, this.$t('pages.enhancesPlugAlarm'))
          }
          if (moduleInfo.moduleId == 13 || moduleInfo.moduleId == 113 || moduleInfo.moduleId == 213) { // 资产管理模块权限
            this.$set(this.bizTypeOptions, 2, this.$t('pages.softOrderAlarm'))
          }
        })
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      if (this.$route.params && this.$route.params.id) {
        this.paramData = Object.assign({}, this.$route.params)
      }
      if (this.$route.params.id && this.$route.params.createDate) {
        searchQuery.id = this.$route.params.id
        this.$refs.searchToolbar.setDate(this.$route.params.createDate)
        searchQuery.createDate = this.$route.params.createDate
        searchQuery.unionSql = this.$route.params.unionSql
        searchQuery.bizType = this.$route.params.bizType
      }
      if (option.page * option.limit > option.dlpTotal) {
        this.$route.params.id = ''
        this.$route.params.createDate = ''
      }
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.query.id = undefined
      this.paramData = {}
      this.gridTable.execRowDataApi(this.query)
    },
    handleView(row) {
      if (row.bizType === 2) {
        getAlarmDetail(row.id).then(respond => {
          this.rowDetail2 = respond.data[0]
          if (respond.data.length === 1) {
            this.rowDetail2.alarmDesc = respond.data[0].alarmDesc
          } else {
            const descs = respond.data.map(item => item['alarmDesc'])
            let num = 1
            this.rowDetail2.alarmDesc = descs.map(item => { return `${num++}.${item}` }).join('\n')
          }
          this.descDetailVisible = true
        })
      } else {
        this.descDetailVisible = true
        this.rowDetail2 = Object.assign({}, row)
      }
    },
    bizTypeFormatter: function(row, data) {
      return this.bizTypeOptions[data]
    },
    alarmTypeFormatter: function(row, data) {
      return this.alarmTypeOptions[data]
    },
    alarmObjectFormatter: function(row, data) {
      if (row.alarmType == 0 || (row.alarmType & 4) > 0) {
        const idSet = new Set(data.split(',').map(Number))
        return this.sysUserList.filter(user => idSet.has(user.id)).map(user => user.name).join(', ') || this.$t('text.unknown')
      }
      return data
    },
    showDetailFormatter: function(row, data) { // 目前仅软件订单预警存在自己录，可查看详情
      return row.bizType != 2
    },
    treatmentMeasureFormatter(row, data) {
      return this.iconAlarmLimitFormatter(row).map(info => {
        return `<svg class="svg-icon" aria-hidden="true" style="width: 1.1em; height: 1.1em;vertical-align: -.15em; fill: currentColor; margin-right: 3px">
            <use xlink:href="#icon-${info.class}">
            <title>${info.title}</title>
            </use>
        </svg>`
      }).join('')
    },
    iconAlarmLimitFormatter(row) {
      if (!row || !row.alarmType) { return [] }
      const resultBits = this.splitBitToArr(row.alarmType)
      return resultBits.map(alarmLimit => getDict(this.alarmLimitDict, alarmLimit)).filter(r => !!r).map(r => {
        return { class: this.iconOptions[r.value], title: r.label, value: r.value }
      })
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      const queryData = Object.assign({}, this.query, this.paramData)
      return exportSysAlarmLog({ exportType, ...queryData })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    splitBitToArr(alarmLimit) {
      const resultBits = []
      let temp = alarmLimit || 0
      let r = 1
      while (temp > 0) {
        if ((temp & 1) === 1) {
          resultBits.push(r)
        }
        temp >>= 1
        r <<= 1
      }
      return resultBits
    }
  }
}
</script>

<style lang="scss">
  .desc-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }
</style>
