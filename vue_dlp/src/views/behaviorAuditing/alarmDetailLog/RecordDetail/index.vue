<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :modal="false"
      :title="$t('pages.screenRecordDetails')"
      :visible.sync="detailDialogVisible"
      width="800px"
      :before-close="handleClose"
    >

      <grid-table
        ref="detailGrid"
        :height="400"
        :multi-select="false"
        :col-model="detailColModel"
        :row-data-api="rowDataApi"
      />

    </el-dialog>
    <video-viewer ref="videoViewer" :title="$t('pages.screenVideoPlayback')" :search-report="queryDetail.searchReport"/>
  </div>
</template>

<script>
import { getLogDetailPage } from '@/api/behaviorAuditing/alarmDetailLog'

export default {
  name: 'MonitorDetail',
  props: {
    detailDialogVisible: {
      type: Boolean,
      default: false
    },
    queryDetail: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      detailColModel: [
        { prop: 'fileName', label: 'fileName', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '150',
          buttons: [
            { label: 'startPlay', click: this.handlePlay, disabledFormatter: this.showRecFormatter }
          ]
        }
      ]
    }
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.queryDetail, option)
      return getLogDetailPage(searchQuery)
    },
    handleClose() {
      this.$emit('handleClose')
    },
    handlePlay(rowData) {
      this.$refs['videoViewer'].play(rowData)
    },
    showRecFormatter(data, btn) {
      return !data.dataFileGuid
    }
  }
}
</script>

<style lang="scss" scoped>
  #videoRecorderDlg >>>.el-dialog{
    height: calc(100vh - 20px);
    .el-dialog__body{
      height: calc(100% - 35px);
      max-height: none;
      overflow: hidden;
    }
  }
</style>
