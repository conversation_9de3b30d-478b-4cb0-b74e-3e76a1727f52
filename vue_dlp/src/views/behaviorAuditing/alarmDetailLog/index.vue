<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="alarmBus" :value="alarmBus">
          <span>{{ $t('pages.commonAlarmType') }}:</span>
          <el-select v-model="alarmBus" :placeholder="$t('pages.all')" style="width: 130px;">
            <el-option value="" :label="$t('pages.all')"/>
            <el-option value="0" :label="$t('pages.alarmType0')"/>
            <el-option v-if="hasLabelModule" value="1" :label="$t('pages.alarmType1')"/>
            <el-option value="2" :label="$t('pages.alarmType2')"/>
          </el-select>
        </SearchItem>
        <SearchItem model-key="alarmType" :value="alarmType">
          <span>{{ $t('pages.violationType') }}:</span>
          <el-select v-model="alarmType" clearable is-filter filterable :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="item in tempTypeOptions" :key="item.alarmType" :label="item.remark" :value="item.alarmType"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.alarmLevel" :value="query.alarmLevel">
          <span>{{ $t('pages.level') }}：</span>
          <el-select v-model="query.alarmLevel" clearable :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.action" :value="query.action">
          <span>{{ $t('pages.action') }}：</span>
          <el-select v-model="query.action" clearable :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(item, key) in actionOptions" :key="key" :label="item.alarmActionDesc" :value="item.alarmAction"></el-option>
          </el-select>
        </SearchItem>

        <audit-log-exporter slot="append" v-permission="'304'" :request="exportFunc"/>
        <audit-log-delete
          v-if="$store.getters.auditingDeleteAble"
          slot="append"
          v-permission="'412'"
          :selection="selection"
          :delete-log="deleteLog"
          :table-getter="gridTable"
        />
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :default-sort="{ prop: 'createTime' }"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('412')"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @handleIcon="(rowDetail,item)=>handleIcon(rowDetail,item)"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.eventDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.action')">
            {{ rowDetail.actionDescribe }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.level')">
            {{ rowDetail.alarmLevelDescribe }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.violationType')">
            {{ rowDetail.alarmTypeDescribe }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.treatmentMeasure')">
            <el-tooltip
              v-for="(item,index) in rowDetail.alarmLimits"
              :key="item"
              effect="dark"
              :content="rowDetail.alarmLimitDescribes[index]"
              placement="top-start"
            >
              <svg-icon :icon-class="iconObj[item]" :style="(item === 128 || item === 256 ) ? 'margin-right:10px;cursor: pointer;color: #68a8d0' : 'margin-right:10px;cursor:pointer'" @click="handleIcon(rowDetail,iconObj[item])"/>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.filePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
        </el-descriptions>
        <el-card :body-style="{'max-height': '320px', 'overflow-y': 'auto'}" class="box-card" style="max-height: 390px;">
          <div slot="header" class="clearfix" style="padding-left: 0;">
            <span>{{ $t('pages.alarmDetails') }}</span>
          </div>
          <el-tag v-if="rowDetail.createTime">{{ rowDetail.createTime }}</el-tag>
          <p style="white-space: pre-wrap;">{{ rowDetail.eventDescription }}</p>
        </el-card>
      </div>
    </el-dialog>
    <MonitorDetail
      ref="detail"
      :detail-dialog-visible="screenshotDialogVisible"
      :query-detail="queryScreenshot"
      :type="'screenshot'"
      @handleClose="screenshotDialogVisible=false"
    />
    <RecordDetail
      ref="record"
      :detail-dialog-visible="recordDialogVisible"
      :query-detail="queryRecord"
      @handleClose="recordDialogVisible=false"
    />
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, getAlarmType, getAlarmAction, exportCommonAlarmLog, deleteLog } from '@/api/behaviorAuditing/alarmDetailLog'
import MonitorDetail from '@/views/behaviorManage/hardware/sensitiveOpLog/MonitorDetail'
import RecordDetail from './RecordDetail'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { isIPv4 } from '@/utils/validate'
import { getAlarmLevelDict } from '@/api/system/baseData/alarmLevel'

export default {
  name: 'AlarmDetailLog',
  components: { MonitorDetail, RecordDetail },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '170', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '130', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'alarmBusDescribe', label: 'commonAlarmType', width: '150' },
        { prop: 'alarmTypeDescribe', label: 'violationType', width: '150' },
        { prop: 'alarmLevelDescribe', label: 'level', width: '100' },
        { prop: '', label: 'treatmentMeasure', ellipsis: false, width: '200', iconFormatter: this.iconClassFormat },
        { prop: 'actionDescribe', label: 'action', width: '100' },
        { prop: 'filePath', label: 'terFilePath', width: '250' },
        { prop: 'capscreenGuid', label: 'screenshotRecordGuid', width: '150', hidden: true },
        // 通过 addViewVideoBtn 方法，在 查看详情 后面添加 查看录屏 按钮
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('207'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      alarmLevelOptions: [],

      iconObj: {
        2: 'alarm-01',
        4: 'alarm-02',
        8: 'alarm-03',
        // 16: '',
        32: 'alarm-04',
        64: 'alarm-05',
        128: 'alarm-06',
        256: 'alarm-07'
      },
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        alarmType: undefined,
        alarmBus: undefined,
        alarmLevel: undefined,
        action: undefined,
        searchReport: 1,
        guid: undefined,
        taskGuid: undefined,
        menuCode: this.$route.meta.code,
        tableKey: 'logList',
        hasChild: undefined,
        taskId: undefined
      },
      queryScreenshot: { // 查询条件
        page: 1,
        createDate: '',
        capscreenGuid: '',
        sortName: 'createTime',
        sortOrder: 'asc',
        isTimes: false
      },
      queryRecord: { // 查询条件
        objectId: '',
        createTime: '',
        recscreenGuid: ''
      },
      rowDetail: {},
      dialogFormVisible: false,
      showTree: true,
      typeOptions: [],
      tempTypeOptions: [],
      actionOptions: [],
      screenshotDialogVisible: false,
      recordDialogVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      guid: undefined,
      selection: [],
      queryVideoMethod: undefined,
      paramData: {},
      alarmBus: undefined,
      alarmType: ''
    }
  },
  computed: {
    hasLabelModule() { // 根据销售模块过滤配置项
      const moduleIds = [...this.$store.getters.saleModuleIds]
      if (moduleIds.length === 0) {
        return true
      }
      if (this.containSalModuleId(moduleIds, 24)) {
        return true
      }
      return false
    }
  },
  watch: {
    '$store.state.commonData.notice.generalAlarmDetail'() {
      this.paramData = Object.assign({}, this.$store.getters.alarmMsg)
      this.query.guid = this.$store.getters.alarmMsg.guid
      this.query.taskGuid = this.$store.getters.alarmMsg.taskGuid
      this.query.hasChild = this.$store.getters.alarmMsg.hasChild
      this.query.taskId = this.$store.getters.alarmMsg.taskId
      this.query.alarmBus = undefined
      this.query.alarmType = ''
      this.$refs.searchToolbar.setDate(this.$store.getters.alarmMsg.createDate)
      this.gridTable().execRowDataApi(this.query)
    },
    alarmBus(value) {
      this.tempTypeOptions = value ? this.typeOptions.filter(item => item.alarmBus == value) : this.typeOptions
      this.query.alarmBus = value

      const alarmType = this.alarmType.slice(-1)
      if (alarmType != value) {
        this.alarmType = ''
      }
    }
  },
  mounted() {
  },
  created() {
    this.$store.dispatch('commonData/setSaleModuleIds')
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    getAlarmType().then(res => {
      this.typeOptions = res.data.sort((a, b) => a.sort - b.sort)
      // alarmType31跟34的都为邮件外发管控，所以在这边过滤掉其中一个
      const alarmType1 = this.typeOptions.findIndex(item => item.alarmType == 31)
      const alarmType2 = this.typeOptions.findIndex(item => item.alarmType == 34)
      if (alarmType1 != -1 && alarmType2 != -1) {
        this.typeOptions.splice(alarmType2, 1)
      }
      // 重置 typeOptions 的属性 alarmType
      this.typeOptions = this.typeOptions.map(item => ({ ...item, alarmType: item.alarmType + '-' + item.alarmBus }))
      this.alarmBus = undefined
      this.alarmType = ''
    })
    getAlarmAction({ type: 1 }).then(res => {
      this.actionOptions = res.data
    })
    addViewVideoBtn(this)
    getAlarmLevelDict().then(res => {
      this.alarmLevelOptions = res.data.map(item => {
        return { value: item.level, label: item.levelName }
      })
    })
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    compare(a, b) {
      if (isIPv4(a) && isIPv4(b)) {
        const m = parseInt(a.toString().replace(/\./g, ''))
        const n = parseInt(b.toString().replace(/\./g, ''))
        return (m > n) ? 1 : (m < n) ? -1 : 0
      } else {
        if (typeof a == 'string') {
          return a.localeCompare(b, 'zh-CN')
        } else {
          return (a > b) ? 1 : (a < b) ? -1 : 0
        }
      }
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      const { guid, createDate, taskGuid, hasChild, taskId } = this.$route.params || {}
      if (guid) {
        this.paramData = Object.assign({}, this.$route.params)
        Object.assign(searchQuery, { alarmBus: undefined, alarmType: '' })
      }
      if (guid && createDate) {
        this.$refs.searchToolbar.setDate(createDate)
        Object.assign(searchQuery, { guid, createDate, taskGuid, hasChild, taskId, alarmBus: undefined, alarmType: '' })
      }
      if (option.page * option.limit > option.dlpTotal) {
        Object.assign(this.$route.params, { guid: '', createDate: '', taskGuid: '', hasChild: '', taskId: '' })
      }
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      this.query.guid = undefined
      this.paramData = {}
      this.dealAlarmType(this.alarmType)
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleView(row) {
      this.rowDetail = Object.assign({}, row)
      if (this.rowDetail && this.rowDetail.eventDescription) {
        this.rowDetail.eventDescription = this.rowDetail.eventDescription.replace(/\\r\\n/g, '\r\n')
      }
      this.dialogFormVisible = true
    },
    iconClassFormat(row) {
      const iconArr = []
      for (let i = 0; i < row.alarmLimits.length; i++) {
        const limit = row.alarmLimits[i]
        const label = row.alarmLimitDescribes[i]
        const iconClass = this.iconObj[limit]
        const iconObj = { class: iconClass, title: label }
        if (limit === 128 || limit === 256) {
          iconObj.className = 'text-button-icon'
        }
        iconArr.push(iconObj)
      }
      return iconArr
    },
    handleIcon(row, iconClass) {
      if (this.hasPermission('207')) {
        let item;
        for (const limit in this.iconObj) {
          const temp = this.iconObj[limit]
          if (temp === iconClass) {
            item = Number.parseInt(limit)
            break;
          }
        }
        if (item === 128) {
          this.screenshotDetail(row)
        } else if (item === 256) {
          this.recordDetail(row)
        }
      }
    },
    screenshotDetail(row) {
      this.queryScreenshot.page = 1
      this.queryScreenshot.objectId = row.terminalId
      this.queryScreenshot.capscreenGuid = row.capscreenGuid
      this.queryScreenshot.createDate = row.createTime
      this.queryScreenshot.searchReport = this.query.searchReport
      this.screenshotDialogVisible = true
      this.$nextTick(() => {
        const detailGrid = this.$refs.detail.$refs.detailGrid
        detailGrid.execRowDataApi(this.queryScreenshot)
      })
    },
    recordDetail(row) {
      this.queryRecord.objectId = row.terminalId
      this.queryRecord.recscreenGuid = row.recscreenGuid
      this.queryRecord.recScreenTime = row.createTime
      this.queryRecord.searchReport = this.query.searchReport
      this.recordDialogVisible = true
      this.$nextTick(() => {
        const detailGrid = this.$refs.record.$refs.detailGrid
        detailGrid.execRowDataApi(this.queryRecord)
      })
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      const queryData = Object.assign({}, this.query, this.paramData)
      return exportCommonAlarmLog({ exportType, ...queryData })
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '207', this.query.searchReport)
      this.query.guid = undefined
      this.query.taskGuid = undefined
      this.query.hasChild = undefined
      this.query.taskId = undefined
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    dealAlarmType(value) {
      if (value == '') {
        this.query.alarmBus = ''
        this.query.alarmType = ''
      } else {
        const [alarmType, alarmBus] = value.split('-')
        this.query.alarmBus = Number(alarmBus)
        this.query.alarmType = alarmType
      }
    },
    containSalModuleId(moduleIds, moduleId) {
      return moduleIds.includes(moduleId) || moduleIds.includes(100 + moduleId) || moduleIds.includes(200 + moduleId) || moduleIds.includes(300 + moduleId)
    }
  }
}
</script>
