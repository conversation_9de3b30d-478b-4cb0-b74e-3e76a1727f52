<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.btFileLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.transmissionTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize1')">
            {{ rowDetail.fileSize }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.deviceAddr')">
            {{ rowDetail.blueAddress }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.devType')">
            {{ devTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.blueName')">
            {{ rowDetail.blueName }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileName1')">
            <el-button
              v-permission="'225'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!225'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.filePath')">
            {{ rowDetail.filePath }}
          </el-descriptions-item>
          <!--<el-descriptions-item span="2" :label="$t('table.backupFilePath')">
            {{ rowDetail.backupFilePath }}
          </el-descriptions-item>-->
        </el-descriptions>
      </div>
    </el-dialog>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'225'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
  </div>
</template>
<script>

import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'BtFileLogDetail',
  components: { AuditFileDownloader },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      selection: [],
      defaultTempTask: {
        backType: 6,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      devTypeOptions: [
        { label: this.$t('pages.computer'), value: '1' },
        { label: this.$t('pages.phone'), value: '2' },
        { label: this.$t('pages.networkAccessPoint'), value: '3' },
        { label: this.$t('pages.audioAndVideo'), value: '4' },
        { label: this.$t('pages.parts'), value: '5' },
        { label: this.$t('pages.imaging'), value: '6' },
        { label: this.$t('pages.wearable'), value: '7' },
        { label: this.$t('pages.toys'), value: '8' },
        { label: this.$t('pages.healthy'), value: '9' },
        { label: this.$t('pages.other'), value: '0' }
        // { label: this.$t('pages.softwareTypes5'), value: 'F' }
      ]
    }
  },
  computed: {
    blueMap() {
      const map = {}
      this.devTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    devTypeFormatter: function(row) {
      return this.blueMap[row.devType]
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    handleDownload: function(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      tempTask.fileName = row.fileName
      return tempTask
    }
  }
}
</script>
