<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.cmdType" :value="query.cmdType">
          <span>{{ $t('pages.transceiverFlag') }}：</span>
          <el-select v-model="query.cmdType" clearable style="width: 180px;">
            <el-option
              v-for="item in cmdTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.mailTitle" :value="query.mailTitle">
          <span>{{ $t('pages.mailTitle') }}：</span>
          <el-input v-model="query.mailTitle" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.attachName" :value="query.attachName">
          <span>{{ $t('pages.attachmentName') }}：</span>
          <el-input v-model="query.attachName" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'234'" :request="handleExport"/>
        <el-button
          slot="append"
          v-permission="'226'"
          :loading="downloading"
          :disabled="downloadingDisable"
          size="mini"
          icon="el-icon-download"
          :title="$t('components.download')"
          class="ellipsis"
          @click.stop="handleDownload"
        >
          {{ $t('components.download') }}
        </el-button>
        <audit-file-downloader v-show="false" ref="auditFileDownloader" slot="append" v-permission="'226'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload" :really-download="attachmentReallyDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'457'" :selection="tempSelection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :autoload="autoload"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.emailLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.isAttachment')">
            {{ attachCountFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.attachmentName')">
            {{ attachNameFormatter(rowDetail, rowDetail.relMailAttachInfoLogs) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.transceiverFlag')">
            {{ cmdTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.size1')">
            {{ fileSizeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.mailTitle')">
            <el-button
              v-permission="'226'"
              type="text"
              :title="rowDetail.mailTitle"
              :disabled="!rowDetail.fileGuid"
              style="margin-bottom: 0; padding: 0;"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.mailTitle }}
            </el-button>
            <span v-permission="'!226'">{{ rowDetail.mailTitle }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.sender')">
            {{ rowDetail.sender }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.addressee')">
            {{ rowDetail.recver }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import {
  getLogPage,
  deleteLog,
  exportExcel,
  getRelAttachment,
  downloadAttachment
} from '@/api/behaviorAuditing/terminal/email'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { formatDate } from 'element-ui/src/utils/date-util'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'EmailLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        // { prop: 'groupId', label: '所属分组', width: '100' },
        { prop: 'attachCount', label: 'isAttachment', width: '110', formatter: this.attachCountFormatter },
        { prop: 'relMailAttachInfoLogs', label: this.$t('pages.attachmentName'), width: '150', formatter: this.attachNameFormatter },
        { prop: 'cmdType', label: 'transceiverFlag', width: '150', formatter: this.cmdTypeFormatter },
        { prop: 'mailTitle', label: 'mailTitle', width: '200' },
        { prop: 'fileSize', label: 'size1', width: '100', formatter: this.fileSizeFormatter },
        { prop: 'sender', label: 'sender', width: '150' },
        { prop: 'recver', label: 'addressee', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right', hidden: !this.hasPermission('226,293'),
          buttons: [
            { label: 'downloadMail', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, isShow: () => this.hasPermission('226') },
            { label: 'downloadAttachment', click: this.handleDownloadAttachment, isShow: row => this.hasPermission('226') && !this.downloadFormatter(row) && row.attachCount == 1 },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('293') }
          ]
        }
      ],
      attachCountMap: {
        0: this.$t('text.no'),
        1: this.$t('text.yes')
      },
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        mailTitle: '',
        attachName: '',
        keyword2: null,
        cmdType: null,
        keyword4: null,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      percentOptions: [
        { status: 0, percent: 0 },
        { status: 1, percent: 1 },
        { status: 5, percent: 50 },
        { status: 6, percent: 100 }
      ],
      cmdTypeShortOpt: {
        1: 'SMTP',
        2: 'POP',
        3: 'IMAP',
        11: 'WEB',
        14: 'Exchange',
        15: 'Exchange'
      },
      tempTask: {},
      defaultTempTask: {
        backType: 9,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      mailBackType: 9,
      browserUploadBackType: 3,
      downloading: false,
      cmdTypeOption: [
        { label: this.$t('pages.emailLog_All'), value: null },
        { label: this.$t('pages.emailLog_Msg'), value: 1 },
        { label: this.$t('pages.emailLog_Msg1'), value: 2 },
        { label: this.$t('pages.emailLog_Msg2'), value: 3 },
        // { label: this.$t('pages.emailLog_Msg3'), value: 13 },
        { label: this.$t('pages.emailLog_Msg4'), value: 11 },
        { label: this.$t('pages.emailLog_Msg5'), value: 14 },
        { label: this.$t('pages.emailLog_Msg6'), value: 15 }
        // ,{ label: this.$t('pages.emailLog_Msg7'), value: 12 }
      ],
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      tempSelection: [],
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined,
      showRelTitle: '',
      // 目前关联附件为 关联网页上传文件的文件
      relWebMailGuids: {}
    }
  },
  computed: {
    cmdTypeMap() {
      const map = {}
      this.cmdTypeOption.forEach(item => {
        map[item.value] = item.label
      })
      return map
    },
    downloadingDisable() {
      return !this.tempSelection || this.tempSelection.length === 0
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    selectable(row, index) {
      return !this.downloadFormatter(row) || (this.$store.getters.auditingDeleteAble && this.hasPermission('457'))
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    cmdTypeFormatter: function(row) {
      if (row.cmdType === 13) {
        return this.$t('pages.emailLog_Msg')
      }
      return this.cmdTypeMap[row.cmdType]
    },
    attachCountFormatter(row) {
      return this.attachCountMap[row.attachCount]
    },
    attachNameFormatter(row, data) {
      return data ? data.map(item => item.fileName).join(',') : ''
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.tempSelection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      tempTask.fileName = row.fileName
      tempTask.downloadType = row.downloadType
      tempTask.backType = row.backType
      tempTask.webMailGuid = row.webMailGuid
      tempTask.attachCount = row.attachCount
      tempTask.mailName = row.mailTitle
      if (row.cmdType && this.cmdTypeShortOpt[row.cmdType] && row.backType == this.mailBackType) {
        tempTask.fileName = `${this.cmdTypeShortOpt[row.cmdType]}${(row.createTime || '').replace(/[-: ]/g, '')}.eml`
      }
      if (row.downloadType == 3 && row.backType == this.mailBackType) {
        tempTask.termId = row.termId
        tempTask.createTime = row.createTime
      }
      return tempTask
    },
    handleLoadDown(row) {
      const opt = Object.assign({}, row, { downloadType: 1, backType: this.mailBackType })
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(opt)
    },
    attachmentReallyDownload(tempTasks, taskFile, closeProgress) {
      const reqTasks = [];
      const attachmentIndexMap = {};
      const browserFileInfo = {};
      (tempTasks || []).forEach(task => {
        if (!task) { return }
        if (!task.downloadType || task.downloadType == 1) {
          reqTasks.push(task)
        } else {
          if (task.backType == this.mailBackType) {
            if (task.downloadType == 3 && task.webMailGuid) {
              const index = attachmentIndexMap[task.webMailGuid]
              if (index >= 0) {
                Object.assign(reqTasks[index], task)
                return
              } else {
                attachmentIndexMap[task.webMailGuid] = reqTasks.length
              }
            }
            reqTasks.push(task)
          } else {
            let index = attachmentIndexMap[task.webMailGuid]
            if (!index && index !== 0) {
              reqTasks.push({ webMailGuid: task.webMailGuid, attachCount: 1, downloadType: 2 })
              index = reqTasks.length - 1
              attachmentIndexMap[task.webMailGuid] = index
            }
            !reqTasks[index].attachmentTasks && (reqTasks[index].attachmentTasks = [])
            reqTasks[index].attachmentTasks.push(task)
            if (task.downloadType == 3) {
              browserFileInfo[task.fileGuid] = task
            }
          }
        }
      })
      // 补充文件后缀名  终端目前优化成统一使用了备份接口，所以备份生成的文件都是以一串GUID来命名 不包含后缀名，因此需要进行补充
      reqTasks.forEach(task => {
        // 网页发送邮件可能会存在多个邮件发送的是同一份文件  此时的fileGuid是相同的， 按目前的赋值方式，只能将网页上传附件赋值到一个邮件上，其他邮件就丢失了这份附件，所以需要做一个补充
        if (task.downloadType == 3 && task.webMailGuid) {
          const attachmentTasks = task.attachmentTasks || []
          const relBrowsers = this.relWebMailGuids[task.webMailGuid] || []
          if (attachmentTasks.length !== relBrowsers.length) {
            const suppleAttachmentTasks = relBrowsers.map(r => r.fileGuid).filter(fileGuid => !attachmentTasks.some(t1 => t1.fileGuid == fileGuid))
              .map(fileGuid => browserFileInfo[fileGuid]).map(fileInfo => Object.assign({}, fileInfo, { webMailGuid: task.webMailGuid }))
            attachmentTasks.push(...suppleAttachmentTasks)
            task.attachmentTasks = attachmentTasks
          }
        }
        if (task.backType == this.mailBackType && task.fileName && !task.fileName.endsWith('.eml')) {
          task.fileName = task.fileName + '.eml'
        }
      })
      downloadAttachment(this.$route.name, reqTasks, taskFile).then(() => {
        taskFile.percent = 100
        this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownResult(taskFile.taskQueueKey, {
          type: 'success',
          title: this.$t('text.success'),
          message: this.$t('components.fileDownload')
        })
      }).catch(() => {
        closeProgress()
      })
    },
    handleDownloadAttachment(row) {
      if (row.cmdType != 11 || !row.webMailGuid) {
        const opt = Object.assign({}, row, { downloadType: 2, backType: this.mailBackType })
        this.selection.splice(0, this.selection.length, opt)
        this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.batchDownload()
        return
      }
      const webMailGuids = this.relWebMailGuids[row.webMailGuid]
      if (webMailGuids) {
        this.selection.splice(0, this.selection.length, ...webMailGuids.map(o => Object.assign({}, o, { downloadType: 2, backType: this.browserUploadBackType })))
        this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.batchDownload()
      } else {
        const searchParams = {
          createDate: formatDate(row.createTime),
          isTimes: false,
          queryInfos: [
            { terminalId: row.termId, webMailGuids: [row.webMailGuid] }
          ]
        }
        getRelAttachment(searchParams).then(res => {
          if (res.data && !res.data.length) {
            this.$message({
              message: this.$t('pages.noGetAttachment'),
              type: 'error'
            })
            return
          }
          this.selection.splice(0)
          res.data.forEach(item => {
            const infos = item.relWebMailInfos
            infos.forEach(o => { o['webMailGuid'] = item.webMailGuid })
            this.relWebMailGuids[item.webMailGuid] = infos
            this.selection.push(...infos.map(o => Object.assign({}, o, { downloadType: 2, backType: this.browserUploadBackType })))
            this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.batchDownload()
          })
        })
      }
    },
    async handleDownload() {
      const tempSelection = this.tempSelection.filter(item => item.fileGuid)
      const pendingGetRelGuids = tempSelection.filter(item => item.cmdType == 11 && item.attachCount == 1 && item.webMailGuid).filter(item => !this.relWebMailGuids[item.webMailGuid])
      let startDate, endDate
      const termGuidMap = {}
      for (const info of pendingGetRelGuids) {
        const date = new Date(info.createTime)
        if (!startDate || startDate > date) {
          startDate = date
        }
        if (!endDate || endDate < date) {
          endDate = date
        }
        !termGuidMap[info.termId] && (termGuidMap[info.termId] = [])
        termGuidMap[info.termId].push(info.webMailGuid)
      }
      if (Object.keys(termGuidMap).length > 0) {
        const searchParams = {
          isTimes: true,
          startDate,
          endDate,
          queryInfos: Object.entries(termGuidMap).map(([key, value]) => { return { terminalId: key, webMailGuids: value } })
        }
        this.downloading = true
        await getRelAttachment(searchParams).then(res => {
          (res.data || []).forEach(item => {
            const infos = item.relWebMailInfos
            infos.forEach(o => { o['webMailGuid'] = item.webMailGuid })
            this.relWebMailGuids[item.webMailGuid] = infos
          })
          this.downloading = false
        }).catch(() => {
          this.downloading = false
        })
      }
      const selection2 = []
      for (const selection of tempSelection) {
        selection2.push(Object.assign({}, selection, { downloadType: 3, backType: this.mailBackType }))
        if (selection.cmdType == 11 && !!selection.webMailGuid && this.relWebMailGuids[selection.webMailGuid]) {
          selection2.push(...this.relWebMailGuids[selection.webMailGuid].map(item => Object.assign({}, item, { downloadType: 3, backType: this.browserUploadBackType })))
        }
      }
      this.selection.splice(0, this.selection.length, ...selection2)
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.batchDownload()
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    fileSizeFormatter(row) {
      // 流量传的是byte，转换成kb
      return row.fileSize ? (Number(row.fileSize) / 1024.0).toFixed(2) : 0
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '293', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss" scoped>
  .rel-download {
    margin-left: 0;
  }
  .rel-dialog >>>.el-dialog__body {
    padding: 10px 19px
  }
</style>
