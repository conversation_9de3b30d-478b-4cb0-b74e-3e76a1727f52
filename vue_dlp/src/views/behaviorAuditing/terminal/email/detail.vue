<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.emailLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.isAttachment')">
            {{ attachCountFormatter(rowDetail) }}
            <el-button
              v-show="!attachCountFormatter(rowDetail)"
              type="text"
              style="margin-bottom: 0; padding: 2px"
              @click="() => clickAttachment(rowDetail)"
            >{{ $t('pages.hasRelAttachment') }}</el-button>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.transceiverFlag')">
            {{ cmdTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.size1')">
            {{ fileSizeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.mailTitle')">
            <el-button
              v-permission="'226'"
              type="text"
              :title="rowDetail.mailTitle"
              :disabled="!rowDetail.fileGuid"
              style="margin-bottom: 0; padding: 0;"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.mailTitle }}
            </el-button>
            <span v-permission="'!226'">{{ rowDetail.mailTitle }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.sender')">
            {{ rowDetail.sender }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.addressee')">
            {{ rowDetail.recver }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="showRelTitle"
      :visible.sync="showRelAttach"
      width="600px"
      class="rel-dialog"
    >
      <grid-table
        ref="relAttachmentTable"
        :height="400"
        :col-model="attachModel"
        :row-data-api="relRowDataApi"
        :autoload="false"
        @selectionChangeEnd="relTableSelectionChangeEnd"
      />
    </el-dialog>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'226'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload" :really-download="attachmentReallyDownload"/>
  </div>
</template>
<script>

import { formatDate } from 'element-ui/src/utils/date-util';
import { getRelAttachment, downloadAttachment } from '@/api/behaviorAuditing/terminal/email';
import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'EmailLogDetail',
  components: { AuditFileDownloader },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      attachCountMap: {
        0: this.$t('text.no'),
        1: this.$t('text.yes')
      },
      showRelAttach: false,
      showRelTitle: '',
      queryRow: {},
      attachModel: [
        { prop: 'fileName', label: 'fileName', width: 150 },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('226'),
          buttons: [
            { label: 'download', click: this.handleDownload }
          ]
        }
      ],
      cmdTypeOption: [
        { label: this.$t('pages.emailLog_All'), value: null },
        { label: this.$t('pages.emailLog_Msg'), value: 1 },
        { label: this.$t('pages.emailLog_Msg1'), value: 2 },
        { label: this.$t('pages.emailLog_Msg2'), value: 3 },
        // { label: this.$t('pages.emailLog_Msg3'), value: 13 },
        { label: this.$t('pages.emailLog_Msg4'), value: 11 },
        { label: this.$t('pages.emailLog_Msg5'), value: 14 },
        { label: this.$t('pages.emailLog_Msg6'), value: 15 }
        // ,{ label: this.$t('pages.emailLog_Msg7'), value: 12 }
      ],
      selection: [],
      relSelection: [],
      cmdTypeShortOpt: {
        1: 'SMTP',
        2: 'POP',
        3: 'IMAP',
        11: 'WEB',
        14: 'Exchange',
        15: 'Exchange'
      },
      mailBackType: 9,
      browserUploadBackType: 3,
      relWebMailGuids: {},
      defaultTempTask: {
        backType: 9,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      }
    }
  },
  computed: {
    cmdTypeMap() {
      const map = {}
      this.cmdTypeOption.forEach(item => {
        map[item.value] = item.label
      })
      return map
    }
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    handleDownload: function(row) {
      const opt = Object.assign({}, row, { downloadType: 1, backType: this.mailBackType })
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(opt)
    },
    cmdTypeFormatter: function(row) {
      if (row.cmdType === 13) {
        return this.$t('pages.emailLog_Msg')
      }
      return this.cmdTypeMap[row.cmdType]
    },
    fileSizeFormatter(row) {
      // 流量传的是byte，转换成kb
      return Math.ceil(parseInt(row.fileSize) / 1024)
    },
    attachCountFormatter(row) {
      if (row.attachCount == 0 && row.webMailGuid) {
        // 可点击“存在关联附件”的权限与邮件记录的查看详情一致，当如果没有查看详情权限时，格式化文字需要展示 “存在关联附件”，而不能点击
        return !this.hasPermission('293') ? this.$t('pages.hasRelAttachment') : ''
      }
      return this.attachCountMap[row.attachCount]
    },
    clickAttachment(row) {
      this.showRelAttach = true
      this.showRelTitle = (row && row.mailTitle ? row.mailTitle + ' - ' : '') + this.$t('pages.relAttachment')
      this.queryRow = row
      this.$nextTick(() => {
        this.$refs['relAttachmentTable'] && this.$refs['relAttachmentTable'].execRowDataApi()
      })
    },
    relRowDataApi(opt) {
      // mailTitle 不作查询条件，仅仅作 管理员操作日志的格式化输出
      const searchParams = Object.assign({
        objectType: 1,
        objectId: this.queryRow.termId,
        createDate: formatDate(this.queryRow.createTime),
        isTimes: false,
        webMailGuid: this.queryRow.webMailGuid,
        mailTitle: this.queryRow.mailTitle
      }, opt)
      return getRelAttachment(searchParams)
    },
    relTableSelectionChangeEnd(selections) {
      this.relSelection = selections
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      tempTask.fileName = row.fileName
      tempTask.downloadType = row.downloadType
      tempTask.backType = row.backType
      tempTask.webMailGuid = row.webMailGuid
      tempTask.attachCount = row.attachCount
      tempTask.mailName = row.mailTitle
      if (row.cmdType && this.cmdTypeShortOpt[row.cmdType] && row.backType == this.mailBackType) {
        tempTask.fileName = `${this.cmdTypeShortOpt[row.cmdType]}${(row.createTime || '').replace(/[-: ]/g, '')}.eml`
      }
      if (row.downloadType == 3 && row.backType == this.mailBackType) {
        tempTask.termId = row.termId
        tempTask.createTime = row.createTime
      }
      return tempTask
    },
    attachmentReallyDownload(tempTasks, taskFile, closeProgress) {
      const reqTasks = [];
      const attachmentIndexMap = {};
      const browserFileInfo = {};
      (tempTasks || []).forEach(task => {
        if (!task) { return }
        if (!task.downloadType || task.downloadType == 1) {
          reqTasks.push(task)
        } else {
          if (task.backType == this.mailBackType) {
            if (task.downloadType == 3 && task.webMailGuid) {
              const index = attachmentIndexMap[task.webMailGuid]
              if (index >= 0) {
                Object.assign(reqTasks[index], task)
                return
              } else {
                attachmentIndexMap[task.webMailGuid] = reqTasks.length
              }
            }
            reqTasks.push(task)
          } else {
            let index = attachmentIndexMap[task.webMailGuid]
            if (!index && index !== 0) {
              reqTasks.push({ webMailGuid: task.webMailGuid, attachCount: 1, downloadType: 2 })
              index = reqTasks.length - 1
              attachmentIndexMap[task.webMailGuid] = index
            }
            !reqTasks[index].attachmentTasks && (reqTasks[index].attachmentTasks = [])
            reqTasks[index].attachmentTasks.push(task)
            if (task.downloadType == 3) {
              browserFileInfo[task.fileGuid] = task
            }
          }
        }
      })
      // 补充文件后缀名  终端目前优化成统一使用了备份接口，所以备份生成的文件都是以一串GUID来命名 不包含后缀名，因此需要进行补充
      reqTasks.forEach(task => {
        // 网页发送邮件可能会存在多个邮件发送的是同一份文件  此时的fileGuid是相同的， 按目前的赋值方式，只能将网页上传附件赋值到一个邮件上，其他邮件就丢失了这份附件，所以需要做一个补充
        if (task.downloadType == 3 && task.webMailGuid) {
          const attachmentTasks = task.attachmentTasks || []
          const relBrowsers = this.relWebMailGuids[task.webMailGuid] || []
          if (attachmentTasks.length !== relBrowsers.length) {
            const suppleAttachmentTasks = relBrowsers.map(r => r.fileGuid).filter(fileGuid => !attachmentTasks.some(t1 => t1.fileGuid == fileGuid))
              .map(fileGuid => browserFileInfo[fileGuid]).map(fileInfo => Object.assign({}, fileInfo, { webMailGuid: task.webMailGuid }))
            attachmentTasks.push(...suppleAttachmentTasks)
            task.attachmentTasks = attachmentTasks
          }
        }
        if (task.backType == this.mailBackType && task.fileName && !task.fileName.endsWith('.eml')) {
          task.fileName = task.fileName + '.eml'
        }
      })
      downloadAttachment(this.$route.name, reqTasks, taskFile).then(() => {
        taskFile.percent = 100
        this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownResult(taskFile.taskQueueKey, {
          type: 'success',
          title: this.$t('text.success'),
          message: this.$t('components.fileDownload')
        })
      }).catch(() => {
        closeProgress()
      })
    }
  }
}
</script>
