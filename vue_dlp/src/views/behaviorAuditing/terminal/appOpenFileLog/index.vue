<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :os-type-filter="8" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <!-- 
        <SearchItem model-key="query.alarmType" :value="query.alarmType">
          <span>日志类型：</span>
          <el-select v-model="query.keyword2">
            <el-option v-for="item in logTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </SearchItem>
        -->
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('pages.fileName') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <SearchItem model-key="query.openType" :value="query.openType">
          <span>{{ $t('table.openType') }}：</span>
          <el-select v-model="query.openType" :placeholder="$t('text.select')" style="width: 150px;">
            <el-option :label="$t('pages.all')" value=""/>
            <el-option :label="$t('table.plusSoft1')" value="poi"/>
            <el-option :label="$t('table.plusSoft2')" value="tbs"/>
            <el-option label="WPS" value="wps"/>
            <el-option :label="$t('table.chosedApp')" value="other"/>
          </el-select>
        </SearchItem>
        <!-- 
        <SearchItem model-key="query.platformType" :value="query.platformType">
          <span>{{ $t('table.platformType') }}：</span>
          <el-select v-model="query.platformType" :placeholder="$t('text.select')" style="width: 150px;">
            <el-option :label="$t('pages.all')" value=""/>
            <el-option label="ios" value="ios"/>
            <el-option label="andriod" value="andriod"/>
            <el-option label="harmonyos" value="harmonyos"/>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.mobileType" :value="query.mobileType">
          <span>{{ $t('table.mobileType') }}：</span>
          <el-input v-model="query.mobileType" v-trim clearable style="width: 200px;" />
        </SearchItem> 
        -->
        <audit-log-exporter slot="append" v-permission="'374'" :request="handleExport"/>
        <!--<el-button slot="append" icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>-->
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'openTime' }" :multi-select="false" :row-data-api="rowDataApi" :custom-col="true" retain-pages @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.appOpenFileLogDetail')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.openTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fileName')">
            {{ rowDetail.fileName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.openType')">
            {{ openTypeFormatter(rowDetail,rowDetail.openType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.platformType')">
            {{ rowDetail.platformType }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.mobileType')">
            {{ rowDetail.mobileType }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLogPage, exportAppLogExcel } from '@/api/behaviorManage/application/appFileOpenLog'

export default {
  name: 'AppOpenFileLog',
  props: {
    logType: { // 日志类型：1程序使用日志 2窗口日志
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'openTime', label: 'time', width: '100', sort: true },
        // { prop: 'groupId', label: '所属分组', width: '100' },
        { prop: 'terminalName', label: 'terminalName', width: '100', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '100', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'fileName', label: 'fileName', width: '200' },
        // { prop: 'fileType', label: 'fileType', width: '200', formatter: this.fileTypeFormatter },
        { prop: 'openType', label: 'openType', width: '200', formatter: this.openTypeFormatter },
        { prop: 'platformType', label: 'platformType', width: '200' },
        { prop: 'mobileType', label: 'mobileType', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('375'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        fileName: '',
        fileType: '',
        openType: '',
        platformType: '',
        mobileType: '',
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      deleteable: false,
      rowDetail: {},
      dialogFormVisible: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    },
    logTypeMap() {
      const map = {}
      this.logTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getLogPage(searchQuery);
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    logTypeFormatter: function(row, data) {
      return this.logTypeMap[data]
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return exportAppLogExcel({ exportType, ...this.query })
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    fileTypeFormatter: function(data, row) {
      return data
    },
    openTypeFormatter: function(row, data) {
      if (data == 'poi') {
        return this.$t('table.plusSoft1')
      } else if (data == 'tbs') {
        return this.$t('table.plusSoft2')
      } else if (data == 'wps') {
        return 'WPS'
      } else if (data == 'other') {
        return this.$t('table.chosedApp')
      } else {
        return ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 100px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 280px;
}
</style>
