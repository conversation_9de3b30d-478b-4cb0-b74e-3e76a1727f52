<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.opType" :value="query.opType">
          <span>{{ $t('pages.operateType') }}：</span>
          <el-select v-model="query.opType" style="width: 150px;">
            <el-option v-for="item in fileOpTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.isWhiteList" :value="query.isWhiteList">
          <span>{{ $t('pages.usbType') }}：</span>
          <el-select v-model="query.isWhiteList" style="width: 150px;">
            <el-option :value="null" :label="$t('pages.all')"/>
            <el-option :value="1" :label="$t('pages.usbWhiteList')"/>
            <el-option :value="0" :label="$t('pages.noUsbWhiteList')"/>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.oriSource" :value="query.oriSource">
          <span>{{ $t('pages.fromPath') }}：</span>
          <el-input v-model="query.oriSource" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.targetSource" :value="query.targetSource">
          <span>{{ $t('pages.targetPath') }}：</span>
          <el-input v-model="query.targetSource" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.actionType" :value="query.actionType">
          <span>{{ $t('table.action') }}：</span>
          <el-select v-model="query.actionType" style="width: 150px">
            <el-option :label="$t('pages.all')" :value="null"/>
            <el-option :label="$t('pages.forbid')" :value="1"></el-option>
            <el-option :label="$t('pages.allow')" :value="0"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'243'" :request="handleExport"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" v-permission="'228'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'432'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :autoload="autoload"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      >
      </grid-table>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.usbFileLogDetails')"
      :visible.sync="dialogFormVisible"
      width="900px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.terminalGroup')">
            <department-detail
              :label="groupFormatter(rowDetail,rowDetail.groupId)"
              :search-id="rowDetail.groupId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.usbName')">
            <usb-device-detail
              :label="rowDetail.usbName"
              :child-data="rowDetail.usbDevice"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.pnpDeviceId')">
            {{ rowDetail.driveSerial }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.isWhiteList')">
            {{ isWhiteListFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.processName')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.operateType')">
            {{ fileOpTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fromFileSize')">
            {{ rowDetail.fileSize }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileName')">
            <el-button
              v-permission="'228'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!228'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fromPath')">
            {{ rowDetail.srcFilePath }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.targetPath')">
            {{ rowDetail.destFilePath }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { listAllUsbDevice } from '@/api/behaviorManage/hardware/usbConfig'
import { getLogPage, exportExcel, deleteLog } from '@/api/behaviorAuditing/terminal/usbFile'
import { deptNameFormatter, logSourceFormatter } from '@/utils/formatter'
import DepartmentDetail from '@/components/ShowDetail/DepartmentDetail';
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import UsbDeviceDetail from '@/components/ShowDetail/UsbDeviceDetail';
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo';
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'UsbFileLog',
  components: { UsbDeviceDetail, DepartmentDetail },
  mixins: [auditLogRouterMixin],
  props: {
  },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '180', sort: 'custom', formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        // { prop: 'terminalGroup', label: 'terminalGroup', width: '150', type: 'showDetail', searchType: 'department', searchParam: 'groupId', formatter: this.terminalGroupFormatter },
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'usbName', label: 'usbName', width: '150', type: 'showDetail', searchType: 'usbDevice', childData: 'usbDevice' },
        { prop: 'driveSerial', label: 'pnpDeviceId', width: '150' },
        { prop: 'isWhiteList', label: 'isWhiteList', width: '150', formatter: this.isWhiteListFormatter },
        { prop: 'processName', label: 'processName', width: '100' },
        { prop: 'fileOperaType', label: 'operateType', width: '100', formatter: this.fileOpTypeFormatter },
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'actionType', label: 'action', width: '130', formatter: this.actionFormatter },
        { prop: 'srcFilePath', label: 'fromPath', width: '150' },
        { prop: 'destFilePath', label: 'targetPath', width: '150' },
        { prop: 'fileSize', label: 'fromFileSize', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('228,272'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, isShow: () => this.hasPermission('228') },
            { label: 'detail', click: this.handleViewDetails, isShow: () => this.hasPermission('272') }
            // { label: 'viewOperaFilesForUSB', click: this.handleView, disabledFormatter: this.viewFormatter }
          ]
        }
      ],
      fileOpTypeOptions: [
        { label: this.$t('pages.allLinux'), value: null },
        { label: this.$t('pages.copyFile'), value: 1 },
        { label: this.$t('pages.cutFile'), value: 2 },
        { label: this.$t('pages.saveFile'), value: 3 }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        usbId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        opType: null,
        oriSource: '',
        targetSource: '',
        isWhiteList: null,
        actionType: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      tempTask: {},
      defaultTempTask: {
        backType: 19,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      usbOperaFileVisible: false,
      usbDeviceList: [{ id: null, usbName: this.$t('pages.all') }],
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined,
      autoload: true
    }
  },
  computed: {
    fileOpTypeMap() {
      const map = {}
      this.fileOpTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    this.loadUsbDeviceTList()
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    selectable(row, index) {
      return !this.downloadFormatter(row) || (this.$store.getters.auditingDeleteAble && this.hasPermission('432'))
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    loadUsbDeviceTList() {
      listAllUsbDevice().then(res => {
        this.usbDeviceList.push(...res.data)
      }).catch(error => {
        console.log(error)
      })
    },
    extChange(selections) {
      // 下拉框选择所有后缀时，取消其他后缀，选择了其他后缀，取消所有后缀
      if (selections) {
        // 最后一次选择是所有的话
        if (selections[selections.length - 1] == '') {
          selections.splice(0, selections.length, '')
        } else {
          selections.forEach((item, index) => {
            if (item == '') {
              selections.splice(index, 1)
            }
          })
        }
      }
    },
    fileOpTypeFormatter: function(row) {
      return this.fileOpTypeMap[row.fileOperaType]
    },
    isWhiteListFormatter: function(row, data) {
      let isWhiteList = ''
      if (row.isWhiteList == 1) {
        isWhiteList = this.$t('text.yes')
      } else {
        isWhiteList = this.$t('text.no')
      }
      return isWhiteList;
    },
    terminalGroupFormatter: function(row, data) {
      return this.groupFormatter(undefined, row.groupId, undefined)
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    rowDataApi1: function(option) {
      const searchQuery = Object.assign({}, this.usbQuery, option)
      return getLogPage(searchQuery)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleExport(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.allFileName ? row.allFileName : row.fileName
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    handleView(row) {
      this.usbOperaFileVisible = true
      this.usbQuery.usbId = row.usbId
      this.usbQuery.page = 1
      this.usbQuery.limit = 10
      this.$refs['usbOperaFileList'].execRowDataApi(this.usbQuery)
    },
    viewFormatter(data, btn) {
      return !data.usbId
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleViewDetails(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    groupFormatter(row, data, col) {
      console.log('index groupFormatter', row, data, col)
      return this.html2Escape(deptNameFormatter(row, data, col, undefined))
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '272', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-descriptions-item__label {
    min-width: 150px;
  }
  >>>.el-descriptions-item__content {
    min-width: 100px;
    max-width: 180px;
  }
</style>
