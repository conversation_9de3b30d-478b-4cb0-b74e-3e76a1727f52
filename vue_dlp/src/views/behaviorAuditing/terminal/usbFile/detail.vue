<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.usbFileLogDetails')"
    :visible.sync="dialogFormVisible"
    width="900px"
  >
    <div class="show-detail-panel">
      <el-descriptions class="margin-top" :column="2" size="" border>
        <el-descriptions-item :label="$t('table.operateTime')">
          {{ rowDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.terminalName')">
          <terminal-detail
            :label="rowDetail.terminalName"
            :search-id="rowDetail.terminalId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.user')">
          <user-detail
            :label="rowDetail.userName"
            :search-id="rowDetail.userId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.terminalGroup')">
          <department-detail
            :label="groupFormatter(rowDetail,rowDetail.groupId)"
            :search-id="rowDetail.groupId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.usbName')">
          <usb-device-detail
            :label="rowDetail.usbName"
            :child-data="rowDetail.usbDevice"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.pnpDeviceId')">
          {{ rowDetail.driveSerial }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.isWhiteList')">
          {{ isWhiteListFormatter(rowDetail) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.processName')">
          {{ rowDetail.processName }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.operateType')">
          {{ fileOpTypeFormatter(rowDetail) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.fromFileSize')">
          {{ rowDetail.fileSize }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.fileName')">
          <el-button
            v-permission="'228'"
            type="text"
            style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
            :disabled="!rowDetail.fileGuid"
            @click="handleDownload(rowDetail)"
          >
            {{ rowDetail.fileName }}
          </el-button>
          <span v-permission="'!228'">{{ rowDetail.fileName }}</span>
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.action')">
          {{ actionFormatter(rowDetail, rowDetail.actionType) }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.fromPath')">
          {{ rowDetail.srcFilePath }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.targetPath')">
          {{ rowDetail.destFilePath }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'228'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
  </el-dialog>
</template>
<script>
import DepartmentDetail from '@/components/ShowDetail/DepartmentDetail.vue';
import UsbDeviceDetail from '@/components/ShowDetail/UsbDeviceDetail.vue';
import { deptNameFormatter } from '@/utils/formatter';
import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'UsbFileLogDetail',
  components: { UsbDeviceDetail, DepartmentDetail, AuditFileDownloader },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      selection: [],
      defaultTempTask: {
        backType: 19,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      fileOpTypeOptions: [
        { label: this.$t('pages.allLinux'), value: null },
        { label: this.$t('pages.copyFile'), value: 1 },
        { label: this.$t('pages.cutFile'), value: 2 },
        { label: this.$t('pages.saveFile'), value: 3 }
      ]
    }
  },
  computed: {
    fileOpTypeMap() {
      const map = {}
      this.fileOpTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    groupFormatter(row, data, col) {
      console.log('detail groupFormatter', row, data, col)
      return this.html2Escape(deptNameFormatter(row, data, col, undefined))
    },
    isWhiteListFormatter: function(row, data) {
      let isWhiteList = ''
      if (row.isWhiteList == 1) {
        isWhiteList = this.$t('text.yes')
      } else {
        isWhiteList = this.$t('text.no')
      }
      return isWhiteList;
    },
    fileOpTypeFormatter: function(row) {
      return this.fileOpTypeMap[row.fileOperaType]
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    handleDownload: function(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      tempTask.fileName = row.allFileName ? row.allFileName : row.fileName
      return tempTask
    }
  }
}
</script>
