<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.eventType" :value="query.eventType">
          <span>{{ $t('table.eventType') }}:</span>
          <el-select v-model="query.eventType" clearable style="width: 150px;">
            <el-option :key="1" :value="1" :label="$t('pages.insert1')"/>
            <el-option :key="2" :value="2" :label="$t('pages.pullOut')"/>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.devTypeId" :value="query.devTypeId">
          <span>{{ $t('pages.devType') }}：</span>
          <el-select
            v-model="query.devTypeId"
            filterable
            clearable
            allow-create
            style="width: 150px;"
          >
            <el-option v-for="(value, key) in deviceTypeMap" :key="key" :label="value" :value="key" >
            </el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'244'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'431'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('431')"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.usbEventDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.identificationTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.eventType')">
            {{ eventFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.devType')">
            {{ rowDetail.deviceType }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.interfaceKey')">
            {{ rowDetail.usbInterfaceKey }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.devDesc')">
            {{ rowDetail.deviceDesc }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { deleteLog, exportExcel, getLogPage } from '@/api/behaviorAuditing/terminal/usbEvent'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'UsbEvent',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'identificationTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        // { prop: 'groupId', label: '所属分组', width: '100' },
        { prop: 'eventType', label: 'eventType', width: '100', formatter: this.eventFormatter },
        { prop: 'deviceType', label: 'devType', width: '100' },
        { prop: 'usbInterfaceKey', label: 'interfaceKey', width: '100' },
        { prop: 'deviceDesc', label: 'devDesc', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('271'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      eventTypeOptions: [
        { label: this.$t('pages.unknownType'), value: 0 },
        { label: this.$t('pages.insert1'), value: 1 },
        { label: this.$t('pages.pullOut'), value: 2 }
      ],
      deviceTypeMap: {
        0: this.$t('pages.devType25'),
        1: this.$t('pages.devType2'),
        2: this.$t('pages.devType6'),
        3: this.$t('pages.devType5'),
        4: this.$t('pages.devType3'),
        5: this.$t('pages.devType4'),
        6: this.$t('pages.devType7'),
        7: this.$t('pages.devType8'),
        8: this.$t('pages.devType9'),
        9: this.$t('pages.devType10'),
        10: this.$t('pages.devType1'),
        11: this.$t('pages.devType11'),
        12: this.$t('pages.devType12'),
        23: this.$t('pages.devType23'),
        24: this.$t('pages.devType26')
      },
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        devTypeId: null,
        eventType: null,
        deleteRecords: [],
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      selection: [],
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  computed: {
    eventTypeMap() {
      const map = {}
      this.eventTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    eventFormatter: function(row) {
      return this.eventTypeMap[row.eventType]
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleExport(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '271', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
