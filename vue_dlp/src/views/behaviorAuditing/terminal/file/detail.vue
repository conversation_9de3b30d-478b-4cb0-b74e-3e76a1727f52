<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.fileOperateDetails')"
    :visible.sync="dialogFormDetailVisible"
    width="800px"
  >
    <div class="show-detail-panel">
      <el-descriptions class="margin-top" :column="2" size="" border>
        <el-descriptions-item :label="$t('table.operateTime')">
          {{ rowDetail.opTimeStr }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.terminalName')">
          <terminal-detail
            :label="rowDetail.terminalName"
            :search-id="rowDetail.terminalId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.user')">
          <user-detail
            :label="rowDetail.userName"
            :search-id="rowDetail.userId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.processName')">
          {{ rowDetail.processName }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.operateType')">
          {{ fileOpTypeFormatter(rowDetail) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.operateObject')">
          <el-button
            v-if="hasPermission('213')"
            type="text"
            style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
            :disabled="!rowDetail.fileGuid"
            @click="handleLoadDown(rowDetail)"
          >
            {{ rowDetail.fileName }}
          </el-button>
          <span v-else>{{ rowDetail.fileName }}</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.srcFileCreateTime')">
          {{ srcTimeFormatter(rowDetail, rowDetail.srcFileCreateTime) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.srcFileModifyTime')">
          {{ srcTimeFormatter(rowDetail, rowDetail.srcFileModifyTime) }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.fromFileSize')">
          {{ rowDetail.srcFileSize }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('pages.fromPath')">
          {{ filePath1Formatter(rowDetail, rowDetail.fileOpPath1) }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('pages.targetPath')">
          {{ filePath2Formatter(rowDetail, rowDetail.fileOpPath2) }}
        </el-descriptions-item>

      </el-descriptions>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'FileOpLogDetail',
  data() {
    return {
      dialogFormDetailVisible: false,
      rowDetail: {},
      fileOpTypeOptions: [
        { label: this.$t('pages.allLinux'), value: null },
        { label: this.$t('pages.fileCreate'), value: 1 },
        { label: this.$t('pages.fileRename'), value: 2 },
        { label: this.$t('pages.fileDelete'), value: 3 },
        { label: this.$t('pages.fileCopy'), value: 4 },
        { label: this.$t('pages.openFile'), value: 17 },
        { label: this.$t('pages.fileEdit'), value: 18 }        /*, { label: '本地备份 ', value: 32 },
        { label: '远程备份', value: 33 },
        { label: '清除文件压缩属性 ', value: 48 },
        { label: '清除文件加密属性', value: 49 },
        { label: '目录创建', value: 129 },
        { label: '目录重命名', value: 130 },
        { label: '目录删除', value: 131 }*/
      ]
    }
  },
  computed: {
    fileOpTypeMap() {
      const map = {}
      this.fileOpTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  methods: {
    show(data) {
      this.dialogFormDetailVisible = true
      this.rowDetail = data
    },
    fileOpTypeFormatter: function(row) {
      if (row.fileOpType == 4) {
        if (row.diskType1 == 3 && row.diskType2 == 3) {
          return this.$t('pages.fileLog_Msg')
        } else if (row.diskType1 == 3) {
          return this.$t('pages.fileLog_Msg1')
        } else if (row.diskType2 == 3) {
          return this.$t('pages.fileLog_Msg2')
        } else if (row.diskType1 == 0) {
          return this.$t('pages.fileLog_Msg3')
        } else if (row.diskType2 == 0) {
          return this.$t('pages.fileLog_Msg4')
        } else {
          return this.$t('pages.fileLog_Msg5')
        }
      } else {
        return this.fileOpTypeMap[row.fileOpType]
      }
    },
    srcTimeFormatter(row, data) {
      return data == '1900-01-01 00:00:00' ? '' : data
    },
    filePath1Formatter: function(row, data) {
      return '（' + this.diskTypeMap[row.diskType1] + '）' + data
    },
    filePath2Formatter: function(row, data) {
      if (data) {
        return '（' + this.diskTypeMap[row.diskType2] + '）' + data
      }
    },
    diskTypeMap() {
      const map = {}
      this.diskTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    },
    handleLoadDown(row) {
      this.$emit('load-down', row)
    }
  }
}
</script>
<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 200px;
}
</style>
