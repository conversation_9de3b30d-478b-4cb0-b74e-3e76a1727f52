<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem v-if="type==1" model-key="query.fileOpType" :value="query.fileOpType">
          <span>{{ $t('pages.operateType') }}：</span>
          <el-select v-model="query.fileOpType" style="width: 150px;">
            <el-option v-for="item in fileOpTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('pages.operateObject') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem v-if="type==1" model-key="query.diskType" :value="query.diskType">
          <span>{{ $t('pages.mediaType') }}：</span>
          <el-select v-model="query.diskType" style="width: 150px;">
            <el-option v-for="item in diskTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem v-if="type==1" model-key="query.extList" :value="query.extList">
          <span>{{ $t('pages.process_Msg5') }}：</span>
          <el-select
            v-model="query.extList"
            collapse-tags
            allow-create
            filterable
            multiple
            :title="query.extList.join(', ')"
            style="width: 180px;"
            @change="extChange"
          >
            <el-option v-for="item in extListOptions" :key="item.value" :label="item.label" :value="item.value" ></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'235'" :request="handleExport"/>
        <audit-file-downloader
          ref="auditFileDownloader"
          slot="append"
          v-permission="'213'"
          :selection="selection"
          :before-download="beforeDownload"
        />
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'439'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :multi-select="true"
        :selectable="selectable"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        :autoload="autoload"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.fileOperateDetails')"
      :visible.sync="dialogFormDetailVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.opTimeStr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.processName')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.operateType')">
            {{ fileOpTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.operateObject')">
            <el-button
              v-permission="'213'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!213'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.srcFileCreateTime')">
            {{ srcTimeFormatter(rowDetail, rowDetail.srcFileCreateTime) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.srcFileModifyTime')">
            {{ srcTimeFormatter(rowDetail, rowDetail.srcFileModifyTime) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fromFileSize')">
            {{ rowDetail.srcFileSize }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.fromPath')">
            {{ filePath1Formatter(rowDetail, rowDetail.fileOpPath1) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('pages.targetPath')">
            {{ filePath2Formatter(rowDetail, rowDetail.fileOpPath2) }}
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/behaviorAuditing/terminal/file'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'FileLog',
  mixins: [auditLogRouterMixin],
  props: {
    type: { // 文档类型 1.所有操作文档 2.usb外发文档
      type: [String, Number],
      default: 1
    }
  },
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: 'custom', formatter: this.timeFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        // { prop: 'groupId', label: '所属分组', width: '100' },
        { prop: 'processName', label: 'processName', width: '100' },
        { prop: 'fileOpType', label: 'operateType', width: '100', formatter: this.fileOpTypeFormatter },
        { prop: 'fileName', label: 'operateObject', width: '200' },
        { prop: 'fileOpPath1', label: 'fromPath', width: '150', formatter: this.filePath1Formatter },
        { prop: 'fileOpPath2', label: 'targetPath', width: '150', formatter: this.filePath2Formatter },
        { prop: 'srcFileSize', label: 'fromFileSize', width: '150' },
        { prop: 'srcFileCreateTime', label: 'srcFileCreateTime', width: '150', formatter: this.srcTimeFormatter },
        { prop: 'srcFileModifyTime', label: 'srcFileModifyTime', width: '150', formatter: this.srcTimeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('213,276'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, isShow: () => this.hasPermission('213') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('276') }
          ]
        }
      ],
      fileOpTypeOptions: [
        { label: this.$t('pages.allLinux'), value: null },
        { label: this.$t('pages.fileCreate'), value: 1 },
        { label: this.$t('pages.fileRename'), value: 2 },
        { label: this.$t('pages.fileDelete'), value: 3 },
        { label: this.$t('pages.fileCopy'), value: 4 },
        { label: this.$t('pages.openFile'), value: 17 },
        { label: this.$t('pages.fileEdit'), value: 18 }        /*, { label: '本地备份 ', value: 32 },
        { label: '远程备份', value: 33 },
        { label: '清除文件压缩属性 ', value: 48 },
        { label: '清除文件加密属性', value: 49 },
        { label: '目录创建', value: 129 },
        { label: '目录重命名', value: 130 },
        { label: '目录删除', value: 131 }*/
      ],
      diskTypeOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.localDisk'), value: 0 },
        { label: this.$t('pages.mobileMedia'), value: 2 },
        { label: this.$t('pages.netFile'), value: 3 },
        { label: this.$t('pages.cd2'), value: 4 }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        fileOpType: null,
        diskType: null,
        fileName: '',
        type: this.type,
        extList: [''],
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      extListOptions: [
        { label: this.$t('pages.all'), value: '' },
        { label: '.doc', value: '.doc' },
        { label: '.docx', value: '.docx' },
        { label: '.pdf', value: '.pdf' },
        { label: '.ppt', value: '.ppt' },
        { label: '.pptx', value: '.pptx' },
        { label: '.txt', value: '.txt' },
        { label: '.rtf', value: '.rtf' },
        { label: '.xls', value: '.xls' },
        { label: '.xlsx', value: '.xlsx' }
      ],
      showTree: true,
      percentOptions: [
        { status: 0, percent: 0 },
        { status: 1, percent: 1 },
        { status: 5, percent: 50 },
        { status: 6, percent: 100 }
      ],
      selection: [],
      tempTask: {},
      defaultTempTask: {
        backType: 1,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      dialogFormDetailVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  computed: {
    fileOpTypeMap() {
      const map = {}
      this.fileOpTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    },
    // 计算 extListOptions 选项的 map ，用于判断是否添加新选项
    extMap() {
      return this.extListOptions.reduce((map, cur) => {
        map[cur.value] = true
        return map
      }, {})
    },
    diskTypeMap() {
      const map = {}
      this.diskTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    extChange(selections) {
      // 获取最后一个选中的值
      const ext = [...selections].pop()
      // 新创建的选项，则添加到 extListOptions 中
      if (ext.trim() && !this.extMap[ext]) {
        const option = { label: ext, value: ext }
        this.extListOptions.push(option)
      }
      // 最后选中的值是 所有，则只保留 所有
      if (ext == '') {
        selections.splice(0, selections.length, '')
      } else {
        // 否则删除 所有 选项
        const allIndex = selections.indexOf('')
        allIndex >= 0 && selections.splice(allIndex, 1)
      }
    },
    filePath1Formatter: function(row, data) {
      return '（' + this.diskTypeMap[row.diskType1] + '）' + data
    },
    filePath2Formatter: function(row, data) {
      if (data) {
        return '（' + this.diskTypeMap[row.diskType2] + '）' + data
      }
    },
    fileOpTypeFormatter: function(row) {
      if (row.fileOpType == 4) {
        if (row.diskType1 == 3 && row.diskType2 == 3) {
          return this.$t('pages.fileLog_Msg')
        } else if (row.diskType1 == 3) {
          return this.$t('pages.fileLog_Msg1')
        } else if (row.diskType2 == 3) {
          return this.$t('pages.fileLog_Msg2')
        } else if (row.diskType1 == 0) {
          return this.$t('pages.fileLog_Msg3')
        } else if (row.diskType2 == 0) {
          return this.$t('pages.fileLog_Msg4')
        } else {
          return this.$t('pages.fileLog_Msg5')
        }
      } else {
        return this.fileOpTypeMap[row.fileOpType]
      }
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    selectable(row, index) {
      return !!(row.fileGuid && row.devId) || (this.$store.getters.auditingDeleteAble && this.hasPermission('439'))
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    timeFormatter: function(row, data) {
      return logSourceFormatter(row, row.opTimeStr)
    },
    srcTimeFormatter(row, data) {
      return data == '1900-01-01 00:00:00' ? '' : data
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormDetailVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '276', this.query.searchReport, undefined, 'opTimeStr')
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-descriptions-item__label {
  min-width: 150px;
}
>>>.el-descriptions-item__content {
  min-width: 100px;
  max-width: 180px;
}
</style>
