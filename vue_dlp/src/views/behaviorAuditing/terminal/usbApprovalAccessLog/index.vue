<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.approvalResult" :value="query.approvalResult">
          <span>{{ $t('pages.approvalResult') }}:</span>
          <el-select v-model="query.approvalResult" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option v-for="(label, value) in resultOptions" :key="value" :label="label" :value="value" />
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.devType" :value="query.devType">
          <span>{{ $t('pages.devType') }}:</span>
          <el-select v-model="query.devType" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option v-for="(label, value) in devTypeOptions" :key="value" :label="label" :value="value" />
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'319'" :request="handleExport"/>
        <el-button v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'430'" :disabled="!deleteable" size="mini" style="margin-left:10px" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('430')"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.usbApprovalAccessLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.approvalTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.volumeName')">
            {{ rowDetail.volumeName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.model')">
            {{ rowDetail.model }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.devType')">
            {{ devTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.driverType')">
            {{ driverTypeFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.approvalResult')">
            {{ resultFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.pid')">
            {{ rowDetail.pid || '' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.vid')">
            {{ rowDetail.vid || '' }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.deviceSerialNumber')">
            {{ rowDetail.srcPnpDeviceId || '' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getPage, exportUSBApprovalAccessLog, deleteLog } from '@/api/behaviorAuditing/terminal/usbApprovalLog'
import { enableStgDelete } from '@/utils'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'UsbApprovalAccessLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'approvalTime', width: '150', sort: true },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'volumeName', label: 'volumeName', width: '100' },
        { prop: 'devType', label: 'devType', width: '200', formatter: this.devTypeFormatter },
        { prop: 'driverType', label: 'driverType', width: '200', formatter: this.driverTypeFormatter },
        { prop: 'approvalResult', label: 'approvalResult', width: '200', formatter: this.resultFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('270'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        approvalResult: null,
        devType: null,
        driverType: null,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      resultOptions: {
        0: this.$t('pages.refuse'), 1: this.$t('pages.agree')
      },
      devTypeOptions: {
        0: this.$t('pages.dictionary_Msg19'), 1: this.$t('pages.dictionary_Msg21')
      },
      driverTypeOptions: {
        1: this.$t('pages.driverType1'), 2: this.$t('pages.driverType2')
      },
      showTree: true,
      rowDetail: {},
      dialogFormVisible: false,
      queryVideoMethod: undefined,
      deleteable: false,
      termsInfo: [] // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      return getPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    devTypeFormatter: function(row, data) {
      let devType = '';
      if (row.devType === 0) {
        // 普通U盘
        devType = this.$t('pages.dictionary_Msg19')
      } else if (row.devType === 1) {
        // 专用U盘
        devType = this.$t('pages.dictionary_Msg21')
      } else if (row.devType === 2) {
        // 解密Key
        devType = this.$t('pages.decryptKey')
      } else if (row.devType === 3) {
        // 外发U盘
        devType = this.$t('pages.outFileUDisk')
      } else if (row.devType === 4) {
        // 安全U盘
        devType = this.$t('pages.safeUDisk')
      } else {
        // 定制专业U盘
        devType = this.$t('pages.customProfessionalUDisk')
      }
      return devType
    },
    driverTypeFormatter: function(row, data) {
      let driverType = '';
      if (row.driverType === 1) {
        driverType = this.$t('pages.driverType1')
      } else {
        driverType = this.$t('pages.driverType2')
      }
      return driverType
    },
    resultFormatter: function(row, data) {
      let result = '';
      if (row.approvalResult === 0) {
        result = this.$t('pages.accessReject')
      } else {
        result = this.$t('pages.accessAccept')
      }
      return result
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportUSBApprovalAccessLog(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDelete = this.gridTable.getSelectedDatas()
        deleteLog(toDelete).then(respond => {
          this.gridTable.deleteRowData(toDelete)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '270')
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
