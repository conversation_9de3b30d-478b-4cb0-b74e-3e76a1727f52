<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.ipArr" :value="query.ipArr">
          <span>{{ $t('pages.serverIp') }}：</span>
          <el-input v-model="query.ipArr" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.cmdData" :value="query.cmdData">
          <span>{{ $t('pages.commandLine') }}：</span>
          <el-input v-model="query.cmdData" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'347'" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'458'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table ref="logList" row-key="logId" :col-model="colModel" :default-sort="{ prop: 'createTime' }" :multi-select="$store.getters.auditingDeleteAble && hasPermission('458')" :row-data-api="rowDataApi" :sortable="sortable" :after-load="afterLoad" :custom-col="true" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.telnetMonitorLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.operateTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.controlCommand')">
            {{ cmdTypeFormatter(rowDetail,rowDetail.cmdType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.serverIp1')">
            {{ rowDetail.ipArr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.commandLine')">
            {{ rowDetail.cmdData }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.telnetLoginUser')">
            {{ rowDetail.loginUser }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.isBlock')">
            {{ actionTypeFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.processName1')">
            {{ rowDetail.processName }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/behaviorManage/application/telnetMonitorLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'TelnetMonitorLog',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '100', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '100', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'cmdType', label: 'controlCommand', width: '150', formatter: this.cmdTypeFormatter },
        { prop: 'ipArr', label: 'serverIp1', width: '150' },
        { prop: 'cmdData', label: 'commandLine', width: '150' },
        { prop: 'loginUser', label: 'telnetLoginUser', width: '150' },
        { prop: 'actionType', label: 'isBlock', width: '150', formatter: this.actionTypeFormatter },
        { prop: 'processName', label: 'processName1', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('294'),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        ipArr: null,
        cmdData: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      selection: [],
      cmdTypes: { 1: this.$t('pages.send'), 3: this.$t('pages.connect') },
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    cmdTypeFormatter: function(row, data) {
      return this.cmdTypes[data]
    },
    actionTypeFormatter: function(row, data) {
      return data == 1 ? this.$t('text.yes') : data == 0 ? this.$t('text.no') : ''
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportExcel(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '294', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
