<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.printLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.opTimeStr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize1')">
            {{ (rowDetail.fileSize / 1024.0).toFixed(2) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.totalPagesPrinted')">
            {{ rowDetail.printedPages }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.numberCopiesPrinted')">
            {{ rowDetail.printedCopies }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fileName2')">
            <el-button
              v-permission="'227'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleDownload(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!227'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.printer')">
            {{ rowDetail.printerName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.printerIP')">
            {{ rowDetail.printIpAddr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'227'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
  </div>
</template>
<script>

import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'PrintLogDetail',
  components: { AuditFileDownloader },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      selection: [],
      defaultTempTask: {
        backType: 7,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      }
    }
  },
  computed: {

  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    },
    handleDownload: function(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      tempTask.fileName = row.fileName
      tempTask.terminalId = row.terminalId
      return tempTask
    }
  }
}
</script>
