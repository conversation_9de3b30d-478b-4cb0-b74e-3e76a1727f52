<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('table.fileName2') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.actionType" :value="query.actionType">
          <span>{{ $t('table.action') }}：</span>
          <el-select v-model="query.actionType" style="width: 150px">
            <el-option :label="$t('pages.all')" :value="null"/>
            <el-option :label="$t('pages.forbid')" :value="1"></el-option>
            <el-option :label="$t('pages.allow')" :value="0"></el-option>
          </el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'236'" :request="exportFunc"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" v-permission="'227'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'425'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :selectable="selectable || ($store.getters.auditingDeleteAble && hasPermission('425'))"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        :autoload="autoload"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.printLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.opTimeStr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.maxFileSize1')">
            {{ (rowDetail.fileSize / 1024.0).toFixed(2) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.totalPagesPrinted')">
            {{ rowDetail.printedPages }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.numberCopiesPrinted')">
            {{ rowDetail.printedCopies }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.fileName2')">
            <el-button
              v-permission="'227'"
              type="text"
              style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
              :disabled="!rowDetail.fileGuid"
              @click="handleLoadDown(rowDetail)"
            >
              {{ rowDetail.fileName }}
            </el-button>
            <span v-permission="'!227'">{{ rowDetail.fileName }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.printer')">
            {{ rowDetail.printerName }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.printerIP')">
            {{ rowDetail.printIpAddr }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.action')">
            {{ actionFormatter(rowDetail, rowDetail.actionType) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/behaviorAuditing/terminal/printer'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'PrintLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'time', width: '150', sort: 'custom', formatter: this.timeFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        // { prop: 'groupId', label: '所属分组', width: '100' },
        { prop: 'fileName', label: 'fileName2', width: '150' },
        { prop: 'fileSize', label: 'maxFileSize1', width: '150', formatter: row => { return (row.fileSize / 1024.0).toFixed(2) } },
        { prop: 'printedPages', label: 'totalPagesPrinted', width: '150' },
        { prop: 'printedCopies', label: 'numberCopiesPrinted', width: '150' },
        { prop: 'printerName', label: 'printerName', width: '150' },
        { prop: 'printIpAddr', label: 'printerIP', width: '150' },
        { prop: 'actionType', label: 'action', width: '150', formatter: this.actionFormatter },
        { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right', hidden: !this.hasPermission('227,267'),
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter, isShow: () => this.hasPermission('227') },
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('267') }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        fileName: null,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList',
        actionType: undefined
      },
      showTree: true,
      tempTask: {},
      defaultTempTask: {
        backType: 7,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    selectable(row, index) {
      return !this.downloadFormatter(row) || this.hasPermission('425')
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.fileName
      this.tempTask.terminalId = row.terminalId
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    timeFormatter: function(row, data) {
      return logSourceFormatter(row, row.opTimeStr)
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '267', this.query.searchReport, undefined, 'opTimeStr')
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    }
  }
}
</script>
