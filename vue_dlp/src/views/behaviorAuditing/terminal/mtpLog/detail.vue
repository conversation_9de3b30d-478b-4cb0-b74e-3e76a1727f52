<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.mtpLogMsg')"
    :visible.sync="dialogFormVisible"
    width="700px"
  >
    <div class="show-detail-panel">
      <el-descriptions class="margin-top" :column="2" size="" border>
        <el-descriptions-item :label="$t('table.fileSentTime')">
          {{ rowDetail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.terminalName')">
          <terminal-detail
            :label="rowDetail.terminalName"
            :search-id="rowDetail.terminalId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.user')">
          <user-detail
            :label="rowDetail.userName"
            :search-id="rowDetail.userId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.fileName')">
          <el-button
            v-permission="'358'"
            type="text"
            style="margin-bottom: 0;padding: 0;white-space:initial;text-align: left"
            :disabled="!rowDetail.fileGuid"
            @click="handleDownload(rowDetail)"
          >
            {{ rowDetail.fileName }}
          </el-button>
          <span v-permission="'!358'">{{ rowDetail.fileName }}</span>
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.action')">
          {{ actionFormatter(rowDetail, rowDetail.actionType) }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.fileSrcPath')">
          {{ rowDetail.srcFilePath }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.targetPath')">
          {{ rowDetail.destFilePath }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.maxFileSize1')">
          {{ rowDetail.fileSize }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'MtpFileLogDetail',
  data() {
    return {
      dialogFormVisible: false,
      tempTask: {},
      rowDetail: {}
    }
  },
  computed: {
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    handleDownload: function(row) {
      this.$emit('downloadAuditLogFile', row)
    },
    actionFormatter: function(row, data) {
      if (data == 1) {
        return this.$t('pages.forbid')
      } else if (data == 0) {
        return this.$t('pages.allow')
      } else {
        return ''
      }
    }
  }
}
</script>
