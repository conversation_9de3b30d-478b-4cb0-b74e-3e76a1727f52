<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <!-- 
        <SearchItem model-key="query.keyword2" :value="query.keyword2">
          <span>日志类型：</span>
          <el-select v-model="query.keyword2">
            <el-option v-for="item in logTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </SearchItem> -->
        <SearchItem model-key="query.logDesc" :value="query.logDesc">
          <span>{{ $t('pages.fileDescription') }}：</span>
          <el-input v-model="query.logDesc" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="logType === 1 ? '323' : '322'" :request="handleExport"/>
        <audit-log-delete
          v-if="$store.getters.auditingDeleteAble"
          slot="append"
          v-permission="logType === 1 ? '435' : '434'"
          :selection="selection"
          :delete-log="deleteLog"
          :table-getter="gridTable"
        />
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && (logType ===1 ? hasPermission('435') : hasPermission('434'))"
        :row-data-api="rowDataApi"
        retain-pages
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      >
      </grid-table>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="1 === logType ? $t('pages.programDetails') : $t('pages.windowTitleDetails')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.logTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.event')">
            {{ logTypeFormatter(rowDetail, rowDetail.logType) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="1 === logType" :label="$t('table.processName1')">
            {{ rowDetail.objName }}
          </el-descriptions-item>
          <el-descriptions-item v-if="1 === logType" :label="$t('table.processID')">
            {{ processIdFormatter(rowDetail, rowDetail.processId) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.fileDescription')">
            {{ rowDetail.logDesc }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, getTitlePage, deleteLog, exportAppLogExcel, exportTitleExcel } from '@/api/behaviorManage/application/appLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'AppLog',
  props: {
    logType: { // 日志类型：1程序使用日志 2窗口日志
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '100', sort: 'custom', formatter: row => logSourceFormatter(row, row.logTime) },
        { prop: 'terminalName', label: 'terminalName', width: '100', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '100', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'logType', label: 'event', width: '100', formatter: this.logTypeFormatter },
        { prop: 'objName', label: 'processName1', width: '100', hidden: 1 !== this.logType },
        { prop: 'processId', label: 'processID', width: '100', hidden: 1 !== this.logType, formatter: this.processIdFormatter },
        { prop: 'logDesc', label: 'fileDescription', width: '200' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: this.operateHidden(),
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      logTypeOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.windowTitle'), value: 8 },
        { label: this.$t('pages.programOn'), value: 9 },
        { label: this.$t('pages.programClose'), value: 10 }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        logDesc: '',
        keyword2: null,
        keyword3: this.logType,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        searchReport: 1,
        dlpTotal: 0,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      selection: [],
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  computed: {
    logTypeMap() {
      const map = {}
      this.logTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    addViewVideoBtn(this)
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return 1 === this.logType ? getLogPage(searchQuery) : getTitlePage(searchQuery);
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      this.query.dlpTotal = 0
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    logTypeFormatter: function(row, data) {
      return this.logTypeMap[data]
    },
    processIdFormatter: function(row, data) {
      if (data == 0 || data == -1) {
        return ''
      } else {
        return data
      }
    },
    operateHidden() {
      // 274 程序使用记录-查看详情  279 窗口标题记录-查看详情
      return (1 === this.logType ? !this.hasPermission('274') : !this.hasPermission('273'))
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      if (1 === this.logType) {
        return exportAppLogExcel(formData)
      } else {
        return exportTitleExcel(formData)
      }
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, () => this.operateHidden, this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
