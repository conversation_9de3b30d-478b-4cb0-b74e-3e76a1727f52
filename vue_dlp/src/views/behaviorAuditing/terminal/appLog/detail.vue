<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="1 === logType ? $t('pages.programDetails') : $t('pages.windowTitleDetails')"
    :visible.sync="dialogFormVisible"
    width="800px"
  >
    <div class="show-detail-panel">
      <el-descriptions class="margin-top" :column="2" size="" border>
        <el-descriptions-item :label="$t('table.time')">
          {{ rowDetail.logTime }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.terminalName')">
          <terminal-detail
            :label="rowDetail.terminalName"
            :search-id="rowDetail.terminalId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('pages.user')">
          <user-detail
            :label="rowDetail.userName"
            :search-id="rowDetail.userId"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('table.event')">
          {{ logTypeFormatter(rowDetail, rowDetail.logType) }}
        </el-descriptions-item>
        <el-descriptions-item v-if="1 === logType" span="2" :label="$t('table.processName1')">
          {{ rowDetail.objName }}
        </el-descriptions-item>
        <el-descriptions-item span="2" :label="$t('table.fileDescription')">
          {{ rowDetail.logDesc }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AppLogDetail',
  props: {
    logType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      dialogFormVisible: false,
      rowDetail: {},
      logTypeOptions: [
        { label: this.$t('pages.all'), value: null },
        { label: this.$t('pages.windowTitle'), value: 8 },
        { label: this.$t('pages.programOn'), value: 9 },
        { label: this.$t('pages.programClose'), value: 10 }
      ]
    }
  },
  computed: {
    logTypeMap() {
      const map = {}
      this.logTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  methods: {
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
    },
    logTypeFormatter: function(row, data) {
      return this.logTypeMap[data]
    }
  }
}
</script>
