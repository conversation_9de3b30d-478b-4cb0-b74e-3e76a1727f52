<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.severity" :value="query.severity">
          <span>{{ $t('pages.severity') }}：</span>
          <el-select v-model="query.severity" clearable :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(value, key) in severityOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.strategyName" :value="query.strategyName">
          <span>{{ $t('pages.violationStrategy') }}：</span>
          <el-input v-model="query.strategyName" v-trim clearable style="width: 150px;"></el-input>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'366'" :multi-dataset="hasPermission('206')" :request="handleExport"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'470'" :selection="selection" :delete-log="deleteIssueLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="$store.getters.auditingDeleteAble && hasPermission('470')"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <drip-log-detail ref="dripLogDetailDlg"></drip-log-detail>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import {
  getIssueLogPage, deleteIssueLog, getMatchLogPage, exportIssueExcel
} from '@/api/behaviorAuditing/issue/dripLog'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'
import { getDictLabel } from '@/utils/dictionary'
import moment from 'moment'
import DripLogDetail from './detailDlg'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'

export default {
  name: 'DripIssueLog',
  components: { DripLogDetail },
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '100', sort: true, formatter: logSourceFormatter },
        { prop: 'terminalName', label: 'terminalName', width: '100', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: 'severity', label: 'severity', width: '100', formatter: this.severityFormatter },
        { prop: 'strategyId', label: 'contentStg', width: '100', formatter: (row) => { return row.strategy } },
        { prop: 'senderIp', label: 'sender1', width: '100', formatter: this.senderFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('206'),
          buttons: [{ label: 'detail', click: this.handleView }]
        }
      ],
      severityOptions: {
        1: this.$t('pages.severityOptions1'),
        2: this.$t('pages.severityOptions2'),
        3: this.$t('pages.severityOptions3'),
        4: this.$t('pages.severityOptions4')
      },
      // lossTypeOptions: getLossTypeDict(),
      lossTypeOptions: [],
      chatToolOptions: [],
      lossTypeValue: undefined,
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        lossType: undefined,
        severity: undefined,
        strategyName: undefined,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        lossType: undefined,
        severity: undefined,
        violator: '',
        content: '',
        occurTime: ''
      },
      textTitle: this.$t('pages.eventDetails'),
      showTree: true,
      selection: [],
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  computed: {
    chatTypeMap() {
      const map = {}
      this.chatToolOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    this.getSensitiveLossType()
    addViewVideoBtn(this, () => !this.$store.getters.desensitizeContentAble)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteIssueLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        this.lossTypeOptions = res.data.map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
      })
    },
    chatTypeFormatter: function(row) {
      return this.chatTypeMap[row.chatType]
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getIssueLogPage(searchQuery)
    },
    subRowDataApi: function(option) {
      const issueLogId = option.id
      const issueLogRowData = this.gridTable().getDataByParam('id', issueLogId)
      delete option.id
      option.guid = issueLogRowData.guid
      option.endDate = moment(issueLogRowData.createTime).format('YYYY-MM-DD')
      return getMatchLogPage(option)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleView(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      this.temp.searchReport = this.query.searchReport
      // this.temp.content.replace('occurTime', this.temp.occurTime)
      this.$refs['dripLogDetailDlg'].show(this.temp)
    },
    severityFormatter: function(row, data) {
      return this.severityOptions[data]
    },
    lossTypeFormatter: function(row, data) {
      const label = getDictLabel(this.lossTypeOptions, data)
      return label == null ? '' : label
    },
    senderFormatter: function(row, data) {
      let result = ''
      if (row.sender) result += row.sender
      if (row.senderIp) {
        result += result.length > 0 ? ('(' + row.senderIp + ')') : row.senderIp
      }
      return result
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      // this.$refs.exportDlg.show(this.gridTable().getSelectedIds())
      return exportIssueExcel({ exportType, ...this.query })
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '206', this.query.searchReport)
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
