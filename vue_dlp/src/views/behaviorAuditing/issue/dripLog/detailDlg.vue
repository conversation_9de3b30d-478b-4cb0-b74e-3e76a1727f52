<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.dripIssueLogDetails')"
      :visible.sync="detailDlgVisible"
      width="800px"
      @close="closeDlg"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="temp.terminalName"
              :search-id="temp.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="temp.userName"
              :search-id="temp.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.severity')">
            {{ getSeverityLabel(temp.severity) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.contentStg')">
            <span :title="temp.strategy">{{ temp.strategy }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.sender')">
            <span :title="temp.sender">{{ temp.sender }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.senderIp')">
            {{ temp.senderIp }}
          </el-descriptions-item>

        </el-descriptions>
      </div>
      <grid-table
        ref="detailGrid"
        style="padding-top: 2px"
        :autoload="false"
        :height="320"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getMatchLogPage } from '@/api/behaviorAuditing/issue/dripLog'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'
import { getSeverityDict, getDictLabel } from '@/utils/dictionary'
import moment from 'moment'

export default {
  name: 'DripLogDetail',
  data() {
    return {
      colModel: [
        { prop: 'createTime', label: 'time', width: '150', sort: true },
        { prop: 'lossType', label: 'lossType', width: '100', formatter: this.lossTypeFormatter },
        { prop: 'filePath', label: 'sensitiveFile', width: '150' },
        { prop: 'content', label: 'sensitiveContent', width: '150', hidden: () => { return this.$store.getters.desensitizeContentAble } },
        { prop: 'ruleId', label: 'detectionRules', width: '100', formatter: (row) => { return row.ruleName } }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined
      },
      temp: {},
      detailDlgVisible: false,
      statusOptions: {
        0: this.$t('pages.startScan'),
        1: this.$t('pages.endScan')
      },
      opTypeOptions: {
        5: this.$t('pages.diskScan_Msg3'),
        3: this.$t('pages.diskScan_Msg4'),
        1: this.$t('pages.fullDecryption'),
        2: this.$t('pages.globalEncryption')
        /*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/
      },
      lossTypeOptions: []
    }
  },
  computed: {

  },
  created() {
    this.getSensitiveLossType()
  },
  activated() {

  },
  methods: {
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        this.lossTypeOptions = res.data.map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
      })
    },
    gridDetailTable() {
      return this.$refs['detailGrid']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getMatchLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
    },
    searchData(query) {
      this.query = query
      this.gridDetailTable().execRowDataApi(this.query)
    },
    show(data) {
      this.temp = data
      this.query.guid = data.guid
      this.query.isTimes = false
      this.query.createDate = moment(data.createTime).format('YYYY-MM-DD')
      this.query.searchReport = data.searchReport
      this.detailDlgVisible = true
      this.$nextTick(() => {
        this.gridDetailTable().execRowDataApi(this.query)
      })
    },
    closeDlg() {
      this.gridDetailTable().rowData.splice(0)
    },
    getLossTypeLabel(value) {
      // return getDictLabel(getLossTypeDict(), value)
      return getDictLabel(this.lossTypeOptions, value)
    },
    getSeverityLabel(value) {
      return getDictLabel(getSeverityDict(), value)
    },
    opTypeFormatter(row, data) {
      row.opType = row.opType + ''
      return this.opTypeOptions[data]
    },
    statusFormatter: function(row, data) {
      return this.statusOptions[data]
    },
    lossTypeFormatter: function(row, data) {
      const label = this.getLossTypeLabel(data)
      return label == null ? '' : label
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-form-item__label{
    text-align: left;
  }
  >>>.el-form-item__content{
    word-break:keep-all;
    text-overflow:ellipsis;
    overflow:hidden;
    white-space: nowrap;
  }
</style>
