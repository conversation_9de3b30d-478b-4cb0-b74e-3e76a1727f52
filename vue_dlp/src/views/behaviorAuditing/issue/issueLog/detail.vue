<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textTitle"
      :visible.sync="dialogFormVisible"
      width="700px"
    >

      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="temp.terminalName"
              :search-id="temp.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="temp.userName"
              :search-id="temp.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.lossType')">
            {{ getLossTypeLabel(temp.lossType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.severity')">
            {{ severityOptions[temp.severity] }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.contentStg')">
            <span :title="temp.strategy">{{ temp.strategy }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.detectionRules')">
            <span :title="temp.checkRule">{{ temp.checkRule }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.sender')">
            <span :title="temp.sender">{{ temp.sender }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.senderIp')">
            {{ temp.senderIp }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <Form ref="dataForm" :model="temp" label-position="right" label-width="100px" style="padding-top: 2px">
        <el-card :body-style="{'max-height': '280px', 'overflow-y': 'auto'}" class="box-card">
          <div slot="header" class="clearfix" style="padding-left: 0px;">
            <span>{{ $t('pages.violationDetails') }}</span>
          </div>
          <el-tag>{{ temp.occurTime }}</el-tag>
          <el-tag>{{ temp.terminalName }}</el-tag>
          <i18n path="pages.issueLog_Msg">
            <el-tag slot="lossType">{{ getLossTypeLabel(temp.lossType) }}</el-tag>
          </i18n>
          <span v-if="temp.fileName" style="line-height: 30px;">
            <i18n path="pages.issueLog_Msg1">
              <el-tag slot="fileName" :title="temp.filePath">
                <a
                  v-if="temp.fileGuid"
                  v-permission="'219'"
                  type="text"
                  @click="handleDownload(temp)"
                >
                  {{ temp.filePath }}
                </a>
                <span v-else>{{ temp.filePath }}</span>
              </el-tag>
            </i18n>
          </span>
          <span v-if="!temp.fileName" style="line-height: 30px;">
            {{ $t('pages.issueLog_Msg2') }}
          </span>
          <span v-if="!$store.getters.desensitizeContentAble" style="line-height: 30px;">
            {{ $t('pages.issueLog_Msg4') }}
          </span>
          <div v-if="!$store.getters.desensitizeContentAble" class="contentContainer" v-html="temp.content"></div>
        </el-card>
      </Form>
    </el-dialog>
    <audit-file-downloader v-show="false" ref="auditFileDownloader" v-permission="'219'" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
  </div>
</template>
<script>

import { formatFileSize } from '@/utils';
import { getDictLabel } from '@/utils/dictionary';
import { getAllSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog';
import AuditFileDownloader from '@/components/AuditFileDownloader/index.vue';

export default {
  name: 'IssueLogDetail',
  components: { AuditFileDownloader },
  data() {
    return {
      textTitle: this.$t('pages.eventDetails'),
      dialogFormVisible: false,
      selection: [],
      defaultTempTask: {
        backType: 14,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      severityOptions: {
        1: this.$t('pages.severityOptions1'),
        2: this.$t('pages.severityOptions2'),
        3: this.$t('pages.severityOptions3'),
        4: this.$t('pages.severityOptions4')
      },
      temp: {},
      allLossTypeOptions: [],
      defaultTemp: { // 表单字段
        id: undefined,
        // deviceType: undefined,
        lossType: undefined,
        severity: undefined,
        violator: '',
        content: '',
        occurTime: '',
        keywords: []
      }
    }
  },
  computed: {
  },
  created() {
    this.getAllSensitiveLossType()
  },
  methods: {
    formatFileSize,
    show(data) {
      this.dialogFormVisible = true
      this.rowDetail = data
      this.handleView(data)
    },
    getLossTypeLabel(value) {
      const realValue = this.setLossTypeOptionKey(value)
      return getDictLabel(this.allLossTypeOptions, realValue)
    },
    setLossTypeOptionKey: function(data) {
      if (data == 128) {
        return 21
      } else if (data == 129) {
        return 20
      } else {
        return data
      }
    },
    getAllSensitiveLossType() {
      getAllSensitiveLossType().then(res => {
        this.allLossTypeOptions = res.data.map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
      })
    },
    handleView(row) {
      this.resetTemp()
      this.temp = { ...this.temp, ...row } // copy obj
      this.temp.lossType = this.setLossTypeOptionKey(this.temp.lossType)
      let highlightType = 'pos'
      // 泄露方式为 访问网页内容 时，不通过关键字的位置高亮
      if (this.temp.lossType === 14) {
        this.temp.content = this.html2Escape(this.temp.content)
        highlightType = 'reg'
      }
      if (this.temp.rules) {
        try {
          let matchRules = JSON.parse(this.temp.rules)
          if (!Array.isArray(matchRules)) {
            matchRules = [matchRules]
          }
          const checkRules = []
          this.temp.keywords = []
          for (let i = 0; i < matchRules.length; i++) {
            const matchRule = matchRules[i]
            const { keyWords, name } = matchRule
            if (keyWords) {
              keyWords.forEach(item => {
                item.name = name
                this.temp.keywords.push(item)
              })
            }
            if (matchRule.ruletype == 224) {
              checkRules.push(this.$t('pages.effectiveContent_text12'))
            } else if (matchRule.ruletype == 225) {
              checkRules.push(this.$t('pages.effectivePasswordConfig'))
            } else if (matchRule.name) {
              checkRules.push(matchRule.name)
            }
          }
          this.temp.checkRule = checkRules.toString()
        } catch (e) {
          console.log(e)
        }
      }
      this.temp.content = this.highlightText(this.temp.content, this.temp.keywords, highlightType)
      // 换行符替换为<br>标签
      this.temp.content = this.temp.content.replace(/\r\n|\n/g, '<br>')
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    highlightText(text, keywords, type) {
      let newText = text
      if (type === 'pos') {
        // 通过返回关键字的位置信息高亮关键字
        const keywordMap = {}
        const posArr = []
        keywords.forEach(info => {
          const pos = info.pos
          pos.forEach(p => {
            // 字符长度，字符位置
            const { charLen, charPos } = p
            const keyword = `<b>${text.substr(charPos, charLen)}</b>`
            keywordMap[charPos] = { keyword, charPos, charLen }
            posArr.push(charPos)
          })
        })
        posArr.sort((a, b) => a - b)
        const newTextArr = [newText]
        while (posArr.length > 0) {
          const text = newTextArr.shift()
          const map = keywordMap[posArr.pop()]
          const { keyword, charPos, charLen } = map
          const strStart = text.substring(0, charPos)
          const strEnd = text.substring(charPos + charLen)
          newTextArr.unshift(strStart, keyword, this.html2Escape(strEnd))
        }
        newTextArr[0] = this.html2Escape(newTextArr[0])
        newText = newTextArr.join('')
      } else {
        keywords.forEach(keyword => {
          const keyWord = keyword.keyWord
          // 这边按照不区分大小写查找
          const reg = new RegExp(keyWord, 'i')
          const arr = reg.exec(newText)
          if (arr) {
            const newKeyword = `<b>${arr[0]}</b>`
            newText = this.replaceStrByPos(newText, newKeyword, arr.index, keyWord.length)
          }
        })
      }
      return newText
    },
    replaceStrByPos(str, newChar, charPos, charLen) {
      const strStart = str.substring(0, charPos)
      const strEnd = str.substring(charPos + charLen)
      return strStart + newChar + strEnd
    },
    handleDownload: function(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    beforeDownload(row) {
      const tempTask = Object.assign({}, this.defaultTempTask)
      tempTask.devId = row.devId
      tempTask.fileGuid = row.fileGuid
      tempTask.fileName = row.fileName
      return tempTask
    }
  }
}
</script>
<style lang="scss" scoped>
.el-tag{
  height: 28px;
  line-height: 28px;
  max-width: 500px;
  word-break: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
  margin: 0 5px 3px;
}
.contentContainer{
  padding: 10px;
  border: 1px solid #ccc;
  line-height: 20px;
  >>>b{
    color: red;
  }
}
</style>
