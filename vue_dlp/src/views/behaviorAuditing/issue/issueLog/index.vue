<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.lossType" :value="query.lossType">
          <span>{{ $t('pages.lossType') }}：</span>
          <el-select v-model="query.lossType" clearable is-filter :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(item, index) in lossTypeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.severity" :value="query.severity">
          <span>{{ $t('pages.severity') }}：</span>
          <el-select v-model="query.severity" clearable :placeholder="$t('pages.all')" style="width: 150px;">
            <el-option v-for="(value, key) in severityOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </SearchItem>
        <SearchItem model-key="query.strategyName" :value="query.strategyName">
          <span>{{ $t('pages.violationStrategy') }}：</span>
          <el-input v-model="query.strategyName" v-trim clearable style="width: 150px;"></el-input>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="'233'" :request="exportFunc"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" v-permission="'469'" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
        <audit-file-downloader
          v-if="!$store.getters.desensitizeContentAble"
          ref="auditFileDownloader"
          slot="append"
          v-permission="'219'"
          :name="$t('table.sensitiveFile')"
          topic="SensitiveContent"
          :button="$t('pages.downloadFile')"
          :selection="selection"
          :before-download="beforeDownload"
        />
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :multi-select="true"
        :selectable="selectable"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        :autoload="autoload"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textTitle"
      :visible.sync="dialogFormVisible"
      width="700px"
      @dragDialog="handleDrag"
    >

      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="temp.terminalName"
              :search-id="temp.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="temp.userName"
              :search-id="temp.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.lossType')">
            {{ getLossTypeLabel(temp.lossType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.severity')">
            {{ severityOptions[temp.severity] }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.contentStg')">
            <span :title="temp.strategy">{{ temp.strategy }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.detectionRules')">
            <span :title="temp.checkRule">{{ temp.checkRule }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.sender')">
            <span :title="temp.sender">{{ temp.sender }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.senderIp')">
            {{ temp.senderIp }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.receiver1')">
            {{ temp.receiver }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <Form ref="dataForm" :model="temp" label-position="right" label-width="100px" style="padding-top: 2px">
        <el-card :body-style="{'max-height': '280px', 'overflow-y': 'auto'}" class="box-card">
          <div slot="header" class="clearfix" style="padding-left: 0px;">
            <span>{{ $t('pages.violationDetails') }}</span>
          </div>
          <el-tag>{{ temp.occurTime }}</el-tag>
          <el-tag>{{ temp.terminalName }}</el-tag>
          <i18n path="pages.issueLog_Msg">
            <el-tag slot="lossType">{{ getLossTypeLabel(temp.lossType) }}</el-tag>
          </i18n>
          <span v-if="temp.fileName" style="line-height: 30px;">
            <i18n path="pages.issueLog_Msg1">
              <el-tag slot="fileName" :title="temp.filePath">
                <a
                  v-if="temp.fileGuid"
                  v-permission="'219'"
                  type="text"
                  @click="handleLoadDown(temp)"
                >
                  {{ temp.filePath }}
                </a>
                <span v-else>{{ temp.filePath }}</span>
              </el-tag>
            </i18n>
          </span>
          <span v-if="!temp.fileName" style="line-height: 30px;">
            {{ $t('pages.issueLog_Msg2') }}
          </span>
          <span v-if="!$store.getters.desensitizeContentAble" style="line-height: 30px;">
            {{ $t('pages.issueLog_Msg4') }}
          </span>
          <div v-if="!$store.getters.desensitizeContentAble" class="contentContainer" v-html="temp.content"></div>
        </el-card>
      </Form>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>
  </div>
</template>

<script>
import { getLogPage, deleteLog, exportExcel } from '@/api/behaviorAuditing/issue/issueLog'
import { getDictLabel } from '@/utils/dictionary'
import { getSensitiveLossType, getAllSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'
import { logSourceFormatter } from '@/utils/formatter'
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'IssueLog',
  mixins: [auditLogRouterMixin],
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'createTime', label: 'operateTime', width: '150', sort: 'custom', formatter: row => logSourceFormatter(row, row.occurTime) },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        // { label: '事件主题', width: '200', formatter: this.subjectFormatter },
        // { prop: 'deviceType', label: '事件来源', width: '150', formatter: this.deviceTypeFormatter },
        { prop: 'lossType', label: 'lossType', width: '100', formatter: this.lossTypeFormatter },
        { prop: 'severity', label: 'severity', width: '80', formatter: this.severityFormatter },
        { prop: 'filePath', label: 'sensitiveFile', width: '150' },
        // { prop: 'content', label: '敏感内容', width: '150' },
        { prop: 'strategyId', label: 'contentStg', width: '100', formatter: (row) => { return row.strategy } },
        { prop: 'senderIp', label: 'sender1', width: '150', formatter: this.senderFormatter },
        { prop: 'receiver', label: 'receiver1', width: '150', formatter: this.receiverFormatter },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right', hidden: !this.hasPermission('218,219'),
          buttons: [
            { label: 'detail', click: this.handleView, isShow: () => this.hasPermission('218') },
            { label: 'download', isShow: () => this.hasPermission('219') && !this.$store.getters.desensitizeContentAble, disabledFormatter: this.downloadFormatter, click: this.handleLoadDown }
          ]
        }
      ],
      severityOptions: {
        1: this.$t('pages.severityOptions1'),
        2: this.$t('pages.severityOptions2'),
        3: this.$t('pages.severityOptions3'),
        4: this.$t('pages.severityOptions4')
      },
      deviceTypeOptions: {
        0: this.$t('pages.all'),
        1: this.$t('pages.terminal'),
        2: this.$t('pages.deviceTypeOptions1'),
        3: this.$t('pages.deviceTypeOptions2')
      },
      // lossTypeOptions: getLossTypeDict(),
      lossTypeOptions: [],
      allLossTypeOptions: [],
      chatToolOptions: [],
      lossTypeValue: undefined,
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        // deviceType: undefined,
        lossType: undefined,
        severity: undefined,
        strategyName: undefined,
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        // deviceType: undefined,
        lossType: undefined,
        severity: undefined,
        violator: '',
        content: '',
        occurTime: '',
        keywords: []
      },
      textTitle: this.$t('pages.eventDetails'),
      dialogFormVisible: false,
      showTree: true,
      selection: [],
      tempTask: {},
      defaultTempTask: {
        backType: 14,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      queryVideoMethod: undefined
    }
  },
  computed: {
    chatTypeMap() {
      const map = {}
      this.chatToolOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    this.getSensitiveLossType()
    this.getAllSensitiveLossType()
    addViewVideoBtn(this, () => !this.$store.getters.desensitizeContentAble)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        this.lossTypeOptions = res.data.map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
      })
    },
    getAllSensitiveLossType() {
      getAllSensitiveLossType().then(res => {
        this.allLossTypeOptions = res.data.map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
      })
    },
    chatTypeFormatter: function(row) {
      return this.chatTypeMap[row.chatType]
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    handleView(row) {
      this.resetTemp()
      this.temp = { ...this.temp, ...row } // copy obj
      this.temp.lossType = this.setLossTypeOptionKey(this.temp.lossType)
      let highlightType = 'pos'
      // 泄露方式为 访问网页内容 时，不通过关键字的位置高亮
      if (this.temp.lossType === 14) {
        this.temp.content = this.html2Escape(this.temp.content)
        highlightType = 'reg'
      }
      if (this.temp.rules) {
        try {
          let matchRules = JSON.parse(this.temp.rules)
          if (!Array.isArray(matchRules)) {
            matchRules = [matchRules]
          }
          const checkRules = []
          this.temp.keywords = []
          for (let i = 0; i < matchRules.length; i++) {
            const matchRule = matchRules[i]
            const { keyWords, name } = matchRule
            if (keyWords) {
              keyWords.forEach(item => {
                item.name = name
                this.temp.keywords.push(item)
              })
            }
            if (matchRule.ruletype == 224) {
              checkRules.push(this.$t('pages.effectiveContent_text12'))
            } else if (matchRule.ruletype == 225) {
              checkRules.push(this.$t('pages.effectivePasswordConfig'))
            } else if (matchRule.name) {
              checkRules.push(matchRule.name)
            }
          }
          this.temp.checkRule = checkRules.toString()
        } catch (e) {
          console.log(e)
        }
      }
      this.temp.content = this.highlightText(this.temp.content, this.temp.keywords, highlightType)
      // 换行符替换为<br>标签
      this.temp.content = this.temp.content.replace(/\r\n|\n/g, '<br>')
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    highlightText(text, keywords, type) {
      let newText = text
      if (type === 'pos') {
        // 通过返回关键字的位置信息高亮关键字
        const keywordMap = {}
        const posArr = []
        keywords.forEach(info => {
          const pos = info.pos
          pos.forEach(p => {
            // 字符长度，字符位置
            const { charLen, charPos } = p
            const keyword = `<b>${text.substr(charPos, charLen)}</b>`
            keywordMap[charPos] = { keyword, charPos, charLen }
            posArr.push(charPos)
          })
        })
        posArr.sort((a, b) => a - b)
        const newTextArr = [newText]
        while (posArr.length > 0) {
          const text = newTextArr.shift()
          const map = keywordMap[posArr.pop()]
          const { keyword, charPos, charLen } = map
          const strStart = text.substring(0, charPos)
          const strEnd = text.substring(charPos + charLen)
          newTextArr.unshift(strStart, keyword, this.html2Escape(strEnd))
        }
        newTextArr[0] = this.html2Escape(newTextArr[0])
        newText = newTextArr.join('')
      } else {
        keywords.forEach(keyword => {
          const keyWord = keyword.keyWord
          // 这边按照不区分大小写查找
          const reg = new RegExp(keyWord, 'i')
          const arr = reg.exec(newText)
          if (arr) {
            const newKeyword = `<b>${arr[0]}</b>`
            newText = this.replaceStrByPos(newText, newKeyword, arr.index, keyWord.length)
          }
        })
      }
      return newText
    },
    replaceStrByPos(str, newChar, charPos, charLen) {
      const strStart = str.substring(0, charPos)
      const strEnd = str.substring(charPos + charLen)
      return strStart + newChar + strEnd
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    // subjectFormatter: function(row, data) {
    //   let result = this.$t('pages.contentStg')
    //   result += row.strategy ? ` (<b style="color: #fff;"> ${row.strategy} </b>)` : ''
    //   return this.$t('pages.issueLog_Msg3', { 0: `<b style="color: #fff;"> ${row.terminalName} </b>(${row.senderIp})`, 1: result })
    // },
    severityFormatter: function(row, data) {
      return this.severityOptions[data]
    },
    // deviceTypeFormatter: function(row, data) {
    //   return this.deviceTypeOptions[data]
    // },
    lossTypeFormatter: function(row, data) {
      return this.getLossTypeLabel(data)
    },
    senderFormatter: function(row, data) {
      let result = ''
      if (row.sender) result += row.sender
      if (row.senderIp) {
        result += result.length > 0 ? `(${row.senderIp})` : row.senderIp
      }
      return result
    },
    receiverFormatter: function(row, data) {
      let result = ''
      if (row.receiver) result += row.receiver
      if (row.receiverIp) {
        result += result.length > 0 ? `(${row.receiverIp})` : row.receiverIp
      }
      return result
    },
    selectable(row, index) {
      return !!(row.fileGuid && row.devId) || (this.$store.getters.auditingDeleteAble && this.hasPermission('469'))
    },
    downloadFormatter(data, btn) {
      return !data.fileGuid
    },
    getLossTypeLabel(value) {
      const realValue = this.setLossTypeOptionKey(value)
      return getDictLabel(this.allLossTypeOptions, realValue)
    },
    setLossTypeOptionKey: function(data) {
      if (data == 128) {
        return 21
      } else if (data == 129) {
        return 20
      } else {
        return data
      }
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    exportFunc(exportType) {
      return exportExcel({ exportType, ...this.query })
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '218', this.query.searchReport, undefined, 'occurTime')
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-form-item__label{
    text-align: left;
    text-indent: 21px;
  }
  >>>.el-form-item__content{
    word-break:keep-all;
    text-overflow:ellipsis;
    overflow:hidden;
    white-space: nowrap;
  }
  .el-tag{
    height: 28px;
    line-height: 28px;
    max-width: 500px;
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: middle;
    margin: 0 5px 3px;
  }
  .contentContainer{
    padding: 10px;
    border: 1px solid #ccc;
    line-height: 20px;
    >>>b{
      color: red;
    }
  }
</style>
