<template>
  <div class="app-container">
    <div class="tree-container">
      <strategy-target-tree ref="strategyTargetTree" @data-change="strategyTargetNodeChange" />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <TimeQuery ref="timeQuery" @getTimeParams="getTimeParams"/>
        <span>
          {{ $t('table.keyword') }}：
          <el-input v-model="query.keyword" v-trim class="input-with-select" clearable style="width: 300px;" >
            <el-select slot="append" v-model="query.logic" placeholder="请选择" style="width: 70px;">
              <el-option label="且" value="and"></el-option>
              <el-option label="或" value="or"></el-option>
            </el-select>
          </el-input>
          <el-tooltip class="item" placement="bottom">
            <div slot="content">关键字含空格时，将拆分为多个关键字，并使用下拉框选择的"且","或"逻辑关系进行查询</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </span>
        场景：
        <el-select key="sceneSelect" v-model="query.sceneName" clearable filterable is-filter :placeholder="$t('text.select')" style="width: 180px;">
          <el-option v-for="(value, key) in sceneOptions" :key="key" :label="value" :value="key"></el-option>
        </el-select>
        <el-popover
          v-if="query.sceneName"
          placement="bottom"
          title=""
          width="500"
          trigger="click"
        >
          <div v-html="sceneDescription"></div>
          <el-button slot="reference" type="text" class="search-icon-left" icon="el-icon-info" circle style="padding: 0 5px;" @click="getSceneDescription"></el-button>
        </el-popover>
        <el-button
          ref="searchButton"
          :type="isSearching ? 'danger' : 'primary'"
          :icon="isSearching ? 'el-icon-close' : 'el-icon-search'"
          size="mini"
          :class="{'searching-button': isSearching}"
          @click="handleSearchButtonClick"
        >
          {{ isSearching ? $t('table.cease') : $t('table.search') }}
        </el-button>
        <audit-log-exporter batch button="导出搜索结果" :name="() => getFileName(realQuery, sceneOptions[realQuery.sceneName])" :disabled="exportDisabled" :request="handleExportSearchResult"/>
        <audit-log-exporter batch button="导出全部审计日志" :name="() => getFileName(query, '审计日志')" :request="handleExportAllAuditLog"/>
        <el-tooltip class="item" placement="bottom">
          <div slot="content">导出全部审计日志时关键字和场景不作为导出条件</div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <div v-if="countList.length > 0 && rate < 100" class="search-progress-container">
        <div class="progress-header">
          <div class="progress-title">
            <svg-icon icon-class="loading" class="progress-icon spin" />
            <span>搜索进度</span>
          </div>
          <div class="progress-percentage">{{ rate }}%</div>
        </div>
        <el-progress
          :percentage="rate"
          :stroke-width="8"
          :show-text="false"
          class="custom-progress"
        ></el-progress>
      </div>
      <div v-if="countList.length > 0" class="scene-type-container">
        <div class="record-type-header">
          <div class="record-type-title">
            审计类型
            <!-- 审计类型提示 - 简单文字版本 -->
            <span v-if="shouldShowAuditTypeTip" class="audit-type-tip-text">
              可以点击下方的审计类型查看对应的审计日志
            </span>
          </div>
          <div class="toggle-control">
            <span class="toggle-label">显示空记录</span>
            <el-switch v-model="showZeroRecords" size="small"></el-switch>
          </div>
        </div>

        <div class="tag-wrapper">
          <el-tag
            v-for="tag in filteredTags"
            :key="tag.name"
            :class="['record-tag', {
              'is-active': activeTag === tag.name,
              'completed': tag.status === 1,
              'incomplete': tag.status === 0,
              'disabled-tag': tag.status === 2
            }]"
            @click="tag.status !== 2 && queryData(tag.name)"
          >
            {{ tag.desc }}
            <template v-if="tag.status === 0">
              <svg-icon icon-class="loading" class="tag-loading-icon spin" />
            </template>
            <template v-else-if="tag.status === 2">
            </template>
            <span v-else class="count-badge">{{ tag.count }}</span>
          </el-tag>
        </div>
      </div>

      <grid-table
        ref="logList"
        row-key="id"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :autoload="false"
        :custom-col="true"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <!-- 一些公共组件 -->

    <!-- 审计日志详情组件 start -->
    <usb-file-log-detail ref="usbFileLogDetail" />
    <bt-file-log-detail ref="btFileLogDetail" />
    <mtp-file-log-detail ref="mtpFileLogDetail" />
    <browser-upload-log-detail ref="browserUploadLogDetail" />
    <remote-desktop-log-detail ref="remoteDesktopLogDetail" />
    <ftp-file-log-detail ref="ftpFileLogDetail" />
    <net-file-share-record-detail ref="netFileShareRecordDetail" />
    <local-file-share-record-detail ref="localFileShareRecordDetail" />
    <email-log-detail ref="emailLogDetail" />
    <file-outgoing-log ref="fileOutgoingLogDetail" />
    <file-backup-log ref="fileBackupLogDetail" />
    <print-log ref="printLogDetail" />
    <adb-log ref="adbLogDetail" />
    <file-op-log ref="fileOpLogDetail" />
    <browser-download-log ref="browserDownloadLogDetail" />
    <chat-tool-download-log ref="chatToolDownloadLogDetail" />
    <net-disk-log ref="netDiskLogDetail" />
    <http-white-list-dec-log ref="httpWhiteListDecLogDetail" />
    <smart-backup-log ref="smartBackupLogDetail" />
    <smart-backup-restore-log ref="smartBackupRestoreLogDetail" />
    <issue-log ref="issueLogDetail" />
    <sensitive-op-log ref="sensitiveOpLogDetail" />
    <!-- 审计日志详情组件 end  -->
  </div>
</template>

<script>
import {
  getDetail,
  getPage,
  getParams,
  getSceneDescription,
  sceneOptions
} from '@/api/behaviorAuditing/auditLogUnifiedQuery/auditLogUnifiedQuery2'
import { getToken } from '@/utils/auth';
import store from '@/store'

import UsbFileLogDetail from '@/views/behaviorAuditing/terminal/usbFile/detail'
import BtFileLogDetail from '@/views/behaviorAuditing/terminal/btFileLog/detail.vue';
import MtpFileLogDetail from '@/views/behaviorAuditing/terminal/mtpLog/detail.vue';
import BrowserUploadLogDetail from '@/views/behaviorAuditing/network/browserUpload/detail.vue';
import RemoteDesktopLogDetail from '@/views/behaviorAuditing/network/remoteDesktopLog/detail.vue';
import FtpFileLogDetail from '@/views/behaviorAuditing/network/ftp/detail.vue';
import NetFileShareRecordDetail from '@/views/behaviorAuditing/network/netFileShareLog/detail.vue';
import EmailLogDetail from '@/views/behaviorAuditing/terminal/email/detail.vue';
import LocalFileShareRecordDetail from '@/views/behaviorAuditing/network/localFileShareLog/detail.vue';
import FileOutgoingLog from '@/views/behaviorAuditing/encryption/fileOutgoingLog/detail.vue';
import FileBackupLog from '@/views/behaviorAuditing/encryption/fileBackupLog/detail.vue';
import PrintLog from '@/views/behaviorAuditing/terminal/printer/detail.vue';
import AdbLog from '@/views/behaviorAuditing/terminal/adbLog/detail.vue';
import FileOpLog from '@/views/behaviorAuditing/terminal/file/detail.vue';
import BrowserDownloadLog from '@/views/behaviorAuditing/network/browserDownload/detail.vue';
import ChatToolDownloadLog from '@/views/behaviorAuditing/network/chatToolDownloadLog/detail.vue';
import NetDiskLog from '@/views/behaviorAuditing/network/netDiskLog/detail.vue';
import HttpWhiteListDecLog from '@/views/behaviorAuditing/encryption/httpWhiteListDecLog/detail.vue';
import SmartBackupLog from '@/views/behaviorAuditing/encryption/smartBackupLog/detail.vue';
import SmartBackupRestoreLog from '@/views/behaviorAuditing/encryption/smartBackupRestoreLog/detail.vue';
import IssueLog from '@/views/behaviorAuditing/issue/issueLog/detail.vue';
import SensitiveOpLog from '@/views/behaviorManage/hardware/sensitiveOpLog/detail.vue';
import i18n from '@/lang';
import moment from 'moment';
import { batchExportAll, exportFileSearchLogs } from '@/api/behaviorAuditing/batchExport';
import { asyncGetLogVideoInfo } from '@/utils/logVideo';

export default {
  name: 'LogQuery2',
  components: {
    SensitiveOpLog,
    IssueLog,
    SmartBackupRestoreLog,
    SmartBackupLog,
    HttpWhiteListDecLog,
    NetDiskLog,
    ChatToolDownloadLog,
    BrowserDownloadLog,
    FileOpLog,
    AdbLog,
    PrintLog,
    FileBackupLog,
    FileOutgoingLog,
    LocalFileShareRecordDetail,
    EmailLogDetail,
    NetFileShareRecordDetail,
    FtpFileLogDetail,
    RemoteDesktopLogDetail,
    BrowserUploadLogDetail,
    MtpFileLogDetail,
    BtFileLogDetail,
    UsbFileLogDetail
  },
  data() {
    return {
      colModel: [
        { prop: 'content', label: '匹配结果', width: '500', formatter: this.logSourceFormatter },
        { prop: 'createDate', label: 'time', width: '150', sort: 'custom' },
        { prop: 'auditTypeName', label: '审计类型', width: '150' },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'termId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { label: 'operate', type: 'button', fixedWidth: '240', fixed: 'right',
          buttons: [
            { label: 'detail', click: this.handleView, isShow: this.isShowDetail },
            { label: '相关搜索', click: this.handleNavigateTo }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        keyword: '',
        logic: 'and',
        searchReport: 1,
        sceneName: '',
        type: '',
        currentQueryVersion: null
      },
      realQuery: {},
      countList: [],
      rate: 0,
      sceneOptions: {},
      showTree: true,
      tempTask: {},
      defaultTempTask: {
        backType: 7,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      dialogFormVisible: false,
      selection: [],
      sortable: true,
      sceneDescription: undefined,
      selectedTagName: undefined,
      termsInfo: [],
      activeTag: null,
      showZeroRecords: true,
      isSearching: false,
      isProcessingClick: false, // 防止快速点击的标志
      currentQueryVersion: null, // 当前查询版本号，用于前端校验
      // 审计类型提示相关
      auditTypeTipStorageKey: 'audit-type-tip-dont-show'
    }
  },
  computed: {
    exportList() {
      return !this.countList ? [] : this.countList.filter(item => item.count > 0)
    },
    exportDisabled() {
      return !this.exportList.length || this.rate < 100
    },
    filteredTags() {
      if (this.showZeroRecords) {
        return this.countList;
      } else {
        return this.countList.filter(tag => tag.count > 0);
      }
    },
    // 是否应该显示审计类型提示
    shouldShowAuditTypeTip() {
      // 检查localStorage中是否设置了不再提示
      const dontShow = localStorage.getItem(this.auditTypeTipStorageKey) === 'true'
      if (dontShow) {
        return false
      }

      // 有审计类型数据且没有选中任何类型且搜索完成时显示提示
      return this.countList.length > 0 && !this.activeTag && this.rate >= 100
    }
  },
  watch: {
    // 监听reportModule的变化，防止当reportModule值更新之前就赋值给了this.query.searchReport
    '$store.getters.reportModule': {
      handler(newVal) {
        // 3:DLP + 报表   1:DLP
        this.query.searchReport = newVal ? 3 : 1;
      },
      immediate: true
    },
    // 监听搜索状态变化，确保UI更新
    isSearching(newVal) {
      console.log('isSearching 状态变为:', newVal);
    },
    // 监听进度，确保在适当时候更新搜索状态
    rate(newVal) {
      if (newVal >= 100 && this.isSearching) {
        this.isSearching = false;
      }
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    this.getSceneOptions()
  },
  methods: {
    getFileName(query, prefix) {
      let name = prefix + '(';
      if (query.isTimes) {
        name += query.startDate + '~' + query.endDate
      } else {
        name += query.createDate
      }
      return name + ')_' + moment().format('YYYY-MM-DD HH-mm-ss')
    },
    handleExportSearchResult(exportType, fileName) {
      const data = { ...this.realQuery, exportType, fileName }
      data.sceneScopes = this.exportList.map(item => item.name)
      return exportFileSearchLogs(data)
    },
    handleExportAllAuditLog(exportType, fileName) {
      return batchExportAll({ exportType, fileName, ...this.query })
    },
    // 统一的按钮点击处理方法
    handleSearchButtonClick() {
      // 防止快速点击导致的状态不一致
      if (this.isProcessingClick) {
        return;
      }

      this.isProcessingClick = true;

      // 使用当前的isSearching状态来决定执行哪个操作
      const currentSearchingState = this.isSearching;

      this.$nextTick(() => {
        if (currentSearchingState) {
          this.ceaseCount();
        } else {
          this.queryCount();
        }

        // 重置点击处理标志
        setTimeout(() => {
          this.isProcessingClick = false;
        }, 100);
      });
    },

    async queryCount() {
      if (!this.query.keyword) {
        // 失败, 显示报错信息
        this.$message({
          type: 'error',
          message: '请输入关键字',
          duration: 5000
        })
        return;
      }

      if (!this.query.sceneName) {
        // 失败, 显示报错信息
        this.$message({
          type: 'error',
          message: '请选择场景',
          duration: 5000
        })
        return;
      }

      if (!this.query.createDate && !this.query.startDate) {
        // 失败, 显示报错信息
        this.$message({
          type: 'error',
          message: '请选择日期',
          duration: 5000
        })
        return;
      }

      try {
        // 生成新的查询版本号
        this.currentQueryVersion = Date.now();

        // 设置搜索状态为正在搜索
        this.isSearching = true;

        // 强制DOM更新，确保按钮显示为红色终止按钮
        this.$nextTick(() => {
          // 强制更新按钮样式
          if (this.$refs.searchButton) {
            const buttonEl = this.$refs.searchButton.$el;
            buttonEl.classList.add('searching-button');
            buttonEl.style.backgroundColor = '#f56c6c';
            buttonEl.style.borderColor = '#f56c6c';
          }
        });

        // 重置分类选中状态
        this.activeTag = null

        this.selectedTagName = undefined
        // 这里先清空列表数据, 重置分页数据
        this.resetGridTable()
        // 这里没有办法放在返回的时候再确认查询，因为后端属于多次返回的，会发生在查询过程中，用户去更改了查询条件，导致查询条件错误。
        this.realQuery = Object.assign({}, this.query)

        // 将查询版本号添加到请求参数中
        const queryWithVersion = Object.assign({}, this.query, { currentQueryVersion: this.currentQueryVersion })

        // 使用可靠的WebSocket发送机制
        await this.sendWebSocketMessageReliably(queryWithVersion)
      } catch (error) {
        console.error('查询失败:', error);
        // 错误已在相应的处理方法中处理
      }
    },
    resetGridTable() {
      this.gridTable().clearRowData()
      this.gridTable().clearPageData()
    },
    logSourceFormatter(row, data) {
      const name = row.id.startsWith('rpt_') ? 'report' : 'database'
      const icon = require(`@/assets/${name}.png`)
      const tips = row.id.startsWith('rpt_') ? i18n.t('route.reportServer') : i18n.t('route.DBServer')
      const img = `<img src="${icon}" style="margin-right: 5px; border-radius: 3px; vertical-align: text-bottom;" alt="${name}" title="${tips}">`
      if (data && data.startsWith(img)) {
        return data
      }
      return img + data
    },
    getSceneDescription() {
      if (!this.query.sceneName) {
        return
      }
      getSceneDescription(this.query.sceneName).then(response => {
        this.sceneDescription = response.data
      })
    },
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    objectTree: function() {
      return this.$refs['objectTree']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.realQuery, option)
      this.changeSortable()
      return getPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      // if (checkedNode) {
      //   if (this.query.objectType &&
      //     this.query.objectId &&
      //     this.query.objectId === checkedNode.dataId &&
      //     this.query.objectType === checkedNode.type) {
      //     // 说明当前节点已经选中了，再次点击则取消选择
      //     this.query.objectType = undefined
      //     this.query.objectId = undefined
      //   } else {
      //     this.query.objectType = checkedNode.type
      //     this.query.objectId = checkedNode.dataId
      //   }
      // } else {
      //   this.query.objectType = undefined
      //   this.query.objectId = undefined
      // }
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.queryCount()
    },
    handleFilter() {
      this.query.page = 1
      this.realQuery.page = 1
      this.gridTable().execRowDataApi(this.realQuery)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.devId
      this.tempTask.fileGuid = row.fileGuid
      this.tempTask.fileName = row.fileName
      return this.tempTask
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleView(row) {
      getDetail(row.id, row.dictName).then(response => {
        const component = response.data.component;
        this.$refs[component].show(response.data.data)
      })
    },
    handleNavigateTo(row) {
      getParams(row, this.realQuery.sceneName, row.dictName).then(response => {
        const path = response.data.viewPath
        const searchQuery = response.data.params;

        this.$router.push({
          path: path,
          query: searchQuery
        })
      })
    },
    getSceneOptions() {
      sceneOptions().then(response => {
        // 方式一， this.sceneOptions[item.value] = item.label 本身没有问题，但VUE2 无法检测到这种变化，所以需要使用this.$forceUpdate() 强制更新
        /*
        response.data.forEach(item => {
          this.sceneOptions[item.value] = item.label
        })
        this.$forceUpdate()
         */
        // 方式二， 通过this.$set 方法确保响应式更新
        response.data.forEach(item => {
          this.$set(this.sceneOptions, item.value, item.label);
        })

        // 如果this.sceneOptions 只有一个场景，则默认选择。
        if (Object.keys(this.sceneOptions).length === 1) {
          this.query.sceneName = Object.keys(this.sceneOptions)[0];
        }
      })
    },
    queryData(tagName) {
      this.activeTag = tagName;
      this.realQuery.type = tagName;
      this.handleFilter();
      // 选中审计类型后，activeTag有值，提示会自动隐藏
    },
    setTagType(tag) {
      this.refreshTagEffect()
      if (tag.status === 0) {
        return 'info'
      }
      return 'warning'
    },
    refreshTagEffect() {
      const prefix = 'tag-'
      for (let i = 0; i < this.countList.length; i++) {
        const name = this.countList[i].name;
        if (!this.$refs[prefix + name]) {
          continue
        }
        const node = this.$refs[prefix + name][0]
        if (!node) {
          continue
        }
        if (this.selectedTagName && this.selectedTagName === name) {
          node.effect = 'dark'
        } else {
          node.effect = 'light'
        }
      }
    },
    isShowDetail(data, btn) {
      return data.showDetail
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, '300', this.query.searchReport)
    },
    // 终止当前搜索的方法
    ceaseCount() {
      console.log('执行终止搜索操作');

      // 这里将调用终止搜索的API（后端需要停止搜索任务）
      this.$socket.send('/app/auditLogCountCease', { 'Authorization': store.getters.token })

      // 立即终止：恢复搜索按钮状态并清空版本号
      this.isSearching = false;
      this.currentQueryVersion = null; // 清空版本号，忽略后续所有消息
      this.rate = 100; // 设置进度为100%，隐藏进度条

      // 更新countList中的数据，将未完成的项标记为终止状态
      if (this.countList && this.countList.length > 0) {
        this.countList.forEach(item => {
          // 如果status为0（未完成），表示该项被终止
          if (item.count === null || item.status === 0) {
            item.status = 2; // 使用状态2表示终止
          }
        });
      }
      // 重置按钮样式
      this.$nextTick(() => {
        this.resetButtonStyle();
      });

      this.$message({
        type: 'info',
        message: '搜索已终止',
        duration: 3000
      })
    },

    // 重置按钮样式
    resetButtonStyle() {
      if (this.$refs.searchButton) {
        const buttonEl = this.$refs.searchButton.$el;
        buttonEl.classList.remove('searching-button');
        buttonEl.style.backgroundColor = '';
        buttonEl.style.borderColor = '';
      }
    },

    // WebSocket可靠发送机制
    async sendWebSocketMessageReliably(queryWithVersion) {
      const maxRetries = 3;
      const connectionWaitTime = 8000; // 增加等待时间，给重连更多时间

      // 首先检查连接状态，如果未连接静默重连
      if (!this.isWebSocketConnected()) {
        console.log('检测到WebSocket未连接，开始静默重连...');
        this.triggerReconnection(true); // 传入true表示静默重连
      }

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`第${attempt}次尝试发送WebSocket消息`);

          // 1. 等待连接就绪
          await this.waitForConnection(connectionWaitTime, attempt === 1);

          // 2. 发送消息和订阅
          this.sendMessageAndSubscribe(queryWithVersion);

          console.log('WebSocket消息发送成功');
          return; // 成功则退出
        } catch (error) {
          console.error(`第${attempt}次发送失败:`, error.message);

          if (attempt === maxRetries) {
            // 最后一次重试失败，提示用户刷新页面
            this.handleConnectionFailure();
            return;
          }

          // 如果不是最后一次重试，触发重连（第2次开始显示提示）
          console.log(`第${attempt}次失败，触发重连...`);
          this.triggerReconnection(attempt === 1); // 第一次失败仍然静默

          // 只等待很短时间让重连开始
          await this.delay(500);
        }
      }
    },

    // 等待WebSocket连接就绪
    waitForConnection(timeout = 8000, isFirstAttempt = false) {
      return new Promise((resolve, reject) => {
        // 检查多种连接状态
        if (this.isWebSocketConnected()) {
          if (!isFirstAttempt) {
            console.log('WebSocket已连接，直接发送消息');
          }
          resolve();
          return;
        }

        if (!isFirstAttempt) {
          console.log('WebSocket未连接，等待连接就绪...');
        }
        let checkCount = 0;
        const maxChecks = timeout / 50; // 每50ms检查一次

        // 定期检查连接状态
        const checkInterval = setInterval(() => {
          checkCount++;

          if (this.isWebSocketConnected()) {
            clearTimeout(timeoutId);
            clearInterval(checkInterval);
            if (!isFirstAttempt) {
              console.log('WebSocket连接就绪');
            }
            resolve();
            return;
          }

          // 只有非首次尝试才输出等待日志
          if (!isFirstAttempt && checkCount % 40 === 0) {
            console.log(`等待WebSocket连接中... (${Math.floor(checkCount * 50 / 1000)}s)`);
          }

          // 如果等待时间过长，尝试再次触发重连（静默）
          if (checkCount === Math.floor(maxChecks / 2)) {
            if (!isFirstAttempt) {
              console.log('等待时间较长，尝试再次触发重连...');
            }
            this.triggerReconnection(true); // 静默重连
          }
        }, 50); // 每50ms检查一次，更频繁

        // 设置超时
        const timeoutId = setTimeout(() => {
          clearInterval(checkInterval);
          if (!isFirstAttempt) {
            console.error(`WebSocket连接超时 (${timeout}ms)`);
          }
          reject(new Error('等待WebSocket连接超时'));
        }, timeout);
      });
    },

    // 检查WebSocket是否真正连接
    isWebSocketConnected() {
      // 方法1：检查基本连接状态
      if (this.$socket && this.$socket.connected) {
        return true;
      }

      // 方法2：检查底层stomp连接状态
      if (this.$socket && this.$socket.stomp && this.$socket.stomp.connected) {
        return true;
      }

      // 方法3：尝试检查全局WebSocket实例（如果reconnect创建了新实例）
      if (window.Vue && window.Vue.prototype.$socket && window.Vue.prototype.$socket.connected) {
        console.log('检测到全局WebSocket实例已连接，更新本地引用');
        this.$socket = window.Vue.prototype.$socket;
        return true;
      }

      return false;
    },

    // 触发重连（利用现有的重连机制）
    triggerReconnection(silent = false) {
      if (!this.isWebSocketConnected()) {
        if (!silent) {
          console.log('触发WebSocket重连...');
        }
        // 由于reconnect()会创建新实例，我们需要重新初始化连接
        this.reinitializeWebSocket(silent);
      }
    },

    // 重新初始化WebSocket连接
    reinitializeWebSocket(silent = false) {
      try {
        if (!silent) {
          console.log('重新初始化WebSocket连接...');

          // 显示连接中的提示
          this.$message({
            type: 'info',
            message: '正在重新连接通信通道...',
            duration: 2000
          });
        }

        // 创建新的WebSocket实例
        const StompSocket = require('@/utils/websocket').default;
        const newSocket = new StompSocket(process.env.VUE_APP_BASE_API + '/websocketJS');
        newSocket.debugEnable(true);
        newSocket.setSubscribeTimeout(60000);

        // 设置连接成功回调
        newSocket.option.successCallback = () => {
          if (!silent) {
            console.log('WebSocket重连成功');

            // 显示连接成功提示
            this.$message({
              type: 'success',
              message: '通信通道连接成功',
              duration: 1500
            });
          }

          // 更新Vue原型上的socket实例
          this.$socket = newSocket;
          if (window.Vue && window.Vue.prototype) {
            window.Vue.prototype.$socket = newSocket;
          }
        };

        // 设置连接失败回调
        newSocket.option.errorCallback = (error) => {
          if (!silent) {
            console.error('WebSocket重连失败:', error);
          }
        };

        // 开始连接
        newSocket.initAndConn();
      } catch (error) {
        if (!silent) {
          console.error('重新初始化WebSocket失败:', error);
        }
        // 如果重新初始化失败，尝试使用原有的重连机制
        this.$socket.reconnect();
      }
    },

    // 发送消息和订阅
    sendMessageAndSubscribe(queryWithVersion) {
      if (!this.isWebSocketConnected()) {
        throw new Error('WebSocket未连接');
      }

      try {
        // 发送消息
        console.log('发送WebSocket消息到服务器...');
        this.$socket.send('/app/auditLogCount', { 'Authorization': store.getters.token }, queryWithVersion);

        // 订阅响应
        this.$socket.subscribeToUser(
          getToken(),
          '/topic/auditLogQueryResult',
          (response, handle) => {
            this.handleWebSocketResponse(response, handle);
          }
        );
      } catch (error) {
        console.error('发送WebSocket消息失败:', error);
        throw error;
      }
    },

    // 响应处理
    handleWebSocketResponse(response, handle) {
      try {
        // 获取后端返回的查询版本号
        const responseVersion = response.data.currentQueryVersion;

        // 校验查询版本号，只处理匹配版本号的消息
        if (responseVersion !== this.currentQueryVersion) {
          console.log('收到过期版本的消息，忽略处理。当前版本:', this.currentQueryVersion, '消息版本:', responseVersion);
          return;
        }

        // 忽略终止消息
        if (response.data.status === 499) {
          return;
        }

        if (response.data.status === 200) {
          this.countList = response.data.list;
          this.rate = response.data.rate;

          // 当搜索完成时恢复搜索按钮状态
          if (this.rate >= 100) {
            this.isSearching = false;
            this.currentQueryVersion = null;
            this.$nextTick(() => this.resetButtonStyle());
          }
        } else {
          // 处理搜索失败的情况
          const errorMsg = response.data.msg || '搜索失败';
          this.$message({
            type: 'error',
            message: errorMsg,
            duration: 5000
          });
          console.log('搜索失败:', errorMsg);

          // 恢复搜索按钮状态
          this.isSearching = false;
          this.currentQueryVersion = null;
          this.$nextTick(() => this.resetButtonStyle());
        }
      } catch (error) {
        console.error('处理WebSocket响应时出错:', error);
      }
    },

    // 连接失败处理 - 提示用户刷新页面
    handleConnectionFailure() {
      this.$confirm('聚合搜索通信通道连接失败，请刷新页面再试', '连接失败', {
        confirmButtonText: '刷新页面',
        cancelButtonText: '取消',
        type: 'error',
        closeOnClickModal: false,
        closeOnPressEscape: false
      }).then(() => {
        // 用户点击刷新页面
        window.location.reload();
      }).catch(() => {
        // 用户点击取消，恢复搜索按钮状态
        this.isSearching = false;
        this.currentQueryVersion = null;
        this.$nextTick(() => this.resetButtonStyle());
      });
    },

    // 工具方法：延迟
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
  }
}
</script>
<style lang="scss" scoped>
.search-item {
  margin-top: 20px;
  padding: 0 10px;
}
.el-tag {
  height: 30px;
  line-height: 28px;
  max-width: 200px;
  word-break: keep-all;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
  margin: 3px 5px;
  position: relative;
  padding-right: 20px;
}

.el-tag >>>.el-icon-close {
  top: 6px;
  right: 2px;
  position: absolute;
}

.tag-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 2px 0;
}

.record-tag {
  margin: 0;
  padding: 6px 14px;
  height: auto;
  line-height: 1.5;
  background-color: rgba(13, 30, 55, 0.7);
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.25s;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.08), 0 2px 4px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(1px);
}

.record-tag:hover {
  background-color: rgba(22, 45, 80, 0.85);
  transform: translateY(-1px);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.12), 0 3px 6px rgba(0, 0, 0, 0.25);
}

.record-tag.is-active {
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
  transform: translateY(-1px);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15), 0 3px 8px rgba(24, 144, 255, 0.4);
}

.el-select .el-input {
  width: 130px;
}
.input-with-select .el-input-group__append {
  background-color: #fff;
}
.input-file-size {
  width: 160px;
  vertical-align: middle;
}

/* 审计类型容器 - 暗色主题默认样式 */
.scene-type-container {
  background-color: #0c223b;
  background-image: linear-gradient(to bottom, #0f2a45, #0c223b);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: hidden;
}

.scene-type-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, rgba(24, 144, 255, 0.7), transparent);
}

/* 更新标签相关样式 */
.record-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.record-type-title {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  display: flex;
  align-items: center;
}

.record-type-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #1890ff;
  margin-right: 8px;
  border-radius: 2px;
}

.count-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
  height: 18px;
  padding: 0 5px;
  margin-left: 10px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.toggle-control {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(15, 37, 63, 0.5);
  padding: 3px 10px;
  border-radius: 16px;
}

.toggle-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
}

/* 明亮主题 */
body.theme-light {
  .scene-type-container {
    background-color: #f5f7fa;
    background-image: linear-gradient(to bottom, #ffffff, #f5f7fa);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.06);
  }

  .scene-type-container::before {
    background: linear-gradient(to right, transparent, rgba(24, 144, 255, 0.5), transparent);
  }

  .record-type-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }

  .record-type-title {
    color: #333;
  }

  .record-tag {
    background-color: rgba(230, 235, 240, 0.8);
    color: #333;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  }

  .record-tag:hover {
    background-color: rgba(210, 220, 230, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .record-tag.is-active {
    background-color: #1890ff;
    color: white;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.25);
  }

  .count-badge {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .toggle-control {
    background-color: rgba(230, 235, 240, 0.9);
  }

  .toggle-label {
    color: rgba(0, 0, 0, 0.65);
  }
}

/* 深黑主题 */
body.theme-dark {
  .scene-type-container {
    background-color: #141414;
    background-image: linear-gradient(to bottom, #1f1f1f, #141414);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.03);
  }

  .record-type-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.07);
  }

  .record-tag {
    background-color: rgba(30, 30, 30, 0.7);
  }

  .record-tag:hover {
    background-color: rgba(45, 45, 45, 0.8);
  }
}

/* 自定义主题 */
body.theme-custom {
  .scene-type-container {
    background-color: #051a30;
    background-image: linear-gradient(to bottom, #072548, #051a30);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  }

  .record-type-title::before {
    background-color: #13c2c2;
  }

  .record-tag {
    background-color: rgba(13, 42, 67, 0.7);
  }

  .record-tag:hover {
    background-color: rgba(18, 53, 83, 0.8);
  }

  .record-tag.is-active {
    background-color: #13c2c2;
    box-shadow: 0 3px 8px rgba(19, 194, 194, 0.3);
  }
}

/* 根据实际主题类名调整 */
/* 默认主题 */
body.default {
  .scene-type-container {
    background-color: #0c223b;
    background-image: linear-gradient(to bottom, #0f2a45, #0c223b);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .scene-type-container::before {
    background: linear-gradient(to right, transparent, rgba(24, 144, 255, 0.7), transparent);
  }

  .record-type-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .record-type-title {
    color: #fff;
  }

  .record-type-title::before {
    background-color: #1890ff;
  }

  .record-tag {
    background-color: rgba(13, 30, 55, 0.7);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.08), 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .record-tag:hover {
    background-color: rgba(22, 45, 80, 0.85);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.12), 0 3px 6px rgba(0, 0, 0, 0.25);
  }

  .record-tag.is-active {
    background-color: rgba(24, 144, 255, 0.9);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15), 0 3px 8px rgba(24, 144, 255, 0.4);
  }

  .count-badge {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .toggle-control {
    background-color: rgba(15, 37, 63, 0.5);
  }

  .toggle-label {
    color: rgba(255, 255, 255, 0.7);
  }
}

/* 蓝色主题 */
body.custom-blue {
  .scene-type-container {
    background-color: #003a8c;
    background-image: linear-gradient(to bottom, #004eb8, #003a8c);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .scene-type-container::before {
    background: linear-gradient(to right, transparent, rgba(64, 169, 255, 0.8), transparent);
  }

  .record-type-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  }

  .record-type-title {
    color: #fff;
  }

  .record-type-title::before {
    background-color: #40a9ff;
  }

  .record-tag {
    background-color: rgba(0, 45, 100, 0.7);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.08), 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .record-tag:hover {
    background-color: rgba(0, 65, 140, 0.85);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.12), 0 3px 6px rgba(0, 0, 0, 0.25);
  }

  .record-tag.is-active {
    background-color: rgba(64, 169, 255, 0.9);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15), 0 3px 8px rgba(64, 169, 255, 0.4);
  }

  .count-badge {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .toggle-control {
    background-color: rgba(0, 58, 140, 0.6);
  }

  .toggle-label {
    color: rgba(255, 255, 255, 0.8);
  }
}

/* 绿色主题 */
body.custom-green {
  .scene-type-container {
    background-color: #006d75;
    background-image: linear-gradient(to bottom, #08979c, #006d75);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .scene-type-container::before {
    background: linear-gradient(to right, transparent, rgba(54, 207, 201, 0.7), transparent);
  }

  .record-type-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  }

  .record-type-title {
    color: #fff;
  }

  .record-type-title::before {
    background-color: #36cfc9;
  }

  .record-tag {
    background-color: rgba(0, 75, 80, 0.7);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.08), 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .record-tag:hover {
    background-color: rgba(8, 110, 115, 0.85);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.12), 0 3px 6px rgba(0, 0, 0, 0.25);
  }

  .record-tag.is-active {
    background-color: rgba(54, 207, 201, 0.9);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15), 0 3px 8px rgba(54, 207, 201, 0.4);
  }

  .count-badge {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .toggle-control {
    background-color: rgba(0, 109, 117, 0.6);
  }

  .toggle-label {
    color: rgba(255, 255, 255, 0.8);
  }
}

/* 搜索进度条样式 */
.search-progress-container {
  margin: 15px 0;
  padding: 16px 20px;
  background: linear-gradient(to right, rgba(13, 30, 55, 0.8), rgba(20, 50, 90, 0.7));
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: hidden;
}

.search-progress-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(64, 169, 255, 0.8), transparent);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-title {
  color: white;
  font-size: 15px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.progress-icon {
  margin-right: 8px;
  color: #1890ff;
  font-size: 16px;
}

.spin {
  animation: spin 1.5s infinite linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.progress-percentage {
  color: #40a9ff;
  font-size: 16px;
  font-weight: 600;
}

.custom-progress {
  margin-top: 5px;
}

.custom-progress >>> .el-progress-bar__outer {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 4px;
}

.custom-progress >>> .el-progress-bar__inner {
  background: linear-gradient(90deg, #0078ff, #40a9ff) !important;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 主题适配 */
body.default .search-progress-container {
  background: linear-gradient(to right, rgba(13, 30, 55, 0.8), rgba(20, 50, 90, 0.7));
}

body.custom-blue .search-progress-container {
  background: linear-gradient(to right, rgba(0, 45, 100, 0.8), rgba(0, 78, 184, 0.7));
}

body.custom-blue .progress-icon,
body.custom-blue .progress-percentage {
  color: #40a9ff;
}

body.custom-blue .custom-progress >>> .el-progress-bar__inner {
  background: linear-gradient(90deg, #1890ff, #69c0ff) !important;
}

body.custom-green .search-progress-container {
  background: linear-gradient(to right, rgba(0, 75, 80, 0.8), rgba(8, 151, 156, 0.7));
}

body.custom-green .progress-icon,
body.custom-green .progress-percentage {
  color: #36cfc9;
}

body.custom-green .custom-progress >>> .el-progress-bar__inner {
  background: linear-gradient(90deg, #13c2c2, #5cdbd3) !important;
  box-shadow: 0 0 10px rgba(19, 194, 194, 0.5);
}

.tag-loading-icon {
  margin-right: 5px;
  font-size: 14px;
  color: #fff;
}

/* 搜索按钮样式 */
.searching-button {
  animation: pulse 1.5s infinite;
  background-color: #f56c6c !important; /* 强制使用红色背景 */
  border-color: #f56c6c !important; /* 强制使用红色边框 */
  color: white !important; /* 强制使用白色文字 */
  font-weight: bold;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}

.disabled-tag {
  opacity: 0.6;
  cursor: not-allowed !important;
}

.record-tag.disabled-tag:hover {
  transform: none !important;
  background-color: inherit !important;
  box-shadow: inherit !important;
}

/* 不同主题下的禁用样式 */
body.default .record-tag.disabled-tag:hover {
  background-color: rgba(13, 30, 55, 0.7) !important;
}

body.custom-blue .record-tag.disabled-tag:hover {
  background-color: rgba(0, 45, 100, 0.7) !important;
}

body.custom-green .record-tag.disabled-tag:hover {
  background-color: rgba(0, 75, 80, 0.7) !important;
}

/* 审计类型提示文字样式 */
.audit-type-tip-text {
  font-size: 11px;
  color: #ff9500;
  margin-left: 12px;
  opacity: 0;
  animation: fadeInSlide 0.5s ease-out 0.3s forwards;
}

@keyframes fadeInSlide {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
