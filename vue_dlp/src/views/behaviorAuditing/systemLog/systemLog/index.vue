<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem v-if="logType==1" model-key="query.logLevel" :value="query.logLevel">
          <span>{{ $t('pages.logLevel') }}：</span>
          <el-select v-model="query.logLevel" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option v-for="(label, value) in logLevelOptions" :key="value" :label="label" :value="value" />
          </el-select>
        </SearchItem>
        <SearchItem v-if="logType==2" model-key="query.eventType" :value="query.eventType">
          <span>{{ $t('pages.operateEvent') }}：</span>
          <el-select v-model="query.eventType" clearable style="width: 150px;">
            <el-option :label="$t('pages.all')" :value="null" />
            <el-option
              v-for="(label, value) in eventTypeOptions"
              :key="value"
              :label="label"
              :value="value"
              :style="eventTypeOptStyle(value)"
            /></el-select>
        </SearchItem>
        <audit-log-exporter slot="append" v-permission="logType === 1 ? '328' : '329'" :request="handleExport"/>
        <audit-log-delete
          v-if="$store.getters.auditingDeleteAble"
          slot="append"
          v-permission="logType === 1 ? '440' : '441'"
          :selection="selection"
          :delete-log="deleteLog"
          :table-getter="gridTable"
        />
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :multi-select="$store.getters.auditingDeleteAble && (logType === 1 ? hasPermission('440') : hasPermission('441'))"
        :default-sort="defaultSort"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="1 === logType ? $t('pages.systemLogDetails') : $t('pages.systemStateLogDetails')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.logStartTime')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.userGroupName')">
            {{ rowDetail.userGroupName }}
          </el-descriptions-item>
          <el-descriptions-item v-if="logType !== 2" :label="$t('table.logRecNum')">
            {{ rowDetail.recordId }}
          </el-descriptions-item>
          <el-descriptions-item v-if="logType !== 2" :label="$t('table.eventId')">
            {{ rowDetail.eventId }}
          </el-descriptions-item>
          <el-descriptions-item v-if="logType !== 2" :label="$t('table.logLevel')">
            {{ logLevelFormatter(rowDetail) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.logSource')">
            {{ rowDetail.logSource }}
          </el-descriptions-item>
          <el-descriptions-item v-if="logType !== 1" span="2" :label="$t('table.operateEvent')">
            {{ eventTypeFormatter(rowDetail, rowDetail.eventId) }}
          </el-descriptions-item>
          <el-descriptions-item span="2" :label="$t('table.eventDesc')">
            {{ rowDetail.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import {
  getLogPage,
  getLogStatePage,
  deleteLog,
  deleteStateLog,
  exportSystemLog,
  exportSystemStateLog
} from '@/api/behaviorAuditing/systemLog/systemLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'
import { logSourceFormatter } from '@/utils/formatter'
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo'

export default {
  name: 'SystemLog',
  props: {
    logType: {
      type: Number,
      default: 1  // 1系统日志，2终端操作日志
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'terminalName', label: 'terminalName', width: '100', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '100' },
        { prop: 'userGroupName', label: 'userGroupName', width: '100', custom: true },
        { prop: 'createTime', label: 'logStartTime', width: '120', sort: true, formatter: logSourceFormatter },
        // { prop: 'sysLogDate', label: '日志发生时间', width: '120' },
        { prop: 'recordId', hidden: this.logType == 2, label: 'logRecNum', width: '80' },
        { prop: 'eventId', hidden: this.logType == 2, label: 'eventId', width: '80' },
        { prop: 'log_level', label: 'logLevel', hidden: this.logType == 2, sort: 'custom', width: '100', formatter: this.logLevelFormatter },
        { prop: 'logSource', label: 'logSource', width: '80' },
        { prop: 'eventId', label: 'operateEvent', hidden: this.logType == 1, width: '100', formatter: this.eventTypeFormatter },
        { prop: 'remark', label: 'eventDesc', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: this.operateHidden,
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      defaultSort: { prop: 'createTime', order: 'descending' },
      logLevelOptions: {
        0: this.$t('pages.keyword'),
        1: this.$t('text.error'),
        2: this.$t('text.warning'),
        3: this.$t('pages.info')
        /*, 4: '审核成功', 5: '审核失败'*/
      },
      eventTypeOptions: {
        1: this.$t('pages.sleepStart'),
        507: this.$t('pages.sleepStart'),
        42: this.$t('pages.sleep'),
        506: this.$t('pages.sleep'),
        6008: this.$t('pages.unexpectedShutdown'),
        6006: this.$t('pages.shutDown'),
        13: this.$t('pages.shutDown'),
        6005: this.$t('pages.powerOn'),
        12: this.$t('pages.powerOn'),
        4647: this.$t('pages.cancellation'),
        7002: this.$t('pages.cancellation'),
        4648: this.$t('pages.logoutThenLogin'),
        7001: this.$t('pages.logoutThenLogin'),
        4802: this.$t('pages.inScreensaver'),
        4803: this.$t('pages.outScreensaver'),
        4800: this.$t('pages.lockScreen'),
        4801: this.$t('pages.unlockScreen'),
        4778: this.$t('pages.reSwitchUser'),
        4779: this.$t('pages.switchUserMiss')
      },
      // 过滤eventType相同的属性
      filterEventType: [13, 12, 7002, 7001, 506, 507],
      logTypeOptions: {
        1: this.$t('pages.system'),
        2: this.$t('pages.terminalOperate')
      },
      query: { // 查询条件
        page: 1,
        logLevel: null,
        eventType: null,
        logType: this.logType,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        isSysTimes: false,
        sysLogDate: '',
        sysLogDateStart: '',
        sysLogDateEnd: '',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      selection: [],
      rowDetail: {},
      dialogFormVisible: false,
      queryVideoMethod: undefined,
      sortable: true,
      termsInfo: [] // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
    }
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
    addViewVideoBtn(this)
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    eventTypeOptStyle(optKey) {
      return { display: !this.filterEventType.includes(Number(optKey)) ? 'list-item' : 'none' }
    },
    gridTable() {
      return this.$refs['logList']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    strategyTargetNodeChange(tabName, data) {
      if (data) {
        this.query.objectType = data.type
        this.query.objectId = data.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      if (this.logType === 1) {
        return getLogPage(searchQuery)
      } else if (this.logType === 2) {
        return getLogStatePage(searchQuery)
      }
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    deleteLog(data) {
      if (this.logType === 1) {
        return deleteLog(data)
      }
      if (this.logType === 2) {
        return deleteStateLog(data)
      }
      return Promise.reject('Unsupported log type: ' + this.logType)
    },
    eventTypeFormatter: function(row, data) {
      return this.eventTypeOptions[data]
    },
    logLevelFormatter: function(row, data) {
      return this.logLevelOptions[row.logLevel]
    },
    operateHidden() {
      // 278 计算机系统日志-查看详情  279 计算机状态变更日志-查看详情
      return (1 === this.logType ? !this.hasPermission('278') : !this.hasPermission('279'))
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      if (this.logType === 1) {
        return exportSystemLog(formData)
      } else if (this.logType === 2) {
        return exportSystemStateLog(formData)
      }
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
      this.queryVideoMethod = asyncGetLogVideoInfo(rowData, () => !this.operateHidden())
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    }
  }
}
</script>
