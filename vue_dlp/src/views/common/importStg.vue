<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      :modal-append-to-body="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogVisible"
      :width="dialogWidth + 'px'"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="130px" :style="{ width: (dialogWidth-80) + 'px' }">
        <FormItem v-if="termAble || userAble" :label="$t('pages.effectiveObject')" prop="objectType">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.effectiveObject') }}<br/>
              <label v-if="termAble">{{ $t('pages.processStgLib_Msg84') }}</label><br/>
              <label v-if="userAble">{{ $t('pages.processStgLib_Msg85') }}</label><br/>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-row>
            <el-col :span="8">
              <el-select v-model="temp.objectType" :disabled="showUploadInfo" @change="objectTypeChange">
                <el-option v-if="termAble" :value="1" :label="$t('pages.terminal')"/>
                <el-option v-if="userAble" :value="2" :label="$t('pages.user')"/>
              </el-select>
            </el-col>
            <el-col :span="16">
              <tree-select
                ref="objectTree"
                node-key="id"
                is-filter
                :local-search="false"
                :filter-key="filterKey"
                :leaf-key="temp.objectType === 1 ? 'terminal' : 'user'"
                :disabled="showUploadInfo"
                @change="objectTreeSelectChange"
              />
            </el-col>
          </el-row>
        </FormItem>
        <FormItem v-if="osAble" :label="$t('pages.osType')" prop="osType">
          <el-radio-group v-model="temp.osType" :disabled="showUploadInfo">
            <el-radio :label="1">Window</el-radio>
            <el-radio :label="2">Linux</el-radio>
            <el-radio :label="4">Mac</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="importTypeAble" :label="$t('pages.repeatNameDealType')" prop="objectType">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.isGlobalUnique') }}<br/>
              {{ $t('pages.importAndUpdate_Msg') }}<br/>
              {{ $t('pages.importAndIgnore_Msg') }}<br/>
              {{ importType2.tipMsg }}<br/>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-radio-group v-model="temp.importType" :disabled="showUploadInfo">
            <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
            <el-radio :label="2">{{ importType2.name }}</el-radio>
          </el-radio-group>
        </FormItem>
        <span v-show="errorMsg" style="color:red; position: absolute; right: 10px; margin-top: 10px;">{{ errorMsg }}</span>
        <el-upload
          ref="uploadFileList"
          action="1111"
          class="upload-demo"
          style="display:inline-block;max-width: 670px;"
          :limit="fileLimit"
          multiple
          list-type="text"
          :file-list="fileList"
          :accept="accept"
          :auto-upload="false"
          :on-change="onFileChange"
          :on-remove="onFileChange"
          :before-upload="beforeUpload"
          :http-request="onUpload"
          :disabled="showUploadInfo"
        >
          <el-tooltip v-show="!showUploadInfo" class="item" effect="dark" :content="$t('pages.processStgLib_Msg74', { num: fileLimit })" placement="right-start">
            <el-button size="small" type="primary">{{ uploadBtnName }}</el-button>
          </el-tooltip>
          <i v-show="!showUploadInfo && fileList.length > 0" class="el-icon-delete-solid" :title="$t('pages.processStgLib_Msg76')" @click="clearFiles"></i>
          <div slot="tip" class="el-upload__tip">
            {{ stgNameRuleRemark }}
          </div>
        </el-upload>
      </Form>
      <el-card v-if="showUploadInfo" :body-style="{'padding': '0'}" class="box-card">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.processStgLib_Msg86') }}</span>
        </div>
        <ul style="margin: 0px;">
          <li>{{ $t('pages.processStgLib_Msg87') }}</li>
          <li v-for="(value, key) in uploadStatus" :key="key">{{ key }}
            <i v-if="'success' === value" class="el-icon-check" style="color: #558dee;"/>
            <i v-if="'success' !== value" class="el-icon-close" style="color: #558dee;"/>
            <i v-if="'success' !== value" class="el-icon-info" :title="formatTip(value)"/>
          </li>
          <li v-show="isUploadEndStatus">{{ $t('pages.processStgLib_Msg88') }}</li>
        </ul>
      </el-card>

      <div slot="footer" class="dialog-footer">
        <el-button v-if="!showUploadInfo" type="primary" :loading="submitting" @click="createData">{{ $t('button.confirm') }}</el-button>
        <el-button v-if="!showUploadInfo" @click="hide">{{ $t('button.cancel') }}</el-button>
        <el-button v-if="showUploadInfo" @click="hide">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from '@/utils/request'

export default {
  name: 'ImportStg',
  props: {
    dataType: { type: String, default: '1' }, // 导入数据类型：1-透明加密策略库；2-半透明加密策略；11-敏感内容识别；-1 - 策略通用导入, -2:自动匹配部门导入策略
    osType: { type: Number, default: 1 }, // 进程系统类型：1-windows，2-linux，4-mac
    osAble: { type: Boolean, default: false }, // 进程系统类型：1-windows，2-linux，4-mac
    importTypeAble: { type: Boolean, default: true }, //  导入类型
    title: {
      type: String,
      default: function() {
        return this.$t('pages.processStgLib_Msg89')
      }
    },
    importType2: {
      type: Object,
      default: function() {
        return {
          name: this.$t('pages.importAndRename'),
          tipMsg: this.$t('pages.importAndRename_Msg')
        }
      }
    },
    //  文件数量限制
    fileLimit: {
      type: Number,
      default: 10
    },
    accept: { type: String, default: '.ldt' },
    termAble: { type: Boolean, default: true },
    userAble: { type: Boolean, default: true },
    autoName: { type: Boolean, default: true }, // 自动设置名称
    uploadFunc: { // 上传函数
      type: Function,
      default: null
    },
    strategyTypeNumber: { type: Number, default: null } //  当dataType为-1时（即：导入数据类型为策略通用导入类型），需要指定导入的策略类型编号，否则可以导入任何类型编号
  },
  data() {
    return {
      taskId: '',
      fileList: [],
      dialogVisible: false,
      dialogWidth: 600,
      submitting: false,
      submitReturnSize: 0,
      uploadBtnName: '',
      errorMsg: '',
      temp: {
        objectType: 1,
        importType: 1,
        osType: this.osType,
        objectIds: [],
        objectGroupIds: [],
        strategyTypeNumber: null
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ]
      },
      selectedUserData: [],
      stgNameRule: 1,
      validateFail: false,
      stgNameRuleRemark: '',
      showUploadInfo: false,
      isUploadEndStatus: false,
      uploadStatus: {},
      checkedObject: [],
      // 后端 ImportErrorTypeDict
      importFailTypeMap: {
        1: this.$t('pages.fileReadFail'),
        2: this.$t('pages.fileTypeIllegal'),
        3: this.$t('pages.fileDataIllegal')
      },
      filterKey: 'G-2'
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
  },
  methods: {
    show() {
      this.resetTemp()
      this.dialogVisible = true
    },
    objectTree() {
      return this.$refs.objectTree
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.showUploadInfo = false
      this.submitting = false
      this.errorMsg = ''
      this.fileList.splice(0)
      this.submitReturnSize = 0
      this.checkedObject.splice(0)
      this.temp.objectIds.splice(0)
      this.temp.objectGroupIds.splice(0)
      this.temp.importType = 1
      this.temp.osType = this.osType
      this.temp.objectType = this.termAble ? 1 : 2
      this.stgNameRuleRemark = ''
      if (this.autoName) {
        this.stgNameRuleRemark = this.termAble || this.userAble
          ? this.$t('pages.processStgLib_Msg90')
          : this.$t('pages.processStgLib_Msg91')
        if (this.dataType == '11') {
          this.stgNameRuleRemark = this.termAble || this.userAble ? this.$t('pages.processStgLib_Msg92') : this.$t('pages.processStgLib_Msg93');
        }
      }
      if (!this.termAble && !this.userAble) {
        this.dialogWidth = 600
      }
      this.uploadBtnName = this.$t('pages.processStgLib_Msg94', { format: this.accept })
    },
    objectTypeChange(val) {
      this.checkedObject.splice(0)
      if (this.objectTree()) {
        this.objectTree().clearSelectedNode()
      }
    },
    objectTreeSelectChange(nodeId, node) {
      const nodeObj = node[0] || node
      this.checkedObject.splice(0, 1, nodeObj)
      this.errorMsg = ''
    },
    beforeUpload(file) {
      this.validateFail = false
      if (!this.termAble && !this.userAble) {
        return
      }
      const temp = this.temp
      temp.objectIds = []
      temp.objectGroupIds = []
      this.checkedObject.forEach(node => {
        if ([3, '3', 4, '4', 'G'].includes(node.type)) {
          temp.objectGroupIds.push(node.dataId)
        } else {
          temp.objectIds.push(node.dataId)
        }
      })
      if (temp.objectIds.length === 0 && temp.objectGroupIds.length === 0) {
        this.validateFail = true
      }
    },
    onUpload(data) {
      if (this.validateFail) {
        this.errorMsg = this.$t('components.chooseApplicationObj')
        return false
      }
      this.showUploadInfo = true
      this.submitting = true
      const fileName = data.file.name
      const fd = new FormData()
      // fd.append('uploadFile', data.file)// 传文件
      // fd.append('importType', this.temp.importType)
      // fd.append('objectType', this.temp.objectType)
      // fd.append('objectIds', this.temp.objectIds)
      // fd.append('objectGroupIds', this.temp.objectGroupIds)

      fd.append('file', data.file)// 传文件
      fd.append('taskType', this.dataType)
      //  如果为-1，表示导入弹窗为策略导入弹窗，需要指定导入的策略类型
      if (this.dataType === '-1') {
        this.temp.strategyTypeNumber = this.strategyTypeNumber
      }
      fd.append('taskId', this.taskId)
      fd.append('size', '' + this.fileList.length)
      fd.append('param', JSON.stringify(this.temp))
      const execFunc = !this.uploadFunc ? this.defaultUploadDataFunc : this.uploadFunc
      execFunc(fd).then(res => {
        // this.$notify({ title: this.$t('text.success'), message: fileName + '文件导入成功', type: 'success', duration: 2000 })
        this.uploadEnd()
      }).catch(res => {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.processStgLib_Msg80', { name: fileName }), type: 'error', duration: 2000 })
        this.uploadEnd()
      })
      return false // 屏蔽了action的默认上传
    },
    uploadEnd() {
      this.submitReturnSize++
      if (this.submitReturnSize === this.fileList.length) {
        this.submitting = false
        // this.hide()
        this.$emit('success', this)
      }
    },
    onFileChange(file, list) {
      this.fileList = this.filterSameNameList(file, list)
      this.errorMsg = ''
    },
    // 去重
    filterSameNameList(file, list) {
      // 相同名称计数
      let sameNameCount = 0;
      list.forEach((item, index) => {
        if (file.name === item.name) {
          sameNameCount++
          if (sameNameCount === 2) {
            list.splice(index, 1);
            this.$message({
              message: this.$t('pages.vml_Msg11', { fileName: item.name }),
              type: 'error',
              duration: 2000
            })
          }
        }
      })
      return list;
    },
    clearFiles(e) {
      window.event ? window.event.cancelBubble = true : e.stopPropagation()
      this.fileList = []
      this.submitReturnSize = 0
      this.$refs.uploadFileList.clearFiles()
    },
    onExceedFunc(files, prevFiles) {
      this.$notify({ title: this.$t('pages.processStgLib_Msg96'), message: this.$t('pages.processStgLib_Msg97'), type: 'warning', duration: 2000 })
    },
    handleDrag() {
    },
    createData() {
      if (this.fileList.length === 0) {
        this.errorMsg = this.$t('pages.processStgLib_Msg98')
        return
      }
      this.uploadStatus = {}
      this.isUploadEndStatus = false
      this.taskId = new Date().getTime()
      this.$socket.subscribeToUser(this.taskId, '/import/data', (resp, handle) => {
        this.uploadStatus = resp.data.dealFiles
        this.isUploadEndStatus = this.fileList.length === Object.keys(this.uploadStatus).length
        if (this.isUploadEndStatus) {
          handle.close()
          this.$emit('success', this)
        }
      }, false)
      this.$refs.uploadFileList.submit()
    },
    defaultUploadDataFunc(data) {
      return request.post('/importData/upload', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    formatTip(tip) {
      try {
        const tipJson = JSON.parse(tip)
        if (typeof tipJson === 'object') {
          const i18nValue = this.importFailTypeMap[tipJson['errorType']]
          return i18nValue ? `${this.$t('text.importFail')}, ${i18nValue} !` : tip
        }
      } catch (e) { return tip }
      return tip
    }
  }
}
</script>

<style lang="scss" scoped>
  .upload-demo>>>.el-upload-list{
    min-height: 20px;
    max-height: 188px;
    overflow: auto;
    margin-top: 2px;
    .el-upload-list__item:hover {
      background-color: #dbdbdb;
    }
  }
  .el-icon-delete-solid{
    margin-left: 20px;
    font-size: 16px;
    &:hover{
      color: rgb(12, 123, 226);
    }
  }
</style>
