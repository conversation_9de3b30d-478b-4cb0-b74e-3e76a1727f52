<template>
  <el-dialog
    v-el-drag-dialog
    :append-to-body="appendToBody"
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="dialogVisible"
    width="400px"
    @dragDialog="handleDrag"
  >
    <Form ref="groupForm" :rules="rules" :model="temp" label-position="right" label-width="100px">
      <FormItem v-if="showName" :label="groupLabel" prop="name">
        <el-input v-model="temp.name" v-trim maxlength="30"/>
      </FormItem>
      <FormItem v-if="showTree" :label="groupTreeLabel" prop="parentId">
        <tree-select
          ref="groupTree"
          :width="280"
          :data="selectTreeData"
          node-key="dataId"
          :filter-key="filterKeys"
          :checked-keys="checkedKeys"
          @change="treeSelectChange"
        />
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="doSubmit">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>
<script>

export default {
  name: 'EditGroupDlg',
  props: {
    appendToBody: { type: Boolean, default: false },
    title: { type: String, default: '' },
    groupLabel: { type: String, default() { return this.$t('form.groupName') } },
    rulesGroupLabel: { type: String, default() { return this.$t('valid.requireGroupName') } },
    parentLabel: { type: String, default() { return this.$t('form.parentGroup') } },
    showParent: { type: Boolean, default: false },
    groupTreeData: { type: Array, default() { return [] } },
    extraParam: { type: Object, default() { return {} } },  // 额外参数
    addFunc: { type: Function, default: null }, // 新增方法
    updateFunc: { type: Function, default: null }, // 修改方法
    deleteFunc: { type: Function, default: null }, // 删除方法
    moveFunc: { type: Function, default: null }, // 移动分组
    editValidFunc: { type: Function, default: null } // 新增、修改校验方法
  },
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        parentId: 0,
        remark: '',
        ids: undefined
      },
      rules: {
        name: [
          { required: true, message: this.rulesGroupLabel, trigger: 'blur' },
          { validator: this.groupNameValidator, trigger: 'blur' }
        ],
        parentId: [
          { required: true, validator: this.parentIdValidator, trigger: 'blur' }
        ]
      },
      showName: true,
      showTree: false,
      dialogVisible: false,
      dialogStatus: '',
      submitting: false,
      checkedKeys: [],
      groupTreeLabel: null,
      filterKeys: [],
      isRemoved: false,
      removedGroupId: null,
      textMap: {
        createGroup: this.i18nConcatText(this.title, 'create'),
        updateGroup: this.i18nConcatText(this.title, 'update'),
        moveGroup: this.$t('button.moveGroup')
      },
      groupTreeDataJSON: '[]' // 保存groupTreeData数据的JSON字符串，用于监听数据是否发生变化。（对象层级较深时，监听不到数据的变化）
    }
  },
  computed: {
    selectTreeData() {
      return JSON.parse(this.groupTreeDataJSON)
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    treeSelectChange(data) {
      this.checkedKeys.splice(0, this.checkedKeys.length, data)
    },
    doSubmit() {
      if (this.dialogStatus === 'moveGroup') {
        this.moveGroup()
      } else if (this.dialogStatus === 'createGroup') {
        this.createNode()
      } else {
        this.updateNode()
      }
    },
    createNode() {
      this.submitting = true
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp, { parentId: this.getParentId() }, this.extraParam)
          this.addFunc(tempData).then(respond => {
            this.submitting = false
            this.dialogVisible = false
            tempData.id = respond.data.id
            this.$emit('addEnd', tempData)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateNode() {
      this.submitting = true
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp, { parentId: this.getParentId() }, this.extraParam)
          this.updateFunc(tempData).then(resp => {
            this.submitting = false
            this.dialogVisible = false
            this.$emit('updateEnd', tempData)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    moveGroup() {
      this.submitting = true
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          this.moveFunc({ parentId: this.getParentId(), ids: this.temp.ids }).then(() => {
            this.submitting = false
            this.dialogVisible = false
            this.$emit('moveEnd', this.getParentId())
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    resetTemp() {
      this.showName = true
      this.showTree = this.showParent
      this.isRemoved = false
      this.removedGroupId = null
      this.checkedKeys.splice(0)
      this.filterKeys.splice(0)
      this.temp = Object.assign({}, this.defaultTemp)
      this.groupTreeLabel = this.parentLabel
      this.groupTreeDataJSON = JSON.stringify(this.groupTreeData)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate(parentId) {
      this.resetTemp()
      this.temp.parentId = parentId
      this.dialogStatus = 'createGroup'
      this.dialogVisible = true
      this.$nextTick(() => {
        if (parentId !== undefined && parentId !== null && parentId !== '') {
          this.checkedKeys.splice(0, 0, parentId)
        }
        this.$refs['groupForm'].clearValidate()
      })
    },
    handleUpdate(data) {
      this.resetTemp()
      this.temp = Object.assign({}, data)
      const { parentId, id } = this.temp
      this.dialogStatus = 'updateGroup'
      this.dialogVisible = true
      this.$nextTick(() => {
        if (parentId !== undefined && parentId !== null && parentId !== '') {
          this.checkedKeys.splice(0, 0, parentId)
        }
        if (id) {
          this.filterKeys.push(id)
        }
        this.$refs['groupForm'].clearValidate()
      })
    },
    handleDelete(dataId) {
      this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
        this.deleteFunc({ id: dataId }).then(respond => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          this.$emit('deleteEnd', dataId)
          this.$parent.refreshTableData && this.$parent.refreshTableData()
        })
      }).catch(() => {})
    },
    handleMove(dataIds) {
      this.resetTemp()
      this.showTree = true
      this.showName = false
      this.temp.ids = dataIds.join(',')
      this.groupTreeLabel = this.$t('pages.moveToGroup')
      this.dialogStatus = 'moveGroup'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['groupForm'].clearValidate()
      })
    },
    groupNameValidator(rule, value, callback) {
      if (value && value.indexOf('->') > -1) {
        callback(new Error(this.$t('valid.specialGroupStr')))
      } else if (this.showName && this.editValidFunc) {
        this.editValidFunc(Object.assign({}, { parentId: this.getParentId(), name: value }, this.extraParam)).then(respond => {
          const group = respond.data
          if (group && group.id != null && group.id != this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      } else {
        callback()
      }
    },
    parentIdValidator(rule, value, callback) {
      if (this.showTree && !this.checkedKeys.length) {
        callback(new Error(this.$t('valid.requireParentGroup')))
      } else {
        callback()
      }
    },
    getParentId() {
      return this.checkedKeys.length > 0 ? this.checkedKeys[0] : 0
    }
  }
}
</script>
