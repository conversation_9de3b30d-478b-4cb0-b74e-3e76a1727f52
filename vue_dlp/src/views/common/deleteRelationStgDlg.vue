<template>
  <el-dialog
    v-el-drag-dialog
    :append-to-body="appendToBody"
    :close-on-click-modal="false"
    :modal="false"
    :title="title"
    :visible.sync="visible"
    :width="dlgWidth"
  >
    <div :style="promptMessageStyle">{{ promptMessage }}</div>
    <grid-table
      ref="table"
      :row-no-label="rowNoLabel"
      :show-pager="false"
      :row-datas="data"
      :col-model="colModel"
      :height="tableHeight"
    >
    </grid-table>

    <div slot="footer" class="dialog-footer">
      <el-button size="mini" :loading="loading" type="primary" @click="handleDelete">
        {{ $t('button.delete') }}
      </el-button>
      <el-button size="mini" @click="handleCancel">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'DeleteRelationStgDlg',
  props: {
    //  标题
    title: {
      type: String,
      default() {
        return this.$t('text.delete')
      }
    },
    //  table高度
    tableHeight: {
      type: Number,
      default: 300
    },
    //  弹窗宽度
    dlgWidth: {
      type: String,
      default: '800px'
    },
    data: {
      type: Array,
      default() {
        return []
      }
    },
    colModel: {
      type: Array,
      default() {
        return []
      }
    },
    //  提示信息
    promptMessage: {
      type: String,
      default() {
        return this.$t('pages.deleteRelationStg_processMessage')
      }
    },
    //  提示信息样式
    promptMessageStyle: {
      type: Object,
      default() {
        return { color: '#68a8d0', float: 'left', 'margin-bottom': '5px' }
      }
    },
    //  删除数据的接口
    delete: {
      type: Function,
      default() {
        return new Promise((resolve, reject) => { resolve({ code: 20000, data: {}}) })
      }
    },
    rowNoLabel: { // 是否显示行号，并设置行号列表头名
      type: String,
      default: null
    },
    appendToBody: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      visible: false
    }
  },
  methods: {
    show() {
      this.visible = true
    },
    handleDelete() {
      const ids = this.$refs['table'].getSelectedKeys() || [];
      if (ids.length === 0) {
        this.$message({
          message: this.$t('pages.deleteRelationStg_text1'),
          type: 'error',
          duration: 2000
        })
        return;
      }
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        this.loading = true
        this.delete({ ids: ids.join(',') }).then(respond => {
          this.$emit('deleteSuccessAfter', ids)
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          this.visible = false
          this.loading = false
        });
      }).catch(() => { this.loading = false })
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
