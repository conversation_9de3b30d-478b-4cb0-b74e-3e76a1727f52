<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleFilter">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.validateMsg_processName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="gridTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChange"
      />
    </div>
    <batch-upload
      ref="batchUpload"
      :support-md5="true"
      :support-group="false"
      support-select-terminal-soft
      :create="addProcessBatch"
      @submitEnd="submitEnd"
    />
    <app-add-dlg
      ref="appAddDlg"
      :width="650"
      :support-md5="true"
      :support-group="false"
      :create="addProcess"
      :update="updateProcess"
      @submitEnd="appAddSubmitEnd"
    />
  </div>
</template>

<script>
import AppAddDlg from '@/views/system/baseData/appLibrary/appAddDlg'
import BatchUpload from '@/views/system/baseData/appLibrary/appBatchAddDlg'
import {
  addHookProcess,
  addHookProcessBatch, deleteHookProcess, getPage,
  updateHookProcess
} from '@/api/behaviorManage/application/hookAutiFake'

export default {
  name: 'HookProcessAutiFake',
  components: { AppAddDlg, BatchUpload },
  props: {
    businessType: {
      type: Number,
      required: true
    },
    reqRoute: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      deleteable: false,
      query: {
        searchInfo: ''
      },
      colModel: [
        { prop: 'processName', label: 'processName', width: '150', sort: 'custom' },
        { prop: 'checkMd5', label: 'checkMd5', width: '80', sort: 'custom', formatter: this.md5LevelFormatter },
        { prop: 'fileDescription', label: 'exeDesc', width: '150' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '60',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ]
    }
  },
  methods: {
    addProcess(data) {
      return addHookProcess(this.transferData(data), this.reqRoute)
    },
    addProcessBatch(data) {
      return addHookProcessBatch(this.transferData(data), this.reqRoute)
    },
    updateProcess(data) {
      return updateHookProcess(this.transferData(data), this.reqRoute)
    },
    gridTable() {
      return this.$refs['gridTable']
    },
    md5LevelFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      }
      if (row.checkMd5 == 1 || row.isCheckMd5 == 1) {
        return this.$t('text.enable')
      }
      return this.$t('text.disable2')
    },
    rowDataApi: function(option) {
      const params = Object.assign({}, this.query, option, { businessType: this.businessType })
      return getPage(params)
    },
    selectionChange(rowDatas) {
      this.deleteable = (rowDatas || []).length > 0
    },
    handleCreate() {
      this.$refs['batchUpload'].show()
    },
    handleUpdate(row) {
      this.$refs['appAddDlg'].show(row)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const ids = this.gridTable().getSelectedIds()
        if (!ids || ids.length === 0) { return }
        deleteHookProcess({ ids: ids.join(',') }, this.reqRoute).then(res => {
          if (res.data) {
            this.gridTable() && this.gridTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          }
        })
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable() && this.gridTable().execRowDataApi(this.query)
    },
    submitEnd() {
      this.gridTable() && this.gridTable().execRowDataApi()
    },
    appAddSubmitEnd(data) {
      this.gridTable() && this.gridTable().execRowDataApi()
    },
    transferData(data) {
      return Object.assign({}, data, { businessType: this.businessType })
    }
  }
}
</script>

<style scoped>

</style>
