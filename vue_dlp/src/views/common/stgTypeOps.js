import i18n from '@/lang'
import Vue from 'vue';

/**
 * 策略包子策略统一配置
 * <p>（C11 终端行为策略包、 D11 网络行为策略包、 E11 数据安全策略包）</p>
 */
export const stgTypeOps = {
  // 终端行为策略包 C11
  behaviorGroup: [
    { number: 94, menuCode: 'A64' },
    { number: 6, menuCode: 'C21' },
    { number: 7, menuCode: 'C22' },
    { number: 8, menuCode: 'C23' },
    { number: 143, menuCode: 'A6A' },
    { number: 159, menuCode: 'A6B' },
    { number: 19, menuCode: 'C31' },
    { number: 44, menuCode: 'E39', param: { stgTypeNumber: 44 }},
    { number: 129, menuCode: 'C34' },
    { number: 45, menuCode: 'C41' },
    { number: 1, menuCode: 'C51' },
    { number: 2, menuCode: 'C52', param: { stgTypeNumber: 2 }},
    { number: 48, menuCode: 'C61' },
    { number: 218, menuCode: 'C65' },
    { number: 228, menuCode: 'C67' },
    { number: 50, menuCode: 'C71' },
    { number: 200, menuCode: 'C74' },
    { number: 18, menuCode: 'C81' },
    { number: 135, menuCode: 'CA1' },
    { number: 163, menuCode: 'CA3' },
    { number: 158, menuCode: 'CA2' },
    { number: 70, menuCode: 'C91' },
    { number: 51, menuCode: 'B25' },
    { number: 211, menuCode: 'C92' },
    { number: 233, menuCode: 'B2Q' },
    { number: 240, menuCode: 'C93' },
    { number: 245, menuCode: 'C94' }
  ],
  // 网络行为策略包 D11
  netGroup: [
    { number: 12, menuCode: 'D21' },
    { number: 13, menuCode: 'D22' },
    { number: 63, menuCode: 'D2B' },
    { number: 206, menuCode: 'D2E' },
    { number: 207, menuCode: 'D2F' },
    { number: 238, menuCode: 'D2G' },
    { number: 239, menuCode: 'D2H' },
    { number: 4, menuCode: 'D31' },
    { number: 5, menuCode: 'D32' },
    { number: 14, menuCode: 'D41' },
    { number: 229, menuCode: 'D46' },
    { number: 201, menuCode: 'D44' },
    { number: 252, menuCode: 'H55' },
    { number: 16, menuCode: 'D42' },
    { number: 15, menuCode: 'D51' },
    { number: 17, menuCode: 'D61' },
    { number: 221, menuCode: 'D66' },
    { number: 208, menuCode: 'D65' },
    { number: 46, menuCode: 'D71' },
    { number: 134, menuCode: 'D81' },
    { number: 205, menuCode: 'D83' },
    { number: 254, menuCode: 'H57' },
    { number: 256, menuCode: 'D2I' },
    { number: 287, menuCode: 'D89' },
    { number: 285, menuCode: 'D85' },
    { number: 286, menuCode: 'D86' }
  ],
  // 数据安全策略包 E11
  encryptionGroup: [
    { number: 73, menuCode: 'E22' },
    { number: 87, menuCode: 'E22', title: i18n.t('pages.encryptionGroup_Msg'), param: { tabName: 'configTab' }, isHidden: () => { return !Vue.prototype.hasPermission('E2B') } },
    { number: 97, menuCode: 'E23' },
    { number: 96, menuCode: 'E24' },
    { number: 113, menuCode: 'E25' },
    { number: 146, menuCode: 'E27' },
    { number: 107, menuCode: 'E33' },
    { number: 68, menuCode: 'E34' },
    { number: 69, menuCode: 'E35' },
    { number: 74, menuCode: 'E36' },
    { number: 88, menuCode: 'E38' },
    // { number: 75, menuCode: 'E36' },
    { number: 61, menuCode: 'E37' },
    { number: 283, menuCode: 'E3G' },
    { number: 72, menuCode: 'E3A' },
    { number: 116, menuCode: 'E3C' },
    { number: 101, menuCode: 'E41', title: i18n.t('route.machineCodeWhiteList') },
    { number: 138, menuCode: 'E42', title: i18n.t('pages.encryptionGroup_Msg3') },
    { number: 275, menuCode: 'E59', title: i18n.t('pages.encryptionGroup_Msg4') },
    { number: 103, menuCode: 'E44', title: i18n.t('route.outgoingScreenWaterMark') },
    { number: 104, menuCode: 'E45', title: i18n.t('route.outgoingPrintWaterMark') },
    { number: 105, menuCode: 'E46', title: i18n.t('pages.encryptionGroup_Msg2') },
    { number: 137, menuCode: 'E47', title: i18n.t('route.templateSet') },
    { number: 114, menuCode: 'E3B' },
    { number: 150, menuCode: 'E3D' },
    { number: 60, menuCode: 'E61', title: i18n.t('pages.mailReceiver'), param: { tabName: 'MailReceiver', isPrepare: true }},
    { number: 62, menuCode: 'E62' },
    { number: 85, menuCode: 'E62', title: i18n.t('pages.encryptionGroup_Msg1'), param: { tabName: 'processTab' }},
    { number: 214, menuCode: 'E3F' },
    { number: 213, menuCode: 'E71' },
    { number: 212, menuCode: 'E74' },
    { number: 151, menuCode: 'E3E' },
    // 即时备份策略
    { number: 270, menuCode: 'E81' },
    { number: 278, menuCode: 'C96' },
    { number: 279, menuCode: 'C97' },
    { number: 280, menuCode: 'C98' }
  ],
  // 敏感内容识别策略包
  contentGroup: [
    { number: 22, menuCode: 'F21' },
    { number: 133, menuCode: 'F24' },
    { number: 145, menuCode: 'F23' },
    { number: 23, menuCode: 'F12' },
    { number: 24, menuCode: 'F13' },
    { number: 223, menuCode: 'F14' }
  ],
  // 终端管理策略包
  terminalGroup: [
    { number: 77, menuCode: 'B3A' },
    { number: 9, menuCode: 'C24', param: { limitType: 1 }, title: i18n.t('pages.limitType1') },
    { number: 10, menuCode: 'C24', param: { limitType: 2, tabName: 'uninstallPacketStrategy' }, title: i18n.t('pages.limitType2') },
    { number: 11, menuCode: 'C24', param: { tabName: 'SpecialPathStrategy' }, title: i18n.t('pages.specialPathStrategy') },
    { number: 152, menuCode: 'B4C' },
    { number: 153, menuCode: 'B4D' },
    { number: 154, menuCode: 'B4E' },
    { number: 210, menuCode: 'B2H' },
    { number: 246, menuCode: 'B64' }
  ],
  // 系统管理策略包
  systemGroup: [
    { number: 79, menuCode: 'A44' },
    { number: 106, menuCode: 'A65' },
    { number: 115, menuCode: 'A24' }
  ],
  // 包含所有策略包的子策略，用于：离线策略包
  getAll: function() {
    return [
      ...this.behaviorGroup,
      ...this.netGroup,
      ...this.encryptionGroup,
      ...this.contentGroup,
      ...this.terminalGroup,
      ...this.systemGroup
    ]
  }
}
