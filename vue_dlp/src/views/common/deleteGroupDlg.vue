<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :append-to-body="appendToBody"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogDeleteVisible"
      :width="$store.getters.language === 'en' ? '455px' : '400px'"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" @submit.native.prevent>
        <p style="margin-block-end: 0.5em; margin-block-start: 0.5em">{{ $t('pages.delete_group_dlg_text1').replace('${dlgTitle}', dlgTitle) }}</p>
        <FormItem>
          <el-radio-group v-model="temp.radio" @change="changeRadio">
            <el-radio v-if="deleteable" label="1" style="display: block" :disabled="deleteGroupAndData === null">{{ $t('pages.delete_group_dlg_text2').replace('${dlgTitle}', dlgTitle) }}</el-radio>
            <el-radio label="2"><span style="">{{ $t('pages.delete_group_dlg_text3').replace('${dlgTitle}', dlgTitle) }}</span></el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem prop="groupName">
          <tree-select
            ref="groupTree"
            class="tree_class"
            :data="groupTreeData"
            :disabled="!chooseMove"
            node-key="dataId"
            style="width: 250px; margin-left: 10px"
            :checked-keys="checkedKeys"
            @change="treeSelectChange"
          />
        </FormItem>
        <p v-show="promptMessage && promptMessage !== ''" style="margin-block-end: 0.5em; margin-block-start: 0.5em; color: red">{{ promptMessage }}</p>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="onsubmit">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogDeleteVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <validate-pass-dlg ref="validatePass" @validated="deleteGroupAllMail" @cancel="cancelInit"/>
  </div>

</template>

<script>
import ValidatePassDlg from '@/views/common/validatePassDlg'

export default {
  name: 'DeleteGroupDlg',
  components: { ValidatePassDlg },
  props: {
    title: {
      type: String,
      default() {
        return this.$t('pages.deleteGroup')
      }
    },
    selectTreeData: {
      type: Array,
      default() {
        return []
      }
    },
    deleteable: {
      type: Boolean,
      default() {
        return true
      }
    },
    deleteGroupAndData: {
      type: Function,
      default: null
    },
    moveGroupToOther: {
      type: Function,
      default: null
    },
    deleteFunc: { // 删除方法
      type: Function,
      default: null
    },
    dlgTitle: {
      type: String,
      default: '数据'
    },
    //   提示语言
    promptMessage: {
      type: String,
      default() {
        return '';
      }
    },
    appendToBody: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogDeleteVisible: false,
      temp: {
        radio: '1'
      },
      checkedKeys: [],
      chooseMove: false,
      chooseGroupData: null,
      queryIdKey: '',
      rules: {
        groupName: [
          { required: true, validator: this.groupNameValidator, trigger: 'change' }
        ]
      },
      submitting: false,
      groupTreeData: []
    }
  },
  watch: {
    selectTreeData: {
      deep: true,
      handler() {
        this.resetGroupTreeData()
      }
    }
  },
  methods: {
    handleCreate(groupData) {
      this.dialogDeleteVisible = true
      this.checkedKeys = []
      this.chooseMove = false
      if (this.deleteable) {
        this.temp.radio = '1'
      } else {
        this.temp.radio = '2'
        this.chooseMove = true
      }
      this.queryIdKey = ''
      this.submitting = false
      if (groupData) {
        this.chooseGroupData = groupData
        this.setGroupId()
      } else { this.chooseGroupData = null }
      this.resetGroupTreeData()
      this.$nextTick(() => {
        this.$refs.dataForm.validateField('groupName')
      })
    },
    cancelInit() {
      this.dialogDeleteVisible = true
      this.checkedKeys = []
      this.chooseMove = this.deleteGroupAndData === null
      this.temp.radio = this.chooseMove ? '2' : '1'
      this.queryIdKey = ''
      this.submitting = false
      this.setGroupId()
      this.resetGroupTreeData()
    },
    changeRadio(label) {
      if (label == '1') {
        this.chooseMove = false
        this.$nextTick(() => {
          this.$refs.dataForm.validateField('groupName')
        })
      } else if (label == '2') {
        this.chooseMove = true
      }
    },
    setGroupId() {
      const t = this.chooseGroupData
      if (t.groupId) { this.queryIdKey = 'groupId' } else if (t.classId) { this.queryIdKey = 'classId' }
    },
    onsubmit() {
      const radio = this.temp.radio
      if (radio == '1') {
        this.dialogDeleteVisible = false
        this.$refs['validatePass'].show()
      } else if (radio == '2') {
        this.moveGroupDataToOtherGroup()
      }
    },
    // 通过验证后的删除分组及分组下数据的方法
    deleteGroupAllMail() {
      const groupId = this.chooseGroupData[this.queryIdKey]
      if (groupId) {
        this.deleteGroupAndData({ [this.queryIdKey]: groupId }).then(res => {
          if (res.code == 20000) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          }
          this.$emit('removeFunc', groupId)
          this.$parent.refreshTableData && this.$parent.refreshTableData()
        })
      }
    },
    moveGroupDataToOtherGroup() {
      const arriveId = this.checkedKeys[0]
      const groupId = this.chooseGroupData[this.queryIdKey]
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (arriveId != groupId) {
            this.moveGroupToOther({ parentId: arriveId, [this.queryIdKey]: groupId }).then(res => {
              this.$emit('deleteEnd', groupId)
              this.$parent.arriveTreeNodeId && this.$parent.arriveTreeNodeId(arriveId)
              this.dialogDeleteVisible = false
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.updateSuccess'),
                type: 'success',
                duration: 2000
              })
            }).catch(res => {
              this.submitting = false
            })
          } else {
            this.$notify({
              title: this.$t('text.fail'),
              message: this.$t('pages.cannotSameSelectGroupAndOldGroup'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
          }
        } else {
          this.submitting = false
        }
      })
    },
    treeSelectChange(data) {
      if (data !== undefined && data !== null && data !== '') {
        this.checkedKeys.splice(0, this.checkedKeys.length, data)
      }
    },
    groupNameValidator(rule, value, callback) {
      if (this.temp.radio == 2 && this.checkedKeys.length == 0) {
        callback(new Error(this.$t('pages.validaGroup')))
      } else {
        callback()
      }
    },
    filter(data) {
      if (this.queryIdKey != '' && data.dataId === this.chooseGroupData[this.queryIdKey]) {
        return null
      }
      if (data.children != null) {
        data.children.forEach((c, index) => {
          data.children[index] = this.filter(c)
        })
        data.children = data.children.filter(c => c != null)
      }
      return data
    },
    resetGroupTreeData() {
      const treeData = JSON.parse(JSON.stringify(this.selectTreeData))

      let temp = null
      this.groupTreeData = []
      treeData.forEach(data => {
        temp = this.filter(data)
        if (temp != null) {
          this.groupTreeData.push(temp)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .tree_class input{
    width: 90%;
    float: right;
  }
</style>
