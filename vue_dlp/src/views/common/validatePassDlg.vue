<template>
  <el-dialog
    v-el-drag-dialog
    append-to-body
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('text.verifyIdentity')"
    :visible.sync="visible"
    width="400px"
  >
    <p style="margin-block: .75em">{{ tip }}</p>
    <Form ref="adminForm" label-width="86px" @submit.native.prevent>
      <FormItem :label="$t('pages.sysUserPassword')" required :error="error">
        <el-input ref="pwd" v-model="password" type="password" show-password clearable @blur="validateRule" @keyup.enter.native="submitPass"/>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitPass">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { validatePassword } from '@/api/user'

export default {
  name: 'ValidatePassDlg',
  props: {
    tip: {
      type: String, 
      default: function() {
        return this.$t('pages.delete_group_dlg_text4')
      } 
    }
  },
  data() {
    return {
      visible: false,
      password: undefined,
      error: undefined,
      validated: false
    }
  },
  watch: {
    visible(value) {
      if (!value) {
        this.$emit(this.validated ? 'validated' : 'cancel')
      }
    }
  },
  methods: {
    show() {
      this.validated = false
      this.visible = true
      this.password = undefined
      this.error = undefined
      this.$nextTick(() => {
        this.$refs.pwd.focus()
      })
    },
    validateRule() {
      if (!this.password) {
        this.error = this.$t('pages.validateMsg_password')
      } else {
        this.error = undefined
      }
    },
    submitPass() {
      if (!this.password) {
        this.error = this.$t('pages.validateMsg_password')
        return
      }
      validatePassword({ password: this.password, encryptProps: ['password'] }).then(res => {
        if (!res.data) {
          this.error = this.$t('pages.wrongPassword')
          return
        }
        this.validated = true
        this.visible = false
      })
    }
  }
}
</script>
