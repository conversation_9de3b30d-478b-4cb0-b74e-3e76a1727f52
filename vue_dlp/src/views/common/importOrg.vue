<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      :modal-append-to-body="true"
      :append-to-body="false"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.importInfo', { info: title })"
      :visible.sync="dialogVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px" :extra-width="{en: 80}" style="width: 500px;">
        <FormItem :label="$t('pages.ldImportOrgId')" prop="objectType">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom">
            <div slot="content">
              <i18n path="pages.ldImportRetainIdTips">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-radio-group v-model="temp.createId" style="padding-left: 10px;" @change="clickCreateId">
            <el-radio :label="1" style="width: 95px;">{{ $t('pages.ldImportNotRetainId') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.ldImportRetainId') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.importOrgModuleConfigMode')" prop="objectType">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom">
            <div slot="content">
              <i18n path="pages.importOrgModuleConfigModeContent">
                <a slot="menu" style="color:#148FF1FF;" @click="()=>{this.$router.push('/system/systemRegistry/moduleConfig')}">{{ $t('route.moduleConfig') }}</a>
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-radio-group v-model="temp.moduleType" style="padding-left: 10px;">
            <el-radio :label="1">{{ $t('pages.autoAllocation') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.notAllocation') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-show="temp.createId" :label="$t('pages.repeatNameDealType')" prop="objectType">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom">
            <div slot="content">
              <i18n path="pages.repeatNameDealTypeContent">
                <template slot="tip">{{ $t('pages.deptName')+'、'+$t('table.userAccount')+'、'+$t('table.terminalGuid') }}</template>
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-radio-group v-model="temp.importWay" style="padding-left: 10px;">
            <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <span v-show="errorMsg" style="color:red; position: absolute; right: 10px; margin-top: 10px;">{{ errorMsg }}</span>
        <el-upload
          ref="uploadFileList"
          action="1111"
          class="upload-demo"
          style="display:inline-block;max-width: 670px;"
          :limit="1"
          multiple
          list-type="text"
          :file-list="fileList"
          :accept="accept"
          :auto-upload="false"
          :on-change="onFileChange"
          :on-remove="onFileChange"
          :before-upload="beforeUpload"
          :http-request="onUpload"
          :disabled="showUploadInfo"
        >
          <el-button v-show="!showUploadInfo" size="small" type="primary">{{ uploadBtnName }}</el-button>
          <i v-show="!showUploadInfo && fileList.length > 0" class="el-icon-delete-solid" @click="clearFiles"></i>
          <div slot="tip" class="el-upload__tip">
            {{ stgNameRuleRemark }}
          </div>
        </el-upload>
      </Form>
      <div class="retain-form" >
        <i18n path="pages.ldImportOrgRetainId">
          <br slot="br"/>
          <span slot="notRetain" style="margin-left: 20px;font-weight: bold">{{ $t('pages.ldImportNotRetainId') }}：</span>
          <span slot="retain" style="margin-left: 20px;font-weight: bold">{{ $t('pages.ldImportRetainId') }}：</span>
        </i18n>
      </div>
      <el-card v-if="showUploadInfo" :body-style="{'padding': '0'}" class="box-card">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.processStgLib_Msg86') }}</span>
        </div>
        <ul style="margin: 0px;">
          <li>{{ $t('pages.processStgLib_Msg87') }}</li>
          <li v-if="uploadStatus.hasOwnProperty('percent')"> {{ $t('pages.importProgress',{ percent: uploadStatus.percent}) }}</li>
          <li v-if="uploadStatus.addSize"> {{ $t('pages.importSuccess',{ size: uploadStatus.addSize} ) }}</li>
          <li v-if="uploadStatus.updateSize"> {{ $t('pages.importUpdateVolume',{ size: uploadStatus.updateSize} ) }}</li>
          <li v-if="uploadStatus.failSize"> {{ $t('pages.importFailVolume',{ size: uploadStatus.failSize} ) }}</li>
          <li v-if="uploadStatus.failMsg" style="color:#ff0000;"> {{ $t('pages.importFailMsg',{ msg: uploadStatus.failMsg} ) }}</li>
          <li v-show="uploadStatus.percent >= 100 && !uploadStatus.failMsg">{{ $t('pages.processStgLib_Msg88') }}</li>
        </ul>
      </el-card>

      <div slot="footer" class="dialog-footer">
        <el-button v-if="!showUploadInfo" type="primary" :loading="submitting" @click="createData">{{ $t('button.confirm') }}</el-button>
        <el-button v-if="!showUploadInfo" @click="hide">{{ $t('button.cancel') }}</el-button>
        <el-button v-if="showUploadInfo" @click="hide">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.ldImportOrgWillClean')"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="retainIdForm"
        label-position="right"
        label-width="120px"
        style="width: 550px;"
      >
        <span class="retain-form">
          <i18n path="pages.ldImportOrgRetainIdTips">
            <br slot="br"/>
            <span slot="disable" class="red-font">{{ $t('table.disable') }}</span>
            <span slot="delete" class="red-font">{{ $t('table.delete') }}</span>
            <span slot="relieve" class="red-font">{{ $t('pages.relieve') }}</span>
            <span slot="default" class="red-font">default</span>
            <a slot="stg" v-permission="'B11'" class="blue-font" @click="()=>{this.$router.push('/terminalManage/overView/strategyOverViews')}">{{ $t('table.stg') }}：</a>
            <span slot="stg" v-permission="'!B11'" class="bold-font">{{ $t('table.stg') }}：</span>
            <a slot="group" v-permission="'A31'" class="blue-font" @click="()=>{this.$router.push('/system/structure/department')}">{{ $t('table.group') }}：</a>
            <span slot="group" v-permission="'!A31'" class="bold-font">{{ $t('table.group') }}：</span>
            <a slot="user" v-permission="'B23'" class="blue-font" @click="()=>{this.$router.push('/terminalManage/terminalManage/user')}">{{ $t('table.user') }}：</a>
            <span slot="user" v-permission="'!B23'" class="bold-font">{{ $t('table.user') }}：</span>
            <a slot="terminal" v-permission="'B24'" class="blue-font" @click="()=>{this.$router.push('/terminalManage/terminalManage/terminal')}">{{ $t('table.terminal') }}：</a>
            <span slot="terminal" v-permission="'!B24'" class="bold-font">{{ $t('table.terminal') }}：</span>
            <span slot="behavior" class="bold-font">{{ $t('route.behaviorAuditing') }}：</span>
            <span slot="sysUserLog" class="bold-font">{{ $t('route.opLog') }}：</span>
          </i18n>
        </span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" :disabled="showTipsTimer" @click="validatePass()">
          {{ $t('button.confirm') }}
          <span v-show="showTipsTimer">({{ showTipsNumber }})</span>
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <validate-pass-dlg ref="validatePass" @validated="validatedAfter" @cancel="cancelInit"/>
  </div>
</template>
<script>
import request from '@/utils/request'
import ValidatePassDlg from '@/views/common/validatePassDlg'

export default {
  name: 'ImportOrg',
  components: { ValidatePassDlg },
  props: {
    title: {
      type: String,
      default: function() {
        return this.$t('button.import')
      }
    },
    accept: { type: String, default: '.base' },
    fileName: { type: String, default: '部门.xls' },
    uploadFunc: { // 上传函数
      type: Function,
      default: null
    }
  },
  data() {
    return {
      taskId: '',
      fileList: [],
      dialogVisible: false,
      dialogFormVisible: false,
      submitting: false,
      submitReturnSize: 0,
      uploadBtnName: '',
      errorMsg: '',
      temp: {
        moduleType: 1,
        importWay: 1,
        createId: 1
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ]
      },
      selectedUserData: [],
      stgNameRule: 1,
      validateFail: false,
      stgNameRuleRemark: '',
      showUploadInfo: false,
      isUploadEndStatus: false,
      uploadStatus: {},
      checkedObject: [],
      showTipsTimer: false,
      showTipsNumber: 5
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
  },
  methods: {
    show() {
      this.resetTemp()
      this.dialogVisible = true
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.showUploadInfo = false
      this.submitting = false
      this.errorMsg = ''
      this.fileList.splice(0)
      this.submitReturnSize = 0
      this.checkedObject.splice(0)
      this.temp.moduleType = 1
      this.temp.importWay = 1
      this.temp.createId = 1
      this.uploadBtnName = this.$t('pages.processStgLib_Msg94', { format: this.accept })
    },
    beforeUpload(file) {
      // 验证
      this.validateFail = false
      const fileName = file.name
      if (this.accept && fileName.substring(fileName.lastIndexOf('.')) !== this.accept) {
        this.$notify({
          title: this.$t('text.prompt'),
          message: this.$t('pages.importSupportFile', { format: this.accept }),
          type: 'warning',
          duration: 2000
        })
        return false
      }
    },
    onUpload(data) {
      if (this.validateFail) {
        this.errorMsg = this.$t('components.chooseApplicationObj')
        return false
      }
      this.showUploadInfo = true
      this.submitting = true
      const fileName = data.file.name
      const fd = new FormData()
      // fd.append('uploadFile', data.file)// 传文件
      // fd.append('moduleType', this.temp.moduleType)
      // fd.append('objectType', this.temp.objectType)
      // fd.append('objectIds', this.temp.objectIds)
      // fd.append('objectGroupIds', this.temp.objectGroupIds)

      fd.append('file', data.file)// 传文件
      fd.append('autoModule', this.temp.moduleType > 0)
      fd.append('importWay', this.temp.importWay)
      fd.append('createId', this.temp.createId)
      fd.append('taskId', this.taskId)
      const execFunc = !this.uploadFunc ? this.defaultUploadDataFunc : this.uploadFunc
      execFunc(fd).then(res => {
        // this.$notify({ title: this.$t('text.success'), message: fileName + '文件导入成功', type: 'success', duration: 2000 })
        this.uploadEnd()
      }).catch(res => {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.processStgLib_Msg80', { name: fileName }), type: 'error', duration: 2000 })
        this.uploadEnd()
      })
      return false // 屏蔽了action的默认上传
    },
    uploadEnd() {
      this.submitReturnSize++
      if (this.submitReturnSize === this.fileList.length) {
        this.submitting = false
        // this.hide()
        this.$emit('success', this)
      }
    },
    onFileChange(file, list) {
      this.fileList = list
      this.errorMsg = ''
    },
    stopEvent(e) {
      window.event ? window.event.cancelBubble = true : e.stopPropagation()
    },
    clearFiles(e) {
      window.event ? window.event.cancelBubble = true : e.stopPropagation()
      this.fileList = []
      this.submitReturnSize = 0
      this.$refs.uploadFileList.clearFiles()
    },
    onExceedFunc(files, prevFiles) {
      this.$notify({ title: this.$t('pages.processStgLib_Msg96'), message: this.$t('pages.processStgLib_Msg97'), type: 'warning', duration: 2000 })
    },
    handleDrag() {
    },
    createData() {
      if (this.fileList.length === 0) {
        this.errorMsg = this.$t('pages.processStgLib_Msg98')
        return
      }
      if (this.temp.createId === 0) {
        this.dialogFormVisible = true;
        this.showTipsTimer = true
        this.showTipsNumber = 5
        const timer = setInterval(() => {
          this.showTipsNumber--
          if (this.showTipsNumber <= 0) {
            clearInterval(timer)
            this.showTipsTimer = false
          }
        }, 1000);
        return
      }
      this.submitData();
    },
    submitData() {
      this.uploadStatus = {}
      this.isUploadEndStatus = false
      this.taskId = new Date().getTime()
      this.$socket.subscribeToUser(this.taskId, '/import/data', (resp, handle) => {
        this.uploadStatus = resp.data
        if (this.uploadStatus.percent >= 100) {
          handle.close()
        }
        // this.isUploadEndStatus = this.fileList.length === Object.keys(this.uploadStatus).length
        // if (this.isUploadEndStatus) {
        //   this.$emit('success', this)
        // }
      }, false)
      this.$refs.uploadFileList.submit()
    },
    defaultUploadDataFunc(data) {
      return request.post('/mailLibrary/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    downloadTemplateFileFunc(data) {
      return request({
        url: '/importData/downloadTemplate',
        method: 'post',
        responseType: 'blob',
        data
      })
    },
    downloadErrorFileFunc(errorFileName) {
      const data = {
        template: errorFileName,
        fileName: this.$t('pages.importFailData') + '.xls'
      }
      return request({
        url: '/importData/downloadCache',
        method: 'post',
        responseType: 'blob',
        data
      })
    },
    clickCreateId(value) {
      // 如果选择保留id，则"重名处理方式"应固定为"1 导入并覆盖"
      if (value === 0) {
        this.temp.importWay = 1;
      }
    },
    validatePass() {
      this.$refs['validatePass'].show()
    },
    validatedAfter() {
      this.dialogFormVisible = false;
      this.submitData();
    },
    cancelInit() {

    }
  }
}
</script>

<style lang="scss" scoped>
  .upload-demo>>>.el-upload-list{
    min-height: 20px;
    max-height: 188px;
    overflow: hidden;
    margin-top: 2px;
    .el-upload-list__item:hover {
      background-color: #dbdbdb;
    }
  }
  .el-icon-delete-solid{
    margin-left: 20px;
    font-size: 16px;
    &:hover{
      color: rgb(12, 123, 226);
    }
  }
  .red-font {
    color: red;
    font-weight: bold
  }
  .blue-font {
    font-weight: bold;
    color:#148FF1FF;
  }
  .bold-font {
    font-weight: bold
  }
  .retain-form {
    color: #666;
    font-size: 14px;
    line-height: normal
  }
</style>
