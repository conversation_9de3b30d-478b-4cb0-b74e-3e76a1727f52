<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.exportInfo', { info: title })"
      :visible.sync="dialogVisible"
      width="550px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="40px" :extra-width="{}" style="width:500px;">
        <FormItem v-if="temp.dataIds && temp.dataIds.length > 0">
          <el-radio v-model="temp.type" :label="1">{{ $t('pages.processStgLib_Msg99') }}{{ title }}</el-radio>
        </FormItem>
        <FormItem>
          <el-radio v-model="temp.type" :label="3">{{ $t('pages.processStgLib_Msg107') }}</el-radio>
        </FormItem>
        <FormItem v-if="groupTreeData.length > 0">
          <el-radio v-model="temp.type" :label="2">{{ $t('pages.processStgLib_Msg100') }}{{ title }}</el-radio>
        </FormItem>
        <FormItem v-if="groupTreeData.length > 0" label-width="65px" prop="groupId">
          <tree-select
            ref="groupTree"
            :data="groupTreeData"
            :node-key="nodeKey"
            is-filter
            :disabled="temp.type != 2"
            :checked-keys="[temp.groupId]"
            :width="300"
            style="width: 300px;"
            @change="treeSelectChange"
          />
        </FormItem>
        <FormItem v-if="stgExport && objectType">
          <el-radio v-model="temp.type" :label="4">
            {{ $t('pages.exportStgMsg2') }}{{ title }}
            <el-tooltip effect="dark" placement="bottom-start">
              <div slot="content">
                <span>{{ $t('pages.exportStgMsg3', { objectType: objectType === 1 ? $t('table.terminal') : $t('table.user') }) }}</span><br>
                <span>{{ $t('pages.exportStgMsg4', { title }) }}</span>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
        </FormItem>
        <FormItem v-if="stgExport && objectType" prop="stgNodes">
          <tree-select
            ref="stgTree"
            :data="stgTreeData"
            :disabled="temp.type != 4"
            check-strictly
            multiple
            :width="300"
            style="width: 300px;"
            @change="entityIdChange"
          />
        </FormItem>
      </Form>

      <div slot="footer" class="dialog-footer">
        <common-downloader
          :loading="submitting"
          :name="title + fileExtension"
          :button-name="$t('button.confirm')"
          button-type="primary"
          button-icon=""
          :before-download="beforeDownload"
          @download="exportExcel"
        />
        <el-button @click="hide">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'ExportDlg',
  components: { CommonDownloader },
  props: {
    title: {
      type: String,
      default: function() {
        return this.$t('route.' + this.$route.meta.title)
      }
    },
    fileExtension: {
      type: String,
      default: function() {
        return '.xlsx'
      }
    },
    groupTreeData: { type: Array, default() { return [] } },
    exportFunc: { // 上传函数
      type: Function,
      default: null
    },
    groupTreeId: {
      type: [Number, String],
      default: '0'
    },
    nodeKey: {
      type: String,
      default: 'dataId'
    },
    stgExport: {  //  策略导出
      type: Boolean,
      default: false
    },
    objectType: {   //  策略导出的生效对象类型 1-终端/终端组  2-操作员/操作员组
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      errorMsg: '',
      temp: {
        type: 2,
        groupId: undefined,
        groupName: undefined,
        dataIds: [],
        isAll: false, //  是否导出所有
        objectIds: [],
        objectGroupIds: [],
        stgNodes: []  //  仅用于保存节点数据和校验数据
      },
      rules: {
        groupId: [{ trigger: 'blur', validator: this.exportGroupIdValidator }],
        stgNodes: [{ trigger: 'change', validator: this.stgNodeIdsValidator }]
      },
      stgTreeData: []
    }
  },
  computed: {
  },
  watch: {
    'temp.type'(value) {
      if (value != 4) {
        this.temp.stgNodes.splice(0);
        this.$refs.stgTree && this.$refs.stgTree.clearSelectedKeys();
      }
      this.$refs.dataForm && this.$refs.dataForm.validate();
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    show(dataIds) {
      this.resetTemp()
      this.dialogVisible = true
      Object.assign(this.temp, {
        type: 3,
        groupId: this.groupTreeId ? this.groupTreeId : '0',
        dataIds: dataIds,
        isAll: false,
        objectIds: [],
        objectGroupIds: [],
        stgNodes: []
      })
      if (this.temp.dataIds && this.temp.dataIds.length > 0) {
        this.temp.type = 1
      }
      if (this.stgExport && this.objectType) {
        this.stgTreeData = this.objectType === 1 ? this.$store.getters.termTree : this.objectType === 2 ? this.$store.getters.userTree : []
      }
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
      })
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.submitting = false
      this.errorMsg = ''
    },
    handleDrag() {
    },
    beforeDownload() {
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    exportExcel(file) {
      this.submitting = true
      //  策略导出
      if (this.stgExport && this.objectType) {
        this.temp.objectType = this.objectType;
        if (this.temp.stgNodes.length) {
          if (this.temp.stgNodes.findIndex(t => t.id === 'G-1') !== -1) {
            this.temp.isAll = true;
          } else {
            this.temp.stgNodes.forEach(node => {
              if (node.id !== 'G-1') {
                if (node.type == 1 || node.type == 2) {
                  this.temp.objectIds.push(node.dataId)
                } else {
                  this.temp.objectGroupIds.push(node.dataId);
                }
              }
            })
          }
        }
      }
      const opts = { file, jwt: true, topic: this.$route.name }
      this.exportFunc(this.temp, opts).then(() => {
        this.submitting = false
      })
      this.dialogVisible = false
    },
    exportGroupIdValidator(rule, value, callback) {
      setTimeout(() => {
        const nodeData = this.$refs['groupTree'].getSelectedNode()
        if (this.temp.type === 2 && nodeData.length === 0) {
          callback(new Error(this.$t('pages.processStgLib_Msg101')))
        } else {
          // v3.51.20231218做的修改，如果有传nodeKey,则groupId不在此赋值，审批流程的导出用到了这个
          if (nodeData.length > 0 && this.nodeKey != 'id') {
            this.temp.groupId = nodeData[0].dataId
            this.temp.groupName = nodeData[0].label
          }
          callback()
        }
      }, 150)
    },
    /**
     * 校验策略的生效对象
     * @param rule
     * @param value
     * @param callback
     */
    stgNodeIdsValidator(rule, value, callback) {
      if (this.temp.type == 4 && !value.length) {
        callback(this.$t('pages.exportStgErrorMsg1'));
      }
      callback();
    },
    treeSelectChange(key, nodeData) {
      const data = nodeData
      this.$emit('childSelectNodeData', data)
    },
    entityIdChange(keys, nodeDatas) {
      this.temp.stgNodes = nodeDatas || []
    }
  }
}
</script>
