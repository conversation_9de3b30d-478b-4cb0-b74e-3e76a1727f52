<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.importInfo', { info: title })"
      :visible.sync="dialogVisible"
      :width="dialogWidth + 'px'"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px" :extra-width="{en: 100}" :style="{ width: (dialogWidth-80) + 'px' }">
        <FormItem v-if="termAble || userAble" :label="$t('pages.effectiveObject')" prop="objectType">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.effectiveObject') }}<br/>
              <label v-if="termAble">{{ $t('pages.processStgLib_Msg84') }}</label><br/>
              <label v-if="userAble">{{ $t('pages.processStgLib_Msg85') }}</label><br/>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-row>
            <el-col :span="8">
              <el-select v-model="temp.objectType" :disabled="showUploadInfo" @change="objectTypeChange">
                <el-option v-if="termAble" :key="1" :value="1" :label="$t('pages.terminal')"/>
                <el-option v-if="userAble" :key="2" :value="2" :label="$t('pages.user')"/>
              </el-select>
            </el-col>
            <el-col :span="16">
              <tree-select
                ref="objectTree"
                node-key="id"
                is-filter
                :check-strictly="objectCheckStrictly"
                :multiple="objectMultiple"
                :local-search="false"
                :filter-key="filterKey"
                :leaf-key="temp.objectType === 1 ? 'terminal' : 'user'"
                :disabled="showUploadInfo"
                @change="objectTreeSelectChange"
              />
            </el-col>
          </el-row>
        </FormItem>
        <FormItem v-if="showImportType" :label="$t('pages.repeatNameDealType')">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.isGlobalUnique') }}<br/>
              {{ $t('pages.importAndUpdate_Msg') }}<br/>
              {{ $t('pages.importAndIgnore_Msg') }}<br/>
              {{ $t('pages.importAndRename_Msg') }}<br/>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-radio-group v-model="temp.importType" :disabled="showUploadInfo">
            <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.importAndRename') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="showImportWay" :label="$t('pages.repeatNameDealType')">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.repeatNameDealTypeContent">
                <template slot="tip">{{ tip }}</template>
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-radio-group v-model="temp.importWay">
            <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="showImportStyle" :label="$t('pages.lowVersionDealType')" style="margin-left: -16px">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.lowVersionDealTypeContent">
                <!-- <template slot="tip">{{ tip }}</template> -->
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-radio-group v-model="temp.importStyle">
            <el-radio :label="1">{{ $t('pages.importAndUpdate') }}</el-radio>
            <el-radio :label="0">{{ $t('pages.importAndIgnore') }}</el-radio>
          </el-radio-group>
        </FormItem>

        <!-- 针对导入时存在 操作员显示方式不同的情况 -->
        <div v-if="userModes.length">
          <div v-for="(item, index) in userModes" :key="index">
            <FormItem :label="item.label">
              <div slot="label">
                {{ item.label }}
                <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                  <div slot="content">
                    <span>
                      {{ $t('pages.userImportShowMode', { user: item.label }) }}<br>
                      {{ $t('pages.userImportShowModeTip') }}
                    </span>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </div>
              <el-radio-group v-model="item.mode">
                <el-radio :label="1">{{ $t('pages.accountDisplay') }}</el-radio>
                <el-radio :label="2">{{ $t('pages.nickNameDisplay') }}</el-radio>
                <el-radio :label="3">{{ $t('pages.accountNickNameDisplay') }}</el-radio>
                <el-radio :label="4">{{ $t('pages.nickNameAccountDisplay') }}</el-radio>
              </el-radio-group>
            </FormItem>
          </div>
        </div>

        <el-upload
          ref="upload"
          action="1111"
          :limit="1"
          list-type="text"
          :file-list="fileList"
          :accept="accept"
          :auto-upload="false"
          :on-exceed="onFileExceed"
          :on-change="onFileChange"
          :on-remove="onFileRemove"
          :before-upload="beforeUpload"
          :http-request="onUpload"
          :disabled="showUploadInfo"
          class="upload-demo"
          style="display:inline-block;max-width: 670px;"
        >
          <el-button v-show="!showUploadInfo" size="small" type="primary">{{ uploadBtnName }}</el-button>
          <div slot="tip" class="el-upload__tip">
            <span v-show="errorMsg">{{ errorMsg }}</span>
          </div>
        </el-upload>
        <div style="float: right">
          <common-downloader
            v-show="!showUploadInfo && template"
            class="tpl-download"
            :name="fileName"
            :button-name="$t('pages.importTemplateDownload')"
            button-type="text"
            button-icon=""
            @download="downloadTemplate"
          />
          <el-radio-group v-show="!showUploadInfo && template" v-model="templateType" style="display: inline-block">
            <el-radio :label="1">{{ '.xls' }}</el-radio>
            <el-radio :label="2">{{ '.xlsx' }}</el-radio>
            <el-radio :label="3">{{ '.et' }}</el-radio>
          </el-radio-group>
        </div>
      </Form>
      <el-card v-if="showUploadInfo" :body-style="{'padding': '0'}" class="box-card">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.processStgLib_Msg86') }}</span>
        </div>
        <ul style="margin: 0;">
          <li>{{ $t('pages.processStgLib_Msg87') }}</li>
          <li v-if="uploadStatus.hasOwnProperty('percent')"> {{ $t('pages.importProgress',{ percent: uploadStatus.percent}) }}</li>
          <li v-if="confirmFlag && uploadStatus.hasOwnProperty('percent')">
            <i18n path="pages.importStgTypeDifferent">
              <span slot="stgType">{{ (termAble || userAble) ? $t('pages.prepareStrategy') : $t('pages.applicationStrategy') }}</span>
              <el-button slot="yes" type="text" size="mini" style="padding: 0; height: 16px; font-size: 13px" @click="confirmClick(true)">{{ $t('text.yes') }}</el-button>
              <el-button slot="no" type="text" size="mini" style="padding: 0; height: 16px; font-size: 13px; margin-left: 10px" @click="confirmClick(false)">{{ cancelButtonName }}</el-button>
            </i18n>
          </li>
          <li v-if="uploadStatus.addSize"> {{ $t('pages.importSuccess',{ size: uploadStatus.addSize} ) }}</li>
          <li v-if="uploadStatus.updateSize"> {{ $t('pages.importUpdateVolume',{ size: uploadStatus.updateSize} ) }}</li>
          <li v-if="uploadStatus.failSize"> {{ $t('pages.importFailVolume',{ size: uploadStatus.failSize} ) }}</li>
          <li v-if="uploadStatus.failSize">
            <common-downloader
              class="err-download"
              :name="$t('pages.importFailData') + temp.fileType"
              :button-name="$t('pages.importFailData') + temp.fileType"
              button-type="text"
              button-icon=""
              @download="downloadErrorFile"
            />
          </li>
          <i18n v-if="uploadStatus.failMsg" path="pages.importFailMsg" tag="li" style="color:#ff0000;">
            <template slot="msg">
              <span v-for="(item, index) in uploadStatus.failMsg.split('\t')" :key="index" style="word-break: break-all;">{{ item }}<br></span>
            </template>
          </i18n>
          <li v-show="uploadStatus.percent >= 100 && !uploadStatus.failMsg">{{ $t('pages.processStgLib_Msg88') }}</li>
        </ul>
      </el-card>

      <div slot="footer" class="dialog-footer">
        <el-button v-if="!showUploadInfo" type="primary" :loading="submitting" @click="handleUpload">{{ $t('button.confirm') }}</el-button>
        <el-button v-if="!showUploadInfo" @click="hide">{{ $t('button.cancel') }}</el-button>
        <el-button v-if="showUploadInfo" @click="hide">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from '@/utils/request'
import CommonDownloader from '@/components/DownloadManager/common'
import { fetchFile } from '@/utils/download/helper'

export default {
  name: 'ImportDlg',
  components: { CommonDownloader },
  props: {
    dataType: { type: String, default: '1' }, // 导入数据类型：1-透明加密策略库；2-半透明加密策略；11-敏感内容识别  -3：自定义策略导入
    title: {
      type: String,
      default: function() {
        return this.$t('button.import')
      }
    },
    showImportType: { type: Boolean, default: true }, // 是否让用户选择重名处理方式
    showImportWay: { type: Boolean, default: false },
    showImportStyle: { type: Boolean, default: false }, // 规则库模块 --> 导入低版本全量包是否更新
    accept: { type: String, default: '.xls,.xlsx,.et' },
    template: { type: String, default: null },
    fileName: { type: String, default: '部门.xls' },
    tip: { type: String, default: '部门名称' },
    uploadFunc: { // 上传函数
      type: Function,
      default: null
    },
    termAble: { type: Boolean, default: false },  //  终端生效对象
    userAble: { type: Boolean, default: false },  //  操作员生效对象
    strategyTypeNumber: { type: Number, default: null }, //  当dataType为-3时（即：导入数据类型为策略通用导入类型），需要指定导入的策略类型编号，否则可以导入任何类型编号
    objectMultiple: { type: Boolean, default: false }, //  生效对象是否支持多选
    objectCheckStrictly: { type: Boolean, default: false },  // 生效对象是否支持多选情况下，是否严格遵循父子不互相关联
    userShowModes: {    //  导入时，存在部分数据因根据【操作员显示方式】导出的，固可能存在和当前的【操作员显示方式】不同，导致导入时出现格式不正确的情况，可通过指定数据的操作员显示方式进行导入
      //  field：导入时后端接收的字段，mode：操作员显示方式 1-账号，2-名称，3-账号（名称），4-名称（账号）
      //  例子：[{ label: '部门主管', field: 'managerMode', mode: 1 }, { label: '分管领导', field: 'branchLeaderMode', mode: 1 }]
      type: Array,
      default() {
        return []
      }
    },
    propTemplateType: { type: Number, default: 1 }
  },
  data() {
    return {
      taskId: '',
      fileList: [],
      dialogVisible: false,
      dialogWidth: 600,
      submitting: false,
      uploadBtnName: '',
      errorMsg: '',
      temp: {
        importType: 1,
        importWay: 1,
        importStyle: 1,
        fileType: '.xls',
        objectType: 1,
        objectIds: [],
        objectGroupIds: [],
        strategyTypeNumber: null
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ],
        objectType: [
          { validator: this.objectValidator, trigger: 'change' }
        ]
      },
      selectedUserData: [],
      stgNameRule: 1,
      showUploadInfo: false,
      isUploadEndStatus: false,
      uploadStatus: {},
      uploadFailed: false,
      checkedObject: [],
      filterKey: 'G-2',
      confirmFlag: false,    //  当文件中存在预定义策略中已存在的策略名时，显示确认消息
      cancelButtonName: this.$t('text.no'),  //  取消按钮文本
      cancelInterval: null,  //  取消按钮计时器

      userModes: [],   //  导入时，存在部分数据因根据【操作员显示方式】导出的，固可能存在和当前的【操作员显示方式】不同，导致导入时出现格式不正确的情况
      templateType: this.propTemplateType
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
  },
  deactivated() {
    if (this.cancelInterval) {
      clearInterval(this.cancelInterval)
    }
  },
  methods: {
    show() {
      this.resetTemp()
      this.userModes = []
      if (this.userShowModes.length) {
        this.userModes = [...JSON.parse(JSON.stringify(this.userShowModes))];
      }
      this.dialogVisible = true
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.uploadFailed = false
      this.showUploadInfo = false
      this.submitting = false
      this.errorMsg = ''
      this.fileList.splice(0)
      this.checkedObject.splice(0)
      this.temp.importType = 1
      this.temp.importWay = 1
      this.temp.importStyle = 1
      this.uploadBtnName = this.$t('pages.processStgLib_Msg94', { format: this.accept })
      this.temp.objectType = this.termAble ? 1 : 2
      this.temp.objectIds.splice(0)
      this.temp.objectGroupIds.splice(0)
    },
    handleUpload() {
      // 验证不通过则返回
      if (!this.validate()) return

      this.uploadStatus = {}
      this.isUploadEndStatus = false
      this.taskId = new Date().getTime()
      //  todo 上传不符合要求的文件，不会报错
      this.$socket.subscribeToUser(this.taskId, '/import/data', (resp, handle) => {
        if (this.uploadFailed) {
          handle.close()
          return
        }
        const uploadedPercent = this.uploadStatus.percent || 0
        if (uploadedPercent <= resp.data.percent) {
          this.uploadStatus = resp.data
        }
        if (this.uploadStatus.percent >= 100) {
          handle.close()
        }
        // this.isUploadEndStatus = this.fileList.length === Object.keys(this.uploadStatus).length
        // if (this.isUploadEndStatus) {
        //   this.$emit('success', this)
        // }
      }, false)
      this.$refs.upload.submit()
    },
    // 提交数据前，验证表单及文件是否正常
    validate() {
      // 验证form表单
      let validateForm = true
      this.$refs.dataForm.validate(valid => {
        validateForm = valid
      })

      let validateFile = true
      if (this.fileList.length === 0) {
        this.errorMsg = this.$t('pages.processStgLib_Msg98')
        validateFile = false
      } else {
        // 验证文件
        const file = this.fileList[0]
        const fileName = file.name
        const fileType = fileName.substring(fileName.lastIndexOf('.'))

        if (this.accept && this.accept.indexOf(fileType) < 0) {
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.importSupportFile', { format: this.accept }),
            type: 'warning',
            duration: 2000
          })
          validateFile = false
        } else {
          this.temp.fileType = fileType
        }
      }
      return validateForm && validateFile
    },
    // 上传文件之前的钩子，参数为上传的文件，若返回 false 或者返回 Promise 且被 reject，则停止上传。
    beforeUpload(file) {
    },
    onUpload(data) {
      // 选择文件后，去修改文件，然后上传会报 network error 错误。
      // 浏览器比较注意文件安全，你修改并保存那就是新的文件（文件修改内容后 file 的本地文件已经丢失），
      // 浏览器是不具有新文件的访问权限的，除非你再走一遍选择文件这个流程
      // 上传文件前，验证是否被修改
      data.file.slice(0, 1) // only the first byte
        .arrayBuffer() // try to read
        .then(() => {
          // 文件没改变，在这里可以发请求了
          this.showUploadInfo = true
          this.submitting = true
          const fileName = data.file.name
          const fd = new FormData()
          // fd.append('uploadFile', data.file)// 传文件
          // fd.append('importType', this.temp.importType)
          // fd.append('objectType', this.temp.objectType)
          // fd.append('objectIds', this.temp.objectIds)
          // fd.append('objectGroupIds', this.temp.objectGroupIds)

          fd.append('file', data.file)// 传文件
          fd.append('fileType', this.temp.fileType)
          fd.append('importType', this.temp.importType)
          fd.append('importWay', this.temp.importWay)
          fd.append('importStyle', this.temp.importStyle)
          fd.append('taskId', this.taskId)
          if (this.userModes.length) {
            this.userModes.forEach(data => {
              fd.append(data.field, data.mode);
            })
          }
          let defaultUploadDataFunc = this.defaultUploadDataFunc
          if (this.strategyTypeNumber && this.dataType === '-3') {
            defaultUploadDataFunc = this.defaultUploadStgFunc
            this.temp.strategyTypeNumber = this.strategyTypeNumber
            //  confirm： true表示后端将校验导入的策略数据是否存在于不同策略类型（应用策略，预定义策略）的数据里
            this.temp.confirm = this.temp.importType === 1
            fd.append('param', JSON.stringify(this.temp));
          }
          const execFunc = !this.uploadFunc ? defaultUploadDataFunc : this.uploadFunc

          this.createImportStgConfirmSocket(this.taskId)

          execFunc(fd).then(res => {
            // this.$notify({ title: this.$t('text.success'), message: fileName + '文件导入成功', type: 'success', duration: 2000 })
            // 传递importGroupId数据，当且仅当excel表的所有数据分组都相同时，才存在importGroupId
            const data = res.data
            this.uploadEnd(data ? data.importGroupId : undefined)
          }).catch(res => {
            this.uploadFailed = true
            this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.processStgLib_Msg80', { name: fileName }), type: 'error', duration: 2000 })
            this.uploadEnd()
          })
          return false // 屏蔽了action的默认上传
        })
        .catch((err) => {
          // 文件有问题，在这里终止掉
          console.error('failed to read', err);
          this.$message({
            message: this.$t('pages.fileModifyChooseAgain'),
            type: 'error',
            duration: 2000
          })
          return false
        })
    },
    uploadEnd(importGroupId) {
      this.submitting = false
      this.$emit('success', importGroupId)
    },
    // 文件超出个数限制时的钩子，实现后上传的文件替换掉先上传的文件
    onFileExceed(files, fileList) {
      // 删除所有上传的文件
      this.$refs.upload.clearFiles();
      // handleStart()指的是手动选择文件，Element Plus 的el-upload有说明
      this.$refs.upload.handleStart(files[0])
    },
    onFileChange(file, fileList) {
      window.uploadFile = file.raw
      this.fileList = fileList
      this.errorMsg = ''
    },
    onFileRemove(file, fileList) {
      this.fileList = fileList
      this.errorMsg = ''
    },
    downloadTemplate(file) {
      return fetchFile({
        file,
        jwt: true,
        topic: 'ImportTemplate',
        url: '/importData/downloadTemplate',
        method: 'post',
        responseType: 'blob',
        data: {
          template: this.template,
          fileName: this.fileName + (this.templateType === 1 ? '.xls' : this.templateType === 2 ? '.xlsx' : '.et'),
          templateType: this.templateType
        }
      })
    },
    handleDrag() {
    },
    defaultUploadDataFunc(data) {
      return request.post('/mailLibrary/import', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    //  通用策略导入接口
    defaultUploadStgFunc(data) {
      return request.post('/importData/importStg', data, { 'Content-Type': 'multipart/form-data', timeout: 0 })
    },
    downloadErrorFile(file) {
      return fetchFile({
        file,
        jwt: true,
        topic: 'ImportErrorData',
        url: '/importData/downloadCache',
        method: 'post',
        responseType: 'blob',
        data: {
          template: this.uploadStatus.failFile,
          fileName: file.name
        }
      })
    },
    objectTree() {
      return this.$refs.objectTree
    },
    objectTypeChange(val) {
      this.checkedObject.splice(0)
      if (this.objectTree()) {
        this.objectTree().clearSelectedNode()
      }
    },
    objectTreeSelectChange(nodeId, node) {
      if (this.objectMultiple) {
        this.checkedObject.splice(0);
        (node || []).forEach(item => {
          this.checkedObject.push(item)
        })
      } else {
        const nodeObj = node[0] || node
        this.checkedObject.splice(0, 1, nodeObj)
      }
      // 更新 objectIds, objectGroupIds
      if (this.termAble || this.userAble) {
        const objectIds = []
        const objectGroupIds = []
        this.checkedObject.forEach(node => {
          if ([3, '3', 4, '4', 'G'].includes(node.type)) {
            objectGroupIds.push(node.dataId)
          } else {
            objectIds.push(node.dataId)
          }
        })
        Object.assign(this.temp, { objectIds, objectGroupIds })
      }
    },
    //  接收服务端发送的确认消息请求
    createImportStgConfirmSocket(taskId) {
      this.$socket.subscribeToUser(taskId, '/importData/importStg', (resp, handle) => {
        //  倒计时-定时器
        let time = parseInt(resp.data || '60000') / 1000 + 1;
        this.cancelInterval = setInterval(() => {
          this.cancelButtonName = this.$t('text.no') + '(' + time + ')'
          if (time === 60) {
            this.confirmFlag = true
          }
          time--
          if (time === -1) {
            this.confirmClick(false)
          }
        }, 1000);
        handle.close();
      }, false);
    },
    //  向后台发送确认请求
    sendImportStgConfirmSocket(taskId, confirm) {
      this.$socket.sendToUser(taskId, '/importData/importStgConfirm', { taskId, confirm }, (resp, handle) => {
        handle.close();
      }, false)
    },
    confirmClick(flag) {
      this.cancelInterval && clearInterval(this.cancelInterval);
      this.cancelInterval = null
      this.sendImportStgConfirmSocket(this.taskId, !!flag);
      this.confirmFlag = false;
    },
    objectValidator(rule, value, callback) {
      if (this.checkedObject.length > 0) {
        callback()
      } else {
        callback(new Error(this.$t('components.chooseApplicationObj')))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .upload-demo>>>.el-upload-list{
    min-height: 20px;
    max-height: 188px;
    overflow: hidden;
    margin-top: 2px;
    .el-upload-list__item:hover {
      background-color: #dbdbdb;
    }
  }
  .el-icon-delete-solid{
    margin-left: 20px;
    font-size: 16px;
    &:hover{
      color: rgb(12, 123, 226);
    }
  }
  .tpl-download {
    margin-left: 0;
    >>>.el-button {
      padding: 5px;
    }
  }
  .err-download>>>.el-button {
    margin-bottom: 0;
  }
  .el-upload__tip {
    padding-left: 15px;
    color: red;
    font-size: 13px;
  }
</style>
