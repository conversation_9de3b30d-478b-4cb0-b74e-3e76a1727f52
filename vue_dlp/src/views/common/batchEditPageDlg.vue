<!-- 批量删除、修改多页数据的组件 -->
<template>
  <el-dialog
    v-el-drag-dialog
    :title="title"
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dlgVisible"
    width="800px"
    @dragDialog="handleDrag"
    @close="closeCallback"
  >
    <Form ref="deptDataForm" label-position="left" style="width: 700px;">
      <FormItem v-if="deleteFilterName != null && editType !== 0 && dlgUsedAble" :label="this.$t('pages.batchEditPageDlgUsed', { name: deleteFilterName })">
        <el-radio-group v-model="filterUsed">
          <el-radio :label="true">{{ $t('pages.batchEditPageDlgUsedType1', { name: deleteFilterName }) }}</el-radio>
          <el-radio v-if="editType === 1" :label="false">{{ $t('pages.batchEditPageDlgUsedType2') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem v-if="deleteFilterName == null && filterNameDesc != null && editType !== 0" :label="this.$t('pages.batchEditPageDlgUsed1', { name: filterNameDesc })">
        <el-radio-group v-model="filterUsed">
          <el-radio :label="true">{{ $t('pages.batchEditPageDlgUsedType3', { name: filterNameDesc }) }}</el-radio>
          <el-radio v-if="editType === 1" :label="false">{{ $t('pages.batchEditPageDlgUsedType2') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem :label="$t('pages.effectiveScope')">
        <el-radio-group v-model="effective" @change="effectiveChangeEnd">
          <el-radio :label="1">{{ $t('pages.currentSelectedData',{selectLength: selectLength}) }}</el-radio>
          <el-radio :label="2">{{ $t('pages.currentQueryAllData',{ allResultLength: allResultLength} ) }}</el-radio>
        </el-radio-group>
      </FormItem>
      <slot></slot>
      <FormItem v-show="effective===2" label-width="11px" :extra-width="{en: 0}">
        <el-checkbox v-model="checkAll" @change="changeSelected">{{ $t('pages.checkAllPagesData') }}</el-checkbox>
      </FormItem>
      <grid-table
        ref="editTable"
        :col-model="tableColModel"
        :row-data-api="remoteRowDataApi"
        :height="tableHeight"
        :autoload="false"
        :after-load="afterLoad"
        pager-small
        :show-pager="effective===2"
        :default-sort="defaultSort"
        @select="selectRemoteData"
        @select-all="selectAllRemoteData"
      />
      <slot name="form-bottom"/>
    </Form>
    <div v-if="notice" style="color: #0c60a5;padding-top: 10px;margin-left: 28px;">{{ notice }}</div>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="doSubmit">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dlgVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'BatchEditPageDlg',
  props: {
    title: { type: String, default() { return this.$t('pages.edit') } },
    notice: { type: String, default: null }, // 提示信息
    height: { type: Number, default: 390 },
    defaultSort: { type: Object,
      default() { return {} }
    },
    rowDataApi: {// 返回行数据
      type: Function,
      default(option) { }
    },
    colModel: { // 列
      type: Array,
      default: () => { return [] }
    },
    removeColModelLabel: { // 删除colModel中对应label的列，默认把“操作”列删除
      type: Array,
      default() { return ['operate'] }
    },
    dlgUsedAble: {  // 是否显示过滤被策略使用选择框
      type: Boolean,
      default: true
    },
    deleteFilterName: {
      type: String,
      default: null
    },
    filterNameDesc: {
      type: String,
      default: null
    },
    editType: { // 0：修改分组 1：删除数据（可以不过滤直接删除） 2：删除数据（必须过滤正在使用的数据）
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      tableHeight: 0,
      query: {},
      tableColModel: [],
      rowDatas: [],
      submitting: false,
      dlgVisible: false,
      effective: 1, // 生效范围 1-当前选中终端 2-当前查询的所有终端
      selectLength: 0, // 当前选中终端的条数
      allResultLength: 0, // 当前查询的所有终端的条数
      checkAll: true,
      backupSelectedIds: [], // 备份当前选中的id
      backupSelectedDatas: [],
      backupUnSelectedIds: [], // 备份当前未选中的id
      backupRowData: [],
      updateQuery: false, // 是否根据条件查询所有数据（批量删除时使用）
      updateParams: {}, // 删除是提交到后台的参数
      filterUsed: true
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.tableColModel.splice(0)
    this.colModel.forEach(item => {
      if (this.removeColModelLabel.indexOf(item.label) < 0) {
        this.tableColModel.push(item)
      }
    })
  },
  methods: {
    show(selectedDatas, searchTotal, searchQuery) {
      this.dlgVisible = true
      this.backupUnSelectedIds.splice(0)
      this.backupSelectedIds.splice(0)
      this.backupSelectedDatas.splice(0)
      this.checkAll = true
      if (selectedDatas) {
        this.rowDatas.splice(0, this.rowDatas.length, ...selectedDatas)
      }
      this.query = !searchQuery ? {} : Object.assign({}, searchQuery)
      this.selectLength = this.rowDatas.length
      this.effective = this.selectLength ? 1 : 2
      this.allResultLength = !searchTotal ? 0 : searchTotal
      this.$nextTick(() => {
        this.effectiveChangeEnd()
        this.editTable().toggleAllSelection()
        // 避免修改的查询参数影响到上一个页面的查询
        this.editTable().execRowDataApi(this.query)
      })
    },
    editTable() {
      return this.$refs['editTable']
    },
    isSearchAll() {
      return this.effective === 2
    },
    effectiveChangeEnd() {
      this.tableHeight = this.height
      if (this.isSearchAll()) {
        this.tableHeight -= 35
      }
      if (this.effective === 1) {
        this.tableColModel = this.tableColModel.map((el) => {
          if (el.sort == 'custom') {
            el.sort = true
            return el
          } else {
            return el
          }
        })
      } else if (this.effective === 2) {
        this.tableColModel = this.tableColModel.map((el) => {
          if (el.sort == true) {
            el.sort = 'custom'
            return el
          } else {
            return el
          }
        })
      }
      this.editTable().execRowDataApi()
    },
    handleDrag() {
    },
    remoteRowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      const promise = this.isSearchAll() ? this.rowDataApi(searchQuery) : this.localRowDataApi(searchQuery)
      promise.then(res => {
        if (this.isSearchAll()) {
          this.allResultLength = res.data.total
        } else {
          this.selectLength = res.data.total
        }
      })
      return promise
    },
    async localRowDataApi(option) {
      return {
        code: 20000,
        data: {
          total: this.rowDatas.length,
          items: this.rowDatas
        }
      }
    },
    afterLoad(rowData, table) {
      this.$nextTick(() => {
        // 选中当前页面所有的终端,处理未选中的数据
        if (rowData.length > 0 && this.checkAll) {
          const backupUnSelectedIdsSet = new Set(this.backupUnSelectedIds)
          rowData.forEach(r => {
            if (!backupUnSelectedIdsSet.has(r['id'])) {
              this.editTable().toggleRowSelection(r, true)
            }
          })
        } else { // 未选中当前页面所有的终端,处理已选中的数据
          const backupSelectedIdsSet = new Set(this.backupSelectedIds)
          rowData.forEach(r => {
            if (backupSelectedIdsSet.has(r['id'])) {
              this.editTable().toggleRowSelection(r, true)
            }
          })
        }
      })
    },
    selectRemoteData(selection, row) {
      this.$nextTick(() => {
        const id = row['id']
        const selectedIds = this.editTable().getSelectedIds()
        const isSelect = selectedIds.indexOf(id) >= 0
        // 选中全部的情况下，缓存取消勾选的数据
        if (this.checkAll) {
          // 当前 row 在缓存的未勾选的数据ids中的位置
          const index = this.backupUnSelectedIds.indexOf(id)
          if (isSelect) {
            // 如果是勾选，则移除
            index >= 0 && this.backupUnSelectedIds.splice(index, 1)
          } else {
            // 如果是取消勾选，则添加
            index == -1 && this.backupUnSelectedIds.push(id)
          }
        } else {
          // 当前 row 在缓存的勾选的数据ids中的位置
          const index = this.backupSelectedIds.indexOf(id)
          if (isSelect) {
            // 如果是勾选，且未添加到数组，则添加
            if (index == -1) {
              this.backupSelectedIds.push(id)
              this.backupSelectedDatas.push(row)
            }
          } else {
            // 如果是取消勾选，且已添加到数组，则移除
            if (index >= 0) {
              this.backupSelectedIds.splice(index, 1)
              this.backupSelectedDatas.splice(index, 1)
            }
          }
        }
      })
    },
    selectAllRemoteData(selection) {
      this.$nextTick(() => {
        const rowDatas = this.editTable().rowData
        // 当前页面选中数据的长度为0，证明是取消全选
        const isUnselectAll = this.editTable().getSelectedIds().length === 0
        if (this.checkAll) {
          const backupUnSelectedIdsSet = new Set(this.backupUnSelectedIds)
          rowDatas.forEach(r => {
            const id = r['id']
            const isExist = backupUnSelectedIdsSet.has(id)
            // 取消全选且未缓存，则添加
            if (isUnselectAll && !isExist) {
              backupUnSelectedIdsSet.add(id)
            }
            // 全选且已缓存，则删除
            if (!isUnselectAll && isExist) {
              backupUnSelectedIdsSet.delete(id)
            }
          })
          // 根据 Set 更新 this.backupUnSelectedIds
          this.backupUnSelectedIds = Array.from(backupUnSelectedIdsSet)
        } else {
          // 未选中全部的情况下，缓存已勾选的数据
          const backupSelectedMap = new Map()
          this.backupSelectedIds.forEach((id, i) => { backupSelectedMap.set(id, this.backupSelectedDatas[i]) })
          rowDatas.forEach(r => {
            const id = r['id']
            const isExist = backupSelectedMap.has(id)
            // 全选且已未缓存，则添加
            if (!isUnselectAll && !isExist) {
              backupSelectedMap.set(id, r)
            }
            // 取消全选且已缓存，则删除
            if (isUnselectAll && isExist) {
              backupSelectedMap.delete(id)
            }
          })
          // 根据 Map 更新 this.backupSelectedIds, this.backupSelectedDatas
          this.backupSelectedIds = Array.from(backupSelectedMap.keys())
          this.backupSelectedDatas = Array.from(backupSelectedMap.values())
        }
      })
    },
    changeSelected() {
      this.backupSelectedIds.splice(0) // 备份已选中的行的id
      this.backupSelectedDatas.splice(0)
      this.backupUnSelectedIds.splice(0)
      if (this.checkAll) {
        this.editTable().toggleAllSelection()
      } else if (this.editTable().getSelectedDatas().length > 0) {
        this.editTable().clearSelection()
      }
    },
    // 验证未勾选数据，返回 false
    doValidate() {
      // 当前查询的所有数据标签页
      if (this.isSearchAll()) {
        // checkAll: 勾选所有页面的数据
        if (this.checkAll) {
          // 未勾选的数据量和总数据量一样，返回 false
          if (this.backupUnSelectedIds.length === this.allResultLength) {
            return false
          }
        } else if (this.backupSelectedIds.length === 0) {
          // 已勾选的数据为 0，返回 false
          return false
        }
      } else if (this.editTable().getSelectedDatas().length === 0) {
        // 当前选中数据标签页，已勾选的数据为 0，返回 false
        return false
      }
      return true
    },
    doSubmit() {
      this.submitting = true
      if (!this.doValidate()) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.currentNoSelectedAnyData'),
          type: 'warning',
          duration: 2000
        })
        this.submitting = false
        return
      }
      if (this.isSearchAll()) {
        if (this.checkAll) {
          this.updateParams = Object.assign({}, this.query)
          this.updateParams.page = 1
          this.updateParams.limit = this.allResultLength
          this.updateParams.backupUnSelectedIds = this.backupUnSelectedIds
          this.updateParams.updateQuery = true
        } else {
          const toDeleteIds = this.backupSelectedIds.join(',')
          this.updateParams = Object.assign({})
          this.updateParams.ids = toDeleteIds
          this.updateParams.updateQuery = false
        }
      } else {
        const toDeleteIds = this.editTable().getSelectedIds().join(',')
        this.updateParams = Object.assign({})
        this.updateParams.ids = toDeleteIds
        this.updateParams.updateQuery = false
      }
      if (this.editType !== 0) {
        this.updateParams.filterUsed = this.filterUsed
      }
      this.$emit('submitEnd', this.updateParams, this.submitCallback)
    },
    resetSubmitting() {
      this.submitting = false
    },
    submitCallback(e) {
      this.submitting = false
      if ('cancel' === e) {
        // 如果是确认框种输入取消，那么不需要关闭
        return
      }
      this.dlgVisible = false
    },
    closeCallback() {
      this.$emit('close')
    }
  }
}
</script>
