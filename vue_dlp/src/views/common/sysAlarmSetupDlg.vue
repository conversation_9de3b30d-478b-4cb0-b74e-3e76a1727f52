<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.sysAlarmSetup_softwareOrder')"
      :visible.sync="dialogVisible"
      width="600px"
      @closed="closed"
    >
      <Form ref="sysAlarmsetupDialog" :rules="rules" :model="temp" label-position="right" label-width="120px" style="width: 550px; padding: 0 16px;">
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <el-checkbox v-model="temp.excessAlarm" class="checkbox" @change="excessAlarmChange">
          {{ $t('pages.sysAlarmSetup_softwareOrder1') }}

          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.sysAlarmSetup_softwareOrderTip1') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-checkbox>
        <el-checkbox v-model="temp.imminentAlarm" class="checkbox" @change="imminentAlarmChange">
          {{ $t('pages.sysAlarmSetup_softwareOrder2') }}
        </el-checkbox>
        <FormItem v-if="temp.imminentAlarm" :label="$t('pages.imminentAlarmDays')" prop="imminentAlarmDays">
          <el-input v-model.number="temp.imminentAlarmDays" style="width: 150px;" maxlength="2" min="1" max="99" @input="numberLimit('imminentAlarmDays')">
            <template slot="append"> {{ $t('pages.day1') }}</template>
          </el-input>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.sysAlarmSetup_softwareOrderTip2', { days: temp.imminentAlarmDays}) }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem v-if="temp.imminentAlarm" :label="$t('pages.alarmFrequency')">
          <el-radio-group v-model="temp.imminentAlarmFrequency">
            <el-radio :label="1">{{ $t('pages.onlyOneAlarm') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.dailyOneAlarm') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <el-checkbox v-model="temp.expireAlarm" class="checkbox" @change="expireAlarmChange">
          {{ $t('pages.sysAlarmSetup_softwareOrder3') }}
        </el-checkbox>
        <FormItem v-if="temp.expireAlarm" :label="$t('pages.expireAlarmDays')" prop="expireAlarmDays">
          <el-input v-model.number="temp.expireAlarmDays" style="width: 150px;" maxlength="2" min="1" max="99" @input="numberLimit('expireAlarmDays')">
            <template slot="append"> {{ $t('pages.day1') }}</template>
          </el-input>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.sysAlarmSetup_softwareOrderTip3', { days: temp.expireAlarmDays}) }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem v-if="temp.expireAlarm" :label="$t('pages.alarmFrequency')">
          <el-radio-group v-model="temp.expireAlarmFrequency">
            <el-radio :label="1">{{ $t('pages.onlyOneAlarm') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.dailyOneAlarm') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <!-- 控制台弹窗告警 -->
        <el-divider content-position="left">{{ $t('pages.serverAlarmResponseRule') }}</el-divider>
        <div class="warnRule">
          <el-checkbox v-model="temp.alarmPop" :disabled="!editable" class="checkbox">
            {{ $t('pages.respondActions3') }}
          </el-checkbox>
          <FormItem v-if="temp.alarmPop && editable" label-width="0px">
            <el-select
              v-model="temp.sysUserIds"
              :disabled="!editable"
              filterable
              multiple
              :placeholder="$t('pages.alarmSetup_text7')"
              @change="sysUserChange"
            >
              <el-option
                v-for="user in sysUserList"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              >
              </el-option>
            </el-select>
          </FormItem>
          <!-- 邮件告警 -->
          <el-checkbox v-model="temp.alarmMail" :disabled="!editable" class="checkbox">
            {{ $t('pages.serverEmailAlarm') }}
          </el-checkbox>
          <data-editor
            v-if="temp.alarmMail && editable"
            append-to-body
            :popover-width="500"
            :updateable="updateable"
            :deletable="deleteable"
            :add-func="createEmail"
            :update-func="updateEmail"
            :delete-func="deleteEmail"
            :cancel-func="cancelEmail"
            :before-update="beforeUpdateEmail"
          >
            <Form ref="emailForm" :model="emailTemp" :rules="emailRules" label-position="right" label-width="85px" style="margin-left: -10px">
              <FormItem :label="$t('table.emailName')" prop="name">
                <el-input v-model="emailTemp.name" maxlength="32"/>
              </FormItem>
              <FormItem :label="$t('table.emailAddress')" prop="address">
                <el-input v-model="emailTemp.address" maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.remark')" prop="remark">
                <el-input v-model="emailTemp.remark" type="textarea" show-word-limit maxlength="128"/>
              </FormItem>
            </Form>
            <empty-mail-server-prompt slot="attach-btn"/>
          </data-editor>
          <grid-table
            v-if="temp.alarmMail && editable"
            ref="emailTable"
            :height="150"
            :multi-select="true"
            :show-pager="false"
            :col-model="emailColModel"
            :row-datas="temp.emails"
            style="margin-bottom: 10px;"
            @selectionChangeEnd="handleSelectionChange"
          />
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="saveData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <mail-import-table-dlg ref="mailImportTableDlg" @submitEnd="importEnd"/>
  </div>
</template>

<script>
import { getAlarmSetup, saveAlarmSetup } from '@/api/softwareManage/copyrightManage/orders'
import { initTimestamp } from '@/utils'
import MailImportTableDlg from '@/views/system/baseData/groupImportList/mailImportTableDlg';
import EmptyMailServerPrompt from '@/views/system/deviceManage/mailServer/prompt'

export default {
  name: 'SysAlarmSetupDlg',
  components: { EmptyMailServerPrompt, MailImportTableDlg },
  data() {
    return {
      temp: {},                     // 当前预警设置的基本信息
      defaultTemp: {
        id: undefined,
        bizType: 2, // 2：软件订单预警
        excessAlarm: false,
        imminentAlarm: false,
        imminentAlarmFrequency: 1,
        imminentAlarmDays: undefined,
        expireAlarm: false,
        expireAlarmDays: undefined,
        expireAlarmFrequency: 1,
        alarmPop: false,
        alarmMail: false,
        sysUserIds: [],
        emails: []
      },
      emailTemp: {},
      defaultEmailTemp: {
        id: undefined,
        name: '',
        address: '',
        remark: ''
      },
      deleteable: false,            // 邮箱删除按钮是否可用
      updateable: false,            // 邮箱修改按钮是否可用
      editable: false,              // 响应规则可编辑状态
      dialogVisible: false,         // 策略窗口显示
      submitting: false,            // 数据提交中
      emailColModel: [
        { prop: 'name', label: 'emailName', width: '100', sort: true },
        { prop: 'address', label: 'emailAddress', width: '120', sort: true },
        { prop: 'remark', label: 'remark', width: '100', sort: true }
      ],
      emailRules: { // 邮箱校验
        name: [{ required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }],
        address: [
          { required: true, type: 'email', message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.emailAddressCheck, trigger: 'blur' }
        ]
      },
      rules: {
        imminentAlarmDays: [{ validator: this.imminentAlarmDaysValidator, trigger: 'blur' }],
        expireAlarmDays: [{ validator: this.expireAlarmDaysValidator, trigger: 'blur' }]
      }
    }
  },
  computed: {
    // 系统管理员列表
    sysUserList() {
      return this.$store.getters.sysUserList
    },
    gridTable() {
      return this.$refs['logList']
    }
  },
  created() {
    initTimestamp(this)
    this.resetTemp()
    this.resetEmailTemp()
    this.getAlarmSetup()
  },
  activated() {
    initTimestamp(this)
  },
  provide() {
    return {
      showImportAccDlg: () => this.$refs['mailImportTableDlg'] && this.$refs['mailImportTableDlg'].show(),
      importDlgBtnName: this.$t('pages.emailLibImport')
    }
  },
  methods: {
    validateForm() {
      if ((this.temp.excessAlarm || this.temp.imminentAlarm) && !this.temp.alarmPop && !this.temp.alarmMail) {
        this.$message({
          message: '请至少选择一种提醒方式',
          type: 'error'
        })
        return false
      }
      return true
    },
    emailAddressCheck(rule, value, callback) {
      const email = (this.temp.emails || []).find(email => email.address === value)
      if (email && email.id !== this.emailTemp.id) {
        callback(new Error(this.$t('pages.effectiveContent_text31')))
        return
      }
      callback()
    },
    // 超额提醒启用状态变更
    excessAlarmChange(data) {
      if (!data && !this.temp.imminentAlarm && !this.temp.expireAlarm) {
        this.temp.alarmPop = false
        this.temp.alarmMail = false
        this.editable = false
      } else {
        this.editable = true
      }
    },
    // 临期提醒启用状态变更
    imminentAlarmChange(data) {
      // 启用临期告警提醒时，临期天数默认7天
      if (data) {
        this.temp.imminentAlarmDays = 7
      }
      if (!data && !this.temp.excessAlarm && !this.temp.expireAlarm) {
        this.temp.alarmPop = false
        this.temp.alarmMail = false
        this.editable = false
      } else {
        this.editable = true
      }
    },
    // 到期提醒启用状态变更
    expireAlarmChange(data) {
      // 启用到期告警提醒时，到期天数默认3天
      if (data) {
        this.temp.expireAlarmDays = 3
      }
      if (!data && !this.temp.excessAlarm && !this.temp.imminentAlarm) {
        this.temp.alarmPop = false
        this.temp.alarmMail = false
        this.editable = false
      } else {
        this.editable = true
      }
    },
    numberLimit(value, type, min, max) {
      this.temp[type] = value > max ? max : value < min ? min : parseInt(value)
    },
    getAlarmSetup() {
      this.editable = false
      getAlarmSetup().then(res => {
        this.temp = Object.assign(this.temp, res.data)
        if (this.temp.excessAlarm || this.temp.imminentAlarm || this.temp.expireAlarm) {
          this.editable = true
        }
      })
    },
    resetEmailTemp() {
      this.emailTemp = JSON.parse(JSON.stringify(this.defaultEmailTemp))
    },
    emailTable() {
      return this.$refs['emailTable']
    },
    emailForm() {
      return this.$refs['emailForm']
    },
    // 新增邮箱数据
    createEmail() {
      let validate
      this.emailForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.emailTemp)
          rowData.id = new Date().getTime()
          const emails = this.temp.emails
          // 防止重复
          if (emails.length > 0 && rowData.id < emails[0].id) {
            rowData.id = emails[0].id + 1
          }
          this.temp.emails.unshift(rowData)
          this.cancelEmail()
          validate = valid
        }
      })
      return validate
    },
    // 修改邮箱数据前，更新emailTemp数据
    beforeUpdateEmail() {
      Object.assign(this.emailTemp, this.emailTable().getSelectedDatas()[0])
    },
    // 修改邮箱数据
    updateEmail() {
      let validate
      this.emailForm().validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.emailTemp)
          for (let i = 0, size = this.temp.emails.length; i < size; i++) {
            const data = this.temp.emails[i]
            if (rowData.id === data.id) {
              this.temp.emails.splice(i, 1, rowData)
              break
            }
          }
          this.cancelEmail()
          validate = valid
        }
      })
      return validate
    },
    // 删除邮箱数据
    deleteEmail() {
      const toDeleteIds = this.emailTable().getSelectedIds()
      this.temp.emails.splice(0, this.temp.emails.length, ...this.emailTable().deleteRowData(toDeleteIds))
      this.cancelEmail()
    },
    // 重置邮箱表单
    cancelEmail() {
      this.emailTable() && this.emailTable().setCurrentRow()
      this.emailForm() && this.emailForm().clearValidate()
      this.resetEmailTemp()
    },
    // 邮箱列表数据勾选
    handleSelectionChange(rowDatas) {
      this.deleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.updateable = true
      } else {
        this.updateable = false
        this.cancelEmail()
      }
    },
    clearValidate() {
      this.$nextTick(() => {
        this.$refs['sysAlarmsetupDialog'] && this.$refs['sysAlarmsetupDialog'].clearValidate()
        let dataForm = this.$refs['sysAlarmsetupDialog' + this.activeSlotName]
        if (Array.isArray(dataForm)) dataForm = dataForm[0]
        if (dataForm) {
          dataForm.clearValidate()
        }
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.submitting = false
    },
    closed() {
      this.$emit('closed')
    },
    show() {
      this.dialogVisible = true
      this.resetTemp()
      this.getAlarmSetup()
    },
    sysUserChange(selections) {
      const length = selections.length
      if (length > 1) {
        if (selections[length - 1] == 0 || length == this.temp.sysUserIds.length - 1) {
          // 当最后一个选择‘所有管理员’，或者选择了除‘所有管理员’的其他所有选项时，清空所有其他选项，只保留‘所有管理员’
          selections.splice(0, length, 0)
        } else if (selections[0] == 0) {
          // ‘所有管理员’在选项第一个时，去掉该选项
          selections.splice(0, 1)
        }
      }
    },
    submitFormData(submitFunc) {
      this.validFormData(() => {
        this.submitting = true
        const tempData = Object.assign({}, this.temp)
        if (!tempData.alarmPop) {
          tempData.sysUserIds = null
        }
        if (!tempData.alarmMail) {
          tempData.emails = null
        }
        submitFunc(tempData).then(respond => {
          this.$emit('submitEnd', tempData)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.saveSuccess'),
            type: 'success',
            duration: 2000
          })
          this.dialogVisible = false
          this.submitting = false
        }).catch(e => {
          this.submitting = false
        })
      })
    },
    validFormData(submitFunc) {
      this.submitting = true
      // 先验证头部公共部分的表单
      this.$refs['sysAlarmsetupDialog'].validate((valid) => {
        if (valid) {
          if (this.validateForm()) {
            if (this.temp.alarmPop && (!this.temp.sysUserIds || this.temp.sysUserIds.length == 0)) {
              this.$message({
                message: '请至少选择一个管理员',
                type: 'error'
              })
              this.submitting = false
            } else if (this.temp.alarmMail && (!this.temp.emails || this.temp.emails.length == 0)) {
              this.$message({
                message: '请至少填写一个邮箱',
                type: 'error'
              })
              this.submitting = false
            } else if (submitFunc) {
              submitFunc()
            } else {
              this.submitting = false
            }
          } else {
            this.submitting = false
          }
        } else { this.submitting = false }
      })
    },
    saveData() {
      this.submitFormData(saveAlarmSetup)
    },
    importEnd(datas) {
      if (datas) {
        datas.forEach(item => {
          const { name, address, remark } = item
          const emails = this.temp.emails || []
          const time = new Date().getTime()
          const id = emails.length === 0 || time > emails[0].id ? time : emails[0].id + 1
          this.emailAddressCheck(null, address, info => {
            if (!info) {
              this.temp.emails.unshift({ id, name, address, remark })
            }
          })
        })
      }
    },
    imminentAlarmDaysValidator(rule, value, callback) {
      if (this.temp.imminentAlarm && (value == 0 || isNaN(Number(value)) || value.toString().indexOf('.') > -1 || value.toString().indexOf('+') > -1 || Number(value) < 0)) {
        callback(this.$t('pages.validateGreaterThan_0'))
      }
      callback()
    },
    expireAlarmDaysValidator(rule, value, callback) {
      if (this.temp.expireAlarm && (value == 0 || isNaN(Number(value)) || value.toString().indexOf('.') > -1 || value.toString().indexOf('+') > -1 || Number(value) < 0)) {
        callback(this.$t('pages.validateGreaterThan_0'))
      }
      callback()
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-divider {
    width: calc(100% + 32px);
    margin-left: -16px !important;
  }
  .checkbox {
    line-height: 30px;
    display: block;
  }
</style>
