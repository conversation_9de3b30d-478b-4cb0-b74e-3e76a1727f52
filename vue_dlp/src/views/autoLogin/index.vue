<template>
  <div class="login-container">
    <div class="prompt">
      <div v-if="lowVersion" class="browser-prompt">
        {{ $t('login.lowVersion_title') }}
        <el-tooltip class="item" effect="dark" placement="bottom">
          <div slot="content">{{ $t('login.lowVersion_tip') }}</div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
    </div>
    <div v-loading="loading" class="content-box">
      <!-- <div class="title-box">
        <img :src="logoSrc" alt="logo">
        <h3 class="title">{{ title }}</h3>
      </div>
      <p v-if="loading">正在自动登录...</p>
      <div v-if="loginErrorMsg" class="msg-box">
        <p>{{ loginErrorMsg }}</p>
        <el-button size="small" @click="returnLogin">{{ $t('pages.returnLogin') }}</el-button>
      </div>
      -->
    </div>
  </div>
</template>

<script>
import { getUrlParams } from '@/utils'
import Cookies from 'js-cookie'
import { getSystemResources } from '@/utils/i18n'
import { getToken } from '@/utils/auth'

const Base64 = require('js-base64').Base64

export default {
  name: 'AutoLogin',
  components: {},
  data() {
    const isIE = !!(window.ActiveXObject || 'ActiveXObject' in window)
    const html5 = typeof (Object.defineProperty) !== 'undefined'
    const lowVersion = isIE || !html5
    return {
      lowVersion: lowVersion,   // IE 或 不支持 html5 的浏览器
      title: '数据保险', // title,
      loading: false,
      redirect: undefined,
      showReg: false,
      loginErrorMsg: ''
    }
  },
  computed: {
    copyright() {
      return getSystemResources('copyright')
    },
    logoSrc() {
      return getSystemResources('topLogo')
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.autoLogin()
  },
  methods: {
    autoLogin() {
      // 从其他系统跳转到Dlp时，携带的参数 userName 账号, password 密码, rememberPassword 记住密码, type 系统类型（第三方系统未携带该参数）, expires 过期时间
      const userInfo = this.getUserInfo()
      const username = userInfo.userName || userInfo.username
      const { password, rememberPassword, type, expires } = userInfo
      if (this.lowVersion) return
      // 有账号、密码
      if (username && password) {
        // 判断自动登录的信息是否过期
        const isExpires = !expires || new Date() > new Date(expires)
        if (isExpires) {
          // 登录信息过期，返回登录页
          this.$message({
            message: this.$t('pages.loginExpires'),
            type: 'error',
            duration: 3000
          })
          this.returnLogin()
        } else {
          // 登录信息未过期，尝试登录
          this.loading = true
          this.handleLogin({ username, password, rememberPassword, type })
        }
      } else {
        this.loading = false
        this.loginErrorMsg = this.$t('pages.loginErrorInfo5')
        if (type) {
          // 没有账号、密码，且拥有 type，返回登录页
          this.$message({
            message: this.$t('pages.loginErrorInfo5'),
            type: 'error',
            duration: 3000
          })
          this.returnLogin()
        } else if (Cookies.get('isLogin')) {
          // 从当前系统访问路由 autoLogin，则跳转到首页
          this.$router.push({ path: '/' })
        }
      }
    },
    // 使用 info 的数据登录系统
    handleLogin(info) {
      this.$store.dispatch('user/login', info).then(() => {
        // 没有 type 的话，在 cookies 中设置 isExternal，用于\src\permission.js 判断当登陆状态失效后 通过代码关闭页面（二开功能）
        if (!info.type) {
          Cookies.set('isExternal', true)
        }
        this.$router.push({ path: this.redirect || '/' })
      }).catch(e => {
        this.loading = false
        this.loginErrorMsg = e
        if (info.type) {
          // 登录失败，且拥有 type，返回登录页
          this.returnLogin()
        }
      })
    },
    getUserInfo() {
      let { t = '' } = getUrlParams(window.location.href)
      if (t && t.length > 1) {
        t = decodeURIComponent(t)
        let i = t.substring(t.length - 1)
        if (!isNaN(Number.parseInt(i))) {
          i = Number.parseInt(i)
          if (t.length > 2 * i) {
            const c1 = t.substring(i, i + 1)
            const c2 = t.substring(2 * i, 2 * i + 1)
            const s = t.substring(0, i) + c2 + t.substring(i + 1, 2 * i) + c1 + t.substring(2 * i + 1, t.length - 1)
            const str = Base64.decode(s)
            return JSON.parse(str)
          }
        }
      }
      return {}
    },
    clearToken() {
      const token = getToken()
      // 如果有token
      if (token) {
        // 清除 token
        this.$store.dispatch('user/setToken', '')
      }
    },
    returnLogin() {
      this.clearToken()
      this.$router.push({ path: '/login' })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100%;
  width: 100%;
  // background-image: url('~@/assets/login/loginBg.jpg');
  background-color: #1F263E;
  overflow: hidden;
}

.prompt{
  line-height: 36px;
  position: absolute;
  text-align: center;
  width: 100%;
  font-size: 18px;
}
.content-box {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%);
  text-align: center;
}
.title-box{
  img{
    vertical-align: middle;
  }
  .title{
    margin: 0;
    padding-left: 20px;
    font-size: 28px;
    display: inline-block;
    vertical-align: middle;
  }
}
.msg-box p{
  color: red;
}
</style>

