<template>
  <div class="wscn-http404-container">
    <div class="wscn-http404" style="display: flex;">
      <div class="pic-404">
        <img class="pic-404__parent" src="@/assets/icon/recover.png" alt="recover">
      </div>
      <div class="bullshit" style="line-height: 400px; height: 420px;">
        <div :style="messageParentStyle">
          <div class="bullshit__oops" :style="message1Style">{{ message1 }}</div>
          <div class="bullshit__oops" :style="message2Style">{{ message2 }}</div>
          <div class="bullshit__oops" style="text-align: center;">
            <el-button v-if="isReturnLogin" @click="returnLogin">{{ $t('pages.recovering_returnLogin') }}</el-button>
          </div>
        </div>
      </div>
    </div>
    <div v-if="fullscreenLoading" style="position: absolute; top: 120%; width: 100%; text-align: center">
      <el-progress :text-inside="true" style="width: 100%; text-align: center" :stroke-width="26" :percentage="percent"></el-progress>
    </div>
  </div>
</template>

<script>

import {
  saveProcess,
  getProcess,
  removeProcess,
  getIsRecovering,
  getMessage,
  removeIsRecovering,
  restartApp,
  saveMessage,
  setIsRecovering,
  removeMessage,
  clearStatusByFailed,
  getBackupRecovering
} from '@/api/system/terminalManage/manageLibrary';
import { getRegisterStatus } from '@/api/system/register/reg';
import Cookies from 'js-cookie';

export default {
  name: 'Recovering',
  data() {
    return {
      timer: null,
      recoverFinishTimer: null,
      message1: '',
      message1Style: { 'font-size': '25px' },
      message2Style: { 'font-size': '25px', 'white-space': 'normal' },
      messageParentStyle: { 'position': 'relative', 'height': '50%', 'top': '50%', 'transform': 'translateY(-50%)' },
      message2: '',
      fullscreenLoading: false,
      percent: 0,  //  进度数  0：0%
      isReturnLogin: false
    }
  },
  watch: {
    fullscreenLoading(val) {
      if (!val) {
        //   恢复状态不在3（正在恢复中，恢复完成）时，将清空恢复进度
        removeProcess()
      }
    }
  },
  activated() {
    this.init()
  },
  created() {
    this.init()
  },
  deactivated() {
    clearInterval(this.timer)
    this.recoverFinishTimer && clearInterval(this.recoverFinishTimer)
    this.recoverFinishTimer = null
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    async init() {
      //  先从缓存中获取数据，使用缓存加载数据，是为了快速显示恢复页面，而不会出现延迟
      const percent = getProcess()
      const message = getMessage()
      let messageData = {}
      let status = getIsRecovering() || 0
      if (message !== undefined && message !== null && message !== '') {
        this.setProcess(percent || 0)
        messageData = JSON.parse(message) || {}
        await this.initAfter(status, messageData)
      }
      //   再实时获取最新数据（防止其他页面在疯狂按F5刷新页面时，频繁刷新的页面和其他页面的进度不一致问题）
      const res = await getBackupRecovering(1);
      status = res.data.status || 0
      this.setProcess(res.data.recoverProcess || 0)
      messageData = res.data.messageHintData || {}
      await this.initAfter(status, messageData)
    },
    async initAfter(status, messageData) {
      clearInterval(this.timer)
      setIsRecovering(status)
      status != 6 && this.setInterval()
      this.fullscreenLoading = status == 3 || status == 1
      if (messageData != null) {
        this.isReturnLogin = messageData.isReturnLogin || false
        if (status) {
          this.setMessage(messageData.message1 || '', messageData.message2 || '', messageData.color1 || null, messageData.color2 || null, this.isReturnLogin);
          //  status === 6 代表重启tomcat服务
          status == 6 && await this.restartTomcat()
        }
      }
    },
    //  监听后台发送socket获取恢复状态
    recoverSocket() {
      const _this = this
      //  获取恢复状态
      this.$socket.subscribeToUser('localBackupTaskId', '/databaseBackup/backupRecover', async(resp, handle) => {
        handle.close();
        const data = JSON.parse(resp.body) || {};
        const language = this.$store.getters.language
        if (!language || data.language == language) {
          await _this.recoveredAfter(data)
        }
      }, false)
    },
    //  根据恢复状态进行对于的处理
    async recoveredAfter(data) {
      const messageHintData = data.messageHintData || {}
      //  设置进度条
      this.setProcess(data.recoverProcess || 0)
      //  上次的恢复状态， 可能存在旧恢复状态和当前恢复状态同时过期，此时
      const oldStatus = data.oldStatus || 0
      //  设置是否显示返回按钮
      const isReturnLogin = messageHintData.isReturnLogin || false
      //  进度条只有在 3:正在恢复中, 1: 恢复成功时才会显示（恢复成功拥有2秒钟的显示效果）
      this.fullscreenLoading = data.status == 3 || data.status == 1
      //   设置恢复状态
      setIsRecovering(data.status);
      //  设置消息
      this.setMessage(messageHintData.message1 || '', messageHintData.message2 || '', messageHintData.color1 || null, messageHintData.color2 || null, isReturnLogin)
      this.isReturnLogin = isReturnLogin
      //  恢复状态3：表示正在恢复中
      if (data.status !== 3) {
        //  失败
        if (data.status == 2 || data.status == 7 || data.status == 8 || data.status == 9 || (data.status == 0 && oldStatus == 1) || (data.status == 0 && oldStatus == 3)) {
          //  停止定时获取恢复状态
          clearInterval(this.timer)
          this.clearRecover()
        } else if (data.status == 0 || data.status == 6) {
          // 停止获取恢复状态
          clearInterval(this.timer)
          //  如果状态为6，通知dlp重启系统
          if (data.status == 6) {
            const restartId = data.restartId || ''
            restartApp({ restartId: restartId }).then(async res => {
              //  如果重启时是重启tomcat
              await this.restartTomcat()
            })
          } else if (oldStatus !== null && oldStatus == 6 && data.status == 0) {
            //  如果重启时是重启tomcat
            await this.restartTomcat()
          } else {
            this.clearRecover()
            Cookies.remove('loginErrorCode')
            this.$router.push({ path: '/login' });
          }
        }
      }
    },
    //   清空状态
    clearRecover() {
      //  清空恢复状态
      removeIsRecovering();
      //  清空提示消息
      removeMessage();
      this.fullscreenLoading = false
    },
    //  向后台请求恢复状态
    getRecoverStatus() {
      const language = this.$store.getters.language
      this.$socket.sendToUser('localBackupTaskId', '/databaseBackup/backupRecover', { language: language }, (resp, handle) => {
        handle.close();
      }, false)
    },
    //  开启定时任务，获取恢复状态
    setInterval() {
      this.recoverSocket();
      this.getRecoverStatus()
      //  每3秒发送一次
      this.timer = setInterval(() => {
        this.recoverSocket();
        this.getRecoverStatus()
      }, 3000)
    },
    //  重启tomcat服务
    async restartTomcat() {
      //  设置页面内容，返回登录界面按钮隐藏
      this.setMessage(this.$t('pages.recovering_dbRecoverFinishRestartMessage1'), '', null, null, false);
      this.setRecoverFinishTimerByTomcat()
      //  设置等待时间，目的是为了进度到达100时等待2秒，再隐藏进度条
      const sleep = (delay) => new Promise((resolve) => setTimeout(resolve, delay))
      await sleep(2000)
      this.fullscreenLoading = false
    },
    //  由于前端向后台请求重启dlp服务时，存在dlp重启失败和成功两种情况，该方法为了保证页面
    setRecoverFinishTimerByTomcat: function() {
      if (this.recoverFinishTimer == null) {
        this.recoverFinishTimer = setInterval(() => {
          //  设置为6时，确保请求无法响应时，不会抛出异常
          setIsRecovering(6)
          //  获取注册状态，确保连接是否成功，若在此处请求接口失败，表示dlp服务重启成功（dlp重启时，前端请求无法被响应）
          getRegisterStatus().then(res => {
            if (res && res.data && res.data.code == 5200) {
              this.recoverFinishTimer && clearInterval(this.recoverFinishTimer)
              this.recoverFinishTimer = null
              removeIsRecovering()
              //  获取恢复状态，重启后状态应该为0
              getBackupRecovering(1).then(res => {
                if (res.data.status == 0) {
                  this.clearRecover()
                  Cookies.remove('loginErrorCode')
                  this.$router.push({ path: '/login' });
                  //  重新连接socket
                  this.$socket.reconnect()
                } else if (res.data.status == 6) {
                  //  若状态为6 重启DLP，再此执行获取恢复状态方法
                  this.setInterval()
                } else if (res.data.status == 7) {
                  //  若状态为7表示重启dlp服务失败
                  const messageHintData = res.data.messageHintData || {}
                  const isReturnLogin = res.data.isReturnLogin || false
                  setIsRecovering(res.data.status)
                  //  设置消息
                  this.setMessage(messageHintData.message1 || '', messageHintData.message2 || '', messageHintData.color1 || null, messageHintData.color2 || null, isReturnLogin)
                }
              })
            }
          })
        }, 5000)
      }
    },
    updateMessageStyle() {
      let count = 0
      if (this.message1.length > 0) {
        count++
      }
      if (this.message2.length > 0) {
        count++
      }
      this.$set(this.messageParentStyle, 'top', count === 2 ? '30%' : '50%');
    },
    //  改变页面的消息
    setMessage(message1, message2, color1, color2, isReturnLogin) {
      this.message1 = message1 === undefined || message1 === null ? this.message1 : message1;
      this.message2 = message2 === undefined || message2 === null ? this.message2 : message2;
      if (color1) {
        this.$set(this.message1Style, 'color', color1);
      } else {
        this.$delete(this.message1Style, 'color');
      }
      if (color2) {
        this.$set(this.message2Style, 'color', color2);
      } else {
        this.$delete(this.message2Style, 'color');
      }
      this.updateMessageStyle()
      //  保存当前提示语言
      saveMessage({ message1, message2, color1, color2, isReturnLogin })
    },
    //  设置进度值
    setProcess(val) {
      if (val !== undefined && val !== null) {
        this.percent = val
      }
      saveProcess(this.percent)
    },
    //  返回到登录界面
    returnLogin() {
      //  清空后台的恢复状态
      clearStatusByFailed().then(res => {
        this.clearRecover()
        Cookies.remove('loginErrorCode')
        this.$router.push({ path: '/login' })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wscn-http404-container{
  transform: translate(-50%,-50%);
  position: absolute;
  top: 40%;
  left: 50%;
}
.wscn-http404 {
  position: relative;
  width: 1200px;
  padding: 0 50px;
  overflow: hidden;
  .pic-404 {
    position: relative;
    float: left;
    width: 600px;
    overflow: hidden;
    &__parent {
      width: 100%;
    }
    &__child {
      position: absolute;
      &.left {
        width: 80px;
        top: 17px;
        left: 220px;
        opacity: 0;
        animation-name: cloudLeft;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
        animation-delay: 1s;
      }
      &.mid {
        width: 46px;
        top: 10px;
        left: 420px;
        opacity: 0;
        animation-name: cloudMid;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
        animation-delay: 1.2s;
      }
      &.right {
        width: 62px;
        top: 100px;
        left: 500px;
        opacity: 0;
        animation-name: cloudRight;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
        animation-delay: 1s;
      }
      @keyframes cloudLeft {
        0% {
          top: 17px;
          left: 220px;
          opacity: 0;
        }
        20% {
          top: 33px;
          left: 188px;
          opacity: 1;
        }
        80% {
          top: 81px;
          left: 92px;
          opacity: 1;
        }
        100% {
          top: 97px;
          left: 60px;
          opacity: 0;
        }
      }
      @keyframes cloudMid {
        0% {
          top: 10px;
          left: 420px;
          opacity: 0;
        }
        20% {
          top: 40px;
          left: 360px;
          opacity: 1;
        }
        70% {
          top: 130px;
          left: 180px;
          opacity: 1;
        }
        100% {
          top: 160px;
          left: 120px;
          opacity: 0;
        }
      }
      @keyframes cloudRight {
        0% {
          top: 100px;
          left: 500px;
          opacity: 0;
        }
        20% {
          top: 120px;
          left: 460px;
          opacity: 1;
        }
        80% {
          top: 180px;
          left: 340px;
          opacity: 1;
        }
        100% {
          top: 200px;
          left: 300px;
          opacity: 0;
        }
      }
    }
  }
  .bullshit {
    position: relative;
    float: left;
    width: 360px;
    padding: 30px 0;
    //overflow: hidden;
    &__oops {
      font-size: 32px;
      font-weight: bold;
      line-height: 40px;
      color: #1482f0;
      opacity: 0;
      margin-bottom: 20px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-fill-mode: forwards;
    }
    &__headline {
      white-space: nowrap;
      font-size: 20px;
      line-height: 24px;
      color: #ccc;
      font-weight: bold;
      opacity: 0;
      margin-bottom: 10px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.1s;
      animation-fill-mode: forwards;
    }
    &__info {
      font-size: 13px;
      line-height: 21px;
      color: grey;
      opacity: 0;
      margin-bottom: 30px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.2s;
      animation-fill-mode: forwards;
    }
    &__return-home {
      display: block;
      float: left;
      width: 110px;
      height: 36px;
      background: #1482f0;
      border-radius: 100px;
      text-align: center;
      color: #ffffff;
      opacity: 0;
      font-size: 14px;
      line-height: 36px;
      cursor: pointer;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.3s;
      animation-fill-mode: forwards;
    }
    @keyframes slideUp {
      0% {
        transform: translateY(60px);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }
  }
}
</style>
