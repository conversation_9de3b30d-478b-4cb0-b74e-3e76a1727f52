<template>
  <div class="container-box">
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.multiLoginAuth_portSetting')"
      :visible.sync="portVisible"
      width="600px"
      @close="close"
    >
      <div>
        <Form ref="form" :rules="rules" :model="form" label-position="right" label-width="0px" size="mini">
          <el-row style="margin-bottom: 10px; display:flex;">
            <div style="color: red">
              <i class="el-icon-error"/>
              {{ $t('pages.pluginConnectionFailed') }}
            </div>
            <div v-if="promptMsg" style="color: #027cfa">
              {{ promptMsg }}
            </div>
          </el-row>
          <el-row style="display: flex">
            <div>
              <i18n path="pages.pluginRunStatusMsg1">
                <common-downloader
                  slot="button"
                  style="margin-left: 0 !important;"
                  :show-button="true"
                  :button-name="$t('pages.downloadPlugin')"
                  :name="filename"
                  button-type="text"
                  button-icon=""
                  @download="downloadPluginPkg"
                />
              </i18n>
            </div>
          </el-row>
          <el-row style="margin-bottom: 10px;">
            <i18n path="pages.pluginRunStatusMsg2">
              <el-tooltip slot="icon" effect="light" placement="top-start" :visible-arrow="false">
                <img slot="content" :src="iconSrc" :alt="iconAlt">
                <img style="width: 18px;position: relative; top: 3px;" :src="iconSrc" :alt="iconAlt">
              </el-tooltip>
            </i18n>
          </el-row>
          <el-row style="margin-bottom: 10px;">
            {{ $t('pages.pluginRunStatusMsg3') }}
          </el-row>
          <el-row style="margin-bottom: 10px;">
            {{ $t('pages.pluginRunStatusMsg4') }}
          </el-row>
          <FormItem prop="port" label-width="90px" :label="$t('pages.pluginPort')">
            <el-row style="align-items: center; display: flex;">
              <el-col :span="10">
                <el-input v-model="form.port" style="width: 100%;"/>
              </el-col>
              <el-col :span="11" style="align-items: center; display: flex;height: 35px; padding: 8px!important; margin-bottom: 0!important;">
                <el-button type="primary" :loading="loading" style="width: 100px; height: 32px; margin-bottom: 0!important;" @click="againConnection">{{ $t('pages.reconnect') }}</el-button>
              </el-col>
            </el-row>
          </FormItem>
        </Form>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import { getSystemResources } from '@/utils/i18n'
import { Level, UTermPluginClient } from '../system/terminalManage/terminal/uterm/client';
import { getPluginVersion, downloadPlugin } from '@/api/system/terminalManage/uterm';
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'PlugPortDlg',
  components: { CommonDownloader },
  props: {
    // 因为组件初始化时，多语言资源还未加载，所以返回的是多语言的key，且在加载多语言后不会更新值
    // 所以修改成显示计算属性的 promptMsg
    promptMessage: { type: String, default() { return undefined } }
  },
  data() {
    return {
      iconSrc: require('@/assets/uterm_plug.png'),
      portVisible: false,
      form: {
        port: UTermPluginClient.getPort()
      },
      defaultForm: {
        port: UTermPluginClient.getPort()
      },
      rules: {
        port: [
          { validator: this.portValidator, trigger: 'blur' }
        ]
      },
      port: UTermPluginClient.getPort(),
      loading: false,
      plug: null,
      latestPluginVer: null //  最新的插件版本号
    }
  },
  computed: {
    // 如果 promptMessage 有传值，则显示 promptMessage，否则显示默认值
    promptMsg() {
      return typeof this.promptMessage === 'string' ? this.promptMessage : this.$t('pages.multiLoginAuth_message1')
    },
    iconAlt() {
      return this.$t('pages.pluginIcon')
    },
    filename() {
      const systemTitle = getSystemResources('pluginName')
      // `${this.systemTitle}插件(V${this.version}).exe`
      return this.$t('pages.dlpPluginName', { systemTitle: systemTitle || '控制台插件', version: this.latestPluginVer || '1.01.230824.SC' })
    }
  },
  created() {
    getPluginVersion().then(ver => {
      this.latestPluginVer = ver
    })
  },
  methods: {
    close() {
      this.portVisible = false
      this.form = Object.assign({}, this.defaultForm)
      this.$refs.form && this.$refs.form.clearValidate()
    },
    show() {
      this.portVisible = true
    },
    sleep(second) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve();
        }, second
        );
      });
    },
    againConnection() {
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          this.loading = true
          this.plug = new UTermPluginClient(this, this.form.port, Level.DEBUG, true);
          try {
            await this.plug.connectedPromise
          } catch (e) {
            this.$notify({
              title: this.$t('text.fail'),
              message: this.$t('pages.multiLoginAuth_message4'),
              type: 'error',
              duration: 2000
            })
            this.loading = false
            return
          }
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.plugInConnectionSuccess'),
            type: 'success',
            duration: 2000
          })
          this.loading = false
          this.portVisible = false
          this.$emit('connectionSuccess', this.plug)
        }
      })
    },
    portValidator(rule, value, callback) {
      const port = value || ''
      if (value === '') {
        callback(new Error(this.$t('pages.multiLoginAuth_portNotNull') + ''))
      }
      //  校验端口号
      const regExp = /^((6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])|[0-5]?\d{0,4})$/g
      const test = port.match(regExp)
      const flag = test != null && test.length > 0
      if (!flag) {
        callback(new Error(this.$t('pages.multiLoginAuth_validPort') + ''))
      } else {
        callback()
      }
    },
    downloadPluginPkg(file) {
      const opts = { file, jwt: true, topic: 'uterm' }
      return downloadPlugin(undefined, opts).then(() => {
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container-box {
  ::v-deep .el-dialog.multiClass{
    height: auto;
    max-height: 80vh;
    overflow-y: auto;
  }
  ::v-deep .el-dialog__body{
    max-height: 70vh!important;
  }
}
</style>
