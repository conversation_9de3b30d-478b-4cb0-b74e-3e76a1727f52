<template>
  <div class="container-box">
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="portVisible"
      width="600px"
      @close="close"
    >
      <div>
        <el-row style="margin-bottom: 10px; display:flex;">
          <div style="color: red">
            <i class="el-icon-error"/>
            {{ $t('pages.pluginConnectionFailed') }}
          </div>
          <div style="color: #027cfa">
            {{ errorTip }}
          </div>
        </el-row>
        <el-row style="display: flex">
          <div>
            <i18n path="pages.pluginUpdateMsg1">
              <common-downloader
                slot="button"
                style="margin-left: 0 !important;"
                :show-button="true"
                :button-name="$t('pages.downloadPlugin')"
                :name="filename"
                button-type="text"
                button-icon=""
                @download="downloadPluginPkg"
              />
              <span slot="operator">{{ operatorMsg }}</span>
            </i18n>
          </div>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSystemResources } from '@/utils/i18n'
import { getPluginVersion, downloadPlugin } from '@/api/system/terminalManage/uterm';
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'PlugUpdateDlg',
  components: { CommonDownloader },
  props: {
    reOperator: { type: String, default() { return this.$t('table.operate') } },
    title: { type: String, default() { return this.$t('pages.plugUpdateDlg') } },
    errorTip: { type: String, default() { return this.$t('pages.multiLoginAuth_message1') } }
  },
  data() {
    return {
      iconSrc: require('@/assets/uterm_plug.png'),
      iconAlt: this.$t('pages.pluginIcon'),
      portVisible: false,
      loading: false,
      plug: null,
      latestPluginVer: null
    }
  },
  computed: {
    filename() {
      const systemTitle = getSystemResources('pluginName')
      // `数据泄露防护系统插件(V${this.version}).exe`
      return this.$t('pages.dlpPluginName', { systemTitle: systemTitle || '控制台插件', version: this.version || '1.01.230824.SC' })
    },
    //  重新操作描述
    operatorMsg() {
      if (this.reOperator) {
        return this.reOperator.toLowerCase()
      }
      return this.$t('table.operate').toLowerCase();
    }
  },
  created() {
    getPluginVersion().then(ver => {
      this.latestPluginVer = ver
    })
  },
  methods: {
    close() {
      this.portVisible = false
    },
    show() {
      this.portVisible = true
    },
    downloadPluginPkg(file) {
      const opts = { file, jwt: true, topic: 'uterm' }
      return downloadPlugin(undefined, opts).then(() => {
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container-box {
  ::v-deep .el-dialog.multiClass{
    height: auto;
    max-height: 80vh;
    overflow-y: auto;
  }
  ::v-deep .el-dialog__body{
    max-height: 70vh!important;
  }
}
</style>
