<template>
  <div class="container-box">
    <el-dialog
      v-el-drag-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :modal="false"
      width="700px"
      custom-class="multiClass"
      @close="close"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('pages.multiLoginAuth_setting') }}
        <el-tooltip effect="dark" placement="top">
          <div slot="content">{{ $t('pages.multiLoginAuth_hit') }}</div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <el-container style="margin: 0; padding: 0; height: 330px">
        <el-aside width="200px" style="height: 330px;">
          <tree-menu
            v-if="dialogVisible"
            ref="groupTree"
            :data="treeData"
            :multiple="true"
            :local-search="true"
            :is-filter="true"
            node-key="dataId"
            :default-checked-keys="defaultCheckedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="handleNodeClick"
            @check-change="checkChange"
          />
        </el-aside>

        <el-main style="height: 330px">
          <Form ref="form" :model="temp" :rules="rules" label-width="120px">
            <div style="margin-bottom: 15px">
              <el-tooltip class="item" :disabled="username.length <= 20" effect="dark" :content="$t('pages.multiLoginAuth_selectedAdmin') + '：' + username" placement="top">
                <el-row style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 100%">
                  {{ $t('pages.multiLoginAuth_selectedAdmin') }}：<span style="font-weight: bolder;">{{ username }}</span>
                </el-row>
              </el-tooltip>
            </div>

            <div v-if="ipEnable" style="margin-bottom: 15px">
              <el-row>
                <el-checkbox v-model="temp.ipEnable">{{ $t('pages.multiLoginAuth_usedIp') }}</el-checkbox>
                <span>（<span style="color: rgb(4, 153, 246)">
                  <i18n path="pages.multiLoginAuth_needClientInstallPlug" class="span-line">
                    <el-button slot="dlpPlug" type="text" @click="downloadDlpPlug">
                      {{ $t('pages.multiLoginAuthDlpPlug') }}
                    </el-button>
                  </i18n>
                </span>
                  ）</span>
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content">
                    <!-- {{ $t('pages.multiLoginAuth_message6') }} -->
                    <i18n path="pages.multiLoginAuth_message6">
                      <br slot="br" />
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-row>
              <FormItem :label="$t('pages.multiLoginAuth_allowIp')" prop="ipAddressList">
                <tag ref="ipAddressTag" v-model="temp.ipAddressList" :err-rule-aligning="true" :input-width="180" :valid-rule="ipAddressListRule" :editable="true" :border="true" :list="temp.ipAddressList"/>
              </FormItem>
            </div>

            <div v-if="macEnable" style="margin-bottom: 15px">
              <el-row>
                <el-checkbox v-model="temp.macEnable">
                  {{ $t('pages.multiLoginAuth_usedMac') }}
                </el-checkbox>
                <span>（<span style="color: rgb(4, 153, 246)">
                  <i18n path="pages.multiLoginAuth_needClientInstallPlug" class="span-line">
                    <el-button slot="dlpPlug" type="text" @click="downloadDlpPlug">
                      {{ $t('pages.multiLoginAuthDlpPlug') }}
                    </el-button>
                  </i18n>
                </span>
                  ）</span>
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content">
                    <!-- {{ $t('pages.multiLoginAuth_message7') }} -->
                    <i18n path="pages.multiLoginAuth_message7">
                      <br slot="br" />
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-row>
              <FormItem :label="$t('pages.multiLoginAuth_allowMac')" prop="macAddressList">
                <tag ref="macAddressTag" v-model="temp.macAddressList" :err-rule-aligning="true" :input-width="200" :editable="true" :border="true" :valid-rule="macAddressRule" :list="temp.macAddressList" :placeholder="$t('pages.multiLoginAuth_macPlaceholder')"/>
              </FormItem>
            </div>

            <div v-if="userKeyEnable" style="margin-bottom: 15px">
              <el-row style="display:flex;">
                <el-checkbox v-model="temp.userKeyEnable">
                  {{ $t('pages.multiLoginAuth_usedUserKey') }}
                </el-checkbox>
                <span>（<span style="color: rgb(4, 153, 246)">
                  <i18n path="pages.multiLoginAuth_needClientInstallPlug" class="span-line">
                    <el-button slot="dlpPlug" type="text" @click="downloadDlpPlug">
                      {{ $t('pages.multiLoginAuthDlpPlug') }}
                    </el-button>
                  </i18n>
                </span>
                  ）</span>
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content">
                    {{ $t('pages.multiLoginAuth_message8') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                <el-button size="mini" style="height: 100%; line-height: 100%;" @click="settingKey">{{ $t('pages.multiLoginAuth_userKeyBtn') }}</el-button>
              </el-row>
              <FormItem label="KEY" prop="userKey">
                <el-input v-model="temp.userKey"/>
              </FormItem>
            </div>

            <div v-if="phoneEnable" style="margin-bottom: 15px">
              <el-row style="display:flex;">
                <el-checkbox v-model="temp.phoneNoteEnable" @change="phoneChange">
                  {{ $t('pages.multiLoginAuth_usedPhone') }}
                </el-checkbox>
                <span>（<span style="color: rgb(4, 153, 246)">{{ $t('pages.multiLoginAuth_needOfferCloudPlatForm') }}</span>）</span>
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content">
                    {{ $t('pages.multiLoginAuth_message9') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-row>
              <FormItem :label="$t('pages.multiLoginAuth_phone')" prop="phone">
                <el-input v-model="temp.phone"/>
              </FormItem>
            </div>

            <div v-if="emailEnable" style="margin-bottom: 15px">
              <el-row>
                <el-checkbox v-model="temp.emailEnable" @change="emailChange">
                  {{ $t('pages.multiLoginAuth_usedEmail') }}
                </el-checkbox>
                <span>（<span style="color: rgb(4, 153, 246)">
                  <i18n path="pages.multiLoginAuth_needConfigEmailServer" class="span-line">
                    <el-button slot="mallServer" type="text" @click="toEmailServer">
                      {{ $t('route.MailServer') }}
                    </el-button>
                  </i18n>
                </span>
                  ）</span>
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content">
                    <!-- {{ $t('pages.multiLoginAuth_message10') }} -->
                    <i18n path="pages.multiLoginAuth_message10">
                      <br slot="br" />
                    </i18n>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-row>
              <FormItem :label="$t('form.email')" prop="email">
                <el-input v-model="temp.email" :maxlength="50"/>
              </FormItem>
            </div>
          </Form>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="confirm">
          {{ $t('pages.multiLoginAuth_saveAndGo') }}
        </el-button>
        <el-button @click="close">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 维护Key -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="'维护Key'"
      :visible.sync="keyVisible"
      width="700px"
      @close="closeKey"
    >
      <div style="height: 400px">
        <div class="toolbar">
          <el-button type="primary" icon="el-icon-upload" size="mini" @click="handleScanKey">
            {{ '扫描Key' }}
          </el-button>
          <el-button icon="el-icon-delete" size="mini" :disabled="!keyDeleteAble" @click="handleDeleteKey">
            {{ $t('button.delete') }}
          </el-button>
        </div>
        <grid-table
          ref="keyTable"
          :col-model="keyColModel"
          :row-datas="keyRowDatas"
          min-height="300"
        />
      </div>
    </el-dialog>

    <!-- 扫描Key -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="'扫描Key'"
      :visible.sync="scanKeyVisible"
      width="300px"
      @close="closeScanKey"
    >
      <Form ref="form" :model="scanKeyTemp" :rules="scanKeyRules" label-width="80px">
        <FormItem label="设备编号">
          <el-input v-model="scanKeyTemp.devCode"/>
        </FormItem>
        <FormItem label="设备昵称">
          <el-input v-model="scanKeyTemp.devName"/>
        </FormItem>
        <FormItem label="绑定管理员">
          <el-select v-mode="scanKeyTemp.bandUser"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="scanKeySubmitting" @click="scanKeySave">
          {{ '保存' }}
        </el-button>
        <el-button @click="closeScanKey">
          {{ '取消' }}
        </el-button>
      </div>
    </el-dialog>

    <usb-plug-downloader ref="downloader"/>
  </div>
</template>

<script>

import {
  getMultiAuthDataBySysUserId,
  settingMultiAuthData
} from '@/api/loginAuth';
import UsbPlugDownloader from '@/views/system/terminalManage/terminal/uterm/downloader'
import { getPluginVersion } from '@/api/system/terminalManage/uterm';
import { isIPv4, isIPv6 } from '@/utils/validate'

export default {
  name: 'LoginAuthDialog',
  components: { UsbPlugDownloader },
  props: {
    ipEnable: { type: Boolean, default: true },
    macEnable: { type: Boolean, default: true },
    //  功能尚未开发
    userKeyEnable: { type: Boolean, default: false },
    //  短信认证q1不上
    phoneEnable: { type: Boolean, default: false },
    emailEnable: { type: Boolean, default: true },
    defaultCheckedKeys: { type: Array, default() { return [] } },
    treeData: { type: Array, default() { return [] } }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      temp: {
      },
      defaultTemp: {
        ipEnable: false,
        ipAddresses: '',
        ipAddressList: [],
        macEnable: false,
        macAddresses: '',
        macAddressList: [],
        userKeyEnable: false,
        userKey: '',
        phoneNoteEnable: false,
        phone: '',
        emailEnable: false,
        email: '',
        sysUserIds: []
      },
      rules: {
        ipAddressList: [
          { validator: this.ipAddressesValidator, trigger: 'blur' }
        ],
        macAddressList: [
          { validator: this.macAddressesValidator, trigger: 'blur' }
        ],
        userKey: [
          { validator: this.userKeyValidator, trigger: 'blur' }
        ],
        phone: [
          { validator: this.phoneMultiAuthValidator, trigger: 'blur' }
        ],
        email: [
          { validator: this.emailValidator, trigger: 'blur' }
        ]
      },
      ipAddressListRule: [{ validator: this.ipAddressValidator, trigger: 'blur' }],
      macAddressRule: [{ validator: this.macAddressValidator, trigger: 'blur' }],
      defaultExpandedKeys: [0],
      // treeData: [{ id: 'G0', dataId: 0, label: this.$t('pages.multiLoginAuth_adminMessage'), children: []
      // }],
      selectedUserNodes: [],    //  选中的用户Id数组，勾选中的用户
      username: this.$t('pages.multiLoginAuth_notSelected'),

      //  维护key
      keyVisible: false,
      keyRowDatas: [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 1 }, { id: 2 }, { id: 3 }, { id: 1 }, { id: 2 }, { id: 3 }, { id: 1 }, { id: 2 }, { id: 3 }],
      keyColModel: [
        { prop: 'devName', label: '设备昵称', width: '100', fixed: true },
        { prop: 'devCode', label: '设备编码', width: '100' },
        { prop: 'bandUser', label: '已绑定管理员', width: '100' }
      ],
      keyDeleteAble: false,

      //  扫描key
      scanKeyVisible: false,
      scanKeyTemp: {},
      defaultScanKeyTemp: {
        devName: '',
        devCode: '',
        bandUser: null
      },
      scanKeyRules: {},
      scanKeySubmitting: false,
      latestPluginVer: null //  最新的插件版本号
    }
  },
  computed: {

  },
  created() {
    getPluginVersion().then(ver => {
      this.latestPluginVer = ver
    })
  },
  methods: {
    show() {
      this.resetTemp();
      this.initData()
      this.clearForm()
      setTimeout(() => {
        this.dialogVisible = true
      }, 200)
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    clearForm() {
      this.$refs['form'] && this.$refs['form'].clearValidate()
    },
    initData() {
      if (this.defaultCheckedKeys.length === 1) {
        const arr = this.treeData[0].children.filter(node => { return this.defaultCheckedKeys.includes(node.dataId) });
        arr.length > 0 && this.handleNodeClick(arr[0])
      } else if (this.defaultCheckedKeys.length === 0) {
        this.treeData[0].children.length > 0 && getMultiAuthDataBySysUserId(this.treeData[0].children[0].userId).then(r => {
          this.temp = this.formatterInitData(r.data)
        })
      }
      if (this.defaultCheckedKeys.length > 0) {
        this.checkChange(this.defaultCheckedKeys, this.treeData[0].children.filter(node => { return this.defaultCheckedKeys.includes(node.dataId) }))
      }
    },
    async confirm() {
      //  校验ip地址和mac地址是否存在输入时未符合格式要求
      const ipAddressValid = await this.$refs.ipAddressTag.validateForm()
      const macAddressValid = await this.$refs.macAddressTag.validateForm()
      if (!ipAddressValid || !macAddressValid) {
        return;
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.selectedUserNodes.length === 0) {
            this.$message({
              message: this.$t('pages.multiLoginAuth_message2'),
              type: 'error',
              duration: 2000
            })
            return;
          }
          this.submitting = true
          const temp = this.formatterConfirmData(this.temp)
          settingMultiAuthData(temp).then(() => {
            this.$message({
              message: this.$t('pages.multiLoginAuth_message5'),
              type: 'success',
              duration: 2000
            })
            this.submitting = false
          }).catch(() => {
            this.submitting = false;
          })
        }
      })
    },
    formatterInitData(temp) {
      temp = Object.assign(JSON.parse(JSON.stringify(this.defaultTemp)), temp)
      temp.ipAddressList = temp.ipAddresses === undefined || temp.ipAddresses === null || temp.ipAddresses === '' ? [] : temp.ipAddresses.split(',')
      temp.macAddressList = temp.macAddresses === undefined || temp.macAddresses === null || temp.macAddresses === '' ? [] : temp.macAddresses.split(',')
      return temp
    },
    formatterConfirmData(temp) {
      temp = JSON.parse(JSON.stringify(temp));
      temp.ipAddresses = temp.ipAddressList.join(',')
      temp.macAddresses = temp.macAddressList.join(',')
      if (this.selectedUserNodes.length === 0) {
        temp.sysUserIds = []
        temp.sysUserIds.push(this.selectedUser.userId)
      } else {
        this.selectedUserNodes.forEach(item => { temp.sysUserIds.push(item.userId) })
      }
      return temp
    },
    close() {
      this.dialogVisible = false
      this.selectedUserNodes = []
      this.username = this.$t('pages.multiLoginAuth_notSelected')
      this.resetTemp()
      this.clearForm()
      this.$refs['ipAddressTag'] && this.$refs['ipAddressTag'].clearInputValue();
      this.$refs['macAddressTag'] && this.$refs['macAddressTag'].clearInputValue();
    },
    handleNodeClick(node) {
      if (node.dataId == 0) {
        this.temp = this.formatterInitData({})
        return;
      }

      //  获取配置信息
      node.userId !== null && getMultiAuthDataBySysUserId(node.userId).then(res => {
        this.temp = this.formatterInitData(res.data)
      })
    },
    checkChange(ids, nodes) {
      this.selectedUserNodes = (nodes || []).filter(node => { return node.dataId != 0 }) || []
      this.username = this.selectedUserNodes.length === 0 ? this.$t('pages.multiLoginAuth_notSelected') : this.selectedUserNodes.map(node => { return node.username }).join(',')
      if (this.selectedUserNodes.length > 1) {
        this.resetTemp()
      }
    },
    phoneChange() {
      if (this.temp.phoneNoteEnable) {
        this.temp.emailEnable = false
      }
    },
    emailChange() {
      if (this.temp.emailEnable) {
        this.temp.phoneNoteEnable = false
      }
    },
    //  校验mac
    validMac(mac) {
      const reg = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
      return reg.test(mac)
    },
    ipAddressValidator(rule, value, callback) {
      if (value === '') {
        callback()
      } else if (!isIPv4(value) && !isIPv6(value)) {
        callback(new Error(this.$t('pages.multiLoginAuth_ipValidatorMessage') + ''))
      } else if (this.temp.ipAddressList.findIndex(item => { return item === value }) > -1) {
        callback(new Error(this.$t('pages.multiLoginAuth_ipExists') + ''));
      } else {
        callback()
      }
    },
    //  校验
    ipAddressesValidator(rule, value, callback) {
      if (this.temp.ipEnable && this.temp.ipAddressList.length === 0) {
        callback(new Error(this.$t('pages.multiLoginAuth_ipAddressNotNull') + ''))
      } else {
        callback()
      }
    },
    macAddressValidator(rule, value, callback) {
      if (value === '') {
        callback()
      } else if (!this.validMac(value)) {
        callback(new Error(this.$t('pages.multiLoginAuth_macValidatorMessage') + ''))
      } else if (this.temp.macAddressList.findIndex(item => { return item.toLowerCase() === value.toLowerCase() }) > -1) {
        callback(new Error(this.$t('pages.multiLoginAuth_macExists') + ''));
      } else {
        callback()
      }
    },
    macAddressesValidator(rule, value, callback) {
      if (this.temp.macEnable && this.temp.macAddressList.length === 0) {
        callback(new Error(this.$t('pages.multiLoginAuth_macAddressNotNull') + ''))
      } else {
        callback()
      }
    },
    userKeyValidator(rule, value, callback) {
      if (this.temp.userKeyEnable && this.temp.userKey.trim() === '') {
        callback(new Error(this.$t('pages.multiLoginAuth_userKeyAddressNotNull') + ''))
      } else {
        callback()
      }
    },
    phoneMultiAuthValidator(rule, value, callback) {
      if (this.temp.phoneNoteEnable && value === '') {
        callback(new Error(this.$t('pages.multiLoginAuth_phoneNotNull') + ''))
      } else if (value && !(/^1[3456789]\d{9}$/.test(value))) {
        callback(new Error(this.$t('pages.multiLoginAuth_phoneValidatorMessage')))
      } else {
        callback()
      }
    },
    emailValidator(rule, value, callback) {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      const reg1 = /^#{1}[a-zA-Z0-9_@.]+#{1}$/
      const len = value.split('#')
      if (this.temp.emailEnable && value === '') {
        callback(new Error(this.$t('pages.multiLoginAuth_emailNotNull') + ''))
      } else if (value && !(reg.test(value) || reg1.test(value) || len.length === 2)) {
        callback(new Error(this.$t('pages.multiLoginAuth_emailValidatorMessage')))
      } else {
        callback()
      }
    },
    settingKey() {
      this.keyVisible = true
    },
    //  维护Key的方法
    handleScanKey() {
      this.scanKeyTemp = Object.assign({}, this.defaultScanKeyTemp)
      this.scanKeyVisible = true
    },
    handleDeleteKey() {

    },
    closeKey() {
      this.keyVisible = false
    },
    //  扫描Key
    scanKeySave() {

    },
    closeScanKey() {
      this.scanKeyVisible = false
    },
    //   跳转到邮箱服务器
    toEmailServer() {
      if (this.hasPermission('A49')) {
        //  tabName的值：resourceManagerTab，需要参照globalConfig中的tab-pane的name值
        this.$router.push('/system/configManage/MailServer')
      } else {
        this.$message({
          message: this.$t('pages.multiLoginAuth_message11'),
          type: 'error',
          duration: 2000
        })
      }
    },
    //  下载DLP插件
    downloadDlpPlug() {
      this.$refs.downloader.show(this.latestPluginVer)
    }
  }
}
</script>

<style lang="scss" scoped>
.container-box {
  // ::v-deep .el-dialog.multiClass{
  //   height: auto;
  //   max-height: 80vh;
  //   overflow-y: auto;
  // }
  // ::v-deep .el-dialog__body{
  //   max-height: 70vh!important;
  // }
  .span-line {
    >>> span {
      border-bottom: 1px solid;
    }
  }
}
</style>
