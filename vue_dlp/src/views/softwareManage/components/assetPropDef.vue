<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.customAttribute')"
    :visible.sync="dialogFormVisible"
    width="800px"
  >
    <data-editor
      append-to-body
      :popover-width="500"
      :updateable="updateable"
      :deletable="deleteable"
      :add-func="createAssetProp"
      :update-func="updateAssetProp"
      :delete-func="deleteAssetProp"
      :cancel-func="cancelAssetProp"
      :before-add="beforeAddAssetProp"
      :before-update="beforeUpdateAssetProp"
    >
      <Form ref="assetPropForm" :model="temp" :rules="rules" label-position="right" label-width="100px" style="margin-left: -10px">
        <FormItem :label="$t('table.parentAsset')" prop="parentId">
          <tree-select
            ref="parentSelectTree"
            v-model="temp.parentId"
            :disabled="temp.id || type == 2"
            :data="treeData"
            :checked-keys="[parentId]"
            node-key="dataId"
            @change="parentSelectChange"
          />
        </FormItem>
        <FormItem :label="$t('table.propName')" prop="name">
          <el-input v-model="temp.name" maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="temp.remark" type="textarea" show-word-limit maxlength="200"/>
        </FormItem>
      </Form>
    </data-editor>
    <div>
      <grid-table
        ref="assetPropTable"
        :row-data-api="rowDataApi"
        :show-pager="false"
        :height="200"
        :col-model="colModel"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">
        {{ $t('button.close') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  createAssetProp,
  deleteAssetProp,
  existAssetInfo,
  getAssetPropPage,
  getFromDataTree,
  updateAssetProp
} from '@/api/assets/assetsConfig/assetsView'

export default {
  name: 'SoftwareTypeDlg',
  props: {
    // 数据类型，1 硬件资产 2 软件资产
    type: {
      type: Number,
      default: 1
    },
    parentId: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'parentId', label: 'parentAsset', width: '100', sort: 'custom', formatter: this.parentIdFormatter },
        { prop: 'name', label: 'propName', width: '100', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '150', sort: 'custom' }
      ],
      rules: {
        name: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      dialogStatus: '',
      dialogFormVisible: false,
      updateable: false,
      deleteable: false,
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        type: this.type,
        parentId: undefined,
        name: '',
        remark: ''
      },
      query: {
        type: this.type,
        parentId: undefined
      },
      submitting: false,
      treeData: []
    }
  },
  computed: {
    assetPropForm() {
      return this.$refs['assetPropForm']
    }
  },
  created() {
    this.resetTemp()
    this.loadAssetTree()
  },
  methods: {
    gridTable() {
      return this.$refs['assetPropTable']
    },
    show() {
      this.dialogFormVisible = true
      if (this.type == 2) {
        this.$set(this.query, 'parentId', this.parentId)
      }
      this.gridTable() && this.gridTable().execRowDataApi(this.query)
    },
    close() {
      this.dialogFormVisible = false
      this.resetTemp()
      this.assetPropForm.resetFields()
      this.gridTable() && this.gridTable().clearSelection()
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      if (this.type == 2) {
        this.$set(this.temp, 'parentId', this.parentId)
      }
    },
    rowDataApi: function(option) {
      if (this.type == 2) {
        this.$set(this.query, 'parentId', this.parentId)
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getAssetPropPage(searchQuery)
    },
    parentSelectChange: function(data, node, vm) {
      this.temp.parentId = data
    },
    handleSelectionChange(rowDatas) {
      this.deleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.updateable = true
      } else {
        this.updateable = false
        this.cancelAssetProp()
      }
    },
    loadAssetTree() {
      this.treeData.splice(0)
      getFromDataTree({ type: this.type, showAll: true }).then(respond => {
        // 自定义查询，资产属性的树数据
        this.colModel[0].treeData = respond.data
        // 自定义属性，父资产属性树数据
        if (respond.data && respond.data.length > 0) {
          respond.data.forEach(item => {
            const assetNode = {
              id: item.id,
              dataId: item.dataId,
              label: item.label
            }
            this.treeData.push(assetNode)
          })
        }
      })
    },
    createAssetProp() {
      let validate
      this.assetPropForm.validate(async(valid) => {
        validate = valid
      })
      return new Promise((resolve, reject) => {
        if (validate) {
          const rowData = Object.assign({}, this.temp)
          resolve(createAssetProp(rowData).then(res => {
            this.gridTable().execRowDataApi()
            this.cancelAssetProp()
            this.$emit('submitEnd', { type: 'create' })
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
            return true
          }).catch(reason => {
            return false
          }))
        } else {
          reject(false)
        }
      })
    },
    updateAssetProp() {
      let validate
      this.assetPropForm.validate(async(valid) => {
        validate = valid
      })
      return new Promise((resolve, reject) => {
        if (validate) {
          const rowData = Object.assign({}, this.temp)
          resolve(updateAssetProp(rowData).then(res => {
            this.gridTable().execRowDataApi()
            this.cancelAssetProp()
            this.$emit('submitEnd', { type: 'update' })
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
            return true
          }).catch(reason => {
            return false
          }))
        } else {
          reject(false)
        }
      })
    },
    deleteData(toDeleteIds) {
      const indexes = []
      const rowDatas = this.gridTable().getDatas()
      toDeleteIds.forEach(id => {
        const index = rowDatas.findIndex(item => item.id === id)
        if (index > -1) {
          indexes.push(rowDatas.length - 1 - index)
        }
      })
      deleteAssetProp({ ids: toDeleteIds.join(',') }).then(() => {
        this.$emit('submitEnd', { type: 'delete', indexes: indexes })
        this.gridTable().execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.deleteSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    deleteAssetProp() {
      const toDeleteIds = this.gridTable().getSelectedIds()
      existAssetInfo({ propIds: toDeleteIds.join(',') }).then(res => {
        if (res.data) {
          this.$confirmBox(this.$t('pages.assetsView_textMap5'), this.$t('text.prompt')).then(() => {
            this.deleteData(toDeleteIds)
          }).catch(() => {})
        } else {
          this.deleteData(toDeleteIds)
        }
      })
    },
    cancelAssetProp() {
      this.gridTable() && this.gridTable().setCurrentRow()
      this.assetPropForm && this.assetPropForm.clearValidate()
      this.resetTemp()
      // this.loadTableColumn()
    },
    beforeAddAssetProp() {
      this.resetTemp()
      this.$refs['assetPropForm'].clearValidate()
    },
    beforeUpdateAssetProp() {
      this.resetTemp()
      Object.assign(this.temp, this.gridTable().getSelectedDatas()[0])
      this.$refs['assetPropForm'].clearValidate()
    },
    parentIdFormatter(row, data) {
      let parentName = this.$t('pages.unknownAsset')
      this.treeData.forEach(node => {
        if (data == node.dataId) {
          parentName = node.label
        }
      })
      return parentName
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
