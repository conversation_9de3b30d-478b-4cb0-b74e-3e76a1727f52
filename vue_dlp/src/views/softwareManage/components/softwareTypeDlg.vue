<template>
  <el-dialog
    v-el-drag-dialog
    :title="dialogTitle"
    :modal="false"
    :close-on-click-modal="false"
    :visible.sync="dialogFormVisible"
    width="550px"
  >
    <Form ref="groupDataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 450px;">
      <FormItem :label="$t('pages.typeName')" prop="name">
        <el-input v-model="temp.name" v-trim :maxlength="20"/>
      </FormItem>
      <FormItem :label="$t('text.remark')" prop="remark">
        <el-input v-model="temp.remark" type="textarea" rows="2" resize="none" maxlength="100" show-word-limit />
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="dialogStatus.indexOf('create') === 0 ? createData() : updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSoftwareGroupByName, createSoftwareGroup, updateSoftwareGroup } from '@/api/softwareManage/assets/assetsView'

export default {
  name: 'SoftwareTypeDlg',
  props: {},
  data() {
    return {
      rules: {
        name: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      dialogStatus: '',
      dialogFormVisible: false,
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        type: undefined,
        name: '',
        remark: ''
      },
      submitting: false
    }
  },
  computed: {
    dialogTitle() {
      const map = {
        create1: this.$t('pages.addSoftwareType'),
        update1: this.$t('pages.editSoftwareType'),
        create2: this.$t('pages.addChargeType'),
        update2: this.$t('pages.editChargeType'),
        create3: this.$t('pages.addIndustryType'),
        update4: this.$t('pages.editIndustryType')
      }
      return map[this.dialogStatus + this.temp.type]
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate(type) {
      this.resetTemp()
      this.temp.type = type
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['groupDataForm'].clearValidate()
      })
    },
    handleUpdate(data) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, data)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['groupDataForm'].clearValidate()
      })
    },
    createData() {
      this.submitting = true
      this.$refs['groupDataForm'].validate((valid) => {
        if (valid) {
          createSoftwareGroup(this.temp).then(res => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$emit('submitEnd', res.data, this.dialogStatus)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['groupDataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateSoftwareGroup(tempData).then(res => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$emit('submitEnd', res.data, this.dialogStatus)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      getSoftwareGroupByName({ type: this.temp.type, name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
