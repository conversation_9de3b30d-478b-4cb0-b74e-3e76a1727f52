<template>
  <div v-if="showButton" class="download-executor">
    <el-button size="mini" @click="handleSyncAsset">
      {{ $t('pages.assetsView_textMap8') }}
      <el-tooltip class="item" effect="dark" placement="top">
        <div slot="content">{{ $t('pages.assetsView_textMap13') }}</div>
        <i class="el-icon-info" />
      </el-tooltip>
    </el-button>

    <el-dialog
      v-el-drag-dialog
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('pages.assetsView_textMap8') }}
        <el-tooltip effect="dark" placement="top">
          <div slot="content">{{ $t('pages.assetsView_textMap13') }}</div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 650px;">
        <FormItem :label="$t('pages.assetsView_textMap9')">
          <el-radio-group v-model="temp.objectType" @change="objectTypeChange">
            <el-radio :label="3">{{ $t('pages.terminalGroup') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.terminal') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.assetsView_textMap10') }}</span>
          </div>
          <div v-if="temp.objectType == 3">
            <tree-menu
              ref="groupTree"
              :data="treeData"
              :local-search="false"
              multiple
              :default-expand-all="false"
              :get-search-list="getSearchListFunction"
              :render-content="renderContent"
              @check-change="handleCheckChange"
            />
          </div>
          <div v-if="temp.objectType == 1" class="table-container" style="height: 300px;">
            <div class="toolbar">
              <div class="searchCon">
                <el-input v-model="query.searchIdAndName" v-trim clearable :placeholder="$t('pages.terminalNameOrNumber')" style="width: 200px;" @keyup.enter.native="handleFilter" />
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <grid-table
              ref="terminalTable"
              :col-model="colModel"
              :row-data-api="rowDataApi"
              :autoload="false"
              :multi-select="multiSelect"
              :is-saved-selected="true"
              @selectionChangeEnd="selectionChangeEnd"
            />
          </div>
        </el-card>
        <span v-if="validMsgVisible" style="color: red;">{{ $t('pages.assetsView_textMap11') }}</span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="syncAsset()">
          {{ $t('pages.handleSync') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { notifySyncAssetInfo } from '@/api/assets/assetsConfig/assetsView'
import { deptNameFormatter, termStatusIconFormatter } from '@/utils/formatter'
import { mapGetters } from 'vuex'
import { getTerminalPage } from '@/api/system/terminalManage/terminal'

export default {
  name: 'SyncAssetBtn',
  components: {},
  props: {
    showButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      multiSelect: true,
      dialogFormVisible: false,
      validMsgVisible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        objectType: 3,
        objectIds: []
      },
      rules: {},
      treeData: [],
      colModel: [
        { prop: 'name', label: 'terminalName', width: '100', fixed: true, sort: 'custom', iconFormatter: termStatusIconFormatter, type: 'showDetail', searchType: 'terminal', searchParam: 'id', labelIndent: 25 },
        { prop: 'groupIds', label: 'terminalGroup', width: '100', formatter: this.groupFormatter, sort: 'custom' },
        { prop: 'computerName', label: 'computerName', width: '100', sort: 'custom' },
        { prop: 'id', label: 'terminalCode', width: '100', sort: 'custom' }
      ],
      // 查询条件
      query: {}, // 查询条件
      defaultQuery: {
        page: 1,
        searchInfo: '',
        searchIdAndName: '',
        name: '',
        types: '0,1,2,3'
      }
    }
  },
  computed: {
    ...mapGetters([
      'logTermTree'
    ])
  },
  watch: {
    'temp.objectType'(val) {
      this.objectTypeChange()
    }
  },
  created() {
    this.initGroupTreeNode()
  },
  activated() {
    this.initGroupTreeNode()
  },
  methods: {
    objectTypeChange() {
      this.initGroupTreeNode()
      this.gridTable() && this.gridTable().execRowDataApi()
    },
    gridTable() {
      return this.$refs['terminalTable']
    },
    selectionChangeEnd: function(rowDatas) {
      this.validMsgVisible = !rowDatas || rowDatas.length <= 0
    },
    // 加载终端分组树结构
    initGroupTreeNode() {
      this.resetQuery()
      if (!this.logTermTree) {
        this.treeData.splice(0)
        return
      }
      const treeData = []
      this.logTermTree.forEach(data => {
        this.formatterSelectedData(data)
        treeData.push(data)
      })
      this.treeData.splice(0, this.treeData.length, ...treeData)
    },
    formatterSelectedData(checkedNode) {
      if (!checkedNode.children || checkedNode.children.length === 0) {
        return
      }
      const children = []
      checkedNode.children.forEach(childNode => {
        if (childNode.dataType === 'G') {
          this.formatterSelectedData(childNode)
          children.push(childNode)
        }
      })
      checkedNode.children = children
    },
    resetTemp() {
      this.submitting = false
      this.validMsgVisible = false
      this.temp = Object.assign({}, this.defaultTemp)
      this.$refs.groupTree && this.$refs.groupTree.clearSelectedNodes()
    },
    handleSyncAsset() {
      this.resetTemp()
      this.initGroupTreeNode()
      this.dialogFormVisible = true
    },
    formValidate() {
      let objectIds = []
      if (this.temp.objectType === 3) {
        this.$refs.groupTree.getCheckedKeys().forEach(key => objectIds.push(key.substring(1)))
      } else {
        objectIds = this.gridTable().getSelectedKeys()
      }
      this.temp.objectIds = objectIds.join(',')
      return objectIds.length > 0
    },
    syncAsset() {
      this.$refs['dataForm'].validate((valid) => {
        this.submitting = true
        if (valid && this.formValidate()) {
          notifySyncAssetInfo(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.assetsView_textMap12'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.validMsgVisible = true
          this.submitting = false
        }
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTerminalPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    getSearchListFunction() {
      return this.$store.getters.deptTreeList || []
    },
    renderContent(h, { node, data, store }) {
      return h('div', { 'class': { 'custom-tree-node': data.type === 'D' }}, [h('span', data.label)])
    },
    handleCheckChange(data, node, vm) {
      this.validMsgVisible = !data || data.length <= 0
    },
    resetQuery() {
      this.query = Object.assign({}, this.defaultQuery)
    },
    groupFormatter: function(row, data, col) {
      return this.html2Escape(deptNameFormatter(row, data, col, this.treeData))
    }
  }
}
</script>

<style lang="scss" scoped>
  .download-executor {
    display: inline-block;
    margin-left: 10px;
    z-index: 1;
    .el-checkbox {
      margin-right: 0;
    }
  }
</style>
