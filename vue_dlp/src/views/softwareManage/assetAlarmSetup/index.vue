<template>
  <div class="app-container">
    <Form
      ref="dataForm"
      :model="temp"
      :hide-required-asterisk="true"
      label-position="right"
      label-width="107px"
      class="asset-alarm"
      style="width: 940px;"
    >
      <el-card class="box-card">
        <!-- <div slot="header">
          <span></span>
        </div> -->
        <el-checkbox v-model="temp.softwareAlarm" :true-label="1" :false-label="0">{{ $t('pages.softwareAssetChangeAlarmSetting') }}</el-checkbox>
        <FormItem :label="`${$t('pages.operateTypeOptions1')}：`" prop="softwareOperateType">
          <el-checkbox-group v-model="temp.softwareOperateTypeList" :disabled="temp.softwareAlarm == 0">
            <el-checkbox v-for="item in operateTypeOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <FormItem :label="`${$t('pages.operateTypeOptions2')}：`" prop="softwareAssetsType">
          <el-checkbox-group v-model="temp.softwareAssetsTypeList" :disabled="temp.softwareAlarm == 0">
            <el-checkbox v-for="item in softwareAssetOption" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <div style="padding-top: 10px">
          <ResponseContent
            class="response_content"
            :status="status"
            :show-select="true"
            read-only
            :editable="temp.softwareAlarm == 1"
            :prop-check-rule="!!temp.isCheck"
            :prop-rule-id="temp.ruleId"
            @getRuleId="getRuleId"
            @ruleIsCheck="ruleIsCheck"
          />
        </div>
      </el-card>
      <el-card class="box-card">
        <el-checkbox v-model="temp.softwareRecord" :true-label="1" :false-label="2">{{ $t('pages.softwareAssetChangeRecordSetting') }}</el-checkbox>
        <FormItem :label="`${$t('pages.operateTypeOptions1')}：`" prop="softwareOperateType2">
          <el-checkbox-group v-model="temp.softwareOperateTypeRecordList" :disabled="temp.softwareRecord != 1">
            <el-checkbox v-for="item in operateTypeOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
        <FormItem :label="`${$t('pages.operateTypeOptions2')}：`" prop="softwareAssetsTypeRecord">
          <el-checkbox-group v-model="temp.softwareAssetsTypeRecordList" :disabled="temp.softwareRecord != 1">
            <el-checkbox v-for="item in softwareAssetOption2" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </FormItem>
      </el-card>
    </Form>
    <div class="save-btn-container">
      <el-button :loading="submitting" type="primary" size="mini" @click="createData()">
        {{ $t('button.save') }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { get, update } from '@/api/softwareManage/assetAlarm/assetAlarmSetup'
import { listPropType } from '@/api/assets/assetsConfig/assetLog'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'

export default {
  name: 'SoftwareAssetAlarmSetup',
  components: { ResponseContent },
  data() {
    return {
      submitting: false,
      operateTypeOptions: [
        { label: this.$t('button.add1'), value: 1 },
        { label: this.$t('button.delete'), value: 2 }
      ],
      softwareAssetOption: [],
      softwareAssetOption2: [],
      temp: {
        softwareAlarm: 0,
        softwareOperateType: 0,
        softwareAssetsType: '',
        softwareOperateTypeList: [],
        softwareAssetsTypeList: [],
        softwareRecord: 1, // 1，开启指定资产类型审计日志记录 2，不开启审计日志记录
        softwareOperateTypeRecord: 3,
        softwareAssetsTypeRecord: '2001|2002|2003|2004',
        softwareOperateTypeRecordList: [],
        softwareAssetsTypeRecordList: [],
        ruleId: null,
        isCheck: 0
      },
      status: ''
    }
  },
  computed: {
  },
  watch: {},
  created() {
    this.getSetting()
    this.getAssetsOptions()
  },
  methods: {
    getSetting() {
      get().then(res => {
        this.temp = Object.assign(this.temp, res.data)
        this.temp.isCheck = !!this.temp.ruleId
        this.temp.softwareOperateTypeList = this.numToList(this.temp.softwareOperateType, 3)
        this.temp.softwareOperateTypeRecordList = this.numToList(this.temp.softwareOperateTypeRecord, 3)
        this.temp.softwareAssetsTypeList = this.splitToNum(this.temp.softwareAssetsType, '|')
        this.temp.softwareAssetsTypeRecordList = this.splitToNum(this.temp.softwareAssetsTypeRecord, '|')
        this.status = 'update'
      })
    },
    createData() {
      const msg = this.validateSettings()
      if (msg) {
        this.$message({
          title: this.$t('text.prompt'),
          message: msg,
          type: 'error',
          duration: 2000
        })
        return;
      }
      this.temp.softwareOperateType = this.getSum(this.temp.softwareOperateTypeList)
      this.temp.softwareOperateTypeRecord = this.getSum(this.temp.softwareOperateTypeRecordList)
      this.temp.softwareAssetsType = this.temp.softwareAssetsTypeList.join('|')
      this.temp.softwareAssetsTypeRecord = this.temp.softwareAssetsTypeRecordList.join('|')
      if (this.temp.isCheck == 1 && this.temp.ruleId == null) return
      this.submitting = true
      update(this.temp).then(res => {
        this.submitting = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.saveSuccess'),
          type: 'success',
          duration: 2000
        })
        this.getSetting()
      })
    },
    getAssetsOptions() {
      listPropType({ type: 2 }).then(res => { // 1硬件2软件
        const options = this.softwareAssetOption
        const options2 = this.softwareAssetOption2
        options.splice(0)
        options2.splice(0)
        res.data.forEach(item => {
          options.push({ label: item.name, value: item.propId })
          options2.push({ label: item.name, value: item.propId })
        })
      })
    },
    // 响应规则选中传值ruleId
    getRuleId(value) {
      this.temp.ruleId = value
    },
    // 响应规则前面的复选框是否选中(0/1)--否/是
    ruleIsCheck(value) {
      this.temp.isCheck = value
    },
    validateSettings() {
      if (this.temp.softwareAlarm === 1) {
        if (this.temp.softwareOperateTypeList.length === 0) {
          return this.$t('pages.saveAlarmSettingTip', { info: this.$t('pages.operateTypeOptions1') })
        }
        if (this.temp.softwareAssetsTypeList.length === 0) {
          return this.$t('pages.saveAlarmSettingTip', { info: this.$t('pages.operateTypeOptions2') })
        }
      }
      if (this.temp.softwareRecord === 1) {
        if (this.temp.softwareOperateTypeRecordList.length === 0) {
          return this.$t('pages.saveRecordSettingTip', { info: this.$t('pages.operateTypeOptions1') })
        }
        if (this.temp.softwareAssetsTypeRecordList.length === 0) {
          return this.$t('pages.saveRecordSettingTip', { info: this.$t('pages.operateTypeOptions2') })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .app-container{
    overflow: auto;
  }
  >>>.el-form-item{
    margin:0px;
  }
  >>>.el-form-item__label{
    color: #ccc;
  }
  .box-card {
    margin-top: 20px;
    background-color: transparent;
  }
  .asset-alarm{
    >>>.el-card__header{
      padding: 0;
    }
    >>>.el-card__body{
      padding-top: 10px;
      padding-bottom: 10px;
    }
    >>>.el-checkbox{
      width: 165px;
    }
    .response_content{
      >>>.el-checkbox{
        width: 450px;
      }
      >>>.el-checkbox:nth-child(2){
        width: 160px;
      }
    }
  }
  .save-btn-container{
    width: 940px;
    margin-top: 10px;
    text-align: right;
  }
  >>>.el-radio__input.is-disabled+span.el-radio__label{
    color: #888;
  }
</style>
