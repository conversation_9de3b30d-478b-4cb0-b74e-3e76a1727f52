<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :height="600"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px">
        <FormItem :label="$t('pages.softwareName')" prop="softwareName">
          <el-input v-model="temp.softwareName" maxlength="128"></el-input>
          <el-tooltip slot="tooltip" effect="dark" placement="top">
            <div slot="content" style="line-height: 20px">
              <div v-for="(tip, index) in softwareNameAndVersionTips" :key="index">{{ tip }}</div>
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
        </FormItem>
        <FormItem v-if="matchTypeVisible" :label="$t('pages.matchType')" prop="matchType">
          <el-select v-model="temp.matchType">
            <el-option :value="0" :label="$t('pages.matchTypeOptions2')"/>
            <el-option :value="1" :label="$t('pages.matchTypeOptions1')"/>
          </el-select>
        </FormItem>
        <el-card class="box-card" :body-style="{'padding': '10px'}" style="height: calc(100% - 100px);">
          <div slot="header">
            <span>{{ $t('pages.software_Msg40') }}</span>
            <el-popover
              v-model="visible"
              :disabled="!formable"
              style="margin-left: 20px"
              placement="right"
              width="400"
              trigger="click"
            >
              <tree-menu
                ref="softwareAssetVersionList"
                :data="softwareAssetVersionList"
                multiple
                :is-filter="false"
                :default-expand-all="true"
                :render-content="renderContent"
                style="height: 350px"
              />
              <div style="text-align: right; margin-top: 10px">
                <el-button v-show="softwareAssetVersionList.length > 0" type="primary" size="mini" @click="handleCheckedVersion">{{ $t('button.confirm') }}</el-button>
                <el-button size="mini" @click="visible = false">{{ $t('button.cancel') }}</el-button>
              </div>
              <el-button slot="reference" size="small" :disabled="!temp.softwareName" @click="scanSoftwareVersions">
                {{ $t('pages.software_Msg38') }}
                <el-tooltip effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.softwareNameGetInstalledVersionNumber') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-button>
            </el-popover>
            <el-button type="primary" size="small" @click="fullVersion">{{ $t('pages.software_Msg39') }}</el-button>
          </div>
          <FormItem prop="versions" label-width="0px">
            <tag v-model="temp.versions" :list="temp.versions" :add-click="addClick" @tagChange="closeChange"/>
          </FormItem>
        </el-card>
        <div v-if="processVisible">
          <el-card class="box-card" :body-style="{'padding': '10px'}" style="height: calc(100% - 100px); border: 1px solid #878787;" >
            <div slot="header" class="clearfix">
              <span>{{ $t('pages.software_Msg13') }}</span>
              <el-button size="small" @click="handleProcessCreate">
                {{ $t('button.insert') }}
              </el-button>
              <el-button size="small" @click="handleImportAppFromSelfLib">
                {{ $t('pages.softAssetGroupImport') }}
                <el-tooltip class="item" effect="dark" :content="$t('pages.software_Msg15')" placement="bottom-start">
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-button>
              <el-button size="small" :disabled="!deleteable" @click="handleProcessDelete">
                {{ $t('button.delete') }}
              </el-button>
            </div>
            <div>
              <grid-table
                ref="processGrid"
                :show-pager="false"
                :height="240"
                :col-model="colModel"
                :row-datas="temp.appInfos"
                @selectionChangeEnd="processSelectionChangeEnd"
              />
            </div>
          </el-card>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="submitEnd">
          {{ $t('pages.addSoftware') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <app-import-table
      ref="appImportTable"
      :os-type="osType"
      :add-group-btn="false"
      :type-tree-data="typeTreeData"
      :group-root-name="$t('pages.softAssetGroup')"
      :list="getSoftInfoPage"
      :count-by-group="countInfoByGroupId"
      :create="createSoftInfo"
      :batch-create="batchAddSoftInfo"
      :update="updateSoftInfo"
      :delete="deleteSoftInfo"
      :import-func="importSoftInfoFromLib"
      :create-group="createSoftType"
      :update-group="updateSoftType"
      :delete-group="deleteSoftType"
      :get-group-by-name="getSoftTypeByName"
      support-select-terminal-soft
      @submitEnd="appMd5ImportSubmitEnd"
    />

  </div>
</template>

<script>
import {
  createSoftType, updateSoftType, deleteSoftType, getSoftTypeByName, createSoftInfo, getSoftTypeTreeNode,
  batchAddSoftInfo, updateSoftInfo, deleteSoftInfo, importSoftInfoFromLib, getSoftInfoPage, countInfoByGroupId
} from '@/api/softwareManage/assets/assetsView'
import { listVersion, getVersionMap } from '@/api/softwareManage/assets/assetsView'
import AppImportTable from '@/views/system/baseData/appLibrary/appImportTableDlg'
import { objectFormatter, entityLink, buttonFormatter } from '@/utils'

export default {
  name: 'SoftwareDlg',
  components: { AppImportTable },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    showPath: { type: Boolean, default: true } // 是否展示安装路径配置项
  },
  data() {
    return {
      softwareNameAndVersionTips: [],
      multiSelect: true,
      colModel: [
        { prop: 'processName', label: 'processName', width: '150', sort: true, formatter: this.nameFormatter },
        { prop: 'isCheckMd5', label: 'checkMd5Label', width: '150', sort: true, formatter: this.md5LevelFormatter }
      ],
      rules: {
        softwareName: [{ required: true, message: this.$t('pages.softwareOrder_text11'), trigger: 'blur' }],
        versions: [{ validator: this.versionsValidator, trigger: 'blur' }]
      },
      temp: {}, // 表单字段
      defaultTemp: {
        softwareName: '',
        versions: [this.$t('pages.software_Msg39')], // ['全版本']
        matchType: 0,
        appInfos: []
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      submitting: false,
      source: null,
      checkedEntityNode: {},
      installPath: '',
      dialogFormVisible: false,
      submitFlag: true,
      validate: false,
      textMap: {
        create: this.$t('pages.addSoftware'),
        update: this.$t('pages.editSoftware')
      },
      dialogStatus: '',
      osType: 1, // 进程系统类型：1-windows，2-linux，4-mac
      typeTreeDataWindow: [],
      typeTreeDataLinux: [],
      typeTreeDataMac: [],
      visible: false,
      softwareAssetVersionList: [],
      processVisible: true,
      matchTypeVisible: true,
      editSoftwareName: '', // 用于缓存正在修改的软件的原名称
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      }
    }
  },
  computed: {
    typeTreeData() {
      if (this.osType == 1) {
        return this.typeTreeDataWindow
      } else if (this.osType == 2) {
        return this.typeTreeDataLinux
      } else if (this.osType == 4) {
        return this.typeTreeDataMac
      }
      return null
    }
  },
  created() {
    for (let i = 0; i < 5; i++) {
      this.softwareNameAndVersionTips.push(this.$t('pages.softwareNameAndVersionTip' + i))
    }
    this.loadAppTypeTreeTree()
  },
  activated() {},
  methods: {
    createSoftInfo,
    batchAddSoftInfo,
    updateSoftInfo,
    deleteSoftInfo,
    importSoftInfoFromLib,
    getSoftInfoPage,
    countInfoByGroupId,
    createSoftType,
    updateSoftType,
    deleteSoftType,
    getSoftTypeByName,
    loadAppTypeTreeTree: function() {
      getSoftTypeTreeNode({ osType: 1 }).then(respond => {
        this.typeTreeDataWindow = respond.data
      })
      getSoftTypeTreeNode({ osType: 2 }).then(respond => {
        this.typeTreeDataLinux = respond.data
      })
      getSoftTypeTreeNode({ osType: 4 }).then(respond => {
        this.typeTreeDataMac = respond.data
      })
    },
    appAddSubmitEnd(data, dlgStatus) {
      if (data.innerAdd) {
        return
      }
      this.temp.appInfos.unshift(data)
    },
    appMd5ImportSubmitEnd(data, editMode) {
      // editMode编辑模式，0-删除，1-添加程序
      if (editMode === 0) {
        // 创建待删除键的集合
        const deleteKeys = new Set(data.map(({ id }) => `${id}`));
        // 使用filter一次性过滤
        this.temp.appInfos = this.temp.appInfos.filter(({ id }) =>
          !deleteKeys.has(`${id}`)
        );
      } else if (editMode === 1) {
        const length = data.length + this.temp.appInfos.length
        if (data && !data[0].innerAdd && length > 10) {
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('pages.softwareProcessAssociationFailedTips'),
            type: 'warning',
            duration: 2000
          })
        } else {
          // 创建一个 Map 用于存储列表中的元素，以方便快速查找
          const listMap = new Map();
          this.temp.appInfos.forEach((item) => {
            const key1 = `${item.id}`;
            const key2 = `${item.processName}-${item.fileMd5}-${item.checkMd5}`;
            listMap.set(key1, item);
            listMap.set(key2, item);
          });
          data.forEach(item => {
            const key1 = `${item.id}`;
            const key2 = `${item.processName}-${item.fileMd5}-${item.checkMd5}`;
            const existApp = listMap.get(key1) || listMap.get(key2);
            if (!existApp) {
              this.appAddSubmitEnd(item)
            } else {
              const { md5Level, checkMd5, typeId, relaFingerPrint } = item
              Object.assign(existApp, { md5Level, checkMd5, typeId, relaFingerPrint })
            }
          })
        }
      }
    },
    handleProcessCreate() {
      this.$refs.appImportTable.showBatchCreate()
    },
    handleImportAppFromSelfLib() {
      this.$refs.appImportTable.show()
    },
    handleProcessDelete() {
      let msg = this.$t('pages.software_Msg19')
      const rows = this.$refs['processGrid'].getSelectedDatas()
      const allDatas = this.$refs['processGrid'].getDatas()
      if (rows.length === allDatas.length) {
        msg = this.$t('pages.software_Msg37')
      }
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        const rows = this.$refs['processGrid'].getSelectedDatas()
        // 创建待删除键的集合
        const deleteKeys = new Set(rows.map(({ id, processType }) => `${id}-${processType}`));
        // 使用filter一次性过滤
        this.temp.appInfos = this.temp.appInfos.filter(({ id, processType }) =>
          !deleteKeys.has(`${id}-${processType}`)
        );
      }).catch(() => {})
    },
    processSelectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCheckedVersion() {
      const checkNodes = this.$refs.softwareAssetVersionList.getCheckedNodes()
      if (checkNodes && checkNodes.length > 0) {
        const versions = this.temp.versions
        const checkVersions = checkNodes.map(node => node.version)
        // 当 versions 只有一个元素，且为 全版本
        const fullVersion = versions.length === 1 && versions[0] === this.$t('pages.software_Msg39')
        fullVersion && versions.splice(0)
        // 合并、去重
        versions.splice(0, versions.length, ...Array.from(new Set([...versions, ...checkVersions])))
      }
      this.visible = false
    },
    renderContent(h, { node, data, store }) {
      return h('div', { class: 'custom-tree-node' }, [
        h('div', { class: 'ellipsis', style: 'width: 175px; padding-right: 10px;' }, [
          h('span', { attrs: { title: data.softName }}, data.softName)
        ]),
        h('div', { class: 'ellipsis', style: 'width: 120px;' }, [
          h('span', { attrs: { title: data.version }}, data.version)
        ])
      ])
    },
    scanSoftwareVersions() {
      this.$refs.softwareAssetVersionList && this.$refs.softwareAssetVersionList.clearSelectedNodes()
      this.softwareAssetVersionList.splice(0)
      if (this.matchTypeVisible && this.temp.matchType == 1) {
        setTimeout(() => {
          getVersionMap({ softName: this.temp.softwareName }).then(res => {
            if (res.data) {
              // res.data 结构： { name1: [v1,v2,...], name2: [v1,v2,...], name3: [v1,v2,...] }
              // Object.entries: 将 res.data 对象转换为键值对数组。flatMap：对每个键值对进行处理，将结果展平为一个一维数组。
              let currentId = 1;
              this.softwareAssetVersionList = Object.entries(res.data).sort().flatMap(([softName, versions]) => {
                // versions 是软件的版本号数组，排序后返回
                return versions.sort().map((version) => {
                  const id = currentId++;
                  return { id, softName, version };
                });
              });
            }
          })
        }, 100);
      } else {
        listVersion({ softName: this.temp.softwareName }).then(res => {
          if (res.data && res.data.length > 0) {
            res.data.forEach((item, index) => {
              if (item) {
                const verNode = {
                  id: index + 1,
                  softName: this.temp.softwareName,
                  version: item
                }
                this.softwareAssetVersionList.push(verNode)
              }
            })
          }
        })
      }
    },
    fullVersion() {
      this.temp.versions = [this.$t('pages.software_Msg39')] // ['全版本']
    },
    addClick() {
      if (this.temp.versions.length === 1 && this.temp.versions[0] === this.$t('pages.software_Msg39')) {
        this.temp.versions.splice(0, 1)
      }
    },
    closeChange(list) {
      if (list.length === 0) {
        this.fullVersion()
      }
    },
    formatSubmitData() {
      const selectedDatas = this.gridTable().getSelectedDatas()
      selectedDatas.forEach(data => {
        data.path = this.installPath
        if (this.validate && (!data.appInfos || data.appInfos.length == 0)) {
          this.submitFlag = false
        }
      })
      return selectedDatas
    },
    validateProcess() {
      let valid = true
      if (this.processVisible) {
        const processData = this.$refs['processGrid'].getDatas()
        if (!processData || processData.length === 0) {
          valid = false
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.pleaseAddAnAssociatedProcess'),
            type: 'warning',
            duration: 2000
          })
        }
      }
      return valid
    },
    submitEnd() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validateProcess()) {
          this.submitting = false
          this.dialogFormVisible = false
          this.$emit('submitEnd', this.temp, this.editSoftwareName)
        } else {
          this.submitting = false
        }
      })
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    handleCreate(type) {
      this.resetTemp()
      this.editSoftwareName = ''
      this.processVisible = type != 'reqInstall' // 必须安装软件策略生效不需要软件进程信息
      this.matchTypeVisible = type == 'limit' // 匹配类型目前仅对软件黑白名单策略生效
      this.dialogFormVisible = true
      this.dialogStatus = 'create'
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row, type) {
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      if (this.temp.versions.length === 1 && this.temp.versions[0] === '*.*') {
        this.temp.versions = [this.$t('pages.software_Msg39')] // ['全版本']
      }
      this.editSoftwareName = this.temp.softwareName
      this.processVisible = type != 'reqInstall' // 必须安装软件策略生效不需要软件进程信息
      this.matchTypeVisible = type == 'limit' // 匹配类型目前仅对软件黑白名单策略生效
      this.dialogFormVisible = true
      this.dialogStatus = 'update'
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    appInfoFormatter: function(row, data) {
      const processNames = row.appInfos ? row.appInfos.map(app => app.processName) : []
      return processNames.join(',')
    },
    nameFormatter(row, data) {
      let msg = ''
      if (row.itemType == 2) {
        this.typeTreeData.some(node => {
          if (node.dataId == row.id) {
            msg = node.label
            return true
          }
        })
      } else {
        msg = row.processName
      }
      return msg
    },
    md5LevelFormatter(row, data) {
      if (row.checkMd5 === 0) {
        return this.$t('pages.software_Msg20')
      }
      return this.md5LevelMap[row.md5Level] || this.md5LevelMap[row.relaFingerPrint[0].checkMd5]
    },
    softwareNameFormatter: function(row, data) {
      return row.softwareName
    },
    softwareView(row, data) {
      this.$router.push({ name: 'SoftwareDetail', params: { softwareInfo: row, version: row.softwareVersion }})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    limitTypeFormatter: function(row, data) {
      for (let i = 0, size = this.limitTypeOptions.length; i < size; i++) {
        const id = this.limitTypeOptions[i].value
        if (id === data || id.toString() === data) {
          return this.limitTypeOptions[i].label
        }
      }
      return ''
    },
    versionsValidator(rule, value, callback) {
      if (value && value.length > 20) {
        callback(this.$t('pages.software_Msg47'))
      } else {
        callback()
      }
    }
  }
}
</script>
