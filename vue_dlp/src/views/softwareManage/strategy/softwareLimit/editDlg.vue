<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.softwareLimit')"
      :active-able="activeAble"
      :stg-code="152"
      :time-able="true"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <div style="padding-left: 30px;">
          <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
          <LocalZoomIn parent-tag="el-dialog">
            <div v-if="formable" class="toolbar">
              <el-button size="small" @click="handleAddSoftware">
                {{ $t('button.insert') }}
              </el-button>
              <el-button size="small" @click="handleSoftware">
                {{ $t('pages.softwareAssetImport') }}
              </el-button>
              <el-button size="small" :disabled="!checkSoftwareDeleteable" @click="handleDelete">
                {{ $t('button.delete') }}
              </el-button>
              <el-button type="primary" icon="el-icon-search" size="mini" style="margin-left: 5px;float: right;" @click="handleFilter">{{ $t('table.search') }}</el-button>
              <el-input v-model="searchInfo" v-trim clearable maxlength="255" :placeholder="$t('pages.inputSoftName')" style="width: 200px;float: right;" @keyup.enter.native="handleFilter"></el-input>
            </div>
            <grid-table
              ref="checkedAppGrid"
              row-key="softwareName"
              :show-pager="false"
              auto-height
              :multi-select="formable"
              :col-model="checkedColModel"
              :row-datas="newTemp.stgRelatedSoftware"
              @selectionChangeEnd="checkSoftwareSelectionChangeEnd"
            />
          </LocalZoomIn>
          <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
          <div v-for="limitTypeOpt in limitTypeOptions" :key="limitTypeOpt.value">
            <el-radio v-model="temp.limitType" :label="limitTypeOpt.value" :disabled="!formable" @change="handleRadio">
              {{ limitTypeOpt.label }}
            </el-radio>
          </div>
          <label v-if="(temp.limitType == 2) && (limitTypeOptions[1].violations.length > 0)" style="font-weight: 500;color: #409eff;font-size: small;color:red;">{{ $t('pages.software_Msg') }}</label>
          <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
          <p v-show="validMsg.length > 0" style="margin-left: 17px;"> <span style="color:red">{{ validMsg }}</span></p>
          <div>
            <el-checkbox-group
              v-model="limitTypeOptions[temp.limitType - 1].violations"
              class="selectionCheckBox"
              style="margin-left: 17px;"
              @change="violationChange"
            >
              <el-checkbox v-for="item in violationOptions" v-show="item.value > 0" :key="item.value" :disabled="!formable" :label="item.value">
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <ResponseContent
            ref="resContent"
            :select-style="{ 'margin-top': '10px' }"
            :show-select="true"
            :editable="formable"
            read-only
            :prop-check-rule="getLimitTypeOpt().propCheckRule"
            :show-check-rule="true"
            :prop-rule-id="temp.ruleId"
            @getRuleId="getRuleId"
            @ruleIsCheck="ruleIsCheck"
            @validate="(val) => { responseValidate = val }"
          />
        </div>
      </template>
    </stg-dialog>

    <software-assert-table ref="softwareTable" @submitEnd="softwareImportSubmitEnd"></software-assert-table>

    <software-dlg ref="softwareDlg" @submitEnd="submitSoftwareEnd"/>

  </div>
</template>

<script>
import {
  batchCreateAppInfo,
  countInfoByGroupId,
  createAppInfo,
  createAppType,
  deleteAppInfo,
  deleteAppType,
  getAppInfoList,
  getAppTypeByName,
  getTreeNode,
  importFromLib,
  updateAppInfo,
  updateAppType
} from '@/api/system/baseData/appLibrary'
import {
  createStrategy,
  getStrategyById,
  getStrategyByName,
  updateStrategy
} from '@/api/softwareManage/strategy/softwareLimit'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import SoftwareAssertTable from '@/views/softwareManage/strategy/softwareAssertTable'
import SoftwareDlg from '@/views/softwareManage/strategy/softwareDlg'

export default {
  name: 'SoftwareBlockDlg',
  components: { SoftwareDlg, SoftwareAssertTable, ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      newTemp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        limitType: 1,
        active: false,
        remark: '',
        timeId: 1,
        classId: '',
        entityType: '',
        entityId: undefined,
        ruleId: null,
        violation: 0,
        stgRelatedSoftware: [],
        action: 0
      },
      defaultTempS: {
        softwareName: '',
        softwareVersion: '',
        appInfos: []
      },
      checkSoftware: [],
      checkedColModel: [
        { prop: 'softwareName', label: 'softwareName', sort: true, width: '120' },
        { prop: 'versions', label: 'versionNumber', width: '120', sort: true, formatter: this.versionsFormatter },
        { prop: 'processName', label: 'processName', width: '120', sort: true, formatter: this.processNameFormatter },
        { prop: 'matchType', label: 'matchType', width: '120', sort: true, formatter: this.matchTypeFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleUpdateSoftware }
          ]
        }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      limitTypeOptions: [
        { value: 1, label: this.$t('pages.software_Msg1_1'), violations: [], propCheckRule: false },
        { value: 2, label: this.$t('pages.software_Msg2_1'), violations: [], propCheckRule: false }
      ],
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      propRuleId: undefined,
      ruleChecked: false,
      checkSoftwareDeleteable: false,
      typeTreeData: [],
      violationOptions: [
        { value: 1, label: this.$t('pages.software_Msg3') },
        { value: 2, label: this.$t('pages.software_Msg4') },
        { value: 4, label: this.$t('pages.software_Msg5') }
      ],
      validMsg: '',
      searchInfo: '',
      oldViolations: [],
      matchTypeOptions: {
        0: this.$t('pages.matchTypeOptions2'),
        1: this.$t('pages.matchTypeOptions1')
      }
    }
  },
  computed: {

  },
  created() {
    this.loadAppTypeTreeTree()
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    createAppInfo,
    updateAppInfo,
    deleteAppInfo,
    importFromLib,
    getAppInfoList,
    countInfoByGroupId,
    createAppType,
    updateAppType,
    deleteAppType,
    getAppTypeByName,
    batchCreateAppInfo,
    getLimitTypeOpt() {
      const opts = this.limitTypeOptions.filter(opt => this.temp.limitType === opt.value)
      return opts.length > 0 ? opts[0] : {}
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
      const limitTypeOpt = this.getLimitTypeOpt()
      if (this.temp.ruleId) {
        limitTypeOpt.propCheckRule = true
        this.propRuleId = this.temp.ruleId
      }
      const violations = limitTypeOpt.violations
      if (violations) {
        violations.length = 0
        if (this.temp.violation & 1) {
          violations.push(1)
          this.oldViolations.push(1)
        }
        if (this.temp.violation & 2) {
          violations.push(2)
          this.oldViolations.push(2)
        }
        if (this.temp.violation & 4) {
          violations.push(4)
          this.oldViolations.push(4)
        }
      }
    },
    handleFilter() {
      this.newTemp = {}
      const tempArr = [...this.temp.stgRelatedSoftware]
      this.newTemp = { ...this.temp, stgRelatedSoftware: tempArr }
      var regex = /^\s+$/
      if (regex.test(this.searchInfo) || this.searchInfo == '') {
        this.$refs['checkedAppGrid'].execRowDataApi()
        return
      }
      var colK = 0;
      var existSearch = false
      for (var i = 0; i < this.newTemp.stgRelatedSoftware.length; i++) {
        if (this.newTemp.stgRelatedSoftware[i].softwareName.toLowerCase().includes(this.searchInfo.toLowerCase())) {
          this.newTemp.stgRelatedSoftware[colK] = this.newTemp.stgRelatedSoftware[i]
          colK++
          if (!existSearch) {
            existSearch = true
          }
        }
      }
      if (existSearch) {
        // 移除多余的数据
        this.newTemp.stgRelatedSoftware.splice(colK, this.newTemp.stgRelatedSoftware.length - colK)
      } else {
        this.newTemp.stgRelatedSoftware = []
      }
    },
    closed() {
      this.searchInfo = ''
      this.newTemp = {}
      this.resetTemp()
    },
    appGridTable() {
      return this.$refs['checkedAppGrid']
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    checkSoftwareSelectionChangeEnd(rowDatas) {
      this.checkSoftwareDeleteable = rowDatas && rowDatas.length > 0
    },
    loadAppTypeTreeTree: function() {
      getTreeNode().then(respond => {
        this.typeTreeData = respond.data
      })
    },
    limitTypeChange: function(data) {
      const rowDatas = this.appGridTable().getDatas()
      if (data === 1 && rowDatas && rowDatas.length > 0) {
        this.$confirmBox(this.$t('pages.software_Msg6'), this.$t('text.prompt')).then(() => {
          // 创建待删除键的集合
          const deleteKeys = new Set(rowDatas.filter(({ appInfos }) => (!appInfos || appInfos.length == 0)).map(item => item.id));
          // 使用filter一次性过滤
          this.temp.checkSoftware = this.temp.checkSoftware.filter(({ id }) =>
            !deleteKeys.has(id)
          );
        }).catch(() => {})
      }
    },
    handleAddSoftware() {
      this.$refs.softwareDlg.handleCreate('limit')
    },
    buttonFormatter(row) {
      return !this.formable
    },
    handleUpdateSoftware(row) {
      this.$refs.softwareDlg.handleUpdate(row, 'limit')
    },
    handleSoftware() {
      this.$refs.softwareTable.show(true)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.software_Msg7'), this.$t('text.prompt')).then(() => {
        const rows = this.appGridTable().getSelectedDatas()
        // 创建待删除键的集合
        const deleteKeys = new Set(rows.map(({ softwareName }) => `${softwareName}`));
        // 使用filter一次性过滤
        this.temp.stgRelatedSoftware = this.temp.stgRelatedSoftware.filter(({ softwareName }) =>
          !deleteKeys.has(`${softwareName}`)
        );
        this.handleFilter()
      }).catch(() => {})
    },
    violationChange(val) {
      let violation = 0
      let isMutex = false
      const newVal = [...val.splice(0)]
      const toOldVal = []
      for (let i = newVal.length - 1; i >= 0; i--) {
        const v = newVal[i]
        if (isMutex && v !== 1) continue
        if (this.oldViolations.indexOf(v) < 0 && v !== 1) isMutex = true
        val.unshift(v)
        toOldVal.push(v)
        violation += v
      }
      this.oldViolations = toOldVal
      this.temp.action = violation > 0 ? 1 : 0;
      this.temp.violation = violation
      if (isMutex && this.temp.limitType === 2) {
        this.$confirmBox(this.$t('pages.software_Msg36'), this.$t('text.prompt'), {
          showClose: false,
          showCancelButton: false
        })
      }
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      // this.checkSoftware = []
      this.propRuleId = undefined
      this.validMsg = ''
      this.limitTypeOptions.forEach(opt => {
        opt.violations.splice(0)
        opt.propCheckRule = false
      })
    },
    resetTempS() {
      return JSON.parse(JSON.stringify(this.defaultTempS))
    },
    handleCreate() {
      this.resetTemp()
      this.ruleChecked = false
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      }, undefined, () => {
        this.$nextTick(() => { // 新增弹框打开时，清除响应规则的选中情况
          this.$refs['resContent'].ruleId = undefined
          this.temp.ruleId = null
        })
      })
    },
    async handleUpdate(row) {
      this.resetTemp()
      if (!this.formable) { // 策略总览查看时，数据未格式化，需要重新从后台查询
        await getStrategyById(row.id).then(response => {
          this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(response.data))) // copy obj
        })
      } else {
        this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      }
      this.propRuleId = row.ruleId
      if (this.propRuleId) {
        this.ruleChecked = true
      } else {
        this.ruleChecked = false
      }
      const tempArr = [...this.temp.stgRelatedSoftware]
      this.newTemp = { ...this.temp, stgRelatedSoftware: tempArr }
      this.$refs['stgDlg'].show(row, this.formable)
      // this.$nextTick(() => {
      //   this.appGridTable().clearSelection()
      // })
    },
    propCheckRuleChange(val) {
      if (!val) {
        this.propRuleId = undefined
        this.temp.ruleId = null
        this.ruleChecked = false
      } else {
        this.ruleChecked = true
      }
    },
    formatRowData(rowData) {

    },
    formatFormData(formData) {
    },
    validateFormData(formData) {
      if (this.temp.stgRelatedSoftware.length > 2500) {
        this.$notify({
          title: this.$t('text.error'),
          message: this.$t('pages.software_Msg49'),
          type: 'success',
          duration: 2000
        })
      } else {
        const limitTypeOpt = this.getLimitTypeOpt()
        this.validMsg = ''
        if (limitTypeOpt.violations.length === 0 && !limitTypeOpt.propCheckRule) {
          this.validMsg = this.$t('pages.install_Msg51')
        }
        return this.validMsg.length === 0 && this.responseValidate
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    submitSoftwareEnd(data, oldSoftwareName) {
      if (!oldSoftwareName) {
        if (this.temp.stgRelatedSoftware && this.temp.stgRelatedSoftware.length > 0) {
          const theIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
            return existApp.softwareName.toLowerCase() == data.softwareName.toLowerCase()
          })
          if (theIndex > -1) {
            this.$confirmBox(this.$t('pages.software_Msg43'), this.$t('text.prompt')).then(() => {
              this.temp.stgRelatedSoftware.splice(theIndex, 1, data)
            }).catch(() => {})
          } else {
            this.temp.stgRelatedSoftware.push(data)
          }
        } else {
          this.temp.stgRelatedSoftware.push(data)
        }
      } else {
        const existIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
          return existApp.softwareName.toLowerCase() == data.softwareName.toLowerCase()
        })
        if (data.softwareName.toLowerCase() != oldSoftwareName.toLowerCase()) { //
          if (existIndex > -1) {
            this.$confirmBox(this.$t('pages.software_Msg44'), this.$t('text.prompt')).then(() => {
              this.temp.stgRelatedSoftware.splice(existIndex, 1, data)
              const theIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
                return existApp.softwareName.toLowerCase() == oldSoftwareName.toLowerCase()
              })
              this.temp.stgRelatedSoftware.splice(theIndex, 1)
            }).catch(() => {})
          } else {
            const theIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
              return existApp.softwareName.toLowerCase() == oldSoftwareName.toLowerCase()
            })
            this.temp.stgRelatedSoftware.splice(theIndex, 1, data)
          }
        } else {
          this.temp.stgRelatedSoftware.splice(existIndex, 1, data)
        }
      }
      this.handleFilter()
    },
    softwareImportSubmitEnd(data) {
      if (data) {
        const existItem = []
        data.forEach(item => {
          if (item.originalName) {
            this.$set(item, 'softwareName', item.originalName)
          }
          item['matchType'] = 0
          this.temp.stgRelatedSoftware.findIndex(existApp => {
            if (existApp.softwareName.toLowerCase() == item.softwareName.toLowerCase()) {
              existItem.push(item.softwareName)
              return true
            }
            return false
          })
        })
        if (existItem.length > 0) {
          const msg = this.$t('pages.software_Msg41', { info: existItem.join(',') })
          this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
            this.importAndUpdateSoftware(data)
          }).catch(() => {})
        } else {
          this.importAndUpdateSoftware(data)
        }
        this.handleFilter()
      }
    },
    importAndUpdateSoftware(data) {
      data.forEach(item => {
        const theIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
          return existApp.softwareName.toLowerCase() == item.softwareName.toLowerCase()
        })
        const software = {
          softwareName: item.softwareName,
          versions: [this.$t('pages.software_Msg39')], // ['全版本']
          matchType: item.matchType,
          appInfos: item.appInfos
        }
        if (theIndex > -1) {
          this.temp.stgRelatedSoftware.splice(theIndex, 1, software)
        } else {
          this.temp.stgRelatedSoftware.push(software)
        }
      })
    },
    versionsFormatter: function(row, data) {
      return data.join(',')
    },
    processNameFormatter: function(row, data) {
      const processNames = []
      if (row.appInfos) {
        row.appInfos.forEach(appInfo => {
          processNames.push(appInfo.processName)
        })
      }
      return processNames.join(',')
    },
    matchTypeFormatter(row, data) {
      return this.matchTypeOptions[row.matchType]
    },
    handleRadio() {
      this.limitTypeOptions.forEach(item => {
        item.violations = []
        item.propCheckRule = false
      })
      this.$refs['resContent'].ruleId = undefined
      this.temp.ruleId = null
    },
    ruleIsCheck(data) {
      this.getLimitTypeOpt().propCheckRule = data === 1
      this.propCheckRuleChange(data === 1);
    }
  }
}
</script>
<style lang="scss" scoped>
.selectionCheckBox {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}
</style>
