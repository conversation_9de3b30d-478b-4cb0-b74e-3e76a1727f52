<template>
  <div>
    <el-row>
      <el-col :span="canUpload ? 22 : 24">
        <FormItem :label="$t('pages.exeName')" prop="processName">
          <el-input v-model="appTemp.processName" :disabled="!writeAble"/>
        </FormItem>
      </el-col>
      <el-col v-if="canUpload" :span="2">
        <el-upload
          ref="uploadProcess"
          name="processFile"
          action="1111"
          accept=".exe"
          :limit="1"
          :show-file-list="false"
          :disabled="fileSubmitting"
          :before-upload="getFileName"
        >
          <el-button type="primary" icon="el-icon-upload" :loading="fileSubmitting"></el-button>
        </el-upload>
      </el-col>
    </el-row>
    <el-row v-if="loading">
      <el-col :span="22">
        <el-progress type="line" :percentage="percentage"/>
      </el-col>
      <el-col :span="2">
        <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'
import { upload } from '@/api/behaviorManage/application/appGroup'

export default {
  name: 'UploadProcess',
  components: { },
  directives: { },
  props: {
    appTemp: { // 表单对象
      type: Object,
      default() {
        return {}
      }
    },
    autoAppend: { // 自动拼接数据
      type: Boolean,
      default: true
    },
    canUpload: {  // 是否显示上传按钮
      type: Boolean,
      default: true
    },
    writeAble: {
      type: Boolean,
      default: false
    },
    fileLimitSize: {  // 可上传文件的大小
      type: Number,
      default: 1024
    },
    onlyExe: {  // 是否只能上传exe
      type: Boolean,
      default: true
    },
    afterUpload: {  // 上传成功后的操作
      type: Function,
      default: () => {
      }
    },
    validProcess: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      fileSubmitting: false,
      percentage: 0,
      source: null  // 上传连接源
    }
  },
  computed: {
  },
  watch: {

  },
  created() {

  },
  methods: {
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    getFileName(file) {
      if (this.validProcess) {
        const valid = this.validProcess(file)
        if (!valid) {
          return false
        }
      } else {
        const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
        if (!isLt2M) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.appGroup_text11', { size: this.fileLimitSize }),
            type: 'error',
            duration: 2000
          })
          return false
        }
        const fileName = file.name
        if (this.onlyExe) {
          const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
          if (ext != 'exe') {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.software_Msg10'),
              type: 'error',
              duration: 2000
            })
            return false
          }
        }
      }

      this.fileSubmitting = true
      this.loading = true
      this.percentage = 0
      const fd = new FormData()
      fd.append('uploadFile', file)// 传文件
      // 上传钩子，用来获取进度条
      const onUploadProgress = (progressEvent) => {
        const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
        this.percentage = parseInt(percent)
      }
      this.source = this.connectionSource()
      // 这个是上传会话token，取消上传操作需要的参数
      const cacheToken = this.source.token
      upload(fd, onUploadProgress, cacheToken).then(res => {
        this.resetUploadComponent()
        if (res.data.length > 0) {
          this.resetAppTempPropety()
          if (this.autoAppend) {
            Object.assign(this.appTemp, res.data[0])
          }
          this.afterUpload(res.data[0])
        }
      }).catch(res => {
        this.resetUploadComponent()
        if (axios.isCancel(res)) {
          // 取消上传后的操作，待补充
        }
      })
      return false // 屏蔽了action的默认上传
    },
    resetAppTempPropety() { // 初始化一些appTemp的进程属性
      this.appTemp.processName = ''
      this.appTemp.productName = ''
      this.appTemp.productVersion = ''
      this.appTemp.originalFilename = ''
      this.appTemp.fileDesc = ''
      this.appTemp.fileDescription = ''
      this.appTemp.companyName = ''
      this.appTemp.internalName = ''
      this.appTemp.legalCopyright = ''
      this.appTemp.fileMd5 = ''
      this.appTemp.quicklyMd5 = ''
      this.appTemp.softSign = ''
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.loading = false
      this.percentage = 0
    }
  }
}
</script>
