<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :height="600"
    :title="$t('pages.softwareInfo')"
    :visible.sync="dialogFormVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <div>
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="softwareTable"
        :height="375"
        :col-model="colModel"
        is-saved-selected
        saved-selected-prop="originalName"
        row-key="originalName"
        :row-data-api="rowDataApi"
        :after-load="afterLoad"
        :multi-select="multiSelect"
        :default-sort="{ prop: 'value' }"
        retain-pages
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="submitEnd">
        {{ $t('pages.addSoftware') }}
      </el-button>
      <el-button @click="clearSelection">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  getSoftAndExtPageGroupByName,
  countSoftAndExtAndOrderPageGroupByName,
  countSoftGroupByName,
  getSoftPageGroupByName
} from '@/api/softwareManage/assets/assetsView'

export default {
  name: 'SoftwareAssertTable',
  props: {
    //  是否隐藏关联程序
    associateHidden: {
      type: Boolean,
      default: false
    },
    //  是否支持获取版本号
    supportVersion: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      multiSelect: true,
      colModel: [
        { prop: 'softwareName', label: 'softwareName', width: '150', fixed: true, sort: 'custom', type: 'button',
          buttons: [
            { formatter: this.softwareNameFormatter, click: this.softwareView }
          ]
        },
        { label: 'associatedApplication', width: '200', formatter: this.appInfoFormatter, hidden: this.associateHidden },
        { prop: 'softwareVersion', label: 'versionNumber', width: '200', type: 'select', rowOptions: 'versions', options: [], hidden: !this.supportVersion }
      ],
      query: { // 查询条件
        page: 1,
        limit: 10,
        assetType: 2004, // 策略仅支持应用软件
        originalSearch: true,
        searchInfo: ''
      },
      submitting: false,
      installPath: '',
      dialogFormVisible: false,
      submitFlag: true,
      validate: false,
      param: null //  保存参数
    }
  },
  computed: {},
  created() {},
  activated() {
    this.handleRefresh()
  },
  methods: {
    // countSoftGroupByName,
    gridTable() {
      return this.$refs['softwareTable']
    },
    show(validate, param) {
      this.dialogFormVisible = true
      this.param = param || null
      this.validate = validate
      this.installPath = ''
      if (this.gridTable()) {
        this.query.page = 1
        this.query.searchInfo = ''
        this.handleRefresh()
      }
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable() && this.gridTable().execRowDataApi(this.query)
    },
    formatSubmitData() {
      const selectedDatas = this.gridTable().getSelectedDatas()
      selectedDatas.forEach(data => {
        data.path = this.installPath
        if (this.validate &&
            (!data.hasOwnProperty('softwareExtInfo') ||
              !data.softwareExtInfo.hasOwnProperty('appInfo') ||
              !data.softwareExtInfo ||
              data.softwareExtInfo.appInfo == '[]')) {
          this.submitFlag = false
        }
      })
      if (this.submitFlag) {
        selectedDatas.forEach(data => {
          if (data.softwareExtInfo && data.softwareExtInfo.appInfo) {
            data.appInfos = JSON.parse(data.softwareExtInfo.appInfo)
          }
        })
      }
      return selectedDatas
    },
    submitEnd() {
      this.submitFlag = true
      const selectedDatas = this.associateHidden ? this.gridTable().getSelectedDatas() : this.formatSubmitData()
      if (this.submitFlag) {
        this.$emit('submitEnd', selectedDatas, this.param)
        this.clearSelection()
      } else {
        this.$message({
          message: this.$t('pages.software_Msg11'),
          type: 'error',
          duration: 2000
        })
      }
    },
    clearSelection() {
      this.gridTable().selectedDatasDelete()
      this.dialogFormVisible = false
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      searchQuery.searchVersion = this.supportVersion
      if (option.searchCount === false) {
        if (this.supportVersion) {
          return getSoftPageGroupByName(searchQuery);
        }
        return getSoftAndExtPageGroupByName(searchQuery)
      }
      return this.rowTotalApi(searchQuery)
    },
    async rowTotalApi(option) {
      const result = {}
      await countSoftAndExtAndOrderPageGroupByName(option).then(resp => {
        result.total = resp.data
      })
      const fun = this.supportVersion ? getSoftPageGroupByName : getSoftAndExtPageGroupByName;
      await fun(option).then(resp => {
        if (resp.data) {
          resp.data.total = result.total
        }
        Object.assign(result, resp);
      })
      return result
    },
    afterLoad(datas) {
      if (this.supportVersion && datas.length > 0) {
        datas.forEach(item => {
          item.versions = [{ value: '*.*', label: this.$t('pages.software_Msg39') }]
          if (item.softwareVersions) {
            item.softwareVersions.forEach(version => {
              if (version) {
                item.versions.push({ value: version, label: version })
              }
            })
          }
          const softwareVersion = item.versions.length > 1 ? item.versions[1].value : item.versions[0].value
          this.$set(item, 'softwareVersion', softwareVersion)
          this.$set(item, 'versions', item.versions)
        })
      }
      return datas;
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    appInfoFormatter: function(row, data) {
      if (!row.softwareExtInfo || !row.softwareExtInfo.appInfo) {
        return ''
      }
      const appInfos = JSON.parse(row.softwareExtInfo.appInfo)
      const processNames = []
      appInfos.forEach(appInfo => {
        processNames.push(appInfo.processName)
      })
      return processNames.join(',')
    },
    softwareNameFormatter: function(row, data) {
      return row.originalName
    },
    softwareView(row, data) {
      if (this.hasPermission('B45')) {
        this.$router.push({ name: 'SoftwareDetail', params: { softwareInfo: row, version: row.softwareVersion, originalSearch: true }})
      } else {
        this.$message({
          message: this.$t('pages.software_Msg50'),
          type: 'error',
          duration: 2000
        })
      }
    }
  }
}
</script>
