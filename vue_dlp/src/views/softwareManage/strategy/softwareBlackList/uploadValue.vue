<template>
  <div>
    <FormItem :label="formItemLabelType === 'processName' ? $t('table.processExeName') : $t('table.programFinger')" :prop="formItemPropLabel">
      <el-input ref="limitValue" v-model="value" v-trim :disabled="!writeAble" style="width: calc(100% - 46px)" :maxlength="128" show-word-limit/>
      <el-upload
        ref="uploadProcess"
        name="processFile"
        action="1111"
        accept=".exe"
        :limit="1"
        :show-file-list="false"
        :disabled="!canUpload || fileSubmitting"
        :before-upload="getFileName"
        style="display: inline-block;"
      >
        <el-button type="primary" icon="el-icon-upload" :disabled="!canUpload || fileSubmitting" :loading="fileSubmitting" style="margin: 0;"></el-button>
      </el-upload>
    </FormItem>
    <FormItem v-if="loading">
      <el-progress type="line" :percentage="percentage" style="width: calc(100% - 46px); display: inline-block;"/>
      <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" style="margin-bottom: 0;" @click="cancel"></el-button>
    </FormItem>
  </div>
</template>

<script>
import axios from 'axios'
import { upload } from '@/api/behaviorManage/application/appGroup'

export default {
  name: 'UploadValue',
  components: { },
  directives: { },
  props: {
    formItemLabelType: {
      type: String,
      default() {
        return 'processName'
      }
    },
    formItemPropLabel: {
      type: String,
      default() {
        return 'processName'
      }
    },
    autoAppend: { // 自动拼接数据
      type: Boolean,
      default: true
    },
    canUpload: {  // 是否显示上传按钮
      type: Boolean,
      default: true
    },
    writeAble: {
      type: Boolean,
      default: false
    },
    fileLimitSize: {  // 可上传文件的大小
      type: Number,
      default: 1024
    },
    onlyExe: {  // 是否只能上传exe
      type: Boolean,
      default: true
    },
    afterUpload: {  // 上传成功后的操作
      type: Function,
      default: () => {
      }
    },
    valueChange: {
      type: Function,
      default: () => {

      }
    },
    validProcess: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      fileSubmitting: false,
      percentage: 0,
      value: '',
      source: null  // 上传连接源
    }
  },
  computed: {
  },
  watch: {
    value() {
      if (this.formItemLabelType === 'processName') {
        this.valueChange(this.value)
      }
    }
  },
  created() {

  },
  methods: {
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    setValue(data) {
      this.value = data
    },
    clearValue() {
      this.value = ''
    },
    getFileName(file) {
      if (this.validProcess) {
        const valid = this.validProcess(file)
        if (!valid) {
          return false
        }
      } else {
        const isLt2M = file.size / 1024 / 1024 < this.fileLimitSize
        if (!isLt2M) {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.appGroup_text11', { size: this.fileLimitSize }),
            type: 'error',
            duration: 2000
          })
          return false
        }
        const fileName = file.name
        if (this.onlyExe && this.formItemLabelType === 'quicklyMd5') {
          const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
          if (ext != 'exe') {
            this.$message({
              title: this.$t('text.prompt'),
              message: this.$t('pages.appGroup_text10'),
              type: 'error',
              duration: 2000
            })
            return false
          }
        }
      }

      this.fileSubmitting = true
      this.loading = true
      this.percentage = 0
      const fd = new FormData()
      fd.append('uploadFile', file)// 传文件
      // 上传钩子，用来获取进度条
      const onUploadProgress = (progressEvent) => {
        const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
        this.percentage = parseInt(percent)
      }
      this.source = this.connectionSource()
      // 这个是上传会话token，取消上传操作需要的参数
      const cacheToken = this.source.token
      upload(fd, onUploadProgress, cacheToken).then(res => {
        this.resetUploadComponent()
        if (res.data.length > 0) {
          if (this.formItemLabelType === 'processName') {
            this.value = res.data[0].processName
          } else if (this.formItemLabelType === 'quicklyMd5') {
            this.value = res.data[0].quicklyMd5
          }
          this.afterUpload(res.data[0])
        }
      }).catch(res => {
        this.resetUploadComponent()
        if (axios.isCancel(res)) {
          // 取消上传后的操作，待补充
        }
      })
      return false // 屏蔽了action的默认上传
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.fileSubmitting = false
      this.loading = false
      this.percentage = 0
    }
  }
}
</script>
