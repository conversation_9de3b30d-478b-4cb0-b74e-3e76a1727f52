<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.limitValue"
            v-trim
            clearable
            :placeholder="$t('table.processExeName') + '/' + $t('table.programFinger')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="blackList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="textMap[dialogStatus]"
        :visible.sync="dialogFormVisible"
        width="500px"
        @dragDialog="handleDrag"
        @closed="dialogStatus=''"
      >
        <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="margin-left:20px;margin-right: 20px;">
          <FormItem :label="$t('table.limitType')" prop="limitType">
            <el-select v-model="temp.limitType" @change="limitTypeChange">
              <el-option :key="1" :value="1" :label="$t('table.processExeName')"/>
              <!--  <el-option :key="2" :value="2" :label="$t('pages.windowTitle')"/>-->
              <el-option :key="3" :value="3" :label="$t('table.programFinger')"/>
            </el-select>
          </FormItem>
          <FormItem :label="$t('pages.effectiveWay')" prop="effectType">
            <el-select v-model="temp.effectType">
              <el-option :key="1" :value="0" :label="$t('pages.forbidRun')"/>
              <el-option :key="2" :value="1" :label="$t('pages.allowRun')"/>
            </el-select>
          </FormItem>
          <upload-value
            v-if="limitValueAble"
            ref="upload"
            :write-able="!writeAble"
            :only-exe="true"
            :prop-label="formItemPropLabel"
            :form-item-label-type="formItemLabelType"
            :after-upload="afterUpload"
            :value-change="valueChange"
          />
          <FormItem v-if="!limitValueAble" :label="$t('table.limitValue')" prop="windowTitle">
            <el-input v-model="temp.limitValue" v-trim maxlength="128" show-word-limit></el-input>
          </FormItem>
          <FormItem :label="$t('pages.remark')" prop="remark">
            <el-input v-model="temp.remark" type="textarea" maxlength="200" show-word-limit></el-input>
          </FormItem>
        </Form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="submitting" @click="dialogStatus === 'create' ? createData() : updateData()">{{ $t('button.confirm') }}</el-button>
          <el-button @click="()=>{dialogFormVisible = false;}">{{ $t('button.cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :title="$t('pages.softBlackList_Msg5')"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :delete-filter-name="$t('route.softwareBlackList')"
      :edit-type="1"
      :dlg-used-able="false"
      @submitEnd="deleteData"
    />
  </div>
</template>
<script>
import UploadValue from './uploadValue.vue'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import { getPage, insertDecToolLimit, updateDecToolLimit, deleteDecToolLimit, getDecToolLimitByTypeAndValue } from '@/api/softwareManage/strategy/decToolLimit'
export default {
  name: 'BlackList',
  components: { UploadValue, BatchEditPageDlg },
  props: {

  },
  data() {
    return {
      query: { // 查询条件
        page: 1,
        limitValue: '',
        limitType: null
      },
      colModel: [
        { prop: 'limitType', label: 'limitType', width: '100', sort: true, formatter: this.typeFormatter },
        { prop: 'effectType', label: this.$t('pages.effectiveWay'), width: '100', sort: true, formatter: this.effectiveTypeFormatter },
        { prop: 'limitValue', label: 'processExeName', width: '150', formatter: this.programNameFormatter },
        { prop: '', label: 'programFinger', width: '150', formatter: this.programFingerFormatter },
        { prop: 'remark', label: 'remark', width: '150' },
        {
          label: 'operate', type: 'button', fixed: 'right', fixedWidth: '150',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      rules: {
        limitType: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        effectType: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        processName: [
          { required: true, trigger: 'blur', validator: this.processNameValidator }
        ],
        quicklyMd5: [
          { required: true, trigger: 'blur', validator: this.quicklyMd5Validator }
        ],
        windowTitle: [
          { required: true, trigger: 'blur', validator: this.windowTitleValidator }
        ]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.$t('button.edit') + this.$t('pages.softBlackList_Msg9'),
        create: this.$t('button.add') + this.$t('pages.softBlackList_Msg9')
      },
      temp: {

      },
      defaultTemp: {
        limitType: 1,
        effectType: 0,
        limitValue: '',
        type: 2,
        remark: null
      },
      limitValueAble: true,
      fileSubmitting: false,
      percentage: 0,
      writeAble: false,
      formItemPropLabel: 'processName',
      formItemLabelType: 'processName'
    }
  },
  methods: {
    processNameValidator(rule, value, callback) {
      if (this.temp.limitType === 1 && (this.temp.limitValue === undefined || this.temp.limitValue === null || this.temp.limitValue === '')) {
        callback(new Error(this.$t('pages.softBlackList_Msg10')))
      } else {
        const obj = {
          limitType: this.temp.limitType,
          limitValue: this.temp.limitValue
        };
        getDecToolLimitByTypeAndValue(obj).then(resp => {
          if (resp.data && resp.data.id != this.temp.id) {
            // 存在
            callback(new Error(this.$t('pages.softBlackList_Msg11')))
          } else {
            // 不存在
            callback()
          }
        })
      }
    },
    quicklyMd5Validator(rule, value, callback) {
      if (this.temp.limitType === 3 && (this.temp.limitValue === undefined || this.temp.limitValue === null || this.temp.limitValue === '')) {
        callback(new Error(this.$t('pages.softBlackList_Msg10')))
      } else {
        var obj = {
          limitType: this.temp.limitType,
          limitValue: this.temp.limitValue
        }
        getDecToolLimitByTypeAndValue(obj).then(resp => {
          if (resp.data && resp.data.id != this.temp.id) {
            // 存在
            callback(new Error(this.$t('pages.softBlackList_Msg11')))
          } else {
            // 不存在
            callback()
          }
        })
      }
    },
    windowTitleValidator(rule, value, callback) {
      if (this.temp.limitType === 2 && (this.temp.limitValue === undefined || this.temp.limitValue === null || this.temp.limitValue === '')) {
        callback(new Error(this.$t('pages.softBlackList_Msg10')))
      } else {
        const obj = {
          limitType: this.temp.limitType,
          limitValue: this.temp.limitValue
        };
        getDecToolLimitByTypeAndValue(obj).then(resp => {
          if (resp.data && resp.data.id != this.temp.id) {
            // 存在
            callback(new Error(this.$t('pages.softBlackList_Msg11')))
          } else {
            // 不存在
            callback()
          }
        })
      }
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          insertDecToolLimit(this.temp).then(resp => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$refs.blackList.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(e => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          updateDecToolLimit(this.temp).then(resp => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$refs.blackList.execRowDataApi(this.query)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(e => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteDecToolLimit(params).then(respond => {
          this.$refs.blackList.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          callback(respond)
        }).catch(e => { callback(e) })
      }).catch(e => { callback(e) })
    },
    selectionChangeEnd: function(rowDatas) {

    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    limitTypeChange() {
      this.temp.limitValue = ''
      if (this.temp.limitType === 1) {
        this.writeAble = false
        this.formItemPropLabel = 'processName'
        this.formItemLabelType = 'processName'
        this.limitValueAble = true
        this.$nextTick(() => {
          this.$refs.upload.clearValue()
        })
      } else if (this.temp.limitType === 3) {
        this.writeAble = true
        this.formItemPropLabel = 'quicklyMd5'
        this.formItemLabelType = 'quicklyMd5'
        this.limitValueAble = true
        this.$nextTick(() => {
          this.$refs.upload.clearValue()
        })
      } else {
        this.limitValueAble = false
      }
      this.$refs.dataForm.clearValidate()
    },
    handleCreate() {
      this.temp = {}
      this.$nextTick(() => {
        this.$refs.upload.clearValue()
        this.$refs.dataForm.clearValidate()
      })
      this.temp = Object.assign({}, this.defaultTemp)
      this.writeAble = false
      this.formItemPropLabel = 'processName'
      this.formItemLabelType = 'processName'
      this.limitValueAble = true
      this.$nextTick(() => {
        this.$refs.upload.clearValue()
      })
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    valueChange(data) {
      if (this.temp.limitType === 1) {
        this.temp.limitValue = data
      }
    },
    afterUpload(appData) {
      const obj = {
        processName: appData.processName,
        quicklyMd5: appData.quicklyMd5
      }
      if (this.temp.limitType != 2) {
        if (this.temp.limitType === 1) {
          if (obj.processName.length > 128) {
            this.temp.limitValue = obj.processName.slice(0, 128)
            this.$refs.upload.setValue(this.temp.limitValue)
          } else {
            this.temp.limitValue = obj.processName
          }
        } else {
          this.temp.limitValue = obj.quicklyMd5
        }
      }
      // 文件读取成功后执行一次校验
      this.$refs.dataForm.validate((valid) => {})
    },
    handleDelete() {
      const selectedData = this.$refs.blackList.getSelectedDatas()
      const total = this.$refs.blackList.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    typeFormatter(row, data) {
      let msg = '';
      if (data === 1) {
        msg = this.$t('table.processExeName')
      } else if (data === 2) {
        msg = this.$t('pages.windowTitle')
      } else if (data === 3) {
        msg = this.$t('table.programFinger')
      }
      return msg
    },
    effectiveTypeFormatter(row, data) {
      if (data === 1) {
        return this.$t('pages.allowRun');
      } else {
        return this.$t('pages.forbidRun');
      }
    },
    programNameFormatter(row, data) {
      const limitType = row.limitType
      if (limitType === 1) {
        return data
      } else {
        return ''
      }
    },
    programFingerFormatter(row, data) {
      const limitType = row.limitType
      if (limitType === 3) {
        return row.limitValue
      } else {
        return ''
      }
    },
    handleFilter() {
      this.query.page = 1
      this.$refs.blackList.execRowDataApi(this.query)
    },
    handleUpdate(row) {
      this.temp = {}
      this.temp = Object.assign({}, row)
      this.formatRowData()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    formatRowData() {
      if (this.temp.limitType === 2) {
        this.limitValueAble = false
      } else {
        this.limitValueAble = true
        if (this.temp.limitType === 1) {
          this.writeAble = false
          this.formItemPropLabel = 'processName'
          this.formItemLabelType = 'processName'
          this.$nextTick(() => {
            this.$refs.upload.setValue(this.temp.limitValue)
          })
        } else if (this.temp.limitType === 3) {
          this.writeAble = true
          this.formItemPropLabel = 'quicklyMd5'
          this.formItemLabelType = 'quicklyMd5'
          this.$nextTick(() => {
            this.$refs.upload.setValue(this.temp.limitValue)
          })
        }
      }
      if (this.temp.effectType === undefined) { // 兼容旧版，默认禁止运行
        this.temp.effectType = 0
      }
    },
    handleDrag() {

    }
  }
}
</script>
