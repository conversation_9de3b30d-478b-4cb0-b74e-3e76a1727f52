<template>
  <div class="app-container">
    <div v-if="treeable && formable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" @data-change="strategyTargetNodeChange" />
    </div>
    <div v-if="formable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>

        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      >
      </grid-table>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="dialogStatus=''"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="margin-left:20px;margin-right: 20px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable"/>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.softBlackList_Msg6') }}</el-divider>
        <FormItem label-width="0px" style="margin-top: 10px;">
          <el-checkbox v-model="temp.isClose" :true-label="1" :false-label="0" :disabled="!formable" style="margin-left: 30px;" @change="isCloseChange">
            {{ $t('pages.softBlackList_Msg2') }}
          </el-checkbox>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <span>
          {{ $t('pages.softBlackList_Msg7') }}
        </span>
        <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
        <FormItem label-width="0" prop="action">
          <ResponseContent
            ref="responseContent"
            :status="dialogStatus"
            style="line-height: 15px !important;"
            :select-style="{ 'margin-top': '5px', 'margin-left': '13px' }"
            :show-select="true"
            :editable="formable && temp.isClose === 0"
            read-only
            :show-check-rule="true"
            :prop-rule-id="temp.ruleId"
            @getRuleId="getRuleId"
            @validate="(val) => { responseValidate = val }"
          />
        </FormItem>
        <el-checkbox v-model="temp.isBackup" :true-label="1" :false-label="0" :disabled="!formable || temp.isClose === 1" style="margin-left: 30px;">
          {{ $t('pages.backupFile') }}
        </el-checkbox>
        <el-checkbox v-model="temp.isAuditAllow" :true-label="1" :false-label="0" :disabled="!formable || temp.isClose === 1" style="display:block;margin-left: 30px;margin-top: 20px">
          {{ $t('pages.auditAllowRunHighRiskSoft') }}
          <el-tooltip effect="dark" placement="top-start">
            <div slot="content">
              {{ $t('pages.auditHighRiskSoftTip') }}<br/>
            </div>
            <i class="el-icon-info"/>
          </el-tooltip>
        </el-checkbox>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="()=>{dialogFormVisible = false;}">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import { validatePolicy } from '@/utils/validate'
import { getStrategyPage, createStrategy, updateStrategy, deleteStrategy, getDecToolLimitStgByName } from '@/api/softwareManage/strategy/decToolLimitStg'
import {
  enableStgBtn,
  enableStgDelete,
  buttonFormatter,
  entityLink,
  objectFormatter,
  refreshPage,
  selectable,
  hiddenActiveAndEntity
} from '@/utils'
export default {
  name: 'BaseConfig',
  components: { ResponseContent },
  props: {
    formable: { type: Boolean, default: true }
  },
  data() {
    return {
      stgCode: 265,
      showTree: true,
      treeable: true,
      deleteable: false,
      responseCheckAble: false,
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      colModel: [
        { prop: 'name', label: 'stgName', fixedWidth: '150', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('route.softwareBlackListStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('route.softwareBlackListStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.stgNameRequired'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      temp: {

      },
      defaultTemp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        isClose: 0,
        ruleId: undefined,
        isBackup: 0,
        isAuditAllow: 0,
        entityType: undefined,
        entityId: undefined
      }
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    isCloseChange(val) {
    //  this.responseCheckAble = false
      this.temp.ruleId = undefined
      this.$refs.responseContent.cancelSelectCheckbox(false)
      this.temp.isBackup = 0
      this.temp.isAuditAllow = 0
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyNameValidator(rule, value, callback) {
      getDecToolLimitStgByName({ name: value }).then(respond => {
        const stg = respond.data
        if (stg && stg.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    createData() {
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.responseValidate) {
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$refs.strategyList.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.responseValidate) {
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$refs.strategyList.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleUpdate: function(row) {
      this.temp = {}
      this.temp = Object.assign({}, row)
      if (this.temp.isClose === 0 && this.temp.ruleId) {
        this.$nextTick(() => {
          this.$refs.responseContent.cancelSelectCheckbox(true)
        })
      } else {
        this.$nextTick(() => {
          this.$refs.responseContent.cancelSelectCheckbox(false)
        })
      }
      if (this.temp.isBackup === undefined) {
        this.$set(this.temp, 'isBackup', 0)
      }
      if (this.temp.isAuditAllow === undefined) {
        this.$set(this.temp, 'isAuditAllow', 0)
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    strategyFormatter: function(row) {
      let msg = ''
      if (row.isClose === 0) {
        msg += this.$t('pages.doNot') + this.$t('pages.softBlackList_Msg2')
      } else {
        msg += this.$t('pages.softBlackList_Msg2')
      }
      if (row.isClose === 0 && row.ruleId) {
        msg += '；' + this.$t('pages.triggerResponseRule')
      }
      if (row.isBackup !== undefined && row.isBackup === 1) {
        msg += '；' + this.$t('pages.backupFile')
      }
      if (row.isAuditAllow !== undefined && row.isAuditAllow === 1) {
        msg += '；' + this.$t('pages.auditAllowRunHighRiskSoft')
      }
      return msg
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    handleDrag() {

    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.$refs.strategyList.execRowDataApi(this.query)
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    handleCreate() {
      this.temp = {}
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate('name')
        this.isCloseChange()
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs.strategyList.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.$refs.strategyList.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    refresh() {
      return refreshPage(this)
    },
    handleFilter() {
      this.query.page = 1
      this.$refs.strategyList.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    }
  }
}
</script>
