<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.basicSetting')" name="baseConfig">
        <base-config ref="baseConfig"/>
      </el-tab-pane>
      <el-tab-pane :label="$t('pages.softBlackList_Msg8')" name="blackList">
        <black-list ref="blackList"/>
      </el-tab-pane>
      <el-tab-pane :label="$t('pages.softBlackList_Msg12')" name="blackListRecord">
        <black-list-record ref="blackListRecord"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import BaseConfig from './baseConfig.vue';
import BlackList from './blackList.vue';
import BlackListRecord from './blackListRecord.vue';
export default {
  name: 'SoftwareBlackList',
  components: { BaseConfig, BlackList, BlackListRecord },
  data() {
    return {
      activeName: 'baseConfig'
    }
  },
  methods: {
    tabClick() {
      if (this.activeName === 'blackList') {
        this.$refs.blackList.handleFilter()
      }
    }
  }
}
</script>
<style lang='scss' scoped>
.module-form{
  margin-left: 210px;
  height: 100%;
  overflow: auto;
  .el-tabs{
    height: calc(100% - 40px);
  }
  .el-tab-pane{
    padding: 0 10px 10px;
  }
}
.app-container .tree-container.hidden+.module-form{
  margin-left: 0;
}
</style>
