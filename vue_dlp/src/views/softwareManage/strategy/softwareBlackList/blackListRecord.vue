<template>
  <div class="app-container">
    <div v-if="treeable && formable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" @data-change="strategyTargetNodeChange" />
    </div>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.fileName" :value="query.fileName">
          <span>{{ $t('table.filePath') }}：</span>
          <el-input v-model="query.fileName" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <SearchItem model-key="query.fastMd5" :value="query.fastMd5">
          <span>{{ $t('table.programFinger') }}：</span>
          <el-input v-model="query.fastMd5" v-trim clearable style="width: 200px;" />
        </SearchItem>
        <SearchItem model-key="query.limitType" :value="query.limitType">
          <span>{{ $t('pages.effectiveWay') }}：</span>
          <el-select v-model="query.limitType" clearable>
            <el-option :key="1" :value="0" :label="$t('pages.forbidRun')"/>
            <el-option :key="2" :value="1" :label="$t('pages.allowRun')"/>
          </el-select>
        </SearchItem>
        <el-button slot="append" icon="el-icon-plus" size="mini" :disabled="selection.length === 0" @click="handleBatchAdd">
          {{ $t('button.addToHighRiskSoftLibrary') }}
        </el-button>
        <audit-log-exporter slot="append" :request="handleExport"/>
        <audit-file-downloader ref="auditFileDownloader" slot="append" :button="$t('table.download')" :selection="selection" :before-download="beforeDownload"/>
        <audit-log-delete v-if="$store.getters.auditingDeleteAble" slot="append" :selection="selection" :delete-log="deleteLog" :table-getter="gridTable"/>
      </SearchToolbar>
      <grid-table
        ref="logList"
        row-key="logId"
        :col-model="colModel"
        :default-sort="{ prop: 'createTime' }"
        :multi-select="true"
        :row-data-api="rowDataApi"
        :sortable="sortable"
        :after-load="afterLoad"
        :custom-col="true"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.softBlackList_Msg12')"
      :visible.sync="dialogFormVisible"
      width="700px"
    >
      <div class="show-detail-panel">
        <el-descriptions class="margin-top" :column="2" size="" border>
          <el-descriptions-item :label="$t('table.time')">
            {{ rowDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <terminal-detail
              :label="rowDetail.terminalName"
              :search-id="rowDetail.terminalId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.user')">
            <user-detail
              :label="rowDetail.userName"
              :search-id="rowDetail.userId"
            />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('pages.effectiveWay')">
            {{ limitTypeFormatter(rowDetail, rowDetail.limitType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.processExeName')">
            {{ fileNameFormatter(rowDetail, rowDetail.fileName) }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.programFinger')">
            {{ rowDetail.fastMd5 }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" :label="$t('table.filePath')">
            {{ rowDetail.fileName }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <video-viewer ref="videoViewer" :search-report="query.searchReport"/>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.addToHighRiskSoftLibrary')"
      :visible.sync="dialogFormVisible2"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px">
        <FormItem :label="$t('table.limitType')" prop="limitType">
          <el-select v-model="temp.limitType">
            <el-option :key="1" :value="1" :label="$t('pages.appInfoName')"/>
            <el-option :key="3" :value="3" :label="$t('table.programFinger')"/>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.effectiveWay')" prop="effectType">
          <el-select v-model="temp.effectType">
            <el-option :key="1" :value="0" :label="$t('pages.forbidRun')"/>
            <el-option :key="2" :value="1" :label="$t('pages.allowRun')"/>
          </el-select>
        </FormItem>
        <div style="float: right">
          <el-button v-if="temp.limitType === 3" type="primary" size="small" @click="handleFillRemark">
            {{ $t('一键备注') }}
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                <span>{{ $t('限制类型为程序指纹时，可批量将程序名称作为备注') }}</span>
              </div>
              <i class="el-icon-info"/>
            </el-tooltip>
          </el-button>
          <el-button v-if="temp.limitType === 3" type="primary" size="small" @click="handleClearRemark">
            {{ $t('清空备注') }}
          </el-button>
        </div>
        <grid-table
          ref="batchAddTable"
          :multi-select="true"
          :height="300"
          :col-model="colModel2"
          :row-datas="rowDatas"
          :show-pager="false"
        />
        <div style="color: rgb(43, 122, 172); margin-top: 5px;">{{ $t('pages.autoLogin_text3') + $t('pages.batchAddHighRiskSoftTip') }}</div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveToHighRiskSoftwareLibrary">
          {{ $t('button.addToHighRiskSoftLibrary') }}
        </el-button>
        <el-button @click="dialogFormVisible2 = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  deleteLog,
  exportLog,
  getLogPage,
  batchAddToLibrary,
  checkExistInLibrary
} from '@/api/behaviorAuditing/terminal/highRiskSoftwareLog'
import { backupLogList } from '@/api/report/baseReport/issueReport/issue'

export default {
  name: 'BlackListRecord',
  components: {},
  props: {
    formable: { type: Boolean, default: true }
  },
  data() {
    return {
      treeable: true,
      colModel: [
        { prop: 'createTime', label: 'time', width: '160', sort: true, fixed: true },
        { prop: 'terminalName', label: 'terminalName', width: '150', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId', attributes: { getTermsInfo: this.getTermsInfo }},
        { prop: 'termGroupName', label: 'terminalGroup', width: '100', custom: true },
        { prop: 'userName', label: 'user', width: '150', type: 'showDetail', searchParam: 'userId', searchType: 'user' },
        { prop: 'userGroupName', label: 'userGroup', width: '100', custom: true },
        { prop: '', label: 'processExeName', width: '150', formatter: this.fileNameFormatter },
        { prop: 'fastMd5', label: 'programFinger', width: '300' },
        { prop: 'fileName', label: 'filePath', width: '300' },
        { prop: 'limitType', label: this.$t('pages.effectiveWay'), width: '100', formatter: this.limitTypeFormatter },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'download', click: this.handleLoadDown, disabledFormatter: this.downloadFormatter },
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      colModel2: [
        { prop: 'fileName', label: this.$t('pages.appInfoName'), width: '100' },
        { prop: 'fastMd5', label: 'programFinger', width: '150' },
        { prop: 'remark', label: 'remark', width: '100', type: 'input', maxlength: 100 }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        fileName: '',
        fastMd5: '',
        limitType: '',
        searchReport: 1,
        menuCode: this.$route.meta.code,
        tableKey: 'logList'
      },
      showTree: true,
      selection: [],
      tempTask: {},
      defaultTempTask: {
        backType: 21,
        type: 2, // 1:上传,2:下载
        devId: undefined,
        fileGuid: '',
        fileName: '',
        reDownload: true
      },
      rowDetail: {},
      dialogFormVisible: false,
      sortable: true,
      termsInfo: [], // 存储前一次查询的列表数据，用于处理由于修改了终端名称导致表格界面终端名称和详情弹窗界面终端名称不一致情况
      dialogFormVisible2: false,
      rowDatas: [],
      rules: {
        limitType: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        effectType: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      temp: {
        limitType: 3,
        effectType: 1
      },
      submitting: false
    }
  },
  watch: {
  },
  created() {
    this.query.searchReport = this.$store.getters.reportModule ? 3 : 1
  },
  activated() {
    this.gridTable().execRowDataApi()
  },
  methods: {
    deleteLog,
    gridTable() {
      return this.$refs['logList']
    },
    batchAddTable() {
      return this.$refs['batchAddTable']
    },
    changeSortable() {
      this.sortable = this.query.searchReport != 3
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.query.sortName = option.sortName
      this.query.sortOrder = option.sortOrder
      this.changeSortable()
      return getLogPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.selection = rowDatas || []
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      let months = ''
      this.query.page = 1
      if (this.query.searchReport != 1) {
        backupLogList(this.query).then(res => {
          if (res.data) {
            res.data.forEach(element => {
              months += '</br>' + element.toString().substr(0, 4) + this.$t('text.month') + element.toString().substr(4, 6) + this.$t('text.day')
            });
            this.$notify({
              title: this.$t('text.prompt'),
              dangerouslyUseHTMLString: true,
              message: this.$t('pages.logBackupTip') + months,
              type: 'warning',
              duration: 2000
            })
          }
        })
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleDrag() {
    },
    async handleBatchAdd() {
      this.temp.limitType = 3
      this.temp.effectType = 1
      const toAddDatas = JSON.parse(JSON.stringify(this.gridTable().getSelectedDatas()))
      let filterData = [];
      toAddDatas.filter((el) => !(el === undefined || (el.fileName === '' && el.fastMd5 === ''))).forEach(data => {
        // 从勾选的数据中过滤掉文件名称和md5重复的数据
        if (!filterData.some(item => item.fileName === data.fileName && item.fastMd5 === data.fastMd5)) {
          filterData.push(data);
        }
      })
      filterData = filterData.map((item) => {
        // 按时间段查询的记录，仅仅以id作为唯一标识可能导致相同，所以以el.id + el.createTime作为唯一标识
        return { id: item.id + item.createTime, fileName: item.fileName.substring(item.fileName.lastIndexOf('\\') + 1), fastMd5: item.fastMd5, remark: '' }
      })
      this.rowDatas = filterData
      this.dialogFormVisible2 = true
      this.$nextTick(() => {
        this.batchAddTable() && this.batchAddTable().toggleAllSelection()
      })
    },
    async saveToHighRiskSoftwareLibrary() {
      const rowDatas = JSON.parse(JSON.stringify(this.batchAddTable().getSelectedDatas()))
      if (!rowDatas || rowDatas.length === 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.pleaseTickContentAtLeast', { content: this.$t('pages.data') }),
          type: 'error',
          duration: 2000
        })
        return
      }
      // 添加时，去除id
      rowDatas.forEach((el) => { el.id = '' })
      const { data: existValues } = await checkExistInLibrary({ limitType: this.temp.limitType, highRiskSoftwareLogList: rowDatas })
      console.log('existValues', existValues)
      if (existValues && existValues.length > 0) { // 存在高危库，需要提示高危软件已存在，是否替换修改
        this.$confirmBox(this.$t('pages.batchAddHighRiskSoftTip2', { limitType: this.temp.limitType === 1 ? this.$t('table.processExeName') : this.$t('table.programFinger'), limitValues: existValues.join(', ') }), this.$t('text.prompt')).then(() => {
          this.submitting = true
          // 添加时，去除id
          rowDatas.forEach((el) => { el.id = '' })
          const datas = { limitType: this.temp.limitType, effectType: this.temp.effectType, highRiskSoftwareLogList: rowDatas }
          batchAddToLibrary(datas).then(respond => {
            this.submitting = false
            this.dialogFormVisible2 = false
            this.gridTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.insertSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        }).catch(() => {})
      } else { // 不存在则直接新增到高危库
        this.submitting = true
        const datas = { limitType: this.temp.limitType, effectType: this.temp.effectType, highRiskSoftwareLogList: rowDatas }
        batchAddToLibrary(datas).then(respond => {
          this.submitting = false
          this.dialogFormVisible2 = false
          this.gridTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.insertSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(() => {
          this.submitting = false
        })
      }
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    handleExport(exportType) {
      return this.exportFunc({ exportType, ...this.query })
    },
    exportFunc(formData) {
      return exportLog(formData)
    },
    handleView(row) {
      this.rowDetail = row
      this.dialogFormVisible = true
    },
    afterLoad(rowData) {
      this.termsInfo = []
      for (const row of rowData) {
        const termId = row.terminalId
        const termName = row.terminalName
        const termsInfo = { id: termId, name: termName }
        this.termsInfo.push(termsInfo)
      }
    },
    getTermsInfo() {
      return JSON.parse(JSON.stringify(this.termsInfo))
    },
    limitTypeFormatter(row, data) {
      if (data === 0) {
        return this.$t('pages.forbidRun')
      } else if (data === 1) {
        return this.$t('pages.allowRun')
      } else {
        return ''
      }
    },
    fileNameFormatter(row, data) {
      const index = row.fileName && row.fileName.lastIndexOf('\\')
      if (index > -1) {
        return row.fileName.substring(row.fileName.lastIndexOf('\\') + 1)
      } else {
        return row.fileName
      }
    },
    beforeDownload(row) {
      this.resetTempTask()
      this.tempTask.devId = row.backupServerId
      this.tempTask.fileGuid = row.uploadFileGuid
      this.tempTask.fileName = row.fileName.substring(row.fileName.lastIndexOf('\\') + 1)
      return this.tempTask
    },
    resetTempTask() {
      this.tempTask = Object.assign({}, this.defaultTempTask)
    },
    handleLoadDown(row) {
      this.$refs.auditFileDownloader && this.$refs.auditFileDownloader.handleDownload(row)
    },
    downloadFormatter(data, btn) {
      return !data.uploadFileGuid
    },
    handleFillRemark() {
      this.rowDatas.forEach(item => {
        item.remark = item.fileName
      })
    },
    handleClearRemark() {
      this.rowDatas.forEach(item => {
        item.remark = ''
      })
    }
  }
}
</script>
