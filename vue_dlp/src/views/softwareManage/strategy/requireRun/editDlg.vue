<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.requireRun_Msg2')"
      :stg-code="154"
      :active-able="activeAble"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <div style="padding-left: 30px;">
          <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
          <LocalZoomIn parent-tag="el-dialog">
            <div v-if="formable" class="toolbar">
              <el-button size="small" @click="handleAddSoftware">
                {{ $t('button.insert') }}
              </el-button>
              <el-button size="small" @click="handleSoftware">
                {{ $t('pages.softwareAssetImport') }}
              </el-button>
              <el-button size="small" :disabled="!checkSoftwareDeleteable" @click="handleDeleteCheckedApp">
                {{ $t('button.delete') }}
              </el-button>
              <el-button type="primary" icon="el-icon-search" size="mini" style="margin-left: 5px;float: right;" @click="handleFilter">{{ $t('table.search') }}</el-button>
              <el-input v-model="searchInfo" v-trim clearable :placeholder="$t('pages.inputSoftName')" maxlength="" style="width: 200px;float: right;" @keyup.enter.native="handleFilter"></el-input>
            </div>
            <grid-table
              ref="checkedSoftwareGrid"
              row-key="softwareName"
              :show-pager="false"
              auto-height
              :multi-select="formable"
              :col-model="checkedColModel"
              :row-datas="newTemp.stgRelatedSoftware"
              @selectionChangeEnd="checkAppSelectionChangeEnd"
            />
          </LocalZoomIn>
          <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
          <FormItem :label="$t('pages.requireRun_Msg3')" label-width="250px" prop="action">
            <el-radio-group v-model="temp.requireType" :disabled="!formable">
              <el-radio v-for="item in requireTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </FormItem>
          <FormItem :label="$t('pages.require_Msg4')" label-width="250px" prop="termSampTime">
            <el-input v-model="temp.termSampTime" type="Number" maxlength="4" min="1" max="1440" :disabled="!formable" style="width:  85px;" @input="numberLimit(arguments[0], 'termSampTime', 1, 1440)"></el-input>
            <span>{{ $t('pages.require_Msg5') }}</span>
          </FormItem>
          <FormItem :label="$t('pages.require_Msg6')" label-width="250px" prop="alarmTimes">
            <el-input v-model="temp.alarmTimes" type="Number" maxlength="4" min="0" :disabled="!formable" style="width: 85px;" @input="numberLimit(arguments[0], 'alarmTimes', 1, 1440)" ></el-input>
            <span>{{ $t('pages.require_Msg7') }}</span>
          </FormItem>
          <el-divider content-position="left">{{ $t('table.respond') }}</el-divider>
          <ResponseContent
            ref="resContent"
            :show-select="true"
            :editable="formable"
            read-only
            :prop-check-rule="true"
            :show-check-rule="false"
            :prop-rule-id="temp.ruleId"
            :check-empty-rule="!!responseMsg"
            :select-style="{ 'margin-top': '0px' }"
            @getRuleId="getRuleId"
          >
          </ResponseContent>
        </div>
      </template>
    </stg-dialog>

    <software-assert-table ref="softwareTable" :show-path="false" @submitEnd="softwareImportSubmitEnd"></software-assert-table>

    <software-dlg ref="softwareDlg" @submitEnd="submitSoftwareEnd"/>
  </div>
</template>

<script>
import {
  batchCreateAppInfo,
  countInfoByGroupId,
  createAppInfo,
  createAppType,
  deleteAppInfo,
  deleteAppType,
  getAppInfoList,
  getAppTypeByName,
  getTreeNode,
  importFromLib,
  updateAppInfo,
  updateAppType
} from '@/api/system/baseData/appLibrary'
import {
  createStrategy,
  getStrategyById,
  getStrategyByName,
  updateStrategy
} from '@/api/softwareManage/strategy/requireRun'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import SoftwareAssertTable from '@/views/softwareManage/strategy/softwareAssertTable'
import SoftwareDlg from '@/views/softwareManage/strategy/softwareDlg'

export default {
  name: 'RequireRunDlg',
  components: { SoftwareDlg, SoftwareAssertTable, ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      newTemp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        requireType: 0,
        active: false,
        // childLimit: 0,
        remark: '',
        // timeId: 1,
        classId: '',
        entityType: '',
        entityId: undefined,
        ruleId: null,
        termSampTime: 5,
        alarmTimes: 1,
        stgRelatedSoftware: []
      },
      checkedColModel: [
        { prop: 'softwareName', label: 'softwareName', width: '150', sort: true },
        { prop: 'versions', label: 'versionNumber', width: '150', sort: true, formatter: this.versionsFormatter },
        { prop: 'processName', label: 'processName', width: '150', sort: true, formatter: this.processNameFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleUpdateSoftware }
          ]
        }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      requireTypeOptions: [
        { value: 0, label: this.$t('pages.requireRun_Msg') },
        { value: 1, label: this.$t('pages.requireRun_Msg1') }
      ],
      autoInstallMap: {
        0: this.$t('text.no'),
        1: this.$t('text.yes')
      },
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      checkSoftwareDeleteable: false,
      typeTreeData: [],
      responseValidate: false,
      validate: false, // 是否提示选择响应规则，首次打开时不提示，当 验证响应规则后 或者 ruleId发生变化后，该值设为 true
      searchInfo: ''
    }
  },
  computed: {
    responseMsg() {
      return this.validate ? !this.responseValidate : false
    }
  },
  created() {
    this.loadAppTypeTreeTree()
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    createAppInfo,
    updateAppInfo,
    deleteAppInfo,
    importFromLib,
    getAppInfoList,
    countInfoByGroupId,
    createAppType,
    updateAppType,
    deleteAppType,
    getAppTypeByName,
    batchCreateAppInfo,
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
    },
    closed() {
      this.searchInfo = ''
      this.newTemp = {}
      this.resetTemp()
    },
    handleFilter() {
      this.newTemp = {}
      const tempArr = [...this.temp.stgRelatedSoftware]
      this.newTemp = { ...this.temp, stgRelatedSoftware: tempArr }
      var regex = /^\s+$/
      if (regex.test(this.searchInfo) || this.searchInfo == '') {
        this.$refs['checkedAppGrid'].execRowDataApi()
        return
      }
      var colK = 0;
      var existSearch = false
      for (var i = 0; i < this.newTemp.stgRelatedSoftware.length; i++) {
        if (this.newTemp.stgRelatedSoftware[i].softwareName.toLowerCase().includes(this.searchInfo.toLowerCase())) {
          this.newTemp.stgRelatedSoftware[colK] = this.newTemp.stgRelatedSoftware[i]
          colK++
          if (!existSearch) {
            existSearch = true
          }
        }
      }
      if (existSearch) {
        // 移除多余的数据
        this.newTemp.stgRelatedSoftware.splice(colK, this.newTemp.stgRelatedSoftware.length - colK)
      } else {
        this.newTemp.stgRelatedSoftware = []
      }
    },
    numberLimit(value, type, min, max) {
      if (type === 'alarmTimes') {
        max = parseInt(1440 / this.temp.termSampTime)
        this.temp.alarmTimes = !value ? min : value > max ? max : value < min ? min : parseInt(value)
      } else if (type === 'termSampTime') {
        this.temp.termSampTime = value > max ? max : value < min ? min : parseInt(value)
        const alarmMaxTimes = parseInt(1440 / this.temp.termSampTime)
        this.temp.alarmTimes = this.temp.alarmTimes > alarmMaxTimes ? alarmMaxTimes : this.temp.alarmTimes < 0 ? 0 : parseInt(this.temp.alarmTimes)
      }
    },
    appGridTable() {
      return this.$refs['checkedSoftwareGrid']
    },
    getRuleId(value) {
      this.temp.ruleId = value
      this.responseValidate = !!value
      if (value) {
        this.validate = true
      }
    },
    checkAppSelectionChangeEnd(rowDatas) {
      this.checkSoftwareDeleteable = rowDatas && rowDatas.length > 0
    },
    loadAppTypeTreeTree: function() {
      getTreeNode().then(respond => {
        this.typeTreeData = respond.data
      })
    },
    handleAddSoftware() {
      this.$refs.softwareDlg.handleCreate()
    },
    buttonFormatter(row) {
      return !this.formable
    },
    handleUpdateSoftware(row) {
      this.$refs.softwareDlg.handleUpdate(row)
    },
    handleSoftware() {
      this.$refs.softwareTable.show(true)
    },
    handleDeleteCheckedApp() {
      this.$confirmBox(this.$t('pages.software_Msg7'), this.$t('text.prompt')).then(() => {
        const rows = this.appGridTable().getSelectedDatas()
        // 创建待删除键的集合
        const deleteKeys = new Set(rows.map(({ softwareName }) => `${softwareName}`));
        // 使用filter一次性过滤
        this.temp.stgRelatedSoftware = this.temp.stgRelatedSoftware.filter(({ softwareName }) =>
          !deleteKeys.has(`${softwareName}`)
        );
        this.handleFilter()
      }).catch(() => {})
    },
    resetTemp() {
      this.validate = false
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.resetTemp()
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      }, undefined, () => {
        this.$nextTick(() => { // 新增弹框打开时，清除响应规则的选中情况
          this.$refs['resContent'].ruleId = undefined
        })
      })
    },
    async handleUpdate(row) {
      this.resetTemp()
      if (!this.formable) { // 策略总览查看时，数据未格式化，需要重新从后台查询
        await getStrategyById(row.id).then(response => {
          this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(response.data))) // copy obj
        })
      } else {
        this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      }
      const tempArr = [...this.temp.stgRelatedSoftware]
      this.newTemp = { ...this.temp, stgRelatedSoftware: tempArr }
      this.$refs['stgDlg'].show(row, this.formable)
      this.$nextTick(() => {
        this.appGridTable() && this.appGridTable().clearSelection()
      })
    },
    formatRowData(rowData) {

    },
    formatFormData(formData) {

    },
    validateFormData(formData) {
      this.validate = true
      if (this.temp.stgRelatedSoftware.length > 2500) {
        this.$notify({
          title: this.$t('text.error'),
          message: this.$t('pages.software_Msg49'),
          type: 'error',
          duration: 2000
        })
      } else {
        return this.responseValidate
      }
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    softwareImportSubmitEnd(data) {
      if (data) {
        const existItem = []
        data.forEach(item => {
          if (item.originalName) {
            this.$set(item, 'softwareName', item.originalName)
          }
          this.temp.stgRelatedSoftware.findIndex(existApp => {
            if (existApp.softwareName == item.softwareName) {
              existItem.push(item.softwareName)
              return true
            }
            return false
          })
        })
        if (existItem.length > 0) {
          const msg = this.$t('pages.software_Msg41', { info: existItem.join(',') })
          this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
            this.importAndUpdateSoftware(data)
          }).catch(() => {})
        } else {
          this.importAndUpdateSoftware(data)
        }
        this.handleFilter()
      }
    },
    importAndUpdateSoftware(data) {
      data.forEach(item => {
        const theIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
          return existApp.softwareName == item.softwareName
        })
        const software = {
          softwareName: item.softwareName,
          versions: [this.$t('pages.software_Msg39')], // ['全版本']
          appInfos: item.appInfos
        }
        if (theIndex > -1) {
          this.temp.stgRelatedSoftware.splice(theIndex, 1, software)
        } else {
          this.temp.stgRelatedSoftware.push(software)
        }
      })
    },
    submitSoftwareEnd(data, oldSoftwareName) {
      if (!oldSoftwareName) {
        if (this.temp.stgRelatedSoftware && this.temp.stgRelatedSoftware.length > 0) {
          const theIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
            return existApp.softwareName == data.softwareName
          })
          if (theIndex > -1) {
            this.$confirmBox(this.$t('pages.software_Msg43'), this.$t('text.prompt')).then(() => {
              this.temp.stgRelatedSoftware.splice(theIndex, 1, data)
            }).catch(() => {})
            this.temp.stgRelatedSoftware.splice(theIndex, 1, data)
          } else {
            this.temp.stgRelatedSoftware.push(data)
          }
        } else {
          this.temp.stgRelatedSoftware.push(data)
        }
      } else {
        const existIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
          return existApp.softwareName == data.softwareName
        })
        if (data.softwareName != oldSoftwareName) { //
          if (existIndex > -1) {
            this.$confirmBox(this.$t('pages.software_Msg44'), this.$t('text.prompt')).then(() => {
              this.temp.stgRelatedSoftware.splice(existIndex, 1, data)
              const theIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
                return existApp.softwareName == oldSoftwareName
              })
              this.temp.stgRelatedSoftware.splice(theIndex, 1)
            }).catch(() => {})
          } else {
            const theIndex = this.temp.stgRelatedSoftware.findIndex(existApp => {
              return existApp.softwareName == oldSoftwareName
            })
            this.temp.stgRelatedSoftware.splice(theIndex, 1, data)
          }
        } else {
          this.temp.stgRelatedSoftware.splice(existIndex, 1, data)
        }
      }
      this.handleFilter()
    },
    versionsFormatter: function(row, data) {
      return data.join(',')
    },
    processNameFormatter: function(row, data) {
      const processNames = []
      if (row.appInfos) {
        row.appInfos.forEach(appInfo => {
          processNames.push(appInfo.processName)
        })
      }
      return processNames.join(',')
    },
    autoInstallFormatter(row, data) {
      return this.autoInstallMap[data]
    }
  }
}
</script>
