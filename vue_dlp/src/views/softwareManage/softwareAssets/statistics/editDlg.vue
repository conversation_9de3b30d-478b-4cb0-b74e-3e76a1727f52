<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :height="200"
      :title="$t('pages.editSoftwareMessage')"
      :visible.sync="dialogFormVisible"
      width="750px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :model="temp" label-position="right" label-width="90px" style="width: 670px; margin-left:20px;">
        <el-row>
          <el-col :span="11">
            <FormItem :label="$t('pages.softwareName')" prop="softwareName">
              <el-input v-model="temp.softwareName" maxlength="128" :title="temp.softwareName" :disabled="true" />
            </FormItem>
          </el-col>
          <el-col :span="11" :offset="1">
            <FormItem :label="$t('pages.softwareDesc')" prop="softwareDesc">
              <el-input v-model="temp.softwareExtInfo.softwareDesc" maxlength="300" />
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="11">
            <FormItem :label="$t('pages.developers')" prop="publisher">
              <el-input v-model="temp.softwareExtInfo.publisher" :maxlength="60" />
            </FormItem>
          </el-col>
          <template v-for="(info, i) in softwareExtInfo">
            <el-col :key="info.label" :span="11" :offset="i == 0 ? 1 : 0">
              <FormItem :label="info.label" :prop="info.prop">
                <el-select :ref="info.prop" v-model="temp.softwareExtInfo[info.prop]" filterable clearable style="width: 100%;" :placeholder="$t('pages.software_Msg12')" @change="typeChange(i+1)">
                  <el-option v-for="(item, j) in info.option" :key="item.name" :label="item.name" :value="item.name">
                    <span style="float: left;">{{ item.name }}</span>
                    <svg-icon v-if="item.id > 14" icon-class="delete" :title="$t('pages.delete')" style="float: right; margin-top: 10px;" @click="removeNode(item, j)"></svg-icon>
                    <svg-icon v-if="item.id > 14" icon-class="edit" :title="$t('pages.edit')" style="float: right; margin: 10px 5px 0px 0px; color: #68a8d0;" @click="editNode(item)"></svg-icon>
                  </el-option>
                </el-select>
              </FormItem>
            </el-col>
            <el-col :key="i" :span="1">
              <svg-icon icon-class="add" :title="$t('pages.add')" class="add-btn" @click="addNode(i+1)"></svg-icon>
            </el-col>
          </template>
        </el-row>
        <el-card class="box-card" :body-style="{'padding': '10px'}" >
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.software_Msg13') }}</span>
            <el-button size="small" :disabled="temp.appInfos.length >= 50" @click="handleProcessCreate">
              {{ $t('button.insert') }}
            </el-button>
            <el-button size="small" :disabled="temp.appInfos.length >= 50" @click="handleImportAppFromSelfLib">
              {{ $t('pages.softAssetGroupImport') }}
              <el-tooltip class="item" effect="dark" :content="$t('pages.software_Msg15')" placement="bottom-start">
                <i class="el-icon-info" />
              </el-tooltip>
            </el-button>
            <el-button size="small" :disabled="!deleteable" @click="handleProcessDelete">
              {{ $t('button.delete') }}
            </el-button>
          </div>
          <div :class="tableClass">
            <grid-table
              ref="processGrid"
              :show-pager="false"
              :height="250"
              :col-model="colModel1"
              :row-datas="temp.appInfos"
              @selectionChangeEnd="processSelectionChangeEnd"
            />
          </div>
        </el-card>
      </Form>

      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveSoftware">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <SoftwareTypeDlg ref="SoftwareTypeDlg" @submitEnd="softwareTypeSubmitEnd"/>

    <App-import-table
      ref="appImportTable"
      :os-type="osType"
      :add-group-btn="false"
      :type-tree-data="typeTreeData"
      :group-root-name="$t('pages.softAssetGroup')"
      :list="getSoftInfoPage"
      :count-by-group="countInfoByGroupId"
      :create="createSoftInfo"
      :batch-create="batchAddSoftInfo"
      :update="updateSoftInfo"
      :delete="deleteSoftInfo"
      :import-func="importSoftInfoFromLib"
      :create-group="createSoftType"
      :update-group="updateSoftType"
      :delete-group="deleteSoftType"
      :get-group-by-name="getSoftTypeByName"
      support-select-terminal-soft
      @submitEnd="appMd5ImportSubmitEnd"
    />
  </div>
</template>

<script>
import { createSoftType, updateSoftType, deleteSoftType, getSoftTypeByName, getSoftTypeTreeNode, createSoftInfo,
  batchAddSoftInfo, updateSoftInfo, deleteSoftInfo, importSoftInfoFromLib, getSoftInfoPage, countInfoByGroupId,
  updateSoftwareInfo, listSoftwareGroup, deleteoftwareGroup, getSoftwareExtInfo
} from '@/api/softwareManage/assets/assetsView'
import AppImportTable from '@/views/system/baseData/appLibrary/appImportTableDlg'
import SoftwareTypeDlg from '../../components/softwareTypeDlg'

export default {
  name: 'SoftwareDlg',
  components: { AppImportTable, SoftwareTypeDlg },
  props: {},
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'processName1', width: '150' },
        { prop: 'processDesc', label: 'processDesc', width: '150' },
        {
          label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleProcessUpdate }
          ]
        }
      ],
      colModel1: [
        { prop: 'processName', label: 'processName1', width: '150', sort: true, formatter: this.nameFormatter },
        { prop: 'isCheckMd5', label: 'checkMd5Label', width: '150', sort: true, formatter: this.md5LevelFormatter }
        // { prop: 'processType', label: '类型', width: '150', formatter: this.processTypeFormatter }
      ],
      softwareExtInfo: [
        { label: this.$t('pages.softwareType'), prop: 'softwareType', option: [] },
        { label: this.$t('pages.chargeType'), prop: 'chargeType', option: [] },
        { label: this.$t('pages.industryType'), prop: 'industryType', option: [] }
      ],
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      },
      processTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      textMap: {
        createS: this.$t('pages.addSoftwareType'),
        updateS: this.$t('pages.editSoftwareType'),
        createC: this.$t('pages.addChargeType'),
        updateC: this.$t('pages.editChargeType'),
        createI: this.$t('pages.addIndustryType'),
        updateI: this.$t('pages.editIndustryType')
      },
      dialogStatus: '',
      dialogFormVisible: false,
      processDatas: [],
      defaultTempExt: {
        softwareName: '',
        publisher: '',
        chargeType: '',
        softwareType: '',
        industryType: '',
        softwareDesc: '',
        softwareId: ''
      },
      tempB: {}, // 表单备份字段
      temp: {}, // 表单字段
      defaultTemp: {
        softwareName: '',
        id: undefined,
        softwareExtInfo: {},
        publisher: '',
        appInfos: []
      },
      tempG: {}, // 表单字段
      defaultTempG: {
        id: undefined,
        type: undefined,
        name: '',
        remark: ''
      },
      submitting: false,
      deleteable: false,
      tableClass: '',
      osType: 1, // 进程系统类型：1-windows，2-linux，4-mac
      typeTreeDataWindow: [],
      typeTreeDataLinux: [],
      typeTreeDataMac: [],
      oldTypeName: '',
      noSelectting: true
    }
  },
  computed: {
    typeTreeData() {
      if (this.osType == 1) {
        return this.typeTreeDataWindow
      } else if (this.osType == 2) {
        return this.typeTreeDataLinux
      } else if (this.osType == 4) {
        return this.typeTreeDataMac
      }
      return null
    }
  },
  created() {
    this.loadAppTypeTreeTree()
    this.initSoftwareGroups()
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
    this.initSoftwareGroups()
  },
  methods: {
    createSoftInfo,
    batchAddSoftInfo,
    updateSoftInfo,
    deleteSoftInfo,
    importSoftInfoFromLib,
    getSoftInfoPage,
    countInfoByGroupId,
    createSoftType,
    updateSoftType,
    deleteSoftType,
    getSoftTypeByName,
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(Object.assign({}, this.defaultTemp)))
      this.temp.softwareExtInfo = Object.assign({}, this.defaultTempExt)
    },
    show(row, dialogStatus) {
      this.resetTemp()
      if (dialogStatus) {
        this.dialogStatus = dialogStatus
      } else {
        this.tableClass = 'table-container'
      }
      this.dialogFormVisible = true
      this.temp = Object.assign(this.temp, row)
      // let softwareNames = row.softwareName
      // if (row['originalNames']) {
      //   softwareNames = softwareNames + ',' + row['originalNames']
      // }
      getSoftwareExtInfo({ softwareName: row.softwareName, originalNames: row['originalNames'] }).then(res => {
        const data = res.data
        if (data) {
          const { appInfo, publisher, softwareName } = data
          if (appInfo) {
            this.temp.appInfos = JSON.parse(appInfo)
          }
          if (!publisher) {
            data.publisher = this.temp.publisher
          }
          if (!softwareName) {
            data.softwareName = this.temp.softwareName
          }
          this.temp.softwareExtInfo = data
        }
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
          this.$refs['processGrid'].execRowDataApi()
        })
      })
    },
    loadAppTypeTreeTree: function() {
      getSoftTypeTreeNode({ osType: 1 }).then(respond => {
        this.typeTreeDataWindow = respond.data
      })
      getSoftTypeTreeNode({ osType: 2 }).then(respond => {
        this.typeTreeDataLinux = respond.data
      })
      getSoftTypeTreeNode({ osType: 4 }).then(respond => {
        this.typeTreeDataMac = respond.data
      })
    },
    initSoftwareGroups() {
      // 初始化所有的类型树，1：软件类型树，2：收费类型树，3：行业类型树
      [1, 2, 3].forEach(type => {
        listSoftwareGroup(type).then(respond => {
          this.softwareExtInfo[type - 1].option = respond.data
        })
      })
    },
    closeEditBtn() {
      this.dialogStatus = 'view'
      this.temp = this.deepClone(this.tempB)
      this.tempB = {}
      this.$emit('submitEnd')
    },
    editSoftware() {
      this.dialogStatus = 'update'
      this.tempB = this.deepClone(this.temp)
    },
    saveSoftware() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          updateSoftwareInfo(this.temp).then(respond => {
            this.submitting = false
            this.dialogStatus = 'view'
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.editSuccess'),
              type: 'success',
              duration: 2000
            })
            this.$emit('submitEnd', respond.data)
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    appAddSubmitEnd(data, dlgStatus) {
      if (data.innerAdd) {
        return
      }
      this.temp.appInfos.unshift(data)
    },
    softwareTypeSubmitEnd(data, dlgStatus) {
      const { type, id, name } = data
      const { option, prop } = this.softwareExtInfo[type - 1]
      if (dlgStatus == 'create') {
        option.push(data)
        if (!this.temp.softwareExtInfo[prop]) {
          this.temp.softwareExtInfo[prop] = name
        }
      } else if (dlgStatus == 'update') {
        const index = option.findIndex(opt => opt.id == id)
        option.splice(index, 1, data)
        if (this.updateSelected) {
          this.temp.softwareExtInfo[prop] = name
        }
      }
    },
    appMd5ImportSubmitEnd(data, editMode) {
      // editMode编辑模式，0-删除，1-添加程序，2-添加类别
      if (editMode === 0) {
        // 创建待删除键的集合
        const deleteKeys = new Set(data.map(({ id }) => `${id}`));
        // 使用filter一次性过滤
        this.temp.appInfos = this.temp.appInfos.filter(({ id }) =>
          !deleteKeys.has(`${id}`)
        );
      } else if (editMode === 1) {
        const length = data.length + this.temp.appInfos.length
        if (data && !data[0].innerAdd && length > 10) {
          this.$message({
            title: this.$t('text.fail'),
            message: this.$t('pages.softwareProcessAssociationFailedTips'),
            type: 'warning',
            duration: 2000
          })
        } else {
          // 创建一个 Map 用于存储列表中的元素，以方便快速查找
          const listMap = new Map();
          this.temp.appInfos.forEach((item) => {
            const key1 = `${item.id}`;
            const key2 = `${item.processName}-${item.fileMd5}-${item.checkMd5}`;
            listMap.set(key1, item);
            listMap.set(key2, item);
          });

          data.forEach(item => {
            const key1 = `${item.id}`;
            const key2 = `${item.processName}-${item.fileMd5}-${item.checkMd5}`;
            const existApp = listMap.get(key1) || listMap.get(key2);
            if (!existApp) {
              this.appAddSubmitEnd(item)
            } else {
              const { md5Level, checkMd5, typeId, relaFingerPrint } = item
              Object.assign(existApp, { md5Level, checkMd5, typeId, relaFingerPrint })
            }
          })
        }
      }
    },
    handleProcessCreate() {
      this.$refs.appImportTable.showBatchCreate()
    },
    handleImportAppFromSelfLib() {
      this.$refs.appImportTable.show()
    },
    handleProcessDelete() {
      let msg = this.$t('pages.software_Msg19')
      const rows = this.$refs['processGrid'].getSelectedDatas()
      const allDatas = this.$refs['processGrid'].getDatas()
      if (rows.length === allDatas.length) {
        msg = this.$t('pages.software_Msg37')
      }
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        // 创建待删除键的集合
        const deleteKeys = new Set(rows.map(({ id, processType }) => `${id}-${processType}`));
        // 使用filter一次性过滤
        this.temp.appInfos = this.temp.appInfos.filter(({ id, processType }) =>
          !deleteKeys.has(`${id}-${processType}`)
        );
      }).catch(() => {})
    },
    processSelectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    typeChange(data) {
      if (!this.noSelectting) {
        const prop = this.softwareExtInfo[data - 1].prop
        this.temp.softwareExtInfo[prop] = this.oldTypeName
      }
      this.noSelectting = true
      this.oldTypeName = ''
    },
    addNode(type) {
      this.$refs['SoftwareTypeDlg'].handleCreate(type)
    },
    editNode(data) {
      const type = data.type
      const prop = this.softwareExtInfo[type - 1].prop
      this.noSelectting = false
      this.oldTypeName = this.temp.softwareExtInfo[prop]
      this.updateSelected = this.oldTypeName === data.name
      this.$refs['SoftwareTypeDlg'].handleUpdate(data)
    },
    removeNode(data, index) {
      const info = this.softwareExtInfo[data.type - 1]
      const prop = info.prop
      const selected = this.temp.softwareExtInfo[prop]
      this.noSelectting = false
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg')).then(() => {
        deleteoftwareGroup(data.id).then(respond => {
          const option = info.option
          option.splice(index, 1)
          this.temp.softwareExtInfo[prop] = selected === data.name ? '' : selected
          this.oldTypeName = selected === data.name ? '' : selected
          this.noSelectting = true
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
        this.temp.softwareExtInfo[prop] = selected
        this.oldTypeName = selected
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    md5LevelFormatter(row, data) {
      if (row.checkMd5 === 0) {
        return this.$t('pages.software_Msg20')
      }
      return this.md5LevelMap[row.md5Level] ? this.md5LevelMap[row.md5Level] : this.md5LevelMap[row.relaFingerPrint[0].checkMd5]
    },
    nameFormatter(row, data) {
      let msg = ''
      if (row.processType == 2) {
        this.temp.appInfos.some(node => {
          if (node.dataId == row.id) {
            msg = node.label
            return true
          }
        })
      } else {
        msg = row.processName
      }
      return msg
    },
    processTypeFormatter(row, data) {
      return this.processTypeMap[data]
    }
  }
}
</script>
<style lang="scss" scoped>
  .add-btn {
    margin: 9px 0px 0px 8px;
    color: #68a8d0;
    cursor: pointer;
  }
</style>
