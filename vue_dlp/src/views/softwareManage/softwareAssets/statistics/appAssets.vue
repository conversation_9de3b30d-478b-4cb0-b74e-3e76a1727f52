<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :terminal-filter-key="terminalFilter" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <common-downloader :name="filename" :button-name="$t('button.export')" button-size="mini" @download="exportAsset"/>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('pages.softwareType')">
                <el-select v-model="query.softwareType" clearable style="width: 100%;">
                  <el-option v-for="(type, i) in softwareTypes" :key="i" :label="type" :value="type"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.chargeType')">
                <el-select v-model="query.chargeType" clearable style="width: 100%;">
                  <el-option v-for="(type, i) in chargeTypes" :key="i" :label="type" :value="type"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.industryType')">
                <el-select v-model="query.industryType" clearable style="width: 100%;">
                  <el-option v-for="(type, i) in industryTypes" :key="i" :label="type" :value="type"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.softwareName')">
                <el-input v-model="query.searchInfo" v-trim maxlength="100" clearable/>
              </FormItem>
              <!-- <FormItem :label="$t('pages.developers')">
                <el-input v-trim v-model="query.publisher" maxlength="100" clearable/>
              </FormItem> -->
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button :loading="submitting" type="primary" size="mini" @click="handleFilter()">{{ $t('button.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>
      <grid-table
        ref="softwareTable"
        row-key="softwareName"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="multiSelect"
        :after-load="tableAfterLoad"
        :default-sort="{ prop: 'value' }"
        retain-pages
      />
    </div>

    <SoftwareDlg ref="softwareDlg" @submitEnd="submitEnd"></SoftwareDlg>
  </div>
</template>

<script>
import SoftwareDlg from './editDlg'
import { initTimestamp, isSameTimestamp } from '@/utils';
import {
  listSoftwareTypes, listChargeTypes, listIndustryTypes, getStatistic,
  countSoftGroupByName, getSoftAndExtAndOrderPageGroupByName, listSoftStatisticByName, exportSoftInfo
} from '@/api/softwareManage/assets/assetsView'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'AppAssets',
  components: { SoftwareDlg, CommonDownloader },
  data() {
    return {
      filename: this.$t('route.' + this.$route.meta.title) + '.xlsx',
      multiSelect: false,
      colModel: [
        { prop: 'softwareName', label: 'softwareName', width: '150', fixed: true, sort: 'custom', type: 'button',
          buttons: [
            { formatter: this.softwareNameFormatter, click: this.softwareView }
          ]
        },
        // { label: 'developers', width: '150', formatter: this.publisherFormatter },
        { label: 'softwareType', width: '150', formatter: this.softwareTypeFormatter },
        { label: 'chargeType', width: '150', formatter: this.chargeTypeFormatter },
        { label: 'industryType', width: '150', formatter: this.industryTypeFormatter },
        { label: 'buyNum', width: '150', formatter: this.orderFormatter },
        { label: 'installNum', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'installSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'installSize', row.statistics) },
            click: this.installedView
          }]
        },
        { label: 'authNum', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'authorizedSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'authorizedSize', row.statistics) },
            click: this.authorizedView
          }]
        },
        { label: 'numberIllegalInstallations', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'unAuthSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'unAuthSize', row.statistics) },
            click: this.violationView
          }]
        },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        flag: 'installed',
        softwareName: '',
        publisher: '',
        softwareType: null,
        chargeType: null,
        industryType: null,
        searchInfo: '',
        objectType: undefined,
        objectId: undefined
      },
      softwareTypes: [
        this.$t('pages.softwareTypes1'),
        this.$t('pages.softwareTypes2'),
        this.$t('pages.softwareTypes3'),
        this.$t('pages.softwareTypes4')
      ],
      chargeTypes: [
        this.$t('pages.chargeTypes1'),
        this.$t('pages.chargeTypes2'),
        this.$t('pages.chargeTypes3'),
        this.$t('pages.chargeTypes4')
      ],
      industryTypes: [
        this.$t('pages.industryTypes1'),
        this.$t('pages.industryTypes2'),
        this.$t('pages.industryTypes3'),
        this.$t('pages.industryTypes4'),
        this.$t('pages.industryTypes5'),
        this.$t('pages.industryTypes6')
      ],
      submitting: false,
      searchOption: {},
      isFirstActivated: true,
      showTree: true
    }
  },
  computed: {
    gridTable() {
      return this.$refs['softwareTable']
    }
  },
  watch: {

  },
  created() {
    initTimestamp(this)
    this.initSoftwareTypes()
  },
  activated() {
    if (!isSameTimestamp(this, 'SoftwareDetail') || !isSameTimestamp(this, 'SoftwareDlg')) {
      this.initSoftwareTypes()
    }
    if (!this.isFirstActivated) {
      this.gridTable.execRowDataApi()
    }
    this.isFirstActivated = false
  },
  methods: {
    countSoftGroupByName,
    initSoftwareTypes: function() {
      this.softwareTypes.splice(0)
      listSoftwareTypes().then(respond => {
        respond.data.forEach(el => {
          if (el && this.softwareTypes.indexOf(el) < 0) {
            this.softwareTypes.push(el)
          }
        })
      })
      this.chargeTypes.splice(0)
      listChargeTypes().then(respond => {
        respond.data.forEach(el => {
          if (el && this.chargeTypes.indexOf(el) < 0) {
            this.chargeTypes.push(el)
          }
        })
      })
      this.industryTypes.splice(0)
      listIndustryTypes().then(respond => {
        respond.data.forEach(el => {
          if (el && this.industryTypes.indexOf(el) < 0) {
            this.industryTypes.push(el)
          }
        })
      })
    },
    submitEnd(data) {
      this.initSoftwareTypes()
      this.gridTable.updateRowData(data)
    },
    saveSoftware() {
      this.$refs['softwareDlg'].saveSoftware()
    },
    handleUpdate(row) {
      this.$refs['softwareDlg'].show(row, 'update')
    },
    rowDataApi(option) {
      this.searchOption = option
      const query = Object.assign({}, this.query, option)
      if (option.searchCount === false) {
        return getSoftAndExtAndOrderPageGroupByName(query)
      }
      return this.rowTotalApi(query)
    },
    async rowTotalApi(option) {
      const result = {}
      await countSoftGroupByName(option).then(resp => {
        result.total = resp.data
      })
      await getSoftAndExtAndOrderPageGroupByName(option).then(resp => {
        if (resp.data) {
          resp.data.total = result.total
        }
        Object.assign(result, resp);
      })
      return result
    },
    tableAfterLoad(rowData, grid) {
      const softNameMap = {}
      const softwareNames = []
      rowData.forEach(item => {
        softwareNames.push(item.softwareName)
        softNameMap[item.softwareName] = item
      })
      listSoftStatisticByName({ softwareNameList: softwareNames }).then(resp => {
        resp.data.forEach(item => {
          const softInfo = softNameMap[item.softwareName]
          softInfo.statistics = item
          this.$refs['softwareTable'].updateRowData(softInfo)
        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    resetQuery() {
      this.query.page = 1
      this.query.searchInfo = ''
      this.query.publisher = ''
      this.query.softwareType = null
      this.query.chargeType = null
      this.query.industryType = null
    },
    // 软件资产暂时先不屏蔽已清理终端
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleRefresh() {
      this.gridTable.execRowDataApi()
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    installedView(row, data) {
      this.$router.push({
        name: 'InstalledSoftware',
        params: { flag: 'installed', softName: row.softwareName }
      })
    },
    authorizedView(row, data) {
      this.$router.push({
        name: 'AuthorizedInstalled',
        params: { flag: 'authorized', softName: row.softwareName }
      })
    },
    violationView(row, data) {
      this.$router.push({
        name: 'IllegalInstalled',
        params: { flag: 'violation', softName: row.softwareName }
      })
    },
    softwareView(row, data) {
      this.$router.push({ name: 'SoftwareDetail', params: { softwareInfo: row }})
    },
    publisherFormatter: function(row, data) {
      let publisher = ''
      if (row.softwareExtInfo && row.softwareExtInfo.publisher) {
        publisher = row.softwareExtInfo.publisher
      } else {
        publisher = row.publisher
      }
      return publisher
    },
    softwareNameFormatter: function(row, data) {
      return row.softwareName
    },
    softwareTypeFormatter: function(row, data) {
      if (row.softwareExtInfo && row.softwareExtInfo.softwareType && row.softwareExtInfo.softwareType.trim()) {
        return row.softwareExtInfo.softwareType
      }
      return this.$t('pages.undefined')
    },
    chargeTypeFormatter: function(row, data) {
      if (row.softwareExtInfo && row.softwareExtInfo.chargeType && row.softwareExtInfo.chargeType.trim()) {
        return row.softwareExtInfo.chargeType
      }
      return this.$t('pages.undefined')
    },
    industryTypeFormatter: function(row, data) {
      if (row.softwareExtInfo && row.softwareExtInfo.industryType && row.softwareExtInfo.industryType.trim()) {
        return row.softwareExtInfo.industryType
      }
      return this.$t('pages.undefined')
    },
    orderFormatter: function(row, data) {
      return !row.purchaseSize ? 0 : row.purchaseSize
    },
    exportAsset(file) {
      const searchQuery = Object.assign({}, this.query, this.searchOption)
      const opts = { file, jwt: true, topic: this.$route.name }
      exportSoftInfo(searchQuery, opts, file.name)
    }
  }
}
</script>
