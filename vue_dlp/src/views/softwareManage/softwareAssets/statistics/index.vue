<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :terminal-filter-key="terminalFilter" :os-type-filter="7" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button v-permission="'199'" size="mini" :title="$t('pages.softwareIdentifyConfig')" @click="handleIdentify">
          {{ $t('pages.softwareIdentifyConfig') }}
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content">{{ $t('pages.softwareInfo_msg13') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
        <common-downloader v-permission="'157'" :name="filename" :button-name="$t('button.export')" button-size="mini" @download="exportAsset"/>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('pages.softwareType')">
                <el-select v-model="query.softwareType" clearable style="width: 100%;">
                  <el-option v-for="(type, i) in softwareTypes" :key="i" :label="type" :value="type"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.chargeType')">
                <el-select v-model="query.chargeType" clearable style="width: 100%;">
                  <el-option v-for="(type, i) in chargeTypes" :key="i" :label="type" :value="type"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.industryType')">
                <el-select v-model="query.industryType" clearable style="width: 100%;">
                  <el-option v-for="(type, i) in industryTypes" :key="i" :label="type" :value="type"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.softwareName')">
                <el-input v-model="query.searchInfo" v-trim maxlength="100" clearable/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button :loading="submitting" type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>

      <el-tabs ref="tabs" v-model="activeName" type="card" style="height: calc(100% - 40px);" @tab-click="tabClick">
        <el-tab-pane v-for="(item, key) in assetTypeTabs" :key="key" :label="item.name" :name="item.propId.toString()">
          <div class="table-container">
            <grid-table
              :ref="item.propId + 'SoftwareTable'"
              row-key="softwareName"
              :col-model="colModel"
              :row-data-api="rowDataApi"
              :multi-select="multiSelect"
              :after-load="tableAfterLoad"
              :default-sort="{ prop: 'value' }"
              retain-pages
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <Drawer
      ref="drawer"
      :visible.sync="drawerVisible"
      :size="820"
      :before-close="beforeClose"
    >
      <div slot="title" class="el-dialog__title">
        {{ $t('pages.softwareIdentifyConfig') }}
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            {{ $t('pages.softwareInfo_msg14') }}
            <br/>
            <br/>
            {{ $t('pages.softwareInfo_msg15') }}
            <br/>
            {{ $t('pages.softwareInfo_msg16') }}
            <br/>
            {{ $t('pages.softwareInfo_msg17') }}
            <br/>
            {{ $t('pages.softwareInfo_msg18') }}
            <br/>
            <br/>
            {{ $t('pages.softwareInfo_msg2') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <div class="table-container">
        <div v-if="editable" class="toolbar">
          <el-button type="primary" size="mini" title="提交" @click="handleAddRule">
            {{ $t('button.add') }}
          </el-button>
          <el-button :disabled="!deleteable" size="mini" title="删除" @click="handleDeleteRule">
            {{ $t('button.delete') }}
          </el-button>
          <el-button size="mini" title="删除" @click="handleClearRule">
            {{ $t('button.clear') }}
          </el-button>
          <el-button size="mini" title="调整优先级" @click="handleSort">
            {{ $t('pages.softwareInfo_msg3') }}
          </el-button>
        </div>
        <grid-table
          ref="ruleTable"
          :col-model="ruleColModel"
          :row-datas="ruleData"
          :multi-select="true"
          :show-pager="false"
          :disabled="!editable"
          :selectable="selectable"
          row-key="level"
          @selectionChangeEnd="selectionChangeEnd"
        />
        <div style="display: flex; justify-content: flex-end; margin-top: 10px;">
          <el-button v-if="!editable" type="primary" size="mini" @click="editConfig">{{ $t('pages.edit') }}</el-button>
          <el-button v-if="!editable" size="mini" @click="drawerVisible = false">{{ $t('text.close') }}</el-button>
          <el-button v-if="editable" type="primary" size="mini" :loading="submitting" @click="saveData">{{ '提交' }}</el-button>
          <el-button v-if="editable" size="mini" @click="cancelConfig">{{ $t('button.cancel') }}</el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-steps :active="active" finish-status="success" simple>
          <el-step :title="$t('pages.softwareInfo_msg4')"></el-step>
          <el-step :title="$t('pages.softwareInfo_msg5')"></el-step>
          <el-step :title="$t('pages.softwareInfo_msg6')"></el-step>
        </el-steps>
      </div>
    </Drawer>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.softwareInfo_msg3')"
      :visible.sync="dialogSortVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <div style="margin: 0 0 10px 10px;">
        <span style="color: #68a8d0">{{ $t('pages.softwareInfo_msg19') }}</span>
      </div>
      <Form ref="sortForm" label-position="right" label-width="75px" style="width: 561px;">
        <div class="sort-header">
          <div class="cell">{{ $t('table.identifyMode') }}</div>
          <div class="cell">{{ $t('table.identifyRule') }}</div>
          <div class="cell">{{ $t('table.assignSoftware') }}</div>
        </div>
        <tree-menu
          ref="targetTree"
          :height="300"
          :data="targetTreeNode"
          :render-content="renderContent"
          :is-filter="false"
          class="sort-tree"
          draggable
          :allow-drag="allowDrag"
          :allow-drop="allowDrop"
          @node-drag-start="handleDragStart"
          @node-drag-enter="handleDragEnter"
          @node-drag-leave="handleDragLeave"
          @node-drag-over="handleDragOver"
          @node-drag-end="handleDragEnd"
        />
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sortRule()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogSortVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <SoftwareDlg ref="softwareDlg" @submitEnd="submitEnd"></SoftwareDlg>
  </div>
</template>

<script>
import SoftwareDlg from './editDlg'
import { initTimestamp, isSameTimestamp } from '@/utils';
import {
  listSoftwareTypes, listChargeTypes, listIndustryTypes, getStatistic,
  getSoftAndExtAndOrderPageGroupByName, countSoftAndExtAndOrderPageGroupByName,
  listSoftStatisticByName, exportSoftIndex, saveSoftwareIdentifyConfig,
  getSoftwareIdentifyConfig, getSoftwareIdentifyStatus, getLastSyncTimestamp
} from '@/api/softwareManage/assets/assetsView'
import { activeSyncAssetBtn } from '@/api/assets/assetsConfig/assetsView'
import { listPropType } from '@/api/assets/assetsConfig/assetLog'
import CommonDownloader from '@/components/DownloadManager/common'
import SyncAssetBtn from '@/views/softwareManage/components/syncAssetBtn'

export default {
  name: 'SoftwareStatistics',
  components: { SoftwareDlg, CommonDownloader, SyncAssetBtn },
  data() {
    return {
      filename: this.$t('route.' + this.$route.meta.title) + '.xlsx',
      multiSelect: false,

      colModel: [
        { prop: 'softwareName', label: 'softwareName', width: '150', fixed: true, sort: 'custom', type: 'button',
          buttons: [
            { formatter: this.softwareNameFormatter, click: this.softwareView }
          ]
        },
        { label: 'softwareType', width: '150', formatter: this.softwareTypeFormatter },
        { label: 'chargeType', width: '150', formatter: this.chargeTypeFormatter },
        { label: 'industryType', width: '150', formatter: this.industryTypeFormatter },
        { label: 'buyNum', width: '150', formatter: this.orderFormatter },
        { label: 'installNum', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'installSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'installSize', row.statistics) },
            click: this.installedView
          }]
        },
        { label: 'authNum', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'authorizedSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'authorizedSize', row.statistics) },
            click: this.authorizedView
          }]
        },
        { label: 'numberIllegalInstallations', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'unAuthSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'unAuthSize', row.statistics) },
            click: this.violationView
          }]
        },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      ruleColModel: [
        { prop: 'type', label: 'identifyMode', width: '150', type: 'select', options: [
          { value: 1, label: this.$t('pages.softwareIdentifyType1') }, { value: 2, label: this.$t('pages.softwareIdentifyType2') },
          { value: 3, label: this.$t('pages.softwareIdentifyType3') }, { value: 4, label: this.$t('pages.softwareIdentifyType4') }
        ], alwaysEdit: true, disabled: this.disable, attributes: { clearable: true }},
        { prop: 'rule', label: 'identifyRule', width: '300', type: 'input', disabled: this.disable, ellipsis: false },
        { prop: 'softwareName', label: 'assignSoftware', width: '150', type: 'input', disabled: this.disable, ellipsis: false }
      ],
      query: { // 查询条件
        page: 1,
        flag: 'installed',
        assetType: 2001,
        softwareName: '',
        publisher: '',
        softwareType: null,
        chargeType: null,
        industryType: null,
        searchInfo: '',
        objectType: undefined,
        objectId: undefined
      },
      softwareTypes: [
        this.$t('pages.softwareTypes1'),
        this.$t('pages.softwareTypes2'),
        this.$t('pages.softwareTypes3'),
        this.$t('pages.softwareTypes4')
      ],
      chargeTypes: [
        this.$t('pages.chargeTypes1'),
        this.$t('pages.chargeTypes2'),
        this.$t('pages.chargeTypes3'),
        this.$t('pages.chargeTypes4')
      ],
      industryTypes: [
        this.$t('pages.industryTypes1'),
        this.$t('pages.industryTypes2'),
        this.$t('pages.industryTypes3'),
        this.$t('pages.industryTypes4'),
        this.$t('pages.industryTypes5'),
        this.$t('pages.industryTypes6')
      ],
      submitting: false,
      searchOption: {},
      isFirstActivated: true,
      showTree: true,
      syncAssetBtnVisible: false,
      drawerVisible: false,
      withFooter: false,
      deleteable: false,
      editable: false,
      active: 0,
      identifyTypeOptions: {
        1: this.$t('pages.softwareIdentifyType1'),
        2: this.$t('pages.softwareIdentifyType2'),
        3: this.$t('pages.softwareIdentifyType3'),
        4: this.$t('pages.softwareIdentifyType4')
      },
      ruleData: [],
      softwareIdentifyConfig: {
        regularRules: [],
        matchRules: []
      },
      dialogSortVisible: false,
      targetTreeNode: [],
      selectedDatas: [],
      ruleTypeOptions: {
        1: this.$t('pages.softwareIdentifyType1'),
        2: this.$t('pages.softwareIdentifyType2'),
        3: this.$t('pages.softwareIdentifyType3'),
        4: this.$t('pages.softwareIdentifyType4')
      },
      lastSyncTimestamp: undefined,
      assetTypeTabs: [],
      activeName: '2001'
    }
  },
  computed: {
    gridTable() {
      return this.$refs['softwareTable']
    }
  },
  watch: {

  },
  created() {
    initTimestamp(this)
    this.initAssetTypeTabs()
    this.initSoftwareTypes()
    this.getSyncAssetBtnPermission()
    this.getLastSyncTimestamp()
    this.getSoftwareIdentifyConfig()
    this.getSoftwareIdentifyStatus()
  },
  activated() {
    this.getSyncAssetBtnPermission()
    this.getLastSyncTimestamp()
    this.getSoftwareIdentifyConfig()
    this.getSoftwareIdentifyStatus()
    if (!isSameTimestamp(this, 'SoftwareDetail') || !isSameTimestamp(this, 'SoftwareDlg')) {
      this.initSoftwareTypes()
    }
    if (!this.isFirstActivated) {
      this.gridTable && this.gridTable.execRowDataApi()
    }
    this.isFirstActivated = false
  },
  methods: {
    initAssetTypeTabs() {
      this.assetTypeTabs.splice(0)
      listPropType({ type: 2 }).then(res => {
        this.assetTypeTabs = res.data
      })
    },
    tabClick(pane, event) {
      this.query.assetType = Number(pane.name)
      pane.$children[0].execRowDataApi(this.query)
    },
    getLastSyncTimestamp() {
      getLastSyncTimestamp().then(res => {
        this.lastSyncTimestamp = res.data
        if (this.lastSyncTimestamp) {
          // 用于接收后端软件资产数据同步的状态
          this.$socket.subscribeToAjax({ requestId: this.lastSyncTimestamp }, 'softwareIdentify/result', (respond, handle) => {
            // 得到异步结果
            handle.close()
            // 软件资产识别同步状态
            const data = respond.data
            // 状态为2表示软件识别同步已完成
            if (data == 2) {
              this.active = 3
              this.handleRefresh()
              this.getLastSyncTimestamp()
            }
          })
        }
      })
    },
    getSoftwareIdentifyConfig() {
      getSoftwareIdentifyConfig().then(res => {
        this.softwareIdentifyConfig = Object.assign(this.softwareIdentifyConfig, res.data)
      })
    },
    getSoftwareIdentifyStatus() {
      getSoftwareIdentifyStatus().then(res => {
        this.active = res.data + 1
      })
    },
    ruleGridTable() {
      return this.$refs['ruleTable']
    },
    disable() {
      return !this.editable
    },
    selectable(row, index) {
      return this.editable
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleAddRule() {
      this.ruleData.forEach(data => {
        data.level = data.level + 1
      })
      this.ruleData.unshift({
        level: 1,
        type: undefined,
        rule: '',
        softwareName: ''
      })
    },
    handleDeleteRule() {
      const toDeleteIds = this.ruleGridTable().getSelectedKeys()
      this.ruleGridTable().deleteRowData(toDeleteIds)
    },
    handleClearRule() {
      this.ruleGridTable().clearRowData()
    },
    allowDrag(draggingNode) {
      return true
    },
    allowDrop(draggingNode, dropNode, type) {
      if (type == 'inner') {
        return false
      } else {
        return true
      }
    },
    handleDragStart(node, ev) {
      const checkDatas = this.$refs['targetTree'].getCheckedNodes() || []
      if (!checkDatas.length) {
        checkDatas.push(node.data)
      }
      if (checkDatas && checkDatas.length > 0) {
        this.selectedDatas.splice(0, this.selectedDatas.length, ...checkDatas)
      }
    },
    handleDragEnter(draggingNode, dropNode, ev) {
      // console.log('node-drag-start', draggingNode, dropNode, ev)
    },
    handleDragLeave(draggingNode, dropNode, ev) {
      // console.log('node-drag-leave', draggingNode, dropNode, ev)
    },
    handleDragOver(draggingNode, dropNode, ev) {
      // console.log('node-drag-over', draggingNode, dropNode, ev)
    },
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      // console.log('node-drag-end', draggingNode, dropNode, dropType, ev)
    },
    sortRule() {
      this.ruleData.splice(0)
      let i = 0
      this.ruleData = this.targetTreeNode.map(node => {
        i++
        node.oriData.level = i
        return node.oriData
      })
      this.dialogSortVisible = false
    },
    renderContent(h, { node, data, store }) {
      const labels = data.label.split(' -> ')
      const labelEle = labels.map((label, index) => {
        return h('div', {
          class: 'cell',
          key: index // 必须提供唯一 key
        }, label);
      })

      return h('div', { class: 'sort-body' }, [
        // 动态生成的数据
        ...labelEle
      ])
    },
    nodeTop(e, store, node, data) {
      e.stopPropagation()
      const parent = node.parent
      const childNodes = parent.childNodes
      const childDatas = parent.data.children || parent.data
      const index = childDatas.findIndex(child => child.dataId === data.dataId)
      if (index === 0) {
        this.$message({
          message: this.$t('pages.department_confirmMsg5'),
          type: 'warning'
        })
      } else if (index !== 0) {
        childNodes.unshift(...childNodes.splice(index, 1))
        childDatas.unshift(...childDatas.splice(index, 1))
      }
    },
    nodeBottom(e, store, node, data) {
      e.stopPropagation()
      const parent = node.parent
      const childNodes = parent.childNodes
      const childDatas = parent.data.children || parent.data
      const index = childDatas.findIndex(child => child.dataId === data.dataId)
      if (index === childDatas.length - 1) {
        this.$message({
          message: this.$t('pages.department_confirmMsg6'),
          type: 'warning'
        })
      } else {
        childNodes.push(...childNodes.splice(index, 1))
        childDatas.push(...childDatas.splice(index, 1))
      }
    },
    nodeUp(e, store, node, data) {
      e.stopPropagation()
      const parent = node.parent
      const childNodes = parent.childNodes
      const childDatas = parent.data.children || parent.data
      const index = childDatas.findIndex(child => child.dataId === data.dataId)
      if (index === 0) {
        this.$message({
          message: this.$t('pages.department_confirmMsg5'),
          type: 'warning'
        })
      } else {
        childNodes.splice(index - 1, 0, ...childNodes.splice(index, 1))
        childDatas.splice(index - 1, 0, ...childDatas.splice(index, 1))
      }
    },
    nodeDown(e, store, node, data) {
      e.stopPropagation()
      const parent = node.parent
      const childNodes = parent.childNodes
      const childDatas = parent.data.children || parent.data
      const index = childDatas.findIndex(child => child.dataId === data.dataId)
      if (index === childDatas.length - 1) {
        this.$message({
          message: this.$t('pages.department_confirmMsg6'),
          type: 'warning'
        })
      } else {
        childNodes.splice(index + 1, 0, ...childNodes.splice(index, 1))
        childDatas.splice(index + 1, 0, ...childDatas.splice(index, 1))
      }
    },
    validateCompleteRule() {
      const datas = this.ruleGridTable().getDatas()
      if (!datas.length) {
        return true
      }
      let flag = true
      for (let i = 0; i < datas.length; i++) {
        if (!datas[i].type || !datas[i].rule || !datas[i].softwareName) {
          flag = false
          break
        }
      }
      return flag
    },
    handleSort() {
      if (this.validateCompleteRule()) {
        this.targetTreeNode.splice(0)
        this.dialogSortVisible = true
        const rowDatas = this.ruleGridTable() && this.ruleGridTable().getDatas()
        this.targetTreeNode = rowDatas.map(data => {
          const label = this.ruleTypeOptions[data.type] + ' -> ' + data.rule + ' -> ' + data.softwareName
          return {
            type: data.type,
            label: label,
            dataId: data.level,
            id: data.level,
            oriData: data
          }
        })
      } else {
        this.$message({
          message: this.$t('pages.softwareInfo_msg9'),
          type: 'error',
          duration: 2000
        })
      }
    },
    handleIdentify() {
      this.getSoftwareIdentifyConfig()
      this.getSoftwareIdentifyStatus()
      this.ruleData = [...this.softwareIdentifyConfig.regularRules, ...this.softwareIdentifyConfig.matchRules]
      let i = 0
      this.ruleData.forEach(data => {
        i++
        data.level = i
      })
      this.drawerVisible = true
    },
    beforeClose() {
      if (this.editable) {
        this.$confirmBox(this.$t('pages.softwareInfo_msg7'), this.$t('text.prompt')).then(() => {
          const result = this.saveData()
          if (result) {
            this.drawerVisible = false
          }
        }).catch(() => {
          this.cancelConfig()
          this.drawerVisible = false
        })
      } else {
        this.drawerVisible = false
      }
    },
    validateRuleData() {
      const datas = this.ruleGridTable().getDatas()
      if (!datas.length) {
        return true
      }
      let flag = true
      for (let i = 0; i < datas.length; i++) {
        if (!datas[i].type && !datas[i].rule && !datas[i].softwareName) {
          datas.splice(i, 1)
          i--
        } else if (!datas[i].type || !datas[i].rule || !datas[i].softwareName) {
          flag = false
          break
        }
      }
      return flag
    },
    formatFormData() {
      const datas = this.ruleGridTable().getDatas()
      this.softwareIdentifyConfig.regularRules = []
      this.softwareIdentifyConfig.matchRules = []
      if (!datas || !datas.length) {
        return
      }
      const toDeleteIds = []
      datas.forEach(data => {
        if (data.type && data.rule && data.softwareName) {
          if (data.type > 1) {
            this.softwareIdentifyConfig.matchRules.push(data)
          } else {
            this.softwareIdentifyConfig.regularRules.push(data)
          }
        } else {
          toDeleteIds.push(data.level)
        }
      })
      this.ruleGridTable().deleteRowData(toDeleteIds)
    },
    editConfig() {
      this.editable = true
      this.active = 1
    },
    saveData() {
      this.submitting = true
      if (this.validateRuleData()) {
        this.formatFormData()
        saveSoftwareIdentifyConfig(this.softwareIdentifyConfig).then(resp => {
          this.submitting = false
          this.editable = false
          // 步骤条设置为软件资产识别
          this.active = 2
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.softwareInfo_msg8'),
            type: 'success',
            duration: 2000
          })
          this.getLastSyncTimestamp()
          return true
        }).catch(reason => {
          this.submitting = false
          return false
        })
      } else {
        this.$message({
          message: this.$t('pages.softwareInfo_msg9'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return false
      }
    },
    cancelConfig() {
      this.ruleData = [...this.softwareIdentifyConfig.regularRules, ...this.softwareIdentifyConfig.matchRules]
      let i = 0
      this.ruleData.forEach(data => {
        i++
        data.level = i
      })
      this.editable = false
      this.getSoftwareIdentifyStatus()
    },
    getSyncAssetBtnPermission() {
      activeSyncAssetBtn().then(res => {
        this.syncAssetBtnVisible = res.data
      })
    },
    initSoftwareTypes: function() {
      this.softwareTypes.splice(0)
      listSoftwareTypes().then(respond => {
        respond.data.forEach(el => {
          if (el && this.softwareTypes.indexOf(el) < 0) {
            this.softwareTypes.push(el)
          }
        })
      })
      this.chargeTypes.splice(0)
      listChargeTypes().then(respond => {
        respond.data.forEach(el => {
          if (el && this.chargeTypes.indexOf(el) < 0) {
            this.chargeTypes.push(el)
          }
        })
      })
      this.industryTypes.splice(0)
      listIndustryTypes().then(respond => {
        respond.data.forEach(el => {
          if (el && this.industryTypes.indexOf(el) < 0) {
            this.industryTypes.push(el)
          }
        })
      })
    },
    submitEnd(data) {
      this.initSoftwareTypes()
      this.gridTable && this.gridTable.updateRowData(data)
    },
    saveSoftware() {
      this.$refs['softwareDlg'].saveSoftware()
    },
    handleUpdate(row) {
      this.$refs['softwareDlg'].show(row, 'update')
    },
    rowDataApi(option) {
      this.searchOption = option
      const query = Object.assign({}, this.query, option)
      if (option.searchCount === false) {
        return getSoftAndExtAndOrderPageGroupByName(query)
      }
      return this.rowTotalApi(query)
    },
    async rowTotalApi(option) {
      const result = {}
      await countSoftAndExtAndOrderPageGroupByName(option).then(resp => {
        result.total = resp.data
      })
      await getSoftAndExtAndOrderPageGroupByName(option).then(resp => {
        if (resp.data) {
          resp.data.total = result.total
        }
        Object.assign(result, resp);
      })
      return result
    },
    tableAfterLoad(rowData, grid) {
      const softNameMap = {}
      const softwareNames = []
      rowData.forEach(item => {
        softwareNames.push(item.softwareName)
        softNameMap[item.softwareName] = item
      })
      const softNameStr = Object.keys(softNameMap).join(',')
      listSoftStatisticByName({ softwareNames: softNameStr, assetType: rowData[0].assetType, objectType: this.query.objectType, objectId: this.query.objectId }).then(resp => {
        resp.data.forEach(item => {
          const softInfo = softNameMap[item.softwareName]
          softInfo.statistics = item
          grid && grid.updateRowData(softInfo)
        })
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    resetQuery() {
      this.query.page = 1
      this.query.searchInfo = ''
      this.query.publisher = ''
      this.query.softwareType = null
      this.query.chargeType = null
      this.query.industryType = null
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 0x20 || (type & 0xf0) === 0x80 || type == 3 || (type & 0xf0) === 0x10)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.$refs.tabs.panes.forEach(pane => {
        if (pane.name == this.$refs.tabs.currentName) {
          pane.$children[0].execRowDataApi(this.query)
        }
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleRefresh() {
      this.$refs.tabs.panes.forEach(pane => {
        if (pane.name == this.$refs.tabs.currentName) {
          pane.$children[0].execRowDataApi()
        }
      })
    },
    handleFilter() {
      this.query.page = 1
      this.$refs.tabs.panes.forEach(pane => {
        if (pane.name == this.$refs.tabs.currentName) {
          pane.$children[0].execRowDataApi(this.query)
        }
      })
    },
    installedView(row, data) {
      this.$router.push({
        name: 'InstalledSoftware',
        params: { flag: 'installed', softName: row.softwareName, assetType: row.assetType, objectType: this.query.objectType, objectId: this.query.objectId }
      })
    },
    authorizedView(row, data) {
      this.$router.push({
        name: 'AuthorizedInstalled',
        params: { flag: 'authorized', softName: row.softwareName, assetType: row.assetType, objectType: this.query.objectType, objectId: this.query.objectId }
      })
    },
    violationView(row, data) {
      this.$router.push({
        name: 'IllegalInstalled',
        params: { flag: 'violation', softName: row.softwareName, assetType: row.assetType, objectType: this.query.objectType, objectId: this.query.objectId }
      })
    },
    softwareView(row, data) {
      this.$router.push({ name: 'SoftwareDetail', params: { softwareInfo: row }})
    },
    handleIcon(row, iconClass) {
      for (let i = 0; i < this.ruleData.length; i++) {
        const data = this.ruleData[i]
        if (data.level == row.level) {
          if (iconClass === 'up-green') {
            const lastRow = this.ruleData[i - 1]
            this.$set(data, 'level', lastRow.level)
            this.$set(lastRow, 'level', lastRow.level + 1)
            this.ruleData.splice(i - 1, 1, data)
            this.ruleData.splice(i, 1, lastRow)
          } else {
            const nextRow = this.ruleData[i + 1]
            this.$set(data, 'level', nextRow.level)
            this.$set(nextRow, 'level', nextRow.level - 1)
            this.ruleData.splice(i + 1, 1, data)
            this.ruleData.splice(i, 1, nextRow)
          }
          break
        }
      }
    },
    iconClassFormat(row) {
      const iconArr = []
      if (this.ruleData[0].level !== row.level) {
        const iconObj1 = { class: 'up-green', title: '上移' }
        iconObj1.className = 'text-button-icon'
        iconArr.push(iconObj1)
      }
      if (this.ruleData[this.ruleData.length - 1].level !== row.level) {
        const iconObj2 = { class: 'down-red', title: '下移' }
        iconObj2.className = 'text-button-icon'
        iconArr.push(iconObj2)
      }
      return iconArr
    },
    publisherFormatter: function(row, data) {
      let publisher = ''
      if (row.softwareExtInfo && row.softwareExtInfo.publisher) {
        publisher = row.softwareExtInfo.publisher
      } else {
        publisher = row.publisher
      }
      return publisher
    },
    softwareNameFormatter: function(row, data) {
      return row.softwareName
    },
    softwareTypeFormatter: function(row, data) {
      if (row.softwareExtInfo && row.softwareExtInfo.softwareType && row.softwareExtInfo.softwareType.trim()) {
        return row.softwareExtInfo.softwareType
      }
      return this.$t('pages.undefined')
    },
    chargeTypeFormatter: function(row, data) {
      if (row.softwareExtInfo && row.softwareExtInfo.chargeType && row.softwareExtInfo.chargeType.trim()) {
        return row.softwareExtInfo.chargeType
      }
      return this.$t('pages.undefined')
    },
    industryTypeFormatter: function(row, data) {
      if (row.softwareExtInfo && row.softwareExtInfo.industryType && row.softwareExtInfo.industryType.trim()) {
        return row.softwareExtInfo.industryType
      }
      return this.$t('pages.undefined')
    },
    orderFormatter: function(row, data) {
      return !row.purchaseSize ? 0 : row.purchaseSize
    },
    exportAsset(file) {
      const searchQuery = Object.assign({ exportVer: 2 }, this.query, this.searchOption)
      const opts = { file, jwt: true, topic: this.$route.name }
      exportSoftIndex(searchQuery, opts)
    }
  }
}
</script>

<style lang="scss" scoped>
  .sort-header {
    display: flex;
    border: 1px solid #aaa;
    border-bottom: 0;
    background: #bbb;
    .cell {
      width: 187px;
      line-height: 30px;
      padding-left: 10px;
      flex: 1;
      font-weight: bold;
      border-left: 1px solid #aaa;
      &:first-child {
        border: 0;
      }
    }
  }
  >>>.sort-tree {
    padding: 0;
    .vue-recycle-scroller {
      overflow-x: hidden;
    }
    .el-tree-node__expand-icon {
      display: none;
    }
    .el-tree-node__content {
      width: calc(100% - 2px);
      border-bottom: 1px solid #aaa;
      cursor: move;
    }
    .sort-body {
      width: 100%;
      flex: 1;
      display: flex;
      .cell {
        width: 187px;
        line-height: 26px;
        padding-left: 10px;
        flex: 1;
        border-left: 1px solid #aaa;
        &:first-child {
          border: 0;
        }
      }
    }
  }
</style>
