<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :treeable="treeable" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" size="mini" style="padding-left: 10px; margin-left: 10px;" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button size="mini" @click="handleAddAttribute">
          {{ $t('pages.customAttribute') }}
        </el-button>
        <sync-asset-btn :show-button="syncAssetBtnVisible"/>
        <common-downloader v-permission="'163'" :loading="submitting" :name="filename" :button-name="$t('button.export')" button-type="primary" button-size="mini" @download="exportAsset"/>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>

      <el-tabs ref="tabs" v-model="activeName" type="card" style="height: calc(100% - 40px);" @tab-click="tabClick">
        <el-tab-pane v-for="(item, key) in assetTypeTabs" :key="key" :label="item.name" :name="item.propId.toString()">
          <div class="table-container">
            <grid-table
              :ref="tableRefOptions[item.propId]"
              :key="tableRefOptions[item.propId]"
              is-saved-selected
              :col-model="colModel"
              :row-data-api="rowDataApi"
              :multi-select="multiSelect"
              :custom-col="true"
              :default-sort="{ prop: 'softwareName' }"
            />
          </div>
        </el-tab-pane>
      </el-tabs>

      <asset-prop-def ref="assetPropDef" :type="type" :parent-id="query.assetType" @submitEnd="submitEnd"/>

      <!-- 资产配置弹窗 -->
      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="$t('pages.softwareInfo_msg20')"
        :visible.sync="dialogFormVisible"
        width="800px"
      >
        <Form
          ref="dataForm"
          :model="temp"
          :hide-required-asterisk="true"
          :rules="rules"
          label-width="90px"
        >
          <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
            <div slot="header">
              <span>{{ $t('pages.softwareInfo_msg21') }}</span>
            </div>
            <el-row>
              <el-col :span="12">
                <FormItem :label="softwareNameOptions[temp.assetType]" prop="originalName">
                  <el-col :span="20">
                    <el-input v-model="temp.originalName" maxlength="128" :title="temp.originalName" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="softwareVersionOptions[temp.assetType]" prop="softwareVersion">
                  <el-col :span="20">
                    <el-input v-model="temp.softwareVersion" maxlength="128" :title="temp.softwareVersion" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.summary')" prop="summary">
                  <el-col :span="20">
                    <el-input v-model="temp.summary" maxlength="128" :title="temp.summary" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.serialNumber')" prop="serialNumber">
                  <el-col :span="20">
                    <el-input v-model="temp.serialNumber" maxlength="128" :title="temp.serialNumber" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.registeredUser')" prop="registeredUser">
                  <el-col :span="20">
                    <el-input v-model="temp.registeredUser" maxlength="128" :title="temp.registeredUser" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('table.developers')" prop="publisher">
                  <el-col :span="20">
                    <el-input v-model="temp.publisher" maxlength="128" :title="temp.publisher" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType !== 2002" :span="12">
                <FormItem :label="$t('table.installTime')" prop="installedTime">
                  <el-col :span="20">
                    <el-input v-model="temp.installedTime" maxlength="128" :title="temp.installedTime" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType !== 2002" :span="12">
                <FormItem :label="$t('table.lastUseTime')" prop="lastUsedTime">
                  <el-col :span="20">
                    <el-input v-model="temp.lastUsedTime" maxlength="128" :title="temp.lastUsedTime" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.computerType')" prop="computerType">
                  <el-col :span="20">
                    <el-select v-model="temp.computerType" :disabled="true">
                      <el-option :label="$t('pages.other')" :value="0"/>
                      <el-option :label="$t('pages.computerType1')" :value="1"/>
                      <el-option :label="$t('pages.computerType2')" :value="2"/>
                      <el-option :label="$t('pages.computerType3')" :value="3"/>
                    </el-select>
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.activeStatus')" prop="active">
                  <el-col :span="20">
                    <el-select v-model="temp.active" :disabled="true">
                      <el-option :label="$t('pages.software_Msg54') " :value="0"/>
                      <el-option :label="$t('pages.software_Msg53') " :value="1"/>
                    </el-select>
                  </el-col>
                </FormItem>
              </el-col>
            </el-row>
          </el-card>
        </Form>
        <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.softwareInfo_msg22') }}</span>
          </div>
          <el-container>
            <el-main>
              <grid-table
                ref="detailTable"
                :show-pager="false"
                :height="410"
                :autoload="false"
                :multi-select="false"
                :col-model="detailColModel"
                :row-datas="customSoftAssetInfos"
              />
            </el-main>
          </el-container>
        </el-card>
        <div style="line-height: 20px; padding-top: 3px; color: #409eff; font-size: small; font-weight:bold;">
          {{ $t('pages.hardAssetCustomeTip') }}
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="submitting" type="primary" @click="handleSave()">
            {{ $t('button.save') }}
          </el-button>
          <el-button @click="dialogFormVisible = false">
            {{ $t('button.close') }}
          </el-button>
        </div>
      </el-dialog>

    </div>
  </div>
</template>

<script>
import {
  getSoftAssetInfoDetail,
  saveCustomSoftAssetInfo,
  getSoftwareInfoPage,
  exportSoftwareInfo
} from '@/api/softwareManage/assets/assetsView'
import CommonDownloader from '@/components/DownloadManager/common'
import SyncAssetBtn from '@/views/softwareManage/components/syncAssetBtn'
import { activeSyncAssetBtn, getCustomAssetColumns } from '@/api/assets/assetsConfig/assetsView'
import { listPropType } from '@/api/assets/assetsConfig/assetLog'
import AssetPropDef from '@/views/softwareManage/components/assetPropDef'

export default {
  name: 'SoftwareAssets',
  components: { AssetPropDef, SyncAssetBtn, CommonDownloader },
  props: {
  },
  data() {
    return {
      filename: `${this.$t('route.' + this.$route.meta.title)}-${this.$t('pages.operatingSystem')}-${this.$t('pages.softwareAsset')}.xlsx`,
      multiSelect: true,
      colModel: [
      ],
      defaultColModel: [
        { label: 'operate', type: 'button', fixedWidth: '80', fixed: 'right',
          buttons: [
            { label: 'assetConfig', title: this.$t('table.assetConfig'), click: this.handleAssetConfig }
          ]
        }
      ],
      detailColModel: [
        { prop: 'name', label: 'params', width: '100' },
        { prop: 'value', label: 'value', width: '200', type: 'input', editMode: true, showText: this.showTextFormatter }
      ],
      colModelOptions: {
        2001: [
          { prop: 'softwareName', label: 'osName', width: '150', sort: 'custom', fixed: true, type: 'button',
            buttons: [
              { formatter: this.softwareNameFormatter, style: 'width: calc(100% - 25px);', click: this.softwareView }
            ]
          },
          { prop: 'terminalName', label: 'terminalName', width: '150', sort: 'custom', fixed: true, type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
          { prop: 'softwareVersion', label: 'osVersion', width: '150' },
          { prop: 'groupName', label: 'terminalGroup', width: '150', type: 'showDetail', searchType: 'department', searchParam: 'groupId' },
          { prop: 'summary', label: 'summary', width: '150' },
          { prop: 'serialNumber', label: 'serialNumber', width: '150' },
          { prop: 'registeredUser', label: 'registeredUser', width: '150' },
          { prop: 'publisher', label: 'developers', width: '150' },
          { prop: 'installedTime', label: 'installDate', width: '150' },
          { prop: 'lastUsedTime', label: 'lastUseTime', width: '150' },
          { prop: 'computerType', label: 'computerType', width: '150', formatter: this.computerTypeFormatter },
          { prop: 'active', label: 'activeStatus', width: '150', formatter: this.systemActiveFormatter },
          { prop: 'path', label: 'installPath', width: '150' }
        ],
        2002: [
          { prop: 'softwareName', label: 'softwareName', width: '150', sort: 'custom', fixed: true, type: 'button',
            buttons: [
              { formatter: this.softwareNameFormatter, style: 'width: calc(100% - 25px);', click: this.softwareView }
            ]
          },
          { prop: 'terminalName', label: 'terminalName', width: '150', sort: 'custom', fixed: true, type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
          { prop: 'softwareVersion', label: 'softwareVersion', width: '150' },
          { prop: 'groupName', label: 'terminalGroup', width: '150', type: 'showDetail', searchType: 'department', searchParam: 'groupId' },
          { prop: 'publisher', label: 'developers', width: '150' }
        ],
        2004: [
          { prop: 'softwareName', label: 'softwareName', width: '150', sort: 'custom', fixed: true, type: 'button',
            buttons: [
              { formatter: this.softwareNameFormatter, style: 'width: calc(100% - 25px);', click: this.softwareView }
            ]
          },
          { prop: 'terminalName', label: 'terminalName', width: '150', sort: 'custom', fixed: true, type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
          { prop: 'softwareVersion', label: 'softwareVersion', width: '150' },
          { prop: 'groupName', label: 'terminalGroup', width: '150', type: 'showDetail', searchType: 'department', searchParam: 'groupId' },
          { prop: 'installedTime', label: 'installDate', width: '150' },
          { prop: 'path', label: 'installPath', width: '150' },
          { prop: 'lastUsedTime', label: 'lastUseTime', width: '150' },
          { prop: 'publisher', label: 'developers', width: '150' }
        ]
      },
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        entityType: undefined,
        entityId: undefined,
        assetType: 2001,
        customSearch: true,
        menuCode: 'B48',
        tableKey: 'osTable'
      },
      type: 2,
      checkedEntityNode: {},
      searchOption: {},
      showTree: true,
      treeable: true,
      syncAssetBtnVisible: false,
      temp: {},
      defaultTemp: {
        id: undefined,
        softwareName: '',
        originalName: '',
        publisher: '',
        softwareVersion: '',
        path: '',
        installedTime: '',
        lastUsedTime: '',
        summary: '',
        serialNumber: '',
        registeredUser: '',
        computerType: 0,
        active: 0,
        assetType: 2001
      },
      authorizedTemp: {},
      defaultAuthorizedTemp: {
        softwareName: '',
        terminalId: undefined
      },
      installSpecialList: [],
      uninstallSpecialList: [],
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.specialDirStg'), 'update'),
        create: this.i18nConcatText(this.$t('pages.specialDirStg'), 'create')
      },
      dialogOrderVisible: false,
      computerTypeOptions: {
        0: this.$t('pages.other'),
        1: this.$t('pages.computerType1'),
        2: this.$t('pages.computerType2'),
        3: this.$t('pages.computerType3')
      },
      assetTypeTabs: [],
      activeName: '2001',
      tableRefOptions: {
        2001: 'osTable',
        2002: 'antivirusTable',
        2004: 'softwareTable'
      },
      customSoftAssetInfos: [],
      softwareNameOptions: {
        2001: this.$t('table.osName'),
        2002: this.$t('table.softwareName'),
        2004: this.$t('table.softwareName')
      },
      softwareVersionOptions: {
        2001: this.$t('table.osVersion'),
        2002: this.$t('table.softwareVersion'),
        2004: this.$t('table.softwareVersion')
      },
      rules: {
        softwareName: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.getSyncAssetBtnPermission()
    this.initAssetTypeTabs()
    this.loadTableColumn()
  },
  activated() {
    this.getSyncAssetBtnPermission()
  },
  methods: {
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    gridTable() {
      return this.$refs[this.tableRefOptions[this.query.assetType]]
    },
    initAssetTypeTabs() {
      this.assetTypeTabs.splice(0)
      listPropType({ type: 2 }).then(res => {
        this.assetTypeTabs = res.data
      })
    },
    getSyncAssetBtnPermission() {
      activeSyncAssetBtn().then(res => {
        this.syncAssetBtnVisible = res.data
      })
    },
    intColModel() {
      this.colModel.splice(0)
      this.colModel.push(...this.colModelOptions[this.query.assetType])
      this.colModel.push(...this.defaultColModel)
    },
    tabClick(pane, event) {
      this.filename = this.$t('route.' + this.$route.meta.title) + '-' + pane.label + '-' + this.$t('pages.softwareAsset') + '.xlsx';
      this.$set(this.query, 'assetType', Number(pane.name))
      this.$set(this.query, 'tableKey', this.tableRefOptions[Number(pane.name)])
      this.assetTypeChange()
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.$refs.tabs.panes.forEach(pane => {
        if (pane.name == this.$refs.tabs.currentName) {
          pane.$children[0].execRowDataApi(this.query)
        }
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetAuthorizedTemp() {
      this.authorizedTemp = Object.assign({}, this.defaultAuthorizedTemp)
    },
    softwareView(row, data) {
      this.$router.push({ name: 'SoftwareDetail', params: { softwareInfo: row, version: row.softwareVersion, originalSearch: true }})
    },
    terminalView(row, data) {
      this.$router.push({ name: 'InstalledSoftware', params: { flag: 'installed', termId: row.terminalId }})
    },
    orderView(row, data) {
      this.$router.push({ name: 'OrderForSoftware', params: { orderIds: row.orderIds.join(',') }})
    },
    handleSave() {
      this.submitting = true
      const assetData = this.$refs['detailTable'].getDatas() || []
      saveCustomSoftAssetInfo(assetData).then(respond => {
        this.submitting = false
        this.dialogFormVisible = false
        this.handleRefresh()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.saveSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.submitting = false
      })
    },
    handleAssetConfig(row) {
      this.customSoftAssetInfos.splice(0)
      this.resetTemp()
      this.dialogFormVisible = true
      getSoftAssetInfoDetail(row.id).then(respond => {
        this.temp = Object.assign(this.temp, respond.data) // copy obj
        this.customSoftAssetInfos = respond.data.customSoftAssetInfos || []
      })
    },
    submitEnd(param) {
      let customColKeys = this.gridTable()[0].customColKeys
      let customKeys = this.gridTable()[0].customKeys
      // 新增自定义属性，那么表格列数组长度+1，操作栏作为最后一个元素，下标需要+1，并更新显示列下标数据
      if (param.type == 'create') {
        const max = Math.max(...customColKeys);
        let maxValueIndex
        customColKeys.forEach((item, index) => {
          if (item == max) {
            maxValueIndex = index
          }
        })
        this.gridTable()[0].customColKeys[maxValueIndex] = max + 1
        this.gridTable()[0].customColKeys.splice(maxValueIndex + 1, 0, max)
        if (customKeys.length === 0) {
          this.gridTable()[0].customKeys = Array.from(new Set([...this.gridTable()[0].customColKeys]))
        } else {
          customKeys.forEach((item, index) => {
            if (item == max) {
              maxValueIndex = index
            }
          })
          this.gridTable()[0].customKeys[maxValueIndex] = max + 1
          this.gridTable()[0].customKeys.splice(maxValueIndex + 1, 0, max)
        }
      } else if (param.type == 'delete') { // 删除自定义属性，对应的自定义显示列下标删除，对应显示的自定义列下标之后的所有下标全部对应-1，并更换自定义列下标数据
        const indexes = param.indexes.map(i => i + this.colModelOptions[this.query.assetType].length)
        customColKeys = customColKeys.filter(item => !indexes.includes(item))
        this.gridTable()[0].customColKeys = customColKeys.map(item => {
          const value = item
          indexes.forEach(item1 => {
            if (value > item1) {
              item--
            }
          })
          return item
        })
        if (customKeys.length === 0) {
          this.gridTable()[0].customKeys = Array.from(new Set([...this.gridTable()[0].customColKeys]))
        } else {
          customKeys = customKeys.filter(item => !indexes.includes(item))
          this.gridTable()[0].customKeys = customKeys.map(item => {
            const value = item
            indexes.forEach(item1 => {
              if (value > item1) {
                item--
              }
            })
            return item
          })
        }
      }
      if (this.gridTable()[0].customKeys.length > 0) {
        this.gridTable()[0].saveCustomCol()
      }
      this.loadTableColumn()
    },
    loadTableColumn() { // 加载表头
      getCustomAssetColumns({ type: this.type, parentId: this.query.assetType }).then(respond => {
        if (respond.data && respond.data.length > 0) {
          respond.data.forEach(item => {
            item.prop = 'customAssetInfoMap.' + item.prop
            item.formatter = this.dataFormatter
          })
          this.colModel = [...this.colModelOptions[this.query.assetType], ...respond.data, ...this.defaultColModel]
        } else {
          this.colModel = [...this.colModelOptions[this.query.assetType], ...this.defaultColModel]
        }
        this.$nextTick(() => {
          this.$refs.tabs.panes.forEach(pane => {
            if (pane.name == this.$refs.tabs.currentName) {
              pane.$children[0].execRowDataApi(this.query)
            }
          })
        })
      })
    },
    handleFilter() {
      this.query.page = 1
      this.$refs.tabs.panes.forEach(pane => {
        if (pane.name == this.$refs.tabs.currentName) {
          pane.$children[0].execRowDataApi(this.query)
        }
      })
    },
    handleRefresh() {
      this.$refs.tabs.panes.forEach(pane => {
        if (pane.name == this.$refs.tabs.currentName) {
          pane.$children[0].execRowDataApi(this.query)
        }
      })
    },
    handleAddAttribute() {
      this.$refs.assetPropDef.show()
    },
    assetTypeChange() {
      this.loadTableColumn()
      this.handleRefresh()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getSoftwareInfoPage(searchQuery)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    showTextFormatter(col, row) {

    },
    computerTypeFormatter: function(row, data) {
      return data ? this.computerTypeOptions[data] : this.computerTypeOptions[0]
    },
    systemActiveFormatter: function(row, data) {
      return data ? this.$t('pages.software_Msg53') : this.$t('pages.software_Msg54')
    },
    softwareNameFormatter: function(row, data) {
      return row.softwareName
    },
    exportAsset(file) {
      this.submitting = true
      const searchQuery = Object.assign({}, this.query, this.searchOption)
      const opts = { file, jwt: true, topic: this.$route.name }
      exportSoftwareInfo(searchQuery, opts, file.name).then(resp => {
        this.submitting = false
      })
    }
  }
}
</script>
