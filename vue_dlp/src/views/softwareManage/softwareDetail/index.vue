<template>
  <div class="app-container">
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-back" size="mini" @click="back">
        {{ $t('pages.software_Msg21', { info: title }) }}
      </el-button>
    </div>

    <div style="height: calc( 100% - 40px); position: relative;">
      <el-tabs ref="tabs" v-model="activeName" type="card" :before-leave="beforeLeave" @tab-click="tabClick">
        <el-tab-pane :label="$t('pages.basicInformation')" name="base" class="detail-tab" style="overflow: auto;">
          <div class="global-config " style="height: 100%;">
            <Software-info ref="softwareInfo" style="height: 100%;"></Software-info>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.softwareVersion')" name="versions" class="detail-tab">
          <div class="table-container">
            <div class="toolbar">
              <el-button type="primary" icon="el-icon-refresh" size="mini" style="margin-left: 5px;" :disabled="!canSet" @click="handleReset">
                {{ $t('pages.softwareInfo_msg10') }}
              </el-button>
            </div>
            <grid-table
              ref="softwareVersionTable"
              :col-model="colModel1"
              :row-data-api="rowDataApi1"
              :multi-select="true"
              :show-pager="false"
              @selectionChangeEnd="selectionChangeEnd"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('pages.orderMessage')" name="orders" class="detail-tab">
          <div class="table-container">
            <grid-table
              ref="softwareOrderTable"
              :col-model="colModel2"
              :row-data-api="rowDataApi2"
              :multi-select="multiSelect"
              :show-pager="false"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
      <el-button v-show="activeName==='base'" type="primary" size="mini" style="position: absolute;right:10px;top:5px" @click="save">
        <svg-icon icon-class="submit"/>{{ $t('pages.software_Msg29') }}
      </el-button>
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.softwareInfo_msg10')"
      :visible.sync="dialogFormVisible"
      width="450px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="tempD" label-position="right" label-width="80px" style="width: 300px; margin-left:20px;">
        <span style="color: #0c60a5; margin-bottom: 10px;">{{ $t('pages.softwareInfo_msg11') + selection }}</span>
        <FormItem :label="$t('pages.softwareInfo_msg12')" prop="softwareName">
          <el-input v-model="tempD.softwareName" maxlength="255" />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveSoftwareInfoDef">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import SoftwareInfo from './softwareInfo'
import { getOrderPage } from '@/api/softwareManage/copyrightManage/orders'
import { getSoftwareVersionPageWithoutLimit, getSoftwareIdentifyStatus, saveSoftwareDef } from '@/api/softwareManage/assets/assetsView'

export default {
  name: 'SoftwareDetail',
  components: { SoftwareInfo },
  data() {
    return {
      multiSelect: false,
      multiSelect1: false,
      colModel2: [
        { prop: 'softwareName', label: 'softwareName', width: '150', fixed: true },
        { prop: 'softwareExtInfo.softwareId', label: 'softwareNum', width: '150' },
        { prop: 'orderId', label: 'orderNumber', width: '150', sort: true },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '150', sort: true },
        { prop: 'publisher', label: 'manufacturerName', width: '150', sort: true },
        { prop: 'contact', label: 'manufacturerContactInformation', width: '150', sort: true },
        { prop: 'purchaseNum', label: 'numberPurchases', width: '100', sort: true },
        { prop: 'price', label: 'purchasesPrice', width: '100', sort: true },
        { prop: 'purchaseDate', label: 'purchasesDate', width: '150', sort: true },
        { prop: 'expirationDate', label: 'dueDate', width: '150', sort: true }
      ],
      colModel1: [
        { prop: 'softwareName', label: 'softwareName', width: '150', fixed: true, sort: true },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '150', sort: true },
        { label: 'installNum', width: '150', type: 'button',
          buttons: [
            { formatter: this.installedNumFormatter, click: this.installedView }
          ]
        },
        { label: 'grantAuthorNum', width: '150', type: 'button',
          buttons: [
            { formatter: this.authorizedNumFormatter, click: this.authorizedView }
          ]
        },
        { label: 'numberIllegalInstallations', width: '150', type: 'button',
          buttons: [
            { formatter: this.violationNumFormatter, click: this.violationView }
          ]
        }
      ],
      activeName: 'base',
      temp: {},
      defaultTemp: {
        softwareName: '',
        id: undefined,
        softwareExtInfo: {},
        softwareId: undefined,
        version: '',
        publisher: '',
        contact: '',
        purchaseNum: undefined,
        price: undefined,
        purchaseDate: undefined,
        expirationDate: undefined,
        remark: '',
        appInfos: []
      },
      tempD: {},
      defaultTempD: {
        softwareName: ''
      },
      // tempExt: {},
      defaultTempExt: {
        softwareName: '',
        chargeType: '',
        softwareType: '',
        industryType: ''
      },
      query: { // 查询条件
        page: 1,
        assetType: 2004,
        softwareName: '',
        softwareVersion: '',
        originalSearch: false
      },
      title: '',
      parentPage: '',
      editable: false,
      isShowBaseTab: true,
      isShowVerTab: false,
      isShowOrderTab: false,
      originalSearch: false,
      canSet: false,
      dialogFormVisible: false,
      submitting: false,
      rules: {
        softwareName: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ]
      },
      selection: []
    }
  },
  watch: {
    dialogFormVisible: function(val) {
      if (!val) {
        this.$nextTick(() => {
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        })
      }
    },
    activeName(val) {
      this.isShowBaseTab = val === 'base'
      this.isShowVerTab = val === 'versions'
      this.isShowOrderTab = val === 'orders'
      this.isShowVerTab && this.$refs['softwareVersionTable'].execRowDataApi()
      this.isShowOrderTab && this.$refs['softwareOrderTable'].execRowDataApi()
    }
  },
  beforeRouteEnter(to, from, next) {
    window.intoSoftware(to, from, next)
  },
  created() {
    this.initSoftwareInfo()
  },
  activated() {
    if (this.$route.params.softwareInfo) {
      this.initSoftwareInfo()
    }
    if (this.isShowVerTab) {
      this.$refs['softwareVersionTable'].execRowDataApi()
    }
  },
  methods: {
    back() {
      if (!this.$refs['softwareInfo'].isEqual()) {
        this.$confirmBox(this.$t('pages.software_Msg30'), this.$t('text.prompt'), { confirmButtonText: this.$t('button.save'), cancelButtonText: this.$t('button.skip') }).then(() => {
          this.save()
          this.backToParent()
        }).catch(() => {
          this.backToParent()
        })
      } else {
        this.backToParent()
      }
    },
    backToParent() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push({ path: this.parentPage, flag: 'refresh' })
    },
    edit() {
      this.editable = true
      this.$refs['softwareInfo'].editSoftware()
    },
    save() {
      this.$refs['softwareInfo'].saveSoftware()
    },
    close() {
      this.editable = false
      this.$refs['softwareInfo'].closeEditBtn()
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.softwareExtInfo = Object.assign({}, this.defaultTempExt)
    },
    resetTempD() {
      this.tempD = Object.assign({}, this.defaultTempD)
    },
    initSoftwareInfo() {
      const originalSearch = !!this.$route.params.originalSearch
      this.show(this.$route.params.softwareInfo, this.$route.params.version, this.$route.params.assetType, originalSearch)
    },
    rowDataApi2: function(option) {
      if (!this.isShowOrderTab) {
        return this.emptyRequestPage()
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getOrderPage(searchQuery)
    },
    installedView(row, data) {
      this.$router.push({ name: 'InstalledSoftware', params: { flag: 'installed', softName: row.softwareName, softVer: row.softwareVersion }})
    },
    authorizedView(row, data) {
      this.$router.push({ name: 'AuthorizedInstalled', params: { flag: 'authorized', softName: row.softwareName, softVer: row.softwareVersion }})
    },
    violationView(row, data) {
      this.$router.push({ name: 'IllegalInstalled', params: { flag: 'violation', softName: row.softwareName, softVer: row.softwareVersion }})
    },
    installedNumFormatter(row, data) {
      return !row.installSize ? 0 : row.installSize
    },
    authorizedNumFormatter(row, data) {
      return !row.authorizedSize ? 0 : row.authorizedSize
    },
    violationNumFormatter(row, data) {
      return !row.unAuthSize ? 0 : row.unAuthSize
    },
    selectionChangeEnd: function(rowDatas) {
      this.canSet = rowDatas && rowDatas.length > 0
    },
    handleReset() {
      this.resetTempD()
      this.selection.splice(0)
      const selectData = this.$refs['softwareVersionTable'].getSelectedDatas()
      const softwareNames = selectData.map(item => item.softwareName)
      this.selection = softwareNames.reduce((pre, cur) => {
        if (pre.indexOf(cur) < 0) {
          pre.push(cur)
        }
        return pre
      }, [])
      this.dialogFormVisible = true
    },
    saveSoftwareInfoDef() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const that = this
          saveSoftwareDef({ softwareName: this.tempD.softwareName, originalNames: this.selection.join(',') }).then(respond => {
            that.dialogFormVisible = false
            that.submitting = false
            that.canSet = false
            this.$refs['softwareVersionTable'].execRowDataApi()
            that.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.submitSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    },
    rowDataApi1: function(option) {
      if (!this.isShowVerTab) {
        return this.emptyRequestPage()
      }
      return getSoftwareVersionPageWithoutLimit({
        softName: this.query.softwareName,
        originalSearch: this.originalSearch,
        assetType: this.query.assetType
      })
    },
    emptyRequestPage() {
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: { total: 0, items: [] }})
      })
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    beforeLeave(activeName, oldActiveName) {
      if (oldActiveName === 'base' && !this.$refs['softwareInfo'].isEqual()) {
        this.$confirmBox(this.$t('pages.software_Msg30'), this.$t('text.prompt'), { confirmButtonText: this.$t('button.save'), cancelButtonText: this.$t('button.skip') }).then(() => {
          this.save()
        }).catch(() => {})
      }
    },
    tabClick(pane, event) {
      if (pane.name == 'versions') {
        this.getSoftwareIdentifyStatus()
      }
    },
    getSoftwareIdentifyStatus() {
      getSoftwareIdentifyStatus().then(res => {
        if (res.data == 2) {
          this.multiSelect1 = true
        }
      })
    },
    show(data, version, assetType, originalSearch) {
      this.resetTemp()
      this.activeName = 'base'
      this.temp = Object.assign(this.temp, data)
      if (assetType) {
        this.query.assetType = assetType
      } else {
        this.query.assetType = this.temp.assetType
      }
      this.query.softwareName = originalSearch ? this.temp.originalName : this.temp.softwareName
      this.query.originalSearch = originalSearch
      this.originalSearch = originalSearch

      this.$nextTick(() => {
        this.$refs['softwareInfo'].show(this.temp, null, originalSearch)
        this.$refs['softwareVersionTable'] && this.$refs['softwareVersionTable'].execRowDataApi()
        this.$refs['softwareOrderTable'] && this.$refs['softwareOrderTable'].execRowDataApi()
      })
    },
    md5LevelFormatter(row, data) {
      if (row.itemType == 2) {
        return ''
      } else if (data === 0) {
        return this.$t('text.disable2')
      } else {
        return this.$t('text.enable')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.detail-tab {
    padding: 10px 20px;
  }
</style>
