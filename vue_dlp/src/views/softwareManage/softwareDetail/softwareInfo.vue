<template>
  <div>
    <Form ref="dataForm" :model="temp" label-position="right" label-width="90px">
      <el-row>
        <el-col :span="8">
          <FormItem :label="$t('pages.softwareName')" prop="softwareName">
            <el-col :span="22">
              <el-input v-model="temp.softwareName" maxlength="128" :title="temp.softwareName" :disabled="true" />
            </el-col>
          </FormItem>
        </el-col>
        <!--        <el-col :span="8">
          <FormItem :label="$t('pages.developers')" prop="publisher">
            <el-col :span="22">
              <el-input v-model="temp.softwareExtInfo.publisher" :maxlength="60" />
            </el-col>
          </FormItem>
        </el-col>-->
        <template v-for="(info, i) in softwareExtInfo">
          <el-col :key="info.label" :span="8">
            <FormItem :label="info.label" :prop="info.prop">
              <el-col :span="22">
                <el-select :ref="info.prop" v-model="temp.softwareExtInfo[info.prop]" filterable clearable style="width: 100%;" :placeholder="$t('pages.software_Msg12')" @change="typeChange(i+1)">
                  <el-option v-for="(item, j) in info.option" :key="item.name" :label="item.name" :value="item.name">
                    <span style="float: left;">{{ item.name }}</span>
                    <svg-icon v-if="item.id > 14" icon-class="delete" :title="$t('pages.delete')" style="float: right; margin-top: 10px;" @click="removeNode(item, j)"></svg-icon>
                    <svg-icon v-if="item.id > 14" icon-class="edit" :title="$t('pages.edit')" style="float: right; margin: 10px 5px 0px 0px; color: #68a8d0;" @click="editNode(item)"></svg-icon>
                  </el-option>
                </el-select>
              </el-col>
              <el-col :key="i" :span="2">
                <svg-icon icon-class="add" :title="$t('pages.add')" class="add-btn" @click="addNode(i+1)"></svg-icon>
              </el-col>
            </FormItem>
          </el-col>
        </template>
        <el-col :span="8">
          <FormItem :label="$t('pages.softwareDesc')" prop="softwareDesc">
            <el-col :span="22">
              <el-input v-model="temp.softwareExtInfo.softwareDesc" maxlength="300" />
            </el-col>
          </FormItem>
        </el-col>
      </el-row>
      <!--      <el-row>
        <template v-for="(info, i) in softwareExtInfo">
          <el-col :key="info.label" :span="8">
            <FormItem :label="info.label" :prop="info.prop">
              <el-col :span="22">
                <el-select :ref="info.prop" v-model="temp.softwareExtInfo[info.prop]" filterable clearable style="width: 100%;" :placeholder="$t('pages.software_Msg12')" @change="typeChange(i+1)">
                  <el-option v-for="(item, j) in info.option" :key="item.name" :label="item.name" :value="item.name">
                    <span style="float: left;">{{ item.name }}</span>
                    <svg-icon v-if="item.id > 14" icon-class="delete" :title="$t('pages.delete')" style="float: right; margin-top: 10px;" @click="removeNode(item, j)"></svg-icon>
                    <svg-icon v-if="item.id > 14" icon-class="edit" :title="$t('pages.edit')" style="float: right; margin: 10px 5px 0px 0px; color: #68a8d0;" @click="editNode(item)"></svg-icon>
                  </el-option>
                </el-select>
              </el-col>
              <el-col :key="i" :span="2">
                <svg-icon icon-class="add" :title="$t('pages.add')" class="add-btn" @click="addNode(i+1)"></svg-icon>
              </el-col>
            </FormItem>
          </el-col>
        </template>
      </el-row>-->
    </Form>
    <el-card class="box-card" :body-style="{'height': 'calc(100% - 40px)', 'padding': '10px', 'overflow': 'auto' }">
      <div slot="header" class="clearfix">
        <span>{{ $t('pages.software_Msg13') }}</span>
      </div>
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="handleProcessCreate">
          {{ $t('button.insert') }}
        </el-button>
        <el-button type="primary" size="mini" @click="handleImportAppFromSelfLib">
          {{ $t('pages.softAssetGroupImport') }}
          <el-tooltip class="item" effect="dark" :content="$t('pages.software_Msg15')" placement="bottom-start">
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
        <el-button type="primary" size="mini" :disabled="!deleteable" @click="handleProcessDelete">
          {{ $t('button.delete') }}
        </el-button>
      </div>
      <div class="table-container" style="height: calc(100% - 40px);">
        <grid-table
          ref="processGrid"
          :show-pager="false"
          auto-height
          :col-model="colModel1"
          :row-datas="temp.appInfos"
          @selectionChangeEnd="processSelectionChangeEnd"
        />
      </div>
    </el-card>

    <el-dialog
      v-el-drag-dialog
      :title="textMap[dialogStatus]"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="550px"
    >
      <Form ref="groupDataForm" :rules="rules" :model="tempG" label-position="right" label-width="80px" style="width: 450px;">
        <FormItem :label="$t('pages.typeName')" prop="name">
          <el-input v-model="tempG.name" v-trim :maxlength="20"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="tempG.remark" type="textarea" rows="2" resize="none" maxlength="100" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus.indexOf('create') === 0 ? createData() : updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <app-import-table
      ref="appImportTable"
      :os-type="osType"
      :add-group-btn="false"
      :type-tree-data="typeTreeData"
      :group-root-name="$t('pages.softAssetGroup')"
      :list="getSoftInfoPage"
      :count-by-group="countInfoByGroupId"
      :create="createSoftInfo"
      :batch-create="batchAddSoftInfo"
      :update="updateSoftInfo"
      :delete="deleteSoftInfo"
      :import-func="importSoftInfoFromLib"
      :create-group="createSoftType"
      :update-group="updateSoftType"
      :delete-group="deleteSoftType"
      :get-group-by-name="getSoftTypeByName"
      support-select-terminal-soft
      @submitEnd="appMd5ImportSubmitEnd"
    />
  </div>
</template>

<script>
import {
  createSoftType, updateSoftType, deleteSoftType, getSoftTypeByName, getSoftTypeTreeNode, createSoftInfo,
  batchAddSoftInfo, updateSoftInfo, deleteSoftInfo, importSoftInfoFromLib, getSoftInfoPage, countInfoByGroupId,
  updateSoftwareInfo, listSoftwareGroup, getSoftwareGroupByName, createSoftwareGroup, updateSoftwareGroup, deleteoftwareGroup,
  getSoftwareExtInfo
} from '@/api/softwareManage/assets/assetsView'
import { deepMerge } from '@/utils'
import AppImportTable from '@/views/system/baseData/appLibrary/appImportTableDlg'

export default {
  name: 'SoftwareInfo',
  components: { AppImportTable },
  props: {},
  data() {
    return {
      colModel: [
        { prop: 'processName', label: 'processName', width: '150' },
        { prop: 'processDesc', label: 'processDesc', width: '150' },
        {
          label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleProcessUpdate }
          ]
        }
      ],
      colModel1: [
        { prop: 'processName', label: 'processName', width: '150', formatter: this.nameFormatter },
        { prop: 'isCheckMd5', label: 'checkMd5Label', width: '150', formatter: this.md5LevelFormatter }
        // { prop: 'processType', label: '类型', width: '150', formatter: this.processTypeFormatter }
      ],
      softwareExtInfo: [
        { label: this.$t('pages.softwareType'), prop: 'softwareType', option: [] },
        { label: this.$t('pages.chargeType'), prop: 'chargeType', option: [] },
        { label: this.$t('pages.industryType'), prop: 'industryType', option: [] }
      ],
      md5LevelMap: {
        1: this.$t('pages.md5LevelMap1'),
        2: this.$t('pages.md5LevelMap2'),
        3: this.$t('pages.md5LevelMap3')
      },
      processTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      rules: {
        name: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      processDatas: [],
      defaultTempExt: {
        softwareName: '',
        publisher: '',
        chargeType: '',
        softwareType: '',
        industryType: '',
        softwareDesc: '',
        softwareId: ''
      },
      tempB: '', // 表单备份字符串字段
      temp: {}, // 表单字段
      defaultTemp: {
        softwareName: '',
        id: undefined,
        softwareExtInfo: {},
        publisher: '',
        appInfos: []
      },
      tempG: {}, // 表单字段
      defaultTempG: {
        id: undefined,
        type: undefined,
        name: '',
        remark: ''
      },
      dialogProcessVisible: false,
      dialogFormVisible: false,
      submitting: false,
      textMap: {
        createS: this.$t('pages.addSoftwareType'),
        updateS: this.$t('pages.editSoftwareType'),
        createC: this.$t('pages.addChargeType'),
        updateC: this.$t('pages.editChargeType'),
        createI: this.$t('pages.addIndustryType'),
        updateI: this.$t('pages.editIndustryType')
      },
      dialogStatus: '',
      deleteable: false,
      tableClass: '',
      osType: 1, // 进程系统类型：1-windows，2-linux，4-mac
      typeTreeDataWindow: [],
      typeTreeDataLinux: [],
      typeTreeDataMac: [],
      softwareTypes: [],
      chargeTypes: [],
      industryTypes: [],
      oldTypeName: '',
      noSelectting: true
    }
  },
  computed: {
    typeTreeData() {
      if (this.osType == 1) {
        return this.typeTreeDataWindow
      } else if (this.osType == 2) {
        return this.typeTreeDataLinux
      } else if (this.osType == 4) {
        return this.typeTreeDataMac
      }
      return null
    }
  },
  created() {
    this.loadAppTypeTreeTree()
    this.initSoftwareGroups()
    this.temp = Object.assign({}, this.defaultTemp)
  },
  activated() {
  },
  methods: {
    createSoftInfo,
    batchAddSoftInfo,
    updateSoftInfo,
    deleteSoftInfo,
    importSoftInfoFromLib,
    getSoftInfoPage,
    countInfoByGroupId,
    createSoftType,
    updateSoftType,
    deleteSoftType,
    getSoftTypeByName,
    resetTemp() {
      this.temp = deepMerge({ user: { name: '' }}, this.defaultTemp)
      this.temp.softwareExtInfo = Object.assign({}, this.defaultTempExt)
    },
    resetTempG() {
      this.tempG = Object.assign({}, this.defaultTempG)
    },
    // 判断两个对象是否相等
    isEqual() {
      const t = JSON.stringify(this.temp)
      return this.tempB == t
    },
    show(row, dialogStatus, originalSearch) {
      this.resetTemp()
      if (dialogStatus) {
        this.dialogStatus = dialogStatus
      }
      this.temp = Object.assign(this.temp, row)
      if (originalSearch) {
        this.$set(this.temp, 'softwareName', this.temp.originalName)
      }
      this.temp.softwareExtInfo = Object.assign(this.defaultTempExt, this.temp.softwareExtInfo)
      // let softwareNames = row.softwareName
      // if (row['originalNames']) {
      //   softwareNames = softwareNames + ',' + row['originalNames']
      // }
      getSoftwareExtInfo({ softwareName: row.softwareName, originalNames: row['originalNames'] }).then(resp => {
        if (resp.data) {
          if (!resp.data.publisher) { // 定义publisher字段
            resp.data['publisher'] = ''
          }
          this.temp.softwareExtInfo = Object.assign(this.defaultTempExt, this.temp.softwareExtInfo, resp.data)
          if (this.temp.softwareExtInfo.appInfo) {
            this.$set(this.temp, 'appInfos', JSON.parse(this.temp.softwareExtInfo.appInfo))
          }
          if (!this.temp.softwareExtInfo.publisher) {
            this.$set(this.temp.softwareExtInfo, 'publisher', this.temp.publisher)
          }
          if (!this.temp.softwareExtInfo.softwareName) {
            this.$set(this.temp.softwareExtInfo, 'softwareName', this.temp.softwareName)
          }
        } else {
          if (!this.temp.softwareExtInfo.publisher) {
            this.$set(this.temp.softwareExtInfo, 'publisher', this.temp.publisher)
          }
        }
        this.tempB = JSON.stringify(this.temp)
        this.$nextTick(() => {
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
          this.$refs['processGrid'].execRowDataApi()
        })
      })
    },
    loadAppTypeTreeTree: function() {
      getSoftTypeTreeNode({ osType: 1 }).then(respond => {
        this.typeTreeDataWindow = respond.data
      })
      getSoftTypeTreeNode({ osType: 2 }).then(respond => {
        this.typeTreeDataLinux = respond.data
      })
      getSoftTypeTreeNode({ osType: 4 }).then(respond => {
        this.typeTreeDataMac = respond.data
      })
    },
    initSoftwareGroups() {
      // 初始化所有的类型树，1：软件类型树，2：收费类型树，3：行业类型树
      [1, 2, 3].forEach(type => {
        listSoftwareGroup(type).then(respond => {
          this.softwareExtInfo[type - 1].option = respond.data
        })
      })
    },
    typeChange(data) {
      if (!this.noSelectting) {
        const prop = this.softwareExtInfo[data - 1].prop
        this.temp.softwareExtInfo[prop] = this.oldTypeName
      }
      this.noSelectting = true
      this.oldTypeName = ''
      return ''
    },
    addNode(type) {
      this.resetTempG()
      this.dialogFormVisible = true
      this.tempG.type = type
      this.dialogStatus = { 1: 'createS', 2: 'createC', 3: 'createI' }[type]
      this.$nextTick(() => {
        this.$refs['groupDataForm'].clearValidate()
      })
    },
    editNode(data) {
      this.resetTempG()
      const type = data.type
      const prop = this.softwareExtInfo[type - 1].prop
      this.noSelectting = false
      this.oldTypeName = this.temp.softwareExtInfo[prop]
      this.updateSelected = this.oldTypeName === data.name
      this.tempG = Object.assign(this.tempG, data)
      this.dialogFormVisible = true
      this.dialogStatus = { 1: 'updateS', 2: 'updateC', 3: 'updateI' }[type]
      this.$nextTick(() => {
        // this.noSelectting = true
        this.$refs['groupDataForm'].clearValidate()
      })
    },
    removeNode(data, index) {
      const info = this.softwareExtInfo[data.type - 1]
      const prop = info.prop
      const selected = this.temp.softwareExtInfo[prop]
      this.noSelectting = false
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg')).then(() => {
        deleteoftwareGroup(data.id).then(respond => {
          const option = info.option
          option.splice(index, 1)
          this.temp.softwareExtInfo[prop] = selected === data.name ? '' : selected
          this.oldTypeName = selected === data.name ? '' : selected
          this.noSelectting = true
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
        this.temp.softwareExtInfo[prop] = selected
        this.oldTypeName = selected
      })
    },
    saveSoftware() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          updateSoftwareInfo(this.temp).then(respond => {
            this.submitting = false
            this.tempB = JSON.stringify(this.temp)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.software_Msg18'),
              type: 'success',
              duration: 2000
            })
            this.$emit('submitEnd')
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    appAddSubmitEnd(data, dlgStatus) {
      if (data.innerAdd) {
        return
      }
      this.temp.appInfos.unshift(data)
    },
    appMd5ImportSubmitEnd(data, editMode) {
      // editMode编辑模式，0-删除，1-添加程序
      if (editMode === 0) {
        // 创建待删除键的集合
        const deleteKeys = new Set(data.map(({ id }) => `${id}`));
        // 使用filter一次性过滤
        this.temp.appInfos = this.temp.appInfos.filter(({ id }) =>
          !deleteKeys.has(`${id}`)
        );
      } else if (editMode === 1) {
        const length = data.length + this.temp.appInfos.length
        if (data && !data[0].innerAdd && length > 10) {
          this.$message({
            title: this.$t('text.fail'),
            message: this.$t('pages.softwareProcessAssociationFailedTips'),
            type: 'warning',
            duration: 2000
          })
        } else {
          // 创建一个 Map 用于存储列表中的元素，以方便快速查找
          const listMap = new Map();
          this.temp.appInfos.forEach((item) => {
            const key1 = `${item.id}`;
            const key2 = `${item.processName}-${item.fileMd5}-${item.checkMd5}`;
            listMap.set(key1, item);
            listMap.set(key2, item);
          });
          data.forEach(item => {
            const key1 = `${item.id}`;
            const key2 = `${item.processName}-${item.fileMd5}-${item.checkMd5}`;
            const existApp = listMap.get(key1) || listMap.get(key2);
            if (!existApp) {
              this.appAddSubmitEnd(item)
            } else {
              const { md5Level, checkMd5, typeId, relaFingerPrint } = item
              Object.assign(existApp, { md5Level, checkMd5, typeId, relaFingerPrint })
            }
          })
        }
      }
    },
    handleProcessCreate() {
      this.$refs.appImportTable.showBatchCreate()
    },
    handleImportAppFromSelfLib() {
      this.$refs.appImportTable.show()
    },
    handleProcessDelete() {
      let msg = this.$t('pages.software_Msg19')
      const rows = this.$refs['processGrid'].getSelectedDatas()
      const allDatas = this.$refs['processGrid'].getDatas()
      if (rows.length === allDatas.length) {
        msg = this.$t('pages.software_Msg37')
      }
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        const rows = this.$refs['processGrid'].getSelectedDatas()
        // 创建待删除键的集合
        const deleteKeys = new Set(rows.map(({ id, processType }) => `${id}-${processType}`));
        // 使用filter一次性过滤
        this.temp.appInfos = this.temp.appInfos.filter(({ id, processType }) =>
          !deleteKeys.has(`${id}-${processType}`)
        );
      }).catch(() => {})
    },
    processSelectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    createData() {
      this.submitting = true
      this.$refs['groupDataForm'].validate((valid) => {
        if (valid) {
          createSoftwareGroup(this.tempG).then(res => {
            const type = res.data.type
            this.softwareExtInfo[type - 1].option.push(res.data)
            this.submitting = false
            this.dialogFormVisible = false
            this.initSoftwareGroups(this.tempG.type)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['groupDataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.tempG)
          updateSoftwareGroup(tempData).then(res => {
            const type = res.data.type
            const { option, prop } = this.softwareExtInfo[type - 1]
            const index = option.findIndex(opt => opt.id == res.data.id)
            option.splice(index, 1, res.data)
            if (this.updateSelected) {
              this.temp.softwareExtInfo[prop] = res.data.name
            }
            this.submitting = false
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    resetTempP() {
      this.tempP = Object.assign({}, this.defaultTempP)
    },
    handleProcessUpdate(row) {
      this.tempP = Object.assign(this.tempP, row)
      this.dialogProcessVisible = true
      this.dialogStatus = 'update'
    },
    handleSelectionChange(val) {
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    saveOrder() {
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.saveSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    md5LevelFormatter(row, data) {
      if (row.checkMd5 === 0) {
        return this.$t('pages.software_Msg20')
      }
      return this.md5LevelMap[row.md5Level] ? this.md5LevelMap[row.md5Level] : this.md5LevelMap[row.relaFingerPrint[0].checkMd5]
    },
    nameFormatter(row, data) {
      let msg = ''
      if (row.processType == 2) {
        this.temp.appInfos.some(node => {
          if (node.dataId == row.id) {
            msg = node.label
            return true
          }
        })
      } else {
        msg = row.processName
      }
      return msg
    },
    processTypeFormatter(row, data) {
      return this.processTypeMap[data]
    },
    nameValidator(rule, value, callback) {
      getSoftwareGroupByName({ type: this.tempG.type, name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.tempG.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-form-item__label{
    padding-top: 5px;
  }
  >>>.el-form-item{
    margin-bottom: 5px;
  }
  .add-btn {
    margin: 11px 0px 0px 15px;
    color: #68a8d0;
    cursor: pointer;
  }
  .box-card {
    height: calc(100% - 100px);
    min-height: 300px;
    margin-bottom: 5px;
    border: 1px solid #878787;
  }
</style>
