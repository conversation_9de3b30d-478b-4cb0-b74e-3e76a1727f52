<template>
  <div class="table-container">
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-refresh" size="mini" style="margin-left: 5px;" @click="handleRefresh">
        {{ $t('button.refresh') }}
      </el-button>
      <common-downloader v-permission="'158'" :name="filename" :button-name="$t('button.export')" button-type="primary" button-size="mini" @download="exportAsset"/>
      <div class="searchCon">
        <span>
          <label>{{ $t('pages.operateTypeOptions2') }}：</label>
          <el-select v-model="query.assetType" clearable is-filter :placeholder="$t('pages.all')" style="width: 150px;" @change="assetTypeChange">
            <el-option :label="$t('route.operatingSystem')" :value="2001"></el-option>
            <el-option :label="$t('route.antivirusSoftware')" :value="2002"></el-option>
            <el-option :label="$t('route.applicationSoftware')" :value="2004"></el-option>
          </el-select>
        </span>
        <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="softwareTable"
      row-key="softwareName"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :multi-select="multiSelect"
      :after-load="tableAfterLoad"
      :default-sort="{ prop: 'value' }"
      retain-pages
    />

  </div>
</template>

<script>
import {
  getSoftPageGroupByName, listSoftStatisticByName, getStatistic, exportSoftwareModuleInfo, countSoftGroupByName
} from '@/api/softwareManage/assets/assetsView'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'SoftwareModule',
  components: { CommonDownloader },
  props: {
    isShow: { // 是否处于显示状态
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filename: `${this.$t('route.' + this.$route.meta.title)}-${this.$t('pages.softwareModule')}.xlsx`,
      multiSelect: false,
      colModel: [
        { prop: 'softwareName', label: 'softwareName', width: '150', fixed: true, sort: 'custom' },
        { label: 'installNum', width: '150', type: 'button',
          buttons: [
            {
              formatter: (row) => { return getStatistic('value', 'installSize', row.statistics) },
              loadingFormatter: (row) => { return getStatistic('loading', 'installSize', row.statistics) },
              click: this.installedView
            }
          ]
        },
        { label: 'authNum', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'authorizedSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'authorizedSize', row.statistics) },
            click: this.authorizedView
          }]
        },
        { label: 'unAuthNum', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'unAuthSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'unAuthSize', row.statistics) },
            click: this.violationView
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        assetType: 2004,
        searchInfo: undefined
      },
      searchOption: {},
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        isInherit1: 0,
        isInherit2: 0,
        specialList: [],
        entityType: '',
        entityId: undefined
      },
      installSpecialList: [],
      uninstallSpecialList: [],
      dialogTerminalVisible: false,
      dialogStatus: '',
      textMap: {
        installed: this.$t('pages.softwareOrder_installed'),
        authorized: this.$t('pages.softwareOrder_authorized'),
        violation: this.$t('pages.softwareOrder_violation')
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['softwareTable']
    }
  },
  watch: {
    isShow(val) {
      val && this.gridTable && this.gridTable.execRowDataApi()
    }
  },
  created() {
  },
  methods: {
    assetTypeChange() {
      this.handleRefresh()
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      if (!this.isShow) {
        return new Promise((resolve, reject) => {
          resolve({ code: 20000, data: { total: 0, items: [] }})
        })
      }
      this.searchOption = option
      const searchQuery = Object.assign({}, this.query, option)
      if (searchQuery.searchCount === false) {
        return getSoftPageGroupByName(searchQuery)
      }
      return this.rowTotalApi(searchQuery)
    },
    async rowTotalApi(option) {
      const result = {}
      await countSoftGroupByName(option).then(resp => {
        result.total = resp.data
      })
      await getSoftPageGroupByName(option).then(resp => {
        if (resp.data) {
          resp.data.total = result.total
        }
        Object.assign(result, resp);
      })
      return result
    },
    tableAfterLoad(rowData, grid) {
      if (!rowData || rowData.length < 1) {
        return
      }
      const softNameMap = {}
      const softwareNames = []
      rowData.forEach(item => {
        softwareNames.push(item.softwareName)
        softNameMap[item.softwareName] = item
      })
      const softNameStr = Object.keys(softNameMap).join(',')
      listSoftStatisticByName({ softwareNames: softNameStr, assetType: this.query.assetType }).then(resp => {
        resp.data.forEach(item => {
          const softInfo = softNameMap[item.softwareName]
          if (softInfo) {
            softInfo.statistics = item
            this.$refs['softwareTable'].updateRowData(softInfo)
          }
        })
      })
    },
    selectionChangeEnd: function(rowDatas) {
      this.selectTableEnd(rowDatas)
    },
    handleFilter() {
      this.query.page = 1
      this.query['searchCount'] = true
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        isInherit1: 0,
        isInherit2: 0,
        specialList: [],
        entityType: '',
        entityId: undefined
      }
      this.installSpecialList = []
      this.uninstallSpecialList = []
    },
    installedView(row, data) {
      this.$router.push({ name: 'InstalledSoftware', params: { flag: 'installed', softName: row.softwareName, assetType: this.query.assetType }})
    },
    authorizedView(row, data) {
      this.$router.push({ name: 'AuthorizedInstalled', params: { flag: 'authorized', softName: row.softwareName, assetType: this.query.assetType }})
    },
    violationView(row, data) {
      this.$router.push({ name: 'IllegalInstalled', params: { flag: 'violation', softName: row.softwareName, assetType: this.query.assetType }})
    },
    exportAsset(file) {
      const searchQuery = Object.assign({}, this.query, this.searchOption)
      const opts = { file, jwt: true, topic: this.$route.name }
      exportSoftwareModuleInfo(searchQuery, opts)
    }
  }
}
</script>
