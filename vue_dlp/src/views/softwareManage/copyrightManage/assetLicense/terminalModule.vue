<template>
  <div class="table-container">
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-refresh" size="mini" style="margin-left: 5px;" @click="handleRefresh">
        {{ $t('button.refresh') }}
      </el-button>
      <common-downloader v-permission="'158'" :name="filename" :button-name="$t('button.export')" button-type="primary" button-size="mini" @download="exportAsset"/>
      <div class="searchCon">
        <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.terminalName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
    </div>
    <grid-table
      ref="terminalTable"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :default-sort="defaultSort"
      :multi-select="multiSelect"
      :after-load="tableAfterLoad"
    />

  </div>
</template>

<script>
import {
  getSoftPageGroupByTerm, listSoftStatisticByTerm, getStatistic, exportTerminalModuleInfo
} from '@/api/softwareManage/assets/assetsView'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'TerminalModule',
  components: { CommonDownloader },
  props: {
    isShow: { // 是否处于显示状态
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filename: `${this.$t('route.' + this.$route.meta.title)}-${this.$t('pages.terminalModule')}.xlsx`,
      multiSelect: false,
      defaultSort: { prop: 'terminalName', order: 'desc' },
      colModel: [
        { prop: 'terminalName', label: 'terminalName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'installedNum', label: 'installNum', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'installSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'installSize', row.statistics) },
            click: this.installedView
          }]
        },
        { prop: 'authorizedNum', label: 'authNum', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'authorizedSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'authorizedSize', row.statistics) },
            click: this.authorizedView
          }]
        },
        { prop: 'violationNum', label: 'unAuthNum', width: '150', type: 'button',
          buttons: [{
            formatter: (row) => { return getStatistic('value', 'unAuthSize', row.statistics) },
            loadingFormatter: (row) => { return getStatistic('loading', 'unAuthSize', row.statistics) },
            click: this.violationView
          }]
        }
      ],
      query: { // 查询条件
        page: 1,
        terminalName: undefined
      },
      searchOption: {},
      dialogSoftwareVisible: false,
      dialogStatus: '',
      textMap: {
        installed: this.$t('pages.terminal_installed'),
        authorized: this.$t('pages.terminal_authorized'),
        violation: this.$t('pages.terminal_violation')
      },
      curFilePath: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['terminalTable']
    }
  },
  watch: {
    isShow(val) {
      val && this.gridTable && this.gridTable.execRowDataApi()
    }
  },
  created() {
  },
  methods: {
    handleRefresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    rowDataApi: function(option) {
      if (!this.isShow) {
        return new Promise((resolve, reject) => {
          resolve({ code: 20000, data: { total: 0, items: [] }})
        })
      }
      this.searchOption = option;
      this.query = Object.assign({}, this.query, option)
      return getSoftPageGroupByTerm(this.query)
    },
    tableAfterLoad(rowData, grid) {
      if (!rowData || rowData.length < 1) {
        return
      }
      const termIdMap = {}
      rowData.forEach(item => {
        termIdMap[item.terminalId] = item
      })
      const termIdStr = Object.keys(termIdMap).join(',')
      listSoftStatisticByTerm({ terminalIds: termIdStr }).then(resp => {
        resp.data.forEach(item => {
          const softInfo = termIdMap[item.terminalId]
          softInfo.statistics = item
          this.$refs['terminalTable'].updateRowData(softInfo)
        })
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    installedView(row, data) {
      this.$router.push({ name: 'InstalledSoftware', params: { flag: 'installed', termId: row.terminalId }})
    },
    authorizedView(row, data) {
      this.$router.push({ name: 'AuthorizedInstalled', params: { flag: 'authorized', termId: row.terminalId }})
    },
    violationView(row, data) {
      this.$router.push({ name: 'IllegalInstalled', params: { flag: 'violation', termId: row.terminalId }})
    },
    exportAsset(file) {
      const searchQuery = Object.assign({}, this.query, this.searchOption)
      const opts = { file, jwt: true, topic: this.$route.name }
      exportTerminalModuleInfo(searchQuery, opts)
    }
  }
}
</script>
