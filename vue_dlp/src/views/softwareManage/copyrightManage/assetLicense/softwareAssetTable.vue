<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :treeable="treeable" :show-tree.sync="showTree" :showed-tree="['terminal']" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" size="mini" style="padding-left: 10px; margin-left: 10px;" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button size="mini" :disabled="!canSet" @click="handlePermit">
          {{ $t('pages.grantAuthor') }}
        </el-button>
        <el-button size="mini" :disabled="!canSet" @click="handleImpermit">
          {{ $t('table.cancelGrantAuthor') }}
        </el-button>
        <common-downloader v-permission="'158'" :name="filename" :button-name="$t('button.export')" button-type="primary" button-size="mini" @download="exportAsset"/>
        <div class="searchCon">
          <span>
            <label>{{ $t('pages.operateTypeOptions2') }}：</label>
            <el-select v-model="query.assetType" clearable is-filter :placeholder="$t('pages.all')" style="width: 150px;" @change="assetTypeChange">
              <el-option :label="$t('route.operatingSystem')" :value="2001"></el-option>
              <el-option :label="$t('route.antivirusSoftware')" :value="2002"></el-option>
              <el-option :label="$t('route.applicationSoftware')" :value="2004"></el-option>
            </el-select>
          </span>
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="softwareAssetTable"
        :col-model="colModel"
        row-key="logId"
        :row-data-api="rowDataApi"
        :multi-select="multiSelect"
        :default-sort="{ prop: 'value' }"
        retain-pages
        @selectionChangeEnd="selectionChangeEnd"
      />

      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="$t('pages.softwareInfo_msg23')"
        :visible.sync="dialogOverVisible"
        width="800px"
        @dragDialog="handleDrag"
      >
        <div style="margin-bottom: 10px; color: #409eff;">{{ $t('pages.softwareInfo_msg24') }}</div>
        <grid-table
          ref="overPermitList"
          :stripe="false"
          row-key="dataId"
          :height="250"
          :show-pager="false"
          :default-expand-all="true"
          :col-model="colModel1"
          :multi-select="multiSelect"
          :sub-multi-select="multiSelect"
          :row-datas="overPermitData"
          :selectable="selectable"
        />
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="submitting" @click="submitOverPermitData">{{ $t('button.confirm') }}</el-button>
          <el-button @click="dialogOverVisible = false">{{ $t('button.close') }}</el-button>
        </div>
      </el-dialog>

    </div>
  </div>
</template>

<script>
import { authorized, recycle } from '@/api/softwareManage/copyrightManage/orders'
import { exportSoftwareAssetInfo, getSoftwarePermitPage, countSoftwareInfo } from '@/api/softwareManage/assets/assetsView'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'SoftwareAssetTable',
  components: { CommonDownloader },
  props: {
    isShow: { // 是否处于显示状态
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filename: `${this.$t('route.' + this.$route.meta.title)}-${this.$t('pages.softwareAsset')}.xlsx`,
      multiSelect: true,
      colModel: [
      ],
      defaultColModel: [
      ],
      colModel1: [
        { prop: 'softwareName', label: 'softwareName', width: '150' },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '150' },
        { prop: 'terminalName', label: 'terminalName', width: '150', sort: 'custom', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'authorizeSize', label: 'authorizableNum', width: '150' }
      ],
      colModelOptions: {
        2001: [
          { prop: 'softwareName', label: 'osName', width: '150', sort: 'custom', type: 'button', iconFormatter: this.authorizedStatusIconFormatter,
            buttons: [
              { formatter: this.softwareNameFormatter, style: 'width: calc(100% - 25px);', click: this.softwareView }
            ]
          },
          { prop: 'softwareVersion', label: 'osVersion', width: '150' },
          { prop: 'terminalName', label: 'terminalName', width: '150', sort: 'custom', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
          { prop: 'summary', label: 'summary', width: '150' },
          { prop: 'serialNumber', label: 'serialNumber', width: '150' },
          { prop: 'registeredUser', label: 'registeredUser', width: '150' },
          { prop: 'publisher', label: 'developers', width: '150' },
          { prop: 'installedTime', label: 'installDate', width: '150', sort: 'custom' },
          { prop: 'lastUsedTime', label: 'lastUseTime', width: '150', sort: 'custom' },
          { prop: 'computerType', label: 'computerType', width: '150', formatter: this.computerTypeFormatter },
          { prop: 'active', label: 'activeStatus', width: '150', formatter: this.systemActiveFormatter },
          { prop: 'path', label: 'installPath', width: '150', sort: 'custom' }
        ],
        2002: [
          { prop: 'softwareName', label: 'softwareName', width: '150', sort: 'custom', type: 'button', iconFormatter: this.authorizedStatusIconFormatter,
            buttons: [
              { formatter: this.softwareNameFormatter, style: 'width: calc(100% - 25px);', click: this.softwareView }
            ]
          },
          { prop: 'softwareVersion', label: 'softwareVersion', width: '150' },
          { prop: 'terminalName', label: 'terminalName', width: '150', sort: 'custom', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
          { prop: 'publisher', label: 'developers', width: '150' }
        ],
        2004: [
          { prop: 'softwareName', label: 'softwareName', width: '150', sort: 'custom', type: 'button', iconFormatter: this.authorizedStatusIconFormatter,
            buttons: [
              { formatter: this.softwareNameFormatter, style: 'width: calc(100% - 25px);', click: this.softwareView }
            ]
          },
          { prop: 'softwareVersion', label: 'softwareVersion', width: '150' },
          { prop: 'terminalName', label: 'terminalName', width: '150', sort: 'custom', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
          { prop: 'installedTime', label: 'installDate', width: '150', sort: 'custom' },
          { prop: 'path', label: 'installPath', width: '150', sort: 'custom' },
          { prop: 'lastUsedTime', label: 'lastUseTime', width: '150', sort: 'custom' },
          { prop: 'publisher', label: 'developers', width: '150' }
        ]
      },
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        entityType: undefined,
        entityId: undefined,
        assetType: 2004
      },
      checkedEntityNode: {},
      searchOption: {},
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: {
        id: undefined,
        name: '',
        active: false,
        remark: '',
        isInherit1: 0,
        isInherit2: 0,
        specialList: [],
        entityType: '',
        entityId: undefined
      },
      termTemp: {},
      authorizedTemp: {},
      defaultAuthorizedTemp: {
        softwareName: '',
        terminalId: undefined
      },
      installSpecialList: [],
      uninstallSpecialList: [],
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.specialDirStg'), 'update'),
        create: this.i18nConcatText(this.$t('pages.specialDirStg'), 'create')
      },
      dialogOverVisible: false,
      orderDatas: [],
      computerTypeOptions: {
        0: this.$t('pages.other'),
        1: this.$t('pages.computerType1'),
        2: this.$t('pages.computerType2'),
        3: this.$t('pages.computerType3')
      },
      canSet: false,
      toPermitData: [],
      overPermitData: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['softwareAssetTable']
    }
  },
  watch: {
    isShow(val) {
      val && this.gridTable && this.gridTable.execRowDataApi()
    }
  },
  created() {
    this.intColModel()
  },
  methods: {
    selectable(row, index) {
      return !row.children
    },
    selectionChangeEnd: function(rowDatas) {
      this.canSet = rowDatas && rowDatas.length > 0
    },
    intColModel() {
      this.colModel.splice(0)
      this.colModel.push(...this.colModelOptions[this.query.assetType])
      this.colModel.push(...this.defaultColModel)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetAuthorizedTemp() {
      this.authorizedTemp = Object.assign({}, this.defaultAuthorizedTemp)
    },
    softwareView(row, data) {
      this.$router.push({ name: 'SoftwareDetail', params: { softwareInfo: row, version: row.softwareVersion, originalSearch: true }})
    },
    terminalView(row, data) {
      this.$router.push({ name: 'InstalledSoftware', params: { flag: 'installed', termId: row.terminalId }})
    },
    orderView(row, data) {
      this.$router.push({ name: 'OrderForSoftware', params: { orderIds: row.orderIds.join(',') }})
    },
    overPermitTable() {
      return this.$refs['overPermitList']
    },
    submitOverPermitData() {
      this.submitting = true
      const selectedData = this.overPermitTable().getSelectedDatas()
      const map = selectedData.reduce((acc, item) => {
        if (!acc.has(item.softwareName)) acc.set(item.softwareName, []);
        acc.get(item.softwareName).push(item);
        return acc;
      }, new Map());

      const softwareNames = []
      map.forEach((value, key) => {
        const authorizedSize = value[0].authorizedSize ? value[0].authorizedSize : 0
        if (value[0].purchaseSize - authorizedSize < value.length) {
          softwareNames.push(key)
        }
      })
      if (softwareNames.length > 0) {
        this.submitting = false
        this.$notify({
          title: this.$t('text.error'),
          message: this.$t('pages.softwareInfo_msg25', softwareNames.join(',')),
          type: 'error',
          duration: 2000
        })
      } else {
        selectedData.forEach(item => {
          const data = {
            softwareName: item.softwareName,
            terminalId: item.terminalId
          }
          this.toPermitData.push(data)
        })
        this.authorized()
      }
    },
    authorized() {
      authorized(this.toPermitData).then(respond => {
        this.dialogOverVisible = false
        this.submitting = false
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.authSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    handlePermit() {
      this.toPermitData.splice(0)
      this.overPermitData.splice(0)
      const selectedData = this.gridTable.getSelectedDatas().filter(item => !item.authorized)
      const map = selectedData.reduce((acc, item) => {
        if (!acc.has(item.softwareName)) acc.set(item.softwareName, []);
        acc.get(item.softwareName).push(item);
        return acc;
      }, new Map());

      map.forEach((value, key) => {
        const authorizedSize = value[0].authorizedSize ? value[0].authorizedSize : 0
        if (value[0].purchaseSize - authorizedSize < value.length) {
          value.forEach(item => {
            item['dataId'] = item.id.toString()
          })
          const overData = {
            dataId: 'P' + value[0].id,
            softwareName: key,
            authorizeSize: value[0].purchaseSize - authorizedSize,
            children: value
          }
          this.overPermitData.push(overData)
        } else {
          value.forEach(item => {
            const data = {
              softwareName: item.softwareName,
              terminalId: item.terminalId
            }
            this.toPermitData.push(data)
          })
        }
      })

      if (this.overPermitData.length > 0) {
        this.dialogOverVisible = true
      } else {
        this.authorized()
      }
    },
    handleImpermit() {
      const selectedData = this.gridTable.getSelectedDatas().filter(item => !!item.authorized)
      recycle(selectedData).then(respond => {
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.recycleSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    handleFilter() {
      this.handleRefresh()
    },
    handleRefresh() {
      this.query.page = 1
      this.query['searchCount'] = true
      this.gridTable.execRowDataApi(this.query)
    },
    assetTypeChange() {
      this.intColModel()
      this.handleRefresh()
    },
    rowDataApi: function(option) {
      this.searchOption = option;
      const searchQuery = Object.assign({}, this.query, option)
      if (option.searchCount === false) {
        return getSoftwarePermitPage(searchQuery)
      }
      return this.rowTotalApi(searchQuery)
    },
    async rowTotalApi(option) {
      const result = {}
      await countSoftwareInfo(option).then(resp => {
        result.total = resp.data
      })
      await getSoftwarePermitPage(option).then(resp => {
        if (resp.data) {
          resp.data.total = result.total
        }
        Object.assign(result, resp);
      })
      return result
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    typeMap(options) {
      const map = {}
      options.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    },
    computerTypeFormatter: function(row, data) {
      return data ? this.computerTypeOptions[data] : this.computerTypeOptions[0]
    },
    systemActiveFormatter: function(row, data) {
      return data ? this.$t('pages.software_Msg53') : this.$t('pages.software_Msg54')
    },
    authorizedStatusIconFormatter: function(row) {
      const icons = []
      const icon = { class: 'authorize' }
      if (row.authorized) {
        icon.style = { 'color': 'green' }
        icon.title = this.$t('pages.authorized')
      } else {
        icon.title = this.$t('pages.unauthorized')
      }
      icons.push(icon)
      return icons
    },
    softwareNameFormatter: function(row, data) {
      return row.softwareName
    },
    exportAsset(file) {
      const searchQuery = Object.assign({}, this.query, this.searchOption)
      const opts = { file, jwt: true, topic: this.$route.name }
      exportSoftwareAssetInfo(searchQuery, opts)
    }
  }
}
</script>
