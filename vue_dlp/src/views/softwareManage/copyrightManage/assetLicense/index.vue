<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.softwareAsset')" name="asset">
        <Software-asset-table ref="softwareAssetTable" :is-show="isAssetShow"></Software-asset-table>
      </el-tab-pane>
      <el-tab-pane :label="$t('pages.softwareModule')" name="software">
        <Software-module ref="softwareModule" :is-show="isSoftwareShow"></Software-module>
      </el-tab-pane>
      <el-tab-pane :label="$t('pages.terminalModule')" name="terminal">
        <Terminal-module ref="terminalModule" :is-show="isTermShow"></Terminal-module>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import SoftwareAssetTable from './softwareAssetTable'
import SoftwareModule from './softwareModule'
import TerminalModule from './terminalModule'

export default {
  name: 'SoftwareAssetLicense',
  components: { TerminalModule, SoftwareModule, SoftwareAssetTable },
  data() {
    return {
      activeName: 'asset',
      isAssetShow: true,
      isSoftwareShow: false,
      isTermShow: false
    }
  },
  watch: {
    activeName(val) {
      this.changeShowStatus(val)
    }
  },
  created() {},
  activated() {
    // 切换显示状态，用于刷新数据，否则会出现授权信息不正确，或者不应该授权的还能授权
    this.changeShowStatus('')
    this.$nextTick(() => {
      this.changeShowStatus(this.activeName)
    })
  },
  methods: {
    tabClick(pane, event) {
      const that = this
      setTimeout(function() {
        if (that.activeName == 'asset') {
          // pane.$children[0].$refs.softwareAssetTable.execRowDataApi()
        }
      }, 0)
    },
    changeShowStatus(val) {
      this.isAssetShow = val === 'asset'
      this.isSoftwareShow = val === 'software'
      this.isTermShow = val === 'terminal'
    }
  }
}
</script>
<style scoped>
.table-container{
  padding: 5px;
}
</style>
