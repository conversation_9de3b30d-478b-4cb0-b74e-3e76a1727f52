<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-back" size="mini" @click="back">
          {{ $t('pages.software_Msg21', { info: title }) }}
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" size="mini" style="padding-left: 10px;" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <el-button size="mini" :disabled="!canSet" @click="handlePermit">
          {{ $t('pages.grantAuthor') }}
        </el-button>
        <el-button size="mini" :disabled="!canSet" @click="handleImpermit">
          {{ $t('table.cancelGrantAuthor') }}
        </el-button>
        <el-button v-if="!assetTypeShow" type="primary" icon="el-icon-refresh" size="mini" style="margin-left: 5px;" :disabled="!canSet" @click="handleReset">
          {{ $t('pages.softwareInfo_msg10') }}
        </el-button>
        <common-downloader :loading="submitting" :name="filename" :button-name="$t('button.export')" button-size="mini" @download="exportAsset"/>
        <div class="searchCon">
          <span v-if="assetTypeShow">
            <label>{{ $t('pages.operateTypeOptions2') }}：</label>
            <el-select v-model="query.assetType" clearable is-filter :placeholder="$t('pages.all')" style="width: 150px;" @change="assetTypeChange">
              <el-option :label="$t('route.operatingSystem')" :value="2001"></el-option>
              <el-option :label="$t('route.antivirusSoftware')" :value="2002"></el-option>
              <el-option :label="$t('route.applicationSoftware')" :value="2004"></el-option>
            </el-select>
          </span>
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        :ref="tableRefOptions[query.assetType]"
        :key="tableRefOptions[query.assetType]"
        is-saved-selected
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="multiSelect"
        :custom-col="true"
        :default-sort="{ prop: 'softwareName' }"
        @selectionChangeEnd="selectionChangeEnd"
      />

      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="$t('pages.softwareInfo_msg10')"
        :visible.sync="dialogFormVisible"
        width="450px"
        @dragDialog="handleDrag"
      >
        <Form ref="dataForm" :rules="rules" :model="tempD" label-position="right" label-width="80px" style="width: 300px; margin-left:20px;">
          <span style="color: #0c60a5; margin-bottom: 10px;">{{ $t('pages.softwareInfo_msg11') + selection }}</span>
          <FormItem :label="$t('pages.softwareInfo_msg12')" prop="softwareName">
            <el-input v-model="tempD.softwareName" maxlength="255" />
          </FormItem>
        </Form>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="submitting" type="primary" @click="saveSoftwareInfoDef">
            {{ $t('button.confirm') }}
          </el-button>
          <el-button @click="dialogFormVisible = false">
            {{ $t('button.cancel') }}
          </el-button>
        </div>
      </el-dialog>

      <!-- 资产配置弹窗 -->
      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="$t('pages.softwareInfo_msg20')"
        :visible.sync="dialogFormVisible2"
        width="800px"
      >
        <Form
          ref="dataForm"
          :model="temp"
          :hide-required-asterisk="true"
          :rules="rules"
          label-width="90px"
        >
          <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
            <div slot="header">
              <span>{{ $t('pages.softwareInfo_msg21') }}</span>
            </div>
            <el-row>
              <el-col :span="12">
                <FormItem :label="softwareNameOptions[temp.assetType]" prop="originalName">
                  <el-col :span="20">
                    <el-input v-model="temp.originalName" maxlength="128" :title="temp.originalName" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="softwareVersionOptions[temp.assetType]" prop="softwareVersion">
                  <el-col :span="20">
                    <el-input v-model="temp.softwareVersion" maxlength="128" :title="temp.softwareVersion" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.summary')" prop="summary">
                  <el-col :span="20">
                    <el-input v-model="temp.summary" maxlength="128" :title="temp.summary" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.serialNumber')" prop="serialNumber">
                  <el-col :span="20">
                    <el-input v-model="temp.serialNumber" maxlength="128" :title="temp.serialNumber" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.registeredUser')" prop="registeredUser">
                  <el-col :span="20">
                    <el-input v-model="temp.registeredUser" maxlength="128" :title="temp.registeredUser" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('table.developers')" prop="publisher">
                  <el-col :span="20">
                    <el-input v-model="temp.publisher" maxlength="128" :title="temp.publisher" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType !== 2002" :span="12">
                <FormItem :label="$t('table.installTime')" prop="installedTime">
                  <el-col :span="20">
                    <el-input v-model="temp.installedTime" maxlength="128" :title="temp.installedTime" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType !== 2002" :span="12">
                <FormItem :label="$t('table.lastUseTime')" prop="lastUsedTime">
                  <el-col :span="20">
                    <el-input v-model="temp.lastUsedTime" maxlength="128" :title="temp.lastUsedTime" :disabled="true" />
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.computerType')" prop="computerType">
                  <el-col :span="20">
                    <el-select v-model="temp.computerType" :disabled="true">
                      <el-option :label="$t('pages.other')" :value="0"/>
                      <el-option :label="$t('pages.computerType1')" :value="1"/>
                      <el-option :label="$t('pages.computerType2')" :value="2"/>
                      <el-option :label="$t('pages.computerType3')" :value="3"/>
                    </el-select>
                  </el-col>
                </FormItem>
              </el-col>
              <el-col v-if="temp.assetType == 2001" :span="12">
                <FormItem :label="$t('table.activeStatus')" prop="active">
                  <el-col :span="20">
                    <el-select v-model="temp.active" :disabled="true">
                      <el-option :label="$t('pages.software_Msg54') " :value="0"/>
                      <el-option :label="$t('pages.software_Msg53') " :value="1"/>
                    </el-select>
                  </el-col>
                </FormItem>
              </el-col>
            </el-row>
          </el-card>
        </Form>
        <el-card class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.softwareInfo_msg22') }}</span>
          </div>
          <el-container>
            <el-main>
              <grid-table
                ref="detailTable"
                :show-pager="false"
                :height="410"
                :autoload="false"
                :multi-select="false"
                :col-model="detailColModel"
                :row-datas="customSoftAssetInfos"
              />
            </el-main>
          </el-container>
        </el-card>
        <div style="line-height: 20px; padding-top: 3px; color: #409eff; font-size: small; font-weight:bold;">
          {{ $t('pages.hardAssetCustomeTip') }}
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="submitting" type="primary" @click="handleSave()">
            {{ $t('button.save') }}
          </el-button>
          <el-button @click="dialogFormVisible2 = false">
            {{ $t('button.close') }}
          </el-button>
        </div>
      </el-dialog>

      <asset-prop-def ref="assetPropDef" :type="type" :parent-id="query.assetType" @submitEnd="submitEnd"/>

      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="$t('pages.softwareInfo_msg23')"
        :visible.sync="dialogOverVisible"
        width="800px"
        @dragDialog="handleDrag"
      >
        <div style="margin-bottom: 10px; color: #409eff;">{{ $t('pages.softwareInfo_msg24') }}</div>
        <grid-table
          ref="overPermitList"
          :stripe="false"
          row-key="dataId"
          :height="250"
          :show-pager="false"
          :default-expand-all="true"
          :col-model="colModel1"
          :multi-select="multiSelect"
          :sub-multi-select="multiSelect"
          :row-datas="overPermitData"
          :selectable="selectable"
        />
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="submitting" @click="submitOverPermitData">{{ $t('button.confirm') }}</el-button>
          <el-button @click="dialogOverVisible = false">{{ $t('button.close') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getTerminalSoftwarePage,
  exportSoftInfo,
  saveSoftwareDef,
  getSoftAssetInfoDetail,
  saveCustomSoftAssetInfo
} from '@/api/softwareManage/assets/assetsView'
import { authorized, recycle } from '@/api/softwareManage/copyrightManage/orders'
import { callSingle } from '@/utils'
import CommonDownloader from '@/components/DownloadManager/common'
import AssetPropDef from '@/views/softwareManage/components/assetPropDef'
import { getCustomAssetColumns } from '@/api/assets/assetsConfig/assetsView'

export default {
  name: 'SoftwareList',
  components: { CommonDownloader, AssetPropDef },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      filename: this.$t('route.' + this.$route.meta.title) + '.xlsx',
      customCol: true,
      multiSelect: true,
      colModel: [],
      colModel1: [
        { prop: 'softwareName', label: 'softwareName', width: '150' },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '150' },
        { prop: 'terminalName', label: 'terminalName', width: '150', sort: 'custom', type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
        { prop: 'authorizeSize', label: 'authorizableNum', width: '150' }
      ],
      operateButton: [
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'assetConfig', title: this.$t('table.assetConfig'), click: this.handleAssetConfig }
          ]
        }
      ],
      detailColModel: [
        { prop: 'name', label: 'params', width: '100' },
        { prop: 'value', label: 'value', width: '200', type: 'input', editMode: true, showText: this.showTextFormatter }
      ],
      colModelOptions: {
        2001: [
          { prop: 'softwareName', label: 'osName', width: '150', sort: 'custom', fixed: true, formatter: this.softwareNameFormatter, iconFormatter: this.authorizedStatusIconFormatter },
          { prop: 'terminalName', label: 'terminalName', width: '150', fixed: true, type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
          { prop: 'softwareVersion', label: 'osVersion', width: '150', formatter: this.versionFormatter },
          { prop: 'mainIp', label: 'IP', width: '150' },
          { prop: 'mainMac', label: 'macAddr', width: '150' },
          { prop: 'groupName', label: 'terminalGroup', width: '150', type: 'showDetail', searchType: 'department', searchParam: 'groupId' },
          { prop: 'summary', label: 'summary', width: '150' },
          { prop: 'serialNumber', label: 'serialNumber', width: '150' },
          { prop: 'registeredUser', label: 'registeredUser', width: '150' },
          { prop: 'publisher', label: 'developers', width: '150' },
          { prop: 'installedTime', label: 'installTime', width: '150' },
          { prop: 'lastUsedTime', label: 'lastUseTime', width: '150' },
          { prop: 'computerType', label: 'computerType', width: '150', formatter: this.computerTypeFormatter },
          { prop: 'active', label: 'activeStatus', width: '150', formatter: this.systemActiveFormatter },
          { prop: 'path', label: 'installPath', width: '150' }
        ],
        2002: [
          { prop: 'softwareName', label: 'softwareName', width: '150', sort: 'custom', fixed: true, formatter: this.softwareNameFormatter, iconFormatter: this.authorizedStatusIconFormatter },
          { prop: 'terminalName', label: 'terminalName', width: '150', fixed: true, type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
          { prop: 'softwareVersion', label: 'softwareVersion', width: '150', formatter: this.versionFormatter },
          { prop: 'mainIp', label: 'IP', width: '150' },
          { prop: 'mainMac', label: 'macAddr', width: '150' },
          { prop: 'groupName', label: 'terminalGroup', width: '150', type: 'showDetail', searchType: 'department', searchParam: 'groupId' },
          { prop: 'publisher', label: 'developers', width: '150' }
        ],
        2004: [
          { prop: 'softwareName', label: 'softwareName', width: '150', sort: 'custom', fixed: true, formatter: this.softwareNameFormatter, iconFormatter: this.authorizedStatusIconFormatter },
          { prop: 'terminalName', label: 'terminalName', width: '150', fixed: true, type: 'showDetail', searchType: 'terminal', searchParam: 'terminalId' },
          { prop: 'softwareVersion', label: 'softwareVersion', width: '150', formatter: this.versionFormatter },
          { prop: 'mainIp', label: 'IP', width: '150' },
          { prop: 'mainMac', label: 'macAddr', width: '150' },
          { prop: 'groupName', label: 'terminalGroup', width: '150', type: 'showDetail', searchType: 'department', searchParam: 'groupId' },
          { prop: 'installedTime', label: 'installTime', width: '150' },
          { prop: 'path', label: 'installPath', width: '150' },
          { prop: 'lastUsedTime', label: 'lastUseTime', width: '150' },
          { prop: 'publisher', label: 'developers', width: '150' }
        ]
      },
      query: { // 查询条件
        page: 1,
        assetType: 2004,
        objectType: undefined,
        objectId: undefined,
        terminalId: undefined,
        softwareNames: '',
        softwareName: '',
        originalName: '',
        softwareVersion: '',
        searchInfo: '',
        menuCode: this.$route.meta.code,
        tableKey: 'softwareTable'
      },
      assetTypeShow: true,
      dialogSoftwareVisible: false,
      submitting: false,
      btnLoading: false,
      dialogStatus: '',
      textMap: {
        installed: this.$t('pages.software_Msg25'),
        authorized: this.$t('pages.software_Msg26'),
        violation: this.$t('pages.software_Msg27')
      },
      parentPage: '',
      initFlag: '',
      authorizedTemp: {},
      defaultAuthorizedTemp: {
        softwareName: '',
        terminalId: undefined
      },
      canSet: false,
      type: 2,
      dialogFormVisible: false,
      dialogFormVisible1: false,
      dialogFormVisible2: false,
      tempD: {},
      defaultTempD: {
        softwareName: ''
      },
      temp: {},
      defaultTemp: {
        id: undefined,
        softwareName: '',
        originalName: '',
        publisher: '',
        softwareVersion: '',
        path: '',
        installedTime: '',
        lastUsedTime: '',
        summary: '',
        serialNumber: '',
        registeredUser: '',
        computerType: 0,
        active: 0,
        assetType: 2004
      },
      rules: {
        softwareName: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }
        ]
      },
      selection: [],
      computerTypeOptions: {
        0: this.$t('pages.other'),
        1: this.$t('pages.computerType1'),
        2: this.$t('pages.computerType2'),
        3: this.$t('pages.computerType3')
      },
      tableRefOptions: {
        2001: 'osTable',
        2002: 'antivirusTable',
        2004: 'softwareTable'
      },
      customSoftAssetInfos: [],
      softwareNameOptions: {
        2001: this.$t('table.osName'),
        2002: this.$t('table.softwareName'),
        2004: this.$t('table.softwareName')
      },
      softwareVersionOptions: {
        2001: this.$t('table.osVersion'),
        2002: this.$t('table.softwareVersion'),
        2004: this.$t('table.softwareVersion')
      },
      dialogOverVisible: false,
      toPermitData: [],
      overPermitData: []
    }
  },
  computed: {
    rootPidValue() {
      return this.query.assetType ? this.query.assetType : this.$route.params.assetType
    }
  },
  watch: {
    dialogFormVisible: function(val) {
      if (!val) {
        this.$nextTick(() => {
          this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        })
      }
    }
  },
  created() {
    callSingle(this, this.initSoftwareInfo)
  },
  activated() {
    callSingle(this, this.initSoftwareInfo)
  },
  methods: {
    selectable(row, index) {
      return !row.children
    },
    assetTypeChange() {
      this.handleRefresh()
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    gridTable() {
      return this.$refs[this.tableRefOptions[this.query.assetType]]
    },
    back() {
      this.$emit('back')
    },
    initSoftwareInfo() {
      const { flag, termId, softName, originalName, softVer, assetType, objectType, objectId } = this.$route.params
      this.initFlag = flag
      this.show(flag, termId, softName, originalName, softVer, assetType, objectType, objectId)
    },
    resetQuery() {
      this.query.page = 1
      this.query.terminalId = null
      this.query.softwareNames = null
      this.query.softwareName = null
      this.query.originalName = null
      this.query.softwareVersion = null
    },
    show(data, termId, softName, originalName, softVer, assetType, objectType, objectId) {
      this.resetQuery()
      this.dialogStatus = data
      this.dialogSoftwareVisible = true
      if (termId) {
        this.query.terminalId = termId
      }
      if (softName) {
        this.query.softwareName = softName
      }
      if (originalName) {
        this.query.originalName = originalName
      }
      if (softVer) {
        this.query.softwareVersion = softVer
      }
      if (assetType) {
        this.assetTypeShow = false
        this.query.assetType = assetType
        this.query.tableKey = this.tableRefOptions[assetType]
      } else {
        this.assetTypeShow = true
        // 默认展示应用软件
        this.query.assetType = 2004
      }
      if (objectType) {
        this.query.objectType = objectType
      }
      if (objectId) {
        this.query.objectId = objectId
      }
      if (data) {
        this.query.flag = data
      }
      this.$nextTick(() => {
        this.loadTableColumn()
        this.handleRefresh()
      })
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable() && this.gridTable().execRowDataApi(this.query)
      this.gridTable() && this.gridTable().clearSaveSelection()
    },
    rowDataApi: function(option) {
      if (!this.query.flag) {
        return new Promise((resolve, reject) => {
          resolve({ code: 20000, data: { total: 0, items: [] }})
        })
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getTerminalSoftwarePage(searchQuery)
    },
    handleDrag() {},
    resetTempD() {
      this.tempD = Object.assign({}, this.defaultTempD)
    },
    selectionChangeEnd: function(rowDatas) {
      this.canSet = rowDatas && rowDatas.length > 0
    },
    loadTableColumn() { // 加载表头
      this.colModel.splice(0)
      getCustomAssetColumns({ type: this.type, parentId: this.query.assetType }).then(respond => {
        if (respond.data && respond.data.length > 0) {
          respond.data.forEach(item => {
            item.prop = 'customAssetInfoMap.' + item.prop
            item.formatter = this.dataFormatter
          })
          this.colModel = [...this.colModelOptions[this.query.assetType], ...respond.data, ...this.operateButton]
        } else {
          this.colModel = [...this.colModelOptions[this.query.assetType], ...this.operateButton]
        }
        this.$nextTick(() => {
          this.gridTable().execRowDataApi()
        })
      })
    },
    handleAddAttribute() {
      this.$refs.assetPropDef.show()
    },
    submitEnd(param) {
      let customColKeys = this.gridTable().customColKeys
      let customKeys = this.gridTable().customKeys
      // 新增自定义属性，那么表格列数组长度+1，操作栏作为最后一个元素，下标需要+1，并更新显示列下标数据
      if (param.type == 'create') {
        const max = Math.max(...customColKeys);
        let maxValueIndex
        customColKeys.forEach((item, index) => {
          if (item == max) {
            maxValueIndex = index
          }
        })
        this.gridTable().customColKeys[maxValueIndex] = max + 1
        if (customKeys.length === 0) {
          this.gridTable().customKeys = Array.from(new Set([...this.gridTable().customColKeys]))
        } else {
          customKeys.forEach((item, index) => {
            if (item == max) {
              maxValueIndex = index
            }
          })
          this.gridTable().customKeys[maxValueIndex] = max + 1
        }
      } else if (param.type == 'delete') { // 删除自定义属性，对应的自定义显示列下标删除，对应显示的自定义列下标之后的所有下标全部对应-1，并更换自定义列下标数据
        const indexes = param.indexes.map(i => i + this.colModelOptions[this.query.assetType].length)
        customColKeys = customColKeys.filter(item => !indexes.includes(item))
        this.gridTable().customColKeys = customColKeys.map(item => {
          const value = item
          indexes.forEach(item1 => {
            if (value > item1) {
              item--
            }
          })
          return item
        })
        if (customKeys.length === 0) {
          this.gridTable().customKeys = Array.from(new Set([...this.gridTable().customColKeys]))
        } else {
          customKeys = customKeys.filter(item => !indexes.includes(item))
          this.gridTable().customKeys = customKeys.map(item => {
            const value = item
            indexes.forEach(item1 => {
              if (value > item1) {
                item--
              }
            })
            return item
          })
        }
      }
      if (this.gridTable().customKeys.length > 0) {
        this.gridTable().saveCustomCol()
      }
      this.loadTableColumn()
    },
    handleReset() {
      this.resetTempD()
      this.selection.splice(0)
      const selectData = this.gridTable().getSelectedDatas()
      const softwareNames = selectData.map(item => item.originalName)
      this.selection = softwareNames.reduce((pre, cur) => {
        if (pre.indexOf(cur) < 0) {
          pre.push(cur)
        }
        return pre
      }, [])
      this.dialogFormVisible = true
    },
    saveSoftwareInfoDef() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          const that = this
          saveSoftwareDef({ softwareName: this.tempD.softwareName, originalNames: this.selection.join(',') }).then(respond => {
            that.dialogFormVisible = false
            that.submitting = false
            that.canSet = false
            that.handleRefresh()
            that.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.submitSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    },
    resetAuthorizedTemp() {
      this.authorizedTemp = Object.assign({}, this.defaultAuthorizedTemp)
    },
    overPermitTable() {
      return this.$refs['overPermitList']
    },
    submitOverPermitData() {
      this.submitting = true
      const selectedData = this.overPermitTable().getSelectedDatas()
      const map = selectedData.reduce((acc, item) => {
        if (!acc.has(item.softwareName)) acc.set(item.softwareName, []);
        acc.get(item.softwareName).push(item);
        return acc;
      }, new Map());

      const softwareNames = []
      map.forEach((value, key) => {
        const authorizedSize = value[0].authorizedSize ? value[0].authorizedSize : 0
        if (value[0].purchaseSize - authorizedSize < value.length) {
          softwareNames.push(key)
        }
      })
      if (softwareNames.length > 0) {
        this.submitting = false
        this.$notify({
          title: this.$t('text.error'),
          message: this.$t('pages.softwareInfo_msg25', softwareNames.join(',')),
          type: 'error',
          duration: 2000
        })
      } else {
        selectedData.forEach(item => {
          const data = {
            softwareName: item.softwareName,
            terminalId: item.terminalId
          }
          this.toPermitData.push(data)
        })
        this.authorized()
      }
    },
    authorized() {
      authorized(this.toPermitData).then(respond => {
        this.dialogOverVisible = false
        this.submitting = false
        this.gridTable().clearSelection()
        this.gridTable().execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.authSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    handlePermit() {
      this.toPermitData.splice(0)
      this.overPermitData.splice(0)
      const selectedData = this.gridTable().getSelectedDatas().filter(item => !item.authorized)
      const map = selectedData.reduce((acc, item) => {
        if (!acc.has(item.softwareName)) acc.set(item.softwareName, []);
        acc.get(item.softwareName).push(item);
        return acc;
      }, new Map());

      map.forEach((value, key) => {
        const authorizedSize = value[0].authorizedSize ? value[0].authorizedSize : 0
        if (value[0].purchaseSize - authorizedSize < value.length) {
          value.forEach(item => {
            item['dataId'] = item.id.toString()
          })
          const overData = {
            dataId: 'P' + value[0].id,
            softwareName: key,
            authorizeSize: value[0].purchaseSize - authorizedSize,
            children: value
          }
          this.overPermitData.push(overData)
        } else {
          value.forEach(item => {
            const data = {
              softwareName: item.softwareName,
              terminalId: item.terminalId
            }
            this.toPermitData.push(data)
          })
        }
      })

      if (this.overPermitData.length > 0) {
        this.dialogOverVisible = true
      } else {
        this.authorized()
      }
    },
    handleImpermit() {
      const selectedData = this.gridTable().getSelectedDatas().filter(item => !!item.authorized)
      recycle(selectedData).then(respond => {
        this.gridTable().clearSelection()
        this.gridTable().execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.recycleSuccess'),
          type: 'success',
          duration: 2000
        })
      })
    },
    handleAssetConfig(row) {
      this.customSoftAssetInfos.splice(0)
      this.resetTemp()
      this.dialogFormVisible2 = true
      getSoftAssetInfoDetail(row.id).then(respond => {
        this.temp = Object.assign(this.temp, respond.data) // copy obj
        this.customSoftAssetInfos = respond.data.customSoftAssetInfos || []
      })
    },
    handleSave() {
      this.submitting = true
      const assetData = this.$refs['detailTable'].getDatas() || []
      saveCustomSoftAssetInfo(assetData).then(respond => {
        this.submitting = false
        this.dialogFormVisible2 = false
        this.handleRefresh()
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.saveSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
        this.submitting = false
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    exportAsset(file) {
      this.submitting = true
      const sortOrder = this.gridTable().sortOrder ? this.gridTable().sortOrder : 'desc'
      const searchQuery = Object.assign({ type: 2, sortOrder: sortOrder, sortName: 'softwareName' }, this.query)
      const rows = this.gridTable().getSelectedDatas()
      if (rows.length > 0) {
        const ids = rows.map(item => {
          return item.terminalId
        })
        searchQuery.terminalIds = ids.join(',')
      }
      const opts = { file, jwt: true, topic: this.$route.name }
      exportSoftInfo(searchQuery, opts, file.name).then(resp => {
        this.submitting = false
      })
    },
    showTextFormatter(col, row) {

    },
    softwareNameFormatter: function(row) {
      return row.originalName ? row.originalName : row.softwareName
    },
    authorizedStatusIconFormatter: function(row) {
      const icons = []
      const icon = { class: 'authorize' }
      if (row.authorized) {
        icon.style = { 'color': 'green' }
        icon.title = this.$t('pages.authorized')
      } else {
        icon.title = this.$t('pages.unauthorized')
      }
      icons.push(icon)
      return icons
    },
    versionFormatter: function(row) {
      return !row.isUninstall ? row.softwareVersion : this.$t('pages.software_Msg28')
    },
    computerTypeFormatter: function(row, data) {
      return data ? this.computerTypeOptions[data] : this.computerTypeOptions[0]
    },
    systemActiveFormatter: function(row, data) {
      return data ? this.$t('pages.software_Msg53') : this.$t('pages.software_Msg54')
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.wl-transfer .transfer-title {
    margin: 0;
  }
</style>
