<template>
  <Software-List ref="softwareView" :title="title" @back="back"></Software-List>
</template>

<script>
import SoftwareList from './softwareList'

export default {
  name: 'AuthorizedInstalled',
  components: { SoftwareList },
  data() {
    return {
      parentPage: '',
      title: ''
    }
  },
  beforeRouteEnter(to, from, next) {
    window.intoSoftware(to, from, next)
  },
  methods: {
    back() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push({ path: this.parentPage })
    }
  }
}
</script>
