<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button v-show="!showEnabled" type="primary" icon="el-icon-back" size="mini" @click="back">
          {{ $t('button.return') }}【{{ title }}】
        </el-button>
        <el-button v-show="showEnabled" type="primary" icon="el-icon-add" size="mini" @click="handleCreate">
          {{ $t('pages.addOrder') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delOrder') }}
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <div v-show="showEnabled" class="searchCon">
          <el-input v-model="query.softwareName" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>

          <el-popover
            placement="bottom"
            width="350"
            trigger="click"
          >
            <Form ref="searchForm" label-position="right" :model="query" label-width="80px">
              <FormItem :label="$t('pages.orderId')">
                <el-input v-model="query.orderId" v-trim :maxlength="60" clearable/>
              </FormItem>
              <FormItem :label="$t('pages.orderName')">
                <el-input v-model="query.name" v-trim :maxlength="60" clearable/>
              </FormItem>
              <FormItem :label="$t('pages.softwareName')">
                <el-input v-model="query.softwareName" v-trim :maxlength="60" clearable/>
              </FormItem>
            </Form>
            <div style="text-align: right; margin-top: 10px">
              <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
              <el-button :loading="submitting" type="primary" size="mini" @click="handleFilter()">{{ $t('table.search') }}</el-button>
            </div>
            <el-button slot="reference" type="primary" size="mini">
              {{ $t('button.advancedSearch') }}
            </el-button>
          </el-popover>
        </div>
      </div>

      <grid-table
        ref="softwareOrderTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :multi-select="multiSelect"
        @selectionChangeEnd="handleSelectionChange"
      />

      <order-edit-dlg ref="orderEditDlg" @submitEnd="handleRefresh" />
    </div>
  </div>
</template>

<script>
import OrderEditDlg from './editDlg'
import { getOrderPage, deleteSoftwareOrder } from '@/api/softwareManage/copyrightManage/orders'

export default {
  name: 'SoftwareOrder',
  components: { OrderEditDlg },
  directives: {},
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      multiSelect: true,
      colModel: [
        { prop: 'orderId', label: 'orderId', width: '150', fixed: true, sort: 'custom' },
        { prop: 'name', label: 'orderName', width: '150', sort: 'custom' },
        { prop: 'softwareName', label: 'softwareName', width: '150', sort: 'custom' },
        { prop: 'purchaseNum', label: 'purchaseNum', width: '100', sort: 'custom' },
        { prop: 'price', label: 'purchasePrice', width: '130', sort: 'custom' },
        { prop: 'purchaseDate', label: 'purchaseDate', width: '120', sort: 'custom' },
        { prop: 'expirationDate', label: 'expirationDate', width: '120', sort: 'custom' },
        { prop: 'publisher', label: 'publisher1', width: '120', sort: 'custom' },
        { prop: 'linkman', label: 'linkman', width: '120', sort: 'custom' },
        { prop: 'contact', label: 'contact', width: '120', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '150', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'editOrder', click: this.handleUpdate },
            { label: 'additionalOrder', click: this.handlePurchase }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        name: '',
        softwareName: '',
        publisher: '',
        orderId: '',
        softwareVersion: '',
        orderIds: '',
        originalSearch: true
      },
      showTree: false,
      deleteable: false,
      submitting: false,
      showEnabled: true,
      isFirstActivated: true
    }
  },
  computed: {
    gridTable() {
      return this.$refs['softwareOrderTable']
    }
  },
  created() {
    this.initOrderInfo()
  },
  activated() {
    if (!this.isFirstActivated) {
      this.initOrderInfo()
      this.handleFilter()
    }
    this.isFirstActivated = false
  },
  methods: {
    initOrderInfo() {
      this.showEnabled = true
      if (this.$route.params.softwareName) {
        this.query.softwareName = this.$route.params.softwareName
        this.showEnabled = false
      }
      if (this.$route.params.orderIds) {
        this.query.orderIds = this.$route.params.orderIds
        this.showEnabled = false
      }
    },
    back() {
      this.$emit('back')
    },
    handleCreate() {
      this.$refs['orderEditDlg'].handleCreate()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteSoftwareOrder({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          if (respond.data && respond.data.length > 0) {
            const msg = this.$t('pages.softwareOrder_text1', { data: respond.data })
            this.$message({
              message: msg,
              type: 'error',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.softwareOrder_text3'),
              type: 'success',
              duration: 2000
            })
          }
        })
      }).catch(() => {})
    },
    handlePurchase(row) {
      this.$refs['orderEditDlg'].handleUpdate(row, 'purchase')
    },
    handleUpdate(row) {
      this.$refs['orderEditDlg'].handleUpdate(row, 'update')
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getOrderPage(searchQuery)
    },
    handleSelectionChange(val) {
      this.selectedData = val
      this.deleteable = val.length > 0
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    resetQuery() {
      this.query.page = 1
      this.query.softwareName = ''
      this.query.publisher = ''
      this.query.orderId = ''
      this.query.name = ''
      this.query.softwareVersion = ''
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    typeMap(options) {
      const map = {}
      options.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    }
  }
}
</script>
