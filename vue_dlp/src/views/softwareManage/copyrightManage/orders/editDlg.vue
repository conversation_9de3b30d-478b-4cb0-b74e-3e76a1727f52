<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="dialogVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <Form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="120px" style="width: 715px; margin-left:20px;">
<!--      <el-card class="box-card" :body-style="{'padding': '10px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.softwareInfo') }}</span>
        </div>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.softwareName')" prop="softwareName">
              <el-tooltip v-if="!isEditMode" slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">
                  <i18n path="pages.softwareOrderSoftwareNameTips">
                    <br slot="br"/>
                  </i18n>
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
              <el-select
                v-model="temp.softwareName"
                clearable
                filterable
                remote
                allow-create
                :disabled="isEditMode"
                :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.keywordSearch') })"
                :remote-method="loadSoftInfo"
                :loading="loadingSelect"
                @change="softSelectChange"
              >
                <el-option v-for="item in softOptions" :key="item" :label="item" :value="item"/>
              </el-select>
            </FormItem>
          </el-col>
&lt;!&ndash;          <el-col :span="12">
            <FormItem :label="$t('pages.softwareDesc')" prop="softwareDesc">
              <el-input v-model="temp.softwareExtInfo.softwareDesc" :maxlength="300" :disabled="dialogStatus==='view' || !temp.softwareName" />
            </FormItem>
          </el-col>&ndash;&gt;
        </el-row>
&lt;!&ndash;        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.softwareId')" prop="softwareExtInfo.softwareId">
              <el-input v-model="temp.softwareExtInfo.softwareId" v-trim :maxlength="60" :disabled="dialogStatus==='view' || !temp.softwareName" />
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.chargeType')" prop="chargeType">
              <el-select v-model="temp.softwareExtInfo.chargeType" filterable clearable style="width: 100%;" :disabled="!temp.softwareName">
                <el-option v-for="(type, i) in chargeTypes" :key="i" :label="type" :value="type"></el-option>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.industryType')" prop="industryType">
              <el-select v-model="temp.softwareExtInfo.industryType" filterable clearable style="width: 100%;" :disabled="!temp.softwareName">
                <el-option v-for="(type, i) in industryTypes" :key="i" :label="type" :value="type"></el-option>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.softwareType')" prop="softwareType">
              <el-select v-model="temp.softwareExtInfo.softwareType" filterable clearable style="width: 100%;" :disabled="!temp.softwareName">
                <el-option v-for="(type, i) in softwareTypes" :key="i" :label="type" :value="type"></el-option>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>&ndash;&gt;
      </el-card>-->
<!--      <el-card class="box-card" :body-style="{'padding': '10px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.orderInfo') }}</span>
        </div>-->
      <el-row>
        <!--          <el-col :span="12">
                    <FormItem :label="$t('table.softwareVersion')" prop="softwareVersion">
                      <el-input v-model="temp.softwareVersion" :maxlength="20" :disabled="dialogStatus==='view'" />
                    </FormItem>
                  </el-col>-->
        <el-col :span="12">
          <FormItem :label="$t('pages.softwareName')" prop="softwareName">
            <el-tooltip v-if="!isEditMode" slot="tooltip" effect="dark" placement="bottom-start">
              <div slot="content">
                <i18n path="pages.softwareOrderSoftwareNameTips">
                  <br slot="br"/>
                </i18n>
              </div>
              <i class="el-icon-question" />
            </el-tooltip>
            <el-select
              v-model="temp.softwareName"
              clearable
              filterable
              remote
              allow-create
              :disabled="isEditMode"
              :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.keywordSearch') })"
              :remote-method="loadSoftInfo"
              :loading="loadingSelect"
            >
              <el-option v-for="item in softOptions" :key="item" :label="item" :value="item"/>
            </el-select>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('pages.publisherInfo')" prop="publisher">
            <el-input v-model="temp.publisher" :maxlength="60" :disabled="dialogStatus==='view'" />
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('table.orderId')" prop="orderId">
            <el-input v-model="temp.orderId" v-trim :maxlength="60" :disabled="dialogStatus==='view'" />
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('table.orderName')" prop="name">
            <el-input v-model="temp.name" v-trim :maxlength="60" :disabled="dialogStatus==='view'" />
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('table.purchaseDate')" prop="purchaseDate">
            <el-date-picker
              v-model="temp.purchaseDate"
              style="width: 100%"
              :clearable="false"
              :editable="false"
              :disabled="dialogStatus==='view'"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              type="date"
              :picker-options="pickerOptionsStart"
              :placeholder="$t('pages.selectDateTime')"
              @change="startLessthanEnd1"
            ></el-date-picker>
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('table.expirationDate')" prop="expirationDate">
            <el-date-picker
              v-model="temp.expirationDate"
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :clearable="false"
              :editable="false"
              :disabled="dialogStatus==='view'"
              type="date"
              :picker-options="pickerOptionsEnd"
              :placeholder="$t('pages.selectDateTime')"
              @change="startLessthanEnd2"
            ></el-date-picker>
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('table.purchaseNum')" prop="purchaseNum">
            <el-input v-model="temp.purchaseNum" type="Number" :maxlength="7" min="1" :disabled="dialogStatus==='view'" @input="numberLimit(arguments[0], 'purchaseNum', 1, 9999999)" />
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('table.purchasePrice')" prop="price">
            <el-input v-model="temp.price" type="Number" :maxlength="9" min="0" :disabled="dialogStatus==='view'" @input="numberLimit(arguments[0], 'price', 0, 999999999)" />
          </FormItem>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <FormItem :label="$t('table.linkman')" prop="linkman">
            <el-input v-model="temp.linkman" :maxlength="60" :disabled="dialogStatus==='view'" />
          </FormItem>
        </el-col>
        <el-col :span="12">
          <FormItem :label="$t('table.contact')" prop="contact">
            <el-input v-model="temp.contact" :maxlength="60" :disabled="dialogStatus==='view'" />
          </FormItem>
        </el-col>
      </el-row>
      <FormItem :label="$t('table.remark')" prop="remark">
        <el-input v-model="temp.remark" type="textarea" show-word-limit maxlength="300"/>
      </FormItem>
<!--      <FormItem label="版权管控激活">
        <el-switch v-model="temp.active"/>
      </FormItem>-->
<!--      </el-card>-->
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="dialogStatus!=='view'" :loading="submitting" type="primary" @click="saveOrder">{{ $t('button.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ dialogStatus==='view' ? $t('button.close') : $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSoftwareOrderByOrderId, createSoftwareOrder, updateSoftwareOrder } from '@/api/softwareManage/copyrightManage/orders'
import { listSoftwareTypes, listChargeTypes, listIndustryTypes, listSoftName, getSoftwareExtInfo } from '@/api/softwareManage/assets/assetsView'

export default {
  name: 'OrderEditDlg',
  data() {
    return {
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.softwareOrder'), 'update'),
        create: this.i18nConcatText(this.$t('pages.softwareOrder'), 'create'),
        view: this.i18nConcatText(this.$t('pages.softwareOrder'), 'details'),
        purchase: this.$t('pages.order_purchase')
      },
      temp: {}, // 表单字段
      defaultTemp: {
        softwareName: '',
        id: undefined,
        orderId: undefined,
        name: '',
        softwareId: undefined,
        softwareVersion: '',
        publisher: '',
        linkman: '',
        contact: '',
        purchaseNum: 1,
        price: 0,
        purchaseDate: new Date(),
        expirationDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
        remark: '',
        active: 0
      },
      defaultTempExt: {
        softwareName: '',
        publisher: '',
        chargeType: '',
        softwareType: '',
        industryType: '',
        softwareDesc: '',
        softwareId: ''
      },
      rules: {
        orderId: [
          { required: true, message: this.$t('pages.softwareOrder_text4'), trigger: 'blur' },
          { validator: this.orderIdValidator, trigger: 'blur' }
        ],
        softwareName: [{ required: true, message: this.$t('pages.softwareOrder_text11'), trigger: 'change' }],
        name: [
          { required: true, message: this.$t('pages.softwareOrder_text5'), trigger: 'blur' }
        ],
        softwareExtInfo: {
          softwareId: [{ required: true, message: this.$t('pages.softwareOrder_text6'), trigger: 'blur' }]
        },
        purchaseNum: [{ required: true, message: this.$t('pages.softwareOrder_text8'), trigger: 'blur' }],
        purchaseDate: [{ required: true, message: this.$t('pages.softwareOrder_text9'), trigger: 'blur' }],
        expirationDate: [{ required: true, message: this.$t('pages.softwareOrder_text10'), trigger: 'blur' }]
      },
      dialogVisible: false,
      isEditMode: false,
      submitting: false,
      loadingSelect: false,
      softOptions: [],
      pickerOptionsStart: {
        disabledDate: time => {
          const endDateVal = this.temp.expirationDate
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime()
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.temp.purchaseDate
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime() - 0 * 24 * 60 * 60 * 1000)
          }
        }
      },
      softwareTypes: [
        this.$t('pages.softwareTypes1'),
        this.$t('pages.softwareTypes2'),
        this.$t('pages.softwareTypes3'),
        this.$t('pages.softwareTypes4')
      ],
      chargeTypes: [
        this.$t('pages.chargeTypes1'),
        this.$t('pages.chargeTypes2'),
        this.$t('pages.chargeTypes3'),
        this.$t('pages.chargeTypes4')
      ],
      industryTypes: [
        this.$t('pages.industryTypes1'),
        this.$t('pages.industryTypes2'),
        this.$t('pages.industryTypes3'),
        this.$t('pages.industryTypes4'),
        this.$t('pages.industryTypes5'),
        this.$t('pages.industryTypes6')
      ]
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.resetTemp()
    this.$nextTick(() => {
      this.loadSoftInfo('')
    })
  },
  activated() {
  },
  methods: {
    numberLimit(value, type, min, max) {
      if (!value) value = 0
      this.temp[type] = value > max ? max : value < min ? min : parseInt(value)
    },
    loadSoftInfo(searchInfo) {
      this.loadingSelect = true
      listSoftName({ searchInfo: searchInfo, limit: 50, page: 1 }).then(resp => {
        this.softOptions = resp.data
        this.loadingSelect = false
      })
    },
    // softSelectChange(val) {
    //   getSoftwareExtInfo({ softwareName: val }).then(resp => {
    //     this.temp.publisher = ''
    //     if (resp.data) {
    //       this.temp.publisher = resp.data.publisher
    //     }
    //   })
    // },
    initSoftwareTpes: function() {
      listSoftwareTypes().then(respond => {
        respond.data.forEach(el => {
          if (el && this.softwareTypes.indexOf(el) < 0) {
            this.softwareTypes.push(el)
          }
        })
      })
      listChargeTypes().then(respond => {
        respond.data.forEach(el => {
          if (el && this.chargeTypes.indexOf(el) < 0) {
            this.chargeTypes.push(el)
          }
        })
      })
      listIndustryTypes().then(respond => {
        respond.data.forEach(el => {
          if (el && this.industryTypes.indexOf(el) < 0) {
            this.industryTypes.push(el)
          }
        })
      })
    },
    startLessthanEnd1(vm) {
      const startTime = new Date(this.temp.purchaseDate).getTime()
      const endTime = new Date(this.temp.expirationDate).getTime()
      if (startTime > endTime) {
        this.temp.purchaseDate = this.temp.expirationDate
      }
    },
    startLessthanEnd2(vm) {
      const startTime = new Date(this.temp.purchaseDate).getTime()
      const endTime = new Date(this.temp.expirationDate).getTime()
      if (startTime > endTime) {
        this.temp.expirationDate = this.temp.purchaseDate
      }
    },
    resetTemp() {
      this.isEditMode = false
      this.submitting = false
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.softwareExtInfo = Object.assign({}, this.defaultTempExt)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.initSoftwareTpes()
      this.dialogStatus = 'create'
      this.dialogVisible = true
      if (this.$refs.dataForm) {
        this.$refs.dataForm.clearValidate()
        this.$refs.dataForm.resetFields('softwareName');
      }
    },
    handleUpdate(row, status) {
      this.resetTemp()
      this.initSoftwareTpes()
      this.temp = Object.assign(this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      this.dialogStatus = status
      if (this.dialogStatus === 'purchase') {
        this.temp.id = undefined
        this.temp.orderId = ''
        this.temp.purchaseNum = ''
        this.temp.price = undefined
        this.temp.purchaseDate = new Date()
        this.temp.expirationDate = new Date(new Date().setMonth(new Date().getMonth() + 1))
        this.temp.remark = ''
      }
      this.isEditMode = true
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    saveOrder() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dialogStatus === 'update') {
            updateSoftwareOrder(this.temp).then(respond => {
              this.submitting = false
              if (respond.data) {
                this.dialogVisible = false
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('pages.softwareOrder_text12'),
                  type: 'success',
                  duration: 2000
                })
                this.$emit('submitEnd')
              } else {
                this.$notify({
                  title: this.$t('text.error'),
                  message: this.$t('pages.softwareOrder_text17'),
                  type: 'error',
                  duration: 2000
                })
              }
            }).catch(reason => {
              this.submitting = false
            })
          } else {
            createSoftwareOrder(this.temp).then(respond => {
              this.$emit('submitEnd')
              this.submitting = false
              this.dialogVisible = false
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.softwareOrder_text13'),
                type: 'success',
                duration: 2000
              })
            }).catch(reason => {
              this.submitting = false
            })
          }
        } else {
          this.submitting = false
        }
      })
    },
    orderIdValidator(rule, value, callback) {
      getSoftwareOrderByOrderId({ orderId: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('pages.softwareOrder_text14')))
        } else {
          callback()
        }
      })
    }
  }
}
</script>
