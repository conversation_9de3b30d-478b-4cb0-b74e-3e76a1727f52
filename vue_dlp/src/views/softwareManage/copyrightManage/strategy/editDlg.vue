<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      :title="$t('pages.softwareCopyrightStrategy')"
      :active-able="activeAble"
      :stg-code="290"
      :time-able="false"
      :rules="rules"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createStrategy"
      :update="updateStrategy"
      :get-by-name="getStrategyByName"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      :validate-form="validateFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <div style="padding-left: 30px;">
          <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider>
          <label>{{ $t('pages.softwareCopyrightStrategy_text1') }}</label>
          <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
          <label>{{ $t('pages.softwareCopyrightStrategy_text2') }}</label>
          <FormItem :label="$t('pages.softwareCopyrightStrategy_text3')" label-width="250px" prop="termSampTime">
            <el-input v-model="temp.termSampTime" type="Number" maxlength="4" min="1" max="1440" :disabled="!formable" style="width:  85px;" @input="numberLimit(arguments[0], 'termSampTime', 1, 1440)"></el-input>
            <span>{{ $t('pages.require_Msg5') }}</span>
          </FormItem>
          <FormItem :label="$t('pages.softwareCopyrightStrategy_text4')" label-width="250px" prop="alarmTimes">
            <el-input v-model="temp.alarmTimes" type="Number" maxlength="4" min="0" :disabled="!formable" style="width: 85px;" @input="numberLimit(arguments[0], 'alarmTimes', 1, 1440)" ></el-input>
            <span>{{ $t('pages.require_Msg7') }}</span>
          </FormItem>
          <el-divider content-position="left">{{ $t('pages.responseRule') }}</el-divider>
          <FormItem label-width="30px" prop="limitType">
            <el-checkbox v-model="temp.limitType" :disabled="!formable" :true-label="1" :false-label="0" style="width: auto;">{{ $t('table.uninstallSoftware') }}</el-checkbox>
          </FormItem>
          <ResponseContent
            ref="resContent"
            :select-style="{ 'margin-top': '5px', 'margin-left': '13px' }"
            :status="dialogStatus"
            :show-select="true"
            :editable="formable"
            read-only
            :prop-check-rule="!!temp.isAlarm"
            :show-check-rule="true"
            :prop-rule-id="temp.ruleId"
            @ruleIsCheck="getRuleIsCheck"
            @getRuleId="getRuleId"
          />
        </div>
      </template>
    </stg-dialog>

  </div>
</template>

<script>
import {
  createStrategy,
  getStrategyById,
  getStrategyByName,
  updateStrategy
} from '@/api/softwareManage/copyrightManage/strategy'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'

export default {
  name: 'SoftwareCopyrightStrategyDlg',
  components: { ResponseContent },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      submitting: false,
      slotName: undefined,
      temp: {},
      newTemp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        limitType: 0,
        active: false,
        remark: '',
        entityType: '',
        entityId: undefined,
        ruleId: null,
        isAlarm: 0,
        termSampTime: 5,
        alarmTimes: 1
      },
      checkedColModel: [
        { prop: 'softwareName', label: 'softwareName', sort: true, width: '120' },
        { prop: 'versions', label: 'versionNumber', width: '120', sort: true, formatter: this.versionsFormatter },
        { prop: 'processName', label: 'processName', width: '120', sort: true, formatter: this.processNameFormatter },
        { prop: 'matchType', label: 'matchType', width: '120', sort: true, formatter: this.matchTypeFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', disabledFormatter: this.buttonFormatter, click: this.handleUpdateSoftware }
          ]
        }
      ],
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }]
      },
      limitTypeOptions: [
        { value: 1, label: this.$t('pages.software_Msg1_1'), violations: [], propCheckRule: false },
        { value: 2, label: this.$t('pages.software_Msg2_1'), violations: [], propCheckRule: false }
      ],
      itemTypeMap: {
        1: this.$t('pages.applicationProgram'),
        2: this.$t('pages.appType'),
        3: this.$t('pages.md5LevelMap3')
      },
      dialogStatus: '',
      propRuleId: undefined,
      ruleChecked: false,
      validMsg: '',
      searchInfo: '',
      oldViolations: [],
      matchTypeOptions: {
        0: this.$t('pages.matchTypeOptions2'),
        1: this.$t('pages.matchTypeOptions1')
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
  },
  activated() {
    this.resetTemp()
  },
  methods: {
    createStrategy,
    updateStrategy,
    getStrategyByName,
    getLimitTypeOpt() {
      const opts = this.limitTypeOptions.filter(opt => this.temp.limitType === opt.value)
      return opts.length > 0 ? opts[0] : {}
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = slotTemp || {}
    },
    handleFilter() {
      this.newTemp = {}
      const tempArr = [...this.temp.stgRelatedSoftware]
      this.newTemp = { ...this.temp, stgRelatedSoftware: tempArr }
      var regex = /^\s+$/
      if (regex.test(this.searchInfo) || this.searchInfo == '') {
        this.$refs['checkedAppGrid'].execRowDataApi()
        return
      }
      var colK = 0;
      var existSearch = false
      for (var i = 0; i < this.newTemp.stgRelatedSoftware.length; i++) {
        if (this.newTemp.stgRelatedSoftware[i].softwareName.toLowerCase().includes(this.searchInfo.toLowerCase())) {
          this.newTemp.stgRelatedSoftware[colK] = this.newTemp.stgRelatedSoftware[i]
          colK++
          if (!existSearch) {
            existSearch = true
          }
        }
      }
      if (existSearch) {
        // 移除多余的数据
        this.newTemp.stgRelatedSoftware.splice(colK, this.newTemp.stgRelatedSoftware.length - colK)
      } else {
        this.newTemp.stgRelatedSoftware = []
      }
    },
    closed() {
      this.searchInfo = ''
      this.newTemp = {}
      this.resetTemp()
    },
    appGridTable() {
      return this.$refs['checkedAppGrid']
    },
    getRuleIsCheck(value) {
      this.temp.isAlarm = value
    },
    getRuleId(value) {
      this.temp.ruleId = value
    },
    numberLimit(value, type, min, max) {
      if (type === 'alarmTimes') {
        max = parseInt(1440 / this.temp.termSampTime)
        this.temp.alarmTimes = !value ? min : value > max ? max : value < min ? min : parseInt(value)
      } else if (type === 'termSampTime') {
        this.temp.termSampTime = value > max ? max : value < min ? min : parseInt(value)
        const alarmMaxTimes = parseInt(1440 / this.temp.termSampTime)
        this.temp.alarmTimes = this.temp.alarmTimes > alarmMaxTimes ? alarmMaxTimes : this.temp.alarmTimes < 0 ? 0 : parseInt(this.temp.alarmTimes)
      }
    },
    buttonFormatter(row) {
      return !this.formable
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      // this.ruleChecked = false
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      }, undefined, () => {
        this.$nextTick(() => { // 新增弹框打开时，清除响应规则的选中情况
          this.$refs['resContent'].ruleId = undefined
          this.temp.ruleId = null
        })
      })
    },
    async handleUpdate(row) {
      this.resetTemp()
      if (!this.formable) { // 策略总览查看时，数据未格式化，需要重新从后台查询
        await getStrategyById(row.id).then(response => {
          this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(response.data))) // copy obj
        })
      } else {
        this.temp = Object.assign({}, this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      }
      this.dialogStatus = 'update'
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(rowData) {

    },
    formatFormData(formData) {
    },
    validateFormData(formData) {
      return true
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.selectionCheckBox {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}
</style>
