<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="handleRefresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-add" size="mini" @click="handleCreate">
          {{ $t('pages.addOrder') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delOrder') }}
        </el-button>
        <el-button size="mini" :disabled="!activeAble" style="text-transform: capitalize" @click="handleBatchActive">
          {{ $t('pages.softwareOrder_text19') }}
        </el-button>
        <el-button size="mini" :disabled="!activeAble" style="text-transform: capitalize" @click="handleBatchInactive">
          {{ $t('pages.softwareOrder_text20') }}
        </el-button>
        <el-button icon="el-icon-setting" size="mini" style="text-transform: capitalize" @click="handleSetting">
          {{ $t('pages.sys_alarm_setup') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.softwareName" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="softwareList"
        :stripe="false"
        row-key="dataId"
        :default-expand-all="false"
        :col-model="colModel"
        :multi-select="multiSelect"
        :sub-multi-select="multiSelect"
        :default-sort="defaultSort"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.orderInfo')"
      :visible.sync="dialogOrderVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="softwareOrderTable"
        :col-model="colModel1"
        :row-datas="orderDatas"
        :multi-select="multiSelect"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogOrderVisible = false">{{ $t('button.close') }}</el-button>
      </div>
    </el-dialog>

    <order-edit-dlg ref="orderEditDlg" @submitEnd="submitEnd" />

    <sys-alarm-setup-dlg ref="sysAlarmSetup" @submitEnd="alarmSetupSubmitEnd"></sys-alarm-setup-dlg>

  </div>
</template>

<script>
import OrderEditDlg from '@/views/softwareManage/copyrightManage/orders/editDlg'
import SysAlarmSetupDlg from '@/views/common/sysAlarmSetupDlg'
import {
  getSoftwareOrderPage,
  scanTriggerSoftwareOrderAlarm,
  getEnableUsingTimeConfig,
  saveEnableUsingTimeConfig,
  updateSoftwareOrderActive, deleteSoftwareOrder
} from '@/api/softwareManage/copyrightManage/orders'
import { listSoftStatisticByOriginalName } from '@/api/softwareManage/assets/assetsView'

export default {
  name: 'SoftwareOrderManage',
  components: { SysAlarmSetupDlg, OrderEditDlg },
  data() {
    return {
      multiSelect: true,
      defaultSort: { prop: 'softwareName', order: 'desc' },
      colModel: [
        { prop: 'softwareName', label: 'softwareName', fixed: true, sort: 'custom', width: '200', iconFormatter: this.activeStatusIconFormatter },
        { prop: 'orderId', label: 'orderId', width: '100' },
        { prop: 'name', label: 'orderName', width: '100' },
        { prop: 'purchaseNum', label: 'purchaseNum', width: '100' },
        { prop: 'price', label: 'purchasePrice', width: '100' },
        { prop: 'purchaseDate', label: 'purchaseDate', width: '100' },
        { prop: 'expirationDate', label: 'expirationDate', width: '100' },
        { prop: 'publisher', label: 'publisher1', width: '100' },
        { prop: 'linkman', label: 'linkman', width: '100' },
        { prop: 'contact', label: 'contact', width: '100' },
        { prop: 'remark', label: 'remark', width: '120' },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'openCopyrightCtr', isShow: (row) => { return row.children && !row.active }, click: this.handleActive },
            { label: 'closeCopyrightCtr', isShow: (row) => { return row.children && row.active }, click: this.handleActive },
            { label: 'editOrder', isShow: (row) => { return !row.children }, click: this.handleUpdate },
            { label: 'additionalOrder', isShow: (row) => { return !row.children }, click: this.handlePurchase }
          ]
        }
      ],
      colModel1: [
        { prop: 'orderId', label: 'orderId', width: '150', sort: 'custom' },
        { prop: 'name', label: 'orderName', width: '150', sort: 'custom' },
        { prop: 'softwareExtInfo.softwareId', label: 'softwareId', width: '150', sort: 'custom' },
        { prop: 'softwareName', label: 'softwareName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '150', sort: 'custom' },
        { prop: 'publisher', label: 'publisher1', width: '150', sort: 'custom' },
        { prop: 'linkman', label: 'linkman', width: '150' },
        { prop: 'contact', label: 'contact', width: '150' },
        { prop: 'purchaseNum', label: 'purchaseNum', width: '100' },
        { prop: 'price', label: 'purchasePrice', width: '100' },
        { prop: 'purchaseDate', label: 'purchaseDate', width: '150', sort: 'custom' },
        { prop: 'expirationDate', label: 'expirationDate', width: '150', sort: 'custom' }
      ],
      query: { // 查询条件
        page: 1,
        softwareName: ''
      },
      showTree: false,
      deleteable: false,
      activeAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        account: '',
        password: '',
        confirmPassword: '',
        groupIds: [],
        mail: '',
        phone: '',
        active: 'true',
        flag: 0
      },
      tempS: {
        id: undefined,
        bizType: 1, // 1：软件订单预警
        excessAlarm: false,
        imminentAlarm: false,
        imminentDays: undefined,
        alarmPop: false,
        alarmMail: false,
        sysUserIds: [],
        emails: []
      },
      dialogOrderVisible: false,
      dialogTerminalVisible: false,
      dialogStatus: '',
      textMap: {
        installed: this.$t('pages.softwareOrder_installed'),
        authorized: this.$t('pages.softwareOrder_authorized'),
        violation: this.$t('pages.softwareOrder_violation')
      },
      softwareTypes: [
        { label: this.$t('pages.allType'), value: null },
        { label: this.$t('pages.softwareTypes5'), value: 0 },
        { label: this.$t('pages.chargeTypes1'), value: 1 },
        { label: this.$t('pages.chargeTypes2'), value: 2 },
        { label: this.$t('pages.chargeTypes3'), value: 3 },
        { label: this.$t('pages.chargeTypes4'), value: 4 }
      ],
      chargeTypes: [
        { label: this.$t('pages.allType'), value: null },
        { label: this.$t('pages.softwareTypes5'), value: 0 },
        { label: this.$t('pages.softwareTypes1'), value: 1 },
        { label: this.$t('pages.softwareTypes2'), value: 2 },
        { label: this.$t('pages.softwareTypes3'), value: 3 },
        { label: this.$t('pages.softwareTypes4'), value: 4 }
      ],
      industryTypes: [
        { label: this.$t('pages.allType'), value: null },
        { label: this.$t('pages.softwareTypes5'), value: 0 },
        { label: this.$t('pages.industryTypes1'), value: 1 },
        { label: this.$t('pages.industryTypes2'), value: 2 },
        { label: this.$t('pages.industryTypes3'), value: 3 },
        { label: this.$t('pages.industryTypes4'), value: 4 },
        { label: this.$t('pages.industryTypes5'), value: 5 },
        { label: this.$t('pages.industryTypes6'), value: 6 }
      ],
      gatherTypes: [
        { label: this.$t('pages.gatherTypes1'), value: 0 },
        { label: this.$t('pages.gatherTypes2'), value: 1 }
      ],
      submitting: false,
      userGroupTreeData: [],
      userGroupTreeSelectData: [],
      orderDatas: [],
      isFirstActivated: true,
      enableUsingTime: undefined, // 版权授权管控软件未授权情况下可使用天数，默认可使用7天
      parentSelectedData: [], // 表格选中的父行数据
      childrenSelectedData: [] // 表格选中的子行数据
    }
  },
  computed: {
    gridTable() {
      return this.$refs['softwareList']
    }
  },
  watch: {
  },
  created() {
  },
  activated() {
    if (!this.isFirstActivated) {
      this.handleRefresh()
    }
    this.isFirstActivated = false
  },
  methods: {
    orderView(row, data) {
      this.$router.push({ name: 'OrderForSoftware', params: { softwareName: row.softwareName }})
    },
    installedView(row, data) {
      this.$router.push({ name: 'InstalledSoftware', params: { flag: 'installed', originalName: row.softwareName }})
    },
    authorizedView(row, data) {
      this.$router.push({ name: 'AuthorizedInstalled', params: { flag: 'authorized', originalName: row.softwareName }})
    },
    violationView(row, data) {
      this.$router.push({ name: 'IllegalInstalled', params: { flag: 'violation', originalName: row.softwareName }})
    },
    rowDataApi: function(option) {
      this.query = Object.assign({}, this.query, option)
      return getSoftwareOrderPage(this.query)
    },
    tableAfterLoad(rowData, grid) {
      const softNameMap = {}
      const softwareNames = []
      rowData.forEach(item => {
        softwareNames.push(item.softwareName)
        softNameMap[item.softwareName] = item
      })
      listSoftStatisticByOriginalName({ softwareNameList: softwareNames }).then(resp => {
        resp.data.forEach(item => {
          const softInfo = softNameMap[item.softwareName]
          this.$refs['softwareList'].updateRowData(Object.assign(softInfo, item))
        })
      })
    },
    handleSelectionChange(val) {
      this.parentSelectedData.splice(0)
      this.childrenSelectedData.splice(0)
      if (val && val.length > 0) {
        val.forEach(item => {
          if (item.children && item.children.length > 0) {
            this.parentSelectedData.push(item)
          } else {
            this.childrenSelectedData.push(item)
          }
        })
        this.deleteable = this.childrenSelectedData.length > 0
        this.activeAble = this.parentSelectedData.length > 0
      } else {
        this.deleteable = false
        this.activeAble = false
      }
    },
    handleCreate() {
      this.$refs['orderEditDlg'].handleCreate()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.childrenSelectedData.map(item => item.id)
        deleteSoftwareOrder({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          if (respond.data && respond.data.length > 0) {
            const msg = this.$t('pages.softwareOrder_text1', { data: respond.data })
            this.$message({
              message: msg,
              type: 'error',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.softwareOrder_text3'),
              type: 'success',
              duration: 2000
            })
          }
        })
      }).catch(() => {})
    },
    handlePurchase(row) {
      this.$refs['orderEditDlg'].handleUpdate(row, 'purchase')
    },
    handleUpdate(row) {
      this.$refs['orderEditDlg'].handleUpdate(row, 'update')
    },
    handleBatchActive() {
      const softwareNameList = this.parentSelectedData.map(item => item.softwareName)
      this.updateSoftwareOrderActive(softwareNameList, 1)
    },
    handleBatchInactive() {
      const softwareNameList = this.parentSelectedData.map(item => item.softwareName)
      this.updateSoftwareOrderActive(softwareNameList, 0)
    },
    handleActive(row) {
      if (row.active) {
        row.active = 0
      } else {
        row.active = 1
      }
      const softwareNameList = [row.softwareName]
      this.updateSoftwareOrderActive(softwareNameList, row.active)
    },
    updateSoftwareOrderActive(softwareNameList, active) {
      const successMsg = active ? this.$t('pages.softwareOrder_text21') : this.$t('pages.softwareOrder_text22')
      updateSoftwareOrderActive({ softwareNameList: softwareNameList, active: active }).then(() => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: successMsg,
          type: 'success',
          duration: 2000
        })
      }).catch(() => {})
    },
    scanTriggerAlarm() {
      scanTriggerSoftwareOrderAlarm().then(() => {})
    },
    alarmSetupSubmitEnd(data) {
      this.scanTriggerAlarm()
    },
    submitEnd(data) {
      this.scanTriggerAlarm()
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleSetting() {
      this.$refs['sysAlarmSetup'].show()
      // this.editable = this.tempS.excessAlarm || this.tempS.imminentAlarm
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    typeMap(options) {
      const map = {}
      options.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    },
    activeStatusIconFormatter(row, data) {
      const icons = []
      const icon = {}
      if (row.active) {
        icon.title = this.$t('pages.softwareOrder_text23', { date: row.effectTime })
        icon.class = 'activeUnlock'
      } else {
        icon.title = this.$t('pages.softwareOrder_text24')
        icon.class = 'activeLock'
      }
      icons.push(icon)
      return icons
    }
  }
}
</script>
