<template>
  <Software-Order ref="softwareOrder" :title="title" @back="back"></Software-Order>
</template>

<script>
import SoftwareOrder from '@/views/softwareManage/copyrightManage/orders'

export default {
  name: 'OrderForSoftware',
  components: { SoftwareOrder },
  directives: {},
  data() {
    return {
      parentPage: '',
      title: ''
    }
  },
  computed: {},
  created() {},
  activated() {},
  beforeRouteEnter(to, from, next) {
    window.intoSoftware(to, from, next)
  },
  methods: {
    back() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.push({ path: this.parentPage })
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-tab-pane{
    padding: 10px 20px;
  }
</style>
