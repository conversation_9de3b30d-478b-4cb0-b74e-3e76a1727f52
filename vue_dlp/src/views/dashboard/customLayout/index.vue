<template>
  <div class="custom-container">
    <!-- 页面图表面板 -->
    <el-carousel ref="panels" v-loading="loading" height="100%" :initial-index="curIndex" :autoplay="false" :loop="false" trigger="click" @change="panelChange">
      <el-carousel-item v-for="(item, index) in showCharts" :key="index" :label="item.name">
        <data-panel :ref="'dataPanel' + index" :temp="termUserCountData"/>
        <row-panel
          :ref="'rowPanel' + index"
          :show-charts="item.echartsOptionDTOs"
          :panel-index="index"
          :layout="item.panel"
          :nonsupport-codes="new Set(unsupoortModifyChart)"
          @setLayout="handleUpdate"
          @settingChart="settingChart"
          @deleteChart="deleteChart"
        />
      </el-carousel-item>
    </el-carousel>

    <!-- 面板配置按钮 -->
    <el-popover
      placement="left-start"
      popper-class="panel-setting"
      :append-to-body="false"
      trigger="click"
    >
      <ul>
        <li @click="handleCreate">{{ $t('pages.createPanel') }}</li>
        <li @click="handleUpdate">{{ $t('pages.updatePanel') }}</li>
        <li @click="deletePanel">{{ $t('pages.deletePanel') }}</li>
      </ul>
      <el-button slot="reference" class="setting-btn" :title="$t('pages.layoutSetting')">
        <svg-icon icon-class="setting"/>
      </el-button>
    </el-popover>

    <!-- 查询条件配置弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :title="nonsupportReport ? customListConfigName : $t('pages.queryCondition')"
      :visible.sync="conditionDialogVisible"
      :modal="false"
      :show-close="false"
      :width="nonsupportReport ? '700px': '400px'"
    >
      <el-container>
        <!--el-aside:自定义列表：只有自定义报表配置才有-->
        <el-aside v-if="nonsupportReport" class="left-list">
          <span class="left-list-title">
            {{ $t('pages.customList') }}
            <el-tooltip class="item" effect="dark" placement="right" :content="'列表中的报表数据将会添加到首页面板'">
              <i class="el-icon-info" />
            </el-tooltip>
            <svg-icon icon-class="add" class="add-icon" :title="$t('text.add')" @click="addCustomList"></svg-icon>
          </span>
          <div class="left-item">
            <div
              v-for="(item, index) in customList"
              :key="index"
            >
              <span class="left-item-title" :title="item.reportName" :class="active==index ? 'itemClass' : '  ' " @click="editorList(index, item)">{{ item.reportName }}</span>
              <svg-icon icon-class="delete" class="delete-icon" :title="$t('text.delete')" @click="deleteCustomList(item)"></svg-icon>
            </div>
          </div>
        </el-aside>
        <el-main class="main-config">
          <div>
            <div style="overflow-x: hidden">
              <!--Form:原有Form表单基础上只加了报表名称，别的内容都没有修改-->
              <Form v-if="conditionDialogVisible" ref="conditionForm" :model="layoutTemp.queryCondition[currentConditionCode]" :rules="conditionRules" label-width="80px" style="width: 350px">
                <!--自定义报表搜索条件多一个“数据项”和“报表名称”-->
                <FormItem v-if="nonsupportReport" :label="$t('pages.reportName')" prop="reportName">
                  <el-input v-model="layoutTemp.queryCondition[currentConditionCode].reportName" maxlength="40"/>
                </FormItem>
                <FormItem v-if="customCodes.includes(currentConditionCode)" label="" prop="customDataItems">
                  <span slot="label">
                    {{ $t('form.dataItem') }}
                    <el-tooltip effect="dark" placement="bottom-start">
                      <div slot="content">
                        {{ $t('pages.customDataItemTip', {num: 5}) }}
                      </div>
                      <i class="el-icon-info"/>
                    </el-tooltip>
                  </span>
                  <el-select
                    v-model="layoutTemp.queryCondition[currentConditionCode].customDataItems"
                    multiple
                    filterable
                    :multiple-limit="5"
                    :placeholder="$t('text.select')"
                    @change="handleCustomDataItemSelect"
                  >
                    <el-option
                      v-for="(item, index) in customDataItems"
                      :key="index"
                      :label="$t(`table.${item.label}`)"
                      :value="item.prop"
                      :disabled="isDisabled(item.prop)"
                    ></el-option>
                  </el-select>
                </FormItem>
                <FormItem v-if="supportDimBaseType" :label="$t('pages.dimBaseType')">
                  <el-select
                    v-if="currentCharType == 'line'"
                    v-model="layoutTemp.queryCondition[currentConditionCode].dimBaseType"
                  >
                    <el-option
                      v-for="(item, index) in trendDateTypeOptions"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <el-select v-else v-model="layoutTemp.queryCondition[currentConditionCode].dimBaseType">
                    <el-option
                      v-for="(item, index) in dateTypeOptions"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </FormItem>
                <FormItem v-if="supportCountByObject" :label="$t('pages.countByObject')">
                  <el-select
                    v-if="currentCharType != 'line'"
                    v-model="layoutTemp.queryCondition[currentConditionCode].countByObject"
                    @change="handleCount"
                  >
                    <!--1:操作员2：终端3：关键字-->
                    <el-option
                      v-for="(item, index) in [{ value: 1, label: this.$t('pages.conutType1') }, { value: 2, label: this.$t('pages.conutType2') }]"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      :disabled="(onlyOperator&&item.value===2) || (onlyTerminal&&item.value===1)"
                      :class="[onlySupportCountByUser && item.value === 2 ? 'hidden-options' : '', onlySupportCountByTerminal && item.value === 1 ? 'hidden-options' : '']"
                    ></el-option>
                    <!-- 敏感关键字报表额外支持按关键字统计 -->
                    <el-option
                      v-if="currentConditionCode == 47"
                      :key="3"
                      :label="$t('pages.countType3')"
                      :value="3"
                    ></el-option>
                  </el-select>
                  <el-select
                    v-else
                    v-model="layoutTemp.queryCondition[currentConditionCode].countByObject"
                    is-filter
                    :placeholder="$t('text.select')"
                  >
                    <el-option
                      v-for="(item, index) in [{ value: 2, label: this.$t('pages.report_text4') }]"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </FormItem>
                <!-- 此处使用v-show是因为表单项若通过 v-if 动态渲染，初始不可见的表单项的校验规则会因为未注册而失效（旧版本关键字报表按关键字统计的查询条件会出现这种情况）-->
                <FormItem v-show="supportCountObject" :label="$t('pages.countObject')" prop="groupIds">
                  <tree-select
                    ref="objectTree"
                    node-key="id"
                    :height="350"
                    :width="500"
                    clearable
                    multiple
                    check-strictly
                    :menu-name="'reportMenu'"
                    :click-node="clickNode"
                    :local-search="false"
                    :checked-keys="checkedKeys"
                    is-filter
                    :leaf-key="layoutTemp.queryCondition[currentConditionCode].countByObject === 2 ? 'terminal' : 'user'"
                    @deptTreeData="deptTreeData"
                    @change="(key, nodeData) => entityIdChange(key, nodeData, layoutTemp.queryCondition[currentConditionCode])"
                  />
                </FormItem>
                <!--  -->
                <FormItem v-if="supportSortFied" :label="$t('pages.sortField')">
                  <el-select
                    ref="top"
                    v-model="layoutTemp.queryCondition[currentConditionCode].sortName"
                    filterable
                    @visible-change="visibleChange"
                  >
                    <!-- 自定义报表的排序项根据选择的数据项动态赋值 -->
                    <el-option
                      v-for="(item, index) in (customCodes.includes(currentConditionCode) ? customSortItems : sortFields[currentConditionCode])"
                      :key="index"
                      :label="$t(`table.${item.label}`)"
                      :value="item.prop"
                    ></el-option>
                  </el-select>
                </FormItem>
                <!-- 扫描任务：敏感扫描和标签扫描 -->
                <FormItem
                  v-if="[57, 94, 95].includes(currentConditionCode)"
                  :label="$t('pages.diskScan')"
                  prop="taskGuid"
                >
                  <el-select
                    ref="op"
                    v-model="layoutTemp.queryCondition[currentConditionCode].taskGuid"
                    filterable
                    @change="taskChange"
                  >
                    <el-option
                      v-for="option in guidOptions"
                      :key="option.key"
                      :value="option.key"
                      :label="option.name"
                    />
                  </el-select>
                  <el-select
                    v-if="showOption2"
                    v-model="layoutTemp.queryCondition[currentConditionCode].batchNo"
                    prop="batchNo"
                    @change="taskChange2"
                  >
                    <el-option
                      v-for="option in batchNoOptions"
                      :key="option.key"
                      :value="option.key"
                      :label="option.name.split('_')[1]"
                    />
                  </el-select>
                  <p v-if="!guidOptions.length" style="color:red">{{ $t('pages.cantAcquireTaskList') }}</p>
                </FormItem>
                <FormItem v-show="95 === currentConditionCode" :label="$t('pages.countObject')" prop="groupIds1">
                  <tree-select
                    ref="parentDept1"
                    node-key="id"
                    leaf-key="terminal"
                    :local-search="false"
                    :checked-keys="checkedKeys1"
                    collapse-tags
                    is-filter
                    multiple
                    check-strictly
                    :width="500"
                    @change="(key, nodeData) => parentIdSelectChange1(key, nodeData, layoutTemp.queryCondition[currentConditionCode])"
                  />
                </FormItem>
                <!-- 全盘扫描标签信息对比报表 -->
                <FormItem v-if="95 === currentConditionCode" :label="$t('pages.diskScan')" prop="taskGuid1">
                  <el-select
                    ref="op"
                    v-model="layoutTemp.queryCondition[currentConditionCode].taskGuid1"
                    filterable
                    @change="taskChange3"
                  >
                    <el-option
                      v-for="option in guidOptions"
                      :key="option.key"
                      :value="option.key"
                      :label="option.name"
                    />
                  </el-select>
                  <el-select
                    v-if="showOption2"
                    v-model="layoutTemp.queryCondition[currentConditionCode].batchNo1"
                    prop="batchNo1"
                  >
                    <el-option
                      v-for="option in batchNoOptions"
                      :key="option.key"
                      :value="option.key"
                      :label="option.name.split('_')[1]"
                    />
                  </el-select>
                  <p v-if="!guidOptions.length" style="color:red">{{ $t('pages.cantAcquireTaskList') }}</p>
                </FormItem>
                <FormItem v-show="95 === currentConditionCode" :label="$t('pages.countObject')" prop="groupIds2">
                  <tree-select
                    ref="parentDept2"
                    node-key="id"
                    leaf-key="terminal"
                    :local-search="false"
                    :checked-keys="checkedKeys2"
                    collapse-tags
                    is-filter
                    multiple
                    check-strictly
                    :width="500"
                    @change="(key, nodeData) => parentIdSelectChange2(key, nodeData, layoutTemp.queryCondition[currentConditionCode])"
                  />
                </FormItem>
                <!-- 数据变化报表 时间段 -->
                <FormItem v-if="61 === currentConditionCode" :label="$t('pages.startDate')">
                  <el-date-picker
                    v-model="tempQuery.startDate"
                    :clearable="false"
                    value-format="yyyy-MM-dd"
                    style="width:270px"
                    type="date"
                    :picker-options="pickerStartOptions"
                    :placeholder="$t('pages.startDate')"
                    @change="handleChange"
                  />
                </FormItem>
                <FormItem v-if="61 === currentConditionCode" :label="$t('pages.endDate')">
                  <el-date-picker
                    v-model="tempQuery.endDate"
                    :clearable="true"
                    style="width:270px"
                    value-format="yyyy-MM-dd"
                    type="date"
                    :picker-options="pickerEndOptions"
                    :placeholder="$t('pages.endDate') + $t('pages.emptyRepresentative')"
                  />
                </FormItem>

                <!-- 数据变化报表 业务名称 -->
                <FormItem v-if="61 == currentConditionCode" :label="$t('table.businessName')">
                  <el-input
                    v-model="layoutTemp.queryCondition[currentConditionCode].searchName"
                    clearable
                    :placeholder="$t('pages.regMessage_8')"
                  ></el-input>
                </FormItem>
                <!--  -->
                <FormItem v-if="supportTop" label="Top">
                  <el-select
                    ref="top"
                    v-model="layoutTemp.queryCondition[currentConditionCode].recordSize"
                    allow-create
                    filterable
                  >
                    <el-option
                      v-for="(item, index) in [{ value: 5, label: 5 }, { value: 10, label: 10 }, { value: 20, label: 20 }]"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </FormItem>
                <FormItem v-if="supportCountByGroup">
                  <el-checkbox
                    v-model="layoutTemp.queryCondition[currentConditionCode].groupType"
                    :false-label="0"
                    :true-label="1"
                    :label="$t('pages.countByGroup')"
                    class="group-type"
                  ></el-checkbox>
                  <el-checkbox
                    v-if="supportCollectSubDept()"
                    v-model="layoutTemp.queryCondition[currentConditionCode].collectSubDept"
                    :false-label="0"
                    :true-label="1"
                    :label="$t('pages.collectSubDept')"
                    class="group-type"
                    :disabled="disabledCollectSubDept"
                  ></el-checkbox>
                </FormItem>
              </Form>
            </div>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <!--保存并继续按钮：只有可以自定义配置的才有-->
        <el-button v-if="nonsupportReport" :loading="submitting" type="primary" @click="customListSave">
          <!-- {{ $t('button.saveAndContinue') }} -->
          保存到列表
        </el-button>
        <!--保存按钮：普通报表保存-->
        <el-button v-else type="primary" @click="saveQueryConditionChange">
          {{ $t('button.save') }}
        </el-button>
        <el-button v-if="nonsupportReport && singleSetting" type="primary" @click="queryConditionClosed">
          {{ $t('button.save') }}
        </el-button>
        <el-button @click="discardQueryConditionChange">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 面板配置弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="700px"
      @closed="dialogClosed"
    >
      <div v-show="showBase" class="base">
        <span class="label">{{ $t('pages.tagName') }}</span>
        <el-input
          v-model="layoutTemp.name"
          v-trim
          :maxlength="60"
          style="width: 230px; margin-left: 40px;"
          @blur="validateName"
        />
        <span style="color: red;">{{ isRequire }}</span>
        <p class="layout">
          <span class="label">{{ $t('pages.quickLayout') }}</span>
          <ul class="layout-setting">
            <li v-for="(item, index) in 8" :key="index" @click="quickSetting(index)">
              <svg-icon
                :icon-class="layoutIcon[index]"
                :class="{active: isSameLayout === false ? false : item == isSameLayout + 1}"
              />
            </li>
          </ul>
          <el-button class="custom-btn" @click="showBase = !showBase">{{ $t('pages.customLayout') }}</el-button>
        </p>
        <el-tabs
          v-model="activeTab"
          tab-position="left"
          @tab-click="tabClick"
        >
          <el-tab-pane v-for="item in visibleChartsOption" :key="item.menuCode" :name="item.menuCode">
            <div slot="label">
              <span :title="$t(`route.${item.title}`)">
                {{ $t(`route.${item.title}`) }}
              </span>
            </div>
          </el-tab-pane>
          <span style="line-height: 30px; color: red; float: left;">{{ reportRequire }}</span>
          <el-input
            v-model="searchText"
            v-trim
            clearable=""
            class="search-input"
            prefix-icon="el-icon-search"
            :placeholder="$t('pages.enterReportName')"
            maxlength="30"
          />
          <ul class="chart-lists">
            <li v-for="(item, index) in chartsList" v-show="showChartForSearch(item)" :key="index">
              <div class="chart-panel" @click="appendChart(item)">
                <div style="height: 28px;">
                  <el-button
                    v-if="!unsupoortModifyChart.includes(item.code)"
                    icon="el-icon-s-tools"
                    style="margin: 2px 2px 0 0; float: right;"
                    @click.stop.prevent="handleCustomConditionClick(item)"
                  ></el-button>
                </div>
                <p>{{ item.name }}</p>
                <svg-icon :icon-class="chartIcons[item.type]" class="chart-icon"/>
                <svg-icon
                  v-show="layoutTemp.reportNum.includes(item.code)"
                  icon-class="active"
                  class="selected-icon"
                ></svg-icon>
              </div>
            </li>
          </ul>
        </el-tabs>
      </div>
      <div v-show="!showBase" class="custom">
        <el-button @click="showBase = !showBase">{{ $t('button.return') }}</el-button>
        <ul class="custom-layout">
          <li v-for="(name, i) in rows" :key="i">
            <span class="row-name">{{ name }}</span>
            <ul class="layout-setting">
              <li v-for="(item, index) in 8" :key="index" @click="setting(i, index)">
                <svg-icon :icon-class="layoutIcon[index]" :class="{active: item == layoutTemp.panel[i].colType + 1}"/>
              </li>
            </ul>
            <div class="height-setting">
              {{ $t('pages.height') }}
              <el-select v-model="heightValue[i]" class="height-select" @change="(val) => heightChange(i, val)">
                <el-option
                  v-for="(item, index) in heightOption"
                  :key="index"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </div>
          </li>
        </ul>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveData">
          {{ $t('button.save') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DataPanel from '../components/dataPanel'
import RowPanel from './components/rowPanel'
import sortFields from './sortFields'
import {
  getReportListByUserId, getReportPanelByUserId,
  getEchartDataByCodes, addPanel, updatePanel, deletePanel, checkReportPanelNameExists
} from '@/api/dashboard'
import { debounce, flowAxis, timeAxis, convert, formatSeconds } from '@/utils'
import { getDataItems } from '@/utils/report'
import {
  getDiskScanGuidOptions, getTaskOptionStep1, getTaskOptionStep2
} from '@/api/report/baseReport/sensitiveControl/diskScanSensitive'
import { getTrwfePermission } from '@/api/report/baseReport/issueReport/detail'
import { filterAppRuntimeColumn } from '@/utils/reportPermissionFiltering'
// const reportList = JSON.parse('[{"code":1,"name":"终端接入数统计趋势图","type":"line","menuCode":"E"},{"code":2,"name":"策略数量统计报表","type":"pie","menuCode":"E"},{"code":3,"name":"计算机系统日志统计报表","type":"bar","menuCode":"E"},{"code":4,"name":"批量加解密报表","type":"pie","menuCode":"E"},{"code":5,"name":"批量加解密趋势图","type":"line","menuCode":"E"},{"code":6,"name":"打印报表","type":"pie","menuCode":"E"},{"code":7,"name":"打印趋势图","type":"line","menuCode":"E"},{"code":8,"name":"文件操作报表","type":"pie","menuCode":"E"},{"code":9,"name":"文件操作趋势图","type":"line","menuCode":"E"},{"code":10,"name":"移动存储报表","type":"bar","menuCode":"E"},{"code":11,"name":"移动存储趋势图","type":"line","menuCode":"F"},{"code":12,"name":"windows系统日志报表","type":"pie","menuCode":"F"},{"code":13,"name":"windows系统日志趋势图","type":"line","menuCode":"F"},{"code":14,"name":"网页浏览报表","type":"pie","menuCode":"F"},{"code":15,"name":"网页浏览趋势图","type":"line","menuCode":"F"},{"code":16,"name":"应用程序运行时长统计报表","type":"pie","menuCode":"F"},{"code":17,"name":"应用程序运行时长统计趋势图","type":"line","menuCode":"F"},{"code":18,"name":"邮件统计报表","type":"pie","menuCode":"F"},{"code":19,"name":"邮件统计趋势图","type":"line","menuCode":"F"},{"code":20,"name":"征兆报表","type":"pie","menuCode":"F"},{"code":21,"name":"征兆趋势图","type":"line","menuCode":"C"},{"code":22,"name":"数据安全报表","type":"bar","menuCode":"C"},{"code":23,"name":"数据安全趋势图","type":"line","menuCode":"C"},{"code":24,"name":"即时通讯报表","type":"pie","menuCode":"C"},{"code":25,"name":"即时通讯趋势图","type":"line","menuCode":"C"},{"code":26,"name":"硬件资产报表","type":"pie","menuCode":"C"},{"code":27,"name":"硬件资产趋势图","type":"line","menuCode":"C"},{"code":28,"name":"软件资产报表","type":"pie","menuCode":"C"},{"code":29,"name":"软件资产趋势图","type":"line","menuCode":"C"},{"code":30,"name":"综合报表","type":"bar","menuCode":"C"},{"code":31,"name":"综合趋势图","type":"line","menuCode":"D"},{"code":32,"name":"审批报表","type":"bar","menuCode":"D"},{"code":33,"name":"审批趋势图","type":"line","menuCode":"D"},{"code":34,"name":"终端上线统计趋势图","type":"line","menuCode":"D"},{"code":35,"name":"终端操作系统报表","type":"pie","menuCode":"D"},{"code":36,"name":"终端版本报表","type":"pie","menuCode":"D"}]')

export default {
  name: 'CustomLayout',
  components: { DataPanel, RowPanel },
  data() {
    return {
      customListConfigName: '',
      tempQuery: {
        startDate: '',
        endDate: ''
      },
      // 限制开始时间不能大于结束时间，且当前时间之后的时间不可选
      pickerStartOptions: {
        disabledDate: time => {
          const endTime = this.tempQuery.endDate;
          if (endTime) {
            return time.getTime() > new Date(endTime).getTime() || time.getTime() > Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天;
          } else {
            return time.getTime() > Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天;
          }
        }
      },
      pickerEndOptions: {
        disabledDate: time => {
          const beginTime = this.tempQuery.startDate;
          if (beginTime) {
            return time.getTime() < new Date(beginTime).getTime() || time.getTime() > Date.now();
          } else {
            return time.getTime() > Date.now();
          }
        }
      },
      sortFields,
      deptData: [],
      dialogFormVisible: false,
      conditionDialogVisible: false,
      currentIsTrwfeSignReport: false, // 当前进行配置的报表是否为审批征兆报表
      currentConditionCode: undefined,
      currentCharType: 0,
      dataLoadPromise: undefined,
      sensitiveGuidOption: [],
      diskScanGuidOption: [],
      loadGuidOption: false,      // 是否需要加载 guidOptions
      batchNoOptions: [],
      batchNoOptions1: [],
      showOption2: false,
      showOption3: false,
      showBase: true,             // 面板配置显示基础页
      userId: undefined,          // 当前用户id
      curIndex: 0,                // 当前页 index
      layoutIcon: ['layout1-1', 'layout1-2', 'layout2-1', 'layout1-1-1', 'layout1-2-1', 'layout1', 'layout1-3', 'layout3-1'],
      activeTab: 'ALL',
      checkedKeys: [],
      checkedKeys1: [],
      checkedKeys2: [],
      // 存放部门树当前选中数据
      clickNode: [],
      chartsOption: [             // 报表分类
        { title: 'all', menuCode: 'ALL' },
        { title: 'terminalManage', menuCode: 'B' },
        { title: 'behaviorManage', menuCode: 'C' },
        { title: 'netManage', menuCode: 'D' },
        { title: 'dataEncryption', menuCode: 'E' },
        { title: 'contentStrategy', menuCode: 'F' },
        { title: 'approvalManage', menuCode: 'G' },
        { title: 'IntegrateLog', menuCode: 'H' },
        { title: 'custom', menuCode: 'CUSTOM' },
        { title: 'signReport', menuCode: 'SIGN' }
        // { title: 'nacReport', menuCode: 'NAC' }
      ],
      chartIcons: {
        line: 'chart-line',
        bar: 'chart-bar',
        pie: 'chart-pie'
      },
      unsupoortModifyChart: [1, 2, 3, 34, 35, 36],
      chartCodesSet: new Set(),   // 已添加的报表的 code 的集合
      chartsDataMap: {},          // 报表数据 map，以报表的 code 作为 key
      searchText: '',             // 报表搜索内容
      chartsList: [],             // 可选择的报表列表
      chartMenuCodeMap: {},       // 可选择的报表分类的 Map
      pageSize: 6,                // 报表选择每页显示数量
      showCharts: [],             // 首页展示的面板数据
      dialogStatus: '',
      layoutTemp: {},
      defaultLayout: {
        id: null,                 // 面板id
        name: '',                 // 面板name
        userId: null,             // 当前用户id
        reportNum: [],            // 报表的code数组，提交数据时需要转换为字符串
        panel: '',
        queryCondition: {},       // 每张报表对应的查询条件，提交数据时转成字符串
        echartsOptionDTOs: []
      },
      rows: [
        this.$t('pages.row1'), this.$t('pages.row2'), this.$t('pages.row3'), this.$t('pages.row4'),
        this.$t('pages.row5'), this.$t('pages.row6'), this.$t('pages.row7'), this.$t('pages.row8'),
        this.$t('pages.row9'), this.$t('pages.row10')
      ],
      defaultHeight: [400, 400, 400, 400, 400, 400, 400, 400, 400, 400],
      heightOption: [300, 350, 400, 450, 500, 550, 600],
      termUserCountData: {
        terminalOnline: 0,
        terminalTotal: 0,
        offlineWeek: 0,
        offlineMonth: 0,
        userOnline: 0,
        userTotal: 0
      },
      loading: false,
      submitting: false,
      layoutTypes: [ // 每一行的布局类型，共8种
        [{ span: 12 }, { span: 12 }],
        [{ span: 8 }, { span: 16 }],
        [{ span: 16 }, { span: 8 }],
        [{ span: 8 }, { span: 8 }, { span: 8 }],
        [{ span: 6 }, { span: 12 }, { span: 6 }],
        [{ span: 24 }],
        [{ span: 6 }, { span: 18 }],
        [{ span: 18 }, { span: 6 }]
      ],
      isRequire: '',        // 面板名称提示信息
      reportRequire: '',    // 勾选报表提示信息
      saveIndex: false, // 是否是通过切换面板触发的 保存当前面板序号
      dateTypeOptions: [
        { value: 5, label: this.$t('pages.dateTypeOptions1') },
        { value: 2, label: this.$t('pages.dateTypeOptions2') },
        { value: 1, label: this.$t('pages.dateTypeOptions3') },
        { value: 3, label: this.$t('pages.dateTypeOptions4') },
        { value: 4, label: this.$t('pages.dateTypeOptions5') }
      ],
      trendDateTypeOptions: [
        { value: 5, label: this.$t('pages.trendDateTypeOptions1') },
        { value: 3, label: this.$t('pages.trendDateTypeOptions2') },
        { value: 4, label: this.$t('pages.trendDateTypeOptions3') },
        { value: 2, label: this.$t('pages.trendDateTypeOptions4') },
        { value: 1, label: this.$t('pages.trendDateTypeOptions5') }
      ],
      restoreQueryCondition: null,
      // 自定义报表code
      customReportCode: 998,
      // 自定义报表和自定义趋势图对应的报表code
      customCodes: [998, 999],
      // 自定义数据项
      customDataItems: getDataItems(),
      // 查询条件表单校验规则
      conditionRules: {
        reportName: [
          { required: true, message: this.$t('pages.regMessage_11'), trigger: 'blur' },
          { validator: this.reportNameValidate, trigger: 'blur' }
        ],
        customDataItems: [
          { type: 'array', required: true, message: this.$t('valid.requireDataItem'), trigger: 'change' }
        ],
        taskGuid: [
          { required: true, message: this.$t('pages.regMessage_12'), trigger: 'change' }
        ],
        taskGuid1: [
          { required: true, message: this.$t('pages.regMessage_12'), trigger: 'change' }
        ],
        batchNo: [
          { required: true, message: this.$t('pages.regMessage_12'), trigger: 'change' }
        ],
        batchNo1: [
          { required: true, message: this.$t('pages.regMessage_12'), trigger: 'change' }
        ],
        groupIds: [
          {
            validator: (rule, value, callback) => {
              const queryCondition = this.layoutTemp.queryCondition[this.currentConditionCode]
              if (this.supportCollectSubDept() && queryCondition.collectSubDept === 1 && !queryCondition.groupIds) {
                callback(new Error(this.$t('pages.regMessage_13')))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      },
      // 自定义报表选中数据
      active: undefined,
      // 自定义列表源数据(接口返回的数据存放在这里--不进行修改)
      customListOrigin: [],
      customTrendListOrigin: [],
      // 自定义列表页面显示数据(在customListOrigin的基础上进行页面操作)
      customList: [],
      // 自定义报表排序项
      customSortItems: [],
      // 是否只支持按操作员统计（审批报表只支持按操作员统计）
      onlyOperator: false,
      // 是否只支持按终端统计（资产报表只支持按终端统计）
      onlyTerminal: false,
      // 自定义报表互斥数据项
      mutexOptions: {
        'trwfeSumAll': ['hardwareAssetChangeTimes', 'softwareAssetChangeTimes'],
        'hardwareAssetChangeTimes': ['trwfeSumAll'],
        'softwareAssetChangeTimes': ['trwfeSumAll']
      },
      // 只支持按终端统计的报表code
      onlySupportCountByTerminalCodes: [26, 28],
      // 不支持报表日期的报表code
      notSupportedReportDateCodes: [57, 61, 94, 95],
      // 不支持统计类型的报表code
      notSupportedStatisticalTypeCodes: [51, 52, 53, 54, 55, 57, 61, 62, 93, 94, 95],
      // 不支持统计对象的报表code
      notSupportedStatisticalObjectCodes: [51, 52, 53, 54, 55, 56, 57, 60, 61, 62, 93, 94, 95],
      // 不支持排序项的报表code
      notSupportedSortItemCodes: [51, 52, 53, 54, 55, 56, 57, 60, 61, 93, 94, 95],
      // 不支持Top的报表code
      notSupportedTopCodes: [51, 52, 53, 54, 55, 56, 57, 60, 61, 93, 94, 95],
      // 不支持按部门统计的报表code
      notSupportedByDeptCodes: [51, 52, 53, 54, 55, 56, 57, 60, 61, 93, 94, 95],
      // 不支持归集子部门功能的报表code
      notSupportedCollectSubDeptCodes: [1, 2, 3, 34, 35, 36, 51, 52, 53, 54, 55, 56, 57, 60, 61, 93, 94, 95],
      // 不支持自定义功能的报表code
      notSupportedCustomCodes: [1, 2, 3, 34, 35, 36, 51, 52, 53, 54, 55, 56, 57, 60, 61, 93, 94, 95],
      // 是否通过首页面板报表的设置图标修改
      singleSetting: false
    }
  },
  computed: {
    textMap() {
      return {
        update: this.$t('pages.updatePanel'),
        create: this.$t('pages.createPanel')
      }
    },
    // 面板每一行报表的高度数组
    heightValue() {
      const layout = this.layoutTemp
      return layout ? layout.panel.map(item => item.height) : [...this.defaultHeight]
    },
    // 可显示的报表分类项
    visibleChartsOption() {
      // 根据报表分类 map 过滤不显示的分类
      const visibleOptions = this.chartsOption.filter(opt => this.chartMenuCodeMap[opt.menuCode])
      // 如果显示的分类数量大于 1，则将 全部 添加到第一个
      if (visibleOptions.length > 1) {
        visibleOptions.unshift(this.chartsOption[0])
      }
      return visibleOptions
    },
    isSameLayout() {
      const layout = this.layoutTemp.panel
      let isSame = true
      let type
      for (let i = 0; i < layout.length; i++) {
        const el = layout[i]
        if (i == 0) {
          type = el.colType
        } else {
          if (type != el.colType) {
            isSame = false
            break
          }
        }
      }
      return isSame ? type : false
    },
    // 是否禁用归集子部门
    disabledCollectSubDept() {
      if (this.supportCollectSubDept()) {
        const condition = this.layoutTemp.queryCondition[this.currentConditionCode]
        return condition !== null && condition !== undefined && condition.groupType === 0
      }
      return false;
    },
    supportDimBaseType() {
      // 全盘扫描报表、数据变化报表 不支持时间统计维度
      return !this.notSupportedReportDateCodes.includes(this.currentConditionCode)
    },
    supportCountByObject() {
      // 全盘扫描报表、数据变化报表、NAC违规接入报表 不支持按终端、操作员统计
      return !this.notSupportedStatisticalTypeCodes.includes(this.currentConditionCode)
    },
    onlySupportCountByTerminal() {
      // 硬件资产、软件资产 只支持按终端统计
      return this.onlySupportCountByTerminalCodes.includes(this.currentConditionCode)
    },
    onlySupportCountByUser() {
      // 审批报表、审批征兆报表只支持按操作员统计
      return this.currentIsTrwfeSignReport
    },
    supportCountObject() {
      const code = this.currentConditionCode
      // 敏感关键字按关键字统计时，不支持统计对象
      if (code == 47 && this.layoutTemp.queryCondition[code].countByObject == 3) {
        return false
      }
      /*
        趋势图、
        敏感违规统计报表、零星违规统计报表、敏感征兆、全盘扫描、风险报表、数据变化报表、敏感关键字
        NAC违规接入报表
        不支持统计对象
      */
      return this.currentCharType != 'line' && !this.notSupportedStatisticalObjectCodes.includes(code)
    },
    supportSortFied() {
      // 趋势图、敏感违规统计报表、零星违规统计报表、敏感征兆、全盘扫描、风险报表 不支持排序项
      return this.currentCharType != 'line' && !this.notSupportedSortItemCodes.includes(this.currentConditionCode)
    },
    supportTop() {
      // 趋势图、敏感违规统计报表、零星违规统计报表、敏感征兆、全盘扫描、风险报表不支持top
      return this.currentCharType != 'line' && !this.notSupportedTopCodes.includes(this.currentConditionCode)
    },
    supportCountByGroup() {
      const code = this.currentConditionCode
      // 敏感关键字按关键字统计时，不支持统计部门
      if (code == 47 && this.layoutTemp.queryCondition[code].countByObject == 3) {
        return false
      }
      // 趋势图、敏感违规统计报表、零星违规统计报表、敏感征兆、全盘扫描、风险报表不支持按部门统计
      return this.currentCharType != 'line' && !this.notSupportedByDeptCodes.includes(code)
    },
    /**
     * 判断是否需要自定义多张报表展示在首页，如果返回true，则代表可以自定义多张
     * 方法中的code代表不需要自定义多项的报表code
     * */
    nonsupportReport() {
      // DLP报表：1, 2, 3, 34, 35, 36敏感报表：51, 52, 53, 54, 55, 56, 57, 60数据变化报表61
      // 征兆报表10000-20000之间
      const standardSignLowerBound = 10000;
      const standardSignUpperBound = 20000;
      const code = this.currentConditionCode
      return !(this.notSupportedCustomCodes.includes(code) || (standardSignLowerBound <= code && code <= standardSignUpperBound));
    },
    guidOptions() {
      // 趋势图、敏感违规统计报表、零星违规统计报表、敏感征兆、全盘扫描、风险报表不支持top
      return this.currentConditionCode == 57 ? this.sensitiveGuidOption : this.diskScanGuidOption
    }
  },
  watch: {
    visibleChartsOption(val) {
      if (val.length > 0) {
        this.activeTab = val[0].menuCode
      }
    },
    disabledCollectSubDept: {
      handler(newValue) {
        if (newValue) {
          this.layoutTemp.queryCondition[this.currentConditionCode].collectSubDept = 0
        }
      }
    }
  },
  created() {
    this.userId = this.$store.getters.userId
    this.filterTrwfeLogSortFieldsByPermission()
    this.filterSortFieldsByPermission()
    this.resetTemp()
    this.initLayout()
    window.onresize = function() {
    }
    // 切换面板时需要存储当前面板的索引，为避免频繁切换面板造成多次提交数据，这边进行防抖处理
    this.savePanelIndex = debounce(this.updateData, 2000, false)
  },
  activated() {
    this.asyncLoadTermUserNum()
  },
  methods: {
    /**
     * 可自定义多项报表：名称重复判断
     * */
    reportNameValidate(rule, value, callback) {
      if (this.customList.length > 0 && value !== '') {
        this.customList.forEach((item, index) => {
          if (this.active === undefined) { // 新增
            if (item.reportName === value) {
              callback(new Error(this.$t('pages.regMessage_14')))
            }
          } else { // 修改
            if (item.reportName === value && index !== this.active) {
              callback(new Error(this.$t('pages.regMessage_14')))
            }
          }
        })
      }
      callback();
    },
    /**
     * 保存（可自定义多项报表）
     * */
    customListSave() {
      this.submitting = true
      this.$refs['conditionForm'].validate((valid) => {
        if (valid) {
          const code = this.currentConditionCode
          if (this.active === undefined) { // 新增
            const isExist = this.checkElements('buttonAdd')
            if (isExist > 9) {
              this.$message({
                title: this.$t('text.prompt'),
                message: this.$t('pages.panelLimit'),
                type: 'error',
                duration: 2000
              })
              this.submitting = false
              return true
            }
            this.customList.push({ ...this.layoutTemp.queryCondition[code] })
          } else { // 修改
            this.$set(this.customList, this.active, { ...this.layoutTemp.queryCondition[code] });
          }
          this.clearFormCustom()
          this.$nextTick(() => {
            this.$refs.conditionForm.clearValidate();
          })
          this.submitting = false
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.saveSuccess'),
            type: 'success',
            duration: 2000
          })
        } else {
          this.submitting = false
          return false
        }
      })
    },
    /**
     * 清空表单--可自定义多项报表（新增，修改时，清空表单）
     * */
    clearFormCustom() {
      this.active = undefined
      const code = this.currentConditionCode
      const universalPrefix = this.layoutTemp.queryCondition[code]
      // 报表名称
      if (this.nonsupportReport) {
        universalPrefix.reportName = ''
      }
      // 报表日期
      if (this.supportDimBaseType) {
        universalPrefix.dimBaseType = 1
      }
      // 统计类型
      if (this.supportCountByObject) {
        if (this.currentIsTrwfeSignReport) {
          universalPrefix.countByObject = 1
        } else {
          universalPrefix.countByObject = 2
        }
      }
      // 统计对象
      if (this.supportCountObject) {
        this.checkedKeys.splice(0)
        universalPrefix.objectIds = ''
        universalPrefix.groupIds = ''
        universalPrefix.chckedObjectIds = ''
        universalPrefix.checkedGroupIds = ''
      }
      // 扫描任务
      // if (code == 57) { universalPrefix.taskGuid = '' }
      // // 数据变化报表
      // if (code == 61) {
      //   // 开始时间，结束时间
      //   this.tempQuery.startDate = ''
      //   this.tempQuery.endDate = ''
      //   // 业务名称
      //   universalPrefix.searchName = ''
      // }
      // Top
      if (this.supportTop) {
        universalPrefix.recordSize = 10
      }
      // 按部门统计
      if (this.supportCountByGroup) {
        universalPrefix.groupType = 1
      }
      // 部门归集
      if (this.supportCollectSubDept()) {
        this.$set(universalPrefix, 'collectSubDept', 0)
      }
      if (this.customCodes.includes(code)) {
        universalPrefix.customDataItems = [] // 自定义报表/趋势--数据项
      }
      if (this.isCustomReport()) {
        universalPrefix.sortName = '' // 自定义报表--排序项
      }
    },
    /**
     * 判断选中的code是否为可自定义多项的code
     * @param value 当前选中的code
     * @param array1 无需自定义多项的code
     * @param lowerBound 征兆报表下限
     * @param upperBound 征兆报表上限
     * */
    isIncluded(value, array1, lowerBound, upperBound) {
      return array1.includes(value) || (value >= lowerBound && value <= upperBound);
    },
    /**
     * 自定义选中旧数据兼容
     * @param type 区分事件,具体描述参考方法checkElements
     * @param value 报表code
     * */
    falseNum(type, value) {
      if (type === 'buttonAdd' && value === this.currentConditionCode) {
        return this.customList.length
      } else {
        if (Array.isArray(this.layoutTemp.queryCondition[value])) {
          return this.layoutTemp.queryCondition[value].length
        } else {
          // 兼容旧数据，如果返回数据为对象，选中数据加1
          return 1
        }
      }
    },
    /**
     * 判断当前选中的报表有多少条，上限条件限制
     * @param type 区分是新增保存按钮'buttonAdd'还是选中事件'selected'（外侧弹框直接选中时，直接获取长度即可，在内测弹框中新增保存时，需要统计当前报表对应的左侧列表编辑情况）
     * return 返回数字，代表当前选中数量
     * */
    checkElements(type) {
      // 当前已选中code
      const selectCode = this.layoutTemp.reportNum
      // 以下是选中时，统计数为1的code
      if (selectCode.length > 0) {
        const lowerBound = 10000;
        const upperBound = 20000;
        // 如果results返回的exists为true,则表示长度为1，否则都是可以自定义多项的，长度要重新计算（弹框左侧列表长度）
        const results = selectCode.map(value => ({
          value,
          exists: this.isIncluded(value, this.notSupportedCustomCodes, lowerBound, upperBound)
        }));
        // 处理数据，生成具体数量
        const processedData = results.map(item => {
          if (item.exists) {
            return { ...item, num: 1 }
          } else {
            return {
              ...item,
              // type === 'buttonAdd'自定义报表配置中点击新增或保存时，当前报表的数量长度与根据编辑修改
              num: this.falseNum(type, item.value)
            }
          }
        });
        // 最终统计选中数
        let resultNum = 0
        processedData.forEach(obj => {
          resultNum += obj.num;
        });
        return resultNum
      } else {
        return 0
      }
    },
    /**
     * 新增（可自定义多项报表--列表新增按钮点击）
     */
    addCustomList() {
      const isExist = this.checkElements('buttonAdd')
      if (isExist >= 10) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.panelLimit'),
          type: 'error',
          duration: 2000
        })
        return true
      }
      this.clearFormCustom()
      this.$nextTick(() => {
        this.$refs.conditionForm.clearValidate();
      })
    },
    /**
     * 修改（可自定义多项报表--列表修改按钮点击）
     * @param index 索引
     * @param item 当前行信息
     */
    editorList(index, item) {
      this.active = index
      const code = this.currentConditionCode
      this.layoutTemp.queryCondition[code] = { ...item }
      this.clickNode = []
      // 统计对象
      this.checkedKeys = []
      this.restoreQueryCondition = JSON.parse(JSON.stringify(this.layoutTemp.queryCondition[code]))
      const { chckedObjectIds, checkedGroupIds, customDataItems } = this.restoreQueryCondition
      this.checkedKeys = [...(chckedObjectIds || '').split(','), ...(checkedGroupIds || '').split(',')]
      this.onlyOperator = false
      this.onlyTerminal = false
      // 排序项
      if (this.customCodes.includes(code)) {
        this.customSortItems = this.customDataItems.filter(item => customDataItems.includes(item.prop))
      }
      if (this.isCustomReport()) {
        this.onlyOperator = customDataItems.includes('trwfeSumAll')
        this.onlyTerminal = customDataItems.includes('hardwareAssetChangeTimes') || customDataItems.includes('softwareAssetChangeTimes')
      }
      this.$nextTick(() => {
        this.$refs.conditionForm.clearValidate();
      })
    },
    /**
     * 删除（可自定义多项报表--列表删除按钮点击）
     * @param item 当前行信息
     */
    deleteCustomList(item) {
      let msg = ''
      if (item.reportName) {
        msg = this.$t('pages.regMessage_15') + '“' + item.reportName + '”？'
      }
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        const indexToDelete = this.customList.findIndex(list => list.reportName === item.reportName);
        // 如果找到了匹配项，则删除它
        if (indexToDelete !== -1) {
          this.customList.splice(indexToDelete, 1);
          // 删除成功后，如果左侧列表为空，则清空右侧表单，否则选中第一个，右侧表单显示第一条数据
          if (this.customList.length === 0) {
            this.clearFormCustom()
            this.$nextTick(() => {
              this.$refs.conditionForm.clearValidate();
            })
          } else {
            this.editorList(0, this.customList[0])
          }
        }
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.deleteSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(() => {
      })
    },
    /**
     *数据项为空时，清空排序项
     * */
    visibleChange() {
      const code = this.currentConditionCode
      if (this.customCodes.includes(code)) {
        if (this.layoutTemp.queryCondition[code].customDataItems.length === 0) {
          this.customSortItems = []
        }
      }
    },
    handleChange(v) {
      console.log(v)
    },
    getSensitiveGuidOption() {
      getTaskOptionStep1({ taskType: 3 }).then(res => {
        this.sensitiveGuidOption = res.data
      })
    },
    getDiskScanGuidOption() {
      getTaskOptionStep1({ taskType: 8 }).then(res => {
        this.diskScanGuidOption = res.data
      })
    },
    getTaskOptionStep1() {
      this.getSensitiveGuidOption()
      this.getDiskScanGuidOption()
    },
    getTaskOptionStep2(fieldName, batchNoOptions) {
      const queryCondition = this.layoutTemp.queryCondition[this.currentConditionCode]
      getTaskOptionStep2({ guid: queryCondition.taskGuid }).then(res => {
        batchNoOptions.splice(0, batchNoOptions.length, ...res.data)
        if (this.batchNoOptions.length > 0 && queryCondition[fieldName] === '') {
          this.$set(queryCondition, fieldName, this.batchNoOptions[0].key)
        }
      })
    },
    taskChange2(data) {
    },
    /**
     * 任务下拉框选择事件
     */
    taskChange(data) {
      const selectedObj = this.guidOptions.find(item => item.key === data);
      this.showOption2 = false
      if (selectedObj.id) {
        this.showOption2 = true
        this.getTaskOptionStep2('batchNo', this.batchNoOptions)
      }
      this.$set(this.layoutTemp.queryCondition[this.currentConditionCode], 'batchNo', '')
    },
    taskChange3(data) {
      const selectedObj = this.guidOptions.find(item => item.key === data);
      this.showOption3 = false
      if (selectedObj.id) {
        this.showOption3 = true
        this.getTaskOptionStep2('batchNo1', this.batchNoOptions1)
      }
      this.$set(this.layoutTemp.queryCondition[this.currentConditionCode], 'batchNo1', '')
    },
    getDiskScanGuidOptions() {
      // 获取全盘扫描任务
      getDiskScanGuidOptions().then(res => {
        // this.guidOptions 通过计算属性获取
        // this.guidOptions = res.data
      })
    },
    /**
     * 获取树结构数据（TreeSelect组件传递给父组件数据）
     * 注：该数据不是一次性全部加载出来的，在组件数据展开时会发生变化
     * */
    deptTreeData(val) {
      this.deptData = val
      // console.log('val111111111111', JSON.parse(JSON.stringify(val)))
    },
    /**
     * 遍历树结构数据
     * @param nodes 当前树结构数据
     * @param nodeData 当前选中的数据（因为通过选中判断是否可选所以判断是取第一个即可）
     * (直接点击)nodeData[0].type 1、终端，2、操作员，3、终端分组，4、操作员分组
     * (查询点击)nodeData[0].type 1、终端，2、操作员，G、终端分组，G、操作员分组
     * */
    getCheckIdsAll(nodes = [], nodeData) {
      // console.log('nodes', nodes)
      if (nodes != null && nodes.length > 0) {
        nodes.forEach(item => {    // 遍历树 拼入相应的disabled
          if (nodeData.length > 0) {
            if (JSON.stringify(item) != '{}') {
              if (nodeData[0].type != item.type) {
                this.$set(item, 'disabled', true)
                this.getCheckIdsAll(item.children, nodeData)
              } else {
                this.$set(item, 'disabled', false)
                this.getCheckIdsAll(item.children, nodeData)
              }
            }
          } else {
            this.$set(item, 'disabled', false)
            this.getCheckIdsAll(item.children, nodeData)
          }
        })
      }
    },
    parentIdSelectChange1(key, nodeData, query) {
      const queryCondition = this.layoutTemp.queryCondition[this.currentConditionCode]
      if (nodeData && nodeData.length > 0) {
        const objectIds = []
        const groupIds = []
        const chckedObjectIds = []
        const checkedGroupIds = []
        nodeData.forEach(item => {
          if (item.dataType === 'G') {
            groupIds.push(item.dataId)
            checkedGroupIds.push(item.id)
          } else {
            objectIds.push(item.dataId)
            chckedObjectIds.push(item.id)
          }
        })
        queryCondition.objectIds = objectIds.join(',')
        queryCondition.groupIds = groupIds.join(',')
        queryCondition.checkedGroupIds = checkedGroupIds.join(',')
        queryCondition.chckedObjectIds = chckedObjectIds.join(',')
      } else {
        queryCondition.objectIds = ''
        queryCondition.groupIds = ''
        queryCondition.checkedGroupIds = ''
        queryCondition.chckedObjectIds = ''
      }
    },
    parentIdSelectChange2(key, nodeData, query) {
      const queryCondition = this.layoutTemp.queryCondition[this.currentConditionCode]
      if (nodeData && nodeData.length > 0) {
        const objectIds = []
        const groupIds = []
        const chckedObjectIds = []
        const checkedGroupIds = []
        nodeData.forEach(item => {
          if (item.dataType === 'G') {
            groupIds.push(item.dataId)
            checkedGroupIds.push(item.id)
          } else {
            objectIds.push(item.dataId)
            chckedObjectIds.push(item.id)
          }
        })
        queryCondition.objectIds1 = objectIds.join(',')
        queryCondition.groupIds1 = groupIds.join(',')
        queryCondition.checkedGroupIds1 = checkedGroupIds.join(',')
        queryCondition.chckedObjectIds1 = chckedObjectIds.join(',')
      } else {
        queryCondition.objectIds1 = ''
        queryCondition.groupIds1 = ''
        queryCondition.checkedGroupIds1 = ''
        queryCondition.chckedObjectIds = ''
      }
    },
    /**
     * 统计部门选中时做禁用操作
     * */
    entityIdChange(key, nodeData, query) {
      // console.log('key', key)
      // console.log('nodeData点击', JSON.parse(JSON.stringify(nodeData)))
      const queryCondition = this.layoutTemp.queryCondition[this.currentConditionCode]
      queryCondition.objectType = queryCondition.countByObject == 1 ? 2 : 1
      if (nodeData && nodeData.length > 0) {
        const objectIds = []
        const chckedObjectIds = []
        const groupIds = []
        const checkedGroupIds = []
        nodeData.forEach(item => {
          if (item.dataType === 'G') {
            groupIds.push(item.dataId)
            checkedGroupIds.push(item.id)
          } else {
            objectIds.push(item.dataId)
            chckedObjectIds.push(item.id)
          }
        })
        queryCondition.objectIds = objectIds.join(',')
        queryCondition.groupIds = groupIds.join(',')
        queryCondition.chckedObjectIds = chckedObjectIds.join(',')
        queryCondition.checkedGroupIds = checkedGroupIds.join(',')
      } else {
        queryCondition.objectIds = ''
        queryCondition.groupIds = ''
        queryCondition.chckedObjectIds = ''
        queryCondition.checkedGroupIds = ''
      }
      // console.log('clickNode', JSON.parse(JSON.stringify(this.clickNode)))
      // 选中，争对选中做是否可选限制
      // this.getCheckIdsAll(this.deptData, this.clickNode)
    },
    /**
     * 统计类型选中发生变化时
     * */
    handleCount(value) {
      const code = this.currentConditionCode
      const queryCondition = this.layoutTemp.queryCondition[code]
      // 敏感关键字报表特殊处理逻辑
      if (47 === code) {
        if (3 === value) {
          // 按关键字统计时不支持按部门统计
          queryCondition.groupType = 0
          queryCondition.collectSubDept = 0
          queryCondition.objectIds = ''
          queryCondition.groupIds = ''
          queryCondition.chckedObjectIds = ''
          queryCondition.checkedGroupIds = ''
        } else {
          queryCondition.groupType = 1
          if (queryCondition.collectSubDept === undefined) {
            // 非按关键字统计且查询条件不存在collectSubDept时给collectSubDept设置初始值（旧版本关键字报表按关键字统计的查询条件会出现这种情况）
            this.$set(queryCondition, 'collectSubDept', 0)
          }
        }
      }
      // 应用程序运行时长报表按操作员统计时只支持应用程序运行时长
      if (16 === code) {
        this.handleAppRuntimeReportSortFields(value)
        queryCondition.sortName = ''
        if (this.sortFields[code].length > 0) {
          queryCondition.sortName = this.sortFields[code][0].prop
        }
      }
      this.checkedKeys.splice(0)
      queryCondition.objectType = queryCondition.countByObject == 1 ? 2 : 1
      queryCondition.objectIds = ''
      // 切换时，清除对统计对象的选中和禁用操作
      this.clickNode = []
      this.getCheckIdsAll(this.deptData, this.clickNode)
    },
    filterTrwfeLogSortFieldsByPermission() {
      const key = 32
      const deleteFields = []
      getTrwfePermission().then(res => {
        const permissionList = res.data
        for (const field of this.sortFields[key]) {
          // console.log(this.$t('table.' + field.label))
          if (!permissionList.some(v => v == this.$t('table.' + field.label))) {
            deleteFields.push(field)
          }
        }
        this.sortFields[key] = this.sortFields[key].filter(field => !deleteFields.includes(field))
      })
    },
    filterSortFieldsByPermission() {
      for (const key in this.sortFields) {
        const deleteFields = []
        for (const field of this.sortFields[key]) {
          if (field.permission) {
            if (!this.hasPermission(field.permission)) {
              deleteFields.push(field)
            }
          }
        }
        this.sortFields[key] = this.sortFields[key].filter(field => !deleteFields.includes(field))
      }
    },
    /**
     * 打开--查询条件弹框显示
     * */
    handleCustomConditionClick(item) {
      const { name, type, code, logType } = item
      
      this.customListConfigName = `${this.$t('route.custom')}“${name}”`
      this.customListOrigin = []
      this.customList = []
      // 判断是否可继续添加报表，若不可以则不允许打开
      if (this.appendChart(item, false)) {
        return
      }
      this.currentCharType = type
      this.currentConditionCode = code
      const queryCondition = this.layoutTemp.queryCondition[code]
      // 审批报表或审批征兆报表
      this.currentIsTrwfeSignReport = logType == 32 || code == 32
      if (this.currentIsTrwfeSignReport) {
        this.processQueryCondition(code, queryCondition, 'currentIsTrwfeSignReport')
      }
      // 如果当前是全盘扫描报表 且 没有 guid ，默认给它一个
      this.processQueryCondition(code, queryCondition, 'fullScan')
      // 如果是数据变动报表 且 没有起始时间 默认给它一个七天前的时间
      this.processQueryCondition(code, queryCondition, 'dataChange')
      // 应用程序运行时长报表按操作员统计时排序项只支持应用程序运行时长
      this.processQueryCondition(code, queryCondition, 'applicationDuration')
      // 报表可自定义多张
      if (this.nonsupportReport) {
        // 多张自定义报表的列表--后台返回赋值；若后台返回数组，则直接赋值，否则需要兼容旧数据
        if (Array.isArray(queryCondition)) {
          this.customListOrigin = JSON.parse(JSON.stringify(queryCondition))
        } else {
          // 兼容旧数据，旧数据无reportName字段，如果currentCodeQuery没有reportName字段，需要set一个，否则可能会影响input双向绑定
          if (!queryCondition.hasOwnProperty('reportName')) {
            this.$set(queryCondition, 'reportName', '');
          }
          // 如果存在旧数据，获取报表名称
          const reportName = this.getReportName(this.layoutTemp.echartsOptionDTOs, code)
          queryCondition.reportName = reportName === '' ? name : reportName;
          // 列表赋值，如果是自定义报表，需要数据项有选中才可赋值，普通报表直接赋值
          if (this.customCodes.includes(code)) {
            if (queryCondition.customDataItems.length > 0) {
              this.customListOrigin.push({ ...queryCondition })
            }
          } else {
            this.customListOrigin = []
            this.customListOrigin.push({ ...queryCondition })
          }
        }
        this.customList = JSON.parse(JSON.stringify(this.customListOrigin))
        // 如果存在多条数据，初始化时，默认选中第一条数据
        if (this.customList.length > 0) {
          this.editorList(0, this.customList[0])
        }
      } else {
        this.restoreQueryCondition = JSON.parse(JSON.stringify(this.layoutTemp.queryCondition[code]))
      }
      this.conditionDialogVisible = true
    },
    /**
     * 封装通用方法：判断是对象还是数组，然后分别处理各自对应的操作（兼容旧数据）
     * @param code 报表code
     * @param condition  this.layoutTemp.queryCondition[code]：可能是对象、也可能是数组
     * @param type 区分处理哪个旧数据，即：处理单个对象的方法有好多种，都需要在该通用方法中调用：如：sortName：清空排序项；collectSubDept：部门归集，等等。
     * */
    processQueryCondition(code, condition, type) {
      if (Array.isArray(condition)) {
        // 如果是数组，则遍历数组并对每个对象调用 processSingleObjectSortName
        condition.forEach((obj, index) => {
          this.processQueryConditionCommon(code, obj, type);
          // 更新原数组中的对象
          condition[index] = obj;
        });
      } else if (typeof condition === 'object' && condition !== null) {
        // 如果是单个对象，则直接处理
        this.processQueryConditionCommon(code, condition, type);
      }
    },
    /**
     * 封装通用方法，通过type区分在方法processQueryCondition中需要调用哪个方法，参数说明和processQueryCondition方法在中一致
     * */
    processQueryConditionCommon(code, paramObj, type) {
      switch (type) {
        case 'sortName':
          this.processSingleObjectSortName(code, paramObj);
          break;
        case 'currentIsTrwfeSignReport':
          this.processSingleObjectIsTrwfeSignReport(code, paramObj);
          break;
        case 'applicationDuration':
          this.processSingleObjectApplicationDuration(code, paramObj);
          break;
        case 'dataChange':
          this.processSingleObjectDataChange(code, paramObj);
          break;
        case 'fullScan':
          this.processSingleObjectFullScan(code, paramObj);
          break;
        default:
          break;
      }
    },
    /**
     * 清空没有权限的排序项：处理单个对象的逻辑（兼容旧数据）
     * @param code 报表code
     * @param obj  this.layoutTemp.queryCondition[code]：一定是对象
     * */
    processSingleObjectSortName(code, obj) {
      if (!this.isCustomReport(code)) {
        if (obj && obj.sortName) {
          const sortName = obj.sortName;
          if (this.sortFields[code]) {
            if (!this.sortFields[code].some(v => v.prop == sortName)) {
              if (this.sortFields[code].length > 0) {
                obj.sortName = this.sortFields[code][0].prop;
              } else {
                obj.sortName = '';
              }
            }
          }
        }
      }
    },
    /**
     * 审批报表或审批征兆报表：处理单个对象的逻辑
     * @param code 报表code
     * @param obj  this.layoutTemp.queryCondition[code]：一定是对象
     * */
    processSingleObjectIsTrwfeSignReport(code, obj) {
      obj.countByObject = 1
    },
    /**
     * 应用程序运行时长报表按操作员统计时排序项只支持应用程序运行时长：处理单个对象的逻辑
     * @param code 报表code
     * @param obj  this.layoutTemp.queryCondition[code]：一定是对象
     * */
    processSingleObjectApplicationDuration(code, obj) {
      if (16 === code) {
        this.onlyTerminal = this.hasPermission('B26') && !this.hasPermission('C27')
        if (this.onlyTerminal) {
          obj.countByObject = 2
        }
        this.handleAppRuntimeReportSortFields(this.onlyTerminal ? 2 : obj.countByObject)
      }
    },
    /**
     * 如果是数据变动报表 且 没有起始时间 默认给它一个七天前的时间：处理单个对象的逻辑
     * @param code 报表code
     * @param obj  this.layoutTemp.queryCondition[code]：一定是对象
     * */
    processSingleObjectDataChange(code, obj) {
      if (61 === code) {
        if (!obj.startDate) {
          const date = new Date(new Date().getTime() - (7 * (1000 * 60 * 60 * 24)));
          this.tempQuery.startDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
        } else {
          this.tempQuery.startDate = obj.startDate
          this.tempQuery.endDate = obj.endDate
        }
      }
    },
    resetGuid(filed1, filed2, code, batchNoOptions) {
      const queryCondition = this.layoutTemp.queryCondition[code]
      if (!queryCondition[filed1]) {
        this.showOption2 = false
        this.$set(queryCondition, filed1, '')
      } else {
        if (this.guidOptions.length > 0 && queryCondition[filed2]) {
          const selectedObj = this.guidOptions.find(item => item.key === queryCondition[filed2]);
          if (selectedObj && selectedObj.id) {
            this.showOption2 = true
            this.getTaskOptionStep2(filed1, batchNoOptions)
          } else {
            this.showOption2 = false
          }
        }
      }
      if (!queryCondition.taskGuid) {
        this.$set(queryCondition, filed2, '')
      }
    },
    /**
     * 如果当前是全盘扫描报表 且 没有 guid ，默认给它一个：处理单个对象的逻辑
     * @param code 报表code
     * @param obj  this.layoutTemp.queryCondition[code]：一定是对象
     * */
    processSingleObjectFullScan(code, obj) {
      // 赋值，防止出现ui和值没有联动
      if (57 === code || 94 === code) {
        this.resetGuid('batchNo', 'taskGuid', code, this.batchNoOptions)
      }
      if (95 === code) {
        const queryCondition = this.layoutTemp.queryCondition[this.currentConditionCode]
        this.resetGuid('batchNo1', 'taskGuid1', code, this.batchNoOptions1)
        // 查询对象进行赋值
        this.checkedKeys1.splice(0)
        this.checkedKeys2.splice(0)
        const { chckedObjectIds, checkedGroupIds, chckedObjectIds1, checkedGroupIds1 } = queryCondition
        chckedObjectIds && this.checkedKeys1.push(...chckedObjectIds.split(','))
        checkedGroupIds && this.checkedKeys1.push(...checkedGroupIds.split(','))
        chckedObjectIds1 && this.checkedKeys2.push(...chckedObjectIds1.split(','))
        checkedGroupIds1 && this.checkedKeys2.push(...checkedGroupIds1.split(','))
      }
      if (57 == code && this.guidOptions.length > 0 && !obj.taskGuid) {
        // obj.taskGuid = this.guidOptions[0].key
      }
    },
    /**
     * 兼容旧数据，获取报表名称
     * @param echartsOptionDTOsList 修改弹框打开时，获取到的之前保存的echarts列表
     * */
    getReportName(echartsOptionDTOsList, code) {
      let name = ''
      if (echartsOptionDTOsList.length > 0) {
        echartsOptionDTOsList.forEach(item => {
          if (item.code === code) {
            name = item.option.title.text
          }
        })
      }
      return name
    },
    /**
     * 关闭--查询弹框关闭
     * */
    discardQueryConditionChange() {
      const code = this.currentConditionCode
      // 可自定义多项的报表：如果点击关闭按钮，将保存并继续的数据复制给参数
      const queryCondition = this.nonsupportReport ? this.customList : this.restoreQueryCondition
      this.layoutTemp.queryCondition[code] = queryCondition
      // 可自定义多项的报表：当查询条件没有设置数据项就关闭时，同时取消报表选中，防止没有设置数据项最后还能保存成功
      if (this.nonsupportReport && queryCondition.length < 1) {
        const index = this.layoutTemp.reportNum.indexOf(code)
        this.cancelChart(this.layoutTemp, index, code)
      }
      // 全盘扫描文件报表，没有设置扫描任务就关闭时，取消选中
      if (57 == code && !queryCondition.taskGuid) {
        const index = this.layoutTemp.reportNum.indexOf(code)
        this.cancelChart(this.layoutTemp, index, code)
      }

      this.conditionDialogVisible = false
      this.singleSetting = false
      this.submitting = false
    },
    queryConditionClosed() {
      if (this.singleSetting) {
        this.nonsupportReport ? this.discardQueryConditionChange() : null
        this.dialogStatus = 'update'
        this.saveData()
      }
    },
    saveQueryConditionChange() {
      this.$refs['conditionForm'].validate((valid) => {
        if (valid) {
          const code = this.currentConditionCode
          // 数据变化报表先不处理，如果后面要改成可自定义多项，再改
          if (61 === code) {
            // 只有数据变化报表有这个条件
            this.layoutTemp.queryCondition[code].startDate = this.tempQuery.startDate
            this.layoutTemp.queryCondition[code].endDate = this.tempQuery.endDate
          }
          this.conditionDialogVisible = false
          this.queryConditionClosed()
        } else {
          return false
        }
      })
    },
    resetTemp() { // 重置数据
      const layout = { height: 400, col: this.layoutTypes[0], colType: 0 }
      const panel = []
      this.rows.forEach(item => {
        panel.push(JSON.parse(JSON.stringify(layout)))
      })
      this.layoutTemp = JSON.parse(JSON.stringify(this.defaultLayout))
      this.layoutTemp.panel = panel
      this.layoutTemp.userId = this.userId
    },
    // 初始化面板数据
    initLayout() {
      this.loading = true
      this.showCharts = []
      // 获取全部面板数据
      getReportPanelByUserId(this.userId).then(res => {
        if (res.data.length > 0) {
          this.loading = false
          // 数据库中有面板信息
          const data = JSON.parse(JSON.stringify(res.data))
          // 需要请求数据的报表code
          const chartCodes = []
          // 遍历面板信息，获取已添加的所有报表的 code
          data.forEach((d, i) => {
            // json 字符串转成对象
            d.panel = JSON.parse(d.panel)
            // 字符串转成数组
            d.reportNum = d.reportNum ? d.reportNum.split(',').map(num => +num) : []
            // 当前面板的报表添加到 chartCodes
            chartCodes.push(...d.reportNum)
            if (d.isStay) {
              // 切换到上次停留的面板
              this.curIndex = i
            }
            // 时间轴和流量轴进行单位转换
            d.echartsOptionDTOs.forEach(option => {
              this.processingOption(option)
            })
          })
          this.showCharts.push(...data)
          this.showCharts.forEach(v => {
            // 处理增加的默认模板多语言有问题的情况
            if (v.name == 'pages.defaultPanel') {
              v.name = this.$t('pages.defaultPanel')
            }
          })
          // todo
          this.asyncLoadTermUserNum()
          // 通过 codes 获取报表数据
          // const codes = [...new Set(chartCodes)].toString()
          // this.getChartDataMap(codes).then(res => {
          //   this.appendChartData()
          // })
        } else {
          // 数据库中没有面板信息，设置默认面板信息
          this.layoutTemp.name = this.$t('pages.defaultPanel')
          const roles = this.$store.getters.roles
          const userMenuCodes = this.$store.getters.userMenuCodes
          // 数据库中没有面板信息，设置默认面板信息
          //  根据角色添加默认图表，若角色为系统管理员时，首次登录添加35-终端操作系统报表，36-终端版本报表到默认版本中
          //  角色为审计管理员时，添加34-终端上线统计趋势图，1-终端接入统计趋势图，3-计算机日志统计报表 到默认模板中
          //  角色为安全管理员时，添加2-策略数量统计报表
          //  若角色不是三员管理员查看是否拥有1，3，2，34，35，36图表所对应的策略权限，存在即添加，不存在不添加

          if (roles.includes(2)) {
            this.layoutTemp.reportNum.push(35, 36);
          }
          if (roles.includes(3)) {
            this.layoutTemp.reportNum.push(2)
          }
          if (roles.includes(4)) {
            this.layoutTemp.reportNum.push(1, 3, 34);
          }
          if (!roles.includes(2) && !roles.includes(3) && !roles.includes(4)) {
            if (userMenuCodes.includes('B22')) {
              this.layoutTemp.reportNum.push(1)
            }
            if (userMenuCodes.includes('B11')) {
              this.layoutTemp.reportNum.push(2)
            }
            if (userMenuCodes.includes('B26')) {
              this.layoutTemp.reportNum.push(3)
            }
            //  B2O 不是 B 2 零，是B 2 o(大写）
            if (userMenuCodes.includes('B2O')) {
              this.layoutTemp.reportNum.push(34)
            }
            if (userMenuCodes.includes('B24')) {
              this.layoutTemp.reportNum.push(35, 36)
            }
          }
          this.layoutTemp.panel[0] = { height: 400, col: this.layoutTypes[5], colType: 5 }
          // todo
          this.asyncLoadTermUserNum()
          // 请求报表数据
          if (this.layoutTemp.reportNum.length > 0) {
            const codes = this.layoutTemp.reportNum.join(',')
            this.getChartDataMap(codes).then(res => {
              this.loading = false
              this.layoutTemp.reportNum.forEach((item, index) => {
                this.$set(this.layoutTemp.echartsOptionDTOs, index, this.chartsDataMap[item])
              })
              this.appendChartData()
              // 保存面板信息
              this.createData(null, true)
            })
          } else {
            this.loading = false
            this.appendChartData()
            // 没有报表，直接保存空白面板信息（空白面板：没有报表的面板）
            this.createData(null, true)
          }
        }
        // 如果是饼图,可能存在鼠标悬停时，数据名称不是报表名称的情况，所以需要添加具体名称属性
        if (this.showCharts.length > 0) {
          this.showCharts.forEach(item => {
            if (item.echartsOptionDTOs.length > 0) {
              item.echartsOptionDTOs.forEach(ech => {
                if (ech.option && ech.type === 'pie') {
                  const label = this.sortFields[ech.code]
                  if (label !== undefined) {
                    ech.option.title.description = this.$t('table.' + label[0].label)
                  }
                }
              })
            }
          })
        }
        // 获取报表列表
        this.getReportList()
      }).catch(e => {
        this.loading = false
        console.error(e)
      })
    },
    // 加载 终端数量统计、终端未上线数量统计、操作员数量统计
    asyncLoadTermUserNum() {
      this.$nextTick(() => {
        if (this.$refs['dataPanel0']) {
          // 终端数量统计、终端未上线数量统计、操作员数量统计
          this.$refs['dataPanel0'][0].getData(this.termUserCountData)
        }
      });
    },
    // 添加报表数据到面板中
    appendChartData() {
      // 其它报表统计数据获取
      this.showCharts.forEach((d, i) => {
        d.echartsOptionDTOs.forEach((el, index) => {
          const chartData = this.chartsDataMap[el.code]
          this.processingOption(chartData)
          el.type = chartData.type
          el.option = chartData.option || {}
        })
      })
    },

    // 时间轴、流量轴 配置修改
    processingOption(data) {
      if ('TIME' === data.yaxisType) {
        timeAxis(data.option)
      }

      if ('FLOW' === data.yaxisType) {
        flowAxis(data.option)
      }

      // 自定义报表中关于时长、流量的统计项进行单位转换
      if (this.customCodes.includes(data.code)) {
        this.processCustomReportOption(data.option)
      }
    },
    /**
     * 通过 code 获取报表数据，并存到 chartsDataMap
     * codes [Array, String]
     */
    getChartDataMap(codes) {
      // codes 转成字符串
      const codeStr = Array.isArray(codes) ? codes.join(',') : codes
      return new Promise((resolve, reject) => {
        if (!codeStr) {
          resolve(this.chartsDataMap)
          return
        }
        // 获取报表数据
        getEchartDataByCodes({ codeStr }).then(res => {
          // 合并到 chartsDataMap
          Object.assign(this.chartsDataMap, res.data)
          resolve(this.chartsDataMap)
        }).catch(e => {
          console.error(e);
          reject(e)
        })
      })
    },
    // 格式化要提交的数据
    formatterData(data) {
      const tempData = JSON.parse(JSON.stringify(data))
      tempData.panel = JSON.stringify(data.panel)
      tempData.reportNum = data.reportNum.toString()
      if (typeof tempData.queryCondition != 'string') {
        tempData.queryCondition = JSON.stringify(data.queryCondition)
      }
      delete tempData.echartsOptionDTOs
      delete tempData.isStay
      return tempData
    },
    // 面板切换
    panelChange(current, old) {
      this.curIndex = current
      this.layoutTemp = JSON.parse(JSON.stringify(this.showCharts[this.curIndex]))
      this.saveIndex = true
      this.savePanelIndex()
      // 数据重置
      this.currentConditionCode = undefined
      this.currentCharType = undefined
    },
    // 快速布局
    quickSetting(type) {
      const col = this.layoutTypes[type]
      const layout = this.layoutTemp.panel
      layout.forEach((item, index) => {
        item.col = col
        item.colType = type
      })
    },
    // 设置行布局
    setting(index, type) {
      const col = this.layoutTypes[type]
      const layout = this.layoutTemp.panel[index]
      layout.col = col
      layout.colType = type
      this.triggerResize()
    },
    // 设置行高
    heightChange(index, height) {
      const layout = this.layoutTemp.panel[index]
      layout.height = height
      this.triggerResize()
    },
    // 获取报表列表, 存到 chartsList，并生成 chartMenuCodeMap
    getReportList() {
      // 如果报表列表非空，则直接返回
      if (this.chartsList && this.chartsList.length > 0) {
        if (this.loadGuidOption) {
          this.getTaskOptionStep1()
        }
        return new Promise(resolve => {
          resolve(this.chartsList)
        })
      }
      // 否则，根据管理员id获取拥有权限的报表列表信息
      return getReportListByUserId(this.userId).then(res => {
        this.chartsList = res.data

        // 插入征兆报表排序项
        for (const i of this.chartsList) {
          if (i.code >= 10000 && i.code <= 20000) {
            this.sortFields[i.code] = [
              { prop: 'normal', label: 'general' },
              { prop: 'importance', label: 'important' },
              { prop: 'seriousness', label: 'serious' },
              { prop: 'total', label: 'total' }
            ]
          }
        }

        // 可选择的报表分类的 Map
        this.chartMenuCodeMap = this.chartsList.reduce((result, chart) => {
          const { menuCode, code } = chart
          result[menuCode] = true
          // 如果报表列表中有 57, 94, 95 ，则说明需要加载 guidOptions
          if ([57, 94, 95].includes(code)) {
            this.loadGuidOption = true
          }
          return result
        }, {})
        if (this.loadGuidOption) {
          this.getTaskOptionStep1()
        }
      }).catch(e => {
        console.log(e)
      })
    },
    // 操作新增面板
    handleCreate() {
      // 面板数量最多5个
      if (this.showCharts.length >= 5) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.panelMessage'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.getReportList().then((res) => {
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
        this.isRequire = ''
        this.reportRequire = ''
        this.showBase = true
        this.resetTemp()
      })
    },
    // 操作修改面板
    handleUpdate(show) {
      return this.getReportList().then((res) => {
        this.dialogStatus = 'update'
        this.dialogFormVisible = show === false ? show : true
        this.isRequire = ''
        this.reportRequire = ''
        this.showBase = true
        this.layoutTemp = JSON.parse(JSON.stringify(this.showCharts[this.curIndex]))
        const codes = this.layoutTemp.reportNum
        // 获取图表类型
        const codeChartTypeMap = this.layoutTemp.echartsOptionDTOs.reduce((map, item) => {
          map.set(item.code, item.type)
          return map
        }, new Map())
        // 兼容先前的数据没有设置查询条件的情况
        const queryCondition = this.layoutTemp.queryCondition ? JSON.parse(this.layoutTemp.queryCondition) : {}
        this.$set(this.layoutTemp, 'queryCondition', queryCondition)

        // 清空没有权限的排序项
        for (const code of codes) {
          const condition = queryCondition[code];
          if (condition) {
            this.processQueryCondition(code, condition, 'sortName');
          }
        }
        for (const code of codes) {
          if (!queryCondition[code]) {
            this.appendDefaultQueryCondition(code, codeChartTypeMap.get(code))
          } else {
            // 支持归集子部门功能的报表兼容旧版本没有归集子部门功能的情况
            if (this.supportCollectSubDept(code, codeChartTypeMap.get(code)) && queryCondition[code].collectSubDept === undefined) {
              this.$set(queryCondition[code], 'collectSubDept', 0)
            }
          }
        }
      })
    },
    /**
     * 保存时，获取this.layoutTemp.queryCondition中code下对对象数组的数据，将所有对象数组下的对象重新组装成一个新的对象数组
     * */
    mergeObjectArrays(data) {
      const mergedArray = [];
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          const value = data[key];
          // 检查是否为数组并且数组中的元素是对象
          if (Array.isArray(value) && value.every(item => typeof item === 'object' && item !== null)) {
            mergedArray.push(...value);
          }
        }
      }
      return mergedArray;
    },
    /**
     * 判断对象数组中，是否存在重复报表名称（自定义报表和自定义趋势报表名称重复判断）
     * */
    customNameRepeat() {
      const mergedArray = this.mergeObjectArrays(this.layoutTemp.queryCondition);
      // 构建 reportName 到其出现次数的映射
      const nameCount = mergedArray.reduce((acc, item) => {
        acc[item.reportName] = (acc[item.reportName] || 0) + 1;
        return acc;
      }, {});
      // 筛选重复的 name
      return Object.keys(nameCount).filter(name => nameCount[name] > 1);
    },
    // 保存面板，根据 dialogStatus 选择新增或修改
    saveData() {
      const repeatList = this.customNameRepeat()
      this.submitting = true
      if (repeatList.length > 0) {
        this.$message({
          title: this.$t('text.prompt'),
          message: '存在重复报表名称：“' + repeatList + '”',
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return true
      }
      this.dialogStatus == 'create' ? this.createData('click') : this.updateData('click')
    },
    dialogClosed() {
      // 保存数据后，待弹窗关闭才修改 submitting，避免多次点击造成多次保存数据
      this.submitting = false
    },
    // 新增面板，保存数据   isNotRequireReport：报表是否非必填，true：非必填，false：必填
    async createData(option, isNotRequireReport) {
      if (!this.validateData(isNotRequireReport)) {
        this.showBase = true
        this.submitting = false
        return
      }
      const data = this.formatterData(this.layoutTemp)
      const resp = await checkReportPanelNameExists({
        id: null,
        name: data.name
      })
      if (resp.data) {
        this.$message({
          message: this.$t('pages.panelNameDuplicate'),
          type: 'error'
        })
        this.showBase = true
        this.submitting = false
        return
      }
      addPanel(data).then(res => {
        this.layoutTemp.id = res.data.id
        this.showCharts.push(JSON.parse(JSON.stringify(this.layoutTemp)))
        this.dialogFormVisible = false
        this.$nextTick(() => {
          // this.$refs.panels.setActiveItem(this.showCharts.length - 1)
          // this.triggerResize()
          // 首次登录系统自动增加面板时，不需要提示信息，其余情况均需要
          this.showCharts.length > 1 && this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.success_create'),
            type: 'success',
            duration: 2000
          })
          if (option == 'click') {
            this.initLayout()
          }
        })
      }).catch(e => {
        this.submitting = false
        console.error(e);
      })
    },
    // 修改面板，保存数据
    async updateData(option) {
      if (!this.validateData()) {
        this.showBase = true
        this.submitting = false
        return
      }
      const data = this.formatterData(this.layoutTemp);
      const resp = await checkReportPanelNameExists({
        id: data.id,
        name: data.name
      })
      if (resp.data) {
        this.$message({
          message: this.$t('pages.panelNameDuplicate'),
          type: 'error'
        })
        this.showBase = true
        this.submitting = false
        return
      }
      updatePanel(data).then(res => {
        // 切换面板时，只修改面板停留的位置。不改变窗口状态且不弹出修改成功的提示。
        if (!this.saveIndex) {
          this.dialogFormVisible = false
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.success_update'),
            type: 'success',
            duration: 2000
          })
        }
        this.$nextTick(() => {
          this.submitting = false
          this.saveIndex = false
          this.showCharts[this.curIndex] = JSON.parse(JSON.stringify(this.layoutTemp))
          this.triggerResize()
          if (option == 'click') {
            this.initLayout()
          }
        })
      }).catch(e => {
        this.submitting = false
        console.error(e);
      })
    },
    // 删除面板
    deletePanel() {
      // 是否只剩一个面板
      const onlyOnePanel = this.showCharts.length == 1
      if (onlyOnePanel) {
        // 只剩一个面板时，不允许删除
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.noAllowDelete'),
          type: 'error',
          duration: 2000
        })
        return
      }
      // 当前面板的 name，id
      const { name, id } = this.showCharts[this.curIndex]
      this.$confirmBox(this.$t('pages.confirmDelete', { panel: name }), this.$t('text.prompt')).then(() => {
        deletePanel(id).then(res => {
          // 当前面板是最后一个面板时，显示前一个面板
          const index = this.curIndex == this.showCharts.length - 1 ? this.curIndex - 1 : this.curIndex
          // 删除面板
          this.showCharts.splice(this.curIndex, 1)
          this.$nextTick(() => {
            // 设置新的 index
            this.$refs.panels.setActiveItem(index)
            this.triggerResize()
          })
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.deletedPanel') + name,
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
      })
    },
    // 验证面板名称是否有值，返回 true
    validateName(e) {
      this.isRequire = this.layoutTemp.name ? '' : this.$t('pages.enterName')
      return !this.isRequire
    },
    // 验证面板数据是否填写，返回 true     isRequireReport: 报表是否非必填，如果为true不校验是否添加报表
    validateData(isNotRequireReport) {
      this.reportRequire = this.layoutTemp.reportNum.length > 0 ? '' : this.$t('pages.selectReport')
      // 报表名称是否填写，选择的报表数量大于 0
      const validate = this.validateName() && (!!isNotRequireReport || !this.reportRequire)
      // console.log('validate', validate, this.validateName(), this.reportRequire)
      return validate
    },
    tabClick(tab) {
      // 切换tab页时，滚动条滚动到顶部
      document.getElementsByClassName('chart-lists')[0].scrollTo(0, 0)
    },
    // 显示搜索图表
    showChartForSearch(chart) {
      const isShowType = this.activeTab === 'ALL' || this.activeTab === chart.menuCode
      const isSearch = chart.name.toLowerCase().indexOf(this.searchText.trim().toLowerCase()) > -1
      return isShowType && isSearch
    },
    // 添加报表查询默认查询条件
    appendDefaultQueryCondition(code, chartType) {
      let sortName = ''
      if (sortFields[code] && sortFields[code].length > 0) {
        sortName = sortFields[code][0].prop
      }
      this.$set(this.layoutTemp.queryCondition, code, {
        dimBaseType: 1, // 当月
        countByObject: 2, // 按终端统计
        groupType: 1, // 按部门分组
        recordSize: 10, // 统计条数
        sortName,
        countDim: [51, 52].includes(code) ? 'lossType' : ''
      })

      // 给自定义报表和自定义趋势图的数据项设置初始值，防止数据项下拉框还未选择就会显示提示信息
      if (this.customCodes.includes(code)) {
        this.$set(this.layoutTemp.queryCondition[code], 'customDataItems', [])
      }
      // 给支持归集子部门功能报表的是否归集子部门选项设置初始值
      if (this.supportCollectSubDept(code, chartType)) {
        this.$set(this.layoutTemp.queryCondition[code], 'collectSubDept', 0)
      }
    },
    // 报表取消选中
    cancelChart(layout, index, code) {
      layout.reportNum.splice(index, 1)
      for (let i = layout.echartsOptionDTOs.length - 1; i >= 0; i--) {
        if (layout.echartsOptionDTOs[i].code === code) {
          layout.echartsOptionDTOs.splice(i, 1); // 删除匹配的元素
        }
      }
      // 取消选中时，直接删除对应code
      delete this.layoutTemp.queryCondition[code]
    },
    // 添加报表
    appendChart(option, removeable = true) {
      const code = option.code
      // 当前编辑的 layout
      const layout = this.layoutTemp
      // 当前点击的报表在 layout 里的 index
      const index = layout.reportNum.indexOf(code)
      // 如果点击的报表已存在，则取消选中
      if (index > -1) {
        // 删除对应的报表数据
        removeable && this.cancelChart(layout, index, code)
        return
      }
      // 勾选了报表，清除提示信息
      this.reportRequire = ''
      // 已添加的报表数量小于10，则添加（多张自定义报表，添加修改）
      const isExist = this.checkElements('selected')
      if (isExist < 10) {
        layout.reportNum.push(code)
        this.appendDefaultQueryCondition(code, option.type)
      } else {
        // 报表数量超过10不允许添加
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.panelLimit'),
          type: 'error',
          duration: 2000
        })
        return true
      }
      // 当选中可自定义多项的报表时，默认打开查询条件框，让用户选择数据项
      // 全盘扫描文件报表，也自动打开
      // 征兆报表10000-20000之间
      const standardSignLowerBound = 10000;
      const standardSignUpperBound = 20000;
      if (!(this.notSupportedCustomCodes.includes(code) || (standardSignLowerBound <= code && code <= standardSignUpperBound)) || code == 57) {
        this.handleCustomConditionClick(option)
      }
    },
    // 修改报表配置
    settingChart(option) {
      this.singleSetting = true
      const chart = this.chartsList.find(item => item.code === option.code)
      // 通过调用 handleUpdate 但不打开窗口，获取报表列表及执行相关代码
      this.handleUpdate(false).then(() => {
        // 然后打开报表的配置窗口
        this.handleCustomConditionClick(chart)
      })
    },
    // 删除报表
    deleteChart(option) {
      const code = option.code
      this.layoutTemp = this.showCharts[this.curIndex]
      const panel = this.layoutTemp
      if (panel.echartsOptionDTOs.length == 1) {
        this.$confirmBox(this.$t('pages.cannotDelete'), this.$t('text.prompt'), {
          showCancelButton: false
        })
      } else {
        this.$confirmBox(this.$t('pages.deleteChart'), this.$t('text.prompt')).then(() => {
          const currentDelete = JSON.parse(panel.queryCondition)[code]
          const echartsList = panel.echartsOptionDTOs
          if (Array.isArray(currentDelete)) {
            // 可自定义多项的报表，不能删除用code删除，单独做删除处理
            const title = option.option.title.text
            const queryList = JSON.parse(panel.queryCondition)
            // 根据code和title删除对应索引对象
            echartsList.forEach((item, index) => {
              if (item.code === code && item.option.title.text === title) {
                panel.echartsOptionDTOs.splice(index, 1)
              }
            })
            // 判断是否还存在code
            const hasCode = echartsList.some(item => item.code === code);
            if (!hasCode) {
              const index = panel.reportNum.indexOf(code);
              if (index > -1) {
                // 如果不存在，删除对应code
                panel.reportNum.splice(index, 1);
              }
            }
            // 删除对应的查询条件(如果对象数组不为空，删除数组中的某个对象)
            queryList[code].forEach((item, index) => {
              if (item.reportName === title) {
                queryList[code].splice(index, 1)
              }
            })
            // 如果数组长度为0，将该报表code以及code对应的空数组删掉
            if (queryList[code].length === 0) {
              delete queryList[code]
            }
            panel.queryCondition = JSON.stringify(queryList)
            this.updateData()
          } else {
            // 除自定义报表以外的删除
            const index = panel.reportNum.indexOf(code)
            panel.reportNum.splice(index, 1)
            // 根据code删除对应索引对象
            echartsList.forEach((item, index) => {
              if (item.code === code) {
                panel.echartsOptionDTOs.splice(index, 1)
              }
            })
            // 删除对应的查询条件
            const qjson = JSON.parse(panel.queryCondition)
            delete qjson[code]
            panel.queryCondition = JSON.stringify(qjson)
            this.updateData()
          }
        }).catch(() => {
        })
      }
    },
    /**
     * 判断是否为自定义报表
     * @param code
     * @returns {*}
     */
    isCustomReport(code = this.currentConditionCode) {
      return this.customReportCode === code
    },
    /**
     * 判断是否支持归集子部门功能
     * @param code 报表code
     * @param chartType 图表类型
     * @returns {boolean}
     */
    supportCollectSubDept(code = this.currentConditionCode, chartType = this.currentCharType) {
      const queryCondition = this.layoutTemp.queryCondition && this.layoutTemp.queryCondition[code]

      if (!code || !chartType || !queryCondition || Array.isArray(queryCondition)) {
        return false;
      }
      // 敏感关键字报表按关键字统计时，不支持归集子部门功能
      if (code === 47 && queryCondition.countByObject === 3) {
        return false
      }
      // 趋势图、DLP报表（6张）、敏感内容管控类报表（7张）、风险报表、数据变化报表，不支持归集子部门功能
      return chartType !== 'line' && !this.notSupportedCollectSubDeptCodes.includes(code)
    },
    // 自定义报表的排序项根据选择的数据项动态赋值
    handleCustomDataItemSelect(selectedItem) {
      if (this.isCustomReport()) {
        const queryCondition = this.layoutTemp.queryCondition[this.currentConditionCode]
        this.customSortItems = []
        queryCondition.sortName = ''
        if (selectedItem.length > 0) {
          this.customSortItems = this.customDataItems.filter(item => selectedItem.includes(item.prop))
          queryCondition.sortName = this.customSortItems[0].prop

          // 数据项包含审批数时，统计类型只能为“按操作员统计”
          if (selectedItem.includes('trwfeSumAll')) {
            queryCondition.countByObject = 1
            this.onlyOperator = true
          } else {
            this.onlyOperator = false
          }

          // 数据项包含资产变更数时，统计类型只能为“按终端统计”
          if (selectedItem.includes('hardwareAssetChangeTimes') || selectedItem.includes('softwareAssetChangeTimes')) {
            queryCondition.countByObject = 2
            this.onlyTerminal = true
          } else {
            this.onlyTerminal = false
          }
        }
      }
    },
    /**
     * 自定义报表数据项禁用
     * @param value
     * @returns {boolean}
     */
    isDisabled(value) {
      if (this.isCustomReport()) {
        const queryCondition = this.layoutTemp.queryCondition[this.currentConditionCode]
        return queryCondition.customDataItems.some(option => this.mutexOptions[option] && this.mutexOptions[option].includes(value))
      }
      return false
    },
    /**
     * 获取应用程序运行时长报表的排序项
     * @param countType 统计类型
     */
    handleAppRuntimeReportSortFields(countType) {
      const code = this.currentConditionCode
      if (1 === countType) {
        this.sortFields[code] = [
          { prop: 'runTimeSumAll', label: 'runTimeSumAll' }
        ]
      } else {
        this.sortFields[code] = [
          { prop: 'runTimeSumAll', label: 'runTimeSumAll' },
          { prop: 'totalUptime', label: 'totalUptime' },
          { prop: 'activeUptime', label: 'activeUptime' }
        ]
      }
      this.sortFields[code] = filterAppRuntimeColumn(this.sortFields[code])
    },
    /**
     * 自定义报表中关于时长、流量的统计项进行单位转换
     * @param option
     */
    processCustomReportOption(option) {
      option.tooltip = {
        formatter: (d) => {
          let res = this.html2Escape(d[0].axisValueLabel) + '<br>'
          for (let i = 0; i < d.length; i++) {
            let value = d[i].data
            const colName = d[i].seriesName
            const colModel = this.customDataItems.find(item => this.$t('table.' + item.label) === colName)
            if (colModel) {
              value = this.formatValue(value, colModel)
            }
            res += colName + ' : ' + value + '<br>'
          }
          return res
        }
      }
    },
    formatValue(value, colModel) {
      if (colModel.flag === 'size') {
        return convert(value)
      }

      if (colModel.flag === 'time') {
        return formatSeconds(value)
      }

      return value
    }
  }
}
</script>

<style lang='scss' scoped>
  .custom-container{
    height: calc(100vh - 90px);
    position: relative;
  }
  .setting-btn{
    position: absolute;
    border: 0;
    padding: 5px;
    top: 15px;
    right: 10px;
    font-size: 20px;
    z-index: 111;
  }
  >>>.el-carousel{
    height: 100%;
    .el-carousel__item{
      overflow: auto;
      padding-bottom: 20px;
    }
    .el-carousel__arrow i{
      font-size: 24px;
    }
  }
  >>>.el-carousel__indicators--labels .el-carousel__button{
    padding: 2px 10px;
  }
  ul{
    padding: 0;
    list-style: none;
  }
  .label{
    display: inline-block;
    width: 80px;
    text-align: right;
  }
  .chart-panel{
    margin: 5px;
    height: 120px;
    border: 1px solid #ccc;
    position: relative;
    cursor: pointer;
    p{
      width: 100%;
      margin-top: 5px;
      padding: 0 10px;
      text-align: center;
      position: absolute;
    }
    .chart-icon{
      font-size: 45px;
      margin: 0 auto;
      position: absolute;
      bottom: 10px;
      left: 50%;
      margin-left: -25px;
    }
    .selected-icon{
      position: absolute;
      right: 1px;
      bottom: 1px;
    }
  }
  .custom-layout{
    position: relative;
    li{
      line-height: 30px;
    }
  }
  .row-name {
    width: 50px;
    display: inline-block;
  }
  .layout-setting{
    position: relative;
    display: inline-block;
    margin: 10px 0px -10px 40px;
    height: 30px;
    line-height: 30px;
    li{
      float: left;
      width: 20px;
      font-size: 16px;
      margin-right: 10px;
      cursor: pointer;
    }
    .active{
      color: #2574b1;
    }
  }
  .el-tabs {
    border: none;
  }
  >>>.el-tabs--left .el-tabs__header.is-left {
    height: 365px;
  }
  .search-input {
    width: 200px;
    float: right;
  }
  .chart-lists{
    height: 320px;
    margin: 40px 0 0;
    overflow: auto;
    li{
      width: 50%;
      display: inline-block;
    }
  }
  .layout{
    line-height: 30px;
    margin-top: 0;
  }
  .custom-btn{
    margin-top: 12px;
    float: right;
  }
  .height-setting {
    height: 30px;
    margin-right: 50px;
    margin-top: 8px;
    float: right;
  }
  .height-select{
    width: 80px;
    vertical-align: top;
    >>>.el-input .el-input__inner{
      min-height: 28px;
      height: 28px;
    }
  }
  >>>.el-pagination.is-background {
    .btn-next, .btn-prev, .el-pager li{
      background: transparent;
      color: #666;
    }
  }
  >>>.el-tabs__item.is-left {
    padding: 0 10px;
    text-align: center;
  }
  >>>.panel-setting{
    min-width: 100px;
    padding: 5px;
    text-align: center;
    ul{
      padding: 0;
      margin: 0;
      li{
        padding: 5px;
        cursor: pointer;
        &:hover{
          background: #e5e5e5;
        }
      }
    }
  }
  >>>.el-select__tags{
    overflow-x: hidden;
  }
  .hidden-options{
    display: none;
  }
  /*自定义报表弹框样式*/
  .itemClass{
    color: #2674b2;
  }
  .left-list{
    width: 210px !important;
    height: 435px;
    border: 1px solid #aaaaaa;
    overflow: hidden;
    .left-list-title{
      display: inline-block;
      width: 100%;
      height: 35px;
      line-height: 35px;
      padding-left: 10px;
      border-bottom: 1px solid #aaaaaa;
      color: #2674b2;
      font-weight: bold;
      svg{
        float: right;
        margin-right: 13px;
        height: 35px;
        line-height: 35px;
        cursor: pointer;
      }
    }
    .left-item{
      overflow: auto;
      height: 90%;
      div{
        height: 30px;
        line-height: 30px;
        cursor: pointer;
        .left-item-title{
          display: inline-block;
          width: 80%;
          padding-left: 10px;
          white-space: nowrap;      /* 防止文本换行 */
          overflow: hidden;         /* 隐藏溢出的内容 */
          text-overflow: ellipsis;  /* 显示省略号 (...) */
        }
        svg{
          height: 30px;
          line-height: 30px;
          margin: 0 10px 0 10px;
        }
      }
    }
  }
</style>
