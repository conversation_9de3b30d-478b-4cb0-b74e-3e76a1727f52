<!--
  /**
  * 首页图标实现loading页面并异步加载数据
  * 参考自@/components/ECharts，重写组件，以免影响其他功能
  */
-->
<template>
  <div class="echarts-container">
    <el-row :gutter="32">
      <el-col v-for="(chart, index) in chartsOption" :key="index" :xs="24" :sm="24" :lg="chart.col || 8">
        <div class="chart-wrapper">
          <pie-chart v-if="chart.type === 'pie'" ref="pie" v-loading="chart.loading || false" :width="width || '100%'" :height="height || '450px'" :chart-data="chartsDatas[index]" :chart-option="chart.option || {}" :click="chart.click"/>
          <bar-chart v-if="chart.type === 'bar'" ref="bar" v-loading="chart.loading || false" :width="width || '100%'" :height="height || '350px'" :chart-data="chartsDatas[index]" :chart-option="chart.option || {}" :click="chart.click" :x-axis-name="xAxisName"/>
          <line-chart v-if="chart.type === 'line'" ref="line" v-loading="chart.loading || false" :width="width || '100%'" :height="height || '350px'" :chart-data="chartsDatas[index]" :chart-option="chart.option || {}" :click="chart.click" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import PieChart from '@/components/ECharts/PieChart'
import BarChart from '@/components/ECharts/BarChart'
import { getEchartDataByCode } from '@/api/dashboard'

export default {
  name: 'ECharts',
  components: {
    LineChart,
    PieChart,
    BarChart
  },
  props: {
    chartsOption: {
      type: Array,
      default() {
        return []
      }
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    xAxisName: {
      type: String,
      default() {
        return this.$t('components.terminal')
      }
    }
  },
  data() {
    return {
      // chartsDatas: []
    }
  },
  computed: {
    chartsDatas() {
      return this.chartsOption.map((el, index) => {
        if (el.getDataApi) {
          el.getDataApi(el.para).then(res => {
            return res
          })
        } else {
          return el.data || []
        }
      })
    }
  },
  mounted() {
    this.getOptionByCode();
  },
  methods: {
    reloadData(index) {
      const chartOpt = this.chartsOption[index]
      chartOpt.getDataApi(chartOpt.para).then(res => {
        this.chartsDatas.splice(index, 1, res)
      })
    },
    getOptionByCode() {
      this.chartsOption.map((el, index) => {
        // loading页面
        this.chartsOption[index].loading = true;
        if (el.code) {
          getEchartDataByCode(el.code).then(res => {
            this.chartsOption[index].option = res.data.option;
            this.chartsOption[index].loading = false;
          });
        } else {
          this.chartsOption[index].loading = false;
        }
      });
    }
  }
}
</script>

<style lang="scss">
  .echarts-container .el-col{
    padding: 10px 16px;
  }
</style>
