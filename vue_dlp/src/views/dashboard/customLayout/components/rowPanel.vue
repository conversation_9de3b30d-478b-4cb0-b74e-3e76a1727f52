<template>
  <div style="padding: 20px 35px;">
    <el-button v-if="showCharts.length === 0" class="add-panel" @click="setLayout"><i class="el-icon-plus"></i></el-button>
    <el-row v-for="(row, i) in layouts" :key="i" :gutter="10">
      <el-col v-for="(col, j) in row.col" :key="j" :xs="24" :sm="24" :lg="col.span || 8" style="margin-bottom: 10px;">
        <div v-show="col.option" class="panel-wrapper" :style="{'height': row.height + 'px'}">
          <div class="chart-title">
            <svg-icon v-if="!nonsupportCodes.has(col.option.code)" icon-class="setting" class="title-icon setting-icon" @click="settingChart(col.option)" />
            <svg-icon icon-class="delete" class="title-icon delete-icon" @click="deleteChart(col.option)" />
          </div>
          <e-charts :height="row.height - 36 + 'px'" :charts-option="[col.option]" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ECharts from '@/components/ECharts'

export default {
  name: 'RowPanel',
  components: { ECharts },
  props: {
    showCharts: {
      type: Array,
      default() {
        return []
      }
    },
    panelIndex: {
      type: Number,
      default: 0
    },
    layout: {
      type: Array,
      default() {
        return []
      }
    },
    nonsupportCodes: {
      type: Set,
      default() {
        return new Set()
      }
    }
  },
  data() {
    return {
      layouts: []
    }
  },
  computed: {
  },
  watch: {
    layout: {
      deep: true,
      handler(val) {
        this.initlayouts()
      }
    },
    showCharts: {
      deep: true,
      handler(val) {
        this.initlayouts()
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.initlayouts()
    })
  },
  methods: {
    setLayout() {
      this.$emit('setLayout')
    },
    initlayouts() {
      const layouts = JSON.parse(JSON.stringify(this.layout))
      const charts = [...this.showCharts]
      const newLayouts = []

      for (let i = 0; i < layouts.length; i++) {
        if (charts.length === 0) {
          break
        }
        newLayouts.push(layouts[i])
        const col = newLayouts[i].col
        // console.log('col', col)
        for (let j = 0; j < col.length; j++) {
          if (charts.length === 0) {
            col.splice(j)
            break
          }
          if (!col[j].option) {
            col[j].option = charts.shift()
          }
        }
      }
      this.layouts.splice(0, this.layouts.length, ...newLayouts)
    },
    settingChart(option) {
      this.$emit('settingChart', option)
    },
    deleteChart(option) {
      this.$emit('deleteChart', option)
    }
  }
}
</script>

<style lang='scss' scoped>
  .panel-wrapper{
    height: 100%;
    border: 1px solid #3b749c;
  }
  .add-panel{
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    margin-left: -30px;
    margin-top: -30px;
    padding: 0;
    font-size: 26px;
  }
  .chart-title {
    height: 16px;
    position: relative;
  }
  .title-icon {
    position: absolute;
    top: 5px;
    display: none;
    z-index: 111;
    cursor: pointer;
  }
  .delete-icon {
    right: 5px;
  }
  .setting-icon {
    top: 1px;
    right: 28px;
    font-size: 22px;
  }
  .panel-wrapper:hover .title-icon{
    display: inline-block;
  }
</style>
