
export default {
  6: [
    { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll' }
  ],
  8: [
    { prop: 'srcFileNumSumAll', label: 'srcFileNumSumAll' }
  ],
  10: [
    { prop: 'deviceInsertTimesSumAll', label: 'deviceInsertTimesSumAll' },
    { prop: 'createFileNumSumAll', label: 'createFileNumSumAll' },
    { prop: 'copyFileNumSumAll', label: 'copyFileNumSumAll' },
    { prop: 'openFileNumSumAll', label: 'openFileNumSumAll' },
    { prop: 'updateFileNumSumAll', label: 'updateFileNumSumAll' }
  ],
  12: [
    { prop: 'logNumSumAll', label: 'logNumSumAll' }
  ],
  14: [
    { prop: 'urlNumSumAll', label: 'urlNumSumAll' }
  ],
  16: [
    { prop: 'runTimeSumAll', label: 'runTimeSumAll', permission: 'C27' },
    { prop: 'totalUptime', label: 'totalUptime', permission: 'B26' },
    { prop: 'activeUptime', label: 'activeUptime', permission: 'B26' }
  ],
  18: [
    { prop: 'mailNumSumAll', label: 'mailNumSumAll' }
  ],
  22: [
    { prop: 'encOrDecSumAll', label: 'encOrDecSumAll', permission: 'E53' },
    { prop: 'diskScanSumAll', label: 'diskScanSumAll', permission: 'E52' },
    { prop: 'outFileSumAll', label: 'outFileSumAll', permission: 'E48' },
    { prop: 'fileBackupSumAll', label: 'fileBackupSumAll', permission: 'E54' },
    { prop: 'encFileSumAll', label: 'encFileSumAll', permission: 'E56' }
  ],
  24: [
    { prop: 'chatRecordNumSumAll', label: 'chatRecordNumSumAll' }
  ],
  26: [
    { prop: 'changeTimesSumAll', label: 'changeTimesSumAll' }
  ],
  28: [
    { prop: 'changeTimesSumAll', label: 'changeTimesSumAll' }
  ],
  30: [
    { prop: 'emailNumSumAll', label: 'emailNumSumAll', permission: 'D64' },
    { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll', permission: 'C54' },
    { prop: 'mobileStorageFileNumSumAll', label: 'mobileStorageFileNumSumAll', permission: 'C42' },
    { prop: 'outFileNumSumAll', label: 'outFileNumSumAll', permission: 'E48' },
    { prop: 'browserUploadFileNumSumAll', label: 'browserUploadFileNumSumAll', permission: 'D29' }
  ],
  32: [
    { prop: 'changeFileLevelSumAll', label: 'changeFileLevelSumAll', value: 256 },
    { prop: 'cancelWatermarkSumAll', label: 'cancelWatermarkSumAll', value: 268435456 },
    { prop: 'offlineSumAll', label: 'offlineSumAll', value: 4096 },
    { prop: 'fileDecryptSumAll', label: 'fileDecryptSumAll', value: 1 },
    { prop: 'outSendSumAll', label: 'outSendSumAll', value: 16 },
    { prop: 'filePrintSumAll', label: 'filePrintSumAll', value: 65536 },
    { prop: 'behaviorControlSumAll', label: 'behaviorControlSumAll', value: 68719476736 },
    { prop: 'sensitiveFileOutSendSumAll', label: 'sensitiveFileOutSendSumAll', value: 4294967296 },
    { prop: 'fileRelieveJurisdictionSumAll', label: 'fileRelieveJurisdictionSumAll', value: 1099511627776 }
  ],
  37: [
    { prop: 'totalTimeSumAll', label: 'totalTimeSumAll' }
  ],
  39: [
    { prop: 'totalFlowNumSumAll', label: 'totalFlow' },
    { prop: 'receiveFlowNumSumAll', label: 'receivingFlow' },
    { prop: 'sendFlowNumSumAll', label: 'sendingFlow' }
  ],
  41: [
    { prop: 'cmdDataNumSumAll', label: 'cmdDataNumSumAll' }
  ],
  43: [
    { prop: 'fileNameNumSumAll', label: 'fileNameNumSumAll' }
  ],
  45: [
    { prop: 'fileNumSumAll', label: 'fileNum' }
  ],
  47: [
    { prop: 'triggerCountSum', label: 'sensitiveKeywordTriggerCount' }
  ],
  49: [
    { prop: 'searchCountSum', label: 'urlSearchSumAll' }
  ],
  58: [
    { prop: 'pasteCountSum', label: 'browserPasteSumAll' }
  ],
  62: [
    { prop: 'antiVirusFailSum', label: 'antiVirusFailSum' },
    { prop: 'processWhiteFailSum', label: 'processWhiteFailSum' },
    { prop: 'processBlackFailSum', label: 'processBlackFailSum' },
    { prop: 'serviceWhiteFailSum', label: 'serviceWhiteFailSum' },
    { prop: 'serviceBlackFailSum', label: 'serviceBlackFailSum' },
    { prop: 'ldFailSum', label: 'ldFailSum' },
    { prop: 'weakPasswordFailSum', label: 'weakPasswordFailSum' },
    { prop: 'computeNameFailSum', label: 'computeNameFailSum' },
    { prop: 'osFailSum', label: 'osFailSum' },
    { prop: 'osVulnerabilityFailSum', label: 'osVulnerabilityFailSum' },
    { prop: 'computeAccountFailSum', label: 'computeAccountFailSum' },
    { prop: 'screenProtectFailSum', label: 'screenProtectFailSum' },
    { prop: 'guestAccountFailSum', label: 'guestAccountFailSum' },
    { prop: 'shareFailSum', label: 'shareFailSum' },
    { prop: 'firewallFailSum', label: 'firewallFailSum' },
    { prop: 'remoteDesktopFailSum', label: 'remoteDesktopFailSum' },
    { prop: 'portFailSum', label: 'portFailSum' },
    { prop: 'domainFailSum', label: 'domainFailSum' },
    { prop: 'countSum', label: 'violationTotal' }
  ],
  64: [
    { prop: 'postCountSum', label: 'postCountSum' }
  ],
  69: [
    { prop: 'outgoingFileCountSum', label: 'outgoingFileCountSum' }
  ],
  71: [
    { prop: 'mtpOutgoingFileCountSum', label: 'mtpOutgoingFileCountSum' }
  ],
  73: [
    { prop: 'securityLevelConvertCountSum', label: 'securityLevelConvertCountSum' }
  ],
  75: [
    { prop: 'ftpFileTransferCountSum', label: 'ftpFileTransferCountSum' }
  ],
  77: [
    { prop: 'bluetoothPairingRecordCountSum', label: 'bluetoothPairingRecordCountSum' }
  ],
  79: [
    { prop: 'bluetoothFileTransferCountSum', label: 'bluetoothFileTransferCountSum' }
  ],
  81: [
    { prop: 'wifiConnRecordCountSum', label: 'wifiConnRecordCountSum' }
  ],
  83: [
    { prop: 'localFileShareCountSum', label: 'localFileShareCountSum' }
  ],
  85: [
    { prop: 'netFileShareCountSum', label: 'netFileShareCountSum' }
  ],
  87: [
    { prop: 'usbOutgoingFileCountSum', label: 'usbOutgoingFileCountSum' }
  ],
  89: [
    { prop: 'computerEnergySavingCountSum', label: 'computerEnergySavingCountSum' }
  ],
  91: [
    { prop: 'cdBurnRecordCountSum', label: 'cdBurnRecordCountSum' }
  ]
}
