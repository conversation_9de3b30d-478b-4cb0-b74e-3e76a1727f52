<template>
  <div class="data-panel">
    <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
      <label :title="item.label">{{ item.label }}</label>
      <span>
        <el-button
          v-for="(btn, i) in item.value"
          :key="i"
          :disabled="!hasPermission(item.menuCode)"
          type="text"
          @click="btn.func"
        ><span>{{ itemValue[btn.key] }}</span></el-button>
      </span>
    </div>
  </div>
</template>

<script>
import {
  getTerminalOnlineNum, getTerminalOfflineWeekNum,
  getTerminalOfflineMonthNum, getUserOnlineNum
} from '@/api/system/terminalManage/terminal'

export default {
  name: 'DataPanel',
  props: {
    temp: {
      type: Object,
      default: undefined
    }
  },
  data() {
    return {
      itemValue: {
        terminalOnline: 0,
        terminalTotal: 0,
        offlineWeek: 0,
        offlineMonth: 0,
        userOnline: 0,
        userTotal: 0
      }
    }
  },
  computed: {
    itemOption() {
      return [
        // 终端数量
        { label: this.$t('pages.terminalLabel1'), menuCode: 'B24', value: [
          { key: 'terminalOnline', func: this.terminalOnline },                 // 在线
          { key: 'terminalTotal', func: this.terminalTotal }]                   // 总数
        },
        // 终端未上线数量
        { label: this.$t('pages.terminalLabel2'), menuCode: 'B24', value: [
          { key: 'offlineWeek', func: this.offlineWeek },                       // 超1周
          { key: 'offlineMonth', func: this.offlineMonth }]                     // 超1月
        },
        // 操作员数量
        { label: this.$t('pages.userLabel'), menuCode: 'B23', value: [
          { key: 'userOnline', func: this.userOnline },                         // 在线
          { key: 'userTotal', func: this.userTotal }]                           // 总数
        }
      ]
    }
  },
  created() {
    this.initData()
  },
  activated() {
    this.initData()
  },
  methods: {
    initData() {
      if (this.temp) {
        this.itemValue = this.temp
      } else {
        this.getData(this.itemValue)
      }
    },
    getData(obj) {
      getTerminalOnlineNum().then(respond => {
        obj.terminalOnline = respond.data.online
        obj.terminalTotal = respond.data.total
      })
      getTerminalOfflineWeekNum().then(respond => {
        obj.offlineWeek = respond.data
      })
      getTerminalOfflineMonthNum().then(respond => {
        obj.offlineMonth = respond.data
      })
      getUserOnlineNum().then(respond => {
        obj.userOnline = respond.data.online
        obj.userTotal = respond.data.total
      })
    },
    terminalOnline() {
      this.toTerminal({ status: 4, offLineType: 0 })
    },
    terminalTotal() {
      this.toTerminal({ status: null, offLineType: 0 })
    },
    offlineWeek() {
      this.toTerminal({ status: 0, offLineType: 7 })
    },
    offlineMonth() {
      this.toTerminal({ status: 0, offLineType: 30 })
    },
    userOnline() {
      this.toUser({ status: 1, reQuery: true })
    },
    userTotal() {
      this.toUser({ reQuery: true })
    },
    // 跳转到终端页面
    toTerminal(params) {
      this.$router.push({ name: 'Terminal', params })
    },
    // 跳转到操作员页面
    toUser(params) {
      this.$router.push({ name: 'User', params })
    }
  }
}
</script>

<style lang='scss' scoped>
  .data-panel{
    width: 100%;
    padding: 20px 35px 0;
    display: flex;
  }
  .data-item{
    flex: 1;
    height: 100px;
    line-height: 100px;
    border: 1px solid #3b749c;
    margin-left: 10px;
    font-size: 16px;
    display: flex;
    &:first-child{
      margin-left: 0;
    }
    label{
      line-height: 30px;
      align-self: center;
      font-weight: normal;
      flex: 2;
      text-align: center;
    }
    span{
      flex: 1;
      text-align: center;
    }
    .el-button {
      padding: 0;
      >>>&>span {
        cursor: default;
        span {
          cursor: pointer;
        }
      }
      &:last-child {
        span {
          margin-left: 10px;
        }
        &:before{
          content: '/';
          cursor: default;
        }
      }
    }
  }
  @media screen and (max-width: 1500px){
    .data-item{
      flex-direction: column;
      line-height: 50px;
    }
  }
</style>
