<template>
  <div class="dashboard-container">
    <component :is="currentRole" />
  </div>
</template>

<script>
import DefaultLayout from './defaultLayout'
import CustomLayout from './customLayout'
// import { isOwn } from '@/api/dashboard'

export default {
  name: 'Dashboard',
  components: { DefaultLayout, CustomLayout },
  data() {
    return {
      currentRole: 'CustomLayout'
    }
  },
  created() {
    // 注释掉说明：不管有没有报表，都按相同模式展示
    // isOwn().then(res => {
    //   const opt = { 1: 'DefaultLayout', 2: 'CustomLayout' }
    //   this.currentRole = opt[res.data]
    // }).catch(e => { console.error(e) })
  }
}
</script>
