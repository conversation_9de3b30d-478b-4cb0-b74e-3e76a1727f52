<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce } from '@/utils'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    testMode: { // 测试模式，仅测试时使用。0：用户，1：部门
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      let legendData = []
      let seriesName = ''
      if (this.testMode === 0) {
        seriesName = '用户违规事件数'
        legendData = ['张三', '李四', '王五', '赵柳', '用户1']
      } else {
        seriesName = '部门违规事件数'
        legendData = ['行政中心', '研发中心', '销售中心', '行业运营事业部', '信息安全研究院']
      }
      this.chart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)',
          backgroundColor: 'rgba(16,16,16,0.7)'
        },
        legend: {
          left: 'center',
          bottom: '10',
          // data: ['张三', '李四', '王五', '赵柳', '用户1'],
          data: legendData,
          inactiveColor: '#666', // 图例关闭时的颜色。
          textStyle: {
            color: '#ccc'
          }
        },
        calculable: true,
        series: [
          {
            name: seriesName,
            type: 'pie',
            // roseType: 'radius',
            radius: [0, 75],
            center: ['50%', '38%'],
            data: [
              { value: 100, name: legendData[0] },
              { value: 85, name: legendData[1] },
              { value: 45, name: legendData[2] },
              { value: 60, name: legendData[3] },
              { value: 59, name: legendData[4] }
            ],
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      })
    }
  }
}
</script>
