<template>
  <div class="dashboard-editor-container">
    <DataPanel />
    <e-charts :charts-option="chartsOption" />
  </div>
</template>

<script>
import DataPanel from '../components/dataPanel'
import ECharts from '@/components/ECharts'
import { getEchartData } from '@/api/dashboard'

export default {
  name: 'DefaultLayout',
  components: {
    ECharts,
    DataPanel
  },
  data() {
    return {
      chartsOption: []
    }
  },
  created() {
    getEchartData().then(res => {
      this.chartsOption = res.data
    })
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  height: calc(100vh - 90px);
  background-color: #0c161e;
  overflow: auto;
  >>>.echarts-container {
    padding: 10px 35px;
  }
  >>>.chart-wrapper {
    border: 1px solid #3b749c;
    padding: 16px 0;
  }
}
</style>
