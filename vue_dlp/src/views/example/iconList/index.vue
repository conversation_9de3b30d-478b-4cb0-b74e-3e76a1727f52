<!--
  该组件仅供开发环境使用，不需做多语言
-->
<template>
  <div class="iconContainer">
    <p class="title">icon 图标列表</p>
    <p class="option">
      <span>图标颜色：</span><el-color-picker v-model="color" size="mini" @change="colorChange"></el-color-picker>
      <br><br>
      搜索图标：<el-input v-model="searchText" v-trim maxlength="" clearable style="width: 200px;"/>
      <br><br>
      <span>点击图标复制组件代码，点击文字复制图标class。</span>
    </p>
    <ul>
      <li v-for="(name, i) in filterSvgIconMap" :key="i">
        <div style="height: 50px; cursor: pointer;" @click="copyCode(name)">
          <svg-icon :icon-class="name" :style="{ color: color }"></svg-icon>
        </div>
        <p @click="copyName(name)">class: <span>{{ name }}</span></p>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: 'IconList',
  data() {
    return {
      color: '#aaa',
      searchText: ''
    }
  },
  computed: {
    filterSvgIconMap() {
      const searchText = this.searchText.toLowerCase()
      return this.svgIconMap.filter(iconName => iconName.toLowerCase().includes(searchText))
    }
  },
  methods: {
    colorChange(val) {
      if (val == null) {
        this.color = '#fff'
      }
    },
    copyCode(name) {
      name = `<svg-icon icon-class="${name}"></svg-icon>`
      this.copyName(name)
    },
    copyName(name) {
      var element = this.createElement(name);
      element.select();
      element.setSelectionRange(0, element.value.length);
      document.execCommand('copy');
      element.remove();
      this.$message({
        message: `已复制 ${name}`,
        type: 'success'
      })
    },
    // 创建临时的输入框元素
    createElement(text) {
      var isRTL = document.documentElement.getAttribute('dir') === 'rtl';
      var element = document.createElement('textarea');
      // 防止在ios中产生缩放效果
      element.style.fontSize = '12pt';
      // 重置盒模型
      element.style.border = '0';
      element.style.padding = '0';
      element.style.margin = '0';
      // 将元素移到屏幕外
      element.style.position = 'absolute';
      element.style[isRTL ? 'right' : 'left'] = '-9999px';
      // 移动元素到页面底部
      const yPosition = window.pageYOffset || document.documentElement.scrollTop;
      element.style.top = `${yPosition}px`;
      // 设置元素只读
      element.setAttribute('readonly', '');
      element.value = text;
      document.body.appendChild(element);
      return element;
    }
  }
}
</script>
<style lang="scss" scoped>
  .iconContainer {
    height: 100%;
    overflow: auto;
    border: 1px solid transparent;
  }
  .title {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
  }
  .option {
    padding-left: 100px;
    margin-bottom: 40px;
    font-size: 18px;
    >>>.el-color-picker {
      vertical-align: middle;
    }
  }
  ul {
    width: 100%;
    list-style: none;
    font-size: 16px;
  }
  li {
    width: 200px;
    height: 100px;
    margin-right: 25px;
    margin-bottom: 25px;
    text-align: center;
    float: left;
    .svg-icon {
      margin-top: 15px;
      font-size: 22px;
    }
  }
  ul p {
    cursor: pointer;
  }
</style>
