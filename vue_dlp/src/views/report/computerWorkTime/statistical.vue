<template>
  <Statistical
    :api="'getComputerWorkTimeBar'"
    :default-col-model="defaultColModel"
    :custom-list="customList"
    :format-option="formatOption"
    :only-terminal="true"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../component/Statistical'
import { formatSeconds } from '@/utils'

export default {
  name: 'ComputerWorkTimeStatistical',
  components: { Statistical },
  data() {
    return {
      workStatusMap: {
        1: this.$t('pages.openComputer'),
        2: this.$t('pages.closeComputer'),
        3: this.$t('pages.login'),
        4: this.$t('pages.logout'),
        5: this.$t('pages.awaken'),
        6: this.$t('pages.sleep'),
        7: this.$t('pages.exitScreensaver'),
        8: this.$t('pages.screenSaver'),
        9: this.$t('pages.exitLock'),
        10: this.$t('pages.lock'),
        11: this.$t('pages.connectUser'),
        12: this.$t('pages.disconnectUser'),
        13: this.$t('pages.unexpectedShutdown'),
        100: this.$t('pages.virtualshutdown'),
        101: this.$t('pages.virtualBoot')
      },
      customList: [
        { prop: 'workTimeAll', label: 'workTime', width: '150', sort: true, sortOriginal: true, flag: 'time', value: 1, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_overtime', detailCol: this.getDetailCol() },
        { prop: 'electricityCostAll', label: 'electricityCost', minWidth: 150, sortable: true, value: 2, detailTable: 'dwd_computer_work_overtime', detailCol: this.getDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'workTimeAll', label: 'workTime', width: '150', sort: true, sortOriginal: true, flag: 'time', value: 1, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_overtime', detailCol: this.getDetailCol() },
        { prop: 'electricityCostAll', label: 'electricityCost', minWidth: '150', sortable: true, value: 2, detailTable: 'dwd_computer_work_overtime', detailCol: this.getDetailCol() }
      ]
    }
  },
  methods: {
    getDetailCol() {
      return [
        { prop: 'create_time', label: 'time', width: '100' },
        { prop: 'terminal_name', label: 'terminalName', width: '100', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
        { prop: 'term_group_name', label: 'terminalGroup', width: '100', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
        { prop: 'op_type', label: 'opType', width: '150', formatter: (row) => {
          return this.workStatusMap[row.op_type]
        } },
        { prop: 'log_description', label: 'logDescription', width: '150' }
      ]
    },
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
    },
    formatOption(option) {
      option.option.yAxis = [{
        name: this.$t('text.hour'),
        axisLabel: {
          formatter: function(value) {
            const hour = Math.ceil(value / 3600)
            if (hour == 1) {
              return (value / 3600).toFixed(2)
            }
            return hour
          }
        }
      }]
    }
  }
}
</script>
