<template>
  <Trend :api="'getFileOpTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('635')" :exportable="hasPermission('636')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getFileOpNumDetailCol, getFileOpTypeMap, beforeLoadFileOpDetail } from '@/utils/report'

export default {
  name: 'FileOpTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'srcFileNumSumAll', label: 'srcFileNumSumAll', width: '150', sort: true, detailTable: 'dwd_file_op_log', detailCol: getFileOpNumDetailCol() }
      ],
      customList: [
        { prop: 'srcFileNumSumAll', label: 'srcFileNumSumAll', width: '150', sort: true, detailTable: 'dwd_file_op_log', detailCol: getFileOpNumDetailCol() }
      ]
    }
  },
  created() {
    this.concatOpTypeCol()
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    concatOpTypeCol() {
      const opTypeCol = Object.entries(getFileOpTypeMap()).map(([key, value]) => ({ prop: 'category_' + key, label: value, width: '150', sort: true, beforeLoadDetail: beforeLoadFileOpDetail, detailTable: 'dwd_file_op_log', detailCol: getFileOpNumDetailCol() }))
      this.customList = this.customList.concat(opTypeCol)
    }
  }
}
</script>
