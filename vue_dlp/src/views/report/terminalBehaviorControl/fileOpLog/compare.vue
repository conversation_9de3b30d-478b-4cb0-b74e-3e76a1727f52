<template>
  <comparative-trend
    :api="'getFileOpLogCompareData'"
    :api-dept="'getFileOpLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'FileOpCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '文件操作',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'srcFileNumSumAll', label: 'srcFileNumSumAll' },
        { prop: 'srcFileSizeSumAll', label: 'srcFileSizeSumAll', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['srcFileNumSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
