<template>
  <Statistical :api="'getFileOpBar'" :default-col-model="defaultColModel" :custom-list="customList" :drill-down="hasPermission('630')" :exportable="hasPermission('631')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '../../component/Statistical'
import { getFileOpNumDetailCol, getFileOpTypeMap, beforeLoadFileOpDetail } from '@/utils/report'

export default {
  name: 'FileOpStatistical',
  components: { Statistical },
  data() {
    return {
      detailColModel: null,
      customList: [
        { prop: 'srcFileNumSumAll', label: 'srcFileNumSumAll', width: '150', sort: true, detailTable: 'dwd_file_op_log', detailCol: getFileOpNumDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'srcFileNumSumAll', label: 'srcFileNumSumAll', width: '150', sort: true, detailTable: 'dwd_file_op_log', detailCol: getFileOpNumDetailCol() }
      ]
    }
  },
  created() {
    this.concatOpTypeCol()
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
    },
    concatOpTypeCol() {
      const opTypeCol = Object.entries(getFileOpTypeMap()).map(([key, value]) => ({ prop: 'category_' + key, label: value, width: '150', sort: true, beforeLoadDetail: beforeLoadFileOpDetail, detailTable: 'dwd_file_op_log', detailCol: getFileOpNumDetailCol() }))
      this.customList = this.customList.concat(opTypeCol)
    }
  }
}
</script>
