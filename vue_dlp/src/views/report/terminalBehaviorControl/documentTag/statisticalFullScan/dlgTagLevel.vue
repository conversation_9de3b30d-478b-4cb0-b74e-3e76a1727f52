<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('report.tagReportMsg11')"
      :fullscreen="fullscreen"
      :visible.sync="dialogVisible"
      width="1000px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="squareList"
        :stripe="false"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :autoload="false"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDiskScanTagDetailPage } from '@/api/report/baseReport/documentTag/tag';

export default {
  name: 'DlgTagLevel',
  components: { },
  props: {

  },
  data() {
    return {
      opTypeOptions: {
        5: this.$t('pages.diskScan_Msg3'), 3: this.$t('pages.diskScan_Msg4'), 1: this.$t('pages.fullDecryption'), 2: this.$t('pages.globalEncryption'),
        7: this.$t('pages.diskScan_Msg47'), 8: this.$t('pages.diskScan_Msg48'), 9: this.$t('pages.diskScan_Msg49')/*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/
      },
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      query: { // 查询条件
        page: 1,
        limit: 20,
        trank: ''
      },
      colModel: [
        { prop: 'create_time', label: 'operateTime', width: '150' },
        { prop: 'terminal_name', label: 'terminalName' },
        { prop: 'term_group_name', label: 'deptName' },
        { prop: 'trank', label: 'labelGrade', formatter: this.trankFormatter },
        { prop: 'trank_name', label: 'gradeName' },
        { prop: 'tag_content', label: 'labelContent', width: '120', formatter: this.tagContentFormatter },
        { prop: 'file_name', label: 'fileName' },
        { prop: 'file_path', label: 'filePath' },
        { prop: 'op_type', label: 'operateType', width: '150', formatter: this.opTypeFormatter },
        { prop: 'encry_flag', label: 'encryFlag', formatter: this.encryFormatter }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['squareList']
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  created() {
  },
  methods: {
    tagContentFormatter(data) {
      if (data.clear_flag == 2) {
        return this.$t('report.tagReportMsg33')
      }
      return data.tag_content.replace(/;+$/, '')
    },
    trankFormatter(row) {
      if (row.result != 0) {
        return null
      }
      return row.trank
    },
    opTypeFormatter(row, data) {
      row.opType = row.opType + ''
      return this.opTypeOptions[data]
    },
    encryFormatter(row, data) {
      if (data === 1) {
        return this.$t('pages.cipherText')
      } else {
        return this.$t('pages.plainText')
      }
    },
    rowDataApi(option) {
      this.query.trank = this.$parent.queryTrank
      const searchQuery = Object.assign({}, this.query, this.$parent.query, option)
      searchQuery.searchCount = true
      return getDiskScanTagDetailPage(searchQuery)
    },
    /**
     * 显示弹框
     */
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.query.page = 1
        this.query.trank = this.$parent.queryTrank
        this.gridTable.execRowDataApi(this.query);
      })
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

