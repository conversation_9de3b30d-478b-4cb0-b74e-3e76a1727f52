<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.obtainNumberLabelFiles')"
      :fullscreen="fullscreen"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="squareList"
        :stripe="false"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :autoload="false"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDiskScanTagDetailPage } from '@/api/report/baseReport/documentTag/tag';

export default {
  name: 'DlgTagFile',
  components: { },
  props: {

  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      query: { // 查询条件
        page: 1,
        limit: 20
      },
      colModel: [
        { prop: 'terminal_name', label: 'terminalName' },
        { prop: 'term_group_name', label: 'deptName' },
        { prop: 'file_name', label: 'fileName', width: '120' },
        { prop: 'file_path', label: 'filePath', width: '120' },
        { prop: 'trank', label: 'labelGrade', formatter: this.trankFormatter },
        { prop: 'trank_name', label: 'gradeName', formatter: this.trankNameFormatter },
        { prop: 'tag_content', label: 'labelContent', formatter: this.tagContentFormatter },
        { prop: 'encry_flag', label: 'encryFlag', formatter: this.encryFormatter }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['squareList']
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  created() {
  },
  methods: {
    trankFormatter(row) {
      if (row.result != 0) {
        return null
      }
      return row.trank
    },
    trankNameFormatter(row) {
      if (row.result != 0) {
        return this.$t('pages.noSignInfo')
      }
      return row.trank_name
    },
    encryFormatter(row, data) {
      if (data === 1) {
        return this.$t('pages.cipherText')
      } else {
        return this.$t('pages.plainText')
      }
    },
    tagContentFormatter(data) {
      if (data.result != 0) {
        return this.$t('pages.noSignInfo')
      }
      if (data.clear_flag == 2) {
        return this.$t('report.tagReportMsg33')
      }
      return data.tag_content.replace(/;+$/, '')
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.query, option)
      searchQuery.filterNotTag = this.query.filterNotTag
      searchQuery.searchCount = true
      return getDiskScanTagDetailPage(searchQuery)
    },
    /**
     * 显示弹框
     */
    show(filterNotTag) {
      if (filterNotTag != null && filterNotTag != undefined) {
        this.query.filterNotTag = filterNotTag
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.query.page = 1
        this.gridTable.execRowDataApi(this.query);
      })
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

