<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      v-zoom-dialog
      :fullscreen="fullscreen"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div class="sensitive-control-echartDlg">
        <div class="toolbar" style="height: 30px;">
          <div style="float: right;">
            <el-select v-model="dialogQuery.recordSize" filterable @change="changeValue">
              <el-option :value="5" :label="$t('pages.topN', { num: 5 })"></el-option>
              <el-option :value="10" :label="$t('pages.topN', { num: 10 })"></el-option>
              <el-option :value="20" :label="$t('pages.topN', { num: 20 })"></el-option>
              <el-option :value="30" :label="$t('pages.topN', { num: 30 })"></el-option>
              <el-option :value="40" :label="$t('pages.topN', { num: 40 })"></el-option>
            </el-select>
          </div>
        </div>
        <div v-if="dialogStatus === 'dept'" :style="{height:tableHeight}" class="no-pointer">
          <pie-chart
            ref="chart1"
            :chart-data="temp.barChartData"
            :chart-option="temp.barChartOption"
            :x-axis-name="this.$t('pages.dept')"
            style="width: 100%;height: 100%"
          />
        </div>
        <div v-if="dialogStatus === 'terminal'" :style="{height:tableHeight}" class="no-pointer">
          <bar-chart
            ref="chart2"
            :chart-data="temp.chartsUserDatas"
            :chart-option="temp.chartUserOption"
            style="width: 100%;height: 100%"
          />
        </div>
        <div v-if="dialogStatus === 'tagContent'" :style="{height:tableHeight}" class="no-pointer" >
          <bar-chart
            ref="chart3"
            :chart-data="temp.tagContentDatas"
            :chart-option="temp.tagContentOption"
            style="width: 100%;height: 100%"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
import { getDiskScanTagGroupChartData, getDiskScanTagTermChartData, getDiskScanTagContentChartData } from '@/api/report/baseReport/documentTag/tag';

export default {
  name: 'EchartDlg',
  components: { BarChart, PieChart },
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    },
    echartStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      // 下拉框选中值
      // 父组件传递过来的query搜索条件
      dialogQuery: {
        recordSize: 10
      },
      dialogStatus: '',
      // 柱状图
      temp: {},
      defaultTemp: {
        // 部门风险行为数量饼图
        barChartData: [],
        barChartOption: {
          title: {
            text: this.$t('report.tagReportMsg12')
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 柱状图 操作员风险行为数量图
        chartsUserDatas: [
        ],
        chartUserOption: {
          title: {
            text: this.$t('report.tagReportMsg13'),
            left: 'center'
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0,
              // 倾斜的程度
              rotate: -60
            }
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 柱状图 标签内容柱状图
        tagContentDatas: [
        ],
        tagContentOption: {
          title: {
            text: this.$t('report.tagReportMsg10'),
            left: 'center'
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: this.$t('report.tagContentLabel'),
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0,
              // 倾斜的程度
              rotate: -60
            }
          },
          grid: {
            top: '25%',
            width: '80%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      }
    }
  },
  computed: {
    tableHeight() {
      return this.fullscreen ? '100%' : '400px'
    }
  },
  watch: {
    fullscreen() {
      this.$nextTick(() => {
        if (this.dialogStatus === 'dept') {
          this.$refs.chart1.chart.resize()
        } else if (this.dialogStatus === 'terminal') {
          this.$refs.chart2.chart.resize()
        } else {
          this.$refs.chart3.chart.resize()
        }
      })
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 显示弹框 (根据查询条件不同，下方显示的柱状图可能是部门，终端，操作员其中一种)
     * @param query 父组件传过来的查询条件query
     * @param status 父组件传过来的状态  dept:部门，terminal：终端，user：操作员（用来区分调哪个接口）
     */
    show(query, status) {
      this.dialogQuery.recordSize = 10
      // 下拉框切换应该也会用到这个参数，所以新声明一个dialogQuery字段
      this.dialogVisible = true
      this.dialogStatus = status
      this.dialogQuery = Object.assign(this.dialogQuery, query)
      this.getData()
    },
    getData() {
      if (this.dialogStatus === 'dept') {
        getDiskScanTagGroupChartData(this.dialogQuery).then(res => {
          this.temp.barChartData = res.data.chartData
        })
      } else if (this.dialogStatus === 'terminal') {
        getDiskScanTagTermChartData(this.dialogQuery).then(res => {
          this.temp.chartsUserDatas = res.data.chartData
          this.temp.chartUserOption.xAxis.data = res.data.xaxisData
          this.temp.chartUserOption.series[0].data = res.data.seriesData
        })
      } else if (this.dialogStatus === 'tagContent') {
        getDiskScanTagContentChartData(this.dialogQuery).then(res => {
          this.temp.tagContentDatas = res.data.chartData
          this.temp.tagContentOption.xAxis.data = res.data.xaxisData
          this.temp.tagContentOption.series[0].data = res.data.seriesData
        })
      }
    },
    /**
     * 下拉框选中发生变化时，直接查询对应的柱状图信息
     * @param val 下拉框选中value值
     */
    changeValue(val) {
      this.getData()
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
<style>
  .no-pointer>div>div>canvas {
    cursor: default;
  }
</style>
