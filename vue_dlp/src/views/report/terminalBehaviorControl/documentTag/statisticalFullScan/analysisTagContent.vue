<template>
  <!--部门分析-->
  <div class="data-details sensitiveness-report">
    <div class="data-analysis-table table-parent-size">
      <div class="table-container table-left-size">
        <!--左侧列表查询条件-->
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchLeftForm" label-position="right" :model="query" label-width="80px">
            <FormItem :label="$t('table.labelContent')">
              <el-input v-model="query.tagContent" v-trim clearable maxlength="100"/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetLeftQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="handleFilterLeft()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button slot="reference" type="primary" icon="el-icon-search" class="search-icon-left" size="mini"></el-button>
        </el-popover>
        <grid-table
          ref="deptList"
          row-key="id"
          :col-model="colModel"
          :row-data-api="rowDataApi"
          :multi-select="false"
          @currentChange="currentChangeLeft"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--部门分析上方几个小方块-->
      <div class="data-panel">
        <div v-for="(item, key) in temp.deptItemOption" :key="key" class="data-item data-item-details" >
          <div class="icon">
            <svg-icon :icon-class="item.ico"></svg-icon>
          </div>
          <div class="content">
            <label :title="item.label">{{ item.label }}</label>
            <span :title="item.value">{{ item.value }}</span>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 167px); padding: 0 9px;position: relative">
        <!--右侧列表查询条件-->
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchRightForm" label-position="right" :model="detailQuery" label-width="80px">
            <FormItem :label="$t('table.terminalName')">
              <el-input v-model="detailQuery.terminalName" v-trim clearable/>
            </FormItem>
            <FormItem :label="$t('table.deptName')">
              <el-input v-model="detailQuery.termGroupName" v-trim clearable/>
            </FormItem>
            <FormItem :label="$t('table.labelGrade')">
              <el-input-number v-model="detailQuery.trank" v-trim clearable :min="0" :step="1" :controls="false" :precision="0" class="left-aligned-input"/>
            </FormItem>
            <FormItem :label="$t('table.gradeName')">
              <el-input v-model="detailQuery.trankContent" v-trim clearable/>
            </FormItem>
            <FormItem :label="$t('table.fileName')">
              <el-input v-model="detailQuery.fileName" v-trim clearable/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetAdvancedQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="searchBtn()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button v-show="isShowGrid" slot="reference" type="primary" icon="el-icon-search" class="search-icon-right" size="mini"></el-button>
        </el-popover>
        <el-tabs v-model="activeDeptName" @tab-click="tabClick">
          <el-tab-pane :label="$t('report.tagReportMsg8')" name="deptMsg" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                ref="deptDetailList"
                :show="isShowGrid"
                :col-model="colModelDetail"
                :row-data-api="getDetailData"
                :autoload="false"
                :multi-select="false"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('report.tagReportMsg9')" name="termStatistics">
            <div class="toolbar" style="height: 20px; margin-right: 3px;margin-top: 2px;">
              <div style="float: right;">
                <el-select v-model="detailQuery.chartSize" filterable style="width: 120px" @change="changeValue">
                  <el-option :value="5" :label="$t('pages.topN', { num: 5 })"></el-option>
                  <el-option :value="10" :label="$t('pages.topN', { num: 10 })"></el-option>
                  <el-option :value="20" :label="$t('pages.topN', { num: 20 })"></el-option>
                  <el-option :value="30" :label="$t('pages.topN', { num: 30 })"></el-option>
                  <el-option :value="40" :label="$t('pages.topN', { num: 40 })"></el-option>
                </el-select>
              </div>
            </div>
            <bar-chart
              v-if="isShowChart"
              :chart-data="temp.tagTerminalDatas"
              :chart-option="temp.tagTerminalOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('report.tagReportMsg32')" name="deptStatistics">
            <div class="toolbar" style="height: 20px; margin-right: 3px;margin-top: 2px;">
              <div style="float: right;">
                <el-select v-model="detailQuery.recordSize" filterable style="width: 120px" @change="changeValue">
                  <el-option :value="5" :label="$t('pages.topN', { num: 5 })"></el-option>
                  <el-option :value="10" :label="$t('pages.topN', { num: 10 })"></el-option>
                  <el-option :value="20" :label="$t('pages.topN', { num: 20 })"></el-option>
                  <el-option :value="30" :label="$t('pages.topN', { num: 30 })"></el-option>
                  <el-option :value="40" :label="$t('pages.topN', { num: 40 })"></el-option>
                </el-select>
              </div>
            </div>
            <pie-chart
              v-if="isShowDept"
              :chart-data="temp.deptPieDatas"
              :chart-option="temp.deptPieOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from '@/components/ECharts/PieChart';
import BarChart from '@/components/ECharts/BarChart';
import {
  getDiskScanTagContentPage,
  getDiskScanTagContentAnalysisData,
  getDiskScanContentDetailPage
} from '@/api/report/baseReport/documentTag/tag';
import { checkDiskScanDetailAble } from '@/api/report/baseReport/issueReport/issue';
export default {
  name: 'AnalysisTagContent',
  components: { PieChart, BarChart },
  props: {

  },
  data() {
    return {
      query: {},
      DefaultQuery: {
        page: 1,
        limit: 20,
        tagContent: undefined
      },
      detailQuery: {},
      DefaultDetailQuery: {
        page: 1,
        limit: 20,
        recordSize: 10,
        chartSize: 10,
        tagId: '',
        tagContent: '',
        trank: undefined,
        tranContent: undefined,
        terminalName: undefined,
        termGroupName: undefined,
        fileName: undefined
      },
      // 左侧列表
      colModel: [
        { prop: 'tag_content', label: 'labelContent', formatter: (row) => {
          if (row.tag_id == 3) {
            return this.$t('report.tagReportMsg33')
          } else {
            return row.tag_content
          }
        } },
        { prop: 'file_num_sum', label: 'obtainNumberLabelFiles', sort: true }
      ],
      // 右侧部门分析
      activeDeptName: 'deptMsg',
      isShowGrid: true,
      isShowGridInfo: false,
      isShowChart: false,
      isShowDept: false,
      // 右侧部门风险信息
      colModelDetail: [
        { prop: 'create_time', label: 'operateTime', width: '120' },
        { prop: 'terminal_name', label: 'terminalName' },
        { prop: 'term_group_name', label: 'deptName' },
        { prop: 'trank', label: 'labelGrade' },
        { prop: 'trank_name', label: 'gradeName' },
        { prop: 'file_name', label: 'fileName' },
        { prop: 'file_path', label: 'filePath', width: '150' },
        { prop: 'encry_flag', label: 'encryFlag', formatter: this.encryFormatter }
      ],
      temp: {},
      defaultTemp: {
        // 右侧上方小标题
        deptName: '',
        // 部门分析
        deptItemOption: [
          { label: '', value: '', ico: 'algorithm' },
          { label: this.$t('table.obtainNumberLabelFiles'), value: 0, ico: 'ruleGroup' }
        ],
        // 终端文档标签数量图 饼图
        tagTerminalDatas: [],
        tagTerminalOption: {
          title: {
            text: this.$t('report.tagReportMsg13'),
            left: 'center',
            subtext: ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            data: [],
            axisLabel: {
              interval: 0,
              // 倾斜的程度
              rotate: -60
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 部门 饼图
        deptPieDatas: [],
        deptPieOption: {
          title: {
            'text': this.$t('report.tagReportMsg12')
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        }
      }
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
    this.query = Object.assign({}, this.DefaultQuery)
  },
  async mounted() {
    await this.$refs['deptList'].execRowDataApi()
    const tableData = this.$refs['deptList'].getDatas()
    // 如果左侧列表长度不为0，初始化页面时显示第一条数据的详情
    if (tableData[0]) {
      this.currentChangeLeft(tableData[0])
    }
  },
  activated() {},
  methods: {
    encryFormatter(row, data) {
      if (data === 1) {
        return this.$t('pages.cipherText')
      } else {
        return this.$t('pages.plainText')
      }
    },
    /**
     *左边表格重置
     * */
    resetLeftQuery() {
      this.query.tagContent = undefined
    },
    /**
     *左边表格搜索
     * */
    async handleFilterLeft() {
      this.query.page = 1
      // console.log('query', this.query)
      await this.$refs['deptList'].execRowDataApi(this.query)
      const tableData = this.$refs['deptList'].getDatas()
      if (tableData[0]) {
        this.currentChangeLeft(tableData[0])
      } else {
        this.resetTemp()
        this.detailQuery.page = 1
        this.$refs.deptDetailList.clearRowData()
        this.$refs.deptDetailList.clearPageData()
      }
    },
    /**
       * 高级查询，查询操作
       * */
    searchBtn() {
      this.detailQuery.page = 1
      this.$refs['deptDetailList'].execRowDataApi(this.detailQuery)
    },
    /**
       *重置《高级查询重置按钮》
       * */
    resetAdvancedQuery() {
      this.detailQuery.fileName = undefined
      this.detailQuery.terminalName = undefined
      this.detailQuery.termGroupName = undefined
      this.detailQuery.trank = undefined
      this.detailQuery.trankContent = undefined
    },
    /**
       *右边表格搜索
       * */
    handleFilterRight() {
      this.detailQuery.page = 1
      this.$refs['deptDetailList'].execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = { ...this.defaultTemp }
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.query, option)
      searchQuery.searchCount = true
      return getDiskScanTagContentPage(searchQuery)
    },
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.detailQuery, this.$parent.query, option)
      searchQuery.searchCount = true
      return getDiskScanContentDetailPage(searchQuery)
    },
    /**
       * 获取左边列表行点击时的信息（右边的详细信息）
       * @param row 当前行信息
       * */
    currentChangeLeft(row) {
      // 小方块
      this.temp.deptItemOption[0].label = this.$t('table.labelContent')
      this.temp.deptItemOption[0].value = row.tag_content
      if (row.tag_id == 3) {
        this.temp.deptItemOption[0].value = this.$t('report.tagReportMsg33')
      }
      this.temp.deptItemOption[1].value = row.file_num_sum
      this.detailQuery.tagId = row.tag_id
      this.detailQuery.tagContent = row.tag_content
      const searchQuery = Object.assign({}, this.detailQuery, this.$parent.query)
      this.changeValue()
      checkDiskScanDetailAble({ guid: this.$parent.query.guid }).then(() => {
        this.$refs['deptDetailList'].execRowDataApi(searchQuery)
      })
    },
    changeValue(val) {
      const searchChartQuery = Object.assign({}, this.detailQuery, this.$parent.query)
      searchChartQuery.fileName = undefined
      searchChartQuery.terminalName = undefined
      searchChartQuery.termGroupName = undefined
      searchChartQuery.trank = undefined
      searchChartQuery.trankContent = undefined

      getDiskScanTagContentAnalysisData(searchChartQuery).then(resp => {
        // 终端占比 饼图
        if (resp.data) {
          // 终端文档标签统计 柱状图
          this.temp.tagTerminalOption.xAxis.data = resp.data.chartDataObjMap.terminalUserBarChartData.xaxisData
          this.temp.tagTerminalOption.series[0].data = resp.data.chartDataObjMap.terminalUserBarChartData.seriesData
          // 部门 饼图
          this.temp.deptPieDatas = resp.data.chartDataObjMap.deptPieChartData.chartData
        } else {
          this.temp = { ...this.defaultTemp }
        }
      })
    },
    /**
       * tab点击事件，如果不写，切换时图表只有小小一点点
       * @param pane
       * @param event
       */
    tabClick(pane, event) {
      if (pane.name === 'deptMsg') {
        this.isShowGrid = true
        this.isShowChart = false
        this.isShowDept = false
      } else if (pane.name === 'termStatistics') {
        this.isShowGrid = false
        this.isShowChart = true
        this.isShowDept = false
      } else {
        this.isShowGrid = false
        this.isShowChart = false
        this.isShowDept = true
      }
    }
  }
}
</script>
<style lang='scss' scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left !important;
  }
</style>
