<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query">
        <i class="el-icon-s-home" style="position: absolute; left: 24px; top: 16px;"></i>
        <!--面包屑-->
        <div style="width: 510px; float: left;">
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <!--上方搜索条件-->
        <div style="float: right">
          <Form ref="searchForm" :model="query" :rules="rules" label-width="120px" label-position="right">
            <FormItem :label="$t('report.fullScanTask')" prop="guid">
              <el-select v-model="query.guid" @change="taskChange">
                <el-option v-for="option in guidOptions" :key="option.key" :value="option.key" :label="option.name"/>
              </el-select>
              <!--图表解读-->
              <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
              <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
              <!-- <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
              <el-button type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>-->
            </FormItem>
          </Form>
        </div>
      </div>

      <!-- 页面无数据 -->
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>

      <!--分析初始页面-->
      <div v-if="InceptionDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
            <div class="icon">
              <svg-icon :icon-class="item.icon"></svg-icon>
            </div>
            <div class="content">
              <label :title="item.label">{{ item.label }}</label>
              <el-button type="text" :class="{'underline': true }" @click="item.func">{{ temp.itemValue[item.key] }}</el-button>
            </div>
          </div>
        </div>
        <!--中间文档等级TOP10--柱状图-->
        <div class="chart-panel">
          <!-- 终端柱状图 -->
          <div class="chart-item flex-2">
            <div class="mini-title">
              <span @click="deptTerminalDocumentClick">{{ $t('report.tagReportMsg14') }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ $t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigTerminal"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.tagTerminalDatas"
              :chart-option="temp.tagTerminalOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.termSensitiveBar.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.termSensitiveBar + '</div>'"></div>
            </div>
          </div>
          <!-- 部门饼图 -->
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="deptTitleClick">{{ $t('report.tagReportMsg15') }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ $t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigDept"/>
              </el-tooltip>
            </div>
            <pie-chart
              :chart-data="temp.barChartData"
              :chart-option="temp.barChartOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.deptDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.deptDistribute + '</div>'"></div>
            </div>
          </div>
        </div>
        <!--下方对部门，终端（操作员），数量趋势的分别展示部分-->
        <div class="chart-panel">
          <!-- 标签内容柱状图 -->
          <div class="chart-item flex-2">
            <div class="mini-title">
              <span @click="notOfflineTitleClick">{{ $t('report.tagReportMsg17') }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigTagContent"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.chartsTagContentDatas"
              :chart-option="temp.chartTagContentOption"
              :x-axis-name="this.$t('report.tagContentLabel')"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.tagContent.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.tagContent + '</div>'"></div>
            </div>
          </div>
          <!-- 标签等级饼图 -->
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="tagLevelTitleClick">{{ $t('report.tagReportMsg16') }}>></span>
            </div>
            <pie-chart
              :chart-data="temp.chartsTagLevelDatas"
              :chart-option="temp.chartTagLevelOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.fileLever.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.fileLever + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
      <!--终端文档分析-->
      <div v-if="terminalDocumentDiv" class="detailsDiv">
        <analysis-terminal-document/>
      </div>
      <!--部门分析-->
      <div v-if="deptDiv" class="detailsDiv">
        <analysis-dept/>
      </div>
      <!--文档等级分析-->
      <div v-if="tagLevelDiv" class="detailsDiv">
        <analysis-tag-level/>
      </div>
      <!--标签内容分析-->
      <div v-if="tagContentDiv" class="detailsDiv">
        <analysis-tag-content/>
      </div>
    </div>
    <!--小方块（获取标签文件个数）点击弹框-->
    <tag-file ref="tagFile"/>
    <!--小方块（部门）点击弹框-->
    <dept-dlg ref="deptDlg"/>
    <!--小方块点（文档等级最高文件个数）击弹框-->
    <tag-level-dlg ref="tagLevelNumDlg"/>
    <!--    部门、终端前五名小图标点击弹框-->
    <echart-dlg ref="echartDlg" :title="echartDlgTitle" :echart-status="echartStatus"/>
  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
import DeptDlg from '@/views/report/terminalBehaviorControl/documentTag/statisticalFullScan/dlgDept';
import EchartDlg from '@/views/report/terminalBehaviorControl/documentTag/statisticalFullScan/echartDlg';
import TagFile from '@/views/report/terminalBehaviorControl/documentTag/statisticalFullScan/dlgTagFile';
import TagLevelDlg from '@/views/report/terminalBehaviorControl/documentTag/statisticalFullScan/dlgTagLevel';
import analysisTerminalDocument from '@/views/report/terminalBehaviorControl/documentTag/statisticalFullScan/analysisTerminalDocument';
import analysisDept from '@/views/report/terminalBehaviorControl/documentTag/statisticalFullScan/analysisDept';
import analysisTagLevel from '@/views/report/terminalBehaviorControl/documentTag/statisticalFullScan/analysisTagLevel';
import analysisTagContent from '@/views/report/terminalBehaviorControl/documentTag/statisticalFullScan/analysisTagContent';
import { getDiskScanGuidOptionsPro, getDiskScanTagHomepageData } from '@/api/report/baseReport/documentTag/tag';

export default {
  name: 'DocumentTagStatisticalFullScan',
  components: { BarChart, PieChart, DeptDlg, EchartDlg, TagFile, TagLevelDlg, analysisTerminalDocument, analysisDept, analysisTagLevel, analysisTagContent },
  props: {
  },
  data() {
    return {
      commonInterpretation: `<span>图表解读：</span>`,
      chartInterpretationContent: {
        deptDistribute: '',
        fileLever: '',
        tagContent: '',
        termSensitiveBar: ''
      },
      // 查询条件下拉框
      guidOptions: [],
      // 搜查查询
      query: {
        // 是否展示图表解读
        isExplain: true,
        filterNotTag: true,
        guid: '',
        batchNo: ''
      },
      queryTrank: '',
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: this.$t('report.tagReportMsg18') }
      ],
      // 暂无数据
      noData: true,
      // 初始页面显示隐藏
      InceptionDiv: false,
      // 终端文档分析显示隐藏
      terminalDocumentDiv: false,
      // 部门分析显示隐藏
      deptDiv: false,
      // 文档等级分析显示隐藏
      tagLevelDiv: false,
      // 标签内容分析显示隐藏
      tagContentDiv: false,
      // 弹框名称
      deviceTitle: '',
      // 放大柱状图弹框名称
      echartDlgTitle: '',
      echartStatus: 'dept',
      rules: {
        guid: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ]
      },
      // 上方小方块
      itemOption: [
        { label: this.$t('table.allDiskScanTagFileNum'), key: 'allNum', icon: 'total', func: this.scanAllNum },
        { label: this.$t('table.obtainNumberLabelFiles'), key: 'tagNum', icon: 'total', func: this.totalGetTagNumFun },
        { label: this.$t('table.dept'), key: 'dept', icon: 'tree', func: this.deptFun },
        { label: this.$t('report.tagReportMsg11'), key: 'tagLevelNum', icon: 'total', func: this.tagLevelNum }
      ],
      temp: {},
      defaultTemp: {
        itemValue: {
          allNum: '0',
          tagNum: '0',
          dept: '0',
          tagLevelNum: '0'
        },
        // 终端文档标签数量图 柱状图
        tagTerminalDatas: [],
        tagTerminalOption: {
          title: {
            text: this.$t('report.tagReportMsg13'),
            left: 'center',
            'subtext': this.$t('pages.topN', { num: 10 })
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0,
              // 倾斜的程度
              rotate: -10
            }
          },
          yAxis: {
            name: this.$t('components.number'),
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 部门文档标签数量图 饼图
        barChartData: [],
        barChartOption: {
          title: {
            'text': this.$t('report.tagReportMsg12'),
            'subtext': this.$t('pages.topN', { num: 10 })
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 文档等级统计 饼图
        chartsTagLevelDatas: [],
        chartTagLevelOption: {
          title: {
            text: this.$t('report.labelLevelStatistics'),
            left: 'center',
            'subtext': this.$t('pages.topN', { num: 10 })
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 文档标签内容统计 柱状图
        chartsTagContentDatas: [],
        chartTagContentOption: {
          title: {
            text: this.$t('report.tagReportMsg10'),
            left: 'center',
            'subtext': this.$t('pages.topN', { num: 10 })
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0,
              // 倾斜的程度
              rotate: -10
            }
          },
          yAxis: {
            name: this.$t('components.number'),
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
    this.getOptions()
    this.loadComponent()
  },
  activated() {},
  methods: {
    /**
    * 任务编号发生变化时，查询条件赋值
    * */
    taskChange(data) {
      const selectedObj = this.guidOptions.find(item => item.key === data);
      this.query.guid = selectedObj.key
      this.query.batchNo = selectedObj.id
    },
    /**
     * 获取全盘扫描任务下拉列表
     * */
    getOptions() {
      getDiskScanGuidOptionsPro({ taskType: 8 }).then(res => {
        this.guidOptions = res.data
        if (this.guidOptions.length > 0) {
          this.query.guid = this.guidOptions[0].key
          this.query.batchNo = this.guidOptions[0].id
          this.loadReport()
        } else {
          this.noData = true
        }
      })
    },
    /**
     * 部门分析小图标点击
     * */
    handleBigDept() {
      this.echartDlgTitle = this.$t('report.tagReportMsg12')
      this.echartStatus = 'dept'
      this.$refs.echartDlg.show(this.query, this.echartStatus)
    },
    /**
     * 部门分析小图标点击
     * */
    handleBigTerminal() {
      this.echartDlgTitle = this.$t('report.tagReportMsg13')
      this.echartStatus = 'terminal'
      this.$refs.echartDlg.show(this.query, this.echartStatus)
    },
    handleBigTagContent() {
      this.echartDlgTitle = this.$t('report.tagReportMsg10')
      this.echartStatus = 'tagContent'
      this.$refs.echartDlg.show(this.query, this.echartStatus)
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取数据信息
     * */
    getData() {
      return getDiskScanTagHomepageData(this.query).then(resp => {
        if (resp.data) {
          this.InceptionDiv = true
          this.noData = false
          // 图表解读
          if (resp.data.itemValueMap.explanation) {
            this.chartInterpretationContent = { ...this.chartInterpretationContent, ...resp.data.itemValueMap.explanation }
          } else {
            this.chartInterpretationContent = {
              deptDistribute: '',
              fileLever: '',
              tagContent: '',
              termSensitiveBar: ''
            }
          }
          // // 上方小方块
          // // console.log('resp.data.itemValueMap', JSON.parse(JSON.stringify(resp.data.itemValueMap)))
          this.temp.itemValue.allNum = resp.data.itemValueMap.allViolateTotal
          this.temp.itemValue.tagNum = resp.data.itemValueMap.violateTotal
          this.temp.itemValue.dept = resp.data.itemValueMap.deptCount
          this.temp.itemValue.tagLevelNum = resp.data.itemValueMap.maxTrankNum
          this.queryTrank = resp.data.itemValueMap.maxTrank
          // 终端文档标签数量图 柱状图
          this.temp.tagTerminalOption.xAxis.data = resp.data.chartDataObjMap.terminalUserBarChartData.xaxisData
          this.temp.tagTerminalOption.series[0].data = resp.data.chartDataObjMap.terminalUserBarChartData.seriesData

          // 部门饼图
          this.temp.barChartData = resp.data.chartDataObjMap.deptPieChartData.chartData

          // 文档等级统计 饼图
          this.temp.chartsTagLevelDatas = resp.data.chartDataObjMap.trankPieChartData.chartData
          // 文档标签内容统计 柱状图
          this.temp.chartTagContentOption.xAxis.data = resp.data.chartDataObjMap.tagContentBarChartData.xaxisData
          this.temp.chartTagContentOption.series[0].data = resp.data.chartDataObjMap.tagContentBarChartData.seriesData
        } else {
          this.resetTemp()
          this.InceptionDiv = false
          this.noData = true
        }
        this.loadComponent()
      }).then(() => {
        this.triggerResize()
      })
    },
    /**
     * 搜索按钮点击（暂时原型主要是对统计类型变化时页面上显示变化的一个处理）
     * */
    loadReport() {
      // 获取子组件查询条件
      this.getData()
    },
    loadComponent() {
      // 初始页面（点击搜索的时候要回到初始页面）
      // this.InceptionDiv = true
      // 终端文档分析 页面
      this.terminalDocumentDiv = false
      // 部门分析 页面
      this.deptDiv = false
      // 文档等级分析 页面
      this.tagLevelDiv = false
      // 标签内容分析 页面
      this.tagContentDiv = false
      this.showFilePath = [{ id: 0, label: this.$t('report.tagReportMsg18') }]
    },
    /**
     * 获取标签文件个数(点击)
     */
    totalGetTagNumFun() {
      this.$refs.tagFile.show(true)
    },
    scanAllNum() {
      this.$refs.tagFile.show(false)
    },
    /**
     * 部门（点击）
     */
    deptFun() {
      this.$refs.deptDlg.show()
    },
    /**
     * 文档等级最高文件个数（点击）
     */
    tagLevelNum() {
      this.$refs.tagLevelNumDlg.show()
    },
    /**
     * 打印
     * */
    handlePrint() {

    },
    /**
     * 导出
     * */
    handleExport() {

    },
    /**
     * 终端文档 分析点击
     * */
    deptTerminalDocumentClick() {
      // 初始 页面
      this.InceptionDiv = false
      // 终端文档分析 页面
      this.terminalDocumentDiv = true
      // 部门分析 页面
      this.deptDiv = false
      // 文档等级分析 页面
      this.tagLevelDiv = false
      // 标签内容分析 页面
      this.tagContentDiv = false
      this.showFilePath.push({ id: 4, label: this.$t('report.tagReportMsg14') })
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    deptTitleClick() {
      // 初始 页面
      this.InceptionDiv = false
      // 终端文档分析 页面
      this.terminalDocumentDiv = false
      // 部门分析 页面
      this.deptDiv = true
      // 文档等级分析 页面
      this.tagLevelDiv = false
      // 标签内容分析 页面
      this.tagContentDiv = false
      this.showFilePath.push({ id: 2, label: this.$t('report.tagReportMsg15') })
    },
    /**
     * 文档等级 分析点击
     * */
    tagLevelTitleClick() {
      // 初始 页面
      this.InceptionDiv = false
      // 终端文档分析 页面
      this.terminalDocumentDiv = false
      // 部门分析 页面
      this.deptDiv = false
      // 文档等级分析 页面
      this.tagLevelDiv = true
      // 标签内容分析 页面
      this.tagContentDiv = false
      this.showFilePath.push({ id: 1, label: this.$t('report.tagReportMsg16') })
    },
    /**
     * 文档标签内容分析 点击
     * */
    notOfflineTitleClick() {
      // 初始 页面
      this.InceptionDiv = false
      // 终端文档分析 页面
      this.terminalDocumentDiv = false
      // 部门分析 页面
      this.deptDiv = false
      // 文档等级分析 页面
      this.tagLevelDiv = false
      // 标签内容分析 页面
      this.tagContentDiv = true
      this.showFilePath.push({ id: 3, label: this.$t('report.tagReportMsg17') })
    },
    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        // 初始 页面
        this.InceptionDiv = true
        // 终端文档分析 页面
        this.terminalDocumentDiv = false
        // 部门分析 页面
        this.deptDiv = false
        // 文档等级分析 页面
        this.tagLevelDiv = false
        // 标签内容分析 页面
        this.tagContentDiv = false
        this.showFilePath = [{ id: 0, label: this.$t('report.tagReportMsg18') }]
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .sensitiveness-query >>>.el-form-item {
    margin-bottom: 0;
  }
</style>
