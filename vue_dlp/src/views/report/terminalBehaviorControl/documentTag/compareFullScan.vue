<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <div>
        <!--上方搜索条件-->
        <div style="float: right">
          <span class="query-report-span">
            <span>
              {{ $t('report.tagReportMsg19') }}：
              <el-select v-model="query.vo1.guid" @change="(key) =>taskChange(1, key)">
                <el-option v-for="option in guidOptions" :key="option.key" :value="option.key" :label="option.name"/>
              </el-select>
            </span>
            <span>
              {{ $t('report.terminalDept') }}：
              <tree-select
                ref="parentDept"
                node-key="id"
                leaf-key="terminal"
                :local-search="false"
                :checked-keys="checkedKeys1"
                collapse-tags
                is-filter
                multiple
                check-strictly
                :width="250"
                style="display: inline-block; width: 250px"
                @change="(key, nodeData) => parentIdSelectChange1(key, nodeData, query.vo1)"
              />
            </span>
            <span>
              {{ $t('report.tagReportMsg20') }}：
              <el-select v-model="query.vo2.guid" @change="(key) =>taskChange(2, key)">
                <el-option v-for="option in guidOptions" :key="option.key" :value="option.key" :label="option.name"/>
              </el-select>
            </span>
            <span>
              {{ $t('report.terminalDept') }}：
              <tree-select
                ref="parentDept"
                node-key="id"
                leaf-key="terminal"
                :local-search="false"
                :checked-keys="checkedKeys2"
                collapse-tags
                is-filter
                multiple
                check-strictly
                :width="250"
                style="display: inline-block; width: 250px"
                @change="(key, nodeData) => parentIdSelectChange2(key, nodeData, query.vo2)"
              />
            </span>
          </span>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
        </div>
      </div>
      <!--      <div v-if="noData" class="detailsDiv">-->
      <!--        <div class="noData">{{ this.$t('text.noData') }}...</div>-->
      <!--      </div>-->
      <!--分析初始页面-->
      <div class="detailsDiv">
        <el-collapse @change="handleChange">
          <el-collapse-item :title="title" name="1">
            <el-row :gutter="32">
              <el-col :xs="24" :sm="24" :lg="12">
                <el-timeline>
                  <el-timeline-item
                    v-for="(activity, index) in temp.activities1"
                    :key="index"
                    :hide-timestamp="true"
                    :icon="activity.icon"
                    :type="activity.type"
                    :color="activity.color"
                    :size="activity.size"
                  >
                    <div class="title-text" :title="activity.content">
                      {{ activity.content }}
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </el-col>
              <el-col :xs="24" :sm="24" :lg="12">
                <el-timeline>
                  <el-timeline-item
                    v-for="(activity, index) in temp.activities2"
                    :key="index"
                    :hide-timestamp="true"
                    :icon="activity.icon"
                    :type="activity.type"
                    :color="activity.color"
                    :size="activity.size"
                  >
                    <div class="title-text" :title="activity.content">
                      {{ activity.content }}
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
        <!--标签信息对比统计   文档等级数量对比统计-->
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="12">
            <bar-chart
              :chart-data="temp.barChartTagInfoData"
              :chart-option="temp.barChartTagInfoOption"
              style="width: 100%;height: 255px"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :lg="12">
            <bar-chart
              :chart-data="temp.barChartTagLevelData"
              :chart-option="temp.barChartTagLevelOption"
              style="width: 100%;height: 255px"
            />
          </el-col>
        </el-row>
        <!--标签内容数量对比统计  终端标签文件数量对比统计 -->
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="12">
            <bar-chart
              :chart-data="temp.barChartTagContentData"
              :chart-option="temp.barChartTagContentOption"
              style="width: 100%;height: 255px"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :lg="12">
            <bar-chart
              :chart-data="temp.barChartTagDocumentData"
              :chart-option="temp.barChartTagDocumentOption"
              style="width: 100%;height: 255px"
            />
          </el-col>
        </el-row>
        <span class="info-title">{{ $t('report.tagReportMsg22') }}</span>
        <el-button v-if="showDetail" size="mini" type="primary" style="margin-bottom: 5px" @click="showDetail = false">
          <i class="el-icon-back"></i>
        </el-button>
        <el-table
          v-show="!showDetail"
          border
          :data="temp.rowDataMSg"
        >
          <!-- 定义每一列，并使用 scoped slot 来自定义单元格内容 -->
          <el-table-column
            v-for="(column, index) in temp.colModelMsg"
            :key="index"
            :prop="column.prop"
            :width="column.width"
            :label="column.label"
          >
            <template slot-scope="scope">
              <!-- 判断:如果是最后一行，则最后一行根据条件显示对应图标 -->
              <span v-if="isLastRow(scope.$index)">
                <!-- 判断第二行减第一行是否大于0，显示不同图标 -->
                <svg-icon v-if="isLastIcon(scope.$index, scope.column.property)>0" icon-class="rise"/>
                <svg-icon v-if="isLastIcon(scope.$index, scope.column.property)<0" icon-class="descend"/>
                <i v-if="isLastIcon(scope.$index, scope.column.property)===0 && scope.column.property !== 'guidName'" class="el-icon-minus"></i>
                <span v-if="scope.column.property === 'guidName'">对比趋势</span>
              </span>
              <!--判断：是否为第一列，第一列添加点击事件-->
              <span v-else-if="isFirstColum(scope.column.property)" class="el-button--text" style="cursor: pointer;" @click="handleRowClick(scope.row)">{{ scope.row[column.prop] }}</span>
              <!--其余情况正常显示列表信息即可-->
              <span v-else>{{ scope.row[column.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>

        <grid-table
          v-show="showDetail"
          ref="detailList"
          row-key="id"
          style="height: 143px"
          :min-height="143"
          :show-pager="true"
          :multi-select="false"
          :col-model="colModelMsgDetails"
          :row-data-api="getDetailData"
        />
      </div>
    </div>

  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart'
import { getDiskScanGuidOptionsPro, getDiskScanTagCompareHomepageData, getDiskScanTagDetailPage, getTaskListByGuid } from '@/api/report/baseReport/documentTag/tag';

export default {
  name: 'DocumentTagCompareFullScan',
  components: { BarChart },
  props: {
  },
  data() {
    return {
      detailQuery: {},
      DefaultDetailQuery: {
        page: 1,
        limit: 20,
        guid: '',
        batchNo: ''
      },
      guidOptions: [],
      opTypeOptions: {
        5: this.$t('pages.diskScan_Msg3'), 3: this.$t('pages.diskScan_Msg4'), 1: this.$t('pages.fullDecryption'), 2: this.$t('pages.globalEncryption'),
        7: this.$t('pages.diskScan_Msg47'), 8: this.$t('pages.diskScan_Msg48'), 9: this.$t('pages.diskScan_Msg49')/*, 4: '全盘扫描敏感文件并解密非敏感文件，'*/
      },
      tempQuery: {},
      // 搜查查询
      query: {
        vo1: {
          guid: '',
          batchNo: '',
          objectType: 1,
          objectIds: null,
          groupIds: null
        },
        vo2: {
          guid: '',
          batchNo: '',
          objectType: 1,
          objectIds: null,
          groupIds: null
        }
      },
      title: this.$t('report.tagReportMsg21') + '▼',
      checkedKeys1: [],
      checkedKeys2: [],
      // 暂无数据
      noData: true,
      showDetail: false,
      colModelMsgDetails: [
        { prop: 'create_time', label: 'operateTime' },
        { prop: 'terminal_name', label: 'terminalName' },
        { prop: 'term_group_name', label: 'deptName' },
        { prop: 'trank', label: 'labelGrade', formatter: this.trankFormatter },
        { prop: 'tag_content', label: 'labelContent', formatter: this.tagContentFormatter },
        { prop: 'file_name', label: 'fileName' },
        { prop: 'file_path', label: 'filePath' },
        { prop: 'op_type', label: 'operateType', formatter: this.opTypeFormatter },
        { prop: 'encry_flag', label: 'encryFlag', formatter: this.encryFormatter }
      ],
      temp: {},
      defaultTemp: {
        activities1: [
          { content: this.$t('report.tagReportMsg19') + '：', size: 'large', color: '#800000', icon: 'el-icon-price-tag' },
          { content: this.$t('pages.stgName') + '：', size: 'large', color: '#cc66cc', icon: 'el-icon-setting' },
          { content: this.$t('pages.scanStartTime') + '：', size: 'large', color: '#006699', icon: 'el-icon-time' },
          { content: this.$t('pages.scanDir') + '：', size: 'large', color: '#ff9900', icon: 'el-icon-folder' },
          { content: this.$t('pages.exceptionDirectory') + '：', size: 'large', color: '#9999ff', icon: 'el-icon-folder-opened' },
          { content: this.$t('pages.suffixes') + '：', size: 'large', color: '#6699cc', icon: 'el-icon-mouse' },
          { content: this.$t('pages.diskScan_Msg15') + '：', size: 'large', color: '#9933ff', icon: 'el-icon-coordinate' }
        ],
        activities2: [
          { content: this.$t('report.tagReportMsg20') + '：', size: 'large', color: '#800000', icon: 'el-icon-price-tag' },
          { content: this.$t('pages.stgName') + '：', size: 'large', color: '#cc66cc', icon: 'el-icon-setting' },
          { content: this.$t('pages.scanStartTime') + '：', size: 'large', color: '#006699', icon: 'el-icon-time' },
          { content: this.$t('pages.scanDir') + '：', size: 'large', color: '#ff9900', icon: 'el-icon-folder' },
          { content: this.$t('pages.exceptionDirectory') + '：', size: 'large', color: '#9999ff', icon: 'el-icon-folder-opened' },
          { content: this.$t('pages.suffixes') + '：', size: 'large', color: '#6699cc', icon: 'el-icon-mouse' },
          { content: this.$t('pages.diskScan_Msg15') + '：', size: 'large', color: '#9933ff', icon: 'el-icon-coordinate' }
        ],
        // 标签信息对比统计 柱状图
        barChartTagInfoData: [],
        barChartTagInfoOption: {
          title: {
            'text': this.$t('report.tagReportMsg23'),
            'subtext': ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: this.$t('pages.labelInfo'),
            data: [],
            axisLabel: {
              rotate: 20, // 标签倾斜角度
              interval: 0, // 强制显示所有标签，避免自动隐藏,
              // 强制标签显示在坐标轴下方
              position: 'bottom',
              align: 'right',
              // 文字顶部对齐(避免向上漂移)
              verticalAlign: 'top'
            }
          },
          series: [
            {
              name: this.$t('report.tagReportMsg19'),
              type: 'bar',
              data: []
            },
            {
              name: this.$t('report.tagReportMsg20'),
              type: 'bar',
              data: []
            }
          ]
        },
        // 文档等级数量对比统计 柱状图
        barChartTagLevelData: [],
        barChartTagLevelOption: {
          title: {
            'text': this.$t('report.tagReportMsg24'),
            'subtext': ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: this.$t('table.labelGrade'),
            data: [],
            axisLabel: {
              rotate: 20, // 标签倾斜角度
              interval: 0, // 强制显示所有标签，避免自动隐藏
              // 强制标签显示在坐标轴下方
              position: 'bottom',
              align: 'right',
              // 文字顶部对齐(避免向上漂移)
              verticalAlign: 'top'
            }
          },
          series: [
            {
              name: this.$t('report.tagReportMsg19'),
              type: 'bar',
              data: []
            },
            {
              name: this.$t('report.tagReportMsg20'),
              type: 'bar',
              data: []
            }
          ]
        },
        // 标签内容数量对比统计 柱状图
        barChartTagContentData: [],
        barChartTagContentOption: {
          title: {
            'text': this.$t('report.tagReportMsg25'),
            'subtext': this.$t('pages.topN', { num: 10 })
          },
          toolbox: {
            show: false
          },
          grid: {
            width: '84%',
            left: '5%',
            containLabel: true
          },
          xAxis: {
            name: this.$t('report.tagContentLabel'),
            data: [],
            axisLabel: {
              rotate: 20, // 标签倾斜角度
              interval: 0, // 强制显示所有标签，避免自动隐藏
              // 强制标签显示在坐标轴下方
              position: 'bottom',
              align: 'right',
              // 文字顶部对齐(避免向上漂移)
              verticalAlign: 'top'
            }
          },
          series: [
            {
              name: this.$t('report.tagReportMsg19'),
              type: 'bar',
              data: []
            },
            {
              name: this.$t('report.tagReportMsg20'),
              type: 'bar',
              data: []
            }
          ]
        },
        // 终端标签文件数量对比统计 柱状图
        barChartTagDocumentData: [],
        barChartTagDocumentOption: {
          title: {
            'text': this.$t('report.tagReportMsg26'),
            'subtext': this.$t('pages.topN', { num: 10 })
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: this.$t('report.tagReportMsg27'),
            data: [],
            axisLabel: {
              rotate: 20, // 标签倾斜角度
              interval: 0, // 强制显示所有标签，避免自动隐藏
              // 强制标签显示在坐标轴下方
              position: 'bottom',
              align: 'right',
              // 文字顶部对齐(避免向上漂移)
              verticalAlign: 'top'
            }
          },
          series: [
            {
              name: this.$t('report.tagReportMsg19'),
              type: 'bar',
              data: []
            },
            {
              name: this.$t('report.tagReportMsg20'),
              type: 'bar',
              data: []
            }
          ]
        },
        colModelMsg: [
          { prop: 'guidName', label: '', width: '360' },
          { prop: 'termNum', label: this.$t('table.terminalNumber') },
          { prop: 'fileNumSum', label: this.$t('table.tagFileAllNum') },
          { prop: 'trunkNum', label: this.$t('table.documentLevelNumber') },
          { prop: 'contentNum', label: this.$t('table.labelContentCount') }
        ],
        rowDataMSg: [
          // { guid: '任务编号1', termNum: 1, fileNumSum: 2, trunkNum: 4, contentNum: 8 },
          // { guid: '任务编号2', termNum: 2, fileNumSum: 3, trunkNum: 4, contentNum: 7 },
          // { guid: '', termNum: '', fileNumSum: '', trunkNum: '', contentNum: '' } // 空数据作为占位符
        ]
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
    this.getOptions()
  },
  activated() {},
  methods: {
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
    },
    /**
     * 任务编号发生变化时，查询条件赋值
     * */
    taskChange(type, key) {
      const selectedObj = this.getGuidValueObj(key);
      if (type === 1) {
        this.query.vo1.guid = selectedObj.key
        this.query.vo1.batchNo = selectedObj.id
      } else {
        this.query.vo2.guid = selectedObj.key
        this.query.vo2.batchNo = selectedObj.id
      }
    },
    /**
     * 获取全盘扫描任务下拉列表
     * */
    getOptions() {
      getDiskScanGuidOptionsPro({ taskType: 8 }).then(res => {
        this.guidOptions = res.data
        if (this.guidOptions.length > 0) {
          this.query.vo1.guid = this.guidOptions[0].key
          this.query.vo1.batchNo = this.guidOptions[0].id
          // 如果大于两个任务，这第二个对比对象选择第二个任务
          if (this.guidOptions.length > 1) {
            this.query.vo2.guid = this.guidOptions[1].key
          } else {
            this.query.vo2.guid = this.guidOptions[0].key
          }
          this.query.vo2.batchNo = this.guidOptions[0].id
          this.loadReport()
        } else {
          // this.noData = true
        }
      })
    },
    /**
     * 获取下拉框选中的guid的对象
     * @param guid 当前选中guid
     * */
    getGuidValueObj(guid) {
      return this.guidOptions.find(item => item.key === guid)
    },
    /**
     * 获取下拉框选中的guid的名称
     * @param guid 当前选中guid
     * */
    getGuidName(guid) {
      const selectedObj = this.guidOptions.find(item => item.key === guid)
      return selectedObj.name
    },
    /**
     * 获取报表数据
     * */
    getData() {
      this.resetTemp()
      // 获取任务1策略数据
      getTaskListByGuid({ guid: this.query.vo1.guid }).then(res => {
        this.temp.activities1[0].content = this.temp.activities1[0].content + this.getGuidName(this.query.vo1.guid)
        if (res.data) {
          const data = res.data
          this.temp.activities1[1].content = this.temp.activities1[1].content + (data.name === '' ? '无' : data.name)
          this.temp.activities1[2].content = this.temp.activities1[2].content + (data.startTime === '' ? '无' : data.startTime)
          this.temp.activities1[3].content = this.temp.activities1[3].content + (data.scanDir === '' ? '无' : data.scanDir)
          this.temp.activities1[4].content = this.temp.activities1[4].content + (data.exceptDir === '' ? '无' : data.exceptDir)
          this.temp.activities1[5].content = this.temp.activities1[5].content + (data.suffix === '' ? '无' : data.suffix)
          this.temp.activities1[6].content = this.temp.activities1[6].content + (data.penetrateSuffix === '' ? '无' : data.penetrateSuffix)
        }
      })
      // 获取任务2策略数据
      getTaskListByGuid({ guid: this.query.vo2.guid }).then(res => {
        this.temp.activities2[0].content = this.temp.activities2[0].content + this.getGuidName(this.query.vo2.guid)
        if (res.data) {
          const data = res.data
          this.temp.activities2[1].content = this.temp.activities2[1].content + (data.name === '' ? '无' : data.name)
          this.temp.activities2[2].content = this.temp.activities2[2].content + (data.startTime === '' ? '无' : data.startTime)
          this.temp.activities2[3].content = this.temp.activities2[3].content + (data.scanDir === '' ? '无' : data.scanDir)
          this.temp.activities2[4].content = this.temp.activities2[4].content + (data.exceptDir === '' ? '无' : data.exceptDir)
          this.temp.activities2[5].content = this.temp.activities2[5].content + (data.suffix === '' ? '无' : data.suffix)
          this.temp.activities2[6].content = this.temp.activities2[6].content + (data.penetrateSuffix === '' ? '无' : data.penetrateSuffix)
        }
      })
      // 除策略外数据获取
      getDiskScanTagCompareHomepageData(this.query).then(res => {
        const taskNum1 = this.getGuidName(this.query.vo1.guid)
        const taskGuid1 = this.query.vo1.guid
        const taskNum2 = this.getGuidName(this.query.vo2.guid)
        const taskGuid2 = this.query.vo2.guid
        // 1、echarts图表处理
        if (res.data.chartDataObjMap) {
          const chartData = res.data.chartDataObjMap
          // 标签信息对比统计
          this.temp.barChartTagInfoOption.xAxis.data = chartData.tagBarCharData.xaxisData
          this.temp.barChartTagInfoOption.series[0].name = this.$t('report.tagReportMsg19') + '：' + taskNum1
          this.temp.barChartTagInfoOption.series[0].data = chartData.tagBarCharData.seriesData.map(subArray => subArray[0])
          this.temp.barChartTagInfoOption.series[1].name = this.$t('report.tagReportMsg20') + '：' + taskNum2
          this.temp.barChartTagInfoOption.series[1].data = chartData.tagBarCharData.seriesData.map(subArray => subArray[1])
          // 文档等级数量对比统计
          this.temp.barChartTagLevelOption.xAxis.data = chartData.trankBarChartData.xaxisData
          this.temp.barChartTagLevelOption.series[0].name = this.$t('report.tagReportMsg19') + '：' + taskNum1
          this.temp.barChartTagLevelOption.series[0].data = chartData.trankBarChartData.seriesData.map(subArray => subArray[0])
          this.temp.barChartTagLevelOption.series[1].name = this.$t('report.tagReportMsg20') + '：' + taskNum2
          this.temp.barChartTagLevelOption.series[1].data = chartData.trankBarChartData.seriesData.map(subArray => subArray[1])
          // 标签内容数量对比统计
          this.temp.barChartTagContentOption.xAxis.data = chartData.contentBarChartData.xaxisData
          this.temp.barChartTagContentOption.series[0].name = this.$t('report.tagReportMsg19') + '：' + taskNum1
          this.temp.barChartTagContentOption.series[0].data = chartData.contentBarChartData.seriesData.map(subArray => subArray[0])
          this.temp.barChartTagContentOption.series[1].name = this.$t('report.tagReportMsg20') + '：' + taskNum2
          this.temp.barChartTagContentOption.series[1].data = chartData.contentBarChartData.seriesData.map(subArray => subArray[1])
          // 终端标签文件数量对比统计
          this.temp.barChartTagDocumentOption.xAxis.data = chartData.termBarChartData.xaxisData
          this.temp.barChartTagDocumentOption.series[0].name = this.$t('report.tagReportMsg19') + '：' + taskNum1
          this.temp.barChartTagDocumentOption.series[0].data = chartData.termBarChartData.seriesData.map(subArray => subArray[0])
          this.temp.barChartTagDocumentOption.series[1].name = this.$t('report.tagReportMsg20') + '：' + taskNum2
          this.temp.barChartTagDocumentOption.series[1].data = chartData.termBarChartData.seriesData.map(subArray => subArray[1])
        }
        // 2、表格数据处理
        if (res.data.itemValueMap) {
          const tableData = res.data.itemValueMap.tableList
          // 给每个对象添加type字段，并根据需要设置不同的值
          tableData[0].guid = taskGuid1;
          tableData[0].guidName = this.getGuidName(taskGuid1);
          tableData[0].batchNo = this.query.vo1.batchNo;
          tableData[1].guid = taskGuid2;
          tableData[1].guidName = this.getGuidName(taskGuid2);
          tableData[1].batchNo = this.query.vo2.batchNo;
          // 创建第三个对象，key与前面的对象一致，value为空字符串
          const thirdObject = {};
          for (const key in tableData[0]) {
            if (tableData[0].hasOwnProperty(key)) {
              thirdObject[key] = '';
            }
          }
          thirdObject.guid = ''; // 添加type字段，值为空字符串
          // 将第三个对象添加到数组中
          tableData.push(thirdObject);
          this.temp.rowDataMSg = tableData
        }
      })
    },
    getTitle() {
      return this.$t('report.tagReportMsg21')
    },
    handleChange(activeNames) {
      if (activeNames.includes('1')) {
        this.title = this.$t('report.tagReportMsg21') + '▲'
      } else {
        this.title = this.$t('report.tagReportMsg21') + '▼'
      }
    },
    /**
     * 搜索按钮点击
     * */
    loadReport() {
      // 获取子组件查询条件
      this.getData()
    },
    /**
     * 树节点选中发生变化时,获取查询参数--任务1终端部门树
     * */
    parentIdSelectChange1(key, nodeData, query) {
      if (nodeData && nodeData.length > 0) {
        const objectIds = []
        const groupIds = []
        nodeData.forEach(item => {
          if (item.dataType === 'G') {
            groupIds.push(item.dataId)
          } else {
            objectIds.push(item.dataId)
          }
        })
        this.query.vo1.objectIds = objectIds.join(',')
        this.query.vo1.groupIds = groupIds.join(',')
      } else {
        this.query.vo1.objectIds = null
        this.query.vo1.groupIds = null
      }
    },
    /**
     * 树节点选中发生变化时,获取查询参数--任务2终端部门树
     * */
    parentIdSelectChange2(key, nodeData, query) {
      if (nodeData && nodeData.length > 0) {
        const objectIds = []
        const groupIds = []
        nodeData.forEach(item => {
          if (item.dataType === 'G') {
            groupIds.push(item.dataId)
          } else {
            objectIds.push(item.dataId)
          }
        })
        this.query.vo2.objectIds = objectIds.join(',')
        this.query.vo2.groupIds = groupIds.join(',')
      } else {
        this.query.vo2.objectIds = null
        this.query.vo2.groupIds = null
      }
    },
    trankFormatter(row) {
      if (row.result != 0) {
        return null
      }
      return row.trank
    },
    opTypeFormatter(row, data) {
      row.opType = row.opType + ''
      return this.opTypeOptions[data]
    },
    encryFormatter(row, data) {
      if (data === 1) {
        return this.$t('pages.cipherText')
      } else {
        return this.$t('pages.plainText')
      }
    },
    tagContentFormatter(data) {
      if (data.clear_flag == 2) {
        return this.$t('report.tagReportMsg33')
      }
      return data.tag_content.replace(/;+$/, '')
    },
    /**
     * 获取表格下钻信息
     * */
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      searchQuery.searchCount = true
      return getDiskScanTagDetailPage(searchQuery)
    },
    /**
     * 对比信息表格：第一列点击事件（点击时传递参数，获取详细信息）
     * */
    handleRowClick(row) {
      this.showDetail = true
      this.detailQuery.guid = row.guid
      this.detailQuery.batchNo = row.batchNo
      this.$nextTick(() => {
        this.$refs.detailList.execRowDataApi(this.detailQuery)
      })
    },
    /**
     * 对比信息表格：判断是不是第一列数据（如果是第一列，添加点击事件，修改第一列样式）
     * */
    isFirstColum(column) {
      if (column !== 'guidName') {
        return false
      } else {
        return true
      }
    },
    /**
     * 对比信息表格：判断是否最后一行（如果是最后一行，则显示图标）
     * */
    isLastRow(index) {
      return index === this.temp.rowDataMSg.length - 1;
    },
    /**
     * 对比信息表格：计算：第2行-第1行的值
     * */
    isLastIcon(index, column) {
      let diff = 0
      if (index === 2) {
        // 不是第一列任务编号的时候在比较数据大小
        if (column !== 'guidName') {
          const value1 = this.temp.rowDataMSg[0][column];
          const value2 = this.temp.rowDataMSg[1][column];
          diff = value2 - value1;
        }
      }
      return diff;
    }
  }
}
</script>

<style lang='scss' scoped>
  .info-title {
    display: inline-block;
    line-height: 30px;
    color: #008acd;
    font-size: 13px;
  }
  .title-text {
    width: 500px; /* 设置一个固定宽度 */
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏超出宽度的部分 */
    text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
    display: inline-block; /* 让容器根据内容大小调整 */
  }
  ::v-deep .el-collapse-item__header {
    font-size: 16px !important;
  }
</style>
