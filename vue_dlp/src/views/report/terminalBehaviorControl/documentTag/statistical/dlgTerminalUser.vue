<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="dialogStatus === 'user' ? this.$t('report.operatorCount') : this.$t('report.terminalNumber')"
      :fullscreen="fullscreen"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="squareList"
        :stripe="false"
        :col-model="colModel"
        :row-data-api="getData"
        :autoload="false"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDocTagAddTypeTerminalUserData } from '@/api/report/baseReport/documentTag/tag';

export default {
  name: 'DlgTerminal',
  components: { },
  props: {
    // 状态，区分终端还是操作员
    dialogStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      // 标签类型
      tagTypeMap: {
        0: this.$t('pages.addTypeMsg'),
        2: this.$t('pages.addTypeMsg2'),
        3: this.$t('pages.addTypeMsg3')
      },
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      query: { // 查询条件
        page: 1,
        limit: 20,
        groupType: 0
      },
      colModel: [
        { prop: 'terminal_user_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'terminal_user_name', label: 'user', hidden: () => this.colHidden(['terminal']) },
        { prop: 'file_num_sum', label: 'numberOfTags', sort: true },
        { prop: 'addTypeStr', label: 'tagType', width: '150', formatter: (row, data) => { return data.replace(/^,|,$/g, '').split(',').map(item => this.tagTypeMap[item]).join(',') } }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['squareList']
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  created() {
  },
  methods: {
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    /**
     * 获取数据信息
     * */
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listDocTagAddTypeTerminalUserData(searchQuery)
    },
    /**
       * 显示弹框
       */
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.query.page = 1
        this.gridTable.execRowDataApi(this.query);
      })
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

