<template>
  <!--数量趋势-->
  <div style="height: 100%">
    <!--数量趋势--上方折线图-->
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="24">
        <line-chart
          :chart-data="temp.lineChartData"
          :chart-option="temp.lineOption"
          style="width: 100%;height: 300px"
        />
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <!--标签分类饼图-->
      <el-col :xs="24" :sm="24" :lg="12">
        <pie-chart
          :chart-data="temp.tagTypeDatas"
          :chart-option="temp.tagTypOption"
          style="width: 100%;height: 300px"
        />
      </el-col>
      <!--文档等级饼图-->
      <el-col :xs="24" :sm="24" :lg="12">
        <bar-chart
          :chart-data="temp.barChartBusinessTypeData"
          :chart-option="temp.barChartBusinessTypeOption"
          style="width: 100%;height: 300px"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import PieChart from '@/components/ECharts/PieChart'
import BarChart from '@/components/ECharts/BarChart';
import { getDocTagAddTypeTrendAnalysisData } from '@/api/report/baseReport/documentTag/tag';

export default {
  name: 'OnOffTrend',
  components: { LineChart, PieChart, BarChart },
  data() {
    return {
      temp: {},
      defaultTemp: {
        lineChartData: [],
        lineOption: {
          title: {
            text: this.$t('report.labelTrend')
          },
          legend: {
            type: 'plain',
            top: 40,
            data: []
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          dataZoom: [
            {
              textStyle: { color: '#027AB6' },
              height: 20, // 时间滚动条的高度
              type: 'slider', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0,  // 默认开始位置（百分比）
              end: 100  // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          series: []
        },
        // 饼图 操作员、终端 加标签数量图
        tagTypeDatas: [],
        tagTypOption: {
          title: {
            text: this.$t('report.labelTypeStatistics'),
            left: 'center',
            subtext: ''
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 文档文档等级 柱状图
        barChartBusinessTypeData: [],
        barChartBusinessTypeOption: {
          title: { text: this.$t('table.labelGrade'), subtext: '' },
          toolbox: { show: false },
          xAxis: {
            name: this.$t('table.labelGrade'),
            data: []
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  created() {
    this.getData()
    this.resetTemp()
  },
  activated() {},
  methods: {
    resetTemp() {
      this.temp = { ...this.defaultTemp }
    },
    /**
     * 获取数据信息
     * */
    getData() {
      getDocTagAddTypeTrendAnalysisData(this.$parent.tempQuery).then(resp => {
        if (resp.data) {
          const response = resp.data
          // 后台返回的加标签总数为add_type_all，需要单独处理一下在显示
          const processArray = (arr) => {
            arr.forEach(item => {
              if (item.name === 'add_type_all') {
                // 直接修改原对象的name属性
                item.name = '加标签总数';
              }
            });
            return arr;
          };
          // 执行处理
          processArray(resp.data.chartDataObjMap.dateLineCharData.seriesData);
          // 折线图数据
          this.temp.lineOption.series = resp.data.chartDataObjMap.dateLineCharData.seriesData
          this.temp.lineOption.legend.data = resp.data.chartDataObjMap.dateLineCharData.seriesData.map(item => item.name)
          this.temp.lineOption.xAxis.data = resp.data.chartDataObjMap.dateLineCharData.xaxisData

          // 文档等级 柱状图
          this.temp.barChartBusinessTypeData = response.chartDataObjMap.trankBarChartData.chartData
          this.temp.barChartBusinessTypeOption.xAxis.data = response.chartDataObjMap.trankBarChartData.xaxisData
          this.temp.barChartBusinessTypeOption.series[0].data = response.chartDataObjMap.trankBarChartData.seriesData

          // 标签类型 饼图
          this.temp.tagTypeDatas = response.chartDataObjMap.addTypePieChartData.chartData.map((name, index) => ({
            name: name,
            value: response.chartDataObjMap.addTypePieChartData.seriesData[index]
          }))
        } else {
          this.temp = { ...this.defaultTemp }
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
