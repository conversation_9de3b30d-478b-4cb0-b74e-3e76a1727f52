<template>
  <!--部门分析-->
  <div class="data-details sensitiveness-report">
    <div class="data-analysis-table table-parent-size">
      <div class="table-container table-left-size">
        <!--左侧列表查询条件-->
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchLeftForm" label-position="right" :model="query" label-width="80px">
            <FormItem :label="$t('table.tagType')">
              <el-select v-model="query.keyword1" clearable :placeholder="$t('pages.all')" style="width: 244px">
                <el-option v-for="(value, key) in tagTypeMap" :key="key" :label="value" :value="key"></el-option>
              </el-select>
            </FormItem>
            <FormItem :label="$t('pages.deptName')">
              <el-input v-model="query.label" v-trim clearable maxlength="100"/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetLeftQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="handleFilterLeft()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button slot="reference" type="primary" icon="el-icon-search" class="search-icon-left" size="mini"></el-button>
        </el-popover>
        <grid-table
          ref="deptList"
          row-key="id"
          :col-model="colModel"
          :row-data-api="rowDataApi"
          :multi-select="false"
          @currentChange="currentChangeLeft"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--部门分析上方几个小方块-->
      <div class="data-panel">
        <div v-for="(item, key) in temp.deptItemOption" :key="key" class="data-item data-item-details" >
          <div class="icon">
            <svg-icon :icon-class="item.ico"></svg-icon>
          </div>
          <div class="content">
            <label :title="item.label">{{ item.label }}</label>
            <span :title="item.value">{{ item.value }}</span>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 167px); padding: 0 9px;position: relative">
        <!--右侧列表查询条件-->
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchRightForm" label-position="right" :model="detailQuery" label-width="80px">
            <FormItem :label="$t('table.tagType')">
              <el-select v-model="detailQuery.condition.add_type" clearable :placeholder="$t('pages.all')" style="width: 244px">
                <el-option v-for="(value, key) in tagTypeMap" :key="key" :label="value" :value="key"></el-option>
              </el-select>
            </FormItem>
            <FormItem :label="$t('table.labelGrade')">
              <el-input-number v-model="detailQuery.trank" v-trim clearable :min="0" :step="1" :controls="false" :precision="0" class="left-aligned-input"/>
            </FormItem>
            <FormItem :label="$t('table.gradeName')">
              <el-input v-model="detailQuery.trankContent" v-trim clearable/>
            </FormItem>
            <FormItem :label="$t('table.labelContent')">
              <el-input v-model="detailQuery.tagContent" v-trim clearable/>
            </FormItem>
            <FormItem :label="$t('table.fileName')">
              <el-input v-model="detailQuery.likeCondition.file_name" v-trim clearable/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetAdvancedQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="searchBtn()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button v-show="isShowGrid" slot="reference" type="primary" icon="el-icon-search" class="search-icon-right" size="mini"></el-button>
        </el-popover>
        <el-tabs v-model="activeDeptName" @tab-click="tabClick">
          <el-tab-pane :label="$t('route.tagingLog')" name="deptMsg" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                ref="deptDetailList"
                :show="isShowGrid"
                :col-model="colModelDetail"
                :row-data-api="getDetailData"
                :autoload="false"
                :multi-select="false"
                row-key="getRowKey"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('report.labelTypeStatistics')" name="tagTypeStatistics">
            <pie-chart
              v-if="isShowChart1"
              :chart-data="temp.tagTypeDatas"
              :chart-option="temp.tagTypOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('report.labelLevelStatistics')" name="tagLevelStatistics">
            <bar-chart
              v-if="isShowChart2"
              :chart-data="temp.barChartBusinessTypeData"
              :chart-option="temp.barChartBusinessTypeOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('report.tagReportMsg17')" name="tagContentStatistics">
            <bar-chart
              v-if="isShowChart3"
              :chart-data="temp.tagContentTypeBarData"
              :chart-option="temp.tagContentTypeBarOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from '@/components/ECharts/PieChart';
import BarChart from '@/components/ECharts/BarChart';
import { listDocTagAddTypeDeptData, getDocTagAddTypeDeptAnalysisData, getTagLogDetail } from '@/api/report/baseReport/documentTag/tag';
import moment from 'moment/moment';

export default {
  name: 'AnalysisDept',
  components: { PieChart, BarChart },
  props: {
    // 状态，区分终端还是操作员
    dialogStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      // 标签类型
      tagTypeMap: {
        0: this.$t('pages.addTypeMsg'),
        2: this.$t('pages.addTypeMsg2'),
        3: this.$t('pages.addTypeMsg3')
      },
      analysisQuery: {
        groupType: 1,
        labelValue: '',
        label: ''
      },
      query: {},
      DefaultQuery: {
        page: 1,
        limit: 20,
        groupType: 1,
        label: undefined,
        keyword1: undefined
      },
      detailQuery: {
      },
      DefaultDetailQuery: {
        page: 1,
        groupType: 1,
        limit: 20,
        detailTable: 'dwd_add_document_tag_log',
        notNullFields: ['add_type'],
        filterGroup: false,
        objectNames: [],
        condition: { add_type: undefined },
        trank: undefined,
        trankContent: null,
        tagContent: null,
        likeCondition: {
          file_name: undefined
        }
      },
      // 左侧列表
      colModel: [
        { prop: 'group_name', label: 'deptName' },
        { prop: 'file_num_sum', label: 'numberOfTags', sort: true },
        { prop: 'addTypeStr', label: 'tagType', width: '140', formatter: (row, data) => { return data.replace(/^,|,$/g, '').split(',').map(item => this.tagTypeMap[item]).join(',') } }
      ],
      // 右侧部门分析
      activeDeptName: 'deptMsg',
      isShowGrid: true,
      isShowGridInfo: false,
      isShowChart1: false,
      isShowChart2: false,
      isShowChart3: false,
      // 右侧部门风险信息
      colModelDetail: [
        { prop: 'create_time', label: 'identificationTime', width: '135' },
        { prop: 'terminal_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'user_name', label: 'userName', hidden: () => this.colHidden(['terminal']) },
        { prop: 'file_name', label: 'fileName', width: '120' },
        { prop: 'file_path', label: 'filePath', width: '120' },
        { prop: 'trank', label: 'labelGrade', formatter: this.trankFormatter },
        { prop: 'trank_content', label: 'gradeName', formatter: this.tagLevelFormatter },
        { prop: 'tag_content', label: 'labelContent', formatter: this.tagContentFormatter },
        { prop: 'add_type', label: 'tagType', width: '100', formatter: this.addTypeFormatter },
        { prop: 'process_name', label: 'processName1' }
      ],
      temp: {},
      defaultTemp: {
        // 右侧上方小标题
        deptName: '',
        // 部门分析
        deptItemOption: [
          { label: '', value: '', ico: 'algorithm' },
          { label: this.$t('table.numberOfTags'), value: 0, ico: 'ruleGroup' }
        ],
        // 饼图 操作员、终端 加标签数量图
        tagTypeDatas: [],
        tagTypOption: {
          title: {
            text: this.$t('report.labelTypeStatistics'),
            left: 'center',
            subtext: ''
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 文档等级 柱状图
        barChartBusinessTypeData: [],
        barChartBusinessTypeOption: {
          title: {
            text: this.$t('report.tagReportMsg5'),
            subtext: ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: this.$t('table.labelGrade'),
            data: []
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        tagContentTypeBarData: [],
        tagContentTypeBarOption: {
          title: {
            'text': this.$t('report.tagReportMsg29'),
            'subtext': ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: this.$t('report.tagContentLabel'),
            data: [],
            axisLabel: {
              rotate: 20, // 标签倾斜角度
              interval: 0, // 强制显示所有标签，避免自动隐藏
              // 强制标签显示在坐标轴下方
              position: 'bottom',
              align: 'right',
              // 文字顶部对齐(避免向上漂移)
              verticalAlign: 'top'
            }
          },
          series: [
          ]
        }
      }
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
    this.query = Object.assign({}, this.DefaultQuery)
  },
  async mounted() {
    await this.$refs['deptList'].execRowDataApi()
    const tableData = this.$refs['deptList'].getDatas()
    // 如果左侧列表长度不为0，初始化页面时显示第一条数据的详情
    if (tableData[0]) {
      this.currentChangeLeft(tableData[0])
    }
  },
  activated() {},
  methods: {
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    },
    trankFormatter(row) {
      if (row.clear_flag == 1) {
        return null
      }
      return row.trank
    },
    /**
     * 文档等级
     * */
    tagLevelFormatter(row, data) {
      if (row.clear_flag == 1) {
        return this.$t('pages.noSignInfo')
      }
      return row.trank_content
    },
    tagContentFormatter(data) {
      if (data.clear_flag == 1) {
        return this.$t('pages.noSignInfo')
      }
      if (data.clear_flag == 2) {
        return this.$t('report.tagReportMsg33')
      }
      return data.tag_content.replace(/;+$/, '')
    },
    /**
     * 标签类型
     * */
    addTypeFormatter(row, data) {
      return this.tagTypeMap[data]
    },
    /**
     *左边表格重置
     * */
    resetLeftQuery() {
      this.query.label = undefined
      this.query.keyword1 = undefined
    },
    /**
     *左边表格搜索
     * */
    async handleFilterLeft() {
      this.query.page = 1
      // console.log('query', this.query)
      await this.$refs['deptList'].execRowDataApi(this.query)
      const tableData = this.$refs['deptList'].getDatas()
      if (tableData[0]) {
        this.currentChangeLeft(tableData[0])
      } else {
        this.resetTemp()
        this.detailQuery.objectNames.splice(0)
        this.detailQuery.page = 1
        this.$refs.deptDetailList.clearRowData()
        this.$refs.deptDetailList.clearPageData()
      }
    },
    /**
     * 高级查询，查询操作
     * */
    searchBtn() {
      this.detailQuery.page = 1
      this.$refs['deptDetailList'].execRowDataApi(this.$parent.tempQuery)
    },
    /**
     *重置《高级查询重置按钮》
     * */
    resetAdvancedQuery() {
      this.detailQuery.condition.add_type = undefined
      this.detailQuery.trank = undefined
      this.detailQuery.trankContent = null
      this.detailQuery.tagContent = null
      this.detailQuery.likeCondition.file_name = undefined
    },
    resetTemp() {
      this.temp = { ...this.defaultTemp }
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
    },
    /**
     * 将终端或操作员的表数据添加到表格中
     * */
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listDocTagAddTypeDeptData(searchQuery)
    },
    getDetailData(option) {
      if (this.detailQuery.condition.add_type == '') {
        this.detailQuery.condition.add_type = undefined
      }
      const searchQuery = Object.assign({}, this.detailQuery, this.$parent.tempQuery, option)
      return getTagLogDetail(searchQuery)
    },
    /**
     * 获取左边列表行点击时的信息（右边的详细信息）
     * @param row 当前行信息
     * */
    currentChangeLeft(row) {
      // 小方块
      this.temp.deptItemOption[0].label = row.group_name
      this.temp.deptItemOption[1].value = row.file_num_sum
      // 查询违规明细数据
      this.detailQuery.objectNames.splice(0, 1, row.group_id + row.group_name)
      this.$refs['deptDetailList'].execRowDataApi(this.detailQuery)
      // 查询分析数据
      const groupId = row.group_id
      const groupName = row.group_name
      this.analysisQuery.labelValue = groupId
      this.analysisQuery.label = this.dealUnknown(groupName)
      const searchQuery = Object.assign({}, this.analysisQuery, this.$parent.tempQuery)
      getDocTagAddTypeDeptAnalysisData(searchQuery).then(res => {
        if (res.data) {
          const response = res.data
          const unit = this.dialogStatus === 'user' ? '人' : '台'
          this.temp.deptItemOption[0].value = response.itemValueMap.terminalUserCount + unit
          // 文档等级 柱状图
          this.temp.barChartBusinessTypeData = response.chartDataObjMap.trankBarChartData.chartData
          this.temp.barChartBusinessTypeOption.xAxis.data = response.chartDataObjMap.trankBarChartData.xaxisData
          this.temp.barChartBusinessTypeOption.series[0].data = response.chartDataObjMap.trankBarChartData.seriesData
          // 文档内容柱状图
          this.temp.tagContentTypeBarOption.xAxis.data = response.chartDataObjMap.tagContentTypeBarChartData.xaxisData
          this.temp.tagContentTypeBarOption.series = response.chartDataObjMap.tagContentTypeBarChartData.seriesData

          // 标签类型 饼图
          this.temp.tagTypeDatas = response.chartDataObjMap.addTypePieChartData.chartData.map((name, index) => ({
            name: name,
            value: response.chartDataObjMap.addTypePieChartData.seriesData[index]
          }))
        } else {
          this.temp = { ...this.defaultTemp }
        }
      })
    },
    /**
     * 当值是“未知”时进行特殊处理（因为报表数据库中存储的值是“未知”，所以在查询时需要进行转换）
     * @param value
     * @returns {string|*}
     */
    dealUnknown(value) {
      if (value === this.$t('text.unknown')) {
        return this.$t('text.unknown')
      }
      return value
    },
    /**
     * 根据终端还是操作员控制表格列的显示隐藏
     * */
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    /**
     * tab点击事件，如果不写，切换时图表只有小小一点点
     * @param pane
     * @param event
     */
    tabClick(pane, event) {
      if (pane.name === 'deptMsg') {
        this.isShowGrid = true
        this.isShowChart1 = false
        this.isShowChart2 = false
        this.isShowChart3 = false
      } else if (pane.name === 'tagTypeStatistics') {
        this.isShowGrid = false
        this.isShowChart1 = true
        this.isShowChart2 = false
        this.isShowChart3 = false
      } else if (pane.name === 'tagLevelStatistics') {
        this.isShowGrid = false
        this.isShowChart1 = false
        this.isShowChart2 = true
        this.isShowChart3 = false
      } else {
        this.isShowGrid = false
        this.isShowChart1 = false
        this.isShowChart2 = false
        this.isShowChart3 = true
      }
    }
  }
}
</script>
<style lang='scss' scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left !important;
}
</style>
