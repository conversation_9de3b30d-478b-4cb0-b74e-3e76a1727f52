<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="this.$t('table.numberOfTags')"
      :fullscreen="fullscreen"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="violateDetailTable"
        :stripe="false"
        :col-model="colModel"
        :row-data-api="getData"
        :autoload="false"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDetailPage, checkDetailAble } from '@/api/report/baseReport/issueReport/issue';

export default {
  name: 'DlgTotalViolation',
  components: { },
  props: {
    dialogStatus: { // 状态，终端和操作员小方块共用一个弹框，若某弹框有特定的列，需要根据状态控制列的显示隐藏
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      // 标签类型
      tagTypeMap: {
        0: this.$t('pages.addTypeMsg'),
        2: this.$t('pages.addTypeMsg2'),
        3: this.$t('pages.addTypeMsg3')
      },
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      submitting: false,
      rowData: [],
      query: { // 查询条件
        page: 1,
        detailTable: 'dwd_add_document_tag_log',
        groupType: 0,
        condition: {},
        notNullFields: []
      },
      colModel: [
        { prop: 'create_time', label: 'operateTime' },
        { prop: 'terminal_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'user_name', label: 'userName', hidden: () => this.colHidden(['terminal']) },
        { prop: 'term_group_name', label: 'deptName', hidden: () => this.colHidden(['user']) },
        { prop: 'user_group_name', label: 'deptName', hidden: () => this.colHidden(['terminal']) },
        { prop: 'file_name', label: 'fileName' },
        { prop: 'file_path', label: 'filePath' },
        { prop: 'trank', label: 'labelGrade', formatter: this.trankFormatter },
        { prop: 'trank_content', label: 'gradeName', formatter: this.tagLevelFormatter },
        { prop: 'tag_content', label: 'labelContent', width: '120', formatter: this.tagContentFormatter },
        { prop: 'add_type', label: 'tagType', width: '80', formatter: this.addTypeFormatter }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['violateDetailTable']
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  created() {
  },
  methods: {
    tagContentFormatter(data) {
      if (data.clear_flag == 1) {
        return this.$t('pages.noSignInfo')
      }
      if (data.clear_flag == 2) {
        return this.$t('report.tagReportMsg33')
      }
      return data.tag_content.replace(/;+$/, '')
    },
    /**
     * 获取数据信息
     * */
    getData(option) {
      // const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      const searchQuery = Object.assign({}, this.$parent.tempQuery, this.query, option)
      return getDetailPage(searchQuery)
    },
    /**
       * 显示弹框
       */
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        checkDetailAble(this.$parent.tempQuery).then(() => {
          if (this.$refs['violateDetailTable']) {
            this.query.page = 1
            this.$refs['violateDetailTable'].execRowDataApi(this.query)
          }
        })
      })
    },
    trankFormatter(row) {
      if (row.clear_flag == 1) {
        return null
      }
      return row.trank
    },
    /**
     * 文档等级
     * */
    tagLevelFormatter(row, data) {
      if (row.clear_flag == 1) {
        return this.$t('pages.noSignInfo')
      }
      return row.trank_content
    },
    /**
     * 标签类型
     * */
    addTypeFormatter(row, data) {
      return this.tagTypeMap[data]
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.submitting = false
    },
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

