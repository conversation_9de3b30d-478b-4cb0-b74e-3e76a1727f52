<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query">
        <i class="el-icon-s-home" style="position: absolute; left: 24px; top: 16px;"></i>
        <!--面包屑-->
        <div style="width: 510px; float: left;">
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <!--上方搜索条件-->
        <div style="float: right">
          <!--查询条件组件-->
          <query ref="queryData"/>
          <!--图表解读-->
          <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
          <!-- <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
          <el-button type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>-->
        </div>
      </div>

      <!-- 页面无数据 -->
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>

      <!--分析初始页面-->
      <div v-if="InceptionDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
            <div class="icon">
              <svg-icon :icon-class="item.icon"></svg-icon>
            </div>
            <div class="content">
              <label :title="item.label">{{ item.label }}</label>
              <el-button type="text" :class="{'underline': true }" @click="item.func">{{ temp.itemValue[item.key] }}</el-button>
            </div>
          </div>
        </div>
        <!--中间文档文档等级TOP10--柱状图-->
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <div class="mini-title"></div>
            <bar-chart
              :chart-data="temp.barChartBusinessTypeData"
              :chart-option="temp.barChartBusinessTypeOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.fileLeverBar.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.fileLeverBar + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title"></div>
            <bar-chart
              :chart-data="temp.tagContentTypeBarData"
              :chart-option="temp.tagContentTypeBarOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.backupType.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.backupType + '</div>'"></div>
            </div>
          </div>
        </div>
        <!--下方对部门，终端（操作员），数量趋势的分别展示部分-->
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="deptTitleClick">{{ $t('report.tagReportMsg1') }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ $t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigDept"/>
              </el-tooltip>
            </div>
            <pie-chart
              :chart-data="temp.barChartData"
              :chart-option="temp.barChartOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.deptBackup.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.deptBackup + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="userTitleClick">{{ terminalUserAnalysisTitle }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ $t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigTU"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.chartsUserDatas"
              :chart-option="temp.chartUserOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.termOrUserBackup.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.termOrUserBackup + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title"><span @click="notOfflineTitleClick">{{ $t('report.tagReportMsg17') }}>></span></div>
            <line-chart
              :chart-data="temp.lineChartData"
              :chart-option="temp.lineOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.backupTrend.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.backupTrend + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
      <!--部门分析-->
      <div v-if="deptDiv" class="detailsDiv">
        <analysis-dept :dialog-status="dialogStatus"/>
      </div>
      <!--终端、操作员分析-->
      <div v-if="userDiv" class="detailsDiv">
        <analysis-terminal-user :dialog-status="dialogStatus"/>
      </div>
      <!--违规数量趋势分析-->
      <div v-if="tendencyDiv" class="detailsDiv">
        <analysis-tendency :dialog-status="dialogStatus"/>
      </div>
    </div>
    <!--小方块（加标签文件总数）点击弹框-->
    <total-violation-dlg ref="totalViolationDlg" :dialog-status="dialogStatus"/>
    <!--小方块（部门）点击弹框-->
    <dept-dlg ref="deptDlg"/>
    <!--小方块点（终端）击弹框-->
    <terminal-user-dlg ref="terminalUserDlg" :dialog-status="dialogStatus"/>
    <!--    部门、终端、操作员前五名小图标点击弹框-->
    <echart-dlg ref="echartDlg" :title="echartDlgTitle" :echart-status="echartStatus"/>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
import Query from '@/views/report/sensitiveControl/common/query';
import DeptDlg from '@/views/report/terminalBehaviorControl/documentTag/statistical/dlgDept';
import EchartDlg from '@/views/report/terminalBehaviorControl/documentTag/statistical/echartDlg';
import TerminalUserDlg from '@/views/report/terminalBehaviorControl/documentTag/statistical/dlgTerminalUser';
import TotalViolationDlg from '@/views/report/terminalBehaviorControl/documentTag/statistical/dlgTotalViolation';
import analysisDept from '@/views/report/terminalBehaviorControl/documentTag/statistical/analysisDept';
import analysisTerminalUser from '@/views/report/terminalBehaviorControl/documentTag/statistical/analysisTerminalUser';
import analysisTendency from '@/views/report/terminalBehaviorControl/documentTag/statistical/analysisTendency';
import { getDocTagAddTypeHomepageData } from '@/api/report/baseReport/documentTag/tag';

export default {
  name: 'DocumentTagStatistical',
  components: { Query, BarChart, LineChart, PieChart, DeptDlg, EchartDlg, TerminalUserDlg, TotalViolationDlg, analysisDept, analysisTerminalUser, analysisTendency },
  props: {
  },
  data() {
    return {
      commonInterpretation: `<span>图表解读：</span>`,
      chartInterpretationContent: {
        backupTrend: '',
        backupType: '',
        deptBackup: '',
        fileLeverBar: '',
        termOrUserBackup: ''
      },
      typeOptions: [{ value: 1, label: this.$t('pages.conutType1') }],
      tempQuery: {},
      // 搜查查询
      query: {
        // 是否显示图表解读
        isExplain: true,
        dimBaseType: 1,
        dimValue: '',
        countByObject: 2
      },
      terminalUserAnalysisTitle: this.$t('report.tagReportMsg3'),
      // 统计方式是否终端
      isTerminalStatistics: true,
      isUserStatistics: false,
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: this.$t('report.tagReportMsg4') }
      ],
      // 暂无数据
      noData: true,
      // 初始页面显示隐藏
      InceptionDiv: false,
      // 操作员分析显示隐藏
      userDiv: false,
      // 部门分析显示隐藏
      deptDiv: false,
      // 趋势分析显示隐藏
      tendencyDiv: false,
      // 弹框名称
      deviceTitle: '',
      // 弹框状态
      dialogStatus: '',
      // 放大柱状图弹框名称
      echartDlgTitle: '',
      echartStatus: 'dept',
      // 上方小方块
      itemOption: [
        { label: this.$t('table.numberOfTags'), key: 'all', icon: 'total', func: this.totalViolationFun },
        { label: this.$t('table.dept'), key: 'dept', icon: 'tree', func: this.deptFun },
        { label: this.$t('report.terminalNumber'), key: 'terminalUser', icon: 'terminal', func: this.terminalUserFun }
      ],
      temp: {},
      defaultTemp: {
        itemValue: {
          all: '0',
          dept: '0',
          terminalUser: '0'
        },
        // 标签信息对比统计 柱状图
        tagContentTypeBarData: [],
        tagContentTypeBarOption: {
          title: {
            'text': this.$t('report.tagReportMsg29'),
            'subtext': this.$t('pages.topN', { num: 10 })
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            name: this.$t('report.tagContentLabel'),
            data: [],
            axisLabel: {
              rotate: 20, // 标签倾斜角度
              interval: 0, // 强制显示所有标签，避免自动隐藏
              // 强制标签显示在坐标轴下方
              position: 'bottom',
              align: 'right',
              // 文字顶部对齐(避免向上漂移)
              verticalAlign: 'top'
            }
          },
          series: [
          ]
        },
        // 文档等级TOP10 柱状图
        barChartBusinessTypeData: [],
        barChartBusinessTypeOption: {
          title: {
            'text': this.$t('report.tagReportMsg5'),
            'subtext': this.$t('pages.topN', { num: 10 })
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            name: this.$t('table.labelGrade'),
            data: []
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 饼图 标签类型统计
        tagTypeDatas: [],
        tagTypOption: {
          title: {
            text: this.$t('report.labelTypeStatistics'),
            left: 'center',
            subtext: ''
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 部门加标签分析 柱状图
        barChartData: [],
        barChartOption: {
          title: {
            'text': this.$t('report.departmentLabelQuantityChart'),
            'subtext': this.$t('pages.topN', { num: 5 })
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 饼图 操作员、终端 加标签数量图
        chartsUserDatas: [
        ],
        chartUserOption: {
          title: {
            text: this.$t('report.terminalLabelQuantityDiagram'),
            left: 'center',
            'subtext': this.$t('pages.topN', { num: 5 })
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0
            }
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 折线图数据 加标签数量趋势分析
        lineChartData: [],
        lineOption: {
          title: {
            'text': this.$t('report.tagReportMsg6')
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
    this.loadComponent()
  },
  activated() {},
  methods: {
    /**
     * 部门分析小图标点击
     * */
    handleBigDept() {
      this.echartDlgTitle = this.$t('report.departmentLabelQuantityChart')
      this.echartStatus = 'dept'
      this.$refs.echartDlg.show(this.query, this.echartStatus)
    },
    /**
     * 终端操作员小图标点击
     * */
    handleBigTU() {
      if (this.query.countByObject === 2) {
        this.echartDlgTitle = this.$t('report.terminalLabelQuantityDiagram')
        this.echartStatus = 'terminal'
      } else {
        this.echartDlgTitle = this.$t('report.operatorTagQuantityChart')
        this.echartStatus = 'user'
      }
      // console.log('this.query11111', JSON.parse(JSON.stringify(this.query)))
      this.$refs.echartDlg.show(this.query, this.echartStatus)
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取数据信息
     * */
    getData() {
      this.query = this.$refs['queryData'].getQuery()
      this.tempQuery = Object.assign({}, this.query)
      return getDocTagAddTypeHomepageData(this.query).then(resp => {
        if (resp.data) {
          this.InceptionDiv = true
          this.noData = false
          // 图表解读
          if (resp.data.itemValueMap.explanation) {
            this.chartInterpretationContent = { ...this.chartInterpretationContent, ...resp.data.itemValueMap.explanation }
          } else {
            this.chartInterpretationContent = {
              backupTrend: '',
              backupType: '',
              deptBackup: '',
              fileLeverBar: '',
              termOrUserBackup: ''
            }
          }
          // 上方小方块
          this.temp.itemValue.all = resp.data.itemValueMap.violateTotal
          this.temp.itemValue.dept = resp.data.itemValueMap.deptCount
          this.temp.itemValue.terminalUser = resp.data.itemValueMap.terminalUserCount

          // 文档等级 柱状图
          this.temp.barChartBusinessTypeData = resp.data.chartDataObjMap.trankBarChartData.chartData
          this.temp.barChartBusinessTypeOption.xAxis.data = resp.data.chartDataObjMap.trankBarChartData.xaxisData
          this.temp.barChartBusinessTypeOption.series[0].data = resp.data.chartDataObjMap.trankBarChartData.seriesData

          // 标签内容 柱状图
          this.temp.tagContentTypeBarOption.xAxis.data = resp.data.chartDataObjMap.tagContentTypeBarChartData.xaxisData
          this.temp.tagContentTypeBarOption.series = resp.data.chartDataObjMap.tagContentTypeBarChartData.seriesData
          // 标签类型 饼图
          this.temp.tagTypeDatas = resp.data.chartDataObjMap.addTypePieChartData.chartData.map((name, index) => ({
            name: name,
            value: resp.data.chartDataObjMap.addTypePieChartData.seriesData[index]
          }))
          // 部门饼图
          this.temp.barChartData = resp.data.chartDataObjMap.deptPieChartData.chartData

          // 终端操作员 柱状图
          this.temp.chartsUserDatas = resp.data.chartDataObjMap.terminalUserBarChartData.chartData
          this.temp.chartUserOption.xAxis.data = resp.data.chartDataObjMap.terminalUserBarChartData.xaxisData
          this.temp.chartUserOption.series[0].data = resp.data.chartDataObjMap.terminalUserBarChartData.seriesData

          // 趋势分析 折线图
          this.temp.lineOption.xAxis.data = resp.data.chartDataObjMap.dateLineCharData.xaxisData
          this.temp.lineOption.series[0].data = resp.data.chartDataObjMap.dateLineCharData.seriesData
        } else {
          this.resetTemp()
          this.InceptionDiv = false
          this.noData = true
        }
        this.loadComponent()
      }).then(() => {
        this.triggerResize()
      })
    },
    /**
     * 搜索按钮点击（暂时原型主要是对统计类型变化时页面上显示变化的一个处理）
     * */
    loadReport() {
      // 获取子组件查询条件
      this.getData()
    },
    loadComponent() {
      if (this.query.countByObject === 1) {
        // 操作员
        Object.assign(this.itemOption[2], { label: this.$t('report.operatorCount'), icon: 'user' })
        this.terminalUserAnalysisTitle = this.$t('report.tagReportMsg7')
        this.temp.chartUserOption.title.text = this.$t('report.operatorTagQuantityChart')
        this.isTerminalStatistics = false
        this.isUserStatistics = true
      } else {
        // 终端
        Object.assign(this.itemOption[2], { label: this.$t('report.terminalNumber'), icon: 'terminal' })
        this.isTerminalStatistics = true
        this.terminalUserAnalysisTitle = this.$t('report.tagReportMsg3')
        this.temp.chartUserOption.title.text = this.$t('report.terminalLabelQuantityDiagram')
        this.isUserStatistics = false
      }
      // 初始页面（点击搜索的时候要回到初始页面）
      // this.InceptionDiv = true
      // 操作员（终端）页面
      this.userDiv = false
      // 部门页面
      this.deptDiv = false
      // 趋势页面
      this.tendencyDiv = false
      this.showFilePath = [{ id: 0, label: this.$t('report.tagReportMsg4') }]
    },
    /**
     * 违规总数(点击)
     */
    totalViolationFun() {
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.$refs.totalViolationDlg.show()
    },
    /**
     * 部门（点击）
     */
    deptFun() {
      this.$refs.deptDlg.show()
    },
    /**
     * 终端或操作员（点击）
     */
    terminalUserFun() {
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.$refs.terminalUserDlg.show()
    },
    /**
     * 打印
     * */
    handlePrint() {

    },
    /**
     * 导出
     * */
    handleExport() {

    },
    /**
     * 终端（操作员）分析点击
     * 显示终端（操作员）分析，面包屑中添加终端（操作员）分析
     * */
    userTitleClick() {
      this.InceptionDiv = false
      this.userDiv = true
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
        this.showFilePath.push({ id: 1, label: this.$t('report.tagReportMsg7') })
      } else {
        // 终端
        this.dialogStatus = 'terminal'
        this.showFilePath.push({ id: 1, label: this.$t('report.tagReportMsg3') })
      }
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    deptTitleClick() {
      this.InceptionDiv = false
      this.deptDiv = true
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 2, label: this.$t('report.tagReportMsg1') })
    },
    /**
     * 数量趋势点击
     * 显示数量趋势，面包屑中添加数量趋势
     * */
    notOfflineTitleClick() {
      this.InceptionDiv = false
      this.tendencyDiv = true
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 3, label: this.$t('report.tagReportMsg2') })
    },
    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        // 初始页面
        this.InceptionDiv = true
        // 操作员（终端）页面
        this.userDiv = false
        // 部门页面
        this.deptDiv = false
        // 趋势页面
        this.tendencyDiv = false
        this.showFilePath = [{ id: 0, label: this.$t('report.tagReportMsg4') }]
      }
    }
  }
}
</script>
