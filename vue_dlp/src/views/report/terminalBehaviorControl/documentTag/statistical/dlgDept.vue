<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :fullscreen="fullscreen"
      :title="$t('table.dept')"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="squareList"
        :stripe="false"
        :row-data-api="rowDataApi"
        :col-model="colModel"
        :autoload="false"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { listDocTagAddTypeDeptData } from '@/api/report/baseReport/documentTag/tag';

export default {
  name: 'DlgDept',
  components: { },
  props: {

  },
  data() {
    return {
      // 标签类型
      tagTypeMap: {
        0: this.$t('pages.addTypeMsg'),
        2: this.$t('pages.addTypeMsg2'),
        3: this.$t('pages.addTypeMsg3')
      },
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      rowData: [],
      query: { // 查询条件
        page: 1,
        limit: 20,
        groupType: 1
      },
      colModel: [
        { prop: 'group_name', label: 'deptName' },
        { prop: 'file_num_sum', label: 'numberOfTags', sort: true },
        { prop: 'addTypeStr', label: 'tagType', width: '150', formatter: (row, data) => { return data.replace(/^,|,$/g, '').split(',').map(item => this.tagTypeMap[item]).join(',') } }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['squareList']
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  created() {
  },
  methods: {
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listDocTagAddTypeDeptData(searchQuery)
    },
    /**
       * 显示弹框
       */
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.query.page = 1
        this.gridTable.execRowDataApi(this.query);
      })
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

