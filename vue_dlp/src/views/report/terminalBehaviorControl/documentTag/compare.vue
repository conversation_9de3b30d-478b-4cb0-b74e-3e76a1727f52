<template>
  <comparative-trend
    :api="'getDocAddTagCompareData'"
    :api-dept="'getDocAddTagCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'DocumentTagCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: this.$t('table.numberOfTags'),
      // 统计项设置 复选框传值
      statisticalList: [
        // { prop: 'securityLevelConvertCountSum', label: 'securityLevelConvertCountSum' }
        { prop: 'manuallyAddNumSum', label: 'addTypeMsg' },
        // { prop: 'manuallyApplyNumSum', label: 'addTypeMsg1' },
        { prop: 'diskScanAddNumSum', label: 'addTypeMsg2' },
        { prop: 'landingAddNumSum', label: 'addTypeMsg3' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['manuallyAddNumSum', 'manuallyApplyNumSum', 'diskScanAddNumSum', 'landingAddNumSum']
    }
  },
  created() {

  },
  methods: {

  }
}
</script>
<style scoped>
</style>
