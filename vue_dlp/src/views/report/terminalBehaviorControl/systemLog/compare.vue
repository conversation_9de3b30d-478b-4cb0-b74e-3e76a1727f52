<template>
  <comparative-trend
    :api="'getSystemLogCompareData'"
    :api-dept="'getSystemLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'SystemLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: 'Windows系统日志',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'logNumSumAll', label: 'logNumSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['logNumSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
