<template>
  <Statistical :api="'getSystemPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('650')" :exportable="hasPermission('651')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '../../component/Statistical'
import { getSystemLogNumDetailCol } from '@/utils/report'

export default {
  name: 'SystemLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'logNumSumAll', label: 'logNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_system_log', detailCol: getSystemLogNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'logNumSum', label: 'logNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_system_log', detailCol: getSystemLogNumDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'logNumSumAll', label: 'logNumSumAll', width: '150', sort: true, detailTable: 'dwd_system_log', detailCol: getSystemLogNumDetailCol() }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'logLevelName', label: 'logLevel', width: '150', sort: true, joinSearch: true, searchCol: 'log_level', searchProp: 'logLevel' },
        { prop: 'logNumSum', label: 'logNumSumAll', width: '150', sort: true, detailTable: 'dwd_system_log', detailCol: getSystemLogNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
