<template>
  <Trend :api="'getSystemTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('655')" :exportable="hasPermission('656')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getSystemLogNumDetailCol } from '@/utils/report'

export default {
  name: 'SystemLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'logNumSumAll', label: 'logNumSumAll', width: '150', sort: true, detailTable: 'dwd_system_log', detailCol: getSystemLogNumDetailCol() }
      ],
      customList: [
        { prop: 'logNumSumAll', label: 'logNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_system_log', detailCol: getSystemLogNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
