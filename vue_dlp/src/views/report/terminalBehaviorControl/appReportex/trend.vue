<template>
  <Trend :api="'getAppReportexTrend'" :col-model="colModel" :custom-list="customList" :conversion-time="true" :drill-down="hasPermission('705')" :exportable="hasPermission('706')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { formatSeconds } from '@/utils'
import { getAppRunTimeDetailCol, getComputerWorkTimeDetailCol, beforeLoadAppRunTimeDetail } from '@/utils/report'
import { listSoftGroup } from '@/api/behaviorManage/application/appGroup'
import { filterAppRuntimeColumn } from '@/utils/reportPermissionFiltering'

export default {
  name: 'AppReportexTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'totalUptime', label: 'totalUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() },
        { prop: 'activeUptime', label: 'activeUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() },
        { prop: 'runTimeSumAll', label: 'runTimeSumAll', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_app_reportex', detailCol: getAppRunTimeDetailCol() }
      ],
      customList: [
        { prop: 'totalUptime', label: 'totalUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() },
        { prop: 'activeUptime', label: 'activeUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() },
        { prop: 'runTimeSumAll', label: 'runTimeSumAll', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_app_reportex', detailCol: getAppRunTimeDetailCol() }
      ]
    }
  },
  created() {
    this.colModel = filterAppRuntimeColumn(this.colModel)
    this.customList = filterAppRuntimeColumn(this.customList)
    if (this.hasPermission('C27')) {
      this.concatProcessTypeCol()
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    concatProcessTypeCol() {
      listSoftGroup().then(res => {
        if (res.data) {
          res.data.push({ id: -1, name: 'ungrouped', escapeFormat: false })
          const processTypeCol = res.data.map(item => {
            return { prop: 'category_' + item.id, label: item.name, width: '150', flag: 'time', sort: true, sortOriginal: true, escapeFormat: item.escapeFormat !== false, beforeLoadDetail: beforeLoadAppRunTimeDetail, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_app_reportex', detailCol: getAppRunTimeDetailCol() }
          })

          this.customList = this.customList.concat(processTypeCol)
        }
      }).catch(err => {
        console.log(err)
      })
    }
  }
}
</script>

