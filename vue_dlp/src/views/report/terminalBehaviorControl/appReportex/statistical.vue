<template>
  <Statistical
    ref="appReportexStatistical"
    :api="'getAppReportexBar'"
    :default-sort-prop="'runTimeSumAll'"
    :default-col-model="defaultColModel"
    :custom-list="customList"
    :conversion-time="true"
    :only-terminal="hasPermission('B26') && !hasPermission('C27')"
    :drill-down="hasPermission('700')"
    :exportable="hasPermission('701')"
    @handleSelect="handleCustomColumn"
    @getCountByObject="handleCountType"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { formatSeconds } from '@/utils'
import { getAppRunTimeDetailCol, getComputerWorkTimeDetailCol, beforeLoadAppRunTimeDetail } from '@/utils/report'
import { listSoftGroup } from '@/api/behaviorManage/application/appGroup'
import { filterAppRuntimeColumn } from '@/utils/reportPermissionFiltering'

export default {
  name: 'AppReportexStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'totalUptime', label: 'totalUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() },
        { prop: 'activeUptime', label: 'activeUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() },
        { prop: 'runTimeSumAll', label: 'runTimeSumAll', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_app_reportex', detailCol: getAppRunTimeDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'totalUptime', label: 'totalUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() },
        { prop: 'activeUptime', label: 'activeUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() },
        { prop: 'runTimeSumAll', label: 'runTimeSumAll', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_app_reportex', detailCol: getAppRunTimeDetailCol() }
      ],
      itemsToProcess: [
        { prop: 'totalUptime', label: 'totalUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() },
        { prop: 'activeUptime', label: 'activeUptime', width: '150', flag: 'time', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_computer_work_time', detailCol: getComputerWorkTimeDetailCol() }
      ],
      // 用于记录从终端切换到操作员之前有哪些列
      originalCol: []
    }
  },
  created() {
    // 根据对应的审计日志菜单权限对数据列过滤
    this.customList = filterAppRuntimeColumn(this.customList)
    this.defaultColModel = filterAppRuntimeColumn(this.defaultColModel)
    this.itemsToProcess = filterAppRuntimeColumn(this.itemsToProcess)
    // 判断是否有程序运行时长统计记录的菜单权限
    if (this.hasPermission('C27')) {
      this.concatProcessTypeCol()
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
    },
    concatProcessTypeCol() {
      listSoftGroup().then(res => {
        if (res.data) {
          res.data.push({ id: -1, name: 'ungrouped', escapeFormat: false })
          const processTypeCol = res.data.map(item => {
            return { prop: 'category_' + item.id, label: item.name, width: '150', flag: 'time', sort: true, sortOriginal: true, escapeFormat: item.escapeFormat !== false, beforeLoadDetail: beforeLoadAppRunTimeDetail, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_app_reportex', detailCol: getAppRunTimeDetailCol() }
          })

          this.customList = this.customList.concat(processTypeCol)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handleCountType(val) {
      const containsTotalUptime = this.customList.some(item => item.prop === 'totalUptime')
      const containsActiveUptime = this.customList.some(item => item.prop === 'activeUptime')
      if (1 === val && containsTotalUptime && containsActiveUptime) {
        this.originalCol = this.defaultColModel.map(item => item.prop)
        this.customList = this.customList.filter(item => item.prop !== 'totalUptime' && item.prop !== 'activeUptime')
        this.defaultColModel = this.defaultColModel.filter(item => item.prop !== 'totalUptime' && item.prop !== 'activeUptime')
      }

      if (2 === val && !containsTotalUptime && !containsActiveUptime) {
        this.customList.unshift(...this.itemsToProcess)

        const itemsToProcess = this.itemsToProcess.filter(item => {
          return this.originalCol.indexOf(item.prop) !== -1
        })
        this.defaultColModel.splice(1, 0, ...itemsToProcess)
      }
    }
  }
}
</script>

