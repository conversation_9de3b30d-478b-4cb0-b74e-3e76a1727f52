<template>
  <comparative-trend
    :api="'getAppReportexCompareData'"
    :api-dept="'getAppReportexCompareDeptData'"
    :only-user="hasPermission('B26') && !hasPermission('C27')"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
    @getCountByObject="handleCountType"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
import { filterAppRuntimeColumn } from '@/utils/reportPermissionFiltering';

export default {
  name: 'AppReportexCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '应用程序运行时长',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'totalUptime', label: 'totalUptime', flag: 'time' },
        { prop: 'activeUptime', label: 'activeUptime', flag: 'time' },
        { prop: 'runTimeSumAll', label: 'runTimeSumAll', flag: 'time' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['totalUptime', 'activeUptime', 'runTimeSumAll'],
      // 用于记录从终端切换到操作员之前有哪些列
      defaultStatisticalList: [
        { prop: 'totalUptime', label: 'totalUptime', flag: 'time' },
        { prop: 'activeUptime', label: 'activeUptime', flag: 'time' },
        { prop: 'runTimeSumAll', label: 'runTimeSumAll', flag: 'time' }
      ],
      defaultStatisticalListCheck: ['totalUptime', 'activeUptime', 'runTimeSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.statisticalList = filterAppRuntimeColumn(this.defaultStatisticalList)
    this.statisticalListCheck = this.statisticalList.map(item => item.prop)
  },
  methods: {
    /**
     * 统计类型发生变化时，如果选中操作员，则过滤掉开机时长和活动时长
     * @param val 1:操作员 2：终端
     */
    handleCountType(val) {
      const defaultStatisticalList = filterAppRuntimeColumn(this.defaultStatisticalList)
      if (1 === val) {
        this.statisticalList = defaultStatisticalList.filter(item => item.prop !== 'totalUptime' && item.prop !== 'activeUptime')
        this.statisticalListCheck = this.statisticalList.map(item => item.prop)
      }
      if (2 === val) {
        this.statisticalList = defaultStatisticalList
        this.statisticalListCheck = this.statisticalList.map(item => item.prop)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
