<template>
  <comparative-trend
    :api="'getEnergySavingRecordCompareData'"
    :api-dept="'getEnergySavingRecordCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'EnergySavingRecordCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '计算机节能',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'computerEnergySavingCountSum', label: 'computerEnergySavingCountSum' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['computerEnergySavingCountSum']
    }
  }
}
</script>
