<template>
  <Trend :api="'getEnergySavingRecordTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('920')" :exportable="hasPermission('921')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getComputerEnergySavingNumDetailCol } from '@/utils/report'

export default {
  name: 'EnergySavingRecordTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'computerEnergySavingCountSum', label: 'computerEnergySavingCountSum', width: '150', sort: true, detailTable: 'dwd_energy_saving_record', detailCol: getComputerEnergySavingNumDetailCol() }
      ],
      customList: [
        { prop: 'computerEnergySavingCountSum', label: 'computerEnergySavingCountSum', width: '150', sort: true, detailTable: 'dwd_energy_saving_record', detailCol: getComputerEnergySavingNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
