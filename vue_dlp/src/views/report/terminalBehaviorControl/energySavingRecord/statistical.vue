<template>
  <Statistical :api="'getEnergySavingRecordPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('917')" :exportable="hasPermission('918')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getComputerEnergySavingNumDetailCol } from '@/utils/report'

export default {
  name: 'EnergySavingRecordStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'computerEnergySavingCountSum', label: 'computerEnergySavingCountSum', width: '150', sort: true, detailTable: 'dwd_energy_saving_record', detailCol: getComputerEnergySavingNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'computerEnergySavingCount', label: 'computerEnergySavingCountSum', width: '150', sort: true, detailTable: 'dwd_energy_saving_record', detailCol: getComputerEnergySavingNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'computerEnergySavingCountSum', label: 'computerEnergySavingCountSum', width: '150', sort: true, detailTable: 'dwd_energy_saving_record', detailCol: getComputerEnergySavingNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'execTypeName', label: 'execType', width: '150', sort: true, joinSearch: true, searchCol: 'exec_type', searchProp: 'execType' },
        { prop: 'computerEnergySavingCount', label: 'computerEnergySavingCountSum', width: '150', sort: true, detailTable: 'dwd_energy_saving_record', detailCol: getComputerEnergySavingNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
