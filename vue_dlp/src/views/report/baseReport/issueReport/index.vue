<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        {{ $t('pages.dimBaseType') }}：
        <el-select v-model="query.dimValue" is-filter :placeholder="$t('text.select')" style="width: 150px; margin-right: 10px;" @change="loadReport" >
          <el-option
            v-for="(item, index) in dateOptions"
            :key="index"
            :label="item.dimDesc"
            :value="item.dimValue"
          ></el-option>
        </el-select>{{ $t('pages.countByObject') }}：
        <el-select v-model="query.countByObject" is-filter :placeholder="$t('text.select')" style="width: 150px; margin-right: 10px;" @change="loadReport">
          <el-option
            v-for="(item, index) in typeOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-checkbox v-model="query.groupType" :false-label="0" :true-label="1" :label="$t('pages.group')" @change="loadReport"></el-checkbox>

        <el-select v-model="showType" style="width: 150px; margin-right: 10px; float: right;">
          <el-option :label="$t('pages.showType1')" :value="1"></el-option>
          <el-option :label="$t('pages.showType2')" :value="2"></el-option>
          <el-option :label="$t('pages.showType3')" :value="3"></el-option>
        </el-select>
        <el-select v-model="query.recordSize" style="width: 100px; margin-right: 10px; float: right;" @change="loadReport">
          <el-option :label="$t('pages.all')" :value="0"></el-option>
          <el-option :label="$t('pages.TOP5')" :value="5"></el-option>
          <el-option :label="$t('pages.TOP10')" :value="10"></el-option>
        </el-select>
      </div>
      <div v-show="showType!==3" style="height: calc(55% - 40px);">
        <e-charts :charts-option="chartsOption" />
      </div>
      <grid-table
        v-show="showType!==2"
        ref="dataList"
        :col-model="colModel"
        :row-datas="rowDatas"
        :multi-select="false"
        :show-pager="false"
        style="height: 45%;"
      />
    </div>
  </div>
</template>

<script>
import ECharts from '@/components/ECharts'
import { getDimInfo, getEncOrDecPie } from '@/api/report/baseReport/issueReport/issue'

export default {
  name: 'Report',
  components: { ECharts },
  data() {
    return {
      chartsOption: [],
      colModel: [
        { prop: 'name', label: 'name', width: '150', sort: true }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'fileTotalSumAll', label: 'fileAllNum', width: '150' },
        { prop: 'succCountSumAll', label: 'successCount', width: '150' },
        { prop: 'failCountSumAll', label: 'errorCount', width: '150' },
        { prop: 'usedTimeSumAll', label: 'operateTime', width: '150' }
      ],
      subColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'opType', label: 'encryptionDecryption', width: '150', sort: true, formatter: (row, val) => { return { 1: this.$t('pages.loadTypeOptions3'), 2: this.$t('pages.loadTypeOptions2') }[val] } },
        { prop: 'fileTotalSum', label: 'fileAllNum', width: '150' },
        { prop: 'succCountSum', label: 'successCount', width: '150' },
        { prop: 'failCountSum', label: 'errorCount', width: '150' },
        { prop: 'usedTimeSum', label: 'operateTime', width: '150' }
      ],
      rowDatas: [],
      query: {
        dimBaseType: 1,
        dimValue: '',
        countByObject: 1,
        recordSize: 0,
        groupType: 0
      },
      dateOptions: [],
      typeOptions: [{ value: 1, label: this.$t('pages.conutType1') }],
      showType: 1,
      chartsInfo: undefined
    }
  },
  computed: {
    gridTable() {
      return this.$refs['dataList']
    }
  },
  watch: {},
  created() {
    // 获取月度维度数据
    const param = { businessCode: 'enc_or_dec_log', dimBaseType: 1 }
    getDimInfo(param).then(response => {
      this.dateOptions = response.data
    })
    this.loadReport()

    // 模拟chartDatas，此处模拟了钻取功能
    // const details = [ // 各个用户详情的模拟数据，后台只需返回单条数据中的 option 即可
    //   {
    //     type: 'pie',
    //     option: {
    //       title: { text: '加解密数量统计', x: '50%' },
    //       legend: {
    //         data: [
    //           '加密数量',
    //           '解密数量'
    //         ]
    //       },
    //       series: [
    //         {
    //           data: [
    //             { name: '加密数量', value: 15 },
    //             { name: '解密数量', value: 10 }
    //           ],
    //           type: 'pie'
    //         }
    //       ]
    //     },
    //     click: (para) => { this.chartsOption.splice(0, 1, chartsOpt[0]) },
    //     col: 24
    //   },
    //   {
    //     type: 'pie',
    //     option: {
    //       title: { text: '加解密数量统计', x: '50%' },
    //       legend: {
    //         data: [
    //           '加密数量',
    //           '解密数量'
    //         ]
    //       },
    //       series: [
    //         {
    //           data: [
    //             { name: '加密数量', value: 6 },
    //             { name: '解密数量', value: 7 }
    //           ],
    //           type: 'pie'
    //         }
    //       ]
    //     },
    //     click: (para) => { this.chartsOption.splice(0, 1, chartsOpt[0]) },
    //     col: 24
    //   },
    //   {
    //     type: 'pie',
    //     option: {
    //       title: { text: '加解密数量统计', x: '50%' },
    //       legend: {
    //         data: [
    //           '加密数量',
    //           '解密数量'
    //         ]
    //       },
    //       series: [
    //         {
    //           data: [
    //             { name: '加密数量', value: 5 },
    //             { name: '解密数量', value: 17 }
    //           ],
    //           type: 'pie'
    //         }
    //       ]
    //     },
    //     click: (para) => { this.chartsOption.splice(0, 1, chartsOpt[0]) },
    //     col: 24
    //   },
    //   {
    //     type: 'pie',
    //     option: {
    //       title: { text: '加解密数量统计', x: '50%' },
    //       legend: {
    //         data: [
    //           '加密数量',
    //           '解密数量'
    //         ]
    //       },
    //       series: [
    //         {
    //           data: [
    //             { name: '加密数量', value: 3 },
    //             { name: '解密数量', value: 2 }
    //           ],
    //           type: 'pie'
    //         }
    //       ]
    //     },
    //     click: (para) => { this.chartsOption.splice(0, 1, chartsOpt[0]) },
    //     col: 24
    //   },
    //   {
    //     type: 'pie',
    //     option: {
    //       title: { text: '加解密数量统计', x: '50%' },
    //       legend: {
    //         data: [
    //           '加密数量',
    //           '解密数量'
    //         ]
    //       },
    //       series: [
    //         {
    //           data: [
    //             { name: '加密数量', value: 9 },
    //             { name: '解密数量', value: 1 }
    //           ],
    //           type: 'pie'
    //         }
    //       ]
    //     },
    //     click: (para) => { this.chartsOption.splice(0, 1, chartsOpt[0]) },
    //     col: 24
    //   }
    // ]
    // const chartsOpt = [ // 用户总览的模拟数据
    //   {
    //     type: 'pie',
    //     option: {
    //       title: { text: '加解密数量统计', x: '50%' },
    //       legend: {
    //         data: [
    //           '用户1',
    //           '用户2',
    //           '用户3',
    //           '用户4',
    //           '用户5'
    //         ]
    //       },
    //       series: [
    //         {
    //           data: [
    //             { name: '用户1', value: 25 },
    //             { name: '用户2', value: 13 },
    //             { name: '用户3', value: 22 },
    //             { name: '用户4', value: 5 },
    //             { name: '用户5', value: 10 }
    //           ],
    //           type: 'pie'
    //         }
    //       ]
    //     },
    //     click: (para) => { this.chartsOption.splice(0, 1, details[para.dataIndex]) },
    //     col: 24
    //   }
    // ]
  },
  methods: {
    loadReport() {
      const changeConfig = (chartsOption, colModel, rowDatas) => {
        this.colModel.splice(0, this.colModel.length, ...colModel)
        this.rowDatas.splice(0, this.rowDatas.length, ...rowDatas)
        this.chartsOption.splice(0, 1, chartsOption)
      }
      getEncOrDecPie(this.query).then(respond => {
        this.chartsInfo = respond.data
        if (this.chartsInfo) {
          const subEChartsOpts = this.chartsInfo.subEChartsOptions
          if (subEChartsOpts && subEChartsOpts.length > 0) {
            // 钻取下级
            this.chartsInfo.click = (para) => {
              const subChartsOpt = subEChartsOpts[para.dataIndex]
              // 返回上级
              subChartsOpt.click = (para) => {
                changeConfig(this.chartsInfo, this.defaultColModel, this.chartsInfo.oriData)
              }
              this.subColModel[0].formatter = (row, val) => { return para.name }
              changeConfig(subChartsOpt, this.subColModel, subChartsOpt.oriData)
            }
          }
          changeConfig(this.chartsInfo, this.defaultColModel, this.chartsInfo.oriData)
        }
      })
    }
  }
}
</script>
