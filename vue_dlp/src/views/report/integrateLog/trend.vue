<template>
  <Trend
    :api="'getIntegrateTrend'"
    :col-model="colModel"
    :custom-list="customList"
    :drill-down="hasPermission('605')"
    :exportable="hasPermission('606')"
    :view-details="hasPermission('607')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Trend from '../component/Trend'
import { getMobileFileOpDetail } from '@/api/report/baseReport/issueReport/detail'
import { getEmailNumDetailCol, getPrintDetailCol, getFileOpNumDetailCol, getOutFileDetailCol, getBrowserUploadDetailCol,
  beforeLoadMobileDiskOpFileNum } from '@/utils/report'
import { filterIntegrateLogCustom, filterIntegrateLogDefault } from '@/utils/reportPermissionFiltering';

export default {
  name: 'IntegrateLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '140', sort: true, sortOriginal: true },
        { prop: 'emailNumSumAll', label: 'emailNumSumAll', width: '130', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll', width: '130', sort: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'mobileStorageFileNumSumAll', label: 'mobileStorageFileNumSumAll', width: '160', sort: true, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoadMobileDiskOpFileNum },
        { prop: 'outFileNumSumAll', label: 'outFileNumSumAll', width: '160', sort: true, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol() },
        { prop: 'browserUploadFileNumSumAll', label: 'browserUploadFileNumSumAll', width: '150', sort: true, detailTable: 'dwd_browser_upload_log', detailCol: getBrowserUploadDetailCol() }
      ],
      customList: [
        { prop: 'emailNumSumAll', label: 'emailNumSumAll', width: '130', sort: true, value: 16, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'emailFileSizeSumAll', label: 'emailFileSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 32, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll', width: '130', sort: true, value: 256, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'printedPagesSumAll', label: 'printedPagesSumAll', width: '130', sort: true, value: 512, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'mobileStorageFileNumSumAll', label: 'mobileStorageFileNumSumAll', width: '160', sort: true, value: 4, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoadMobileDiskOpFileNum },
        { prop: 'mobileStorageFileSizeSumAll', label: 'mobileStorageFileSizeSumAll', width: '180', sort: true, flag: 'size', sortOriginal: true, value: 8, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoadMobileDiskOpFileNum },
        { prop: 'outFileNumSumAll', label: 'outFileNumSumAll', width: '160', sort: true, value: 1, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol() },
        { prop: 'outFileSizeSumAll', label: 'outFileSizeSumAll', width: '160', sort: true, flag: 'size', sortOriginal: true, value: 2, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol() },
        { prop: 'browserUploadFileNumSumAll', label: 'browserUploadFileNumSumAll', width: '150', sort: true, value: 64, detailTable: 'dwd_browser_upload_log', detailCol: getBrowserUploadDetailCol() },
        { prop: 'browserUploadFileSizeSumAll', label: 'browserUploadFileSizeSumAll', width: '160', sort: true, flag: 'size', sortOriginal: true, value: 128, detailTable: 'dwd_browser_upload_log', detailCol: getBrowserUploadDetailCol() }
      ]
    }
  },
  created() {
    this.filterData()
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    /**
     * 权限过滤
     */
    filterData() {
      this.customList = filterIntegrateLogCustom(this.customList)
      this.colModel = filterIntegrateLogDefault(this.colModel, 'labelValue')
    },
    getEmailNumDetailCol() {
      return getEmailNumDetailCol(this.hasPermission('607'))
    }
  }
}
</script>
