<template>
  <div ref="print" class="app-container" style="overflow:hidden;">
    <div v-loading="loading">
      <div class="toolbar clearfix" style="padding: 5px 20px 0 15px">
        <span>
          {{ $t('pages.timeQuery') }}：
          <el-date-picker
            ref="start"
            v-model="query.startDate"
            :clearable="false"
            value-format="yyyy-MM-dd"
            type="date"
            style="width: 140px"
            :picker-options="pickerStartOptions"
            :placeholder="$t('pages.startDate')"
            @change="changeStart"
          />
          <span>~</span>
          <el-date-picker
            ref="end"
            v-model="query.endDate"
            :clearable="false"
            value-format="yyyy-MM-dd"
            type="date"
            style="width: 140px"
            :picker-options="pickerEndOptions"
            :placeholder="$t('pages.endDate')"
            @change="changeEnd"
          />
        </span>
        <span>
          <el-input v-model="query.searchName" v-trim clearable :placeholder="$t('pages.tableNameOrBizName')" style="width: 200px;"></el-input>
        </span>
        <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">{{ $t('pages.dataChange_Msg') }}</div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <div class="table-container">
        <div style="height: 350px;margin-bottom: 5px">
          <el-row :gutter="10" class="dynamic-height">
            <el-col :lg="isShowChartInterpretation ? 20 : 24" style="margin-bottom: 5px;">
              <div class="panel-wrapper">
                <line-chart
                  :chart-data="lineChartData"
                  :chart-option="lineOption"
                  :click="lineChartFun"
                  style="width: 100%;height: 350px"
                />
              </div>
            </el-col>
            <!--图表解读-->
            <el-col v-if="isShowChartInterpretation" :lg="4" style="margin-bottom: 5px;height: 100%;">
              <div class="panel-wrapper unscramble">
                <div v-html="chartInterpretationContent"></div>
              </div>
            </el-col>
          </el-row>
        </div>
        <grid-table
          ref="dataList"
          :col-model="colModel"
          :multi-select="false"
          :row-data-api="rowDataApi"
          :autoload="false"
          :min-height="200"
          style="height: calc(100vh - 520px)"
        />
      </div>
    </div>
    <!--查看详情-->
    <details-dlg ref="details"/>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import detailsDlg from '@/views/report/dataTansaction/details'
import { getDataCollectTrend, getDataCollectPage } from '@/api/report/baseReport/dataTansaction'
import moment from 'moment';

export default {
  name: 'DataTansaction',
  components: { LineChart, detailsDlg },
  props: {

  },
  data() {
    return {
      // 是否显示图表解读
      isShowChartInterpretation: false,
      chartInterpretationContent: `<div class="unscramble-title">图表解读：</div>
                1、从整体趋势上看，2024年硬件资产变更趋势较上个月同期<span class="unscramble-red">下降50%</span>。其中，5-6月<span class="unscramble-red">下降57%</span>，4-5月<span class="unscramble-green">上升15%</span>。<br/>
                2、从数量上看，<span class="unscramble-title">4月</span>变更数量最大，变更了<span class="unscramble-title">381</span>次，<span class="unscramble-red">12月</span>变更数量最小，变更了<span class="unscramble-green">53</span>次。<br/>
                结论：硬件资产变更还是保持在一个相对稳定的状态，还是<span class="unscramble-green">比较安全</span>的。`,
      // 限制开始时间不能大于结束时间，且当前时间之后的时间不可选
      pickerStartOptions: {
        disabledDate: time => {
          const endTime = this.query.endDate;
          if (endTime) {
            return time.getTime() > new Date(endTime).getTime() || time.getTime() > Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天;
          } else {
            return time.getTime() > Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天;
          }
        }
      },
      pickerEndOptions: {
        disabledDate: time => {
          const beginTime = this.query.startDate;
          if (beginTime) {
            return time.getTime() < new Date(beginTime).getTime() || time.getTime() > Date.now();
          } else {
            return time.getTime() > Date.now();
          }
        }
      },
      loading: false,
      // 折线图数据
      lineChartData: [],
      lineOption: {
        title: {
          text: this.$t('route.DataTansaction') + this.$t('pages.statistical')
        },
        toolbox: {
          show: false
        },
        xAxis: {
          name: this.$t('pages.time'),
          type: 'category',
          data: []
        },
        yAxis: {
          name: this.$t('components.number'),
          type: 'value',
          // 控制分隔线
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          // 控制隔区域
          splitArea: {
            show: false
          }
        },
        series: [
          {
            data: [],
            type: 'line'
          }
        ]
      },
      colModel: [
        { prop: 'srcTable', label: this.$t('pages.tableName'), formatter: this.tableNameFormatter },
        // { prop: 'tableName', label: 'businessName' },
        { prop: 'allCount', label: 'logNumSumAll' },
        { prop: 'changeCount', label: 'changeCount' },
        { prop: 'changeRate', label: 'changeRate' },
        { prop: 'srcSystem', label: 'srcSystem' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'detail', click: this.handleView }
          ]
        }
      ],
      // 表格数据
      rowDatas: [],
      // 查询数据
      query: {
        searchName: '',
        page: 1,
        limit: 20,
        startDate: '',
        endDate: '',
        detailDate: ''
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['dataList']
    }
  },
  watch: {

  },
  created() {
    this.query.endDate = moment(new Date()).format('YYYY-MM-DD')
    this.query.startDate = moment(new Date()).add(-7, 'd').format('YYYY-MM-DD')
    this.loadReport()
  },
  methods: {
    // 时间删除的时候选中当前组件选中值
    changeStart(val) {
      if (!val) {
        this.query.startDate = this.$refs.start.value; // 如果输入值为空，则重置为当前选中的日期
      }
    },
    changeEnd(val) {
      if (!val) {
        this.query.endDate = this.$refs.end.value; // 如果输入值为空，则重置为当前选中的日期
      }
    },
    /**
     * 数据库采集表表名（多语言转换）
     * @param row
     * @param name
     * @returns {*|string}
     */
    tableNameFormatter(row, name) {
      // console.log('name', name)
      const lastUnderscoreIndex = name.lastIndexOf('_');
      if (lastUnderscoreIndex !== -1) {
        const before = name.substring(0, lastUnderscoreIndex);  // 截取下划线前部分
        const after = name.substring(lastUnderscoreIndex + 1);   // 截取下划线后部分
        // 尝试翻译
        const translatedText = this.$t('tableName.' + before);
        // 检查翻译结果是否等于键名本身
        if (translatedText == `tableName.${before}`) {
          // 如果是，则说明没有找到对应的翻译，返回原始值
          return name;
        } else {
          // 否则，返回翻译后的文本
          return translatedText + '_' + after;
        }
      } else {
        return name
      }
    },
    /**
     * 获取页面数据
     * */
    getTrend() {
      this.loading = true
      getDataCollectTrend(this.query).then(res => {
        this.loading = false
        this.lineOption.xAxis.data = res.data.trend.xaxisData
        this.lineOption.series[0].data = res.data.trend.seriesData
        this.lineOption.tooltip = {
          formatter: (d) => {
            // console.log('d', d)
            const time = d[0].axisValue + '<br>';
            const data = '日志数量：' + d[0].data;
            return time + data
          }
        }
      }).catch(() => {
        this.loading = false
      })
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getDataCollectPage(searchQuery)
    },
    /**
       * 搜索按钮点击
       */
    loadReport() {
      this.query.detailDate = ''
      this.getTrend()
      this.$nextTick(() => {
        this.query.page = 1
        this.gridTable.execRowDataApi(this.query)
      })
    },
    /**
     * 查看详情
     * @param row 当前行选中信息
     */
    handleView(row) {
      this.$refs['details'].show(row, this.query)
    },
    /**
     * 折线图上的小原点（点击）
     * 表格数据显示当前点击详细数据
     */
    lineChartFun(params) {
      this.query.detailDate = params.name
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    }
  }
}
</script>

<style lang="scss" scoped>
  .app-main>.app-container{
    overflow: auto;
  }
  .table-container{
    display: block;
    height: calc(100vh - 150px);
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 5px;
  }
</style>
