<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.details')"
      :visible.sync="dialogVisible"
      width="700px"
      @dragDialog="handleDrag"
    >
      <div style="height: 370px;">
        <line-chart
          v-if="dialogVisible"
          ref="chart"
          :chart-data="lineChartData"
          :chart-option="lineOption"
          style="width: 100%;height: 100%"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <span style="color: #2d8cf0;font-size: 14px;float: left;margin-left: 20px;line-height: 30px">{{ $t('pages.dataChange_dialog_Msg') }}</span>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog'
import LineChart from '@/components/ECharts/LineChart'
import { getDataCollectTrend } from '@/api/report/baseReport/dataTansaction'

export default {
  name: 'Edit',
  directives: { elDragDialog },
  components: { LineChart },
  props: {

  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      // 折线图数据
      lineChartData: [],
      lineOption: {
        title: {
          text: ''
        },
        toolbox: {
          show: false
        },
        xAxis: {
          name: this.$t('pages.time'),
          type: 'category',
          data: []
        },
        yAxis: {
          name: this.$t('components.number'),
          type: 'value',
          // 控制分隔线
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          // 控制隔区域
          splitArea: {
            show: false
          }
        },
        series: [
          {
            data: [],
            type: 'line'
          }
        ]
      }
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
  },
  methods: {
    show(row, query) {
      const tempQuery = Object.assign({}, query)
      const table = row.srcTable.substring(0, row.srcTable.lastIndexOf('_'));
      let tableName = ''
      const translatedText = this.$t('tableName.' + table);
      // 检查翻译结果是否等于键名本身
      if (translatedText == `tableName.${table}`) {
        // 如果是，则说明没有找到对应的翻译，返回原始值
        tableName = table;
      } else {
        // 否则，返回翻译后的文本
        tableName = translatedText;
      }
      tempQuery.businessCode = table
      this.dialogVisible = true
      this.lineOption.title.text = tableName + this.$t('pages.dataChange')
      getDataCollectTrend(tempQuery).then(res => {
        this.lineOption.xAxis.data = res.data.trend.xaxisData
        this.lineOption.series[0].data = res.data.trend.seriesData
        this.lineOption.tooltip = {
          formatter: (d) => {
            // console.log('d', d)
            const time = d[0].axisValue + '<br>';
            const data = '日志数量：' + d[0].data;
            return time + data
          }
        }
        this.$refs['chart'].__resizeHandler()
      })
    },
    handleDrag() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>
