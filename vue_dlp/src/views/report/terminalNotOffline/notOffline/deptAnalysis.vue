<template>
  <!--部门分析-->
  <div class="data-details">
    <div class="data-analysis-table table-parent-size" style="padding-top: 20px;">
      <div class="table-container table-left-size">
        <grid-table
          ref="terminalNotOfflineList"
          :col-model="colModel"
          :row-datas="rowData"
          :show-pager="false"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--部门分析上方几个小方块-->
      <div class="data-panel">
        <div v-for="(item, key) in deptItemOption" :key="key" class="data-item" >
          <label :title="item.label">{{ item.label }}</label>
          <span :title="item.value">{{ item.value }}</span>
        </div>
      </div>
      <div style="height: calc(100% - 140px); padding: 0 20px">
        <el-tabs v-model="activeNotOfflineName" @tab-click="tabClick">
          <el-tab-pane label="未关机信息统计" name="notMsg" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                v-if="isShowGrid"
                ref="terminalNotOfflineList"
                :col-model="colModelNotMsg"
                :row-datas="rowData"
                style="height: calc(100% - 20px);top: 20px"
                :show-pager="false"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane label="未关机数量趋势" name="notNum">
            <line-chart
              v-if="isShowChart"
              :chart-data="lineChartData"
              :chart-option="lineOption"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
export default {
  name: 'DeptAnalysis',
  components: { LineChart },
  data() {
    return {
      colModel: [
        { prop: 'terminalName', label: '部门名称' },
        { prop: 'softwareName', label: '耗电金额', sort: true }
      ],
      rowData: [],
      // 部门分析
      deptItemOption: [
        { label: '销售部', value: '4人' },
        { label: '未关机人天', value: '4' },
        { label: '未关机总时间', value: '410h51min' }
      ],
      // 部门分析
      activeNotOfflineName: 'notMsg',
      isShowGrid: true,
      isShowChart: false,
      colModelNotMsg: [
        { prop: 'terminalName', label: '姓名' },
        { prop: '1', label: '未关机天数', sort: true },
        { prop: '2', label: '未关机总时长', sort: true },
        { prop: '3', label: '耗电金额估算', sort: true },
        { prop: '4', label: '未关机原因' }
      ],
      // 折线图数据
      lineChartData: [150, 230, 224, 218, 135, 147, 260],
      lineOption: {
        title: {
          'text': '部门未关机数量趋势图',
          'subtext': '最近7天'
        },
        xAxis: {
          type: 'category',
          data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
          }
        ]
      }
    }
  },
  computed: {

  },
  created() {

  },
  activated() {},
  methods: {
    /**
     * tab点击事件，如果不写，切换时图表只有小小一点点
     * @param pane
     * @param event
     */
    tabClick(pane, event) {
      if (pane.name === 'notMsg') {
        this.isShowGrid = true
        this.isShowChart = false
      } else {
        this.isShowGrid = false
        this.isShowChart = true
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  /*小方格样式*/
  .data-panel{
    width: 100%;
    padding: 20px;
    display: flex;
  }
  .data-item{
    flex: 1;
    height: 100px;
    line-height: 100px;
    border: 1px solid #3b749c;
    display: inline-block;
    margin-left: 1%;
    font-size: 14px;
    display: flex;
    &:first-child{
      margin-left: 0;
    }
    label{
      line-height: 15px;
      align-self: center;
      font-weight: normal;
      flex: 2;
      text-align: center;
      font-size: 12px;
    }
    span{
      flex: 1;
      text-align: center;
      font-size: 12px;
    }
  }
  @media screen and (max-width: 1500px){
    .data-item{
      flex-direction: column;
      line-height: 50px;
    }
  }
  .data-analysis{
    flex: 1;
    height: 100%;
  }
  .data-details{
    width: 100%;
    height: 100%;
    padding:0 20px 20px 20px;
    display: flex;
  }
  .data-analysis-table{
    flex: 1;
  }
  .data-analysis-details{
    flex: 2;
  }
  /*解决表格宽度随浏览器大小变化自适应问题*/
  .table-parent-size{
    position: relative;
  }
  .table-left-size{
    width: 100%;
    height: calc(100% - 22px);
    position: absolute;
  }
  .table-right-size{
    width: 100%;
    position: absolute;
  }
  >>>.el-pagination{
    overflow-x: auto;
    overflow-y: hidden;
  }
</style>
