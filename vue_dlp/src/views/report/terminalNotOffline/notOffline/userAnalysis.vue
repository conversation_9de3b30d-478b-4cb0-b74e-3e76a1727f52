<template>
  <!--员工分析-->
  <div class="data-details">
    <div class="data-analysis-table table-parent-size" style="padding-top: 20px;">
      <div class="table-container table-left-size">
        <grid-table
          ref="terminalNotOfflineList"
          :col-model="colModel"
          :row-datas="rowData"
          :show-pager="false"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--用户分析上方几个小方块-->
      <div class="data-panel">
        <div v-for="(item, key) in userItemOption" :key="key" class="data-item" >
          <label :title="item.label">{{ item.label }}</label>
          <span>
            <el-button
              v-for="(btn, i) in item.value"
              :key="i"
              type="text"
              @click="btn.func"
            >{{ itemValue[btn.key] }}</el-button>
          </span>
        </div>
      </div>
      <!--用户分析中时间-->
      <div style="height: calc(100% - 140px);padding: 0 20px">
        <el-tabs v-model="activeNotOfflineName" @tab-click="tabClick">
          <el-tab-pane label="未关机日历" name="notCalendar">
            <div style="padding: 10px 20px;float: right;position: relative"><span style="display: inline-block;width: 10px;height: 10px;border-radius:50%;background: #ff0000;position: absolute;top: 13px;right: 75px;border: 1px solid #fff;"></span>未关机</div>
            <div v-if="isShowCalendar" class="data-analysis-panel">
              <div class="data-analysis">
                <el-calendar v-model="calendarValue1">
                  <!--选中小红点-->
                  <template
                    slot="dateCell"
                    slot-scope="{date, data}"
                  >
                    <div>
                      <div v-for="(item, key) in activeday" :key="key">
                        <el-badge v-if="data.day == item.dat" is-dot class="item"></el-badge>
                      </div>
                      <div class="spandate">{{ data.day.split('-').slice(2).join('-') }}</div>
                    </div>
                  </template>
                </el-calendar>
              </div>
              <div class="data-analysis">
                <el-calendar v-model="calendarValue2">
                  <!--选中小红点-->
                  <template
                    slot="dateCell"
                    slot-scope="{date, data}"
                  >
                    <div>
                      <div v-for="(item, key) in activeday" :key="key">
                        <el-badge v-if="data.day == item.dat" is-dot class="item"></el-badge>
                      </div>
                      <div class="spandate">{{ data.day.split('-').slice(2).join('-') }}</div>
                    </div>
                  </template>
                </el-calendar>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="未关机分析" name="notAnalysis">
            <div v-if="isShowNotAnalysis" class="data-analysis-panel">
              <div class="data-analysis">
                <pie-chart
                  :chart-data="chartsDatas"
                  :chart-option="chartOption"
                  :click="pieChartFun"
                />
              </div>
              <div class="data-analysis">
                <pie-chart
                  :chart-data="chartsWorkDatas"
                  :chart-option="chartWorkOption"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <!--小方块点击弹框-->
    <square-user-dlg ref="squareDlg" :title="deviceTitle" :dialog-status="dialogStatus"/>
  </div>
</template>

<script>
import squareUserDlg from '@/views/report/terminalNotOffline/notOffline/squareUserDlg';
import PieChart from '@/components/ECharts/PieChart'
export default {
  name: 'UserAnalysis',
  components: { squareUserDlg, PieChart },
  data() {
    return {
      colModel: [
        { prop: 'terminalName', label: '姓名' },
        { prop: 'softwareName', label: '未关机天数', sort: true }
      ],
      rowData: [],
      workTitle: '', // 弹框名称
      deviceTitle: '', // 弹框名称
      dialogStatus: '', // 弹框状态
      // 员工分析
      userItemOption: [
        { label: '孟平', value: [{ key: 'dept', func: this.noFun }] },
        { label: '未关机总时间', value: [{ key: 'time', func: this.timeFun }] },
        { label: '耗电金额核算', value: [{ key: 'money', func: this.moneyFun }] }
      ],
      itemValue: {
        dept: '研发中心', // 未关机总人天
        time: '410h51min', // 未关机总时间
        money: '188.25元' // 耗电金额核算
      },
      activeNotOfflineName: 'notCalendar',
      isShowCalendar: true,
      isShowNotAnalysis: false,
      // 日历
      calendarValue1: new Date(),
      calendarValue2: new Date(),
      activeday: [ // 日历组件选中的日期，小红点，时间格式必须为yyyy-MM-dd，比如2月5号要写成02-05而不是2-5
        { dat: '2023-03-21' },
        { dat: '2023-03-15' },
        { dat: '2023-03-05' }
      ],
      // 饼图 未关机原因
      chartsDatas: [
        { value: 100, name: '加班' },
        { value: 85, name: '跑批' },
        { value: 45, name: '忘记关机' }
      ],
      chartOption: {
        title: {
          text: '未关机原因',
          left: 'center'
        },
        toolbox: {
          show: false
        },
        legend: {
          left: 'center'
        },
        series: [
          {
            radius: '30%'
          }
        ]
      },
      // 饼图 加班分析
      chartsWorkDatas: [
        { value: 100, name: '工作' },
        { value: 85, name: '浏览新闻' },
        { value: 15, name: '待机' },
        { value: 25, name: '聊天' },
        { value: 35, name: '追剧' },
        { value: 15, name: '网购' }
      ],
      chartWorkOption: {
        title: {
          text: '加班分析',
          left: 'center'
        },
        toolbox: {
          show: false
        },
        legend: {
          left: 'center'
        },
        series: [
          {
            radius: '30%'
          }
        ]
      }
    }
  },
  computed: {

  },
  created() {

  },
  activated() {},
  methods: {
    /**
     * 耗电金额核算
     * deviceTitle：弹框打开时标题
     * dialogStatus：区分点击的哪个小方块，共用一个弹框表格，控制某一列的显示隐藏
     * show(1)：传的1，2，3可以理解为到时候掉接口的一个参数，区分类型，如果后期不用可去掉
     */
    moneyFun() {
      this.deviceTitle = '耗电金额核算'
      this.dialogStatus = 'money'
      this.$refs.squareDlg.show(1)
    },
    /**
     * 未关机总时间
     */
    timeFun() {
      this.deviceTitle = '未关机时间'
      this.dialogStatus = 'time'
      this.$refs.squareDlg.show(2)
    },
    /**
     * 饼图（未关机原因）点击，列表弹框
     * */
    pieChartFun(params) {
      console.log('params', params)
      this.deviceTitle = params.name
      this.dialogStatus = 'pie'
      this.$refs.squareDlg.show(3)
    },
    noFun() {},
    /**
     * tab点击事件，如果不写，切换时图表只有小小一点点
     * @param pane
     * @param event
     */
    tabClick(pane, event) {
      if (pane.name === 'notCalendar') {
        this.isShowCalendar = true
        this.isShowNotAnalysis = false
      } else {
        this.isShowCalendar = false
        this.isShowNotAnalysis = true
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  /*小方格样式*/
  .data-panel{
    width: 100%;
    padding: 20px;
    display: flex;
  }
  .data-item{
    flex: 1;
    height: 100px;
    line-height: 100px;
    border: 1px solid #3b749c;
    display: inline-block;
    margin-left: 1%;
    font-size: 14px;
    display: flex;
    &:first-child{
      margin-left: 0;
    }
    label{
      line-height: 15px;
      align-self: center;
      font-weight: normal;
      flex: 2;
      text-align: center;
      font-size: 12px;
    }
    span{
      flex: 1;
      text-align: center;
      font-size: 12px;
    }
  }
  @media screen and (max-width: 1500px){
    .data-item{
      flex-direction: column;
      line-height: 50px;
    }
  }
  .data-analysis-panel{
    width: 100%;
    height: calc(100% - 140px);
    padding:0 20px 20px 20px;
    display: flex;
  }
  .data-analysis{
    flex: 1;
    height: 100%;
  }
  .data-details{
    width: 100%;
    height: 100%;
    padding:0 20px 20px 20px;
    display: flex;
  }
  .data-analysis-table{
    flex: 1;
  }
  .data-analysis-details{
    flex: 2;
  }
  /*解决表格宽度随浏览器大小变化自适应问题*/
  .table-parent-size{
    position: relative;
  }
  .table-left-size{
    width: 100%;
    height: calc(100% - 22px);
    position: absolute;
  }
  .table-right-size{
    width: 100%;
    position: absolute;
  }
  >>>.el-pagination{
    overflow-x: auto;
    overflow-y: hidden;
  }
  /*日历样式修改*/
  .data-analysis /deep/  .el-calendar-table .el-calendar-day{
    width: 100%;
    height: 100%;
  }
  /*隐藏今天按钮*/
  >>>.el-button-group>.el-button:not(:first-child):not(:last-child){
    display: none;
  }
  /*去掉原本背景颜色*/
  .data-analysis >>>.el-calendar{
    background: transparent;
  }
  >>>.el-calendar-table td:hover{
    background: transparent;
  }
  /*去掉选中背景颜色*/
  .data-analysis >>>.el-calendar-table td.is-selected{
    background: transparent;
  }
  /*修改每一小格大小*/
  .data-analysis >>>.el-calendar-table .el-calendar-day{
    position: relative;
    padding: 10px;
  }
  /*小红点样式*/
  .data-analysis >>>.el-badge{
    position: absolute;
    right: 5px;
    top: 5px;
  }
  /*日历边框颜色*/
  .data-analysis >>>.el-calendar-table tr td:first-child{
    border-left: 1px solid #666666;
  }
  .data-analysis >>>.el-calendar-table tr:first-child td{
    border-top: 1px solid #666666;
  }
  .data-analysis >>>.el-calendar-table td{
    border-bottom: 1px solid #666666;
    border-right: 1px solid #666666;
  }
  /*表格周一到周日颜色*/
  .data-analysis >>>.el-calendar-table thead th{
    color: #68a8d0;
  }
  /*头部日期颜色*/
  .data-analysis >>>.el-calendar__title{
    color: #68a8d0;
  }
  /*头部下面的横线*/
  .data-analysis >>>.el-calendar__header{
    border-bottom: 1px solid #666666;
  }
  /*鼠标悬停样式*/
  .data-analysis >>>.el-calendar-table .el-calendar-day:hover{
    cursor: default;
    background: transparent;
  }
  /*非本月字体颜色*/
  .data-analysis >>>.el-calendar-table:not(.is-range) td.next, .el-calendar-table:not(.is-range) td.prev{
    color: #666666;
    cursor: pointer;
  }
</style>
