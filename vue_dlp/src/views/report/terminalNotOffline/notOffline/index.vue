<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="roleTree"
        :data="treeData"
        node-key="dataId"
        :current-node-key="currentNodeKey"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
      ></tree-menu>
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" @click="btnConfig">
          配置
        </el-button>
      </div>

      <div>
        <div style="width: 510px; float: left;">
          <!--上方面包屑，面包屑控制下方内容的显示隐藏-->
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div style="float: right">
          <el-tabs style="width: 270px; border: 0;">
            <el-select
              v-model="query.searchSelect"
              filterable
              clearable
              collapse-tags
              type="text"
              style="height: 35px; width: 100%; margin: 0;"
              placeholder="请选择"
            >
              <el-option v-for="item in searchSelect" :key="item.id" :value="item.id" :label="item.label"></el-option>
            </el-select>
          </el-tabs>
        </div>
      </div>
      <!--终端未关机分析11111111111111-->
      <div v-if="notOffline" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
            <label :title="item.label">{{ item.label }}</label>
            <span>
              <el-button
                v-for="(btn, i) in item.value"
                :key="i"
                type="text"
                @click="btn.func"
              >{{ itemValue[btn.key] }}</el-button>
            </span>
          </div>
        </div>
        <!--下方对员工，部门，未关机员工的分别展示部分-->
        <div class="data-analysis-panel">
          <div class="data-analysis">
            <div class="mini-title"><span @click="userTitleClick">员工分析>></span></div>
            <div class="table-parent-size">
              <div class="table-container table-left-size">
                <grid-table
                  ref="terminalNotOfflineList"
                  :col-model="colModel"
                  :row-datas="rowData"
                  style="height: calc(100% - 40px);"
                  :show-pager="false"
                />
              </div>
            </div>
          </div>
          <div class="data-analysis">
            <div class="mini-title"><span @click="deptTitleClick">部门分析>></span></div>
            <div style="height: calc(100% - 40px)">
              <bar-chart
                :chart-data="barChartData"
                :chart-option="barChartOption"
                :click="barChartFun"
                :x-axis-name="xAxisName"
                style="width: 100%;height: 100%"
              />
            </div>
          </div>
          <div class="data-analysis">
            <div class="mini-title"><span @click="notOfflineTitleClick">未关机员工数量趋势>></span></div>
            <div style="height: calc(100% - 40px)">
              <line-chart
                :chart-data="lineChartData"
                :chart-option="lineOption"
                style="width: 100%;height: 100%"
              />
            </div>
          </div>
        </div>
      </div>
      <!--员工分析-->
      <div v-if="notOfflineUser" class="detailsDiv">
        <user-analysis/>
      </div>
      <!--部门分析-->
      <div v-if="notOfflineDept" class="detailsDiv">
        <dept-analysis/>
      </div>
      <!--未关机员工数量趋势-->
      <div v-if="notOfflineNum" class="detailsDiv">
        <on-off-trend/>
      </div>
    </div>
    <!--配置-->
    <config-dlg ref="configDlg"/>
    <!--小方块点击弹框-->
    <square-dlg ref="squareDlg" :title="deviceTitle" :dialog-status="dialogStatus"/>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import BarChart from '@/components/ECharts/BarChart'
import configDlg from '@/views/report/terminalNotOffline/notOffline/config'
import squareDlg from '@/views/report/terminalNotOffline/notOffline/squareDlg';
import userAnalysis from '@/views/report/terminalNotOffline/notOffline/userAnalysis';
import deptAnalysis from '@/views/report/terminalNotOffline/notOffline/deptAnalysis';
import onOffTrend from '@/views/report/terminalNotOffline/notOffline/onOffTrend';
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department';

export default {
  name: 'TerminalNotOffline',
  components: { BarChart, LineChart, configDlg, squareDlg, userAnalysis, deptAnalysis, onOffTrend },
  props: {
    xAxisName: {
      type: String,
      default() {
        return '部门'
      }
    }
  },
  data() {
    return {
      colModel: [
        { prop: 'terminalName', label: '姓名' },
        { prop: 'softwareName', label: '未关机天数', sort: true }
      ],
      treeData: [],
      currentNodeKey: '',
      defaultExpandedKeys: [''],
      rowData: [],
      query: { // 查询条件
        page: 1,
        limit: 100,
        sortName: 'id',
        searchInfo: '',
        searchSelect: []
      },
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: '终端未关机分析' }
      ],
      notOffline: true, // 终端未关机分析显示隐藏
      notOfflineUser: false, // 员工分析显示隐藏
      notOfflineDept: false, // 部门分析显示隐藏
      notOfflineNum: false, // 未关机员工数量趋势显示隐藏
      searchSelect: [
        { id: 0, label: '近30天' },
        { id: 2, label: '近20天' }
      ],
      deviceTitle: '', // 弹框名称
      dialogStatus: '', // 弹框状态
      // 终端未关机分析
      itemOption: [
        { label: '未关机总人天', value: [{ key: 'allDay', func: this.allDayFun }] },
        { label: '耗电金额核算', value: [{ key: 'money', func: this.moneyFun }] },
        { label: '未关机总时间', value: [{ key: 'time', func: this.timeFun }] }
      ],
      itemValue: {
        allDay: '42人天', // 未关机总人天
        money: '188.25元', // 耗电金额核算
        time: '410h51min' // 未关机总时间
      },
      // 柱状图数据
      barChartData: [120, 200, 150, 80],
      barChartOption: {
        title: {
          'text': '未关机部门数量图',
          'subtext': '最近7天'
        },
        xAxis: {
          type: 'category',
          data: ['研发一部', '研发二部', '研发三部', '研发四部'],
          axisLabel: {
            formatter: function(value, index) {
              if (value.length > 3) {
                return value.substr(0, 2) + '...'
              } else {
                return value
              }
            }
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [120, 200, 150, 80],
            type: 'bar'
          }
        ]
      },
      // 折线图数据
      lineChartData: [150, 230, 224, 218, 135, 147, 260],
      lineOption: {
        title: {
          'text': '未关机员工数量趋势图',
          'subtext': '最近7天'
        },
        xAxis: {
          type: 'category',
          data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
          }
        ]
      },
      temp: {},
      defaultTemp: { // 表单字段

      },
      submitable: false,
      showTree: true,
      dialogFormVisible: false,
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['terminalNotOfflineList']
    }
  },
  watch: {
    '$store.getters.deptTree'() {
      this.initGroupTreeNode()
    }
  },
  created() {
    this.initGroupTreeNode()
  },
  activated() {},
  methods: {
    reloadTable() {
      this.tableLoading = true
      const searchQuery = Object.assign({}, this.query)
      return searchQuery
    },
    /**
     * 获取左侧树信息
     * */
    initGroupTreeNode() {
      getDeptTreeFromCache().then(respond => {
        this.treeData = respond.data
      })
    },
    /**
     * 左侧树点击
     * @param data
     * @param node
     * @param el
     */
    handleNodeClick(data, node, el) {
      console.log('点击', data)
    },
    /**
     * 未关机总人天(点击)
     * deviceTitle：弹框打开时标题
     * dialogStatus：区分点击的哪个小方块，共用一个弹框表格，控制某一列的显示隐藏
     * show(1)：传的1，2，3可以理解为到时候掉接口的一个参数，区分类型，如果后期不用可去掉
     */
    allDayFun(e) {
      // console.log('e', e)
      this.deviceTitle = '未关机总人天'
      this.dialogStatus = 'allDay'
      this.$refs.squareDlg.show(1)
    },
    /**
     * 耗电金额核算
     */
    moneyFun(e) {
      // console.log('e', e)
      this.deviceTitle = '耗电金额核算'
      this.dialogStatus = 'money'
      this.$refs.squareDlg.show(2)
    },
    /**
     * 未关机总时间
     */
    timeFun(e) {
      // console.log('e', e)
      this.deviceTitle = '未关机总时间'
      this.dialogStatus = 'time'
      this.$refs.squareDlg.show(3)
    },
    /**
     * 柱状图点击，列表弹框
     * */
    barChartFun(params) {
      console.log('params', params)
      if (params.name) {
        this.deviceTitle = '未关机部门数量'
        this.dialogStatus = 'dept'
        this.$refs.squareDlg.show(4)
      }
    },
    /**
     * 员工分析点击
     * 显示员工分析，面包屑中添加部门分析
     * */
    userTitleClick() {
      this.notOffline = false
      this.notOfflineUser = true
      this.showFilePath.push({ id: 1, label: '员工分析' })
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    deptTitleClick() {
      this.notOffline = false
      this.notOfflineDept = true
      this.showFilePath.push({ id: 2, label: '部门分析' })
    },
    /**
     * 未关机员工数量趋势点击
     * 显示未关机员工数量趋势，面包屑中添加部门分析
     * */
    notOfflineTitleClick() {
      this.notOffline = false
      this.notOfflineNum = true
      this.showFilePath.push({ id: 3, label: '未关机员工数量趋势' })
    },
    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        this.notOffline = true
        this.notOfflineUser = false
        this.notOfflineDept = false
        this.notOfflineNum = false
        this.showFilePath = [{ id: 0, label: '终端未关机分析' }]
      }
      if (index === 1) {
        this.notOffline = false
        this.notOfflineUser = true
        this.notOfflineDept = false
        this.notOfflineNum = false
      }
      if (index === 2) {
        this.notOffline = false
        this.notOfflineUser = false
        this.notOfflineDept = true
        this.notOfflineNum = false
      }
      if (index === 3) {
        this.notOffline = false
        this.notOfflineUser = false
        this.notOfflineDept = false
        this.notOfflineNum = true
      }
    },
    /**
     * 配置
     */
    btnConfig() {
      this.$refs.configDlg.show()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    }
  }
}
</script>

<style lang='scss' scoped>
>>>.el-breadcrumb{
  line-height: 40px;
}
.detailsDiv{
  height: 100%;
  border: 1px solid #666666;
}
>>>.el-breadcrumb__inner a{
  color: #68a8d0 ;
}
/*小方格样式*/
.data-panel{
  width: 100%;
  padding: 20px;
  display: flex;
}
.data-item{
  flex: 1;
  height: 100px;
  line-height: 100px;
  border: 1px solid #3b749c;
  display: inline-block;
  margin-left: 1%;
  font-size: 16px;
  display: flex;
  &:first-child{
    margin-left: 0;
  }
  label{
    line-height: 15px;
    align-self: center;
    font-weight: normal;
    flex: 2;
    text-align: center;
    font-size: 12px;
  }
  span{
    flex: 1;
    text-align: center;
    font-size: 12px;
  }
}
@media screen and (max-width: 1500px){
  .data-item{
    flex-direction: column;
    line-height: 50px;
  }
}
.data-analysis-panel{
  width: 100%;
  height: calc(100% - 140px);
  padding:0 20px 20px 20px;
  display: flex;
}
.data-analysis{
  flex: 1;
  height: 100%;
}
.mini-title{
  height: 30px;
  line-height: 30px;
  padding-left: 20px;
  margin-bottom: 10px;
  span{
    font-weight: bold;
    color: #68a8d0;
    cursor: pointer;
  }
}
.data-details{
  width: 100%;
  height: 100%;
  padding:0 20px 20px 20px;
  display: flex;
}
.data-analysis-table{
  flex: 1;
}
.data-analysis-table >>>.el-table{
  top: 20px;
}
.data-analysis-details{
  flex: 2;
}
/*解决表格宽度随浏览器大小变化自适应问题*/
.table-parent-size{
  position: relative;
  width: 100%;
  height: 100%;
}
.table-left-size{
  width: 100%;
  height: calc(100% - 35px);
  position: absolute;
}
>>>.el-pagination{
  overflow-x: auto;
  overflow-y: hidden;
}
</style>
