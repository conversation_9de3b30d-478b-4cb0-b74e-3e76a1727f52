<template>
  <!--未关机员工数量趋势-->
  <div style="height: 100%">
    <!--未关机员工数量趋势-->
    <div class="data-details">
      <line-chart
        :chart-data="lineChartData"
        :chart-option="lineOption"
        style="height: 100%"
      />
    </div>
    <div class="data-details">
      <!--未关机原因-->
      <div class="data-analysis-details">
        <pie-chart
          :chart-data="chartsDatas"
          :chart-option="chartOption"
          style="height: 350px"
        />
      </div>
      <!--加班分析-->
      <div class="data-analysis-details">
        <pie-chart
          :chart-data="chartsWorkDatas"
          :chart-option="chartWorkOption"
          style="height: 350px"
        />
      </div>
      <!--用户行为分析-->
      <div class="data-analysis-details">
        <radar-chart
          :chart-indicator="chartIndicator"
          :chart-data="chartsDatasRadar"
          :chart-option="chartOptionRadar"
          style="height: 350px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import PieChart from '@/components/ECharts/PieChart'
import RadarChart from '@/components/ECharts/RadarChart';
export default {
  name: 'OnOffTrend',
  components: { LineChart, PieChart, RadarChart },
  data() {
    return {
      // 折线图数据
      lineChartData: [150, 230, 224, 218, 135, 147, 260],
      lineOption: {
        title: {
          'text': '未关机员工数量趋势图',
          'subtext': '最近7天'
        },
        xAxis: {
          type: 'category',
          data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
          }
        ]
      },
      // 饼图 未关机原因
      chartsDatas: [
        { value: 100, name: '加班' },
        { value: 85, name: '跑批' },
        { value: 45, name: '忘记关机' }
      ],
      chartOption: {
        title: {
          text: '未关机原因',
          left: 'center'
        },
        toolbox: {
          show: false
        },
        legend: {
          left: 'center'
        },
        series: [
          {
            radius: '30%'
          }
        ]
      },
      // 饼图 加班分析
      chartsWorkDatas: [
        { value: 100, name: '工作' },
        { value: 85, name: '浏览新闻' },
        { value: 15, name: '待机' },
        { value: 25, name: '聊天' },
        { value: 35, name: '追剧' },
        { value: 15, name: '网购' }
      ],
      chartWorkOption: {
        title: {
          text: '加班分析',
          left: 'center'
        },
        toolbox: {
          show: false
        },
        legend: {
          left: 'center'
        },
        series: [
          {
            radius: '30%'
          }
        ]
      },
      // 雷达图 用户行为分析
      chartIndicator: [
        { name: '文件传送', max: 6500 },
        { name: '网络访问', max: 16000 },
        { name: '进程行为', max: 30000 },
        { name: 'U盘操作行为', max: 38000 },
        { name: '打印操作', max: 52000 },
        { name: '敏感文件异常行为', max: 25000 }
      ],
      chartsDatasRadar: [
        {
          name: '用户行为分析',
          value: [4200, 3000, 20000, 35000, 50000, 18000],
          areaStyle: {
            color: '#32dadd'
          }
        }
      ],
      chartOptionRadar: {
        title: {
          text: '用户行为分析',
          left: 'center'
        },
        radar: {
          radius: '40%'
        },
        toolbox: {
          show: false
        }
      }
    }
  },
  computed: {

  },
  created() {

  },
  activated() {},
  methods: {

  }
}
</script>

<style lang='scss' scoped>
  .data-analysis-panel{
    width: 100%;
    height: calc(100% - 140px);
    padding:0 20px 20px 20px;
    display: flex;
  }
  .data-analysis{
    flex: 1;
    height: 100%;
  }
  .data-details{
    width: 100%;
    height: 50%;
    padding:0 20px 20px 20px;
    display: flex;
  }
  .data-analysis-details{
    flex: 1;
    border: 1px solid #3b749c;
  }
</style>
