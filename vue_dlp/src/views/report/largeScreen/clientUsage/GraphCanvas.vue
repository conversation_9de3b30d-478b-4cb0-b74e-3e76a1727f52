<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import { debounce } from '@/utils'

require('echarts/theme/macarons') // echarts theme
export default {
  name: 'GraphCanvas',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    },
    chartDatas: {
      type: Array,
      default() {
        return []
      }
    },
    chartLinks: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)
    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions() {
      // console.log('chartDatas', JSON.parse(JSON.stringify(this.chartDatas)))
      // console.log('chartLinks', JSON.parse(JSON.stringify(this.chartLinks)))
      const data = this.chartDatas
      const links = this.chartLinks
      /**
       * 自定义图片
       */
      if (data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i].symbol = 'image://' + require('@/assets/service.png')
          if (data[i].status === 0) {
            data[i].symbolSize = 50;
          }
        }
      }
      const defaultOpt = {
        tooltip: {
          trigger: 'item'
        },
        toolbox: {
          show: true,
          top: 20,
          left: 20,
          feature: {
            show: false
          }
        },
        color: ['#09022C',
          '#040193',
          '#073CFE',
          '#0065C2'],
        series: [{// 图片配置
          type: 'graph', // 关系图
          // name : "拓扑图", //系列名称，用于tooltip的显示，legend 的图例筛选，在 setOption 更新数据和配置项时用于指定对应的系列。
          layout: 'force', // 图的布局，类型为力导图，'circular' 采用环形布局，见示例 Les Miserables
          legendHoverLink: false, // 是否启用图例 hover(悬停) 时的联动高亮。
          hoverAnimation: true, // 是否开启鼠标悬停节点的显示动画
          coordinateSystem: null, // 坐标系可选
          xAxisIndex: 0, // x轴坐标 有多种坐标系轴坐标选项
          yAxisIndex: 0, // y轴坐标
          force: {
            repulsion: 150, // 相距距离
            edgeLength: [50, 100],
            layoutAnimation: true
          },
          roam: true, // 是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
          nodeScaleRatio: 0.6, // 鼠标漫游缩放时节点的相应缩放比例，当设为0时节点不随着鼠标的缩放而缩放
          draggable: true, // 节点是否可拖拽，只在使用力引导布局的时候有用。
          focusNodeAdjacency: true, // 是否在鼠标移到节点上的时候突出显示节点以及节点的边和邻接节点。
          edgeSymbol: ['none', 'arrow'], // 边两端的标记类型，可以是一个数组分别指定两端，也可以是单个统一指定。默认不显示标记，常见的可以设置为箭头，如下：edgeSymbol: ['circle', 'arrow']
          symbolSize: [30, 30], // 图形大小
          edgeSymbolSize: 5, // 边两端的标记大小，可以是一个数组分别指定两端，也可以是单个统一指定。
          lineStyle: { // todo==========节点连线样式。
            normal: {
              color: '#027ab5',
              width: '1',
              type: 'solid', // 线的类型 'solid'（实线）'dashed'（虚线）'dotted'（点线）
              curveness: 0, // 线条的曲线程度，从0到1
              opacity: 1
              // 图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。默认0.5
            },
            emphasis: {// 高亮状态

            }
          },
          label: { // todo=============图形上的文本标签(图片名称)
            normal: {
              show: true, // 是否显示标签。
              position: 'bottom', // 标签的位置。['50%', '50%'] [x,y]   'inside'
              textStyle: { // 标签的字体样式
                color: '#027ab5', // 字体颜色
                fontStyle: 'normal', // 文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                fontWeight: 'bolder', // 'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                fontFamily: 'sans-serif', // 文字的字体系列
                fontSize: 12 // 字体大小
              }
            },
            emphasis: {// 高亮状态

            }
          },
          data: data,
          links: links // edges是其别名代表节点间的关系数据。
        }]

      }
      this.chart.setOption(defaultOpt, true)
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.setOptions()
    }
  }
}
</script>

