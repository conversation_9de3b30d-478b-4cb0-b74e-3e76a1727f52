<!--使用关系图做出类似气泡图的效果，不用自己设置坐标-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<!--chartDatas数据格式：
[
{ name: '外传文件', value: 8340 },
{ name: 'IM文件', value: 10 },
{ name: '大文件', value: 520 },
{ name: '拷贝文件', value: 230 }
]-->
<script>
import echarts from 'echarts'
import { debounce } from '@/utils'

require('echarts/theme/macarons') // echarts theme
export default {
  name: 'GraphBubble',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    },
    chartDatas: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartDatas: {
      deep: true,
      handler(val) {
        this.setOptions()
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)
    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    /**
     * 1、计算最大公约数
     * @param arr 数组，如：[48, 18, 42]
     * @returns {*}  返回如[48, 18, 42]的最大公约数为6
     */
    gcdArray(arr) {
      function gcd(a, b) {
        while (b != 0) {
          const temp = b;
          b = a % b;
          a = temp;
        }
        return a;
      }
      return arr.reduce((a, b) => gcd(a, b));
    },
    /**
     * 2、数组中每一个数，都除以最大公约数，获取到一个新数组
     * @param arr  数组
     * @param num  最大公约数
     * @returns {*} 返回新数组
     */
    divideArrayByNumber(arr, num) {
      return arr.map(function(item) {
        return item / num;
      });
    },
    /**
     * 节点大小处理
     * 根据每个节点的数量，计算一个差不多的节点大小，进行页面显示显示
     */
    dataSize() {
      // 所有节点数据
      const data = this.chartDatas
      // 0、获取到节点中的数量，放到一个新数组中
      const dataNumber = []
      if (data.length > 0) {
        data.forEach(item => {
          dataNumber.push(item.value)
        })
      }
      // console.log('dataNumber', dataNumber)
      // console.log('data', JSON.parse(JSON.stringify(data)))
      // 下面差不多都是对0中的数据做处理
      // 1、最大公约数
      const greatestCommonDivisor = this.gcdArray(dataNumber)
      // 2、除以最大公约数后，获取到的新数组
      const newNumberArray = this.divideArrayByNumber(dataNumber, greatestCommonDivisor)
      // 3、获取2中的最大长度
      const maxLength = Math.max(...newNumberArray.map(item => String(item).length));
      // 4、对3中的最大长度处理，即如果是1，返回1，如果是2，返回10，如果是3，返回100，如果是4返回1000
      const convert = Math.pow(10, maxLength - 1)
      // 5、最终数据（给数组中每个数据除4中的数据）
      const resultArray = this.divideArrayByNumber(newNumberArray, convert)
      if (dataNumber.length > 0 && data.length > 0) {
        for (let i = 0; i < dataNumber.length; i++) {
          for (let j = 0; j < data.length; j++) {
            if (data[j].value == dataNumber[i]) {
              // 设置节点大小，处理好的数据稍微有点小，在原基础上又乘了个5
              data[j].symbolSize = Number(20 + resultArray[i] * 5)
            }
          }
        }
      }
    },
    /**
     * 动态修改每个节点的颜色
     */
    dataColor() {
      // 所有节点数据
      const data = this.chartDatas
      if (data.length > 0) {
        data.forEach(function(node) {
          node.itemStyle = {
            // 随机生成16进制颜色
            color: '#' + Math.floor(Math.random() * 16777215).toString(16).padEnd(6, '0')
            // color: `#${Math.floor(Math.random() * 0x1000000).toString(16).padEnd(6, '0')}`
          }
        })
      }
      // console.log('node111', JSON.parse(JSON.stringify(data)))
    },
    setOptions() {
      // 所有节点数据
      const data = this.chartDatas
      if (data.length > 0) {
        this.dataSize()
        this.dataColor()
        const defaultOpt = {
          tooltip: {
            trigger: 'item'
          },
          toolbox: {
            show: true,
            top: 20,
            left: 20,
            feature: {
              show: false
            }
          },
          series: [{// 图片配置
            type: 'graph', // 关系图
            // name : "拓扑图", //系列名称，用于tooltip的显示，legend 的图例筛选，在 setOption 更新数据和配置项时用于指定对应的系列。
            layout: 'force', // 图的布局，类型为力导图，'circular' 采用环形布局，见示例 Les Miserables
            legendHoverLink: false, // 是否启用图例 hover(悬停) 时的联动高亮。
            hoverAnimation: true, // 是否开启鼠标悬停节点的显示动画
            coordinateSystem: null, // 坐标系可选
            xAxisIndex: 0, // x轴坐标 有多种坐标系轴坐标选项
            yAxisIndex: 0, // y轴坐标
            force: {
              repulsion: 50, // 相距距离
              edgeLength: [50, 100],
              layoutAnimation: true
            },
            roam: true, // 是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
            zoom: 0.8, // 初始化缩放
            nodeScaleRatio: 0.6, // 鼠标漫游缩放时节点的相应缩放比例，当设为0时节点不随着鼠标的缩放而缩放
            draggable: false, // 节点是否可拖拽，只在使用力引导布局的时候有用。
            focusNodeAdjacency: false, // 是否在鼠标移到节点上的时候突出显示节点以及节点的边和邻接节点。
            edgeSymbol: ['none', 'arrow'], // 边两端的标记类型，可以是一个数组分别指定两端，也可以是单个统一指定。默认不显示标记，常见的可以设置为箭头，如下：edgeSymbol: ['circle', 'arrow']
            symbolSize: [30, 30], // 图形大小
            edgeSymbolSize: 5, // 边两端的标记大小，可以是一个数组分别指定两端，也可以是单个统一指定。
            lineStyle: { // todo==========节点连线样式。
              normal: {
                color: '#027ab5',
                width: '1',
                type: 'solid', // 线的类型 'solid'（实线）'dashed'（虚线）'dotted'（点线）
                curveness: 0, // 线条的曲线程度，从0到1
                opacity: 1
                // 图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。默认0.5
              },
              emphasis: {// 高亮状态

              }
            },
            label: { // 图形上的文本标签(图片名称)
              normal: {
                show: true, // 是否显示标签。
                position: 'bottom', // 标签的位置。['50%', '50%'] [x,y]   'inside'
                // position: 'bottom', // 标签的位置。['50%', '50%'] [x,y]   'inside'
                textStyle: { // 标签的字体样式
                  color: 'orange', // 字体颜色
                  fontStyle: 'normal', // 文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                  fontWeight: 'normal', // 'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                  fontFamily: 'sans-serif', // 文字的字体系列
                  fontSize: 10 // 字体大小
                }
              },
              emphasis: {// 高亮状态

              }
            },
            data: data,
            links: [] // edges是其别名代表节点间的关系数据。
          }]

        }
        this.chart.setOption(defaultOpt, true)
      }
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.setOptions()
    }
  }
}
</script>

