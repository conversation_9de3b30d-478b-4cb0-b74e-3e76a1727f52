<template>
  <!--设置parentContent的宽高为浏览器大小-->
  <div ref="parentContent" class="parentContent">
    <div id="container" ref="container"></div>
  </div>
</template>
<script>
import G6 from '@antv/g6'
export default {
  name: 'ServerG6',
  data() {
    return {
      data: {}, // 拓扑图数据
      graph: undefined, // new G6
      canvasWidth: 0, // 画布宽度
      canvasHeight: 0 // 画布高度
    }
  },
  mounted() {
    this.initComponent()
    this.initSize()
  },
  methods: {
    /**
     * 设置画布大小自适应
     */
    initSize() {
      const self = this
      setTimeout(() => {
        // todo 浏览器窗口发生变化时
        window.onresize = function() {
          // todo 获取div parentContent 的宽度和高度
          self.canvasWidth = self.$refs.parentContent.clientWidth
          self.canvasHeight = self.$refs.parentContent.clientHeight
          // todo 修改画布的大小
          self.graph.changeSize(self.canvasWidth, self.canvasHeight)
          // todo 将图移动到画布中心位置
          self.graph.fitCenter()
        }
      }, 20)
    },
    /**
       * 创建G6，并对G6的一些设置
       * */
    initComponent() {
      this.data = {
        nodes: [
          {
            id: 'node1',
            label: '主服务器',
            status: 0
          },
          {
            id: 'node2',
            label: '300',
            status: 1
          },
          {
            id: 'node3',
            label: '200',
            status: 2
          },
          {
            id: 'node4',
            label: '512',
            status: 3
          }
        ],
        edges: [
          {
            source: 'node1',
            target: 'node2'
          },
          {
            source: 'node1',
            target: 'node3'
          },
          {
            source: 'node1',
            target: 'node4'
          }
        ]
      }
      /**
         * 遍历节点数据，给节点添加图片
         */
      for (let i = 0, len = this.data.nodes.length; i < len; i++) {
        // eslint-disable-next-line eqeqeq
        if (this.data.nodes[i].status == 0) { // 'offlineAnomaly'
          this.data.nodes[i].img = require('@/assets/service1.png')
        } else {
          this.data.nodes[i].img = require('@/assets/service.png')
        }
      }
      // todo 初始化画布宽高为div parentContent 的宽度和高度
      this.canvasWidth = this.$refs.parentContent.clientWidth
      this.canvasHeight = this.$refs.parentContent.clientHeight
      this.graph = new G6.Graph({
        container: 'container',
        width: this.canvasWidth,
        height: this.canvasHeight,
        linkCenter: true,
        plugins: [], // 配置 Tooltip 插件
        modes: {
          default: ['drag-canvas', 'zoom-canvas', 'drag-node', 'activate-relations'] // 允许拖拽画布、放缩画布、拖拽节点、设置高亮
        },
        layout: {
          type: 'force',
          preventOverlap: true, // 防止节点重叠
          // 防碰撞必须设置nodeSize或size,否则不生效，由于节点的size设置了40，虽然节点不碰撞了，但是节点之间的距离很近，label几乎都挤在一起，所以又重新设置了大一点的nodeSize,这样效果会好很多
          nodeSize: 100,
          linkDistance: 50 // 指定边距离为150
        },
        defaultNode: { // 节点样式修改
          type: 'image', // 设置节点为图片
          size: [30, 30], // 节点大小
          labelCfg: { // 修改节点label样式
            style: {
              fill: '#5B8FF9', // 字体颜色
              fontSize: 14 // 字体大小
            }
          }
        },
        defaultEdge: {
          style: {
            lineWidth: 2, // 线宽
            stroke: '#1296db'
          }
        }
      })
      // 接收数据并渲染
      this.graph.data(this.data)
      this.graph.render()
    }
  }
}
</script>

<style>
  .parentContent {
    width: 100%;
    height: 100%;
  }
</style>
