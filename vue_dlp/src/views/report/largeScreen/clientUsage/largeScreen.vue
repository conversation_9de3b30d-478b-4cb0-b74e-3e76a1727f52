<template>
  <div :style="{ height: appContainerHeight}" class="app-container largeScreen-report">
    <div class="table-container">
      <div class="large-screen-head">
        <div class="large-screen-head-title">终端使用情况数据</div>
        <div class="large-screen-head-time">
          <div>最后更新时间：{{ temp.lastUpdateTime }}</div>
          <div>系统当前时间：{{ temp.currentTime }}</div>
        </div>
        <div class="large-screen-head-search">
          <query ref="queryData" @thisQuery="thisQuery"/>
        </div>
      </div>
      <div class="large-screen-body">
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">操作系统版本统计</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.barChartOperatingSystemData"
                  :chart-option="temp.barChartOperatingSystemOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-name">{{ item.currentName }}</div>
                <div class="large-screen-middle-num">{{ item.currentNum }}</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">在线终端Top10情况</div>
              </div>
              <div class="large-screen-chart-div">
                <div class="div_risk_ranking_item" style="color: orange">
                  <span>终端名称</span>
                  <span>终端分组</span>
                  <span>在线时长</span>
                </div>
                <div :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.onlineDatas" :class-option="classOption">
                    <div v-for="(item, index) in temp.onlineDatas" :key="index" class="div_risk_ranking_item">
                      <span :title="item.terminal">{{ item.terminal }}</span>
                      <span :title="item.terminalGroup">{{ item.terminalGroup }}</span>
                      <span :title="item.time">{{ item.time }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">终端连接服务器情况</div>
              </div>
              <div class="large-screen-chart-div">
                <graph-canvas
                  :chart-datas="temp.serviceData"
                  :chart-links="temp.serviceLink"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">按部门统计终端情况</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.barChartDeptData"
                  :chart-option="temp.barChartDeptOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">离线终端时长分布</div>
              </div>
              <div class="large-screen-chart-div">
                <graph-bubble
                  :chart-datas="temp.timeData"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">终端升级情况</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.upgradePieData"
                  :chart-option="temp.upgradePieOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">终端在线及活动趋势</div>
              </div>
              <div class="large-screen-chart-div">
                <line-chart
                  :chart-data="temp.lineChartOnlineData"
                  :chart-option="temp.lineOnlineOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">终端接入方式</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.accessModePieData"
                  :chart-option="temp.accessModePieOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import Query from '@/views/report/largeScreen/common/query'
import LineChart from '@/views/report/largeScreen/common/LineChart'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import PieChart from '@/views/report/largeScreen/common/PieChart'
import GraphCanvas from '@/views/report/largeScreen/clientUsage/GraphCanvas';
import GraphBubble from '@/views/report/largeScreen/clientUsage/GraphBubble';
import { getCurrentTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
export default {
  name: 'LargeScreen',
  components: { Query, LineChart, BarChart, GraphBubble, GraphCanvas, PieChart, vueSeamlessScroll },
  props: {
  },
  data() {
    return {
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight: '19vh',
      middleOutHeight: '7.1vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      // 查询条件
      tempQuery: {},
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: '',
        currentTime: '',
        middleMsg: [
          { currentName: '在线终端数', currentNum: '89000' },
          { currentName: '离线终端数', currentNum: '4551' },
          { currentName: '在线终端时长', currentNum: '1245小时' },
          { currentName: '离线终端时长', currentNum: '451285小时' },
          { currentName: '终端接入数', currentNum: '4521' },
          { currentName: '终端升级数', currentNum: '3541' }
        ],
        // 按操作系统版本统计(柱状图)
        barChartOperatingSystemData: [8, 45, 66, 95, 125, 165],
        barChartOperatingSystemOption: {
          yAxis: {
            type: 'category',
            data: ['xp', 'linux', 'centos7.0', 'win10', 'win11', 'win7']
          },
          xAxis: {
            type: 'value'
          },
          series: [
            {
              data: [8, 45, 66, 95, 125, 165]
            }
          ]
        },
        // 终端在线情况
        onlineDatas: [
          { sort: 1, terminal: '小新', terminalGroup: '研发一部', time: '156小时27分32秒' },
          { sort: 2, terminal: '小黄', terminalGroup: '研发二部', time: '123小时27分32秒' },
          { sort: 3, terminal: '小黑', terminalGroup: '测试部', time: '112小时27分32秒' },
          { sort: 4, terminal: '小康', terminalGroup: '市场部', time: '95小时27分32秒' },
          { sort: 5, terminal: '小莉', terminalGroup: '行政部', time: '64小时27分32秒' },
          { sort: 6, terminal: '小妖', terminalGroup: '研发四部', time: '32小时27分32秒' }
        ],
        // 终端连接服务器情况(关系图)
        serviceData: [
          { id: 'node1', name: '主服务器', status: 0, value: 123 },
          { id: 'node2', name: '采集1(在线:200)', status: 1, value: 258 },
          { id: 'node3', name: '采集2(在线:120)', status: 2, value: 451 },
          { id: 'node4', name: '采集3(在线:30)', status: 3, value: 148 }
        ],
        serviceLink: [
          { source: 'node1', target: 'node2' },
          { source: 'node1', target: 'node3' },
          { source: 'node1', target: 'node4' }
        ],
        // 离线终端时长分布
        timeData: [
          { id: 1, name: '一周以内', value: 8340 },
          { id: 2, name: '一个月以内', value: 10 },
          { id: 3, name: '1-3个月', value: 520 },
          { id: 4, name: '1年以上', value: 230 }
        ],
        // 按部门统计终端情况(柱状图)
        barChartDeptData: [120, 200, 150, 80, 100],
        barChartDeptOption: {
          xAxis: {
            data: ['研发一部', '研发二部', '研发三部', '研发四部', '测试部']
          },
          series: [
            {
              data: [120, 200, 150, 80, 100]
            }
          ]
        },
        // 终端升级情况(饼图)
        upgradePieData: [
          { value: 50, name: '升级成功' },
          { value: 12, name: '升级成功但未重启电脑' },
          { value: 45, name: '正在执行安装包' },
          { value: 32, name: '正在下载安装包' },
          { value: 42, name: '等待下载安装包' },
          { value: 21, name: '开始执行安装包' }
        ],
        upgradePieOption: {
          series: [
            {
              radius: ['20%', '50%']
            }
          ]
        },
        // 终端在线及活动趋势(折线图)
        lineChartOnlineData: [],
        lineOnlineOption: {
          legend: {
            data: ['在线', '活动']
          },
          xAxis: {
            type: 'category',
            data: ['2024-2-22', '2024-2-21', '2024-2-20', '2024-2-19', '2024-2-18', '2024-2-17', '2024-2-16']
          },
          series: [
            {
              name: '在线',
              data: [150, 230, 224, 218, 135, 147, 260],
              type: 'line'
            },
            {
              name: '活动',
              data: [213, 123, 12, 34, 321, 421, 42],
              type: 'line'
            }
          ]
        },
        // 终端接入方式（饼图）
        accessModePieData: [
          { value: 50, name: '新终端自动接入' },
          { value: 12, name: '旧终端自动接入' },
          { value: 45, name: '手动审批接入' },
          { value: 32, name: '手动删除' },
          { value: 42, name: '手动清理' },
          { value: 21, name: '自动清理' },
          { value: 53, name: '彻底删除(回收)' },
          { value: 23, name: '手动恢复' },
          { value: 53, name: '自动恢复' }
        ],
        accessModePieOption: {
          series: [
            {
              radius: ['30%', '50%']
            }
          ]
        }
      }
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
    this.getReportInfo()
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
    this.temp.currentTime = getCurrentTime()
  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
  },
  mounted() {
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  methods: {
    /**
     * 获取接口参数（子组件点击查询时获取）
     * @param data 参数
     * */
    thisQuery(data) {
      this.tempQuery = data
    },
    /**
     * 获取页面数据
     * */
    getLargeScreenData() {
      this.$nextTick(() => {
        this.tempQuery = Object.assign({}, this.$refs['queryData'].getQuery())
        // console.log('this.tempQuery', this.tempQuery)
      })
    },
    /**
       * 系统当前时间，实时获取，实时刷新
       * */
    getTimes() {
      setInterval(this.getSystemTime, 1000)
    },
    /**
       * 系统当前时间，赋值
       * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
       * 获取页面数据
       * */
    getReportInfo() {
      // 最后更新于
      this.temp.lastUpdateTime = getCurrentTime()
    },
    /**
       * 判断是否全屏（监听全屏）
       * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
       * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
       * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight = '23.5vh'
        this.middleOutHeight = '8.5vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight = '19vh'
        this.middleOutHeight = '7.1vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  //中间特殊的小块
  .large-screen-middle-out{
    width: 49%;
    float: left;
    div{
      width: 90%;
      margin: auto;
      font-size: 15px;
      height: 60%; /* 设置div的高度为50% */
      display: flex; /* 使用flex布局 */
      align-items: center; /* 垂直居中 */
    }
    .large-screen-middle-num{
      font-size: 22px;
    }
    .large-screen-middle-name{
      font-size: 14px;
      height: 40%; /* 设置div的高度为50% */
      display: flex; /* 使用flex布局 */
      align-items: center; /* 垂直居中 */
    }
  }
</style>
