<template>
  <div :style="{ height: appContainerHeight}" class="app-container largeScreen-report">
    <div class="table-container">
      <div class="large-screen-head">
        <div class="large-screen-head-title">移动存储使用数据</div>
        <div class="large-screen-head-time">
          <div>最后更新时间：{{ temp.lastUpdateTime }}</div>
          <div>系统当前时间：{{ temp.currentTime }}</div>
        </div>
        <div class="large-screen-head-search">
          <query/>
        </div>
      </div>
      <div class="large-screen-body">
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">按设备类型统计</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.deviceTypeData"
                  :chart-option="temp.deviceTypeOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-name">{{ item.currentName }}</div>
                <div class="large-screen-middle-num">{{ item.currentNum }}</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">移动存储使用情况</div>
              </div>
              <div class="large-screen-chart-div">
                <div class="div_risk_ranking_item" style="color: orange">
                  <span>终端名称</span>
                  <span>终端分组</span>
                  <span>拷贝文件数量</span>
                  <span>编辑文件数量</span>
                </div>
                <div :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.useDatas" :class-option="classOption">
                    <div v-for="(item, index) in temp.useDatas" :key="index" class="div_risk_ranking_item">
                      <span :title="item.terminal">{{ item.terminal }}</span>
                      <span :title="item.terminalGroup">{{ item.terminalGroup }}</span>
                      <span :title="item.copy">{{ item.copy }}</span>
                      <span :title="item.edit">{{ item.edit }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">按操作类型统计</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.operateTypeData"
                  :chart-option="temp.operateTypeOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">设备插入次数Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.insertionTimesData"
                  :chart-option="temp.insertionTimesOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">操作总文件数量Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.fileNumData"
                  :chart-option="temp.fileNumOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="24">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">近7日拷贝总趋势</div>
              </div>
              <div class="large-screen-chart-div">
                <line-chart
                  :chart-data="temp.loginTimesData"
                  :chart-option="temp.loginTimesOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import Query from '@/views/report/largeScreen/common/query'
import LineChart from '@/views/report/largeScreen/common/LineChart'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import PieChart from '@/views/report/largeScreen/common/PieChart'
import { getCurrentTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
export default {
  name: 'LargeScreen',
  components: { Query, BarChart, PieChart, LineChart, vueSeamlessScroll },
  props: {
  },
  data() {
    return {
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight: '19vh',
      middleOutHeight: '7.1vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: '',
        currentTime: '',
        middleMsg: [
          { currentName: '设备插入次数', currentNum: '89000' },
          { currentName: '创建文件数量', currentNum: '8423' },
          { currentName: '拷贝文件数量', currentNum: '3123' },
          { currentName: '打开文件数量', currentNum: '423' },
          { currentName: '编辑文件数量', currentNum: '1245' },
          { currentName: '删除文件数量', currentNum: '3541' }
        ],
        // 按设备类型统计(饼图)
        deviceTypeData: [
          { value: 1048, name: '移动硬盘' },
          { value: 735, name: 'U盘' }
        ],
        deviceTypeOption: {
          series: [
            {
              radius: ['30%', '50%']
            }
          ]
        },
        // 移动存储使用情况
        useDatas: [
          { sort: 1, terminal: '小新', terminalGroup: '研发一部', copy: '156', edit: '23' },
          { sort: 2, terminal: '小黄', terminalGroup: '研发二部', copy: '123', edit: '12' },
          { sort: 3, terminal: '小黑', terminalGroup: '测试部', copy: '112', edit: '10' },
          { sort: 4, terminal: '小康', terminalGroup: '市场部', copy: '95', edit: '9' },
          { sort: 5, terminal: '小莉', terminalGroup: '行政部', copy: '64', edit: '8' },
          { sort: 6, terminal: '小妖', terminalGroup: '研发四部', copy: '32', edit: '8' }
        ],
        // 按操作类型统计(柱状图)
        operateTypeData: [120, 200, 150, 80, 100, 120],
        operateTypeOption: {
          xAxis: {
            data: ['创建', '打开', '编辑', '删除', '拷贝', '重命名']
          },
          series: [
            {
              data: [120, 200, 150, 80, 100, 120]
            }
          ]
        },
        // 操作总文件数量Top10对象(柱状图)
        fileNumData: [8, 45, 66, 95, 8, 45, 66, 95, 8, 45],
        fileNumOption: {
          xAxis: {
            data: ['小猪', '小新', '小丑', '小贝', '小诺', '小明', '小艾', '小弟', '小幅', '小鬼']
          },
          series: [
            {
              data: [8, 45, 66, 95, 8, 45, 66, 95, 8, 45]
            }
          ]
        },
        // 设备插入次数Top10对象(饼图)
        insertionTimesData: [
          { value: 1048, name: '小数' },
          { value: 735, name: '小弟' },
          { value: 735, name: '小康' },
          { value: 735, name: '小计' },
          { value: 735, name: '小平' },
          { value: 735, name: '小娥' },
          { value: 735, name: '小柔' },
          { value: 735, name: '小媛' },
          { value: 735, name: '小芳' },
          { value: 580, name: '小瓦' }
        ],
        insertionTimesOption: {
          series: [
            {
              radius: '50%',
              center: ['50%', '50%'],
              itemStyle: {
                borderRadius: 8
              }
            }
          ]
        },
        // 近7日拷贝总趋势(折线图)
        loginTimesData: [],
        loginTimesOption: {
          xAxis: {
            data: ['2024-2-22', '2024-2-21', '2024-2-20', '2024-2-19', '2024-2-18', '2024-2-17', '2024-2-16']
          },
          series: [
            {
              data: [150, 230, 224, 218, 135, 147, 260],
              type: 'line'
            }
          ]
        }
      }
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
    this.getReportInfo()
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
    this.temp.currentTime = getCurrentTime()
  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
  },
  mounted() {
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  methods: {
    /**
     * 系统当前时间，实时获取，实时刷新
     * */
    getTimes() {
      setInterval(this.getSystemTime, 1000)
    },
    /**
     * 系统当前时间，赋值
     * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取页面数据
     * */
    getReportInfo() {
      // 最后更新于
      this.temp.lastUpdateTime = getCurrentTime()
    },
    /**
     * 判断是否全屏（监听全屏）
     * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
     * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
     * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      // console.log('this.isFullScreen', this.isFullScreen)
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight = '23.5vh'
        this.middleOutHeight = '8.5vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight = '19vh'
        this.middleOutHeight = '7.1vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  //中间特殊的小块
  .large-screen-middle-out{
    width: 49%;
    float: left;
    div{
      width: 90%;
      margin: auto;
      font-size: 15px;
      height: 35px;
      line-height: 35px;
    }
    .large-screen-middle-num{
      font-size: 22px;
    }
    .large-screen-middle-name{
      font-size: 14px;
      height: 25px;
      line-height: 25px;
    }
  }
</style>

