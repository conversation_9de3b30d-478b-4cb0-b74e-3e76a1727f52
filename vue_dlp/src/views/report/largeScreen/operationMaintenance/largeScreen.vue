<template>
  <div :style="{ height: appContainerHeight}" class="app-container largeScreen-report">
    <div class="table-container">
      <div class="large-screen-head">
        <div class="large-screen-head-title">{{ $t('route.operationMaintenance') }}</div>
        <!--全屏、布局配置-->
        <TopIcoButton/>
        <div class="large-screen-head-time">
          <div>{{ $t('pages.dataWasLastUpdated') }}：{{ temp.lastUpdateTime }}</div>
          <div>{{ $t('pages.currentSystemTime') }}：{{ temp.currentTime }}</div>
        </div>
      </div>
      <div class="large-screen-body">
        <!--页面通过layout动态布局-->
        <el-row v-for="(row, rowIndex) in layout" :key="'row-' + rowIndex" class="large-screen-row" :gutter="row.gutter">
          <el-col v-for="(col, colIndex) in row.cols" :key="'col-' + colIndex" :xs="24" :sm="24" :lg="col.span">
            <!--小方块样式-->
            <!--终端、用户小方块-->
            <div v-if="col.feature ==='dataItemConditionTerminal'" class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg1" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-name">{{ item.currentName }}</div>
                <div class="large-screen-middle-num">{{ item.currentNum }}</div>
              </div>
            </div>
            <!--CPU相关小方块-->
            <div v-if="col.feature ==='dataItemConditionCpu'" class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg2" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-name">
                  {{ item.currentName }}
                  <el-tooltip v-if="index === 0 || index === 1 || index === 2" effect="dark" placement="top-start">
                    <div v-if="index === 0" slot="content">{{ $t('pages.foundationForm_Msg53') }}</div>
                    <div v-if="index === 1" slot="content">{{ $t('pages.foundationForm_Msg54') }}</div>
                    <div v-if="index === 2" slot="content">{{ $t('pages.foundationForm_Msg55') }}</div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </div>
                <div class="large-screen-middle-num" :class="(item.num > 80 && item.flag === 1) ? 'redColor' : '  ' ">{{ item.currentNum }}</div>
              </div>
            </div>
            <!--服务器状态和近7日--切换-->
            <div v-if="col.feature==='serviceCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div v-if="showServer" class="large-screen-title">{{ $t('pages.serverInfo') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleServerOr7days()"></i></div>
                <div v-if="show7Days" class="large-screen-title">{{ $t('pages.changeTrend7Days') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleServerOr7days()"></i></div>
              </div>
              <div v-if="Object.keys(dataTree).length === 0 && showServer" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-if="temp.loginTimesOption.series[0].data.length === 0 && show7Days" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-else class="large-screen-chart-div" @mouseenter="clearServerOr7days('server7day')" @mouseleave="startServerOr7days('server7day')">
                <tree
                  v-if="Object.keys(dataTree).length !== 0 && showServer"
                  :data-tree="dataTree"
                  :style="{ height: chartDivHeight}"
                />
                <line-chart
                  v-if="temp.loginTimesOption.series[0].data.length > 0 && show7Days"
                  :chart-data="temp.loginTimesData"
                  :chart-option="temp.loginTimesOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
            <!--审计日志增长率前五名/后五名--切换-->
            <div v-if="col.feature==='auditLogCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div v-if="auditLogTop5" class="large-screen-title">审计日志增长率(前5名)<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleAuditLog()"></i></div>
                <div v-if="auditLogAfter5" class="large-screen-title">审计日志增长率(后5名)<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleAuditLog()"></i></div>
              </div>
              <div v-if="temp.auditLogTop5Option.series[0].data.length === 0 && auditLogTop5" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-if="temp.auditLogAfter5Option.series[0].data.length === 0 && auditLogAfter5" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div class="large-screen-chart-div" @mouseenter="clearAuditLog('topAfter')" @mouseleave="startAuditLog('topAfter')">
                <bar-chart
                  v-if="temp.auditLogTop5Option.series[0].data.length > 0 && auditLogTop5"
                  :chart-data="temp.auditLogTop5Data"
                  :chart-option="temp.auditLogTop5Option"
                  :y-axis-name="yAxisName"
                  :style="{ height: chartDivHeight}"
                />
                <bar-chart
                  v-if="temp.auditLogAfter5Option.series[0].data.length > 0 && auditLogAfter5"
                  :chart-data="temp.auditLogAfter5Data"
                  :chart-option="temp.auditLogAfter5Option"
                  :y-axis-name="yAxisName"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
            <div v-if="col.feature !=='dataItemConditionTerminal' && col.feature !=='dataItemConditionCpu' && col.feature !=='serviceCondition' && col.feature !== 'auditLogCondition'" class="large-screen-border">
              <!--小方块没有小标题-->
              <div class="large-screen-title-div">
                <div class="large-screen-title">{{ col.name }}</div>
              </div>
              <!--列表样式-->
              <!--重点监控日志-->
              <div v-if="col.feature ==='keyMonitoringLogCondition'" class="large-screen-chart-div">
                <div class="div_risk_ranking_item last_span_no_time" style="color: orange">
                  <span :title="$t('table.terminalName')">{{ $t('table.terminalName') }}</span>
                  <span :title="$t('pages.cpuAlarmTimes')">{{ $t('pages.cpuAlarmTimes') }}</span>
                  <span :title="$t('pages.memoryAlarmCount')">{{ $t('pages.memoryAlarmCount') }}</span>
                  <span :title="$t('pages.diskAlarmCount')">{{ $t('pages.diskAlarmCount') }}</span>
                </div>
                <div v-if="temp.chatRecord.length === 0" :style="{ height: rankingDivHeight}" class="div_risk_ranking no-data">
                  <div>{{ $t('text.noData') }}...</div>
                </div>
                <div v-else :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.chatRecord" :class-option="classOption">
                    <div v-for="(item, index) in temp.chatRecord" :key="index" :class="item.isNew ? 'activeClassRed' : '  '" class="div_risk_ranking_item last_span_no_time">
                      <span :title="item.name">{{ item.name }}</span>
                      <span :title="item.cpu">{{ item.cpu }}{{ $t('pages.openTimes2') }}</span>
                      <span :title="item.memory">{{ item.memory }}{{ $t('pages.openTimes2') }}</span>
                      <span :title="item.disk">{{ item.disk }}{{ $t('pages.openTimes2') }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
              <!--除小方块和列表以外的样式-->
              <div v-else class="large-screen-chart-div">
                <!--磁盘分区-->
                <span v-if="col.feature === 'diskCondition'" class="remark-span" style="position:absolute; top: 10px;right: 10px;">{{ temp.diskConditionNum }}</span>
                <div v-if="col.feature === 'diskCondition'" class="more-pie" :style="{ height: chartDivHeight}">
                  <div v-for="(item, index) in temp.resultPartitionList" :key="index">
                    <pie-disk-chart
                      :chart-color="item.color"
                      :chart-name="item.name"
                      :chart-data="item.data"
                      :chart-option="temp.chartFileTypeOption"
                      style="height: 100%"
                    />
                  </div>
                </div>
                <!--CPU占比及波动趋势-->
                <span v-if="col.feature === 'cpuCondition'" class="remark-span">{{ temp.cpuPieDataName }}</span>
                <PieLineChart v-if="col.feature === 'cpuCondition'" :pie-data="temp.cpuPieData" :chart-data="temp.cpuChart" :style="{ height: chartDivHeight}" />
                <!--磁盘IOTraffic-->
                <span v-if="col.feature === 'diskIOTraffic'" class="remark-span" style="position:absolute; top: 10px;right: 10px;">{{ temp.diskIOTrafficNum }}</span>
                <span v-if="col.feature === 'diskIOTraffic'" class="remark-span">{{ temp.diskIOTrafficName }}</span>
                <line-chart v-if="col.feature === 'diskIOTraffic'" :chart-data="temp.diskIOTrafficData" :chart-option="temp.diskIOTrafficOption" :y-axis-name="yAxisName" :x-axis-name="yAxisName" :style="{ height: chartDivHeight}" />
                <!--磁盘IOPS-->
                <span v-if="col.feature === 'diskIops'" class="remark-span" style="position:absolute; top: 10px;right: 10px;">{{ temp.diskIopsNum }}</span>
                <span v-if="col.feature === 'diskIops'" class="remark-span">{{ temp.diskIopsName }}</span>
                <line-chart v-if="col.feature === 'diskIops'" :chart-data="temp.diskIopsData" :chart-option="temp.diskIopsOption" :y-axis-name="yAxisName" :x-axis-name="yAxisName" :style="{ height: chartDivHeight}" />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--布局配置弹框-->
    <dialog-layout-config ref="dialogLayoutConfig" :original-layout="originalLayout" :layout="orLayout" :parameter-option="parameterOption"/>
  </div>
</template>

<script>
import LineChart from '@/views/report/largeScreen/common/LineChart'
import PieDiskChart from '@/views/report/largeScreen/common/PieDiskChart';
import PieLineChart from '@/components/ECharts/PieLineChart'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import Tree from '@/views/report/largeScreen/operationMaintenance/tree';
import { getCurrentTime, getSetIntervalTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
import {
  displayPanelGetServerResourceInfo,
  displayPanelGetServerInfoForOption,
  displayPanelGetCollectionAndBatchInfo,
  displayPanelGetCollectionAndBatchInfoNoOplog,
  displayPanelGetComputerWarnTimes,
  displayPanelGetTerminalCount,
  getDataCollectTrendNoOpLog
} from '@/api/report/baseReport/largeScreen/largeScreen';
import { getValueByCondition } from '@/api/user';
import DialogLayoutConfig from '@/views/report/largeScreen/common/DialogLayoutConfig';
import axios from 'axios' // 中断文件上传请求时使用
import TopIcoButton from '../common/TopIcoButton';
export default {
  name: 'LargeScreen',
  components: { LineChart, PieDiskChart, PieLineChart, BarChart, Tree, vueSeamlessScroll, DialogLayoutConfig, TopIcoButton },
  props: {
    // 大屏轮播是否首次加载
    isFirst: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      yAxisName: '',
      // 接口参数(自定义布局)
      parameterOption: {
        menuCode: 'S12',
        tableKey: 'largeScreenOperationMaintenance'
      },
      // 控制(服务器状态)和(近7日数据变化)定时切换显示
      showServer: true,
      intervalIdStatusOrApply: null,
      // 控制审计日志增长率定时切换显示
      auditLogTop5: true,
      intervalIdAuditLog: null,
      // 深拷贝originalLayout,否则弹框修改时，会影响父组件layout数据
      orLayout: [],
      wsMid: '',
      // 页面需要展示的布局
      layout: [],
      // 动态配置布局
      // 注：flag代表的布局：['layout1-1', 'layout1-2', 'layout2-1', 'layout1-1-1', 'layout1-2-1', 'layout1', 'layout1-3', 'layout3-1']
      // 如flag:3为索引3，既平均分配的三列显示：layout1-1-1
      // feature:每一个展示项有一个特有的feature名称，不能重复，用于页面动态显示时判断
      // 初始化布局，layout会随着接口返回发生变化，所以初始化的数据要一直保留，不被修改
      originalLayout: [
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('pages.foundationForm_Msg56'), type: 'chart-data', feature: 'dataItemConditionTerminal' },
            { span: 8, name: this.$t('pages.keyMonitoringLog'), type: 'chart-log', feature: 'keyMonitoringLogCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg57'), type: 'chart-data', feature: 'dataItemConditionCpu' }
          ]
        },
        { gutter: 20, flag: 2,
          cols: [
            { span: 16, name: this.$t('pages.foundationForm_Msg58'), type: 'chart-tree', feature: 'serviceCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg59'), type: 'chart-disk', feature: 'diskCondition' }
          ]
        },
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('pages.cpuUsageAndTrend'), type: 'chart-line-pie', feature: 'cpuCondition' },
            { span: 8, name: '审计日志增长率', type: 'chart-bar', feature: 'auditLogCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg61'), type: 'chart-line', feature: 'diskIOTraffic' },
            { span: 8, name: this.$t('pages.foundationForm_Msg60'), type: 'chart-line', feature: 'diskIops' }
          ]
        }
      ],
      // 近7日数据变化趋势查询数据
      query: {
        searchName: '',
        page: 1,
        limit: 20,
        startDate: '',
        endDate: '',
        detailDate: ''
      },
      // 树图传值
      dataTree: {},
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 是否首次执行定时器
      isFirstTime: true,
      // 是否首次加载
      isMountedOperationMaintenance: false,
      // 中断请求
      sourceGetLargeScreenConfig: null,
      sourceGetLayout: null,
      sourceGetServerResourceInfo: null,
      sourceGetCollectionAndBatchInfo: null,
      sourceGetComputerWarnTimes: null,
      sourceGetTerminalCount: null,
      sourceGet7DayDataChange: null,
      // 定时器
      currentTime: undefined,
      timer: undefined,
      diskTimer: undefined,
      diskPartitionTimer: undefined,
      collectTimer: undefined,
      logTimer: undefined,
      terminalUserTimer: undefined,
      last7DaysTimer: undefined,
      serverTimer: undefined,
      dataLoadPromise: undefined,
      // 切换间隔时间，8秒
      intervalTime: 8000,
      // CPU、内存、磁盘1秒
      intervalCpu: 1000,
      // 跑批和采集4小时
      intervalCollect: 14400000,
      // 重点监控日志10分钟
      intervalLog: 600000,
      // 终端、操作员4小时
      intervalTerminalUser: 14400000,
      // 近7日数据变化4小时
      intervalLast7Days: 14400000,
      // 服务器状态5秒
      intervalServer: 5000,
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight: '19vh',
      middleOutHeight: '7.1vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: '',
        currentTime: '',
        middleMsg1: [
          { currentName: this.$t('pages.terminalOnLine'), currentNum: '0' },
          { currentName: this.$t('pages.terminalAll'), currentNum: '0' },
          { currentName: this.$t('pages.numberOfflineTerminalsWeek'), currentNum: '0' },
          { currentName: this.$t('pages.numberOfflineTerminalsMonth'), currentNum: '0' },
          { currentName: this.$t('pages.onLineOperators'), currentNum: '0' },
          { currentName: this.$t('pages.numberOperators'), currentNum: '0' }
        ],
        middleMsg2: [
          { currentName: 'CPU', currentNum: '0' + '%', flag: 1, num: 0 },
          { currentName: this.$t('table.memory'), currentNum: '0' + '%', flag: 1, num: 0 },
          { currentName: this.$t('pages.disk'), currentNum: '0' + '%', flag: 1, num: 0 },
          { currentName: this.$t('pages.numberAuditLogsCollectedToday'), currentNum: '0', flag: 2, num: 0 },
          { currentName: this.$t('pages.numberRunsToday'), currentNum: '0', flag: 2, num: 0 },
          { currentName: this.$t('pages.indicatesNumberAuditLogs'), currentNum: '0', flag: 2, num: 0 }
        ],
        // 加解密记录
        chatRecord: [],
        // 近7日数据变化趋势(折线图)
        loginTimesData: [],
        loginTimesOption: {
          xAxis: {
            data: []
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        },
        // 审批日志增长率(柱状图)
        auditLogTop5Data: [],
        auditLogTop5Option: {
          xAxis: {
            data: []
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}%' // 将数值格式化为百分比形式
            },
            // 控制分隔线
            splitLine: {
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: []
            }
          ]
        },
        auditLogAfter5Data: [],
        auditLogAfter5Option: {
          yAxis: {
            type: 'category',
            data: []
          },
          xAxis: {
            name: '',
            type: 'value',
            axisLabel: {
              formatter: '{value}%' // 将数值格式化为百分比形式
            }
          },
          series: [
            {
              data: []
            }
          ]
        },
        // CPU占用率(饼图)
        cpuPieData: [
          { value: 0, name: this.$t('pages.haveBeenUsed') },
          { value: 0, name: this.$t('pages.unused') }
        ],
        cpuPieDataName: '',
        // 磁盘IOPS(折线图)
        diskFirst: 0, // 是不是第一次请求
        switchCurrent: 0, // 切换需要
        diskIopsName: '', // 磁盘相关描述信息
        diskIopsNum: '', // 磁盘数量
        diskIopsArray: [], // 存放磁盘内容，用于赋值给diskIopsOption
        diskIopsData: [],
        diskIopsOption: {
          legend: {
            selectedMode: false,
            data: ['写', '读']
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            // 控制隔区域
            splitArea: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          color: ['#FFC851', '#45C2E0'],
          series: [
            {
              name: this.$t('pages.write'),
              data: [],
              areaStyle: {},
              type: 'line'
            },
            {
              name: this.$t('pages.read'),
              data: [],
              areaStyle: {},
              type: 'line'
            }
          ]
        },
        // 磁盘IOTraffic(折线图)
        diskIOTrafficFirst: 0, // 是不是第一次请求
        switchCurrentIOTraffic: 0, // 切换需要
        diskIOTrafficName: '', // 磁盘相关描述信息
        diskIOTrafficNum: '', // 磁盘数量
        diskIOTrafficArray: [], // 存放磁盘内容，用于赋值给diskIopsOption
        diskIOTrafficData: [],
        diskIOTrafficOption: {
          legend: {
            selectedMode: false,
            data: [this.$t('pages.write'), this.$t('pages.read')]
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            // 控制隔区域
            splitArea: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          color: ['#FFC851', '#45C2E0'],
          series: [
            {
              name: this.$t('pages.write'),
              data: [],
              areaStyle: {},
              type: 'line'
            },
            {
              name: this.$t('pages.read'),
              data: [],
              areaStyle: {},
              type: 'line'
            }
          ]
        },
        // 磁盘分区(饼图)
        resultPartitionAllList: [],
        boxList: [],
        boxIndex: 0,
        diskConditionNum: '',
        resultPartitionList: [],
        chartsFileTypeData: [],
        chartFileTypeOption: {},
        cpuChart: {
          yAxisData: []
        },
        memoryChart: {
          yAxisData: []
        }
      }
    }
  },
  computed: {
    show7Days() {
      return !this.showServer;
    },
    auditLogAfter5() {
      return !this.auditLogTop5;
    }
  },
  created() {
    for (const key in this.$socket.bindCallbackMap) {
      // 删掉之前的监听
      if (this.$socket.bindCallbackMap[key].url == '/getServerInfoList') {
        delete this.$socket.bindCallbackMap[key]
      }
    }
    if (window.wsMid) {
      delete this.$socket.bindCallbackMap[window.wsMid]
      this.$socket.unsubscribe('/getServerInfoList')
    }
    this.getLayout()
    this.getDiskPartition()
    this.isMountedOperationMaintenance = true
    this.resetTemp()
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    this.dataLoadPromise = Promise.all([
      this.getLargeScreenConfig()
    ]).then(() => {
      this.startIntervalFun()
      this.getServerResourceInfo()
      this.getCollectionAndBatchInfo()
      this.getComputerWarnTimes()
      this.getTerminalCount()
      this.get7DayDataChange()
      this.sendToServerList()
      this.statusOrApplyToggle()
      this.startAuditLogToggle()
    })
    this.getTimes()
    this.temp.currentTime = getCurrentTime()
    setTimeout(() => {
      this.isMountedOperationMaintenance = false
    }, 1000)
  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    if (this.isMountedOperationMaintenance === false) {
      this.startIntervalFun()
      this.getTimes()
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    console.log('destory')
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    this.$nextTick(() => {
      this.stopIntervalFun()
      clearInterval(window.currentTime)
      this.stopRequest()
    })
    if (window.wsMid) {
      delete this.$socket.bindCallbackMap[window.wsMid]
      this.$socket.unsubscribe('/getServerInfoList')
    }
    clearInterval(window.intervalIdStatusOrApply);
    window.intervalIdStatusOrApply = null
    clearInterval(window.intervalIdAuditLog);
    window.intervalIdAuditLog = null
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    this.$nextTick(() => {
      this.stopIntervalFun()
      this.stopRequest()
      clearInterval(this.currentTime)
    })
    if (window.wsMid) {
      delete this.$socket.bindCallbackMap[window.wsMid]
      this.$socket.unsubscribe('/getServerInfoList')
    }
  },
  methods: {
    /**
     * 获取layout布局（动态显示页面布局）
     * */
    getLayout(option) {
      const userId = this.$store.getters.userId
      this.sourceGetLayout = axios.CancelToken.source()
      let cacheToken = this.sourceGetLayout.token
      if (option === 'config') {
        cacheToken = ''
      }
      getValueByCondition({ sysUserId: userId, menuCode: this.parameterOption.menuCode, tableKey: this.parameterOption.tableKey, type: 4 }, cacheToken).then(res => {
        if (res.data) {
          // 如果存在data,直接显示接口返回，否则为前端页面设置的默认值
          // console.log('运维数据', JSON.parse(res.data.value))
          this.layout = JSON.parse(res.data.value)
          this.orLayout = JSON.parse(res.data.value)
        } else {
          // 初始化时先赋原来的初始值
          // 设置页面初始化时，不显示磁盘IOPS，如果用户想要显示，可以自定义配置
          const initialLayout = JSON.parse(JSON.stringify(this.originalLayout))
          if (initialLayout[2].cols.length === 4) {
            if (initialLayout[2].cols[3].feature === 'diskIops') {
              initialLayout[2].cols.pop()
            }
          }
          this.layout = JSON.parse(JSON.stringify(initialLayout))
          this.orLayout = JSON.parse(JSON.stringify(initialLayout))
        }
      })
    },
    /**
     * 打开布局配置弹框
     */
    configBtn() {
      this.getLayout()
      this.$refs.dialogLayoutConfig.show()
    },
    /**
     * 获取大屏配置中的配置信息（动态设置定时器时长）
     * */
    getLargeScreenConfig() {
      const userId = this.$store.getters.userId
      this.sourceGetLargeScreenConfig = axios.CancelToken.source()
      const cacheToken = this.sourceGetLargeScreenConfig.token
      return getValueByCondition({ sysUserId: userId, menuCode: '1001', tableKey: 'largeScreenConfig', type: 4 }, cacheToken).then(res => {
        if (res.data) {
          const response = JSON.parse(res.data.value)
          if (response.operationMaintenanceChartSwitchValue) {
            // 切换间隔时间，单位毫秒
            this.intervalTime = getSetIntervalTime(response.operationMaintenanceChartSwitchValue, response.operationMaintenanceChartSwitchSelect)
            this.intervalCpu = getSetIntervalTime(response.operationMaintenanceCpuValue, response.operationMaintenanceCpuSelect)
            this.intervalCollect = getSetIntervalTime(response.operationMaintenanceCollectValue, response.operationMaintenanceCollectSelect)
            this.intervalLog = getSetIntervalTime(response.operationMaintenanceMonitoringLogValue, response.operationMaintenanceMonitoringLogSelect)
            this.intervalTerminalUser = getSetIntervalTime(response.operationMaintenanceTerminalUserValue, response.operationMaintenanceTerminalUserSelect)
            this.intervalServer = getSetIntervalTime(response.operationMaintenanceServerValue, response.operationMaintenanceServerSelect)
          }
        }
      })
    },
    /**
       * 获取近7日数据变化趋势
       * */
    get7DayDataChange() {
      // 获取今天的日期
      const today = new Date();
      const todayYear = today.getFullYear();
      const todayMonth = (today.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的
      const todayDay = today.getDate().toString().padStart(2, '0');
      // 获取今天之前6天的日期
      const sixDaysAgo = new Date();
      sixDaysAgo.setDate(sixDaysAgo.getDate() - 6);
      const sixDaysYear = sixDaysAgo.getFullYear();
      const sixDaysMonth = (sixDaysAgo.getMonth() + 1).toString().padStart(2, '0');
      const sixDaysDay = sixDaysAgo.getDate().toString().padStart(2, '0');
      this.query.startDate = `${sixDaysYear}-${sixDaysMonth}-${sixDaysDay}`
      this.query.endDate = `${todayYear}-${todayMonth}-${todayDay}`
      this.sourceGet7DayDataChange = axios.CancelToken.source()
      const cacheToken = this.sourceGet7DayDataChange.token
      getDataCollectTrendNoOpLog(this.query, cacheToken).then(res => {
        this.temp.lastUpdateTime = getCurrentTime()
        // console.log('近7日', JSON.parse(JSON.stringify(res.data)))
        // 近7日数据变化
        this.temp.loginTimesOption.xAxis.data = res.data.trend.xaxisData
        this.temp.loginTimesOption.series[0].data = res.data.trend.seriesData
        this.temp.loginTimesOption.tooltip = {
          formatter: (d) => {
            // console.log('d', d)
            const time = d[0].axisValue + '<br>';
            const data = this.$t('table.logNumSumAll') + '：' + d[0].data;
            return time + data
          }
        }
        // 审计日志增长率
        this.temp.auditLogTop5Option.xAxis.data = res.data.topRate.xaxisData
        this.temp.auditLogTop5Option.series[0].data = res.data.topRate.seriesData
        this.temp.auditLogTop5Option.xAxis.axisLabel = {
          formatter: function(value) {
            // 如果文本长度超过8个字，则返回前八个字加上省略号
            if (value.length > 8) {
              return value.slice(0, 8) + '...';
            }
            // 如果文本长度不超过8个字，则直接返回原文本
            return value;
          }
        }
        this.temp.auditLogTop5Option.tooltip = {
          formatter: function(params) {
            return `${params[0].marker}${params[0].name}：${params[0].value}%`;
          }
        }
        this.temp.auditLogAfter5Data = res.data.bottomRate.seriesData
        this.temp.auditLogAfter5Option.yAxis.data = res.data.bottomRate.xaxisData
        this.temp.auditLogAfter5Option.series[0].data = res.data.bottomRate.seriesData
        this.temp.auditLogAfter5Option.yAxis.axisLabel = {
          formatter: function(value) {
            // 如果文本长度超过8个字，则返回前八个字加上省略号
            if (value.length > 8) {
              return value.slice(0, 8) + '...';
            }
            // 如果文本长度不超过8个字，则直接返回原文本
            return value;
          }
        }
        this.temp.auditLogAfter5Option.tooltip = {
          formatter: function(params) {
            return `${params[0].marker}${params[0].name}：${params[0].value}%`;
          }
        }
        // console.log('this.temp', JSON.parse(JSON.stringify(this.temp)))
      }).catch(() => {})
    },
    /**
     * 拓扑图节点值获取
     * 参考服务器状态那边，websocket接口调用
     * */
    sendToServerList() {
      // 使用session避免socket没有进入时，造成的页面内容为空
      const existTreeData = sessionStorage.getItem('serverTreeData')
      if (existTreeData) {
        this.dataTree = JSON.parse(existTreeData)
      }
      window.wsMid = this.$socket.sendToUser(1, '/getServerInfoList', 1, (respond, handle) => {
        handle.close()
        this.temp.lastUpdateTime = getCurrentTime()
        const arr = JSON.parse(respond.data.dataTree)
        this.dataTree = arr[0]
        if (window.wsMid) {
          delete this.$socket.bindCallbackMap[window.wsMid]
          this.$socket.unsubscribe('/getServerInfoList')
        }
        sessionStorage.setItem('serverTreeData', JSON.stringify(arr[0]))
      }, (handle) => {
        handle.close()
      })
    },
    /**
       * 获取cpu内存磁盘信息
       * */
    getServerResourceInfo() {
      this.sourceGetServerResourceInfo = axios.CancelToken.source()
      const cacheToken = this.sourceGetServerResourceInfo.token
      // CPU、内容、磁盘占比
      displayPanelGetServerResourceInfo(cacheToken).then(res => {
        this.temp.lastUpdateTime = getCurrentTime()
        // console.log('获取cpu内存磁盘信息', JSON.parse(JSON.stringify(res.data)))
        const response = res.data
        this.temp.middleMsg2[0].currentNum = response.cpuUsage + '%'
        this.temp.middleMsg2[2].currentNum = response.diskUsage + '%'

        this.temp.middleMsg2[0].num = response.cpuUsage
        this.temp.middleMsg2[1].num = response.memoryUsage
        this.temp.middleMsg2[2].num = response.diskUsage
        // cpu饼图
        this.temp.cpuPieData[0].value = response.cpuUsage
        this.temp.cpuPieData[1].value = (100 - response.cpuUsage).toFixed(2)
        // cpu折线图
        if (this.temp.cpuChart.yAxisData.length == 50) {
          this.temp.cpuChart.yAxisData.splice(0, 1)
        }
        this.temp.cpuChart.yAxisData.push(response.cpuUsage)
      })
      // 比上面一个接口请求更全面
      this.getDiskInfo()
    },
    /**
     * 获取磁盘，内存信息（比另一个接口内容更丰富）
     * */
    getDiskInfo() {
      displayPanelGetServerInfoForOption({ option: 27 }).then(res => {
        this.temp.diskFirst++
        this.temp.diskIOTrafficFirst++
        // console.log('res系统信息', res.data)
        // 内存
        const memoryData = res.data.memoryInfo
        this.temp.middleMsg2[1].currentNum = ((Number(memoryData.total) - Number(memoryData.available)) / 1073741824).toFixed(2) + '/' + (Number(memoryData.total) / 1073741824).toFixed(2) + 'GB(' + memoryData.usageRate + '%)'
        // CPU名称及内核
        this.temp.cpuPieDataName = res.data.cpuInfo.cpuName + '（' + res.data.cpuInfo.coreNum + this.$t('pages.nuclear') + '）'
        // 磁盘IOPS
        const diskIOPSData = res.data.iops
        // 第一次请求，先确定是几张磁盘，确定diskIopsArray对应的框架结构
        if (this.temp.diskFirst === 1) {
          const itemArray = JSON.parse(JSON.stringify(diskIOPSData))
          if (itemArray.length > 0) {
            itemArray.forEach(item => {
              item.readTimesPer = []
              item.writeTimesPer = []
            })
          }
          this.temp.diskIopsArray = itemArray
        }
        if (diskIOPSData.length > 0) {
          // 处理折线图数据，将每次请求的数据push到对应的读写数组中，如果数组长度为50，截取一个
          diskIOPSData.forEach((item, index) => {
            const writeArray = item.writeTimesPer.toFixed(2)
            const readArray = item.readTimesPer.toFixed(2)
            this.temp.diskIopsArray[index].writeTimesPer.push(writeArray)
            this.temp.diskIopsArray[index].readTimesPer.push(readArray)
            if (this.temp.diskIopsArray[index].writeTimesPer.length == 50) {
              this.temp.diskIopsArray[index].writeTimesPer.splice(0, 1)
              this.temp.diskIopsArray[index].readTimesPer.splice(0, 1)
            }
          });
        }
        // 磁盘IOTraffic
        const diskIOTrafficData = res.data.ioTraffic
        // 第一次请求，先确定是几张磁盘，确定diskIopsArray对应的框架结构
        if (this.temp.diskIOTrafficFirst === 1) {
          const itemArray = JSON.parse(JSON.stringify(diskIOTrafficData))
          if (itemArray.length > 0) {
            itemArray.forEach(item => {
              item.readSizePer = []
              item.writeSizePer = []
            })
          }
          this.temp.diskIOTrafficArray = itemArray
        }
        if (diskIOTrafficData.length > 0) {
          // 处理折线图数据，将每次请求的数据push到对应的读写数组中，如果数组长度为50，截取一个
          diskIOTrafficData.forEach((item, index) => {
            const writeArray = (item.writeSizePer / 1048576).toFixed(2)
            const readArray = (item.readSizePer / 1048576).toFixed(2)
            this.temp.diskIOTrafficArray[index].writeSizePer.push(writeArray)
            this.temp.diskIOTrafficArray[index].readSizePer.push(readArray)
            if (this.temp.diskIOTrafficArray[index].writeSizePer.length == 50) {
              this.temp.diskIOTrafficArray[index].writeSizePer.splice(0, 1)
              this.temp.diskIOTrafficArray[index].readSizePer.splice(0, 1)
            }
          });
        }
        // 首次加载，需要先调用一下页面数据
        if (this.isFirstTime) {
          this.switchingDisk()
        }
        this.isFirstTime = false
      })
    },
    /**
     * 磁盘切换(存在多张磁盘时，自动切换)
     * */
    switchingDisk() {
      // 如果存在多张磁盘，定时器切换显示每一张磁盘
      if (this.temp.diskIopsArray.length > 0) {
        if (this.temp.switchCurrent > this.temp.diskIopsArray.length - 1) {
          this.temp.switchCurrent = 0
        }
        const readArray = this.temp.diskIopsArray[this.temp.switchCurrent].readTimesPer
        const writeArray = this.temp.diskIopsArray[this.temp.switchCurrent].writeTimesPer
        const currentRead = readArray[readArray.length - 1]
        const currentWrite = writeArray[writeArray.length - 1]
        this.temp.diskIopsNum = this.temp.diskIopsArray[this.temp.switchCurrent].name + '（' + (this.temp.switchCurrent + 1) + '/' + this.temp.diskIopsArray.length + '）'
        this.temp.diskIopsName = this.$t('pages.foundationForm_Msg62') + currentRead + this.$t('pages.foundationForm_Msg63') + currentWrite + this.$t('pages.foundationForm_Msg64')
        this.temp.diskIopsOption.series[0].data = writeArray
        this.temp.diskIopsOption.series[1].data = readArray
        this.temp.switchCurrent++
      }
      if (this.temp.diskIOTrafficArray.length > 0) {
        if (this.temp.switchCurrentIOTraffic > this.temp.diskIOTrafficArray.length - 1) {
          this.temp.switchCurrentIOTraffic = 0
        }
        const readArray = this.temp.diskIOTrafficArray[this.temp.switchCurrentIOTraffic].readSizePer
        const writeArray = this.temp.diskIOTrafficArray[this.temp.switchCurrentIOTraffic].writeSizePer
        const currentRead = readArray[readArray.length - 1]
        const currentWrite = writeArray[writeArray.length - 1]
        this.temp.diskIOTrafficNum = this.temp.diskIOTrafficArray[this.temp.switchCurrentIOTraffic].name + '（' + (this.temp.switchCurrentIOTraffic + 1) + '/' + this.temp.diskIOTrafficArray.length + '）'
        this.temp.diskIOTrafficName = this.$t('pages.foundationForm_Msg65') + currentRead + this.$t('pages.foundationForm_Msg66') + currentWrite + this.$t('pages.foundationForm_Msg67')
        this.temp.diskIOTrafficOption.series[0].data = writeArray
        this.temp.diskIOTrafficOption.series[1].data = readArray
        this.temp.switchCurrentIOTraffic++
      }
    },
    /**
     * 磁盘分区切换(一个页面最多显示6张磁盘，多出来的部分切换显示)
     * */
    switchingDiskPartition() {
      if (this.temp.boxList.length > 0) {
        if (this.temp.boxIndex > this.temp.boxList.length - 1) {
          this.temp.boxIndex = 0
        }
        this.temp.resultPartitionList = this.temp.boxList[this.temp.boxIndex]
        this.temp.diskConditionNum = '（' + (this.temp.boxIndex + 1) + '/' + this.temp.boxList.length + '）'
        this.temp.boxIndex++
      }
    },
    /**
     * 获取磁盘分区(数据处理)
     * */
    getDiskPartition() {
      displayPanelGetServerInfoForOption({ option: 4 }).then(res => {
        const partitionList = res.data.partitionList
        if (partitionList.length > 0) {
          partitionList.forEach(item => {
            // 可用
            const usable = (item.freeSpace / 1073741824).toFixed(2)
            // 已用
            const used = ((item.totalSpace - item.freeSpace) / 1073741824).toFixed(2)
            // 已用占比
            const percent = ((item.totalSpace - item.freeSpace) / item.totalSpace).toFixed(2)
            // 已用占比大于等于85%时，已用红色展示
            let pieColor = ['#26a0da', '#666666']
            if (percent >= 0.85) {
              pieColor = ['#da2626', '#666666']
            }
            const obj = {}
            obj.name = item.name.replace(this.$t('pages.immobilization'), '')
            obj.color = pieColor
            obj.data = [
              { value: used, name: this.$t('pages.used') + '\n' + used + 'GB' },
              { value: usable, name: this.$t('pages.usable') + '\n' + usable + 'GB' }
            ]
            // 处理后的所有磁盘数据
            this.temp.resultPartitionAllList.push(obj)
          })
        }
        // 数据太多的话，页面显示不下，会溢出，限制一次性只能显示几个，多余的定时器切换显示
        const allList = this.temp.resultPartitionAllList
        const box = []
        let son = []
        // 将allList中的数据按6个截取重新构造成新的二维数组
        if (allList.length > 0) {
          allList.forEach(item => {
            if (son.length === 6) {
              son = []
            }
            if (son.length === 0) {
              box.push(son)
            }
            son.push(item)
          })
        }
        this.temp.boxList = box
        // 如果box.length大于0，将二维数组中的每一个数组定时切换显示
        this.switchingDiskPartition()
      })
    },
    /**
       * 获取综合报表跑批和采集记录数量
       * */
    getCollectionAndBatchInfo() {
      // 根据是否首次加载，调用有日志和没日志的接口
      let apiFun = displayPanelGetCollectionAndBatchInfoNoOplog
      if (this.isMountedOperationMaintenance && this.isFirst) {
        apiFun = displayPanelGetCollectionAndBatchInfo
      }
      this.sourceGetCollectionAndBatchInfo = axios.CancelToken.source()
      const cacheToken = this.sourceGetCollectionAndBatchInfo.token
      apiFun(cacheToken).then(res => {
        // console.log('获取综合报表跑批和采集记录数量', JSON.parse(JSON.stringify(res.data)))
        this.temp.lastUpdateTime = getCurrentTime()
        const response = res.data
        this.temp.middleMsg2[3].currentNum = response.collectionCount
        this.temp.middleMsg2[4].currentNum = response.batchCount
        this.temp.middleMsg2[5].currentNum = response.todayLogNum
      })
    },
    /**
     * 截取字符串中第一个_后的内容
     * */
    getContentAfterFirstUnderscore(str) {
      const result = str.match(/_([^]*)/);
      return result ? result[1] : '';
    },
    /**
     * 获取当日计算机告警信息次数
     * */
    getComputerWarnTimes() {
      this.sourceGetComputerWarnTimes = axios.CancelToken.source()
      const cacheToken = this.sourceGetComputerWarnTimes.token
      displayPanelGetComputerWarnTimes(cacheToken).then(res => {
        // console.log('获取当日计算机告警信息次数', JSON.parse(JSON.stringify(res.data)))
        this.temp.lastUpdateTime = getCurrentTime()
        const response = res.data
        // 截取第一个_后面的内容为部门名称
        const datas = response.map(item => {
          return { ...item, name: this.getContentAfterFirstUnderscore(item.name) };
          // return { ...item, name: item.name.split('_')[1] };
        })
        // 对比新旧数据，如果有变化，添加标记isNew: true，否则isNew: false。用于区分新旧数据显示
        const oldArray = this.temp.chatRecord
        const newArray = datas
        const markNewObjects = (newArray, oldArray) => {
          return newArray.map(newObj => {
            // 数据从无到有的时候不变红色，只有新旧数据都存在，且存在新的数据时，才变红色
            let oldObj = {}
            if (oldArray.length > 0) {
              oldObj = oldArray.find(oldObj => oldObj.name === newObj.name)
            }
            return oldObj ? Object.assign({}, newObj, { isNew: false }) : Object.assign({}, newObj, { isNew: true });
          });
        };
        this.temp.chatRecord = markNewObjects(newArray, oldArray);
        // console.log('this.temp.chatRecord', JSON.parse(JSON.stringify(this.temp.chatRecord)))
      })
    },
    /**
       * 获取终端统计数
       * */
    getTerminalCount() {
      this.sourceGetTerminalCount = axios.CancelToken.source()
      const cacheToken = this.sourceGetTerminalCount.token
      displayPanelGetTerminalCount(cacheToken).then(res => {
        this.temp.lastUpdateTime = getCurrentTime()
        // console.log('获取终端统计数', JSON.parse(JSON.stringify(res.data)))
        const response = res.data
        this.temp.middleMsg1[0].currentNum = response.onLinelNum
        this.temp.middleMsg1[1].currentNum = response.totalNum
        this.temp.middleMsg1[2].currentNum = response.offlineWeekNum
        this.temp.middleMsg1[3].currentNum = response.offlineMonthNum
        this.temp.middleMsg1[4].currentNum = response.userOnlineNum
        this.temp.middleMsg1[5].currentNum = response.userTotalNum
      })
    },
    /**
       * 系统当前时间，实时获取，实时刷新
       * */
    getTimes() {
      if (window.currentTime) {
        clearInterval(window.currentTime)
      }
      window.currentTime = setInterval(this.getSystemTime, 1000)
    },
    /**
       * 系统当前时间，赋值
       * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      // 最后更新于
      this.temp.lastUpdateTime = getCurrentTime()
    },
    // 鼠标移入清除定时器（切换）
    clearServerOr7days(option) {
      if (option === 'server7day') {
        clearInterval(window.intervalIdStatusOrApply);
      }
    },
    // 鼠标移出调用定时器（切换）
    startServerOr7days(option) {
      if (option === 'server7day') {
        this.statusOrApplyToggle()
      }
    },
    // 鼠标移入清除定时器（切换）
    clearAuditLog(option) {
      if (option === 'topAfter') {
        clearInterval(window.intervalIdAuditLog);
      }
    },
    // 鼠标移出调用定时器（切换）
    startAuditLog(option) {
      if (option === 'topAfter') {
        this.startAuditLogToggle()
      }
    },
    // 设置（服务器状态）和（近7日数据变化）定时器
    statusOrApplyToggle() {
      if (window.intervalIdStatusOrApply) {
        clearInterval(window.intervalIdStatusOrApply);
      }
      window.intervalIdStatusOrApply = setInterval(this.toggleServerOr7days, this.intervalTime);
    },
    // 设置（服务器状态）和（近7日数据变化）定时器
    startAuditLogToggle() {
      if (window.intervalIdAuditLog) {
        clearInterval(window.intervalIdAuditLog);
      }
      window.intervalIdAuditLog = setInterval(this.toggleAuditLog, this.intervalTime);
    },
    // 设置（服务器状态）和（近7日数据变化）切换显示
    toggleServerOr7days() {
      this.showServer = !this.showServer;
    },
    // 设置审计日志切换显示
    toggleAuditLog() {
      this.auditLogTop5 = !this.auditLogTop5;
    },
    /**
     * 中断http请求（组件关闭或停用时，如果http接口未请求完成，页面切换时中断请求调用）
     * */
    stopRequest() {
      // 中断大屏配置接口获取
      if (this.sourceGetLargeScreenConfig) { this.sourceGetLargeScreenConfig.cancel() }
      // 中断大屏布局接口获取
      if (this.sourceGetLayout) { this.sourceGetLayout.cancel() }
      // 中断CPU内存磁盘信息接口获取
      if (this.sourceGetServerResourceInfo) { this.sourceGetServerResourceInfo.cancel() }
      // 中断综合报表跑批和采集记录数量接口获取
      if (this.sourceGetCollectionAndBatchInfo) { this.sourceGetCollectionAndBatchInfo.cancel() }
      // 中断当前计算机告警信息次数接口获取
      if (this.sourceGetComputerWarnTimes) { this.sourceGetComputerWarnTimes.cancel() }
      // 中断终端统计数接口获取
      if (this.sourceGetTerminalCount) { this.sourceGetTerminalCount.cancel() }
      // 中断终端统计数接口获取
      if (this.sourceGet7DayDataChange) { this.sourceGet7DayDataChange.cancel() }
    },
    /**
     * 定时器启动
     * */
    startIntervalFun() {
      if (window.timer) {
        clearInterval(window.timer)
      }
      window.timer = setInterval(() => { this.getServerResourceInfo() }, this.intervalCpu)
      if (window.collectTimer) {
        clearInterval(window.collectTimer)
      }
      window.collectTimer = setInterval(() => { this.getCollectionAndBatchInfo() }, this.intervalCollect)
      if (window.logTimer) {
        clearInterval(window.logTimer)
      }
      window.logTimer = setInterval(() => { this.getComputerWarnTimes() }, this.intervalLog)
      if (window.terminalUserTimer) {
        clearInterval(window.terminalUserTimer)
      }
      window.terminalUserTimer = setInterval(() => { this.getTerminalCount() }, this.intervalTerminalUser)
      if (window.last7DaysTimer) {
        clearInterval(window.last7DaysTimer)
      }
      window.last7DaysTimer = setInterval(() => { this.get7DayDataChange() }, this.intervalLast7Days)
      if (window.serverTimer) {
        clearInterval(window.serverTimer)
      }
      window.serverTimer = setInterval(() => { this.sendToServerList() }, this.intervalServer)
      if (window.diskTimer) {
        clearInterval(window.diskTimer)
      }
      window.diskTimer = setInterval(() => { this.switchingDisk() }, this.intervalTime)
      if (window.diskPartitionTimer) {
        clearInterval(window.diskPartitionTimer)
      }
      window.diskPartitionTimer = setInterval(() => { this.switchingDiskPartition() }, this.intervalTime)
    },
    /**
     * 定时器停止
     * */
    stopIntervalFun() {
      clearInterval(window.timer)
      clearInterval(window.collectTimer)
      clearInterval(window.logTimer)
      clearInterval(window.terminalUserTimer)
      clearInterval(window.last7DaysTimer)
      clearInterval(window.serverTimer)
      clearInterval(window.diskTimer)
      clearInterval(window.diskPartitionTimer)
      window.timer = null
      window.collectTimer = null
      window.logTimer = null
      window.terminalUserTimer = null
      window.last7DaysTimer = null
      window.serverTimer = null
      window.diskTimer = null
      window.diskPartitionTimer = null
    },
    /**
       * 判断是否全屏（监听全屏）
       * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
       * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
       * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      // console.log('this.isFullScreen', this.isFullScreen)
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight = '23.5vh'
        this.middleOutHeight = '8.5vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight = '19vh'
        this.middleOutHeight = '7.1vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .activeClassRed{
    color: #ff0000;
  }
  //中间特殊的小块
  .large-screen-middle-out{
    width: 49%;
    float: left;
    div{
      width: 90%;
      margin: auto;
      font-size: 15px;
      height: 60%; /* 设置div的高度为50% */
      display: flex; /* 使用flex布局 */
      align-items: center; /* 垂直居中 */
    }
    .large-screen-middle-num{
      font-size: 18px;
    }
    .large-screen-middle-name{
      font-size: 14px;
      height: 40%; /* 设置div的高度为50% */
      display: flex; /* 使用flex布局 */
      align-items: center; /* 垂直居中 */
    }
  }
  .redColor{
    color: red;
  }
  .remark-span{
    position: absolute;
    font-size: 12px;
    display: inline-block;
    padding-left: 12px;
    color: orange;
    line-height: 20px;
  }
  .more-pie{
    display: flex;
    flex-wrap: wrap;
    div{
      display: inline-block;
      height: 50%;
      width: 33.33%;
    }
  }
</style>

