<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import { debounce } from '@/utils'

require('echarts/theme/macarons') // echarts theme
export default {
  name: 'GraphCanvas',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    dataTree: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      topoNum: 0,
      chart: null,
      sidebarElm: null,
      onlines: {
        0: this.$t('pages.offline'),
        1: this.$t('pages.online')
      },
      adminAuthorized: {
        0: this.$t('pages.unauthorized'),
        1: this.$t('pages.authorized')
      },
      legendOptions: [
        { label: this.$t('pages.serverStatus', { isOnline: this.$t('pages.online'), isAuthor: this.$t('pages.authorization') }), src: require('@/assets/serverTopo/server_online_reg.png') },
        { label: this.$t('pages.serverStatus', { isOnline: this.$t('pages.online'), isAuthor: this.$t('pages.unauthorized') }), src: require('@/assets/serverTopo/server_online_warn.png') },
        { label: this.$t('pages.serverStatusError'), src: require('@/assets/serverTopo/server_offline_warn.png') },
        { label: this.$t('text.connectNormal'), src: require('@/assets/serverTopo/lineGreen.png') },
        { label: this.$t('text.connectExceptional'), src: require('@/assets/serverTopo/lineRed.png') }
      ],
      data: []
    }
  },
  computed: {
    theme() {
      return this.$store.getters.theme || this.$store.getters.sysResources.theme
    }
  },
  watch: {
    dataTree(val, oldVal) {
      if (this.topoNum != val.length) {
        this.setOptions();
      }
    },
    theme() {
      this.setOptions()
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)
    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  created() {

  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    /**
     * 遍历树
     * */
    readNodes(nodes) {
      for (const item of nodes) { // js遍历树形数组结构
        if (item.children && item.children.length) {
          if (!this.hasPermission('A4K')) {
            // 未购买软件中心模块时隐藏软件中心服务器
            item.children = item.children.filter(child => child.devType !== 172)
          }
          this.readNodes(item.children)
        }
        if (item.status == 1) {
          if (item.adminAuthorized === 0) {
            // 采集未授权
            item.symbol = 'image://' + require('@/assets/serverTopo/server_online_warn.png');
          } else {
            item.symbol = 'image://' + require('@/assets/serverTopo/server_online_reg.png');
          }
        } else {
          item.symbol = 'image://' + require('@/assets/serverTopo/server_offline_warn.png');
        }
        if (item.lineStatus === 1) {
          // 修改连线颜色
          item.lineStyle = {
            color: '#2E8B57'
          }
        } else {
          item.lineStyle = {
            color: '#ff0000'
          }
        }
      }
    },
    setOptions() {
      const textColor = this.theme === 'default' ? '#fff' : '#333'
      if (Object.keys(this.dataTree).length !== 0) {
        this.readNodes([this.dataTree])
        const defaultOpt = {
          tooltip: {
            show: true,
            trigger: 'item',
            triggerOn: 'mousemove',
            backgroundColor: 'rgba(0, 0, 0, 0.9)',
            enterable: true, // 鼠标是否可进入提示框浮层中
            formatter: this.formatterHover// 修改鼠标悬停显示的内容
          },
          toolbox: {
            show: true,
            top: 20,
            left: 30
          },
          series: [
            {
              type: 'tree',
              data: [this.dataTree],
              top: '1%',
              left: '10%',
              bottom: '1%',
              right: '20%',
              label: {
                position: 'left',
                verticalAlign: 'middle',
                align: 'right',
                color: textColor,
                fontSize: 12
              },
              lineStyle: {
                color: '#2E8B57'
              },
              orient: 'LR',
              leaves: {
                label: {
                  position: 'right',
                  verticalAlign: 'middle',
                  align: 'left'
                }
              },
              symbolSize: [30, 30],
              edgeForkPosition: '72%',
              emphasis: { // 高亮
                focus: 'descendant'
              },
              initialTreeDepth: -1, // 树图初始展开层级-1，展开所有子节点
              roam: true, // 鼠标缩放，拖拽整颗树
              expandAndCollapse: false, // 无关的子树折叠收起
              animationDuration: 550,
              animationDurationUpdate: 750,
              width: '50%'// 组件宽度
            }
          ]
        }
        this.chart.setOption(defaultOpt, true)
      }
    },
    /**
     * 鼠标悬停显示详情
     * @param params
     * @returns {string}
     */
    formatterHover(params) {
      // console.log('params', JSON.parse(JSON.stringify(params)))
      if (this.contextmenu) {
        // 隐藏悬停
        return '';
      }
      // 1：引擎 6:采集 9：审批 11：文件 12：检测 219:数据库
      var deviceType = params.data.devType;
      let serverIp = ' '
      const commlist = params.data.commlist
      if (commlist != undefined) {
        commlist.forEach(function(value) {
          serverIp = serverIp + `[${value.ip}:${value.port}]`
        })
      }
      let html = `<span class="text">${this.$t('table.deviceNum')}：${params.data.devId}</span></br>` +
        `<span class="text">${this.$t('table.deviceName')}：${params.data.devName}</span></br>` +
        `<span class="text">${this.$t('table.versionNumber')}：${params.data.version}</span></br>` +
        `<span class="text">${this.$t('pages.onlineStatus')}：${this.onlines[params.data.status]}</span></br>`;
      if (deviceType === 1) {
        html += `<span class="text">${this.$t('pages.onlineTerminals')} / ${this.$t('pages.terminalAll')}：${params.data.devOnlineNum} / ${params.data.devTotalNum}</span></br>`
        if (params.data.devIp != null) {
          html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span></br>`
        }
      }
      if (deviceType === 6) {
        if (params.data.adminAuthorized != undefined) {
          html += `<span class="text">${this.$t('table.usbDiskStatus')}：${this.adminAuthorized[params.data.adminAuthorized]}</span></br>`
        }
        html += `<span class="text">${this.$t('pages.onlineTerminals')} / ${this.$t('pages.terminalAll')}：${params.data.devOnlineNum} / ${params.data.devTotalNum}</span></br>` +
          `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span></br>`
      }
      if (deviceType === 9) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span></br>`
      }
      if (deviceType === 11) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span></br>`
      }
      if (deviceType === 12) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span></br>`
      }
      if (deviceType === 219) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span></br>`
      }
      if (deviceType === 172) {
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span></br>`
      }
      if (deviceType === 186) {
        html += `<span class="text">${this.$t('pages.onlineTerminals')} / ${this.$t('pages.terminalAll')}：${params.data.devOnlineNum} / ${params.data.devTotalNum}</span></br>`
        html += `<span class="text">${this.$t('table.ip')}：${params.data.devIp}</span></br>`
        if (params.data.adminAuthorized != undefined) {
          html += `<span class="text">${this.$t('table.usbDiskStatus')}：${this.adminAuthorized[params.data.adminAuthorized]}</span></br>`
        }
      }
      return html;
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.setOptions()
    }
  }
}
</script>

