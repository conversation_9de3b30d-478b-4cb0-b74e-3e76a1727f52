<!--敏感内容检测报表查询条件封装-->
<template>
  <span class="query-report-span">
    <span>
      <el-radio-group v-if="isLargeScreenConfig" v-model="query.dateCycleLargeScreen" style="display: inline-block" @change="dataType">
        <el-radio :label="0" style="margin-right: 10px">
          {{ $t('table.previousCycle') }}
          <el-tooltip effect="dark" placement="top-start">
            <div slot="content">
              {{ $t('report.largeScreenConfigMsg8') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-radio>
        <el-radio :label="1" style="margin-right: 10px">
          {{ $t('pages.itemRowData6') }}
          <el-tooltip effect="dark" placement="top-start">
            <div slot="content">
              {{ $t('report.largeScreenConfigMsg9') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-radio>
        <el-radio :label="2" style="margin-right: 10px">
          {{ $t('report.largeScreenConfigMsg7') }}
          <el-tooltip effect="dark" placement="top-start">
            <div slot="content">
              {{ $t('report.largeScreenConfigMsg10') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-radio>
      </el-radio-group>
      <!--报表日期选择下拉框-->
      <span v-if="!isLargeScreenConfig">{{ $t('pages.dimBaseType') }}</span>
      <el-select v-model="query.dimBaseType" :popper-append-to-body="!isFullScreen" style="width: 125px;" @change="loadDimTime" >
        <template v-for="(item, index) in dateTypeOptions">
          <el-option
            v-if="showOption(item)"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </template>
      </el-select>
      <!--报表日期选择下拉框后面的级联选择器-->
      <el-cascader
        v-if="query.dimBaseType !== 6"
        :key="cascaderKey"
        v-model="query.dimValue"
        :disabled="query.dateCycleLargeScreen !== 2 && isLargeScreenConfig"
        :append-to-body="!isFullScreen"
        :options="dateOptions"
        :props="{ expandTrigger: 'hover' }"
        :separator="$t('pages.cascaderSep')"
        style="width: 150px; margin-right: 10px;line-height:0"
        @change="dimValueChange"
      ></el-cascader>
      <!--报表日期选择下拉框后面的近几天选择下拉框，，按近几天统计时显示-->
      <el-select
        v-if="query.dimBaseType === 6"
        v-model="query.dimValue"
        :popper-append-to-body="!isFullScreen"
        style="width: 150px;"
      >
        <el-option
          v-for="(item, index) in recentDaysOptions"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </span>
    <el-button v-if="!isLargeScreenConfig" type="primary" size="mini" @click="searchQuery()">{{ $t('button.search') }}</el-button>
  </span>
</template>

<script>
import { getDimTime } from '@/api/report/baseReport/issueReport/issue';
import axios from 'axios' // 中断文件上传请求时使用
export default {
  name: 'Query',
  components: {},
  props: {
    // 是否大屏配置弹框调用（如果是大屏弹框，不显示搜索按钮）
    isLargeScreenConfig: {
      type: Boolean,
      default: false
    },
    // 大屏弹框传递过来的日期选中值
    dataTemp: {
      type: Object,
      default() {
        return {}
      }
    },
    countByObject: {
      type: Number,
      default: 2
    },
    risk: {
      type: Boolean,
      default: false
    },
    // 统计类型数组
    typeOptions: {
      type: Array,
      default() {
        return [{ value: 1, label: this.$t('pages.conutType1') }, { value: 2, label: this.$t('pages.conutType2') }]
      }
    }
  },
  data() {
    return {
      // 中断请求
      source: null,
      // 判断是否全屏
      isFullScreen: false,
      // 报表日期统计下拉框
      dateTypeOptions: [
        { value: 5, label: this.$t('pages.dateTypeOptions1') }, // 按天统计
        { value: 2, label: this.$t('pages.dateTypeOptions2') }, // 按周统计
        { value: 1, label: this.$t('pages.dateTypeOptions3') }, // 按月统计
        { value: 3, label: this.$t('pages.dateTypeOptions4') }, // 按季度统计
        { value: 4, label: this.$t('pages.dateTypeOptions5') }, // 按年份统计
        { value: 6, label: this.$t('pages.dateTypeOptions6') }  // 按近几天统计
      ],
      // 报表日期时间选择下拉框
      dateOptions: [],
      // 报表日期时间选择下拉框
      months: ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      quarters: ['', 'theFirst', 'theSecond', 'theThird', 'theFourth'],
      cascaderKey: 0,
      // 近几天
      recentDaysOptions: [
        { value: 7, label: this.$t('pages.recentDays', { day: 7 }) },
        { value: 15, label: this.$t('pages.recentDays', { day: 15 }) },
        { value: 30, label: this.$t('pages.recentDays', { day: 30 }) }
      ],
      searchClick: 0,
      // 搜查查询
      query: {
        dateCycleLargeScreen: 0, // 周期单选按钮选中
        dimBaseType: 1, // 报表日期类型
        dimValue: '',  // 报表日期
        countByObject: 1 // 统计类型(看着好像没啥用)
      }
    }
  },
  watch: {
    dataTemp: {
      deep: true,
      handler(val, oldValue) {
        if (Object.keys(val).length !== 0) {
          if (val.encOrDecDimValue !== '') {
            // 监听大屏弹框传递的日期选中值，进行默认选中
            this.query.dimBaseType = val.encOrDecDimBaseType
            this.query.dimValue = val.encOrDecDimValue
            this.query.dateCycleLargeScreen = val.dateCycleLargeScreen
            this.loadDimTime('initial')
          }
        }
      }
    },
    query: {
      deep: true, // 启用深度监听
      handler(newVal, oldVal) {
        this.$emit('thisQuery', newVal)
      }
    }
  },
  created() {
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    // 弹框关掉在重新打开时，需选中之前的选中
    // console.log('大屏重新打开this.dataTemp', JSON.parse(JSON.stringify(this.dataTemp)))
    if (this.isLargeScreenConfig) {
      // 大屏配置弹框
      this.query.dateCycleLargeScreen = this.dataTemp.dateCycleLargeScreen
      this.query.dimBaseType = this.dataTemp.encOrDecDimBaseType
      this.query.dimValue = this.dataTemp.encOrDecDimValue
    }
    // 报表日期搜索下拉框
    this.loadDimTime('initial')
  },
  mounted() {
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    this.stopRequest()
  },
  deactivated() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    this.stopRequest()
  },
  methods: {
    /**
     * 单选按钮选中发生变化时，重置选中日期
     * */
    dataType(data) {
      if (data === 0 && this.query.dimBaseType === 6) {
        this.query.dimBaseType = 1
      }
      this.loadDimTime(data)
    },
    /**
     * 中断http请求（组件关闭或停用时，如果http接口未请求完成，页面切换时中断请求调用）
     * */
    stopRequest() {
      // 中断接口获取
      if (this.source) { this.source.cancel() }
    },
    /**
     * 判断是否全屏（监听全屏，通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示）
     * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
    },
    /**
       * 将查询条件传给父组件
       * */
    getQuery() {
      return this.query
    },
    searchQuery() {
      this.searchClick = 1
      this.$emit('thisQuery', this.query)
      // 全屏修改后，就不是调用父组件方法了
      this.$parent.getLargeScreenData()
    },
    /**
     * 限制选择上一个周期时：不显示近几天选项
     * */
    showOption(item) {
      if (item.value == 6 && this.query.dateCycleLargeScreen == 0) {
        return false
      }
      return true
    },
    /**
       * 报表日期下拉框选择时，后面的下拉框根据条件发生变化
       * @param value 当前下拉框选中的value值，value === 6时显示近几天下拉框，否则显示时间级联选择器
       * */
    loadDimTime(value) {
      // dataTemp父组件传递过来的默认选中日期，isConfig是否存在默认传值
      const dataTemp = this.dataTemp
      let isConfig = false
      if (Object.keys(dataTemp).length !== 0) {
        isConfig = true
      }
      const dimBaseType = this.query.dimBaseType
      if (6 === dimBaseType) {
        // 如果父组件有传值，且为初始化，则选中传递值，否则设置默认值（注：后面的一致，不添加备注了）
        if (isConfig && value === 'initial') {
          this.query.dimValue = dataTemp.encOrDecDimValue
          return
        } else {
          // 设置默认选中近7天
          this.query.dimValue = 7
          return
        }
      } else {
        const formatterOption = (dimValue) => {
          const dimYear = dimValue.substring(0, 4)
          if (dimBaseType === 4) {
            this.dateOptions.push({ label: this.$t('pages.year', { Year: dimYear }), value: dimValue })
          } else {
            if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
            if (dimBaseType === 1) {
              this.dateOptions[dimYear].children.push({ label: this.$t('text.' + this.months[parseInt(dimValue.substring(4))]), value: dimValue })
            } else if (dimBaseType === 2) {
              this.dateOptions[dimYear].children.push({ label: this.$t('pages.weekNum', { Week: dimValue.substring(4) }), value: dimValue })
            } else if (dimBaseType === 3) {
              this.dateOptions[dimYear].children.push({ label: this.$t('pages.quarterNum', { Quarter: this.$t('pages.' + this.quarters[parseInt(dimValue.substring(4))]) }), value: dimValue })
            } else if (dimBaseType === 5) {
              const month = dimValue.substring(4, 6)
              const day = dimValue.substring(6, 8).startsWith('0') ? dimValue.substring(7, 8) : dimValue.substring(6, 8)
              const monthChildren = this.dateOptions[dimYear].children
              const index = monthChildren.findIndex(item => item.label === this.$t('text.' + this.months[parseInt(month)]))
              index < 0 && monthChildren.push({ label: this.$t('text.' + this.months[parseInt(month)]), value: dimValue.substring(0, 6), children: [{ label: this.$t('pages.dateNum', { date: day }), value: dimValue }] })
              index >= 0 && monthChildren[index].children.push({ label: this.$t('pages.dateNum', { date: day }), value: dimValue })
            }
          }
        }
        this.source = axios.CancelToken.source()
        const cacheToken = this.source.token
        getDimTime(this.query.dimBaseType, cacheToken).then(response => {
          this.dateOptions.splice(0)
          response.data.sort((a, b) => {
            return a > b ? 1 : -1 // 返回正数 ，b排列在a之前
          })
          for (let i = 0; i < response.data.length; i++) {
            let time = response.data[i] + ''
            while (time.length < 6) { time += '0' }
            formatterOption(time)
          }
          ++this.cascaderKey
          const children = this.dateOptions[this.dateOptions.length - 1].children
          // 上一周期值获取（如果存在没有上一周期数据的情况，则默认为当前最新数据）
          let children2 = this.dateOptions[this.dateOptions.length - 1].children
          if (this.dateOptions[this.dateOptions.length - 2]) {
            children2 = this.dateOptions[this.dateOptions.length - 2].children
          }
          if (this.query.dimBaseType === 4) {
            // console.log('this.dateOptions[this.dateOptions.length - 1].value', this.dateOptions[this.dateOptions.length - 1].value)
            // 大屏页面
            if (isConfig && value === 'initial' && this.isLargeScreenConfig === false) {
              if (dataTemp.dateCycleLargeScreen === 0 && this.query.dimBaseType !== 6) {
                this.query.dimValue = this.dateOptions[this.dateOptions.length - 2].value
              } else if (dataTemp.dateCycleLargeScreen === 1 && this.query.dimBaseType !== 6) {
                this.query.dimValue = this.dateOptions[this.dateOptions.length - 1].value
              } else {
                this.query.dimValue = dataTemp.encOrDecDimValue
              }
            } else if (isConfig && value === 'initial' && this.isLargeScreenConfig === true) {
              // 大屏配置弹框
              if (dataTemp.dateCycleLargeScreen === 2 || this.query.dimBaseType === 6) {
                this.query.dimValue = dataTemp.encOrDecDimValue
              } else {
                this.query.dimValue = this.dateOptions[this.dateOptions.length - 1].value
              }
            } else {
              this.query.dimValue = this.dateOptions[this.dateOptions.length - 1].value
            }
          } else if (this.query.dimBaseType === 5) {
            // 大屏页面
            if (isConfig && value === 'initial' && this.isLargeScreenConfig === false) {
              if (dataTemp.dateCycleLargeScreen === 0 && this.query.dimBaseType !== 6) {
                if (children[children.length - 1].children[children[children.length - 1].children.length - 2]) {
                  this.query.dimValue = children[children.length - 1].children[children[children.length - 1].children.length - 2].value
                } else {
                  this.query.dimValue = children2[children2.length - 1].children[children2[children2.length - 1].children.length - 2].value
                }
              } else if (dataTemp.dateCycleLargeScreen === 1 && this.query.dimBaseType !== 6) {
                this.query.dimValue = children[children.length - 1].children[children[children.length - 1].children.length - 1].value
              } else {
                this.query.dimValue = dataTemp.encOrDecDimValue
              }
            } else if (isConfig && value === 'initial' && this.isLargeScreenConfig === true) {
              // 大屏配置弹框
              if (dataTemp.dateCycleLargeScreen === 2 || this.query.dimBaseType === 6) {
                this.query.dimValue = dataTemp.encOrDecDimValue
              } else {
                this.query.dimValue = children[children.length - 1].children[children[children.length - 1].children.length - 1].value
              }
            } else {
              this.query.dimValue = children[children.length - 1].children[children[children.length - 1].children.length - 1].value
            }
          } else {
            // 大屏页面
            if (isConfig && value === 'initial' && this.isLargeScreenConfig === false) {
              if (dataTemp.dateCycleLargeScreen === 0 && this.query.dimBaseType !== 6) {
                this.query.dimValue = children[children.length - 2] ? children[children.length - 2].value : children2[children2.length - 1].value
              } else if (dataTemp.dateCycleLargeScreen === 1 && this.query.dimBaseType !== 6) {
                this.query.dimValue = children[children.length - 1] ? children[children.length - 1].value : ''
              } else {
                this.query.dimValue = dataTemp.encOrDecDimValue
              }
            } else if (isConfig && value === 'initial' && this.isLargeScreenConfig === true) {
              // 大屏配置弹框
              if (dataTemp.dateCycleLargeScreen === 2 || this.query.dimBaseType === 6) {
                this.query.dimValue = dataTemp.encOrDecDimValue
              } else {
                this.query.dimValue = children[children.length - 1] ? children[children.length - 1].value : ''
              }
            } else {
              this.query.dimValue = children[children.length - 1] ? children[children.length - 1].value : ''
            }
          }
          value === 'initial' && this.$parent.getData()
        }).catch(() => {
          if (this.loading) {
            this.loading = false
          }
        })
      }
    },
    /**
       * 级联选择器发生变化时
       * @param datas 当前选中日期时间
       * */
    dimValueChange(datas) {
      // console.log('datas', datas)
      if (datas && datas.length > 0) {
        this.query.dimValue = datas[datas.length - 1]
      } else {
        this.query.dimValue = undefined
      }
    }
  }
}
</script>

<style>
  .query-report-span>span{
    font-size: 14px;
  }
</style>
