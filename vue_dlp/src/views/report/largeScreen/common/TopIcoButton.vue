<template>
  <span>
    <div v-append-tooltip>
      <el-tooltip class="item" effect="dark" placement="bottom" :append-to-body="false">
        <div slot="content">
          {{ isFullScreen ? $t('pages.exitFullScreen') : $t('pages.fullScreen') }}
        </div>
        <i ref="tooltipRef" class="el-icon-full-screen" style="cursor: pointer;position: absolute;right: 65px" @click="fullscreenTags()"></i>
      </el-tooltip>
    </div>
    <div v-append-tooltip>
      <el-tooltip ref="tooltipRef2" class="item" effect="dark" placement="bottom" :append-to-body="false">
        <div slot="content">
          {{ $t('pages.customLayout') }}<br><br>
          {{ $t('pages.customLayoutMsg') }}
        </div>
        <i class="el-icon-s-tools" style="cursor: pointer;position: absolute;right: 35px" @click="configBtn"></i>
      </el-tooltip>
    </div>
  </span>
</template>

<script>
import { EventBus } from '@/layout/event-bus';

export default {
  name: 'TopIcoButton',
  data() {
    return {
      isFullScreen: false
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleFullScreenChange)
  },
  deactivated() {
    window.removeEventListener('resize', this.handleFullScreenChange)
  },
  methods: {
    /**
     * 全屏监听
     */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
    },
    /**
     * 全屏
     * */
    fullscreenTags() {
      EventBus.$emit('changeFullScreen')
    },
    /**
     * 布局配置
     */
    configBtn() {
      this.$parent.configBtn()
    }
  }
}
</script>

<style scoped>

</style>
