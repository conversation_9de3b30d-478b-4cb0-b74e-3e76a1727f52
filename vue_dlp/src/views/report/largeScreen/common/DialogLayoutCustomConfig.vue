<template>
  <div>
    <!-- 自定义布局 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.customLayoutConfig')"
      :visible.sync="customLayoutFormVisible"
      width="450px"
    >
      <ul class="custom-layout">
        <li>
          <span class="row-name">{{ $t('pages.row1') }}</span>
          <ul class="layout-setting">
            <li v-for="(item, index) in 8" :key="index" @click="setting(index, 1)">
              <svg-icon :icon-class="layoutIcon[index]" :class="temp.indexRow1===index ? 'active' : '  ' "/>
            </li>
          </ul>
        </li>
        <li>
          <span class="row-name">{{ $t('pages.row2') }}</span>
          <ul class="layout-setting">
            <li v-for="(item, index) in 8" :key="index" @click="setting(index, 2)">
              <svg-icon :icon-class="layoutIcon[index]" :class="temp.indexRow2===index ? 'active' : '  ' "/>
            </li>
          </ul>
        </li>
        <li>
          <span class="row-name">{{ $t('pages.row3') }}</span>
          <ul class="layout-setting">
            <li v-for="(item, index) in 8" :key="index" @click="setting(index, 3)">
              <svg-icon :icon-class="layoutIcon[index]" :class="temp.indexRow3===index ? 'active' : '  ' "/>
            </li>
          </ul>
        </li>
      </ul>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="customBtn">
          {{ $t('button.save') }}
        </el-button>
        <el-button @click="customLayoutFormVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DialogLayoutCustomConfig',
  components: { },
  props: {
    // DialogLayoutConfig组件中传递过来的layout
    layout: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      temp: {},
      // 每一行的选中值
      defaultTemp: {
        indexRow1: 0,
        indexRow2: 0,
        indexRow3: 0
      },
      submitting: false,
      layoutIcon: ['layout1-1', 'layout1-2', 'layout2-1', 'layout1-1-1', 'layout1-2-1', 'layout1', 'layout1-3', 'layout3-1'],
      customLayoutFormVisible: false
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
  },
  activated() {

  },
  mounted() {

  },
  beforeDestroy() {

  },
  deactivated() {

  },
  methods: {
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 弹框显示（父组件传过来的layout中的flag为页面需要选中的索引，进行默认选中操作）
     * */
    show() {
      this.resetTemp()
      this.temp.indexRow1 = this.layout[0].flag
      this.temp.indexRow2 = this.layout[1].flag
      this.temp.indexRow3 = this.layout[2].flag
      this.customLayoutFormVisible = true
    },
    /**
     * 布局点击时，切换页面显示效果
     * @param index 当前点击索引
     * @param type 区分当前点击是哪一行
     */
    setting(index, type) {
      // console.log('index', index)
      if (type === 1) {
        this.temp.indexRow1 = index
      } else if (type === 2) {
        this.temp.indexRow2 = index
      } else if (type === 3) {
        this.temp.indexRow3 = index
      }
    },
    /**
     * 确认按钮点击
     * 需要构造出行列关系
     */
    customBtn() {
      // console.log('temp', JSON.parse(JSON.stringify(this.temp)))
      const parentLayout = this.layout
      // 1、页面选中数据处理
      const row1 = this.processingLine(this.temp.indexRow1, parentLayout[0])
      const row2 = this.processingLine(this.temp.indexRow2, parentLayout[1])
      const row3 = this.processingLine(this.temp.indexRow3, parentLayout[2])
      // 2、页面选中和layout一起处理
      this.processingLayout(row1, parentLayout[0].cols)
      this.processingLayout(row2, parentLayout[1].cols)
      this.processingLayout(row3, parentLayout[2].cols)
      this.$parent.leftItemStyle(parentLayout)
      this.customLayoutFormVisible = false
      // console.log('layout确认', JSON.parse(JSON.stringify(this.layout[0].cols)))
      // console.log('parentLayout', JSON.parse(JSON.stringify(parentLayout)))
    },
    /**
     * 布局处理
     * 根据当前行选中，以及父组件传递过来的layout数据，对数据进行处理
     * @param processingLineArray 处理后的页面行数组(当前弹框选中的布局)
     * @param layoutArray 父组件传递的layout数据
     **/
    processingLayout(processingLineArray, layoutArray) {
      // 1、如果layout列大于当前选中列，需要删除layout中多余的列
      if (processingLineArray.length < layoutArray.length) {
        layoutArray.splice(processingLineArray.length)
      }
      // 2、如果layout列小于当前选中列，需要添加缺少列，直到两个数据长度相同
      if (processingLineArray.length > layoutArray.length) {
        for (let i = 0; i < layoutArray.length; i++) {
          if (processingLineArray.length > layoutArray.length) {
            layoutArray.push({ span: 0, name: '', type: '' })
          }
        }
      }
      // 3、长度相同时，将当前选中列信息赋值到layout的span中
      if (processingLineArray.length === layoutArray.length) {
        processingLineArray.forEach((item1, index1) => {
          layoutArray.forEach((item2, index2) => {
            if (index1 === index2) {
              item2.span = item1
            }
          })
        })
      }
    },
    /**
     * 处理行列：span数据
     * 根据页面当前选中，确定layout中span对应的一行为几列，每一列的大小是多少，每行为24，对24的分配如下：
     * 注：flag代表的布局：['layout1-1', 'layout1-2', 'layout2-1', 'layout1-1-1', 'layout1-2-1', 'layout1', 'layout1-3', 'layout3-1']
     * @param index 当前选中行列索引
     * @param layoutArray layout数据，将当前索引赋值到flag中，用于弹框初始显示时，默认选中哪一个
     */
    processingLine(index, layoutArray) {
      layoutArray.flag = index
      // 一行两列1-1，12 12
      if (index === 0) { return [12, 12] }
      // 一行两列1-2，8 16
      if (index === 1) { return [8, 16] }
      // 一行两列2-1，16 8
      if (index === 2) { return [16, 8] }
      // 一行三列1-1-1，8 8 8
      if (index === 3) { return [8, 8, 8] }
      // 一行三列1-2-1，6 12 6
      if (index === 4) { return [6, 12, 6] }
      // 一行一列1，24
      if (index === 5) { return [24] }
      // 一行两列1-3，6 18
      if (index === 6) { return [6, 18] }
      // 一行两列3-1，18 6
      if (index === 7) { return [18, 6] }
    }
  }
}
</script>

<style lang='scss' scoped>
  .custom-layout{
    position: relative;
    list-style-type: none;
  }
  .row-name {
    width: 50px;
    display: inline-block;
  }
  .layout-setting{
    position: relative;
    display: inline-block;
    list-style-type: none;
    margin: 10px 0px -10px 0px;
    height: 30px;
    line-height: 30px;
    li{
      float: left;
      width: 20px;
      font-size: 16px;
      margin-right: 10px;
      cursor: pointer;
    }
    .active{
      color: #2574b1;
    }
  }
</style>
