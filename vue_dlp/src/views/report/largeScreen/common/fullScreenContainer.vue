<!--
  全屏容器
  1、如果某个菜单需要全屏展示，需将改组件导入到全屏页面
  2、需要在该菜单路由配置中添加customPath属性，如：meta: { title: '终端使用情况数据', customPath: true }
  2、将全屏页面的内容用引入组件包裹即可：
  如：
  （1）导入组件：import fullScreenContainer from '@/views/report/largeScreen/common/fullScreenContainer';
  （2）<template>
        <full-screen-container>
            中间内容就是正常页面的内容
         </full-screen-container>
        </template>
-->

<template>
  <fullscreen :fullscreen.sync="isFullscreen">
    <!-- 这里放置页面特有的内容 -->
    <div :style="menuStyle">
      <slot></slot>
    </div>
  </fullscreen>
</template>

<script>
import Vue from 'vue'
import Fullscreen from 'vue-fullscreen'
Vue.use(Fullscreen)
import { EventBus } from '@/layout/event-bus';

export default {
  name: 'FullScreenContainer',
  components: { },
  directives: {
    'fullscreen': Fullscreen
  },
  data() {
    return {
      // 平板放大缩小相关配置
      scale: 1, // 当前缩放比例
      offsetX: 0, // 水平偏移量
      offsetY: 0, // 垂直偏移量
      initialDistance: null, // 初始的两个触摸点距离
      initialX: null, // 初始的触摸点 X 坐标
      initialY: null, // 初始的触摸点 Y 坐标
      isScaling: false, // 是否正在缩放
      isDragging: false, // 是否正在拖动
      // 平板双击相关配置
      lastTouchEndTime: 0, // 记录上一次触摸结束的时间
      doubleClickThreshold: 200, // 双击时间阈值（单位：毫秒）
      // 是否全屏
      customPath: this.$route.meta.customPath,
      // 全屏背景色
      bodyBg: '',
      // 控制页面全屏展示
      isFullscreen: false,
      // 是否首次加载
      isMounted: false
    }
  },
  computed: {
    // 动态计算菜单的样式
    menuStyle() {
      return {
        backgroundColor: this.bodyBg,
        transform: `scale(${this.scale}) translate(${this.offsetX}px, ${this.offsetY}px)`,
        transition: this.isScaling || this.isDragging ? 'none' : 'transform 0.2s ease' // 拖动或缩放时禁用过渡
      };
    }
  },
  watch: {
    $route(val) {
      // console.log('val', val)
      // 移除全屏监听（路由切换时，也需要清除，不然虽不影响功能，但是监听事件会累加，浏览器f12会报错）
      this.removeEvent()
    }
  },
  /**
     * 组件被重新显示时的钩子（添加监听事件，因为多个页面都用到这个组件，所以监听路由变化时移除了监听，如果这边不添加，菜单没有关闭重新显示时，f2事件会消失）
     * 页面重新展示时，判断isMounted为false时，才添加事件总线，否则初始化时会添加两个，导致点击全屏出现bug,
     * */
  activated() {
    // 如果是数据大屏，点击的时候直接放大
    if (this.$route.name === 'Carousel') {
      this.isFullscreen = true
    }
    if (this.isMounted === false) {
      this.addEvent()
    }
  },
  /**
     * 首次挂载到DOM时的钩子（添加监听事件，但是这个只有页面首次加载时执行一次，后期菜单切换时，就不加载了）
     * 首次挂载时，EventBus都添加事件总线，但是一秒后设置isMounted为false
     * */
  mounted() {
    // 如果是数据大屏，点击的时候直接放大
    if (this.$route.name === 'Carousel') {
      this.isFullscreen = true
    }
    // 全屏监听
    this.isMounted = true
    // console.log('首次加载开11111111111111,isMounted', this.isMounted)
    this.addEvent()
    setTimeout(() => {
      this.isMounted = false
    }, 1000)
  },
  /**
     * 组件销毁时，（移除监听）
     * */
  beforeDestroy() {
    // 组件关闭时--清除监听
    this.removeEvent()
  },
  created() {

  },
  methods: {
    addEvent() {
      // 添加监听
      window.addEventListener('keydown', this.handleKeyDown);
      window.addEventListener('resize', this.handleFullScreenChange)
      window.addEventListener('touchstart', this.handleTouchStart)
      window.addEventListener('touchmove', this.handleTouchMove, { passive: false })
      window.addEventListener('touchend', this.handleTouchEnd)
      EventBus.$on('changeFullScreen', this.reportFullScreen);
    },
    removeEvent() {
      // 清除监听
      window.removeEventListener('keydown', this.handleKeyDown);
      window.removeEventListener('resize', this.handleFullScreenChange)
      window.removeEventListener('touchstart', this.handleTouchStart)
      window.removeEventListener('touchmove', this.handleTouchMove)
      window.removeEventListener('touchend', this.handleTouchEnd)
      EventBus.$off('changeFullScreen', this.reportFullScreen);
    },
    /**
     * 重置页面（如果页面有放大缩小，在一些操作下需要初始页面为原始大小）
     * */
    resetScale() {
      this.scale = 1 // 当前缩放比例
      this.offsetX = 0 // 水平偏移量
      this.offsetY = 0 // 垂直偏移量
      this.initialDistance = null // 初始的两个触摸点距离
      this.initialX = null // 初始的触摸点 X 坐标
      this.initialY = null // 初始的触摸点 Y 坐标
      this.isScaling = false // 是否正在缩放
      this.isDragging = false // 是否正在拖动
    },
    /**
     * 手指触摸开始：记录原始的大小和距离等
     * */
    handleTouchStart(event) {
      if (event.touches.length === 2) {
        // 双指触摸，开始缩放
        this.isScaling = true;
        this.isDragging = false;
        this.initialDistance = this.getDistance(event.touches[0], event.touches[1]);
      } else if (event.touches.length === 1) {
        // 单指触摸，开始拖动
        this.isScaling = false;
        this.isDragging = true;
        this.initialX = event.touches[0].clientX - this.offsetX;
        this.initialY = event.touches[0].clientY - this.offsetY;
      }
    },
    /**
     * 手指触摸移动：进行缩放或拖动
     * */
    handleTouchMove(event) {
      if (this.isScaling && event.touches.length === 2) {
        // 双指缩放
        this.isScaling = true;
        this.isDragging = false;
        const currentDistance = this.getDistance(event.touches[0], event.touches[1]);
        if (this.initialDistance) {
          const newScale = (currentDistance / this.initialDistance) * this.scale;
          this.scale = Math.max(0.5, Math.min(3, newScale)); // 限制缩放范围
        }
      } else if (this.isDragging && event.touches.length === 1) {
        // 单指拖动
        const currentX = event.touches[0].clientX - this.initialX;
        const currentY = event.touches[0].clientY - this.initialY;
        this.offsetX = currentX;
        this.offsetY = currentY;
      }
    },
    /**
     * 触摸过程中，计算距离
     * */
    getDistance(touch1, touch2) {
      const dx = touch1.clientX - touch2.clientX;
      const dy = touch1.clientY - touch2.clientY;
      return Math.sqrt(dx * dx + dy * dy);
    },
    /**
     * 触摸结束
     * */
    handleTouchEnd() {
      // 双击配置
      if (this.isScaling) {
        // 如果是缩放操作，不处理双击事件
        this.isScaling = false;
        return;
      }
      const currentTime = new Date().getTime();
      if (currentTime - this.lastTouchEndTime < this.doubleClickThreshold) {
        // 双击：恢复初始大小
        this.resetScale()
      }
      // 更新上一次触摸结束的时间
      this.lastTouchEndTime = currentTime;
      // 放大缩小，相关配置
      this.isScaling = false;
      this.isDragging = false;
      this.initialDistance = null;
      this.initialX = null;
      this.initialY = null;
    },
    /**
       * 判断是否全屏（设置全屏的背景颜色）
       * 如果直接设置颜色或通过theme主体切换，都会存在各种bug，所以监听resize的变化时修改背景色
       * */
    handleFullScreenChange() {
      this.resetScale()
      const isFull = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      if (isFull) {
        // 如果是全屏，设备颜色为当前背景色
        this.bodyBg = window.getComputedStyle(document.body).backgroundColor
      } else {
        // 否则设置背景色为空
        this.bodyBg = ''
      }
    },
    /**
       *键盘按下事件，如果是大屏菜单，且按下f2，则打开全屏
       * */
    handleKeyDown(event) {
      // 路由中配置的customPath属性，如果有配置，代表大屏菜单
      const customPath = this.$route.meta.customPath
      if (event.key === 'F2' && customPath) {
        this.isFullscreen = true
      }
    },
    /**
       * 全屏展示（大屏配置）
       * 1、菜单点击时，全屏显示，如果需要大屏显示，需在路由中配置customPath: true）
       * 2、TagsView/index.vue页面点击全屏时，调用
       */
    reportFullScreen() {
      // 路由中配置的customPath属性，如果有配置，代表大屏菜单
      this.customPath = this.$route.meta.customPath
      if (this.customPath) {
        this.isFullscreen = !this.isFullscreen;
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>

