<!--
  /**
  * echarts柱状图封装，可根据实际需求修改配置项
  * 调用示例：
      <bar-chart
        :chart-data="chartData"       // chart数据，部分配置属性查看echarts文档的series
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

      chartData = [
        { name: 'pageA', data: [79, 52, 200, 334, 390, 330, 220] },
        { name: 'pageB', data: [80, 52, 200, 334, 390, 330, 220] },
        { name: 'pageC', data: [30, 52, 200, 334, 390, 330, 220] }
      ]
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import { debounce, deepMerge } from '@/utils'
import { showSaveImage } from '@/components/ECharts/saveImage'

require('echarts/theme/macarons') // echarts theme

export default {
  name: 'BarChart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据
    chartData: {
      type: Array,
      required: true
    },
    xAxisName: {
      type: String,
      default() {
        return this.$t('components.terminal')
      }
    },
    yAxisName: {
      type: String,
      default() {
        return this.$t('components.number')
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData)
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)
    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      const series = []
      const legendData = []
      if (chartData.length > 0) {
        chartData.forEach(data => {
          series.push({
            name: '',
            type: 'bar',
            data: [],
            animationDuration: 2800,
            animationEasing: 'quadraticOut',
            // 设置宽度
            barMaxWidth: '20%',
            // 设置渐变色
            itemStyle: {
              color: {
                type: 'line',
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#1E90FF' },
                  { offset: 1, color: '#87CEFA' }
                ]
              },
              emphasis: {
                opacity: 1
              }
            },
            ...data
          })
          legendData.push(data.name)
        })
      } else {
        series.push({
          name: '',
          type: 'bar',
          data: [],
          animationDuration: 2800,
          animationEasing: 'quadraticOut',
          // 设置宽度
          barMaxWidth: '20',
          // 设置渐变色
          itemStyle: {
            color: {
              type: 'line',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#1E90FF' },
                { offset: 1, color: '#87CEFA' }
              ]
            },
            emphasis: {
              opacity: 1
            }
          }
        })
        legendData.push([])
      }

      const defaultOpt = {
        title: {
          show: true,
          text: '', // 大标题
          subtext: '', // 子标题
          left: 'center'
          // x: 'left' // 标题所在位置：left、center
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(16,16,16,0.7)',
          confine: true,
          extraCssText: 'white-space: break-spaces;'
        },
        legend: {
          show: true,
          type: 'scroll',
          pageIconColor: '#008acd', // 翻页按钮的颜色
          data: legendData,
          x: 'center', // 图例所在位置：left、center
          y: 'bottom',
          padding: [0, 20, 5, 20],
          inactiveColor: '#666', // 图例关闭时的颜色。
          textStyle: {
            color: '#2281e4'
          }
        },
        toolbox: {
          show: false,
          feature: {
            // magicType: {
            //   show: true,
            //   type: ['line', 'bar'],
            //   title: {
            //     line: this.$t('components.switchToLine'),
            //     bar: this.$t('components.switchToBar')
            //   }
            // },
            saveAsImage: { show: true, title: this.$t('components.saveAsPic') }
          }
        },
        calculable: true,
        grid: {
          left: '2%',    // 距离左侧的距离
          right: '4%',   // 距离右侧的距离
          top: '18%',    // 距离顶部的距离
          bottom: '8%',  // 距离底部的距离
          containLabel: true
        },
        xAxis: {
          name: '',
          type: 'category',
          // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          data: [],
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            // interval: 0,
            // rotate: 40,
            formatter: (value) => {
              // 一行显示10个字符，最多显示3行，超过显示...
              var maxLength = 10;
              value = value.length > 30 ? value.substring(0, 27) + '...' : value
              var valLength = value.length;
              var rowN = Math.ceil(valLength / maxLength);
              if (rowN > 1) {
                // 根据行数计算每行的字符数
                const partLength = Math.floor(valLength / rowN);
                const result = [];
                let startIndex = 0;
                // 切割并放入result
                for (let i = 0; i < rowN; i++) {
                  if (i == rowN - 1) {
                    result.push(value.substring(startIndex));
                  } else {
                    result.push(value.substring(startIndex, startIndex + partLength));
                  }
                  startIndex += partLength;
                }
                return result.join('\n')
              } else {
                return value;
              }
            }
          },
          // 控制分隔线
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          // 控制隔区域
          splitArea: {
            show: false
          }
        },
        yAxis: [{
          name: this.yAxisName,
          type: 'value',
          minInterval: 1,
          axisLabel: {
            formatter: '{value}'
          },
          // 控制分隔线
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          // 控制隔区域
          splitArea: {
            show: false
          }
        }],
        series: series
      }
      const option = deepMerge(defaultOpt, this.chartOption)
      this.chart.setOption(option, true)
      // this.chart.setOption(deepMerge(defaultOpt, this.chartOption))
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })

      // 1、柱状图点击（只能点击柱子有颜色的位置，柱子外不能点击）
      // this.chart.on('click', (params) => {
      //   this.click(params)
      // })
      // 2、柱状图点击（当柱状图没有数据或数据很少时，可以点击鼠标悬停上去的背景色栏实现点击）
      this.chart.getZr().off('click')
      this.chart.getZr().on('click', params => {
        // 点击柱子的方法获取不到当前点击的名称，故：通过一些操作获取到当前点击的名称
        const pointInPixel = [
          params.offsetX,
          params.offsetY
        ];
        if (this.chart.containPixel('grid', pointInPixel)) {
          const op = this.chart.getOption();
          // console.log('op', op)
          // 竖向柱状图
          if (op.xAxis[0].data) {
            const index = this.chart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)[0];
            // console.log('index', index)
            params.name = op.xAxis[0].data[index]
          }
          // 横向柱状图
          if (op.yAxis[0].data) {
            const index = this.chart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)[1];
            // console.log('index', index)
            params.name = op.yAxis[0].data[index]
          }
          this.click(params)
        }
      })
      this.setOptions(this.chartData)
      showSaveImage(this.chart)
    }
  }
}
</script>
