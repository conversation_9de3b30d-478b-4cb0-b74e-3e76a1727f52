<!--
  /**
  * echarts饼状图封装，可根据实际需求修改配置项
  * 调用示例：
      <pie-chart
        :chart-data="chartData"       // chart数据，部分配置属性查看echarts文档的series
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

      chartData = [
        { value: 100, name: 'pie1' },
        { value: 85, name: 'pie2' },
        { value: 45, name: 'pie3' }
      ]
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'
import { showSaveImage } from '@/components/ECharts/saveImage'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '450px'
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据，[{ value: 1, name: 'name1' },{ value: 2, name: 'name2' }]
    chartData: {
      type: Array,
      default() {
        return []
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData)
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      // 后续优化，根据容器的变化实现图表resize
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      let legendData = []
      // 用于存储数据中重复name的map，以 name + id 作为key， name作为 value
      const duplicateMap = {}
      if (chartData.length) {
        legendData = chartData.map(item => {
          return item.name
        })
      } else if (chartData['legend']) {
        legendData = chartData.legend['data']
      }
      // 图例、图标label的字数限制值
      const labelLimit = 15
      const defaultOpt = {
        title: {
          text: '',
          subtext: '',
          left: 'center'
          // x: 'left'
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            // console.log('params', params)
            // 鼠标悬停，超过15个字符时，换行显示
            var threshold = 15;
            var name = params.name;
            if (name.length > threshold) {
              name = name.slice(0, threshold) + '</br>' + name.slice(threshold);
            }
            return params.marker + name + '：' + params.value;
          }
        },
        legend: {
          show: false,
          // 设置图例靠右，上下居中，垂直排列
          right: 0,
          top: 'center',
          orient: 'vertical',
          // 图例图标设置为圆形
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          padding: [0, 32, 0, 0],
          data: legendData, // 字符串数组，如'直接访问','邮件营销'
          inactiveColor: '#666', // 图例关闭时的颜色。
          formatter: name => {
            const legendName = duplicateMap[name] || name
            return echarts.format.truncateText(legendName, 120, '14px Microsoft Yahei', '…')
          },
          tooltip: { show: true },
          textStyle: {
            color: '#2281e4'
          }
        },
        color: ['#45C2E0', '#237fca', '#FFC851', '#5A5476', '#1869A0', '#FF9393'],
        toolbox: {
          show: false,
          feature: {
            saveAsImage: { show: true, title: this.$t('components.saveAsPic') }
          }
        },
        calculable: true,
        series: [
          {
            name: '',
            type: 'pie',
            // roseType: 'radius',
            radius: [0, 70], // 饼图大小
            center: ['49%', '45%'], // 饼图中心点
            label: {
              normal: {
                // formatter: '{b}',
                formatter: (params) => {
                  const labelName = duplicateMap[params.name] || params.name
                  return labelName.length > labelLimit ? labelName.substring(0, labelLimit) + '...' : labelName
                }
              }
            },
            labelLine: {
              normal: { length: 5, length2: 5 }
            },
            data: chartData,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      }
      const mergeOption = deepMerge(defaultOpt, this.chartOption)
      this.duplicateFormatter(mergeOption, duplicateMap)
      this.chart.setOption(mergeOption)
    },
    // 对 option 中的 legend 和 series 进行 重复name 的处理
    duplicateFormatter(option, duplicateMap) {
      option.legend.data.splice(0)
      const series = Array.isArray(option.series) ? option.series : [option.series]
      series.forEach((item) => {
        const seriesData = item.data
        seriesData.forEach((sData) => {
          const name = sData.name
          const id = sData.id
          if (!duplicateMap[name]) {
            // 非重复的name，直接以 name 作为 key
            duplicateMap[name] = name
          } else {
            // 重复的name，以 name + id 作为 key
            const newName = name + id
            duplicateMap[newName] = name
            // 修改 name， 并增加 duplicateName
            sData.name = newName
            sData.duplicateName = name
          }
        })
        // 添加图例
        option.legend.data.push(...seriesData.map(item => item.name))
      });
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.chart.on('click', (params) => {
        this.click(params)
      })
      this.setOptions(this.chartData)
      showSaveImage(this.chart)
    }
  }
}
</script>

<style lang="scss">
  .tooltip-dot{
    display: inline-block;
    margin-right: 5px;
    border-radius: 10px;
    width: 9px;
    height: 9px;
  }
  .dataView{
    width:100%;
    height:100%;
    border-top: 1px solid #000;
    padding: 10px;
    overflow: auto;
    color: #666;
  }
  .dataView table{
    width:100%;
    border:1px solid #666;
    border-collapse: collapse;
    font-size: 14px;
  }
  .dataView table th,.dataView table td{
    padding: 2px 10px;
    border: 1px solid #aaa;
  }
</style>
