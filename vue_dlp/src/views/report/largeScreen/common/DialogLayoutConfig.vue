<template>
  <div>
    <!-- 面板配置弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <template slot="title">
        <span style="color: #333; font-weight: bold">{{ $t('pages.layoutConfig') }}</span>
        <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
          <div slot="content" class="tip">
            <p>{{ $t('pages.largeScreenConfig33') }}</p>
          </div>
          <i class="el-icon-info" style="color: #333" />
        </el-tooltip>
      </template>
      <el-container>
        <el-aside class="layout-aside-config">
          <span class="left-drag-title">{{ $t('pages.largeScreenConfig34') }}</span>
          <div class="left-drag">
            <div
              v-for="(item, index) in configList"
              :key="index"
              :class="item.flag ? '  ' : 'itemClass'"
              draggable="true"
              @dragstart="handleDragStart(item)"
              @dragend="handleDragEnd"
            >
              <svg-icon :icon-class="item.type" />{{ item.name }}
            </div>
          </div>
        </el-aside>
        <el-main class="layout-main-config">
          <div>
            <div class="layout-main-custom">
              <el-button type="primary" @click="deleteAll">{{ $t('pages.clearLayout') }}</el-button>
              <el-tooltip class="item" effect="dark" placement="bottom">
                <div slot="content">
                  {{ $t('pages.customLayout') }}<br><br>
                  {{ $t('pages.largeScreenConfig35') }}
                </div>
                <el-button type="primary" @click="customLayoutBtn">{{ $t('pages.customLayout') }}</el-button>
              </el-tooltip>
            </div>
            <el-row v-for="(row, rowIndex) in layout" :key="'row-' + rowIndex" class="layout-main-row" :gutter="row.gutter">
              <el-col v-for="(col, colIndex) in row.cols" :key="'col-' + colIndex" :xs="8" :sm="8" :lg="col.span">
                <div class="layout-main-border" @dragover="dragover(rowIndex, colIndex)">
                  <svg-icon v-if="col.name !== ''" icon-class="delete" class="delete-icon delete-icon-item" :title="$t('button.clear')" @click="e => { selectedDatasDelete(e,rowIndex, colIndex) }"></svg-icon>
                  <div class="layout-main-name">{{ col.name }}</div>
                  <div class="layout-main-svg"><svg-icon :icon-class="col.type" class="layout-svf-item" /></div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <span style="color: #ff0000;font-size: 12px;float: left;line-height: 30px;display: inline-block;width: 80%;text-align: right">{{ errorMsg }}</span>
        <el-button :loading="submitting" type="primary" @click="layoutBtn">
          {{ $t('button.save') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
    <!--自定义布局-->
    <dialog-layout-custom-config ref="dialogLayoutCustomConfig" :layout="layout"/>
    <!--删除提示-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.prompt')"
      :visible.sync="deleteFormVisible"
      width="450px"
    >
      <div class="el-message-box__container">
        <div class="el-message-box__status el-icon-warning"></div>
        <div class="el-message-box__message"><p>{{ deleteMsg }}</p></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="deleteBtn">
          {{ $t('button.confirm2') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DialogLayoutCustomConfig from '@/views/report/largeScreen/common/DialogLayoutCustomConfig';
import { insertPersonalization } from '@/api/user';
export default {
  name: 'DialogLayoutConfig',
  components: { DialogLayoutCustomConfig },
  props: {
    /**
     * 九宫格布局
     * */
    layout: {
      type: Array,
      default() {
        return []
      }
    },
    /**
     * 原始布局，用于显示左侧列表
     * */
    originalLayout: {
      type: Array,
      default() {
        return []
      }
    },
    /**
     * 接口参数
     * */
    parameterOption: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      submitting: false,
      dialogFormVisible: false,
      errorMsg: '',
      // 拖拽内容
      dragObj: {},
      // 拖拽到九宫格的索引
      rowCustomIndex: undefined,
      colCustomIndex: undefined,
      // 左侧可拖拽的列表
      configList: [],
      // 清空布局和清空单个弹框改造，使用普通的this.$confirmBox在全屏状态下，不显示弹框，故用dialog替换
      deleteFormVisible: false,
      deleteMsg: '',
      deleteStatus: 'all',
      singleRowIndex: undefined,
      singleColIndex: undefined
    }
  },
  computed: {
  },
  created() {

  },
  activated() {

  },
  mounted() {

  },
  beforeDestroy() {

  },
  deactivated() {

  },
  methods: {
    /**
     * 确认按钮点击
     * */
    layoutBtn() {
      this.submitting = true
      const layoutSubmit = JSON.parse(JSON.stringify(this.layout))
      const currentLayout = this.getArrayList(layoutSubmit)
      // console.log('currentLayout', JSON.parse(JSON.stringify(currentLayout)))
      let isNull = 0
      let isName = 0
      for (let i = 0; i < currentLayout.length; i++) {
        if (currentLayout[i].name === '' && currentLayout[i].type === '') {
          isNull = 1
          this.submitting = false
          this.errorMsg = this.$t('pages.largeScreenConfig36')
          return false
        } else {
          this.errorMsg = ''
        }
      }
      if (this.hasDuplicateName(currentLayout) !== '') {
        isName = 1
        // 通过feature获取重复对象的name字段，用于弹框提示
        const getRepeatObj = currentLayout.find(obj => obj.feature === this.hasDuplicateName(currentLayout));
        this.submitting = false
        this.errorMsg = this.$t('pages.largeScreenConfig37') + '：“' + getRepeatObj.name + '”'
        return false
      } else {
        this.errorMsg = ''
      }
      if (isNull === 0 && isName === 0) {
        const userId = this.$store.getters.userId
        const tempString = JSON.stringify(this.layout)
        insertPersonalization({ sysUserId: userId, menuCode: this.parameterOption.menuCode, value: tempString, tableKey: this.parameterOption.tableKey, type: 4 }).then(response => {
          // console.log('this.layout确认按钮点击', JSON.parse(JSON.stringify(this.layout)))
          // console.log('this.$route', this.$route)
          this.$parent.getLayout('config')
          // 修改后页面需要重新加载，不然echarts图表不会自适应页面布局大小
          const { fullPath } = this.$route
          this.$nextTick(() => {
            this.$router.replace({
              path: '/redirect' + fullPath
            })
          })
          this.dialogFormVisible = false
          this.submitting = false
        }).catch(res => {
          this.submitting = false
        })
      }
    },
    /**
     * 获取左侧拖拽列表数据，直接从layout的cols中获取，组成一个普通对象数组
     * */
    getConfigList() {
      const configList = JSON.parse(JSON.stringify(this.originalLayout))
      this.configList = this.getArrayList(configList)
    },
    /**
     * 弹框显示
     * */
    show() {
      this.getConfigList()
      this.errorMsg = ''
      this.dialogFormVisible = true
      this.leftItemStyle(this.originalLayout)
    },
    /**
     * 打开布局配置弹框
     */
    customLayoutBtn() {
      this.$refs.dialogLayoutCustomConfig.show()
    },
    /**
     * 判断对象数组中是否存在feature字段值相同的对象（用于保存时重复提示）
     * @param arr 需要判断的对象数组
     * @return 返回重复对象的feature字段，如果不重复，则返回''字符串
     * */
    hasDuplicateName(arr) {
      // console.log('arr', JSON.parse(JSON.stringify(arr)))
      let resp = ''
      const features = arr.map(obj => obj.feature);
      features.some((item, index) => {
        if (features.indexOf(item) !== index) {
          resp = item
        }
      })
      return resp
    },
    /**
     * 将layout中每一行中的列取出来，组成一个普通的对象数组[{},{}] 用在通用的地方
     * @param layoutData layout 原始数据
     * */
    getArrayList(layoutData) {
      const layoutDataArray = []
      layoutData.map(obj => {
        obj.cols.forEach(item => {
          layoutDataArray.push(item)
        })
      });
      // console.log('layoutDataArray', JSON.parse(JSON.stringify(layoutDataArray)))
      return layoutDataArray
    },
    /**
     * 删除弹框确定按钮点击时，进行删除
     * */
    deleteBtn() {
      // 清空全部
      if (this.deleteStatus === 'all') {
        this.layout.map(obj => {
          // console.log('obj', JSON.parse(JSON.stringify(obj)))
          obj.cols.forEach(item => {
            // console.log('item', JSON.parse(JSON.stringify(item)))
            item.name = ''
            item.type = ''
            item.feature = ''
          })
        });
      } else {
        // 清空单个
        this.layout[this.singleRowIndex].cols[this.singleColIndex].name = ''
        this.layout[this.singleRowIndex].cols[this.singleColIndex].type = ''
        this.layout[this.singleRowIndex].cols[this.singleColIndex].feature = ''
      }
      this.leftItemStyle(this.layout)
      this.deleteFormVisible = false
    },
    /**
     * 删除：全部删除（九宫格中拖拽的内容）（弹框打开）
     * */
    deleteAll() {
      this.deleteFormVisible = true
      this.deleteMsg = this.$t('pages.largeScreenConfig38')
      this.deleteStatus = 'all'
    },
    /**
     * 删除：单个删除（弹框打开）
     * */
    selectedDatasDelete(e, rowIndex, colIndex) {
      this.deleteFormVisible = true
      this.deleteMsg = this.$t('pages.largeScreenConfig39')
      this.deleteStatus = 'single'
      this.singleRowIndex = rowIndex
      this.singleColIndex = colIndex
    },
    /**
     * 拖拽开始：获取当前拖拽元素，用于拖拽结束后的元素修改
     * @param item 当前拖拽元素
     * 添加报表图表数据内容（处理）
     * */
    handleDragStart(item) {
      this.dragObj = item
    },
    /**
     * 拖拽在九宫格div上移动时：获取当前移动在哪个九宫格中，方便拖拽结束时修改改九宫格中的元素
     * @param rowIndex 行索引
     * @param colIndex 列索引
     */
    dragover(rowIndex, colIndex) {
      this.rowCustomIndex = rowIndex
      this.colCustomIndex = colIndex
    },
    /**
     * 拖拽结束：将拖拽元素放入想要拖拽的九宫格中
     */
    handleDragEnd() {
      // console.log('拖拽结束', 'rowIndex:', this.rowCustomIndex, 'colIndex:', this.colCustomIndex, 'dragObj:', this.dragObj)
      if (this.rowCustomIndex !== undefined && this.colCustomIndex !== undefined) {
        this.layout[this.rowCustomIndex].cols[this.colCustomIndex].name = this.dragObj.name
        this.layout[this.rowCustomIndex].cols[this.colCustomIndex].type = this.dragObj.type
        this.layout[this.rowCustomIndex].cols[this.colCustomIndex].feature = this.dragObj.feature
        // 如果不初始化，在九宫格外随便拖，都会拖进上一次拖拽过的九宫格中去
        this.rowCustomIndex = undefined
        this.colCustomIndex = undefined
      }
      this.leftItemStyle(this.layout)
    },
    /**
     * 设置左侧拖拽列表颜色（如果右侧没有配置，则配置项显示蓝色，否则显示正常颜色）
     * @param layoutConfig 当前布局配置
     */
    leftItemStyle(layoutConfig) {
      // currentConfig：当前弹框右侧配置内容，originalList左侧原始列表
      const currentConfig = this.getArrayList(JSON.parse(JSON.stringify(layoutConfig)))
      let originalList = this.getArrayList(JSON.parse(JSON.stringify(this.originalLayout)))
      // 创建一个currentConfig中所有feature的集合，以便快速查找
      const setCurrent = new Set(currentConfig.map(item => item.feature));
      // 遍历originalList并根据setCurrent中的feature设置flag属性
      originalList = originalList.map(item => {
        return {
          ...item,
          flag: setCurrent.has(item.feature) // 如果currentConfig中有相同的feature, 则为true, 否则为false
        };
      });
      // 将加了flag属性的数据重新赋值给左侧列表，页面通过flag标签控制当前拖拽选项的颜色变化
      this.configList = originalList
      // console.log('originalList', JSON.parse(JSON.stringify(originalList)));
    }
  }
}
</script>

<style lang='scss' scoped>
  .itemClass{
    color: #1296db;
  }
  .layout-aside-config{
    width: 210px !important;
    height: 376px;
    border: 1px solid #aaaaaa;
    .left-drag-title{
      display: inline-block;
      width: 100%;
      height: 35px;
      line-height: 35px;
      padding-left: 10px;
      border-bottom: 1px solid #aaaaaa;
      color: #2674b2;
      font-weight: bold;
    }
    .left-drag{
      div{
        height: 30px;
        line-height: 30px;
        cursor: move;
        svg{
          margin: 0 10px 0 10px;
        }
      }
    }
  }
  .layout-main-config{
    overflow-x: hidden;
    .layout-main-custom{
      text-align: right;
      margin-right: 10px;
      cursor: pointer;
      color: #2674b2;
    }
    .layout-main-border{
      height: 98px;
      margin: 10px;
      border: 1px solid #aaa;
      position: relative;
      .delete-icon{
        position: absolute;
        right: 3px;
        top: 3px;
        cursor: pointer;
      }
      .layout-main-name{
        height: 40px;
        line-height: 40px;
        font-size: 12px;
        text-align: center;
      }
      .layout-main-svg{
        position: relative;
        .layout-svf-item{
          font-size: 40px;
          position: absolute;
          top: 20px;
          right: 0;
          left: 0;
          bottom: 0;
          margin: auto;
        }
      }
    }
  }
  .delete-icon-item{
    display: none;
  }
  .layout-main-border:hover .delete-icon-item{
    display: block;
  }
</style>
