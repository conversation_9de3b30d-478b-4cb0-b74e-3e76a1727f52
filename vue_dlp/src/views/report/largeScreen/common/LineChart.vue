<!--
  /**
  * echarts线性图封装，可根据实际需求修改配置项
  * 调用示例：
      <line-chart
        :chart-data="chartData"       // chart数据，部分配置属性查看echarts文档的series
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

      chartData = [
        { name: 'line1', data: [100, 120, 161, 134, 105, 160, 165] },
        { name: 'line2', data: [120, 82, 91, 154, 162, 140, 145] }
      ]
  */
-->
<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: <PERSON>olean,
      default: true
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chart数据
    chartData: {
      type: Array,
      required: true
    },
    yAxisName: {
      type: String,
      default() {
        return this.$t('components.number')
      }
    },
    xAxisName: {
      type: String,
      default() {
        return this.$t('text.date')
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData)
      }
    }
  },
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.__resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      window.addEventListener('resize', this.__resizeHandler)
    }

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.__resizeHandler)
    }

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData) {
      const series = []
      const legendData = []

      if (chartData.length) {
        chartData.forEach(data => {
          series.push({
            name: '',
            smooth: 0.3,
            type: 'line',
            data: [],
            animationDuration: 2800,
            animationEasing: 'quadraticOut',
            ...data
          })
          legendData.push(data.name)
        })
      }
      const defaultOpt = {
        title: {
          show: true,
          text: '', // 大标题
          subtext: '', // 子标题
          left: 'center'
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          backgroundColor: 'rgba(16,16,16,0.7)',
          confine: true,
          extraCssText: 'white-space: break-spaces;'
        },
        legend: {
          show: true,
          type: 'scroll',
          pageIconColor: '#008acd', // 翻页按钮的颜色
          data: legendData,
          bottom: 0,
          x: 'center', // 图例所在位置：left、center
          padding: [0, 20, 5, 20],
          inactiveColor: '#666', // 图例关闭时的颜色。
          textStyle: {
            color: '#2281e4'
          }
        },
        toolbox: {
          show: false
        },
        calculable: true,
        xAxis: {
          boundaryGap: false,
          name: this.xAxisName,
          type: 'category',
          data: [],
          axisLabel: {
            formatter: function(value, index) {
              // return defaultOpt.formatTime(value, true)
              // 一行显示10个字符，最多显示3行，超过显示...
              var maxLength = 10;
              value = value.length > 30 ? value.substring(0, 27) + '...' : value
              var valLength = value.length;
              var rowN = Math.ceil(valLength / maxLength);
              if (rowN > 1) {
                // 根据行数计算每行的字符数
                const partLength = Math.floor(valLength / rowN);
                const result = [];
                let startIndex = 0;
                // 切割并放入result
                for (let i = 0; i < rowN; i++) {
                  if (i == rowN - 1) {
                    result.push(value.substring(startIndex));
                  } else {
                    result.push(value.substring(startIndex, startIndex + partLength));
                  }
                  startIndex += partLength;
                }
                return result.join('\n')
              } else {
                return value;
              }
            }
          }
        },
        yAxis: {
          name: this.yAxisName,
          type: 'value',
          minInterval: 1,
          axisLabel: {
            formatter: '{value}'
          },
          // 控制分隔线
          splitLine: {
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          // 控制隔区域
          splitArea: {
            show: false
          }
        },
        series: series,
        grid: {
          left: '2%',    // 距离左侧的距离
          right: '10%',   // 距离右侧的距离
          top: '18%',    // 距离顶部的距离
          bottom: '20%',  // 距离底部的距离
          containLabel: true
        },
        color: ['#45C2E0', '#FFC851'],
        loadEnd: function(chart, chartInfo) {},
        formatTime: function(value, isn) {
          if (value) {
            if (value.length == 'yyyyMMddHHmm'.length) {
              const sp = isn ? '\n' : ' '
              return value.substring(0, 4) + '-' + value.substring(4, 6) + '-' + value.substring(6, 8) +
                sp + value.substring(8, 10) + ':' + value.substring(10, 12)
            } else if (value.length == 'yyyyMMddHH'.length) {
              const sp = isn ? '\n' : ' '
              return value.substring(0, 4) + '-' + value.substring(4, 6) + '-' + value.substring(6, 8) +
                        sp + value.substring(8, 10) + ':00'
            } else if (value.length == 'yyyyMMdd'.length) {
              return value.substring(0, 4) + '-' + value.substring(4, 6) + '-' + value.substring(6, 8)
            } else if (value.length == 'yyyyMM'.length) {
              return value.substring(0, 4) + this.$t('text.year') + value.substring(4, 6) + this.$t('text.month')
            } else if (value.length == 'yyyyQ'.length) {
              return value.substring(0, 4) + this.$t('text.year') + value.substring(4, 5) + this.$t('text.quarter')
            } else if (value.length == 'yyyy'.length) {
              return value.substring(0, 4)
            } else {
              return value
            }
          }
          return ''
        }
      }

      this.chart.setOption(deepMerge(defaultOpt, this.chartOption), true)
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.chart.on('click', (params) => {
        this.click(params)
      })
      this.setOptions(this.chartData)
    }
  }
}
</script>
