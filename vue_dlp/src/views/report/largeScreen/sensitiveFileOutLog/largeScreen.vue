<template>
  <div :style="{ height: appContainerHeight}" class="app-container largeScreen-report">
    <div class="table-container">
      <div class="large-screen-head">
        <div class="large-screen-head-title">{{ $t('route.outgoingFileAnalysisData') }}</div>
        <!--全屏、布局配置-->
        <TopIcoButton/>
        <div class="large-screen-head-time">
          <div>{{ $t('pages.dataWasLastUpdated') }}：{{ temp.lastUpdateTime }}</div>
          <div>{{ $t('pages.currentSystemTime') }}：{{ temp.currentTime }}</div>
        </div>
        <div class="large-screen-head-search">
          <query ref="queryData" :data-temp="dataTemp" @thisQuery="thisQuery"/>
        </div>
      </div>
      <div class="large-screen-body">
        <el-row v-for="(row, rowIndex) in layout" :key="'row-' + rowIndex" class="large-screen-row" :gutter="row.gutter">
          <el-col v-for="(col, colIndex) in row.cols" :key="'col-' + colIndex" :xs="24" :sm="24" :lg="col.span">
            <!--小方块-->
            <div v-if="col.feature ==='dataItemCondition'" class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-name">
                  <span v-if="index !== 0 && index !== 1" class="today-color">{{ $t('pages.today') }}</span>
                  {{ item.currentName }}
                </div>
                <div class="large-screen-middle-num">{{ item.currentNum }}</div>
              </div>
            </div>
            <div v-if="col.feature !=='dataItemCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div v-if="col.feature ==='todayFileOutingCondition'" class="large-screen-title">
                  <span class="today-color">{{ $t('pages.today') }}</span>{{ $t('report.outgoingFileTransferTrend') }}</div>
                <div v-else class="large-screen-title">{{ col.name }}</div>
              </div>
              <!--表格--风险外传实时预警-->
              <div v-if="col.feature ==='riskLogCondition'" class="large-screen-chart-div">
                <div class="div_risk_ranking_item  last_span_time" style="color: orange">
                  <span>{{ $t('table.terminal') }}</span>
                  <span>{{ $t('table.terminalGroup') }}</span>
                  <span>{{ $t('table.fileName') }}</span>
                  <span>{{ $t('table.severity') }}</span>
                  <span>{{ $t('table.outwardRoute') }}</span>
                  <span>{{ $t('table.time') }}</span>
                </div>
                <div v-if="temp.riskRowDatas.length === 0" :style="{ height: rankingDivHeight}" class="div_risk_ranking-chart-div no-data">
                  <div>{{ $t('text.noData') }}...</div>
                </div>
                <div v-else :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.riskRowDatas" :class-option="classOption">
                    <div v-for="(item, index) in temp.riskRowDatas" :key="index" :class="item.isNew ? 'activeClassRed' : '  '" class="div_risk_ranking_item  last_span_time">
                      <span :title="item.terminalName">{{ item.terminalName }}</span>
                      <span :title="item.termGroupName">{{ item.termGroupName }}</span>
                      <span :title="item.fileName">{{ item.fileName }}</span>
                      <span :title="item.severity">{{ item.severity }}</span>
                      <span :title="item.lossType">{{ item.lossType }}</span>
                      <span :title="item.createTime">{{ item.createTime }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
              <!--表格--实时文件外传事件-->
              <div v-if="col.feature ==='eventLogCondition'" class="large-screen-chart-div">
                <div class="div_risk_ranking_item  last_span_time" style="color: orange">
                  <span>{{ $t('table.terminal') }}</span>
                  <span>{{ $t('table.terminalGroup') }}</span>
                  <span>{{ $t('table.fileName') }}</span>
                  <span>{{ $t('table.outwardRoute') }}</span>
                  <span>{{ $t('table.time') }}</span>
                </div>
                <div v-if="temp.eventRowDatas.length === 0" :style="{ height: rankingDivHeight}" class="div_risk_ranking-chart-div no-data">
                  <div>{{ $t('text.noData') }}...</div>
                </div>
                <div v-else :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.eventRowDatas" :class-option="classOption">
                    <div v-for="(item, index) in temp.eventRowDatas" :key="index" :class="item.isNew ? 'activeClassRed' : '  '" class="div_risk_ranking_item  last_span_time">
                      <span :title="item.terminalName">{{ item.terminalName }}</span>
                      <span :title="item.termGroupName">{{ item.termGroupName }}</span>
                      <span :title="item.fileName">{{ item.fileName }}</span>
                      <span :title="item.logSource">{{ item.logSource }}</span>
                      <span :title="item.createTime">{{ item.createTime }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
              <!--除列表和小方块以外的内容-->
              <div v-else class="large-screen-chart-div">
                <pie-chart
                  v-if="col.feature === 'categoryCondition' && temp.fileCategoryData.length > 0"
                  :chart-data="temp.fileCategoryData"
                  :chart-option="temp.fileCategoryOption"
                  :style="{ height: chartDivHeight}"
                />
                <div v-if="col.feature === 'categoryCondition' && temp.fileCategoryData.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <line-chart
                  v-if="col.feature === 'historyFileOutingCondition' && temp.historicalOutgoingTrendOption.series[0].data.length > 0"
                  :chart-data="temp.historicalOutgoingTrendData"
                  :chart-option="temp.historicalOutgoingTrendOption"
                  :style="{ height: chartDivHeight}"
                />
                <div v-if="col.feature === 'historyFileOutingCondition' && temp.historicalOutgoingTrendOption.series[0].data.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <bar-chart
                  v-if="col.feature === 'outgoingRouteCondition' && temp.wayOption.series[0].data.length > 0"
                  :chart-data="temp.wayData"
                  :chart-option="temp.wayOption"
                  :style="{ height: chartDivHeight}"
                />
                <div v-if="col.feature === 'outgoingRouteCondition' && temp.wayOption.series[0].data.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <line-chart
                  v-if="col.feature === 'todayFileOutingCondition' && temp.todayOutgoingTrendOption.series[0].data.length > 0"
                  :chart-data="temp.todayOutgoingTrendData"
                  :chart-option="temp.todayOutgoingTrendOption"
                  :x-axis-name="xAxisName"
                  :style="{ height: chartDivHeight}"
                />
                <div v-if="col.feature === 'todayFileOutingCondition' && temp.todayOutgoingTrendOption.series[0].data.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <bar-chart
                  v-if="col.feature === 'userTop10Condition' && temp.barChartUserOption.series[0].data.length > 0"
                  :chart-data="temp.barChartUserTopData"
                  :chart-option="temp.barChartUserOption"
                  :style="{ height: chartDivHeight}"
                />
                <div v-if="col.feature === 'userTop10Condition' && temp.barChartUserOption.series[0].data.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <pie-chart
                  v-if="col.feature === 'riskEventCondition' && temp.gradeData.length > 0"
                  :chart-data="temp.gradeData"
                  :chart-option="temp.gradeOption"
                  :style="{ height: chartDivHeight}"
                />
                <div v-if="col.feature === 'riskEventCondition' && temp.gradeData.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--布局配置弹框-->
    <dialog-layout-config ref="dialogLayoutConfig" :original-layout="originalLayout" :layout="orLayout" :parameter-option="parameterOption"/>
  </div>
</template>

<script>
import Query from '@/views/report/largeScreen/common/query'
import LineChart from '@/views/report/largeScreen/common/LineChart'
import PieChart from '@/views/report/largeScreen/common/PieChart'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import { getLossType } from '@/api/dataEncryption/encryption/sensitiveFileConfig';
import { getCurrentTime, getSetIntervalTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
import DialogLayoutConfig from '@/views/report/largeScreen/common/DialogLayoutConfig';
import { displayPanelGetTodayFileOutRecord, displayPanelGetFileOutPanel, displayPanelGetFileOutPanelNoOplog, displayPanelGetTodayFileOutCount, displayPanelGetTodayRiskFileOutRecord } from '@/api/report/baseReport/largeScreen/largeScreen';
import { getValueByCondition } from '@/api/user';
import TopIcoButton from '../common/TopIcoButton';
import axios from 'axios';

export default {
  name: 'LargeScreen',
  components: { Query, PieChart, LineChart, BarChart, vueSeamlessScroll, DialogLayoutConfig, TopIcoButton },
  props: {
    // 大屏轮播是否首次加载
    isFirst: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 风险外发实时预警  外传途径
      alarmType: [],
      xAxisName: this.$t('table.time'),
      // 大屏配置中报表日期，传值给query组件
      dataTemp: {
        encOrDecDimBaseType: 1,
        encOrDecDimValue: '',
        dateCycleLargeScreen: 0
      },
      // 是否首次加载
      isMounted: false,
      // 接口参数(自定义布局)
      parameterOption: {
        menuCode: 'S13',
        tableKey: 'outgoingDocument'
      },
      // 深拷贝originalLayout,否则弹框修改时，会影响父组件layout数据
      orLayout: [],
      // 页面需要展示的布局
      layout: [],
      // 动态配置布局
      // 注：flag代表的布局：['layout1-1', 'layout1-2', 'layout2-1', 'layout1-1-1', 'layout1-2-1', 'layout1', 'layout1-3', 'layout3-1']
      // 如flag:3为索引3，既平均分配的三列显示：layout1-1-1
      // feature:每一个展示项有一个特有的feature名称，不能重复，用于页面动态显示时判断
      // 初始化布局，layout会随着接口返回发生变化，所以初始化的数据要一直保留，不被修改
      originalLayout: [
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('report.distributionOfOutgoingFiles'), type: 'chart-pie', feature: 'categoryCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg45'), type: 'chart-data', feature: 'dataItemCondition' },
            { span: 8, name: this.$t('report.realTimeEarlyWarningOfExternalRisks'), type: 'chart-log', feature: 'riskLogCondition' }
          ]
        },
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('report.trendOfOutgoingHistoricalFiles'), type: 'chart-line', feature: 'historyFileOutingCondition' },
            { span: 8, name: this.$t('report.distributionOfOutgoingFileChannels'), type: 'chart-pie', feature: 'outgoingRouteCondition' },
            { span: 8, name: this.$t('report.todayOutgoingDocumentTransferTrend'), type: 'chart-line', feature: 'todayFileOutingCondition' }
          ]
        },
        { gutter: 20, flag: 5,
          cols: [
            { span: 8, name: this.$t('report.operatorOutgoingTop10'), type: 'chart-bar', feature: 'userTop10Condition' },
            { span: 8, name: this.$t('report.riskEventLevelDistribution'), type: 'chart-pie', feature: 'riskEventCondition' },
            { span: 8, name: this.$t('report.realTimeOutgoingFileTransferEvent'), type: 'chart-log', feature: 'eventLogCondition' }
          ]
        }
      ],
      // 一个函数执行完在执行另一个函数
      dataLoadPromise: undefined,
      // 实时文件外传事件，1分钟
      fileOutingEventTime: 60000,
      // 今日数据，1分钟
      todayDataTime: 60000,
      // 风险外传实时预警，1分钟
      riskOutingTime: 60000,
      // 查询数据，3小时
      searchIntervalTime: 10800000,
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 中断请求
      sourceGetLargeScreenConfig: null,
      sourceGetLayout: null,
      sourceGetTodayFileOutRecord: null,
      sourceGetFileOutPanel: null,
      sourceGtTodayFileOutCount: null,
      sourceGetTodayRiskFileOutRecord: null,
      // 定时器
      currentTime: undefined,
      timerFileOutingEvent: undefined,
      timerTodayData: undefined,
      timerRiskOuting: undefined,
      timerSearch: undefined,
      // 严重程度
      severityMapping: {
        1: this.$t('pages.severityOptions1'),
        2: this.$t('pages.severityOptions2'),
        3: this.$t('pages.severityOptions3'),
        4: this.$t('pages.severityOptions4')
      },
      // 外传方式
      logSourceMapping: {
        dwd_print_monitor_log: this.$t('pages.filePrint'),
        dwd_mtp_file_log: this.$t('table.mtpSendFileSumAll'),
        dwd_browser_upload_log: this.$t('table.webUploadFileSumAll'),
        dwd_net_file_share_record: this.$t('report.networkSharedFile'),
        dwd_local_file_share_record: this.$t('table.localShareFileSumAll'),
        dwd_net_disk_log: this.$t('table.webDiskUploadFileSumAll'),
        dwd_ftp_monitor_log: this.$t('report.ftpFileTransfer'),
        dwd_adb_monitor_log: this.$t('table.adbSendFileSumAll'),
        dwd_usb_device_opera_log: this.$t('pages.stgMessage1'),
        dwd_bt_file_log: this.$t('table.bluetoothSendFileSumAll'),
        dwd_chat_log_ex: this.$t('report.communicationToolsTransferFiles'),
        dwd_mail_log: this.$t('report.emailSendingFile'),
        dwd_general_scan_violate_log: this.$t('report.sensitiveExtraneous')
      },
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight: '19vh',
      middleOutHeight: '7.1vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      // 查询条件
      tempQuery: {},
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: getCurrentTime(),
        currentTime: '',
        middleMsg: [
          { currentName: this.$t('report.totalNumberOfOutgoingFiles'), currentNum: '0' },
          { currentName: this.$t('report.totalAmountOfRiskIssued'), currentNum: '0' },
          { currentName: this.$t('pages.fileOutgoing_Msg1'), currentNum: '0' },
          { currentName: this.$t('report.theAmountOfRiskIssued'), currentNum: '0' },
          { currentName: this.$t('report.numberOfPersonnelAtRisk'), currentNum: '0' },
          { currentName: this.$t('report.numberOfRiskTerminals'), currentNum: '0' }
        ],
        // 外传文件分类分布(饼图)
        fileCategoryData: [],
        fileCategoryOption: {
          series: [
            {
              radius: ['30%', '50%']
            }
          ]
        },
        // 风险外传实时预警
        riskRowDatas: [],
        // 风险事件实时预警
        eventRowDatas: [],
        // 历史文件外传趋势(折线图)
        historicalOutgoingTrendData: [],
        historicalOutgoingTrendOption: {
          legend: {
            data: [this.$t('report.outgoingDocument'), this.$t('report.riskOutsourcing')]
          },
          xAxis: {
            data: []
          },
          series: [
            {
              name: this.$t('report.outgoingDocument'),
              data: [],
              type: 'line'
            },
            {
              name: this.$t('report.riskOutsourcing'),
              data: [],
              type: 'line'
            }
          ]
        },
        // 今日文件外传趋势(折线图)
        todayOutgoingTrendData: [],
        todayOutgoingTrendOption: {
          legend: {
            data: [this.$t('report.outgoingDocument'), this.$t('report.riskOutsourcing')]
          },
          xAxis: {
            data: []
          },
          series: [
            {
              name: this.$t('report.outgoingDocument'),
              data: [],
              type: 'line'
            },
            {
              name: this.$t('report.riskOutsourcing'),
              data: [],
              type: 'line'
            }
          ]
        },
        // 外传文件途径分布(柱状图)
        wayData: [],
        wayOption: {
          yAxis: {
            type: 'category',
            data: []
          },
          xAxis: {
            name: '',
            type: 'value'
          },
          series: [{ data: [] }]
        },
        // 风险事件等级分布(饼图)
        gradeData: [],
        gradeOption: {
          series: [
            {
              radius: '50%',
              center: ['50%', '50%'],
              itemStyle: {
                borderRadius: 8
              }
            }
          ]
        },
        // 操作员外传Top10(柱状图)
        barChartUserTopData: [],
        barChartUserOption: {
          xAxis: {
            data: []
          },
          series: [
            {
              data: []
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  watch: {
    tempQuery: {
      deep: true,
      handler(newVal, oldVal) {
        this.tempQuery = newVal
      }
    }
  },
  created() {
    this.resetTemp()
    this.getConfig()
  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    if (this.isMounted === false) {
      this.startIntervalFun()
      // 系统当前时间
      this.getTimes()
    }
  },
  mounted() {
    // 挂载后
    this.isMounted = true
    this.getLayout()
    this.temp.currentTime = getCurrentTime()
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    // 系统当前时间
    this.getTimes()
    this.dataLoadPromise = Promise.all([
      this.getLargeScreenConfig()
    ]).then(() => {
      this.startIntervalFun()
      this.getTodayFileOutRecordList()
      this.getTodayFileOutCount()
      this.getTodayRiskFileOutRecord()
      // 获取右上方查询条件查出来的信息,3小时刷新一次（首次加载时，报表日期的获取可能需要一点点时长）
      setTimeout(() => {
        this.getLargeScreenData()
      }, 1000)
    })
    setTimeout(() => {
      this.isMounted = false
    }, 1000)
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(window.currentTime);
    window.currentTime = null
    this.stopIntervalFun()
    this.stopRequest()
    clearInterval(window.timerFileOutingEvent);
    window.timerFileOutingEvent = null
    clearInterval(window.timerTodayData);
    window.timerTodayData = null
    clearInterval(window.timerRiskOuting);
    window.timerRiskOuting = null
    clearInterval(window.timerSearch);
    window.timerSearch = null
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(window.currentTime);
    window.currentTime = null
    this.stopIntervalFun()
    this.stopRequest()
  },
  methods: {
    /**
     * 获取外传途径（风险外传实时预警）
     */
    getConfig() {
      getLossType().then(res => {
        this.alarmType = res.data
        // console.log('this.alarmType', JSON.parse(JSON.stringify(this.alarmType)))
      })
    },
    /**
     * 中断http请求（组件关闭或停用时，如果http接口未请求完成，页面切换时中断请求调用）
     * */
    stopRequest() {
      // 中断大屏配置接口获取
      if (this.sourceGetLargeScreenConfig) { this.sourceGetLargeScreenConfig.cancel() }
      // 中断自定义大屏配置接口获取
      if (this.sourceGetLayout) { this.sourceGetLayout.cancel() }
      // 中断实时文件外传事件接口获取
      if (this.sourceGetTodayFileOutRecord) { this.sourceGetTodayFileOutRecord.cancel() }
      // 中断右上方查询条件查出来的信息接口获取
      if (this.sourceGetFileOutPanel) { this.sourceGetFileOutPanel.cancel() }
      // 中断今日小方块数据、今日文件外传趋势
      if (this.sourceGtTodayFileOutCount) { this.sourceGtTodayFileOutCount.cancel() }
      // 中断风险外传实时预警
      if (this.sourceGetTodayRiskFileOutRecord) { this.sourceGetTodayRiskFileOutRecord.cancel() }
    },
    /**
     * 定时器启动
     * */
    startIntervalFun() {
      if (window.timerFileOutingEvent) {
        clearInterval(window.timerFileOutingEvent)
      }
      window.timerFileOutingEvent = setInterval(() => { this.getTodayFileOutRecordList() }, this.fileOutingEventTime)
      if (window.timerTodayData) {
        clearInterval(window.timerTodayData)
      }
      window.timerTodayData = setInterval(() => { this.getTodayFileOutCount() }, this.todayDataTime)
      if (window.timerRiskOuting) {
        clearInterval(window.timerRiskOuting)
      }
      window.timerRiskOuting = setInterval(() => { this.getTodayRiskFileOutRecord() }, this.riskOutingTime)
      if (window.timerSearch) {
        clearInterval(window.timerSearch)
      }
      window.timerSearch = setInterval(() => { this.getLargeScreenData() }, this.searchIntervalTime)
    },
    /**
     * 定时器停止
     * */
    stopIntervalFun() {
      clearInterval(window.timerFileOutingEvent)
      clearInterval(window.timerTodayData)
      clearInterval(window.timerRiskOuting)
      clearInterval(window.timerSearch)
      window.timerFileOutingEvent = null
      window.timerTodayData = null
      window.timerRiskOuting = null
      window.timerSearch = null
    },
    /**
     * 获取layout布局（动态显示页面布局）
     * */
    getLayout(option) {
      const userId = this.$store.getters.userId
      this.sourceGetLayout = axios.CancelToken.source()
      let cacheToken = this.sourceGetLayout.token
      if (option === 'config') {
        cacheToken = ''
      }
      getValueByCondition({ sysUserId: userId, menuCode: this.parameterOption.menuCode, tableKey: this.parameterOption.tableKey, type: 4 }, cacheToken).then(res => {
        if (res.data) {
          // 如果存在data,直接显示接口返回，否则为前端页面设置的默认值
          // console.log('软件安装与使用', JSON.parse(res.data.value))
          this.layout = JSON.parse(res.data.value)
          this.orLayout = JSON.parse(res.data.value)
        } else {
          // 初始化时先赋原来的初始值
          this.layout = JSON.parse(JSON.stringify(this.originalLayout))
          this.orLayout = JSON.parse(JSON.stringify(this.originalLayout))
        }
      })
    },
    /**
     * 打开布局配置弹框
     */
    configBtn() {
      this.getLayout()
      this.$refs.dialogLayoutConfig.show()
    },
    /**
     * 获取大屏配置中的配置信息（动态设置定时器时长）
     * */
    getLargeScreenConfig() {
      const userId = this.$store.getters.userId
      this.sourceGetLargeScreenConfig = axios.CancelToken.source()
      const cacheToken = this.sourceGetLargeScreenConfig.token
      return getValueByCondition({ sysUserId: userId, menuCode: '1001', tableKey: 'largeScreenConfig', type: 4 }, cacheToken).then(res => {
        if (res.data) {
          const response = JSON.parse(res.data.value)
          // console.log('res.data大屏配置', response)
          if (response.outgoingDocumentDimBaseType && response.outgoingDocumentDimValue !== '') {
            this.dataTemp.encOrDecDimBaseType = response.outgoingDocumentDimBaseType
            this.dataTemp.encOrDecDimValue = response.outgoingDocumentDimValue
            this.dataTemp.dateCycleLargeScreen = response.outgoingDocumentDimDataCycle
            // 切换间隔时间，单位毫秒
            this.fileOutingEventTime = getSetIntervalTime(response.outgoingDocumentEventLogValue, response.outgoingDocumentEventLogSelect)
            this.todayDataTime = getSetIntervalTime(response.outgoingDocumentTodayDataValue, response.outgoingDocumentTodayDataSelect)
            this.riskOutingTime = getSetIntervalTime(response.outgoingDocumentRiskLogValue, response.outgoingDocumentRiskLogSelect)
            this.searchIntervalTime = getSetIntervalTime(response.outgoingDocumentQueryValue, response.outgoingDocumentQuerySelect)
          }
        }
      })
    },
    /**
     * 获取：实时文件外传事件
     * */
    getTodayFileOutRecordList() {
      this.sourceGetTodayFileOutRecord = axios.CancelToken.source()
      const cacheToken = this.sourceGetTodayFileOutRecord.token
      displayPanelGetTodayFileOutRecord(cacheToken).then(res => {
        // 最后更新时间
        this.temp.lastUpdateTime = getCurrentTime()
        const response = res.data
        let datas = []
        const oldArray = this.temp.eventRowDatas
        // console.log('res：实时文件外传事件', JSON.parse(JSON.stringify(res)))
        if (response.length > 0) {
          response.forEach(item => {
            item.logSource = this.logSourceMapping[item.logSource] || item.logSource
          })
          datas = response
        }
        const newArray = datas
        const markNewObjects = (newArray, oldArray) => {
          return newArray.map(newObj => {
            // 数据从无到有的时候不变红色，只有新旧数据都存在，且存在新的数据时，才变红色
            let oldObj = {}
            if (oldArray.length > 0) {
              oldObj = oldArray.find(oldObj => (oldObj.terminalName === newObj.terminalName && oldObj.termGroupName === newObj.termGroupName && oldObj.fileName === newObj.fileName && oldObj.logSource === newObj.logSource && oldObj.createTime === newObj.createTime))
            }
            return oldObj ? Object.assign({}, newObj, { isNew: false }) : Object.assign({}, newObj, { isNew: true });
          });
        };
        this.temp.eventRowDatas = markNewObjects(newArray, oldArray);
        // console.log('this.temp.eventRowDatas', JSON.parse(JSON.stringify(this.temp.eventRowDatas)))
      })
    },
    /**
     * 获取右上方查询条件查出来的信息
     * */
    getLargeScreenData() {
      this.$nextTick(() => {
        // console.log('this.tempQuery文件外传', JSON.parse(JSON.stringify(this.tempQuery)))
        // 根据是否首次加载，调用有日志和没日志的接口
        let apiFun = displayPanelGetFileOutPanelNoOplog
        if (this.isMounted && this.isFirst) {
          apiFun = displayPanelGetFileOutPanel
        }
        this.sourceGetFileOutPanel = axios.CancelToken.source()
        const cacheToken = this.sourceGetFileOutPanel.token
        apiFun(this.tempQuery, cacheToken).then(res => {
          // console.log('res：获取右上方查询条件查出来的信息', JSON.parse(JSON.stringify(res)))
          // 最后更新时间
          this.temp.lastUpdateTime = getCurrentTime()
          if (res.data) {
            const responseItem = res.data.itemValueMap
            const responseChart = res.data.chartDataObjMap
            // 获取中间数据
            this.temp.middleMsg[0].currentNum = responseItem.fileOutSum
            this.temp.middleMsg[1].currentNum = responseItem.riskFileOutSum
            // 外传文件分类分布（饼图）
            if (JSON.stringify(responseChart.fileTypePieCharData) !== '{}') {
              this.temp.fileCategoryData = responseChart.fileTypePieCharData.chartData
            }
            // 外传文件途径分布（柱状图）
            if (JSON.stringify(responseChart.outSendWayPie) !== '{}') {
              this.temp.wayData = responseChart.outSendWayPie.seriesData
              this.temp.wayOption.yAxis.data = responseChart.outSendWayPie.xaxisData.map(key => this.logSourceMapping.hasOwnProperty(key) ? this.logSourceMapping[key] : key)
              this.temp.wayOption.series[0].data = responseChart.outSendWayPie.seriesData
              this.temp.wayOption.yAxis.axisLabel = {
                formatter: function(value) {
                  // 如果文本长度超过8个字，则返回前八个字加上省略号
                  if (value.length > 8) {
                    return value.slice(0, 8) + '...';
                  }
                  // 如果文本长度不超过8个字，则直接返回原文本
                  return value;
                }
              }
            }
            // 文件外传趋势（折线图）
            if (JSON.stringify(responseChart.dateLine) !== '{}') {
              this.temp.historicalOutgoingTrendOption.xAxis.data = responseChart.dateLine.xaxisData
              this.temp.historicalOutgoingTrendOption.series[0].data = responseChart.dateLine.seriesData[0]
              this.temp.historicalOutgoingTrendOption.series[1].data = responseChart.dateLine.seriesData[1]
            }
            // 操作员外传Top10（柱状图）
            if (JSON.stringify(responseChart.userChartData) !== '{}') {
              this.temp.barChartUserOption.xAxis.data = this.getName(responseChart.userChartData.xaxisData)
              this.temp.barChartUserOption.series[0].data = responseChart.userChartData.seriesData
            }
            // 风险事件等级分布（饼图）
            if (JSON.stringify(responseChart.severityTypePie) !== '{}') {
              const responseSeverityTypePie = responseChart.severityTypePie.chartData
              if (responseSeverityTypePie.length > 0) {
                responseSeverityTypePie.forEach(item => {
                  item.name = this.severityMapping[item.name] || item.name
                })
                this.temp.gradeData = responseSeverityTypePie
              }
            }
          } else {
            // 没有返回时，需要重置，但是今日的不要重置，所以重新调用今日一下接口
            this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
            this.getTodayFileOutCount()
            this.getTodayRiskFileOutRecord()
            this.getTodayFileOutRecordList()
          }
        })
      })
    },
    /**
     * 获取：今日小方块数据、今日文件外传趋势
     * */
    getTodayFileOutCount() {
      this.sourceGtTodayFileOutCount = axios.CancelToken.source()
      const cacheToken = this.sourceGtTodayFileOutCount.token
      displayPanelGetTodayFileOutCount(cacheToken).then(res => {
        // 最后更新时间
        this.temp.lastUpdateTime = getCurrentTime()
        if (res.data) {
          const responseItem = res.data.itemValueMap
          const responseChart = res.data.chartDataObjMap
          // 获取中间数据
          this.temp.middleMsg[2].currentNum = responseItem.allCount
          this.temp.middleMsg[3].currentNum = responseItem.riskCount
          this.temp.middleMsg[4].currentNum = responseItem.userCount
          this.temp.middleMsg[5].currentNum = responseItem.termCount
          // 今日文件外传趋势（折线图）
          if (JSON.stringify(responseChart.todayTimeChart) !== '{}') {
            const xData = responseChart.todayTimeChart.xaxisData
            this.temp.todayOutgoingTrendOption.xAxis.data = xData.map(item => item + ':00')
            this.temp.todayOutgoingTrendOption.series[0].data = responseChart.todayTimeChart.seriesData[0]
            this.temp.todayOutgoingTrendOption.series[1].data = responseChart.todayTimeChart.seriesData[1]
          }
        } else {
          this.temp.middleMsg[2].currentNum = 0
          this.temp.middleMsg[3].currentNum = 0
          this.temp.middleMsg[4].currentNum = 0
          this.temp.middleMsg[5].currentNum = 0
          this.temp.todayOutgoingTrendOption.xAxis.data = []
          this.temp.todayOutgoingTrendOption.series[0].data = []
          this.temp.todayOutgoingTrendOption.series[1].data = []
        }
      })
    },
    /**
     * 获取：风险外传实时预警
     * */
    getTodayRiskFileOutRecord() {
      this.sourceGetTodayRiskFileOutRecord = axios.CancelToken.source()
      const cacheToken = this.sourceGetTodayRiskFileOutRecord.token
      displayPanelGetTodayRiskFileOutRecord(cacheToken).then(res => {
        // 最后更新时间
        this.temp.lastUpdateTime = getCurrentTime()
        const response = res.data
        // console.log('res：风险外传实时预警', JSON.parse(JSON.stringify(response)))
        let datas = []
        const oldArray = this.temp.riskRowDatas
        if (response.length > 0) {
          response.forEach((item, index) => {
            item.severity = this.severityMapping[item.severity] || item.severity
            this.alarmType.forEach((it, index) => {
              if (item.lossType === it.alarmType) {
                item.lossType = it.alarmDesc
              }
            })
          })
          datas = response
        }
        const newArray = datas
        const markNewObjects = (newArray, oldArray) => {
          return newArray.map(newObj => {
            // 数据从无到有的时候不变红色，只有新旧数据都存在，且存在新的数据时，才变红色
            let oldObj = {}
            if (oldArray.length > 0) {
              oldObj = oldArray.find(oldObj => (oldObj.terminalName === newObj.terminalName && oldObj.termGroupName === newObj.termGroupName && oldObj.fileName === newObj.fileName && oldObj.severity === newObj.severity && oldObj.createTime === newObj.createTime))
            }
            return oldObj ? Object.assign({}, newObj, { isNew: false }) : Object.assign({}, newObj, { isNew: true });
          });
        };
        this.temp.riskRowDatas = markNewObjects(newArray, oldArray);
      })
    },
    /**
     * 获取名称，截取掉名称中的id
     * @param nameArray 接口返回的名称数组
     * 截取第一个_后面的内容为名称
     * */
    getName(nameArray) {
      return nameArray.map(str => str.split('_').slice(1).join('_'));
    },

    /**
     * 获取接口参数（子组件点击查询时获取）
     * @param data 参数
     * */
    thisQuery(data) {
      this.tempQuery = data
    },
    /**
     * 系统当前时间，实时获取，实时刷新
     * */
    getTimes() {
      if (this.currentTime) {
        clearInterval(this.currentTime)
      }
      this.currentTime = setInterval(this.getSystemTime, 1000)
    },
    /**
     * 系统当前时间，赋值
     * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      // 报表日期默认值设置
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      this.currentTime = year.toString() + month
      this.dataTemp.encOrDecDimValue = this.currentTime
    },
    /**
     * 判断是否全屏（监听全屏）
     * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
     * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
     * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight = '23.5vh'
        this.middleOutHeight = '8.5vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight = '19vh'
        this.middleOutHeight = '7.1vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>

<style lang='scss' scoped>
.activeClassRed{
  color: #ff0000;
}
//中间特殊的小块
.large-screen-middle-out{
  width: 49%;
  float: left;
  div{
    width: 90%;
    margin: auto;
    font-size: 15px;
    height: 35px;
    line-height: 35px;
  }
  .large-screen-middle-num{
    font-size: 22px;
  }
  .large-screen-middle-name{
    font-size: 14px;
    height: 25px;
    line-height: 25px;
  }
}
.activeClassHeight{
  color: #ff0000;
}
</style>
