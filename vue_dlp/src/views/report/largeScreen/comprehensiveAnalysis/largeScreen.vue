<template>
  <div :style="{ height: appContainerHeight}" class="app-container largeScreen-report">
    <div class="table-container">
      <div class="large-screen-head">
        <div class="large-screen-head-title">综合分析数据</div>
        <div class="large-screen-head-time">
          <div>最后更新时间：{{ temp.lastUpdateTime }}</div>
          <div>系统当前时间：{{ temp.currentTime }}</div>
        </div>
        <div class="large-screen-head-search">
          <query/>
        </div>
      </div>
      <div class="large-screen-body">
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">文件打印Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.print10UserData"
                  :chart-option="temp.print10UserOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-name">{{ item.currentName }}</div>
                <div class="large-screen-middle-num">{{ item.currentNum }}</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">网页浏览时长Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <div class="div_risk_ranking_item" style="color: orange">
                  <span>操作员</span>
                  <span>操作员组</span>
                  <span>时间</span>
                </div>
                <div :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.timeUserDatas" :class-option="classOption">
                    <div
                      v-for="(item, index) in temp.timeUserDatas"
                      :key="index"
                      class="div_risk_ranking_item pointer"
                    >
                      <span :title="item.user">{{ item.user }}</span>
                      <span :title="item.userGroup">{{ item.userGroup }}</span>
                      <span :title="item.time">{{ item.time }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">违规泄露方式</div>
                <div class="large-screen-search">
                  <el-select v-model="query.divulgeMode" :popper-append-to-body="false" placeholder="请选择">
                    <el-option
                      v-for="item in statisticalTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.divulgeModeData"
                  :chart-option="temp.divulgeModeOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">违规严重程度</div>
                <div class="large-screen-search">
                  <el-select v-model="query.severity" :popper-append-to-body="false" placeholder="请选择">
                    <el-option
                      v-for="item in statisticalTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.severityData"
                  :chart-option="temp.severityOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">文件外发Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <div class="div_risk_ranking_item" style="color: orange">
                  <span>操作员</span>
                  <span>操作员组</span>
                  <span>次数</span>
                </div>
                <div :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.outgoingDocumentDatas" :class-option="classOption">
                    <div
                      v-for="(item, index) in temp.outgoingDocumentDatas"
                      :key="index"
                      class="div_risk_ranking_item pointer"
                    >
                      <span :title="item.user">{{ item.user }}</span>
                      <span :title="item.userGroup">{{ item.userGroup }}</span>
                      <span :title="item.value">{{ item.value }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">风险行为统计</div>
                <div class="large-screen-search">
                  <el-select v-model="query.riskBehavior" :popper-append-to-body="false" placeholder="请选择">
                    <el-option
                      v-for="item in statisticalTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.riskBehaviorData"
                  :chart-option="temp.riskBehaviorOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">近7日数据变化趋势</div>
              </div>
              <div class="large-screen-chart-div">
                <line-chart
                  :chart-data="temp.loginTimesData"
                  :chart-option="temp.loginTimesOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">应用程序运行时长Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.applicationDurationData"
                  :chart-option="temp.applicationDurationOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import Query from '@/views/report/largeScreen/common/query'
import LineChart from '@/views/report/largeScreen/common/LineChart'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import PieChart from '@/views/report/largeScreen/common/PieChart'
import { getCurrentTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
import { getDataCollectTrend } from '@/api/report/baseReport/dataTansaction';
export default {
  name: 'LargeScreen',
  components: { Query, LineChart, BarChart, PieChart, vueSeamlessScroll },
  props: {
  },
  data() {
    return {
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight: '19vh',
      middleOutHeight: '7.1vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      query: {
        divulgeMode: 0,
        severity: 0,
        riskBehavior: 0
      },
      statisticalTypeOptions: [{ value: 0, label: '按操作员统计' }, { value: 1, label: '按终端统计' }],
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: '',
        currentTime: '',
        middleMsg: [
          { currentName: '加密数', currentNum: '89000' },
          { currentName: '解密数', currentNum: '20GB' },
          { currentName: '打印份数', currentNum: '3123' },
          { currentName: '文件外发数', currentNum: '423' },
          { currentName: '应用程序运行时长', currentNum: '1小时20分' },
          { currentName: '网页浏览时长', currentNum: '2小时3分2秒' }
        ],
        // 文件打印Top10对象(饼图)
        print10UserData: [
          { value: 1048, name: '小弟' },
          { value: 735, name: '小明' },
          { value: 45, name: '小瓦' },
          { value: 212, name: '小强' },
          { value: 57, name: '小王' },
          { value: 145, name: '小二' },
          { value: 542, name: '小柔' },
          { value: 217, name: '小天' },
          { value: 12, name: '小妖' },
          { value: 580, name: '小平' }
        ],
        print10UserOption: {
          series: [
            {
              radius: ['30%', '50%']
            }
          ]
        },
        // 近7日数据变化趋势(折线图)
        loginTimesData: [],
        loginTimesOption: {
          xAxis: {
            data: []
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        },
        // 应用程序运行时长Top10对象(柱状图)
        applicationDurationData: [],
        applicationDurationOption: {
          xAxis: {
            data: ['小猪', '小新', '小丑', '小贝', '小诺', '小明', '小艾', '小弟', '小幅', '小鬼']
          },
          series: [
            {
              data: [120, 200, 150, 80, 100, 120, 200, 150, 80, 100]
            }
          ]
        },
        // 违规泄露方式(柱状图)
        divulgeModeData: [120, 200, 150, 80],
        divulgeModeOption: {
          xAxis: {
            data: ['文件打印内容', '即时通讯发送文件', 'FTP上传文件', '邮件传输文本内容'],
            axisLabel: {
              interval: 0,
              // 倾斜的程度
              rotate: 25
            }
          },
          series: [
            {
              data: [120, 200, 150, 80]
            }
          ]
        },
        // 违规严重程度(饼图)
        severityData: [
          { value: 1048, name: '轻微' },
          { value: 735, name: '一般' },
          { value: 735, name: '严重' },
          { value: 580, name: '非常严重' }
        ],
        severityOption: {
          series: [
            {
              radius: '50%',
              center: ['50%', '50%'],
              itemStyle: {
                borderRadius: 8
              }
            }
          ]
        },
        // 风险行为统计(柱状图)
        riskBehaviorData: [8, 45, 66, 95],
        riskBehaviorOption: {
          yAxis: {
            type: 'category',
            data: ['全盘扫描敏感文件数', '文件外发审批数', '零星检测-数据泄露违规数', '常规检查-数据泄露违规数']
          },
          xAxis: {
            type: 'value'
          },
          series: [
            {
              data: [8, 45, 66, 95]
            }
          ]
        },
        // 网页浏览时长Top10对象
        timeUserDatas: [
          { id: 1, user: '小新', userGroup: '研发一部', time: '125小时25分23秒' },
          { id: 2, user: '小黄', userGroup: '研发二部', time: '121小时12分45秒' },
          { id: 3, user: '小黑', userGroup: '研发三部', time: '98小时45分35秒' },
          { id: 4, user: '小妖', userGroup: '研发四部', time: '67小时42分36秒' },
          { id: 5, user: '小红', userGroup: '研发四部', time: '67小时42分36秒' }
        ],
        // 文件外发Top10对象
        outgoingDocumentDatas: [
          { id: 1, user: '小新', userGroup: '研发一部', value: '452' },
          { id: 2, user: '小黄', userGroup: '研发二部', value: '345' },
          { id: 3, user: '小黑', userGroup: '研发三部', value: '254' },
          { id: 4, user: '小妖', userGroup: '研发四部', value: '125' },
          { id: 5, user: '小花', userGroup: '研发四部', value: '125' }
        ]
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
    this.getReportInfo()
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
    this.temp.currentTime = getCurrentTime()
    this.get7DayDataChange()
  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
  },
  mounted() {
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  methods: {
    /**
     * 获取近7日数据变化趋势
     * */
    get7DayDataChange() {
      // 获取今天的日期
      const today = new Date();
      const todayYear = today.getFullYear();
      const todayMonth = (today.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的
      const todayDay = today.getDate().toString().padStart(2, '0');
      // 获取今天之前6天的日期
      const sixDaysAgo = new Date();
      sixDaysAgo.setDate(sixDaysAgo.getDate() - 6);
      const sixDaysYear = sixDaysAgo.getFullYear();
      const sixDaysMonth = (sixDaysAgo.getMonth() + 1).toString().padStart(2, '0');
      const sixDaysDay = sixDaysAgo.getDate().toString().padStart(2, '0');
      this.query.startDate = `${sixDaysYear}-${sixDaysMonth}-${sixDaysDay}`
      this.query.endDate = `${todayYear}-${todayMonth}-${todayDay}`
      getDataCollectTrend(this.query).then(res => {
        this.temp.lastUpdateTime = getCurrentTime()
        // console.log('近7日', JSON.parse(JSON.stringify(res.data)))
        this.temp.loginTimesOption.xAxis.data = res.data.xaxisData
        this.temp.loginTimesOption.series[0].data = res.data.seriesData
      }).catch(() => {})
    },
    /**
     * 系统当前时间，实时获取，实时刷新
     * */
    getTimes() {
      setInterval(this.getSystemTime, 1000)
    },
    /**
     * 系统当前时间，赋值
     * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取页面数据
     * */
    getReportInfo() {
      // 最后更新于
      this.temp.lastUpdateTime = getCurrentTime()
    },
    /**
     * 判断是否全屏（监听全屏）
     * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
     * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
     * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      // console.log('this.isFullScreen', this.isFullScreen)
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight = '23.5vh'
        this.middleOutHeight = '8.5vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight = '19vh'
        this.middleOutHeight = '7.1vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  //中间特殊的小块
  .large-screen-middle-out{
    width: 49%;
    height: 7.3vh;
    float: left;
    div{
      width: 90%;
      margin: auto;
      font-size: 15px;
      height: 35px;
      line-height: 35px;
    }
    .large-screen-middle-num{
      font-size: 22px;
    }
    .large-screen-middle-name{
      font-size: 14px;
      height: 25px;
      line-height: 25px;
    }
  }
  .pointer{
    cursor: pointer;
  }
</style>
