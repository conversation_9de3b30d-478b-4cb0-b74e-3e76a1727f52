<!--
  大屏轮播，大屏配置，控制台右上角电脑图表下拉列表展示
-->
<template>
  <div v-if="isShow">
    <el-dropdown trigger="hover" placement="bottom">
      <span style="color: #eee;font-size: 20px;">
        <svg-icon icon-class="alarm-07" style="width: 0.8em; cursor: pointer"/>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item>
          <!--<router-link to="/report/largeScreen/carousel">{{ $t('pages.largeDataScreen') }}</router-link>-->
          <router-link to="/report/largeScreen/carousel">{{ $t('pages.bigScreenWheelCasting') }}</router-link>
        </el-dropdown-item>
        <el-dropdown-item>
          <span style="display:block;" @click="configBtn">{{ $t('pages.largeScreenConfig') }}</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!--    大屏配置弹框-->
    <config ref="config"/>
  </div>
</template>

<script>
import Config from '@/views/report/largeScreen/carouselConfig/config'
import { mapGetters } from 'vuex';
export default {
  name: 'CarouseConfig',
  components: { Config },
  data() {
    return {
      isShow: true
    }
  },
  computed: {
    ...mapGetters([
      'routesMap'
    ])
  },
  watch: {

  },
  mounted() {

  },
  created() {
    const getRouter = JSON.parse(JSON.stringify(this.routesMap))
    const objectsArray = getRouter.filter(item => item.meta.hasOwnProperty('customPath'))
    // console.log('objectsArray', objectsArray)
    if (objectsArray.length > 0) {
      this.isShow = true
    } else {
      this.isShow = false
    }
  },
  beforeDestroy() {

  },
  destroyed() {

  },
  methods: {
    /**
     * 大屏配置点击，显示弹框
     */
    configBtn() {
      this.$refs.config.show()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
