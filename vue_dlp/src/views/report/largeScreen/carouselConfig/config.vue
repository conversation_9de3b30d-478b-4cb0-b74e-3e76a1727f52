<!--大屏配置弹框-->
<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal="false"
      :title="this.$t('pages.largeScreenConfig')"
      :visible.sync="visible"
      width="800px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="20px" style="width: 750px;">
        <div class="block">
          <el-divider content-position="left">{{ $t('pages.foundationForm_Msg68') }}</el-divider>
          <FormItem>
            <span>
              {{ $t('pages.carouselMenu') }}
              <el-tooltip effect="dark" placement="top-start">
                <div slot="content">
                  (1){{ $t('pages.foundationForm_Msg69') }}<br>
                  (2){{ $t('pages.foundationForm_Msg70') }}<br>
                  (3){{ $t('pages.foundationForm_Msg71') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
            </span>
            <drag-select v-model="temp.carouselMenu" style="width: 80%" filterable multiple :placeholder="this.$t('pages.foundationForm_Msg72')">
              <el-option
                v-for="(item, index) in carouselMenuOptionPermission"
                :key="index"
                :label="$t('route.' + item.meta.title)"
                :value="item.name"
              >
              </el-option>
            </drag-select>
          </FormItem>
          <FormItem label="" prop="carouselMenuValue">
            <span>{{ $t('pages.menuInterval') }}</span>
            <el-input-number v-model="temp.carouselMenuValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
            <el-select v-model="temp.carouselMenuSelect" style="width: 80px" :placeholder="this.$t('text.select')">
              <el-option
                v-for="item in timeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span>{{ $t('pages.castOnce') }}</span>
          </FormItem>
          <template v-if="hasPermission('IA7')">
            <el-divider content-position="left">{{ $t('pages.encDecryptFileConfig') }}</el-divider>
            <FormItem label="" prop="encOrDecChartSwitchValue">
              <span>
                {{ $t('pages.largeScreenConfig1') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.largeScreenConfig2') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.encOrDecChartSwitchValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.encOrDecChartSwitchSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig4') }}</span>
            </FormItem>
            <FormItem label="" prop="encOrDecTodayLogValue">
              <span>
                {{ $t('pages.largeScreenConfig5') }}</span>
              <el-input-number v-model="temp.encOrDecTodayLogValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.encOrDecTodayLogSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="encOrDecTodayValue">
              <span>
                {{ $t('pages.largeScreenConfig7') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.largeScreenConfig8') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}</span>
              <el-input-number v-model="temp.encOrDecTodayValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.encOrDecTodaySelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="encOrDecDateQueryValue">
              <span>{{ $t('pages.largeScreenConfig9') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.largeScreenConfig10') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}</span>
              <el-input-number v-model="temp.encOrDecDateQueryValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.encOrDecDateQuerySelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="">
              <!--报表日期-->
              <query v-if="visible" ref="queryData" :is-large-screen-config="true" :data-temp="dataTemp"/>
            </FormItem>
          </template>
          <template v-if="hasPermission('IL3')">
            <el-divider content-position="left">{{ $t('pages.largeScreenConfig11') }}</el-divider>
            <FormItem label="" prop="todayRiskValue">
              <span>
                {{ $t('pages.largeScreenConfig12') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.largeScreenConfig13') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.todayRiskValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.todayRiskSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="todayCountByObject">
              <span>
                {{ $t('pages.countByObject') }}
              </span>
              <el-select v-model="temp.todayCountByObject" is-filter :placeholder="$t('text.select')" style="width: 130px; margin-right: 10px;">
                <el-option
                  v-for="(item, index) in typeOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </FormItem>
          </template>
          <template v-if="hasPermission('S13')">
            <el-divider content-position="left">{{ $t('report.largeScreenConfigMsg1') }}</el-divider>
            <FormItem label="" prop="softwareInstallUseLogValue">
              <span>
                {{ $t('report.largeScreenConfigMsg2') }}，{{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.softwareInstallUseLogValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.softwareInstallUseLogSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="softwareInstallUseQueryValue">
              <span>{{ $t('pages.largeScreenConfig9') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('report.largeScreenConfigMsg3') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}</span>
              <el-input-number v-model="temp.softwareInstallUseQueryValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.softwareInstallUseQuerySelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="">
              <!--报表日期-->
              <query v-if="visible" ref="queryDataSoftwareInstallUse" :is-large-screen-config="true" :data-temp="dataTempSoftwareInstallUse"/>
            </FormItem>
          </template>
          <template v-if="hasPermission('S12')">
            <el-divider content-position="left">{{ $t('pages.largeScreenConfig14') }}</el-divider>
            <FormItem label="" prop="operationMaintenanceChartSwitchValue">
              <span>
                {{ $t('pages.largeScreenConfig1') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.largeScreenConfig15') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.operationMaintenanceChartSwitchValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.operationMaintenanceChartSwitchSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig4') }}</span>
            </FormItem>
            <FormItem label="" prop="operationMaintenanceCpuValue">
              <span>
                {{ $t('pages.largeScreenConfig16') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.largeScreenConfig17') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.operationMaintenanceCpuValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.operationMaintenanceCpuSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="operationMaintenanceCollectValue">
              <span>
                {{ $t('pages.largeScreenConfig18') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.largeScreenConfig19') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.operationMaintenanceCollectValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.operationMaintenanceCollectSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="operationMaintenanceMonitoringLogValue">
              <span>{{ $t('pages.largeScreenConfig20') }}</span>
              <el-input-number v-model="temp.operationMaintenanceMonitoringLogValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.operationMaintenanceMonitoringLogSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="operationMaintenanceTerminalUserValue">
              <span>
                {{ $t('pages.largeScreenConfig21') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.largeScreenConfig22') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.operationMaintenanceTerminalUserValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.operationMaintenanceTerminalUserSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="operationMaintenanceLast7DaysValue">
              <span>{{ $t('report.largeScreenConfigMsg4') }}，{{ $t('pages.largeScreenConfig3') }}</span>
              <el-input-number v-model="temp.operationMaintenanceLast7DaysValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.operationMaintenanceLast7DaysSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="operationMaintenanceServerValue">
              <span>{{ $t('pages.largeScreenConfig24') }}</span>
              <el-input-number v-model="temp.operationMaintenanceServerValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.operationMaintenanceServerSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
          </template>

          <template v-if="hasPermission('S15')">
            <el-divider content-position="left">{{ $t('report.largeScreenConfigMsg5') }}</el-divider>
            <FormItem label="" prop="outgoingDocumentEventLogValue">
              <span>
                {{ $t('report.realTimeOutgoingFileTransferEvent') }} ，{{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.outgoingDocumentEventLogValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.outgoingDocumentEventLogSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="outgoingDocumentTodayDataValue">
              <span>
                {{ $t('report.largeScreenConfigMsg6') }}，{{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.outgoingDocumentTodayDataValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.outgoingDocumentTodayDataSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="outgoingDocumentRiskLogValue">
              <span>
                {{ $t('report.realTimeEarlyWarningOfExternalRisks') }}，{{ $t('pages.largeScreenConfig3') }}
              </span>
              <el-input-number v-model="temp.outgoingDocumentRiskLogValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.outgoingDocumentRiskLogSelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="outgoingDocumentQueryValue">
              <span>{{ $t('pages.largeScreenConfig9') }}
                <el-tooltip effect="dark" placement="top-start">
                  <div slot="content">
                    {{ $t('pages.largeScreenConfig40') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
                {{ $t('pages.largeScreenConfig3') }}</span>
              <el-input-number v-model="temp.outgoingDocumentQueryValue" :min="1" :controls="false" :precision="0" style="width:80px"/>
              <el-select v-model="temp.outgoingDocumentQuerySelect" style="width: 80px" :placeholder="$t('text.select')">
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <span>{{ $t('pages.largeScreenConfig6') }}</span>
            </FormItem>
            <FormItem label="" prop="">
              <!--报表日期-->
              <query v-if="visible" ref="queryDataOutgoingDocument" :is-large-screen-config="true" :data-temp="dataTempOutgoingDocument"/>
            </FormItem>
          </template>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <span style="color: #ff0000;font-size: 12px;float: left;line-height: 30px">{{ $t('pages.largeScreenConfig25') }}</span>
        <el-button :loading="submitting" type="primary" @click="saveBtn">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DragSelect from '@/components/dragSelect'
import Query from '@/views/report/largeScreen/common/query'
import { insertPersonalization, getValueByCondition } from '@/api/user';
import { mapGetters } from 'vuex';

export default {
  name: 'Config',
  components: { DragSelect, Query },
  data() {
    return {
      visible: false,
      submitting: false,
      currentTime: '',
      // 加解密报表日期
      dataTemp: {
        encOrDecDimBaseType: 1,
        encOrDecDimValue: '',
        dateCycleLargeScreen: 0
      },
      // 软件安装与使用报表日期
      dataTempSoftwareInstallUse: {
        encOrDecDimBaseType: 1,
        encOrDecDimValue: '',
        dateCycleLargeScreen: 0
      },
      // 文件外发报表日期
      dataTempOutgoingDocument: {
        encOrDecDimBaseType: 1,
        encOrDecDimValue: '',
        dateCycleLargeScreen: 0
      },
      temp: {},
      defaultTemp: {
        // 数据大屏轮播配置
        carouselMenu: [],
        carouselMenuValue: 5,
        carouselMenuSelect: 1,
        // 加解密文件操作数据配置
        encOrDecChartSwitchValue: 8,
        encOrDecChartSwitchSelect: 0,
        encOrDecTodayLogValue: 1,
        encOrDecTodayLogSelect: 1,
        encOrDecTodayValue: 1,
        encOrDecTodaySelect: 1,
        encOrDecDateQueryValue: 3,
        encOrDecDateQuerySelect: 2,
        encOrDecDimBaseType: 1,  // 报表日期
        encOrDecDimValue: '',
        encOrDecDimDataCycle: 0,
        // 今日风险数据配置
        todayRiskValue: 1,
        todayRiskSelect: 1,
        todayCountByObject: 2,
        // 软件安装与使用数据配置
        softwareInstallUseLogValue: 1,
        softwareInstallUseLogSelect: 1,
        softwareInstallUseQueryValue: 4,
        softwareInstallUseQuerySelect: 2,
        softwareInstallDimBaseType: 1, // 报表日期
        softwareInstallDimValue: '',
        softwareInstallDimDataCycle: 0,
        // 运维数据配置
        operationMaintenanceChartSwitchValue: 8,
        operationMaintenanceChartSwitchSelect: 0,
        operationMaintenanceCpuValue: 2,
        operationMaintenanceCpuSelect: 0,
        operationMaintenanceCollectValue: 4,
        operationMaintenanceCollectSelect: 2,
        operationMaintenanceMonitoringLogValue: 10,
        operationMaintenanceMonitoringLogSelect: 1,
        operationMaintenanceTerminalUserValue: 4,
        operationMaintenanceTerminalUserSelect: 2,
        operationMaintenanceLast7DaysValue: 4,
        operationMaintenanceLast7DaysSelect: 2,
        operationMaintenanceServerValue: 60,
        operationMaintenanceServerSelect: 0,
        // 文件外发数据配置
        outgoingDocumentEventLogValue: 1,
        outgoingDocumentEventLogSelect: 1,
        outgoingDocumentTodayDataValue: 1,
        outgoingDocumentTodayDataSelect: 1,
        outgoingDocumentRiskLogValue: 1,
        outgoingDocumentRiskLogSelect: 1,
        outgoingDocumentQueryValue: 4,
        outgoingDocumentQuerySelect: 2,
        outgoingDocumentDimBaseType: 1, // 报表日期
        outgoingDocumentDimValue: '',
        outgoingDocumentDimDataCycle: 0
      },
      rules: {
        carouselMenuValue: [
          { required: true, message: this.$t('table.menu') + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.carouselMenuValidator, trigger: 'blur' }
        ],
        encOrDecChartSwitchValue: [
          { required: true, message: this.$t('pages.largeScreenConfig1') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.encOrDecChartSwitchValidator, trigger: 'blur' }
        ],
        encOrDecTodayLogValue: [
          { required: true, message: this.$t('pages.largeScreenConfig27') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.encOrDecTodayLogValidator, trigger: 'blur' }
        ],
        encOrDecTodayValue: [
          { required: true, message: this.$t('pages.largeScreenConfig7') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.encOrDecTodayValidator, trigger: 'blur' }
        ],
        encOrDecDateQueryValue: [
          { required: true, message: this.$t('pages.largeScreenConfig9') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.encOrDecDateQueryValidator, trigger: 'blur' }
        ],
        todayRiskValue: [
          { required: true, message: this.$t('pages.largeScreenConfig12') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.todayRiskValidator, trigger: 'blur' }
        ],
        softwareInstallUseLogValue: [
          { required: true, message: this.$t('report.largeScreenConfigMsg2') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.softwareInstallUseLogValidator, trigger: 'blur' }
        ],
        softwareInstallUseQueryValue: [
          { required: true, message: this.$t('pages.largeScreenConfig9') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.softwareInstallUseQueryValidator, trigger: 'blur' }
        ],
        operationMaintenanceChartSwitchValue: [
          { required: true, message: this.$t('pages.largeScreenConfig1') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.operationMaintenanceChartSwitchValidator, trigger: 'blur' }
        ],
        operationMaintenanceCpuValue: [
          { required: true, message: this.$t('pages.largeScreenConfig16') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.operationMaintenanceCpuValidator, trigger: 'blur' }
        ],
        operationMaintenanceCollectValue: [
          { required: true, message: this.$t('pages.largeScreenConfig18') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.operationMaintenanceCollectValidator, trigger: 'blur' }
        ],
        operationMaintenanceMonitoringLogValue: [
          { required: true, message: this.$t('pages.keyMonitoringLog') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.operationMaintenanceMonitoringLogValidator, trigger: 'blur' }
        ],
        operationMaintenanceTerminalUserValue: [
          { required: true, message: this.$t('pages.largeScreenConfig21') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.operationMaintenanceTerminalUserValidator, trigger: 'blur' }
        ],
        operationMaintenanceLast7DaysValue: [
          { required: true, message: this.$t('report.largeScreenConfigMsg4') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.operationMaintenanceLast7DaysValidator, trigger: 'blur' }
        ],
        operationMaintenanceServerValue: [
          { required: true, message: this.$t('pages.serverInfo') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.operationMaintenanceServerValidator, trigger: 'blur' }
        ],
        outgoingDocumentEventLogValue: [
          { required: true, message: this.$t('report.realTimeOutgoingFileTransferEvent') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.outgoingDocumentEventLogValidator, trigger: 'blur' }
        ],
        outgoingDocumentTodayDataValue: [
          { required: true, message: this.$t('report.largeScreenConfigMsg6') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.outgoingDocumentTodayDataValidator, trigger: 'blur' }
        ],
        outgoingDocumentRiskLogValue: [
          { required: true, message: this.$t('report.realTimeEarlyWarningOfExternalRisks') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.outgoingDocumentRiskLogValidator, trigger: 'blur' }
        ],
        outgoingDocumentQueryValue: [
          { required: true, message: this.$t('pages.largeScreenConfig9') + '，' + this.$t('pages.largeScreenConfig26'), trigger: 'blur' },
          { validator: this.outgoingDocumentQueryValidator, trigger: 'blur' }
        ]
      },
      // 统计类型
      typeOptions: [{ value: 1, label: this.$t('pages.conutType1') }, { value: 2, label: this.$t('pages.conutType2') }],
      // 轮播菜单选项
      carouselMenuOption: [],
      // 通过carouselMenuOption中的code过滤出有权限的轮播组件，存放在componentsPermissions中
      carouselMenuOptionPermission: [],
      timeOptions: [
        { value: 0, label: this.$t('text.second') },
        { value: 1, label: this.$t('text.minute') },
        { value: 2, label: this.$t('text.hour') }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'routesMap'
    ])
  },
  created() {
    this.resetTemp()
    this.filterMenuPermissions()
  },
  methods: {
    /**
       * 过滤出有权限菜单，下拉框选择时，只显示有权限的菜单
       * */
    filterMenuPermissions() {
      const getRouter = JSON.parse(JSON.stringify(this.routesMap))
      const oldMenu = getRouter.filter(item => item.meta.hasOwnProperty('customPath'))
      this.carouselMenuOptionPermission = oldMenu.filter(item => this.hasPermission(item.code))
      // console.log('this.carouselMenuOptionPermission', JSON.parse(JSON.stringify(this.carouselMenuOptionPermission)))
    },
    /**
       * 初始化参数
       */
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      // 报表日期默认值设置
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      this.currentTime = year.toString() + month
      this.temp.encOrDecDimValue = this.currentTime
      this.temp.softwareInstallDimValue = this.currentTime
    },
    /**
       * 弹框显示
       */
    show() {
      this.visible = true
      this.resetTemp()
      const userId = this.$store.getters.userId
      getValueByCondition({ sysUserId: userId, menuCode: '1001', tableKey: 'largeScreenConfig', type: 4 }).then(res => {
        if (res.data) {
          // 如果存在data,直接显示接口返回，否则为前端页面设置的默认值
          const response = JSON.parse(res.data.value)
          // console.log('response', response)
          this.temp = Object.assign({}, this.temp, JSON.parse(res.data.value))
          // console.log('this.temp', JSON.parse(JSON.stringify(this.temp)))
          // 报表日期
          if (this.temp.encOrDecDimBaseType === 1 && this.temp.encOrDecDimValue === '') {
            this.temp.encOrDecDimValue = this.currentTime
            this.dataTemp.encOrDecDimBaseType = 1
            this.dataTemp.encOrDecDimValue = this.currentTime
          } else {
            this.dataTemp.encOrDecDimBaseType = this.temp.encOrDecDimBaseType
            this.dataTemp.encOrDecDimValue = this.temp.encOrDecDimValue
          }
          // 报表日期
          if (this.temp.softwareInstallDimBaseType === 1 && this.temp.softwareInstallDimValue === '') {
            this.temp.softwareInstallDimValue = this.currentTime
            this.dataTempSoftwareInstallUse.encOrDecDimBaseType = 1
            this.dataTempSoftwareInstallUse.encOrDecDimValue = this.currentTime
          } else {
            this.dataTempSoftwareInstallUse.encOrDecDimBaseType = this.temp.softwareInstallDimBaseType
            this.dataTempSoftwareInstallUse.encOrDecDimValue = this.temp.softwareInstallDimValue
          }
          // 报表日期
          if (this.temp.outgoingDocumentDimBaseType === 1 && this.temp.outgoingDocumentDimValue === '') {
            this.temp.outgoingDocumentDimValue = this.currentTime
            this.dataTempOutgoingDocument.encOrDecDimBaseType = 1
            this.dataTempOutgoingDocument.encOrDecDimValue = this.currentTime
          } else {
            this.dataTempOutgoingDocument.encOrDecDimBaseType = this.temp.outgoingDocumentDimBaseType
            this.dataTempOutgoingDocument.encOrDecDimValue = this.temp.outgoingDocumentDimValue
          }
          this.dataTemp.dateCycleLargeScreen = this.temp.encOrDecDimDataCycle
          this.dataTempSoftwareInstallUse.dateCycleLargeScreen = this.temp.softwareInstallDimDataCycle
          this.dataTempOutgoingDocument.dateCycleLargeScreen = this.temp.outgoingDocumentDimDataCycle
          // 轮播菜单后台返回值与下拉列表不一致时,进行筛选显示
          const newMenu = []
          const responseMenu = response.carouselMenu
          const menuList = this.carouselMenuOptionPermission
          if (menuList.length > 0 && responseMenu.length > 0) {
            for (let i = 0; i < menuList.length; i++) {
              for (let j = 0; j < responseMenu.length; j++) {
                if (menuList[i].name === responseMenu[j]) {
                  newMenu.push(responseMenu[j])
                }
              }
            }
          }
          this.temp.carouselMenu = newMenu
          // 轮播菜单
        } else {
          // 如果没有初始化配置，可选菜单只有一个，默认选中一个，可选菜单>=2个，默认选中前两个
          if (this.carouselMenuOptionPermission.length > 0 && this.carouselMenuOptionPermission.length < 2) {
            this.temp.carouselMenu = this.carouselMenuOptionPermission.slice(0, 1).map(obj => obj.name)
          }
          if (this.carouselMenuOptionPermission.length >= 2) {
            this.temp.carouselMenu = this.carouselMenuOptionPermission.slice(0, 2).map(obj => obj.name)
          }
          this.dataTemp.encOrDecDimBaseType = this.temp.encOrDecDimBaseType
          this.dataTemp.encOrDecDimValue = this.temp.encOrDecDimValue
          this.dataTemp.dateCycleLargeScreen = this.temp.encOrDecDimDataCycle
          this.dataTempSoftwareInstallUse.encOrDecDimBaseType = this.temp.softwareInstallDimBaseType
          this.dataTempSoftwareInstallUse.encOrDecDimValue = this.temp.softwareInstallDimValue
          this.dataTempSoftwareInstallUse.dateCycleLargeScreen = this.temp.softwareInstallDimDataCycle
          this.dataTempOutgoingDocument.encOrDecDimBaseType = this.temp.outgoingDocumentDimBaseType
          this.dataTempOutgoingDocument.encOrDecDimValue = this.temp.outgoingDocumentDimValue
          this.dataTempOutgoingDocument.dateCycleLargeScreen = this.temp.outgoingDocumentDimDataCycle
        }
      })
      this.$nextTick(() => {
        // console.log('弹框显示：dataTemp', JSON.parse(JSON.stringify(this.dataTemp)))
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
       * 确认按钮点击（保存页面修改数据）
       */
    saveBtn() {
      // 加解密报表查询日期
      if (this.$refs['queryData']) {
        const encQuery = this.$refs['queryData'].getQuery()
        this.temp.encOrDecDimBaseType = encQuery.dimBaseType
        this.temp.encOrDecDimValue = encQuery.dimValue
        this.temp.encOrDecDimDataCycle = encQuery.dateCycleLargeScreen
      }
      // 软件安装与使用报表查询日期
      if (this.$refs['queryDataSoftwareInstallUse']) {
        const encQuery = this.$refs['queryDataSoftwareInstallUse'].getQuery()
        this.temp.softwareInstallDimBaseType = encQuery.dimBaseType
        this.temp.softwareInstallDimValue = encQuery.dimValue
        this.temp.softwareInstallDimDataCycle = encQuery.dateCycleLargeScreen
      }
      // 文件外发报表查询日期
      if (this.$refs['queryDataOutgoingDocument']) {
        const encQuery = this.$refs['queryDataOutgoingDocument'].getQuery()
        this.temp.outgoingDocumentDimBaseType = encQuery.dimBaseType
        this.temp.outgoingDocumentDimValue = encQuery.dimValue
        this.temp.outgoingDocumentDimDataCycle = encQuery.dateCycleLargeScreen
      }
      // console.log('保存', JSON.parse(JSON.stringify(this.temp)))
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.temp.carouselMenu.length < 1) {
            this.$notify({ title: this.$t('text.error'), message: this.$t('pages.largeScreenConfig28'), type: 'error', duration: 2000 })
            this.submitting = false
            return true
          }
          const userId = this.$store.getters.userId
          const tempString = JSON.stringify(this.temp)
          insertPersonalization({ sysUserId: userId, menuCode: '1001', value: tempString, tableKey: 'largeScreenConfig', type: 4 }).then(response => {
            this.visible = false
            this.submitting = false
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    /**
       * 通用表单验证（大屏配置相关表单如果设置的时间过小，可能会导致服务器奔溃，所以对下拉框选中秒和分钟的，稍作限制）
       * @param value 文本框输入内容
       * @param time 时间下拉框当前选中 0：秒，1：分钟，2：小时
       * @param msg 错误提示信息（不符合条件时的错误提示）
       * @param seconds 秒数（下拉框选中秒时，最小秒数）
       * @param minute 分钟（下拉框选中分钟时，最小分钟）
       */
    itemFormValidation(value, time, msg, seconds, minute) {
      if (time === 0 && value < seconds) {
        return msg + this.$t('pages.largeScreenConfig29') + seconds + this.$t('text.second')
      } else if (time === 1 && value < minute) {
        return msg + this.$t('pages.largeScreenConfig29') + minute + this.$t('text.minute')
      } else if (time === 0 && value > 86400) {
        return msg + this.$t('pages.largeScreenConfig30')
      } else if (time === 1 && value > 1440) {
        return msg + this.$t('pages.largeScreenConfig31')
      } else if (time === 2 && value > 24) {
        return msg + this.$t('pages.largeScreenConfig32')
      } else {
        return ''
      }
    },
    carouselMenuValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.carouselMenuSelect, this.$t('pages.menuInterval'), 10, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    encOrDecChartSwitchValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.encOrDecChartSwitchSelect, this.$t('pages.largeScreenConfig1') + '，' + this.$t('pages.largeScreenConfig3'), 8, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    encOrDecTodayLogValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.encOrDecTodayLogSelect, this.$t('pages.largeScreenConfig27') + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    encOrDecTodayValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.encOrDecTodaySelect, this.$t('pages.largeScreenConfig7') + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    encOrDecDateQueryValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.encOrDecDateQuerySelect, this.$t('pages.largeScreenConfig9') + '，' + this.$t('pages.largeScreenConfig3'), 600, 10)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    todayRiskValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.todayRiskSelect, this.$t('pages.largeScreenConfig12') + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    softwareInstallUseLogValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.softwareInstallUseLogSelect, this.$t('pages.largeScreenConfigMsg2') + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    softwareInstallUseQueryValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.softwareInstallUseQuerySelect, this.$t('pages.largeScreenConfig9') + '，' + this.$t('pages.largeScreenConfig3'), 600, 10)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    operationMaintenanceChartSwitchValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.operationMaintenanceChartSwitchSelect, this.$t('pages.largeScreenConfig1') + '，' + this.$t('pages.largeScreenConfig3'), 8, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    operationMaintenanceCpuValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.operationMaintenanceCpuSelect, this.$t('pages.largeScreenConfig16') + '，' + this.$t('pages.largeScreenConfig3'), 2, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    operationMaintenanceCollectValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.operationMaintenanceCollectSelect, this.$t('pages.largeScreenConfig18') + '，' + this.$t('pages.largeScreenConfig3'), 600, 10)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    operationMaintenanceMonitoringLogValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.operationMaintenanceMonitoringLogSelect, this.$t('pages.keyMonitoringLog') + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    operationMaintenanceTerminalUserValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.operationMaintenanceTerminalUserSelect, this.$t('pages.largeScreenConfig21') + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    operationMaintenanceLast7DaysValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.operationMaintenanceLast7DaysSelect, '近7日数据变化趋势、审计日志增长率' + '，' + this.$t('pages.largeScreenConfig3'), 600, 10)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    operationMaintenanceServerValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.operationMaintenanceServerSelect, this.$t('pages.serverInfo') + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    outgoingDocumentEventLogValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.outgoingDocumentEventLogSelect, '实时文件外发事件' + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    outgoingDocumentTodayDataValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.outgoingDocumentTodayDataSelect, '今日数据' + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    outgoingDocumentRiskLogValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.outgoingDocumentRiskLogSelect, '风险外发实时预警' + '，' + this.$t('pages.largeScreenConfig3'), 60, 1)
      if (result === '') { callback() } else { callback(new Error(result)) }
    },
    outgoingDocumentQueryValidator(rule, value, callback) {
      const result = this.itemFormValidation(value, this.temp.outgoingDocumentQuerySelect, this.$t('pages.largeScreenConfig9') + '，' + this.$t('pages.largeScreenConfig3'), 600, 10)
      if (result === '') { callback() } else { callback(new Error(result)) }
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-dialog__body .el-input .el-input__inner:read-only{
    background-color: #f5f5f5 !important;
  }
  >>>.el-dialog__body .el-input .el-input__inner:disabled{
    background-color: #e4e7e9 !important;
  }
</style>
