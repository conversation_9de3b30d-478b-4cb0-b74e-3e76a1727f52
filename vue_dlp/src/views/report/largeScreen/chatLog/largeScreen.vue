<template>
  <div :style="{ height: appContainerHeight}" class="app-container largeScreen-report">
    <div class="table-container">
      <div class="large-screen-head">
        <div class="large-screen-head-title">即时通讯工具数据</div>
        <div class="large-screen-head-time">
          <div>最后更新时间：{{ temp.lastUpdateTime }}</div>
          <div>系统当前时间：{{ temp.currentTime }}</div>v
        </div>
        <div class="large-screen-head-search">
          <query/>
        </div>
      </div>
      <div class="large-screen-body">
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">聊天记录数Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.chatHistoryData"
                  :chart-option="temp.chatHistoryOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-name">{{ item.currentName }}</div>
                <div class="large-screen-middle-num">{{ item.currentNum }}</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">通讯工具使用情况</div>
              </div>
              <div class="large-screen-chart-div">
                <div class="div_risk_ranking_item" style="color: orange">
                  <span>操作员</span>
                  <span>操作员组</span>
                  <span>聊天记录数</span>
                </div>
                <div :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.chatRecord" :class-option="classOption">
                    <div v-for="(item, index) in temp.chatRecord" :key="index" class="div_risk_ranking_item">
                      <span :title="item.user">{{ item.user }}</span>
                      <span :title="item.userGroup">{{ item.userGroup }}</span>
                      <span :title="item.num">{{ item.num }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">聊天使用软件分布</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.chatSoftwareData"
                  :chart-option="temp.chatSoftwareOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">聊天字符数Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.chatCharacterData"
                  :chart-option="temp.chatCharacterOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">消息类型分类</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.messageTypeData"
                  :chart-option="temp.messageTypeOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">文件传送Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.fileTransferData"
                  :chart-option="temp.fileTransferOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">图片视频传送Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.pictureVideoData"
                  :chart-option="temp.pictureVideoOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">按日期统计登录次数趋势</div>
              </div>
              <div class="large-screen-chart-div">
                <line-chart
                  :chart-data="temp.loginTimesData"
                  :chart-option="temp.loginTimesOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import Query from '@/views/report/largeScreen/common/query'
import LineChart from '@/views/report/largeScreen/common/LineChart'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import PieChart from '@/views/report/largeScreen/common/PieChart'
import { getCurrentTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
export default {
  name: 'LargeScreen',
  components: { Query, BarChart, PieChart, LineChart, vueSeamlessScroll },
  props: {
  },
  data() {
    return {
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight: '19vh',
      middleOutHeight: '7.1vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: '',
        currentTime: '',
        middleMsg: [
          { currentName: '聊天记录数', currentNum: '89000' },
          { currentName: '账号登录数', currentNum: '20GB' },
          { currentName: '语句数', currentNum: '3123' },
          { currentName: '字符数', currentNum: '423' },
          { currentName: '图片数', currentNum: '1245' },
          { currentName: '文件数', currentNum: '3541' }
        ],
        // 聊天记录数Top10对象(柱状图)
        chatHistoryData: [120, 200, 150, 80, 100, 120, 200, 150, 80, 100],
        chatHistoryOption: {
          xAxis: {
            data: ['小猪', '小新', '小丑', '小贝', '小诺', '小明', '小艾', '小弟', '小幅', '小鬼']
          },
          series: [
            {
              data: [120, 200, 150, 80, 100, 120, 200, 150, 80, 100]
            }
          ]
        },
        // 加解密记录
        chatRecord: [
          { sort: 1, user: '小新', userGroup: '研发一部', time: '2024-8-13 16:08:41', num: '1245' },
          { sort: 2, user: '小黄', userGroup: '研发二部', time: '2024-8-13 17:45:44', num: '1124' },
          { sort: 3, user: '小黑', userGroup: '测试部', time: '2024-8-13 18:21:12', num: '1025' },
          { sort: 4, user: '小康', userGroup: '市场部', time: '2024-8-13 18:21:12', num: '954' },
          { sort: 5, user: '小莉', userGroup: '行政部', time: '2024-8-13 18:21:12', num: '854' },
          { sort: 6, user: '小妖', userGroup: '研发四部', time: '2024-8-13 14:46:15', num: '245' }
        ],
        // 聊天使用软件分布(饼图)
        chatSoftwareData: [
          { value: 1048, name: '钉钉' },
          { value: 735, name: '微信' },
          { value: 45, name: '企业微信' },
          { value: 212, name: '企业QQ' },
          { value: 57, name: 'QQ' },
          { value: 145, name: '飞秋' }
        ],
        chatSoftwareOption: {
          series: [
            {
              radius: ['30%', '50%']
            }
          ]
        },
        // 聊天字符数Top10对象(柱状图)
        chatCharacterData: [8, 45, 66, 95, 8, 45, 66, 95, 8, 45],
        chatCharacterOption: {
          yAxis: {
            type: 'category',
            data: ['小猪', '小新', '小丑', '小贝', '小诺', '小明', '小艾', '小弟', '小幅', '小鬼']
          },
          xAxis: {
            type: 'value'
          },
          series: [
            {
              data: [8, 45, 66, 95, 8, 45, 66, 95, 8, 45]
            }
          ]
        },
        // 消息类型分类(饼图)
        messageTypeData: [
          { value: 1048, name: '视频' },
          { value: 735, name: '链接' },
          { value: 735, name: '文件' },
          { value: 735, name: '表情' },
          { value: 735, name: '图片' },
          { value: 580, name: '文字' }
        ],
        messageTypeOption: {
          series: [
            {
              radius: '50%',
              center: ['50%', '50%'],
              itemStyle: {
                borderRadius: 8
              }
            }
          ]
        },
        // 文件传送Top10对象(柱状图)
        fileTransferData: [120, 200, 150, 80, 120, 200, 150, 80, 120, 200],
        fileTransferOption: {
          xAxis: {
            data: ['小猪', '小新', '小丑', '小贝', '小诺', '小明', '小艾', '小弟', '小幅', '小鬼'],
            axisLabel: {
              interval: 0,
              // 倾斜的程度
              rotate: 25
            }
          },
          series: [
            {
              data: [120, 200, 150, 80, 120, 200, 150, 80, 120, 200]
            }
          ]
        },
        // 图片视频传送Top10对象(饼图)
        pictureVideoData: [
          { value: 1048, name: '小莉' },
          { value: 735, name: '小康' },
          { value: 580, name: '小计' },
          { value: 580, name: '小黑' },
          { value: 580, name: '小鬼' },
          { value: 580, name: '小幅' },
          { value: 580, name: '小队' },
          { value: 580, name: '小数' },
          { value: 580, name: '小瓦' },
          { value: 484, name: '小二' }
        ],
        pictureVideoOption: {
          series: [
            {
              radius: [20, 50],
              center: ['50%', '50%'],
              roseType: 'area',
              itemStyle: {
                borderRadius: 8
              }
            }
          ]
        },
        // 按日期加密趋势(折线图)
        loginTimesData: [],
        loginTimesOption: {
          legend: {
            data: ['钉钉', '微信', '企业微信', '企业QQ', 'QQ']
          },
          xAxis: {
            data: ['2024-2-22', '2024-2-21', '2024-2-20', '2024-2-19', '2024-2-18', '2024-2-17', '2024-2-16']
          },
          series: [
            {
              name: '钉钉',
              data: [150, 230, 224, 218, 135, 147, 260],
              type: 'line'
            },
            {
              name: '微信',
              data: [213, 123, 12, 34, 321, 421, 42],
              type: 'line'
            },
            {
              name: '企业微信',
              data: [154, 542, 421, 541, 544, 313, 721],
              type: 'line'
            },
            {
              name: '企业QQ',
              data: [641, 541, 754, 214, 641, 641, 541],
              type: 'line'
            },
            {
              name: 'QQ',
              data: [541, 654, 214, 644, 314, 641, 654],
              type: 'line'
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
    this.getReportInfo()
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    this.temp.currentTime = getCurrentTime()
    this.getTimes()
  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
  },
  mounted() {
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  methods: {
    /**
     * 系统当前时间，实时获取，实时刷新
     * */
    getTimes() {
      setInterval(this.getSystemTime, 1000)
    },
    /**
     * 系统当前时间，赋值
     * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取页面数据
     * */
    getReportInfo() {
      // 最后更新于
      this.temp.lastUpdateTime = getCurrentTime()
    },
    /**
     * 判断是否全屏（监听全屏）
     * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
     * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
     * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      // console.log('this.isFullScreen', this.isFullScreen)
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight = '23.5vh'
        this.middleOutHeight = '8.5vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight = '19vh'
        this.middleOutHeight = '7.1vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  //中间特殊的小块
  .large-screen-middle-out{
    width: 49%;
    float: left;
    div{
      width: 90%;
      margin: auto;
      font-size: 15px;
      height: 60%; /* 设置div的高度为50% */
      display: flex; /* 使用flex布局 */
      align-items: center; /* 垂直居中 */
    }
    .large-screen-middle-num{
      font-size: 22px;
    }
    .large-screen-middle-name{
      font-size: 14px;
      height: 40%; /* 设置div的高度为50% */
      display: flex; /* 使用flex布局 */
      align-items: center; /* 垂直居中 */
    }
  }
  .activeClass{
    color: #268BDE;
  }
  .pointer{
    cursor: pointer;
  }
</style>

