<template>
  <full-screen-container ref="fullScreenContainer">
    <div class="carousel-content">
      <!--组件轮播部分-->
      <component :is="currentComponent" :is-first="isFirst"></component>
      <!-- 左箭头按钮 -->
      <div class="carouselArrow leftBtn" :title="this.$t('pages.previous')" @click="throttle(PrevFun)"><i class="el-icon-arrow-left"></i></div>
      <!-- 右箭头按钮 -->
      <div class="carouselArrow rightBtn" :title="this.$t('pages.next')" @click="throttle(NextFun)"><i class="el-icon-arrow-right"></i></div>
      <!-- 下方指示点容器 -->
      <div class="instBox">
        <div
          v-for="(item,index) in componentsPermissions"
          :key="index"
          class="inst"
          :class="[index==currentIndex?'instActv':'']"
          @click="instFun(index)"
        >
          {{ $t('route.' + item.meta.title) }}
        </div>
      </div>
    </div>
  </full-screen-container>
</template>

<script>
import FullScreenContainer from '@/views/report/largeScreen/common/fullScreenContainer';
// 导入你的Vue组件
import EncOrDecFileAnalysisLargeScreen from '@/views/report/largeScreen/encOrDecFileAnalysis/largeScreen'
import TodayReport from '@/views/report/largeScreen/todayReport/largeScreen'
import ClientUsage from '@/views/report/largeScreen/clientUsage/largeScreen'
import SoftwareInstallUse from '@/views/report/largeScreen/softwareInstallUse/largeScreen'
import SensitiveFileOutLargeScreen from '@/views/report/largeScreen/sensitiveFileOutLog/largeScreen'
import ComprehensiveAnalysis from '@/views/report/largeScreen/comprehensiveAnalysis/largeScreen'
import MobileStorage from '@/views/report/largeScreen/mobileStorage/largeScreen'
import OnlineBehavior from '@/views/report/largeScreen/onlineBehavior/largeScreen'
import ChatLog from '@/views/report/largeScreen/chatLog/largeScreen'
import OperationMaintenance from '@/views/report/largeScreen/operationMaintenance/largeScreen'
import { getValueByCondition } from '@/api/user';
import { getSetIntervalTime } from '@/utils/reportLargeScreen';
import { mapGetters } from 'vuex';
export default {
  name: 'Carousel',
  components: {
    EncOrDecFileAnalysisLargeScreen,
    TodayReport,
    ClientUsage,
    SoftwareInstallUse,
    SensitiveFileOutLargeScreen,
    ComprehensiveAnalysis,
    MobileStorage,
    OnlineBehavior,
    ChatLog,
    OperationMaintenance,
    FullScreenContainer
  },
  data() {
    return {
      isFullScreen: false,
      // 当前显示报表的索引
      currentIndex: 0,
      // 所有轮播报表组件
      components: [],
      // 通过components中的code过滤出有权限的轮播组件，存放在componentsPermissions中
      componentsPermissions: [],
      // 用来节流防止重复点击
      flag: true,
      // 自动执行下一张定的时器
      start: null,
      // 轮播定时器时长
      rotationDuration: 1000 * 60 * 5,
      // 一个函数执行完在执行另一个函数
      dataLoadPromise: undefined,
      // 是否首次加载
      isMounted: false,
      // 每次轮播的时候，将所以添加到数组中
      carouselIndex: [],
      // 当前菜单是否第一次轮播
      isFirst: true
    };
  },
  computed: {
    ...mapGetters([
      'routesMap'
    ]),
    // eslint-disable-next-line vue/return-in-computed-property
    currentComponent() {
      if (this.componentsPermissions.length > 0) {
        return this.componentsPermissions[this.currentIndex].name;
      }
    }
  },
  created() {
    this.carouselIndex = []
  },
  activated() {
    if (this.isMounted === false) {
      this.automaticWheelSeeding()
    }
  },
  mounted() {
    // 挂载后
    this.isMounted = true
    this.getLargeScreenConfig()
    this.dataLoadPromise = Promise.all([
      this.getLargeScreenConfig()
    ]).then(() => {
      this.automaticWheelSeeding()
    })
    setTimeout(() => {
      this.isMounted = false
    }, 1000)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    clearTimeout(this.start)
  },
  deactivated() {
    // 组件停用时--清除监听
    clearTimeout(this.start)
  },
  methods: {
    /**
       * 获取大屏配置中的配置信息
       * */
    getLargeScreenConfig() {
      const userId = this.$store.getters.userId
      return getValueByCondition({ sysUserId: userId, menuCode: '1001', tableKey: 'largeScreenConfig', type: 4 }).then(res => {
        const getRouter = JSON.parse(JSON.stringify(this.routesMap))
        const objectsArray = getRouter.filter(item => item.meta.hasOwnProperty('customPath'))
        if (res.data) {
          // 如果存在data,直接显示接口返回，否则为前端页面设置的默认值
          // console.log('res.data', JSON.parse(res.data.value))
          const response = JSON.parse(res.data.value)
          // 过滤出后台返回的菜单，轮播时，只轮大屏配置中配置的菜单
          const valuesArray = response.carouselMenu; // 示例数组
          this.componentsPermissions = valuesArray.map(value =>
            objectsArray.find(obj => obj.name === value)
          ).filter(Boolean); // 过滤掉undefined
          // 获取到轮播时长，用于定时器时长设置
          this.rotationDuration = getSetIntervalTime(response.carouselMenuValue, response.carouselMenuSelect)
        } else {
          // 如果没有初始化配置，轮播菜单默认轮播前两个
          if (objectsArray.length >= 2) {
            this.componentsPermissions = objectsArray.slice(0, 2)
          }
        }
      })
    },
    /**
       * 自动轮播（多长时间切换一次切一次菜单）
       */
    automaticWheelSeeding() {
      this.start = setInterval(() => {
        this.currentIndex = (this.currentIndex + 1) % this.componentsPermissions.length;
        this.isFist()
      }, this.rotationDuration);
      this.isFist()
      // 轮播时，重置手指放大缩小等
      this.$refs['fullScreenContainer'].resetScale()
    },
    /**
     * 判断轮播菜单是否第一次加载，区分接口请求是否产生日志
     * */
    isFist() {
      // 判断carouselIndex数组中是否存在轮播索引，如果不存在将索引push进去，设置isFirst为true,否则设置isFirst为false
      const exists = this.carouselIndex.includes(this.currentIndex);
      if (exists === false) {
        this.isFirst = true
        this.carouselIndex.push(this.currentIndex)
      } else {
        this.isFirst = false
      }
    },
    /**
       * 指示器指示器点击时
       * @param index
       */
    instFun(index) {
      clearTimeout(this.start)
      this.currentIndex = index
      this.automaticWheelSeeding()
    },
    /**
       * 这里通过额外封装的节流函数触发 PrevFun() 和 NextFun(),以达到防止重复点击的效果
       * */
    throttle(fun) {
      if (this.flag) {
        this.flag = false;
        // 此为模板中传递进来的PrevFun()或NextFun()函数
        fun();
        setTimeout(() => {
          this.flag = true;
        }, 650); // 设置节流间隔时间,不得小于图片过渡时间
      }
    },
    /**
       * 上一张报表
       * @constructor
       */
    PrevFun() {
      clearTimeout(this.start)
      this.currentIndex = (this.currentIndex - 1) % this.componentsPermissions.length;
      if (this.currentIndex < 0) {
        this.currentIndex = this.componentsPermissions.length - 1
      }
      this.automaticWheelSeeding()
    },
    /**
       * 下一张报表
       * @constructor
       */
    NextFun() {
      clearTimeout(this.start)
      this.currentIndex = (this.currentIndex + 1) % this.componentsPermissions.length;
      this.automaticWheelSeeding()
    }
  }
};
</script>
<style lang='scss' scoped>
  /* 两个按钮共有的样式,也可直接使用箭头图片替代 */
  .leftBtn,
  .rightBtn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(109, 109, 109, 0.445);
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    user-select: none;
  }
  .leftBtn {
    left: 10px;
  }
  .rightBtn {
    right: 10px;
  }
  .carouselArrow{
    display: none;
  }
  .carousel-content:hover .carouselArrow{
    display: block;
  }
  /* 下方指示器盒子 */
  .instBox {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 5px;
    display: flex;
  }
  /* 指示器 */
  .inst {
    height: 20px;
    line-height: 17px;
    margin-right: 8px;
    background: #cccccc;
    cursor: pointer;
    white-space: nowrap;
    font-size: 12px;
    color: #000000;
    padding: 2px 10px;
  }
  .inst:last-child {
    margin-right: 0px;
  }
  .instActv {
    border: 1px solid orange;
    background: orange;
  }
</style>
