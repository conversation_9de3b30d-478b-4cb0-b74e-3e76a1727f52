<template>
  <div :style="{ height: appContainerHeight}" class="app-container largeScreen-report">
    <div class="table-container">
      <div class="large-screen-head">
        <div class="large-screen-head-title">上网行为数据</div>
        <div class="large-screen-head-time">
          <div>最后更新时间：{{ temp.lastUpdateTime }}</div>
          <div>系统当前时间：{{ temp.currentTime }}</div>
        </div>
        <div class="large-screen-head-search">
          <query/>
        </div>
      </div>
      <div class="large-screen-body">
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">网页浏览次数Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.numberViewsData"
                  :chart-option="temp.numberViewsOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-name">{{ item.currentName }}</div>
                <div class="large-screen-middle-num">{{ item.currentNum }}</div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">热门网站浏览次数排名</div>
              </div>
              <div class="large-screen-chart-div">
                <div class="div_risk_ranking_item" style="color: orange">
                  <span>网站名称</span>
                  <span>浏览次数</span>
                </div>
                <div :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.browseRanking" :class-option="classOption">
                    <div v-for="(item, index) in temp.browseRanking" :key="index" class="div_risk_ranking_item">
                      <span :title="item.name">{{ item.name }}</span>
                      <span :title="item.value">{{ item.value }}次</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">网页搜索高频关键字</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.webSearchData"
                  :chart-option="temp.webSearchOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">网页浏览时长Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.timeTopData"
                  :chart-option="temp.timeTopOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">网站访问时长排名</div>
              </div>
              <div class="large-screen-chart-div">
                <div class="div_risk_ranking_item" style="color: orange">
                  <span>网站名称</span>
                  <span>浏览时长</span>
                </div>
                <div :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.timeRanking" :class-option="classOption">
                    <div v-for="(item, index) in temp.timeRanking" :key="index" class="div_risk_ranking_item">
                      <span :title="item.name">{{ item.name }}</span>
                      <span :title="item.value">{{ item.value }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="32" class="large-screen-row">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">网页粘贴记录数Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <bar-chart
                  :chart-data="temp.pasteTopData"
                  :chart-option="temp.pasteTopOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">论坛发帖记录数Top10对象</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.postMessageTopData"
                  :chart-option="temp.postMessageTopOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">网站分类访问量</div>
              </div>
              <div class="large-screen-chart-div">
                <pie-chart
                  :chart-data="temp.websiteClassData"
                  :chart-option="temp.websiteClassOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import Query from '@/views/report/largeScreen/common/query'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import PieChart from '@/views/report/largeScreen/common/PieChart'
import { getCurrentTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
export default {
  name: 'LargeScreen',
  components: { Query, BarChart, PieChart, vueSeamlessScroll },
  props: {
  },
  data() {
    return {
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight: '19vh',
      middleOutHeight: '7.1vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: '',
        currentTime: '',
        middleMsg: [
          { currentName: '网页浏览记录数', currentNum: '89000' },
          { currentName: '网页浏览时长', currentNum: '8423' },
          { currentName: '网页搜索记录数', currentNum: '3123' },
          { currentName: '网页粘贴记录数', currentNum: '423' },
          { currentName: '论坛发帖记录数', currentNum: '1245' },
          { currentName: '高频关键字搜索数', currentNum: '554' }
        ],
        // 网页浏览次数Top10对象(柱状图)
        numberViewsData: [120, 200, 150, 80, 100, 120, 120, 200, 150, 80],
        numberViewsOption: {
          xAxis: {
            data: ['小莉', '小弟', '小芳', '小幅', '小绿', '小牛', '小新', '小诺', '小二', '小明']
          },
          series: [
            {
              data: [120, 200, 150, 80, 100, 120, 120, 200, 150, 80]
            }
          ]
        },
        // 热门网站浏览次数排名(列表)
        browseRanking: [
          { value: 1048, name: '百度', sort: 1 },
          { value: 154, name: '新浪', sort: 2 },
          { value: 412, name: 'CSDN', sort: 3 },
          { value: 451, name: '掘金', sort: 4 },
          { value: 735, name: 'element', sort: 5 }
        ],
        // 网页搜索高频关键字(饼图)
        webSearchData: [
          { value: 145, name: '数组过滤' },
          { value: 453, name: '对象去重' },
          { value: 842, name: '正则表达式' },
          { value: 735, name: '数组去重' }
        ],
        webSearchOption: {
          series: [
            {
              radius: ['30%', '50%']
            }
          ]
        },
        // 网页浏览时长Top10对象(柱状图)
        timeTopData: [8, 45, 66, 95, 8, 45, 66, 95, 8, 45],
        timeTopOption: {
          xAxis: {
            data: ['小猪', '小新', '小丑', '小贝', '小诺', '小明', '小艾', '小弟', '小幅', '小鬼']
          },
          series: [
            {
              data: [8, 45, 66, 95, 8, 45, 66, 95, 8, 45]
            }
          ]
        },
        // 网站访问时长排名(列表)
        timeRanking: [
          { value: '8小时45分12秒', name: '百度', sort: 1 },
          { value: '7小时45分12秒', name: '新浪', sort: 2 },
          { value: '6小时45分12秒', name: 'CSDN', sort: 3 },
          { value: '5小时45分12秒', name: '掘金', sort: 4 },
          { value: '4小时45分12秒', name: 'element', sort: 5 },
          { value: '3小时45分12秒', name: '必应', sort: 6 }
        ],
        // 网页粘贴记录数Top10对象(柱状图)
        pasteTopData: [8, 45, 66, 95, 8, 45, 66, 95, 8, 45],
        pasteTopOption: {
          xAxis: {
            data: ['小猪', '小新', '小丑', '小贝', '小诺', '小明', '小艾', '小弟', '小幅', '小鬼']
          },
          series: [
            {
              data: [8, 45, 66, 95, 8, 45, 66, 95, 8, 45]
            }
          ]
        },
        // 论坛发帖记录数Top10对象(饼图)
        postMessageTopData: [
          { value: 1048, name: '小数' },
          { value: 735, name: '小弟' },
          { value: 735, name: '小康' },
          { value: 735, name: '小计' },
          { value: 735, name: '小平' },
          { value: 735, name: '小娥' },
          { value: 735, name: '小柔' },
          { value: 735, name: '小媛' },
          { value: 735, name: '小芳' },
          { value: 580, name: '小瓦' }
        ],
        postMessageTopOption: {
          series: [
            {
              radius: '50%',
              center: ['50%', '50%'],
              itemStyle: {
                borderRadius: 8
              }
            }
          ]
        },
        // 网站分类访问量(饼图)
        websiteClassData: [
          { value: 1048, name: '学习网站' },
          { value: 735, name: '购物网站' },
          { value: 735, name: '新闻网站' },
          { value: 580, name: '视频网站' }
        ],
        websiteClassOption: {
          series: [
            {
              radius: [20, 60],
              center: ['50%', '50%'],
              roseType: 'area',
              itemStyle: {
                borderRadius: 8
              }
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
    this.getReportInfo()
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
    this.temp.currentTime = getCurrentTime()
  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
  },
  mounted() {
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(this.getTimes);
  },
  methods: {
    /**
     * 系统当前时间，实时获取，实时刷新
     * */
    getTimes() {
      setInterval(this.getSystemTime, 1000)
    },
    /**
     * 系统当前时间，赋值
     * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取页面数据
     * */
    getReportInfo() {
      // 最后更新于
      this.temp.lastUpdateTime = getCurrentTime()
    },
    /**
     * 判断是否全屏（监听全屏）
     * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
     * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
     * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      // console.log('this.isFullScreen', this.isFullScreen)
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight = '23.5vh'
        this.middleOutHeight = '8.5vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight = '19vh'
        this.middleOutHeight = '7.1vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  //中间特殊的小块
  .large-screen-middle-out{
    width: 49%;
    float: left;
    div{
      width: 90%;
      margin: auto;
      font-size: 15px;
      height: 35px;
      line-height: 35px;
    }
    .large-screen-middle-num{
      font-size: 22px;
    }
    .large-screen-middle-name{
      font-size: 14px;
      height: 25px;
      line-height: 25px;
    }
  }
</style>

