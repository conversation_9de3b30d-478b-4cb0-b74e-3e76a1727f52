<template>
  <div :style="{ height: appContainerHeight}" class="app-container sensitiveness-report largeScreen-report">
    <div v-if="noData" class="detailsDiv">
      <div class="noData">{{ noDataMsg }}</div>
    </div>
    <div v-if="!noData" class="table-container">
      <!--<div class="table-container">-->
      <div class="large-screen-head">
        <div class="large-screen-head-title">{{ $t('route.todayRiskData') }}</div>
        <!--全屏、布局配置-->
        <TopIcoButton/>
        <div class="large-screen-head-time">
          <div>{{ $t('pages.dataWasLastUpdated') }}：{{ temp.lastUpdateTime }}</div>
          <div>{{ $t('pages.currentSystemTime') }}：{{ temp.currentTime }}</div>
        </div>
        <div class="large-screen-head-search">
          <span>
            {{ $t('pages.countByObject') }}：
            <el-select v-model="query.countByObject" :popper-append-to-body="!isFullScreen" is-filter :placeholder="$t('text.select')" style="width: 130px; margin-right: 10px;" @change="countByObjectChange">
              <el-option
                v-for="(item, index) in typeOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </span>
        </div>
      </div>
      <div class="large-screen-body">
        <!--页面通过layout动态布局-->
        <el-row v-for="(row, rowIndex) in layout" :key="'row-' + rowIndex" class="large-screen-row" :gutter="row.gutter">
          <el-col v-for="(col, colIndex) in row.cols" :key="'col-' + colIndex" :xs="24" :sm="24" :lg="col.span">
            <!--小方块样式-->
            <div v-if="col.feature ==='dataItemCondition'" class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in middleMsg" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-current">
                  {{ item.currentName }}
                  <el-tooltip class="item" effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ item.msg }}
                    </div>
                    <i><i class="el-icon-info" /></i>
                  </el-tooltip>
                </div>
                <div class="large-screen-middle-num">{{ item.currentNum }}</div>
                <div class="large-screen-middle-yd">{{ $t('pages.yesterday') }}{{ item.yesterdayNum }}
                  <span :style="{ 'font-size': item.increase === '∞' ? '32px' : '14px',color: item.increase > 0 ? '#ff0000' : (item.increase === '∞' ? '' : '#0E932E') }" style="margin-left: 20px">{{ item.increase === '∞' ? item.increase : item.increase + '%' }}</span>
                  <span>
                    <svg-icon v-if="item.increase > 0" icon-class="rise" :title="$t('pages.rise')"/>
                    <svg-icon v-if="item.increase < 0" icon-class="descend" :title="$t('pages.descend')"/>
                  </span>
                </div>
              </div>
            </div>
            <div v-if="col.feature !=='dataItemCondition'" class="large-screen-border">
              <!--小方块没有小标题-->
              <div class="large-screen-title-div">
                <div class="large-screen-title">{{ col.name }}</div>
                <div v-if="col.feature ==='riskRankingCondition'" class="large-screen-search">
                  <el-select v-if="query.countByObject === 2" v-model="query.riskRankingType" :popper-append-to-body="false" :placeholder="$t('text.select')" @change="riskRankingChange">
                    <el-option
                      v-for="item in terminalOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <el-select v-else v-model="query.riskRankingType" :popper-append-to-body="false" :placeholder="$t('text.select')" @change="riskRankingChange">
                    <el-option
                      v-for="item in userOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </div>
              <!--列表样式-->
              <!--风险排行-->
              <div v-if="col.feature ==='riskRankingCondition'" class="large-screen-chart-div">
                <div class="div_risk_ranking_item last_span_no_time" style="color: orange">
                  <span v-if="query.countByObject === 1">{{ (query.countByObject === 1 && query.riskRankingType === 0) ? $t('table.user') : $t('report.userDept') }}</span>
                  <span v-if="query.countByObject === 2">{{ (query.countByObject === 2 && query.riskRankingType === 0) ? $t('table.terminal') : $t('report.terminalDept') }}</span>
                  <span>{{ $t('table.riskCount') }}</span>
                  <span v-if="query.countByObject === 1 && query.riskRankingType === 0">{{ $t('report.userDept') }}</span>
                  <span v-if="query.countByObject === 2 && query.riskRankingType === 0">{{ $t('report.terminalDept') }}</span>
                  <span>{{ $t('table.changeRate') }}</span>
                </div>
                <div v-if="rowDatas.length === 0" :style="{ height: rankingDivHeight}" class="div_risk_ranking-chart-div no-data">
                  <div>{{ $t('text.noData') }}...</div>
                </div>
                <div v-else :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="rowDatas" :class-option="classOption">
                    <div v-for="(item, index) in rowDatas" :key="index" :class="item.isNew ? 'activeClassRed' : '  '" class="div_risk_ranking_item last_span_no_time">
                      <span :title="item.name">{{ item.name }}</span>
                      <span :title="item.sum">{{ item.sum }}{{ $t('pages.openTimes2') }}</span>
                      <span v-if="item.groupName" :title="item.groupName">{{ item.groupName }}</span>
                      <span :title="item.increase + '%'" :style="{ 'font-size': item.increase === '∞' ? '32px' : '14px' }">
                        {{ item.increase }}<span v-if="item.increase !== '∞'">%</span>
                        <svg-icon v-if="item.increase > 0" icon-class="rise" :title="$t('pages.rise')"/>
                        <svg-icon v-if="item.increase < 0" icon-class="descend" :title="$t('pages.descend')"/>
                      </span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
              <!--风险事件记录-->
              <div v-if="col.feature ==='riskLogCondition'" class="large-screen-chart-div">
                <div class="div_risk_ranking_item last_span_time" style="color: orange">
                  <span>{{ $t('table.riskLevel') }}</span>
                  <span>{{ $t('table.riskType') }}</span>
                  <span v-if="query.countByObject === 1">{{ $t('table.user') }}</span>
                  <span v-if="query.countByObject === 1">{{ $t('report.userDept') }}</span>
                  <span v-if="query.countByObject === 2">{{ $t('table.terminal') }}</span>
                  <span v-if="query.countByObject === 2">{{ $t('report.terminalDept') }}</span>
                  <span>{{ $t('table.createTime') }}</span>
                </div>
                <div v-if="rowDatasEvent.length === 0" :style="{ height: rankingDivHeight}" class="div_risk_ranking-chart-div no-data">
                  <div>{{ $t('text.noData') }}...</div>
                </div>
                <div v-else :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="rowDatasEvent" :class-option="classOption">
                    <div v-for="(item, index) in rowDatasEvent" :key="index" :class="item.riskLevel== 4 ? 'activeClassRed' : (item.riskLevel== 3 ?'activeClassYellow' : '  ' )" class="div_risk_ranking_item last_span_time">
                      <span :title="item.riskLevelName">{{ item.riskLevelName }}</span>
                      <span :title="item.riskDefName">{{ item.riskDefName }}</span>
                      <span v-if="query.countByObject === 1" :title="item.userName">{{ item.userName }}</span>
                      <span v-if="query.countByObject === 1" :title="item.userGroupName">{{ item.userGroupName }}</span>
                      <span v-if="query.countByObject === 2" :title="item.terminalName">{{ item.terminalName }}</span>
                      <span v-if="query.countByObject === 2" :title="item.termGroupName">{{ item.termGroupName }}</span>
                      <span :title="item.createTime">{{ item.createTime }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
              <!--除小方块和列表以外的样式-->
              <div v-else class="large-screen-chart-div">
                <!--近7日风险趋势-->
                <line-chart v-if="col.feature === '7DayRiskTrendCondition' && temp.lineLast7DaysOption.series[0].data.length > 0" :chart-data="temp.lineChartLast7DaysData" :chart-option="temp.lineLast7DaysOption" :style="{ height: chartDivHeight}"/>
                <div v-if="col.feature === '7DayRiskTrendCondition' && temp.lineLast7DaysOption.series[0].data.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <!--今日风险象限-->
                <scatter-diagram-chart v-if="col.feature === 'todayRiskQuadrantCondition' && temp.scatterChartSource.length > 0" :chart-source="temp.scatterChartSource" :chart-option="temp.scatterChartOption" :y-avg="temp.scatterYAvg" :median="temp.median" :style="{ height: chartDivHeight}"/>
                <div v-if="col.feature === 'todayRiskQuadrantCondition' && temp.scatterChartSource.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <!--各时间段内风险趋势-->
                <line-chart v-if="col.feature === 'riskTrendTimeCondition' && temp.lineTimeOption.series[0].data.length > 0" :chart-data="temp.lineChartTimeData" :chart-option="temp.lineTimeOption" :style="{ height: chartDivHeight}"/>
                <div v-if="col.feature === 'riskTrendTimeCondition' && temp.lineTimeOption.series[0].data.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <!--风险文档类型占比-->
                <pie-chart v-if="col.feature === 'riskDocumentTypesCondition' && temp.chartsFileTypeData.length > 0" :chart-data="temp.chartsFileTypeData" :chart-option="temp.chartFileTypeOption" :style="{ height: chartDivHeight}"/>
                <div v-if="col.feature === 'riskDocumentTypesCondition' && temp.chartsFileTypeData.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <!--泄露方式风险分析-->
                <bar-chart v-if="col.feature === 'riskAnalysisModeCondition' && temp.barChartGroupOption.series[0].data.length > 0" :chart-data="temp.barChartGroupData" :chart-option="temp.barChartGroupOption" :style="{ height: chartDivHeight}"/>
                <div v-if="col.feature === 'riskAnalysisModeCondition' && temp.barChartGroupOption.series[0].data.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
                <!--风险类型占比-->
                <pie-chart v-if="col.feature === 'proportionRiskTypesCondition' && temp.chartsRiskTypeData.length > 0" :chart-data="temp.chartsRiskTypeData" :chart-option="temp.chartRiskTypeOption" :style="{ height: chartDivHeight}"/>
                <div v-if="col.feature === 'proportionRiskTypesCondition' && temp.chartsRiskTypeData.length === 0" class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--布局配置弹框-->
    <dialog-layout-config ref="dialogLayoutConfig" :original-layout="originalLayout" :layout="orLayout" :parameter-option="parameterOption"/>
  </div>
</template>

<script>
import LineChart from '@/views/report/largeScreen/common/LineChart'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import PieChart from '@/views/report/largeScreen/common/PieChart'
import ScatterDiagramChart from '@/views/report/sensitiveControl/riskStatement/ScatterDiagramChart'
import { realTimeRiskReportSummary, realTimeRiskReportSummaryByAudit } from '@/api/report/baseReport/largeScreen/largeScreen'
import { getCurrentTime, getSetIntervalTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
import { getValueByCondition } from '@/api/user';
import DialogLayoutConfig from '@/views/report/largeScreen/common/DialogLayoutConfig';
import axios from 'axios' // 中断文件上传请求时使用
import TopIcoButton from '../common/TopIcoButton';
export default {
  name: 'LargeScreen',
  components: { LineChart, BarChart, PieChart, ScatterDiagramChart, vueSeamlessScroll, DialogLayoutConfig, TopIcoButton },
  props: {
    // 大屏轮播是否首次加载
    isFirst: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      noDataMsg: this.$t('pages.largeScreen_Msg1') + '...',
      // 是否首次加载
      isMounted: false,
      firstLoad: true,
      // 接口参数(自定义布局)
      parameterOption: {
        menuCode: 'IL3',
        tableKey: 'largeScreenTodayReport'
      },
      // 深拷贝originalLayout,否则弹框修改时，会影响父组件layout数据
      orLayout: [],
      // 页面需要展示的布局
      layout: [],
      // 动态配置布局
      // 注：flag代表的布局：['layout1-1', 'layout1-2', 'layout2-1', 'layout1-1-1', 'layout1-2-1', 'layout1', 'layout1-3', 'layout3-1']
      // 如flag:3为索引3，既平均分配的三列显示：layout1-1-1
      // feature:每一个展示项有一个特有的feature名称，不能重复，用于页面动态显示时判断
      // 初始化布局，layout会随着接口返回发生变化，所以初始化的数据要一直保留，不被修改
      originalLayout: [
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('pages.recent7DayRiskTrend'), type: 'chart-line', feature: '7DayRiskTrendCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg45'), type: 'chart-data', feature: 'dataItemCondition' },
            { span: 8, name: this.$t('pages.riskRanking'), type: 'chart-log', feature: 'riskRankingCondition' }
          ]
        },
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('pages.todayRiskQuadrant'), type: 'chart-splattering', feature: 'todayRiskQuadrantCondition' },
            { span: 8, name: this.$t('pages.riskTrendEachTimePeriod'), type: 'chart-line', feature: 'riskTrendTimeCondition' },
            { span: 8, name: this.$t('pages.riskEventRecord'), type: 'chart-log', feature: 'riskLogCondition' }
          ]
        },
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('pages.riskDocumentTypes'), type: 'chart-pie', feature: 'riskDocumentTypesCondition' },
            { span: 8, name: this.$t('pages.lossTypeRiskAnalysis'), type: 'chart-bar', feature: 'riskAnalysisModeCondition' },
            { span: 8, name: this.$t('pages.riskTypeProportion'), type: 'chart-pie', feature: 'proportionRiskTypesCondition' }
          ]
        }
      ],
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 中断请求
      sourceGetLargeScreenConfig: null,
      sourceGetLayout: null,
      sourceGetReportData: null,
      // 定时器
      currentTime: undefined,
      timeLoad: undefined,
      dataLoadPromise: undefined,
      intervalTimeLoad: 60000,
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight: '19vh',
      middleOutHeight: '11.1vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      // 暂无数据
      noData: true,
      query: {
        countByObject: 2,
        riskRankingType: 0
      },
      // 中间四个小方块数据
      middleMsg: [],
      // 风险排行 列表
      top10Users: [],
      top10TermGroups: [],
      top10Terms: [],
      top10UserGroups: [],
      // 风险排行
      rowDatas: [],
      // 风险事件
      rowDatasEvent: [],
      // 统计类型
      typeOptions: [{ value: 1, label: this.$t('pages.conutType1') }, { value: 2, label: this.$t('pages.conutType2') }],
      // 风险排行
      terminalOptions: [{ value: 0, label: this.$t('table.terminal') }, { value: 1, label: this.$t('report.terminalDept') }],
      userOptions: [{ value: 0, label: this.$t('table.user') }, { value: 1, label: this.$t('report.userDept') }],
      // 接口返回数据（保存数据，提供给下拉框切换时使用）
      responseData: {},
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: '',
        currentTime: '',
        // 近7日风险趋势(折线图)
        lineChartLast7DaysData: [],
        lineLast7DaysOption: {
          xAxis: {
            data: []
          },
          grid: {
            left: '5%',
            right: '10%',
            bottom: '3%',
            containLabel: true
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        },
        // 今日风险象限--散点图（每个点的坐标）
        scatterChartSource: [
          [2, 3],
          [-3, 2],
          [-2, -3],
          [4, -3]
        ],
        scatterYAvg: 0,
        median: 0,
        // 散点图option
        scatterChartOption: {
          title: {
            text: '',
            left: 'center',
            subtext: ''
          },
          tooltip: {
            show: true
          },
          grid: {
            left: '10%',    // 距离左侧的距离
            right: '13%',   // 距离右侧的距离
            top: '16%',    // 距离顶部的距离
            bottom: '12%'  // 距离底部的距离
          }
        },
        // 各时间段内风险趋势(折线图)
        lineChartTimeData: [],
        lineTimeOption: {
          xAxis: {
            name: this.$t('pages.time'),
            type: 'category',
            data: []
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        },
        // 风险类型占比(饼图)
        chartsRiskTypeData: [],
        chartRiskTypeOption: {
          series: [
            {
              radius: ['30%', '60%'],
              center: ['50%', '50%']
            }
          ]
        },
        // 泄露方式风险分析(柱状图)
        barChartGroupData: [],
        barChartGroupOption: {
          xAxis: {
            data: [],
            axisLabel: {
              interval: 0,
              // 倾斜的程度
              rotate: 25
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 风险文档类型占比(饼图)
        chartsFileTypeData: [],
        chartFileTypeOption: {
          series: [
            {
              radius: ['30%', '60%'],
              center: ['50%', '50%']
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
    this.getLayout()
    this.dataLoadPromise = Promise.all([
      this.getLargeScreenConfig()
    ]).then(() => {
      // 定时器切换
      this.startIntervalFun()
      this.getReportData()
    })
    setTimeout(() => {
      this.isMounted = false
    }, 1000)
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    this.getTimes()
    this.temp.currentTime = getCurrentTime()
  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    if (this.isMounted === false) {
      this.getTimes()
      this.startIntervalFun()
    }
  },
  mounted() {
    this.isMounted = true
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    // clearInterval(this.currentTime);
    this.stopIntervalFun()
    this.stopRequest()
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    // clearInterval(this.currentTime);
    this.stopIntervalFun()
    this.stopRequest()
  },
  methods: {
    /**
     * 中断http请求（组件关闭或停用时，如果http接口未请求完成，页面切换时中断请求调用）
     * */
    stopRequest() {
      // 中断大屏配置接口获取
      if (this.sourceGetLargeScreenConfig) { this.sourceGetLargeScreenConfig.cancel() }
      // 中断大屏布局接口获取
      if (this.sourceGetLayout) { this.sourceGetLayout.cancel() }
      // 中断页面数据接口获取
      if (this.sourceGetReportData) { this.sourceGetReportData.cancel() }
    },
    /**
     * 定时器启动
     * */
    startIntervalFun() {
      if (window.todayReportTimeLoad) {
        clearInterval(window.todayReportTimeLoad)
      }
      if (window.todayReportCurrentTime) {
        clearInterval(window.todayReportCurrentTime)
      }
      window.todayReportTimeLoad = setInterval(() => {
        this.getReportData()
      }, this.intervalTimeLoad)
      window.todayReportCurrentTime = setInterval(this.getSystemTime, 1000)
    },
    /**
     * 定时器停止
     * */
    stopIntervalFun() {
      clearInterval(window.todayReportTimeLoad)
      window.todayReportTimeLoad = null
      clearInterval(window.todayReportCurrentTime)
      window.todayReportCurrentTime = null
    },
    /**
     * 获取layout布局（动态显示页面布局）
     * */
    getLayout(option) {
      const userId = this.$store.getters.userId
      this.sourceGetLayout = axios.CancelToken.source()
      let cacheToken = this.sourceGetLayout.token
      if (option === 'config') {
        cacheToken = ''
      }
      getValueByCondition({ sysUserId: userId, menuCode: this.parameterOption.menuCode, tableKey: this.parameterOption.tableKey, type: 4 }, cacheToken).then(res => {
        if (res.data) {
          // 如果存在data,直接显示接口返回，否则为前端页面设置的默认值
          // console.log('今日风险', JSON.parse(res.data.value))
          this.layout = JSON.parse(res.data.value)
          this.orLayout = JSON.parse(res.data.value)
        } else {
          // 初始化时先赋原来的初始值
          this.layout = JSON.parse(JSON.stringify(this.originalLayout))
          this.orLayout = JSON.parse(JSON.stringify(this.originalLayout))
        }
      })
    },
    /**
     * 打开布局配置弹框
     */
    configBtn() {
      this.getLayout()
      this.$refs.dialogLayoutConfig.show()
    },
    /**
     * 获取大屏配置中的配置信息（动态设置定时器时长）
     * */
    getLargeScreenConfig() {
      const userId = this.$store.getters.userId
      this.sourceGetLargeScreenConfig = axios.CancelToken.source()
      const cacheToken = this.sourceGetLargeScreenConfig.token
      return getValueByCondition({ sysUserId: userId, menuCode: '1001', tableKey: 'largeScreenConfig', type: 4 }, cacheToken).then(res => {
        if (res.data) {
          const response = JSON.parse(res.data.value)
          // console.log('response', response)
          if (response.todayCountByObject) {
            // 切换间隔时间，单位毫秒
            this.intervalTimeLoad = getSetIntervalTime(response.todayRiskValue, response.todayRiskSelect)
            this.query.countByObject = response.todayCountByObject
          }
        }
      })
    },
    /**
     * 系统当前时间，实时获取，实时刷新
     * */
    getTimes() {
      if (this.currentTime === undefined) {
        // this.currentTime = setInterval(this.getSystemTime, 1000)
      }
    },
    /**
     * 系统当前时间，赋值
     * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      // 风险用户排名
      this.top10Users = []
      // 风险用户部门排名
      this.top10UserGroups = []
      // 风险终端排名
      this.top10Terms = []
      // 风险终端部门排名
      this.top10TermGroups = []
      // 接口返回数据
      this.responseData = {}
    },
    /**
     * 获取报表数据
     */
    getReportData() {
      this.sourceGetReportData = axios.CancelToken.source()
      const cacheToken = this.sourceGetReportData.token
      let func = realTimeRiskReportSummary
      if (this.firstLoad && this.isFirst) {
        this.firstLoad = false
        func = realTimeRiskReportSummaryByAudit
      }
      func(cacheToken).then(res => {
        if (res.hasOwnProperty('data')) {
          // 接口有返回data的时候才处理，否则显示暂无数据
          this.noData = false
          const responseData = res.data
          this.responseData = responseData
          // console.log('query.countByObject', this.query.countByObject)
          // console.log('responseData', JSON.parse(JSON.stringify(responseData)))
          // 最后更新时间
          this.temp.lastUpdateTime = responseData.lastUpdateTime
          // 近7日风险趋势数据处理
          this.weeklyTrendDataProcessing(responseData)
          // 四个小方块数据处理
          this.middleDataProcessing(this.query.countByObject, responseData)
          // 风险排行数据处理
          this.riskRankingDataGet(this.query.countByObject, responseData)
          // 风险象限数据处理
          this.riskQuadrantDataProcessing(this.query.countByObject, responseData)
          // 各时间段内风险趋势数据处理
          this.timeRangeTrendDataProcessing(responseData)
          // 风险事件记录数据处理
          this.riskEventDataGet(responseData)
          // 饼图数据处理--（风险类型占比/风险文档类型占比）
          this.temp.chartsRiskTypeData = this.pieProcessing(responseData.riskDistribute)
          this.temp.chartsFileTypeData = this.pieProcessing(responseData.fileExtensionDistribute)
          // 泄露方式风险分析数据处理
          this.lossTypeDistributeProcessing(responseData)
        } else {
          this.noData = true
        }
      }).catch(error => {
        this.noDataMsg = this.$t('text.noData')
        console.log(error)
      })
    },
    /**
     * 近7日风险趋势(数据处理)
     * @param responseData 接口返回数据
     * */
    weeklyTrendDataProcessing(responseData) {
      const weeklyTrend = responseData.weeklyTrend
      // console.log('weeklyTrend', JSON.parse(JSON.stringify(weeklyTrend)))
      const xAxisData = []
      const seriesData = []
      if (weeklyTrend.length > 0) {
        weeklyTrend.forEach(item => {
          xAxisData.push(item.key)
          seriesData.push(item.value)
        })
      }
      this.temp.lineLast7DaysOption.xAxis.data = xAxisData
      this.temp.lineLast7DaysOption.series[0].data = seriesData
    },
    /**
     * 中间四个小方块数据处理(根据接口返回，处理成页面上好显示的格式)
     * @param type 统计类型下拉框--1：操作员；2：终端
     * @param responseData 接口返回数据
     */
    middleDataProcessing(type, responseData) {
      // console.log('responseData接口返回', JSON.parse(JSON.stringify(responseData)))
      // 1、默认两个小方块数据处理
      this.middleMsg = [
        { currentName: this.$t('pages.todayRisk'), msg: this.$t('pages.largeScreen_Msg2'), currentNum: '', yesterdayNum: '', increase: '' },
        { currentName: this.$t('pages.monitoringDocumentsToday'), msg: this.$t('pages.largeScreen_Msg3'), currentNum: '', yesterdayNum: '', increase: '' }
      ]
      this.middleMsg[0].currentNum = responseData.sum
      this.middleMsg[0].yesterdayNum = responseData.beforeDaySum
      this.middleMsg[0].increase = this.percentageTreatment(responseData.sum, responseData.beforeDaySum)
      this.middleMsg[1].currentNum = responseData.fileSum
      this.middleMsg[1].yesterdayNum = responseData.beforeDayFileSum
      this.middleMsg[1].increase = this.percentageTreatment(responseData.fileSum, responseData.beforeDayFileSum)
      if (type === 1) {
        // 2、操作员小方块数据处理
        const middleUser = [
          { currentName: this.$t('pages.todayRiskUser'), msg: this.$t('pages.largeScreen_Msg4'), currentNum: '', yesterdayNum: '', increase: '' },
          { currentName: this.$t('pages.todayRiskUserDepartment'), msg: this.$t('pages.largeScreen_Msg5'), currentNum: '', yesterdayNum: '', increase: '' }
        ]
        middleUser[0].currentNum = responseData.userSum
        middleUser[0].yesterdayNum = responseData.beforeDayUserSum

        middleUser[0].increase = this.percentageTreatment(responseData.userSum, responseData.beforeDayUserSum)
        middleUser[1].currentNum = responseData.userGroupSum
        middleUser[1].yesterdayNum = responseData.beforeDayUserGroupSum
        middleUser[1].increase = this.percentageTreatment(responseData.userGroupSum, responseData.beforeDayUserGroupSum)
        if (middleUser.length > 0) {
          middleUser.forEach(item => {
            this.middleMsg.push(item)
          })
        }
      } else {
        // 3、终端小方块数据处理
        const middleTerm = [
          { currentName: this.$t('pages.todayRiskTerminal'), msg: this.$t('pages.largeScreen_Msg6'), currentNum: '18', yesterdayNum: '16', increase: '' },
          { currentName: this.$t('pages.todayRiskTerminalDepartment'), msg: this.$t('pages.largeScreen_Msg7'), currentNum: '10', yesterdayNum: '12', increase: '' }
        ]
        middleTerm[0].currentNum = responseData.termSum
        middleTerm[0].yesterdayNum = responseData.beforeDayTermSum
        middleTerm[0].increase = this.percentageTreatment(responseData.termSum, responseData.beforeDayTermSum)
        middleTerm[1].currentNum = responseData.termGroupSum
        middleTerm[1].yesterdayNum = responseData.beforeDayTermGroupSum
        middleTerm[1].increase = this.percentageTreatment(responseData.termGroupSum, responseData.beforeDayTermGroupSum)
        if (middleTerm.length > 0) {
          middleTerm.forEach(item => {
            this.middleMsg.push(item)
          })
        }
      }
      // console.log('this.middleMsg3333', JSON.parse(JSON.stringify(this.middleMsg)))
    },
    /**
     * 风险排行数据获取（右上角）
     * 1、先将接口返回的数据赋值到页面data声明的变量中，及页面初始化数据显示
     * @param type 1：操作员；2：终端
     * @param responseData 接口返回数据
     */
    riskRankingDataGet(type, responseData) {
      // console.log('responseData··11', type, JSON.parse(JSON.stringify(responseData)))
      // 1、通过统计类型，先把一个类型下的两组数据获取到
      if (type === 1) {
        // 统计类型：操作员
        // 风险用户排名
        this.top10Users = responseData.top10Users
        // 风险用户部门排名
        this.top10UserGroups = responseData.top10UserGroups
        // 列表数据初始化为操作员排名
        if (this.query.riskRankingType === 0) {
          this.riskRankingDataProcessing(this.top10Users)
        } else {
          this.riskRankingDataProcessing(this.top10UserGroups)
        }
      } else {
        // 统计类型：终端
        // 风险终端排名
        this.top10Terms = responseData.top10Terms
        // 风险终端部门排名
        this.top10TermGroups = responseData.top10TermGroups
        // 列表数据初始化为终端排名
        if (this.query.riskRankingType === 0) {
          this.riskRankingDataProcessing(this.top10Terms)
        } else {
          this.riskRankingDataProcessing(this.top10TermGroups)
        }
      }
    },
    /**
     * 风险排行（右上角下拉框选中发生变化时，修改对应列表数据）
     * 2、边上下拉框查询条件发生变化时，对应数据进行赋值
     * */
    riskRankingChange(val) {
      const countByObject = this.query.countByObject
      // console.log('val11151', val)
      if (countByObject === 1) {
        if (val === 0) {
          this.riskRankingDataProcessing(this.top10Users)
        } else {
          this.riskRankingDataProcessing(this.top10UserGroups)
        }
      } else {
        if (val === 0) {
          this.riskRankingDataProcessing(this.top10Terms)
        } else {
          this.riskRankingDataProcessing(this.top10TermGroups)
        }
      }
    },
    /**
     * 风险排行数据处理(右上角)
     * 3、对（1，2）中筛选出的数据进行处理，添加increase百分比字段处理
     * @param datas 列表数据
     */
    riskRankingDataProcessing(datas) {
      // console.log('datas··33', JSON.parse(JSON.stringify(datas)))
      if (datas.length > 0) {
        datas.forEach((item, index) => {
          const currentNum = item.sum
          const compareDataNum = item.lastSum
          item.increase = this.percentageTreatment(currentNum, compareDataNum)
          item.sort = index + 1
        })
      }
      // 对比新旧数据，如果有变化，添加标记isNew: true，否则isNew: false。用于区分新旧数据显示
      const oldArray = this.rowDatas
      const newArray = datas
      const markNewObjects = (newArray, oldArray) => {
        return newArray.map(newObj => {
          // 数据从无到有的时候不变红色，只有新旧数据都存在，且存在新的数据时，才变红色
          let oldObj = {}
          if (oldArray.length > 0) {
            oldObj = oldArray.find(oldObj => oldObj.id === newObj.id)
          }
          return oldObj ? Object.assign({}, newObj, { isNew: false }) : Object.assign({}, newObj, { isNew: true });
        });
      };
      this.rowDatas = markNewObjects(newArray, oldArray);
    },
    /**
     * 风险事件记录数据获取
     * 将接口数据经过处理后在返回
     * @param responseData 接口返回数据
     */
    riskEventDataGet(responseData) {
      this.rowDatasEvent = responseData.last10Events
      if (this.rowDatasEvent.length > 0) {
        this.rowDatasEvent.forEach((item, index) => {
          item.sort = index + 1
          if (item.riskLevel === 1) {
            item.riskLevelName = this.$t('pages.severityOptions1')
          } else if (item.riskLevel === 2) {
            item.riskLevelName = this.$t('pages.severityOptions2')
          } else if (item.riskLevel === 3) {
            item.riskLevelName = this.$t('pages.severityOptions3')
          } else if (item.riskLevel === 4) {
            item.riskLevelName = this.$t('pages.severityOptions4')
          } else {
            item.riskLevelName = ''
          }
        })
      }
      // console.log('this.rowDatasEvent', JSON.parse(JSON.stringify(this.rowDatasEvent)))
    },
    /**
     * 统计类型发生变化（右上角下拉框）
     */
    countByObjectChange() {
      this.query.riskRankingType = 0
      // 获取报表数据
      this.middleDataProcessing(this.query.countByObject, this.responseData)
      this.riskRankingDataGet(this.query.countByObject, this.responseData)
      this.riskQuadrantDataProcessing(this.query.countByObject, this.responseData)
    },
    /**
     * 风险象限(数据处理)
     * @param type 1：操作员；2：终端
     * @param responseData 接口返回数据
     */
    riskQuadrantDataProcessing(type, responseData) {
      let riskQ = []
      if (type === 1) {
        riskQ = responseData.userRiskScatterList
      } else {
        riskQ = responseData.termRiskScatterList
      }
      if (riskQ) {
        // 风险象限（风险报表那边直接抄过来的）
        this.temp.scatterChartSource = riskQ.map(v => [v.riskLevel - 1, v.eventCount, v.userName, v.userId, v.terminalName, v.termId])
        this.temp.scatterYAvg = Math.max(...this.temp.scatterChartSource.map(v => v[1])) / 2
        this.temp.median = this.median(this.temp.scatterChartSource.map(v => v[1]))
        // console.log('this.temp.scatterChartSource', JSON.parse(JSON.stringify(this.temp)))
      }
    },
    /**
     * 对风险象限散点图的处理
     * */
    median(arr) {
      // 首先对数组进行排序
      arr.sort(function(a, b) {
        return a - b;
      });
      var len = arr.length;
      // 如果数组长度为奇数，则中位数为中间值
      if (len % 2 !== 0) {
        return arr[Math.floor(len / 2)];
      } else {
        var mid1 = arr[len / 2 - 1];
        var mid2 = arr[len / 2];
        return (mid1 + mid2) / 2;
      }
    },
    /**
     * 各时间段内风险趋势(数据处理)
     * @param responseData 接口返回数据
     * */
    timeRangeTrendDataProcessing(responseData) {
      const timeRangeTrend = responseData.timeRangeTrend
      let xAxisData = []
      const seriesData = []
      if (timeRangeTrend.length > 0) {
        timeRangeTrend.forEach(item => {
          seriesData.push(item.value)
        })
        xAxisData = this.generateHourArray(timeRangeTrend.length)
      }
      // console.log('xAxisData', xAxisData)
      // console.log('seriesData', seriesData)
      this.temp.lineTimeOption.xAxis.data = xAxisData
      this.temp.lineTimeOption.series[0].data = seriesData
    },
    /**
     * 各时间段内风险趋势(折线图横坐标时间处理)
     * 如果返回数据长度是多少，则时间是0点到多少点
     * @param length 后台返回数据长度
     * */
    generateHourArray(length) {
      return Array.from({ length: length }, (_, i) => {
        const hour = ('0' + i).slice(-2); // 确保是两位数字格式
        return `${hour}:00`;
      });
    },
    /**
     * 饼图数据处理(风险类型占比/风险文档类型占比)
     * @param pieData 接口返回饼图数据
     * */
    pieProcessing(pieData) {
      const name = Object.keys(pieData)
      const value = Object.values(pieData)
      const pieDataProcessing = []
      if (name.length > 0) {
        for (let i = 0; i < name.length; i++) {
          const obj = {}
          obj.value = value[i]
          obj.name = name[i]
          pieDataProcessing.push(obj)
        }
      }
      // console.log('pieDataProcessing', JSON.parse(JSON.stringify(pieDataProcessing)))
      return pieDataProcessing
    },
    /**
     * 泄露方式风险分析(数据处理)
     * @param responseData 接口返回数据
     * */
    lossTypeDistributeProcessing(responseData) {
      const lossTypeDistribute = responseData.lossTypeDistribute
      const name = Object.keys(lossTypeDistribute)
      const value = Object.values(lossTypeDistribute)
      this.temp.barChartGroupOption.xAxis.data = name
      this.temp.barChartGroupOption.series[0].data = value
    },
    /**
     * 百分比数据处理（页面中涉及百分比的计算）
     * @param currentNum 当前数据
     * @param compareDataNum 需要对比的数据
     * @returns {string}
     */
    percentageTreatment(currentNum, compareDataNum) {
      const increaseNum = (((currentNum - compareDataNum) / compareDataNum) * 100).toFixed(2)
      let increaseLabel
      // console.log('increaseNum', increaseNum)
      // 分母为0
      if (increaseNum === 'Infinity' || increaseNum === '-Infinity') {
        increaseLabel = '∞'
        // 分子分母都为0
      } else if (increaseNum === 'NaN') {
        increaseLabel = '0.00'
      } else {
        increaseLabel = increaseNum
      }
      return increaseLabel
    },
    /**
     * 判断是否全屏（监听全屏）
     * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
     * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
     * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      // console.log('this.isFullScreen', this.isFullScreen)
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight = '23.5vh'
        this.middleOutHeight = '13.2vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight = '19vh'
        this.middleOutHeight = '11.1vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .activeClassRed{
    color: #ff0000;
  }
  .activeClassYellow{
    color: #e6a23c;
  }
  .large-screen-row>.el-col{
    /*中间小方块*/
    .large-screen-middle-out{
      width: 49%;
      float: left;
      div{
        width: 90%;
        margin: auto;
        font-size: 15px;
        height: 35%;
        display: flex; /* 使用flex布局 */
        align-items: center; /* 垂直居中 */
      }
      .large-screen-middle-num{
        font-size: 22px;
      }
      .large-screen-middle-yd{
        font-size: 12px;
        height: 20%;
        display: flex; /* 使用flex布局 */
        align-items: center; /* 垂直居中 */
      }
    }
  }
</style>
