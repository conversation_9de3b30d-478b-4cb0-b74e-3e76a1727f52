<template>
  <div :style="{ height: appContainerHeight}" class="app-container sensitiveness-report largeScreen-report">
    <div class="table-container">
      <div class="large-screen-head">
        <div class="large-screen-head-title">{{ $t('route.encOrDecFileAnalysisLargeScreen') }}</div>
        <!--全屏、布局配置-->
        <TopIcoButton/>
        <div class="large-screen-head-time">
          <div><span class="today-color">{{ $t('pages.today') }}</span>{{ $t('pages.dataWasLastUpdated') }}：{{ temp.lastUpdateTime }}</div>
          <div>{{ $t('pages.currentSystemTime') }}：{{ temp.currentTime }}</div>
        </div>
        <div class="large-screen-head-search">
          <query ref="queryData" :data-temp="dataTemp" @thisQuery="thisQuery"/>
        </div>
      </div>
      <div class="large-screen-body">
        <!--页面通过layout动态布局-->
        <el-row v-for="(row, rowIndex) in layout" :key="'row-' + rowIndex" class="large-screen-row" :gutter="row.gutter">
          <el-col v-for="(col, colIndex) in row.cols" :key="'col-' + colIndex" :xs="24" :sm="24" :lg="col.span">
            <!--小方块样式-->
            <div v-if="col.feature ==='dataItemCondition'" class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-out-left">
                  <div>
                    <img :src="require(`@/assets/${item.img}.png`)" >
                    <!--<img src="@/assets/unLock.png">-->
                  </div>
                </div>
                <div class="large-screen-middle-out-right">
                  <div><span v-if="index === 2 || index === 3" class="today-color">{{ $t('pages.today') }}</span>{{ item.currentName }}</div><div :class="item.change? 'animate-background' : '  ' ">{{ item.currentNum }}</div>
                </div>
              </div>
            </div>
            <!--解密审批状态分布/提交申请Top10操作员-->
            <div v-if="col.feature==='approveApplyCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div v-if="showPieStatus" class="large-screen-title">{{ $t('pages.decryptionApprovalStatusDistribution') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleStatusOrApply()"></i></div>
                <div v-if="showBarApplyUser" class="large-screen-title">{{ $t('pages.submitApplicationsTop10Operators') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleStatusOrApply()"></i></div>
              </div>
              <div v-if="temp.chartsApprovalData.length === 0 && showPieStatus" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-if="temp.barChartApplyTopOption.series[0].data.length === 0 && temp.barChartApplyTopOption.series[1].data.length === 0 && temp.barChartApplyTopOption.series[2].data.length === 0 && showBarApplyUser" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-else class="large-screen-chart-div" @mouseenter="clearDeptOrUser('statusApply')" @mouseleave="startDeptOrUser('statusApply')">
                <pie-chart
                  v-if="temp.chartsApprovalData.length > 0 && showPieStatus"
                  :chart-data="temp.chartsApprovalData"
                  :chart-option="temp.chartApprovalOption"
                  :style="{ height: chartDivHeight}"
                />
                <bar-chart
                  v-if="(temp.barChartApplyTopOption.series[0].data.length > 0 || temp.barChartApplyTopOption.series[1].data.length > 0 || temp.barChartApplyTopOption.series[2].data.length > 0) && showBarApplyUser"
                  :chart-data="temp.barChartApplyTopData"
                  :chart-option="temp.barChartApplyTopOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
            <!--加解密记录-->
            <div v-if="col.feature==='encDecLogCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title"><span class="today-color">{{ $t('pages.today') }}</span>{{ $t('pages.encAndDecRecord') }}</div>
              </div>
              <div class="large-screen-chart-div">
                <div class="div_risk_ranking_item last_span_time" style="color: orange">
                  <span>{{ $t('table.type') }}</span>
                  <span>{{ $t('table.user') }}</span>
                  <span>{{ $t('table.userGroup') }}</span>
                  <span>{{ $t('table.time') }}</span>
                </div>
                <div v-if="temp.encOrdecryptionRecord.length === 0" :style="{ height: rankingDivHeight}" class="div_risk_ranking-chart-div no-data">
                  <div>{{ $t('text.noData') }}...</div>
                </div>
                <div v-else :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.encOrdecryptionRecord" :class-option="classOption">
                    <div v-for="(item, index) in temp.encOrdecryptionRecord" :key="index" :class="item.isNew ? 'activeClassRed' : '  '" class="div_risk_ranking_item last_span_time">
                      <span style="width: 35px" :title="item.typeName">{{ item.typeName }}</span>
                      <span :title="item.objectName">{{ item.objectName }}</span>
                      <span :title="item.groupName">{{ item.groupName }}</span>
                      <span :title="item.createTime">{{ item.createTime }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
            </div>
            <!--按部门统计解密情况Top10/解密Top10操作员-->
            <div v-if="col.feature==='encDecDeptUserCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div v-if="showBarChartGroup" class="large-screen-title">{{ $t('pages.top10DecDepartmentStatistics') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleDisplay()"></i></div>
                <div v-if="showBarChartDecrypt" class="large-screen-title">{{ $t('pages.decTop10Operator') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleDisplay()"></i></div>
              </div>
              <div v-if="temp.barChartGroupOption.series[0].data.length === 0 && showBarChartGroup" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-if="temp.barChartDecryptOption.series[0].data.length === 0 && showBarChartDecrypt" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-else class="large-screen-chart-div" @mouseenter="clearDeptOrUser('deptOrUser')" @mouseleave="startDeptOrUser('deptOrUser')">
                <bar-chart
                  v-if="temp.barChartGroupOption.series[0].data.length > 0 && showBarChartGroup"
                  :chart-data="temp.barChartGroupData"
                  :chart-option="temp.barChartGroupOption"
                  :style="{ height: chartDivHeight}"
                />
                <bar-chart
                  v-if="temp.barChartDecryptOption.series[0].data.length > 0 && showBarChartDecrypt"
                  :chart-data="temp.barChartDecryptTopData"
                  :chart-option="temp.barChartDecryptOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
            <!--按日期统计解密分布/按日期加密趋势-->
            <div v-if="col.feature==='encDecDistributionCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div v-if="showScatterDecDistribution" class="large-screen-title">{{ $t('pages.decDistributionDate') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleDistributionTrend()"></i></div>
                <div v-if="showLineCryptoTrend" class="large-screen-title">{{ $t('pages.encTrendsDate') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleDistributionTrend()"></i></div>
              </div>
              <div v-if="temp.chartDecryptionDistributionOption.series.length === 0 && showScatterDecDistribution" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-if="temp.lineTimeOption.series[0].data.length === 0 && temp.lineTimeOption.series[1].data.length === 0 && showLineCryptoTrend" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-else class="large-screen-chart-div" @mouseenter="clearDeptOrUser('distributionTrend')" @mouseleave="startDeptOrUser('distributionTrend')">
                <scatter-common
                  v-if="temp.chartDecryptionDistributionOption.series.length > 0 && showScatterDecDistribution"
                  :chart-data="temp.chartDecryptionDistributionData"
                  :chart-option="temp.chartDecryptionDistributionOption"
                  :style="{ height: chartDivHeight}"
                />
                <line-chart
                  v-if="(temp.lineTimeOption.series[0].data.length > 0 || temp.lineTimeOption.series[1].data.length > 0) && showLineCryptoTrend"
                  :chart-data="temp.lineChartTimeData"
                  :chart-option="temp.lineTimeOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
            <!--加密Top10部门/加密Top10操作员-->
            <div v-if="col.feature==='encDeptUserCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div v-if="showBarChartTodayGroup" class="large-screen-title"><span class="today-color">{{ $t('pages.today') }}</span>{{ $t('pages.top10EncDepartments') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleTodayDeptOrUser()"></i></div>
                <div v-if="showBarChartTodayDecrypt" class="large-screen-title"><span class="today-color">{{ $t('pages.today') }}</span>{{ $t('pages.top10EncUser') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleTodayDeptOrUser()"></i></div>
              </div>
              <div v-if="temp.barChartTodayGroupOption.series[0].data.length === 0 && showBarChartTodayGroup" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-if="temp.barChartTodayDecryptOption.series[0].data.length === 0 && showBarChartTodayDecrypt" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-else class="large-screen-chart-div" @mouseenter="clearDeptOrUser('todayDeptOrUser')" @mouseleave="startDeptOrUser('todayDeptOrUser')">
                <bar-chart
                  v-if="temp.barChartTodayGroupOption.series[0].data.length > 0 && showBarChartTodayGroup"
                  :chart-data="temp.barChartTodayGroupData"
                  :chart-option="temp.barChartTodayGroupOption"
                  :style="{ height: chartDivHeight}"
                />
                <bar-chart
                  v-if="temp.barChartTodayDecryptOption.series[0].data.length > 0 && showBarChartTodayDecrypt"
                  :chart-data="temp.barChartTodayDecryptTopData"
                  :chart-option="temp.barChartTodayDecryptOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
            <!--按文件类型统计解密分布/按操作类型统计解密分布-->
            <div v-if="col.feature==='fileDecCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div v-if="showPieFileDecDistribution" class="large-screen-title">{{ $t('pages.statisticsDecDistributionFileType') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleDisplayDecDistribution()"></i></div>
                <div v-if="showPieOperateDecDistribution" class="large-screen-title">{{ $t('pages.statisticsDecDistributionOperateType') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleDisplayDecDistribution()"></i></div>
              </div>
              <div v-if="(temp.chartsFileTypeData.length === 0 && showPieFileDecDistribution) || (temp.chartsOperateData.length === 0 && showPieOperateDecDistribution)" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-if="temp.chartsFileTypeData.length !== 0 && showPieFileDecDistribution" class="large-screen-chart-div" @mouseenter="clearDeptOrUser('distribution')" @mouseleave="startDeptOrUser('distribution')">
                <pie-chart
                  v-if="showPieFileDecDistribution"
                  :chart-data="temp.chartsFileTypeData"
                  :chart-option="temp.chartFileTypeOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
              <div v-if="temp.chartsOperateData.length !== 0 && showPieOperateDecDistribution" class="large-screen-chart-div" @mouseenter="clearDeptOrUser('distribution')" @mouseleave="startDeptOrUser('distribution')">
                <pie-chart
                  v-if="showPieOperateDecDistribution"
                  :chart-data="temp.chartsOperateData"
                  :chart-option="temp.chartFileTypeOption"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
            <!--加解密趋势-->
            <div v-if="col.feature==='encTrendCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title"><span class="today-color">{{ $t('pages.today') }}</span>{{ $t('pages.encAndDecTrend') }}</div>
              </div>
              <div v-if="temp.lineChartTodayTimeOption.series[0].data.length === 0 && temp.lineChartTodayTimeOption.series[1].data.length === 0" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-else class="large-screen-chart-div">
                <line-chart
                  :chart-data="temp.lineChartTodayTimeData"
                  :chart-option="temp.lineChartTodayTimeOption"
                  :x-axis-name="todayTimeXName"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
            <!--解密Top10部门/解密Top10操作员-->
            <div v-if="col.feature==='decDeptUserCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div v-if="showBarChartTodayGroupDec" class="large-screen-title"><span class="today-color">{{ $t('pages.today') }}</span>{{ $t('pages.decryptTop10Departments') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleTodayDeptOrUserDec()"></i></div>
                <div v-if="showBarChartTodayDecryptDec" class="large-screen-title"><span class="today-color">{{ $t('pages.today') }}</span>{{ $t('pages.decryptTop10User') }}<i class="el-icon-sort large-icon" :title="$t('pages.toggle')" @click="toggleTodayDeptOrUserDec()"></i></div>
              </div>
              <div v-if="temp.barChartTodayGroupOptionDec.series[0].data.length === 0 && showBarChartTodayGroupDec" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-if="temp.barChartTodayDecryptOptionDec.series[0].data.length === 0 && showBarChartTodayDecryptDec" class="large-screen-chart-div">
                <div class="no-data" :style="{ height: chartDivHeight}">{{ $t('text.noData') }}...</div>
              </div>
              <div v-else class="large-screen-chart-div" @mouseenter="clearDeptOrUser('todayDeptOrUserDec')" @mouseleave="startDeptOrUser('todayDeptOrUserDec')">
                <bar-chart
                  v-if="temp.barChartTodayGroupOptionDec.series[0].data.length > 0 && showBarChartTodayGroupDec"
                  :chart-data="temp.barChartTodayGroupDataDec"
                  :chart-option="temp.barChartTodayGroupOptionDec"
                  :style="{ height: chartDivHeight}"
                />
                <bar-chart
                  v-if="temp.barChartTodayDecryptOptionDec.series[0].data.length > 0 && showBarChartTodayDecryptDec"
                  :chart-data="temp.barChartTodayDecryptTopDataDec"
                  :chart-option="temp.barChartTodayDecryptOptionDec"
                  :style="{ height: chartDivHeight}"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--布局配置弹框-->
    <dialog-layout-config ref="dialogLayoutConfig" :original-layout="originalLayout" :layout="orLayout" :parameter-option="parameterOption"/>
  </div>
</template>

<script>
import Query from '@/views/report/largeScreen/common/query'
import LineChart from '@/views/report/largeScreen/common/LineChart'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import PieChart from '@/views/report/largeScreen/common/PieChart'
import scatterCommon from '@/components/ECharts/scatterCommon'
import { displayPanelGetDataSecurityPanel, displayPanelGetDataSecurityPanelNoOplog, displayPanelGetEncDecRecordRecord, displayPanelGetTodayEncDesCount } from '@/api/report/baseReport/largeScreen/largeScreen'
import { getCurrentTime, getSetIntervalTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
import { getValueByCondition } from '@/api/user';
import DialogLayoutConfig from '@/views/report/largeScreen/common/DialogLayoutConfig';
import TopIcoButton from '../common/TopIcoButton';
import axios from 'axios' // 中断文件上传请求时使用
export default {
  name: 'LargeScreen',
  components: { Query, LineChart, BarChart, PieChart, scatterCommon, vueSeamlessScroll, DialogLayoutConfig, TopIcoButton },
  props: {
    // 大屏轮播是否首次加载
    isFirst: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      todayTimeXName: this.$t('pages.time'),
      // 大屏配置中报表日期，传值给query组件
      dataTemp: {
        encOrDecDimBaseType: 1,
        encOrDecDimValue: '',
        dateCycleLargeScreen: 0
      },
      // 是否首次加载
      isMounted: false,
      // 接口参数(自定义布局)
      parameterOption: {
        menuCode: 'IA7',
        tableKey: 'largeScreenEncOrDecFileAnalysisLargeScreen'
      },
      // 深拷贝originalLayout,否则弹框修改时，会影响父组件layout数据
      orLayout: [],
      // 页面需要展示的布局
      layout: [],
      // 动态配置布局
      // 注：flag代表的布局：['layout1-1', 'layout1-2', 'layout2-1', 'layout1-1-1', 'layout1-2-1', 'layout1', 'layout1-3', 'layout3-1']
      // 如flag:3为索引3，既平均分配的三列显示：layout1-1-1
      // feature:每一个展示项有一个特有的feature名称，不能重复，用于页面动态显示时判断
      // 初始化布局，layout会随着接口返回发生变化，所以初始化的数据要一直保留，不被修改
      originalLayout: [
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('pages.foundationForm_Msg44'), type: 'chart-pie', feature: 'approveApplyCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg45'), type: 'chart-data', feature: 'dataItemCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg46'), type: 'chart-log', feature: 'encDecLogCondition' }
          ]
        },
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('pages.foundationForm_Msg47'), type: 'chart-cross-bar', feature: 'encDecDeptUserCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg48'), type: 'chart-splattering', feature: 'encDecDistributionCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg49'), type: 'chart-bar', feature: 'encDeptUserCondition' }
          ]
        },
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('pages.foundationForm_Msg50'), type: 'chart-pie', feature: 'fileDecCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg51'), type: 'chart-line', feature: 'encTrendCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg52'), type: 'chart-bar', feature: 'decDeptUserCondition' }
          ]
        }
      ],
      // 一个函数执行完在执行另一个函数
      dataLoadPromise: undefined,
      hasChanged: {},
      // 控制(解密审批状态分布)和(提交申请Top10操作员)定时切换显示
      showPieStatus: true,
      intervalIdStatusOrApply: null,
      // 控制(按部门统计解密情况Top10)和(解密Top10操作员)定时切换显示
      showBarChartGroup: true,
      intervalIdDeptOrUser: null,
      // 控制(按文件类型统计解密分布)和(按操作类型统计解密分布)定时切换显示
      showPieFileDecDistribution: true,
      intervalIdDecDistribution: null,
      // 控制(按日期统计解密分布)和(按日期加密趋势)定时切换显示
      showScatterDecDistribution: true,
      intervalIdDecDistributionTrend: null,
      // 控制(今日加密Top10部门)和(今日加密Top10操作员)定时切换显示
      showBarChartTodayGroup: true,
      intervalIdDeptOrUserToday: null,
      // 控制(今日解密Top10部门)和(今日解密Top10操作员)定时切换显示
      showBarChartTodayGroupDec: true,
      intervalIdDeptOrUserTodayDec: null,
      // 切换间隔时间，8秒
      intervalTime: 8000,
      // 今天记录，1分钟
      todayLogIntervalTime: 60000,
      // 今天数据，1分钟
      todayIntervalTime: 60000,
      // 查询数据，3小时
      searchIntervalTime: 10800000,
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 中断请求
      sourceGetLargeScreenConfig: null,
      sourceGetLayout: null,
      sourceGetLargeScreenData: null,
      sourceGetDayRecord: null,
      sourceGetTodayData: null,
      // 定时器
      currentTime: undefined,
      timer: undefined,
      timerSearch: undefined,
      timerLog: undefined,
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight: '19vh',
      middleOutHeight: '11.2vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      // 查询条件
      tempQuery: {},
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: getCurrentTime(),
        currentTime: '',
        middleMsg: [
          { currentName: this.$t('pages.historicalEncQuantity'), currentNum: 0, img: 'lock', change: false },
          { currentName: this.$t('pages.numberHistoricalDecryptions'), currentNum: 0, img: 'unLock', change: false },
          { currentName: this.$t('pages.encryptionNumber'), currentNum: 0, img: 'lock', change: false },
          { currentName: this.$t('pages.decryptionNumber'), currentNum: 0, img: 'unLock', change: false }
        ],
        // 解密审批状态分布(饼图)
        chartsApprovalData: [],
        chartApprovalOption: {
          series: [
            {
              radius: ['30%', '50%']
            }
          ]
        },
        // 按部门统计解密情况Top10(柱状图)
        barChartGroupData: [],
        barChartGroupOption: {
          yAxis: {
            type: 'category',
            data: []
          },
          xAxis: {
            name: '',
            type: 'value'
          },
          series: [
            {
              data: []
            }
          ]
        },
        // 提交申请Top10操作员(柱状图)
        barChartApplyTopData: [],
        barChartApplyTopOption: {
          yAxis: {
            type: 'category',
            data: []
          },
          xAxis: {
            name: '',
            type: 'value'
          },
          series: [
            {
              name: this.$t('pages.decryptionApproval'),
              type: 'bar',
              stack: 'total',
              data: [],
              barMaxWidth: '30%',
              itemStyle: {
                color: {
                  type: 'linear', // 线性渐变
                  colorStops: [
                    { offset: 1, color: '#18CFD9' },
                    { offset: 0, color: '#59E6B4' }
                  ]
                },
                emphasis: {
                  opacity: 1
                }
              }
            },
            {
              name: this.$t('pages.offlineApproval'),
              type: 'bar',
              stack: 'total',
              data: [],
              barMaxWidth: '30%',
              itemStyle: {
                color: {
                  type: 'linear', // 线性渐变
                  colorStops: [
                    { offset: 1, color: '#DC5D78' },
                    { offset: 0, color: '#E6C55A' }
                  ]
                },
                emphasis: {
                  opacity: 1
                }
              }
            },
            {
              name: this.$t('pages.directOutApproval'),
              type: 'bar',
              stack: 'total',
              data: [],
              barMaxWidth: '30%',
              itemStyle: {
                color: {
                  type: 'linear', // 线性渐变
                  colorStops: [
                    { offset: 1, color: '#87430D' },
                    { offset: 0, color: '#E7BC27' }
                  ]
                },
                emphasis: {
                  opacity: 1
                }
              }
            }
          ]
        },
        // 按日期统计解密分布(散点图)
        chartDecryptionDistributionData: [],
        chartDecryptionDistributionOption: {
          title: {
            text: '',
            left: 'center',
            subtext: ''
          },
          grid: {
            left: '2%',    // 距离左侧的距离
            right: '2%',   // 距离右侧的距离
            top: '8%',    // 距离顶部的距离
            bottom: '8%'  // 距离底部的距离
          },
          toolbox: {
            show: false
          },
          // color: ['#45C2E0', '#237fca', '#FFC851', '#5A5476', '#1869A0', '#FF9393'],
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: []
        },
        // 今日加解密记录
        encOrdecryptionRecord: [],
        // 解密Top10操作员(柱状图)
        barChartDecryptTopData: [],
        barChartDecryptOption: {
          xAxis: {
            data: []
          },
          series: [
            {
              data: []
            }
          ]
        },
        // 按日期加密趋势(折线图)
        lineChartTimeData: [],
        lineTimeOption: {
          legend: {
            data: [this.$t('pages.loadTypeOptions2'), this.$t('pages.loadTypeOptions3')]
          },
          xAxis: {
            data: []
          },
          series: [
            {
              name: this.$t('pages.loadTypeOptions2'),
              data: [],
              type: 'line'
            },
            {
              name: this.$t('pages.loadTypeOptions3'),
              data: [],
              type: 'line'
            }
          ]
        },
        // 今日加解密趋势(折线图)
        lineChartTodayTimeData: [],
        lineChartTodayTimeOption: {
          legend: {
            data: [this.$t('pages.loadTypeOptions2'), this.$t('pages.loadTypeOptions3')]
          },
          xAxis: {
            data: []
          },
          series: [
            {
              name: this.$t('pages.loadTypeOptions2'),
              data: [],
              type: 'line'
            },
            {
              name: this.$t('pages.loadTypeOptions3'),
              data: [],
              type: 'line'
            }
          ]
        },
        // 按文件类型统计解密分布(饼图)
        chartsFileTypeData: [],
        chartFileTypeOption: {
          series: [
            {
              radius: [20, 45],
              center: ['50%', '50%'],
              itemStyle: {
                borderRadius: 8
              }
            }
          ]
        },
        // 按操作类型统计解密分布(饼图)
        chartsOperateData: [],
        // 今日加密Top10部门(柱状图)
        barChartTodayGroupData: [],
        barChartTodayGroupOption: {
          yAxis: {
            type: 'category',
            data: []
          },
          xAxis: {
            name: '',
            type: 'value'
          },
          series: [
            {
              data: []
            }
          ]
        },
        // 今日解密Top10部门(柱状图)
        barChartTodayGroupDataDec: [],
        barChartTodayGroupOptionDec: {
          yAxis: {
            type: 'category',
            data: []
          },
          xAxis: {
            name: '',
            type: 'value'
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 今日加密Top10操作员(柱状图)
        barChartTodayDecryptTopData: [],
        barChartTodayDecryptOption: {
          xAxis: {
            data: []
          },
          series: [
            {
              data: []
            }
          ]
        },
        // 今日解密Top10操作员(柱状图)
        barChartTodayDecryptTopDataDec: [],
        barChartTodayDecryptOptionDec: {
          xAxis: {
            data: []
          },
          series: [
            {
              data: []
            }
          ]
        }
      }
    }
  },
  computed: {
    showBarApplyUser() {
      return !this.showPieStatus;
    },
    showBarChartDecrypt() {
      return !this.showBarChartGroup;
    },
    showPieOperateDecDistribution() {
      return !this.showPieFileDecDistribution;
    },
    showLineCryptoTrend() {
      return !this.showScatterDecDistribution;
    },
    showBarChartTodayDecrypt() {
      return !this.showBarChartTodayGroup;
    },
    showBarChartTodayDecryptDec() {
      return !this.showBarChartTodayGroupDec;
    }
  },
  watch: {
    tempQuery: {
      deep: true,
      handler(newVal, oldVal) {
        this.tempQuery = newVal
      }
    }
  },
  created() {

  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    if (this.isMounted === false) {
      this.startIntervalFun()
      // 系统当前时间
      this.getTimes()
    }
  },
  mounted() {
    // 挂载后
    this.isMounted = true
    this.resetTemp()
    this.getLayout()
    this.temp.currentTime = getCurrentTime()
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    window.addEventListener('resize', this.handleFullScreenChange)
    // 系统当前时间
    this.getTimes()
    this.dataLoadPromise = Promise.all([
      this.getLargeScreenConfig()
    ]).then(() => {
      // 定时器切换
      this.statusOrApplyToggle()
      this.deptOrUserToggle()
      this.decDistributionToggle()
      this.distributionTrendToggle()
      this.todayDeptOrUserToggle()
      this.todayDeptOrUserToggleDec()
      this.startIntervalFun()
      // 获取今日加解密记录
      this.getDayRecord()
      // 获取除今日加解密记录以外的今日数据，1分钟刷新一次
      this.getTodayData()
      // 获取右上方查询条件查出来的信息,3小时刷新一次（首次加载时，报表日期的获取可能需要一点点时长）
      setTimeout(() => {
        this.getLargeScreenData()
      }, 1000)
    })
    setTimeout(() => {
      this.isMounted = false
    }, 1000)
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(window.currentTime);
    window.currentTime = null
    this.stopIntervalFun()
    this.stopRequest()
    clearInterval(window.intervalIdStatusOrApply);
    window.intervalIdStatusOrApply = null
    clearInterval(window.intervalIdDeptOrUser);
    window.intervalIdDeptOrUser = null
    clearInterval(window.intervalIdDecDistribution);
    window.intervalIdDecDistribution = null
    clearInterval(window.intervalIdDecDistributionTrend);
    window.intervalIdDecDistributionTrend = null
    clearInterval(window.intervalIdDeptOrUserToday);
    window.intervalIdDeptOrUserToday = null
    clearInterval(window.intervalIdDeptOrUserTodayDec);
    window.intervalIdDeptOrUserTodayDec = null
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    clearInterval(window.currentTime);
    window.currentTime = null
    this.stopIntervalFun()
    this.stopRequest()
  },
  methods: {
    /**
     * 中断http请求（组件关闭或停用时，如果http接口未请求完成，页面切换时中断请求调用）
     * */
    stopRequest() {
      // 中断大屏配置接口获取
      if (this.sourceGetLargeScreenConfig) { this.sourceGetLargeScreenConfig.cancel() }
      // 中断大屏布局接口获取
      if (this.sourceGetLayout) { this.sourceGetLayout.cancel() }
      // 中断右上方查询条件查出来的信息接口获取
      if (this.sourceGetLargeScreenData) { this.sourceGetLargeScreenData.cancel() }
      // 中断今日加解密记录列表接口获取
      if (this.sourceGetDayRecord) { this.sourceGetDayRecord.cancel() }
      // 中断除今日加解密记录以外的今日数据接口获取
      if (this.sourceGetTodayData) { this.sourceGetTodayData.cancel() }
    },
    /**
     * 定时器启动
     * */
    startIntervalFun() {
      if (window.timerSearch) {
        clearInterval(window.timerSearch)
      }
      window.timerSearch = setInterval(() => { this.getLargeScreenData() }, this.searchIntervalTime)
      if (window.timer) {
        clearInterval(window.timer)
      }
      window.timer = setInterval(() => { this.getTodayData() }, this.todayIntervalTime)
      if (window.timerLog) {
        clearInterval(window.timerLog)
      }
      window.timerLog = setInterval(() => { this.getDayRecord() }, this.todayLogIntervalTime)
    },
    /**
     * 定时器停止
     * */
    stopIntervalFun() {
      clearInterval(window.timer)
      clearInterval(window.timerSearch)
      clearInterval(window.timerLog)
      window.timer = null
      window.timerSearch = null
      window.timerLog = null
    },
    /**
     * 获取layout布局（动态显示页面布局）
     * */
    getLayout(option) {
      const userId = this.$store.getters.userId
      this.sourceGetLayout = axios.CancelToken.source()
      let cacheToken = this.sourceGetLayout.token
      if (option === 'config') {
        cacheToken = ''
      }
      getValueByCondition({ sysUserId: userId, menuCode: this.parameterOption.menuCode, tableKey: this.parameterOption.tableKey, type: 4 }, cacheToken).then(res => {
        if (res.data) {
          // 如果存在data,直接显示接口返回，否则为前端页面设置的默认值
          // console.log('加解密', JSON.parse(res.data.value))
          this.layout = JSON.parse(res.data.value)
          this.orLayout = JSON.parse(res.data.value)
        } else {
          // 初始化时先赋原来的初始值
          this.layout = JSON.parse(JSON.stringify(this.originalLayout));
          this.orLayout = JSON.parse(JSON.stringify(this.originalLayout))
        }
      })
    },
    /**
     * 打开布局配置弹框(子组件调用该方法)
     */
    configBtn() {
      this.getLayout()
      this.$refs.dialogLayoutConfig.show()
    },
    /**
     * 获取大屏配置中的配置信息（动态设置定时器时长）
     * */
    getLargeScreenConfig() {
      const userId = this.$store.getters.userId
      this.sourceGetLargeScreenConfig = axios.CancelToken.source()
      const cacheToken = this.sourceGetLargeScreenConfig.token
      return getValueByCondition({ sysUserId: userId, menuCode: '1001', tableKey: 'largeScreenConfig', type: 4 }, cacheToken).then(res => {
        // console.log('res.data大屏配置', JSON.parse(res.data.value))
        if (res.data) {
          const response = JSON.parse(res.data.value)
          // 报表日期(兼容旧数据)
          if (response.encOrDecDimBaseType && response.encOrDecDimValue !== '') {
            this.dataTemp.encOrDecDimBaseType = response.encOrDecDimBaseType
            this.dataTemp.encOrDecDimValue = response.encOrDecDimValue
          }
          // 报表日期(兼容旧数据)
          if (response.encOrDecDimDataCycle) {
            this.dataTemp.dateCycleLargeScreen = response.encOrDecDimDataCycle
          }
          // 切换间隔时间，单位毫秒
          if (response.encOrDecChartSwitchValue) {
            this.intervalTime = getSetIntervalTime(response.encOrDecChartSwitchValue, response.encOrDecChartSwitchSelect)
            this.todayLogIntervalTime = getSetIntervalTime(response.encOrDecTodayLogValue, response.encOrDecTodayLogSelect)
            this.todayIntervalTime = getSetIntervalTime(response.encOrDecTodayValue, response.encOrDecTodaySelect)
            this.searchIntervalTime = getSetIntervalTime(response.encOrDecDateQueryValue, response.encOrDecDateQuerySelect)
          }
        }
      })
    },
    /**
     * 获取除今日加解密记录以外的今日数据
     * */
    getTodayData() {
      this.sourceGetTodayData = axios.CancelToken.source()
      const cacheToken = this.sourceGetTodayData.token
      displayPanelGetTodayEncDesCount(cacheToken).then(res => {
        // 最后更新时间
        this.temp.lastUpdateTime = getCurrentTime()
        // console.log('res', JSON.parse(JSON.stringify(res)))
        const responseItem = res.data.itemValueMap
        const responseChart = res.data.chartDataObjMap
        // 处理今日数据的变化
        if (this.temp.middleMsg[2].currentNum !== responseItem.encCount) {
          this.temp.middleMsg[2].currentNum = responseItem.encCount
          this.temp.middleMsg[2].change = true
          setTimeout(() => {
            this.temp.middleMsg[2].change = false
          }, 3000);
        }
        if (this.temp.middleMsg[3].currentNum !== responseItem.desCount) {
          this.temp.middleMsg[3].currentNum = responseItem.desCount
          this.temp.middleMsg[3].change = true
          setTimeout(() => {
            this.temp.middleMsg[3].change = false
          }, 3000);
        }
        // 今日加解密趋势(折线图)
        if (JSON.stringify(responseChart.todayTimeChart) !== '{}') {
          const xData = responseChart.todayTimeChart.xaxisData
          this.temp.lineChartTodayTimeOption.xAxis.data = xData.map(item => item + ':00')
          this.temp.lineChartTodayTimeOption.series[0].data = responseChart.todayTimeChart.seriesData[0]
          this.temp.lineChartTodayTimeOption.series[1].data = responseChart.todayTimeChart.seriesData[1]
        }
        // 今日加密Top10部门（柱状图横向）
        if (responseChart.groupEncChart.chartData.length > 0) {
          const data = this.barChartProcessing(responseChart.groupEncChart.chartData)
          this.temp.barChartTodayGroupData = data.data
          this.temp.barChartTodayGroupOption.yAxis.data = data.xData
          this.temp.barChartTodayGroupOption.series[0].data = data.data
          this.temp.barChartTodayGroupOption.yAxis.axisLabel = data.axisLabel
        }
        // 今日加密Top10操作员（柱状图纵向）
        if (responseChart.userEncChart.chartData.length > 0) {
          const data = this.barChartProcessing(responseChart.userEncChart.chartData)
          this.temp.barChartTodayDecryptOption.xAxis.data = data.xData
          this.temp.barChartTodayDecryptOption.series[0].data = data.data
          this.temp.barChartTodayDecryptOption.xAxis.axisLabel = data.axisLabel
        }
        // 今日解密Top10部门（柱状图横向）
        if (responseChart.groupDesChart.chartData.length > 0) {
          const data = this.barChartProcessing(responseChart.groupDesChart.chartData)
          this.temp.barChartTodayGroupDataDec = data.data
          this.temp.barChartTodayGroupOptionDec.yAxis.data = data.xData
          this.temp.barChartTodayGroupOptionDec.series[0].data = data.data
          this.temp.barChartTodayGroupOptionDec.yAxis.axisLabel = data.axisLabel
        }
        // 今日解密Top10操作员（柱状图纵向）
        if (responseChart.userDesChart.chartData.length > 0) {
          const data = this.barChartProcessing(responseChart.userDesChart.chartData)
          this.temp.barChartTodayDecryptOptionDec.xAxis.data = data.xData
          this.temp.barChartTodayDecryptOptionDec.series[0].data = data.data
          this.temp.barChartTodayDecryptOptionDec.xAxis.axisLabel = data.axisLabel
        }
      })
    },
    /**
     * 处理今日加解密柱状图
     * @param option 接口返回数据
     * */
    barChartProcessing(option) {
      const data = option
      const array = []
      array.xData = this.getDeptName(data.map(obj => obj.name))
      array.data = data.map(obj => obj.value)
      array.axisLabel = {
        formatter: function(value) {
          // 如果文本长度超过8个字，则返回前八个字加上省略号
          if (value.length > 8) {
            return value.slice(0, 8) + '...';
          }
          // 如果文本长度不超过8个字，则直接返回原文本
          return value;
        }
      }
      // console.log('array', array)
      return array
    },
    // 鼠标移入清除定时器
    clearDeptOrUser(option) {
      if (option === 'statusApply') {
        clearInterval(window.intervalIdStatusOrApply);
      } if (option === 'deptOrUser') {
        clearInterval(window.intervalIdDeptOrUser);
      } if (option === 'distribution') {
        clearInterval(window.intervalIdDecDistribution);
      } if (option === 'distributionTrend') {
        clearInterval(window.intervalIdDecDistributionTrend);
      } if (option === 'todayDeptOrUser') {
        clearInterval(window.intervalIdDeptOrUserToday);
      } if (option === 'todayDeptOrUserDec') {
        clearInterval(window.intervalIdDeptOrUserTodayDec);
      }
    },
    // 鼠标移出调用定时器
    startDeptOrUser(option) {
      if (option === 'statusApply') {
        this.statusOrApplyToggle()
      } if (option === 'deptOrUser') {
        this.deptOrUserToggle()
      } if (option === 'distribution') {
        this.decDistributionToggle()
      } if (option === 'distributionTrend') {
        this.distributionTrendToggle()
      } if (option === 'todayDeptOrUser') {
        this.todayDeptOrUserToggle()
      } if (option === 'todayDeptOrUserDec') {
        this.todayDeptOrUserToggleDec()
      }
    },
    // 设置（按部门统计解密情况Top10）和（解密Top10操作员）定时器
    statusOrApplyToggle() {
      if (window.intervalIdStatusOrApply) {
        clearInterval(window.intervalIdStatusOrApply);
      }
      window.intervalIdStatusOrApply = setInterval(this.toggleStatusOrApply, this.intervalTime);
    },
    // 设置（按部门统计解密情况Top10）和（解密Top10操作员）切换显示
    toggleStatusOrApply() {
      this.showPieStatus = !this.showPieStatus;
    },
    // 设置（按部门统计解密情况Top10）和（解密Top10操作员）定时器
    deptOrUserToggle() {
      if (window.intervalIdDeptOrUser) {
        clearInterval(window.intervalIdDeptOrUser);
      }
      window.intervalIdDeptOrUser = setInterval(this.toggleDisplay, this.intervalTime);
    },
    // 设置（按部门统计解密情况Top10）和（解密Top10操作员）切换显示
    toggleDisplay() {
      this.showBarChartGroup = !this.showBarChartGroup;
    },
    // 设置（按部门统计解密情况Top10）和（解密Top10操作员）定时器
    decDistributionToggle() {
      if (window.intervalIdDecDistribution) {
        clearInterval(window.intervalIdDecDistribution);
      }
      window.intervalIdDecDistribution = setInterval(this.toggleDisplayDecDistribution, this.intervalTime);
    },
    // 设置（按部门统计解密情况Top10）和（解密Top10操作员）切换显示
    toggleDisplayDecDistribution() {
      this.showPieFileDecDistribution = !this.showPieFileDecDistribution;
    },
    // 设置（按日期统计解密分布）和（按日期加密趋势）定时器
    distributionTrendToggle() {
      if (window.intervalIdDecDistributionTrend) {
        clearInterval(window.intervalIdDecDistributionTrend);
      }
      window.intervalIdDecDistributionTrend = setInterval(this.toggleDistributionTrend, this.intervalTime);
    },
    // 设置（按日期统计解密分布）和（按日期加密趋势）切换显示
    toggleDistributionTrend() {
      this.showScatterDecDistribution = !this.showScatterDecDistribution;
    },
    // 设置（今日加密Top10部门）和（今日加密Top10操作员）定时器
    todayDeptOrUserToggle() {
      if (window.intervalIdDeptOrUserToday) {
        clearInterval(window.intervalIdDeptOrUserToday);
      }
      window.intervalIdDeptOrUserToday = setInterval(this.toggleTodayDeptOrUser, this.intervalTime);
    },
    // 设置（今日加密Top10部门）和（今日加密Top10操作员）切换显示
    toggleTodayDeptOrUser() {
      this.showBarChartTodayGroup = !this.showBarChartTodayGroup;
    },
    // 设置（今日解密Top10部门）和（今日解密Top10操作员）定时器
    todayDeptOrUserToggleDec() {
      if (window.intervalIdDeptOrUserTodayDec) {
        clearInterval(window.intervalIdDeptOrUserTodayDec);
      }
      window.intervalIdDeptOrUserTodayDec = setInterval(this.toggleTodayDeptOrUserDec, this.intervalTime);
    },
    // 设置（今日解密Top10部门）和（今日解密Top10操作员）切换显示
    toggleTodayDeptOrUserDec() {
      this.showBarChartTodayGroupDec = !this.showBarChartTodayGroupDec;
    },
    /**
     * 系统当前时间，实时获取，实时刷新
     * */
    getTimes() {
      if (this.currentTime) {
        clearInterval(this.currentTime)
      }
      this.currentTime = setInterval(this.getSystemTime, 1000)
    },
    /**
     * 系统当前时间，赋值
     * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      // 报表日期默认值设置
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      this.currentTime = year.toString() + month
      this.dataTemp.encOrDecDimValue = this.currentTime
    },
    /**
     * 获取今日加解密记录列表数据
     * */
    getDayRecord() {
      this.sourceGetDayRecord = axios.CancelToken.source()
      const cacheToken = this.sourceGetDayRecord.token
      displayPanelGetEncDecRecordRecord(cacheToken).then(res => {
        // 最后更新时间
        this.temp.lastUpdateTime = getCurrentTime()
        const list = res.data
        // console.log('res记录', JSON.parse(JSON.stringify(res)))
        if (list.length > 0) {
          list.forEach((item, index) => {
            let typeName
            if (item.type === 1) {
              typeName = this.$t('pages.batchEncryption')
            } else if (item.type === 2) {
              typeName = this.$t('pages.batchDecryption')
            } else if (item.type === 3) {
              typeName = this.$t('pages.globalEncryption')
            } else if (item.type === 4) {
              typeName = this.$t('pages.fullDecryption')
            } else if (item.type === 5) {
              typeName = this.$t('pages.approvalDecryption')
            } else if (item.type === 6) {
              typeName = this.$t('pages.approvalEncryption')
            } else if (item.type === 7) {
              typeName = this.$t('pages.fileEncryption')
            } else if (item.type === 8) {
              typeName = this.$t('pages.diskScanSensitiveEncode')
            } else {
              typeName = ''
            }
            item.typeName = typeName
          })
        }
        // 对比新旧数据，如果有变化，添加标记isNew: true，否则isNew: false。用于区分新旧数据显示
        const datas = list
        const oldArray = this.temp.encOrdecryptionRecord
        const newArray = datas
        const markNewObjects = (newArray, oldArray) => {
          return newArray.map(newObj => {
            // 数据从无到有的时候不变红色，只有新旧数据都存在，且存在新的数据时，才变红色
            let oldObj = {}
            if (oldArray.length > 0) {
              oldObj = oldArray.find(oldObj => (oldObj.createTime === newObj.createTime && oldObj.failSum === newObj.failSum && oldObj.groupId === newObj.groupId && oldObj.objectId === newObj.objectId && oldObj.sucSum === newObj.sucSum && oldObj.totolSum === newObj.totolSum && oldObj.type === newObj.type))
            }
            return oldObj ? Object.assign({}, newObj, { isNew: false }) : Object.assign({}, newObj, { isNew: true });
          });
        };
        this.temp.encOrdecryptionRecord = markNewObjects(newArray, oldArray);
        // console.log('list记录', JSON.parse(JSON.stringify(this.temp.encOrdecryptionRecord)))
      })
    },
    /**
     * 获取部门名称，截取掉部门名称中的id
     * @param deptArray 接口返回的部门数组
     * 截取第一个_后面的内容为部门名称
     * */
    getDeptName(deptArray) {
      return deptArray.map(str => str.split('_').slice(1).join('_'));
    },
    /**
       * 获取接口参数（子组件点击查询或监听到query发生变化时获取）
       * @param data 参数
       * */
    thisQuery(data) {
      this.tempQuery = data
    },
    /**
     * 获取右上方查询条件查出来的信息
     * */
    getLargeScreenData() {
      this.$nextTick(() => {
        // console.log('this.tempQuery大屏参数', JSON.parse(JSON.stringify(this.tempQuery)))
        // 根据是否首次加载，调用有日志和没日志的接口
        let apiFun = displayPanelGetDataSecurityPanelNoOplog
        if (this.isMounted && this.isFirst) {
          apiFun = displayPanelGetDataSecurityPanel
        }
        this.sourceGetLargeScreenData = axios.CancelToken.source()
        const cacheToken = this.sourceGetLargeScreenData.token
        apiFun(this.tempQuery, cacheToken).then(res => {
          // console.log('res', JSON.parse(JSON.stringify(res.data)))
          const responseItem = res.data.itemValueMap
          const responseChart = res.data.chartDataObjMap
          // 解密审批状态分布（饼图）
          if (JSON.stringify(responseChart.desApprovalPieChartData) !== '{}') {
            this.temp.chartsApprovalData = responseChart.desApprovalPieChartData.chartData
          } else {
            this.temp.chartsApprovalData = []
          }
          // 中间小方块
          this.temp.middleMsg[0].currentNum = responseItem.encCount
          this.temp.middleMsg[1].currentNum = responseItem.desCount
          // 按部门统计解密情况Top10（柱状图）
          if (JSON.stringify(responseChart.deptTopDesChartData) !== '{}') {
            this.temp.barChartGroupData = responseChart.deptTopDesChartData.seriesData
            this.temp.barChartGroupOption.yAxis.data = this.getDeptName(responseChart.deptTopDesChartData.xaxisData)
            this.temp.barChartGroupOption.series[0].data = responseChart.deptTopDesChartData.seriesData
            this.temp.barChartGroupOption.yAxis.axisLabel = {
              formatter: function(value) {
                // 如果文本长度超过8个字，则返回前八个字加上省略号
                if (value.length > 8) {
                  return value.slice(0, 8) + '...';
                }
                // 如果文本长度不超过8个字，则直接返回原文本
                return value;
              }
            }
          } else {
            this.temp.barChartGroupOption.yAxis.data = []
            this.temp.barChartGroupOption.series[0].data = []
          }
          // 按日期统计解密分布(散点图)
          if (JSON.stringify(responseChart.desTerminalDateCharData) !== '{}') {
            this.temp.chartDecryptionDistributionOption.xAxis.data = responseChart.desTerminalDateCharData.xaxisData
            this.temp.chartDecryptionDistributionOption.series = this.scatterProcessing(responseChart.desTerminalDateCharData.seriesData)
          } else {
            this.temp.chartDecryptionDistributionOption.xAxis.data = []
            this.temp.chartDecryptionDistributionOption.series = []
          }
          // 提交申请Top10操作员(柱状图)
          if (JSON.stringify(responseChart.approvalTopUser) !== '{}') {
            // console.log('responseChart.approvalTopUser.xaxisData', responseChart.approvalTopUser)
            this.temp.barChartApplyTopOption.yAxis.data = this.getDeptName(responseChart.approvalTopUser.xaxisData)
            this.temp.barChartApplyTopOption.yAxis.axisLabel = {
              formatter: function(value) {
                return value.length > 8 ? value.slice(0, 8) + '...' : value;
              }
            }
            this.applyTopProcessing(responseChart.approvalTopUser.seriesData)
          } else {
            this.temp.barChartApplyTopOption.yAxis.data = []
          }
          // 解密Top10操作员(柱状图)
          if (JSON.stringify(responseChart.userTopDesChartData) !== '{}') {
            this.temp.barChartDecryptOption.xAxis.data = this.getDeptName(responseChart.userTopDesChartData.xaxisData)
            this.temp.barChartDecryptOption.series[0].data = responseChart.userTopDesChartData.seriesData
            this.temp.barChartDecryptOption.xAxis.axisLabel = {
              formatter: function(value) {
                // 如果文本长度超过8个字，则返回前八个字加上省略号
                if (value.length > 6) {
                  return value.slice(0, 6) + '...';
                }
                // 如果文本长度不超过8个字，则直接返回原文本
                return value;
              }
            }
          } else {
            this.temp.barChartDecryptOption.xAxis.data = []
            this.temp.barChartDecryptOption.series[0].data = []
          }
          // 按日期加密趋势(折线图)
          if (JSON.stringify(responseChart.ensDesDateCharData) !== '{}') {
            this.temp.lineTimeOption.xAxis.data = responseChart.ensDesDateCharData.xaxisData
            this.temp.lineTimeOption.series[0].data = responseChart.ensDesDateCharData.seriesData[0]
            this.temp.lineTimeOption.series[1].data = responseChart.ensDesDateCharData.seriesData[1]
          } else {
            this.temp.lineTimeOption.xAxis.data = []
            this.temp.lineTimeOption.series[0].data = []
            this.temp.lineTimeOption.series[1].data = []
          }
          // 按文件类型统计解密分布(饼图)
          if (JSON.stringify(responseChart.fileTypePieCharData) !== '{}') {
            this.temp.chartsFileTypeData = responseChart.fileTypePieCharData.chartData
          } else {
            this.temp.chartsFileTypeData = []
          }
          // 按操作类型统计解密分布(饼图)
          if (JSON.stringify(responseChart.desTypePieCharData) !== '{}') {
            this.temp.chartsOperateData = responseChart.desTypePieCharData.chartData
          } else {
            this.temp.chartsOperateData = []
          }
        })
      })
    },
    /**
       * 提交申请Top10操作员,折线数据处理
       * */
    applyTopProcessing(seriesData) {
      const decryptArray = []
      const offlineArray = []
      const outsourceArray = []
      if (seriesData.length > 0) {
        seriesData.forEach(item => {
          decryptArray.push(item[0])
          offlineArray.push(item[1])
          outsourceArray.push(item[2])
        })
      }
      this.temp.barChartApplyTopOption.series[0].data = decryptArray
      this.temp.barChartApplyTopOption.series[1].data = offlineArray
      this.temp.barChartApplyTopOption.series[2].data = outsourceArray
    },
    /**
       * 散点图数据处理
       * 后台给的数据是【日期，日期】、【【数据】，【数据】】，每一个日期，对应二维数组中的一个数据
       * 散点图需要的数据是横向的，即：几个时间，一个data里几个数据，按数据看，要取每个二维数组的第一个数据组成一个data,第二个数据组成一个data,依次类推
       * @param seriesData 后台返回data数据
       * */
    scatterProcessing(seriesData) {
      // console.log('seriesData', JSON.parse(JSON.stringify(seriesData)))
      // 过滤部门名称中的id
      seriesData.map(innerArray => {
        return innerArray.map(item => {
          if (item.name) {
            item.name = item.name.split('_', 2)[1] || ''
          }
          return item;
        });
      })
      const series = []
      if (seriesData && seriesData.length > 0) {
        // 1、获取二维数组下的长度最长的数组的长度
        let maxLength = 0;
        for (let i = 0; i < seriesData.length; i++) {
          if (seriesData[i].length > maxLength) {
            maxLength = seriesData[i].length;
          }
        }
        // 2、遍历长度，获取散点图能显示的数据（即：如果最大长度为3，series下就有3个obj对象）
        for (let j = 0; j < maxLength; j++) {
          const obj = {
            symbolSize: 10,
            data: [],
            type: 'scatter',
            itemStyle: {
              color: '#15D6f4'
            }
          }
          // 3、获取每个二位数组下对应数据，innerArray[0]代表将所有数组的第一个对象拿出来组成一个新的对象数组
          var newArray = seriesData.map(function(innerArray) {
            return innerArray[j];
          });
            // 4、如果存在对象数组长度不一致，没有的会显示undefined，将undefined都替换成{ name: '', value: '' }
          obj.data = newArray.map(item => item || { name: '', value: '' })
          series.push(obj)
        }
      }
      // console.log('series', JSON.parse(JSON.stringify(series)))
      return series
    },
    /**
       * 判断是否全屏（监听全屏）
       * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
       * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
       * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      // console.log('this.isFullScreen', this.isFullScreen)
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight = '23.5vh'
        this.middleOutHeight = '13.2vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight = '19vh'
        this.middleOutHeight = '11.2vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>
<style>
</style>
<style lang='scss' scoped>
  .activeClassRed{
    color: #ff0000;
  }
  //中间特殊的小块
  .large-screen-middle-out{
    width: 49%;
    float: left;
    display: flex;
    .large-screen-middle-out-left{
      flex: 1;
      position: relative;
      box-shadow: 5px 0 5px -3px rgba(30, 144, 255, 0.5);
      div{
        width: 30px;
        height: 30px;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        img{
          width: 30px;
        }
      }
    }
    .large-screen-middle-out-right{
      flex: 3;
      font-size: 14px;
      div{
        height: 50%;
        display: flex; /* 使用flex布局 */
        align-items: center; /* 垂直居中 */
        justify-content: center; /* 水平居中 */
      }
      div:first-child{
        font-size: 16px;
        box-shadow: 0px 5px 5px -3px rgba(30, 144, 255, 0.5);
      }
      div:last-child{
        font-size: 30px;
      }
    }
  }
</style>
