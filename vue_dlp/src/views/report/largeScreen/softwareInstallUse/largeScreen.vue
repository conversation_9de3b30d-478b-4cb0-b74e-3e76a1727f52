<template>
  <div :style="{ height: appContainerHeight}" class="app-container largeScreen-report">
    <div class="table-container">
      <div class="large-screen-head">
        <div class="large-screen-head-title">{{ $t('route.softwareInstallationAndUsageData') }}</div>
        <!--全屏、布局配置-->
        <TopIcoButton/>
        <div class="large-screen-head-time">
          <div>{{ $t('pages.dataWasLastUpdated') }}：{{ temp.lastUpdateTime }}</div>
          <div>{{ $t('pages.currentSystemTime') }}：{{ temp.currentTime }}</div>
        </div>
        <div class="large-screen-head-search">
          <query ref="queryData" :data-temp="dataTemp" @thisQuery="thisQuery"/>
        </div>
      </div>
      <div class="large-screen-body">
        <el-row v-for="(row, rowIndex) in layout" :key="'row-' + rowIndex" class="large-screen-row" :gutter="row.gutter">
          <el-col v-for="(col, colIndex) in row.cols" :key="'col-' + colIndex" :xs="24" :sm="24" :lg="col.span">
            <!--小方块-->
            <div v-if="col.feature ==='dataItemCondition'" class="large-screen-border" style="border: 0;background-size: 0">
              <div v-for="(item, index) in temp.middleMsg" :key="index" :style="{ height: middleOutHeight}" class="large-screen-middle-out">
                <div class="large-screen-middle-name">{{ item.currentName }}</div>
                <div class="large-screen-middle-num">{{ item.currentNum }}</div>
              </div>
            </div>
            <!--数据安装与使用情况-->
            <div v-if="col.feature !=='dataItemCondition'" class="large-screen-border">
              <div class="large-screen-title-div">
                <div class="large-screen-title">{{ col.name }}</div>
              </div>
              <div v-if="col.feature ==='installationAndUseCondition'" class="large-screen-chart-div">
                <div class="div_risk_ranking_item  last_span_no_time" style="color: orange">
                  <!--<span></span>-->
                  <span>{{ $t('pages.itemRowData5') }}</span>
                  <span>{{ $t('pages.terminalGroup') }}</span>
                  <span>{{ $t('report.programUsageTimes') }}</span>
                  <span>{{ $t('report.programDuration') }}</span>
                </div>
                <div v-if="temp.installUseDatas.length === 0" :style="{ height: rankingDivHeight}" class="div_risk_ranking-chart-div no-data">
                  <div>{{ $t('text.noData') }}...</div>
                </div>
                <div v-else :style="{ height: rankingDivHeight}" class="div_risk_ranking">
                  <vue-seamless-scroll :data="temp.installUseDatas" :class-option="classOption">
                    <div v-for="(item, index) in temp.installUseDatas" :key="index" :class="item.isNew ? 'activeClassRed' : '  '" class="div_risk_ranking_item  last_span_no_time">
                      <span :title="item.terminalName">{{ item.terminalName }}</span>
                      <span :title="item.termGroupName">{{ item.termGroupName }}</span>
                      <span :title="item.appNum">{{ item.appNum }}</span>
                      <span :title="item.appRunTime ? item.appRunTime : 0">{{ item.appRunTime ? item.appRunTime : 0 }}</span>
                    </div>
                  </vue-seamless-scroll>
                </div>
              </div>
              <!--除列表和小方块以外的内容-->
              <div v-else class="large-screen-chart-div">
                <bar-chart
                  v-if="col.feature === 'softwareLicensingCondition' && temp.barChartSoftwareLicenseOption.series[0].data.length > 0"
                  :chart-data="temp.barChartSoftwareLicenseData"
                  :chart-option="temp.barChartSoftwareLicenseOption"
                  :style="{ height: chartDivHeight2}"
                />
                <div v-if="col.feature === 'softwareLicensingCondition' && temp.barChartSoftwareLicenseOption.series[0].data.length === 0" class="no-data" :style="{ height: chartDivHeight2}">{{ $t('text.noData') }}...</div>
                <graph-bubble
                  v-if="col.feature === 'installationSoftwareClassificationCondition' && temp.softwareCategory.length > 0"
                  :chart-datas="temp.softwareCategory"
                  :style="{ height: chartDivHeight2}"
                />
                <div v-if="col.feature === 'installationSoftwareClassificationCondition' && temp.softwareCategory.length === 0" class="no-data" :style="{ height: chartDivHeight2}">{{ $t('text.noData') }}...</div>
                <bar-chart
                  v-if="col.feature === 'limitRateTOP10Condition' && temp.barLeaveUnusedOption.series[0].data.length > 0"
                  :chart-data="temp.barLeaveUnusedData"
                  :chart-option="temp.barLeaveUnusedOption"
                  :style="{ height: chartDivHeight2}"
                />
                <div v-if="col.feature === 'limitRateTOP10Condition' && temp.barLeaveUnusedOption.series[0].data.length === 0" class="no-data" :style="{ height: chartDivHeight2}">{{ $t('text.noData') }}...</div>
                <pie-chart
                  v-if="col.feature === 'usageDistributionCondition' && temp.softwareUsageDistributionData.length > 0"
                  :chart-data="temp.softwareUsageDistributionData"
                  :chart-option="temp.softwareUsageDistributionOption"
                  :style="{ height: chartDivHeight2}"
                />
                <div v-if="col.feature === 'usageDistributionCondition' && temp.softwareUsageDistributionData.length === 0" class="no-data" :style="{ height: chartDivHeight2}">{{ $t('text.noData') }}...</div>
                <line-chart
                  v-if="col.feature === 'durationTOP10Condition' && temp.lineTimeOption.series.length > 0"
                  :chart-data="temp.lineTimeData"
                  :chart-option="temp.lineTimeOption"
                  :y-axis-name="tOP10ConditionyAxisName"
                  :style="{ height: chartDivHeight2}"
                />
                <div v-if="col.feature === 'durationTOP10Condition' && temp.lineTimeOption.series.length === 0" class="no-data" :style="{ height: chartDivHeight2}">{{ $t('text.noData') }}...</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--布局配置弹框-->
    <dialog-layout-config ref="dialogLayoutConfig" :original-layout="originalLayout" :layout="orLayout" :parameter-option="parameterOption"/>
  </div>
</template>

<script>
import Query from '@/views/report/largeScreen/common/query'
import LineChart from '@/views/report/largeScreen/common/LineChart'
import BarChart from '@/views/report/largeScreen/common/BarChart'
import PieChart from '@/views/report/largeScreen/common/PieChart'
import GraphBubble from '@/views/report/largeScreen/clientUsage/GraphBubble';
import { getCurrentTime, getSetIntervalTime } from '@/utils/reportLargeScreen';
import vueSeamlessScroll from 'vue-seamless-scroll'
import { displayPanelGetSoftwareReportHomePageData, displayPanelGetSoftwareReportHomePageDataNoOplog, displayPanelAppGetSoftwareReportToDayInfo } from '@/api/report/baseReport/largeScreen/largeScreen';
import { getValueByCondition } from '@/api/user';
import { formatSeconds } from '@/utils';
import DialogLayoutConfig from '@/views/report/largeScreen/common/DialogLayoutConfig';
import axios from 'axios';
import TopIcoButton from '../common/TopIcoButton';
export default {
  name: 'LargeScreen',
  components: { Query, PieChart, LineChart, BarChart, GraphBubble, vueSeamlessScroll, DialogLayoutConfig, TopIcoButton },
  props: {
    // 大屏轮播是否首次加载
    isFirst: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tOP10ConditionyAxisName: this.$t('components.time'),
      // 是否首次加载
      isMounted: false,
      // 大屏配置中报表日期，传值给query组件
      dataTemp: {
        encOrDecDimBaseType: 1,
        encOrDecDimValue: '',
        dateCycleLargeScreen: 0
      },
      // 接口参数(自定义布局)
      parameterOption: {
        menuCode: 'S13',
        tableKey: 'softwareInstallUse'
      },
      // 深拷贝originalLayout,否则弹框修改时，会影响父组件layout数据
      orLayout: [],
      // 页面需要展示的布局
      layout: [],
      // 动态配置布局
      // 注：flag代表的布局：['layout1-1', 'layout1-2', 'layout2-1', 'layout1-1-1', 'layout1-2-1', 'layout1', 'layout1-3', 'layout3-1']
      // 如flag:3为索引3，既平均分配的三列显示：layout1-1-1
      // feature:每一个展示项有一个特有的feature名称，不能重复，用于页面动态显示时判断
      // 初始化布局，layout会随着接口返回发生变化，所以初始化的数据要一直保留，不被修改
      originalLayout: [
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('report.softwareLicenseUsage'), type: 'chart-bar', feature: 'softwareLicensingCondition' },
            { span: 8, name: this.$t('pages.foundationForm_Msg45'), type: 'chart-data', feature: 'dataItemCondition' },
            { span: 8, name: this.$t('report.dataInstallationAndUsage'), type: 'chart-log', feature: 'installationAndUseCondition' }
          ]
        },
        { gutter: 20, flag: 3,
          cols: [
            { span: 8, name: this.$t('report.classificationOfInstalledSoftware'), type: 'chart-splattering', feature: 'installationSoftwareClassificationCondition' },
            { span: 8, name: this.$t('report.top5SoftwareWithIdleRate'), type: 'chart-bar', feature: 'limitRateTOP10Condition' },
            { span: 8, name: this.$t('report.softwareUsageDistribution'), type: 'chart-pie', feature: 'usageDistributionCondition' }
          ]
        },
        { gutter: 20, flag: 5,
          cols: [
            { span: 24, name: this.$t('report.top10TrendsInSoftwareUsageDuration'), type: 'chart-line', feature: 'durationTOP10Condition' }
          ]
        }
      ],
      classOption: {
        // 设置滚动速度，值越大滚动的越快
        step: 0.5
      },
      // 中断请求
      sourceGetLargeScreenConfig: null,
      sourceGetLayout: null,
      sourceGetSoftwareReportHomePageData: null,
      sourceGetSoftwareReportToDayInfo: null,
      // 定时器
      dataLoadPromise: undefined,
      currentTime: undefined,
      logLoad: undefined,
      queryLoad: undefined,
      intervalLogLoad: 60000,
      intervalQueryLoad: 14400000,
      // 根据是否全屏，动态修改appContainer、每个小方块、中间小方块的高度，可适应全屏和非全屏的高度
      appContainerHeight: `calc(100vh - 85px)`,
      chartDivHeight2: '19vh',
      middleOutHeight: '11.1vh',
      rankingDivHeight: '16vh',
      // 判断是否全屏
      isFullScreen: false,
      // 查询条件
      tempQuery: {},
      temp: {},
      defaultTemp: {
        // 最后更新时间
        lastUpdateTime: getCurrentTime(),
        currentTime: '',
        middleMsg: [
          { currentName: this.$t('report.softwareInstalls'), currentNum: '0' },
          { currentName: this.$t('report.softwarePurchases'), currentNum: '0' },
          { currentName: this.$t('report.softwareLicenses'), currentNum: '0' },
          { currentName: this.$t('report.numberOfUnlicensedSoftware'), currentNum: '0' }
        ],
        // 软件授权使用情况(柱状图)
        barChartSoftwareLicenseData: [],
        barChartSoftwareLicenseOption: {
          yAxis: {
            type: 'category',
            data: []
          },
          xAxis: {
            name: '',
            type: 'value'
          },
          series: [{ data: [] }]
        },
        // 软件安装与使用情况
        installUseDatas: [],
        // 安装软件分类情况
        softwareCategory: [],
        // 软件使用分布(饼图)
        softwareUsageDistributionData: [],
        softwareUsageDistributionOption: {
          series: [
            {
              radius: [10, 40]
            }
          ]
        },
        // 终端在线及活动趋势(折线图)
        lineTimeData: [],
        lineTimeOption: {
          legend: {
            data: []
          },
          xAxis: {
            type: 'category',
            data: []
          },
          series: []
        },
        // 闲置TOP5软件(柱状图)
        barLeaveUnusedData: [],
        barLeaveUnusedOption: {
          xAxis: { data: [] },
          series: [{
            name: this.$t('report.authorizedNumber'),
            type: 'bar',
            stack: 'total',
            data: [],
            itemStyle: {
              color: {
                type: 'linear', // 线性渐变
                colorStops: [
                  { offset: 1, color: '#DC5D78' },
                  { offset: 0, color: '#E6C55A' }
                ]
              },
              emphasis: {
                opacity: 1
              }
            }
          },
          {
            name: this.$t('table.purchaseNum'),
            type: 'bar',
            stack: 'total',
            data: [],
            itemStyle: {
              color: {
                type: 'linear', // 线性渐变
                colorStops: [
                  { offset: 1, color: '#18CFD9' },
                  { offset: 0, color: '#59E6B4' }
                ]
              },
              emphasis: {
                opacity: 1
              }
            }
          }
          ]
        }
      }
    }
  },
  computed: {
  },
  watch: {
    tempQuery: {
      deep: true,
      handler(newVal, oldVal) {
        this.tempQuery = newVal
      }
    }
  },
  created() {

  },
  activated() {
    // 组件重新显示时--添加监听
    window.addEventListener('resize', this.handleFullScreenChange)
    if (this.isMounted === false) {
      this.startIntervalFun()
      // 系统当前时间
      this.getTimes()
    }
  },
  mounted() {
    // 挂载后
    this.isMounted = true
    this.resetTemp()
    this.getLayout()
    // 大屏轮播初始化时，浏览器大小不会发生变化，但是需要检测是否大屏，否则大屏中一些内容会失效
    this.handleFullScreenChange()
    this.getTimes()
    this.temp.currentTime = getCurrentTime()
    this.dataLoadPromise = Promise.all([
      this.getLargeScreenConfig()
    ]).then(() => {
      // 定时器切换
      this.startIntervalFun()
      this.getLogList()
      // 获取右上方查询条件查出来的信息,3小时刷新一次（首次加载时，报表日期的获取可能需要一点点时长）
      setTimeout(() => {
        this.getLargeScreenData()
      }, 1000)
    })
    setTimeout(() => {
      this.isMounted = false
    }, 1000)
    window.addEventListener('resize', this.handleFullScreenChange)
  },
  beforeDestroy() {
    // 组件关闭时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    this.stopIntervalFun()
    this.stopRequest()
    clearInterval(window.currentTime)
    window.currentTime = null
  },
  deactivated() {
    // 组件停用时--清除监听
    window.removeEventListener('resize', this.handleFullScreenChange)
    this.stopIntervalFun()
    this.stopRequest()
    clearInterval(window.currentTime)
    window.currentTime = null
  },
  methods: {
    /**
     * 中断http请求（组件关闭或停用时，如果http接口未请求完成，页面切换时中断请求调用）
     * */
    stopRequest() {
      // 中断大屏配置接口获取
      if (this.sourceGetLargeScreenConfig) { this.sourceGetLargeScreenConfig.cancel() }
      // 中断自定义大屏配置接口获取
      if (this.sourceGetLayout) { this.sourceGetLayout.cancel() }
      // 中断右上方查询条件查出来的信息接口获取
      if (this.sourceGetSoftwareReportHomePageData) { this.sourceGetSoftwareReportHomePageData.cancel() }
      // 终端获取软件安装与使用情况列表数据
      if (this.sourceGetSoftwareReportToDayInfo) { this.sourceGetSoftwareReportToDayInfo.cancel() }
    },
    /**
     * 定时器启动
     * */
    startIntervalFun() {
      if (window.logLoad) {
        clearInterval(window.logLoad)
      }
      if (window.queryLoad) {
        clearInterval(window.queryLoad)
      }
      window.logLoad = setInterval(() => { this.getLogList() }, this.intervalLogLoad)
      window.queryLoad = setInterval(() => { this.getLargeScreenData() }, this.intervalQueryLoad)
    },
    /**
     * 定时器停止
     * */
    stopIntervalFun() {
      clearInterval(window.logLoad)
      window.logLoad = null
      clearInterval(window.queryLoad)
      window.queryLoad = null
    },
    /**
     * 获取layout布局（动态显示页面布局）
     * */
    getLayout(option) {
      const userId = this.$store.getters.userId
      this.sourceGetLayout = axios.CancelToken.source()
      let cacheToken = this.sourceGetLayout.token
      if (option === 'config') {
        cacheToken = ''
      }
      getValueByCondition({ sysUserId: userId, menuCode: this.parameterOption.menuCode, tableKey: this.parameterOption.tableKey, type: 4 }, cacheToken).then(res => {
        if (res.data) {
          // 如果存在data,直接显示接口返回，否则为前端页面设置的默认值
          // console.log('软件安装与使用', JSON.parse(res.data.value))
          this.layout = JSON.parse(res.data.value)
          this.orLayout = JSON.parse(res.data.value)
        } else {
          // 初始化时先赋原来的初始值
          this.layout = JSON.parse(JSON.stringify(this.originalLayout))
          this.orLayout = JSON.parse(JSON.stringify(this.originalLayout))
        }
      })
    },
    /**
     * 打开布局配置弹框
     */
    configBtn() {
      this.getLayout()
      this.$refs.dialogLayoutConfig.show()
    },
    /**
     * 获取大屏配置中的配置信息（动态设置定时器时长）
     * */
    getLargeScreenConfig() {
      const userId = this.$store.getters.userId
      this.sourceGetLargeScreenConfig = axios.CancelToken.source()
      const cacheToken = this.sourceGetLargeScreenConfig.token
      return getValueByCondition({ sysUserId: userId, menuCode: '1001', tableKey: 'largeScreenConfig', type: 4 }, cacheToken).then(res => {
        if (res.data) {
          const response = JSON.parse(res.data.value)
          // console.log('response', response)
          // 报表日期
          if (response.softwareInstallDimBaseType && response.softwareInstallDimValue !== '') {
            this.dataTemp.encOrDecDimBaseType = response.softwareInstallDimBaseType
            this.dataTemp.encOrDecDimValue = response.softwareInstallDimValue
            // 切换间隔时间，单位毫秒
            this.intervalLogLoad = getSetIntervalTime(response.softwareInstallUseLogValue, response.softwareInstallUseLogSelect)
            this.intervalQueryLoad = getSetIntervalTime(response.softwareInstallUseQueryValue, response.softwareInstallUseQuerySelect)
          }
          // 报表日期(兼容旧数据)
          if (response.softwareInstallDimDataCycle) {
            this.dataTemp.dateCycleLargeScreen = response.softwareInstallDimDataCycle
          }
        }
      })
    },
    /**
     * 获取接口参数（子组件点击查询时获取）
     * @param data 参数
     * */
    thisQuery(data) {
      this.tempQuery = data
    },
    /**
     * 获取右上方查询条件查出来的信息
     * */
    getLargeScreenData() {
      this.$nextTick(() => {
        // console.log('this.tempQuery软件安装与使用', JSON.parse(JSON.stringify(this.tempQuery)))
        // 根据是否首次加载，调用有日志和没日志的接口
        let apiFun = displayPanelGetSoftwareReportHomePageDataNoOplog
        if (this.isMounted && this.isFirst) {
          apiFun = displayPanelGetSoftwareReportHomePageData
        }
        this.sourceGetSoftwareReportHomePageData = axios.CancelToken.source()
        const cacheToken = this.sourceGetSoftwareReportHomePageData.token
        apiFun(this.tempQuery, cacheToken).then(res => {
          // 最后更新时间
          this.temp.lastUpdateTime = getCurrentTime()
          const response = res.data
          // console.log('response', JSON.parse(JSON.stringify(response)))
          // 获取中间数据
          this.getMiddleData(response)
          const chartOption = response.chartDataObjMap
          // 软件授权使用情况
          this.getSoftwareUsage(chartOption)
          // 安装软件分类情况
          this.temp.softwareCategory = chartOption.softwareTypeChart.chartData
          // 闲置TOP5软件
          this.getVacancyRate(chartOption)
          // 软件使用分布
          this.temp.softwareUsageDistributionData = this.createObjectsArray(chartOption.topGroupInstallNumChart.xaxisData, chartOption.topGroupInstallNumChart.seriesData)
          // 软件使用时长TOP10趋势
          this.temp.lineTimeOption.xAxis.data = chartOption.appReportexDayLine.xaxisData
          this.temp.lineTimeOption.series = chartOption.appReportexDayLine.seriesData
          this.temp.lineTimeOption.legend.data = chartOption.appReportexDayLine.seriesData.map(obj => obj.name)
          this.temp.lineTimeOption.tooltip = {
            formatter: (d) => {
              // console.log('d', d)
              let res = d[0].name + '<br>';
              for (var i = 0; i < d.length; i++) {
                let number = d[i].value
                number = formatSeconds(d[i].value)
                res += d[i].marker + d[i].seriesName + ' : ' + number + '<br>'
              }
              return res
            }
          }
        })
      })
    },
    /**
     * 获取软件授权使用情况数据
     * @param data 接口返回的res.data.chartDataObjMap
     * */
    getSoftwareUsage(data) {
      this.temp.barChartSoftwareLicenseData = data.softTopNumChart.seriesData
      this.temp.barChartSoftwareLicenseOption.yAxis.data = data.softTopNumChart.xaxisData
      this.temp.barChartSoftwareLicenseOption.series[0].data = data.softTopNumChart.seriesData
      this.temp.barChartSoftwareLicenseOption.yAxis.axisLabel = {
        formatter: function(value) {
          // 如果文本长度超过8个字，则返回前八个字加上省略号
          if (value.length > 8) {
            return value.slice(0, 8) + '...';
          }
          // 如果文本长度不超过8个字，则直接返回原文本
          return value;
        }
      }
    },
    /**
     * 获取闲置TOP5软件数据
     * @param data 接口返回的res.data.chartDataObjMap
     * */
    getVacancyRate(data) {
      // arrays数组第一个数是授权数，第二个是购买数
      const arrays = data.topIdleSoftChart.seriesData;
      // 提取二维数组中第一个元素为授权数
      this.temp.barLeaveUnusedOption.series[0].data = arrays.map(pair => pair[0])
      // 提取二维数组中第二个元素为采购数
      this.temp.barLeaveUnusedOption.series[1].data = arrays.map(pair => pair[1])

      this.temp.barLeaveUnusedOption.xAxis.data = data.topIdleSoftChart.xaxisData
      this.temp.barLeaveUnusedOption.xAxis.axisLabel = {
        formatter: function(value) {
          // 如果文本长度超过5个字，则返回前5个字加上省略号
          if (value.length > 5) {
            return value.slice(0, 5) + '...';
          }
          // 如果文本长度不超过8个字，则直接返回原文本
          return value;
        }
      }
    },
    /**
     * 将两个数组合并成一个对象数组
     * 软件使用分布（饼图数据处理）
     * */
    createObjectsArray(names, values) {
      return names.map((name, index) => {
        return {
          name: name.split('_').slice(1).join('_'),
          value: values[index]
        };
      });
    },
    /**
     * 获取中间数据
     * @param data 接口返回的res.data
     * */
    getMiddleData(data) {
      const middleItem = data.itemValueMap
      this.temp.middleMsg[0].currentNum = middleItem.installSum
      this.temp.middleMsg[1].currentNum = middleItem.purchaseSum
      this.temp.middleMsg[2].currentNum = middleItem.authSum
      this.temp.middleMsg[3].currentNum = middleItem.unAuthSum
    },
    /**
     * 获取软件安装与使用情况列表数据
     * */
    getLogList() {
      this.sourceGetSoftwareReportToDayInfo = axios.CancelToken.source()
      const cacheToken = this.sourceGetSoftwareReportToDayInfo.token
      displayPanelAppGetSoftwareReportToDayInfo(cacheToken).then(res => {
        // 最后更新时间
        this.temp.lastUpdateTime = getCurrentTime()
        // 对比新旧数据，如果有变化，添加标记isNew: true，否则isNew: false。用于区分新旧数据显示
        const datas = res.data
        const oldArray = this.temp.installUseDatas
        const newArray = datas
        const markNewObjects = (newArray, oldArray) => {
          return newArray.map(newObj => {
            // 数据从无到有的时候不变红色，只有新旧数据都存在，且存在新的数据时，才变红色
            let oldObj = {}
            if (oldArray.length > 0) {
              oldObj = oldArray.find(oldObj => (oldObj.terminalName === newObj.terminalName && oldObj.termGroupName === newObj.termGroupName && oldObj.appNum === newObj.appNum && oldObj.appRunTime === newObj.appRunTime))
            }
            return oldObj ? Object.assign({}, newObj, { isNew: false }) : Object.assign({}, newObj, { isNew: true });
          });
        };
        this.temp.installUseDatas = markNewObjects(newArray, oldArray);
        // console.log('list记录', JSON.parse(JSON.stringify(this.temp.installUseDatas)))
      })
    },
    /**
     * 系统当前时间，实时获取，实时刷新
     * */
    getTimes() {
      if (window.currentTime) {
        clearInterval(window.currentTime)
      }
      window.currentTime = setInterval(this.getSystemTime, 1000)
    },
    /**
     * 系统当前时间，赋值
     * */
    getSystemTime() {
      this.temp.currentTime = getCurrentTime()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      // 最后更新于
      this.temp.lastUpdateTime = getCurrentTime()
      // 报表日期默认值设置
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      this.currentTime = year.toString() + month
      this.dataTemp.encOrDecDimValue = this.currentTime
    },
    /**
     * 判断是否全屏（监听全屏）
     * 1、通知el-select和el-cascader的弹出框，大屏需要设置false才显示，普通屏幕需要设置true才显示
     * 2、动态设置页面一些元素的高度，主要是适配全屏与非全屏(高度尽量大，不出现纵向滚动条)
     * */
    handleFullScreenChange() {
      this.isFullScreen = !!document.fullscreenElement || !!document.mozFullScreenElement || !!document.webkitFullscreenElement || !!document.msFullscreenElement;
      if (this.isFullScreen) {
        this.appContainerHeight = '100vh'
        this.chartDivHeight2 = '23.5vh'
        this.middleOutHeight = '13.2vh'
        this.rankingDivHeight = '20.8vh'
      } else {
        this.appContainerHeight = `calc(100vh - 85px)`
        this.chartDivHeight2 = '19vh'
        this.middleOutHeight = '11.1vh'
        this.rankingDivHeight = '16vh'
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .activeClassRed{
    color: #ff0000;
  }
  //中间特殊的小块
  .large-screen-middle-out{
    width: 49%;
    float: left;
    div{
      width: 90%;
      margin: auto;
      font-size: 15px;
      height: 35px;
      line-height: 35px;
    }
    .large-screen-middle-num{
      font-size: 22px;
    }
    .large-screen-middle-name{
      font-size: 14px;
      height: 55px;
      line-height: 55px;
    }
  }
</style>
