<template>
  <comparative-trend
    :api="'getTrwfeLogCompareData'"
    :api-dept="'getTrwfeLogCompareDeptData'"
    :chart-title="chartTitle"
    :only-terminal="true"
    :count-by-object="1"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
import { getTrwfePermission } from '@/api/report/baseReport/issueReport/detail';
import { filterTrwfeLogCustom } from '@/utils/reportPermissionFiltering';
export default {
  name: 'TrwfeLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '审批',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'fileDecryptSumAll', label: 'fileDecryptSumAll', value: 1 },
        { prop: 'fileDecryptApproveSumAll', label: 'fileDecryptApproveSumAll', value: 2 },
        { prop: 'fileDecryptRejectSumAll', label: 'fileDecryptRejectSumAll', value: 4 },
        { prop: 'fileDecryptAutoApproveSumAll', label: 'fileDecryptAutoApproveSumAll', value: 8 },
        { prop: 'outSendSumAll', label: 'outSendSumAll', value: 16 },
        { prop: 'outSendApproveSumAll', label: 'outSendApproveSumAll', value: 32 },
        { prop: 'outSendRejectSumAll', label: 'outSendRejectSumAll', value: 64 },
        { prop: 'outSendAutoApproveSumAll', label: 'outSendAutoApproveSumAll', value: 128 },
        { prop: 'changeFileLevelSumAll', label: 'changeFileLevelSumAll', value: 256 },
        { prop: 'changeFileLevelApproveSumAll', label: 'changeFileLevelApproveSumAll', value: 512 },
        { prop: 'changeFileLevelRejectSumAll', label: 'changeFileLevelRejectSumAll', value: 1024 },
        { prop: 'changeFileLevelAutoApproveSumAll', label: 'changeFileLevelAutoApproveSumAll', value: 2048 },
        { prop: 'offlineSumAll', label: 'offlineSumAll', value: 4096 },
        { prop: 'offlineApproveSumAll', label: 'offlineApproveSumAll', value: 8192 },
        { prop: 'offlineRejectSumAll', label: 'offlineRejectSumAll', value: 16384 },
        { prop: 'offlineAutoApproveSumAll', label: 'offlineAutoApproveSumAll', value: 32768 },

        { prop: 'filePrintSumAll', label: 'filePrintSumAll', value: 65536 },
        { prop: 'filePrintApproveSumAll', label: 'filePrintApproveSumAll', value: 131072 },
        { prop: 'filePrintRejectSumAll', label: 'filePrintRejectSumAll', value: 262144 },
        { prop: 'filePrintAutoApproveSumAll', label: 'filePrintAutoApproveSumAll', value: 524288 },

        { prop: 'cancelWatermarkSumAll', label: 'cancelWatermarkSumAll', value: 268435456 },
        { prop: 'cancelWatermarkApproveSumAll', label: 'cancelWatermarkApproveSumAll', value: 536870912 },
        { prop: 'cancelWatermarkRejectSumAll', label: 'cancelWatermarkRejectSumAll', value: 1073741824 },
        { prop: 'cancelWatermarkAutoApproveSumAll', label: 'cancelWatermarkAutoApproveSumAll', value: 2147483648 },

        { prop: 'sensitiveFileOutSendSumAll', label: 'sensitiveFileOutSendSumAll', width: '210', value: 4294967296 },
        { prop: 'sensitiveFileOutSendApproveSumAll', label: 'sensitiveFileOutSendApproveSumAll', value: 8589934592 },
        { prop: 'sensitiveFileOutSendRejectSumAll', label: 'sensitiveFileOutSendRejectSumAll', value: 17179869184 },
        { prop: 'sensitiveFileOutSendAutoApproveSumAll', label: 'sensitiveFileOutSendAutoApproveSumAll', value: 34359738368 },

        { prop: 'behaviorControlSumAll', label: 'behaviorControlSumAll', value: 68719476736 },
        { prop: 'behaviorControlApproveSumAll', label: 'behaviorControlApproveSumAll', value: 137438953472 },
        { prop: 'behaviorControlRejectSumAll', label: 'behaviorControlRejectSumAll', value: 274877906944 },
        { prop: 'behaviorControlAutoApproveSumAll', label: 'behaviorControlAutoApproveSumAll', value: 549755813888 },

        { prop: 'fileRelieveJurisdictionSumAll', label: 'fileRelieveJurisdictionSumAll', value: 1099511627776 },
        { prop: 'fileRelieveJurisdictionApproveSumAll', label: 'fileRelieveJurisdictionApproveSumAll', value: 2199023255552 },
        { prop: 'fileRelieveJurisdictionRejectSumAll', label: 'fileRelieveJurisdictionRejectSumAll', value: 4398046511104 },
        { prop: 'fileRelieveJurisdictionAutoApproveSumAll', label: 'fileRelieveJurisdictionAutoApproveSumAll', value: 8796093022208 }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['fileDecryptSumAll', 'outSendSumAll', 'changeFileLevelSumAll', 'offlineSumAll', 'filePrintSumAll', 'cancelWatermarkSumAll', 'sensitiveFileOutSendSumAll', 'behaviorControlSumAll', 'fileRelieveJurisdictionSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    this.filterData()
  },
  methods: {
    /**
     * 权限过滤
     */
    async filterData() {
      await getTrwfePermission().then(res => {
        if (res.data) {
          const valueList = []
          this.statisticalList.forEach(item => {
            if (res.data.indexOf(this.$t('table.' + item.label)) > -1) {
              valueList.push(item.value)
            }
          })
          if (valueList) {
            this.statisticalList = filterTrwfeLogCustom(this.statisticalList, valueList)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
