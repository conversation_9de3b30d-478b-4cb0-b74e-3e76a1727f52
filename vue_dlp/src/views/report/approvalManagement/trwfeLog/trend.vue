<template>
  <Trend :api="'getTrwfeTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('775')" :exportable="hasPermission('776')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getTrwfeLogDetail, getTrwfePermission } from '@/api/report/baseReport/issueReport/detail'
import { getTrwfeLogCol, beforeLoad } from '@/utils/report'
import { filterTrwfeLogCustom } from '@/utils/reportPermissionFiltering';

export default {
  name: 'TrwfeLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '140', sort: true, sortOriginal: true },
        { prop: 'fileDecryptSumAll', label: 'fileDecryptSumAll', width: '130', sort: true, value: 1, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'outSendSumAll', label: 'outSendSumAll', width: '160', sort: true, value: 16, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'changeFileLevelSumAll', label: 'changeFileLevelSumAll', width: '150', sort: true, value: 256, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'offlineSumAll', label: 'offlineSumAll', width: '160', sort: true, value: 4096, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'filePrintSumAll', label: 'filePrintSumAll', width: '160', sort: true, value: 65536, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'cancelWatermarkSumAll', label: 'cancelWatermarkSumAll', width: '190', sort: true, value: 268435456, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'sensitiveFileOutSendSumAll', label: 'sensitiveFileOutSendSumAll', width: '210', sort: true, value: 4294967296, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'behaviorControlSumAll', label: 'behaviorControlSumAll', width: '210', sort: true, value: 68719476736, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'fileRelieveJurisdictionSumAll', label: 'fileRelieveJurisdictionSumAll', width: '210', sort: true, value: 1099511627776, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad }
      ],
      customList: [
        { prop: 'fileDecryptSumAll', label: 'fileDecryptSumAll', width: '150', sort: true, value: 1, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'fileDecryptApproveSumAll', label: 'fileDecryptApproveSumAll', width: '170', sort: true, value: 2, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'fileDecryptRejectSumAll', label: 'fileDecryptRejectSumAll', width: '170', sort: true, value: 4, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'fileDecryptAutoApproveSumAll', label: 'fileDecryptAutoApproveSumAll', width: '200', sort: true, value: 8, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'outSendSumAll', label: 'outSendSumAll', width: '180', sort: true, value: 16, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'outSendApproveSumAll', label: 'outSendApproveSumAll', width: '180', sort: true, value: 32, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'outSendRejectSumAll', label: 'outSendRejectSumAll', width: '180', sort: true, value: 64, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'outSendAutoApproveSumAll', label: 'outSendAutoApproveSumAll', width: '230', sort: true, value: 128, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'changeFileLevelSumAll', label: 'changeFileLevelSumAll', width: '150', sort: true, value: 256, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'changeFileLevelApproveSumAll', label: 'changeFileLevelApproveSumAll', width: '160', sort: true, value: 512, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'changeFileLevelRejectSumAll', label: 'changeFileLevelRejectSumAll', width: '160', sort: true, value: 1024, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'changeFileLevelAutoApproveSumAll', label: 'changeFileLevelAutoApproveSumAll', width: '200', sort: true, value: 2048, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'offlineSumAll', label: 'offlineSumAll', width: '160', sort: true, value: 4096, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'offlineApproveSumAll', label: 'offlineApproveSumAll', width: '160', sort: true, value: 8192, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'offlineRejectSumAll', label: 'offlineRejectSumAll', width: '160', sort: true, value: 16384, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'offlineAutoApproveSumAll', label: 'offlineAutoApproveSumAll', width: '200', sort: true, value: 32768, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },

        { prop: 'filePrintSumAll', label: 'filePrintSumAll', width: '160', sort: true, value: 65536, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'filePrintApproveSumAll', label: 'filePrintApproveSumAll', width: '160', sort: true, value: 131072, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'filePrintRejectSumAll', label: 'filePrintRejectSumAll', width: '160', sort: true, value: 262144, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'filePrintAutoApproveSumAll', label: 'filePrintAutoApproveSumAll', width: '220', sort: true, value: 524288, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },

        // { prop: 'printOutsendSumAll', label: '打印外发审批完成数', width: '190', sort: true, value: 1048576 },
        // { prop: 'printOutsendApproveSumAll', label: '打印外发审批通过数', width: '190', sort: true, value: 2097152 },
        // { prop: 'printOutsendRejectSumAll', label: '打印外发审批拒绝数', width: '190', sort: true, value: 4194304 },
        // { prop: 'printOutsendAutoApproveSumAll', label: '打印外发审批系统自动应答数', width: '250', sort: true, value: 8388608 },

        { prop: 'cancelWatermarkSumAll', label: 'cancelWatermarkSumAll', width: '190', sort: true, value: 268435456, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'cancelWatermarkApproveSumAll', label: 'cancelWatermarkApproveSumAll', width: '190', sort: true, value: 536870912, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'cancelWatermarkRejectSumAll', label: 'cancelWatermarkRejectSumAll', width: '190', sort: true, value: 1073741824, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'cancelWatermarkAutoApproveSumAll', label: 'cancelWatermarkAutoApproveSumAll', width: '250', sort: true, value: 2147483648, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },

        { prop: 'sensitiveFileOutSendSumAll', label: 'sensitiveFileOutSendSumAll', width: '210', sort: true, value: 4294967296, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'sensitiveFileOutSendApproveSumAll', label: 'sensitiveFileOutSendApproveSumAll', width: '210', sort: true, value: 8589934592, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'sensitiveFileOutSendRejectSumAll', label: 'sensitiveFileOutSendRejectSumAll', width: '210', sort: true, value: 17179869184, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'sensitiveFileOutSendAutoApproveSumAll', label: 'sensitiveFileOutSendAutoApproveSumAll', width: '260', sort: true, value: 34359738368, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },

        { prop: 'behaviorControlSumAll', label: 'behaviorControlSumAll', width: '210', sort: true, value: 68719476736, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'behaviorControlApproveSumAll', label: 'behaviorControlApproveSumAll', width: '210', sort: true, value: 137438953472, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'behaviorControlRejectSumAll', label: 'behaviorControlRejectSumAll', width: '210', sort: true, value: 274877906944, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'behaviorControlAutoApproveSumAll', label: 'behaviorControlAutoApproveSumAll', width: '260', sort: true, value: 549755813888, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },

        { prop: 'fileRelieveJurisdictionSumAll', label: 'fileRelieveJurisdictionSumAll', width: '210', sort: true, value: 1099511627776, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'fileRelieveJurisdictionApproveSumAll', label: 'fileRelieveJurisdictionApproveSumAll', width: '210', sort: true, value: 2199023255552, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'fileRelieveJurisdictionRejectSumAll', label: 'fileRelieveJurisdictionRejectSumAll', width: '210', sort: true, value: 4398046511104, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'fileRelieveJurisdictionAutoApproveSumAll', label: 'fileRelieveJurisdictionAutoApproveSumAll', width: '260', sort: true, value: 8796093022208, detailCol: getTrwfeLogCol(), getDetailMethod: getTrwfeLogDetail, beforeLoadDetail: beforeLoad }
      ]
    }
  },
  created() {
    this.filterData()
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    /**
     * 权限过滤
     */
    async filterData() {
      await getTrwfePermission().then(res => {
        if (res.data) {
          const filterColModel = []
          const valueList = []
          this.customList.forEach(item => {
            if (res.data.indexOf(this.$t('table.' + item.label)) > -1) {
              valueList.push(item.value)
            }
          })
          if (valueList) {
            this.customList = filterTrwfeLogCustom(this.customList, valueList)
          }
          this.colModel.forEach(item => {
            if (item.prop == 'labelValue') {
              filterColModel.push(item)
            } else if (res.data.indexOf(this.$t('table.' + item.label)) > -1) {
              filterColModel.push(item)
            }
          })
          this.colModel = filterColModel
        }
      })
    }
  }
}
</script>
