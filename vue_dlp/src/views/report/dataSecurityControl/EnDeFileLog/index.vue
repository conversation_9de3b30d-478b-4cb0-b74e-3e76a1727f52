<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['user']" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <SearchToolbar
        ref="searchToolbar"
        @toggleTreeMenu="toggleTreeMenu"
        @getTimeParams="getTimeParams"
        @handleFilter="handleFilter"
      >
        <SearchItem model-key="query.fileKeyWord" :value="query.fileKeyWord">
          <span>{{ $t('pages.enDeFileLog_Msg') }}：</span>
          <el-input v-model="query.fileKeyWord" v-trim clearable style="width: 150px;" />
        </SearchItem>
        <SearchItem model-key="query.extList" :value="query.extList">
          <span>{{ $t('pages.process_Msg5') }} ：</span>
          <el-select
            v-model="query.extList"
            collapse-tags
            allow-create
            filterable
            multiple
            clearable
            :title="query.extList.join(', ')"
            style="width: 200px; height: 30px;"
            @change="extChange"
          >
            <el-option v-for="item in extListOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </SearchItem>
        <span>Top</span>
        <el-select v-model="query.topChart" style="width: 100px">
          <el-option :value="5" label="5"/>
          <el-option :value="10" label="10"/>
          <el-option :value="20" label="20"/>
        </el-select>
        <audit-log-exporter slot="append" v-permission="'599'" multi-dataset :request="handleExport"/>
      </SearchToolbar>
      <div class="container">
        <div>
          <e-charts ref="charts" :charts-option="chartsOption"></e-charts>
          <div>
            <span style="float: right;font-size: 15px">
              <span
                v-for="(item, index) in chartDescription"
                :key="index"
                :style="index < 2 ? 'padding-right: 15px;' : ''"
              >
                {{ item }}
              </span>
            </span>
          </div>
        </div>
        <grid-table
          ref="logList"
          row-key="logId"
          :height="340"
          :after-load="afterload"
          :col-model="colModel"
          :default-sort="{ prop: 'createTime' }"
          :multi-select="multiSelect"
          :row-data-api="rowDataApi"
          :autoload="autoload"
          @selectionChangeEnd="selectionChangeEnd"
        />
      </div>
    </div>
    <video-viewer ref="videoViewer"/>
  </div>
</template>

<script>
import { getLogPage, getChartData, getCountPage, exportExcel } from '@/api/dataEncryption/encryption/EnDeFile'
import ECharts from '@/components/ECharts'
import { enableStgDelete } from '@/utils';
import { addViewVideoBtn, asyncGetLogVideoInfo } from '@/utils/logVideo';
import { auditLogRouterMixin } from '@/mixins/routerMixins';

export default {
  name: 'EnDeFileLogStatistical',
  components: { ECharts },
  mixins: [auditLogRouterMixin],
  props: {
  },
  data() {
    return {
      autoload: true,
      colModel: [
        { prop: 'userName', label: 'object', width: '200' },
        { prop: 'objectType', label: 'objectType', width: '100', formatter: this.entityFormatter },
        { prop: 'fileNum', label: 'fileNum', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right', hidden: !this.hasPermission('598'),
          buttons: [
            { label: 'detail', formatter: this.buttonFormatter, click: this.handleDetail }
          ]
        }
      ],
      numColModel: [
        { prop: 'userName', label: 'object', width: '200' },
        { prop: 'objectType', label: 'objectType', width: '100', formatter: this.entityFormatter },
        { prop: 'fileNum', label: 'fileNum', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'detail', formatter: this.buttonFormatter, click: this.handleDetail }
          ]
        }
      ],
      detailColModel: [
        { prop: 'filePath', label: 'file', width: '200' },
        { prop: 'userName', label: 'user', width: '100' },
        { prop: 'userGroupName', label: 'userGroup', width: '100' },
        { prop: 'fileProc', label: 'operationProcess', width: '100' },
        { prop: 'createTime', label: 'operateTime', width: '150', sort: 'custom', formatter: this.timeFormatter }
      ],
      sortCondition: { sortName: 'createTime', sortOrder: 'desc' },
      query: { // 查询条件
        page: 1,
        objectType: 4,
        objectId: undefined,
        objectName: this.$t('pages.enDeFileLog_Msg4'),
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        extList: [],
        fileKeyWord: '',
        topChart: 5,
        picBase64: ''
      },
      extListOptions: [
        { label: this.$t('pages.collectAll'), value: '' },
        { label: '.doc', value: '.doc' },
        { label: '.docx', value: '.docx' },
        { label: '.pdf', value: '.pdf' },
        { label: '.ppt', value: '.ppt' },
        { label: '.pptx', value: '.pptx' },
        { label: '.txt', value: '.txt' },
        { label: '.rtf', value: '.rtf' },
        { label: '.xls', value: '.xls' },
        { label: '.xlsx', value: '.xlsx' }
      ],
      showTree: true,
      dialogFormVisible: false,
      chartsOption: [],
      detailQuery: { // 查询条件
        page: 1,
        objectType: 4,
        objectId: undefined,
        objectName: undefined,
        createDate: '',
        startDate: '',
        endDate: '',
        isTimes: false,
        extList: [],
        fileKeyWord: ''
      },
      allNum: 0,
      deleteable: false,
      multiSelect: false,
      queryVideoMethod: undefined
    }
  },
  computed: {
    gridTable() {
      return this.$refs['logList']
    },
    fileOpTypeMap() {
      const map = {}
      this.fileOpTypeOptions.forEach(item => {
        if (item.value !== null) {
          map[item.value] = item.label
        }
      })
      return map
    },
    // 计算 extListOptions 选项的 map ，用于判断是否添加新选项
    extMap() {
      return this.extListOptions.reduce((map, cur) => {
        map[cur.value] = true
        return map
      }, {})
    },
    chartDescription() {
      return [
        `${this.$t('pages.enDeFileLog_Msg1')}：${this.query.objectName}`,
        `${this.$t('pages.enDeFileLog_Msg2')}：${this.allNum}`,
        `${this.$t('pages.enDeFileLog_Msg3')}：${this.query.isTimes ? (this.query.startDate + this.$t('pages.till') + this.query.endDate) : this.query.createDate}`
      ]
    }
  },
  created() {
    this.getChartData()
    addViewVideoBtn(this, undefined, 'detailColModel')
  },
  activated() {
    this.queryVideoMethod && this.queryVideoMethod()
  },
  methods: {
    afterload(rowData, grid) {
      if (rowData && rowData.length > 0) {
        this.allNum = rowData[0].allNum
      } else {
        this.allNum = 0
      }
      if (this.query.objectType == 2) {
        this.queryVideoMethod = asyncGetLogVideoInfo(rowData)
      }
    },
    entityFormatter: function(row, data) {
      return data == 4 ? this.$t('pages.dept') : this.$t('pages.user')
    },
    getChartData() {
      getChartData(this.query).then(res => {
        this.chartsOption = res.data
        if (this.query.objectName != undefined) {
          this.chartsOption.forEach(item => {
            item.option.title.text = this.$t('pages.enDeFileLog_Msg5', { object: this.query.objectName, echartName: item.option.title.text })
          })
        }
      })
    },
    extChange(selections) {
      // 获取最后一个选中的值
      const ext = [...selections].pop()
      // 新创建的选项，则添加到 extListOptions 中
      if (ext && ext.trim() && !this.extMap[ext]) {
        const option = { label: ext, value: ext }
        this.extListOptions.push(option)
      }
      // 最后选中的值是 所有，则只保留 所有
      if (ext == '') {
        selections.splice(0, selections.length, '')
      } else {
        // 否则删除 所有 选项
        const allIndex = selections.indexOf('')
        allIndex >= 0 && selections.splice(allIndex, 1)
      }
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      this.sortCondition = { sortName: option.sortName, sortOrder: option.sortOrder }
      return getCountPage(searchQuery)
    },
    rowDataApi2: function(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      return getLogPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.multiSelect = false
      if (!this.hasPermission('598')) { return }
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
        this.query.objectName = checkedNode.label
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.query.objectName = undefined
      }
      if (this.query.objectType == 2) {
        this.colModel = this.detailColModel
        this.multiSelect = this.$store.getters.auditingDeleteAble && this.hasPermission('468')
      } else {
        this.colModel = this.numColModel
      }
      this.gridTable.execRowDataApi(this.query)
      this.getChartData()
    },
    handleExport(exportType) {
      const chart = this.$refs.charts.$refs.bar[0].chart
      return this.getChartConnectedDataURL(chart).then(dataURL => {
        this.query.picBase64 = dataURL
        const reqParam = Object.assign({}, this.query, this.sortCondition)
        reqParam.chartDesc = this.chartDescription.join('  ')
        reqParam.exportType = exportType
        return exportExcel(reqParam)
      })
    },
    getChartConnectedDataURL(chart) {
      return new Promise(resolve => {
        const width = chart.getWidth()
        if (width === 632) {
          resolve(chart.getConnectedDataURL({
            excludeComponents: ['toolbox'],
            backgroundColor: '#fff'
          }))
          return
        }
        // chart太宽或太窄会造成导出的excel中图片严重变形，此处需要重置一下chart的宽度
        chart.getDom().style.width = '632px'
        chart.resize()
        // 改变图表尺寸后需要等待渲染完成，才能获取到正确的图片数据
        chart.on('finished', () => {
          chart.off('finished')
          const dataURL = chart.getConnectedDataURL({
            excludeComponents: ['toolbox'],
            backgroundColor: '#fff'
          })
          // 获取图表数据后恢复图表尺寸
          chart.getDom().style.width = '100%'
          chart.resize()
          resolve(dataURL)
        })
      })
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
      this.getChartData()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleDetail(row) {
      const stTree = this.$refs.strategyTargetTree || this.$store.getters.commonVm.strategyTargetTree
      const list = stTree.$refs['user'][0].getSearchList()
      const node = list.filter(node => {
        // list节点中分组节点的type为G，这边需要做转换才能找到节点
        const type = node.type == 'G' ? 4 : node.type
        return node.dataId == row.userId && type == row.objectType
      })[0]
      stTree.$refs['user'][0].selectSearch(node)
    },
    buttonFormatter(row) {
      if (row.objectType) {
        return this.$t('pages.detail')
      } else {
        return ''
      }
    },
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container{
  overflow-y: scroll;
}
.container {
  height: calc(100% - 30px);
  padding: 0 16px 0 0;
  overflow: auto;
}
</style>
