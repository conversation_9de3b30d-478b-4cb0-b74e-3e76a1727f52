<template>
  <Trend :api="'getDataSecurityTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('735')" :exportable="hasPermission('736')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getDiskScanLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getEncOrDecDetailCol, getDiskScanDetailCol, getOutFileDetailCol, getFileBackupDetailCol, getEncDecFilesLogCol,
  beforeLoad, beforeLoadEncOrDec, beforeLoadOutFile } from '@/utils/report'
import { filterDataSecurityLogCustom, filterDataSecurityLogDefault } from '@/utils/reportPermissionFiltering'
export default {
  name: 'DataSecurityTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'encOrDecSumAll', label: 'encOrDecSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'diskScanSumAll', label: 'diskScanSumAll', width: '190', sort: true, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'outFileSumAll', label: 'outFileSumAll', width: '150', sort: true, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol() },
        { prop: 'fileBackupSumAll', label: 'fileBackupSumAll', width: '150', sort: true, detailTable: 'dwd_file_backup_record', detailCol: getFileBackupDetailCol() },
        { prop: 'encFileSumAll', label: 'encFileSumAll', width: '150', sort: true, detailTable: 'dwd_enc_dec_files_log', detailCol: getEncDecFilesLogCol(), beforeLoadDetail: beforeLoadEncOrDec }
      ],
      customList: [
        { prop: 'encOrDecSumAll', label: 'encOrDecSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'encSumAll', label: 'encSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol(), beforeLoadDetail: beforeLoadEncOrDec },
        { prop: 'decSumAll', label: 'decSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol(), beforeLoadDetail: beforeLoadEncOrDec },
        { prop: 'diskScanSumAll', label: 'diskScanSumAll', width: '190', sort: true, value: 8, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'diskScanSstSumAll', label: 'diskScanSstSumAll', width: '180', sort: true, value: 16, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'diskScanNonsstSumAll', label: 'diskScanNonsstSumAll', width: '190', sort: true, value: 32, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'diskScanEncSumAll', label: 'diskScanEncSumAll', width: '190', sort: true, value: 64, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'diskScanDecSumAll', label: 'diskScanDecSumAll', width: '190', sort: true, value: 128, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        // { prop: 'trwfeSumAll', label: 'trwfeSumAll', width: '190', sort: true, value: 256, detailTable: 'dwd_act_hi_taskinst', detailCol: getTrwfeLogCol() },
        { prop: 'outFileSumAll', label: 'outFileSumAll', width: '150', sort: true, value: 512, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol() },
        { prop: 'directOutFileSumAll', label: 'directOutFileSumAll', width: '150', sort: true, value: 1024, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol(), beforeLoadDetail: beforeLoadOutFile },
        { prop: 'directOutFileSizeSumAll', label: 'directOutFileSizeSumAll', width: '160', sort: true, flag: 'size', sortOriginal: true, value: 2048, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol(), beforeLoadDetail: beforeLoadOutFile },
        { prop: 'applyOutFileSumAll', label: 'applyOutFileSumAll', width: '150', sort: true, value: 4096, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol(), beforeLoadDetail: beforeLoadOutFile },
        { prop: 'applyOutFileSizeSumAll', label: 'applyOutFileSizeSumAll', width: '160', sort: true, flag: 'size', sortOriginal: true, value: 8192, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol(), beforeLoadDetail: beforeLoadOutFile },
        { prop: 'fileBackupSumAll', label: 'fileBackupSumAll', width: '150', sort: true, value: 16384, detailTable: 'dwd_file_backup_record', detailCol: getFileBackupDetailCol() },
        { prop: 'fileBackupSizeSumAll', label: 'fileBackupSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 32768, detailTable: 'dwd_file_backup_record', detailCol: getFileBackupDetailCol() },
        { prop: 'encFileSumAll', label: 'encFileSumAll', width: '150', sort: true, value: 65536, detailTable: 'dwd_enc_dec_files_log', detailCol: getEncDecFilesLogCol(), beforeLoadDetail: beforeLoadEncOrDec }
      ]
    }
  },
  created() {
    this.filterData()
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    /**
     * 权限过滤
     */
    filterData() {
      this.customList = filterDataSecurityLogCustom(this.customList)
      this.colModel = filterDataSecurityLogDefault(this.colModel, 'labelValue')
    }
  }
}
</script>
