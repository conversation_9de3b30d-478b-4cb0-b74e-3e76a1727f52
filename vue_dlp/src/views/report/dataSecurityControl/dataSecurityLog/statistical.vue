<template>
  <Statistical :api="'getDataSecurityBar'" :default-col-model="defaultColModel" :custom-list="customList" :drill-down="hasPermission('730')" :exportable="hasPermission('731')" @handleSelect="handleCustomColumn" @getCountByObject="getCountByObject"/>
</template>

<script>
import Statistical from '../../component/Statistical'
import { getDiskScanLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getEncOrDecDetailCol, getDiskScanDetailCol, getOutFileDetailCol, getFileBackupDetailCol, getEncDecFilesLogCol,
  beforeLoad, beforeLoadEncOrDec, beforeLoadOutFile } from '@/utils/report'
import { filterDataSecurityLogCustom, filterDataSecurityLogDefault } from '@/utils/reportPermissionFiltering'

export default {
  name: 'DataSecurityStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'encOrDecSumAll', label: 'encOrDecSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'encSumAll', label: 'encSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol(), beforeLoadDetail: beforeLoadEncOrDec },
        { prop: 'decSumAll', label: 'decSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol(), beforeLoadDetail: beforeLoadEncOrDec },
        { prop: 'diskScanSumAll', label: 'diskScanSumAll', width: '190', sort: true, value: 8, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'diskScanSstSumAll', label: 'diskScanSstSumAll', width: '180', sort: true, value: 16, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'diskScanNonsstSumAll', label: 'diskScanNonsstSumAll', width: '190', sort: true, value: 32, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'diskScanEncSumAll', label: 'diskScanEncSumAll', width: '190', sort: true, value: 64, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'diskScanDecSumAll', label: 'diskScanDecSumAll', width: '190', sort: true, value: 128, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'outFileSumAll', label: 'outFileSumAll', width: '150', sort: true, value: 512, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol() },
        { prop: 'directOutFileSumAll', label: 'directOutFileSumAll', width: '150', sort: true, value: 1024, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol(), beforeLoadDetail: beforeLoadOutFile },
        { prop: 'directOutFileSizeSumAll', label: 'directOutFileSizeSumAll', width: '160', sort: true, flag: 'size', sortOriginal: true, value: 2048, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol(), beforeLoadDetail: beforeLoadOutFile },
        { prop: 'applyOutFileSumAll', label: 'applyOutFileSumAll', width: '150', sort: true, value: 4096, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol(), beforeLoadDetail: beforeLoadOutFile },
        { prop: 'applyOutFileSizeSumAll', label: 'applyOutFileSizeSumAll', width: '160', sort: true, flag: 'size', sortOriginal: true, value: 8192, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol(), beforeLoadDetail: beforeLoadOutFile },
        { prop: 'fileBackupSumAll', label: 'fileBackupSumAll', width: '150', sort: true, value: 16384, detailTable: 'dwd_file_backup_record', detailCol: getFileBackupDetailCol() },
        { prop: 'fileBackupSizeSumAll', label: 'fileBackupSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 32768, detailTable: 'dwd_file_backup_record', detailCol: getFileBackupDetailCol() },
        { prop: 'encFileSumAll', label: 'encFileSumAll', width: '150', sort: true, value: 65536, detailTable: 'dwd_enc_dec_files_log', detailCol: getEncDecFilesLogCol(), beforeLoadDetail: beforeLoadEncOrDec }
      ],

      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'encOrDecSumAll', label: 'encOrDecSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'diskScanSumAll', label: 'diskScanSumAll', width: '190', sort: true, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'outFileSumAll', label: 'outFileSumAll', width: '150', sort: true, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol() },
        { prop: 'fileBackupSumAll', label: 'fileBackupSumAll', width: '150', sort: true, detailTable: 'dwd_file_backup_record', detailCol: getFileBackupDetailCol() },
        { prop: 'encFileSumAll', label: 'encFileSumAll', width: '150', sort: true, detailTable: 'dwd_enc_dec_files_log', detailCol: getEncDecFilesLogCol(), beforeLoadDetail: beforeLoadEncOrDec }
      ],
      curSelected: [
        { prop: 'encOrDecSumAll', label: 'encOrDecSumAll', width: 150, sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'diskScanSumAll', label: 'diskScanSumAll', width: 190, sort: true, detailCol: getDiskScanDetailCol(), getDetailMethod: getDiskScanLogDetail, beforeLoadDetail: beforeLoad },
        { prop: 'outFileSumAll', label: 'outFileSumAll', width: 150, sort: true, detailTable: 'dwd_ldx_file_record', detailCol: getOutFileDetailCol() },
        { prop: 'fileBackupSumAll', label: 'fileBackupSumAll', width: 150, sort: true, detailTable: 'dwd_file_backup_record', detailCol: getFileBackupDetailCol() },
        { prop: 'encFileSumAll', label: 'encFileSumAll', width: '150', sort: true, detailTable: 'dwd_enc_dec_files_log', detailCol: getEncDecFilesLogCol(), beforeLoadDetail: beforeLoadEncOrDec }
      ]
    }
  },
  created() {
    this.filterData()
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.curSelected = customList
      this.defaultColModel = [this.defaultColModel[0], ...customList]
    },
    getCountByObject(value) {
      // if (value === 2) {
      //   const index = this.customList.findIndex(item => item.prop === 'trwfeSumAll')
      //   index >= 0 && this.customList.splice(index, 1)
      //   // 切换终端需要把审批数那列删除
      //   const index2 = this.curSelected.findIndex(item => item.prop === 'trwfeSumAll')
      //   index2 >= 0 && this.curSelected.splice(index2, 1)
      //   this.defaultColModel = [this.defaultColModel[0], ...this.curSelected]
      // } else if (this.hasPermission('E55')) {
      //   const index = this.customList.findIndex(item => item.prop === 'diskScanDecSumAll')
      //   this.customList.splice(index + 1, 0, { prop: 'trwfeSumAll', label: 'trwfeSumAll', width: '190', sort: true, value: 256, detailTable: 'dwd_act_hi_taskinst', detailCol: getTrwfeLogCol() })
      // }
    },
    /**
     * 权限过滤
     */
    filterData() {
      this.customList = filterDataSecurityLogCustom(this.customList)
      this.defaultColModel = filterDataSecurityLogDefault(this.defaultColModel, 'label')
    }
  }
}
</script>
