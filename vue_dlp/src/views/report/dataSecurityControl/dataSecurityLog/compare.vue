<template>
  <comparative-trend
    :api="'getSecurityCompareData'"
    :api-dept="'getSecurityCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
import { filterDataSecurityLogCustom } from '@/utils/reportPermissionFiltering'

export default {
  name: 'DataSecurityCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '数据安全',
      // 统计项设置 复选框传值（如果是文件大小或时长，需添加字段flag: 'size'或flag: 'time'）
      statisticalList: [
        { prop: 'encOrDecSumAll', label: 'encOrDecSumAll' },
        { prop: 'encSumAll', label: 'encSumAll' },
        { prop: 'decSumAll', label: 'decSumAll' },
        { prop: 'diskScanSumAll', label: 'diskScanSumAll' },
        { prop: 'diskScanSstSumAll', label: 'diskScanSstSumAll' },
        { prop: 'diskScanNonsstSumAll', label: 'diskScanNonsstSumAll' },
        { prop: 'diskScanEncSumAll', label: 'diskScanEncSumAll' },
        { prop: 'diskScanDecSumAll', label: 'diskScanDecSumAll' },
        { prop: 'outFileSumAll', label: 'outFileSumAll' },
        { prop: 'directOutFileSumAll', label: 'directOutFileSumAll' },
        { prop: 'directOutFileSizeSumAll', label: 'directOutFileSizeSumAll', flag: 'size' },
        { prop: 'applyOutFileSumAll', label: 'applyOutFileSumAll' },
        { prop: 'applyOutFileSizeSumAll', label: 'applyOutFileSizeSumAll', flag: 'size' },
        { prop: 'fileBackupSumAll', label: 'fileBackupSumAll' },
        { prop: 'fileBackupSizeSumAll', label: 'fileBackupSizeSumAll', flag: 'size' },
        { prop: 'encFileSumAll', label: 'encFileSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['encOrDecSumAll', 'diskScanSumAll', 'outFileSumAll', 'fileBackupSumAll', 'encFileSumAll']
    }
  },
  computed: {

  },
  watch: {
  },
  created() {
    this.filterData()
  },
  methods: {
    /**
     * 权限过滤
     */
    filterData() {
      this.statisticalList = filterDataSecurityLogCustom(this.statisticalList)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
