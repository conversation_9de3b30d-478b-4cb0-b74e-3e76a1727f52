<template>
  <comparative-trend
    :api="'getSmartBackupLogCompareData'"
    :api-dept="'getSmartBackupLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'SmartBackupLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '智能备份',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'fullScanCount', label: 'fullScanCount' },
        { prop: 'fullScanFileSize', label: 'fullScanFileSize', flag: 'size' },
        { prop: 'timedScanCount', label: 'timedScanCount' },
        { prop: 'timedScanFileSize', label: 'timedScanFileSize', flag: 'size' },
        { prop: 'manualBackupCount', label: 'manualBackupCount' },
        { prop: 'manualBackupFileSize', label: 'manualBackupFileSize', flag: 'size' },
        { prop: 'instantBackupCreateCount', label: 'instantBackupCreateCount' },
        { prop: 'instantBackupCreateFileSize', label: 'instantBackupCreateFileSize', flag: 'size' },
        { prop: 'instantBackupModifyCount', label: 'instantBackupModifyCount' },
        { prop: 'instantBackupModifyFileSize', label: 'instantBackupModifyFileSize', flag: 'size' },
        { prop: 'instantBackupRenameCount', label: 'instantBackupRenameCount' },
        { prop: 'instantBackupRenameFileSize', label: 'instantBackupRenameFileSize', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['fullScanCount', 'timedScanCount', 'manualBackupCount', 'instantBackupCreateCount', 'instantBackupModifyCount', 'instantBackupRenameCount']
    }
  }
}
</script>
