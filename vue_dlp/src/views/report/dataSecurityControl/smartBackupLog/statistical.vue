<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query">
        <i class="el-icon-s-home" style="position: absolute; left: 24px; top: 16px;"></i>
        <!--面包屑-->
        <div style="width: 510px; float: left;">
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <!--上方搜索条件-->
        <div style="float: right">
          <!--查询条件组件-->
          <query ref="queryData"/>
          <!--图表解读-->
          <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
        </div>
      </div>

      <!-- 页面无数据 -->
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>

      <!--分析初始页面-->
      <div v-if="inceptionDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
            <div class="icon">
              <svg-icon :icon-class="item.icon"></svg-icon>
            </div>
            <div class="content">
              <label :title="item.label">{{ item.label }}</label>
              <el-button type="text" :class="{'underline': item.clickable }" @click="item.func">{{ temp.itemValue[item.key] }}</el-button>
            </div>
          </div>
        </div>
        <!--中间文件备份业务类型统计--柱状图-->
        <div class="chart-panel">
          <div class="chart-item flex-2">
            <div class="mini-title"></div>
            <bar-chart
              :chart-data="temp.businessTypeBarChartData"
              :chart-option="temp.businessTypeBarChartOption"
              :click="barChartFun"
              :x-axis-name="''"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.backupType.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.backupType + '</div>'"></div>
            </div>
          </div>
        </div>
        <!--下方对部门，终端（操作员），数量趋势的分别展示部分-->
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleDeptAnalysis">部门智能备份分析>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ $t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigDept"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.deptBarChartData"
              :chart-option="temp.deptBarChartOption"
              :x-axis-name="this.$t('pages.dept')"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.deptBackup.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.deptBackup + '</div>'"></div>
            </div>
          </div>

          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleTerminalUserAnalysis">{{ terminalUserAnalysisTitle }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ $t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigTerminalUser"/>
              </el-tooltip>
            </div>
            <pie-chart
              :chart-data="temp.terminalUserPieChartData"
              :chart-option="temp.terminalUserPieChartOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.termOrUserBackup.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.termOrUserBackup + '</div>'"></div>
            </div>
          </div>

          <div class="chart-item flex-1">
            <div class="mini-title"><span @click="handleBackupTrendAnalysis">智能备份数量趋势分析>></span></div>
            <line-chart
              :chart-data="temp.trendLineChartData"
              :chart-option="temp.trendLineChartOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.backupTrend.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.backupTrend + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
      <!--部门分析-->
      <div v-if="deptDiv" class="detailsDiv">
        <analysis-dept
          :dialog-status="dialogStatus"
          chart-type="bar"
          chart-title="业务类型统计"
          detail-title="备份记录"
          :detail-table="detailTable"
          :dept-api="deptApi"
          :dept-analysis-api="deptAnalysisApi"
          :select-label="this.$t('table.bizType')"
          :select-map="businessTypeMap"
          :drill-down="drillDown"
          :specific-col="specificCol"
          :specific-detail-col="specificDetailCol"
          :select-list="selectList"
          :input-list="inputList"
        />
      </div>
      <!--终端、操作员分析-->
      <div v-if="userDiv" class="detailsDiv">
        <analysis-terminal-user
          :dialog-status="dialogStatus"
          chart-title="业务类型统计"
          detail-title="备份记录"
          :detail-table="detailTable"
          :terminal-user-api="terminalUserApi"
          :terminal-user-analysis-api="terminalUserAnalysisApi"
          :select-label="this.$t('table.bizType')"
          :select-map="businessTypeMap"
          :drill-down="drillDown"
          :specific-col="specificCol"
          :specific-detail-col="specificDetailCol"
          :select-list="selectList"
          :input-list="inputList"
        />
      </div>
      <!--备份数量趋势分析-->
      <div v-if="tendencyDiv" class="detailsDiv">
        <analysis-tendency chart-title="备份数量趋势" :trend-analysis-api="trendAnalysisApi"/>
      </div>
    </div>
    <!--小方块（备份总数）点击弹框-->
    <violate-detail-dlg ref="totalBackupDlg" dlg-title="备份记录" :detail-table="detailTable" :specific-col="specificDetailCol"/>
    <!--小方块（部门）点击弹框-->
    <dept-dlg ref="deptDlg" :data-api="deptApi" :specific-col="specificCol"/>
    <!--小方块（终端/操作员）点击击弹框-->
    <terminal-user-dlg ref="terminalUserDlg" :title="terminalUserDlgTitle" :data-api="terminalUserApi" :specific-col="specificCol"/>
    <!--业务类型（柱状统计图）点击弹框-->
    <violate-detail-dlg ref="businessTypeDlg" :dlg-title="businessTypeTitle" :detail-table="detailTable" :specific-col="specificDetailCol.slice(1)" :search-condition="searchCondition"/>
    <!--部门、终端、操作员前五名小图标点击弹框-->
    <echart-dlg ref="echartDlg" :title="echartDlgTitle" :dept-chart-api="deptChartApi" :terminal-user-chart-api="terminalUserChartApi"/>
  </div>
</template>

<script>
import { deepClone, formatFileSize, convert } from '@/utils'
import LineChart from '@/components/ECharts/LineChart'
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
import Query from '@/views/report/sensitiveControl/common/query'
import EchartDlg from '@/views/report/sensitiveControl/common/echartDlg'
import DeptDlg from '@/views/report/sensitiveControl/common/dlgDept'
import TerminalUserDlg from '@/views/report/sensitiveControl/common/dlgTerminalUser'
import ViolateDetailDlg from '@/views/report/sensitiveControl/common/dlgViolateDetail'
import analysisDept from '@/views/report/sensitiveControl/common/analysisDept'
import analysisTerminalUser from '@/views/report/sensitiveControl/common/analysisTerminalUser'
import analysisTendency from '@/views/report/sensitiveControl/common/analysisTendency'
import { getSmartBackupHomepageData, listSmartBackupDeptData, listSmartBackupTerminalUserData, getSmartBackupDeptChartData, getSmartBackupTerminalUserChartData, getSmartBackupDeptAnalysisData, getSmartBackupTerminalUserAnalysisData, getSmartBackupTrendAnalysisData } from '@/api/report/baseReport/sensitiveControl/smartBackup'

export default {
  name: 'SmartBackupLogStatistical',
  components: { Query, BarChart, LineChart, PieChart, DeptDlg, EchartDlg, TerminalUserDlg, ViolateDetailDlg, analysisDept, analysisTerminalUser, analysisTendency },
  props: {
  },
  data() {
    return {
      commonInterpretation: `<span>图表解读：</span>`,
      chartInterpretationContent: {
        backupTrend: '',
        backupType: '',
        deptBackup: '',
        termOrUserBackup: ''
      },
      // 搜查查询
      query: {
        // 是否展示图表解读
        isExplain: true,
        dimBaseType: 1,
        dimValue: '',
        countByObject: 2
      },
      tempQuery: {},
      terminalUserAnalysisTitle: '终端智能备份分析',
      // 面包屑显示数组
      showFilePath: [
        { id: 0, label: '智能备份分析' }
      ],
      // 暂无数据
      noData: true,
      // 初始页面显示隐藏
      inceptionDiv: false,
      // 操作员分析显示隐藏
      userDiv: false,
      // 部门分析显示隐藏
      deptDiv: false,
      // 趋势分析显示隐藏
      tendencyDiv: false,
      // 业务类型弹框名称
      businessTypeTitle: '',
      // 违规终端/操作员弹窗名称
      terminalUserDlgTitle: '',
      // 放大柱状图弹框名称
      echartDlgTitle: '',
      echartStatus: '',
      // 弹框状态
      dialogStatus: '',
      // 上方小方块
      itemOption: [
        { label: '备份总数', key: 'all', icon: 'total', func: this.totalBackupFun },
        { label: '备份大小', key: 'allSize', icon: 'total', func: this.totalBackupFun },
        { label: this.$t('table.dept'), key: 'dept', icon: 'tree', func: this.deptFun, clickable: true },
        { label: this.$t('table.terminal'), key: 'terminalUser', icon: 'terminal', func: this.terminalUserFun, clickable: true }
      ],
      commonChartOption: {
        title: {
          text: '',
          subtext: ''
        },
        toolbox: {
          show: false
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          // 控制分隔线
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          // 控制隔区域
          splitArea: {
            show: false
          }
        },
        series: [
          {
            data: [],
            type: 'bar'
          }
        ]
      },
      temp: {},
      defaultTemp: {
        itemValue: {
          all: '0',
          allSize: '0',
          dept: '0',
          terminalUser: '0'
        },
        // 业务类型柱状图
        businessTypeBarChartData: [],
        businessTypeBarChartOption: {},
        // 部门备份数量柱状图
        deptBarChartData: [],
        deptBarChartOption: {},
        // 终端/操作员备份数量饼图
        terminalUserPieChartData: [],
        terminalUserPieChartOption: {},
        // 备份数量趋势图
        trendLineChartData: [],
        trendLineChartOption: {}
      },
      businessTypeMap: {
        1: this.$t('pages.fullScan'),
        2: this.$t('pages.timedScan'),
        3: this.$t('pages.activeBackup'),
        4: this.$t('pages.instantBackupCreate'),
        5: this.$t('pages.instantBackupModify'),
        6: this.$t('pages.instantBackupRename')
      },
      backupResultMap: {
        0: this.$t('text.success'),
        1: this.$t('text.fail')
      },
      detailTable: 'dwd_smart_backup_log',
      specificDetailCol: [
        { prop: 'business_type', label: 'bizType', formatter: (row, data) => { return this.businessTypeMap[data] } },
        { prop: 'backup_result', label: this.$t('pages.backupResult'), formatter: (row, data) => { return data === 0 ? this.$t('text.success') : this.$t('text.fail') } },
        { prop: 'file_size', label: 'maxFileSize2', formatter: (row, data) => formatFileSize(data) },
        { prop: 'file_path', label: 'localFilePath' }
      ],
      specificCol: [
        { prop: 'backup_record_count', label: 'backupCount', sort: true },
        { prop: 'businessTypeStr', label: 'bizType', formatter: (row, data) => { return data.replace(/^,|,$/g, '').split(',').map(item => this.businessTypeMap[item]).join(',') } }
      ],
      searchCondition: {
        business_type: ''
      },
      // 明细数据输入框搜索条件
      inputList: [
        {
          inputLabel: this.$t('table.localFilePath'),
          inputKey: 'file_path'
        }
      ],
      deptApi: listSmartBackupDeptData,
      terminalUserApi: listSmartBackupTerminalUserData,
      deptChartApi: getSmartBackupDeptChartData,
      terminalUserChartApi: getSmartBackupTerminalUserChartData,
      deptAnalysisApi: getSmartBackupDeptAnalysisData,
      terminalUserAnalysisApi: getSmartBackupTerminalUserAnalysisData,
      trendAnalysisApi: getSmartBackupTrendAnalysisData,
      // 是否支持下钻
      drillDown: this.hasPermission('929')
    }
  },
  computed: {
    // 明细数据下拉框搜索条件
    selectList() {
      return [
        {
          // 标签名
          selectLabel: this.$t('table.bizType'),
          // 绑定字段名
          selectKey: 'business_type',
          // 下拉选项
          selectMap: this.businessTypeMap
        },
        {
          selectLabel: this.$t('pages.backupResult'),
          selectKey: 'backup_result',
          selectMap: this.backupResultMap
        }
      ]
    }
  },
  created() {
    this.$set(this.itemOption[0], 'clickable', this.drillDown)
    this.$set(this.itemOption[1], 'clickable', this.drillDown)
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.defaultTemp.businessTypeBarChartOption = this.getBusinessTypeBarChartOption()
      this.defaultTemp.deptBarChartOption = this.getDeptBarChartOption(5)
      this.defaultTemp.terminalUserPieChartOption = this.getTerminalUserPieChartOption(5)
      this.defaultTemp.terminalUserPieChartOption.title.text = this.query.countByObject === 1 ? '操作员智能备份分析' : '终端智能备份分析'
      this.defaultTemp.trendLineChartOption = this.getTrendLineChartOption()
      this.temp = deepClone(this.defaultTemp)
    },
    getData() {
      this.query = this.$refs['queryData'].getQuery()
      this.tempQuery = Object.assign({}, this.query)
      return getSmartBackupHomepageData(this.query).then(resp => {
        if (resp.data) {
          this.inceptionDiv = true
          this.noData = false
          // 图表解读
          if (resp.data.itemValueMap.explanation) {
            this.chartInterpretationContent = { ...this.chartInterpretationContent, ...resp.data.itemValueMap.explanation }
          } else {
            this.chartInterpretationContent = {
              backupTrend: '',
              backupType: '',
              deptBackup: '',
              termOrUserBackup: ''
            }
          }
          this.temp.itemValue.all = resp.data.itemValueMap.violateTotal
          this.temp.itemValue.allSize = convert(resp.data.itemValueMap.fileSizeTotal)
          this.temp.itemValue.dept = resp.data.itemValueMap.deptCount
          this.temp.itemValue.terminalUser = resp.data.itemValueMap.terminalUserCount

          this.temp.businessTypeBarChartData = resp.data.chartDataObjMap.businessTypeBarChartData.chartData
          this.temp.businessTypeBarChartOption.xAxis.data = resp.data.chartDataObjMap.businessTypeBarChartData.xaxisData
          this.temp.businessTypeBarChartOption.series[0].data = resp.data.chartDataObjMap.businessTypeBarChartData.seriesData

          this.temp.deptBarChartData = resp.data.chartDataObjMap.deptBarChartData.chartData
          this.temp.deptBarChartOption.xAxis.data = resp.data.chartDataObjMap.deptBarChartData.xaxisData
          this.temp.deptBarChartOption.series[0].data = resp.data.chartDataObjMap.deptBarChartData.seriesData

          this.temp.terminalUserPieChartData = resp.data.chartDataObjMap.terminalUserPieChartData.chartData

          this.temp.trendLineChartData = resp.data.chartDataObjMap.backupCountTrendLineCharData.chartData
          this.temp.trendLineChartOption.xAxis.data = resp.data.chartDataObjMap.backupCountTrendLineCharData.xaxisData
          this.temp.trendLineChartOption.series[0].data = resp.data.chartDataObjMap.backupCountTrendLineCharData.seriesData
        } else {
          this.resetTemp()
          this.inceptionDiv = false
          this.noData = true
        }
      }).then(() => {
        this.triggerResize()
      })
    },
    loadReport() {
      this.getData()
      if (this.query.countByObject === 1) {
        // 操作员
        this.itemOption[3].label = this.$t('table.user')
        this.terminalUserAnalysisTitle = '操作员智能备份分析'
        this.temp.terminalUserPieChartOption.title.text = '操作员智能备份统计'
      } else {
        // 终端
        this.itemOption[3].label = this.$t('table.terminal')
        this.terminalUserAnalysisTitle = '终端智能备份分析'
        this.temp.terminalUserPieChartOption.title.text = '终端智能备份统计'
      }
      // 操作员（终端）页面
      this.userDiv = false
      // 部门页面
      this.deptDiv = false
      // 趋势页面
      this.tendencyDiv = false
      this.showFilePath = [{ id: 0, label: '智能备份分析' }]
    },
    getDeptBarChartOption(top) {
      const commonChartOption = deepClone(this.commonChartOption)
      return {
        ...commonChartOption,
        title: {
          text: '部门智能备份统计',
          subtext: this.$t('pages.topN', { num: top })
        }
      }
    },
    getTerminalUserPieChartOption(top) {
      return {
        title: {
          text: '',
          subtext: this.$t('pages.topN', { num: top })
        },
        toolbox: {
          show: false
        },
        legend: {
          show: false,
          left: 'center'
        },
        series: [
          {
            radius: '40%',
            center: ['50%', '50%']
          }
        ]
      }
    },
    getBusinessTypeBarChartOption() {
      const commonChartOption = deepClone(this.commonChartOption)
      return {
        ...commonChartOption,
        title: {
          text: '业务类型统计'
        },
        xAxis: {
          ...commonChartOption.xAxis,
          axisLabel: {
            interval: 0,
            rotate: -10
          }
        }
      }
    },
    getTrendLineChartOption() {
      const commonChartOption = deepClone(this.commonChartOption)
      return {
        ...commonChartOption,
        title: {
          text: '备份数量趋势'
        },
        series: [
          {
            ...commonChartOption.series[0],
            type: 'line'
          }
        ]
      }
    },
    /**
     * 备份总数(点击)
     */
    totalBackupFun() {
      if (this.drillDown) {
        this.$refs.totalBackupDlg.show()
      }
    },
    /**
     * 部门（点击）
     */
    deptFun() {
      this.$refs.deptDlg.show()
    },
    /**
     * 终端或操作员（点击）
     */
    terminalUserFun() {
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.terminalUserDlgTitle = '操作员'
      } else {
        // 终端
        this.terminalUserDlgTitle = '终端'
      }
      this.$refs.terminalUserDlg.show()
    },
    /**
     * 业务类型（点击）
     */
    barChartFun(params) {
      if (this.drillDown && params.name) {
        this.businessTypeTitle = params.name
        this.searchCondition.business_type = Object.keys(this.businessTypeMap).find(k => this.businessTypeMap[k] === params.name)
        this.$refs.businessTypeDlg.show()
      }
    },
    /**
     * 部门分析小图标点击
     * */
    handleBigDept() {
      this.echartDlgTitle = '部门智能备份统计'
      this.echartStatus = 'dept'
      this.$refs.echartDlg.show(this.echartStatus)
    },
    /**
     * 终端操作员小图标点击
     * */
    handleBigTerminalUser() {
      if (this.tempQuery.countByObject === 2) {
        this.echartDlgTitle = '终端智能备份统计'
        this.echartStatus = 'terminal'
      } else {
        this.echartDlgTitle = '操作员智能备份统计'
        this.echartStatus = 'user'
      }
      this.$refs.echartDlg.show(this.echartStatus, this.echartDlgTitle)
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    handleDeptAnalysis() {
      this.inceptionDiv = false
      this.deptDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 2, label: '部门智能备份分析' })
    },
    /**
     * 终端（操作员）分析点击
     * 显示终端（操作员）分析，面包屑中添加终端（操作员）分析
     * */
    handleTerminalUserAnalysis() {
      this.inceptionDiv = false
      this.userDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
        this.showFilePath.push({ id: 1, label: '操作员智能备份分析' })
      } else {
        // 终端
        this.dialogStatus = 'terminal'
        this.showFilePath.push({ id: 1, label: '终端智能备份分析' })
      }
    },
    /**
     * 数量趋势点击
     * 显示数量趋势，面包屑中添加数量趋势
     * */
    handleBackupTrendAnalysis() {
      this.inceptionDiv = false
      this.tendencyDiv = true
      this.showFilePath.push({ id: 3, label: '智能备份数量趋势分析' })
    },
    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        // 初始页面
        this.inceptionDiv = true
        // 操作员（终端）页面
        this.userDiv = false
        // 部门页面
        this.deptDiv = false
        // 趋势页面
        this.tendencyDiv = false
        this.showFilePath = [{ id: 0, label: '智能备份分析' }]
      }
      if (index === 1) {
        this.inceptionDiv = false
        this.userDiv = true
        this.deptDiv = false
        this.tendencyDiv = false
      }
      if (index === 2) {
        this.inceptionDiv = false
        this.userDiv = false
        this.deptDiv = true
        this.tendencyDiv = false
      }
      if (index === 3) {
        this.inceptionDiv = false
        this.userDiv = false
        this.deptDiv = false
        this.tendencyDiv = true
      }
    }
  }
}
</script>

<style lang='scss' scoped>
.underlineClass{
  >>>.el-button--text{
    text-decoration: underline;
  }
}
</style>
