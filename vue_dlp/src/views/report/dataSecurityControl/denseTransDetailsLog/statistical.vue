<template>
  <Statistical :api="'getDenseTransDetailsLogPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('563')" :exportable="hasPermission('564')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getSecurityLevelConvertNumDetailCol } from '@/utils/report'

export default {
  name: 'DenseTransDetailsLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'securityLevelConvertCountSum', label: 'securityLevelConvertCountSum', width: '150', sort: true, detailTable: 'dwd_dense_trans_details_log', detailCol: getSecurityLevelConvertNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'securityLevelConvertCount', label: 'securityLevelConvertCountSum', width: '150', sort: true, detailTable: 'dwd_dense_trans_details_log', detailCol: getSecurityLevelConvertNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'securityLevelConvertCountSum', label: 'securityLevelConvertCountSum', width: '150', sort: true, detailTable: 'dwd_dense_trans_details_log', detailCol: getSecurityLevelConvertNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'opResultName', label: 'opResult', width: '150', sort: true, joinSearch: true, searchCol: 'op_result', searchProp: 'opResult' },
        { prop: 'securityLevelConvertCount', label: 'securityLevelConvertCountSum', width: '150', sort: true, detailTable: 'dwd_dense_trans_details_log', detailCol: getSecurityLevelConvertNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
