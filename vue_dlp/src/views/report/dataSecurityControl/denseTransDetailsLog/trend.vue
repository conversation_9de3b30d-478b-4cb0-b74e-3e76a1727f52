<template>
  <Trend :api="'getDenseTransDetailsLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('566')" :exportable="hasPermission('567')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getSecurityLevelConvertNumDetailCol } from '@/utils/report'

export default {
  name: 'DenseTransDetailsLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'securityLevelConvertCountSum', label: 'securityLevelConvertCountSum', width: '150', sort: true, detailTable: 'dwd_dense_trans_details_log', detailCol: getSecurityLevelConvertNumDetailCol() }
      ],
      customList: [
        { prop: 'securityLevelConvertCountSum', label: 'securityLevelConvertCountSum', width: '150', sort: true, detailTable: 'dwd_dense_trans_details_log', detailCol: getSecurityLevelConvertNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
