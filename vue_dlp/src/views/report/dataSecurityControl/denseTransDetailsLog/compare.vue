<template>
  <comparative-trend
    :api="'getDenseTransDetailsLogCompareData'"
    :api-dept="'getDenseTransDetailsLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'DenseTransDetailsLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '密级转换',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'securityLevelConvertCountSum', label: 'securityLevelConvertCountSum' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['securityLevelConvertCountSum']
    }
  }
}
</script>
