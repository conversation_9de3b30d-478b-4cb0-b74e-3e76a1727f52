<template>
  <Statistical
    :api="'getCurTaskInfoLogPie'"
    :default-col-model="defaultColModel"
    :sub-col-model="subColModel"
    :custom-list="customList"
    :drill-down="hasPermission('800')"
    :exportable="hasPermission('801')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { getAppUninstallNumDetailCol } from '@/utils/report'

export default {
  name: 'AppUninstallStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'fileNameNumSumAll', label: 'fileNameNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_cur_task_info_log', detailCol: getAppUninstallNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'fileNameNumSum', label: 'fileNameNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_cur_task_info_log', detailCol: getAppUninstallNumDetailCol() }],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'fileNameNumSumAll', label: 'fileNameNumSumAll', width: '150', sort: true, detailTable: 'dwd_cur_task_info_log', detailCol: getAppUninstallNumDetailCol() }],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'taskStatusName', label: 'result', width: '150', sort: true, joinSearch: true, searchCol: 'task_status', searchProp: 'taskStatus' },
        { prop: 'fileNameNumSum', label: 'fileNameNumSumAll', width: '150', sort: true, detailTable: 'dwd_cur_task_info_log', detailCol: getAppUninstallNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>

