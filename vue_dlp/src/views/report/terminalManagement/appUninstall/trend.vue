<template>
  <Trend :api="'getCurTaskInfoLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('805')" :exportable="hasPermission('806')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getAppUninstallNumDetailCol } from '@/utils/report'

export default {
  name: 'AppUninstallTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'fileNameNumSumAll', label: 'fileNameNumSumAll', width: '150', sort: true, detailTable: 'dwd_cur_task_info_log', detailCol: getAppUninstallNumDetailCol() }
      ],
      customList: [
        { prop: 'fileNameNumSumAll', label: 'fileNameNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_cur_task_info_log', detailCol: getAppUninstallNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>

