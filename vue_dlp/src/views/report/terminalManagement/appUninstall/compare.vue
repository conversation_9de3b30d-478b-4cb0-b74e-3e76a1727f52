<template>
  <comparative-trend
    :api="'getCurTaskInfoLogCompareData'"
    :api-dept="'getCurTaskInfoLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'AppUninstallCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '软件卸载数量',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'fileNameNumSumAll', label: 'fileNameNumSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['fileNameNumSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
