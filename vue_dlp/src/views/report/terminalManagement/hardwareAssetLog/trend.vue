<template>
  <Trend
    :api="'getHardwareAssetTrend'"
    :col-model="colModel"
    :custom-list="customList"
    :drill-down="hasPermission('755')"
    :exportable="hasPermission('756')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Trend from '../../component/Trend'
import { getAssetLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getHardwareAssetChangeTimesDetailCol, beforeLoadHardwareAssetChangeTimesDetail } from '@/utils/report'

export default {
  name: 'HardwareTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '120', sort: true, sortOriginal: true },
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'cpuChangeTimesSumAll', label: 'cpuChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'moboChangeTimesSumAll', label: 'moboChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'vcChangeTimesSumAll', label: 'vcChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'hddChangeTimesSumAll', label: 'hddChangeTimesSumAll', width: '150', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'ramChangeTimesSumAll', label: 'ramChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'otherChangeTimesSumAll', label: 'otherChangeTimesSumAll', width: '150', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail }
      ],
      customList: [
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll', width: '120', sort: true, value: 1, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'cpuChangeTimesSumAll', label: 'cpuChangeTimesSumAll', width: '120', sort: true, value: 2, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'moboChangeTimesSumAll', label: 'moboChangeTimesSumAll', width: '120', sort: true, value: 4, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'vcChangeTimesSumAll', label: 'vcChangeTimesSumAll', width: '120', sort: true, value: 8, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'hddChangeTimesSumAll', label: 'hddChangeTimesSumAll', width: '120', sort: true, value: 16, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'ramChangeTimesSumAll', label: 'ramChangeTimesSumAll', width: '120', sort: true, value: 32, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'otherChangeTimesSumAll', label: 'otherChangeTimesSumAll', width: '120', sort: true, value: 64, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
