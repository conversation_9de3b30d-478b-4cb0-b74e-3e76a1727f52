<template>
  <Statistical
    :api="'getHardwareAssetPie'"
    :default-col-model="defaultColModel"
    :sub-col-model="subColModel"
    :custom-list="customList"
    :only-terminal="true"
    :drill-down="hasPermission('750')"
    :exportable="hasPermission('751')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { getAssetLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getHardwareAssetChangeTimesDetailCol, beforeLoadHardwareAssetChangeTimesDetail } from '@/utils/report'

export default {
  name: 'HardwareStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll', width: '120', sort: true, value: 1, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'cpuChangeTimesSumAll', label: 'cpuChangeTimesSumAll', width: '120', sort: true, value: 2, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'moboChangeTimesSumAll', label: 'moboChangeTimesSumAll', width: '120', sort: true, value: 4, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'vcChangeTimesSumAll', label: 'vcChangeTimesSumAll', width: '120', sort: true, value: 8, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'hddChangeTimesSumAll', label: 'hddChangeTimesSumAll', width: '120', sort: true, value: 16, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'ramChangeTimesSumAll', label: 'ramChangeTimesSumAll', width: '120', sort: true, value: 32, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'otherChangeTimesSumAll', label: 'otherChangeTimesSumAll', width: '120', sort: true, value: 64, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail }
      ],
      subCustomList: [
        { prop: 'changeTimesSum', label: 'changeTimesSumAll', width: '120', sort: true, value: 1, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'cpuChangeTimesSum', label: 'cpuChangeTimesSumAll', width: '120', sort: true, value: 2, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'moboChangeTimesSum', label: 'moboChangeTimesSumAll', width: '120', sort: true, value: 4, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'vcChangeTimesSum', label: 'vcChangeTimesSumAll', width: '120', sort: true, value: 8, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'hddChangeTimesSum', label: 'hddChangeTimesSumAll', width: '150', sort: true, value: 16, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'ramChangeTimesSum', label: 'ramChangeTimesSumAll', width: '120', sort: true, value: 32, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'otherChangeTimesSum', label: 'otherChangeTimesSumAll', width: '150', sort: true, value: 64, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'terminal', width: '120', sort: true },
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'cpuChangeTimesSumAll', label: 'cpuChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'moboChangeTimesSumAll', label: 'moboChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'vcChangeTimesSumAll', label: 'vcChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'hddChangeTimesSumAll', label: 'hddChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'ramChangeTimesSumAll', label: 'ramChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'otherChangeTimesSumAll', label: 'otherChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'terminal', width: '120', sort: true },
        { prop: 'logTypeName', label: 'eventType', width: '150', sort: true, joinSearch: true, searchCol: 'log_type', searchProp: 'logType' },
        { prop: 'changeTimesSum', label: 'changeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'cpuChangeTimesSum', label: 'cpuChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'moboChangeTimesSum', label: 'moboChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'vcChangeTimesSum', label: 'vcChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'hddChangeTimesSum', label: 'hddChangeTimesSumAll', width: '150', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'ramChangeTimesSum', label: 'ramChangeTimesSumAll', width: '120', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail },
        { prop: 'otherChangeTimesSum', label: 'otherChangeTimesSumAll', width: '150', sort: true, detailCol: getHardwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadHardwareAssetChangeTimesDetail }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
