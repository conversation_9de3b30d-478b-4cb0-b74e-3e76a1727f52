<template>
  <comparative-trend
    :api="'getHardwareAssetLogCompareData'"
    :api-dept="'getHardwareAssetLogCompareDeptData'"
    :chart-title="chartTitle"
    :only-user="true"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'HardwareCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '硬件资产变更',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll' },
        { prop: 'cpuChangeTimesSumAll', label: 'cpuChangeTimesSumAll' },
        { prop: 'moboChangeTimesSumAll', label: 'moboChangeTimesSumAll' },
        { prop: 'vcChangeTimesSumAll', label: 'vcChangeTimesSumAll' },
        { prop: 'hddChangeTimesSumAll', label: 'hddChangeTimesSumAll' },
        { prop: 'ramChangeTimesSumAll', label: 'ramChangeTimesSumAll' },
        { prop: 'otherChangeTimesSumAll', label: 'otherChangeTimesSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['changeTimesSumAll', 'cpuChangeTimesSumAll', 'moboChangeTimesSumAll', 'vcChangeTimesSumAll', 'hddChangeTimesSumAll', 'ramChangeTimesSumAll', 'otherChangeTimesSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
