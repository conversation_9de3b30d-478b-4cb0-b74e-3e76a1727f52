<template>
  <Trend
    :api="'getSoftwareAssetTrend'"
    :col-model="colModel"
    :custom-list="customList"
    :drill-down="hasPermission('765')"
    :exportable="hasPermission('766')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Trend from '../../component/Trend'
import { getAssetLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getSoftwareAssetChangeTimesDetailCol, beforeLoadSoftwareAssetChangeTimesDetail } from '@/utils/report'

export default {
  name: 'SoftwareTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'osChangeTimesSumAll', label: 'osChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'avChangeTimesSumAll', label: 'avChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'spChangeTimesSumAll', label: 'spChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'appChangeTimesSumAll', label: 'appChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail }
      ],
      customList: [
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll', width: '150', sort: true, value: 1, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'osChangeTimesSumAll', label: 'osChangeTimesSumAll', width: '150', sort: true, value: 2, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'avChangeTimesSumAll', label: 'avChangeTimesSumAll', width: '150', sort: true, value: 4, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'spChangeTimesSumAll', label: 'spChangeTimesSumAll', width: '150', sort: true, value: 8, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'appChangeTimesSumAll', label: 'appChangeTimesSumAll', width: '150', sort: true, value: 16, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
