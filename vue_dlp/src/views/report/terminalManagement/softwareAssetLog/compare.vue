<template>
  <comparative-trend
    :api="'getSoftwareAssetLogCompareData'"
    :api-dept="'getSoftwareAssetLogCompareDeptData'"
    :chart-title="chartTitle"
    :only-user="true"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'SoftwareCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '软件资产变更',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll' },
        { prop: 'osChangeTimesSumAll', label: 'osChangeTimesSumAll' },
        { prop: 'avChangeTimesSumAll', label: 'avChangeTimesSumAll' },
        { prop: 'spChangeTimesSumAll', label: 'spChangeTimesSumAll' },
        { prop: 'appChangeTimesSumAll', label: 'appChangeTimesSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['changeTimesSumAll', 'osChangeTimesSumAll', 'avChangeTimesSumAll', 'spChangeTimesSumAll', 'appChangeTimesSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
