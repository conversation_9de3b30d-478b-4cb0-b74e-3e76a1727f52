<template>
  <Statistical
    :api="'getSoftwareAssetPie'"
    :default-col-model="defaultColModel"
    :sub-col-model="subColModel"
    :custom-list="customList"
    :only-terminal="true"
    :drill-down="hasPermission('760')"
    :exportable="hasPermission('761')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { getAssetLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getSoftwareAssetChangeTimesDetailCol, beforeLoadSoftwareAssetChangeTimesDetail } from '@/utils/report'

export default {
  name: 'SoftwareStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll', width: '150', sort: true, value: 1, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'osChangeTimesSumAll', label: 'osChangeTimesSumAll', width: '150', sort: true, value: 2, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'avChangeTimesSumAll', label: 'avChangeTimesSumAll', width: '150', sort: true, value: 4, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'spChangeTimesSumAll', label: 'spChangeTimesSumAll', width: '150', sort: true, value: 8, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'appChangeTimesSumAll', label: 'appChangeTimesSumAll', width: '150', sort: true, value: 16, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail }
      ],
      subCustomList: [
        { prop: 'changeTimesSum', label: 'changeTimesSumAll', width: '150', sort: true, value: 1, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'osChangeTimesSum', label: 'osChangeTimesSumAll', width: '150', sort: true, value: 2, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'avChangeTimesSum', label: 'avChangeTimesSumAll', width: '150', sort: true, value: 4, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'spChangeTimesSum', label: 'spChangeTimesSumAll', width: '150', sort: true, value: 8, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'appChangeTimesSum', label: 'appChangeTimesSumAll', width: '150', sort: true, value: 16, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'terminal', width: '150', sort: true },
        { prop: 'changeTimesSumAll', label: 'changeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'osChangeTimesSumAll', label: 'osChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'avChangeTimesSumAll', label: 'avChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'spChangeTimesSumAll', label: 'spChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'appChangeTimesSumAll', label: 'appChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'terminal', width: '150', sort: true },
        { prop: 'logTypeName', label: 'eventType', width: '150', sort: true, joinSearch: true, searchCol: 'log_type', searchProp: 'logType' },
        { prop: 'changeTimesSum', label: 'changeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'osChangeTimesSum', label: 'osChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'avChangeTimesSum', label: 'avChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'spChangeTimesSum', label: 'spChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail },
        { prop: 'appChangeTimesSum', label: 'appChangeTimesSumAll', width: '150', sort: true, detailCol: getSoftwareAssetChangeTimesDetailCol(), getDetailMethod: getAssetLogDetail, beforeLoadDetail: beforeLoadSoftwareAssetChangeTimesDetail }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
