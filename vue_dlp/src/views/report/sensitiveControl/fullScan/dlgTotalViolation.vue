<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :fullscreen="fullscreen"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="squareList"
        :stripe="false"
        row-key="id"
        :col-model="colModel"
        :row-data-api="rowDetailDataApi"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        default-expand-all
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {
  getDiskScanSensitiveDetailPage
} from '@/api/report/baseReport/sensitiveControl/diskScanSensitive';

export default {
  name: 'DlgTotalViolation',
  components: { },
  props: {
    searchCondition: { // 任务编号
      type: Object,
      default() {
        return {
          guid: '',
          batchNo: ''
        }
      }
    }
  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      severityOptions: {
        4: this.$t('pages.severityOptions4'),
        3: this.$t('pages.severityOptions3'),
        2: this.$t('pages.severityOptions2'),
        1: this.$t('pages.severityOptions1')
      },
      ruleTypeOptions: {
        1: this.$t('route.file'),
        2: this.$t('route.keyword'),
        3: this.$t('route.regular'),
        4: this.$t('route.edm'),
        5: this.$t('route.vml'),
        6: this.$t('route.fileFp'),
        12: this.$t('route.sourceCode'),
        224: this.$t('table.contentTimeoutSumAll'),
        225: this.$t('table.filePwdSensitiveSumAll')
      },
      dialogVisible: false,
      submitting: false,
      rowData: [],
      title: '',
      // 查询条件
      query: {
        page: 1,
        limit: 20,
        severity: null,
        searchInfo: undefined
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['deviceList']
    },
    colModel() {
      return [
        { prop: 'guid', label: 'taskNum' },
        { prop: 'terminalName', label: 'terminalName' },
        { prop: 'termGroupName', label: 'terminalGroup' },
        { prop: 'severity', label: 'severity', formatter: this.severityFormatter },
        { prop: 'ruleType', label: 'ruleType', formatter: this.ruleTypeFormatter },
        { prop: 'ruleName', label: 'ruleName' },
        { prop: 'fileName', label: 'fileName' }
      ]
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    severityFormatter: function(row, data) {
      return this.severityOptions[data]
    },
    ruleTypeFormatter: function(row, data) {
      return this.ruleTypeOptions[data]
    },
    rowDetailDataApi(option) {
      const searchQuery = Object.assign({}, this.searchCondition, this.query, option)
      searchQuery.searchCount = true
      return getDiskScanSensitiveDetailPage(searchQuery)
    },
    /**
       * 显示弹框
       */
    show(name) {
      this.resetTemp()
      this.dialogStatus = name
      if (name === 1) {
        this.query.severity = null
        this.title = this.$t('report.violationFileNum')
      } else if (name === 2) {
        this.query.severity = 4
        this.title = this.$t('report.verySeriousFileNum')
      } else {
        this.query.severity = 3
        this.title = this.$t('report.seriousFileNum')
      }
      this.dialogVisible = true
      this.query.page = 1
      this.$refs.squareList.execRowDataApi(this.query)
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.submitting = false
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

