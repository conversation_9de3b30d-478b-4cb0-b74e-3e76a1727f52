<template>
  <!--规则分析-->
  <div class="data-details sensitiveness-report">
    <div class="data-analysis-table table-parent-size">
      <div class="table-container table-left-size">
        <grid-table
          ref="deptList"
          row-key="id"
          :col-model="colModel"
          :row-datas="ruleDataList"
          :show-pager="false"
          :multi-select="false"
          :autoload="false"
          @currentChange="currentChangeLeft"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--规则分析上方几个小方块-->
      <div class="data-panel">
        <div v-for="(item, key) in temp.itemOption" :key="key" class="data-item data-item-details">
          <div class="icon">
            <svg-icon :icon-class="item.ico"></svg-icon>
          </div>
          <div class="content">
            <label :title="item.label">{{ item.label }}</label>
            <span :title="item.value">{{ item.value }}</span>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 127px); padding: 0 20px;position: relative">
        <!--右侧列表查询条件-->
        <el-popover
          :disabled="detailQuery.ruleType==null"
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchRightForm" label-position="right" :model="detailQuery" label-width="80px">
            <FormItem :label="$t('pages.severity')">
              <el-select v-model="detailQuery.severity" style="width: 244px" clearable>
                <el-option v-for="(label, value) in severityOptions" :key="value" :label="label" :value="value"></el-option>
              </el-select>
            </FormItem>
            <FormItem :label="$t('pages.ruleName')">
              <el-input v-model="detailQuery.ruleName" clearable maxlength="100"/>
            </FormItem>
            <FormItem :label="$t('pages.fileName')">
              <el-input v-model="detailQuery.fileName" clearable maxlength="100"/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetAdvancedQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="searchBtn()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button v-show="isShowGrid" slot="reference" type="primary" icon="el-icon-search" class="search-icon-right" size="mini"></el-button>
        </el-popover>
        <el-tabs v-model="activeDeptName" @tab-click="tabClick">
          <el-tab-pane v-if="drillDown" :label="$t('report.ruleTypeInfo')" name="deptMsg" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                v-show="isShowGrid"
                ref="deptMsgList"
                row-key="id"
                :autoload="false"
                :col-model="colModelDeptMsg"
                :row-data-api="rowDetailDataApi"
                :multi-select="false"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('report.ruleNamePercent')" name="deptStatistics">
            <pie-chart
              v-show="isShowChart"
              ref="pieChart"
              :chart-data="temp.chartsDatas"
              :chart-option="temp.chartsOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from '@/components/ECharts/PieChart'
import {
  getDiskScanSensitiveRuleList,
  getDiskScanSensitiveDetailPage,
  getDiskScanSensitiveRuleNamePie
} from '@/api/report/baseReport/sensitiveControl/diskScanSensitive'
import { checkDiskScanDetailAble } from '@/api/report/baseReport/issueReport/issue'

export default {
  name: 'AnalysisRule',
  components: { PieChart },
  props: {
    searchCondition: { // 任务编号
      type: Object,
      default() {
        return {
          guid: '',
          batchNo: ''
        }
      }
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      query: {
        page: 1,
        limit: 20
      },
      detailQuery: {},
      DefaultDetailQuery: {
        page: 1,
        limit: 20,
        ruleType: null,
        severity: '',
        ruleName: '',
        fileName: ''
      },
      colModel: [
        { prop: 'name', label: 'ruleType' },
        { prop: 'value', label: this.$t('report.sensitiveRuleNum') }
      ],
      ruleDataList: [],
      // 左侧列表
      leftListRow: [],
      temp: {},
      defaultTemp: {
        // 小方块
        itemOption: [
          { label: '', value: '', ico: 'algorithm' },
          { label: this.$t('report.sensitiveFileNum'), value: '', ico: 'ruleGroup' }
        ],
        // 饼图 规则类型统计
        chartsDatas: [
        ],
        chartsOption: {
          title: {
            text: this.$t('report.ruleNamePercent'),
            left: 'center',
            'subtext': ''
          },
          toolbox: {
            show: false
          },
          legend: {
            left: 'center'
          },
          series: [
            {
              radius: '25%'
            }
          ]
        }
      },
      // 规则分析
      activeDeptName: '',
      isShowGrid: this.drillDown,
      isShowChart: !this.drillDown,
      colModelDeptMsg: [
        { prop: 'guid', label: 'taskNum' },
        { prop: 'terminalName', label: 'terminalName' },
        { prop: 'termGroupName', label: 'terminalGroup' },
        { prop: 'severity', label: 'severity', formatter: this.severityFormatter },
        { prop: 'ruleType', label: 'ruleType', formatter: this.ruleTypeFormatter },
        { prop: 'ruleName', label: 'ruleName' },
        { prop: 'fileName', label: 'fileName' }
      ],
      severityOptions: {
        4: this.$t('pages.severityOptions4'),
        3: this.$t('pages.severityOptions3'),
        2: this.$t('pages.severityOptions2'),
        1: this.$t('pages.severityOptions1')
      },
      ruleTypeOptions: {
        1: this.$t('route.file'),
        2: this.$t('route.keyword'),
        3: this.$t('route.regular'),
        4: this.$t('route.edm'),
        5: this.$t('route.vml'),
        6: this.$t('route.fileFp'),
        12: this.$t('route.sourceCode'),
        224: this.$t('table.contentTimeoutSumAll'),
        225: this.$t('table.filePwdSensitiveSumAll')
      }
    }
  },
  computed: {

  },
  created() {
    this.activeDeptName = this.drillDown ? 'deptMsg' : 'deptStatistics'
    this.resetTemp()
    this.getRuleDataList()
  },
  async mounted() {
    await this.$refs['deptList'].execRowDataApi()
    const tableData = this.$refs['deptList'].getDatas()
    // 如果左侧列表长度不为0，初始化页面时显示第一条数据的详情
    if (tableData[0]) {
      this.currentChangeLeft(tableData[0])
    }
  },
  activated() {},
  methods: {
    /**
     *右边表格搜索
     * */
    handleFilterRight() {
      this.detailQuery.page = 1
      // console.log('query', this.query)
      this.$refs['deptMsgList'].execRowDataApi(this.query)
    },
    /**
     *高级查询弹框显示
     * */
    handleAdvanceQuery() {
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
    },
    /**
     * 高级查询，查询操作
     * */
    searchBtn() {
      this.detailQuery.page = 1
      this.$refs['deptMsgList'].execRowDataApi(this.detailQuery)
    },
    /**
     *重置《高级查询重置按钮》
     * */
    resetAdvancedQuery() {
      this.detailQuery.severity = null
      this.detailQuery.ruleName = ''
      this.detailQuery.fileName = ''
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    severityFormatter: function(row, data) {
      return this.severityOptions[data]
    },
    ruleTypeFormatter: function(row, data) {
      return this.ruleTypeOptions[data]
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
    },
    /**
     * 左侧表格数据
     * */
    getRuleDataList() {
      getDiskScanSensitiveRuleList(this.searchCondition).then(res => {
        this.ruleDataList = res.data
        if (this.ruleDataList.length > 0) {
          this.currentChangeLeft(this.ruleDataList[0])
        } else {
          this.resetTemp()
        }
      })
    },
    rowDetailDataApi(option) {
      const searchQuery = Object.assign({}, this.searchCondition, this.detailQuery, option)
      searchQuery.searchCount = true
      return getDiskScanSensitiveDetailPage(searchQuery)
    },
    /**
     * 获取左边列表行点击时的信息（右边的详细信息）
     * @param row 当前行信息
     * */
    currentChangeLeft(row) {
      this.resetTemp()
      this.temp.itemOption[0].label = row.name
      this.temp.itemOption[1].value = row.value
      this.detailQuery.page = 1
      this.detailQuery.ruleType = row.id
      if (this.drillDown) {
        this.$refs.deptMsgList.clearRowData()
        this.$refs.deptMsgList.clearPageData()
        checkDiskScanDetailAble({ guid: this.searchCondition.guid }).then(() => {
          this.$refs.deptMsgList.execRowDataApi(this.detailQuery)
        })
      }
      // 获取终端敏感数量饼图
      // 获取终端敏感数量饼图
      const searchQuery = Object.assign({}, this.searchCondition, this.detailQuery)
      getDiskScanSensitiveRuleNamePie(searchQuery).then(res => {
        this.temp.chartsDatas = res.data
      })
    },
    /**
       * tab点击事件，如果不写，切换时图表只有小小一点点
       * @param pane
       * @param event
       */
    tabClick(pane, event) {
      if (pane.name === 'deptMsg') {
        this.isShowGrid = true
        this.isShowChart = false
      } else {
        this.isShowGrid = false
        this.isShowChart = true
        this.$refs['pieChart'].__resizeHandler()
      }
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
