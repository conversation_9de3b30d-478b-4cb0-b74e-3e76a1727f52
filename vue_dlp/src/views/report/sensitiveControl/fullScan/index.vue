<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query">
        <i class="el-icon-s-home" style="position: absolute; left: 24px; top: 16px;"></i>
        <!--面包屑-->
        <div style="width: 510px; float: left;">
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <!--上方搜索条件-->
        <div style="float: right;height: 40px">
          <Form ref="searchForm" :model="query" :rules="rules" label-width="120px" label-position="right">
            <FormItem :label="$t('report.fullScanTask')" prop="guid">
              <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
              <el-select v-model="query.guid" style="width: 230px" @change="taskChange">
                <el-option v-for="option in guidOptions" :key="option.key" :value="option.key" :label="option.name"/>
              </el-select>

              <el-select v-if="showOption2" v-model="query.batchNo" style="width: 150px">
                <el-option v-for="option in batchNoOptions" :key="option.key" :value="option.key" :label="option.name.split('_')[1]"/>
              </el-select>
              <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
              <!-- <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
              <el-button type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>-->
            </FormItem>
          </Form>
        </div>
      </div>

      <!-- 页面无数据 -->
      <div v-if="noDataFlag" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>

      <!--分析初始页面-->
      <div v-if="InceptionDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
            <div class="icon">
              <svg-icon :icon-class="item.icon"></svg-icon>
            </div>
            <div class="content">
              <label :title="item.label">{{ item.label }}</label>
              <el-button type="text" :class="{'underline': item.clickable }" @click="item.func">{{ temp.itemValue[item.key] }}</el-button>
            </div>
          </div>
        </div>
        <!--中间敏感文件分析柱状图-->
        <div class="chart-panel">
          <div class="chart-item flex-2">
            <div class="mini-title"><span @click="nodeTitleClick">{{ $t('report.termSensitiveFileAnalysis') }}>></span></div>
            <bar-chart
              ref="termSensitiveBarChart"
              :chart-data="temp.barChartDivulgeData"
              :chart-option="temp.barChartDivulgeOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.termSensitiveBar.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title">
                解读：<span style="color: #909399;">通过各终端敏感文件数量对比，建立安全基线，定位超标终端。</span>
              </div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.termSensitiveBar + '</div>'"></div>
            </div>
          </div>
        </div>
        <!--下方对部门，终端（操作员），数量趋势的分别展示部分-->
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="deptTitleClick">{{ $t('report.groupSensitiveFileAnalysis') }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigDept"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.barChartData"
              :chart-option="temp.barChartOption"
              :x-axis-name="this.$t('pages.dept')"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.deptDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.deptDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title"><span @click="ruleTitleClick">{{ $t('report.sensitiveFileRuleAnalysis') }}>></span></div>
            <pie-chart
              :chart-data="temp.chartsRulelDatas"
              :chart-option="temp.chartRuleOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.sensRule.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.sensRule + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title"><span @click="severityTitleClick">{{ $t('report.sensitiveFileSeverityAnalysis') }}>></span></div>
            <pie-chart
              :chart-data="temp.chartsSeverityDatas"
              :chart-option="temp.chartSeverityOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.sensLever.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.sensLever + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
      <!--终端敏感文件分析-->
      <div v-if="nodeDiv" class="detailsDiv">
        <analysis-terminal :search-condition="query" :drill-down="drillDown"/>
      </div>
      <!--部门分析-->
      <div v-if="deptDiv" class="detailsDiv">
        <analysis-dept :search-condition="query" :drill-down="drillDown"/>
      </div>
      <!--敏感文件规则分析-->
      <div v-if="ruleDiv" class="detailsDiv">
        <analysis-rule :search-condition="query" :drill-down="drillDown"/>
      </div>
      <!--敏感文件严重程度分析-->
      <div v-if="severityDiv" class="detailsDiv">
        <analysis-severity :search-condition="query" :drill-down="drillDown"/>
      </div>
    </div>
    <!--小方块点击弹框-->
    <total-violation-dlg ref="totalViolationDlg" :search-condition="query"/>
    <!--    部门、终端、操作员前五名小图标点击弹框-->
    <echart-dlg ref="echartDlg" :search-condition="query" :title="echartDlgTitle" :dialog-status="echartStatus"/>
  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
import TotalViolationDlg from '@/views/report/sensitiveControl/fullScan/dlgTotalViolation';
import analysisDept from '@/views/report/sensitiveControl/fullScan/analysisDept';
import EchartDlg from '@/views/report/sensitiveControl/fullScan/echartDlg';
import analysisTerminal from '@/views/report/sensitiveControl/fullScan/analysisTermina';
import analysisSeverity from '@/views/report/sensitiveControl/fullScan/analysisSeverity';
import analysisRule from '@/views/report/sensitiveControl/fullScan/analysisRule';
import {
  getDiskScanSensitiveHomepageData,
  getTaskOptionStep1,
  getTaskOptionStep2
} from '@/api/report/baseReport/sensitiveControl/diskScanSensitive'
import { checkDiskScanDetailAble } from '@/api/report/baseReport/issueReport/issue'

export default {
  name: 'FullScan',
  components: { BarChart, PieChart, TotalViolationDlg, analysisDept, EchartDlg, analysisTerminal, analysisSeverity, analysisRule },
  props: {
  },
  data() {
    return {
      commonInterpretation: `<span>图表解读：</span>`,
      chartInterpretationContent: {
        termSensitiveBar: '',
        deptDistribute: '',
        sensRule: '',
        sensLever: ''
      },
      // 统计类型
      typeOptions: [
        { value: 1, label: this.$t('pages.conutType1') },
        { value: 2, label: this.$t('pages.conutType2') }
      ],
      guidOptions: [],
      batchNoOptions: [],
      showOption2: false,
      // 搜查查询
      query: {
        id: null,
        guid: '',
        batchNo: '',
        sortOrder: 'desc',
        isExplain: true
      },
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: this.$t('report.diskScanSensitiveAnalysis') }
      ],
      noDataFlag: true,
      // 初始页面显示隐藏
      InceptionDiv: true,
      // 终端敏感文件分析
      nodeDiv: false,
      // 部门分析显示隐藏
      deptDiv: false,
      // 敏感文件规则分析
      ruleDiv: false,
      // 敏感文件严重程度分析
      severityDiv: false,
      // 放大柱状图弹框名称
      echartDlgTitle: '',
      echartStatus: 'dept',
      // 弹框名称
      deviceTitle: '',
      // 是否支持下钻
      drillDown: this.hasPermission('855'),
      // 上方小方块
      itemOption: [
        { label: this.$t('report.violationFileNum'), key: 'all', icon: 'total', func: this.totalViolationFun },
        { label: this.$t('report.verySeriousFileNum'), key: 'verySerious', icon: 'total', func: this.deptFun },
        { label: this.$t('report.seriousFileNum'), key: 'serious', icon: 'total', func: this.terminalUserFun }
      ],
      temp: {
        itemValue: {
          all: '0',
          verySerious: '0',
          serious: '0'
        },
        // 终端敏感文件分析，柱状图
        barChartDivulgeData: [],
        barChartDivulgeOption: {
          title: {
            'text': this.$t('report.termSensitiveFileAnalysis'),
            'subtext': this.$t('pages.topN', { num: 20 })
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0
            }
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 部门风险行为数量柱状图数据
        barChartData: [],
        barChartOption: {
          title: {
            'text': this.$t('report.groupDiskScanSensitiveAnalysis'),
            'subtext': this.$t('pages.topN', { num: 5 })
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0
            }
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 饼图 敏感文件规则分析
        chartsRulelDatas: [
        ],
        chartRuleOption: {
          title: {
            text: this.$t('report.sensitiveFileRuleAnalysis'),
            left: 'center',
            'subtext': ''
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%'
            }
          ]
        },
        // 饼图 敏感文件严重程度分析
        chartsSeverityDatas: [
        ],
        chartSeverityOption: {
          title: {
            text: this.$t('report.sensitiveFileSeverityAnalysis'),
            left: 'center',
            'subtext': ''
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: ['35%', '40%']
            }
          ]
        }
      },
      rules: {
        guid: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ],
        batchNo: [
          { required: true, message: this.$t('components.required'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {

  },
  watch: {
  },
  created() {
    this.itemOption.forEach(item => this.$set(item, 'clickable', this.drillDown))
    // this.getOptions()
    // this.getData()
    this.getTaskOptionStep1()
  },
  activated() {},
  methods: {
    getTaskOptionStep1() {
      getTaskOptionStep1({ taskType: 3 }).then(res => {
        this.guidOptions = res.data
        if (this.guidOptions.length > 0) {
          this.query.guid = this.guidOptions[0].key
          // 是否为定时扫描任务
          const cycle = this.guidOptions[0].id
          if (!cycle) {
            this.query.guid = this.guidOptions[0].key
            this.query.batchNo = '0'
            this.showOption2 = false
            this.loadReport()
          } else {
            // 定时扫描任务，同一个guid会有多个批次，对应不同的批次号
            this.showOption2 = true
            this.getTaskOptionStep2()
          }
        } else {
          this.noDataFlag = true
        }
      })
    },
    getTaskOptionStep2() {
      getTaskOptionStep2({ guid: this.query.guid }).then(res => {
        this.batchNoOptions = res.data
        if (this.batchNoOptions.length > 0) {
          this.query.batchNo = this.batchNoOptions[0].key
          this.loadReport()
        } else {
          this.query.batchNo = '0'
          this.loadReport()
        }
      })
    },
    /**
     * 任务下拉框选择事件
     */
    taskChange(data) {
      const selectedObj = this.guidOptions.find(item => item.key === data);
      if (selectedObj.id) {
        this.showOption2 = true
        this.getTaskOptionStep2()
      } else {
        this.showOption2 = false
      }
    },
    /**
     * 部门分析小图标点击
     * */
    handleBigDept() {
      this.echartDlgTitle = this.$t('report.groupDiskScanSensitiveAnalysis')
      this.echartStatus = 'dept'
      this.$refs.echartDlg.show(this.query, this.echartStatus)
    },
    // getOptions() {
    //   getDiskScanGuidOptionsPro({ taskType: 3 }).then(res => {
    //     this.guidOptions = res.data
    //     if (this.guidOptions.length > 0) {
    //       this.query.id = this.guidOptions[0].key + '@@' + this.guidOptions[0].id
    //       this.query.guid = this.guidOptions[0].key
    //       this.query.batchNo = this.guidOptions[0].id
    //       this.loadReport()
    //     } else {
    //       this.noDataFlag = true
    //     }
    //   })
    // },
    /**
     * 获取数据信息
     * */
    getData() {
      getDiskScanSensitiveHomepageData(this.query).then(res => {
        // 图表解读
        if (res.data.itemValueMap.explanation) {
          this.chartInterpretationContent = { ...this.chartInterpretationContent, ...res.data.itemValueMap.explanation }
        } else {
          this.chartInterpretationContent = {
            termSensitiveBar: '',
            deptDistribute: '',
            sensRule: '',
            sensLever: ''
          }
        }

        this.temp.itemValue = res.data.itemValueMap
        // 终端柱状图
        this.temp.barChartDivulgeOption.xAxis.data = res.data.chartDataObjMap.termSensitiveBar.xaxisData
        this.temp.barChartDivulgeOption.series[0].data = res.data.chartDataObjMap.termSensitiveBar.seriesData
        // 部门柱状图
        this.temp.barChartOption.xAxis.data = res.data.chartDataObjMap.groupSensitiveBar.xaxisData
        this.temp.barChartOption.series[0].data = res.data.chartDataObjMap.groupSensitiveBar.seriesData
        // 规则类型饼图
        this.temp.chartsRulelDatas = res.data.chartDataObjMap.ruleTypePie.chartData
        // 严重程度饼图
        this.temp.chartsSeverityDatas = res.data.chartDataObjMap.severityPie.chartData
      }).then(() => {
        this.triggerResize()
      })
    },
    /**
     * 搜索按钮点击（暂时原型主要是对统计类型变化时页面上显示变化的一个处理）
     * */
    loadReport() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.noDataFlag = false
          // 初始页面（点击搜索的时候要回到初始页面）
          this.InceptionDiv = true
          // 终端敏感文件分析
          this.nodeDiv = false
          // 部门页面
          this.deptDiv = false
          // 敏感文件规则分析
          this.ruleDiv = false
          // 敏感文件严重程度分析
          this.severityDiv = false
          this.showFilePath = [{ id: 0, label: this.$t('report.diskScanSensitiveAnalysis') }]
          this.getData()
        }
      })
    },
    /**
     * 违规文件总数(点击)
     */
    totalViolationFun() {
      if (this.drillDown) {
        checkDiskScanDetailAble(this.query).then(() => {
          this.$refs.totalViolationDlg.show(1)
        })
      }
    },
    /**
     * 非常严重文件数（点击）
     */
    deptFun() {
      if (this.drillDown) {
        checkDiskScanDetailAble(this.query).then(() => {
          this.$refs.totalViolationDlg.show(2)
        })
      }
    },
    /**
     * 严重文件数（点击）
     */
    terminalUserFun() {
      if (this.drillDown) {
        checkDiskScanDetailAble(this.query).then(() => {
          this.$refs.totalViolationDlg.show(3)
        })
      }
    },
    /**
     * 打印
     * */
    handlePrint() {

    },
    /**
     * 导出
     * */
    handleExport() {

    },
    /**
     * 终端敏感文件分析点击
     * 显示终端敏感文件分析，面包屑中添加终端敏感文件分析
     * */
    nodeTitleClick() {
      this.InceptionDiv = false
      this.nodeDiv = true
      this.showFilePath.push({ id: 4, label: this.$t('report.termSensitiveFileAnalysis') })
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    deptTitleClick() {
      this.InceptionDiv = false
      this.deptDiv = true
      this.showFilePath.push({ id: 2, label: this.$t('report.groupSensitiveFileAnalysis') })
    },
    /**
     * 敏感文件规则分析分析点击
     * 显示敏感文件规则分析，面包屑中添加敏感文件规则分析
     * */
    ruleTitleClick() {
      this.InceptionDiv = false
      this.ruleDiv = true
      this.showFilePath.push({ id: 1, label: this.$t('report.sensitiveFileRuleAnalysis') })
    },

    /**
     * 敏感文件严重程度分析点击
     * 显示敏感文件严重程度分析，面包屑中添加敏感文件严重程度分析
     * */
    severityTitleClick() {
      this.InceptionDiv = false
      this.severityDiv = true
      this.showFilePath.push({ id: 3, label: this.$t('report.sensitiveFileSeverityAnalysis') })
    },

    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        // 初始页面
        this.InceptionDiv = true
        // 终端敏感文件分析
        this.nodeDiv = false
        // 部门页面
        this.deptDiv = false
        // 敏感文件规则分析
        this.ruleDiv = false
        // 敏感文件严重程度分析
        this.severityDiv = false
        this.showFilePath = [{ id: 0, label: this.$t('report.diskScanSensitiveAnalysis') }]
      }
      if (index === 1) {
        // 初始页面
        this.InceptionDiv = false
        // 终端敏感文件分析
        this.nodeDiv = false
        // 部门页面
        this.deptDiv = false
        // 敏感文件规则分析
        this.ruleDiv = true
        // 敏感文件严重程度分析
        this.severityDiv = false
      }
      if (index === 2) {
        // 初始页面
        this.InceptionDiv = false
        // 终端敏感文件分析
        this.nodeDiv = false
        // 部门页面
        this.deptDiv = true
        // 敏感文件规则分析
        this.ruleDiv = false
        // 敏感文件严重程度分析
        this.severityDiv = false
      }
      if (index === 3) {
        // 初始页面
        this.InceptionDiv = false
        // 终端敏感文件分析
        this.nodeDiv = false
        // 部门页面
        this.deptDiv = false
        // 敏感文件规则分析
        this.ruleDiv = false
        // 敏感文件严重程度分析
        this.severityDiv = true
      }
      if (index === 4) {
        // 初始页面
        this.InceptionDiv = false
        // 终端敏感文件分析
        this.nodeDiv = true
        // 部门页面
        this.deptDiv = false
        // 敏感文件规则分析
        this.ruleDiv = false
        // 敏感文件严重程度分析
        this.severityDiv = false
      }
    }
  }
}
</script>
