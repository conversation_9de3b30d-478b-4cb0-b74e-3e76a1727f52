<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      v-zoom-dialog
      :fullscreen="fullscreen"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div class="sensitive-control-echartDlg">
        <div class="toolbar" style="height: 30px;">
          <div style="float: right;">
            <el-select v-model="query.recordSize" filterable @change="changeValue">
              <el-option :value="5" :label="$t('pages.topN', { num: 5 })"></el-option>
              <el-option :value="10" :label="$t('pages.topN', { num: 10 })"></el-option>
              <el-option :value="20" :label="$t('pages.topN', { num: 20 })"></el-option>
              <el-option :value="30" :label="$t('pages.topN', { num: 30 })"></el-option>
              <el-option :value="40" :label="$t('pages.topN', { num: 40 })"></el-option>
            </el-select>
          </div>
        </div>
        <div :style="{height:tableHeight}" class="no-pointer">
          <bar-chart
            ref="chart1"
            :chart-data="temp.deptBarChartData"
            :chart-option="temp.deptBarChartOption"
            :x-axis-name="this.$t('pages.dept')"
            style="width: 100%;height: 100%"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart'
import {
  getDiskScanSensitiveGroupChartData
} from '@/api/report/baseReport/sensitiveControl/diskScanSensitive';

export default {
  name: 'EchartDlg',
  components: { BarChart },
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    },
    searchCondition: { // 任务编号
      type: Object,
      default() {
        return {
          guid: '',
          batchNo: ''
        }
      }
    },
    dialogStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      // 下拉框选中值
      query: {
        recordSize: 20
      },
      // 柱状图
      temp: {},
      defaultTemp: {
        deptBarChartData: [],
        deptBarChartOption: {
          title: {
            'text': this.$t('report.groupDiskScanSensitiveAnalysis')
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0,
              // 倾斜的程度
              rotate: -70
            }
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      }
    }
  },
  computed: {
    tableHeight() {
      return this.fullscreen ? '100%' : '400px'
    }
  },
  watch: {
    fullscreen() {
      this.$nextTick(() => {
        this.$refs.chart1.chart.resize()
      })
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    getData() {
      const tempQuery = Object.assign({}, this.query, this.searchCondition)
      getDiskScanSensitiveGroupChartData(tempQuery).then(res => {
        this.temp.deptBarChartOption.xAxis.data = res.data.xaxisData
        this.temp.deptBarChartOption.series[0].data = res.data.seriesData
      })
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
    * 显示弹框 (根据查询条件不同，下方显示的柱状图可能是部门，终端，操作员其中一种)
    * @param query 父组件传过来的查询条件query
    */
    show(query) {
      // 下拉框切换应该也会用到这个参数，所以新声明一个dialogQuery字段
      this.dialogVisible = true
      this.getData()
      // eslint-disable-next-line no-empty
      if (status === 'dept') {
      }
    },
    handleBlur(e) {
      const value = e.target.value
      const reg = new RegExp(/^[1-9]\d*$/)
      if (reg.test(value)) {
        this.query.recordSize = value
      } else {
        this.query.recordSize = this.query.recordSize
      }
    },
    /**
     * 下拉框选中发生变化时，直接查询对应的柱状图信息
     * @param val 下拉框选中value值
     */
    changeValue(val) {
      // 这边下拉框选中发生变化时需要一个接口获取柱状图信息，接口调用成功后对deptBarChartData和deptBarChartOption进行赋值
      this.getData()
      // eslint-disable-next-line no-empty
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
<style>
  .no-pointer>div>div>canvas {
    cursor: default;
  }
</style>
