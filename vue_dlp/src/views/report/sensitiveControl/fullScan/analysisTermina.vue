<template>
  <!--终端敏感文件分析-->
  <div class="data-details sensitiveness-report">
    <div class="data-analysis-table table-parent-size">
      <div class="table-container table-left-size">
        <!--左侧列表查询条件-->
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchLeftForm" label-position="right" :model="query" label-width="80px">
            <FormItem :label="$t('table.terminalName')">
              <el-input v-model="query.searchObjectName" v-trim clearable maxlength="100"/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetLeftQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="handleFilterLeft()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button slot="reference" type="primary" icon="el-icon-search" class="search-icon-left" size="mini"></el-button>
        </el-popover>
        <grid-table
          ref="terminalUserList"
          :col-model="colModel"
          :row-data-api="rowDataApi"
          :multi-select="false"
          :autoload="false"
          @currentChange="currentChangeLeft"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--终端操作员分析上方几个小方块-->
      <div class="data-panel">
        <div v-for="(item, key) in temp.itemOption" :key="key" class="data-item data-item-details">
          <div class="icon">
            <svg-icon :icon-class="item.ico"></svg-icon>
          </div>
          <div class="content">
            <label :title="item.label">{{ item.label }}</label>
            <span :title="item.value">{{ item.value }}</span>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 165px); padding: 0 20px;position: relative">
        <!--右侧列表查询条件-->
        <el-popover
          :disabled="detailQuery.objectId==null"
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchRightForm" label-position="right" :model="detailQuery" label-width="80px">
            <FormItem :label="$t('pages.severity')">
              <el-select v-model="detailQuery.severity" style="width: 244px" clearable>
                <el-option v-for="(label, value) in severityOptions" :key="value" :label="label" :value="value"></el-option>
              </el-select>
            </FormItem>
            <FormItem :label="$t('pages.ruleType')">
              <el-select v-model="detailQuery.ruleType" style="width: 244px" clearable>
                <el-option v-for="(label, value) in ruleTypeOptions" :key="value" :label="label" :value="value"></el-option>
              </el-select>
            </FormItem>
            <FormItem :label="$t('pages.ruleName')">
              <el-input v-model="detailQuery.ruleName" v-trim clearable maxlength="100"/>
            </FormItem>
            <FormItem :label="$t('pages.fileName')">
              <el-input v-model="detailQuery.fileName" v-trim clearable maxlength="100"/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetAdvancedQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="searchBtn()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button v-show="isShowGrid" slot="reference" type="primary" icon="el-icon-search" class="search-icon-right" size="mini"></el-button>
        </el-popover>
        <el-tabs v-model="activeName" @tab-click="tabClick">
          <el-tab-pane v-if="drillDown" ref="terminalMsg" :label="$t('report.termScanFileInfo')" name="terminalMsg" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                v-show="isShowGrid"
                ref="deptMsgList"
                :autoload="false"
                :col-model="colModelTerminalMsg"
                :multi-select="false"
                :row-data-api="rowDetailDataApi"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane ref="statistics" :label="$t('report.termSeverityPercentAnalysis')" name="statistics">
            <pie-chart
              v-show="isShowChart"
              ref="pieChart"
              :chart-data="temp.chartDatas"
              :chart-option="temp.chartOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from '@/components/ECharts/PieChart'
import {
  getDiskScanSensitiveTerminalPage,
  getDiskScanSensitiveDetailPage
} from '@/api/report/baseReport/sensitiveControl/diskScanSensitive'
import { checkDiskScanDetailAble } from '@/api/report/baseReport/issueReport/issue'

export default {
  name: 'AnalysisTerminal',
  components: { PieChart },
  props: {
    searchCondition: { // 任务编号
      type: Object,
      default() {
        return {
          guid: '',
          batchNo: ''
        }
      }
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      query: {},
      DefaultQuery: {
        page: 1,
        limit: 20,
        label: '',
        searchObjectName: ''
      },
      detailQuery: {},
      DefaultDetailQuery: {
        page: 1,
        limit: 20,
        objectId: null,
        objectType: 1,
        objectName: '',
        severity: null,
        ruleType: null,
        ruleName: '',
        fileName: ''
      },
      colModel: [
        { prop: 'label', label: 'terminalName' },
        { prop: 'allSum', label: this.$t('report.sensitiveFileNum') }
      ],
      // 左侧列表
      leftListRow: [],
      temp: {},
      defaultTemp: {
        // 小方块
        itemOption: [
          { label: '', value: '', ico: 'algorithm' },
          { label: this.$t('report.sensitiveFileNum'), value: '', ico: 'ruleGroup' }
        ],
        // 详情列表信息
        terminalListMessage: [],
        // 饼图 严重程度占比图
        chartDatas: [],
        chartOption: {
          title: {
            text: this.$t('report.termSeverityPercentAnalysis'),
            left: 'center',
            'subtext': ''
          },
          toolbox: {
            show: false
          },
          legend: {
            left: 'center'
          },
          series: [
            {
              radius: ['25%', '30%']
            }
          ]
        }
      },
      // 终端操作员分析
      activeName: '',
      isShowGrid: this.drillDown,
      isShowChart: !this.drillDown,
      colModelTerminalMsg: [
        { prop: 'termGroupName', label: 'terminalGroup' },
        { prop: 'severity', label: 'severity', formatter: this.severityFormatter },
        { prop: 'ruleType', label: 'ruleType', formatter: this.ruleTypeFormatter },
        { prop: 'ruleName', label: 'ruleName' },
        { prop: 'fileName', label: 'fileName' }
      ],
      severityOptions: {
        4: this.$t('pages.severityOptions4'),
        3: this.$t('pages.severityOptions3'),
        2: this.$t('pages.severityOptions2'),
        1: this.$t('pages.severityOptions1')
      },
      ruleTypeOptions: {
        1: this.$t('route.file'),
        2: this.$t('route.keyword'),
        3: this.$t('route.regular'),
        4: this.$t('route.edm'),
        5: this.$t('route.vml'),
        6: this.$t('route.fileFp'),
        12: this.$t('route.sourceCode'),
        224: this.$t('table.contentTimeoutSumAll'),
        225: this.$t('table.filePwdSensitiveSumAll')
      }
    }
  },
  computed: {
  },
  created() {
    this.activeName = this.drillDown ? 'terminalMsg' : 'statistics'
    this.resetTemp()
  },
  async mounted() {
    await this.$refs['terminalUserList'].execRowDataApi()
    const tableData = this.$refs['terminalUserList'].getDatas()
    // 如果左侧列表长度不为0，初始化页面时显示第一条数据的详情
    if (tableData[0]) {
      this.currentChangeLeft(tableData[0])
    }
  },
  activated() {},
  methods: {
    /**
     *左边表格重置
     * */
    resetLeftQuery() {
      this.query.searchObjectName = ''
    },
    /**
     *左边表格搜索
     * */
    async handleFilterLeft() {
      this.query.page = 1
      await this.$refs['terminalUserList'].execRowDataApi(this.query)
      const tableData = this.$refs['terminalUserList'].getDatas()
      if (tableData[0]) {
        this.currentChangeLeft(tableData[0])
      } else {
        this.resetTemp()
        this.detailQuery.objectId = null
        this.detailQuery.objectName = ''
        this.detailQuery.page = 1
        if (this.$refs.deptMsgList) {
          this.$refs.deptMsgList.clearRowData()
          this.$refs.deptMsgList.clearPageData()
        }
      }
    },
    /**
     *右边表格搜索
     * */
    handleFilterRight() {
      this.detailQuery.page = 1
      // console.log('query', this.query)
      this.$refs['deptMsgList'].execRowDataApi(this.query)
    },
    /**
     *高级查询弹框显示
     * */
    handleAdvanceQuery() {
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
    },
    /**
     * 高级查询，查询操作
     * */
    searchBtn() {
      this.detailQuery.page = 1
      this.$refs['deptMsgList'].execRowDataApi(this.detailQuery)
    },
    /**
     *重置《高级查询重置按钮》
     * */
    resetAdvancedQuery() {
      this.detailQuery.severity = null
      this.detailQuery.ruleType = null
      this.detailQuery.ruleName = ''
      this.detailQuery.fileName = ''
    },
    severityFormatter: function(row, data) {
      return this.severityOptions[data]
    },
    ruleTypeFormatter: function(row, data) {
      return this.ruleTypeOptions[data]
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
      // this.query = Object.assign({}, this.DefaultQuery)
    },
    /**
     * 将终端或操作员的表数据添加到表格中
     * */
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.searchCondition, this.query, option)
      searchQuery.searchCount = true
      return getDiskScanSensitiveTerminalPage(searchQuery)
    },
    rowDetailDataApi(option) {
      const searchQuery = Object.assign({}, this.searchCondition, this.detailQuery, option)
      searchQuery.searchCount = true
      return getDiskScanSensitiveDetailPage(searchQuery)
    },
    /**
     * 获取左边列表行点击时的信息（右边的详细信息）
     * @param row 当前行信息
     * */
    currentChangeLeft(row) {
      this.resetTemp()
      this.detailQuery.objectId = row.labelValue
      this.detailQuery.objectName = row.label
      this.temp.itemOption[0].label = row.label
      this.temp.itemOption[1].value = row.allSum
      if (row.verySeriousSum) {
        this.temp.chartDatas.push({ name: this.$t('pages.severityOptions4'), value: row.verySeriousSum })
      }
      if (row.seriousSum) {
        this.temp.chartDatas.push({ name: this.$t('pages.severityOptions3'), value: row.seriousSum })
      }
      if (row.commonlySum) {
        this.temp.chartDatas.push({ name: this.$t('pages.severityOptions2'), value: row.commonlySum })
      }
      if (row.slightSum) {
        this.temp.chartDatas.push({ name: this.$t('pages.severityOptions1'), value: row.slightSum })
      }
      if (this.drillDown) {
        this.detailQuery.page = 1
        this.$refs.deptMsgList.clearRowData()
        this.$refs.deptMsgList.clearPageData()
        checkDiskScanDetailAble({ guid: this.searchCondition.guid }).then(() => {
          this.$refs.deptMsgList.execRowDataApi(this.detailQuery)
        })
      }
    },
    /**
       * tab点击事件，如果不写，切换时图表只有小小一点点
       * @param pane
       * @param event
       */
    tabClick(pane, event) {
      if (pane.name === 'terminalMsg') {
        this.isShowGrid = true
        this.isShowChart = false
      } else {
        this.isShowGrid = false
        this.isShowChart = true
        this.$refs['pieChart'].__resizeHandler()
      }
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
