<template>
  <!--部门分析-->
  <div class="data-details sensitiveness-report">
    <div class="data-analysis-table table-parent-size" style="padding-top: 20px;">
      <div class="table-container table-left-size">
        <!--左侧列表查询条件-->
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchLeftForm" label-position="right" :model="query" label-width="80px">
            <FormItem :label="$t('pages.deptName')">
              <el-input v-model="query.keyword1" v-trim clearable maxlength="100"/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetLeftQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="handleFilterLeft()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button slot="reference" type="primary" icon="el-icon-search" class="search-icon-left" size="mini"></el-button>
        </el-popover>
        <grid-table
          ref="deptList"
          row-key="id"
          :col-model="colModel"
          :row-data-api="rowDataApi"
          :multi-select="false"
          @currentChange="currentChangeLeft"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <div style="display: block;padding: 20px 20px 0 20px"> {{ temp.deptName }} </div>
      <!--部门分析上方几个小方块-->
      <div class="data-panel">
        <div v-for="(item, key) in temp.deptItemOption" :key="key" class="data-item data-item-details">
          <div class="content">
            <label :title="item.label">{{ item.label }}</label>
            <span :title="item.value">{{ item.value }}</span>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 185px); padding: 0 9px;position: relative">
        <!--右侧列表查询条件-->
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchRightForm" label-position="right" :model="detailQuery" label-width="80px">
            <FormItem :label="(dialogStatus === 'user') ? $t('pages.userName') : $t('pages.terminalName')">
              <el-input v-model="detailQuery.keyword2" v-trim clearable maxlength="100"/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetAdvancedQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="searchBtn()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button v-show="isShowGrid" slot="reference" type="primary" icon="el-icon-search" class="search-icon-right" size="mini"></el-button>
        </el-popover>
        <el-tabs v-model="activeDeptName" @tab-click="tabClick">
          <el-tab-pane :label="$t('report.deptRiskBehaviorAnalysis')" name="deptMsg" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                ref="deptDetailList"
                :show="isShowGrid"
                :col-model="colModelDetail"
                :row-data-api="getDetailData"
                :autoload="false"
                :multi-select="false"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('report.deptRiskBehaviorStatistics')" name="deptStatistics">
            <radar-chart
              v-if="isShowChart"
              :chart-indicator="temp.chartIndicator"
              :chart-data="temp.chartsDatasRadar"
              :chart-option="temp.chartOptionRadar"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import RadarChart from '@/components/ECharts/RadarChart';
import {
  listSensitiveSymptomDeptData,
  listSensitiveSymptomTerminalUserData
} from '@/api/report/baseReport/sensitiveControl/sensitiveContent'

export default {
  name: 'AnalysisDept',
  components: { RadarChart },
  props: {
    // 状态，区分终端还是操作员
    dialogStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      query: {},
      DefaultQuery: {
        page: 1,
        limit: 20,
        groupType: 1,
        keyword1: ''
      },
      detailQuery: {},
      DefaultDetailQuery: {
        page: 1,
        limit: 20,
        objectNames: [],
        keyword2: ''
      },
      // 左侧列表
      colModel: [
        { prop: 'group_name', label: 'deptName' },
        { prop: 'count_sum_all', label: this.$t('report.riskyBehaviorsSum'), sort: true }
      ],
      // 右侧部门分析
      activeDeptName: 'deptMsg',
      isShowGrid: true,
      isShowGridInfo: false,
      isShowChart: false,
      // 右侧部门风险信息
      colModelDetail: [
        { prop: 'object_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'object_name', label: 'user', hidden: () => this.colHidden(['terminal']) },
        { prop: 'general_scan_violate_log_sum_all', label: this.$t('report.generalDataViolationsSum') },
        { prop: 'drip_scan_violate_log_sum_all', label: this.$t('report.dripDataViolationsSum') },
        { prop: 'disk_scan_sst_sum_all', label: this.$t('table.diskScanSstSumAll') },
        { prop: 'sensitive_file_out_send_sum_all', label: this.$t('report.fileOutgoingSum') }
      ],
      temp: {},
      defaultTemp: {
        // 右侧上方小标题
        deptName: '',
        // 部门分析
        deptItemOption: [
          { label: this.$t('report.generalDataViolationsSum'), value: 0 },
          { label: this.$t('report.dripDataViolationsSum'), value: 0 },
          { label: this.$t('report.fileOutgoingSum'), value: 0 },
          { label: this.$t('table.diskScanSstSumAll'), value: 0 }
        ],
        // 雷达图 风险行为分析
        chartIndicator: [
          { name: this.$t('report.generalDataViolationsSum'), max: 20 },
          { name: this.$t('report.dripDataViolationsSum'), max: 20 },
          { name: this.$t('report.fileOutgoingSum'), max: 20 },
          { name: this.$t('table.diskScanSstSumAll'), max: 20 }
        ],
        chartsDatasRadar: [
          {
            name: this.$t('report.riskBehavior'),
            value: [0, 0, 0, 0],
            areaStyle: {
              color: '#32dadd'
            }
          }
        ],
        chartOptionRadar: {
          title: {
            text: this.$t('report.riskBehavior'),
            left: 'center'
          },
          radar: {
            radius: '40%'
          },
          toolbox: {
            show: false
          }
        }
      }
    }
  },
  computed: {
  },
  created() {
    this.resetTemp()
    this.query = Object.assign({}, this.DefaultQuery)
  },
  async mounted() {
    await this.$refs['deptList'].execRowDataApi()
    const tableData = this.$refs['deptList'].getDatas()
    // 如果左侧列表长度不为0，初始化页面时显示第一条数据的详情
    if (tableData[0]) {
      this.currentChangeLeft(tableData[0])
    }
  },
  activated() {},
  methods: {
    /**
     *左边表格重置
     * */
    resetLeftQuery() {
      this.query.keyword1 = ''
    },
    /**
     *左边表格搜索
     * */
    async handleFilterLeft() {
      this.query.page = 1
      // console.log('query', this.query)
      await this.$refs['deptList'].execRowDataApi(this.query)
      const tableData = this.$refs['deptList'].getDatas()
      if (tableData[0]) {
        this.currentChangeLeft(tableData[0])
      } else {
        this.resetTemp()
        this.detailQuery.objectNames.splice(0)
        this.detailQuery.page = 1
        this.$refs.deptDetailList.clearRowData()
        this.$refs.deptDetailList.clearPageData()
      }
    },
    /**
     *高级查询弹框显示
     * */
    handleAdvanceQuery() {
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
    },
    /**
     * 高级查询，查询操作
     * */
    searchBtn() {
      this.detailQuery.page = 1
      this.$refs['deptDetailList'].execRowDataApi(this.detailQuery)
    },
    /**
     *重置《高级查询重置按钮》
     * */
    resetAdvancedQuery() {
      this.detailQuery.keyword2 = ''
    },
    /**
     *右边表格搜索
     * */
    handleFilterRight() {
      this.detailQuery.page = 1
      this.$refs['deptDetailList'].execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = { ...this.defaultTemp }
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
    },
    /**
     * 将终端或操作员的表数据添加到表格中
     * */
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listSensitiveSymptomDeptData(searchQuery)
    },
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.detailQuery, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listSensitiveSymptomTerminalUserData(searchQuery)
    },
    /**
       * 获取左边列表行点击时的信息（右边的详细信息）
       * @param row 当前行信息
       * */
    currentChangeLeft(row) {
      this.temp.deptName = row.group_name
      this.temp.deptItemOption[0].value = row.general_scan_violate_log_sum_all
      this.temp.deptItemOption[1].value = row.drip_scan_violate_log_sum_all
      this.temp.deptItemOption[2].value = row.sensitive_file_out_send_sum_all
      this.temp.deptItemOption[3].value = row.disk_scan_sst_sum_all
      this.temp.chartIndicator[0].max = row.max_general_scan_violate_log_sum_all
      this.temp.chartIndicator[1].max = row.max_drip_scan_violate_log_sum_all
      this.temp.chartIndicator[2].max = row.max_sensitive_file_out_send_sum_all
      this.temp.chartIndicator[3].max = row.max_disk_scan_sst_sum_all
      this.temp.chartsDatasRadar[0].value = [row.general_scan_violate_log_sum_all, row.drip_scan_violate_log_sum_all, row.sensitive_file_out_send_sum_all, row.disk_scan_sst_sum_all]
      // 查询违规明细数据
      this.detailQuery.objectNames.splice(0, 1, row.group_name + '(' + row.group_id + ')')
      this.$refs['deptDetailList'].execRowDataApi(this.detailQuery)
    },
    /**
       * 部门风险行为信息列表中需要点击的数字设置
       * */
    sizeFormatter1(row) {
      return row.leakageQuantityRoutine
    },
    sizeFormatter2(row) {
      return row.leakageQuantityAFew
    },
    sizeFormatter3(row) {
      return row.leakageQuantityOutsource
    },
    sizeFormatter4(row) {
      return row.leakageQuantityComprehensive
    },
    /**
       * 部门风险行为信息下钻返回按钮
       **/
    backBtn() {
      this.isShowGridInfo = false
      this.isShowGrid = true
    },
    /**
       * 根据终端还是操作员控制表格列的显示隐藏
       * */
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    /**
       * tab点击事件，如果不写，切换时图表只有小小一点点
       * @param pane
       * @param event
       */
    tabClick(pane, event) {
      if (pane.name === 'deptMsg') {
        this.isShowGrid = true
        this.isShowChart = false
      } else {
        this.isShowGrid = false
        this.isShowChart = true
      }
    },
    groupNameFormatter(row, data) {
      if (data && data.lastIndexOf('(') >= 0) {
        data = data.substr(0, data.lastIndexOf('('))
      }
      return this.html2Escape(data);
    }
  }
}
</script>
<style lang='scss' scoped>
  .data-item-details {
    .content{
      label, span{
        font-size: 14px;
      }
      label{
        width: 145px;
      }
      span{
        width: 50px;
      }
    }
  }
</style>
