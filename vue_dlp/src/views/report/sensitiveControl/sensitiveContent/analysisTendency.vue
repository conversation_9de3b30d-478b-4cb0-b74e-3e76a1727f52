<template>
  <!--数量趋势-->
  <div style="height: 100%">
    <!--数量趋势--上方折线图-->
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="24">
        <line-chart
          :chart-data="temp.lineChartData"
          :chart-option="temp.lineOption"
          style="width: 100%;height: 500px"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
// import PieChart from '@/components/ECharts/PieChart'
// import RadarChart from '@/components/ECharts/RadarChart';
import {
  getSensitiveSymptomTrendAnalysisData
} from '@/api/report/baseReport/sensitiveControl/sensitiveContent'

export default {
  name: 'OnOffTrend',
  components: { LineChart },
  data() {
    return {
      temp: {},
      defaultTemp: {
        lineChartData: [],
        lineOption: {
          title: {
            'text': this.$t('report.riskyBehaviorsTrend')
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          legend: {
            type: 'plain',
            x: 'center', // 图例水平居中
            bottom: 0,
            data: [this.$t('report.allRiskyBehaviors'), this.$t('report.generalDataViolationsSum'), this.$t('report.dripDataViolationsSum'), this.$t('report.fileOutgoingSum'), this.$t('table.diskScanSstSumAll')]
          },
          dataZoom: [
            {
              textStyle: {
                color: '#027AB6'
              },
              bottom: 60,
              height: 20, // 时间滚动条的高度
              type: 'slider', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0,  // 默认开始位置（百分比）
              end: 100  // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          grid: {
            top: '10%',
            width: '85%',
            bottom: '15%',
            left: '4%',
            containLabel: true
          },
          series: [
            {
              data: [],
              type: 'line',
              name: this.$t('report.allRiskyBehaviors')
            },
            {
              data: [],
              type: 'line',
              name: this.$t('report.generalDataViolationsSum')
            },
            {
              data: [],
              type: 'line',
              name: this.$t('report.dripDataViolationsSum')
            },
            {
              data: [],
              type: 'line',
              name: this.$t('report.fileOutgoingSum')
            },
            {
              data: [],
              type: 'line',
              name: this.$t('table.diskScanSstSumAll')
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  created() {
    this.getData()
    this.resetTemp()
  },
  activated() {},
  methods: {
    resetTemp() {
      this.temp = { ...this.defaultTemp }
    },
    /**
     * 获取数据信息
     * */
    getData() {
      getSensitiveSymptomTrendAnalysisData(this.$parent.tempQuery).then(resp => {
        if (resp.data) {
          // 折线图数据
          this.temp.lineOption.series[0].data = resp.data.chartDataObjMap.trendLine.seriesData[0]
          this.temp.lineOption.series[1].data = resp.data.chartDataObjMap.trendLine.seriesData[1]
          this.temp.lineOption.series[2].data = resp.data.chartDataObjMap.trendLine.seriesData[2]
          this.temp.lineOption.series[3].data = resp.data.chartDataObjMap.trendLine.seriesData[3]
          this.temp.lineOption.series[4].data = resp.data.chartDataObjMap.trendLine.seriesData[4]
          this.temp.lineOption.xAxis.data = resp.data.chartDataObjMap.trendLine.xaxisData
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
