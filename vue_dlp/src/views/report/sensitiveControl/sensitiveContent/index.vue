<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query">
        <i class="el-icon-s-home" style="position: absolute; left: 24px; top: 16px;"></i>
        <!--面包屑-->
        <div style="width: 510px; float: left;">
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <!--上方搜索条件-->
        <div style="float: right">
          <!--查询条件组件-->
          <query ref="queryData"/>
          <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
          <!-- <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
          <el-button type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>-->
        </div>
      </div>

      <!-- 页面无数据 -->
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>

      <!--分析初始页面-->
      <div v-if="InceptionDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
            <div class="icon">
              <svg-icon :icon-class="item.icon"></svg-icon>
            </div>
            <div class="content">
              <label :title="item.label">{{ item.label }}</label>
              <el-button type="text" :class="{'underline': item.clickable }" @click="item.func">{{ temp.itemValue[item.key] }}</el-button>
            </div>
          </div>
        </div>
        <!--中间敏感内容柱状图和雷达图-->
        <div class="chart-panel">
          <div class="chart-item flex-2">
            <div class="mini-title">
            </div>
            <bar-chart
              :chart-data="temp.barChartDivulgeData"
              :chart-option="temp.barChartDivulgeOption"
              :x-axis-name="''"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.riskPie.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title">
                解读：<span style="color: #909399;">各风险行为占比构成风险感知能力图谱，为构建分级预警体系提供量化依据。</span>
              </div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.riskPie + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title">
            </div>
            <!--饼图-->
            <pie-chart
              :chart-data="temp.pieChartData"
              :chart-option="temp.pieChartOption"
              class="flex-2"
            />
          </div>
        </div>
        <!--下方对部门，终端（操作员），数量趋势的分别展示部分-->
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="deptTitleClick">{{ $t('report.deptRiskBehavior') }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigDept"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.barChartData"
              :chart-option="temp.barChartOption"
              :x-axis-name="this.$t('pages.dept')"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.deptDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.deptDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="userTitleClick">{{ terminalUserAnalysisTitle }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigTU"/>
              </el-tooltip>
            </div>
            <pie-chart
              :chart-data="temp.chartsUserDatas"
              :chart-option="temp.chartUserOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.termOrUserDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.termOrUserDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title"><span @click="notOfflineTitleClick">{{ $t('pages.violationCountTrendAnalysis') }}>></span></div>
            <line-chart
              :chart-data="temp.lineChartData"
              :chart-option="temp.lineOption"
              class="flex-2"
            />
            <div v-if="chartInterpretationContent.violationNum.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + chartInterpretationContent.violationNum + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
      <!--部门分析-->
      <div v-if="deptDiv" class="detailsDiv">
        <analysis-dept :dialog-status="dialogStatus"/>
      </div>
      <!--终端、操作员分析-->
      <div v-if="userDiv" class="detailsDiv">
        <analysis-terminal-user :dialog-status="dialogStatus" :drill-down="hasPermission('860')"/>
      </div>
      <!--违规数量趋势分析-->
      <div v-if="tendencyDiv" class="detailsDiv">
        <analysis-tendency :dialog-status="dialogStatus"/>
      </div>
    </div>
    <!--小方块（风险行为数）点击弹框-->
    <total-violation-dlg ref="totalViolationDlg" :dialog-status="dialogStatus"/>
    <!--小方块（部门）点击弹框-->
    <dept-dlg ref="deptDlg"/>
    <!--小方块点（终端）击弹框-->
    <terminal-user-dlg ref="terminalUserDlg" :dialog-status="dialogStatus"/>
    <!--    部门、终端、操作员前五名小图标点击弹框-->
    <echart-dlg ref="echartDlg" :title="echartDlgTitle" :echart-status="echartStatus"/>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
// import RadarChart from '@/components/ECharts/RadarChart';
import Query from '@/views/report/sensitiveControl/common/query';
import DeptDlg from '@/views/report/sensitiveControl/sensitiveContent/dlgDept';
import EchartDlg from '@/views/report/sensitiveControl/sensitiveContent/echartDlg';
import TerminalUserDlg from '@/views/report/sensitiveControl/sensitiveContent/dlgTerminalUser';
import TotalViolationDlg from '@/views/report/sensitiveControl/sensitiveContent/dlgTotalViolation';
import analysisDept from '@/views/report/sensitiveControl/sensitiveContent/analysisDept';
import analysisTerminalUser from '@/views/report/sensitiveControl/sensitiveContent/analysisTerminalUser';
import analysisTendency from '@/views/report/sensitiveControl/sensitiveContent/analysisTendency';
import { getSensitiveSymptomHomepageData } from '@/api/report/baseReport/sensitiveControl/sensitiveContent'

export default {
  name: 'SensitiveContent',
  components: { Query, BarChart, LineChart, PieChart, DeptDlg, EchartDlg, TerminalUserDlg, TotalViolationDlg, analysisDept, analysisTerminalUser, analysisTendency },
  props: {
  },
  data() {
    return {
      commonInterpretation: `<span>图表解读：</span>`,
      chartInterpretationContent: {
        riskPie: '',
        deptDistribute: '',
        termOrUserDistribute: '',
        violationNum: ''
      },
      typeOptions: [{ value: 1, label: this.$t('pages.conutType1') }],
      tempQuery: {},
      // 搜查查询
      query: {
        isExplain: true,
        dimBaseType: 1,
        dimValue: '',
        recordSize: 5,
        countByObject: 2
      },
      terminalUserAnalysisTitle: this.$t('report.termRiskBehaviorCharts'),
      // 统计方式是否终端
      isTerminalStatistics: true,
      isUserStatistics: false,
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: this.$t('report.riskBehavior') }
      ],
      // 暂无数据
      noData: true,
      // 初始页面显示隐藏
      InceptionDiv: false,
      // 操作员分析显示隐藏
      userDiv: false,
      // 部门分析显示隐藏
      deptDiv: false,
      // 趋势分析显示隐藏
      tendencyDiv: false,
      // 弹框名称
      deviceTitle: '',
      // 弹框状态
      dialogStatus: '',
      // 放大柱状图弹框名称
      echartDlgTitle: '',
      echartStatus: 'dept',
      // 上方小方块
      itemOption: [
        { label: this.$t('report.riskBehaviorNum'), key: 'all', icon: 'total', func: this.totalViolationFun },
        { label: this.$t('table.dept'), key: 'dept', icon: 'tree', func: this.deptFun },
        { label: this.$t('pages.violationTerminal'), key: 'terminalUser', icon: 'terminal', func: this.terminalUserFun }
      ],
      temp: {},
      defaultTemp: {
        itemValue: {
          all: '0',
          dept: '0',
          terminalUser: '0'
        },
        // 风险行为统计 横向柱状统计图
        barChartDivulgeData: [],
        barChartDivulgeOption: {
          title: {
            'text': this.$t('report.riskBehaviorCharts'),
            'subtext': ''
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          yAxis: {
            type: 'category',
            data: [this.$t('table.diskScanSstSumAll'), this.$t('report.fileOutgoingSum'), this.$t('report.dripDataViolationsSum'), this.$t('report.generalDataViolationsSum')],
            axisLabel: {
              interval: 0
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 风险行为分析
        pieChartData: [],
        pieChartOption: {
          title: {
            text: this.$t('report.riskBehaviorPercent')
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
              // silent: true // 图表没有点击效果时取消禁用小手指的鼠标状态
            }
          ]
        },
        // 部门风险行为数量柱状图数据
        barChartData: [],
        barChartOption: {
          title: {
            'text': this.$t('report.deptSensitiveContentAnalysis'),
            'subtext': this.$t('pages.topN', { num: 5 })
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0
            }
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 饼图 操作员风险行为数量图
        chartsUserDatas: [
        ],
        chartUserOption: {
          title: {
            text: this.$t('report.termSensitiveContentAnalysis'),
            left: 'center',
            'subtext': this.$t('pages.topN', { num: 5 })
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 折线图数据 违规数量趋势分析
        lineChartData: [],
        lineOption: {
          title: {
            'text': this.$t('report.riskBehaviorTrendAnalysis')
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
    this.loadComponent()
  },
  activated() {},
  methods: {
    /**
     * 部门分析小图标点击
     * */
    handleBigDept() {
      this.echartDlgTitle = this.$t('report.deptSensitiveContentAnalysis')
      this.echartStatus = 'dept'
      this.$refs.echartDlg.show(this.query, this.echartStatus)
    },
    /**
     * 终端操作员小图标点击
     * */
    handleBigTU() {
      if (this.query.countByObject === 2) {
        this.echartDlgTitle = this.$t('report.termSensitiveContentAnalysis')
        this.echartStatus = 'terminal'
      } else {
        this.echartDlgTitle = this.$t('report.userSensitiveContentAnalysis')
        this.echartStatus = 'user'
      }
      // console.log('this.query11111', JSON.parse(JSON.stringify(this.query)))
      this.$refs.echartDlg.show(this.query, this.echartStatus)
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取数据信息
     * */
    getData() {
      this.query = this.$refs['queryData'].getQuery()
      this.query.recordSize = 5
      this.tempQuery = Object.assign({}, this.query)
      return getSensitiveSymptomHomepageData(this.query).then(resp => {
        if (resp.data) {
          this.InceptionDiv = true
          this.noData = false

          // 图表解读
          if (resp.data.itemValueMap.explanation) {
            this.chartInterpretationContent = { ...this.chartInterpretationContent, ...resp.data.itemValueMap.explanation }
          } else {
            this.chartInterpretationContent = {
              riskPie: '',
              deptDistribute: '',
              termOrUserDistribute: '',
              violationNum: ''
            }
          }
          this.temp.itemValue.all = resp.data.itemValueMap.violateTotal
          this.temp.itemValue.dept = resp.data.itemValueMap.deptCount
          this.temp.itemValue.terminalUser = resp.data.itemValueMap.terminalUserCount

          // 部门柱状图
          this.temp.barChartData = resp.data.chartDataObjMap.deptBarChartData.chartData
          this.temp.barChartOption.xAxis.data = resp.data.chartDataObjMap.deptBarChartData.xaxisData
          this.temp.barChartOption.series[0].data = resp.data.chartDataObjMap.deptBarChartData.seriesData

          // 风险行为柱状图
          this.temp.barChartDivulgeOption.series[0].data = resp.data.chartDataObjMap.symptomBarChartData.seriesData

          // 风险行为饼图
          this.temp.pieChartData = resp.data.chartDataObjMap.symptomPieChartData.chartData

          // 用户饼图
          this.temp.chartsUserDatas = resp.data.chartDataObjMap.terminalUserPieChartData.chartData

          // 趋势分析折线图
          this.temp.lineOption.xAxis.data = resp.data.chartDataObjMap.trendLineChartData.xaxisData
          this.temp.lineOption.series[0].data = resp.data.chartDataObjMap.trendLineChartData.seriesData
        } else {
          this.resetTemp()
          this.InceptionDiv = false
          this.noData = true
        }
        this.loadComponent()
      }).then(() => {
        this.triggerResize()
      })
    },
    /**
     * 搜索按钮点击（暂时原型主要是对统计类型变化时页面上显示变化的一个处理）
     * */
    loadReport() {
      // 获取子组件查询条件
      this.getData()
    },
    loadComponent() {
      if (this.query.countByObject === 1) {
        // 操作员
        Object.assign(this.itemOption[2], { label: this.$t('pages.violationUser'), icon: 'user' })
        this.terminalUserAnalysisTitle = this.$t('report.userRiskBehaviorCharts')
        this.temp.chartUserOption.title.text = this.$t('report.userSensitiveContentAnalysis')
        this.isTerminalStatistics = false
        this.isUserStatistics = true
      } else {
        // 终端
        Object.assign(this.itemOption[2], { label: this.$t('pages.violationTerminal'), icon: 'user' })
        this.isTerminalStatistics = true
        this.terminalUserAnalysisTitle = this.$t('report.termRiskBehaviorCharts')
        this.temp.chartUserOption.title.text = this.$t('report.termSensitiveContentAnalysis')
        this.isUserStatistics = false
      }
      // 初始页面（点击搜索的时候要回到初始页面）
      // this.InceptionDiv = true
      // 操作员（终端）页面
      this.userDiv = false
      // 部门页面
      this.deptDiv = false
      // 趋势页面
      this.tendencyDiv = false
      this.showFilePath = [{ id: 0, label: this.$t('report.riskBehavior') }]
    },
    /**
     * 违规总数(点击)
     */
    totalViolationFun() {
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.$refs.totalViolationDlg.show()
    },
    /**
     * 部门（点击）
     */
    deptFun() {
      this.$refs.deptDlg.show()
    },
    /**
     * 终端或操作员（点击）
     */
    terminalUserFun() {
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.$refs.terminalUserDlg.show()
    },
    /**
     * 打印
     * */
    handlePrint() {

    },
    /**
     * 导出
     * */
    handleExport() {

    },
    /**
     * 终端（操作员）分析点击
     * 显示终端（操作员）分析，面包屑中添加终端（操作员）分析
     * */
    userTitleClick() {
      this.InceptionDiv = false
      this.userDiv = true
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
        this.showFilePath.push({ id: 1, label: this.$t('report.userRiskBehavior') })
      } else {
        // 终端
        this.dialogStatus = 'terminal'
        this.showFilePath.push({ id: 1, label: this.$t('report.termRiskBehavior') })
      }
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    deptTitleClick() {
      this.InceptionDiv = false
      this.deptDiv = true
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 2, label: this.$t('report.deptRiskBehavior') })
    },
    /**
     * 数量趋势点击
     * 显示数量趋势，面包屑中添加数量趋势
     * */
    notOfflineTitleClick() {
      this.InceptionDiv = false
      this.tendencyDiv = true
      if (this.query.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 3, label: this.$t('pages.violationCountTrendAnalysis') })
    },
    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        // 初始页面
        this.InceptionDiv = true
        // 操作员（终端）页面
        this.userDiv = false
        // 部门页面
        this.deptDiv = false
        // 趋势页面
        this.tendencyDiv = false
        this.showFilePath = [{ id: 0, label: this.$t('report.riskBehavior') }]
      }
      if (index === 1) {
        this.InceptionDiv = false
        this.userDiv = true
        this.deptDiv = false
        this.tendencyDiv = false
      }
      if (index === 2) {
        this.InceptionDiv = false
        this.userDiv = false
        this.deptDiv = true
        this.tendencyDiv = false
      }
      if (index === 3) {
        this.InceptionDiv = false
        this.userDiv = false
        this.deptDiv = false
        this.tendencyDiv = true
      }
    }
  }
}
</script>
