<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('report.riskBehaviorNum')"
      :fullscreen="fullscreen"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="squareList"
        :stripe="false"
        :col-model="colModel"
        :row-data-api="getData"
        :autoload="false"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSensitiveSymptomTerminalUserData
} from '@/api/report/baseReport/sensitiveControl/sensitiveContent';
export default {
  name: 'DlgTotalViolation',
  components: { },
  props: {
    dialogStatus: { // 状态，终端和操作员小方块共用一个弹框，若某弹框有特定的列，需要根据状态控制列的显示隐藏
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      submitting: false,
      rowData: [],
      query: { // 查询条件
        page: 1,
        limit: 20,
        keyword1: '1'
      },
      colModel: [
        { prop: 'object_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'object_name', label: 'userName', hidden: () => this.colHidden(['terminal']) },
        { prop: 'group_name', label: 'deptName' },
        { prop: 'general_scan_violate_log_sum_all', label: this.$t('report.generalDataViolationsSum') },
        { prop: 'drip_scan_violate_log_sum_all', label: this.$t('report.dripDataViolationsSum') },
        { prop: 'sensitive_file_out_send_sum_all', label: this.$t('report.fileOutgoingSum') },
        { prop: 'disk_scan_sst_sum_all', label: 'diskScanSstSumAll' }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['squareList']
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  created() {
  },
  methods: {
    /**
     * 获取数据信息
     * */
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listSensitiveSymptomTerminalUserData(searchQuery)
    },
    /**
       * 显示弹框
       */
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.query.page = 1
        this.gridTable.execRowDataApi(this.query);
      })
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.submitting = false
    },
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

