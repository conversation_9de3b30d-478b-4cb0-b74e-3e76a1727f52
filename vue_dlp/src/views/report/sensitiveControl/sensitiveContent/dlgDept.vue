<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :fullscreen="fullscreen"
      :title="$t('table.dept')"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="squareList"
        :stripe="false"
        :row-data-api="rowDataApi"
        :col-model="colModel"
        :autoload="false"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {
  listSensitiveSymptomDeptData
} from '@/api/report/baseReport/sensitiveControl/sensitiveContent'

export default {
  name: 'DlgDept',
  components: { },
  props: {

  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      rowData: [],
      query: { // 查询条件
        page: 1,
        limit: 20,
        groupType: 1
      },
      colModel: [
        { prop: 'group_name', label: 'deptName' },
        { prop: 'general_scan_violate_log_sum_all', label: this.$t('report.generalDataViolationsSum') },
        { prop: 'drip_scan_violate_log_sum_all', label: this.$t('report.dripDataViolationsSum') },
        { prop: 'sensitive_file_out_send_sum_all', label: this.$t('report.fileOutgoingSum') },
        { prop: 'disk_scan_sst_sum_all', label: 'diskScanSstSumAll' }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['squareList']
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  created() {
  },
  methods: {
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listSensitiveSymptomDeptData(searchQuery)
    },
    /**
       * 显示弹框
       */
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.query.page = 1
        this.gridTable.execRowDataApi(this.query);
      })
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

