<template>
  <comparative-trend
    :api="'getScanViolateLogCompareData'"
    :api-dept="'getScanViolateLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'SensitiveContentCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '敏感内容征兆',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'generalScanViolateLogSumAll', label: 'generalScanViolateLogSumAll' },
        { prop: 'dripScanViolateLogSumAll', label: 'dripScanViolateLogSumAll' },
        { prop: 'sensitiveFileOutSendSumAll', label: 'sensitiveFileOutSendSumAll' },
        { prop: 'diskScanSstSumAll', label: 'diskScanSstSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['generalScanViolateLogSumAll', 'dripScanViolateLogSumAll', 'sensitiveFileOutSendSumAll', 'diskScanSstSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
