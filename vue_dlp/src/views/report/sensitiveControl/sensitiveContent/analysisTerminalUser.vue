<template>
  <!--终端操作员分析-->
  <div class="data-details sensitiveness-report">
    <div class="data-analysis-table table-parent-size">
      <div class="table-container table-left-size">
        <!--左侧列表查询条件-->
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchLeftForm" label-position="right" :model="query" label-width="80px">
            <FormItem :label="(dialogStatus === 'user') ? $t('pages.userName') : $t('pages.terminalName')">
              <el-input v-model="query.keyword2" v-trim clearable maxlength="100"/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetLeftQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="handleFilterLeft()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button slot="reference" type="primary" icon="el-icon-search" class="search-icon-left" size="mini"></el-button>
        </el-popover>
        <grid-table
          ref="terminalUserList"
          row-key="id"
          :col-model="colModel"
          :row-data-api="rowDataApi"
          :multi-select="false"
          @currentChange="currentChangeLeft"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--终端操作员分析上方几个小方块-->
      <div style="display: block;padding: 0px 20px 0 20px"> {{ temp.miniTitle }} </div>
      <div class="data-panel">
        <div v-for="(item, key) in temp.itemOption" :key="key" :class="active===key ? 'activeClass' : '  ' " class="data-item data-item-details" @click="item.func">
          <div class="content">
            <label :title="item.label">{{ item.label }}</label>
            <span :title="item.value">{{ item.value }}</span>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 185px); padding: 0 9px;position: relative">
        <!--右侧列表查询条件-->
        <el-popover
          v-if="active != 2 && active != 3 && detailQuery.objectNames.length > 0"
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchRightForm" label-position="right" :model="detailQuery" label-width="80px">
            <FormItem v-if="active==0" :label="$t('table.lossType')">
              <el-select v-model="detailQuery.lossType" style="width: 245px" clearable>
                <el-option v-for="(label, value) in lossTypeOptions" :key="value" :label="label" :value="value"></el-option>
              </el-select>
            </FormItem>
            <FormItem v-if="active==0 || active==1" :label="$t('pages.severity')">
              <el-select v-model="detailQuery.severity" style="width: 245px" clearable >
                <el-option v-for="(label, value) in severityMap" :key="value" :label="label" :value="value"></el-option>
              </el-select>
            </FormItem>
            <FormItem v-if="active==0 || active==1" :label="$t('pages.violationStrategy')">
              <el-input v-model="detailQuery.stgDefName" v-trim clearable maxlength="100"/>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetAdvancedQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="searchBtn()">{{ $t('button.search') }}</el-button>
          </div>
          <el-button v-show="isShowGrid" slot="reference" type="primary" icon="el-icon-search" class="search-icon-right" size="mini"></el-button>
        </el-popover>
        <el-tabs v-model="activeName" @tab-click="tabClick">
          <el-tab-pane v-if="drillDown" :label="$t('report.riskBehaviorAnalysis')" name="terminalMsg" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                ref="deptMsgList"
                :col-model="colModelDetail"
                :multi-select="false"
                :autoload="false"
                :row-data-api="rowDetailApi"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('report.riskBehaviorPercent')" name="statistics">
            <radar-chart
              ref="chart"
              :chart-indicator="temp.chartIndicator"
              :chart-data="temp.chartsDatasRadar"
              :chart-option="temp.chartOptionRadar"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import RadarChart from '@/components/ECharts/RadarChart';
import {
  getTrwfeLogDetail,
  getDiskScanLogDetail
} from '@/api/report/baseReport/issueReport/detail'
import {
  getDetailPage, checkDetailAble
} from '@/api/report/baseReport/issueReport/issue'
import {
  listSensitiveSymptomTerminalUserData
} from '@/api/report/baseReport/sensitiveControl/sensitiveContent'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'

export default {
  name: 'AnalysisTerminalUser',
  components: { RadarChart },
  props: {
    // 状态，区分终端还是操作员
    dialogStatus: {
      type: String,
      default() {
        return ''
      }
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      active: 0, // 默认选中第一个样式
      isShowGrid: this.drillDown,
      diskScanOpTypeMap: {
        1: this.$t('pages.fullDecryption'),
        2: this.$t('pages.globalEncryption'),
        3: this.$t('pages.diskScan_Msg4'),
        5: this.$t('pages.diskScan_Msg3')
      },
      diskScanStatusTypeMap: {
        0: this.$t('pages.startScan'),
        1: this.$t('pages.endScan'),
        2: this.$t('pages.pauseScan')
      },
      procTypeMap: {
        fileDecrypt: this.$t('pages.decryptionApproval'),
        printOutsend: this.$t('pages.printOutApproval'),
        offline: this.$t('pages.offlineApproval'),
        outSend: this.$t('pages.directOutApproval'),
        changeFileLevel: this.$t('pages.classifiedApproval'),
        filePrint: this.$t('pages.printApproval'),
        cancelWatermark: this.$t('pages.cancelWatermarkApproval'),
        sensitiveFileOutSend: this.$t('pages.externalApprovalSensitive'),
        fileRelieveJurisdiction: this.$t('pages.readConversionApproval')
      },
      auditPassMap: {
        'true': this.$t('pages.approvalLog_Msg26'),
        'pass': this.$t('pages.approvalLog_Msg23'),
        'false': this.$t('pages.approvalLog_Msg28')
      },
      severityMap: {
        4: this.$t('pages.severityOptions4'),
        3: this.$t('pages.severityOptions3'),
        2: this.$t('pages.severityOptions2'),
        1: this.$t('pages.severityOptions1')
      },
      // 改成调用后台接口获取，原因详见jira:TRLD-17040
      lossTypeOptions: {},
      typeMap: {
        1: this.$t('pages.doDrip'),
        2: this.$t('pages.nomalCheck')
      },
      query: {},
      DefaultQuery: {
        page: 1,
        limit: 20,
        groupType: 0,
        keyword2: ''
      },
      notNullFields: [],
      detailQuery: {},
      DefaultDetailQuery: {
        page: 1,
        limit: 20,
        groupType: 0,
        detailTable: '',
        filterGroup: false,
        objectNames: [],
        notNullFields: this.notNullFields,
        colProp: '',
        severity: null,
        lossType: null,
        stgDefName: ''
      },
      // 左侧列表
      colModel: [
        { prop: 'object_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'object_name', label: 'user', hidden: () => this.colHidden(['terminal']) },
        { prop: 'count_sum_all', label: this.$t('report.riskyBehaviorsSum'), sort: true }
      ],
      // 终端操作员分析
      activeName: '',
      // 右侧终端操作员列表
      colModelDetail: [
        { prop: 'time', label: 'operateTime' },
        { prop: 'terminal', label: 'terminalName' },
        { prop: 'terminalGroup', label: 'terminalGroup' },
        { prop: 'type', label: 'type' },
        { prop: 'severity', label: 'severity' },
        { prop: 'dispatcher', label: 'sender1' },
        { prop: 'content', label: 'contentStg' }
      ],
      temp: {},
      defaultTemp: {
        // 终端操作员分析，上方小标题
        miniTitle: '',
        // 终端操作员分析，上方小方块内容
        itemOption: [
          { label: this.$t('report.generalDataViolationsSum'), value: 0, func: this.generalTableFlush },
          { label: this.$t('report.dripDataViolationsSum'), value: 0, func: this.dripTableFlush },
          { label: this.$t('report.fileOutgoingSum'), value: 0, func: this.fileOutTableFlush },
          { label: this.$t('table.diskScanSstSumAll'), value: 0, func: this.diskScanTableFlush }
        ],
        // 右侧表格数据
        terminalListMessage: [],
        // 雷达图 风险行为分析
        chartIndicator: [
          { name: this.$t('report.generalDataViolationsSum'), max: 20 },
          { name: this.$t('report.dripDataViolationsSum'), max: 20 },
          { name: this.$t('report.fileOutgoingSum'), max: 20 },
          { name: this.$t('table.diskScanSstSumAll'), max: 20 }
        ],
        chartsDatasRadar: [
          {
            name: this.$t('report.riskBehavior'),
            value: [0, 0, 0, 0],
            areaStyle: {
              color: '#32dadd'
            }
          }
        ],
        chartOptionRadar: {
          title: {
            text: this.$t('report.riskBehavior'),
            left: 'center'
          },
          radar: {
            radius: '40%'
          },
          toolbox: {
            show: false
          }
        }
      }
    }
  },
  computed: {

  },
  created() {
    this.getSensitiveLossType()
    this.active = this.drillDown ? 0 : -1
    this.activeName = this.drillDown ? 'terminalMsg' : 'statistics'
    this.resetTemp()
    this.query = Object.assign({}, this.DefaultQuery)
  },
  async mounted() {
    await this.$refs['terminalUserList'].execRowDataApi()
    const tableData = this.$refs['terminalUserList'].getDatas()
    // 如果左侧列表长度不为0，初始化页面时显示第一条数据的详情
    if (tableData[0]) {
      this.currentChangeLeft(tableData[0])
    }
  },
  activated() {},
  methods: {
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        res.data.forEach(item => {
          this.lossTypeOptions[item.lossType] = item.lossDesc
        })
      }).catch(err => {
        console.error('获取泄露方式数据失败:', err)
      })
    },
    /**
     *左边表格重置
     * */
    resetLeftQuery() {
      this.query.keyword2 = ''
    },
    /**
     *左边表格搜索
     * */
    async handleFilterLeft() {
      this.query.page = 1
      // console.log('query', this.query)
      await this.$refs['terminalUserList'].execRowDataApi(this.query)
      const tableData = this.$refs['terminalUserList'].getDatas()
      if (tableData[0]) {
        this.currentChangeLeft(tableData[0])
      } else {
        this.resetTemp()
        this.detailQuery.objectNames.splice(0)
        this.detailQuery.page = 1
        if (this.$refs.deptMsgList) {
          this.$refs.deptMsgList.clearRowData()
          this.$refs.deptMsgList.clearPageData()
        }
      }
    },
    /**
     *右边表格搜索
     * */
    handleFilterRight() {
      this.detailQuery.page = 1
      // console.log('query', this.query)
      this.$refs['deptMsgList'].execRowDataApi(this.query)
    },
    /**
     *高级查询弹框显示
     * */
    handleAdvanceQuery() {
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
    },
    /**
     * 高级查询，查询操作
     * */
    searchBtn() {
      this.detailQuery.page = 1
      if (this.detailQuery.severity === '') {
        this.detailQuery.severity = null
      }
      if (this.detailQuery.lossType === '') {
        this.detailQuery.lossType = null
      }
      this.$refs['deptMsgList'].execRowDataApi(this.detailQuery)
    },
    /**
     *重置《高级查询重置按钮》
     * */
    resetAdvancedQuery() {
      this.detailQuery.severity = null
      this.detailQuery.lossType = null
      this.detailQuery.stgDefName = ''
    },
    detailMethod: getDetailPage,
    generalTableFlush() {
      if (!this.drillDown) {
        return
      }
      this.active = 0 // 点击时，切换选中索引
      this.colModelDetail = this.getGeneralCol()
      this.detailMethod = getDetailPage
      this.detailQuery.detailTable = 'dwd_general_scan_violate_log'
      this.detailQuery.page = 1
      checkDetailAble(this.$parent.tempQuery).then(() => {
        this.$refs.deptMsgList.execRowDataApi(this.detailQuery)
      })
    },
    dripTableFlush() {
      if (!this.drillDown) {
        return
      }
      this.active = 1 // 点击时，切换选中索引
      this.colModelDetail = this.getDripDetailCol()
      this.detailMethod = getDetailPage
      this.detailQuery.detailTable = 'dwd_drip_scan_violate_log'
      this.detailQuery.page = 1
      checkDetailAble(this.$parent.tempQuery).then(() => {
        this.$refs.deptMsgList.execRowDataApi(this.detailQuery)
      })
    },
    fileOutTableFlush() {
      if (!this.drillDown) {
        return
      }
      this.active = 2 // 点击时，切换选中索引
      if (this.dialogStatus == 'terminal') {
        this.$refs.deptMsgList.clearRowData()
        this.$refs.deptMsgList.clearPageData()
      } else {
        this.colModelDetail = this.getTrwfeCol()
        this.detailMethod = getTrwfeLogDetail
        this.detailQuery.colProp = 'sensitiveFileOutSendSumAll'
        this.detailQuery.page = 1
        checkDetailAble(this.$parent.tempQuery).then(() => {
          this.$refs.deptMsgList.execRowDataApi(this.detailQuery)
        })
      }
    },
    diskScanTableFlush() {
      if (!this.drillDown) {
        return
      }
      this.active = 3 // 点击时，切换选中索引
      this.colModelDetail = this.getDiskScanDetailCol()
      this.detailMethod = getDiskScanLogDetail
      this.detailQuery.colProp = 'diskScanSstSumAll'
      this.detailQuery.page = 1
      checkDetailAble(this.$parent.tempQuery).then(() => {
        this.$refs.deptMsgList.execRowDataApi(this.detailQuery)
      })
    },
    /**
     * 将终端或操作员的表数据添加到表格中
     * */
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listSensitiveSymptomTerminalUserData(searchQuery)
    },
    rowDetailApi(option) {
      const searchQuery = Object.assign({}, this.detailQuery, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      const conditonMap = {}
      const conditonLikeMap = {}
      if (this.active == 0 || this.active == 1) {
        if (this.detailQuery.severity != null) {
          conditonMap.severity = this.detailQuery.severity
          searchQuery.severity = null
        }
        if (this.detailQuery.stgDefName) {
          conditonLikeMap.stg_def_name = this.detailQuery.stgDefName
          searchQuery.stg_def_name = null
        }
      }
      if (this.active == 0) {
        if (this.detailQuery.lossType != null) {
          conditonMap.loss_type = this.detailQuery.lossType
          searchQuery.loss_type = null
        }
      }
      searchQuery.condition = conditonMap
      searchQuery.likeCondition = conditonLikeMap
      return this.detailMethod(searchQuery)
    },
    /**
     * 获取左边列表行点击时的信息（右边的详细信息）
     * @param row 当前行信息
     * */
    currentChangeLeft(row) {
      this.temp.miniTitle = row.object_name
      this.temp.itemOption[0].value = row.general_scan_violate_log_sum_all
      this.temp.itemOption[1].value = row.drip_scan_violate_log_sum_all
      this.temp.itemOption[2].value = row.sensitive_file_out_send_sum_all
      this.temp.itemOption[3].value = row.disk_scan_sst_sum_all
      this.temp.chartIndicator[0].max = row.max_general_scan_violate_log_sum_all
      this.temp.chartIndicator[1].max = row.max_drip_scan_violate_log_sum_all
      this.temp.chartIndicator[2].max = row.max_sensitive_file_out_send_sum_all
      this.temp.chartIndicator[3].max = row.max_disk_scan_sst_sum_all
      this.temp.chartsDatasRadar[0].value = [row.general_scan_violate_log_sum_all, row.drip_scan_violate_log_sum_all, row.sensitive_file_out_send_sum_all, row.disk_scan_sst_sum_all]
      // 查询违规明细数据
      this.detailQuery.objectNames.splice(0, 1, row.object_id + row.object_name)
      this.generalTableFlush()
    },
    /**
     * 页面初始化
     * */
    resetTemp() {
      this.temp = { ...this.defaultTemp }
      this.detailQuery = Object.assign({}, this.DefaultDetailQuery)
      this.query = Object.assign({}, this.DefaultQuery)
    },
    /**
       * 根据终端还是操作员控制表格列的显示隐藏
       * */
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    /**
       * tab点击事件，如果不写，切换时图表只有小小一点点
       * @param pane
       * @param event
       */
    tabClick(pane, event) {
      if (pane.name == 'statistics') {
        this.$refs['chart'].__resizeHandler()
        this.isShowGrid = false
      } else {
        this.isShowGrid = true
      }
    },
    getTrwfeCol() {
      return [
        { prop: 'create_time', label: 'operateTime', width: '150', fixed: true },
        { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
        { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
        { prop: 'proc_type_name', label: 'floatType', width: '100', formatter: row => {
          return this.procTypeMap[row.category]
        } },
        { prop: 'audit_pass_name', label: 'approvalResult', width: '100', formatter: row => {
          return this.auditPassMap[row.audit_pass]
        } }
      ]
    },
    getGeneralCol() {
      return [
        { prop: 'create_time', label: 'operateTime', width: '150', fixed: true },
        { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
        { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
        { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
        { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
        { prop: 'loss_type_name', label: 'lossType', width: '100', formatter: row => {
          return this.lossTypeOptions[row.loss_type]
        } },
        { prop: 'severity_name', label: 'severity', width: '80', formatter: (row) => {
          return this.severityMap[row.severity]
        } },
        { prop: 'file_name', label: 'sensitiveFile', width: '150', formatter: (row) => {
          return row.file_name + (row.child_file_path ? row.child_file_path : '')
        } },
        { prop: 'stg_def_name', label: 'contentStg', width: '100' },
        { prop: 'sender_ip', label: 'sender1', width: '150', formatter: this.senderIpFormatter }
      ]
    },
    getDripDetailCol() {
      return [
        { prop: 'create_time', label: 'operateTime', width: '150', fixed: true },
        { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
        { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
        { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
        { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
        { prop: 'severity_name', label: 'severity', width: '100', formatter: (row) => {
          return this.severityMap[row.severity]
        } },
        { prop: 'stg_def_name', label: 'contentStg', width: '100' },
        { prop: 'sender_ip', label: 'sender1', width: '100', formatter: this.senderIpFormatter }
      ]
    },
    senderIpFormatter(row, data) {
      let result = ''
      if (row.sender) result += row.sender
      if (row.sender_ip) {
        result += result.length > 0 ? ('(' + row.sender_ip + ')') : row.sender_ip
      }
      return result
    },
    getDiskScanDetailCol() {
      return [
        { prop: 'create_time', label: 'operateTime', width: '150', fixed: true },
        { prop: 'terminal_name', label: 'terminalName', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal' },
        { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department' },
        { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
        { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
        { prop: 'op_type_name', label: 'operateType', width: '150', formatter: (row, data) => {
          return this.diskScanOpTypeMap[row.op_type]
        } },
        { prop: 'start_time', label: 'startTime', width: '150' },
        { prop: 'end_time', label: 'endTime', width: '150' },
        { prop: 'status_type_name', label: 'taskStatus', width: '100', formatter: (row) => {
          return this.diskScanStatusTypeMap[row.status_type]
        } },
        { prop: 'proc_total', label: 'totalNumberScan', width: '110' },
        { prop: 'proc_suc', label: 'numberSuccessProcess', width: '100' },
        { prop: 'proc_fail', label: 'numberFailProcess', width: '100' },
        { prop: 'sst_file', label: 'numberSensitive', width: '100' },
        { prop: 'nonsst_file', label: 'numberNonSensitive', width: '110' }
      ]
    }
  }
}
</script>

<style lang='scss' scoped>
  .activeClass{
    color:#409EFF
  }
  .data-item-details {
    .content{
      label, span{
        font-size: 14px;
      }
      label{
        width: 145px;
      }
      span{
        width: 50px;
      }
    }
  }
</style>
