<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.violationTerminal')"
      :fullscreen="fullscreen"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="squareList"
        :stripe="false"
        :col-model="colModel"
        :row-data-api="getData"
        :autoload="false"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSensitiveSymptomTerminalUserData
} from '@/api/report/baseReport/sensitiveControl/sensitiveContent'

export default {
  name: 'DlgTerminal',
  components: { },
  props: {
    // 状态，区分终端还是操作员
    dialogStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      query: { // 查询条件
        page: 1,
        limit: 20
      },
      colModel: [
        { prop: 'object_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'object_name', label: 'userName', hidden: () => this.colHidden(['terminal']) },
        { prop: 'general_scan_violate_log_sum_all', label: this.$t('report.generalDataViolationsSum') },
        { prop: 'drip_scan_violate_log_sum_all', label: this.$t('report.dripDataViolationsSum') },
        { prop: 'sensitive_file_out_send_sum_all', label: this.$t('report.fileOutgoingSum') },
        { prop: 'disk_scan_sst_sum_all', label: 'diskScanSstSumAll' }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['squareList']
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  created() {
  },
  methods: {
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    /**
     * 获取数据信息
     * */
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listSensitiveSymptomTerminalUserData(searchQuery)
    },
    /**
       * 显示弹框
       */
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.query.page = 1
        this.gridTable.execRowDataApi(this.query);
      })
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

