<template>
  <comparative-trend
    :api="'getSensitiveKeywordCompareData'"
    :api-dept="'getSensitiveKeywordCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'SensitiveKeywordLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '敏感关键字',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'triggerCountSum', label: 'sensitiveKeywordTriggerCount' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['triggerCountSum']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
