<template>
  <Statistical
    ref="statistical"
    :api="'getSensitiveKeywordLogPie'"
    :default-col-model="defaultColModel"
    :sub-col-model="subColModel"
    :custom-list="customList"
    :count-by-object="3"
    :type-options="typeOptions"
    :show-group-type="showGroupType"
    :x-axis-label="xAxisLabel"
    :customized-condition="customizedCondition"
    :drill-down="hasPermission('820')"
    :exportable="hasPermission('821')"
    @handleSelect="handleCustomColumn"
    @getCountByObject="handleShowGroupType"
    @subColModelProcessing="handleSubColModel"
    @customizedCondition="handleCustomizedCondition"
    @customizedObjectNames="handleCustomizedObjectNames"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { getSensitiveKeywordNumDetailCol } from '@/utils/report'

export default {
  name: 'SensitiveKeywordLogStatistical',
  components: { Statistical },
  data() {
    return {
      customizedCondition: true,
      xAxisLabel: this.$t('table.sensitiveKeyword'),
      showGroupType: false,
      typeOptions: [{ value: 1, label: this.$t('pages.conutType1') }, { value: 2, label: this.$t('pages.conutType2') }, { value: 3, label: this.$t('pages.countType3') }],
      customList: [
        { prop: 'triggerCountSum', label: 'sensitiveKeywordTriggerCount', width: '150', sort: true, value: 1, detailTable: 'dwd_sensitive_keyword_log', detailCol: getSensitiveKeywordNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'triggerCount', label: 'sensitiveKeywordTriggerCount', width: '150', sort: true, value: 1, detailTable: 'dwd_sensitive_keyword_log', detailCol: getSensitiveKeywordNumDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'triggerCountSum', label: 'sensitiveKeywordTriggerCount', width: '150', sort: true, detailTable: 'dwd_sensitive_keyword_log', detailCol: getSensitiveKeywordNumDetailCol() },
        { prop: 'keywordPercent', label: 'percent', width: '150' }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'keyword', label: 'sensitiveKeyword', width: '150', sort: true, joinSearch: true, searchCol: 'keyword' },
        { prop: 'triggerCount', label: 'sensitiveKeywordTriggerCount', width: '150', sort: true, detailTable: 'dwd_sensitive_keyword_log', detailCol: getSensitiveKeywordNumDetailCol() },
        { prop: 'keywordPercent', label: 'percent', width: '150' }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    },
    handleShowGroupType(countByObject) {
      // 敏感关键字报表“按关键字统计”类型不展示“按部门统计”
      this.showGroupType = 3 !== countByObject

      if (!this.showGroupType) {
        this.$refs.statistical.query.groupType = 0
        this.xAxisLabel = this.$t('table.sensitiveKeyword')
        this.customizedCondition = true
      } else {
        this.xAxisLabel = ''
        this.customizedCondition = false
      }
    },
    handleSubColModel(countByObject) {
      const index = this.subColModel.findIndex(item => item.prop === 'groupName')
      if (index < 0 && 3 === countByObject) {
        this.subColModel.splice(1, 1, { prop: 'groupName', label: 'dept', width: '150', sort: true, joinSearch: true, searchCol: 'user_group_name' })
      }

      if (index > 0 && 3 !== countByObject) {
        this.subColModel.splice(1, 1, { prop: 'keyword', label: 'sensitiveKeyword', width: '150', sort: true, joinSearch: true, searchCol: 'keyword' })
      }
    },
    handleCustomizedCondition(val, callback) {
      const condition = {}
      condition['keyword'] = val
      callback(condition)
    },
    handleCustomizedObjectNames(val, callback) {
      callback(val.label)
    }
  }
}
</script>
