<template>
  <Trend :api="'getSensitiveKeywordLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('825')" :exportable="hasPermission('826')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getSensitiveKeywordNumDetailCol } from '@/utils/report'

export default {
  name: 'SensitiveKeywordLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'triggerCountSum', label: 'sensitiveKeywordTriggerCount', width: '150', sort: true, detailTable: 'dwd_sensitive_keyword_log', detailCol: getSensitiveKeywordNumDetailCol() }
      ],
      customList: [
        { prop: 'triggerCountSum', label: 'sensitiveKeywordTriggerCount', width: '150', sort: true, value: 1, detailTable: 'dwd_sensitive_keyword_log', detailCol: getSensitiveKeywordNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
