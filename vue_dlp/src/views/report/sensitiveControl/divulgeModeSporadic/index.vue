<template>
  <divulge-mode
    type="sporadic"
    detail-table="dwd_sporadic_violate_log"
    tip="报表显示的数据为零星检测触发n次的产生的数据（注：零星检测触发一次可能会产生多条数据）"
    :homepage-data-api="homepageDataApi"
    :dept-chart-api="deptChartApi"
    :terminal-user-chart-api="terminalUserChartApi"
    :dept-api="deptApi"
    :terminal-user-api="terminalUserApi"
    :dept-analysis-api="deptAnalysisApi"
    :terminal-user-analysis-api="terminalUserAnalysisApi"
    :trend-analysis-api="trendAnalysisApi"
    :drill-down="hasPermission('845')"
  >
  </divulge-mode>
</template>

<script>
import DivulgeMode from '@/views/report/sensitiveControl/common/divulgeMode/index'
import { getSporadicDivulgeModeHomepageData, getSporadicDivulgeModeDeptChartData, getSporadicDivulgeModeTerminalUserChartData, listSporadicDivulgeModeDeptData, listSporadicDivulgeModeTerminalUserData,
  getSporadicDivulgeModeDeptAnalysisData, getSporadicDivulgeModeTerminalUserAnalysisData, getSporadicDivulgeModeTrendAnalysisData } from '@/api/report/baseReport/sensitiveControl/sporadicDivulgeMode'

export default {
  name: 'DivulgeModeSporadic',
  components: { DivulgeMode },
  data() {
    return {
      homepageDataApi: getSporadicDivulgeModeHomepageData,
      deptChartApi: getSporadicDivulgeModeDeptChartData,
      terminalUserChartApi: getSporadicDivulgeModeTerminalUserChartData,
      deptApi: listSporadicDivulgeModeDeptData,
      terminalUserApi: listSporadicDivulgeModeTerminalUserData,
      deptAnalysisApi: getSporadicDivulgeModeDeptAnalysisData,
      terminalUserAnalysisApi: getSporadicDivulgeModeTerminalUserAnalysisData,
      trendAnalysisApi: getSporadicDivulgeModeTrendAnalysisData
    }
  }
}
</script>
