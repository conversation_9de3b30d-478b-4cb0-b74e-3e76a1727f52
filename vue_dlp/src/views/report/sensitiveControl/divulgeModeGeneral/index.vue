<template>
  <divulge-mode
    type="general"
    detail-table="dwd_general_violate_log"
    tip="报表显示的数据为常规检测的详细数据"
    :homepage-data-api="homepageDataApi"
    :dept-chart-api="deptChartApi"
    :terminal-user-chart-api="terminalUserChartApi"
    :dept-api="deptApi"
    :terminal-user-api="terminalUserApi"
    :dept-analysis-api="deptAnalysisApi"
    :terminal-user-analysis-api="terminalUserAnalysisApi"
    :trend-analysis-api="trendAnalysisApi"
    :drill-down="hasPermission('830')"
  >
  </divulge-mode>
</template>

<script>
import DivulgeMode from '@/views/report/sensitiveControl/common/divulgeMode/index'
import { getGeneralDivulgeModeHomepageData, getGeneralDivulgeModeDeptChartData, getGeneralDivulgeModeTerminalUserChartData, listGeneralDivulgeModeDeptData, listGeneralDivulgeModeTerminalUserData,
  getGeneralDivulgeModeDeptAnalysisData, getGeneralDivulgeModeTerminalUserAnalysisData, getGeneralDivulgeModeTrendAnalysisData } from '@/api/report/baseReport/sensitiveControl/generalDivulgeMode'

export default {
  name: 'DivulgeModeGeneral',
  components: { DivulgeMode },
  data() {
    return {
      homepageDataApi: getGeneralDivulgeModeHomepageData,
      deptChartApi: getGeneralDivulgeModeDeptChartData,
      terminalUserChartApi: getGeneralDivulgeModeTerminalUserChartData,
      deptApi: listGeneralDivulgeModeDeptData,
      terminalUserApi: listGeneralDivulgeModeTerminalUserData,
      deptAnalysisApi: getGeneralDivulgeModeDeptAnalysisData,
      terminalUserAnalysisApi: getGeneralDivulgeModeTerminalUserAnalysisData,
      trendAnalysisApi: getGeneralDivulgeModeTrendAnalysisData
    }
  }
}
</script>
