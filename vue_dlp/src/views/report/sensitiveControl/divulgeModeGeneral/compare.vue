<template>
  <comparative-trend
    :api="'getGeneralViolateLossTypeCompareData'"
    :api-dept="'getGeneralViolateLossTypeCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'

export default {
  name: 'GeneralViolateLossTypeCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '违规泄露方式',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'ftpUploadFileSumAll', label: 'ftpUploadFileSumAll' },
        { prop: 'emailTransferContentSumAll', label: 'emailTransferContentSumAll' },
        { prop: 'usbCopyFileSumAll', label: 'usbCopyFileSumAll' },
        { prop: 'usbCutFileSumAll', label: 'usbCutFileSumAll' },
        { prop: 'usbSaveFileSumAll', label: 'usbSaveFileSumAll' },
        { prop: 'filePrintContentSumAll', label: 'filePrintContentSumAll' },
        { prop: 'remoteShareFileSumAll', label: 'remoteShareFileSumAll' },
        { prop: 'imSendContentSumAll', label: 'imSendContentSumAll' },
        { prop: 'imSendFileSumAll', label: 'imSendFileSumAll' },
        { prop: 'forumPostContentSumAll', label: 'forumPostContentSumAll' },
        { prop: 'cdBurnFileSumAll', label: 'cdBurnFileSumAll' },
        { prop: 'webAccessContentSumAll', label: 'webAccessContentSumAll' },
        { prop: 'webUploadFileSumAll', label: 'webUploadFileSumAll' },
        { prop: 'webPasteContentSumAll', label: 'webPasteContentSumAll' },
        { prop: 'localShareFileSumAll', label: 'localShareFileSumAll' },
        { prop: 'emailAttachmentSumAll', label: 'emailAttachmentSumAll' },
        { prop: 'bluetoothSendFileSumAll', label: 'bluetoothSendFileSumAll' },
        { prop: 'webDiskUploadFileSumAll', label: 'webDiskUploadFileSumAll' },
        { prop: 'webDiskDownloadFileSumAll', label: 'webDiskDownloadFileSumAll' },
        { prop: 'imDownloadFileSumAll', label: 'imDownloadFileSumAll' },
        { prop: 'imReceiveFileSumAll', label: 'imReceiveFileSumAll' },
        { prop: 'adbSendFileSumAll', label: 'adbSendFileSumAll' },
        { prop: 'mtpSendFileSumAll', label: 'mtpSendFileSumAll' },
        { prop: 'cdDownloadFileSumAll', label: 'cdDownloadFileSumAll' },
        { prop: 'webDownloadFileSumAll', label: 'webDownloadFileSumAll' },
        { prop: 'remoteOutgoingFileSumAll', label: 'remoteOutgoingFileSumAll' },
        { prop: 'remoteToolUploadFileSumAll', label: 'remoteToolUploadFileSumAll' },
        { prop: 'aiTransfersTextContentSumAll', label: 'aiTransfersTextContentSumAll' },
        { prop: 'aiUploadFileSumAll', label: 'aiUploadFileSumAll' }
      ],
      // 违规方式对应表格的prop
      alarmTypeMap: {
        'ftpUploadFileSumAll': 0,
        'emailTransferContentSumAll': 1,
        'usbCopyFileSumAll': 2,
        'usbCutFileSumAll': 3,
        'usbSaveFileSumAll': 4,
        'filePrintContentSumAll': 5,
        'remoteShareFileSumAll': 7,
        'imSendContentSumAll': 8,
        'imSendFileSumAll': 9,
        'forumPostContentSumAll': 11,
        'cdBurnFileSumAll': 13,
        'webAccessContentSumAll': 14,
        'webUploadFileSumAll': 15,
        'webPasteContentSumAll': 16,
        'localShareFileSumAll': 17,
        'emailAttachmentSumAll': 18,
        'bluetoothSendFileSumAll': 20,
        'webDiskUploadFileSumAll': 23,
        'webDiskDownloadFileSumAll': 24,
        'imDownloadFileSumAll': 25,
        'imReceiveFileSumAll': 26,
        'adbSendFileSumAll': 27,
        'mtpSendFileSumAll': 29,
        'cdDownloadFileSumAll': 30,
        'webDownloadFileSumAll': 31,
        'remoteOutgoingFileSumAll': 32,
        'remoteToolUploadFileSumAll': 33,
        'aiTransfersTextContentSumAll': 34,
        'aiUploadFileSumAll': 35
      },
      // 统计项设置 默认选中
      statisticalListCheck: ['emailTransferContentSumAll', 'usbCopyFileSumAll', 'filePrintContentSumAll', 'imSendFileSumAll',
        'webAccessContentSumAll', 'webUploadFileSumAll', 'emailAttachmentSumAll']
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getSensitiveLossType()
  },
  methods: {
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        const map = {}
        res.data.forEach(item => {
          map[item.lossType] = item.lossDesc
        })
        this.statisticalList = this.statisticalList.filter(item => {
          return map[this.alarmTypeMap[item.prop]] != null && map[this.alarmTypeMap[item.prop]] != undefined
        })
        this.statisticalListCheck = this.statisticalListCheck.filter(item => {
          return map[this.alarmTypeMap[item]] != null && map[this.alarmTypeMap[item]] != undefined
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
