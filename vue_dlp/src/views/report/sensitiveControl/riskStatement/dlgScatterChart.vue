<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <grid-table
        ref="scatterChartTable"
        :stripe="false"
        row-key="id"
        :col-model="colModel"
        :row-datas="list"
        :indent="0"
        :height="325"
        :multi-select="false"
        default-expand-all
        :show-pager="false"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DlgScatterChart',
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      query: {
        page: 1,
        groupType: 0
      },
      list: [],
      colModel: [
        { prop: 'terminalName', label: 'terminalName', hidden: () => this.$parent.tempQuery.countByObject === 1 },
        { prop: 'userName', label: 'user', hidden: () => this.$parent.tempQuery.countByObject === 2 },
        { prop: 'eventCount', label: 'riskCount', sort: true },
        { prop: 'riskLevels', label: 'riskLevel', width: '110', sort: true, formatter: this.riskLevelsFormatter }
      ]
    }
  },
  computed: {},
  methods: {
    /**
     * 显示弹框
     * @param option 父组件传过来的表格数据
     */
    show(option) {
      this.dialogVisible = true
      this.list = option
    },
    riskLevelsFormatter(row) {
      if (row.riskLevels === 0) {
        return this.$t('pages.severityOptions1')
      }
      if (row.riskLevels === 1) {
        return this.$t('pages.severityOptions2')
      }
      if (row.riskLevels === 2) {
        return this.$t('pages.severityOptions3')
      }
      if (row.riskLevels === 3) {
        return this.$t('pages.severityOptions4')
      }
    }
  }
}
</script>

