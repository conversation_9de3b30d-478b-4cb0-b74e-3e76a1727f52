<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="dlgTitle"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <grid-table
        ref="violateDetailTable"
        :stripe="false"
        :row-key="getRowKey"
        :col-model="colModel"
        :row-data-api="getData"
        :indent="0"
        :height="325"
        :multi-select="false"
        default-expand-all
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getDetailPage } from '@/api/report/baseReport/issueReport/issue'
import moment from 'moment'
import { getAllDef } from './riskReportApi'

export default {
  name: 'DlgTotalViolation',
  props: {
    dlgTitle: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      levelMap: {
        1: this.$t('pages.severityOptions1'),
        2: this.$t('pages.severityOptions2'),
        3: this.$t('pages.severityOptions3'),
        4: this.$t('pages.severityOptions4')
      },
      dialogVisible: false,
      query: {
        page: 1,
        groupType: 0
      },
      colModel: [
        { prop: 'risk_level', label: 'riskLevel', width: '110', formatter: (row) => this.levelMap[row.risk_level] },
        { prop: 'risk_def_id', label: 'riskType', width: '110', formatter: (row) => this.defMap[row.risk_def_id] },
        { prop: 'terminal_name', label: 'terminalName', hidden: () => this.$parent.tempQuery.countByObject === 1 },
        { prop: 'term_group_name', label: 'terminalGroup', hidden: () => this.$parent.tempQuery.countByObject === 1 },
        { prop: 'user_name', label: 'user', hidden: () => this.$parent.tempQuery.countByObject === 2 },
        { prop: 'user_group_name', label: 'userGroup', hidden: () => this.$parent.tempQuery.countByObject === 2 },
        { prop: 'create_time', label: 'createTime' }
      ],
      defMap: {}
    }
  },
  computed: {
  },
  async created() {
    const resp = await getAllDef()
    for (const i of resp.data) {
      this.defMap[i.id] = i.riskName
    }
  },
  methods: {
    /**
       * 获取数据信息
       * */
    getData(option) {
      const searchQuery = Object.assign({}, this.$parent.tempQuery, this.query, option)
      searchQuery.detailTable = 'dwd_risk_event'
      return getDetailPage(searchQuery)
    },
    /**
       * 显示弹框
       */
    show() {
      this.dialogVisible = true
      if (this.$refs['violateDetailTable']) {
        this.$refs['violateDetailTable'].execRowDataApi(this.query)
      }
    },
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    }
  }
}
</script>
