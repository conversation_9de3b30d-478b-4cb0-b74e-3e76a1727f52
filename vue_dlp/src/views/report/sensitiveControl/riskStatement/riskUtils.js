
export function toRiskLevel(x) {
  if (x >= 0 && x < 2) return 0
  if (x >= 2 && x < 4) return 1
  if (x >= 4 && x < 6) return 2
  if (x >= 6 && x < 8) return 3
}

/**
 * 带种子的随机数生成器
* 使用线性同余生成器（LCG）算法，它是一种简单且广泛使用的伪随机数生成器：
 */
class SeededRandom {
  constructor(seed) {
    // 将种子转换为整数
    this.seed = seed % 2147483647;
    if (this.seed <= 0) this.seed += 2147483646;
  }

  // 生成 [0, 1) 范围内的伪随机数
  random() {
    // 线性同余公式
    this.seed = (this.seed * 16807) % 2147483647;
    return (this.seed - 1) / 2147483646;
  }

  // 生成 [min, max) 范围内的随机数
  range(min, max) {
    return min + this.random() * (max - min);
  }

  // 生成 [min, max] 范围内的随机整数
  rangeInt(min, max) {
    return Math.floor(this.range(min, max + 1));
  }
}

/**
 * 将数值随机转换到指定范围内，相同参数返回相同结果
 * @param {number} value - 输入值（用于生成种子）
 * @param {number} min - 目标范围最小值
 * @param {number} max - 目标范围最大值
 * @param {number} [extraSeed=0] - 额外种子（可选）
 * @returns {number} 转换后的随机值
 */
function randomTransform(value, min, max, extraSeed = 0) {
  // 使用输入值和额外种子生成唯一种子
  const seed = Math.floor(value * 1000) + extraSeed;
  const rng = new SeededRandom(seed);
  
  // 生成范围内的随机数
  return rng.range(min, max);
}

// 对每个风险等级的散点做下随机变化，避免串成一条直线
export function randomize(riskLevel, index = 0) {
  // if (riskLevel == 0) {
  //   // 0 - 2之间的随机数
  //   return Math.random() * 2
  // } else if (riskLevel == 1) {
  //   // 2 - 4之间的随机数
  //   return Math.random() * 2 + 2
  // } else if (riskLevel == 2) {
  //   // 4 - 6之间的随机数
  //   return Math.random() * 2 + 4
  // } else if (riskLevel == 3) {
  //   // 6 -8之间的随机数
  //   return Math.random() * 2 + 6
  // }
  const min = riskLevel * 2
  const max = min + 2
  const seed = (1 + index) * 100000
  return randomTransform(riskLevel, min, max, seed)
}
