<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query">
        <i class="el-icon-s-home" style="position: absolute; left: 24px; top: 16px;"></i>
        <!--面包屑-->
        <div style="width: 510px; float: left;">
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <!--上方搜索条件-->
        <div style="float: right">
          <!--查询条件组件-->
          <query ref="queryData" :risk="true"/>
          <!--图表解读-->
          <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
          <!-- <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
          <el-button type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>-->
        </div>
      </div>

      <!-- 页面无数据 -->
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>

      <!--分析初始页面-->
      <div v-if="inceptionDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
            <div class="icon">
              <svg-icon :icon-class="item.icon"></svg-icon>
            </div>
            <div class="content">
              <label :title="item.label">{{ item.label }}</label>
              <el-button type="text" :class="{'underline': item.clickable }" @click="item.func">{{ temp.itemValue[item.key] }}</el-button>
            </div>
          </div>
        </div>
        <!--中间散点图和饼图-->
        <div class="chart-panel">
          <div class="chart-item flex-2">
            <div class="mini-title">
              <el-tooltip effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.splattering_Msg') }}</div>
                <i class="el-icon-info" style="position:absolute; left: 5px; top: 5px; z-index: 1"/>
              </el-tooltip>
            </div>
            <!--散点图-->
            <scatter-diagram-chart
              :chart-source="temp.scatterChartSource"
              :chart-option="temp.scatterChartOption"
              :y-avg="temp.scatterYAvg"
              :median="temp.median"
              :click="scatterClick"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.riskScatter.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.riskScatter + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title">
            </div>
            <!--饼图-->
            <pie-chart
              :chart-data="temp.riskTypeBarChartData"
              :chart-option="temp.riskTypeBarChartOption"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.riskDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.riskDistribute + '</div>'"></div>
            </div>
          </div>
        </div>

        <!--下方对部门，终端（操作员），泄露方式的分别展示部分-->
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleDeptAnalysis">{{ $t('pages.deptRiskAnalysis') }}>></span>
              <!-- <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigDept"/>
              </el-tooltip> -->
            </div>
            <bar-chart
              :chart-data="temp.deptBarChartData"
              :chart-option="temp.deptBarChartOption"
              :x-axis-name="this.$t('pages.dept')"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.deptDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.deptDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleTerminalUserAnalysis">{{ terminalUserAnalysisTitle }}>></span>
              <!-- <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigTerminalUser"/>
              </el-tooltip> -->
            </div>
            <pie-chart
              :chart-data="temp.terminalUserPieChartData"
              :chart-option="temp.terminalUserPieChartOption"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.termOrUserDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.termOrUserDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title"><span @click="handleDivulgeAnalysis">{{ $t('pages.lossTypeRiskAnalysis') }}>></span></div>
            <bar-chart
              :chart-data="temp.divulgeBarChartData"
              :chart-option="temp.divulgeBarChartOption"
              :x-axis-name="''"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.lossTypeDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.lossTypeDistribute + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
      <!--部门分析-->
      <div v-if="deptDiv" class="detailsDiv">
        <analysis-dept :dialog-status="dialogStatus" :drill-down="drillDown"/>
      </div>
      <!--终端、操作员分析-->
      <div v-if="userDiv" class="detailsDiv">
        <analysis-terminal-user :dialog-status="dialogStatus" :drill-down="drillDown"/>
      </div>
      <!--泄露方式风险分析-->
      <div v-if="divulgeDiv" class="detailsDiv">
        <analysis-Divulge :dialog-status="dialogStatus" :drill-down="drillDown"/>
      </div>
    </div>
    <!--小方块（风险总数）点击弹框-->
    <violate-detail-dlg ref="totalViolationDlg" :dlg-title="$t('pages.totalRisks')"/>
    <!--小方块（部门）点击弹框-->
    <dept-dlg ref="deptDlg" :data-api="deptApi"/>
    <!--小方块（终端/操作员）点击击弹框-->
    <terminal-user-dlg ref="terminalUserDlg" :title="terminalUserDlgTitle" :data-api="terminalUserApi"/>
    <!--    部门、终端、操作员前五名小图标点击弹框-->
    <echart-dlg ref="echartDlg" :title="echartDlgTitle" :dept-chart-api="deptChartApi" :terminal-user-chart-api="terminalUserChartApi"/>
    <!--小方块（部门）点击弹框-->
    <scatter-chart-dlg ref="scatterDlg" :title="scatterDlgTitle"/>
  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
import ScatterDiagramChart from './ScatterDiagramChart'
import Query from '@/views/report/sensitiveControl/common/query'
import DeptDlg from '@/views/report/sensitiveControl/riskStatement/dlgDept'
import EchartDlg from '@/views/report/sensitiveControl/riskStatement/echartDlg'
import TerminalUserDlg from '@/views/report/sensitiveControl/riskStatement/dlgTerminalUser'
import ScatterChartDlg from '@/views/report/sensitiveControl/riskStatement/dlgScatterChart';
import ViolateDetailDlg from '@/views/report/sensitiveControl/riskStatement/dlgViolateDetail'
import analysisDept from '@/views/report/sensitiveControl/riskStatement/analysisDept'
import analysisTerminalUser from '@/views/report/sensitiveControl/riskStatement/analysisTerminalUser'
import analysisDivulge from '@/views/report/sensitiveControl/riskStatement/analysisDivulge'
import { getSummary, getDeptAggDetail, getTermOrUserAggDetail } from './riskReportApi'
import { getGeneralRuleTypeDeptChartData, getGeneralRuleTypeTerminalUserChartData } from '@/api/report/baseReport/sensitiveControl/generalRuleType'
import { toRiskLevel } from './riskUtils'

function median(arr) {
  // 首先对数组进行排序
  arr.sort(function(a, b) {
    return a - b;
  });

  var len = arr.length;
  // 如果数组长度为奇数，则中位数为中间值
  if (len % 2 !== 0) {
    return arr[Math.floor(len / 2)];
  } else {
    var mid1 = arr[len / 2 - 1];
    var mid2 = arr[len / 2];
    return (mid1 + mid2) / 2;
  }
}

export default {
  name: 'RiskStatement',
  components: { Query, BarChart, PieChart, ScatterDiagramChart, DeptDlg, EchartDlg, ScatterChartDlg, TerminalUserDlg, ViolateDetailDlg, analysisDept, analysisTerminalUser, analysisDivulge },
  props: {
  },
  data() {
    return {
      // 搜查查询
      query: {
        // 是否展示图表解读
        isExplain: true,
        dimBaseType: 1,
        dimValue: '',
        objectType: 1,
        countByObject: 2
      },
      tempQuery: {},
      terminalUserAnalysisTitle: this.$t('pages.termRiskAnalysis'),
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: '风险分析' }
      ],
      // 暂无数据
      noData: true,
      // 初始页面显示隐藏
      inceptionDiv: false,
      // 泄露方式分析显示隐藏
      divulgeDiv: false,
      // 操作员分析显示隐藏
      userDiv: false,
      // 部门分析显示隐藏
      deptDiv: false,
      // 违规终端/操作员弹窗名称
      terminalUserDlgTitle: '',
      // 散点图弹框标题
      scatterDlgTitle: '',
      // 放大柱状图弹框名称
      echartDlgTitle: '',
      echartStatus: '',
      // 弹框状态
      dialogStatus: '',
      // 上方小方块
      itemOption: [
        { label: this.$t('pages.totalRisks'), key: 'all', icon: 'total', func: this.totalViolationFun, clickable: true },
        { label: this.$t('pages.riskDept'), key: 'dept', icon: 'tree', func: this.deptFun, clickable: true },
        { label: this.$t('pages.riskTerm'), key: 'terminalUser', icon: 'terminal', func: this.terminalUserFun, clickable: true }
      ],
      // 图表解读
      commonInterpretation: `<span>图表解读：</span>`,
      temp: {},
      defaultTemp: {
        // 图表解读
        chartInterpretationContent: {
          // 风险象限图
          riskScatter: '',
          // 风险类型占比
          riskDistribute: '',
          // 部门占比
          deptDistribute: '',
          // 终端/操作员占比
          termOrUserDistribute: '',
          // 泄露方式占比
          lossTypeDistribute: ''
        },
        itemValue: {
          all: '0',
          dept: '0',
          terminalUser: '0'
        },
        // 散点图（每个点的坐标）
        scatterChartSource: [
          [2, 3],
          [-3, 2],
          [-2, -3],
          [4, -3]
        ],
        scatterYAvg: 0,
        median: 0,
        // 散点图option
        scatterChartOption: {
          title: {
            text: this.$t('pages.riskQuadrant'),
            subtext: '',
            left: 'center'
          }
        },
        // 风险类型占比统计图
        riskTypeBarChartData: [],
        riskTypeBarChartOption: {
          title: {
            text: this.$t('pages.riskTypeProportion')
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%'
            }
          ]
        },
        // 部门柱状图数据
        deptBarChartData: [],
        deptBarChartOption: {
          title: {
            text: this.$t('pages.deptRiskStatistics')
          },
          legend: {
            show: false
          }
        },
        // 终端/操作员数量图
        terminalUserPieChartData: [],
        terminalUserPieChartOption: {},
        // 泄露方式柱状统计图
        divulgeBarChartData: [],
        divulgeBarChartOption: {
          title: {
            text: this.$t('pages.lossTypeRiskStatistics')
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          yAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      },
      deptApi: getDeptAggDetail,
      // deptApi: listGeneralRuleTypeDeptData,
      terminalUserApi: getTermOrUserAggDetail,
      deptChartApi: getGeneralRuleTypeDeptChartData,
      terminalUserChartApi: getGeneralRuleTypeTerminalUserChartData,
      // 是否支持下钻
      drillDown: this.hasPermission('906')
    }
  },
  created() {
    this.itemOption[0].clickable = this.drillDown
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.defaultTemp.deptBarChartOption = this.getDeptBarChartOption(5)
      this.defaultTemp.terminalUserPieChartOption = this.getTerminalUserPieChartOption(5)
      this.defaultTemp.terminalUserPieChartOption.title.text = this.query.countByObject === 1 ? this.$t('pages.userRisk') : this.$t('pages.termRisk')
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 散点图点击时弹框显示该区域的节点信息
     * @param params 当前节点点击时echarts返回的信息
     * params.value返回的数据格式：[0, 5, undefined, undefined, 'win7-64', 10012]，分别代表：【风险等级、违规次数、操作员名称、操作员Id、终端名称、终端Id】
     * params.value[0]风险等级，0：轻微，1：一般，2：严重，3：非常严重可以判断出0，1在二三象限，2，3在一四象限（后台返回不是整数时，直接取整Math.floor(number)）
     * this.temp.scatterYAvg均值可以和违规次数params.value[1]对比大小，判断出某个点具体在哪个象限
     * */
    scatterClick(params) {
      // console.log('params', params)
      if (params.componentType === 'series') {
        // 1、根据是哪个象限，动态修改弹框的名称为对象象限名称
        const riskLevel = toRiskLevel(Math.floor(params.value[0]))
        // 低风险
        const lowRisk = riskLevel <= 1
        // 低频率
        const lowFrequency = params.value[1] < this.temp.scatterYAvg
        if (lowRisk && !lowFrequency) {
          this.scatterDlgTitle = this.$t('pages.highFrequencyLowRisk')
        } else if (lowRisk && lowFrequency) {
          this.scatterDlgTitle = this.$t('pages.lowFrequencyLowRisk')
        } else if (!lowRisk && !lowFrequency) {
          this.scatterDlgTitle = this.$t('pages.highFrequencyHighRisk')
        } else if (!lowRisk && lowFrequency) {
          this.scatterDlgTitle = this.$t('pages.lowFrequencyHighRisk')
        }
        // this.temp.scatterChartSource为所有点的数据
        // 2、foundObjects1先通过风险等级过滤出一部分数据
        let foundObjects1
        if (lowRisk) {
          foundObjects1 = this.temp.scatterChartSource.filter(obj => Math.floor(obj[0]) === 0 || Math.floor(obj[0]) === 1);
        } else {
          foundObjects1 = this.temp.scatterChartSource.filter(obj => Math.floor(obj[0]) === 2 || Math.floor(obj[0]) === 3);
        }
        // 3、foundObjects2在通过均值过滤出当前象限的数据
        let foundObjects2
        if (!lowFrequency) {
          foundObjects2 = foundObjects1.filter(obj => obj[1] >= this.temp.scatterYAvg);
        } else {
          foundObjects2 = foundObjects1.filter(obj => obj[1] < this.temp.scatterYAvg);
        }
        // console.log('this.temp.scatterChartSource', this.temp.scatterChartSource)
        // console.log('this.temp.scatterYAvg', this.temp.scatterYAvg)
        // console.log('foundObjects1', foundObjects1)
        // console.log('foundObjects2', foundObjects2)
        // 4、将过滤出来的数据拼接成表格需要的数据，传递给子应用，用于表格数据显示
        const tableList = []
        foundObjects2.forEach(item => {
          const obj = {
            terminalName: item[4],
            terminalId: item[5],
            userName: item[2],
            userId: item[3],
            eventCount: item[1],
            riskLevels: Math.floor(item[0])
          }
          tableList.push(obj)
        })
        this.$refs.scatterDlg.show(tableList)
      }
    },
    /**
       * 获取数据信息
       * */
    getData() {
      this.resetTemp()
      this.query = this.$refs['queryData'].getQuery()
      this.query.objectType = this.query.countByObject == 1 ? 2 : 1
      this.tempQuery = Object.assign({}, this.query)
      getSummary(this.query).then(resp => {
        if (resp.data) {
          if (!resp.data.riskCount) {
            this.inceptionDiv = false
            this.noData = true
            return
          }
          this.inceptionDiv = true
          this.noData = false
          // 图表解读
          if (resp.data.explanation) {
            this.temp.chartInterpretationContent = { ...this.temp.chartInterpretationContent, ...resp.data.explanation }
          }
          //  上方小方块
          this.temp.itemValue.all = resp.data.riskCount
          this.temp.itemValue.dept = resp.data.riskDeptCount
          this.temp.itemValue.terminalUser = resp.data.riskTermOrUserCount
          // 风险象限
          this.temp.scatterChartSource = resp.data.riskScatterList.map(v => [v.riskLevel - 1, v.eventCount, v.userName, v.userId, v.terminalName, v.termId])
          this.temp.scatterYAvg = Math.max(...this.temp.scatterChartSource.map(v => v[1])) / 2
          this.temp.median = median(this.temp.scatterChartSource.map(v => v[1]))

          // 风险类型分布
          this.temp.riskTypeBarChartData = Object.entries(resp.data.riskDistribute).map(v => {
            return { name: v[0], value: v[1] }
          })

          // 部门柱状图相关数据
          const deptDataObj = Object.entries(resp.data.deptDistribute)
            .map(v => {
              return {
                name: v[0],
                value: v[1]
              }
            })
            .sort((a, b) => b.value - a.value)
          this.temp.deptBarChartOption.xAxis.data = deptDataObj
            .map(v => v.name)
          this.temp.deptBarChartOption.series[0].data = deptDataObj.map(v => v.value)
          // this.temp.deptBarChartOption.title.text = '部门风险统计'

          // 终端操作员饼图相关数据
          this.temp.terminalUserPieChartData = Object.entries(resp.data.termOrUserDistribute)
            .map(v => {
              return {
                name: v[0],
                value: v[1]
              }
            })

          // 泄露方式柱状图相关数据
          const divulgeBarChartDataObj = Object.entries(resp.data.lossTypeDistribute)
            .map(v => {
              return {
                name: v[0],
                value: v[1]
              }
            })
            .sort((a, b) => b.value - a.value)
          this.temp.divulgeBarChartOption.yAxis.data = divulgeBarChartDataObj
            .map(v => v.name)
          this.temp.divulgeBarChartOption.series[0].data = divulgeBarChartDataObj
        } else {
          this.resetTemp()
          this.inceptionDiv = false
          this.noData = true
        }
      }).then(() => {
        this.triggerResize()
      })
    },
    /**
       * 搜索按钮点击（暂时原型主要是对统计类型变化时页面上显示变化的一个处理）
       * */
    loadReport() {
      this.getData()
      if (this.query.countByObject === 1) {
        // 操作员
        Object.assign(this.itemOption[2], { label: '违规操作员', icon: 'user' })
        this.terminalUserAnalysisTitle = this.$t('pages.userRiskAnalysis')
        this.temp.terminalUserPieChartOption.title.text = this.$t('pages.userRisk')
      } else {
        // 终端
        Object.assign(this.itemOption[2], { label: '违规终端', icon: 'terminal' })
        this.terminalUserAnalysisTitle = this.$t('pages.termRiskAnalysis')
        this.temp.terminalUserPieChartOption.title.text = this.$t('pages.termRisk')
      }
      // 泄露方式页面
      this.divulgeDiv = false
      // 操作员（终端）页面
      this.userDiv = false
      // 部门页面
      this.deptDiv = false
      // 趋势页面
      this.showFilePath = [{ id: 0, label: '风险分析' }]
    },
    getDeptBarChartOption(top) {
      return {
        title: {
          text: this.$t('pages.deptRiskStatistics'),
          subtext: this.$t('pages.topN', { num: top })
        },
        toolbox: {
          show: false
        },
        grid: {
          top: 60,
          width: '85%',
          bottom: 20,
          left: '5%',
          containLabel: true
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          splitArea: {
            show: false
          }
        },
        xAxis: {
          type: 'category',
          data: []
        },
        legend: {
          show: false
        },
        series: [
          {
            data: [],
            type: 'bar'
          }
        ]
      }
    },
    getTerminalUserPieChartOption(top) {
      return {
        title: {
          text: '',
          subtext: this.$t('pages.topN', { num: top })
        },
        toolbox: {
          show: false
        },
        legend: {
          show: false,
          left: 'center'
        },
        series: [
          {
            radius: '40%',
            center: ['50%', '55%']
          }
        ]
      }
    },
    /**
       * 违规总数(点击)
       */
    totalViolationFun() {
      if (this.drillDown) {
        this.$refs.totalViolationDlg.show()
      }
    },
    /**
       * 部门（点击）
       */
    deptFun() {
      this.$refs.deptDlg.show()
    },
    /**
       * 终端或操作员（点击）
       */
    terminalUserFun() {
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.terminalUserDlgTitle = '违规操作员'
      } else {
        // 终端
        this.terminalUserDlgTitle = '违规终端'
      }
      this.$refs.terminalUserDlg.show()
    },
    /**
       * 部门分析小图标点击
       * */
    handleBigDept() {
      this.echartDlgTitle = this.$t('pages.deptRiskAnalysis')
      this.echartStatus = 'dept'
      this.$refs.echartDlg.show(this.echartStatus)
    },
    /**
       * 终端操作员小图标点击
       * */
    handleBigTerminalUser() {
      if (this.tempQuery.countByObject === 2) {
        this.echartDlgTitle = '终端风险统计'
        this.echartStatus = 'terminal'
      } else {
        this.echartDlgTitle = '操作员风险统计'
        this.echartStatus = 'user'
      }
      this.$refs.echartDlg.show(this.echartStatus, this.echartDlgTitle)
    },
    /**
       * 终端（操作员）分析点击
       * 显示终端（操作员）分析，面包屑中添加终端（操作员）分析
       * */
    handleTerminalUserAnalysis() {
      this.inceptionDiv = false
      this.userDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
        this.showFilePath.push({ id: 1, label: '操作员风险分析' })
      } else {
        // 终端
        this.dialogStatus = 'terminal'
        this.showFilePath.push({ id: 1, label: '终端风险分析' })
      }
    },
    /**
       * 部门分析点击
       * 显示部门分析，面包屑中添加部门分析
       * */
    handleDeptAnalysis() {
      this.inceptionDiv = false
      this.deptDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 2, label: '部门风险分析' })
    },
    /**
       * 数量趋势点击
       * 显示数量趋势，面包屑中添加数量趋势
       * */
    handleViolateTrendAnalysis() {
      this.inceptionDiv = false
      this.showFilePath.push({ id: 3, label: '泄露方式风险分析' })
    },
    /**
       * 泄露方式风险分析点击
       * 显示泄露方式风险分析，面包屑中添加泄露方式风险分析
       * */
    handleDivulgeAnalysis() {
      this.inceptionDiv = false
      this.divulgeDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 4, label: '泄露方式风险分析' })
    },
    /**
       * 面包屑点击方法（控制页面内容显示隐藏）
       * @param index
       * @param filePath
       * @param breadcrumb
       */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        // 初始页面
        this.inceptionDiv = true
        // 泄露方式页面
        this.divulgeDiv = false
        // 操作员（终端）页面
        this.userDiv = false
        // 部门页面
        this.deptDiv = false
        // 趋势页面
        this.showFilePath = [{ id: 0, label: '风险分析' }]
      }
      if (index === 1) {
        this.inceptionDiv = false
        this.divulgeDiv = false
        this.userDiv = true
        this.deptDiv = false
      }
      if (index === 2) {
        this.inceptionDiv = false
        this.divulgeDiv = false
        this.userDiv = false
        this.deptDiv = true
      }
      if (index === 3) {
        this.inceptionDiv = false
        this.divulgeDiv = false
        this.userDiv = false
        this.deptDiv = false
      }
    },
    /**
       * 打印
       * */
    handlePrint() {

    },
    /**
       * 导出
       * */
    handleExport() {

    }
  }
}
</script>
