<template>
  <el-popover placement="bottom" width="350" trigger="click">
    <Form ref="searchLeftForm" label-position="right" :model="query" label-width="80px">
      <FormItem label="风险级别">
        <el-select v-model="query.keyword1" clearable :placeholder="$t('pages.all')" style="width: 244px">
          <el-option v-for="(item, index) in selectMap" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </FormItem>
      <FormItem v-show="!isShowDivulge" :label="inputLabel">
        <el-input v-model="query.label" v-trim clearable/>
      </FormItem>
      <!-- <FormItem v-show="isShowDivulge" label="泄露方式">
        <el-select v-model="query.divulge" clearable :placeholder="$t('pages.all')" style="width: 244px">
          <el-option v-for="(item, index) in divulgeMap" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </FormItem> -->
    </Form>
    <div style="text-align: right; margin-top: 10px">
      <!--重置、查询按钮-->
      <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
      <el-button type="primary" size="mini" @click="searchData">{{ $t('button.search') }}</el-button>
    </div>
    <el-button slot="reference" type="primary" icon="el-icon-search" class="search-icon-left" size="mini"></el-button>
  </el-popover>
</template>

<script>
export default {
  name: 'RoughQuery',
  props: {
    /**
     * 可能是部门名称、终端名称、操作员名称等
     */
    inputLabel: {
      type: String,
      default: ''
    },
    isShowDivulge: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      query: {
        keyword1: '',
        label: '',
        divulge: ''
      },
      selectMap: [
        { label: '轻微', id: 1 },
        { label: '一般', id: 2 },
        { label: '严重', id: 3 },
        { label: '非常严重', id: 4 }
      ],
      divulgeMap: [
        { label: '即时通讯发送文件', id: 1 },
        { label: '文件打印内容', id: 2 },
        { label: 'USB复制文件', id: 3 },
        { label: 'ADB外发文件', id: 4 },
        { label: '蓝牙外发文件', id: 5 }
      ]
    }
  },
  methods: {
    resetQuery() {
      this.query.keyword1 = ''
      this.query.label = ''
      this.query.divulge = ''
    },
    async searchData() {
      await this.$parent.$refs['roughList'].execRowDataApi({ page: 1 })
      const tableData = this.$parent.$refs['roughList'].getDatas()
      if (tableData[0]) {
        this.$parent.handleRowClick(tableData[0])
      } else {
        this.$parent.resetTemp()
        if (this.ruleTypeAnalysis) {
          this.$parent.detailQuery.condition.rule_type = -1
        } else {
          this.$parent.detailQuery.objectNames = []
          this.$parent.detailQuery.objectNames.push(-1)
        }
        if (this.$parent.$refs['detailList']) {
          this.$parent.$refs['detailList'].clearRowData()
          this.$parent.$refs['detailList'].clearPageData()
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .search-icon-left {
    position: absolute;
    right: -24px;
    top: 0;
    cursor: pointer;
    padding: 4px;
  }
</style>
