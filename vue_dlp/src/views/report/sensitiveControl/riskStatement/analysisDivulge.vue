<template>
  <!--规则类型分析-->
  <div class="data-details sensitiveness-report">
    <div class="data-analysis-table table-parent-size">
      <div class="table-container table-left-size">
        <!--左侧列表查询条件-->
        <rough-query v-show="false" ref="roughQuery" :is-show-divulge="true" :input-label="this.$t('pages.deptName')"/>
        <grid-table
          ref="roughList"
          :col-model="colModel"
          :row-data-api="getData"
          :multi-select="false"
          :autoload="false"
          @row-click="handleRowClick"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--规则类型分析上方几个小方块-->
      <div class="data-panel">
        <div class="data-item data-item-details" >
          <div class="icon">
            <svg-icon icon-class="algorithm"></svg-icon>
          </div>
          <div class="content">
            <label :title="temp.item.ruleTypeName">{{ temp.item.ruleTypeName }}</label>
          </div>
        </div>
        <div class="data-item data-item-details" >
          <div class="icon">
            <svg-icon icon-class="ruleGroup"></svg-icon>
          </div>
          <div class="content">
            <label :title="this.$t('table.riskCount')"> {{ $t('table.riskCount') }} </label>
            <span :title="temp.item.violateCount">{{ temp.item.violateCount }}</span>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 165px); padding: 0 9px;position: relative">
        <!--右侧列表查询条件-->
        <detail-query v-show="false" ref="detailQuery" :is-show-grid="isShowGrid"></detail-query>
        <el-tabs v-model="activeTab" @tab-click="tabClick">
          <el-tab-pane v-if="drillDown" :label="this.$t('pages.violationRecord')" name="detail" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                ref="detailList"
                :show="isShowGrid"
                :col-model="colModelDetail"
                :row-data-api="getDetailData"
                :autoload="false"
                :multi-select="false"
                row-key="getRowKey"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane label="风险级别统计" name="statisticsRuleName">
            <pie-chart
              v-if="!isShowGrid"
              :chart-data="temp.divulgePieChartData"
              :chart-option="temp.divulgePieChartOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from '@/components/ECharts/PieChart'
import moment from 'moment'
import { getDetailPage, checkDetailAble } from '@/api/report/baseReport/issueReport/issue'
import RoughQuery from '@/views/report/sensitiveControl/riskStatement/roughQuery'
import DetailQuery from '@/views/report/sensitiveControl/riskStatement/detailQuery'
import { deepClone } from '@/utils'
import { getAllDef, getLossTypeAggDetail, getLossTypeDetailChartData } from './riskReportApi'

export default {
  name: 'AnalysisTerminalUser',
  components: { DetailQuery, RoughQuery, PieChart },
  props: {
    dialogStatus: { // 状态，区分终端还是操作员
      type: String,
      default() {
        return ''
      }
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      levelMap: {
        1: this.$t('pages.severityOptions1'),
        2: this.$t('pages.severityOptions2'),
        3: this.$t('pages.severityOptions3'),
        4: this.$t('pages.severityOptions4')
      },
      activeTab: '',
      isShowGrid: this.drillDown,
      colModel: [
        { prop: 'name', label: 'lossType' },
        { prop: 'eventCount', label: 'riskCount' },
        { prop: 'risks', label: 'riskType', width: '110' },
        { prop: 'riskLevels', label: 'riskLevel', width: '110', formatter: (row) => row.riskLevels.split(',').map(v => this.levelMap[v]).join(',') }
      ],
      colModelDetail: [
        { prop: 'risk_level', label: 'riskLevel', width: '110', formatter: row => this.levelMap[row.risk_level] },
        { prop: 'risk_def_id', label: 'riskType', width: '110', formatter: (row) => this.defMap[row.risk_def_id] },
        { prop: 'terminal_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'term_group_name', label: 'terminalGroup', hidden: () => this.colHidden(['user']) },
        { prop: 'user_name', label: 'user', hidden: () => this.colHidden(['terminal']) },
        { prop: 'user_group_name', label: 'userGroup', hidden: () => this.colHidden(['terminal']) },
        { prop: 'create_time', label: 'operateTime', width: '110' }
      ],
      query: {
        page: 1
      },
      detailQuery: {
        page: 1,
        detailTable: 'dwd_risk_event',
        countByObject: 0,
        condition: { loss_type: '' }
      },
      analysisQuery: {
        labelValue: ''
      },
      temp: {},
      defMap: {},
      defaultTemp: {
        item: {
          ruleTypeName: '',
          violateCount: 0
        },
        divulgePieChartData: [],
        divulgePieChartOption: {
          title: {
            text: '风险级别统计'
          },
          toolbox: {
            show: false
          },
          legend: {
            left: 'center'
          },
          series: [
            {
              radius: '30%'
            }
          ]
        }
      }
    }
  },
  computed: {
  },
  async created() {
    this.activeTab = this.drillDown ? 'detail' : 'statisticsRuleName'
    this.resetTemp()
    const resp = await getAllDef()
    for (const i of resp.data) {
      this.defMap[i.id] = i.riskName
    }
  },
  async mounted() {
    await this.$refs['roughList'].execRowDataApi()
    const tableData = this.$refs['roughList'].getDatas()
    // 如果左侧列表长度不为0，初始化页面时显示第一条数据的详情
    if (tableData[0]) {
      this.handleRowClick(tableData[0])
    }
  },
  methods: {
    // 查询违规数据
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$refs.roughQuery.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return getLossTypeAggDetail(searchQuery)
    },
    // 查询违规明细数据
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.$parent.tempQuery, this.$refs.detailQuery.query, this.detailQuery, option)
      return checkDetailAble(searchQuery).then(v => getDetailPage(searchQuery))
    },
    // 点击左侧表格
    handleRowClick(rowData) {
      // 设置生效的tab
      if (this.drillDown) {
        this.isShowGrid = true
        this.activeTab = 'detail'
      }
      // 查询分析数据
      const ruleType = rowData.name
      this.temp.item.ruleTypeName = ruleType
      this.analysisQuery.labelValue = rowData.lossType
      const searchQuery = Object.assign({}, this.analysisQuery, this.$parent.tempQuery)
      this.temp.item.violateCount = rowData.eventCount
      getLossTypeDetailChartData(searchQuery).then(resp => {
        this.temp.divulgePieChartData = Object.entries(resp.data)
          .map(v => {
            return {
              name: this.levelMap[parseInt(v[0])],
              value: v[1]
            }
          })
      })
      // 查询违规明细数据
      if (this.drillDown) {
        this.detailQuery.condition.loss_type = rowData.lossType
        this.$refs['detailList'].execRowDataApi({ page: 1 })
      }
    },
    /**
     * 页面初始化
     * */
    resetTemp() {
      this.temp = deepClone(this.defaultTemp)
    },
    /**
     * 根据终端还是操作员控制表格列的显示隐藏
     * */
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    /**
     * tab点击事件，如果不写，切换时图表只有小小一点点
     * @param pane
     * @param event
     */
    tabClick(pane, event) {
      this.isShowGrid = pane.name === 'detail'
    },
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
