import request from '@/utils/request'

export function getSummary(data) {
  return request({
    url: '/riskReport/summary',
    method: 'post',
    data
  })
}

export function getAllDef() {
  return request('/riskReport/defs')
}

export function getDeptAggDetail(data) {
  return request({
    url: '/riskReport/dept/aggDetail',
    method: 'post',
    data
  })
}

export function getDeptDetailChartData(data) {
  return request({
    url: '/riskReport/dept/chart',
    method: 'post',
    data
  })
}

export function getTermOrUserAggDetail(data) {
  return request({
    url: '/riskReport/termOrUser/aggDetail',
    method: 'post',
    data
  })
}

export function getTermOrUserDetailChartData(data) {
  return request({
    url: '/riskReport/termOrUser/chart',
    method: 'post',
    data
  })
}

export function getLossTypeAggDetail(data) {
  return request({
    url: '/riskReport/lossType/aggDetail',
    method: 'post',
    data
  })
}

export function getLossTypeDetailChartData(data) {
  return request({
    url: '/riskReport/lossType/chart',
    method: 'post',
    data
  })
}
