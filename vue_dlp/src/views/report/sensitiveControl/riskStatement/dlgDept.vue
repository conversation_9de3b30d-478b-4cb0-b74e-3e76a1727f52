<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.dept')"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <grid-table
        ref="deptTable"
        :stripe="false"
        row-key="id"
        :col-model="colModel"
        :row-data-api="getData"
        :indent="0"
        :height="325"
        :multi-select="false"
        default-expand-all
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'DlgDept',
  props: {
    dataApi: {
      type: Function,
      default: function() {}
    }
  },
  data() {
    return {
      levelMap: {
        1: this.$t('pages.severityOptions1'),
        2: this.$t('pages.severityOptions2'),
        3: this.$t('pages.severityOptions3'),
        4: this.$t('pages.severityOptions4')
      },
      dialogVisible: false,
      query: {
        page: 1,
        groupType: 1
      },
      colModel: [
        { prop: 'termGroupName', label: 'dept', hidden: () => this.$parent.tempQuery.countByObject === 1 },
        { prop: 'userGroupName', label: 'dept', hidden: () => this.$parent.tempQuery.countByObject === 2 },
        { prop: 'eventCount', label: 'riskCount' },
        { prop: 'riskLevels', label: 'riskLevel', width: '110', formatter: (row) => row.riskLevels.split(',').map(v => this.levelMap[v]).join(',') },
        { prop: 'risks', label: 'riskType', width: '110' }
      ]
    }
  },
  computed: { },
  methods: {
    /**
     * 获取数据信息
     * */
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return this.dataApi(searchQuery)
    },
    /**
     * 显示弹框
     */
    show() {
      this.dialogVisible = true
      if (this.$refs['deptTable']) {
        this.$refs['deptTable'].execRowDataApi(this.query)
      }
    }
  }
}
</script>

