<!--
  /**
  * echart散点图(存在一二三四象限)封装，可根据实际需求修改配置项
  * 调用示例：
      <scatter-diagram-chart
        :chart-source="chartSource"  // chart数据，散点图各个点
        :chart-data="chartData"       // chart数据，散点图四象限坐标轴
        :chart-option="chartOption"   // chart配置项，可通过外部传入修改相对应的属性
      />

      // 散点图每一个点的位置
      source: [
        [2, 3],
        [-3, 2],
        [-2, -3],
        [4, -3]
      ]
    // 散点图四象限坐标轴
      chartData =  [
        { value: 0, label: '高危', color: '#e06343' },
        { value: 1, label: '严重', color: '#b55dba' },
        { value: 2, label: '中等', color: '#37A2DA' },
        { value: 3, label: '一般', color: '#37a354' }
      ]
  */
-->
<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce, deepMerge } from '@/utils'
import { toRiskLevel, randomize } from './riskUtils'

export default {
  name: 'ScatterDiagramChart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '450px'
    },
    yAvg: {
      type: Number,
      default: 0
    },
    median: {
      type: Number,
      default: 0
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    // chartData数据(四象限)
    chartData: {
      type: Array,
      default() {
        return []
      }
    },
    // chartSource数据(散点图每一个点的坐标)
    chartSource: {
      type: Array,
      default() {
        return []
      }
    },
    click: {
      type: Function,
      default(para) {
        // console.log(para)
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val, this.chartSource)
      }
    },
    chartSource: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData, this.chartSource)
      }
    },
    chartOption: {
      deep: true,
      handler(val) {
        this.setOptions(this.chartData, this.chartSource)
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    sidebarResizeHandler(e) {
      // 后续优化，根据容器的变化实现图表resize
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions(chartData, chartSource) {
      const yAvg = this.yAvg
      const defaultOpt = {
        title: {
          text: '',
          subtext: '',
          left: 'center'
        },
        tooltip: {
          show: true,
          formatter: params => {
            let user = ''
            // console.log(params)
            if (params.componentType === 'markLine') {
              if (params.data.yAxis) {
                return `频次基线: ${params.data.yAxis}`
              } else {
                return ''
              }
            }
            if (params.componentType === 'series') {
              if (params.data[2]) {
                user = `${params.data[2]} 次数: ${params.data[1]}`
              } else {
                user = `${params.data[4]} 次数: ${params.data[1]}`
              }
              return user
            }
          }
        },
        dataset: [
          {
            source: chartSource.map((v, i) => {
              const cv = [...v]
              cv[0] = randomize(cv[0], i)
              return cv
            })
          },
          {
            transform: {
              config: {
                clusterCount: 6,
                outputType: 'single',
                outputClusterIndexDimension: 2
              }
            }
          }
        ],
        xAxis: {
          splitLine: {// 去除网格线
            show: false
          },
          axisTick: {
            show: false
          },
          splitNumber: 8,
          type: 'value',
          min: 0,
          max: 8,
          // data: ['-', this.$t('pages.severityOptions2'), '-', this.$t('pages.secondary'), '-', this.$t('pages.severityOptions3'), '-', this.$t('pages.severityOptions4'), '-'],
          // data: [0, 1, 2, 3, 4],
          splitArea: { show: false }, // 去除网格区域
          axisLabel: {
            formatter: function(v, index) {
              // console.log(v, index)
              if (v == 1) {
                return '轻微'
              }
              if (v == 3) {
                return '一般'
              }
              if (v == 5) {
                return '严重'
              }
              if (v == 7) {
                return '非常严重'
              }
              return ''
            }
          }
        },
        yAxis: {
          splitLine: {// 去除网格线
            show: false
          },
          name: '频次',
          splitArea: { show: false }// 去除网格区域
        },
        series: {
          type: 'scatter',
          symbolSize: 12, // 散点图点的大小
          itemStyle: {
            normal: {
              shadowBlur: 10,
              shadowColor: 'rgba(120, 36, 50, 0.5)',
              shadowOffsetY: 5,
              color: function(e) {
                const level = toRiskLevel(e.data[0])
                const count = e.data[1]
                if (level >= 2 && count >= yAvg) {
                  // 高频高风险
                  return '#FFBD8A'
                }
                if (level >= 2) {
                  // 低频高风险
                  return '#54B1E6'
                }
                if (count >= yAvg) {
                  // 高频低风险
                  return '#B6A2DE'
                }
                // 低频低风险
                return '#2FCDCD'
              }
            }
          },
          markLine: {
            symbol: 'none',
            data: [
              {
                yAxis: this.yAvg,
                label: {
                  formatter: '频次基线' // 显示标签为数据点的名称和值
                }
              },
              {
                xAxis: 4,
                label: {
                  formatter: ''
                }
              }
            ]
          },
          markArea: {
            silent: true,
            data: [
              [{
                name: this.$t('pages.lowFrequencyHighRisk'),
                itemStyle: {
                  color: 'transparent'
                },
                label: {
                  show: true,
                  position: 'insideTopLeft',
                  fontStyle: 'normal',
                  color: '#2CBBBD',
                  fontSize: 14
                },
                coord: [4, this.yAvg]
              }, {
                coord: [Number.MAX_VALUE, 0]
              }],
              [{
                name: this.$t('pages.lowFrequencyLowRisk'),
                itemStyle: {
                  color: 'transparent'
                },
                label: {
                  show: true,
                  position: 'insideTopRight',
                  fontStyle: 'normal',
                  color: '#2CBBBD',
                  fontSize: 14
                },
                coord: [0, 0]
              }, {
                coord: [4, this.yAvg]
              }],
              [{
                name: this.$t('pages.highFrequencyHighRisk'),
                itemStyle: {
                  color: 'transparent'
                },
                label: {
                  show: true,
                  position: 'insideBottomLeft',
                  fontStyle: 'normal',
                  color: '#2CBBBD',
                  fontSize: 14
                },
                coord: [4, this.yAvg]
              }, {
                coord: [Number.MAX_VALUE, Number.MAX_VALUE]
              }],
              [{
                name: this.$t('pages.highFrequencyLowRisk'),
                itemStyle: {
                  color: 'transparent'
                },
                label: {
                  show: true,
                  position: 'insideBottomRight',
                  fontStyle: 'normal',
                  color: '#2CBBBD',
                  fontSize: 14
                },
                coord: [0, Number.MAX_VALUE]
              }, {
                coord: [4, this.yAvg]
              }]
            ]
          }
        }
      }
      this.chart.setOption(deepMerge(defaultOpt, this.chartOption))
    },
    initChart() {
      const _this = this
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.chart.on('click', function(params) {
        _this.click(params)
      });
      this.setOptions(this.chartData, this.chartSource)
    }
  }
}
</script>

<style lang="scss">
.tooltip-dot {
  display: inline-block;
  margin-right: 5px;
  border-radius: 10px;
  width: 9px;
  height: 9px;
}

.dataView {
  width: 100%;
  height: 100%;
  border-top: 1px solid #000;
  padding: 10px;
  overflow: auto;
  color: #666;
}

.dataView table {
  width: 100%;
  border: 1px solid #666;
  border-collapse: collapse;
  font-size: 14px;
}

.dataView table th,
.dataView table td {
  padding: 2px 10px;
  border: 1px solid #aaa;
}</style>
