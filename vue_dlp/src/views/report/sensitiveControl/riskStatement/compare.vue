<template>
  <comparative-trend
    :api="'getRiskEventCompareData'"
    :api-dept="'getRiskEventCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';

export default {
  name: 'RiskStatementCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '风险',
      // 统计项设置 复选框传值（如果是文件大小或时长，需添加字段flag: 'size'或flag: 'time'）
      statisticalList: [
        { prop: 'riskLevel1Sum', label: 'slightSumAll' },
        { prop: 'riskLevel2Sum', label: 'generalSumAll' },
        { prop: 'riskLevel3Sum', label: 'seriousSumAll' },
        { prop: 'riskLevel4Sum', label: 'esSeriousSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['riskLevel1Sum', 'riskLevel2Sum', 'riskLevel3Sum', 'riskLevel4Sum']
    }
  },
  computed: {

  },
  watch: {
  },
  created() {
    this.filterData()
  },
  methods: {
    /**
     * 权限过滤
     */
    filterData() {
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
