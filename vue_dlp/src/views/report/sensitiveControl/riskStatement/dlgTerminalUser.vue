<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <grid-table
        ref="terminalUserTable"
        :stripe="false"
        row-key="id"
        :col-model="colModel"
        :row-data-api="getData"
        :indent="0"
        :height="325"
        :multi-select="false"
        default-expand-all
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

const levelMap = {
  1: '轻微',
  2: '一般',
  3: '严重',
  4: '非常严重'
}

export default {
  name: 'DlgTerminalUser',
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    },
    dataApi: {
      type: Function,
      default: function() {}
    }
  },
  data() {
    return {
      dialogVisible: false,
      query: {
        page: 1,
        groupType: 0
      },
      colModel: [
        { prop: 'terminalName', label: 'terminalName', hidden: () => this.$parent.tempQuery.countByObject === 1 },
        { prop: 'userName', label: 'user', hidden: () => this.$parent.tempQuery.countByObject === 2 },
        { prop: 'eventCount', label: 'riskCount' },
        { prop: 'riskLevels', label: 'riskLevel', width: '110', formatter: (row) => row.riskLevels.split(',').map(v => levelMap[v]).join(',') },
        { prop: 'risks', label: 'riskType', width: '110' }
      ]
    }
  },
  computed: {},
  methods: {
    /**
     * 获取数据信息
     * */
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return this.dataApi(searchQuery)
    },
    /**
     * 显示弹框
     */
    show() {
      this.dialogVisible = true
      if (this.$refs['terminalUserTable']) {
        this.$refs['terminalUserTable'].execRowDataApi(this.query)
      }
    }
  }
}
</script>

