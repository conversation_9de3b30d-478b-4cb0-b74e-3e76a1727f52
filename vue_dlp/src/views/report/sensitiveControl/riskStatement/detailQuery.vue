<template>
  <el-popover placement="bottom" width="350" trigger="click">
    <Form ref="searchRightForm" label-position="right" :model="query" label-width="80px">
      <FormItem label="风险级别">
        <el-select v-model="query.riskLevel" clearable :placeholder="$t('pages.all')" style="width: 244px">
          <el-option v-for="(item, index) in selectMap" :key="index" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </FormItem>
      <FormItem label="风险类别">
        <el-input v-model="query.riskCategory" v-trim clearable/>
      </FormItem>
    </Form>
    <div style="text-align: right; margin-top: 10px">
      <!--重置、查询按钮-->
      <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
      <el-button type="primary" size="mini" @click="searchData()">{{ $t('button.search') }}</el-button>
    </div>
    <el-button v-show="isShowGrid" slot="reference" type="primary" icon="el-icon-search" class="search-icon-right" size="mini"></el-button>
  </el-popover>
</template>

<script>
export default {
  name: 'DetailQuery',
  props: {
    isShowGrid: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      query: {
        riskLevel: '',
        riskCategory: ''
      },
      selectMap: [
        { label: '高危', id: 1 },
        { label: '严重', id: 2 },
        { label: '中等', id: 3 },
        { label: '一般', id: 4 },
        { label: '轻微', id: 5 }
      ]
    }
  },
  methods: {
    resetQuery() {
      this.query.riskLevel = ''
      this.query.riskCategory = ''
    },
    searchData() {
      this.$parent.$refs['detailList'].execRowDataApi({ page: 1 })
    }
  }
}
</script>

<style lang='scss' scoped>
  .search-icon-right {
    position: absolute;
    right: 15px;
    top: 5px;
    cursor: pointer;
    padding: 4px;
    z-index: 1;
  }
</style>
