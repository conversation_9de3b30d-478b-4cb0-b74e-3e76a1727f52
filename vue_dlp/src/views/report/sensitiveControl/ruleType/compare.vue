<template>
  <comparative-trend
    :api="'getGeneralViolateRuleTypeCompareData'"
    :api-dept="'getGeneralViolateRuleTypeCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'GeneralRuleTypeCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '违规规则类型',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'fileAttrSumAll', label: 'fileAttrSumAll' },
        { prop: 'keywordSumAll', label: 'keywordSumAll' },
        { prop: 'dataIdentifierSumAll', label: 'dataIdentifierSumAll' },
        { prop: 'dbFingerprintSumAll', label: 'dbFingerprintSumAll' },
        { prop: 'docClassSumAll', label: 'docClassSumAll' },
        { prop: 'fileFingerprintSumAll', label: 'fileFingerprintSumAll' },
        { prop: 'sourceCodeSumAll', label: 'sourceCodeSumAll' },
        { prop: 'contentTimeoutSumAll', label: 'contentTimeoutSumAll' },
        { prop: 'filePwdSensitiveSumAll', label: 'filePwdSensitiveSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['fileAttrSumAll', 'keywordSumAll', 'dataIdentifierSumAll', 'dbFingerprintSumAll',
        'docClassSumAll', 'fileFingerprintSumAll', 'sourceCodeSumAll', 'contentTimeoutSumAll', 'filePwdSensitiveSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
