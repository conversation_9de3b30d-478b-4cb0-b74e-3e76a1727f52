<template>
  <!--数量趋势-->
  <div style="height: 100%">
    <!--数量趋势--上方折线图-->
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="24">
        <line-chart
          :chart-data="temp.ruleTypeTrendLineChartData"
          :chart-option="temp.ruleTypeTrendLineChartOption"
          style="height: 300px"
        />
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12">
        <pie-chart
          :chart-data="temp.ruleTypePieChartData"
          :chart-option="temp.ruleTypePieChartOption"
          style="height: 300px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
        <bar-chart
          :chart-data="temp.ruleTypeBarChartData"
          :chart-option="temp.ruleTypeBarChartOption"
          :x-axis-name="''"
          style="height: 300px"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import PieChart from '@/components/ECharts/PieChart'
import BarChart from '@/components/ECharts/BarChart'
import { getGeneralRuleTypeTrendAnalysisData } from '@/api/report/baseReport/sensitiveControl/generalRuleType'
export default {
  name: 'AnalysisTendency',
  components: { LineChart, PieChart, BarChart },
  data() {
    return {
      temp: {},
      defaultTemp: {
        // 规则类型折线图
        ruleTypeTrendLineChartData: [],
        ruleTypeTrendLineChartOption: {
          title: {
            text: this.$t('pages.violationCountTrend')
          },
          legend: {
            type: 'plain',
            top: 40,
            data: []
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          dataZoom: [
            {
              textStyle: {
                color: '#027AB6'
              },
              height: 20, // 时间滚动条的高度
              type: 'slider', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0,  // 默认开始位置（百分比）
              end: 100  // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          series: []
        },
        // 规则类型饼图
        ruleTypePieChartData: [],
        ruleTypePieChartOption: {
          title: {
            text: this.$t('pages.ruleTypeStatistical')
          },
          toolbox: {
            show: false
          },
          series: [
            {
              radius: '30%'
            }
          ]
        },
        // 规则类型柱状统计图
        ruleTypeBarChartData: [],
        ruleTypeBarChartOption: {
          title: {
            text: this.$t('pages.ruleTypeStatistical')
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          yAxis: {
            type: 'category',
            data: []
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      }
    }
  },
  created() {
    this.resetTemp()
    this.getData()
  },
  methods: {
    resetTemp() {
      this.temp = { ...this.defaultTemp }
    },
    /**
     * 获取数据信息
     * */
    getData() {
      return getGeneralRuleTypeTrendAnalysisData(this.$parent.tempQuery).then(resp => {
        if (resp.data) {
          this.temp.ruleTypeTrendLineChartOption.legend.data = resp.data.chartDataObjMap.ruleTypeTrendLineCharData.legendData
          this.temp.ruleTypeTrendLineChartOption.xAxis.data = resp.data.chartDataObjMap.ruleTypeTrendLineCharData.xaxisData
          this.temp.ruleTypeTrendLineChartOption.series = resp.data.chartDataObjMap.ruleTypeTrendLineCharData.seriesData

          this.temp.ruleTypePieChartData = resp.data.chartDataObjMap.ruleTypePieChartData.chartData

          this.temp.ruleTypeBarChartData = resp.data.chartDataObjMap.ruleTypeBarChartData.chartData
          this.temp.ruleTypeBarChartOption.yAxis.data = resp.data.chartDataObjMap.ruleTypeBarChartData.xaxisData
          this.temp.ruleTypeBarChartOption.series[0].data = resp.data.chartDataObjMap.ruleTypeBarChartData.seriesData
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
  .data-analysis{
    flex: 1;
    height: 100%;
  }
  .data-details{
    width: 100%;
    height: 50%;
    padding:0 20px 20px 20px;
    display: flex;
  }
  .data-analysis-details{
    flex: 1;
    border: 1px solid #3b749c;
  }
</style>
