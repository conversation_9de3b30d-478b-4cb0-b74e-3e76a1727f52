<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query">
        <i class="el-icon-s-home" style="position: absolute; left: 24px; top: 16px;"></i>
        <!--面包屑-->
        <div style="width: 510px; float: left;">
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-tooltip effect="dark" placement="bottom-start" style="margin-left: 10px">
              <span slot="content">报表显示的数据为常规检测的详细数据</span>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <!--上方搜索条件-->
        <div style="float: right">
          <!--查询条件组件-->
          <query ref="queryData"/>
          <!--图表解读-->
          <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
          <!-- <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
          <el-button type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>-->
        </div>
      </div>

      <!-- 页面无数据 -->
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>

      <!--分析初始页面-->
      <div v-if="inceptionDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
            <div class="icon">
              <svg-icon :icon-class="item.icon"></svg-icon>
            </div>
            <div class="content">
              <label :title="item.label">{{ item.label }}</label>
              <el-button type="text" :class="{'underline': item.clickable }" @click="item.func">{{ temp.itemValue[item.key] }}</el-button>
            </div>
          </div>
        </div>
        <!--中间规则类型分析柱状图-->
        <div class="chart-panel">
          <div class="chart-item flex-2">
            <div class="mini-title"><span @click="handleRuleTypeAnalysis">{{ this.$t('pages.ruleTypeAnalysis') }}>></span></div>
            <bar-chart
              ref="ruleTypeBarChart"
              :chart-data="temp.ruleTypeBarChartData"
              :chart-option="temp.ruleTypeBarChartOption"
              :x-axis-name="''"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.ruleType.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title">
                解读：<span style="color: #909399;">通过不同严重程度的违规触发分布，评估当前安全风险等级，为制定分级响应策略提供数据支撑。</span>
              </div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.ruleType + '</div>'"></div>
            </div>
          </div>
        </div>
        <!--下方对部门，终端（操作员），数量趋势的分别展示部分-->
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleDeptAnalysis">{{ $t('pages.deptRuleTypeAnalysis') }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigDept"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.deptBarChartData"
              :chart-option="temp.deptBarChartOption"
              :x-axis-name="this.$t('pages.dept')"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.deptDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.deptDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleTerminalUserAnalysis">{{ terminalUserAnalysisTitle }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigTerminalUser"/>
              </el-tooltip>
            </div>
            <pie-chart
              :chart-data="temp.terminalUserPieChartData"
              :chart-option="temp.terminalUserPieChartOption"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.termOrUserDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.termOrUserDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title"><span @click="handleViolateTrendAnalysis">{{ $t('pages.violationCountTrendAnalysis') }}>></span></div>
            <line-chart
              :chart-data="temp.trendLineChartData"
              :chart-option="temp.trendLineChartOption"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.violationNum.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.violationNum + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
      <!--规则分析-->
      <div v-if="ruleDiv" class="detailsDiv">
        <analysis-rule
          :dialog-status="dialogStatus"
          :detail-table="detailTable"
          :rule-type-map="ruleTypeMap"
          :drill-down="drillDown"
          :specific-detail-col="specificDetailCol"
          :input-list="inputList"
        />
      </div>
      <!--部门分析-->
      <div v-if="deptDiv" class="detailsDiv">
        <analysis-dept
          :dialog-status="dialogStatus"
          :chart-title="this.$t('pages.ruleTypeStatistical')"
          :detail-table="detailTable"
          :dept-api="deptApi"
          :dept-analysis-api="deptAnalysisApi"
          :not-null-fields="notNullFields"
          :select-label="this.$t('pages.ruleType')"
          :select-map="ruleTypeMap"
          :drill-down="drillDown"
          :specific-col="specificCol"
          :specific-detail-col="specificDetailCol"
          :select-list="selectList"
          :input-list="inputList"
        />
      </div>
      <!--终端、操作员分析-->
      <div v-if="userDiv" class="detailsDiv">
        <analysis-terminal-user
          :dialog-status="dialogStatus"
          :chart-title="this.$t('pages.ruleTypeStatistical')"
          :detail-table="detailTable"
          :terminal-user-api="terminalUserApi"
          :terminal-user-analysis-api="terminalUserAnalysisApi"
          :not-null-fields="notNullFields"
          :select-label="this.$t('pages.ruleType')"
          :select-map="ruleTypeMap"
          :drill-down="drillDown"
          :specific-col="specificCol"
          :specific-detail-col="specificDetailCol"
          :select-list="selectList"
          :input-list="inputList"
        />
      </div>
      <!--违规数量趋势分析-->
      <div v-if="tendencyDiv" class="detailsDiv">
        <analysis-tendency/>
      </div>
    </div>
    <!--小方块（违规总数）点击弹框-->
    <violate-detail-dlg ref="totalViolationDlg" :dlg-title="this.$t('pages.violationTotal')" :detail-table="detailTable" :not-null-fields="notNullFields" :specific-col="specificDetailCol"/>
    <!--小方块（部门）点击弹框-->
    <dept-dlg ref="deptDlg" :data-api="deptApi" :specific-col="specificCol"/>
    <!--小方块（终端/操作员）点击击弹框-->
    <terminal-user-dlg ref="terminalUserDlg" :title="terminalUserDlgTitle" :data-api="terminalUserApi" :specific-col="specificCol"/>
    <!--部门、终端、操作员前五名小图标点击弹框-->
    <echart-dlg ref="echartDlg" :title="echartDlgTitle" :dept-chart-api="deptChartApi" :terminal-user-chart-api="terminalUserChartApi"/>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
import Query from '@/views/report/sensitiveControl/common/query'
import DeptDlg from '@/views/report/sensitiveControl/common/dlgDept'
import EchartDlg from '@/views/report/sensitiveControl/common/echartDlg'
import TerminalUserDlg from '@/views/report/sensitiveControl/common/dlgTerminalUser'
import ViolateDetailDlg from '@/views/report/sensitiveControl/common/dlgViolateDetail'
import analysisDept from '@/views/report/sensitiveControl/common/analysisDept'
import analysisTerminalUser from '@/views/report/sensitiveControl/common/analysisTerminalUser'
import analysisTendency from '@/views/report/sensitiveControl/ruleType/analysisTendency'
import analysisRule from '@/views/report/sensitiveControl/ruleType/analysisRule'
import { getGeneralRuleTypeHomepageData } from '@/api/report/baseReport/sensitiveControl/generalRuleType'
import { deepClone } from '@/utils';
import { listGeneralRuleTypeDeptData, listGeneralRuleTypeTerminalUserData, getGeneralRuleTypeDeptChartData, getGeneralRuleTypeTerminalUserChartData, getGeneralRuleTypeDeptAnalysisData, getGeneralRuleTypeTerminalUserAnalysisData } from '@/api/report/baseReport/sensitiveControl/generalRuleType'

export default {
  name: 'RuleType',
  components: { Query, BarChart, LineChart, PieChart, DeptDlg, EchartDlg, TerminalUserDlg, ViolateDetailDlg, analysisDept, analysisTerminalUser, analysisTendency, analysisRule },
  props: {
  },
  data() {
    return {
      // 搜查查询
      query: {
        // 是否展示图表解读
        isExplain: true,
        dimBaseType: 1,
        dimValue: '',
        countByObject: 2
      },
      tempQuery: {},
      terminalUserAnalysisTitle: this.$t('pages.terminalRuleTypeAnalysis'),
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: this.$t('pages.ruleTypeAnalysis') }
      ],
      // 暂无数据
      noData: true,
      // 初始页面显示隐藏
      inceptionDiv: false,
      // 规则分析显示隐藏
      ruleDiv: false,
      // 操作员分析显示隐藏
      userDiv: false,
      // 部门分析显示隐藏
      deptDiv: false,
      // 趋势分析显示隐藏
      tendencyDiv: false,
      // 违规终端/操作员弹窗名称
      terminalUserDlgTitle: '',
      // 放大柱状图弹框名称
      echartDlgTitle: '',
      echartStatus: '',
      // 弹框状态
      dialogStatus: '',
      // 上方小方块
      itemOption: [
        { label: this.$t('pages.violationTotal'), key: 'all', icon: 'total', func: this.totalViolationFun },
        { label: this.$t('pages.dept'), key: 'dept', icon: 'tree', func: this.deptFun, clickable: true },
        { label: this.$t('pages.violationTerminal'), key: 'terminalUser', icon: 'terminal', func: this.terminalUserFun, clickable: true }
      ],
      // 图表解读
      commonInterpretation: `<span>图表解读：</span>`,
      temp: {},
      defaultTemp: {
        // 图表解读
        chartInterpretationContent: {
          // 规则类型
          ruleType: '',
          // 部门泄露方式统计
          deptDistribute: '',
          // 终端/操作员泄露方式统计
          termOrUserDistribute: '',
          // 违规数量分析
          violationNum: ''
        },
        itemValue: {
          all: '0',
          dept: '0',
          terminalUser: '0'
        },
        // 规则类型柱状统计图
        ruleTypeBarChartData: [],
        ruleTypeBarChartOption: {
          title: {
            text: this.$t('pages.ruleTypeStatistical')
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          yAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 部门规则类型数量柱状图数据
        deptBarChartData: [],
        deptBarChartOption: {},
        // 终端/操作员规则类型数量图
        terminalUserPieChartData: [],
        terminalUserPieChartOption: {},
        // 折线图数据 违规数量趋势分析
        trendLineChartData: [],
        trendLineChartOption: {
          title: {
            text: this.$t('pages.violationCountTrend')
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        }
      },
      ruleTypeMap: {
        1: this.$t('pages.fileRule'),
        2: this.$t('pages.keywordRule'),
        3: this.$t('pages.regularRule'),
        4: this.$t('pages.edmRule'),
        5: this.$t('pages.vmlRule'),
        6: this.$t('pages.fileFingerRule'),
        12: this.$t('pages.sourceCodeRule'),
        224: this.$t('pages.contentDetectionTimeoutRule'),
        225: this.$t('pages.fileWithPasswordSensitiveDetectionRule')
      },
      detailTable: 'dwd_general_violate_log',
      notNullFields: ['rule_type'],
      specificDetailCol: [
        { prop: 'rule_type', label: 'ruleType', formatter: (row, data) => { return this.ruleTypeMap[data] } },
        { prop: 'rule_name', label: 'ruleName' },
        { prop: 'file_name', label: 'sensitiveFile', formatter: this.fileFormatter },
        { prop: 'stg_def_name', label: 'contentStg' }
      ],
      specificCol: [
        { prop: 'violate_count', label: 'violationCount', sort: true },
        { prop: 'ruleTypeStr', label: 'ruleType', formatter: (row, data) => { return data.replace(/^,|,$/g, '').split(',').map(item => this.ruleTypeMap[item]).join(',') } }
      ],
      // 明细数据输入框搜索条件
      inputList: [
        {
          // 标签名
          inputLabel: this.$t('table.ruleName'),
          // 绑定字段名
          inputKey: 'rule_name'
        },
        {
          inputLabel: this.$t('table.sensitiveFile'),
          inputKey: 'file_name'
        },
        {
          inputLabel: this.$t('table.contentStg'),
          inputKey: 'stg_def_name'
        }
      ],
      deptApi: listGeneralRuleTypeDeptData,
      terminalUserApi: listGeneralRuleTypeTerminalUserData,
      deptChartApi: getGeneralRuleTypeDeptChartData,
      terminalUserChartApi: getGeneralRuleTypeTerminalUserChartData,
      deptAnalysisApi: getGeneralRuleTypeDeptAnalysisData,
      terminalUserAnalysisApi: getGeneralRuleTypeTerminalUserAnalysisData,
      // 是否支持下钻
      drillDown: this.hasPermission('840')
    }
  },
  computed: {
    // 明细数据下拉框搜索条件
    selectList() {
      return [
        {
          // 标签名
          selectLabel: this.$t('pages.ruleType'),
          // 绑定字段名
          selectKey: 'rule_type',
          // 下拉选项
          selectMap: this.ruleTypeMap
        }
      ]
    }
  },
  watch: {
  },
  created() {
    this.$set(this.itemOption[0], 'clickable', this.drillDown)
    this.resetTemp()
  },
  methods: {
    fileFormatter(row) {
      return row.file_name + (row.child_file_path ? row.child_file_path : '')
    },
    resetTemp() {
      this.defaultTemp.deptBarChartOption = this.getDeptBarChartOption(5)
      this.defaultTemp.terminalUserPieChartOption = this.getTerminalUserPieChartOption(5)
      this.defaultTemp.terminalUserPieChartOption.title.text = this.query.countByObject === 1 ? this.$t('pages.userRuleTypeStatistical') : this.$t('pages.terminalRuleTypeStatistical')
      this.temp = deepClone(this.defaultTemp)
    },
    /**
     * 获取数据信息
     * */
    getData() {
      this.query = this.$refs['queryData'].getQuery()
      this.tempQuery = Object.assign({}, this.query)
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      return getGeneralRuleTypeHomepageData(this.query).then(resp => {
        if (resp.data) {
          this.inceptionDiv = true
          this.noData = false
          // 图表解读
          if (resp.data.itemValueMap.explanation) {
            this.temp.chartInterpretationContent = { ...this.temp.chartInterpretationContent, ...resp.data.itemValueMap.explanation }
          } else {
            this.temp.chartInterpretationContent = {
              // 规则类型
              ruleType: '',
              // 部门泄露方式统计
              deptDistribute: '',
              // 终端/操作员泄露方式统计
              termOrUserDistribute: '',
              // 违规数量分析
              violationNum: ''
            }
          }
          this.temp.itemValue.all = resp.data.itemValueMap.violateTotal
          this.temp.itemValue.dept = resp.data.itemValueMap.deptCount
          this.temp.itemValue.terminalUser = resp.data.itemValueMap.terminalUserCount

          this.temp.ruleTypeBarChartData = resp.data.chartDataObjMap.ruleTypeBarChartData.chartData
          this.temp.ruleTypeBarChartOption.yAxis.data = resp.data.chartDataObjMap.ruleTypeBarChartData.xaxisData
          this.temp.ruleTypeBarChartOption.series[0].data = resp.data.chartDataObjMap.ruleTypeBarChartData.seriesData
          this.temp.deptBarChartData = resp.data.chartDataObjMap.deptBarChartData.chartData
          this.temp.deptBarChartOption.xAxis.data = resp.data.chartDataObjMap.deptBarChartData.xaxisData
          this.temp.deptBarChartOption.series[0].data = resp.data.chartDataObjMap.deptBarChartData.seriesData

          this.temp.terminalUserPieChartData = resp.data.chartDataObjMap.terminalUserPieChartData.chartData

          this.temp.trendLineChartData = resp.data.chartDataObjMap.violateCountTrendLineCharData.chartData
          this.temp.trendLineChartOption.xAxis.data = resp.data.chartDataObjMap.violateCountTrendLineCharData.xaxisData
          this.temp.trendLineChartOption.series[0].data = resp.data.chartDataObjMap.violateCountTrendLineCharData.seriesData
        } else {
          this.resetTemp()
          this.inceptionDiv = false
          this.noData = true
        }
      }).then(() => {
        this.triggerResize()
      })
    },
    /**
     * 搜索按钮点击（暂时原型主要是对统计类型变化时页面上显示变化的一个处理）
     * */
    loadReport() {
      this.getData()
      if (this.query.countByObject === 1) {
        // 操作员
        Object.assign(this.itemOption[2], { label: this.$t('pages.violationUser'), icon: 'user' })
        this.terminalUserAnalysisTitle = this.$t('pages.userRuleTypeAnalysis')
        this.temp.terminalUserPieChartOption.title.text = this.$t('pages.userRuleTypeStatistical')
      } else {
        // 终端
        Object.assign(this.itemOption[2], { label: this.$t('pages.violationTerminal'), icon: 'terminal' })
        this.terminalUserAnalysisTitle = this.$t('pages.terminalRuleTypeAnalysis')
        this.temp.terminalUserPieChartOption.title.text = this.$t('pages.terminalRuleTypeStatistical')
      }
      // 初始页面（点击搜索的时候要回到初始页面）
      // this.inceptionDiv = true
      // 规则页面
      this.ruleDiv = false
      // 操作员（终端）页面
      this.userDiv = false
      // 部门页面
      this.deptDiv = false
      // 趋势页面
      this.tendencyDiv = false
      this.showFilePath = [{ id: 0, label: this.$t('pages.ruleTypeAnalysis') }]
    },
    getDeptBarChartOption(top) {
      return {
        title: {
          text: this.$t('pages.deptRuleTypeStatistical'),
          subtext: this.$t('pages.topN', { num: top })
        },
        toolbox: {
          show: false
        },
        grid: {
          top: 60,
          width: '85%',
          bottom: 20,
          left: '5%',
          containLabel: true
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          splitArea: {
            show: false
          }
        },
        xAxis: {
          type: 'category',
          data: []
        },
        series: [
          {
            data: [],
            type: 'bar'
          }
        ]
      }
    },
    getTerminalUserPieChartOption(top) {
      return {
        title: {
          text: '',
          subtext: this.$t('pages.topN', { num: top })
        },
        toolbox: {
          show: false
        },
        legend: {
          show: false,
          left: 'center'
        },
        series: [
          {
            radius: '40%',
            center: ['50%', '55%']
          }
        ]
      }
    },
    /**
     * 违规总数(点击)
     */
    totalViolationFun() {
      if (this.drillDown) {
        this.$refs.totalViolationDlg.show()
      }
    },
    /**
     * 部门（点击）
     */
    deptFun() {
      this.$refs.deptDlg.show()
    },
    /**
     * 终端或操作员（点击）
     */
    terminalUserFun() {
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.terminalUserDlgTitle = this.$t('pages.violationUser')
      } else {
        // 终端
        this.terminalUserDlgTitle = this.$t('pages.violationTerminal')
      }
      this.$refs.terminalUserDlg.show()
    },
    /**
     * 部门分析小图标点击
     * */
    handleBigDept() {
      this.echartDlgTitle = this.$t('pages.deptRuleTypeStatistical')
      this.echartStatus = 'dept'
      this.$refs.echartDlg.show(this.echartStatus)
    },
    /**
     * 终端操作员小图标点击
     * */
    handleBigTerminalUser() {
      if (this.tempQuery.countByObject === 2) {
        this.echartDlgTitle = this.$t('pages.terminalRuleTypeStatistical')
        this.echartStatus = 'terminal'
      } else {
        this.echartDlgTitle = this.$t('pages.userRuleTypeStatistical')
        this.echartStatus = 'user'
      }
      this.$refs.echartDlg.show(this.echartStatus, this.echartDlgTitle)
    },
    /**
     * 终端（操作员）分析点击
     * 显示终端（操作员）分析，面包屑中添加终端（操作员）分析
     * */
    handleTerminalUserAnalysis() {
      this.inceptionDiv = false
      this.userDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
        this.showFilePath.push({ id: 1, label: this.$t('pages.userRuleTypeAnalysis') })
      } else {
        // 终端
        this.dialogStatus = 'terminal'
        this.showFilePath.push({ id: 1, label: this.$t('pages.terminalRuleTypeAnalysis') })
      }
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    handleDeptAnalysis() {
      this.inceptionDiv = false
      this.deptDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 2, label: this.$t('pages.deptRuleTypeAnalysis') })
    },
    /**
     * 数量趋势点击
     * 显示数量趋势，面包屑中添加数量趋势
     * */
    handleViolateTrendAnalysis() {
      this.inceptionDiv = false
      this.tendencyDiv = true
      this.showFilePath.push({ id: 3, label: this.$t('pages.violationCountTrendAnalysis') })
    },
    /**
     * 规则分析点击
     * 显示规则分析，面包屑中添加规则分析
     * */
    handleRuleTypeAnalysis() {
      this.inceptionDiv = false
      this.ruleDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 4, label: this.$t('pages.ruleTypeAnalysis') })
    },
    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        // 初始页面
        this.inceptionDiv = true
        // 规则页面
        this.ruleDiv = false
        // 操作员（终端）页面
        this.userDiv = false
        // 部门页面
        this.deptDiv = false
        // 趋势页面
        this.tendencyDiv = false
        this.showFilePath = [{ id: 0, label: this.$t('pages.ruleTypeAnalysis') }]
      }
      if (index === 1) {
        this.inceptionDiv = false
        this.ruleDiv = false
        this.userDiv = true
        this.deptDiv = false
        this.tendencyDiv = false
      }
      if (index === 2) {
        this.inceptionDiv = false
        this.ruleDiv = false
        this.userDiv = false
        this.deptDiv = true
        this.tendencyDiv = false
      }
      if (index === 3) {
        this.inceptionDiv = false
        this.ruleDiv = false
        this.userDiv = false
        this.deptDiv = false
        this.tendencyDiv = true
      }
      if (index === 4) {
        this.inceptionDiv = false
        this.ruleDiv = true
        this.userDiv = false
        this.deptDiv = false
        this.tendencyDiv = false
      }
    },
    /**
     * 打印
     * */
    handlePrint() {

    },
    /**
     * 导出
     * */
    handleExport() {

    }
  }
}
</script>
