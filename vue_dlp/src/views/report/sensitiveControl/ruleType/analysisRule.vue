<template>
  <!--规则类型分析-->
  <div class="data-details sensitiveness-report">
    <div class="data-analysis-table table-parent-size">
      <div class="table-container table-left-size">
        <!--左侧列表查询条件-->
        <rough-query ref="roughQuery" :rule-type-analysis="true" :select-label="this.$t('pages.ruleType')" :select-map="ruleTypeMap"/>
        <grid-table
          ref="roughList"
          :col-model="colModel"
          :row-data-api="getData"
          :multi-select="false"
          :autoload="false"
          @row-click="handleRowClick"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--规则类型分析上方几个小方块-->
      <div class="data-panel">
        <div class="data-item data-item-details" >
          <div class="icon">
            <svg-icon icon-class="algorithm"></svg-icon>
          </div>
          <div class="content">
            <label :title="temp.item.ruleTypeName">{{ temp.item.ruleTypeName }}</label>
          </div>
        </div>
        <div class="data-item data-item-details" >
          <div class="icon">
            <svg-icon icon-class="ruleGroup"></svg-icon>
          </div>
          <div class="content">
            <label :title="this.$t('pages.violationCount')">{{ this.$t('pages.violationCount') }}</label>
            <span :title="temp.item.violateCount">{{ temp.item.violateCount }}</span>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 165px); padding: 0 9px;position: relative">
        <!--右侧列表查询条件-->
        <detail-query ref="detailQuery" :input-list="inputList" :is-show-grid="isShowGrid"/>
        <el-tabs v-model="activeTab" @tab-click="tabClick">
          <el-tab-pane v-if="drillDown" :label="this.$t('pages.violationRecord')" name="detail" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                ref="detailList"
                :show="isShowGrid"
                :col-model="detailColModel"
                :row-data-api="getDetailData"
                :autoload="false"
                :multi-select="false"
                row-key="getRowKey"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane :label="terminalUserChartLabel" name="statisticsTerminalUser">
            <pie-chart
              v-if="isShowTerminalUserChart"
              :chart-data="temp.terminalUserPieChartData"
              :chart-option="temp.terminalUserPieChartOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
          <el-tab-pane :label="this.$t('pages.ruleNameStatistical')" name="statisticsRuleName">
            <pie-chart
              v-if="isShowRuleNameChart"
              :chart-data="temp.ruleNamePieChartData"
              :chart-option="temp.ruleNamePieChartOption"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from '@/components/ECharts/PieChart'
import moment from 'moment'
import { getGeneralRuleTypeAnalysisData, listGeneralRuleTypeData } from '@/api/report/baseReport/sensitiveControl/generalRuleType'
import { getDetailPage, checkDetailAble } from '@/api/report/baseReport/issueReport/issue'
import RoughQuery from '@/views/report/sensitiveControl/common/roughQuery'
import DetailQuery from '@/views/report/sensitiveControl/common/detailQuery'
import { deepClone } from '@/utils'

export default {
  name: 'AnalysisTerminalUser',
  components: { DetailQuery, RoughQuery, PieChart },
  props: {
    dialogStatus: { // 状态，区分终端还是操作员
      type: String,
      default() {
        return ''
      }
    },
    detailTable: {
      type: String,
      default() {
        return ''
      }
    },
    ruleTypeMap: {
      type: Object,
      default: function() {
        return {}
      }
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    },
    specificDetailCol: {
      type: Array,
      default() {
        return []
      }
    },
    inputList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeTab: '',
      isShowGrid: this.drillDown,
      isShowTerminalUserChart: !this.drillDown,
      isShowRuleNameChart: false,
      colModel: [
        { prop: 'rule_type', label: 'ruleType', formatter: (row, data) => { return this.ruleTypeMap[data] } },
        { prop: 'violate_count', label: 'violationCount', sort: true }
      ],
      baseDetailCol: [
        { prop: 'create_time', label: 'operateTime', width: '110' },
        { prop: 'terminal_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'term_group_name', label: 'terminalGroup', hidden: () => this.colHidden(['user']) },
        { prop: 'user_name', label: 'user', hidden: () => this.colHidden(['terminal']) },
        { prop: 'user_group_name', label: 'userGroup', hidden: () => this.colHidden(['terminal']) }
      ],
      query: {
        page: 1
      },
      detailQuery: {
        page: 1,
        detailTable: this.detailTable,
        countByObject: 0,
        condition: { rule_type: '' }
      },
      analysisQuery: {
        labelValue: ''
      },
      temp: {},
      defaultTemp: {
        item: {
          ruleTypeName: '',
          violateCount: 0
        },
        ruleNamePieChartData: [],
        ruleNamePieChartOption: {
          title: {
            text: this.$t('pages.ruleNameStatistical'),
            subtext: this.$t('pages.topN', { num: 5 })
          },
          toolbox: {
            show: false
          },
          legend: {
            left: 'center'
          },
          series: [
            {
              radius: '30%'
            }
          ]
        },
        terminalUserPieChartData: [],
        terminalUserPieChartOption: {
          title: {
            text: '',
            subtext: this.$t('pages.topN', { num: 5 })
          },
          toolbox: {
            show: false
          },
          legend: {
            left: 'center'
          },
          series: [
            {
              radius: '40%'
            }
          ]
        }
      }
    }
  },
  computed: {
    detailColModel() {
      return this.baseDetailCol.concat(this.specificDetailCol)
    },
    terminalUserChartLabel() {
      return this.dialogStatus === 'user' ? this.$t('pages.userStatistical') : this.$t('pages.terminalStatistical')
    }
  },
  created() {
    this.activeTab = this.drillDown ? 'detail' : 'statisticsTerminalUser'
    this.resetTemp()
  },
  async mounted() {
    await this.$refs['roughList'].execRowDataApi()
    const tableData = this.$refs['roughList'].getDatas()
    // 如果左侧列表长度不为0，初始化页面时显示第一条数据的详情
    if (tableData[0]) {
      this.handleRowClick(tableData[0])
    }
  },
  methods: {
    // 查询违规数据
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$refs.roughQuery.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return listGeneralRuleTypeData(searchQuery)
    },
    // 查询违规明细数据
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.$parent.tempQuery, this.$refs.detailQuery.query, this.detailQuery, option)
      return getDetailPage(searchQuery)
    },
    // 点击左侧表格
    handleRowClick(rowData) {
      // 设置生效的tab
      if (this.drillDown) {
        this.isShowGrid = true
        this.isShowTerminalUserChart = false
        this.isShowRuleNameChart = false
        this.activeTab = 'detail'
      } else {
        this.isShowTerminalUserChart = true
        this.isShowRuleNameChart = false
        this.activeTab = 'statisticsTerminalUser'
      }

      // 查询分析数据
      const ruleType = rowData.rule_type
      this.analysisQuery.labelValue = ruleType
      const searchQuery = Object.assign({}, this.analysisQuery, this.$parent.tempQuery)
      getGeneralRuleTypeAnalysisData(searchQuery).then(resp => {
        this.temp.item.ruleTypeName = this.ruleTypeMap[ruleType]
        this.temp.item.violateCount = rowData.violate_count

        this.temp.terminalUserPieChartData = resp.data.chartDataObjMap.terminalUserPieChartData.chartData
        this.temp.ruleNamePieChartData = resp.data.chartDataObjMap.ruleNamePieChartData.chartData
      })
      // 查询违规明细数据
      if (this.drillDown) {
        checkDetailAble(this.$parent.tempQuery).then(() => {
          this.detailQuery.condition.rule_type = ruleType
          this.$refs['detailList'].execRowDataApi({ page: 1 })
        })
      }
    },
    /**
     * 页面初始化
     * */
    resetTemp() {
      this.defaultTemp.terminalUserPieChartOption.title.text = this.terminalUserChartLabel
      this.temp = deepClone(this.defaultTemp)
    },
    /**
     * 根据终端还是操作员控制表格列的显示隐藏
     * */
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    /**
     * tab点击事件，如果不写，切换时图表只有小小一点点
     * @param pane
     * @param event
     */
    tabClick(pane, event) {
      if (pane.name === 'detail') {
        this.isShowGrid = true
        this.isShowTerminalUserChart = false
        this.isShowRuleNameChart = false
      } else if (pane.name === 'statisticsTerminalUser') {
        this.isShowGrid = false
        this.isShowTerminalUserChart = true
        this.isShowRuleNameChart = false
      } else {
        this.isShowGrid = false
        this.isShowTerminalUserChart = false
        this.isShowRuleNameChart = true
      }
    },
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
