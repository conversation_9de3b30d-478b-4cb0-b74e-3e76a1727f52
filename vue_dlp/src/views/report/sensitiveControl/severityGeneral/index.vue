<template>
  <severity
    type="general"
    detail-table="dwd_general_violate_log"
    tip="报表显示的数据为常规检测的详细数据"
    :homepage-data-api="homepageDataApi"
    :dept-chart-api="deptChartApi"
    :terminal-user-chart-api="terminalUserChartApi"
    :dept-api="deptApi"
    :terminal-user-api="terminalUserApi"
    :dept-analysis-api="deptAnalysisApi"
    :terminal-user-analysis-api="terminalUserAnalysisApi"
    :trend-analysis-api="trendAnalysisApi"
    :drill-down="hasPermission('835')"
  >
  </severity>
</template>

<script>
import Severity from '@/views/report/sensitiveControl/common/severity/index'
import { getGeneralSeverityHomepageData, getGeneralSeverityDeptChartData, getGeneralSeverityTerminalUserChartData, listGeneralSeverityDeptData, listGeneralSeverityTerminalUserData,
  getGeneralSeverityDeptAnalysisData, getGeneralSeverityTerminalUserAnalysisData, getGeneralSeverityTrendAnalysisData } from '@/api/report/baseReport/sensitiveControl/generalSeverity'

export default {
  name: 'SeverityGeneral',
  components: { Severity },
  data() {
    return {
      homepageDataApi: getGeneralSeverityHomepageData,
      deptChartApi: getGeneralSeverityDeptChartData,
      terminalUserChartApi: getGeneralSeverityTerminalUserChartData,
      deptApi: listGeneralSeverityDeptData,
      terminalUserApi: listGeneralSeverityTerminalUserData,
      deptAnalysisApi: getGeneralSeverityDeptAnalysisData,
      terminalUserAnalysisApi: getGeneralSeverityTerminalUserAnalysisData,
      trendAnalysisApi: getGeneralSeverityTrendAnalysisData
    }
  }
}
</script>
