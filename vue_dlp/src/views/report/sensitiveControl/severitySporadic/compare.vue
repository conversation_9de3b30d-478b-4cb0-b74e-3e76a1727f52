<template>
  <comparative-trend
    :api="'getSporadicViolateSeverityCompareData'"
    :api-dept="'getSporadicViolateSeverityCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'SporadicViolateSeverityCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '零星-违规严重程度',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'slightSumAll', label: 'slightSumAll' },
        { prop: 'generalSumAll', label: 'generalSumAll' },
        { prop: 'seriousSumAll', label: 'seriousSumAll' },
        { prop: 'esSeriousSumAll', label: 'esSeriousSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['slightSumAll', 'generalSumAll', 'seriousSumAll', 'esSeriousSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
