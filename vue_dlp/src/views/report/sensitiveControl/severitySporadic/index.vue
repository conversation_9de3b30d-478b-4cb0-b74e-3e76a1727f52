<template>
  <severity
    type="sporadic"
    detail-table="dwd_sporadic_violate_log"
    tip="报表显示的数据为零星检测触发n次的产生的数据（注：零星检测触发一次可能会产生多条数据）"
    :homepage-data-api="homepageDataApi"
    :dept-chart-api="deptChartApi"
    :terminal-user-chart-api="terminalUserChartApi"
    :dept-api="deptApi"
    :terminal-user-api="terminalUserApi"
    :dept-analysis-api="deptAnalysisApi"
    :terminal-user-analysis-api="terminalUserAnalysisApi"
    :trend-analysis-api="trendAnalysisApi"
    :drill-down="hasPermission('850')"
  >
  </severity>
</template>

<script>
import Severity from '@/views/report/sensitiveControl/common/severity/index'
import { getSporadicSeverityHomepageData, getSporadicSeverityDeptChartData, getSporadicSeverityTerminalUserChartData, listSporadicSeverityDeptData, listSporadicSeverityTerminalUserData,
  getSporadicSeverityDeptAnalysisData, getSporadicSeverityTerminalUserAnalysisData, getSporadicSeverityTrendAnalysisData } from '@/api/report/baseReport/sensitiveControl/sporadicSeverity'

export default {
  name: 'SeveritySporadic',
  components: { Severity },
  data() {
    return {
      homepageDataApi: getSporadicSeverityHomepageData,
      deptChartApi: getSporadicSeverityDeptChartData,
      terminalUserChartApi: getSporadicSeverityTerminalUserChartData,
      deptApi: listSporadicSeverityDeptData,
      terminalUserApi: listSporadicSeverityTerminalUserData,
      deptAnalysisApi: getSporadicSeverityDeptAnalysisData,
      terminalUserAnalysisApi: getSporadicSeverityTerminalUserAnalysisData,
      trendAnalysisApi: getSporadicSeverityTrendAnalysisData
    }
  }
}
</script>
