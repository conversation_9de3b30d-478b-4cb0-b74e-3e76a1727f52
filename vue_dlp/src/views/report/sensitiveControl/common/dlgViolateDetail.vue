<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :fullscreen="fullscreen"
      :title="dlgTitle"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <grid-table
        ref="violateDetailTable"
        :stripe="false"
        :row-key="getRowKey"
        :col-model="colModel"
        :row-data-api="getData"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        default-expand-all
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getDetailPage, checkDetailAble } from '@/api/report/baseReport/issueReport/issue'
import moment from 'moment'

export default {
  name: 'DlgTotalViolation',
  props: {
    dlgTitle: {
      type: String,
      default() {
        return ''
      }
    },
    detailTable: {
      type: String,
      default() {
        return ''
      }
    },
    specificCol: {
      type: Array,
      default() {
        return []
      }
    },
    searchCondition: {
      type: Object,
      default() {
        return {}
      }
    },
    notNullFields: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      query: {
        page: 1,
        detailTable: this.detailTable,
        groupType: 0,
        condition: this.searchCondition,
        notNullFields: this.notNullFields
      },
      baseCol: [
        { prop: 'create_time', label: 'operateTime', width: '110' },
        { prop: 'terminal_name', label: 'terminalName', hidden: () => this.$parent.tempQuery.countByObject === 1 },
        { prop: 'term_group_name', label: 'terminalGroup', hidden: () => this.$parent.tempQuery.countByObject === 1 },
        { prop: 'user_name', label: 'user', hidden: () => this.$parent.tempQuery.countByObject === 2 },
        { prop: 'user_group_name', label: 'userGroup', hidden: () => this.$parent.tempQuery.countByObject === 2 }
      ]
    }
  },
  computed: {
    colModel() {
      return this.baseCol.concat(this.specificCol)
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  methods: {
    /**
     * 获取数据信息
     * */
    getData(option) {
      const searchQuery = Object.assign({}, this.$parent.tempQuery, this.query, option)
      return getDetailPage(searchQuery)
    },
    /**
     * 显示弹框
     */
    show() {
      checkDetailAble(this.$parent.tempQuery).then(() => {
        this.dialogVisible = true
        if (this.$refs['violateDetailTable']) {
          this.$refs['violateDetailTable'].execRowDataApi(this.query)
        }
      })
    },
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    }
  }
}
</script>
