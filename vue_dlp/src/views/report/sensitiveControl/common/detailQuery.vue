<template>
  <el-popover placement="bottom" width="350" trigger="click">
    <Form ref="searchRightForm" label-position="right" :model="query" label-width="80px">
      <!-- 动态生成下拉框 -->
      <FormItem
        v-for="(selectItem, index) in selectList"
        :key="'select-' + index"
        :label="selectItem.selectLabel"
      >
        <el-select
          v-model="query.condition[selectItem.selectKey]"
          clearable
          :placeholder="$t('pages.all')"
          style="width: 244px"
        >
          <el-option
            v-for="(value, key) in selectItem.selectMap"
            :key="key"
            :label="value"
            :value="key"
          ></el-option>
        </el-select>
      </FormItem>

      <!-- 动态生成输入框 -->
      <FormItem
        v-for="(inputItem, index) in inputList"
        :key="'input-' + index"
        :label="inputItem.inputLabel"
      >
        <el-input
          v-model="query.likeCondition[inputItem.inputKey]"
          v-trim
          maxlength=""
          clearable
        />
      </FormItem>
    </Form>
    <div style="text-align: right; margin-top: 10px">
      <!--重置、查询按钮-->
      <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
      <el-button type="primary" size="mini" @click="searchData()">{{ $t('button.search') }}</el-button>
    </div>
    <el-button v-show="isShowGrid" slot="reference" type="primary" icon="el-icon-search" class="search-icon-right" size="mini"></el-button>
  </el-popover>
</template>

<script>
export default {
  name: 'DetailQuery',
  props: {
    selectList: {
      type: Array,
      default: () => []
    },
    inputList: {
      type: Array,
      default: () => []
    },
    isShowGrid: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      query: {
        condition: {},
        likeCondition: {}
      }
    }
  },
  methods: {
    resetQuery() {
      this.query.condition = {}
      this.query.likeCondition = {}
    },
    searchData() {
      this.removeEmptyKey(this.query.condition)
      this.removeEmptyKey(this.query.likeCondition)
      this.$parent.$refs['detailList'].execRowDataApi({ page: 1 })
    },
    removeEmptyKey(obj) {
      Object.keys(obj).forEach(key => {
        if (!obj[key].trim()) {
          delete obj[key]
        }
      })
    }
  }
}
</script>
