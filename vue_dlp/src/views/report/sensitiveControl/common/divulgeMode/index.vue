<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query">
        <i class="el-icon-s-home" style="position: absolute; left: 24px; top: 16px;"></i>
        <!--面包屑-->
        <div style="width: 510px; float: left;">
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-tooltip effect="dark" placement="bottom-start" style="margin-left: 10px">
              <span slot="content">
                {{ tip }}
              </span>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <!--上方搜索条件-->
        <div style="float: right">
          <!--查询条件组件-->
          <query ref="queryData"/>
          <!--图表解读-->
          <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
          <!-- <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
          <el-button type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>-->
        </div>
      </div>

      <!-- 页面无数据 -->
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>

      <!--分析初始页面-->
      <div v-if="inceptionDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in type === 'sporadic' ? itemOption : itemOption.slice(1)" :key="key" class="data-item" >
            <div class="icon">
              <svg-icon :icon-class="item.icon"></svg-icon>
            </div>
            <div class="content">
              <label :title="item.label">{{ item.label }}</label>
              <el-button type="text" :class="{'underline': !item.clickable }" @click="item.func">{{ temp.itemValue[item.key] }}</el-button>
            </div>
          </div>
        </div>
        <!--中间泄露方式统计图-->
        <div class="chart-panel">
          <div class="chart-item flex-2">
            <div style="position: relative;float: right;right: 20px;z-index: 1;text-align: right">
              <el-button type="text" @click.native="handleColumn"><i class="el-icon-s-tools"></i>{{ this.$t('pages.customColumn') }}</el-button>
            </div>
            <bar-chart
              ref="divulgeModeBarChart"
              :chart-data="temp.divulgeModeBarChartData"
              :chart-option="temp.divulgeModeBarChartOption"
              :x-axis-name="''"
              :click="barChartFun"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.divulgeMode.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title">
                解读：<span style="color: #909399;">通过泄露途径的频次对比，明确风险优先级，定位安全管理薄弱环节，为资源分配提供依据。</span>
              </div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.divulgeMode + '</div>'"></div>
            </div>
          </div>
        </div>

        <!--下方对部门，终端（操作员），泄露方式的分别展示部分-->
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleDeptAnalysis">{{ $t('pages.deptDisclosureModeAnalysis') }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigDept"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.deptBarChartData"
              :chart-option="temp.deptBarChartOption"
              :x-axis-name="this.$t('pages.dept')"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.deptDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.deptDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleTerminalUserAnalysis">{{ terminalUserAnalysisTitle }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigTerminalUser"/>
              </el-tooltip>
            </div>
            <pie-chart
              :chart-data="temp.terminalUserPieChartData"
              :chart-option="temp.terminalUserPieChartOption"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.termOrUserDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.termOrUserDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title"><span @click="handleViolateTrendAnalysis">{{ $t('pages.violationCountTrendAnalysis') }}>></span></div>
            <line-chart
              :chart-data="temp.trendLineChartData"
              :chart-option="temp.trendLineChartOption"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.violationNum.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.violationNum + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
      <!--部门分析-->
      <div v-if="deptDiv" class="detailsDiv">
        <analysis-dept
          :dialog-status="dialogStatus"
          chart-type="bar"
          :chart-title="this.$t('pages.disclosureModeStatistical')"
          :detail-table="detailTable"
          :dept-api="deptApi"
          :dept-analysis-api="deptAnalysisApi"
          :not-null-fields="notNullFields"
          :select-label="this.$t('pages.lossType')"
          :select-map="divulgeModeMap"
          :drill-down="drillDown"
          :specific-col="specificCol"
          :specific-detail-col="specificDetailCol"
          :select-list="selectList"
          :input-list="inputList"
        />
      </div>
      <!--终端、操作员分析-->
      <div v-if="userDiv" class="detailsDiv">
        <analysis-terminal-user
          :dialog-status="dialogStatus"
          :chart-title="this.$t('pages.disclosureModeStatistical')"
          :detail-table="detailTable"
          :terminal-user-api="terminalUserApi"
          :terminal-user-analysis-api="terminalUserAnalysisApi"
          :not-null-fields="notNullFields"
          :select-label="this.$t('pages.lossType')"
          :select-map="divulgeModeMap"
          :drill-down="drillDown"
          :specific-col="specificCol"
          :specific-detail-col="specificDetailCol"
          :select-list="selectList"
          :input-list="inputList"
        />
      </div>
      <!--违规数量趋势分析-->
      <div v-if="tendencyDiv" class="detailsDiv">
        <analysis-tendency :chart-title="this.$t('pages.violationCountTrend')" :trend-analysis-api="trendAnalysisApi"/>
      </div>
    </div>
    <el-dialog
      v-el-drag-dialog
      :title="this.$t('pages.customColumn')"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :modal="false"
      :append-to-body="true"
    >
      <div class="checkbox">
        <h4>{{ $t('pages.report_text1') }}</h4>
        <div v-show="tempSelectedDivulgeMode.length===0" style="color:red;font-size:12px;margin-bottom:10px">{{ $t('pages.report_text2') }}</div>
        <div>
          <el-button type="text" @click="handleCheckAllChange">{{ $t('button.selectAll') }}</el-button> /
          <el-button type="text" @click="handleCheckNone">{{ $t('button.selectNone') }}</el-button>
        </div>
        <el-checkbox-group v-model="tempSelectedDivulgeMode">
          <el-checkbox
            v-for="(value, key) in divulgeModeMap"
            :key="key"
            :label="key"
          >{{ value }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConfirmColumn">{{ $t('button.confirm2') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>
    <!--小方块（违规总数）点击弹框-->
    <violate-detail-dlg ref="totalViolationDlg" :dlg-title="this.$t('pages.violationTotal')" :detail-table="detailTable" :specific-col="specificDetailCol" :not-null-fields="notNullFields"/>
    <!--小方块（部门）点击弹框-->
    <dept-dlg ref="deptDlg" :data-api="deptApi" :specific-col="specificCol"/>
    <!--小方块（终端/操作员）点击击弹框-->
    <terminal-user-dlg ref="terminalUserDlg" :title="terminalUserDlgTitle" :data-api="terminalUserApi" :specific-col="specificCol"/>
    <!--泄露方式（柱状统计图）点击弹框-->
    <violate-detail-dlg ref="modeDisclosureDlg" :dlg-title="divulgeModeTitle" :detail-table="detailTable" :specific-col="specificDetailCol.slice(1)" :search-condition="searchCondition"/>
    <!--部门、终端、操作员前五名小图标点击弹框-->
    <echart-dlg ref="echartDlg" :title="echartDlgTitle" :dept-chart-api="deptChartApi" :terminal-user-chart-api="terminalUserChartApi"/>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
import Query from '@/views/report/sensitiveControl/common/query'
import DeptDlg from '@/views/report/sensitiveControl/common/dlgDept'
import EchartDlg from '@/views/report/sensitiveControl/common/echartDlg'
import TerminalUserDlg from '@/views/report/sensitiveControl/common/dlgTerminalUser'
import ViolateDetailDlg from '@/views/report/sensitiveControl/common/dlgViolateDetail'
import analysisDept from '@/views/report/sensitiveControl/common/analysisDept'
import analysisTerminalUser from '@/views/report/sensitiveControl/common/analysisTerminalUser'
import analysisTendency from '@/views/report/sensitiveControl/common/analysisTendency'
import { deepClone } from '@/utils'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'

export default {
  name: 'DivulgeMode',
  components: { Query, BarChart, PieChart, LineChart, DeptDlg, EchartDlg, TerminalUserDlg, ViolateDetailDlg, analysisDept, analysisTerminalUser, analysisTendency },
  props: {
    tip: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    detailTable: {
      type: String,
      default: ''
    },
    homepageDataApi: {
      type: Function,
      default: function() {}
    },
    deptChartApi: {
      type: Function,
      default: function() {}
    },
    terminalUserChartApi: {
      type: Function,
      default: function() {}
    },
    deptApi: {
      type: Function,
      default: function() {}
    },
    terminalUserApi: {
      type: Function,
      default: function() {}
    },
    deptAnalysisApi: {
      type: Function,
      default: function() {}
    },
    terminalUserAnalysisApi: {
      type: Function,
      default: function() {}
    },
    trendAnalysisApi: {
      type: Function,
      default: function() {}
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 搜查查询
      query: {
        // 是否展示图表解读
        isExplain: true,
        dimBaseType: 1,
        dimValue: '',
        countByObject: 2
      },
      tempQuery: {},
      terminalUserAnalysisTitle: this.$t('pages.terminalDisclosureModeAnalysis'),
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: this.$t('pages.disclosureModeAnalysis') }
      ],
      // 暂无数据
      noData: true,
      // 初始页面显示隐藏
      inceptionDiv: false,
      // 操作员分析显示隐藏
      userDiv: false,
      // 部门分析显示隐藏
      deptDiv: false,
      // 趋势分析显示隐藏
      tendencyDiv: false,
      // 泄露方式弹框名称
      divulgeModeTitle: '',
      // 违规终端/操作员弹窗名称
      terminalUserDlgTitle: '',
      // 放大柱状图弹框名称
      echartDlgTitle: '',
      echartStatus: '',
      // 弹框状态
      dialogStatus: '',
      // 上方小方块
      itemOption: [
        { label: '触发次数', key: 'trigger', icon: 'total', func: this.triggerFun, clickable: false },
        { label: this.$t('pages.violationTotal'), key: 'all', icon: 'total', func: this.totalViolationFun, clickable: this.drillDown },
        { label: this.$t('pages.dept'), key: 'dept', icon: 'tree', func: this.deptFun, clickable: true },
        { label: this.$t('pages.violationTerminal'), key: 'terminalUser', icon: 'terminal', func: this.terminalUserFun, clickable: true }
      ],
      commonChartOption: {
        title: {
          text: '',
          subtext: ''
        },
        toolbox: {
          show: false
        },
        grid: {
          top: 60,
          width: '85%',
          bottom: 20,
          left: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          // 控制分隔线
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          // 控制隔区域
          splitArea: {
            show: false
          }
        },
        series: [
          {
            data: [],
            type: 'bar'
          }
        ]
      },
      commonInterpretation: `<span>图表解读：</span>`,
      temp: {},
      defaultTemp: {
        // 图表解读
        chartInterpretationContent: {
          // 泄露方式统计
          divulgeMode: '',
          // 部门泄露方式统计
          deptDistribute: '',
          // 终端/操作员泄露方式统计
          termOrUserDistribute: '',
          // 违规数量分析
          violationNum: ''
        },
        itemValue: {
          trigger: '0',
          all: '0',
          dept: '0',
          terminalUser: '0'
        },
        // 泄露方式柱状统计图
        divulgeModeBarChartData: [],
        divulgeModeBarChartOption: {},
        // 部门泄露数量柱状图数据
        deptBarChartData: [],
        deptBarChartOption: {},
        // 饼图 终端/操作员泄露数量图
        terminalUserPieChartData: [],
        terminalUserPieChartOption: {},
        // 折线图数据 违规数量趋势分析
        trendLineChartData: [],
        trendLineChartOption: {}
      },
      // 自定义列
      dialogVisible: false,
      selectedDivulgeMode: [],
      tempSelectedDivulgeMode: [],
      // 用于记录泄露方式-数量
      divulgeModeCountMap: new Map(),
      // 调用后台接口获取
      divulgeModeMap: {},
      specificDetailCol: [
        { prop: 'loss_type', label: 'lossType', formatter: (row, data) => { return this.divulgeModeMap[data] } },
        { prop: 'file_name', label: 'sensitiveFile', formatter: this.fileFormatter },
        { prop: 'stg_def_name', label: 'contentStg' }
      ],
      specificCol: [
        { prop: 'violate_count', label: 'violationCount', sort: true },
        { prop: 'lossTypeStr', label: 'lossType', formatter: (row, data) => { return data.replace(/^,|,$/g, '').split(',').map(item => this.divulgeModeMap[item]).join(',') } }
      ],
      searchCondition: {
        loss_type: ''
      },
      notNullFields: ['loss_type'],
      // 明细数据输入框搜索条件
      inputList: [
        {
          inputLabel: this.$t('table.sensitiveFile'),
          inputKey: 'file_name'
        },
        {
          inputLabel: this.$t('table.contentStg'),
          inputKey: 'stg_def_name'
        }
      ]
    }
  },
  computed: {
    // 明细数据下拉框搜索条件
    selectList() {
      return [
        {
          // 标签名
          selectLabel: this.$t('pages.lossType'),
          // 绑定字段名
          selectKey: 'loss_type',
          // 下拉选项
          selectMap: this.divulgeModeMap
        }
      ]
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
    this.getSensitiveLossType()
  },
  methods: {
    fileFormatter(row) {
      return row.file_name + (row.child_file_path ? row.child_file_path : '')
    },
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        res.data.forEach(item => {
          this.divulgeModeMap[item.lossType] = item.lossDesc
        })
      }).catch(err => {
        console.error('获取泄露方式数据失败:', err)
      })
    },
    resetTemp() {
      this.defaultTemp.divulgeModeBarChartOption = this.getDivulgeModeBarChartOption()
      this.defaultTemp.deptBarChartOption = this.getDeptBarChartOption(5)
      this.defaultTemp.terminalUserPieChartOption = this.getTerminalUserPieChartOption(5)
      this.defaultTemp.terminalUserPieChartOption.title.text = this.query.countByObject === 1 ? this.$t('pages.userDisclosureModeStatistical') : this.$t('pages.terminalDisclosureModeStatistical')
      this.defaultTemp.trendLineChartOption = this.getTrendLineChartOption()
      this.temp = deepClone(this.defaultTemp)
    },
    /**
     * 获取数据信息
     * */
    getData() {
      this.query = this.$refs['queryData'].getQuery()
      this.tempQuery = Object.assign({}, this.query)
      this.selectedDivulgeMode = []
      this.divulgeModeCountMap.clear()
      this.temp = JSON.parse(JSON.stringify(this.temp))
      return this.homepageDataApi(this.query).then(resp => {
        if (resp.data) {
          this.inceptionDiv = true
          this.noData = false
          // 图表解读
          if (resp.data.itemValueMap.explanation) {
            this.temp.chartInterpretationContent = { ...this.temp.chartInterpretationContent, ...resp.data.itemValueMap.explanation }
          } else {
            this.temp.chartInterpretationContent = {
              // 泄露方式统计
              divulgeMode: '',
              // 部门泄露方式统计
              deptDistribute: '',
              // 终端/操作员泄露方式统计
              termOrUserDistribute: '',
              // 违规数量分析
              violationNum: ''
            }
          }
          if ('sporadic' === this.type) {
            this.temp.itemValue.trigger = resp.data.itemValueMap.logCount
          }
          this.temp.itemValue.all = resp.data.itemValueMap.violateTotal
          this.temp.itemValue.dept = resp.data.itemValueMap.deptCount
          this.temp.itemValue.terminalUser = resp.data.itemValueMap.terminalUserCount

          this.temp.divulgeModeBarChartData = resp.data.chartDataObjMap.lossTypeBarChartData.chartData.slice(0, 10)
          this.temp.divulgeModeBarChartOption.xAxis.data = resp.data.chartDataObjMap.lossTypeBarChartData.xaxisData.slice(0, 10)
          this.temp.divulgeModeBarChartOption.series[0].data = resp.data.chartDataObjMap.lossTypeBarChartData.seriesData.slice(0, 10)
          this.temp.divulgeModeBarChartOption.grid = { top: '25%', width: '90%', bottom: '15%', left: '5%', containLabel: true }
          this.buildSelectedDivulgeMode(this.temp.divulgeModeBarChartOption.xAxis.data)
          this.buildDivulgeModeCountMap(resp.data.chartDataObjMap.lossTypeBarChartData.xaxisData, resp.data.chartDataObjMap.lossTypeBarChartData.chartData)

          this.temp.deptBarChartData = resp.data.chartDataObjMap.deptBarChartData.chartData
          this.temp.deptBarChartOption.xAxis.data = resp.data.chartDataObjMap.deptBarChartData.xaxisData
          this.temp.deptBarChartOption.series[0].data = resp.data.chartDataObjMap.deptBarChartData.seriesData

          this.temp.terminalUserPieChartData = resp.data.chartDataObjMap.terminalUserPieChartData.chartData

          this.temp.trendLineChartData = resp.data.chartDataObjMap.violateCountTrendLineCharData.chartData
          this.temp.trendLineChartOption.xAxis.data = resp.data.chartDataObjMap.violateCountTrendLineCharData.xaxisData
          this.temp.trendLineChartOption.series[0].data = resp.data.chartDataObjMap.violateCountTrendLineCharData.seriesData
        } else {
          this.resetTemp()
          this.inceptionDiv = false
          this.noData = true
        }
      }).then(() => {
        this.triggerResize()
      })
    },
    /**
     * 搜索按钮点击
     * */
    loadReport() {
      this.getData()
      if (this.query.countByObject === 1) {
        // 操作员
        Object.assign(this.itemOption[this.itemOption.length - 1], { label: this.$t('pages.violationUser'), icon: 'user' })
        this.terminalUserAnalysisTitle = this.$t('pages.userDisclosureModeAnalysis')
        this.temp.terminalUserPieChartOption.title.text = this.$t('pages.userDisclosureModeStatistical')
      } else {
        // 终端
        Object.assign(this.itemOption[this.itemOption.length - 1], { label: this.$t('pages.violationTerminal'), icon: 'terminal' })
        this.terminalUserAnalysisTitle = this.$t('pages.terminalDisclosureModeAnalysis')
        this.temp.terminalUserPieChartOption.title.text = this.$t('pages.terminalDisclosureModeStatistical')
      }
      // 初始页面（点击搜索的时候要回到初始页面）
      // this.inceptionDiv = true
      // 操作员（终端）页面
      this.userDiv = false
      // 部门页面
      this.deptDiv = false
      // 趋势页面
      this.tendencyDiv = false
      this.showFilePath = [{ id: 0, label: this.$t('pages.disclosureModeAnalysis') }]
    },
    getDeptBarChartOption(top) {
      const commonChartOption = deepClone(this.commonChartOption)
      return {
        ...commonChartOption,
        title: {
          text: this.$t('pages.deptDisclosureModeStatistical'),
          subtext: this.$t('pages.topN', { num: top })
        }
      }
    },
    getTerminalUserPieChartOption(top) {
      return {
        title: {
          text: '',
          subtext: this.$t('pages.topN', { num: top })
        },
        toolbox: {
          show: false
        },
        legend: {
          show: false,
          left: 'center'
        },
        series: [
          {
            radius: '40%',
            center: ['50%', '50%']
          }
        ]
      }
    },
    getDivulgeModeBarChartOption() {
      const commonChartOption = deepClone(this.commonChartOption)
      return {
        ...commonChartOption,
        title: {
          text: this.$t('pages.disclosureModeStatistical')
        },
        xAxis: {
          ...commonChartOption.xAxis,
          axisLabel: {
            interval: 0,
            rotate: -10
          }
        }
      }
    },
    getTrendLineChartOption() {
      const commonChartOption = deepClone(this.commonChartOption)
      return {
        ...commonChartOption,
        title: {
          text: this.$t('pages.violationCountTrend')
        },
        series: [
          {
            ...commonChartOption.series[0],
            type: 'line'
          }
        ]
      }
    },
    /**
     * 触发次数（没有点击效果，但是因为遍历，所以还是要加一个点击方法）
     * */
    triggerFun() {

    },
    /**
     * 违规总数(点击)
     */
    totalViolationFun() {
      if (this.drillDown) {
        this.$refs.totalViolationDlg.show()
      }
    },
    /**
     * 部门（点击）
     */
    deptFun() {
      this.$refs.deptDlg.show()
    },
    /**
     * 终端或操作员（点击）
     */
    terminalUserFun() {
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.terminalUserDlgTitle = this.$t('pages.violationUser')
      } else {
        // 终端
        this.terminalUserDlgTitle = this.$t('pages.violationTerminal')
      }
      this.$refs.terminalUserDlg.show()
    },
    /**
     * 泄露方式（点击）
     */
    barChartFun(params) {
      if (this.drillDown && params.name) {
        this.divulgeModeTitle = params.name
        this.searchCondition.loss_type = Object.keys(this.divulgeModeMap).find(k => this.divulgeModeMap[k] === params.name)
        this.$refs.modeDisclosureDlg.show()
      }
    },
    /**
     * 自定义列弹框显示
     * */
    handleColumn() {
      this.tempSelectedDivulgeMode = this.selectedDivulgeMode
      this.dialogVisible = true
    },
    /**
     * 自定义列确认按钮点击时
     * */
    handleConfirmColumn() {
      if (this.tempSelectedDivulgeMode.length > 0) {
        this.selectedDivulgeMode = this.tempSelectedDivulgeMode
        this.resetDivulgeModeBarchartData()
        this.dialogVisible = false
      }
    },
    /**
     * 自定义列设置全选
     * */
    handleCheckAllChange() {
      this.tempSelectedDivulgeMode = Object.keys(this.divulgeModeMap)
    },
    /**
     * 自定义列设置全不选
     * */
    handleCheckNone() {
      this.tempSelectedDivulgeMode = []
    },
    /**
     * 部门分析小图标点击
     * */
    handleBigDept() {
      this.echartDlgTitle = this.$t('pages.deptDisclosureModeStatistical')
      this.echartStatus = 'dept'
      this.$refs.echartDlg.show(this.echartStatus)
    },
    /**
     * 终端操作员小图标点击
     * */
    handleBigTerminalUser() {
      if (this.tempQuery.countByObject === 2) {
        this.echartDlgTitle = this.$t('pages.terminalDisclosureModeStatistical')
        this.echartStatus = 'terminal'
      } else {
        this.echartDlgTitle = this.$t('pages.userDisclosureModeStatistical')
        this.echartStatus = 'user'
      }
      this.$refs.echartDlg.show(this.echartStatus, this.echartDlgTitle)
    },
    buildSelectedDivulgeMode(divulgeModeNames) {
      divulgeModeNames.forEach(item => {
        const lossTypeName = Object.keys(this.divulgeModeMap).find(k => this.divulgeModeMap[k] === item)
        if (lossTypeName) {
          this.selectedDivulgeMode.push(lossTypeName)
        }
      })
    },
    buildDivulgeModeCountMap(divulgeModeNames, counts) {
      for (let i = 0; i < divulgeModeNames.length; i++) {
        this.divulgeModeCountMap.set(Object.keys(this.divulgeModeMap).find(k => this.divulgeModeMap[k] === divulgeModeNames[i]), counts[i])
      }
    },
    resetDivulgeModeBarchartData() {
      const counts = []
      const divulgeModeNames = []
      this.divulgeModeCountMap.forEach((value, key) => {
        if (this.selectedDivulgeMode.includes(key)) {
          counts.push(value)
          divulgeModeNames.push(this.divulgeModeMap[key])
        }
      })
      this.temp.divulgeModeBarChartData = counts
      this.temp.divulgeModeBarChartOption.xAxis.data = divulgeModeNames
      this.temp.divulgeModeBarChartOption.series[0].data = counts
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    handleDeptAnalysis() {
      this.inceptionDiv = false
      this.deptDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 2, label: this.$t('pages.deptDisclosureModeAnalysis') })
    },
    /**
     * 终端（操作员）分析点击
     * 显示终端（操作员）分析，面包屑中添加终端（操作员）分析
     * */
    handleTerminalUserAnalysis() {
      this.inceptionDiv = false
      this.userDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
        this.showFilePath.push({ id: 1, label: this.$t('pages.userDisclosureModeAnalysis') })
      } else {
        // 终端
        this.dialogStatus = 'terminal'
        this.showFilePath.push({ id: 1, label: this.$t('pages.terminalDisclosureModeAnalysis') })
      }
    },
    /**
     * 数量趋势点击
     * 显示数量趋势，面包屑中添加数量趋势
     * */
    handleViolateTrendAnalysis() {
      this.inceptionDiv = false
      this.tendencyDiv = true
      this.showFilePath.push({ id: 3, label: this.$t('pages.violationCountTrendAnalysis') })
    },
    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        // 初始页面
        this.inceptionDiv = true
        // 操作员（终端）页面
        this.userDiv = false
        // 部门页面
        this.deptDiv = false
        // 趋势页面
        this.tendencyDiv = false
        this.showFilePath = [{ id: 0, label: this.$t('pages.disclosureModeAnalysis') }]
      }
      if (index === 1) {
        this.inceptionDiv = false
        this.userDiv = true
        this.deptDiv = false
        this.tendencyDiv = false
      }
      if (index === 2) {
        this.inceptionDiv = false
        this.userDiv = false
        this.deptDiv = true
        this.tendencyDiv = false
      }
      if (index === 3) {
        this.inceptionDiv = false
        this.userDiv = false
        this.deptDiv = false
        this.tendencyDiv = true
      }
    },
    /**
     * 打印
     * */
    handlePrint() {

    },
    /**
     * 导出
     * */
    handleExport() {

    }
  }
}
</script>

<style lang='scss' scoped>
  .checkbox {
    padding: 0 20px;
    h4{
      margin: 0 0 15px 0;
    }
    .el-checkbox {
      width: 50%;
      margin: 0 0 20px 0;
      >>>.el-checkbox__label {
        display: inline-flex;
      }
    }
    .width-50-percent {
      width: 50%;
      float: left;
    }
  }
  .sensitiveness-report .data-item .underline {
    text-decoration: none;
  }
</style>
