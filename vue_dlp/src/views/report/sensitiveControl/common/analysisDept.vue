<template>
  <!--部门分析-->
  <div class="data-details sensitiveness-report">
    <div class="data-analysis-table table-parent-size">
      <div class="table-container table-left-size">
        <!--左侧列表查询条件-->
        <rough-query ref="roughQuery" :input-label="this.$t('pages.deptName')" :select-label="selectLabel" :select-map="selectMap"/>
        <grid-table
          ref="roughList"
          :col-model="colModel"
          :row-data-api="getData"
          :multi-select="false"
          :autoload="false"
          @row-click="handleRowClick"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--部门分析上方几个小方块-->
      <div class="data-panel">
        <div class="data-item data-item-details" >
          <div class="icon">
            <svg-icon icon-class="algorithm"></svg-icon>
          </div>
          <div class="content">
            <label :title="temp.item.deptName">{{ temp.item.deptName }}</label>
            <span>{{ temp.item.terminalUserCount }} {{ unit }}</span>
          </div>
        </div>
        <div class="data-item data-item-details" >
          <div class="icon">
            <svg-icon icon-class="ruleGroup"></svg-icon>
          </div>
          <div class="content">
            <label :title="this.$t('table.' + specificCol[0].label)">{{ this.$t('table.' + specificCol[0].label) }}</label>
            <span :title="temp.item.statisticalCount">{{ temp.item.statisticalCount }}</span>
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 165px); padding: 0 9px;position: relative">
        <!--右侧列表查询条件-->
        <detail-query ref="detailQuery" :select-list="selectList" :input-list="inputList" :is-show-grid="isShowGrid"/>
        <el-tabs v-model="activeTab" @tab-click="tabClick">
          <el-tab-pane v-if="drillDown" :label="detailTitle" name="detail" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                ref="detailList"
                :show="isShowGrid"
                :col-model="detailColModel"
                :row-data-api="getDetailData"
                :autoload="false"
                :multi-select="false"
                row-key="getRowKey"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane :label="chartTitle" name="deptStatistics">
            <pie-chart
              v-if="!isShowGrid && 'pie' === chartType"
              :chart-data="temp.deptAnalysisChartData"
              :chart-option="temp.deptAnalysisChartOption"
              style="width: 100%;height: 100%"
            />
            <bar-chart
              v-if="!isShowGrid && 'bar' === chartType"
              :chart-data="temp.deptAnalysisChartData"
              :chart-option="temp.deptAnalysisChartOption"
              :x-axis-name="''"
              style="width: 100%;height: 100%"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import PieChart from '@/components/ECharts/PieChart'
import BarChart from '@/components/ECharts/BarChart'
import { getDetailPage, checkDetailAble } from '@/api/report/baseReport/issueReport/issue'
import moment from 'moment'
import { deepClone } from '@/utils'
import RoughQuery from '@/views/report/sensitiveControl/common/roughQuery'
import DetailQuery from '@/views/report/sensitiveControl/common/detailQuery'

export default {
  name: 'AnalysisDept',
  components: { DetailQuery, RoughQuery, PieChart, BarChart },
  props: {
    dialogStatus: { // 状态，区分终端还是操作员
      type: String,
      default() {
        return ''
      }
    },
    chartType: {
      type: String,
      default() {
        return 'pie'
      }
    },
    chartTitle: {
      type: String,
      default() {
        return ''
      }
    },
    detailTitle: {
      type: String,
      default() {
        return this.$t('pages.violationRecord')
      }
    },
    detailTable: {
      type: String,
      default: ''
    },
    deptApi: {
      type: Function,
      default: function() {}
    },
    deptAnalysisApi: {
      type: Function,
      default: function() {}
    },
    notNullFields: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    },
    specificCol: {
      type: Array,
      default() {
        return []
      }
    },
    specificDetailCol: {
      type: Array,
      default() {
        return []
      }
    },
    selectList: {
      type: Array,
      default: () => []
    },
    inputList: {
      type: Array,
      default: () => []
    },
    selectLabel: {
      type: String,
      default: ''
    },
    selectMap: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      activeTab: '',
      isShowGrid: this.drillDown,
      baseCol: [
        { prop: 'group_name', label: 'dept' }
      ],
      baseDetailCol: [
        { prop: 'create_time', label: 'operateTime', width: '110' },
        { prop: 'terminal_name', label: 'terminalName', hidden: () => this.colHidden(['user']) },
        { prop: 'term_group_name', label: 'terminalGroup', hidden: () => this.colHidden(['user']) },
        { prop: 'user_name', label: 'user', hidden: () => this.colHidden(['terminal']) },
        { prop: 'user_group_name', label: 'userGroup', hidden: () => this.colHidden(['terminal']) }
      ],
      query: {
        page: 1,
        groupType: 1
      },
      detailQuery: {
        page: 1,
        groupType: 1,
        detailTable: this.detailTable,
        objectNames: [],
        notNullFields: this.notNullFields,
        filterGroup: false
      },
      analysisQuery: {
        groupType: 1,
        labelValue: '',
        label: ''
      },
      temp: {},
      defaultTemp: {
        item: {
          deptName: '',
          terminalUserCount: 0,
          statisticalCount: 0
        },
        deptAnalysisChartData: [],
        deptAnalysisChartOption: {}
      }
    }
  },
  computed: {
    colModel() {
      return this.baseCol.concat(this.specificCol)
    },
    detailColModel() {
      return this.baseDetailCol.concat(this.specificDetailCol)
    },
    unit() {
      return this.dialogStatus === 'user' ? '人' : '台'
    }
  },
  created() {
    this.activeTab = this.drillDown ? 'detail' : 'deptStatistics'
    this.resetTemp()
  },
  async mounted() {
    await this.$refs['roughList'].execRowDataApi()
    const tableData = this.$refs['roughList'].getDatas()
    // 如果左侧列表长度不为0，初始化页面时显示第一条数据的详情
    if (tableData[0]) {
      this.handleRowClick(tableData[0])
    }
  },
  methods: {
    // 查询违规数据
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$refs.roughQuery.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return this.deptApi(searchQuery)
    },
    // 查询违规明细数据
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.detailQuery, this.$refs.detailQuery.query, this.$parent.tempQuery, option)
      return getDetailPage(searchQuery)
    },
    // 点击左侧表格
    handleRowClick(rowData) {
      // 设置生效的tab
      if (this.drillDown) {
        this.isShowGrid = true
        this.activeTab = 'detail'
      }
      // 查询分析数据
      const groupId = rowData.group_id
      const groupName = rowData.group_name
      this.analysisQuery.labelValue = groupId
      this.analysisQuery.label = this.dealUnknown(groupName)
      const searchQuery = Object.assign({}, this.analysisQuery, this.$parent.tempQuery)
      this.deptAnalysisApi(searchQuery).then(resp => {
        this.temp.item.deptName = groupName
        this.temp.item.terminalUserCount = resp.data.itemValueMap.terminalUserCount
        this.temp.item.statisticalCount = rowData[this.specificCol[0].prop]

        if ('pie' === this.chartType) {
          this.temp.deptAnalysisChartData = resp.data.chartDataObjMap.deptAnalysisChartData.chartData
        } else if ('bar' === this.chartType) {
          this.temp.deptAnalysisChartData = resp.data.chartDataObjMap.deptAnalysisChartData.chartData
          this.temp.deptAnalysisChartOption.xAxis.data = resp.data.chartDataObjMap.deptAnalysisChartData.xaxisData
          this.temp.deptAnalysisChartOption.series[0].data = resp.data.chartDataObjMap.deptAnalysisChartData.seriesData
        }
      })
      // 查询违规明细数据
      if (this.drillDown) {
        checkDetailAble(this.$parent.tempQuery).then(() => {
          this.detailQuery.objectNames = []
          this.detailQuery.objectNames.push(groupId + this.dealUnknown(groupName))
          this.$refs['detailList'].execRowDataApi({ page: 1 })
        })
      }
    },
    resetTemp() {
      this.defaultTemp.deptAnalysisChartOption = this.getDeptAnalysisChartOption()
      this.temp = deepClone(this.defaultTemp)
    },
    getDeptAnalysisChartOption() {
      if ('pie' === this.chartType) {
        return {
          title: {
            text: this.chartTitle
          },
          toolbox: {
            show: false
          },
          legend: {
            left: 'center'
          },
          series: [
            {
              radius: '30%'
            }
          ]
        }
      } else if ('bar' === this.chartType) {
        return {
          title: {
            text: this.chartTitle
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0,
              rotate: -25
            }
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      }
    },
    /**
     * 根据终端还是操作员控制表格列的显示隐藏
     * */
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    /**
     * tab点击事件，如果不写，切换时图表只有小小一点点
     * @param pane
     * @param event
     */
    tabClick(pane, event) {
      this.isShowGrid = pane.name === 'detail'
    },
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    },
    /**
     * 当值是“未知”时进行特殊处理（因为报表数据库中存储的值是“未知”，所以在查询时需要进行转换）
     * @param value
     * @returns {string|*}
     */
    dealUnknown(value) {
      if (value === this.$t('text.unknown')) {
        return '未知'
      }

      return value
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
