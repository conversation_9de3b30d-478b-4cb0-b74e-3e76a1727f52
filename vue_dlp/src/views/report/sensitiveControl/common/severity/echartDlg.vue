<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      v-zoom-dialog
      :fullscreen="fullscreen"
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <div class="sensitive-control-echartDlg">
        <div class="toolbar" style="height: 30px;">
          <div style="float: right;">
            <el-select v-model="top" filterable placeholder="请选择" @change="changeValue">
              <el-option :value="5" :label="$t('pages.topN', { num: 5 })"></el-option>
              <el-option :value="10" :label="$t('pages.topN', { num: 10 })"></el-option>
              <el-option :value="20" :label="$t('pages.topN', { num: 20 })"></el-option>
              <el-option :value="30" :label="$t('pages.topN', { num: 30 })"></el-option>
              <el-option :value="40" :label="$t('pages.topN', { num: 40 })"></el-option>
            </el-select>
          </div>
        </div>
        <div v-if="dialogStatus === 'dept'" :style="{height:tableHeight}">
          <bar-chart
            ref="chart1"
            :chart-data="temp.deptBarChartData"
            :chart-option="temp.deptBarChartOption"
            :x-axis-name="this.$t('pages.dept')"
            style="width: 100%;height: 100%"
          />
        </div>
        <div v-else :style="{height:tableHeight}">
          <bar-chart
            ref="chart2"
            :chart-data="temp.terminalUserBarChartData"
            :chart-option="temp.terminalUserBarChartOption"
            :x-axis-name="xAxisName"
            style="width: 100%;height: 100%"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart'

export default {
  name: 'EchartDlg',
  components: { BarChart },
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    },
    deptChartApi: {
      type: Function,
      default: function() {}
    },
    terminalUserChartApi: {
      type: Function,
      default: function() {}
    },
    xAxisName: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      dialogStatus: '',
      terminalUserTitle: '',
      top: 10,
      temp: {
        deptBarChartData: [],
        deptBarChartOption: {},
        terminalUserBarChartData: [],
        terminalUserBarChartOption: {}
      }
    }
  },
  computed: {
    tableHeight() {
      return this.fullscreen ? '100%' : '400px'
    }
  },
  watch: {
    fullscreen() {
      this.$nextTick(() => {
        if (this.dialogStatus === 'dept') {
          this.$refs.chart1.chart.resize()
        } else {
          this.$refs.chart2.chart.resize()
        }
      })
    }
  },
  created() {
    this.temp.deptBarChartOption = this.$parent.getDeptBarChartOption(this.top)
    this.temp.terminalUserBarChartOption = this.$parent.getTerminalUserBarChartOption(this.top)
  },
  methods: {
    show(dialogStatus, title) {
      this.dialogStatus = dialogStatus
      this.terminalUserTitle = title
      this.top = 10
      this.dialogVisible = true
      this.loadData()
    },
    changeValue() {
      this.loadData()
    },
    loadData() {
      if (this.dialogStatus === 'dept') {
        this.temp.deptBarChartOption.title.subtext = this.$t('pages.topN', { num: this.top })
        this.deptChartApi(Object.assign({}, this.$parent.tempQuery, { recordSize: this.top })).then(resp => {
          if (resp.data) {
            this.temp.deptBarChartOption.xAxis.data = resp.data.xaxisData
            this.temp.deptBarChartOption.series = this.$parent.buildDeptBarChartSeries(resp.data.seriesData)
          } else {
            this.temp.deptBarChartOption.xAxis.data = []
            this.temp.deptBarChartOption.series = []
          }
        })
      } else {
        this.temp.terminalUserBarChartOption.title.text = this.terminalUserTitle
        this.temp.terminalUserBarChartOption.title.subtext = this.$t('pages.topN', { num: this.top })
        this.terminalUserChartApi(Object.assign({}, this.$parent.tempQuery, { recordSize: this.top })).then(resp => {
          if (resp.data) {
            this.temp.terminalUserBarChartOption.xAxis.data = resp.data.xaxisData
            this.temp.terminalUserBarChartOption.series = this.$parent.buildTerminalUserBarChartSeries(resp.data.seriesData)
          } else {
            this.temp.terminalUserBarChartOption.xAxis.data = []
            this.temp.terminalUserBarChartOption.series = []
          }
        })
      }
    }
  }
}
</script>
