<template>
  <!--数量趋势-->
  <div style="height: 100%">
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="24">
        <line-chart
          :chart-data="temp.severityTrendLineChartData"
          :chart-option="temp.severityTrendLineChartOption"
          style="height: 300px"
        />
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12">
        <pie-chart
          :chart-data="temp.severityPieChartData"
          :chart-option="temp.severityPieChartOption"
          style="height: 300px"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
        <bar-chart
          :chart-data="temp.severityBarChartData"
          :chart-option="temp.severityBarChartOption"
          :x-axis-name="''"
          style="height: 300px"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import PieChart from '@/components/ECharts/PieChart'
import BarChart from '@/components/ECharts/BarChart'
export default {
  name: 'AnalysisTendency',
  components: { LineChart, PieChart, BarChart },
  props: {
    trendAnalysisApi: {
      type: Function,
      default: function() {}
    }
  },
  data() {
    return {
      temp: {},
      defaultTemp: {
        // 严重程度折线图
        severityTrendLineChartData: [],
        severityTrendLineChartOption: {
          title: {
            text: this.$t('pages.violationCountTrend')
          },
          legend: {
            type: 'plain',
            top: 40,
            data: []
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          dataZoom: [
            {
              textStyle: {
                color: '#027AB6'
              },
              height: 20, // 时间滚动条的高度
              type: 'slider', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0,  // 默认开始位置（百分比）
              end: 100  // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          series: []
        },
        // 严重程度饼图
        severityPieChartData: [],
        severityPieChartOption: {
          title: {
            text: this.$t('pages.severityStatistical')
          },
          toolbox: {
            show: false
          },
          series: [
            {
              radius: '30%'
            }
          ]
        },
        // 严重程度柱状统计图
        severityBarChartData: [],
        severityBarChartOption: {
          title: {
            text: this.$t('pages.severityStatistical')
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            },
            {
              data: [],
              type: 'line'
            }
          ]
        }
      }
    }
  },
  created() {
    this.resetTemp()
    this.getData()
  },
  methods: {
    resetTemp() {
      this.temp = { ...this.defaultTemp }
    },
    /**
     * 获取数据信息
     * */
    getData() {
      return this.trendAnalysisApi(this.$parent.tempQuery).then(resp => {
        // console.log('resp', JSON.parse(JSON.stringify(resp.data)))
        if (resp.data) {
          this.temp.severityTrendLineChartOption.legend.data = resp.data.chartDataObjMap.severityTrendLineCharData.legendData
          this.temp.severityTrendLineChartOption.xAxis.data = resp.data.chartDataObjMap.severityTrendLineCharData.xaxisData
          this.temp.severityTrendLineChartOption.series = resp.data.chartDataObjMap.severityTrendLineCharData.seriesData

          this.temp.severityPieChartData = resp.data.chartDataObjMap.severityPieChartData.chartData

          this.temp.severityBarChartData = resp.data.chartDataObjMap.severityBarChartData.chartData
          this.temp.severityBarChartOption.xAxis.data = resp.data.chartDataObjMap.severityBarChartData.xaxisData
          this.temp.severityBarChartOption.series[0].data = resp.data.chartDataObjMap.severityBarChartData.seriesData
          // this.temp.severityBarChartOption.series[1].data = resp.data.chartDataObjMap.severityBarChartData.seriesData
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
