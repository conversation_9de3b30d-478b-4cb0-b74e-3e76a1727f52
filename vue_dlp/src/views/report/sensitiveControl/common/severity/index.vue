<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query">
        <i class="el-icon-s-home" style="position: absolute; left: 24px; top: 16px;"></i>
        <!--面包屑-->
        <div style="width: 510px; float: left;">
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-tooltip effect="dark" placement="bottom-start" style="margin-left: 10px">
              <span slot="content">
                {{ tip }}
              </span>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <!--上方搜索条件-->
        <div style="float: right">
          <!--查询条件组件-->
          <query ref="queryData"/>
          <!--图表解读-->
          <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
          <!-- <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
          <el-button type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>-->
        </div>
      </div>

      <!-- 页面无数据 -->
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>

      <!--分析初始页面-->
      <div v-if="inceptionDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in type === 'sporadic' ? itemOption : itemOption.slice(1)" :key="key" class="data-item" >
            <div class="icon">
              <svg-icon :icon-class="item.icon"></svg-icon>
            </div>
            <div class="content">
              <label :title="item.label">{{ item.label }}</label>
              <el-button type="text" :class="{'underline': !item.clickable }" @click="item.func">{{ temp.itemValue[item.key] }}</el-button>
            </div>
          </div>
        </div>
        <!--中间图表-->
        <div class="chart-panel">
          <div class="chart-item flex-2">
            <div class="mini-title">
            </div>
            <!-- 柱状图 -->
            <bar-chart
              ref="severityModeBarChart"
              :chart-data="temp.severityBarChartData"
              :chart-option="temp.severityBarChartOption"
              :x-axis-name="''"
              :click="barChartFun"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.severityPie.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title">
                解读：<span style="color: #909399;">通过各违规类型的触发频次对比，识别高频违规类型，优化检测策略，提升规则覆盖率和精准度。</span>
              </div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.severityPie + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1 col-1">
            <div class="mini-title">
            </div>
            <!--饼图-->
            <pie-chart
              ref="severityModePieChart"
              :chart-data="temp.severityPieChartData"
              :chart-option="temp.severityPieChartOption"
              class="flex-2"
            />
          </div>
        </div>
        <!--下方对部门，终端（操作员），数量趋势的分别展示部分-->
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleDeptAnalysis">{{ $t('pages.deptSeverityAnalysis') }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigDept"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.deptBarChartData"
              :chart-option="temp.deptBarChartOption"
              :x-axis-name="this.$t('pages.dept')"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.deptDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.deptDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title">
              <span @click="handleTerminalUserAnalysis">{{ terminalUserAnalysisTitle }}>></span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">{{ this.$t('components.seeMore') }}</div>
                <svg-icon icon-class="big" aria-hidden="false" class="big-svg" @click="handleBigTerminalUser"/>
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.terminalUserBarChartData"
              :chart-option="temp.terminalUserBarChartOption"
              :x-axis-name="temp.terminalUserXName"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.termOrUserDistribute.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.termOrUserDistribute + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <div class="mini-title"><span @click="handleViolateTrendAnalysis">{{ $t('pages.violationCountTrendAnalysis') }}>></span></div>
            <line-chart
              :chart-data="temp.trendLineChartData"
              :chart-option="temp.trendLineChartOption"
              class="flex-2"
            />
            <div v-if="temp.chartInterpretationContent.violationNum.length > 0" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + temp.chartInterpretationContent.violationNum + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
      <!--部门分析-->
      <div v-if="deptDiv" class="detailsDiv">
        <analysis-dept
          :dialog-status="dialogStatus"
          :chart-title="this.$t('pages.severityStatistical')"
          :detail-table="detailTable"
          :dept-api="deptApi"
          :dept-analysis-api="deptAnalysisApi"
          :select-label="this.$t('pages.severity')"
          :select-map="severityMap"
          :drill-down="drillDown"
          :specific-col="specificCol"
          :specific-detail-col="specificDetailCol"
          :select-list="selectList"
          :input-list="inputList"
        />
      </div>
      <!--终端、操作员分析-->
      <div v-if="userDiv" class="detailsDiv">
        <analysis-terminal-user
          :dialog-status="dialogStatus"
          :chart-title="this.$t('pages.severityStatistical')"
          :detail-table="detailTable"
          :terminal-user-api="terminalUserApi"
          :terminal-user-analysis-api="terminalUserAnalysisApi"
          :select-label="this.$t('pages.severity')"
          :select-map="severityMap"
          :drill-down="drillDown"
          :specific-col="specificCol"
          :specific-detail-col="specificDetailCol"
          :select-list="selectList"
          :input-list="inputList"
        />
      </div>
      <!--违规数量趋势分析-->
      <div v-if="tendencyDiv" class="detailsDiv">
        <analysis-tendency :trend-analysis-api="trendAnalysisApi"/>
      </div>
    </div>
    <!--小方块（违规总数）点击弹框-->
    <violate-detail-dlg ref="totalViolationDlg" :dlg-title="this.$t('pages.violationTotal')" :detail-table="detailTable" :specific-col="specificDetailCol"/>
    <!--小方块（部门）点击弹框-->
    <dept-dlg ref="deptDlg" :data-api="deptApi" :specific-col="specificCol"/>
    <!--小方块点（终端）击弹框-->
    <terminal-user-dlg ref="terminalUserDlg" :title="terminalUserDlgTitle" :data-api="terminalUserApi" :specific-col="specificCol"/>
    <!--严重程度（横向柱状统计图）点击弹框-->
    <violate-detail-dlg ref="severityDlg" :dlg-title="severityTitle" :detail-table="detailTable" :specific-col="specificDetailCol.slice(1)" :search-condition="searchCondition"/>
    <!--    部门、终端、操作员前五名小图标点击弹框-->
    <echart-dlg ref="echartDlg" :title="echartDlgTitle" :dept-chart-api="deptChartApi" :terminal-user-chart-api="terminalUserChartApi" :x-axis-name="temp.terminalUserXName"/>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import BarChart from '@/components/ECharts/BarChart'
import PieChart from '@/components/ECharts/PieChart'
import Query from '@/views/report/sensitiveControl/common/query'
import DeptDlg from '@/views/report/sensitiveControl/common/dlgDept'
import EchartDlg from '@/views/report/sensitiveControl/common/severity/echartDlg'
import TerminalUserDlg from '@/views/report/sensitiveControl/common/dlgTerminalUser'
import ViolateDetailDlg from '@/views/report/sensitiveControl/common/dlgViolateDetail'
import analysisDept from '@/views/report/sensitiveControl/common/analysisDept'
import analysisTerminalUser from '@/views/report/sensitiveControl/common/analysisTerminalUser'
import analysisTendency from '@/views/report/sensitiveControl/common/severity/analysisTendency'
import { deepClone } from '@/utils'

export default {
  name: 'Severity',
  components: { Query, BarChart, LineChart, PieChart, DeptDlg, EchartDlg, TerminalUserDlg, ViolateDetailDlg, analysisDept, analysisTerminalUser, analysisTendency },
  props: {
    tip: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    detailTable: {
      type: String,
      default: ''
    },
    homepageDataApi: {
      type: Function,
      default: function() {
      }
    },
    deptChartApi: {
      type: Function,
      default: function() {}
    },
    terminalUserChartApi: {
      type: Function,
      default: function() {}
    },
    deptApi: {
      type: Function,
      default: function() {
      }
    },
    terminalUserApi: {
      type: Function,
      default: function() {
      }
    },
    deptAnalysisApi: {
      type: Function,
      default: function() {
      }
    },
    terminalUserAnalysisApi: {
      type: Function,
      default: function() {
      }
    },
    trendAnalysisApi: {
      type: Function,
      default: function() {
      }
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 搜查查询
      query: {
        // 是否展示图表解读
        isExplain: true,
        dimBaseType: 1,
        dimValue: '',
        countByObject: 2
      },
      tempQuery: {},
      terminalUserAnalysisTitle: this.$t('pages.terminalSeverityAnalysis'),
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: this.$t('pages.severityAnalysis') }
      ],
      // 暂无数据
      noData: true,
      // 初始页面显示隐藏
      inceptionDiv: false,
      // 操作员分析显示隐藏
      userDiv: false,
      // 部门分析显示隐藏
      deptDiv: false,
      // 趋势分析显示隐藏
      tendencyDiv: false,
      // 弹框名称
      severityTitle: '',
      // 违规终端/操作员弹窗名称
      terminalUserDlgTitle: '',
      // 放大柱状图弹框名称
      echartDlgTitle: '',
      echartStatus: 'dept',
      // 弹框状态
      dialogStatus: '',
      // 上方小方块
      itemOption: [
        { label: '触发次数', key: 'trigger', icon: 'total', func: this.triggerFun, clickable: false },
        { label: this.$t('pages.violationTotal'), key: 'all', icon: 'total', func: this.totalViolationFun, clickable: this.drillDown },
        { label: this.$t('pages.dept'), key: 'dept', icon: 'tree', func: this.deptFun, clickable: true },
        { label: this.$t('pages.violationTerminal'), key: 'terminalUser', icon: 'terminal', func: this.terminalUserFun, clickable: true }
      ],
      commonInterpretation: `<span>图表解读：</span>`,
      temp: {},
      defaultTemp: {
        // 图表解读
        chartInterpretationContent: {
          // 严重程度（饼图）
          severityPie: '',
          // 严重程度（柱状图）
          severityBar: '',
          // 部门严重程度
          deptDistribute: '',
          // 终端/操作员严重程度
          termOrUserDistribute: '',
          // 违规数量分析
          violationNum: ''
        },
        itemValue: {
          trigger: '0',
          all: '0',
          dept: '0',
          terminalUser: '0'
        },
        // 饼图 严重程度占比分析
        severityPieChartData: [],
        severityPieChartOption: {
          title: {
            text: this.$t('pages.severityStatistical')
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%'
            }
          ]
        },
        // 严重程度柱状统计图
        severityBarChartData: [],
        severityBarChartOption: {
          title: {
            text: this.$t('pages.severityStatistical')
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          yAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 部门严重程度数量柱状图数据
        deptBarChartData: [],
        deptBarChartOption: {},
        // 终端/操作员严重程度数量图
        terminalUserBarChartData: [],
        terminalUserBarChartOption: {},
        terminalUserXName: '',
        // 折线图数据 违规数量趋势分析
        trendLineChartData: [],
        trendLineChartOption: {
          title: {
            text: this.$t('pages.violationCountTrend')
          },
          toolbox: {
            show: false
          },
          grid: {
            top: '25%',
            width: '85%',
            bottom: 20,
            left: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        }
      },
      severityMap: {
        4: this.$t('pages.severityOptions4'),
        3: this.$t('pages.severityOptions3'),
        2: this.$t('pages.severityOptions2'),
        1: this.$t('pages.severityOptions1')
      },
      specificDetailCol: [
        { prop: 'severity', label: 'severity', formatter: (row, data) => { return this.severityMap[data] } },
        { prop: 'file_name', label: 'sensitiveFile', formatter: this.fileFormatter },
        { prop: 'stg_def_name', label: 'contentStg' }
      ],
      searchCondition: {
        severity: ''
      },
      specificCol: [
        { prop: 'violate_count', label: 'violationCount', sort: true },
        { prop: 'severityStr', label: 'severity', formatter: (row, data) => { return data.replace(/^,|,$/g, '').split(',').map(item => this.severityMap[item]).join(',') } }
      ],
      // 明细数据输入框搜索条件
      inputList: [
        {
          inputLabel: this.$t('table.sensitiveFile'),
          inputKey: 'file_name'
        },
        {
          inputLabel: this.$t('table.contentStg'),
          inputKey: 'stg_def_name'
        }
      ]
    }
  },
  computed: {
    // 明细数据下拉框搜索条件
    selectList() {
      return [
        {
          // 标签名
          selectLabel: this.$t('pages.severity'),
          // 绑定字段名
          selectKey: 'severity',
          // 下拉选项
          selectMap: this.severityMap
        }
      ]
    }
  },
  watch: {
  },
  created() {
    this.resetTemp()
  },
  methods: {
    fileFormatter(row) {
      return row.file_name + (row.child_file_path ? row.child_file_path : '')
    },
    resetTemp() {
      this.defaultTemp.deptBarChartOption = this.getDeptBarChartOption(5)
      this.defaultTemp.terminalUserBarChartOption = this.getTerminalUserBarChartOption(5)
      this.defaultTemp.terminalUserBarChartOption.title.text = this.query.countByObject === 1 ? this.$t('pages.userSeverityStatistical') : this.$t('pages.terminalSeverityStatistical')
      this.temp = deepClone(this.defaultTemp)
    },
    /**
     * 获取数据信息
     * */
    getData() {
      this.query = this.$refs['queryData'].getQuery()
      this.tempQuery = Object.assign({}, this.query)
      this.temp = JSON.parse(JSON.stringify(this.temp))
      return this.homepageDataApi(this.query).then(resp => {
        if (resp.data) {
          this.inceptionDiv = true
          this.noData = false
          // 图表解读
          if (resp.data.itemValueMap.explanation) {
            this.temp.chartInterpretationContent = { ...this.temp.chartInterpretationContent, ...resp.data.itemValueMap.explanation }
          } else {
            this.temp.chartInterpretationContent = {
              // 严重程度（饼图）
              severityPie: '',
              // 严重程度（柱状图）
              severityBar: '',
              // 部门严重程度
              deptDistribute: '',
              // 终端/操作员严重程度
              termOrUserDistribute: '',
              // 违规数量分析
              violationNum: ''
            }
          }
          if ('sporadic' === this.type) {
            this.temp.itemValue.trigger = resp.data.itemValueMap.logCount
          }
          this.temp.itemValue.all = resp.data.itemValueMap.violateTotal
          this.temp.itemValue.dept = resp.data.itemValueMap.deptCount
          this.temp.itemValue.terminalUser = resp.data.itemValueMap.terminalUserCount

          this.temp.severityPieChartData = resp.data.chartDataObjMap.severityPieChartData.chartData

          this.temp.severityBarChartData = resp.data.chartDataObjMap.severityBarChartData.chartData
          this.temp.severityBarChartOption.yAxis.data = resp.data.chartDataObjMap.severityBarChartData.xaxisData
          this.temp.severityBarChartOption.series[0].data = resp.data.chartDataObjMap.severityBarChartData.seriesData

          this.temp.deptBarChartOption.xAxis.data = resp.data.chartDataObjMap.deptBarChartData.xaxisData
          this.temp.deptBarChartOption.series = this.buildDeptBarChartSeries(resp.data.chartDataObjMap.deptBarChartData.seriesData)

          this.temp.terminalUserBarChartOption.xAxis.data = resp.data.chartDataObjMap.terminalUserBarChartData.xaxisData
          this.temp.terminalUserBarChartOption.series = this.buildTerminalUserBarChartSeries(resp.data.chartDataObjMap.terminalUserBarChartData.seriesData)
          this.temp.terminalUserXName = this.query.countByObject === 1 ? this.$t('components.user') : this.$t('components.terminal')

          this.temp.trendLineChartData = resp.data.chartDataObjMap.violateCountTrendLineCharData.chartData
          this.temp.trendLineChartOption.xAxis.data = resp.data.chartDataObjMap.violateCountTrendLineCharData.xaxisData
          this.temp.trendLineChartOption.series[0].data = resp.data.chartDataObjMap.violateCountTrendLineCharData.seriesData
        } else {
          this.resetTemp()
          this.inceptionDiv = false
          this.noData = true
        }
      }).then(() => {
        this.triggerResize()
      })
    },
    /**
     * 搜索按钮点击（暂时原型主要是对统计类型变化时页面上显示变化的一个处理）
     * */
    loadReport() {
      this.getData()
      if (this.query.countByObject === 1) {
        // 操作员
        Object.assign(this.itemOption[this.itemOption.length - 1], { label: this.$t('pages.violationUser'), icon: 'user' })
        this.terminalUserAnalysisTitle = this.$t('pages.userSeverityAnalysis')
        this.temp.terminalUserBarChartOption.title.text = this.$t('pages.userSeverityStatistical')
      } else {
        // 终端
        Object.assign(this.itemOption[this.itemOption.length - 1], { label: this.$t('pages.violationTerminal'), icon: 'terminal' })
        this.terminalUserAnalysisTitle = this.$t('pages.terminalSeverityAnalysis')
        this.temp.terminalUserBarChartOption.title.text = this.$t('pages.terminalSeverityStatistical')
      }
      // 初始页面（点击搜索的时候要回到初始页面）
      // this.inceptionDiv = true
      // 操作员（终端）页面
      this.userDiv = false
      // 部门页面
      this.deptDiv = false
      // 趋势页面
      this.tendencyDiv = false
      this.showFilePath = [{ id: 0, label: this.$t('pages.severityAnalysis') }]
    },
    buildDeptBarChartSeries(data) {
      return data.map(item => {
        return { ...item, type: 'bar', barGap: 0, emphasis: { focus: 'series' }}
      })
    },
    buildTerminalUserBarChartSeries(data) {
      return data.map(item => {
        return { ...item, type: 'bar', stack: 'severity', emphasis: { focus: 'series' }}
      })
    },
    getDeptBarChartOption(top) {
      return {
        title: {
          text: this.$t('pages.deptSeverityStatistical'),
          subtext: this.$t('pages.topN', { num: top })
        },
        toolbox: {
          show: false
        },
        grid: {
          top: 60,
          width: '85%',
          bottom: 20,
          left: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          splitArea: {
            show: false
          }
        },
        series: []
      }
    },
    getTerminalUserBarChartOption(top) {
      return {
        title: {
          text: '',
          subtext: this.$t('pages.topN', { num: top })
        },
        toolbox: {
          show: false
        },
        grid: {
          top: '25%',
          width: '85%',
          bottom: 20,
          left: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          splitArea: {
            show: false
          }
        },
        series: []
      }
    },
    /**
     * 触发次数（没有点击效果，但是因为遍历，所以还是要加一个点击方法）
     * */
    triggerFun() {

    },
    /**
     * 违规总数(点击)
     */
    totalViolationFun() {
      if (this.drillDown) {
        this.$refs.totalViolationDlg.show()
      }
    },
    /**
     * 部门（点击）
     */
    deptFun() {
      this.$refs.deptDlg.show()
    },
    /**
     * 终端或操作员（点击）
     */
    terminalUserFun() {
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.terminalUserDlgTitle = this.$t('pages.violationUser')
      } else {
        // 终端
        this.terminalUserDlgTitle = this.$t('pages.violationTerminal')
      }
      this.$refs.terminalUserDlg.show()
    },
    /**
     * 严重程度横向柱状图
     * 柱状图点击，列表弹框
     * */
    barChartFun(params) {
      if (this.drillDown && params.name) {
        this.severityTitle = params.name
        this.searchCondition.severity = Object.keys(this.severityMap).find(k => this.severityMap[k] === params.name)
        this.$refs.severityDlg.show()
      }
    },
    /**
     * 部门分析小图标点击
     * */
    handleBigDept() {
      this.echartDlgTitle = this.$t('pages.deptSeverityStatistical')
      this.echartStatus = 'dept'
      this.$refs.echartDlg.show(this.echartStatus)
    },
    /**
     * 终端操作员小图标点击
     * */
    handleBigTerminalUser() {
      if (this.tempQuery.countByObject === 2) {
        this.echartDlgTitle = this.$t('pages.terminalSeverityStatistical')
        this.echartStatus = 'terminal'
      } else {
        this.echartDlgTitle = this.$t('pages.userSeverityStatistical')
        this.echartStatus = 'user'
      }
      this.$refs.echartDlg.show(this.echartStatus, this.echartDlgTitle)
    },
    /**
     * 终端（操作员）分析点击
     * 显示终端（操作员）分析，面包屑中添加终端（操作员）分析
     * */
    handleTerminalUserAnalysis() {
      this.inceptionDiv = false
      this.userDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
        this.showFilePath.push({ id: 1, label: this.$t('pages.userSeverityAnalysis') })
      } else {
        // 终端
        this.dialogStatus = 'terminal'
        this.showFilePath.push({ id: 1, label: this.$t('pages.terminalSeverityAnalysis') })
      }
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    handleDeptAnalysis() {
      this.inceptionDiv = false
      this.deptDiv = true
      if (this.tempQuery.countByObject === 1) {
        // 操作员
        this.dialogStatus = 'user'
      } else {
        // 终端
        this.dialogStatus = 'terminal'
      }
      this.showFilePath.push({ id: 2, label: this.$t('pages.deptSeverityAnalysis') })
    },
    /**
     * 数量趋势点击
     * 显示数量趋势，面包屑中添加数量趋势
     * */
    handleViolateTrendAnalysis() {
      this.inceptionDiv = false
      this.tendencyDiv = true
      this.showFilePath.push({ id: 3, label: this.$t('pages.violationCountTrendAnalysis') })
    },
    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        // 初始页面
        this.inceptionDiv = true
        // 操作员（终端）页面
        this.userDiv = false
        // 部门页面
        this.deptDiv = false
        // 趋势页面
        this.tendencyDiv = false
        this.showFilePath = [{ id: 0, label: this.$t('pages.severityAnalysis') }]
      }
      if (index === 1) {
        this.inceptionDiv = false
        this.userDiv = true
        this.deptDiv = false
        this.tendencyDiv = false
      }
      if (index === 2) {
        this.inceptionDiv = false
        this.userDiv = false
        this.deptDiv = true
        this.tendencyDiv = false
      }
      if (index === 3) {
        this.inceptionDiv = false
        this.userDiv = false
        this.deptDiv = false
        this.tendencyDiv = true
      }
    },
    /**
     * 打印
     * */
    handlePrint() {

    },
    /**
     * 导出
     * */
    handleExport() {

    }
  }
}
</script>
<style lang='scss' scoped>
.sensitiveness-report .data-item .underline {
  text-decoration: none;
}
</style>
