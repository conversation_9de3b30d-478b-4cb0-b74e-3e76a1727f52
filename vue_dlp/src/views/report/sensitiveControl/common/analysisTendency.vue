<template>
  <!--数量趋势-->
  <div style="height: 100%">
    <!--数量趋势--上方折线图-->
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="24">
        <line-chart
          :chart-data="temp.trendLineChartData"
          :chart-option="temp.trendLineChartOption"
          style="width: 100%;height: 500px"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
export default {
  name: 'AnalysisTendency',
  components: { LineChart },
  props: {
    chartTitle: {
      type: String,
      default() {
        return ''
      }
    },
    trendAnalysisApi: {
      type: Function,
      default: function() {}
    }
  },
  data() {
    return {
      temp: {},
      defaultTemp: {
        trendLineChartData: [],
        trendLineChartOption: {
          grid: {
            left: '2%',    // 距离左侧的距离
            right: '2%',   // 距离右侧的距离
            top: '5%',    // 距离顶部的距离
            bottom: '30%'  // 距离底部的距离
          },
          title: {
            text: this.chartTitle
          },
          toolbox: {
            show: false
          },
          legend: {
            type: 'plain',
            x: 'center', // 图例水平居中
            bottom: 0,
            data: []
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            splitArea: {
              show: false
            }
          },
          dataZoom: [
            {
              textStyle: {
                color: '#027AB6'
              },
              bottom: 60,
              height: 15, // 时间滚动条的高度
              type: 'slider', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0,  // 默认开始位置（百分比）
              end: 100  // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          series: []
        }
      }
    }
  },
  created() {
    this.resetTemp()
    this.getData()
  },
  methods: {
    resetTemp() {
      this.temp = { ...this.defaultTemp }
    },
    /**
     * 获取数据信息
     * */
    getData() {
      return this.trendAnalysisApi(this.$parent.tempQuery).then(resp => {
        if (resp.data) {
          this.temp.trendLineChartOption.legend.data = resp.data.chartDataObjMap.trendLineCharData.legendData
          this.temp.trendLineChartOption.xAxis.data = resp.data.chartDataObjMap.trendLineCharData.xaxisData
          this.temp.trendLineChartOption.series = resp.data.chartDataObjMap.trendLineCharData.seriesData
          this.temp.trendLineChartOption.grid = {
            top: '10%',
            width: '85%',
            bottom: '15%',
            left: '4%',
            containLabel: true
          }
        }
      })
    }
  }
}
</script>
