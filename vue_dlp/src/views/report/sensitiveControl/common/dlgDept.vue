<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('table.dept')"
      :fullscreen="fullscreen"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <grid-table
        ref="deptTable"
        :stripe="false"
        row-key="id"
        :col-model="colModel"
        :row-data-api="getData"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        default-expand-all
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DlgDept',
  props: {
    specificCol: {
      type: Array,
      default() {
        return []
      }
    },
    dataApi: {
      type: Function,
      default: function() {}
    }
  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      // 表格高度
      dialogVisible: false,
      query: {
        page: 1,
        groupType: 1
      },
      baseCol: [
        { prop: 'group_name', label: 'dept' }
      ]
    }
  },
  computed: {
    colModel() {
      return this.baseCol.concat(this.specificCol)
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  methods: {
    /**
     * 获取数据信息
     * */
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return this.dataApi(searchQuery)
    },
    /**
     * 显示弹框
     */
    show() {
      this.dialogVisible = true
      if (this.$refs['deptTable']) {
        this.$refs['deptTable'].execRowDataApi(this.query)
      }
    }
  }
}
</script>

