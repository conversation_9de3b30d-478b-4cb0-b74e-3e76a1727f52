<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      v-zoom-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :fullscreen="fullscreen"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <grid-table
        ref="terminalUserTable"
        :stripe="false"
        row-key="id"
        :col-model="colModel"
        :row-data-api="getData"
        :indent="0"
        :style="{height:tableHeight}"
        :multi-select="false"
        default-expand-all
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DlgTerminalUser',
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    },
    specificCol: {
      type: Array,
      default() {
        return []
      }
    },
    dataApi: {
      type: Function,
      default: function() {}
    }
  },
  data() {
    return {
      // 弹框是否全屏展示
      fullscreen: false,
      dialogVisible: false,
      query: {
        page: 1,
        groupType: 0
      },
      baseCol: [
        { prop: 'terminal_user_name', headerFormat: () => { return this.$parent.tempQuery.countByObject === 1 ? this.$t('table.user') : this.$t('table.terminalName') } },
        { prop: 'group_name', headerFormat: () => { return this.$parent.tempQuery.countByObject === 1 ? this.$t('table.userGroupName') : this.$t('table.terminalGroup') } }
      ]
    }
  },
  computed: {
    colModel() {
      return this.baseCol.concat(this.specificCol)
    },
    // 表格高度
    tableHeight() {
      return this.fullscreen ? '100%' : '325px'
    }
  },
  methods: {
    /**
     * 获取数据信息
     * */
    getData(option) {
      const searchQuery = Object.assign({}, this.query, this.$parent.tempQuery, option)
      searchQuery.searchCount = true
      return this.dataApi(searchQuery)
    },
    /**
     * 显示弹框
     */
    show() {
      this.dialogVisible = true
      if (this.$refs['terminalUserTable']) {
        this.$refs['terminalUserTable'].execRowDataApi(this.query)
      }
    }
  }
}
</script>
