<template>
  <el-popover placement="bottom" width="350" trigger="click">
    <Form ref="searchLeftForm" label-position="right" :model="query" label-width="80px">
      <FormItem :label="selectLabel">
        <el-select v-model="query.keyword1" clearable :placeholder="$t('pages.all')" style="width: 244px">
          <el-option v-for="(value, key) in selectMap" :key="key" :label="value" :value="key"></el-option>
        </el-select>
      </FormItem>
      <FormItem v-show="!ruleTypeAnalysis" :label="inputLabel">
        <el-input v-model="query.label" v-trim maxlength="" clearable/>
      </FormItem>
    </Form>
    <div style="text-align: right; margin-top: 10px">
      <!--重置、查询按钮-->
      <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
      <el-button type="primary" size="mini" @click="searchData">{{ $t('button.search') }}</el-button>
    </div>
    <el-button slot="reference" type="primary" icon="el-icon-search" class="search-icon-left" size="mini"></el-button>
  </el-popover>
</template>

<script>
export default {
  name: 'RoughQuery',
  props: {
    inputLabel: {
      type: String,
      default: ''
    },
    selectLabel: {
      type: String,
      default: ''
    },
    selectMap: {
      type: Object,
      default: function() {
        return {}
      }
    },
    ruleTypeAnalysis: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      query: {
        keyword1: '',
        label: ''
      }
    }
  },
  methods: {
    resetQuery() {
      this.query.keyword1 = ''
      this.query.label = ''
    },
    async searchData() {
      await this.$parent.$refs['roughList'].execRowDataApi({ page: 1 })
      const tableData = this.$parent.$refs['roughList'].getDatas()
      if (tableData[0]) {
        this.$parent.handleRowClick(tableData[0])
      } else {
        this.$parent.resetTemp()
        if (this.ruleTypeAnalysis) {
          this.$parent.detailQuery.condition.rule_type = -1
        } else {
          this.$parent.detailQuery.objectNames = []
          this.$parent.detailQuery.objectNames.push(-1)
        }
        if (this.$parent.$refs['detailList']) {
          this.$parent.$refs['detailList'].clearRowData()
          this.$parent.$refs['detailList'].clearPageData()
        }
      }
    }
  }
}
</script>
