<template>
  <comparative-trend
    :api="'getWebBrowsingTimeCompareData'"
    :api-dept="'getWebBrowsingTimeCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'WebBrowsingTimeCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '网页浏览时长',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'totalTimeSumAll', label: 'totalTimeSumAll', flag: 'time' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['totalTimeSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
