<template>
  <Trend :api="'getWebBrowsingTimeTrend'" :col-model="colModel" :custom-list="customList" :conversion-time="true" :drill-down="hasPermission('675')" :exportable="hasPermission('676')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { formatSeconds } from '@/utils'
import { getWebBrowsingTimeDetailCol, beforeLoadWebBrowsingDetail } from '@/utils/report'
import { listUrlGroup } from '@/api/system/baseData/urlLibrary'

export default {
  name: 'WebBrowsingTimeTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'totalTimeSumAll', label: 'totalTimeSumAll', width: '150', sort: true, sortOriginal: true, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_web_browsing_time', detailCol: getWebBrowsingTimeDetailCol() }
      ],
      customList: [
        { prop: 'totalTimeSumAll', label: 'totalTimeSumAll', width: '150', sort: true, sortOriginal: true, value: 1, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_web_browsing_time', detailCol: getWebBrowsingTimeDetailCol() }
      ]
    }
  },
  created() {
    this.concatWebsiteTypeCol()
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    concatWebsiteTypeCol() {
      listUrlGroup().then(res => {
        if (res.data) {
          res.data.push({ id: -1, name: 'ungrouped', escapeFormat: false })
          const websiteTypeCol = res.data.map(item => {
            return { prop: 'category_' + item.id, label: item.name, width: '150', flag: 'time', sort: true, escapeFormat: item.escapeFormat !== false, sortOriginal: true, beforeLoadDetail: beforeLoadWebBrowsingDetail, formatter: (row, data) => { return formatSeconds(data) }, detailTable: 'dwd_web_browsing_time', detailCol: getWebBrowsingTimeDetailCol() }
          })

          this.customList = this.customList.concat(websiteTypeCol)
        }
      }).catch(err => {
        console.log(err)
      })
    }
  }
}
</script>
