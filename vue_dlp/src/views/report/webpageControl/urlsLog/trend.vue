<template>
  <Trend :api="'getUrlsTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('665')" :exportable="hasPermission('666')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getWebBrowsingNumDetailCol, beforeLoadWebBrowsingDetail } from '@/utils/report'
import { listUrlGroup } from '@/api/system/baseData/urlLibrary'

export default {
  name: 'UrlsLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'urlNumSumAll', label: 'urlNumSumAll', width: '150', sort: true, detailTable: 'dwd_urls_log', detailCol: getWebBrowsingNumDetailCol() }
      ],
      customList: [
        { prop: 'urlNumSumAll', label: 'urlNumSumAll', width: '150', sort: true, detailTable: 'dwd_urls_log', detailCol: getWebBrowsingNumDetailCol() }
      ]

    }
  },
  created() {
    this.concatWebsiteTypeCol()
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    concatWebsiteTypeCol() {
      listUrlGroup().then(res => {
        if (res.data) {
          res.data.push({ id: -1, name: 'ungrouped', escapeFormat: false })
          const websiteTypeCol = res.data.map(item => {
            return { prop: 'category_' + item.id, label: item.name, width: '150', sort: true, escapeFormat: item.escapeFormat !== false, beforeLoadDetail: beforeLoadWebBrowsingDetail, detailTable: 'dwd_urls_log', detailCol: getWebBrowsingNumDetailCol() }
          })

          this.customList = this.customList.concat(websiteTypeCol)
        }
      }).catch(err => {
        console.log(err)
      })
    }
  }
}
</script>
