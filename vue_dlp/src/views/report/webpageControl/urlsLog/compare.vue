<template>
  <comparative-trend
    :api="'getUrlsLogCompareData'"
    :api-dept="'getUrlsLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'UrlsLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '网页浏览',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'urlNumSumAll', label: 'urlNumSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['urlNumSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
