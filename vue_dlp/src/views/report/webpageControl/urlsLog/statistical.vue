<template>
  <Statistical :api="'getUrlsBar'" :default-col-model="defaultColModel" :custom-list="customList" :drill-down="hasPermission('660')" :exportable="hasPermission('661')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '../../component/Statistical'
import { getWebBrowsingNumDetailCol, beforeLoadWebBrowsingDetail } from '@/utils/report'
import { listUrlGroup } from '@/api/system/baseData/urlLibrary'

export default {
  name: 'UrlsLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'urlNumSumAll', label: 'urlNumSumAll', width: '150', sort: true, detailTable: 'dwd_urls_log', detailCol: getWebBrowsingNumDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'urlNumSumAll', label: 'urlNumSumAll', width: '150', sort: true, detailTable: 'dwd_urls_log', detailCol: getWebBrowsingNumDetailCol() }
      ]
    }
  },
  created() {
    this.concatWebsiteTypeCol()
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
    },
    concatWebsiteTypeCol() {
      listUrlGroup().then(res => {
        if (res.data) {
          res.data.push({ id: -1, name: 'ungrouped', escapeFormat: false })
          const websiteTypeCol = res.data.map(item => {
            return { prop: 'category_' + item.id, label: item.name, width: '150', sort: true, escapeFormat: item.escapeFormat !== false, beforeLoadDetail: beforeLoadWebBrowsingDetail, detailTable: 'dwd_urls_log', detailCol: getWebBrowsingNumDetailCol() }
          })

          this.customList = this.customList.concat(websiteTypeCol)
        }
      }).catch(err => {
        console.log(err)
      })
    }
  }
}
</script>
