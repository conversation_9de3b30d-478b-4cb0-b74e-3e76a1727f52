<template>
  <comparative-trend
    :api="'getForumDataLogCompareData'"
    :api-dept="'getForumDataLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'ForumDataLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '论坛发帖',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'postCountSum', label: 'postCountSum' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['postCountSum']
    }
  }
}
</script>
