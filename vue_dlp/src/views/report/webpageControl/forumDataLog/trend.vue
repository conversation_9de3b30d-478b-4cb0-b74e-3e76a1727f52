<template>
  <Trend :api="'getForumDataLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('885')" :exportable="hasPermission('886')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getForumPostNumDetailCol } from '@/utils/report'

export default {
  name: 'ForumDataLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'postCountSum', label: 'postCountSum', width: '150', sort: true, detailTable: 'dwd_forum_data_log', detailCol: getForumPostNumDetailCol() }
      ],
      customList: [
        { prop: 'postCountSum', label: 'postCountSum', width: '150', sort: true, value: 1, detailTable: 'dwd_forum_data_log', detailCol: getForumPostNumDetailCol() }
      ]

    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
