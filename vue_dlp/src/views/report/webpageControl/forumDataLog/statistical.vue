<template>
  <Statistical :api="'getForumDataLogBar'" :default-col-model="defaultColModel" :custom-list="customList" :drill-down="hasPermission('880')" :exportable="hasPermission('881')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '../../component/Statistical'
import { getForumPostNumDetailCol } from '@/utils/report'

export default {
  name: 'ForumDataLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'postCountSum', label: 'postCountSum', width: '150', sort: true, value: 1, detailTable: 'dwd_forum_data_log', detailCol: getForumPostNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'postCountSum', label: 'postCountSum', width: '150', sort: true, detailTable: 'dwd_forum_data_log', detailCol: getForumPostNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
    }
  }
}
</script>
