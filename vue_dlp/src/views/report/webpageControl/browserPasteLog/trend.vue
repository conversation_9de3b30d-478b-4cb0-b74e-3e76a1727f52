<template>
  <Trend :api="'getBrowserPasteLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('695')" :exportable="hasPermission('696')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getWebPasteNumDetailCol } from '@/utils/report'

export default {
  name: 'BrowserPasteLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'pasteCountSum', label: 'browserPasteSumAll', width: '150', sort: true, detailTable: 'dwd_browser_paste_log', detailCol: getWebPasteNumDetailCol() }
      ],
      customList: [
        { prop: 'pasteCountSum', label: 'browserPasteSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_browser_paste_log', detailCol: getWebPasteNumDetailCol() }
      ]

    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
