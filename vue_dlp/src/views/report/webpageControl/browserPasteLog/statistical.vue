<template>
  <Statistical :api="'getBrowserPasteLogPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('690')" :exportable="hasPermission('691')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '../../component/Statistical'
import { getWebPasteNumDetailCol } from '@/utils/report'

export default {
  name: 'BrowserPasteLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'pasteCountSum', label: 'browserPasteSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_browser_paste_log', detailCol: getWebPasteNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'pasteCount', label: 'browserPasteSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_browser_paste_log', detailCol: getWebPasteNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'pasteCountSum', label: 'browserPasteSumAll', width: '150', sort: true, detailTable: 'dwd_browser_paste_log', detailCol: getWebPasteNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'browserTypeName', label: 'browserType', width: '150', sort: true, joinSearch: true, searchCol: 'browser_type', searchProp: 'browserType' },
        { prop: 'pasteCount', label: 'browserPasteSumAll', width: '150', sort: true, detailTable: 'dwd_browser_paste_log', detailCol: getWebPasteNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
