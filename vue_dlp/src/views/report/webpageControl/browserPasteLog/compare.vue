<template>
  <comparative-trend
    :api="'getBrowserPasteLogCompareData'"
    :api-dept="'getBrowserPasteLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'BrowserPasteLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '网页粘贴',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'pasteCountSum', label: 'browserPasteSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['pasteCountSum']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
