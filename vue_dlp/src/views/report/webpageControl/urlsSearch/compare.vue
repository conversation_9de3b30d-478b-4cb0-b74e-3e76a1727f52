<template>
  <comparative-trend
    :api="'getUrlsSearchCompareData'"
    :api-dept="'getUrlsSearchCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'UrlsSearchCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '网页搜索',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'searchCountSum', label: 'urlSearchSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['searchCountSum']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
