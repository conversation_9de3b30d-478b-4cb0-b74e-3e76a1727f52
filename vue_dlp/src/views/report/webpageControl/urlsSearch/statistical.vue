<template>
  <Statistical :api="'getUrlsSearchPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('680')" :exportable="hasPermission('681')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '../../component/Statistical'
import { getWebSearchNumDetailCol } from '@/utils/report'

export default {
  name: 'UrlsSearchStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'searchCountSum', label: 'urlSearchSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_urls_search', detailCol: getWebSearchNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'searchCount', label: 'urlSearchSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_urls_search', detailCol: getWebSearchNumDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'searchCountSum', label: 'urlSearchSumAll', width: '150', sort: true, detailTable: 'dwd_urls_search', detailCol: getWebSearchNumDetailCol() }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'host', label: 'website', width: '150', sort: true, joinSearch: true, searchCol: 'host' },
        { prop: 'searchCount', label: 'urlSearchSumAll', width: '150', sort: true, detailTable: 'dwd_urls_search', detailCol: getWebSearchNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
