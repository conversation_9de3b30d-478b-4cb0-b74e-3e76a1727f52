<template>
  <Trend :api="'getUrlsSearchTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('685')" :exportable="hasPermission('686')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getWebSearchNumDetailCol } from '@/utils/report'

export default {
  name: 'UrlsSearchTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'searchCountSum', label: 'urlSearchSumAll', width: '150', sort: true, detailTable: 'dwd_urls_search', detailCol: getWebSearchNumDetailCol() }
      ],
      customList: [
        { prop: 'searchCountSum', label: 'urlSearchSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_urls_search', detailCol: getWebSearchNumDetailCol() }
      ]

    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
