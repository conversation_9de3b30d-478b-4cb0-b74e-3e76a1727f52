<template>
  <comparative-trend
    :api="'getSecurityCompareData'"
    :api-dept="'getSecurityCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'EncOrDecCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '批量加解密数量',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'fileTotalSumAll', label: 'fileTotalSumAll' },
        { prop: 'succCountSumAll', label: 'succCountSumAll' },
        { prop: 'failCountSumAll', label: 'failCountSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['fileTotalSumAll', 'succCountSumAll', 'failCountSumAll']
    }
  },
  computed: {

  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
