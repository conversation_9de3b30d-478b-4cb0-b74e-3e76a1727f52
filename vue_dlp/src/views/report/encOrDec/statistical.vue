<template>
  <Statistical :api="'getEncOrDecPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('610')" :exportable="hasPermission('611')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '../component/Statistical'
import { getEncOrDecDetailCol } from '@/utils/report'

export default {
  name: 'EncOrDecStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'fileTotalSumAll', label: 'fileTotalSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'succCountSumAll', label: 'succCountSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'failCountSumAll', label: 'failCountSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() }
      ],
      subCustomList: [
        { prop: 'fileTotalSum', label: 'fileTotalSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'succCountSum', label: 'succCountSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'failCountSum', label: 'failCountSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'fileTotalSumAll', label: 'fileTotalSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'succCountSumAll', label: 'succCountSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'failCountSumAll', label: 'failCountSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'opName', label: 'opName', width: '150', sort: true, joinSearch: true, searchCol: 'op_type', searchProp: 'opType' },
        { prop: 'fileTotalSum', label: 'fileTotalSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'succCountSum', label: 'succCountSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'failCountSum', label: 'failCountSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>

