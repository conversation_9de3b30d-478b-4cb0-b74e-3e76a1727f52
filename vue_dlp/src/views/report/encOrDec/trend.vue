<template>
  <Trend :api="'getEncOrDecTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('615')" :exportable="hasPermission('616')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../component/Trend'
import { getEncOrDecDetailCol } from '@/utils/report'

export default {
  name: 'EncOrDecTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'fileTotalSumAll', label: 'fileTotalSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'succCountSumAll', label: 'succCountSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'failCountSumAll', label: 'failCountSumAll', width: '150', sort: true, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() }
      ],
      customList: [
        { prop: 'fileTotalSumAll', label: 'fileTotalSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'succCountSumAll', label: 'succCountSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() },
        { prop: 'failCountSumAll', label: 'failCountSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_enc_or_dec_log', detailCol: getEncOrDecDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>

