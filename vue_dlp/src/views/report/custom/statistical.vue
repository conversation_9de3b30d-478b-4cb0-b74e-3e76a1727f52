<template>
  <Statistical
    :api="'getCustomBar'"
    :default-col-model="defaultColModel"
    :custom-list="customList"
    :drill-down="hasPermission('865')"
    :exportable="hasPermission('866')"
    :view-details="hasPermission('867')"
  />
</template>

<script>
import Statistical from '../component/StatisticalCustom'
import { getDataItems } from '@/utils/report'

export default {
  name: 'CustomStatistical',
  components: { Statistical },
  data() {
    return {
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true }
      ],
      customList: getDataItems(this.hasPermission('867'))
    }
  }
}
</script>

