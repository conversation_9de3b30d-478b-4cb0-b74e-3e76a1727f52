<template>
  <Trend
    :api="'getCustomTrend'"
    :col-model="colModel"
    :custom-list="customList"
    :drill-down="hasPermission('870')"
    :exportable="hasPermission('871')"
    :view-details="hasPermission('872')"
  />
</template>

<script>
import Trend from '../component/TrendCustom'
import { getDataItems } from '@/utils/report';

export default {
  name: 'CustomTrend',
  components: { Trend },
  data() {
    return {
      colModel: [
        { prop: 'labelValue', label: 'month', width: '140', sort: true, sortOriginal: true }
      ],
      customList: getDataItems(this.hasPermission('872'))
    }
  }
}
</script>
