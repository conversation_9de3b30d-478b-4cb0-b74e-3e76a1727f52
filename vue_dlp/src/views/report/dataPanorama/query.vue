<!--敏感内容检测报表查询条件封装-->
<template>
  <span class="query-report-span">
    <span>
      <!--报表日期选择下拉框-->
      {{ $t('pages.dimBaseType') }}：
      <el-select v-model="query.dimBaseType" style="width: 125px;" @change="loadDimTime" >
        <template v-for="(item, index) in dateTypeOptions">
          <el-option
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </template>
      </el-select>
      <!--报表日期选择下拉框后面的级联选择器-->
      <el-cascader
        :key="cascaderKey"
        v-model="query.dimValue"
        :options="dateOptions"
        :props="{ expandTrigger: 'hover' }"
        :separator="$t('pages.cascaderSep')"
        style="width: 150px; margin-right: 10px;line-height:0"
        @change="dimValueChange"
      ></el-cascader>
    </span>
  </span>
</template>

<script>
import { getDimTime } from '@/api/report/baseReport/issueReport/issue';

export default {
  name: 'Query',
  components: {},
  props: {

  },
  data() {
    return {
      // 报表日期统计下拉框
      dateTypeOptions: [
        { value: 5, label: this.$t('pages.dateTypeOptions1') },
        { value: 2, label: this.$t('pages.dateTypeOptions2') },
        { value: 1, label: this.$t('pages.dateTypeOptions3') },
        { value: 3, label: this.$t('pages.dateTypeOptions4') },
        { value: 4, label: this.$t('pages.dateTypeOptions5') }
      ],
      // 报表日期时间选择下拉框
      dateOptions: [],
      // 报表日期时间选择下拉框
      months: ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      quarters: ['', 'theFirst', 'theSecond', 'theThird', 'theFourth'],
      cascaderKey: 0,
      // 近几天
      recentDaysOptions: [
        { value: 7, label: this.$t('pages.recentDays', { day: 7 }) },
        { value: 15, label: this.$t('pages.recentDays', { day: 15 }) },
        { value: 30, label: this.$t('pages.recentDays', { day: 30 }) }
      ],
      // 搜查查询
      query: {
        dimBaseType: 1,
        dimValue: ''
      }
    }
  },
  watch: {

  },
  created() {
    // 报表日期搜索下拉框
    this.loadDimTime('initial')
  },
  methods: {
    /**
     * 将查询条件传给父组件
     * */
    getQuery() {
      return this.query
    },
    /**
       * 报表日期下拉框选择时，后面的下拉框根据条件发生变化
       * @param value 当前下拉框选中的value值，value === 6时显示近几天下拉框，否则显示时间级联选择器
       * */
    loadDimTime(value) {
      const dimBaseType = this.query.dimBaseType
      // console.log('value', value)
      const formatterOption = (dimValue) => {
        const dimYear = dimValue.substring(0, 4)
        if (dimBaseType === 4) {
          this.dateOptions.push({ label: this.$t('pages.year', { Year: dimYear }), value: dimValue })
        } else {
          if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
          if (dimBaseType === 1) {
            this.dateOptions[dimYear].children.push({ label: this.$t('text.' + this.months[parseInt(dimValue.substring(4))]), value: dimValue })
          } else if (dimBaseType === 2) {
            this.dateOptions[dimYear].children.push({ label: this.$t('pages.weekNum', { Week: dimValue.substring(4) }), value: dimValue })
          } else if (dimBaseType === 3) {
            this.dateOptions[dimYear].children.push({ label: this.$t('pages.quarterNum', { Quarter: this.$t('pages.' + this.quarters[parseInt(dimValue.substring(4))]) }), value: dimValue })
          } else if (dimBaseType === 5) {
            const month = dimValue.substring(4, 6)
            const day = dimValue.substring(6, 8).startsWith('0') ? dimValue.substring(7, 8) : dimValue.substring(6, 8)
            const monthChildren = this.dateOptions[dimYear].children
            const index = monthChildren.findIndex(item => item.label === this.$t('text.' + this.months[parseInt(month)]))
            index < 0 && monthChildren.push({ label: this.$t('text.' + this.months[parseInt(month)]), value: dimValue.substring(0, 6), children: [{ label: this.$t('pages.dateNum', { date: day }), value: dimValue }] })
            index >= 0 && monthChildren[index].children.push({ label: this.$t('pages.dateNum', { date: day }), value: dimValue })
          }
        }
      }
      getDimTime(this.query.dimBaseType).then(response => {
        this.dateOptions.splice(0)
        response.data.sort((a, b) => {
          return a > b ? 1 : -1 // 返回正数 ，b排列在a之前
        })
        for (let i = 0; i < response.data.length; i++) {
          let time = response.data[i] + ''
          while (time.length < 6) { time += '0' }
          formatterOption(time)
        }
        ++this.cascaderKey
        const children = this.dateOptions[this.dateOptions.length - 1].children
        if (this.query.dimBaseType === 4) {
          this.query.dimValue = this.dateOptions[this.dateOptions.length - 1].value
        } else if (this.query.dimBaseType === 5) {
          this.query.dimValue = children[children.length - 1].children[0].value
        } else {
          this.query.dimValue = children[children.length - 1] ? children[children.length - 1].value : ''
        }
        value === 'initial' && this.$parent.getData()
      }).catch(() => {
        if (this.loading) {
          this.loading = false
        }
      })
    },
    /**
     * 级联选择器发生变化时
     * @param datas 当前选中日期时间
     * */
    dimValueChange(datas) {
      // console.log('datas', datas)
      if (datas && datas.length > 0) {
        this.query.dimValue = datas[datas.length - 1]
      } else {
        this.query.dimValue = undefined
      }
    }
  }
}
</script>

<style>
  .query-report-span>span{
    font-size: 14px;
  }
</style>
