<template>
  <div class="scrollable-content">
    <el-row :gutter="32">
      <div class="container">
        <div v-for="(item, index) in temp.itemData" :key="index" class="item">
          <div class="img"><i :class="item.ico"></i></div>
          <div class="name">{{ item.name }}</div>
          <div class="num">{{ item.num }}</div>
        </div>
      </div>
    </el-row>
    <el-row :gutter="32">
      <!--终端在线时长统计/开机时长统计-->
      <el-col v-if="isShowDurationStatistics" :xs="24" :sm="24" :lg="8">
        <data-panorama-gauge
          v-if="today"
          :chart-data="temp.onlineTimeDatas"
          style="width: 100%;height: 275px"
        />
        <pie-chart
          v-else
          :chart-data="temp.bootTimeDatas"
          :chart-option="temp.bootTimeOption"
          style="width: 100%;height: 275px"
        />
      </el-col>
      <!--应用程序运行时长统计-->
      <el-col :xs="24" :sm="24" :lg="showRightLg1">
        <pie-chart
          ref="leftEcharts1"
          :chart-data="temp.applicationDurationDatas"
          :chart-option="temp.applicationDurationOption"
          style="width: 100%;height: 275px"
        />
      </el-col>
      <!--文件操作数量统计-->
      <el-col :xs="24" :sm="24" :lg="showRightLg2">
        <pie-chart
          ref="leftEcharts2"
          :chart-data="temp.fileOperationDatas"
          :chart-option="temp.fileOperationOption"
          style="width: 100%;height: 275px"
        />
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <!--网页浏览记录数统计-->
      <el-col :xs="24" :sm="24" :lg="16">
        <bar-chart
          :chart-data="temp.webBrowsingData"
          :chart-option="temp.webBrowsingOption"
          style="width: 100%;height: 275px"
        />
      </el-col>
      <!--敏感关键字触发次数统计-->
      <el-col :xs="24" :sm="24" :lg="8">
        <pie-chart
          :chart-data="temp.keywordDatas"
          :chart-option="temp.keywordOption"
          style="width: 100%;height: 275px"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import PieChart from '@/components/ECharts/PieChart';
import BarChart from '@/components/ECharts/BarChart';
import dataPanoramaGauge from '@/components/ECharts/dataPanoramaGauge';
import { getDataPanoramaToday, getDataPanoramaHistory } from '@/api/report/baseReport/dataPanorama';
import { formatSeconds } from '@/utils';

export default {
  name: 'DataChart',
  components: { PieChart, BarChart, dataPanoramaGauge },
  props: {
    // 父组件传递过来的接口参数
    query: {
      type: Object,
      default() {
        return {}
      }
    },
    showLoading: {
      type: Function,
      required: true
    },
    hideLoading: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      today: true,
      isShowDurationStatistics: true,
      showRightLg1: 8,
      showRightLg2: 8,
      temp: {},
      defaultTemp: {
        itemData: [
          { name: this.$t('table.encOrDecSumAll'), num: 0, ico: 'el-icon-receiving' },
          { name: this.$t('table.outFileSumAll'), num: 0, ico: 'el-icon-folder' },
          { name: this.$t('table.printedCopiesSumAll'), num: 0, ico: 'el-icon-printer' },
          { name: this.$t('table.emailNumSumAll'), num: 0, ico: 'el-icon-message' },
          { name: this.$t('table.urlSearchSumAll'), num: 0, ico: 'el-icon-search' },
          { name: this.$t('table.chatRecordNumSumAll'), num: 0, ico: 'el-icon-chat-line-round' }
          // { name: '文件备份数', num: 32, ico: 'el-icon-money' },
          // { name: '文件操作次数', num: 43, ico: 'el-icon-edit-outline' },
          // { name: '全盘扫描文件数', num: 43, ico: 'el-icon-stopwatch' },
          // { name: '审批数', num: 43, ico: 'el-icon-finished' },
          // { name: '加密文件数', num: 43, ico: 'el-icon-lock' },
          // { name: '论坛发帖记录数', num: 43, ico: 'el-icon-position' },
          // { name: 'USB外发文件数', num: 43, ico: 'el-icon-attract' }
        ],
        onlineTimeDatas: 0,
        // 饼图 开机时长统计
        bootTimeDatas: [],
        bootTimeOption: {
          title: {
            text: this.$t('report.startupDurationStatistics'),
            left: 'center',
            subtext: ''
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: ['25%', '45%'],
              center: ['50%', '55%']
            }
          ]
        },
        // 饼图 应用程序运行时长统计
        applicationDurationDatas: [],
        applicationDurationOption: {
          title: {
            text: this.$t('components.runningTimeStatistics'),
            left: 'center',
            subtext: 'Top10'
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 饼图 文件操作类型统计
        fileOperationDatas: [],
        fileOperationOption: {
          title: {
            text: this.$t('report.FileLogStatistics'),
            left: 'center',
            subtext: ''
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: ['25%', '45%'],
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        },
        // 网页浏览记录数统计 柱状图
        webBrowsingData: [],
        webBrowsingOption: {
          title: {
            text: this.$t('report.urlBrowseStatistics'),
            subtext: 'Top10'
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: this.$t('report.webPageType'),
            data: []
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 饼图 敏感关键字触发次数统计
        keywordDatas: [],
        keywordOption: {
          title: {
            text: this.$t('report.SensitiveKeywordStatistics'),
            left: 'center',
            subtext: 'Top10'
          },
          toolbox: {
            show: false
          },
          legend: {
            show: false,
            left: 'center'
          },
          series: [
            {
              radius: '40%',
              center: ['50%', '55%'] // 图的位置，距离左跟上的位置
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  watch: {
    // el-col宽度发生变化时，需要对图表做宽度自适应操作，否则图表宽度为原始容器宽度，在页面上不会自动居中
    showRightLg1(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.$nextTick(() => {
          this.$refs['leftEcharts1'].__resizeHandler()
        })
      }
    },
    showRightLg2(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.$nextTick(() => {
          this.$refs['leftEcharts2'].__resizeHandler()
        })
      }
    }
  },
  activated() {

  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取图表信息
     */
    getChartsData() {
      this.resetTemp()
      this.showLoading()
      // console.log('获取父组件参数', JSON.parse(JSON.stringify(this.query)))
      // 如果选中是操作员,则隐藏:"终端在线时长统计"和"开机时长统计",并修改布局宽度
      this.durationStatisticsShow()
      // 区分"今日"还是"历史",用于确定图表显示"终端在线时长统计"还是"开机时长统计"
      this.durationStatisticsTodayHistory()
      if (this.query.dimBaseType) {
        // 历史数据获取
        getDataPanoramaHistory(this.query).then(res => {
          this.hideLoading()
          const response = res.data
          // 上方小方块获取
          this.topItemGet(response.itemValueMap)
          // 开机时长统计
          if (response.chartDataObjMap.computerBootTimeChartData) {
            this.temp.bootTimeDatas = response.chartDataObjMap.computerBootTimeChartData.chartData
            this.commonChartFormatter(this.temp.bootTimeOption)
          }
          // 通用图表
          this.commonChartGet(response.chartDataObjMap)
        }).catch(() => {
          this.hideLoading()
        })
      } else {
        // 今日数据获取
        getDataPanoramaToday(this.query).then(res => {
          this.hideLoading()
          const response = res.data
          // 上方小方块获取
          this.topItemGet(response.itemValueMap)
          // 终端在线时长统计
          if (response.itemValueMap.hasOwnProperty('terminalOnlineTime')) {
            this.temp.onlineTimeDatas = response.itemValueMap.terminalOnlineTime
          }
          // 通用图表
          this.commonChartGet(response.chartDataObjMap)
        }).catch(() => {
          this.hideLoading()
        })
      }
    },
    /**
     * 上方小方块数据获取
     * @param item 接口返回的itemValueMap字段
     * */
    topItemGet(item) {
      this.temp.itemData[0].num = item.encOrDecSumAll
      this.temp.itemData[1].num = item.outFileSumAll
      this.temp.itemData[2].num = item.printedCopiesSumAll
      this.temp.itemData[3].num = item.emailNumSumAll
      this.temp.itemData[4].num = item.urlSearchSumAll
      this.temp.itemData[5].num = item.chatRecordNumSumAll
    },
    /**
     * 通用图表获取
     * @param option 接口返回的chartDataObjMap字段
     * */
    commonChartGet(option) {
      // 应用程序运行时长统计
      if (option.hasOwnProperty('appRunTimeChartData')) {
        this.temp.applicationDurationDatas = option.appRunTimeChartData.chartData
        this.commonChartFormatter(this.temp.applicationDurationOption)
      }
      // 文件操作数量统计
      if (option.hasOwnProperty('fileOpChartData')) {
        this.temp.fileOperationDatas = option.fileOpChartData.chartData
      }
      // 网页浏览记录数统计
      if (option.hasOwnProperty('webBrowsingChartData')) {
        this.temp.webBrowsingOption.xAxis.data = option.webBrowsingChartData.xaxisData
        this.temp.webBrowsingOption.series[0].data = option.webBrowsingChartData.seriesData
      }
      // 敏感关键字触发次数统计
      if (option.hasOwnProperty('sensitiveKeywordChartData')) {
        this.temp.keywordDatas = option.sensitiveKeywordChartData.chartData
      }
    },
    /**
     * 鼠标悬停时长转换（开机时长、应用程序运行时长统计）
     * @param option echarts配置项对象
     */
    commonChartFormatter(option) {
      option.tooltip = {
        formatter: function(params) {
          return `${params.marker}${params.name}：${formatSeconds(params.value)}`;
        }
      }
    },
    /**
     * 判断是否显示时长统计(只有选中终端时,才会显示时长统计,否则隐藏)
     */
    durationStatisticsShow() {
      if (this.query.objectType === '1') {
        this.isShowDurationStatistics = true
        this.showRightLg1 = 8
        this.showRightLg2 = 8
      } else {
        this.isShowDurationStatistics = false
        this.showRightLg1 = 16
        this.showRightLg2 = 8
      }
    },
    /**
     * 判断当前选中是"今日"还是"历史"
     * 今日:显示"终端在线时长统计"
     * 历史:显示"开机时长统计"
     */
    durationStatisticsTodayHistory() {
      if (this.query.dimBaseType) {
        this.today = false
      } else {
        this.today = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .scrollable-content{
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .container {
    display: flex;
    flex-wrap: wrap;
    width: 95%;
    margin: auto;
    .item {
      flex: 1 1 calc(16% - 10px);
      margin: 5px;
      box-sizing: border-box;
      text-align: center;
      font-size: 14px;
      border: 1px solid #3b749c;
      div{
        height: 20px;
        line-height: 20px;
      }
      .num{
        color: #409EFF;
      }
    }
  }
</style>
