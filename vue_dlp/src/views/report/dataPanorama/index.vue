<template>
  <div
    v-loading="loading"
    class="app-container"
  >
    <TransferTree ref="transferTree" :listable="listable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <!--上方搜索条件-->
        <div v-show="name === 'history'" style="float: right">
          <!--查询条件组件-->
          <query ref="queryData"/>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="searchClick">{{ $t('text.search') }}</el-button>
        </div>
      </div>
      <div v-if="showTips" class="tableBox">
        <div class="tips-msg">{{ showTipsContent }}</div>
      </div>
      <div v-if="!showTips" class="tableBox">
        <el-tabs v-model="name" @tab-click="handleClick">
          <el-tab-pane :label="$t('report.todayDataPanorama')" name="today">
            <data-chart v-if="name === 'today'" ref="todayPane" :query="query" :show-loading="showLoading" :hide-loading="hideLoading"/>
          </el-tab-pane>
          <el-tab-pane :label="$t('report.panoramaHistoricalData')" name="history">
            <data-chart v-if="name === 'history'" ref="historyPane" :query="query" :show-loading="showLoading" :hide-loading="hideLoading"/>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import dataChart from '@/views/report/dataPanorama/dataChart';
import query from '@/views/report/dataPanorama/query';
export default {
  name: 'DataPanorama',
  components: { dataChart, query },
  props: {
    listable: { type: Boolean, default: true }
  },
  data() {
    return {
      loading: false,
      showTips: true,
      showTipsContent: this.$t('report.dataPanoramaMsg1'),
      name: 'today',
      query: { }, // 查询条件
      commonObjectId: undefined,
      defaultQuery: {
        // 终端，操作员
        objectType: undefined,
        objectId: undefined,
        // 报表日期
        dimBaseType: undefined,
        dimValue: undefined,
        objectIds: undefined
      },
      showTree: true
    }
  },
  computed: {

  },
  activated() {

  },
  created() {
    // 清除选中节点，避免刷新时选中的节点还存在
    // this.transferTree.clearSelectedNode()
    this.resetTemp()
  },
  methods: {
    showLoading() {
      this.loading = true
    },
    hideLoading() {
      this.loading = false
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    /**
     * 获取接口参数(报表日期)
     * */
    updateQueryData() {
      this.query.dimBaseType = this.$refs.queryData.getQuery().dimBaseType
      this.query.dimValue = this.$refs.queryData.getQuery().dimValue
    },
    /**
     * 重置接口参数(报表日期)
     * */
    resetQueryData() {
      this.query.dimBaseType = undefined
      this.query.dimValue = undefined
    },
    /**
     * 获取子组件方法,getChartsData为子组件接口调用修改页面数据的方法
     * */
    getChildData() {
      this.$nextTick(() => {
        if (this.$refs.todayPane) {
          this.$refs.todayPane.getChartsData()
        }
        if (this.$refs.historyPane) {
          this.$refs.historyPane.getChartsData()
        }
      })
    },
    /**
     * 查询条件点击(修改query参数)
     * */
    searchClick() {
      this.updateQueryData()
      this.getChildData()
    },
    /**
     * 左侧树选中发生变化时(修改query参数)
     * 1、如果选中终端或操作员，将参数传递给子组件，获取子组件渲染
     * 2、如果选中分组，则直接显示提示信息
     * @param tabName
     * @param checkedNode
     */
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.commonObjectId = checkedNode.dataId
        if (this.name === 'history') {
          this.updateQueryData()
          this.query.objectIds = this.commonObjectId
          this.query.objectId = undefined
        } else {
          this.resetQueryData()
          this.query.objectIds = undefined
          this.query.objectId = this.commonObjectId
        }
        if (checkedNode.type == 1 || checkedNode.type == 2) {
          // 显示用户全景
          this.showTips = false
          this.getChildData()
        } else {
          // 显示提示信息
          this.showTips = true
        }
      } else {
        // 显示提示信息
        this.showTips = true
        this.query.objectType = undefined
        this.query.objectId = undefined
        this.query.objectIds = undefined
        this.query.dimBaseType = undefined
        this.query.dimValue = undefined
      }
    },
    /**
     * 今日/历史tab点击时(修改query参数)
     * @param tab
     * @param event
     */
    handleClick(tab, event) {
      this.$nextTick(() => {
        if (this.name === 'history') {
          this.updateQueryData()
          this.query.objectIds = this.commonObjectId
          this.query.objectId = undefined
        } else {
          this.resetQueryData()
          this.query.objectIds = undefined
          this.query.objectId = this.commonObjectId
        }
        this.getChildData()
      })
    },
    resetTemp() {
      this.query = JSON.parse(JSON.stringify(this.defaultQuery))
    }
  }
}
</script>

<style lang="scss" scoped>
.tips-msg{
  width: 100%;
  height: 30px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  text-align: center;
  font-size: 14px;
}
</style>
