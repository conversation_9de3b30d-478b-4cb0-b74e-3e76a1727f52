<template>
  <Trend :api="'getScanViolateTrend'" :col-model="colModel" :custom-list="customList" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../component/Trend'
import { getIllegalLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getIllegalLogDetailCol, getGeneralDetailCol, getDripDetailCol } from '@/utils/report'

export default {
  name: 'ScanViolateTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'scanViolateLogSumAll', label: 'scanViolateLogSumAll', width: '150', sort: true, detailCol: getIllegalLogDetailCol(), getDetailMethod: getIllegalLogDetail },
        { prop: 'generalScanViolateLogSumAll', label: 'generalScanViolateLogSumAll', width: '150', sort: true, detailTable: 'dwd_general_scan_violate_log', detailCol: getGeneralDetailCol() },
        { prop: 'dripScanViolateLogSumAll', label: 'dripScanViolateLogSumAll', width: '150', sort: true, detailTable: 'dwd_drip_scan_violate_log', detailCol: getDripDetailCol() }
      ],
      customList: [
        { prop: 'scanViolateLogSumAll', label: 'scanViolateLogSumAll', width: '150', sort: true, value: 1, detailCol: getIllegalLogDetailCol(), getDetailMethod: getIllegalLogDetail },
        { prop: 'generalScanViolateLogSumAll', label: 'generalScanViolateLogSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_general_scan_violate_log', detailCol: getGeneralDetailCol() },
        { prop: 'dripScanViolateLogSumAll', label: 'dripScanViolateLogSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_drip_scan_violate_log', detailCol: getDripDetailCol() }
      ]

    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
