<template>
  <Statistical :api="'getScanViolatePie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '../component/Statistical'
import { getIllegalLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getIllegalLogDetailCol, getGeneralDetailCol, getDripDetailCol } from '@/utils/report'

export default {
  name: 'ScanViolateStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'scanViolateLogSumAll', label: 'scanViolateLogSumAll', width: '150', sort: true, value: 1, detailCol: getIllegalLogDetailCol(), getDetailMethod: getIllegalLogDetail },
        { prop: 'generalScanViolateLogSumAll', label: 'generalScanViolateLogSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_general_scan_violate_log', detailCol: getGeneralDetailCol() },
        { prop: 'dripScanViolateLogSumAll', label: 'dripScanViolateLogSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_drip_scan_violate_log', detailCol: getDripDetailCol() }
      ],
      subCustomList: [
        { prop: 'scanViolateLogSum', label: 'scanViolateLogSumAll', width: '150', sort: true, value: 1, detailCol: getIllegalLogDetailCol(), getDetailMethod: getIllegalLogDetail },
        { prop: 'generalScanViolateLogSum', label: 'generalScanViolateLogSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_general_scan_violate_log', detailCol: getGeneralDetailCol() },
        { prop: 'dripScanViolateLogSum', label: 'dripScanViolateLogSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_drip_scan_violate_log', detailCol: getDripDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'scanViolateLogSumAll', label: 'scanViolateLogSumAll', width: '150', sort: true, detailCol: getIllegalLogDetailCol(), getDetailMethod: getIllegalLogDetail },
        { prop: 'generalScanViolateLogSumAll', label: 'generalScanViolateLogSumAll', width: '150', sort: true, detailTable: 'dwd_general_scan_violate_log', detailCol: getGeneralDetailCol() },
        { prop: 'dripScanViolateLogSumAll', label: 'dripScanViolateLogSumAll', width: '150', sort: true, detailTable: 'dwd_drip_scan_violate_log', detailCol: getDripDetailCol() }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'severityName', label: 'severity', width: '100', sort: true, joinSearch: true, searchCol: 'severity', searchProp: 'severity' },
        { prop: 'scanViolateLogSum', label: 'scanViolateLogSumAll', width: '150', sort: true, detailCol: getIllegalLogDetailCol(), getDetailMethod: getIllegalLogDetail },
        { prop: 'generalScanViolateLogSum', label: 'generalScanViolateLogSumAll', width: '150', sort: true, detailTable: 'dwd_general_scan_violate_log', detailCol: getGeneralDetailCol() },
        { prop: 'dripScanViolateLogSum', label: 'dripScanViolateLogSumAll', width: '150', sort: true, detailTable: 'dwd_drip_scan_violate_log', detailCol: getDripDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
