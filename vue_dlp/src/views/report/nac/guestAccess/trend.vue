<template>
  <Trend
    :api="'getNacGuestAccessTrend'"
    :col-model="colModel"
    :custom-list="customList"
    :drill-down="hasPermission('898')"
    :exportable="hasPermission('899')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Trend from '../../component/Trend'

export default {
  name: 'GuestAccess',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      dateTypes: [
        { value: 3, label: this.$t('pages.trendDateTypeOptions2') },
        { value: 2, label: this.$t('pages.trendDateTypeOptions4') },
        { value: 1, label: this.$t('pages.trendDateTypeOptions5') }
      ],
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'dataCount', label: '访客接入数', width: '150', sort: true, detailTable: 'dwd_nac_guest_access_log', detailCol: this.getDetailCol() }
      ],
      customList: [
        { prop: 'dataCount', label: '访客接入数', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_guest_access_log', detailCol: this.getDetailCol() }
      ]
    }
  },
  methods: {
    getDetailCol() {
      return [
        { prop: 'login_user', label: '姓名' },
        { prop: 'company', label: '公司' },
        { prop: 'visit', label: '拜访对象' },
        { prop: 'acl', label: 'ACL' },
        { prop: 'phone', label: '联系电话' },
        { prop: 'vlan', label: 'VLAN' },
        { prop: 's_time', label: '入网时间', width: '150' },
        { prop: 'e_time', label: '离线时间', width: '150' }
        // { prop: 'login_method', label: '登录方式' },
        // { prop: 'fail_data', label: '失败原因' }
      ]
    },
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
