<template>
  <Trend
    :api="'getNacIllegalAccessTrend'"
    :col-model="colModel"
    :custom-list="customList"
    :drill-down="hasPermission('904')"
    :exportable="hasPermission('905')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Trend from '../../component/Trend'

export default {
  name: 'IllegalAccess',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      dateTypes: [
        { value: 3, label: this.$t('pages.trendDateTypeOptions2') },
        { value: 2, label: this.$t('pages.trendDateTypeOptions4') },
        { value: 1, label: this.$t('pages.trendDateTypeOptions5') }
      ],
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'dataCount', label: '违规接入数', width: '150', sort: true, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() }
      ],
      customList: [
        { prop: 'dataCount', label: '违规接入数', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() }
      ]
    }
  },
  methods: {
    getDetailCol() {
      return [
        { prop: 'create_time', label: 'createTime', width: '150' },
        { prop: 'login_account', label: '登录账号' },
        { prop: 'login_user', label: '登录用户' },
        { prop: 'depart_name', label: '部门名称' },
        { prop: 'fail_reason', label: '失败原因', width: '150', formatter: row => row.fail_reason + '失败' },
        { prop: 'ip', label: 'ip', width: '150' },
        { prop: 'ipv6', label: 'ipv6' },
        { prop: 'device_type', label: '设备类型' },
        { prop: 'name', label: '设备名称', width: '150' },
        { prop: 'mac', label: 'mac', width: '150' },
        { prop: 'switch_ip', label: '交换机IP', width: '150' },
        { prop: 'switch_port', label: '交换机端口' },
        { prop: 'fail_data', label: '失败原因明细', width: '300', formatter: (row) => {
          return JSON.parse(row.fail_data).failData
        } }
        // { prop: 'login_method', label: '登录方式' },
        // { prop: 'fail_data', label: '失败原因' }
      ]
    },
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
