<template>
  <Statistical
    :api="'getNacIllegalAccessPie'"
    :show-count-by-object="false"
    :default-col-model="defaultColModel"
    :sub-col-model="subColModel"
    :custom-list="customList"
    :label-clickable="false"
    :x-axis-label="'用户'"
    :disable-subchart-column-setting="true"
    :drill-down="hasPermission('896')"
    :exportable="hasPermission('897')"
    :custom-tooltip-formatter="(d) => d.name + ': ' + d.value"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../../component/Statistical'

export default {
  name: 'IllegalAccessReport',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'antiVirusFailSum', label: 'antiVirusFailSum', searchCol: 'fail_reason', searchValue: '杀毒软件扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'processWhiteFailSum', label: 'processWhiteFailSum', searchCol: 'fail_reason', searchValue: '进程白名单扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'processBlackFailSum', label: 'processBlackFailSum', searchCol: 'fail_reason', searchValue: '进程黑名单扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'serviceWhiteFailSum', label: 'serviceWhiteFailSum', searchCol: 'fail_reason', searchValue: '白名单服务扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'serviceBlackFailSum', label: 'serviceBlackFailSum', searchCol: 'fail_reason', searchValue: '黑名单服务扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'ldFailSum', label: 'ldFailSum', searchCol: 'fail_reason', searchValue: '绿盾扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'weakPasswordFailSum', label: 'weakPasswordFailSum', searchCol: 'fail_reason', searchValue: '弱口令扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'computeNameFailSum', label: 'computeNameFailSum', searchCol: 'fail_reason', searchValue: '计算机名称规范扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'osFailSum', label: 'osFailSum', searchCol: 'fail_reason', searchValue: '操作系统扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'osVulnerabilityFailSum', label: 'osVulnerabilityFailSum', searchCol: 'fail_reason', searchValue: '操作系统漏洞扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'computeAccountFailSum', label: 'computeAccountFailSum', searchCol: 'fail_reason', searchValue: '计算机账户策略扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'screenProtectFailSum', label: 'screenProtectFailSum', searchCol: 'fail_reason', searchValue: '屏保扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'guestAccountFailSum', label: 'guestAccountFailSum', searchCol: 'fail_reason', searchValue: '来宾账号扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'shareFailSum', label: 'shareFailSum', searchCol: 'fail_reason', searchValue: '禁用共享配置扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'firewallFailSum', label: 'firewallFailSum', searchCol: 'fail_reason', searchValue: '防火墙扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'remoteDesktopFailSum', label: 'remoteDesktopFailSum', searchCol: 'fail_reason', searchValue: '禁用远程桌面扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'portFailSum', label: 'portFailSum', searchCol: 'fail_reason', searchValue: '端口扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'domainFailSum', label: 'domainFailSum', searchCol: 'fail_reason', searchValue: '域环境扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'countSum', label: 'violationTotal', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() }
      ],
      subCustomList: [
        { prop: 'logNumSum', label: 'logNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: '111', width: '150', sort: true },
        // { prop: 'loginUser', label: '用户', width: '150' },
        // { prop: 'departName', label: '部门', width: '150' },
        { prop: 'antiVirusFailSum', label: '杀毒软件扫描失败数', searchCol: 'fail_reason', searchValue: '杀毒软件扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'processWhiteFailSum', label: '进程白名单扫描失败数', searchCol: 'fail_reason', searchValue: '进程白名单扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'processBlackFailSum', label: '进程黑名单扫描失败数', searchCol: 'fail_reason', searchValue: '进程黑名单扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'countSum', label: 'violationTotal', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'antiVirusFailSum', label: 'antiVirusFailSum', searchCol: 'fail_reason', searchValue: '杀毒软件扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'processWhiteFailSum', label: 'processWhiteFailSum', searchCol: 'fail_reason', searchValue: '进程白名单扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'processBlackFailSum', label: 'processBlackFailSum', searchCol: 'fail_reason', searchValue: '进程黑名单扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'serviceWhiteFailSum', label: 'serviceWhiteFailSum', searchCol: 'fail_reason', searchValue: '白名单服务扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'serviceBlackFailSum', label: 'serviceBlackFailSum', searchCol: 'fail_reason', searchValue: '黑名单服务扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'ldFailSum', label: 'ldFailSum', searchCol: 'fail_reason', searchValue: '绿盾扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'weakPasswordFailSum', label: 'weakPasswordFailSum', searchCol: 'fail_reason', searchValue: '弱口令扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'computeNameFailSum', label: 'computeNameFailSum', searchCol: 'fail_reason', searchValue: '计算机名称规范扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'osFailSum', label: 'osFailSum', searchCol: 'fail_reason', searchValue: '操作系统扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'osVulnerabilityFailSum', label: 'osVulnerabilityFailSum', searchCol: 'fail_reason', searchValue: '操作系统漏洞扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'computeAccountFailSum', label: 'computeAccountFailSum', searchCol: 'fail_reason', searchValue: '计算机账户策略扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'screenProtectFailSum', label: 'screenProtectFailSum', searchCol: 'fail_reason', searchValue: '屏保扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'guestAccountFailSum', label: 'guestAccountFailSum', searchCol: 'fail_reason', searchValue: '来宾账号扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'shareFailSum', label: 'shareFailSum', searchCol: 'fail_reason', searchValue: '禁用共享配置扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'firewallFailSum', label: 'firewallFailSum', searchCol: 'fail_reason', searchValue: '防火墙扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'remoteDesktopFailSum', label: 'remoteDesktopFailSum', searchCol: 'fail_reason', searchValue: '禁用远程桌面扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'portFailSum', label: 'portFailSum', searchCol: 'fail_reason', searchValue: '端口扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'domainFailSum', label: 'domainFailSum', searchCol: 'fail_reason', searchValue: '域环境扫描', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() },
        { prop: 'countSum', label: 'violationTotal', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_illegal_access_log', detailCol: this.getDetailCol() }
      ]
    }
  },
  methods: {
    getDetailCol() {
      return [
        { prop: 'create_time', label: 'createTime', width: '150' },
        { prop: 'login_account', label: '登录账号' },
        { prop: 'login_user', label: '登录用户' },
        { prop: 'depart_name', label: '部门名称' },
        { prop: 'fail_reason', label: '失败原因', width: '150', formatter: row => row.fail_reason + '失败' },
        { prop: 'ip', label: 'ip', width: '150' },
        { prop: 'ipv6', label: 'ipv6' },
        { prop: 'device_type', label: '设备类型' },
        { prop: 'name', label: '设备名称', width: '150' },
        { prop: 'mac', label: 'mac', width: '150' },
        { prop: 'switch_ip', label: '交换机IP', width: '150' },
        { prop: 'switch_port', label: '交换机端口' },
        { prop: 'fail_data', label: '失败原因明细', width: '300', formatter: (row) => {
          return JSON.parse(row.fail_data).failData
        } }
        // { prop: 'login_method', label: '登录方式' },
        // { prop: 'fail_data', label: '失败原因' }
      ]
    },
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
