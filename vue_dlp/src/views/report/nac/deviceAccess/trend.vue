<template>
  <Trend
    :api="'getNacDeviceAccessTrend'"
    :col-model="colModel"
    :custom-list="customList"
    :drill-down="hasPermission('902')"
    :exportable="hasPermission('903')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Trend from '../../component/Trend'

export default {
  name: 'DeviceAccess',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      dateTypes: [
        { value: 3, label: this.$t('pages.trendDateTypeOptions2') },
        { value: 2, label: this.$t('pages.trendDateTypeOptions4') },
        { value: 1, label: this.$t('pages.trendDateTypeOptions5') }
      ],
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'dataCount', label: '设备接入数', width: '150', sort: true, detailTable: 'dwd_nac_device_access_log', detailCol: this.getDetailCol() }
      ],
      customList: [
        { prop: 'dataCount', label: '设备接入数', width: '150', sort: true, value: 1, detailTable: 'dwd_nac_device_access_log', detailCol: this.getDetailCol() }
      ]
    }
  },
  methods: {
    getDetailCol() {
      return [
        { prop: 'name', label: '设备名称' },
        { prop: 'login_user', label: '姓名' },
        { prop: 'login_account', label: '账号' },
        { prop: 'ip', label: 'IPv4地址' },
        { prop: 'ipv6', label: 'IPv6地址' },
        { prop: 'mac', label: 'MAC' },
        { prop: 'switch_ip', label: '交换机IP' },
        { prop: 'switch_port', label: '交换机端口' },
        { prop: 'acl', label: 'ACL' },
        { prop: 'vlan', label: 'VLAN' },
        { prop: 'create_time', label: '入网时间', width: '150' }
      ]
    },
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
