<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.linkGraph')"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div>
        <div class="toolbar" style="height: 30px;">
          <div style="float: right;">
            <el-select v-model="changeSelect">
              <el-option :value="0" :label="$t('pages.horizontalBarChart')"></el-option>
              <el-option :value="1" :label="$t('pages.longitudinalBarChart')"></el-option>
            </el-select>
          </div>
        </div>
        <div class="no-pointer" style="height: 400px">
          <bar-double-chart
            v-if="changeSelect === 0"
            :chart-data="barChartData"
            :chart-option="barChartOption"
            style="height: 400px"
          />
          <bar-chart
            v-else
            :chart-data="barData"
            :chart-option="barOption"
            :x-axis-name="xAxisName"
            style="width: 100%;height: 100%"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart'
import BarDoubleChart from '@/components/ECharts/BarDoubleChart';
import { convert, formatSeconds } from '@/utils';

export default {
  name: 'ComparativeHbDialog',
  components: { BarChart, BarDoubleChart },
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      changeSelect: 0,
      xAxisName: this.$t('table.statisticalItem'),
      // 双层柱状图
      barChartData: [],
      barChartOption: {
        title: {
          text: ''
        },
        calculable: true,
        yAxis: [
          // 左轴
          {
            type: 'category',
            gridIndex: 0,
            axisLabel: {
              padding: 0,
              fontSize: 12,
              interval: 0,
              // 倾斜的程度
              // rotate: 10,
              formatter: (value) => {
                // 如果名称长度超过10个字符，截断并添加省略号
                if (value.length > 8) {
                  return value.substring(0, 8) + '...';
                } else {
                  return value;
                }
              }
            },
            data: []
          },
          {
            gridIndex: 1,
            type: 'category',
            axisLabel: {
              // 隐藏中间轴的名称(不隐藏的话左边柱子盖不住中间轴名称)
              show: false
            },
            data: []
          },
          {
            gridIndex: 2,
            axisLabel: {
              interval: 0,
              padding: 0,
              fontSize: 12,
              // 倾斜的程度
              // rotate: 10,
              formatter: (value) => {
                // 如果名称长度超过8个字符，截断并添加省略号
                if (value.length > 8) {
                  return value.substring(0, 8) + '...';
                } else {
                  return value;
                }
              }
            },
            type: 'category',
            nameGap: 25,
            // 轴线
            axisLine: {
              show: true
            },
            // 分割线
            axisTick: {
              show: false
            },
            data: []
          }
        ],
        series:
          [
            // 左侧轴
            {
              name: '2023',
              type: 'bar',
              // 对应的左侧轴x轴索引
              xAxisIndex: 0,
              yAxisIndex: 0,
              // 柱体收缩，不会太粗
              barMaxWidth: '40%',
              itemStyle: {
                color: {
                  type: 'linear', // 线性渐变
                  colorStops: [
                    { offset: 1, color: '#F76B1C' },
                    { offset: 0, color: '#FAD961' }
                  ]
                },
                emphasis: {
                  opacity: 1
                }
              },
              data: []
            },
            // 右侧轴
            {
              name: '2024',
              type: 'bar',
              xAxisIndex: 1,
              yAxisIndex: 1,
              barMaxWidth: '40%',
              itemStyle: {
                color: {
                  type: 'linear', // 线性渐变
                  colorStops: [
                    { offset: 0, color: '#1171C9' },
                    { offset: 1, color: '#61C4FA' }
                  ]
                },
                emphasis: {
                  opacity: 1
                }
              },
              data: []
            }
          ]
      },
      // 柱状图
      barData: [],
      barOption: {
        title: {
          text: ''
        },
        toolbox: {
          show: false
        },
        legend: {
          show: true,
          data: [this.$t('table.currentLabel'), this.$t('table.qoq')]
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 30,
            formatter: function(value, index) {
              // 如果文本长度超过8个字，则返回前八个字加上省略号
              if (value.length > 8) {
                return value.slice(0, 8) + '...';
              }
              // 如果文本长度不超过8个字，则直接返回原文本
              return value;
            }
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          splitArea: {
            show: false
          }
        },
        series: [
          {
            data: [],
            name: this.$t('table.currentLabel'),
            type: 'bar',
            barGap: 0,
            emphasis: { focus: 'series' }
          },
          {
            data: [],
            name: this.$t('table.qoq'),
            type: 'bar',
            barGap: 0,
            emphasis: { focus: 'series' }
          }
        ]
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    /**
       * 显示弹框
       * @param tempOption 双层柱状图pieChartData数据
       * @param chartTitle 当前报表名称
       */
    show(tempOption, chartTitle) {
      this.changeSelect = 0
      // console.log('chartTitle', chartTitle)
      // console.log('tempOption', JSON.parse(JSON.stringify(tempOption)))
      this.dialogVisible = true
      // 双轴柱状图（数据处理）
      this.barChartOption.title.text = chartTitle + this.$t('pages.sequentialQuantityStatistics')
      // 双轴柱状图需要的yAxis数据（分开赋值，避免样式被覆盖）
      this.barChartOption.yAxis[0].data = tempOption.yAxis[0].data
      this.barChartOption.yAxis[1].data = tempOption.yAxis[1].data
      this.barChartOption.yAxis[2].data = tempOption.yAxis[2].data
      // // 双轴柱状图需要的series数据
      this.barChartOption.series = tempOption.series
      // console.log('this.barChartOption', JSON.parse(JSON.stringify(this.barChartOption)))
      // 柱状图（数据处理）
      this.barOption.title.text = chartTitle + this.$t('pages.sequentialQuantityStatistics')
      this.barOption.xAxis.data = tempOption.yAxis[0].data
      this.barOption.series[0].data = tempOption.series[0].data
      this.barOption.series[1].data = tempOption.series[1].data
      const objFlag = tempOption.series[0].flag
      this.barOption.tooltip = {
        formatter: (d) => {
          // console.log('d', d)
          let res = d[0].name + '<br>';
          for (var i = 0; i < d.length; i++) {
            let number = d[i].value
            if (objFlag === 'size') {
              number = convert(d[i].value)
            } else if (objFlag === 'time') {
              number = formatSeconds(d[i].value)
            }
            res += d[i].marker + d[i].seriesName + ' : ' + number + '<br>'
          }
          return res
        }
      }
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
<style>
  .no-pointer>div>div>canvas {
    cursor: default;
  }
</style>
