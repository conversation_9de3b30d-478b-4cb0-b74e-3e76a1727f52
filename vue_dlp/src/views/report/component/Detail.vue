<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :title="title"
      :width="width"
      :visible.sync="visible"
      :modal="false"
      :close-on-click-modal="false"
      @close="detailCol = []"
    >
      <el-descriptions class="margin-top" :column="2" border>
        <el-descriptions-item
          v-for="(col, index) in detailCol.filter(i => filterCol(i))"
          :key="index"
          :label="$t('table.' + col.label)"
          :span="col.span"
        >
          {{ col.formatter ? col.formatter(rowData) : col.sensitive ? decode(rowData[col.prop]) : rowData[col.prop] }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { aesDecode } from '@/utils/encrypt'
export default {
  name: 'Detail',
  props: {
    title: {
      type: String,
      default() {
        return this.$t('pages.recordDetails')
      }
    },
    width: {
      type: String,
      default() {
        return '700px'
      }
    }
  },
  data() {
    return {
      visible: false,
      detailCol: [],
      rowData: {}
    }
  },
  methods: {
    filterCol(col) {
      // 过滤操作列
      if (col.label === 'operate') {
        return false
      }

      // 放开敏感列
      if (col.sensitive) {
        return true
      }

      // 过滤隐藏列
      if (typeof col.hidden === 'function') {
        return !col.hidden()
      }
      return !col.hidden
    },
    decode(data) {
      if (!data) {
        return data
      }

      try {
        const decodeData = aesDecode(data, 'tr838408$report$')
        return decodeData || data
      } catch (e) {
        console.error('Decode error, original data: ', data, 'Error: ', e)
        return data
      }
    }
  }
}
</script>
