<!--列表显示Table组件，多级表头
调用示例：
<my-table
  :col="colModel"                 // 表格列信息
  :data="rowDatasTB"              // 表格传入数据
  :cell-style="cellStyleTB"       // 单元格样式
  @cell-click="cellClickTB"       // 单元格点击事件
/>
// 表格列信息----注：这边与正常表格的区别是，列colModel中存在children
colModel: [
  { prop: 'time', label: '时间', sort: 'custom', detailChart: true },
  { prop: 'encryptionNumHeadline', label: '文件加密数',
    children: [
      { prop: 'encryptionNum', label: '文件加密数', detailCol: true },
      { prop: 'compareEncryptionNum', label: '同比文件加密数', width: 125, sort: 'custom' },
      { prop: 'compareEncryptionIncrease', label: '同比增长率' }
    ]
  },
  { prop: 'successesNum', label: '成功数', detailCol: true },
  { prop: 'compareSuccessesNum', label: '同比成功数' }
]-->
<template>
  <div class="tableBox tableBox_myTable">
    <el-table
      :data="data"
      :border="true"
      :height="212"
      :min-height="212"
      :cell-style="cellStyle"
      @cell-click="cellClick"
    >
      <my-column
        v-for="(item,index) in col"
        :key="index+Math.random()"
        :col="item"
        :hidden="hidden"
        :width="width"
      />
    </el-table>
  </div>
</template>

<script>
import MyColumn from './MyColumn'
export default {
  components: {
    MyColumn
  },
  props: {
    // 列宽
    width: {
      type: Number,
      default() {
        return 200
      }
    },
    // 隐藏列
    hidden: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 表格列
    col: {
      type: Array,
      default() {
        return []
      }
    },
    // 单元格样式
    cellStyle: {
      type: Function,
      default: function() {
        return ''
      }
    },
    // 表格数据
    data: {
      type: Array,
      default() {
        return []
      }
    }
  },
  computed: {
  },
  methods: {
    /**
     * 表格单元格点击事件
     * @param row
     * @param column
     * @param cell
     * @param event
     */
    cellClick(row, column, cell, event) {
      // 处理点击事件
      this.$emit('cell-click', row, column, cell, event);
    }
  }
}
</script>
<style lang="scss" scoped>
  //给固定列设置下边距
  .el-table {
    .el-table__fixed {
      height: 100% !important; //设置高优先，以覆盖内联样式
      /*height:auto !important;*/
      bottom:9px !important; //具体值是多少根据你横向滚动条的高度进行设置
    }
  }
  //去掉固定列下方的横线
  .el-table__fixed::before, .el-table__fixed-left::before {
    display:none;
  }
  .el-table__fixed-body-wrapper .el-table__body {
    padding-bottom: 9px; // 6px为横向滚动条高度
  }
</style>
<style>
  /*表格设置固定列fixed后，如果表格存在纵向滚动条，则表格固定列最后一层超过高度会被隐藏起来*/
  .tableBox_myTable>.el-table.el-table--scrollable-x>.el-table__fixed>.el-table__fixed-body-wrapper, .tableBox_myTable>.el-table.el-table--scrollable-x>.el-table__fixed-right>.el-table__fixed-body-wrapper{
    height: calc(100% - 61px) !important;
  }
  .tableBox_myTable>.el-table>.el-table__fixed>.el-table__fixed-header-wrapper>table>thead.is-group>tr>th.el-table__cell{
    background: #68a8d0;
  }
  .tableBox_myTable>.el-table thead.is-group th.el-table__cell{
    background: #68a8d0;
  }
</style>
