<template>
  <el-table-column
    v-if="!col.hidden"
    :prop="col.prop"
    :label="labelFormat(col.label)"
    :cell-style="cellStyle"
    :min-width="130"
    :width="col.width"
    :show-overflow-tooltip="true"
    :fixed="col.fixed"
    align="center"
    @cell-click="cellClick"
  >
    <template v-if="col.children && col.children.length">
      <my-column
        v-for="(item, index) in col.children"
        :key="index"
        :col="item"
      />
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: 'MyColumn',
  props: {
    // 隐藏列
    hidden: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 单元格样式
    cellStyle: {
      type: Function,
      default: function() {
        return ''
      }
    },
    // 表格列
    col: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  methods: {
    /**
     *表格字段多语言配置
     * */
    labelFormat(label) {
      const hasKey = this.$te(`table.${label}`);
      return hasKey ? this.$t(`table.${label}`) : label;
    },
    /**
     * 表格单元格点击事件
     * @param row
     * @param column
     * @param cell
     * @param event
     */
    cellClick(row, column, cell, event) {
      // 处理点击事件
      console.log('Cell clicked:', row, column, cell, event);
    }
  }
}
</script>
<style scoped>
</style>
