<!--自定义报表-->
<template>
  <div ref="print" class="app-container" style="overflow:hidden;">
    <div v-loading="loading">
      <div class="toolbar clearfix" style="padding: 5px 20px 0 15px">
        <span>
          <!--报表日期-->
          {{ $t('pages.dimBaseType') }}：
          <el-select v-model="query.dimBaseType" style="width: 140px;" @change="loadDimTime" >
            <el-option
              v-for="(item, index) in dateTypeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-cascader
            :key="cascadeKey"
            v-model="query.dimValue"
            :options="dateOptions"
            :props="{ expandTrigger: 'hover' }"
            :separator="$t('pages.cascaderSep')"
            style="width: 150px; margin-right: 10px;line-height:0"
            @change="dimValueChange"
          ></el-cascader>
        </span>
        <span>
          <label style="color: #F56C6C">*</label>{{ $t('form.dataItem') }}<el-tooltip effect="dark" placement="bottom-end">
            <div slot="content">
              {{ $t('pages.customDataItemTip', { num: 5 }) }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>：
          <!--数据项中的数据用了原来列表设置中的值，页面中获取到选中值后，在点击搜索之前对数据进行处理，实现搜索后需要的表格列数据-->
          <el-select v-model="query.customDataItems" multiple filterable collapse-tags :multiple-limit="5" is-filter :placeholder="$t('text.select')" style="width: 250px; margin-right: 10px;" @change="getDataIItemChange">
            <el-option
              v-for="(item) in customList"
              :key="item.prop"
              :label="$t(`table.${item.label}`)"
              :value="item.prop"
              :disabled="isDisabled(item.prop)"
            ></el-option>
          </el-select>
        </span>
        <!--高级查询（查询条件太多了，页面放不下）-->
        <el-popover
          v-model="visibleAdvanced"
          :append-to-body="false"
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchLeftForm" label-position="right" :model="query" label-width="65px">
            <!--统计类型-->
            <FormItem :label="$t('pages.countByObject')">
              <el-select v-model="query.countByObject" is-filter :placeholder="$t('text.select')" @change="handleCount">
                <el-option
                  v-for="(item, index) in typeOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  :disabled="(onlyOperator&&item.value===2) || (onlyTerminal&&item.value===1)"
                ></el-option>
              </el-select>
            </FormItem>
            <FormItem :label="$t('pages.countObject')">
              <tree-select
                ref="objectTree"
                node-key="id"
                class="targetObject"
                :height="350"
                :width="500"
                clearable
                multiple
                check-strictly
                :menu-name="menuName"
                :click-node="clickNode"
                :local-search="false"
                :checked-keys="checkedKeys"
                is-filter
                style="margin-left: -5px;width: 264px"
                :data="query.countByObject == 2 ? reportTermTree : reportUserTree"
                :get-search-list="getSearchListFunction"
                @deptTreeData="deptTreeData"
                @change="(key, nodeData) => entityIdChange(key, nodeData, query)"
              />
            </FormItem>
            <FormItem label="Top">
              <el-select ref="top" v-model="query.recordSize" allow-create filterable @visible-change="visibleChange">
                <el-option
                  v-for="(item, index) in sizeOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </FormItem>
            <!--排序项-->
            <FormItem :label="$t('pages.sortField')">
              <el-select v-model="query.sortName" filterable>
                <el-option
                  v-for="(item, index) in sortModels"
                  :key="index"
                  :label="$t(`table.${item.label}`)"
                  :value="item.prop"
                ></el-option>
              </el-select>
            </FormItem>
            <FormItem label="">
              <!--按部门统计-->
              <el-checkbox v-show="showGroupType" v-model="query.groupType" :false-label="0" :true-label="1" :label="$t('pages.countByGroup')" class="group-type"></el-checkbox>
              <el-checkbox v-model="query.collectSubDept" :false-label="0" :true-label="1" :label="$t('pages.collectSubDept')" class="group-type" :disabled="disabledCollectSubDept"></el-checkbox>
            </FormItem>
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="search()">{{ $t('table.search') }}</el-button>
          </div>
          <el-button slot="reference" type="primary" class="search-icon-left" size="mini">{{ $t('button.advancedSearch') }}</el-button>
        </el-popover>
        <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="search">{{ $t('text.search') }}</el-button>
        <slot name="button"></slot>
        <!--图标切换-->
        <el-select v-model="showType" style="width: 150px; float: right;">
          <el-option :label="$t('pages.showType1')" :value="1"></el-option>
          <el-option :label="$t('pages.showType2')" :value="2"></el-option>
          <el-option :label="$t('pages.showType3')" :value="3"></el-option>
        </el-select>
      </div>
      <div class="table-container">
        <!--打印、导出-->
        <div class="no-print">
          <div style="display:flex;justify-content: flex-end;" >
            <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
            <el-button v-if="exportable" type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>
          </div>
        </div>
        <!--中间echarts图表-->
        <div v-if="showType!==3&&!echartsImg" style="height: 370px;">
          <el-row :gutter="10" class="dynamic-height">
            <el-col :lg="isShowChartInterpretation ? 20 : 24" style="margin-bottom: 5px;">
              <div class="panel-wrapper">
                <e-charts ref="charts" :charts-option="chartsOption" :x-axis-name="xAxisName" :y-axis-name="yAxisName"/>
              </div>
            </el-col>
            <!--图表解读-->
            <el-col v-if="isShowChartInterpretation" :lg="4" style="margin-bottom: 5px;height: 100%;">
              <div class="panel-wrapper unscramble">
                <div v-html="chartInterpretationContent"></div>
              </div>
            </el-col>
          </el-row>
        </div>
        <!--没看出来是啥，可能和中间echarts图表有什么关系，在图表不显示的时候显示这个-->
        <div v-if="echartsImg" style="height: 370px; padding: 10px 0px;">
          <img :src="echartsImg" />
        </div>
        <!--存在下钻表格时的返回按钮-->
        <el-button v-if="showType!==2 && showDetail" size="mini" type="primary" style="margin-bottom: 5px" @click="showDetail = false">
          <i class="el-icon-back"></i>
        </el-button>
        <!--echarts图表下方的表格-->
        <grid-table
          v-show="showType!==2 && !showDetail"
          ref="dataList"
          :col-model="colModel"
          :row-datas="rowDatas"
          :header-rendering="true"
          :default-sort="{ prop: ''}"
          :multi-select="false"
          :show-pager="false"
          :min-height="200"
          :style="showType===1 ? {'height':'calc(100vh - 590px)'} : {'height':'calc(100% - 90px)'}"
          :cell-style="cellStyle"
          @cell-click="cellClick"
        />
        <!--下钻表格-->
        <grid-table
          v-show="showType!==2 && showDetail"
          ref="detailList"
          :autoload="false"
          :row-data-api="getDetailData"
          :col-model="detailModel"
          :multi-select="false"
          :show-pager="true"
          :min-height="200"
          :style="showType===1 ? {'height':'calc(100vh - 600px)'} : {'height':'calc(100% - 115px)'}"
          :row-key="getRowKey"
        />
      </div>
    </div>
    <!-- 导出弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.export')"
      width="400px"
      :visible.sync="dialogVisibleExport"
      :close-on-click-modal="false"
      :modal="false"
    >
      <div>
        <el-radio v-model="exportAll" label="top">
          {{ $t('pages.exportTop') }}
          <el-input-number v-model="exportNum" style="width:160px;margin:5px 10px" :min="query.recordSize" :precision="0" :max="100000" :disabled="exportAll === 'all'"></el-input-number>
        </el-radio>
        <el-radio v-model="exportAll" label="all">
          {{ $t('pages.exportAll') }}
        </el-radio>
      </div>
      <span slot="footer" class="dialog-footer">
        <report-exporter
          :api="api"
          :data="buildExcelData"
          button-type="primary"
          :button-icon="false"
          :button-name="$t('button.confirm2')"
          @exported="dialogVisibleExport = false"
        />
        <el-button @click="dialogVisibleExport=false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>

    <!-- 查看详情弹窗 -->
    <Detail v-if="viewDetails" ref="detailDialog"/>
  </div>
</template>

<script>
import ECharts from '@/components/ECharts'
import { checkDetailAble, getDetailPage, getDimTime, getReportChart } from '@/api/report/baseReport/issueReport/issue'
import { convert, formatSeconds, deepClone } from '@/utils'
import moment from 'moment'
import ReportExporter from './ReportExporter'
import Detail from './Detail'
import { mapGetters } from 'vuex';
export default {
  name: 'StatisticalCustom',
  components: { ECharts, ReportExporter, Detail },
  props: {
    // 数据请求接口
    api: {
      type: String,
      default: ''
    },
    // 默认展示的数据列
    defaultColModel: {
      type: Array,
      default() {
        return []
      }
    },
    // 自定义数据列（列表设置中的复选框）
    customList: {
      type: Array,
      default() {
        return []
      }
    },
    // 统计类型
    countByObject: {
      type: Number,
      default: 2
    },
    terminalFilter: { type: Function, default: null },
    // 统计类型数组
    typeOptions: {
      type: Array,
      default() {
        return [{ value: 1, label: this.$t('pages.conutType1') }, { value: 2, label: this.$t('pages.conutType2') }]
      }
    },
    // 是否支持按部门统计
    showGroupType: {
      type: Boolean,
      default: true
    },
    // 横坐标名称
    xAxisLabel: {
      type: String,
      default: ''
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    },
    // 是否支持导出
    exportable: {
      type: Boolean,
      default: false
    },
    // 是否支持查看详情
    viewDetails: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 是否显示图表解读
      isShowChartInterpretation: false,
      chartInterpretationContent: `<div class="unscramble-title">图表解读：</div>
                1、从整体趋势上看，2024年硬件资产变更趋势较上个月同期<span class="unscramble-red">下降50%</span>。其中，5-6月<span class="unscramble-red">下降57%</span>，4-5月<span class="unscramble-green">上升15%</span>。<br/>
                2、从数量上看，<span class="unscramble-title">4月</span>变更数量最大，变更了<span class="unscramble-title">381</span>次，<span class="unscramble-red">12月</span>变更数量最小，变更了<span class="unscramble-green">53</span>次。<br/>
                结论：硬件资产变更还是保持在一个相对稳定的状态，还是<span class="unscramble-green">比较安全</span>的。`,
      // 是否只支持按操作员统计
      onlyOperator: false,
      // 是否只支持按终端统计
      onlyTerminal: false,
      // 互斥数据项
      mutexOptions: {
        'trwfeSumAll': ['hardwareAssetChangeTimes', 'softwareAssetChangeTimes'],
        'hardwareAssetChangeTimes': ['trwfeSumAll'],
        'softwareAssetChangeTimes': ['trwfeSumAll']
      },
      deptData: [],
      loading: false,
      // 统计对象，树相关参数
      checkedKeys: [],
      clickNode: [],
      menuName: 'reportMenu',
      // 明细数据
      detailMethod: getDetailPage,
      // echarts图表相关设置
      chartsInfo: undefined,
      chartsOption: [],
      defaultChartsOption: {
        type: 'pie',
        col: 24
      },
      // echarts下方表格列
      colModel: [
        { prop: 'name', label: 'name', width: '150', sort: true }
      ],
      // echarts下方表格数据
      rowDatas: [],
      // 下钻表格列
      detailModel: [],
      // 查询项
      query: {
        objectType: null,
        objectIds: '',
        groupIds: '',
        dimBaseType: 1,
        dimValue: '',
        countByObject: this.countByObject || 2,
        // 数据项
        customDataItems: [],
        recordSize: 5,
        groupType: 0,
        // 是否归集子部门：0-否，1-是
        collectSubDept: 0,
        sortName: '',
        sortOrder: 'desc'
      },
      // 报表日期相关配置
      dateTypeOptions: [
        { value: 5, label: this.$t('pages.dateTypeOptions1') },
        { value: 2, label: this.$t('pages.dateTypeOptions2') },
        { value: 1, label: this.$t('pages.dateTypeOptions3') },
        { value: 3, label: this.$t('pages.dateTypeOptions4') },
        { value: 4, label: this.$t('pages.dateTypeOptions5') }
      ],
      dateOptions: [],
      months: ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      quarters: ['', 'theFirst', 'theSecond', 'theThird', 'theFourth'],
      // Top选项
      sizeOptions: [{ value: 5, label: 5 }, { value: 10, label: 10 }, { value: 20, label: 20 }],
      // 看样子是根据值控制一下显示隐藏
      showType: 1,
      cascadeKey: 0,
      dialogVisible: false,
      dialogVisibleExport: false,
      visibleAdvanced: false,
      // 数据项
      curCustomList: [],
      // 这个是排序下拉框的选项所需要的属性，为了防止修改显示的列，但是没有点击保存，而selected还是会进行修改的问题
      selectedFinal: [],
      // 打印有用到，还有一些显示隐藏？？
      echartsImg: '',
      // 导出
      exportNum: 0,
      exportAll: 'top',
      showDetail: false,
      // 查询
      tempQuery: {},
      defaultDetailQuery: {
        page: 1,
        detailObjectIds: '',
        objectIds: '',
        objectNames: [],
        detailTable: ''
      },
      detailQuery: {},
      // 统计对象终端树和操作员树（报表要过滤需要自定义一个字段进行过滤，否则会影响到别的页面的数据）
      reportTermTree: [],
      reportUserTree: [],
      /** objectIds objectNames是用来查看明细时，点击查看其他对象明细时用到 */
      objectIds: [],
      objectNames: []
    }
  },
  computed: {
    // 缓存好的终端树和操作员树
    ...mapGetters([
      'termTree',
      'termTreeList',
      'userTree',
      'userTreeList'
    ]),
    // echarts图表下方的表格
    gridTable() {
      return this.$refs['dataList']
    },
    // 柱状图横坐标名称设置
    xAxisName() {
      const label = this.xAxisLabel ? this.xAxisLabel : this.query.countByObject === 1 ? this.$t('table.user') : this.$t('table.terminal')
      return this.query.groupType === 1 ? `${label}${this.$t('table.dept')}` : label
    },
    // 柱状图纵坐标名称设置
    yAxisName() {
      return ''
    },
    // 排序项下拉框
    sortModels() {
      return this.selectedFinal.map(item => {
        return {
          label: item.label,
          prop: item.prop
        }
      })
    },
    // 是否禁用归集子部门
    disabledCollectSubDept() {
      return this.query.groupType === 0
    }
  },
  watch: {
    // 终端树发生变化时，监听
    termTree() {
      const tree = JSON.parse(JSON.stringify(this.termTree))
      this.filterTermTree(tree)
      this.reportTermTree = tree
    },
    userTree() {
      this.reportUserTree = JSON.parse(JSON.stringify(this.userTree))
    },
    disabledCollectSubDept(newValue) {
      if (newValue) {
        this.query.collectSubDept = 0
      }
    }
  },
  created() {
    this.loading = true
    this.loadDimTime('initial')
    // 初始化统计对象树赋值
    if (this.termTree !== undefined) {
      const tree = JSON.parse(JSON.stringify(this.termTree))
      this.filterTermTree(tree)
      this.reportTermTree = tree
    }
    if (this.userTree !== undefined) {
      this.reportUserTree = JSON.parse(JSON.stringify(this.userTree))
    }
  },
  methods: {
    /**
     * 终端树过滤（老板终端、U判断终端、永久离线终端）
     * */
    filterTermTree(termTree) {
      for (let i = termTree.length - 1; i >= 0; i--) {
        const item = termTree[i]
        if (item.dataType === 'G') {
          this.filterTermTree(item.children || [])
          continue
        }
        const type = (parseInt(item.dataType) & 0xf0) >> 4
        if (type === 1 || type === 2 || type === 8) {
          termTree.splice(i, 1)
        }
      }
    },
    /**
     * 因为data数据为外传数据，所以需要从外部传入搜索列表（内置数据才会有搜索列表）
     * */
    getSearchListFunction() {
      if (this.query.countByObject == 2) {
        // 终端
        return this.termTreeList
      } else {
        // 操作员
        return this.userTreeList
      }
    },
    /**
     * 获取统计对象树结构数据（TreeSelect组件传递给父组件数据）
     * 注：该数据不是一次性全部加载出来的，在组件数据展开时会发生变化
     * */
    deptTreeData(val) {
      this.deptData = val
    },
    /**
     * 遍历统计对象树结构数据
     * @param nodes 当前树结构数据
     * @param nodeData 当前选中的数据（因为通过选中判断是否可选所以判断是取第一个即可）
     * (直接点击)nodeData[0].type 1、终端，2、操作员，3、终端分组，4、操作员分组
     * (查询点击)nodeData[0].type 1、终端，2、操作员，G、终端分组，G、操作员分组
     * */
    getCheckIdsAll(nodes = [], nodeData) {
      if (nodes != null && nodes.length > 0) {
        nodes.forEach(item => {    // 遍历树 拼入相应的disabled
          if (nodeData.length > 0) {
            if (JSON.stringify(item) !== '{}') {
              if (nodeData[0].type !== item.type) {
                this.$set(item, 'disabled', true)
                this.getCheckIdsAll(item.children, nodeData)
              } else {
                this.$set(item, 'disabled', false)
                this.getCheckIdsAll(item.children, nodeData)
              }
            }
          } else {
            this.$set(item, 'disabled', false)
            this.getCheckIdsAll(item.children, nodeData)
          }
        })
      }
    },
    /**
     * 统计部门选中时做禁用操作
     * */
    entityIdChange(key, nodeData, query) {
      this.query.objectType = this.query.countByObject === 1 ? 2 : 1
      if (nodeData && nodeData.length > 0) {
        const objectIds = []
        const groupIds = []
        nodeData.forEach(item => {
          if (item.dataType === 'G') {
            groupIds.push(item.dataId)
          } else {
            objectIds.push(item.dataId)
          }
        })
        this.query.objectIds = objectIds.join(',')
        this.query.groupIds = groupIds.join(',')
      } else {
        this.query.objectIds = ''
        this.query.groupIds = ''
      }
    },
    /**
     * 获取下钻表格数据
     * */
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      return this.detailMethod(searchQuery)
    },
    /**
     *表格点击--下钻（样式）
     *可以下钻的，鼠标悬停变成小手指
     * */
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (!this.drillDown) {
        return ''
      }
      let cellStyle = '';
      this.colModel.forEach(item => {
        if (item.prop === column.property && item.hasOwnProperty('detailCol')) {
          cellStyle = 'cursor: pointer;'
        }
      })
      return cellStyle;
    },
    /**
     *表格点击--下钻
     *单元格点击数字查看明细
     * */
    cellClick(row, column, cell, event) {
      if (!this.drillDown) {
        return
      }
      let col = null
      this.colModel.forEach(item => {
        if (item.prop === column.property) {
          col = item
        }
      })
      if (!col || (!col.detailTable && !col.getDetailMethod)) {
        return
      }

      // 替换终名称、用户名称、分组名称等的渲染函数
      for (const i of col.detailCol) {
        if (i.prop == 'user_name') {
          i.formatter = (row) => {
            if (!row.user_id || row.user_id == -1) {
              return this.$t('pages.null')
            }
            return row.user_name
          }
        }
        if (i.prop == 'user_group_name') {
          i.formatter = (row) => {
            if (!row.user_group_id || row.user_group_id == -1) {
              return ''
            }
            return row.user_group_name
          }
        }

        if (i.prop == 'terminal_name') {
          i.formatter = (row) => {
            if (!row.term_id || row.term_id == -1) {
              return this.$t('pages.null')
            }
            return row.terminal_name
          }
        }
        if (i.prop == 'term_group_name') {
          i.formatter = (row) => {
            if (!row.term_group_id || row.term_group_id == -1) {
              return ''
            }
            return row.term_group_name
          }
        }
      }

      // 绑定查看详情事件
      const operateCol = col.detailCol.find(i => i.label === 'operate')
      if (operateCol) {
        const detailBtn = operateCol.buttons.find(j => j.label === 'detail')
        if (detailBtn) {
          detailBtn.click = this.handleDetail
        }
      }

      // 重置明细查询条件
      this.detailQuery = deepClone(this.defaultDetailQuery)
      this.detailQuery = Object.assign(this.detailQuery, this.tempQuery)

      // 验证是否可以查看明细，因为报表系统会有清理历史数据功能，所以有些时间是不支持查看明细的。
      checkDetailAble(this.detailQuery).then(res => {
        // 明细表格列重新配置
        this.detailModel.splice(0, this.detailModel.length, ...col.detailCol)
        // 如果表格列配置里面没有detailTable字段，说明不需要显示详情
        // 设置查询明细的条件
        this.showDetail = true
        this.detailQuery.detailTable = col.detailTable

        // 设置指定显示的对象
        if (this.query.collectSubDept === 1) {
          this.detailQuery.groupIds = row.labelValue
        } else {
          this.detailQuery.detailObjectIds = row.labelValue
          this.detailQuery.objectNames.splice(0, 1, row.labelValue + this.dealUnknown(row.label) + this.dealUnknown(row.groupName))
        }

        if (col && col.beforeLoadDetail) {
          // 前置处理
          this.detailQuery = Object.assign({}, this.detailQuery, col.beforeLoadDetail(row, column, cell, event, this.detailQuery))
        } else {
          this.detailQuery = Object.assign({}, this.detailQuery)
        }

        if (col && col.getDetailMethod) {
          // 表格详情的数据加载方法走特殊处理
          this.detailMethod = col.getDetailMethod
        } else {
          this.detailMethod = getDetailPage
        }
        // 搜索条件的过滤对象id
        this.detailQuery.objectType = this.query.objectType
        this.detailQuery.objectIds = this.query.objectIds
        this.$refs.detailList.execRowDataApi(this.detailQuery)
      })
    },
    /** 格式化二级表格数据的内容，把名称和id加进去 */
    formatDetailLabelValue(info) {
      if (info.oriData) {
        this.objectIds.splice(0)
        this.objectNames.splice(0)
        info.oriData.forEach(item => {
          this.objectIds.push(item.labelValue)
          this.objectNames.push(item.labelValue + this.dealUnknown(item.label) + this.dealUnknown(item.groupName))
        })
      }
    },
    /**
     * 报表日期下拉框选择时前端相关交互
     * */
    loadDimTime(value) {
      const formatterOption = (dimValue) => {
        const dimBaseType = this.query.dimBaseType
        const dimYear = dimValue.substring(0, 4)
        if (dimBaseType === 4) {
          this.dateOptions.push({ label: this.$t('pages.year', { Year: dimYear }), value: dimValue })
        } else {
          if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
          if (dimBaseType === 1) {
            this.dateOptions[dimYear].children.push({ label: this.$t('text.' + this.months[parseInt(dimValue.substring(4))]), value: dimValue })
          } else if (dimBaseType === 2) {
            this.dateOptions[dimYear].children.push({ label: this.$t('pages.weekNum', { Week: dimValue.substring(4) }), value: dimValue })
          } else if (dimBaseType === 3) {
            this.dateOptions[dimYear].children.push({ label: this.$t('pages.quarterNum', { Quarter: this.$t('pages.' + this.quarters[parseInt(dimValue.substring(4))]) }), value: dimValue })
          } else if (dimBaseType === 5) {
            const month = dimValue.substring(4, 6)
            const day = dimValue.substring(6, 8).startsWith('0') ? dimValue.substring(7, 8) : dimValue.substring(6, 8)
            const monthChildren = this.dateOptions[dimYear].children
            const index = monthChildren.findIndex(item => item.label === this.$t('text.' + this.months[parseInt(month)]))
            index < 0 && monthChildren.push({ label: this.$t('text.' + this.months[parseInt(month)]), value: dimValue.substring(0, 6), children: [{ label: this.$t('pages.dateNum', { date: day }), value: dimValue }] })
            index >= 0 && monthChildren[index].children.push({ label: this.$t('pages.dateNum', { date: day }), value: dimValue })
          }
        }
      }
      getDimTime(this.query.dimBaseType).then(response => {
        this.dateOptions.splice(0)
        response.data.sort((a, b) => {
          return a > b ? 1 : -1 // 返回正数 ，b排列在a之前
        })
        for (let i = 0; i < response.data.length; i++) {
          let time = response.data[i] + ''
          while (time.length < 6) { time += '0' }
          formatterOption(time)
        }
        ++this.cascadeKey
        const children = this.dateOptions[this.dateOptions.length - 1].children
        if (this.query.dimBaseType === 4) {
          this.query.dimValue = this.dateOptions[this.dateOptions.length - 1].value
        } else if (this.query.dimBaseType === 5) {
          this.query.dimValue = children[children.length - 1].children[0].value
        } else {
          this.query.dimValue = children[children.length - 1] ? children[children.length - 1].value : ''
        }
        value === 'initial' && this.loadReport()
      }).catch(() => {
        if (this.loading) {
          this.loading = false
        }
      })
    },
    /**
     * 报表日期后面下拉框选中发生变化操作
     * */
    dimValueChange(datas) {
      if (datas && datas.length > 0) {
        this.query.dimValue = datas[datas.length - 1]
      } else {
        this.query.dimValue = undefined
      }
    },
    /**
     * 高级查询重置
     * */
    resetQuery() {
      this.query.countByObject = this.onlyOperator ? 1 : 2
      this.query.recordSize = 5
      this.query.sortName = this.selectedFinal.length > 0 ? this.selectedFinal[0].prop : ''
      this.query.groupType = 0
      this.query.collectSubDept = 0
      this.query.groupIds = ''
      this.query.objectIds = ''
      this.checkedKeys = []
      this.$emit('getCountByObject', this.query.countByObject)
    },
    /**
     * 数据项下拉框发生变化时
     * 1、通过选中值，获取到defaultColModel数据，用户表格列查询项的显示
     * 2、通过数据项选中值，获取到排序项下拉框selectedFinal中的值（注：数据项选中值后，排序项才会有值）
     * */
    getDataIItemChange() {
      this.defaultColModel.splice(1)
      const currentData = this.query.customDataItems
      this.selectedFinal = []
      this.query.sortName = ''
      if (currentData.length > 0) {
        for (let i = 0; i < currentData.length; i++) {
          for (let j = 0; j < this.customList.length; j++) {
            if (currentData[i] === this.customList[j].prop) {
              this.defaultColModel.push(this.customList[j])
              this.selectedFinal.push(this.customList[j])
            }
          }
        }

        // 默认第一项为排序项
        this.query.sortName = this.selectedFinal[0].prop

        // 数据项包含审批数时，统计类型只能为“按操作员统计”
        if (currentData.includes('trwfeSumAll')) {
          this.query.countByObject = 1
          this.onlyOperator = true
        } else {
          this.onlyOperator = false
        }

        // 数据项包含资产变更数时，统计类型只能为“按终端统计”
        if (currentData.includes('hardwareAssetChangeTimes') || currentData.includes('softwareAssetChangeTimes')) {
          this.query.countByObject = 2
          this.onlyTerminal = true
        } else {
          this.onlyTerminal = false
        }
      }
    },
    search() {
      if (this.query.customDataItems.length === 0) {
        this.$notify({ title: this.$t('text.error'), message: this.$t('valid.requireDataItem'), type: 'error', duration: 2000 })
        return
      }

      if (this.query.collectSubDept === 1 && !this.query.groupIds) {
        this.$notify({ title: this.$t('text.error'), message: '请至少选择一个部门！', type: 'error', duration: 2000 })
        return
      }

      this.loadReport()
    },
    /**
     * 搜索查询方法实现
     */
    loadReport() {
      if (!this.loading) {
        this.loading = true
      }
      this.showDetail = false
      this.tempQuery = Object.assign({}, this.query)
      if (this.query.recordSize > 20) {
        this.showType = 3
      }
      if (this.query.objectIds === '') {
        this.query.objectType = this.query.countByObject === 1 ? 2 : 1
      }
      getReportChart(this.query, this.api).then(respond => {
        this.loading = false
        this.chartsInfo = respond.data
        if (this.chartsInfo) {
          this.formatDetailLabelValue(this.chartsInfo)
          this.changeConfig(this.chartsInfo, this.defaultColModel, this.chartsInfo.oriData)
        }
      }).catch(() => {
        this.loading = false
      })
      this.visibleAdvanced = false
    },
    /**
     * 搜索实现过程中的一些变化配置
     * */
    changeConfig(chartsOption, colModel, rowDatas) {
      this.colModel.splice(0, this.colModel.length, ...colModel)
      this.rowDatas.splice(0, this.rowDatas.length, ...rowDatas)
      chartsOption.option.tooltip = {
        formatter: (d) => {
          let res = this.html2Escape(d[0].axisValueLabel) + '<br>';
          for (var i = 0; i < d.length; i++) {
            let value = d[i].data
            const col = this.getCol(d[i].seriesName);
            if (col != null) {
              const data = chartsOption.oriData[d[i].dataIndex]
              value = this.valueFormat(data, value, col)
            }
            res += d[i].seriesName + ' : ' + value + '<br>'
          }
          return res
        },
        confine: true,
        extraCssText: 'white-space: break-spaces;'
      }
      if (chartsOption.option.xAxis) {
        chartsOption.option.xAxis[0].axisLabel = {
          formatter: (value) => {
            // 一行显示10个字符，最多显示3行，超过显示...
            var maxLength = 10;
            value = value.length > 30 ? value.substring(0, 27) + '...' : value
            var valLength = value.length;
            var rowN = Math.ceil(valLength / maxLength);
            if (rowN > 1) {
              // 根据行数计算每行的字符数
              const partLength = Math.floor(valLength / rowN);
              const result = [];
              let startIndex = 0;
              // 切割并放入result
              for (let i = 0; i < rowN; i++) {
                if (i == rowN - 1) {
                  result.push(value.substring(startIndex));
                } else {
                  result.push(value.substring(startIndex, startIndex + partLength));
                }
                startIndex += partLength;
              }
              return result.join('\n')
            } else {
              return value;
            }
          }
        }
      }
      this.chartsOption.splice(0, 1, chartsOption)
      // 大小转换
      this.gridTable.colModel.forEach(item => {
        if (item.flag === 'size') {
          item.formatter = (row, data) => {
            return convert(data)
          }
        }
      })
      this.colModel[0].label = this.xAxisName
      this.colModel[0].fixed = true
      // 根据首个colMode类型，来判断用什么详情组件(terminal、user、department)
      this.handleSearchType(this.colModel[0]);
      if (this.showGroupType) {
        const index = this.colModel.findIndex(item => item.prop === 'groupName')
        if (this.query.groupType === 0) {
          index < 0 && this.colModel.splice(1, 0, { prop: 'groupName', label: 'dept', width: '150', sort: true, formatter: this.groupNameFormatter })
        } else {
          index > 0 && this.colModel.splice(index, 1)
        }
      }
    },
    groupNameFormatter(row, data) {
      if (data && data.lastIndexOf('(') >= 0) {
        data = data.substr(0, data.lastIndexOf('('))
      }
      return this.html2Escape(data);
    },
    /**
     * 搜索相关配置
     * 根据首个colMode类型，来判断用什么详情组件(terminal、user、department)
     * */
    handleSearchType(colModel) {
      if (undefined !== colModel.formatter) {
        // 因下钻饼状图时无labelValue数据，暂时就先滤过
        return;
      }
      colModel.type = 'showDetail';
      colModel.searchParam = 'labelValue';
      if (this.$t('table.terminal') === colModel.label) {
        colModel.searchType = 'terminal';
      } else if (this.$t('table.user') === colModel.label || this.$t('table.applicant') === colModel.label) {
        colModel.searchType = 'user';
      } else if (colModel.label !== null && colModel.label.indexOf(this.$t('table.dept')) > -1) {
        colModel.searchType = 'department';
      } else {
        colModel.type = undefined;
        colModel.searchType = undefined;
        colModel.searchParam = undefined;
      }
    },
    /**
     * 图表鼠标悬停时，获取表格列信息，在图表上显示具体信息（某一项名称）
     * */
    getCol(seriesName) {
      return this.customList.find(item => {
        return this.$t('table.' + item.label) === seriesName
      })
    },
    /**
     * 图表鼠标悬停时，获取表格列信息，在图表上显示具体信息（某一项值）
     * */
    valueFormat(data, value, colModel) {
      if (colModel.flag === 'size') {
        value = convert(value)
      } else if (colModel.flag === 'time') {
        value = formatSeconds(value)
      } else if (colModel.formatter) {
        return colModel.formatter(data, value)
      }
      return value
    },
    /**
     * 打印
     * */
    handlePrint() {
      if (!this.chartsInfo) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.compareReport24'),
          type: 'error'
        })
        return
      }
      this.colModel[0].fixed = false
      this.echartsImg = this.$refs.charts ? this.$refs.charts.$refs.bar[0].chart._api.getDataURL({ excludeComponents: ['toolbox'] }) : ''
      setTimeout(() => {
        this.$print(this.$refs.print)
      }, 100)
      setTimeout(() => {
        this.echartsImg = ''
        this.colModel[0].fixed = true
      }, 200)
    },
    /**
     * 导出
     * */
    handleExport() {
      this.exportNum = this.query.recordSize
      this.dialogVisibleExport = true
    },
    /**
     * 导出一些操作配置
     * */
    buildExcelData() {
      if (!this.chartsInfo) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.compareReport24'),
          type: 'error'
        })
        return
      }
      let imgBase64Info = ''
      if (this.$refs.charts) {
        imgBase64Info = this.$refs.charts.$refs.bar[0].chart._api.getDataURL({
          excludeComponents: ['toolbox'],
          backgroundColor: '#fff'
        })
        this.$refs.charts.$refs.bar[0].chart.resize()
      }

      if (this.exportAll === 'all') {
        this.exportNum = 0
      }
      this.tempQuery.recordSize = this.exportNum

      const sortModel = this.sortModels.find(sortModel => sortModel.prop === this.tempQuery.sortName)
      const sortLabel = sortModel ? this.$t(`table.${sortModel.label}`) : ''
      return Object.assign(this.tempQuery, {
        imgBase64Info,
        showType: this.showType,
        sortLabel: sortLabel
      })
    },
    /**
     * 统计类型选中发生变化时
     * */
    handleCount(value) {
      this.checkedKeys.splice(0)
      this.query.objectType = null
      this.query.objectIds = ''
      this.query.groupIds = ''
      // console.log('this.query', JSON.parse(JSON.stringify(this.query)))
      // 切换时，清除对统计对象的选中和禁用操作
      this.clickNode = []
      this.getCheckIdsAll(this.deptData, this.clickNode)
      this.$emit('getCountByObject', value)
    },
    /**
     * top下拉框出现、隐藏时，设置文本值
     * */
    visibleChange() {
      const value = this.query.recordSize
      const reg = new RegExp(/^[1-9]\d*$/)
      if (!reg.test(value)) {
        this.query.recordSize = 5
      } else {
        this.query.recordSize = parseInt(value)
      }
    },
    /**
     * 获取row-key(下钻表格)
     * */
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    },
    /**
     * 当值是“未知”时进行特殊处理（因为报表数据库中存储的值是“未知”，所以在查询时需要进行转换）
     * @param value
     * @returns {string|*}
     */
    dealUnknown(value) {
      if (value === (this.$t('text.unknown') + '(-1)')) {
        return this.$t('text.unknown') + '(-1)'
      }
      if (value === this.$t('text.unknown')) {
        return this.$t('text.unknown')
      }
      return value
    },
    /**
     * 查看详情事件
     * @param row 当前行数据
     */
    handleDetail(row) {
      if (this.$refs.detailDialog) {
        this.$refs.detailDialog.visible = true
        this.$refs.detailDialog.rowData = row
        this.$refs.detailDialog.detailCol = this.detailModel
      }
    },
    /**
     * 数据项禁用
     * @param value
     * @returns {boolean}
     */
    isDisabled(value) {
      return this.query.customDataItems.some(option => this.mutexOptions[option] && this.mutexOptions[option].includes(value))
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container{
  display: block;
  height: calc(100vh - 150px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 5px;
}
>>>.el-select{
  width: 100%;
}
</style>
