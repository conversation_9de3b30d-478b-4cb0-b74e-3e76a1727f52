<template>
  <div ref="print" class="app-container" style="overflow:hidden;">
    <div v-loading="loading">
      <div class="toolbar clearfix" style="padding: 5px 20px 0 15px">
        <span>
          {{ $t('pages.dimBaseType') }}：
          <el-select ref="dimType" v-model="query.dimBaseType" style="width: 135px;" @change="loadDimTime" >
            <el-option
              v-for="(item, index) in dateTypeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-cascader
            ref="cascader"
            :key="cascaderKey"
            v-model="query.dimValue"
            :options="dateOptions"
            :props="{ expandTrigger: 'hover' }"
            :separator="$t('pages.cascaderSep')"
            style="width: 160px; margin-right: 10px;line-height:0"
            @change="dimValueChange"
          ></el-cascader>
        </span>
        <span>
          {{ $t('pages.countByObject') }}：
          <el-select v-model="query.countByObject" is-filter :placeholder="$t('text.select')" style="width: 150px; margin-right: 10px;">
            <el-option
              v-for="(item, index) in typeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </span>
        <el-button type="primary" size="mini" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
        <el-select v-model="showType" style="width: 150px; margin-right: 10px; float: right;">
          <el-option :label="$t('pages.showType1')" :value="1"></el-option>
          <el-option :label="$t('pages.showType2')" :value="2"></el-option>
          <el-option :label="$t('pages.showType3')" :value="3"></el-option>
        </el-select>
      </div>
      <div class="table-container">
        <div class="no-print">
          <div style="display:flex;justify-content: flex-end;" >
            <el-button v-show="showType!==2" type="text" @click.native="handleColumn"><i class="el-icon-s-tools"></i>{{ $t('pages.handleColumn') }}</el-button>
            <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
            <report-exporter v-if="exportable" :api="api" :data="buildExcelData"/>
          </div>
        </div>
        <div v-if="showType!=3&&!echartsImg" style="height: 370px;">
          <el-row :gutter="10" class="dynamic-height">
            <el-col :lg="isShowChartInterpretation ? 20 : 24" style="margin-bottom: 5px;">
              <div class="panel-wrapper">
                <e-charts ref="charts" :charts-option="chartsOption" />
              </div>
            </el-col>
            <!--图表解读-->
            <el-col v-if="isShowChartInterpretation" :lg="4" style="margin-bottom: 5px;height: 100%;">
              <div class="panel-wrapper unscramble">
                <div v-html="chartInterpretationContent"></div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div v-if="echartsImg" style="height: 370px; padding: 10px 0px;">
          <img :src="echartsImg" />
        </div>
        <el-button v-if="showType!==2 && showDetail" size="mini" type="primary" style="margin-bottom: 5px" @click="showDetail = false">
          <i class="el-icon-back"></i>
        </el-button>
        <grid-table
          v-show="showType!=2 && !showDetail"
          ref="dataList"
          :col-model="colModel"
          :row-datas="rowDatas"
          :default-sort="{ prop: ''}"
          :multi-select="false"
          :show-pager="false"
          :min-height="200"
          :not-limit-height="!!echartsImg|| onlyTabel"
          :style="showType===1 ? {'height':'calc(100vh - 565px)'} : {'height':'calc(100% - 90px)'}"
          :cell-style="cellStyle"
          @cell-click="cellClick"
        />
        <grid-table
          v-show="showType!==2 && showDetail"
          ref="detailList"
          :autoload="false"
          :row-data-api="getDetailData"
          :col-model="detailModel"
          :multi-select="false"
          :show-pager="true"
          :min-height="200"
          :not-limit-height="!!echartsImg||onlyTabel"
          :style="showType===1 ? {'height':'calc(100vh - 600px)'} : {'height':'calc(100% - 115px)'}"
          :row-key="getRowKey"
        />
      </div>
    </div>
    <!-- 列表设置 -->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.handleColumn')"
      :width="dialogWidth"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :modal="false"
    >
      <div class="checkbox">
        <h4>{{ $t('pages.report_text1') }}</h4>
        <div v-show="selected.length===0" style="color:red;font-size:12px;margin-bottom:10px">{{ $t('pages.report_text2') }}</div>
        <div>
          <el-button type="text" @click="handleCheckAllChange">{{ $t('button.selectAll') }}</el-button> /
          <el-button type="text" @click="handleCheckNone">{{ $t('button.selectNone') }}</el-button>
        </div>
        <el-checkbox-group v-model="selected">
          <el-checkbox
            v-for="(item) in customList"
            :key="item.prop"
            :label="item"
            :class="{ 'width-50-percent': customList.length > 6 }"
          >{{ item.escapeFormat ? item.label : ($te(`table.${item.label}`) ? $t(`table.${item.label}`) : item.label) }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleChoose">{{ $t('button.confirm2') }}</el-button>
        <el-button @click="dialogVisible=false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>

    <!-- 查看详情弹窗 -->
    <Detail v-if="viewDetails" ref="detailDialog"/>
  </div>
</template>

<script>
import ECharts from '@/components/ECharts'
import { checkDetailAble, getDimTime, getReportChart, getTrendDetail } from '@/api/report/baseReport/issueReport/issue'
import { convert, flowAxis, formatSeconds, timeAxis } from '@/utils'
import moment from 'moment'
import ReportExporter from './ReportExporter'
import Detail from './Detail'

export default {
  name: 'Trend',
  components: { ECharts, ReportExporter, Detail },
  props: {
    api: {
      type: String,
      default: ''
    },
    colModel: {
      type: Array,
      default() {
        return []
      }
    },
    customList: {
      type: Array,
      default() {
        return []
      }
    },
    conversionTime: {
      type: Boolean,
      default() {
        return false
      }
    },
    conversionFlow: {
      type: Boolean,
      default() {
        return false
      }
    },
    formatOption: { // 自定义格式化显示参数
      type: Function,
      default(option) {
      }
    },
    dateTypeOptions: {
      type: Array,
      default() {
        return [
          { value: 5, label: this.$t('pages.trendDateTypeOptions1') },
          { value: 3, label: this.$t('pages.trendDateTypeOptions2') },
          { value: 4, label: this.$t('pages.trendDateTypeOptions3') },
          { value: 2, label: this.$t('pages.trendDateTypeOptions4') },
          { value: 1, label: this.$t('pages.trendDateTypeOptions5') }
        ]
      }
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    },
    // 是否支持导出
    exportable: {
      type: Boolean,
      default: false
    },
    // 是否支持查看详情
    viewDetails: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 是否显示图表解读
      isShowChartInterpretation: false,
      chartInterpretationContent: `<div class="unscramble-title">图表解读：</div>
                1、从整体趋势上看，2024年硬件资产变更趋势较上个月同期<span class="unscramble-red">下降50%</span>。其中，5-6月<span class="unscramble-red">下降57%</span>，4-5月<span class="unscramble-green">上升15%</span>。<br/>
                2、从数量上看，<span class="unscramble-title">4月</span>变更数量最大，变更了<span class="unscramble-title">381</span>次，<span class="unscramble-red">12月</span>变更数量最小，变更了<span class="unscramble-green">53</span>次。<br/>
                结论：硬件资产变更还是保持在一个相对稳定的状态，还是<span class="unscramble-green">比较安全</span>的。`,
      loading: false,
      chartsOption: [],
      defaultChartsOption: {
        type: 'line',
        col: 24
      },
      rowDatas: [],
      query: {
        isTrend: 1,
        dimBaseType: 2,
        dimValue: '',
        countByObject: 1,
        recordSize: 0,
        groupType: 0,
        selectedColumn: {}
      },
      dateOptions: [],
      dateTableName: ['', this.$t('text.quarter'), this.$t('text.month'), this.$t('pages.week'), this.$t('text.month'), this.$t('table.day')],
      typeOptions: [{ value: 1, label: this.$t('pages.report_text4') }],
      showType: 1,
      chartsInfo: undefined,
      // selectedDimValue: [],
      cascaderKey: 0,
      dialogVisible: false,
      selected: [],
      selectedFinal: [],
      echartsImg: '',
      onlyTabel: false,
      tempQuery: {},
      showDetail: false,
      detailModel: [],
      detailQuery: {
        isTrend: 1,
        page: 1,
        detailTable: '',
        label: ''
      },
      detailMethod: getTrendDetail,
      /** objectIds objectNames是用来查看明细时，点击查看其他对象明细时用到 */
      objectIds: [],
      objectNames: [],
      months: ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      quarters: ['', 'theFirst', 'theSecond', 'theThird', 'theFourth']
    }
  },
  computed: {
    gridTable() {
      return this.$refs['dataList']
    },
    dialogWidth() {
      return this.customList.length > 6 ? '800px' : '400px'
    }
  },
  watch: {
    showType(value) {
    }
  },
  created() {
    this.loading = true
    this.loadDimTime('initial')
    this.chartsOption.splice(0, 1, this.defaultChartsOption)
    const arr = this.colModel.map(item => item.prop)
    this.selected = this.customList.filter(item => {
      return arr.indexOf(item.prop) !== -1
    })
    this.selectedFinal.push(...this.selected)
  },
  methods: {
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      return this.detailMethod(searchQuery)
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (!this.drillDown) {
        return ''
      }
      let cellStyle = '';
      this.colModel.forEach(item => {
        if (item.prop === column.property && item.hasOwnProperty('detailCol')) {
          cellStyle = 'cursor: pointer;'
        }
      })
      return cellStyle;
    },
    cellClick(row, column, cell, event) {  // 单元格点击数字查看明细
      if (!this.drillDown) {
        return
      }
      let col = null
      this.colModel.some(item => {
        if (item.prop == column.property) {
          col = item
        }
        return item.prop == column.property
      })
      // 如果表格列配置里面没有detailTable字段，说明不需要显示详情
      if (!col || (!col.detailTable && !col.getDetailMethod)) {
        return
      }
      // 绑定查看详情事件
      const operateCol = col.detailCol.find(i => i.label === 'operate')
      if (operateCol) {
        const detailBtn = operateCol.buttons.find(j => j.label === 'detail')
        if (detailBtn) {
          detailBtn.click = this.handleDetail
        }
      }
      // 明细表格列重新配置
      this.detailModel.splice(0, this.detailModel.length, ...col.detailCol)
      // 设置查询明细的条件
      this.detailQuery = Object.assign(this.detailQuery, this.tempQuery)
      this.detailQuery.page = 1
      this.detailQuery.label = row.label
      this.detailQuery.detailTable = col.detailTable
      this.detailQuery.condition = {}

      if (col.beforeLoadDetail) {
        // 前置处理
        this.detailQuery = Object.assign({}, this.detailQuery, col.beforeLoadDetail(row, column, cell, event, this.detailQuery))
      } else {
        this.detailQuery = Object.assign({}, this.detailQuery)
      }

      // 替换终名称、用户名称、分组名称等的渲染函数
      for (const i of col.detailCol) {
        if (i.prop == 'user_name') {
          i.formatter = (row) => {
            if (!row.user_id || row.user_id == -1) {
              return this.$t('pages.null')
            }
            return row.user_name
          }
        }
        if (i.prop == 'user_group_name') {
          i.formatter = (row) => {
            if (!row.user_group_id || row.user_group_id == -1) {
              return ''
            }
            return row.user_group_name
          }
        }

        if (i.prop == 'terminal_name') {
          i.formatter = (row) => {
            if (!row.term_id || row.term_id == -1) {
              return this.$t('pages.null')
            }
            return row.terminal_name
          }
        }
        if (i.prop == 'term_group_name') {
          i.formatter = (row) => {
            if (!row.term_group_id || row.term_group_id == -1) {
              return ''
            }
            return row.term_group_name
          }
        }
      }
      checkDetailAble(this.detailQuery).then(res => {
        this.showDetail = true
        if (col && col.getDetailMethod) {
          // 表格详情的数据加载方法走特殊处理
          this.detailMethod = col.getDetailMethod
        } else {
          this.detailMethod = getTrendDetail
        }
        this.$refs.detailList.execRowDataApi(this.detailQuery)
      })
    },
    loadDimTime(value) {
      const formatterOption = (dimValue) => {
        const dimBaseType = this.query.dimBaseType
        const dimYear = dimValue.substring(0, 4)
        if (dimBaseType === 4) {
          if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
          this.dateOptions[dimYear].children.push({ label: this.$t('pages.quarterNum', { Quarter: this.$t('pages.' + this.quarters[parseInt(dimValue.substring(4))]) }), value: dimValue })
        } else if (dimBaseType === 5) {
          if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
          this.dateOptions[dimYear].children.push({ label: this.$t('pages.weekNum', { Week: dimValue.substring(4) }), value: dimValue })
        } else {
          this.dateOptions.push({ label: this.$t('pages.year', { Year: dimYear }), value: dimValue })
        }
      }
      let timeType
      if (this.query.dimBaseType === 4) {
        timeType = 3
      } else if (this.query.dimBaseType === 5) {
        timeType = 2
      } else {
        timeType = 4
      }
      getDimTime(timeType).then(response => {
        this.dateOptions.splice(0)
        response.data.sort((a, b) => {
          return a > b ? 1 : -1 // 返回正数 ，b排列在a之前
        })
        for (let i = 0; i < response.data.length; i++) {
          let time = response.data[i] + ''
          while (time.length < 6) { time += '0' }
          formatterOption(time)
        }
        ++this.cascaderKey
        const children = this.dateOptions[this.dateOptions.length - 1].children
        this.query.dimValue = (this.query.dimBaseType === 4 || this.query.dimBaseType === 5) ? (children[children.length - 1] ? children[children.length - 1].value : '') : this.dateOptions[this.dateOptions.length - 1].value
        value === 'initial' && this.loadReport()
      }).catch(() => {
        if (this.loading) {
          this.loading = false
        }
      })
    },
    dimValueChange(datas) {
      if (datas && datas.length > 0) {
        this.query.dimValue = datas[datas.length - 1]
      } else {
        this.query.dimValue = undefined
      }
    },
    loadReport() {
      if (!this.loading) {
        this.loading = true
      }
      this.showDetail = false
      // 保证selectedColumn的顺序和customList一致
      this.query.selectedColumn = this.customList
        .filter(item => this.selectedFinal.some(selected => selected.prop === item.prop))
        .map(item => ({ label: item.label, prop: item.prop }))
      this.tempQuery = Object.assign({}, this.query)
      getReportChart(this.query, this.api).then(respond => {
        this.loading = false
        this.chartsInfo = respond.data
        if (this.chartsInfo) {
          this.rowDatas.splice(0, this.rowDatas.length, ...this.chartsInfo.oriData)
          this.chartsOption.splice(0, 1, this.chartsInfo)
          // 拼接趋势图显示日期
          const labelColmn = this.gridTable.colModel[0]
          const dimBaseType = this.query.dimBaseType
          const dimValue = this.query.dimValue
          labelColmn.formatter = (row, data) => {
            return this.dateFormat(dimBaseType, dimValue, data)
          }
          // this.chartsInfo.option.tooltip = {
          //   formatter: (d) => {
          //     let res = d[0].axisValueLabel + '<br>';
          //     for (var i = 0; i < d.length; i++) {
          //       let value = d[i].data
          //       const col = this.getCol(d[i].seriesName);
          //       if (col != null) {
          //         const data = this.chartsInfo.oriData[d[i].dataIndex]
          //         value = this.valueFormat(data, value, col)
          //       }
          //       res += d[i].seriesName + ' : ' + value + '<br>'
          //     }
          //     return res
          //   },
          //   confine: true,
          //   extraCssText: 'white-space: break-spaces;'
          // }
          // this.chartsInfo.option.title.text === '应用程序运行时长趋势图'
          if (this.conversionTime) {
            timeAxis(this.chartsInfo.option)
          }
          if (this.conversionFlow) {
            flowAxis(this.chartsInfo.option)
          }
          const xAxis = this.chartsInfo.option.xAxis[0].data
          if (xAxis != null) {
            this.chartsInfo.option.xAxis[0].data = xAxis.map(item => { return this.dateFormat(dimBaseType, dimValue, item) })
          }
          // 自定义格式化echart样式
          this.formatOption(this.chartsInfo)
          // 大小转换
          this.gridTable.colModel.forEach(item => {
            if (item.flag === 'size') {
              item.formatter = (row, data) => {
                return convert(data)
              }
            }
          })
          // 趋势图日期表格头名称
          this.colModel[0].label = this.dateTableName[this.query.dimBaseType]
          this.colModel[0].fixed = true
        }
      }).catch(() => {
        this.loading = false
      })
    },
    dateFormat(dimBaseType, dimValue, data) {
      if (dimBaseType === 5) {
        const str = data.toString()
        if (str.length === 4) {
          const month = str.substring(0, 2)
          const day = str.substring(2, 4)
          return this.$t('text.monthDay', { Month: this.$t('text.' + this.months[month]), Day: day })
        } else {
          const month = str.substring(0, 1)
          const day = str.substring(1, 3)
          return this.$t('text.monthDay', { Month: this.$t('text.' + this.months[month]), Day: day })
        }
      } else if (dimBaseType === 4) {
        return this.$t('pages.yearQuarterMonth', { Year: dimValue.substring(0, 4), Quarter: this.$t('pages.' + this.quarters[parseInt(dimValue.substring(4))]), Month: this.$t('text.' + this.months[data]) })
      } else if (dimBaseType === 3) {
        return this.$t('pages.yearWeek', { Year: dimValue.substring(0, 4), Week: data })
      } else if (dimBaseType === 2) {
        return this.$t('pages.yearMonth', { Year: dimValue.substring(0, 4), Month: this.$t('text.' + this.months[data]) })
      } else {
        return this.$t('pages.yearQuarter', { Year: dimValue.substring(0, 4), Quarter: this.$t('pages.' + this.quarters[parseInt(data)]) })
      }
    },
    getCol(seriesName) {
      const colModel = this.customList.find(item => {
        return this.$t('table.' + item.label) == seriesName
      })
      return colModel
    },
    valueFormat(data, value, colModel) {
      if (colModel.flag === 'size') {
        value = convert(value)
      } else if (colModel.flag === 'time') {
        value = formatSeconds(value)
      } else if (colModel.formatter != undefined && colModel.formatter != null) {
        return colModel.formatter(data, value)
      }
      return value
    },
    getCurSelected() {
      return this.customList.filter(ele => {
        return this.colModel.map(item => item.prop).indexOf(ele.prop) !== -1
      })
    },
    handleColumn() {
      this.selected = this.getCurSelected()
      this.dialogVisible = true
    },
    handleCheckAllChange() {
      this.selected = this.customList
    },
    handleCheckNone() {
      this.selected = []
    },
    handleChoose() {
      if (this.selected.length > 0) {
        this.$emit('handleSelect', this.selected)
        this.selectedFinal.splice(0)
        this.selectedFinal.push(...this.selected)
        this.loadReport()
        this.dialogVisible = false
      }
    },
    handlePrint() {
      if (!this.chartsInfo) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.compareReport24'),
          type: 'error'
        })
        return
      }
      this.onlyTabel = true
      this.colModel[0].fixed = false
      // 在打印的时候，防止把柱状对象是否显示的设置重置掉，所以更新一下当前设置
      if (this.showType !== 3) {
        const op = this.$refs.charts.$refs.line[0].chart._api.getOption()
        this.chartsOption[0].option.legend.selected = op.legend[0].selected
        this.echartsImg = this.$refs.charts ? this.$refs.charts.$refs.line[0].chart._api.getDataURL({
          excludeComponents: ['toolbox']
        }) : ''
        this.$refs.charts.$refs.line[0].chart.resize()
      }
      setTimeout(() => {
        this.$print(this.$refs.print)
      }, 100)
      setTimeout(() => {
        this.echartsImg = ''
        this.onlyTabel = false
        this.colModel[0].fixed = true
      }, 200)
    },
    buildExcelData() {
      if (!this.chartsInfo) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.compareReport24'),
          type: 'error'
        })
        return
      }
      let imgBase64Info
      if (this.$refs.charts) {
        imgBase64Info = this.$refs.charts ? this.$refs.charts.$refs.line[0].chart._api.getDataURL({
          excludeComponents: ['toolbox'],
          backgroundColor: '#fff'
        }) : ''
        this.$refs.charts.$refs.line[0].chart.resize()
      } else {
        imgBase64Info = ''
      }
      const curSelected = this.getCurSelected()
      const showColumn = curSelected.map(item => item.value).reduce(function(prev, curr, idx, arr) {
        return prev + curr
      })
      return Object.assign(this.tempQuery, {
        imgBase64Info,
        showType: this.showType,
        showColumn: this.api === 'getTrwfeTrend' ? undefined : showColumn,
        showLongColumn: this.api === 'getTrwfeTrend' ? showColumn : undefined
      })
    },
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    },
    /**
     * 查看详情事件
     * @param row 当前行数据
     */
    handleDetail(row) {
      if (this.$refs.detailDialog) {
        this.$refs.detailDialog.visible = true
        this.$refs.detailDialog.rowData = row
        this.$refs.detailDialog.detailCol = this.detailModel
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container{
  display: block;
  height: calc(100vh - 140px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 5px;
}
.checkbox {
  padding: 0 20px;
  h4{
    margin: 0 0 15px 0;
  }
  .el-checkbox {
    display: block;
    margin: 0 0 20px 0;
    >>>.el-checkbox__label {
      display: inline-flex;
    }
  }
  .width-50-percent {
    width: 50%;
    float: left;
  }
}
</style>
