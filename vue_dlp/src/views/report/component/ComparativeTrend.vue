<!--同比环比趋势对比-->
<!--
调用示例：
<comparative-trend
  :api="'getSecurityCompareData'"                   // 除部门折线外的接口地址
  :api-dept="'getSecurityCompareDeptData'"          // 部门折线接口地址
  :chart-title="chartTitle"                         // 正常就是报表名称
  :statistical-list="statisticalList"               // 统计项复选框传值
  :statistical-list-check="statisticalListCheck"    // 统计项复选框默认选中
  :sub-col-model-tb="subColModelTb"                 // 同比表格下钻列（暂时没用）
  :sub-col-model-hb="subColModelHb"                 // 环比表格下钻列（暂时没用）
/>
-->
<template>
  <div ref="print" class="app-container sensitiveness-report">
    <div v-loading="loading">
      <div class="toolbar" style="padding: 5px 20px 0 15px">
        <span>
          <!--报表日期-->
          {{ $t('pages.dimBaseType') }}：
          <el-select ref="dimType" v-model="query.dimBaseType" style="width: 135px;" @change="loadDimTime" >
            <el-option
              v-for="(item, index) in dateTypeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-cascader
            ref="cascader"
            :key="cascaderKey"
            v-model="query.dimValue"
            :options="dateOptions"
            :props="{ expandTrigger: 'hover' }"
            :separator="$t('pages.cascaderSep')"
            style="width: 160px; margin-right: 10px;line-height:0"
            @change="dimValueChange"
          ></el-cascader>
        </span>
        <span>
          <!--统计类型-->
          {{ $t('pages.countByObject') }}：
          <el-select v-model="query.countByObject" is-filter :placeholder="$t('text.select')" style="width: 135px; margin-right: 10px;" @change="handleCount">
            <el-option
              v-for="(item, index) in typeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
              :disabled="(onlyTerminal&&item.value===2) || (onlyUser&&item.value===1)"
            ></el-option>
          </el-select>
        </span>
        <!--1:操作员2：终端3：关键字-->
        <!--<span v-if="query.countByObject !== 3">
          统计对象：
          <tree-select
            ref="objectTree"
            node-key="id"
            class="targetObject"
            :height="350"
            :width="500"
            clearable
            multiple
            check-strictly
            :local-search="false"
            :checked-keys="checkedKeys"
            is-filter
            :leaf-key="query.countByObject === 2 ? 'terminal' : 'user'"
            style="width: 200px;display: inline-block;margin-right: 10px"
            @change="(key, nodeData) => entityIdChange(key, nodeData, query)"
          />
        </span>-->
        <!--统计项设置-->
        <el-button type="text" @click.native="handleStatistical"><i class="el-icon-s-tools"></i>{{ $t('pages.statisticItemSetting') }}</el-button>
        <el-button type="primary" size="mini" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
      </div>
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('text.noData') }}...</div>
      </div>
      <div v-if="!noData" class="content-div">
        <!--第一行折线图-->
        <el-row :gutter="32">
          <!--showRightLg：根据部门折线图是否显示，动态控制大屏幕下的列宽-->
          <el-col :xs="24" :sm="24" :lg="showRightLg">
            <div class="chart-wrapper table-container">
              <span class="span-mini-title">
                <span> {{ miniTitle.lineNumTitle.miniTitle }}</span>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <!--通过v-html 渲染字符串  最后添加 white-space: pre-line 即可实现 el-tooltip 换行，换行信息中的换行符使用\n-->
                  <div slot="content" style="white-space: pre-line" v-html="miniTitle.lineNumTitle.titleMsg"></div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
              <!--左边总体趋势折线图-->
              <line-chart
                ref="leftEcharts"
                :chart-data="temp.lineChartData"
                :chart-option="temp.lineOption"
                :click="leftLineChartClick"
                style="height: 200px;width: 100%"
              />
            </div>
          </el-col>
          <el-col v-if="showRightDept" :xs="24" :sm="24" :lg="12">
            <div class="chart-wrapper table-container">
              <span class="span-mini-title">
                <span>{{ deptChangeTitle }}{{ miniTitle.lineDeptTitle.miniTitle }}</span>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <!--通过v-html 渲染字符串  最后添加 white-space: pre-line 即可实现 el-tooltip 换行，换行信息中的换行符使用\n-->
                  <div slot="content" style="white-space: pre-line" v-html="miniTitle.lineDeptTitle.titleMsg"></div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
              <span style="font-size: 12px;float: right;margin-right: 9vh">
                Top
                <el-select ref="top" v-model="query.recordSize" style="width: 65px;" @change="topChange">
                  <el-option
                    v-for="(item, index) in sizeOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </span>
              <!--右边部门趋势折线图-->
              <line-chart
                :chart-data="temp.lineDeptChartData"
                :chart-option="temp.lineDeptOption"
                :click="rightLineChartClick"
                style="height: 200px;width: 100%"
              />
            </div>
          </el-col>
        </el-row>
        <!--第一行图表解读-->
        <el-row v-if="isShowChartInterpretation" :gutter="32">
          <el-col :lg="24" style="margin-bottom: 10px;">
            <div class="panel-wrapper unscramble">
              <div v-html="chartInterpretationContent"></div>
            </div>
          </el-col>
        </el-row>
        <!--第二行同比图-->
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="chart-wrapper table-container">
              <span class="span-mini-title">
                <span><span v-html="tbChartChangeTitle"></span>{{ miniTitle.tbChartTitle.miniTitle }} </span>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <!--通过v-html 渲染字符串  最后添加 white-space: pre-line 即可实现 el-tooltip 换行，换行信息中的换行符使用\n-->
                  <div slot="content" style="white-space: pre-line" v-html="miniTitle.tbChartTitle.titleMsg"></div>
                  <i class="el-icon-info" />
                </el-tooltip>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div slot="content">{{ $t('components.seeMore') }}</div>
                  <svg-icon icon-class="big" aria-hidden="false" class="big-svg" style="cursor: pointer" @click="handleTb"/>
                </el-tooltip>
              </span>
              <!--左边同比双层饼图-->
              <pie-double-chart
                :chart-data="temp.pieChartData"
                :chart-option="temp.pieChartOption"
                style="height: 210px"
              />
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="16">
            <div class="chart-wrapper table-container">
              <el-button v-if="showDetailTB" size="mini" type="primary" style="margin-bottom: 5px" @click="showDetailTB = false">
                <i class="el-icon-back"></i>
              </el-button>
              <span class="span-mini-title">
                <span> {{ tbTableChangeTitle }}{{ miniTitle.tbTableTitle.miniTitle }}</span>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <!--通过v-html 渲染字符串  最后添加 white-space: pre-line 即可实现 el-tooltip 换行，换行信息中的换行符使用\n-->
                  <div slot="content" style="white-space: pre-line" v-html="miniTitle.tbTableTitle.titleMsg"></div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
              <!--右边同比表格数据信息-->
              <my-table
                v-show="!showDetailTB"
                ref="tbTable"
                :col="colModelTB"
                :data="rowDatasTB"
                style="height: 200px"
                :cell-style="cellStyle"
                @cell-click="cellClickTB"
              />
              <!--              <grid-table-->
              <!--                v-show="showDetailTB"-->
              <!--                ref="gridTable"-->
              <!--                :height="200"-->
              <!--                :min-height="180"-->
              <!--                :show-pager="false"-->
              <!--                :multi-select="false"-->
              <!--                :col-model="colModelTBDetails"-->
              <!--                :row-datas="rowDatasTBDetails"-->
              <!--              />-->
            </div>
          </el-col>
        </el-row>
        <!--第二行图表解读-->
        <el-row v-if="isShowChartInterpretation" :gutter="32">
          <el-col :lg="24" style="margin-bottom: 10px;">
            <div class="panel-wrapper unscramble">
              <div v-html="chartInterpretationContent"></div>
            </div>
          </el-col>
        </el-row>
        <!--第三行环比图-->
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="8">
            <div class="chart-wrapper table-container">
              <span class="span-mini-title">
                <span><span v-html="hbChartChangeTitle"></span>{{ miniTitle.hbChartTitle.miniTitle }}</span>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <!--通过v-html 渲染字符串  最后添加 white-space: pre-line 即可实现 el-tooltip 换行，换行信息中的换行符使用\n-->
                  <div slot="content" style="white-space: pre-line" v-html="miniTitle.hbChartTitle.titleMsg"></div>
                  <i class="el-icon-info" />
                </el-tooltip>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div slot="content">{{ $t('components.seeMore') }}</div>
                  <svg-icon icon-class="big" aria-hidden="false" style="cursor: pointer" @click="handleHb"/>
                </el-tooltip>
              </span>
              <!--左边环比双轴柱状图-->
              <bar-double-chart
                :chart-data="temp.barChartData"
                :chart-option="temp.barChartOption"
                style="height: 210px"
              />
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="16">
            <div class="chart-wrapper table-container">
              <el-button v-if="showDetailHB" size="mini" type="primary" style="margin-bottom: 5px" @click="showDetailHB = false">
                <i class="el-icon-back"></i>
              </el-button>
              <span class="span-mini-title">
                <span> {{ hbTableChangeTitle }}{{ miniTitle.hbTableTitle.miniTitle }}</span>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <!--通过v-html 渲染字符串  最后添加 white-space: pre-line 即可实现 el-tooltip 换行，换行信息中的换行符使用\n-->
                  <div slot="content" style="white-space: pre-line" v-html="miniTitle.hbTableTitle.titleMsg"></div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </span>
              <!--右边环比表格数据信息-->
              <my-table
                v-show="!showDetailHB"
                ref="hbTable"
                :col="colModelHB"
                :data="rowDatasHB"
                style="height: 200px"
                :cell-style="cellStyle"
                @cell-click="cellClickHB"
              />
              <!--              <grid-table-->
              <!--                v-show="showDetailHB"-->
              <!--                ref="gridTable"-->
              <!--                :height="200"-->
              <!--                :min-height="200"-->
              <!--                :show-pager="false"-->
              <!--                :multi-select="false"-->
              <!--                :col-model="colModelHBDetails"-->
              <!--                :row-datas="rowDatasHBDetails"-->
              <!--              />-->
            </div>
          </el-col>
        </el-row>
        <!--第三行图表解读-->
        <el-row v-if="isShowChartInterpretation" :gutter="32">
          <el-col :lg="24" style="margin-bottom: 10px;">
            <div class="panel-wrapper unscramble">
              <div v-html="chartInterpretationContent"></div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 统计项设置弹框 -->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.statisticItemSetting')"
      :width="dialogWidth"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :modal="false"
      @close="handleStatisticalCancel"
    >
      <div class="checkbox">
        <h4>{{ $t('pages.compareReport1') }}</h4>
        <el-divider v-if="filteredNumOptions.length > 0" content-position="left">{{ $t('components.number') }}</el-divider>
        <el-card v-if="filteredNumOptions.length > 0" class="box-card">
          <div>
            <el-button type="text" @click="handleCheckAllChange(filteredNumOptions)">{{ $t('button.selectAll') }}</el-button> /
            <el-button type="text" @click="handleCheckNone(filteredNumOptions)">{{ $t('button.selectNone') }}</el-button>
          </div>
          <el-checkbox-group v-model="query.columnList">
            <el-checkbox
              v-for="(item) in filteredNumOptions"
              :key="item.prop"
              :label="item.prop"
              :class="{ 'width-50-percent': statisticalList.length > 6 }"
            >{{ $te(`table.${item.label}`) ? $t(`table.${item.label}`) : item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-card>
        <el-divider v-if="filteredSizeOptions.length > 0" content-position="left">{{ $t('table.size') }}</el-divider>
        <el-card v-if="filteredSizeOptions.length > 0" class="box-card">
          <div>
            <el-button type="text" @click="handleCheckAllChange(filteredSizeOptions)">{{ $t('button.selectAll') }}</el-button> /
            <el-button type="text" @click="handleCheckNone(filteredSizeOptions)">{{ $t('button.selectNone') }}</el-button>
          </div>
          <el-checkbox-group v-model="query.columnList">
            <el-checkbox
              v-for="(item) in filteredSizeOptions"
              :key="item.prop"
              :label="item.prop"
              :class="{ 'width-50-percent': statisticalList.length > 6 }"
            >{{ $te(`table.${item.label}`) ? $t(`table.${item.label}`) : item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-card>

        <el-divider v-if="filteredTimeOptions.length > 0" content-position="left">{{ $t('table.duration') }}</el-divider>
        <el-card v-if="filteredTimeOptions.length > 0" class="box-card">
          <div>
            <el-button type="text" @click="handleCheckAllChange(filteredTimeOptions)">{{ $t('button.selectAll') }}</el-button> /
            <el-button type="text" @click="handleCheckNone(filteredTimeOptions)">{{ $t('button.selectNone') }}</el-button>
          </div>
          <el-checkbox-group v-model="query.columnList">
            <el-checkbox
              v-for="(item) in filteredTimeOptions"
              :key="item.prop"
              :label="item.prop"
              :class="{ 'width-50-percent': statisticalList.length > 6 }"
            >{{ $te(`table.${item.label}`) ? $t(`table.${item.label}`) : item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-card>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="tip-foot-div">1、{{ $t('pages.compareReport2') }}</div>
        <div class="tip-foot-div" style="margin-top: 20px">2、{{ $t('pages.compareReport3') }}</div>
        <el-button type="primary" @click="handleStatisticalConfirm">{{ $t('button.confirm2') }}</el-button>
        <el-button @click="handleStatisticalCancel">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>
    <comparative-tb-dialog ref="comparativeTbDialog"/>
    <comparative-hb-dialog ref="comparativeHbDialog"/>
  </div>
</template>

<script>
import MyTable from '@/views/report/component/MultilevelTable/MyTable'
import LineChart from '@/components/ECharts/LineChart'
import PieDoubleChart from '@/components/ECharts/PieDoubleChart'
import BarDoubleChart from '@/components/ECharts/BarDoubleChart'
import ComparativeTbDialog from '@/views/report/component/ComparativeTbDialog';
import ComparativeHbDialog from '@/views/report/component/ComparativeHbDialog';
import { getDimTime, getTrendDetail } from '@/api/report/baseReport/issueReport/issue';
import { getReportCompareChart } from '@/api/report/baseReport/issueReport/compare';
import moment from 'moment';
import { convert, formatSeconds } from '@/utils';
export default {
  name: 'ComparativeTrend',
  components: { LineChart, PieDoubleChart, BarDoubleChart, MyTable, ComparativeTbDialog, ComparativeHbDialog },
  props: {
    // 数据请求接口(除部门折线图以外的接口)
    api: {
      type: String,
      default: ''
    },
    // 数据请求接口(部门折线图接口)
    apiDept: {
      type: String,
      default: ''
    },
    // echarts图表名称（前半段统一由父组件传递）
    chartTitle: {
      type: String,
      default: ''
    },
    // 统计项设置
    statisticalList: {
      type: Array,
      default() {
        return []
      }
    },
    // 统计项设置 默认选中
    statisticalListCheck: {
      type: Array,
      default() {
        return []
      }
    },
    // 统计类型
    countByObject: {
      type: Number,
      default: 2
    },
    // 是否只支持按终端统计
    onlyTerminal: {
      type: Boolean,
      default: false
    },
    // 是否只支持按操作员统计
    onlyUser: {
      type: Boolean,
      default: false
    }
    // 同比表格详情（暂时没有）
    // subColModelTb: {
    //   type: Array,
    //   default() {
    //     return []
    //   }
    // },
    // 环比表格详情（暂时没有）
    // subColModelHb: {
    //   type: Array,
    //   default() {
    //     return []
    //   }
    // }
  },
  data() {
    return {
      // 是否显示图表解读
      isShowChartInterpretation: false,
      chartInterpretationContent: `<div class="unscramble-title">图表解读：</div>
              1、从整体上看，2025年环比比2024年多很多<span class="unscramble-red">其他资产变更次数、资产变更次数</span>占了相当大的一部分比例。<br/>
              2、目前按年份统计，当前只是<span class="unscramble-green">2025年的第一个月</span>，而<span class="unscramble-red">2024年</span>的统计数量有<span class="unscramble-red">一整年</span>，所以年环比不是很精确。<br/>
              结论：相对稳定安全。`,
      // 暂无数据
      noData: false,
      loading: false,
      dialogVisible: false,
      // 统计项配置弹框选中
      showSelect: [],
      // 动态控制第一行列宽
      showRightLg: 24,
      // 部门折线图是否显示
      showRightDept: false,
      // 动态修改小标题(如果直接在miniTitle上修改，小标题会累加，所以重新声明一些变量，只争对页面点击时使用)
      deptChangeTitle: '',
      tbChartChangeTitle: '',
      tbTableChangeTitle: '',
      hbChartChangeTitle: '',
      hbTableChangeTitle: '',
      // 同比弹框柱状图传值
      tbDialogBarData: [],
      // 查询项
      query: {
        // 报表日期
        dimBaseType: 1,
        dimValue: '',
        // 统计项设置
        columnList: this.statisticalListCheck,
        // 部门折线图点击
        groupName: '',
        // 统计类型 1：操作员，2：终端
        countByObject: this.countByObject,
        // 查询对象，查询对象支持同时查询部门和终端
        objectIds: '',
        // 部门ID
        groupIds: '',
        // top  (以下是右上折线部门需要参数)
        recordSize: 5,
        columnName: '',
        sortName: ''
      },
      // 报表日期相关配置
      dateTypeOptions: [
        { value: 1, label: this.$t('pages.trendDateTypeOptions5') },
        { value: 2, label: this.$t('pages.trendDateTypeOptions4') },
        { value: 4, label: this.$t('pages.trendDateTypeOptions3') }
      ],
      dateOptions: [],
      cascaderKey: 0,
      months: ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      quarters: ['', 'theFirst', 'theSecond', 'theThird', 'theFourth'],
      // 统计对象相关配置
      typeOptions: [
        { value: 1, label: this.$t('pages.conutType1') },
        { value: 2, label: this.$t('pages.conutType2') }
      ],
      checkedKeys: [],
      // Top
      sizeOptions: [{ value: 5, label: 5 }, { value: 10, label: 10 }],
      // 表格表头名称
      dateTableName: ['', this.$t('text.quarter'), this.$t('text.month'), this.$t('pages.week'), this.$t('text.month'), this.$t('text.day')],
      echartsImg: '',
      // 下钻表格相关配置（暂时没有）
      onlyTabel: false,
      tempQuery: {},
      showDetailTB: false,
      showDetailHB: false,
      detailModel: [],
      detailQuery: {
        page: 1,
        detailTable: '',
        label: ''
      },
      detailMethod: getTrendDetail,
      /** objectIds objectNames是用来查看明细时，点击查看其他对象明细时用到 */
      objectIds: [],
      objectNames: [],
      // 同比表格
      colModelTB: [],
      colModelTBDetails: [],
      rowDatasTB: [],
      rowDatasTBDetails: [],
      // 环比表格
      colModelHB: [],
      // colModelHBDetails: [],
      rowDatasHB: [],
      // rowDatasHBDetails: [],
      temp: {},
      defaultTemp: {
        deptflag: '',
        // 数量趋势图（左上角）
        lineChartData: [],
        lineOption: {
          title: {
            top: '-3',
            text: this.chartTitle + this.$t('pages.trendChart')
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          legend: {
            // 设置图例加分页
            type: 'scroll', // 开启滚动
            pageIconColor: '#008acd', // 翻页按钮的颜色
            pageButtonItemGap: 5, // 设置分页按钮之间的间隔
            pageButtonGap: 5, // 设置图例与分页按钮之间的间隔
            x: 'center', // 图例水平居中
            bottom: 0,
            data: []
          },
          grid: {
            top: '10%',
            width: '85%',
            bottom: '15%',
            left: '4%',
            containLabel: true
          },
          series: []
        },
        // 部门趋势图
        lineDeptChartData: [],
        lineDeptOption: {
          title: {
            top: '-3',
            text: this.chartTitle + this.$t('pages.deptTrendChart')
          },
          toolbox: {
            show: false
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value',
            // 控制分隔线
            splitLine: {
              show: true,
              lineStyle: {
                width: 0.5,
                color: '#bce5ff'
              }
            },
            // 控制隔区域
            splitArea: {
              show: false
            }
          },
          legend: {
            // 设置图例加分页
            type: 'scroll', // 开启滚动
            pageIconColor: '#008acd', // 翻页按钮的颜色
            pageButtonItemGap: 5, // 设置分页按钮之间的间隔
            pageButtonGap: 5, // 设置图例与分页按钮之间的间隔
            x: 'center', // 图例水平居中
            bottom: 0,
            data: []
          },
          grid: {
            top: '10%',
            width: '85%',
            bottom: '15%',
            left: '4%',
            containLabel: true
          },
          series: []
        },
        // 双层饼图
        pieChartData: {
          outerLayer: [],
          innerLayer: []
        },
        pieChartOption: {
          title: {
            text: this.chartTitle + this.$t('pages.yearOnYearChart')
          },
          toolbox: {
            show: false
          },
          legend: {
            show: true,
            type: 'scroll', // 开启滚动
            pageIconColor: '#008acd', // 翻页按钮的颜色
            pageButtonItemGap: 5, // 设置分页按钮之间的间隔
            pageButtonGap: 5, // 设置图例与分页按钮之间的间隔
            left: 'center'
          },
          series: [
            {
              name: '',
              type: 'pie',
              animation: false,
              radius: ['20%', '40%'],
              label: {
                position: 'inner'
              }
            },
            {
              name: '',
              type: 'pie',
              radius: ['30%', '40%'],
              label: {
                normal: {
                  position: 'outside',
                  formatter: '{b} {d}% '
                }
              }
            }
          ]
        },
        // 双轴柱状图
        barChartData: [],
        barChartOption: {
          title: {
            text: this.chartTitle + this.$t('pages.sequentialQuantityStatistics')
          },
          calculable: true,
          yAxis: [
            // 左轴
            {
              type: 'category',
              gridIndex: 0,
              axisLabel: {
                padding: 0,
                fontSize: 9,
                interval: 0,
                // 倾斜的程度
                // rotate: 10,
                formatter: (value) => {
                  // 如果名称长度超过5个字符，截断并添加省略号
                  if (value.length > 5) {
                    return value.substring(0, 5) + '...';
                  } else {
                    return value;
                  }
                }
              },
              data: []
            },
            {
              gridIndex: 1,
              type: 'category',
              axisLabel: {
                // 隐藏中间轴的名称(不隐藏的话左边柱子盖不住中间轴名称)
                show: false
              },
              data: []
            },
            {
              gridIndex: 2,
              axisLabel: {
                interval: 0,
                padding: 0,
                fontSize: 9,
                // 倾斜的程度
                // rotate: 10,a
                formatter: (value) => {
                  // 如果名称长度超过5个字符，截断并添加省略号
                  if (value.length > 5) {
                    return value.substring(0, 5) + '...';
                  } else {
                    return value;
                  }
                }
              },
              type: 'category',
              nameGap: 25,
              // 轴线
              axisLine: {
                show: true
              },
              // 分割线
              axisTick: {
                show: false
              },
              data: []
            }
          ],
          series:
            [
              // 左侧轴
              {
                name: '2023',
                type: 'bar',
                // 对应的左侧轴x轴索引
                xAxisIndex: 0,
                yAxisIndex: 0,
                // 柱体收缩，不会太粗
                barMaxWidth: '40%',
                itemStyle: {
                  color: {
                    type: 'linear', // 线性渐变
                    colorStops: [
                      { offset: 1, color: '#F76B1C' },
                      { offset: 0, color: '#FAD961' }
                    ]
                  },
                  emphasis: {
                    opacity: 1
                  }
                },
                data: []
              },
              // 右侧轴
              {
                name: '2024',
                type: 'bar',
                xAxisIndex: 1,
                yAxisIndex: 1,
                barMaxWidth: '40%',
                itemStyle: {
                  color: {
                    type: 'linear', // 线性渐变
                    colorStops: [
                      { offset: 0, color: '#1171C9' },
                      { offset: 1, color: '#61C4FA' }
                    ]
                  },
                  emphasis: {
                    opacity: 1
                  }
                },
                data: []
              }
            ]
        }
      },
      // 小标题信息（每张图表左上角的小标题）
      miniTitle: {
        lineNumTitle: {
          miniTitle: this.$t('pages.generalTrend'),
          titleMsg: this.$t('pages.compareReport4') + '\n' +
            this.$t('pages.compareReport5')
        },
        lineDeptTitle: {
          miniTitle: this.$t('pages.deptTrendChart'),
          titleMsg: this.$t('pages.compareReport6') + '\n' +
            this.$t('pages.compareReport7') + '\n' +
            this.$t('pages.compareReport8')
        },
        tbChartTitle: {
          miniTitle: this.$t('pages.yearOnYearChart'),
          titleMsg: this.$t('pages.compareReport9') + '\n' +
            this.$t('pages.compareReport10')
        },
        tbTableTitle: {
          miniTitle: this.$t('pages.yearOnYearInfo'),
          titleMsg: this.$t('pages.compareReport11') + '\n' +
            this.$t('pages.compareReport12') + '\n' +
            this.$t('pages.compareReport13')
        },
        hbChartTitle: {
          miniTitle: this.$t('pages.linkGraph'),
          titleMsg: this.$t('pages.compareReport14') + '\n' +
            this.$t('pages.compareReport15')
        },
        hbTableTitle: {
          miniTitle: this.$t('pages.linkGraphInfo'),
          titleMsg: this.$t('pages.compareReport16') + '\n' +
            this.$t('pages.compareReport17') + '\n' +
            this.$t('pages.compareReport18')
        }
      },
      activeName: 'num'
    }
  },
  computed: {
    // 统计项设置，宽度配置
    dialogWidth() {
      return this.statisticalList.length > 6 ? '800px' : '500px'
    },
    // 通过计算属性过滤出带有flag且值为size的选项（统计项设置选项）
    filteredSizeOptions() {
      return this.statisticalList.filter(item => item.hasOwnProperty('flag') && item.flag === 'size');
    },
    // 通过计算属性过滤出带有flag且值为size的选项（统计项设置选项）
    filteredTimeOptions() {
      return this.statisticalList.filter(item => item.hasOwnProperty('flag') && item.flag === 'time');
    },
    // 通过计算属性过滤出不带flag的选项（统计项设置选项）
    filteredNumOptions() {
      return this.statisticalList.filter(item => item.hasOwnProperty('flag') === false);
    }
  },
  watch: {
    // 第一行折线图容易大小发生变化时，需要对图表做宽度自适应操作，否则图表宽度为原始容器宽度，在页面上重叠在一起
    showRightLg(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.$nextTick(() => {
          this.$refs['leftEcharts'].__resizeHandler()
        })
      }
    }
  },
  created() {
    this.loading = false
    // 表格列的初始化
    // this.colModelTBDetails = this.subColModelTb
    // this.colModelHBDetails = this.subColModelHb
    this.loadDimTime('initial')
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.temp = { ...this.defaultTemp }
      this.tbDialogBarData = []
    },
    /**
     * 左上（总体趋势）折线图（点击时）
     * 右上（部门趋势）折线图显示对应点击信息
     * @param option echarts点击时，获取到当前点击点的信息
     * */
    leftLineChartClick(option) {
      this.query.groupName = ''
      // 点击时，修改小标题名称
      this.deptChangeTitle = '“' + option.seriesName + '”'
      // 点击时，修改容器宽度，显示部门折线图
      this.showRightLg = 12
      this.showRightDept = true
      // console.log('option', option)
      // 接口传参需要值columnName，option不能直接拿到，通过索引获取到对应字段
      this.query.columnName = this.temp.lineOption.series[option.seriesIndex].prop
      // sortName优先取自定义的sortColumn值
      const target = this.statisticalList.find(item => item.prop === this.query.columnName)
      if (target && target.sortColumn) {
        this.query.sortName = target.sortColumn
      } else {
        // 驼峰字段转换成下划线命名（避免数据库字段不匹配，所以驼峰和下划线格式都传递）
        this.query.sortName = this.query.columnName.replace(/([A-Z])/g, '_$1').toLowerCase()
      }
      // 获取部门折线图数据
      getReportCompareChart(this.query, this.apiDept).then(res => {
        // console.log('部门接口获取', res)
        // 右上角折线图（部门）数据处理
        this.lineRightDeptDataProcessing(res.data)
      })
    },
    /**
     * 右上（部门趋势）折线图（点击时）
     * 下方e-charts图和表格都显示当前点击对应信息
     * @param option echarts点击时，获取到当前点击点的信息
     * */
    rightLineChartClick(option) {
      // console.log('option', option)
      // 查询条件部门名称赋值
      // 接口传参需要查询条件部门名称赋值groupName，option不能直接拿到，通过索引获取到对应字段
      this.query.groupName = this.temp.lineDeptOption.series[option.seriesIndex].groupName
      // 点击时，修改小标题名称
      const currentDate = this.query.dimValue.substring(0, 4)
      const currentDateBefore = Number(currentDate) - 1
      this.tbChartChangeTitle = '“' + currentDate + this.$t('text.year') + `<span style="color: orange">(` + this.$t('table.currentLabel') + `)</span>/` + currentDateBefore + this.$t('text.year') + `<span style="color: orange">(` + this.$t('table.yoy') + `)</span>` + option.seriesName + '”'
      this.tbTableChangeTitle = '“' + option.seriesName + '”'
      this.hbChartChangeTitle = '“' + currentDate + this.$t('text.year') + `<span style="color: orange">(` + this.$t('table.currentLabel') + `)</span>/` + currentDateBefore + this.$t('text.year') + `<span style="color: orange">(` + this.$t('table.qoq') + `)</span>` + option.seriesName + '”'
      this.hbTableChangeTitle = '“' + option.seriesName + '”'
      // 接口调用，获取页面数据
      getReportCompareChart(this.query, this.api).then(res => {
        // console.log('response获取接口数据1212', res)
        // 同比双层饼图数据处理
        this.yoyPieDataProcessing(res.data.yoyOption, 'getData')
        // 同比表格数据处理
        this.colModelTB = this.tableColProcessing(res.data.yoyData, 'yoy')
        this.rowDatasTB = this.tableDataProcessing(res.data.yoyData, 'yoy')
        // 环比双轴图数据处理
        this.qoqPieDataProcessing(res.data.qoqOption, 'getData')
        // 环比表格数据处理
        this.colModelHB = this.tableColProcessing(res.data.yoyData, 'qoq')
        this.rowDatasHB = this.tableDataProcessing(res.data.qoqData, 'qoq')
      })
    },
    /**
     * 左上角折线图数据处理
     * 将接口返回的数据处理成折线图表显示需要的格式
     * 1、通过接口返回数据与页面“统计项设置”配合映射出折线图所要显示的数据series及图例legend.data
     * 2、通过接口数据，构造出折线图的横坐标xAxis.data
     *@param lineOption 接口返回的左上数据option
     * */
    lineLeftDataProcessing(lineOption) {
      const leftLineSeries = []
      const legendData = []
      // 1、series数据及图例legend.data处理
      if (this.statisticalList.length > 0) {
        this.statisticalList.forEach(item => {
          // console.log('item', item)
          // 接口返回数据与页面“统计项设置”映射出折线图需要的数据
          const objKey = item.prop
          if (lineOption.seriesData.hasOwnProperty(objKey)) {
            const leftLineOption = {}
            leftLineOption.data = lineOption.seriesData[objKey]
            leftLineOption.type = 'line'
            leftLineOption.name = this.$t('table.' + item.label)
            // prop为图表点击时，需要接口传参属性
            leftLineOption.prop = item.prop
            leftLineSeries.push(leftLineOption)
            legendData.push(leftLineOption.name)
            // 鼠标悬停时文件大小和时长的处理
            if (item.hasOwnProperty('flag')) {
              this.deptflag = item.flag
              leftLineOption.flag = item.flag
            }
          }
        })
      }
      // 折线图需要的series数据
      this.temp.lineOption.series = leftLineSeries
      // 折线图需要的legend.data数据
      this.temp.lineOption.legend.data = legendData
      // console.log('leftLineSeries', JSON.parse(JSON.stringify(leftLineSeries)))
      // 2、横坐标xAxis.data数据处理
      if (lineOption.xaxisData.length > 0) {
        // 前4位 时间传值
        const top4 = this.query.dimValue.substring(0, 4)
        const xaxisData = []
        lineOption.xaxisData.forEach(item => {
          if (this.query.dimBaseType !== 1) {
            item = top4 + this.$t('text.year') + item + this.$t('text.month')
            xaxisData.push(item)
          } else {
            item = top4 + this.$t('pages.yearControl') + item + this.$t('text.quarter')
            xaxisData.push(item)
          }
        })
        // 折线图需要的xAxis.data数据
        this.temp.lineOption.xAxis.data = xaxisData
        // 鼠标悬停数据处理(如果是文件大小或者时长，需要处理单位)
        if (this.temp.lineOption.series.length > 0) {
          // console.log('this.temp.lineOption', JSON.parse(JSON.stringify(this.temp.lineOption)))
          const objFlag = this.temp.lineOption.series[0].flag
          // console.log('objFlag', JSON.parse(JSON.stringify(this.temp.lineOption.series[0].flag)))
          this.temp.lineOption.tooltip = {
            formatter: (d) => {
              // console.log('d', d)
              let res = d[0].name + '<br>';
              for (var i = 0; i < d.length; i++) {
                let number = d[i].value
                if (objFlag === 'size') {
                  number = convert(d[i].value)
                } else if (objFlag === 'time') {
                  number = formatSeconds(d[i].value)
                }
                res += d[i].marker + d[i].seriesName + ' : ' + number + '<br>'
              }
              return res
            }
          }
        }
      }
    },
    /**
     * 右上角折线图数据处理（左上折线图点击时，调用）
     * 将接口返回的数据处理成折线图表显示需要的格式
     * 1、通过接口返回数据seriesData获取到对象中所有的key为部门名称
     * 2、通过接口数据，构造出折线图的横坐标xAxis.data
     *@param lineOption 接口返回的右上数据res.data
     * */
    lineRightDeptDataProcessing(lineOption) {
      const leftLineSeries = []
      const legendData = []
      // 1、获取对象中所有的key为部门名称
      const deptArr = Object.keys(lineOption.seriesData)
      if (deptArr.length > 0) {
        // 2、series数据处理
        deptArr.forEach(item => {
          if (lineOption.seriesData.hasOwnProperty(item)) {
            const leftLineOption = {}
            leftLineOption.data = lineOption.seriesData[item]
            leftLineOption.type = 'line'
            leftLineOption.name = item.split('_')[0]
            // groupName为图表点击时，需要接口传参属性
            leftLineOption.groupName = item
            leftLineSeries.push(leftLineOption)
            legendData.push(leftLineOption.name)
          }
        })
      }
      // console.log('leftLineSeries', JSON.parse(JSON.stringify(leftLineSeries)))
      // 折线图需要的series数据
      this.temp.lineDeptOption.series = leftLineSeries
      // 折线图需要的legend.data数据
      this.temp.lineDeptOption.legend.data = legendData
      // 3、横坐标xAxis.data数据处理
      if (lineOption.xaxisData.length > 0) {
        // 前4位 时间传值
        const top4 = this.query.dimValue.substring(0, 4)
        const xaxisData = []
        lineOption.xaxisData.forEach(item => {
          if (this.query.dimBaseType !== 1) {
            item = top4 + this.$t('text.year') + item + this.$t('text.month')
            xaxisData.push(item)
          } else {
            item = top4 + this.$t('pages.yearControl') + item + this.$t('text.quarter')
            xaxisData.push(item)
          }
        })
        // 折线图需要的xAxis.data数据
        this.temp.lineDeptOption.xAxis.data = xaxisData
        // console.log('this.temp', JSON.parse(JSON.stringify(this.temp)))
        // console.log('this.deptflag', this.deptflag)
        this.temp.lineDeptOption.tooltip = {
          formatter: (d) => {
            // console.log('d', d)
            let res = d[0].name + '<br>';
            for (var i = 0; i < d.length; i++) {
              let number = d[i].value
              if (this.deptflag === 'size') {
                number = convert(d[i].value)
              } else if (this.deptflag === 'time') {
                number = formatSeconds(d[i].value)
              }
              // 设置部门名称超过20个字符显示...
              if (d[i].seriesName.length > 15) {
                d[i].seriesName = d[i].seriesName.substring(0, 15) + '...';
              }
              // console.log('d[i].seriesName', d[i].seriesName)
              res += d[i].marker + d[i].seriesName + ' : ' + number + '<br>'
            }
            return res
          }
        }
      }
    },
    /**
     * 同比双层饼图数据处理（正常接口查询和表格行时间点击时都有用到）
     * 分别将接口返回数据和表格点击获取到的行数据，处理成双层饼图需要的格式
     * 通过接口返回数据与页面“统计项设置”配合映射出双层饼所要显示的数据outerLayer和innerLayer
     * @param pieOption 接口返回的双层饼图数据yoyOption 或者 行点击时，获取到的行数据row
     * @param type 区分接口直接获取还是表格时间点击 getData:接口获取 cellClick：表格行点击
     * */
    yoyPieDataProcessing(pieOption, type) {
      // console.log('this.statisticalList', JSON.parse(JSON.stringify(this.statisticalList)))
      // console.log('pieOption11111', pieOption)
      // 初始化时，后台返回的柱状图字段可能不全，会导致页面缺柱子，通过当前选中统计项，对比，将统计项中没有的字段都加上0，保证字段一样
      // 然后在对比两个对象，将key相同，且value为0的都删掉
      // 传递给弹框打开时柱状图的显示(数据格式不一致，会导致柱状图显示不正确(点击和初始化时的数据格式不一样))
      const selectColumn = this.query.columnList
      if (type === 'getData') {
        for (let i = 0; i < selectColumn.length; i++) {
          // console.log('barOption.compareData.hasOwnProperty(selectColumn[i])', pieOption.compareData.hasOwnProperty(selectColumn[i]), selectColumn[i])
          // console.log('barOption.data.hasOwnProperty(selectColumn[i])', pieOption.data.hasOwnProperty(selectColumn[i]), selectColumn[i])
          if (pieOption.compareData.hasOwnProperty(selectColumn[i]) === false) {
            pieOption.compareData[selectColumn[i]] = 0
          }
          if (pieOption.data.hasOwnProperty(selectColumn[i]) === false) {
            pieOption.data[selectColumn[i]] = 0
          }
          // barOption.compareData.hasOwnProperty(selectColumn[i])
        }
        // console.log('pieOption', JSON.parse(JSON.stringify(pieOption)))
        this.removeZeroValues(pieOption.data, pieOption.compareData);
        this.tbDialogBarData = pieOption
      } else {
        // console.log('pieOption', JSON.parse(JSON.stringify(pieOption)))
        this.tbDialogBarData = {}
        const data = {}
        const compareData = {}
        for (let i = 0; i < selectColumn.length; i++) {
          // console.log('selectColumn', selectColumn[i])
          if (pieOption.hasOwnProperty(selectColumn[i] + 'Source')) {
            const name = selectColumn[i]
            data[name] = pieOption[name + 'Source']
          }
          if (pieOption.hasOwnProperty(selectColumn[i] + 'TbSource')) {
            const name = selectColumn[i]
            compareData[name] = pieOption[name + 'TbSource']
          }
        }
        this.removeZeroValues(data, compareData);
        this.tbDialogBarData.data = data
        this.tbDialogBarData.compareData = compareData
      }
      let innerLayerCurrent = []
      let innerLayerCompare = []
      const outerLayerData = []
      const innerLayerData = []
      // 1、先分别处理生成当前和同比的数据
      if (this.statisticalList.length > 0) {
        this.statisticalList.forEach(item => {
          // 2、接口返回数据与页面“统计项设置”映射出双层饼图需要的数据
          // 3、接口返回和当前行点击时的数据格式不一致，所以要做简单处理
          // objKey:对象中的key currentObj：当前值对象 compareObj：同比值对象
          let objKey = ''
          let objKeyTb = ''
          let currentObj = {}
          let compareObj = {}
          // 鼠标悬停时文件大小和时长的处理
          if (type === 'getData') {
            // 接口获取，对数据的处理
            objKey = item.prop
            objKeyTb = item.prop
            currentObj = pieOption.data
            compareObj = pieOption.compareData
          } else {
            // 表格点击，对数据的处理(存在文件大小和时长，所以在表格列中多多了source源数据)
            objKey = item.prop + 'Source'
            objKeyTb = item.prop + 'TbSource'
            currentObj = pieOption
            compareObj = pieOption
          }
          // 4、通过对3中两种来源的数据处理后，生成饼图需要的数据（外圈）
          if (currentObj.hasOwnProperty(objKey)) {
            // console.log('currentObj', JSON.parse(JSON.stringify(currentObj)))
            // 当前数据
            const innerLayerOption = {}
            // 过滤掉值为0的数据，不显示
            if (currentObj[objKey] !== 0) {
              innerLayerOption.value = currentObj[objKey]
              innerLayerOption.name = '(' + this.$t('table.currentLabel') + ')' + this.$t('table.' + item.label)
              if (item.hasOwnProperty('flag')) {
                innerLayerOption.flag = item.flag
              }
              innerLayerCurrent.push(innerLayerOption)
            }
            // 如果总量为0，不显示当前数据
            if (innerLayerCurrent.length > 0) {
              let allCurrentNum = 0
              innerLayerCurrent.forEach(item => {
                allCurrentNum = item.value + allCurrentNum
              })
              if (allCurrentNum === 0) {
                innerLayerCurrent = []
              }
            }
          }
          if (compareObj.hasOwnProperty(objKeyTb)) {
            // 同比数据
            const innerLayerCompareOption = {}
            // 过滤掉值为0的数据，不显示
            if (compareObj[objKeyTb] !== 0) {
              innerLayerCompareOption.value = compareObj[objKeyTb]
              innerLayerCompareOption.name = '(' + this.$t('table.yoy') + ')' + this.$t('table.' + item.label)
              if (item.hasOwnProperty('flag')) {
                innerLayerCompareOption.flag = item.flag
              }
              innerLayerCompare.push(innerLayerCompareOption)
            }
            // console.log('innerLayerCompare', JSON.parse(JSON.stringify(innerLayerCompare)))
            // 如果总量为0，不显示同比数据
            if (innerLayerCompare.length > 0) {
              let allCompareNum = 0
              innerLayerCompare.forEach(item => {
                allCompareNum = item.value + allCompareNum
              })
              if (allCompareNum === 0) {
                innerLayerCompare = []
              }
            }
            // console.log('innerLayerCompare', JSON.parse(JSON.stringify(innerLayerCompare)))
          }
        })
      }
      // console.log('innerLayerCurrent', JSON.parse(JSON.stringify(innerLayerCurrent)))
      // console.log('innerLayerCompare', JSON.parse(JSON.stringify(innerLayerCompare)))
      // 5、将4中的数据计算出总量（内圈）
      if (innerLayerCurrent.length > 0) {
        let allCurrentNum = 0
        innerLayerCurrent.forEach(item => {
          // 计算当前数据总量
          allCurrentNum = item.value + allCurrentNum
          // 将每一项push到数组中，用于给饼图赋值
          innerLayerData.push(item)
        })
        if (innerLayerCurrent[0].hasOwnProperty('flag')) {
          outerLayerData.push({ value: allCurrentNum, name: this.$t('pages.currentTotal'), flag: innerLayerCurrent[0].flag })
        } else {
          outerLayerData.push({ value: allCurrentNum, name: this.$t('pages.currentTotal') })
        }
      }
      // 同上
      if (innerLayerCompare.length > 0) {
        let allCompareNum = 0
        innerLayerCompare.forEach(item => {
          allCompareNum = item.value + allCompareNum
          innerLayerData.push(item)
        })
        if (innerLayerCompare[0].hasOwnProperty('flag')) {
          outerLayerData.push({ value: allCompareNum, name: this.$t('pages.lastPeriodTotal'), flag: innerLayerCompare[0].flag })
        } else {
          outerLayerData.push({ value: allCompareNum, name: this.$t('pages.lastPeriodTotal') })
        }
      }
      // 双层饼图需要的pieChartData.outerLayer数据
      this.temp.pieChartData.outerLayer = outerLayerData
      // 双层饼图需要的pieChartData.innerLayer数据
      this.temp.pieChartData.innerLayer = innerLayerData
      // 鼠标悬停数据处理(如果是文件大小或者时长，需要处理单位)
      // console.log('outerLayerData', JSON.parse(JSON.stringify(outerLayerData)))
      if (this.temp.pieChartData.outerLayer.length > 0) {
        const objFlag = this.temp.pieChartData.outerLayer[0].flag
        this.temp.pieChartOption.tooltip = {
          formatter: (d) => {
            // console.log('d', d)
            let number = d.value
            if (objFlag === 'size') {
              number = convert(d.value)
            } else if (objFlag === 'time') {
              number = formatSeconds(d.value)
            }
            return d.marker + d.name + ' : ' + number
          }
        }
      }
      // console.log('temp', JSON.parse(JSON.stringify(this.temp)))
    },
    /**
     * 判断两个对象，如果存在相同的key对应的值都是0，将这个字段从对象中删掉
     * */
    removeZeroValues(obj1, obj2) {
      const keysToRemove = [];
      for (const key in obj1) {
        if (obj1[key] === 0 && obj2[key] === 0) {
          keysToRemove.push(key);
        }
      }
      for (const key of keysToRemove) {
        delete obj1[key];
        delete obj2[key];
      }
    },
    /**
     * 环比双轴图数据处理（正常接口查询和表格行时间点击时都有用到）
     * 分别将接口返回数据和表格点击获取到的行数据，处理成双轴柱状图需要的格式
     * 通过接口返回数据与页面“统计项设置”配合映射出双轴柱状图所要显示的数据series和yAxis
     * @param barOption 接口返回的双轴柱状图数据qoqOption 或者 行点击时，获取到的行数据row
     * @param type 区分接口直接获取还是表格时间点击 getData:接口获取 cellClick：表格行点击
     * */
    qoqPieDataProcessing(barOption, type) {
      // console.log('columnList', this.query.columnList)
      // console.log('barOption', JSON.parse(JSON.stringify(barOption)))
      // 初始化时，后台返回的柱状图字段可能不全，会导致页面缺柱子，通过当前选中统计项，对比，将统计项中没有的字段都加上0，保证字段一样
      // 然后在对比两个对象，将key相同，且value为0的都删掉
      const selectColumn = this.query.columnList
      if (type === 'getData') {
        for (let i = 0; i < selectColumn.length; i++) {
          // console.log('barOption.compareData.hasOwnProperty(selectColumn[i])', barOption.compareData.hasOwnProperty(selectColumn[i]), selectColumn[i])
          // console.log('barOption.data.hasOwnProperty(selectColumn[i])', barOption.data.hasOwnProperty(selectColumn[i]), selectColumn[i])
          if (barOption.compareData.hasOwnProperty(selectColumn[i]) === false) {
            barOption.compareData[selectColumn[i]] = 0
          }
          if (barOption.data.hasOwnProperty(selectColumn[i]) === false) {
            barOption.data[selectColumn[i]] = 0
          }
          // barOption.compareData.hasOwnProperty(selectColumn[i])
        }
        this.removeZeroValues(barOption.data, barOption.compareData);
        // console.log('barOption', JSON.parse(JSON.stringify(barOption)))
      }
      const seriesCurrentData = []
      const seriesCompareData = []
      const yAxisData = []
      let flag = ''
      // 1、先分别处理生成当前和同比的数据
      if (this.statisticalList.length > 0) {
        this.statisticalList.forEach(item => {
          // console.log('item', item)
          // 2、接口返回数据与页面“统计项设置”映射出双轴柱状图图需要的数据
          // 3、接口返回和当前行点击时的数据格式不一致，所以要做简单处理
          // objKey:对象中的key currentObj：当前值对象 compareObj：环比值对象
          let objKey = ''
          let objKeyHb = ''
          let currentObj = {}
          let compareObj = {}
          if (type === 'getData') {
            // 接口获取，对数据的处理
            objKey = item.prop
            objKeyHb = item.prop
            currentObj = barOption.data
            compareObj = barOption.compareData
          } else {
            // 表格点击，对数据的处理(存在文件大小和时长，所以在表格列中多多了source源数据)
            objKey = item.prop + 'Source'
            objKeyHb = item.prop + 'HbSource'
            currentObj = barOption
            compareObj = barOption
          }
          // 4、通过对3中两种来源的数据处理后，生成柱状图图需要的数据
          if (currentObj.hasOwnProperty(objKey) && compareObj.hasOwnProperty(objKeyHb)) {
            if (item.hasOwnProperty('flag')) {
              flag = item.flag
            }
            // 两根柱子至少有一个不为0时，在push进去进行数据显示
            if (currentObj[objKey] !== 0 || compareObj[objKeyHb] !== 0) {
              // 当前
              seriesCurrentData.push(currentObj[objKey])
              // 对比
              seriesCompareData.push(compareObj[objKeyHb])
              yAxisData.push(this.$t('table.' + item.label))
            }
          }
        })
      }
      // console.log('seriesCurrentData', JSON.parse(JSON.stringify(seriesCurrentData)))
      // console.log('seriesCompareData', JSON.parse(JSON.stringify(seriesCompareData)))
      // console.log('flag', flag)
      // 双轴柱状图需要的yAxis数据
      this.temp.barChartOption.yAxis[0].data = yAxisData
      this.temp.barChartOption.yAxis[1].data = yAxisData
      this.temp.barChartOption.yAxis[2].data = yAxisData
      // 双轴柱状图需要的series数据
      this.temp.barChartOption.series[0].data = seriesCurrentData
      this.temp.barChartOption.series[0].name = this.$t('table.currentLabel')
      this.temp.barChartOption.series[0].flag = flag
      this.temp.barChartOption.series[1].data = seriesCompareData
      this.temp.barChartOption.series[1].name = this.$t('table.qoq')
      this.temp.barChartOption.series[1].flag = flag
      // console.log('this.temp', JSON.parse(JSON.stringify(this.temp)))
    },
    /**
     * 表格列表处理，（根据接口返回数据，动态生成表格列）
     * 注：一级表头label为原本有的表格字段，二级表头的名称统一修改成：当前（currentLabel）、同比变化（yoyLabel）、环比变化（qoqLabel）、上一个周期（previousCycle），否则多语言能给人写死
     * @param tableData 接口返回的表格数据
     * @param type 区分是同比还是环比，增长率计算方式不同 qoq:环比， yoy:同比
     * */
    tableColProcessing(tableData, type) {
      // console.log('this.statisticalList', JSON.parse(JSON.stringify(this.statisticalList)))
      // console.log('tableData', tableData)
      // 表格列，默认存在时间列
      const tableDataArrayCol = [
        { prop: 'time', label: 'time', detailChart: true, fixed: 'left', width: 240 }
      ]
      // 定义表格列，自动生成表格列数据
      let colProp = ''
      let colLabel = ''
      if (type === 'yoy') {
        colProp = 'Tb'
        colLabel = 'yoyLabel'
      } else {
        colProp = 'Hb'
        colLabel = 'qoqLabel'
      }
      if (this.statisticalList.length > 0 && tableData.length > 0) {
        for (let i = 0; i < this.statisticalList.length; i++) {
          let tableObjCol = {}
          const objKey = this.statisticalList[i].prop
          const headLabel = this.statisticalList[i].label
          if (tableData[0].hasOwnProperty(objKey)) {
            tableObjCol = { prop: objKey + 'Headline', label: headLabel, children: [
              { prop: objKey, label: 'currentLabel' },
              { prop: objKey + colProp, label: 'previousCycle' },
              { prop: objKey + 'Increase', label: colLabel }
            ] }
          }
          if (Object.keys(tableObjCol).length !== 0) {
            tableDataArrayCol.push(tableObjCol)
          }
        }
      }
      // console.log('tableDataArrayCol', JSON.parse(JSON.stringify(tableDataArrayCol)))
      return tableDataArrayCol
    },
    /**
     * 表格数据处理，处理成页面多级表头所需要的数据
     * @param tableData 接口返回的表格数据
     * @param type 区分是同比还是环比，增长率计算方式不同 qoq:环比， yoy:同比
     * */
    tableDataProcessing(tableData, type) {
      // console.log('this.statisticalList', JSON.parse(JSON.stringify(this.statisticalList)))
      // console.log('tableData', tableData)
      const tableDataRow = []
      // 1、根据接口返回处理显示接口有返回的数据
      if (this.statisticalList.length > 0 && tableData.length > 0) {
        for (let j = 0; j < tableData.length; j++) {
          const tableObj = {}
          for (let i = 0; i < this.statisticalList.length; i++) {
            const objKey = this.statisticalList[i].prop
            const objFlag = this.statisticalList[i].flag
            // console.log('objFlag', objFlag)
            // 数据列表数据字段，当前数据是正常返回的字段，同比环比是在正常字段名称后面加Tb或Hb,增长率是在正常字段后面加Increase
            const hb = objKey + 'Hb'
            const tb = objKey + 'Tb'
            const increase = objKey + 'Increase'
            if (tableData[j].hasOwnProperty(objKey)) {
              // 年份
              const yearLabel = parseInt(tableData[j].year)
              // 月份或季度
              const timeLabel = parseInt(tableData[j].time)
              // 年份的前一年
              const preYear = yearLabel - 1
              if (type === 'yoy') {
                // 同比数据
                if (this.query.dimBaseType !== 1) {
                  tableObj.time = yearLabel + this.$t('text.year') + timeLabel + this.$t('text.month') + ' / ' + preYear + this.$t('text.year') + timeLabel + this.$t('text.month')
                } else {
                  tableObj.time = yearLabel + this.$t('pages.yearControl') + timeLabel + this.$t('text.quarter') + ' / ' + preYear + this.$t('pages.yearControl') + timeLabel + this.$t('text.quarter')
                }
              } else {
                // 环比数据
                let hbYear
                let hbTime
                // 如果月份或季度减1为0，则说明要和上一年的最后一年或上一年的最后一个季度进行环比
                if ((timeLabel - 1) === 0) {
                  hbYear = preYear
                  if (this.query.dimBaseType !== 1) {
                    hbTime = 12
                  } else {
                    hbTime = 4
                  }
                } else {
                  hbYear = yearLabel
                  hbTime = timeLabel - 1
                }
                if (this.query.dimBaseType !== 1) {
                  tableObj.time = yearLabel + this.$t('text.year') + timeLabel + this.$t('text.month') + ' / ' + hbYear + this.$t('text.year') + hbTime + this.$t('text.month')
                } else {
                  tableObj.time = yearLabel + this.$t('pages.yearControl') + timeLabel + this.$t('text.quarter') + ' / ' + hbYear + this.$t('pages.yearControl') + hbTime + this.$t('text.quarter')
                }
              }
              // 数量、文件大小、时长显示处理（对文件大小和时长的显示数据要重新处理）
              const currentData = tableData[j][objKey].data
              const compareData = tableData[j][objKey].compareData
              if (objFlag === 'size') {
                // 当前数据处理
                tableObj[objKey] = convert(currentData)
                // 同比、环比数据处理
                if (type === 'yoy') {
                  // 同比数据
                  tableObj[tb] = convert(compareData)
                } else {
                  // 环比数据
                  tableObj[hb] = convert(compareData)
                }
              } else if (objFlag === 'time') {
                // 当前数据处理
                tableObj[objKey] = formatSeconds(currentData)
                // 同比、环比数据处理
                if (type === 'yoy') {
                  // 同比数据
                  tableObj[tb] = formatSeconds(compareData)
                } else {
                  // 环比数据
                  tableObj[hb] = formatSeconds(compareData)
                }
              } else {
                // 当前数据处理
                tableObj[objKey] = currentData
                // 同比、环比数据处理
                if (type === 'yoy') {
                  // 同比数据
                  tableObj[tb] = compareData
                } else {
                  // 环比数据
                  tableObj[hb] = compareData
                }
              }
              // 源数据处理(存在文件大小和时长，所以在表格列中多多了source源数据)
              tableObj[objKey + 'Source'] = currentData
              // 同比、环比数据处理
              if (type === 'yoy') {
                // 同比数据
                tableObj[tb + 'Source'] = compareData
              } else {
                // 环比数据
                tableObj[hb + 'Source'] = compareData
              }
              let increaseNum
              // 增长率处理
              if (objFlag === 'size') {
                // 文件大小的增长率按转换后去掉单位的数字进行计算
                // console.log('convert(currentData)', convert(currentData))
                const currentDataSize = convert(currentData)
                const compareDataSize = convert(compareData)
                // 截取数字部分
                const currentNum = currentDataSize.replace(/[^0-9.]/g, '')
                const compareDataNum = compareDataSize.replace(/[^0-9.]/g, '')
                // 截取单位部分
                const currentNoNum = currentDataSize.match(/[A-Za-z]+/g).join('')
                const compareDataNoNum = compareDataSize.match(/[A-Za-z]+/g).join('')
                // console.log('currentDataSize', currentDataSize)
                // console.log('compareDataSize', compareDataSize)
                //
                // console.log('currentNum', currentNum)
                // console.log('compareDataNum', compareDataNum)
                //
                // console.log('currentNoNum', currentNoNum)
                // console.log('compareDataNoNum', compareDataNoNum)
                if (currentNoNum === compareDataNoNum) {
                  // 如果单位一样，按页面现有显示数据计算百分比
                  increaseNum = (((currentNum - compareDataNum) / compareDataNum) * 100).toFixed(2)
                } else {
                  // 如果单位不一样，按原始数据计算百分比
                  increaseNum = (((currentData - compareData) / compareData) * 100).toFixed(2)
                }
              } else {
                increaseNum = (((currentData - compareData) / compareData) * 100).toFixed(2)
              }
              let increaseLabel
              // console.log('increaseNum', increaseNum)
              // 分母为0
              if (increaseNum === 'Infinity' || increaseNum === '-Infinity') {
                increaseLabel = '∞'
                // 分子分母都为0
              } else if (increaseNum === 'NaN') {
                increaseLabel = '0.00%'
              } else {
                increaseLabel = increaseNum + '%'
              }
              tableObj[increase] = increaseLabel
              // console.log('tableObj', tableObj)
            }
          }
          tableDataRow.push(tableObj)
        }
        // console.log('tableDataRow', JSON.parse(JSON.stringify(tableDataRow)))
        return tableDataRow
      }
    },
    /**
     * 部门折线top选中发生变化时，接口重新获取部门折线图数据
     * */
    topChange() {
      this.query.groupName = ''
      // 接口调用，获取页面数据
      getReportCompareChart(this.query, this.apiDept).then(res => {
        // console.log('部门接口获取', res)
        // 右上角折线图（部门）数据处理
        this.lineRightDeptDataProcessing(res.data)
      })
    },
    /**
     * 搜索按钮点击
     */
    loadReport() {
      // 页面初始化隐藏下钻表格
      // this.showDetailTB = false
      // this.showDetailHB = false
      // 页面初始化清除对小标题的点击修改状态
      if (this.statisticalList.length === 0) {
        this.query.columnList = []
      }
      this.showSelect = this.query.columnList
      const currentDate = this.query.dimValue.substring(0, 4)
      const quarter = this.query.dimValue.substring(this.query.dimValue.length - 2)
      const lastChart = this.query.dimValue.substring(this.query.dimValue.length - 1)
      const currentDateBefore = Number(currentDate) - 1
      const quarterBefore = Number(lastChart) - 1
      if (quarter === '00') {
        // 季度外都显示年
        this.tbChartChangeTitle = '“' + currentDate + this.$t('text.year') + `<span style="color: orange">(` + this.$t('table.currentLabel') + `)</span>/` + currentDateBefore + this.$t('text.year') + `<span style="color: orange">(` + this.$t('table.yoy') + `)</span>` + '”'
      } else {
        // 如果是季度，同比当前年前一年的同同一个季度
        this.tbChartChangeTitle = '“' + currentDate + this.$t('pages.yearControl') + lastChart + this.$t('text.quarter') + `<span style="color: orange">(` + this.$t('table.currentLabel') + `)</span>/` + currentDateBefore + this.$t('pages.yearControl') + lastChart + this.$t('text.quarter') + `<span style="color: orange">(` + this.$t('table.yoy') + `)</span>` + '”'
      }
      this.tbTableChangeTitle = ''
      this.deptChangeTitle = ''
      if (quarter === '00') {
        // 季度外都显示年
        this.hbChartChangeTitle = '“' + currentDate + this.$t('text.year') + `<span style="color: orange">(` + this.$t('table.currentLabel') + `)</span>/` + currentDateBefore + this.$t('text.year') + `<span style="color: orange">(` + this.$t('table.qoq') + `)</span>` + '”'
      } else if (quarter === '01') {
        // 如果当前是第一季度，环比就是去年第4季度
        this.hbChartChangeTitle = '“' + currentDate + this.$t('pages.yearControl') + lastChart + this.$t('text.quarter') + `<span style="color: orange">(` + this.$t('table.currentLabel') + `)</span>/` + currentDateBefore + this.$t('pages.compareReport21') + `<span style="color: orange">(` + this.$t('table.qoq') + `)</span>` + '”'
      } else {
        // 如果当前是不是第一季度，环比就是当前年的前一个季度
        this.hbChartChangeTitle = '“' + currentDate + this.$t('pages.yearControl') + lastChart + this.$t('text.quarter') + `<span style="color: orange">(` + this.$t('table.currentLabel') + `)</span>/` + currentDate + this.$t('pages.yearControl') + quarterBefore + this.$t('text.quarter') + `<span style="color: orange">(` + this.$t('table.qoq') + `)</span>` + '”'
      }
      this.hbTableChangeTitle = ''
      this.query.groupName = ''
      this.deptflag = ''
      // 接口调用，获取页面数据
      getReportCompareChart(this.query, this.api).then(res => {
        // console.log('response获取接口数据1212', res)
        if (res.hasOwnProperty('data')) {
          // 接口有返回data的时候才处理，否则显示暂无数据
          this.noData = false
          // 左上角折线图（统计项）数据处理
          this.lineLeftDataProcessing(res.data.option)
          // 同比双层饼图数据处理
          this.yoyPieDataProcessing(res.data.yoyOption, 'getData')
          // 同比表格数据处理
          this.colModelTB = this.tableColProcessing(res.data.yoyData, 'yoy')
          this.rowDatasTB = this.tableDataProcessing(res.data.yoyData, 'yoy')
          // 环比双轴图数据处理
          this.qoqPieDataProcessing(res.data.qoqOption, 'getData')
          // 环比表格数据处理
          this.colModelHB = this.tableColProcessing(res.data.yoyData, 'qoq')
          this.rowDatasHB = this.tableDataProcessing(res.data.qoqData, 'qoq')
          // 页面初始化部门折线图设置
          this.showRightLg = 24
          this.showRightDept = false
        } else {
          this.noData = true
          this.colModelTB = []
        }
      })
    },
    /**
     *表格点击--下钻(同比)
     * 表格点击时间，左侧图表显示当前点击的数据
     *单元格点击数字查看明细（暂时没有）
     * */
    cellClickTB(row, column, cell, event) {  // 单元格点击数字查看明细
      this.colModelTB.forEach(item => {
        // 时间列点击时，左侧图表显示当前时间对应信息
        if (item.prop === column.property && item.hasOwnProperty('detailChart')) {
          // console.log('row', JSON.parse(JSON.stringify(row)))
          // console.log('row.time', row.time)
          const str = row.time
          const parts = str.split('/');
          this.tbChartChangeTitle = '“' + parts[0] + `<span style="color: orange">(` + this.$t('table.currentLabel') + `)</span>/` + parts[1] + `<span style="color: orange">(` + this.$t('table.yoy') + `)</span>` + '”'
          this.yoyPieDataProcessing(row, 'cellClick')
        }
        // 表格特定列，（多级表头）点击下钻
        // if (item.children) {
        //   item.children.forEach(child => {
        //     // 表格下钻
        //     if (child.prop === column.property && child.hasOwnProperty('detailCol')) {
        //       this.showDetailTB = true
        //     }
        //   })
        // }
        // // 表格特定列，（通用表头）点击下钻
        // if (item.prop === column.property && item.hasOwnProperty('detailCol')) {
        //   this.showDetailTB = true
        // }
      })
    },
    /**
     *表格点击--下钻(环比)
     * 表格点击时间，左侧图表显示当前点击的数据
     *单元格点击数字查看明细（暂时没有）
     * */
    cellClickHB(row, column, cell, event) {  // 单元格点击数字查看明细
      // console.log('当前点击行信息', JSON.parse(JSON.stringify(row)))
      this.colModelHB.forEach(item => {
        // 时间列点击时，左侧图表显示当前时间对应信息
        if (item.prop === column.property && item.hasOwnProperty('detailChart')) {
          const str = row.time
          const parts = str.split('/');
          this.hbChartChangeTitle = '“' + parts[0] + `<span style="color: orange">(` + this.$t('table.currentLabel') + `)</span>/` + parts[1] + `<span style="color: orange">(` + this.$t('table.qoq') + `)</span>` + '”'
          // console.log('row', JSON.parse(JSON.stringify(row)))
          this.qoqPieDataProcessing(row, 'cellClick')
        }
        // 表格特定列，（多级表头）点击下钻
        // if (item.children) {
        //   item.children.forEach(child => {
        //     // 表格下钻
        //     if (child.prop === column.property && child.hasOwnProperty('detailCol')) {
        //       this.showDetailHB = true
        //     }
        //   })
        // }
        // // 表格特定列，（通用表头）点击下钻
        // if (item.prop === column.property && item.hasOwnProperty('detailCol')) {
        //   this.showDetailHB = true
        // }
      })
    },
    /**
     * 统计项设置，弹框打开
     * */
    handleStatistical() {
      // 弹框打开时，选中复选框根据当前表格显示列进行选中操作
      if (this.colModelTB.length > 0 && this.statisticalList.length > 0) {
        this.showSelect = []
        this.colModelTB.forEach(item => {
          this.statisticalList.forEach(list => {
            if (item.children && (item.prop === list.prop + 'Headline')) {
              this.showSelect.push(item.children[0].prop)
            }
          })
        })
        this.query.columnList = this.showSelect
      }
      this.dialogVisible = true
    },
    /**
     * 统计项设置，确认按钮点击
     * 1、统计项必须至少选中一条数据
     * 2、统计项的种类可能存在数量、文件大小、时长，statisticalList传值如果存在大小或时长，则配置中存在flag:size或flag：time,通过判断是否存在flag以及flag的值，确认是否选中多个维度的统计
     * */
    handleStatisticalConfirm() {
      // console.log('this.query.columnList确认弹框', JSON.parse(JSON.stringify(this.query.columnList)))
      // console.log('this.query.statisticalList', JSON.parse(JSON.stringify(this.statisticalList)))
      if (this.query.columnList.length > 0) {
        // checkArray:当前选中；allList：总的复选框列表
        const checkArray = this.query.columnList
        const allList = this.statisticalList
        // 设置三种类型初始都是0,如果判断出存在某一种赋值为1
        let isNum = 0; let isSize = 0; let isTime = 0
        for (let i = 0; i < checkArray.length; i++) {
          for (let j = 0; j < allList.length; j++) {
            if (checkArray[i] === allList[j].prop) {
              if (allList[j].hasOwnProperty('flag') === true) {
                if (allList[j].flag === 'size') {
                  isSize = 1
                } else {
                  isTime = 1
                }
              }
              if (allList[j].hasOwnProperty('flag') === false) {
                isNum = 1
              }
            }
          }
        }
        // 三种类型如果只选中一种，allCategory为1
        const allCategory = isNum + isSize + isTime
        // console.log('isTime', isNum, isSize, isTime, allCategory)
        if (allCategory > 1) {
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.compareReport20'), type: 'error', duration: 2000 })
          return
        }
        this.loadReport()
        this.dialogVisible = false
      } else {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.compareReport19'), type: 'error', duration: 2000 })
        return
      }
    },
    handleStatisticalCancel() {
      if (this.showSelect.length > 0) {
        this.query.columnList = this.showSelect
      }
      this.dialogVisible = false
    },
    /**
     *表格点击--下钻（样式--同比、环比第一个表格）
     *可以下钻的，鼠标悬停变成小手指
     * */
    cellStyle({ row, column, rowIndex, columnIndex }) {
      let cellStyle = '';
      this.colModelHB.forEach(item => {
        // console.log('item', item)
        // console.log('column', column)
        // 表格特定列，（通用表头）可以点击的单元格添加鼠标悬停显示“小手指”
        if ((item.prop === column.property && item.hasOwnProperty('detailCol')) || (item.prop === column.property && item.hasOwnProperty('detailChart'))) {
          cellStyle = 'cursor: pointer;'
        }
        // 表格特定列，（多级表头）可以点击的单元格添加鼠标悬停显示“小手指”
        if (item.children) {
          item.children.forEach(child => {
            // 表格下钻
            if ((child.prop === column.property && child.hasOwnProperty('detailCol')) || (child.prop === column.property && child.hasOwnProperty('detailChart'))) {
              cellStyle = 'cursor: pointer;'
            }
          })
        }
      })
      return cellStyle;
    },
    /**
     * 报表日期下拉框选择时前端相关交互
     * dimBaseType 1:按年（季）统计 2:按年（月）统计 3:按季（月）统计
      * */
    loadDimTime(value) {
      const formatterOption = (dimValue) => {
        const dimBaseType = this.query.dimBaseType
        const dimYear = dimValue.substring(0, 4)
        if (dimBaseType === 4) {
          if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
          this.dateOptions[dimYear].children.push({ label: this.$t('pages.quarterNum', { Quarter: this.$t('pages.' + this.quarters[parseInt(dimValue.substring(4))]) }), value: dimValue })
        } else if (dimBaseType === 5) {
          if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
          this.dateOptions[dimYear].children.push({ label: this.$t('pages.weekNum', { Week: dimValue.substring(4) }), value: dimValue })
        } else {
          this.dateOptions.push({ label: this.$t('pages.year', { Year: dimYear }), value: dimValue })
        }
      }
      let timeType
      if (this.query.dimBaseType === 4) {
        timeType = 3
      } else if (this.query.dimBaseType === 5) {
        timeType = 2
      } else {
        timeType = 4
      }
      getDimTime(timeType).then(response => {
        // console.log('response日期', response)
        // 对接口返回的response.data进行处理，长度不到6位的补齐的6位，如：2024补齐成202400
        this.dateOptions.splice(0)
        response.data.sort((a, b) => {
          return a > b ? 1 : -1 // 返回正数 ，b排列在a之前
        })
        for (let i = 0; i < response.data.length; i++) {
          let time = response.data[i] + ''
          while (time.length < 6) { time += '0' }
          formatterOption(time)
        }
        ++this.cascaderKey
        const children = this.dateOptions[this.dateOptions.length - 1].children
        this.query.dimValue = (this.query.dimBaseType === 4 || this.query.dimBaseType === 5) ? (children[children.length - 1] ? children[children.length - 1].value : '') : this.dateOptions[this.dateOptions.length - 1].value
        value === 'initial' && this.loadReport()
      }).catch(() => {
        if (this.loading) {
          this.loading = false
        }
      })
    },
    /**
       * 报表日期后面下拉框选中发生变化操作
       * */
    dimValueChange(datas) {
      if (datas && datas.length > 0) {
        this.query.dimValue = datas[datas.length - 1]
      } else {
        this.query.dimValue = undefined
      }
    },
    /**
       * 获取row-key(下钻表格)
       * */
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    },
    /**
     * 统计项全选
     * */
    handleCheckAllChange(option) {
      this.query.columnList = option.map(item => item.prop)
    },
    /**
     * 统计项全不选
     * */
    handleCheckNone(option) {
      // 当前操作项对应的全部数据
      const currentList = option.map(item => item.prop)
      // console.log('currentList', currentList)
      // console.log('this.query.columnList', this.query.columnList)
      // 和当前选中对比，去重，删掉对应需要删掉的内容，如果直接赋[]会影响其它地方
      const del = this.query.columnList.filter(item => !currentList.includes(item))
      // console.log('del', del)
      this.query.columnList = del
    },
    // 同比点击弹框
    handleTb() {
      this.$refs['comparativeTbDialog'].show(this.temp.pieChartData, this.chartTitle, this.tbDialogBarData, this.statisticalList)
    },
    // 环比点击弹框
    handleHb() {
      this.$refs['comparativeHbDialog'].show(this.temp.barChartOption, this.chartTitle)
    },
    /**
     * 统计类型选中发生变化时
     * 传数据到父组件，用于某些菜单对统计项的控制
     * */
    handleCount(value) {
      this.showSelect = []
      this.$emit('getCountByObject', value)
      // this.checkedKeys.splice(0)
      // this.query.countByObject = null
      // this.query.objectIds = ''
    }
    /**
     * 获取下钻表格数据 （暂时没有）
     * */
    // getDetailData(option) {
    //   const searchQuery = Object.assign({}, this.detailQuery, option)
    //   return this.detailMethod(searchQuery)
    // },
    /**
     * 暂时好像没啥用，先隐藏
     * 统计部门选中发生变化时，对查询groupIds赋值
     * */
    // entityIdChange(key, nodeData, query) {
    //   // console.log('key', key)
    //   // console.log('nodeData点击', JSON.parse(JSON.stringify(nodeData)))
    //   if (nodeData && nodeData.length > 0) {
    //     const objectIds = []
    //     const groupIds = []
    //     nodeData.forEach(item => {
    //       if (item.dataType === 'G') {
    //         groupIds.push(item.dataId)
    //       } else {
    //         objectIds.push(item.dataId)
    //       }
    //     })
    //     this.query.objectIds = objectIds.join(',')
    //     this.query.groupIds = groupIds.join(',')
    //   } else {
    //     this.query.objectIds = ''
    //     this.query.groupIds = ''
    //   }
    //   // console.log('query', JSON.parse(JSON.stringify(this.query)))
    // },
  }
}
</script>

<style lang="scss" scoped>
  .table-container{
    display: block;
  }
  .content-div{
    height: calc(100vh - 140px);
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 25px;
  }
  .checkbox {
    padding: 0 20px;
    h4{
      margin: 0 0 15px 0;
    }
    .el-checkbox {
      display: block;
      margin: 0 0 20px 0;
      >>>.el-checkbox__label {
        display: inline-flex;
      }
    }
    .width-50-percent {
      width: 33%;
      float: left;
    }
  }
  .chart-wrapper{
    padding: 5px;
  }
  .span-mini-title{
    position: relative;
    font-size: 14px;
    font-weight: bold;
    z-index: 999;
  }
  .span-mini-title>span{
    color: #008acd !important;
  }
  .tip-foot-div{
    position: absolute;
    font-size: 12px;
    color: #2d8cf0;
  }
  >>>.el-icon-loading:before{
    display: none;
  }
</style>
