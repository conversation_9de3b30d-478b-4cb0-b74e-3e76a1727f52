<template>
  <div>
    <el-dialog
      v-if="dialogVisible"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.yearOnYearChart')"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div>
        <div class="toolbar" style="height: 30px;">
          <div style="float: right;">
            <el-select v-model="changeSelect">
              <el-option :value="0" :label="$t('pages.pie')"></el-option>
              <el-option :value="1" :label="$t('pages.bar')"></el-option>
            </el-select>
          </div>
        </div>
        <div class="no-pointer" style="height: 400px">
          <pie-double-chart
            v-if="changeSelect === 0"
            :chart-data="pieData"
            :chart-option="pieOption"
            style="width: 100%;height: 100%"
          />
          <bar-chart
            v-else
            :chart-data="barData"
            :chart-option="barOption"
            :x-axis-name="xAxisName"
            style="width: 100%;height: 100%"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart'
import PieDoubleChart from '@/components/ECharts/PieDoubleChart';
import { convert, formatSeconds } from '@/utils';

export default {
  name: 'ComparativeTbDialog',
  components: { BarChart, PieDoubleChart },
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      changeSelect: 0,
      xAxisName: this.$t('table.statisticalItem'),
      // 双层饼图
      pieData: {
        outerLayer: [],
        innerLayer: []
      },
      pieOption: {
        title: {
          text: ''
        },
        toolbox: {
          show: false
        },
        legend: {
          show: true,
          type: 'scroll', // 开启滚动
          pageIconColor: '#008acd', // 翻页按钮的颜色
          pageButtonItemGap: 5, // 设置分页按钮之间的间隔
          pageButtonGap: 5, // 设置图例与分页按钮之间的间隔
          left: 'center'
        },
        series: [
          {
            name: '',
            type: 'pie',
            animation: false,
            radius: ['20%', '40%'],
            label: {
              position: 'inner'
            }
          },
          {
            name: '',
            type: 'pie',
            radius: ['30%', '40%'],
            label: {
              normal: {
                position: 'outside',
                formatter: '{b} {d}% '
              }
            }
          }
        ]
      },
      // 柱状图
      barData: [],
      barOption: {
        title: {
          text: ''
        },
        toolbox: {
          show: false
        },
        legend: {
          show: true,
          data: [this.$t('table.currentLabel'), this.$t('table.yoy')]
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 30,
            formatter: function(value, index) {
              // 如果文本长度超过8个字，则返回前八个字加上省略号
              if (value.length > 8) {
                return value.slice(0, 8) + '...';
              }
              // 如果文本长度不超过8个字，则直接返回原文本
              return value;
            }
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          splitArea: {
            show: false
          }
        },
        series: [
          {
            data: [],
            name: this.$t('table.currentLabel'),
            type: 'bar',
            barGap: 0,
            emphasis: { focus: 'series' }
          },
          {
            data: [],
            name: this.$t('table.yoy'),
            type: 'bar',
            barGap: 0,
            emphasis: { focus: 'series' }
          }
        ]
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    /**
     * 显示弹框
     * @param tempOption 双层饼图pieChartData数据
     * @param chartTitle 当前报表名称
     * @param barData 柱状图数据
     * @param statisticalList 列表数据
     */
    show(tempOption, chartTitle, barData, statisticalList) {
      this.changeSelect = 0
      // console.log('chartTitle', chartTitle)
      // console.log('tempOption弹框显示', JSON.parse(JSON.stringify(tempOption)))
      this.pieData = tempOption
      this.dialogVisible = true
      // 双层饼图（数据处理）
      this.pieOption.title.text = chartTitle + this.$t('pages.yearOnYearChart')
      // 弹框位置比较大，可以对饼图单独处理label显示
      this.pieOption.series[0].label.normal = {
        formatter: (params) => {
          // 如果标签角度小于20度，不显示标签
          if (params.percent < 0) {
            return '...';
          }
          // 如果名称长度超过20个字符，截断并添加省略号
          if (params.name.length > 20) {
            return params.name.substring(0, 20) + '...';
          } else {
            return params.name;
          }
        }
      }
      if (this.pieData.outerLayer.length > 0) {
        const objFlag = this.pieData.outerLayer[0].flag
        this.pieOption.tooltip = {
          formatter: (d) => {
            // console.log('d', d)
            let number = d.value
            if (objFlag === 'size') {
              number = convert(d.value)
            } else if (objFlag === 'time') {
              number = formatSeconds(d.value)
            }
            return d.marker + d.name + ' : ' + number
          }
        }
      }
      this.pieOption.series[1].label.labelLine = {
        normal: { length: 185, length2: 185 }
      }
      // 柱状图（数据处理，因为后台返回的数据同比和当前字段长度不一致，所以前端只能自己处理数据）
      // 1、横坐标名称处理
      const currentName = []
      // 2、data数据处理
      // console.log('barData弹框', JSON.parse(JSON.stringify(barData)))
      const getCurrentData = []
      const getCompareData = []
      if (statisticalList.length > 0) {
        statisticalList.forEach(item => {
          const objKey = item.prop
          if (barData.data.hasOwnProperty(objKey)) {
            getCurrentData.push(barData.data[objKey])
            // 对比
            getCompareData.push(barData.compareData[objKey])
            currentName.push(this.$t('table.' + item.label))
          }
        })
      }
      // 柱状图赋值
      this.barOption.title.text = chartTitle + this.$t('pages.yearOnYearChart')
      this.barOption.xAxis.data = currentName
      this.barOption.series[0].data = getCurrentData
      this.barOption.series[1].data = getCompareData

      if (this.pieData.outerLayer.length > 0) {
        const objFlag = this.pieData.outerLayer[0].flag
        // console.log('objFlag', objFlag)
        this.barOption.tooltip = {
          formatter: (d) => {
            // console.log('d', d)
            let res = d[0].name + '<br>';
            for (var i = 0; i < d.length; i++) {
              let number = d[i].value
              if (objFlag === 'size') {
                number = convert(d[i].value)
              } else if (objFlag === 'time') {
                number = formatSeconds(d[i].value)
              }
              res += d[i].marker + d[i].seriesName + ' : ' + number + '<br>'
            }
            return res
          }
        }
      }
    },
    hide() {
      this.dialogVisible = false
    },
    closed() {
    },
    handleDrag() {
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
<style>
  .no-pointer>div>div>canvas {
    cursor: default;
  }
</style>
