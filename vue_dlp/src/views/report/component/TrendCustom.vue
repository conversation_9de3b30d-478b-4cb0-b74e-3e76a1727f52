<!--自定义报表趋势-->
<template>
  <div ref="print" class="app-container" style="overflow:hidden;">
    <div v-loading="loading">
      <div class="toolbar clearfix" style="padding: 5px 20px 0 15px">
        <!--报表日期-->
        <span>
          {{ $t('pages.dimBaseType') }}：
          <el-select ref="dimType" v-model="query.dimBaseType" style="width: 135px;" @change="loadDimTime" >
            <el-option
              v-for="(item, index) in dateTypeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-cascader
            ref="cascader"
            :key="cascadeKey"
            v-model="query.dimValue"
            :options="dateOptions"
            :props="{ expandTrigger: 'hover' }"
            :separator="$t('pages.cascaderSep')"
            style="width: 160px; margin-right: 10px;line-height:0"
            @change="dimValueChange"
          ></el-cascader>
        </span>
        <span>
          <!--统计类型-->
          {{ $t('pages.countByObject') }}：
          <el-select v-model="query.countByObject" is-filter :placeholder="$t('text.select')" style="width: 150px; margin-right: 10px;">
            <el-option
              v-for="(item, index) in typeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </span>
        <span>
          <label style="color: #F56C6C">*</label>{{ $t('form.dataItem') }}<el-tooltip effect="dark" placement="bottom-end">
            <div slot="content">
              {{ $t('pages.customDataItemTip', { num: 5 }) }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>：
          <!--数据项中的数据用了原来列表设置中的值，页面中获取到选中值后，在点击搜索之前对数据进行处理，实现搜索后需要的表格列数据-->
          <el-select v-model="query.customDataItems" multiple filterable collapse-tags :multiple-limit="5" is-filter :placeholder="$t('text.select')" style="width: 250px; margin-right: 10px;">
            <el-option
              v-for="(item) in customList"
              :key="item.prop"
              :label="$t(`table.${item.label}`)"
              :value="item.prop"
            ></el-option>
          </el-select>
        </span>
        <!--搜索-->
        <el-button type="primary" size="mini" class="no-print" @click="search">{{ $t('text.search') }}</el-button>
        <el-select v-model="showType" style="width: 150px; margin-right: 10px; float: right;">
          <el-option :label="$t('pages.showType1')" :value="1"></el-option>
          <el-option :label="$t('pages.showType2')" :value="2"></el-option>
          <el-option :label="$t('pages.showType3')" :value="3"></el-option>
        </el-select>
      </div>
      <div class="table-container">
        <!--打印、导出-->
        <div class="no-print">
          <div style="display:flex;justify-content: flex-end;" >
            <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
            <report-exporter v-if="exportable" :api="api" :data="buildExcelData"/>
          </div>
        </div>
        <!--中间echarts图表-->
        <div v-if="showType!==3&&!echartsImg" style="height: 370px;">
          <el-row :gutter="10" class="dynamic-height">
            <el-col :lg="isShowChartInterpretation ? 20 : 24" style="margin-bottom: 5px;">
              <div class="panel-wrapper">
                <e-charts ref="charts" :charts-option="chartsOption" :y-axis-name="yAxisName" />
              </div>
            </el-col>
            <!--图表解读-->
            <el-col v-if="isShowChartInterpretation" :lg="4" style="margin-bottom: 5px;height: 100%;">
              <div class="panel-wrapper unscramble">
                <div v-html="chartInterpretationContent"></div>
              </div>
            </el-col>
          </el-row>
        </div>
        <!--没看出来是啥，可能和中间echarts图表有什么关系，在图表不显示的时候显示这个-->
        <div v-if="echartsImg" style="height: 370px; padding: 10px 0px;">
          <img :src="echartsImg" />
        </div>
        <!--存在下钻表格时的返回按钮-->
        <el-button v-if="showType!==2 && showDetail" size="mini" type="primary" style="margin-bottom: 5px" @click="showDetail = false">
          <i class="el-icon-back"></i>
        </el-button>
        <!--echarts图表下方的表格-->
        <grid-table
          v-show="showType!==2 && !showDetail"
          ref="dataList"
          :col-model="colModel"
          :row-datas="rowDatas"
          :header-rendering="true"
          :default-sort="{ prop: ''}"
          :multi-select="false"
          :show-pager="false"
          :min-height="200"
          :style="showType===1 ? {'height':'calc(100vh - 595px)'} : {'height':'calc(100% - 90px)'}"
          :cell-style="cellStyle"
          @cell-click="cellClick"
        />
        <!--下钻表格-->
        <grid-table
          v-show="showType!==2 && showDetail"
          ref="detailList"
          :autoload="false"
          :row-data-api="getDetailData"
          :col-model="detailModel"
          :multi-select="false"
          :show-pager="true"
          :min-height="200"
          :style="showType===1 ? {'height':'calc(100vh - 600px)'} : {'height':'calc(100% - 115px)'}"
          :row-key="getRowKey"
        />
      </div>
    </div>

    <!-- 查看详情弹窗 -->
    <Detail v-if="viewDetails" ref="detailDialog"/>
  </div>
</template>

<script>
import ECharts from '@/components/ECharts'
import { checkDetailAble, getDimTime, getReportChart, getTrendDetail } from '@/api/report/baseReport/issueReport/issue'
import { convert, formatSeconds } from '@/utils'
import moment from 'moment'
import ReportExporter from './ReportExporter'
import Detail from './Detail'

export default {
  name: 'TrendCustom',
  components: { ECharts, ReportExporter, Detail },
  props: {
    api: {
      type: String,
      default: ''
    },
    colModel: {
      type: Array,
      default() {
        return []
      }
    },
    customList: {
      type: Array,
      default() {
        return []
      }
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    },
    // 是否支持导出
    exportable: {
      type: Boolean,
      default: false
    },
    // 是否支持查看详情
    viewDetails: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 是否显示图表解读
      isShowChartInterpretation: false,
      chartInterpretationContent: `<div class="unscramble-title">图表解读：</div>
                1、从整体趋势上看，2024年硬件资产变更趋势较上个月同期<span class="unscramble-red">下降50%</span>。其中，5-6月<span class="unscramble-red">下降57%</span>，4-5月<span class="unscramble-green">上升15%</span>。<br/>
                2、从数量上看，<span class="unscramble-title">4月</span>变更数量最大，变更了<span class="unscramble-title">381</span>次，<span class="unscramble-red">12月</span>变更数量最小，变更了<span class="unscramble-green">53</span>次。<br/>
                结论：硬件资产变更还是保持在一个相对稳定的状态，还是<span class="unscramble-green">比较安全</span>的。`,
      loading: false,
      // echarts图表相关设置
      chartsOption: [],
      defaultChartsOption: {
        type: 'line',
        col: 24
      },
      chartsInfo: undefined,
      // echarts下方表格数据
      rowDatas: [],
      // 查询项
      query: {
        isTrend: 1,
        dimBaseType: 2,
        dimValue: '',
        // 数据项
        customDataItems: '',
        countByObject: 1,
        recordSize: 0,
        groupType: 0
      },
      // 报表日期相关配置
      dateTypeOptions: [
        { value: 5, label: this.$t('pages.trendDateTypeOptions1') },
        { value: 3, label: this.$t('pages.trendDateTypeOptions2') },
        { value: 4, label: this.$t('pages.trendDateTypeOptions3') },
        { value: 2, label: this.$t('pages.trendDateTypeOptions4') },
        { value: 1, label: this.$t('pages.trendDateTypeOptions5') }
      ],
      dateOptions: [],
      cascadeKey: 0,
      months: ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      quarters: ['', 'theFirst', 'theSecond', 'theThird', 'theFourth'],
      // 表格表头名称
      dateTableName: ['', this.$t('text.quarter'), this.$t('text.month'), this.$t('pages.week'), this.$t('text.month'), this.$t('table.day')],
      // 统计类型
      typeOptions: [{ value: 1, label: this.$t('pages.report_text4') }],
      // 看样子是根据值控制一下显示隐藏
      showType: 1,
      // 列表设置相关配置
      dialogVisible: false,
      selected: [],
      // 打印有用到，还有一些显示隐藏？？
      echartsImg: '',
      detailModel: [],
      tempQuery: {},
      showDetail: false,
      defaultDetailQuery: {
        isTrend: 1,
        page: 1,
        detailTable: '',
        label: ''
      },
      detailQuery: {},
      detailMethod: getTrendDetail,
      /** objectIds objectNames是用来查看明细时，点击查看其他对象明细时用到 */
      objectIds: [],
      objectNames: []
    }
  },
  computed: {
    // echarts图表下方的表格
    gridTable() {
      return this.$refs['dataList']
    },
    // 折线图纵坐标名称设置
    yAxisName() {
      return ''
    },
    dialogWidth() {
      return this.customList.length > 6 ? '800px' : '400px'
    }
  },
  created() {
    this.loading = true
    this.loadDimTime('initial')
    this.chartsOption.splice(0, 1, this.defaultChartsOption)
  },
  methods: {
    /**
     * 获取下钻表格数据
     * */
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      return this.detailMethod(searchQuery)
    },
    /**
     *表格点击--下钻（样式）
     *可以下钻的，鼠标悬停变成小手指
     * */
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (!this.drillDown) {
        return ''
      }
      let cellStyle = '';
      this.colModel.forEach(item => {
        if (item.prop === column.property && item.hasOwnProperty('detailCol')) {
          cellStyle = 'cursor: pointer;'
        }
      })
      return cellStyle;
    },
    /**
     *表格点击--下钻
     *单元格点击数字查看明细
     * */
    cellClick(row, column, cell, event) {
      if (!this.drillDown) {
        return
      }
      let col = null
      this.colModel.some(item => {
        if (item.prop === column.property) {
          col = item
        }
        return item.prop === column.property
      })
      // 如果表格列配置里面没有detailTable字段，说明不需要显示详情
      if (!col || (!col.detailTable && !col.getDetailMethod)) {
        return
      }

      // 替换终名称、用户名称、分组名称等的渲染函数
      for (const i of col.detailCol) {
        if (i.prop == 'user_name') {
          i.formatter = (row) => {
            if (!row.user_id || row.user_id == -1) {
              return this.$t('pages.null')
            }
            return row.user_name
          }
        }
        if (i.prop == 'user_group_name') {
          i.formatter = (row) => {
            if (!row.user_group_id || row.user_group_id == -1) {
              return ''
            }
            return row.user_group_name
          }
        }

        if (i.prop == 'terminal_name') {
          i.formatter = (row) => {
            if (!row.term_id || row.term_id == -1) {
              return this.$t('pages.null')
            }
            return row.terminal_name
          }
        }
        if (i.prop == 'term_group_name') {
          i.formatter = (row) => {
            if (!row.term_group_id || row.term_group_id == -1) {
              return ''
            }
            return row.term_group_name
          }
        }
      }

      // 绑定查看详情事件
      const operateCol = col.detailCol.find(i => i.label === 'operate')
      if (operateCol) {
        const detailBtn = operateCol.buttons.find(j => j.label === 'detail')
        if (detailBtn) {
          detailBtn.click = this.handleDetail
        }
      }
      // 明细表格列重新配置
      this.detailModel.splice(0, this.detailModel.length, ...col.detailCol)
      // 设置查询明细的条件
      this.detailQuery = Object.assign({}, this.defaultDetailQuery)
      this.detailQuery = Object.assign(this.detailQuery, this.tempQuery)
      this.detailQuery.label = row.label
      this.detailQuery.detailTable = col.detailTable

      if (col.beforeLoadDetail) {
        // 前置处理
        this.detailQuery = Object.assign({}, this.detailQuery, col.beforeLoadDetail(row, column, cell, event, this.detailQuery))
      } else {
        this.detailQuery = Object.assign({}, this.detailQuery)
      }
      checkDetailAble(this.detailQuery).then(res => {
        this.showDetail = true
        if (col && col.getDetailMethod) {
          // 表格详情的数据加载方法走特殊处理
          this.detailMethod = col.getDetailMethod
        } else {
          this.detailMethod = getTrendDetail
        }
        this.$refs.detailList.execRowDataApi(this.detailQuery)
      })
    },
    /**
     * 报表日期下拉框选择时前端相关交互
     * */
    loadDimTime(value) {
      const formatterOption = (dimValue) => {
        const dimBaseType = this.query.dimBaseType
        const dimYear = dimValue.substring(0, 4)
        if (dimBaseType === 4) {
          if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
          this.dateOptions[dimYear].children.push({ label: this.$t('pages.quarterNum', { Quarter: this.$t('pages.' + this.quarters[parseInt(dimValue.substring(4))]) }), value: dimValue })
        } else if (dimBaseType === 5) {
          if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
          this.dateOptions[dimYear].children.push({ label: this.$t('pages.weekNum', { Week: dimValue.substring(4) }), value: dimValue })
        } else {
          this.dateOptions.push({ label: this.$t('pages.year', { Year: dimYear }), value: dimValue })
        }
      }
      let timeType
      if (this.query.dimBaseType === 4) {
        timeType = 3
      } else if (this.query.dimBaseType === 5) {
        timeType = 2
      } else {
        timeType = 4
      }
      getDimTime(timeType).then(response => {
        this.dateOptions.splice(0)
        response.data.sort((a, b) => {
          return a > b ? 1 : -1 // 返回正数 ，b排列在a之前
        })
        for (let i = 0; i < response.data.length; i++) {
          let time = response.data[i] + ''
          while (time.length < 6) { time += '0' }
          formatterOption(time)
        }
        ++this.cascadeKey
        const children = this.dateOptions[this.dateOptions.length - 1].children
        this.query.dimValue = (this.query.dimBaseType === 4 || this.query.dimBaseType === 5) ? (children[children.length - 1] ? children[children.length - 1].value : '') : this.dateOptions[this.dateOptions.length - 1].value
        value === 'initial' && this.loadReport()
      }).catch(() => {
        if (this.loading) {
          this.loading = false
        }
      })
    },
    /**
     * 报表日期后面下拉框选中发生变化操作
     * */
    dimValueChange(datas) {
      if (datas && datas.length > 0) {
        this.query.dimValue = datas[datas.length - 1]
      } else {
        this.query.dimValue = undefined
      }
    },
    /**
     * 用于查询时调用
     * 数据项下拉框选中时，获取列表数据项值
     * 通过选中值，获取到colModel数据，用户表格列查询项的显示
     * */
    getDataIItemChange() {
      this.colModel.splice(1)
      const currentData = this.query.customDataItems
      if (currentData.length > 0) {
        for (let i = 0; i < currentData.length; i++) {
          for (let j = 0; j < this.customList.length; j++) {
            if (currentData[i] === this.customList[j].prop) {
              this.colModel.push(this.customList[j])
            }
          }
        }
      }
    },
    /**
     * 搜索按钮点击
     */
    search() {
      if (this.query.customDataItems.length === 0) {
        this.$notify({ title: this.$t('text.error'), message: this.$t('valid.requireDataItem'), type: 'error', duration: 2000 })
        return
      }

      this.getDataIItemChange()
      this.loadReport()
    },
    loadReport() {
      if (!this.loading) {
        this.loading = true
      }
      this.showDetail = false
      this.tempQuery = Object.assign({}, this.query)
      getReportChart(this.query, this.api).then(respond => {
        this.loading = false
        this.chartsInfo = respond.data
        if (this.chartsInfo) {
          this.rowDatas.splice(0, this.rowDatas.length, ...this.chartsInfo.oriData)
          this.chartsOption.splice(0, 1, this.chartsInfo)
          // 拼接趋势图显示日期
          const labelColmn = this.gridTable.colModel[0]
          const dimBaseType = this.query.dimBaseType
          const dimValue = this.query.dimValue
          labelColmn.formatter = (row, data) => {
            return this.dateFormat(dimBaseType, dimValue, data)
          }
          this.chartsInfo.option.tooltip = {
            formatter: (d) => {
              let res = this.html2Escape(d[0].axisValueLabel) + '<br>';
              for (var i = 0; i < d.length; i++) {
                let value = d[i].data
                const col = this.getCol(d[i].seriesName);
                if (col != null) {
                  const data = this.chartsInfo.oriData[d[i].dataIndex]
                  value = this.valueFormat(data, value, col)
                }
                res += d[i].seriesName + ' : ' + value + '<br>'
              }
              return res
            },
            confine: true,
            extraCssText: 'white-space: break-spaces;'
          }
          const xAxis = this.chartsInfo.option.xAxis[0].data
          if (xAxis != null) {
            this.chartsInfo.option.xAxis[0].data = xAxis.map(item => { return this.dateFormat(dimBaseType, dimValue, item) })
          }
          // 大小转换
          this.gridTable.colModel.forEach(item => {
            if (item.flag === 'size') {
              item.formatter = (row, data) => {
                return convert(data)
              }
            }
          })
          // 趋势图日期表格头名称
          this.colModel[0].label = this.dateTableName[this.query.dimBaseType]
          this.colModel[0].fixed = true
        }
      }).catch(() => {
        this.loading = false
      })
    },
    /**
     * echarts图表日期显示的一些配置
     * @param dimBaseType
     * @param dimValue
     * @param data
     * @returns {VueI18n.TranslateResult | TranslateResult}
     */
    dateFormat(dimBaseType, dimValue, data) {
      if (dimBaseType === 5) {
        const str = data.toString()
        if (str.length === 4) {
          const month = str.substring(0, 2)
          const day = str.substring(2, 4)
          return this.$t('text.monthDay', { Month: this.$t('text.' + this.months[month]), Day: day })
        } else {
          const month = str.substring(0, 1)
          const day = str.substring(1, 3)
          return this.$t('text.monthDay', { Month: this.$t('text.' + this.months[month]), Day: day })
        }
      } else if (dimBaseType === 4) {
        return this.$t('pages.yearQuarterMonth', { Year: dimValue.substring(0, 4), Quarter: this.$t('pages.' + this.quarters[parseInt(dimValue.substring(4))]), Month: this.$t('text.' + this.months[data]) })
      } else if (dimBaseType === 3) {
        return this.$t('pages.yearWeek', { Year: dimValue.substring(0, 4), Week: data })
      } else if (dimBaseType === 2) {
        return this.$t('pages.yearMonth', { Year: dimValue.substring(0, 4), Month: this.$t('text.' + this.months[data]) })
      } else {
        return this.$t('pages.yearQuarter', { Year: dimValue.substring(0, 4), Quarter: this.$t('pages.' + this.quarters[parseInt(data)]) })
      }
    },
    /**
     * 图表鼠标悬停时，获取表格列信息，在图表上显示具体信息（某一项名称）
     * */
    getCol(seriesName) {
      const colModel = this.customList.find(item => {
        return this.$t('table.' + item.label) === seriesName
      })
      return colModel
    },
    /**
     * 图表鼠标悬停时，获取表格列信息，在图表上显示具体信息（某一项值）
     * */
    valueFormat(data, value, colModel) {
      if (colModel.flag === 'size') {
        value = convert(value)
      } else if (colModel.flag === 'time') {
        value = formatSeconds(value)
      } else if (colModel.formatter) {
        return colModel.formatter(data, value)
      }
      return value
    },
    /**
     * 打印
     * */
    handlePrint() {
      if (!this.chartsInfo) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.compareReport24'),
          type: 'error'
        })
        return
      }
      this.colModel[0].fixed = false
      this.echartsImg = this.$refs.charts ? this.$refs.charts.$refs.line[0].chart._api.getDataURL({
        excludeComponents: ['toolbox']
      }) : ''
      // 只显示列表时没有charts，所以这边要加判断，否则会报错
      if (this.$refs.charts) {
        this.$refs.charts.$refs.line[0].chart.resize()
      }
      setTimeout(() => {
        this.$print(this.$refs.print)
      }, 100)
      setTimeout(() => {
        this.echartsImg = ''
        this.colModel[0].fixed = true
      }, 200)
    },
    /**
     * 导出
     * */
    buildExcelData() {
      if (!this.chartsInfo) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.compareReport24'),
          type: 'error'
        })
        return
      }
      let imgBase64Info = ''
      if (this.$refs.charts) {
        imgBase64Info = this.$refs.charts ? this.$refs.charts.$refs.line[0].chart._api.getDataURL({
          excludeComponents: ['toolbox'],
          backgroundColor: '#fff'
        }) : ''
        this.$refs.charts.$refs.line[0].chart.resize()
      }

      return Object.assign(this.tempQuery, {
        imgBase64Info,
        showType: this.showType
      })
    },
    /**
     * 获取row-key(下钻表格)
     * */
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    },
    /**
     * 查看详情事件
     * @param row 当前行数据
     */
    handleDetail(row) {
      if (this.$refs.detailDialog) {
        this.$refs.detailDialog.visible = true
        this.$refs.detailDialog.rowData = row
        this.$refs.detailDialog.detailCol = this.detailModel
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container{
  display: block;
  height: calc(100vh - 175px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 5px;
}
</style>
