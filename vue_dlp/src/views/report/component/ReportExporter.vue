<template>
  <el-button :type="buttonType" @click="handleExport"><i :class="iconClass"></i>{{ buttonName }}</el-button>
</template>

<script>
import { buildDownloadFileByName, fetchDownload } from '@/utils/download/helper'

export default {
  name: 'ReportExporter',
  props: {
    api: {
      type: String,
      required: true
    },
    data: {
      type: [Object, Function],
      default: null
    },
    filename: {
      type: String,
      default() {
        return this.$t('route.' + this.$route.meta.title) + '.xls'
      }
    },
    topic: {
      type: String,
      default: 'ReportForms'
    },
    buttonType: {
      type: String,
      default: 'text'
    },
    buttonIcon: {
      type: [Boolean, String],
      default: 'el-icon-download'
    },
    buttonName: {
      type: String,
      default() {
        return this.$t('button.export')
      }
    }
  },
  computed: {
    iconClass() {
      if ('string' === typeof this.buttonIcon && this.buttonIcon.length > 0) {
        return this.buttonIcon
      }
      if (this.buttonIcon) {
        return 'el-icon-download'
      }
      return null
    }
  },
  methods: {
    handleExport() {
      if ('function' === typeof this.data) {
        const data = this.data()
        if (data instanceof Promise) {
          data.then(this.exportReport)
        } else {
          this.exportReport(data)
        }
      } else {
        this.exportReport(this.data)
      }
      this.$emit('exported')
    },
    exportReport(data) {
      if (!data) {
        return
      }
      const file = buildDownloadFileByName(this.filename)
      return fetchDownload({
        topic: this.topic,
        url: `/excel/${this.api}`,
        method: 'post',
        responseType: 'blob',
        timeout: 0,
        jwt: true,
        data,
        file
      })
    }
  }
}
</script>
