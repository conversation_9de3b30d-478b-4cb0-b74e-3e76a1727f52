<template>
  <div ref="print" class="app-container" style="overflow:hidden;">
    <div v-loading="loading">
      <div class="toolbar clearfix" style="padding: 5px 20px 0 15px">
        <!--        <span v-if="!searchDateTime">-->
        <span>
          {{ $t('pages.dimBaseType') }}：
          <el-select v-model="query.dimBaseType" style="width: 140px;" @change="loadDimTime" >
            <el-option
              v-for="(item, index) in dateTypeOptions"
              :key="index"
              :class="[+ !searchDateTime &&item.value === 6 ? 'hidden-options' : '']"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-cascader
            v-if="query.dimBaseType !== 6"
            :key="cascaderKey"
            v-model="query.dimValue"
            :options="dateOptions"
            :props="{ expandTrigger: 'hover' }"
            :separator="$t('pages.cascaderSep')"
            style="width: 150px; margin-right: 10px;line-height:0"
            @change="dimValueChange"
          ></el-cascader>
        </span>
        <span v-if="query.dimBaseType === 6">
          <el-date-picker
            v-model="query.startTime"
            value-format="yyyy-MM-dd"
            type="date"
            style="width: 140px"
            :picker-options="pickerOptions"
            :placeholder="$t('pages.startTime')"
          />
          <span>~</span>
          <el-date-picker
            v-model="query.endTime"
            value-format="yyyy-MM-dd"
            type="date"
            style="width: 140px"
            :picker-options="pickerOptions"
            :placeholder="$t('pages.endTime')"
          />
        </span>
        <!--<el-select v-model="query.dimValue" is-filter :placeholder="$t('text.select')" style="width: 150px; margin-right: 10px;" @change="loadReport" >
          <el-option
            v-for="(item, index) in dateOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>-->
        <!--高级查询（查询条件太多了，页面放不下）-->
        <el-popover
          v-model="visibleAdvanced"
          :append-to-body="false"
          placement="bottom"
          width="350"
          trigger="click"
        >
          <Form ref="searchLeftForm" label-position="right" :model="query" label-width="65px">
            <!--统计类型-->
            <FormItem v-if="showCountByObject" :label="$t('pages.countByObject')">
              <el-select v-model="query.countByObject" is-filter :placeholder="$t('text.select')" @change="handleCount">
                <el-option
                  v-for="(item, index) in typeOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  :disabled="onlyTerminal&&item.value===1"
                ></el-option>
              </el-select>
            </FormItem>
            <FormItem v-if="showCountByObject && query.countByObject != 3" :label="$t('pages.countObject')">
              <tree-select
                ref="objectTree"
                node-key="id"
                class="targetObject"
                :height="350"
                :width="500"
                clearable
                multiple
                check-strictly
                :menu-name="menuName"
                :click-node="clickNode"
                :local-search="false"
                :checked-keys="checkedKeys"
                is-filter
                :data="query.countByObject == 2 ? reportTermTree : reportUserTree"
                :get-search-list="getSearchListFunction"
                style="margin-left: -5px;width: 264px"
                @deptTreeData="deptTreeData"
                @change="(key, nodeData) => entityIdChange(key, nodeData, query)"
              />
            </FormItem>
            <FormItem label="Top">
              <el-select ref="top" v-model="query.recordSize" allow-create filterable @visible-change="visibleChange">
                <el-option
                  v-for="(item, index) in sizeOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </FormItem>
            <!--排序项-->
            <FormItem :label="$t('pages.sortField')">
              <el-select v-model="query.sortName" filterable>
                <el-option
                  v-for="(item, index) in sortModels"
                  :key="index"
                  :label="item.escapeFormat ? item.label : ($te(`table.${item.label}`) ? $t(`table.${item.label}`) : item.label)"
                  :value="item.prop"
                ></el-option>
              </el-select>
            </FormItem>
            <FormItem label="">
              <!--按部门统计-->
              <el-checkbox v-show="showGroupType" v-model="query.groupType" :false-label="0" :true-label="1" :label="$t('pages.countByGroup')" class="group-type"></el-checkbox>
              <el-checkbox v-show="showGroupType" v-model="query.collectSubDept" :false-label="0" :true-label="1" :label="$t('pages.collectSubDept')" class="group-type" :disabled="disabledCollectSubDept"></el-checkbox>
            </FormItem>
            <!--图表解读-->
            <!--<FormItem label="">-->
            <!--<el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>-->
            <!--</FormItem>-->
          </Form>
          <div style="text-align: right; margin-top: 10px">
            <!--重置、查询按钮-->
            <el-button size="mini" @click="resetQuery">{{ $t('button.reset') }}</el-button>
            <el-button type="primary" size="mini" @click="loadReport()">{{ $t('table.search') }}</el-button>
          </div>
          <el-button slot="reference" type="primary" class="search-icon-left" size="mini">{{ $t('button.advancedSearch') }}</el-button>
        </el-popover>

        <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="loadReport">{{ $t('text.search') }}</el-button>
        <slot name="button"></slot>
        <el-select v-model="showType" style="width: 150px; float: right;">
          <el-option :label="$t('pages.showType1')" :value="1"></el-option>
          <el-option :label="$t('pages.showType2')" :value="2"></el-option>
          <el-option :label="$t('pages.showType3')" :value="3"></el-option>
        </el-select>
      </div>
      <div class="table-container">
        <div class="no-print">
          <div style="display:flex;justify-content: flex-end;" >
            <el-button v-if="showType!==2 && !(disableSubchartColumnSetting && subColumnValue)" type="text" @click.native="handleColumn"><i class="el-icon-s-tools"></i>{{ $t('pages.handleColumn') }}</el-button>
            <el-button type="text" @click="handlePrint"><i class="el-icon-printer"></i>{{ $t('pages.print') }}</el-button>
            <el-button v-if="exportable" type="text" @click="handleExport"><i class="el-icon-download"></i>{{ $t('button.export') }}</el-button>
          </div>
        </div>
        <div v-if="showType!=3&&!echartsImg" style="height: 370px;">
          <el-row :gutter="10" class="dynamic-height">
            <el-col :lg="showInterpretation" style="margin-bottom: 5px;">
              <div class="panel-wrapper">
                <e-charts ref="charts" :charts-option="chartsOption" :x-axis-name="xAxisName"/>
              </div>
            </el-col>
            <!--图表解读-->
            <el-col v-if="chartInterpretationContent.length > 0" :lg="4" style="margin-bottom: 5px;height: 100%;">
              <div class="panel-wrapper unscramble">
                <div v-html="chartInterpretationContent"></div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div v-if="echartsImg" style="height: 370px; padding: 10px 0px;">
          <img :src="echartsImg" />
        </div>

        <el-button v-if="showType!==2 && showDetail" size="mini" type="primary" style="margin-bottom: 5px" @click="showDetail = false">
          <i class="el-icon-back"></i>
        </el-button>
        <grid-table
          v-show="showType!==2 && !showDetail"
          ref="dataList"
          :col-model="colModel"
          :row-datas="rowDatas"
          :default-sort="{ prop: ''}"
          :multi-select="false"
          :show-pager="false"
          :min-height="200"
          :not-limit-height="!!echartsImg||onlyTabel"
          :style="showType===1 ? {'height':'calc(100vh - 590px)'} : {'height':'calc(100% - 90px)'}"
          :cell-style="cellStyle"
          @cell-click="cellClick"
        />
        <grid-table
          v-show="showType!==2 && showDetail"
          ref="detailList"
          :autoload="false"
          :row-data-api="getDetailData"
          :col-model="detailModel"
          :multi-select="false"
          :show-pager="detailShowPager"
          :min-height="200"
          :not-limit-height="!!echartsImg||onlyTabel"
          :style="showType===1 ? {'height':'calc(100vh - 600px)'} : {'height':'calc(100% - 115px)'}"
          :row-key="getRowKey"
        />
      </div>
    </div>
    <!-- 列表设置 -->
    <el-dialog
      v-el-drag-dialog
      :append-to-body="false"
      :title="$t('pages.handleColumn')"
      :width="dialogWidth"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :modal="false"
    >
      <div class="checkbox">
        <h4>{{ $t('pages.report_text1') }}</h4>
        <div v-show="selected.length===0" style="color:red;font-size:12px;margin-bottom:10px">{{ $t('pages.report_text2') }}</div>
        <div>
          <el-button type="text" @click="handleCheckAllChange">{{ $t('button.selectAll') }}</el-button> /
          <el-button type="text" @click="handleCheckNone">{{ $t('button.selectNone') }}</el-button>
        </div>
        <el-checkbox-group v-model="selected">
          <el-checkbox
            v-for="(item) in curCustomList"
            :key="item.prop"
            :label="item"
            :class="{ 'width-50-percent': customList.length > 6 }"
          >{{ item.escapeFormat ? item.label : ($te(`table.${item.label}`) ? $t(`table.${item.label}`) : item.label) }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleChoose">{{ $t('button.confirm2') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>
    <!-- 导入弹窗 -->
    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.export')"
      width="400px"
      :visible.sync="dialogVisibleExport"
      :close-on-click-modal="false"
      :modal="false"
    >
      <div>
        <el-radio v-model="exportAll" label="top">
          {{ $t('pages.exportTop') }}
          <el-input-number v-model="exportNum" style="width:160px;margin:5px 10px" :min="query.recordSize" :precision="0" :max="100000" :disabled="exportAll === 'all'"></el-input-number>
        </el-radio>
        <el-radio v-model="exportAll" label="all">
          {{ $t('pages.exportAll') }}
        </el-radio>
      </div>
      <span slot="footer" class="dialog-footer">
        <report-exporter
          :api="api"
          :data="buildExcelData"
          button-type="primary"
          :button-icon="false"
          :button-name="$t('button.confirm2')"
          @exported="dialogVisibleExport = false"
        />
        <el-button @click="dialogVisibleExport=false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>

    <!-- 查看详情弹窗 -->
    <Detail v-if="viewDetails" ref="detailDialog"/>
  </div>
</template>

<script>
import ECharts from '@/components/ECharts'
import { getDimTime, getReportChart, getDetailPage, checkDetailAble } from '@/api/report/baseReport/issueReport/issue'
import { convert, formatSeconds, flowAxis, timeAxis } from '@/utils'
import moment from 'moment'
import ReportExporter from './ReportExporter'
import Detail from './Detail'
import { mapGetters } from 'vuex';

export default {
  name: 'Statistical',
  components: { ECharts, ReportExporter, Detail },
  props: {
    detailExtraData: {
      type: Object,
      default() {
        return {}
      }
    },
    // 详情是否分页
    detailShowPager: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 数据请求接口
    api: {
      type: String,
      default: ''
    },
    // 默认展示的数据列
    defaultColModel: {
      type: Array,
      default() {
        return []
      }
    },
    // 饼图钻取后展示的数据列
    subColModel: {
      type: Array,
      default() {
        return []
      }
    },
    // 自定义数据列
    customList: {
      type: Array,
      default() {
        return []
      }
    },
    // 报表日期查询显示时间段还是多种情况查询
    searchDateTime: {
      type: Boolean,
      default: false
    },
    // 是否只支持按终端统计
    onlyTerminal: {
      type: Boolean,
      default: false
    },
    conversionTime: {
      type: Boolean,
      default() {
        return false
      }
    },
    // Y轴是否以流量为单位
    conversionFlow: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 自定义格式化显示参数
    formatOption: {
      type: Function,
      default(option) {
      }
    },
    // 是否展示统计类型
    showCountByObject: {
      type: Boolean,
      default() {
        return true
      }
    },
    labelClickable: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 统计类型
    countByObject: {
      type: Number,
      default: 2
    },
    terminalFilter: { type: Function, default: null },
    // 统计类型数组
    typeOptions: {
      type: Array,
      default() {
        return [{ value: 1, label: this.$t('pages.conutType1') }, { value: 2, label: this.$t('pages.conutType2') }]
      }
    },
    // 是否支持按部门统计
    showGroupType: {
      type: Boolean,
      default: true
    },
    // 首列名称
    xAxisLabel: {
      type: String,
      default: ''
    },
    /*
     * 是否需要自定义查询明细数据的条件
     * 针对统计类型为其他的情况（统计类型既不是按操作员统计也不是按终端统计），需要自定义查询明细数据的条件
    */
    customizedCondition: {
      type: Boolean,
      default: false
    },
    // 是否支持下钻
    drillDown: {
      type: Boolean,
      default: false
    },
    // 是否支持导出
    exportable: {
      type: Boolean,
      default: false
    },
    // 是否支持查看详情
    viewDetails: {
      type: Boolean,
      default: false
    },
    disableSubchartColumnSetting: {
      type: Boolean,
      default: false
    },
    customTooltipFormatter: {
      type: Function,
      default: null
    },
    /**
     * 饼图prop后缀，因为之前是写死的，但是有的命名后缀并不是这个
     */
    propSuffix: {
      type: String,
      default: 'All'
    },
    /**
     * 默认排序字段
     */
    defaultSortProp: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 图表解读
      showInterpretation: 24,
      chartInterpretationContent: '',
      deptData: [],
      pickerOptions: {
        disabledDate: time => {
          // return time.getTime() < new Date(new Date().setDate(new Date().getDate() - 1)).getTime()
        }
      },
      loading: false,
      checkedKeys: [],
      // 存放部门树当前选中数据
      clickNode: [],
      menuName: 'reportMenu',
      detailMethod: getDetailPage,
      chartsInfo: undefined,
      chartsOption: [],
      defaultChartsOption: {
        type: 'pie',
        col: 24
      },
      colModel: [
        { prop: 'name', label: 'name', width: '150', sort: true }
      ],
      detailModel: [],
      columnList: [],
      rowDatas: [],
      query: {
        objectType: null,
        objectId: '',
        objectIds: '',
        groupIds: '',
        dimBaseType: 1,
        dimValue: '',
        countByObject: this.countByObject || 2,
        recordSize: 5,
        groupType: 0,
        // 是否归集子部门：0-否，1-是
        collectSubDept: 0,
        // 是否展示图表解读
        isExplain: false,
        sortName: '',
        sortOrder: 'desc',
        startTime: '',
        endTime: '',
        selectedColumn: {}
      },
      dateTypeOptions: [
        { value: 5, label: this.$t('pages.dateTypeOptions1') },
        { value: 2, label: this.$t('pages.dateTypeOptions2') },
        { value: 1, label: this.$t('pages.dateTypeOptions3') },
        { value: 3, label: this.$t('pages.dateTypeOptions4') },
        { value: 4, label: this.$t('pages.dateTypeOptions5') },
        { value: 6, label: this.$t('pages.countByTimeRange') }
      ],
      dateOptions: [],
      sizeOptions: [{ value: 5, label: 5 }, { value: 10, label: 10 }, { value: 20, label: 20 }],
      showType: 1,
      // selectedDimValue: [],
      cascaderKey: 0,
      dialogVisible: false,
      dialogVisibleExport: false,
      visibleAdvanced: false,
      selected: [],
      selectedFinal: [],  // 这个是排序下拉框的选项所需要的属性，为了防止修改显示的列，但是没有点击保存，而selected还是会进行修改的问题
      downloadLoading: false,
      echartsImg: '',
      subColumnValue: undefined,
      subColumnLabel: undefined,
      onlyTabel: false,
      curCustomList: [],
      tempQuery: {},
      exportNum: 0,
      exportAll: 'top',
      showDetail: false,
      detailQuery: {
        page: 1,
        detailObjectIds: '',
        objectIds: '',
        objectNames: [],
        detailTable: '',
        condition: {},
        groupIds: '',
        filterGroupIds: '',
        filterObjectIds: '',  // 不显示的对象id
        filterObjectNames: [], // 不显示的对象名称
        filterCondition: {}   // 不显示的条件
      },
      // 统计对象终端树和操作员树（报表要过滤需要自定义一个字段进行过滤，否则会影响到别的页面的数据）
      reportTermTree: [],
      reportUserTree: [],
      /** objectIds objectNames是用来查看明细时，点击查看其他对象明细时用到 */
      objectIds: [],
      objectNames: [],
      months: ['', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      quarters: ['', 'theFirst', 'theSecond', 'theThird', 'theFourth']
    }
  },
  computed: {
    // 缓存好的终端树和操作员树
    ...mapGetters([
      'termTree',
      'termTreeList',
      'userTree',
      'userTreeList'
    ]),
    gridTable() {
      return this.$refs['dataList']
    },
    dialogWidth() {
      // console.log(999, this.customList.length);
      return this.customList.length > 6 ? '800px' : '400px'
    },
    xAxisName() {
      const label = this.xAxisLabel ? this.xAxisLabel : this.query.countByObject === 1 ? this.$t('table.user') : this.$t('table.terminal')
      if (!this.labelClickable) {
        if (this.query.groupType === 1) return this.$t('table.dept')
      }
      return this.query.groupType === 1 ? `${label}${this.$t('table.dept')}` : label
    },
    sortModels() {
      const list = this.selectedFinal.map(item => {
        const obj = {
          label: item.label,
          prop: item.prop,
          escapeFormat: item.escapeFormat
        }
        if (item.sortField) {
          obj.prop = item.sortField
        }
        return obj
      })
      return list
    },
    // 是否禁用归集子部门
    disabledCollectSubDept() {
      return this.query.groupType === 0
    }
  },
  watch: {
    // 终端树发生变化时，监听
    termTree() {
      const tree = JSON.parse(JSON.stringify(this.termTree))
      this.filterTermTree(tree)
      this.reportTermTree = tree
    },
    userTree() {
      this.reportUserTree = JSON.parse(JSON.stringify(this.userTree))
    },
    showType(value) {
    },
    customList() {
      const arr = this.defaultColModel.map(item => item.prop)
      this.selected = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.defaultSort()
    },
    disabledCollectSubDept(newValue) {
      if (newValue) {
        this.query.collectSubDept = 0
      }
    },
    // 图表解读显示和隐藏时，图表容易宽度会发生变化，echarts图表需要自适应
    showInterpretation(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.$nextTick(() => {
          if (this.chartsInfo.type === 'pie') {
            this.$refs.charts.$refs.pie[0].__resizeHandler()
          } else {
            this.$refs.charts.$refs.bar[0].__resizeHandler()
          }
        })
      }
    }
  },
  created() {
    this.loading = true
    this.loadDimTime('initial')
    this.chartsOption.splice(0, 1, this.defaultChartsOption)
    this.colModel.splice(0, this.colModel.length, ...this.defaultColModel)
    this.colModel[0].label = this.xAxisName
    this.colModel[0].fixed = true
    const arr = this.defaultColModel.map(item => item.prop)
    this.selected = this.customList.filter(item => {
      return arr.indexOf(item.prop) !== -1
    })
    this.defaultSort()
    // 初始化统计对象树赋值
    if (this.termTree !== undefined) {
      const tree = JSON.parse(JSON.stringify(this.termTree))
      this.filterTermTree(tree)
      this.reportTermTree = tree
    }
    if (this.userTree !== undefined) {
      this.reportUserTree = JSON.parse(JSON.stringify(this.userTree))
    }
  },
  methods: {
    /**
     * 终端树过滤（老板终端、U判断终端、永久离线终端）
     * */
    filterTermTree(termTree) {
      for (let i = termTree.length - 1; i >= 0; i--) {
        const item = termTree[i]
        if (item.dataType === 'G') {
          this.filterTermTree(item.children || [])
          continue
        }
        const type = (parseInt(item.dataType) & 0xf0) >> 4
        if (type === 1 || type === 2 || type === 8) {
          termTree.splice(i, 1)
        }
      }
    },
    /**
     * 因为data数据为外传数据，所以需要从外部传入搜索列表（内置数据才会有搜索列表）
     * */
    getSearchListFunction() {
      if (this.query.countByObject == 2) {
        // 终端
        return this.termTreeList
      } else {
        // 操作员
        return this.userTreeList
      }
    },
    /**
     * 获取树结构数据（TreeSelect组件传递给父组件数据）
     * 注：该数据不是一次性全部加载出来的，在组件数据展开时会发生变化
     * */
    deptTreeData(val) {
      this.deptData = val
      // console.log('val111111111111', JSON.parse(JSON.stringify(val)))
    },
    /**
     * 遍历树结构数据
     * @param nodes 当前树结构数据
     * @param nodeData 当前选中的数据（因为通过选中判断是否可选所以判断是取第一个即可）
     * (直接点击)nodeData[0].type 1、终端，2、操作员，3、终端分组，4、操作员分组
     * (查询点击)nodeData[0].type 1、终端，2、操作员，G、终端分组，G、操作员分组
     * */
    getCheckIdsAll(nodes = [], nodeData) {
      // console.log('nodes', nodes)
      if (nodes != null && nodes.length > 0) {
        nodes.forEach(item => {    // 遍历树 拼入相应的disabled
          if (nodeData.length > 0) {
            if (JSON.stringify(item) != '{}') {
              if (nodeData[0].type != item.type) {
                this.$set(item, 'disabled', true)
                this.getCheckIdsAll(item.children, nodeData)
              } else {
                this.$set(item, 'disabled', false)
                this.getCheckIdsAll(item.children, nodeData)
              }
            }
          } else {
            this.$set(item, 'disabled', false)
            this.getCheckIdsAll(item.children, nodeData)
          }
        })
      }
    },
    /**
     * 统计部门选中时做禁用操作
     * */
    entityIdChange(key, nodeData, query) {
      // console.log('key', key)
      // console.log('nodeData点击', JSON.parse(JSON.stringify(nodeData)))
      this.query.objectType = this.query.countByObject == 1 ? 2 : 1
      if (nodeData && nodeData.length > 0) {
        const objectIds = []
        const groupIds = []
        nodeData.forEach(item => {
          if (item.dataType === 'G') {
            groupIds.push(item.dataId)
          } else {
            objectIds.push(item.dataId)
          }
        })
        this.query.objectIds = objectIds.join(',')
        this.query.groupIds = groupIds.join(',')
      } else {
        this.query.objectIds = ''
        this.query.groupIds = ''
      }
      // console.log('clickNode', JSON.parse(JSON.stringify(this.clickNode)))
      // 选中，争对选中做是否可选限制
      // this.getCheckIdsAll(this.deptData, this.clickNode)
    },
    defaultSort() {
      this.selectedFinal.splice(0)
      this.selectedFinal.push(...this.selected)
      // 优先使用存在的排序项（query.sortName）
      if (this.query.sortName && this.sortModels.some(item => item.prop === this.query.sortName)) {
        return
      }

      // 再使用defaultSortProp
      if (this.defaultSortProp && this.sortModels.some(item => item.prop === this.defaultSortProp)) {
        this.query.sortName = this.defaultSortProp
        return
      }

      // 最后使用第一个可排序字段
      if (this.sortModels.length > 0) {
        this.query.sortName = this.sortModels[0].prop
        return
      }

      this.query.sortName = ''
    },
    getDetailData(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      searchQuery.detailExtraData = this.detailExtraData
      if (searchQuery.startTime) {
        searchQuery.startTime += ' 00:00:00'
      }
      if (searchQuery.endTime) {
        searchQuery.endTime += ' 00:00:00'
      }
      return this.detailMethod(searchQuery)
    },
    resetDetailQuery() {
      this.detailQuery.objectNames.splice(0)
      this.detailQuery.detailObjectIds = ''
      this.detailQuery.filterObjectIds = ''
      this.detailQuery.filterObjectNames.splice(0)
      this.detailQuery.filterCondition = {}
      this.detailQuery.condition = {}
      this.detailQuery.objectIds = ''
      this.detailQuery.groupIds = ''
      this.detailQuery.filterGroupIds = ''
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (!this.drillDown) {
        return ''
      }
      let cellStyle = '';
      this.colModel.forEach(item => {
        if (item.prop === column.property && item.hasOwnProperty('detailCol')) {
          cellStyle = 'cursor: pointer;'
        }
      })
      return cellStyle;
    },
    cellClick(row, column, cell, event) {  // 单元格点击数字查看明细
      if (!this.drillDown) {
        return
      }
      let col = null
      const detailCondition = {}
      const propSearchMap = {}
      this.colModel.forEach(item => {
        if (item.prop == column.property) {
          col = item
        }
        if (item.joinSearch) {
          if (item.searchProp) {
            propSearchMap[item.searchCol] = item.searchProp
            if (0 !== row[item.searchProp] && !row[item.searchProp]) {
              detailCondition[item.searchCol] = this.$t('pages.other')
            } else {
              detailCondition[item.searchCol] = row[item.searchProp]
            }
          } else {
            detailCondition[item.searchCol] = row[item.prop]
            propSearchMap[item.searchCol] = item.prop
          }
        }
      })
      this.colModel.forEach(item => {
        if (item.prop == column.property && item.searchCol && item.searchValue) {
          detailCondition[item.searchCol] = item.searchValue
          propSearchMap[item.searchCol] = item.searchValue
        }
      })
      if (!col || (!col.detailTable && !col.getDetailMethod)) {
        return
      }
      this.resetDetailQuery()
      this.detailQuery = Object.assign(this.detailQuery, this.tempQuery)
      this.detailQuery.page = 1

      // 替换终名称、用户名称、分组名称等的渲染函数
      for (const i of col.detailCol) {
        if (i.prop == 'user_name') {
          i.formatter = (row) => {
            if (!row.user_id || row.user_id == -1) {
              return this.$t('pages.null')
            }
            return row.user_name
          }
        }
        if (i.prop == 'user_group_name') {
          i.formatter = (row) => {
            if (!row.user_group_id || row.user_group_id == -1) {
              return ''
            }
            return row.user_group_name
          }
        }

        if (i.prop == 'terminal_name') {
          i.formatter = (row) => {
            if (!row.term_id || row.term_id == -1) {
              return this.$t('pages.null')
            }
            return row.terminal_name
          }
        }
        if (i.prop == 'term_group_name') {
          i.formatter = (row) => {
            if (!row.term_group_id || row.term_group_id == -1) {
              return ''
            }
            return row.term_group_name
          }
        }
      }

      // 绑定查看详情事件
      const operateCol = col.detailCol.find(i => i.label === 'operate')
      if (operateCol) {
        const detailBtn = operateCol.buttons.find(j => j.label === 'detail')
        if (detailBtn) {
          detailBtn.click = this.handleDetail
        }
      }

      // 验证是否可以查看明细，因为报表系统会有清理历史数据功能，所以有些时间是不支持查看明细的。
      checkDetailAble(this.detailQuery).then(res => {
        // 明细表格列重新配置
        this.detailModel.splice(0, this.detailModel.length, ...col.detailCol)
        // 如果表格列配置里面没有detailTable字段，说明不需要显示详情
        // 设置查询明细的条件
        this.showDetail = true
        this.detailQuery.detailTable = col.detailTable

        // 点击查看明细时，有六种情况需要进行查询条件判断。
        // 1对象 2.其他对象 3.对象-类型条件 4.对象-其他类型条件 5其他对象-类型条件 6.其他对象-其他类型条件
        let customizedCondition = {}
        let customizedFilterCondition = {}
        if (row.label !== this.$t('pages.other')) {  // 对象
          // 设置指定显示的对象
          if (this.customizedCondition) {
            this.$emit('customizedCondition', row.label, result => {
              customizedCondition = result
            })
          } else {
            if (this.query.collectSubDept === 1) {
              // 如果是查看归集部门的明细时,上报的条件只需要说查看部门的id
              this.detailQuery.groupIds = row.labelValue
            } else {
              this.detailQuery.detailObjectIds = row.labelValue
              this.detailQuery.objectNames.splice(0, 1, row.labelValue + this.dealUnknown(row.label) + this.dealUnknown(row.groupName))
            }
          }
        } else { // 其他对象
          // 设置不显示的对象
          if (this.customizedCondition) {
            this.$emit('customizedCondition', [...this.objectNames], result => {
              customizedFilterCondition = result
            })
          } else {
            if (this.query.collectSubDept === 1) {
              // 如果是查看其他对象的归集部门的明细时,需要过滤掉前面几个部门id
              this.detailQuery.filterGroupIds = this.objectIds.join(',')
              // 并且归集的部门也要查询，总结就是查询归集的部门id且过滤掉前几的部门id就是其他对象的明细
              this.detailQuery.groupIds = this.query.groupIds
            } else {
              this.detailQuery.filterObjectNames = [...this.objectNames]
            }
          }
        }
        if (Object.keys(detailCondition)) {
          if (Object.values(detailCondition)[0] !== this.$t('pages.other')) {  // 类型条件
            this.detailQuery.condition = detailCondition
          } else { // 其他类型条件
            const filterCondition = {}  // 保存需要过滤不显示的类型值
            // 遍历表格数据获取类型值，并保存假如过滤条件中
            this.rowDatas.forEach(item => {
              for (const i in detailCondition) {
                if (filterCondition[i] == null) {
                  filterCondition[i] = []
                }
                if (item[propSearchMap[i]] || 0 === item[propSearchMap[i]]) {
                  Array.isArray(item[propSearchMap[i]]) ? filterCondition[i].push(...item[propSearchMap[i]]) : filterCondition[i].push(item[propSearchMap[i]])
                }
              }
            })
            this.detailQuery.filterCondition = filterCondition
          }
        }

        if (JSON.stringify(customizedCondition) !== '{}') {
          Object.assign(this.detailQuery.condition, customizedCondition)
        }
        if (JSON.stringify(customizedFilterCondition) !== '{}') {
          Object.assign(this.detailQuery.filterCondition, customizedFilterCondition)
        }
        if (col && col.beforeLoadDetail) {
          // 前置处理
          this.detailQuery = Object.assign({}, this.detailQuery, col.beforeLoadDetail(row, column, cell, event, this.detailQuery))
        } else {
          this.detailQuery = Object.assign({}, this.detailQuery)
        }

        if (col && col.getDetailMethod) {
          // 表格详情的数据加载方法走特殊处理
          this.detailMethod = col.getDetailMethod
        } else {
          this.detailMethod = getDetailPage
        }
        // 搜索条件的过滤对象id
        this.detailQuery.objectType = this.query.objectType
        this.detailQuery.objectIds = this.query.objectIds
        this.$refs.detailList.execRowDataApi(this.detailQuery)
      })
    },
    loadDimTime(value) {
      // 如果不按时间段查询，清空时间段
      if (this.query.dimBaseType !== 6) {
        this.query.startTime = ''
        this.query.endTime = ''
      }
      const formatterOption = (dimValue) => {
        const dimBaseType = this.query.dimBaseType
        const dimYear = dimValue.substring(0, 4)
        if (dimBaseType === 4) {
          this.dateOptions.push({ label: this.$t('pages.year', { Year: dimYear }), value: dimValue })
        } else {
          if (!this.dateOptions[dimYear]) this.dateOptions[dimYear] = { label: this.$t('pages.year', { Year: dimYear }), value: dimYear, children: [] }
          if (dimBaseType === 1) {
            this.dateOptions[dimYear].children.push({ label: this.$t('text.' + this.months[parseInt(dimValue.substring(4))]), value: dimValue })
          } else if (dimBaseType === 2) {
            this.dateOptions[dimYear].children.push({ label: this.$t('pages.weekNum', { Week: dimValue.substring(4) }), value: dimValue })
          } else if (dimBaseType === 3) {
            this.dateOptions[dimYear].children.push({ label: this.$t('pages.quarterNum', { Quarter: this.$t('pages.' + this.quarters[parseInt(dimValue.substring(4))]) }), value: dimValue })
          } else if (dimBaseType === 5) {
            const month = dimValue.substring(4, 6)
            const day = dimValue.substring(6, 8).startsWith('0') ? dimValue.substring(7, 8) : dimValue.substring(6, 8)
            const monthChildren = this.dateOptions[dimYear].children
            const index = monthChildren.findIndex(item => item.label === this.$t('text.' + this.months[parseInt(month)]))
            index < 0 && monthChildren.push({ label: this.$t('text.' + this.months[parseInt(month)]), value: dimValue.substring(0, 6), children: [{ label: this.$t('pages.dateNum', { date: day }), value: dimValue }] })
            index >= 0 && monthChildren[index].children.push({ label: this.$t('pages.dateNum', { date: day }), value: dimValue })
          }
        }
      }
      getDimTime(this.query.dimBaseType).then(response => {
        this.dateOptions.splice(0)
        response.data.sort((a, b) => {
          return a > b ? 1 : -1 // 返回正数 ，b排列在a之前
        })
        for (let i = 0; i < response.data.length; i++) {
          let time = response.data[i] + ''
          while (time.length < 6) { time += '0' }
          formatterOption(time)
        }
        ++this.cascaderKey
        const children = this.dateOptions[this.dateOptions.length - 1].children
        if (this.query.dimBaseType === 4) {
          this.query.dimValue = this.dateOptions[this.dateOptions.length - 1].value
        } else if (this.query.dimBaseType === 5) {
          this.query.dimValue = children[children.length - 1].children[0].value
        } else {
          this.query.dimValue = children[children.length - 1] ? children[children.length - 1].value : ''
        }
        value === 'initial' && this.loadReport()
      }).catch(() => {
        if (this.loading) {
          this.loading = false
        }
      })
    },
    dimValueChange(datas) {
      if (datas && datas.length > 0) {
        this.query.dimValue = datas[datas.length - 1]
      } else {
        this.query.dimValue = undefined
      }
    },
    /** 格式化二级表格数据的内容，把名称和id加进去 */
    formatDetailLabelValue() {
      if (this.chartsInfo.oriData) {
        this.chartsInfo.oriData.forEach(item => {
          const subEChartsOption = this.chartsInfo.subEChartsOptions[item.id]
          if (subEChartsOption && subEChartsOption.oriData) {
            subEChartsOption.oriData.forEach(item2 => {
              item2.labelValue = item.labelValue
              item2.label = item.label
            })
          }
        })
      }
    },
    extractObjectIdsAndNames() {
      if (this.chartsInfo.oriData) {
        this.objectIds.splice(0)
        this.objectNames.splice(0)
        this.chartsInfo.oriData.forEach(item => {
          if (item.label !== this.$t('pages.other')) {
            this.objectIds.push(item.labelValue)
            if (this.customizedCondition) {
              this.$emit('customizedObjectNames', item, result => {
                this.objectNames.push(result)
              })
            } else {
              this.objectNames.push(item.labelValue + this.dealUnknown(item.label) + this.dealUnknown(item.groupName))
            }
          }
        })
      }
    },
    /**
     * 搜索按钮点击
     */
    loadReport() {
      this.$emit('search')
      if (this.query.collectSubDept === 1 && !this.query.groupIds) {
        this.$notify({ title: this.$t('text.error'), message: '请至少选择一个部门！', type: 'error', duration: 2000 })
        return
      }
      if (this.query.dimBaseType == 6) {
        // console.log('this.query.startTime', this.query.startTime)
        if (this.query.startTime === '' || this.query.startTime === null) {
          this.$message({ message: this.$t('pages.compareReport22'), type: 'error', duration: 2000 })
          return
        } else if (this.query.endTime === '' || this.query.endTime === null) {
          this.$message({ message: this.$t('pages.compareReport23'), type: 'error', duration: 2000 })
          return
        } else {
          const t1 = new Date(this.query.startTime)
          const t2 = new Date(this.query.endTime)
          if (t2 < t1) {
            this.$message({ message: this.$t('pages.softwareTask_Validate1'), type: 'error', duration: 2000 })
            return
          }
        }
        this.$confirmBox(this.$t('pages.signReportTimeRangeTip'), this.$t('text.prompt')).then(() => {
          this.loadReportSearch()
        }).catch(() => {})
      } else {
        this.loadReportSearch()
      }
      this.visibleAdvanced = false
    },
    /**
     * 高级查询重置
     * */
    resetQuery() {
      this.query.countByObject = this.countByObject || 2
      this.query.recordSize = 5
      if (this.defaultSortProp && this.sortModels.some(item => item.prop === this.defaultSortProp)) {
        this.query.sortName = this.defaultSortProp
      } else if (this.sortModels.length > 0) {
        this.query.sortName = this.sortModels[0].prop
      } else {
        this.query.sortName = ''
      }
      this.query.groupType = 0
      this.query.groupIds = ''
      this.query.collectSubDept = 0
      this.query.objectIds = ''
      this.query.isExplain = false
      this.checkedKeys = []
      this.$emit('getCountByObject', this.query.countByObject)
    },
    /**
     * 搜索查询
     */
    loadReportSearch(ignoreLoading = false) {
      // console.log('this.query.objectType', this.query.objectType)
      if (!this.loading && !ignoreLoading) {
        this.loading = true
      }
      this.showDetail = false
      // 保证selectedColumn的顺序和customList一致
      this.query.selectedColumn = this.customList
        .filter(item => this.selectedFinal.some(selected => selected.prop === item.prop))
        .map(item => ({ label: item.label, prop: item.prop }))
      this.tempQuery = Object.assign({}, this.query)
      if (this.query.recordSize > 20) {
        this.showType = 3
      }
      if (this.query.objectIds == '') {
        this.query.objectType = this.query.countByObject == 1 ? 2 : 1
      }
      getReportChart(this.query, this.api).then(respond => {
        this.loading = false
        this.subColumnValue = undefined
        this.subColumnLabel = undefined
        this.chartsInfo = respond.data
        // console.log(respond)
        if (this.chartsInfo) {
          // console.log(222)
          // 图表解读
          if (this.chartsInfo.explanation) {
            this.showInterpretation = 20
            this.chartInterpretationContent = '<div class="unscramble-title">图表解读：</div>' + this.chartsInfo.explanation
          } else {
            this.showInterpretation = 24
            this.chartInterpretationContent = ''
          }
          this.extractObjectIdsAndNames()
          const subEChartsOpts = this.chartsInfo.subEChartsOptions
          if (subEChartsOpts && JSON.stringify(subEChartsOpts) !== '{}') {
            this.formatDetailLabelValue()
            // 钻取下级
            this.chartsInfo.click = (para) => {
              this.showDetail = false
              this.subColumnValue = para.data.id
              this.subColumnLabel = para.data.duplicateName || para.data.name
              const subChartsOpt = subEChartsOpts[para.data.id]
              // 返回上级
              subChartsOpt.click = (para) => {
                this.showDetail = false
                this.subColumnValue = undefined
                this.subColumnLabel = undefined
                this.changeConfig(this.chartsInfo, this.defaultColModel, this.chartsInfo.oriData)
              }
              // 父组件如需对subColModel进行特殊处理，可监听该事件（目前敏感关键字报表需要特殊处理）
              this.$emit('subColModelProcessing', this.query.countByObject)
              this.subColModel[0].formatter = (row, val) => { return para.name }
              this.changeConfig(subChartsOpt, this.subColModel, subChartsOpt.oriData)
            }
          }
          // console.log(111)
          this.changeConfig(this.chartsInfo, this.defaultColModel, this.chartsInfo.oriData)
        }
      }).catch(() => {
        this.loading = false
      })
    },
    changeConfig(chartsOption, colModel, rowDatas) {
      this.colModel.splice(0, this.colModel.length, ...colModel)
      this.rowDatas.splice(0, this.rowDatas.length, ...rowDatas)
      if (chartsOption.type === 'pie') {
        chartsOption.option.tooltip = {
          formatter: (d) => {
            if (this.customTooltipFormatter) {
              return this.customTooltipFormatter(d)
            }
            // 对数据进行处理后，若有 duplicateName，使用 duplicateName
            const name = d.data.duplicateName || d.data.name
            const key = d.data.id
            for (let i = 0, size = chartsOption.oriData.length; i < size; i++) {
              const data = chartsOption.oriData[i]
              const label = data['id']
              if (label === key) {
                let res = name + '<br/>'
                const tempList = Object.assign([], this.customList)
                if (this.showGroupType) {
                  tempList.splice(0, 0, { prop: 'groupName', label: 'dept', width: '150', sort: true, formatter: this.groupNameFormatter })
                }
                tempList.forEach((item, index) => {
                  // 在排序项后面显示百分比
                  const percent = (item.sortField === this.tempQuery.sortName || item.prop === this.tempQuery.sortName) ? this.$t('pages.percent1', { percent: d.percent }) : ''
                  let value
                  if (item.flag === 'size') {
                    value = data.hasOwnProperty(item.prop) ? convert(data[item.prop]) : convert(data[item.prop.substring(0, item.prop.length - 3)])
                  } else if (item.flag === 'time') {
                    value = data.hasOwnProperty(item.prop) ? formatSeconds(data[item.prop]) : formatSeconds(data[item.prop.substring(0, item.prop.length - 3)])
                  } else if (item.formatter) {
                    return item.formatter(data, data[item.prop])
                  } else {
                    value = data.hasOwnProperty(item.prop) ? data[item.prop] : data[item.prop.substring(0, item.prop.length - 3)]
                  }
                  res += `${this.$t('table.' + item.label)}：` + value + percent + '<br/>'
                })
                return res
              }
            }
            return this.$t('pages.percent2', { key: key, percent: d.percent })
          },
          confine: true,
          extraCssText: 'white-space: break-spaces;'
        }
      } else {
        // chartsOption.option.tooltip = {
        //   formatter: (d) => {
        //     let res = d[0].axisValueLabel + '<br>';
        //     for (var i = 0; i < d.length; i++) {
        //       let value = d[i].data
        //       const col = this.getCol(d[i].seriesName);
        //       if (col != null) {
        //         const data = chartsOption.oriData[d[i].dataIndex]
        //         value = this.valueFormat(data, value, col)
        //       }
        //       res += d[i].seriesName + ' : ' + value + '<br>'
        //     }
        //     return res
        //   },
        //   confine: true,
        //   extraCssText: 'white-space: break-spaces;'
        // }
        if (chartsOption.option.xAxis) {
          chartsOption.option.xAxis[0].axisLabel = {
            formatter: (value) => {
              // 一行显示10个字符，最多显示3行，超过显示...
              var maxLength = 10;
              value = value.length > 30 ? value.substring(0, 27) + '...' : value
              var valLength = value.length;
              var rowN = Math.ceil(valLength / maxLength);
              if (rowN > 1) {
                // 根据行数计算每行的字符数
                const partLength = Math.floor(valLength / rowN);
                const result = [];
                let startIndex = 0;
                // 切割并放入result
                for (let i = 0; i < rowN; i++) {
                  if (i == rowN - 1) {
                    result.push(value.substring(startIndex));
                  } else {
                    result.push(value.substring(startIndex, startIndex + partLength));
                  }
                  startIndex += partLength;
                }
                return result.join('\n')
              } else {
                return value;
              }
            }
          }
        }
      }
      this.formatOption(chartsOption)
      if (this.conversionTime) {
        timeAxis(this.chartsInfo.option)
      }
      if (this.conversionFlow) {
        flowAxis(chartsOption.option)
      }
      this.chartsOption.splice(0, 1, chartsOption)
      // 大小转换
      this.gridTable.colModel.forEach(item => {
        if (item.flag === 'size') {
          item.formatter = (row, data) => {
            return convert(data)
          }
        }
      })
      this.colModel[0].label = this.xAxisName
      this.colModel[0].fixed = true
      // 根据首个colMode类型，来判断用什么详情组件(terminal、user、department)
      this.handleSearchType(this.colModel[0]);
      if (this.showGroupType) {
        const index = this.colModel.findIndex(item => item.prop === 'groupName')
        if (this.query.groupType === 0) {
          index < 0 && this.colModel.splice(1, 0, { prop: 'groupName', label: 'dept', width: '150', sort: true, formatter: this.groupNameFormatter })
        } else {
          index > 0 && this.colModel.splice(index, 1)
        }
      }
    },
    groupNameFormatter(row, data) {
      if (data && data.lastIndexOf('(') >= 0) {
        data = data.substr(0, data.lastIndexOf('('))
      }
      return this.html2Escape(data);
    },
    getCol(seriesName) {
      const colModel = this.customList.find(item => {
        return this.$t('table.' + item.label) == seriesName
      })
      return colModel
    },
    valueFormat(data, value, colModel) {
      if (colModel.flag === 'size') {
        value = convert(value)
      } else if (colModel.flag === 'time') {
        value = formatSeconds(value)
      } else if (colModel.formatter != undefined && colModel.formatter != null) {
        return colModel.formatter(data, value)
      }
      return value
    },
    getCurSelected() {
      const arr = this.colModel.map(item => item.prop)
      this.curCustomList = this.subColumnValue !== undefined
        ? this.customList.map(item => {
          const obj = Object.assign({}, item)
          obj.prop = obj.prop.substring(0, item.prop.length - 3)
          return obj
        })
        : this.customList
      const select = this.curCustomList.filter(ele => {
        return arr.indexOf(ele.prop) !== -1
      })
      return select
    },
    handleColumn() {
      this.selected = this.getCurSelected()
      this.dialogVisible = true
    },
    handleCheckAllChange() {
      this.selected = this.curCustomList
    },
    handleCheckNone() {
      this.selected = []
    },
    visibleChange() {
      const value = this.query.recordSize
      const reg = new RegExp(/^[1-9]\d*$/)
      if (!reg.test(value)) {
        this.query.recordSize = 5
      } else {
        this.query.recordSize = parseInt(value)
      }
    },
    handleChoose() {
      if (this.selected.length > 0) {
        this.subColumnValue !== undefined && this.selected.forEach(item => { item.prop = item.prop + this.propSuffix })
        this.$emit('handleSelect', this.selected)
        this.defaultSort()
        this.loadReport()
        this.dialogVisible = false
      }
    },
    handlePrint() {
      if (!this.chartsInfo) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.compareReport24'),
          type: 'error'
        })
        return
      }
      this.onlyTabel = true
      this.colModel[0].fixed = false
      // 柱状图在打印的时候，防止把柱状对象是否显示的设置重置掉，所以更新一下当前设置
      if (this.showType !== 3) {
        if (this.chartsInfo.type !== 'pie') {
          const op = this.$refs.charts.$refs.bar[0].chart._api.getOption()
          this.chartsOption[0].option.legend.selected = op.legend[0].selected
        }
        this.echartsImg = this.$refs.charts ? (this.chartsInfo.type === 'pie' ? this.$refs.charts.$refs.pie[0].chart._api.getDataURL({
          excludeComponents: ['toolbox']
        }) : this.$refs.charts.$refs.bar[0].chart._api.getDataURL({
          excludeComponents: ['toolbox']
        })) : ''
      }
      setTimeout(() => {
        this.$print(this.$refs.print)
      }, 100)
      setTimeout(() => {
        this.echartsImg = ''
        this.onlyTabel = false
        this.colModel[0].fixed = true
      }, 200)
    },
    handleExport() {
      this.exportNum = this.query.recordSize
      this.dialogVisibleExport = true
    },
    buildExcelData() {
      if (this.exportNum === undefined) {
        this.exportNum = 5
      }
      if (!this.chartsInfo) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.compareReport24'),
          type: 'error'
        })
        return
      }
      let imgBase64Info
      if (this.exportAll === 'top' && this.exportNum < this.query.recordSize) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.exportExceedLimit', { Limit: this.query.recordSize }),
          type: 'error'
        })
        return
      } else if (this.exportAll === 'all') {
        this.exportNum = 0
      }
      if (this.$refs.charts) {
        if (this.chartsInfo.type === 'pie') {
          imgBase64Info = this.$refs.charts.$refs.pie[0].chart._api.getDataURL({
            excludeComponents: ['toolbox'],
            backgroundColor: '#fff'
          })
          this.$refs.charts.$refs.pie[0].chart.resize()
        } else {
          imgBase64Info = this.$refs.charts.$refs.bar[0].chart._api.getDataURL({
            excludeComponents: ['toolbox'],
            backgroundColor: '#fff'
          })
          this.$refs.charts.$refs.bar[0].chart.resize()
        }
      } else {
        imgBase64Info = ''
      }
      const curSelected = this.getCurSelected()
      const showColumn = curSelected.map(item => item.value).reduce(function(prev, curr, idx, arr) {
        return prev + curr
      })
      this.tempQuery.recordSize = this.exportNum
      const sortModel = this.sortModels.find(sortModel => sortModel.prop === this.tempQuery.sortName)
      const sortLabel = sortModel.escapeFormat ? sortModel.label : (this.$te(`table.${sortModel.label}`) ? this.$t(`table.${sortModel.label}`) : sortModel.label)
      return Object.assign(this.tempQuery, {
        imgBase64Info,
        showType: this.showType,
        showColumn: this.api === 'getTrwfeBar' ? undefined : showColumn,
        showLongColumn: this.api === 'getTrwfeBar' ? showColumn : undefined,
        subColumnValue: this.subColumnValue,
        subColumnLabel: this.subColumnLabel,
        sortLabel: sortLabel
      })
    },
    /**
     * 统计类型选中发生变化时
     * */
    handleCount(value) {
      this.checkedKeys.splice(0)
      this.query.objectType = null
      this.query.objectIds = ''
      this.query.groupIds = ''
      // 切换时，清除对统计对象的选中和禁用操作
      this.clickNode = []
      this.getCheckIdsAll(this.deptData, this.clickNode)
      this.$emit('getCountByObject', value)
    },
    handleSearchType(colModel) {
      if (undefined !== colModel.formatter) {
        // 因下钻饼状图时无labelValue数据，暂时就先滤过
        return;
      }
      colModel.type = 'showDetail';
      colModel.searchParam = 'labelValue';
      if (this.$t('table.terminal') === colModel.label) {
        colModel.searchType = 'terminal';
      } else if (this.$t('table.user') === colModel.label || this.$t('table.applicant') === colModel.label) {
        colModel.searchType = 'user';
      } else if (colModel.label !== null && colModel.label.indexOf(this.$t('table.dept')) > -1) {
        colModel.searchType = 'department';
      } else {
        colModel.type = undefined;
        colModel.searchType = undefined;
        colModel.searchParam = undefined;
      }
      if (!this.labelClickable) {
        colModel.type = undefined;
        colModel.searchType = undefined;
        colModel.searchParam = undefined;
      }
    },
    getRowKey(row) {
      return row.id + '_' + moment(row.create_time).unix()
    },
    /**
     * 当值是“未知”时进行特殊处理（因为报表数据库中存储的值是“未知”，所以在查询时需要进行转换）
     * @param value
     * @returns {string|*}
     */
    dealUnknown(value) {
      if (value === (this.$t('text.unknown') + '(-1)')) {
        return this.$t('text.unknown') + '(-1)'
      }

      if (value === this.$t('text.unknown')) {
        return this.$t('text.unknown')
      }

      return value
    },
    /**
     * 查看详情事件
     * @param row 当前行数据
     */
    handleDetail(row) {
      if (this.$refs.detailDialog) {
        this.$refs.detailDialog.visible = true
        this.$refs.detailDialog.rowData = row
        this.$refs.detailDialog.detailCol = this.detailModel
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.table-container{
  display: block;
  height: calc(100vh - 150px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 5px;
}
>>>.el-select{
  width: 100%;
}
.checkbox {
  padding: 0 20px;
  h4{
    margin: 0 0 15px 0;
  }
  .el-checkbox {
    display: block;
    margin: 0 0 20px 0;
    >>>.el-checkbox__label {
      display: inline-flex;
    }
  }
  .width-50-percent {
    width: 50%;
    float: left;
  }
}
.group-type >>>.el-checkbox__inner::after{
  border: 1px solid #000;
  border-left: 0;
  border-top: 0;
}
  .hidden-options{
    display: none;
  }
</style>
