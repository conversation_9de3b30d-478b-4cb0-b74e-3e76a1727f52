import request from '@/utils/request'

export function getReportLevelDesc(id) {
  return request({
    url: '/signReport/reportLevelDesc?id=' + id,
    method: 'get'
  })
}

export function getMetadata(id) {
  return request({
    url: '/signReport/metadata?id=' + id,
    method: 'get'
  })
}

export function getAlarmConfig(id) {
  return request({
    url: '/signReport/alarmConfig?id=' + id,
    method: 'get'
  })
}

export function saveAlarmConfig(id, title, data) {
  return request({
    url: '/signReport/saveAlarmConfig?id=' + id + '&title=' + encodeURIComponent(title),
    method: 'post',
    data
  })
}

