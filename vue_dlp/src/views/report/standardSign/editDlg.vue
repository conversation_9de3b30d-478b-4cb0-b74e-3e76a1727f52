<template>
  <div style="display: inline-block">
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :dialog-status="dialogStatus"
      :visible.sync="dialogVisible"
      width="750px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="180px" style="width: 700px;">
        <el-row>
          <el-col :span="23">
            <FormItem label="报表名称" prop="name" label-width="105px">
              <el-input v-model="temp.name" v-trim maxlength="30"></el-input>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="23">
            <FormItem label="备注" prop="remark" label-width="105px">
              <el-input v-model="temp.remark" maxlength="100"></el-input>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="23">
            <FormItem label="数据来源" prop="source" label-width="105px">
              <el-select v-model="temp.source" style="float: right;">
                <el-option v-for="(item, index) in sourceOption" :key="index" :value="item.value" :label="item.label"/>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="19">
            <FormItem label="窗口大小" prop="window" label-width="105px">
              <el-input v-model="temp.window" maxlength="100"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="4" style="height: 30px;line-height: 30px;margin-left: 10px">
            分钟、天、小时
          </el-col>
        </el-row>
        <el-divider content-position="left">级别与条件</el-divider>

        <el-row>
          <el-col :span="21">
            <FormItem label="" label-width="105px">
              以下列表中为<span style="font-size: 16px;font-weight: bold">严重</span>级别下的条件
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-button size="mini" title="新增严重条件" class="editBtn" @click="addSeverity"><svg-icon icon-class="add" /></el-button>
          </el-col>
        </el-row>
        <!--因为有三个一样的for循环，:key="index"浏览器会报错，所以区分一下:key="'severity'+index"-->
        <el-row v-for="(e, index) in temp.severityList" :key="'severity'+index">
          <el-col :span="11">
            <FormItem label="" prop="severity" label-width="105px">
              <el-select v-model="e.severity" style="float: right;">
                <el-option v-for="item in fileCountOption" :key="item.value" :value="item.value" :label="item.label"/>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label="" prop="severityNum" label-width="5px">
              <el-select v-model="e.severityNum" style="float: right;">
                <el-option v-for="item in sizeComparisonOption" :key="item.value" :value="item.value" :label="item.label"/>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label="" prop="severityInput" label-width="5px">
              <el-input v-model="e.severityInput"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-button size="mini" title="删除严重条件" class="editBtn" @click="deleteSeverity(index)"><svg-icon icon-class="delete" /></el-button>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="21">
            <FormItem label="" label-width="105px">
              以下列表中为<span style="font-size: 16px;font-weight: bold">重要</span>级别下的条件
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-button size="mini" title="新增重要条件" class="editBtn" @click="addImport"><svg-icon icon-class="add" /></el-button>
          </el-col>
        </el-row>
        <el-row v-for="(e, index) in temp.importList" :key="'import'+index">
          <el-col :span="11">
            <FormItem label="" prop="import" label-width="105px">
              <el-select v-model="e.import" style="float: right;">
                <el-option v-for="item in fileCountOption" :key="item.value" :value="item.value" :label="item.label"/>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label="" prop="importNum" label-width="5px">
              <el-select v-model="e.importNum" style="float: right;">
                <el-option v-for="item in sizeComparisonOption" :key="item.value" :value="item.value" :label="item.label"/>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label="" prop="importInput" label-width="5px">
              <el-input v-model="e.importInput"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-button size="mini" title="删除重要条件" class="editBtn" @click="deleteImport(index)"><svg-icon icon-class="delete" /></el-button>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="21">
            <FormItem label="" label-width="105px">
              以下列表中为<span style="font-size: 16px;font-weight: bold">一般</span>级别下的条件
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-button size="mini" title="新增一般条件" class="editBtn" @click="addNormal"><svg-icon icon-class="add" /></el-button>
          </el-col>
        </el-row>
        <el-row v-for="(e, index) in temp.normalList" :key="'normal'+index">
          <el-col :span="11">
            <FormItem label="" prop="normal" label-width="105px">
              <el-select v-model="e.normal" style="float: right;">
                <el-option v-for="item in fileCountOption" :key="item.value" :value="item.value" :label="item.label"/>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label="" prop="normalNum" label-width="5px">
              <el-select v-model="e.normalNum" style="float: right;">
                <el-option v-for="item in sizeComparisonOption" :key="item.value" :value="item.value" :label="item.label"/>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="5">
            <FormItem label="" prop="normalInput" label-width="5px">
              <el-input v-model="e.normalInput"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-button size="mini" title="删除一般条件" class="editBtn" @click="deleteNormal(index)"><svg-icon icon-class="delete" /></el-button>
          </el-col>
        </el-row>
        <el-divider content-position="left">过滤条件</el-divider>
        <el-row>
          <el-col :span="6" class="check-box-label">
            <FormItem label="" prop="filePathIncludesCheck" label-width="20px">
              <el-checkbox v-model="temp.filePathIncludesCheck" maxlength="100">文件路径包含</el-checkbox>
            </FormItem>
          </el-col>
          <el-col :span="17">
            <FormItem label="" prop="filePathIncludesInput" label-width="0">
              <el-input v-model="temp.filePathIncludesInput" maxlength="100"></el-input>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" class="check-box-label">
            <FormItem label="" prop="filePathNotIncludesCheck" label-width="20px">
              <el-checkbox v-model="temp.filePathNotIncludesCheck" maxlength="100">文件路径不包含</el-checkbox>
            </FormItem>
          </el-col>
          <el-col :span="17">
            <FormItem label="" prop="filePathNotIncludesInput" label-width="0">
              <el-input v-model="temp.filePathNotIncludesInput" maxlength="100"></el-input>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" class="check-box-label">
            <FormItem label="" prop="diskIncludesCheck" label-width="20px">
              <el-checkbox v-model="temp.diskIncludesCheck" maxlength="100">磁盘类型名称包含</el-checkbox>
            </FormItem>
          </el-col>
          <el-col :span="17">
            <FormItem label="" prop="diskNotIncludesInput" label-width="0">
              <el-input v-model="temp.diskIncludesInput" maxlength="100"></el-input>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" class="check-box-label">
            <FormItem label="" prop="diskNotIncludesCheck" label-width="20px">
              <el-checkbox v-model="temp.diskNotIncludesCheck" maxlength="100">磁盘类型名称不包含</el-checkbox>
            </FormItem>
          </el-col>
          <el-col :span="17">
            <FormItem label="" prop="diskNotIncludesInput" label-width="0">
              <el-input v-model="temp.diskNotIncludesInput" maxlength="100"></el-input>
            </FormItem>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from '@/directive/el-dragDialog'

export default {
  name: 'EditDlg',
  directives: { elDragDialog },
  components: { },
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    },
    dialogStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      temp: {},
      defaultTemp: {
        // 表单字段
        id: '',
        // 部门名称
        name: '',
        // 备注
        remark: '',
        // 数据来源
        source: '',
        // 窗口大小
        window: '',
        // 级别与条件（严重）
        severityList: [{
          severity: '',
          severityNum: '',
          severityInput: ''
        }],
        // 级别与条件（重要）
        importList: [{
          import: '',
          importNum: '',
          importInput: ''
        }],
        // 级别与条件（一般）
        normalList: [{
          normal: '',
          normalNum: '',
          normalInput: ''
        }],
        // 文件路径包含
        filePathIncludesCheck: false,
        filePathIncludesInput: '',
        // 文件路径不包含
        filePathNotIncludesCheck: false,
        filePathNotIncludesInput: '',
        // 磁盘类型名称包含
        diskIncludesCheck: false,
        diskIncludesInput: '',
        // 磁盘类型名称不包含
        diskNotIncludesCheck: false,
        diskNotIncludesInput: ''
      },
      // 数据来源下拉框
      sourceOption: [
        {
          value: 1,
          label: '数据源1'
        },
        {
          value: 2,
          label: '数据源2'
        }
      ],
      // 严重级别配置下拉框
      fileCountOption: [
        {
          value: 1,
          label: '文件发送次数'
        },
        {
          value: 2,
          label: '文件删除次数'
        },
        {
          value: 3,
          label: '发送数据量'
        }
      ],
      // 严重级别大小比较下拉框
      sizeComparisonOption: [
        {
          value: 1,
          label: '大于'
        },
        {
          value: 2,
          label: '小于'
        },
        {
          value: 3,
          label: '等于'
        }
      ],
      rules: {
        name: [
          { required: true, message: '报表名称不能为空', trigger: 'blur' }
        ]
      },
      formable: false
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.resetTemp()
  },
  methods: {
    show() {
      this.resetTemp()
      this.dialogVisible = true
    },
    /**
     * 新增(确认)
     */
    createData() {
      this.dialogVisible = false
      // this.$refs['dataForm'].validate((valid) => {
      //   const tempData = Object.assign({}, this.temp)
      //   if (valid) {
      //     deviceBindAdd(tempData).then(res => {
      //       // console.log('res', JSON.parse(JSON.stringify(res)))
      //       this.dialogVisible = false
      //       this.submitting = false
      //       this.$parent.getSwitchReport()  // 新增成功，重新获取切换报表下拉框中的值
      //       this.$notify({ title: this.$t('text.success'), message: '新增成功！', type: 'success', duration: 2000 })
      //     }).catch(() => {
      //       this.submitting = false
      //     })
      //   } else {
      //     this.submitting = false
      //   }
      // })
    },
    /**
     * 修改(确认)
     */
    updateData() {
      this.dialogVisible = false
      // this.$refs['dataForm'].validate((valid) => {
      //   const tempData = Object.assign({}, this.temp)
      //   if (valid) {
      //     deviceBindEdit(tempData).then(res => {
      //       // console.log('res', JSON.parse(JSON.stringify(res)))
      //       this.dialogVisible = false
      //       this.submitting = false
      //       this.$parent.getSwitchReport()  // 修改成功，重新获取切换报表下拉框中的值
      //       this.$notify({ title: this.$t('text.success'), message: '修改成功！', type: 'success', duration: 2000 })
      //     }).catch(() => {
      //       this.submitting = false
      //     })
      //   } else {
      //     this.submitting = false
      //   }
      // })
    },
    /**
     * 新增严重级别<前端假新增>
     */
    addSeverity() {
      this.temp.severityList.push({
        severity: '',
        severityNum: '',
        severityInput: ''
      })
    },
    /**
     * 删除严重级别<前端假删除>
     * @param index 当前点击元素索引
     */
    deleteSeverity(index) {
      // console.log('index', index)
      this.$confirmBox('确定删除严重级别条件配置？', '提示').then(() => {
        for (let i = 0; i < this.temp.severityList.length; i++) {
          if (index === i) {
            this.temp.severityList.splice(i, 1)
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          }
        }
      }).catch(res => {})
    },
    /**
     * 新增重要级别<前端假新增>
     */
    addImport() {
      this.temp.importList.push({
        import: '',
        importNum: '',
        importInput: ''
      })
    },
    /**
     * 删除重要级别<前端假删除>
     * @param index 当前点击元素索引
     */
    deleteImport(index) {
      // console.log('index', index)
      this.$confirmBox('确定删除重要级别条件配置？', '提示').then(() => {
        for (let i = 0; i < this.temp.importList.length; i++) {
          if (index === i) {
            this.temp.importList.splice(i, 1)
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          }
        }
      }).catch(res => {})
    },
    /**
     * 新增一般级别<前端假新增>
     */
    addNormal() {
      this.temp.normalList.push({
        normal: '',
        normalNum: '',
        normalInput: ''
      })
    },
    /**
     * 删除一般级别<前端假删除>
     * @param index 当前点击元素索引
     */
    deleteNormal(index) {
      // console.log('index', index)
      this.$confirmBox('确定删除一般级别条件配置？', '提示').then(() => {
        for (let i = 0; i < this.temp.normalList.length; i++) {
          if (index === i) {
            this.temp.normalList.splice(i, 1)
            this.$notify({
              title: '成功',
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
          }
        }
      }).catch(res => {})
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.submitting = false
      this.temp = { ... this.defaultTemp }
    },
    closed() {
    },
    handleDrag() {
    }
  }
}
</script>

<style lang="scss" scoped>
  .check-box-label{
    text-align: right;
    margin-right: 5px;
  }
  >>>.el-checkbox{
    /*margin-right: 19px;*/
    font-weight: 600;
  }
</style>
