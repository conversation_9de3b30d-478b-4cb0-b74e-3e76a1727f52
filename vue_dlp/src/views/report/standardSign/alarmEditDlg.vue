<template>
  <div style="display: inline-block">
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.symptomPushConfig')"
      :visible.sync="dialogVisible"
      width="750px"
    >
      <div v-for="(alarm, index) in alarmList" :key="index">
        <el-row>
          <el-col :span="8">
            {{ $t('pages.dimBaseType') }}:
            <el-select v-model="alarm.dimBaseValue" style="width: 140px;display: inline-block" >
              <el-option
                v-for="(item, sindex) in dateTypeOptions"
                :key="sindex"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="16">
            <ResponseContent
              show-rule-name
              :rule-type-name="$t('pages.pushRule')"
              :select-style="{ 'margin-top': 0 }"
              :show-select="true"
              :show-check-rule="false"
              :editable="true"
              :ignore-rule-types="[2,32,64,128,256]"
              read-only
              :prop-check-rule="true"
              :prop-rule-id="alarm.responseRuleId"
              :filter-data="filterData"
              style="display: inline-block;"
              @getRuleId="id => getRuleId(index, id)"
            >
              <el-button slot="tail" style="height: 30px; padding: 0 7px; margin: 2px 0 0;" @click="removeAlarm(index)"><i class="el-icon-delete"></i></el-button>
            </ResponseContent>
          </el-col>
        </el-row>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="appendAlarm">{{ $t('pages.insertNewRule') }}</el-button>
        <el-button type="primary" @click="handleSaveAlarmConfig">{{ $t('button.save') }}</el-button>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { saveAlarmConfig, getAlarmConfig } from './signReport.js'

export default {
  name: 'AlarmEditDlg',
  components: { ResponseContent },
  props: {
    reportId: {
      type: String,
      default() {
        return ''
      },
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      dateTypeOptions: [
        { value: 5, label: this.$t('pages.dateTypeOptions1') },
        { value: 2, label: this.$t('pages.dateTypeOptions2') },
        { value: 1, label: this.$t('pages.dateTypeOptions3') },
        { value: 3, label: this.$t('pages.dateTypeOptions4') },
        { value: 4, label: this.$t('pages.dateTypeOptions5') }
      ],
      alarmList: [
        {
          dimBaseValue: 5,
          responseRuleId: undefined
        }
      ]
    }
  },
  methods: {
    async show() {
      this.dialogVisible = true
      const resp = await getAlarmConfig(this.reportId)
      if (resp.data && resp.data.alarmConfigs) {
        this.alarmList = resp.data.alarmConfigs
      } else {
        // 如果不初始化赋值，弹框会显示空
        this.alarmList = [
          {
            dimBaseValue: 5,
            responseRuleId: undefined
          }
        ]
      }
    },
    /**
     * 过滤响应规则，只保留有配置控制台告警和邮件告警的响应规则
     * @param data
     * @returns {[]}
     */
    filterData(data) {
      // console.log('data', JSON.parse(JSON.stringify(data)))
      const reportArray = []
      if (data.length > 0) {
        data.forEach(item => {
          const isConsoleAlarm = 4 & item.alarmLimit
          const isEmailAlarm = 8 & item.alarmLimit
          if (parseInt(isConsoleAlarm) > 0 || parseInt(isEmailAlarm) > 0) {
            reportArray.push(item)
          }
        })
      }
      return reportArray
    },
    appendAlarm() {
      this.alarmList.push({
        dimBaseValue: 5,
        responseRuleId: undefined
      })
    },
    removeAlarm(index) {
      this.alarmList.splice(index, 1)
    },
    getRuleId(index, id) {
      this.alarmList[index].responseRuleId = id
    },
    /**
     * 判断对象数组中是否有重复对象
     * @param arr 对象数组
     * @returns {*}  如果返回false，则代表存在重复
     */
    hasDuplicates(arr) {
      const jsonStrings = arr.map(obj => JSON.stringify(obj));
      return jsonStrings.every((json, index) =>
        jsonStrings.indexOf(json) === index
      )
    },
    async handleSaveAlarmConfig() {
      for (const i of this.alarmList) {
        if (!i.responseRuleId) {
          this.$message({
            message: this.$t('pages.fillCompletely'),
            type: 'error'
          })
          return;
        }
      }
      if (this.hasDuplicates(this.alarmList) === false) {
        this.$message({
          message: this.$t('pages.pushMsg'),
          type: 'error'
        })
        return;
      }
      const title = this.$route.meta.title
      await saveAlarmConfig(this.reportId, title, {
        alarmConfigs: this.alarmList
      })
      this.$message({
        message: this.$t('text.success'),
        type: 'success',
        duration: 2000
      })
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.select-con .el-select {
    width: 300px;
  }
</style>
