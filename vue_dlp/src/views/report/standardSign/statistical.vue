<template>
  <div v-loading="loading" class="app-container" element-loading-text="报表生成中，请稍后！" element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)">
    <Statistical
      ref="stat"
      :api="api"
      :default-col-model="defaultColModel"
      :detail-show-pager="false"
      :detail-extra-data="detailExtraData"
      :type-options="countTypeOptions"
      :custom-list="computedCustomList"
      :count-by-object="countByObject"
      :search-date-time="searchDateTime"
      :drill-down="hasPermission('875')"
      :exportable="hasPermission('876')"
      @search="refreshLevelDesc"
      @handleSelect="handleCustomColumn"
    >
      <template v-slot:button>
        <el-button type="primary" size="mini" @click="$refs.alarmEditDlg.show()">{{ $t('pages.symptomPushConfig') }}</el-button>
        <el-tooltip class="item" effect="dark" placement="bottom">
          <div slot="content">
            <span class="tooltip-span">{{ $t('pages.signReportLevelDesc') }}</span>
            <div v-if="levelDesc.normal.length > 1">
              <div class="tooltip-title">{{ $t('pages.severityOptions2') }}:</div>
              <div v-for="(item, index) in levelDesc.normal" :key="index" class="tooltip-content">
                {{ item }}
              </div>
            </div>
            <div v-if="levelDesc.importance.length > 1">
              <div class="tooltip-title">{{ $t('pages.important') }}:</div>
              <div v-for="(item, index) in levelDesc.importance" :key="index" class="tooltip-content">
                {{ item }}
              </div>
            </div>
            <div v-if="levelDesc.severity.length > 1">
              <div class="tooltip-title">{{ $t('pages.severityOptions3') }}:</div>
              <div v-for="(item, index) in levelDesc.severity" :key="index" class="tooltip-content">
                {{ item }}
              </div>
            </div>
          </div>
          <i class="el-icon-info"></i>
        </el-tooltip>
      </template>
    </Statistical>
    <edit-dlg ref="editDlg" :title="dialogStatusTitle" :dialog-status="dialogStatus"/>
    <alarmEditDlg ref="alarmEditDlg" :report-id="detailExtraData.reportId" />
  </div>
</template>

<script>
import Statistical from '../component/Statistical'
import alarmEditDlg from './alarmEditDlg.vue'
import editDlg from '@/views/report/standardSign/editDlg'
import { getReportLevelDesc, getMetadata } from './signReport.js'
// const arr = location.href.split('-');
// const id = arr[arr.length - 1]
export default {
  name: 'StandardSymptomReport',
  components: { Statistical, editDlg, alarmEditDlg },
  data() {
    return {
      // loading:控制报表生成中显示隐藏，后期需要通过接口返回控制
      loading: false,
      detailExtraData: {
        reportId: ''
      },
      levelDesc: {
        normal: [],
        importance: [],
        severity: []
      },
      api: '',
      onlyUser: false,
      // 报表下拉框
      reportId: '',
      dialogStatus: '',
      dialogStatusTitle: '',
      // 报表日期查询显示
      searchDateTime: true,
      // 所有数据列（页面列表设置，弹框中的数据为所有列表选项）
      customList: [
        { prop: 'normal', value: 1, label: 'general', width: '190', sort: true, detailTable: 'special_dwd_sign_report_0', detailCol: this.getDetailCol() },
        { prop: 'importance', value: 2, label: 'important', width: '190', sort: true, detailTable: 'special_dwd_sign_report_1', detailCol: this.getDetailCol() },
        { prop: 'seriousness', value: 4, label: 'serious', width: '150', sort: true, detailTable: 'special_dwd_sign_report_2', detailCol: this.getDetailCol() },
        { prop: 'total', value: 8, label: 'total', width: '150', sort: true, detailTable: 'special_dwd_sign_report_all', detailCol: this.getDetailCol() }
      ],
      // 默认展示的数据列({ prop: 'label', label: 'user', width: '150', sort: true },去掉会报错，应该是组件里有限制)
      defaultColModel: [
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'normal', label: 'general', width: '190', sort: true, detailTable: 'special_dwd_sign_report_0', detailCol: this.getDetailCol(), hidden: () => this.levelDesc.normal.length <= 1 },
        { prop: 'importance', label: 'important', width: '190', sort: true, detailTable: 'special_dwd_sign_report_1', detailCol: this.getDetailCol(), hidden: () => this.levelDesc.importance.length <= 1 },
        { prop: 'seriousness', label: 'serious', width: '150', sort: true, detailTable: 'special_dwd_sign_report_2', detailCol: this.getDetailCol(), hidden: () => this.levelDesc.severity.length <= 1 },
        { prop: 'total', label: 'total', width: '150', sort: true, detailTable: 'special_dwd_sign_report_all', detailCol: this.getDetailCol() }
      ],
      // 查询条件排序项中的值
      curSelected: []
    }
  },
  computed: {
    computedCustomList() {
      const customList = []
      if (this.levelDesc.normal.length > 1) {
        customList.push({ prop: 'normal', value: 1, label: 'general', width: '190', sort: true, detailTable: 'special_dwd_sign_report_0', detailCol: this.getDetailCol() })
      }
      if (this.levelDesc.importance.length > 1) {
        customList.push({ prop: 'importance', value: 2, label: 'important', width: '190', sort: true, detailTable: 'special_dwd_sign_report_1', detailCol: this.getDetailCol() })
      }
      if (this.levelDesc.severity.length > 1) {
        customList.push({ prop: 'seriousness', value: 4, label: 'serious', width: '150', sort: true, detailTable: 'special_dwd_sign_report_2', detailCol: this.getDetailCol() })
      }
      customList.push({ prop: 'total', value: 8, label: 'total', width: '150', sort: true, detailTable: 'special_dwd_sign_report_all', detailCol: this.getDetailCol() })
      return customList
    },
    countTypeOptions() {
      if (this.onlyUser) {
        return [{ value: 1, label: this.$t('pages.conutType1') }]
      } else {
        return [{ value: 1, label: this.$t('pages.conutType1') }, { value: 2, label: this.$t('pages.conutType2') }]
      }
    },
    countByObject() {
      if (this.onlyUser) {
        return 1
      }
      return 2
    }
  },
  watch: {
    onlyUser() {
      if (this.onlyUser) {
        this.$refs.stat.query.countByObject = 1
      }
    }
  },
  created() {
    const reportId = this.$route.path.split('-')[1]
    const title = this.$route.meta.title // 用于记录管理员日志，功能名称
    this.api = 'getSignReportSummary?id=' + reportId + '&title=' + encodeURIComponent(title)
    getMetadata(reportId).then(resp => {
      // 审批日志只支持按操作员统计
      if (resp.data.logType == 32) {
        this.onlyUser = true
      }
      setTimeout(() => {
        this.$refs.stat.loadReportSearch(true)
      }, 1000)
    })
    this.detailExtraData.reportId = reportId

    this.refreshLevelDesc()
  },
  methods: {
    refreshLevelDesc() {
      const reg = /\{\{.*?\}\}/g
      const replaceFunc = v => v.replace(reg, $1 => this.$t($1.replace('{{', '').replace('}}', '')))
      getReportLevelDesc(this.detailExtraData.reportId).then(resp => {
        // 一般
        const normal = resp.data[0].split('\n')
        const normalArr = normal.filter(i => i && i.trim()).map(replaceFunc)
        // 重要
        const importance = resp.data[1].split('\n')
        const importanceArr = importance.filter(i => i && i.trim()).map(replaceFunc)
        // 严重
        const severity = resp.data[2].split('\n')
        const severityArr = severity.filter(i => i && i.trim()).map(replaceFunc)
        this.levelDesc.normal = normalArr
        this.levelDesc.importance = importanceArr
        this.levelDesc.severity = severityArr
      })
    },
    signLelveFormatter(row) {
      if (row.sign_level == 0) {
        return this.$t('pages.severityOptions2')
      }
      if (row.sign_level == 1) {
        return this.$t('pages.important')
      }
      if (row.sign_level == 2) {
        return this.$t('pages.severityOptions3')
      }
    },
    getDetailCol() {
      return [
        { prop: 'start_time', label: 'startTime', width: '150', sort: true },
        { prop: 'end_time', label: 'endTime', width: '150', sort: true },
        { prop: 'terminal_name', label: 'terminal', width: '150', type: 'showDetail', searchParam: 'term_id', searchType: 'terminal', hidden: () => this.onlyUser },
        { prop: 'term_group_name', label: 'terminalGroup', width: '150', type: 'showDetail', searchParam: 'term_group_id', searchType: 'department', hidden: () => this.onlyUser },
        { prop: 'user_name', label: 'user', width: '150', type: 'showDetail', searchParam: 'user_id', searchType: 'user' },
        { prop: 'user_group_name', label: 'userGroup', width: '150', type: 'showDetail', searchParam: 'user_group_id', searchType: 'department' },
        { prop: 'sign_level', label: 'signLevel', width: '150', formatter: this.signLelveFormatter }
      ]
    },
    /**
     * 排序项目显示内容（排序项显示的是defaultColModel中有的字段）
     * @param val
     * @param groupType
     */
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.curSelected = customList
      this.defaultColModel = [this.defaultColModel[0], ...customList]
    }
  }
}
</script>
<style>
  .toolbar>span{
    line-height: 30px
  }
  .tooltip-span{
    color: #2d8cf0;
    font-size: 15px;
    font-weight: bold;
    line-height: 35px;
  }
  .tooltip-title{
    color: #2d8cf0;
    font-size: 14px;
    font-weight: bold;
    line-height: 35px;
  }
  .tooltip-content{
    font-size: 12px;
    line-height: 20px;
  }
</style>
