<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('report.dataIncrementMsg11')"
      :visible.sync="dialogVisible"
      width="850px"
    >
      <grid-table
        ref="deptTable"
        :stripe="false"
        row-key="id"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :indent="0"
        style="height:350px"
        :multi-select="false"
        default-expand-all
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getIncrementDetail } from '@/api/report/baseReport/dataIncrement';
export default {
  name: 'TotalSizeDialog',
  props: {

  },
  data() {
    return {
      dialogVisible: false,
      query: { // 查询条件
        page: 1,
        limit: 20,
        groupType: 1
      },
      colModel: [
        { prop: 'businessCode', label: 'databaseCollectionTableName', width: '110' },
        { prop: 'beforeStartTotal', label: 'startTimeLogNumber', width: '110' },
        { prop: 'endTotal', label: 'endTimeLogNumber', width: '110' },
        { prop: 'increment', label: 'growth', width: '60' },
        { prop: 'growthRate', label: 'changeRate', width: '60' },
        { prop: 'srcSystem', label: 'srcSystem', width: '175' }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['deptTable']
    }
  },
  methods: {
    /**
     * 列表数据获取
     * @param option
     * @returns {AxiosPromise}
     */
    rowDataApi(option) {
      this.query.page = 1
      const searchQuery = Object.assign({}, this.query, this.$parent.query, option)
      searchQuery.searchCount = true
      return getIncrementDetail(searchQuery)
    },
    /**
       * 显示弹框
       */
    show() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.query.page = 1
        this.gridTable.execRowDataApi(this.query);
      })
    }
  }
}
</script>

