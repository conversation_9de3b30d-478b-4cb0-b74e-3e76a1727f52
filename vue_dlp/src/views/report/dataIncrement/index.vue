<template>
  <div class="app-container sensitiveness-report">
    <div class="table-container">
      <!-- 顶部 -->
      <div class="sensitiveness-query" style="margin-bottom: 5px;">
        <div style="width: 410px; float: left;padding-left: 10px;line-height: 28px;color: #68a8d0;">
          <i class="el-icon-pie-chart"></i>
          日志增量总数：<span style="cursor: pointer;text-decoration: underline;" @click="handleTotalSize">{{ temp.totalSize }}</span>
        </div>
        <div style="float: right">
          <span>
            {{ $t('pages.timeQuery') }}：
            <el-date-picker
              ref="start"
              v-model="query.startDate"
              :clearable="false"
              value-format="yyyy-MM-dd"
              type="date"
              style="width: 140px"
              :picker-options="pickerStartOptions"
              :placeholder="$t('pages.startDate')"
              @change="changeStart"
            />
            <span>~</span>
            <el-date-picker
              ref="end"
              v-model="query.endDate"
              :clearable="false"
              value-format="yyyy-MM-dd"
              type="date"
              style="width: 140px"
              :picker-options="pickerEndOptions"
              :placeholder="$t('pages.endDate')"
              @change="changeEnd"
            />
          </span>
          <!--图表解读-->
          <el-checkbox v-model="query.isExplain" label="查看图表解读" class="group-type"></el-checkbox>
          <el-button type="primary" size="mini" style="margin-left:10px" class="no-print" @click="searchClick">{{ $t('text.search') }}</el-button>
        </div>
      </div>
      <!--暂无数据-->
      <div v-if="noData" class="detailsDiv">
        <div class="noData">{{ this.$t('report.dataIncrementMsg1') }}...</div>
      </div>
      <div v-if="!noData" class="detailsDiv">
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <!--日志数量变化趋势-->
            <div class="mini-title">
              <span>{{ $t('report.dataIncrementMsg5') }}</span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content"> 查询时间段，日志总数变化趋势图</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
            <line-chart
              :chart-data="temp.logQuantityData"
              :chart-option="temp.logQuantityOption"
              class="flex-2"
            />
            <div v-if="query.isExplain" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + '我是解读内容' + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <!--日志增量对比统计-->
            <div class="mini-title">
              <span>审计日志增量TOP10</span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">
                  1、显示某个(终端/操作员)某天产生的所有审计日志数，每个柱子代表一个业务表增加的数据量<br>
                  2、可通过右侧“查询按钮”更改查询条件，查看对应统计数据</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <!--查询条件组件-->
              <query-bottom ref="queryBottom" @query-search="handleQuerySearch"/>
            </div>
            <bar-chart
              :chart-data="temp.incrementalContrastData"
              :chart-option="temp.incrementalContrastOption"
              class="flex-2"
            />
            <div v-if="query.isExplain" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + '我是解读内容' + '</div>'"></div>
            </div>
          </div>
        </div>
        <div class="chart-panel">
          <div class="chart-item flex-1">
            <!--增量TOP10采集表-->
            <div class="mini-title">
              <span>{{ $t('report.dataIncrementMsg3') }}</span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">
                  1、显示增量最多的10张采集表数据统计<br>
                  2、柱子点击时，右侧"采集表趋势图"显示当前点击表在查询时间段内的变化趋势<!--{{ $t('report.dataIncrementMsg4') }}--></div>
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
            <bar-chart
              :chart-data="temp.incrementTop10Data"
              :chart-option="temp.incrementTop10Option"
              class="flex-2"
              :click="loadTableTrend"
            />
            <div v-if="query.isExplain" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + '我是解读内容' + '</div>'"></div>
            </div>
          </div>
          <div class="chart-item flex-1">
            <!--采集表趋势图-->
            <div class="mini-title">
              <span>采集表趋势图</span>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">
                  1、默认展示左侧“增量TOP10采集表中的第一条数据”对应的趋势图<br>
                  2、折线点击时，显示对应TOP5终端/操作员信息</div>
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
            <line-chart
              :chart-data="temp.tableQuantityData"
              :chart-option="temp.tableQuantityOption"
              class="flex-2"
              :click="loadUserBar"
            />
            <div v-if="query.isExplain" class="panel-wrapper unscramble flex-1">
              <div class="unscramble-title" v-html="commonInterpretation"></div>
              <div class="unscramble-body" v-html="'<div style=\'text-indent: 2em\'>' + '我是解读内容' + '</div>'"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--top--终端--用户--弹框-->
    <top5-user-term ref="top5UserTerm"/>
    <!--日志增量总大小--弹框-->
    <total-size-dialog ref="totalSizeDialog"/>
  </div>
</template>
<script>
import BarChart from '@/components/ECharts/BarChart';
import LineChart from '@/components/ECharts/LineChart';
import TotalSizeDialog from '@/views/report/dataIncrement/totalSizeDialog';
import top5UserTerm from '@/views/report/dataIncrement/top5UserTerm.vue';
import { getDataIncrement, getIncrementTrendByTable, getTableByUserAndDate } from '@/api/report/baseReport/dataIncrement';
import moment from 'moment';
import QueryBottom from '@/views/report/dataIncrement/queryBottom.vue';
export default {
  name: 'DataIncrement',
  components: { top5UserTerm, BarChart, LineChart, TotalSizeDialog, QueryBottom },
  props: {

  },
  data() {
    return {
      // 图表解读
      commonInterpretation: `<span>图表解读：</span>`,
      // 暂无数据
      noData: false,
      // 查询条件（右上角）
      query: {
        // 是否展示图表解读
        isExplain: true,
        startDate: '',
        endDate: ''
      },
      // 限制时间选择不能超过当天
      pickerStartOptions: {
        disabledDate: time => {
          const endTime = this.query.endDate;
          if (endTime) {
            return time.getTime() > new Date(endTime).getTime() || time.getTime() > Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天;
          } else {
            return time.getTime() > Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天;
          }
        }
      },
      pickerEndOptions: {
        disabledDate: time => {
          const beginTime = this.query.startDate;
          if (beginTime) {
            return time.getTime() < new Date(beginTime).getTime() || time.getTime() > Date.now();
          } else {
            return time.getTime() > Date.now();
          }
        }
      },
      temp: {},
      defaultTemp: {
        // 日志增量总数
        totalSize: 0,
        // 增量TOP10采集表 柱状图
        incrementTop10Data: [],
        incrementTop10Option: {
          title: {
            text: ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: '采集表名',
            data: [],
            axisLabel: {
              rotate: 10
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        },
        // 日志数量变化趋势(折线图)
        logQuantityData: [],
        logQuantityOption: {
          title: {
            text: ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            data: []
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        },
        // 采集表趋势图(折线图)
        tableQuantityData: [],
        tableQuantityOption: {
          title: {
            text: ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            data: []
          },
          series: [
            {
              data: [],
              type: 'line'
            }
          ]
        },
        // 获取审计日志增量TOP10 柱状图
        incrementalContrastData: [],
        incrementalContrastOption: {
          title: {
            text: ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: '采集表名',
            data: [],
            axisLabel: {
              rotate: 10
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  watch: {

  },
  activated() {

  },
  created() {
    this.query.endDate = moment(new Date()).format('YYYY-MM-DD')
    this.query.startDate = moment(new Date()).add(-7, 'd').format('YYYY-MM-DD')
    this.resetTemp()
    this.getPageData()
  },
  mounted() {
    this.resetAuditLogData()
  },
  methods: {
    /**
     * 日志增量总大小弹框
     */
    handleTotalSize() {
      this.$refs.totalSizeDialog.show()
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取页面数据
     */
    getPageData() {
      this.resetTemp()
      // this.query.endDate = '2025-05-20'
      // this.query.startDate = '2025-05-13'
      getDataIncrement(this.query).then(res => {
        const response = res.data
        // console.log('res增量报表', JSON.parse(JSON.stringify(response)))
        if (response.totalNum === 0) {
          this.noData = true
        } else {
          this.noData = false
        }
        // 日志增量总大小统计
        this.temp.totalSize = response.totalNum
        // 增量TOP10采集表(柱状图)
        this.temp.incrementTop10Option.xAxis.data = response.incrementBar.xaxisData
        this.temp.incrementTop10Option.series[0].data = response.incrementBar.seriesData

        // 日志数量变化趋势(折线图)
        this.temp.logQuantityOption.xAxis.data = response.trendChart.xaxisData
        this.temp.logQuantityOption.series[0].data = response.trendChart.seriesData

        // 某采集表日志数量七日变化趋势(折线图)
        this.temp.tableQuantityOption.title.text = response.incrementBar.xaxisData[0]
        this.temp.tableQuantityOption.xAxis.data = response.tableTrendLine.xaxisData
        this.temp.tableQuantityOption.series[0].data = response.tableTrendLine.seriesData
      }).catch(() => {})
    },
    /**
     * 搜索按钮点击时
     */
    searchClick() {
      this.getPageData()
    },
    /**
     * 获取审计日志增量TOP10（页面加载时，调用）
     * 初始化时，设置默认参数，终端，统计对象为树的第一条数据，时间为当天
     */
    resetAuditLogData() {
      setTimeout(() => {
        const query = this.$refs['queryBottom'].query
        const queryBottom = this.$refs['queryBottom'].queryBottom
        const treeList = this.$refs['queryBottom'].treeList
        const obj = {
          detailDate: '',
          objectId: null,
          objectType: 1
        }
        obj.detailDate = query.countDate.replace(/-/g, '')
        obj.objectId = Number(treeList[0].dataId)
        obj.objectType = queryBottom.objectType
        // console.log('构造的obj', obj)
        this.getAuditLogData(obj)
      }, 1500);
    },
    /**
     * 获取审计日志增量TOP10（搜索按钮点击时，调用）
     * @param data 子组件传递过来的数据
     */
    handleQuerySearch(data) {
      // console.log('接收到子组件的数据:', JSON.parse(JSON.stringify(data)));
      this.getAuditLogData(data)
    },
    /**
     * 获取审计日志增量TOP10--获取
     * @param data 接口参数
     */
    getAuditLogData(data) {
      getTableByUserAndDate(data).then(res => {
        // console.log('res', res)
        const response = res.data
        // 审计日志增量TOP10(柱状图)
        this.temp.incrementalContrastOption.xAxis.data = response.xaxisData
        this.temp.incrementalContrastOption.series[0].data = response.seriesData
      }).catch(() => {})
    },
    // 时间删除的时候选中当前组件选中值
    changeStart(val) {
      if (!val) {
        this.query.startDate = this.$refs.start.value; // 如果输入值为空，则重置为当前选中的日期
      }
    },
    changeEnd(val) {
      if (!val) {
        this.query.endDate = this.$refs.end.value; // 如果输入值为空，则重置为当前选中的日期
      }
    },
    /**
     * 获取采集表趋势图数据 (增量TOP10采集表--柱子点击时)
     * @param params
     */
    loadTableTrend(params) {
      const query = { ...this.query, businessCode: params.name }
      getIncrementTrendByTable(query).then(res => {
        this.temp.tableQuantityOption.title.text = query.businessCode
        this.temp.tableQuantityOption.xAxis.data = res.data.xaxisData
        this.temp.tableQuantityOption.series[0].data = res.data.seriesData
      })
    },
    /**
     * 获取某张表某天数据增量Top5终端/操作员(采集表趋势图--点击时获取)
     * @param params
     */
    loadUserBar(params) {
      const query = { ...this.query, businessCode: this.temp.tableQuantityOption.title.text, detailDate: params.name }
      // console.log('queryTOP5', JSON.parse(JSON.stringify(query)))
      this.$refs.top5UserTerm.show(query)
    }
  }
}
</script>

<style lang="scss" scoped>
  .sensitiveness-report{
    .chart-item{
      height: 360px;
    }
  }
</style>
