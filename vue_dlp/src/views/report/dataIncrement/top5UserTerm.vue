<!--top5终端/操作员-->
<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
        <el-tab-pane :label="$t('table.terminal')" name="term">
          <bar-chart
            v-if="isTerm"
            :chart-data="temp.termUserIncrementTop5Data"
            :chart-option="temp.termUserIncrementTop5Option"
            class="flex-2"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('table.user')" name="user">
          <bar-chart
            v-if="isUser"
            :chart-data="temp.termUserIncrementTop5Data"
            :chart-option="temp.termUserIncrementTop5Option"
            class="flex-2"
          />
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BarChart from '@/components/ECharts/BarChart';
import { getIncrementUserByTable } from '@/api/report/baseReport/dataIncrement';
export default {
  name: 'Top5UserTerm',
  components: { BarChart },
  props: {

  },
  data() {
    return {
      // 接口查询条件
      queryObj: {},
      dialogVisible: false,
      isTerm: true,
      isUser: false,
      activeName: 'term',
      userDialogTitle: '',
      title: '',
      temp: {},
      defaultTemp: {
        // TOP5终端、操作员 柱状图
        termUserIncrementTop5Data: [],
        termUserIncrementTop5Option: {
          title: {
            text: ''
          },
          toolbox: {
            show: false
          },
          xAxis: {
            name: this.$t('table.terminal'),
            data: [],
            axisLabel: {
              rotate: 10
            }
          },
          series: [
            {
              data: [],
              type: 'bar'
            }
          ]
        }
      }
    }
  },
  computed: {

  },
  methods: {
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * tab点击时，切换终端/操作员，获取对应接口数据
     * @param pane
     * @param event
     */
    tabClick(pane, event) {
      if (this.activeName === 'term') {
        this.isTerm = true
        this.isUser = false
        this.queryObj.objectType = 1
        this.getUserTermDate(this.queryObj)
      } else {
        this.isTerm = false
        this.isUser = true
        this.queryObj.objectType = 2
        this.getUserTermDate(this.queryObj)
      }
    },
    /**
     * 显示弹框（弹框打开时，默认显示终端数据）
     */
    show(query) {
      this.dialogVisible = true
      this.activeName = 'term'
      this.title = query.businessCode + query.detailDate + '数据增量Top5'
      // 查询参数添加objectType区分终端(1)、操作员(2)
      query.objectType = 1
      this.queryObj = query
      this.getUserTermDate(this.queryObj)
    },
    /**
     * 获取终端/操作员(接口返回数据获取)
     * @param query
     */
    getUserTermDate(query) {
      this.resetTemp()
      getIncrementUserByTable(query).then(res => {
        // console.log('res', JSON.parse(JSON.stringify(res.data)))
        if (query.objectType === 1) {
          this.temp.termUserIncrementTop5Option.xAxis.name = this.$t('table.terminal')
        } else {
          this.temp.termUserIncrementTop5Option.xAxis.name = this.$t('table.user')
        }
        this.temp.termUserIncrementTop5Option.xAxis.data = res.data.xaxisData
        this.temp.termUserIncrementTop5Option.series[0].data = res.data.seriesData
      }).catch(() => {})
    }
  }
}
</script>

