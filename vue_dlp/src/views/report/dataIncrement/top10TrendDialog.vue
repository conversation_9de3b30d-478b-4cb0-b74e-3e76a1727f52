<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <line-chart
        ref="chart"
        :chart-data="lineChartData"
        :chart-option="lineOption"
        style="width: 100%;height: 245px"
      />

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart';
import { getDataCollectTrend } from '@/api/report/baseReport/dataTansaction'
export default {
  name: 'Top10TrendDialog',
  components: { LineChart },
  props: {

  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      // 日志数量变化趋势(折线图)
      lineChartData: [],
      lineOption: {
        title: {
          text: ''
        },
        toolbox: {
          show: false
        },
        xAxis: {
          name: this.$t('pages.time'),
          type: 'category',
          data: []
        },
        yAxis: {
          name: this.$t('components.number'),
          type: 'value',
          // 控制分隔线
          splitLine: {
            show: true,
            lineStyle: {
              width: 0.5,
              color: '#bce5ff'
            }
          },
          // 控制隔区域
          splitArea: {
            show: false
          }
        },
        series: [
          {
            data: [],
            type: 'line'
          }
        ]
      }
    }
  },
  computed: {

  },
  methods: {
    resetTemp() {

    },
    /**
       * 显示弹框
       */
    show(name, query) {
      this.resetTemp()
      // console.log('query', JSON.parse(JSON.stringify(query)))
      this.dialogVisible = true
      this.title = name + this.$t('report.dataIncrementMsg10')
      const tempQuery = Object.assign({}, query)
      tempQuery.businessCode = name
      this.dialogVisible = true
      this.lineOption.title.text = name + this.$t('pages.dataChange')
      getDataCollectTrend(tempQuery).then(res => {
        this.lineOption.xAxis.data = res.data.trend.xaxisData
        this.lineOption.series[0].data = res.data.trend.seriesData
        this.lineOption.tooltip = {
          formatter: (d) => {
            // console.log('d', d)
            const time = d[0].axisValue + '<br>';
            const data = this.$t('table.logNumSumAll') + '：' + d[0].data;
            return time + data
          }
        }
        this.$refs['chart'].__resizeHandler()
      })
    }
  }
}
</script>

