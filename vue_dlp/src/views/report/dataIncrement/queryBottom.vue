<!--审计日志增量TOP10，查询条件-->
<template>
  <el-popover
    :append-to-body="false"
    placement="bottom"
    width="350"
    trigger="click"
    @show="handlePopoverShow"
  >
    <Form ref="searchLeftForm" label-position="right" label-width="65px">
      <FormItem :label="$t('pages.countByObject')">
        <el-select v-model="query.countByObject" is-filter :placeholder="$t('text.select')" @change="handleCount">
          <el-option
            v-for="(item, index) in typeOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </FormItem>
      <FormItem :label="$t('pages.countObject')">
        <tree-select
          ref="objectTree"
          node-key="id"
          class="targetObject"
          clearable
          check-strictly
          :click-node="clickNode"
          :local-search="false"
          :checked-keys="[checkedKeys]"
          is-filter
          style="width: 102%; margin-left: -5px;display: inline-block"
          :data="query.countByObject == 2 ? reportTermTree : reportUserTree"
          :get-search-list="getSearchListFunction"
          @deptTreeData="deptTreeData"
          @change="(key, nodeData) => entityIdChange(key, nodeData, queryBottom)"
        />
      </FormItem>
      <FormItem label="统计日期">
        <el-date-picker
          ref="countDate"
          v-model="query.countDate"
          value-format="yyyy-MM-dd"
          :clearable="false"
          type="date"
          style="width: 260px"
          :picker-options="pickerStartOptions"
          :placeholder="$t('pages.endDate')"
          @change="changeDate"
        >
        </el-date-picker>
      </FormItem>
    </Form>
    <div style="text-align: right; margin-top: 10px">
      <!--重置、查询按钮-->
      <el-button size="mini" @click="resetBottomQuery">{{ $t('button.reset') }}</el-button>
      <el-button type="primary" size="mini" style="color: #0d485a" @click="searchClick()">{{ $t('table.search') }}</el-button>
    </div>
    <el-tooltip class="item" effect="dark" placement="right">
      <div slot="content"> {{ $t('report.dataIncrementMsg4') }}</div>
      <i slot="reference" class="el-icon-info" />
    </el-tooltip>
    <el-button slot="reference" type="primary" size="mini" icon="el-icon-search" circle></el-button>
  </el-popover>
</template>

<script>

import { mapGetters } from 'vuex';
import moment from 'moment/moment';

export default {
  name: 'QueryBottom',
  components: {},
  props: {

  },
  data() {
    return {
      // 限制时间选择不能超过当天
      pickerStartOptions: {
        disabledDate: time => {
          const endTime = this.query.endDate;
          if (endTime) {
            return time.getTime() > new Date(endTime).getTime() || time.getTime() > Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天;
          } else {
            return time.getTime() > Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天;
          }
        }
      },
      // 查询条件（左下角）
      query: {
        // 统计类型 1：操作员，2：终端
        countByObject: 2,
        countDate: '',
        objectId: null
      },
      queryBottom: {
        objectType: 1,
        // 查询对象，查询对象支持同时查询部门和终端
        objectId: null,
        // 部门ID
        // groupIds: '',
        // 统计日期
        detailDate: ''
      },
      checkedKeys: '',
      // 存放部门树当前选中数据
      clickNode: [],
      // 统计对象终端树和操作员树（报表要过滤需要自定义一个字段进行过滤，否则会影响到别的页面的数据）
      reportTermTree: [],
      reportUserTree: [],
      // 统计对象相关配置  1：操作员，2：终端
      typeOptions: [
        { value: 1, label: this.$t('pages.conutType1') },
        { value: 2, label: this.$t('pages.conutType2') }
      ],
      // 获取树数据（初始化接口调用需要）
      treeList: []
    }
  },
  computed: {
    // 缓存好的终端树和操作员树
    ...mapGetters([
      'termTree',
      'termTreeList',
      'userTree',
      'userTreeList'
    ])
  },
  watch: {
    // 终端树发生变化时，监听
    termTree() {
      const tree = JSON.parse(JSON.stringify(this.termTree))
      this.filterTermTree(tree)
      this.reportTermTree = tree
    },
    // 操作员树变化时，监听
    userTree() {
      this.reportUserTree = JSON.parse(JSON.stringify(this.userTree))
    }
  },
  created() {
    this.resetBottomQuery()
    this.query.countDate = moment(new Date()).format('YYYY-MM-DD')
    // 初始化统计对象树赋值
    if (this.termTree !== undefined) {
      const tree = JSON.parse(JSON.stringify(this.termTree))
      this.filterTermTree(tree)
      this.reportTermTree = tree
    }
    if (this.userTree !== undefined) {
      this.reportUserTree = JSON.parse(JSON.stringify(this.userTree))
    }
  },
  mounted() {

  },
  methods: {
    /**
     * 重置按钮点击时
     */
    resetBottomQuery() {
      this.query.objectId = null
      this.query.countByObject = 2
      this.query.countDate = moment(new Date()).format('YYYY-MM-DD')
      this.checkedKeys = ''
      this.queryBottom.objectId = null
      this.queryBottom.detailDate = ''
    },
    /**
     * 获取终端树，页面初始化时，需要获取树的第一个节点，用户默认参数的传递
     * @param data
     */
    deptTreeData(data) {
      this.treeList = data
      // console.log('获取树数据', JSON.parse(JSON.stringify(data)))
    },
    /**
     * 弹框显示时，初始化选中值（统计对象）设置
     */
    handlePopoverShow() {
      // 初始化树组件选中值（默认选中第一个终端数据）
      // console.log('this.reportTermTree打开', JSON.parse(JSON.stringify(this.reportTermTree)))
      if (this.query.countByObject === 2) {
        if (this.reportTermTree.length > 0 && this.queryBottom.objectId === null) {
          this.checkedKeys = this.reportTermTree[0].id
          this.query.objectId = Number(this.reportTermTree[0].dataId)
        }
      } else {
        if (this.reportUserTree.length > 0 && this.queryBottom.objectId === null) {
          this.checkedKeys = this.reportUserTree[0].id
          this.query.objectId = Number(this.reportUserTree[0].dataId)
        }
      }
    },
    /**
     * 搜索按钮点击时，将参数传给父组件
     */
    searchClick() {
      // 1、日期需要去掉中间分隔符'-'
      this.queryBottom.detailDate = this.query.countDate.replace(/-/g, '')
      // 2、如果第一次打开，统计对象有设置默认值，则需要赋值
      if (this.query.objectId !== null) {
        this.queryBottom.objectId = this.query.objectId
      }
      // console.log('子组件发送', JSON.parse(JSON.stringify(this.queryBottom)))
      this.$emit('query-search', this.queryBottom);
    },
    /**
     * 统计对象树节点点击时，获取对应参数
     * @param val
     */
    changeDate(val) {
      if (!val) {
        this.query.countDate = this.$refs.countDate.value; // 如果输入值为空，则重置为当前选中的日期
      }
    },
    /**
     * 统计对象选中发生时，获取对应参数
     * */
    entityIdChange(key, nodeData, queryBottom) {
      // console.log('nodeData点击', JSON.parse(JSON.stringify(nodeData)))
      this.queryBottom.objectType = this.query.countByObject == 1 ? 2 : 1
      if (!Array.isArray(nodeData)) {
        this.queryBottom.objectId = Number(nodeData.dataId)
        this.query.objectId = Number(nodeData.dataId)
      } else {
        this.queryBottom.objectId = null
      }
    },
    /**
     * 终端树过滤（老板终端、U判断终端、永久离线终端）
     * */
    filterTermTree(termTree) {
      for (let i = termTree.length - 1; i >= 0; i--) {
        const item = termTree[i]
        if (item.dataType === 'G') {
          this.filterTermTree(item.children || [])
          continue
        }
        const type = (parseInt(item.dataType) & 0xf0) >> 4
        if (type === 1 || type === 2 || type === 8) {
          termTree.splice(i, 1)
        }
      }
    },
    /**
     * 统计类型选中发生变化时
     * */
    handleCount(value) {
      this.checkedKeys = ''
      this.queryBottom.objectType = this.query.countByObject == 1 ? 2 : 1
      this.queryBottom.objectId = null
      this.query.objectId = null
      // this.queryBottom.groupIds = ''
      // 切换时，清除对统计对象的选中和禁用操作
      this.clickNode = []
    },
    /**
     * 获取搜索列表(统计对象)
     * @returns {[]}
     */
    getSearchListFunction() {
      if (this.query.countByObject === 2) {
        // 终端
        return this.termTreeList
      } else {
        // 操作员
        return this.userTreeList
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-bottom{
  line-height: 45px;
  display: flex;
  align-items: center; /* 确保子元素垂直居中对齐 */
  .labelName {
    /* 设置固定的宽度 */
    width: 80px; /* 根据需要调整宽度 */
    flex-shrink: 0; /* 防止收缩 */
    text-align: right; /* 如果需要右对齐标签名称 */
    margin-right: 10px; /* 可选：给两个 span 之间增加一点间距 */
    font-size: 14px;
  }
  .labelForm {
    flex-grow: 1; /* 让 .labelForm 占据剩余的所有空间 */
    flex-basis: 0; /* 从零开始增长以占据剩余空间 */
  }
  .el-select,.el-date-editor,.el-button{
    width: 100%;
  }
}
</style>
