<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      title="配置"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form style="width: 550px" label-width="90px" label-position="right">
        <el-divider content-position="left">工作日配置</el-divider>
        <el-row style="height: 30px;">
          <el-col :span="19">
            快捷设置班次：班次打卡：08：30-17：30
          </el-col>
          <el-col :span="4" class="span-click">
            <span @click="handleChange()">更改班次</span>
          </el-col>
        </el-row>
        <grid-table
          ref="terminalNotOfflineList"
          :col-model="colModel"
          :row-datas="rowData"
          :height="250"
          :show-pager="false"
        />
        <el-row style="height: 30px;line-height: 30px;">
          <el-checkbox v-model="temp.holidayChecked">法定节假日自动排休</el-checkbox>
        </el-row>
        <el-divider content-position="left">加班配置</el-divider>
        <el-row style="height: 30px;">
          <el-col :span="14">
            <FormItem label="工作日加班" prop="IPv6Len" label-width="155px">
              <el-time-select
                v-model="temp.workStart"
                :picker-options="{
                  start: '06:30',
                  step: '00:15',
                  end: '22:30'
                }"
                placeholder="选择时间"
              >
              </el-time-select>
            </FormItem>
          </el-col>
          <el-col :span="1">-</el-col>
          <el-col :span="8">
            <el-time-select
              v-model="temp.workEnd"
              :picker-options="{
                start: '06:30',
                step: '00:15',
                end: '22:30'
              }"
              placeholder="选择时间"
            >
            </el-time-select>
          </el-col>
        </el-row>
        <el-row style="height: 30px;">
          <el-col :span="14">
            <FormItem label="非工作日加班" prop="IPv6Len" label-width="155px">
              <el-time-select
                v-model="temp.noWorkStart"
                :picker-options="{
                  start: '06:30',
                  step: '00:15',
                  end: '22:30'
                }"
                placeholder="选择时间"
              >
              </el-time-select>
            </FormItem>
          </el-col>
          <el-col :span="1">-</el-col>
          <el-col :span="8">
            <el-time-select
              v-model="temp.noWorkEnd"
              :picker-options="{
                start: '06:30',
                step: '00:15',
                end: '22:30'
              }"
              placeholder="选择时间"
            >
            </el-time-select>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="submitData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      title="更改班次"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogVisibleChange"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form style="width: 350px" label-width="90px" label-position="right">
        <el-row style="height: 30px;">
          <el-col :span="11">
            <el-time-select
              v-model="startTime"
              :picker-options="{
                start: '06:30',
                step: '00:15',
                end: '22:30'
              }"
              placeholder="选择时间"
            >
            </el-time-select>
          </el-col>
          <el-col :span="1">-</el-col>
          <el-col :span="11">
            <el-time-select
              v-model="endTime"
              :picker-options="{
                start: '06:30',
                step: '00:15',
                end: '22:30'
              }"
              placeholder="选择时间"
            >
            </el-time-select>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="submitDataChange()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogVisibleChange = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  name: 'Config',
  props: {

  },
  data() {
    return {
      dialogVisible: false, // 配置
      dialogVisibleChange: false, // 更改班次
      submitting: false,
      colModel: [
        { prop: 'work', label: '工作日' },
        { prop: 'remark', label: '班次时间段' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: '更改班次', click: this.handleChange }
          ]
        }
      ],
      rowData: [
        { 'id': 1, 'work': '周一', 'remark': '班次 打卡:8:30-17:30' },
        { 'id': 2, 'work': '周二', 'remark': '班次 打卡:8:30-17:30' },
        { 'id': 3, 'work': '周三', 'remark': '班次 打卡:8:30-17:30' },
        { 'id': 4, 'work': '周四', 'remark': '班次 打卡:8:30-17:30' },
        { 'id': 5, 'work': '周五', 'remark': '班次 打卡:8:30-17:30' },
        { 'id': 6, 'work': '周六', 'remark': '休息' },
        { 'id': 7, 'work': '周日', 'remark': '休息' }
      ],
      temp: {},
      defaultTemp: {
        holidayChecked: true, // 法定节假日自动排休
        workStart: '19:00', // 工作日加班
        workEnd: '20:00',
        noWorkStart: '9:00', // 非工作日加班
        noWorkEnd: '18:00'
      },
      startTime: '8:30',
      endTime: '18:30'
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    resetTemp() {
      this.temp = { ...this.defaultTemp }
    },
    /**
     * 更改班次
     * */
    handleChange() {
      this.dialogVisibleChange = true
    },
    /**
     * 父组件调用，显示配置弹框
     */
    show() {
      this.dialogVisible = true
    },
    /**
     * 确认按钮点击，配置
     */
    submitData() {
      this.dialogVisible = false
    },
    /**
     * 确认按钮点击，更改班次
     */
    submitDataChange() {
      this.dialogVisibleChange = false
    },
    handleDrag() {

    }
  }
}
</script>
<style lang="scss" scoped>
.span-click{
  text-align: center;
  color: #68a8d0;
  cursor: pointer
}
  .input-width{
    width: 150px;
  }
</style>

