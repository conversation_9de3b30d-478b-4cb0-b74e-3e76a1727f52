<template>
  <div class="app-container">
    <div class="table-container">
      <div>
        <div style="width: 510px; float: left;">
          <!--上方面包屑，面包屑控制下方内容的显示隐藏-->
          <el-breadcrumb ref="fileBreadcrumb" separator="/">
            <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
              <a href="javascript:void(0);" :title="item.label" @click="breadcrumbClick(index, showFilePath, true)">{{ item.label }}</a>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div style="float: right">
          <el-tabs style="width: 280px; border: 0;">
            <el-select
              v-model="query.recentDays"
              filterable
              clearable
              collapse-tags
              type="text"
              style="height: 35px; width: 200px; margin: 0;"
              placeholder="请选择"
            >
              <el-option v-for="item in searchSelect" :key="item.id" :value="item.id" :label="item.label"></el-option>
            </el-select>
            <el-button type="primary" icon="el-icon-search" size="mini" style="margin-bottom: 5px" @click="loadPanel">
              {{ $t('table.search') }}
            </el-button>
          </el-tabs>
        </div>
      </div>
      <!--员工加班分析-->
      <div v-if="employeeOvertimeDiv" class="detailsDiv">
        <!--上方几个小方块-->
        <div class="data-panel">
          <div v-for="(item, key) in itemOption" :key="key" class="data-item" >
            <label :title="item.label">{{ item.label }}</label>
            <span>
              <div
                v-for="(btn, i) in item.value"
                :key="i"
              >{{ itemValue[btn.key] }}</div>
            </span>
          </div>
        </div>
        <!--下方对员工，部门，加班员工数量趋势的分别展示部分-->
        <div class="data-analysis-panel">
          <div class="data-analysis">
            <div class="mini-title"><span @click="userTitleClick">员工加班时长（共{{ userNumber }}人）>></span></div>
            <div class="table-parent-size">
              <div class="table-container table-left-size">
                <grid-table
                  ref="userOverTimeList"
                  :col-model="colModel"
                  :row-datas="rowData"
                  style="height: calc(100% - 40px);"
                  :show-pager="false"
                />
              </div>
            </div>
          </div>
          <div class="data-analysis">
            <div class="mini-title"><span @click="deptTitleClick">部门分析>></span></div>
            <div style="height: calc(100% - 40px)">
              <bar-chart
                :chart-data="barChartData"
                :chart-option="barChartOption"
                :x-axis-name="xAxisName"
                style="width: 100%;height: 100%"
              />
            </div>
          </div>
          <div class="data-analysis">
            <div class="mini-title"><span>加班员工数量趋势</span></div>
            <div style="height: calc(100% - 40px)">
              <line-chart
                :chart-data="lineChartData"
                :chart-option="lineOption"
                style="width: 100%;height: 100%"
              />
            </div>
          </div>
        </div>
      </div>
      <!--员工加班时长分析-->
      <div v-if="employeeHoursDiv" class="detailsDiv">
        <user-analysis ref="userReport" :recent-days="query.recentDays"/>
      </div>
      <!--部门分析-->
      <div v-if="employeeDeptDiv" class="detailsDiv">
        <dept-analysis ref="deptReport" :recent-days="query.recentDays"/>
      </div>
      <!--加班员工数量趋势-->
      <div v-if="employeeNumDiv" class="detailsDiv">
        <on-off-trend/>
      </div>
    </div>
    <!--配置-->
    <config-dlg ref="configDlg"/>
    <!--小方块点击弹框-->
    <square-dlg ref="squareDlg" :title="deviceTitle" :dialog-status="dialogStatus"/>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import BarChart from '@/components/ECharts/BarChart'
import configDlg from '@/views/report/overtimeAnalysis/employeeOvertime/config'
import squareDlg from '@/views/report/overtimeAnalysis/employeeOvertime/squareDlg';
import userAnalysis from '@/views/report/overtimeAnalysis/employeeOvertime/userAnalysis';
import deptAnalysis from '@/views/report/overtimeAnalysis/employeeOvertime/deptAnalysis';
import onOffTrend from '@/views/report/overtimeAnalysis/employeeOvertime/onOffTrend';
import { getOverTimePanel } from '@/api/report/baseReport/panelReport/panel';
import { formatSeconds } from '@/utils'

export default {
  name: 'EmployeeOvertime',
  components: { BarChart, LineChart, configDlg, squareDlg, userAnalysis, deptAnalysis, onOffTrend },
  props: {
    xAxisName: {
      type: String,
      default() {
        return '部门'
      }
    }
  },
  data() {
    return {
      userNumber: 0,
      colModel: [
        { prop: 'objectName', label: '姓名' },
        { prop: 'overTimeSumAll', label: '加班时长', formatter: (row, data) => { return formatSeconds(data) } }
      ],
      treeData: [],
      currentNodeKey: '',
      defaultExpandedKeys: [''],
      rowData: [],
      query: { // 查询条件
        page: 1,
        limit: 100,
        sortName: 'id',
        searchInfo: '',
        recentDays: 30
      },
      showFilePath: [ // 面包屑显示数组
        { id: 0, label: '加班分析' }
      ],
      employeeOvertimeDiv: true, // 员工加班分析显示隐藏
      employeeHoursDiv: false, // 员工加班时长显示隐藏
      employeeDeptDiv: false, // 部门分析显示隐藏
      employeeNumDiv: false, // 加班员工数量趋势显示隐藏
      searchSelect: [
        { id: 7, label: '近7天' },
        { id: 30, label: '近30天' },
        { id: 90, label: '近90天' }
      ],
      deviceTitle: '', // 弹框名称
      dialogStatus: '', // 弹框状态
      // 加班员工数量趋势分析--上方小方块
      itemOption: [
        { label: '加班总人数', value: [{ key: 'allDays', func: this.allPeopleFun }] },
        { label: '人均加班时长', value: [{ key: 'avgTime', func: this.peopleTimeFun }] },
        { label: '非工作加班人数', value: [{ key: 'noWorkDays', func: this.numFun }] }
      ],
      itemValue: {
        allDays: '', // 加班总认数
        avgTime: '', // 人均加班时长
        noWorkDays: '' // 非工作加班人数
      },
      // 柱状图数据
      barChartData: [],
      barChartOption: {
        xAxis: [{
          type: 'category',
          data: [],
          axisLabel: {
            width: 50,
            overflow: 'truncate',
            ellipsis: '...'
          }
        }],
        yAxis: [
          {
            show: false
          }
        ],
        tooltip: {
          show: true,
          formatter: (d) => {
            let res = ''
            for (let i = 0; i < d.length; i++) {
              res += this.html2Escape(d[i].axisValueLabel) + ' : ' + formatSeconds(d[i].value) + '<br>'
            }
            return res
          }
        },
        series: [
          {
            data: [],
            barMaxWidth: '8%',
            type: 'bar',
            label: {
              show: true,
              position: 'top',
              formatter(d) {
                return formatSeconds(d.data)
              }
            }
          }
        ]
      },
      // 折线图数据
      lineChartData: [],
      lineOption: {
        xAxis: [{
          type: 'category',
          data: []
        }],
        yAxis: {
          type: 'value'
        },
        legend: {
          top: '5%',
          selected: {
            '加班天数': true,
            '非工作加班天数': true
          },
          data: ['加班天数', '非工作加班天数']
        },
        dataZoom: [
          {
            textStyle: {
              color: '#027AB6'
            },
            type: 'inside',
            start: 0,
            end: 30
          },
          {
            type: 'slider',
            start: 0,
            end: 30
          }
        ],
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line',
            name: '加班天数'
          },
          {
            data: [200, 300, 100, 321, 156, 362, 231],
            type: 'line',
            name: '非工作加班天数'
          }
        ]
      },
      temp: {},
      defaultTemp: { // 表单字段

      },
      submitable: false,
      showTree: true,
      dialogFormVisible: false,
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['userOverTimeList']
    }
  },
  created() {
    this.loadPanel()
  },
  activated() {},
  methods: {
    loadPanel() {
      getOverTimePanel(this.query).then(respond => {
        const panelData = respond.data
        if (panelData && panelData.companyData) {
          this.itemValue.allDays = panelData.companyData.overTimeDaysSumAll + '人天'
          this.itemValue.avgTime = formatSeconds(panelData.companyData.overTimeAvgSumAll)
          this.itemValue.noWorkDays = panelData.companyData.overTimeNoWorkDaysSumAll + '人天'
        }
        if (panelData && panelData.userDatas) {
          this.rowData = panelData.userDatas
        }
        if (panelData && panelData.userNumber) {
          this.userNumber = panelData.userNumber
        }
        if (panelData && panelData.departmentOption) {
          this.barChartOption.series[0].data = panelData.departmentOption.datas
          this.barChartOption.xAxis[0].data = panelData.departmentOption.xaxisData
        }
        if (panelData && panelData.trendMap) {
          this.lineOption.series[0].data = panelData.trendMap.work.datas
          this.lineOption.series[1].data = panelData.trendMap.noWork.datas
          this.lineOption.xAxis[0].data = panelData.trendMap.work.xaxisData
        }
      })
    },
    reloadTable() {
      this.tableLoading = true
      const searchQuery = Object.assign({}, this.query)
      return searchQuery
    },
    /**
     * 左侧树点击
     * @param data
     * @param node
     * @param el
     */
    handleNodeClick(data, node, el) {
      console.log('点击', data)
    },
    /**
     * 加班总人数(点击)
     * deviceTitle：弹框打开时标题
     * dialogStatus：区分点击的哪个小方块，共用一个弹框表格，控制某一列的显示隐藏
     * show(1)：传的1，2，3可以理解为到时候掉接口的一个参数，区分类型，如果后期不用可去掉
     */
    allPeopleFun(e) {
      // console.log('e', e)
      this.deviceTitle = '加班总人数'
      this.dialogStatus = 'allPeople'
      this.$refs.squareDlg.show(1)
    },
    /**
     * 人均加班时长
     */
    peopleTimeFun(e) {
      // console.log('e', e)
      this.deviceTitle = '人均加班时长'
      this.dialogStatus = 'people'
      this.$refs.squareDlg.show(2)
    },
    /**
     * 非工作加班人数
     */
    numFun(e) {
      // console.log('e', e)
      this.deviceTitle = '非工作加班人数'
      this.dialogStatus = 'num'
      this.$refs.squareDlg.show(3)
    },
    /**
     * 柱状图点击，列表弹框
     * */
    barChartFun(params) {
      console.log('params', params)
      if (params.name) {
        this.deviceTitle = '加班部门数量'
        this.dialogStatus = 'dept'
        this.$refs.squareDlg.show(4)
      }
    },
    /**
     * 员工分析点击
     * 显示员工分析，面包屑中添加部门分析
     * */
    userTitleClick() {
      this.employeeOvertimeDiv = false
      this.employeeHoursDiv = true
      this.showFilePath.push({ id: 1, label: '员工加班时长' })
      this.$nextTick(() => {
        this.$refs.userReport.loadReport(this.query.recentDays)
      })
    },
    /**
     * 部门分析点击
     * 显示部门分析，面包屑中添加部门分析
     * */
    deptTitleClick() {
      this.employeeOvertimeDiv = false
      this.employeeDeptDiv = true
      this.showFilePath.push({ id: 2, label: '部门分析' })
    },
    /**
     * 加班员工数量趋势点击
     * 显示加班员工数量趋势，面包屑中添加部门分析
     * */
    notOfflineTitleClick() {
      this.employeeOvertimeDiv = false
      this.employeeNumDiv = true
      this.showFilePath.push({ id: 3, label: '加班员工数量趋势' })
    },
    /**
     * 面包屑点击方法（控制页面内容显示隐藏）
     * @param index
     * @param filePath
     * @param breadcrumb
     */
    breadcrumbClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      if (index === 0) {
        this.employeeOvertimeDiv = true
        this.employeeHoursDiv = false
        this.employeeDeptDiv = false
        this.employeeNumDiv = false
        this.showFilePath = [{ id: 0, label: '加班分析' }]
      }
      if (index === 1) {
        this.employeeOvertimeDiv = false
        this.employeeHoursDiv = true
        this.employeeDeptDiv = false
        this.employeeNumDiv = false
      }
      if (index === 2) {
        this.employeeOvertimeDiv = false
        this.employeeHoursDiv = false
        this.employeeDeptDiv = true
        this.employeeNumDiv = false
      }
      if (index === 3) {
        this.employeeOvertimeDiv = false
        this.employeeHoursDiv = false
        this.employeeDeptDiv = false
        this.employeeNumDiv = true
      }
    },
    /**
     * 配置
     */
    btnConfig() {
      this.$refs.configDlg.show()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    }
  }
}
</script>

<style lang='scss' scoped>
>>>.el-breadcrumb{
  line-height: 40px;
}
.detailsDiv{
  height: 100%;
  border: 1px solid #666666;
}
>>>.el-breadcrumb__inner a{
  color: #68a8d0 ;
}
/*小方格样式*/
.data-panel{
  width: 100%;
  padding: 20px;
  display: flex;
}
.data-item{
  flex: 1;
  height: 100px;
  line-height: 100px;
  border: 1px solid #3b749c;
  display: inline-block;
  margin-left: 1%;
  font-size: 16px;
  display: flex;
  &:first-child{
    margin-left: 0;
  }
  label{
    line-height: 15px;
    align-self: center;
    font-weight: normal;
    flex: 2;
    text-align: center;
    font-size: 12px;
  }
  span{
    flex: 1;
    text-align: center;
    font-size: 12px;
  }
}
@media screen and (max-width: 1500px){
  .data-item{
    flex-direction: column;
    line-height: 50px;
  }
}
.data-analysis-panel{
  width: 100%;
  height: calc(100% - 140px);
  padding:0 20px 20px 20px;
  display: flex;
}
.data-analysis{
  flex: 1;
  height: 100%;
}
.mini-title{
  height: 30px;
  line-height: 30px;
  padding-left: 20px;
  margin-bottom: 10px;
  span{
    font-weight: bold;
    color: #68a8d0;
    cursor: pointer;
  }
}
.data-details{
  width: 100%;
  height: 100%;
  padding:0 20px 20px 20px;
  display: flex;
}
.data-analysis-table{
  flex: 1;
}
.data-analysis-table >>>.el-table{
  top: 20px;
}
.data-analysis-details{
  flex: 2;
}
/*解决表格宽度随浏览器大小变化自适应问题*/
.table-parent-size{
  position: relative;
  width: 100%;
  height: 100%;
}
.table-left-size{
  width: 100%;
  height: calc(100% - 35px);
  position: absolute;
}
>>>.el-pagination{
  overflow-x: auto;
  overflow-y: hidden;
}
</style>
