<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      append-to-body
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="squareList"
        :stripe="false"
        row-key="id"
        :col-model="colModel"
        :row-datas="rowData"
        :indent="0"
        :height="325"
        :multi-select="false"
        default-expand-all
        :show-pager="true"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'SquareDlg',
  components: { },
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    },
    dialogStatus: { // 状态，小方块共用一个弹框，若某弹框有特定的列，需要根据状态控制列的显示隐藏
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      rowData: [],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['deviceList']
    },
    colModel() {
      return [
        { prop: 'name', label: '姓名' },
        { prop: 'dept', label: '部门' },
        { prop: 'noDay', label: '加班总人数', hidden: () => this.colHidden(['num', 'people']) },
        { prop: 'money', label: '人均加班时长', hidden: () => this.colHidden(['num', 'allPeople']) },
        { prop: 'time', label: '非工作日加班人数', hidden: () => this.colHidden(['allPeople', 'people']) }
      ]
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    /**
       * 显示弹框
       */
    show(type) {
      // console.log('type', type)
      this.resetTemp()
      this.dialogVisible = true
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.submitting = false
    },
    colHidden(type) {
      return type.includes(this.dialogStatus)
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

