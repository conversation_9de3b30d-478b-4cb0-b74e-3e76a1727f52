<template>
  <!--员工加班时长-->
  <div class="data-details">
    <div class="data-analysis-table table-parent-size" style="padding-top: 20px;">
      <div class="table-container table-left-size">
        <grid-table
          ref="gridTable"
          :col-model="colModel"
          :row-data-api="rowDataApi"
          :show-pager="true"
          @cell-click="cellClick"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--用户分析上方几个小方块-->

      <div class="data-panel">
        <div class="data-item" >
          <label :title="itemValue.userName">{{ itemValue.userName }}</label>
        </div>
        <div v-for="(item, key) in userItemOption" :key="key" class="data-item" >
          <label :title="item.label">{{ item.label }}</label>
          <span>
            <el-button
              v-for="(btn, i) in item.value"
              :key="i"
              type="text"
              @click="btn.func"
            >{{ itemValue[btn.key] }}</el-button>
          </span>
        </div>
      </div>
      <!--用户分析中时间-->
      <div style="height: calc(100% - 140px);padding: 0 20px">
        <el-tabs v-model="activeNotOfflineName" @tab-click="tabClick">
          <el-tab-pane label="加班日历分析" name="notCalendar">
            <div style="padding: 10px 20px;float: right;position: relative"><span style="display: inline-block;width: 10px;height: 10px;border-radius:50%;background: #33ff00;position: absolute;top: 15px;right: 90px;border: 1px solid #fff;"></span>加班工作</div>
            <div style="padding: 10px 20px;float: right;position: relative"><span style="display: inline-block;width: 10px;height: 10px;border-radius:50%;background: #ff0000;position: absolute;top: 15px;right: 105px;border: 1px solid #fff;"></span>加班未工作</div>
            <div v-if="isShowCalendar" class="data-analysis-panel">
              <div class="data-analysis">
                <el-calendar v-model="calendarValue1">
                  <!--选中小红点-->
                  <template
                    slot="dateCell"
                    slot-scope="{date, data}"
                  >
                    <div>
                      <div v-for="(item, key) in activeday" :key="key">
                        <el-badge v-if="data.day == item.dat && item.type == 1" is-dot class="successColor"></el-badge>
                        <el-badge v-if="data.day == item.dat && item.type == 2" is-dot class="errorColor"></el-badge>
                      </div>
                      <div class="spandate">{{ data.day.split('-').slice(2).join('-') }}</div>
                    </div>
                  </template>
                </el-calendar>
              </div>
              <div class="data-analysis">
                <el-calendar v-model="calendarValue2">
                  <!--选中小红点-->
                  <template
                    slot="dateCell"
                    slot-scope="{date, data}"
                  >
                    <div>
                      <div v-for="(item, key) in activeday" :key="key">
                        <el-badge v-if="data.day == item.dat && item.type == 1" is-dot class="successColor"></el-badge>
                        <el-badge v-if="data.day == item.dat && item.type == 2" is-dot class="errorColor"></el-badge>
                      </div>
                      <div class="spandate">{{ data.day.split('-').slice(2).join('-') }}</div>
                    </div>
                  </template>
                </el-calendar>
              </div>
            </div>
          </el-tab-pane>
          <!--          <el-tab-pane label="加班分析" name="notAnalysis">
            <div v-if="isShowNotAnalysis" class="data-analysis-panel">
              <div class="data-analysis">
                <pie-chart
                  :chart-data="chartsWorkDatas"
                  :chart-option="chartWorkOption"
                />
              </div>
            </div>
          </el-tab-pane>-->
        </el-tabs>
      </div>
    </div>
    <!--小方块点击弹框-->
    <square-user-dlg ref="squareDlg" :title="deviceTitle" :dialog-status="dialogStatus"/>
  </div>
</template>

<script>
import squareUserDlg from '@/views/report/overtimeAnalysis/employeeOvertime/squareUserDlg';
// import PieChart from '@/components/ECharts/PieChart'
import { getOverTimeUserPage } from '@/api/report/baseReport/panelReport/panel';
import { formatSeconds } from '@/utils'

export default {
  name: 'UserAnalysis',
  components: { squareUserDlg },
  props: {
    recentDays: { type: Number, default: 30 }
  },
  data() {
    return {
      query: {
        page: 1,
        limit: 20,
        sortName: 'id',
        searchInfo: '',
        recentDays: this.recentDays
      },
      colModel: [
        { prop: 'objectName', label: '姓名' },
        { prop: 'overTimeDaysSumAll', label: '加班总天数', sort: true }
      ],
      rowData: [],
      workTitle: '', // 弹框名称
      deviceTitle: '', // 弹框名称
      dialogStatus: '', // 弹框状态
      // 员工分析
      userName: '',
      userItemOption: [
        { label: '加班总天数', value: [{ key: 'day', func: this.dayFun }] },
        { label: '加班总时长', value: [{ key: 'time', func: this.timeFun }] },
        { label: '非工作加班天数', value: [{ key: 'noWork', func: this.noWorkFun }] }
      ],
      itemValue: {
        userName: '', //
        day: '', // 加班天数
        time: '', // 加班总活动时长
        noWork: '' // 加班总活动时长
      },
      activeNotOfflineName: 'notCalendar',
      isShowCalendar: true,
      isShowNotAnalysis: false,
      // 日历
      calendarValue1: new Date(),
      calendarValue2: new Date(),
      activeday: [ // 日历组件选中的日期，小红点，时间格式必须为yyyy-MM-dd，比如2月5号要写成02-05而不是2-5
      ],
      // 饼图 加班分析
      chartsWorkDatas: [
        { value: 100, name: '工作' },
        { value: 85, name: '浏览新闻' },
        { value: 15, name: '待机' },
        { value: 25, name: '聊天' },
        { value: 35, name: '追剧' },
        { value: 15, name: '网购' }
      ],
      chartWorkOption: {
        title: {
          text: '加班分析',
          left: 'center'
        }
      }
    }
  },
  computed: {

  },
  created() {

  },
  activated() {},
  methods: {
    isSameMonth(dt1, dt2) {
      return (dt1.getFullYear() == dt2.getFullYear() && dt1.getMonth() == dt2.getMonth())
    },
    setCalendarValue(type, dayStr) {
      const day = {
        dat: dayStr,
        type: type
      }
      this.activeday.push(day)
      // 为日期组件初始化一个默认展示月份
      const theDate = new Date(dayStr.replace(/-/g, '/'))
      if (this.calendarValue1 != null && this.calendarValue2 == null && !this.isSameMonth(theDate, this.calendarValue1)) {
        this.calendarValue2 = theDate
      }
      if (this.calendarValue1 == null) {
        this.calendarValue1 = theDate
      }
    },
    cellClick(row, column, cell, event) {
      this.itemValue.userName = row.objectName
      this.itemValue.day = row.overTimeDaysSumAll + '天'
      this.itemValue.time = formatSeconds(row.overTimeSumAll)
      this.itemValue.noWork = row.overTimeNoWorkDaysSumAll + '天'
      this.activeday.splice(0)
      this.calendarValue1 = null
      this.calendarValue2 = null
      if (row.dayList) {
        row.dayList.forEach(item => {
          this.setCalendarValue(1, item)
        })
      }
      if (row.noWorkDayList) {
        row.noWorkDayList.forEach(item => {
          this.setCalendarValue(2, item)
        })
      }
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getOverTimeUserPage(searchQuery)
    },
    loadReport() {
      this.query.recentDays = this.recentDays
      // this.$refs.gridTable.execRowDataApi(this.query)
    },
    /**
     * 加班总活动时长
     * deviceTitle：弹框打开时标题
     * dialogStatus：区分点击的哪个小方块，共用一个弹框表格，控制某一列的显示隐藏
     * show(1)：传的1，2，3可以理解为到时候掉接口的一个参数，区分类型，如果后期不用可去掉
     */
    timeFun() {
      this.deviceTitle = '耗电金额核算'
      this.dialogStatus = 'time'
      this.$refs.squareDlg.show(1)
    },
    /**
     * 加班天数
     */
    dayFun() {
      this.deviceTitle = '加班天数'
      this.dialogStatus = 'day'
      this.$refs.squareDlg.show(2)
    },
    /**
     * 非工作加班
     */
    noWorkFun() {
      this.deviceTitle = '非工作加班'
      this.dialogStatus = 'noWork'
      this.$refs.squareDlg.show(3)
    },
    noFun() {},
    /**
     * tab点击事件，如果不写，切换时图表只有小小一点点
     * @param pane
     * @param event
     */
    tabClick(pane, event) {
      if (pane.name === 'notCalendar') {
        this.isShowCalendar = true
        this.isShowNotAnalysis = false
      } else {
        this.isShowCalendar = false
        this.isShowNotAnalysis = true
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  /*小方格样式*/
  .data-panel{
    width: 100%;
    padding: 20px;
    display: flex;
  }
  .data-item{
    flex: 1;
    height: 100px;
    line-height: 100px;
    border: 1px solid #3b749c;
    display: inline-block;
    margin-left: 1%;
    font-size: 14px;
    display: flex;
    &:first-child{
      margin-left: 0;
    }
    label{
      line-height: 15px;
      align-self: center;
      font-size: 12px;
      font-weight: normal;
      flex: 2;
      text-align: center;
    }
    span{
      flex: 1;
      text-align: center;
      font-size: 12px;
    }
  }
  @media screen and (max-width: 1500px){
    .data-item{
      flex-direction: column;
      line-height: 50px;
    }
  }
  .data-analysis-panel{
    width: 100%;
    height: calc(100% - 140px);
    padding:0 20px 20px 20px;
    display: flex;
  }
  .data-analysis{
    flex: 1;
    height: 100%;
  }
  .data-details{
    width: 100%;
    height: 100%;
    padding:0 20px 20px 20px;
    display: flex;
  }
  .data-analysis-table{
    flex: 1;
  }
  .data-analysis-details{
    flex: 2;
  }
  /*解决表格宽度随浏览器大小变化自适应问题*/
  .table-parent-size{
    position: relative;
  }
  .table-left-size{
    width: 100%;
    height: calc(100% - 22px);
    position: absolute;
  }
  .table-right-size{
    width: 100%;
    position: absolute;
  }
  >>>.el-pagination{
    overflow-x: auto;
    overflow-y: hidden;
  }
  /*日历样式修改*/
  .data-analysis /deep/  .el-calendar-table .el-calendar-day{
    width: 100%;
    height: 100%;
  }
  /*隐藏今天按钮*/
  >>>.el-button-group>.el-button:not(:first-child):not(:last-child){
    display: none;
  }
  /*去掉原本背景颜色*/
  .data-analysis >>>.el-calendar{
    background: transparent;
  }
  >>>.el-calendar-table td:hover{
    background: transparent;
  }
  /*去掉选中背景颜色*/
  .data-analysis >>>.el-calendar-table td.is-selected{
    background: transparent;
  }
  /*修改每一小格大小*/
  .data-analysis >>>.el-calendar-table .el-calendar-day{
    position: relative;
    padding: 10px;
  }
  /*小红点样式*/
  .data-analysis >>>.el-badge{
    position: absolute;
    right: 5px;
    top: 5px;
  }
  .successColor >>>.el-badge__content{
    background: #33ff00;
  }
  .errorColor >>>.el-badge__content{
    background: #ff0000;
  }
  /*日历边框颜色*/
  .data-analysis >>>.el-calendar-table tr td:first-child{
    border-left: 1px solid #666666;
  }
  .data-analysis >>>.el-calendar-table tr:first-child td{
    border-top: 1px solid #666666;
  }
  .data-analysis >>>.el-calendar-table td{
    border-bottom: 1px solid #666666;
    border-right: 1px solid #666666;
  }
  /*表格周一到周日颜色*/
  .data-analysis >>>.el-calendar-table thead th{
    color: #68a8d0;
  }
  /*头部日期颜色*/
  .data-analysis >>>.el-calendar__title{
    color: #68a8d0;
  }
  /*头部下面的横线*/
  .data-analysis >>>.el-calendar__header{
    border-bottom: 1px solid #666666;
  }
  /*鼠标悬停样式*/
  .data-analysis >>>.el-calendar-table .el-calendar-day:hover{
    cursor: default;
    background: transparent;
  }
  /*非本月字体颜色*/
  .data-analysis >>>.el-calendar-table:not(.is-range) td.next, .el-calendar-table:not(.is-range) td.prev{
    color: #666666;
    cursor: pointer;
  }
</style>
