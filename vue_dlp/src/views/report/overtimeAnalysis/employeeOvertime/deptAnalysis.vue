<template>
  <!--部门分析-->
  <div class="data-details">
    <div class="data-analysis-table table-parent-size" style="padding-top: 20px;">
      <div class="table-container table-left-size">
        <grid-table
          ref="deptTableList"
          :row-data-api="rowDataApi"
          :col-model="colModel"
          :show-pager="true"
          @cell-click="cellClick"
        />
      </div>
    </div>
    <div class="data-analysis-details">
      <!--部门分析上方几个小方块-->
      <div class="data-panel">
        <div class="data-item" >
          <label :title="itemValue.deptName">{{ itemValue.deptName }}</label>
        </div>
        <div v-for="(item, key) in deptItemOption" :key="key" class="data-item" >
          <label :title="item.label">{{ item.label }}</label>
          <span> :title="itemValue[item.key]"{{ itemValue[item.key] }}</span>
        </div>
      </div>
      <div style="height: calc(100% - 140px); padding: 0 20px">
        <el-tabs v-model="activeNotOfflineName" @tab-click="tabClick">
          <el-tab-pane label="加班信息统计" name="notMsg" class="table-parent-size">
            <div class="table-container table-right-size">
              <grid-table
                ref="detailTableList"
                :autoload="false"
                :row-data-api="detailDataApi"
                :col-model="detailColModel"
                :show-pager="true"
              />
            </div>
          </el-tab-pane>
          <el-tab-pane label="加班数量趋势" name="notNum">
            <line-chart
              v-if="isShowChart"
              :chart-data="lineChartData"
              :chart-option="lineOption"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
import { getOverTimeDeptPage, getOverTimeDeptDetail, getDeptTrend } from '@/api/report/baseReport/panelReport/panel';
import { formatSeconds } from '@/utils'

export default {
  name: 'DeptAnalysis',
  components: { LineChart },
  props: {
    recentDays: { type: Number, default: 30 }
  },
  data() {
    return {
      query: {
        page: 1,
        limit: 100,
        recentDays: this.recentDays
      },
      detailQuery: {
        page: 1,
        limit: 100,
        groupId: null,
        groupName: '',
        recentDays: this.recentDays
      },
      colModel: [
        { prop: 'objectName', label: '部门名称' },
        { prop: 'overTimeAvgSumAll', label: '人均加班时长', formatter: (row, data) => { return formatSeconds(data) } }
      ],
      rowData: [],
      // 部门分析
      deptItemOption: [
        { label: '加班人天', key: 'days' },
        { label: '人均加班时长', key: 'avg' }
      ],
      itemValue: {
        deptName: '',
        days: 0,
        avg: 0
      },
      // 部门分析
      activeNotOfflineName: 'notMsg',
      isShowGrid: true,
      isShowChart: false,
      detailColModel: [
        { prop: 'objectName', label: '姓名' },
        { prop: 'overTimeDaysSumAll', label: '加班总天数' },
        { prop: 'overTimeSumAll', label: '加班总时长', formatter: (row, data) => { return formatSeconds(data) } },
        { prop: 'overTimeNoWorkDaysSumAll', label: '非工作日加班天数' }
      ],
      // 折线图数据
      lineChartData: [],
      lineOption: {
        xAxis: [{
          type: 'category',
          data: []
        }],
        yAxis: [{
          type: 'value'
        }],
        legend: {
          top: '5%',
          selected: {
            '工作加班天数': true,
            '非工作加班天数': true
          },
          data: ['工作加班天数', '非工作加班天数']
        },
        dataZoom: [
          {
            textStyle: {
              color: '#027AB6'
            },
            type: 'inside',
            start: 0,
            end: 30
          },
          {
            type: 'slider',
            start: 0,
            end: 30
          }
        ],
        series: [
          {
            data: [],
            type: 'line',
            name: '工作加班天数'
          },
          {
            data: [],
            type: 'line',
            name: '非工作加班天数'
          }
        ]
      }
    }
  },
  computed: {

  },
  created() {

  },
  activated() {},
  methods: {
    cellClick(row, column, cell, event) {
      this.itemValue.deptName = row.objectName
      this.itemValue.days = row.overTimeDaysSumAll
      this.itemValue.avg = formatSeconds(row.overTimeAvgSumAll)
      this.detailQuery.groupId = row.objectId
      this.detailQuery.groupName = row.objectName
      this.$refs.detailTableList.execRowDataApi(this.detailQuery)
      getDeptTrend(this.detailQuery).then(res => {
        const trendData = res.data
        this.lineOption.series[0].data = trendData.work.datas
        this.lineOption.series[1].data = trendData.noWork.datas
        this.lineOption.xAxis[0].data = trendData.work.xaxisData
      })
    },
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getOverTimeDeptPage(searchQuery)
    },
    detailDataApi(option) {
      const searchQuery = Object.assign({}, this.detailQuery, option)
      return getOverTimeDeptDetail(searchQuery)
    },
    /**
     * tab点击事件，如果不写，切换时图表只有小小一点点
     * @param pane
     * @param event
     */
    tabClick(pane, event) {
      if (pane.name === 'notMsg') {
        this.isShowGrid = true
        this.isShowChart = false
      } else {
        this.isShowGrid = false
        this.isShowChart = true
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  /*小方格样式*/
  .data-panel{
    width: 100%;
    padding: 20px;
    display: flex;
  }
  .data-item{
    flex: 1;
    height: 100px;
    line-height: 100px;
    border: 1px solid #3b749c;
    display: inline-block;
    margin-left: 1%;
    font-size: 14px;
    display: flex;
    &:first-child{
      margin-left: 0;
    }
    label{
      line-height: 15px;
      align-self: center;
      font-weight: normal;
      font-size: 12px;
      flex: 2;
      text-align: center;
    }
    span{
      flex: 1;
      text-align: center;
      font-size: 12px;
    }
  }
  @media screen and (max-width: 1500px){
    .data-item{
      flex-direction: column;
      line-height: 50px;
    }
  }
  .data-analysis{
    flex: 1;
    height: 100%;
  }
  .data-details{
    width: 100%;
    height: 100%;
    padding:0 20px 20px 20px;
    display: flex;
  }
  .data-analysis-table{
    flex: 1;
  }
  .data-analysis-details{
    flex: 2;
  }
  /*解决表格宽度随浏览器大小变化自适应问题*/
  .table-parent-size{
    position: relative;
  }
  .table-left-size{
    width: 100%;
    height: calc(100% - 22px);
    position: absolute;
  }
  .table-right-size{
    width: 100%;
    position: absolute;
  }
  >>>.el-pagination{
    overflow-x: auto;
    overflow-y: hidden;
  }
</style>
