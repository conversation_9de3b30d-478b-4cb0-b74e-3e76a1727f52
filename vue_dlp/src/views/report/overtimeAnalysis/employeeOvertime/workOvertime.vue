<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      append-to-body
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <line-chart
        :chart-data="lineChartData"
        :chart-option="lineOption"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import LineChart from '@/components/ECharts/LineChart'
export default {
  name: 'WorkOvertime',
  components: { LineChart },
  props: {
    title: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      // 折线图数据
      lineChartData: [150, 230, 224, 218, 135, 147, 260],
      lineOption: {
        title: {
          'text': '员工加班分析图',
          'subtext': '最近7天'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['工作', '网购', '追剧', '聊天', '待机', '浏览新闻']
        },
        xAxis: {
          type: 'category',
          data: ['2023-2-22', '2023-2-21', '2023-2-20', '2023-2-19', '2023-2-18', '2023-2-17', '2023-2-16']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '工作',
            type: 'line',
            stack: 'Total',
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: '网购',
            type: 'line',
            stack: 'Total',
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: '追剧',
            type: 'line',
            stack: 'Total',
            data: [150, 232, 201, 154, 190, 330, 410]
          },
          {
            name: '聊天',
            type: 'line',
            stack: 'Total',
            data: [320, 332, 301, 334, 390, 330, 320]
          },
          {
            name: '待机',
            type: 'line',
            stack: 'Total',
            data: [820, 932, 901, 934, 1290, 1330, 1320]
          },
          {
            name: '浏览新闻',
            type: 'line',
            stack: 'Total',
            data: [420, 300, 500, 400, 200, 100, 500]
          }
        ]
      }
    }
  },
  computed: {

  },
  created() {
    this.resetTemp()
  },
  methods: {
    /**
       * 显示弹框
       */
    show() {
      this.resetTemp()
      this.dialogVisible = true
    },
    hide() {
      this.resetTemp()
      this.dialogVisible = false
    },
    resetTemp() {
      this.submitting = false
    },
    closed() {
    },
    handleDrag() {
    },
    createData() {

    }
  }
}
</script>

<style lang="scss" scoped>

</style>

