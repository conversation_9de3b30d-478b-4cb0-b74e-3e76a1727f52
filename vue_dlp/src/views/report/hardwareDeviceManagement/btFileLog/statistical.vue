<template>
  <Statistical :api="'getBtFileLogPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('581')" :exportable="hasPermission('582')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getBluetoothFileTransferNumDetailCol } from '@/utils/report'

export default {
  name: 'BtFileLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'bluetoothFileTransferCountSum', label: 'bluetoothFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() },
        { prop: 'bluetoothFileTransferSizeSum', label: 'bluetoothFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'bluetoothFileTransferCount', label: 'bluetoothFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() },
        { prop: 'bluetoothFileTransferSize', label: 'bluetoothFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'bluetoothFileTransferCountSum', label: 'bluetoothFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() },
        { prop: 'bluetoothFileTransferSizeSum', label: 'bluetoothFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'devTypeName', label: 'devType', width: '150', sort: true, joinSearch: true, searchCol: 'dev_type', searchProp: 'devType' },
        { prop: 'bluetoothFileTransferCount', label: 'bluetoothFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() },
        { prop: 'bluetoothFileTransferSize', label: 'bluetoothFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
