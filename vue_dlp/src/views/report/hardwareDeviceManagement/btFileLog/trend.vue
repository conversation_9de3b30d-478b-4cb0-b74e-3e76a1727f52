<template>
  <Trend :api="'getBtFileLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('584')" :exportable="hasPermission('585')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getBluetoothFileTransferNumDetailCol } from '@/utils/report'

export default {
  name: 'BtFileLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'bluetoothFileTransferCountSum', label: 'bluetoothFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() },
        { prop: 'bluetoothFileTransferSizeSum', label: 'bluetoothFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() }
      ],
      customList: [
        { prop: 'bluetoothFileTransferCountSum', label: 'bluetoothFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() },
        { prop: 'bluetoothFileTransferSizeSum', label: 'bluetoothFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_bt_file_log', detailCol: getBluetoothFileTransferNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
