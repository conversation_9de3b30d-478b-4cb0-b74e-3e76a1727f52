<template>
  <comparative-trend
    :api="'getBtFileLogCompareData'"
    :api-dept="'getBtFileLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'BtFileLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '蓝牙文件传输',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'bluetoothFileTransferCountSum', label: 'bluetoothFileTransferCountSum' },
        { prop: 'bluetoothFileTransferSizeSum', label: 'bluetoothFileTransferSizeSum', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['bluetoothFileTransferCountSum']
    }
  }
}
</script>
