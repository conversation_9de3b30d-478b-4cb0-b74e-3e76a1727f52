<template>
  <comparative-trend
    :api="'getMobileStorageCompareData'"
    :api-dept="'getMobileStorageCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
import { filterMobileStorageCustom } from '@/utils/reportPermissionFiltering';
export default {
  name: 'MobileStorageCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '移动存储',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'deviceInsertTimesSumAll', label: 'deviceInsertTimesSumAll' },
        { prop: 'fileNumSumAll', label: 'fileNumSumAll' },
        { prop: 'fileSizeSumAll', label: 'fileSizeSumAll', flag: 'size' },
        { prop: 'createFileNumSumAll', label: 'createFileNumSumAll' },
        { prop: 'createFileSizeSumAll', label: 'createFileSizeSumAll', flag: 'size' },
        { prop: 'copyFileNumSumAll', label: 'copyFileNumSumAll' },
        { prop: 'copyFileSizeSumAll', label: 'copyFileSizeSumAll', flag: 'size' },
        { prop: 'renameFileNumSumAll', label: 'renameFileNumSumAll' },
        { prop: 'renameFileSizeSumAll', label: 'renameFileSizeSumAll', flag: 'size' },
        { prop: 'deleteFileNumSumAll', label: 'deleteFileNumSumAll' },
        { prop: 'deleteFileSizeSumAll', label: 'deleteFileSizeSumAll', flag: 'size' },
        { prop: 'openFileNumSumAll', label: 'openFileNumSumAll' },
        { prop: 'openFileSizeSumAll', label: 'openFileSizeSumAll', flag: 'size' },
        { prop: 'updateFileNumSumAll', label: 'updateFileNumSumAll' },
        { prop: 'updateFileSizeSumAll', label: 'updateFileSizeSumAll', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['deviceInsertTimesSumAll', 'createFileNumSumAll', 'copyFileNumSumAll', 'openFileNumSumAll', 'updateFileNumSumAll']
    }
  },
  computed: {

  },
  watch: {
  },
  created() {
    this.filterData()
  },
  methods: {
    /**
     * 权限过滤
     */
    filterData() {
      this.statisticalList = filterMobileStorageCustom(this.statisticalList)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
