<template>
  <Trend :api="'getMobileStorageOpTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('645')" :exportable="hasPermission('646')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getMobileStorageDetail, getMobileFileOpDetail } from '@/api/report/baseReport/issueReport/detail'
import { getDeviceInsertTimesDetailCol, getFileOpNumDetailCol, beforeLoad } from '@/utils/report'
import { filterMobileStorageCustom, filterMobileStorageDefault } from '@/utils/reportPermissionFiltering';

export default {
  name: 'MobileStorageTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '140', sort: true, sortOriginal: true },
        { prop: 'deviceInsertTimesSumAll', label: 'deviceInsertTimesSumAll', width: '160', sort: true, detailCol: getDeviceInsertTimesDetailCol(), getDetailMethod: getMobileStorageDetail, beforeLoadDetail: beforeLoad },
        { prop: 'createFileNumSumAll', label: 'createFileNumSumAll', width: '130', sort: true, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'copyFileNumSumAll', label: 'copyFileNumSumAll', width: '130', sort: true, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'openFileNumSumAll', label: 'openFileNumSumAll', width: '130', sort: true, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'updateFileNumSumAll', label: 'updateFileNumSumAll', width: '130', sort: true, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad }
      ],
      customList: [ // 表头数据
        { prop: 'deviceInsertTimesSumAll', label: 'deviceInsertTimesSumAll', width: '160', sort: true, value: 1, detailCol: getDeviceInsertTimesDetailCol(), getDetailMethod: getMobileStorageDetail, beforeLoadDetail: beforeLoad },
        { prop: 'fileNumSumAll', label: 'opFileNumSumAll', width: '160', sort: true, value: 2, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'fileSizeSumAll', label: 'opFileSizeSumAll', width: '160', sort: true, flag: 'size', sortOriginal: true, value: 4, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'createFileNumSumAll', label: 'createFileNumSumAll', width: '130', sort: true, value: 8, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'createFileSizeSumAll', label: 'createFileSizeSumAll', width: '130', sort: true, flag: 'size', sortOriginal: true, value: 16, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'copyFileNumSumAll', label: 'copyFileNumSumAll', width: '130', sort: true, value: 512, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'copyFileSizeSumAll', label: 'copyFileSizeSumAll', width: '130', sort: true, flag: 'size', sortOriginal: true, value: 1024, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'renameFileNumSumAll', label: 'renameFileNumSumAll', width: '150', sort: true, value: 32, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'renameFileSizeSumAll', label: 'renameFileSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 64, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'deleteFileNumSumAll', label: 'deleteFileNumSumAll', width: '130', sort: true, value: 128, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'deleteFileSizeSumAll', label: 'deleteFileSizeSumAll', width: '130', sort: true, flag: 'size', sortOriginal: true, value: 256, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'openFileNumSumAll', label: 'openFileNumSumAll', width: '130', sort: true, value: 2048, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'openFileSizeSumAll', label: 'openFileSizeSumAll', width: '130', sort: true, flag: 'size', sortOriginal: true, value: 4096, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'updateFileNumSumAll', label: 'updateFileNumSumAll', width: '130', sort: true, value: 8192, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad },
        { prop: 'updateFileSizeSumAll', label: 'updateFileSizeSumAll', width: '130', sort: true, flag: 'size', sortOriginal: true, value: 16384, detailCol: getFileOpNumDetailCol(), getDetailMethod: getMobileFileOpDetail, beforeLoadDetail: beforeLoad }
      ]
    }
  },
  created() {
    this.filterData()
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    /**
     * 权限过滤
     */
    filterData() {
      this.customList = filterMobileStorageCustom(this.customList)
      this.colModel = filterMobileStorageDefault(this.colModel, 'labelValue')
    }
  }
}
</script>
