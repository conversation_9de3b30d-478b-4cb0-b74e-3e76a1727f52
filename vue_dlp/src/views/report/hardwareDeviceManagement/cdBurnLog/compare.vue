<template>
  <comparative-trend
    :api="'getCdBurnLogCompareData'"
    :api-dept="'getCdBurnLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'CdBurnLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '刻录机监控',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'cdBurnRecordCountSum', label: 'cdBurnRecordCountSum' },
        { prop: 'cdBurnFileSizeKbSum', label: 'cdBurnFileSizeSum', sortColumn: 'cd_burn_file_size_sum', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['cdBurnRecordCountSum']
    }
  }
}
</script>
