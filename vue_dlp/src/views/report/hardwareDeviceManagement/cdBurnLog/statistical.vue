<template>
  <Statistical :api="'getCdBurnLogPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('923')" :exportable="hasPermission('924')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getCdBurnNumDetailCol } from '@/utils/report'

export default {
  name: 'CdBurnLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'cdBurnRecordCountSum', label: 'cdBurnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() },
        { prop: 'cdBurnFileSizeKbSum', label: 'cdBurnFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, sortField: 'cdBurnFileSizeSum', detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'cdBurnRecordCount', label: 'cdBurnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() },
        { prop: 'cdBurnFileSizeKb', label: 'cdBurnFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'cdBurnRecordCountSum', label: 'cdBurnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() },
        { prop: 'cdBurnFileSizeKbSum', label: 'cdBurnFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, sortField: 'cdBurnFileSizeSum', detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'actionTypeName', label: 'actionTypeName', width: '150', sort: true, joinSearch: true, searchCol: 'action_type', searchProp: 'actionType' },
        { prop: 'cdBurnRecordCount', label: 'cdBurnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() },
        { prop: 'cdBurnFileSizeKb', label: 'cdBurnFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
