<template>
  <Trend :api="'getCdBurnLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('926')" :exportable="hasPermission('927')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getCdBurnNumDetailCol } from '@/utils/report'

export default {
  name: 'CdBurnLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'cdBurnRecordCountSum', label: 'cdBurnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() },
        { prop: 'cdBurnFileSizeKbSum', label: 'cdBurnFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() }
      ],
      customList: [
        { prop: 'cdBurnRecordCountSum', label: 'cdBurnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() },
        { prop: 'cdBurnFileSizeKbSum', label: 'cdBurnFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_cd_burn_log', detailCol: getCdBurnNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
