<template>
  <Trend :api="'getPrintMonitorTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('625')" :exportable="hasPermission('626')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getPrintDetailCol } from '@/utils/report'

export default {
  name: 'PrintTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll', width: '150', sort: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'printedPagesSumAll', label: 'printedPagesSumAll', width: '150', sort: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'fileSizeSumAll', label: 'printFileSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() }
      ],
      customList: [
        { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'printedPagesSumAll', label: 'printedPagesSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'fileSizeSumAll', label: 'printFileSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 4, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
