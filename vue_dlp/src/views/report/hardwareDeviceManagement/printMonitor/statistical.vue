<template>
  <Statistical :api="'getPrintMonitorPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('620')" :exportable="hasPermission('621')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '../../component/Statistical'
import { getPrintDetailCol } from '@/utils/report'

export default {
  name: 'PrintStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'printedPagesSumAll', label: 'printedPagesSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'fileSizeSumAll', label: 'printFileSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 4, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() }
      ],
      subCustomList: [
        { prop: 'printedCopiesSum', label: 'printedCopiesSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'printedPagesSum', label: 'printedPagesSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'fileSizeSum', label: 'printFileSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 4, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll', width: '150', sort: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'printedPagesSumAll', label: 'printedPagesSumAll', width: '150', sort: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'fileSizeSumAll', label: 'printFileSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'printerName', label: 'printerName', width: '150', sort: true, joinSearch: true, searchCol: 'printer_name' },
        { prop: 'printedCopiesSum', label: 'printedCopiesSumAll', width: '150', sort: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'printedPagesSum', label: 'printedPagesSumAll', width: '150', sort: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() },
        { prop: 'fileSizeSum', label: 'printFileSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_print_monitor_log', detailCol: getPrintDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
