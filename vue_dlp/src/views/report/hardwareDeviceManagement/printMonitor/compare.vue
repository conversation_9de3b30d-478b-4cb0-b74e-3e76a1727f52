<template>
  <comparative-trend
    :api="'getPrintMonitorLogCompareData'"
    :api-dept="'getPrintMonitorCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'PrintCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '打印',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'printedCopiesSumAll', label: 'printedCopiesSumAll' },
        { prop: 'printedPagesSumAll', label: 'printedPagesSumAll' },
        { prop: 'fileSizeSumAll', label: 'printFileSizeSumAll', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['printedCopiesSumAll', 'printedPagesSumAll']
    }
  },
  computed: {

  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
