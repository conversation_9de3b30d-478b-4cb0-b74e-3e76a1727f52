<template>
  <Trend :api="'getMtpFileLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('560')" :exportable="hasPermission('561')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getMtpOutgoingFileNumDetailCol } from '@/utils/report'

export default {
  name: 'MtpFileLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'mtpOutgoingFileCountSum', label: 'mtpOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() },
        { prop: 'mtpOutgoingFileSizeSum', label: 'mtpOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() }
      ],
      customList: [
        { prop: 'mtpOutgoingFileCountSum', label: 'mtpOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() },
        { prop: 'mtpOutgoingFileSizeSum', label: 'mtpOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
