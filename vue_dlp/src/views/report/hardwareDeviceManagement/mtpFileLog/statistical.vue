<template>
  <Statistical :api="'getMtpFileLogPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('556')" :exportable="hasPermission('557')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getMtpOutgoingFileNumDetailCol } from '@/utils/report'

export default {
  name: 'MtpFileLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'mtpOutgoingFileCountSum', label: 'mtpOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() },
        { prop: 'mtpOutgoingFileSizeSum', label: 'mtpOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'mtpOutgoingFileCount', label: 'mtpOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() },
        { prop: 'mtpOutgoingFileSize', label: 'mtpOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'mtpOutgoingFileCountSum', label: 'mtpOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() },
        { prop: 'mtpOutgoingFileSizeSum', label: 'mtpOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'actionTypeName', label: 'actionTypeName', width: '150', sort: true, joinSearch: true, searchCol: 'action_type', searchProp: 'actionType' },
        { prop: 'mtpOutgoingFileCount', label: 'mtpOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() },
        { prop: 'mtpOutgoingFileSize', label: 'mtpOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_mtp_file_log', detailCol: getMtpOutgoingFileNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
