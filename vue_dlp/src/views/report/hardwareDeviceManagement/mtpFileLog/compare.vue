<template>
  <comparative-trend
    :api="'getMtpFileLogCompareData'"
    :api-dept="'getMtpFileLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'MtpFileLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: 'MTP监控',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'mtpOutgoingFileCountSum', label: 'mtpOutgoingFileCountSum' },
        { prop: 'mtpOutgoingFileSizeSum', label: 'mtpOutgoingFileSizeSum', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['mtpOutgoingFileCountSum']
    }
  }
}
</script>
