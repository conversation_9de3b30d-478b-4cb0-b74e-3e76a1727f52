<template>
  <comparative-trend
    :api="'getUsbDeviceOperaLogCompareData'"
    :api-dept="'getUsbDeviceOperaLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'UsbDeviceOperaLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: 'USB监控',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'usbOutgoingFileCountSum', label: 'usbOutgoingFileCountSum' },
        { prop: 'usbOutgoingFileSizeSum', label: 'usbOutgoingFileSizeSum', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['usbOutgoingFileCountSum']
    }
  }
}
</script>
