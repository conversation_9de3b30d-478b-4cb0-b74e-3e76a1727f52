<template>
  <Statistical :api="'getUsbDeviceOperaLogPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('911')" :exportable="hasPermission('912')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getUsbOutgoingFileNumDetailCol } from '@/utils/report'

export default {
  name: 'UsbDeviceOperaLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'usbOutgoingFileCountSum', label: 'usbOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() },
        { prop: 'usbOutgoingFileSizeSum', label: 'usbOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'usbOutgoingFileCount', label: 'usbOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() },
        { prop: 'usbOutgoingFileSize', label: 'usbOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'usbOutgoingFileCountSum', label: 'usbOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() },
        { prop: 'usbOutgoingFileSizeSum', label: 'usbOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'fileOpTypeName', label: 'operateType', width: '150', sort: true, joinSearch: true, searchCol: 'file_op_type', searchProp: 'fileOpType' },
        { prop: 'usbOutgoingFileCount', label: 'usbOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() },
        { prop: 'usbOutgoingFileSize', label: 'usbOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
