<template>
  <Trend :api="'getUsbDeviceOperaLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('914')" :exportable="hasPermission('915')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getUsbOutgoingFileNumDetailCol } from '@/utils/report'

export default {
  name: 'UsbDeviceOperaLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'usbOutgoingFileCountSum', label: 'usbOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() },
        { prop: 'usbOutgoingFileSizeSum', label: 'usbOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() }
      ],
      customList: [
        { prop: 'usbOutgoingFileCountSum', label: 'usbOutgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() },
        { prop: 'usbOutgoingFileSizeSum', label: 'usbOutgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_usb_device_opera_log', detailCol: getUsbOutgoingFileNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
