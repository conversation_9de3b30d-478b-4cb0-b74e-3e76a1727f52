<template>
  <comparative-trend
    :api="'getAdbMonitorLogCompareData'"
    :api-dept="'getAdbMonitorLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'AdbMonitorLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: 'ADB监控',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'outgoingFileCountSum', label: 'outgoingFileCountSum' },
        { prop: 'outgoingFileSizeSum', label: 'outgoingFileSizeSum', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['outgoingFileCountSum']
    }
  }
}
</script>
