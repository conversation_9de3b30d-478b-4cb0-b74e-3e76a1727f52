<template>
  <Statistical :api="'getAdbMonitorLogPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('550')" :exportable="hasPermission('551')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getAdbOutgoingFileNumDetailCol } from '@/utils/report'

export default {
  name: 'AdbMonitorLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'outgoingFileCountSum', label: 'outgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() },
        { prop: 'outgoingFileSizeSum', label: 'outgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'outgoingFileCount', label: 'outgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() },
        { prop: 'outgoingFileSize', label: 'outgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'outgoingFileCountSum', label: 'outgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() },
        { prop: 'outgoingFileSizeSum', label: 'outgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'actionTypeName', label: 'actionTypeName', width: '150', sort: true, joinSearch: true, searchCol: 'action_type', searchProp: 'actionType' },
        { prop: 'outgoingFileCount', label: 'outgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() },
        { prop: 'outgoingFileSize', label: 'outgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
