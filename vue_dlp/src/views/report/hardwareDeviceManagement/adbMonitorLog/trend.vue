<template>
  <Trend :api="'getAdbMonitorLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('553')" :exportable="hasPermission('554')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getAdbOutgoingFileNumDetailCol } from '@/utils/report'

export default {
  name: 'AdbMonitorLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'outgoingFileCountSum', label: 'outgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() },
        { prop: 'outgoingFileSizeSum', label: 'outgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() }
      ],
      customList: [
        { prop: 'outgoingFileCountSum', label: 'outgoingFileCountSum', width: '150', sort: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() },
        { prop: 'outgoingFileSizeSum', label: 'outgoingFileSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_adb_monitor_log', detailCol: getAdbOutgoingFileNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
