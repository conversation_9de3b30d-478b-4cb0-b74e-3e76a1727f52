<template>
  <Statistical :api="'getBtConnChangePie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('575')" :exportable="hasPermission('576')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getBluetoothParingNumDetailCol } from '@/utils/report'

export default {
  name: 'BtConnChangeStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'bluetoothPairingRecordCountSum', label: 'bluetoothPairingRecordCountSum', width: '150', sort: true, detailTable: 'dwd_bt_conn_change', detailCol: getBluetoothParingNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'bluetoothPairingRecordCount', label: 'bluetoothPairingRecordCountSum', width: '150', sort: true, detailTable: 'dwd_bt_conn_change', detailCol: getBluetoothParingNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'bluetoothPairingRecordCountSum', label: 'bluetoothPairingRecordCountSum', width: '150', sort: true, detailTable: 'dwd_bt_conn_change', detailCol: getBluetoothParingNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'devTypeName', label: 'devType', width: '150', sort: true, joinSearch: true, searchCol: 'dev_type', searchProp: 'devType' },
        { prop: 'bluetoothPairingRecordCount', label: 'bluetoothPairingRecordCountSum', width: '150', sort: true, detailTable: 'dwd_bt_conn_change', detailCol: getBluetoothParingNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
