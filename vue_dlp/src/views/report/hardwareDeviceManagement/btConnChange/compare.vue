<template>
  <comparative-trend
    :api="'getBtConnChangeCompareData'"
    :api-dept="'getBtConnChangeCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'BtConnChangeCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '蓝牙配对',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'bluetoothPairingRecordCountSum', label: 'bluetoothPairingRecordCountSum' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['bluetoothPairingRecordCountSum']
    }
  }
}
</script>
