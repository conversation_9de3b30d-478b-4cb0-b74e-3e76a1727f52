<template>
  <Trend :api="'getBtConnChangeTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('578')" :exportable="hasPermission('579')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getBluetoothParingNumDetailCol } from '@/utils/report'

export default {
  name: 'BtConnChangeTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'bluetoothPairingRecordCountSum', label: 'bluetoothPairingRecordCountSum', width: '150', sort: true, detailTable: 'dwd_bt_conn_change', detailCol: getBluetoothParingNumDetailCol() }
      ],
      customList: [
        { prop: 'bluetoothPairingRecordCountSum', label: 'bluetoothPairingRecordCountSum', width: '150', sort: true, detailTable: 'dwd_bt_conn_change', detailCol: getBluetoothParingNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
