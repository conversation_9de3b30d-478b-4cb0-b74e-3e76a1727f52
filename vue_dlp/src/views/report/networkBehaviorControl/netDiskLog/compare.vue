<template>
  <comparative-trend
    :api="'getNetDiskLogCompareData'"
    :api-dept="'getNetDiskLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'NetDiskLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '网盘审计',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'fileNumSumAll', label: 'fileNum' },
        { prop: 'fileSizeKbSumAll', label: 'fileSizeSumAll', sortColumn: 'file_size_sum_all', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['fileNumSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
