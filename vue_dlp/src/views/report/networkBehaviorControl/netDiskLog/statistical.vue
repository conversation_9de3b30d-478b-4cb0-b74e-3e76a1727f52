<template>
  <Statistical
    :api="'getNetDiskLogPie'"
    :default-col-model="defaultColModel"
    :sub-col-model="subColModel"
    :custom-list="customList"
    :drill-down="hasPermission('810')"
    :exportable="hasPermission('811')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { getNetworkDiskAuditFileNumDetailCol } from '@/utils/report'

export default {
  name: 'NetDiskLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'fileNumSumAll', label: 'fileNum', width: '150', sort: true, value: 1, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() },
        { prop: 'fileSizeKbSumAll', label: 'fileSizeSumAll', width: '150', sort: true, sortOriginal: true, sortField: 'fileSizeSumAll', flag: 'size', value: 2, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'fileNumSum', label: 'fileNum', width: '150', sort: true, value: 1, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() },
        { prop: 'fileSizeKbSum', label: 'fileSizeSumAll', width: '150', sort: true, sortOriginal: true, flag: 'size', value: 2, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'fileNumSumAll', label: 'fileNum', width: '150', sort: true, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() },
        { prop: 'fileSizeKbSumAll', label: 'fileSizeSumAll', width: '150', flag: 'size', sort: true, sortOriginal: true, sortField: 'fileSizeSumAll', detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() }],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'signName', label: 'signName', width: '150', sort: true, joinSearch: true, searchCol: 'sign', searchProp: 'sign' },
        { prop: 'fileNumSum', label: 'fileNum', width: '150', sort: true, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() },
        { prop: 'fileSizeKbSum', label: 'fileSizeSumAll', width: '150', flag: 'size', sort: true, sortOriginal: true, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>

