<template>
  <Trend :api="'getNetDiskLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('815')" :exportable="hasPermission('816')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getNetworkDiskAuditFileNumDetailCol } from '@/utils/report'

export default {
  name: 'NetDiskLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'fileNumSumAll', label: 'fileNum', width: '150', sort: true, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() },
        { prop: 'fileSizeKbSumAll', label: 'fileSizeSumAll', width: '150', flag: 'size', sort: true, sortOriginal: true, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() }
      ],
      customList: [
        { prop: 'fileNumSumAll', label: 'fileNum', width: '150', sort: true, value: 1, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() },
        { prop: 'fileSizeKbSumAll', label: 'fileSizeSumAll', width: '150', sort: true, sortOriginal: true, flag: 'size', value: 2, detailTable: 'dwd_net_disk_log', detailCol: getNetworkDiskAuditFileNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>

