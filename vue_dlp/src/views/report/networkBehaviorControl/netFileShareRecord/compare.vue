<template>
  <comparative-trend
    :api="'getNetFileShareRecordCompareData'"
    :api-dept="'getNetFileShareRecordCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'NetFileShareRecordCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '网络共享',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'netFileShareCountSum', label: 'netFileShareCountSum' },
        { prop: 'netFileShareSizeSum', label: 'netFileShareSizeSum', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['netFileShareCountSum']
    }
  }
}
</script>
