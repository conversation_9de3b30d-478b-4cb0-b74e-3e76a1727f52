<template>
  <Trend :api="'getNetFileShareRecordTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('893')" :exportable="hasPermission('894')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getNetFileShareNumDetailCol } from '@/utils/report'

export default {
  name: 'NetFileShareRecordTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'netFileShareCountSum', label: 'netFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() },
        { prop: 'netFileShareSizeSum', label: 'netFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() }
      ],
      customList: [
        { prop: 'netFileShareCountSum', label: 'netFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() },
        { prop: 'netFileShareSizeSum', label: 'netFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
