<template>
  <Statistical :api="'getNetFileShareRecordPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('890')" :exportable="hasPermission('891')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getNetFileShareNumDetailCol } from '@/utils/report'

export default {
  name: 'NetFileShareRecordStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'netFileShareCountSum', label: 'netFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() },
        { prop: 'netFileShareSizeSum', label: 'netFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'netFileShareCount', label: 'netFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() },
        { prop: 'netFileShareSize', label: 'netFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'netFileShareCountSum', label: 'netFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() },
        { prop: 'netFileShareSizeSum', label: 'netFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'fileOpTypeName', label: 'operateType', width: '150', sort: true, joinSearch: true, searchCol: 'file_op_type', searchProp: 'fileOpType' },
        { prop: 'netFileShareCount', label: 'netFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() },
        { prop: 'netFileShareSize', label: 'netFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_net_file_share_record', detailCol: getNetFileShareNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
