<template>
  <Statistical
    :api="'getMailPie'"
    :default-col-model="defaultColModel"
    :sub-col-model="subColModel"
    :custom-list="customList"
    :drill-down="hasPermission('710')"
    :exportable="hasPermission('711')"
    :view-details="hasPermission('712')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { getEmailNumDetailCol } from '@/utils/report'

export default {
  name: 'MailLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'mailNumSumAll', label: 'mailNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'mailSendNumSumAll', label: 'mailSendNumSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'mailReceiveNumSumAll', label: 'mailReceiveNumSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'fileSizeSumAll', label: 'mailSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 8, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'attachMailCountSumAll', label: 'attachMailCountSumAll', width: '150', sort: true, value: 16, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad }
      ],
      subCustomList: [
        { prop: 'mailNumSum', label: 'mailNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'mailSendNumSum', label: 'mailSendNumSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'mailReceiveNumSum', label: 'mailReceiveNumSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'fileSizeSum', label: 'mailSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 8, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'attachMailCountSum', label: 'attachMailCountSumAll', width: '150', sort: true, value: 16, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'mailNumSumAll', label: 'mailNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'mailSendNumSumAll', label: 'mailSendNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'mailReceiveNumSumAll', label: 'mailReceiveNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'fileSizeSumAll', label: 'mailSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'attachMailCountSumAll', label: 'attachMailCountSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'cmdTypeName', label: 'cmdTypeName', width: '150', sort: true, joinSearch: true, searchCol: 'cmd_type', searchProp: 'cmdType' },
        { prop: 'mailNumSum', label: 'mailNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'mailSendNumSum', label: 'mailSendNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'mailReceiveNumSum', label: 'mailReceiveNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'fileSizeSum', label: 'mailSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'attachMailCountSum', label: 'attachMailCountSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad }
      ]
    }
  },
  methods: {
    beforeLoad(row, column, cell, event, detailQuery) {
      if (!detailQuery.hasOwnProperty('condition')) {
        detailQuery['condition'] = {}
      }
      if (column.property == 'mailSendNumSum' || column.property == 'mailSendNumSumAll') {
        if (row.cmdType) {
          if ([1, 13, 11, 14].indexOf(row.cmdType) != -1) {
            detailQuery['condition']['cmd_type'] = [row.cmdType]
          } else {
            detailQuery['condition']['cmd_type'] = [-9999]
          }
        } else {
          detailQuery['condition']['cmd_type'] = [1, 13, 11, 14]
        }
      }
      if (column.property == 'mailReceiveNumSum' || column.property == 'mailReceiveNumSumAll') {
        if (row.cmdType) {
          if ([2, 3, 12, 15].indexOf(row.cmdType) != -1) {
            detailQuery['condition']['cmd_type'] = [row.cmdType]
          } else {
            detailQuery['condition']['cmd_type'] = [-9999]
          }
        } else {
          detailQuery['condition']['cmd_type'] = [2, 3, 12, 15]
        }
      }
      if (column.property == 'attachMailCountSum' || column.property == 'attachMailCountSumAll') {
        detailQuery['condition']['attach_count'] = 1
      }

      return detailQuery;
    },
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    },
    getEmailNumDetailCol() {
      return getEmailNumDetailCol(this.hasPermission('712'))
    }
  }
}
</script>
