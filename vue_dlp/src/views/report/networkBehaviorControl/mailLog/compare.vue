<template>
  <comparative-trend
    :api="'getMailLogCompareData'"
    :api-dept="'getMailLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'MailLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '邮件',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'mailNumSumAll', label: 'mailNumSumAll' },
        { prop: 'mailSendNumSumAll', label: 'mailSendNumSumAll' },
        { prop: 'mailReceiveNumSumAll', label: 'mailReceiveNumSumAll' },
        { prop: 'fileSizeSumAll', label: 'fileSizeSumAll', flag: 'size' },
        { prop: 'attachMailCountSumAll', label: 'attachMailCountSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['mailNumSumAll', 'mailSendNumSumAll', 'mailReceiveNumSumAll', 'attachMailCountSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
