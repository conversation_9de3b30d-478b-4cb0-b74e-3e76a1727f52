<template>
  <Trend
    :api="'getMailTrend'"
    :col-model="colModel"
    :custom-list="customList"
    :drill-down="hasPermission('715')"
    :exportable="hasPermission('716')"
    :view-details="hasPermission('717')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Trend from '../../component/Trend'
import { getEmailNumDetailCol } from '@/utils/report'

export default {
  name: 'MailLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'mailNumSumAll', label: 'mailNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'mailSendNumSumAll', label: 'mailSendNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'mailReceiveNumSumAll', label: 'mailReceiveNumSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'fileSizeSumAll', label: 'mailSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'attachMailCountSumAll', label: 'attachMailCountSumAll', width: '150', sort: true, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad }
      ],
      customList: [
        { prop: 'mailNumSumAll', label: 'mailNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'mailSendNumSumAll', label: 'mailSendNumSumAll', width: '150', sort: true, value: 2, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'mailReceiveNumSumAll', label: 'mailReceiveNumSumAll', width: '150', sort: true, value: 4, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad },
        { prop: 'fileSizeSumAll', label: 'mailSizeSumAll', width: '150', sort: true, flag: 'size', sortOriginal: true, value: 8, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol() },
        { prop: 'attachMailCountSumAll', label: 'attachMailCountSumAll', width: '150', sort: true, value: 16, detailTable: 'dwd_mail_log', detailCol: this.getEmailNumDetailCol(), beforeLoadDetail: this.beforeLoad }
      ]
    }
  },
  methods: {
    beforeLoad(row, column, cell, event, detailQuery) {
      if (!detailQuery.hasOwnProperty('condition')) {
        detailQuery['condition'] = {}
      }
      if (column.property == 'mailSendNumSum' || column.property == 'mailSendNumSumAll') {
        detailQuery['condition']['cmd_type'] = [1, 13, 11, 14]
      }
      if (column.property == 'mailReceiveNumSum' || column.property == 'mailReceiveNumSumAll') {
        detailQuery['condition']['cmd_type'] = [2, 3, 12, 15]
      }
      if (column.property == 'attachMailCountSum' || column.property == 'attachMailCountSumAll') {
        detailQuery['condition']['attach_count'] = 1
      }
      return detailQuery;
    },
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    getEmailNumDetailCol() {
      return getEmailNumDetailCol(this.hasPermission('717'))
    }
  }
}
</script>
