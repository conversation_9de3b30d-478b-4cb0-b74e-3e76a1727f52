<template>
  <Trend :api="'getTelnetMonitorLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('795')" :exportable="hasPermission('796')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getCmdNumDetailCol } from '@/utils/report'

export default {
  name: 'TelnetMonitorTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'cmdDataNumSumAll', label: 'cmdDataNumSumAll', width: '150', sort: true, detailTable: 'dwd_telnet_monitor_log', detailCol: getCmdNumDetailCol() }
      ],
      customList: [
        { prop: 'cmdDataNumSumAll', label: 'cmdDataNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_telnet_monitor_log', detailCol: getCmdNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
