<template>
  <comparative-trend
    :api="'getTelnetMonitorLogCompareData'"
    :api-dept="'getTelnetMonitorLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'TelnetMonitorCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: 'Telnet监控',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'cmdDataNumSumAll', label: 'cmdDataNumSumAll' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['cmdDataNumSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
