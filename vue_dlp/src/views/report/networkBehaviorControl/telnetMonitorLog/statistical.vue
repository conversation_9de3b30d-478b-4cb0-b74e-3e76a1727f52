<template>
  <Statistical
    :api="'getTelnetMonitorLogPie'"
    :default-col-model="defaultColModel"
    :sub-col-model="subColModel"
    :custom-list="customList"
    :drill-down="hasPermission('790')"
    :exportable="hasPermission('791')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { getCmdNumDetailCol } from '@/utils/report'

export default {
  name: 'TelnetMonitorStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'cmdDataNumSumAll', label: 'cmdDataNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_telnet_monitor_log', detailCol: getCmdNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'cmdDataNumSum', label: 'cmdDataNumSumAll', width: '150', sort: true, value: 1, detailTable: 'dwd_telnet_monitor_log', detailCol: getCmdNumDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'cmdDataNumSumAll', label: 'cmdDataNumSumAll', width: '150', sort: true, detailTable: 'dwd_telnet_monitor_log', detailCol: getCmdNumDetailCol() }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'ipArr', label: 'ipAddr', width: '150', sort: true, joinSearch: true, searchCol: 'ip_arr' },
        { prop: 'cmdDataNumSum', label: 'cmdDataNumSumAll', width: '150', sort: true, detailTable: 'dwd_telnet_monitor_log', detailCol: getCmdNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
