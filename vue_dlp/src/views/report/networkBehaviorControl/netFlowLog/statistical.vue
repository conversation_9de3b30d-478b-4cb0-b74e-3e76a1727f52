<template>
  <Statistical
    :api="'getNetFlowLogBar'"
    :default-col-model="defaultColModel"
    :custom-list="customList"
    :conversion-flow="true"
    :drill-down="hasPermission('780')"
    :exportable="hasPermission('781')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { getNetFlowDetailCol } from '@/utils/report'

export default {
  name: 'NetFlowLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'totalFlowNumSumAll', label: 'totalFlow', width: '150', sort: true, sortOriginal: true, flag: 'size', value: 1, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() },
        { prop: 'receiveFlowNumSumAll', label: 'receivingFlow', width: '150', sort: true, sortOriginal: true, flag: 'size', value: 2, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() },
        { prop: 'sendFlowNumSumAll', label: 'sendingFlow', width: '150', sort: true, sortOriginal: true, flag: 'size', value: 4, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '150', sort: true },
        { prop: 'totalFlowNumSumAll', label: 'totalFlow', width: '150', flag: 'size', sort: true, sortOriginal: true, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() },
        { prop: 'receiveFlowNumSumAll', label: 'receivingFlow', width: '150', flag: 'size', sort: true, sortOriginal: true, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() },
        { prop: 'sendFlowNumSumAll', label: 'sendingFlow', width: '150', flag: 'size', sort: true, sortOriginal: true, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
    }
  }
}
</script>
