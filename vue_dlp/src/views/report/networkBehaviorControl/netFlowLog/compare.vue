<template>
  <comparative-trend
    :api="'getNetFlowLogCompareData'"
    :api-dept="'getNetFlowLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'NetFlowLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '流量',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'totalFlowNumSumAll', label: 'totalFlow', flag: 'size' },
        { prop: 'receiveFlowNumSumAll', label: 'receivingFlow', flag: 'size' },
        { prop: 'sendFlowNumSumAll', label: 'sendingFlow', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['totalFlowNumSumAll', 'receiveFlowNumSumAll', 'sendFlowNumSumAll']
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
