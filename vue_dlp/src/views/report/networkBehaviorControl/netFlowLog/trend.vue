<template>
  <Trend :api="'getNetFlowLogTrend'" :col-model="colModel" :custom-list="customList" :conversion-flow="true" :drill-down="hasPermission('785')" :exportable="hasPermission('786')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '../../component/Trend'
import { getNetFlowDetailCol } from '@/utils/report'

export default {
  name: 'NetFlowLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'totalFlowNumSumAll', label: 'totalFlow', width: '150', flag: 'size', sort: true, sortOriginal: true, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() },
        { prop: 'receiveFlowNumSumAll', label: 'receivingFlow', width: '150', flag: 'size', sort: true, sortOriginal: true, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() },
        { prop: 'sendFlowNumSumAll', label: 'sendingFlow', width: '150', flag: 'size', sort: true, sortOriginal: true, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() }
      ],
      customList: [
        { prop: 'totalFlowNumSumAll', label: 'totalFlow', width: '150', sort: true, sortOriginal: true, flag: 'size', value: 1, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() },
        { prop: 'receiveFlowNumSumAll', label: 'receivingFlow', width: '150', sort: true, sortOriginal: true, flag: 'size', value: 2, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() },
        { prop: 'sendFlowNumSumAll', label: 'sendingFlow', width: '150', sort: true, sortOriginal: true, flag: 'size', value: 4, detailTable: 'dwd_net_flow_log', detailCol: getNetFlowDetailCol() }
      ]

    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
