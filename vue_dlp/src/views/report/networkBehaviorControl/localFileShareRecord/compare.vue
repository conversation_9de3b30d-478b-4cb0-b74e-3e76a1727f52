<template>
  <comparative-trend
    :api="'getLocalFileShareRecordCompareData'"
    :api-dept="'getLocalFileShareRecordCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'LocalFileShareRecordCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '本地共享',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'localFileShareCountSum', label: 'localFileShareCountSum' },
        { prop: 'localFileShareSizeSum', label: 'localFileShareSizeSum', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['localFileShareCountSum']
    }
  }
}
</script>
