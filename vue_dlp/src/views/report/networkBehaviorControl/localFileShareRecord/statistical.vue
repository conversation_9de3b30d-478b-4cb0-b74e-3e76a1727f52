<template>
  <Statistical :api="'getLocalFileShareRecordPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('593')" :exportable="hasPermission('594')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getLocalFileShareNumDetailCol } from '@/utils/report'

export default {
  name: 'LocalFileShareRecordStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'localFileShareCountSum', label: 'localFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() },
        { prop: 'localFileShareSizeSum', label: 'localFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'localFileShareCount', label: 'localFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() },
        { prop: 'localFileShareSize', label: 'localFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'localFileShareCountSum', label: 'localFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() },
        { prop: 'localFileShareSizeSum', label: 'localFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'fileOpTypeName', label: 'operateType', width: '150', sort: true, joinSearch: true, searchCol: 'file_op_type', searchProp: 'fileOpType' },
        { prop: 'localFileShareCount', label: 'localFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() },
        { prop: 'localFileShareSize', label: 'localFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
