<template>
  <Trend :api="'getLocalFileShareRecordTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('596')" :exportable="hasPermission('597')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getLocalFileShareNumDetailCol } from '@/utils/report'

export default {
  name: 'LocalFileShareRecordTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'localFileShareCountSum', label: 'localFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() },
        { prop: 'localFileShareSizeSum', label: 'localFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() }
      ],
      customList: [
        { prop: 'localFileShareCountSum', label: 'localFileShareCountSum', width: '150', sort: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() },
        { prop: 'localFileShareSizeSum', label: 'localFileShareSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_local_file_share_record', detailCol: getLocalFileShareNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
