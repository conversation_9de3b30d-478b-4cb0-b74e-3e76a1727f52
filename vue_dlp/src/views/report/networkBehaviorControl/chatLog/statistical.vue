<template>
  <Statistical
    :api="'getChatPie'"
    :default-col-model="defaultColModel"
    :sub-col-model="subColModel"
    :custom-list="customList"
    :drill-down="hasPermission('740')"
    :exportable="hasPermission('741')"
    :view-details="hasPermission('742')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Statistical from '../../component/Statistical'
import { getChatLogDetail, getChatImageLogDetail, getChatFileLogDetail, getAllChatLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getAllChatLogDetailCol, getChatLogDetailCol, getChatImageLogDetailCol, getChatFileLogDetailCol, getChatLoginLogDetailCol } from '@/utils/report'

export default {
  name: 'ChatLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'chatRecordNumSumAll', label: 'chatRecordNumSumAll', width: '120', sort: true, value: 1, detailCol: this.getAllChatLogDetailCol(), getDetailMethod: getAllChatLogDetail },
        { prop: 'loginNumSumAll', label: 'loginNumSumAll', width: '120', sort: true, value: 2, detailTable: 'dwd_chat_login_log', detailCol: getChatLoginLogDetailCol() },
        { prop: 'sentenceNumSumAll', label: 'sentenceNumSumAll', width: '100', sort: true, value: 4, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'charNumSumAll', label: 'charNumSumAll', width: '100', sort: true, value: 8, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'imageNumSumAll', label: 'imageNumSumAll', width: '100', sort: true, value: 16, detailCol: this.getChatImageLogDetailCol(), getDetailMethod: getChatImageLogDetail },
        { prop: 'fileNumSumAll', label: 'fileNumSumAll', width: '100', sort: true, value: 32, detailCol: this.getChatFileLogDetailCol(), getDetailMethod: getChatFileLogDetail },
        { prop: 'fileSizeSumAll', label: 'fileSizeSumAll', width: '120', sort: true, flag: 'size', sortOriginal: true, value: 64, detailCol: this.getChatFileLogDetailCol(), getDetailMethod: getChatFileLogDetail }
      ],
      subCustomList: [
        { prop: 'chatRecordNumSum', label: 'chatRecordNumSumAll', width: '120', sort: true, value: 1, detailCol: this.getAllChatLogDetailCol(), getDetailMethod: getAllChatLogDetail },
        { prop: 'loginNumSum', label: 'loginNumSumAll', width: '120', sort: true, value: 2, detailTable: 'dwd_chat_login_log', detailCol: getChatLoginLogDetailCol() },
        { prop: 'sentenceNumSum', label: 'sentenceNumSumAll', width: '100', sort: true, value: 4, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'charNumSum', label: 'charNumSumAll', width: '100', sort: true, value: 8, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'imageNumSum', label: 'imageNumSumAll', width: '100', sort: true, value: 16, detailCol: this.getChatImageLogDetailCol(), getDetailMethod: getChatImageLogDetail },
        { prop: 'fileNumSum', label: 'fileNumSumAll', width: '100', sort: true, value: 32, detailCol: this.getChatFileLogDetailCol(), getDetailMethod: getChatFileLogDetail },
        { prop: 'fileSizeSum', label: 'fileSizeSumAll', width: '120', sort: true, flag: 'size', sortOriginal: true, value: 64, detailCol: this.getChatFileLogDetailCol(), getDetailMethod: getChatFileLogDetail }
      ],
      defaultColModel: [ // 表头数据
        { prop: 'label', label: 'user', width: '100', sort: true },
        { prop: 'chatRecordNumSumAll', label: 'chatRecordNumSumAll', width: '120', sort: true, detailCol: this.getAllChatLogDetailCol(), getDetailMethod: getAllChatLogDetail },
        { prop: 'loginNumSumAll', label: 'loginNumSumAll', width: '120', sort: true, detailTable: 'dwd_chat_login_log', detailCol: getChatLoginLogDetailCol() },
        { prop: 'sentenceNumSumAll', label: 'sentenceNumSumAll', width: '100', sort: true, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'charNumSumAll', label: 'charNumSumAll', width: '100', sort: true, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'imageNumSumAll', label: 'imageNumSumAll', width: '100', sort: true, detailCol: this.getChatImageLogDetailCol(), getDetailMethod: getChatImageLogDetail },
        { prop: 'fileNumSumAll', label: 'fileNumSumAll', width: '100', sort: true, detailCol: this.getChatFileLogDetailCol(), getDetailMethod: getChatFileLogDetail }
      ],
      subColModel: [ // 钻取表头数据
        { prop: 'label', label: 'user', width: '100', sort: true },
        { prop: 'chatTypeName', label: 'chatTypeName', width: '130', sort: true, joinSearch: true, searchCol: 'chat_type', searchProp: 'chatType' },
        { prop: 'chatRecordNumSum', label: 'chatRecordNumSumAll', width: '120', sort: true, detailCol: this.getAllChatLogDetailCol(), getDetailMethod: getAllChatLogDetail },
        { prop: 'loginNumSum', label: 'loginNumSumAll', width: '120', sort: true, detailTable: 'dwd_chat_login_log', detailCol: getChatLoginLogDetailCol() },
        { prop: 'sentenceNumSum', label: 'sentenceNumSumAll', width: '100', sort: true, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'charNumSum', label: 'charNumSumAll', width: '100', sort: true, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'imageNumSum', label: 'imageNumSumAll', width: '100', sort: true, detailCol: this.getChatImageLogDetailCol(), getDetailMethod: getChatImageLogDetail },
        { prop: 'fileNumSum', label: 'fileNumSumAll', width: '100', sort: true, detailCol: this.getChatFileLogDetailCol(), getDetailMethod: getChatFileLogDetail }
      ]
    }
  },
  methods: {
    handleCustomColumn(val, groupType) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    },
    getAllChatLogDetailCol() {
      return getAllChatLogDetailCol(this.viewDetails())
    },
    getChatLogDetailCol() {
      return getChatLogDetailCol(this.viewDetails())
    },
    getChatImageLogDetailCol() {
      return getChatImageLogDetailCol(this.viewDetails())
    },
    getChatFileLogDetailCol() {
      return getChatFileLogDetailCol(this.viewDetails())
    },
    viewDetails() {
      return this.hasPermission('742')
    }
  }
}
</script>
