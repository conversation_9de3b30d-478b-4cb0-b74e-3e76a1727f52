<template>
  <comparative-trend
    :api="'getChatLogCompareData'"
    :api-dept="'getChatLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend';
export default {
  name: 'ChatLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: '即时通讯',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'chatRecordNumSumAll', label: 'chatRecordNumSumAll' },
        { prop: 'loginNumSumAll', label: 'loginNumSumAll' },
        { prop: 'sentenceNumSumAll', label: 'sentenceNumSumAll' },
        { prop: 'charNumSumAll', label: 'charNumSumAll' },
        { prop: 'imageNumSumAll', label: 'imageNumSumAll' },
        { prop: 'fileNumSumAll', label: 'fileNumSumAll' },
        { prop: 'fileSizeSumAll', label: 'fileSizeSumAll', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['chatRecordNumSumAll', 'loginNumSumAll', 'sentenceNumSumAll', 'charNumSumAll', 'imageNumSumAll', 'fileNumSumAll']
    }
  },
  computed: {

  },
  watch: {
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>

</style>
