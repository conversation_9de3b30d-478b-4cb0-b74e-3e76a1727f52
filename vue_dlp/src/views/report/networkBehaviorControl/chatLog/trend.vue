<template>
  <Trend
    :api="'getChatTrend'"
    :col-model="colModel"
    :custom-list="customList"
    :drill-down="hasPermission('745')"
    :exportable="hasPermission('746')"
    :view-details="hasPermission('747')"
    @handleSelect="handleCustomColumn"
  />
</template>

<script>
import Trend from '../../component/Trend'
import { getChatLogDetail, getChatImageLogDetail, getChatFileLogDetail, getAllChatLogDetail } from '@/api/report/baseReport/issueReport/detail'
import { getAllChatLogDetailCol, getChatLogDetailCol, getChatImageLogDetailCol, getChatFileLogDetailCol, getChatLoginLogDetailCol } from '@/utils/report'

export default {
  name: 'ChatLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'chatRecordNumSumAll', label: 'chatRecordNumSumAll', width: '120', sort: true, detailCol: this.getAllChatLogDetailCol(), getDetailMethod: getAllChatLogDetail },
        { prop: 'loginNumSumAll', label: 'loginNumSumAll', width: '120', sort: true, detailTable: 'dwd_chat_login_log', detailCol: getChatLoginLogDetailCol() },
        { prop: 'sentenceNumSumAll', label: 'sentenceNumSumAll', width: '100', sort: true, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'charNumSumAll', label: 'charNumSumAll', width: '100', sort: true, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'imageNumSumAll', label: 'imageNumSumAll', width: '100', sort: true, detailCol: this.getChatImageLogDetailCol(), getDetailMethod: getChatImageLogDetail },
        { prop: 'fileNumSumAll', label: 'fileNumSumAll', width: '100', sort: true, detailCol: this.getChatFileLogDetailCol(), getDetailMethod: getChatFileLogDetail }
      ],
      customList: [
        { prop: 'chatRecordNumSumAll', label: 'chatRecordNumSumAll', width: '120', sort: true, value: 1, detailCol: this.getAllChatLogDetailCol(), getDetailMethod: getAllChatLogDetail },
        { prop: 'loginNumSumAll', label: 'loginNumSumAll', width: '120', sort: true, value: 2, detailTable: 'dwd_chat_login_log', detailCol: getChatLoginLogDetailCol() },
        { prop: 'sentenceNumSumAll', label: 'sentenceNumSumAll', width: '100', sort: true, value: 4, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'charNumSumAll', label: 'charNumSumAll', width: '100', sort: true, value: 8, detailCol: this.getChatLogDetailCol(), getDetailMethod: getChatLogDetail },
        { prop: 'imageNumSumAll', label: 'imageNumSumAll', width: '100', sort: true, value: 16, detailCol: this.getChatImageLogDetailCol(), getDetailMethod: getChatImageLogDetail },
        { prop: 'fileNumSumAll', label: 'fileNumSumAll', width: '100', sort: true, value: 32, detailCol: this.getChatFileLogDetailCol(), getDetailMethod: getChatFileLogDetail },
        { prop: 'fileSizeSumAll', label: 'fileSizeSumAll', width: '120', sort: true, flag: 'size', sortOriginal: true, value: 64, detailCol: this.getChatFileLogDetailCol(), getDetailMethod: getChatFileLogDetail }
      ]

    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    },
    getAllChatLogDetailCol() {
      return getAllChatLogDetailCol(this.viewDetails())
    },
    getChatLogDetailCol() {
      return getChatLogDetailCol(this.viewDetails())
    },
    getChatImageLogDetailCol() {
      return getChatImageLogDetailCol(this.viewDetails())
    },
    getChatFileLogDetailCol() {
      return getChatFileLogDetailCol(this.viewDetails())
    },
    viewDetails() {
      return this.hasPermission('747')
    }
  }
}
</script>
