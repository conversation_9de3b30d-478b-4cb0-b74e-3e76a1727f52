<template>
  <Trend :api="'getFtpMonitorLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('572')" :exportable="hasPermission('573')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getFtpFileTransferNumDetailCol } from '@/utils/report'

export default {
  name: 'FtpMonitorLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'ftpFileTransferCountSum', label: 'ftpFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() },
        { prop: 'ftpFileTransferSizeSum', label: 'ftpFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() }
      ],
      customList: [
        { prop: 'ftpFileTransferCountSum', label: 'ftpFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() },
        { prop: 'ftpFileTransferSizeSum', label: 'ftpFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
