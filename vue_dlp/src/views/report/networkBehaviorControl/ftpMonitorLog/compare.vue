<template>
  <comparative-trend
    :api="'getFtpMonitorLogCompareData'"
    :api-dept="'getFtpMonitorLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'FtpMonitorLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: 'FTP监控',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'ftpFileTransferCountSum', label: 'ftpFileTransferCountSum' },
        { prop: 'ftpFileTransferSizeSum', label: 'ftpFileTransferSizeSum', flag: 'size' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['ftpFileTransferCountSum']
    }
  }
}
</script>
