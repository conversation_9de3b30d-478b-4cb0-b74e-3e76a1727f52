<template>
  <Statistical :api="'getFtpMonitorLogPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('569')" :exportable="hasPermission('570')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getFtpFileTransferNumDetailCol } from '@/utils/report'

export default {
  name: 'FtpMonitorLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'ftpFileTransferCountSum', label: 'ftpFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() },
        { prop: 'ftpFileTransferSizeSum', label: 'ftpFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'ftpFileTransferCount', label: 'ftpFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() },
        { prop: 'ftpFileTransferSize', label: 'ftpFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'ftpFileTransferCountSum', label: 'ftpFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() },
        { prop: 'ftpFileTransferSizeSum', label: 'ftpFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'cmdTypeName', label: 'terminalOperateType', width: '150', sort: true, joinSearch: true, searchCol: 'cmd_type', searchProp: 'cmdType' },
        { prop: 'ftpFileTransferCount', label: 'ftpFileTransferCountSum', width: '150', sort: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() },
        { prop: 'ftpFileTransferSize', label: 'ftpFileTransferSizeSum', width: '150', sort: true, flag: 'size', sortOriginal: true, detailTable: 'dwd_ftp_monitor_log', detailCol: getFtpFileTransferNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
