<template>
  <comparative-trend
    :api="'getWifiAlarmLogCompareData'"
    :api-dept="'getWifiAlarmLogCompareDeptData'"
    :chart-title="chartTitle"
    :statistical-list="statisticalList"
    :statistical-list-check="statisticalListCheck"
  />
</template>

<script>
import ComparativeTrend from '@/views/report/component/ComparativeTrend'
export default {
  name: 'WifiAlarmLogCompare',
  components: { ComparativeTrend },
  data() {
    return {
      // echarts图表标题
      chartTitle: 'WiFi连接',
      // 统计项设置 复选框传值
      statisticalList: [
        { prop: 'wifiConnRecordCountSum', label: 'wifiConnRecordCountSum' }
      ],
      // 统计项设置 默认选中
      statisticalListCheck: ['wifiConnRecordCountSum']
    }
  }
}
</script>
