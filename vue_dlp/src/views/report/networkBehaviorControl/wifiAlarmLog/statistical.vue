<template>
  <Statistical :api="'getWifiAlarmLogPie'" :default-col-model="defaultColModel" :sub-col-model="subColModel" :custom-list="customList" :drill-down="hasPermission('587')" :exportable="hasPermission('588')" :prop-suffix="'Sum'" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Statistical from '@/views/report/component/Statistical'
import { getWifiConnNumDetailCol } from '@/utils/report'

export default {
  name: 'WifiAlarmLogStatistical',
  components: { Statistical },
  data() {
    return {
      customList: [
        { prop: 'wifiConnRecordCountSum', label: 'wifiConnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_wifi_alarm_log', detailCol: getWifiConnNumDetailCol() }
      ],
      subCustomList: [
        { prop: 'wifiConnRecordCount', label: 'wifiConnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_wifi_alarm_log', detailCol: getWifiConnNumDetailCol() }
      ],
      defaultColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'wifiConnRecordCountSum', label: 'wifiConnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_wifi_alarm_log', detailCol: getWifiConnNumDetailCol() }
      ],
      subColModel: [
        { prop: 'label', label: 'user', width: '140', sort: true },
        { prop: 'wifiName', label: 'wifiName', width: '150', sort: true, joinSearch: true, searchCol: 'wifi_name', searchProp: 'wifiName' },
        { prop: 'wifiConnRecordCount', label: 'wifiConnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_wifi_alarm_log', detailCol: getWifiConnNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      const subArr = arr.map(item => item.substring(0, item.length - 3))
      const subCustomList = this.subCustomList.filter(item => {
        return subArr.indexOf(item.prop) !== -1
      })
      this.defaultColModel = [this.defaultColModel[0], ...customList]
      this.subColModel = [this.subColModel[0], this.subColModel[1], ...subCustomList]
    }
  }
}
</script>
