<template>
  <Trend :api="'getWifiAlarmLogTrend'" :col-model="colModel" :custom-list="customList" :drill-down="hasPermission('590')" :exportable="hasPermission('591')" @handleSelect="handleCustomColumn"/>
</template>

<script>
import Trend from '@/views/report/component/Trend'
import { getWifiConnNumDetailCol } from '@/utils/report'

export default {
  name: 'WifiAlarmLogTrend',
  components: { Trend },
  data() {
    return {
      dimBaseType: this.$t('text.month'),
      colModel: [
        { prop: 'labelValue', label: 'month', width: '150', sort: true, sortOriginal: true },
        { prop: 'wifiConnRecordCountSum', label: 'wifiConnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_wifi_alarm_log', detailCol: getWifiConnNumDetailCol() }
      ],
      customList: [
        { prop: 'wifiConnRecordCountSum', label: 'wifiConnRecordCountSum', width: '150', sort: true, detailTable: 'dwd_wifi_alarm_log', detailCol: getWifiConnNumDetailCol() }
      ]
    }
  },
  methods: {
    handleCustomColumn(val) {
      const arr = val.map(item => item.prop)
      const customList = this.customList.filter(item => {
        return arr.indexOf(item.prop) !== -1
      })
      this.colModel = [this.colModel[0], ...customList]
    }
  }
}
</script>
