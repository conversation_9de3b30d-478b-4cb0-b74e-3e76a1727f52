<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <TimeQuery @getTimeParams="getTimeParams"/>
        <el-select v-model="query.statusVo" clearable>
          <el-option :value="1" :label="$t('pages.sendSuccess')"></el-option>
          <el-option :value="0" :label="$t('pages.sendFail')"></el-option>
        </el-select>
        <el-input v-model="query.searchInfo" clearable :placeholder="$t('pages.softwareTask_Validate4')" style="width: 200px" @keyup.enter.native="handleFilter" />
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
          {{ $t('table.search') }}
        </el-button>
      </div>
      <grid-table
        ref="pushingRecordList"
        :col-model="colModel"
        :multi-select="false"
        :row-data-api="rowDataApi"
      />
    </div>
  </div>
</template>

<script>
import { logMsgPush } from '@/api/report/baseReport/operationalAnalysis/sendConfig';

export default {
  name: 'TemplateRecord',
  components: { },
  data() {
    return {
      query: {
        page: 1,
        bizCode: 1002,
        contentType: 1,
        searchInfo: '',
        statusVo: undefined,
        pushObject: undefined,
        status: undefined
      },
      colModel: [
        { prop: 'createTime', label: 'pushTime', width: '120', sort: true },
        { prop: 'templateTitle', label: 'taskName2', width: '150', formatter: this.taskNameFormatter },
        { prop: 'countByObject', label: 'msgPushContent', width: '350', formatter: this.countByObjectFormatter },
        { prop: 'status', label: 'pushStatus', formatter: this.statusFormatter },
        { prop: 'receiver', label: 'pushObject', width: '150' }
      ]
    }
  },
  computed: {

  },
  watch: {
    // 监听下拉框变化，如果清空，赋值undefined
    'query.statusVo'(newValue) {
      // console.log('newValue', newValue)
      if (newValue === '') {
        this.query.statusVo = undefined;
      }
    }
  },
  created() {

  },
  methods: {
    getTimeParams(timeObj) {
      this.query = Object.assign(this.query, timeObj)
    },
    /**
     * 列表接口获取
     * */
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return logMsgPush(searchQuery)
    },
    /**
     * 查询
     * */
    handleFilter() {
      this.query.page = 1
      this.$refs.pushingRecordList.execRowDataApi(this.query)
    },
    /**
     * 任务名称
     * @param row
     */
    taskNameFormatter(row) {
      const taskInfo = JSON.parse(row.taskInfo)
      return taskInfo.taskName
    },
    /**
     * 推送内容
     * @param row
     */
    countByObjectFormatter(row) {
      const taskJson = JSON.parse(row.taskJson)
      const taskInfo = JSON.parse(row.taskInfo)
      const type = this.$t('pages.countByObject') + ':' + this.$t('pages.accordingTo') + taskInfo.countByObject + this.$t('pages.statistical') + '；'
      // const type = '统计类型：按' + taskInfo.countByObject + '统计；'
      let top = ''
      let range = ''
      if (taskJson.param) {
        top = 'Top：' + taskJson.param.request.top + '；'
      }
      if (taskJson.param && taskJson.param.response.groupName) {
        range = this.$t('pages.reportScope') + '：' + taskJson.param.response.groupName + '。'
      }
      const tempName = this.$t('pages.templateType') + '：' + taskInfo.templateName + '；'
      const date = this.$t('pages.templateDate') + '：' + taskInfo.reportDate + '；'
      return type + top + tempName + date + range
    },
    /**
     * 推送状态
     * @param row
     */
    statusFormatter(row) {
      if (row.status == '200') {
        return this.$t('text.success')
      } else {
        if (row.statusJson) {
          const statusJson = JSON.parse(row.statusJson)
          return this.$t('text.fail') + '：' + statusJson.msg
        } else {
          return this.$t('text.fail')
        }
      }
    }
  }
}
</script>
