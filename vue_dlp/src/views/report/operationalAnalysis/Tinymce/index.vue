<template>
  <div :class="{ fullscreen: fullscreen }" class="tinymce-container" :style="{ width: containerWidth }">
    <textarea :id="tinymceId" class="tinymce-textarea" />
  </div>
</template>

<script>
/**
   * docs:
   * https://panjiachen.github.io/vue-element-admin-site/feature/component/rich-editor.html#tinymce
   */
import plugins from './plugins'
import toolbar from './toolbar'
import load from './dynamicLoadScript'
import { mapGetters } from 'vuex';

// const tinymceCDN = 'https://fastly.jsdelivr.net/npm/tinymce-all-in-one@4.9.3/tinymce.min.js''
const tinymceCDN = '/tinymce/tinymce.min.js'

export default {
  name: 'Tinymce',
  props: {
    id: {
      type: String,
      default: function() {
        return 'vue-tinymce-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '')
      }
    },
    value: {
      type: String,
      default: ''
    },
    toolbar: {
      type: Array,
      required: false,
      default() {
        return []
      }
    },
    menubar: {
      type: String,
      default: 'file edit insert view format table'
    },
    dragHtml: {
      type: String,
      default: ''
    },
    height: {
      type: [Number, String],
      required: false,
      default: 360
    },
    width: {
      type: [Number, String],
      required: false,
      default: 'auto'
    }
  },
  data() {
    return {
      // bodyStyle: 'body { background-color: #0c161f;color:#bbbbbb }',
      hasChange: false,
      hasInit: false,
      tinymceId: this.id,
      fullscreen: false,
      languageTypeList: {
        en: 'en',
        zh: 'zh_CN',
        es: 'es_MX',
        ja: 'ja'
      }
    }
  },
  computed: {
    ...mapGetters([
      'theme'
    ]),
    language() {
      return this.languageTypeList['zh']
    },
    containerWidth() {
      const width = this.width
      if (/^[\d]+(\.[\d]+)?$/.test(width)) {
        // matches `100`, `'100'`
        return `${width}px`
      }
      return width
    }
  },
  watch: {
    /**
     * 主题切换时，修改编辑器颜色(注释掉了，因为后台说导出word背景一般是白色的)
     * @param val
     */
    theme(val) {
      // if (val === 'default') {
      //   this.bodyStyle = 'body { background-color: #0c161f;color:#bbbbbb }'
      // } else {
      //   this.bodyStyle = 'body { background-color: #f5f5f5;color:#666 }'
      // }
      // 先移除富文本编辑器
      window.tinymce.remove()
      // 重新初始化，否则样式修改不生效
      this.initTinymce()
    },
    value(val) {
      // console.log('val', val)
      if (!this.hasChange && this.hasInit) {
        this.$nextTick(() => window.tinymce.get(this.tinymceId).setContent(val || ''))
      }
    },
    language() {
      // this.destroyTinymce()
      this.$nextTick(() => this.initTinymce())
    }
  },
  mounted() {
    this.init()
  },
  activated() {
    if (window.tinymce) {
      this.initTinymce()
    }
  },
  deactivated() {
    this.destroyTinymce()
  },
  destroyed() {
    this.destroyTinymce()
  },
  methods: {
    init() {
      // dynamic load tinymce from cdn
      load(tinymceCDN, (err) => {
        if (err) {
          this.$message({
            message: err.message,
            type: 'error',
            duration: 2000
          })
          return
        }
        this.initTinymce()
      })
      // 初始化编辑器颜色
      // if (this.theme === 'default') {
      //   this.bodyStyle = 'body { background-color: #0c161f;color:#bbbbbb }'
      // } else {
      //   this.bodyStyle = 'body { background-color: #f5f5f5;color:#666 }'
      // }
    },
    initTinymce() {
      const _this = this
      window.tinymce.init({
        language: this.language,
        selector: `#${this.tinymceId}`,
        language_url: '/tinymce/langs/zh_CN.js',
        height: this.height,
        body_class: 'panel-body',
        // content_style: _this.bodyStyle,
        content_editable: false,  // 设置为false禁止拖拽
        // extended_valid_elements: 'img[class|src|border=0|alt|title|width|height|align|onmouseover|onmouseout|style]',
        // content_style: 'body { background-color: #0c161f;color:#bbbbbb }',
        branding: false,
        object_resizing: false,
        // 工具栏配置
        toolbar: this.toolbar.length > 0 ? this.toolbar : toolbar,
        menubar: this.menubar,
        plugins: plugins,
        toolbar_drawer: true,
        end_container_on_empty_block: true,
        powerpaste_word_import: 'clean',
        paste_data_images: true, // 允许粘贴base64图片
        paste_enable_default_filters: false, // word文本设置
        code_dialog_height: 450,
        code_dialog_width: 1000,
        advlist_bullet_styles: 'default,circle,disc,square',
        // advlist_number_styles: 'default',
        imagetools_cors_hosts: ['www.tinymce.com', 'codepen.io'],
        default_link_target: '_blank',
        link_title: true,
        fontsize_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
        font_formats:
            '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
        nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin
        statusbar: false,
        init_instance_callback: (editor) => {
          // console.log('init_instance_callback', editor)
          if (_this.value) {
            editor.setContent(_this.value)
          }
          _this.hasInit = true
          editor.on('NodeChange Change KeyUp SetContent', () => {
            this.hasChange = true
            this.$emit('input', editor.getContent())
          })
        },
        setup(editor) {
          editor.on('FullscreenStateChanged', (e) => {
            _this.fullscreen = e.state
          })
          /**
           * 拖拽结束，将父组件传过来的拖拽文本插入的富文本编辑器中
           * 处理放置：如果是echarts图表元素，拖拽过来的时候检测光标位置，如果嵌套在前一个echarts中，则跳出该div，在该div后创建光标，
           * 放置新的拖拽内容
           */
          editor.on('drop', function(event) {
            // console.log('drop1111111', _this.dragHtml);
            event.stopPropagation();
            event.preventDefault();
            const draggedContent = _this.dragHtml
            const rng = editor.selection.getRng();
            let node = rng.startContainer;
            // 如果node是文本节点，则使用其父元素
            if (node.nodeType === Node.TEXT_NODE) {
              node = node.parentElement;
            }
            // 找到最近的‘dragImageAllDiv’祖先
            let allDivAncestor = null;
            while (node && !allDivAncestor) {
              // console.log('node.classList', node.classList)
              // 拖拽时判断，是否上方是图标，普通表格，对比表格的排序项内容
              if (node.classList && (node.classList.contains('dragImageAllDiv') || node.classList.contains('sortNameParameter') || node.classList.contains('compareMark'))) {
                allDivAncestor = node;
              }
              node = node.parentElement;
            }
            if (allDivAncestor) {
              // 插入‘dragImageAllDiv’之后
              const newNode = document.createElement('div');
              newNode.innerHTML = draggedContent;
              const allDivRange = editor.dom.createRng();
              allDivRange.setStartAfter(allDivAncestor);
              allDivRange.setEndAfter(allDivAncestor);
              editor.selection.setRng(allDivRange);
              editor.insertContent(newNode.innerHTML);
            } else {
              // 直接插入到当前位置
              editor.insertContent(draggedContent);
            }
          })
          /**
           * 监听外部元素的dragover事件，以允许拖放
           * 如果只写上面的拖拽方法，鼠标在编辑器中任意选中内容拖拽后，从外部拖拽到富文本中的功能会失效，所以必须添加
           */
          editor.on('dragover', function(event) {
            event.preventDefault(); // 阻止默认行为
            event.stopPropagation();
          });
        },
        convert_urls: false,
        images_upload_handler(blobInfo, success, failure, progress) {
          const img = `data:${blobInfo.blob().type};base64,${blobInfo.base64()}`
          success(img)
        }
      })
    },
    destroyTinymce() {
      const tinymce = window.tinymce.get(this.tinymceId)
      if (this.fullscreen) {
        tinymce.execCommand('mceFullScreen')
      }

      if (tinymce) {
        tinymce.destroy()
      }
    },
    setContent(value) {
      window.tinymce.get(this.tinymceId).setContent(value)
    },
    getContent() {
      window.tinymce.get(this.tinymceId).getContent()
    },
    imageSuccessCBK(arr) {
      arr.forEach((v) => window.tinymce.get(this.tinymceId).insertContent(`<img class="wscnph" src="${v.url}" >`))
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.mce-panel{
    border: 0 solid #2674b2;
    background-color: transparent;
  }
  >>>.mce-btn{
    background: transparent;
  }
  >>>.mce-ico{
    color: #bbbbbb;
  }
  >>>i.mce-i-backcolor{
    background: #f5f5f5;
  }
  >>>.mce-listbox button{
    border: 1px solid #bbbbbb;
    color: #bbbbbb;
  }
  >>>.mce-menubar{
    border: 0;
  }
  /*初始化时页面上会有一闪而过的textarea*/
  .tinymce-textarea{
    background: transparent;
    border: 0;
    overflow: hidden;
    color: transparent;
  }
  .tinymce-container{
    textarea::-webkit-resizer {
      display: none;
    }
  }
</style>

