<template>
  <div
    v-loading="loading"
    class="app-container"
    :element-loading-text="$t('pages.foundationForm_Msg35')"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.6)"
  >
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.name" v-trim clearable :placeholder="$t('pages.softwareTask_Validate4')" style="width: 200px;" @keyup.enter.native="handleFilter"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="messageTaskList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <edit ref="edit" :dialog-status="dialogStatus" :dialog-title="dialogTitle"/>
  </div>
</template>

<script>
import Edit from '@/views/report/operationalAnalysis/sendConfig/edit';
import { msgPushGetPage, msgPushDelTask, msgPushSendMsg } from '@/api/report/baseReport/operationalAnalysis/sendConfig';
import { getTemplateList } from '@/api/report/baseReport/operationalAnalysis/templateConfig';
import { getPushTypeDict, receiveObjectFormatter } from '@/utils/messagePush';
import i18n from '@/lang';

export default {
  name: 'SendConfig',
  components: { Edit },
  data() {
    return {
      loading: false,
      deleteable: false,
      dialogStatus: '',
      dialogTitle: '',
      tempNameList: [],
      colModel: [
        { prop: 'name', label: 'taskName', width: '130', iconFormatter: this.taskEnableIconFormatter },
        { prop: 'templateId', label: 'templateName', width: '150', formatter: this.tempNameFormatter },
        { prop: 'countByObject', label: 'countByObject', width: '150', formatter: this.countByObjectFormatter },
        { prop: 'pushType', label: 'pushObject', width: '150', formatter: this.receiveObjectFormatter },
        { prop: 'executionRules', label: 'pushTime', width: '150', formatter: this.timeRuleFormatter },
        { prop: 'remark', label: 'remark', width: '120' },
        {
          label: 'operate', type: 'button', width: '120', fixed: 'right',
          buttons: [
            { label: 'edit', isShow: this.disabledEdit, click: this.handleUpdate },
            { label: 'view', isShow: this.disabledView, click: this.handleView },
            { label: 'manualPush', click: this.handleManualPush }
          ]
        }
      ],
      query: {
        name: '',
        dataType: 2
      },
      pushTypeDict: undefined
    }
  },
  computed: {
    gridTable() {
      return this.$refs['messageTaskList']
    }
  },
  watch: {

  },
  created() {
    this.getTemplateList()
    this.pushTypeDict = getPushTypeDict()
  },
  methods: {
    /**
     * 是否显示修改按钮
     * */
    disabledEdit(row) {
      if ((row.sysUserAccount === this.$store.getters.userId) || (row.sysUserAccount === undefined && this.$store.getters.userId === 1)) {
        return true
      } else {
        return false
      }
    },
    /**
     * 是否显示查看按钮
     * */
    disabledView(row) {
      if ((row.sysUserAccount === this.$store.getters.userId) || (row.sysUserAccount === undefined && this.$store.getters.userId === 1)) {
        return false
      } else {
        return true
      }
    },
    /**
     * 获取模板名称
     * */
    getTemplateList() {
      const query = { searchInfo: '', page: 1, sortName: 'id', sortOrder: 'desc' }
      getTemplateList(query).then(res => {
        // console.log('推送配置列表名称显示', JSON.parse(JSON.stringify(res)))
        this.tempNameList = res.data.items
      })
    },
    rowDataApi(option) {
      const searchData = Object.assign({}, this.query, option)
      return msgPushGetPage(searchData)
    },
    /**
     * 新增
     */
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogTitle = this.$t('text.add')
      this.$refs['edit'].handleCreateEditShow()
    },
    /**
     * 修改
     * @param row
     */
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.dialogTitle = this.$t('text.edit')
      this.$refs['edit'].handleUpdateEditShow(row)
    },
    /**
     * 查看
     * */
    handleView(row) {
      this.dialogStatus = 'view'
      this.dialogTitle = this.$t('table.view')
      this.$refs['edit'].handleUpdateEditShow(row)
    },
    /**
     * 立即推送
     * */
    handleManualPush(row) {
      this.loading = true
      // console.log('row', JSON.parse(JSON.stringify(row)))
      msgPushSendMsg(row).then(res => {
        this.loading = false
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.foundationForm_Msg36'),
          type: 'success',
          duration: 2000
        })
      }).catch(res => {
        this.loading = false
      })
    },
    /**
     * 删除
     */
    handleDelete() {
      const taskIds = this.gridTable.getSelectedKeys().join(',')
      // console.log('taskIds', taskIds)
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt'))
        .then(() => {
          msgPushDelTask({ ids: taskIds }).then(res => {
            this.gridTable.deleteRowData(taskIds)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }).catch(() => {})
    },
    /**
     * 刷新表
     * */
    refreshTab() {
      this.getTemplateList()
      this.gridTable.execRowDataApi()
    },
    /**
     * 查询
     */
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    /**
     * 统计类型(列表显示)
     * */
    countByObjectFormatter(row) {
      if (row.reportConfig.countByObject === 1) {
        return this.$t('pages.conutType1')
      }
      if (row.reportConfig.countByObject === 2) {
        return this.$t('pages.conutType2')
      }
    },
    receiveObjectFormatter(row, data) {
      const pushObjMap = {}
      row.receiveObject.forEach(t => {
        if (!pushObjMap[t.pushType]) {
          pushObjMap[t.pushType] = []
        }
        pushObjMap[t.pushType].push(t.receiver)
      })
      const result = []
      for (const key in pushObjMap) {
        const keyLabel = this.pushTypeDict[key]
        let receivers = pushObjMap[key].toString()
        if (key == 2) {
          receivers = receiveObjectFormatter(null, receivers, this.receiveObjectTreeData)
        }
        result.push(keyLabel + '：' + receivers)
      }
      return result.toString()
    },
    /**
     * 推送时间(列表显示)
     * */
    timeRuleFormatter(row, data) {
      const dimBaseType = row.reportConfig.dimBaseType
      data = JSON.parse(data)
      const day = data.day
      let str = this.changeDimBaseType(dimBaseType)
      if (dimBaseType < 5) {
        str += day + this.$t('pages.day1')
      }
      str += data.range + this.$t('pages.pushReportData')
      return str
    },
    /**
     * 推送时间(列表显示)
     * */
    changeDimBaseType(dimBaseType) {
      let str = ''
      switch (dimBaseType) {
        case 1: str = this.$t('pages.dimFormatText1'); break
        case 2: str = this.$t('pages.dimFormatText2'); break
        case 3: str = this.$t('pages.dimFormatText3'); break
        case 4: str = this.$t('pages.dimFormatText4'); break
        case 5: str = this.$t('pages.dimFormatText5'); break
      }
      return str
    },
    /**
     * 模板名称(列表显示)
     * */
    tempNameFormatter(row) {
      const templateId = row.reportConfig.templateId
      if (this.tempNameList.length > 0) {
        for (let i = 0; i < this.tempNameList.length; i++) {
          let str = ''
          if (templateId == this.tempNameList[i].id) {
            str = this.tempNameList[i].title
            return str
          }
        }
      }
    },
    /**
     * 是否启用图标添加
     * */
    taskEnableIconFormatter(row) {
      const activeValue = { true: true, false: false, 1: true, 0: false }[row.isPush]
      const activeOptions = {
        true: i18n.t('text.enable'),
        false: i18n.t('text.disable2')
      }
      const label = activeOptions[activeValue]
      return activeValue ? [{ class: 'active', title: label }] : [{ class: 'offline', title: label, style: 'color: #888;' }]
    },
    /**
     * 列表选中发生变化
     * @param rows
     */
    selectionChangeEnd(rows) {
      if (rows && rows.length > 0) this.deleteable = true
      else this.deleteable = false
    }
  }
}
</script>
