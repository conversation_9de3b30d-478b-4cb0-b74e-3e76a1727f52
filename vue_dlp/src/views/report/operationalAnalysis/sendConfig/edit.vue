<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="dialogTitle"
      :visible.sync="dlgVisible"
      width="700px"
    >
      <Form ref="dataForm" :model="temp" :rules="formRules" label-position="right" label-width="100px" style="width: 650px;">
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.taskName')" prop="name" label-width="120px">
              <el-input v-model="temp.name" v-trim maxlength="60" :disabled="unEditable"/>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.remark')">
              <el-input v-model="temp.remark" v-trim maxlength="100" :disabled="unEditable" show-word-limit/>
            </FormItem>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <FormItem :label="$t('pages.enable')" label-width="120px">
              <el-switch v-model="temp.isPush" :disabled="unEditable"/>
            </FormItem>
          </el-col>
        </el-row>
        <el-card :body-style="{'padding-top': '0'}">
          <el-divider content-position="left">{{ $t('pages.pushContent') }}</el-divider>
          <!--报表推送的内容-->
          <el-row>
            <el-col :span="12">
              <FormItem :label="$t('pages.countByObject')">
                <el-select v-model="temp.reportConfig.countByObject" :disabled="unEditable" is-filter :placeholder="$t('text.select')">
                  <el-option
                    v-for="(item, index) in typeOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </FormItem>
            </el-col>
            <el-col :span="12">
              <FormItem label="Top">
                <el-select ref="top" v-model="temp.reportConfig.top" :disabled="unEditable" allow-create filterable @visible-change="visibleChange">
                  <el-option
                    v-for="(item, index) in sizeOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </FormItem>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12" style="position: relative">
              <FormItem :label="$t('pages.templateName')">
                <el-select v-model="temp.reportConfig.templateId" filterable :disabled="unEditable" is-filter class="unitSelect" :placeholder="$t('text.select')" @visible-change="tempListVisibleChange">
                  <el-option
                    v-for="(item, index) in tempNameList"
                    :key="index"
                    :label="item.title"
                    :value="item.id"
                  />
                </el-select>
              </FormItem>
              <router-link v-if="hasPermission('907')" to="/report/operationalAnalysis/templateConfig" style="color: #3f9beb;position: absolute;top: 8px;right: -18px;">
                <el-button size="mini" :title="$t('pages.addTemplateConfig1')" class="editBtn" style="width: 17px;height: 17px;padding: 0 !important;border: none"><svg-icon icon-class="add" /></el-button>
              </router-link>
            </el-col>
            <el-col :span="12">
              <FormItem :label="$t('pages.templateDate')">
                <el-select v-model="temp.reportConfig.dimBaseType" :disabled="unEditable" is-filter class="unitSelect" :placeholder="$t('text.select')" @change="formatPushTimePre">
                  <el-option v-for="(item, index) in reportTimeTypesList" :key="index" :label="item.label" :value="item.value"/>
                </el-select>
              </FormItem>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <FormItem :label="$t('pages.reportScope')">
                <span v-if="unEditable">{{ departName }}</span>
                <tree-select
                  v-if="!unEditable"
                  ref="deptTree"
                  leaf-key="dept"
                  :placeholder="$t('pages.reportScopeText1')"
                  :local-search="false"
                  :checked-keys="[temp.reportConfig.groupIds]"
                  :filter-key="filterKey"
                  node-key="dataId"
                  is-filter
                  :width="286"
                  @change="deptChange"
                />
                <!--<el-select v-model="temp.reportConfig.groupType" :disabled="unEditable" class="unitSelect" @change="changeRange">
                  <el-option v-for="item in reportScopeList" :key="item.id" :label="item.name" :value="item.id"/>
                </el-select>-->
              </FormItem>
            </el-col>
          </el-row>
          <el-divider content-position="left"> {{ $t('pages.pushWay') }} </el-divider>
          <!-- 推送方式新增修改弹框 -->
          <data-editor
            v-if="!unEditable"
            append-to-body
            :popover-width="500"
            :updateable="editReceiverAble"
            :deletable="editReceiverAble"
            :add-func="createReceiver"
            :update-func="updateReceiver"
            :delete-func="deleteReceiver"
            :before-add="beforeAddReceiver"
            :before-update="beforeUpdateReceiver"
            :cancel-func="resetPushObj"
          >
            <Form ref="receiverForm" :model="pushObj" :rules="receiverRules" label-position="right" label-width="85px" style="margin-left: -10px">
              <FormItem :label="$t('table.msgPushMethod')" prop="pushType">
                <el-select v-model="pushObj.pushType" style="width: 400px" @change="clearVal">
                  <el-option v-for="(item, key) in pushTypeDict" :key="key" :label="item.label" :value="item.value"/>
                </el-select>
              </FormItem>
              <FormItem :label="$t('table.nickName')" prop="name">
                <el-input v-model="pushObj.name" maxlength="60"/>
              </FormItem>
              <FormItem :label="$t('table.account')" prop="receiver">
                <el-input v-if="pushObj.pushType != '2'" v-model="pushObj.receiver" maxlength="60"/>
                <wechat-user-select
                  v-if="pushObj.pushType == 2"
                  ref="pushObjectTree"
                  :width="360"
                  :editable="!unEditable"
                  :check-keys="pushUserIds"
                  :wechat-tree-data="receiveObjectTreeData"
                  :show-link-btn="false"
                  @getChangeValue="getSelectWechatUser"
                />
              </FormItem>
            </Form>
          </data-editor>
          <!--推送方式表格-->
          <grid-table
            ref="receiverTable"
            :height="150"
            :multi-select="!unEditable"
            :show-pager="false"
            :col-model="receiverColModel"
            :row-datas="temp.receiveObject"
            :autoload="false"
            style="margin-bottom: 10px;"
            @selectionChangeEnd="receiverSelectionChange"
          />
          <el-divider content-position="left"> {{ $t('pages.pushTime') }} </el-divider>
          <div style="font-weight: 800;margin-left: 20px;">
            <span>{{ pushTimePreText }}</span>
            <el-select v-if="temp.reportConfig.dimBaseType != 5" v-model="temp.day" :disabled="unEditable" style="width: 60px">
              <el-option v-for="item in 7" :key="item" :label="item" :value="item"></el-option>
            </el-select>
            <span v-if="temp.reportConfig.dimBaseType != 5">{{ $t('pages.dimFormatText6') }}</span>
            <el-time-picker
              v-model="temp.pushTimeRange"
              style="width: 160px"
              is-range
              :editable="false"
              :clearable="false"
              format="HH:mm"
              value-format="HH:mm"
              :range-separator="$t('pages.till')"
              :disabled="unEditable"
            >
            </el-time-picker>
            <span>{{ $t('pages.pushReportData') }}</span>
          </div>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="dialogStatus!=='create' && !unEditable" type="primary" @click="handleCopy">{{ $t('components.saveAs' ) }}</el-button>
        <el-button v-show="!unEditable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dlgVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <!--另存为-->
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('components.saveAs')"
      :visible.sync="dialogCopyVisible"
      width="400px"
    >
      <Form ref="copyForm" :model="copy" :rules="copyRules" label-position="right" label-width="70px" style="width: 300px; margin-left:20px;">
        <FormItem :label="$t('pages.taskName')" prop="name">
          <el-input v-model="copy.name" v-trim maxlength="60"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="copy.remark" v-trim maxlength="100" show-word-limit/>
        </FormItem>
        <FormItem :label="$t('pages.enable')">
          <el-switch v-model="copy.isPush"/>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="copyConfirm()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogCopyVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <mail-import-table-dlg ref="mailImportTableDlg" @submitEnd="importEnd"/>
  </div>
</template>

<script>
import WechatUserSelect from '@/components/WechatUserSelect/index';
import MailImportTableDlg from '@/views/system/baseData/groupImportList/mailImportTableDlg';
import { getReportTimeTypesList } from '@/utils/messagePush';
import { getTemplateList } from '@/api/report/baseReport/operationalAnalysis/templateConfig';
import { msgPushMessageTask, msgPushUpdateTask } from '@/api/report/baseReport/operationalAnalysis/sendConfig';
import { getTaskByName } from '@/api/system/messageNotification/messagePush'
export default {
  name: 'Edit',
  components: { WechatUserSelect, MailImportTableDlg },
  props: {
    dialogTitle: {
      type: String,
      default() {
        return ''
      }
    },
    dialogStatus: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      submitting: false,
      unEditable: false,
      dlgVisible: false,
      editReceiverAble: false,
      dialogCopyVisible: false,
      departName: '',
      // 详情赋值
      detailsRow: {},
      temp: {},
      defaultForm: {
        id: undefined,
        name: '',
        remark: '',
        sysUserAccount: '',
        dataType: 2,
        isPush: false,
        reportConfig: {
          // 模板名称
          templateId: undefined,
          groupType: 1,
          // 模板日期
          dimBaseType: 5,
          // 统计类型
          countByObject: 1,
          groupIds: 0,
          // Top
          top: 5
        },
        // 推送方式表格数据
        receiveObject: [],
        // 推送时间
        day: 1,
        pushTimeRange: ['08:00', '18:00']
      },
      // 另存为
      copy: {},
      defaultCopy: {
        name: '',
        remark: '',
        isPush: false
      },
      sizeOptions: [{ value: 5, label: 5 }, { value: 10, label: 10 }, { value: 20, label: 20 }],
      typeOptions: [
        { value: 1, label: this.$t('pages.conutType1') },
        { value: 2, label: this.$t('pages.conutType2') }
      ],
      tempNameList: [],
      // 推送方式表格相关
      pushObj: {},
      defaultPushObject: {
        pushType: '3',
        receiver: undefined,
        name: undefined
      },
      // 微信列表
      pushUserIds: [],
      receiveObjectTreeData: [],
      // 推送方式
      pushTypeDict: [
        { value: '3', label: this.$t('pages.pushType3') }
      ],
      // 模板日期
      pushTimePreText: '',
      reportTimeTypesList: undefined,
      // 新增修改表单验证
      formRules: {
        name: [
          { required: true, message: this.$t('pages.MsgPushValidatorText1'), trigger: 'blur' },
          { validator: this.nameValidate, trigger: 'blur' }
        ],
        templateId: [
          { required: true, message: this.$t('pages.foundationForm_Msg3'), trigger: 'change' }
        ]
      },
      // 另存为表单验证
      copyRules: {
        name: [
          { required: true, message: this.$t('pages.MsgPushValidatorText1'), trigger: 'blur' },
          { validator: this.copyNameValidate, trigger: 'blur' }
        ]
      },
      // 推送方式弹框表单验证
      receiverRules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_emailName'), trigger: 'blur' }],
        receiver: [
          { required: true, type: 'email', message: this.$t('pages.validateMsg_email'), trigger: 'blur' },
          { validator: this.emailAddressCheck, trigger: 'blur' }
        ]
        // receiver: [{ required: true, validator: this.receiverValidate, trigger: 'blur' }]
      },
      // 推送方式表格列
      receiverColModel: [
        { prop: 'pushType', label: 'msgPushMethod', width: '130', formatter: (row, data) => { return this.pushTypeDict[0].label } },
        { prop: 'name', label: 'nickName', width: '130' },
        { prop: 'receiver', label: 'account', width: '130' }
      ],
      reportScopeList: [
        { id: 2, name: this.$t('pages.company') },
        { id: 1, name: this.$t('pages.dept') }
      ],
      filterKey: '-2'
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
    // 报表日期下拉框
    this.reportTimeTypesList = getReportTimeTypesList()
    this.resetTemp()
    this.getTemplateList()
  },
  provide() {
    return {
      showImportAccDlg: () => this.$refs['mailImportTableDlg'] && this.$refs['mailImportTableDlg'].show(),
      importDlgBtnName: this.$t('pages.emailLibImport')
    }
  },
  methods: {
    /**
     * 统计范围切换为部门时，部门id清空
     * */
    // changeRange(data) {
    //   // console.log('data', data)
    //   if (data === 2) {
    //     this.temp.reportConfig.groupIds = 0
    //   }
    // },
    /**
     * 部门树选择
     * */
    deptChange(key, data) {
      this.temp.reportConfig.groupIds = key
    },
    /**
     * 获取模板名称（下拉列表）
     * */
    getTemplateList() {
      const query = { searchInfo: '', page: 1, sortName: 'id', sortOrder: 'desc' }
      getTemplateList(query).then(res => {
        // console.log('推送配置弹框', res.data)
        this.tempNameList = res.data.items
      })
    },
    /**
     * 模板名称下拉列表打开时，重新获取列表值
     * */
    tempListVisibleChange(data) {
      if (data) {
        this.getTemplateList()
      }
    },
    /**
     * 另存为弹框显示
     * */
    handleCopy() {
      this.dialogCopyVisible = true
      this.copy = JSON.parse(JSON.stringify(this.temp))
      // console.log('this.copy', this.copy)
    },
    resetTemp() {
      this.temp = Object.assign({}, JSON.parse(JSON.stringify(this.defaultForm)))
      this.copy = Object.assign({}, JSON.parse(JSON.stringify(this.defaultCopy)))
    },
    /**
     * 新增确认
     * */
    createData() {
      this.submitting = true
      if (!this.temp.reportConfig.hasOwnProperty('templateId')) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.foundationForm_Msg31'), type: 'error', duration: 5000 })
        return
      }
      if (this.temp.reportConfig.groupIds === '' || this.temp.reportConfig.groupIds === null) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.moduleConfigDeptNotNull'), type: 'error', duration: 5000 })
        return
      }
      if (this.temp.receiveObject.length === 0) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.foundationForm_Msg6'), type: 'error', duration: 5000 })
        return false
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          this.assignTemplateName(tempData)
          // console.log('tempData', tempData)
          msgPushMessageTask(tempData).then(respond => {
            this.submitting = false
            this.dlgVisible = false
            // this.gridTable.execRowDataApi(this.query)
            this.$parent.refreshTab()
            // this.$emit('submitEnd')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    /**
     * 修改确认
     * */
    updateData() {
      this.submitting = true
      if (!this.temp.reportConfig.hasOwnProperty('templateId')) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.foundationForm_Msg31'), type: 'error', duration: 5000 })
        return
      }
      if (this.temp.reportConfig.groupIds === '' || this.temp.reportConfig.groupIds === null) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.moduleConfigDeptNotNull'), type: 'error', duration: 5000 })
        return
      }
      if (this.temp.receiveObject.length === 0) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.foundationForm_Msg6'), type: 'error', duration: 5000 })
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          this.assignTemplateName(tempData)
          msgPushUpdateTask(tempData).then(respond => {
            this.submitting = false
            this.dlgVisible = false
            this.$parent.refreshTab()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    /**
     * 另存为确认按钮点击
     * */
    copyConfirm() {
      this.submitting = true
      if (!this.temp.reportConfig.hasOwnProperty('templateId')) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.foundationForm_Msg31'), type: 'error', duration: 5000 })
        return
      }
      if (this.temp.reportConfig.groupIds === '' || this.temp.reportConfig.groupIds === null) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.moduleConfigDeptNotNull'), type: 'error', duration: 5000 })
        return
      }
      if (this.temp.receiveObject.length === 0) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.foundationForm_Msg6'), type: 'error', duration: 5000 })
        return
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitting = false
          this.$refs['copyForm'].validate((valid) => {
            if (valid) {
              this.temp.reportConfig.id = undefined
              const tempData = {}
              tempData.id = undefined
              tempData.name = this.copy.name
              tempData.remark = this.copy.remark
              tempData.isPush = this.copy.isPush
              tempData.dataType = 2
              tempData.reportConfig = this.temp.reportConfig
              tempData.receiveObject = this.temp.receiveObject
              // tempData.reportConfig.id = undefined
              // 推送时间
              tempData.day = this.temp.day
              tempData.pushTimeRange = this.temp.pushTimeRange
              this.assignTemplateName(tempData)
              msgPushMessageTask(tempData).then(respond => {
                this.submitting = false
                this.dlgVisible = false
                this.dialogCopyVisible = false
                // this.gridTable.execRowDataApi(this.query)
                this.$parent.refreshTab()
                // this.$emit('submitEnd')
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.createSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(reason => {
                this.submitting = false
              })
            }
          })
        } else {
          this.submitting = false
        }
      })
    },
    /**
     * 邮箱新增去重
     * */
    emailAddressCheck(rule, value, callback) {
      const email = (this.temp.receiveObject || []).find(email => email.receiver === value)
      if (email && email.receiver === this.pushObj.receiver) {
        // 修改
        if (this.pushObj.hasOwnProperty('id') && this.pushObj.id === email.id) {
          callback()
        } else {
          callback(new Error(this.$t('pages.effectiveContent_text31')))
          return
        }
      }
      callback()
    },
    /**
     * 邮箱库导入去重
     * */
    mergeArrays(arrA, arrB) {
      arrA.forEach(aItem => {
        // 在arrB中查找是否存在相同的对象
        const bItem = arrB.find(bItem => bItem.receiver === aItem.receiver);
        if (!bItem) {
          // 如果不存在，将aItem添加到arrB中
          arrB.unshift(aItem);
        }
      });
      // 限制最多15条
      if (arrB.length > 15) {
        arrB.splice(0, arrB.length - 15);
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.foundationForm_Msg32'),
          type: 'warning',
          duration: 2000
        })
      }
      return arrB;
    },
    /**
     * 邮箱库导入
     * */
    importEnd(datas) {
      if (datas) {
        const newData = []
        datas.forEach(item => {
          const { name, address, id } = item
          const receiver = address
          const pushType = '3'
          newData.push({ id, name, receiver, pushType })
        })
        this.mergeArrays(newData, this.temp.receiveObject);
      }
    },
    /**
     * 打开新增弹框<父组件调用>
     */
    handleCreateEditShow() {
      this.resetTemp()
      this.formatPushTimePre(this.temp.reportConfig.dimBaseType)
      this.dlgVisible = true
      this.$nextTick(() => {
        // 模板名称设置默认选中第一个
        if (this.tempNameList.length > 0) {
          this.$set(this.temp.reportConfig, 'templateId', this.tempNameList[0].id);
        }
        this.unEditable = false
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * 打开修改弹框<父组件调用>
     */
    handleUpdateEditShow(row) {
      this.departName = ''
      this.resetTemp()
      // console.log('row111', JSON.parse(JSON.stringify(row)), JSON.parse(JSON.stringify(this.temp)))
      this.$nextTick(() => {
        // 如果直接用JSON.parse深拷贝赋值，下面时间规则修改不了，如果直接浅拷贝Object.assign赋值，弹框修改时表格也会发生变化，所以做了个中转
        this.detailsRow = JSON.parse(JSON.stringify(row))
        this.temp = Object.assign(this.temp, this.detailsRow)
        // 初始化时间规则
        const timeRules = JSON.parse(row.executionRules)
        this.temp.day = timeRules.day
        const range = timeRules.range.split('-')
        this.temp.pushTimeRange = [range[0], range[1]]
        this.formatPushTimePre(row.reportConfig.dimBaseType)
        if (this.dialogStatus === 'view') {
          this.unEditable = true
          this.departName = row.reportConfig.groupName
        } else {
          this.unEditable = false
        }
        this.$refs['dataForm'].clearValidate()
      })
      this.dlgVisible = true
    },
    /**
     * 报表日期下拉框选择
     * @param dimBaseType
     */
    formatPushTimePre(dimBaseType) {
      this.pushTimePreText = this.changeDimBaseType(dimBaseType)
    },
    /**
     * 报表日期下拉框选择
     * @param dimBaseType
     */
    changeDimBaseType(dimBaseType) {
      let str = ''
      switch (dimBaseType) {
        case 1: str = this.$t('pages.dimFormatText1'); break
        case 2: str = this.$t('pages.dimFormatText2'); break
        case 3: str = this.$t('pages.dimFormatText3'); break
        case 4: str = this.$t('pages.dimFormatText4'); break
        case 5: str = this.$t('pages.dimFormatText5'); break
      }
      return str
    },
    /**
     * 新增推送方式（确认按钮点击）
     * @returns {*}
     */
    createReceiver() {
      let validate
      if (this.temp.receiveObject.length >= 15) {
        this.submitting = false
        this.$notify({ title: this.$t('text.error'), message: this.$t('pages.foundationForm_Msg33'), type: 'error', duration: 5000 })
        return
      }
      this.$refs['receiverForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.pushObj)
          rowData.id = new Date().getTime()
          // console.log('rowData', JSON.parse(JSON.stringify(rowData)))
          this.temp.receiveObject.unshift(rowData)
          validate = valid
          // console.log('this.temp', JSON.parse(JSON.stringify(this.temp)))
        }
      })
      return validate
    },
    /**
     * 新增推送方式（修改）
     * @returns {*}
     */
    updateReceiver() {
      let validate
      this.$refs['receiverForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.pushObj)
          for (let i = 0, size = this.temp.receiveObject.length; i < size; i++) {
            const data = this.temp.receiveObject[i]
            if (rowData.id === data.id) {
              this.temp.receiveObject.splice(i, 1, rowData)
              break
            }
          }
          validate = valid
        }
      })
      return validate
    },
    /**
     * 推送方式切换时，清空下方两个下拉列表的值
     * */
    clearVal() {
      this.pushObj.name = undefined
      this.pushObj.receiver = undefined
      this.pushUserIds = []
    },
    resetPushObj() {
      this.pushObj = JSON.parse(JSON.stringify(this.defaultPushObject))
      this.clearValidate()
    },
    clearValidate() {
      this.$refs['receiverForm'] && this.$refs['receiverForm'].clearValidate()
    },
    /**
     * 新增推送方式（删除按钮点击）
     * @returns {*}
     */
    deleteReceiver() {
      const toDeleteIds = this.$refs['receiverTable'].getSelectedIds()
      this.temp.receiveObject.splice(0, this.temp.receiveObject.length, ...this.$refs['receiverTable'].deleteRowData(toDeleteIds))
    },
    beforeAddReceiver() {
      this.resetPushObj()
    },
    /**
     * 删除之前？？(消息推送抄过来的)
     */
    beforeUpdateReceiver() {
      this.resetPushObj()
      const selectDatas = this.$refs['receiverTable'].getSelectedDatas()
      this.pushObj = Object.assign({
        pushType: '3',
        receiver: undefined,
        name: undefined
      }, selectDatas && selectDatas.length > 0 ? selectDatas[0] : {})
      this.pushObj['pushType'] = this.pushObj['pushType'] + ''
    },
    /**
     * 表格选中发生变化时，设置按钮状态
     * @param rowDatas
     */
    receiverSelectionChange(rowDatas) {
      this.editReceiverAble = rowDatas.length > 0
    },
    /**
     * 微信公众号推送的啥东西
     * @param key
     */
    getSelectWechatUser(key) {
      this.pushUserIds = key
    },
    /**
     * 任务名表单验证(新增，修改)
     * @param rule
     * @param value
     * @param callback
     */
    nameValidate(rule, value, callback) {
      getTaskByName({ name: value, dataType: 2 }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('pages.foundationForm_Msg34')))
        } else {
          callback()
        }
      })
    },
    /**
     * 任务名表单验证（另存为）
     * @param rule
     * @param value
     * @param callback
     */
    copyNameValidate(rule, value, callback) {
      getTaskByName({ name: value, dataType: 2 }).then(respond => {
        const bean = respond.data
        let saveId = this.temp.id
        if (this.dialogCopyVisible) {
          saveId = undefined
        }
        if (bean && bean.id !== saveId) {
          callback(new Error(this.$t('pages.foundationForm_Msg34')))
        } else {
          callback()
        }
      })
    },
    /**
     * 昵称表单验证
     * @param rule
     * @param value
     * @param callback
     */
    receiverValidate(rule, value, callback) {
      if (this.pushObj.pushType == 3 && (value !== undefined && value !== null && value !== '')) {
        const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        const reg1 = /^#{1}[a-zA-Z0-9_@.]+#{1}$/
        const len = value.split('#')
        if (!(reg.test(value) || reg1.test(value) || len.length === 2)) {
          callback(new Error(this.$t('pages.validateMsg_email2')))
        }
      } else if (this.pushObj.pushType != 2 && (value === undefined || value === null || value === '')) {
        callback(new Error(this.$t('text.cantNullInfo', { info: this.$t('table.account') })))
      } else if (this.pushObj.pushType == 1 && !(/^1[3456789]\d{9}$/.test(value))) {
        callback(new Error(this.$t('pages.validateMsg_phone')))
      }
      callback()
    },
    /**
     * top选择发生变化时
     */
    visibleChange() {
      const value = this.temp.reportConfig.top
      const reg = new RegExp(/^[1-9]\d*$/)
      if (!reg.test(value)) {
        this.temp.reportConfig.top = 5
      } else {
        this.temp.reportConfig.top = parseInt(value)
      }
    },
    assignTemplateName(tempData) {
      if (!tempData) { return }
      const templateId = (tempData.reportConfig || {}).templateId
      templateId && (tempData.reportConfig.reportName = (this.tempNameList.find(item => item.id == templateId) || {}).title)
    }
  }
}
</script>
