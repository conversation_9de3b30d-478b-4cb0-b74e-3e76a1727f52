<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 350px;">
        <FormItem :label="$t('pages.templateType')" prop="title">
          <el-input v-model="temp.title" maxlength="60"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogVisible=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getByTitle } from '@/api/report/baseReport/operationalAnalysis/templateConfig'

export default {
  name: 'AddTemplate',
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      type: '',
      temp: {},
      defaultTemp: {
        title: ''
      },
      rules: {
        title: [
          { required: true, message: this.$t('pages.foundationForm_Msg3'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    show(title, type) {
      this.resetTemp()
      this.dialogTitle = title
      this.type = type
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * 确认按钮点击
     */
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 传递给父组件，用于新增报表的报表名称，模板内容
          this.$emit('addReportName', this.temp.title, 'saveAs')
          // 另存为，点击确认时需要调用父组件的保存方法
          this.$parent.saveBtn()
          this.dialogVisible = false
        }
      })
    },
    /**
     * 判断模板名称是否重复
     * @param rule
     * @param value
     * @param callback
     */
    nameValidator(rule, value, callback) {
      getByTitle({ id: '', title: value }).then(respond => {
        const pattern = /[\\/:：*?？"”<>|]/;
        if (respond.data === true) {
          callback(new Error(this.$t('pages.foundationForm_Msg4')))
        } else if (value.length < 3) {
          callback(new Error(this.$t('pages.foundationForm_Msg5')))
        } else if (pattern.test(value)) {
          callback(new Error(this.$t('pages.foundationForm_Msg30') + '：\\ / : * ? " < > |'))
        } else {
          callback()
        }
      })
    },
    handleDrag() {

    }
  }
}
</script>

<style scoped>

</style>
