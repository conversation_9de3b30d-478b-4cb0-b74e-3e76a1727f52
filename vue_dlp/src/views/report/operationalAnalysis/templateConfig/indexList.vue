<template>
  <div
    v-loading="loading"
    class="app-container"
    :element-loading-text="$t('pages.foundationForm_Msg42')"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.6)"
  >
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addTemplate') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" clearable :placeholder="$t('pages.pleaseModuleName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('button.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="messageTaskList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
  </div>
</template>

<script>
import { getTemplateList, deleteTemplate, downloadReport } from '@/api/report/baseReport/operationalAnalysis/templateConfig';
export default {
  name: 'IndexList',
  components: { },
  data() {
    return {
      loading: false,
      deleteable: false,
      updateTemp: {
        id: '',
        isLib: 0,
        templateFileName: '',
        title: ''
      },
      query: {
        searchInfo: '',
        page: 1,
        limit: 20,
        sortName: 'id',
        sortOrder: 'desc'
      },
      colModel: [
        { prop: 'title', label: 'templateName' },
        { prop: 'createTime', label: 'createTime' },
        {
          label: 'operate', type: 'button', width: '120', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
            // { label: '测试模板', click: this.handleTest }
          ]
        }
      ]
    }
  },
  computed: {
    gridTable() {
      return this.$refs['messageTaskList']
    }
  },
  watch: {

  },
  created() {

  },
  methods: {
    /**
     * 获取模板列表数据
     * */
    rowDataApi(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTemplateList(searchQuery)
    },
    /**
     * 搜索
     * */
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    /**
       * 新增
       */
    handleCreate() {
      this.updateTemp = {
        id: '',
        isLib: 0,
        templateFileName: '',
        title: ''
      }
      // console.log('新增', JSON.parse(JSON.stringify(this.updateTemp)))
      this.$emit('AddTemp', false, 'add', this.updateTemp);
    },
    /**
       * 修改
       * @param row
       */
    handleUpdate(row) {
      this.updateTemp.id = row.id
      this.updateTemp.templateFileName = row.templateFileName
      this.updateTemp.title = row.title
      this.$emit('AddTemp', false, 'update', this.updateTemp);
      // console.log('row', JSON.parse(JSON.stringify(row)))
    },
    /**
     * 测试模板
     * @param row
     */
    handleTest(row) {
      this.loading = true
      downloadReport({ id: row.id }).then(res => {
        this.loading = false
        if (res.data) {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.foundationForm_Msg43'),
            type: 'success',
            duration: 2000
          })
        }
      }).catch(res => {
        this.loading = false
      })
    },
    /**
       * 删除
       */
    handleDelete() {
      const taskIds = this.gridTable.getSelectedKeys().join(',')
      // console.log('taskIds', taskIds)
      const toDeleteDatas = this.gridTable.getSelectedDatas() || []
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt'))
        .then(() => {
          deleteTemplate({ ids: taskIds }).then(respond => {
            this.gridTable.deleteRowData(toDeleteDatas)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }).catch(() => {})
    },
    /**
       * 列表选中发生变化
       * @param rows
       */
    selectionChangeEnd(rows) {
      if (rows && rows.length > 0) this.deleteable = true
      else this.deleteable = false
    }
  }
}
</script>
