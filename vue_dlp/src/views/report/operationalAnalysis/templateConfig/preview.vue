<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.overview')"
      :visible.sync="dialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div v-html="previewContent"></div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'Preview',
  data() {
    return {
      dialogVisible: false,
      previewContent: ''
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.previewContent = ''
    },
    /**
     * 替换html字符串中所有{{...}}变量为随机数字（不包括表格）
     * @param str 需要替换的字符串
     * @param num 随机数
     * @returns {*}
     */
    replaceVariables(str, num) {
      // 替换{{}}中的所有内容
      // return str.replace(/\{\{([^}]+)\}\}/g, () => num);
      // 判断{{}}中是否只包含英文和数字，如果是，则替换
      return str.replace(/\{\{(\w+)\}\}/g, () => num);
    },
    /**
     * 替换html字符串中所有{{...}}变量为空（只有表格的表头那个变量，不包含别的变量）
     * @param str 需要替换的字符串
     * @returns {*}
     */
    replaceVariables1(str) {
      return str.replace(/\<td style="background-color: #4874cb;">\{\{\s*\w+\s*\}\}/g, '<td style="background-color: #4874cb;">');
    },
    /**
     * 替换html字符串中所有[...]变量为随机数字（主要是表格的表体数据替换）
     * @param str 需要替换的字符串
     * @param num 随机数
     * @returns {*}
     */
    replacePlaceholders(str, num) {
      return str.replace(/\[.*?\]/g, () => num);
    },
    /**
     * 弹框显示（将html处理后显示为预览效果，主要是替换富文本中的变量）
     * @param htmlString
     */
    show(htmlString) {
      this.resetTemp()
      this.dialogVisible = true
      const result2 = this.replaceVariables1(htmlString);
      const result = this.replaceVariables(result2, Math.floor(Math.random() * 100));
      this.previewContent = this.replacePlaceholders(result, Math.floor(Math.random() * 100))
    },
    handleDrag() {

    }
  }
}
</script>

<style scoped>

</style>
