<template>
  <tree-menu
    ref="tree"
    :data="indexTableTreeList"
    :height="200"
    :accordion="false"
    node-key="dataId"
    draggable
    :filter-node-method="filterNode"
    :allow-drop="allowDrop"
    :allow-drag="allowDrag"
    :render-content="renderChartContent"
    @node-drag-start="handleDragStart"
    @node-drag-end="handleDragEnd"
  />
</template>

<script>
import { getReportTableTemplates } from '@/api/report/baseReport/operationalAnalysis/templateConfig';
export default {
  name: 'DragTable',
  components: { },
  data() {
    return {
      // 报表表格内容
      indexTableTreeList: [],
      dragHtml: ''
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.indexTableTree()
  },
  methods: {
    /**
     * 过滤时展开子节点
     * */
    filterNode(value, data, node) {
      // 输入为空时，折叠展开内容
      if (!value && node.parent.expanded) {
        node.parent.expanded = false
      }
      if (data.label) {
        return this.chooseNode(value, data, node);
      }
    },
    // 过滤父节点 / 子节点 (如果输入的参数是父节点且能匹配，则返回该节点以及其下的所有子节点；如果参数是子节点，则返回该节点的父节点。name是中文字符，enName是英文字符.
    chooseNode(value, data, node) {
      // console.log('data.label.indexOf(value)', data.label.indexOf(value))
      if (data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1) {
        return true
      }
      const level = node.level
      // 如果传入的节点本身就是一级节点就不用校验了
      if (level === 1) {
        return false
      }
      // 先取当前节点的父节点
      let parentData = node.parent
      // 遍历当前节点的父节点
      let index = 0
      while (index < level - 1) {
        // 如果匹配到直接返回，此处name值是中文字符，enName是英文字符。判断匹配中英文过滤
        if (parentData.data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1) {
          return true
        }
        // 否则的话再往上一层做匹配
        parentData = parentData.parent
        index++
      }
      // 没匹配到返回false
      return false
    },
    indexTableTree() {
      getReportTableTemplates().then(res => {
        // console.log('res表格树', JSON.parse(JSON.stringify(res.data)))
        // 1、递归修改数据，主要处理节点的label数据
        const respArr = res.data
        for (let i = 0; i < respArr.length; i++) {
          // console.log('respArr[i]', JSON.parse(JSON.stringify(respArr[i])))
          if (respArr[i].children) {
            respArr[i].label = this.$t('route.' + respArr[i].label)
          }
          // for (let j = 0; j < respArr[i].children.length; j++) {
          //   respArr[i].children[j].label = this.$t('pages.' + respArr[i].children[j].label)
          // }
        }
        // 2、赋值显示
        this.indexTableTreeList = respArr
      })
    },
    /**
     * 自定义treeMenu图标
     * */
    renderChartContent(h, { node, data, store }) {
      let type
      // console.log('data.type', data)
      if (data.children) {
        type = 'table2'
      } else {
        type = 'table1'
      }
      return (
        <div>
          <span title={node.label}>
            <svg-icon style={({ marginRight: '10px' })} icon-class={type}/>
            {node.label}
          </span>
        </div>
      )
    },
    /**
     * 设置节点可以被拖拽
     * */
    allowDrag() {
      return true;
    },
    /**
     * 拖拽时限制模板节点不能被放置
     * */
    allowDrop() {
      return false;
    },
    /**
     * 表格标签封装（用于拖拽时表格的显示）
     * @param node 表格界定啊信息
     * @param tHead 表头的td
     * @param tBody 表体的td
     * @param sortName 排序项默认值(注：排序项只有普通报表有)
     * @param sortingDescription 排序项提示的描述信息
     * @return 返回表格的html标签
     * */
    tableHtml(node, tHead, tBody, sortName, sortingDescription) {
      // console.log('node', JSON.parse(JSON.stringify(node)))
      const tableTemplate = `<div style="height: 40px;line-height: 40px;text-align:center;font-size: 14px;font-weight: bold; color: #000000">${node.label} <span style="display: none"></span></div>
          <table style="border-collapse: collapse; width: 100%;text-align: center" border="1">
          <tbody>
              <tr style="color: #ffffff;height: 40px;line-height: 40px;font-weight: 400">${tHead}</tr>
              <tr style="color: #000000;height: 40px;line-height: 40px;font-weight: 400">${tBody}</tr>
          </tbody>
          </table>
          <style> .compareMark { background: red; }</style>
          `
      if (node.type === 'normal') {
        let sortName1 = sortName
        // 应用时长报表的默认排序项需要改成"应用程序运行时长"
        if (node.dataId === 'appRunTime') {
          sortName1 = 'appRunningTime'
        }
        // 敏感关键字报表中，存在百分比字段不能作为排序项，单独做特殊提示
        // let specialNote = ''
        // if (node.dataId === 'sensitiveKeywordLog') {
        //   specialNote = '（百分比不能作为排序项）'
        // }

        return `${tableTemplate}
            <div class="compareMark" style="display: block;font-size: 12px;line-height: 25px;font-weight: 400;text-align: left"><label class="compareMarkLabel" style="display: block;color: #ff0000;">注：您可通过下方配置实现自定义排序项,可选排序项参数有：${sortingDescription}，只可以选择一项指标作为排序项</label></div>
            <div class="sortNameParameter" style="display: block;color: #000000;font-weight: 400;text-align: left"><label class="compareMarkLabel" style="display: block;">sortName:${sortName1}</label></div>
            <div></div><div></div>`
      }
      if (node.type === 'trend') {
        return `${tableTemplate}
            <div></div><div></div>`
      }
      if (node.type === 'compare') {
        return `${tableTemplate}
            <div class="compareMark" style="display: block;font-size: 12px;line-height: 25px;font-weight: 400;text-align: left"><label class="compareMarkLabel" style="display: block;color: #ff0000;">${this.$t('pages.foundationForm_Msg37')}：</label></div>
            <div class="compareMark" style="display: block;font-size: 12px;color: #ff0000;line-height: 25px;font-weight: 400"><label class="compareMarkLabel" style="display: block;"color: #ff0000;text-align: left">${this.$t('pages.foundationForm_Msg38')}；</label></div>
            <div class="compareMark" style="display: block;font-size: 12px;color: #ff0000;line-height: 25px;font-weight: 400"><label class="compareMarkLabel" style="display: block;color: #ff0000;text-align: left">${this.$t('pages.foundationForm_Msg39')}</label></div>
            <div class="qoq compareMark" style="display: block;color: #000000;font-weight: 400;text-align: left"><label class="compareMarkLabel" style="display: block;">qoqData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-qoqData)*100.0/qoqData)</label></div>
            <div class="yoy compareMark" style="display: block;color: #000000;font-weight: 400;text-align: left"><label class="compareMarkLabel" style="display: block;">yoyData == 0 ? (data == 0 ? '0' : '∞') : T(java.lang.String).format('%.2f%%',(data-yoyData)*100.0/yoyData)</label></div>
            <div></div><div></div>`
      }
    },
    /**
     * 拖拽开始
     * 添加报表表格数据内容（处理）
     * */
    handleDragStart(node) {
      // console.log('node', JSON.parse(JSON.stringify(node.data.type)))
      // 判断是否存在'children'字段，没有children的才可以拖拽
      if (!('children' in node.data)) {
        if (node.data.type != 'compare') {
          // 普通报表和趋势报表表格生成
          let tdHead = ``
          let tdBody = ``
          const sortName = []
          let sortingDescription = ''
          for (let i = 0; i < node.data.oriData.length; i++) {
            const item = node.data.oriData[i]
            let tabName = this.$t(item.colName)
            // 如果tabName中包含.，所有多语言前缀不是table，则改成report,如果还是没有，改成pages
            if (tabName == item.colName) {
              tabName = this.$t('table.' + item.colName)
              if (tabName.includes('.')) {
                tabName = this.$t('report.' + item.colName)
                if (tabName.includes('.')) {
                  tabName = this.$t('pages.' + item.colName)
                }
              }
            }
            let tabTag = '{{' + node.data.dataId + '}}'
            if (i > 0) {
              tabTag = ''
            }
            // console.log('tabTag', tabTag)
            tdHead += `<td style="background-color: #4874cb;">${tabTag} ${tabName}</td>`
            tdBody += `<td>[${item.colTag}]</td>`
            // 普通报表添加默认排序项以及排序项提示信息
            if (item.sortField === 1) {
              // 默认排序项
              sortName.push(item.colTag)
              // 排序项描述
              sortingDescription = (sortingDescription + ',' + tabName + ':' + item.colTag).replace(/^,/, '')
            }
          }
          // 传递给父组件，用于拖拽显示
          this.$emit('tableHtml', this.tableHtml(node.data, tdHead, tdBody, sortName[0], sortingDescription))
        } else {
          // 对比报表表格生成
          const timeTag = '{{' + node.data.dataId + '}}'
          const tdHead = `<td style="background-color: #4874cb;">${timeTag}${this.$t('table.statisticalItem')}</td>
                          <td style="background-color: #4874cb;">${this.$t('table.currentLabel')}{{curDate}}</td>
                          <td style="background-color: #4874cb;">{{qoqLastDate}}</td>
                          <td style="background-color: #4874cb;">${this.$t('table.qoq')}</td>
                          <td style="background-color: #4874cb;">{{yoyLastDate}}</td>
                          <td style="background-color: #4874cb;">${this.$t('table.yoy')}</td>`
          const tdBody = `<td>[name]</td>
                          <td>[data]</td>
                          <td>[qoqData]</td>
                          <td>[qoqDataPercent]</td>
                          <td>[yoyData]</td>
                          <td>[yoyDataPercent]</td>`
          this.$emit('tableHtml', this.tableHtml(node.data, tdHead, tdBody, '', ''))
        }
      }
    },
    /**
     * 拖拽结束
     * 如果拖拽结束后，不清空，在页面上随便拖拽内容，还会添加之前最后一次拖拽的内容
     * */
    handleDragEnd() {
      setTimeout(() => {
        // 传递给父组件，用于拖拽显示
        this.$emit('tableHtml', '')
      }, 1000)
    }
  }
}
</script>
