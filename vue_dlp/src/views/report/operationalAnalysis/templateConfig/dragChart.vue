<template>
  <tree-menu
    ref="tree"
    :data="indexChartTreeList"
    :height="200"
    :accordion="false"
    node-key="dataId"
    draggable
    :filter-node-method="filterNode"
    :allow-drop="allowDrop"
    :allow-drag="allowDrag"
    :render-content="renderChartContent"
    @node-drag-start="handleDragStart"
    @node-drag-end="handleDragEnd"
  />
</template>

<script>
import { getReportEchartList } from '@/api/report/baseReport/operationalAnalysis/templateConfig';
import sortFields from '../../../dashboard/customLayout/sortFields'
export default {
  name: 'Drag<PERSON>hart',
  components: { },
  data() {
    return {
      // 排序项
      sortFields,
      // 报表echarts图表内容
      indexChartTreeList: [],
      dragHtml: ''
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.indexEchartsTree()
  },
  methods: {
    /**
     * 过滤时展开子节点
     * */
    filterNode(value, data, node) {
      // 输入为空时，折叠展开内容
      if (!value && node.parent.expanded) {
        node.parent.expanded = false
      }
      if (data.label) {
        return this.chooseNode(value, data, node);
      }
    },
    // 过滤父节点 / 子节点 (如果输入的参数是父节点且能匹配，则返回该节点以及其下的所有子节点；如果参数是子节点，则返回该节点的父节点。name是中文字符，enName是英文字符.
    chooseNode(value, data, node) {
      // console.log('data.label.indexOf(value)', data.label.indexOf(value))
      if (data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1) {
        return true
      }
      const level = node.level
      // 如果传入的节点本身就是一级节点就不用校验了
      if (level === 1) {
        return false
      }
      // 先取当前节点的父节点
      let parentData = node.parent
      // 遍历当前节点的父节点
      let index = 0
      while (index < level - 1) {
        // 如果匹配到直接返回，此处name值是中文字符，enName是英文字符。判断匹配中英文过滤
        if (parentData.data.label.toLowerCase().indexOf(value.toLowerCase()) !== -1) {
          return true
        }
        // 否则的话再往上一层做匹配
        parentData = parentData.parent
        index++
      }
      // 没匹配到返回false
      return false
    },
    /**
     * 图表内容树结构获取
     * */
    indexEchartsTree() {
      getReportEchartList().then(res => {
        // 1、递归修改数据，主要处理根节点数据
        const respArr = res.data
        respArr.forEach(item => {
          if (item.children) {
            item.type = 'chart'
            item.label = this.$t('route.' + item.label)
          }
        })
        // 2、赋值显示
        this.indexChartTreeList = respArr
        // console.log('图表respArr', JSON.parse(JSON.stringify(respArr)))
        // console.log('图表', JSON.parse(JSON.stringify(res.data)))
      })
    },
    /**
     * 自定义treeMenu图标
     * */
    renderChartContent(h, { node, data, store }) {
      let type
      // console.log('data.type', data.type)
      if (data.type === 'chart') {
        type = data.type
      } else {
        type = 'chart-' + data.type
      }
      return (
        <div>
          <span title={node.label}>
            <svg-icon style={({ marginRight: '10px' })} icon-class={type}/>
            {node.label}
          </span>
        </div>
      )
    },
    /**
     * 设置节点可以被拖拽
     * */
    allowDrag() {
      return true;
    },
    /**
     * 拖拽时限制模板节点不能被放置
     * */
    allowDrop() {
      return false;
    },
    /**
     * 拖拽开始
     * 添加报表图表数据内容（处理）
     * */
    handleDragStart(node) {
      // console.log('node', JSON.parse(JSON.stringify(node.data)))
      // 图表排序项参数设置
      const currentSort = this.sortFields[node.data.dataId]
      let resultTips = ''
      let resultDefault = ''
      if (currentSort && currentSort.length > 0) {
        // 拖拽排序项提示信息
        resultTips = currentSort.map(item => this.$t('table.' + item.label) + `:${item.prop}`).join(',');
        resultDefault = currentSort[0].prop
      }
      if (node.data.type !== 'chart') {
        // 传递给父组件，用于拖拽显示
        node.data.resultTips = resultTips
        node.data.resultDefault = resultDefault
        this.$emit('chartHtml', node.data)
      }
    },
    /**
     * 拖拽结束
     * 如果拖拽结束后，不清空，在页面上随便拖拽内容，还会添加之前最后一次拖拽的内容
     * */
    handleDragEnd() {
      setTimeout(() => {
        // 传递给父组件，用于拖拽显示
        this.$emit('chartHtml', '')
      }, 1000)
    }
  }
}
</script>

