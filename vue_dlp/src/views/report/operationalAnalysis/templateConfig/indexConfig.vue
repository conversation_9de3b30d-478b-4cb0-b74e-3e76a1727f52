<template>
  <div
    v-loading="loading"
    class="app-container"
    :element-loading-text="$t('pages.foundationForm_Msg7')"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <div class="tree-container" :class="showTree?'':'hidden'">
      <el-collapse v-model="activeNames" style="height: 100%;border: 1px solid #666666;overflow: auto">
        <el-collapse-item :title="$t('pages.indexData')" name="1">
          <drag-content @contentHtml="contentHtmlChild"/>
        </el-collapse-item>
        <el-collapse-item :title="$t('pages.chartData')" name="2">
          <drag-chart @chartHtml="chartHtmlChild"/>
        </el-collapse-item>
        <el-collapse-item :title="$t('pages.tabularData')" name="3">
          <drag-table @tableHtml="contentHtmlChild"/>
        </el-collapse-item>
      </el-collapse>
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <span style="color: #ff0000;margin-right: 2px">*</span>{{ $t('pages.templateName') }}：
        <el-input v-model="temp.title" maxlength="60" style="width: 200px"/>
        <!--        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">-->
        <!--          新增模板-->
        <!--        </el-button>-->
        <el-button type="primary" size="mini" @click="saveBtn">
          {{ $t('button.save') }}
        </el-button>
        <el-button type="primary" size="mini" @click="saveAsBtn">
          {{ $t('components.saveAs') }}
        </el-button>
        <el-button type="primary" size="mini" @click="backListBtn">
          <svg-icon icon-class="back"></svg-icon>{{ $t('button.returnList') }}
        </el-button>
        <el-button size="mini" @click="previewWord">
          <svg-icon icon-class="eye-open" />  {{ $t('pages.overview') }}
        </el-button>
        <el-tooltip slot="tooltip" effect="dark" placement="bottom" >
          <div slot="content">
            {{ $t('pages.foundationForm_Msg8') }}
            <br>
            <br>
            {{ $t('pages.foundationForm_Msg9') }}
            <br>
            <br>
            {{ $t('pages.foundationForm_Msg10') }}
            <br>
            <br>
            {{ $t('pages.foundationForm_Msg11') }}
            <br>
            {{ $t('pages.foundationForm_Msg12') }}
            <br>
            {{ $t('pages.foundationForm_Msg13') }}
            <br>
          </div>
          <i class="el-icon-info"/>
        </el-tooltip>
      </div>
      <!--富文本编辑器-->
      <div class="table-container">
        <TinyMce
          :key="tinymceFlag"
          ref="tiny"
          :value="content"
          :toolbar="toolbar"
          width="100%"
          height="75vh"
          :drag-html="dragHtml"
          :menubar="''"
          @input="handleInput"
        />
        <div id="preview" style="display:none;"></div>
      </div>
    </div>
    <!--新增模板-->
    <add-template ref="add-temp" @addReportName="addReportName"/>
    <!--预览-->
    <preview ref="preview"/>
  </div>
</template>

<script>
import TinyMce from '@/views/report/operationalAnalysis/Tinymce';
import {
  getByTitle,
  addTemplate,
  getReportTempFile,
  updateTemplate
} from '@/api/report/baseReport/operationalAnalysis/templateConfig';
import dragContent from '@/views/report/operationalAnalysis/templateConfig/dragContent';
import dragTable from '@/views/report/operationalAnalysis/templateConfig/dragTable';
import dragChart from '@/views/report/operationalAnalysis/templateConfig/dragChart';
import AddTemplate from '@/views/report/operationalAnalysis/templateConfig/addTemplate'
import preview from '@/views/report/operationalAnalysis/templateConfig/preview';
export default {
  name: 'IndexConfig',
  components: { AddTemplate, TinyMce, dragContent, dragTable, dragChart, preview },
  props: {
    // 接口参数
    updateTemp: {
      type: Object,
      default() {
        return {}
      }
    },
    // 区分新增还是修改
    typeOperate: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      tinymceFlag: 1,
      activeNames: ['1'],
      showTree: true,
      loading: false,
      // 新增模板子组件传过来的模板名称
      addTempName: '',
      addTempType: '',
      // 模板名称列表
      templateNameList: [],
      // 要传给子组件的拖拽内容
      dragHtml: '',
      temp: {},
      defaultTemp: this.updateTemp,
      // 新增修改时，需要传给后台富文本中表格和echarts图表中的一些字段
      tempJson: {
        tables: [],
        echarts: []
      },
      // 对富文本中的html处理，主要是拖拽图表删除问题的处理
      tempHtmlString: '',
      // 富文本编辑器工具栏
      toolbar: [
        'formatselect fontselect fontsizeselect bold italic forecolor backcolor alignleft aligncenter alignright outdent indent searchreplace undo redo'
      ],
      // 富文本编辑器文本内容
      content: '',
      // 菜单切换后，content的内容会重新加载，如果富文本有修改，但是没有保存，切换后会刷新到未修改之前
      oldContent: '',
      // window.tinymce.activeEditor.getContent()失效，获取富文本内容
      getContent: '',
      // 如果是新增模板，富文本获得的内容没有头尾，需要自己添加（否则后台下xhtml要求比较高，识别出错）
      htmlStart: '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">\n' +
          '<html lang="en">\n' +
          '<head>\n' +
          '    <title>WangEditor Display HTML</title>\n' +
          '    <meta charset="UTF-8"/>\n' +
          '</head>\n' +
          '<body>',
      htmlEnd: '</body>\n' +
          '</html>'
    }
  },
  computed: {

  },
  activated() {
    this.tinymceFlag++
    // 页面重新加载时，将原本的数据赋值给content
    if (this.oldContent) {
      this.content = this.oldContent
    }
  },
  created() {
    this.resetTemp()
    this.getTemplate()
  },
  methods: {
    /**
     * 富文本内容发生变化时，将内容放进oldContent
     * */
    handleInput(data) {
      this.oldContent = this.htmlStart + data + this.htmlEnd
      this.getContent = data
      // console.log('change', data)
    },
    /**
     * 返回列表页面
     * */
    backListBtn() {
      this.$emit('BackTemp', true);
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 获取富文本编辑器模板内容（修改时才有模板名称）
     * */
    getTemplate() {
      if (this.typeOperate === 'update') {
        getReportTempFile(this.updateTemp).then(res => {
          // console.log('res', res.data)
          // echarts图表图片预处理(控制图片显示隐藏，后台需要的是变量字段，前端显示需要的是图片)
          const preHtml1 = res.data.replace(/imageBusinessImg" style="display: block/g, 'imageBusinessImg" style="display: none');
          const preHtml2 = preHtml1.replace(/class="imgShow" style="display: none/g, 'class="imgShow" style="display: block')
          const preHtml3 = preHtml2.replace(/class="dimBaseTypeDiv" style="display: none/g, 'class="dimBaseTypeDiv" style="display: block')
          const preHtml4 = preHtml3.replace(/class="openChartInterpretation" style="display: none/g, 'class="openChartInterpretation" style="display: block')
          const preHtml5 = preHtml4.replace(/class="chartInterpretation" style="display: block/g, 'class="chartInterpretation" style="display: none')
          this.content = preHtml5.replace(/compareMarkLabel" style="display: none/g, 'compareMarkLabel" style="display: block')
        })
      } else {
        this.content = '<div class="content">\n' +
          '<h2 style="text-align: center">' + this.$t('pages.foundationForm_Msg14') + '</h2>\n' +
          '<p style="padding-left: 20px">' + this.$t('pages.foundationForm_Msg15') + '<span style="color: #ff0000">' + this.$t('pages.foundationForm_Msg16') + '</span>，' +
          this.$t('pages.foundationForm_Msg17') + '<span style="color: #ff0000">' + this.$t('pages.foundationForm_Msg18') + '</span>' + this.$t('pages.foundationForm_Msg19') + '</p>\n' +
          '<p style="padding-left: 20px">' + this.$t('pages.foundationForm_Msg20') + '<span style="color: #ff0000">' + this.$t('pages.foundationForm_Msg21') + '</span>' +
          this.$t('pages.foundationForm_Msg22') + '</p>\n' +
          '<p style="padding-left: 20px">' + this.$t('pages.foundationForm_Msg23') + '</p>\n' +
          '<p style="padding-left: 20px">' + this.$t('pages.foundationForm_Msg24') + '<span style="color: #ff0000">${1}</span>' + this.$t('pages.foundationForm_Msg25') + '</p>\n' +
          '<p style="padding-left: 20px">' + this.$t('pages.foundationForm_Msg26') + '</p>\n' +
          '<p style="padding-left: 20px">' + this.$t('pages.foundationForm_Msg27') + '<span style="color: #ff0000">{{...}}</span>' + this.$t('pages.foundationForm_Msg28') + '<span style="color: #ff0000">[...]</span>' + this.$t('pages.foundationForm_Msg29') + '</p>\n' +
          '</div>'
      }
    },
    /**
       * 替换html文件中的dody中的内容
       * 富文本编辑器通过getContent()获取到的数据只有html，body中的内容，所以后台html返回的body前后可能存在一些特有的css等，都需要截取到
       * @param htmlString 原始的html内容
       * @param newContent 新的body中的内容（富文本编辑器中的内容，需插入到原本html的body中）
       * */
    replaceBodyContent(htmlString, newContent) {
      // console.log('newContent', newContent)
      // console.log('tempHtmlString', this.tempHtmlString)
      const bodyStartIndex = htmlString.indexOf('<body>') + '<body>'.length;
      const bodyEndIndex = htmlString.indexOf('</body>');
      if (bodyStartIndex !== -1 && bodyEndIndex !== -1) {
        // <body>及body前的元素 + 富文本编辑器的内容 + </body>及body后的元素
        return htmlString.substring(0, bodyStartIndex) + newContent + htmlString.substring(bodyEndIndex);
      } else {
        // 如果是新增，没有原始tml，需要前端自己拼接头尾
        return this.htmlStart + newContent + this.htmlEnd
      }
    },
    /**
       * 替换html文件中的图片
       * 1、富文本编辑器中的图片转html时的预处理（否则居中居右导出word存在bug）
       * 2、图片只是模板的显示，要提交给后台使用的是一个变量(后台模板替换需要：注：后台替换图表图片需要一个变量名称，而前端模板需要显示一个图片，方便用户理解)
       * @param originalHtml 原来的html，经过replaceBodyContent处理过的html
       * 处理原因：富文本编辑器中居中或居右的图片，生成的html转word格式都是居左的
       * 居中摸搜：富文本编辑器中设置的图片居中，只有在上一行文字居中，且居中完文字下方上传居中图片导出的word才是居中的，都在都在左边
       * 但是设置了居中的图片都有相同的样式display: block; margin-left: auto; margin-right: auto，所以字符串找出所有居中的图片，对图片的前一个元素设置style="text-align:center"才会居中
       * 居右摸搜：类似居中，居右的图片都有相同的样式float: right;，所以字段中找出所有居右的图片，对图片的前一个元素设置style="text-align:right"
       * */
    insertBeforeImg(originalHtml) {
      // replace('需要被替换的内容', '替换内容')
      // 1、居左居右处理
      let newHtmlString = ''
      // 输入方式的不同，可能导致包裹img的标签可能直接是<p>也可能是<p style="...">所以要区分替换
      // 图片居中处理
      newHtmlString = originalHtml.replace(/p><img style="display: block; margin-left: auto; margin-right: auto;/g, 'p style="text-align:center"><img style="display: block; margin-left: auto; margin-right: auto;');
      newHtmlString = newHtmlString.replace(/text-align: left;"><img style="display: block; margin-left: auto; margin-right: auto;/g, 'text-align: center;">><img style="display: block; margin-left: auto; margin-right: auto;');
      // 图片居右处理
      newHtmlString = newHtmlString.replace(/p><img style="float: right;/g, 'p style="text-align:right"><img style="float: right;');
      newHtmlString = newHtmlString.replace(/text-align: left;"><img style="float: right;/g, 'text-align: right;"><img style="float: right;');
      // console.log('newHtmlString', newHtmlString)

      // 2、给后台传变量处理，控制图片和变量显示隐藏
      newHtmlString = newHtmlString.replace(/imageBusinessImg" style="display: none/g, 'imageBusinessImg" style="display: block');
      newHtmlString = newHtmlString.replace(/class="imgShow" style="display: block/g, 'class="imgShow" style="display: none');
      newHtmlString = newHtmlString.replace(/compareMarkLabel" style="display: block/g, 'compareMarkLabel" style="display: none')
      newHtmlString = newHtmlString.replace(/openChartInterpretation" style="display: block/g, 'openChartInterpretation" style="display: none')
      // 创建一个新的DOM解析器
      const parser = new DOMParser();
      // 解析HTML字符串
      const doc = parser.parseFromString(newHtmlString, 'text/html');
      const dimBaseTypeDivElements = doc.querySelectorAll('.dimBaseTypeDiv')
      const openChartInterpretationElements = doc.querySelectorAll('.openChartInterpretation')
      // console.log('dimBaseTypeDivElements', dimBaseTypeDivElements)
      dimBaseTypeDivElements.forEach(function(element) {
        if (element.querySelector('div') || element.querySelector('.imgShow')) {
          element.style.display = 'block'
          // 这里可以执行你需要的操作，比如删除该元素或进行其他处理
        } else {
          element.style.display = 'none'
        }
      });
      // 保存到后台时，根据模板设置的是否显示图表解读，做显示隐藏
      openChartInterpretationElements.forEach(function(element) {
        if (element.querySelector('.isOpenChartInterpretation')) {
          const isOpen = element.querySelector('.isOpenChartInterpretation').innerText
          // 获取下一个兄弟元素
          const nextElement = element.nextElementSibling;
          if (isOpen === '1' && nextElement && nextElement.classList.contains('chartInterpretation')) {
            nextElement.style.display = 'block'
          } else {
            nextElement.style.display = 'none'
          }
        }
      });
      // 将doc转成字符串
      const serializer = new XMLSerializer();
      return serializer.serializeToString(doc)
    },
    /**
     * 另存为
     * */
    saveAsBtn() {
      this.$refs['add-temp'].show('另存为', 'saveAs')
    },
    /**
     * 新增模板确认按钮点击后（子组件传递到父组件的值）
     * @param name 模板名称
     * @param type 区分另存为
     * */
    addReportName(name, type) {
      if (name !== '') {
        // 重新初始化  清空模板名称的选择
        this.resetTemp()
        this.addTempName = name
        this.addTempType = type
      }
    },
    /**
       * 保存按钮点击
       * 新增或修改模板
       */
    saveBtn() {
      this.loading = true
      // 保存时需要的一些字段数据处理
      this.commitDataProcessing()
      // console.log('tempJson', JSON.parse(JSON.stringify(this.tempJson)));
      // console.log('this.temp', JSON.parse(JSON.stringify(this.temp)))
      const pattern = /[\\/:：*?？"”<>|]/;
      if (this.temp.title === '') {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.foundationForm_Msg3'), type: 'error', duration: 2000 })
        this.loading = false
        return false
      } else if (this.temp.title.length < 3) {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.foundationForm_Msg5'), type: 'error', duration: 2000 })
        this.loading = false
        return false
      } else if (pattern.test(this.temp.title)) {
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.foundationForm_Msg30') + '：\\ / : * ? " < > |', type: 'error', duration: 2000 })
        this.loading = false
        return false
      } else if (this.tempJson.echarts.length > 0) {
        const echartsJson = this.tempJson.echarts
        let isMsg = false
        echartsJson.forEach(item => {
          const itemType = item.dimBaseType
          if (itemType != '') {
            if (!/^[1-5]$/.test(itemType)) {
              isMsg = true
            }
          }
        })
        if (isMsg) {
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.foundationForm_Msg40'), type: 'error', duration: 2000 })
          this.loading = false
          return false
        }
      }
      getByTitle({ id: this.temp.id, title: this.temp.title }).then(respond => {
        if (respond.data === true) {
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.foundationForm_Msg4'), type: 'error', duration: 2000 })
          this.loading = false
          return false
        } else {
          const formData = new FormData()
          // 上传模板文件(传html文件)  insertBeforeImg对html中的图片处理
          // formData.append('uploadFile', new Blob([this.insertBeforeImg(updatedHtml)], { type: 'text/html' }))

          // 将处理后的html传给后台
          const updatedHtml1 = this.replaceBodyContent(this.content, this.tempHtmlString);
          // console.log('tempJson', JSON.parse(JSON.stringify(this.tempJson)))
          // console.log('this.insertBeforeImg(updatedHtml11111)', this.insertBeforeImg(updatedHtml1))
          formData.append('uploadFile', new Blob([this.insertBeforeImg(updatedHtml1)], { type: 'text/html' }))
          // console.log('type保存', this.addTempType)
          // 1、模板名称未选中，代表新增，如果addTempType === 'saveAs'，代表另存为（实际也是新增）
          // console.log('this.typeOperate', this.typeOperate)
          // console.log('this.addTempType', this.addTempType)
          if (this.typeOperate === 'add' || this.addTempType === 'saveAs') {
            // 子组件传过来的模板名称 this.addTempName
            if (this.addTempName !== '') {
              formData.append('title', this.addTempName)
            } else {
              formData.append('title', this.temp.title)
            }
            formData.append('tempJson', JSON.stringify(this.tempJson))
            addTemplate(formData).then(res => {
              this.loading = false
              this.addTempType = ''
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.saveSuccess'), type: 'success', duration: 2000 })
              this.backListBtn()
            }).catch(res => {
              this.loading = false
              this.addTempType = ''
            })
          } else {
            // 2、修改
            formData.append('id', this.temp.id)
            formData.append('title', this.temp.title)
            formData.append('templateFileName', this.temp.templateFileName)
            formData.append('tempJson', JSON.stringify(this.tempJson))
            updateTemplate(formData).then(res => {
              this.loading = false
              this.addTempType = ''
              this.$notify({ title: this.$t('text.success'), message: this.$t('text.saveSuccess'), type: 'success', duration: 2000 })
              this.backListBtn()
            }).catch(res => {
              this.loading = false
              this.addTempType = ''
            })
          }
        }
      })
      // console.log('this.addTempName', this.addTempName)
      // console.log('保存按钮点击', JSON.parse(JSON.stringify(this.temp)))
      // console.log('保存按钮点击tempJson', JSON.parse(JSON.stringify(this.tempJson)))
    },
    /**
       * 将图片地址改成base64位（不转base64,导出docx图片不显示）
       * @param url 需要转换的图片地址
       * */
    convertImageToBase64(url, callback) {
      fetch(url)
        .then(response => response.blob())
        .then(blob => {
          const reader = new FileReader();
          reader.onload = e => callback(e.target.result);
          reader.readAsDataURL(blob);
        })
        .catch(error => console.error('Error converting image to Base64:', error));
    },
    /**
       * 拖拽到富文本编辑器中
       * 获取子组件传值，将(echarts图表)显示在富文本编辑器中
       * 添加报表图表内容
       * */
    chartHtmlChild(data) {
      // console.log('node.data', JSON.parse(JSON.stringify(data)))
      if (data !== '' && data.label !== undefined && data.type !== undefined) {
        const timestamp = new Date().getTime();
        const chartImg = 'chart-' + data.type + '.png'
        const src = require('@/assets/' + chartImg)
        const imgType = data.type.replace(/chart-/g, '')
        const imgCode = '{{@img' + data.dataId + '_' + timestamp + '}}'
        const imgCharInterpretation = '{{~img' + data.dataId + '_' + 'explain}}'
        // console.log('imgCode', imgCode)
        let dimBaseType = ''
        let tips = ''
        const tipsSrc = require('@/assets/tips.png')
        const titleTips = this.$t('pages.foundationForm_Msg41')
        if (data.type === 'line') {
          dimBaseType = '{{2}}'
          tips = `<span title="${titleTips}"><img src="${tipsSrc}" style="position: absolute;width: 24px;bottom: -4px;"/></span>`
        } else {
          dimBaseType = ''
          tips = ''
        }
        // 排序项
        let sortNameHtml = ''
        if (data.resultDefault !== '') {
          sortNameHtml = `<div class="compareMark" style="display: block;font-size: 12px;line-height: 25px;font-weight: 400;text-align: left"><label class="compareMarkLabel" style="display: block;color: #ff0000;">注：您可通过下方配置实现自定义排序项,可选排序参数有：${data.resultTips}，只可以选择一项指标作为排序项</label></div>
            <div class="sortNameParameterChart" style="display: block;color: #000000;font-weight: 400;text-align: left"><label class="compareMarkLabel" style="display: block;">sortName:${data.resultDefault}</label></div>`
        }
        // 图表解读（暂时限制只有几张敏感报表有该功能）
        let chartInterpretationHtml = ''
        if (data.dataId === '51' || data.dataId === '52' || data.dataId === '53' || data.dataId === '54' || data.dataId === '55' || data.dataId === '56' || data.dataId === '60') {
          chartInterpretationHtml = `<div class="openChartInterpretation" style="display: block;margin-top: 10px">是否显示图表解读？0：不显示，1：显示。{{<span class="isOpenChartInterpretation">1</span>}}</div>
        <div class="chartInterpretation" style="display: none">${imgCharInterpretation}</div>`
        }
        // console.log('imgCode', imgCode)
        // 图片地址转base64，因为从外面拖拽进富文本编辑器的图片，在富文本获取html内容时，图片地址不会自动转base64,导致html转word时，图片不显示
        this.convertImageToBase64(src, base64 => {
          // 图片里面内置的字段可能后台需要也可能前端需要
          this.dragHtml = `<div style="width: 100%;" class="dragImageAllDiv">
                             <div style="text-align: center;font-size: 15px;font-weight: bold;height: 40px;line-height: 40px">${data.label}</div>
                             <div class="imageSrc" style="text-align: center;">
                               <div class="imageType" style="display: none">${imgType}</div>
                               <div class="imageCode" style="display: none">${data.dataId}</div>
                               <div class="imageBusinessImg" style="display: none;text-align: center;">${imgCode}</div>
                               <div class="dimBaseTypeDiv" style="display: block;position:relative;text-align: center">${dimBaseType}${tips}</div>
                               <img class="imgShow" style="display: block;width: 80px;height: 80px;margin: auto" src="${base64}"/>
                                ${sortNameHtml}
                                ${chartInterpretationHtml}
                             </div>
                           </div><div></div><div></div>`
        });
      } else {
        // 拖拽结束后要清空
        this.dragHtml = ''
      }
    },
    /**
       * 拖拽到富文本编辑器中(报表内容和表格)
       * */
    contentHtmlChild(contentHtml) {
      // console.log('contentHtml', contentHtml)
      if (contentHtml !== '') {
        this.dragHtml = contentHtml
      } else {
        // 拖拽结束后要清空
        this.dragHtml = ''
      }
    },
    /**
       * 获取{{...}}中间的内容（保存时，对富文本中表格处理，获取到需要的参数tempJson）
       * */
    getContentBetweenBraces(str) {
      const pattern = /\{\{(.*?)\}\}/g;
      const matches = str.match(pattern);
      if (matches) {
        return matches.map(match => match.replace(/\{\{|\}\}/g, ''));
      }
      return [];
    },
    /**
       * 获取[...]中间的内容（提交时，对富文本中表格处理，获取到需要的参数tempJson）
       * */
    getContentBetweenBrackets(str) {
      const matches = str.match(/\[([^\]]+)\]/g);
      return matches ? matches.map(s => s.slice(1, -1)) : []; // 去掉首尾的括号
    },
    /**
       * echarts图表数据处理（获取提交参数tempJson）
       * @param doc:html字符串解析出来的数据
       * */
    echartsDataProcessing(doc) {
      // console.log('doc111', doc)
      const imageType = doc.querySelectorAll('.imageType')
      // console.log('imageType', imageType)
      const echData = []
      if (imageType.length > 0) {
        for (let i = 0; i < imageType.length; i++) {
          const obj = {}
          const imageTypeDiv = imageType[i].innerText
          const imageCodeDiv = imageType[i].nextElementSibling.innerText
          const imageBusinessCodeDiv = imageType[i].nextElementSibling.nextElementSibling.innerText
          const dimBaseTypeValue = imageType[i].nextElementSibling.nextElementSibling.nextElementSibling.innerText
          // console.log('imageBusinessCodeDiv', imageBusinessCodeDiv)
          obj.picType = imageTypeDiv
          obj.imgCode = imageCodeDiv
          obj.businessCode = imageBusinessCodeDiv
          // 拖拽进去的趋势图表，如果存在两个code一样，报表日期不一样的趋势图，生成的报表生成的结果是一样的
          // 前端保存数据的时候，在原来基础上添加一个echartCode字段，传递一个后台需要格式的字符串，用于区分
          obj.echartCode = imageBusinessCodeDiv.slice(3, -2)
          // 获取{{}}中间的内容，并转成字符串
          obj.dimBaseType = this.getContentBetweenBraces(dimBaseTypeValue).toString()

          // echarts图表添加排序项功能
          const parent = imageType[i].parentNode;
          // 在父节点下查找 class 为 'sortNameParameterChart' 的元素
          const sortNameParameterCharts = parent.querySelectorAll('.sortNameParameterChart');
          if (sortNameParameterCharts.length > 0) {
            const sortNameText = sortNameParameterCharts[0].innerText.replace(/\n/g, '')
            const parts = sortNameText.split(':');
            if (parts.length === 2) {
              const key = parts[0].trim(); // 获取键, trim()用于去除可能的空格
              // 将键值对添加到对象中
              obj[key] = parts[1].trim();
            }
          }
          // echarts 图表解读  是否开启
          const chartInterpretation = parent.querySelectorAll('.isOpenChartInterpretation');
          if (chartInterpretation.length > 0) {
            obj.isExplain = chartInterpretation[0].innerText
          }
          echData.push(obj)
          // console.log('imageBusinessCodeDiv', imageBusinessCodeDiv)
          // console.log('imageTypeDiv', imageTypeDiv)
        }
      }
      this.tempJson.echarts = echData
      // console.log('要传给后台的表格数据echartsData', JSON.parse(JSON.stringify(echData)))
    },
    /**
     * 对比表格
     * 判断对比表格后面是否存在class名称为qoq或yoy的div
     * @param type 区分qoq或yoy
     * @param element 表格元素
     * */
    isCompareTableHasClassName(type, element) {
      // 假设tableElement是你要检查的表格元素
      // 假设tableElement的直接父元素包含多个子节点，其中一些可能是元素节点
      // 使用tableElement找到其父元素
      const parentElement = element.parentElement;
      // 使用childNodes属性获取所有子节点
      if (parentElement) {
        const childNodes = parentElement.childNodes;
        // 使用some方法检查是否存在具有特定类名的子元素
        return Array.prototype.some.call(childNodes, function(node) {
          // 确保节点是元素节点
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 检查元素的classList是否包含'qoq'或'yoy'
            return node.classList.contains(type);
          }
          return false;
        });
      }
      return false
    },
    /**
     * 获取表格排序项（获取表格下，第一个兄弟class="sortNameParameter"的div）
     * @param tableEl 表格table元素
     * */
    findClosestSortDivAfterTable(tableEl) {
      let sibling = tableEl.nextElementSibling;
      while (sibling) {
        if (
          sibling.nodeType === Node.ELEMENT_NODE && // 确保是元素节点
      sibling.tagName.toLowerCase() === 'div' && // 是 div
      sibling.classList.contains('sortNameParameter') // class 匹配
        ) {
          return sibling; // 找到了，返回
        }
        sibling = sibling.nextElementSibling; // 继续下一个兄弟节点
      }
      return null; // 没有找到符合条件的 div
    },
    /**
       * 表格数据处理（提交参数）
       * @param doc:html字符串解析出来的数据
       * */
    tableDataProcessing(doc) {
      // 获取所有的table元素
      const tables = doc.getElementsByTagName('table');
      // 表格字段处理（将得到的数据处理成后台需要的格式）
      const tablesData = []
      // console.log('tables', tables)
      /**
       * 遍历所有的table
       * 获取每个table中的td内容,以及每张table
       * */
      const tdContents = [];
      for (let i = 0; i < tables.length; i++) {
        const tableTds = [];
        const obj = {}
        const tds = tables[i].getElementsByTagName('td');
        for (let j = 0; j < tds.length; j++) {
          tableTds.push(tds[j].textContent);
          obj.taCol = tableTds
          obj.table = tables[i]
        }
        tdContents.push(obj);
      }
      // console.log('tdContents555555', JSON.parse(JSON.stringify(tdContents)));
      // 遍历tdContents,请所有表格下的td文本
      /**
       * 遍历tdContents
       * taCol：表格中每一个td中的内容，用于传递参数policyName
       * taCol：富文本中所有表格，用于对比表格中计算变化率表达式qoqDataPercent和yoyDataPercent的获取
       */
      for (let i = 0; i < tdContents.length; i++) {
        // console.log('tdContents[i]', tdContents[i])
        const obj = {}
        // 表格的第一列为businessCode
        // obj.businessCode = tdContents[i][0]
        // 将数组转成字符串，方便获取到每个td中{{}}和[]中间的内容，用于传给后台的参数
        const str = tdContents[i].taCol.toString();
        const tableElement = tdContents[i].table;
        // console.log('str', str)
        // console.log('this.getContentBetweenBraces(str).toString()', this.getContentBetweenBraces(str).toString())
        // 获取到{{}}中间的内容
        let isCompare = false
        if (this.getContentBetweenBraces(str).toString() !== '') {
          // 如果长度>1，说明是对比表格，普通表格policyName只需要传第一个值，对比表格需要传dateType区分同环比
          const braces = this.getContentBetweenBraces(str)
          // console.log('braces', braces)
          if (braces.length > 1) {
            const type = braces[braces.length - 1].toLowerCase()
            isCompare = true
            obj.policyName = this.getContentBetweenBraces(str)[0].toString()
            obj.dateType = type

            // 判断对比表格下方是否存在class名为qoq或yoy相关div元素
            const qoqClass = this.isCompareTableHasClassName('qoq', tableElement)
            const yoyClass = this.isCompareTableHasClassName('yoy', tableElement)
            const defaultQoq = 'qoqData == 0 ? (data == 0 ? \'0\' : \'∞\') : T(java.lang.String).format(\'%.2f%%\',(data-qoqData)*100.0/qoqData)'
            const defaultYoy = 'yoyData == 0 ? (data == 0 ? \'0\' : \'∞\') : T(java.lang.String).format(\'%.2f%%\',(data-yoyData)*100.0/yoyData)'
            if (qoqClass) {
              // 如果存在class，则获取该class对应的元素及该元素下的文本内容
              const elementWithClass = [...tableElement.parentElement.children].find(child => child.classList.contains('qoq'))
              // 可能会存在div下不全是想要的内容，删掉
              const divsToRemove = elementWithClass.querySelectorAll('div, table');
              divsToRemove.forEach(function(element) {
                element.remove();
              });
              const qoqText = elementWithClass.innerText.replace(/\n/g, '')
              // 如果文本不为空，赋值给后台，否则使用默认值
              if (qoqText.trim() !== '') {
                obj.qoqDataPercent = qoqText
              } else {
                obj.qoqDataPercent = defaultQoq
              }
            } else {
              obj.qoqDataPercent = defaultQoq
            }
            // yoy与qoq一致
            if (yoyClass) {
              // console.log('yoyClass', yoyClass)
              const elementWithClass = [...tableElement.parentElement.children].find(child => child.classList.contains('yoy'))
              // 可能会存在div下不全是想要的内容，删掉
              const divsToRemove = elementWithClass.querySelectorAll('div, table');
              divsToRemove.forEach(function(element) {
                element.remove();
              });
              const yoyText = elementWithClass.innerText.replace(/\n/g, '')
              if (yoyText.trim() !== '') {
                obj.yoyDataPercent = yoyText
              } else {
                obj.yoyDataPercent = defaultYoy
              }
            } else {
              obj.yoyDataPercent = defaultYoy
            }
          } else {
            obj.policyName = this.getContentBetweenBraces(str).toString()
            // 自定义排序项参数
            // const elementWithClass = [...tableElement.parentElement.children].find(child => child.classList.contains('sortNameParameter'))
            const elementWithClass = this.findClosestSortDivAfterTable(tableElement)
            if (elementWithClass) {
              // 可能会存在div下不全是想要的内容，删掉
              const divsToRemove = elementWithClass.querySelectorAll('div, table');
              divsToRemove.forEach(function(element) {
                element.remove();
              });
              const sortNameText = elementWithClass.innerText.replace(/\n/g, '')
              // 使用正则表达式或字符串操作来分割字符串得到键和值
              const parts = sortNameText.split(':');
              if (parts.length === 2) {
                const key = parts[0].trim(); // 获取键, trim()用于去除可能的空格
                // 将键值对添加到对象中
                obj[key] = parts[1].trim();
              }
            }
          }
        }
        // 获取到[]中间的内容
        if (this.getContentBetweenBrackets(str).toString() !== '') {
          if (isCompare) {
            // 对比表格不需要传col
            obj.col = ''
          } else {
            obj.col = this.getContentBetweenBrackets(str).toString()
          }
        }
        if (JSON.stringify(obj) !== '{}') {
          tablesData.push(obj)
        }
      }
      this.tempJson.tables = tablesData
      // console.log('要传给后台的表格数据tables：', JSON.parse(JSON.stringify(tablesData)))
    },
    /**
       * 提交时数据处理
       * 用于提交时参数tempJson传递及提交时html字符串的处理
       * */
    commitDataProcessing() {
      // 富文本编辑器中的内容替换body中的内容
      let updatedHtml = this.content
      if (this.getContent !== '') {
        updatedHtml = this.replaceBodyContent(this.content, this.getContent)
      }
      // console.log('updatedHtml', updatedHtml)
      // console.log('this.insertBeforeImg(updatedHtml)', this.insertBeforeImg(updatedHtml))
      // 创建一个新的DOM解析器
      const parser = new DOMParser();
      // 解析HTML字符串
      const doc = parser.parseFromString(this.insertBeforeImg(updatedHtml), 'text/html');
      // 1、拖拽进来的echarts图表如果在富文本编辑器上删除，需要将内置的信息也删除掉
      const imageSrcDiv = doc.querySelectorAll('.imageSrc')
      if (imageSrcDiv.length > 0) {
        imageSrcDiv.forEach(item => {
          const isImg = item.querySelector('.imgShow')
          const IsImgType = item.querySelector('.imageType')
          if (isImg === null && IsImgType !== null) {
            // 如果不存在图片，但是存在内置信息，需要将内置信息也删除
            item.parentNode.removeChild(item)
          }
        })
      }
      // 删除后的dom转字符串，新增修改保存时使用
      // 由于后台需要img标签必须有结尾符，所以对img单独处理
      let bodyContent = doc.body.innerHTML
      // 正则表达式用来查找不完整的<img>标签
      const regex = /<img[^>]*>/gi;
      // 修复不完整的<img>标签
      bodyContent = bodyContent.replace(regex, function(match) {
        if (!/<\/img>/.test(match)) {
          return match + '</img>';
        }
        return match;
      });
      // 然后你可以将修复后的bodyContent发送到后端
      this.tempHtmlString = bodyContent
      // console.log('this.tempHtmlString获取到的', this.tempHtmlString)
      // 表格数据处理
      this.tableDataProcessing(doc)
      // echarts图表数据处理
      this.echartsDataProcessing(doc)
    },
    /**
       * 预览
       */
    previewWord() {
      let updatedHtml = this.content
      if (this.getContent !== '') {
        updatedHtml = this.replaceBodyContent(this.content, this.getContent)
      }
      const resHtml = this.insertBeforeImg(updatedHtml)
      const preHtml1 = resHtml.replace(/imageBusinessImg" style="display: block/g, 'imageBusinessImg" style="display: none');
      const preHtml2 = preHtml1.replace(/class="imgShow" style="display: none/g, 'class="imgShow" style="display: block')
      const preHtml3 = preHtml2.replace(/class="openChartInterpretation" style="display: block/g, 'class="openChartInterpretation" style="display: none')
      const preHtml4 = preHtml3.replace(/class="chartInterpretation" style="display: block/g, 'class="chartInterpretation" style="display: none')
      this.$refs['preview'].show(preHtml4)
    }
  }
}
</script>
<style>
  .mce-container-body>.mce-edit-area>iframe>html>body{
    background: transparent !important;
    color: #bbbbbb !important;
  }
</style>
<style lang='scss' scoped>
  >>>.mce-container-body >>>.panel-body{
    background: transparent !important;
    color: #bbbbbb !important;
  }
  /*折叠面板样式设置*/
  >>>.el-collapse-item__header{
    background: transparent;
    color: #409EFF;
    font-weight: bold;
    border-bottom: 1px solid #666666;
    padding-left: 5px;
  }
  >>>.el-collapse-item__wrap{
    background: transparent;
    border-bottom: 1px solid #666666;
  }
  /*左侧折叠面板下的树设置*/
  >>>.tree-menu{
    border: 0;
  }
  >>>.el-tree-node__content{
    cursor: move;
  }
</style>
