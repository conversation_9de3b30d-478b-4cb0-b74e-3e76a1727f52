<template>
  <tree-menu
    ref="dataIndexTree1"
    :data="indexContentTreeList"
    :height="200"
    :accordion="false"
    node-key="id"
    is-filter
    draggable
    :allow-drop="allowDrop"
    :allow-drag="allowDrag"
    :render-content="renderChartContent"
    @node-drag-start="handleDragStart"
    @node-drag-end="handleDragEnd"
  />
</template>

<script>
import { getTemplateFragmentList } from '@/api/report/baseReport/operationalAnalysis/templateConfig';

export default {
  name: 'DragContent',
  components: { },
  data() {
    return {
      // 报表指标内容
      indexContentTreeList: [],
      dragHtml: ''
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.indexContentTree()
  },
  methods: {
    /**
     * 指标内容树结构获取
     * */
    indexContentTree() {
      getTemplateFragmentList().then(res => {
        // console.log('内容', JSON.parse(JSON.stringify(res.data)))
        this.indexContentTreeList = res.data
      })
    },
    /**
     * 自定义treeMenu图标
     * */
    renderChartContent(h, { node, data, store }) {
      let type
      // console.log('data.type', data)
      if (data.children) {
        type = 'text1'
      } else {
        type = 'text2'
      }
      return (
        <div>
          <span title={node.label}>
            <svg-icon style={({ marginRight: '10px' })} icon-class={type}/>
            {node.label}
          </span>
        </div>
      )
    },
    /**
     * 设置节点可以被拖拽
     * */
    allowDrag() {
      return true;
    },
    /**
     * 拖拽时限制模板节点不能被放置
     * */
    allowDrop() {
      return false;
    },
    /**
     * 拖拽开始
     * 添加报表图表数据内容（处理）
     * */
    handleDragStart(node) {
      // console.log('node', node)
      if (node.data) {
        this.$emit('contentHtml', node.data.str)
      }
      // let contentString;
      // if (node.data.oriData) {
      //   // 如果拖拽子节点，将该节点拖拽到富文本中
      //   contentString = node.data.oriData.str.replace('%s', '${' + node.data.oriData.id + '}')
      // } else {
      //   // 如果拖拽父节点，将父节点下的子节点全部拖拽到富文本中
      //   contentString = node.data.children.map(item => item.oriData.str.replace('%s', '${' + item.oriData.id + '}')).join(',')
      // }
      //   // 传递给父组件，用于拖拽显示
      //   this.$emit('contentHtml', contentString)
    },
    /**
     * 拖拽结束
     * 如果拖拽结束后，不清空，在页面上随便拖拽内容，还会添加之前最后一次拖拽的内容
     * */
    handleDragEnd() {
      setTimeout(() => {
        // 传递给父组件，用于拖拽显示
        this.$emit('contentHtml', '')
      }, 1000)
    }
  }
}
</script>
