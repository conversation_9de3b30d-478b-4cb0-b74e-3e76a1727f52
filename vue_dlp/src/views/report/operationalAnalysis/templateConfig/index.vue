<template>
  <div>
    <index-list v-if="showCurrent" @AddTemp="AddTemp"/>
    <index-config v-if="!showCurrent" :type-operate="typeOperate" :update-temp="updateTemp" @BackTemp="BackTemp"/>
  </div>
</template>

<script>
import indexList from './indexList';
import indexConfig from './indexConfig';
export default {
  name: 'TemplateConfig',
  components: { indexList, indexConfig },
  data() {
    return {
      typeOperate: '',
      updateTemp: {
        id: '',
        isLib: 0,
        templateFileName: '',
        title: ''
      },
      // addTemp,editTemp
      showCurrent: true
    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  methods: {
    AddTemp(isShow, type, option) {
      this.typeOperate = type
      this.showCurrent = isShow
      // console.log('isShow', isShow)
      // console.log('type', type)
      // console.log('option', option)
      this.updateTemp = option
    },
    BackTemp(msg) {
      this.showCurrent = msg
      // console.log('msg', msg)
    }
  }
}
</script>
