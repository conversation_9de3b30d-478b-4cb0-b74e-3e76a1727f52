<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <el-card class="box-card" style="height: 100%;border: 1px solid #666666;">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.foundationForm') }}</span>
        </div>
        <div v-for="(item,index) in templateNameList" :key="item.id" :value="item.templateFileName" class="text item" :class="active==index ? 'activeClass' : '  ' ">
          <span @click="tempNameClick(index,$event,item)">{{ item.title }}</span>
        </div>
      </el-card>
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="showTree = !showTree">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addTemplateConfig') }}
        </el-button>
        <el-button size="mini" @click="previewWord">
          <svg-icon icon-class="eye-open" />  {{ $t('pages.overview') }}
        </el-button>
        <el-tooltip slot="tooltip" effect="dark" placement="bottom" >
          <div slot="content">
            {{ $t('pages.foundationForm_Msg1') }}
          </div>
          <i class="el-icon-info"/>
        </el-tooltip>
      </div>
      <div class="table-container" style="border: 1px solid #666666; overflow:auto;background: #ffffff;color:#000000;padding: 0 10px" v-html="templateContent"></div>
    </div>
    <!--新增模板-->
    <add-template ref="add-temp"/>
    <!--预览-->
    <preview ref="preview"/>
  </div>
</template>

<script>
import { getReportTempFile, getTemplateLibList } from '@/api/report/baseReport/operationalAnalysis/templateConfig';
import addTemplate from '@/views/report/operationalAnalysis/templateLibrary/addTemplate';
import preview from '@/views/report/operationalAnalysis/templateConfig/preview'
export default {
  name: 'TemplateLibrary',
  components: { addTemplate, preview },
  data() {
    return {
      // 默认选中第一个样式
      active: 0,
      showTree: true,
      // 模板名称列表
      templateNameList: [],
      temp: {},
      defaultTemp: {
        id: undefined,
        isLib: 1,
        // 模板名称
        title: '',
        // 模板文件名（唯一）
        templateFileName: ''
      },
      // 模板内容(页面显示，对后台返回的有处理后在显示)
      templateContent: '',
      // 模板内容(传给后台，原始的)
      templateContentStr: ''
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.resetTemp()
    this.getTempList()
  },
  methods: {
    resetTemp() {
      this.templateContent = ''
      this.templateContentStr = ''
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 模板名称点击
     * @param index 索引
     * @param event 当前点击事件
     * @param item 当前点击行的一些信息
     * */
    tempNameClick: function(index, event, item) {
      this.active = index // 点击时，切换选中索引
      // console.log('event', event)
      // console.log('item', item)
      this.getReportTemp(item)
    },
    /**
     * 获取模板名称
     */
    getTempList() {
      getTemplateLibList().then(res => {
        // console.log('res模板名称，下拉列表', JSON.parse(JSON.stringify(res)))
        // 下拉列表获取
        this.templateNameList = res.data.items
        if (res.data.items.length > 0) {
          this.getReportTemp(res.data.items[0])
        }
      })
    },
    /**
     * 获取模板（预览显示在右边的内容）
     * @param currentSelect 当前选中模板名称信息
     */
    getReportTemp(currentSelect) {
      this.resetTemp()
      this.temp = currentSelect
      // console.log(' this.temp', this.temp)
      this.temp.isLib = 1
      getReportTempFile(this.temp).then(res => {
        // console.log('res模板', res.data)
        this.templateContentStr = res.data
        // 对一些echarts图表的处理，处理完在显示在页面上
        const preHtml1 = res.data.replace(/imageBusinessImg" style="display: block/g, 'imageBusinessImg" style="display: none');
        const preHtml2 = preHtml1.replace(/class="imgShow" style="display: none/g, 'class="imgShow" style="display: block')
        const preHtml3 = preHtml2.replace(/class="dimBaseTypeDiv" style="display: none/g, 'class="dimBaseTypeDiv" style="display: block')
        const preHtml4 = preHtml3.replace(/class="openChartInterpretation" style="display: none/g, 'class="openChartInterpretation" style="display: block')
        this.templateContent = preHtml4.replace(/compareMark" style="display: none/g, 'compareMark" style="display: block')
      })
    },
    /**
     * 新增模板点击，显示弹框，新增模板名称
     * */
    handleCreate() {
      this.$refs['add-temp'].show(this.templateContentStr, this.temp.title)
    },
    /**
     * 预览
     */
    previewWord() {
      const resHtml = this.templateContentStr
      const preHtml1 = resHtml.replace(/imageBusinessImg" style="display: block/g, 'imageBusinessImg" style="display: none');
      const preHtml2 = preHtml1.replace(/class="imgShow" style="display: none/g, 'class="imgShow" style="display: block')
      const preHtml3 = preHtml2.replace(/compareMarkLabel" style="display: block/g, 'compareMarkLabel" style="display: none')
      const preHtml4 = preHtml3.replace(/class="openChartInterpretation" style="display: block/g, 'class="openChartInterpretation" style="display: none')
      const preHtml5 = preHtml4.replace(/class="chartInterpretation" style="display: block/g, 'class="chartInterpretation" style="display: none')
      // 创建一个新的DOM解析器
      const parser = new DOMParser();
      // 解析HTML字符串
      const doc = parser.parseFromString(preHtml5, 'text/html');
      const dimBaseTypeDivElements = doc.querySelectorAll('.dimBaseTypeDiv')
      // console.log('dimBaseTypeDivElements', dimBaseTypeDivElements)
      dimBaseTypeDivElements.forEach(function(element) {
        if (element.querySelector('div') || element.querySelector('.imgShow')) {
          element.style.display = 'block'
          // 这里可以执行你需要的操作，比如删除该元素或进行其他处理
        } else {
          element.style.display = 'none'
        }
      });
      // 将doc转成字符串
      const serializer = new XMLSerializer();
      const preHtml6 = serializer.serializeToString(doc)
      this.$refs['preview'].show(preHtml6)
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-card__body{
    height: 85%;
    width: 100%;
    overflow: auto;
    .text{
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      cursor: pointer;
      span{
        display: inline-block;
        width: 100%;
        height: 100%;
      }
    }
  }
  .activeClass{
    color:#409EFF
  }
</style>
