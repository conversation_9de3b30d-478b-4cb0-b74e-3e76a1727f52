<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.addTemplateConfig')"
      :visible.sync="dialogVisible"
      width="400px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 350px;">
        <FormItem :label="$t('pages.templateName')" prop="title">
          <el-input v-model="temp.title" maxlength="60"></el-input>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="createData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogVisible=false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getByTitle, addTemplate } from '@/api/report/baseReport/operationalAnalysis/templateConfig'

export default {
  name: 'AddTemplate',
  data() {
    return {
      dialogVisible: false,
      loading: false,
      temp: {},
      defaultTemp: {
        title: '',
        uploadFileString: ''
      },
      // 新增修改时，需要传给后台富文本中表格和echarts图表中的一些字段
      tempJson: {
        tables: [],
        echarts: []
      },
      rules: {
        title: [
          { required: true, message: this.$t('pages.foundationForm_Msg3'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    /**
     * 弹框显示
     * @param content 模板html文件
     * @param title 当前选中的模板名称
     * */
    show(content, title) {
      this.resetTemp()
      // console.log('title', title)
      // console.log('content', content)
      this.temp.title = title
      this.temp.uploadFileString = content
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    /**
     * 替换html文件中的图片
     * 1、富文本编辑器中的图片转html时的预处理（否则居中居右导出word存在bug）
     * 2、图片只是模板的显示，要提交给后台使用的是一个变量(后台模板替换需要：注：后台替换图表图片需要一个变量名称，而前端模板需要显示一个图片，方便用户理解)
     * @param originalHtml 原来的html，经过replaceBodyContent处理过的html
     * 处理原因：富文本编辑器中居中或居右的图片，生成的html转word格式都是居左的
     * 居中摸搜：富文本编辑器中设置的图片居中，只有在上一行文字居中，且居中完文字下方上传居中图片导出的word才是居中的，都在都在左边
     * 但是设置了居中的图片都有相同的样式display: block; margin-left: auto; margin-right: auto，所以字符串找出所有居中的图片，对图片的前一个元素设置style="text-align:center"才会居中
     * 居右摸搜：类似居中，居右的图片都有相同的样式float: right;，所以字段中找出所有居右的图片，对图片的前一个元素设置style="text-align:right"
     * */
    insertBeforeImg(originalHtml) {
      // replace('需要被替换的内容', '替换内容')
      // 1、居左居右处理
      let newHtmlString = ''
      // 输入方式的不同，可能导致包裹img的标签可能直接是<p>也可能是<p style="...">所以要区分替换
      // 图片居中处理
      newHtmlString = originalHtml.replace(/p><img style="display: block; margin-left: auto; margin-right: auto;/g, 'p style="text-align:center"><img style="display: block; margin-left: auto; margin-right: auto;');
      newHtmlString = newHtmlString.replace(/text-align: left;"><img style="display: block; margin-left: auto; margin-right: auto;/g, 'text-align: center;">><img style="display: block; margin-left: auto; margin-right: auto;');
      // 图片居右处理
      newHtmlString = newHtmlString.replace(/p><img style="float: right;/g, 'p style="text-align:right"><img style="float: right;');
      newHtmlString = newHtmlString.replace(/text-align: left;"><img style="float: right;/g, 'text-align: right;"><img style="float: right;');
      // console.log('newHtmlString', newHtmlString)

      // 2、给后台传变量处理，控制图片和变量显示隐藏
      newHtmlString = newHtmlString.replace(/imageBusinessImg" style="display: none/g, 'imageBusinessImg" style="display: block');
      newHtmlString = newHtmlString.replace(/class="imgShow" style="display: block/g, 'class="imgShow" style="display: none');
      newHtmlString = newHtmlString.replace(/class="dimBaseTypeDiv" style="display: block/g, 'class="dimBaseTypeDiv" style="display: none');
      newHtmlString = newHtmlString.replace(/compareMarkLabel" style="display: block/g, 'compareMarkLabel" style="display: none')
      return newHtmlString
    },
    /**
     * 确认按钮点击
     * 将当前基础模板添加到模板配置菜单的模板名称列表中去
     */
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          // 保存时需要的tempJson字段数据处理
          this.commitDataProcessing()
          const formData = new FormData()
          // 将html传给后台
          formData.append('uploadFile', new Blob([this.insertBeforeImg(this.temp.uploadFileString)], { type: 'text/html' }))
          formData.append('title', this.temp.title)
          formData.append('tempJson', JSON.stringify(this.tempJson))
          // console.log('formData', formData)
          // console.log('this.tempJson', JSON.parse(JSON.stringify(this.tempJson)))
          // console.log('this.temp.title', this.temp.title)
          addTemplate(formData).then(res => {
            this.loading = false
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.insertSuccess'), type: 'success', duration: 2000 })
          }).catch(res => {
            this.loading = false
          })
          this.dialogVisible = false
        }
      })
    },
    /**
     * 提交时数据处理
     * 用于提交时参数tempJson传递及提交时html字符串的处理
     * */
    commitDataProcessing() {
      // 富文本编辑器中的内容替换body中的内容
      const updatedHtml = this.temp.uploadFileString;
      // console.log('updatedHtml', updatedHtml)
      // 创建一个新的DOM解析器
      const parser = new DOMParser();
      // 解析HTML字符串
      const doc = parser.parseFromString(updatedHtml, 'text/html');
      // 1、拖拽进来的echarts图表如果在富文本编辑器上删除，需要将内置的信息也删除掉
      const imageSrcDiv = doc.querySelectorAll('.imageSrc')
      if (imageSrcDiv.length > 0) {
        imageSrcDiv.forEach(item => {
          const isImg = item.querySelector('.imgShow')
          const IsImgType = item.querySelector('.imageType')
          if (isImg === null && IsImgType !== null) {
            // 如果不存在图片，但是存在内置信息，需要将内置信息也删除
            item.parentNode.removeChild(item)
          }
        })
      }
      // 表格数据处理
      this.tableDataProcessing(doc)
      // echarts图表数据处理
      this.echartsDataProcessing(doc)
    },
    /**
     * 判断模板名称是否重复(不能和模板配置那边的名称重复)
     * @param rule
     * @param value
     * @param callback
     */
    nameValidator(rule, value, callback) {
      getByTitle({ title: value }).then(respond => {
        const pattern = /[\\/:：*?？"”<>|]/;
        if (respond.data === true) {
          callback(new Error(this.$t('pages.foundationForm_Msg4')))
        } else if (value.length < 3) {
          callback(new Error(this.$t('pages.foundationForm_Msg5')))
        } else if (pattern.test(value)) {
          callback(new Error(this.$t('pages.foundationForm_Msg30') + '：\\ / : * ? " < > |'))
        } else {
          callback()
        }
      })
    },
    /**
     * 获取{{...}}中间的内容（保存时，对富文本中表格处理，获取到需要的参数tempJson）
     * */
    getContentBetweenBraces(str) {
      const pattern = /\{\{(.*?)\}\}/g;
      const matches = str.match(pattern);
      if (matches) {
        return matches.map(match => match.replace(/\{\{|\}\}/g, ''));
      }
      return [];
    },
    /**
     * 获取[...]中间的内容（提交时，对富文本中表格处理，获取到需要的参数tempJson）
     * */
    getContentBetweenBrackets(str) {
      const matches = str.match(/\[([^\]]+)\]/g);
      return matches ? matches.map(s => s.slice(1, -1)) : []; // 去掉首尾的括号
    },
    /**
     * echarts图表数据处理（获取提交参数tempJson）
     * @param doc:html字符串解析出来的数据
     * */
    echartsDataProcessing(doc) {
      const imageType = doc.querySelectorAll('.imageType')
      // console.log('imageType', imageType)
      const echData = []
      if (imageType.length > 0) {
        for (let i = 0; i < imageType.length; i++) {
          const obj = {}
          const imageTypeDiv = imageType[i].innerText
          const imageCodeDiv = imageType[i].nextElementSibling.innerText
          const imageBusinessCodeDiv = imageType[i].nextElementSibling.nextElementSibling.innerText
          const dimBaseTypeValue = imageType[i].nextElementSibling.nextElementSibling.nextElementSibling.innerText
          obj.picType = imageTypeDiv
          obj.imgCode = imageCodeDiv
          obj.businessCode = imageBusinessCodeDiv
          // 拖拽进去的趋势图表，如果存在两个code一样，报表日期不一样的趋势图，生成的报表生成的结果是一样的
          // 前端保存数据的时候，在原来基础上添加一个echartCode字段，传递一个后台需要格式的字符串，用于区分
          obj.echartCode = imageBusinessCodeDiv.slice(3, -2)
          // 获取{{}}中间的内容，并转成字符串
          obj.dimBaseType = this.getContentBetweenBraces(dimBaseTypeValue).toString()
          echData.push(obj)
          // console.log('imageBusinessCodeDiv', imageBusinessCodeDiv)
          // console.log('imageTypeDiv', imageTypeDiv)
        }
      }
      this.tempJson.echarts = echData
      // console.log('要传给后台的表格数据echartsData', JSON.parse(JSON.stringify(echData)))
    },
    /**
     * 对比表格
     * 判断对比表格后面是否存在class名称为qoq或yoy的div
     * @param type 区分qoq或yoy
     * @param element 表格元素
     * */
    isCompareTableHasClassName(type, element) {
      // 假设tableElement是你要检查的表格元素
      // 假设tableElement的直接父元素包含多个子节点，其中一些可能是元素节点
      // 使用tableElement找到其父元素
      const parentElement = element.parentElement;
      // 使用childNodes属性获取所有子节点
      const childNodes = parentElement.childNodes;
      // 使用some方法检查是否存在具有特定类名的子元素
      return Array.prototype.some.call(childNodes, function(node) {
        // 确保节点是元素节点
        if (node.nodeType === Node.ELEMENT_NODE) {
          // 检查元素的classList是否包含'qoq'或'yoy'
          return node.classList.contains(type);
        }
        return false;
      });
    },
    /**
     * 表格数据处理（提交参数）
     * @param doc:html字符串解析出来的数据
     * */
    tableDataProcessing(doc) {
      // 获取所有的table元素
      const tables = doc.getElementsByTagName('table');
      // 表格字段处理（将得到的数据处理成后台需要的格式）
      const tablesData = []
      // console.log('tables', tables)
      /**
       * 遍历所有的table
       * 获取每个table中的td内容,以及每张table
       * */
      const tdContents = [];
      for (let i = 0; i < tables.length; i++) {
        const tableTds = [];
        const obj = {}
        const tds = tables[i].getElementsByTagName('td');
        for (let j = 0; j < tds.length; j++) {
          tableTds.push(tds[j].textContent);
          obj.taCol = tableTds
          obj.table = tables[i]
        }
        tdContents.push(obj);
      }
      // console.log('tdContents555555', JSON.parse(JSON.stringify(tdContents)));
      // 遍历tdContents,请所有表格下的td文本
      /**
       * 遍历tdContents
       * taCol：表格中每一个td中的内容，用于传递参数policyName
       * taCol：富文本中所有表格，用于对比表格中计算变化率表达式qoqDataPercent和yoyDataPercent的获取
       */
      for (let i = 0; i < tdContents.length; i++) {
        // console.log('tdContents[i]', tdContents[i])
        const obj = {}
        // 表格的第一列为businessCode
        // obj.businessCode = tdContents[i][0]
        // 将数组转成字符串，方便获取到每个td中{{}}和[]中间的内容，用于传给后台的参数
        const str = tdContents[i].taCol.toString();
        const tableElement = tdContents[i].table;
        // console.log('str', str)
        // console.log('this.getContentBetweenBraces(str).toString()', this.getContentBetweenBraces(str).toString())
        // 获取到{{}}中间的内容
        let isCompare = false
        if (this.getContentBetweenBraces(str).toString() !== '') {
          // 如果长度>1，说明是对比表格，普通表格policyName只需要传第一个值，对比表格需要传dateType区分同环比
          const braces = this.getContentBetweenBraces(str)
          // console.log('braces', braces)
          if (braces.length > 1) {
            const type = braces[braces.length - 1].toLowerCase()
            isCompare = true
            obj.policyName = this.getContentBetweenBraces(str)[0].toString()
            obj.dateType = type

            // 判断对比表格下方是否存在class名为qoq或yoy相关div元素
            const qoqClass = this.isCompareTableHasClassName('qoq', tableElement)
            const yoyClass = this.isCompareTableHasClassName('yoy', tableElement)
            const defaultQoq = 'qoqData == 0 ? (data == 0 ? \'0\' : \'∞\') : T(java.lang.String).format(\'%.2f%%\',(data-qoqData)*100.0/qoqData)'
            const defaultYoy = 'yoyData == 0 ? (data == 0 ? \'0\' : \'∞\') : T(java.lang.String).format(\'%.2f%%\',(data-yoyData)*100.0/yoyData)'
            if (qoqClass) {
              // 如果存在class，则获取该class对应的元素及该元素下的文本内容
              const elementWithClass = [...tableElement.parentElement.children].find(child => child.classList.contains('qoq'))
              // 可能会存在div下不全是想要的内容，删掉
              const divsToRemove = elementWithClass.querySelectorAll('div, table');
              divsToRemove.forEach(function(element) {
                element.remove();
              });
              const qoqText = elementWithClass.innerText.replace(/\n/g, '')
              // 如果文本不为空，赋值给后台，否则使用默认值
              if (qoqText.trim() !== '') {
                obj.qoqDataPercent = qoqText
              } else {
                obj.qoqDataPercent = defaultQoq
              }
            } else {
              obj.qoqDataPercent = defaultQoq
            }
            // yoy与qoq一致
            if (yoyClass) {
              const elementWithClass = [...tableElement.parentElement.children].find(child => child.classList.contains('yoy'))
              // 可能会存在div下不全是想要的内容，删掉
              const divsToRemove = elementWithClass.querySelectorAll('div, table');
              divsToRemove.forEach(function(element) {
                element.remove();
              });
              const yoyText = elementWithClass.innerText.replace(/\n/g, '')
              if (yoyText.trim() !== '') {
                obj.yoyDataPercent = yoyText
              } else {
                obj.yoyDataPercent = defaultYoy
              }
            } else {
              obj.yoyDataPercent = defaultYoy
            }
          } else {
            obj.policyName = this.getContentBetweenBraces(str).toString()
          }
        }
        // 获取到[]中间的内容
        if (this.getContentBetweenBrackets(str).toString() !== '') {
          if (isCompare) {
            // 对比表格不需要传col
            obj.col = ''
          } else {
            obj.col = this.getContentBetweenBrackets(str).toString()
          }
        }
        if (JSON.stringify(obj) !== '{}') {
          tablesData.push(obj)
        }
      }
      this.tempJson.tables = tablesData
      // console.log('要传给后台的表格数据tables：', JSON.parse(JSON.stringify(tablesData)))
    },
    handleDrag() {

    }
  }
}
</script>

<style scoped>

</style>
