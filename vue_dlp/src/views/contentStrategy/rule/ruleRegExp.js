/**
 * 规则名称正则表达式（限制只允许输入以下字符）
 * <p>【常用字符】数字 字母 中文 空格 !#$%&'()+,-.;=@[]^_`{}~·¥×÷`。？！，、；：“”‘'（）《》【】←↑→↓—―‖…￠￡￢￤￣￥
 * <p>【带圈或括号的数字】①到⑩ ⑴到⒇
 * <p>【罗马数字】ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩⅪⅫⅰⅱⅲⅳⅴⅵⅶⅷⅸⅹ
 * <p>【全角符号】！＂＃＄％＆＇（）＊＋，－．／０１２３４５６７８９：；＜＝＞？＠ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺ［＼］＾＿｀ａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ｛｜｝～
 * <p>【日文平假名】ぁあぃいぅうぇえぉおかがきぎくぐけげこごさざしじすずせぜそぞただちぢっつづてでとどなにぬねのはばぱひびぴふぶぷへべぺほぼぽまみむめもゃやゅゆょよらりるれろゎわゐゑをん゛゜ゝゞ
 * <p>【日文片假名】ァアィイゥウェエォオカガキギクグケゲコゴサザシジスズセゼソゾタダチヂッツヅテデトドナニヌネノハバパヒビピフブプヘベペホボポマミムメモャヤュユョヨラリルレロヮワヰヱヲンヴヵヶーヽヾ
 * @type {RegExp}
 */
export const nameRegExp = /[^\d^\[a-zA-Z\]\u0020\u4e00-\u9fa5\u0021\u0023-\u0029\u002B-\u002E\u003B\u003D\u0040\u005B\u005D-\u0060\u007B\u007D\u007E\u00B7\u00A5\u00D7\u00F7\u3002\uff1f\uff01\uff0c\u3001\uff1b\uff1a\u201c\u201d\u2018\u2019\uff08\uff09\u300a\u300b\u3010\u3011\u2190-\u2193\u2014-\u2016\u2026\uFFE0-\uFFE5\uFF01-\uFF64\u2460-\u2469\u2474-\u2487\u2160-\u216B\u2170-\u2179\u3041-\u3094\u309B-\u309E\u30A1-\u30F6\u30FC-\u30FE]/g;
