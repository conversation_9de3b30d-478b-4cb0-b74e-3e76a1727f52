<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addRule') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="sourceCodeList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <source-code-dialog ref="sourceCodeDialog"/>
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="$t('text.batchDelete', {info: $t('route.sourceCode')})"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :filter-name-desc="$t('route.sourceCode')"
      :edit-type="1"
      @submitEnd="batchDeleteFunc"
    />
  </div>
</template>

<script>
import { getRulePage, deleteRule } from '@/api/contentStrategy/rule/sourceCode'
import SourceCodeDialog from './sourceCodeDialog'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

export default {
  name: 'SourceCode',
  components: { SourceCodeDialog, BatchEditPageDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'ruleName', width: '150', fixed: true },
        { prop: 'sensitiveCodeType', label: 'sensitiveCodeType', width: '350' },
        { prop: 'model', label: 'SCRModel', width: '200', formatter: this.modelFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      deleteable: false
    }
  },
  computed: {
    ruleTable() {
      return this.$refs['sourceCodeList']
    }
  },
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRulePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.ruleTable.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.sourceCodeDialog.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.sourceCodeDialog.handleUpdate(row)
    },
    handleDelete() {
      const selectedDatas = this.ruleTable.getSelectedDatas()
      const total = this.ruleTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedDatas, total, this.query)
    },
    modelFormatter(row) {
      // 模式一：全文完全检测
      // 模式二：截取前 个字符和后 个字符进行检测
      // 模式三：随机采样 段文本进行检测
      // 模式四：根据文本长度自动选择策略
      const model = row.model;
      let result;
      switch (model) {
        case 0 : result = this.$t('pages.SCRModel_text1'); break;
        case 1 : result = this.$t('pages.SCRModel_text2', { beforeChar: row.beforeChar, afterChar: row.afterChar }); break;
        case 2 : result = this.$t('pages.SCRModel_text3', { paragraph: row.randomParagraph }); break;
        case -1 : result = this.$t('pages.SCRModel_text4'); break;
        default : result = (model < 0 ? this.$t('pages.SCRModel_text4') : this.$t('SCRModel_text1')); break;
      }
      return result;
    },
    batchDeleteFunc(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.ruleTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch((e) => { callback(e) })
      }).catch((e) => { callback(e) })
    }
  }
}
</script>
