<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="dialogFormVisible"
    width="800px"
    @dragDialog="handleDrag"
    @closed="closeFunc"
  >
    <div slot="title" class="el-dialog__title">
      {{ textMap[dialogStatus] }}
      <el-tooltip effect="dark" placement="bottom-start">
        <span slot="content">
          {{ $t('pages.sourceCodeRuleSupportType') }}
        </span>
        <i class="el-icon-info" />
      </el-tooltip>
    </div>
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px" style="width: 750px;">
      <FormItem :label="$t('table.ruleName')" prop="name">
        <el-input v-model="temp.name" v-trim maxlength="60" :disabled="!formable"></el-input>
      </FormItem>
      <el-card class="box-card" :body-style="{padding: '0 5px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.inclusionConditions') }}</span>
        </div>
        <FormItem label="" label-width="0px" prop="includeFilter">
          <div style="padding: 5px;" @click="() => { includeFilterValidatorError = false }">
            <el-row>
              <el-col :span="24">
                <FormItem :label="$t('pages.sensitiveCodeType')" prop="sensitiveCodeType">
                  <el-checkbox v-model="checkAll" :indeterminate="indeterminate" :disabled="!formable" style="margin-left: 10px;" @change="handleCheckAllChange">{{ $t('button.selectAll') }}</el-checkbox>
                  <el-checkbox-group v-model="temp.sensitiveCodeType" :disabled="!formable" @change="handleCheckedSuffixChange">
                    <el-checkbox v-for="item in fileSuffixOptions" :key="item.id" :label="item.name" style="width: 90px">
                      <el-tooltip :content="item.suffix" placement="top-start">
                        <span>{{ item.name }}</span>
                      </el-tooltip>
                    </el-checkbox>
                  </el-checkbox-group>
                </FormItem>
              </el-col>
            </el-row>
            <div v-if="includeFilterValidatorError" style="color: #F56C6C;">
              {{ $t('pages.file_text2') }}
            </div>
          </div>
        </FormItem>
      </el-card>
      <el-card class="box-card" :body-style="{padding: '0 5px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.SCRModel') }}</span>
          <el-tooltip effect="dark" placement="bottom-start">
            <span slot="content">
              {{ $t('pages.SCRModel_text1') }}<br/>
              {{ $t('pages.SCRModel_text2', { beforeChar: 'xxx', afterChar: 'xxx' }) }}<br/>
              {{ $t('pages.SCRModel_text3', { paragraph: 'n'}) }}<br/>
              {{ $t('pages.SCRModel_text4') }}<br/>
              <i18n path="pages.SCRModel_text4_1" style="margin-left: 48px; display: inline-block;">
                <br slot="br"/>
              </i18n>
            </span>
            <i class="el-icon-info" />
          </el-tooltip>
        </div>
        <FormItem label="" label-width="0px" prop="model">
          <div style="padding: 5px;">
            <el-radio-group v-model="temp.model" :disabled="!formable" >
              <el-radio :label="0">{{ $t('pages.SCRModel_text1') }}</el-radio><br/>
              <el-radio :label="1">
                <i18n path="pages.SCRModel_text2">
                  <el-input slot="beforeChar" v-model="temp.beforeChar" :min="500" :max="9999" maxlength="" :disabled="!formable" style="width: 80px" @input="temp.beforeChar=temp.beforeChar.replace(/^(0+)|[^\d]+/g,'')" @blur="charFuc()"></el-input>
                  <el-input slot="afterChar" v-model="temp.afterChar" :min="500" :max="9999" maxlength="" :disabled="!formable" style="width: 80px" @input="temp.afterChar=temp.afterChar.replace(/^(0+)|[^\d]+/g,'')" @blur="charFuc()"></el-input>
                  <template slot="space">&nbsp;</template>
                </i18n>
              </el-radio><br/>
              <el-radio :label="2">
                <i18n path="pages.SCRModel_text3">
                  <el-input slot="paragraph" v-model="temp.randomParagraph" :min="1" :max="9999" maxlength="" :disabled="!formable" style="width: 80px" @input="temp.randomParagraph=temp.randomParagraph.replace(/^(0+)|[^\d]+/g,'')" @blur="paragraphFuc()"></el-input>
                  <template slot="space">&nbsp;</template>
                </i18n>
              </el-radio>
              <br/>
              <el-radio :label="-1">{{ $t('pages.SCRModel_text4') }}</el-radio>
            </el-radio-group>
          </div>
        </FormItem>
      </el-card>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="false" type="primary" @click="handleModify">
        {{ $t('button.edit') }}
      </el-button>
      <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getRuleByName, createRule, updateRule, getFileSuffixOptions } from '@/api/contentStrategy/rule/sourceCode'
import { nameRegExp } from '../ruleRegExp'

export default {
  name: 'SourceCodeDialog',
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        sensitiveCodeType: '',
        fileSuffix: '',
        model: -1,
        beforeChar: 500,
        afterChar: 500,
        randomParagraph: 10
      },
      isDoDrip: false, // 零星检测勾选时，隐藏例外条件和匹配计数
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      includeFilterValidatorError: false,
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        sensitiveCodeType: [{ required: true, trigger: 'blur', validator: this.sensitiveCodeTypeValidator }]
      },
      isRuleUpdate: false,
      formable: true,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.sourceCodeRule'), 'update'),
        create: this.i18nConcatText(this.$t('pages.sourceCodeRule'), 'create'),
        view: this.i18nConcatText(this.$t('pages.sourceCodeRule'), 'details')
      },
      // 源代码识别
      checkAll: false,
      indeterminate: false,
      // 后缀
      fileSuffixOptions: []
    }
  },
  computed: {
    gridTable() {
      return this.$parent.ruleTable
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    handleCheckAllChange(val) {
      const checked = this.fileSuffixOptions.map(item => {
        return item.name;
      });
      this.temp.sensitiveCodeType = val ? checked : [];
      this.indeterminate = false;
    },
    handleCheckedSuffixChange(value) {
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.fileSuffixOptions.length;
      this.indeterminate = checkedCount > 0 && checkedCount < this.fileSuffixOptions.length;
    },
    closeFunc() {
      const data = this.isRuleUpdate ? 'G12' : ''
      const id = this.ruleId
      this.$emit('sourceCodeClosed', data, id)
      this.isRuleUpdate = false
      this.formable = true
      this.ruleId = ''
    },
    fileSuffixToCheckbox() {
      const rowData = Object.assign({}, this.temp)
      const sensitiveCodeType = rowData.sensitiveCodeType;
      const checkSensitiveCodeType = [];
      // 先组装成set，然后到option集合里判断是否存在，若存在则选中
      const sensitiveCodeTypeSet = new Set();
      sensitiveCodeType.split(',').map(name => {
        sensitiveCodeTypeSet.add(name);
      });
      this.fileSuffixOptions.map(option => {
        const name = option.name;
        // 此处需要处理 "C,C++" 这一项
        if (sensitiveCodeType.indexOf('C++') > -1 && name.indexOf('C++') > -1) {
          checkSensitiveCodeType.push(name);
        } else if (sensitiveCodeTypeSet.has(name)) {
          checkSensitiveCodeType.push(name);
        }
      });
      this.temp.sensitiveCodeType = checkSensitiveCodeType;
      this.handleCheckedSuffixChange(checkSensitiveCodeType);
    },
    resetTemp() {
      // 初始化fileSuffixOptions
      this.initFileSuffixOptions();
      this.temp = Object.assign({}, this.defaultTemp)
      this.includeFilterValidatorError = false
      this.handleCheckAllChange(this.checkAll = true);
    },
    initFileSuffixOptions() {
      getFileSuffixOptions().then(res => {
        this.fileSuffixOptions = res.data;
      });
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      this.fileSuffixToCheckbox()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleView(row, doDrip) {
      this.isDoDrip = !!doDrip;
      this.formable = false
      this.handleUpdate(row)
      this.dialogStatus = 'view'
    },
    handleModify() {
      this.dialogStatus = 'update'
      this.formable = !this.formable
    },
    checkboxToFileSuffix(tempData) {
      const checkSensitiveCodeType = tempData.sensitiveCodeType;
      const sensitiveCodeType = [];
      const fileSuffix = [];
      // 组装为set
      const checkSet = new Set();
      checkSensitiveCodeType.map(name => {
        checkSet.add(name);
      });
      this.fileSuffixOptions.map(option => {
        const name = option.name;
        const suffix = option.suffix;
        if (checkSet.has(name)) {
          sensitiveCodeType.push(name);
          fileSuffix.push(suffix);
        }
      });
      tempData.sensitiveCodeType = sensitiveCodeType.join(',');
      tempData.fileSuffix = fileSuffix.join(',');
    },
    createData() {
      if (this.modelParamsValidator(this.temp)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.includeFilterValidator()) {
            this.submitting = false
            return
          }
          const tempData = Object.assign({}, this.temp)
          this.checkboxToFileSuffix(tempData);
          createRule(tempData).then(respond => {
            this.submitting = false
            this.isRuleUpdate = true
            this.ruleId = respond.data.id
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      if (this.modelParamsValidator(this.temp)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.includeFilterValidator()) {
            this.submitting = false
            return
          }
          const tempData = Object.assign({}, this.temp)
          this.checkboxToFileSuffix(tempData);
          updateRule(tempData).then(respond => {
            this.submitting = false
            this.isRuleUpdate = true
            this.ruleId = respond.data.id
            this.dialogFormVisible = false
            this.gridTable.updateRowData(respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      const errorStr = value.match(nameRegExp)
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } if (nameRegExp.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_IllegalCharacter').concat('：').concat(errorStr.join(''))))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    sensitiveCodeTypeValidator(rule, value, callback) {
      if (!value || 0 === value.length) {
        callback(new Error(this.$t('pages.validateMsg_enterSourceCode')))
      } else {
        callback()
      }
    },
    includeFilterValidator() {
      let isSet = false
      const exceptKeys = ['name', 'sensitiveCodeType']
      for (const key in this.temp) {
        if (exceptKeys.indexOf(key) < 0 && this.temp[key]) {
          isSet = true
          break
        }
      }
      this.includeFilterValidatorError = !isSet
      return isSet
    },
    modelParamsValidator(temp) {
      const beforeChar = temp.beforeChar;
      const afterChar = temp.afterChar;
      const randomParagraph = temp.randomParagraph;
      const model = temp.model;
      if (1 === model) {
        if (undefined === beforeChar || '' === beforeChar) {
          this.$message({
            message: this.$t('pages.SCRModel_valid_text1'),
            type: 'error',
            duration: 2000
          })
          return true
        }
        if (undefined === afterChar || '' === afterChar) {
          this.$message({
            message: this.$t('pages.SCRModel_valid_text2'),
            type: 'error',
            duration: 2000
          })
          return true
        }
      } else if (2 === model) {
        if (undefined === randomParagraph || '' === randomParagraph) {
          this.$message({
            message: this.$t('pages.SCRModel_valid_text3'),
            type: 'error',
            duration: 2000
          })
          return true
        }
      }
      return false;
    },
    charFuc() {
      if (this.temp.beforeChar < 500) {
        this.$message({
          message: this.$t('pages.SCRModel_char_range1'),
          type: 'error',
          duration: 2000
        })
        this.temp.beforeChar = 500;
      }
      if (this.temp.beforeChar > 9999) {
        this.$message({
          message: this.$t('pages.SCRModel_char_range1'),
          type: 'error',
          duration: 2000
        })
        this.temp.beforeChar = 9999;
      }
      if (this.temp.afterChar < 500) {
        this.$message({
          message: this.$t('pages.SCRModel_char_range1'),
          type: 'error',
          duration: 2000
        })
        this.temp.afterChar = 500;
      }
      if (this.temp.afterChar > 9999) {
        this.$message({
          message: this.$t('pages.SCRModel_char_range1'),
          type: 'error',
          duration: 2000
        })
        this.temp.afterChar = 9999;
      }
    },
    paragraphFuc() {
      if (this.temp.randomParagraph < 0) {
        this.$message({
          message: this.$t('pages.SCRModel_char_range2'),
          type: 'error',
          duration: 2000
        })
        this.temp.randomParagraph = 1;
      }
      if (this.temp.randomParagraph > 9999) {
        this.$message({
          message: this.$t('pages.SCRModel_char_range2'),
          type: 'error',
          duration: 2000
        })
        this.temp.randomParagraph = 9999;
      }
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
