<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="title"
    :visible.sync="visible"
    width="800px"
    @close="visible = false"
  >
    <grid-table ref="ruleList" :col-model="colModel" :multi-select="false" :row-datas="ruleList" :height="400"/>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">
        {{ $t('button.close') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCollectorResultDict } from '@/utils/dictionary'
import { listRuleById as listEdmRuleById } from '@/api/contentStrategy/rule/edm'
import { listRuleById as listVmlRuleById } from '@/api/contentStrategy/rule/vml'
import { listRuleById as listFileFpRuleById } from '@/api/contentStrategy/rule/fileFp'

export default {
  name: 'FailTrainDetailDlg',
  props: {
    ruleIds: {
      type: [Number, String, Array],
      default() {
        return ''
      }
    },
    ruleType: {
      type: Number,
      default() {
        return 1
      }
    }
  },
  data() {
    return {
      title: this.$t('pages.ruleFailDetail'),
      visible: false,
      colModel: [
        { prop: 'name', label: 'ruleName', width: '150', fixed: true },
        { prop: 'state', label: 'acquisitionStatus', width: '100', formatter: this.stateFormatter },
        { prop: 'trainTime', label: 'latestAcquisitionTime', width: '150' }
      ],
      ruleList: [],
      stateOptions: { 0: this.$t('pages.notCollected'), 1: this.$t('pages.collecting'), 2: this.$t('pages.completeAcquisition'), 3: this.$t('pages.collectionFailed') },
      collectorResultOption: getCollectorResultDict()
    }
  },
  watch: {
    ruleIds(val) {
      this.listRuleById(val)
    }
  },
  methods: {
    show() {
      this.visible = true
    },
    listRuleById(ruleIds) {
      if (ruleIds) {
        let ids = ''
        if (ruleIds instanceof Array) {
          ids = ruleIds.join(',')
        } else if (typeof ruleIds === 'number') {
          ids = ruleIds.toString()
        } else {
          ids = ruleIds
        }
        if (this.ruleType === 4) {
          listEdmRuleById({ ids: ids }).then(res => {
            this.ruleList = res.data
          })
        } else if (this.ruleType === 5) {
          listVmlRuleById({ ids: ids }).then(res => {
            this.ruleList = res.data
          })
        } else if (this.ruleType === 6) {
          listFileFpRuleById({ ids: ids }).then(res => {
            this.ruleList = res.data
          })
        }
      }
    },
    stateFormatter(row, data) {
      if (data === 3 && row.result) {
        return this.collectorResultOption[row.result] ? this.collectorResultOption[row.result] : this.collectorResultOption[40000]
      }
      return this.stateOptions[data]
    },
    handleClose() {
      this.$emit('closed')
    }
  }
}
</script>
