<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="fileFpDialogFormVisible"
    width="800px"
    @dragDialog="handleDrag"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 700px; margin-left:30px;">
      <FormItem :label="$t('pages.ruleName')" prop="name">
        <el-input v-model="temp.name" v-trim maxlength="60" :disabled="!formable" />
      </FormItem>
      <el-card class="box-card" :body-style="{padding: '0 5px 5px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.inclusionConditions') }}</span>
        </div>
        <div style="margin-top: 5px;">
          <FormItem :label="$t('pages.fileFp_Msg26')">
            <vml-upload
              ref="uploadFile"
              :formable="formable"
              :file-num-limit="fileNumLimit"
              :file-size-limit="fileSizeLimit"
              :up-file-list="upFileList"
              :check-duplicate="checkDuplicate"
              @add="fileAdd"
              @remove="fileRemove"
              @clear="clearFiles"
            />
          </FormItem>
        </div>
      </el-card>
      <el-card class="box-card" :body-style="{padding: '0 5px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.exceptions') }}</span>
        </div>
        <FormItem :label="$t('pages.keywords')" prop="exceptWords">
          <span>{{ $t('pages.fileFp_Msg9') }}</span>
          <el-input v-model="temp.rule.exceptWords" no-limit type="textarea" :rows="3" maxlength="1024" :placeholder="$t('pages.keyword_text14')" resize="none" :disabled="!formable" @keydown.native="listenKey($event)"></el-input>
        </FormItem>
      </el-card>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="fileFpDialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { uploadFile, getRuleByName, createRule, updateRule, getRuleById } from '@/api/contentStrategy/rule/fileFp'
import { nameRegExp } from '../ruleRegExp'
import VmlUpload from '@/views/contentStrategy/rule/vml/vmlUpload'

export default {
  name: 'FileFpDialog',
  components: { VmlUpload },
  data() {
    return {
      fileNumLimit: 1000,
      fileSizeLimit: 1000 * 1024 * 1024,
      upFileList: [],
      toDeleteFiles: [],
      temp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        fileName: '',
        fileMd5: '',
        state: 0,
        trainCostTime: '',
        trainTime: '',
        rule: {
          file: '',
          exceptWords: '',
          caseSensitive: false
        },
        mulFile: 1,
        fileList: []
      },
      checkTimesType: 1,
      checkTimesEditable: false,
      fileFpDialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.fileFingerRule'), 'update'),
        create: this.i18nConcatText(this.$t('pages.fileFingerRule'), 'create'),
        view: this.i18nConcatText(this.$t('pages.fileFingerRule'), 'details')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        // fileName: { required: true, validator: this.fileValidator, trigger: 'change' },
        exceptWords: [{ trigger: 'blur', validator: this.contentValidator }]
      },
      submitting: false,
      isRuleUpdate: false,
      formable: true,
      uploaded: false,
      loadText: this.$t('pages.clickUpload'),
      suffix: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'txt'],
      fileChange: false // 文件是否发生变更
    }
  },
  computed: {
    gridTable() {
      return this.$parent.fileFpTable
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    checkDuplicate(file) {
      for (let i = 0; i < this.upFileList.length; i++) {
        if (file.name === this.upFileList[i].name) {
          return true
        }
      }
      return false
    },
    fileAdd(files, fileList) {
      this.upFileList = fileList
    },
    fileRemove(file, fileList) {
      this.upFileList = fileList
      this.toDeleteFiles.push(file.name)
    },
    clearFiles(e) {
      this.upFileList.forEach(file => {
        this.toDeleteFiles.push(file.name)
      })
      this.upFileList = []
    },
    handleUploadFileChange: async function(file, fileList) {
      this.submitting = true
      // const result = await isUtf8File(file.raw)
      const fileName = file.raw.name
      const fileSuffix = fileName.substr(fileName.lastIndexOf('.') + 1)
      const result = this.suffix.indexOf(fileSuffix) > -1
      if (result) {
        this.loadText = this.$t('pages.fileFp_Msg10')
        const fd = new FormData()
        fd.append('file', file.raw)// 传文件
        uploadFile(fd).then(res => {
          this.loadText = this.$t('pages.clickUpload')
          this.submitting = false
          this.temp.fileName = res.data.fileName
          this.temp.fileMd5 = res.data.fileMd5
        }).catch(res => {
          this.loadText = this.$t('pages.clickUpload')
          this.submitting = false
          this.$refs['upload'] && this.$refs['upload'].clearFiles()
        })
      } else {
        this.loadText = this.$t('pages.clickUpload')
        this.submitting = false
        this.$refs['upload'] && this.$refs['upload'].clearFiles()
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.fileFp_Msg25'), type: 'error', duration: 2000 })
      }
      return false // 屏蔽了action的默认上传
    },
    beforeUpload(file) {
      this.submitting = true
      this.loadText = this.$t('pages.fileFp_Msg10')
      const fd = new FormData()
      fd.append('file', file)// 传文件
      uploadFile(fd).then(res => {
        this.loadText = this.$t('pages.clickUpload')
        this.submitting = false
        this.temp.fileName = res.data.fileName
        this.temp.fileMd5 = res.data.fileMd5
      }).catch(res => {
        this.loadText = this.$t('pages.clickUpload')
        this.submitting = false
      })
      return false // 屏蔽了action的默认上传
    },
    checkTimesTypeChange: function(data) {
      if (data > 1) {
        this.checkTimesType = 2
        this.checkTimesEditable = true
        this.temp.checkTimes = data
      } else {
        this.checkTimesType = 1
        this.checkTimesEditable = false
        this.temp.checkTimes = 1
      }
    },
    listenKey(e) {
      if (e.keyCode === 13) {
        e.preventDefault() // 阻止浏览器默认换行操作
        return false
      }
    },
    resetTemp() {
      this.uploaded = false
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.upFileList = []
      this.toDeleteFiles = []
      this.fileChange = false
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.fileFpDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        const File = this.$refs['uploadFile']
        File && File.clearFiles()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.uploaded = true
      this.temp = Object.assign(this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      this.dialogStatus = 'update'
      this.fileFpDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.temp.fileList.forEach(item => {
          this.upFileList.push({ name: item })
        })
      })
    },
    handleView(row) {
      this.formable = false
      getRuleById(row.id).then(respond => {
        this.handleUpdate(respond.data)
        this.dialogStatus = 'view'
      })
    },
    createData() {
      this.formatKeyWord();
      this.submitting = true
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (!this.upFileList || this.upFileList.length == 0) {
            this.$message({
              message: this.$t('pages.chooseFile'),
              type: 'error'
            })
            this.submitting = false
          } else {
            const formData = this.toFormData(this.temp)
            formData.append('toDeleteFiles[]', null)
            this.upFileList.forEach(file => {
              if (file.raw) {
                formData.append('files[]', file.raw)
              }
            })
            uploadFile(formData).then(respond => {
              this.temp.fileMd5 = respond.data.fileMd5
              createRule(this.temp).then(respond => {
                this.submitting = false
                this.fileFpDialogFormVisible = false
                this.$emit('submitEnd')
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.createSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(() => {
                this.submitting = false
              })
            })
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.formatKeyWord();
      this.submitting = true
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (!this.upFileList || this.upFileList.length == 0) {
            this.$message({
              message: this.$t('pages.chooseFile'),
              type: 'error'
            })
            this.submitting = false
          } else {
            const formData = this.toFormData(this.temp)
            let i = 0
            if (this.toDeleteFiles.length > 0) {
              this.fileChange = true
              formData.append('toDeleteFiles[]', this.toDeleteFiles)
              i++
            } else {
              formData.append('toDeleteFiles[]', null)
            }
            this.upFileList.forEach(file => {
              if (file.raw) {
                this.fileChange = true
                formData.append('files[]', file.raw)
                i++
              }
            })
            if (i) {
              uploadFile(formData).then(respond => {
                this.updateRule()
              }).catch(reason => {
                this.submitting = false
              })
            } else {
              this.updateRule()
            }
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateRule() {
      const tempData = Object.assign({}, this.temp)
      updateRule(tempData).then(respond => {
        this.submitting = false
        this.fileFpDialogFormVisible = false
        this.$emit('submitEnd')
        this.$notify({
          title: this.$t('text.success'),
          message: this.fileChange ? this.$t('pages.edm_Msg17') : this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
      }).catch(res => {
        this.submitting = false
      })
    },
    nameValidator(rule, value, callback) {
      const errorStr = value.match(nameRegExp)
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } if (nameRegExp.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_IllegalCharacter').concat('：').concat(errorStr.join(''))))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const strategy = respond.data
          if (strategy && strategy.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    fileValidator(rule, value, callback) {
      if (!this.upFileList || this.upFileList.length == 0) {
        callback(new Error(this.$t('pages.chooseFile')))
      } else if (value.length > 100) {
        callback(new Error(this.$t('pages.fileFp_Msg11')))
      } else {
        callback()
      }
    },
    contentValidator(rule, value, callback) {
      value = this.temp.rule.exceptWords
      if (value && value.indexOf('\\n') > -1) {
        callback(new Error(this.$t('pages.fileFp_Msg12')))
      } else if (value && value.indexOf('，') > -1) {
        callback(new Error(this.$t('pages.fileFp_Msg13')))
      } else {
        const keywords = value.split(',')
        if (keywords.length > 50) {
          callback(new Error(this.$t('pages.keyword_text14')))
        }
        const maxLength = keywords.reduce((max, keyword) => Math.max(max, keyword.length), 0)
        // 单个关键字不能超过256个字符。
        if (maxLength > 256) {
          callback(new Error(this.$t('pages.keyword_text13')))
        }
        callback()
      }
    },
    // 格式化关键字（主要为了去除连续输入的","和"-"，例如：1,2,,,,,3，去除后：1,2,3）
    formatKeyWord() {
      const exceptWords = this.temp.rule.exceptWords;
      this.temp.rule.exceptWords = exceptWords.replaceAll(/,{2,}/g, ',').replaceAll(/-{2,}/g, '-');
      this.temp.fileList = []
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
