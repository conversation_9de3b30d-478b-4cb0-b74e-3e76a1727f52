<template>
  <div class="app-container">

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleTrain">
          {{ $t('pages.startCollection') }}
        </el-button>
        <el-button icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleCancelTrain">
          {{ $t('pages.stopCollection') }}
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <!--<el-button size="mini">
          导入导出
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="fileFpRuleList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd">
        <template slot="popoverContent" slot-scope="props">
          <ul class="detail-list">
            <li v-for="(item, index) in props.detail" :key="index">{{ item }}</li>
          </ul>
        </template>
      </grid-table>
    </div>

    <file-fp-dialog ref="fileFpDialog" @submitEnd="submitEnd" />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="$t('text.batchDelete', {info: $t('route.fileFp')})"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :filter-name-desc="$t('route.fileFp')"
      :edit-type="1"
      @submitEnd="batchDeleteFunc"
    />
    <rule-check-dlg
      ref="checkDlg"
      :title="textMap[dialogStatus]"
      :result-options="resultOptions"
      :upload-file-api="uploadCkeckFile"
      :check-file-api="checkFile"
      :check-result-api="getCheckResult"
    />
    <fail-train-detail-dlg
      ref="failTrainDetailDlg"
      :rule-ids="ruleIds"
      :rule-type="ruleType"
    />
  </div>
</template>

<script>
import {
  cancelTrainFileFingerprint,
  checkFile,
  deleteRule,
  getCheckResult,
  getRulePage,
  trainFileFingerprint,
  uploadCkeckFile
} from '@/api/contentStrategy/rule/fileFp'
import { getCollectorResultDict } from '@/utils/dictionary'
import FileFpDialog from './fileFpDialog'
import RuleCheckDlg from '@/views/contentStrategy/rule/RuleCheckDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import FailTrainDetailDlg from '@/views/contentStrategy/rule/FailTrainDetailDlg'

export default {
  name: 'FileFpRule',
  components: { FileFpDialog, RuleCheckDlg, BatchEditPageDlg, FailTrainDetailDlg },
  data() {
    return {
      colModel: [ // 主页面列表属性
        { prop: 'name', label: 'ruleName', width: '150', fixed: true },
        { prop: 'state', label: 'acquisitionStatus', width: '100', formatter: this.stateFormatter },
        { prop: 'trainCostTime', label: 'acquisitionTime', width: '100', formatter: this.trainCostTimeFormatter },
        { prop: 'trainTime', label: 'latestAcquisitionTime', width: '150' },
        { prop: 'fileNum', type: 'popover', childData: 'fileList', label: 'collectDocuments', width: '150' },
        { prop: 'rule.exceptWords', label: 'exceptionKeyword', width: '150' },
        // { prop: 'checkTimes', label: '匹配计数', width: '150', formatter: this.checkTimesFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'testing', isShow: (row) => row.state === 2, click: this.handleCheckFile }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      showTree: true,
      deleteable: false,
      collectable: false,
      selectedCols: [], // 选中的文件采集列编号列表
      tableOptions: [],
      stateOptions: { 0: this.$t('pages.notCollected'), 1: this.$t('pages.collecting'), 2: this.$t('pages.completeAcquisition'), 3: this.$t('pages.collectionFailed') },
      checkTimesTypeOptions: [
        { value: 1, label: 'pages.fileFp_Msg1', input: false },
        { value: 2, label: 'pages.fileFp_Msg2', input: true }
      ],
      checkTimesType: 1,
      checkTimesEditable: false,
      dialogFormVisible: false,
      dialogGroupFormVisible: false,
      dialogStatus: '',
      loadText: this.$t('pages.clickUpload'),
      submitting: false,
      textMap: {
        update: this.$t('pages.editFileFingerRule'),
        create: this.$t('pages.addFileFingerRule'),
        createGroup: this.$t('pages.addFingerDatabase'),
        updateGroup: this.$t('pages.editFingerDatabase'),
        check: this.$t('pages.fingerRuleDetection')
      },
      multipleSelection: [], // 选中行数组集合
      groupTreeData: [{ id: '0', dataId: '0', label: this.$t('pages.fingerDatabase'), parentId: '', children: [] }],
      filterKey: '',
      tableTreeSelectNode: [],
      resultOptions: {
        '0': this.$t('pages.fileFp_Msg3'),
        '1': this.$t('pages.fileFp_Msg4')
      },
      collectorResultOption: getCollectorResultDict(),
      ruleIds: null,
      ruleType: 6// 类型值默认为6
    }
  },
  computed: {
    fileFpTable() {
      return this.$refs['fileFpRuleList']
    }
  },
  created() {
    this.$socket.subscribe({ url: '/topic/updateStatus', callback: (resp, handle) => {
      if (resp.data == 'train_filefp' || resp.data === 'train_mulfilefp') {
        this.handleRefresh()
      }
    } })
  },
  methods: {
    uploadCkeckFile,
    checkFile,
    getCheckResult,
    rowDataApi: function(option) {
      return getRulePage(option)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.fileFpTable.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.fileFpDialog.handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs.fileFpDialog.handleUpdate(row)
    },
    // type 操作类型：新增、修改； data 行数据
    submitEnd(type, data) {
      this.handleRefresh()
    },
    handleDelete() {
      const selectedDatas = this.fileFpTable.getSelectedDatas()
      const total = this.fileFpTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedDatas, total, this.query)
    },
    handleTrain() {
      const toTrainIds = this.fileFpTable.getSelectedIds()
      trainFileFingerprint({ ids: toTrainIds.join(',') }).then(respond => {
        this.fileFpTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.fileFp_Msg5'),
          type: 'success',
          duration: 2000
        })
        if (respond.data && respond.data.length > 0) {
          this.ruleIds = respond.data
          this.$refs['failTrainDetailDlg'].show()
        }
      })
    },
    handleCancelTrain() {
      this.$confirmBox(this.$t('pages.fileFp_Msg6'), this.$t('text.prompt')).then(() => {
        const toCancelIds = this.fileFpTable.getSelectedIds()
        cancelTrainFileFingerprint({ ids: toCancelIds.join(',') }).then(respond => {
          this.fileFpTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.fileFp_Msg7'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleRefresh() {
      this.fileFpTable.execRowDataApi(this.query)
    },
    handleCheckFile(row) {
      this.dialogStatus = 'check'
      this.$refs['checkDlg'].show(row)
    },
    stateFormatter(row, data) {
      if (data === 3 && row.result) {
        return this.collectorResultOption[row.result] ? this.collectorResultOption[row.result] : this.collectorResultOption[40000]
      }
      return this.stateOptions[data]
    },
    trainCostTimeFormatter(row, data) {
      return !!data || data === 0 ? data + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('text.second') : ''
    },
    checkTimesFormatter(row, data) {
      let option
      if (row.checkTimes === 1) {
        option = this.checkTimesTypeOptions[0]
      } else {
        option = this.checkTimesTypeOptions[1]
      }
      return option.input ? this.$t(option.label, { input: row.checkTimes }) : this.$t(option.label)
    },
    batchDeleteFunc(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.fileFpTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch((e) => { callback(e) })
      }).catch((e) => { callback(e) })
    }
  }
}
</script>

<style lang="scss">
  .detail-list {
    max-height: 300px;
    max-width: 300px;
    padding: 0 10px;
    overflow: auto;
    list-style: none;
    li {
      padding: 5px 20px 5px 10px;
      cursor: default;
      &:hover {
        background: #eee;
      }
    }
  }
</style>
