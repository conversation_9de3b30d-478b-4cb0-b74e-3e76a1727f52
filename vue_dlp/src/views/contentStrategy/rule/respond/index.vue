<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addRule') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="respondRuleList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <respond-dialog ref="respondDialog" />

    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="$t('text.batchDelete', {info: $t('route.respond')})"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :filter-name-desc="$t('route.respond')"
      :edit-type="1"
      @submitEnd="batchDeleteFunc"
    />
  </div>
</template>

<script>
import { getRulePage, deleteRule } from '@/api/contentStrategy/rule/respond'
import RespondDialog from './respondDialog'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

export default {
  name: 'RespondRule',
  components: { RespondDialog, BatchEditPageDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'ruleName', width: '100', fixed: true },
        { label: 'condition', width: '150', formatter: this.filterFormatter },
        { label: 'respondOperation', width: '200', formatter: this.operationFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      respondActions: [
        { label: this.$t('pages.respondActions1'), value: 1 },
        { label: this.$t('pages.respondActions2'), value: 2 },
        { label: this.$t('pages.respondActions3'), value: 4 },
        { label: this.$t('pages.respondActions4'), value: 8 },
        { label: this.$t('pages.respondActions5'), value: 16 },
        { label: this.$t('pages.lockScreen'), value: 32 },
        { label: this.$t('pages.respondActions6'), value: 64 },
        { label: this.$t('pages.screenshot'), value: 128 },
        { label: this.$t('pages.recordingScreen'), value: 256 }
      ],
      stopOutgoingExMap: [
        { id: 1, label: this.$t('pages.respondActions7') },
        { id: 2, label: this.$t('pages.respondActions8') },
        { id: 4, label: this.$t('pages.respondActions9') }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      deleteable: false
    }
  },
  computed: {
    respondTable() {
      return this.$refs['respondRuleList']
    },
    alarmRules() {
      return this.$store.getters.alarmRules
    }
  },
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRulePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.respondTable.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.respondDialog.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.respondDialog.handleUpdate(row)
    },
    handleDelete() {
      const selectedDatas = this.respondTable.getSelectedDatas()
      const total = this.respondTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedDatas, total, this.query)
    },
    filterFormatter(row, data) {
      return this.$refs.respondDialog.filterFormatter(row, data)
    },
    operationFormatter(row, data) {
      // 修改响应规则库的规则时，响应规则中的alarmInfo不会改变，因此这里的alarmLint需要获取最新的值 2022-06-08修改
      // let alarmInfoObj = row.alarmInfo
      // if (typeof row.alarmInfo === 'string') {
      //   alarmInfoObj = JSON.parse(row.alarmInfo)
      // }
      let limit = 0
      this.alarmRules.forEach(element => {
        if (element.id === row.alarmRuleId) {
          limit = element.alarmLimit
        }
      })
      // const actionCkecks = !alarmInfoObj ? [] : this.numToList(alarmInfoObj.alarmLimit, 12)
      const actionCkecks = !limit ? [] : this.numToList(limit, 12)
      const actionNames = []
      for (let i = 0, size = this.respondActions.length; i < size; i++) {
        const id = this.respondActions[i].value
        if (actionCkecks.indexOf(id) >= 0) {
          actionNames.push(this.respondActions[i].label)
        }
      }
      // if (row.stopOutgoing) {
      //   actionNames.push(this.$t('pages.respond_text1'))
      // }
      // 格式化阻止数据外发
      if (row.stopOutgoingEx) {
        const stopList = this.numToList(row.stopOutgoingEx, 3)
        for (let i = 0; i < this.stopOutgoingExMap.length; i++) {
          if (stopList.indexOf(this.stopOutgoingExMap[i].id) >= 0) {
            actionNames.push(this.stopOutgoingExMap[i].label)
          }
        }
      }
      if (row.stopOutgoingEx >= 2 && row.outSendApply) {
        actionNames.push(this.$t('pages.respondActions10'))
      }
      return actionNames.join('，')
    },
    batchDeleteFunc(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.respondTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch((e) => { callback(e) })
      }).catch((e) => { callback(e) })
    }
  }
}
</script>
