<template>
  <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" :style="{width: formWidth}">
    <FormItem :label="$t('table.ruleName')" prop="name">
      <el-input v-model="temp.name" v-trim maxlength="60" :disabled="visitOnly"></el-input>
    </FormItem>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ $t('table.condition') }}</span>
      </div>
      <!--<FormItem label="设备类型" prop="deviceType">
        <el-select v-model="temp.deviceTypeFilter" multiple :disabled="visitOnly" :placeholder="$t('text.select')">
          <el-option v-for="item in deviceTypeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </FormItem>-->
      <FormItem :label="$t('pages.lossType')" prop="lossType">
        <el-select v-model="temp.lossTypeFilter" filterable multiple size="small" :placeholder="$t('pages.group_text5')" :disabled="visitOnly" @change="lossTypeChange">
          <el-option v-for="item in lossTypeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
      </FormItem>
    </el-card>
    <el-card class="box-card" :body-style="{ padding: '0 20px' }">
      <div slot="header" class="clearfix">
        <span>{{ $t('table.respondOperation') }}</span>
        <span style="margin-left: 10px;color: #f15858;">{{ checkErrorMsg }}</span>
      </div>
      <FormItem label-width="13px">
        <!-- <el-checkbox v-model="temp.stopOutgoing" :disabled="visitOnly">{{ $t('pages.respond_text1') }}</el-checkbox> -->
        <i :class="showChildSelect?'el-icon-caret-bottom':'el-icon-caret-right'" @click="()=>{showChildSelect = !showChildSelect}"></i>
        <el-checkbox v-model="checkedAllStopEx" :disabled="visitOnly" :indeterminate="isIndeterminate" @change="handleCheckAllChange">{{ $t('pages.respondActions11') }}</el-checkbox>
        <el-checkbox-group v-show="showChildSelect" v-model="stopOutgoingExList" style="display: block" :disabled="visitOnly" @change="handleCheckedChange">
          <el-checkbox :label="1" style="padding-top: 1px; margin-left: 30px; display: block">{{ $t('pages.respondActions7') }}</el-checkbox>
          <el-checkbox v-if="hasEncPermision" :label="2" style="padding-top: 1px; margin-left: 30px; display: block">{{ $t('pages.respondActions8') }}</el-checkbox>
          <el-checkbox :label="4" style="padding-top: 1px; margin-left: 30px; display: block">{{ hasEncPermision ? $t('pages.respondActions9'): $t('pages.respondActions13') }}</el-checkbox>
        </el-checkbox-group>
      </FormItem>
      <FormItem v-show="stopOutgoingExList.indexOf(2) > -1 || stopOutgoingExList.indexOf(4) > -1" label-width="30px">
        <!-- <el-checkbox v-model="temp.stopOutgoing" :disabled="visitOnly">{{ $t('pages.respond_text1') }}</el-checkbox> -->
        <el-checkbox v-model="temp.outSendApply" :disabled="visitOnly">{{ $t('pages.respondActions10') }}
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.respondActions12">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip></el-checkbox>
      </FormItem>
      <FormItem label-width="13px">
        <ResponseContent
          ref="responseContent"
          :status="dialogStatus"
          :show-select="true"
          :editable="!visitOnly"
          read-only
          :prop-check-rule="propCheckRule"
          :prop-rule-id="propRuleId"
          :select-style="{'margin-top': '0'}"
          @getRuleId="getRuleId"
          @getChecked="getChecked"
        />
      </FormItem>
    </el-card>
  </Form>
</template>

<script>
import { getRuleByName, createRule, updateRule } from '@/api/contentStrategy/rule/respond'
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent'
import { existSaleModule } from '@/api/system/terminalManage/moduleConfig'
import { nameRegExp } from '../ruleRegExp'
export default {
  name: 'RespondForm',
  components: { ResponseContent },
  props: {
    formWidth: {
      type: String,
      default: '750px'
    },
    formDatas: {
      type: Object,
      default: null
    },
    visitOnly: {
      type: Boolean,
      default: false
    },
    lossTypeOptions: {
      type: Array,
      default() {
        return []
      }
    }
  },

  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        deviceTypeFilter: [1],
        lossTypeFilter: [],
        warnContent: '',
        stopOutgoing: false,
        stopOutgoingEx: 0,

        outSendApply: false,
        alarmRuleId: undefined,
        alarmInfo: {
          alarmLimit: 0,
          msgFormClose: 0,
          msgFormPosition: 0,
          msgFormType: 2,
          showSensContent: 0
        }
      },
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        lossType: [{ validator: this.lossTypeValidator }]
      },
      checkErrorMsg: '',  // 用于判断必须要勾选了阻止数据外发或者勾选违规响应规则外发不能全都不勾选
      dialogStatus: 'create',
      propCheckRule: false,
      propRuleId: undefined,
      lossTypeAll: -1,
      checkedAllStopEx: false,
      deviceTypeOptions: [
        { value: 1, label: this.$t('pages.deviceTypeOptions3') },
        { value: 2, label: this.$t('pages.deviceTypeOptions1') },
        { value: 3, label: this.$t('pages.deviceTypeOptions2') }
      ],
      stopOutgoingExList: [],
      isIndeterminate: false,
      showChildSelect: false,
      moduleIds: [],
      hasEncPermision: true  // 是包含加解密模块
    }
  },
  watch: {
    formDatas(val) {
      const data = JSON.parse(JSON.stringify(val))
      data.alarmInfo = typeof data.alarmInfo == 'string' ? JSON.parse(data.alarmInfo) : data.alarmInfo
      this.handleUpdate(data)
    }
  },
  created() {
    if (this.formDatas) {
      const data = JSON.parse(JSON.stringify(this.formDatas))
      data.alarmInfo = typeof data.alarmInfo == 'string' ? JSON.parse(data.alarmInfo) : data.alarmInfo
      this.handleUpdate(data)
    } else {
      this.resetTemp()
    }
    // 获取注册模块
    this.listModule()
  },
  methods: {
    async listModule() {
      await existSaleModule(51).then(resp => {
        this.hasEncPermision = resp.data
      })
    },
    resetTemp() {
      this.dialogStatus = ''
      this.checkErrorMsg = null
      this.$emit('submitting', false)
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.stopOutgoingExList.splice(0)
      this.checkedAllStopEx = false
      this.isIndeterminate = false
      this.propCheckRule = undefined
      this.propRuleId = undefined
    },
    lossTypeChange(selections) {
      const selectAllIndex = selections.indexOf(this.lossTypeAll)
      if (selections.length > 1) {
        if (selectAllIndex === selections.length - 1 || selections.length === this.lossTypeOptions.length - 1) {
          // 所有类型都选中，则显示为所有泄露方式
          selections.splice(0, selections.length, this.lossTypeAll)
        } else if (selectAllIndex > -1) {
          selections.splice(selectAllIndex, 1)
        }
      }
    },
    getChecked(alarmLimit, obj) {
      if (typeof this.temp.alarmInfo === 'string') {
        this.temp.alarmInfo = JSON.parse(this.temp.alarmInfo)
      }
      this.temp.alarmInfo.alarmLimit = alarmLimit.length > 0 ? alarmLimit.reduce(function(prev, curr, idx, arr) {
        return prev + curr
      }) : 0
      this.temp.alarmInfo = alarmLimit.includes(2) ? Object.assign(this.temp.alarmInfo, obj) : this.temp.alarmInfo
    },
    getRuleId(value) {
      this.temp.alarmRuleId = value
    },
    handleCreate() {
      this.resetTemp()
      this.$nextTick(() => {
        this.dialogStatus = 'create'
        if (this.$refs['dataForm']) this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = JSON.parse(JSON.stringify(row)) // copy obj
      // 处理阻止数据外发，兼容旧版本（旧策略）
      if (this.temp.stopOutgoing === true && this.temp.stopOutgoingEx == undefined) {
        if (this.hasEncPermision) {
          this.stopOutgoingExList = [1, 2, 4]
          this.temp.stopOutgoingEx = 7
        } else {
          this.stopOutgoingExList = [1, 4]
          this.temp.stopOutgoingEx = 5
        }
      } else if (this.temp.stopOutgoingEx > 0) {
        this.stopOutgoingExList = this.numToList(this.temp.stopOutgoingEx, 3)
      }
      this.isIndeterminate = this.stopOutgoingExList.length > 0 && (this.hasEncPermision ? this.stopOutgoingExList.length < 3 : this.stopOutgoingExList.length < 2)
      this.checkedAllStopEx = this.hasEncPermision ? this.stopOutgoingExList.length === 3 : this.stopOutgoingExList.length === 2

      if (!this.temp.alarmInfo) {
        this.temp.alarmInfo = JSON.parse(JSON.stringify(this.defaultTemp.alarmInfo))
      } else if (typeof this.temp.alarmInfo === 'string') {
        this.temp.alarmInfo = JSON.parse(this.temp.alarmInfo)
      }
      this.filterLossType(this.temp.lossTypeFilter)
      if (this.temp.lossTypeFilter.length === this.lossTypeOptions.length - 1) {
        let selectAll = true
        this.lossTypeOptions.forEach(opt => {
          if (opt.value != this.lossTypeAll && this.temp.lossTypeFilter.indexOf(opt.value) == -1) {
            selectAll = false
          }
        })
        if (selectAll) {
          this.temp.lossTypeFilter.splice(0, this.temp.lossTypeFilter.length, this.lossTypeAll)
        }
      }
      this.$nextTick(() => {
        this.dialogStatus = 'update'
        if (this.$refs['dataForm']) this.$refs['dataForm'].clearValidate()
        this.propCheckRule = !!row.alarmRuleId
        this.propRuleId = row.alarmRuleId
      })
    },
    // 根据销售模块过滤泄露方式
    filterLossType(data) {
      if (data.length > 0) {
        const lossType = this.lossTypeOptions.map(item => item.value)
        this.temp.lossTypeFilter = data.filter(item => lossType.indexOf(item) > -1)
      }
    },
    createData() {
      this.$emit('submitting', true)
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.msgValid(this.$refs.responseContent.checkRule, this.temp.alarmRuleId)) {
          const formData = this.formatFormData()
          if (this.checkErrorMsg) {
            this.$emit('submitting', false)
            return
          }
          this.checkErrorMsg = null
          createRule(formData).then(respond => {
            this.$emit('submitting', false)
            this.$emit('closeDlg')
            this.$emit('updateGridTable', 'create', respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.$emit('submitting', false)
          })
        } else {
          this.$emit('submitting', false)
        }
      })
    },
    updateData() {
      this.$emit('submitting', true)
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.msgValid(this.$refs.responseContent.checkRule, this.temp.alarmRuleId)) {
          const formData = this.formatFormData()
          if (this.checkErrorMsg) {
            this.$emit('submitting', false)
            return
          }
          this.checkErrorMsg = null
          updateRule(formData).then(respond => {
            this.$emit('submitting', false)
            this.$emit('closeDlg')
            this.$emit('updateGridTable', 'update', respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.$emit('submitting', false)
          })
        } else {
          this.$emit('submitting', false)
        }
      })
    },
    formatFormData() {
      // 响应操作不能全为空，请至少设置一种响应操作
      this.checkErrorMsg = null
      const checkRule = this.$refs['responseContent'].checkRule
      if (!this.stopOutgoingExList.length && !checkRule) {
        this.checkErrorMsg = this.$t('pages.respond_text3')
        return
      }
      // 处理阻止文件外发的数据
      this.temp.stopOutgoingEx = this.getSum(this.stopOutgoingExList)
      if (this.temp.stopOutgoingEx > 0) {
        this.temp.stopOutgoing = true
      } else {
        this.temp.stopOutgoing = false
      }
      const formData = JSON.parse(JSON.stringify(this.temp))
      if (formData.alarmInfo.msgFormClose) {
        formData.alarmInfo.msgFormClose = Number.parseInt(formData.alarmInfo.msgFormClose)
      } else {
        formData.alarmInfo.msgFormClose = 0
      }
      if (formData.lossTypeFilter.indexOf(this.lossTypeAll) > -1) {
        const temp = []
        this.lossTypeOptions.forEach(opt => {
          if (opt.value != this.lossTypeAll) {
            temp.push(opt.value)
          }
        })
        formData.lossTypeFilter = temp
      }
      delete formData.alarmInfo.alarmLimitDescribe
      delete formData.alarmInfo.terminalAlarmParam
      formData.alarmInfo = JSON.stringify(formData.alarmInfo)
      return formData
    },
    msgValid(checked, ruleId) {
      let flag = true
      if (checked && !ruleId) {
        flag = false
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.install_Msg15'),
          type: 'warning',
          duration: 2000
        })
      }
      return flag
    },
    nameValidator(rule, value, callback) {
      const errorStr = value.match(nameRegExp)
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } if (nameRegExp.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_IllegalCharacter').concat('：').concat(errorStr.join(''))))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    lossTypeValidator(rule, value, callback) {
      if (this.temp.lossTypeFilter.length > 0) {
        callback()
      } else {
        callback(this.$t('pages.group_text5'))
      }
    },
    handleCheckAllChange(val) {
      this.stopOutgoingExList = val ? (this.hasEncPermision ? [1, 2, 4] : [1, 4]) : []
      this.isIndeterminate = false
    },
    handleCheckedChange(value) {
      const checkedCount = value.length
      this.checkedAllStopEx = (this.hasEncPermision ? checkedCount === 3 : checkedCount == 2)
      this.isIndeterminate = checkedCount > 0 && (this.hasEncPermision ? checkedCount < 3 : checkedCount < 2)
    }
  }
}
</script>

<style lang='scss' scoped>

</style>

