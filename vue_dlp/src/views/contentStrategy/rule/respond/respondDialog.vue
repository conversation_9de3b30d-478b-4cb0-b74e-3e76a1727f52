<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="dialogFormVisible"
    width="800px"
    @dragDialog="handleDrag"
    @closed="closeFunc"
  >
    <respond-form
      ref="respondForm"
      :loss-type-options="lossTypeOptions"
      @submitting="(val) => {submitting = val}"
      @closeDlg="() => {dialogFormVisible = false}"
      @updateGridTable="updateGridTable"
    />
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import RespondForm from './respondForm'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'

export default {
  name: 'RespondDialog',
  components: { RespondForm },
  data() {
    return {
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        deviceTypeFilter: [1],
        lossTypeFilter: [],
        warnContent: '',
        stopOutgoing: false
      },
      // lossTypeOptions: getLossTypeDict(),
      lossTypeOptions: [],
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.$t('pages.respond_update'),
        create: this.$t('pages.respond_create')
      },
      lossTypeAll: -1
    }
  },
  computed: {
    gridTable() {
      return this.$parent.respondTable
    }
  },
  created() {
    this.getSensitiveLossType()
  },
  methods: {
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        const data = res.data.map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
        this.lossTypeOptions = [{ label: this.$t('pages.lossTypeOptions'), value: this.lossTypeAll }, ...data]
      })
    },
    closeFunc() {
      this.$emit('respondClosed')
      this.$refs.respondForm.$refs.responseContent.checkRule = false
      this.$refs.respondForm.$refs.responseContent.ruleId = undefined
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['respondForm'].handleCreate()
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['respondForm'].handleUpdate(row)
      })
    },
    createData() {
      this.$refs['respondForm'].createData()
    },
    updateData() {
      this.$refs['respondForm'].updateData()
    },
    updateGridTable(type, data) {
      if (type == 'create') {
        this.$emit('createData', data)
        this.gridTable && this.gridTable.execRowDataApi()
      } else {
        this.gridTable && this.gridTable.updateRowData(data)
      }
    },
    filterFormatter(row, data) {
      const lossTypeNames = []
      let all = row.lossTypeFilter.length == 0 || row.lossTypeFilter.length == this.lossTypeOptions.length
      if (row.lossTypeFilter.length > 0) {
        for (let i = 0, size = this.lossTypeOptions.length; i < size; i++) {
          const id = this.lossTypeOptions[i].value
          if (row.lossTypeFilter.indexOf(id) >= 0) {
            lossTypeNames.push(this.lossTypeOptions[i].label)
          } else {
            all = false
          }
        }
      }
      return `${this.$t('pages.lossType')}：` + (all ? this.$t('pages.lossTypeOptions') : lossTypeNames.join(','))
    }
  }

}
</script>

<style lang='scss' scoped>

</style>
