<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    width="800px"
    :title="$t('pages.identifierRule_create')"
    :visible.sync="addRegularDialogVisible"
    @close="close"
    @dragDialog="handleDrag"
  >
    <Form ref="elForm" :model="formData" :rules="rules" label-width="100px">
      <FormItem :label="$t('table.name')" prop="name">
        <el-input v-model="formData.name" :placeholder="$t('pages.validateMsg_enterName')" maxlength="60" clearable style="width: 385px"></el-input>
      </FormItem>
      <FormItem :label="$t('table.regular')" prop="regular">
        <el-row>
          <el-col :span="$store.getters.language === 'en'? 15 : 14">
            <el-input v-model="formData.regular" clearable style="width: 385px" maxlength="" @blur="blurEvent"/>
          </el-col>

          <el-col :span="1">
            <el-tooltip class="item" effect="light" placement="bottom-end">
              <div slot="content">
                <div v-for="index in (1, 17)" :key="index" class="regular-tip">
                  <span>{{ $t('pages.identifierRule_tip' + index) }}</span>
                </div>
              </div>
              <i class="el-icon-info" style="margin-left:5px"/>
            </el-tooltip>
          </el-col>

          <el-col :span="$store.getters.language === 'en'? 8 : 9"><span>{{ '(' + $t('pages.identifierRule_text1') + ')' }}</span></el-col>

        </el-row>
      </FormItem>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.identifierRule_text8') }}</span><span style="color:grey;font-size: 12px;">{{ $t('pages.identifierRule_text9') }}</span>
        </div>
        <el-row style="display:flex;align-items:center">
          <el-col :span="10">
            {{ $t('pages.identifierRule_regular') }}
            <el-autocomplete
              v-model="temp.matchType"
              popper-class="my-autocomplete"
              clearable
              :fetch-suggestions="querySearch"
              :placeholder="$t('pages.identifierRule_text2')"
              style="width:250px"
            >
              <template slot-scope="{ item }">
                <div class="name">{{ item.name }}</div>
                <span class="value">{{ item.value }}</span>
              </template>
            </el-autocomplete>
          </el-col>
          <el-col :span="$store.getters.language === 'en' ? 9 : 7">
            {{ $t('pages.identifierRule_text3') }}&nbsp;
            <el-input v-model="temp.minNum" oninput="value=value.replace(/[^\d]/g,'')" style="width:60px"></el-input>
            &nbsp;~&nbsp;
            <el-input v-model="temp.maxNum" oninput="value=value.replace(/[^\d]/g,'')" style="width:60px"></el-input>
          </el-col>
          <!-- 取反不好处理先隐藏 -->
          <!-- <el-col :span="3">
            <el-checkbox v-model="temp.negation">{{ $t('pages.negation') }}</el-checkbox>
          </el-col> -->
          <el-col :span="2">
            <el-button type="primary" size="medium" style="margin-bottom:0" @click="insertIdentifierRule"> {{ $t('pages.identifierRule_insert') }} </el-button>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.matchingTest') }}</span><span style="color:grey;font-size: 12px;">{{ $t('pages.matchingTestExp') }}</span>
        </div>
        <el-input v-model="testText" type="textarea" maxlength="500" show-word-limit :placeholder="$t('pages.identifierRule_text6')" :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"/>
        <el-button type="primary" style="margin-top: 5px;" size="medium" :disabled="matchResLoading" @click="testMatch">测试</el-button>
        <div style="display: inline-table">
          <div v-if="checkOk === 10031">
            <i class="el-icon-success" style="color:#4995c5"/>
            <span style="margin-left:5px">
              {{ $t('pages.matchingResult') }}
              <span v-for="(text, index) in checkRes" :key="index" class="res-text">
                {{ text }}
              </span>
            </span>
          </div>
          <span v-else-if="checkOk === 10030" class="font-s12">{{ $t('pages.identifierRule_text5') }}</span>
        </div>
      </el-card>
    </Form>
    <div slot="footer">
      <el-button type="primary" @click="handleConfirm(false)">{{ $t('button.confirm') }}</el-button>
      <el-button type="primary" @click="handleConfirm(true)">{{ $t('pages.confirmAndSave') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { createIdentifierRule, matchRegular, getMatchResult, getByName } from '@/api/contentStrategy/rule/regular'

export default {
  name: 'AddRegularDialog',
  inheritAttrs: false,
  props: [],
  data() {
    return {
      formData: {
        name: '',
        regular: '',
        checkTimes: 1,
        modifyVer: 1
      },
      temp: {
        matchType: '',
        negation: false,
        minNum: '1',
        maxNum: ''
      },
      addRegularDialogVisible: false,
      restaurants: [],
      testText: '',
      checkOk: 0,
      checkRes: [],
      blurIndex: 0,
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        regular: [{
          required: true,
          message: this.$t('pages.identifierRule_text10'),
          trigger: 'change'
        }]
      },
      taskId: undefined,
      matchResLoading: false,
      timer: undefined
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.restaurants = this.loadAll();
  },
  methods: {
    handleCreate() {
      this.matchResLoading = false
      this.addRegularDialogVisible = true
      this.blurIndex = 0
      this.$nextTick(() => {
        this.$refs['elForm'].clearValidate()
      })
    },
    close() {
      const taskId = this.taskId;
      this.clearData()
      this.matchResLoading = false
      this.addRegularDialogVisible = false
      if (taskId) {
        this.getCheckResult(taskId);
      }
    },
    clearData() {
      this.checkOk = 0
      this.formData = { name: '', regular: '', check_times: 1, modify_ver: 1 }
      this.temp = { matchType: '', negation: false, minNum: '1', maxNum: '' }
      this.testText = ''
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = undefined
      }
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleConfirm(needSave) {
      const regular = this.formData.regular
      if (needSave && this.formData.name === '') {
        this.$message({
          message: this.$t('pages.validateMsg_enterName'),
          type: 'error'
        })
        return
      }
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          this.testText = ''
          if (needSave) {
            createIdentifierRule(this.formData).then(respond => {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.createSuccess'),
                type: 'success',
                duration: 2000
              })
              this.$emit('handleCheckedNewRule', regular)
              this.close()
            })
          } else {
            this.$emit('handleCheckedNewRule', regular)
            this.close()
          }
        }
      })
    },
    blurEvent(e) {
      this.blurIndex = e.srcElement.selectionStart
    },
    insertIdentifierRule() {
      const regular = this.formData.regular
      let regex_temp = '(' + (this.temp.negation ? '^' : '') + this.temp.matchType + ')'
      if ((this.temp.minNum !== '') || this.temp.maxNum !== '') {
        if (this.temp.maxNum !== '' && this.temp.minNum !== '' && Number(this.temp.maxNum) < Number(this.temp.minNum)) {
          this.$message({
            message: this.$t('pages.identifierRule_text7'),
            type: 'error'
          })
          return
        }
        regex_temp += '{' + (this.temp.minNum === '' ? '0' : this.temp.minNum) + ',' + this.temp.maxNum + '}'
      }
      if (regular.length === 0) {
        this.blurIndex = 0
      }
      this.formData.regular = regular.slice(0, this.blurIndex) + regex_temp + regular.slice(this.blurIndex)
      this.blurIndex += regex_temp.length
      this.temp = {
        matchType: '',
        negation: false,
        minNum: '1',
        maxNum: ''
      }
    },
    loadAll() {
      return [
        { 'name': this.$t('pages.number2'), 'value': '\\d' },
        { 'name': this.$t('pages.alphabet'), 'value': '[a-zA-Z]' },
        { 'name': this.$t('pages.lowercaseLetter'), 'value': '[a-z]' },
        { 'name': this.$t('pages.capitalLetter'), 'value': '[A-Z]' },
        { 'name': this.$t('pages.chinese'), 'value': '[\\x{4e00}-\\x{9fa5}]' },
        { 'name': this.$t('pages.character1'), 'value': '\\w' },
        { 'name': this.$t('pages.character2'), 'value': '[\\x{4e00}-\\x{9fa5}a-zA-Z0-9_]' },
        { 'name': this.$t('pages.blankCharacter'), 'value': '\\s' }
      ]
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    testMatch() {
      if (this.testText.length === 0 || this.formData.regular.length === 0) {
        this.checkOk = 0
        this.$message({
          message: this.$t('pages.identifierRule_text11'),
          type: 'warning'
        })
        return
      }
      matchRegular({ regular: this.formData.regular, matchText: this.testText }).then(res => {
        this.matchResLoading = true
        this.getCheckResult(res.data)
      })
    },
    getCheckResult(taskId) {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        getMatchResult(taskId).then(respond => {
          if (respond.data) {
            const json = JSON.parse(respond.data)
            if (json.result === 10031) {
              this.matchResLoading = false
              this.checkOk = json.result
              this.checkRes = json.matchValue.split(',')
              this.taskId = undefined;
            } else if (json.result === 10030) {
              this.checkOk = json.result
              this.matchResLoading = false
            } else {
              this.getCheckResult(taskId)
            }
          } else {
            this.getCheckResult(taskId)
          }
        }).catch(reason => {
          this.checkOk = 0
        })
      }, 500)
    },
    nameValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else {
        getByName({ name: value }).then(respond => {
          const identifierRule = respond.data
          if (identifierRule && identifierRule.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    }
  }
}

</script>
<style lang="scss" scoped>
.my-autocomplete li{
    line-height: normal;
    padding: 7px;
}
.my-autocomplete .name {
  color: black;
}
.my-autocomplete .value {
  font-size: 10px;
  color: gray;
}
.font-s12 {
  font-size: 12px;
}
.regular-tip {
  font-size: 14px;
  color: gray;
}
.regular-tip span{
  line-height: 20px;
}
.res-text {
  color: red;
}
</style>
