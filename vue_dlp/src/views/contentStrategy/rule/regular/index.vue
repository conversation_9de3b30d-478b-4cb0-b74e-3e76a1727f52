<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addRule') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="regularRuleList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="(row) => { return !row.disableEdit }" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <regular-dialog ref="regularDialog" />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="$t('text.batchDelete', {info: $t('route.regular')})"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :filter-name-desc="$t('route.regular')"
      :edit-type="1"
      @submitEnd="batchDeleteFunc"
    />
  </div>
</template>

<script>
import { isEmpty } from '@/utils/validate'
import { getRulePage, deleteRule, getPreciseRulePage } from '@/api/contentStrategy/rule/regular'
import RegularDialog from './regularDialog'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

export default {
  name: 'RegularRule',
  components: { RegularDialog, BatchEditPageDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'ruleName', width: '150', fixed: true },
        { prop: 'regular', label: 'regular', width: '150' },
        { prop: 'lua', label: 'lua', width: '150' },
        { prop: 'matchType', label: 'matchRule', width: '150', formatter: this.matchRuleFormatter },
        { prop: 'exceptWords', label: 'exceptWords', width: '150' },
        { prop: 'checkTimes', label: 'checkTimes', width: '150', formatter: this.checkTimesFormatter },
        // { prop: 'checkPosition', label: '匹配位置', width: '100', formatter: this.checkPositionFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate, disabledFormatter: (row) => { return row.disableEdit } }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      deleteable: false,
      preciseIDRules: []
    }
  },
  computed: {
    regularTable() {
      return this.$refs['regularRuleList']
    }
  },
  created() {
    this.loadPreciseRulePage()
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRulePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.regularTable.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.regularDialog.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.regularDialog.handleUpdate(row)
    },
    handleDelete() {
      const selectedDatas = this.regularTable.getSelectedDatas()
      const total = this.regularTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedDatas, total, this.query)
    },
    checkPositionFormatter(row, data) {
      return this.$refs.regularDialog.checkPositionFormatter(row, data)
    },
    checkTimesFormatter(row, data) {
      return this.$refs.regularDialog.checkTimesFormatter(row, data)
    },
    batchDeleteFunc(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.regularTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch((e) => { callback(e) })
      }).catch((e) => { callback(e) })
    },
    loadPreciseRulePage() {
      getPreciseRulePage().then(respond => {
        this.preciseIDRules = respond.data
      })
    },
    matchRuleFormatter(row, data) {
      if (data) {
        const rule = this.preciseIDRules.filter(r => r.matchType == data)[0]
        return isEmpty(rule) ? '' : rule.name
      }
      return ''
    }
  }
}
</script>
