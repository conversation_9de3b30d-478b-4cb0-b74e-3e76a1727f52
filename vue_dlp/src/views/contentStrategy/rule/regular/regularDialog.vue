<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="regularDialogVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="closeFunc"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="95px" style="width: 700px;">
        <FormItem :label="$t('table.ruleName')" prop="name">
          <el-input v-model="temp.name" v-trim maxlength="60" :disabled="!formable"></el-input>
        </FormItem>
        <el-card class="box-card" :body-style="{padding: '5px','padding-bottom':'0'}">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.keyword_text1') }}</span>
            <el-tooltip v-show="formable" class="item" effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.regular_text1') }}<br/>
                {{ $t('pages.regular_text2') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <FormItem v-show="formable">
            <el-radio-group v-model="temp.mode" @change="modeChange">
              <el-radio :label="1" border style="padding-top: 5px;">{{ $t('pages.regularMode1') }}</el-radio>
              <el-radio :label="2" border style="padding-top: 5px;">{{ $t('pages.regularMode2') }}</el-radio>
              <el-radio :label="3" border style="padding-top: 5px;">{{ $t('pages.regularMode3') }}</el-radio>
            </el-radio-group>
          </FormItem>
          <div v-if="temp.mode !== 3">
            <FormItem :label="$t('table.regular')" prop="regular">
              <el-input v-model="temp.regular" type="textarea" resize="none" maxlength="512" :placeholder="$t('pages.inputMaxLength', { num: 512 })" :disabled="!formable" style="width: 93%;" @keydown.native="listenKey($event)"></el-input>
              <el-popover
                v-model="visible1"
                :disabled="!formable"
                style="margin-left: 4px"
                placement="right"
                width="400"
                trigger="manual"
              >
                <tree-menu
                  ref="identifierRule"
                  style="height: 370px"
                  :data="defaultIdentifierRuleList"
                  :render-content="renderContent"
                  :is-filter="false"
                  :default-expand-all="true"
                  @check-change="checkChange"
                />
                <div>
                  <div style="float:left; margin-top: 10px">
                    <el-button size="mini" type="primary" @click="handleAdd">{{ $t('button.add') }}</el-button>
                  </div>
                  <div style="float:right; margin-top: 10px">
                    <el-button type="primary" size="mini" @click="handleCheckedIdentifierRule">{{ $t('button.confirm') }}</el-button>
                    <el-button size="mini" @click="visible1 = false">{{ $t('button.cancel') }}</el-button>
                  </div>
                </div>
                <el-button v-show="formable" slot="reference" :title="$t('pages.importModel')" icon="el-icon-plus" style="margin-bottom: 15px;" round @click="visible1 = !visible1"></el-button>
              </el-popover>
            </FormItem>
            <FormItem :label="$t('pages.precise_matching')">
              {{ $t('pages.regular_text6') }}
              <el-checkbox-group v-model="checkPreciseMatching" style="display:contents" :disabled="!formable" @change="handleCheckedMatch">
                <el-checkbox label="number">{{ $t('pages.number2') }}</el-checkbox>
                <el-checkbox label="alphabet">{{ $t('pages.alphabet') }}</el-checkbox>
                <el-checkbox label="chinese">{{ $t('pages.chinese') }}</el-checkbox>
              </el-checkbox-group>
            </FormItem>
            <FormItem v-show="temp.mode === 2" class="required" :label="$t('table.lua')" prop="lua">
              <el-input v-model="temp.lua" type="textarea" :placeholder="placeholder" maxlength="512" resize="none" :disabled="!formable" :rows="8" style="width: 93%;"></el-input>
              <el-popover
                v-model="visible2"
                :disabled="!formable"
                style="margin-left: 4px"
                placement="right"
                width="400"
                trigger="click"
              >
                <tree-menu ref="luaRule" style="height: 370px" :data="defaultLuaRuleList" :is-filter="false" :default-expand-all="true"/>
                <div style="text-align: right; margin-top: 10px">
                  <el-button type="primary" size="mini" @click="handleCheckedLuaRule">{{ $t('button.confirm') }}</el-button>
                  <el-button size="mini" @click="visible2 = false">{{ $t('button.cancel') }}</el-button>
                </div>
                <el-button v-show="formable" slot="reference" :title="$t('pages.importModel')" icon="el-icon-plus" style="margin-bottom: 75px;" round></el-button>
              </el-popover>
            </FormItem>
          </div>
          <div v-if="temp.mode === 3">
            <FormItem :label="$t('table.matchRule')" prop="matchType">
              <el-select v-model="temp.matchType" style="width: 250px" :placeholder="$t('text.select')" :disabled="!formable" @change="handlePreciseRulesChange">
                <el-option
                  v-for="item in preciseIDRules"
                  :key="item.matchType"
                  :label="item.name"
                  :value="item.matchType"
                >
                </el-option>
              </el-select>
            </FormItem>
            <FormItem :label="$t('table.remark')" prop="remark">
              <el-input v-model="temp.remark" type="textarea" resize="none" disabled :autosize="{ minRows: 2, maxRows: 4}" style="width: 93%;"></el-input>
            </FormItem>
          </div>
        </el-card>
        <!--      例外条件-->
        <el-card v-show="!isDoDrip" class="box-card" :body-style="{padding: '0 5px'}">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.excepeCondition') }}</span>
          </div>
          <FormItem :label="$t('table.keyword')" prop="exceptWords">
            <span>{{ $t('pages.keyword_text3') }}</span>
            <el-input v-model="temp.exceptWords" type="textarea" maxlength="1024" :placeholder="$t('pages.keyword_text14')" :disabled="!formable" @keydown.native="listenKey($event)"></el-input>
          </FormItem>
        </el-card>
        <!--      匹配计数-->
        <FormItem v-show="!isDoDrip" :label="$t('table.checkTimes')" prop="checkTimes" style="margin-bottom: 0px;">
          <el-radio-group v-model="checkTimesType" @change="checkTimesTypeChange">
            <el-radio v-for="item in checkTimesTypeOptions" :key="item.value" :label="item.value" :disabled="!formable">
              {{ item.labelStart }}
              <el-input v-show="item.input" v-model="temp.checkTimes" :disabled="!formable || !checkTimesEditable" style="width: 57px;" maxlength="3" @blur="checkTimesChange"></el-input>
              {{ item.labelEnd }}
            </el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-show="!isDoDrip && checkTimesEditable">
          <el-checkbox v-model="temp.uniqueMatch" true-label="true" false-label="false" :disabled="!formable" style="margin-left: 40px;">{{ '匹配去重' }}</el-checkbox>
        </FormItem>
        <!--<FormItem label="匹配位置" prop="checkPosition">
          <el-checkbox-group v-model="temp.checkPosition">
            <el-checkbox v-for="item in checkPositionOptions" :key="item.value" :label="item.value" :disabled="!formable">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </FormItem>-->
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="false" type="primary" @click="handleModify">
          {{ $t('button.edit') }}
        </el-button>
        <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="closeDialog">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <add-regular-dlg ref="addRegularDialog" @handleCheckedNewRule="handleCheckedNewRule"/>
  </div>
</template>

<script>
import { getRuleByName, createRule, updateRule, listLuaRuleTreeNode, deleteIdentifierRule, getPreciseRulePage } from '@/api/contentStrategy/rule/regular'
import { listIdentifierRuleTreeNode } from '@/api/contentStrategy/rule/group'
import AddRegularDlg from './addRegularDlg'
import { nameRegExp } from '../ruleRegExp'

export default {
  name: 'RegularDialog',
  components: { AddRegularDlg },
  data() {
    return {
      checkPositionOptions: [
        { value: 1, label: this.$t('pages.checkPosition1') },
        { value: 2, label: this.$t('pages.checkPosition2') },
        { value: 3, label: this.$t('pages.checkPosition3') },
        { value: 4, label: this.$t('pages.checkPosition4') }
      ],
      checkTimesTypeOptions: [
        { value: 1, labelStart: this.$t('pages.keyword_text6'), labelEnd: '', input: false },
        { value: 2, labelStart: this.$t('pages.keyword_text7'), labelEnd: this.$t('pages.keyword_text8'), input: true }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        regular: '',
        lua: '',
        mode: 1,
        exceptWords: '',
        // checkPosition: [],
        checkTimes: 1,
        matchType: undefined,
        uniqueMatch: false,
        remark: ''
      },
      isDoDrip: false, // 零星检测勾选时，隐藏例外条件和匹配计数
      checkTimesType: 1,
      checkTimesEditable: false,
      regularDialogVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.regularRule'), 'update'),
        create: this.i18nConcatText(this.$t('pages.regularRule'), 'create'),
        view: this.i18nConcatText(this.$t('pages.regularRule'), 'details')
      },
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        regular: [
          { required: true, message: this.$t('pages.regular_text3'), trigger: 'blur' }
        ],
        lua: [
          { trigger: 'blur', validator: this.luaValidator }
        ],
        matchType: [
          { required: true, message: this.$t('pages.regular_text7'), trigger: 'change' }
        ],
        exceptWords: [{ trigger: 'blur', validator: this.contentValidator }]
        // checkPosition: [{ required: true, message: '匹配位置必填', trigger: 'blur' }]
      },
      submitting: false,
      isRuleUpdate: false,
      formable: true,
      visible1: false,
      visible2: false,
      defaultIdentifierRuleList: [{ label: this.$t('table.regular'), id: 0, children: [] }],
      defaultLuaRuleList: [{ label: this.$t('table.lua'), id: 0, children: [] }],
      placeholder: `${this.$t('pages.inputMaxLength', { num: 512 })}\n` + `${this.$t('pages.regular_text4')}：\n` +
        'function func(str)\n' +
        '    if (' + `${this.$t('table.condition')}` + ') then\n' +
        '        return true\n' +
        '    else\n' +
        '        return false\n' +
        '    end\n' +
        'end',
      checkPreciseMatching: [],
      deleteable: false,
      preciseIDRules: [],
      selectPreciseIDRule: {},
      isPreciseMode: false
    }
  },
  computed: {
    gridTable() {
      return this.$parent.regularTable
    }
  },
  created() {
    this.resetTemp()
    this.loadIdentifierRuleTree()
    this.loadLuaRuleTree()
    this.loadPreciseRulePage()
  },
  methods: {
    loadIdentifierRuleTree() {
      listIdentifierRuleTreeNode().then(respond => {
        this.defaultIdentifierRuleList = [{ label: this.$t('table.regular'), id: 0, children: respond.data }]
      })
    },
    loadLuaRuleTree() {
      listLuaRuleTreeNode().then(respond => {
        this.defaultLuaRuleList[0].children = respond.data
      })
    },
    loadPreciseRulePage() {
      getPreciseRulePage().then(respond => {
        this.preciseIDRules = respond.data
      })
    },
    handleCheckedIdentifierRule() {
      const checkNode = this.$refs.identifierRule.getCurrentNode()
      if (checkNode && checkNode.oriData) {
        this.temp.regular = checkNode.oriData.regular
        this.checkPreciseMatching = []
        this.visible1 = false
        this.$refs['dataForm'].clearValidate(['regular'])
      }
    },
    handleCheckedLuaRule() {
      const checkNode = this.$refs.luaRule.getCurrentNode()
      if (checkNode && checkNode.oriData) {
        this.temp.lua = checkNode.oriData.lua
        this.temp.regular = checkNode.oriData.regular
        this.checkPreciseMatching = []
        this.visible2 = false
        this.$refs['dataForm'].clearValidate(['regular', 'lua'])
      }
    },
    handleCheckedNewRule(newRegular) {
      this.temp.regular = newRegular
      this.checkPreciseMatching = []
      this.$refs['dataForm'].clearValidate(['regular'])
      this.loadIdentifierRuleTree()
    },
    initCheckedMatch(str) {
      const reg = /^\(\?\<!\[.*?\]\).*\(\?!\[.*?\]\)$/
      if (reg.test(str)) {
        const startStr = str.match(/^\(\?\<!\[.*?\]\)/)[0].replace('(?<![', '').replace('])', '')
        const endStr = str.match(/\(\?!\[.*?\]\)$/)[0].replace('(?![', '').replace('])', '')
        if (startStr.indexOf('0-9') >= 0 && endStr.indexOf('0-9') >= 0) {
          this.checkPreciseMatching.push('number')
        }
        if (startStr.indexOf('a-zA-Z') >= 0 && endStr.indexOf('a-zA-Z') >= 0) {
          this.checkPreciseMatching.push('alphabet')
        }
        if (startStr.indexOf('\\x{4e00}-\\x{9fa5}') >= 0 && endStr.indexOf('\\x{4e00}-\\x{9fa5}') >= 0) {
          this.checkPreciseMatching.push('chinese')
        }
      }
    },
    handleCheckedMatch(val) {
      let str = ''
      let originalRegular = this.temp.regular
      let startStr = ''
      let endStr = ''
      if (originalRegular.match(/^\(\?\<!\[.*?\]\)/)) {
        startStr = originalRegular.match(/^\(\?\<!\[.*?\]\)/)[0].replace(/\(\?<!\[|]\)|0-9|a-zA-Z|\\x{4e00}-\\x{9fa5}/g, '')
        originalRegular = originalRegular.replace(/^\(\?\<!\[.*?\]\)/, '')
      }
      if (originalRegular.match(/\(\?!\[.*?\]\)$/)) {
        endStr = originalRegular.match(/\(\?!\[.*?\]\)$/)[0].replace(/\(\?!\[|]\)|0-9|a-zA-Z|\\x{4e00}-\\x{9fa5}/g, '')
        originalRegular = originalRegular.replace(/\(\?!\[.*?\]\)$/, '')
      }
      if (val.length === 0 && startStr.length === 0 && endStr.length === 0) {
        this.temp.regular = originalRegular
        return
      }
      if (val.indexOf('number') >= 0) {
        str += '0-9'
      }
      if (val.indexOf('alphabet') >= 0) {
        str += 'a-zA-Z'
      }
      if (val.indexOf('chinese') >= 0) {
        str += '\\x{4e00}-\\x{9fa5}'
      }
      startStr = startStr + str
      endStr = endStr + str
      this.temp.regular = (startStr.length > 0 ? '(?<![' + startStr + '])' : '') + originalRegular + (endStr.length > 0 ? '(?![' + endStr + '])' : '')
    },
    modeChange(val) {
      this.isPreciseMode = false
      if (val == 1) {
        this.temp.lua = ''
      } else if (val == 3) {
        this.isPreciseMode = true
      }
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.handlePreciseRulesChange(this.temp.matchType)
    },
    listenKey(e) {
      if (e.keyCode === 13) {
        e.preventDefault() // 阻止浏览器默认换行操作
        return false
      }
    },
    closeFunc() {
      const data = this.isRuleUpdate ? 'G3' : ''
      const id = this.ruleId
      this.$emit('regularClosed', data, id)
      this.isRuleUpdate = false
      this.formable = true
      this.ruleId = ''
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.checkPreciseMatching = []
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.regularDialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      this.checkTimesTypeChange(this.temp.checkTimes)
      this.dialogStatus = 'update'
      this.regularDialogVisible = true
      this.handlePreciseRulesChange(this.temp.matchType)
      this.initCheckedMatch(this.temp.regular)
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleView(row, doDrip) {
      // console.log('row', row, doDrip)
      if (doDrip) {
        this.isDoDrip = true
      } else {
        this.isDoDrip = false
      }
      this.formable = false
      this.handleUpdate(row)
      this.dialogStatus = 'view'
    },
    handleModify() {
      this.dialogStatus = 'update'
      this.formable = !this.formable
    },
    handleAdd() {
      this.visible1 = false
      this.visible2 = false
      this.$refs.addRegularDialog.handleCreate()
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != null
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.deleteRegular(r, data)} />
          </span>
        </div>
      )
    },
    deleteRegular(event, data) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg2'), this.$t('text.prompt')).then(() => {
        deleteIdentifierRule({ ids: data.dataId }).then(res => {
          this.loadIdentifierRuleTree()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      })
    },
    checkChange(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatKeyWord();
          this.formatMode(this.temp.mode);
          createRule(this.temp).then(respond => {
            this.ruleId = respond.data.id
            this.submitting = false
            this.isRuleUpdate = true
            this.regularDialogVisible = false
            this.visible1 = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatKeyWord();
          this.formatMode(this.temp.mode);
          const tempData = Object.assign({}, this.temp)
          updateRule(tempData).then(respond => {
            this.ruleId = respond.data.id
            this.submitting = false
            this.isRuleUpdate = true
            this.regularDialogVisible = false
            this.visible1 = false
            this.gridTable.updateRowData(respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      const errorStr = value.match(nameRegExp)
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } if (nameRegExp.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_IllegalCharacter').concat('：').concat(errorStr.join(''))))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    luaValidator(rule, value, callback) {
      if (this.temp.mode === 2 && !value) {
        callback(new Error(this.$t('pages.regular_text5')))
      } else {
        callback()
      }
    },
    contentValidator(rule, value, callback) {
      if (value && value.indexOf('\\n') > -1) {
        callback(new Error(this.$t('pages.fileFp_Msg12')))
      } else if (value && value.indexOf('，') > -1) {
        callback(new Error(this.$t('pages.fileFp_Msg13')))
      } else {
        const keywords = value.split(',')
        if (keywords.length > 50) {
          callback(new Error(this.$t('pages.keyword_text14')))
        }
        const maxLength = keywords.reduce((max, keyword) => Math.max(max, keyword.length), 0)
        // 单个关键字不能超过256个字符。
        if (maxLength > 256) {
          callback(new Error(this.$t('pages.keyword_text13')))
        }
        callback()
      }
    },
    checkTimesChange(event) {
      const val = event.target.value.trim()
      if (val < 2) {
        this.checkTimesType = 1
        this.checkTimesEditable = false
        this.temp.checkTimes = 1
        this.temp.uniqueMatch = false
      }
    },
    checkTimesTypeChange: function(data) {
      if (data > 1) {
        this.checkTimesType = 2
        this.checkTimesEditable = true
        this.temp.checkTimes = data
      } else {
        this.checkTimesType = 1
        this.checkTimesEditable = false
        this.temp.checkTimes = 1
        this.temp.uniqueMatch = false
      }
    },
    // checkPositionFormatter(row, data) {
    //   const positionNames = []
    //   for (let i = 0, size = this.checkPositionOptions.length; i < size; i++) {
    //     const id = this.checkPositionOptions[i].value
    //     if (data.indexOf(id) >= 0) {
    //       positionNames.push(this.checkPositionOptions[i].label)
    //     }
    //   }
    //   return positionNames.join(',')
    // },
    checkTimesFormatter(row, data) {
      let option
      if (row.checkTimes === 1) {
        option = this.checkTimesTypeOptions[0]
      } else {
        if (row.uniqueMatch) {
          option = { value: 3, labelStart: this.$t('pages.keyword_text15'), labelEnd: this.$t('pages.keyword_text8'), input: true }
        } else {
          option = this.checkTimesTypeOptions[1]
        }
      }
      return option.labelStart + (option.input ? row.checkTimes : '') + option.labelEnd
    },
    addRegular() {
      this.$refs.addRegularDialog.init()
      this.regularDialogVisible = false
      this.visible1 = false
      this.closeFunc()
    },
    returnPage() {
      this.regularDialogVisible = true
    },
    // 新增正则后将新增的表达式传到新增数据标识符规则的正则表达式中
    returnAddRegular(value) {
      this.temp.regular = value
      this.checkPreciseMatching = []
    },
    // 格式化关键字（主要为了去除连续输入的","和"-"，例如：1,2,,,,,3，去除后：1,2,3）
    formatKeyWord() {
      const exceptWords = this.temp.exceptWords;
      this.temp.exceptWords = exceptWords.replaceAll(/,{2,}/g, ',').replaceAll(/-{2,}/g, '-');
    },
    formatMode(val) {
      if (val == 1 || val == 2) {
        this.temp.matchType = undefined
      } else if (val == 3) {
        this.temp.regular = ''
        this.temp.lua = ''
      }
    },
    closeDialog() {
      this.regularDialogVisible = false
      this.visible1 = false
    },
    handlePreciseRulesChange(value) {
      if (value) {
        const rule = this.preciseIDRules.filter(r => r.matchType == value)[0]
        this.temp.matchType = rule.matchType
        this.temp.remark = rule.remark
        this.selectPreciseIDRule = rule
      } else {
        this.temp.matchType = undefined
        this.temp.remark = ''
        this.selectPreciseIDRule = undefined
      }
    }
  }
}
</script>

<style lang='scss' scoped>

</style>
