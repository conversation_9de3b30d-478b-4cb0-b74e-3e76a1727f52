<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="edmDialogFormVisible"
    width="800px"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="110px" style="width: 700px; margin-left:30px;">
      <FormItem :label="$t('pages.fingerDatabase')" prop="groupId">
        <tree-select
          ref="groupTree"
          :data="groupData"
          node-key="dataId"
          :disabled="!formable || dialogStatus==='update'"
          :checked-keys="[temp.groupId]"
          :filter-key="filterKey"
          :filter-node-method="filterNode"
          :icon-option="iconOption"
          :width="296"
          @change="groupNodeSelectChange"
        />
      </FormItem>
      <FormItem :label="$t('pages.ruleName')" prop="name">
        <el-input v-model="temp.name" v-trim maxlength="60" :disabled="!formable" />
      </FormItem>

      <FormItem v-if="groupType != 1" :label="$t('pages.acquisitionCycle')" :tooltip-content="$t('pages.edm_Msg2')" prop="cycle" tooltip-placement="bottom-start">
        <el-input v-model.number="temp.cycle" v-trim maxlength="3" :disabled="!formable" :placeholder="$t('pages.edm_Msg3')">
          <template slot="append">{{ $t('text.day') }}</template>
        </el-input>
      </FormItem>
      <!-- <el-row v-if="groupType != 1">
        <el-col :span="23">
          <FormItem :label="$t('pages.acquisitionCycle')" :tooltip-content="$t('pages.edm_Msg2')" prop="cycle">
            <el-input v-model.number="temp.cycle" v-trim :maxlength="3" :disabled="!formable" :placeholder="$t('pages.edm_Msg3')">
              <template slot="append">{{ $t('pages.day') }}</template>
            </el-input>
          </FormItem>
        </el-col>
        <el-col :span="1" style="padding: 5px;">
          {{ $t('pages.day') }}
        </el-col>
      </el-row> -->
      <!-- <el-row style="margin: 0 0 0 100px;">
        <el-checkbox v-model="temp.inTerminal" :disabled="!formable" :true-label="1" :false-label="0" >{{ $t('pages.inTerminal') }}</el-checkbox>
      </el-row> -->
      <el-card class="box-card" :body-style="{padding: '0 5px 5px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.inclusionConditions') }}</span>
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.inclusionConditionsTips">
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </div>
        <div v-if="groupType == 1" style="margin-top: 5px;">
          <FormItem v-show="!uploaded" :label="$t('pages.fileName')" prop="fileName" label-width="127px">
            <el-tooltip slot="tooltip" effect="dark" placement="bottom-start" style="margin-left: 5px;">
              <div slot="content">
                {{ $t('pages.fileFp_Msg23') }}<br/>
                {{ $t('pages.fileFp_Msg24') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
            <el-input v-model="temp.fileName" class="input-with-botton" maxlength="100" disabled no-limit :placeholder="$t('pages.fileFp_Msg23')" style="width: calc(100% - 130px);"/>
            <el-upload
              ref="upload"
              class="upload-demo"
              name="uploadFile"
              action="aaaaaa"
              accept=".txt,.csv,.xls,.xlsx"
              :auto-upload="false"
              :limit="1"
              :show-file-list="false"
              :on-change="handleUploadFileChange"
              style="display: inline-block;"
            >
              <el-button size="small" type="primary" style="width: 120px; height: 30px; margin: 0;" @click="clearFiles">{{ loadText }}</el-button>
            </el-upload>
          </FormItem>
          <FormItem v-show="uploaded || dialogStatus=='view'" :label="$t('pages.originalFileName')">
            <el-input v-model="temp.fileName" maxlength="100" disabled></el-input>
          </FormItem>

          <FormItem label-width="127px">
            <el-checkbox v-model="temp.firstRowAsColName" :true-label="1" :false-label="0" :disabled="!formable" >{{ $t('pages.edm_Msg4') }}</el-checkbox>
          </FormItem>
          <FormItem :label-width="uploaded ? '100px' : '0'">
            <el-input
              v-model="temp.colSeparator"
              :disabled="!formable || fileSuffix.indexOf(temp.fileName.substring(temp.fileName.lastIndexOf('.'))) > -1"
              :placeholder="$t('pages.edm_Msg5')"
              class="input-with-button"
              style="width: calc(100% - 130px);"
              no-limit
            />
            <el-button
              type="primary"
              :disabled="!formable || temp.colSeparator==''"
              :title="$t('pages.edm_Msg6')"
              class="ellipsis"
              style="width: 120px; height: 30px;"
              @click="colCollect()"
            >{{ $t('pages.edm_Msg6') }}</el-button>
          </FormItem>
          <FormItem label-width="0" style="margin-top: 5px;" prop="collectColList">
            <grid-table
              ref="collectColList"
              :height="200"
              row-key="colNum"
              :col-model="collectColModel"
              :checked-row-keys="selectedCols"
              :selectable="() => { return formable }"
              :autoload="false"
              :row-data-api="collectColRowDataApi"
              :show-pager="false"
              @selectionChangeEnd="collectColSelectionChangeEnd"
            />
          </FormItem>
        </div>
        <div v-if="groupType != 1">
          <el-radio-group v-model="gatherDBType" :disabled="!formable" @input="gatherDBTypeChange" >
            <el-radio :label="1">{{ $t('pages.SQLCollection') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.singleAcquisition') }}</el-radio>
          </el-radio-group>
          <div :class="{DBListHide: gatherDBType == 2}">
            <el-input v-model="temp.querySql" type="textarea" maxlength="600" class="input-with-button" style="width: calc(100% - 80px);" :rows="2" resize="none" :disabled="!formable" @change="querySqlChange"></el-input>
            <el-button type="primary" :disabled="!formable || temp.querySql==''" style="width: 70px;" @click="querySqlValidate">{{ $t('pages.verification') }}</el-button>
          </div>
          <div :class="{DBListHide: gatherDBType == 1}">
            <FormItem :label="$t('pages.acquisitionTable')" prop="tableName">
              <el-select v-model="temp.tableName" filterable :placeholder="$t('text.select')" :disabled="!formable" @change="tableChange">
                <el-option
                  v-for="item in tableOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </FormItem>
            <FormItem label-width="0" style="margin-top: 5px;" prop="sqlColList">
              <grid-table
                ref="sqlColList"
                :height="200"
                row-key="name"
                :checked-row-keys="selectedCols"
                :col-model="sqlColModel"
                :selectable="() => { return formable }"
                :autoload="false"
                :row-data-api="sqlColRowDataApi"
                :show-pager="false"
                @select="sqlColSelectionChangeEnd"
                @select-all="sqlColSelectionChangeEnd"
                @selectionChangeEnd="sqlColSelectionInit"
              />
            </FormItem>
          </div>
        </div>
        <FormItem :label="$t('table.minMatchColumn')" prop="minMatchColumn" style="margin-top: 5px; ">
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.edm_Msg18') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-input v-model.number="temp.rule.minMatchColumn" :disabled="!formable" maxlength="1024" style="width: calc(99% - 20px);">
            <template slot="append"> {{ $t('pages.column') }}</template>
          </el-input>
        </FormItem>
      </el-card>
      <el-card class="box-card" :body-style="{padding: '0 5px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.exceptions') }}</span>
        </div>
        <FormItem :label="$t('pages.keywords')" label-width="80px" prop="exceptWords">
          <span>{{ $t('pages.fileFp_Msg9') }}</span>
          <el-input v-model="temp.rule.exceptWords" no-limit type="textarea" maxlength="1024" :placeholder="$t('pages.keyword_text14')" :rows="3" resize="none" :disabled="!formable" @keydown.native="listenKey($event)"></el-input>
        </FormItem>
      </el-card>
      <FormItem v-show="groupType != 1" :label="$t('table.matchCount')" prop="checkTimes">
        <el-radio-group v-model="checkTimesType" :disabled="!formable" @change="checkTimesTypeChange">
          <el-radio v-for="item in checkTimesTypeOptions" :key="item.value" :label="item.value">
            <i18n :path="`${item.label}`">
              <el-input-number v-show="item.input" slot="input" v-model="temp.rule.checkTimes" :disabled="!formable || !checkTimesEditable" :precision="0" :controls="false" :min="1" :max="999" size="mini" style="width:60px;"/>
              <template slot="space" >&nbsp;</template>
            </i18n>
          </el-radio>
        </el-radio-group>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="edmDialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getRuleById, getRuleByName, createRule, updateRule, uploadFile, listTableCol, listFileColInfo, listTable, getTreeNode } from '@/api/contentStrategy/rule/edm'
import { isUtf8File } from '@/utils/utf8'
import { nameRegExp } from '../ruleRegExp'

export default {
  name: 'EdmDialog',
  data() {
    return {
      checkTimesTypeOptions: [
        { value: 1, label: 'pages.fileFp_Msg1', input: false },
        { value: 2, label: 'pages.fileFp_Msg2', input: true }
      ],
      temp: {},
      rowTemp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        fileName: '',
        fileMd5: '',
        firstRowAsColName: 0,
        colSeparator: '',
        querySql: '',
        cycle: '',
        groupId: null,
        state: 0,
        trainCostTime: '',
        trainTime: '',
        tableName: '',
        inTerminal: 0,
        rule: {
          file: '',
          exceptWords: '',
          caseSensitive: false,
          checkTimes: 2,
          minMatchColumn: null
        }
      },
      checkTimesType: 1,
      checkTimesEditable: false,
      edmDialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.edmRule'), 'update'),
        create: this.i18nConcatText(this.$t('pages.edmRule'), 'create'),
        view: this.i18nConcatText(this.$t('pages.edmRule'), 'details')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileName: { required: true, validator: this.fileValidator, trigger: 'change' },
        collectColList: { validator: this.collectColValidator, trigger: 'blur' },
        tableName: { validator: this.tableNameValidator, trigger: 'blur' },
        sqlColList: { validator: this.sqlColValidator, trigger: 'blur' },
        cycle: { required: true, validator: this.cycleValidator, trigger: 'blur' },
        exceptWords: [{ trigger: 'blur', validator: this.contentValidator }],
        minMatchColumn: [{ validator: this.minMatchColumnValidator, trigger: 'blur' }]
      },
      submitting: false,
      formable: true,
      loadText: this.$t('pages.clickUpload'),
      selectedCols: [], // 选中的文件采集列编号列表
      filterKey: '',
      iconOption: {
        typeKey: 'oriData.dbType',
        1: 'file',
        2: 'database',
        3: 'database'
      },
      fileSuffix: ['.csv', '.xls', '.xlsx'],
      uploaded: false,
      collectColModel: [ // 文档库
        { prop: 'colNum', label: 'number', width: '100' },
        { prop: 'colText', label: 'colText', width: '150' }
      ],
      sqlColModel: [ // 数据库
        { prop: 'name', label: 'colName', width: '100' },
        { prop: 'type', label: 'dataType', width: '150' },
        { prop: 'comment', label: 'notes', width: '150' }
      ],
      colSelected: [],
      tableOptions: [], // 采集表下拉框表名数据
      gatherDBType: 1, // 采集数据库指纹类型, 1.SQL采集  2.单表采集
      groupData: [], // 指纹库数据
      groupType: 1,  // 指纹库类型， 1 文档库，其余值 数据库
      maxMatchColumn: 1 // 最大匹配列数
    }
  },
  watch: {
    '$store.state.commonData.notice.edmLibrary'(val) {
      this.loadEdmGroupTree()
    },
    tableOptions(val) {
      // 监听采集表 如果表数量大于0 再获取对应表的列
      if (this.tableOptions.length > 0 && this.temp.tableName) {
        this.tableChange(this.temp.tableName)
      }
    }
  },
  created() {
    this.resetTemp()
    this.loadEdmGroupTree()
  },
  methods: {
    groupTree() {
      return this.$refs['groupTree']
    },
    // 加载指纹库树数据
    loadEdmGroupTree() {
      getTreeNode().then(res => {
        this.groupData = res.data
      })
    },
    // 文档库：采集列名
    colCollect() {
      this.temp.cols = ''
      this.selectedCols.splice(0)
      this.collectColTable() && this.collectColTable().execRowDataApi(this.temp)
    },
    initCollectTable() {
      this.selectedCols.splice(0)
      const cols = this.temp.cols
      if (cols) {
        this.selectedCols.push(...cols.split(',').map(col => Number(col)))
      }
      this.$nextTick(() => {
        this.collectColTable() && this.collectColTable().execRowDataApi(this.temp)
      })
    },
    loadSQLTableTree(data) {
      this.tableOptions = []
      this.clearSqlTable()
      listTable(data).then(respond => {
        if (respond.data && respond.data.length > 0) {
          this.tableOptions = respond.data
        }
      })
    },
    collectColTable() {
      return this.$refs['collectColList']
    },
    sqlColTable() {
      return this.$refs['sqlColList']
    },
    collectColRowDataApi(option) {
      return listFileColInfo(this.temp)
    },
    sqlColRowDataApi(option) {
      const query = { id: this.temp.groupId, tableName: this.temp.tableName, querySql: '' }
      return listTableCol(query)
    },
    collectColSelectionChangeEnd(rowDatas) {
      this.colSelected = rowDatas.map(data => data.colNum)
      this.maxMatchColumn = rowDatas.length > 0 ? rowDatas.length : 1
      this.$refs['dataForm'].validateField('collectColList')
      // 只有列表存在勾选列才触发校验
      if (rowDatas.length > 0) {
        this.$refs['dataForm'].validateField('minMatchColumn')
      }
    },
    sqlColSelectionInit(rowDatas) {
      if (this.gatherDBType == 2) {
        this.colSelected = rowDatas.map(data => data.name)
        this.maxMatchColumn = rowDatas.length > 0 ? rowDatas.length : 1
        this.$refs['dataForm'].validateField('sqlColList')
        // 只有列表存在勾选列才触发校验
        if (rowDatas.length > 0) {
          this.$refs['dataForm'].validateField('minMatchColumn')
        }
      }
    },
    sqlColSelectionChangeEnd(rowDatas) {
      // 添加判断，避免selectedCols初始化时触发maxMatchColumn的变更，导致校验失准
      if (this.gatherDBType == 2) {
        if (rowDatas.length > 0 && (!this.temp.rule.minMatchColumn || this.temp.rule.minMatchColumn > rowDatas.length)) {
          this.temp.rule.minMatchColumn = rowDatas.length
        }
      }
    },
    gatherDBTypeChange(val) {
      if (val == 1) {
        if (this.temp.querySql) {
          this.maxMatchColumn = this.temp.querySql.split(',').length
          this.$refs['dataForm'].validateField('minMatchColumn')
        }
      } else {
        if (this.selectedCols && this.selectedCols.length > 0) {
          this.maxMatchColumn = this.selectedCols.length
          this.$refs['dataForm'].validateField('minMatchColumn')
        }
      }
    },
    querySqlChange(val) {
      this.maxMatchColumn = val ? val.split(',').length : 1
      this.$refs['dataForm'].validateField('minMatchColumn')
    },
    formatQuerySQL(cols) {
      return `select ${cols} from ${this.temp.tableName}`
    },
    formatCols(querySQL) {
      return Object.keys(querySQL.split(',')).join()
    },
    // 验证sql
    querySqlValidate(notify) {
      return new Promise((resolve, reject) => {
        if (this.temp.querySql) {
          listTableCol({ id: this.temp.groupId, tableName: '', querySql: this.temp.querySql }).then(respond => {
            // 点击验证时才允许提示验证成功
            if (notify) {
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('pages.edm_Msg10'),
                type: 'success',
                duration: 2000
              })
            }
            resolve(respond)
          }).catch(res => {
            reject(res)
          })
        } else {
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.edm_Msg12'),
            type: 'error',
            duration: 2000
          })
          reject(this.$t('pages.edm_Msg12'))
        }
      })
    },
    beforeUpload(file) {
      this.submitting = true
      this.loadText = this.$t('pages.fileFp_Msg10')
      const fd = new FormData()
      fd.append('file', file)// 传文件
      uploadFile(fd).then(res => {
        this.loadText = this.$t('pages.clickUpload')
        this.submitting = false
        this.temp.fileName = res.data.fileName
        this.temp.fileMd5 = res.data.fileMd5
      }).catch(res => {
        this.loadText = this.$t('pages.clickUpload')
        this.submitting = false
      })
      return false // 屏蔽了action的默认上传
    },
    clearFiles() {
      this.$refs['upload'] && this.$refs['upload'].clearFiles()
    },
    handleUploadFileChange: async function(file, fileList) {
      this.submitting = true
      const fileSuffix = file.name.substring(file.name.lastIndexOf('.'))
      let result = false
      if (fileSuffix == '.txt' || fileSuffix == '.csv') {
        result = await isUtf8File(file.raw)
      } else if (fileSuffix == '.xls' || fileSuffix == '.xlsx') {
        result = true
      }
      if (result) {
        this.loadText = this.$t('pages.fileFp_Msg10')
        const fd = new FormData()
        fd.append('file', file.raw)// 传文件
        uploadFile(fd).then(res => {
          this.loadText = this.$t('pages.clickUpload')
          this.submitting = false
          this.temp.fileName = res.data.fileName
          this.temp.fileMd5 = res.data.fileMd5
          this.temp.colSeparator = res.data.colSeparator
          this.collectColTable().execRowDataApi(this.temp)
        }).catch(res => {
          this.loadText = this.$t('pages.clickUpload')
          this.submitting = false
          this.$refs['upload'] && this.$refs['upload'].clearFiles()
        })
      } else {
        this.loadText = this.$t('pages.clickUpload')
        this.submitting = false
        this.$refs['upload'] && this.$refs['upload'].clearFiles()
        this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.fileFp_Msg23'), type: 'error', duration: 2000 })
      }
      return false // 屏蔽了action的默认上传
    },
    groupNodeSelectChange(data) {
      if (!data) {
        return
      }
      this.temp.groupId = data
      const node = this.groupTree().findNode(this.groupData, data, 'dataId')
      this.groupType = node.oriData.dbType
      if (this.groupType != 1) {
        // 当指纹库为数据库时，检测计数默认为1，不展示
        this.temp.rule.checkTimes = this.dialogStatus !== 'create' ? this.temp.rule.checkTimes : 1
        this.loadSQLTableTree(this.temp.groupId)
      }
    },
    changeTreeSelectNode(nodeKey, status) {
      const oriData = this.groupTree().findNode(this.groupData, nodeKey, 'dataId').oriData
      this.groupType = oriData.dbType
      if (status == 'update') {
        this.filterKey = oriData.dbType
      } else {
        this.filterKey = ''
      }
    },
    filterNode(value, data, node) {
      return value ? data.oriData.dbType == value : true
    },
    checkTimesTypeChange(data) {
      if (data > 1) {
        this.checkTimesType = 2
        this.checkTimesEditable = true
        this.temp.rule.checkTimes = data
      } else {
        this.checkTimesType = 1
        this.checkTimesEditable = false
        this.temp.rule.checkTimes = 1
      }
    },
    listenKey(e) {
      if (e.keyCode === 13) {
        e.preventDefault() // 阻止浏览器默认换行操作
        return false
      }
    },
    resetTemp() {
      this.uploaded = false
      this.gatherDBType = 1
      this.checkTimesType = 1
      this.checkTimesEditable = false
      this.colSelected.splice(0)
      this.selectedCols.splice(0)
      this.maxMatchColumn = 1
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    tableChange(data) {
      if (data && this.tableOptions.length > 0) {
        if (this.temp.querySql.indexOf(data) == -1) {
          this.selectedCols = []
        } else {
          this.selectedCols = this.temp.cols.split(',')
        }
        this.sqlColTable().execRowDataApi({ id: this.temp.groupId, tableName: data, querySql: '' })
        this.$refs['dataForm'].validateField('tableName')
      } else {
        this.clearSqlTable()
      }
    },
    clearSqlTable() {
      if (this.sqlColTable()) {
        this.sqlColTable().rowData = []
      }
    },
    handleCreate() {
      this.resetTemp()
      this.temp.groupId = this.$parent.query.groupId
      this.dialogStatus = 'create'
      this.edmDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.changeTreeSelectNode(this.temp.groupId, 'create')
        this.tableChange()
        this.initCollectTable()
        if (this.groupType == 2) {
          this.loadSQLTableTree(this.temp.groupId)
        }
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.uploaded = true
      // 兼容旧数据，旧数据没有minMatchColumn字段，默认匹配所有列数
      if (!row.rule.minMatchColumn) {
        row.rule['minMatchColumn'] = row.cols.split(',').length
      }
      this.temp = Object.assign(this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      // 用于保存时对比数据是否变更
      this.rowTemp = Object.assign({}, this.temp)
      if (this.temp.tableName && this.temp.querySql) {
        this.gatherDBType = 2
      }
      if (this.temp.rule.checkTimes > 1) {
        this.checkTimesType = 2
        this.checkTimesEditable = true
      }
      this.maxMatchColumn = this.temp.cols.split(',').length
      this.dialogStatus = 'update'
      this.edmDialogFormVisible = true
      this.$nextTick(() => {
        this.changeTreeSelectNode(this.temp.groupId, 'update')
        this.$refs['dataForm'].clearValidate()
        if (this.temp.querySql) {
          this.tableChange(this.temp.tableName)
        } else {
          this.initCollectTable()
        }
        if (this.groupType == 2) {
          this.loadSQLTableTree(this.temp.groupId)
        }
      })
    },
    handleView(row) {
      getRuleById(row.id).then(respond => {
        this.handleUpdate(respond.data)
        this.dialogStatus = 'view'
        this.$nextTick(() => {
          this.formable = false
        })
      })
    },
    createData() {
      if (this.checkTimesValidator(this.temp)) {
        return
      }
      this.formatKeyWord();
      this.$refs['dataForm'].validate(async valid => {
        if (valid) {
          this.submitting = true
          let sqlValidate = true
          this.temp.cols = this.colSelected.join(',')
          // 数据库，对 cols、querySql进行处理
          if (this.groupType != 1) {
            if (this.gatherDBType === 2) { // 单表采集
              this.temp.querySql = this.formatQuerySQL(this.temp.cols)
            } else { // SQL采集
              await this.querySqlValidate().then(res => {
                this.temp.cols = this.formatCols(this.temp.querySql)
                this.temp.tableName = ''
                this.selectedCols = []
              }).catch(error => {
                console.log(error)
                sqlValidate = false
              })
            }
          }
          if (!sqlValidate) {
            this.submitting = false
            return false
          }
          if (this.checkTimesType === 1) {
            this.temp.rule.checkTimes = 1
          }
          createRule(this.temp).then(respond => {
            this.submitting = false
            this.edmDialogFormVisible = false
            this.$emit('submitEnd', 'create', this.temp)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      if (this.checkTimesValidator(this.temp)) {
        return
      }
      this.formatKeyWord();
      this.submitting = true
      this.$refs['dataForm'].validate(async valid => {
        if (valid) {
          let sqlValidate = true
          this.temp.cols = this.colSelected.join(',')
          // 数据库，对 cols、querySql进行处理
          if (this.groupType != 1) {
            if (this.gatherDBType === 2) { // 单表采集
              this.temp.querySql = this.formatQuerySQL(this.temp.cols)
            } else { // SQL采集
              await this.querySqlValidate().then(res => {
                this.temp.cols = this.formatCols(this.temp.querySql)
                this.temp.tableName = ''
                // this.selectedCols = []
              }).catch(error => {
                console.log(error)
                sqlValidate = false
              })
            }
          }
          if (!sqlValidate) {
            this.submitting = false
            return false
          }
          if (this.checkTimesType === 1) {
            this.temp.rule.checkTimes = 1
          }
          const tempData = Object.assign({}, this.temp)
          updateRule(tempData).then(respond => {
            this.submitting = false
            this.edmDialogFormVisible = false
            this.$emit('submitEnd', 'update', tempData)
            this.$notify({
              title: this.$t('text.success'),
              message: this.isObjEqual(tempData, this.rowTemp) ? this.$t('text.updateSuccess') : this.$t('pages.edm_Msg17'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    isObjEqual(o1, o2) {
      const keyList = ['colSeparator', 'cols', 'firstRowAsColName', 'querySql', 'tableName']
      const Object1 = Object.getOwnPropertyNames(o1);
      for (var i = 0, max = Object1.length; i < max; i++) {
        const propName = Object1[i];
        if (keyList.indexOf(propName) > -1) {
          if (o1[propName] !== o2[propName]) {
            return false;
          }
        }
      }
      return true;
    },
    nameValidator(rule, value, callback) {
      const errorStr = value.match(nameRegExp)
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else if (nameRegExp.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_IllegalCharacter').concat('：').concat(errorStr.join(''))))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    fileValidator(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pages.chooseFile')))
      } else if (value.length > 100) {
        callback(new Error(this.$t('pages.fileFp_Msg11')))
      } else {
        callback()
      }
    },
    collectColValidator(rule, value, callback) {
      if (this.temp.fileName && this.colSelected.length == 0) {
        callback(this.$t('pages.edm_Msg13'))
      } else if (this.colSelected.length > 30) {
        callback(this.$t('pages.edm_Msg21'))
      } else {
        callback()
      }
    },
    tableNameValidator(rule, value, callback) {
      if (this.gatherDBType == 2 && !this.temp.tableName) {
        callback(this.$t('pages.edm_Msg14'))
      } else {
        callback()
      }
    },
    sqlColValidator(rule, value, callback) {
      if (this.gatherDBType == 2 && this.temp.tableName && this.colSelected.length == 0) {
        callback(this.$t('pages.edm_Msg15'))
      } else if (this.colSelected.length > 30) {
        callback(this.$t('pages.edm_Msg21'))
      } else {
        callback()
      }
    },
    minMatchColumnValidator(rule, value, callback) {
      value = this.temp.rule.minMatchColumn
      if (!value || isNaN(Number(value)) || value.toString().indexOf('.') > -1 || value.toString().indexOf('+') > -1 || Number(value) < 0) {
        callback(this.$t('pages.validateGreaterThan_0'))
      } else if (value > this.maxMatchColumn) {
        callback(this.$t('pages.edm_Msg20'))
      }
      callback()
    },
    contentValidator(rule, value, callback) {
      value = this.temp.rule.exceptWords
      if (value && value.indexOf('\\n') > -1) {
        callback(new Error(this.$t('pages.fileFp_Msg12')))
      } else if (value && value.indexOf('，') > -1) {
        callback(new Error(this.$t('pages.fileFp_Msg13')))
      } else {
        const keywords = value.split(',')
        if (keywords.length > 50) {
          callback(new Error(this.$t('pages.keyword_text14')))
        }
        const maxLength = keywords.reduce((max, keyword) => Math.max(max, keyword.length), 0)
        // 单个关键字不能超过256个字符。
        if (maxLength > 256) {
          callback(new Error(this.$t('pages.keyword_text13')))
        }
        callback()
      }
    },
    cycleValidator(rule, value, callback) {
      const reg = /^[1-9]([0-9])*$/
      if (value === '') {
        callback(this.$t('pages.edm_Msg3'))
      } else {
        if (value !== 0 && !reg.test(value)) {
          callback(this.$t('pages.edm_Msg16'))
        } else {
          callback()
        }
      }
    },
    checkTimesValidator(temp) {
      const checkTimes = temp.rule.checkTimes;
      const checkTimesType = this.checkTimesType;
      if (2 === checkTimesType && (undefined === checkTimes || '' === checkTimes)) {
        this.$message({
          message: this.$t('pages.keyword_text12'),
          type: 'error',
          duration: 2000
        })
        return true
      }
      return false;
    },
    // 格式化关键字（主要为了去除连续输入的","和"-"，例如：1,2,,,,,3，去除后：1,2,3）
    formatKeyWord() {
      const exceptWords = this.temp.rule.exceptWords;
      this.temp.rule.exceptWords = exceptWords.replaceAll(/,{2,}/g, ',').replaceAll(/-{2,}/g, '-');
    }
  }
}
</script>

<style lang='scss' scoped>
  >>>.DBListHide{
    position: absolute;
    z-index: -1;
  }
</style>
