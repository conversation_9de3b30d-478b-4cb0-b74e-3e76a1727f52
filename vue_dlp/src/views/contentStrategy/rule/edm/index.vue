<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu ref="edmGroupTree" :default-expand-all="true" :data="groupTreeData" :render-content="renderContent" @node-click="groupTreeNodeCheckChange" />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleTrain">
          {{ $t('pages.startCollection') }}
        </el-button>
        <el-button icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleCancelTrain">
          {{ $t('pages.stopCollection') }}
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <!--<el-button size="mini">
          导入导出
        </el-button>-->
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="edmRuleList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <edm-dialog ref="edmDialog" @submitEnd="submitEnd" />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="$t('text.batchDelete', {info: $t('route.edm')})"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :filter-name-desc="$t('route.edm')"
      :edit-type="1"
      @submitEnd="batchDeleteFunc"
    />

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogGroupFormVisible"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="groupForm" :rules="groupRules" :model="tempG" label-position="right" label-width="100px" style="width: 500px; margin-left:25px;">
        <FormItem :label="$t('pages.fingerprintLibraryType')" prop="groupType">
          <el-select v-model="groupType" :disabled="groupTypeDisabled">
            <el-option v-for="item in groupTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.fingerprintLibraryName')" prop="name">
          <el-input v-model="tempG.name" maxlength="30" show-word-limit/>
        </FormItem>
        <div v-show="groupType == 2">
          <FormItem :label="$t('pages.DBType')" prop="dbType">
            <el-select v-model="tempG.dbType" @change="dbTypeChange">
              <el-option v-for="item in dbTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </FormItem>
          <FormItem v-show="tempG.dbType !== 1" :label="tempG.dbType == 2 ? $t('pages.DBName') : $t('pages.serverNames')" prop="dbName">
            <el-input v-model="tempG.dbName" maxlength="30"/>
          </FormItem>
          <FormItem :label="$t('pages.hostAddr')" prop="dbHost">
            <el-input v-model="tempG.dbHost" maxlength="64"/>
          </FormItem>
          <FormItem :label="$t('pages.port')" prop="dbPort">
            <el-input v-model="tempG.dbPort" maxlength="5"/>
          </FormItem>
          <FormItem :label="$t('pages.userName1')" prop="dbUsername">
            <el-input v-model="tempG.dbUsername" maxlength="30"/>
          </FormItem>
          <FormItem :label="$t('pages.pwdInfo')" prop="dbPassword">
            <encrypt-input v-model="tempG.dbPassword" maxlength="32"/>
          </FormItem>
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-show="groupType == 2" type="primary" @click="testGroupDBEnable()">
          {{ $t('button.testConnection') }}
        </el-button>
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='createGroup'?createNode():updateNode()">
          {{ $t('button.save') }}
        </el-button>
        <el-button @click="dialogGroupFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

    <rule-check-dlg
      ref="checkDlg"
      :title="textMap[dialogStatus]"
      :result-options="resultOptions"
      :upload-file-api="uploadCkeckFile"
      :check-file-api="checkFile"
      :check-result-api="getCheckResult"
    />

    <fail-train-detail-dlg
      ref="failTrainDetailDlg"
      :rule-ids="ruleIds"
      :rule-type="ruleType"
    />
  </div>
</template>

<script>
import {
  cancelTrainEdm,
  checkFile,
  connectDB,
  countRuleByGroupId,
  createGroup,
  deleteGroup,
  deleteRule,
  getCheckResult,
  getGroupByName,
  getRulePage,
  getTreeNode,
  trainEdm,
  updateGroup,
  uploadCkeckFile
} from '@/api/contentStrategy/rule/edm'
import { getCollectorResultDict } from '@/utils/dictionary'
import EdmDialog from './edmDialog'
import RuleCheckDlg from '@/views/contentStrategy/rule/RuleCheckDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import EncryptInput from '@/components/EncryptInput'
import FailTrainDetailDlg from '@/views/contentStrategy/rule/FailTrainDetailDlg'

export default {
  name: 'EdmRule',
  components: { EncryptInput, EdmDialog, RuleCheckDlg, BatchEditPageDlg, FailTrainDetailDlg },
  data() {
    return {
      colModel: [ // 主页面列表属性
        { prop: 'name', label: 'ruleName', width: '150', fixed: true },
        { prop: 'groupId', label: 'fingerprintDatabase', width: '150', formatter: this.groupFormatter },
        { prop: 'state', label: 'acquisitionStatus', width: '100', formatter: this.stateFormatter },
        { prop: 'trainCostTime', label: 'acquisitionTime', width: '100', formatter: this.trainCostTimeFormatter },
        { prop: 'trainTime', label: 'latestAcquisitionTime', width: '150' },
        { label: 'collectSQL', width: '150', formatter: this.trainInfoFormatter },
        { prop: 'rule.exceptWords', label: 'exceptionKeyword', width: '150' },
        { prop: 'rule.checkTimes', label: 'matchCount', width: '150', formatter: this.checkTimesFormatter },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'testing', isShow: (row) => row.state === 2, click: this.handleCheckFile }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      collectable: false,
      dbNameBak: '',
      groupType: 1, // 指纹库类型 1文档库 2数据库
      groupTypeDisabled: false,
      // selectedDBCols: [], // 选中的数据库中数据表列名列表
      groupTypeOptions: [{ label: this.$t('pages.documentLibrary'), value: 1 }, { label: this.$t('pages.database'), value: 2 }],
      dbTypeOptions: [{ label: 'MYSQL', value: 2 }, { label: 'ORACLE', value: 3 }],
      stateOptions: { 0: this.$t('pages.notCollected'), 1: this.$t('pages.collecting'), 2: this.$t('pages.completeAcquisition'), 3: this.$t('pages.collectionFailed') },
      checkPositionOptions: [
        { value: 1, label: this.$t('pages.theme') },
        { value: 2, label: this.$t('pages.text') },
        { value: 3, label: this.$t('pages.attachmentName') },
        { value: 4, label: this.$t('pages.attachmentContent') }
      ],
      checkTimesTypeOptions: [
        { value: 1, label: 'pages.fileFp_Msg1', input: false },
        { value: 2, label: 'pages.fileFp_Msg2', input: true }
      ],
      tempG: {},
      defaultTempG: { // 表单字段
        id: undefined,
        name: '',
        dbType: 2,
        dbName: '',
        dbHost: '',
        dbPort: '',
        dbUsername: '',
        dbPassword: ''
      },
      groupRules: {
        name: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.groupNameValidator, trigger: 'blur' }
        ]
      },
      checkTimesType: 1,
      checkTimesEditable: false,
      dialogFormVisible: false,
      dialogGroupFormVisible: false,
      dialogStatus: '',
      loadText: this.$t('pages.clickUpload'),
      submitting: false,
      textMap: {
        createGroup: this.$t('pages.addFingerDatabase'),
        updateGroup: this.$t('pages.editFingerDatabase'),
        check: this.$t('pages.edm_Msg')
      },
      multipleSelection: [], // 选中行数组集合
      treeNodeType: '',
      groupTreeData: [{ id: 'EdmGroup0', dataId: '0', label: this.$t('pages.fingerDatabase'), parentId: '', children: [] }],
      filterKey: '',
      tableTreeSelectNode: [],
      resultOptions: {
        '0': this.$t('pages.fileFp_Msg3'),
        '1': this.$t('pages.fileFp_Msg4')
      },
      collectorResultOption: getCollectorResultDict(),
      ruleIds: null,
      ruleType: 4 // 类型值默认为4
    }
  },
  computed: {
    gridTable() {
      return this.$refs['edmRuleList']
    },
    edmGroupTree: function() {
      return this.$refs['edmGroupTree']
    }
  },
  created() {
    this.resetTempG()
    this.loadGroupTree()
    this.$socket.subscribe({ url: '/topic/updateStatus', callback: (resp, handle) => {
      if (resp.data == 'train_edm') {
        this.handleRefresh()
      }
    } })
  },
  methods: {
    getCheckResult,
    uploadCkeckFile,
    checkFile,
    listenKey(e) {
      if (e.keyCode === 13) {
        e.preventDefault() // 阻止浏览器默认换行操作
        return false
      }
    },
    // type 操作类型：新增、修改； data 行数据
    submitEnd(type, data) {
      if (type == 'create') {
        const node = this.edmGroupTree.findNode(this.groupTreeData, data.groupId, 'dataId')
        this.edmGroupTree.setCurrent(node)
        this.query.groupId = data.groupId
        this.handleRefresh()
      } else if (type == 'update') {
        this.gridTable.updateRowData(data)
      }
    },
    rowDataApi: function(option) {
      return getRulePage(option)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    // parseQuerySQL: function(data) {
    //   if (data) {
    //     data = data.substring(7, data.length)
    //     const attr = data.split(' from ')
    //     this.tableName = attr[1]
    //     this.gatherDBType == 2
    //   }
    // },
    groupTreeNodeCheckChange: function(tabName, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedNode && checkedNode.data.dataId != '0'
      if (checkedNode) {
        this.query.groupId = checkedNode.data.dataId
      } else {
        this.query.groupId = undefined
      }
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    dbTypeChange: function(data) {
      const tempStr = this.tempG.dbName
      this.tempG.dbName = this.dbNameBak
      this.dbNameBak = tempStr
    },
    loadGroupTree: function() {
      getTreeNode().then(respond => {
        this.groupTreeData[0].children = respond.data
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id,
        label: data.name,
        parentId: 'EdmGroup0',
        oriData: data
      }
    },
    testGroupDBEnable() {
      const tempData = Object.assign({}, this.tempG, { encryptProps: ['dbPassword'] })
      connectDB(tempData).then(respond => {
        if (respond.data === true) {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.DBConnectionSucceeded'),
            type: 'success',
            duration: 2000
          })
        } else {
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.DBConnectionFail'),
            type: 'error',
            duration: 2000
          })
        }
      })
    },
    createNode() {
      this.submitting = true
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          this.tempG.dbType = this.groupType == 1 ? 1 : this.tempG.dbType
          if (this.tempG.dbType != 1) {
            this.testGroupDBEnable()
          }
          const tempData = Object.assign({}, this.tempG, { encryptProps: ['dbPassword'] })
          createGroup(tempData).then(respond => {
            this.submitting = false
            this.dialogGroupFormVisible = false
            tempData.id = respond.data.id
            this.edmGroupTree.addNode(this.dataToTreeNode(respond.data))
            this.$store.dispatch('commonData/changeNotice', 'edmLibrary')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateNode() {
      this.submitting = true
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          this.tempG.dbType = this.groupType == 1 ? 1 : this.tempG.dbType
          if (this.tempG.dbType != 1) {
            this.testGroupDBEnable()
          }
          const tempData = Object.assign({}, this.tempG, { encryptProps: ['dbPassword'] })
          updateGroup(tempData).then(respond => {
            this.submitting = false
            this.dialogGroupFormVisible = false
            this.edmGroupTree.updateNode(this.dataToTreeNode(respond.data))
            this.$store.dispatch('commonData/changeNotice', 'edmLibrary')
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    removeNode(data) {
      this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = [data.dataId]
        if (data.children && data.children.length > 0) {
          this.pushChildDataIds(toDeleteIds, data)
        }
        deleteGroup({ ids: toDeleteIds.join(',') }).then(respond => {
          this.edmGroupTree.removeNode([data.id])
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTempG() {
      this.groupType = 1
      this.dbNameBak = ''
      this.tempG = Object.assign({}, this.defaultTempG)
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleGroupCreate(data) {
      this.resetTempG()
      this.dialogStatus = 'createGroup'
      this.dialogGroupFormVisible = true
      this.groupTypeDisabled = false
      this.$nextTick(() => {
        this.$refs['groupForm'].clearValidate()
      })
    },
    handleGroupUpdate: function(data) {
      this.resetTempG()
      this.tempG = JSON.parse(JSON.stringify(data.oriData))
      this.groupType = this.tempG.dbType == 1 ? 1 : 2
      this.dialogStatus = 'updateGroup'
      this.dialogGroupFormVisible = true
      this.groupTypeDisabled = true
      this.$nextTick(() => {
        this.$refs['groupForm'].clearValidate()
      })
    },
    handleGroupDelete: function(data) {
      countRuleByGroupId(data.dataId).then(respond => {
        if (respond.data === 0) {
          this.$confirmBox(this.$t('pages.mailLibrary_text5'), this.$t('text.prompt')).then(() => {
            deleteGroup(data.dataId).then(respond => {
              this.edmGroupTree.removeNode(data.id)
              this.$notify({
                title: this.$t('text.success'),
                message: this.$t('text.deleteSuccess'),
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {})
        } else {
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.edm_Msg1'),
            type: 'warning',
            duration: 2000
          })
        }
      })
    },
    handleCreate() {
      this.$refs.edmDialog.groupType = this.groupType
      this.$refs.edmDialog.handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs.edmDialog.groupType = row.dbType
      this.$refs.edmDialog.handleUpdate(row)
    },
    handleImport() {},
    handleExport() {},
    handleDelete() {
      const selectedDatas = this.gridTable.getSelectedDatas()
      const total = this.gridTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedDatas, total, this.query)
    },
    handleTrain() {
      const toTrainIds = this.gridTable.getSelectedIds()
      trainEdm({ ids: toTrainIds.join(',') }).then(respond => {
        this.gridTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.fileFp_Msg5'),
          type: 'success',
          duration: 2000
        })
        if (respond.data && respond.data.length > 0) {
          this.ruleIds = respond.data
          this.$refs['failTrainDetailDlg'].show()
        }
      })
    },
    handleCancelTrain() {
      this.$confirmBox(this.$t('pages.fileFp_Msg6'), this.$t('text.prompt')).then(() => {
        const toCancelIds = this.gridTable.getSelectedIds()
        cancelTrainEdm({ ids: toCancelIds.join(',') }).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.fileFp_Msg7'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleRefresh() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleCheckFile(row) {
      this.dialogStatus = 'check'
      this.$refs['checkDlg'].show(row)
    },
    groupNameValidator(rule, value, callback) {
      getGroupByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.tempG.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      let icon = ''
      if (data.oriData) {
        if (data.oriData.dbType == 1) {
          icon = <svg-icon icon-class='file' />
        } else {
          icon = <svg-icon icon-class='database' />
        }
      }
      // <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.removeNode(data)} />
      return (
        <div class='custom-tree-node'>
          <span>{icon} {data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space' on-click={r => this.handleGroupCreate(data)} />
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space' on-click={r => this.handleGroupUpdate(data)} />
            <svg-icon v-show={iconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space' on-click={r => this.handleGroupDelete(data)} />
          </span>
        </div>
      )
    },
    pushChildDataIds: function(toDeleteIds, data) {
      data.children.forEach(child => {
        toDeleteIds.push(child.dataId)
        if (child.children && child.children.length > 0) {
          this.pushChildDataIds(toDeleteIds, child)
        }
      })
    },
    checkPositionFormatter(row, data) {
      const positionNames = []
      for (let i = 0, size = this.checkPositionOptions.length; i < size; i++) {
        const id = this.checkPositionOptions[i].value
        if (data.indexOf(id) >= 0) {
          positionNames.push(this.checkPositionOptions[i].label)
        }
      }
      return positionNames.join(',')
    },
    groupFormatter(row, data) {
      const node = this.edmGroupTree.findNode(this.groupTreeData, data, 'dataId')
      return node ? node.label : data
    },
    stateFormatter(row, data) {
      if (data === 3 && row.result) {
        return this.collectorResultOption[row.result] ? this.collectorResultOption[row.result] : this.collectorResultOption[40000]
      }
      return this.stateOptions[data]
    },
    trainCostTimeFormatter(row, data) {
      return !!data || data === 0 ? data + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('text.second') : ''
    },
    trainInfoFormatter(row, data) {
      return row.querySql || row.fileName
    },
    checkTimesFormatter(row, data) {
      let option
      if (!row.rule) {
        row['rule'] = { checkTimes: 1 }
      }
      if (row.rule.checkTimes === 1) {
        option = this.checkTimesTypeOptions[0]
      } else {
        option = this.checkTimesTypeOptions[1]
      }
      return option.input ? this.$t(option.label, { input: row.rule.checkTimes }) : this.$t(option.label)
    },
    batchDeleteFunc(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.gridTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch((e) => { callback(e) })
      }).catch((e) => { callback(e) })
    }
  }
}
</script>
