<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="title"
    :visible.sync="visible"
    width="800px"
    @close="handleCancelCheck"
  >
    <Form ref="checkDataForm" :model="temp" label-position="right" label-width="95px" style="width: 750px;">
      <FormItem :label="$t('pages.ruleName')" prop="name">
        <el-input v-model="temp.name" disabled></el-input>
      </FormItem>
      <el-row>
        <el-col :span="21">
          <FormItem :label="$t('pages.textFile')" prop="checkFileName">
            <el-input v-model="checkFileName" disabled :placeholder="$t('pages.fileFp_Msg25')"></el-input>
          </FormItem>
        </el-col>
        <el-col :span="3">
          <el-upload
            ref="uploadCheckFile"
            name="checkFile"
            action=""
            accept=".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt"
            :limit="-1"
            :auto-upload="false"
            :show-file-list="false"
            :on-exceed="handleSelectFile"
          >
            <el-button size="small" type="primary" style="margin-left: 16px;">{{ $t('pages.selectFile') }}</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <FormItem :label="$t('pages.textResult')" prop="result">
        <el-input v-model="checkResult" type="textarea" :placeholder="$t('pages.fileFp_Msg14')" disabled></el-input>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :disabled="checkFileName===''" :loading="submitting" @click="handleCheckFile">
        {{ $t('pages.startText') }}
      </el-button>
      <el-button :loading="submitting" @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCollectorResultDict } from '@/utils/dictionary'

export default {
  name: 'RuleCheckDlg',
  props: {
    title: {
      type: String,
      default: function() {
        return this.$t('pages.ruleTest')
      }
    },
    resultOptions: {
      type: Object,
      default() {
        return {}
      }
    },
    uploadFileApi: {
      type: Function,
      default(formData) {
        return Promise.reject(this.$t('pages.fileFp_Msg15'))
      }
    },
    checkFileApi: {
      type: Function,
      default(data) {
        return Promise.reject(this.$t('pages.fileFp_Msg16'))
      }
    },
    checkResultApi: {
      type: Function,
      default(taskId) {
        return Promise.reject(this.$t('pages.fileFp_Msg17'))
      }
    }
  },
  data() {
    return {
      visible: false,
      temp: {},
      defaultTemp: {
        id: undefined,
        name: ''
      },
      checkFile: undefined,
      checkResult: '',
      tempIndex: 1,
      timer: undefined,
      submitting: false,
      taskId: undefined,
      collectorResultOption: getCollectorResultDict()
    }
  },
  computed: {
    checkFileName() {
      if (this.checkFile) {
        return this.checkFile.name
      }
      return ''
    }
  },
  methods: {
    show(row) {
      this.clearFile()
      this.temp = Object.assign({}, row)
      this.visible = true
      this.taskId = undefined;
    },
    clearFile() {
      this.resetTemp()
      this.checkFile = undefined
      this.checkResult = ''
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = undefined
      }
      this.$refs.uploadCheckFile && this.$refs.uploadCheckFile.clearFiles()
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleSelectFile(files, fileList) {
      const rawFile = files[0]
      rawFile.uid = Date.now() + this.tempIndex++;
      fileList.splice(0, fileList.length, {
        status: 'ready',
        name: rawFile.name,
        size: rawFile.size,
        percentage: 0,
        uid: rawFile.uid,
        raw: rawFile
      })
      this.checkFile = rawFile
      this.checkResult = this.$t('pages.fileFp_Msg18')
    },
    handleCheckFile() {
      this.submitting = true
      this.checkResult = this.$t('pages.fileFp_Msg19')
      const formData = new FormData()
      formData.append('checkFile', this.checkFile)
      this.uploadFileApi.call(null, formData).then(response => {
        this.checkResult = this.$t('pages.fileFp_Msg20')
        this.checkFileApi.call(null, { id: this.temp.id, checkFileName: response.data }).then(respond => {
          this.taskId = respond.data;
          this.getCheckResult(respond.data);
        }).catch(reason => {
          this.checkResult = this.$t('pages.fileFp_Msg21') + reason
          this.submitting = false
        })
      }).catch(reason => {
        this.checkResult = this.$t('pages.fileFp_Msg22') + reason
        this.submitting = false
      })
    },
    getCheckResult(taskId) {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.checkResultApi.call(null, taskId).then(respond => {
          this.timer = undefined;
          if (respond.data) {
            this.checkResult = this.collectorResultOption[respond.data]
            if (!this.checkResult) {
              this.checkResult = this.collectorResultOption[40000]
            }
            this.submitting = false
            this.taskId = undefined;
          } else {
            this.getCheckResult(taskId)
          }
        }).catch(reason => {
          this.checkResult = this.$t('pages.fileFp_Msg22') + reason
          this.timer = undefined;
          this.submitting = false
        })
      }, 1000)
    },
    handleCancelCheck() {
      const taskId = this.taskId;
      this.clearFile()
      this.submitting = false
      this.visible = false
      // 解决在上传文件后执行检测时关闭窗口导致任务一直在进行，未去获取检测结果，故关闭时若有任务id则执行获取结果
      if (taskId) {
        this.getCheckResult(taskId);
      }
    }
  }
}
</script>
