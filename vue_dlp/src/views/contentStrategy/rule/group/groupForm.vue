<template>
  <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 530px;">
    <FormItem :label="$t('table.ruleGroupName')" prop="name">
      <el-input v-model="temp.name" v-trim :disabled="visitOnly" maxlength="60"></el-input>
    </FormItem>
    <FormItem :label="$t('pages.className')" prop="groupId">
      <el-select v-model="temp.groupId" :disabled="visitOnly" :placeholder="$t('text.select')">
        <el-option v-for="item in treeSelectNode" :key="item.id" :label="item.label" :value="Number(item.dataId)"/>
      </el-select>
    </FormItem>
    <FormItem :label="$t('table.remark')" prop="remark">
      <el-input v-model="temp.remark" maxlength="100" show-word-limit :disabled="visitOnly"></el-input>
    </FormItem>
    <FormItem v-if="dripChangeAble" v-permission="'117'" :label="$t('pages.doDrip')" prop="doDrip">
      <el-switch v-model="temp.doDrip" :active-value="1" :inactive-value="0" :disabled="visitOnly || dripDisabled" @change="dripChange" />
    </FormItem>
    <el-row>
      <el-col :span="7">
        <FormItem v-if="showDripConfig" prop="dripMode" :label="$t('pages.countMode')">
          <el-switch v-model="isCountMode" :disabled="visitOnly"></el-switch>
        </FormItem>
      </el-col>
      <el-col :span="7">
        <FormItem v-if="showDripConfig" :label="$t('pages.weightMode')">
          <el-switch v-model="isWeightMode" :disabled="visitOnly"></el-switch>
        </FormItem>
      </el-col>
      <el-col :span="10">
        <FormItem v-if="showDripConfig && isWeightMode" :label="$t('pages.weightThreshold')" label-width="100px" prop="weightThreshold">
          <el-input v-model="temp.weightThreshold" maxlength="3" :disabled="visitOnly" @input="temp.weightThreshold=temp.weightThreshold.replace(/^(0+)|[^\d]+/g,'')"></el-input>
        </FormItem>
      </el-col>
    </el-row>
    <FormItem :label="$t('table.detectionRules')" prop="checkRule">
      <div :class="{'select-box': true, visitOnly: visitOnly}">
        <el-popover
          v-if="!visitOnly"
          v-model="checkVisible"
          placement="right"
          width="350"
          class="select-button"
          trigger="click"
          @after-leave="checkRuleTreeHide"
        >
          <tree-menu
            ref="checkRuleTree"
            style="height: 320px"
            :data="filterTreeNode"
            :accordion="false"
            :default-expand-all="true"
            expand-on-click-node
            :render-content="renderContent"
            @node-click="checkRuleTreeClick"
          />
          <div style="text-align: right; margin-top: 10px">
            <el-button size="mini" type="primary" @click="checkVisible = false">{{ $t('button.close') }}</el-button>
          </div>
          <el-button slot="reference" class="choose-btn">{{ $t('pages.chooseRule') }}</el-button>
        </el-popover>
        <el-tag
          v-for="(item, index) in checkRules"
          :key="index"
          class="select-tag"
          type="info"
          :closable="!visitOnly"
          :title="`${item.name}（${ruleOptions[item.type].label}）`"
          :style="ruleOptions[item.type].style"
          @click="handleRuleView(item.id)"
          @close="closeTagFunc(checkRules, index)"
        >
          <el-link v-if="visitOnly && index > 0" style="padding: 0 5px;" @click.stop>{{ item.relation }}</el-link>
          <el-link v-if="!visitOnly && index > 0" type="primary" style="padding: 0 5px;" @click.stop="clickTagLinkFunc(item)">{{ item.relation }}</el-link>
          <span>{{ item.name.length > 30 ? item.name.substring(0,29) + '...' : item.name }}</span>
          <el-popover
            v-if="showDripConfig"
            placement="right"
            :title="visitOnly?$t('pages.group_text2'):$t('pages.group_text3', { regular: item.name })"
            :trigger="visitOnly?'hover':'click'"
            @show="dripShow(item.id)"
          >
            <Form ref="dripForm" :rules="dripRules" :model="dripProps[item.id]" label-position="right" label-width="90px" :extra-width="{en: 50}" style="width: 500px;">
              <FormItem :label="$t('pages.lossType')">
                <el-select v-model="dripProps[item.id].opTypes" filterable multiple size="small" :placeholder="$t('pages.group_text5')" style="width: 100%;" :disabled="visitOnly" @change="lossTypeChange">
                  <el-option v-for="option in lossTypeOptions" :key="option.value" :value="option.value" :label="option.label"></el-option>
                </el-select>
              </FormItem>
              <FormItem v-if="visitOnly" :label="$t('pages.timeSpan')">
                <el-input v-model="dripProps[item.id].timeSpan" disabled></el-input>
              </FormItem>
              <FormItem v-else :label="$t('pages.timeSpan')" class="is-required" prop="timeSpan">
                <i18n path="pages.timeSpan1" class="time-span">
                  <el-input slot="week" v-model.number="dripProps[item.id].w" maxlength="1"></el-input>
                  <el-input slot="day" v-model.number="dripProps[item.id].d" maxlength="1"></el-input>
                  <el-input slot="hour" v-model.number="dripProps[item.id].h" maxlength="2"></el-input>
                  <el-input slot="minute" v-model.number="dripProps[item.id].m" maxlength="2"></el-input>
                  <el-input slot="second" v-model.number="dripProps[item.id].s" maxlength="2"></el-input>
                </i18n>
              </FormItem>
              <FormItem :label="$t('pages.uniqueMatch')">
                <el-switch v-model="dripProps[item.id].uniqueMatch" :disabled="visitOnly"/>
              </FormItem>
              <FormItem v-if="isCountMode || !isWeightMode" :label="$t('pages.countThreshold')" class="is-required" prop="countThreshold">
                <el-input
                  v-model="dripProps[item.id].countThreshold"
                  :disabled="visitOnly || !isCountMode"
                  :maxlength="3"
                  :placeholder="$t('pages.group_text6')"
                  @input="val => {
                    val = val.replace(/^(0+)|[^\d]+/g,'')
                    dripProps[item.id].countThreshold= val == '' ? '' : Number(val)
                  }"
                />
              </FormItem>
              <FormItem v-if="isWeightMode || !isCountMode" :label="$t('pages.unitWeight')" prop="weight">
                <el-input
                  v-model="dripProps[item.id].weight"
                  :disabled="visitOnly || !isWeightMode"
                  :maxlength="2"
                  :placeholder="$t('pages.group_text7')"
                  @input="val => {
                    val = val.replace(/^(0+)|[^\d]+/g,'')
                    dripProps[item.id].weight = val == '' ? '' : Number(val)
                  }"
                />
              </FormItem>
            </Form>
            <span slot="reference" @click.stop><svg-icon icon-class="drip" class="drip" :title="$t('pages.dripRule')" /></span>
          </el-popover>
        </el-tag>
      </div>
      <label v-if="!visitOnly">{{ $t('pages.group_text8') }}</label>
      <el-tooltip v-if="!visitOnly" class="item" effect="dark" placement="bottom-start">
        <div slot="content">{{ $t('pages.group_text9') }}</div>
        <i class="el-icon-info" />
      </el-tooltip>
      <br v-if="!visitOnly">
      <label v-if="!visitOnly && showDripConfig">
        <i18n path="pages.group_text10">
          <svg-icon slot="icon" icon-class="drip" />
        </i18n>
      </label>
    </FormItem>
    <FormItem v-if="!showDripConfig" :label="$t('pages.exceptRule')">
      <div :class="{'select-box': true, visitOnly: visitOnly}">
        <el-popover
          v-if="!visitOnly"
          v-model="exceptVisible"
          placement="right"
          width="350"
          class="select-button"
          trigger="click"
          @after-leave="exceptRuleTreeHide"
        >
          <tree-menu
            ref="exceptRuleTree"
            style="height: 320px"
            :data="filterTreeNode"
            :accordion="false"
            :default-expand-all="true"
            expand-on-click-node
            :render-content="renderContent"
            @node-click="exceptRuleTreeClick"
          />
          <div style="text-align: right; margin-top: 10px">
            <el-button size="mini" type="primary" @click="exceptVisible = false">{{ $t('button.close') }}</el-button>
          </div>
          <el-button slot="reference" class="choose-btn">{{ $t('pages.chooseRule') }}</el-button>
        </el-popover>
        <el-tag
          v-for="(item, index) in exceptRules"
          :key="index"
          class="select-tag"
          type="info"
          :closable="!visitOnly"
          :title="`${item.name}(${ruleOptions[item.type].label})`"
          :style="ruleOptions[item.type].style"
          @click="handleRuleView(item.id)"
          @close="closeTagFunc(exceptRules, index)"
        >
          <el-link v-if="index > 0" style="padding: 0 5px;">{{ item.relation }}</el-link>
          <span>{{ item.name.length > 30 ? item.name.substring(0,29) + '...' : item.name }}</span>
        </el-tag>
      </div>
    </FormItem>
  </Form>
</template>

<script>
import {
  getRuleByName,
  createRule,
  updateRule,
  listRegularRuleTreeNode,
  listkeywordRuleTreeNode,
  listFileAttributeRuleTreeNode,
  listFileFingerprintRuleTreeNode,
  listEdmRuleTreeNode,
  listVmlRuleTreeNode,
  listSourceCodeRuleTreeNode,
  getGroupTreeNode
} from '@/api/contentStrategy/rule/group'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'
import { isSameTimestamp, initTimestamp } from '@/utils'
import { nameRegExp } from '../ruleRegExp'

export default {
  name: 'GroupForm',
  props: {
    dripAble: { type: Boolean, default: true },         // 是否支持零星检测
    dripChangeAble: { type: Boolean, default: true },   // 是否支持修改零星检测
    formDatas: { type: Object, default: null },
    visitOnly: { type: Boolean, default: false },
    stgCode: { type: Number, default: 23 },              // 策略编码，stg_base_config的number字段值
    filterHighConfig: { type: Boolean, default: false } // 是否过滤高级算法配置
  },
  data() {
    return {
      // G1: 文件属性规则, G2: 关键字规则, G3: 数据标识符规则,
      // G6: 文件指纹规则, G4: 数据库指纹规则, G5: 文档分类规则, G12: 源代码识别规则
      // menu用于权限控制是否可配置，有menu属性的节点需要判断是否有权限
      treeNode: [
        { label: this.$t('route.regular'), id: 'G3', dataId: '0', children: [] },
        { label: this.$t('route.keyword'), id: 'G2', dataId: '-1', children: [] },
        { label: this.$t('route.file'), id: 'G1', dataId: '-2', children: [] },
        { menu: '118', label: this.$t('route.fileFp'), id: 'G6', dataId: '-3', children: [] },
        { menu: '118', label: this.$t('route.edm'), id: 'G4', dataId: '-4', children: [] },
        { menu: '118', label: this.$t('route.vml'), id: 'G5', dataId: '-5', children: [] },
        { label: this.$t('route.sourceCode'), id: 'G12', dataId: '-12', children: [] }
      ],
      // func: 请求节点的方法， name：记录时间戳的组件名，dripAble：是否支持零星检测
      ruleMap: {
        'G1': { id: 'G1', func: listFileAttributeRuleTreeNode, name: 'FileRule', dripAble: false },
        'G2': { id: 'G2', func: listkeywordRuleTreeNode, name: 'KeywordRule', dripAble: true },
        'G3': { id: 'G3', func: listRegularRuleTreeNode, name: 'RegularRule', dripAble: true },
        'G4': { id: 'G4', func: listEdmRuleTreeNode, name: 'EdmRule', dripAble: false },
        'G5': { id: 'G5', func: listVmlRuleTreeNode, name: 'VmlRule', dripAble: false },
        'G6': { id: 'G6', func: listFileFingerprintRuleTreeNode, name: 'FileFpRule', dripAble: false },
        'G12': { id: 'G12', func: listSourceCodeRuleTreeNode, name: 'SourceCode', dripAble: false }
      },
      checkRules: [],             // 已选检测规则
      checkArr: [],               // 已选检测规则的表达式拆分数组，用于判断检测规则是否可以添加
      exceptRules: [],            // 已选例外规则
      exceptArr: [],              // 已选例外规则的表达式拆分数组，用于判断例外规则是否可以添加
      dripProps: [],              // 零星规则，key为规则id，value为零星规则对象
      ruleOptions: {
        'G1': { label: this.$t('route.file'), style: 'border-left: 2px solid green;' },
        'G2': { label: this.$t('route.keyword'), style: 'border-left: 2px solid red;' },
        'G3': { label: this.$t('route.regular'), style: 'border-left: 2px solid blue;' },
        'G6': { label: this.$t('route.fileFp'), style: 'border-left: 2px solid yellowgreen;' },
        'G4': { label: this.$t('route.edm'), style: 'border-left: 2px solid black;' },
        'G5': { label: this.$t('route.vml'), style: 'border-left: 2px solid darkgray;' },
        'G12': { label: this.$t('route.sourceCode'), style: 'border-left: 2px solid #e0ca2e;' }
      },
      lossTypeOptions: [],
      lossTypeLoaded: false,
      relationMap: {
        '且': '&', '或': '|', '非': '!',
        '&': this.$t('pages.and1'), '|': this.$t('pages.or'), '!': this.$t('text.no1'),
        'and': '&', 'or': '|', 'No': '!'
      },
      tagOptionMap: {},
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        groupId: undefined,
        remark: '',
        operators: '',
        ruleIds: '',
        checkExpr: '',
        exceptExpr: '',
        doDrip: 0,
        weightThreshold: null
      },
      tempCheckExpr: '',    // 检测规则表达式的临时数据
      tempExceptExpr: '',   // 例外规则表达式的临时数据
      defaultDripTemp: {
        show: false,
        w: undefined,
        d: undefined,
        h: undefined,
        m: undefined,
        s: undefined,
        uniqueMatch: true,
        opTypes: [],
        countThreshold: null,
        weight: null
      },
      dripConfigAble: false, // 控制是否显示零星检测配置项的属性，确认开启零星检测后，该值才会为true
      isWeightMode: false,
      isCountMode: false,
      operatorsCheckedItems: [],
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        checkRule: [{ required: true, trigger: 'none', validator: this.operatorsValidator }],
        weightThreshold: [
          { required: true, trigger: 'blur', message: this.$t('pages.group_text12') }
        ],
        dripMode: [{ required: true, trigger: 'blur', validator: this.dripModeValidator }]
      },
      dripRules: {
        timeSpan: [{ trigger: 'blur', validator: this.timeSpanValidator }],
        countThreshold: [
          { required: true, trigger: 'blur', message: this.$t('pages.group_text14') }
        ],
        weight: [{ required: true, trigger: 'blur', message: this.$t('pages.group_text13') }]
      },
      visible: false,
      checkVisible: false,
      exceptVisible: false,
      currentId: null,
      formable: true,
      dripDisabled: false,
      lossTypeAll: -1, // 所有泄露方式
      treeSelectNode: []
    }
  },
  computed: {
    // 是否显示零星检测配置项
    showDripConfig() {
      return this.dripConfigAble && this.temp.doDrip
    },
    // 规则树数据
    filterTreeNode() {
      const dripAbleSet = new Set()
      const isDrip = this.temp.doDrip
      if (isDrip) {
        // 获取支持零星检测的规则
        Object.values(this.ruleMap).forEach(rule => {
          rule.dripAble && dripAbleSet.add(rule.id)
        })
      }
      // 过滤数据
      let filterTreeNodes = this.treeNode.filter(node => !isDrip || dripAbleSet.has(node.id))
      if (this.filterHighConfig) {
        filterTreeNodes = filterTreeNodes.filter(node => node.menu != '118')
      }
      return filterTreeNodes
    }
  },
  watch: {
    'temp.doDrip'(newValue, oldValue) {
      this.toDripProps()
    },
    treeNode: {
      deep: true,
      handler(val) {
        const checkExpr = this.tempCheckExpr || this.temp.checkExpr
        const exceptExpr = this.tempExceptExpr || this.temp.exceptExpr
        this.checkRules = this.formatExprToTagOp(checkExpr)
        this.exceptRules = this.formatExprToTagOp(exceptExpr)
      }
    },
    checkRules: {
      deep: true,
      handler(val) {
        this.toDripProps()
        this.checkArr = this.formatExprArr(this.checkRules)
      }
    },
    exceptRules: {
      deep: true,
      handler(val) {
        this.toDripProps()
        this.exceptArr = this.formatExprArr(this.exceptRules)
      }
    },
    formDatas(val) {
      this.handleUpdate(val)
    }
  },
  activated() {
    this.loadRuleGroupClassTree()
    this.loadRuleTree()
  },
  deactivated() {
    // 离开当前页面时，设置表达式的临时数据
    this.initTempExpr()
  },
  created() {
    // TODO 优化：泄露方式数据，多处使用，加载耗时
    this.getSensitiveLossType()
    this.resetTemp()
    this.hiddenRuleWithoutPermission()
    this.loadRuleGroupClassTree()
    this.loadRuleTree()
    if (this.formDatas) {
      this.handleUpdate(this.formDatas)
    }
  },
  methods: {
    loadRuleGroupClassTree: function() {
      this.treeSelectNode.splice(0)
      return getGroupTreeNode().then(respond => {
        this.treeSelectNode = respond.data
      })
    },
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        const data = res.data.map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
        this.lossTypeOptions = [{ label: this.$t('pages.lossTypeOptions'), value: this.lossTypeAll }, ...data]
        this.lossTypeLoaded = true
      })
    },
    checkRuleTreeHide() {
      this.$refs.checkRuleTree && this.$refs.checkRuleTree.clearFilter()
    },
    exceptRuleTreeHide() {
      this.$refs.exceptRuleTree && this.$refs.exceptRuleTree.clearFilter()
    },
    // 赋值临时数据
    initTempExpr() {
      this.tempCheckExpr = this.checkArr.join('|')
      this.tempExceptExpr = this.exceptArr.join('|')
    },
    // 清除临时数据
    clearTempExpt() {
      this.tempCheckExpr = ''
      this.tempExceptExpr = ''
    },
    clearRules() {
      this.checkRules.splice(0)
      this.exceptRules.splice(0)
    },
    lossTypeChange(selections) {
      const selectAllIndex = selections.indexOf(this.lossTypeAll)
      if (selections.length > 1) {
        if (selectAllIndex === selections.length - 1 || selections.length === this.lossTypeOptions.length - 1) {
          selections.splice(0, selections.length, this.lossTypeAll)
        } else if (selectAllIndex > -1) {
          selections.splice(selectAllIndex, 1)
        }
      }
    },
    handleCreate(groupId) {
      this.resetTemp()
      this.temp.groupId = groupId
      this.dripDisabled = false
      this.isCountMode = false
      this.$nextTick(() => {
        this.dripChange()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row, dripDisabled = true) {
      this.resetTemp()
      this.dripDisabled = dripDisabled
      this.temp = Object.assign({}, this.temp, row) // copy obj
      this.checkRules = this.formatExprToTagOp(this.temp.checkExpr)
      this.exceptRules = this.formatExprToTagOp(this.temp.exceptExpr)
      const timer = setInterval(() => {
        if (!this.lossTypeLoaded) return
        clearInterval(timer)
        if (row.doDrip) {
          this.isCountMode = row.countMode
          this.isWeightMode = !!row.weightThreshold
          row.dripProps.forEach(drip => {
            drip = JSON.parse(JSON.stringify(drip))
            drip.show = false
            if (drip.opTypes) {
              drip.opTypes = this.toNumberArray(drip.opTypes)
              if (drip.opTypes.length === this.lossTypeOptions.length - 1) {
                let selectAll = true
                this.lossTypeOptions.forEach(opt => {
                  if (opt.value != this.lossTypeAll && drip.opTypes.indexOf(opt.value) == -1) {
                    selectAll = false
                  }
                })
                if (selectAll) {
                  drip.opTypes.splice(0, drip.opTypes.length, this.lossTypeAll)
                }
              }
            }
            this.toTimeSpan(drip)
            this.$set(this.dripProps, drip.id, drip)
          })
        }
        delete this.temp.json
      }, 100)
      this.$nextTick(() => {
        this.dripChange()
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$emit('submitting', true)
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatFormData(this.temp)
          createRule(this.temp).then(respond => {
            this.$emit('submitting', false)
            this.$emit('closeDlg')
            this.$emit('updateGridTable', 'create', respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.$emit('submitting', false)
          })
        } else {
          this.$emit('submitting', false)
        }
      })
    },
    updateData() {
      this.$emit('submitting', true)
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          this.formatFormData(tempData)
          updateRule(tempData).then(respond => {
            this.$emit('closeDlg')
            this.$emit('submitting', false)
            this.$emit('updateGridTable', 'update', respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.$emit('submitting', false)
          })
        } else {
          this.$emit('submitting', false)
        }
      })
    },
    dripChange(val) {
      this.dripConfigAble = false
      if (this.temp.doDrip) {
        // 已选择的规则中不能配置零星检测的规则
        const limitedRule = this.checkRules.filter(rule => rule.type != 'G2' && rule.type != 'G3')
        if (limitedRule.length > 0) {
          const rules = limitedRule.map(rule => `${rule.name}（${this.ruleOptions[rule.type].label}）`)
          this.$confirmBox(this.$t('pages.group_text11', { rules }), this.$t('text.prompt'), { showClose: false }).then(() => {
            this.dripConfigAble = true
            // 删除不能配置零星检测的规则
            this.checkRules = this.checkRules.filter(rule => rule.type == 'G2' || rule.type == 'G3')
          }).catch(() => {
            this.temp.doDrip = 0
          })
        } else {
          this.dripConfigAble = true
        }
      }
      this.$refs['dataForm'].clearValidate('checkRule')
    },
    // 根据菜单权限过滤规则树和例外规则树节点，有menu属性的节点需要判断是否有权限
    hiddenRuleWithoutPermission() {
      this.treeNode = this.treeNode.filter(node => !node.menu || this.hasPermission(node.menu))
    },
    // 更新规则树的数据
    loadRuleTree() {
      this.hiddenByStgCode()
      const treeNode = JSON.parse(JSON.stringify(this.treeNode))
      // 获取各个规则的数据
      const func = treeNode.map(data => {
        const rule = this.ruleMap[data.id]
        // 不支持配置零星检测的规则，只有在非零星检测模式下才需要更新。根据是否支持零星检测和时间戳是否相同，判断是否需要更新数据
        if ((rule.dripAble || !this.dripAble) && !isSameTimestamp(this, rule.name)) {
          return rule.func()
        } else {
          return null
        }
      })
      // 使用 Promise.all 在所有数据都请求回来后，修改 this.treeNode 的值
      Promise.all(func).then((posts) => {
        treeNode.forEach((data, index) => {
          const post = posts[index]
          if (post) {
            data.children = post.data
          }
        })
        // 修改 this.treeNode 的值
        this.treeNode = treeNode
      }).catch((reason) => {
        console.error('promise catch', reason);
      });
      initTimestamp(this)
    },
    hiddenByStgCode() {
      // 224 敏感文件自检内容检测策略; 125 全盘扫描内容检测策略
      if (this.stgCode == 224 || this.stgCode == 125 || this.stgCode == 269) {
        // 过滤高级算法规则
        this.treeNode = this.treeNode.filter(node => ['G1', 'G2', 'G3', 'G12'].includes(node.id))
      }
    },
    renderContent(h, { node, data, store }) {
      const iconShow = !data.parentId && data.id !== 'G4' && data.id !== 'G5' && data.id !== 'G6'
      const label = !data.parentId ? <b>{data.label}</b> : <span>{data.label}</span>
      return (
        <div>
          {label}
          <span class='el-ic'>
            <svg-icon v-show={iconShow} icon-class='add' title={this.$t('button.insert')} class='addRule' on-click={r => this.handleRuleCreate(data)} />
          </span>
        </div>
      )
    },
    handleRuleCreate(data) {
      const exprType = this.checkVisible ? 'checkExpr' : 'exceptExpr'
      this.checkVisible = false
      this.exceptVisible = false
      const tempData = Object.assign({}, this.temp)
      tempData.countMode = this.isCountMode
      this.formatFormData(tempData)
      this.$emit('ruleClose', true) // 是否由于打开规则窗口，导致的规则集窗口关闭
      this.$emit('closeDlg')
      // 弹窗类型
      const dlg = { G1: 'fileAttributeDialog', G2: 'keywordDialog', G3: 'regularDialog', G12: 'sourceCodeDialog' }[data.id]
      dlg && this.$emit('createRule', dlg, tempData, exprType)
    },
    handleRuleView(id) {
      this.$emit('ruleView', id, this.temp.doDrip)
    },
    checkRuleTreeClick: function(data, node, vm) {
      if (data.id.indexOf('G') < 0) {
        if (this.checkArr.indexOf(data.dataId) == -1) {
          this.clearTempExpt()
          const rule = this.treeNodeDataToTagOption(data)
          this.checkRules.push(rule)
          this.$emit('updateRulesMap', rule.id)
          this.$refs['dataForm'].clearValidate('checkRule')
        } else {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.group_text15'),
            type: 'error',
            duration: 2000
          })
        }
      }
    },
    exceptRuleTreeClick: function(data, node, vm) {
      if (data.id.indexOf('G') < 0) {
        if (this.exceptArr.indexOf(data.dataId) == -1) {
          this.clearTempExpt()
          const rule = this.treeNodeDataToTagOption(data)
          this.exceptRules.push(rule)
          this.$emit('updateRulesMap', rule.id)
        } else {
          this.$message({
            title: this.$t('text.prompt'),
            message: this.$t('pages.group_text16'),
            type: 'error',
            duration: 2000
          })
        }
      }
    },
    treeNodeDataToTagOption(nodeData) {
      return nodeData ? { id: nodeData.dataId, name: nodeData.label, relation: this.$t('pages.or'), type: nodeData.parentId } : null
    },
    // 将选中的规则转成零星检测规则
    toDripProps() {
      if (this.temp.doDrip) {
        this.checkRules.forEach(rule => {
          const drip = this.dripProps[rule.id] || { id: parseInt(rule.id), ...this.defaultDripTemp }
          this.$set(this.dripProps, rule.id, drip)
        })
      } else {
        this.dripProps = []
      }
    },
    toNumberArray(str) {
      const numberArray = []
      const strArray = Array.isArray(str) ? str : str.split(',')
      strArray.forEach(d => numberArray.push(Number(d)))
      return numberArray
    },
    // 将时间串拆分
    toTimeSpan(drip) {
      let timeSpan = drip.timeSpan || ''
      const timeOpt = ['w', 'd', 'h', 'm', 's']
      timeOpt.forEach(t => {
        if (timeSpan.indexOf(t) > 0) {
          const arr = timeSpan.split(t)
          // 如果有值则不修改
          const value = drip[t] || arr[0]
          this.$set(drip, t, value)
          timeSpan = arr[1]
        }
      })
    },
    // 格式化时间跨度，drip：零星规则对象， type：时间跨度文字，false为 key，true为 value
    formatTimeSpan(drip, type = false) {
      let result = ''
      const langOpt = {
        w: this.$t('pages.week'),
        d: this.$t('pages.day'),
        h: this.$t('pages.hour'),
        m: this.$t('pages.minute'),
        s: this.$t('pages.second')
      }
      Object.keys(langOpt).forEach(key => {
        const unit = type ? langOpt[key] : key
        if (drip[key]) result += drip[key] + unit
      })
      return result
    },
    formatFormData(data) {
      data.checkExpr = this.formatExpr('check')
      data.exceptExpr = this.formatExpr('except')
      if (data.doDrip) {
        const ids = []
        if (!this.isWeightMode) data.weightThreshold = null
        data.dripProps = []
        this.checkRules.forEach(rule => {
          if (ids.indexOf(rule.id) === -1) {
            ids.push(rule.id)
            const drip = { ...this.dripProps[rule.id] }
            if (drip.opTypes.indexOf(this.lossTypeAll) > -1) {
              const temp = []
              this.lossTypeOptions.forEach(opt => {
                if (opt.value != this.lossTypeAll) {
                  temp.push(opt.value)
                }
              })
              drip.opTypes = temp
            }
            data.dripProps.push({
              id: parseInt(rule.id),
              timeSpan: this.formatTimeSpan(drip),
              uniqueMatch: drip.uniqueMatch,
              countThreshold: this.isCountMode ? drip.countThreshold : null,
              weight: this.isWeightMode ? (drip.weight ? drip.weight : 1) : null,
              opTypes: drip.opTypes ? drip.opTypes.join(',') : ''
            })
          }
        })
      }
    },
    // 格式化选中的规则表达式
    formatExpr(rules) {
      const exprArr = this[rules + 'Arr']
      if (exprArr.length > 0) {
        // 当有设置规则时，需要简化并去重， 如 1|1&2|3&4|4&3 -> 1|3&4
        let tempArr = []
        for (let i = 0; i < exprArr.length; i++) {
          const expr = exprArr[i]
          if (expr.indexOf('&') == -1) {
            // 单独不重复的规则
            if (tempArr.indexOf(expr) == -1) {
              tempArr = tempArr.map(t => {
                if (t && t.split('&').indexOf(expr) == -1) {
                  return t
                }
              })
              tempArr.push(expr)
            }
          } else if (expr.indexOf('&') > -1) {
            // 包含 & 关系的多条规则
            const ea = expr.split('&').sort((a, b) => { return a - b })
            const temp = []
            let push = true
            for (let j = 0; j < ea.length; j++) {
              const e = ea[j]
              if (tempArr.indexOf(e) > -1) {
                push = false
                break
              }
              if (temp.indexOf(e) == -1) {
                temp.push(e)
              }
            }
            const ts = temp.join('&')
            if (temp.length == 1) {
              // 单独不重复的规则
              if (tempArr.indexOf(ts) == -1) {
                tempArr = tempArr.map(t => {
                  if (t && t.split('&').indexOf(ts) == -1) {
                    return t
                  }
                })
                tempArr.push(ts)
              }
            } else {
              if (push && tempArr.indexOf(ts) == -1) {
                tempArr.push(ts)
              }
            }
          }
        }
        tempArr = tempArr.filter((s) => {
          return s && s.trim() // 注：IE9(不包含IE9)以下的版本没有trim()方法
        })
        return tempArr.join('|')
      } else {
        return ''
      }
    },
    formatExprArr(rules) {
      let expr = ''
      rules.forEach((rule) => {
        if (expr.length > 0) {
          expr += this.relationMap[rule.relation]
        }
        expr += rule.id
      })
      return expr ? expr.split('|') : []
    },
    // 将表达式转化为tag
    formatExprToTagOp(expr) {
      const tagOps = []
      if (!expr) return tagOps
      const ruleIds = expr.replace(/&/g, '|').split('|')
      let p = 0
      // 根据规则id，构造tag
      ruleIds.forEach((ruleId, index) => {
        const nodeData = this.getNodeDataByDataId(this.treeNode, ruleId)
        this.tagOptionMap[ruleId] = nodeData ? this.treeNodeDataToTagOption(nodeData) : null
        let relation = ''
        if (index > 0) { // 首位没有关系符号&或|
          relation = expr.substring(p, p + 1)
          p += 1
        }
        const tagOp = this.tagOptionMap[ruleId]
        p += ruleId.length
        if (tagOp) {
          let relationName = this.relationMap[relation]
          relationName = !relationName ? tagOp.relation : relationName
          tagOps.push(Object.assign({}, tagOp, { relation: relationName }))
        }
      })
      return tagOps
    },
    // 根据销售模块过滤泄露方式
    filterLossType(data) {
      if (data) {
        const lossType = this.lossTypeOptions.map(item => item.value)
        if (data.opTypes.length > 0) {
          const filterLossType = data.opTypes.filter(value => lossType.indexOf(value) > -1)
          this.$set(data, 'opTypes', filterLossType)
          // data.opTypes = filterLossType
        }
      }
    },
    dripShow(id) {
      this.filterLossType(this.dripProps[id])
      this.toTimeSpan(this.dripProps[id])
      this.dripProps[id].timeSpan = this.formatTimeSpan(this.dripProps[id], true)
      this.currentId = id
    },
    getNodeDataByDataId(nodeDatas, dataId) {
      for (let i = 0, size = nodeDatas.length; i < size; i++) {
        const nodeData = nodeDatas[i]
        if (nodeData.dataId === dataId) {
          return nodeData
        } else if (nodeData.children) {
          const targetNode = this.getNodeDataByDataId(nodeData.children, dataId)
          if (targetNode) return targetNode
        }
      }
      return null
    },
    closeTagFunc: function(tagDatas, index) {
      tagDatas.splice(index, 1)
    },
    clickTagLinkFunc: function(data) {
      data.relation = data.relation === this.$t('pages.and1') ? this.$t('pages.or') : this.$t('pages.and1')
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.doDrip = this.dripAble ? 1 : 0
      this.checkRules.splice(0, this.checkRules.length)
      this.exceptRules.splice(0, this.exceptRules.length)
      this.dripProps = []
      this.isWeightMode = false
    },
    nameValidator(rule, value, callback) {
      const errorStr = value.match(nameRegExp)
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } if (nameRegExp.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_IllegalCharacter').concat('：').concat(errorStr.join(''))))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    operatorsValidator(rule, value, callback) {
      if (this.checkRules.length === 0) {
        callback(new Error(this.$t('pages.group_text17')))
        return
      } else if (this.temp.doDrip) {
        for (let i = 0, size = this.checkRules.length; i < size; i++) {
          const rule = this.checkRules[i]
          const drip = this.dripProps[rule.id]
          const timeSpanNotNum = ['w', 'd', 'h', 'm', 's'].filter((item) => { return drip[item] && (isNaN(drip[item]) || drip[item] <= 0) })
          // 泄露方式非空验证
          if (!drip.opTypes || drip.opTypes.length === 0) {
            callback(new Error(this.$t('pages.group_text19', { rule: rule.name })))
            return
          }
          // 时间跨度非空验证
          if (!drip.w && !drip.d && !drip.h && !drip.m && !drip.s) {
            callback(new Error(this.$t('pages.group_text20', { rule: rule.name })))
            return
          }
          // 时间跨度正整数验证
          if (timeSpanNotNum.length > 0) {
            callback(new Error(this.$t('pages.group_text21', { rule: rule.name })))
            return
          }
          // 计数模式次数阈值正整数验证
          if (this.isCountMode && (isNaN(drip.countThreshold) || drip.countThreshold <= 0)) {
            callback(new Error(this.$t('pages.group_text22', { rule: rule.name })))
            return
          }
          // 单位权重正整数验证
          if (this.isWeightMode && (isNaN(drip.weight) || drip.weight <= 0)) {
            callback(new Error(this.$t('pages.group_text24', { rule: rule.name })))
            return
          }
          // 单位权重大小验证
          if (this.isWeightMode && Number(drip.weight) > Number(this.temp.weightThreshold)) {
            callback(new Error(this.$t('pages.group_text23', { rule: rule.name })))
            return
          }
        }
      }
      callback()
    },
    timeSpanValidator(rule, value, callback) {
      const drip = this.dripProps[this.currentId]
      const timeSpanNotNum = ['w', 'd', 'h', 'm', 's'].filter((item) => { return drip[item] && isNaN(drip[item]) })
      if (timeSpanNotNum.length > 0) {
        callback(new Error(this.$t('pages.group_text25')))
      } else {
        callback()
      }
    },
    dripModeValidator(rule, value, callback) {
      if (!this.isCountMode && !this.isWeightMode) {
        callback(new Error(this.$t('pages.group_text26')))
      } else {
        callback()
      }
    },
    getPathFromRuleOptions(ruleNodes, checkedData, level) {
      if (!ruleNodes || !checkedData) return ''
      const checkedNodeId = checkedData[level ? level - 1 : 0]
      for (let i = 0, size = ruleNodes.length; i < size; i++) {
        const node = ruleNodes[i]
        if (node.id === checkedNodeId || node.id.toString() === checkedNodeId) {
          let path = ''
          if (level < checkedData.length) {
            path = this.getPathFromRuleOptions(node.children, checkedData, ++level)
          }
          return node.label + (path ? '/' + path : '')
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  >>>.el-form-item__error{
    word-break: normal !important;
  }
  .drip{
    cursor: pointer;
  }
  .time-span  .el-input{
    width: 50px;
    >>>.el-input__inner{
      padding: 0 8px;
      text-align: center;
    }
  }
  .visitOnly{
    background: #e4e7e9;
  }
</style>

