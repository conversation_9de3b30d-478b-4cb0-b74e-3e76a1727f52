<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <tree-menu
        ref="ruleGroupClassTree"
        resizeable
        :default-expand-all="true"
        :data="ruleGroupClassTreeData"
        :render-content="renderContent"
        @node-click="ruleGroupClassTreeNodeCheckChange"
      />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree"/>
        </el-button>
        <el-button type="primary" size="mini" @click="handleMoving">
          {{ $t('button.moveGroup') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('pages.addGroup') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.delGroup') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.group_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="ruleGroupList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <group-dialog ref="groupDialog" />
    <edit-group-dlg
      ref="editGroupDlg"
      :title="$t('pages.group_text27')"
      :group-tree-data="formTreeData"
      :add-func="createRuleGroupClass"
      :update-func="updateRuleGroupClass"
      :delete-func="deleteRuleGroupClass"
      :move-func="moveGroup"
      :edit-valid-func="getRuleGroupClassByName"
      @addEnd="createNode"
      @updateEnd="updateNode"
      @deleteEnd="removeGroupEnd"
      @moveEnd="moveGroupEnd"
    />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="updateGroupForm ? $t('text.editInfo', { info: $t('pages.group_text27') }) : $t('text.batchDelete', { info: $t('route.group') })"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :filter-name-desc="$t('route.group')"
      :edit-type="updateGroupForm ? 0 : 1"
      @submitEnd="batchEditFunc"
    >
      <FormItem v-if="updateGroupForm" class="required" :label="$t('table.groupName')" style="display: flex">
        <tree-select :data="ruleGroupClassTreeData[0].children" :checked-keys="checkedKeys" is-filter :width="270" style="width: 270px;" @change="parentIdObjChange" />
      </FormItem>
    </batch-edit-page-dlg>

    <delete-group-dlg
      ref="deleteGroupDlg"
      :title="$t('text.deleteInfo', { info: $t('pages.group_text27') }) "
      :dlg-title="$t('route.group')"
      :select-tree-data="formTreeData"
      :delete-group-and-data="deleteGroupAndData"
      :delete-func="deleteRuleGroupClass"
      :move-group-to-other="moveGroupToOther"
      @removeFunc="removeGroupEnd"
      @deleteEnd="removeGroupEnd"
      @refreshTableData="refreshTableData"
    ></delete-group-dlg>

  </div>
</template>

<script>
import {
  getRulePage,
  deleteRule,
  countChildByGroupId,
  getGroupTreeNode,
  createRuleGroupClass,
  updateRuleGroupClass,
  deleteRuleGroupClass,
  getRuleGroupClassByName,
  moveGroup,
  batchUpdateAllGroup,
  batchUpdateGroup,
  deleteGroupAndData,
  moveGroupToOther
} from '@/api/contentStrategy/rule/group'
import GroupDialog from './groupDialog'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import EditGroupDlg from '@/views/common/editGroupDlg'
import DeleteGroupDlg from '@/views/common/deleteGroupDlg'

export default {
  name: 'Group',
  components: { DeleteGroupDlg, EditGroupDlg, GroupDialog, BatchEditPageDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'ruleGroupName', width: '150', fixed: true },
        { prop: 'groupId', label: 'sourceGroup', width: '150', sort: 'custom', formatter: this.groupFormatter },
        { prop: 'checkExpr', label: 'detectionRules', width: '300', formatter: this.operatorsFormatter },
        { prop: 'exceptExpr', label: 'exceptExpr', width: '300', formatter: this.operatorsFormatter },
        { prop: 'doDrip', label: 'doDrip', width: '150', hidden: !this.hasPermission('117'), formatter: this.doDripFormatter },
        { prop: 'remark', label: 'remark', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        groupId: undefined
      },
      deleteable: false,
      addBtnAble: false,
      showTree: true,
      ruleGroupClassTreeData: [{ id: 'G0', dataId: '0', label: this.$t('pages.group_text27'), parentId: '', children: [] }],
      formTreeData: [],
      checkedKeys: [],
      updateGroupForm: false,
      updateGroupId: undefined // 批量修改的分组id
    }
  },
  computed: {
    ruleGroupTable() {
      return this.$refs['ruleGroupList']
    },
    ruleGroupClassTree: function() {
      return this.$refs['ruleGroupClassTree']
    }
  },
  activated() {
    this.loadRuleGroupClassTree()
  },
  created() {
    this.loadRuleGroupClassTree()
  },
  methods: {
    getRuleGroupClassByName,
    deleteRuleGroupClass,
    updateRuleGroupClass,
    createRuleGroupClass,
    moveGroup,
    deleteGroupAndData,
    moveGroupToOther,
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    renderContent(h, { node, data, store }) {
      const iconShow = data.dataId != '0'
      const delIconShow = data.dataId != '1'
      return (
        <div class='custom-tree-node'>
          <span>{data.label}</span>
          <span class='el-ic'>
            <svg-icon v-show={!iconShow} icon-class='add' title={this.$t('button.insert')} class='icon-space'
              on-click={r => this.handleGroupCreate(data)}/>
            <svg-icon v-show={iconShow} icon-class='edit' title={this.$t('button.edit')} class='icon-space'
              on-click={r => this.handleGroupUpdate(data)}/>
            <svg-icon v-show={iconShow && delIconShow} icon-class='delete' title={this.$t('button.delete')} class='icon-space'
              on-click={r => this.removeNode(data)}/>
          </span>
        </div>
      )
    },
    loadRuleGroupClassTree: function() {
      return getGroupTreeNode().then(respond => {
        this.ruleGroupClassTreeData[0].children = respond.data
        if (respond.data.length > 0) {
          this.treeNodeType = respond.data[0].type
        }
      })
    },
    handleGroupCreate(data) {
      this.$refs['editGroupDlg'].handleCreate(0)
    },
    handleGroupUpdate: function(data) {
      this.$refs['editGroupDlg'].handleUpdate({
        id: data.dataId,
        name: data.label,
        parentId: this.replaceTreeNodeType(data.parentId)
      })
    },
    replaceTreeNodeType: function(data) {
      if (this.treeNodeType) {
        return data.replace(this.treeNodeType, '')
      }
      return data
    },
    removeNode(data) {
      countChildByGroupId(data.dataId).then(respond => {
        if (respond.data > 0) {
          this.formTreeData = this.loadTypeTreeExceptRoot()
          this.$refs['deleteGroupDlg'].handleCreate({ groupId: this.query.groupId })
          return
        }
        this.$refs['editGroupDlg'].handleDelete(data.dataId)
      })
    },
    loadTypeTreeExceptRoot() {
      return this.ruleGroupClassTreeData[0].children
    },
    ruleGroupClassTreeNodeCheckChange: function(checkedData, checkedNode) {
      checkedNode.expanded = true
      this.addBtnAble = !!checkedData && checkedData.dataId != '0'
      if (checkedData) {
        this.query.groupId = Number(checkedData.dataId)
      } else {
        this.query.groupId = undefined
      }
      this.ruleGroupTable.execRowDataApi(this.query)
    },
    handleMoving() {
      this.updateGroupForm = true
      this.updateGroupId = undefined
      this.checkedKeys = []
      const selectedData = this.ruleGroupTable.getSelectedDatas()
      const total = this.ruleGroupTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    dataToTreeNode(data) {
      return {
        id: this.treeNodeType + data.id,
        type: this.treeNodeType,
        dataId: data.id.toString(),
        label: data.name,
        parentId: 'G0'
      }
    },
    createNode(data) {
      this.ruleGroupClassTree.addNode(this.dataToTreeNode(data))
      this.$refs.groupDialog && this.$refs.groupDialog.loadRuleGroupClassTree()
    },
    updateNode(data) {
      this.ruleGroupClassTree.updateNode(this.dataToTreeNode(data))
      this.$refs.groupDialog && this.$refs.groupDialog.loadRuleGroupClassTree()
    },
    removeGroupEnd(dataId) {
      const nodeData = this.ruleGroupClassTree.findNode(this.ruleGroupClassTreeData, dataId, 'dataId')
      if (nodeData) {
        this.ruleGroupClassTree.removeNode([nodeData.id])
        this.addBtnAble = false
      }
      this.$refs.groupDialog && this.$refs.groupDialog.loadRuleGroupClassTree()
    },
    arriveTreeNodeId(dataId) {
      const nodeData = this.ruleGroupClassTree.findNode(this.ruleGroupClassTreeData, dataId, 'dataId')
      if (nodeData) {
        this.ruleGroupClassTree.setCurrentKey(nodeData.id)
        this.ruleGroupClassTreeNodeCheckChange(nodeData, {})
      }
    },
    refreshTableData() {
      this.query.groupId = null
      this.ruleGroupTable.clearRowData()
      this.ruleGroupTable.execRowDataApi()
    },
    moveGroupEnd() {
      this.ruleGroupTable.execRowDataApi()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRulePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.ruleGroupTable.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.groupDialog.handleCreate(this.query.groupId)
    },
    handleUpdate(row) {
      this.$refs.groupDialog.handleUpdate(row)
    },
    handleDelete() {
      this.updateGroupForm = false
      this.idleMailDelete = false
      const selectedData = this.ruleGroupTable.getSelectedDatas()
      const total = this.ruleGroupTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedData, total, this.query)
    },
    getGroupNameByDataId(treeDatas, dataId) {
      for (let i = 0, size = treeDatas.length; i < size; i++) {
        const treeData = treeDatas[i]
        if (treeData.dataId == dataId) {
          return treeData.label
        } else if (treeData.children) {
          const result = this.getGroupNameByDataId(treeData.children, dataId)
          if (result) return result
        }
      }
      return ''
    },
    groupFormatter(row, data) {
      return this.getGroupNameByDataId(this.ruleGroupClassTreeData, data)
    },
    operatorsFormatter(row, data) {
      return this.$refs.groupDialog.operatorsFormatter(row, data)
    },
    doDripFormatter(row, data) {
      return data ? this.$t('text.yes') : ''
    },
    parentIdObjChange(key, nodeData) {
      if (nodeData) {
        this.updateGroupId = Number(nodeData.dataId)
      }
    },
    batchEditFunc(params, callback) {
      if (this.updateGroupForm) {
        this.moveData(params, callback)
      } else {
        this.deleteData(params, callback)
      }
    },
    moveData(params, callback) {
      if (!this.updateGroupId) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.validaGroup1'),
          type: 'warning',
          duration: 2000
        })
        callback('cancel')
        return
      }
      const updateFunc = params.ids ? batchUpdateGroup : batchUpdateAllGroup
      updateFunc(params, this.updateGroupId).then(respond => {
        this.ruleGroupTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('text.updateSuccess'),
          type: 'success',
          duration: 2000
        })
        callback(respond)
      }).catch(e => { callback(e) })
    },
    deleteData(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.ruleGroupTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch((e) => { callback(e) })
      }).catch((e) => { callback(e) })
    }
  }
}
</script>
