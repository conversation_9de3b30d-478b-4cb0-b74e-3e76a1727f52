<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="groupDialogVisible"
      width="600px"
      @dragDialog="handleDrag"
      @close="closeFunc"
      @closed="closed"
    >
      <group-form
        ref="groupForm"
        :drip-able="dripAble"
        :drip-change-able="dripChangeAble"
        :stg-code="stgCode"
        :filter-high-config="filterHighConfig"
        @submitting="(val) => {submitting = val}"
        @closeDlg="() => {groupDialogVisible = false}"
        @ruleClose="(val) => {ruleClose = val}"
        @updateGridTable="updateGridTable"
        @createRule="createRule"
        @ruleView="ruleView"
        @updateRulesMap="updateRulesMap"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="groupDialogVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <regular-dialog ref="regularDialog" @regularClosed="ruleClosed" />
    <keyword-dialog ref="keywordDialog" @keywordClosed="ruleClosed" />
    <file-attribute-dialog ref="fileAttributeDialog" @closed="ruleClosed" />
    <file-fp-dialog ref="fileFpDialog" @fileFpClosed="ruleClosed" />
    <edm-dialog ref="edmDialog" @edmClosed="ruleClosed" />
    <vml-dialog ref="vmlDialog" @vmlClosed="ruleClosed" />
    <source-code-dialog ref="sourceCodeDialog" @sourceCodeClosed="ruleClosed" />
  </div>
</template>

<script>
import {
  listRegularRuleTreeNode, listkeywordRuleTreeNode, listFileAttributeRuleTreeNode, listFileFingerprintRuleTreeNode,
  listEdmRuleTreeNode, listVmlRuleTreeNode, getSubRuleByIds, listSourceCodeRuleTreeNode
} from '@/api/contentStrategy/rule/group'
import GroupForm from './groupForm'
import RegularDialog from '@/views/contentStrategy/rule/regular/regularDialog'
import KeywordDialog from '@/views/contentStrategy/rule/keyword/keywordDialog'
import FileAttributeDialog from '@/views/contentStrategy/rule/fileAttribute/fileAttributeDialog'
import FileFpDialog from '@/views/contentStrategy/rule/fileFp/fileFpDialog'
import EdmDialog from '@/views/contentStrategy/rule/edm/edmDialog'
import VmlDialog from '@/views/contentStrategy/rule/vml/vmlDialog'
import SourceCodeDialog from '@/views/contentStrategy/rule/sourceCode/sourceCodeDialog'
import { isSameTimestamp, initTimestamp } from '@/utils'

export default {
  name: 'GroupDialog',
  components: { GroupForm, FileAttributeDialog, RegularDialog, KeywordDialog, FileFpDialog, EdmDialog, VmlDialog, SourceCodeDialog },
  props: {
    dripAble: { type: Boolean, default: false }, // 是否支持零星检测
    dripChangeAble: { type: Boolean, default: true }, // 是否支持修改零星检测
    stgCode: { type: Number, default: 23 }, // 策略编码，stg_base_config的number字段值
    filterHighConfig: { type: Boolean, default: false } // 是否过滤高级算法配置
  },
  data() {
    return {
      groupDialogVisible: false,
      dialogStatus: '',
      submitting: false,
      rulesMap: {}, // 规则详情，规则id作为key，规则对象为 value
      ruleClose: false, // 是否由于打开规则编辑弹窗而导致分类弹窗关闭
      noOpenGroup: false,
      treeNode: [
        { label: this.$t('route.regular'), id: 'G3', dataId: '0', children: [] },
        { label: this.$t('route.keyword'), id: 'G2', dataId: '-1', children: [] },
        { label: this.$t('route.file'), id: 'G1', dataId: '-2', children: [] },
        { label: this.$t('route.fileFp'), id: 'G6', dataId: '-3', children: [] },
        { label: this.$t('route.edm'), id: 'G4', dataId: '-4', children: [] },
        { label: this.$t('route.vml'), id: 'G5', dataId: '-5', children: [] },
        { label: this.$t('route.sourceCode'), id: 'G12', dataId: '-12', children: [] }
      ],
      ruleMap: {
        'G1': { id: 'G1', func: listFileAttributeRuleTreeNode, name: 'FileRule' },
        'G2': { id: 'G2', func: listkeywordRuleTreeNode, name: 'KeywordRule' },
        'G3': { id: 'G3', func: listRegularRuleTreeNode, name: 'RegularRule' },
        'G4': { id: 'G4', func: listEdmRuleTreeNode, name: 'EdmRule' },
        'G5': { id: 'G5', func: listVmlRuleTreeNode, name: 'VmlRule' },
        'G6': { id: 'G6', func: listFileFingerprintRuleTreeNode, name: 'FileFpRule' },
        'G12': { id: 'G12', func: listSourceCodeRuleTreeNode, name: 'SourceCode' }
      },
      relationMap: { '且': '&', '或': '|', '&': this.$t('pages.and1'), '|': this.$t('pages.or'), '!': this.$t('text.no1'), 'And': '&', 'Or': '|', 'No': '!' },
      tagOptionMap: {}
    }
  },
  computed: {
    title() {
      return (this.dripAble ? this.$t('pages.doDrip') : '') + this.$t('route.group')
    },
    textMap() {
      return {
        update: this.i18nConcatText(this.title, 'update'),
        create: this.i18nConcatText(this.title, 'create')
      }
    },
    gridTable() {
      return this.$parent.ruleGroupTable
    }
  },
  activated() {
    this.loadRuleTree()
  },
  created() {
    this.loadRuleTree()
  },
  methods: {
    groupForm() {
      return this.$refs['groupForm']
    },
    closeFunc() {
      this.$emit('groupClosed', this.ruleClose)
      this.ruleClose = false
    },
    closed() {
      this.$refs.groupForm.clearRules()
    },
    // 根据 id 获取规则的数据，并存到 rulesMap
    getRules(ids) {
      getSubRuleByIds(ids).then(res => {
        res.data.forEach(item => {
          this.rulesMap[item.id] = item
        })
      })
    },
    loadRuleTree() {
      let updateFlag = false
      this.treeNode.forEach((nodeData) => {
        const rule = this.ruleMap[nodeData.id]
        // 根据时间戳是否相同，判断是否需要更新数据
        if (!isSameTimestamp(this, rule.name)) {
          rule.func().then(respond => {
            nodeData.children = respond.data
          })
          updateFlag = true
        }
      })
      // 更新 rulesMap 数据
      updateFlag && this.getRules(Object.keys(this.rulesMap).toString())
      initTimestamp(this)
    },
    loadRuleGroupClassTree: function() {
      this.$refs.groupForm && this.$refs.groupForm.loadRuleGroupClassTree()
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate(groupId) {
      this.dialogStatus = 'create'
      this.groupDialogVisible = true
      this.$nextTick(() => {
        this.$refs['groupForm'].handleCreate(groupId)
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.groupDialogVisible = true
      this.getRules(row.ruleIds)
      this.$nextTick(() => {
        this.$refs['groupForm'].handleUpdate(row)
      })
    },
    createData() {
      this.$refs['groupForm'].createData()
    },
    updateData() {
      this.$refs['groupForm'].updateData()
    },
    updateGridTable(type, data) {
      if (type == 'create') {
        this.$emit('createData', data)
        this.gridTable && this.gridTable.execRowDataApi()
      } else {
        this.gridTable && this.gridTable.updateRowData(data)
      }
    },
    // type 规则类型，groupData 数据， exprType 表达式类型
    createRule(type, groupData, exprType) {
      // 保存编辑状态
      this.tempData = groupData
      this.exprType = exprType
      this.$refs[type].handleCreate()
    },
    updateRulesMap(id) {
      this.getRules(id)
    },
    ruleView(id, doDrip) {
      this.handleRuleView(this.rulesMap[id], doDrip)
    },
    handleRuleView(data, doDrip) {
      this.noOpenGroup = true
      const dlg = { 1: 'fileAttributeDialog', 2: 'keywordDialog', 3: 'regularDialog',
        4: 'edmDialog', 5: 'vmlDialog', 6: 'fileFpDialog', 12: 'sourceCodeDialog' }[data.type]
      dlg && this.$refs[dlg].handleView(data, doDrip)
    },
    // 列表中 检测规则、例外规则 字段格式化
    operatorsFormatter(row, data) {
      const operatorNames = []
      if (data) {
        const ruleIds = data.replace(/&/g, '|').split('|')
        let p = 0
        ruleIds.forEach((ruleId, index) => {
          if (!this.tagOptionMap[ruleId]) {
            const treeNode = this.$refs['groupForm'] ? this.$refs['groupForm'].treeNode : this.treeNode
            const nodeData = this.getNodeDataByDataId(treeNode, ruleId)
            this.tagOptionMap[ruleId] = this.treeNodeDataToTagOption(nodeData)
          }
          let relation = ''
          if (index > 0) { // 首位没有关系符号&或|
            relation = data.substring(p, p + 1)
            p += 1
          }
          const tagOp = this.tagOptionMap[ruleId]
          p += ruleId.length
          if (tagOp) {
            // 没有关系符，或关系符是 或 时，relationName 设为 null
            const relationName = !relation || relation === '|' ? null : this.relationMap[relation]
            if (relationName) {
              // 关系符是 且 时，与最后一个值拼接在一起
              const editIndex = operatorNames.length - 1
              operatorNames[editIndex] += ` ${relationName} ${tagOp.name}`
            } else {
              operatorNames.push(tagOp.name)
            }
          }
        })
      }
      return operatorNames.join('，')
    },
    getNodeDataByDataId(nodeDatas, dataId) {
      for (let i = 0, size = nodeDatas.length; i < size; i++) {
        const nodeData = nodeDatas[i]
        if (nodeData.dataId === dataId) {
          return nodeData
        } else if (nodeData.children) {
          const targetNode = this.getNodeDataByDataId(nodeData.children, dataId)
          if (targetNode) return targetNode
        }
      }
      return null
    },
    // 树节点转成 tag 标签 option
    treeNodeDataToTagOption(nodeData) {
      return nodeData ? { id: nodeData.dataId, name: nodeData.label, relation: this.$t('pages.or'), type: nodeData.parentId } : null
    },
    // 新增规则弹窗关闭后的操作
    ruleClosed(ruleType, ruleId) {
      if (this.noOpenGroup) {
        this.noOpenGroup = false
        return
      }
      this.groupDialogVisible = true
      this.tempData[this.exprType] += ruleId ? '|' + ruleId : ''
      this.getRules(ruleId)
      // 清除表达式的临时数据
      this.$refs['groupForm'].clearTempExpt()
      this.$refs['groupForm'].handleUpdate(this.tempData, false)
      if (ruleType) {
        const rule = this.ruleMap[ruleType]
        const treeNodes = this.$refs['groupForm'].treeNode
        // 规则弹窗关闭后，更新对应树节点数据
        for (let i = 0; i < treeNodes.length; i++) {
          const node = treeNodes[i]
          if (node.id === ruleType) {
            rule.func().then(respond => { node.children = respond.data })
            break
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
