<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addRule') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="fileRuleList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <file-attribute-dialog ref="fileAttributeDialog"/>
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="$t('text.batchDelete', {info: $t('route.file')})"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :filter-name-desc="$t('route.file')"
      :edit-type="1"
      @submitEnd="batchDeleteFunc"
    />
  </div>
</template>

<script>
import { getRulePage, deleteRule } from '@/api/contentStrategy/rule/fileAttribute'
import FileAttributeDialog from './fileAttributeDialog'
import { parseTime } from '@/utils'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

export default {
  name: 'FileRule',
  components: { FileAttributeDialog, BatchEditPageDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'ruleName', width: '150', fixed: true },
        { prop: 'fileName', label: 'fileName', width: '150' },
        { prop: 'fileSuffix', label: 'suffixes', width: '100' },
        { prop: 'fileAuthor', label: 'fileAuthor', width: '100' },
        // { prop: 'lastModifier', label: '最后保存者', width: '100' },
        { label: 'maxFileSize1', width: '120', formatter: this.fileSizeFormatter },
        { label: 'fileCreateDate', width: '120', formatter: this.createDateFormatter },
        { label: 'fileUpdateDate', width: '120', formatter: this.updateDateFormatter },
        { prop: 'isEncode', label: 'isEncode', width: '120', formatter: this.isEncodeFormatter },
        { prop: 'exceptFileName', label: 'exceptFileName', width: '150' },
        { prop: 'exceptFileSuffix', label: 'exceptFileSuffix', width: '150' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      deleteable: false
    }
  },
  computed: {
    ruleTable() {
      return this.$refs['fileRuleList']
    }
  },
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRulePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.ruleTable.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.fileAttributeDialog.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.fileAttributeDialog.handleUpdate(row)
    },
    handleDelete() {
      const selectedDatas = this.ruleTable.getSelectedDatas()
      const total = this.ruleTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedDatas, total, this.query)
    },
    fileSizeFormatter: function(row) {
      if (row.minSize && row.maxSize) {
        return row.minSize + ' ~ ' + row.maxSize
      } else if (row.minSize) {
        return '>=' + row.minSize
      } else if (row.maxSize) {
        return '<=' + row.maxSize
      }
      return ''
    },
    createDateFormatter: function(row) {
      const datePatten = 'y-m-d h:i:s'
      if (row.minCreateDate && row.maxCreateDate) {
        return parseTime(row.minCreateDate, datePatten) + ' ~ ' + parseTime(row.maxCreateDate, datePatten)
      } else if (row.minCreateDate) {
        return '>=' + parseTime(row.minCreateDate, datePatten)
      } else if (row.maxCreateDate) {
        return '<=' + parseTime(row.maxCreateDate, datePatten)
      }
      return ''
    },
    updateDateFormatter: function(row) {
      const datePatten = 'y-m-d h:i:s'
      if (row.minUpdateDate && row.maxUpdateDate) {
        return parseTime(row.minUpdateDate, datePatten) + ' ~ ' + parseTime(row.maxUpdateDate, datePatten)
      } else if (row.minUpdateDate) {
        return '>=' + parseTime(row.minUpdateDate, datePatten)
      } else if (row.maxUpdateDate) {
        return '<=' + parseTime(row.maxUpdateDate, datePatten)
      }
      return ''
    },
    isEncodeFormatter: function(row, data) {
      return data ? this.$t('text.yes') : ''
    },
    batchDeleteFunc(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.ruleTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch((e) => { callback(e) })
      }).catch((e) => { callback(e) })
    }
  }
}
</script>
