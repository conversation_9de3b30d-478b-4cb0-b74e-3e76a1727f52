<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @close="closeFunc"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 750px;">
        <FormItem :label="$t('table.ruleName')" prop="name">
          <el-input v-model="temp.name" v-trim maxlength="60" :disabled="!formable"></el-input>
        </FormItem>
        <el-card class="box-card" :body-style="{padding: '0 5px'}">
          <div slot="header" class="clearfix">
            <span class="required">{{ $t('pages.keyword_text1') }}</span>
          </div>
          <div style="padding: 5px;">
            <el-row>
              <el-col :span="23">
                <FormItem :label="$t('table.fileName')" :tooltip-content="$t('pages.file_text6')" tooltip-placement="bottom-start">
                  <el-input v-model="temp.fileName" :disabled="!formable" maxlength="256" :placeholder="$t('pages.inputMaxLength', { num: 256 })" @change="validateIncludeFilter"></el-input>
                </FormItem>
              </el-col>
            </el-row>
            <FormItem :label="$t('table.suffixes')">
              <el-checkbox-group v-model="fileSuffixChecked" :disabled="!formable" @change="validateIncludeFilter">
                <el-checkbox v-for="item in fileSuffixOptions" :key="item.value" :label="item.value">
                  {{ item.label }}
                  <el-input
                    v-if="item.value===0"
                    v-model="fileSuffixOther"
                    :disabled="!formable || !fileSuffixOtherEditable"
                    :placeholder="$t('pages.file_text1')"
                    :maxlength="suffixMaxLength"
                    style="width: 300px;"
                    @change="validateIncludeFilter"
                  ></el-input>
                </el-checkbox>
                <el-button :disabled="!formable || !fileSuffixOtherEditable" size="small" style="margin-left: 10px;padding: 5px" @click="handleFileSuffixImport('fileSuffix')">
                  {{ $t('button.FileSuffixLibImport') }}
                </el-button>
              </el-checkbox-group>
            </FormItem>
            <el-row>
              <el-col :span="11">
                <FormItem :label="$t('table.fileAuthor')">
                  <el-input v-model="temp.fileAuthor" :disabled="!formable" maxlength="256" :placeholder="$t('pages.inputMaxLength', { num: 256 })" @change="validateIncludeFilter"></el-input>
                </FormItem>
              </el-col>
              <!--<el-col :span="1">&nbsp;</el-col>
              <el-col :span="11">
                <FormItem label="最后保存者">
                  <el-input v-model="temp.lastModifier" :disabled="!formable"></el-input>
                </FormItem>
              </el-col>-->
            </el-row>
            <el-row>
              <FormItem :label="$t('table.maxFileSize1')" prop="fileSize">
                <div style="height: 30px;">
                  <el-col :span="11">
                    <el-input-number
                      v-model="temp.minSize"
                      :precision="0"
                      :step="1"
                      :min="0"
                      :max="9999999"
                      controls-position="right"
                      :disabled="!formable"
                      style="width: 98%;"
                      @change="validateIncludeFilter"
                    />
                  </el-col>
                  <el-col :span="1">~</el-col>
                  <el-col :span="11">
                    <el-input-number
                      v-model="temp.maxSize"
                      :precision="0"
                      :step="1"
                      :min="1"
                      :max="9999999"
                      controls-position="right"
                      :disabled="!formable"
                      style="width: 98%;"
                      @change="validateIncludeFilter"
                    />
                  </el-col>
                </div>
              </FormItem>
            </el-row>
            <el-row>
              <FormItem :label="$t('table.fileCreateDate')">
                <el-col :span="11">
                  <el-date-picker
                    v-model="temp.minCreateDate"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :placeholder="$t('pages.file_text3')"
                    :disabled="!formable"
                    style="width: 98%;"
                    @change="()=>{ createDateChange(1); validateIncludeFilter(); }"
                  ></el-date-picker>
                </el-col>
                <el-col :span="1">~</el-col>
                <el-col :span="11">
                  <el-date-picker
                    v-model="temp.maxCreateDate"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :placeholder="$t('pages.file_text4')"
                    :disabled="!formable"
                    style="width: 98%;"
                    @change="()=>{ createDateChange(2); validateIncludeFilter(); }"
                  ></el-date-picker>
                </el-col>
              </FormItem>
            </el-row>
            <el-row>
              <FormItem :label="$t('table.fileUpdateDate')">
                <el-col :span="11">
                  <el-date-picker
                    v-model="temp.minUpdateDate"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :placeholder="$t('pages.file_text3')"
                    :disabled="!formable"
                    style="width: 98%;"
                    @change="()=>{ updateDateChange(1); validateIncludeFilter(); }"
                  ></el-date-picker>
                </el-col>
                <el-col :span="1">~</el-col>
                <el-col :span="11">
                  <el-date-picker
                    v-model="temp.maxUpdateDate"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :placeholder="$t('pages.file_text4')"
                    :disabled="!formable"
                    style="width: 98%;"
                    @change="()=>{ updateDateChange(2); validateIncludeFilter(); }"
                  ></el-date-picker>
                </el-col>
              </FormItem>
            </el-row>
            <FormItem label=" ">
              <el-checkbox v-model="temp.isEncode" :disabled="!formable" @change="validateIncludeFilter">{{ $t('pages.file_testPw') }}</el-checkbox>
            </FormItem>
            <FormItem prop="includeFilter"></FormItem>
          </div>
        </el-card>
        <el-card v-show="!isDoDrip" class="box-card" :body-style="{padding: '0 5px'}">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.excepeCondition') }}</span>
            <el-tooltip effect="dark" :content="$t('pages.file_text7')" placement="top-start">
              <i class="el-icon-info"/>
            </el-tooltip>
          </div>
          <div style="padding: 5px;">
            <el-row>
              <el-col :span="23">
                <FormItem :label="$t('table.fileName')" prop="fileName" :tooltip-content="$t('pages.file_text6')" tooltip-placement="bottom-start">
                  <el-input v-model="temp.exceptFileName" :disabled="!formable" maxlength="256" :placeholder="$t('pages.inputMaxLength', { num: 256 })"></el-input>
                </FormItem>
              </el-col>
            </el-row>
            <FormItem :label="$t('table.suffixes')">
              <el-checkbox-group v-model="exceptFileSuffixChecked" :disabled="!formable">
                <el-checkbox v-for="item in fileSuffixOptions" :key="item.value" :label="item.value">
                  {{ item.label }}
                  <el-input v-if="item.value===0" v-model="exceptFileSuffixOther" :disabled="!formable || !exceptFileSuffixOtherEditable" :placeholder="$t('pages.file_text1')" :maxlength="suffixMaxLength" style="width: 300px;"></el-input>
                </el-checkbox>
                <el-button :disabled="!formable || !exceptFileSuffixOtherEditable" size="small" style="margin-left: 10px;padding: 5px" @click="handleFileSuffixImport('exceptFileSuffix')">
                  {{ $t('button.FileSuffixLibImport') }}
                </el-button>
              </el-checkbox-group>
            </FormItem>
          </div>
        </el-card>
        <FormItem :label="$t('pages.matchType')">
          <el-radio-group v-model="temp.caseSensitive">
            <el-radio v-for="item in caseSensitiveOptions" :key="item.value" :label="item.value" :disabled="!formable">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="false" type="primary" @click="handleModify">
          {{ $t('button.edit') }}
        </el-button>
        <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
  </div>
</template>

<script>
import { getRuleByName, createRule, updateRule } from '@/api/contentStrategy/rule/fileAttribute'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'
import { nameRegExp } from '../ruleRegExp'

export default {
  name: 'FileAttributeDialog',
  components: { FileSuffixLibImport },
  data() {
    return {
      fileSuffixOptions: [
        { value: 1, label: 'doc,docx' },
        { value: 2, label: 'xls,xlsx' },
        { value: 3, label: 'ppt,pptx' },
        { value: 4, label: 'txt' },
        { value: 5, label: 'pdf' },
        { value: 6, label: 'zip' },
        { value: 0, label: this.$t('pages.others') }
      ],
      fileSuffixChecked: [],
      fileSuffixOther: '',
      exceptFileSuffixChecked: [],
      exceptFileSuffixOther: '',
      deleteable: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        fileName: '',
        fileSuffix: '',
        fileAuthor: '',
        lastModifier: '',
        minSize: undefined,
        maxSize: undefined,
        minCreateDate: undefined,
        maxCreateDate: undefined,
        minUpdateDate: undefined,
        maxUpdateDate: undefined,
        isEncode: false,
        exceptFileName: '',
        exceptFileSuffix: '',
        caseSensitive: false
      },
      isDoDrip: false, // 零星检测勾选时，隐藏例外条件和匹配计数
      caseSensitiveOptions: [
        { value: false, label: this.$t('pages.caseSensitive3') },
        { value: true, label: this.$t('pages.caseSensitive4') }
      ],
      checkTimesType: 1,
      checkTimesEditable: false,
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        fileSize: [{ validator: this.sizeValidator }],
        includeFilter: [{ validator: this.includeFilterValidator }]
      },
      downloadLoading: false,
      isRuleUpdate: false,
      formable: true,
      textMap: {
        update: this.i18nConcatText(this.$t('pages.fileRule'), 'update'),
        create: this.i18nConcatText(this.$t('pages.fileRule'), 'create'),
        view: this.i18nConcatText(this.$t('pages.fileRule'), 'details')
      },
      importFileSuffixType: '',
      suffixMaxLength: 2048
    }
  },
  computed: {
    gridTable() {
      return this.$parent.ruleTable
    },
    fileSuffixOtherEditable() {
      return this.fileSuffixChecked.indexOf(0) > -1
    },
    exceptFileSuffixOtherEditable() {
      return this.exceptFileSuffixChecked.indexOf(0) > -1
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    closeFunc() {
      const data = this.isRuleUpdate ? 'G1' : ''
      const id = this.ruleId
      this.$emit('closed', data, id)
      this.isRuleUpdate = false
      this.formable = true
      this.ruleId = ''
    },
    checkboxToFileSuffix() {
      const fileSuffix = []
      const exceptFileSuffix = []
      for (let i = 0, size = this.fileSuffixOptions.length; i < size; i++) {
        const id = this.fileSuffixOptions[i].value
        if (this.fileSuffixChecked.indexOf(id) >= 0) {
          if (id > 0) {
            fileSuffix.push(this.fileSuffixOptions[i].label)
          } else if (this.fileSuffixOther) {
            this.fileSuffixOther = this.fileSuffixOther.replaceAll('，', ',').replace(/(^,+|,+$)/g, '').replaceAll(/,{2,}/g, ',')
            fileSuffix.push(this.fileSuffixOther)
          }
        }
        if (this.exceptFileSuffixChecked.indexOf(id) >= 0) {
          if (id > 0) {
            exceptFileSuffix.push(this.fileSuffixOptions[i].label)
          } else if (this.exceptFileSuffixOther) {
            this.exceptFileSuffixOther = this.exceptFileSuffixOther.replaceAll('，', ',').replace(/(^,+|,+$)/g, '').replaceAll(/,{2,}/g, ',')
            exceptFileSuffix.push(this.exceptFileSuffixOther)
          }
        }
      }
      this.temp.fileSuffix = fileSuffix.join(',')
      this.temp.exceptFileSuffix = exceptFileSuffix.join(',')
    },
    fileSuffixToCheckbox() {
      const rowData = Object.assign({}, this.temp)
      for (let i = 0, size = this.fileSuffixOptions.length; i < size; i++) {
        const option = this.fileSuffixOptions[i]
        if (rowData.fileSuffix) {
          const startIndex = rowData.fileSuffix.indexOf(option.label)
          if (startIndex >= 0) {
            rowData.fileSuffix = rowData.fileSuffix.replace(option.label, '')
            this.fileSuffixChecked.push(option.value)
          }
        }
        if (rowData.exceptFileSuffix) {
          const startIndex = rowData.exceptFileSuffix.indexOf(option.label)
          if (startIndex >= 0) {
            rowData.exceptFileSuffix = rowData.exceptFileSuffix.replace(option.label, '')
            this.exceptFileSuffixChecked.push(option.value)
          }
        }
      }
      if (rowData.fileSuffix) this.fileSuffixOther = rowData.fileSuffix.replace(/(^,+|,+$)/g, '').replace(/,{2,}/g, ',')
      if (rowData.exceptFileSuffix) this.exceptFileSuffixOther = rowData.exceptFileSuffix.replace(/(^,+|,+$)/g, '').replace(/,{2,}/g, ',')
      if (this.fileSuffixOther) this.fileSuffixChecked.push(0)
      if (this.exceptFileSuffixOther) this.exceptFileSuffixChecked.push(0)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    createDateChange: function(index) {
      if (this.temp.minCreateDate && this.temp.maxCreateDate && this.temp.minCreateDate >= this.temp.maxCreateDate) {
        if (index === 1) {
          this.temp.minCreateDate = undefined
          this.$message({ message: this.$t('pages.fileTimeValid1'), type: 'error', duration: 2000 })
        } else {
          this.temp.maxCreateDate = undefined
          this.$message({ message: this.$t('pages.fileTimeValid2'), type: 'error', duration: 2000 })
        }
      }
    },
    updateDateChange: function(index) {
      if (this.temp.minUpdateDate && this.temp.maxUpdateDate && this.temp.minUpdateDate >= this.temp.maxUpdateDate) {
        if (index === 1) {
          this.temp.minUpdateDate = undefined
          this.$message({ message: this.$t('pages.fileTimeValid1'), type: 'error', duration: 2000 })
        } else {
          this.temp.maxUpdateDate = undefined
          this.$message({ message: this.$t('pages.fileTimeValid2'), type: 'error', duration: 2000 })
        }
      }
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.fileSuffixOther = ''
      this.exceptFileSuffixOther = ''
      this.fileSuffixChecked = []
      this.exceptFileSuffixChecked = []
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, this.temp, row) // copy obj
      this.fileSuffixToCheckbox()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleView(row, doDrip) {
      if (doDrip) {
        this.isDoDrip = true
      } else {
        this.isDoDrip = false
      }
      this.formable = false
      this.handleUpdate(row)
      this.dialogStatus = 'view'
    },
    handleModify() {
      this.dialogStatus = 'update'
      this.formable = !this.formable
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.checkboxToFileSuffix()
          createRule(this.temp).then(respond => {
            this.submitting = false
            this.isRuleUpdate = true
            this.ruleId = respond.data.id
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.checkboxToFileSuffix()
          const tempData = Object.assign({}, this.temp)
          updateRule(tempData).then(respond => {
            this.submitting = false
            this.isRuleUpdate = true
            this.ruleId = respond.data.id
            this.dialogFormVisible = false
            this.gridTable.updateRowData(respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      const errorStr = value.match(nameRegExp)
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else if (nameRegExp.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_IllegalCharacter').concat('：').concat(errorStr.join(''))))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    sizeValidator(rule, value, callback) {
      if (this.temp.minSize > this.temp.maxSize) {
        callback(this.$t('pages.file_text5'))
      } else {
        callback()
      }
    },
    includeFilterValidator(rule, value, callback) {
      let isSet = false
      const exceptKeys = ['name', 'id', 'type', 'createTime', 'modifyTime', 'fileSuffix', 'active', 'exceptFileName', 'exceptFileSuffix']
      for (const key in this.temp) {
        if (exceptKeys.indexOf(key) < 0 && this.temp[key]) {
          isSet = true
          break
        }
      }
      if (!isSet && this.fileSuffixChecked.length > 1) {
        isSet = true
      } else if (!isSet && this.fileSuffixChecked.length === 1) {
        if (this.fileSuffixChecked[0] > 0 || this.fileSuffixOther.length > 0) {
          isSet = true
        }
      }
      if (isSet) {
        callback()
      } else {
        callback(this.$t('pages.file_text2'))
      }
    },
    validateIncludeFilter() {
      this.$refs['dataForm'].validateField('includeFilter')
    },
    handleFileSuffixImport(type) {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
      this.importFileSuffixType = type
    },
    duplicateRemoval(nv, ov) {
      const reg = new RegExp('[.]', 'g');
      const new_suffix = nv.replace(reg, '').split('|')
      let old_suffix = null
      let union_suffix = null
      if (ov == null || ov === '') {
        union_suffix = [...new Set(new_suffix)].join(',')
      } else {
        old_suffix = ov.split(',')
        union_suffix = [...new Set(old_suffix.concat(new_suffix))].join(',')
      }
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf(','))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      return union_suffix
    },
    importFileSuffix(suffix) {
      if (this.importFileSuffixType === 'fileSuffix') {
        this.fileSuffixOther = this.duplicateRemoval(suffix, this.fileSuffixOther)
        this.validateIncludeFilter()
      } else if (this.importFileSuffixType === 'exceptFileSuffix') {
        this.exceptFileSuffixOther = this.duplicateRemoval(suffix, this.exceptFileSuffixOther)
      }
    }
  }
}
</script>

<style lang='scss' scoped>
  .required:before{
    content: '*';
    color: #F56C6C;
    margin-right: 4px;
  }
</style>
