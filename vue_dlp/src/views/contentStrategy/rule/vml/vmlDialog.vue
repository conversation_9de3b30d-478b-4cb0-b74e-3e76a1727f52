<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="vmlDialogFormVisible"
    width="800px"
    @dragDialog="handleDrag"
    @closed="closeFunc"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="95px" style="width: 750px;">
      <FormItem :label="$t('pages.ruleName')" prop="name">
        <el-input v-model="temp.name" v-trim maxlength="60" :disabled="!formable" ></el-input>
      </FormItem>
      <FormItem :label="$t('table.deepTraining')">
        <el-switch v-model="temp.deepTraining" :active-value="1" :inactive-value="0" :disabled="!formable" />
        <el-tooltip class="item" effect="dark" placement="bottom-start">
          <div slot="content">{{ $t('pages.vml_Msg14') }}</div>
          <i class="el-icon-info" />
        </el-tooltip>
      </FormItem>
      <el-card class="box-card" :body-style="{padding: '5px 10px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.inclusionConditions') }}</span>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.fileFp_Msg25') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </div>
        <div>
          <el-row :gutter="20">
            <el-col :span="12" style="border-right: 1px solid #ccc;">
              <label>{{ $t('pages.positiveExDocument') }}</label>
              <vml-upload
                ref="uploadProsFile"
                :formable="formable"
                :file-num-limit="fileNumLimit"
                :file-size-limit="fileSizeLimit"
                :up-file-list="prosFiles"
                :check-duplicate="checkProsDuplicate"
                @add="prosFileAdd"
                @remove="prosFileRemove"
                @clear="clearProsFiles"
              />
            </el-col>
            <el-col :span="12">
              <label>{{ $t('pages.negativeExDocument') }}</label>
              <vml-upload
                ref="uploadConsFile"
                :formable="formable"
                :file-num-limit="fileNumLimit"
                :file-size-limit="fileSizeLimit"
                :up-file-list="consFiles"
                :check-duplicate="checkConsDuplicate"
                @add="consFileAdd"
                @remove="consFileRemove"
                @clear="clearConsFiles"
              />
            </el-col>
          </el-row>
        </div>
        <!--<div v-show="dialogStatus=='update' || dialogStatus=='view'">
          <FormItem :label="$t('pages.positiveExDocumentNum')" prop="prosFileNum">
            <el-input v-model="temp.prosFileNum" disabled></el-input>
          </FormItem>
          <FormItem :label="$t('pages.negativeExDocumentNum')" prop="keywords">
            <el-input v-model="temp.consFileNum" disabled></el-input>
          </FormItem>
        </div>-->
      </el-card>
      <el-card class="box-card" :body-style="{padding: '0 5px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.exceptions') }}</span>
        </div>
        <FormItem :label="$t('pages.keywords')" label-width="65px" prop="exceptWords">
          <span>{{ $t('pages.fileFp_Msg9') }}</span>
          <el-input v-model="temp.rule.exceptWords" no-limit type="textarea" maxlength="1024" :placeholder="$t('pages.keyword_text14')" :disabled="!formable" @keydown.native="listenKey($event)"></el-input>
        </FormItem>
      </el-card>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="vmlDialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getRuleById, getRuleByName, createRule, updateRule, uploadFiles } from '@/api/contentStrategy/rule/vml'
import VmlUpload from '@/views/contentStrategy/rule/vml/vmlUpload'
import { nameRegExp } from '../ruleRegExp'

export default {
  name: 'VmlDialog',
  components: { VmlUpload },
  data() {
    return {
      fileNumLimit: 1000,
      fileSizeLimit: 1000 * 1024 * 1024,
      rowTemp: {},
      temp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        prosFileNum: '',
        consFileNum: '',
        sourceDir: '',
        // checkPosition: [],
        state: 0,
        trainCostTime: '',
        trainTime: '',
        matchRate: '',
        rule: {
          file: '',
          exceptWords: '',
          caseSensitive: false
        },
        prosFileList: [],
        consFileList: [],
        deepTraining: 0
      },
      checkTimesType: 1,
      checkTimesEditable: false,
      vmlDialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.vmlRule'), 'update'),
        create: this.i18nConcatText(this.$t('pages.vmlRule'), 'create'),
        view: this.i18nConcatText(this.$t('pages.vmlRule'), 'details')
      },
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        exceptWords: [{ trigger: 'blur', validator: this.contentValidator }]
      },
      prosFiles: [],
      consFiles: [],
      toDeleteFiles: [],
      submitting: false,
      isRuleUpdate: false,
      formable: true,
      fileChange: false // 文件是否发生变更
    }
  },
  computed: {
    gridTable() {
      return this.$parent.vmlTable
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.vmlDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        const ProsFile = this.$refs['uploadProsFile']
        const ConsFile = this.$refs['uploadConsFile']
        ProsFile && ProsFile.clearFiles()
        ConsFile && ConsFile.clearFiles()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, JSON.parse(JSON.stringify(row))) // copy obj
      // 用于保存时对比数据是否变更
      this.rowTemp = Object.assign({}, this.temp)
      this.dialogStatus = 'update'
      this.vmlDialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        const ProsFile = this.$refs['uploadProsFile']
        const ConsFile = this.$refs['uploadConsFile']
        ProsFile && ProsFile.clearFiles()
        ConsFile && ConsFile.clearFiles()
        this.temp.prosFileList.forEach(item => {
          this.prosFiles.push({ name: item })
        })
        this.temp.consFileList.forEach(item => {
          this.consFiles.push({ name: item })
        })
      })
    },
    checkDuplicate(file, fileList) {
      for (let i = 0; i < fileList.length; i++) {
        if (file.name === fileList[i].name) {
          return true
        }
      }
      return false
    },
    checkProsDuplicate(file) {
      return this.checkDuplicate(file, this.consFiles)
    },
    checkConsDuplicate(file) {
      return this.checkDuplicate(file, this.prosFiles)
    },
    prosFileAdd(files, fileList) {
      this.prosFiles = fileList
      this.temp.prosFileNum = fileList.length
    },
    consFileAdd(files, fileList) {
      this.consFiles = fileList
      this.temp.consFileNum = fileList.length
    },
    prosFileRemove(file, fileList) {
      this.prosFiles = fileList
      this.temp.prosFileNum = fileList.length
      this.toDeleteFiles.push(file.name)
    },
    consFileRemove(file, fileList) {
      this.consFiles = fileList
      this.temp.consFileNum = fileList.length
      this.toDeleteFiles.push(file.name)
    },
    clearProsFiles(e) {
      this.prosFiles.forEach(file => {
        this.toDeleteFiles.push(file.name)
      })
      this.prosFiles = []
      this.temp.prosFileNum = 0
    },
    clearConsFiles(e) {
      this.consFiles.forEach(file => {
        this.toDeleteFiles.push(file.name)
      })
      this.consFiles = []
      this.temp.consFileNum = 0
    },
    listenKey(e) {
      if (e.keyCode === 13) {
        e.preventDefault() // 阻止浏览器默认换行操作
        return false
      }
    },
    closeFunc() {
      const data = this.isRuleUpdate ? 'G3' : ''
      const id = this.ruleId
      this.$emit('vmlClosed', data, id)
      this.isRuleUpdate = false
      this.formable = true
      this.ruleId = ''
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.prosFiles = []
      this.consFiles = []
      this.toDeleteFiles = []
      this.fileChange = false
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleView(row) {
      this.formable = false
      getRuleById(row.id).then(respond => {
        this.handleUpdate(respond.data)
        this.dialogStatus = 'view'
      })
    },
    formatFileNum() {
      this.temp.prosFileNum = this.prosFiles.length
      this.temp.consFileNum = this.consFiles.length
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.prosFiles || this.prosFiles.length == 0) {
            this.$message({
              message: this.$t('pages.vml_Msg5'),
              type: 'error'
            })
            this.submitting = false
          } else if (!this.consFiles || this.consFiles.length == 0) {
            this.$message({
              message: this.$t('pages.vml_Msg6'),
              type: 'error'
            })
            this.submitting = false
          } else {
            this.formatKeyWord();
            this.formatFileNum()
            const formData = this.toFormData(this.temp)
            formData.append('toDeleteFiles[]', null)
            this.prosFiles.forEach(file => {
              if (file.raw) {
                formData.append('prosFiles[]', file.raw)
              }
            })
            this.consFiles.forEach(file => {
              if (file.raw) {
                formData.append('consFiles[]', file.raw)
              }
            })
            uploadFiles(formData).then(respond => {
              this.temp.sourceDir = respond.data.sourceDir
              createRule(this.temp).then(respond => {
                this.submitting = false
                this.vmlDialogFormVisible = false
                this.temp.id = respond.data.id
                this.gridTable.execRowDataApi()
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.createSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(reason => {
                this.submitting = false
              })
            }).catch(reason => {
              this.submitting = false
            })
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.prosFiles || this.prosFiles.length == 0) {
            this.$message({
              message: this.$t('pages.vml_Msg5'),
              type: 'error'
            })
            this.submitting = false
          } else if (!this.consFiles || this.consFiles.length == 0) {
            this.$message({
              message: this.$t('pages.vml_Msg6'),
              type: 'error'
            })
            this.submitting = false
          } else {
            this.formatKeyWord()
            this.formatFileNum()
            // 清空冗余数据，避免请求参数超过限定长度，目前后端接收的请求参数长度限定10000
            this.temp.prosFileList.splice(0)
            this.temp.consFileList.splice(0)
            const formData = this.toFormData(this.temp)
            let i = 0
            if (this.toDeleteFiles.length > 0) {
              this.fileChange = true
              formData.append('toDeleteFiles[]', this.toDeleteFiles)
              i++
            } else {
              formData.append('toDeleteFiles[]', null)
            }
            this.prosFiles.forEach(file => {
              if (file.raw) {
                this.fileChange = true
                formData.append('prosFiles[]', file.raw)
                i++
              }
            })
            this.consFiles.forEach(file => {
              if (file.raw) {
                this.fileChange = true
                formData.append('consFiles[]', file.raw)
                i++
              }
            })
            if (i) {
              uploadFiles(formData).then(respond => {
                this.updateRule()
              }).catch(reason => {
                this.submitting = false
              })
            } else {
              this.updateRule()
            }
          }
        } else {
          this.submitting = false
        }
      })
    },
    updateRule() {
      const tempData = Object.assign({}, this.temp)
      updateRule(tempData).then(respond => {
        this.submitting = false
        this.vmlDialogFormVisible = false
        this.gridTable.execRowDataApi()
        this.$notify({
          title: this.$t('text.success'),
          message: this.isObjEqual(tempData, this.rowTemp) ? this.$t('text.updateSuccess') : this.$t('pages.edm_Msg17'),
          type: 'success',
          duration: 2000
        })
      }).catch(reason => {
        this.submitting = false
      })
    },
    isObjEqual(o1, o2) {
      if (this.fileChange || o1.deepTraining !== o2.deepTraining) {
        return false
      }
      return true;
    },
    nameValidator(rule, value, callback) {
      const errorStr = value.match(nameRegExp)
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } else if (nameRegExp.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_IllegalCharacter').concat('：').concat(errorStr.join(''))))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    contentValidator(rule, value, callback) {
      value = this.temp.rule.exceptWords
      if (value && value.indexOf('\\n') > -1) {
        callback(new Error(this.$t('pages.fileFp_Msg12')))
      } else if (value && value.indexOf('，') > -1) {
        callback(new Error(this.$t('pages.fileFp_Msg13')))
      } else {
        const keywords = value.split(',')
        if (keywords.length > 50) {
          callback(new Error(this.$t('pages.keyword_text14')))
        }
        const maxLength = keywords.reduce((max, keyword) => Math.max(max, keyword.length), 0)
        // 单个关键字不能超过256个字符。
        if (maxLength > 256) {
          callback(new Error(this.$t('pages.keyword_text13')))
        }
        callback()
      }
    },
    ruleNameValidator(vl) {
      return vl.replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g, '')
    },
    // 格式化关键字（主要为了去除连续输入的","和"-"，例如：1,2,,,,,3，去除后：1,2,3）
    formatKeyWord() {
      const exceptWords = this.temp.rule.exceptWords;
      this.temp.rule.exceptWords = exceptWords.replaceAll(/,{2,}/g, ',').replaceAll(/-{2,}/g, '-');
    }
  }
}
</script>
