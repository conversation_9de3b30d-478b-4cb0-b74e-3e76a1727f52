<template>
  <el-upload
    ref="upload"
    class="vml-upload"
    action=""
    accept=".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt"
    multiple
    :limit="-1"
    :file-list="upFileList"
    :auto-upload="false"
    :disabled="!formable"
    :on-remove="handleRemove"
    :on-exceed="handleExceed"
  >
    <el-tooltip v-show="formable" class="item" effect="dark" :content="$t('pages.vml_Msg7')" placement="right-start">
      <el-button v-show="formable" size="small" type="primary">{{ $t('pages.selectFile') }}</el-button>
    </el-tooltip>
    <i v-show="upFileList.length > 0 && formable" class="el-icon-delete-solid" :title="$t('pages.processStgLib_Msg76')" @click="clearFiles"></i>
    <div v-show="formable" slot="tip" class="el-upload__tip">{{ $t('pages.fileFp_Msg25') }}</div>
  </el-upload>
</template>

<script>
// import { isUtf8File } from '@/utils/utf8'

export default {
  name: 'VmlUpload',
  props: {
    formable: { type: Boolean, default: true },                              // 是否可编辑状态
    fileNumLimit: {
      type: Number,
      default: undefined
    },
    fileSizeLimit: {
      type: Number,
      default: undefined
    },
    checkDuplicate: {
      type: Function,
      default(file) {
        return false
      }
    },
    upFileList: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      fileList: [],
      tempIndex: 1
    }
  },
  computed: {
    allFileSize() {
      if (this.fileList.length === 0) {
        return 0
      }
      return this.fileList.map(file => file.size).reduce((a, b) => a + b)
    }
  },
  methods: {
    errorTips(msg) {
      this.$message({
        message: msg,
        type: 'error',
        duration: 2000
      })
    },
    wrapFile(rawFile) {
      rawFile.uid = Date.now() + this.tempIndex++;
      return {
        status: 'ready',
        name: rawFile.name,
        size: rawFile.size,
        percentage: 0,
        uid: rawFile.uid,
        raw: rawFile
      };
    },
    isSameFile(file) {
      for (let i = 0; i < this.fileList.length; i++) {
        if (file.name === this.fileList[i].name) {
          return true
        }
      }
      return false
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.$emit('remove', file, fileList)
    },
    async handleExceed(files, fileList) {
      if (this.fileNumLimit && fileList.length + files.length > this.fileNumLimit) {
        this.errorTips(this.$t('pages.vml_Msg8', { limit: this.fileNumLimit, new: files.length, total: files.length + fileList.length }));
        return;
      }
      const addFileList = []
      let addSize = 0
      for (let i = 0; i < files.length; i++) {
        const rawFile = files[i]
        if (this.isSameFile(rawFile) || this.checkDuplicate(rawFile)) {
          this.errorTips(this.$t('pages.vml_Msg11', { fileName: rawFile.name }))
          return;
        }
        addSize += rawFile.size
        if (this.allFileSize + addSize > this.fileSizeLimit) {
          this.errorTips(this.$t('pages.vml_Msg12'))
          return;
        }
        addFileList.push(this.wrapFile(rawFile))
      }
      fileList.push(...addFileList)
      this.fileList = fileList
      this.$emit('add', addFileList, fileList)
    },
    clearFiles(e) {
      window.event ? window.event.cancelBubble = true : e.stopPropagation()
      this.fileList.splice(0)
      // this.upFileList.splice(0)
      this.$refs.upload.clearFiles()
      this.$emit('clear', e)
    }
  }
}
</script>

<style lang="scss" scoped>
  .vml-upload>>>.el-upload-list{
    min-height: 100px;
    max-height: 188px;
    overflow: auto;
    margin-top: 2px;
    .el-upload-list__item:hover {
      background-color: #dbdbdb;
    }
  }
  .el-icon-delete-solid{
    margin-left: 20px;
    font-size: 16px;
    &:hover{
      color: rgb(12, 123, 226);
    }
  }
</style>
