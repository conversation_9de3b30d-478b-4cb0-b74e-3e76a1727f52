<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.add') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <el-button icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleTrain">
          {{ $t('pages.startCollection') }}
        </el-button>
        <el-button icon="el-icon-edit" size="mini" :disabled="!deleteable" @click="handleCancelTrain">
          {{ $t('pages.stopCollection') }}
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="vmlRuleList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd">
        <template slot="popoverContent" slot-scope="props">
          <ul class="detail-list">
            <li v-for="(item, index) in props.detail" :key="index">{{ item }}</li>
          </ul>
        </template>
      </grid-table>
    </div>

    <vml-dialog ref="vmlDialog" />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="$t('text.batchDelete', {info: $t('route.vml')})"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :filter-name-desc="$t('route.vml')"
      :edit-type="1"
      @submitEnd="batchDeleteFunc"
    />

    <rule-check-dlg
      ref="checkDlg"
      :title="textMap[dialogStatus]"
      :result-options="resultOptions"
      :upload-file-api="uploadFile"
      :check-file-api="checkFile"
      :check-result-api="getCheckResult"
    />
    <fail-train-detail-dlg
      ref="failTrainDetailDlg"
      :rule-ids="ruleIds"
      :rule-type="ruleType"
    />
  </div>
</template>

<script>
import {
  cancelTrainVml,
  checkFile,
  deleteRule,
  getCheckResult,
  getRulePage,
  trainVml,
  uploadFile
} from '@/api/contentStrategy/rule/vml'
import { getCollectorResultDict } from '@/utils/dictionary'
import VmlDialog from './vmlDialog'
import RuleCheckDlg from '@/views/contentStrategy/rule/RuleCheckDlg'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'
import FailTrainDetailDlg from '@/views/contentStrategy/rule/FailTrainDetailDlg'

export default {
  name: 'VmlRule',
  components: { VmlDialog, RuleCheckDlg, BatchEditPageDlg, FailTrainDetailDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'ruleName', width: '150', fixed: true },
        { prop: 'state', label: 'acquisitionStatus', width: '100', formatter: this.stateFormatter },
        { prop: 'trainCostTime', label: 'acquisitionTime', width: '100', formatter: this.trainCostTimeFormatter },
        { prop: 'trainTime', label: 'latestAcquisitionTime', width: '150' },
        { prop: 'prosFileNum', type: 'popover', childData: 'prosFileList', label: 'positiveExDocument', width: '100' },
        { prop: 'consFileNum', type: 'popover', childData: 'consFileList', label: 'negativeExDocument', width: '100' },
        { prop: 'rule.exceptWords', label: 'exceptionKeyword', width: '150' },
        // { prop: 'checkPosition', label: '匹配位置', width: '100', formatter: this.checkPositionFormatter },
        { label: 'operate', type: 'button', width: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate },
            { label: 'testing', isShow: (row) => row.state === 2, click: this.handleCheckFile }
          ]
        }
      ],
      stateOptions: { 0: this.$t('pages.notCollected'), 1: this.$t('pages.collecting'), 2: this.$t('pages.completeAcquisition'), 3: this.$t('pages.collectionFailed') },
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      deleteable: false,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.vmlRule'), 'update'),
        create: this.i18nConcatText(this.$t('pages.vmlRule'), 'create'),
        check: this.$t('pages.vml_Msg2')
      },
      resultOptions: {
        '0': this.$t('pages.vml_Msg3'),
        '1': this.$t('pages.vml_Msg4')
      },
      collectorResultOption: getCollectorResultDict(),
      ruleIds: null,
      ruleType: 5 // 类型值默认为5
    }
  },
  computed: {
    vmlTable() {
      return this.$refs['vmlRuleList']
    }
  },
  created() {
    this.$socket.subscribe({ url: '/topic/updateStatus', callback: (resp, handle) => {
      if (resp.data == 'train_vml') {
        this.handleRefresh()
      }
    } })
  },
  methods: {
    uploadFile,
    checkFile,
    getCheckResult,
    listenKey(e) {
      if (e.keyCode === 13) {
        e.preventDefault() // 阻止浏览器默认换行操作
        return false
      }
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRulePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.vmlTable.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.vmlDialog.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.vmlDialog.handleUpdate(row)
    },
    handleDelete() {
      const selectedDatas = this.vmlTable.getSelectedDatas()
      const total = this.vmlTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedDatas, total, this.query)
    },
    handleTrain() {
      const toTrainIds = this.vmlTable.getSelectedIds()
      trainVml({ ids: toTrainIds.join(',') }).then(respond => {
        this.vmlTable.execRowDataApi(this.query)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.fileFp_Msg5'),
          type: 'success',
          duration: 2000
        })
        if (respond.data && respond.data.length > 0) {
          this.ruleIds = respond.data
          this.$refs['failTrainDetailDlg'].show()
        }
      })
    },
    handleCancelTrain() {
      this.$confirmBox(this.$t('pages.fileFp_Msg6'), this.$t('text.prompt')).then(() => {
        const toCancelIds = this.vmlTable.getSelectedIds()
        cancelTrainVml({ ids: toCancelIds.join(',') }).then(respond => {
          this.vmlTable.execRowDataApi(this.query)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.fileFp_Msg7'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleCheckFile(row) {
      this.dialogStatus = 'check'
      this.$refs['checkDlg'].show(row)
    },
    handleRefresh() {
      this.vmlTable.execRowDataApi(this.query)
    },
    stateFormatter(row, data) {
      if (data === 3 && row.result) {
        return this.collectorResultOption[row.result] ? this.collectorResultOption[row.result] : this.collectorResultOption[40000]
      }
      return this.stateOptions[data]
    },
    trainCostTimeFormatter(row, data) {
      return !!data || data === 0 ? data + `${this.$store.getters.language === 'en' ? ' ' : ''}` + this.$t('text.second') : ''
    },
    batchDeleteFunc(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.vmlTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch((e) => { callback(e) })
      }).catch((e) => { callback(e) })
    }
  }
}
</script>

<style lang="scss" scoped>
  .upload-demo>>>.el-upload-list{
    min-height: 100px;
    max-height: 200px;
    overflow: auto;
    margin-top: 2px;
    .el-upload-list__item:hover {
      background-color: #dbdbdb;
    }
  }
  .el-icon-delete-solid{
    margin-left: 20px;
    font-size: 16px;
    &:hover{
      color: rgb(12, 123, 226);
    }
  }
  .detail-list {
    max-height: 300px;
    max-width: 300px;
    padding: 0 10px;
    overflow: auto;
    list-style: none;
    li {
      padding: 5px 20px 5px 10px;
      cursor: default;
      &:hover {
        background: #eee;
      }
    }
  }
</style>
