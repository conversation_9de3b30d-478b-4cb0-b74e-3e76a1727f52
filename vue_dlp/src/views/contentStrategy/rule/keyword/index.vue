<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.addRule') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.alarmSetup_text1')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="keywordRuleList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <keyword-dialog ref="keywordDialog" />
    <batch-edit-page-dlg
      ref="batchDeleteDlg"
      :height="380"
      :title="$t('text.batchDelete', {info: $t('route.keyword')})"
      :col-model="colModel"
      :row-data-api="rowDataApi"
      :filter-name-desc="$t('route.keyword')"
      :edit-type="1"
      @submitEnd="batchDeleteFunc"
    />
  </div>
</template>

<script>
import { getRulePage, deleteRule } from '@/api/contentStrategy/rule/keyword'
import KeywordDialog from './keywordDialog'
import BatchEditPageDlg from '@/views/common/batchEditPageDlg'

export default {
  name: 'KeywordRule',
  components: { KeywordDialog, BatchEditPageDlg },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'ruleName', width: '150', fixed: true },
        { prop: 'keywords', label: 'keywords', width: '150' },
        { prop: 'dbkeywords', label: 'dbkeywords', width: '150' },
        { prop: 'exceptWords', label: 'exceptWords', width: '150' },
        { prop: 'caseSensitive', label: 'caseSensitive', width: '150', formatter: this.caseSensitiveFormatter },
        { prop: 'checkTimes', label: 'checkTimes', width: '150', formatter: this.checkTimesFormatter },
        // { prop: 'checkPosition', label: '匹配位置', width: '100', formatter: this.checkPositionFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      deleteable: false
    }
  },
  computed: {
    keywordTable() {
      return this.$refs['keywordRuleList']
    }
  },
  created() {
  },
  methods: {
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getRulePage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas.length > 0
    },
    handleFilter() {
      this.query.page = 1
      this.keywordTable.execRowDataApi()
    },
    handleCreate() {
      this.$refs.keywordDialog.handleCreate()
    },
    handleUpdate(row) {
      this.$refs.keywordDialog.handleUpdate(row)
    },
    handleDelete() {
      const selectedDatas = this.keywordTable.getSelectedDatas()
      const total = this.keywordTable.getTotal()
      this.$refs['batchDeleteDlg'].show(selectedDatas, total, this.query)
    },
    checkPositionFormatter(row, data) {
      return this.$refs.keywordDialog.checkPositionFormatter(row, data)
    },
    caseSensitiveFormatter(row, data) {
      return data ? this.$t('text.yes') : this.$t('text.no')
    },
    checkTimesFormatter(row, data) {
      return this.$refs.keywordDialog.checkTimesFormatter(row, data)
    },
    batchDeleteFunc(params, callback) {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        deleteRule(params).then(respond => {
          this.keywordTable.execRowDataApi(this.query)
          if (respond.data.ids.length !== 0) {
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          } else {
            this.$notify({
              title: this.$t('text.prompt'),
              message: this.$t('text.deleteSuccess2'),
              type: 'success',
              duration: 2000
            })
          }
          callback(respond)
        }).catch((e) => { callback(e) })
      }).catch((e) => { callback(e) })
    }
  }
}
</script>
