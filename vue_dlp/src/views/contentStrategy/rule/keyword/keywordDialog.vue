<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="textMap[dialogStatus]"
    :visible.sync="dialogFormVisible"
    width="800px"
    @dragDialog="handleDrag"
    @closed="closeFunc"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="95px" style="width: 700px;">
      <FormItem :label="$t('table.ruleName')" prop="name">
        <el-input v-model="temp.name" v-trim maxlength="60" :disabled="!formable"></el-input>
      </FormItem>
      <el-card class="box-card" :body-style="{padding: '0 5px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.keyword_text1') }}</span>
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.keyword_text2') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </div>
        <FormItem :label="$t('table.keyword')" prop="keywords">
          <span>{{ $t('pages.keyword_text3') }}</span>
          <el-input v-model="temp.keywords" type="textarea" maxlength="1024" :placeholder="$t('pages.keyword_text14')" :disabled="!formable" @keydown.native="listenKey($event, temp.keywords)"></el-input>
        </FormItem>
        <FormItem :label="$t('table.dbkeywords')" prop="dbkeywords">
          <span>{{ $t('pages.keyword_text4') }}</span>
          <el-button v-if="formable" type="primary" icon="el-icon-plus" style="position: absolute;right: 12px;" @click="addDbkeywordRow"></el-button>
          <div v-for="(item, index) in dbkeywordRows" :key="index">
            <el-row>
              <el-col :span="7"><el-input v-model="item[0]" :placeholder="$t('pages.uninstallManage_text1')" maxlength="256" :disabled="!formable" @input="item[0] = item[0].replace(/[,-]/g,'')"></el-input></el-col>
              <el-col :span="1" style="text-align: center;">-</el-col>
              <el-col :span="7"><el-input v-model="item[1]" :placeholder="$t('pages.uninstallManage_text1')" maxlength="256" :disabled="!formable" @input="item[1] = item[1].replace(/[,-]/g,'')"></el-input></el-col>
              <el-col :span="1" style="text-align: center;">-</el-col>
              <el-col :span="6"><el-input v-model="item[2]" :placeholder="$t('pages.keyword_text5')" maxlength="" :disabled="!formable" :min="1" :max="99" @input="spacingInput(arguments[0], index, 1, 99)"></el-input></el-col>
              <el-col :span="2" style="text-align: center;">
                <el-button v-if="formable" type="primary" icon="el-icon-delete" @click="deleteDbkeywordRow(index)"></el-button>
              </el-col>
            </el-row>
          </div>
        </FormItem>
      </el-card>
      <el-card v-show="!isDoDrip" class="box-card" :body-style="{padding: '0 5px'}">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.excepeCondition') }}</span>
        </div>
        <FormItem :label="$t('table.keyword')" prop="exceptWords">
          <span>{{ $t('pages.keyword_text3') }}</span>
          <el-input v-model="temp.exceptWords" type="textarea" maxlength="1024" :placeholder="$t('pages.keyword_text14')" :disabled="!formable" @keydown.native="listenKey($event, temp.exceptWords)"></el-input>
        </FormItem>
      </el-card>
      <FormItem :label="$t('pages.matchType')" prop="caseSensitive">
        <el-radio-group v-model="temp.caseSensitive">
          <el-radio v-for="item in caseSensitiveOptions" :key="item.value" :label="item.value" :disabled="!formable">
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem v-show="!isDoDrip" :label="$t('table.checkTimes')" prop="checkTimes">
        <el-radio-group v-model="checkTimesType" @change="checkTimesTypeChange">
          <el-radio v-for="item in checkTimesTypeOptions" :key="item.value" :label="item.value" :disabled="!formable">
            {{ item.labelStart }}
            <el-input v-show="item.input" v-model="temp.checkTimes" :disabled="!formable || !checkTimesEditable" style="width: 57px;" maxlength="3" @input="temp.checkTimes=temp.checkTimes.replace(/^(0+)|[^\d]+/g,'')"></el-input>
            {{ item.labelEnd }}
          </el-radio>
        </el-radio-group>
      </FormItem>
      <!--<FormItem label="匹配位置" prop="checkPosition">
        <el-checkbox-group v-model="temp.checkPosition">
          <el-checkbox v-for="item in checkPositionOptions" :key="item.value" :label="item.value" :disabled="!formable">{{ item.label }}</el-checkbox>
        </el-checkbox-group>
      </FormItem>-->
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-if="false" type="primary" @click="handleModify">
        {{ $t('button.edit') }}
      </el-button>
      <el-button v-if="formable" type="primary" :loading="submitting" @click="dialogStatus==='create'?createData():updateData()">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="dialogFormVisible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getRuleByName, createRule, updateRule } from '@/api/contentStrategy/rule/keyword'
import { nameRegExp } from '../ruleRegExp'

export default {
  name: 'KeywordDialog',
  data() {
    return {
      checkPositionOptions: [
        { value: 1, label: this.$t('pages.checkPosition1') },
        { value: 2, label: this.$t('pages.checkPosition2') },
        { value: 3, label: this.$t('pages.checkPosition3') },
        { value: 4, label: this.$t('pages.checkPosition4') }
      ],
      checkTimesTypeOptions: [
        { value: 1, labelStart: this.$t('pages.keyword_text6'), labelEnd: '', input: false },
        { value: 2, labelStart: this.$t('pages.keyword_text7'), labelEnd: this.$t('pages.keyword_text8'), input: true }
      ],
      caseSensitiveOptions: [
        { value: false, label: this.$t('pages.caseSensitive1') },
        { value: true, label: this.$t('pages.caseSensitive2') }
      ],
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        keywords: '',
        dbkeywords: '',
        exceptWords: '',
        caseSensitive: false,
        // checkPosition: [],
        checkTimes: 1
      },
      isDoDrip: false, // 零星检测勾选时，隐藏例外条件和匹配计数
      dbkeywordRows: [], // eg: [[key1, key2, range], [key1, key2, range]]
      checkTimesType: 1,
      checkTimesEditable: false,
      dialogFormVisible: false,
      dialogStatus: '',
      submitting: false,
      isRuleUpdate: false,
      formable: true,
      textMap: {
        update: this.$t('pages.keyword_update'),
        create: this.$t('pages.keyword_create'),
        view: this.$t('pages.keyword_view')
      },
      rules: {
        name: [{ required: true, trigger: 'blur', validator: this.nameValidator }],
        keywords: [
          // { required: true, message: '包含条件必填', trigger: 'blur' },
          { trigger: 'blur', validator: this.contentValidator }
        ],
        exceptWords: [{ trigger: 'blur', validator: this.contentValidator }]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$parent.keywordTable
    }
  },
  created() {
    this.resetTemp()
    this.addDbkeywordRow()
  },
  methods: {
    spacingInput(value, index, min, max) {
      if (value) {
        value = value.replace(/[^\d]/g, '')
        this.dbkeywordRows[index][2] = value > max ? max : value < min ? min : parseInt(value)
      }
    },
    listenKey(e) {
      if (e.keyCode === 13) {
        e.preventDefault() // 阻止浏览器默认换行操作
        return false
      }
    },
    closeFunc() {
      const data = this.isRuleUpdate ? 'G2' : ''
      const id = this.ruleId
      this.$emit('keywordClosed', data, id)
      this.isRuleUpdate = false
      this.formable = true
      this.ruleId = ''
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.checkTimesType = 1
    },
    addDbkeywordRow() {
      this.dbkeywordRows.unshift(['', '', ''])
    },
    deleteDbkeywordRow(index) {
      this.dbkeywordRows.splice(index, 1)
      if (this.dbkeywordRows.length === 0) {
        this.addDbkeywordRow()
      }
    },
    formatRowData2Dbkeywords() {
      const result = []
      for (let i = 0, size = this.dbkeywordRows.length; i < size; i++) {
        const data = this.dbkeywordRows[i]
        if (data[0] && data[1] && data[2]) {
          result.push(data.join('-'))
        }
      }
      this.temp.dbkeywords = result.join(',')
    },
    formatDbkeywords2RowData() {
      const array = this.temp.dbkeywords.split(',')
      this.dbkeywordRows.length = 0
      for (let i = 0, size = array.length; i < size; i++) {
        this.dbkeywordRows.push(array[i].split('-'))
      }
    },
    keyValidator(optionKeywords, optionDbkeywordRows) {
      const dbRows = optionDbkeywordRows;
      const dbRowsArr = [];
      let account = 0;
      for (let i = 0; i < dbRows.length; i++) {
        if ((dbRows[i][0] !== undefined && dbRows[i][0] !== '' && (dbRows[i][1] == undefined || dbRows[i][1] == '' || dbRows[i][2] == undefined || dbRows[i][2] == '')) ||
          (dbRows[i][1] !== undefined && dbRows[i][1] !== '' && (dbRows[i][0] == undefined || dbRows[i][0] == '' || dbRows[i][2] == undefined || dbRows[i][2] == '')) ||
          (dbRows[i][2] !== undefined && dbRows[i][2] !== '' && (dbRows[i][1] == undefined || dbRows[i][1] == '' || dbRows[i][0] == undefined || dbRows[i][0] == ''))) {
          this.$message({
            message: this.$t('pages.keyword_text16'),
            type: 'error',
            duration: 2000
          })
          return true
        }
        dbRowsArr.push(dbRows[i]);
      }
      if (optionKeywords == undefined || optionKeywords == '') {
        for (let j = 0; j < dbRowsArr.length; j++) {
          if (dbRowsArr[j][0] == '' || dbRowsArr[j][1] == '' || dbRowsArr[j][2] == '') {
            account++
          }
        }
      }
      if (dbRowsArr.length == account) {
        this.$message({
          message: this.$t('pages.keyword_text2'),
          type: 'error',
          duration: 2000
        })
        return true
      }
      return false
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    handleCreate() {
      this.resetTemp()
      this.dbkeywordRows = [['', '', '']]
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      this.checkTimesTypeChange(this.temp.checkTimes)
      this.formatDbkeywords2RowData()
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleView(row, doDrip) {
      if (doDrip) {
        this.isDoDrip = true
      } else {
        this.isDoDrip = false
      }
      this.formable = false
      this.handleUpdate(row)
      this.dialogStatus = 'view'
    },
    handleModify() {
      this.dialogStatus = 'update'
      this.formable = !this.formable
    },
    createData() {
      if (this.keyValidator(this.temp.keywords, this.dbkeywordRows)) {
        return
      }
      if (this.checkTimesValidator(this.temp)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatRowData2Dbkeywords();
          this.formatKeyWord();
          createRule(this.temp).then(respond => {
            this.submitting = false
            this.isRuleUpdate = true
            this.ruleId = respond.data.id
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      if (this.keyValidator(this.temp.keywords, this.dbkeywordRows)) {
        return
      }
      if (this.checkTimesValidator(this.temp)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.formatRowData2Dbkeywords()
          this.formatKeyWord();
          const tempData = Object.assign({}, this.temp)
          updateRule(tempData).then(respond => {
            this.submitting = false
            this.isRuleUpdate = true
            this.ruleId = respond.data.id
            this.dialogFormVisible = false
            this.gridTable.updateRowData(respond.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    nameValidator(rule, value, callback) {
      const errorStr = value.match(nameRegExp)
      if (!value) {
        callback(new Error(this.$t('pages.validateMsg_enterName')))
      } if (nameRegExp.test(value)) {
        callback(new Error(this.$t('pages.validateMsg_IllegalCharacter').concat('：').concat(errorStr.join(''))))
      } else {
        getRuleByName({ name: value }).then(respond => {
          const user = respond.data
          if (user && user.id !== this.temp.id) {
            callback(new Error(this.$t('valid.sameName')))
          } else {
            callback()
          }
        })
      }
    },
    contentValidator(rule, value, callback) {
      if (value && value.indexOf('\\n') > -1) {
        callback(new Error(this.$t('pages.keyword_text9')))
      } else if (value && value.indexOf('，') > -1) {
        callback(new Error(this.$t('pages.keyword_text10')))
      } else {
        const keywords = value.split(',')
        if (keywords.length > 50) {
          callback(new Error(this.$t('pages.keyword_text14')))
        }
        const maxLength = keywords.reduce((max, keyword) => Math.max(max, keyword.length), 0)
        // 单个关键字不能超过256个字符。
        if (maxLength > 256) {
          callback(new Error(this.$t('pages.keyword_text13')))
        }
        callback()
      }
    },
    checkTimesTypeChange: function(data) {
      if (data > 1) {
        this.checkTimesType = 2
        this.checkTimesEditable = true
        this.temp.checkTimes = data
      } else {
        this.checkTimesType = 1
        this.checkTimesEditable = false
        this.temp.checkTimes = 1
      }
    },
    // checkPositionFormatter(row, data) {
    //   const positionNames = []
    //   for (let i = 0, size = this.checkPositionOptions.length; i < size; i++) {
    //     const id = this.checkPositionOptions[i].value
    //     if (data.indexOf(id) >= 0) {
    //       positionNames.push(this.checkPositionOptions[i].label)
    //     }
    //   }
    //   return positionNames.join(',')
    // },
    checkTimesFormatter(row, data) {
      let option
      if (row.checkTimes === 1) {
        option = this.checkTimesTypeOptions[0]
      } else {
        option = this.checkTimesTypeOptions[1]
      }
      return option.labelStart + (option.input ? ' ' + row.checkTimes + ' ' : '') + option.labelEnd
    },
    checkTimesValidator(temp) {
      const checkTimes = temp.checkTimes;
      const checkTimesType = this.checkTimesType;
      if (2 === checkTimesType && (undefined === checkTimes || '' === checkTimes)) {
        this.$message({
          message: this.$t('pages.keyword_text12'),
          type: 'error',
          duration: 2000
        })
        return true
      }
      return false;
    },
    // 格式化关键字（主要为了去除连续输入的","和"-"，例如：1,2,,,,,3，去除后：1,2,3）
    formatKeyWord() {
      const keywords = this.temp.keywords;
      const exceptWords = this.temp.exceptWords;
      this.temp.keywords = keywords.replaceAll(/,{2,}/g, ',').replaceAll(/-{2,}/g, '-');
      this.temp.exceptWords = exceptWords.replaceAll(/,{2,}/g, ',').replaceAll(/-{2,}/g, '-');
    }
  }

}
</script>

<style lang='scss' scoped>

</style>
