<template>
  <strategy-group ref="strategyGroup" :group-id="3" :stg-type-ops="stgTypeOps" group-name="敏感内容识别策略包"/>
</template>
<script>
import StrategyGroup from '@/views/strategyGroup'

export default {
  name: 'ContentStrategyGroup',
  components: { StrategyGroup },
  data() {
    const stgTypeOps = [
      { number: 22, menuCode: 'F21' },
      { number: 145, menuCode: 'F23' },
      { number: 23, menuCode: 'F12' },
      { number: 24, menuCode: 'F13' }
    ]
    return {
      stgTypeOps: stgTypeOps
    }
  }
}
</script>

