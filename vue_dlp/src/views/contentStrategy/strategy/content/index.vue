<template>
  <div class="app-container">
    <div :class="{ 'content-mask': contentMask }"></div>
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button v-if="treeable" type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <!-- 与策略配置中是否有生效对象有关 -->
        <strategy-extend v-if="query.strategyDefType === 0" ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="treeable" icon="el-icon-check" size="mini" :disabled="!deleteable" @click="handleStatus(true)">
          {{ $t('pages.keyToEnable') }}
        </el-button>
        <el-button v-if="treeable" icon="el-icon-close" size="mini" :disabled="!deleteable" @click="handleStatus(false)">
          {{ $t('pages.keyToDisable') }}
        </el-button>
        <el-button v-if="importAble" v-permission="'521'" icon="el-icon-upload2" size="mini" @click="handleImportFile">{{ $t('button.import') }}</el-button>
        <common-downloader
          v-if="!dripAble && exportAble"
          v-permission="'520'"
          :disabled="!deleteable"
          suffix=".ldi"
          :button-name="$t('button.export')"
          button-size="mini"
          @download="handleExport"
        />
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="stgDialogVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ textMap[dialogStatus] }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            <span v-if="respondAble">{{ $t('pages.content_text1', { menuTitle: menuTitle }) }}</span>
            <span v-if="!respondAble">{{ $t('pages.content_text8', { menuTitle: menuTitle }) }}</span>
            <br/><br/>
            {{ $t('pages.content_text2') }}<br/>
            {{ $t('pages.content_text3') }}<br/><br/>
            <!-- {{ $t('pages.content_text4') }}<br/>
            {{ $t('pages.content_text5') }}<br/><br/> -->
            {{ $t('pages.content_text20', { stgType: menuTitle, relStgType: $t(relStgTypeLabel) }) }}<br/>
            {{ $t('pages.content_text21', { stgType: menuTitle, relStgType: $t(relStgTypeLabel) }) }}
            <br/>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 700px;">
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
        />
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.stgName')" prop="name">
              <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('table.severity')" prop="severity">
              <el-select v-model="temp.severity" :disabled="!formable" :placeholder="$t('text.select')">
                <el-option v-for="severity in severityOptions" :key="severity.value" :label="severity.label" :value="severity.value"></el-option>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('table.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="temp.stgTypeNumber != 52 && treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" /><span style="color: #8e8e8e;margin-left: 5px;">{{ $t('pages.content_text10') }}</span>
        </FormItem>
        <el-row>
          <el-col :span="24">
            <FormItem :label="$t('route.group')" :tooltip-content="$t('pages.content_text11', {menuTitle: menuTitle})" prop="ruleGroupList" tooltip-placement="bottom-start">
              <el-popover
                v-show="formable"
                v-model="gShow"
                :disabled="!formable"
                :popper-class="popperClass"
                placement="right"
                width="400"
                trigger="manual"
                @show="groupTreeShow"
                @after-leave="groupTreeHide"
              >
                <tree-menu
                  ref="ruleGroup"
                  style="height: 370px"
                  multiple
                  check-on-click-node
                  :data="ruleGroupTreeDatas"
                  :checked-keys="ruleGroupList"
                  :default-expand-all="true"
                />
                <el-button type="primary" size="mini" style="height: 30px;" class="popover-confirm" @click="gShow = false">{{ $t('button.confirm2') }}</el-button>
                <el-button slot="reference" style="height: 30px;" @click="gShow = true">{{ $t('pages.chooseRuleGroup') }}</el-button>
              </el-popover>
              <el-button v-show="formable" :title=" $t('pages.addRuleGroup')" class="add-btn" @click="addGroup"><svg-icon icon-class="add" /></el-button>

              <el-collapse v-if="ruleGroupRows.length > 0" v-model="activeNames1" @change="handleChange">
                <el-collapse-item v-for="(ruleGroup, index) in ruleGroupRows" :key="index" :name="index">
                  <template slot="title">
                    {{ ruleGroup.name }}<i v-if="formable" class="el-icon-delete rule-group-delete" @click.stop="deleteItem('ruleGroup', index)"></i>
                  </template>
                  <group-form ref="groupForm" :form-datas="ruleGroup" :visit-only="true" :drip-able="dripAble" :drip-change-able="false" @ruleView="handleRuleView" @updateRulesMap="updateRulesMap"/>
                </el-collapse-item>
              </el-collapse>
            </FormItem>
          </el-col>
          <el-col v-if="respondAble" :span="24">
            <FormItem :label="$t('route.respond')" prop="respondRuleList">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">{{ $t('pages.content_text15') }}<br/>{{ $t('pages.content_text16', {menuTitle: menuTitle}) }}</div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-popover
                v-show="formable"
                v-model="rShow"
                :disabled="!formable"
                :popper-class="popperClass"
                placement="right"
                width="400"
                trigger="manual"
                @show="respondTreeShow"
                @after-leave="respondTreeHide"
              >
                <tree-menu
                  ref="respondRule"
                  style="height: 370px"
                  multiple
                  node-key="dataId"
                  check-on-click-node
                  :data="respondRuleOptions"
                  :checked-keys="respondRuleList"
                  :default-expand-all="true"
                />
                <el-button type="primary" size="mini" style="height: 30px;" class="popover-confirm" @click="rShow = false">{{ $t('button.confirm2') }}</el-button>
                <el-button slot="reference" style="height: 30px;" @click="rShow = true">{{ $t('pages.chooseRespond') }}</el-button>
              </el-popover>
              <el-button v-show="formable" :title="$t('pages.addRespond')" class="add-btn" @click="addRespond"><svg-icon icon-class="add" /></el-button>

              <el-collapse v-if="respondRuleRows.length > 0" v-model="activeNames2" @change="handleChange">
                <el-collapse-item v-for="(respond, index) in respondRuleRows" :key="index" :name="index">
                  <template slot="title">
                    {{ respond.name }}<i v-if="formable" class="el-icon-delete rule-group-delete" @click.stop="deleteItem('respondRule', index)"></i>
                  </template>
                  <respond-form ref="respondForm" form-width="540px" :loss-type-options="lossTypeOptions" :form-datas="respond" :visit-only="true" />
                </el-collapse-item>
              </el-collapse>
            </FormItem>
            <!--<FormItem>
              <el-checkbox v-model="temp.stopSendOut">阻止数据的外发</el-checkbox>
              <el-tooltip content="外发数据时，如果检测到敏感，是否需要阻止数据的外发！" effect="dark" placement="bottom-start">
                <i class="el-icon-info" />
              </el-tooltip>
            </FormItem>-->
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'F31'" :link-url="'/contentStrategy/RuleGroup/group'" :btn-text="$t('pages.maintainRuleGroup')"/>
        <link-button v-if="respondAble" btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'F32'" :link-url="'/contentStrategy/RuleGroup/respond'" :btn-text="$t('pages.maintainRespond')"/>
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :get-by-name="getByName"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" :disabled="submitDisabled" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="stgDialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <group-dialog ref="groupDialog" :drip-able="dripAble" :drip-change-able="false" :stg-code="stgCode" @createData="createGroup" @groupClosed="groupClosed" />
    <respond-dialog ref="respondDialog" @createData="createRespond" @respondClosed="respondClosed" />
    <import-stg
      ref="importDlg"
      accept=".ldi"
      :title="$t('pages.importContentStg')"
      data-type="11"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      @success="importSuccess"
    />
  </div>
</template>

<script>
import {
  createDripStrategy,
  createStrategy,
  deleteDripStrategy,
  deleteStrategy,
  exportStg,
  getStrategyByName,
  getStrategyPage,
  importStrategy,
  listRespondRuleTreeNode,
  listRuleGroupTreeNode,
  updateDripStatusBatch,
  updateDripStrategy,
  updateStatusBatch,
  updateStrategy,
  valiRules
} from '@/api/contentStrategy/strategy/content'
import { getRuleByIds as getGroupByIds, getSubRuleByIds } from '@/api/contentStrategy/rule/group'
import { getRuleByIds as getRespondByIds } from '@/api/contentStrategy/rule/respond'
import { getSensitiveLossType } from '@/api/behaviorAuditing/alarmDetailLog'
import {
  buttonFormatter,
  containStgObject,
  enableStgBtn,
  enableStgDelete,
  entityLink,
  hiddenActiveAndEntity,
  initTimestamp,
  isSameTimestamp,
  objectFormatter,
  refreshPage,
  selectable
} from '@/utils'
import { validatePolicy } from '@/utils/validate'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import GroupDialog from '@/views/contentStrategy/rule/group/groupDialog'
import GroupForm from '@/views/contentStrategy/rule/group/groupForm'
import RespondDialog from '@/views/contentStrategy/rule/respond/respondDialog'
import RespondForm from '@/views/contentStrategy/rule/respond/respondForm'
import ImportStg from '@/views/common/importStg'
import CommonDownloader from '@/components/DownloadManager/common'

export default {
  name: 'Content',
  components: { ImportStg, GroupDialog, GroupForm, RespondDialog, RespondForm, CommonDownloader },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    respondAble: { type: Boolean, default: true }, // 是否提供相应规则配置
    activeAble: { type: Boolean, default: true }, // 是否提供启用的配置
    dripAble: { type: Boolean, default: false }, // 是否支持零星检测
    advancedRuleAble: { type: Boolean, default: true }, // 是否支持高级检测规则
    importAble: { type: Boolean, default: true }, // 是否支持导入
    exportAble: { type: Boolean, default: true }, // 是否支持导出
    stgCode: { type: Number, default: 23 }, // 策略编码，stg_base_config的number字段值
    stgTypeLabel: { type: String, default: 'route.contentStg' }, // 内容策略类型名称，例如全盘扫描内容检测策略、文件自检内容检测策略等
    relStgTypeLabel: { type: String, default: 'route.effectiveContentStrategy' }, // 关联策略类型名称，例如敏感内容检测配置、全盘扫描、敏感内容自检、智能加密等
    createStgFunc: { type: Function, default: createStrategy },
    updateStgFunc: { type: Function, default: updateStrategy },
    deleteStgFunc: { type: Function, default: deleteStrategy }
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', fixed: true, fixedWidth: '150', iconFormatter: this.activeFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', hidden: this.$route.query.stgTypeNumber == 52, iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'severity', label: 'severity', width: '100', formatter: this.severityFormatter },
        { prop: 'ruleGroupIds', label: 'ruleGroupIds', width: '200', formatter: this.ruleGroupFormatter },
        { prop: 'respondRuleIds', label: 'respond', width: '200', hidden: !this.respondAble, formatter: this.respondRuleFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, disabledFormatter: this.btnDisableFormatter, click: this.handleUpdate }
          ]
        }
      ],
      severityOptions: [
        { value: 1, label: this.$t('pages.severityOptions1') },
        { value: 2, label: this.$t('pages.severityOptions2') },
        { value: 3, label: this.$t('pages.severityOptions3') },
        { value: 4, label: this.$t('pages.severityOptions4') }
      ],
      ruleGroupOptions: [], // 规则集所有树数据
      ruleGroupTreeDatas: [], // 规则集过滤后的树数据
      checkRules: [], // 各个分类包含的检测规则， [{ 'check': [], 'except': [] }, { 'check': [], 'except': [] }]
      rulesMap: {}, // 规则详情，规则id作为key，规则对象为 value
      respondRuleOptions: [], // 响应规则树数据
      relationMap: { '且': '&', '或': '|', '非': '!', '&': '且', '|': '或', '!': '非' },
      ruleGroupList: [], // 规则集选中的节点id
      ruleGroupRows: [], // 规则集选中的数据
      respondRuleList: [], // 响应规则选中的节点id
      respondRuleRows: [], // 响应规则选中的数据
      activeNames1: [], // 规则集列表展开的集合
      activeNames2: [], // 响应规则列表展开的集合
      lossTypeOptions: [],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        stgTypeNumber: undefined,
        dripScan: undefined
      },
      treeable: true,
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      menuTitle: this.$t('route.contentStg'),
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        remark: '',
        active: false,
        dripScan: false,
        severity: 1,
        ruleGroupIds: '',
        respondRuleIds: '',
        entityType: undefined,
        entityId: undefined,
        stgTypeNumber: undefined,
        stopSendOut: false
      },
      stgDialogVisible: false,
      dialogStatus: '',
      textMap: {
        update: '',
        create: ''
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        ruleGroupList: [{ required: true, trigger: 'none', validator: this.ruleGroupValidator }]
      },
      submitting: false,
      gShow: false,
      rShow: false,
      filterString: '',
      curRoute: ''
    }
  },
  computed: {
    popoverShow() {
      return this.curRoute == this.$route.fullPath
    },
    popperClass() {
      return this.popoverShow ? '' : 'hidden'
    },
    contentMask() {
      return this.gShow || this.rShow
    },
    gridTable() {
      return this.$refs['strategyList']
    },
    stgTypeNumber() { // 策略类型编号
      const queryStgCode = this.$route.query.stgTypeNumber
      return !queryStgCode ? this.stgCode : queryStgCode
    },
    submitDisabled() {
      return this.gShow || this.rShow
    }
  },
  watch: {
    ruleGroupList: {
      deep: true,
      handler(val) {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate('ruleGroupList')
      }
    }
  },
  created() {
    this.curRoute = this.$route.fullPath
    initTimestamp(this)
    this.resetTemp()
    this.initRespondRuleOptions()
    this.initRuleGroupOptions()
    this.getSensitiveLossType()
    this.query.stgTypeNumber = this.stgTypeNumber
    this.defaultTemp.stgTypeNumber = this.stgTypeNumber
    if (this.stgTypeNumber == 52) {
      this.treeable = false
      this.addBtnAble = true
    }
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      // this.query.stgTypeNumber = undefined
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    this.query.dripScan = this.dripAble
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
    // 从规则集页面返回后，需要更新规则集树数据
    if (!isSameTimestamp(this, 'Group')) {
      this.initRuleGroupOptions()
      this.$nextTick(() => {
        this.updateGroup = true
        this.updateRuleGroup()
      })
    }
    // 从响应规则页面返回后，需要更新响应规则树数据
    if (!isSameTimestamp(this, 'RespondRule')) {
      this.initRespondRuleOptions()
      this.$nextTick(() => {
        this.updateRespond = true
        this.updateRespondRule()
      })
    }
    this.getRules(Object.keys(this.rulesMap).toString())
    initTimestamp(this)
  },
  methods: {
    getSensitiveLossType() {
      getSensitiveLossType({ type: 2 }).then(res => {
        const data = res.data.map(item => {
          return {
            value: item.lossType,
            label: item.lossDesc
          }
        })
        this.lossTypeOptions = [{ label: this.$t('pages.lossTypeOptions'), value: -1 }, ...data]
      })
    },
    groupTreeShow() {
      this.ruleGroupList.splice(0, 0)
      this.updateGroup = true
      this.groupSelectChange = true
      this.ruleGroupListStr = this.ruleGroupList.toString()
    },
    groupTreeHide() {
      // 选中的规则集发生变更，才会更新规则集列表
      if (this.ruleGroupListStr != this.$refs.ruleGroup.selectedKeys.toString()) {
        this.updateRuleGroup()
      } else {
        this.groupSelectChange = false
      }
      this.$refs.ruleGroup && this.$refs.ruleGroup.clearFilter()
    },
    respondTreeShow() {
      this.respondRuleList.splice(0, 0)
      this.updateRespond = true
      this.respondSelectChange = true
      this.respondRuleListStr = this.respondRuleList.toString()
    },
    respondTreeHide() {
      // 选中的响应规则发生变更，才会更新响应规则列表
      if (this.respondRuleListStr != this.$refs.respondRule.selectedKeys.toString()) {
        this.updateRespondRule()
      } else {
        this.respondSelectChange = false
      }
      this.$refs.respondRule && this.$refs.respondRule.clearFilter()
    },
    toArray(str) {
      return str.split(',').map(v => Number(v))
    },
    // 更新规则数组，用于查看规则详情
    updateRulesMap(id) {
      this.getRules(id)
    },
    getRules(ids) {
      getSubRuleByIds(ids).then(res => {
        res.data.forEach(item => {
          this.rulesMap[item.id] = item
        })
      })
    },
    handleRuleView(id) {
      let isShow
      if (this.dripAble) {
        isShow = true
      } else {
        isShow = false
      }
      this.$refs.groupDialog.handleRuleView(this.rulesMap[id], isShow)
    },
    handleChange(val) {
      // console.log('change:', val)
    },
    handleImportFile() {
      this.$refs.importDlg.show()
    },
    importSuccess() {
      this.initRespondRuleOptions()
      this.initRuleGroupOptions()
      this.handleFilter()
    },
    importFunc(formData) {
      return importStrategy(formData)
    },
    deleteItem(type, index) {
      const rows = type + 'Rows'
      const list = type + 'List'
      this[rows].splice(index, 1)
      this[list].splice(index, 1)
    },
    // 更新规则集列表
    updateRuleGroup() {
      if (this.updateGroup) {
        // 从规则集树发起的更新以树选中id为准，其余途径发起的更新不变更选中规则集id
        const selectedKeys = this.$refs.ruleGroup.selectedKeys.filter(item => !item.includes('G'))
        this.ruleGroupList = this.groupSelectChange ? selectedKeys : this.ruleGroupList
        getGroupByIds(this.ruleGroupList.toString()).then(res => {
          const len = this.ruleGroupRows.length
          this.activeNames1 = []
          this.ruleGroupRows.splice(0, len, ...res.data)
          this.updateGroup = false
          this.groupSelectChange = false
          res.data.forEach((item, index) => {
            this.formatExprToTagOp(item, index)
          })
        })
      } else {
        this.$refs.ruleGroup.checkSelectedNode(this.ruleGroupList)
      }
    },
    // 更新规则集列表
    updateRespondRule() {
      if (this.updateRespond) {
        // 从响应规则树发起的更新以树选中id为准，其余途径发起的更新不变更选中响应规则id
        this.respondRuleList = this.respondSelectChange ? this.$refs.respondRule.selectedKeys : this.respondRuleList
        getRespondByIds(this.respondRuleList.toString()).then(res => {
          this.activeNames2 = []
          this.updateRespond = false
          this.respondSelectChange = false
          this.respondRuleRows = res.data
        })
      } else {
        this.$refs.respondRule.checkSelectedNode(this.respondRuleList)
      }
    },
    async formatExprToTagOp(group, key) {
      this.checkRules[key] = { 'check': [], 'except': [] }
      if (group.ruleIds) {
        const tagOptionMap = {}
        // 获取分类中包含的规则详情
        // 需等待数据获取完，tagOptionMap赋值完毕，所以使用await
        await getSubRuleByIds(group.ruleIds).then(res => {
          res.data.forEach(item => {
            if (!tagOptionMap[item.id]) {
              tagOptionMap[item.id] = this.treeNodeDataToTagOption(item)
            }
            this.rulesMap[item.id] = item
          })
        })
        this.ExprToTag(group.checkExpr, tagOptionMap, this.checkRules[key].check)
        this.ExprToTag(group.exceptExpr, tagOptionMap, this.checkRules[key].except)

        if (group.doDrip) { // 零星检测规则的相关配置
          group.dripProps.forEach(drip => {
            if (!group.isCountMode) group.isCountMode = !!drip.countThreshold
          })
        }
      }
    },
    ExprToTag(expr, tags, checkRules) {
      if (!expr) return
      const checks = expr.replace(/&/g, '|').split('|')
      let cp = 0
      checks.forEach((id, i) => {
        let relation = ''
        if (i > 0) { // 首位没有关系符号&或|
          relation = expr.substring(cp, cp + 1)
          cp += 1
        }
        cp += String(id).length
        const tagOp = tags[id]
        if (tagOp) {
          checkRules.push(Object.assign({}, tagOp, { relation: this.relationMap[relation] }))
        }
      })
    },
    treeNodeDataToTagOption(nodeData) {
      return nodeData ? { id: nodeData.id, name: nodeData.name, relation: '或', type: nodeData.type } : null
    },
    addGroup() {
      // this.stgDialogVisible = false
      this.$refs.groupDialog.handleCreate()
    },
    createGroup(data) {
      this.$nextTick(() => {
        this.ruleGroupList.push(data.id)
        this.ruleGroupRows.push(data)
        this.updateGroup = true
        this.updateRuleGroup()
      })
    },
    groupClosed(data) {
      if (!data) {
        this.stgDialogVisible = true
        this.initRuleGroupOptions()
      }
    },
    addRespond() {
      // this.stgDialogVisible = false
      this.$refs.respondDialog.handleCreate()
    },
    createRespond(data) {
      this.$nextTick(() => {
        this.respondRuleList.push(data.id)
        this.respondRuleRows.push(data)
        this.updateRespond = true
        this.updateRespondRule()
      })
    },
    respondClosed() {
      this.stgDialogVisible = true
      this.initRespondRuleOptions()
    },
    selectable(row, index) {
      if (this.dripAble !== row.dripScan) {
        return false
      }
      if (containStgObject(row, this.query.objectType, this.query.objectId)) {
        return true
      }
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    // 初始化响应规则树数据
    initRespondRuleOptions: function() {
      listRespondRuleTreeNode().then(respond => {
        this.respondRuleOptions = respond.data
      })
    },
    initRuleGroupOptions() {
      this.ruleGroupOptions.splice(0)
      listRuleGroupTreeNode().then(respond => {
        this.ruleGroupOptions = respond.data
        this.filterRuleGroupList(this.temp.dripScan, true)
      })
    },
    filterRuleGroupList: function(isDripScan, isInit) { // 过滤规则集树数据：普通和零星
      this.ruleGroupTreeDatas.splice(0)
      if (!isInit) {
        // 初始化不删除已选规则集，通过切换零星规则开关才清空
        this.ruleGroupRows.splice(0)
        this.ruleGroupList.splice(0)
      }
      for (let i = 0, size = this.ruleGroupOptions.length; i < size; i++) {
        const temp = this.ruleGroupOptions[i]
        if (temp.children && temp.children.length > 0) {
          temp.children = temp.children.filter(item => {
            return isDripScan && item.oriData.doDrip || !isDripScan && !item.oriData.doDrip
          })
          if (temp.children && temp.children.length > 0) {
            this.ruleGroupTreeDatas.push(temp)
          }
        }
      }
      // 全盘扫描、文件自检内容等检测策略不支持高级算法，需要额外过滤掉
      if (!this.advancedRuleAble) {
        this.valiRules()
      }
    },
    async valiRules() {
      const filterData = []
      this.ruleGroupTreeDatas.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            if (child.oriData) {
              const json = {}
              json.id = child.oriData.id
              json.ruleIds = child.oriData.ruleIds
              filterData.push(json)
            }
          })
        }
      })
      const filterRuleGroupTreeDatas = []
      await valiRules(filterData).then(res => {
        if (res.data.length > 0) {
          this.filterString = res.data
        }
      })
      this.ruleGroupTreeDatas.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.children = item.children.filter(child => {
            return !child.oriData || !this.filterString || this.filterString.indexOf(child.oriData.id + '') > -1
          })
          if (item.children && item.children.length > 0) {
            filterRuleGroupTreeDatas.push(item)
          }
        }
      })
      this.ruleGroupTreeDatas = filterRuleGroupTreeDatas
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.strategyDefType = this.query.strategyDefType
      this.temp.dripScan = this.dripAble
      this.ruleGroupList.splice(0)
      this.respondRuleList.splice(0)
      this.activeNames1 = []
      this.activeNames2 = []
      // 修改菜单标题
      this.menuTitle = this.dripAble ? this.$t('route.dripContent') : this.$t(this.stgTypeLabel)
      const create = this.i18nConcatText(this.menuTitle, 'create')
      const update = this.i18nConcatText(this.menuTitle, 'update', this.formable)
      this.textMap = { create, update }
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.filterRuleGroupList(this.dripAble, true)
      this.dialogStatus = 'create'
      this.stgDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs.dataForm) this.$refs['dataForm'].clearValidate()
        if (this.$refs.ruleGroup) this.$refs.ruleGroup.clearSelectedNodes()
        if (this.$refs.respondRule) this.$refs.respondRule.clearSelectedNodes()
        this.ruleGroupRows.splice(0)
        this.respondRuleRows.splice(0)
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      this.temp.stgTypeNumber = this.defaultTemp.stgTypeNumber
      if (this.temp.ruleGroupIds) {
        this.ruleGroupList.splice(0, 0, ...this.temp.ruleGroupIds.split(','))
        getGroupByIds(this.temp.ruleGroupIds).then(res => {
          if (res.data && res.data.length > 0) {
            this.ruleGroupRows = res.data
            res.data.forEach((item, index) => {
              this.formatExprToTagOp(item, index)
            })
          } else {
            this.ruleGroupList.splice(0)
          }
        })
      }
      if (this.temp.respondRuleIds) {
        this.respondRuleList.push(...this.temp.respondRuleIds.split(','))
        getRespondByIds(this.temp.respondRuleIds).then(res => {
          if (res.data && res.data.length > 0) {
            this.respondRuleRows = res.data
          } else {
            this.respondRuleList.splice(0)
          }
        })
      } else {
        this.respondRuleRows.splice(0)
      }
      this.filterRuleGroupList(this.temp.dripScan, true)
      this.dialogStatus = 'update'
      this.stgDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs['dataForm']) this.$refs['dataForm'].clearValidate()
        if (this.$refs.ruleGroup) this.$refs.ruleGroup.clearSelectedNodes()
        if (this.$refs.respondRule) this.$refs.respondRule.clearSelectedNodes()
        this.ruleGroupRows.splice(0)
        this.respondRuleRows.splice(0)
      })
    },
    handleImport() {},
    handleExport(file) {
      const stgIds = this.gridTable.getSelectedIds()
      const opts = { file, jwt: true, topic: 'contentStg' }
      exportStg({ strategyIds: stgIds.join(','), remark: file.name }, opts)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.ruleGroupIds = this.ruleGroupList.join(',')
          this.temp.respondRuleIds = this.respondRuleList.join(',')
          if (this.stgTypeNumber == 52) {
            this.temp.active = true
          }
          (this.dripAble ? createDripStrategy : this.createStgFunc)(this.temp).then(respond => {
            this.submitting = false
            this.stgDialogVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.ruleGroupIds = this.ruleGroupList.join(',')
          tempData.respondRuleIds = this.respondRuleList.join(',');
          (this.dripAble ? updateDripStrategy : this.updateStgFunc)(tempData).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.stgDialogVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds();
        (this.dripAble ? deleteDripStrategy : this.deleteStgFunc)({ ids: toDeleteIds.join(',') }).then(respond => {
          if (respond.data === 'disable_4_relate') {
            const msg = this.$t('pages.content_text13', { stgType: this.$t(this.relStgTypeLabel) })
            this.$notify({ title: this.$t('text.warning'), message: msg, type: 'warning', duration: 2000 })
          } else {
            this.gridTable.deleteRowData(toDeleteIds)
            this.$notify({ title: this.$t('text.success'), message: this.$t('text.deleteSuccess'), type: 'success', duration: 2000 })
          }
        })
      }).catch(() => {})
    },
    handleStatus(status) {
      this.$confirmBox(`${this.$t('pages.content_text18', { status: status ? this.$t('text.enable') : this.$t('text.stop') })}`, this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds();
        (this.dripAble ? updateDripStatusBatch : updateStatusBatch)({ ids: toDeleteIds.join(','), active: status }).then(respond => {
          this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
          this.gridTable.execRowDataApi()
        })
      }).catch(() => {})
    },
    getByName(option) {
      return getStrategyByName({ name: option.name, stgTypeNumber: this.query.stgTypeNumber }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          this.$message({
            message: this.$t('valid.sameName'),
            type: 'error',
            duration: 2000
          })
          return Promise.reject(this.$t('valid.sameName'))
        } else {
          return respond
        }
      })
    },
    getStgByName(option) {
      return getStrategyByName({ name: option.name, stgTypeNumber: this.query.stgTypeNumber }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          return Promise.reject(this.$t('valid.sameName'))
        } else {
          return respond
        }
      })
    },
    nameValidator(rule, value, callback) {
      this.getStgByName({ name: value, stgTypeNumber: this.query.stgTypeNumber }).then(() => {
        callback()
      }).catch(reason => {
        callback(new Error(reason))
      })
    },
    ruleGroupValidator(rule, value, callback) {
      if (!this.ruleGroupList || this.ruleGroupList.length === 0) {
        callback(new Error(this.$t('pages.content_text19')))
      } else {
        callback()
      }
    },
    activeFormatter(row, data) {
      if (this.$route.query.stgTypeNumber == 52) {
        return []
      }
      return stgActiveIconFormatter(row)
    },
    severityFormatter: function(row, data) {
      for (let i = 0, size = this.severityOptions.length; i < size; i++) {
        const id = this.severityOptions[i].value
        if (id === data || id.toString() === data) {
          return this.severityOptions[i].label
        }
      }
      return ''
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    ruleGroupFormatter(row, data) {
      const names = []
      const ruleGroups = data ? data.split(',') : []
      for (let i = 0, size = this.ruleGroupOptions.length; i < size; i++) {
        const children = this.ruleGroupOptions[i].children
        if (children && children.length > 0) {
          children.forEach((item) => {
            const id = item.id
            if (ruleGroups.indexOf(id) >= 0) {
              names.push(item.label)
            }
          })
        }
      }
      return names.join(',')
    },
    respondRuleFormatter: function(row, data) {
      const names = []
      const respondRules = data ? data.split(',') : []
      for (let i = 0, size = this.respondRuleOptions.length; i < size; i++) {
        const id = this.respondRuleOptions[i].dataId
        if (respondRules.indexOf(id) >= 0) {
          names.push(this.respondRuleOptions[i].label)
        }
      }
      return names.join(',')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    btnDisableFormatter: function(row) {
      return this.dripAble !== row.dripScan
    }
  }
}
</script>

<style lang='scss' scoped>
  .content-mask{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: #000;
    opacity: 0.3;
    z-index: 3001;
  }
  .add-btn{
    width: 30px;
    height: 30px;
  }
  .drip{
    cursor: pointer;
  }
  .popover-confirm {
    height: 30px;
    width: 60px;
    padding: 5px;
    margin-top: 5px;
    margin-bottom: -5px;
    float: right;
  }
  >>>.el-collapse{
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
  }
  >>>.el-collapse-item__header{
    height: 32px;
    line-height: 32px;
    color: #303133 !important;
    background-color: #e4e7e9;
    padding-left: 12px;
    border-bottom: 1px solid #cccccc;
    position: relative;
    &.is-active{
      border-bottom-color: transparent;
    }
    .rule-group-delete{
      color: red;
      position: absolute;
      top: 9px;
      right: 30px;
      display: none;
    }
    &:hover .rule-group-delete{
      display: block;
    }
  }
  >>>.el-collapse-item__wrap{
    background-color: #e4e7e9;
    border-bottom: 1px solid #cccccc;
  }
  >>>.el-collapse-item__content{
    padding: 10px 16px;
  }
</style>
