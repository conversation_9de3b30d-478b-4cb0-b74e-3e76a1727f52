<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="223"/>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="diskScanSelfCheckList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <disk-scan-self-check-dlg
      ref="stgDlg"
      :active-able="treeable"
      :entity-node="checkedEntityNode"
      @submitEnd="submitEnd"
    />
  </div>
</template>

<script>
import DiskScanSelfCheckDlg from './editDlg'
import { getPage, deleteStg } from '@/api/dataEncryption/encryption/diskScanSelfCheck'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'

export default {
  name: 'DiskScanSelfCheck',
  components: { DiskScanSelfCheckDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', fixedWidth: '150', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '150', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [{ formatter: this.entityFormatter, click: this.entityClick }]
        },
        { prop: '', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '80', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      checkedEntityNode: {},
      dayOptions: { 0: this.$t('pages.everyDay'), 1: this.$t('pages.monday1'), 2: this.$t('pages.tuesday1'), 4: this.$t('pages.wednesday1'), 8: this.$t('pages.Thursday1'), 16: this.$t('pages.friday1'), 32: this.$t('pages.saturday1'), 64: this.$t('pages.sunday1') }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['diskScanSelfCheckList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  activated() {
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    selectionChangeEnd(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.checkedEntityNode = checkedNode
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.checkedEntityNode = {}
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.handleFilter()
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    handleFilter() {
      this.query.page = 1
      this.searchData(this.query)
    },
    searchData(query) {
      this.gridTable.execRowDataApi(query)
    },    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.$refs['stgDlg'].handleCreate()
    },
    handleUpdate(row) {
      this.$refs['stgDlg'].handleUpdate(row)
    },
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStg({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    pathFormatter: function(row, data) {
      return !data ? '' : data
    },
    activeFormatter: function(row, data) {
      return row.status == 0 ? 'active' : ''
    },
    strategyFormatter: function(row, data) {
      let msg = ''
      if (row.scanDir) {
        msg += this.$t('table.scanDir') + '：' + row.scanDir
      } else {
        msg += this.$t('table.scanDir') + '：' + this.$t('pages.all')
      }
      if (row.exceptDir) {
        msg += '；' + this.$t('table.exceptionDirectory') + '：' + row.exceptDir
      } else {
        msg += '；' + this.$t('table.exceptionDirectory') + '：' + this.$t('pages.null')
      }
      if (row.suffix) {
        msg += '；' + this.$t('table.suffixes') + '：' + row.suffix
      } else {
        msg += '；' + this.$t('table.suffixes') + '：' + this.$t('pages.null')
      }
      if (row.scanMode >= 0) {
        msg += '；' + this.$t('table.scanMode') + '：' + (row.scanMode == 0 ? this.$t('pages.userDefined') : (row.scanMode == 1 ? this.$t('pages.diskScan_Msg') : row.scanMode == 2 ? this.$t('pages.diskScan_Msg1') : this.$t('pages.diskScan_Msg2')))
      }
      if (row.scanMode == 0 && row.cpuMem) {
        msg += '；' + this.$t('pages.diskScan_Msg20') + row.cpuMem.split('|')[0] + this.$t('pages.diskScan_Msg21') + row.cpuMem.split('|')[1]
      }
      if (row.endDate) {
        msg += '；' + this.$t('pages.endTime') + '：' + row.endDate
      }
      if (row.enforceRun >= 0) {
        msg += '；' + this.$t('pages.diskScanSelfCheck_Msg3') + '：' + (row.enforceRun == 0 ? this.$t('pages.diskScanSelfCheck_Msg4') : (row.enforceRun == 1 ? this.$t('pages.diskScanSelfCheck_Msg5') : this.$t('pages.diskScanSelfCheck_Msg12')))
      }
      if (row.tipsType == 0) {
        msg += '；' + this.$t('pages.diskScanSelfCheck_Msg13') + '：' + this.dayOptions[row.tipsType]
      } else if (row.tipsType > 0) {
        msg += '；' + this.$t('pages.diskScanSelfCheck_Msg13') + '：'
        const day = this.numToList(row.tipsType, 7)
        if (day.indexOf(1) >= 0) msg += this.dayOptions[1] + ' '
        if (day.indexOf(2) >= 0) msg += this.dayOptions[2] + ' '
        if (day.indexOf(4) >= 0) msg += this.dayOptions[4] + ' '
        if (day.indexOf(8) >= 0) msg += this.dayOptions[8] + ' '
        if (day.indexOf(16) >= 0) msg += this.dayOptions[16] + ' '
        if (day.indexOf(32) >= 0) msg += this.dayOptions[32] + ' '
        if (day.indexOf(64) >= 0) msg += this.dayOptions[64] + ' '
      }
      if (row.tipsDateTime) {
        msg += '；' + this.$t('pages.diskScanSelfCheck_Msg14') + '：' + row.tipsDateTime
      }
      if (row.isShowTips) {
        msg += '；' + this.$t('pages.remindersNum') + '：' + row.tipTimes + this.$t('pages.openTimes2')
      }
      if (row.isReport) {
        msg += '；' + this.$t('pages.reportSelfTestResults')
      }
      if (row.markNonSst) {
        msg += '；' + (row.markBy == 0 ? this.$t('pages.diskScanSelfCheck_Msg30') : (row.markBy == 1 ? this.$t('pages.diskScanSelfCheck_Msg31') : this.$t('pages.diskScanSelfCheck_Msg32')))
      }
      return msg
    }
  }
}
</script>

