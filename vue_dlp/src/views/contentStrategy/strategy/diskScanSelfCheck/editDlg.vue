<template>
  <div>
    <stg-dialog
      ref="stgDlg"
      type="drawer"
      :title="$t('route.diskScanSelfCheck')"
      :stg-code="223"
      :rules="rules"
      :active-able="activeAble"
      :model="defaultTemp"
      :entity-node="entityNode"
      :create="createScan"
      :update="updateScan"
      :format-row-data="formatRowData"
      :format-form-data="formatFormData"
      @submitEnd="submitEnd"
      @slotChange="slotChange"
      @closed="closed"
    >
      <template :slot="slotName">
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.sensitiveStrategy')" class="content-flex" prop="contentStgId">
              <el-select v-model="temp.contentStgId" :disabled="!editable">
                <el-option v-for="(value, key) in contentStgOptions" :key="key" :label="value" :value="parseInt(key)"></el-option>
              </el-select>
              <link-button
                btn-class="editBtn"
                :formable="editable"
                :menu-code="'F15'"
                :always-show="true"
                :link-url="{
                  path:'/contentStrategy/contentStg/diskScanSelfCheckSensitive',
                  query: { stgTypeNumber: 224, treeable: false }
                }"
              />
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.scanDir')">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.diskScanSelfCheck_Msg') }}<br/>
              {{ $t('pages.diskScan_Msg9') }}<br/>
              {{ $t('pages.diskScan_Msg10') }}<br/>
              {{ $t('pages.inputMaxLength', { num: 256 }) }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <div style="display: flex; align-items: center;">
            <tag
              ref="tag"
              :list="temp.scanDir"
              border
              input-length="256"
              :disabled="!formable"
              :valid-rule="scanDirRules"
              style="width: calc(100% - 50px); min-height: 30px; background-color: #f5f5f5;"
            />
            <el-tooltip class="item" effect="dark" :content="$t('pages.configScanDir')" placement="top" style="margin-left: 5px;">
              <el-button type="primary" :disabled="!formable" size="mini" @click="openConfigDir()">
                <svg-icon icon-class="setting" />
              </el-button>
            </el-tooltip>
          </div>
        </FormItem>
        <FormItem :label="$t('pages.exceptionDirectory')" prop="exceptDir">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.diskScanSelfCheck_Msg1') }}<br/>
              {{ $t('pages.diskScan_Msg12') }}<br/>
              {{ $t('pages.diskScan_Msg13') }}<br/>
              {{ $t('pages.inputMaxLength', { num: 256 }) }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag :list="temp.exceptDir" border :disabled="!formable || !editable" input-length="256" style="min-height: 30px; background-color: #f5f5f5;"/>
        </FormItem>
        <FormItem :label="$t('pages.process_Msg5')" prop="suffix" tooltip-placement="bottom-start">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.diskScanSelfCheck_Msg2') }}<br/>
              {{ $t('pages.inputMaxLength', { num: 256 }) }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag :list="temp.suffix" border :disabled="!formable || !editable" class="input-with-button" input-length="256" style="width: calc(100% - 122px); min-height: 30px; background-color: #f5f5f5;" @tagChange="suffixChange"/>
          <el-tooltip class="item" effect="dark" :content="$t('pages.importFileSuffixLib')" placement="top">
            <el-button type="primary" :disabled="!formable" size="mini" @click="handleFileSuffixImport()">
              <svg-icon icon-class="import" />
            </el-button>
          </el-tooltip>
          <el-button v-if="formable" :disabled="!formable" size="small" class="clear-btn" @click="handleClear">
            {{ $t('button.clear') }}
          </el-button>
        </FormItem>
        <!-- <FormItem :label="$t('pages.diskScan_Msg15')" prop="penetrateSuffix">
          <el-checkbox-group v-model="temp.penetrateSuffix" :disabled="!editable">
            <el-checkbox v-for="(suffix, index) in zipSuffixes" :key="index" :label="suffix">{{ suffix }}</el-checkbox>
          </el-checkbox-group>
        </FormItem> -->
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.scanMode')" prop="scanMode">
              <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.diskScan_Msg16') }}<br/>
                  {{ $t('pages.diskScan_Msg17') }}<br/>
                  {{ $t('pages.diskScan_Msg18') }}<br/>
                  {{ $t('pages.diskScan_Msg19') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-select v-model="temp.scanMode" :disabled="!editable" clearable>
                <el-option v-for="item in scanModeOptions" :key="item.id" :label="item.label" :value="item.id"></el-option>
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem v-show="temp.scanMode === 0">
          <el-row style="text-align: right;">
            <el-col :span="6">
              <span>{{ $t('pages.diskScan_Msg20') }}</span>
            </el-col>
            <el-col :span="4">
              <el-input-number v-model="temp.cpuMem.cpu" controls-position="right" :min="1" :max="100" :disabled="!editable"></el-input-number>
            </el-col>
            <el-col :span="7">
              <span>{{ $t('pages.diskScan_Msg21') }}</span>
            </el-col>
            <el-col :span="4">
              <el-input-number v-model="temp.cpuMem.mem" controls-position="right" :min="1" :max="100" :disabled="!editable"></el-input-number>
            </el-col>
          </el-row>
        </FormItem>
        <FormItem :label="$t('pages.diskScanSelfCheck_Msg3')" prop="enforceRun">
          <el-radio-group v-model="temp.enforceRun" :disabled="!formable" @change="enforceRunChange">
            <el-radio :label="0">{{ $t('pages.diskScanSelfCheck_Msg4') }}</el-radio><br/>
            <el-radio :label="1">{{ $t('pages.diskScanSelfCheck_Msg5') }}</el-radio><br/>
            <FormItem ref="stgEndDate" key="stgEndDate" label-width="100px" :label="$t('pages.diskScanSelfCheck_Msg6')" prop="stgEndDate" style="margin-left: 25px" :class="(temp.enforceRun == 1) ? 'specilClass' : 'defaultClass'">
              <el-date-picker v-model="stgEndDate" type="date" value-format="yyyy-MM-dd" :disabled="!formable || !(temp.enforceRun == 1)" :picker-options="pickerOptions" style="width:275px;" :placeholder="$t('pages.diskScanSelfCheck_Msg7')"></el-date-picker>
            </FormItem>
            <FormItem :label="$t('pages.diskScanSelfCheck_Msg8')" label-width="100px" style="margin-left: 10px" :class="(temp.enforceRun == 1) ? 'specilClass' : 'defaultClass'">
              <el-tooltip class="item" effect="dark" placement="bottom-start">
                <div slot="content">
                  {{ $t('pages.diskScanSelfCheck_Msg9') }}
                </div>
                <i class="el-icon-info" />
              </el-tooltip>
              <el-checkbox-group v-model="runTips" :disabled="!formable || !(temp.enforceRun == 1)" style="margin-left: 30px; display: inline-block;">
                <el-checkbox :label="1">{{ $t('pages.diskScanSelfCheck_Msg10') }}</el-checkbox>
                <el-checkbox :label="2">{{ $t('pages.diskScanSelfCheck_Msg11') }}</el-checkbox>
              </el-checkbox-group>
            </FormItem>
            <el-radio :label="2">{{ $t('pages.diskScanSelfCheck_Msg12') }}</el-radio>
          </el-radio-group>
          <div style="margin: 0 25px">
            <FormItem :label="$t('pages.diskScanSelfCheck_Msg13')" label-width="100px" prop="tipsType" :class="(temp.enforceRun == 2) ? 'specilClass' : 'defaultClass'">
              <el-select
                v-model="temp.tipsType"
                multiple
                :placeholder="$t('text.select')"
                :disabled="!formable || !(temp.enforceRun == 2)"
                style="width: 400px;"
                @change="tipsTypeChange"
              >
                <el-option v-for="item in dayOptions" :key="item.value" :label="item.label" :value="item.value" >
                </el-option>
              </el-select>
            </FormItem>
            <FormItem prop="tipsDateTime" :label="$t('pages.diskScanSelfCheck_Msg14')" label-width="100px" :class="(temp.enforceRun == 2) ? 'specilClass' : 'defaultClass'">
              <el-input v-model="temp.tipsDateTime" disabled style="width: 400px;"></el-input>
              <svg-icon v-show="formable && !isHide" icon-class="add" style="margin-left: 5px;" @click="showTipTimeOption()" />
              <svg-icon v-show="formable && tipTimeList.length > 0 && isHide" icon-class="active" style="margin-left: 5px;" />
              <div v-if="tipTimeList.length > 0 && isHide" class="edit-item">
                <FormItem label-width="0">
                  <div v-for="(item,index) in tipTimeList" :key="index">
                    <el-time-picker
                      v-model="tipTimeList[index]"
                      :editable="true"
                      format="HH:mm"
                      value-format="HH:mm"
                      style="width: 210px"
                      :placeholder="$t('pages.webFlow_text2')"
                    />
                    <i v-show="tipTimeList.length < 10" class="el-icon-circle-plus-outline" style="color: #68a8d0; cursor: pointer;" @click="addTime(index + 1)"></i>
                    <i v-show="tipTimeList.length > 1" class="el-icon-remove-outline" style="color: #F56C6C; cursor: pointer;" @click="deleteTime(index)"></i>
                  </div>
                </FormItem>
                <span style="padding-left: 10px; color: #0c60a5;">{{ $t('pages.diskScanSelfCheck_Msg15') }}</span>
              </div>
            </FormItem>
            <FormItem key="tipEndDate" :label="$t('pages.diskScanSelfCheck_Msg16')" label-width="100px" prop="tipEndDate" :class="(temp.enforceRun == 2) ? 'specilClass' : 'defaultClass'">
              <el-date-picker v-model="tipEndDate" type="date" value-format="yyyy-MM-dd" :disabled="!formable || !(temp.enforceRun == 2)" :picker-options="pickerOptions" style="width:275px;" :placeholder="$t('pages.diskScanSelfCheck_Msg6')"></el-date-picker>
            </FormItem>
            <FormItem style="margin-left: 10px" prop="tipTimes">
              <el-checkbox v-model="temp.showTips" :true-label="1" :false-label="0" :disabled="!formable || !(temp.enforceRun == 2)" style="margin-left: -5px">
                <i18n path="pages.diskScanSelfCheck_Msg17">
                  <el-input-number slot="times" v-model="temp.tipTimes" :disabled="!temp.showTips || !formable || !(temp.enforceRun == 2)" :min="0" :max="100" :controls="false" style="width: 80px;" :precision="0"></el-input-number>
                </i18n>
                <el-tooltip class="item" effect="dark" placement="bottom-start">
                  <div slot="content">
                    {{ $t('pages.diskScanSelfCheck_Msg18') }}
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </el-checkbox>
            </FormItem>
          </div>
        </FormItem>
        <FormItem :label="$t('pages.diskScanSelfCheck_Msg19')" prop="markNonSst">
          <!-- <el-checkbox v-model="temp.isReport" :true-label="1" :false-label="0">上报自检结果</el-checkbox><br/> -->
          <el-checkbox v-model="temp.markNonSst" :true-label="1" :false-label="0" :disabled="!formable" style="margin-left: 10px;" @change="markNonSstChange">{{ $t('pages.diskScanSelfCheck_Msg20') }}</el-checkbox>
          <div style="margin-left: 30px">
            <el-radio-group v-model="temp.markBy" :disabled="!formable || temp.markNonSst == 0" @change="markByChange">
              <el-radio :label="2" style="margin: 0;">{{ $t('pages.diskScanSelfCheck_Msg21') }}</el-radio>
              <el-radio :label="1" style="margin: 0;">{{ $t('pages.diskScanSelfCheck_Msg22') }}</el-radio>
              <el-radio :label="0">{{ $t('pages.diskScanSelfCheck_Msg23') }}</el-radio>
            </el-radio-group>
          </div>
        </FormItem>
      </template>
    </stg-dialog>
    <file-suffix-lib-import ref="fileSuffixLibImport" @importfilesuffix="importFileSuffix"/>
    <el-dialog
      ref="configDir"
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.configScanDir')"
      :visible.sync="dirVisible"
      width="650px"
    >
      <el-card class="box-card" style="margin: 5px;">
        <div slot="header" class="clearfix">
          <span>{{ $t('pages.commonDir') }}</span>
        </div>
        <div style="line-height: 22px;">
          <el-button type="text" @click="selectAll(1)">{{ $t('button.selectAll') }}</el-button>
          <el-button type="text" @click="selectAll(2)">{{ $t('button.cancelSelectAll') }}</el-button>
          <el-checkbox-group v-model="checkedCommonDir" style="margin-top: 6px;">
            <el-row>
              <el-col v-for="(item, index) in commonsDir" :key="index" :span="8">
                <el-checkbox :label="item.name" @change="commonDirChange($event)">
                  <span :title="item.title" class="ellipsis label-text">{{ item.name }}</span>
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </div>
      </el-card>
      <el-card class="box-card" style="margin: 5px;">
        <div slot="header" class="clearFix">
          <span>{{ $t('pages.specifyDir') }}</span>
          <span style="color: #3296FA;font-weight: normal;">
            ({{ $t('pages.diskScan_Msg44') }})
          </span>
        </div>
        <div style="display: flex;align-items: center; line-height: 22px;">
          <span style="width: 70px;">
            {{ $t('pages.scanDir') }}
          </span>
          <el-tooltip class="item" effect="dark" placement="bottom-start" style="margin-left: -9px;">
            <div slot="content">
              {{ $t('pages.diskScan_Msg33') }}<br>
              {{ $t('pages.diskScan_Msg37') }}<br>
              {{ $t('pages.diskScan_Msg35') }}<br>
              {{ $t('pages.diskScan_Msg36') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <tag :list="specifyDir" border input-length="256" class="input-with-button" style="min-height: 30px;background-color:#f5f5f5;margin-left: 5px;"/>
        </div>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveConfigDir()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="cancelConfigDir ()">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createScan, updateScan, getScanContentStgIdName } from '@/api/dataEncryption/encryption/diskScanSelfCheck'
import moment from 'moment'
import FileSuffixLibImport from '@/views/system/baseData/fileSuffixLib/fileSuffixImportDlg.vue'

export default {
  name: 'DiskScanSelfCheckDlg',
  components: { FileSuffixLibImport },
  props: {
    formable: { type: Boolean, default: true }, // 能否提交表单
    activeAble: { type: Boolean, default: true }, // 是否显示启用按钮
    entityNode: { type: Object, default() { return {} } }
  },
  data() {
    return {
      editable: true,
      slotName: undefined,
      scanModeOptions: [{ id: 0, label: this.$t('pages.userDefined') }, { id: 1, label: this.$t('pages.diskScan_Msg') }, { id: 2, label: this.$t('pages.diskScan_Msg1') }, { id: 3, label: this.$t('pages.diskScan_Msg2') }],
      zipSuffixes: ['.zip', '.rar', '.7z'],
      contentStgOptions: [],
      temp: {},
      defaultTemp: { // 表单字段
        id: null,
        name: '',
        opType: 6,
        scanDir: [],
        exceptDir: ['Windows', 'Program Files', 'Local Settings'],
        suffix: [],
        penetrateSuffix: [],
        scanMode: null,
        cpuMem: { cpu: 50, mem: 50 },
        endDate: '',
        autoShutdown: 0,
        statusType: 0,
        status: 1, // 0-开始扫描，1-停止扫描，2-暂停扫描
        entityType: '',
        entityId: '',
        contentStgId: '', // 内容检测策略ID
        enforceRun: null,  // 首次自检方式
        tipsType: [],
        isReport: 0,
        markNonSst: 0,
        isShowTips: 0,
        showTips: 0,
        markBy: null,
        tipTimes: 0,
        tipsDateTime: '',
        runTips: null
      },
      runTips: [],
      stgEndDate: '',
      tipEndDate: '',
      rules: {
        name: [{ required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' }],
        scanMode: [{ required: true, validator: this.scanModeValidator, trigger: ['blur', 'change'] }],
        contentStgId: [{ required: true, validator: this.contentRuleValidator, trigger: ['blur', 'change'] }],
        // scanDir: [{ validator: this.scanDirValidator, trigger: 'blur' }],
        stgEndDate: [{ required: true, validator: this.stgEndDateValidator, trigger: 'blur' }],
        tipEndDate: [{ required: true, validator: this.tipEndDateValidator, trigger: 'blur' }],
        tipsType: [{ required: true, validator: this.tipsTypeValidator, trigger: 'blur' }],
        tipsDateTime: [{ required: true, validator: this.tipsDateTimeValidator, trigger: 'blur' }],
        /* tipTimes: [{ required: true, validator: this.tipTimesValidator, trigger: 'blur' }], */
        enforceRun: [{ required: true, validator: this.enforceRunValidator, trigger: ['blur', 'change'] }]
        /* markNonSst: [{ required: true, validator: this.markNonSstValidator, trigger: 'blur' }] */
      },
      scanDirRules: [
        { validator: this.scanDirValidator, trigger: 'blur' }
      ],
      tipTimeList: [],
      isHide: false,
      dealIds: [],
      pickerOptions: { // 时间设置今天以及今天之后
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      dayOptions: [
        { value: 0, label: this.$t('pages.everyDay') }, { value: 1, label: this.$t('pages.monday1') },
        { value: 2, label: this.$t('pages.tuesday1') }, { value: 4, label: this.$t('pages.wednesday1') },
        { value: 8, label: this.$t('pages.Thursday1') }, { value: 16, label: this.$t('pages.friday1') },
        { value: 32, label: this.$t('pages.saturday1') }, { value: 64, label: this.$t('pages.sunday1') }
      ],
      limitType: 'number',
      tipMsg: this.$t('pages.tipMsg'),
      suffixMaxLength: 300,
      dirVisible: false,
      checkedCommonDir: [],
      checkedRealDirValue: [],
      specifyDir: [],
      commonsDir: [
        {
          name: this.$t('pages.scanDesktop'),
          value: '#DESKTOP#',
          title: this.$t('pages.diskScan_Desktop')
        },
        {
          name: this.$t('pages.scanDocument'),
          value: '#MYDOCUMENTS#',
          title: this.$t('pages.diskScan_Documents')
        },
        {
          name: this.$t('pages.scanFavorites'),
          value: '#FAVORITES#',
          title: this.$t('pages.diskScan_Favorites')
        },
        {
          name: this.$t('pages.scanMusic'),
          value: '#MYMUSICS#',
          title: this.$t('pages.diskScan_Musics')
        },
        {
          name: this.$t('pages.scanPictures'),
          value: '#MYPICTURES#',
          title: this.$t('pages.diskScan_Pictures')
        },
        {
          name: this.$t('pages.scanVideo'),
          value: '#MYVIDEOS#',
          title: this.$t('pages.diskScan_Videos')
        },
        {
          name: this.$t('pages.scanAppData'),
          value: '#APPDATA#',
          title: this.$t('pages.diskScan_AppData')
        }
      ]
    }
  },
  computed: {
  },
  watch: {
    /* 'isShowTips'(newVal, oldVal) {
      if (newVal == 0) {
        this.temp.tipTimes = 0
      }
    } */
  },
  created() {
    this.resetTemp()
    this.loadContentStgOption()
  },
  activated() {
    this.loadContentStgOption()
  },
  methods: {
    createScan,
    updateScan,
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
      this.stgEndDate = ''
      this.tipEndDate = ''
      this.runTips = []
      this.temp.showTips = 0
      this.tipTimeList = []
      this.isHide = false
      this.$refs['tag'] && this.$refs['tag'].clearInputValue()
    },
    slotChange(name, slotTemp) {
      this.slotName = name
      this.temp = !slotTemp ? {} : slotTemp
    },
    closed() {
      this.resetTemp()
    },
    selectAll(type) {
      // type == 1 全选
      if (type == 1) {
        this.checkedCommonDir = []
        this.checkedCommonDir = this.commonsDir.map(item => item.name)
        this.checkedRealDirValue = []
        this.checkedRealDirValue = this.commonsDir.map(item => item.value)
      } else {
        this.checkedCommonDir = []
        this.checkedRealDirValue = []
      }
    },
    loadContentStgOption() {
      getScanContentStgIdName(224).then(respond => {
        this.contentStgOptions = respond.data
      })
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    saveConfigDir() {
      if (this.checkedRealDirValue.length == 0 && this.specifyDir.length == 0) {
        this.$message({
          title: this.$t('text.fail'),
          message: this.$t('pages.diskScan_Msg32'),
          type: 'error',
          duration: 3000
        })
        return
      }
      // 正则表达式，检测是否是带盘符的windows目录或正确的linux、mac目录
      const reg = /^(([a-zA-Z]:\\((((?! )[^/:*?<>\""|\\]+)+\\?)|(\\)?))|(\/))[\s\S]*$/
      // 检测是否是$:\test风格的window相对目录
      const specifyReg = /^(([$]:\\((((?! )[^/:*?<>\""|\\]+)+\\?)|(\\)?))|(\/))[\s\S]*$/
      if (this.specifyDir.length > 0) {
        for (const item of this.specifyDir) {
          if (!reg.test(item)) {
            // 不符合带盘符的windows目录，且不是linux或mac目录
            // 检测是否是$:\test风格的windos相对目录
            if (!specifyReg.test(item)) {
              this.$message({
                title: this.$t('text.fail'),
                message: this.$t('pages.diskScan_Msg30', { scanDir: item }),
                type: 'error',
                duration: 3000
              })
              return
            }
          }
        }
      }
      // 所有目录都通过了扫描
      const finalDir = []
      let num = 0
      if (this.checkedRealDirValue.length > 0) {
        this.checkedRealDirValue.forEach((item, index) => {
          finalDir[num] = item
          num++
        })
      }
      if (this.specifyDir.length > 0) {
        this.specifyDir.forEach((item, index) => {
          finalDir[num] = item
          num++
        })
      }
      // 将格式化好的目录赋值给外层弹窗的扫描目录
      finalDir.forEach((item, index) => {
        if (!this.temp.scanDir.includes(item)) {
          this.temp.scanDir.push(item)
        }
      })
      this.dirVisible = false
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
      this.$notify({
        title: this.$t('text.success'),
        message: this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    cancelConfigDir() {
      this.dirVisible = false
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
    },
    openConfigDir() {
      this.checkedCommonDir = []
      this.checkedRealDirValue = []
      this.specifyDir = []
      this.dirVisible = true
    },
    commonDirChange(event) {
      this.checkedRealDirValue = []
      if (this.checkedCommonDir.length > 0) {
        this.checkedCommonDir.forEach((res, pos) => {
          this.commonsDir.forEach((item, index) => {
            if (item.name == res) {
              this.checkedRealDirValue.push(item.value)
            }
          })
        })
      }
    },
    handleCreate() {
      this.resetTemp()
      this.editable = true
      this.$refs['stgDlg'].show({
        entityType: this.entityNode.type,
        entityId: this.entityNode.dataId
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.editable = this.formable
      this.$refs['stgDlg'].show(row, this.formable)
    },
    formatRowData(rowData) {
      if (rowData.scanMode === 0 && rowData.cpuMem) {
        const array = rowData.cpuMem.split('|')
        rowData.cpuMem = { cpu: array[0], mem: array[1] }
      } else {
        rowData.cpuMem = { cpu: 50, mem: 50 }
      }
      if (rowData.enforceRun == 1) {
        this.stgEndDate = rowData.endDate
      } else if (rowData.enforceRun == 2) {
        this.tipEndDate = rowData.endDate
        if (rowData.tipsType == 0) {
          rowData.tipsType = [0]
        } else if (rowData.tipsType > 0) {
          rowData.tipsType = this.numToArr(rowData.tipsType)
        } else {
          rowData.tipsType = []
        }
      } else {
        this.tipEndDate = ''
      }
      if (rowData.runTips > 0) {
        this.runTips = this.numToArr(rowData.runTips)
      } else {
        this.runTips = []
      }
      if (rowData.penetrateSuffix && !Array.isArray(rowData.penetrateSuffix)) {
        rowData.penetrateSuffix = rowData.penetrateSuffix.split('|')
      } else {
        rowData.penetrateSuffix = []
      }
      rowData.scanDir = !rowData.scanDir ? [] : rowData.scanDir.split('|')
      rowData.exceptDir = !rowData.exceptDir ? [] : rowData.exceptDir.split('|')
      rowData.suffix = !rowData.suffix ? [] : rowData.suffix.split('|')
      if (rowData.tipsDateTime != '') {
        this.tipTimeList = rowData.tipsDateTime.split('|')
      } else {
        this.tipTimeList = []
      }
      if (rowData.markNonSst == 0) {
        rowData.markBy = null
      }
    },
    formatFormData(formData) {
      formData.cpuMem = formData.cpuMem.cpu + '|' + formData.cpuMem.mem
      formData.penetrateSuffix = formData.penetrateSuffix.join('|')
      formData.scanDir = formData.scanDir.join('|')
      formData.exceptDir = formData.exceptDir.join('|')
      formData.suffix = formData.suffix.join('|')
      if (formData.enforceRun == 0) {
        this.stgEndDate = ''
        this.runTips = []
        formData.runTips = 0
        formData.tipsType = ''
        formData.tipsDateTime = ''
        formData.showTips = 0
        formData.isShowTips = 0
        formData.tipTimes = 0
        this.tipEndDate = ''
        formData.endDate = ''
      } else if (formData.enforceRun == 1) {
        formData.tipsType = ''
        formData.tipsDateTime = ''
        formData.isShowTips = 0
        formData.tipTimes = 0
        formData.showTips = 0
        this.tipEndDate = ''
        if (this.runTips.length) {
          formData.runTips = this.getSum(this.runTips)
        } else {
          formData.runTips = 0
        }
        if (this.stgEndDate) {
          formData.endDate = this.stgEndDate
        }
      } else if (formData.enforceRun == 2) {
        this.stgEndDate = ''
        this.runTips = []
        formData.runTips = 0
        if (formData.tipsType.length > 0) {
          if (formData.tipsType.indexOf(0) != -1) {
            formData.tipsType = 0
          } else {
            formData.tipsType = this.getSum(formData.tipsType)
          }
        }
        if (this.tipTimeList.length == 0) {
          formData.tipsDateTime = ''
        } else if (this.tipTimeList.length > 0) {
          formData.tipsDateTime = [...new Set(this.tipTimeList)].join('|')
        }
        if (this.tipEndDate) {
          formData.endDate = this.tipEndDate
        }
        formData.isShowTips = 1
        if (formData.showTips == 0) {
          formData.tipTimes = 0
        }
      }
      if (formData.markNonSst == 0 && formData.markBy >= 0) {
        formData.markBy = null
      }
    },
    showTipTimeOption() {
      this.isHide = true
      if (this.temp.tipsDateTime.length > 0) {
        this.tipTimeList = this.temp.tipsDateTime.split('|')
      } else {
        this.tipTimeList.push('08:30')
      }
    },
    deleteTime(index) {
      this.tipTimeList.splice(index, 1)
    },
    addTime(index) {
      this.tipTimeList.splice(index, 0, '08:30')
    },
    submitEnd(dlgStatus) {
      this.$emit('submitEnd', dlgStatus)
      this.$notify({
        title: this.$t('text.success'),
        message: dlgStatus === 'create' ? this.$t('text.createSuccess') : this.$t('text.updateSuccess'),
        type: 'success',
        duration: 2000
      })
    },
    enforceRunChange(val) {
      this.$refs['stgEndDate'].clearValidate()
    },
    tipsTypeChange(selections) {
      const selectedAllIndex = selections.indexOf(0)
      if (selections.length > 1) {
        if (selectedAllIndex === selections.length - 1 || selections.length === this.dayOptions.length - 1) {
          selections.splice(0, selections.length, 0)
        } else if (selectedAllIndex > -1) {
          selections.splice(selectedAllIndex, 1)
        }
      }
    },
    markNonSstChange(val) {
      if (val == 1 && this.temp.markBy == null) {
        this.temp.markBy = 2
      } else {
        this.temp.markBy = this.temp.markBy
      }
    },
    markByChange(val) {
      this.temp.markBy = val
    },
    contentRuleValidator(rule, value, callback) {
      if (!this.temp.contentStgId) {
        callback(new Error(this.$t('pages.sysAlarmConfig_text9')))
      } else {
        callback()
      }
    },
    scanDirValidator(rule, value, callback) {
      const reg = /^(([a-zA-Z]:\\((((?! )[^/:*?<>\""|\\]+)+\\?)|(\\)?))|(\/))[\s\S]*$/
      if (value) {
        // const scanDirs = this.temp.scanDir.split('|')
        let flag
        if (!reg.test(value)) {
          flag = true
        }
        if (flag && flag === true) {
          callback(new Error(this.$t('pages.diskScan_Msg25')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    stgEndDateValidator(rule, value, callback) {
      if (this.temp.enforceRun == 1 && !this.stgEndDate) {
        callback(new Error(this.$t('pages.diskScanSelfCheck_Msg7')))
      } else if (this.temp.enforceRun == 1 && this.stgEndDate < moment(new Date()).format('YYYY-MM-DD')) {
        callback(new Error(this.$t('pages.diskScanSelfCheck_Msg24')))
      } else {
        callback()
      }
    },
    tipEndDateValidator(rule, value, callback) {
      if (this.temp.enforceRun == 2 && !this.tipEndDate) {
        callback(new Error(this.$t('pages.diskScanSelfCheck_Msg7')))
      } else if (this.temp.enforceRun == 2 && this.tipEndDate < moment(new Date()).format('YYYY-MM-DD')) {
        callback(new Error(this.$t('pages.diskScanSelfCheck_Msg24')))
      } else {
        callback()
      }
    },
    tipsTypeValidator(rule, value, callback) {
      if (this.temp.enforceRun == 2 && this.temp.tipsType.length == 0) {
        callback(new Error(this.$t('pages.diskScanSelfCheck_Msg25')))
      } else {
        callback()
      }
    },
    tipsDateTimeValidator(rule, value, callback) {
      if (this.temp.enforceRun == 2 && this.tipTimeList.length == 0 && this.temp.tipsDateTime.length == 0) {
        callback(new Error(this.$t('pages.diskScanSelfCheck_Msg26')))
      } else {
        callback()
      }
    },
    tipTimesValidator(rule, value, callback) {
      if ((this.temp.enforceRun == 2 && this.isShowTips == 1 && this.temp.tipTimes == 0) ||
          (this.temp.enforceRun == 2 && this.isShowTips == 0) || (this.temp.enforceRun == 2 && this.temp.tipTimes == 0)) {
        callback(new Error(this.$t('pages.diskScanSelfCheck_Msg27')))
      } else {
        callback()
      }
    },
    enforceRunValidator(rule, value, callback) {
      if (this.temp.enforceRun == null) {
        callback(new Error(this.$t('pages.diskScanSelfCheck_Msg28')))
      } else {
        callback()
      }
    },
    markNonSstValidator(rule, value, callback) {
      if (this.temp.markNonSst == 0) {
        callback(new Error(this.$t('pages.diskScanSelfCheck_Msg29')))
      } else {
        callback()
      }
    },
    scanModeValidator(rule, value, callback) {
      if (this.temp.scanMode === null || this.temp.scanMode === undefined || this.temp.scanMode === '') {
        callback(new Error(this.$t('pages.diskScan_Msg27')))
      } else {
        callback()
      }
    },
    handleFileSuffixImport() {  // 从文件后缀库导入
      this.$refs.fileSuffixLibImport.show()
    },
    importFileSuffix(suffix) {
      const new_suffix = suffix.split('|')
      let union_suffix = [...new Set(this.temp.suffix.concat(new_suffix))].join('|')
      if (union_suffix.length > this.suffixMaxLength) {
        union_suffix = union_suffix.slice(0, this.suffixMaxLength + 1)
        union_suffix = union_suffix.slice(0, union_suffix.lastIndexOf('|'))
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('pages.suffixLengthOutOfLimit'),
          type: 'warning',
          duration: 2000
        })
      }
      this.temp.suffix = union_suffix.split('|')
    },
    suffixChange(list) {
      const newMap = new Map()
      //  自动添加前缀
      list.forEach(item => {
        item = item.trim()
        if (!item.startsWith('.')) {
          item = '.' + item
        }
        if (item.length >= 2) {
          newMap[item] = ''
        }
      })
      this.temp.suffix = Object.keys(newMap) || [];
    },
    handleClear() {
      this.temp.suffix.splice(0)
    }
  }
}
</script>

<style lang="scss" scoped>
  .specilClass >>>.el-form-item__label{
    color: #666 !important;
    font-weight: 500;
  }
  .defaultClass >>>.el-form-item__label{
    color: #888 !important;
    font-weight: 500;
  }
  .edit-item{
    width: 400px;
    padding: 5px 7px;
    margin: 5px 0 0;
    border: 1px solid #aaa;
  }
  >>>.el-dialog__body {
    .el-checkbox__label, .el-radio__label {
      overflow-wrap: break-word;
      white-space: initial;
    }
  }
  .clear-btn {
    width: 42px;
    height: 30px;
    margin: 2px 0 0;
    vertical-align: top;
    text-align: center;
  }
</style>
