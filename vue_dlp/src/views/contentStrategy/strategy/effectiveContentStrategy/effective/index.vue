<template>
  <div style="width:100%">
    <el-divider content-position="left">{{ $t('pages.outfileConfig') }}</el-divider>
    <div style="margin-left:40px">
      <el-button v-if="formable" type="text" @click="selectAll(true, 'out')">{{ $t('button.selectAll') }}</el-button>
      <el-button v-if="formable" type="text" @click="selectAll(false, 'out')">{{ $t('button.cancelSelectAll') }}</el-button>
      <el-row>
        <el-col v-for="item in configList.out" :key="item.value" :span="item.detail ? 24 : 12">
          <el-checkbox v-model="temp[item.key]" :disabled="!formable || configDisabled(item.value)" :true-label="item.value" :false-label="0">
            {{ item.label }}
            <i v-if="item.value === 1010 || item.value === 1001" class="el-icon-setting" style="color: #454545; cursor: pointer;" @click.stop.prevent="()=> $set(item, 'detail', !item.detail)" />
          </el-checkbox>
          <el-tooltip v-if="item.value === 1003" class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.effectiveContent_text26') }}<br/>
              {{ $t('pages.effectiveContent_text29') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-tooltip v-if="item.value === 1004" class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.effectiveContent_text27') }}<br/>
              {{ $t('pages.effectiveContent_text29') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-tooltip v-if="item.value === 1016" class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.effectiveContent_text13') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <!--<el-tooltip v-if="item.value === 1001" class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.effectiveContent_text7') }}<br/>
              {{ $t('pages.effectiveContent_text8') }}<br/>
              {{ $t('pages.effectiveContent_text9') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>-->
          <el-tooltip v-if="item.value === 1017" class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.effectiveContent_text32') }}<br/>
              {{ $t('pages.effectiveContent_text33') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <div v-if="item.detail && item.value === 1010" title="本地共享文件" class="config-detail-container">
            <FormItem :label="$t('pages.effectiveContent_text23')" :tooltip-content="$t('pages.effectiveContent_text24')" tooltip-placement="bottom-start">
              <el-radio-group v-model="shareStgConfig.blockType" :disabled="!formable">
                <el-radio :label="1">{{ $t('pages.blockType1') }}</el-radio>
                <el-radio :label="2">{{ $t('pages.blockType2') }}</el-radio>
              </el-radio-group>
            </FormItem>
          </div>
          <div v-if="item.detail && item.value === 1001" title="光盘刻录" class="config-detail-container">
            <FormItem :label="$t('pages.effectiveContent_text23')">
              <el-radio-group v-model="temp.cdBurnType" :disabled="!formable">
                <el-radio :label="1">{{ $t('pages.cdBurnType1') }}</el-radio>
                <el-radio :label="0">{{ $t('pages.cdBurnType2') }}</el-radio>
              </el-radio-group>
            </FormItem>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-divider content-position="left">{{ $t('pages.downloadOrReceiveConfig') }}</el-divider>
    <div style="margin-left:40px">
      <el-button v-if="formable" type="text" @click="selectAll(true, 'download')">{{ $t('button.selectAll') }}</el-button>
      <el-button v-if="formable" type="text" @click="selectAll(false, 'download')">{{ $t('button.cancelSelectAll') }}</el-button>
      <el-row>
        <el-col v-for="item in configList.download" :key="item.value" :span="12">
          <el-checkbox v-model="temp[item.key]" :disabled="!formable || configDisabled(item.value)" :true-label="item.value" :false-label="0">
            {{ item.label }}
          </el-checkbox>
          <el-tooltip v-if="item.value === 1019" class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.effectiveContent_text34') }}<br/>
              {{ $t('pages.effectiveContent_text29') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-tooltip v-if="item.value === 1015" class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.effectiveContent_text28') }}<br/>
              {{ $t('pages.effectiveContent_text30') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-tooltip v-if="item.value === 1018" class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.effectiveContent_text14') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { getOutgoingConfigDict, getDownloadConfigDict, getContentStgConfigDict } from '@/utils/dictionary'

export default {
  name: 'Effective',
  components: {},
  directives: {},
  props: {
    formable: { // 能否提交表单
      type: Boolean,
      default: true
    },
    temp: {
      type: Object,
      default: function() {
        return {}
      }
    },
    lossTypeOptions: {
      type: Array,
      default: function() {
        return []
      }
    },
    shareStgConfig: {
      type: Object,
      default: function() {
        return {
          blockType: 1
        }
      }
    }
  },
  data() {
    return {
      moduleIds: [],
      configList: {},
      showDetail1010: false // 显示本地共享文件的特殊配置
    }
  },
  computed: {
    // 计算得到 销售模块是否禁用 的 map， 31: 网页浏览管控, 34: 邮件管控, 36: 即时通讯管控, 19: 共享管控, 17: 便携设备连接管控, 32: 网络管控, 35: 打印管控, 16: 存储设备管控（U盘管控）, 18: 刻录管控
    moduleDisabled() {
      // 默认值 true 禁用
      const moduleMap = { 31: true, 34: true, 36: true, 19: true, 35: true, 16: true, 18: true, 17: true, 32: true }
      this.moduleIds.forEach(id => {
        if (moduleMap[id]) {
          // 开通的销售模块设置为 不禁用
          moduleMap[id] = false
        }
      })
      return moduleMap
    },
    // 各个配置 对应的 销售模块 map
    configType() {
      return {
        // 1005: 访问网页文本内容 1007: 网页粘贴文本内容 1006: 网页上传文件 1009: 论坛发帖文本内容;  31: 网页浏览管控
        1005: 31, 1006: 31, 1007: 31, 1009: 31,
        // 1008: 邮件传输文本内容 1012: 邮件附件内容;  34: 邮件管控
        1008: 34, 1012: 34,
        // 1003: 即时通讯发送文本内容 1004: 即时通讯发送文件 1019: 即时通讯接收文本内容 1015: 即时通讯下载文件;  36: 即时通讯管控
        1003: 36, 1004: 36, 1015: 36, 1019: 36,
        // 1010: 本地共享文件 1011: 远程共享文件外发;  19: 共享管控
        1010: 19, 1011: 19,
        // 1014: 蓝牙外发文件 1020: ADB外发文件 1021: MTP外发文件;  17: 便携设备连接管控
        1014: 17, 1020: 17, 1021: 17,
        // 1016: 网盘上传文件 1017: FTP上传文件 1018: 网盘下载文件  1024 远程桌面管控销售 1025 远程工具上传文件 1026 AI传输文本内容; 1027 AI上传文件   32: 网络管控 
        1016: 32, 1017: 32, 1018: 32, 1024: 32, 1025: 32, 1026: 32, 1027: 32,
        // 1013: 文件打印内容;  35: 打印管控; 1002: USB文件外发;  16: 存储设备管控（U盘管控）; 1001: 光盘刻录文件;  18: 刻录管控; 1022: 光盘下载文件; 18: 刻录管控
        1013: 35, 1002: 16, 1001: 18, 1022: 18
      }
    }
  },
  created() {
    // 更新 store 中的销售模块id
    this.$store.dispatch('commonData/setSaleModuleIds')
    this.configList = getContentStgConfigDict()
  },
  methods: {
    // 未开通销售模块的配置则禁用
    configDisabled(id) {
      const type = this.configType[id]
      return this.moduleDisabled[type]
    },
    // 设置勾选状态
    listModule() {
      const moduleIds = [...this.$store.getters.saleModuleIds]
      if (this.moduleIds.toString() != moduleIds.toString()) {
        this.moduleIds.splice(0, this.moduleIds.length, ...moduleIds)
      }
      const selectOpt = { ...getOutgoingConfigDict(), ...getDownloadConfigDict() }
      for (const key in selectOpt) {
        if (Object.hasOwnProperty.call(selectOpt, key)) {
          const id = selectOpt[key]
          // 配置未勾选 或 未开通销售模块的配置，则将值设为 0
          if (!this.temp[key] || this.configDisabled(id)) {
            selectOpt[key] = 0
          }
        }
      }
      Object.assign(this.temp, selectOpt)
    },
    /**
    * 全选/取消全选的方法
    * @param boolean true为全选，false为取消全选
    * @param type 'out': 外发配置的选项，'download'：下载配置的选项
    */
    selectAll(boolean, type) {
      // 获取配置的字典，默认为开启
      const selectOpt = type == 'out' ? getOutgoingConfigDict() : getDownloadConfigDict()
      if (!boolean) {
        // 取消全选
        for (const key in selectOpt) {
          if (Object.hasOwnProperty.call(selectOpt, key)) {
            selectOpt[key] = 0
          }
        }
      } else {
        // 全选，需要剔除 未开通的销售模块 对应的配置
        for (const key in selectOpt) {
          if (Object.hasOwnProperty.call(selectOpt, key)) {
            const id = selectOpt[key]
            // 未开通销售模块的配置不允许勾选
            if (this.configDisabled(id)) {
              selectOpt[key] = 0
            }
          }
        }
      }
      Object.assign(this.temp, selectOpt)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-row {
  line-height: 30px;
}
.el-icon-info {
  position: relative;
  top: -10px;
}
.config-detail-container {
  margin: 0px 22px;
  border: 1px solid #d5d3d3;
}
</style>

