<template>
  <div>
    <div v-permission="'114'">
      <el-divider content-position="left">{{ $t('pages.OCRImage') }}</el-divider>
      <FormItem :label="$t('pages.effectiveContent_text10')">
        <el-radio-group v-model="effectiveConfig.ocrCheckImg" :disabled="!formable">
          <el-radio :label="1">{{ $t('pages.ocrCheckImg1') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.ocrCheckImg2') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem :label="$t('pages.effectiveContent_text11')">
        <el-radio-group v-model="effectiveConfig.orcCheckDoc" :disabled="!formable">
          <el-radio :label="1">{{ $t('pages.ocrCheckImg1') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.ocrCheckImg2') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </div>

    <!--    <div v-permission="'114'">
      <el-divider content-position="left">{{ $t('pages.OCRFileContent') }}</el-divider>
      <FormItem :label="$t('pages.tamperProofFileSuffix')" :tooltip-content="$t('pages.tamperProofFileSuffixTip')">
        <el-radio-group v-model="effectiveConfig.ocrCheckContent" :disabled="!formable">
          <el-radio :label="1">{{ $t('text.open') }}</el-radio>
          <el-radio :label="0">{{ $t('text.close') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </div>-->

    <div>
      <el-divider content-position="left">{{ $t('pages.effectiveContent_text12') }}</el-divider>
      <FormItem :label="$t('pages.effectivechooseRuleGroup')" :tooltip-content="$t('pages.effectiveaddRuleGroup')" tooltip-placement="bottom-start" prop="timeout" :rules="[{ validator: requireSpecial, trigger: 'blur' }]">
        <el-col :span="6">
          <el-input-number v-model="effectiveConfig.timeout" :max="10" :min="1" step-strictly :step="1" :controls="false" :disabled="!formable"/>
        </el-col>
        <el-col :span="3" style="margin-left: 5px;">
          <span>{{ $t('text.second') }}</span>
        </el-col>
      </FormItem>
      <FormItem :label="$t('pages.effectiveContent_text15')" >
        <el-radio-group v-model="effectiveConfig.ifTimeout" :disabled="!formable" @input="validateViolationResp(arguments[0], 'ifTimeoutRuleId')">
          <el-radio :label="0">{{ $t('pages.ifTimeout1') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.ifTimeout2') }}
            <el-tooltip class="item" effect="dark" placement="bottom" content="内容检测策略或零星检测策略所配置的响应规则有勾选阻止数据外发以及泄露方式，才能触发检测超时阻断">
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>

        </el-radio-group>
      </FormItem>
      <div class="error-skew">
        <FormItem :label="$t('table.respond')" prop="ifTimeoutRuleId" :rules="[{ validator: requireIfTimeoutRuleId, trigger: 'blur' }]">
          <response-content
            ref="timeoutRuleResp"
            show-select
            prop-check-rule
            read-only
            :show-check-rule="false"
            :check-empty-rule="effectiveConfig.ifTimeout == 1"
            :editable="formable && effectiveConfig.ifTimeout == 1"
            :prop-rule-id="effectiveConfig.ifTimeoutRuleId"
            @getRuleId="getIfTimeoutRuleId"
          >
            <el-checkbox
              slot="tail"
              v-model="effectiveConfig.ifTimeoutOutSendApply"
              :disabled="!formable || effectiveConfig.ifTimeout == 0"
              class="out-going ellipsis"
              :title="$t('pages.respondActions10')"
            >{{ $t('pages.respondActions10') }}</el-checkbox>
          </response-content>
        </FormItem>
      </div>
      <span style="color: #0c60a5;margin-left:40px">
        {{ $t('pages.effectiveContent_text16') }}
      </span>
    </div>
    <!-- 敏感检测带密码配置-无法识别的文件 -->
    <div>
      <el-divider content-position="left">{{ $t('pages.effectivePasswordConfig') }}</el-divider>
      <FormItem :label="$t('pages.unrecognizedFile')">
        <el-radio-group v-model="effectiveConfig.unrecognizedFile" :disabled="!formable" @input="validateViolationResp(arguments[0], 'unrecognizedFileRuleId')">
          <el-radio :label="0">{{ $t('pages.ifTimeout1') }}</el-radio>
          <el-radio :label="1">{{ $t('pages.ifTimeout2') }}
            <el-tooltip class="item" effect="dark" placement="bottom" content="内容检测策略或零星检测策略所配置的响应规则有勾选阻止数据外发以及泄露方式，才能触发带密码口令文件阻断">
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </FormItem>
      <div class="error-skew">
        <FormItem :label="$t('table.respond')" prop="unrecognizedFileRuleId" :rules="[{ validator: requireUnrecognizedFileRuleId, trigger: 'blur' }]">
          <response-content
            ref="unrecognizedFileRuleResp"
            show-select
            prop-check-rule
            read-only
            :show-check-rule="false"
            :check-empty-rule="effectiveConfig.unrecognizedFile == 1"
            :editable="formable && effectiveConfig.unrecognizedFile == 1"
            :prop-rule-id="effectiveConfig.unrecognizedFileRuleId"
            @getRuleId="getUnrecognizedFileRuleId"
          >
            <el-checkbox
              slot="tail"
              v-model="effectiveConfig.unrecognizedFileOutSendApply"
              :disabled="!formable || effectiveConfig.unrecognizedFile == 0"
              class="out-going ellipsis"
              :title="$t('pages.respondActions10')"
            >{{ $t('pages.respondActions10') }}</el-checkbox>
          </response-content>
        </FormItem>
      </div>
      <span style="color: #0c60a5;margin-left:40px">
        {{ $t('pages.respondActions14') }}
      </span>
      <br/>
      <span style="color: #0c60a5;margin-left:82px">
        {{ $t('pages.respondActions15') }}
      </span>
    </div>
    <!-- 2022年 q3版本修改，去掉该选项<div>
      <el-divider content-position="left">{{ $t('pages.effectiveContent_text17') }}</el-divider>
      <FormItem :label="$t('pages.effectivechooseRespond')" :tooltip-content="$t('pages.effectiveaddRespond')" label-width="210px">
        <el-radio-group v-model="effectiveConfig.encFileOutSend" :disabled="!formable">
          <el-radio :label="1">{{ $t('pages.allow') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.notAllow') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </div> -->
    <!--<el-card class="box-card" style="background-color: transparent;">
      <div slot="header" style="">
        <span>以下参数，仅在高级检测时生效</span>
      </div>
      <FormItem label="检测文件阈值" tooltip-content="根据文本大小判定是否提交高级检测" prop="fileLimit" :rules="[{ validator: this.fileLimitRequire, trigger: 'blur' }]" label-width="210px">
        <el-col :span="6">
          <el-input-number v-model="effectiveConfig.fileLimit" :max="9999" :min="0" step-strictly :step="1" :controls="false" :disabled="!formable"/>
        </el-col>
        <el-col :span="3">
          <span>&nbsp;MB</span>
        </el-col>
        <el-col :span="9">
          <span style="color: #2b7aac;">{{ $t('pages.processMonitor_Msg2') }}</span>
        </el-col>
      </FormItem>
    </el-card>-->
    <div>
      <el-divider content-position="left">{{ $t('pages.effectiveContent_text35') }}</el-divider>
      <FormItem :label="$t('pages.effectiveContent_text36')" :tooltip-content="$t('pages.effectiveContent_text37')" tooltip-placement="bottom-start">
        <el-radio-group v-model="effectiveConfig.isSensitiveContent" :disabled="!formable">
          <el-radio :label="1">{{ $t('pages.display') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.noDisplay') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem :label="$t('pages.sensitiveContentTruncationLength')" :tooltip-content="$t('pages.effectiveContent_text38')" tooltip-placement="bottom-start" prop="timeout" :rules="[{ validator: requireSpecial, trigger: 'blur' }]">
        <el-col :span="6">
          <el-input-number v-model="effectiveConfig.sensitiveContentLength" :max="100" :min="0" step-strictly :step="1" :controls="false" :disabled="!formable"/>
        </el-col>
        <el-col :span="3" style="margin-left: 5px;">
          <span>{{ $t('pages.effectiveContent_text39') }}</span>
        </el-col>
      </FormItem>
    </div>
    <div>
      <el-divider content-position="left">{{ $t('pages.archiveCheckConfig') }}</el-divider>
      <FormItem :label="$t('pages.effectiveContent_text40')">
        <el-radio-group v-model="effectiveConfig.continueCheck" :disabled="!formable">
          <el-radio :label="1">{{ $t('pages.continueCheck') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.stopCheck') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </div>
    <div>
      <el-divider content-position="left">{{ $t('pages.effectiveContent_text20') }}</el-divider>
      <!-- 这个q2版本实现方式提到上层钩子实现，这个配置没有使用了；控制台帮忙做个隐藏处理 -->
      <div v-show="false">
        <FormItem :label="$t('pages.limitFileSize')" prop="limitFileSize" >
          <el-col :span="6">
            <el-input-number v-model="shareStgConfig.limitFileSize" :disabled="!formable || !temp.isRemoteDir" :controls="false" :min="0" :max="999999" size="mini"/>
          </el-col>
          <el-col :span="3">
            <span>&nbsp;MB</span>
          </el-col>
          <el-col :span="9">
            <span style="color: #2b7aac;">{{ $t('pages.processMonitor_Msg2') }}</span>
          </el-col>
        </FormItem>
        <FormItem :label="$t('pages.effectiveContent_text21')" :tooltip-content="$t('pages.effectiveContent_text22')" label-width="210px" tooltip-placement="bottom-start">
          <el-radio-group v-model="shareStgConfig.fileLimitType" :disabled="!formable || !temp.isRemoteDir">
            <el-radio :label="1">{{ $t('pages.ifTimeout1') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.ifTimeout2') }}</el-radio>
          </el-radio-group>
        </FormItem>
      </div>
      <FormItem :label="$t('pages.effectiveContent_text23')" :tooltip-content="$t('pages.effectiveContent_text24')" tooltip-placement="bottom-start">
        <el-radio-group v-model="shareStgConfig.blockType" :disabled="!formable || !temp.isLocalDir">
          <el-radio :label="1">{{ $t('pages.blockType1') }}</el-radio>
          <el-radio :label="2">{{ $t('pages.blockType2') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </div>
    <div>
      <el-divider content-position="left">{{ $t('pages.effectiveContent_text25') }}</el-divider>
      <FormItem :label="$t('pages.cdBurnType')">
        <el-radio-group v-model="temp.cdBurnType" :disabled="!temp.isCDBurn || !formable">
          <el-radio :label="1">{{ $t('pages.cdBurnType1') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.cdBurnType2') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </div>
  </div>
</template>
<script>
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent';

export default {
  name: 'Advanced',
  components: { ResponseContent },
  directives: {},
  props: {
    formable: { // 能否提交表单
      type: Boolean,
      default: true
    },
    temp: {
      type: Object,
      default() {
        return {}
      }
    },
    effectiveConfig: {
      type: Object,
      default: function() {
        return {
          id: undefined,
          name: '',
          active: false,
          remark: '',
          entityType: undefined,
          entityId: undefined,
          effectiveContStgId: undefined,
          option: [],
          ocrCheckImg: 1, // ocr检测图片文件
          orcCheckDoc: 1, // ocr检测文档中的图片
          ocrCheckContent: 1, // ocr根据内容识别文件类型
          timeout: 10,
          ifTimeout: 0,
          // 内容检测超时的响应规则ID
          ifTimeoutRuleId: undefined,
          ifTimeoutOutSendApply: false,
          fileLimit: 40,
          encFileOutSend: 0,
          // 是否显示敏感内容概要
          isSensitiveContent: 0,
          // 敏感检测带密码配置-无法识别的文件
          unrecognizedFile: 1,
          // 敏感检测带密码配置-无法识别的文件的响应规则ID
          unrecognizedFileRuleId: undefined,
          unrecognizedFileOutSendApply: false,
          // 检测到敏感时是否继续检测
          continueCheck: 0,
          // 检测到敏感时截断关键词前后n个字符作为敏感内容概要
          sensitiveContentLength: 0
        }
      }
    },
    shareStgConfig: {
      type: Object,
      default: function() {
        return {
          id: undefined,
          name: '',
          active: false,
          remark: '',
          entityType: undefined,
          entityId: undefined,
          effectiveContStgId: undefined,
          blockType: 1,
          fileLimitType: 1,
          limitFileSize: 100,
          ocrEnable: 1
        }
      }
    }
  },
  data() {
    return {
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    createInit() {
      if (this.$refs['timeoutRuleResp']) {
        this.$refs['timeoutRuleResp'].isShow = false
      }
      if (this.$refs['unrecognizedFileRuleResp']) {
        this.$refs['unrecognizedFileRuleResp'].isShow = false
      }
    },
    requireSpecial(rule, value, callback) {
      if (!this.effectiveConfig.timeout) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    fileLimitRequire(rule, value, callback) {
      if (!this.effectiveConfig.fileLimit) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    requireIfTimeoutRuleId(rule, value, callback) {
      if (!this.effectiveConfig.ifTimeoutRuleId && this.effectiveConfig.ifTimeout == 1) {
        // 响应规则有错误提示信息，这边只校验，不显示信息
        callback(new Error(' '))
      }
      callback()
    },
    requireUnrecognizedFileRuleId(rule, value, callback) {
      if (!this.effectiveConfig.unrecognizedFileRuleId && this.effectiveConfig.unrecognizedFile == 1) {
        // 响应规则有错误提示信息，这边只校验，不显示信息
        callback(new Error(' '))
      }
      callback()
    },
    getIfTimeoutRuleId(value) {
      this.effectiveConfig.ifTimeoutRuleId = value
      this.$emit('validate', 'ifTimeoutRuleId')
    },
    getUnrecognizedFileRuleId(value) {
      this.effectiveConfig.unrecognizedFileRuleId = value
      this.$emit('validate', 'unrecognizedFileRuleId')
    },
    validateViolationResp(val, propName) {
      if (val == 0) {
        this.$emit('validate', propName)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
 .error-skew {
   >>>.el-form-item__error {
     margin-left: 26px;
   }
 }
 .out-going {
  max-width: 200px;
  margin-left: 20px;
  vertical-align: middle;
  >>>.el-checkbox__label {
    white-space: nowrap;
  }
 }
</style>
