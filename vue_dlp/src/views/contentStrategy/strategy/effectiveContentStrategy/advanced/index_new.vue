<template>
  <div>
    <el-card class="box-card" style="background-color: transparent;">
      <div slot="header" style="">
        <span>OCR图像识别配置</span>
      </div>
      <FormItem v-for="(item, i) in ocrOptions" :key="i" :label="item.label" label-width="180px">
        <el-radio-group v-model="orcChecked[item.id]" :disabled="!formable">
          <el-radio :label="1">检测</el-radio>
          <el-radio :label="0">不检测</el-radio>
        </el-radio-group>
      </FormItem>
    </el-card>
    <el-card class="box-card" style="background-color: transparent;">
      <div slot="header" style="">
        <span>以下参数，仅在敏感内容检测配置-共享目录监控开关开启时才会生效</span>
      </div>

      <FormItem label="共享文件阈值" prop="limitFileSize" label-width="180px">
        <el-col :span="6">
          <el-input-number v-model="shareStgConfig.limitFileSize" :disabled="!formable || !temp.isRemoteDir" :controls="false" :min="0" :max="999999" size="mini"/>
        </el-col>
        <el-col :span="3">
          <span>&nbsp;MB</span>
        </el-col>
        <el-col :span="9">
          <span style="color: #2b7aac;">0表示不限制</span>
        </el-col>
      </FormItem>
      <FormItem label="超过阈值处理方式" tooltip-content="只对远程共享内容生效" label-width="180px">
        <el-radio-group v-model="shareStgConfig.fileLimitType" :disabled="!formable || !temp.isRemoteDir">
          <el-radio :label="1">放行</el-radio>
          <el-radio :label="2">阻断</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem label="敏感检测违规阻断方式" tooltip-content="只对本地共享内容生效" label-width="180px">
        <el-radio-group v-model="shareStgConfig.blockType" :disabled="!formable || !temp.isLocalDir">
          <el-radio :label="1">禁用所有共享</el-radio>
          <el-radio :label="2">取消包含敏感内容共享目录</el-radio>
        </el-radio-group>
      </FormItem>
    </el-card>
    <el-card class="box-card" style="background-color: transparent;">
      <div slot="header" style="">
        <span>以下参数，仅在敏感内容检测配置-光盘刻录内容开关开启时才会生效</span>
      </div>

      <FormItem label="阻断类型" label-width="80px">
        <el-radio-group v-model="temp.cdBurnType" :disabled="!temp.isCDBurn || !formable">
          <el-radio :label="1">全部阻断</el-radio>
          <el-radio :label="0">部分阻断</el-radio>
        </el-radio-group>
      </FormItem>
    </el-card>
  </div>
</template>
<script>
export default {
  name: 'Advanced',
  components: {},
  directives: {},
  props: {
    formable: { // 能否提交表单
      type: Boolean,
      default: true
    },
    temp: {
      type: Object,
      default: function() {
        return {}
      }
    },
    shareStgConfig: {
      type: Object,
      default: function() {
        return {
          id: undefined,
          effectiveContStgId: undefined,
          blockType: 1,
          fileLimitType: 1,
          limitFileSize: 100
        }
      }
    },
    effectiveConfig: {
      type: Object,
      default: function() {
        return {
          id: undefined,
          effectiveContStgId: undefined,
          option: []
        }
      }
    }
  },
  data() {
    return {
      ocrOptions: [
        { id: 1001, label: '检测图片文件' },
        { id: 1002, label: '检测文档中的图片' }
      ],
      orcChecked: {}
    }
  },
  watch: {
    orcChecked(val) {
      for (let i = 0; i < this.ocrOptions.length; i++) {
        const option = this.ocrOptions[i]
        const value = !val[option.id] ? 0 : 1
        this.effectiveConfig.option[i] = { id: option.id, value: value }
      }
    },
    effectiveConfig(val) {
      this.initOcrOption(val)
    }
  },
  created() {
    this.initOcrOption(this.effectiveConfig)
  },
  methods: {
    initOcrOption(val) {
      // 设置默认值
      for (let i = 0; i < this.ocrOptions.length; i++) {
        const option = this.ocrOptions[i]
        this.orcChecked[option.id] = 1
      }
      // 设置修改的值
      if (val.option && val.option.length > 0) {
        for (let i = 0; i < val.option.length; i++) {
          const option = val.option[i]
          this.orcChecked[option.id] = option.value
        }
        this.$nextTick(() => {
          this.orcChecked.reverse()
        })
      }
    }
  }
}
</script>
