<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;" v-html="strategyFormatter(props.detail)"></span>
          </div>
        </template>
      </grid-table>
    </div>

    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="closed"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="170px"
        style="width: 710px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="temp"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
          label-width="100px"
        />
        <FormItem :label="$t('table.stgName')" prop="name" label-width="100px">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="20" />
        </FormItem>
        <FormItem :label="$t('table.remark')" label-width="100px">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit class="word-limit-3digits" />
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')" label-width="100px">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-tabs ref="tabs" v-model="activeName" :before-leave="changeTab" @tab-click="tabClick">
          <el-tab-pane :label="$t('pages.basicSetting')" name="effective">
            <effective ref="effective" :formable="formable" :temp="temp" :loss-type-options="lossTypeOptions" :share-stg-config="shareStgConfig"/>
          </el-tab-pane>
          <el-tab-pane :label="$t('pages.exceptSet')" name="except">
            <except
              ref="except"
              :formable="formable"
              :temp="temp"
              :row-datas="exceptStrategy.exceptDetail"
            />
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importEffectiveFunctionStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import { getStrategyPage, getStrategyByName, createStrategy, updateStrategy, deleteStrategy,
  getShareStgConfig, getExceptStrategy
} from '@/api/contentStrategy/strategy/effectiveContentStrategy'
import { getByIds, getByUrls } from '@/api/system/baseData/urlLibrary'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity,
  objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { getOutgoingConfigDict, getDownloadConfigDict, getContentStgConfigDict } from '@/utils/dictionary'
import { stgActiveIconFormatter, stgEntityIconFormatter } from '@/utils/formatter'
import { validatePolicy } from '@/utils/validate'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import effective from './effective'
import except from './except'

export default {
  name: 'EffectiveContentStrategy',
  components: { effective, except, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 22,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'keyword', label: 'stgMessage', ellipsis: false, type: 'popover', width: '200', originData: true, formatter: this.strategyFormatter },
        // { prop: 'keyword', label: 'stgMessage', width: '200', formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { prop: 'createdTime', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段, 外发配置、下载配置从字典文件中读取
        id: undefined,
        name: '',
        active: false,
        remark: '',
        cdBurnType: 1,
        entityType: undefined,
        entityId: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.sensitiveContentDetectionStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.sensitiveContentDetectionStg'), 'create')
      },
      slotName: 'all',
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      activeName: 'effective',
      shareStgConfig: {},
      defaultShareStgConfig: {
        id: null,
        name: '',
        active: false,
        remark: '',
        entityType: null,
        entityId: null,
        effectiveContStgId: null,
        blockType: 1,
        fileLimitType: 1,
        limitFileSize: 100,
        ocrEnable: 1
      },
      exceptStrategy: {},
      defaultExceptStrategy: {
        id: null,
        name: '',
        active: false,
        remark: '',
        entityType: null,
        entityId: null,
        effectiveContStgId: null,
        exceptDetail: [],
        exceptSensiDetectByBrowser: {},
        exceptSensiDetectByURL: []
      },
      lossTypeOptions: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    configList() {
      return [...getContentStgConfigDict().out, ...getContentStgConfigDict().download]
    },
    // 外发配置 map: { key: value }
    outgoingConfig() {
      return getOutgoingConfigDict()
    },
    // 下载配置 map: { key: value }
    downloadConfig() {
      return getDownloadConfigDict()
    }
  },
  created() {
    // 合并到defaultTemp
    Object.assign(this.defaultTemp, this.outgoingConfig, this.downloadConfig)
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // this.getSensitiveLossType()
    this.$store.dispatch('commonData/setSaleModuleIds')
  },
  methods: {
    slotChange(name) {
      this.slotName = name
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    // getSensitiveLossType() {
    //   getSensitiveLossType({ type: 2 }).then(res => {
    //     this.lossTypeOptions = res.data
    //   })
    // },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    getShareStgConfig: function(superiorId) {
      getShareStgConfig(superiorId).then(res => {
        this.shareStgConfig = Object.assign(this.shareStgConfig, res.data)
      })
    },
    getExceptStrategy: function(superiorId) {
      getExceptStrategy(superiorId).then(async res => {
        this.exceptStrategy = Object.assign(this.exceptStrategy, res.data)
        await this.initFormatExceptStrategy()
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    tabClick(pane, event) {
      if (pane.name == 'except') {
        this.$refs.except.loadTreeData()
      }
    },
    changeTab(activeName, oldActiveName) {
      if (activeName === 'except') {
        this.$refs[activeName].resetDetail()
      }
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.activeName = 'effective'
      this.temp = Object.assign({}, this.defaultTemp)
      this.shareStgConfig = JSON.parse(JSON.stringify(this.defaultShareStgConfig))
      this.exceptStrategy = JSON.parse(JSON.stringify(this.defaultExceptStrategy))
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['effective'].listModule()
        this.$refs['dataForm'].clearValidate()
      })
    },
    sleep(millisecond) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve()
        }, millisecond)
      })
    },
    handleUpdate: async function(row) {
      if (!this.formable) {
        //  延迟0.3秒，等待created的数据加载完毕
        await this.sleep(300);
      }
      this.resetTemp()
      // 兼容旧策略找不到新属性时，勾选框无法勾选问题
      const selectOpt = { ...this.outgoingConfig, ...this.downloadConfig }
      for (const key in selectOpt) {
        if (row[key] == undefined) {
          this.$set(row, key, 0)
        }
      }
      this.temp = Object.assign({}, row) // copy obj
      this.getShareStgConfig(this.temp.id)
      this.getExceptStrategy(this.temp.id)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['effective'].listModule()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleShow: function(data) {
      this.resetTemp()
      this.temp = Object.assign({}, data.effectiveContentStg) // copy obj
      this.shareStgConfig = Object.assign(this.exceptStrategy, data.shareStgConfig)
      this.exceptStrategy = Object.assign(this.exceptStrategy, data.exceptStrategy)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['effective'].listModule()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate(async(valid, objs) => {
        if (valid) {
          const exceptStrategy = await this.formatExceptStrategy(this.exceptStrategy)
          createStrategy({
            effectiveContentStg: this.temp,
            shareStgConfig: this.shareStgConfig,
            exceptStrategy: exceptStrategy
          }).then(res => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.strategyCreate'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.validFailSkip(objs)
          this.submitting = false
        }
      })
    },
    closed() {
      this.$refs['except'].$refs['exceptForm'] && this.$refs['except'].$refs['exceptForm'].clearValidate()
    },
    async formatExceptStrategy(exceptStrategy) {
      exceptStrategy = JSON.parse(JSON.stringify(exceptStrategy))
      //  获取只包含url策略的数据
      const exceptDetailUrl = exceptStrategy.exceptDetail.filter(item => {
        return item.bussType == 1006
      }) || []
      exceptStrategy.exceptDetail = exceptStrategy.exceptDetail.filter(item => {
        return item.bussType != 1006
      });
      if (exceptDetailUrl.length > 0) {
        exceptStrategy.exceptSensiDetectByBrowser = {
          detectRules: 1,
          fileActions: 0 //  当前该fileActions属性未生效，默认填0
        }
        exceptStrategy.exceptSensiDetectByURL = [];

        //  获取 urlIds
        const urlIds = exceptDetailUrl.filter(item => item.urlIds).map(item => item.urlIds)
        const urlMap = {}
        //  根据urlIds获取网址
        if (urlIds.length > 0) {
          const res = await getByIds({ ids: urlIds.join(',') });
          (res.data || []).forEach(url => {
            urlMap[url.id] = url.address || '';
          })
        }
        exceptDetailUrl.forEach(item => {
          if (item.urlIds) {
            const urlIds = item.urlIds.split(',');
            urlIds.forEach(urlId => {
              const url = urlMap[urlId]
              if (url) {
                //  当前仅支持模糊查询：matchKind:0
                //  若为bussType=1006即网页上传文件（过滤URL）    转换opeType的值, 当前网页文件上传的例外操作只支持 1：上传文件敏感不检测，2：上传文件敏感检测但不阻断
                const opeType = item.opeType == 1 ? 1 : item.opeType == 4 ? 2 : null
                exceptStrategy.exceptSensiDetectByURL.push({ addId: item.id, url: url, matchKind: 0, opeType: opeType, urlId: urlId })
              }
            })
          }
        });
      } else {
        exceptStrategy.exceptSensiDetectByURL = []
        exceptStrategy.exceptSensiDetectByBrowser = {}
      }
      return exceptStrategy;
    },
    async initFormatExceptStrategy() {
      if (this.exceptStrategy && this.exceptStrategy.exceptSensiDetectByURL) {
        //  获取所有的url
        const urls = this.exceptStrategy.exceptSensiDetectByURL.map(item => item.url)

        const urlMap = {};
        //  根据url查询网址信息
        if (urls.length > 0) {
          const res = await getByUrls(urls);
          (res.data || []).forEach(url => {
            urlMap[url.address] = url
          })
        }

        this.exceptStrategy.exceptSensiDetectByURL.forEach(item => {
          const urlData = urlMap[item.url]
          const url = urlData ? `${urlData.name}(${urlData.address})` : ''
          if (url) {
            const exceptUrl = this.exceptStrategy.exceptDetail.find(t => {
              return t.id === item.addId
            })
            if (exceptUrl) {
              exceptUrl.names += ',' + url
              exceptUrl.urlIds += ',' + urlData.id
            } else {
              //  由于原有例外设置中例外操作类型操作类型的值为 1：不检测，3：不告警，4：不阻断，浏览器url过滤的例外设置中1：表示上传文件敏感不检测，2：上传文件敏感检测但不阻断，4：下载文件敏感不检测，8:表示下载文件敏感检测但不阻断
              const opeType = item.opeType == 1 ? 1 : item.opeType == 2 ? 4 : null;
              //  exceptType = 4: 代表网址
              this.exceptStrategy.exceptDetail.push({
                id: item.addId,
                bussType: 1006,
                exceptType: 4,
                opeType: opeType,
                names: url,
                urlIds: urlData.id + ''
              })
            }
          }
        })
      }
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate(async(valid, objs) => {
        if (valid) {
          const exceptStrategy = await this.formatExceptStrategy(this.exceptStrategy)
          const tempData = Object.assign({}, this.temp)
          this.shareStgConfig.active = tempData.active
          exceptStrategy.active = tempData.active
          updateStrategy({
            id: '0',
            effectiveContentStg: this.temp,
            shareStgConfig: this.shareStgConfig,
            exceptStrategy: exceptStrategy
          }).then(res => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.strategyUpdate'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.validFailSkip(objs)
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyNameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    strategyFormatter: function(row, data) {
      // 开启、关闭多语言标签化
      const open = `<span style="color: #36ab60;">${this.$t('text.open')}</span>`
      const close = `<span style="color: #f95757;">${this.$t('button.close')}</span>`

      return this.configList.map(({ key, label }) => {
        // 行数据中该属性的值，有值且不为0返回 open，否则返回 close
        const result = row[key] ? open : close
        return `${label}: ${result}; `
      }).join('')
    },
    validFailSkip(validObj) {
      const timeoutRule = validObj['ifTimeoutRuleId'];
      const unrecognizedFileRule = validObj['unrecognizedFileRuleId']
      if ((timeoutRule && timeoutRule[0] instanceof Error) ||
        (unrecognizedFileRule && unrecognizedFileRule[0] instanceof Error)) {
        // this.activeName = 'advanced'
      }
    }
  }
}
</script>
