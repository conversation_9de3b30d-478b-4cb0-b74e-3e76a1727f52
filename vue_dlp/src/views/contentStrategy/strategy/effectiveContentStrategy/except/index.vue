<template>
  <div style="margin: 5px 0 15px;">
    <data-editor
      :formable="formable"
      append-to-body
      :popover-width="680"
      :updateable="strategyEditable"
      :deletable="strategyDeleteable"
      :add-func="createStrategy"
      :update-func="updateStrategy"
      :delete-func="deleteStrategy"
      :cancel-func="cancelStrategy"
      :before-update="beforeUpdateStrategy"
      :before-add="beforeAddStrategy"
    >
      <Form ref="exceptForm" :model="exceptDetail" :rules="exceptRules" label-position="right" label-width="130px" style="width: 600px;">
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('table.sensitiveDetectType')" :tooltip-content="$t('pages.effectiveContent_text1')" prop="bussType" tooltip-placement="bottom-start">
              <el-select v-model="exceptDetail.bussType" :placeholder="$t('text.select')" @change="bussTypeChange">
                <el-option
                  v-for="(item) in bussTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disable"
                />
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="1">
            <el-tooltip v-if="isBrowerSendFile" effect="dark" placement="bottom-start">
              <div slot="content">
                {{ $t('pages.browserSupport') }}
              </div>
              <i class="el-icon-info" style="margin-left: 5px; margin-top: 8px"/>
            </el-tooltip>
          </el-col>
          <el-col :span="11" :offset="isBrowerSendFile ? 0 : 1">
            <FormItem :label="$t('table.excepeObj')">
              <el-select v-model="exceptDetail.exceptType" @change="exceptTypeChange">
                <el-option
                  v-for="(item, key) in exceptTypeOptions"
                  :key="key"
                  :label="item.label"
                  :value="Number(key)"
                  :disabled="item.disable"
                />
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="24">
            <FormItem :label="$t('table.excepeOperation')">
              <el-select v-model="exceptDetail.opeType" style="width: 170px;">
                <el-option
                  v-for="(item, key) in opeTypeOptions"
                  :key="key"
                  :label="item.label"
                  :value="Number(key)"
                  :disabled="item.disable"
                />
              </el-select>
            </FormItem>
          </el-col>
        </el-row>
        <!-- 例外设置的树组件 -->
        <div v-show="exceptDetail.bussType">
          <!-- 网页上传、即时通讯 -->
          <FormItem v-show="isProcessName || isBrowerSendFile" :label="exceptTypeLabel" prop="names">
            <im-list v-if="!isBrowerSendFile" ref="imListRef" :im-tools="exceptDetail.names" @change="imToolChange"/>
            <url-list v-else ref="urlListRef" :visible="isBrowerSendFile" :urls="exceptDetail.names" @change="urlListChange"/>
            <div v-if="isBrowerSendFile" v-html="urlFormatHitMsg()"></div>
          </FormItem>
          <!-- USB、邮箱、打印 -->
          <FormItem v-show="!isProcessName && !isBrowerSendFile" :label="exceptTypeLabel" prop="exceptDataIds">
            <div style="height: 200px;">
              <tree-menu
                ref="exceptDataTree"
                :data="treeData"
                multiple
                node-key="id"
                expand-on-click-node
                :default-checked-keys="checkedKeys"
                :icon-option="iconOption"
                @check-change="handleCheckChange"
              />
            </div>
          </FormItem>
        </div>
      </Form>
      <div slot="btn-group">
        <link-button
          v-show="showUsbBtn"
          btn-type="primary"
          btn-style="float: left; height: 30px; padding: 0 10px;"
          :formable="formable"
          :menu-code="'A5B'"
          :link-url="'/system/baseData/usbDevice'"
          :btn-text="$t('pages.maintainUsb')"
        />
        <link-button
          v-show="showEmailBtn"
          btn-type="primary"
          btn-style="float: left; height: 30px; padding: 0 10px;"
          :formable="formable"
          :menu-code="'A52'"
          :link-url="'/system/baseData/mailLibrary'"
          :btn-text="$t('pages.maintainMail')"
        />
        <link-button
          v-show="showUrlBtn"
          btn-type="primary"
          btn-style="float: left; height: 30px; padding: 0 10px;"
          :formable="formable"
          :menu-code="'A53'"
          :link-url="'/system/baseData/urlLibrary'"
          :btn-text="$t('pages.maintainWeb')"
        />
        <link-button
          v-show="showPrinterBtn"
          btn-type="primary"
          btn-style="float: left; height: 30px; padding: 0 10px;"
          :formable="formable"
          :menu-code="'A5P'"
          :link-url="'/system/baseData/printerLibrary'"
          :btn-text="$t('pages.maintainPrinter')"
        />
      </div>
      <div v-if="showSyncMacBtn" slot="attach-btn" style="display: inline-block;margin-right: 10px; float: right;">
        <el-button size="small" :title="$t('pages.effectiveContent_text19')" @click="noneMacTipVisibleChange"> {{ $t('pages.effectiveContent_text18') }} </el-button>
      </div>
    </data-editor>
    <grid-table
      ref="exceptListTable"
      :height="250"
      :multi-select="true"
      :show-pager="false"
      :col-model="exceptColModel"
      :row-datas="rowDatas"
      style="padding: 5px;"
      @selectionChangeEnd="exceptSelectionChange"
    >
      <template slot="popoverContent" slot-scope="props">
        <div style="max-height: 500px; max-width: 600px; overflow: auto;">
          <span style="padding: 5px 10px; display: inline-block;">{{ props.detail.split(',').join(', ') }}</span>
        </div>
      </template>
    </grid-table>

  </div>
</template>
<script>
import { getExceptStrategy } from '@/api/contentStrategy/strategy/effectiveContentStrategy'
import { findUsbDeviceTree } from '@/api/behaviorManage/hardware/usbConfig'
import { getTreeNode } from '@/api/system/baseData/mailLibrary'
import { printerListTree } from '@/api/system/baseData/printerLibrary'
import { getByUrls } from '@/api/system/baseData/urlLibrary'
import ImList from './imList'
import UrlList from './urlList'

export default {
  name: 'Except',
  components: { ImList, UrlList },
  props: {
    formable: { // 能否提交表单
      type: Boolean,
      default: true
    },
    temp: {
      type: Object,
      default: function() {
        return {}
      }
    },
    rowDatas: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      exceptColModel: [
        { prop: 'bussType', label: 'sensitiveDetectType', width: '120', formatter: this.bussTypeFormatter },
        { prop: 'exceptType', label: 'excepeObj', width: '120', formatter: this.exceptTypeFormatter },
        { prop: 'names', label: 'excepeData', ellipsis: false, type: 'popover', width: '200', formatter: this.namesFormatter },
        { prop: 'opeType', label: 'excepeOperation', width: '100', formatter: this.opeTypeFormatter }
      ],
      // 敏感检测类型选项
      bussTypeOptions: [ // 由于终端未实现，部分枚举暂时先注释
        // { name: 'isCDBurn', value: 1001, label: '光盘刻录内容', disable: false },                              // 光盘刻录内容
        { name: 'isUsbDev', value: 1002, label: this.$t('pages.stgMessage1'), disable: false },                  // USB外发文件
        { name: 'isChatText', value: 1003, label: this.$t('pages.stgLabelImSendContent'), disable: false },                // 即时通讯发送文本内容
        { name: 'isChatFile', value: 1004, label: this.$t('pages.stgLabelImSendFile'), disable: false },                // 即时通讯发送文件
        // { name: 'isBrowseWebUrl', value: 1005, label: '访问网页内容', disable: false },                        // 访问网页内容
        { name: 'isBrowerSendFileOption', value: 1006, label: this.$t('pages.stgLabelWebUploadFile'), disable: false },    // 网页上传文件
        // { name: 'isBrowerPasterOption', value: 1007, label: '网页粘贴文本内容', disable: false },              // 网页粘贴文本内容
        { name: 'isEmailContent', value: 1008, label: this.$t('pages.stgLabelEmailTransferContent'), disable: false },            // 邮件传输文本内容
        // { name: 'isForumData', value: 1009, label: '论坛发帖内容', disable: false },                           // 论坛发帖内容
        // { name: 'isLocalDir', value: 1010, label: '本地共享内容', disable: false },                            // 本地共享内容
        // { name: 'isRemoteDir', value: 1011, label: '远程共享内容', disable: false },                           // 远程共享内容
        { name: 'isEmailAttach', value: 1012, label: this.$t('pages.stgLabelMail'), disable: false },             // 邮件附件内容
        { name: 'isPrint', value: 1013, label: this.$t('pages.stgLabelFilePrintContent'), disable: false }                   // 文件打印内容
        // { name: 'isBlueFile', value: 1014, label: '蓝牙传输文件', disable: false }                             // 蓝牙传输文件
      ],
      // 例外设置对象选项
      exceptTypeOptions: {
        1: { label: this.$t('pages.processName1'), disable: false },                  // 进程名
        2: { label: this.$t('pages.exceptTypeOptions1'), disable: false },            // 收件邮箱地址
        3: { label: this.$t('pages.deviceCode'), disable: false },                    // 设备编码
        4: { label: this.$t('pages.effectiveContent_url'), disable: true },           // 网址
        5: { label: this.$t('pages.printerInformation'), disable: false }             // 打印机信息
      },
      // 例外操作选项
      opeTypeOptions: {
        1: { label: this.$t('pages.opeTypeOptions1'), disable: false },               // 不检测
        4: { label: this.$t('pages.opeTypeOptions2'), disable: false }                // 不阻断
        // 2: '不告警',  //暂时屏蔽
      },
      iconOption: { 'G': 'group', 'usb': 'usb' },
      // 检测规则
      detectRuleOptions: {
        1: '检测指定的URL',
        2: '过滤掉指定的URL'
      },
      // 触发规定的文件操作才进行检测
      fileActionOptions: {
        1: '上传文件敏感不检测',
        2: '上传文件敏感检测但不阻断',
        4: '下载文件敏感不检测',
        8: '表示下载文件敏感检测但不阻断'
      },
      strategyDeleteable: false,          // 例外设置删除按钮是否可用
      strategyEditable: false,            // 例外设置修改按钮是否可用
      treeData: [],                       // 例外设置树数据
      usbTreeData: [],                    // USB树 源数据
      mailTreeData: [],                   // 邮件树 源数据
      printerTreeData: [],                // 打印机树 源数据
      checkedKeys: [],                    // 勾选的树节点 key
      tempCheckedIds: '',                 // 新增、修改过程中，临时保存的已勾选的key
      deleteRowDate: [],                  // 用来存放被删除的表格数据
      exceptDetail: {},
      defaultExceptDetail: {
        id: undefined,
        exceptType: 1,
        exceptDataIds: '',
        opeType: 1,
        bussType: undefined,
        names: '',
        urlIds: ''  //  敏感内容检测类型为网页上传文件时，保存选中的urlIds
      },
      exceptRules: {
        bussType: [
          { required: true, message: this.$t('pages.effectiveContent_text3'), trigger: 'blur' }
        ],
        names: [
          { validator: this.namesValidator, trigger: 'blur' },
          { validator: this.namesValidator1, trigger: 'blur' }
        ],
        exceptDataIds: [
          { validator: this.exceptDataIdsValidator, trigger: 'blur' },
          { validator: this.exceptDataIdsValidator1, trigger: 'blur' }
        ]
      },
      operatorStatus: '',
      updateTempId: null,
      webUploadUrlMaxLimit: 100  //  网页文件上传-url最多支持选中100条，若要修改，后端需同步更改
    }
  },
  computed: {
    // 敏感检测类型是否 网页上传文件
    isBrowerSendFile() {
      return this.exceptDetail.bussType == 1006
    },
    // 例外设置对象是否 进程名
    isProcessName() {
      return this.exceptDetail.exceptType == 1
    },
    // 例外设置对象的 label
    exceptTypeLabel() {
      const exceptType = this.exceptDetail.exceptType
      return exceptType ? this.exceptTypeOptions[exceptType].label : ''
    },
    showUsbBtn() {
      return this.exceptDetail.bussType == 1002
    },
    showEmailBtn() {
      const bussType = this.exceptDetail.bussType
      return bussType == 1008 || bussType == 1012
    },
    showUrlBtn() {
      return this.exceptDetail.bussType == 1006
    },
    showPrinterBtn() {
      return this.exceptDetail.bussType == 1013
    },
    showSyncMacBtn() {
      let noneMacTipVisible = false
      this.rowDatas.forEach(row => {
        if (row.exceptType == 1 && row.names && row.names.length > 0) {
          const names = row.names.split(',')
          if (names.includes('qq.exe') && !names.includes('QQ')) {
            noneMacTipVisible = true
          }
          if (names.includes('WeiXin.exe') && !names.includes('WeChat')) {
            noneMacTipVisible = true
          }
          if (names.includes('DingTalk.exe') && !names.includes('DingTalk')) {
            noneMacTipVisible = true
          }
          if (names.includes('FeiShu.exe') && !names.includes('Lark')) {
            noneMacTipVisible = true
          }
        }
      })
      return noneMacTipVisible
    }
  },
  watch: {
    temp: {  // 深度监听，可监听到对象、数组的变化
      deep: true,
      handler(newVal, oldVal) {
        this.bussTypeOptions.forEach(option => {
          option.disable = false
          const isBussActive = this.temp[option.name] == option.value
          if (!isBussActive) {
            // 如果开关没选中，例外配置中表格对应数据删掉，并存放在deleteRowDate中
            option.disable = true
            for (let i = this.rowDatas.length - 1; i >= 0; i--) {
              if (this.rowDatas[i].bussType === option.value) {
                this.deleteRowDate.push(this.rowDatas[i])
                this.rowDatas.splice(i, 1)
              }
            }
          } else { // 如果开关选中，例外配置中的表格将刚刚删除保存在并存放在deleteRowDate中的数据在push进表格数据中去
            if (this.deleteRowDate.length > 0) {
              for (let i = this.deleteRowDate.length - 1; i >= 0; i--) {
                if (this.deleteRowDate[i].bussType === option.value) {
                  this.rowDatas.push(this.deleteRowDate[i])
                  this.deleteRowDate.splice(i, 1)
                }
              }
            }
          }
        })
      }
    }
  },
  created() {
    this.resetDetail()
    this.loadTreeData()
  },
  activated() {
    this.loadTreeData()
    if (this.isBrowerSendFile && this.temp && this.temp.id) {
      this.loadUrlExceptStrategy(this.temp.id)
    }
  },
  methods: {
    exceptTable() {
      return this.$refs['exceptListTable']
    },
    loadUrlExceptStrategy(superiorId) {
      getExceptStrategy(superiorId).then(async res => {
        const exceptDetail = await this.initFormatExceptStrategy(res.data)
        this.rowDatas.forEach(data => {
          const t = exceptDetail.find(detail => { return data.addId && detail.addId === data.addId }) || null
          if (t) {
            data.names = t.names || ''
          }
        })
        const selectedTemp = this.exceptTable().getSelectedDatas()[0] || {}
        if (selectedTemp.names) {
          this.$nextTick(() => {
            this.$refs['urlListRef'].checkedKey(selectedTemp.names || '')
          })
        }
      })
    },
    async initFormatExceptStrategy(exceptStrategy) {
      if (exceptStrategy && exceptStrategy.exceptSensiDetectByURL) {
        //  获取所有的url
        const urls = [];
        exceptStrategy.exceptSensiDetectByURL.forEach(item => {
          urls.push(item.url)
        });

        const urlMap = {};
        //  根据url查询网址信息
        if (urls.length > 0) {
          const res = await getByUrls(urls);
          (res.data || []).forEach(url => {
            urlMap[url.address] = url
          })
        }

        exceptStrategy.exceptSensiDetectByURL.forEach(item => {
          const urlData = urlMap[item.url] || null
          const url = urlData !== null ? (urlData.name + '(' + urlData.address + ')') : null
          if (url != null) {
            const exceptUrl = exceptStrategy.exceptDetail.find(t => {
              return t.id === item.addId
            }) || null
            if (exceptUrl !== null) {
              exceptUrl.names += ',' + url
              exceptUrl.urlIds += ',' + urlData.id
            } else {
              //  由于原有例外设置中例外操作类型操作类型的值为 1：不检测，3：不告警，4：不阻断，浏览器url过滤的例外设置中1：表示上传文件敏感不检测，2：上传文件敏感检测但不阻断，4：下载文件敏感不检测，8:表示下载文件敏感检测但不阻断
              const opeType = item.opeType == 1 ? 1 : item.opeType == 2 ? 4 : null;
              //  exceptType = 4: 代表网址
              exceptStrategy.exceptDetail.push({
                id: item.addId,
                bussType: 1006,
                exceptType: 4,
                opeType: opeType,
                names: url,
                urlIds: urlData.id + ''
              })
            }
          }
        });
        return exceptStrategy.exceptDetail || []
      }
    },
    // 敏感检测类型变化
    bussTypeChange(val) {
      this.clearChecked()
      this.$refs.exceptDataTree.clearSelectedNodes()
      this.$refs.exceptForm.clearValidate()
      const exceptType = this.getExceptType(val)
      this.setExceptType(exceptType)

      if (this.isBrowerSendFile) {
        this.exceptDetail.names = ''
        this.$nextTick(() => {
          this.$refs['urlListRef'] && this.$refs['urlListRef'].show()
        })
      } else if (this.isProcessName) {
        this.exceptDetail.names = ''
        this.$nextTick(() => {
          this.$refs['imListRef'] && this.$refs['imListRef'].show()
        })
      } else {
        this.exceptTypeChange(exceptType)
      }
    },
    clearChecked() {
      this.checkedKeys.splice(0)
      this.tempCheckedIds = ''
    },
    // 获取例外设置对象的 key
    getExceptType(val) {
      const map = { 1003: 1, 1004: 1, 1008: 2, 1012: 2, 1002: 3, 1006: 4, 1013: 5 }
      return map[val] || null
    },
    // 设置例外设置对象并禁用其他选项
    setExceptType(val) {
      this.exceptDetail.exceptType = val
      for (const key in this.exceptTypeOptions) {
        this.exceptTypeOptions[key].disable = val ? val != key : false
      }
    },
    // 点击新增按钮
    beforeAddStrategy() {
      this.operatorStatus = 'create'
      this.updateTempId = null
    },
    // 点击修改按钮
    beforeUpdateStrategy() {
      this.operatorStatus = 'update'
      const rowData = this.exceptTable().getSelectedDatas()[0]
      // 保存更新的例外设置配置Id
      this.updateTempId = rowData.id || null
      if (rowData) {
        this.exceptDetail = Object.assign(this.exceptDetail, rowData)
        this.setExceptType(this.exceptDetail.exceptType)
        this.tempCheckedIds = this.exceptDetail.exceptDataIds

        if (this.isBrowerSendFile) {
          // 网页上传文件
          this.$nextTick(() => {
            this.$refs['urlListRef'] && this.$refs['urlListRef'].show()
          })
        } else if (this.isProcessName && !this.isBrowerSendFile) {
          // 即时通讯
          this.$nextTick(() => {
            this.$refs['imListRef'] && this.$refs['imListRef'].show()
          })
        } else {
          this.changeTreeData(this.exceptDetail.exceptType)
        }
      }
    },
    createStrategy() {
      let validate
      this.$refs['exceptForm'].validate((valid) => {
        if (valid) {
          this.$refs.exceptDataTree.clearSelectedNodes()
          const rowData = Object.assign({}, this.exceptDetail)
          rowData.id = new Date().getTime()
          this.rowDatas.unshift(rowData)
          this.cancelStrategy()
          validate = valid
        }
      })
      return validate
    },
    updateStrategy() {
      let validate
      this.$refs['exceptForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.exceptDetail)
          for (let i = 0, size = this.rowDatas.length; i < size; i++) {
            const data = this.rowDatas[i]
            if (rowData.id === data.id) {
              this.rowDatas.splice(i, 1, rowData)
              break
            }
          }
          // this.resetDetail()
          this.cancelStrategy()
          validate = valid
        }
      })
      return validate
    },
    deleteStrategy() {
      const toDeleteIds = this.exceptTable().getSelectedIds()
      this.exceptTable().deleteRowData(toDeleteIds, this.rowDatas)
      this.cancelStrategy()
    },
    cancelStrategy() {
      this.resetDetail()
      if (this.$refs['exceptDataTree']) {
        this.$refs['exceptDataTree'].clearFilter()
        this.$refs['exceptDataTree'].clearSelectedNodes()
      }
      this.exceptTable() && this.exceptTable().setCurrentRow()
      this.$refs['exceptForm'] && this.$refs['exceptForm'].clearValidate()
    },
    noneMacTipVisibleChange() {
      this.rowDatas.forEach(row => {
        const names = row.names.split(',')
        if (row.exceptType == 1 && names && names.length > 0) {
          const newNames = []
          names.forEach(name => {
            newNames.push(name)
            if (name === 'qq.exe') {
              newNames.push('QQ')
            } else if (name === 'WeiXin.exe') {
              newNames.push('WeChat')
            } else if (name === 'DingTalk.exe') {
              newNames.push('DingTalk')
            } else if (name === 'FeiShu.exe') {
              newNames.push('Lark')
            }
          })
          row.names = newNames.join(',')
        }
      })
    },
    exceptTypeChange(val) {
      this.changeTreeData(val)
    },
    loadTreeData() {
      this.loadMailTree()
      this.loadUsbDeviceTree()
      this.loadPrinterTree()
    },
    // 变更树数据源
    changeTreeData(val) {
      this.checkedKeys.splice(0)
      // 根据 val 的值变更数据源，若数据源没变，则不修改
      if (val == 2) {
        if (JSON.stringify(this.mailTreeData) != JSON.stringify(this.treeData)) {
          this.treeData = this.mailTreeData
        }
      } else if (val == 3) {
        if (JSON.stringify(this.usbTreeData) != JSON.stringify(this.treeData)) {
          this.treeData = this.usbTreeData
        }
      } else if (val == 5) {
        if (JSON.stringify(this.printerTreeData) != JSON.stringify(this.treeData)) {
          this.treeData = this.printerTreeData
        }
      }
      this.$nextTick(() => {
        this.checkedKeys = this.tempCheckedIds.split(',')
      })
    },
    //  标签中添加设备编码
    labelAddUseCode(data = []) {
      if (data.length === 0) {
        return
      }
      data.forEach(item => {
        const { oriData, label } = item
        if (oriData && oriData.usbCode) {
          item.label = `${label}(${oriData.usbCode})`
        }
        this.labelAddUseCode(item.children);
      })
    },
    loadUsbDeviceTree() {
      findUsbDeviceTree().then(res => {
        this.labelAddUseCode(res.data)
        if (JSON.stringify(this.usbTreeData) != JSON.stringify(res.data)) {
          this.usbTreeData = res.data
          if (this.exceptDetail.exceptType == 3) {
            this.changeTreeData(this.exceptDetail.exceptType)
          }
        }
      }).catch(error => {
        console.log(error)
      })
    },
    loadMailTree() {
      getTreeNode().then(res => {
        if (JSON.stringify(this.mailTreeData) != JSON.stringify(res.data)) {
          this.mailTreeData = res.data
          if (this.exceptDetail.exceptType == 2) {
            this.changeTreeData(this.exceptDetail.exceptType)
          }
        }
      }).catch(error => {
        console.log(error)
      })
    },
    loadPrinterTree() {
      printerListTree().then(res => {
        if (JSON.stringify(this.printerTreeData) != JSON.stringify(res.data)) {
          this.printerTreeData = res.data
          if (this.exceptDetail.exceptType == 5) {
            this.changeTreeData(this.exceptDetail.exceptType)
          }
        }
      }).catch(error => {
        console.log(error)
      })
    },
    // 重置例外设置数据
    resetDetail() {
      this.exceptDetail = Object.assign({}, this.defaultExceptDetail)
    },
    // 树节点勾选变化的回调
    handleCheckChange(keys, datas) {
      const ids = []
      keys.forEach(key => {
        if (key.indexOf('G') < 0) {
          ids.push(key)
        }
      })
      this.checkedKeys = ids
      this.exceptDetail.exceptDataIds = ids.join(',')
      this.tempCheckedIds = ids.join(',')
      this.$nextTick(() => {
        this.$refs['exceptForm'].validateField('exceptDataIds')
      })
    },
    // 例外设置列表勾选状态变化的回调
    exceptSelectionChange(rowDatas) {
      this.strategyDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.strategyEditable = true
      } else {
        this.cancelStrategy()
        this.strategyEditable = false
      }
    },
    bussTypeFormatter: function(row, data) {
      let label = ''
      this.bussTypeOptions.forEach(option => {
        if (option.value == data) {
          label = option.label
        }
      })
      return label
    },
    exceptTypeFormatter: function(row, data) {
      const type = this.exceptTypeOptions[data]
      return !type ? '' : type.label
    },
    opeTypeFormatter: function(row, data) {
      return this.opeTypeOptions[data].label
    },
    namesFormatter: function(row, data) {
      return this.html2Escape(data)
    },
    namesValidator1(rule, value, callback) {
      if (this.exceptDetail && (this.isProcessName || this.isBrowerSendFile)) {
        let flag = false
        const { id, exceptType, bussType, names } = this.exceptDetail
        this.rowDatas.forEach(except => {
          if (except.id == id) return
          if (except.exceptType == exceptType && except.bussType == bussType) {
            const rowNames = except.names
            const rowNamesArr = rowNames && rowNames.length > 0 ? rowNames.split(',') : []
            const namesArr = names.length > 0 ? names.split(',') : []
            for (const name of namesArr) {
              const exist = rowNamesArr.indexOf(name)
              if (exist >= 0) {
                flag = true
                break
              }
            }
          }
        })
        if (flag) {
          callback(new Error(this.$t('pages.effectiveContent_text31')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    exceptDataIdsValidator1(rule, value, callback) {
      if (this.exceptDetail && !this.isProcessName && !this.isBrowerSendFile) {
        let flag = false
        const { id, exceptType, bussType, names } = this.exceptDetail
        this.rowDatas.forEach(except => {
          if (except.id == id) return
          if (except.exceptType == exceptType && except.bussType == bussType) {
            const rowNames = except.names
            const rowNamesArr = rowNames && rowNames.length > 0 ? rowNames.split(',') : []
            const namesArr = names.length > 0 ? names.split(',') : []
            for (const name of namesArr) {
              const exist = rowNamesArr.indexOf(name)
              if (exist >= 0) {
                flag = true
                break
              }
            }
          }
        })
        if (flag) {
          callback(new Error(this.$t('pages.effectiveContent_text31')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    namesValidator(rule, value, callback) {
      if (this.isProcessName) {
        if (!this.exceptDetail.names) {
          callback(this.$t('pages.effectiveContent_text4'))
        } else if (this.exceptDetail.names.indexOf('，') > -1) {
          // 多个进程以英文逗号相隔
          callback(new Error(this.$t('pages.effectiveContent_text5')))
        } else {
          callback()
        }
      } else if (this.isBrowerSendFile && !this.exceptDetail.names) {
        callback(this.$t('pages.effectiveContent_text6'))
      } else if (this.isBrowerSendFile && this.exceptDetail.names.length) {
        //  已添加的url
        const addedNum = this.getAddedNum()
        //  待添加的url
        if (addedNum > this.webUploadUrlMaxLimit) {
          callback(new Error(this.$t('pages.exceptionSettingWebUploadErrorMsg1', { limit: this.webUploadUrlMaxLimit })))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    urlFormatHitMsg() {
      const num = this.getAddedNum()
      const addedNum = num > 100 ? `<span style="color: red;">${num}</span>` : num
      return this.$t('pages.exceptionSettingWebUploadErrorMsg2', { total: this.webUploadUrlMaxLimit, addedNum })
    },
    // 获取已添加的url数量
    getAddedNum() {
      const list = this.rowDatas.filter(data => data.bussType === 1006 && (this.operatorStatus === 'create' || this.updateTempId !== data.id)) || []
      let num = 0
      list.forEach(item => {
        num += item.names.split(',').length
      })
      num += (this.exceptDetail.names ? this.exceptDetail.names.split(',').length : 0)
      return num
    },
    exceptDataIdsValidator(rule, value, callback) {
      // 非进程名 且 非网页上传文件
      if (!this.isProcessName && !this.isBrowerSendFile) {
        const nodes = this.$refs['exceptDataTree'].getCheckedNodes()
        const dataIds = []
        const names = []
        nodes.forEach((node) => {
          if (node.id.indexOf('G' + node.dataId) < 0) {
            dataIds.push(node.dataId)
            names.push(node.label)
          }
        })
        this.exceptDetail.exceptDataIds = dataIds.length > 0 ? dataIds.join(',') : ''
        this.exceptDetail.names = names.length > 0 ? names.join(',') : ''
        if (!this.isProcessName && !this.exceptDetail.exceptDataIds) {
          callback(new Error(this.$t('pages.effectiveContent_text6')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    urlListChange(list) {
      const labels = []
      const ids = []
      list.forEach(url => {
        ids.push(url.id)
        labels.push(url.label)
      })
      this.exceptDetail.urlIds = ids.join(',')
      this.exceptDetail.names = labels.join(',');
      this.$refs['exceptForm'].validateField('names')
    },
    imToolChange(list) {
      this.exceptDetail.names = (list || []).join(',')
      this.$refs['exceptForm'].validateField('names')
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-tree>.el-tree-node{
    display: block;
  }
</style>
