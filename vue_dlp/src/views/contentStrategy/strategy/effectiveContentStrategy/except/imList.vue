<template>
  <div style="height: 200px;">
    <tree-menu
      ref="urlDataTree"
      :data="treeData"
      multiple
      node-key="id"
      :checked-keys="checkedKeys"
      @check-change="handleCheckChange"
    />
  </div>
</template>

<script>
export default {
  name: 'ImList',
  props: {
    imTools: { type: String, default: '' }
  },
  data() {
    return {
      treeData: [
        { id: 'qq.exe', label: 'QQ' },
        { id: 'QIDIAN.EXE', label: this.$t('pages.enterpriseQQ') },  // 新版企业QQ的进程名； 旧版进程名： QQEIM.EXE 上传到后端时，如果存在，添加一下
        { id: 'WeiXin.exe', label: this.$t('pages.wechat') },  // 新版微信的进程名； 旧版进程名：WeChat.exe 上传到后端时，如果存在，添加一下
        { id: 'WXWork.exe', label: this.$t('pages.enterpriseWeChat') },
        { id: 'DingTalk.exe', label: this.$t('pages.dingTalk') },
        { id: 'FeiQ.exe', label: this.$t('pages.feiQ') },
        { id: 'AliIM.exe', label: this.$t('pages.aliTalk') },
        { id: 'FeiShu.exe', label: this.$t('pages.feiShu') },
        { id: 'skype.exe', label: 'Skype' },
        { id: 'tim.exe', label: this.$t('pages.tim') },
        { id: 'SHIYELINE.EXE', label: this.$t('pages.shiyeLine') }
      ],
      checkedKeys: []
    }
  },
  methods: {
    show() {
      // 加个map是因为考虑兼容旧版本的即时通讯的进程；原环境出现即时通讯配置dingtalk.exe的情况，(输入框的形式)
      // 但是现阶段只有进程名为DingTalk.exe才能选择钉钉，所以做下兼容
      const idList = this.imTools.split(',').map(im => {
        if (im) {
          const option = this.treeData.find(opt => opt.id.localeCompare(im, undefined, { sensitivity: 'base' }) === 0)
          return (option || {}).id
        }
        return null
      }).filter(im => !!im)
      this.checkedKeys.splice(0, this.checkedKeys.length, ...idList)
    },
    handleCheckChange(data) {
      let tempData = data
      const index = (data || []).indexOf('QIDIAN.EXE')
      if (index >= 0) {
        tempData = [...data]
        // 腾讯企点关联两个进程名
        tempData.splice(index, 0, 'QQEIM.EXE')
      }
      const index1 = (data || []).indexOf('WeiXin.exe')
      if (index1 >= 0) {
        tempData = [...data]
        // 微信关联两个进程名
        tempData.splice(index1, 0, 'WeChat.exe')
      }
      // 格式化进程名，兼容MAC终端的进程名格式
      const formatData = []
      tempData.forEach(item => {
        formatData.push(item)
        if (item === 'qq.exe') {
          formatData.push('QQ')
        } else if (item === 'WeiXin.exe') {
          formatData.push('WeChat')
        } else if (item === 'DingTalk.exe') {
          formatData.push('DingTalk')
        } else if (item === 'FeiShu.exe') {
          formatData.push('Lark')
        }
      })
      this.$emit('change', formatData)
    }
  }
}
</script>

<style scoped>

</style>
