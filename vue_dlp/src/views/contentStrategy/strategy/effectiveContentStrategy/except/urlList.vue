<template>
  <div style="height: 200px;">
    <tree-menu
      ref="urlDataTree"
      :data="treeData"
      multiple
      node-key="id"
      :icon-option="iconOption"
      :checked-keys="checkedKeys"
      @check-change="handleCheckChange"
    />
  </div>
</template>

<script>
import { urlListTreeNode } from '@/api/system/baseData/urlLibrary'
export default {
  name: 'UrlList',
  components: {},
  props: {
    visible: { type: Boolean, default: false },
    urls: { type: String, default: '' }
  },
  data() {
    return {
      treeData: [],
      checkedKeys: [],
      iconOption: { 'G': 'group' }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.show()
      }
    }
  },
  activated() {
    this.show()
  },
  created() {
  },
  methods: {
    checkedKey(urls) {
      const urlList = urls.split(',')
      if (urlList && urlList.length > 0) {
        this.setUrlData(urlList, this.treeData)
      }
    },
    show() {
      urlListTreeNode().then(res => {
        this.treeData = res.data
        this.checkedKeys.splice(0)
        const urlList = this.urls.split(',')
        if (urlList && urlList.length > 0) {
          this.setUrlData(urlList, this.treeData)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    setUrlData(urlList, datas) {
      datas.forEach(item => {
        if (!item.type && urlList.includes(item.label)) {
          this.checkedKeys.push(item.id)
        }
        item.children && this.setUrlData(urlList, item.children || [])
      })
    },
    handleCheckChange(keys, datas) {
      const urlList = []
      //  去除分组和重复的url
      datas.forEach(item => {
        if (item.id.indexOf('G') < 0 && !urlList.includes(item.label)) {
          urlList.push(item)
        }
      })
      this.$emit('change', urlList)
    }
  }
}
</script>
