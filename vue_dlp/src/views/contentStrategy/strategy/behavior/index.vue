<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.stgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="width: 720px; margin-left:30px;">
        <stg-target-form-item
          :target-tree="strategyTree()"
          :is-disabled="!formable"
          :strategy-def-type="query.strategyDefType"
        />
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('text.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-radio-group v-model="postTypePanel">
          <el-radio :label="1">端点通信</el-radio>
          <el-radio :label="2">网络通信</el-radio>
        </el-radio-group>
        <el-card v-show="postTypePanel === 1" class="box-card">
          <label>存储介质方式泄露防护</label>
          <el-row :gutter="24">
            <el-col :span="8">手机设备</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.phone" :disabled="!formable">
                <el-radio v-for="item in enableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">其他移动存储介质</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.usbDisk" :disabled="!formable">
                <el-radio v-for="item in usbDiskOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <label>打印输出方式泄露防护</label>
          <el-row :gutter="24">
            <el-col :span="8">打印</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.printer" :disabled="!formable">
                <el-radio v-for="item in printerOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <label>接口方式泄露防护</label>
          <el-row :gutter="24">
            <el-col :span="8">串口</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.serialPort" :disabled="!formable">
                <el-radio v-for="item in enableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">并口</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.parallelPort" :disabled="!formable">
                <el-radio v-for="item in enableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">IEEE 1394接口</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.ieee1394Port" :disabled="!formable">
                <el-radio v-for="item in enableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">USB接口</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.usbPort" :disabled="!formable">
                <el-radio v-for="item in enableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">红外/蓝牙等无线接口</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.wirelessPort" :disabled="!formable">
                <el-radio v-for="item in enableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">PCI接口</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.pciPort" :disabled="!formable">
                <el-radio v-for="item in enableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">SCSI、IDE、SATA等接口</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.hardDiskPort" :disabled="!formable">
                <el-radio v-for="item in enableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
        </el-card>
        <el-card v-show="postTypePanel === 2" class="box-card">
          <label>附件泄露防护</label>
          <el-row :gutter="24">
            <el-col :span="6">传输的文件超过</el-col>
            <el-col :span="8">
              <el-input-number v-model="temp.fileSize" :disabled="!formable" :min="1" :max="32"></el-input-number>
            </el-col>
            <el-col :span="8">
              M，进行如下设置
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">{{ $t('table.limitType') }}</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.fileLimitType" :disabled="!formable">
                <el-radio v-for="item in fileLimitTypeOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <label>网络通信方式泄露防护</label>
          <el-row :gutter="24">
            <el-col :span="8">FTP</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.ftp" :disabled="!formable">
                <el-radio v-for="item in netEnableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">HTTP</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.http" :disabled="!formable">
                <el-radio v-for="item in netEnableOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">SMTP</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.smtp" :disabled="!formable">
                <el-radio v-for="item in smtpOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">TELNET</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.telnet" :disabled="!formable">
                <el-radio v-for="item in telnetOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">即时聊天</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.im" :disabled="!formable">
                <el-radio v-for="item in imOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">文件共享</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.fileSharing" :disabled="!formable">
                <el-radio v-for="item in fileSharingOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <!--<el-row :gutter="24">
            <el-col :span="8">IP地址、TCP端口和UDP端口</el-col>
            <el-col :span="16">
              <el-radio-group v-model="temp.ipTUPort">
                <el-radio v-for="item in IPTUPortOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <div style="float:right;margin: 0 0 5px;">
            <el-button style="padding: 5px;" @click="function() { }">添加</el-button>
            <el-button style="padding: 5px;" @click="function() { }">修改</el-button>
            <el-button style="padding: 5px;" @click="function() { }">删除</el-button>
            <el-button style="padding: 5px;" @click="function() { }">{{ $t('button.cancel') }}</el-button>
          </div>
          <grid-table
            ref="IPTUPortList"
            :height="250"
            :multi-select="true"
            :show-pager="false"
            :col-model="IPTUPortColModel"
            :row-datas="IPTUPortRowData"
          />-->
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getStrategyPage, getStrategyByName, createStrategy, updateStrategy, deleteStrategy
} from '@/api/contentStrategy/strategy/behavior'
import { objectFormatter, entityLink, refreshPage, buttonFormatter, hiddenActiveAndEntity } from '@/utils'

export default {
  name: 'Behavior',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', fixed: true, fixedWidth: '150' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: '存储介质泄露防护', width: '200', formatter: this.storageMediumFormatter },
        { label: '打印输出泄露防护', width: '200', formatter: this.printoutFormatter },
        { label: '接口泄露防护', width: '200', formatter: this.interfaceFormatter },
        { label: '网络通信泄露防护', width: '200', formatter: this.networkFormatter },
        { prop: 'remark', label: 'remark', width: '150' },
        { prop: 'active', label: 'enable', fixedWidth: '80', type: 'icon', iconClass: 'active' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      IPTUPortColModel: [
        { prop: 'type', label: 'type', fixedWidth: '150' },
        { prop: 'ip', label: 'IP', width: '100' },
        { prop: 'port', label: 'port', width: '100' }
      ],
      IPTUPortRowData: [],
      enableOptions: [{ value: 1, label: '正常使用' }, { value: 2, label: '禁用' }],
      usbDiskOptions: [{ value: 1, label: '正常使用' }, { value: 2, label: '禁用' }, { value: 3, label: '只读' }, { value: 4, label: '输出保护' }],
      printerOptions: [{ value: 1, label: '正常使用' }, { value: 2, label: '禁用' }, { value: 3, label: '输出保护' }],
      fileLimitTypeOptions: [{ value: 1, label: '允许传输' }, { value: 2, label: '禁止传输' }],
      netEnableOptions: [{ value: 1, label: '正常访问' }, { value: 2, label: '禁用上传' }, { value: 3, label: '输出保护' }],
      smtpOptions: [{ value: 1, label: '正常访问' }, { value: 2, label: '禁用访问' }, { value: 3, label: '禁止附件' }, { value: 4, label: '输出保护' }],
      telnetOptions: [{ value: 1, label: '正常访问' }, { value: 2, label: '禁用访问' }],
      imOptions: [{ value: 1, label: '正常访问' }, { value: 2, label: '禁用' }, { value: 3, label: '禁止发送文件' }, { value: 4, label: '输出保护' }],
      fileSharingOptions: [{ value: 1, label: '正常访问' }, { value: 2, label: '禁用' }, { value: 3, label: '输出保护' }],
      IPTUPortOptions: [{ value: 1, label: '默认允许使用' }, { value: 2, label: '默认禁止使用' }],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      deleteable: false,
      addBtnAble: false,
      treeable: true,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        remark: '',
        active: false,
        phone: 1,
        usbDisk: 1,
        printer: 1,
        serialPort: 1,
        parallelPort: 1,
        ieee1394Port: 1,
        usbPort: 1,
        wirelessPort: 1,
        pciPort: 1,
        hardDiskPort: 1,
        fileSize: 32,
        fileLimitType: 1,
        ftp: 1,
        http: 1,
        smtp: 1,
        telnet: 1,
        im: 1,
        fileSharing: 1,
        ipTUPort: 1,
        ipPorts: [],
        objectType: undefined,
        objectId: undefined
      },
      postTypePanel: 1,
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.formable ? this.i18nConcatText(this.$t('pages.behavioralStrategy'), 'update') : this.i18nConcatText(this.$t('pages.behavioralStrategy'), 'details'),
        create: this.i18nConcatText(this.$t('pages.behavioralStrategy'), 'create')
      },
      rules: {
        name: [
          { required: true, message: 'name is required', trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.resetTemp()
  },
  methods: {
    strategyTree: function() {
      return this.$refs['strategyTargetTree']
    },
    rowDataApi: function(option) {
      return getStrategyPage(option)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      this.addBtnAble = !!checkedNode
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.postTypePanel = 1
    },
    // v-el-drag-dialog onDrag callback function
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {},
    handleExport() {},
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createStrategy(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateStrategy(tempData).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    storageMediumFormatter: function(row, data) {
      return [
        this.optionFormatter(this.enableOptions, row.phone),
        this.optionFormatter(this.usbDiskOptions, row.usbDisk)
      ].join(',')
    },
    printoutFormatter: function(row, data) {
      return [
        this.optionFormatter(this.printerOptions, row.printer)
      ].join(',')
    },
    interfaceFormatter: function(row, data) {
      return [
        this.optionFormatter(this.enableOptions, row.serialPort),
        this.optionFormatter(this.enableOptions, row.parallelPort),
        this.optionFormatter(this.enableOptions, row.ieee1394Port),
        this.optionFormatter(this.enableOptions, row.usbPort),
        this.optionFormatter(this.enableOptions, row.wirelessPort),
        this.optionFormatter(this.enableOptions, row.pciPort),
        this.optionFormatter(this.enableOptions, row.hardDiskPort)
      ].join(',')
    },
    networkFormatter: function(row, data) {
      return [
        this.optionFormatter(this.fileLimitTypeOptions, row.fileLimitType) + ' ' + row.fileSize + ' M附件',
        this.optionFormatter(this.netEnableOptions, row.ftp),
        this.optionFormatter(this.netEnableOptions, row.http),
        this.optionFormatter(this.smtpOptions, row.smtp),
        this.optionFormatter(this.telnetOptions, row.telnet),
        this.optionFormatter(this.imOptions, row.im),
        this.optionFormatter(this.fileSharingOptions, row.fileSharing),
        this.optionFormatter(this.IPTUPortOptions, row.ipTUPort)
      ].join(',')
    },
    optionFormatter: function(options, dataValue) {
      for (let i = 0, size = options.length; i < size; i++) {
        if (dataValue === options[i].value) {
          return options[i].label
        }
      }
      return ''
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    }
  }
}
</script>
