<template>
  <div class="app-container">
    <div :class="{ 'content-mask': contentMask }"></div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <el-button v-permission="'723'" icon="el-icon-setting" size="mini" @click="handleConfig">
          {{ $t('button.highConfig') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="stgDialogVisible"
      :title="textMap[dialogStatus]"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 700px;">
        <FormItem :label="$t('table.ruleName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"></el-input>
        </FormItem>
        <FormItem :label="$t('table.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <!-- <el-divider content-position="left">{{ $t('pages.detectionRules') }}</el-divider> -->
        <el-row>
          <el-col :span="24" style="margin-left: -39px">
            <FormItem :label="$t('pages.sensitiveContentRuleSet')" label-width="130px" :tooltip-content="$t('pages.content_text11', {menuTitle: menuTitle})" prop="ruleGroupList" tooltip-placement="bottom-start">
              <el-popover
                v-model="gShow"
                :disabled="!formable"
                :popper-class="popperClass"
                placement="right"
                width="400"
                trigger="manual"
                @show="groupTreeShow"
                @after-leave="groupTreeHide"
              >
                <tree-menu
                  ref="ruleGroup"
                  style="height: 370px"
                  multiple
                  check-on-click-node
                  :data="ruleGroupTreeDatas"
                  :checked-keys="ruleGroupList"
                  :default-expand-all="true"
                />
                <el-button type="primary" size="mini" style="height: 30px;" class="popover-confirm" @click="gShow = false">{{ $t('button.confirm2') }}</el-button>
                <el-button slot="reference" style="height: 30px;" @click="gShow = true">{{ $t('pages.chooseRuleGroup') }}</el-button>
              </el-popover>
              <el-button v-show="formable" :title=" $t('pages.addRuleGroup')" class="add-btn" @click="addGroup"><svg-icon icon-class="add" /></el-button>

              <el-collapse v-if="ruleGroupRows.length > 0" v-model="activeNames1" @change="handleChange">
                <el-collapse-item v-for="(ruleGroup, index) in ruleGroupRows" :key="index" :name="index">
                  <template slot="title">
                    {{ ruleGroup.name }}<i v-if="formable" class="el-icon-delete rule-group-delete" @click.stop="deleteItem('ruleGroup', index)"></i>
                  </template>
                  <group-form ref="groupForm" :form-datas="ruleGroup" :visit-only="true" :drip-able="dripAble" :drip-change-able="false" @ruleView="handleRuleView" @updateRulesMap="updateRulesMap"/>
                </el-collapse-item>
              </el-collapse>
            </FormItem>
          </el-col>
        </el-row>
        <el-divider content-position="left">{{ $t('pages.executionRules') }}</el-divider>
        <span>{{ $t('pages.tagRuleTips') }}</span>
        <el-divider content-position="left" style="margin: 0 15px">{{ $t('pages.responseRule') }}</el-divider>
        <FormItem :label="$t('pages.tagLevel')">
          <tree-select
            ref="groupTree"
            :data="labelTreeData"
            is-filter
            :checked-keys="tabCheckedKeys"
            multiple
            :width="500"
            style="width: 200px;"
            clearable
            check-strictly
            :disabled-nodes="disabledNodes"
            :render-content="renderContent"
            @change="treeSelectChange"
            @clickSelect="clickSelect"
          />
        </FormItem>
        <FormItem :label="$t('pages.documentLevel')">
          <tree-select
            :checked-keys="checkedKeys"
            :data="labelGradeTree"
            :width="200"
            style="width: 200px;"
            clearable
            @change="handleCheckChange"
          />
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <link-button btn-type="primary" btn-style="float: left" :formable="formable" :menu-code="'F31'" :link-url="'/contentStrategy/RuleGroup/group'" :btn-text="$t('pages.maintainRuleGroup')"/>
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="stgDialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <group-dialog ref="groupDialog" :drip-able="dripAble" :drip-change-able="false" :stg-code="281" :filter-high-config="true" @createData="createGroup" @groupClosed="groupClosed" />

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.highConfig')"
      :visible.sync="dialogVisible"
      width="650px"
    >
      <Form
        ref="dataForm"
        :model="dataForm"
        label-position="right"
        label-width="180px"
      >
        <FormItem label="文档等级修改模式">
          <el-radio-group v-model="dataList.labelLowerLevel.value">
            <el-radio :label="0">允许升级/降级</el-radio>
            <el-radio :label="1">只允许升级</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="showTagConfig" label="文档等级修改对象">
          <!-- (隐藏) -->
          <el-radio-group v-model="dataList.labelLevelMode.value">
            <el-radio :label="0">对全盘扫描/落地加的等级生效</el-radio>
            <el-radio :label="1">对手动添加/全盘扫描/落地加的等级生效</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem label="标签内容追加模式">
          <el-radio-group v-model="dataList.labelDealAdd.value">
            <el-radio :label="0">{{ $t('pages.cover') }}</el-radio>
            <el-radio :label="1" style="margin-left: 27px">{{ $t('pages.labelDetectRuleAdvanceConfig9') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="showTagConfig" label="标签内容修改对象">
          <!-- (隐藏) -->
          <el-radio-group v-model="dataList.labelDealMode.value">
            <el-radio :label="0">对全盘扫描/落地加的标签生效</el-radio>
            <el-radio :label="1">对手动添加/全盘扫描/落地加的标签生效</el-radio>
          </el-radio-group>
        </FormItem>

        <FormItem label="未命中规则时清除标签内容">
          <el-radio-group v-model="dataList.labelDealClean.value">
            <el-radio :label="0">不清除</el-radio>
            <el-radio :label="1" style="margin-left: 14px">清除</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="showTagConfig" label="">
          <!-- (隐藏) -->
          <el-radio-group v-model="dataList.labelDealCleanMode.value" :disabled="!dataList.labelDealClean.value">
            <el-radio :label="0" style="width: 250px;">对全盘扫描/落地加的标签生效</el-radio>
            <el-radio :label="1">对手动添加/全盘扫描/落地加的标签生效</el-radio>
          </el-radio-group>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveConfig">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { insertStg, update, getStrategyPage, deleteStg, updateConfig } from '@/api/dataEncryption/encryption/autoLabeling'
import { initTimestamp, isSameTimestamp, refreshPage } from '@/utils'
import { getRuleByIds as getGroupByIds, getSubRuleByIds } from '@/api/contentStrategy/rule/group'
import GroupDialog from '@/views/contentStrategy/rule/group/groupDialog'
import GroupForm from '@/views/contentStrategy/rule/group/groupForm'
import { listRuleGroupTreeNode, valiRules } from '@/api/contentStrategy/strategy/content'
import { getList } from '@/api/system/baseData/labelGradeLibrary'
import { getTreeNode } from '@/api/system/baseData/labelLibrary'
import { getLabelConfig } from '@/api/system/configManage/globalConfig'
import { getConfigByKey } from '@/api/system/configManage/globalConfig'
export default {
  name: 'AutoLabelingStrategy',
  components: { GroupDialog, GroupForm },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', fixed: true, fixedWidth: '150', iconFormatter: this.activeFormatter },
        { prop: 'ruleGroupIds', label: 'ruleGroupIds', width: '200', formatter: this.ruleGroupFormatter },
        { prop: 'value', label: 'respond', width: '200', formatter: this.respondRuleFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        stgTypeNumber: 281
      },
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        remark: '',
        active: true,
        dripScan: false,
        ruleGroupIds: '',
        objectGroupIds: [0],
        objectType: 3,
        type: 1,
        tagId: '',
        trankId: '',
        stgTypeNumber: 281,
        severity: 1
      },
      dataForm: {},
      listable: true,
      formable: true,
      deleteable: false,
      stgDialogVisible: false,
      submitting: false,
      dialogStatus: '',
      menuTitle: this.$t('pages.labelDetectionRule'),
      checkRules: [], // 各个分类包含的检测规则， [{ 'check': [], 'except': [] }, { 'check': [], 'except': [] }]
      relationMap: { '且': '&', '或': '|', '非': '!', '&': '且', '|': '或', '!': '非' },
      ruleGroupOptions: [], // 规则集所有树数据
      ruleGroupTreeDatas: [], // 规则集过滤后的树数据
      ruleGroupList: [], // 规则集选中的节点id
      ruleGroupRows: [], // 规则集选中的数据
      activeNames1: [], // 规则集列表展开的集合
      rulesMap: {}, // 规则详情，规则id作为key，规则对象为 value
      dripAble: false, // 是否支持零星检测
      curRoute: '',
      gShow: false,
      filterHighConfig: true,
      colDatas: [],
      labelTreeData: [],
      textMap: {
        update: this.i18nConcatText(this.$t('pages.labelDetectionRule'), 'update'),
        create: this.i18nConcatText(this.$t('pages.labelDetectionRule'), 'create'),
        delete: this.i18nConcatText(this.$t('pages.labelDetectionRule'), 'delete')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        ruleGroupList: [{ required: true, trigger: 'none', validator: this.ruleGroupValidator }]
      },
      checkedKeys: [],
      tabCheckedKeys: [],
      nodeKey: 'id',
      labelGradeTree: [],
      dialogVisible: false,
      showTagConfig: false,
      dataList: {
        labelLowerLevel: { isProp: false, label: '标签文档等级高于本次命中文档等级时，不允许降级', value: 0 },
        labelLevelMode: { isProp: false, label: '允许更改文档等级', value: 0 },
        labelDealMode: { isProp: false, label: '非手动标签内容添加模式(覆盖/追加)', value: 0 },
        labelDealClean: { isProp: false, label: '未命中规则时清空非手动添加的标签内容', value: 0 },
        labelDealCleanMode: { isProp: false, label: '未命中规则时清空所有标签内容', value: 0 },
        labelDealAdd: { isProp: false, label: '标签内容添加模式(覆盖/追加)', value: 0 }
      },
      filterString: ''
    }
  },
  computed: {
    popoverShow() {
      return this.curRoute == this.$route.fullPath
    },
    popperClass() {
      return this.popoverShow ? '' : 'hidden'
    },
    contentMask() {
      return this.gShow
    },
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
    ruleGroupList: {
      deep: true,
      handler(val) {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate('ruleGroupList')
      }
    },
    '$store.state.commonData.notice.updateLabelInfo'() {
      this.getTreeNode()
    },
    '$store.state.commonData.notice.updateFileGrade'() {
      this.loadGradeList()
    }
  },
  created() {
    this.curRoute = this.$route.fullPath
    initTimestamp(this)
    this.initRuleGroupOptions()
    this.resetTemp()
    this.loadGradeList()
    this.getTreeNode()
    this.getShowTagConfig()
  },
  activated() {
    // 从规则集页面返回后，需要更新规则集树数据
    if (!isSameTimestamp(this, 'Group')) {
      this.initRuleGroupOptions()
      this.$nextTick(() => {
        this.updateGroup = true
        this.updateRuleGroup()
      })
    }
    this.getRules(Object.keys(this.rulesMap).toString())
    initTimestamp(this)
    this.getShowTagConfig()
  },
  methods: {
    submitEnd(dlgStatus) {
      this.gridTable.execRowDataApi()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.stgDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs.dataForm) this.$refs['dataForm'].clearValidate()
        if (this.$refs.ruleGroup) this.$refs.ruleGroup.clearSelectedNodes()
        this.ruleGroupRows.splice(0)
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      const value = JSON.parse(this.temp.value)
      this.temp.tagId = value.tagId.split(',')
      this.temp.trankId = value.trankId
      this.tabCheckedKeys.splice(0, this.tabCheckedKeys.length, ...this.temp.tagId)
      this.checkedKeys.splice(0, this.checkedKeys.length, this.temp.trankId)
      this.temp.stgTypeNumber = this.defaultTemp.stgTypeNumber
      if (this.temp.ruleGroupIds) {
        this.ruleGroupList.splice(0, 0, ...this.temp.ruleGroupIds.split(','))
        getGroupByIds(this.temp.ruleGroupIds).then(res => {
          if (res.data && res.data.length > 0) {
            this.ruleGroupRows = res.data
            res.data.forEach((item, index) => {
              this.formatExprToTagOp(item, index)
            })
          } else {
            this.ruleGroupList.splice(0)
          }
        })
      }
      this.filterRuleGroupList(this.temp.dripScan, true)
      this.dialogStatus = 'update'
      this.stgDialogVisible = true
      this.$nextTick(() => {
        if (this.$refs['dataForm']) this.$refs['dataForm'].clearValidate()
        if (this.$refs.ruleGroup) this.$refs.ruleGroup.clearSelectedNodes()
        this.ruleGroupRows.splice(0)
      })
    },
    handleDrag() {
    },
    refresh() {
      return refreshPage(this)
    },
    selectable(row, index) {
      return true
    },
    selectionChangeEnd(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.strategyDefType = this.query.strategyDefType
      this.temp.dripScan = this.dripAble
      this.ruleGroupList.splice(0)
      this.activeNames1 = []
      this.checkedKeys = []
      this.tabCheckedKeys = []
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    valiResponseConfig() {
      return (this.temp.tagId.length == 0 || this.temp.tagId == null) && (this.temp.trankId == '' || this.temp.trankId == null)
    },
    valiTagLength() {
      const data = []
      if (this.temp.tagId && this.temp.tagId.length > 0) {
        data.push(...this.temp.tagId.filter(node => !node.includes('G')))
      }
      return data && data.length > 50
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.valiResponseConfig()) {
            this.$message({
              message: this.$t('pages.install_Msg15'),
              type: 'warning',
              duration: 3000
            })
            this.submitting = false
            return
          }
          if (this.valiTagLength()) {
            this.$message({
              message: '标签数量最大支持50个',
              type: 'warning',
              duration: 3000
            })
            this.submitting = false
            return
          }
          this.temp.ruleGroupIds = this.ruleGroupList.join(',')
          const value = { tagId: this.temp.tagId ? this.temp.tagId.join(',') : '', trankId: this.temp.trankId ? this.temp.trankId : '' }
          this.temp.value = JSON.stringify(value)
          insertStg(this.temp).then(respond => {
            this.submitting = false
            this.stgDialogVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.valiResponseConfig()) {
            this.$message({
              message: this.$t('pages.install_Msg15'),
              type: 'warning',
              duration: 3000
            })
            this.submitting = false
            return
          }
          if (this.valiTagLength()) {
            this.$message({
              message: '标签数量最大支持50个',
              type: 'warning',
              duration: 3000
            })
            this.submitting = false
            return
          }
          this.temp.ruleGroupIds = this.ruleGroupList.join(',')
          const value = { tagId: this.temp.tagId ? this.temp.tagId.join(',') : '', trankId: this.temp.trankId ? this.temp.trankId : '' }
          this.temp.value = JSON.stringify(value)
          update(this.temp).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.stgDialogVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    getTreeNode: function(option) {
      getTreeNode().then(respond => {
        const data = respond.data
        if (data && data.length > 0) {
          this.labelTreeData = data
        }
      })
    },
    loadGradeList() {
      getList().then(res => {
        const data = res.data
        if (data) {
          data.forEach(item => {
            const index = item.content.indexOf('zh:')
            item.label = index > -1 ? item.content.slice(index + 3, item.content.length) : item.content
          })
          this.labelGradeTree = data
        }
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds();
        deleteStg({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    groupTreeShow() {
      this.ruleGroupList.splice(0, 0)
      this.updateGroup = true
      this.groupSelectChange = true
      this.ruleGroupListStr = this.ruleGroupList.toString()
    },
    groupTreeHide() {
      // 选中的规则集发生变更，才会更新规则集列表
      if (this.ruleGroupListStr != this.$refs.ruleGroup.selectedKeys.toString()) {
        this.updateRuleGroup()
      } else {
        this.groupSelectChange = false
      }
      this.$refs.ruleGroup && this.$refs.ruleGroup.clearFilter()
    },
    handleChange() {

    },
    deleteItem(type, index) {
      const rows = type + 'Rows'
      const list = type + 'List'
      this[rows].splice(index, 1)
      this[list].splice(index, 1)
    },
    handleRuleView(id) {
      let isShow
      if (this.dripAble) {
        isShow = true
      } else {
        isShow = false
      }
      this.$refs.groupDialog.handleRuleView(this.rulesMap[id], isShow)
    },
    // 更新规则数组，用于查看规则详情
    updateRulesMap(id) {
      this.getRules(id)
    },
    getRules(ids) {
      getSubRuleByIds(ids).then(res => {
        res.data.forEach(item => {
          this.rulesMap[item.id] = item
        })
      })
    },
    groupClosed(data) {
      if (!data) {
        this.stgDialogVisible = true
        this.initRuleGroupOptions()
      }
    },
    // 更新规则集列表
    updateRuleGroup() {
      if (this.updateGroup) {
        // 从规则集树发起的更新以树选中id为准，其余途径发起的更新不变更选中规则集id
        const selectedKeys = this.$refs.ruleGroup.selectedKeys.filter(item => !item.includes('G'))
        this.ruleGroupList = this.groupSelectChange ? selectedKeys : this.ruleGroupList
        getGroupByIds(this.ruleGroupList.toString()).then(res => {
          const len = this.ruleGroupRows.length
          this.activeNames1 = []
          this.ruleGroupRows.splice(0, len, ...res.data)
          this.updateGroup = false
          this.groupSelectChange = false
          res.data.forEach((item, index) => {
            this.formatExprToTagOp(item, index)
          })
        })
      } else {
        this.$refs.ruleGroup.checkSelectedNode(this.ruleGroupList)
      }
    },
    async formatExprToTagOp(group, key) {
      this.checkRules[key] = { 'check': [], 'except': [] }
      if (group.ruleIds) {
        const tagOptionMap = {}
        // 获取分类中包含的规则详情
        // 需等待数据获取完，tagOptionMap赋值完毕，所以使用await
        await getSubRuleByIds(group.ruleIds).then(res => {
          res.data.forEach(item => {
            if (!tagOptionMap[item.id]) {
              tagOptionMap[item.id] = this.treeNodeDataToTagOption(item)
            }
            this.rulesMap[item.id] = item
          })
        })
        this.ExprToTag(group.checkExpr, tagOptionMap, this.checkRules[key].check)
        this.ExprToTag(group.exceptExpr, tagOptionMap, this.checkRules[key].except)

        if (group.doDrip) { // 零星检测规则的相关配置
          group.dripProps.forEach(drip => {
            if (!group.isCountMode) group.isCountMode = !!drip.countThreshold
          })
        }
      }
    },
    ExprToTag(expr, tags, checkRules) {
      if (!expr) return
      const checks = expr.replace(/&/g, '|').split('|')
      let cp = 0
      checks.forEach((id, i) => {
        let relation = ''
        if (i > 0) { // 首位没有关系符号&或|
          relation = expr.substring(cp, cp + 1)
          cp += 1
        }
        cp += String(id).length
        const tagOp = tags[id]
        if (tagOp) {
          checkRules.push(Object.assign({}, tagOp, { relation: this.relationMap[relation] }))
        }
      })
    },
    treeNodeDataToTagOption(nodeData) {
      return nodeData ? { id: nodeData.id, name: nodeData.name, relation: '或', type: nodeData.type } : null
    },
    getShowTagConfig() {
      getConfigByKey({ key: 'show.tagConfig' }).then(resp => {
        if (resp.data) {
          this.showTagConfig = JSON.parse(resp.data.value)
        }
      })
    },
    addGroup() {
      this.$refs.groupDialog.handleCreate()
    },
    createGroup(data) {
      this.$nextTick(() => {
        this.ruleGroupList.push(data.id)
        this.ruleGroupRows.push(data)
        this.updateGroup = true
        this.updateRuleGroup()
      })
    },
    initRuleGroupOptions() {
      listRuleGroupTreeNode().then(respond => {
        this.ruleGroupOptions = respond.data
        this.filterRuleGroupList(this.temp.dripScan, true)
      })
    },
    filterRuleGroupList: function(isDripScan, isInit) { // 过滤规则集树数据：普通和零星
      this.ruleGroupTreeDatas.splice(0)
      if (!isInit) {
        // 初始化不删除已选规则集，通过切换零星规则开关才清空
        this.ruleGroupRows.splice(0)
        this.ruleGroupList.splice(0)
      }
      for (let i = 0, size = this.ruleGroupOptions.length; i < size; i++) {
        const temp = this.ruleGroupOptions[i]
        if (temp.children && temp.children.length > 0) {
          temp.children = temp.children.filter(item => {
            return isDripScan && item.oriData.doDrip || !isDripScan && !item.oriData.doDrip
          })
          if (temp.children && temp.children.length > 0) {
            this.ruleGroupTreeDatas.push(temp)
          }
        }
      }
      this.valiRules()
    },
    async valiRules() {
      const filterData = []
      this.ruleGroupTreeDatas.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            if (child.oriData) {
              const json = {}
              json.id = child.oriData.id
              json.ruleIds = child.oriData.ruleIds
              filterData.push(json)
            }
          })
        }
      })
      const filterRuleGroupTreeDatas = []
      await valiRules(filterData).then(res => {
        if (res.data.length > 0) {
          this.filterString = res.data
        }
      })
      this.ruleGroupTreeDatas.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.children = item.children.filter(child => {
            return !child.oriData || !this.filterString || this.filterString.indexOf(child.oriData.id + '') > -1
          })
          if (item.children && item.children.length > 0) {
            filterRuleGroupTreeDatas.push(item)
          }
        }
      })
      this.ruleGroupTreeDatas = filterRuleGroupTreeDatas
    },
    ruleGroupFormatter(row, data) {
      const names = []
      const ruleGroups = data ? data.split(',') : []
      for (let i = 0, size = this.ruleGroupOptions.length; i < size; i++) {
        const children = this.ruleGroupOptions[i].children
        if (children && children.length > 0) {
          children.forEach((item) => {
            const id = item.id
            if (ruleGroups.indexOf(id) >= 0) {
              names.push(item.label)
            }
          })
        }
      }
      return names.join(',')
    },
    respondRuleFormatter(row, data) {
      const names = []
      const labelNames = []
      const gradeNames = []
      const info = JSON.parse(data)
      if (info && info.tagId) {
        const tagIds = info.tagId.split(',')
        if (tagIds && tagIds.length > 0) {
          for (let i = 0; i < this.labelTreeData.length; i++) {
            const label = this.labelTreeData[i];
            if (label && label.children && label.children.length > 0) {
              const labelChildren = label.children
              labelChildren.forEach(labelChild => {
                if (tagIds.indexOf(labelChild.id) > -1) {
                  labelNames.push(labelChild.label)
                }
              })
            }
          }
        }
      }
      if (labelNames && labelNames.length > 0) {
        names.push(`${this.$t('table.labelName')}：` + labelNames.join(','))
      }
      if (info && info.trankId) {
        for (let i = 0; i < this.labelGradeTree.length; i++) {
          const grade = this.labelGradeTree[i];
          if (info.trankId == grade.id) {
            gradeNames.push(grade.label)
          }
        }
      }
      if (gradeNames && gradeNames.length > 0) {
        names.push(`${this.$t('table.gradeName')}：` + gradeNames.join(','))
      }
      return names.join('; ')
    },
    disabledNodes(data, node) {
      return data.type == 'G'
    },
    renderContent(h, { node, data, store }) {
      const show = data.type == 'G'
      return (
        <div>
          <span>{data.label}</span>
          <span v-show={show} class='el-ic'>
            <span class='el-icon-circle-check' on-click={e => this.checkedAll(e, store, node, data)}>全选</span>
            <span class='el-icon-circle-close' on-click={e => this.cancelCheckedAll(e, store, node, data)}>取消全选</span>
          </span>
        </div>
      )
    },
    checkedAll(e, store, node, data) {
      if (node && node.childNodes && node.childNodes.length > 0) {
        node.childNodes.forEach(item => {
          item.checked = true
        })
      }
    },
    cancelCheckedAll(e, store, node, data) {
      if (node && node.childNodes && node.childNodes.length > 0) {
        node.childNodes.forEach(item => {
          item.checked = false
        })
      }
    },
    treeSelectChange(data, node, vm) {
      this.temp.tagId = data
    },
    clickSelect() {
      this.$nextTick(() => {
        this.$refs.groupTree && this.$refs.groupTree.clearFilter()
      })
    },
    handleCheckChange(data, node, vm) {
      this.temp.trankId = data
    },
    handleConfig() {
      this.dialogVisible = true
      this.getLabelConfig()
    },
    getLabelConfig() {
      getLabelConfig().then(res => {
        const data = res.data
        for (let i = 0; i < data.length; i++) {
          const item = data[i]
          if (item.key === 'labelLowerLevel') {
            this.dataList[item.key].value = parseInt(item.value)
          }
          if (item.key === 'labelDealMode') {
            this.dataList[item.key].value = parseInt(item.value)
          }
          if (item.key === 'labelDealClean') {
            this.dataList[item.key].value = parseInt(item.value)
          }
          if (item.key === 'labelLevelMode') {
            this.dataList[item.key].value = parseInt(item.value)
          }
          if (item.key === 'labelDealCleanMode') {
            this.dataList[item.key].value = parseInt(item.value)
          }
          if (item.key === 'labelDealAdd') {
            this.dataList[item.key].value = parseInt(item.value)
          }
        }
      })
    },
    ruleGroupValidator(rule, value, callback) {
      if (!this.ruleGroupList || this.ruleGroupList.length === 0) {
        callback(new Error(this.$t('pages.content_text19')))
      } else {
        callback()
      }
    },
    treeNodeCheckChange: function(data, node, vm) {
      if (data.type == 'G') {
        return false
      }
    },
    saveConfig() {
      this.submitting = true
      const formData = []
      Object.entries(this.dataList).forEach(([key, data]) => {
        if (!this.showTagConfig && key != 'labelLevelMode' && key != 'labelDealCleanMode' && key != 'labelDealMode') {
          formData.push({ key, value: data.value, type: data.type, isProp: !!data.isProp })
        } else if (this.showTagConfig) {
          formData.push({ key, value: data.value, type: data.type, isProp: !!data.isProp })
        }
      })
      updateConfig(formData).then(respond => {
        this.submitting = false
        this.dialogVisible = false
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
      }).catch(res => {
        this.submitting = false
      })
    }
  }
}
</script>

<style lang='scss' scoped>
  .content-mask{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: #000;
    opacity: 0.3;
    z-index: 3001;
  }
  .add-btn{
    width: 30px;
    height: 30px;
  }
  .popover-confirm {
    height: 30px;
    width: 60px;
    padding: 5px;
    margin-top: 5px;
    margin-bottom: -5px;
    float: right;
  }
  >>>.el-collapse{
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
  }
  >>>.el-collapse-item__header{
    height: 32px;
    line-height: 32px;
    color: #303133 !important;
    background-color: #e4e7e9;
    padding-left: 12px;
    border-bottom: 1px solid #cccccc;
    position: relative;
    &.is-active{
      border-bottom-color: transparent;
    }
    .rule-group-delete{
      color: red;
      position: absolute;
      top: 9px;
      right: 30px;
      display: none;
    }
    &:hover .rule-group-delete{
      display: block;
    }
  }
  >>>.el-collapse-item__wrap{
    background-color: #e4e7e9;
    border-bottom: 1px solid #cccccc;
  }
  >>>.el-collapse-item__content{
    padding: 10px 16px;
  }
</style>
