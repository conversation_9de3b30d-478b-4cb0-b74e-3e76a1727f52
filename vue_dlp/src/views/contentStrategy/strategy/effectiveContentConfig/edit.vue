<template>
  <div>
    <div v-permission="'114'">
      <el-divider content-position="left">{{ $t('pages.OCRImage') }}</el-divider>
      <FormItem :label="$t('pages.effectiveContent_text10')">
        <el-radio-group v-model="effectiveConfig[(addCode + 1001)]" :disabled="!formable">
          <el-radio :label="1">{{ $t('pages.ocrCheckImg1') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.ocrCheckImg2') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem :label="$t('pages.effectiveContent_text11')">
        <el-radio-group v-model="effectiveConfig[addCode + 1002]" :disabled="!formable">
          <el-radio :label="1">{{ $t('pages.ocrCheckImg1') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.ocrCheckImg2') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </div>
    <div>
      <el-divider content-position="left">{{ $t('pages.effectiveContent_text12') }}</el-divider>
      <FormItem :label="$t('pages.effectivechooseRuleGroup')" prop="timeout" :rules="[{ validator: requireSpecial, trigger: 'blur' }]">
        <el-col :span="6">
          <el-input-number v-model="effectiveConfig[addCode + 1003]" :max="10" :min="1" step-strictly :step="1" :controls="false" :disabled="!formable"/>
        </el-col>
        <el-col :span="3" style="margin-left: 5px;">
          <span>{{ $t('text.second') }}</span>
        </el-col>
      </FormItem>
      <FormItem v-if="effectiveConfig.hasOwnProperty(timeoutRespondTypeKey)" :label="$t('pages.effectiveContent_text15')" >
        <el-radio-group v-model="effectiveConfig[timeoutRespondTypeKey]" :disabled="!formable" @input="validateViolationResp(arguments[0], timeoutRuleIdKey)">
          <el-radio :label="0">{{ $t(timeoutFalseLabel) }}</el-radio>
          <el-radio :label="1">{{ $t(timeoutTrueLabel) }}
            <el-tooltip v-if="addCode===0" class="item" effect="dark" placement="bottom" content="内容检测策略或零星检测策略所配置的响应规则有勾选阻止数据外发以及泄露方式，才能触发检测超时阻断">
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </FormItem>
      <div v-if="effectiveConfig.hasOwnProperty(addCode + 1013)" class="error-skew">
        <FormItem :label="$t('table.respond')" prop="ifTimeoutRuleId" :rules="[{ validator: requireIfTimeoutRuleId, trigger: 'blur' }]">
          <response-content
            ref="timeoutRuleResp"
            show-select
            prop-check-rule
            read-only
            :show-check-rule="false"
            :check-empty-rule="effectiveConfig[timeoutRespondTypeKey] == 1"
            :editable="formable && effectiveConfig[timeoutRespondTypeKey] == 1"
            :prop-rule-id="effectiveConfig[timeoutRuleIdKey]"
            @getRuleId="getIfTimeoutRuleId"
          >
            <el-checkbox
              slot="tail"
              v-model="effectiveConfig[addCode + 1013]"
              :disabled="!formable || effectiveConfig[timeoutRespondTypeKey] == 0"
              class="out-going ellipsis"
              :title="$t('pages.respondActions10')"
            >{{ $t('pages.respondActions10') }}</el-checkbox>
          </response-content>
        </FormItem>
      </div>
      <span v-if="addCode===0" style="color: #0c60a5;margin-left:40px">
        {{ $t('pages.effectiveContent_text16') }}
      </span>
    </div>
    <!-- 敏感检测带密码配置-无法识别的文件 -->
    <div v-if="effectiveConfig.hasOwnProperty(unrecognizedFileRespondTypeKey)">
      <el-divider content-position="left">{{ $t('pages.effectivePasswordConfig') }}</el-divider>
      <FormItem :label="$t('pages.unrecognizedFile')">
        <el-radio-group v-model="effectiveConfig[unrecognizedFileRespondTypeKey]" :disabled="!formable" @input="validateViolationResp(arguments[0], unrecognizedFileRuleIdKey)">
          <el-radio :label="0">{{ $t(unrecognizedFileFalseLabel) }}</el-radio>
          <el-radio :label="1">{{ $t(unrecognizedFileTrueLabel) }}
            <el-tooltip v-if="addCode===0" class="item" effect="dark" placement="bottom" content="内容检测策略或零星检测策略所配置的响应规则有勾选阻止数据外发以及泄露方式，才能触发带密码口令文件阻断">
              <i class="el-icon-info" />
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </FormItem>
      <div v-if="effectiveConfig.hasOwnProperty(addCode + 1014)" class="error-skew">
        <FormItem :label="$t('table.respond')" prop="unrecognizedFileRuleId" :rules="[{ validator: requireUnrecognizedFileRuleId, trigger: 'blur' }]">
          <response-content
            ref="unrecognizedFileRuleResp"
            show-select
            prop-check-rule
            read-only
            :show-check-rule="false"
            :check-empty-rule="effectiveConfig[unrecognizedFileRespondTypeKey] == 1"
            :editable="formable && effectiveConfig[unrecognizedFileRespondTypeKey] == 1"
            :prop-rule-id="effectiveConfig[unrecognizedFileRuleIdKey]"
            @getRuleId="getUnrecognizedFileRuleId"
          >
            <el-checkbox
              slot="tail"
              v-model="effectiveConfig[addCode + 1014]"
              :disabled="!formable || effectiveConfig[unrecognizedFileRespondTypeKey] == 0"
              class="out-going ellipsis"
              :title="$t('pages.respondActions10')"
            >{{ $t('pages.respondActions10') }}</el-checkbox>
          </response-content>
        </FormItem>
      </div>
      <span style="color: #0c60a5;margin-left:40px">
        {{ $t('pages.respondActions14') }}
      </span>
      <br/>
      <span style="color: #0c60a5;margin-left:82px">
        {{ $t('pages.respondActions15') }}
      </span>
    </div>
    <div v-if="effectiveConfig.hasOwnProperty(addCode + 1009) || effectiveConfig.hasOwnProperty(addCode + 1016)">
      <el-divider content-position="left">{{ $t('pages.effectiveContent_text35') }}</el-divider>
      <FormItem v-if="effectiveConfig.hasOwnProperty(addCode + 1009)" :label="$t('pages.effectiveContent_text36')" :tooltip-content="$t(addCode==0 ? 'pages.effectiveContent_text37' : 'pages.effectiveContent_text43')" tooltip-placement="bottom-start">
        <el-radio-group v-model="effectiveConfig[addCode + 1009]" :disabled="!formable">
          <el-radio :label="1">{{ $t('pages.display') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.noDisplay') }}</el-radio>
        </el-radio-group>
      </FormItem>
      <FormItem v-if="effectiveConfig.hasOwnProperty(addCode + 1016)" :label="$t('pages.sensitiveContentTruncationLength')" :tooltip-content="$t('pages.effectiveContent_text38')" tooltip-placement="bottom-start" prop="timeout" :rules="[{ validator: requireSensitiveLentgh, trigger: 'blur' }]">
        <el-col :span="6">
          <el-input-number v-model="effectiveConfig[addCode + 1016]" :max="100" :min="0" step-strictly :step="1" :controls="false" :disabled="!formable"/>
        </el-col>
        <el-col :span="3" style="margin-left: 5px;">
          <span>{{ $t('pages.effectiveContent_text39') }}</span>
        </el-col>
      </FormItem>
    </div>
    <div v-show="effectiveConfig.hasOwnProperty(addCode + 1015) && addCode + 1015 !== 2015 && addCode + 1015 !== 3015">
      <el-divider content-position="left">{{ $t('pages.archiveCheckConfig') }}</el-divider>
      <FormItem :label="$t('pages.effectiveContent_text40')">
        <el-radio-group v-model="effectiveConfig[addCode + 1015]" :disabled="!formable">
          <el-radio :label="1">{{ $t('pages.continueCheck') }}</el-radio>
          <el-radio :label="0">{{ $t('pages.stopCheck') }}</el-radio>
        </el-radio-group>
      </FormItem>
    </div>
  </div>
</template>
<script>
import ResponseContent from '@/views/system/baseData/alarmSetup/responseContent.vue';

export default {
  name: 'Advanced',
  components: { ResponseContent },
  directives: {},
  props: {
    formable: { type: Boolean, default: true },  // 能否提交表单
    addCode: { type: Number, default: 0 }, // 添加的编码，例如编码为1001时，添加的编码为1000， 那么最终的编码就是2001
    timeoutTrueLabel: { type: String, default: 'pages.ifTimeout2' },
    timeoutFalseLabel: { type: String, default: 'pages.ifTimeout1' },
    unrecognizedFileTrueLabel: { type: String, default: 'pages.ifTimeout2' },
    unrecognizedFileFalseLabel: { type: String, default: 'pages.ifTimeout1' },
    effectiveConfig: {
      type: Object,
      default: function() {
        return {
          id: undefined,
          name: '',
          active: false,
          remark: '',
          entityType: undefined,
          entityId: undefined,
          effectiveContStgId: undefined,
          option: []
        }
      }
    }
  },
  data() {
    return {
    }
  },
  computed: {
    timeoutRespondTypeKey() {
      return this.addCode + 1004
    },
    timeoutRuleIdKey() {
      return (this.addCode + 1013) + 'RuleId'
    },
    unrecognizedFileRespondTypeKey() {
      return this.addCode + 1012
    },
    unrecognizedFileRuleIdKey() {
      return (this.addCode + 1014) + 'RuleId'
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    createInit() {
      if (this.$refs['timeoutRuleResp']) {
        this.$refs['timeoutRuleResp'].isShow = false
      }
      if (this.$refs['unrecognizedFileRuleResp']) {
        this.$refs['unrecognizedFileRuleResp'].isShow = false
      }
    },
    requireSpecial(rule, value, callback) {
      if (!this.effectiveConfig[this.addCode + 1003]) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    requireSensitiveLentgh(rule, value, callback) {
      console.log(1111, this.effectiveConfig[this.addCode + 1016])
      if (this.effectiveConfig[this.addCode + 1016] !== 0 && !this.effectiveConfig[this.addCode + 1016]) {
        callback(new Error(this.$t('text.cantNull')))
      } else {
        callback()
      }
    },
    requireIfTimeoutRuleId(rule, value, callback) {
      if (!this.effectiveConfig[this.timeoutRuleIdKey] && this.effectiveConfig[this.timeoutRespondTypeKey] == 1) {
        // 响应规则有错误提示信息，这边只校验，不显示信息
        callback(new Error(' '))
      }
      callback()
    },
    requireUnrecognizedFileRuleId(rule, value, callback) {
      if (!this.effectiveConfig[this.unrecognizedFileRuleIdKey] && this.effectiveConfig[this.unrecognizedFileRespondTypeKey] == 1) {
        // 响应规则有错误提示信息，这边只校验，不显示信息
        callback(new Error(' '))
      }
      callback()
    },
    getIfTimeoutRuleId(value) {
      this.effectiveConfig[this.timeoutRuleIdKey] = value
      this.$emit('validate', this.timeoutRuleIdKey)
    },
    getUnrecognizedFileRuleId(value) {
      this.effectiveConfig[this.unrecognizedFileRuleIdKey] = value
      this.$emit('validate', this.unrecognizedFileRuleIdKey)
    },
    validateViolationResp(val, propName) {
      if (val == 0) {
        this.$emit('validate', propName)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
 .error-skew {
   >>>.el-form-item__error {
     margin-left: 26px;
   }
 }
 .out-going {
  max-width: 200px;
  margin-left: 20px;
  vertical-align: middle;
  >>>.el-checkbox__label {
    white-space: nowrap;
  }
 }
</style>
