<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="stgCode"/>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      >
        <template slot="popoverContent" slot-scope="props">
          <div style="max-height: 500px; max-width: 600px; overflow: auto;">
            <span style="padding: 5px 10px; display: inline-block;" v-html="strategyFormatter(props.detail)"></span>
          </div>
        </template>
      </grid-table>
    </div>

    <tr-dialog
      v-el-drag-dialog
      type="drawer"
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
      @closed="closed"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="effectiveConfig"
        label-position="right"
        label-width="170px"
        style="width: 710px;"
      >
        <stg-target-form-item
          ref="formItem"
          :stg-code="stgCode"
          :form-data="effectiveConfig"
          :is-disabled="!formable"
          :is-disabled-type="dialogStatus != 'create'"
          :strategy-def-type="query.strategyDefType"
          label-width="100px"
        />
        <FormItem :label="$t('table.stgName')" prop="name" label-width="100px">
          <el-input v-model="effectiveConfig.name" v-trim :disabled="!formable" maxlength="20" />
        </FormItem>
        <FormItem :label="$t('table.remark')" label-width="100px">
          <el-input v-model="effectiveConfig.remark" :disabled="!formable" maxlength="100" show-word-limit class="word-limit-3digits" />
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')" label-width="100px">
          <el-switch v-model="effectiveConfig.active" :disabled="!formable" />
        </FormItem>
        <el-tabs ref="tabs" v-model="activeName" :before-leave="changeTab" @tab-click="tabClick">
          <el-tab-pane name="outCheckTab">
            <template slot="label">
              {{ '外发检测配置' }}
              <el-tooltip slot="content" class="item" effect="dark" placement="bottom-start" content="外发检测包括敏感检测外发配置的外发途径">
                <i class="el-icon-info"/>
              </el-tooltip>
            </template>
            <edit ref="outCheck" :formable="formable" :add-code="0" :effective-config="effectiveConfig" @validate="validateFields"/>
          </el-tab-pane>
          <el-tab-pane v-if="landCheckAble" name="landCheckTab">
            <template slot="label">
              {{ '落地检测配置' }}
              <el-tooltip slot="content" class="item" effect="dark" placement="bottom-start" :content="landCheckTooltip">
                <i class="el-icon-info"/>
              </el-tooltip>
            </template>
            <edit
              ref="landCheck"
              :formable="formable"
              :add-code="1000"
              :effective-config="effectiveConfig"
              :timeout-true-label="defaultOptionMap[2004]['trueLabel']"
              :timeout-false-label="defaultOptionMap[2004]['falseLabel']"
              :unrecognized-file-true-label="defaultOptionMap[2012]['trueLabel']"
              :unrecognized-file-false-label="defaultOptionMap[2012]['falseLabel']"
              @validate="validateFields"
            />
          </el-tab-pane>
          <el-tab-pane name="scanCheckTab">
            <template slot="label">
              {{ '全盘检测配置' }}
              <el-tooltip slot="content" class="item" effect="dark" placement="bottom-start" content="全盘检测包括全盘扫描和敏感文件自检">
                <i class="el-icon-info"/>
              </el-tooltip>
            </template>
            <edit
              ref="scanCheck"
              :formable="formable"
              :add-code="2000"
              :effective-config="effectiveConfig"
              :timeout-true-label="defaultOptionMap[3004]['trueLabel']"
              :timeout-false-label="defaultOptionMap[3004]['falseLabel']"
              :unrecognized-file-true-label="defaultOptionMap[3012]['trueLabel']"
              :unrecognized-file-false-label="defaultOptionMap[3012]['falseLabel']"
              @validate="validateFields"
            />
          </el-tab-pane>
        </el-tabs>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="stgCode"
          :formable="formable"
          :time-able="false"
          :form-data="effectiveConfig"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
        />
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </tr-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importEffectiveFunctionStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="stgCode"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import { getStrategyPage, getStrategyByName, createStrategy, updateStrategy, deleteStrategy
} from '@/api/contentStrategy/strategy/effectiveContentConfig'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity,
  objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { stgActiveIconFormatter } from '@/utils/formatter'
import { validatePolicy } from '@/utils/validate'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'
import edit from './edit'

export default {
  name: 'EffectiveContentConfig',
  components: { edit, ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      stgCode: 133,
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: 'stgMessage', ellipsis: false, type: 'popover', width: '200', originData: true, formatter: this.strategyFormatter },
        { prop: 'remark', label: 'remark', width: '100' },
        { prop: 'createdTime', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.sensitiveContentConfigStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.sensitiveContentConfigStg'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      activeName: 'outCheckTab',
      effectiveConfig: {
      },
      defaultEffectiveConfig: {
        id: null,
        name: '',
        active: false,
        remark: '',
        entityType: null,
        entityId: null,
        option: []
      },
      lossTypeOptions: [],
      // 特殊配置项：1013 1014 为响应规则id和允许文件外发申请的复合配置，大于0表示允许文件外发申请，小于0表示不允许文件外发申请，然后Math.abs(value)对应响应规则ID
      specialOptionIds: [1013, 1014],
      defaultOptionMap: {
        1001: { value: 1, label: '外发检测图片文件' },
        1002: { value: 1, label: '外发检测文档中图片' },
        1003: { value: 10, label: '外发检测超时时间' },
        1004: { value: 0, label: '外发检测超时响应方式', trueLabel: 'pages.ifTimeout2', falseLabel: 'pages.ifTimeout1' },
        // 1005: { value: 40, label: '敏感高级检测文件大小限制' },
        // 1006: { value: 0, label: '敏感加密文件是否允许外发' },
        // 1007: { value: 0, label: '敏感检测文件大小阈值' },
        // 1008: { value: 0, label: '敏感检测文件超过阈值处理方式' },
        1009: { value: 0, label: '外发检测显示敏感内容概要详情' },
        // 1010: { value: 0, label: '根据内容识别文件类型' },
        // 1011: { value: 0, label: '高级算法是否下移终端' },
        1012: { value: 1, label: '外发检测带密码口令且无法识别文件响应方式', trueLabel: 'pages.ifTimeout2', falseLabel: 'pages.ifTimeout1' },
        1013: { value: 0, label: '外发检测超时响应规则' },
        1014: { value: 0, label: '外发检测带密码口令且无法识别文件响应规则' },
        1015: { value: 0, label: '外发检测压缩包检测配置' },
        1016: { value: 0, label: '外发检测敏感内容概要截取长度' },

        2001: { value: 1, moduleId: [24, 51], label: '落地检测图片文件' },
        2002: { value: 1, moduleId: [24, 51], label: '落地检测文档中图片' },
        2003: { value: 10, moduleId: [24, 51], label: '落地检测超时时间' },
        2004: { value: 0, moduleId: [51], label: '落地检测超时响应方式', trueLabel: '智能加密检测时，加密检测超时的文件', falseLabel: 'pages.noDeal' },
        2012: { value: 1, moduleId: [51], label: '落地检测带密码口令且无法识别文件响应方式', trueLabel: '智能加密检测时，加密无法识别的文件', falseLabel: 'pages.noDeal' },
        2015: { value: 0, label: '落地检测压缩包检测配置' },

        3001: { value: 1, label: '全盘检测图片文件' },
        3002: { value: 1, label: '全盘检测文档中图片' },
        3003: { value: 10, label: '全盘检测超时时间' },
        3004: { value: 0, label: '全盘检测超时响应方式', trueLabel: '识别为敏感文件', falseLabel: '识别为非敏感文件' },
        3009: { value: 0, label: '全盘检测显示敏感内容概要详情' },
        3012: { value: 1, label: '全盘检测带密码口令且无法识别文件响应方式', trueLabel: '识别为敏感文件', falseLabel: '识别为非敏感文件' },
        3015: { value: 0, label: '全盘检测压缩包检测配置' },
        3016: { value: 0, label: '全盘检测敏感内容概要截取长度' }
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    targetOptionMap() { // 根据销售模块过滤配置项
      const moduleIds = [...this.$store.getters.saleModuleIds]
      if (moduleIds.length === 0) {
        return this.defaultOptionMap
      }
      for (const code in this.defaultOptionMap) {
        const opt = this.defaultOptionMap[code]
        if (opt.moduleId && opt.moduleId.length > 0) {
          let toDel = true
          for (const moduleId of opt.moduleId) {
            if (this.containSalModuleId(moduleIds, moduleId)) {
              toDel = false
            }
          }
          if (toDel) {
            delete this.defaultOptionMap[code]
          }
        }
      }
      return this.defaultOptionMap
    },
    landCheckAble() { // 销售模块是否支持落地检测配置
      const optionMap = this.targetOptionMap
      for (const code in optionMap) {
        if (code.startsWith('2')) {
          return true
        }
      }
      return false
    },
    landCheckTooltip() {
      let msg = '落地检测包括：'
      const moduleIds = [...this.$store.getters.saleModuleIds]
      if (moduleIds.length === 0) {
        msg += this.$t('route.smartEncStrategy') + '和' + this.$t('route.documentLabel')
      } else {
        let existPrev = false
        if (this.containSalModuleId(moduleIds, 51)) {
          msg += this.$t('route.smartEncStrategy')
          existPrev = true
        }
        if (this.containSalModuleId(moduleIds, 24)) {
          msg += (existPrev ? '和' : '') + this.$t('route.documentLabel')
        }
      }
      return msg
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    // 更新 store 中的销售模块id
    this.$store.dispatch('commonData/setSaleModuleIds')
  },
  methods: {
    containSalModuleId(moduleIds, moduleId) {
      return moduleIds.includes(moduleId) || moduleIds.includes(100 + moduleId) || moduleIds.includes(200 + moduleId) || moduleIds.includes(300 + moduleId)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    formatOldRowData(row) { // 格式化旧数据，用于升级兼容处理
      if (!row.option) {
        row.option = []
      }
      try {
        const existOpMap = row.option.reduce((acc, item) => {
          acc.set(item.id + '', item);
          return acc;
        }, new Map());
        for (const key in this.defaultOptionMap) {
          const existOp = existOpMap.get(key)
          if (!existOp) {
            // 由于旧版配置值的id范围  1000<id<2000，因此大于此值的id，如果自身没有赋值，则赋予旧id的值，即2009赋予1009的值
            const oldOpId = Number.parseInt(key) % 1000 + 1000
            const existOldOp = existOpMap.get(oldOpId + '')
            row.option.push({
              id: Number.parseInt(key),
              value: !existOldOp ? this.defaultOptionMap[key].value : existOldOp.value
            })
          }
        }
      } catch (e) {
        console.error(e)
      }
    },
    afterLoad(rowData, grid) {
      // 为了兼容旧策略，需要给rowData补充旧的配置项
      rowData.forEach(row => {
        this.formatOldRowData(row)
      })
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    formatOptionToMap(effectiveConfig) {
      const existOptionMap = {}
      if (effectiveConfig.option) {
        effectiveConfig.option.forEach(op => {
          existOptionMap[op.id] = op.value
        })
      }
      for (const key in this.defaultOptionMap) {
        if (existOptionMap.hasOwnProperty(key)) {
          this.$set(effectiveConfig, key, existOptionMap[key])
        } else {
          this.$set(effectiveConfig, key, this.defaultOptionMap[key].value)
        }
      }
      // 1013 1014 2013 2014 3013 3014 为响应规则id和允许文件外发申请的复合配置，大于0表示允许文件外发申请，小于0表示不允许文件外发申请，然后Math.abs(value)对应响应规则ID
      this.specialOptionIds.forEach(opId => {
        const opVal = effectiveConfig[opId]
        this.$set(effectiveConfig, opId + 'RuleId', Math.abs(opVal))
        this.$set(effectiveConfig, opId, opVal > 0)
      })
    },
    formatOptionToArray(effectiveConfig) {
      const optionArr = []
      for (const key in this.defaultOptionMap) {
        let targetVal = effectiveConfig[key]
        if (this.specialOptionIds.includes(Number.parseInt(key))) {
          targetVal = (effectiveConfig[key] ? 1 : -1) * effectiveConfig[key + 'RuleId']
          delete effectiveConfig[key + 'RuleId']
        }
        optionArr.push({ id: key, value: targetVal })
        delete effectiveConfig[key]
      }
      effectiveConfig.option = optionArr
      console.log(effectiveConfig)
      return effectiveConfig
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    tabClick(pane, event) {

    },
    changeTab(activeName, oldActiveName) {

    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.activeName = 'outCheckTab'
      this.effectiveConfig = JSON.parse(JSON.stringify(this.defaultEffectiveConfig))
    },
    handleCreate() {
      this.resetTemp()
      this.formatOptionToMap(this.effectiveConfig)
      this.effectiveConfig.entityType = this.query.objectType
      this.effectiveConfig.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['outCheck'].createInit()
        this.$refs['landCheck'].createInit()
        this.$refs['scanCheck'].createInit()
      })
    },
    sleep(millisecond) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve()
        }, millisecond)
      })
    },
    handleUpdate: async function(row) {
      if (!this.formable) {
        //  延迟0.3秒，等待created的数据加载完毕
        await this.sleep(300);
      }
      this.resetTemp()
      this.effectiveConfig = Object.assign({}, row) // copy obj
      this.formatOptionToMap(this.effectiveConfig)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['outCheck'].createInit()
        this.$refs['landCheck'].createInit()
        this.$refs['scanCheck'].createInit()
      })
    },
    handleShow: function(data) {
      this.resetTemp()
      this.effectiveConfig.option = data.effectiveConfig.option
      this.formatOptionToMap(this.effectiveConfig)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['outCheck'].createInit()
        this.$refs['landCheck'].createInit()
        this.$refs['scanCheck'].createInit()
      })
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: this.stgCode })
    },
    importSuccess() {
      this.handleFilter()
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.effectiveConfig, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate(async(valid, objs) => {
        if (valid) {
          const formData = this.formatOptionToArray(Object.assign({}, this.effectiveConfig))
          createStrategy(formData).then(res => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.strategyCreate'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.validFailSkip(objs)
          this.submitting = false
        }
      })
    },
    closed() {
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.effectiveConfig, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate(async(valid, objs) => {
        if (valid) {
          const formData = this.formatOptionToArray(Object.assign({}, this.effectiveConfig))
          updateStrategy(formData).then(res => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.strategyUpdate'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.validFailSkip(objs)
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyNameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const data = respond.data
        if (data && data.id !== this.effectiveConfig.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    strategyFormatter: function(row, data) {
      let msg = ''
      if (row.option) {
        row.option.forEach(op => {
          const dop = this.defaultOptionMap[op.id]
          if (dop && op.id !== 2015 && op.id !== 3015) {
            if (op.id == 1003 || op.id == 2003 || op.id == 3003) {
              // 增加超时时间参数
              msg += dop.label + '：' + op.value + '秒; '
            } else if (op.id == 1016 || op.id == 3016) {
              const value = op.value ? op.value : 0
              // 增加参数单位
              msg += dop.label + '：' + value + '个字符; '
            } else if (op.id == 1015) {
              console.log(1015, dop.label)
              const value = op.value ? this.$t('pages.continueCheck') : this.$t('pages.stopCheck')
              msg += dop.label + '：' + value + '; '
            } else if (dop.hasOwnProperty('trueLabel')) {
              msg += dop.label + '：' + (op.value ? this.$t(dop.trueLabel) : this.$t(dop.falseLabel)) + '; '
            } else if (op.value) {
              // 其它
              msg += dop.label + ';'
            }
          }
        })
      }
      return msg
    },
    validateFields(fields) {
      this.$refs['dataForm'] && this.$refs['dataForm'].validateField(fields)
    },
    validFailSkip(validObj) {
      const timeoutRule = validObj['ifTimeoutRuleId'];
      const unrecognizedFileRule = validObj['unrecognizedFileRuleId']
      if ((timeoutRule && timeoutRule[0] instanceof Error) ||
          (unrecognizedFileRule && unrecognizedFileRule[0] instanceof Error)) {
        this.activeName = 'outCheckTab'
      }
    }
  }
}
</script>
