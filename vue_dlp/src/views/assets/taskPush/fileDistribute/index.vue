<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ i18nConcatText($t('pages.distributeTasks'), 'create') }}
        </el-button>
        <el-button style="margin-left: 10px;" icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ i18nConcatText($t('pages.distributeTasks'), 'delete') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="this.$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyList" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <div v-if="!!formable" class="toolbar">
          <el-button @click="handleImport">
            {{ $t('button.insert') }}
          </el-button>
          <el-button :disabled="!deleteable1" @click="removeFile">
            {{ $t('button.delete') }}
          </el-button>
        </div>
        <div>
          <grid-table
            ref="fileList"
            :show-pager="false"
            :height="200"
            row-key="fileName"
            :col-model="colModel2"
            :row-data-api="rowDataApi1"
            :after-load="afterLoad2"
            @selectionChangeEnd="selectionChangeEnd1"
          />
        </div>
        <FormItem :label="$t('table.taskName')" prop="name" style="margin-top: 20px">
          <el-input v-model="temp.name" v-trim :disabled="!formable"></el-input>
        </FormItem>
        <FormItem v-if="treeable" :label="$t('table.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <FormItem :label="$t('pages.executionValidity')">
          <el-radio-group v-model="temp.noLimitTime">
            <el-radio :label="0">{{ $t('pages.limitedTime') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.permanentlyValid') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="temp.noLimitTime == 0" :label="$t('pages.startTime')">
          <el-date-picker
            v-model="temp.taskValidBeginTime"
            disabled
            type="datetime"
            :placeholder="$t('pages.selectDateTime')"
          >
          </el-date-picker>
        </FormItem>
        <FormItem v-if="temp.noLimitTime == 0" :label="$t('pages.endTime')">
          <el-date-picker
            v-model="temp.taskValidEndTime"
            :clearable="false"
            :editable="false"
            type="datetime"
            :picker-options="pickerOptionsEnd"
            :placeholder="$t('pages.selectDateTime')"
          >
          </el-date-picker>
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" type="textarea" rows="2" resize="none" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <el-checkbox v-model="temp.noTip" :true-label="1" :false-label="0" :disabled="!formable">{{ $t('pages.pushTaskTerminalMessage') }}</el-checkbox>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('text.insertInfo', { info: $t('pages.File') })"
      :visible.sync="dialogFormVisible1"
      width="600px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm2" :rules="rules" :model="temp2" label-position="right" label-width="80px">
        <el-row>
          <el-col :span="22">
            <FormItem :label="$t('pages.fileName')" prop="fileName">
              <el-input v-model="temp2.fileName" readonly/>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-upload
              ref="upload"
              name="uploadFile"
              action="1111"
              :limit="1"
              :on-change="fileChange"
              :show-file-list="false"
              :auto-upload="false"
            >
              <el-button type="primary" icon="el-icon-upload"></el-button>
            </el-upload>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.documentRemark')">
          <el-input v-model="temp2.fileMemo" :disabled="!formable" type="textarea" rows="2" resize="none" maxlength="100" show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('table.fileType')">
          <el-radio-group v-model="temp2.fileType">
            <el-radio :label="1">{{ $t('pages.normalFile') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.installFile') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem label-width="0">
          <el-row>
            <el-col :span="4">
              <el-checkbox v-model="temp2.isParam" :label="$t('pages.executionParameters')" :true-label="1" :false-label="0" :disabled="temp2.fileType==1 ||!formable"></el-checkbox>
            </el-col>
            <el-col :span="20">
              <el-input v-model="temp2.installParam" />
            </el-col>
          </el-row>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="createData2()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible1 = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getStrategyList, getStrategyByName, createStrategy, updateStrategy,
  deleteStrategy, importTransferFile, findList, deleteFile
} from '@/api/assets/taskPush/fileDistribute'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import moment from 'moment'

export default {
  name: 'FileDistribute',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.temp.taskValidBeginTime
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime() - 1 * 24 * 60 * 60 * 1000)
          }
        }
      },
      colModel: [
        { prop: 'name', label: 'taskName2', fixedWidth: '150' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: '', label: this.$t('pages.distributeDocuments'), width: '200', formatter: this.strategyFormat },
        { prop: 'taskValidEndTime', label: 'executionPeriod', width: '200', formatter: this.timeIncludeFormat },
        { prop: 'remark', label: 'remark', width: '200' },
        { prop: 'active', label: 'enable', fixedWidth: '80', type: 'icon', iconClass: 'active' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      colModel2: [
        { prop: 'fileName', label: 'fileName', fixedWidth: '150' },
        { prop: 'fileSize', label: 'maxFileSize2', width: '100', formatter: this.fileSizeFormat },
        { prop: 'installParam', label: this.$t('pages.operatingParameters'), width: '100' },
        { prop: 'fileMemo', label: 'remark', width: '200' }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      keyword: '',
      showTree: true,
      treeable: true,
      deleteable: false,
      deleteable1: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        noLimitTime: 0,
        taskValidBeginTime: '',
        taskValidEndTime: '',
        noTip: 0,
        entityType: '',
        entityId: undefined
      },
      temp2: {
        fileName: '',
        filePath: '',
        fileSize: '',
        fileMd5: '',
        fileMemo: '',
        fileType: 1,
        isParam: 0,
        installParam: ''
      },
      dialogFormVisible: false,
      dialogFormVisible1: false,
      dialogStatus: '',
      submitting: false,
      textMap: {
        update: this.formable ? this.i18nConcatText(this.$t('pages.fileDistributionTask'), 'update') : this.i18nConcatText(this.$t('pages.fileDistributionTask'), 'details'),
        create: this.i18nConcatText(this.$t('pages.fileDistributionTask'), 'create')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.infoRequired', { info: this.$t('pages.taskName') }), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ],
        fileName: [{ required: true, message: this.$t('pages.pleaseImportInfo', { info: this.$t('pages.File') }), trigger: 'blur' }]
      },
      downloadLoading: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
    'temp2.fileType'(newValue, oldValue) {
      if (newValue == 1) {
        this.temp2.isParam = 0
      }
    }
  },
  activated() {
  },
  created() {
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    afterLoad2(rowData, grid) {
      var selectMap = {}
      this.temp.fileList.forEach((item, index) => {
        selectMap[item.id] = item.id
      })
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          if (selectMap[item.id] != null) {
            grid.toggleRowSelection(item)
          }
        })
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    rowDataApi1: function(option) {
      return findList(option)
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    selectionChangeEnd1: function(rowDatas) {
      this.deleteable1 = rowDatas && rowDatas.length > 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.temp.fileList = []
    },
    resetTemp2() {
      this.temp2 = {
        fileName: '',
        filePath: '',
        fileSize: '',
        fileMd5: '',
        fileMemo: '',
        fileType: 1,
        isParam: 0,
        installParam: ''
      }
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    fileChange(file, fileList) {
      this.temp2.fileName = file.name
    },
    handleCreate() {
      this.resetTemp()
      const date = new Date()
      this.temp.taskValidBeginTime = moment(date).format('YYYY-MM-DD kk:mm:ss')
      this.temp.taskValidEndTime = moment(date).add(1, 'days').format('YYYY-MM-DD') + ' 23:59:59'
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.$refs.fileList.clearSelection()
      })
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      if (this.temp.taskValidEndTime == '0000-00-00 00:00:00') {
        this.temp.noLimitTime = 1
      } else {
        this.temp.noLimitTime = 0
      }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.$refs.fileList.execRowDataApi()
      })
    },
    handleImport() {
      this.dialogFormVisible1 = true
      this.resetTemp2()
    },
    handleExport() {},
    removeFile() {
      this.$confirmBox(this.$t('pages.confirmDeleteCheckedDataMsg', { info: this.$t('pages.File') }), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.$refs.fileList.getSelectedIds()
        deleteFile({ ids: toDeleteIds.join(',') }).then(respond => {
          this.$refs.fileList.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    validTemp() {
      if (this.temp.noLimitTime == 0 && moment(this.temp.taskValidBeginTime).isAfter(moment(this.temp.taskValidEndTime))) {
        this.$message({
          message: this.$t('pages.softwareTask_Validate1'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const selectDatas = this.$refs.fileList.getSelectedDatas()
      if (selectDatas == null || selectDatas.length == 0) {
        this.$message({
          message: this.$t('pages.pleaseTickContent', { content: this.$t('pages.distributeDocuments') }),
          type: 'error',
          duration: 2000
        })
        return false
      }

      if (this.temp.noLimitTime == 1) {
        this.temp.taskValidEndTime = '0000-00-00 00:00:00'
      }
      this.temp.fileList = this.$refs.fileList.getSelectedDatas()
      return true
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.validTemp()) {
            this.submitting = false
            return
          }
          createStrategy(this.temp).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    createData2() {
      this.submitting = true
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          if (this.temp2.isParam == 0) {
            this.temp2.installParam = ''
          }
          const formData = this.toFormData(this.temp2)
          const uploadFiles = this.$refs['upload'].uploadFiles
          formData.append('uploadFile', uploadFiles[0].raw) // 传文件
          importTransferFile(formData).then(respond => {
            this.submitting = false
            this.dialogFormVisible1 = false
            this.$refs.fileList.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.exportSucceeded'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.validTemp()) {
            this.submitting = false
            return
          }
          updateStrategy(this.temp).then(respond => {
            this.submitting = false
            this.gridTable.execRowDataApi()
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.delControlProcess'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const bean = respond.data
        if (bean && bean.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    fileSizeFormat(row, data) {
      return parseFloat(data / 1024 / 1024).toFixed(2) + 'MB'
    },
    timeIncludeFormat: function(row, data) {
      if (row.taskValidEndTime == '0000-00-00 00:00:00') {
        return this.$t('pages.permanentEffect')
      } else {
        return row.taskValidEndTime
      }
    },
    strategyFormat(row, data) {
      return row.fileList.map(item => { return item.fileName }).join('，')
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    }
  }
}
</script>
