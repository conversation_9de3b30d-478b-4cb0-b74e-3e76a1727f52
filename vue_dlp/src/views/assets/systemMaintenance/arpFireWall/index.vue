<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!addBtnAble" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.validateStgName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="strategySelectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        :hide-required-asterisk="true"
        label-position="right"
        label-width="80px"
        style="width: 700px; margin-left: 30px;"
      >
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30" />
        </FormItem>
        <FormItem :label="$t('text.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" show-word-limit class="word-limit-3digits" />
        </FormItem>
        <FormItem v-if="treeable" :label="$t('components.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-card class="box-card">
          <Form ref="ipPortForm" :rules="rules" :model="tempItem" :hide-required-asterisk="true" label-position="right" label-width="80px">
            <el-row>
              <el-col :span="24">
                <FormItem label="IP" prop="ip">
                  <el-input v-model="tempItem.ip" :disabled="!formable" style="width: 60%" />
                  <el-button style="padding: 5px;" @click="ipToMac">IP-->MAC</el-button>
                </FormItem>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <FormItem label="MAC" prop="mac">
                  <el-input v-model="tempItem.mac" :maxlength="12" :disabled="!formable" style="width: 60%"/>
                </FormItem>
              </el-col>
            </el-row>
          </Form>
          <div v-if="!!formable" style="margin: 5px 0;">
            <el-button style="padding: 5px;" @click="createItem">{{ $t('button.insert') }}</el-button>
            <el-button style="padding: 5px;" @click="updateItem">{{ $t('button.edit') }}</el-button>
            <el-button style="padding: 5px;" @click="deleteItem">{{ $t('button.delete') }}</el-button>
            <el-button style="padding: 5px;" @click="cancelItem">{{ $t('button.cancel') }}</el-button>
          </div>
          <grid-table
            ref="ipPortList"
            :height="200"
            :show-pager="false"
            :multi-select="false"
            :col-model="itemColModel"
            :row-datas="itemRowData"
            @currentChange="handleSelectionChange"
          />
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import {
  fetchList, getByName, createData, updateData, deleteData
} from '@/api/assets/systemMaintenance/arpFireWall'
import {
  enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity,
  objectFormatter, entityLink, refreshPage, buttonFormatter } from '@/utils'
import { isIPv4 } from '@/utils/validate'

export default {
  name: 'ArpFireWall',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150' },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { prop: 'keyword', label: 'stgMessage', width: '300', formatter: this.formatStrateMsg },
        { prop: 'remark', label: 'remark', width: '200' },
        { prop: 'active', label: 'enable', fixedWidth: '80', type: 'icon', iconClass: 'active' },
        {
          prop: 'createdTime',
          label: 'operate',
          type: 'button',
          fixed: 'right',
          fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate
          }]
        }
      ],
      itemColModel: [
        { prop: 'ip', label: 'IP', width: '40' },
        { prop: 'mac', label: 'MAC', width: '40' }
      ],
      options: [
        { value: 1, label: this.$t('pages.allDay') },
        { value: 2, label: this.$t('pages.morning') }
      ],
      query: {
        page: 1,
        searchInfo: '',
        entityType: undefined,
        entityId: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        ipMacList: [],
        entityType: undefined,
        entityId: undefined
      },
      tempItem: {},
      defaultTempItem: { // 表单字段
        id: undefined,
        ip: '0.0.0.0',
        mac: ''
      },
      validId: null, // 用来验证ip重复时，判断修改记录id的ip是否是当前ip
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.formable ? this.i18nConcatText(this.$t('pages.firewallSettings', 'update')) : this.i18nConcatText(this.$t('pages.firewallSettings', 'details')),
        create: this.i18nConcatText(this.$t('pages.firewallSettings', 'create'))
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.infoRequired', { info: this.$t('pages.stgName') }), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        ip: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' },
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        mac: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' },
          { validator: this.macValidator, trigger: 'blur' }
        ]
      },
      itemRowData: [],
      submitting: false
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
    'temp.alarm'(newValue, oldValue) {
      if (!newValue) {
        this.temp.notice = false
      }
    }
  },
  created() {
    this.resetTemp()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.addBtnAble = true
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
  },
  methods: {
    ipValidator(rule, value, callback) {
      if (value && isIPv4(value)) {
        const flag = this.itemRowData.some(item => {
          return item.ip == value && this.validId != item.id
        })
        if (flag) {
          callback(new Error('列表中已存在此IP记录！'))
        }
        callback()
      } else {
        callback(new Error(this.$t('pages.validateFile_6')))
      }
    },
    macValidator(rule, value, callback) {
      if (value.length != 12) {
        callback(new Error('MAC地址需输入12位!'))
      } else {
        callback()
      }
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    ipPortTable: function() {
      return this.$refs['ipPortList']
    },
    formatStrateMsg: function(row) {
      let msg = '绑定ARP信息：'
      if (row.ipMacList) {
        row.ipMacList.forEach(item => {
          msg += `【IP：${item.ip}，MAC：${item.mac}】`
        })
      }
      return msg
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return fetchList(searchQuery)
    },
    itemRowDataApi: function(option) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve({
            code: 20000,
            data: {
              items: this.temp.whiteList,
              total: 1
            }
          })
        }, 1)
      })
    },
    strategySelectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.itemRowData.splice(0, this.itemRowData.length)
      this.temp = Object.assign({}, this.defaultTemp)
      this.tempItem = Object.assign({}, this.defaultTempItem)
    },
    resetTempItem() {
      this.tempItem = Object.assign({}, this.defaultTempItem)
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.$refs['ipPortForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      this.temp.ipMacList.forEach(data => {
        this.itemRowData.push(data)
      })
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.$refs['ipPortForm'].clearValidate()
      })
    },
    handleImport() {
    },
    handleExport() {
    },
    handleSelectionChange(rowData) {
      this.tempItem = Object.assign({}, rowData)
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.ipMacList = this.ipPortTable().getDatas()
          createData(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.ipMacList = this.ipPortTable().getDatas()
          updateData(tempData).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteData({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    ipToMac() {

    },
    createItem() {
      this.validId = null
      this.$refs['ipPortForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempItem)
          rowData.id = new Date().getTime()
          this.itemRowData.unshift(rowData)
          this.cancelItem()
        }
      })
    },
    updateItem() {
      this.validId = this.tempItem.id
      this.$refs['ipPortForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempItem)
          for (let i = 0, size = this.itemRowData.length; i < size; i++) {
            const data = this.itemRowData[i]
            if (rowData.id === data.id) {
              this.itemRowData.splice(i, 1, rowData)
              break
            }
          }
          this.cancelItem()
        }
      })
    },
    deleteItem() {
      const rowData = Object.assign({}, this.tempItem)
      this.ipPortTable().deleteRowData([rowData.id], this.itemRowData)
    },
    cancelItem() {
      this.ipPortTable().clearSelection()
      this.resetTempItem()
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyNameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    }
  }
}
</script>
