<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :listable="listable" :treeable="treeable" :show-tree.sync="showTree" :showed-tree="['terminal']" :os-type-filter="7" @data-change="strategyTargetNodeChange"/>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>

        <el-button type="primary" icon="el-icon-plus" :disabled="!addBtnAble" size="mini" @click="handleCreate">
          {{ $t('button.addStrategy') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('button.deleteStrategy') }}
        </el-button>
        <el-button v-if="treeable" type="primary" icon="el-icon-menu" size="mini" @click="handleBind">
          {{ $t('pages.bindingWorks') }}
        </el-button>
        <el-button v-if="importAble" icon="el-icon-upload2" size="mini" @click="handleImport">{{ $t('button.import') }}</el-button>
        <el-button v-if="exportAble" icon="el-icon-download" :disabled="!deleteable" size="mini" @click="handleExport">{{ $t('button.export') }}</el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateStgName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="bindInfoListTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <div slot="title" class="el-dialog__title">
        {{ textMap[rootDialogStatus] }}
        <el-tooltip effect="dark" placement="bottom-start">
          <div slot="content">
            <i18n path="pages.ipMac_Msg9">
              <br slot="br"/>
            </i18n>
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </div>
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="90px"
        style="width: 700px;"
      >
        <stg-target-form-item
          ref="stgTargetItem"
          :stg-code="77"
          :form-data="temp"
          :multiple="false"
          :is-disabled="stgTargetDisabled"
          :strategy-def-type="query.strategyDefType"
          rewrite-node-click-fuc
          :node-click-fuc="policyTreeNodeCheckChange"
        />
        <el-tooltip effect="dark" :content="$t('pages.ipMac_Msg10')" placement="bottom-end" style="position: absolute;right: 30px;top: 65px;">
          <i class="el-icon-info" />
        </el-tooltip>
        <FormItem :label="$t('pages.stgName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30"/>
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.enable')">
          <el-switch v-model="temp.active" :disabled="!formable" />
        </FormItem>
        <el-card class="box-card" :body-style="{ 'padding': '3px' }">
          <div slot="header">
            <span>{{ $t('pages.iPAndMAC') }}</span>
          </div>
          <data-editor
            :formable="formable"
            :popover-width="480"
            :updateable="ipmacEditable"
            :deletable="ipmacDeleteable"
            :add-func="createIpmac"
            :update-func="updateIpmac"
            :delete-func="deleteIpmac"
            :cancel-func="cancelIpmac"
            :before-update="beforeUpdateIpmac"
          >
            <Form
              v-if="formable"
              ref="ipmacForm"
              :rules="ipmacrules"
              hide-required-asterisk
              :model="tempI"
              label-position="right"
              label-width="90px"
            >
              <el-row>
                <el-col :span="24">
                  <FormItem :label="$t('pages.macAddr')" prop="mac">
                    <el-input v-model="tempI.mac" maxlength="17" :placeholder="$t('pages.ipMac_Msg8')" />
                  </FormItem>
                </el-col>
                <el-col :span="24">
                  <FormItem :label="$t('pages.IPv4Addr')" prop="ip">
                    <el-input v-model="tempI.ip" maxlength="15" @blur="inputBlur('ipv6')" />
                  </FormItem>
                </el-col>
                <el-col :span="24" >
                  <FormItem :label="$t('pages.IPv6Addr')" prop="ipv6">
                    <el-input v-model="tempI.ipv6" maxlength="39" @blur="inputBlur('ip')" />
                  </FormItem>
                </el-col>
              </el-row>
            </Form>
            <!-- <div v-if="formable" style="float:right;margin: 5px 0;">
              <el-button size="small" @click="createIpmac">{{ $t('button.insert') }}</el-button>
              <el-button size="small" :disabled="!ipmacEditable" @click="updateIpmac">{{ $t('button.edit') }}</el-button>
              <el-button size="small" :disabled="!ipmacDeleteable" @click="deleteIpmac">{{ $t('button.delete') }}</el-button>
              <el-button size="small" @click="cancelIpmac">{{ $t('button.cancel') }}</el-button>
            </div> -->
            <div v-show="query.objectId" slot="attach-btn" style="display: inline-block;margin-right: 10px;">
              <el-button size="small" @click="handleImportFromTerminalBtnClick">{{ $t("pages.importFromTerminal") }}</el-button>
            </div>
          </data-editor>
          <grid-table
            ref="ipmacList"
            :height="200"
            :show-pager="false"
            :multi-select="true"
            :col-model="ipmacColModel"
            :row-datas="ipmacRowData"
            @selectionChangeEnd="ipmacSelectionChange"
          />
          <span style="color: #0e7beb; padding-right: 5%;padding-top: 5px; padding-bottom: 5px; display: inline-block;">
            <i18n path="pages.ipMac_Msg11">
              <br slot="br"/>
            </i18n>
          </span>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <copy-policy
          :dialog-status="dialogStatus"
          :stg-code="77"
          :formable="formable"
          :form-data="temp"
          :entity-click="entityClick"
          :create-data="createData"
          :strategy-def-type="query.strategyDefType"
          rewrite-node-click-fuc
          :node-click-fuc="policyTreeNodeCheckChange"
        />
        <el-button v-if="formable" :loading="submitting" type="primary" @click="rootDialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogBindFormVisible"
      width="900px"
      @dragDialog="handleDrag"
    >
      <el-container style="height: 460px;">
        <el-aside width="200px" :class="showTree?'':'hidden'">
          <tree-menu
            ref="groupTree"
            :data="treeData"
            :local-search="false"
            :get-search-list="getSearchListFunction"
            :render-content="renderContent"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="terminalTreeNodeChange"
          />
        </el-aside>
        <el-main>
          <div class="toolbar">
            <div class="searchCon">
              <el-input v-model="queryT.searchInfo" v-trim clearable :placeholder="$t('pages.terminalNameOrNumber')" style="width: 200px;" @keyup.enter.native="handleTermFilter" />
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleTermFilter">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </div>
          <grid-table
            ref="terminalListTable"
            :height="424"
            :col-model="terminalColModel"
            :row-data-api="terminalRowDataApi"
            @selectionChangeEnd="handleTerminalSelectionChange"
          />
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="dialogStatus !== 'importFromTerminal'" :loading="submitting" type="primary" @click="bindTerminal()">
          {{ $t('button.bind') }}
        </el-button>
        <el-button v-else type="primary" @click="handleImportFromTerminal">
          {{ $t('button.import') }}
        </el-button>
        <el-button @click="dialogBindFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <import-stg
      ref="importDlg"
      accept=".lds"
      :title="$t('pages.importIpAndMacBindStg')"
      data-type="-1"
      :auto-name="true"
      :term-able="listable && treeable"
      :user-able="listable && treeable"
      :strategy-type-number="77"
      @success="importSuccess"
    />
  </div>
</template>
<script>
import {
  getStrategyPage,
  getStrategyByName,
  createStrategy,
  updateStrategy,
  deleteStrategy,
  bindTerminal
} from '@/api/assets/systemMaintenance/IPAndMAC'
import { addRecycleNode } from '@/utils/tree'
import { getTerminalPage } from '@/api/system/terminalManage/terminal'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { enableStgDelete, buttonFormatter, objectFormatter, entityLink, refreshPage, hiddenActiveAndEntity } from '@/utils'
import { isSameTimestamp, initTimestamp } from '@/utils'
import { stgActiveIconFormatter } from '@/utils/formatter'
import { isIPv4, isIPv6, validatePolicy } from '@/utils/validate'
import ImportStg from '@/views/common/importStg'
import { exportStg } from '@/api/stgCommon'

export default {
  name: 'IPAndMAC',
  components: { ImportStg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    importAble: { type: Boolean, default: false }, // 是否支持导入,default改为false，此注解备注时间：2022-08-09
    exportAble: { type: Boolean, default: false } // 是否支持导出，default改为false，此注解备注时间：2022-08-09
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'stgName', width: '150', sort: 'custom', iconFormatter: stgActiveIconFormatter },
        { prop: 'entityName', label: 'source', type: 'button', width: '100',
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'iPv4AndMAC', width: '200', formatter: this.IPAndMACFormatter },
        { label: 'iPv6AndMAC', width: '200', formatter: this.IPv6AndMACFormatter },
        { prop: 'remark', label: 'remark', width: '100', sort: 'custom' },
        { label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined,
        searchInfo: ''
      },
      currentTerminalInfo: null,
      ipmacColModel: [
        { prop: 'mac', label: 'macAddr', width: '120' },
        { prop: 'ip', label: 'IPv4Addr', width: '140' },
        { prop: 'ipv6', label: 'IPv6Addr', width: '170' }
      ],
      terminalColModel: [
        { label: 'terminal', width: '150', formatter: this.nameFormatter },
        { prop: 'mainMac', label: 'macAddr', width: '150' },
        { prop: 'mainIp', label: 'ip', width: '150' }
      ],
      queryT: { // 查询条件
        page: 1,
        types: '0,1,2,16,17,18', // PC终端
        groupIds: '',
        searchInfo: '',
        useType: undefined
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        entityType: 1,
        entityId: undefined,
        entityName: '',
        active: false,
        strategyDefType: 0,
        name: '',
        ipMacs: [],
        remark: ''
      },
      tempI: {},
      defaultTempI: { // 表单字段
        id: undefined,
        ip: '',
        ipv6: '',
        mac: ''
      },
      selectedTerminalIds: [],
      treeSelectNode: [],
      treeData: [],
      defaultExpandedKeys: ['G0'],
      terminalCheckedId: '',
      dialogFormVisible: false,
      dialogBindFormVisible: false,
      dialogStatus: '',
      rootDialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.ipMacStg'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.ipMacStg'), 'create'),
        bind: this.$t('pages.bindingWorks'),
        importFromTerminal: this.$t('pages.importIpMacFromTerminal')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ]
      },
      ipmacrules: {
        ip: [
          { validator: this.ipValidator, trigger: 'blur' }
        ],
        ipv6: [
          { validator: this.ipv6Validator, trigger: 'blur' }
        ],
        mac: [
          { required: true, message: this.$t('pages.ipMac_Msg5'), trigger: 'blur' },
          { validator: this.macValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      ipmacEditable: false,
      ipmacDeleteable: false,
      ipmacRowData: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['bindInfoListTable']
    },
    // 生效对象禁用状态（新增策略 或 生效对象为空修改策略，不禁用）
    stgTargetDisabled() {
      // objectIds 没有值时，允许修改生效对象
      return this.temp.objectIds ? !!String(this.temp.objectIds) : false
    }
  },
  watch: {
    '$store.getters.deptTree'() {
      this.initGroupTreeNode()
    }
  },
  created() {
    initTimestamp(this)
    this.initGroupTreeNode()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    // eslint-disable-next-line no-undef
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.resetTemp()
    this.resetTempI()
  },
  activated() {
    if (!isSameTimestamp(this, 'Department')) {
      this.initGroupTreeNode()
      this.terminalTable() && this.terminalTable().execRowDataApi(this.queryT)
    }
  },
  methods: {
    policyTreeNodeCheckChange: function(data, node, vm) {
      if (data.id.indexOf('G' + data.dataId) >= 0) {
        return false
      }
    },
    handleImportFromTerminal(data) {
      for (const item of data) {
        // 去重
        const mac = item.mainMac.split('-').join('')
        let containIPv4 = false
        this.ipmacRowData.forEach(v => {
          const m = v.mac.split('-').join('')
          if (item.mainIp == v.ip && mac == m) {
            containIPv4 = true
          }
        })
        let containIPv6 = false
        this.ipmacRowData.forEach(v => {
          const m = v.mac.split('-').join('')
          if (item.mainIp == v.ipv6 && mac == m) {
            containIPv6 = true
          }
        })

        if (containIPv4 || containIPv6) {
          const msg = containIPv4 ? this.$t('pages.alreadyExistsSameCombinationIPv4Mac')
            : this.$t('pages.alreadyExistsSameCombinationIPv6Mac')
          this.$message({
            message: msg,
            type: 'error'
          })
          console.log(`${item.mainMac}, ${item.mainIp} 已存在 去重`)
          continue
        }

        this.ipmacRowData.push({
          id: new Date().getTime() + Math.floor(Math.random() * 1000),
          mac: item.mainMac,
          ip: !isIPv6(item.mainIp) ? item.mainIp : '',
          ipv6: isIPv6(item.mainIp) ? item.mainIp : ''
        })
      }
      this.$nextTick(() => {
        this.ipmacTable() && this.ipmacTable().setCurrentRow(null)
      })
      this.dialogBindFormVisible = false
    },
    handleImportFromTerminalBtnClick() {
      const { mainMac: mac, mainIp: ip } = this.currentTerminalInfo
      if (!mac && !ip) {
        this.$message({
          message: this.$t('pages.importFromTerminalNoBinding'),
          type: 'warning'
        })
        return
      }
      const msg = this.$t('pages.importFromTerminalTip', { mac, ip })
      this.$confirmBox(msg, this.$t('text.prompt')).then(() => {
        this.handleImportFromTerminal([this.currentTerminalInfo])
      }).catch(() => {})
    },
    async getCurrentTerminalInfo() {
      const resp = await getTerminalPage({ page: 1, ids: this.query.objectId })
      this.currentTerminalInfo = resp.data.items[0]
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
        this.getCurrentTerminalInfo()
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.addBtnAble = this.query.objectType != 3
      this.gridTable.execRowDataApi(this.query)
    },
    initGroupTreeNode: function() {
      getDeptTreeFromCache().then(res => {
        this.treeData = addRecycleNode(res.data)
      })
    },
    getSearchListFunction() {
      const groupList = this.$store.getters.deptTreeList || []
      return groupList
    },
    renderContent(h, { node, data, store }) {
      return h('div', { 'class': { 'custom-tree-node': data.type === 'D' }}, [h('span', data.label)])
    },
    // 获取所有子部门
    getAllChildrenGroupId(groups, groupIds) {
      groups.forEach(group => {
        if (group.id.indexOf('G') > -1) {
          groupIds.push(group.dataId)
          if (group.children && group.children.length > 0) {
            this.getAllChildrenGroupId(group.children, groupIds)
          }
        }
      })
    },
    terminalTreeNodeChange(data, node) {
      const groupIds = []
      if (data.id.indexOf('G') > -1) {
        groupIds.push(data.dataId)
      }
      if (data.children && data.children.length > 0) {
        this.getAllChildrenGroupId(data.children, groupIds)
      }
      node.expanded = true
      this.queryT.groupIds = groupIds.join(',')
      if (data.dataId == -2) {
        this.queryT.groupIds = undefined
        this.queryT.useType = '1,2'
      } else if (data.dataId == 0) {
        this.queryT.useType = undefined
      } else {
        this.queryT.useType = '0'
      }
      this.terminalTable().execRowDataApi(this.queryT)
    },
    ipmacTable() {
      return this.$refs['ipmacList']
    },
    deptTree() {
      return this.$refs['deptTree']
    },
    terminalTable() {
      return this.$refs['terminalListTable']
    },
    handleTerminalListFilter() {
      this.queryT.page = 1
      this.terminalTable().execRowDataApi()
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    terminalRowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.queryT, option)
      return getTerminalPage(searchQuery)
    },
    handleTermFilter: function() {
      if (this.terminalTable()) {
        this.terminalTable().execRowDataApi()
      }
    },
    handleTerminalSelectionChange: function(rowDatas) {
      this.selectedTerminalIds = []
      rowDatas.forEach(el => {
        this.selectedTerminalIds.push(el.id)
      })
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.resetTempI()
      this.ipmacRowData = []
      this.ipmacRowData.length = 0
    },
    resetTempI() {
      this.tempI = Object.assign({}, this.defaultTempI)
    },
    handleCreate() {
      this.resetTemp()
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.rootDialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row) // copy obj
      const ipMacs = this.temp.ipMacs
      ipMacs.forEach(data => {
        this.ipmacRowData.push(data)
      })
      this.rootDialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'] && this.$refs['dataForm'].clearValidate()
        this.$refs['ipmacForm'] && this.$refs['ipmacForm'].clearValidate()
        this.ipmacTable() && this.ipmacTable().setCurrentRow(null)
      })
    },
    handleBind: function(row) {
      this.dialogStatus = 'bind'
      this.dialogBindFormVisible = true
      if (this.terminalTable()) {
        this.terminalTable().execRowDataApi()
      }
    },
    handleImport() {
      this.$refs.importDlg.show()
    },
    handleExport() {
      const stgIds = this.gridTable.getSelectedIds()
      exportStg({ strategyIds: stgIds.join(','), stgTypeNumber: 77 })
    },
    importSuccess() {
      this.handleFilter()
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    ipmacSelectionChange(rowDatas) {
      this.ipmacDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.ipmacEditable = true
      } else {
        this.ipmacEditable = false
        this.cancelIpmac()
      }
    },
    createIpmac() {
      let validate
      this.$refs['ipmacForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempI)
          const mac = this.tempI.mac.split('-').join('')
          let containIPv4 = false
          this.ipmacRowData.forEach(data => {
            const m = data.mac.split('-').join('')
            if (rowData.ip == data.ip && mac.toLowerCase() == m.toLowerCase()) {
              containIPv4 = true
            }
          })
          let containIPv6 = false
          this.ipmacRowData.forEach(data => {
            const m = data.mac.split('-').join('')
            if (rowData.ipv6 == data.ipv6 && mac.toLowerCase() == m.toLowerCase()) {
              containIPv6 = true
            }
          })
          if (containIPv4 === true || containIPv6 === true) {
            if (containIPv4 === true && containIPv6 === true) {
              this.$message({
                message: this.$t('pages.alreadyExistsSameCombinationIPv46Mac'),
                type: 'error'
              })
            } else if (containIPv4 === true) {
              this.$message({
                message: this.$t('pages.alreadyExistsSameCombinationIPv4Mac'),
                type: 'error'
              })
            } else {
              this.$message({
                message: this.$t('pages.alreadyExistsSameCombinationIPv6Mac'),
                type: 'error'
              })
            }
          } else {
            rowData.id = new Date().getTime()
            let newMac = ''
            mac.split('').forEach((c, i) => {
              if (i > 0 && i % 2 == 0) {
                newMac += '-'
              }
              newMac += c
            })
            rowData.mac = newMac
            this.ipmacRowData.unshift(rowData)
            this.resetTempI()
            this.cancelIpmac()
          }
          validate = valid
        }
      })
      return validate
    },
    beforeUpdateIpmac() {
      this.tempI = Object.assign({}, this.ipmacTable().getSelectedDatas()[0])
    },
    updateIpmac() {
      let validate
      this.$refs['ipmacForm'].validate((valid) => {
        if (valid) {
          const rowData = Object.assign({}, this.tempI)
          const mac = this.tempI.mac.split('-').join('')
          let containIp = false
          this.ipmacRowData.forEach(data => {
            const m = data.mac.split('-').join('')
            if (rowData.id != data.id && rowData.ip == data.ip && rowData.ipv6 == data.ipv6 && mac.toLowerCase() == m.toLowerCase()) {
              containIp = true
            }
          })
          if (containIp) {
            this.$message({
              message: this.$t('pages.alreadyExistsSameCombinationIPv46Mac'),
              type: 'error'
            })
          } else {
            let newMac = ''
            mac.split('').forEach((c, i) => {
              if (i > 0 && i % 2 == 0) {
                newMac += '-'
              }
              newMac += c
            })
            rowData.mac = newMac
            for (let i = 0, size = this.ipmacRowData.length; i < size; i++) {
              const data = this.ipmacRowData[i]
              if (rowData.id === data.id) {
                this.ipmacRowData.splice(i, 1, rowData)
                break
              }
            }
            this.resetTempI()
            this.cancelIpmac()
          }
          validate = valid
        }
      })
      return validate
    },
    deleteIpmac() {
      const toDeleteIds = this.ipmacTable().getSelectedIds()
      this.ipmacTable().deleteRowData(toDeleteIds, this.ipmacRowData)
      this.resetTempI()
      this.cancelIpmac()
    },
    cancelIpmac() {
      this.ipmacTable().setCurrentRow()
      this.$refs['ipmacForm'].clearValidate()
      this.resetTempI()
    },
    bindTerminal() {
      const datas = this.terminalTable().getSelectedDatas()
      this.submitting = true
      if (!this.selectedTerminalIds || this.selectedTerminalIds.length == 0) {
        this.$message({
          message: this.$t('pages.chooseTerminal'),
          type: 'error'
        })
        this.submitting = false
        return
      }
      bindTerminal({ terminalIds: this.selectedTerminalIds.join(','), terminals: datas }).then(respond => {
        this.submitting = false
        this.dialogBindFormVisible = false
        this.gridTable.execRowDataApi()
        const msg = respond.data
        let title = this.$t('text.success')
        let type = 'success'
        if (msg.indexOf('<br/>') > -1) {
          title = this.$t('text.warning')
          type = 'warning'
        }
        this.$notify({
          title: title,
          message: msg,
          type: type,
          dangerouslyUseHTMLString: true,
          duration: 3000
        })
      }).catch(res => {
        this.submitting = false
      })
    },
    createData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.ipMacs = this.ipmacTable().getDatas()
          createStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      // 不是预定义策略，且对象类型和应用对象为空的时候，进行判断
      if (!validatePolicy(this.temp, this, this.query.strategyDefType)) {
        return
      }
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.ipMacs = this.ipmacTable().getDatas()
          updateStrategy(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.updateSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    nameFormatter(row, data) {
      return row.name + '(' + row.id + ')'
    },
    IPAndMACFormatter(row, data) {
      let ipAndMac = ''
      row.ipMacs.forEach(el => {
        if (el.ip) {
          if (ipAndMac) {
            ipAndMac = ipAndMac + ', '
          }
          ipAndMac = ipAndMac + el.ip + ' / ' + el.mac
        }
      })
      return ipAndMac
    },
    IPv6AndMACFormatter(row, data) {
      let ipAndMac = ''
      row.ipMacs.forEach(el => {
        if (el.ipv6) {
          if (ipAndMac) {
            ipAndMac = ipAndMac + ', '
          }
          ipAndMac = ipAndMac + el.ipv6 + ' / ' + el.mac
        }
      })
      return ipAndMac
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      refreshPage(this)
      this.addBtnAble = false
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    strategyNameValidator(rule, value, callback) {
      getStrategyByName({ name: value }).then(respond => {
        const strategy = respond.data
        if (strategy && strategy.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    inputBlur(validateProp) {
      this.$refs['ipmacForm'].validateField(validateProp)
    },
    ipValidator(rule, value, callback) {
      if (!value) {
        if (!this.tempI.ipv6) {
          callback(new Error(this.$t('pages.ipValidate', {})))
        } else {
          callback()
        }
      } else {
        if (!(isIPv4(value))) {
          callback(new Error(this.$t('pages.ipv4_text1')))
        } else {
          callback()
        }
      }
    },
    ipv6Validator(rule, value, callback) {
      if (!value) {
        if (!this.tempI.ip) {
          callback(new Error(this.$t('pages.ipValidate', {})))
        } else {
          callback()
        }
      } else {
        if (!isIPv6(value)) {
          callback(new Error(this.$t('pages.ipv6_text1')))
        } else {
          callback()
        }
      }
    },
    macValidator(rule, value, callback) {
      const re = /^[A-Fa-f0-9]+$/
      const val = value.split('-').join('')
      if (val.length != 12 || !re.test(val)) {
        callback(new Error(this.$t('pages.ipMac_Msg7')))
      } else {
        callback()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-container .el-main .tableBox{
    height: 420px;
    .el-table{
      height: 370px;
    }
  }
</style>
