<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :os-type-filter="7" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange"/>
    <div v-loading="showLoading" class="table-container system-container" :element-loading-text="$t('pages.loadingDesperately')">
      <div class="toolbar" style="margin-bottom: 0">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-checkbox v-model="autoRefresh" :true-label="1" :false-label="0" style="margin-left: 10px;font-weight: bold" @change="changeIntervalTime">
          {{ $t('pages.automaticRefreshTime') }}
        </el-checkbox>
        <el-input-number
          v-model="intervalTime"
          controls-position="right"
          :precision="0"
          :min="1"
          :max="60"
          style="width:120px;"
          :disabled="autoRefresh === 0"
          @blur="handleBlur"
          @change="changeIntervalTime"
        />
      </div>
      <el-row style="height:50%">
        <el-col :span="14">
          <fieldset style="padding: 2px 0 0 12px;">
            <legend>{{ $t('pages.basicInformation') }}</legend>
            <ul>
              <li>
                <label>
                  {{ $t('pages.computerName') }}
                  <el-tooltip effect="dark" placement="top-start">
                    <div slot="content">
                      {{ $t('pages.computerNameShowTip') }}
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                  ：
                </label>
                <span>
                  {{ temp.computerName }}
                  <el-button v-show="temp.computerName && showEditButton" type="text" class="el-icon-edit row-edit" style="padding:0 0 2px 3px;" @click="handleUpdateComputerName"/>
                </span>
              </li>
              <li>
                <label>{{ $t('pages.operatingSystem') }}：</label>
                <span>{{ temp.osName }}</span>
              </li>
              <li>
                <label>{{ $t('pages.startupTime') }}：</label>
                <span>{{ temp.bootTime }}</span>
              </li>
              <li>
                <label>{{ $t('pages.systemUser') }}：</label>
                <span>{{ temp.currentUser }}</span>
              </li>
              <li>
                <label>{{ $t('pages.totalPartitions') }}：</label>
                <span>{{ temp.diskNums }}</span>
              </li>
              <li>
                <label>{{ $t('pages.totalNetworkCards') }}：</label>
                <span>{{ temp.netCardNums }}</span>
              </li>
            </ul>
          </fieldset>
        </el-col>
        <el-col :span="10">
          <fieldset style="padding: 2px 0 0 12px;">
            <legend>{{ $t('pages.systemInformation') }}</legend>
            <ul style="margin: 0">
              <li>
                <label>{{ $t('pages.allPhysicalMemory') }}：</label>
                <span>{{ temp.totalSize }}</span>
              </li>
              <li>
                <label>{{ $t('pages.freePhysicalMemory') }}：</label>
                <span>{{ temp.freeSize }}</span>
              </li>
              <li>
                <label>{{ $t('pages.physicalMemoryUsage') }}：</label>
                <span>{{ temp.memoryUsage }}</span>
              </li>
              <el-divider></el-divider>
              <li>
                <label>{{ $t('pages.totalVirtualMemory') }}：</label>
                <span>{{ temp.totalVirtualSize }}</span>
              </li>
              <li>
                <label>{{ $t('pages.availableVirtualMemory') }}：</label>
                <span>{{ temp.freeVirtualSize }}</span>
              </li>
              <li>
                <label>{{ $t('pages.submitChanges') }}：</label>
                <span>{{ temp.pageChangeSize }}</span>
              </li>
              <el-divider></el-divider>
              <li>
                <label>{{ $t('pages.pagedMemory') }}：</label>
                <span v-if="temp.pagedMemory">{{ temp.pagedMemory + ' MB' }}</span>
              </li>
              <li>
                <label>{{ $t('pages.nonPagedMemory') }}：</label>
                <span v-if="temp.nonPagedMemory">{{ temp.nonPagedMemory + ' MB' }}</span>
              </li>
              <el-divider></el-divider>
              <li v-if="(temp.osName || '').indexOf('mac') == -1">
                <label>{{ $t('pages.numberHandles') }}：</label>
                <span>{{ temp.handleNum }}</span>
              </li>
              <li>
                <label>{{ $t('pages.numberProcess') }}：</label>
                <span>{{ temp.processNum }}</span>
              </li>
              <li>
                <label>{{ $t('pages.numberThreads') }}：</label>
                <span>{{ temp.threadNum }}</span>
              </li>
            </ul>
          </fieldset>
        </el-col>
      </el-row>
      <el-row style="height:50%;">
        <el-col :span="14">
          <div class="below" style="height:50%">
            <fieldset style="padding-top: 2px; padding-bottom: 0">
              <legend>{{ $t('pages.hardDiskInformation') }}</legend>
              <div class="table-container">
                <grid-table
                  ref="diskTable"
                  :col-model="diskColModel"
                  :row-datas="diskRowData"
                  :default-sort="diskDefaultSort"
                  :multi-select="multiSelect"
                  :show-pager="false"
                  :size="'mini'"
                />
              </div>
            </fieldset>
          </div>
          <div class="below" style="height:50%">
            <fieldset style="padding-top: 2px; padding-bottom: 0">
              <legend>{{ $t('pages.networkCardInformation') }}</legend>
              <div class="table-container">
                <grid-table
                  ref="netCardTable"
                  :col-model="netCardColModel"
                  :row-datas="netCardRowData"
                  :default-sort="netCardDefaultSort"
                  :multi-select="multiSelect"
                  :show-pager="false"
                  :size="'mini'"
                />
              </div>
            </fieldset>
          </div>
        </el-col>
        <el-col :span="10">
          <fieldset style="padding-top: 2px; padding-bottom: 0">
            <legend>{{ $t('pages.CPU_utilization') }}</legend>
            <div class="chart-container">
              <CpuChart height="270px" :chart-data="chartData"/>
            </div>
          </fieldset>
        </el-col>
      </el-row>
    </div>

    <el-dialog
      v-el-drag-dialog
      :title="$t('pages.sysFunction5')"
      :close-on-click-modal="false"
      :modal="false"
      :visible.sync="computerNameVisible"
      width="450px"
    >
      <Form ref="computerNameForm" :rules="rules" :model="computerNameForm" label-position="right" label-width="105px">
        <FormItem :label="$t('pages.computerName')" prop="oriComputerName">
          <el-input v-model="computerNameForm.oriComputerName" :disabled="true"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.newComputerName')" :tooltip-content="$t('pages.updateComputerNameTip')" tooltip-placement="top-end" prop="computerName">
          <el-input v-model="computerNameForm.computerName" v-trim clearable maxlength="15" @input="handleInput"></el-input>
        </FormItem>
      </Form>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="updateComputerName">{{ $t('button.confirm') }}</el-button>
        <el-button @click="computerNameVisible = false">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getSystemInfo, updateComputerName } from '@/api/assets/systemMaintenance/system'
import CpuChart from '@/components/ECharts/CpuChart'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'
import { getTermById } from '@/api/system/terminalManage/terminal'

export default {
  name: 'SystemInformation',
  components: { CpuChart },
  data() {
    return {
      multiSelect: false,
      diskDefaultSort: { prop: 'name', order: 'desc' },
      diskColModel: [
        { prop: 'name', label: 'diskName', width: '120', fixed: true, sort: true },
        { prop: 'type', label: 'diskFormat', width: '110', sort: true },
        { prop: 'totalSize', label: 'diskSize', width: '110', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'usedSize', label: 'usedSpace', width: '110', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'freeSize', label: 'remainingSpace', width: '110', sort: true, sortOriginal: true, formatter: this.sizeFormatter },
        { prop: 'usedPercent', label: 'diskUtilization', width: '110', sort: true, sortOriginal: true, formatter: this.usedPercentFormatter }
      ],
      netCardDefaultSort: { prop: 'mac', order: 'desc' },
      netCardColModel: [
        { prop: 'mac', label: 'physicalAddress', width: '150', fixed: true, sort: true },
        { prop: 'ips', label: 'networkAddressIPv4', width: '150', sort: true, formatter: this.arrayToStringFormatter },
        { prop: 'localIpv6s', label: 'localIpv6', width: '150', sort: true, formatter: this.arrayToStringFormatter },
        { prop: 'ipv6s', label: 'networkAddressIPv6', width: '150', sort: true, formatter: this.arrayToStringFormatter }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined
      },
      temp: {},
      defaultTemp: {
        computerName: '',
        osName: '',
        bootTime: '',
        currentUser: '',
        cpuUsage: '',
        diskNums: undefined,
        netCardNums: undefined,
        totalSize: undefined,
        freeSize: undefined,
        memoryUsage: undefined,
        totalVirtualSize: undefined,
        freeVirtualSize: undefined,
        pageChangeSize: undefined,
        handleNum: undefined,
        threadNum: undefined,
        processNum: undefined,
        pagedMemory: undefined,
        nonPagedMemory: undefined
      },
      showTree: true,
      timer: undefined,
      termId: undefined,
      termName: '',
      retFinished: true,
      diskRowData: [],
      netCardRowData: [],
      showLoading: false,
      ctrlAble: false,
      isLog: false, // 是否记录管理员日志，自动刷新不应该重复记录日志
      chartData: {
        yAxisData: []
      },
      timeout: 0,
      computerNameVisible: false,
      computerNameForm: {
        oriComputerName: '',
        computerName: ''
      },
      loading: false,
      rules: {
        computerName: [
          { required: true, message: this.$t('pages.validateMsg_enterName'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      respondCode: {
        // 0 操作成功 1 操作成功，但是超出15字节部分将被截断 2 字符串超出长度 3 开头为数字 4 含有非法字符 5 注册表打开失败
        // 6 注册表修改失败 7 回调函数为空 8 空指针或空字符串 9 win10以下系统含有非标准字符 10 开头或结尾为“-”
        0: this.$t('text.operateSuccess'),
        1: this.$t('pages.updateComputerNameRespondCode1'),
        2: this.$t('pages.updateComputerNameRespondCode2'),
        3: this.$t('pages.updateComputerNameRespondCode3'),
        4: this.$t('pages.updateComputerNameRespondCode4'),
        5: this.$t('pages.updateComputerNameRespondCode5'),
        6: this.$t('pages.updateComputerNameRespondCode6'),
        7: this.$t('pages.updateComputerNameRespondCode7'),
        8: this.$t('pages.updateComputerNameRespondCode8'),
        9: this.$t('pages.updateComputerNameRespondCode9'),
        10: this.$t('pages.updateComputerNameRespondCode10')
      },
      supportUpdateComputer: true,
      updateComputerNameLoading: false,
      showEditButton: true,
      intervalTime: 5,
      autoRefresh: 1
    }
  },
  activated() {
    clearInterval(this.timer)
    if (this.autoRefresh) {
      this.timer = setInterval(this.checkAndCallMethod, this.intervalTime * 1000)
    }
  },
  deactivated() {
    clearInterval(this.timer)
  },
  mounted() {
    clearInterval(this.timer)
    if (this.autoRefresh) {
      this.timer = setInterval(this.checkAndCallMethod, this.intervalTime * 1000)
    }
    this.triggerResize()
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange: function(tabName, data) {
      this.diskRowData.splice(0)
      this.netCardRowData.splice(0)
      this.temp = {}
      if (!data) return
      this.termId = undefined
      // x轴显示60秒数据
      this.chartData.yAxisData = Array(60).fill()
      this.showLoading = false
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.termId = data.dataId
        this.termName = data.label
        this.retFinished = true
        this.showLoading = true
        this.isLog = true
        // getSystemInfo(data.dataId);好像没有必要
        this.checkCtrlAble(this.termId)
      }
    },
    checkCtrlAble(termId) {
      const that = this
      this.ctrlAble = false
      clearInterval(this.timer)
      enableCtrlTerm('system', termId).then(resp => {
        if (resp.data < 1) {
          that.diskRowData.splice(0, that.diskRowData.length)
          that.netCardRowData.splice(0, that.netCardRowData.length)
          this.ctrlAble = false
          this.showLoading = false
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.validSupportUpdateComputer(this.termId)
          this.ctrlAble = true
          this.timeout = 0
          if (this.autoRefresh) {
            this.timer = setInterval(this.checkAndCallMethod, this.intervalTime * 1000)
          }
          this.sendToUser()
        }
      })
    },
    checkAndCallMethod() {
      if (this.timeout == 1) {
        this.timeout = 0
        this.sendToUser()
      }
    },
    sendToUser() {
      const that = this
      if (this.ctrlAble && that.termId && that.retFinished == true) {
        that.retFinished = false
        getSystemInfo(that.termId, this.isLog).then(respond => {
          const { diskInfos, netCardInfos, systemInfo, diskNums, netCardNums } = respond.data
          that.diskRowData.splice(0, that.diskRowData.length, ...diskInfos)
          const tempNetCardInfos = [...netCardInfos]
          tempNetCardInfos.forEach(data => {
            const localIpv6s = []
            const netIpv6s = []
            if (data.ipv6s) {
              data.ipv6s.forEach(row => {
                if (row.startsWith('fe80:')) {
                  localIpv6s.push(row)
                } else {
                  netIpv6s.push(row)
                }
              })
            }
            data.ipv6s = [...netIpv6s]
            data.localIpv6s = [...localIpv6s]
          })
          // that.netCardRowData.splice(0, that.netCardRowData.length, ...netCardInfos)
          that.netCardRowData.splice(0, that.netCardRowData.length, ...tempNetCardInfos)
          const { cpuUsage, totalSize, freeSize, totalVirtualSize, freeVirtualSize, totalPageSize, freePageSize } = systemInfo
          systemInfo.cpuUsage += '%'
          systemInfo.memoryUsage += '%'
          systemInfo.totalSize = this.sizeConversion(totalSize)
          systemInfo.freeSize = this.sizeConversion(freeSize)
          systemInfo.totalVirtualSize = this.sizeConversion(totalVirtualSize)
          systemInfo.freeVirtualSize = this.sizeConversion(freeVirtualSize)
          const changeSize = totalPageSize - freePageSize
          systemInfo.pageChangeSize = totalPageSize > 1024
            ? (changeSize / 1024).toFixed(2) + ' / ' + (totalPageSize / 1024).toFixed(2) + ' GB'
            : changeSize + '/' + totalPageSize + ' MB'
          const temp = that.temp
          temp.diskNums = diskNums
          temp.netCardNums = netCardNums
          Object.assign(temp, systemInfo)
          that.retFinished = true
          that.chartData.yAxisData.shift()
          that.chartData.yAxisData.push(cpuUsage)
          if (!this.ctrlAble) {
            that.diskRowData.splice(0, that.diskRowData.length)
            that.netCardRowData.splice(0, that.netCardRowData.length)
            that.temp = {}
            that.chartData.yAxisData.splice(0)
          }
        }).catch(e => {
          if (that.showLoading) {
            this.temp = {}
          }
        }).finally(t => {
          that.isLog = false
          that.retFinished = true
          that.showLoading = false
          this.timeout = 1
          this.updateComputerNameLoading = false
        })
      } else {
        that.isLog = false
        that.retFinished = true
        if (that.showLoading) {
          that.showLoading = false
          this.temp = {}
        }
        this.updateComputerNameLoading = false
      }
    },
    sizeConversion(size) {
      return size > 1024 ? (size / 1024).toFixed(2) + ' GB' : size + ' MB'
    },
    sizeFormatter: function(row, data) {
      return data + ' GB'
    },
    usedPercentFormatter: function(row, data) {
      return data + '%'
    },
    arrayToStringFormatter: function(row, data) {
      let ipsStr = ''
      if (data) {
        data.forEach(el => {
          if (ipsStr) {
            ipsStr = ipsStr + '<br/>' + el
          } else {
            ipsStr = el
          }
        })
      }
      return ipsStr
    },
    handleUpdateComputerName() {
      if (this.supportUpdateComputer === false) {
        this.$message({
          message: this.$t('pages.updateComputerNameOnlySupportVersion'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.computerNameForm.oriComputerName = this.temp.computerName
      this.computerNameForm.computerName = this.temp.computerName
      this.computerNameVisible = true
      this.$nextTick(() => {
        this.$refs['computerNameForm'].clearValidate()
      })
    },
    updateComputerName() {
      this.$refs['computerNameForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.termId !== undefined && this.computerNameForm.computerName !== '') {
            updateComputerName({ termId: this.termId, computerName: this.computerNameForm.computerName, oriComputerName: this.computerNameForm.oriComputerName }).then((resp) => {
              const data = resp.data
              // 0 操作成功 1 操作成功，但是超出15字节部分将被截断 2 字符串超出长度 3 开头为数字 4 含有非法字符 5 注册表打开失败
              // 6 注册表修改失败 7 回调函数为空 8 空指针或空字符串 9 win10以下系统含有非标准字符 10 开头或结尾为“-”
              if (data === 0 || data === 1) {
                this.loading = false
                this.computerNameVisible = false
                this.updateComputerNameLoading = true
                this.sendToUser()
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.respondCode[data],
                  type: 'success',
                  duration: 2000
                })
              } else {
                this.loading = false
                this.$message({
                  message: this.respondCode[data],
                  type: 'error',
                  duration: 2000
                })
              }
            }).catch(err => {
              this.loading = false
              console.error('修改计算机名称失败', err);
            })
          }
        }
      })
    },
    validSupportUpdateComputer(termId) {
      getTermById(termId).then((resp) => {
        const terminal = resp.data
        this.showEditButton = true
        const terType = terminal.type
        this.showEditButton = terType === 0 || terType === 16; // 修改计算机名称目前只支持Windows终端（Windows终端、Windows老板终端）
        const version = terminal.version ? terminal.version : ''
        this.supportUpdateComputer = '3.53.240827.SC'.localeCompare(version) < 0;
      })
    },
    nameValidator(rule, value, callback) {
      if (this.computerNameForm.oriComputerName == value) {
        callback(new Error(this.$t('pages.notSameWithOldComputerName')))
      } else {
        callback()
      }
    },
    handleInput(str) {
      // 汉字占两个字节，英文字母占一个字节
      let len = 0
      let subIndex = 15
      for (let i = 0; i < str.length; i++) {
        if (str.charCodeAt(i) > 255) { // 通过检测字符的unicode编码，编码大于255的即为汉字
          len += 2
        } else {
          len++
        }
        if (len > 15) {
          subIndex = subIndex < i ? subIndex : i
        }
      }
      if (len > 15) { // 超过15字节将不让输入
        this.computerNameForm.computerName = str.substring(0, subIndex)
      }
    },
    handleBlur(event) {
      const val = event.target.value.trim()
      if (!val) {
        this.intervalTime = 1
      }
    },
    changeIntervalTime() {
      clearInterval(this.timer)
      if (this.autoRefresh) {
        this.timer = setInterval(this.checkAndCallMethod, this.intervalTime * 1000)
      }
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    }
  }
}
</script>
<style lang="scss" scoped>
.el-col{
  height: 100%;
}
.table-container{
  overflow: hidden;
  .table-container{
    height: calc(100% - 20px);
    overflow: auto;
    .tableBox{
      height: calc(100% - 10px);
    }
  }
  .below{
    width: 100%;
    fieldset{
      min-width: 99%;
    }
  }
  fieldset{
    min-height: 100%;
    height: 100%;
    line-height: 23px;
    ul{
      height: calc(100% - 20px);
      margin-top: 0;
      font-size: 0.9em;
      list-style: none;
      overflow: auto;
    }
    .el-divider--horizontal{
      margin: 3px 0;
      width: 250px;
    }
  }
  .chart-container{
    height: calc(100% - 20px);
    overflow: auto;
  }
}
.system-container>>>.el-loading-mask{
  background-color: rgba(0, 0, 0, 0.7);
}
.row-edit{
  cursor: pointer;
  color: #0086da;
}
>>>.el-checkbox__label {
  padding-left: 0;
}
</style>
