<template>
  <div class="app-container">
    <TransferTree
      ref="transferTree"
      :show-tree.sync="showTree"
      :showed-tree="['terminal']"
      :os-type-filter="7"
      :terminal-filter-key="terminalFilter"
      @data-change="strategyTargetNodeChange"
    />
    <div v-loading="tableLoading" class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" :disabled="refreshDisable" size="mini" @click="refreshDevice">
          {{ $t('button.refresh') }}
        </el-button>
        <audit-log-exporter v-permission="'152'" :request="handleExport" :disabled="refreshDisable"/>
        <el-checkbox
          v-model="showHideDev"
          :true-label="1"
          :false-label="0"
          :disabled="!supportShowHideDev || termId === undefined"
          style="margin-left: 10px;font-weight: bold;"
          @change="showHideDevChange"
        >
          <span>{{ $t('pages.showHideDev') }}</span>
          <el-tooltip v-if="termId !== undefined && !supportShowHideDev" effect="dark" placement="top-start">
            <div slot="content">
              {{ $t('pages.termVersionNotSupport') }}<br/>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-checkbox>
      </div>

      <div class="tableBox" style="height: calc(100% - 40px);">
        <tree-menu
          ref="deviceTree"
          :data="deviceTreeData"
          :is-filter="false"
          :default-expanded-keys="defaultExpandedKeys"
          :render-content="renderDeviceContentFunc"
          @node-expand="nodeExpand"
          @node-collapse="nodeCollapse"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getSystemDevice, updateSystemDevice, exportSystemDevice } from '@/api/assets/systemMaintenance/device'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'SystemDevice',
  data() {
    return {
      deviceTreeData: [],
      defaultExpandedKeys: [],
      expandedNodeList: [],
      tableLoading: false,
      showTree: true,
      submitting: false,
      ctrlAble: false,
      refreshDisable: true,
      curNodeData: null,   // 记录当前选中的终端节点
      isLog: true, // 是否记录管理员日志，自动刷新不应该重复记录日志
      iconOption: {
        1: 'terminal',
        3: 'terminalGroup',
        'device': 'service',
        'activeDevice': 'active',
        'deactiveDevice': 'delete'
      },
      exportColModel: [
        { prop: 'devType', label: 'devType', width: 100 },
        { prop: 'devName', label: 'deviceName', width: 200 },
        { prop: 'state', label: 'status', width: 100, formatter: this.stateFormatter }
      ],
      termId: undefined,
      showHideDev: 0, // 0-不显示 1-显示
      supportShowHideDev: true
    }
  },
  computed: {
    deviceTree() {
      return this.$refs['deviceTree']
    },
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    }
  },
  methods: {
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('device', termId, [2]).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.refreshDisable = true
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange(tabName, data) {
      // 刷新 更新默认展开的key
      if (tabName == 'refresh') {
        this.defaultExpandedKeys = [
          ...new Set(this.expandedNodeList.map(item => item.id))
        ]
      }
      if (this.termId) {
        // 切换了终端
        if (this.termId != data.dataId) {
          this.showHideDev = 0
        }
      }
      this.termId = undefined
      this.supportShowHideDev = true
      this.deviceTreeData.splice(0, this.deviceTreeData.length)
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.refreshDisable = false
        this.checkCtrlAble(data.dataId).then(() => {
          if (!this.ctrlAble) return
          // 切换成其他终端 清除默认展开的key
          if (this.curNodeData !== data) {
            this.defaultExpandedKeys = []
            this.expandedNodeList = []
          }
          this.termId = data.dataId
          this.clickNode(tabName, data)
        })
      } else {
        // 部门 清除默认展开的key
        this.refreshDisable = true
        this.defaultExpandedKeys = []
        this.expandedNodeList = []
      }
    },
    clickNode(tabName, data) {
      this.curNodeData = data
      this.tableLoading = true
      getSystemDevice({ termId: data.dataId, showHideDev: this.showHideDev }).then(respond => {
        this.tableLoading = false
        // 25D2版本之前的旧终端，不支持显示隐藏设备，25D2版本终端，支持显示隐藏设备，通过响应是否存在字段isHide做区分
        this.supportShowHideDev = respond.data[0].oriData.isHide !== undefined
        const rootNode = Object.assign({}, data, { children: respond.data })
        this.deviceTreeData.splice(0, this.deviceTreeData.length)
        this.deviceTreeData.push(rootNode)
        if (!this.defaultExpandedKeys.includes(rootNode.id)) {
          this.defaultExpandedKeys.push(rootNode.id)
        }
      }).catch(e => {
        if (this.tableLoading) {
          this.tableLoading = false
        }
      })
    },
    handleExport(exportType) {
      return exportSystemDevice({ exportType, termId: this.curNodeData.dataId, showHideDev: this.showHideDev })
    },
    refreshDevice: function() {
      this.strategyTargetNodeChange('refresh', this.curNodeData)
    },
    updateDevice: function(nodeData, state) {
      if (state === 1) {
        this.$confirmBox(this.$t('pages.sureWhatTodo', { what: this.$t('table.disable') }), this.$t('text.prompt')).then(() => {
          this.execUpdateState(nodeData, state)
        }).catch((e) => {})
      } else {
        this.execUpdateState(nodeData, state)
      }
    },
    execUpdateState(nodeData, state) {
      const terminalNode = this.strategyTargetTree.getCurrentNode()
      nodeData.oriData.state = state === 0 ? 0.5 : -0.5
      updateSystemDevice({
        termId: this.curNodeData.dataId,
        devId: nodeData.dataId,
        devName: nodeData.label,
        state: state
      }).then(respond => {
        if (respond.data === 1) {
          this.$notify({ title: this.$t('text.success'), message: this.$t('pages.successMsg', { msg: (state === 0 ? this.$t('text.enable') : this.$t('text.disable')) }), type: 'success', duration: 2000 })
        } else if (respond.data === 0) {
          this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.failMsg', { msg: (state === 0 ? this.$t('text.enable') : this.$t('text.disable')) }), type: 'error', duration: 2000 })
        } else if (respond.data === 2) {
          this.$confirmBox(this.$t('pages.systemDevice_Content1'), this.$t('text.prompt'), { showCancelButton: false }).then(() => {
          })
        }
        this.refreshDevice()
      })
    },
    renderDeviceContentFunc(h, { node, data, store }) {
      if (data.type && data.type !== 'Map') {
        const iconClass = this.iconOption[data.type] ? <svg-icon icon-class={this.iconOption[data.type]} /> : ''
        return (<span>{iconClass} {node.label}</span>)
      } else if (data.parentId !== '0') {
        const iconClass = data.oriData.state === 0 ? this.iconOption['activeDevice'] : this.iconOption['deactiveDevice']
        const onText = data.oriData.state === 0.5 ? this.$t('text.enable1') : this.$t('text.enable')
        const offText = data.oriData.state === -0.5 ? this.$t('text.disable1') : this.$t('text.disable')
        const onDisableStatus = data.oriData.state !== 1
        const offDisableStatus = data.oriData.state !== 0
        if (data.oriData.canIDisableIt) {
          return (
            <span class='tree-node-label'>
              <span><svg-icon icon-class={iconClass} />{node.label}</span>
              <span class='node-btn'>
                <el-button disabled={onDisableStatus} size='small' type='text' on-click={ () => this.updateDevice(data, 0) }>{onText}</el-button>
                <el-button disabled={offDisableStatus} size='small' type='text' on-click={ () => this.updateDevice(data, 1)}>{offText}</el-button>
              </span>
            </span>
          )
        }
        return (<span><svg-icon icon-class={iconClass} />{node.label}</span>)
      } else {
        const iconClass = this.iconOption['device']
        return (<span><svg-icon icon-class={iconClass} />{node.label}</span>)
      }
    },

    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    nodeExpand(node) {
      !this.expandedNodeList.includes(node) && this.expandedNodeList.push(node)
    },
    nodeCollapse(node) {
      this.expandedNodeList = this.expandedNodeList.filter(item => item.id !== node.id)
    },
    stateFormatter(row, data) {
      if (data == undefined) { return '' }
      return data == 0 ? this.$t('text.enable') : this.$t('text.disable')
    },
    getRowData() {
      const deviceTreeData = this.deviceTreeData
      if (deviceTreeData && deviceTreeData[0] && deviceTreeData[0].children) {
        const treeData = deviceTreeData[0].children
        let curDevType = ''
        const list = []
        treeData.forEach(tree => {
          curDevType = tree.label
          if (tree.children) {
            tree.children.forEach(data => {
              list.push({ devType: curDevType, devName: data.label, state: data.oriData.state })
            })
          }
        })
        return list
      }
      return []
    },
    showHideDevChange(val) {
      this.refreshDevice()
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.tree-node-label .node-btn {
  margin-left: 15px;
}
</style>
