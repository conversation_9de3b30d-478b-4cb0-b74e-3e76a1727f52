<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :os-type-filter="7" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <el-tabs
        ref="tabs"
        v-model="activeName"
        type="card"
        style="padding:0 10px 10px 0"
        @tab-click="tabClick"
      >
        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.name">
          <div class="table-container">
            <div class="toolbar">
              <el-button type="primary" size="mini" @click="toggleTreeMenu">
                <svg-icon icon-class="tree" />
              </el-button>
              <el-button type="primary" icon="el-icon-refresh" size="mini" :disabled="refreshDisable" @click="refreshInfo">
                {{ $t('button.refresh') }}
              </el-button>
              <audit-log-exporter v-permission="'159'" :request="handleExport" :disabled="refreshDisable"/>
              <div class="searchCon">
                <el-input
                  v-model="query[item.query]"
                  v-trim
                  clearable
                  :placeholder="item.placeholder"
                  style="width: 160px"
                  @keyup.enter.native="handleFilter"
                ></el-input>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <grid-table
              :ref="item.name"
              v-loading="tableLoading"
              :col-model="item.colModel"
              :row-data-api="rowDataApi"
              :default-sort="item.defaultSort"
              :show-pager="false"
              :multi-select="multiSelect"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { ctrlErrorMap, enableCtrlTerm } from '@/api/system/terminalManage/moduleConfig'
import {
  exportLocalGroup,
  exportLocalUser,
  listLocalGroup,
  listLocalUser, updateLocalUser
} from '@/api/assets/systemMaintenance/localUserAndGroup'

export default {
  name: 'LocalUserAndGroup',
  data() {
    return {
      tabList: [
        { label: this.$t('pages.localUser'), name: 'localUser', query: 'userName', placeholder: this.$t('table.name'), defaultSort: { prop: 'userName' },
          colModel: [
            { prop: 'userName', label: 'name', width: '100', fixed: true, sort: true },
            { prop: 'userFullName', label: 'fullName', width: '150', sort: true },
            { prop: 'comment', label: 'fileDescription', width: '200', sort: true },
            { prop: 'sid', label: 'SID', width: '100', sort: true },
            { prop: 'domainName', label: 'domain', width: '100', sort: true, hidden: true },
            { prop: 'status', label: 'status', width: '100', sort: true, formatter: this.statusFormatter },
            { label: 'operate', type: 'button', fixedWidth: '120', fixed: 'right',
              buttons: [
                { label: 'allowEnable', click: (row) => this.handleUpdateStatus(row, 1), disabledFormatter: this.enableBtnDisabledFormatter },
                { label: 'disable', click: (row) => this.handleUpdateStatus(row, 0), disabledFormatter: this.disableBtnDisabledFormatter }
              ]
            }
          ]
        },
        { label: this.$t('pages.localGroup'), name: 'localGroup', query: 'groupName', placeholder: this.$t('table.name'), defaultSort: { prop: 'groupName' },
          colModel: [
            { prop: 'groupName', label: 'name', width: '100', fixed: true, sort: true },
            { prop: 'comment', label: 'fileDescription', width: '150', sort: true },
            { prop: 'sid', label: 'SID', width: '100', sort: true },
            { prop: 'domainName', label: 'domain', width: '100', sort: true, hidden: true }
          ]
        }
      ],
      multiSelect: false,
      rowData: [],
      showTree: true,
      ctrlAble: false,
      refreshDisable: true,
      query: {
        userName: '',
        groupName: ''
      },
      tableLoading: false,
      activeName: 'localUser'
    }
  },
  computed: {
    gridTable() {
      const curTab = this.getCurTab()
      return this.$refs[curTab.name][0]
    },
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    },
    queryApi() {
      return this.activeName === 'localUser' ? listLocalUser : this.activeName === 'localGroup' ? listLocalGroup : undefined
    },
    exportApi() {
      return this.activeName === 'localUser' ? exportLocalUser : this.activeName === 'localGroup' ? exportLocalGroup : undefined
    }
  },
  watch: {
    rowData: {
      deep: true,
      handler(val) {
        this.gridTable && this.gridTable.execRowDataApi()
      }
    }
  },
  created() {
  },
  methods: {
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('localUserAndGroup', termId, [2], '5.01').then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.refreshDisable = true
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) })
        } else {
          this.ctrlAble = true
        }
      })
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    rowDataApi(options) {
      const curTab = this.getCurTab()
      const searchInfo = (this.query[curTab.query] || '').toLowerCase()
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: searchInfo ? this.rowData.filter(data => data[curTab.query].toLowerCase().includes(searchInfo)) : this.rowData })
      })
    },
    strategyTargetNodeChange(tabName, data) {
      this.rowData = []
      if (!data || data.id.indexOf('G' + data.dataId) > -1) {
        this.refreshDisable = true
        return
      }
      this.checkCtrlAble(data.dataId).then(() => {
        if (!this.ctrlAble) return
        this.refreshDisable = false
        this.tableLoading = true
        const queryObj = this.getQueryObj()
        queryObj.termId = data.dataId
        this.queryApi(queryObj).then(respond => {
          this.rowData = []
          this.tableLoading = false
          respond.data.forEach((data, index) => {
            data.id = new Date().getTime() + index // 添加行ID
            this.rowData.push(data)
          })
        }).catch(e => {
          if (this.tableLoading) {
            this.tableLoading = false
          }
        })
      })
    },
    refreshInfo: function() {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      this.strategyTargetNodeChange(null, curNodeData)
    },
    handleDrag() {
    },
    handleExport() {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      const queryObj = this.getQueryObj()
      queryObj.termId = curNodeData.dataId
      return this.exportApi(queryObj)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleFilter() {
      this.gridTable && this.gridTable.execRowDataApi()
    },
    tabClick(tab, event) {
      this.refreshInfo()
    },
    getQueryObj() {
      return this.activeName === 'localUser' ? { userName: this.query.userName } : this.activeName === 'localGroup' ? { groupName: this.query.groupName } : undefined
    },
    getCurTab() {
      return this.tabList.find(item => item.name === this.activeName)
    },
    enableBtnDisabledFormatter: function(data) {
      return data.status === 1
    },
    disableBtnDisabledFormatter: function(data) {
      return data.status === 0
    },
    handleUpdateStatus(row, status) {
      const terminalNode = this.strategyTargetTree.getCurrentNode()
      const data = {
        termId: terminalNode.dataId,
        status: status,
        userNameLength: row.userNameLength,
        userName: row.userName
      }
      updateLocalUser(data).then(resp => {
        const flag = resp.data
        if (flag === 1) {
          this.$notify({
            title: this.$t('text.success'),
            message: status === 1 ? this.$t('pages.startupItem_Msg1') : this.$t('pages.startupItem_Msg2'),
            type: 'success',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshInfo()
          }, 1000)
        } else {
          this.$notify({
            title: this.$t('text.fail'),
            message: status === 1 ? this.$t('pages.startupItem_Msg3') : this.$t('pages.startupItem_Msg4'),
            type: 'error',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshInfo()
          }, 1000)
        }
      })
    },
    statusFormatter(row, data) {
      if (data === 1) {
        return this.$t('text.enable')
      } else if (data === 0) {
        return this.$t('text.disable')
      } else {
        return ''
      }
    }
  }
}
</script>
