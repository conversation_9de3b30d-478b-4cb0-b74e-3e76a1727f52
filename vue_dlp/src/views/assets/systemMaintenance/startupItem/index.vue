<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :os-type-filter="7" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <el-tabs
        ref="tabs"
        v-model="activeName"
        type="card"
        style="padding:0 10px 10px 0"
        @tab-click="tabClick"
      >
        <el-tab-pane v-for="(item,index) in tabList" :key="index" :label="item.label" :name="item.name">
          <div class="table-container" style="margin-left:10px">
            <div class="toolbar">
              <el-button type="primary" size="mini" @click="toggleTreeMenu">
                <svg-icon icon-class="tree" />
              </el-button>
              <el-button type="primary" icon="el-icon-refresh" :disabled="refreshDisable" size="mini" @click="refreshService">
                {{ $t('button.refresh') }}
              </el-button>
              <audit-log-exporter v-permission="'155'" :request="handleExport" :disabled="refreshDisable"/>
              <div class="searchCon">
                <el-input
                  v-model="query[item.query]"
                  v-trim
                  clearable
                  :placeholder="item.placeholder"
                  style="width: 160px"
                  @keyup.enter.native="handleFilter"
                ></el-input>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <grid-table
              :ref="item.name"
              v-loading="tableLoading"
              :col-model="item.colModel"
              :row-datas="rowData"
              :default-sort="item.defaultSort"
              :multi-select="false"
              :show-pager="false"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import {
  getSystemBootStart,
  updateDisableSystemBootStart,
  exportSystemBootStart,
  getSystemBootService,
  updateSystemBootService,
  exportSystemBootService,
  getSystemBootTask,
  updateSystemBootTask,
  deleteSystemBootTask,
  exportSystemBootTask,
  updateEnableSystemBootStart,
  deleteSystemBootStart
} from '@/api/assets/systemMaintenance/boot'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'StartupItem',
  data() {
    return {
      tabList: [
        { label: this.$t('pages.startupMaster'), name: 'startup', query: 'keyFileName', placeholder: this.$t('table.appOrProcessName'),
          defaultSort: { prop: 'keyFileName' },
          colModel: [
            { prop: 'keyFileName', label: 'appOrProcessName', width: '200', sort: true, fixed: true },
            { prop: 'valuePath', label: 'startCommand', width: '250', sort: true },
            { prop: 'descript', label: 'fileOrProductDesc', width: '200', sort: true },
            { prop: 'local', label: 'position', width: '250', sort: true },
            { prop: 'company', label: 'publisher', width: '150', sort: true },
            { prop: 'state', label: 'status', width: '100', sort: true, formatter: this.formatState },
            { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
              buttons: [
                { label: 'allowEnable', click: this.handleBootItemEnable,
                  formatter: this.formatStartBtn,
                  disabledFormatter: this.startBtnDisabledFormatter,
                  isShow: this.showStartupEnableAndDeleteBtn
                },
                { label: 'disable', click: this.handleBootItemDisable,
                  formatter: this.formatStopBtn,
                  disabledFormatter: this.stopBtnDisabledFormatter
                },
                { label: 'delete', click: this.handleBootItemDelete,
                  formatter: this.formatDeleteBtn,
                  disabledFormatter: (row) => row.effective !== 0,
                  isShow: this.showStartupEnableAndDeleteBtn
                }
              ]
            }
          ]
        },
        { label: this.$t('pages.startupServerMaster'), name: 'startupService', query: 'serviceName', placeholder: this.$t('pages.startupServerMasterName'),
          defaultSort: { prop: 'serviceName' },
          colModel: [
            { prop: 'serviceName', label: 'servername', width: '150', sort: true },
            { prop: 'displayName', label: 'displayName', width: '200', sort: true },
            { prop: 'descript', label: 'serverDes', width: '300', sort: true },
            { prop: 'state', label: 'status', width: '100', sort: true, formatter: this.formatState1 },
            { label: 'operate', type: 'button', fixedWidth: '150',
              buttons: [
                { label: 'allowEnable', click: (row) => this.handleServiceItemState(row, 0),
                  formatter: this.formatStartBtn,
                  disabledFormatter: this.startBtnDisabledFormatter
                },
                { label: 'disable', click: (row) => this.handleServiceItemState(row, 1),
                  formatter: this.formatStopBtn,
                  disabledFormatter: this.stopBtnDisabledFormatter
                }
              ]
            }
          ]
        },
        { label: this.$t('pages.taskPlanManagement'), name: 'taskPlan', query: 'taskName', placeholder: this.$t('pages.taskPlanManagementName'),
          defaultSort: { prop: 'taskName' },
          colModel: [
            { prop: 'taskName', label: 'name', width: '200', sort: true, fixed: true },
            { prop: 'taskFullName', label: 'position', width: '250', sort: true, formatter: this.formatTaskFullName },
            { prop: 'taskDescript', label: 'fileDescription', width: '250', sort: true },
            { prop: 'taskAuthor', label: 'manufacturerOrCreator', width: '150', sort: true },
            { prop: 'taskNextRunTime', label: 'taskNextRunTime', width: '160', sort: true },
            { prop: 'taskLastRunTime', label: 'taskLastRunTime', width: '160', sort: true },
            { prop: 'taskLastResult', label: 'taskLastResult', width: '150', sort: true, formatter: this.taskLastResultFormatter },
            { prop: 'taskPath', label: 'taskPath', width: '250', sort: true },
            { prop: 'state', label: 'status', width: '100', sort: true, formatter: this.formatState1 },
            { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
              buttons: [
                { label: 'allowEnable', click: (row) => this.handleTaskItemState(row, 0),
                  formatter: this.formatStartBtn,
                  disabledFormatter: this.startBtnDisabledFormatter
                },
                { label: 'disable', click: (row) => this.handleTaskItemState(row, 1),
                  formatter: this.formatStopBtn,
                  disabledFormatter: this.stopBtnDisabledFormatter
                },
                { label: 'delete', click: (row) => this.handleTaskItemDelete(row),
                  formatter: this.formatDeleteBtn,
                  disabledFormatter: this.deleteBtnDisabledFormatter
                }
              ]
            }
          ]
        }
      ],
      query: {
        keyFileName: '',
        serviceName: '',
        taskName: ''
      },
      isLog: true, // 是否记录管理员日志，自动刷新不应该重复记录日志
      refreshDisable: true,
      rowData: [],
      tempRowData: [],
      showTree: true,
      ctrlAble: false,
      tableLoading: false,
      activeName: 'startup'
    }
  },
  computed: {
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    },
    queryApi() {
      return this.activeName === 'startup' ? getSystemBootStart : (this.activeName === 'startupService' ? getSystemBootService : getSystemBootTask)
    },
    exportApi() {
      return this.activeName === 'startup' ? exportSystemBootStart : (this.activeName === 'startupService' ? exportSystemBootService : exportSystemBootTask)
    }
  },
  watch: {
    tempRowData(val) {
      this.handleFilter()
    }
  },
  methods: {
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('startup', termId, [2]).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.refreshDisable = true
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange(tabName, data) {
      this.tempRowData = []
      if (!data || data.id.indexOf('G' + data.dataId) > -1) {
        this.refreshDisable = true
        return
      }
      this.checkCtrlAble(data.dataId).then(() => {
        if (!this.ctrlAble) return
        this.tableLoading = true
        this.refreshDisable = false
        this.queryApi(data.dataId, this.isLog).then(respond => {
          this.tempRowData = [...respond.data]
          this.tableLoading = false
          this.isLog = true
        }).catch(e => {
          if (this.tableLoading) {
            this.tableLoading = false
          }
        })
      })
    },
    tabClick(tab, event) {
      this.refreshService()
    },
    showStartupEnableAndDeleteBtn(row) {
      // 旧版开机启动项管理面板（24D3）没有state字段，操作栏不显示启用和删除按钮
      return row.state !== undefined
    },
    deleteBtnDisabledFormatter: function(data) {
      return !!data.btnDeleteText
    },
    startBtnDisabledFormatter: function(data) {
      return data.state === 0 || !!data.btnStartText || data.effective === 0
    },
    stopBtnDisabledFormatter: function(data) {
      return data.state === 1 || !!data.btnStopText || data.effective === 0
    },
    handleFilter() {
      const curTab = this.tabList.find(item => item.name === this.activeName)
      const name = this.query[curTab.query] && this.query[curTab.query].toLowerCase()
      this.rowData = this.tempRowData.filter(item => {
        const itemName = item[curTab.query] && item[curTab.query].toLowerCase()
        return itemName.includes(name)
      })
    },
    handleExport() {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      const condition = this.activeName === 'startup' ? { keyFileName: this.query.keyFileName } : (this.activeName === 'startupService' ? { serviceName: this.query.serviceName } : { taskName: this.query.taskName })
      condition.termId = curNodeData.dataId;
      return this.exportApi(condition)
    },
    refreshService: function() {
      this.isLog = false
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      this.strategyTargetNodeChange(null, curNodeData)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleBootItemEnable(row) {
      const terminalNode = this.strategyTargetTree.getCurrentNode()
      row.btnStartText = this.$t('pages.allowActive')
      const params = {
        termId: Number(terminalNode.dataId),
        keyFileNameLen: row.keyFileNameLen,
        keyFileName: row.keyFileName,
        valuePathLen: row.valuePathLen,
        valuePath: row.valuePath,
        localLen: row.localLen,
        local: row.local
      }
      updateEnableSystemBootStart(params).then(respond => {
        if (respond.data === 1) {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.startupItem_Msg1'),
            type: 'success',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        } else {
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('pages.startupItem_Msg3'),
            type: 'error',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        }
      })
    },
    handleBootItemDisable(row) {
      const terminalNode = this.strategyTargetTree.getCurrentNode()
      row.btnStopText = this.$t('text.disable1')
      // 旧版本 和 25D1 版本，禁用需携带的参数不同
      const params = this.showStartupEnableAndDeleteBtn(row) ? {
        termId: Number(terminalNode.dataId),
        keyFileNameLen: row.keyFileNameLen,
        keyFileName: row.keyFileName,
        valuePathLen: row.valuePathLen,
        valuePath: row.valuePath,
        localLen: row.localLen,
        local: row.local
      } : {
        termId: Number(terminalNode.dataId),
        keyFileName: row.keyFileName,
        valuePath: row.valuePath
      }
      updateDisableSystemBootStart(params).then(respond => {
        if (respond.data === 1) {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.startupItem_Msg2'),
            type: 'success',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        } else {
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('pages.startupItem_Msg4'),
            type: 'error',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        }
      })
    },
    handleServiceItemState(row, state) {
      const terminalNode = this.strategyTargetTree.getCurrentNode()
      if (state === 0) {
        row.btnStartText = this.$t('pages.allowActive')
      } else {
        row.btnStopText = this.$t('pages.disableEnabling')
      }
      const params = {
        termId: Number(terminalNode.dataId),
        serviceName: row.serviceName,
        state
      }
      updateSystemBootService(params).then(respond => {
        if (respond.data === 1) {
          this.$notify({
            title: this.$t('text.success'),
            message: state === 0 ? this.$t('pages.startupItem_Msg1') : this.$t('pages.startupItem_Msg2'),
            type: 'success',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        } else {
          this.$notify({
            title: this.$t('text.fail'),
            message: state === 0 ? this.$t('pages.startupItem_Msg3') : this.$t('pages.startupItem_Msg4'),
            type: 'error',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        }
      })
    },
    handleTaskItemState(row, state) {
      const terminalNode = this.strategyTargetTree.getCurrentNode()
      if (state === 0) {
        row.btnStartText = this.$t('pages.allowActive')
      } else {
        row.btnStopText = this.$t('pages.disableEnabling')
      }
      const params = {
        termId: Number(terminalNode.dataId),
        taskName: row.taskName,
        taskFullName: row.taskFullName,
        state
      }
      updateSystemBootTask(params).then(respond => {
        if (respond.data === 1) {
          this.$notify({
            title: this.$t('text.success'),
            message: state === 0 ? this.$t('pages.startupItem_Msg1') : this.$t('pages.startupItem_Msg2'),
            type: 'success',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        } else {
          this.$notify({
            title: this.$t('text.fail'),
            message: state === 0 ? this.$t('pages.startupItem_Msg3') : this.$t('pages.startupItem_Msg4'),
            type: 'error',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        }
      })
    },
    handleBootItemDelete(row) {
      const terminalNode = this.strategyTargetTree.getCurrentNode()
      row.btnDeleteText = this.$t('pages.deleting')
      const params = {
        termId: Number(terminalNode.dataId),
        state: row.state,
        keyFileNameLen: row.keyFileNameLen,
        keyFileName: row.keyFileName,
        valuePathLen: row.valuePathLen,
        valuePath: row.valuePath,
        localLen: row.localLen,
        local: row.local
      }
      deleteSystemBootStart(params).then(respond => {
        if (respond.data === 1) {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        } else {
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('text.deleteFail'),
            type: 'error',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        }
      })
    },
    handleTaskItemDelete(row) {
      const terminalNode = this.strategyTargetTree.getCurrentNode()
      row.btnDeleteText = this.$t('pages.deleting')
      const params = {
        termId: Number(terminalNode.dataId),
        taskName: row.taskName,
        taskFullName: row.taskFullName
      }
      deleteSystemBootTask(params).then(respond => {
        if (respond.data === 1) {
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        } else {
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('text.deleteFail'),
            type: 'error',
            duration: 2000
          })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        }
      })
    },
    taskLastResultFormatter(row, data) {
      if (data == '0') {
        return this.$t('text.operateSuccess')
      } else if (data == '(null)') {
        return this.$t('pages.unknownType')
      } else {
        return data
      }
    },
    formatTaskFullName(row, data) {
      const index = data ? data.lastIndexOf('\\') : -1
      if (index > 0) {
        return data && data.slice(0, index)
      } else {
        return data
      }
    },
    formatState(row, state) {
      const effective = row.effective
      if (effective === 0) {
        return this.$t('pages.invalid')
      } else if (effective === 1 && state === 1) {
        return this.$t('table.disable')
      } else if (effective === 1 && state === 0) {
        return this.$t('table.allowEnable')
      } else {
        return state
      }
    },
    formatState1(row, state) {
      if (state === 0) {
        return this.$t('table.allowEnable')
      } else if (state === 1) {
        return this.$t('table.disable')
      } else {
        return state
      }
    },
    formatStartBtn(row, data) {
      return row.btnStartText ? data.btnStartText : this.$t('table.allowEnable')
    },
    formatStopBtn(row, data) {
      return row.btnStopText ? data.btnStopText : this.$t('table.disable')
    },
    formatDeleteBtn(row, data) {
      return row.btnDeleteText ? data.btnDeleteText : this.$t('table.delete')
    }
  }
}
</script>
