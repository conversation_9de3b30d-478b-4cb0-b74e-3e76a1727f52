<template>
  <div v-show="dialogVisible">
    <el-tabs v-model="activeName" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.vnc_Session')" name="infoTab">
        <info-panel ref="infoPanel" :is-show="isShowInfo" :term-id="termId" @stop="stop"/>
      </el-tab-pane>
      <el-tab-pane v-if="clipboardEnable" :label="$t('pages.vnc_Cliboard')" name="clipboardTab">
        <template slot="label">
          {{ $t('pages.vnc_Cliboard') }}
          <el-tooltip class="item" effect="dark" placement="bottom" :content="$t('pages.vnc_Cliboard4')">
            <i class="el-icon-info"/>
          </el-tooltip>
        </template>
        <clipboard ref="clipboardPanel" :is-show="isShowClipboard" :term-id="termId"/>
      </el-tab-pane>
      <el-tab-pane v-if="fileEnable" :label="$t('pages.vnc_Transfer')" name="fileTab">
        <file-transfer ref="filePanel" :is-show="isShowFile" :term-id="termId"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { enableCtrlTerm } from '@/api/system/terminalManage/moduleConfig'
import InfoPanel from './infoPanel'
import Clipboard from './clipboard'
import FileTransfer from './fileTransfer'

export default {
  name: 'ControlPanel',
  components: { InfoPanel, Clipboard, FileTransfer },
  props: { },
  data() {
    return {
      dialogVisible: false,
      activeName: 'infoTab',
      termId: 11253,
      isShowInfo: false,
      isShowFile: false,
      isShowClipboard: false,
      clipboardEnable: false,
      fileEnable: false
    }
  },
  methods: {
    tabClick(panel, event) {
      this.changeShowStatus(panel.name)
    },
    changeShowStatus(val) {
      this.isShowInfo = val === 'infoTab'
      this.isShowClipboard = val === 'clipboardTab'
      this.isShowFile = val === 'fileTab'
    },
    stop() {
      this.$emit('stop')
    },
    show(data) {
      this.termId = Number.parseInt(data.termId)
      const isVerEnable = data.termVer && data.termVer.localeCompare('3.55.') > 0
      // 3.55版本才支持
      this.clipboardEnable = isVerEnable && this.hasPermission('799')
      if (this.clipboardEnable) {
        enableCtrlTerm('clipboard', this.termId).then(resp => {
          if (resp.data < 1) {
            this.clipboardEnable = false
          }
        })
      }
      this.fileEnable = isVerEnable && this.hasPermission('797|798')
      if (this.fileEnable) {
        enableCtrlTerm('explorer', this.termId).then(resp => {
          if (resp.data < 1) {
            this.fileEnable = false
          }
        })
      }
      this.dialogVisible = true
      this.$refs.infoPanel.start()
      this.$nextTick(() => {
        window.setTimeout(() => {
          this.changeShowStatus(this.activeName)
        }, 1000)
      })
    },
    hide() {
      this.dialogVisible = false
      this.isShowInfo = false
      this.activeName = 'infoTab'
      this.$refs.infoPanel && this.$refs.infoPanel.init()
      this.$refs.clipboardPanel && this.$refs.clipboardPanel.init()
      this.$refs.filePanel && this.$refs.filePanel.init()
    }
  }
}
</script>

<style lang="scss" scoped>
  >>>.el-tabs__item {
    padding: 0 15px;
  }
  >>>.el-tabs__content {
    padding: 0 5px;
    overflow: auto;
  }
</style>
