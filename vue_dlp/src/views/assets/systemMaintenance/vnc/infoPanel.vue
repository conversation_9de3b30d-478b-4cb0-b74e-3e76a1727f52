<template>
  <div class="info-panel">
    <el-row>
      <el-col :span="6">
        <svg-icon style="color:green; width: 50px;height: 50px;" :icon-class="termIconOpts[termInfo.type] || 'terminal'"/>
      </el-col>
      <el-col :span="18" style="padding-top: 10px;">
        <label class="label-title">{{ termInfo.mainIp }}</label>
      </el-col>
    </el-row>
    <div class="div-contain">
      <label class="label-title">{{ $t('route.terminalInfo') }}</label>
      <el-divider/>
      <el-descriptions :column="1" content-class-name="desc-content">
        <el-descriptions-item :label="$t('pages.terminalCode')">{{ termInfo.id }}</el-descriptions-item>
        <el-descriptions-item :label="$t('pages.terminalName')">{{ termInfo.name }}</el-descriptions-item>
        <el-descriptions-item :label="$t('pages.computerName')">{{ termInfo.computerName }}</el-descriptions-item>
        <el-descriptions-item :label="$t('table.macAddr')">{{ termInfo.mainMac }}</el-descriptions-item>
        <el-descriptions-item :label="$t('table.terminalVersion')">{{ termInfo.version }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="div-contain">
      <label class="label-title">{{ $t('pages.vnc_Session2') }}</label>
      <el-divider/>
      <el-descriptions :column="1" content-class-name="desc-content">
        <el-descriptions-item :label="$t('table.createTime')">{{ startTime && startTime.toLocaleString() }}</el-descriptions-item>
        <el-descriptions-item :label="$t('pages.vnc_Session3')">{{ totalTime }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-button icon="el-icon-switch-button" size="small" @click="stop">{{ $t('pages.vnc_Session4') }}</el-button>
  </div>
</template>

<script>
import { getTermTypeDict } from '@/utils/dictionary';
import { getTermById } from '@/api/system/terminalManage/terminal';

export default {
  name: 'InfoPanel',
  props: {
    termId: { type: Number, required: true },
    isShow: { type: Boolean, default: false }  // 是否处于显示状态
  },
  data() {
    return {
      startTime: undefined,         // 连接开始的时间
      totalTime: undefined,         // 连接时长
      totalTimeTimer: null,         // 更新连接时长的定时器
      termInfo: {},                 // 终端信息
      termIconOpts: {}              // 终端图标
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        // 获取终端信息，会话信息
        this.getTermInfo()
        this.setUpdateInterval()
      } else {
        // 清除定时器
        clearInterval(this.totalTimeTimer)
      }
    }
  },
  created() {
    // 获取终端图标
    getTermTypeDict().forEach(item => { this.termIconOpts[item.value] = item.icon })
  },
  methods: {
    // 获取终端信息
    getTermInfo() {
      getTermById(this.termId).then(resp => {
        this.termInfo = resp.data
      })
    },
    init() {
      this.termInfo = {}
      this.startTime = undefined
      this.totalTime = undefined
      clearInterval(this.totalTimeTimer)
    },
    // 开始会话
    start() {
      this.startTime = new Date()
      this.setUpdateInterval()
    },
    // 结束会话
    stop() {
      clearInterval(this.totalTimeTimer)
      this.$emit('stop')
    },
    // 设置更新时间的定时器
    setUpdateInterval() {
      clearInterval(this.totalTimeTimer)
      this.updateTotalTime()
      this.totalTimeTimer = setInterval(this.updateTotalTime, 1000);
    },
    // 更新连接时长
    updateTotalTime() {
      if (!this.startTime) return
      const millis = new Date().getTime() - this.startTime.getTime()
      const totalSecond = Number.parseInt(millis / 1000)
      const hour = Number.parseInt(totalSecond / 3600)
      const minute = Number.parseInt(totalSecond / 60) % 60
      const second = totalSecond % 60
      if (hour > 0) {
        this.totalTime = `${hour}小时${minute}分钟${second}秒`
      } else if (minute > 0) {
        this.totalTime = `${minute}分钟${second}秒`
      } else {
        this.totalTime = `${second}秒`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .info-panel {
    .label-title {
      font-size: 14px;
    }
    .div-contain {
      padding: 5px;
      margin-top:20px;
    }
    .el-divider--horizontal{
      margin: 3px 0;
    }
    >>>.el-descriptions-item__label {
      min-width: 65px;
    }
    >>>.el-descriptions-item{
      font-size: 12px;
    }
    >>>.el-button {
      width: 97%;
      margin: 3px;
      color: #fff;
      border-color: #ff3b30;
      background-color: #ff3b30;
    }
  }
</style>
