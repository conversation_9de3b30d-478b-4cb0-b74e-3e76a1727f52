<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :visible.sync="dialogVisible"
    width="600px"
  >
    <div slot="title" class="el-dialog__title">
      {{ $t('pages.remoteAssistance') }}
      <el-tooltip effect="dark" placement="bottom-start">
        <div slot="content">
          <i18n path="pages.vnc_Content9">
            <br slot="br"/>
          </i18n>
        </div>
        <i class="el-icon-info" />
      </el-tooltip>
    </div>
    <Form ref="dataForm" v-loading="!connectAble" label-position="right" label-width="100px" style="width: 550px;">
      <template v-if="saveBtnAble">
        <FormItem :label="$t('pages.remotePort')" label-width="150px" :error="error.port">
          <el-input v-model="config.port" :maxlength="5" clearable class="port" @blur="handlePortBlur" @clear="handlePortBlur"/>
          <el-tooltip effect="dark" placement="bottom-end">
            <div slot="content">
              <i18n path="pages.vnc_Content12">
                <br slot="br"/>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </FormItem>
        <FormItem label-width="90px">
          <el-checkbox v-model="config.extranetEnable" :true-label="1" :false-label="0">
            {{ $t('pages.enableExtranetMapping') }}
          </el-checkbox>
        </FormItem>
        <FormItem v-if="config.extranetEnable===1" :label="$t('pages.extranetRemotePort')" label-width="150px" :error="error.extranetPort">
          <el-input v-model="config.extranetPort" :maxlength="5" clearable class="port" @blur="handleExtranetPortBlur" @clear="handleExtranetPortBlur"/>
        </FormItem>
        <FormItem :label="$t('pages.connectionTimeout')" label-width="150px">
          <el-input-number v-model="config.timeout" :controls="false" :min="5" :max="60" :precision="0" style="width: calc(100% - 70px);"></el-input-number> {{ $t('pages.secondLower') }}
        </FormItem>
      </template>

      <template v-else>
        <el-divider content-position="left">{{ $t('route.terminalInfo') }}</el-divider>
        <el-descriptions :column="2" class="term-info" content-class-name="ellipsis">
          <el-descriptions-item :label="$t('pages.terminalCode')">{{ termInfo.termId }}</el-descriptions-item>
          <el-descriptions-item :label="$t('table.terminalVersion')">{{ termInfo.termVer }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.terminalName')">
            <span :title="termInfo.termName">{{ termInfo.termName }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('table.ip')">{{ termInfo.termIp }}</el-descriptions-item>
          <el-descriptions-item :label="$t('pages.computerName')">
            <span :title="termInfo.computerName">{{ termInfo.computerName }}</span>
          </el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">远程配置</el-divider>
        <FormItem :label="$t('pages.consoleIP')" :error="error.ip">
          <el-tooltip slot="tooltip" effect="dark" placement="bottom-start">
            <div slot="content">
              <i18n path="pages.vnc_Content20">
                <span slot="confPage" class="span-line" style="color: #098af5" @click="toGlobalConfig">{{ configName }}</span>
              </i18n>
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-select v-model="temp.ip" clearable @change="handleIpChange">
            <el-option v-for="item in consoleIps" :key="item.ip" :value="item.ip">
              <span>
                <i :class="item.iconClass" :title="item.title"/> {{ item.ip }}
              </span>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem :label="$t('pages.remotePort')" :error="error.port">
          <el-input v-model="vncPort" clearable style="width: 90px;" readonly="readonly"/>
          <i class="el-icon-setting" style="color: #098af5; cursor: pointer;" @click="configFunc"></i>
        </FormItem>
        <FormItem :label="$t('pages.pictureQuality')" style="margin-left: -10px;">
          <el-radio-group v-model="temp.quality" size="small">
            <el-radio :label="1">{{ $t('pages.imageQuality1') }}</el-radio>
            <el-radio :label="5">{{ $t('pages.imageQuality3') }}</el-radio>
            <el-radio :label="9">{{ $t('pages.imageQuality4') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.zoom')" style="margin-left: -10px;">
          <el-radio-group v-model="temp.zoomType" size="small">
            <el-radio label="remote">{{ $t('components.originalSize') }}</el-radio>
            <el-radio label="scale">{{ $t('components.selfAdaptation') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="temp.queryAble" v-permission="'501'">
          <el-checkbox v-model="temp.isQuery" :true-label="2" :false-label="1">{{ $t('pages.vnc_Content16') }}</el-checkbox>
        </FormItem>
        <FormItem v-if="noConsoleIps">
          <i18n path="pages.vnc_Content18" style="color: #2f78b4;">
            <span slot="confPage" class="span-line" style="color: black" @click="toGlobalConfig">{{ configName }}</span>
          </i18n>
        </FormItem>
      </template>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button v-show="saveBtnAble" :loading="submitting" type="primary" @click="handleSave()">{{ $t('button.save') }}</el-button>
      <el-button v-show="!saveBtnAble" :loading="submitting" type="primary" :disabled="!connectAble" @click="handleConnect()">{{ $t('pages.connect') }}</el-button>
      <el-button @click="handleCancel">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateVNC } from '@/api/assets/systemMaintenance/vnc'
import { isPort1 } from '@/utils/validate'
import Cookies from 'js-cookie';

export default {
  name: 'ConfigDlg',
  data() {
    return {
      saveBtnAble: false,
      connectAble: false,
      dialogVisible: false,
      submitting: false,
      vncPort: undefined,
      termInfo: {
        termId: undefined,
        termType: undefined,
        termVer: undefined,
        termName: undefined,
        termIp: undefined,
        computerName: undefined
      },
      temp: {
        ip: undefined,
        port: undefined,
        extranetPort: undefined,
        timeout: undefined,
        queryAble: false,
        quality: 5,
        zoomType: 'remote',
        isQuery: 1,
        browserIp: undefined
      },
      config: {
        port: undefined,
        extranetPort: undefined,
        extranetEnable: false,
        timeout: undefined
      },
      error: {
        ip: undefined,
        port: undefined,
        extranetPort: undefined
      },
      consoleIps: [],
      noConsoleIps: false
    }
  },
  computed: {
    configName() {
      return this.$t('route.globalConfig')
    }
  },
  created() {
    this.resetTemp()
  },
  beforeDestroy() {
  },
  methods: {
    show(data) {
      this.dialogVisible = true
      this.saveBtnAble = false
      this.connectAble = false
      this.statusMsg = ''
      const consoleIps = []
      Object.entries(data.consoleAddress).forEach(([key, value]) => {
        if (key.toLowerCase().indexOf('ip') >= 0 && !!value && consoleIps.indexOf(value) < 0) {
          const isV6 = key.toLowerCase().indexOf('ipv6') >= 0
          const isMapping = key.toLowerCase().indexOf('mapping') >= 0
          consoleIps.push({
            ip: value,
            isMapping: isMapping,
            title: this.$t(isV6 ? (isMapping ? 'pages.consoleExtranetIPv6' : 'pages.consoleIntranetIPv6') : (isMapping ? 'pages.consoleExtranetIPv4' : 'pages.consoleIntranetIP')),
            iconClass: isMapping ? 'el-icon-share' : 'el-icon-s-platform'
          })
        }
      })
      this.resetTemp()
      this.consoleIps = consoleIps
      this.noConsoleIps = consoleIps.length === 0
      this.copyObj(data, this.termInfo)
      this.copyObj(data, this.temp)

      const defaultVncIp = Cookies.get('vnc_ip')
      if (defaultVncIp) {
        this.temp.ip = defaultVncIp
      }
      this.connectAble = true
      this.setVncPort(this.temp)
    },
    setVncPort(data) {
      let isMapping = false
      for (let i = 0; i < this.consoleIps.length; i++) {
        const item = this.consoleIps[i]
        if (item.ip === data.ip) {
          isMapping = item.isMapping
          break
        }
      }
      // 根据选中的控制台IP是否映射的ip，决定是否使用映射端口
      this.vncPort = isMapping && data.extranetPort ? data.extranetPort : data.port
    },
    copyObj(src, target) {
      Object.entries(src).forEach(([key, value]) => {
        if (target.hasOwnProperty(key)) {
          target[key] = value
        }
      })
    },
    resetTemp() {
      this.temp.quality = 5
      this.temp.zoomType = 'remote'
      this.temp.isQuery = 1
      this.error.ip = undefined
      this.error.port = undefined
      this.error.extranetPort = undefined
    },
    configFunc() {
      this.handleIpChange()
      if (this.error.ip) {
        return
      }
      this.copyObj(this.temp, this.config)
      if (this.config.port !== this.config.extranetPort) {
        this.config.extranetEnable = 1
      }
      this.error.port = undefined
      this.saveBtnAble = true
    },
    handleSave() {
      this.submitting = true
      this.handlePortBlur()
      if (this.config.extranetEnable === 0) {
        this.config.extranetPort = this.config.port
      }
      this.handleExtranetPortBlur()
      if (this.error.port || this.error.extranetPort) {
        this.submitting = false
        return
      }
      const isPortChange = this.config.port !== this.temp.port
      this.copyObj(this.config, this.temp)
      if (isPortChange) {
        this.$confirmBox('修改远程端口，将断开目前正在远程的终端，确定继续修改？', this.$t('text.prompt')).then(() => {
          this.doUpdateVNC(this.temp)
        }).catch(() => {
          this.submitting = false
        })
      } else {
        this.doUpdateVNC(this.temp)
      }
    },
    doUpdateVNC(data) {
      updateVNC(data).then((resp) => {
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
        this.setVncPort(data)
        this.saveBtnAble = false
        this.submitting = false
      }).catch(() => {
        this.submitting = false
      })
    },
    handleCancel() {
      this.resetTemp()
      if (this.saveBtnAble) {
        this.saveBtnAble = false
      } else {
        this.dialogVisible = false
      }
    },
    handleConnect() {
      const isDoing = this.submitting
      this.submitting = true
      this.handleIpChange()
      if (this.error.ip) {
        this.submitting = false
        return
      }
      if (!isDoing) {
        const param = Object.assign({}, this.temp, this.termInfo, { port: this.vncPort })
        this.$emit('connect', param)
        setTimeout(() => {
          this.dialogVisible = false
          this.submitting = false
        }, 500)
      }
    },
    status(text) {
      this.statusMsg = text
      console.log(this.statusMsg)
    },
    handleIpChange() {
      if (this.temp.ip) {
        this.error.ip = undefined
      } else {
        this.error.ip = this.$t('pages.validateSelectIP')
      }
      this.setVncPort(this.temp)
    },
    handlePortBlur() {
      if (isPort1(this.config.port)) {
        this.error.port = undefined
      } else {
        this.error.port = this.$t('pages.validateMsg_PortError')
      }
    },
    handleExtranetPortBlur() {
      if (isPort1(this.config.extranetPort)) {
        this.error.extranetPort = undefined
      } else {
        this.error.extranetPort = this.$t('pages.validateMsg_PortError')
      }
    },
    toGlobalConfig() {
      if (this.hasPermission('A68')) {
        //  tabName的值：resourceManagerTab，需要参照globalConfig中的tab-pane的name值
        this.$router.push('/system/configManage/globalConfig?tabName=consoleAddressTab')
      } else {
        this.$message({
          message: this.$t('pages.vnc_Content19'),
          type: 'error',
          duration: 2000
        })
      }
    }
  }

}
</script>
<style lang="scss" scoped>
.port {
  width: calc(100% - 70px);
  >>>.el-input__inner {
    text-align: center;
  }
}
.span-line:hover {
  text-decoration: underline;
  cursor: pointer;
}
.term-info {
  padding: 0 30px;
  >>>.el-descriptions-item__label {
    min-width: 65px;
  }
}
</style>
