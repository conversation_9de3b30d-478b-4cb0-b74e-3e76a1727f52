<template>
  <div class="clipboard-panel">
    <el-input v-model="clipboardText" type="textarea" rows="20" maxlength="5000" show-word-limit/>
    <el-button icon="el-icon-upload2" size="small" class="send-button" @click="sendText">{{ $t('pages.vnc_Cliboard2') }}</el-button>
    <el-button icon="el-icon-download" size="small" class="receive-button" @click="copyToClipboard">{{ $t('pages.vnc_Cliboard3') }}</el-button>
  </div>
</template>

<script>
import { isEmpty } from '@/utils'
import { getTermClipboard, setTermClipboard } from '@/api/assets/systemMaintenance/explorer'
export default {
  name: 'Clipboard',
  props: {
    termId: { type: Number, required: true },
    isShow: { type: Boolean, default: false }  // 是否处于显示状态
  },
  data() {
    return {
      clipboardText: ''
    }
  },
  methods: {
    init() {
      this.clipboardText = ''
    },
    copy(data) {
      const url = data;
      const oInput = document.createElement('textarea');
      oInput.value = url;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;
      document.execCommand('Copy'); // 执行浏览器复制命令
      this.$message({
        message: this.$t('pages.copy_success'),
        type: 'success'
      });
      oInput.remove()
    },
    sendText() {
      if (isEmpty(this.clipboardText)) {
        return
      }
      setTermClipboard({ termId: this.termId, action: 2, text: this.clipboardText }).then(resp => {
        if (resp.data === 0) {
          this.clipboardText = ''
        } else {
          this.$notify({
            title: this.$t('text.fail'),
            message: this.$t('text.settingFail'),
            type: 'fail',
            duration: 2000
          })
        }
      })
    },
    copyToClipboard() {
      // 将输入框的内容，添加到本地剪切板
      getTermClipboard({ termId: this.termId, action: 1 }).then(resp => {
        if (resp.data && resp.data.text) {
          this.clipboardText = resp.data.text
          this.copy(this.clipboardText)
        } else {
          this.$notify({
            title: this.$t('text.prompt'),
            message: '剪切板内容为空',
            duration: 2000
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .clipboard-panel {
    >>>.el-textarea {
      width: calc(100% - 5px);
      margin: 3px;
      background-color: #f1f1f1;
    }
    >>>.el-button {
      width: calc(100% - 5px);
      margin: 3px;
      color: #fff;
    }
    >>>.send-button {
      background-color: #46c017;
      border-color: #46c017;
    }
    >>>.receive-button {
      background-color: #439edf;
      border-color: #439edf;
    }
  }
</style>
