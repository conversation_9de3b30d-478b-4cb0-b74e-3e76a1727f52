<template>
  <div class="file-transfer">
    <div>
      <el-button-group class="file-transfer-operate">
        <el-button size="mini" icon="el-icon-refresh" class="operate-item" title="刷新" @click="handleRefresh"></el-button>
        <el-upload
          ref="upload"
          v-permission="'797'"
          name="uploadFile"
          action="aaaaaa"
          :disabled="submitting"
          :show-file-list="false"
          :before-upload="beforeUpload"
          class="operate-item upload"
        >
          <el-button size="mini" icon="el-icon-upload2" :loading="submitting" :title="$t('pages.uploadFile1')" :disabled="disabledUpload"></el-button>
        </el-upload>
        <explorer-downloader
          v-permission="'798'"
          append-to-body
          :terminal-id="termId"
          :dir="curDir"
          :selection="curSelection"
          download-btn-label=""
          :download-btn-title="curBtnTitle"
          :show-zip-mode="false"
          :biz-type="2"
          class="operate-item"
        />
      </el-button-group>
    </div>
    <file-manager out-class="file-manager" :show-refresh="false" :file-path="curFilePath" :path-str-max-length="25" @clickPath="curFilePathClick">
      <grid-table
        ref="tableList"
        v-loading="tableLoading"
        :col-model="colModel"
        :multi-select="true"
        :show-pager="false"
        :row-datas="rowData"
        :selectable="rowSelectAbleFunc"
        :default-sort="defaultSort"
        @selectionChangeEnd="handleSelectionChange"
        @row-dblclick="rowDblclickFunc"
      />
    </file-manager>
  </div>
</template>

<script>
import FileManager from '@/components/FileManager'
import ExplorerDownloader from '@/views/assets/systemMaintenance/explorer/downloader'
import { getFileIcon } from '@/icons/extension'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'
import { listChildFile } from '@/api/assets/systemMaintenance/explorer'
import { upload } from '@/api/assets/systemMaintenance/explorer'
import { isShowFileDownload } from '@/api/grantAuth'
import { formatFileSize } from '@/utils'

export default {
  name: 'FileTransfer',
  components: { ExplorerDownloader, FileManager },
  props: {
    termId: { type: Number, required: true },
    isShow: { type: Boolean, default: false }  // 是否处于显示状态
  },
  data() {
    return {
      lastTermId: 0,
      percentage: 0,
      tableLoading: false,
      submitting: false,
      folderName: undefined,
      uploadLimitSize: 2048 * 1024 * 1024,
      fileDownAble: false,
      continueFlag: 2,
      curFilePath: [''],
      rowData: [],
      selection: [],
      defaultSort: { prop: 'oriData.type', order: 'desc' },
      colModel: [
        { prop: 'label', label: 'name', width: '200', sort: false, iconFormatter: this.iconClassFormatter }
      ]
    }
  },
  computed: {
    curDir() {
      const dir = this.curFilePath.filter(item => {
        const fi = !!item
        return fi
      }).join('/')
      return dir
    },
    curSelection() {
      return this.fileDownAble ? this.selection : []
    },
    curBtnTitle() {
      return this.fileDownAble ? this.$t('pages.downloadFile') + '(' + this.$t('pages.downloadLongFileName') + ')'
        : this.$t('pages.exploreDownloadDisable')
    },
    disabledUpload() {
      return !this.curFilePath || this.curFilePath.length < 2
    }
  },
  watch: {
    isShow(val) {
      if (val && this.termId !== this.lastTermId) {
        this.handleRefresh()
      }
    },
    termId(val) {
      val !== this.lastTermId && this.handleRefresh()
    }
  },
  created() {
    isShowFileDownload().then(res => {
      this.fileDownAble = res.data
    })
  },
  methods: {
    init() {
      this.lastTermId = 0
      this.tableLoading = false
      this.submitting = false
      this.folderName = undefined
      this.continueFlag = 2
      this.curFilePath = ['']
      this.rowData = []
      this.selection = []
      this.changeFileList(this.curFilePath)
    },
    // 面包屑点击方法
    curFilePathClick(index, filePath) {
      this.changeFileList(filePath)
    },
    rowSelectAbleFunc(rowData) {
      return rowData.oriData.type == 2
    },
    handleSelectionChange(rowDatas) {
      this.selection = rowDatas.map(item => item.oriData)
    },
    rowDblclickFunc(rowData, column, event) {
      if (rowData.oriData.type == 2) return
      this.curFilePath.push(rowData.label)
      this.rowData = []
      const fileType = this.calcLoadFileType(!rowData.oriData)
      const parentPath = this.getParentPath()
      this.sendToUser(this.termId, this.continueFlag, fileType, parentPath)
    },
    // 获取终端的资源数据
    sendToUser(terminalId, continueFlag, fileType, parentPath) {
      this.tableLoading = true
      listChildFile({
        terminalId, continueFlag, fileType, parentPath
      }).then(response => {
        this.$socket.subscribeToAjax(response, terminalId + '/listChildFile', (respond, handle) => {
          handle.close()
          this.tableLoading = false
          if (respond.data && respond.data.length > 0) {
            const timestamp = new Date().getTime()
            respond.data.forEach(node => {
              node.suffix = node.label.indexOf('.') > -1 ? node.label.split('.').pop() : ''
              node.id = `${terminalId}-${node.id}-${timestamp}`
              // 将数据添加到table
              this.rowData.push(node)
            })
            // 排序
            this.rowData.sort((a, b) => a.oriData.type - b.oriData.type)
            // oriData.endFlag === 1 说明数据未完全请求回来，需要再次请求数据
            if (respond.data[0].oriData.endFlag === 1) {
              this.$nextTick(() => {
                this.sendToUser(terminalId, 1, fileType, parentPath)
              })
            }
          }
        })
      }).catch(e => {
        if (this.tableLoading) {
          this.tableLoading = false
        }
      })
    },
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('termLog', termId).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    handleRefresh() {
      this.lastTermId = this.termId
      this.changeFileList(this.curFilePath)
    },
    changeFileList(filePath) {
      this.curFilePath = filePath
      this.rowData = []
      const fileType = this.calcLoadFileType(this.curFilePath.length < 2)
      const parentPath = this.getParentPath()
      this.sendToUser(this.termId, this.continueFlag, fileType, parentPath)
    },
    beforeUpload(file) {
      const isLtSize = file.size < this.uploadLimitSize
      if (!isLtSize) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('components.overLimit_total_msg', { size: formatFileSize(file.size), maxSize: formatFileSize(this.uploadLimitSize) }),
          type: 'warning',
          duration: 3000
        })
        return false
      }
      if (this.rowData && this.rowData.length > 0) {
        for (let i = 0; i < this.rowData.length; i++) {
          const row = this.rowData[i]
          if (row.label === file.name) {
            this.$confirmBox(this.$t('pages.targetExistFile', { fileName: file.name }), this.$t('text.prompt'), {
              confirmButtonText: this.$t('pages.replaceTargetFile'),
              cancelButtonText: this.$t('pages.ignoreFile')
            }).then(() => {
              this.submitUpload(file)
            }).catch(() => {})
            return false
          }
        }
      }
      this.submitUpload(file)
      return false // 屏蔽了action的默认上传
    },
    submitUpload(file) {
      // const fileName = file.name
      // const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      this.submitting = true
      this.percentage = 0
      // const onUploadProgress = (progressEvent) => {
      //   const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
      //   this.percentage = Number.parseInt(percent)
      // }
      // this.source = this.connectionSource()
      // const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数

      const destDir = this.curFilePath.filter(item => item !== '').join('/')

      const fd = new FormData()
      fd.append('file', file)// 传文件
      fd.append('termId', this.termId)// 终端ID
      fd.append('type', 1)// 业务类型，1-远程协助
      fd.append('destDir', destDir)// 模板目录
      upload(fd).then(res => {
        if (res.data && res.data.msgSn) {
          this.awaitUploadResult(res.data.msgSn)
        } else {
          this.resetUploadComponent()
        }
      }).catch(res => {
        this.resetUploadComponent()
      })
    },
    awaitUploadResult(msgSn) {
      this.$socket.subscribeToUser(msgSn, '/termDownloadFileState', (resp, handle) => {
        if (resp.data && resp.data > 0) {
          const stateCode = resp.data
          let errorMsg = this.$t('pages.vnc_Content21')
          if (stateCode === 2) {
            errorMsg = this.$t('pages.vnc_Content22')
          } else if (stateCode === 3) {
            errorMsg = this.$t('pages.vnc_Content23')
          }
          // 说明是未知错误
          this.$notify({
            title: this.$t('text.error'),
            message: errorMsg,
            type: 'error',
            duration: 3000
          })
          this.resetUploadComponent()
          return
        }
        this.changeFileList(this.curFilePath)
        this.resetUploadComponent()
        handle.close();
      }, false)
    },
    resetUploadComponent() {  // 重置上传组件状态
      this.submitting = false
      this.percentage = 0
    },
    // 获取当前的目录路径
    getParentPath: function() {
      let path = ''
      this.curFilePath.forEach(t => {
        if (t) {
          path += path.length > 0 ? '\\' : ''
          path += t
        }
      })
      return path
    },
    calcLoadFileType(isDisk) {
      // 0：枚举盘符
      if (isDisk) {
        return 0
      }
      // 文件类型，按位与。1：枚举文件夹（不包含子目录）2：枚举加密文件（不包含子目录）4：枚举非加密文件
      const fileType = 1 | 2 | 4
      return fileType
    },
    iconClassFormatter: function(row) {
      const icons = []
      // 0：硬盘 1：文件夹 2. 文件
      const type = row.oriData.type
      let title
      let iconName
      if (type == 2) {
        title = row.label.split('.').pop().toLowerCase()
        iconName = getFileIcon(title)
      } else {
        title = { 0: '磁盘', 1: '文件夹' }[type]
        iconName = { 0: 'disk1', 1: 'dir1' }[type]
      }
      icons.push({ class: iconName, title: title })
      return icons
    }
  }
}
</script>

<style lang="scss" scoped>
  .file-transfer {
    height: 100%;
    .file-transfer-title {
      line-height: 30px;
      padding: 5px 10px;
    }
    .file-transfer-operate {
      width: 100%;
      display: flex;
      >>>.el-button {
        padding: 0;
        font-size: 16px;
        color: #0c4cdb;
        background-color: #ffffff;
        border-color: #089ba2;
        border-radius: 3px;
        &.is-disabled {
          color: #888;
          background-color: #acacac;
        }

      }
      >>>i {
        font-weight: bold;
      }
      .operate-item {
        flex: 1;
        >>>.el-upload {
          width: 100%;
          margin-left: 1px;
        }
        >>>.el-button {
          width: 100%;
        }
      }
    }
    .file-manager {
      height: calc(100% - 35px);
    }
    >>>.download-executor{
      margin-left: 1px;
    }
    >>>.toolbar{
      margin-bottom: 0;
    }
    >>>.el-breadcrumb{
      width: 100%;
      margin: 4px 0 0;
      background-color: #f5f5f5;
      border-color: #089ba2;
      border-radius: 3px 3px 0px 0px;
    }
  }
</style>
