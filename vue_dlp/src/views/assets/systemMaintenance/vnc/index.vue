<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" :os-type-filter="7" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange" />
    </div>
    <div class="table-container">
      <div class="toolbar">
        <el-button v-show="isShowTreeSwitch" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button v-show="isShowControlSwitch" type="primary" size="mini" @click="toggleControlPanel">
          <svg-icon icon-class="application" />
        </el-button>
        <el-button v-if="!connecting && !showLoading" type="primary" icon="el-icon-plus" size="mini" :loading="btnLoading" :disabled="!addBtnAble" @click="handleOpen">
          {{ $t('pages.remoteAssistance') }}
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.vnc_Content1') }}<br/>{{ $t('pages.vnc_Content2') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
        <el-button v-if="connecting || showLoading" :title="$t('pages.disconnect')" type="primary" icon="el-icon-video-pause" size="mini" @click="handleDisconnect"/>

        <el-dropdown v-if="connecting" style="padding-left: 10px;" @command="handleQuality">
          <el-button size="mini">
            {{ $t('pages.pictureQuality') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="1" :style="qualityStyle.L1">{{ $t('pages.imageQuality1') }}</el-dropdown-item>
            <el-dropdown-item command="5" :style="qualityStyle.L5">{{ $t('pages.imageQuality3') }}</el-dropdown-item>
            <el-dropdown-item command="9" :style="qualityStyle.L9">{{ $t('pages.imageQuality4') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown v-if="connecting" style="padding-left: 10px;" @command="handleZoom">
          <el-button size="mini">
            {{ $t('pages.zoom') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="remote" :style="zoomStyle.remote">{{ $t('components.originalSize') }}</el-dropdown-item>
            <el-dropdown-item command="scale" :style="zoomStyle.scale">{{ $t('components.selfAdaptation') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown v-if="connecting" style="padding-left: 10px;" @command="handleKey">
          <el-button size="mini">
            {{ $t('pages.shortcutKey') }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-if="'2' == termType" command="CtrlAltDel">{{ $t('text.sendInfo', { info : 'fn+command'}) }}</el-dropdown-item>
            <el-dropdown-item v-else command="CtrlAltDel">{{ $t('text.sendInfo', { info : 'Ctrl+Alt+Del'}) }}</el-dropdown-item>

            <el-dropdown-item command="Tab">{{ $t('text.sendKeyInfo', { info : 'Tab'}) }}</el-dropdown-item>
            <el-dropdown-item command="Esc">{{ $t('text.sendKeyInfo', { info : 'Esc'}) }}</el-dropdown-item>

            <el-dropdown-item v-if="'2' == termType" command="Ctrl" :style="keyStyle.ctrl">{{ $t('text.pressTheKeyInfo', { info: 'control'}) }}</el-dropdown-item>
            <el-dropdown-item v-else command="Ctrl" :style="keyStyle.ctrl">{{ $t('text.pressTheKeyInfo', { info: 'Ctrl'}) }}</el-dropdown-item>

            <el-dropdown-item v-if="'2' == termType" command="Alt" :style="keyStyle.alt">{{ $t('text.pressTheKeyInfo', { info: 'command'}) }}</el-dropdown-item>
            <el-dropdown-item v-else command="Alt" :style="keyStyle.alt">{{ $t('text.pressTheKeyInfo', { info: 'Alt'}) }}</el-dropdown-item>

            <el-dropdown-item v-if="'2' !== termType" command="Window" :style="keyStyle.wind">{{ $t('text.pressTheKeyInfo', { info: 'Window'}) }}</el-dropdown-item>          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="tableBox">
        <control-panel v-show="controlVisible" ref="controlPanel" :class="{box: true, 'left-box': controlVisible}" @stop="handleDisconnect"/>
        <div id="screen" v-loading="showLoading" :element-loading-text="loadingText" class="box right-box"/>
      </div>
    </div>
    <config-dlg ref="configDlg" @connect="handleConnect"/>
  </div>
</template>

<script>
import ControlPanel from './controlPanel'
import ConfigDlg from './configDlg'
import Cookies from 'js-cookie'
import RFB from '@/novnc/core/rfb'
import { initLogging } from '@/novnc/core/util/logging.js'
import KeyTable from '@/novnc/core/input/keysym.js'
import { getByTermId, requestRemoteAssist, updateVNC } from '@/api/assets/systemMaintenance/vnc'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'Vnc',
  components: { ControlPanel, ConfigDlg },
  data() {
    return {
      showTree: true,
      addBtnAble: false,
      saveBtnAble: false,
      connecting: false,
      isShowTreeSwitch: true,
      isShowControlSwitch: false,
      statusMsg: '',
      curNode: {},
      rfb: null,
      desktopName: '',
      controlVisible: false,
      loadingText: this.$t('pages.loadingDesperately'),
      showLoading: false,
      btnLoading: false,
      zoomType: 'remote',
      zoomStyle: {
        remote: {},
        scale: {}
      },
      keyStyle: {
        ctrl: {},
        alt: {},
        wind: {}
      },
      selectedQuality: 5,
      qualityStyle: {
        L1: {},
        L5: {},
        L9: {}
      },
      temp: {
        ip: undefined,
        port: undefined,
        timeout: undefined,
        queryAble: false,
        termId: undefined,
        browserIp: undefined
      },
      subscribeKey: 123,
      termType: 0,
      serverPort: undefined,
      contextPath: undefined,
      isQuery: 1
    }
  },
  computed: {
    configName() {
      return this.$t('route.globalConfig')
    }
  },
  created() {
    this.subscribeKey = Math.floor(Math.random() * new Date().getTime())
    this.$socket.subscribeToUser(this.subscribeKey, '/vnc/msg', (resp, handle) => {
      this.$message({ message: this.$t(resp.data) + '', type: 'info' })
    })
    this.resetTemp()
  },
  beforeDestroy() {
    this.handleDisconnect()
  },
  methods: {
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.curNode = checkedNode
        this.termType = checkedNode.dataType
      } else {
        this.curNode = {}
      }
      this.addBtnAble = false
      if (checkedNode.id.indexOf('G') < 0 && checkedNode.online && ['0', '1', '2'].indexOf(checkedNode.dataType) > -1) {
        enableCtrlTerm('vnc', this.curNode.dataId).then(resp => {
          if (resp.data < 1) {
            this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
            return
          }
          this.addBtnAble = true
        })
      }
      this.handleDisconnect()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    toggleControlPanel() {
      this.controlVisible = !this.controlVisible
    },
    hideTreeMenu() {
      this.showTree = false
    },
    handleOpen() {
      this.statusMsg = ''
      this.btnLoading = true
      getByTermId(this.curNode.dataId).then(async(resp) => {
        this.serverPort = resp.data.serverPort
        this.contextPath = resp.data.contextPath
        this.$refs.configDlg.show(resp.data)
        this.btnLoading = false
      }).catch(t => {
        this.btnLoading = false
      })
    },
    resetTemp() {
      this.zoomType = 'remote'
      this.handleCtrlKey(true)
      this.handleAltKey(true)
      this.handleWinKey(true)
    },
    handleConnect(data) {
      this.temp = data
      if (!this.temp.ip) {
        return
      }
      this.isQuery = data.isQuery
      this.showLoading = true
      this.isShowTreeSwitch = false
      this.selectedQuality = data.quality
      this.zoomType = data.zoomType
      this.loadingText = this.$t('pages.vnc_Content17')
      this.initRFB()
      this.resetTemp()
      this.hideTreeMenu()
      requestRemoteAssist({ terminalId: this.curNode.dataId }).then(resp => {
        Cookies.set('vnc_ip', this.temp.ip)
      })
    },
    handleDisconnect() {
      if (this.rfb) {
        this.rfb.disconnect()
        this.status('disconnecting')
      } else {
        this.showTree = true
        this.isShowTreeSwitch = true
      }
    },
    handleZoom(mode) {
      this.zoomType = mode
      for (const key in this.zoomStyle) {
        this.zoomStyle[key] = {}
      }
      this.zoomStyle[mode] = { color: '#68a8d0' }
      if (this.rfb) {
        this.rfb.scaleViewport = mode === 'scale'
        this.rfb.resizeSession = mode === 'remote'
      }
    },
    handleKey(key) {
      if ('CtrlAltDel' === key) {
        this.rfb.sendCtrlAltDel()
      } else if ('Tab' === key) {
        this.rfb.sendKey(KeyTable.XK_Tab, 'Tab')
      } else if ('Esc' === key) {
        this.rfb.sendKey(KeyTable.XK_Escape, 'Escape')
      } else if ('Ctrl' === key) {
        this.handleCtrlKey()
      } else if ('Alt' === key) {
        this.handleAltKey()
      } else if ('Window' === key) {
        this.handleWinKey()
      }
    },
    handleCtrlKey(isReset) {
      if (isReset || this.keyStyle.ctrl.hasOwnProperty('color')) {
        this.keyStyle.ctrl = {}
        this.rfb && this.rfb.sendKey(KeyTable.XK_Control_L, 'ControlLeft', false)
      } else {
        this.keyStyle.ctrl = { color: '#68a8d0' }
        this.rfb && this.rfb.sendKey(KeyTable.XK_Control_L, 'ControlLeft', true)
      }
    },
    handleAltKey(isReset) {
      if (isReset || this.keyStyle.alt.hasOwnProperty('color')) {
        this.keyStyle.alt = {}
        this.rfb && this.rfb.sendKey(KeyTable.XK_Alt_L, 'AltLeft', false)
      } else {
        this.keyStyle.alt = { color: '#68a8d0' }
        this.rfb && this.rfb.sendKey(KeyTable.XK_Alt_L, 'AltLeft', true)
      }
    },
    handleWinKey(isReset) {
      if (isReset || this.keyStyle.wind.hasOwnProperty('color')) {
        this.keyStyle.wind = {}
        this.rfb && this.rfb.sendKey(KeyTable.XK_Super_L, 'MetaLeft', false)
      } else {
        this.keyStyle.wind = { color: '#68a8d0' }
        this.rfb && this.rfb.sendKey(KeyTable.XK_Super_L, 'MetaLeft', true)
      }
    },
    handleQuality(level, dom) {
      this.selectedQuality = parseInt(level)
      for (const key in this.qualityStyle) {
        this.qualityStyle[key] = {}
      }
      this.qualityStyle['L' + level] = { color: '#68a8d0' }
      if (this.rfb) {
        console.log('set qualityLevel:' + this.selectedQuality)
        this.rfb.qualityLevel = this.selectedQuality
        if (dom) {
          // 如果是切换画质操作，则提示，如果是初始化时设置画质则无需提示
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('pages.vnc_Content3'),
            type: 'success',
            duration: 2000
          })
        }
      }
    },
    getWebsocketUrl() {
      // By default, use the host and port of server that served this file
      const host = this.readQueryVariable('host', window.location.hostname)
      let hostPort = window.location.port
      if (process.env.NODE_ENV === 'development') {
        // 开发模式下，前后端端口不一致，因此从后端获取赋值
        hostPort = this.serverPort
      }
      const port = this.readQueryVariable('port', hostPort)
      const sysUserAccount = this.$store.getters.account

      const defaultPath = `${this.contextPath}/websockify?browserIp=${this.temp.browserIp}&account=${sysUserAccount}&termId=${this.temp.termId}&que=${this.isQuery}&ip=${this.temp.ip}&port=${this.temp.port}&time=${this.temp.timeout}&sk=${this.subscribeKey}`

      const path = this.readQueryVariable('path', defaultPath)

      let url
      if (window.location.protocol === 'https:') {
        url = 'wss://'
      } else {
        url = 'ws://'
      }
      url += host
      if (port) {
        url += ':' + port
      }
      if (!path.startsWith('/')) {
        url += '/'
      }
      url += path
      return url
    },
    initRFB() {
      // Read parameters specified in the URL query string
      const password = this.readQueryVariable('password', '')
      this.status('Connecting')

      initLogging('debug')

      const url = this.getWebsocketUrl()
      console.log(url)
      // Creating a new RFB object will start a new connection
      this.rfb = new RFB(document.getElementById('screen'), url,
        { credentials: { password: password }})

      // Add listeners to important events from the RFB module
      this.rfb.addEventListener('connect', this.connectedToServer)
      this.rfb.addEventListener('disconnect', this.disconnectedFromServer)
      this.rfb.addEventListener('credentialsrequired', this.credentialsAreRequired)
      this.rfb.addEventListener('desktopname', this.updateDesktopName)

      // Set parameters that can be changed on an active connection
      this.rfb.viewOnly = this.readQueryVariable('view_only', false)
      this.rfb.scaleViewport = this.readQueryVariable('scale', false)
      this.handleQuality(this.selectedQuality)
      this.handleZoom(this.zoomType)
    },
    status(text) {
      this.statusMsg = text
      console.log(this.statusMsg)
    },
    // 根据连接或断开连接，修改部分组件的状态
    changeStatus(connect) {
      this.connecting = connect
      this.showTree = !connect
      this.isShowTreeSwitch = !connect
      this.controlVisible = connect
      this.isShowControlSwitch = connect
      if (connect) {
        this.$refs.controlPanel && this.$refs.controlPanel.show(this.temp)
      } else {
        this.$refs.controlPanel && this.$refs.controlPanel.hide()
      }
    },
    // When this function is called we have
    // successfully connected to a server
    connectedToServer(e) {
      this.showLoading = false
      this.status('Connected to ' + this.desktopName)
      this.changeStatus(true)
      updateVNC(this.temp)
      window.setVncCursorInterval = setInterval(() => {
        // 解决部分情况下，远程时不显示鼠标的问题
        if (this.rfb._cursor._target && this.rfb._cursor._target.style.cursor === 'none') {
          this.rfb._cursor._target.style.cursor = 'auto'
        } else {
          clearInterval(window.setVncCursorInterval)
        }
      }, 500)
    },
    // This function is called when we are disconnected
    disconnectedFromServer(e) {
      this.showLoading = false
      this.changeStatus(false)
      // this.$message({ message: '远程连接已断开', type: 'info' })
      if (e.detail.clean) {
        this.status('Disconnected')
      } else {
        this.status('Something went wrong, connection is closed')
      }
    },
    // When this function is called, the server requires
    // credentials to authenticate
    credentialsAreRequired(e) {
      const password = prompt('Password Required:')
      this.rfb.sendCredentials({ password: password })
    },
    // When this function is called we have received
    // a desktop name from the server
    updateDesktopName(e) {
      this.desktopName = e.detail.name
    },
    // Since most operating systems will catch Ctrl+Alt+Del
    // before they get a chance to be intercepted by the browser,
    // we provide a way to emulate this key sequence.
    sendCtrlAltDel() {
      this.rfb.sendCtrlAltDel()
      return false
    },
    // This function extracts the value of one variable from the
    // query string. If the variable isn't defined in the URL
    // it returns the default value instead.
    readQueryVariable(name, defaultValue) {
      // A URL with a query parameter can look like this:
      // https://www.example.com?myqueryparam=myvalue
      //
      // Note that we use location.href instead of location.search
      // because Firefox < 53 has a bug w.r.t location.search
      const re = new RegExp('.*[?&]' + name + '=([^&#]*)')
      const match = document.location.href.match(re)
      if (match) {
        // We have to decode the URL since want the cleartext value
        return decodeURIComponent(match[1])
      }
      return defaultValue
    }
  }
}
</script>
<style lang="scss" scoped>
  .box {
    height: 100%;
    border: 1px solid #666;
  }
  .left-box {
    width: 270px;
    float: left
  }
  .right-box {
    margin: 0;
  }
  .left-box+.right-box {
    margin-left: 270px;
  }
</style>
