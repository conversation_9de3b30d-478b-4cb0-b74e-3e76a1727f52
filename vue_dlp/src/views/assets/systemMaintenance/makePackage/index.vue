<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-upload" size="mini" style="text-transform: capitalize" @click="handleCreatePackage">
          {{ $t('pages.uploadInstallPackage') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!packageDeleteable" @click="handleDeletePack">
          {{ $t('button.delete') }}
        </el-button>
        <el-button icon="el-icon-circle-plus-outline" size="small" @click="handleCreate">
          {{ $t('pages.buildUpgradeTask') }}
        </el-button>
        <download-tool :show-type="showType"></download-tool>
        <span style="padding-left: 10px">
          {{ $t('pages.makePackage_text1') }}
        </span>
        <div class="searchCon">
          <el-input
            v-model="query.searchInfo"
            v-trim
            clearable
            :placeholder="$t('pages.pleasePackageName')"
            style="width: 200px;"
            @keyup.enter.native="handleFilter"
          ></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="packageList"
        :show-pager="true"
        :multi-select="true"
        :col-model="packageModel"
        :row-data-api="loadPackage"
        @selectionChangeEnd="packageSelectChange"
      />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.makeInstallPackage')"
      :visible.sync="makeVisible"
      width="650px"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="190px">
        <FormItem :label="$t('pages.productionType')" label-width="110px" :tooltip-content="$t('pages.terminalOldInstallPackageOnlyCreateSilent')">
          <el-radio-group v-model="temp.type" style="padding-left: 20px;">
            <el-radio :label="1">{{ $t('pages.silentInstallationPackage') }}</el-radio>
            <el-radio :label="2" :disabled="!oldType">
              {{ $t('pages.commonInstallationPackage') }}
              <!-- <el-tooltip effect="dark" placement="bottom-end">
                <div slot="content">
                  默认隐藏终端类型选择界面及用户信息配置界面
                </div>
                <i class="el-icon-info" />
              </el-tooltip> -->
            </el-radio>
          </el-radio-group>
        </FormItem>
        <el-card v-if="temp.type == 1" class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.addServerAddressAndPort', { server: $t('route.daqServer') }) }}
              <svg-icon v-show="oldType" style="color: #409EFF" icon-class="add" class-name="add-time" @click="addSlientServer"/>
            </span>
          </div>
          <div v-if="silentServerList.length > 0">
            <el-row v-for="(item,index) in silentServerList" :key="index">
              <el-col :span="12" >
                <FormItem :label="$t('pages.serverAddress')" label-width="120px">
                  <el-input v-model="item.serverIp" :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.address') })" maxlength=""></el-input>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('pages.serverPort')" label-width="120px">
                  <el-input-number v-model="item.serverPort" :controls="false" :step="1" step-strictly :min="1" :max="65535" :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.port') })" style="width: calc(100% - 20px)"></el-input-number>
                  <i v-show="silentServerList.length > 1" class="el-icon-remove-outline" @click="deleteSilentServer(index)"></i>
                </FormItem>
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card v-if="temp.type == 1" class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.makePackage_text4') }}</span>
          </div>
          <FormItem :label="$t('pages.makePackage_text5')" prop="userName">
            <el-input v-model="temp.userName" style="width: calc(100% - 20px);" maxlength="30"/>
            <el-tooltip effect="dark" placement="bottom-end">
              <div slot="content">
                {{ $t('pages.makePackage_text6') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </FormItem>
          <FormItem :label="$t('pages.pwdInfo')" prop="userPassword">
            <el-input v-model="temp.userPassword" style="width: calc(100% - 20px);" maxlength=""></el-input>
          </FormItem>
        </el-card>
        <el-card v-if="temp.type == 2" class="box-card" :body-style="{'padding': ' 5px 10px'}">
          <div slot="header">
            <span>{{ $t('pages.addServerAddressAndPort', { server: $t('route.daqServer') }) }}
              <!-- <svg-icon v-show="temp.forceSetServerAdd == 1" icon-class="add" class-name="add-time" @click="addGeneralServer"/> -->
            </span>
          </div>
          <el-checkbox v-model="temp.forceSetServerAdd" :true-label="1" :false-label="0" style="padding-left: 20px">
            {{ $t('pages.builtInServerAddressInfo') }}
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content">
                {{ $t('pages.builtInServerAddressInfoTips', { server: $t('route.daqServer'), port: 20181 }) }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
          <div v-if="generalServerList.length > 0">
            <el-row v-for="(item,index) in generalServerList" :key="index">
              <el-col :span="12" >
                <FormItem :label="$t('pages.serverAddress')" label-width="120px">
                  <el-input v-model="item.serverIp" :disabled="temp.forceSetServerAdd == 0" :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.address') })" maxlength=""></el-input>
                </FormItem>
              </el-col>
              <el-col :span="12">
                <FormItem :label="$t('pages.serverPort')" label-width="120px">
                  <el-input-number v-model="item.serverPort" :controls="false" :step="1" step-strictly :min="1" :max="65535" :disabled="temp.forceSetServerAdd == 0" :placeholder="$t('text.pleaseEnterInfo', { info: $t('pages.port') })"></el-input-number>
                  <i v-show="generalServerList.length > 1" class="el-icon-remove-outline" @click="deleteGeneralServer(index)"></i>
                </FormItem>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <span v-if="submitting" style="color: #666; margin-right: 65px;">{{ $t('pages.makingPackage') }}</span>
        <el-button :loading="submitting" type="primary" @click="createData()">{{ $t('pages.making') }}</el-button>
        <el-button @click="makeVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.uploadInstallPackage')"
      :visible.sync="uploadVisible"
      width="400px"
    >
      <Form ref="dataForm" :rules="rules" :model="packageTemp" label-position="right" label-width="90px" style="width: 340px;">
        <el-row>
          <el-col :span="21">
            <FormItem :label="$t('pages.exeName')" prop="fileName">
              <el-input v-model="packageTemp.fileName" readonly/>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-upload
              ref="upload"
              name="upload"
              action="1111"
              accept=".exe"
              :on-change="fileChange"
              :show-file-list="false"
              :file-list="fileList"
              :disabled="fileSubmitting"
              :auto-upload="false"
            >
              <el-button type="primary" icon="el-icon-upload" :loading="fileSubmitting" style="margin: 0 0 0 1px;"></el-button>
            </el-upload>
          </el-col>
        </el-row>
        <el-row v-if="fileSubmitting">
          <el-col :span="22">
            <el-progress type="line" :percentage="percentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" icon="el-icon-switch-button" :title="$t('pages.cancelUpload')" @click="cancel"></el-button>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveFile()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="uploadVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <edit-dlg ref="editDlg" :version-opts="versionOpts"/>
  </div>
</template>

<script>
import {
  uploadSoft, getPackageFilterPage, deletePack, makeInstallationPackage, isNewInstallPackage, fetchDistinctVersions
} from '@/api/assets/systemMaintenance/terminalUpgrade'
import DownloadTool from '@/views/dataEncryption/encryption/fileOutgoing/downloadTool'
import axios from 'axios'
import EditDlg from '@/views/assets/systemMaintenance/terminalUpgrade/upgradeStrategy/editDlg'

export default {
  name: 'MakeInstallPackage',
  components: { EditDlg, DownloadTool },
  data() {
    return {
      packageModel: [
        { prop: 'fileName', label: 'installPackageName', width: '100', sort: 'custom' },
        { prop: 'version', label: 'installPackageVersion', width: '120', sort: 'custom' },
        { prop: 'createTime', label: 'time', width: '120', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'making', click: this.handleMakePackage, disabledFormatter: this.makePackageFormatter }
          ]
        }
      ],
      alternateServerRowDatas: [],
      uploadVisible: false,
      makeVisible: false,
      packageDeleteable: false,
      dialogFormVisible: false,
      submitting: false,
      fileSubmitting: false,
      percentage: 0,
      source: null,
      fileList: [],
      query: { // 查询条件
        page: 1,
        searchInfo: ''
      },
      temp: {
      },
      defaultTemp: {
        fileName: '',
        packageId: null,
        type: 1,
        forceSetServerAdd: 1,
        serverInfo: [],
        userName: '',
        userPassword: ''
      },
      showType: 2,
      packageTemp: {}, // 表单字段
      silentServerList: [{
        serverIp: '127.0.0.1',
        serverPort: 20181
      }],
      generalServerList: [{
        serverIp: '127.0.0.1',
        serverPort: 20181
      }],
      oldType: true,
      defaultPackageTemp: {
        id: undefined,
        fileName: ''
      },
      rules: {
        fileName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        serverIp: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        serverPort: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        userPassword: [
          { validator: this.passwordRightValid, trigger: 'blur' }
        ]
      },
      // 所有终端版本（去重）放在index组件，方便后续扩展需要在表格中显示 生效对象版本
      versionOpts: []
    }
  },
  computed: {
  },
  watch: {
    uploadVisible(val) {
      if (!val) {
        this.cancel()
      }
    }
  },
  created() {
    this.resetTemp()
    this.resetVersionOpts()
  },
  methods: {
    getPackageTable() {
      return this.$refs['packageList']
    },
    loadPackage(option) {
      return getPackageFilterPage(option)
    },
    packageSelectChange(val) {
      this.packageDeleteable = val.length > 0
    },
    setPackageTemp() {
      this.packageTemp = Object.assign({}, this.defaultPackageTemp)
      this.fileList.splice(0)
    },
    handleMakePackage(row) {
      this.resetTemp()
      this.temp.packageId = row.id
      this.temp.fileName = row.fileName
      isNewInstallPackage({ packageId: row.id }).then(resp => {
        if (resp.data) {
          this.oldType = true
          this.makeVisible = true
        } else {
          // 旧版终端安装包只允许配置单个采集服务器ip、地址
          this.oldType = false
          this.makeVisible = true
        }
      })
    },
    handleCreatePackage() {
      this.uploadVisible = true
      this.resetPackageTemp()
    },
    handleCreate() {
      this.$refs.editDlg.handleCreate(this.$refs.packageList.getSelectedDatas())
    },
    resetPackageTemp() {
      this.packageTemp = Object.assign({}, this.defaultPackageTemp)
      this.fileList.splice(0)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.silentServerList = [{
        serverIp: '127.0.0.1',
        serverPort: 20181
      }]
      this.generalServerList = [{
        serverIp: '127.0.0.1',
        serverPort: 20181
      }]
    },
    handleFilter() {
      this.query.page = 1
      this.getPackageTable().execRowDataApi(this.query)
    },
    handleDeletePack() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.getPackageTable().getSelectedIds()
        deletePack({ ids: toDeleteIds.join(',') }).then(respond => {
          this.getPackageTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    fileChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          message: this.$t('pages.upgrade_text2'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const fileName = file.name
      // const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      // if (ext != 'exe') {
      //   this.$message({
      //     message: this.$t('pages.makePackage_text7'),
      //     type: 'error',
      //     duration: 2000
      //   })
      // }
      this.packageTemp.fileName = fileName
      this.fileList.splice(0, 1, file)
    },
    saveFile() {
      this.submitting = true
      this.fileSubmitting = true
      this.percentage = 0
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const onUploadProgress = (progressEvent) => {
            const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
            this.percentage = parseInt(percent)
          }
          this.source = this.connectionSource()
          const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
          // 通过 FormData 对象上传文件
          const fd = this.toFormData(this.packageTemp)
          fd.append('uploadFile', this.fileList[0].raw)
          // 发起请求
          uploadSoft(fd, onUploadProgress, cacheToken).then(res => {
            this.submitting = false
            this.fileSubmitting = false
            this.percentage = 0
            this.uploadVisible = false
            this.getPackageTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.upgrade_text3'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
            this.fileSubmitting = false
            this.percentage = 0
          })
        } else {
          this.submitting = false
          this.fileSubmitting = false
          this.percentage = 0
        }
      })
    },
    passwordRightValid(rule, value, callback) {
      if (this.temp.userName != '' && this.temp.userPassword == '') {
        callback(new Error(this.$t('pages.makePackage_text8')))
      } else {
        callback()
      }
    },
    duplication(data) {
      const serverInfo = [];
      for (const server of data) {
        if (serverInfo.find((item) => item.serverIp == server.serverIp && item.serverPort == server.serverPort)) {
          continue;
        }
        serverInfo.push(server);
      }
      return serverInfo;
    },
    formatterData() {
      if (this.temp.type == 1) {
        this.silentServerList = this.duplication(this.silentServerList)
        this.temp.serverInfo.splice(0, this.temp.serverInfo.length, ...this.silentServerList)
      } else if (this.temp.type == 2) {
        if (this.temp.forceSetServerAdd == 0) {
          const server = { serverIp: '127.0.0.1', serverPort: 20181 }
          this.temp.serverInfo.splice(0, this.temp.serverInfo.length, server)
        } else {
          this.generalServerList = this.duplication(this.generalServerList)
          this.temp.serverInfo.splice(0, this.temp.serverInfo.length, ...this.generalServerList)
        }
      }
    },
    createData() {
      this.formatterData()
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          makeInstallationPackage(this.temp).then(res => {
            this.submitting = false
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    addSlientServer() {
      const server = {
        serverIp: '127.0.0.1',
        serverPort: 20181
      }
      this.silentServerList.push(server)
    },
    addGeneralServer() {
      const server = {
        serverIp: '127.0.0.1',
        serverPort: 20181
      }
      this.generalServerList.push(server)
    },
    deleteSilentServer(index) {
      this.silentServerList.splice(index, 1)
    },
    deleteGeneralServer(index) {
      this.generalServerList.splice(index, 1)
    },
    makePackageFormatter(data, btn) {
      return data.fileName.substr(data.fileName.length - 4, data.fileName.length) != '.exe'
    },
    resetVersionOpts() {
      fetchDistinctVersions().then(res => {
        this.versionOpts.splice(0, this.versionOpts.length, ...res.data)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-input-number.is-without-controls .el-input__inner {
    text-align: left;
  }
</style>
