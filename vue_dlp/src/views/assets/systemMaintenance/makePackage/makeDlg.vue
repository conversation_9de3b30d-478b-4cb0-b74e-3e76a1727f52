<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="$t('pages.makePackage_text2')"
    :visible.sync="makeVisible"
    width="600px"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px">
      <input type="text" class="autocomplete">
      <input type="password" class="autocomplete">
      <el-divider content-position="left"><span style="color: #0c60a5">{{ $t('pages.collectionServerList') }}</span></el-divider>
      <div style="position: absolute;top: 60px;right: 15px;background-color: #e4e7e9;">
        <i class="el-icon-remove-outline" style="float: right;color: #73b6e0;font-size: 20px;" />
        <i class="el-icon-circle-plus-outline" style="float: right;color: #73b6e0;font-size: 20px;" />
        <i class="el-icon-download" style="float: right;color: #73b6e0;font-size: 15px;border: 1px solid rgb(115, 191, 227);border-radius: 10px;margin: 1px 2px;" />
      </div>
      <grid-table
        ref="alternateServerTable"
        :multi-select="true"
        :col-model="alternateServerColModel"
        :row-datas="alternateServerRowDatas"
        :show-pager="false"
        :height="200"
      />
      <el-divider content-position="left"><span style="color: #0c60a5">{{ $t('pages.makePackage_text4') }}</span></el-divider>
      <FormItem :label="$t('pages.makePackage_text5')" prop="userName">
        <el-input v-model="temp.userName" style="width: 90%;" maxlength="30"/>
        <el-tooltip effect="dark" placement="bottom-end">
          <div slot="content">
            {{ $t('pages.makePackage_text6') }}
          </div>
          <i class="el-icon-info" />
        </el-tooltip>
      </FormItem>
      <FormItem :label="$t('pages.pwdInfo')" prop="userPassword">
        <el-input v-model="temp.userPassword" type="password" style="width: 90%;" show-password></el-input>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <span v-if="submitting" style="color: #666; margin-right: 65px;">{{ $t('pages.makingPackage') }}</span>
      <el-button :loading="submitting" type="primary" @click="createData()">{{ $t('pages.making') }}</el-button>
      <el-button @click="makeVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { makeInstallationPackage } from '@/api/assets/systemMaintenance/terminalUpgrade'

export default {
  name: 'MakeDlg',
  data() {
    return {
      alternateServerColModel: [
        { prop: 'ip', label: this.$t('pages.daqServerAddress'), width: '100', sort: 'custom' },
        { prop: 'port', label: this.$t('pages.daqServerPort'), width: '100', sort: 'custom' }
      ],
      alternateServerRowDatas: [],
      configAlternateServer: 0,
      makeVisible: false,
      submitting: false,
      temp: {
      },
      defaultTemp: {
        fileName: '',
        packageId: null,
        installType: 1,               // 安装类型 0-默认安装类型 1-静默安装 2-静默提权安装
        serverIp: '127.0.0.1',        // 服务器ip
        serverPort: 20181,            // 服务器 端口号
        userName: '',                 // 计算机用户名
        userPassword: ''              // 用户密码
      },
      rules: {
        fileName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        serverIp: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        serverPort: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        userPassword: [
          { validator: this.passwordRightValid, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  methods: {
    show(data) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, data)
      this.makeVisible = true
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    passwordRightValid(rule, value, callback) {
      if (this.temp.userName != '' && this.temp.userPassword == '') {
        callback(new Error(this.$t('pages.makePackage_text8')))
      } else {
        callback()
      }
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          makeInstallationPackage(this.temp).then(res => {
            this.submitting = false
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
</style>
