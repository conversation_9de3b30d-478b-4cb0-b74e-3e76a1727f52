<template>
  <div class="app-container">
    <TransferTree
      ref="transferTree"
      :show-tree.sync="showTree"
      :showed-tree="['terminal']"
      :os-type-filter="7"
      :terminal-filter-key="terminalFilter"
      @data-change="strategyTargetNodeChange"
    />
    <div class="table-container">
      <el-tabs
        ref="tabs"
        v-model="activeName"
        type="card"
        @tab-click="tabClick"
      >
        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.name">
          <div class="table-container">
            <div class="toolbar">
              <el-button type="primary" size="mini" @click="toggleTreeMenu">
                <svg-icon icon-class="tree" />
              </el-button>
              <el-button type="primary" icon="el-icon-refresh" size="mini" :disabled="refreshDisable" @click="refreshInfo">
                {{ $t('button.refresh') }}
              </el-button>
              <audit-log-exporter v-permission="'161'" :request="handleExport" :disabled="refreshDisable"/>
              <div class="searchCon">
                <el-input
                  v-model="query[item.query]"
                  v-trim
                  clearable
                  :placeholder="item.placeholder"
                  style="width: 160px"
                  @keyup.enter.native="handleFilter"
                ></el-input>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <grid-table
              :ref="item.name"
              v-loading="tableLoading"
              :col-model="item.colModel"
              :row-data-api="rowDataApi"
              :default-sort="item.defaultSort"
              :show-pager="false"
              :multi-select="false"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { ctrlErrorMap, enableCtrlTerm } from '@/api/system/terminalManage/moduleConfig'
import { listTCPConnection, listListenPort, exportTCPConnection, exportListenPort } from '@/api/assets/systemMaintenance/networkResourceMonitor'

export default {
  name: 'NetworkResourceMonitor',
  data() {
    return {
      tabList: [
        { label: this.$t('pages.tcpConnection'), name: 'tcpConnection', query: 'tcpProcessName', placeholder: this.$t('table.name'), defaultSort: { prop: 'processName' },
          colModel: [
            { prop: 'processName', label: 'name', width: '150', fixed: true, sort: true },
            { prop: 'processId', label: 'PID', width: '100', sort: true, formatter: this.formatPID },
            { prop: 'localAddress', label: this.$t('pages.localAddress'), width: '200', sort: true },
            { prop: 'localPort', label: this.$t('pages.localPort'), width: '100', sort: true },
            { prop: 'remoteAddress', label: this.$t('pages.remoteAddress'), width: '200', sort: true },
            { prop: 'remotePort', label: this.$t('pages.remotePort'), width: '100', sort: true }
          ]
        },
        { label: this.$t('pages.listenPort'), name: 'listenPort', query: 'portProcessName', placeholder: this.$t('table.name'), defaultSort: { prop: 'processName' },
          colModel: [
            { prop: 'processName', label: 'name', width: '150', fixed: true, sort: true },
            { prop: 'processId', label: 'PID', width: '100', sort: true, formatter: this.formatPID },
            { prop: 'address', label: this.$t('pages.address'), width: '200', sort: true, formatter: this.formatAddress },
            { prop: 'port', label: 'port', width: '100', sort: true },
            { prop: 'protocol', label: 'protocol', width: '100', sort: true, formatter: this.formatProtocol },
            { prop: 'firewallAllow', label: this.$t('pages.firewallStatus'), width: '200', sort: true, formatter: this.formatFirewallStatus }
          ]
        }
      ],
      rowData: [],
      showTree: true,
      ctrlAble: false,
      refreshDisable: true,
      query: {
        tcpProcessName: '',
        portProcessName: ''
      },
      tableLoading: false,
      activeName: 'tcpConnection'
    }
  },
  computed: {
    gridTable() {
      const curTab = this.getCurTab()
      return this.$refs[curTab.name][0]
    },
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    },
    queryApi() {
      return this.activeName === 'tcpConnection' ? listTCPConnection : this.activeName === 'listenPort' ? listListenPort : undefined
    },
    exportApi() {
      return this.activeName === 'tcpConnection' ? exportTCPConnection : this.activeName === 'listenPort' ? exportListenPort : undefined
    }
  },
  watch: {
    rowData: {
      deep: true,
      handler(val) {
        this.gridTable && this.gridTable.execRowDataApi()
      }
    }
  },
  created() {
  },
  methods: {
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('networkResourceMonitor', termId, [2], '5.02').then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.refreshDisable = true
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) })
        } else {
          this.ctrlAble = true
        }
      })
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    rowDataApi(options) {
      const curTab = this.getCurTab()
      const searchInfo = (this.query[curTab.query] || '').toLowerCase()
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: searchInfo ? this.rowData.filter(data => data['processName'].toLowerCase().includes(searchInfo)) : this.rowData })
      })
    },
    strategyTargetNodeChange(tabName, data) {
      this.rowData = []
      if (!data || data.id.indexOf('G' + data.dataId) > -1) {
        this.refreshDisable = true
        return
      }
      this.checkCtrlAble(data.dataId).then(() => {
        if (!this.ctrlAble) return
        this.refreshDisable = false
        this.tableLoading = true
        const queryObj = this.getQueryObj()
        queryObj.termId = data.dataId
        this.queryApi(queryObj).then(respond => {
          this.rowData = []
          if (respond.data.supportWinXP == 0) {
            this.$message({
              message: this.$t('pages.winXpNotSupport'),
              type: 'info',
              duration: 2000
            })
            this.tableLoading = false
            return
          }
          this.tableLoading = false
          respond.data.list.forEach((data, index) => {
            data.id = new Date().getTime() + index // 添加行ID
            this.rowData.push(data)
          })
        }).catch(e => {
          if (this.tableLoading) {
            this.tableLoading = false
          }
        })
      })
    },
    refreshInfo: function() {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      this.strategyTargetNodeChange(null, curNodeData)
    },
    handleDrag() {
    },
    handleExport() {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      const queryObj = this.getQueryObj()
      queryObj.termId = curNodeData.dataId
      return this.exportApi(queryObj)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleFilter() {
      this.gridTable && this.gridTable.execRowDataApi()
    },
    tabClick(tab, event) {
      this.refreshInfo()
    },
    getQueryObj() {
      return this.activeName === 'tcpConnection' ? { processName: this.query.tcpProcessName } : this.activeName === 'listenPort' ? { processName: this.query.portProcessName } : undefined
    },
    getCurTab() {
      return this.tabList.find(item => item.name === this.activeName)
    },
    formatAddress(row, data) {
      if (data == '0.0.0.0') {
        return this.$t('pages.notSpecified') + ' ' + this.$t('table.ipv4')
      } else if (data == '::') {
        return this.$t('pages.notSpecified') + ' ' + this.$t('table.ipv6')
      } else if (data == '127.0.0.1') {
        return this.$t('pages.ipv4Loopback')
      } else if (data == '::1') {
        return this.$t('pages.ipv6Loopback')
      } else {
        return data
      }
    },
    formatProtocol(row, data) {
      if (data == 0) {
        return 'TCP'
      } else if (data == 1) {
        return 'UDP'
      } else {
        return data
      }
    },
    formatPID(row, data) {
      if (data == '0') {
        return ''
      } else {
        return data
      }
    },
    formatFirewallStatus(row, data) {
      if (data == 2 || row.firewallRestrict == 2) {
        return ''
      }
      const allowStr = data == 0 ? this.$t('pages.notAllow') : this.$t('pages.allow');
      const restrictStr = row.firewallRestrict == 0 ? this.$t('pages.unrestricted') : this.$t('pages.restricted');
      return allowStr + '，' + restrictStr;
    }
  }
}
</script>
