<template>
  <file-manager :selection="selection" @refresh="refreshFile">
    <tree-menu
      slot="tree"
      ref="fileTree"
      resizeable
      accordion
      :data="treeData"
      :icon-option="treeIconOption"
      :local-search="false"
      :get-search-list="getSearchListFunction()"
      :checked-keys="selectedData"
      :expand-on-click-node="true"
      :default-expanded-keys="defaultExpandedKeys"
      :filter-key="filterFunc"
      @node-click="nodeClick"
    />
    <template slot="toolbar">
      <file-search
        style="float: right; margin-left: 10px; z-index: 1;"
        :disabled="searchDisabled"
        @search="handleSearch"
      />
      <!--<chunk-download v-if="showFileDownload" :download-obj="downloadObj"/>-->
      <explorer-downloader
        v-if="showFileDownload"
        :terminal-id="currentTerminal.dataId"
        :dir="curDir"
        :selection="selection"
      />
      <div ref="breadcrumbCon" class="breadcrumb-container" :style="`width: calc(100% - ${containerW}px)`">
        <el-dropdown v-show="hiddenFilePath.length > 0" class="show-hidden" placement="bottom" trigger="click" @command="handleHiddenFile">
          <i class="el-icon-d-arrow-left icon"></i>
          <el-dropdown-menu slot="dropdown" class="hidden-item">
            <el-dropdown-item v-for="(item, index) in hiddenFilePath" :key="item.id" :title="item.label" :command="index">{{ item.label }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-breadcrumb v-show="curFilePath.length > 0" ref="fileBreadcrumb" separator="/">
          <el-breadcrumb-item v-for="(item, index) in showFilePath" :key="item.id">
            <a href="javascript:void(0);" :title="item.label" @click="curFilePathClick(index, showFilePath, true)">{{ item.label }}</a>
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </template>
    <div class="toolbar">
      <!--<el-radio-group v-model="layout" style="margin-right: 30px;">
        <el-radio-button label="tile"><i class="el-icon-menu"></i> 平铺</el-radio-button>
        <el-radio-button label="icon"><i class="el-icon-picture-outline"></i> 图标</el-radio-button>
        <el-radio-button label="list"><i class="el-icon-s-grid"></i> 列表</el-radio-button>
        <el-radio-button label="detail"><i class="el-icon-notebook-2"></i> 详细信息</el-radio-button>
      </el-radio-group>-->
      <el-checkbox v-model="showHiddenFile" @change="handleHiddenTreeNode">{{ $t('pages.showHiddenFile') }}</el-checkbox>
      <el-checkbox v-model="showEncryptedIcon">{{ $t('pages.showEncryptedIcon') }}</el-checkbox>
      <el-checkbox v-model="onlyShowEncryptedFile">{{ $t('pages.onlyShowEncryptedFile') }}</el-checkbox>
      <el-dropdown class="layout-dropdown" trigger="click" @command="handleLayoutChange">
        <el-button size="mini">
          {{ $t('pages.layout') }}<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="(item, index) in layoutOpts"
            :key="index"
            :command="item.value"
            :icon="item.icon"
            :class="{ 'highlight-layout': item.value === layout }"
          >
            {{ item.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!--<el-button v-if="showFileDownload" class="layout-dropdown" icon="el-icon-setting" size="mini" @click="openSetting">
        {{ $t('route.downloadSetting') }}
      </el-button>-->
    </div>
    <explorer-layout
      :layout="layout"
      :loading="tableLoading"
      :data="filteredRowData"
      :col-model="colModel"
      :selectable="selectable"
      :default-sort="defaultSort"
      :filter-selected-type="2"
      :icon-formatter="iconClassFormatter"
      :show-encrypted-icon="showEncryptedIcon"
      @selected="handleSelectionChange"
      @dblclick="rowDblclickFunc"
    />
    <explorer-download-setting ref="setting"/>
  </file-manager>
</template>

<script>
import { mapGetters } from 'vuex'
import ExplorerLayout from './layout'
import ExplorerDownloader from './downloader'
import ExplorerDownloadSetting from './setting'
import FileManager from '@/components/FileManager'
import FileSearch from '@/components/FileManager/search'
import { getFileIcon } from '@/icons/extension'
import moment from 'moment'
import { debounce } from '@/utils'
import { isShowFileDownload } from '@/api/grantAuth'
import { fileTypeFormatter, fileSizeFormatter, listChildFile } from '@/api/assets/systemMaintenance/explorer'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'
import { getTermTypeDict } from '@/utils/dictionary'

export default {
  name: 'Explorer',
  components: { ExplorerLayout, ExplorerDownloader, ExplorerDownloadSetting, FileManager, FileSearch },
  data() {
    return {
      layout: 'detail',
      layoutOpts: [
        { value: 'tile', label: this.$t('pages.placeOptions10'), icon: 'el-icon-menu' },
        { value: 'icon', label: this.$t('table.icon'), icon: 'el-icon-picture-outline' },
        { value: 'list', label: this.$t('pages.list'), icon: 'el-icon-s-grid' },
        { value: 'detail', label: this.$t('table.detailInfo'), icon: 'el-icon-notebook-2' }
      ],
      defaultSort: { prop: 'oriData.type', order: 'desc' },
      colModel: [
        { prop: 'label', label: 'name', width: '200', sort: true, iconFormatter: this.iconClassFormatter },
        { prop: 'oriData.type', label: 'fileType', width: '150', sort: true, sortOriginal: [true, false], sortArr: ['oriData.type', 'suffix'], formatter: this.fileTypeFormatter },
        { prop: 'oriData.size', label: 'size1', width: '150', sort: true, sortOriginal: true, formatter: this.fileSizeFormatter },
        { prop: 'oriData.modifyTime', label: 'updateTime', width: '150', sort: true },
        { prop: 'oriData.createTime', label: 'createTime', width: '150', sort: true },
        { prop: 'oriData.attributes', label: 'attribute', width: '100', sort: true, formatter: this.attributesFormatter }
      ],
      query: { // 查询条件
        page: 1,
        groupId: undefined,
        searchInfo: ''
      },
      treeData: [],
      selectedData: [],
      treeIconOption: {
        typeKey: 'dataType',
        'G': 'terminalGroup',
        127: 'error'
      },
      osType: 7, // 支持的终端操作系统类型，1-win,2-linux,4-mac,8-移动设备
      rowData: [],
      dataCache: {},
      curFilePath: [],
      showFilePath: [],
      hiddenFilePath: [],
      defaultExpandedKeys: [],
      deleteable: false,
      ctrlAble: false,
      submitting: false,
      iconOption: {
        0: 'disk',
        1: 'dir',
        2: 'file'
      },
      fileType: {
        1: this.$t('pages.FileFolder'),
        2: this.$t('pages.File')
      },
      currentTerminal: {},
      currentNode: null, // 保存当前节点
      tableLoading: false,
      downloadPath: [], // 获取文件路径有用的片段
      downloadObj: {}, // 文件上传参数
      dirPath: '', // 拼接文件路径
      showFileDownload: false,
      showHiddenFile: false,
      showEncryptedIcon: true,
      onlyShowEncryptedFile: false,
      selection: []
    }
  },
  computed: {
    ...mapGetters([
      'termTree',
      'termTreeList',
      'termStatusMap'
    ]),
    fileTree() {
      return this.$refs['fileTree']
    },
    containerW() {
      const downloadW = this.showFileDownload ? 135 : 50
      const marginW = 100
      return downloadW + marginW
    },
    searchDisabled() {
      if (this.tableLoading || this.curFilePath.length === 0) {
        return true
      }
      const lastNode = this.curFilePath[this.curFilePath.length - 1]
      if (lastNode.type === '1') {
        return !lastNode.online
      }
      return lastNode.type !== 'Map'
    },
    filteredRowData() {
      // 当 不显示隐藏文件 或 只显示加密文件 时，需要过滤
      if (!this.showHiddenFile || this.onlyShowEncryptedFile) {
        return this.rowData.filter(row => {
          // 显示隐藏的文件
          const showHiddenFile = this.filterHiddenData(row)
          // 非只显示加密文件 或 文件为加密文件（attributes包含64），则为true
          const onlyShowEncryptedFile = !this.onlyShowEncryptedFile || (row.oriData.type < 2 || (row.oriData.attributes & 64) > 0)
          // 同时满足上述条件则显示
          return showHiddenFile && onlyShowEncryptedFile
        })
      }
      // 不过滤
      return this.rowData
    },
    curDir() {
      return this.curFilePath.filter(item => {
        if (!item.hasOwnProperty('oriData') || !item.oriData) {
          return false
        }
        return typeof item.oriData === 'object' && item.oriData.fileName === item.label
      }).map(item => item.label).join('/')
    }
  },
  watch: {
    termTree() {
      this.initTreeNode()
    },
    termStatusMap() {
      this.changeTermTreeStatus(this.treeData, this.termStatusMap)
    },
    curFilePath(val) {
      this.resetBreadcrumb(val)
    }
  },
  mounted() {
    // 防抖
    this.__resizeHandler = debounce(() => {
      if (this.$route.name != 'Explorer') return
      this.resetBreadcrumb(this.curFilePath)
    }, 100)
    window.addEventListener('resize', this.__resizeHandler)
  },
  created() {
    getTermTypeDict().forEach(item => {
      this.$set(this.treeIconOption, item.value, item.icon)
    })
    this.resetTemp()
    if (this.termTree && this.termTree.length > 0) {
      this.initTreeNode()
    }
    this.checkShowFileDownload();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.__resizeHandler)
  },
  methods: {
    fileTypeFormatter,
    fileSizeFormatter,
    checkShowFileDownload() {
      if (!this.hasPermission('205')) {
        this.showFileDownload = false
        return;
      }
      isShowFileDownload().then(resp => {
        this.showFileDownload = resp.data
      })
    },
    // 设置只有行是文件的时候才可以点击，文件夹不可以点击勾选
    selectable(row, index) {
      return row.isLeaf
    },
    handleLayoutChange(command) {
      this.layout = command
    },
    openSetting() {
      this.$refs.setting.show()
    },
    changeTermTreeStatus(nodeDatas, statusMap) {
      if (nodeDatas.length > 0) {
        nodeDatas.forEach(nodeData => {
          if (nodeData.type == 1) { // 表示终端
            const status = statusMap[nodeData.dataId]
            nodeData.online = !!status && status.status != 0
            this.$set(nodeData, 'colorClass', status ? (status.status == 0 ? '' : 'green') : '')
          } else if (nodeData.children) {
            this.changeTermTreeStatus(nodeData.children, statusMap)
          }
        })
      }
    },
    initTreeNode() {
      if (this.treeData.length > 0) {
        return
      }
      const treeData = JSON.parse(JSON.stringify(this.termTree))
      this.treeData.splice(0, this.treeData.length, ...treeData)
      this.changeTermTreeStatus(this.treeData, this.termStatusMap)
    },
    // 通过终端类型过滤节点
    filterTermNode(node) {
      let termOsType = 0
      if (node.oriData != 0) { // 说明是已清理的终端
        termOsType = 0
      } else if (node.dataType >= 0x10) { // 说明是老板终端
        termOsType = Math.pow(2, node.dataType - 0x10)
      } else { // 说明是普通终端
        termOsType = Math.pow(2, node.dataType)
      }
      return (this.osType & termOsType) > 0
    },
    // 显示隐藏文件 或 文件属性非隐藏（attributes不包含2），则为true
    filterHiddenData(data) {
      return this.showHiddenFile || (data.oriData.attributes & 2) == 0
    },
    resetBreadcrumb(path) {
      this.showFilePath.splice(0, this.showFilePath.length, ...path)
      this.$nextTick(() => {
        const containerW = this.$refs.breadcrumbCon.offsetWidth
        const itemsW = this.$refs.fileBreadcrumb.$children.map(item => item.$el.offsetWidth)
        let totalW = itemsW.reduce((total, cur, i) => total + cur + 16, 0)
        let index
        for (let i = 1; totalW > containerW; i++) {
          totalW -= itemsW.shift()
          index = i
        }
        this.hiddenFilePath.splice(0, this.hiddenFilePath.length, ...this.showFilePath.splice(0, index).reverse())
      })
    },
    // 面包屑点击方法
    curFilePathClick(index, filePath, breadcrumb) {
      if (filePath.length === index + 1 && breadcrumb) {
        return // 路径的最后一个节点，点击无效
      }
      const data = filePath[index]
      this.defaultExpandedKeys.splice(0)
      const ztree = this.fileTree
      const node = ztree.getNode(data.id)
      ztree.setCurrentKey(data.id)
      this.defaultExpandedKeys.push(data)
      this.nodeClick(data, node)
    },
    handleHiddenFile(index) {
      this.curFilePathClick(index, this.hiddenFilePath)
    },
    // 勾选 “显示隐藏文件” 多选框时，对树节点进行过滤
    handleHiddenTreeNode(val) {
      if (val) {
        // 添加隐藏的节点
        this.addHiddenNode(this.currentTerminal)
      } else {
        // 移除隐藏的节点
        this.removeHiddenNode(this.currentTerminal)
      }
      this.$nextTick(() => {
        // 当前节点是否被隐藏
        const node = this.fileTree.getNode(this.currentNode.key)
        if (node) {
          // 未被隐藏，滚动到当前节点
          this.fileTree.scrollToNode(node.data)
        } else {
          // 被隐藏，滚动到当前终端节点
          this.fileTree.scrollToNode(this.currentTerminal)
        }
      })
    },
    // 添加隐藏的文件夹
    addHiddenNode(nodeData) {
      const datas = [nodeData]
      const ztree = this.fileTree
      while (datas.length > 0) {
        const data = datas.shift()
        const allChildren = this.dataCache[data.id]
        if (allChildren) {
          datas.push(...allChildren)
        }
        // 终端磁盘、文件夹 才需要添加到树结构中
        if (data.oriData.type === 0 || data.oriData.type === 1) {
          const node = ztree.getNode(data.id)
          if (!node) {
            ztree.addNode(data)
          }
        }
      }
    },
    // 移除隐藏的文件夹
    removeHiddenNode(nodeData) {
      const datas = nodeData.children ? [...nodeData.children] : []
      const ztree = this.fileTree
      while (datas.length > 0) {
        const nodeData = datas.shift()
        if (!this.filterHiddenData(nodeData)) {
          // 隐藏的文件夹直接移除
          ztree.removeNode([nodeData.id])
        } else {
          // 将子文件夹添加到 datas，继续判断
          const children = nodeData.children || []
          datas.unshift(...children)
        }
      }
    },
    // 修改当前节点的路径数组
    changeCurFilePath(node) {
      this.currentNode = node
      // 获取节点路径
      const nodePath = this.fileTree.getNodePath(node)
      this.curFilePath.splice(0, this.curFilePath.length, ...nodePath)
    },
    filterFunc(data) {
      // 过滤回收站节点和被清理终端节点
      return data.id != 'G-2' && data.oriData != 1
    },
    //
    getSearchListFunction() {
      return () => {
        return this.termTreeList || []
      }
    },
    // 获取终端的资源数据
    sendToUser(terminalId, continueFlag, fileType, parentPath, data, node) {
      listChildFile({
        terminalId, continueFlag, fileType, parentPath
      }).then(response => {
        this.$socket.subscribeToAjax(response, terminalId + '/listChildFile', (respond, handle) => {
          handle.close()
          this.tableLoading = false
          if (respond.data && respond.data.length > 0) {
            const timestamp = new Date().getTime()
            respond.data.forEach(node => {
              node.suffix = node.label.indexOf('.') > -1 ? node.label.split('.').pop() : ''
              node.id = `${terminalId}-${node.id}-${timestamp}`
              // 将数据添加到table
              this.rowData.push(node)
              // 将终端磁盘、文件夹添加到树结构中
              if (node.oriData.type === 0 || node.oriData.type === 1) {
                // 修改返回节点的父节点id
                node.parentId = data.id
                // 未被过滤的文件夹添加到树节点
                if (this.filterHiddenData(node)) {
                  this.fileTree.addNode({ ...node })
                }
                this.isOnlyFile = false
              } else {
                node.isLeaf = true
              }
            })
            // oriData.endFlag === 1 说明数据未完全请求回来，需要再次请求数据
            if (respond.data[0].oriData.endFlag === 1) {
              this.$nextTick(() => {
                this.sendToUser(terminalId, 1, fileType, parentPath, data, node)
              })
            } else {
              this.setDataCache(data.id, this.rowData)
              node.loading = false
              node.expanded = true
              if (this.isOnlyFile) {
                node.isLeaf = true
              }
            }
          } else {
            node.isLeaf = true
            node.loading = false
            this.setDataCache(data.id, this.rowData)
          }
        })
      }).catch(e => {
        if (this.tableLoading) {
          this.tableLoading = false
        }
      })
    },
    // 获取当前的目录路径
    getParentPath: function() {
      const lastNode = this.curFilePath.slice(-1)[0]
      const isTerminal = lastNode.type != 'Map' && lastNode.type != 'G'
      const parentPath = this.curFilePath.reduce((path, item) => item.oriData ? (path ? `${path}\\${item.label}` : item.label) : '', '')
      return isTerminal ? lastNode.id : parentPath
    },
    calcLoadFileType(isDisk) {
      // 0：枚举盘符
      if (isDisk) {
        return 0
      }
      // 文件类型，按位与。1：枚举文件夹（不包含子目录）2：枚举加密文件（不包含子目录）4：枚举非加密文件
      // 8：隐藏文件，隐藏文件是否显示由前端控制
      return 1 | 2 | 4 | 8
    },
    // 获取组织结构及终端资源数据
    loadFileTree: function(data, node, el) {
      this.tableLoading = true
      this.rowData = []
      if (!(!data.id || data.id.indexOf('G' + data.dataId) >= 0)) { // 终端节点或目录节点
        const terminalId = data.oriData ? parseInt(data.oriData.terminalId) : data.dataId
        const continueFlag = 2
        const fileType = this.calcLoadFileType(!data.oriData)
        const parentPath = this.getParentPath()
        // 如果切换了终端就将缓存的数据清除
        if (this.currentTerminal.dataId != terminalId) {
          this.clearDataCache(this.currentTerminal)
          // 将原终端节点的展开图标隐藏
          if (this.currentTerminal.id) {
            const node = this.fileTree.getNode(this.currentTerminal.id)
            node.isLeaf = true
          }
          this.currentTerminal = data
          this.defaultExpandedKeys.splice(0)
        }
        this.isOnlyFile = true
        this.checkCtrlAble(terminalId).then(() => {
          if (!this.ctrlAble) {
            this.tableLoading = false
            node.loading = false
            return
          }
          this.sendToUser(terminalId, continueFlag, fileType, parentPath, data, node)
        })
      }
    },
    refreshFile() {
      if (this.currentNode) {
        const node = this.currentNode
        const data = node.data
        if (data && (data.type == 1 || data.type == 'Map')) {
          this.rowData = []
          data.children && data.children.splice(0)
          node.childNodes && node.childNodes.splice(0)
          node.expanded = false
          node.loading = true
          this.loadFileTree(data, node, null)
        }
      }
    },
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('explorer', termId, [2]).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    clearDataCache(data) {
      if (!data.dataId) return
      this.dataCache = {}
      const node = this.$refs.fileTree.getNode(data)
      if (node) {
        data.children && data.children.splice(0)
        node.childNodes && node.childNodes.splice(0)
        node.expanded = false
      }
    },
    setDataCache(key, val) {
      this.dataCache[key] = [...val]
    },
    nodeClick(data, node, el) {
      if (this.currentNode && this.currentNode.key != node.key) {
        // 切换节点后，未加载完毕的节点加载中的图标隐藏掉
        this.currentNode.loading = false
      }
      this.changeCurFilePath(node)
      const dataCache = this.dataCache[data.id]
      if (dataCache) {
        this.rowData = dataCache
      } else {
        this.rowData = []
        if (data.type == 1 || data.type == 'Map') {
          node.loading = true
          this.loadFileTree(data, node, el)
        }
      }
    },
    rowDblclickFunc(rowData, column, event) {
      if (rowData.oriData.type == 2) return
      const ztree = this.fileTree
      const node = ztree.getNode(rowData.id)
      ztree.setCurrentKey(rowData.id)
      const data = node.data
      this.defaultExpandedKeys.push(data)
      this.nodeClick(data, node)
    },
    handleSelectionChange(selection) {
      this.deleteable = selection.length > 0
      this.selection = selection.map(item => {
        const { oriData: { fileName: name, size, type }} = item
        return { name, size, type }
      })
      // this.downloadData(selection)
    },
    /**
     * 上传文件数据
     * @param val 表格选中值
     */
    downloadData(val) {
      if (val.length === 0) {
        this.downloadObj = undefined
        return
      }
      this.downloadPath = []
      this.curFilePath.forEach((item, index) => { // 从curFilePath中获取需要的文件路径
        if (item.hasOwnProperty('oriData')) {
          this.downloadPath.push(item)
        }
      })
      this.dirPath = '' // 拼接文件路径
      for (let i = 0; i < this.downloadPath.length; i++) {
        this.dirPath += this.downloadPath[i].label + '/'
      }
      if (val.length !== 0) {
        const file = []
        for (let i = 0; i < val.length; i++) { // 获取选中文件的名称和大小
          const fileObj = {}
          fileObj.name = val[i].oriData.fileName
          fileObj.size = val[i].oriData.size
          file.push(fileObj)
        }
        this.downloadObj = { // 文件下载按钮点击时，需要传给后台的值
          terminalId: val[0].oriData.terminalId,
          dir: this.dirPath.substring(0, this.dirPath.length - 1),
          fileList: file
        }
      }
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    sizeFormatter: function(row, data) {
      let size = ''
      if (row.oriData.type === 2) {
        size = Math.ceil(data / 1024)
      }
      return size
    },
    iconClassFormatter: function(row) {
      const icons = []
      // 0：硬盘 1：文件夹 2. 文件
      const type = row.oriData.type
      let title
      let iconName
      if (type == 2) {
        title = row.label.split('.').pop().toLowerCase()
        iconName = getFileIcon(title)
      } else {
        title = { 0: this.$t('pages.disk'), 1: this.$t('pages.isFileFolder') }[type]
        iconName = { 0: 'disk1', 1: 'dir1' }[type]
      }
      icons.push({ class: iconName, title: title })
      // 加密
      if (this.showEncryptedIcon && (row.oriData.attributes & 64) > 0) {
        icons.push({ class: 'lock', css: { 'margin-right': 0 }, style: 'font-size: 7px; position: absolute; bottom: 8px; left: 19px;' })
        icons[0].title += this.$t('pages.encryptionParentheses')
      }
      return icons
    },
    attributesFormatter: function(row, data) {
      const result = []
      if (data & 1) {
        result.push(this.$t('pages.readOnly'))
      }
      if (data & 2) {
        result.push(this.$t('pages.hide'))
      }
      if (data & 32) {
        result.push(this.$t('pages.files1'))
      }
      // if (data & 64) {
      //   result.push('加密')
      // }
      return result.join(' | ')
    },
    handleSearch(data) {
      const currentNodeId = this.currentNode.data.id
      this.rowData = (this.dataCache[currentNodeId] || []).filter(item => {
        if (data.name && item.label.toLowerCase().indexOf(data.name.toLowerCase()) < 0) {
          return false
        }
        if (item.oriData.type === 0) { // 磁盘没有时间
          return true
        }
        if (data.modified) {
          const modifyTime = moment(item.oriData.modifyTime, 'YYYY-MM-DD HH:mm:ss').unix()
          if (modifyTime < data.modified[0] || modifyTime > data.modified[1]) {
            return false
          }
        }
        if (data.creation) {
          const createTime = moment(item.oriData.createTime, 'YYYY-MM-DD HH:mm:ss').unix()
          if (createTime < data.creation[0] || createTime > data.creation[1]) {
            return false
          }
        }
        return true
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .breadcrumb-container {
    height: 30px;
    line-height: 28px;
    padding-left: 5px;
    margin-left: 100px;
    position: relative;
  }
  .el-breadcrumb {
    width: 90%;
    height: 29px;
    line-height: 28px;
    margin-left: 20px;
    position: absolute;
  }
  >>>.el-breadcrumb__inner a {
    max-width: 200px;
    display: inline-block;
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  >>>.el-breadcrumb__separator {
    vertical-align: top;
  }
  .show-hidden {
    height: 100%;
    float: left;
    .icon {
      height: 100%;
      vertical-align: middle;
      cursor: pointer;
      color: white;
      &:before{
        margin-top: 8px;
        display: inline-block;
      }
    }
  }
  .hidden-item {
    .el-dropdown-menu__item{
      max-width: 200px;
      word-break: keep-all;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .layout-dropdown {
    position: relative;
    float: right;
    margin-left: 10px;
  }
  .highlight-layout {
    background-color: #ecf5ff;
    color: #66b1ff;
  }
</style>
