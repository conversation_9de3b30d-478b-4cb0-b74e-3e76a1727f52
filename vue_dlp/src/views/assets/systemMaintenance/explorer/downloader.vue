<template>
  <download-executor
    v-if="isEnableDownloadManager()"
    float="right"
    :disabled="disabled"
    :append-to-body="appendToBody"
    :show-zip-mode="showed"
    :button-name="downloadBtnLabel"
    :button-title="downloadBtnTitle"
    button-type="primary"
    button-size="mini"
    need-check-address
    :before-download="beforeDownload"
    @download="handleDownload"
  />
  <file-downloader
    v-else
    :disabled="disabled"
    :show-zip-mode="showed"
    :progress-visible.sync="progressVisible"
    :progress-active="downloadFile.active"
    :progress-error="downloadFile.error"
    :progress-percent="downloadFile.percent"
    :progress-steps="progressSteps"
    need-check-address
    :show-address-dialog="false"
    @download="handleDownload"
    @cancel-task="handleCancelTask"
  />
</template>

<script>
import DownloadExecutor from '@/components/DownloadManager/executor'
import FileDownloader from '@/components/FileManager/downloader'
import Cookies from 'js-cookie'
import { formatFileSize } from '@/utils'
// import { countUtf8Byte } from '@/utils/utf8'
import { downloadZeroByteFile } from '@/utils/download/index'
import { getExtractFileConfig } from '@/api/system/configManage/globalConfig'
import { getTerminalDetailById } from '@/api/system/terminalManage/terminal'
import {
  getExtractStatus,
  getExtractStatus4Log,
  getUploadChunks,
  ieDownload,
  notifyUpload,
  notifyUploaded,
  pauseDownload
} from '@/api/assets/systemMaintenance/explorer'
import { buildDownloadFile, isDownloadFileRemovable, DEFAULT_DOWNLOAD_FILE } from '@/utils/download/helper'
import { mapGetters } from 'vuex'

export default {
  name: 'ExplorerDownloader',
  components: { DownloadExecutor, FileDownloader },
  props: {
    appendToBody: { type: Boolean, default: false },
    showZipMode: { type: Boolean, default: true },
    bizType: { type: Number, default: 1 }, // 业务类型：1-资源管理器，2-远程协助
    downloadBtnLabel: {
      type: String,
      default() {
        return this.$t('components.download')
      }
    },
    downloadBtnTitle: {
      type: String,
      default() {
        return this.$t('pages.downloadLongFileName')
      }
    },
    isLog: {
      type: Boolean,
      default: false
    },
    terminalId: {
      type: [Number, String],
      default: undefined
    },
    dir: {
      type: String,
      default: ''
    },
    selection: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      zipped: false,
      fileInfo: {},
      virtualPercentTimer: undefined,
      terminalResponseTimer: undefined,
      downloadFile: { ...DEFAULT_DOWNLOAD_FILE },
      taskId: undefined,
      subscribeSocketRespUrl: undefined,
      subscribeProgressUrl: undefined,
      progressVisible: false,
      finishUpload: false,
      progressSteps: [],
      defaultSteps: `[
        { "title": "${this.$t('pages.serverlog_download_progress1')}" },
        { "title": "${this.$t('pages.serverlog_download_progress2')}" },
        { "title": "${this.$t('pages.serverlog_download_progress3')}" }
      ]`,
      heartbeat: undefined,
      zipTimeout: 5 * 60 * 1000,
      chunkTimeout: 90 * 1000,
      uploadType: {
        upload: 0,
        pause: 1,
        close: 2
      },
      zipErrs: {
        1: this.$t('text.normal'),
        2: this.$t('components.chunkDownload_Msg'),
        3: this.$t('components.chunkDownload_Msg1'),
        4: this.$t('components.chunkDownload_Msg2'),
        5: this.$t('components.chunkDownload_Msg3'),
        6: this.$t('components.chunkDownload_Msg4'),
        7: this.$t('components.chunkDownload_Msg5'),
        8: this.$t('components.chunkDownload_Msg12'),
        9: this.$t('components.chunkDownload_Msg15')
      },
      uploadErrs: {
        1: this.$t('text.normal'),
        2: this.$t('pages.terminalFileUploadErr2'),
        3: this.$t('pages.terminalFileUploadErr3'),
        4: this.$t('pages.terminalFileUploadErr4'),
        5: this.$t('pages.terminalFileUploadErr5'),
        6: this.$t('pages.terminalFileUploadErr6'),
        7: this.$t('pages.terminalFileUploadErr7')
      }
    }
  },
  computed: {
    ...mapGetters(['downloadFiles']),
    disabled() {
      return !this.selection || this.selection.length < 2
    },
    showed() {
      if (this.disabled) {
        return false
      }
      return this.showZipMode && this.selection[0].type === 2
    },
    virtualPercentTimeout() {
      if (this.downloadFile.percent < 20) {
        return 500
      }
      if (this.downloadFile.percent < 35) {
        return 1000
      }
      if (this.downloadFile.percent < 50) {
        return 2000
      }
      if (this.downloadFile.percent < 65) {
        return 3000
      }
      if (this.downloadFile.percent < 80) {
        return 4000
      }
      return 5000
    }
  },
  methods: {
    beforeDownload(zipped) {
      this.zipped = zipped
      const files = this.selection.filter(item => item.type === 2).map(item => item.name)
      const folders = this.selection.filter(item => item.type === 1).map(item => item.name)
      const mode = (files.length > 1 || folders.length > 0) ? 2 : (this.zipped ? 1 : 3)
      const fullPath = this.terminalId + ':' + mode + ':' + this.dir + ':' + this.selection.map(item => item.name).sort().join('|')
      for (let i = 0; i < this.downloadFiles.length; i++) {
        const downloadFile = this.downloadFiles[i]
        if (isDownloadFileRemovable(downloadFile) || downloadFile.error || !('isLog' in downloadFile)) {
          continue
        }
        if (downloadFile.fullPath === fullPath) {
          console.warn('重复下载。。。。。。。。。。。。。。')
          return false
        }
        if (downloadFile.isLog === this.isLog) {
          const topic = this.$t('route.' + (this.isLog ? 'TerminalLog' : 'Explorer'))
          const h = this.$createElement
          this.$message({
            message: h('div', { class: 'el-message__content', style: 'cursor: default;' }, [
              h('div', null, this.$t('components.chunkDownload_Msg13', { topic })),
              h('div', { style: 'color: #909399;' }, this.$t('components.chunkDownload_Msg14', { name: downloadFile.name }))
            ]),
            type: 'warning',
            duration: 3000
          })
          return false
        }
      }
      this.extractData = {
        mode,
        fullPath,
        fileType: (files.length ? 1 : 0) | (folders.length ? 2 : 0),
        files: files.join('|'),
        folders: folders.join('|')
      }
      return true
    },
    handleDownload(zipped) {
      // 文件目录不能超过260个字节
      // if (this.dir > 0 || countUtf8Byte(this.dir) > 260) {
      //   this.$alert(this.$t('components.chunkDownload_Msg10'), this.$t('text.prompt'), {
      //     confirmButtonText: this.$t('button.confirm2'),
      //     type: 'warning'
      //   })
      //   return
      // }
      // socket协议终端最多收到16K，为了方便客户理解，所有字符都按3个字节算，因此最多传5000个字符
      const maxLength = 5 * 1000
      const nameLength = this.selection.map(item => item.name).join().length
      if (nameLength > maxLength) {
        this.$notify({
          title: this.$t('text.warning'),
          message: this.$t('components.overLength_total_msg', { length: nameLength, maxLength: maxLength }),
          type: 'warning',
          duration: 3000
        })
        return
      }
      if (this.isLog) {
        this.extractFile()
      } else {
        this.checkExtractLimit(this.extractFile)
      }
    },
    handleCancelTask(forceCancel) {
      this.downloadFile.canceled = true
      this.cancelTerminalTimoutTips()
      this.stopVirtualPercent()
      if (forceCancel || this.downloadFile.active === 1) {
        pauseDownload({
          terminalId: this.terminalId,
          taskId: this.taskId,
          guid: this.fileInfo.guid,
          type: this.uploadType.close,
          bizType: this.bizType
        })
      }
      this.closeSubscribe()
    },
    async extractFile() {
      this.closeSubscribe()
      this.taskId = this.terminalId + '_' + Cookies.get('sid') + '_' + Date.now()
      this.subscribeTerminalSocketResp()

      this.finishUpload = false
      this.fileInfo = {}
      this.heartbeat = Date.now()
      this.terminalTimeoutTips(this.zipTimeout)
      this.progressSteps = JSON.parse(this.defaultSteps)
      this.showVirtualPercent()
      this.progressVisible = true
      try {
        const extractData = {
          ...this.extractData,
          taskId: this.taskId,
          terminalId: this.terminalId,
          dir: this.dir
        }
        delete this.extractData
        const { data: { name: terminalName }} = await getTerminalDetailById(this.terminalId)
        this.downloadFile = buildDownloadFile(this.selection, extractData.mode < 3, terminalName + '_' + this.terminalId)
        this.downloadFile.isLog = this.isLog
        this.downloadFile.fullPath = extractData.fullPath
        delete extractData.fullPath
        this.downloadFile.steps = 3
        this.downloadFile.abort = () => { this.handleCancelTask() }
        const extractFunc = this.isLog ? getExtractStatus4Log : getExtractStatus
        const resp = await extractFunc(extractData)
        // 上传状态：1 压缩中；2 压缩完成；3 文件上传中；4 文件上传暂停；5 上传完成
        const status = resp.data.uploadStatus
        if (status > 1) {
          const { md5, size, guid } = resp.data
          this.fileInfo = { downloadTaskId: this.taskId, filename: this.downloadFile.name, md5, size, guid }
          if (size === 0 || status === 5) { // 文件大小为0 或 上传完成
            this.downloadRequest()
            return
          }
          this.notifyUploadAndSubscribeProgress()
        }
      } catch (e) {
        this.downloadFile.error = e.message || e
        this.stopVirtualPercent()
      }
    },
    formatUploadErr(errorCode) {
      if (errorCode < 100) {
        return this.uploadErrs[errorCode] || (this.$t('pages.terminalFileUploadErr600') + errorCode)
      }
      if (errorCode > 500) {
        return this.$t('pages.terminalFileUploadErr600') + errorCode
      }
      // 26 - couldn't open/read from file
      // 28 - the timeout time was reached
      // 35 - wrong when connecting with SSL
      const curlCode = errorCode - (errorCode > 300 ? 300 : 100)
      const ipVer = errorCode > 300 ? '[IPv6] ' : '[IPv4] '
      switch (curlCode) {
        case 26:
          return ipVer + this.$t('pages.terminalFileUploadErr26')
        case 28:
          return ipVer + this.$t('pages.terminalFileUploadErr28')
        case 35:
          return ipVer + this.$t('pages.terminalFileUploadErr35')
        default:
          return ipVer + this.$t('pages.terminalFileUploadErr600') + curlCode
      }
    },
    subscribeTerminalSocketResp() {
      this.subscribeSocketRespUrl = '/topic/extractRemoteFile/' + this.taskId
      this.$socket.subscribe({
        url: this.subscribeSocketRespUrl,
        callback: (resp, handle) => {
          if (this.downloadFile.canceled) {
            handle.close()
            this.subscribeSocketRespUrl = undefined
            return
          }
          this.heartbeat = Date.now()
          this.downloadFile.error = false
          const protocol = resp.data.protocol
          const endMark = resp.data.endMark
          switch (protocol) {
            case 0x0202: // 压缩文件回复
              this.stopVirtualPercent()
              if (endMark === 1 || endMark === 5) {
                const { md5, size, guid } = resp.data.fileInfo
                this.fileInfo = { downloadTaskId: this.taskId, filename: this.downloadFile.name, md5, size, guid }
                if (endMark === 5) {
                  this.$confirmBox(this.$t('components.chunkDownload_Msg7', { msg: this.zipErrs[endMark] }), this.$t('text.prompt'))
                    .then(this.notifyUploadAndSubscribeProgress)
                    .catch(() => {
                      this.handleCancelTask(true)
                      this.progressVisible = false
                    })
                } else {
                  if (size === 0) {
                    this.downloadRequest()
                    return
                  }
                  this.notifyUploadAndSubscribeProgress()
                }
              } else {
                this.downloadFile.error = this.zipErrs[endMark]
              }
              break
            case 0x0203: // 通知上传回复
              if (endMark !== 1) {
                this.stopVirtualPercent()
                this.downloadFile.error = this.formatUploadErr(endMark)
              } else if (!this.finishUpload) {
                this.terminalTimeoutTips(this.chunkTimeout)
              }
              break
            default:
              break
          }
        }
      })
    },
    subscribeTerminalUploadProgress() {
      this.subscribeProgressUrl = '/topic/extractFileProgress/' + this.terminalId + '_' + this.fileInfo.md5
      this.$socket.subscribe({
        url: this.subscribeProgressUrl,
        callback: (resp, handle) => {
          if (this.downloadFile.canceled) {
            handle.close()
            this.subscribeProgressUrl = undefined
            return
          }
          this.heartbeat = Date.now()
          this.downloadFile.error = false
          this.terminalTimeoutTips(this.chunkTimeout)

          this.stopVirtualPercent()
          const { chunkIndex, chunkLength, size, finishUpload } = resp.data
          if (finishUpload) {
            this.downloadRequest()
          } else {
            this.downloadFile.percent = Math.max(this.downloadFile.percent, Math.floor((chunkIndex + chunkLength) * 100 / size))
          }
        }
      })
    },
    closeSubscribe() {
      if (this.subscribeSocketRespUrl) {
        this.$socket.unsubscribe(this.subscribeSocketRespUrl)
        this.subscribeSocketRespUrl = undefined
      }
      if (this.subscribeProgressUrl) {
        this.$socket.unsubscribe(this.subscribeProgressUrl)
        this.subscribeProgressUrl = undefined
      }
    },
    notifyUploadAndSubscribeProgress() {
      this.downloadFile.active = 1
      this.downloadFile.percent = 0
      this.stopVirtualPercent()
      this.subscribeTerminalUploadProgress()
      this.notifyChunksUpload()
    },
    notifyChunksUpload() {
      getUploadChunks({
        downloadTaskId: this.taskId,
        terminalId: this.terminalId,
        md5: this.fileInfo.md5
      }).then(respond => {
        const chunks = respond.data
        // 终端未上传
        if (chunks.length === 0) {
          this.notifyUpload()
          return
        }
        // 终端上传完成
        const chunk1 = chunks[0]
        if (chunk1.length === this.fileInfo.size) {
          this.downloadRequest()
          return
        }
        // 终端上传部分
        this.notifyUpload()
      }).catch(() => {
        this.stopVirtualPercent()
      })
    },
    notifyUpload() {
      this.heartbeat = Date.now()
      this.terminalTimeoutTips(this.chunkTimeout)
      return notifyUpload({
        terminalId: this.terminalId,
        taskId: this.taskId,
        type: this.uploadType.upload,
        guid: this.fileInfo.guid,
        chunkIndex: 0,
        chunkLength: this.fileInfo.size,
        bizType: this.bizType
      }).catch(() => {
        this.stopVirtualPercent()
      })
    },
    downloadRequest() {
      if (this.bizType === 2) {
        // 远程控制通知终端文件传输完成
        notifyUploaded(this.terminalId, this.fileInfo.guid)
      }
      this.finishUpload = true
      this.downloadFile.active = 2
      this.cancelTerminalTimoutTips()
      this.stopVirtualPercent()
      if (this.fileInfo.size > 0) {
        this.downloadFile.percent = 0
        ieDownload(this.fileInfo, this.downloadFile)
      } else {
        this.downloadFile.percent = 100
        downloadZeroByteFile(this.fileInfo.filename)
      }
      this.progressVisible = false
    },
    // 文件提取限制校验
    checkExtractLimit(callback) {
      getExtractFileConfig().then(resp => {
        const fileSumSize = (resp.data || []).filter(item => item.key === 'fileSumSize').map(item => parseInt(item.value))[0]
        const sizeCount = this.selection.map(item => item.size).reduce((prev, curr) => prev + curr)
        if (fileSumSize && sizeCount > fileSumSize) {
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('components.overLimit_total_msg', { size: formatFileSize(sizeCount), maxSize: formatFileSize(fileSumSize) }),
            type: 'warning',
            duration: 3000
          })
          return
        }
        callback && callback()
      })
    },
    terminalTimeoutTips(timeout) {
      this.cancelTerminalTimoutTips()
      this.terminalResponseTimer = setTimeout(() => {
        if (!this.terminalResponseTimer) {
          return
        }
        if (isDownloadFileRemovable(this.downloadFile)) {
          this.stopVirtualPercent()
          this.cancelTerminalTimoutTips()
          return
        }
        if (Date.now() - this.heartbeat >= timeout) {
          this.stopVirtualPercent()
          this.downloadFile.error = this.$t('components.chunkDownload_Msg6')
        }
      }, timeout)
    },
    cancelTerminalTimoutTips() {
      if (this.terminalResponseTimer) {
        clearTimeout(this.terminalResponseTimer)
        this.terminalResponseTimer = undefined
      }
    },
    showVirtualPercent() {
      this.stopVirtualPercent()
      this.virtualPercentTimer = setTimeout(() => {
        if (this.virtualPercentTimer && this.downloadFile.percent++ < 100) {
          this.showVirtualPercent()
        }
      }, this.virtualPercentTimeout)
    },
    stopVirtualPercent() {
      if (this.virtualPercentTimer) {
        clearTimeout(this.virtualPercentTimer)
        this.virtualPercentTimer = undefined
      }
    }
  }
}
</script>
