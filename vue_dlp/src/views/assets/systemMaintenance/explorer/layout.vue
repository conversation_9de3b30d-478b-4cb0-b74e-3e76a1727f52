<template>
  <grid-table
    v-if="layout === 'detail'"
    ref="layoutDetail"
    v-loading="loading"
    :col-model="colModel"
    :row-datas="data"
    :default-sort="defaultSort"
    :multi-select="multiSelect"
    :selectable="selectable"
    :show-pager="false"
    :border="false"
    @selectionChangeEnd="handleSelectionChange"
    @row-dblclick="handleDblClick"
    @row-click="handleClick"
  />
  <div
    v-else
    ref="fileContainer"
    v-loading="loading"
    :class="['layout-box', { 'flex-col-wrap': layout === 'list', 'mouse-to-box': moveMouseToBox }]"
    @click="cancelSelectText"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @scroll="handleScroll"
  >
    <div
      v-for="(item, index) in items"
      :key="index"
      :title="calcFileTips(item)"
      :class="['layout-item', `layout-${layout}`, { 'highlight-item': item.highlight && selectable(item), 'highlight-border': item.highlight }]"
      @click="event => handleClick(item, index, event)"
      @dblclick="handleDblClick(item)"
    >
      <span class="layout-item__icon">
        <img v-if="item.icon.imgIcon" class="img-icon" :src="item.icon.src" :alt="item.icon.alt">
        <svg-icon v-else :icon-class="item.icon.class"/>
        <svg-icon v-if="item.showEncryptedIcon" icon-class="lock" class-name="encrypted-icon"/>
      </span>
      <span class="layout-item__info">
        <span class="info-unit file-name">{{ item.label }}</span>
        <template v-if="layout === 'tile'">
          <span class="info-unit file-type">{{ item.fileType }}</span>
          <span v-if="showFileSize" class="info-unit file-size">{{ item.fileSize }}</span>
        </template>
      </span>
    </div>
    <div
      v-if="moveMouseToBox"
      class="selection-box"
      :style="selectionBoxStyle"
    ></div>
  </div>
</template>

<script>
import { fileTypeFormatter } from '@/api/assets/systemMaintenance/explorer'
import { formatFileSize, selectText, cancelSelectText, debounce } from '@/utils'

export default {
  name: 'ExplorerLayout',
  props: {
    layout: {
      type: String,
      default: 'detail'
    },
    loading: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default() {
        return []
      }
    },
    colModel: {
      type: Array,
      default() {
        return []
      }
    },
    selectable: {
      type: Function,
      default() {
        return true
      }
    },
    multiSelect: {
      type: Boolean,
      default() {
        return true
      }
    },
    defaultSort: {
      type: Object,
      default() {
        return { prop: 'id', order: 'desc' }
      }
    },
    iconFormatter: {
      type: Function,
      default() {
        return []
      }
    },
    showEncryptedIcon: {
      type: Boolean,
      default: true
    },
    // 可选择的文件类型
    filterSelectedType: {
      type: Number,
      default: undefined
    },
    // 是否显示文件大小
    showFileSize: {
      type: Boolean,
      default: true
    },
    // 是否支持框选后多选文件
    moveMouseToBox: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      items: [],
      highlightIndex: -1,
      selection: [],
      isMouseDown: false,
      mouseMoving: false,
      scrollTop: 0,
      scrollLeft: 0,
      boxSelectedIndices: [],
      fileContainerRect: {},
      start: { x: 0, y: 0 },
      current: { x: 0, y: 0 }
    }
  },
  computed: {
    boxAttr() {
      if (!this.mouseMoving) { return {} }
      const left = Math.max(Math.min(this.start.x, this.current.x) - (this.fileContainerRect.x || 0), 0)
      const top = Math.max(Math.min(this.start.y, this.current.y) - (this.fileContainerRect.y || 0), 0)
      const width = Math.abs(this.current.x - this.start.x)
      const height = Math.abs(this.current.y - this.start.y)
      const scrollHeight = (this.$refs['fileContainer'] || {}).scrollHeight || 425
      const scrollWidth = (this.$refs['fileContainer'] || {}).scrollWidth || 602
      return { left, top, width: Math.min(width, scrollWidth - left), height: Math.min(height, scrollHeight - top) }
    },
    selectionBoxStyle() {
      if (!this.mouseMoving) { return { display: 'none' } }
      const { left, top, width, height } = this.boxAttr
      return {
        left: `${left}px`,
        top: `${top}px`,
        width: `${width}px`,
        height: `${height}px`
      };
    }
  },
  watch: {
    layout(curVal, oldVal) {
      if (curVal === 'detail') {
        const highlightIds = this.selection.map(item => item.id)
        this.$nextTick(() => {
          const table = this.$refs.layoutDetail
          this.data.forEach(item => {
            table.toggleRowSelection(item, highlightIds.indexOf(item.id) >= 0)
          })
        })
        return
      }
      if (oldVal === 'detail') {
        const highlightIds = this.selection.map(item => item.id)
        this.items.forEach(item => {
          item.highlight = highlightIds.indexOf(item.id) >= 0
        })
      }
    },
    data: {
      deep: true,
      immediate: true,
      handler() {
        this.highlightIndex = -1
        this.items = this.data.map(item => {
          const _item = { ...item, highlight: false }
          _item.icon = this.iconFormatter(item)[0]
          _item.showEncryptedIcon = this.showEncryptedIcon && (item.oriData.attributes & 64) > 0
          const type = item.oriData.type
          _item.fileType = fileTypeFormatter(item, type)
          if (type === 0) {
            _item.fileSize = (item.oriData.size / 1024).toFixed(1) + ' GB'
          } else if (type === 2) {
            _item.fileSize = formatFileSize(item.oriData.size)
          }
          return _item
        }).sort((a, b) => {
          const { prop, order } = this.defaultSort
          const aVal = this.invokeObjectFieldValue(a, prop)
          const bVal = this.invokeObjectFieldValue(b, prop)
          let result
          if (typeof aVal === 'number') {
            result = aVal - bVal
          } else {
            result = aVal.toString().localeCompare(bVal.toString(), 'zh-CN')
          }
          return order === 'desc' ? result : -result
        })
      }
    },
    showEncryptedIcon(val) {
      this.items.forEach(item => {
        item.showEncryptedIcon = val && (item.oriData.attributes & 64) > 0
      })
    },
    moveMouseToBox: {
      handler(val) {
        if (val) {
          document.addEventListener('mousemove', this.handleMouseMove)
        } else {
          document.removeEventListener('mousemove', this.handleMouseMove)
        }
      },
      immediate: true
    }
  },
  methods: {
    cancelSelectText,
    handleClick(data, index, event) {
      if (this.multiSelect) {
        // win系统按Ctrl键，mac系统按command键
        if (event.ctrlKey || event.metaKey) {
          data.highlight = !data.highlight
        } else if (event.shiftKey) {
          const min = Math.min(index, this.highlightIndex)
          const max = Math.max(index, this.highlightIndex)
          this.items.forEach((item, i) => {
            item.highlight = i >= min && i <= max
          })
        } else {
          const selection = this.items.filter(item => item.highlight)
          if (selection.length === 1 && selection[0].id === data.id && data.highlight) {
            const target = event.target
            if (target.className === 'info-unit file-name') {
              selectText(target)
              event.preventDefault()
              event.stopPropagation()
            }
          } else {
            selection.forEach(item => {
              item.highlight = false
            })
            data.highlight = true
          }
        }
        this.highlightIndex = index
        this.emitSelected()
      } else {
        if (!this.selectable || this.selectable(data)) {
          this.$emit('selected', [data])
        }
      }
    },
    handleDblClick(data) {
      this.$emit('dblclick', data)
    },
    handleSelectionChange(selection) {
      this.selection = selection
      this.$emit('selected', this.selection)
    },
    handleMouseDown(event) {
      if (!this.moveMouseToBox) { return }
      this.scrollTop = this.$refs['fileContainer'].scrollTop
      this.scrollLeft = this.$refs['fileContainer'].scrollLeft
      const rect = { x: event.clientX + this.scrollLeft, y: event.clientY + this.scrollTop }
      this.start = { ...rect }
      this.current = { ...rect }
      this.boxSelectedIndices = []
      this.fileContainerRect = this.$refs['fileContainer'].getBoundingClientRect()
      this.isMouseDown = true
    },
    handleMouseMove(event) {
      if (!this.moveMouseToBox || !this.isMouseDown) { return }
      this.current.x = Math.max(event.clientX + this.scrollLeft, this.fileContainerRect.x)
      this.current.y = Math.max(event.clientY + this.scrollTop, this.fileContainerRect.y)
      this.mouseMoving = true
    },
    handleMouseUp(event) {
      this.isMouseDown = false
      if (!this.moveMouseToBox && this.mouseMoving) { return }
      const { left, top, width, height } = this.boxAttr
      const minX = left + (this.fileContainerRect.x || 0) - this.scrollLeft
      const maxX = left + width + (this.fileContainerRect.x || 0) - this.scrollLeft
      const minY = top + (this.fileContainerRect.y || 0) - this.scrollTop
      const maxY = top + height + (this.fileContainerRect.y || 0) - this.scrollTop
      const layoutItems = window.document.getElementsByClassName('layout-item')
      const updateIndexes = []
      for (let i = 0; i < layoutItems.length; i++) {
        const rect = layoutItems[i].getBoundingClientRect()
        if (rect.right > maxX && maxX > rect.left || rect.right <= maxX && rect.right > minX) {
          if (rect.bottom > maxY && maxY > rect.top || rect.bottom <= maxY && rect.bottom > minY) {
            updateIndexes.push(i)
          }
        }
      }
      const isUpdate = updateIndexes.length > 0
      if (isUpdate) {
        for (let i = 0, updateIndex = 0; i < this.items.length; i++) {
          if (!event.ctrlKey && (updateIndex >= updateIndexes.length || i !== updateIndexes[updateIndex])) {
            this.items[i].highlight = false
          } else if (i === updateIndexes[updateIndex]) {
            this.items[i].highlight = event.ctrlKey ? !this.items[i].highlight : true
            updateIndex++
          }
        }
      }
      this.mouseMoving = false
      this.emitSelected()
    },
    handleScroll: debounce(function(event) {
      this.scrollTop = event.target.scrollTop
      this.scrollLeft = event.target.scrollLeft
    }, 10),
    emitSelected() {
      this.selection = this.items.filter(item => item.highlight && (Number.isInteger(this.filterSelectedType) ? item.oriData.type === this.filterSelectedType : true))
      this.$emit('selected', this.selection)
    },
    calcFileTips(data) {
      const type = data.oriData.type
      if (type === 0) {
        return this.$t('pages.explorerLayoutCalcFileTips1', {
          fileType: data.fileType,
          label: data.label,
          size: data.fileSize
        })
      }
      if (type === 1) {
        return this.$t('pages.explorerLayoutCalcFileTips2', {
          label: data.label,
          time: data.oriData.createTime
        })
      }
      return this.$t('pages.explorerLayoutCalcFileTips3', {
        label: data.label,
        fileType: data.fileType,
        size: data.fileSize,
        time: data.oriData.modifyTime
      })
    },
    invokeObjectFieldValue(obj, fieldName) {
      if (fieldName) {
        const fields = fieldName.split('.')
        let val = obj
        for (let i = 0; i < fields.length; i++) {
          if (typeof val === 'object') {
            val = val[fields[i]]
          } else {
            break
          }
        }
        return val
      }
      return obj
    }
  }
}
</script>

<style lang="scss" scoped>
  .layout-item {
    display: inline-block;
    padding: 5px;
    margin: 5px;
    .layout-item__icon {
      display: inline-block;
      width: 50px;
      margin-right: 3px;
      position: relative;
      >>>.img-icon {
        height: 50px;
        position: relative;
        right: 6px;
      }
      >>>.svg-icon {
        width: 50px;
        height: 50px;
      }
      >>>.encrypted-icon {
        position: absolute;
        left: 30px;
        bottom: 5px;
        width: 20px;
        height: 20px;
      }
    }
    .layout-item__info {
      display: inline-block;
      width: 180px;
      vertical-align: top;
      .info-unit {
        height: 18px;
        font-size: 14px;
        display: block;
      }
      .file-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        user-select: text;
      }
      .file-type {
        color: #909399;
      }
      .file-size {
        color: #909399;
      }
    }
  }
  .layout-tile {
    width: 252px;
    height: 62px;
  }
  .layout-icon {
    width: 84px;
    height: 98px;
    .layout-item__icon {
      display: block !important;
      left: 12px;
    }
    .layout-item__info {
      display: inherit !important;
      width: 100%;
      text-align: center;
      .file-name {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-line-break: after-white-space;
        -webkit-box-orient: vertical;
        overflow-wrap: break-word;
        white-space: normal;
        height: 32px !important;
      }
    }
  }
  .layout-list {
    height: 30px;
    margin: 0 5px;
    .layout-item__icon {
      width: 18px !important;
      >>>.img-icon {
        height: auto !important;
        right: 2px !important;
        top: 2px;
      }
      >>>.svg-icon {
        width: 1.1em !important;
        height: 1.1em !important;
      }
      >>>.encrypted-icon {
        font-size: 7px;
        left: 8px !important;
        bottom: 2px !important;
      }
    }
    .layout-item__info {
      width: auto !important;
      vertical-align: middle !important;
      .file-name {
        overflow: visible !important;
      }
    }
  }
  .flex-col-wrap {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
  }
  .mouse-to-box {
    position: relative;
  }
  .selection-box {
    position: absolute;
    border: 1px dashed #007bff;
    pointer-events: none;
  }
</style>
