<template>
  <el-dialog
    v-el-drag-dialog
    :title="$t('pages.extractTerminalProfile')"
    width="416px"
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
  >
    <term-file-extract ref="conf" hide-save/>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="save">
        {{ $t('button.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('button.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import TermFileExtract from '@/components/DownloadManager/setting/termFileExtract'
export default {
  name: 'ExplorerDownloadSetting',
  components: { TermFileExtract },
  data() {
    return {
      visible: false,
      submitting: false
    }
  },
  methods: {
    show() {
      this.$refs.conf && this.$refs.conf.initConfig()
      this.visible = true
    },
    save() {
      this.submitting = true
      this.$refs.conf.updateConfig().then(() => {
        this.visible = false
      }).finally(() => {
        this.submitting = false
      })
    }
  }
}
</script>
