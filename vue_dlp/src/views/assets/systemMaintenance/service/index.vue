<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :os-type-filter="7" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange"/>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" :disabled="refreshDisable" size="mini" @click="refreshService">
          {{ $t('button.refresh') }}
        </el-button>
        <audit-log-exporter v-permission="'154'" :request="handleExport" :disabled="refreshDisable"/>
        <div class="searchCon">
          <el-input
            v-model="searchText"
            v-trim
            clearable
            :placeholder="$t('table.servername')"
            style="width: 160px"
            @keyup.enter.native="handleFilter"
          ></el-input>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleFilter"
          >{{ $t('table.search') }}</el-button>
        </div>
      </div>
      <grid-table
        ref="fileTable"
        v-loading="tableLoading"
        :col-model="colModel"
        :row-datas="rowData"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        :show-pager="false"
      />
    </div>
    <el-dialog
      :title="serverObj.title"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      width="400px"
    >
      <span><i class="el-icon-warning" style="font-size:18px"></i>{{ serverObj.desc }}</span>
      <ul class="server-list">
        <li v-for="(item,index) in serverObj.dependents" :key="index">{{ item.relyServiceDisplayName }}</li>
      </ul>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="btnLoading" @click="stopServers">{{ $t('button.confirm') }}</el-button>
        <el-button @click="handleCancel">{{ $t('button.cancel') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getSystemService, updateSystemService, exportSystemService } from '@/api/assets/systemMaintenance/service'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'SystemService',
  data() {
    return {
      multiSelect: false,
      defaultSort: { prop: 'name' },
      colModel: [
        { prop: 'name', label: 'name', width: '200', fixed: true, sort: true, iconClass: 'service' },
        { prop: 'remark', label: 'fileDescription', width: '200', sort: true },
        { prop: 'state', label: 'status', width: '80', sort: 'custom', formatter: this.stateFormatter },
        { prop: 'runType', label: 'startType', width: '100', sort: 'custom', formatter: this.runTypeFormatter },
        { prop: 'cmd', label: 'commandLine', width: '150', sort: true },
        { prop: 'processId', label: 'processID', width: '150', sort: true, formatter: this.processIdFormatter },
        { prop: 'loginName', label: 'loginIdentity', width: '150', sort: true, formatter: this.loginNameFormatter },
        { prop: 'groupName', label: 'group', width: '150', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '280', fixed: 'right',
          buttons: [
            { label: 'startUp', disabledFormatter: this.startBtnDisabledFormatter, click: this.startService, formatter: (data, btn) => data.btnStartText ? data.btnStartText : this.$t('table.startUp') },
            { label: 'stop', disabledFormatter: this.stopBtnDisabledFormatter, click: this.stopService, formatter: (data, btn) => data.btnStopText ? data.btnStopText : this.$t('table.stop') },
            { label: 'restart', disabledFormatter: this.restartBtnDisabledFormatter, click: this.restartService, formatter: (data, btn) => data.btnRestartText ? data.btnRestartText : this.$t('table.restart') },
            { label: '|', disabled: true },
            { label: 'automatic', disabledFormatter: this.autoRunBtnDisabledFormatter, click: this.toAutoRun },
            { label: 'manual', disabledFormatter: this.handRunBtnDisabledFormatter, click: this.toHandRun },
            { label: 'disable', disabledFormatter: this.disableRunBtnDisabledFormatter, click: this.toDisableRun }
          ]
        }
      ],
      query: {
        serverName: ''
      },
      rowData: [],
      runTypeOptions: {
        2: this.$t('pages.automatic'),
        3: this.$t('pages.manual'),
        4: this.$t('pages.disable')
      },
      stateOptions: {
        1: this.$t('pages.stopped'),
        2: this.$t('pages.starting'),
        3: this.$t('pages.stopping'),
        4: this.$t('pages.started'),
        5: this.$t('pages.recovering'),
        6: this.$t('pages.pausing'),
        7: this.$t('pages.paused')
      },
      curFilePath: [],
      showTree: true,
      deleteable: false,
      temp: {}, // 表单字段
      defaultTemp: {
        name: '',
        id: undefined,
        userId: undefined,
        groupIds: [],
        remark: '',
        user: {
          name: ''
        }
      },
      dialogFormVisible: false,
      submitting: false,
      tableLoading: false,
      dialogVisible: false,
      serverObj: {
        title: '',
        desc: '',
        dependents: [],
        rowObj: {},
        state: undefined
      },
      isLog: true, // 是否记录管理员日志，自动刷新不应该重复记录日志
      refreshDisable: true,
      btnLoading: false,
      ctrlAble: false,
      tempRowData: [],
      searchText: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs['fileTable']
    },
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    }
  },
  watch: {
    tempRowData(val) {
      this.handleFilter()
    }
  },
  created() {
    this.resetTemp()
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('service', termId, [2]).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.refreshDisable = true
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange(tabName, data) {
      if (!data) {
        return
      }
      this.tempRowData = []
      if (data.id.indexOf('G' + data.dataId) < 0) {
        this.refreshDisable = false
        this.checkCtrlAble(data.dataId).then(() => {
          if (!this.ctrlAble) return
          this.clickNode(data)
        })
      } else {
        this.refreshDisable = true
      }
    },
    clickNode(data) {
      const that = this
      this.tableLoading = true
      getSystemService(data.dataId, this.isLog).then(respond => {
        that.tempRowData = [...respond.data]
        this.tableLoading = false
        this.isLog = true
      }).catch(e => {
        if (that.tableLoading) {
          that.tableLoading = false
        }
      })
    },
    handleFilter() {
      const searchText = this.searchText.toLowerCase()
      this.rowData = this.tempRowData.filter(data => {
        const name = data.name.toLowerCase()
        return name.indexOf(searchText) > -1
      })
    },
    handleExport(exportType) {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      return exportSystemService({ exportType, termId: curNodeData.dataId, serviceNameInfo: this.searchText })
    },
    refreshService: function() {
      this.isLog = false
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      this.strategyTargetNodeChange(null, curNodeData)
    },
    startService: function(row) {
      row.btnStartText = this.$t('text.enable1')
      this.updateSystemServiceFunc(row, 1, this.$t('pages.service_Msg1'))
    },
    restartService: function(row) {
      row.btnRestartText = this.$t('text.restarting')
      this.updateSystemServiceFunc(row, 4, this.$t('pages.service_Msg2'))
    },
    stopService: function(row) {
      this.resetServerObj()
      this.$confirmBox(this.$t('pages.service_Msg3'), this.$t('text.prompt')).then(() => {
        row.btnStopText = this.$t('text.stopping')
        const index = this.tempRowData.findIndex(item => item.name === row.name)
        this.tempRowData.splice(index, 1, row)
        this.updateSystemServiceFunc(row, 2, this.$t('pages.service_Msg4'))
      }).catch(() => { })
    },
    toAutoRun: function(row) {
      this.updateSystemServiceFunc(row, 5, this.$t('pages.service_Msg5'))
    },
    toHandRun: function(row) {
      this.updateSystemServiceFunc(row, 6, this.$t('pages.service_Msg6'))
    },
    toDisableRun: function(row) {
      this.$confirmBox(this.$t('pages.service_Msg7'), this.$t('text.prompt')).then(() => {
        this.updateSystemServiceFunc(row, 3, this.$t('pages.service_Msg8'))
      }).catch(() => {})
    },
    resetServerObj() {
      this.serverObj = {
        title: '',
        desc: '',
        dependents: [],
        rowObj: {},
        state: undefined
      }
    },
    updateSystemServiceFunc: function(row, state, sucMsg) {
      this.btnLoading = true
      const terminalNode = this.strategyTargetTree.getCurrentNode()
      const serviceNames = this.serverObj.dependents.map(item => item.relyServiceName)
      updateSystemService({
        termId: terminalNode.dataId,
        serviceNames: [...serviceNames, row.serviceName],
        name: row.name,
        state: state
      }).then(respond => {
        this.btnLoading = false
        if (respond.data.state === 1) {
          this.resetServerObj()
          this.dialogVisible = false
          this.$notify({ title: this.$t('text.success'), message: sucMsg, type: 'success', duration: 2000 })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        } else if (respond.data.state === 0) {
          this.resetServerObj()
          this.dialogVisible = false
          this.$notify({ title: this.$t('text.fail'), message: this.$t('text.updateServiceFail'), type: 'error', duration: 2000 })
          setTimeout(() => {
            this.refreshService()
          }, 1000)
        } else {
          this.dialogVisible = true
          this.serverObj = {
            title: respond.data.state === 2 ? this.$t('pages.service_Msg9') : this.$t('pages.service_Msg10'),
            desc: respond.data.state === 2 ? `${this.$t('pages.service_Msg12', { serviceName: row.name })}` : `${this.$t('pages.service_Msg13', { serviceName: row.name })}`,
            dependents: respond.data.dependents,
            row,
            state
          }
        }
      })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    stateFormatter: function(row, data) {
      return this.stateOptions[data]
    },
    runTypeFormatter: function(row, data) {
      return this.runTypeOptions[data]
    },
    processIdFormatter: function(row, data) {
      if (data == 0) {
        return ''
      } else {
        return data
      }
    },
    loginNameFormatter: function(row, data) {
      if (data === undefined) { // 旧版本终端，25D1版本之前没有登录身份字段
        return data
      } else { // 25D1版本终端
        if (data && data.toLowerCase().indexOf('localservice') > -1) {
          return this.$t('pages.localService')
        } else if (data && data.toLowerCase().indexOf('networkservice') > -1) {
          return this.$t('pages.networkService')
        } else if (data === '' || (data && data.toLowerCase().indexOf('localsystem') > -1)) {
          return this.$t('pages.localSystem')
        } else {
          return data
        }
      }
    },
    startBtnDisabledFormatter: function(data) {
      return data.state === 4 || data.runType === 4 || !!data.btnStartText
    },
    stopBtnDisabledFormatter: function(data) {
      return data.state !== 4 || data.runType === 4 || !!data.btnStopText
    },
    restartBtnDisabledFormatter: function(data) {
      return data.state !== 4 || data.runType === 4 || !!data.btnRestartText
    },
    autoRunBtnDisabledFormatter: function(data) {
      return data.runType === 2
    },
    handRunBtnDisabledFormatter: function(data) {
      return data.runType === 3
    },
    disableRunBtnDisabledFormatter: function(data) {
      return data.runType === 4
    },
    stopServers() {
      this.serverObj.state === 2 ? this.updateSystemServiceFunc(this.serverObj.row, 2, this.$t('pages.service_Msg4')) : this.updateSystemServiceFunc(this.serverObj.row, 4, this.$t('pages.service_Msg2'))
    },
    handleCancel() {
      this.serverObj.row.btnStopText = undefined
      const index = this.tempRowData.findIndex(item => item.name === this.serverObj.row.name)
      this.tempRowData.splice(index, 1, this.serverObj.row)
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.server-list{
  height: 160px;
  border: 1px solid #999;
  padding: 10px 0 0 25px;
  margin: 10px 0 0 0;
  li{
    margin-bottom: 10px;
  }
}
</style>
