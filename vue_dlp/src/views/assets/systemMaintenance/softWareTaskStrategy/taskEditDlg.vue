<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.createUninstallTask')"
      :visible.sync="dialogFormVisible"
      width="800px"
      @closed="closed"
    >
      <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="width: 740px;">
        <el-divider content-position="left">{{ $t('pages.baseInfo') }}</el-divider>
        <el-row>
          <el-col :span="12">
            <FormItem :label="$t('pages.uninstallNum')" prop="oneTime">
              <el-select v-model="temp.oneTime">
                <el-option :label="$t('pages.uninstallNum1')" :value="1"></el-option>
                <el-option :label="$t('pages.uninstallNum2')" :value="0"></el-option>
              </el-select>
            </FormItem>
          </el-col>
          <el-col :span="12">
            <FormItem :label="$t('pages.deadline')" prop="name">
              <el-date-picker
                v-model="temp.endTime"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                :clearable="false"
                :picker-options="pickerOptions"
                :placeholder="$t('pages.selectDateTime')"
                popper-class="no-atTheMoment"
                style="width: 100%"
              >
              </el-date-picker>
            </FormItem>
          </el-col>
        </el-row>
        <FormItem :label="$t('pages.effectiveObject')" prop="objectIds">
          <tree-select
            ref="objectTree"
            node-key="id"
            :height="350"
            :width="468"
            multiple
            check-strictly
            collapse-tags
            is-filter
            :filter-key="{ prop: 'dataType', value: '3', showIfNoProp: true }"
            :local-search="false"
            leaf-key="terminal"
            @change="checkedIdChange"
          />
        </FormItem>
        <FormItem :label="$t('pages.remark')" prop="remark">
          <el-input v-model="temp.remark" maxlength="100"></el-input>
        </FormItem>
        <el-divider content-position="left">{{ $t('pages.uninstallSoftware') }}</el-divider>
        <div class="strategyCon">
          <grid-table
            ref="softwareTable"
            :show-pager="false"
            :multi-select="false"
            :col-model="colModel"
            :row-datas="softwareDatas"
            :height="250"
          />
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.softwareVersionList')"
      :visible.sync="verTreeMenuDlgVisible"
      width="400px"
    >
      <tree-menu
        ref="softwareAssetVersionList"
        :data="softwareAssetVersionList"
        :is-filter="false"
        multiple
        check-on-click-node
        default-expand-all
        style="height: 350px"
      />
      <div slot="footer" class="dialog-footer">
        <el-button v-show="softwareAssetVersionList.length > 0" type="primary" size="mini" @click="handleCheckedVersion">{{ $t('button.confirm') }}</el-button>
        <el-button size="mini" @click="verTreeMenuDlgVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="i18nConcatText(this.$t('pages.softwareVersion'), 'update')"
      :visible.sync="verDlgVisible"
      width="600px"
    >
      <Form ref="itemForm" :rules="rules" :model="tempItem" label-position="right" label-width="70px">
        <FormItem :label="$t('pages.softwareName')">
          {{ tempItem.softwareName }}
        </FormItem>
        <FormItem :label="$t('pages.softwareVersion')">
          <tag :list="tempItem.softwareVersions" border style="background-color:#f5f5f5;" :add-click="addClick" @tagChange="closeChange"/>
        </FormItem>
        <span v-if="showErrorVerMsg" style="color:red;margin-left: 70px;font-size: 12px;">{{ $t('pages.pleaseSetAtLeastOneVersion') }}</span>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="fullVersions()">{{ $t('pages.software_Msg39') }}</el-button>
        <el-button type="primary" @click="scanVersions()">
          {{ $t('pages.software_Msg46') }}
          <el-tooltip effect="dark" placement="bottom-start">
            <div slot="content">
              {{ $t('pages.softwareNameGetInstalledVersionNumber') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button>
        <el-button :loading="submitting" type="primary" @click="updateItem()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="verDlgVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { saveStrategy } from '@/api/assets/systemMaintenance/softWareTaskStrategy'
import { listVersion } from '@/api/softwareManage/assets/assetsView'
import { parseTime } from '@/utils'
import moment from 'moment'

export default {
  name: 'TaskEditDlg',
  props: { },
  data() {
    return {
      colModel: [
        { prop: 'softwareName', label: 'softwareName', width: '150', sort: true },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '150', sort: true, formatter: this.softVerFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', click: this.handleUpdateItem },
            { label: 'delete', click: this.handleDeleteItem }
          ]
        }
      ],
      rules: {
        oneTime: [
          { required: true, message: this.$t('pages.uninstallManage_text2'), trigger: 'blur' }
        ],
        taskValidEndTime: [
          { required: true, message: this.$t('pages.uninstallManage_text3'), trigger: 'blur' }
        ],
        objectIds: [
          { required: true, validator: this.objectIdsValidator, trigger: 'change' }
        ],
        name: [
          { required: true, validator: this.endTimeValidator, trigger: 'change' }
        ]
      },
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        endTime: new Date(),
        softWareTasks: [],
        oneTime: 1,
        remark: '',
        active: true,
        strategyDefType: 0,
        objectType: 3,
        objectIds: [],
        objectGroupIds: []
      },
      tempItem: {
        id: undefined,
        softwareVersions: [],
        softwareName: ''
      },
      pickerOptions: {
        selectableRange: '00:00:00 - 23:59:59',
        disabledDate: (date) => {
          const curDate = moment(new Date()).format('YYYY-MM-DD')
          const selectDate = moment(new Date(date)).format('YYYY-MM-DD')
          return selectDate < curDate
        }
      },
      softWareTask: {},
      softwareDatas: [],
      selectedTerminalData: [],
      dialogFormVisible: false,
      verDlgVisible: false,
      verTreeMenuDlgVisible: false,
      showErrorVerMsg: false,
      submitting: false,
      softwareAssetVersionList: [],
      tempEndDate: ''
    }
  },
  computed: {

  },
  watch: {
    'temp.endTime'(val) {
      if (!val) return
      const curDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss').split(' ')
      const endTime = moment(new Date(val)).format('YYYY-MM-DD HH:mm:ss').split(' ')
      if (endTime[0] != this.tempEndDate) {
        // 解决切换日期时，时间始终选择为：23:59:59
        this.tempEndDate = endTime[0]
        if (moment(new Date(val)).format('YYYY-MM-DD HH:mm:ss') !== this.temp.taskValidEndTime) {
          this.temp.endTime = endTime[0] + ' 23:59:59'
        }
        if (curDate[0] != endTime[0]) {
          this.$set(this.pickerOptions, 'selectableRange', '00:00:00 - 23:59:59')
        } else {
          this.$set(this.pickerOptions, 'selectableRange', `${curDate[1]} - 23:59:59`)
        }
        return
      }
      if (curDate[0] == endTime[0]) {
        this.temp.endTime = curDate > endTime ? curDate.join(' ') : endTime.join(' ')
        this.$set(this.pickerOptions, 'selectableRange', `${curDate[1]} - 23:59:59`)
      } else {
        if (this.pickerOptions.selectableRange != '00:00:00 - 23:59:59') {
          this.temp.endTime = endTime[0] + ' 00:00:00'
          this.$set(this.pickerOptions, 'selectableRange', '00:00:00 - 23:59:59')
        }
      }
    }
  },
  created() {
  },
  methods: {
    gridTable() {
      return this.$refs['softwareTable']
    },
    objectTree: function() {
      return this.$refs['objectTree']
    },
    closed() {
      this.resetTemp()
    },
    resetTemp() {
      this.tempEndDate = ''
      this.temp = Object.assign({}, this.defaultTemp)
      const date = new Date()
      date.setDate(date.getDate() + 1)
      date.setHours(23)
      date.setMinutes(59)
      date.setSeconds(59)
      this.temp.endTime = date
      this.tempEndDate = this.temp.endTime.getFullYear() + '-' + ('0' + (this.temp.endTime.getMonth() + 1)).slice(-2) + '-' + ('0' + this.temp.endTime.getDate()).slice(-2)
      this.selectedTerminalData.splice(0)
      if (this.objectTree()) {
        this.objectTree().clearSelectedNode()
      }
    },
    show(softs) {
      this.resetTemp()
      this.dialogFormVisible = true
      this.softwareDatas = this.formatSoftVersion(softs)
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    formatSoftVersion(softs) {
      const result = {}
      softs.forEach(item => {
        if (item.softwareVersions) {
          item.softwareVersions = item.softwareVersions.filter(version => {
            return version
          })
        }
        if (!item.softwareVersions || item.softwareVersions.length === 0) {
          item.softwareVersions = item.softwareVersion ? item.softwareVersion.split(',') : [this.$t('pages.software_Msg39')] // ['全版本']
        }
        if (!result[item.softwareName]) {
          result[item.softwareName] = JSON.parse(JSON.stringify(item))
        } else {
          const resultVers = result[item.softwareName].softwareVersions
          item.softwareVersions.forEach(ver => {
            if (resultVers.indexOf(ver) < 0) {
              resultVers.push(ver)
            }
          })
        }
      })
      return Object.values(result).sort((a, b) => a.softwareName > b.softwareName)
    },
    checkedIdChange(keys, options) {
      this.selectedTerminalData.splice(0, this.selectedTerminalData.length, ...options)
    },
    resetSoftWareTask() {
      this.softWareTask = {
        guid: undefined,
        objectName: '',
        objectProperty: '',
        taskType: 4,
        installParam: '',
        remark: '',
        exceptKey: '',
        taskValidEndTime: ''
      }
    },
    formatSubmitData() {
      const temp = this.temp
      let time = new Date().getTime()
      temp.softWareTasks = []
      this.softwareDatas.forEach(data => {
        data.softwareVersions.forEach(ver => {
          this.resetSoftWareTask()
          this.softWareTask.objectName = data.softwareName
          this.softWareTask.objectProperty = ver === this.$t('pages.software_Msg39') ? '*.*' : ver
          this.softWareTask.oneTime = this.temp.oneTime
          this.softWareTask.taskValidEndTime = parseTime(this.temp.endTime, 'y-m-d h:i:s')
          this.softWareTask.remark = this.temp.remark
          this.softWareTask.guid = time
          time = time + 1
          temp.softWareTasks.push(this.softWareTask)
        })
      })
      temp.objectIds = []
      temp.objectGroupIds = []
      this.selectedTerminalData.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          temp.objectIds.push(nodeData.dataId)
        } else {
          temp.objectGroupIds.push(nodeData.dataId)
        }
      })
    },
    validateTable() {
      const rows = this.gridTable().getDatas()
      if (!rows || rows.length == 0) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.software_Msg48'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        // this.dialogFormVisible = false
        return false
      }
      return true
    },
    validEndTime() {
      if (new Date(this.temp.endTime) < new Date()) {
        this.$notify({
          title: this.$t('text.fail'),
          message: this.$t('pages.deadline_error'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return false
      }
      return true
    },
    saveData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.validateTable() && this.validEndTime()) {
          this.formatSubmitData()
          saveStrategy(this.temp).then(resp => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$emit('submitEnd', resp.data)
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(reason => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleUpdateItem(row) {
      this.showErrorVerMsg = false
      this.verDlgVisible = true
      this.tempItem.oriData = row
      this.tempItem.softwareName = row.softwareName
      this.tempItem.softwareVersions.splice(0)
      this.tempItem.softwareVersions.splice(0, 0, ...row.softwareVersions)
    },
    handleDeleteItem(row) {
      for (let i = 0; i < this.softwareDatas.length; i++) {
        if (this.softwareDatas[i].id === row.id) {
          this.softwareDatas.splice(i, 1)
        }
      }
    },
    addClick() {
      if (this.tempItem.softwareVersions.length === 1 && this.tempItem.softwareVersions[0] === this.$t('pages.software_Msg39')) {
        this.tempItem.softwareVersions.splice(0, 1)
      }
    },
    closeChange(list) {
      if (list.length === 0) {
        this.fullVersions()
      }
    },
    handleCheckedVersion() {
      const checkNodes = this.$refs.softwareAssetVersionList.$refs.tree.getCheckedNodes()
      if (checkNodes && checkNodes.length > 0) {
        if (this.tempItem.softwareVersions.length === 1 && this.tempItem.softwareVersions[0] === this.$t('pages.software_Msg39')) {
          this.tempItem.softwareVersions.splice(0)
        }
        checkNodes.forEach(nodeData => {
          if (this.tempItem.softwareVersions.indexOf(nodeData.label) < 0) {
            this.tempItem.softwareVersions.push(nodeData.label)
          }
        })
      }
      this.verTreeMenuDlgVisible = false
      this.showErrorVerMsg = false
    },
    scanVersions() {
      this.$refs.softwareAssetVersionList && this.$refs.softwareAssetVersionList.clearSelectedNode()
      this.verTreeMenuDlgVisible = true
      this.softwareAssetVersionList.splice(0)
      listVersion({ softName: this.tempItem.softwareName }).then(res => {
        if (res.data && res.data.length > 0) {
          res.data.forEach((item, index) => {
            if (item) {
              const verNode = {
                id: index + 1,
                label: item
              }
              this.softwareAssetVersionList.push(verNode)
            }
          })
        } else {
          this.verTreeMenuDlgVisible = false
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.software_Msg51'),
            type: 'warning',
            duration: 2000
          })
        }
      }).catch(() => {
        this.verTreeMenuDlgVisible = false
      })
    },
    fullVersions() {
      this.showErrorVerMsg = false
      this.tempItem.softwareVersions = [this.$t('pages.software_Msg39')] // ['全版本']
    },
    updateItem() {
      this.showErrorVerMsg = false
      if (this.tempItem.softwareVersions.length === 0) {
        this.showErrorVerMsg = true
        return
      }
      this.tempItem.oriData.softwareVersions.splice(0)
      this.tempItem.oriData.softwareVersions.splice(0, 0, ...this.tempItem.softwareVersions)
      this.gridTable().updateRowData(this.tempItem.oriData)
      this.verDlgVisible = false
    },
    softVerFormatter(row, data) {
      if (row.softwareVersions) {
        return row.softwareVersions.join(' , ')
      }
      return row.softwareVersion
    },
    objectIdsValidator(rule, value, callback) {
      this.$nextTick(() => {
        if (!this.selectedTerminalData || this.selectedTerminalData.length === 0) {
          callback(new Error(this.$t('components.chooseApplicationObj')))
        } else {
          callback()
        }
      })
    },
    endTimeValidator(rule, value, callback) {
      this.$nextTick(() => {
        if (this.temp.endTime == undefined || this.temp.endTime == null || this.temp.endTime == '') {
          callback(new Error(this.$t('pages.deadline_no_null')))
        } else {
          callback()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
