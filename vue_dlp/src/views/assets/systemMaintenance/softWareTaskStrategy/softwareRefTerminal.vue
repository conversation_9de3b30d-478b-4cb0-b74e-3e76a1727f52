<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button size="mini" :disabled="btnDisable" @click="handleCreate">
          {{ $t('pages.createUninstallTask') }}
        </el-button>
        <strategy-extend :scope="1"/>
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.uninstallManage_text9')" style="width: 200px;" @keyup.enter.native="handleFilter"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="softwareTable"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        is-saved-selected
        row-key="softwareName"
        saved-selected-prop="softwareName"
        :default-sort="{ prop: 'value' }"
        retain-pages
        style="-webkit-box-flex: 3; -ms-flex-positive: 3; flex-grow: 3; margin-bottom: 10px;"
        @selectionChangeEnd="selectionChangeEnd"
        @currentChange="currentChangeEnd"
      />
      <grid-table
        ref="terminalTable"
        :multi-select="false"
        :col-model="colModel1"
        :row-data-api="termRowDataApi"
        :hide-on-single-page="true"
        style="-webkit-box-flex: 2; -ms-flex-positive: 2; flex-grow: 2;"
      />
    </div>
    <task-edit-dlg ref="taskEditDlg" @submitEnd="taskEditSubmitEnd"/>
  </div>
</template>

<script>
import {
  getSoftPageGroupByName, countSoftGroupByName, listSoftVerInfo, getTermPageBySoftNameVer
} from '@/api/softwareManage/assets/assetsView'
import { termStatusIconFormatter } from '@/utils/formatter'
import TaskEditDlg from './taskEditDlg'

export default {
  name: 'SoftwareRefTerminal',
  components: { TaskEditDlg },
  props: {
    isShow: { type: Boolean, default: false }  // 是否处于显示状态
  },
  data() {
    return {
      multiSelect: false,
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { prop: 'softwareName', label: 'softwareName', width: '150', sort: true },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '150' }
      ],
      colModel1: [
        { prop: 'name', label: 'terminalName', width: '150', fixed: true, sort: 'custom', iconFormatter: termStatusIconFormatter },
        { prop: 'computerName', label: 'computerName', width: '150' },
        { prop: 'id', label: 'terminalCode', width: '150' },
        { prop: 'ips', label: 'ip', width: '150' },
        { prop: 'macs', label: 'macAddr', width: '150' },
        { prop: 'newVersion', label: 'terminalVersion', width: '150', formatter: this.versionFormatter }
      ],
      query: { // 查询条件
        page: 1,
        assetType: 2004, // 软件卸载策略仅对应用软件生效
        objectType: undefined,
        objectId: undefined,
        originalSearch: true,
        searchInfo: ''
      },
      btnDisable: true,
      tableLoading: false
    }
  },
  computed: {
  },
  watch: {
    isShow(val) {
      val && this.gridTable() && this.gridTable().execRowDataApi()
    }
  },
  created() {
    // this.loadTableColumn()
  },
  methods: {
    // countSoftGroupByName,
    selectionChangeEnd: function(rowDatas) {
      this.$nextTick(() => {
        this.btnDisable = !rowDatas || rowDatas.length == 0
      })
    },
    currentChangeEnd: function(rowData) {
      this.clickSoftName = rowData.softwareName
      this.terminalTable().execRowDataApi()
    },
    gridTable() {
      return this.$refs['softwareTable']
    },
    terminalTable() {
      return this.$refs['terminalTable']
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
      this.gridTable().setCurrentRow(null)
      this.terminalTable().clearRowData()
    },
    rowDataApi: function(option) {
      if (!this.isShow) {
        return new Promise((resolve, reject) => {
          resolve({ code: 20000, data: { total: 0, items: [] }})
        })
      }
      const searchQuery = Object.assign({}, this.query, option)
      if (option.searchCount === false) {
        return getSoftPageGroupByName(searchQuery)
      }
      return this.rowTotalApi(searchQuery)
    },
    async rowTotalApi(option) {
      const result = {}
      await countSoftGroupByName(option).then(resp => {
        result.total = resp.data
      })
      await getSoftPageGroupByName(option).then(resp => {
        if (resp.data) {
          resp.data.total = result.total
        }
        Object.assign(result, resp);
      })
      return result
    },
    tableAfterLoad(rowData, grid) {
      const softNameMap = {}
      rowData.forEach(item => {
        softNameMap[item.softwareName] = item
      })
      const softNameStr = Object.keys(softNameMap).join('@#$')
      if (softNameStr && this.gridTable()) {
        listSoftVerInfo({ softwareNames: softNameStr }).then(resp => {
          resp.data.forEach(item => {
            const softInfo = softNameMap[item.softwareName]
            if (softInfo) {
              this.gridTable().updateRowData(Object.assign(softInfo, item))
            }
          })
        })
      }
    },
    tableSelectable(row, index) {
      return row.softwareVersions
    },
    termRowDataApi(option) {
      if (!this.clickSoftName) {
        return new Promise((resolve, reject) => {
          resolve({ code: 20000, data: { total: 0, items: [] }})
        })
      }
      const searchQuery = Object.assign({}, option, { originalName: this.clickSoftName })
      return getTermPageBySoftNameVer(searchQuery)
    },
    handleCreate() {
      const selectedRowDatas = this.gridTable().getSelectedDatas()
      this.$refs['taskEditDlg'].show(selectedRowDatas);
    },
    handleRefresh() {
      this.gridTable().execRowDataApi(this.query)
      this.gridTable().clearSelection()
      this.gridTable().setCurrentRow(null)
      this.terminalTable().clearRowData()
    },
    taskEditSubmitEnd(data) {
      this.gridTable().selectedDatasDelete()
    },
    versionFormatter: function(row, data) {
      if (!data) {
        return row.version
      } else {
        return data
      }
    },
    softVerFormatter(row, data) {
      if (row.softwareVersions) {
        return row.softwareVersions.join(' , ')
      }
      return row.softwareVersion
    },
    softSizeFormatter(row, data) {
      return Number.isInteger(row.softSize) ? row.softSize : ''
    },
    softVerLoadingFormatter(row, btn) {
      return !row.softwareVersions
    },
    installedNumFormatter(row, data) {
      if (row.terminalIds) {
        return row.terminalIds.split(',').length
      }
      return 0
    },
    softSizeLoadingFormatter(row, btn) {
      return !Number.isInteger(row.softSize)
    }
  }
}
</script>
