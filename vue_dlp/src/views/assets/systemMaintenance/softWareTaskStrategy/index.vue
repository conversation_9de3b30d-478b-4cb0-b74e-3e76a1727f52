<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" :before-leave="changeTab" @tab-click="tabClick">
      <!-- 卸载任务 -->
      <el-tab-pane :label="$t('pages.uninstallTask')" name="TaskList">
        <Task-list ref="TaskList" :limit-type="1" :is-show="isTaskShow"></Task-list>
      </el-tab-pane>
      <!-- 计算机模式 -->
      <el-tab-pane :label="$t('pages.computerMode')" name="TerminalRefSoftware">
        <Terminal-ref-software ref="TerminalRefSoftware" :limit-type="1" :is-show="isTermShow"></Terminal-ref-software>
      </el-tab-pane>
      <!-- 软件模式 -->
      <el-tab-pane :label="$t('pages.softwareModule')" name="SoftwareRefTerminal">
        <Software-ref-terminal ref="SoftwareRefTerminal" :limit-type="2" :is-show="isSoftwareShow"></Software-ref-terminal>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import TaskList from './taskList'
import TerminalRefSoftware from './terminalRefSoftware'
import SoftwareRefTerminal from './softwareRefTerminal'

export default {
  name: 'SoftwareTaskStrategy',
  components: { TaskList, TerminalRefSoftware, SoftwareRefTerminal },
  data() {
    return {
      activeName: 'TaskList',
      isTaskShow: true,
      isSoftwareShow: false,
      isTermShow: false
    }
  },
  computed: {
  },
  watch: {
    activeName(val) {
      this.changeShowStatus(val)
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    tabClick(pane, event) {
      const that = this
      setTimeout(function() {
        if (that.activeName == 'TaskList') {
          pane.$children[0].$refs.strategyListTable.execRowDataApi()
        }
      }, 0)
    },
    changeTab(activeName, oldActiveName) {
      this.activeName = activeName
    },
    changeShowStatus(val) {
      this.isTaskShow = val === 'TaskList'
      this.isSoftwareShow = val === 'SoftwareRefTerminal'
      this.isTermShow = val === 'TerminalRefSoftware'
    }
  }
}
</script>

<style lang='scss' scoped>
  .module-form{
    margin-left: 210px;
    height: 100%;
    overflow: auto;
    .el-tabs{
      height: calc(100% - 40px);
    }
    .el-tab-pane{
      padding: 0 10px 10px;
    }
  }
  .app-container .tree-container.hidden+.module-form{
    margin-left: 0;
  }
  .app-container{
    padding: 10px 15px;
  }
</style>
