<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" :os-type-filter="7" @data-change="strategyTargetNodeChange" />
    </div>

    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.deleteTask') }}
        </el-button>
        <strategy-extend ref="stgExtend" :stg-type="98" :scope="1"/>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.uninstallManage_text8')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyListTable" :default-sort="defaultSort" :col-model="colModel" :row-data-api="rowDataApi" :selectable="selectable" :after-load="afterLoad" @selectionChangeEnd="selectionChangeEnd" />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="i18nConcatText(this.$t('pages.uninstallTask'), 'update', formable)"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <Form ref="dataForm" label-position="right" label-width="100px" style="width: 700px; margin-left:30px;">
        <div v-if="formable" style="float:right;margin: 0 0 5px;">
          <!--<el-button @click="restore">{{ $t('button.restore') }}</el-button>
          <el-button :disabled="!taskDeleteable" @click="deleteTask">{{ $t('button.delete') }}</el-button>-->
          <data-editor
            :formable="formable"
            :popover-width="500"
            :append-to-body="true"
            placement="bottom-end"
            :updateable="configEditable"
            :deletable="configDeleteable"
            :add-func="createConfig"
            :update-func="updateConfig"
            :delete-func="deleteConfig"
            :cancel-func="cancelConfig"
            :before-add="beforeCreateConfig"
            :before-update="beforeupdateConfig"
          >
            <Form ref="configForm" :model="tempC" :rules="tempCRules" label-position="right" label-width="90px" style="width: 430px;">
              <FormItem :label="$t('pages.softwareName')" prop="objectName">
                <el-tooltip v-if="!isEditMode" slot="tooltip" effect="dark" placement="bottom-start">
                  <div slot="content">
                    <i18n path="pages.softwareOrderSoftwareNameTips">
                      <br slot="br"/>
                    </i18n>
                  </div>
                  <i class="el-icon-question" />
                </el-tooltip>
                <el-select
                  v-model="tempC.objectName"
                  clearable
                  filterable
                  remote
                  allow-create
                  default-first-option
                  :disabled="isEditMode"
                  :remote-method="loadSoftInfo"
                  :loading="loadingSelect"
                  style="width: 100%;"
                  @change="softSelectChange"
                >
                  <el-option v-for="item in softOptions" :key="item" :label="item" :value="item"/>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.softwareVersion')" prop="objectProperty">
                <el-input v-model="tempC.objectProperty" maxlength="100" style="width: 70%;"/>
                <el-button size="small" type="primary" :disabled="!tempC.objectName" @click="scanVersions()">
                  {{ $t('pages.software_Msg46') }}
                  <el-tooltip effect="dark" placement="bottom-start">
                    <div slot="content">
                      {{ $t('pages.softwareNameGetInstalledVersionNumber') }}
                    </div>
                    <i class="el-icon-info" />
                  </el-tooltip>
                </el-button>
              </FormItem>
              <FormItem :label="$t('pages.uninstallNum')" prop="oneTime">
                <el-select v-model="tempC.oneTime" style="width: 100%;">
                  <el-option :label="$t('pages.uninstallNum1')" :value="1"></el-option>
                  <el-option :label="$t('pages.uninstallNum2')" :value="0"></el-option>
                </el-select>
              </FormItem>
              <FormItem :label="$t('pages.deadline')" prop="endTime">
                <el-date-picker
                  v-model="tempC.endTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :clearable="false"
                  :picker-options="pickerOptions"
                  :placeholder="$t('pages.selectDateTime')"
                  popper-class="no-atTheMoment"
                  style="width: 100%"
                >
                </el-date-picker>
              </FormItem>
              <FormItem :label="$t('table.remark')" prop="remark">
                <el-input v-model="tempC.remark" type="textarea" maxlength="100"/>
              </FormItem>
            </Form>
          </data-editor>
        </div>
        <grid-table ref="taskList" :height="350" :default-sort="defaultSort" :col-model="colModel1" row-key="guid" :row-datas="rowDatas" :show-pager="false" @selectionChangeEnd="taskSelectionChangeEnd"/>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="formable" :loading="submitting" type="primary" @click="updateData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      append-to-body
      :title="$t('pages.softwareVersionList')"
      :visible.sync="verTreeMenuDlgVisible"
      width="400px"
    >
      <tree-menu ref="softwareAssetVersionList" style="height: 350px" :data="softwareAssetVersionList" :is-filter="false" :default-expand-all="true"/>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="fullVersions"> {{ $t('pages.software_Msg39') }}</el-button>
        <el-button v-show="softwareAssetVersionList.length > 0" type="primary" size="mini" @click="handleCheckedVersion">{{ $t('button.confirm') }}</el-button>
        <el-button size="mini" @click="verTreeMenuDlgVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getStrategyList, deleteStrategy, updateStrategy } from '@/api/assets/systemMaintenance/softWareTaskStrategy'
import { enableStgBtn, enableStgDelete, selectable, hiddenActiveAndEntity, objectFormatter, entityLink, refreshPage, parseTime, buttonFormatter } from '@/utils'
import { listSoftName, listVersion } from '@/api/softwareManage/assets/assetsView'
import { stgEntityIconFormatter } from '@/utils/formatter'
import moment from 'moment'

export default {
  name: 'TaskList',
  props: {
    listable: { type: Boolean, default: true },   // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    formable: { type: Boolean, default: true }    // 能否提交表单
  },
  data() {
    return {
      defaultSort: { prop: 'name', order: 'desc' },
      colModel: [
        { prop: 'entityName', label: 'source', type: 'button', width: '100', iconFormatter: (row) => stgEntityIconFormatter(row, this),
          buttons: [
            { formatter: this.entityFormatter, click: this.entityClick }
          ]
        },
        { label: 'uninstallSoftware', width: '200', formatter: this.assertInfoFormatter },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      colModel1: [
        { prop: 'objectName', label: 'softwareName', fixedWidth: '150', sort: true },
        { prop: 'objectProperty', label: 'softwareVersion', fixedWidth: '150', sort: true, formatter: this.softVerFormatter },
        { prop: 'oneTime', label: 'uninstallNum', fixedWidth: '150', formatter: this.oneTimeFormatter },
        { prop: 'taskValidEndTime', label: 'taskValidEndTime', width: '150', formatter: this.timeFormatter }
      ],
      oneTimeOptions: {
        0: this.$t('pages.uninstallNum2'),
        1: this.$t('pages.uninstallNum1')
      },
      rowDatas: [],
      defaultRowDatas: [],
      disableAllMode: 3,
      urlCheckedKeys: [],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      treeable: true,
      showTree: true,
      taskDeleteable: false,
      deleteable: false,
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        endTime: new Date(),
        softWareTasks: [],
        oneTime: 1,
        remark: '',
        active: true,
        strategyDefType: 0,
        objectIds: [],
        objectGroupIds: []
      },
      dialogFormVisible: false,
      submitting: false,
      urlTreeData: [],
      taskNameSuffix: '卸载任务',
      tempC: {},
      defaultTempC: {
        guid: undefined,
        objectName: '',
        objectProperty: this.$t('pages.software_Msg39'),
        oneTime: 1,
        taskType: 4,
        taskValidEndTime: '',
        endTime: new Date(),
        exceptKey: '',
        installParam: '',
        remark: ''
      },
      isEditMode: false,
      loadingSelect: false,
      softOptions: [],
      pickerOptions: {
        selectableRange: '00:00:00 - 23:59:59',
        disabledDate: (date) => {
          const curDate = moment(new Date()).format('YYYY-MM-DD')
          const selectDate = moment(new Date(date)).format('YYYY-MM-DD')
          return selectDate < curDate
        }
      },
      verTreeMenuDlgVisible: false,
      softwareAssetVersionList: [],
      tempCRules: {
        objectName: [
          { required: true, message: this.$t('pages.inputSoftName'), trigger: 'change' }
        ],
        objectProperty: [
          { required: true, message: this.$t('pages.inputSoftVersion'), trigger: 'blur' }
        ],
        oneTime: [
          { required: true, message: this.$t('pages.uninstallManage_text2'), trigger: 'blur' }
        ]
      },
      configEditable: false,
      configDeleteable: false,
      tempEndDate: ''
    }
  },
  computed: {
  },
  watch: {
    'tempC.endTime'(val) {
      const curDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss').split(' ')
      const endTime = moment(new Date(val)).format('YYYY-MM-DD HH:mm:ss').split(' ')
      if (endTime[0] != this.tempEndDate) {
        // 解决切换日期时，时间始终选择为：23:59:59
        this.tempEndDate = endTime[0]
        if (moment(new Date(val)).format('YYYY-MM-DD HH:mm:ss') !== this.tempC.taskValidEndTime) {
          this.tempC.endTime = endTime[0] + ' 23:59:59'
        }
        if (curDate[0] != endTime[0]) {
          this.$set(this.pickerOptions, 'selectableRange', '00:00:00 - 23:59:59')
        } else {
          this.$set(this.pickerOptions, 'selectableRange', `${curDate[1]} - 23:59:59`)
        }
        return
      }
      if (curDate[0] == endTime[0]) {
        this.tempC.endTime = curDate > endTime ? curDate.join(' ') : endTime.join(' ')
        this.$set(this.pickerOptions, 'selectableRange', `${curDate[1]} - 23:59:59`)
      } else {
        if (this.pickerOptions.selectableRange != '00:00:00 - 23:59:59') {
          this.tempC.endTime = endTime[0] + ' 00:00:00'
          this.$set(this.pickerOptions, 'selectableRange', '00:00:00 - 23:59:59')
        }
      }
    }
  },
  created() {
    this.resetTemp()
    // this.loadUrlTree()
    if (this.$route.query.treeable === 'false' || this.$route.query.treeable === false) {
      this.treeable = false
      this.query.strategyDefType = 1 // 预定义策略
    } else {
      this.query.strategyDefType = 0 // 应用策略
    }
    hiddenActiveAndEntity(this.colModel, this.treeable)
    this.$nextTick(() => {
      this.loadSoftInfo('')
    })
  },
  activated() {
    this.gridTable() && this.gridTable().execRowDataApi()
    this.$nextTick(() => {
      this.loadSoftInfo('')
    })
  },
  methods: {
    createConfig() {
      let validate
      this.$refs['configForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempC))
          if (rowData.objectProperty === this.$t('pages.software_Msg39')) {
            rowData.objectProperty = '*.*'
          }
          rowData.guid = new Date().getTime()
          rowData.taskValidEndTime = parseTime(rowData.endTime, 'y-m-d h:i:s')
          this.rowDatas.unshift(rowData)
          this.cancelConfig()
          validate = valid
        }
      })
      return validate
    },
    updateConfig() {
      let validate
      this.$refs['configForm'].validate((valid) => {
        if (valid) {
          const rowData = JSON.parse(JSON.stringify(this.tempC))
          for (let i = 0, size = this.rowDatas.length; i < size; i++) {
            const data = this.rowDatas[i]
            if (rowData.guid === data.guid) {
              if (rowData.objectProperty === this.$t('pages.software_Msg39')) {
                rowData.objectProperty = '*.*'
              }
              rowData.guid = new Date().getTime() // 修改的卸载任务需要修改guid，否则可能会导致终端不执行该卸载任务
              rowData.taskValidEndTime = parseTime(rowData.endTime, 'y-m-d h:i:s')
              this.rowDatas.splice(i, 1, rowData)
              break
            }
          }
          this.cancelConfig()
          validate = valid
        }
      })
      return validate
    },
    beforeCreateConfig() {
      this.$refs['configForm'] && this.$refs['configForm'].clearValidate()
    },
    beforeupdateConfig() {
      Object.assign(this.tempC, this.taskListTable().getSelectedDatas()[0])
      this.tempC.endTime = new Date(this.tempC.taskValidEndTime)
    },
    deleteConfig() {
      this.cancelConfig()
      const toDeleteKeys = this.taskListTable().getSelectedKeys()
      this.taskListTable().deleteRowData(toDeleteKeys, this.rowDatas)
    },
    cancelConfig() {
      this.$refs['configForm'] && this.$refs['configForm'].clearValidate()
      this.resetTempC()
    },
    resetTempC() {
      this.tempEndDate = ''
      this.tempC = JSON.parse(JSON.stringify(this.defaultTempC))
      this.tempC.endTime = null
      const date = new Date()
      date.setDate(date.getDate() + 1)
      date.setHours(23)
      date.setMinutes(59)
      date.setSeconds(59)
      this.tempC.endTime = date
      this.tempEndDate = this.tempC.endTime.getFullYear() + '-' + ('0' + (this.tempC.endTime.getMonth() + 1)).slice(-2) + '-' + ('0' + this.tempC.endTime.getDate()).slice(-2)
    },
    scanVersions() {
      this.$refs.softwareAssetVersionList && this.$refs.softwareAssetVersionList.clearSelectedNode()
      this.softwareAssetVersionList.splice(0)
      listVersion({ softName: this.tempC.objectName }).then(res => {
        if (res.data && res.data.length > 0) {
          res.data.forEach((item, index) => {
            if (item) {
              const verNode = {
                id: index + 1,
                label: item
              }
              this.softwareAssetVersionList.push(verNode)
            }
          })
          this.verTreeMenuDlgVisible = true
        } else {
          this.verTreeMenuDlgVisible = false
          this.$notify({
            title: this.$t('text.warning'),
            message: this.$t('pages.software_Msg51'),
            type: 'warning',
            duration: 2000
          })
        }
      }).catch(() => {
        this.verTreeMenuDlgVisible = false
      })
    },
    fullVersions() {
      this.tempC.objectProperty = this.$t('pages.software_Msg39') // '全版本'
      this.verTreeMenuDlgVisible = false
    },
    handleCheckedVersion() {
      const checkNode = this.$refs.softwareAssetVersionList.$refs.tree.getCurrentNode()
      if (checkNode) {
        this.tempC.objectProperty = checkNode.label == this.$t('pages.software_Msg39') ? '*.*' : checkNode.label
      }
      this.verTreeMenuDlgVisible = false
    },
    loadSoftInfo(searchInfo) {
      this.loadingSelect = true
      listSoftName({ searchInfo: searchInfo, limit: 50, page: 1, assetType: 2004 }).then(resp => {
        this.softOptions = resp.data
        this.loadingSelect = false
      })
    },
    softSelectChange() {
      this.tempC.objectProperty = this.$t('pages.software_Msg39')
    },
    gridTable() {
      return this.$refs['strategyListTable']
    },
    taskListTable() {
      return this.$refs['taskList']
    },
    refreshGridTable() {
      this.gridTable().execRowDataApi(this.query)
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyList(searchQuery)
    },
    taskSelectionChangeEnd: function(rowDatas) {
      this.configDeleteable = rowDatas.length > 0
      if (rowDatas.length === 1) {
        this.configEditable = true
      } else {
        this.configEditable = false
        this.cancelConfig()
      }
    },
    selectionChangeEnd: function(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    urlTreeNodeCheckChange: function(keys, datas) {
      keys.forEach(function(id, i) {
        if (id.indexOf('G') > -1) {
          keys.splice(i, 1)
        }
      })
      this.temp.urlIds = keys
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.resetTempC()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleUpdateTask(row) {

    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      this.rowDatas.splice(0, this.rowDatas.length)
      this.defaultRowDatas.splice(0, this.defaultRowDatas.length)
      this.temp.softWareTasks.forEach(el => {
        this.rowDatas.push(el)
        this.defaultRowDatas.push(el)
      })
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.temp.mode = row.mode
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleImport() {},
    handleExport() {},
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable().getSelectedIds()
        deleteStrategy({ ids: toDeleteIds.join(',') }).then(respond => {
          this.gridTable().deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    updateData() {
      this.submitting = true
      if (!this.rowDatas || this.rowDatas.length === 0) {
        this.$confirmBox(this.$t('pages.validateMsg_deleteMsg3'), this.$t('text.prompt')).then(() => {
          deleteStrategy({ ids: this.temp.id.toString() }).then(respond => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable().deleteRowData(this.temp.id.toString())
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }).catch(() => {
          this.submitting = false
        })
      } else {
        this.temp.softWareTasks = this.rowDatas
        updateStrategy(this.temp).then(respond => {
          this.submitting = false
          this.dialogFormVisible = false
          this.gridTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.updateSuccess'),
            type: 'success',
            duration: 2000
          })
        }).catch(reason => {
          this.submitting = false
        })
      }
    },
    restore() {
      const len = this.rowDatas.length
      this.rowDatas.splice(0, len)
      this.defaultRowDatas.forEach(el => {
        this.rowDatas.push(el)
      })
    },
    deleteTask() {
      const toDeleteDatas = this.taskListTable().getSelectedDatas()
      toDeleteDatas.forEach(el => {
        const res = this.rowDatas.map(m => m.guid);
        this.rowDatas.splice(res.indexOf(el.guid), 1)
      })
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    nameFormatter(row, data) {
      // 由于stg_def表的策略名称不能重复，因此部门卸载任务的名称添加了ID，但是部门ID在其它界面都没显示，此处显示有点奇怪，因此格式化任务名称
      if (data && data.startsWith('部门') && data.endsWith(this.taskNameSuffix)) {
        const pattern = '\\(\\d+\\)' + this.taskNameSuffix
        const str = data.replace(new RegExp(pattern), '')
        return str === data ? data : str + this.taskNameSuffix
      }
      return data
    },
    assertInfoFormatter: function(row, data) {
      const softwares = []
      row.softWareTasks.forEach(el => {
        const objectProperty = el.objectProperty === '*.*' ? this.$t('pages.software_Msg39') : el.objectProperty
        softwares.push(el.objectName + '(' + objectProperty + ')')
      })
      return softwares.join(',')
    },
    buttonFormatter: function(row) {
      return buttonFormatter(row, this)
    },
    softVerFormatter(row, data) {
      let softwareVersions = ''
      if (row.objectProperty) {
        softwareVersions = row.objectProperty === '*.*' ? this.$t('pages.software_Msg39') : row.objectProperty
      }
      return softwareVersions
    },
    oneTimeFormatter: function(row, data) {
      return this.oneTimeOptions[data]
    },
    timeFormatter(row, data) {
      return parseTime(data, 'y-m-d h:i:s')
    },
    handleView: function(row) {
      this.rowDatas.splice(0, this.rowDatas.length)
      row.softWareTasks.forEach(el => {
        this.rowDatas.push(el)
      })
    }
  }
}
</script>
