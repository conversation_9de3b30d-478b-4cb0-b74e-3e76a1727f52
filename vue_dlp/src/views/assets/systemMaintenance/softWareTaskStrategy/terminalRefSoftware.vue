<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" :os-type-filter="7" @data-change="strategyTargetNodeChange" />
    </div>

    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button size="mini" :disabled="btnDisable" @click="handleCreate">
          {{ $t('pages.createUninstallTask') }}
        </el-button>
        <strategy-extend :scope="1"/>
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareOrder_text11')" style="width: 200px;" @keyup.enter.native="handleFilter"/>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>

      <grid-table
        ref="softwareTable"
        :col-model="colModel"
        is-saved-selected
        saved-selected-prop="softwareName"
        :row-data-api="rowDataApi"
        :default-sort="{ prop: 'value' }"
        retain-pages
        :selected-data-title-formatter="selectedDataTitleFormatter"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <task-edit-dlg ref="taskEditDlg" @submitEnd="taskEditSubmitEnd"/>
  </div>
</template>

<script>
import { getSoftwareInfoPage, countSoftwareInfo } from '@/api/softwareManage/assets/assetsView'
import TaskEditDlg from './taskEditDlg'

export default {
  name: 'TerminalRefSoftware',
  components: { TaskEditDlg },
  props: {
    isShow: { type: Boolean, default: false }  // 是否处于显示状态
  },
  data() {
    return {
      multiSelect: false,
      defaultSort: { prop: 'terminalName', order: 'desc' },
      colModel: [
        { prop: 'terminalName', label: 'terminalName', width: '150', sort: 'custom' },
        { prop: 'softwareName', label: 'softwareName', width: '150', sort: true },
        { prop: 'softwareVersion', label: 'softwareVersion', width: '150' },
        { prop: 'path', label: 'path', width: '150', sort: 'custom' },
        { prop: 'installedTime', label: 'installDate', width: '150', sort: 'custom' },
        { prop: 'publisher', label: 'publisher2', width: '150', sort: 'custom' },
        { prop: 'lastUsedTime', label: 'lastUsedTime', width: '150', sort: 'custom' }
      ],
      query: { // 查询条件
        page: 1,
        assetType: 2004, // 软件卸载策略仅对应用软件生效
        objectType: undefined,
        objectId: undefined,
        mark: 0,
        conditionList: []
      },
      showTree: true,
      btnDisable: true,
      tableLoading: false
    }
  },
  computed: {
  },
  watch: {
    isShow(val) {
      val && this.gridTable() && this.gridTable().execRowDataApi()
    }
  },
  created() {
  },
  methods: {
    countSoftwareInfo,
    selectedDataTitleFormatter(row, prop) {
      return `${row.terminalName} - ${row.softwareName} - ${row.softwareVersion}`
    },
    selectionChangeEnd: function(rowDatas) {
      this.$nextTick(() => {
        this.btnDisable = !rowDatas || rowDatas.length == 0
      })
    },
    gridTable() {
      return this.$refs['softwareTable']
    },
    rowDataApi: function(option) {
      if (!this.isShow) {
        return new Promise((resolve, reject) => {
          resolve({ code: 20000, data: { total: 0, items: [] }})
        })
      }
      const searchQuery = Object.assign({ type: 2 }, this.query, option)
      if (option.searchCount === false) {
        return getSoftwareInfoPage(searchQuery)
      }
      return this.rowTotalApi(searchQuery)
    },
    async rowTotalApi(option) {
      const result = {}
      await countSoftwareInfo(option).then(resp => {
        result.total = resp.data
      })
      await getSoftwareInfoPage(option).then(resp => {
        if (resp.data) {
          resp.data.total = result.total
        }
        Object.assign(result, resp);
      })
      return result
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        // this.btnDisable = false
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    handleCreate() {
      const selectedRowDatas = this.gridTable().getSelectedDatas()
      this.$refs['taskEditDlg'].show(selectedRowDatas);
    },
    handleRefresh() {
      this.gridTable().execRowDataApi(this.query)
      this.gridTable().clearSelection()
    },
    taskEditSubmitEnd(data) {
      this.gridTable().selectedDatasDelete()
    }
  }
}
</script>
