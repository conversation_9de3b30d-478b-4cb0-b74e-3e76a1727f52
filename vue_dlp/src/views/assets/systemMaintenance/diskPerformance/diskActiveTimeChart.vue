<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import { debounce } from '@/utils'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '195px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    // chart配置项，可通过外部传入修改相对应的属性
    chartOption: {
      type: Object,
      default() {
        return {}
      }
    },
    chartData: {
      type: Object,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      chart: null,
      sidebarElm: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.initChart()
    if (this.autoResize) {
      this.__resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 100)
      window.addEventListener('resize', this.__resizeHandler)
    }

    // 监听侧边栏的变化
    this.sidebarElm = document.getElementsByClassName('sidebar-container')[0]
    this.sidebarElm && this.sidebarElm.addEventListener('transitionend', this.sidebarResizeHandler)
  },
  activated() {
    this.__resizeHandler()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    if (this.autoResize) {
      window.removeEventListener('resize', this.__resizeHandler)
    }

    this.sidebarElm && this.sidebarElm.removeEventListener('transitionend', this.sidebarResizeHandler)

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    resize() {
      window.setTimeout(() => {
        if (this.chart) {
          this.chart.resize()
        }
      }, 500)
    },
    sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.__resizeHandler()
      }
    },
    setOptions({ yAxisData } = {}) {
      const defaultOpt = {
        tooltip: {
          show: true,
          trigger: 'item',
          axisPointer: {
            type: 'line'
          },
          formatter: function(params) {
            let result = params.seriesName + '<br/>'
            result += `${params.marker} ${params.value + ' %'}`
            return result
          },
          backgroundColor: 'rgba(16,16,16,0.7)',
          itemStyle: {
            normal: {
              label: {
                show: true
              }
            }
          }
        },
        grid: {
          left: '8%',   // 左侧留白比例
          right: '8%',  // 右侧留白比例
          top: '5%',
          bottom: '12%'
        },
        xAxis: {
          type: 'category',
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#315070'],
              width: 1,
              type: 'solid'
            }
          },
          data: []
        },
        yAxis: {
          min: 0,
          max: 100,
          type: 'value',
          position: 'right',
          axisLabel: {
            formatter: function(value) {
              return value + '%';
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#315070'],
              width: 1,
              type: 'solid'
            }
          }
        },
        legend: {
          show: true,
          bottom: '-5px',
          inactiveColor: '#666', // 图例关闭时的颜色。
          textStyle: {
            color: '#2ec7c9'
          }
        },
        series: [{
          name: this.$t('pages.activeTime'),
          type: 'line',
          smooth: false,
          symbolSize: 6,
          areaStyle: {
            normal: {}
          },
          itemStyle: {
            color: '#2ec7c9'
          },
          data: yAxisData
        }]
      }
      this.chart.setOption(defaultOpt)
    },
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons', { devicePixelRatio: 2 })
      this.setOptions(this.chartData)
    }
  }
}
</script>
