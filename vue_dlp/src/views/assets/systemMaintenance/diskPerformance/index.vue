<template>
  <div class="app-container">
    <TransferTree
      ref="transferTree"
      :show-tree.sync="showTree"
      :showed-tree="['terminal']"
      :os-type-filter="7"
      :terminal-filter-key="terminalFilter"
      @data-change="strategyTargetNodeChange"
    />
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" :disabled="refreshDisable" size="mini" @click="refreshDiskInfo">
          {{ $t('button.refresh') }}
        </el-button>
        <el-checkbox v-model="autoRefresh" :true-label="1" :false-label="0" style="margin-left: 10px;font-weight: bold" @change="changeIntervalTime">
          {{ $t('pages.automaticRefreshTime') }}
        </el-checkbox>
        <el-input-number
          v-model="intervalTime"
          controls-position="right"
          :precision="0"
          :min="1"
          :max="30"
          :disabled="autoRefresh === 0"
          style="width:120px;"
          @blur="handleBlur"
          @change="changeIntervalTime"
        />
      </div>
      <el-tabs v-if="termId !== undefined" ref="tabs" v-model="activeName" v-loading="diskLoading" type="card" @tab-click="tabClick">
        <el-tab-pane v-for="(diskName, index) in diskLabelList" :key="index" :label="formatDiskLabel(diskName)" :name="diskName">
          <div v-loading="diskDetailLoading" class="table-container">
            <fieldset style="height: 40%;overflow: auto;margin-bottom: 5px;">
              <legend>{{ $t('pages.activeTime') }}</legend>
              <div>
                <DiskActiveTimeChart ref="activeTimeChart" :chart-data="temp.activeTimeChartData"/>
              </div>
            </fieldset>

            <fieldset style="height: 40%;overflow: auto;margin-bottom: 5px;">
              <legend>{{ $t('pages.diskTransferRate') }}</legend>
              <div>
                <DiskTransferRateChart ref="diskTransferRateChart" :chart-data="temp.transferRateChartData"/>
              </div>
            </fieldset>

            <div style="display: flex;height: 20%;font-size: 15px">
              <ul style="width: 50%;">
                <li>
                  <label>{{ $t('pages.activeTime') }}：</label>
                  <span>{{ temp.activeTime === undefined ? '' : temp.activeTime + ' %' }}</span>
                </li>
                <!--              暂时屏蔽 平均响应时间-->
                <!--              <li>-->
                <!--                <label>{{ $t('pages.avgTime') }}：</label>-->
                <!--                <span>{{ temp.avgTime === undefined ? '' : (temp.avgTime / 10) + ' MS' }}</span>-->
                <!--              </li>-->
                <li>
                  <label>{{ $t('pages.readSpeed') }}：</label>
                  <span>{{ temp.readSpeed === undefined ? '' : formatReadWriteSpeed(temp.readSpeed) }}</span>
                </li>
                <li>
                  <label>{{ $t('pages.writeSpeed') }}：</label>
                  <span>{{ temp.writeSpeed === undefined ? '' : formatReadWriteSpeed(temp.writeSpeed) }}</span>
                </li>
                <li>
                  <label>{{ $t('pages.diskTitle') }}：</label>
                  <span>{{ temp.diskTitle === undefined ? '' : temp.diskTitle }}</span>
                </li>
                <li>
                  <label>{{ $t('table.driveSerial') }}：</label>
                  <span>{{ temp.serialNumber === undefined ? '' : temp.serialNumber }}</span>
                </li>
              </ul>
              <ul style="width: 50%">
                <li>
                  <label>{{ $t('table.devSize') }}：</label>
                  <span>{{ temp.diskSize }}</span>
                </li>
                <li>
                  <label>{{ $t('pages.formattedCapacity') }}：</label>
                  <span>{{ temp.formattedCapacity }}</span>
                </li>
                <li>
                  <label>{{ $t('pages.termSecuritySysTemDisk') }}：</label>
                  <span>{{ temp.systemDisk === undefined ? '' : temp.systemDisk ? $t('text.yes') : $t('text.no') }}</span>
                </li>
                <li>
                  <label>{{ $t('pages.pageFile') }}：</label>
                  <span>{{ temp.pageFile === undefined ? '' : temp.pageFile ? $t('text.yes') : $t('text.no') }}</span>
                </li>
                <li>
                  <label>{{ $t('table.type') }}：</label>
                  <span>{{ formatDiskType(temp.diskType) }}</span>
                </li>
              </ul>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div v-else style="height: 100%;border: 1px solid #666;">
        <span style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);color: #909399;font-size: 14px">{{ $t('text.noData') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'
import { listDisk, getDiskPerformance } from '@/api/assets/systemMaintenance/diskPerformance';
import DiskActiveTimeChart from '@/views/assets/systemMaintenance/diskPerformance/diskActiveTimeChart'
import DiskTransferRateChart from '@/views/assets/systemMaintenance/diskPerformance/diskTransferRateChart'
import { isEmpty } from '@/utils/validate';

export default {
  name: 'DiskPerformance',
  components: { DiskActiveTimeChart, DiskTransferRateChart },
  data() {
    return {
      showTree: true,
      refreshDisable: true,
      ctrlAble: false,
      diskLoading: false,
      diskDetailLoading: false,
      timer: undefined,
      termId: undefined,
      diskLabel: undefined,
      intervalTime: 1,
      autoRefresh: 1,
      retFinished: true, // 数据响应回来后，再发送请求
      temp: {},
      defaultTemp: {
        diskLabel: undefined,
        diskTitle: undefined,
        activeTime: undefined,
        avgTime: undefined,
        readSpeed: undefined,
        writeSpeed: undefined,
        diskSize: undefined,
        formattedCapacity: undefined,
        systemDisk: undefined,
        pageFile: undefined,
        diskType: undefined,
        serialNumber: undefined,
        activeTimeChartData: {
          yAxisData: []
        },
        transferRateChartData: {
          yAxisData1: [],
          yAxisData2: []
        }
      },
      activeName: '',
      diskLabelList: []
    }
  },
  computed: {
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
    clearInterval(this.timer)
    if (this.autoRefresh) {
      this.timer = setInterval(() => {
        this.getDiskDetail(this.termId, this.diskLabel)
      }, this.intervalTime * 1000)
    }
  },
  deactivated() {
    clearInterval(this.timer)
  },
  mounted() {
    clearInterval(this.timer)
    if (this.autoRefresh) {
      this.timer = setInterval(() => {
        this.getDiskDetail(this.termId, this.diskLabel)
      }, this.intervalTime * 1000)
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('diskPerformance', termId, [2], '5.02').then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    strategyTargetNodeChange(tabName, data) {
      if (this.termId) {
        if (this.termId == data.dataId) {
          // 未切换终端
          return
        }
      }
      this.termId = undefined
      this.diskLabel = undefined
      this.refreshDisable = true
      this.resetTemp()
      this.diskLabelList = []
      if (!data || data.id.indexOf('G' + data.dataId) > -1) {
        return
      }
      this.checkCtrlAble(data.dataId).then(() => {
        if (!this.ctrlAble) return
        this.termId = data.dataId
        this.loadDiskLabelList()
      })
    },
    loadDiskLabelList() {
      this.diskLoading = true
      listDisk(this.termId).then((resp) => {
        this.diskLabelList = []
        this.diskLabelList = resp.data.map(disk => disk.diskLabel)
        this.activeName = this.diskLabelList[0]
        this.tabClick()
        this.diskLoading = false
      }).catch(e => {
        if (this.diskLoading) {
          this.diskLoading = false
        }
      })
    },
    refreshDiskInfo() {
      // 刷新选中磁盘的详情
      this.resetTemp()
      this.temp.activeTimeChartData.yAxisData = Array(60).fill()
      this.temp.transferRateChartData.yAxisData1 = Array(60).fill()
      this.temp.transferRateChartData.yAxisData2 = Array(60).fill()
      this.diskDetailLoading = true
      this.getDiskDetail(this.termId, this.diskLabel)
    },
    tabClick() {
      if (this.diskLabel) {
        if (this.diskLabel == this.activeName) {
          // 未切换磁盘
          return
        }
      }
      this.resetTemp()
      this.resizeChart()
      this.retFinished = true
      this.diskDetailLoading = true
      this.diskLabel = this.activeName
      this.refreshDisable = false
      this.temp.activeTimeChartData.yAxisData = Array(60).fill()
      this.temp.transferRateChartData.yAxisData1 = Array(60).fill()
      this.temp.transferRateChartData.yAxisData2 = Array(60).fill()
      this.getDiskDetail(this.termId, this.diskLabel)
    },
    getDiskDetail(termId, diskLabel) {
      if (termId && diskLabel && this.retFinished === true) {
        this.retFinished = false
        getDiskPerformance(termId, diskLabel).then((resp) => {
          // 响应回来的数据太慢，还没来得及处理，此时关闭自动刷新，或者切换了终端，后续的响应不处理直接return
          if (this.autoRefresh === 0 || this.termId === undefined || this.termId !== termId || this.diskLabel !== diskLabel) {
            this.diskDetailLoading = false
            return
          }
          const diskDetail = resp.data.length === 0 ? undefined : resp.data[0]
          if (diskDetail === undefined) {
            this.resetTemp()
          } else {
            this.temp.activeTimeChartData.yAxisData.shift()
            this.temp.activeTimeChartData.yAxisData.push(diskDetail.activeTime)
            this.temp.transferRateChartData.yAxisData1.shift()
            this.temp.transferRateChartData.yAxisData1.push(diskDetail.readSpeed)
            this.temp.transferRateChartData.yAxisData2.shift()
            this.temp.transferRateChartData.yAxisData2.push(diskDetail.writeSpeed)
            this.$set(this.temp, 'diskLabel', diskDetail.diskLabel)
            this.$set(this.temp, 'diskTitle', diskDetail.diskTitle)
            this.$set(this.temp, 'activeTime', diskDetail.activeTime)
            this.$set(this.temp, 'avgTime', diskDetail.avgTime)
            this.$set(this.temp, 'readSpeed', diskDetail.readSpeed)
            this.$set(this.temp, 'writeSpeed', diskDetail.writeSpeed)
            this.$set(this.temp, 'diskSize', diskDetail.diskSize)
            this.$set(this.temp, 'formattedCapacity', diskDetail.formattedCapacity)
            this.$set(this.temp, 'systemDisk', diskDetail.systemDisk)
            this.$set(this.temp, 'pageFile', diskDetail.pageFile)
            this.$set(this.temp, 'diskType', diskDetail.diskType)
            this.$set(this.temp, 'serialNumber', diskDetail.serialNumber)
          }
          this.retFinished = true
          this.diskDetailLoading = false
        }).catch(e => {
          this.retFinished = true
          this.diskDetailLoading = false
        })
      }
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
      this.resizeChart()
    },
    handleBlur(event) {
      const val = event.target.value.trim()
      if (!val) {
        this.intervalTime = 1
      }
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80 || node.oriData != 0)
    },
    changeIntervalTime() {
      clearInterval(this.timer)
      if (this.autoRefresh) {
        this.timer = setInterval(() => {
          this.getDiskDetail(this.termId, this.diskLabel)
        }, this.intervalTime * 1000)
      }
    },
    resetTemp() {
      this.temp = JSON.parse(JSON.stringify(this.defaultTemp))
    },
    renderContent(h, { node, data, store }) {
      const diskLabel = this.formatDiskLabel(data.label)
      return (
        <div class='custom-tree-node' title={diskLabel}>
          <span>{diskLabel}</span>
        </div>
      )
    },
    formatDiskType(type) {
      if (type === 0) {
        return this.$t('pages.undefined')
      } else if (type === 2) {
        return this.$t('pages.fixedDisk')
      } else if (type === 3) {
        return 'HDD'
      } else if (type === 4) {
        return 'SSD'
      } else if (type === 5) {
        return 'SCM'
      } else {
        return type
      }
    },
    formatDiskLabel(diskLabel) {
      if (isEmpty(diskLabel)) {
        return ''
      }
      const firstIndex = diskLabel.indexOf(' ')
      const diskLabelLeft = diskLabel.substring(0, firstIndex)
      const diskLabelRight = diskLabel.substring(firstIndex + 1)
      return this.$t('pages.disk') + ' ' + diskLabelLeft + (diskLabelRight ? ' (' + diskLabelRight + ')' : '')
    },
    resizeChart() {
      if (this.$refs['activeTimeChart']) {
        this.$refs['activeTimeChart'].forEach(chart => {
          chart.__resizeHandler()
        })
      }
      if (this.$refs['diskTransferRateChart']) {
        this.$refs['diskTransferRateChart'].forEach(chart => {
          chart.__resizeHandler()
        })
      }
    },
    formatReadWriteSpeed(speed) {
      const castGB = 1024 * 1024 * 1024
      const castMB = 1024 * 1024
      const castKB = 1024
      return speed >= castGB ? (speed / castGB).toFixed(2) + ' GB/S'
        : speed >= castMB ? (speed / castMB).toFixed(2) + ' MB/S'
          : (speed / castKB).toFixed(2) + ' KB/S'
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-checkbox__label {
  padding-left: 0;
}
ul {
  list-style: none;
  overflow: auto;
  margin: 0;
  padding-left: 0;
  li {
    margin: 5px;
  }
  li:first-child {
    margin-top: 3px;
  }
}
</style>
