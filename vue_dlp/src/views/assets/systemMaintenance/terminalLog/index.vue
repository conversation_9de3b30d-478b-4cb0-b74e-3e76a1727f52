<template>
  <file-manager :selection="selection" @refresh="refresh">
    <strategy-target-tree
      slot="tree"
      ref="strategyTargetTree"
      :showed-tree="['terminal']"
      :os-type-filter="7"
      @data-change="strategyTargetNodeChange"
    />
    <template slot="toolbar">
      <file-search
        style="float: right; margin-left: 10px;"
        :tips="$t('pages.serverlog_search_tips')"
        :disabled="searchDisabled"
        @search="handleSearch"
      />
      <!--<chunk-download :download-obj="downloadObj" terminal-log/>-->
      <explorer-downloader is-log :terminal-id="terminalId" :dir="curDir" :selection="selection"/>
      <el-breadcrumb v-show="curFilePath.length > 1" separator="/" style="height:29px; line-height:28px; padding-left:10px; margin-left:110px;">
        <el-breadcrumb-item v-for="(item, index) in curFilePath" :key="item.id">
          <a href="javascript:void(0);" :title="item" @click="curFilePathClick(index)">{{ item }}</a>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </template>
    <grid-table
      ref="tableList"
      v-loading="tableLoading"
      :col-model="colModel"
      :multi-select="true"
      :show-pager="false"
      :row-datas="rowData"
      :default-sort="defaultSort"
      @selectionChangeEnd="selectionChangeEnd"
      @row-dblclick="rowDblclickFunc"
    />
  </file-manager>
</template>

<script>
import ExplorerDownloader from '@/views/assets/systemMaintenance/explorer/downloader'
import FileManager from '@/components/FileManager'
import FileSearch from '@/components/FileManager/search'
import { getFileIcon } from '@/icons/extension'
import moment from 'moment'
import { fileTypeFormatter, fileSizeFormatter } from '@/api/assets/systemMaintenance/explorer'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'TerminalLog',
  components: { ExplorerDownloader, FileManager, FileSearch },
  data() {
    return {
      ctrlAble: false,
      parentPath: '',
      defaultParentPath: '$TERMINAL_LOG',
      iconOption: {
        0: 'disk',
        1: 'dir',
        2: 'file'
      },
      fileType: {
        1: this.$t('pages.folder'),
        2: this.$t('pages.file1')
      },
      curFilePath: ['Terminal'],
      defaultSort: { prop: 'oriData.type', order: 'desc' },
      colModel: [
        { prop: 'label', label: 'name', width: '200', sort: true, iconFormatter: this.iconClassFormatter },
        { prop: 'oriData.type', label: 'fileType', width: '150', sort: true, sortOriginal: true, sortArr: ['oriData.type', 'suffix'], formatter: this.fileTypeFormatter },
        { prop: 'oriData.size', label: 'size1', width: '150', sort: true, sortOriginal: true, formatter: this.fileSizeFormatter },
        { prop: 'oriData.modifyTime', label: 'updateTime', width: '150', sort: true },
        { prop: 'oriData.createTime', label: 'createTime', width: '150', sort: true }
      ],
      allRowData: [],
      tempRowData: [],
      rowData: [],
      rowDataCache: undefined,
      tableLoading: false,
      curNodeData: undefined,
      terminalId: undefined,
      selection: [],
      downloadPath: [], // 获取文件路径有用的片段
      downloadObj: {}, // 文件上传参数
      dirPath: '' // 拼接文件路径
    }
  },
  computed: {
    gridTable() {
      return this.$refs['tableList']
    },
    terminalTree() {
      return this.$refs['strategyTargetTree']
    },
    searchDisabled() {
      if (this.tableLoading || !this.curNodeData) {
        return true
      }
      if (this.curNodeData.dataType === '0') {
        return !this.curNodeData.online
      }
      return !(this.curNodeData.dataType === '0' && this.curNodeData.online)
    },
    curDir() {
      const paths = this.curFilePath.slice(1)
      paths.unshift(this.defaultParentPath)
      return paths.join('/')
    }
  },
  watch: {
    tempRowData(val) {
      this.rowDataCache = undefined
      if (val.length > 0) {
        this.tableLoading = true
        this.rowData = [...this.tempRowData]
        this.tempRowData.splice(0)
        this.$nextTick(() => {
          this.tableLoading = false
        })
      }
    }
  },
  methods: {
    fileTypeFormatter,
    fileSizeFormatter,
    selectionChangeEnd(selection) {
      this.selection = selection.map(item => {
        const { oriData: { fileName: name, size, type }} = item
        return { name, size, type }
      })
      // this.downloadData(selection)
    },
    /**
     * 上传文件数据
     * @param val 表格选中值
     */
    downloadData(val) {
      if (val.length === 0) {
        this.downloadObj = undefined
        return
      }
      this.downloadPath = this.curFilePath.slice(1)
      this.dirPath = '' // 拼接文件路径
      for (let i = 0; i < this.downloadPath.length; i++) {
        this.dirPath += this.downloadPath[i] + '/'
      }
      if (val.length !== 0) {
        const file = []
        for (let i = 0; i < val.length; i++) { // 获取选中文件的名称和大小
          const fileObj = {}
          fileObj.name = val[i].oriData.fileName
          fileObj.size = val[i].oriData.size
          fileObj.type = val[i].oriData.type
          file.push(fileObj)
        }
        this.downloadObj = { // 文件下载按钮点击时，需要传给后台的值
          terminalId: val[0].oriData.terminalId,
          dir: this.dirPath.substring(0, this.dirPath.length - 1),
          fileList: file
        }
        // console.log('this.downloadObj111', JSON.parse(JSON.stringify(this.downloadObj)))
      }
    },
    strategyTargetNodeChange(tabName, data) {
      this.curNodeData = data
      const that = this
      const terminalId = data.dataId
      const continueFlag = 2
      const fileType = 5
      var parentPath = that.defaultParentPath
      if (that.curFilePath.length > 1) {
        var pathTemp = that.curFilePath.slice(1)
        pathTemp.forEach(item => {
          parentPath = parentPath + '\\' + item
        })
      }
      this.rowData = []
      if (data.id.indexOf('G') < 0) {
        this.terminalId = terminalId
        this.tableLoading = true
        this.checkCtrlAble(terminalId).then(() => {
          if (!this.ctrlAble) {
            this.tableLoading = false
            return
          }
          this.$socket.sendToUser(terminalId, '/listChildFile', {
            terminalId, continueFlag, fileType, parentPath
          }, (respond, handle) => {
            handle.close()
            const datas = respond.data.map(item => {
              item.suffix = item.label.indexOf('.') > -1 ? item.label.split('.').pop() : ''
              return item
            })
            that.tempRowData = [...datas]
            that.tableLoading = false
            // this.allRowData.splice(0, this.allRowData.length)
            // if (respond.data) {
            //   this.tableLoading = false
            //   respond.data.forEach((row) => {
            //     this.allRowData.push(row)
            //   })
            // }
          }, (handle) => {
            handle.close()
            if (that.tableLoading) {
              that.tableLoading = false
              that.$notify({ title: this.$t('text.error'), message: this.$t('text.requestTimeout'), type: 'error', duration: 2000 })
            }
          })
        })
      } else {
        this.terminalId = undefined
        this.tableLoading = false
        this.tempRowData = []
      }
    },
    rowDblclickFunc(rowData, column, event) {
      if (rowData.oriData.type == 2) return
      const curNodeData = this.terminalTree.getCurrentNode()
      if (curNodeData) {
        this.curFilePath.push(rowData.label)
        this.strategyTargetNodeChange('terminal', curNodeData)
      }
    },
    refresh() {
      const curNodeData = this.terminalTree.getCurrentNode()
      if (curNodeData) {
        this.curFilePath.splice(1, this.curFilePath.length - 1)
        this.strategyTargetNodeChange('terminal', curNodeData)
      }
    },
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('termLog', termId).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    iconClassFormatter: function(row) {
      const icons = []
      // 0：硬盘 1：文件夹 2. 文件
      const type = row.oriData.type
      let title
      let iconName
      if (type == 2) {
        title = row.label.split('.').pop().toLowerCase()
        iconName = getFileIcon(title)
      } else {
        title = { 0: '磁盘', 1: '文件夹' }[type]
        iconName = { 0: 'disk1', 1: 'dir1' }[type]
      }
      icons.push({ class: iconName, title: title })
      return icons
    },
    attributesFormatter: function(row, data) {
      const result = []
      if (data & 1) {
        result.push(this.$t('pages.readOnly'))
      }
      if (data & 2) {
        result.push(this.$t('pages.hide'))
      }
      if (data & 32) {
        result.push(this.$t('pages.files1'))
      }
      return result.join(' | ')
    },
    // 面包屑点击方法
    curFilePathClick: function(index) {
      if (this.curFilePath.length === index + 1) {
        return // 路径的最后一个节点，点击无效
      }
      this.curFilePath.splice(index + 1, this.curFilePath.length - index)
      const curNodeData = this.terminalTree.getCurrentNode()
      curNodeData && this.strategyTargetNodeChange('terminal', curNodeData)
    },
    handleSearch(data) {
      if (!this.rowDataCache) {
        this.rowDataCache = this.rowData
      }
      this.rowData = this.rowDataCache.filter(item => {
        if (data.name && item.label.toLowerCase().indexOf(data.name.toLowerCase()) < 0) {
          return false
        }
        if (item.oriData.type === 0) { // 磁盘没有时间
          return true
        }
        if (data.modified) {
          const modifyTime = moment(item.oriData.modifyTime, 'YYYY-MM-DD HH:mm:ss').unix()
          if (modifyTime < data.modified[0] || modifyTime > data.modified[1]) {
            return false
          }
        }
        if (data.creation) {
          const createTime = moment(item.oriData.createTime, 'YYYY-MM-DD HH:mm:ss').unix()
          if (createTime < data.creation[0] || createTime > data.creation[1]) {
            return false
          }
        }
        return true
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-breadcrumb__inner a {
    max-width: 200px;
    display: inline-block;
    word-break: keep-all;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  >>>.el-breadcrumb__separator {
    vertical-align: top;
  }
</style>
