<template>
  <div class="app-container">
    <div class="table-container">
      <div class="toolbar">
        <el-button size="mini" :disabled="!addAble" @click="handleCreate">
          {{ $t('pages.buildUpgradeTask') }}
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleRefresh">
          {{ $t('button.refresh') }}
        </el-button>
      </div>
      <grid-table
        ref="terminalTable"
        :multi-select="true"
        :row-data-api="rowDataApi"
        :col-model="colModel1"
        @selectionChangeEnd="handleSelectionChange"
      />
      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="$t('pages.buildUpgradeTask')"
        :visible.sync="dialogFormVisible"
        width="800px"
      >
        <div style="">
          <el-button type="primary" icon="el-icon-upload" size="mini" @click="handleImport">
            {{ $t('pages.uploadInstallPackage') }}
          </el-button>
          <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDeletePack">
            {{ $t('pages.delete') }}
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="handleSearch">
            {{ $t('button.refresh') }}
          </el-button>
        </div>
        <div>
          <grid-table
            ref="packageList"
            :height="200"
            :show-pager="true"
            :multi-select="true"
            :col-model="colModel"
            :row-data-api="rowDataApi2"
            @selectionChangeEnd="handleSelectionChange2"
          />
        </div>
        <div style="color: #0c60a5;padding-top: 10px">
          {{ $t('pages.installPackageText') }}
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="submitting" type="primary" @click="saveData()">{{ $t('button.confirm') }}</el-button>
          <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
        </div>
      </el-dialog>
      <el-dialog
        v-el-drag-dialog
        :close-on-click-modal="false"
        :modal="false"
        :title="$t('pages.uploadInstallPackage')"
        :visible.sync="uploadVisible"
        width="400px"
      >
        <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="90px" style="width: 340px;">
          <FormItem :label="$t('pages.fileDescription')" prop="describe">
            <el-input v-model="temp.describe" :maxlength="50"/>
          </FormItem>
          <el-row>
            <el-col :span="21">
              <FormItem :label="$t('pages.exeName')" prop="fileName">
                <el-input v-model="temp.fileName" readonly/>
              </FormItem>
            </el-col>
            <el-col :span="2">
              <el-upload
                ref="upload"
                name="upload"
                action="1111"
                accept=".exe"
                :on-change="fileChange"
                :show-file-list="false"
                :file-list="fileList"
                :disabled="fileSubmitting"
                :auto-upload="false"
              >
                <el-button type="primary" icon="el-icon-upload" :loading="fileSubmitting" style="margin: 0 0 0 1px;"></el-button>
              </el-upload>
            </el-col>
          </el-row>
        </Form>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="submitting" type="primary" @click="saveFile()">{{ $t('button.confirm') }}</el-button>
          <el-button @click="uploadVisible = false">{{ $t('button.cancel') }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  uploadSoft, getPacketList, deletePack, reUploadFile, addTerminalUpgrade
} from '@/api/assets/systemMaintenance/terminalUpgrade'
import { getTerminalPage } from '@/api/system/terminalManage/terminal'

export default {
  name: 'UploadPack',
  data() {
    return {
      colModel: [
        { prop: 'fileName', label: 'installPackageName', width: '100' },
        { prop: 'version', label: 'installPackageVersionNumber', width: '120' },
        { prop: 'describe', label: 'fileDescription', width: '100' },
        { prop: 'statusInfo', label: 'uploadStatus', width: '100' },
        { prop: 'createTime', label: 'time', width: '120' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'reUpload', click: this.reUploadFile }
          ]
        }
      ],
      colModel1: [
        { prop: 'name', label: 'terminalName', width: '150', fixed: true, sort: 'custom' },
        { prop: 'computerName', label: 'computerName', width: '150', sort: 'custom' },
        { prop: 'id', label: 'terminalCode', width: '150', sort: 'custom' },
        { prop: 'mainIp', label: 'IP', width: '150', sort: 'custom' },
        { prop: 'mainMac', label: 'mac', width: '150' },
        { prop: 'version', label: 'terminalVersion', width: '150', sort: 'custom' }
      ],
      query: { // 查询条件
        page: 1,
        objectType: undefined,
        objectId: undefined
      },
      deleteable: false,
      rules: {
        describe: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        fileName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ]
      },
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        fileName: '',
        describe: ''
      },
      softWareTask: {},
      defaultSoftWareTask: {
        guid: undefined,
        objectName: '',
        objectProperty: '',
        taskType: 4,
        installParam: '',
        remark: '',
        exceptKey: '',
        taskValidEndTime: ''
      },
      dialogFormVisible: false,
      uploadVisible: false,
      submitting: false,
      addAble: false,
      fileList: [],
      fileSubmitting: false
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    reUploadFile: function(row, data) {
      reUploadFile(row).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.upgrade_text1'),
          type: 'success',
          duration: 2000
        })
      })
    },
    versionFormatter: function(row, data) {
      if (!data) {
        return row.version
      } else {
        return data
      }
    },
    gridTable() {
      return this.$refs['terminalTable']
    },
    getPackageTable() {
      return this.$refs['packageList']
    },
    handleSelectionChange(val) {
      this.addAble = val.length > 0
    },
    handleSelectionChange2(val) {
      this.deleteable = val.length > 0
    },
    handleSearch() {
      this.getPackageTable().execRowDataApi()
    },
    selectable(row, index) {
      return row.status == 6
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTerminalPage(searchQuery)
    },
    rowDataApi2: function(option) {
      return getPacketList(option)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.fileList.splice(0)
    },
    handleImport() {
      this.uploadVisible = true
      this.resetTemp()
    },
    handleDeletePack() {
      this.$confirmBox(this.$t('pages.delControlProcess'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.getPackageTable().getSelectedIds()
        deletePack({ ids: toDeleteIds.join(',') }).then(respond => {
          this.getPackageTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    handleCreate() {
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.getPackageTable().execRowDataApi()
      })
    },
    handleRefresh() {
      this.gridTable().execRowDataApi(this.query)
    },
    fileChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          title: this.$t('text.prompt'),
          message: this.$t('pages.upgrade_text2'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const fileName = file.name
      const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      if (ext == 'exe') {
        this.temp.fileName = fileName
        this.fileList.splice(0, 1, file)
      }
    },
    saveFile() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 通过 FormData 对象上传文件
          const fd = this.toFormData(this.temp)
          fd.append('uploadFile', this.fileList[0].raw)
          // 发起请求
          uploadSoft(fd).then(res => {
            this.submitting = false
            this.uploadVisible = false
            this.getPackageTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.upgrade_text3'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    saveData() {
      this.submitting = true
      const packageList = this.getPackageTable().getSelectedDatas()
      if (packageList.length <= 0) {
        this.$message({
          message: this.$t('pages.pleaseCheckedOneTerminalInstallPackage'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      } else if (packageList.length > 1) {
        this.$message({
          message: this.$t('pages.atMostSelectedOneInstallPackage'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      const datas = this.gridTable().getSelectedDatas()
      const terminalNames = []
      const terminalIds = []
      const packageId = packageList[0].id
      const packageName = packageList[0].fileName
      datas.forEach(item => {
        terminalNames.push(item.name)
        terminalIds.push(item.id)
      })
      this.$confirmBox(this.$t('pages.installPackageUpgradeTerminalMsg', { packageName: packageName, terminalName: terminalNames.join('，') }), this.$t('text.prompt')).then(() => {
        addTerminalUpgrade({ terminalNames: terminalNames, terminalIds: terminalIds, packageId: packageId, packageName: packageName }).then(res => {
          this.submitting = false
          this.$notify({
            title: this.$t('text.prompt'),
            message: this.$t('pages.installPackageAddTermnalUpgradeTaskMsg'),
            type: 'success',
            duration: 2000
          })
          this.dialogFormVisible = false
        }).catch(() => {
          this.submitting = false
        })
      }).catch(() => {
        this.submitting = false
      })
    }
  }
}
</script>
