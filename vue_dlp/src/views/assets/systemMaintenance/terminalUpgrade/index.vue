<template>
  <div class="app-container">
    <el-tabs ref="tabs" v-model="activeName" type="card" :before-leave="changeTab" @tab-click="tabClick">
      <el-tab-pane :label="$t('pages.termUpgrade')" name="upgradeStrategy">
        <upgrade-strategy ref="upgradeStrategy"/>
      </el-tab-pane>
      <el-tab-pane :label="$t('pages.taskPerformance')" name="taskList">
        <task-list ref="taskList"></task-list>
      </el-tab-pane>
      <!--<el-tab-pane :label="$t('pages.makeInstallPackage')" name="makePackage">
        <make-package ref="makePackage"></make-package>
      </el-tab-pane>-->
    </el-tabs>
  </div>
</template>

<script>
// import UploadPack from '@/views/assets/systemMaintenance/terminalUpgrade/uploadPack'
import TaskList from '@/views/assets/systemMaintenance/terminalUpgrade/taskList'
import UpgradeStrategy from '@/views/assets/systemMaintenance/terminalUpgrade/upgradeStrategy'
import MakePackage from '@/views/assets/systemMaintenance/makePackage'

export default {
  name: 'TerminalUpgrade',
  components: { TaskList, UpgradeStrategy, MakePackage },
  data() {
    return {
      activeName: 'upgradeStrategy'
    }
  },
  computed: {
  },
  watch: {
  },
  created() {
  },
  activated() {
  },
  methods: {
    tabClick(pane, event) {
      if (pane.name == 'makePackage') {
        this.$nextTick(() => {
          this.$refs.makePackage.handleFilter()
        })
      }
    },
    changeTab(activeName, oldActiveName) {
      this.activeName = activeName
    }
  }
}
</script>
<style scoped>
  .app-container{
    padding: 10px 15px;
  }
</style>
