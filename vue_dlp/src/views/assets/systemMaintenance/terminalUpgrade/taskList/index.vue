<template>
  <div class="app-container">
    <div v-if="listable && treeable" class="tree-container" :class="showTree?'':'hidden'">
      <strategy-target-tree ref="strategyTargetTree" :showed-tree="['terminal']" :terminal-filter-key="terminalFilter" is-log-terminal-tree @data-change="strategyTargetNodeChange"/>
    </div>
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button v-if="treeable" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" size="mini" :title="$t('button.refresh')" @click="refresh">
          <svg-icon icon-class="refresh" />
        </el-button>
        <div class="searchCon">
          <el-select v-model="query.strategyDefType">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="strategyListTable" :multi-select="false" :col-model="colModel" :row-data-api="rowDataApi" />
    </div>
  </div>
</template>

<script>
import { getEffectiveStrategy } from '@/api/assets/systemMaintenance/terminalUpgrade'
import { refreshPage } from '@/utils'

export default {
  name: 'TaskList',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'entityName', label: 'terminalName', fixedWidth: '200' },
        { prop: 'ip', label: 'ip', fixedWidth: '150' },
        { prop: 'name', label: 'taskName2', fixedWidth: '200' },
        { prop: 'pack.fileName', label: 'installPackageName', width: '200' },
        { prop: 'pack.version', label: 'installPackageVersion', width: '150' },
        { prop: 'curVersion', label: 'curTermVersion', width: '100' },
        { prop: 'status', label: 'UudateSituation', width: '150', formatter: this.statusFormatter }
      ],
      query: { // 查询条件
        page: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined,
        strategyDefType: null
      },
      treeable: true,
      showTree: true,
      temp: {}, // 表单字段
      defaultTemp: {
        id: undefined,
        name: '',
        endTime: new Date(),
        softWareTasks: [],
        oneTime: 1,
        remark: '',
        active: true,
        objectIds: [],
        objectGroupIds: []
      },
      options: [{ value: null, label: this.$t('pages.all') }, { value: 0, label: this.$t('pages.upgrading') }, { value: 1, label: this.$t('pages.upgraded') }, { value: 2, label: this.$t('pages.installPackagWaitingDownload') },
        { value: 3, label: this.$t('pages.installPackagDownloading') }, { value: 4, label: this.$t('pages.installPackagStartExecute') }, { value: 5, label: this.$t('pages.installPackagDownloadFailed') }, { value: 6, label: this.$t('pages.installPackagUpgradSucceededButNotReboot') },
        { value: 7, label: this.$t('pages.installPackagSilentUpgradingOrUninstalling') }, { value: 8, label: this.$t('pages.installPackagAlreadyProcessExecuting') }, { value: 9, label: this.$t('pages.installPackagNotExecutedWithAdminPermission') }, { value: 10, label: this.$t('pages.installPackagNotRebbotAfterLastExecutionSucceeded') },
        { value: 11, label: this.$t('pages.installPackagUpgradingNotAllowedDowngrade') }, { value: 12, label: this.$t('pages.installPackagParsingFailed') }, { value: 13, label: this.$t('pages.installPackagConnectServerFailed') },
        { value: 14, label: this.$t('pages.installPackagSilentInstallationSilentPrivilegeInstallationFailed') }, { value: 16, label: this.$t('pages.termVersionFail') }, { value: 17, label: this.$t('pages.termLangVerFail') },
        { value: 18, label: '安装包复制失败' }, { value: 19, label: '获取服务器时间失败' },
        { value: 20, label: '安装包已失效' }, { value: 21, label: '终端安装码不存在' }
      ]
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
  },
  methods: {
    gridTable() {
      return this.$refs['strategyListTable']
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getEffectiveStrategy(searchQuery)
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !(type === 3 || (type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable().execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable().execRowDataApi(this.query)
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    refresh() {
      return refreshPage(this)
    },
    statusFormatter(row) {
      if (!row.step) {
        /* return row.status == 1 ? `<span style="color: green">${this.$t('pages.upgraded')}</span>` : `<span style="color: red">${this.$t('pages.upgrading')}</span>` */
        return row.status == 1 ? `${this.$t('pages.upgraded')}` : `${this.$t('pages.upgrading')}`
      } else {
        let msg = this.$t('pages.excuteFail')
        if (row.step == 1 && row.result == 0) {
          // 等待下载安装包
          msg = this.$t('pages.installPackagWaitingDownload')
        } else if (row.step == 2 && row.result == 0) {
          // 正在下载安装包
          msg = this.$t('pages.installPackagDownloading')
        } else if (row.step == 3 && row.result == 0) {
          // 开始执行安装包
          msg = this.$t('pages.installPackagStartExecute')
        } else if (row.step == 3 && row.result == 1) {
          // 下载安装包失败
          msg = this.$t('pages.installPackagDownloadFailed')
        } else if (row.step == 4 && row.result == 0) {
          // 升级成功但未重启电脑
          msg = this.$t('pages.installPackagUpgradSucceededButNotReboot')
        } else if (row.step == 4 && row.result == 1) {
          // 静默升级或卸载中
          msg = this.$t('pages.installPackagSilentUpgradingOrUninstalling')
        } else if (row.step == 4 && row.result == 2) {
          // 该安装包已有进程在执行
          msg = this.$t('pages.installPackagAlreadyProcessExecuting')
        } else if (row.step == 4 && row.result == 3) {
          // 未以管理员权限执行安装
          msg = this.$t('pages.installPackagNotExecutedWithAdminPermission')
        } else if (row.step == 4 && row.result == 4) {
          // 上一次执行成功后未重启电脑
          msg = this.$t('pages.installPackagNotRebbotAfterLastExecutionSucceeded')
        } else if (row.step == 4 && row.result == 5) {
          // 升级中，不允许降级安装
          msg = this.$t('pages.installPackagUpgradingNotAllowedDowngrade')
        } else if (row.step == 4 && row.result == 6) {
          // 解析安装包失败
          msg = this.$t('pages.installPackagParsingFailed')
        } else if (row.step == 4 && row.result == 7) {
          // 连接服务器失败
          msg = this.$t('pages.installPackagConnectServerFailed')
        } else if (row.step == 4 && row.result == 8) {
          // 静默安装/静默提权安装失败
          msg = this.$t('pages.installPackagSilentInstallationSilentPrivilegeInstallationFailed')
        } else if (row.step == 4 && row.result == 14) {
          msg = this.$t('pages.termVersionFail')
        } else if (row.step == 4 && row.result == 15) {
          msg = this.$t('pages.termLangVerFail')
        } else if (row.step == 5 && row.result == 0) {
          // 升级成功
          msg = this.$t('pages.upgraded')
        } else if (row.step == 4 && row.result === 16) {
          msg = '安装包复制失败'
        } else if (row.step == 4 && row.result === 17) {
          msg = '获取服务器时间失败'
        } else if (row.step == 4 && row.result === 18) {
          msg = '安装包已失效'
        } else if (row.step == 4 && row.result === 19) {
          msg = '终端安装码不存在'
        }
        return msg
      }
    }
  }
}
</script>
