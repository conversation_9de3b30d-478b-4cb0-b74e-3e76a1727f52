<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="90px"
        style="width: 700px; margin-left:30px;"
      >
        <FormItem v-if="!exeOnly" :label="$t('pages.taskName')" prop="name">
          <el-input v-model="temp.name" v-trim :disabled="!formable" maxlength="30" />
        </FormItem>
        <FormItem v-if="!exeOnly" :label="$t('pages.effectiveObject')" prop="checkedIds">
          <tree-select
            ref="objectTree"
            node-key="id"
            :height="350"
            :width="468"
            multiple
            check-strictly
            is-filter
            :filter-key="terminalFilter"
            :local-search="false"
            :checked-keys="checkedKeys"
            leaf-key="terminal"
            @change="checkedIdChange"
          />
        </FormItem>
        <FormItem v-if="!exeOnly" :label="$t('table.effectiveObjectVersion')">
          <el-select v-model="versionDatas" multiple clearable filterable :placeholder="$t('pages.selectVersionInstallPackage')" @change="changeVersion">
            <el-option :label="$t('pages.allVersion1')" :value="'*.*'"/>
            <el-option v-for="(version, index) in versionOpts" :key="index" :label="version" :value="version"/>
          </el-select>
        </FormItem>
        <FormItem v-if="!exeOnly" :label="$t('pages.remark')">
          <el-input v-model="temp.remark" :disabled="!formable" maxlength="100" />
        </FormItem>
        <div v-if="exeOnly">
          <div style="">
            <el-button icon="el-icon-upload" size="small" @click="handleCreatePackage">
              {{ $t('pages.uploadInstallPackage') }}
            </el-button>
            <el-button icon="el-icon-delete" size="small" :disabled="!packageDeleteable" @click="handleDeletePack">
              {{ $t('button.delete') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="handlePackageSearch">
              {{ $t('button.refresh') }}
            </el-button>
          </div>
          <grid-table
            ref="packageList"
            :height="350"
            :show-pager="false"
            :multi-select="true"
            :col-model="packageModel"
            :row-data-api="loadPackage"
            :after-load="afterLoad2"
            @selectionChangeEnd="packageSelectChange"
          />
        </div>
        <el-card v-if="!exeOnly" :body-style="{ 'padding': '5px' }" style="margin-top: 20px">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.termInstallPackage') }}</span>
            <span style="color: #0c60a5;padding-left: 10px">
              {{ $t('pages.installPackageText') }}
            </span>
            <el-button size="mini" style="float: right;padding: 3px;margin: 0px;height: 20px;" @click="handleViewTermVersion">
              {{ $t('pages.curTermVersion') }}
            </el-button>
          </div>
          <div v-if="formable" style="">
            <el-button icon="el-icon-upload" size="small" @click="handleCreatePackage">
              {{ $t('pages.uploadInstallPackage') }}
            </el-button>
            <el-button icon="el-icon-delete" size="small" :disabled="!packageDeleteable" @click="handleDeletePack">
              {{ $t('button.delete') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="handlePackageSearch">
              {{ $t('button.refresh') }}
            </el-button>
          </div>
          <div>
            <grid-table
              ref="packageList"
              :height="240"
              :show-pager="false"
              :multi-select="true"
              :col-model="packageModel"
              :row-data-api="loadPackage"
              :after-load="afterLoad2"
              @selectionChangeEnd="packageSelectChange"
            />
          </div>
        </el-card>
      </Form>
      <div v-if="!exeOnly" slot="footer" class="dialog-footer">
        <el-button v-if="!!formable" :loading="submitting" type="primary" @click="dialogStatus==='create'?createData():updateData()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.uploadInstallPackage')"
      :visible.sync="uploadVisible"
      width="400px"
    >
      <Form ref="uploadForm" :rules="rules" :model="packageTemp" label-position="right" label-width="90px" style="width: 340px;">
        <el-row>
          <el-col :span="21">
            <FormItem :label="$t('pages.exeName')" prop="fileName">
              <el-input v-model="packageTemp.fileName" readonly/>
            </FormItem>
          </el-col>
          <el-col :span="2">
            <el-upload
              ref="upload"
              name="upload"
              action="1111"
              accept=".exe"
              :on-change="fileChange"
              :show-file-list="false"
              :auto-upload="false"
              :file-list="fileList"
              :disabled="fileSubmitting"
              :on-progress="handleProgress"
            >
              <el-button type="primary" icon="el-icon-upload" :loading="fileSubmitting||fileSaving" style="margin: 0 0 0 1px;"></el-button>
            </el-upload>
          </el-col>
        </el-row>
        <el-row v-if="fileSaving">
          <el-col :span="22">
            <el-progress type="line" :percentage="percentage"/>
          </el-col>
          <el-col :span="2">
            <el-button type="primary" icon="el-icon-switch-button" title="取消上传" @click="cancel"></el-button>
          </el-col>
        </el-row>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveFile()">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="uploadVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.curTermVersion')"
      :visible.sync="dialogTermVisible"
      width="800px"
    >
      <el-container style="height: 400px;">
        <el-aside width="180px">
          <tree-menu
            ref="groupTree"
            :data="treeData"
            :local-search="false"
            :get-search-list="getSearchListFunction"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="handleNodeClick"
          />
        </el-aside>
        <el-main>
          <div class="toolbar" style="height: 33px; margin-bottom: 7px;">
            <div class="searchCon">
              <el-input v-model="termQuery.name" v-trim clearable :placeholder="$t('pages.terminalName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFIlterTerm">
                {{ $t('table.search') }}
              </el-button>
            </div>
          </div>
          <grid-table
            ref="terminalTable"
            :height="360"
            :multi-select="false"
            :row-data-api="loadTerminalTable"
            :col-model="termColModel"
            pager-small
          />
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTermVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  uploadSoft, getPacketList, deletePack, reUploadFile, getStrategyPage,
  addTerminalUpgrade, updateTerminalUpgrade,
  getByName, getTargetObject, getValidMsg
} from '@/api/assets/systemMaintenance/terminalUpgrade'
import { enableStgBtn, enableStgDelete, selectable, objectFormatter, entityLink, refreshPage } from '@/utils'
import { getDeptTreeFromCache } from '@/api/system/terminalManage/department'
import { getTerminalPage } from '@/api/system/terminalManage/terminal'
import { addRecycleNode } from '@/utils/tree'
import axios from 'axios'
import { osTypeIconFormatter } from '@/utils/formatter'

export default {
  name: 'UpgradeStrategy',
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true }, // 能否提交表单
    versionOpts: { type: Array, default() { return [] } } // 终端版本
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'taskName2', width: '150', sort: 'custom' },
        { prop: 'objectNames', label: 'applicationObj', width: '100' },
        { prop: 'pack.fileName', label: 'installPackage', width: '200', formatter: this.packListFormatter },
        { prop: 'version', label: 'packageVersion', width: '150', formatter: this.versionFormatter },
        { prop: 'modifyTime', label: 'operateTime', width: '150', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '200' },
        { prop: 'createdTime', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      termColModel: [
        { prop: 'name', label: 'terminalName', width: '80', sort: 'custom' },
        { prop: 'id', label: 'terminalCode', width: '80', sort: 'custom' },
        { prop: 'version', label: 'terminalVersion', width: '100', sort: 'custom' }
      ],
      packageModel: [
        { prop: 'fileName', label: 'installPackageName', width: '200', sort: 'custom', iconFormatter: osTypeIconFormatter },
        { prop: 'version', label: 'installPackageVersion', width: '130', sort: 'custom' },
        { prop: 'statusInfo', label: 'uploadStatus', width: '120', sort: 'custom' },
        { prop: 'createTime', label: 'time', width: '160', sort: 'custom' },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'reUpload', click: this.reUploadFile }
          ]
        }
      ],
      osTypeMap: {
        1: 'Windows',
        2: 'Linux',
        4: 'Mac'
      },
      treeData: [],
      defaultExpandedKeys: ['G0'],
      query: { // 查询条件
        page: 1,
        strategyDefType: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      termQuery: { // 查询条件
        page: 1,
        groupId: undefined,
        name: '',
        // 默认包含子部门
        includeSubGroup: true,
        useType: undefined,
        filterType: 3
      },
      showTree: true,
      treeable: true,
      deleteable: false,
      addBtnAble: false,
      exeOnly: false, // 是否安装包管理模式
      exeTableH: 200,
      keyword: '',
      temp: {},
      defaultTemp: { // 表单字段
        id: undefined,
        name: '',
        active: false,
        remark: '',
        pack: null,
        packList: [],
        objectIds: [],
        objectGroupIds: [],
        checkedIds: []
      },
      checkedKeys: [],
      dialogFormVisible: false,
      dialogExeVisible: false,
      dialogStatus: '',
      textMap: {
        update: this.i18nConcatText(this.$t('pages.termUpgradeTask'), 'update', this.formable),
        create: this.i18nConcatText(this.$t('pages.termUpgradeTask'), 'create'),
        exe: this.$t('pages.installPackageManage')
      },
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.strategyNameValidator, trigger: 'blur' }
        ],
        fileName: [
          { required: true, message: this.$t('text.cantNull'), trigger: 'blur' }
        ],
        checkedIds: [
          { required: true, validator: this.checkedIdsValidator, trigger: 'blur' }
        ]
      },
      submitting: false,
      uploadVisible: false,
      dialogTermVisible: false,
      fileList: [],
      versionDatas: [],
      allVersion: '*.*',
      firstSelected: false,
      packageDeleteable: false,
      fileSubmitting: false,
      fileSaving: false,
      packageTemp: {}, // 表单字段
      defaultPackageTemp: {
        id: undefined,
        fileName: ''
      },
      source: null,
      percentage: 0
    }
  },
  computed: {
    gridTable() {
      return this.$parent.gridTable
    }
  },
  watch: {
    uploadVisible(val) {
      if (!val) {
        this.cancel()
      }
    }
  },
  created() {
    this.resetTemp()
    this.resetPackageTemp()
  },
  methods: {
    objectTree() {
      return this.$refs.objectTree
    },
    handleViewTermVersion() {
      this.initGroupTreeNode()
      this.dialogTermVisible = true
      this.$nextTick(() => {
        this.$refs.groupTree && this.$refs.groupTree.clearFilter()
        this.termQuery.name = ''
        this.$refs.terminalTable.execRowDataApi(this.query)
      })
    },
    initGroupTreeNode: function() {
      getDeptTreeFromCache().then(res => {
        this.treeData = addRecycleNode(res.data)
      })
    },
    getSearchListFunction() {
      const groupList = this.$store.getters.deptTreeList || []
      return groupList
    },
    handleNodeClick(data, node) {
      node.expanded = true
      this.termQuery.page = 1
      this.termQuery.groupId = data.dataId
      if (data.dataId == -2) {
        this.termQuery.groupId = undefined
        this.termQuery.useType = '1,2'
      } else if (data.dataId == 0) {
        this.termQuery.useType = undefined
      } else {
        this.termQuery.useType = '0'
      }
      this.$refs.terminalTable.execRowDataApi(this.termQuery)
    },
    loadTerminalTable(option) {
      const searchQuery = Object.assign({}, this.termQuery, option)
      return getTerminalPage(searchQuery)
    },
    connectionSource() {  // 得到取消操作需要的连接
      return axios.CancelToken.source()
    },
    cancel() {  // 取消上传
      if (this.source) {
        this.source.cancel(this.$t('pages.cancelUpload'))
      }
    },
    getPackageTable() {
      return this.$refs['packageList']
    },
    loadPackage(option) {
      option.limit = null
      return getPacketList(option)
    },
    packageSelectChange(val) {
      this.packageDeleteable = val.length > 0
    },
    afterLoad2: function(rowData, grid) {
      if (!this.temp.packList) {
        return
      }
      this.$nextTick(() => {
        rowData.forEach((item, index) => {
          for (const pack of this.temp.packList) {
            if (pack.id == item.id) {
              grid.toggleRowSelection(item)
              break
            }
          }
        })
      })
    },
    handlePackageSearch() {
      this.getPackageTable().execRowDataApi()
    },
    reUploadFile: function(row, data) {
      reUploadFile(row).then(res => {
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.upgrade_text1'),
          type: 'success',
          duration: 2000
        })
      })
    },
    fileChange(file, fileList) {
      const isLt2M = file.size / 1024 / 1024 < 1024
      if (!isLt2M) {
        this.$message({
          message: this.$t('pages.upgrade_text2'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      const fileName = file.name
      // const ext = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
      this.packageTemp.fileName = fileName
      this.fileSubmitting = false
      this.fileList.splice(0, 1, file)
    },
    handleProgress() {
      this.fileSubmitting = true
    },
    handleCreatePackage() {
      this.uploadVisible = true
      this.resetPackageTemp()
    },
    handleDeletePack() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.getPackageTable().getSelectedIds()
        deletePack({ ids: toDeleteIds.join(',') }).then(respond => {
          this.getPackageTable().execRowDataApi()
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    resetPackageTemp() {
      this.packageTemp = Object.assign({}, this.defaultPackageTemp)
      this.fileList.splice(0)
    },
    saveFile() {
      this.submitting = true
      this.fileSaving = true
      this.percentage = 0
      this.$refs['uploadForm'].validate((valid) => {
        if (valid) {
          const onUploadProgress = (progressEvent) => {
            const percent = (progressEvent.loaded / progressEvent.total * 100 || 0)
            this.percentage = parseInt(percent)
          }
          this.source = this.connectionSource()
          const cacheToken = this.source.token  // 这个是上传会话token，取消上传操作需要的参数
          // 通过 FormData 对象上传文件
          const fd = this.toFormData(this.packageTemp)
          fd.append('uploadFile', this.fileList[0].raw)
          // 发起请求
          uploadSoft(fd, onUploadProgress, cacheToken).then(res => {
            this.submitting = false
            this.fileSaving = false
            this.percentage = 0
            this.uploadVisible = false
            this.getPackageTable().execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('pages.upgrade_text3'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
            this.fileSaving = false
            this.percentage = 0
          })
        } else {
          this.submitting = false
          this.fileSaving = false
          this.percentage = 0
        }
      })
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      if (option != null && option !== undefined) {
        option.name = this.keyword
      }
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    handleFIlterTerm() {
      this.termQuery.page = 1
      this.$refs.terminalTable.execRowDataApi(this.termQuery)
    },
    handleDrag() {
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.exeOnly = false
      this.checkedKeys.splice(0)
      this.versionDatas.splice(0, this.versionDatas.length, this.allVersion)
      if (this.objectTree()) {
        this.objectTree().clearSelectedNode()
        this.objectTree().clearFilter()
      }
    },
    handleCreate(data) {
      this.resetTemp()
      if (data) {
        this.temp.packList = data
      }
      this.temp.entityType = this.query.objectType
      this.temp.entityId = this.query.objectId
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.getPackageTable().execRowDataApi()
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate: function(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      row.filterVersions && this.versionDatas.splice(0, this.versionDatas.length, ...(row.filterVersions.split(',') || [this.allVersion]))
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.firstSelected = true
      this.$nextTick(() => {
        this.getPackageTable().execRowDataApi()
        this.$refs['dataForm'].clearValidate()
        getTargetObject(row.id).then(res => {
          if (res.data) {
            this.checkedKeys = res.data
          }
        })
      })
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    formatData() {
      const rows = this.getPackageTable().getSelectedDatas()
      if (rows && rows.length > 0) {
        const failRows = rows.filter(row => row.status != 6)
        if (failRows && failRows.length > 0) {
          this.$message({
            message: this.$t('pages.upgrade_text10'),
            type: 'error',
            duration: 2000
          })
          return false
        }
      }
      if (rows.length > 1) {
        // 检查每种操作系统的安装包是否超过一个
        const osMap = {}
        rows.forEach(item => {
          const osType = osMap[item.osType]
          if (!osType) {
            osMap[item.osType] = 1
          } else {
            osMap[item.osType] = osMap[item.osType] + 1
          }
        })
        for (const key in osMap) {
          if (osMap[key] > 1) {
            this.$message({
              message: this.$t('pages.upgrade_text4'),
              type: 'error',
              duration: 2000
            })
            return false
          }
        }
      } else if (rows.length == 0) {
        this.$message({
          message: this.$t('pages.upgrade_text5'),
          type: 'error',
          duration: 2000
        })
        return false
      }
      this.temp.packList = rows
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      this.objectTree().getSelectedNode().forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          this.temp.objectIds.push(nodeData.dataId)
        } else {
          this.temp.objectGroupIds.push(nodeData.dataId)
        }
      })
      this.temp.filterVersions = this.versionDatas.includes('*.*') ? '' : this.versionDatas.join(',')
      return true
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.formatData()) {
            this.submitting = false
            return
          }
          getValidMsg(this.temp).then(res => {
            if (res.data) {
              let text = res.data
              if (text.length > 300) {
                text = text.substring(0, 299) + '...'
              }
              this.$confirmBox(`${this.$t('pages.upgrade_text6')}: <p class="p1" title="${res.data}">${text}</p>${this.$t('pages.upgrade_text7')}`, this.$t('text.prompt'), { dangerouslyUseHTMLString: true }).then(() => {
                addTerminalUpgrade(this.temp).then(() => {
                  this.submitting = false
                  this.dialogFormVisible = false
                  this.$store.dispatch('commonData/changeNotice', 'createData')
                  this.$notify({
                    title: this.$t('text.success'),
                    message: this.$t('text.createSuccess'),
                    type: 'success',
                    duration: 2000
                  })
                }).catch(res => {
                  this.submitting = false
                })
              }).catch(() => {
                this.submitting = false
              })
            } else {
              addTerminalUpgrade(this.temp).then(() => {
                this.submitting = false
                this.dialogFormVisible = false
                this.$store.dispatch('commonData/changeNotice', 'createData')
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.createSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(res => {
                this.submitting = false
              })
            }
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.formatData()) {
            this.submitting = false
            return
          }
          const tempData = Object.assign({}, this.temp)
          getValidMsg(this.temp).then(res => {
            if (res.data) {
              let text = res.data
              if (text.length > 300) {
                text = text.substring(0, 299) + '...'
              }
              this.$confirmBox(`${this.$t('pages.upgrade_text6')}: <p title="${res.data}">${text}</p>${this.$t('pages.upgrade_text8')}`, this.$t('text.prompt'), { dangerouslyUseHTMLString: true }).then(() => {
                updateTerminalUpgrade(tempData).then(() => {
                  this.submitting = false
                  this.dialogFormVisible = false
                  this.gridTable.execRowDataApi()
                  this.$notify({
                    title: this.$t('text.success'),
                    message: this.$t('text.updateSuccess'),
                    type: 'success',
                    duration: 2000
                  })
                }).catch(reason => {
                  this.submitting = false
                })
              }).catch(() => {
                this.submitting = false
              })
            } else {
              updateTerminalUpgrade(tempData).then(() => {
                this.submitting = false
                this.dialogFormVisible = false
                this.gridTable.execRowDataApi()
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.updateSuccess'),
                  type: 'success',
                  duration: 2000
                })
              }).catch(reason => {
                this.submitting = false
              })
            }
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    handleExe() {
      this.resetTemp()
      this.dialogFormVisible = true
      this.dialogStatus = 'exe'
      this.exeOnly = true
      this.$nextTick(() => {
        this.getPackageTable().execRowDataApi()
      })
    },
    changeVersion() {
      // 修改任务时，有一些任务配置的版本号 在升级之后 系统已不存在这些版本，此时配置依旧显示旧的版本号，但是下拉框已经不存在旧的版本号了，首次选择需要进行过滤
      // if (this.dialogStatus === 'update' && this.firstSelected) {
      //   for (let i = this.versionDatas.length - 1; i >= 0; i--) {
      //     if (this.versionDatas[i] !== this.allVersion && this.binarySearch(this.versionOpts, this.versionDatas[i]) === -1) {
      //       this.versionDatas.splice(i, 1)
      //     }
      //   }
      //   this.firstSelected = false
      // }
      const length = this.versionDatas.length
      const index = this.versionDatas.indexOf(this.allVersion)
      if (length === 0 || this.versionDatas[length - 1] === this.allVersion || (length === this.versionOpts.length && index == -1)) {
        this.versionDatas.splice(0, length, this.allVersion)
      } else {
        index >= 0 && this.versionDatas.splice(index, 1)
      }
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        return true
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !(type === 3 || (type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    checkedIdChange(selected) {
      this.temp.checkedIds = selected
      this.$refs['dataForm'].validateField('checkedIds')
    },
    entityFormatter: function(row, data) {
      return objectFormatter(row, data, this)
    },
    entityClick: function(row, data) {
      return entityLink(row, data, this)
    },
    refresh() {
      return refreshPage(this)
    },
    strategyNameValidator(rule, value, callback) {
      getByName({ name: value }).then(respond => {
        const role = respond.data
        if (role && role.id !== this.temp.id) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    checkedIdsValidator(rule, value, callback) {
      if (this.temp.checkedIds && this.temp.checkedIds.length > 0) {
        callback()
      } else {
        callback(this.$t('pages.behaviorGroup_text9'))
      }
    },
    binarySearch(arr, target) {
      let left = 0;
      let right = arr.length - 1;

      while (left <= right) {
        const mid = Math.floor((left + right) / 2); // 计算中间索引

        if (arr[mid] === target) {
          return mid; // 找到目标值，返回索引
        } else if (arr[mid] < target) {
          left = mid + 1; // 目标值在右侧，更新左边界
        } else {
          right = mid - 1; // 目标值在左侧，更新右边界
        }
      }

      return -1; // 未找到目标值，返回 -1
    }
  }
}
</script>
