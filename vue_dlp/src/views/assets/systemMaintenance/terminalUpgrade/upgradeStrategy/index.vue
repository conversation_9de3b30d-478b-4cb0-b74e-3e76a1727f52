<template>
  <div class="app-container">
    <div v-if="listable" class="table-container">
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate">
          {{ $t('pages.createUpgradeTask') }}
        </el-button>
        <el-button icon="el-icon-delete" size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.deleteTask') }}
        </el-button>
        <strategy-extend :scope="1"/>
        <el-button v-permission="'379'" icon="el-icon-setting" size="mini" @click="handleConfig">
          {{ $t('button.highConfig') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.validateMsg_enterName')" style="width: 200px;" @keyup.enter.native="handleFilter" />
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="strategyList"
        :col-model="colModel"
        :row-data-api="rowDataApi"
        :selectable="selectable"
        :after-load="afterLoad"
        @selectionChangeEnd="handleSelectionChange"
      />
    </div>

    <edit-dlg ref="editDlg" :version-opts="versionOpts"/>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('button.highConfig')"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <Form
        ref="dataForm"
        :model="showDialog"
        label-position="right"
        label-width="80px"
        style="width: 500px; margin-left:10px;"
      >
        <FormItem>
          <el-checkbox v-model="showDialog.value" :true-label="1" :false-label="0">
            {{ $t('pages.highConfigTip') }}
          </el-checkbox>
        </FormItem>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="saveConfig">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getStrategyPage, deleteTerminalUpgrade, updateConfig, fetchDistinctVersions
} from '@/api/assets/systemMaintenance/terminalUpgrade'
import { enableStgBtn, enableStgDelete, selectable, refreshPage } from '@/utils'
import editDlg from './editDlg'
import { getConfigByKey } from '@/api/system/configManage/globalConfig'

export default {
  name: 'UpgradeStrategy',
  components: { editDlg },
  props: {
    // 只显示详情弹窗，策略总览、策略包等界面只需要dlg，不需要list
    listable: { type: Boolean, default: true },
    formable: { type: Boolean, default: true } // 能否提交表单
  },
  data() {
    return {
      colModel: [
        { prop: 'name', label: 'taskName2', width: '150', sort: 'custom' },
        { prop: 'objectNames', label: 'effectiveObject', width: '100' },
        { label: 'effectiveObjectVersion', width: '110', formatter: this.objVersionFormatter },
        { prop: 'pack.fileName', label: 'installPackage', width: '200', formatter: this.packListFormatter },
        { prop: 'version', label: 'packageVersion', width: '150', formatter: this.versionFormatter },
        { prop: 'modifyTime', label: 'operateTime', width: '150', sort: 'custom' },
        { prop: 'remark', label: 'remark', width: '200' },
        { prop: 'createdTime', label: 'operate', type: 'button', fixed: 'right', fixedWidth: '100',
          buttons: [{
            label: 'edit', click: this.handleUpdate }
          ]
        }
      ],
      showDialog: { key: 'showDialog', isProp: false, label: this.$t('pages.highConfigTip'), value: 0 },
      query: { // 查询条件
        page: 1,
        strategyDefType: 1,
        searchInfo: undefined,
        objectType: undefined,
        objectId: undefined
      },
      deleteable: false,
      dialogVisible: false,
      submitting: false,
      // 所有终端版本（去重）放在index组件，方便后续扩展需要在表格中显示 生效对象版本
      versionOpts: []
    }
  },
  computed: {
    gridTable() {
      return this.$refs['strategyList']
    }
  },
  watch: {
    '$store.state.commonData.notice.createData'(val) {
      this.reloadTable()
    }
  },
  created() {
    this.resetVersionOpts()
  },
  methods: {
    reloadTable() {
      this.gridTable.execRowDataApi()
    },
    selectable(row, index) {
      return selectable(row, index)
    },
    afterLoad(rowData, grid) {
      enableStgBtn(rowData, this)
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getStrategyPage(searchQuery)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleCreate() {
      this.$refs.editDlg.handleCreate()
    },
    handleUpdate: function(row) {
      this.$refs.editDlg.handleUpdate(row)
    },
    handleSelectionChange(rowDatas) {
      enableStgDelete(rowDatas, this)
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const toDeleteIds = this.gridTable.getSelectedIds()
        deleteTerminalUpgrade({ ids: toDeleteIds.join(',') }).then(() => {
          this.gridTable.deleteRowData(toDeleteIds)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.deleteSuccess'),
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {})
    },
    getConfigByKey() {
      getConfigByKey({ key: 'showDialog' }).then(resp => {
        if (resp.data) {
          this.showDialog.value = parseInt(resp.data.value)
        }
      })
    },
    handleConfig() {
      this.dialogVisible = true
      this.getConfigByKey()
    },
    saveConfig() {
      const data = [this.showDialog]
      updateConfig(data).then(respond => {
        this.submitting = false
        this.dialogVisible = false
        this.$notify({ title: this.$t('text.success'), message: this.$t('text.updateSuccess'), type: 'success', duration: 2000 })
      }).catch(res => {
        this.submitting = false
      })
    },
    refresh() {
      return refreshPage(this)
    },
    packListFormatter(row, data) {
      const names = []
      if (row.packList) {
        row.packList.forEach(item => {
          names.push(`${item.fileName}`)
        })
      }
      return names.join('，')
    },
    objVersionFormatter(row, data) {
      return row.filterVersions || this.$t('pages.allVersion1')
    },
    versionFormatter(row, data) {
      let version = ''
      if (row.packList) {
        row.packList.forEach(item => {
          version = item.version
        })
      }
      return version
    },
    resetVersionOpts() {
      fetchDistinctVersions().then(res => {
        this.versionOpts.splice(0, this.versionOpts.length, ...res.data)
      })
    }
  }
}
</script>
