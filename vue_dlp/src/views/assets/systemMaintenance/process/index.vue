<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :os-type-filter="7" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" :disabled="refreshDisable" size="mini" @click="refreshProcess">
          {{ $t('button.refresh') }}
        </el-button>
        <audit-log-exporter v-permission="'150'" :request="handleExport" :disabled="refreshDisable"/>
        <el-checkbox v-model="autoRefresh" :true-label="1" :false-label="0" style="margin-left: 10px;font-weight: bold" @change="changeIntervalTime">
          {{ $t('pages.automaticRefreshTime') }}
        </el-checkbox>
        <el-input-number
          v-model="intervalTime"
          controls-position="right"
          :precision="0"
          :min="1"
          :max="60"
          :disabled="autoRefresh === 0"
          style="width:120px;"
          @blur="handleBlur"
          @change="changeIntervalTime"
        />
        <div class="searchCon">
          <el-input v-model="searchText" v-trim clearable :placeholder="$t('pages.processName1')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="processTable"
        v-loading="tableLoading"
        :col-model="colModel"
        :row-datas="rowData"
        :default-sort="defaultSort"
        :multi-select="multiSelect"
        :custom-col="true"
        :show-pager="false"
      />
    </div>
  </div>
</template>

<script>
import { getSystemProcess, updateSystemProcess, exportSystemProcess } from '@/api/assets/systemMaintenance/process'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'SystemProcess',
  data() {
    return {
      multiSelect: false,
      defaultSort: { prop: 'name', order: 'asc' },
      colModel: [
        { prop: 'name', label: 'processName', width: '150', fixed: true, sort: true },
        { prop: 'id', label: 'processID', width: '100', sort: true },
        { prop: 'cpu', label: 'cpu', width: '100', sort: true },
        { prop: 'memory', label: 'memoryKB', width: '120', sort: true, formatter: this.sizeFormatter },
        { prop: 'vmMemory', label: 'virtualMemoryKB', width: '120', sort: true, formatter: this.sizeFormatter },
        { prop: 'handleCount', label: 'handleCount', width: '120', sort: true },
        { prop: 'threadCount', label: 'threadCount', width: '120', sort: true },
        { prop: 'processUserName', label: 'processUserName', width: '150', sort: true },
        { prop: 'sessionId', label: 'sessionId', width: '120', sort: true, custom: true },
        { prop: 'priority', label: 'mainPriority', width: '120', sort: true, custom: true, formatter: this.priorityFormatter },
        { prop: 'ioReadOpCount', label: 'ioReadOpCount', width: '150', sort: true, custom: true },
        { prop: 'ioWriteOpCount', label: 'ioWriteOpCount', width: '150', sort: true, custom: true },
        { prop: 'ioReadBytes', label: 'ioReadBytes', width: '150', sort: true, custom: true },
        { prop: 'ioWriteBytes', label: 'ioWriteBytes', width: '150', sort: true, custom: true },
        { prop: 'cpuTime', label: 'cpuTime', width: '120', sort: true, custom: true },
        { prop: 'processPath', label: 'processPath', width: '180', sort: true, custom: true },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'endProcess', formatter: this.buttonFormatter, click: this.stopProcess }
          ]
        }
      ],
      searchText: '',
      rowData: [],
      tempRowData: [],
      curFilePath: [],
      showTree: true,
      refreshDisable: true,
      ctrlAble: false,
      loopAble: false,
      temp: {}, // 表单字段
      defaultTemp: {
        name: '',
        id: undefined,
        userId: undefined,
        groupIds: [],
        remark: '',
        user: {
          name: ''
        }
      },
      isLog: false, // 是否记录管理员日志，自动刷新不应该重复记录日志
      dialogFormVisible: false,
      submitting: false,
      tableLoading: false,
      timer: undefined,
      termId: undefined,
      retFinished: true,
      refreshNum: 0, // 刷新次数
      intervalTime: 30,
      autoRefresh: 1
    }
  },
  computed: {
    gridTable() {
      return this.$refs['processTable']
    },
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    }
  },
  watch: {
    tempRowData(val) {
      this.handleFilter()
    }
  },
  created() {
    this.resetTemp()
  },
  activated() {
    clearInterval(this.timer)
    if (this.autoRefresh) {
      this.timer = setInterval(() => {
        if (!this.loopAble) return
        this.refreshProcess()
      }, this.intervalTime * 1000)
    }
  },
  deactivated() {
    clearInterval(this.timer)
  },
  mounted() {
    clearInterval(this.timer)
    if (this.autoRefresh) {
      this.timer = setInterval(() => {
        if (!this.loopAble) return
        this.refreshProcess()
      }, this.intervalTime * 1000)
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80 || node.oriData != 0)
    },
    strategyTargetNodeChange(tabName, data) {
      this.tempRowData = []
      if (data) {
        this.loopAble = false
        this.termId = undefined
        if (data.id.indexOf('G' + data.dataId) < 0) {
          this.tableLoading = true
          this.termId = data.dataId
          this.refreshDisable = false
          this.retFinished = true
          this.isLog = true
          this.refreshProcess()
        } else {
          this.tableLoading = false
          this.refreshDisable = true
        }
      }
    },
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('process', termId, [2]).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.refreshDisable = true
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    refreshProcess: function() {
      const that = this
      if (!that.termId || !that.retFinished) {
        return
      }
      that.retFinished = false
      this.checkCtrlAble(that.termId).then(() => {
        if (!this.ctrlAble) {
          that.tableLoading = false
          that.retFinished = true
          return;
        }
        this.loopAble = true
        this.refreshNum++
        if (this.refreshNum > 100000) {
          this.refreshNum = 0
        }
        getSystemProcess(that.termId, that.isLog).then(respond => {
          that.tempRowData = [...respond.data]
          that.tableLoading = false
          that.retFinished = true
          that.isLog = false
        }).catch(e => {
          that.retFinished = true
          if (that.tableLoading) {
            that.tableLoading = false
          }
        })
      })
    },
    handleFilter() {
      const searchText = this.searchText.toLowerCase()
      this.rowData = this.tempRowData.filter(data => {
        const name = data.name.toLowerCase()
        return name.indexOf(searchText) > -1
      })
    },
    handleExport(exportType) {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      const columnIds = this.gridTable.getCustomColKeys().filter(key => key != 16)
      const columns = []
      columnIds.sort((a, b) => a - b)
      columnIds.forEach(item => {
        columns.push(this.colModel[item].prop)
      })
      return exportSystemProcess({ exportType, termId: curNodeData.dataId, processName: this.searchText || null, columns: columns })
    },
    stopProcess: function(row) {
      if (row.name) {
        if (row.name === 'System Idle Process' || row.name === 'System') {
          this.$message({
            duration: 2000,
            type: 'error',
            message: this.$t('pages.endProcess_Msg1')
          })
          return
        }
      }
      this.$confirmBox(this.$t('pages.systemProcess_Content1'), this.$t('text.prompt')).then(() => {
        const terminalNode = this.strategyTargetTree.getCurrentNode()
        const tempNum = this.refreshNum
        updateSystemProcess({
          termId: terminalNode.dataId,
          processId: row.id,
          // 新增“进程名” 用途于管理员日志的参数显示更为全面
          processName: row.name
        }).then(respond => {
          if (respond.data) {
            this.$notify({ title: this.$t('text.success'), message: this.$t('pages.systemProcess_Content2'), type: 'success', duration: 2000 })
            // 次数比对是为了防止删除到已刷新的数据
            if (tempNum === this.refreshNum) {
              const index = this.tempRowData.findIndex(r => r.id == row.id)
              index >= 0 && this.tempRowData.splice(index, 1)
              this.handleFilter()
            }
          } else {
            this.refreshProcess()
            this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.systemProcess_Content3'), type: 'error', duration: 2000 })
          }
        })
      }).catch(() => {})
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    priorityFormatter(row, data) {
      if (data === 0) {
        return this.$t('pages.null')
      } else if (data === 1) {
        return this.$t('pages.process_low_free')
      } else if (data === 2) {
        return this.$t('pages.process_low_normal')
      } else if (data === 3) {
        return this.$t('pages.process_normal')
      } else if (data === 4) {
        return this.$t('pages.process_high_normal')
      } else if (data === 5) {
        return this.$t('pages.process_high')
      } else if (data === 6) {
        return this.$t('pages.process_high_real_time')
      } else {
        return null
      }
    },
    sizeFormatter: function(row, data) {
      return data / 1024
    },
    buttonFormatter: function(row, data) {
      if (['ldbusiness_32.exe', 'ldbusiness32.exe'].indexOf(row.name.toLowerCase()) >= 0) {
        return ''
      } else {
        return this.$t('pages.endProcess')
      }
    },
    handleBlur(event) {
      const val = event.target.value.trim()
      if (!val) {
        this.intervalTime = 1
      }
    },
    changeIntervalTime() {
      clearInterval(this.timer)
      if (this.autoRefresh) {
        this.timer = setInterval(() => {
          if (!this.loopAble) return
          this.refreshProcess()
        }, this.intervalTime * 1000)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
>>>.el-checkbox__label {
  padding-left: 0;
}
</style>
