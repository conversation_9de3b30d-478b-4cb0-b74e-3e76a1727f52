<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" :os-type-filter="7" :terminal-filter-key="terminalFilter" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <el-tabs
        ref="tabs"
        v-model="activeName"
        type="card"
        style="padding:0 10px 10px 0"
        @tab-click="tabClick"
      >
        <el-tab-pane v-for="(item, index) in tabList" :key="index" :label="item.label" :name="item.name">
          <div class="table-container">
            <div class="toolbar">
              <el-button type="primary" size="mini" @click="toggleTreeMenu">
                <svg-icon icon-class="tree" />
              </el-button>
              <el-button type="primary" icon="el-icon-refresh" size="mini" :disabled="refreshDisable" @click="refreshInfo">
                {{ $t('button.refresh') }}
              </el-button>
              <audit-log-exporter v-permission="'153'" :request="handleExport" :disabled="refreshDisable"/>
              <div class="searchCon">
                <el-input
                  v-model="query[item.query]"
                  v-trim
                  clearable
                  :placeholder="item.placeholder"
                  style="width: 160px"
                  @keyup.enter.native="handleFilter"
                ></el-input>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
                  {{ $t('table.search') }}
                </el-button>
              </div>
            </div>
            <grid-table
              :ref="item.name"
              v-loading="tableLoading"
              :col-model="item.colModel"
              :row-data-api="rowDataApi"
              :default-sort="item.defaultSort"
              :show-pager="false"
              :multi-select="multiSelect"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="textTitle"
      :visible.sync="dialogFormVisible"
      width="800px"
      @dragDialog="handleDrag"
    >
      <grid-table
        ref="shareObjTable"
        :height="300"
        :col-model="objColModel"
        :row-datas="objRowData"
        :default-sort="objDefaultSort"
        :multi-select="multiSelect"
        :show-pager="false"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.close') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  exportSystemShare,
  exportSystemShareOpenFile,
  exportSystemShareSession,
  getSystemShare,
  getSystemShareObj,
  getSystemShareOpenFile,
  getSystemShareSession,
  updateSystemShare
} from '@/api/assets/systemMaintenance/share'
import { ctrlErrorMap, enableCtrlTerm } from '@/api/system/terminalManage/moduleConfig'

export default {
  name: 'ShareManage',
  data() {
    return {
      tabList: [
        { label: this.$t('pages.shareInfo'), name: 'shareInfo', query: 'fileName', placeholder: this.$t('table.shareName'), defaultSort: { prop: 'fileName' },
          colModel: [
            { prop: 'fileName', label: 'shareName', width: '150', fixed: true, sort: true },
            { prop: 'accessNum', label: 'shareUserNum', width: '100', sort: true },
            { prop: 'path', label: 'sharePath', width: '150', sort: true },
            { prop: 'currentUses', label: 'curShareClientNum', width: '150', sort: true },
            { prop: 'describe', label: 'fileDescription', width: '150', sort: true },
            { label: 'operate', type: 'button', fixedWidth: '200', fixed: 'right',
              buttons: [
                { label: 'viewShareObj', click: this.handleView },
                { label: 'cancelShare', click: this.handleCancel }
              ]
            }
          ]
        },
        { label: this.$t('pages.sessionInfo'), name: 'sessionInfo', query: 'userName', placeholder: this.$t('pages.user1'), defaultSort: { prop: 'userName' },
          colModel: [
            { prop: 'userName', label: this.$t('pages.user1'), width: '150', fixed: true, sort: true },
            { prop: 'openedNum', label: 'shareSessionOpenFile', width: '100', sort: true },
            { prop: 'connectTime', label: 'connectTime', width: '150', sort: true, formatter: this.secondsFormatter },
            { prop: 'idleTime', label: 'freeTime', width: '150', sort: true, formatter: this.secondsFormatter }
          ]
        },
        { label: this.$t('pages.openFileInfo'), name: 'openFileInfo', query: 'filePath', placeholder: this.$t('pages.openFile'), defaultSort: { prop: 'filePath' },
          colModel: [
            { prop: 'filePath', label: this.$t('pages.openFile'), width: '150', fixed: true, sort: true },
            { prop: 'userName', label: 'visitor', width: '100', sort: true },
            { prop: 'locks', label: 'shareOpenFileLock', width: '150', sort: true },
            { prop: 'permissions', label: 'shareOpenFilePermission', width: '150', sort: true, formatter: this.permissionsFormatter }
          ]
        }
      ],
      multiSelect: false,
      objDefaultSort: { prop: 'userName', order: 'desc' },
      objColModel: [
        { prop: 'userName', label: 'groupUser', width: '150', fixed: true, sort: true },
        { prop: 'competence', label: 'jurisdiction', width: '150', sort: true, formatter: this.competenceFormatter }
      ],
      rowData: [],
      objRowData: [],
      showTree: true,
      ctrlAble: false,
      refreshDisable: true,
      query: {
        fileName: '',
        userName: '',
        filePath: ''
      },
      temp: {}, // 表单字段
      defaultTemp: {
        shareFileName: '',
        accessNum: 0,
        sharePath: '',
        shareObjects: []
      },
      competenceOptions: {
        1: this.$t('pages.completeControl'),
        2: this.$t('pages.readable'),
        3: this.$t('pages.readableWritable'),
        4: this.$t('pages.refuseFullControl'),
        5: this.$t('pages.readDenied'),
        6: this.$t('pages.refuseWrite')
      },
      dialogFormVisible: false,
      submitting: false,
      textTitle: this.$t('pages.viewSharedObjects'),
      tableLoading: false,
      activeName: 'shareInfo'
    }
  },
  computed: {
    gridTable() {
      const curTab = this.getCurTab()
      return this.$refs[curTab.name][0]
    },
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    },
    queryApi() {
      return this.activeName === 'shareInfo' ? getSystemShare : this.activeName === 'sessionInfo' ? getSystemShareSession : getSystemShareOpenFile
    },
    exportApi() {
      return this.activeName === 'shareInfo' ? exportSystemShare : this.activeName === 'sessionInfo' ? exportSystemShareSession : exportSystemShareOpenFile
    }
  },
  watch: {
    rowData: {
      deep: true,
      handler(val) {
        this.gridTable && this.gridTable.execRowDataApi()
      }
    }
  },
  created() {
    this.resetTemp()
  },
  methods: {
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('share', termId, [2], this.getCurTab().name !== 'shareInfo' ? '5.01' : '').then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.refreshDisable = true
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    rowDataApi(options) {
      const curTab = this.getCurTab()
      const searchInfo = (this.query[curTab.query] || '').toLowerCase()
      return new Promise((resolve, reject) => {
        resolve({ code: 20000, data: searchInfo ? this.rowData.filter(data => data[curTab.query].toLowerCase().includes(searchInfo)) : this.rowData })
      })
    },
    strategyTargetNodeChange(tabName, data) {
      this.rowData = []
      if (!data || data.id.indexOf('G' + data.dataId) > -1) {
        this.refreshDisable = true
        return
      }
      this.checkCtrlAble(data.dataId).then(() => {
        if (!this.ctrlAble) return
        this.refreshDisable = false
        this.tableLoading = true
        const queryObj = this.getQueryObj()
        queryObj.termId = data.dataId;
        this.queryApi(queryObj).then(respond => {
          this.rowData = []
          this.tableLoading = false
          respond.data.forEach((data, index) => {
            data.id = new Date().getTime() + index // 添加行ID
            this.rowData.push(data)
          })
        }).catch(e => {
          if (this.tableLoading) {
            this.tableLoading = false
          }
        })
      })
    },
    refreshInfo: function() {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      this.strategyTargetNodeChange(null, curNodeData)
    },
    handleDrag() {
    },
    handleExport() {
      const curNodeData = this.strategyTargetTree.getCurrentNode()
      const queryObj = this.getQueryObj()
      queryObj.termId = curNodeData.dataId;
      return this.exportApi(queryObj)
    },
    handleCancel: function(row) {
      this.$confirmBox(this.$t('pages.share_Msg1'), this.$t('text.prompt')).then(() => {
        const terminalNode = this.strategyTargetTree.getCurrentNode()
        updateSystemShare({
          termId: terminalNode.dataId,
          fileName: row.fileName
        }).then(respond => {
          if (respond.data) {
            this.$notify({ title: this.$t('text.success'), message: this.$t('pages.share_Msg2'), type: 'success', duration: 2000 })
            const index = this.rowData.findIndex(item => item.id == row.id)
            index >= 0 && this.rowData.splice(index, 1)
          } else {
            this.refreshInfo()
            this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.share_Msg3'), type: 'error', duration: 2000 })
          }
        })
      }).catch(() => {})
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
    },
    handleView: function(row) {
      const that = this
      const terminalNode = that.strategyTargetTree.getCurrentNode()
      that.objRowData.splice(0, that.objRowData.length)
      getSystemShareObj(terminalNode.dataId, row.fileName).then(respond => {
        if (respond.data) {
          that.objRowData.splice(0, that.objRowData.length, ...respond.data)
        }
        this.dialogFormVisible = true
      })
    },
    competenceFormatter: function(row, data) {
      return this.competenceOptions[data]
    },
    handleFilter() {
      this.gridTable && this.gridTable.execRowDataApi()
    },
    tabClick(tab, event) {
      this.refreshInfo()
    },
    getQueryObj() {
      return this.activeName === 'shareInfo' ? { fileName: this.query.fileName } : this.activeName === 'sessionInfo' ? { userName: this.query.userName } : { filePath: this.query.filePath }
    },
    getCurTab() {
      return this.tabList.find(item => item.name === this.activeName)
    },
    secondsFormatter(row, seconds) {
      let dd;
      let hh;
      let mm;
      let ss;
      if (seconds === undefined || seconds < 0) {
        return '';
      }
      // 得到天
      dd = (seconds / (24 * 60 * 60)) | 0
      seconds = parseInt(seconds) - dd * (24 * 60 * 60);
      if (parseInt(dd) > 0) {
        dd = dd + this.$t('pages.day') + ' ';
      } else {
        dd = ''
      }
      // 得到小时
      hh = seconds / 3600 | 0;
      seconds = parseInt(seconds) - hh * 3600;
      if (parseInt(hh) < 10) {
        hh = '0' + hh;
      }
      // 得到分
      mm = seconds / 60 | 0;
      if (parseInt(mm) < 10) {
        mm = '0' + mm;
      }
      // 得到秒
      ss = parseInt(seconds) - mm * 60;
      if (ss < 10) {
        ss = '0' + ss;
      }
      return dd + hh + ':' + mm + ':' + ss;
    },
    // 打开模式，仅第一位为1，读取; 仅第二位为1，写入; 第一位和第二位都为1，写入 + 读取; 第一位和第二位都为0，不能访问;
    permissionsFormatter(row, permissions) {
      if ((permissions & 1) === 1 && (permissions & 2) === 2) {
        return this.$t('pages.readWrite')
      } else if ((permissions & 1) === 1) {
        return this.$t('pages.read1')
      } else if ((permissions & 2) === 2) {
        return this.$t('pages.write1')
      } else if ((permissions & 1) === 0 && (permissions & 2) === 0) {
        return this.$t('pages.notAccess')
      } else {
        return permissions;
      }
    }
  }
}
</script>
