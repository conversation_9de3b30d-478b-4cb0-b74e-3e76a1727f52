<template>
  <div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="title"
      :visible.sync="dialogFormVisible"
      width="650px"
      @dragDialog="handleDrag"
    >
      <Form ref="dataForm" label-position="right" label-width="80px" style="margin-left:20px;margin-right: 20px;">
        <FormItem :label="$t('pages.effectiveObject')" class="required">
          <tree-select
            ref="terminalTree"
            node-key="id"
            :height="350"
            :width="450"
            :local-search="true"
            is-filter
            :multiple="true"
            check-strictly
            :collapse-tags="true"
            :leaf-key="'terminal'"
            :filter-key="terminalFilter"
            @change="entityIdChange"
          />
        </FormItem>
        <FormItem label-width="0px" style="margin-top: 10px;">
          <el-checkbox v-model="childSelectModel" :true-label="1" :false-label="0" style="margin-left: 5px;">
            <span style="font-size: 15px;">{{ $t('pages.cmdCommand_Msg45') }}</span>
            <el-tooltip class="item" effect="dark" placement="bottom">
              <div slot="content">
                {{ $t('pages.cmdCommand_Msg49') }}
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
        </FormItem>
      </Form>
      <div style="display: flex;margin-top: 10px;">
        <div style="white-space: pre-wrap;color: #2b7aac;width: 50px">{{ $t('text.prompt') }}：</div>
        <div style="white-space: pre-wrap;color: #2b7aac;">
          <span>1 {{ $t('pages.cmdCommand_Msg46') }}</span><br><br>
          <span>2 {{ $t('pages.cmdCommand_Msg47') }}</span>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="handleAdd()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>  
</template>  
  
<script>
import { enableCtrlMultipleTerm } from '@/api/system/terminalManage/moduleConfig'
export default {
  name: 'QuickTree',
  props: {
  },
  data() {
    return {
      dialogFormVisible: false,
      title: this.$t('pages.cmdCommand_Msg51'),
      childSelectModel: 0,
      checkedNodeData: [],
      submitting: false
    };
  },
  created() {
  },
  methods: { 
    getTerminalAndChildrenNodes(tree) {  
      var nodes = [];  
      function traverse(node) {  
        // 获取在线windows终端节点
        if (node.id && node.id.includes('T') && node.online && Number.parseInt(node.dataType) === 0) {   
          nodes.push(node);  
        }  
        if (node.children && node.children.length > 0) {  
          node.children.forEach(child => traverse(child));  
        }  
      }    
      traverse(tree); // 从根节点开始遍历  
      return nodes;  
    },
    getTerminalNodes(tree) {
      const nodes = []
      if (tree.children && tree.children.length > 0) {
        tree.children.forEach(node => {
          if (node.id && node.id.indexOf('T') > -1 && node.online && Number.parseInt(node.dataType) === 0) {
            nodes.push(node)
          }
        })
      }
      return nodes;
    },
    async handleAdd() {
      if (this.checkedNodeData.length === 0) {
        this.$message({
          title: this.$t('text.error'),
          message: this.$t('pages.behaviorGroup_text9'),
          type: 'error',
          duration: 2000
        })
        return
      }
      this.submitting = true
      var connectionNodes = []
      this.checkedNodeData.forEach(node => {
        // 节点分为终端节点和分组节点，终端节点直接加入，分组节点则需要进行遍历去获取分组下的终端
        if (node.id.indexOf('G') > -1) {
          // 获取整棵树的数据
          const treeData = this.$refs.terminalTree.treeData
          // 找到当前勾选的节点数据
          const currentNode = this.$refs.terminalTree.findNode(treeData, node.id, 'id')
          // 根据过滤方式拿到已上线的终端节点
          var terminalNodes = ''
          this.childSelectModel === 0 ? terminalNodes = this.getTerminalNodes(currentNode) : terminalNodes = this.getTerminalAndChildrenNodes(currentNode)
          // 加入前判断数据是否重复
          terminalNodes.forEach(data => {
            if (!connectionNodes.find(item => Number.parseInt(item.dataId) === Number.parseInt(data.dataId))) {
              connectionNodes.push(data)
            }
          })
        } else {
          if (!connectionNodes.find(item => Number.parseInt(item.dataId) === Number.parseInt(node.dataId))) {
            connectionNodes.push(node)
          }
        }
      })
      // 获取符合连接条件的终端
      const ids = []
      connectionNodes.forEach(node => {
        ids.push(Number.parseInt(node.dataId))
      })
      const resp = await enableCtrlMultipleTerm('cmdCommand', ids, [1, 2, 3])
      const enableStatusMap = resp.data
      const enableTermIds = []
      const disableTermIds = []
      for (const termId in enableStatusMap) {
        const status = enableStatusMap[termId]
        if (status > 0) {
          enableTermIds.push(Number.parseInt(termId))
        } else {
          disableTermIds.push(termId)
        }
      }
      // 过滤不符合最终条件的终端
      connectionNodes = connectionNodes.filter(node => enableTermIds.includes(Number.parseInt(node.dataId)))
      if (connectionNodes.length === 0) {
        this.$message({
          title: this.$t('text.error'),
          message: this.$t('pages.cmdCommand_Msg48'),
          type: 'error',
          duration: 2000
        })
        this.submitting = false
        return
      }
      this.$emit('checkedAfter', connectionNodes)
      this.submitting = false
      this.dialogFormVisible = false
    },
    entityIdChange(key, nodeData) {
      this.checkedNodeData = [...nodeData]
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80 || node.oriData != 0)
    },
    handleDrag() {

    },
    checkedNodeChange(data, node) {

    },
    show() {
      this.dialogFormVisible = true
      this.childSelectModel = 0
      this.checkedNodeData = []
      this.$nextTick(() => {
        this.$refs.terminalTree.clearFilter()
        this.$refs.terminalTree.clearSelectedNode()
      })
    }
  }
};
</script>
  
<style lang='scss' scoped>

</style>
