<template>
  <div style="margin-top: 15px;">
    <div class="execute-container">
      <div class="info-container">
        <div style="color: #66b1ff;">
          <label>[{{ $t('pages.cmdCommand_Msg11') }}]</label>
          <label style="margin-left: 15px;">{{ terminalName }}</label>
        </div>
        <div style="margin-top: 15px;">
          <label>[{{ $t('pages.cmdCommand_Msg12') }}]</label>
          <label style="margin-left: 15px;">{{ commandList }}</label>
        </div>
      </div>
      <div class="input-container">  
        <el-input
          ref="inputRef"
          v-model="cmdCommand"
          type="text"
          :placeholder="$t('pages.cmdCommand_Msg16')" 
          :disabled="inputAble"
          :maxlength="256"
          @keydown.native="handleUpArrow"
          @keyup.enter.native="executeCommand"
        >
        </el-input>
        <el-button class="cmd-button" type="primary" :icon="executeCmdIcon" size="mini" :disabled="executeAble" style="margin-left: 3px;" @click="executeCommand">{{ executeCmdLabel }}</el-button> 
        <el-button :loading="closeExecuLoading" class="cmd-button" type="primary" icon="el-icon-scissors" size="mini" :disabled="closeExecuAble" @click="closeExecuteCommand">{{ $t('pages.cmdCommand_Ms10') }}</el-button>  
        <el-button class="cmd-button" type="primary" icon="el-icon-close" size="mini" :disabled="executeAble" @click="clearResult">
          {{ $t('pages.cmdCommand_Msg28') }}
          <el-tooltip class="item" effect="dark" placement="bottom-start">
            <div slot="content">{{ $t('pages.cmdCommand_Msg33') }}</div>
            <i class="el-icon-info" />
          </el-tooltip>
        </el-button> 
        <div style="display: flex; align-items: center;width: 500px;;">
          <el-checkbox v-model="multipleSendMode" :true-label="1" :false-label="0" style="margin-left: 5px;">
            <span style="font-size: 15px;">{{ $t('pages.cmdCommand_Msg37') }}</span>
            <el-tooltip class="item" effect="dark" placement="bottom">
              <div slot="content">
                {{ $t('pages.cmdCommand_Msg30') }}<br>
                {{ $t('pages.cmdCommand_Msg31') }}<br>{{ $t('pages.cmdCommand_Msg15') }}<br>
                {{ $t('pages.cmdCommand_Msg32') }}{{ $t('pages.cmdCommand_Msg34') }}{{ $t('pages.cmdCommand_Msg33') }}</div>
              <i class="el-icon-info" />
            </el-tooltip>
          </el-checkbox>
        </div>
      </div>  
    </div>
    <div class="cmd-container">
      <div ref="output" class="cmd-output">
        <div v-for="(line, index) in outputLines" :key="index" class="output-line">
          <pre>{{ line }}</pre>
        </div>
      </div>
    </div>
  </div>  
</template>  

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'CmdResult',
  props: {
    webSocketKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      executeCmdLabel: '',
      startExecuteCmdLabel: this.$t('pages.cmdCommand_Msg17'),
      doingExecuteCmdLabel: this.$t('pages.cmdCommand_Msg18'),
      executeCmdIcon: '',
      startExecuteCmdIcon: 'el-icon-video-play',
      doingExecuteCmdIcon: 'el-icon-video-pause',
      outputLines: [],
      result: {},
      cmdCommand: '',
      terminalName: '',
      commandList: '',
      inputAble: false,
      executeAble: false,
      closeExecuAble: true,
      closeExecuLoading: false,
      multipleSendMode: 0,
      secondSendFlag: false
    };
  },
  computed: {
    ...mapGetters([
      'userId'
    ])
  },
  watch: {
    
  },
  created() {
    this.executeCmdIcon = this.startExecuteCmdIcon
    this.executeCmdLabel = this.startExecuteCmdLabel
  },
  methods: { 
    handleUpArrow(event) {
      if (event.key === 'ArrowUp') {
        // 在这里处理向上键的逻辑
        this.$emit('keyboardUpOrDown', 'ArrowUp')
      } else if (event.key === 'ArrowDown') {
        // 在这里处理向下键的逻辑
        this.$emit('keyboardUpOrDown', 'ArrowDown')
      }
    },
    setCmdCommand(data) {
      this.cmdCommand = data
    },
    setResultObject(data, outputLines) {
      this.result = data
      this.initResultToCmd(data)
      this.outputLines = [...outputLines]
    },
    switchCmdDlg(row, outputLines) {
      this.initResultToCmd(row)
      this.outputLines = [...outputLines]
      this.$nextTick(() => {  
        this.$refs.output.scrollTop = this.$refs.output.scrollHeight;  
      });
    },
    inputFocus() {
      this.$nextTick(() => {
        if (!this.inputAble) {
          this.$refs.inputRef.focus()
        }
      })
    },
    switchBtnState(state) {
      if (state === 0 || state === 2 || state === 3) {
        this.executeAble = false
        this.inputAble = false
        this.closeExecuAble = true
        this.executeCmdIcon = this.startExecuteCmdIcon
        this.executeCmdLabel = this.startExecuteCmdLabel
      } else {
        this.executeAble = true
        this.closeExecuAble = false
        this.inputAble = true
      }
    },
    disableExecute() {
      this.executeAble = true
      this.closeExecuAble = true
    },
    clearCmdCommand() {
      this.cmdCommand = ''
    },
    setOutputLines(data) {
      this.outputLines = [...data]
      this.$nextTick(() => {  
        this.$refs.output.scrollTop = this.$refs.output.scrollHeight;  
      });
    },
    initResultToCmd(data) {
      this.terminalName = data.label
      this.commandList = data.commandList
    },
    executeCommand() {
      if (!this.cmdCommand) {
        this.$message({
          title: this.$t('text.error'), message: this.$t('pages.cmdCommand_Msg19'), type: 'error', duration: 2000
        })
        return
      }
      // 向终端发送命令请求后，在等待结果返回的过程中，不可在发送其它命令但不包括二次命令
      this.executeAble = true
      this.closeExecuAble = false
      this.inputAble = true
      this.executeCmdLabel = this.doingExecuteCmdLabel
      this.executeCmdIcon = this.doingExecuteCmdIcon
      // TODO 这边还需要判断是不是二次输入命令
      this.$emit('executeCommand', this.multipleSendMode, this.cmdCommand)
    },
    closeExecuteCommand() {
      this.$confirmBox(this.$t('pages.cmdCommand_Msg27'), this.$t('text.prompt')).then(() => {
        // 中断命令执行
        this.closeExecuLoading = true
        this.$emit('closeExecuteCommand', this.multipleSendMode)
      })
    },
    clearCloseExecuLoading() {
      this.closeExecuLoading = false
    },
    clearResult() {
      this.$confirmBox(this.$t('pages.cmdCommand_Msg20'), this.$t('text.prompt')).then(() => {
        // 清空执行结果
        this.$emit('clearResult', this.multipleSendMode)
      })
    }
  }
};
</script>

<style lang='scss' scoped>
.cmd-container {
  font-family: 'Courier New', Courier, monospace;
  color: white;
  background-color: #000;
  position: relative;
  height: 400px;
  border: 1px solid #333; 
  overflow: hidden; /* 防止cmd容器内部滚动 */
  display: flex;
  flex-direction: column;
}

.cmd-output {
  overflow-y: auto; /* 启用垂直滚动条 */
  flex-grow: 1; /*  占据剩余空间 */
  padding: 10px; 
  border-bottom: 1px solid #333;
}

.output-line {
  white-space: pre-wrap; /* 保持空格和换行 */
}
.input-container {  
  margin-top: 9px;
  display: flex;  
  align-items: center; /* 垂直居中 */  
  // background-color: #222;
  padding: 5px; 
  border-bottom: 1px solid #333; 
}  
  
input[type="text"] {  
  flex: 1; /* 占据剩余空间 */  
  /* background-color: #333; */
  background-color: #333;
  color: #ddd;   
  border: none; 
  padding: 8px;   
  font-size: 14px; 
  font-family: 'Courier New', Courier, monospace; 
  outline: none; /* 去除点击时的轮廓 */  
}  
</style>
