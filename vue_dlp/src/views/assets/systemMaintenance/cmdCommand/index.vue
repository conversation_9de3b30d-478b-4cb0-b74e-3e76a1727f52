<template>
  <div class="app-container">
    <div class="tree-container" :class="showTree ? '':'hidden'">
      <strategy-target-tree
        ref="terminalTree"
        :os-type-filter="7"
        :showed-tree="['terminal']"
        :resizeable="true"
        :terminal-filter-key="terminalFilter"
        @data-change="dataChange"
      />
    </div>
    <div class="table-container">
      <!-- width: 100%; height: calc(100% - 200px);overflow: auto; -->
      <div v-if="cmdShowFlag" style="overflow: auto;">
        <cmd-result
          ref="cmdDlg"
          @executeCommand="executeCommand"
          @clearResult="clearResult"
          @closeExecuteCommand="closeExecuteCommand"
          @keyboardUpOrDown="keyboardUpOrDown"
        >
        </cmd-result>
      </div>
      <div class="toolbar" style="margin-top: 5px;">
        <el-button v-if="connectBtnShow" type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-checkbox v-if="hasPermission('595') && cmdGrantAbled && connectBtnShow" v-model="dlgShowOption" :disabled="dlgShowDisabled" :true-label="1" :false-label="0" style="margin-left: 5px;">
          {{ $t('pages.cmdCommand_Msg50') }}
          <!-- <el-tooltip class="item" effect="dark" placement="bottom">
            <div slot="content">
              {{ $t('pages.cmdCommand_Msg41') }}
            </div>
            <i class="el-icon-info" />
          </el-tooltip> -->
        </el-checkbox>
        <el-button v-if="connectBtnShow" type="primary" :icon="openOrCloseBtnIcon" size="mini" :disabled="connectionBtnDisabled" @click="sendConnectionRequest">
          {{ openOrCloseBtnLabel }}
        </el-button>
        <el-button v-if="connectBtnShow" type="primary" icon="el-icon-delete" size="mini" :disabled="deleteDisabled" @click="deleteRowDatas">
          {{ $t('button.delete') }}
        </el-button>
        <el-button v-if="connectBtnShow" type="primary" icon="el-icon-setting" size="mini" :disabled="quickConfigDisabled" @click="quickConfig">
          {{ $t('pages.cmdCommand_Msg43') }}
        </el-button>
        <el-button v-if="!connectBtnShow && dlgShowOption === 0" type="primary" icon="el-icon-close" size="mini" @click="stopConnection">
          {{ $t('pages.cmdCommand_Msg39') }}
        </el-button>
      </div>
      <grid-table
        ref="terminalTable"
        v-loading="loadingShow"
        :element-loading-text="loadingText"
        :v-loading="true"
        :col-model="colModel"
        :row-datas="rowDatas"
        :multi-select="true"
        :show-pager="false"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <quick-tree
      ref="cmdQuickTree"
      @checkedAfter="checkedAfter"
    >
    </quick-tree>
  </div>
</template>

<script>
import QuickTree from './quickTree.vue'
import { mapGetters } from 'vuex'
import CmdResult from './cmdResult.vue'
import { connectionCmd, heartbeatOrCloseCmd, sendCmdCommand, terminalCloseConnection, cmdGrantAuth, stopConnection } from '@/api/assets/systemMaintenance/cmdCommand'
import { ctrlErrorMap, enableCtrlMultipleTerm } from '@/api/system/terminalManage/moduleConfig'
export default {
  name: 'CmdCommand',
  components: { CmdResult, QuickTree },
  data() {
    return {
      showTree: true,
      cmdShowFlag: false,                             // 命令执行框是否可以显示
      rowDatas: [],
      colModel: [
        { prop: 'label', label: 'terminalName', width: '150', sort: true },
        { prop: 'connectionState', label: 'connectionState', width: '150', sort: true, formatter: this.connectionStateFormatter },
        { prop: 'executeState', label: 'executeState', width: '150', sort: true, formatter: this.executeStateFormatter },
        { label: 'operate', type: 'button', fixedWidth: '150', fixed: 'right',
          buttons: [
            { label: 'view', click: this.viewResult, isShow: this.isShowViewResultFormat }
          ]
        }
      ],
      dlgShowDisabled: false,                         // 不询问，直接控制终端计算机的配置项是否可勾选
      connectionBtnDisabled: true,                    // 连接按钮是否可点击
      deleteDisabled: true,                           // 删除按钮是否可点击
      openOrCloseBtnIcon: null,
      openOrCloseBtnLabel: null,
      openIcon: 'el-icon-open',
      closeIcon: 'el-icon-switch-button',
      doingIcon: 'el-icon-more',
      openLabel: this.$t('pages.cmdCommand_Msg2'),
      closeLabel: this.$t('pages.cmdCommand_Msg3'),
      doningLabel: this.$t('pages.cmdCommand_Msg4'),
      dlgShowOption: 0,                                 // 不询问，直接控制终端计算机的配置项对应的值
      connectionData: [],                               // 发起连接返回的结果
      resultData: [],                                   // 命令执行返回的执行结果
      tempResultData: [],                               // 存储格式化完成的终端
      doingExecuteData: [],                             // 存储执行结果尚未全部返回的终端
      executeData: [],                                  // 存储格式化完成的命令执行结果
      webSocketKey: '',                                 // 接收webSocket的key
      connectionFirstTime: '',                          // 使用建立连接按钮发起连接的时间
      heartbeatTime: 60,                                // 心跳超时时间
      checkTerminal: [],                                // 追踪的终端信息
      cmdCommandList: [],                               // 命令执行列表，存储终端最近一直执行的命令
      lastHearbeatFlag: false,                          // 上一次心跳处理是否结束
      requestTime: 35,                                  // 请求超时时间
      sendHearbeatLastTime: '',
      closeExecuteStatus: false,                        // 是否处于中断命令执行的状态
      clsoeExecuteTerminalNum: 0,
      hearbeatData: [],                                 // 存储心跳返回结果
      quickConfigDisabled: false,                       // 快速配置按钮是否可用
      resultFlag: false,                                // 上一次的命令执行结果是否处理完成
      currentTerminal: {},                              // 当前命令执行框对应的终端信息
      interruptData: [],                                // 需要中断命令执行的终端
      interruptResultData: [],                          // 存储中断结果
      interruptTime: '',                                // 发起中断的时间
      nodeInfo: [],                                     // 存储点击的节点信息
      loadingText: this.$t('pages.cmdCommand_Msg38'),
      loadingShow: false,
      connectBtnShow: true,                            // 停止连接时，按钮栏是否显示
      waitConnectionFlag: false,                       // 避免多次点击向终端发送连接请求
      waitConnectionFlagTime: '',                      // 标志位上一次变为true的时间， 避免哪里出错导致标志位未重置
      waitConnectionData: [],                          // 目前还在等待连接响应的终端
      tableDataMultiFlag: false,                       // 避免多次点击在表格内添加多条信息
      cmdGrantAbled: false,
      firstConnectionData: [],                        // 初次建立连接时，发起连接的终端
      firstConnectionEndData: [],                     // 初次建立连接时，完成响应的终端
      firstConnectionFlag: false,                     // 初次建立连接时，是否可以进入方法体处理连接结果
      cmdCommandPos: -1,                               // 当前执行命令在该终端的历史命令列表里的位置
      keyWordFlag: false                              // 键盘切换事件是否执行完毕
    }
  },
  computed: {
    ...mapGetters([
      'userId'
    ]),
    terminalTree() {
      return this.$refs['terminalTree']
    },
    terminalTable() {
      return this.$refs['terminalTable']
    }
  },
  watch: {
    rowDatas() {
      // 表格为空，但是还有终端未响应连接请求，则需要加载loadind框
      if (this.rowDatas.length === 0 && (this.firstConnectionData.length > 0 || this.waitConnectionData.length > 0)) {
        this.loadingShow = true
        this.connectBtnShow = false
        this.showTree = false
      }
      // 根据表格数据过滤终端历史执行命令
      if (this.rowDatas.length === 0) {
        this.cmdCommandList = []
      } else {
        const ids = []
        this.rowDatas.forEach(row => ids.push(row.id))
        this.cmdCommandList = this.cmdCommandList.filter(cmd => ids.includes(cmd.termId))
      }
    },
    currentTerminal() {
      this.cmdCommandPos = -1
    }
  },
  mounted() {
    // 监听刷新浏览器
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  },
  created() {
    this.openOrCloseBtnIcon = this.openIcon
    this.openOrCloseBtnLabel = this.openLabel
    cmdGrantAuth().then(resp => {
      this.cmdGrantAbled = resp.data
    })
  },
  beforeDestroy() {
    this.clearAllInterval()
    // 页面刷新时, 通知终端断开连接和停止连接
    var checkTerminalIds = []
    this.checkTerminal.forEach(item => {
      checkTerminalIds.push(item.id)
    })
    if (this.openOrCloseBtnIcon != this.openIcon) {
      heartbeatOrCloseCmd(checkTerminalIds, 1, this.webSocketKey)
    }
    const ids = this.getWaitTerminal()
    if (ids.length > 0) {
      stopConnection(ids, this.webSocketKey)
    }
    // this.$socket.unsubscribe('/' + this.webSocketKey + '/getHearBeat')
  },
  methods: {
    handleDrag() {

    },
    // 实现键盘向上向下切换历史命令
    keyboardUpOrDown(event) {
      if (!this.keyWordFlag) {
        this.keyWordFlag = true
        var cmdCommand = ''
        // 如果当前终端没有执行过命令，跳过
        if (!this.cmdCommandList.find(item => item.termId === this.currentTerminal.id)) {
          this.keyWordFlag = false
          return
        }
        const hisCmds = this.cmdCommandList.find(item => item.termId === this.currentTerminal.id).execCommands
        if (this.cmdCommandPos === -1) {
          this.cmdCommandPos = hisCmds.length
        }
        if (event === 'ArrowUp') {
          // 键盘向上键, 获取前一个命令
          if (hisCmds.length === 1) {
            this.$refs.cmdDlg.setCmdCommand(hisCmds[0])
            this.keyWordFlag = false
            return
          }
          if (this.cmdCommandPos === 0) {
            // 已经是历史命令列表第一个命令了，保持不变
            this.keyWordFlag = false
            return
          }
          this.cmdCommandPos--
          cmdCommand = hisCmds[this.cmdCommandPos]
          // 切换命令列表
          this.$refs.cmdDlg.setCmdCommand(cmdCommand)
          this.keyWordFlag = false
        } else {
          // 键盘向下键, 获取下一个命令
          if (this.cmdCommandPos >= (hisCmds.length - 1) || hisCmds.length === 1) {
            // 已经是历史命令列表最新的一个命令了，保持不变
            this.keyWordFlag = false
            return
          }
          this.cmdCommandPos++
          cmdCommand = hisCmds[this.cmdCommandPos]
          // 切换命令列表
          this.$refs.cmdDlg.setCmdCommand(cmdCommand)
          this.keyWordFlag = false
        }
      }
    },
    handleBeforeUnload() {
      this.clearAllInterval()
      // 页面刷新时, 通知终端断开连接和停止连接
      var checkTerminalIds = []
      this.checkTerminal.forEach(item => {
        checkTerminalIds.push(item.id)
      })
      if (this.openOrCloseBtnIcon != this.openIcon) {
        heartbeatOrCloseCmd(checkTerminalIds, 1, this.webSocketKey)
      }
      const ids = this.getWaitTerminal()
      if (ids.length > 0) {
        stopConnection(ids, this.webSocketKey)
      }
    },
    // 获取连接请求还未返回的终端
    getWaitTerminal() {
      const ids = []
      this.firstConnectionData.forEach(data => {
        ids.push(Number.parseInt(data.id))
      })
      this.waitConnectionData.forEach(data => {
        if (!ids.includes(data.id)) {
          ids.push(Number.parseInt(data.id))
        }
      })
      return ids;
    },
    executeStateFormatter(row) {
      if (row.executeState === 0) {
        return this.$t('pages.cmdCommand_Msg7')
      } else if (row.executeState === 1) {
        return this.$t('pages.cmdCommand_Msg8')
      } else if (row.executeState === 2) {
        return this.$t('pages.cmdCommand_Msg9')
      } else if (row.executeState === 3) {
        return this.$t('pages.cmdCommand_Msg10')
      } else {
        return null
      }
    },
    connectionStateFormatter(row) {
      if (row.connectionState === 0) {
        return this.$t('pages.cmdCommand_Msg5')
      } else if (row.connectionState === 1) {
        return this.$t('pages.cmdCommand_Msg6')
      }
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80 || node.oriData != 0)
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    createConnection() {

    },
    // 切换命令执行窗口
    viewResult(row) {
      if (this.currentTerminal.id === row.id) {
        this.$notify({
          title: this.$t('text.prompt'),
          message: this.$t('pages.serverProcess_Msg27'),
          type: 'info',
          duration: 2000
        })
        return
      }
      this.currentTerminal = Object.assign({}, row)
      var outputLines = []
      this.executeData.forEach(item => {
        if (item.termId == row.id) {
          outputLines = [...item.outputLines]
        }
      })
      this.$nextTick(() => {
        // 初始化命令执行窗口信息
        this.$refs.cmdDlg.switchBtnState(row.executeState)
        this.$refs.cmdDlg.switchCmdDlg(row, outputLines)
        this.$notify({
          title: this.$t('text.success'),
          message: this.$t('pages.serverProcess_Msg26'),
          type: 'success',
          duration: 2000
        })
      })
    },
    // 将树下面的终端节点过滤出来
    getTerminalNodes(tree) {
      var nodes = [];
      function traverse(node) {
        // 获取在线windows终端节点
        if (node.id && node.id.includes('T') && node.online && Number.parseInt(node.dataType) === 0) {
          nodes.push(node);
        }
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => traverse(child));
        }
      }
      traverse(tree); // 从根节点开始遍历
      return nodes;
    },
    checkedAfter(nodes) {
      nodes.forEach(node => {
        if (!this.rowDatas.find(item => item.id === Number.parseInt(node.dataId))) {
          const obj = {
            id: Number.parseInt(node.dataId),
            label: node.label,
            connectionState: 0
          }
          this.rowDatas.push(obj)
          this.$nextTick(() => {
            this.terminalTable.toggleAllSelection()
          })
        }
      })
    },
    async dataChange(tabName, checkedNode) {
      if (checkedNode.id.indexOf('G') > -1 || this.openOrCloseBtnIcon === this.doingIcon) {
        // 在连接过程中点击节点不进行任何操作
        return
      }
      if (this.rowDatas.find(item => item.id === Number.parseInt(checkedNode.dataId))) {
        return
      }
      // 先过滤得到所有能够进行远程命令的终端
      const checkedTermIds = [checkedNode.dataId]
      const resp = await enableCtrlMultipleTerm('cmdCommand', checkedTermIds, [1, 2, 3], '3.55.241219.SC')
      const enableStatusMap = resp.data
      const enableTermIds = []
      const disableTermIds = []
      for (const termId in enableStatusMap) {
        const status = enableStatusMap[termId]
        if (status > 0) {
          enableTermIds.push(Number.parseInt(termId))
        } else {
          disableTermIds.push(termId)
        }
      }
      // 提示不支持远程命令的终端
      if (disableTermIds.length > 0) {
        const msg = []
        disableTermIds.forEach(t => {
          msg.push(ctrlErrorMap(enableStatusMap[t]))
        })
        this.$message({ duration: 2000, message: msg.join('; ') });
      }
      // 如果当前处于已连接状态且该节点支持连接，直接发起连接
      if (this.isConnection() && enableTermIds.length > 0) {
        // 避免重复发起请求
        if (this.waitConnectionFlag || this.waitConnectionData.find(waitData => waitData.id === Number.parseInt(checkedNode.dataId))) {
          return
        } else {
          this.waitConnectionFlag = true
          this.waitConnectionFlagTime = new Date().getTime()
        }
        // 将节点对应的终端名称存储起来，后续添加到表格中需要用到
        if (!this.nodeInfo.find(item => item.termId === Number.parseInt(checkedNode.dataId))) {
          this.nodeInfo.push({ termId: Number.parseInt(checkedNode.dataId), label: checkedNode.label })
        }
        const waitObj = {
          id: Number.parseInt(checkedNode.dataId),
          time: new Date().getTime()
        }
        // 再次判断点击的节点是否已经发起过请求，只是还在等待响应
        if (this.waitConnectionData.find(waitData => waitData.id === Number.parseInt(checkedNode.dataId))) {
          this.waitConnectionFlag = false
          return
        }
        this.waitConnectionData.push(waitObj)
        const option = this.dlgShowOption == 1 ? 2 : 3
        connectionCmd([checkedNode.dataId], option, this.webSocketKey).then(r => {
          // 将该终端从存储执行结果的数组中移除，避免产生干扰
          this.doingExecuteData = this.doingExecuteData.filter(data => data != Number.parseInt(checkedNode.dataId))
          this.resultData = this.resultData.fill(data => data.termId != Number.parseInt(checkedNode.dataId))
          this.waitConnectionFlag = false
        })
        return
      }
      if (enableTermIds.length > 0 && !this.tableDataMultiFlag) {
        // 说明这个节点支持远程命令，加入表格列表
        this.tableDataMultiFlag = true
        // 在判断一次是否重复
        if (this.rowDatas.find(item => item.id === Number.parseInt(checkedNode.dataId))) {
          this.tableDataMultiFlag = false
          return
        }
        const obj = {
          id: Number.parseInt(checkedNode.dataId),
          label: checkedNode.label,
          connectionState: 0
        }
        this.rowDatas.push(obj)
        this.tableDataMultiFlag = false
        // 加入表格后默认勾选
        this.$nextTick(() => {
          this.terminalTable.toggleAllSelection()
        })
      }
    },
    // 切换命令窗口按钮是否显示
    isShowViewResultFormat() {
      return this.openOrCloseBtnIcon === this.closeIcon & !this.closeExecuteStatus
    },
    selectionChangeEnd: function(rowDatas) {
      this.connectionBtnDisabled = !((rowDatas && rowDatas.length > 0) && this.openOrCloseBtnIcon != this.doingIcon)
      this.deleteDisabled = !((rowDatas && rowDatas.length > 0) && this.openOrCloseBtnIcon === this.openIcon)
    },
    // 终端心跳结果未返回处理
    noHearbeatResult(ids, type) {
      const disableName = []
      const nodes = this.checkTerminal
      var num = 0;
      var terminal = []
      nodes.forEach(node => {
        if (ids.indexOf(Number.parseInt(node.id)) > -1) {
          this.rowDatas = this.rowDatas.filter(row => row.id != node.id)
          this.checkTerminal = this.checkTerminal.filter(row => row.id != node.id)
          // 节点取消勾选后，心跳结果数组也要跟着移除，确保心跳结果数量与连接的节点数量一致
          this.hearbeatData = this.hearbeatData.filter(item => Number.parseInt(item.termId) != Number.parseInt(node.id))
          // 清除历史结果记录
          this.executeData = this.executeData.filter(item => item.termId != node.id)
          disableName.push(node.label)
          terminal.push(node.id)
          num++
        }
      })
      if (num > 0) {
        // 这边多一个num > 0的判断是因为终端主动断开连接和控制台断开某个终端的连接都会导致勾选的节点发送变化影响此处的判断，因此需要确保是心跳造成节点变化才进行相应的操作
        // 如果心跳结果处理完成后，已经没有连接的终端，需重置各个按钮和终端树的状态并关闭心跳
        if (this.checkTerminal.length == 0) {
          clearInterval(this.hearbeatTimer)
          this.dlgShowDisabled = false
          this.quickConfigDisabled = false
          this.openOrCloseBtnIcon = this.openIcon
          this.openOrCloseBtnLabel = this.openLabel
          // 如果心跳结果处理完成后，已经没有连接的终端,关闭cmd窗口
          this.cmdShowFlag = false
          this.$message({
            title: this.$t('text.warning'), message: this.$t('pages.cmdCommand_Msg21'), type: 'warning', duration: 2000
          })
        } else {
          // 如果断开连接的终端里包括当前的命令执行窗口对应的终端，则将命令执行窗口切换到表格第一个
          if (terminal.indexOf(this.currentTerminal.id) > -1) {
            this.switchCmd()
          }
          this.$message({
            title: this.$t('text.warning'), message: disableName.join(',') + this.$t('pages.cmdCommand_Msg22'), type: 'warning', duration: 2000
          })
        }
      }
    },
    hearbeatResult() {
      const tempData = [...this.hearbeatData]
      const disabledIds = []
      tempData.forEach(item => {
        const nowTime = new Date().getTime()
        if (item.time) {
          if ((nowTime - item.time) > this.heartbeatTime * 1000) {
            // 该终端上一次心跳返回的时间到现在已经超过了心跳的等待时间，断开连接
            disabledIds.push(item.termId)
          }
        }
      })
      if (disabledIds.length > 0) {
        // 大于0说明有终端心跳没返回，做相应处理。等于0，说明所有终端的连接状态都在正常无需其它处理
        this.noHearbeatResult(disabledIds, 1)
      }
    },
    receiveHearbeat() {
      this.$socket.subscribeToUser(this.webSocketKey, '/getHearBeat', (resp, handle) => {
        // 判断返回的心跳的终端是否在表格里
        var tableExistTerminalFlag = false
        this.rowDatas.forEach(row => {
          if (Number.parseInt(row.id) === Number.parseInt(resp.data.termId)) {
            tableExistTerminalFlag = true
          }
        })
        if (!tableExistTerminalFlag) {
          // 返回心跳的终端不在表格内，说明控制台认为该终端已断开连接但是终端却还认为是正常连接的，则向该终端补发送断开连接的请求
          heartbeatOrCloseCmd([resp.data.termId], 1, this.webSocketKey)
          return
        }
        if (this.openOrCloseBtnIcon == this.closeIcon) {
          // 处于已连接时接收心跳才有意义，更新最后一次接收心跳的时间
          var flag = true
          this.hearbeatData.forEach(item => {
            if (Number.parseInt(item.termId) === Number.parseInt(resp.data.termId)) {
              item.time = new Date().getTime()
              flag = false
            }
          })
          if (flag) {
            const obj = resp.data
            obj.time = new Date().getTime()
            this.hearbeatData.push(obj)
          }
          // 定时发送心跳功能受浏览器节约资源规则的限制，浏览器在不可视的情况下（最小化，一般是超过一分钟）会断掉定时器，因此维持心跳还需要依靠每次一接收到终端的心跳响应就立马发送一次心跳请求来维持
          heartbeatOrCloseCmd([resp.data.termId], 0, this.webSocketKey)
          console.log('心跳发送,时间：' + JSON.stringify(this.getCurrentTimeFormatted()))
        }
      }, false)
    },
    sendHeartbeat(data) {
      // 发送心跳
      // var checkTerminalIds = []
      // this.checkTerminal.forEach(item => {
      //   checkTerminalIds.push(item.id)
      // })
      // 第一次发送心跳之前就需要往心跳存储数组加入终端信息，避免其它因素干扰
      data.forEach(item => {
        const temphearbeatData = {
          option: 0,
          termId: Number.parseInt(item),
          time: new Date().getTime()
        }
        if (!this.hearbeatData.find(item => item.termId === temphearbeatData.termId)) {
          this.hearbeatData.push(temphearbeatData)
        } else {
          this.hearbeatData.forEach(item => {
            if (item.termId === temphearbeatData.termId) {
              item.time = new Date().getTime()
            }
          })
        }
      })
      heartbeatOrCloseCmd(data, 0, this.webSocketKey).then(respond => {
        console.log('心跳首次发送,时间：' + JSON.stringify(this.getCurrentTimeFormatted()))
        this.receiveHearbeat()
        clearInterval(this.hearbeatTimer)
        // 心跳结果处理定时器
        this.hearbeatTimer = setInterval(() => {
          this.hearbeatResult()
        }, 1000)
      })
    },
    connectionFailAfter() {
      this.openOrCloseBtnIcon = this.openIcon
      this.openOrCloseBtnLabel = this.openLabel
      this.dlgShowDisabled = false
      this.quickConfigDisabled = false
      this.loadingShow = false
      this.showTree = true
      this.connectBtnShow = true
      this.rowDatas = []
      clearInterval(this.timer)
    },
    // 发送终端主动断开连接协议
    waitTerminalCloseConnection(data) {
      terminalCloseConnection(data, this.webSocketKey).then(resp => {
        this.receiveTerminalCloseConnection()
      })
    },
    // 接收终端的断开响应
    receiveTerminalCloseConnection() {
      this.$socket.subscribeToUser(this.webSocketKey, '/getTerminalCloseConnection', (resp, handle) => {
        // 延时处理终端的断开响应是为了给终端改变连接状态的时间，避免控制台一断开又马上去建立连接时，终端还没处理完成，导致返回终端已被其它控制台连接的响应
        setTimeout(() => {
          var terminalName = ''
          this.checkTerminal.forEach(row => {
            if (Number.parseInt(row.id) === Number.parseInt(resp.data)) {
              terminalName = row.label
            }
          })
          this.rowDatas = this.rowDatas.filter(row => row.id != resp.data)
          this.checkTerminal = this.checkTerminal.filter(row => row.id != resp.data)
          this.hearbeatData = this.hearbeatData.filter(item => item.termId != resp.data)
          if (this.checkTerminal.length == 0) {
            // 终端已全部断开连接
            if (this.firstConnectionData.length === 0) {
              clearInterval(this.timer)
              if (this.waitConnectionData.length === 0) {
                // 等待响应连接请求的终端分别存储firstConnectionData和waitConnectionData数组中，当两张表都为空且checkTerminal也为空，说明所有终端都拒绝连接了，清空心跳计时器，可以重新发起建立连接请求
                clearInterval(this.hearbeatTimer)
              }
            }
            this.dlgShowDisabled = false
            this.quickConfigDisabled = false
            this.cmdShowFlag = false
            if (this.isConnection()) {
              this.$message({
                title: this.$t('text.warning'), message: this.$t('pages.cmdCommand_Msg25'), type: 'warning', duration: 2000
              })
            }
            this.deleteDisabled = false
            this.openOrCloseBtnIcon = this.openIcon
            this.openOrCloseBtnLabel = this.openLabel
          } else {
            // 清空历史执行结果
            this.executeData = this.executeData.filter(item => item.termId != resp.data)
            if (this.currentTerminal.id === resp.data) {
              // 终端断开连接的终端刚好是控制台当前显示的命令执行窗口，需要切换窗口
              this.$refs.cmdDlg && this.switchCmd()
            }
            if (this.isConnection()) {
              this.$message({
                title: this.$t('text.warning'), message: terminalName + ' ' + this.$t('pages.cmdCommand_Msg3'), type: 'warning', duration: 2000
              })
            }
          }
        }, 500)
      }, false)
    },
    executeResult() {
      if (!this.resultFlag) {
        this.resultFlag = true
        // 处理结果之前,先拷贝一份数据，避免在处理过程中webSocket又接收新的数据产生干扰
        const tempResultData = [...this.resultData]
        tempResultData.forEach(item => {
          // 获取终端名称
          this.checkTerminal.forEach(data => {
            if (Number.parseInt(data.id) === Number.parseInt(item.termId)) {
              item.label = data.label
            }
          })
          // 判断是否执行结束
          if (this.tempResultData.indexOf(item.termId) < 0) {
            const option = this.numToList(item.option, 3)
            if (option.indexOf(4) > -1) {
              // 命令不支持，执行完毕，改变执行状态并将结果存入
              this.showAndSaveExecuteData(item)
              // 说明该终端的执行结果已经处理完成，避免重复进行处理
              this.tempResultData.push(item.termId)
              this.rowDatas.forEach(row => {
                if (row.id === item.termId) {
                  // 更新表格里该终端的当前执行路径
                  row.execPath = item.execPath
                  // 在命令执行窗口输出当前执行路径
                  this.showExecPath(item)
                  row.executeState = 2
                }
              })
              this.terminalTable.execRowDataApi()
              this.resultData = this.resultData.filter(data => data.termId != item.termId)
              this.$refs.cmdDlg.clearCmdCommand()
            } else if (option.indexOf(2) > -1) {
              // 需要二次输入命令,二次输入命令也需要判断下是否是多帧返回的，如果是则与前几帧返回的结果进行拼接后在进行显示
              this.doingExecuteData.indexOf(item.termId) < 0 ? this.showAndSaveExecuteData(item) : this.showAndSaveExecuteData(item, 1)
              this.tempResultData.push(item.termId)
              this.rowDatas.forEach(row => {
                if (row.id === item.termId) {
                  row.execPath = item.execPath
                  this.showExecPath(item, 1)
                  row.executeState = 2
                }
              })
              // 如果执行结果是多帧返回的，则接收并处理完成后，去doingExecuteData清空该终端的相关记录，避免对下次结果产生干扰
              if (this.doingExecuteData.indexOf(item.termId) > -1) {
                this.doingExecuteData = this.doingExecuteData.filter(data => data != item.termId)
              }
              this.terminalTable.execRowDataApi()
              this.resultData = this.resultData.filter(data => data.termId != item.termId)
              this.$refs.cmdDlg.clearCmdCommand()
            } else if (option.indexOf(1) > -1) {
              // 命令执行结束
              // 先判断命令是否是分多次返回的，如果是进行拼接后在进行显示
              this.doingExecuteData.indexOf(item.termId) < 0 ? this.showAndSaveExecuteData(item) : this.showAndSaveExecuteData(item, 1)
              this.tempResultData.push(item.termId)
              this.rowDatas.forEach(row => {
                if (row.id === item.termId) {
                  row.execPath = item.execPath
                  this.showExecPath(item)
                  row.executeState = 2
                }
              })
              if (this.doingExecuteData.indexOf(item.termId) > -1) {
                this.doingExecuteData = this.doingExecuteData.filter(data => data != item.termId)
              }
              this.terminalTable.execRowDataApi()
              this.resultData = this.resultData.filter(data => data.termId != item.termId)
              this.$refs.cmdDlg.clearCmdCommand()
            } else {
              // 说明命令尚未接收完整，终端将此命令的结果分为多次返回
              if (this.doingExecuteData.indexOf(item.termId) < 0) {
                this.doingExecuteData.push(item.termId)
                this.showAndSaveExecuteData(item)
              } else {
                this.showAndSaveExecuteData(item, 1)
              }
              this.rowDatas.forEach(row => {
                if (row.id === item.termId) {
                  row.execPath = item.execPath
                }
              })
              // 将该终端的处理结果从命令结果存储数组里过滤掉，且不将该终端id加入tempResultData，确保该终端的其余执行结果可以正常的解析
              this.resultData = this.resultData.filter(data => data.termId != item.termId && data.execRet != item.execRet)
              // 心跳补发，结果分多帧返回可能会导致将心跳响应丢失
              this.excesendHearbeat(item.termId)
            }
          }
        })
        this.resultFlag = false
      }
    },
    showExecPath(item, type) {
      var recet = []
      // 如diskpart、netsh这种命令，执行完成后返回结果会自带指示符如diskpart> 、netsh> 因此对于这种需要二次输入的命令不用在额外输出当前执行路径
      if (!type) {
        var execPath = ''
        // 获取表格数据里该终端的执行路径
        this.rowDatas.forEach(row => {
          if (row.id === item.termId) {
            execPath = row.execPath
          }
        })
        this.executeData.forEach(data => {
          if (data.termId === item.termId) {
            data.outputLines.push(execPath + '>')
            recet = [...data.outputLines]
          }
        })
      }
      if (this.currentTerminal.id === item.termId) {
        this.$nextTick(() => {
          if (!this.closeExecuteStatus) {
            // 如果还处于中断命令执行状态，则不改变按钮，等待中断命令执行完成后，由其修改按钮状态
            this.$refs.cmdDlg.switchBtnState(2)
          }
          if (!type) {
            this.$refs.cmdDlg.setOutputLines(recet)
          }
          this.$refs.cmdDlg.inputFocus()
        })
      }
      this.cmdCommandPos = -1
      this.closeExecuteAfterExceptSendHearbeat(item)
    },
    showAndSaveExecuteData(item, type) {
      var cmdCommand = ''
      this.cmdCommandList.forEach(cmd => {
        if (cmd.termId === item.termId) {
          cmdCommand = cmd.execCommands[cmd.execCommands.length - 1]
        }
      })
      var recet = []
      this.executeData.forEach(data => {
        if (data.termId === item.termId) {
          // 如果执行结果过大或者时间较长，会将执行结果分为多帧返回，只有第一帧在输出之前需要先输出当前执行路径和执行命令
          if (!type) {
            // 第一次得到返回结果时，需要先显示当前的执行路径和执行命令
            const lastLine = data.outputLines[data.outputLines.length - 1] + cmdCommand
            data.outputLines[data.outputLines.length - 1] = lastLine
          }
          const lastLine = data.outputLines[data.outputLines.length - 1]
          if (!lastLine.endsWith('\r\n') && !lastLine.endsWith('>') && !lastLine.endsWith(cmdCommand)) {
            // 如dir、tasklist这种一次结果分多次返回的，可能会从某行结果中间阶段，该判断就是为了此情况发生时，同一行的结果即使分多帧返回依旧可以显示在同一行
            data.outputLines[data.outputLines.length - 1] = lastLine + item.execRet
          } else {
            data.outputLines.push(item.execRet)
          }
          data.option = item.option
          recet = [...data.outputLines]
          // data.outputLines.push(item.execRet)
          // recet = [...data.outputLines]
        }
      })
      if (this.currentTerminal.id === item.termId) {
        this.$nextTick(() => {
          this.$refs.cmdDlg.setOutputLines(recet)
        })
      }
    },
    // 多帧返回执行结果时，可能会造成心跳响应，因此结果分多帧返回的在接受时额外发送心跳
    excesendHearbeat(termId) {
      this.hearbeatData.forEach(item => {
        if (item.termId === termId) {
          const subTiem = new Date().getTime() - item.time
          // 心跳等待时间超过5秒的再补发
          if (subTiem > 5000) {
            var ids = []
            this.checkTerminal.forEach(item => { ids.push(item.id) })
            heartbeatOrCloseCmd(ids, 0, this.webSocketKey)
            // 补发心跳后立马更新心跳时间，避免发送大量心跳造成终端的处理负担
            item.time = new Date().getTime()
          }
        }
      })
    },
    // 命令执行完成后，额外发送一次心跳
    closeExecuteAfterExceptSendHearbeat(item) {
      heartbeatOrCloseCmd([item.termId], 0, this.webSocketKey)
      this.hearbeatData.forEach(data => {
        if (data.termId === item.termId) {
          item.time = new Date().getTime()
        }
      })
    },
    // 接收命令执行结果
    receiveCmdResult() {
      this.$socket.subscribeToUser(this.webSocketKey, '/getSendCmdCommand', (resp, handle) => {
        const data = resp.data
        this.tempResultData = this.tempResultData.filter(item => item != resp.data.termId)
        this.resultData.push(data)
      }, false)
    },
    // 接收中断执行响应
    handleInterruptResult() {
      if ((new Date().getTime() - this.interruptTime) > 20000) {
        // 超过20秒中断执行响应还未接收完整则认为中断执行结束
        this.interruptResult()
        return
      }
      if (this.interruptResultData.length === this.interruptData.length) {
        // 所有中断响应都返回了
        this.interruptResult()
        return
      }
    },
    // 中断结果处理
    interruptResult() {
      const interruptIds = []
      const interruptNames = []
      this.interruptData.forEach(data => {
        // 如果在执行中断时，命令已经执行结束或者接收完整，提示中断失败
        const existFlag1 = this.resultData.find(item => item.termId === data.termId && (this.numToList(item.option, 3).indexOf(4) > -1 || this.numToList(item.option, 3).indexOf(1) > -1))
        const existFlag2 = this.rowDatas.find(row => row.id === data.termId && row.executeState === 2)
        if (existFlag1 || existFlag2) {
          // 获取中断失败的终端id
          interruptIds.push(data.termId)
          interruptNames.push(data.label)
        }
      })
      if (interruptNames.length > 0) {
        this.$notify({
          title: this.$t('text.error'), message: interruptNames.join(',') + this.$t('pages.cmdCommand_Msg24'), type: 'error', duration: 5000
        })
      }
      if (interruptIds.length === this.interruptData.length) {
        // 全部中断失败
        this.$refs.cmdDlg.clearCmdCommand()
        this.$refs.cmdDlg.switchBtnState(2)
        this.closeExecuteStatus = false
        this.$refs.cmdDlg.clearCloseExecuLoading()
        clearInterval(this.interruptTimer)
        return
      }
      // 对于中断成功的，修改表格数据该终端的执行状态为执行完毕，并输出当前路径
      this.rowDatas.forEach(row => {
        if (interruptIds.indexOf(row.id) < 0) {
          this.executeData.forEach(data => {
            if (data.termId === row.id && interruptIds.indexOf(data.termId) < 0 && this.interruptData.find(item => item.termId === data.termId)) {
              row.executeState = 2
              const lastLine = row.execPath + '>'
              data.outputLines.push(lastLine)
              if (data.termId === this.currentTerminal.id) {
                this.$refs.cmdDlg.setOutputLines(data.outputLines)
              }
            }
          })
        }
      })
      this.$refs.cmdDlg.clearCmdCommand()
      this.$refs.cmdDlg.switchBtnState(2)
      this.closeExecuteStatus = false
      this.$refs.cmdDlg.clearCloseExecuLoading()
      clearInterval(this.interruptTimer)
    },
    receiveInterruptResult() {
      this.$socket.subscribeToUser(this.webSocketKey, '/getCloseExecuteCmd', (resp, handle) => {
        this.interruptResultData.push(resp.data)
      }, false)
    },
    closeExecuteCommand(mode) {
      this.interruptData = []
      this.interruptResultData = []
      clearInterval(this.interruptTimer)
      var ids = []
      if (mode == 0) {
        // 只对当前终端执行
        const interruptObj = {
          termId: this.currentTerminal.id,
          label: this.currentTerminal.label
        }
        ids.push(this.currentTerminal.id)
        this.interruptData.push(interruptObj)
      } else {
        // 只对执行状态为执行中的终端执行中断命令
        const copyRoeDatas = [...this.rowDatas]
        const datas = []
        copyRoeDatas.forEach(row => {
          if (row.connectionState == 1 && row.executeState == 1) {
            const interruptObj = {
              termId: row.id,
              label: row.label
            }
            datas.push(interruptObj)
            ids.push(row.id)
          }
        })
        if (datas.length === 0) {
          this.$refs.cmdDlg.clearCloseExecuLoading()
          this.$notify({
            title: this.$t('text.error'), message: this.$t('pages.cmdCommand_Msg24'), type: 'error', duration: 2000
          })
          return
        }
        this.interruptData = [...datas]
      }
      heartbeatOrCloseCmd(ids, 2, this.webSocketKey).then(resp => {
        this.closeExecuteStatus = true
        this.interruptTime = new Date().getTime()
        this.receiveInterruptResult()
        clearInterval(this.interruptTimer)
        this.interruptTimer = setInterval(() => {
          this.handleInterruptResult()
        }, 2000)
      })
    },
    // 清空执行结果
    clearResult(mode) {
      if (mode == 0) {
        // 只对当前终端执行
        this.executeData.forEach(data => {
          if (data.termId === this.currentTerminal.id) {
            if (this.currentTerminal.connectionState == 1 && this.currentTerminal.executeState != 1) {
              // 未执行的才可以清空
              const option = data.option ? this.numToList(data.option, 3) : []
              if (!option.includes(2) || option.length === 0) {
                // 不处于需要二次输入命令状态的才可以清空，如不处于diskpart、netsh命令下
                const outline = data.outputLines[data.outputLines.length - 1]
                data.outputLines = [outline]
                this.$refs.cmdDlg.setOutputLines(data.outputLines)
              } else {
                this.$notify({
                  title: this.$t('text.warning'),
                  message: this.$t('pages.cmdCommand_Msg35'),
                  type: 'warning',
                  duration: 2000
                })
              }
            }
          }
        })
      } else {
        // 对所有终端执行需要过滤掉还处于执行状态的终端
        const copyRoeDatas = [...this.rowDatas]
        const datas = []
        copyRoeDatas.forEach(row => {
          if (row.connectionState == 1 && row.executeState != 1) {
            datas.push(row.id)
          }
        })
        this.executeData.forEach(data => {
          if (datas.indexOf(data.termId) > -1) { // 判断是否已执行完成
            // 判断是否处于二次输入状态的，未执行完成的处于二次输入状态的都不可以清空
            const option = data.option ? this.numToList(data.option, 3) : []
            if (!option.includes(2) || option.length === 0) {
              const outline = data.outputLines[data.outputLines.length - 1]
              data.outputLines = [outline]
              if (data.termId === this.currentTerminal.id) {
                this.$refs.cmdDlg.setOutputLines(data.outputLines)
              }
            } else {
              if (data.termId === this.currentTerminal.id) {
                // 当前命令执行窗口的状态不支持清空结果的，才进行提示
                this.$notify({
                  title: this.$t('text.warning'),
                  message: this.$t('pages.cmdCommand_Msg35'),
                  type: 'warning',
                  duration: 2000
                })
              }
            }
          }
        })
      }
    },
    executeCommand(mode, cmdCommand) {
      var obj = []
      if (mode == 0) {
        // 只对当前终端执行
        const data = Object.assign({}, this.currentTerminal)
        data.webSocketKey = this.webSocketKey
        data.execCommand = cmdCommand
        data.termId = data.id
        obj.push(data)
        // 设置执行状态为正在执行
        this.rowDatas.forEach(row => {
          if (row.id === data.id) {
            row.executeState = 1
            data.execPath = row.execPath
          }
        })
        var flag = false
        // 将当前的执行命令存入，用于后续进行展示
        this.cmdCommandList.forEach(cmd => {
          if (cmd.termId === data.termId) {
            cmd.execCommands.push(cmdCommand)
            flag = true
          }
        })
        if (!flag) {
          const cmd = {
            termId: data.termId,
            execCommands: [cmdCommand]
          }
          this.cmdCommandList.push(cmd)
        }
      } else {
        // 对所有终端执行需要过滤掉还处于执行状态的终端
        const copyRoeDatas = [...this.rowDatas]
        const datas = []
        copyRoeDatas.forEach(row => {
          if (row.connectionState == 1 && row.executeState != 1) {
            datas.push(row)
          }
        })
        datas.forEach(item => {
          item.webSocketKey = this.webSocketKey
          item.execCommand = cmdCommand
          item.termId = item.id
          obj.push(item)
          // 设置执行状态为正在执行
          this.rowDatas.forEach(row => {
            if (row.id === item.id) {
              row.executeState = 1
              item.execPath = row.execPath
            }
          })
          // 将当前的执行命令存入，用于后续进行展示
          var flag1 = false
          this.cmdCommandList.forEach(cmd => {
            if (cmd.termId === item.termId) {
              cmd.execCommands.push(cmdCommand)
              flag1 = true
            }
          })
          if (!flag1) {
            const cmd = {
              termId: item.termId,
              execCommands: [cmdCommand]
            }

            this.cmdCommandList.push(cmd)
          }
        })
      }
      this.terminalTable.execRowDataApi()
      obj.forEach(item => {
        // this.interruptData = this.interruptData.filter(data => data != item.termId)
        // 存储命令结果的相关数组，需要过滤需要执行的终端，避免产生干扰
        this.doingExecuteData = this.doingExecuteData.filter(data => data != item.id)
        this.resultData = this.resultData.filter(data => data.termId != item.id)
      })
      sendCmdCommand(obj).then(resp => {
        // 向终端发送执行命令成功后，中断命令执行按钮可以点击
        this.closeExecuAble = false
        this.receiveCmdResult()
        clearInterval(this.sendCmdTimer)
        this.sendCmdTimer = setInterval(() => {
          this.executeResult()
        }, 1000)
      })
    },
    // 解析失败的具体原因
    connectionFailReason(option) {
      var msg = ''
      if (option.indexOf(2) > -1) {
        msg = this.$t('pages.cmdCommand_Msg52')
      } else if (option.indexOf(4) > -1) {
        msg = this.$t('pages.cmdCommand_Msg53')
      } else if (option.indexOf(32) > -1) {
        msg = this.$t('pages.cmdCommand_Msg54')
      } else {
        msg = this.$t('pages.cmdCommand_Msg55')
      }
      return msg
    },
    // 解析协议返回结果判断终端是否同意连接
    filterConnectionData(data) {
      var enableIds = []
      var disableTerms = []
      var termIds = []
      var enableData = []
      data.forEach(item => {
        if (this.firstConnectionEndData.indexOf(item.termId) < 0) {
          const option = this.numToList(item.option, 6)
          if (option.indexOf(1) > -1) {
            enableIds.push(item.termId)
            enableData.push(item)
          } else {
            var msg = this.connectionFailReason(option)
            const obj = {
              termId: item.termId,
              reason: msg
            }
            // disableIds.push(item.termId)
            disableTerms.push(obj)
          }
          this.firstConnectionEndData.push(item.termId)
          termIds.push(item.termId)
          this.waitConnectionData = this.waitConnectionData.filter(data => data.id != item.termId)
        }
      })
      return { enableIds, disableTerms, termIds, enableData }
    },
    formatConnectionSuccess(enabeleIds, enableData) {
      if (!this.cmdShowFlag) {
        // 第一次连接
        this.rowDatas = []
        this.checkTerminal = []
      }
      // 连接成功的加入表格
      enabeleIds.forEach(id => {
        var tableExistFlag = false
        this.rowDatas.forEach(row => {
          if (row.id === id) {
            row.connectionState = 1
            tableExistFlag = true
          }
        })
        // 不存在
        if (!tableExistFlag) {
          var nameFlag = false
          var terminalName = ''
          this.firstConnectionData.forEach(item => {
            if (item.id === id) {
              nameFlag = true
              terminalName = item.label
            }
          })
          if (!nameFlag) {
            this.nodeInfo.forEach(item => {
              if (item.termId === id) {
                terminalName = item.label
              }
            })
          }
          var connectionInfo = {}
          enableData.forEach(item => {
            if (item.termId === id) {
              connectionInfo = Object.assign({}, item)
            }
          })
          const obj = {
            id: id,
            label: terminalName,
            connectionState: 1,
            execPath: connectionInfo.initPath,
            commandList: connectionInfo.command,
            executeState: 0
          }
          this.rowDatas.push(obj)
          this.checkTerminal = [...this.rowDatas]
          const executeObj = {
            termId: id,
            outputLines: [obj.execPath + '>']
          }
          this.executeData.push(executeObj)
        }
      })
      if (!this.cmdShowFlag) {
        this.openOrCloseBtnIcon = this.closeIcon
        this.openOrCloseBtnLabel = this.closeLabel
        this.connectionDisabled = false
        this.deleteDisabled = true
        this.loadingShow = false
        this.connectBtnShow = true
        this.showTree = true
        this.$nextTick(() => {
          var outputLines = []
          this.executeData.forEach(data => {
            if (data.termId === this.rowDatas[0].id) {
              outputLines = [...data.outputLines]
            }
          })
          this.$refs.cmdDlg.setResultObject(this.rowDatas[0], outputLines)
          this.currentTerminal = Object.assign({}, this.rowDatas[0])
        })
        this.cmdShowFlag = true
        this.quickConfigDisabled = true
        this.dlgShowDisabled = true
      }
      // 发送心跳
      this.sendHeartbeat(enabeleIds)
      this.waitTerminalCloseConnection(enabeleIds)
      enabeleIds.forEach(id => {
        // 将连接成功的终端从firstConnectionData中移除，标明该终端对连接请求已进行响应
        this.firstConnectionData = this.firstConnectionData.filter(data => data.id != id)
      })
    },
    handleWaitConnection() {
      if ((new Date().getTime() - this.waitConnectionFlagTime) > 1500) {
        this.waitConnectionFlag = false
      }
      var num = 0
      // 初次建立连接后，又发起连接请求的那些终端，响应的等待时间超过了超时时间，则默认为连接失败
      this.waitConnectionData.forEach(data => {
        if ((new Date().getTime() - data.time) > (this.requestTime * 1000)) {
          num++
        }
      })
      if (num > 0) {
        this.waitConnectionData = this.waitConnectionData.filter(data => (new Date().getTime() - data.time) < this.requestTime * 1000)
        if (this.waitConnectionData.length === 0 && this.firstConnectionData.length === 0 && this.loadingShow) {
          clearInterval(this.waitConDataTimer)
          this.connectionFailAfter()
        }
      }
    },
    connectionResult() {
      // 判断是否有已响应连接请求但结果还未处理的终端
      var waitFlag = false
      this.connectionData.forEach(data => {
        this.waitConnectionData.forEach(item => {
          if (Number.parseInt(item.id) === Number.parseInt(data.termId)) {
            waitFlag = true
          }
        })
      })
      // 初次连接的终端部分终端的响应等待时间超过了超时时间且没有已响应连接请求但结果还未处理的终端
      if ((new Date().getTime() - this.connectionFirstTime) > this.requestTime * 1000 && !waitFlag) {
        if (this.firstConnectionData.length > 0) {
          const errorNames = []
          this.firstConnectionData.forEach(itemData => {
            errorNames.push(itemData.label)
          })
          this.connectionData = []
          this.firstConnectionData = []
          this.firstConnectionEndData = []
          if (this.openOrCloseBtnIcon === this.doingIcon) {
            // 说明初次建立连接就没有成功的，直接关闭loading属性，恢复未发起连接的状态
            this.connectionFailAfter()
          } else if (this.openOrCloseBtnIcon === this.openIcon) {
            // 按钮显示为“建立连接”，则需要考虑初次连接成功后，有没有发起新的连接请求处于等待中
            if (this.waitConnectionData.length === 0) {
              this.connectionFailAfter()
            } else {
              // 初次连接成功后，有发起新的连接请求处于等待中。则loading要处于加载状态（在watch函数里进行了处理），连接按钮则恢复原状
              this.openOrCloseBtnIcon = this.openIcon
              this.openOrCloseBtnLabel = this.openLabel
              this.rowDatas = []
              clearInterval(this.timer)
            }
          } else {
            clearInterval(this.timer)
          }
          this.$message({
            // 不是拒绝连接、超时未同时、终端未初始化、终端已被其它管理员连接的连接失败，只提示为某某终端连接失败
            title: this.$t('text.error'), message: errorNames.join(',') + this.$t('pages.connectFail'), type: 'error', duration: 2000
          })
        } else {
          clearInterval(this.timer)
        }
        return
      }
      const initData = [...this.connectionData]
      if (!this.firstConnectionFlag) {
        this.firstConnectionFlag = true
        const { enableIds, disableTerms, enableData } = this.filterConnectionData(initData)
        // var disableNames = []
        const disableNameAndReason = []
        disableTerms.forEach(term => {
          // 获取连接失败的终端名称
          var disableNameFlag = false
          this.firstConnectionData.forEach(item => {
            if (item.id === term.termId) {
              // disableNames.push(item.label + ' ' + term.reason)
              const obj = {
                name: item.label,
                reason: term.reason
              }
              disableNameAndReason.push(obj)
              disableNameFlag = true
            }
          })
          if (!disableNameFlag) {
            this.nodeInfo.forEach(item => {
              if (item.termId === term.termId) {
                const obj = {
                  name: item.label,
                  reason: term.reason
                }
                disableNameAndReason.push(obj)
                // disableNames.push(item.label + ' ' + term.reason)
              }
            })
          }
          // 将连接失败的终端从firstConnectionData中移除，标明该终端对连接请求已进行响应
          this.firstConnectionData = this.firstConnectionData.filter(data => data.id != term.termId)
          // 失败的终端从表格移除
          this.rowDatas = this.rowDatas.filter(row => row.id != term.termId)
          if (this.rowDatas.length === 0) {
            // 初次申请连接的终端全部连接失败
            // this.firstConnectionEndData = this.firstConnectionEndData.filter(data => data != term.termId)
            if (this.firstConnectionData.length === 0) {
              this.firstConnectionEndData = []
              this.connectionData = []
              this.firstConnectionData = []
              this.connectionFailAfter()
            }
          }
        })
        if (disableNameAndReason.length > 0) {
          const nameAndReasonArray = disableNameAndReason.reduce((acc, item) => {
            const existingItem = acc.find(el => el.reason === item.reason);
            if (existingItem) {
              existingItem.names.push(item.name);
            } else {
              acc.push({ reason: item.reason, names: [item.name] });
            }
            return acc;
          }, []);

          if (nameAndReasonArray.length > 0) {
            var msg = ''
            nameAndReasonArray.forEach(info => {
              msg += info.names.join(',') + info.reason + ' '
            })
            this.$message({
              title: this.$t('text.error'), message: msg, type: 'error', duration: 2000
            })
          }
        }
        // if (disableNames.length > 0) {
        //   this.$message({
        //     title: this.$t('text.error'), message: disableNames.join(','), type: 'error', duration: 2000
        //   })
        // }
        if (enableIds.length > 0) {
          this.formatConnectionSuccess(enableIds, enableData)
        }
        this.firstConnectionFlag = false
      }
      if (this.firstConnectionData.length === 0) {
        // 首次发起连接的都已经处理完毕，判断connectionData里是否有不是首次连接的，有的话还不移除计时器
        // 通过firstConnectionEndData来判断，如果connectionData里某个值在firstConnectionEndData不存在，说明未处理等其处理完毕
        var timerFlag = true
        this.connectionData.forEach(data => {
          if (this.firstConnectionEndData.indexOf(data.termId) < 0) {
            timerFlag = false
          }
        })
        if (timerFlag) {
          this.firstConnectionEndData = []
          this.firstConnectionData = []
          this.connectionData = []
          clearInterval(this.timer)
        }
      }
    },
    receiveConnectionRespond() {
      this.$socket.subscribeToUser(this.webSocketKey, '/getConnectionCmd', (resp, handle) => {
        if (this.openOrCloseBtnIcon == this.doingIcon || this.firstConnectionData.length > 0) {
          // 还处于建立连接的过程中才接收或者初次连接的终端还没接收完整时
          this.connectionData.push(resp.data)
        } else if (this.isConnection()) {
          // 处于连接状态下，点击树节点，如果该节点符合远程命令的要求，则会自动发起连接请求所以这边需要对这种情况做处理
          const option = this.numToList(resp.data.option, 6)
          if (option.indexOf(1) > -1) {
            // 连接成功，加入列表
            var label = ''
            this.nodeInfo.forEach(item => {
              if (item.termId === resp.data.termId) {
                label = item.label
              }
            })
            if (label === '') {
              // label等于''说明不是连接状态点击节点的连接请求不做处理
              return
            }
            const obj = {
              id: resp.data.termId,
              label: label,
              connectionState: 1,
              execPath: resp.data.initPath,
              commandList: resp.data.command,
              executeState: 0
            }
            this.rowDatas.push(obj)
            this.checkTerminal = [...this.rowDatas]
            const cmdObj = {
              termId: obj.id,
              outputLines: [obj.execPath + '>']
            }
            this.executeData.push(cmdObj)
            this.terminalTable.execRowDataApi()
            // 发起心跳
            const temphearbeatData = {
              option: 0,
              termId: resp.data.termId,
              time: new Date().getTime()
            }
            heartbeatOrCloseCmd([resp.data.termId], 0, this.webSocketKey).then(res => {
              if (!this.hearbeatData.find(item => item.termId === temphearbeatData.termId)) {
                this.hearbeatData.push(temphearbeatData)
              } else {
                this.hearbeatData.forEach(item => {
                  if (item.termId === temphearbeatData.termId) {
                    item.time = new Date().getTime()
                  }
                })
              }
            }
            )
            // 发起终端主动qqq断开连接的请求，并等待响应
            terminalCloseConnection([resp.data.termId], this.webSocketKey)
            this.waitConnectionData = this.waitConnectionData.filter(data => data.id != resp.data.termId)
          } else {
            // 拒绝连接， 提示连接失败
            this.waitConnectionData = this.waitConnectionData.filter(data => data.id != resp.data.termId)
            this.showErrorMessage(option, resp.data)
          }
        } else if (this.openOrCloseBtnIcon === this.openIcon) {
          // 该情况出现情形有A、B两个终端，第一次先对A终端发起连接，A终端同意连接后，对B终端发起请求，B终端先不响应然后断开A终端此时在对B终端进行响应处理
          // 连接响应返回时，一个终端也没有连接
          const option = this.numToList(resp.data.option, 6)
          if (option.indexOf(1) > -1) {
            // 终端选择同意
            // heartbeatOrCloseCmd([resp.data.termId], 1, this.webSocketKey)
            var label1 = ''
            // 记录该节点的终端名称，后续可能会用上
            this.nodeInfo.forEach(item => {
              if (item.termId === resp.data.termId) {
                label1 = item.label
              }
            })
            const obj = {
              id: resp.data.termId,
              label: label1,
              connectionState: 1,
              execPath: resp.data.initPath,
              commandList: resp.data.command,
              executeState: 0
            }
            this.rowDatas.push(obj)
            this.checkTerminal = [...this.rowDatas]
            this.executeData = this.executeData.filter(temp => temp.termId != obj.id)
            const cmdObj = {
              termId: obj.id,
              outputLines: [obj.execPath + '>']
            }
            this.executeData.push(cmdObj)
            this.terminalTable.execRowDataApi()
            this.openOrCloseBtnIcon = this.closeIcon
            this.openOrCloseBtnLabel = this.closeLabel
            this.connectionDisabled = false
            this.deleteDisabled = true
            this.loadingShow = false
            this.connectBtnShow = true
            this.showTree = true
            this.$nextTick(() => {
              var outputLines = []
              this.executeData.forEach(data => {
                if (data.termId === this.rowDatas[0].id) {
                  outputLines = [...data.outputLines]
                }
              })
              this.$refs.cmdDlg.setResultObject(this.rowDatas[0], outputLines)
              this.currentTerminal = Object.assign({}, this.rowDatas[0])
            })
            this.cmdShowFlag = true
            this.quickConfigDisabled = true
            this.dlgShowDisabled = true
            // 发起心跳
            const temphearbeatData = {
              option: 0,
              termId: resp.data.termId,
              time: new Date().getTime()
            }
            heartbeatOrCloseCmd([resp.data.termId], 0, this.webSocketKey).then(res => {
              if (!this.hearbeatData.find(item => item.termId === temphearbeatData.termId)) {
                this.hearbeatData.push(temphearbeatData)
              } else {
                this.hearbeatData.forEach(item => {
                  if (item.termId === temphearbeatData.termId) {
                    item.time = new Date().getTime()
                  }
                })
              }
            }
            )
            // 发起终端主动qqq断开连接的请求，并等待响应
            terminalCloseConnection([resp.data.termId], this.webSocketKey)
            this.waitConnectionData = this.waitConnectionData.filter(data => data.id != resp.data.termId)
          } else {
            this.waitConnectionData = this.waitConnectionData.filter(data => data.id != resp.data.termId)
            if (this.waitConnectionData.length === 0) {
              this.loadingShow = false
              this.showTree = true
              this.connectBtnShow = true
            }
            this.showErrorMessage(option, resp.data)
          }
        }
      }, false)
    },
    showErrorMessage(option, data) {
      var msg = this.connectionFailReason(option)
      var connectioLabel = ''
      this.nodeInfo.forEach(node => {
        if (node.termId === data.termId) {
          connectioLabel = node.label + msg
        }
      })
      this.$message({
        title: this.$t('text.error'), message: connectioLabel, type: 'error', duration: 2000
      })
    },
    isConnection() {
      // 按钮显示'建立连接'，说明未连接
      return this.openOrCloseBtnIcon != this.openIcon
    },
    receiveCloseConnection(type, ids) {
      if (type === 0) {
        // 断开连接后，需要取消心跳相关的定时器, 断开连接不需要等待响应，控制台发送断开连接后就默认已经断开
        this.dlgShowDisabled = false
        this.quickConfigDisabled = false
        this.openOrCloseBtnIcon = this.openIcon
        this.openOrCloseBtnLabel = this.openLabel
        this.deleteDisabled = false
        this.terminalTable.clearSelection()
        this.cmdShowFlag = false
        this.rowDatas = []
        this.checkTerminal = []
        this.hearbeatData = []
        // 断开连接后，将历史执行结果清空
        this.executeData = []
        if (this.firstConnectionData.length === 0) {
          clearInterval(this.timer)
          if (this.waitConnectionData.length === 0) {
            clearInterval(this.hearbeatTimer)
          }
        }
        this.$message({
          title: this.$t('text.success'), message: this.$t('pages.cmdCommand_Msg25'), type: 'success', duration: 2000
        })
      } else {
        const closeConnectionName = []
        this.rowDatas.forEach(row => {
          if (ids.includes(row.id)) {
            closeConnectionName.push(row.label)
          }
        })
        this.rowDatas = this.rowDatas.filter(row => ids.indexOf(row.id) < 0)
        this.checkTerminal = this.checkTerminal.filter(row => ids.indexOf(row.id) < 0)
        this.hearbeatData = this.hearbeatData.filter(item => ids.indexOf(item.termId) < 0)
        // 断开连接后，将历史执行结果清空
        this.executeData = this.executeData.filter(item => ids.indexOf(item.termId) < 0)
        // 断开连接的终端有当前的命令执行窗口，将cmd框切换为表格中的第一个数据
        if (ids.indexOf(this.currentTerminal.id) > -1) {
          this.switchCmd()
        }
        this.$message({
          title: this.$t('text.success'), message: closeConnectionName.join(', ') + ' ' + this.$t('pages.cmdCommand_Msg3'), type: 'success', duration: 2000
        })
      }
      this.connectionBtnDisabled = false
    },
    // 切换命令执行窗口
    switchCmd() {
      this.currentTerminal = Object.assign({}, this.rowDatas[0])
      var outputLines = []
      this.executeData.forEach(item => {
        if (item.termId === this.currentTerminal.id) {
          outputLines = [...item.outputLines]
        }
      })
      this.$nextTick(() => {
        this.$refs.cmdDlg.switchBtnState(this.currentTerminal.executeState)
        this.$refs.cmdDlg.switchCmdDlg(this.currentTerminal, outputLines)
      })
    },
    stopConnection() {
      if (this.openOrCloseBtnIcon === this.closeIcon || this.cmdShowFlag) {
        return
      }
      const ids = []
      // 等待响应连接请求的终端存储在firstConnectionData、waitConnectionData两个数组内，因此停止连接需要对这两个数组内的终端发送
      this.firstConnectionData.forEach(data => {
        ids.push(Number.parseInt(data.id))
      })
      this.waitConnectionData.forEach(data => {
        if (!ids.includes(data.id)) {
          ids.push(Number.parseInt(data.id))
        }
      })
      stopConnection(ids, this.webSocketKey).then(resp => {
        this.dlgShowDisabled = false
        this.quickConfigDisabled = false
        this.loadingShow = false
        this.showTree = true
        this.connectBtnShow = true
        this.openOrCloseBtnIcon = this.openIcon
        this.openOrCloseBtnLabel = this.openLabel
        this.connectionData = []
        this.waitConnectionData = []
        this.firstConnectionData = []
        this.firstConnectionEndData = []
        this.clearAllInterval()
        this.$nextTick(() => {
          this.terminalTable.toggleAllSelection()
        })
      })
    },
    sendConnectionRequest() {
      if (this.isConnection()) {
        // 已建立连接,则断开连接
        this.$confirmBox(this.$t('pages.cmdCommand_Msg26'), this.$t('text.prompt')).then(() => {
          const data = this.terminalTable.getSelectedDatas()
          if (data.length === 0) {
            this.$message({
              title: this.$t('text.error'), message: this.$t('pages.cmdCommand_Msg42'), type: 'error', duration: 2000
            })
            return
          }
          var checkTerminalIds = []
          data.forEach(item => {
            checkTerminalIds.push(item.id)
          })
          heartbeatOrCloseCmd(checkTerminalIds, 1, this.webSocketKey).then(respond => {
            this.connectionBtnDisabled = true
            setTimeout(() => {
              checkTerminalIds.length === this.checkTerminal.length ? this.receiveCloseConnection(0) : this.receiveCloseConnection(1, checkTerminalIds)
            }, 500)
          })
        })
      } else {
        // 建立连接
        this.dlgShowDisabled = true
        this.quickConfigDisabled = true
        this.openOrCloseBtnIcon = this.doingIcon
        this.openOrCloseBtnLabel = this.doningLabel
        this.connectBtnShow = false
        this.loadingShow = true
        this.showTree = false
        this.connectionBtnDisabled = true
        this.deleteDisabled = true
        this.firstConnectionFlag = false
        this.initData()
        this.waitConnectionFlag = false
        this.waitConnectionData = []
        const option = this.dlgShowOption == 1 ? 2 : 3
        this.connectionFirstTime = new Date().getTime()
        this.webSocketKey = this.userId + '-' + this.getCurrentTimeFormatted() + '-' + this.generateRandomString(5)
        this.checkTerminal = this.terminalTable.getSelectedDatas()
        var checkTerminalIds = []
        this.checkTerminal.forEach(item => {
          checkTerminalIds.push(item.id)
        })
        this.rowDatas = this.rowDatas.filter(row => checkTerminalIds.indexOf(row.id) > -1)
        connectionCmd(checkTerminalIds, option, this.webSocketKey).then(respond => {
          this.firstConnectionData = [...this.checkTerminal]
          // 建立连接时需要清空所有定时器避免干扰
          this.clearAllInterval()
          this.timer = setInterval(() => {
            this.connectionResult()
          }, 1000)
          this.receiveConnectionRespond()
          this.waitConDataTimer = setInterval(() => {
            this.handleWaitConnection()
          }, 2000)
          // 定时发送心跳功能受浏览器节约资源规则的限制，浏览器在不可视的情况下（最小化，一般是超过一分钟）会断掉定时器，因此维持心跳还需要依靠每次一接收到终端的心跳响应就立马发送一次心跳请求来维持
          this.globalHeartBeatTimer = setInterval(() => {
            this.sendGlobalHeartBeat()
          }, 20000);
        })
      }
    },
    sendGlobalHeartBeat() {
      if (this.openOrCloseBtnIcon === this.closeIcon) {
        // 连接状态再来判断是否需要发送心跳
        const ids = []
        this.checkTerminal.forEach(item => {
          if (this.rowDatas.find(row => Number.parseInt(row.id) === Number.parseInt(item.id))) {
            ids.push(Number.parseInt(item.id))
          }
        })
        if (ids.length > 0) {
          heartbeatOrCloseCmd(ids, 0, this.webSocketKey)
        }
      }
    },
    quickConfig() {
      this.$refs.cmdQuickTree.show()
    },
    deleteRowDatas() {
      var rowIds = []
      const rows = this.terminalTable.getSelectedDatas()
      rows.forEach(row => { rowIds.push(row.id) })
      this.rowDatas = this.rowDatas.filter(row => rowIds.indexOf(row.id) < 0)
      this.tableDataMultiFlag = false
    },
    initData() {
      this.hearbeatData = []
      this.connectionData = []
      this.resultData = []
      this.tempResultData = []
      this.doingExecuteData = []
      this.executeData = []
      this.lastHearbeatFlag = false
      this.resultFlag = false
      this.currentTerminal = {}
      this.interruptData = []
      this.interruptResultData = []
      this.nodeInfo = []
    },
    clearAllInterval() {
      clearInterval(this.timer)
      clearInterval(this.hearbeatTimer)
      clearInterval(this.interruptTimer)
      clearInterval(this.waitConDataTimer)
      clearInterval(this.sendCmdTimer)
      clearInterval(this.globalHeartBeatTimer)
    },
    // 生成webSocket的key
    getCurrentTimeFormatted() {
      const now = new Date();
      const year = now.getFullYear().toString().padStart(4, '0');
      const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的
      const day = now.getDate().toString().padStart(2, '0');
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      return `${year}${month}${day}${hours}${minutes}${seconds}`;
    },
    generateRandomString(length) {
      let result = '';
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      const charactersLength = characters.length;
      for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }
      return result;
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
