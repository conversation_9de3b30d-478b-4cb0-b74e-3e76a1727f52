<template>
  <div class="app-container">
    <TransferTree
      ref="transferTree"
      :show-tree.sync="showTree"
      :showed-tree="['terminal']"
      :os-type-filter="7"
      :terminal-filter-key="terminalFilter"
      @data-change="strategyTargetNodeChange"
    />
    <div class="table-container">
      <div class="toolbar">
        <el-button type="primary" size="mini" @click="toggleTreeMenu">
          <svg-icon icon-class="tree" />
        </el-button>
        <el-button type="primary" icon="el-icon-refresh" :disabled="refreshDisable" size="mini" @click="refresh">{{ $t('button.refresh') }}</el-button>
        <audit-log-exporter v-permission="'151'" :request="handleExport" :disabled="refreshDisable"/>

        <div class="searchCon">
          <el-input v-model="searchText" v-trim clearable :placeholder="$t('pages.softwareName')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table
        ref="tableList"
        v-loading="tableLoading"
        :col-model="colModel"
        :multi-select="false"
        :show-pager="false"
        :row-datas="rowData"
        @selectionChangeEnd="selectionChangeEnd"
      />
    </div>
    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.createUninstallTask')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="150px"
        style="width: 700px; margin-left: 30px;"
      >
        <FormItem :label="$t('pages.taskName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="20"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.softwareName')">
          <el-input v-model="temp.objectName" readonly show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('pages.softwareVersion')">
          <el-input v-model="temp.objectProperty" readonly show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.remark" :maxlength="300" show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('pages.unloadingMode')">
          <el-radio-group v-model="temp.taskType">
            <el-radio :label="4">{{ $t('pages.generalUninstall') }}</el-radio>
            <el-radio :label="5">{{ $t('pages.silentUninstall') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="temp.taskType==4" :label="$t('pages.unloadParameters')">
          <el-input v-model="temp.installParam" :maxlength="300" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="temp.taskType==5" :label="$t('pages.unloadDelete')">
          <el-input v-model="temp.exceptKey" :maxlength="300" show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('pages.executionValidity')">
          <el-radio-group v-model="temp.noLimitTime">
            <el-radio :label="0">{{ $t('pages.limitedTime') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.permanentlyValid') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="temp.noLimitTime == 0" :label="$t('pages.startTime')">
          <el-date-picker
            v-model="temp.taskValidBeginTime"
            disabled
            type="datetime"
            :placeholder="$t('pages.selectDateTime')"
          ></el-date-picker>
        </FormItem>
        <FormItem v-if="temp.noLimitTime == 0" :label="$t('pages.endTime')">
          <el-date-picker
            v-model="temp.taskValidEndTime"
            :clearable="false"
            :editable="false"
            type="datetime"
            :picker-options="pickerOptionsEnd"
            :placeholder="$t('pages.selectDateTime')"
          ></el-date-picker>
        </FormItem>
        <el-card style="margin-top: 20px">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.effectiveObject') }}</span>
          </div>
          <tree-select-panel
            leaf-key="terminal"
            :local-search="false"
            :selected-data="selectedTerminalData"
            :include-child="false"
            :to-select-title="$t('pages.optionalTerminal')"
            :selected-title="$t('pages.selectedTerminal')"
            height="370px"
          />
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="createData()">{{ $t('button.confirm') }}</el-button>
        <el-button @click="dialogFormVisible = false">{{ $t('button.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSystemSoft, updateSystemSoft, exportSystemSoft } from '@/api/assets/systemMaintenance/soft'
import { enableCtrlTerm, ctrlErrorMap } from '@/api/system/terminalManage/moduleConfig'
import { insertTask, getByName } from '@/api/assets/systemMaintenance/softwareTask'
import TreeSelectPanel from '@/components/TreeSelectPanel'
import moment from 'moment'

export default {
  name: 'SoftList',
  components: { TreeSelectPanel },
  data() {
    return {
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.temp.taskValidBeginTime
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime() - 1 * 24 * 60 * 60 * 1000)
          }
        }
      },
      temp: {
        id: null,
        active: false,
        name: '',
        objectName: '',
        objectProperty: '',
        taskType: 5,
        installParam: '',
        remark: '',
        exceptKey: '',
        taskValidBeginTime: '',
        taskValidEndTime: '',
        noLimitTime: 0,
        objectIds: [],
        objectGroupIds: []
      },
      query: { // 查询条件
        objectType: undefined,
        objectId: undefined
      },
      selectedTerminalData: [],
      showTree: true,
      addAble: false,
      ctrlAble: false,
      submitting: false,
      dialogFormVisible: false,
      refreshDisable: true,
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      },
      colModel: [
        { prop: 'name', label: 'softwareName', width: '150', sort: true, formatter: this.nameFormatter },
        { prop: 'publisher', label: 'publisher', width: '150', sort: true },
        { prop: 'version', label: 'softwareVersion', width: '100' },
        { prop: 'size', label: 'size', width: '100', sort: true },
        { prop: 'installLocation', label: 'installPath', width: '100', sort: true },
        { prop: 'installDate', label: 'installTime', width: '200', sort: true },
        { label: 'operate', type: 'button', fixedWidth: '100', fixed: 'right',
          buttons: [
            { label: 'uninstall', click: this.uninstall, formatter: this.btnFormatter, disabledFormatter: this.disabledFormatter }
          ]
        }
      ],
      tempRowData: [],
      rowData: [],
      tableLoading: false,
      isLog: true, // 是否记录管理员日志，自动刷新不应该重复记录日志
      searchText: ''
    }
  },
  computed: {
    gridTable() {
      return this.$refs['tableList']
    }
  },
  watch: {
    tempRowData(val) {
      this.handleFilter()
    },
    showTree() {
      this.$emit('stg-tree-params', {
        showTree: this.showTree
      })
    }
  },
  methods: {
    strategyTargetTree() {
      return this.$refs['strategyTargetTree'] || this.$store.getters.commonVm.strategyTargetTree
    },
    btnFormatter(data, btn) {
      return data.btnText ? data.btnText : this.$t('pages.uninstall')
    },
    disabledFormatter(data, btn) {
      return !!data.btnText
    },
    nameValidator(rule, value, callback) {
      const data = Object.assign({ name: value }, this.query)
      getByName(data).then(respond => {
        const bean = respond.data
        if (bean && bean.guid !== this.temp.guid) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    selectionChangeEnd: function(rowDatas) {
      this.addAble = rowDatas && rowDatas.length == 1
    },
    async checkCtrlAble(termId) {
      this.ctrlAble = false
      await enableCtrlTerm('soft', termId, [2]).then(resp => {
        if (resp.data < 1) {
          this.ctrlAble = false
          this.refreshDisable = true
          this.$message({ duration: 2000, message: ctrlErrorMap(resp.data) });
        } else {
          this.ctrlAble = true
        }
      })
    },
    terminalFilter(node) {
      // 分组显示
      if (node.type == 3) {
        // 回收站节点不显示
        return node.id != 'G-2'
      }
      const type = parseInt(node.dataType)
      if (isNaN(type)) {
        return false
      }
      return !((type & 0xf0) === 32 || (type & 0xf0) === 0x80)
    },
    strategyTargetNodeChange(tabName, data) {
      if (!data) return
      this.tempRowData = []
      if (data.id.indexOf('G') < 0) {
        this.refreshDisable = false
        this.checkCtrlAble(data.dataId).then(() => {
          if (!this.ctrlAble) return
          this.sendToUser(data)
        })
      } else {
        this.refreshDisable = true
        this.tableLoading = false
      }
    },
    sendToUser(data) {
      this.tableLoading = true
      const that = this
      getSystemSoft(data.dataId, this.isLog).then(respond => {
        that.tempRowData = [...respond.data]
        that.tableLoading = false
        that.isLog = true
      }).catch(e => {
        if (that.tableLoading) {
          that.tableLoading = false
        }
      })
    },
    refresh() {
      this.isLog = false
      const curNodeData = this.strategyTargetTree().getCurrentNode()
      this.strategyTargetNodeChange('terminal', curNodeData)
    },
    handleFilter() {
      const searchText = this.searchText.toLowerCase()
      this.rowData = this.tempRowData.filter(data => {
        const name = data.name.toLowerCase()
        return name.indexOf(searchText) > -1
      })
    },
    handleExport(exportType) {
      const curNodeData = this.strategyTargetTree().getCurrentNode()
      return exportSystemSoft({ exportType, termId: curNodeData.dataId, processName: this.searchText })
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = {
        id: null,
        active: false,
        name: '',
        objectName: '',
        objectProperty: '',
        taskType: 5,
        installParam: '',
        remark: '',
        exceptKey: '',
        taskValidBeginTime: '',
        taskValidEndTime: '',
        noLimitTime: 0,
        objectIds: [],
        objectGroupIds: []
      }
    },
    formatSubmitData() { // 格式化数据
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      this.selectedTerminalData.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          this.temp.objectIds.push(nodeData.dataId)
        } else {
          this.temp.objectGroupIds.push(nodeData.dataId)
        }
      })
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (moment(this.temp.taskValidBeginTime).isAfter(moment(this.temp.taskValidEndTime))) {
            this.$message({
              message: this.$t('pages.softwareTask_Validate1'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          if (this.temp.noLimitTime == 1) {
            this.temp.taskValidEndTime = '0000-00-00 00:00:00'
          }
          this.formatSubmitData()
          insertTask(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    uninstall(row) {
      const terminalNode = this.strategyTargetTree().getCurrentNode()
      this.$confirmBox(`${this.$t('pages.softwareTask_Validate2')} ${row.name}${this.$t('pages.softwareTask_Validate3')}`, this.$t('text.prompt')).then(() => {
        row.btnText = this.$t('pages.unloading')
        const index = this.tempRowData.findIndex(item => item.name === row.name)
        this.tempRowData.splice(index, 1, row)
        updateSystemSoft({
          termId: terminalNode.dataId,
          name: row.name,
          version: row.version,
          uninstallString: row.uninstallString,
          quietUninstallString: row.quietUninstallString
        }).then(respond => {
          if (respond.data) {
            this.$notify({ title: this.$t('text.success'), message: this.$t('pages.uninstallSuccess'), type: 'success', duration: 2000 })
          } else {
            this.$notify({ title: this.$t('text.fail'), message: this.$t('pages.uninstallFail'), type: 'error', duration: 2000 })
          }
          this.refresh()
        })
      }).catch(() => {

      })
    },
    nameFormatter(row, data) {
      if (!row.icon && row.iconLen === undefined) { // 旧版不显示图标
        return data
      }
      if (!row.icon && row.iconLen === 0) { // 25D1新版，无图标情况下，显示默认图标
        return `<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABpSURBVDhPYxh40NrZ858STLIBh44cQ6HBBjx79owsjNWAX79+EY1xGsBQfh0Fg8RmzpyJggkawFt38z9b9Q24AdgwXgPAmisocAFMM9kuQMYgMXTbSTYAG8ZpALEYbgAlGJojBgwwMAAAtd9fYDL5HxkAAAAASUVORK5CYII=" style="width: 16px; height: 16px; vertical-align: text-bottom; margin-right: 5px;" alt="ico">${data}`
      }
      return `<img src="data:image/png;base64,${row.icon}" style="width: 16px; height: 16px; vertical-align: text-bottom; margin-right: 5px;" alt="ico">${data}`
    }
  }
}
</script>
