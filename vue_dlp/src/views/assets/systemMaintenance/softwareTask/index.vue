<template>
  <soft-list ref="softList" />
  <!-- <div>
    <el-tabs ref="tabs" v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane label="软件信息" name="softList">
        <soft-list/>
      </el-tab-pane> 
      <el-tab-pane label="任务信息" name="taskList">
        <task-list/>
      </el-tab-pane>
    </el-tabs>
  </div> -->
</template>

<script>
import SoftList from '@/views/assets/systemMaintenance/softwareTask/softList'
// import TaskList from '@/views/assets/systemMaintenance/softwareTask/taskList'
export default {
  name: 'SoftwareTask',
  components: { SoftList },
  data() {
    return {
      activeName: 'softList'
    }
  },
  methods: {
    tabClick(pane, event) {
    }
  }
}
</script>
