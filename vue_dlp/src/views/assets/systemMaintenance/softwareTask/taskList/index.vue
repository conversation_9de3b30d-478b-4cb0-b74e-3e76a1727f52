<template>
  <div class="app-container">
    <TransferTree ref="transferTree" :show-tree.sync="showTree" :showed-tree="['terminal']" @data-change="strategyTargetNodeChange"/>
    <div class="table-container">
      <div class="toolbar">
        <el-button size="mini" :disabled="!deleteable" @click="handleDelete">
          {{ $t('pages.deleteTask') }}
        </el-button>
        <div class="searchCon">
          <el-input v-model="query.searchInfo" v-trim clearable :placeholder="$t('pages.softwareTask_Validate4')" style="width: 200px;" @keyup.enter.native="handleFilter"></el-input>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">
            {{ $t('table.search') }}
          </el-button>
        </div>
      </div>
      <grid-table ref="tableList" :col-model="colModel" :show-pager="false" :row-data-api="rowDataApi" @selectionChangeEnd="selectionChangeEnd" />
    </div>

    <el-dialog
      v-el-drag-dialog
      :close-on-click-modal="false"
      :modal="false"
      :title="$t('pages.createUninstallTask')"
      :visible.sync="dialogFormVisible"
      width="800px"
    >
      <Form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="right"
        label-width="150px"
        style="width: 700px; margin-left: 30px;"
      >
        <FormItem :label="$t('pages.taskName')" prop="name">
          <el-input v-model="temp.name" v-trim :maxlength="20"></el-input>
        </FormItem>
        <FormItem :label="$t('pages.softwareName')">
          <el-input v-model="temp.objectName" :maxlength="60" show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('pages.softwareVersion')">
          <el-input v-model="temp.objectProperty" :maxlength="20" show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('pages.remark')">
          <el-input v-model="temp.remark" :maxlength="300" show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('pages.unloadingMode')">
          <el-radio-group v-model="temp.taskType">
            <el-radio :label="4">{{ $t('pages.generalUninstall') }}</el-radio>
            <el-radio :label="5">{{ $t('pages.silentUninstall') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="temp.taskType==4" :label="$t('pages.unloadParameters')">
          <el-input v-model="temp.installParam" :maxlength="300" show-word-limit></el-input>
        </FormItem>
        <FormItem v-if="temp.taskType==5" :label="$t('pages.unloadDelete')">
          <el-input v-model="temp.exceptKey" :maxlength="300" show-word-limit></el-input>
        </FormItem>
        <FormItem :label="$t('pages.executionValidity')">
          <el-radio-group v-model="temp.noLimitTime">
            <el-radio :label="0">{{ $t('pages.limitedTime') }}</el-radio>
            <el-radio :label="1">{{ $t('pages.permanentlyValid') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem v-if="temp.noLimitTime == 0" :label="$t('pages.startTime')">
          <el-date-picker
            v-model="temp.taskValidBeginTime"
            disabled
            type="datetime"
            :placeholder="$t('pages.selectDateTime')"
          >
          </el-date-picker>
        </FormItem>
        <FormItem v-if="temp.noLimitTime == 0" :label="$t('pages.endTime')">
          <el-date-picker
            v-model="temp.taskValidEndTime"
            :clearable="false"
            :editable="false"
            type="datetime"
            :picker-options="pickerOptionsEnd"
            :placeholder="$t('pages.selectDateTime')"
          >
          </el-date-picker>
        </FormItem>
        <el-card style="margin-top: 20px">
          <div slot="header" class="clearfix">
            <span>{{ $t('pages.effectiveObject') }}</span>
          </div>
          <tree-select-panel
            ref="terminalTree"
            leaf-key="terminal"
            :local-search="false"
            :selected-data="selectedTerminalData"
            :include-child="false"
            :to-select-title="$t('pages.optionalTerminal')"
            :selected-title="$t('pages.selectedTerminal')"
            height="370px"
          />
        </el-card>
      </Form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="submitting" type="primary" @click="updateData">
          {{ $t('button.confirm') }}
        </el-button>
        <el-button @click="dialogFormVisible = false">
          {{ $t('button.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTaskList, updateTask, getByName, listObjectTree, deleteTask } from '@/api/assets/systemMaintenance/softwareTask'
import TreeSelectPanel from '@/components/TreeSelectPanel'
import moment from 'moment'

export default {
  name: 'TaskList',
  components: { TreeSelectPanel },
  data() {
    return {
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.temp.taskValidBeginTime
          if (beginDateVal) {
            return (time.getTime() < new Date(beginDateVal).getTime() - 1 * 24 * 60 * 60 * 1000)
          }
        }
      },
      colModel: [
        { prop: 'name', label: 'taskName', width: '150' },
        { prop: 'entityName', label: 'source', width: '150' },
        { prop: 'objectName', label: 'softwareName', width: '150' },
        { prop: 'objectProperty', label: 'softwareVersion', width: '100' },
        { prop: 'taskType', label: 'unloadingMode', width: '100', formatter: this.taskTypeFormat },
        { prop: 'installParam', label: 'unloadParameters', width: '100' },
        { prop: 'taskValidEndTime', label: 'executionPeriod', width: '200', formatter: this.timeIncludeFormat },
        { prop: 'remark', label: 'remark', width: '200' },
        {
          prop: 'createdTime', label: 'operate', type: 'button', fixedWidth: '100',
          buttons: [{
            label: 'edit', formatter: this.buttonFormatter, click: this.handleUpdate }
          ]
        }
      ],
      temp: {
        id: null,
        active: false,
        name: '',
        objectName: '',
        objectProperty: '',
        taskType: 5,
        installParam: '',
        remark: '',
        exceptKey: '',
        taskValidBeginTime: '',
        taskValidEndTime: '',
        noLimitTime: 0,
        objectIds: [],
        objectGroupIds: []
      },
      query: { // 查询条件
        page: 1,
        searchInfo: null,
        objectType: undefined,
        objectId: undefined
      },
      selectedTerminalData: [],
      showTree: true,
      deleteable: false,
      submitting: false,
      dialogFormVisible: false,
      rules: {
        name: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.nameValidator, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    gridTable() {
      return this.$refs['tableList']
    }
  },
  created() {
  },
  methods: {
    nameValidator(rule, value, callback) {
      const data = Object.assign({ name: value }, this.query)
      getByName(data).then(respond => {
        const bean = respond.data
        if (bean && bean.guid !== this.temp.guid) {
          callback(new Error(this.$t('valid.sameName')))
        } else {
          callback()
        }
      })
    },
    rowDataApi: function(option) {
      const searchQuery = Object.assign({}, this.query, option)
      return getTaskList(searchQuery)
    },
    selectionChangeEnd: function(rowDatas) {
      this.deleteable = rowDatas && rowDatas.length > 0
    },
    strategyTargetNodeChange: function(tabName, checkedNode) {
      if (checkedNode) {
        this.query.objectType = checkedNode.type
        this.query.objectId = checkedNode.dataId
      } else {
        this.query.objectType = undefined
        this.query.objectId = undefined
      }
      this.gridTable.execRowDataApi(this.query)
    },
    handleFilter() {
      this.query.page = 1
      this.gridTable.execRowDataApi(this.query)
    },
    handleDrag() {
      // this.$refs.select.blur()
    },
    toggleTreeMenu() {
      this.showTree = !this.showTree
    },
    resetTemp() {
      this.temp = {
        id: null,
        active: false,
        name: '',
        objectName: '',
        objectProperty: '',
        taskType: 5,
        installParam: '',
        remark: '',
        exceptKey: '',
        taskValidBeginTime: '',
        taskValidEndTime: '',
        noLimitTime: 0,
        objectIds: [],
        objectGroupIds: []
      }
    },
    listObjectTree() {
      if (this.temp.id) {
        listObjectTree({ id: this.temp.id, objectType: 1 }).then(respond => {
          this.selectedTerminalData = respond.data
        })
      } else {
        this.selectedTerminalData.splice(0)
      }
    },
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign(this.temp, row)
      if (this.temp.taskValidEndTime == '0000-00-00 00:00:00') {
        this.temp.noLimitTime = 1
      } else {
        this.temp.noLimitTime = 0
      }
      this.listObjectTree()
      this.dialogFormVisible = true
    },
    handleDelete() {
      this.$confirmBox(this.$t('pages.validateMsg_deleteMsg'), this.$t('text.prompt')).then(() => {
        const idArr = []
        this.gridTable.selectedData.forEach(function(item, index) {
          idArr.push(item.id)
        })
        if (idArr.length > 0) {
          const obj = {
            ids: idArr.join(',')
          }
          deleteTask(obj).then(() => {
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.deleteSuccess'),
              type: 'success',
              duration: 2000
            })
          })
        }
      }).catch(() => {})
    },
    formatSubmitData() { // 格式化数据
      this.temp.objectIds = []
      this.temp.objectGroupIds = []
      this.selectedTerminalData.forEach(nodeData => {
        if (nodeData.id.indexOf('G' + nodeData.dataId) < 0) {
          this.temp.objectIds.push(nodeData.dataId)
        } else {
          this.temp.objectGroupIds.push(nodeData.dataId)
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (moment(this.temp.taskValidBeginTime).isAfter(moment(this.temp.taskValidEndTime))) {
            this.$message({
              message: this.$t('pages.softwareTask_Validate1'),
              type: 'error',
              duration: 2000
            })
            this.submitting = false
            return
          }
          if (this.temp.noLimitTime == 1) {
            this.temp.taskValidEndTime = '0000-00-00 00:00:00'
          }
          this.formatSubmitData()
          updateTask(this.temp).then(() => {
            this.submitting = false
            this.dialogFormVisible = false
            this.gridTable.execRowDataApi()
            this.$notify({
              title: this.$t('text.success'),
              message: this.$t('text.createSuccess'),
              type: 'success',
              duration: 2000
            })
          }).catch(res => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    taskTypeFormat: function(row, data) {
      return data == 4 ? this.$t('pages.generalUninstall') : this.$t('pages.silentUninstall')
    },
    timeIncludeFormat: function(row, data) {
      if (row.taskValidEndTime == '0000-00-00 00:00:00') {
        return this.$t('pages.permanentEffect')
      } else {
        return row.taskValidEndTime
      }
    }
  }
}
</script>
