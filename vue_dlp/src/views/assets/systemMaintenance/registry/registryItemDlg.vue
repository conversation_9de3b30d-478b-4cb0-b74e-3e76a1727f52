<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="title"
    :visible.sync="itemDlgVisible"
    width="400px"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="50px">
      <FormItem :label="$t('table.name')" prop="itemName">
        <el-input v-model="temp.itemName" maxlength="255" clearable style="width: 100%;"></el-input>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="itemDlgVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addRegistryItem, updateRegistryItem } from '@/api/assets/systemMaintenance/registry'
import { countUtf8Byte } from '@/utils/utf8'

export default {
  name: 'RegistryItemDlg',
  data() {
    return {
      itemDlgVisible: false,
      dialogStatus: 'create',
      submitting: false,
      temp: {},
      defaultTemp: {
        termId: undefined,
        path: '',
        itemNameLen: 0,
        itemName: '',
        oldNameLen: 0,
        oldName: '',
        newNameLen: 0,
        newName: ''
      },
      rules: {
        itemName: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' },
          { validator: this.itemNameValidator, trigger: 'blur' }
        ]
      },
      errorCode: {
        2: this.$t('pages.systemNotFindPath'),
        5: this.$t('pages.notAccess'),
        87: '无法创建项：写入注册表时出错',
        183: this.$t('pages.alreadyExistsInfo', { info: this.$t('table.name') }),
        1379: this.$t('pages.alreadyExistsInfo', { info: this.$t('table.name') })
      },
      curNode: undefined // 当前修改项的树节点
    }
  },
  computed: {
    title() {
      return this.dialogStatus === 'create' ? this.$t('text.addInfo', { info: this.$t('table.registryItem') }) : this.$t('text.editInfo', { info: this.$t('table.registryItem') })
    }
  },
  methods: {
    show(dialogStatus, termId, path, oldName, node) {
      this.resetTemp()
      this.dialogStatus = dialogStatus
      this.temp.termId = termId
      this.temp.path = path
      if (this.dialogStatus === 'update') {
        this.temp.itemName = oldName
        this.temp.oldName = oldName
        this.curNode = node
      }
      this.itemDlgVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.curNode = undefined
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$confirmBox(this.$t('pages.confirmDeleteRegistry', { operator: this.$t('button.add'), info: this.$t('table.registryItem') }), this.$t('text.warning'), 'warning').then(() => {
            const formData = this.formatFormData(this.temp)
            addRegistryItem(formData).then(respond => {
              if (respond.data == 0) {
                this.$emit('submitEnd', this.dialogStatus, this.curNode)
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.insertSuccess'),
                  type: 'success',
                  duration: 2000
                })
              } else {
                this.$notify({
                  title: this.$t('text.fail'),
                  message: this.errorCode[respond.data] ? this.errorCode[respond.data] : '错误码' + respond.data,
                  type: 'error',
                  duration: 2000
                })
              }
              this.submitting = false
              this.itemDlgVisible = false
            }).catch(() => {
              this.submitting = false
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$confirmBox(this.$t('pages.confirmDeleteRegistry', { operator: this.$t('button.edit'), info: this.$t('table.registryItem') }), this.$t('text.warning'), 'warning').then(() => {
            const formData = this.formatFormData(this.temp)
            updateRegistryItem(formData).then(respond => {
              if (respond.data == 0) {
                this.$emit('submitEnd', this.dialogStatus, this.curNode)
                this.$notify({
                  title: this.$t('text.success'),
                  message: this.$t('text.editSuccess'),
                  type: 'success',
                  duration: 2000
                })
              } else {
                this.$notify({
                  title: this.$t('text.fail'),
                  message: this.errorCode[respond.data] ? this.errorCode[respond.data] : '错误码' + respond.data,
                  type: 'error',
                  duration: 2000
                })
              }
              this.submitting = false
              this.itemDlgVisible = false
            }).catch(() => {
              this.submitting = false
            })
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    formatFormData(temp) {
      const formData = {
        termId: undefined,
        pathLen: 0,
        path: '',
        itemNameLen: 0,
        itemName: '',
        oldNameLen: 0,
        oldName: '',
        newNameLen: 0,
        newName: ''
      }
      formData.termId = temp.termId
      formData.pathLen = countUtf8Byte(temp.path)
      formData.path = temp.path
      formData.itemNameLen = countUtf8Byte(temp.itemName)
      formData.itemName = temp.itemName
      if (this.dialogStatus === 'update') {
        formData.oldNameLen = countUtf8Byte(temp.oldName)
        formData.oldName = temp.oldName
        formData.newNameLen = countUtf8Byte(temp.itemName)
        formData.newName = temp.itemName
      }
      return formData
    },
    itemNameValidator(rule, value, callback) {
      const oldName = this.temp.oldName
      if (value == oldName) {
        callback(new Error(this.$t('pages.notSameWithOldName')))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang='scss' scoped>
</style>
