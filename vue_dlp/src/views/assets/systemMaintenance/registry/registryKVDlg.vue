<template>
  <el-dialog
    v-el-drag-dialog
    :close-on-click-modal="false"
    :modal="false"
    :title="title"
    :visible.sync="kvDlgVisible"
    class="registry-kv-dlg"
    width="450px"
  >
    <Form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="50px">
      <FormItem :label="$t('table.name')" class="key-name-label" prop="keyName">
        <el-input v-model="temp.keyName" maxlength="260" :disabled="dialogStatus === 'update'" :placeholder="placeholder" clearable style="width: 100%;"></el-input>
      </FormItem>
      <FormItem :label="$t('table.type')" prop="type">
        <el-select v-model="temp.type" :disabled="dialogStatus === 'update'" :placeholder="$t('text.select')" @change="handleTypeChange">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          <el-option v-if="dialogStatus === 'update'" label="REG_NONE" :value="0"></el-option>
        </el-select>
      </FormItem>
      <FormItem v-if="temp.type === 0" :label="$t('pages.data')" :tooltip-content="$t('pages.binaryTip')" prop="value">
        <el-input v-model="temp.value" maxlength="509" type="textarea" autosize clearable style="width: 100%;" @blur="autoFillSpace" @keyup.native="autoFillSpace"></el-input>
      </FormItem>
      <FormItem v-else-if="temp.type === 1" :label="$t('pages.data')" prop="value">
        <el-input v-model="temp.value" maxlength="512" clearable style="width: 100%;"></el-input>
      </FormItem>
      <FormItem v-else-if="temp.type === 2" :label="$t('pages.data')" prop="value">
        <el-input v-model="temp.value" maxlength="512" clearable style="width: 100%;"></el-input>
      </FormItem>
      <FormItem v-else-if="temp.type === 3" :label="$t('pages.data')" :tooltip-content="$t('pages.binaryTip')" prop="value">
        <el-input v-model="temp.value" maxlength="509" type="textarea" autosize clearable style="width: 100%;" @blur="autoFillSpace" @keyup.native="autoFillSpace"></el-input>
      </FormItem>
      <div v-else-if="temp.type === 4">
        <FormItem :label="$t('pages.baseNumber')">
          <el-radio-group v-model="baseNumber" @change="handleBaseNumChange">
            <el-radio :label="1">{{ $t('pages.hexadecimal') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.decimal') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.data')" prop="value">
          <el-input v-model="temp.value" clearable :maxlength="baseNumber === 1 ? 8 : 10" style="width: 100%;" @input="handleDQWORD"></el-input>
        </FormItem>
      </div>
      <div v-else-if="temp.type === 11">
        <FormItem :label="$t('pages.baseNumber')">
          <el-radio-group v-model="baseNumber" @change="handleBaseNumChange">
            <el-radio :label="1">{{ $t('pages.hexadecimal') }}</el-radio>
            <el-radio :label="2">{{ $t('pages.decimal') }}</el-radio>
          </el-radio-group>
        </FormItem>
        <FormItem :label="$t('pages.data')" prop="value">
          <el-input v-model="temp.value" clearable :maxlength="baseNumber === 1 ? 16 : 20" style="width: 100%;" @input="handleDQWORD"></el-input>
        </FormItem>
      </div>
      <FormItem v-else-if="temp.type === 7" :label="$t('pages.data')" :tooltip-content="$t('pages.expandTip')" prop="value">
        <el-input v-model="temp.value" maxlength="512" type="textarea" autosize clearable style="width: 100%;"></el-input>
      </FormItem>
    </Form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="submitting" type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">{{ $t('button.confirm') }}</el-button>
      <el-button @click="kvDlgVisible = false">{{ $t('button.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addRegistryKV, updateRegistryKV } from '@/api/assets/systemMaintenance/registry'
import { countUtf8Byte } from '@/utils/utf8'

export default {
  name: 'RegistryKVDlg',
  data() {
    return {
      kvDlgVisible: false,
      dialogStatus: 'create',
      submitting: false,
      temp: {},
      defaultTemp: {
        termId: undefined,
        path: '',
        keyName: '',
        type: undefined,
        value: ''
      },
      rules: {
        keyName: [
          { validator: this.keyNameValidator, trigger: 'blur' }
        ],
        type: [
          { required: true, message: this.$t('pages.required1'), trigger: 'blur' }
        ],
        value: [
          { validator: this.valueValidator, trigger: 'blur' }
        ]
      },
      typeOptions: [
        { value: 1, label: this.$t('pages.REG_SZ') },
        { value: 3, label: this.$t('pages.REG_BINARY') },
        { value: 4, label: this.$t('pages.REG_DWORD') },
        { value: 11, label: this.$t('pages.REG_QWORD') },
        { value: 7, label: this.$t('pages.REG_MULTI_SZ') },
        { value: 2, label: this.$t('pages.REG_EXPAND_SZ') }
        // { value: 8, label: 'REG_RESOURCE_LIST' },
        // { value: 9, label: 'REG_FULL_RESOURCE_DESCRIPTOR' },
        // { value: 10, label: 'REG_RESOURCE_REQUIREMENTS_LIST' }
      ],
      errorCode: {
        2: this.$t('pages.systemNotFindPath'),
        5: this.$t('pages.notAccess'),
        183: this.$t('pages.alreadyExistsInfo', { info: this.$t('table.name') })
      },
      placeholder: '',
      baseNumber: 1
    }
  },
  computed: {
    title() {
      return this.dialogStatus === 'create' ? this.$t('text.addInfo', { info: this.$t('table.registryKV') }) : this.$t('text.editInfo', { info: this.$t('table.registryKV') })
    }
  },
  methods: {
    show(termId, path, dialogStatus, row) {
      this.resetTemp()
      this.temp.termId = termId
      this.temp.path = path
      this.dialogStatus = dialogStatus
      if (dialogStatus === 'update') {
        this.temp.keyName = row.keyName
        this.temp.type = row.type
        this.temp.value = row.value
        if (this.temp.keyName === '') {
          this.placeholder = `(${this.$t('text.default')})`
        }
        if (this.temp.type === 3 || this.temp.type === 0) { // REG_BINARY、REG_NONE
          this.autoFillSpace()
        } else if (this.temp.type === 4 || this.temp.type === 11) { // REG_DWORD、REG_QWORD
          this.handleBaseNumChange(1)
        } else if (this.temp.type === 7) { // REG_MULTI_SZ
          this.temp.value = row.value.split('#end#').join('\n')
        }
      }
      this.kvDlgVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    resetTemp() {
      this.temp = Object.assign({}, this.defaultTemp)
      this.placeholder = ''
      this.baseNumber = 1
    },
    createData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$confirmBox(this.$t('pages.confirmDeleteRegistry', { operator: this.$t('button.add'), info: this.$t('table.registryKV') }), this.$t('text.warning')).then(() => {
            const formData = this.formatFormData(this.temp)
            this.validateValueAndExec(formData, 'create')
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    addRegistryKV(formData) {
      addRegistryKV(formData).then(respond => {
        if (respond.data == 0) {
          this.$emit('submitEnd', formData)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.insertSuccess'),
            type: 'success',
            duration: 2000
          })
        } else {
          this.$notify({
            title: this.$t('text.insertFail'),
            message: this.errorCode[respond.data] ? this.errorCode[respond.data] : '错误码' + respond.data,
            type: 'error',
            duration: 2000
          })
        }
        this.submitting = false
        this.kvDlgVisible = false
      }).catch(() => {
        this.submitting = false
      })
    },
    updateData() {
      this.submitting = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$confirmBox(this.$t('pages.confirmDeleteRegistry', { operator: this.$t('button.edit'), info: this.$t('table.registryKV') }), this.$t('text.warning')).then(() => {
            const formData = this.formatFormData(this.temp)
            this.validateValueAndExec(formData, 'update')
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.submitting = false
        }
      })
    },
    updateRegistryKV(formData) {
      updateRegistryKV(formData).then(respond => {
        if (respond.data == 0) {
          this.$emit('submitEnd', formData)
          this.$notify({
            title: this.$t('text.success'),
            message: this.$t('text.editSuccess'),
            type: 'success',
            duration: 2000
          })
        } else {
          this.$notify({
            title: this.$t('text.fail'),
            message: this.errorCode[respond.data] ? this.errorCode[respond.data] : '错误码' + respond.data,
            type: 'error',
            duration: 2000
          })
        }
        this.submitting = false
        this.kvDlgVisible = false
      }).catch(() => {
        this.submitting = false
      })
    },
    formatFormData(temp) {
      const formData = {
        termId: undefined,
        pathLen: 0,
        path: '',
        keyNameLen: 0,
        keyName: '',
        type: undefined,
        valueLen: 0,
        value: ''
      }
      formData.termId = temp.termId
      formData.pathLen = countUtf8Byte(temp.path)
      formData.path = temp.path
      formData.keyNameLen = countUtf8Byte(temp.keyName)
      formData.keyName = temp.keyName
      formData.type = temp.type
      formData.valueLen = countUtf8Byte(temp.value)
      formData.value = temp.value
      if (formData.type === 3 || formData.type === 0) { // REG_BINARY、REG_NONE
        const trimAll = this.trimAll(temp.value)
        formData.valueLen = countUtf8Byte(trimAll)
        formData.value = trimAll
      } else if (formData.type === 4 || formData.type === 11) { // REG_DWORD、REG_QWORD
        if (this.temp.value === undefined || this.temp.value === '') {
          this.temp.value = 0
        }
        if (this.baseNumber === 1) { // 16进制转为10进制
          // eslint-disable-next-line no-undef
          const val = BigInt('0x' + temp.value + '').toString(10)
          formData.valueLen = countUtf8Byte(val)
          formData.value = val
        }
      } else if (formData.type === 7) { // REG_MULTI_SZ
        const val = temp.value.replace(/\n+/g, '#end#'); // 连续出现多个回车符，只保留一个回车符
        formData.valueLen = countUtf8Byte(val)
        formData.value = val
      }
      return formData
    },
    handleDQWORD() {
      const val = this.temp.value
      if (this.baseNumber === 1) {
        // 只允许输入十六进制数
        this.temp.value = val.replace(/[^0-9A-Fa-f]/g, '')
      } else if (this.baseNumber === 2) {
        if (isNaN(val)) {
          this.temp.value = val.replace(/[^\d]/g, '')
        }
      }
    },
    trimAll(str) {
      const reg = /[\t\r\f\n\s]*/g;
      return str.replace(reg, '')
    },
    autoFillSpace() {
      let value = this.temp.value
      // 只允许输入十六进制数
      value = value.replace(/[^0-9A-Fa-f]/g, '')
      // 每2个字符插入两个空格
      value = value.replace(/(.{2})/g, '$1  ').trim()
      // 每16个十六进制字符插入一个换行符
      value = value.replace(/(.{32})/g, '$1\n').trim()
      value = value.toUpperCase()
      this.temp.value = value
    },
    valueValidator(rule, value, callback) {
      if (this.temp.type === 3 || this.temp.type === 0) {
        if (value) {
          if (this.trimAll(value).length % 2 !== 0) {
            callback(new Error(this.$t('pages.binaryTip2')))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    keyNameValidator(rule, value, callback) {
      if (this.placeholder) { // 说明此时修改的名称是（默认），则无需必填
        callback()
      } else {
        if (value) {
          callback()
        } else {
          callback(new Error(this.$t('pages.required1')))
        }
      }
    },
    handleBaseNumChange(baseNum) {
      if (this.temp.value === undefined || this.temp.value === '') {
        this.temp.value = ''
        return
      }
      if (baseNum === 1) { // 转为16进制
        // eslint-disable-next-line no-undef
        this.temp.value = BigInt(this.temp.value + '').toString(16)
      } else if (baseNum === 2) { // 转为10进制
        // eslint-disable-next-line no-undef
        this.temp.value = BigInt('0x' + this.temp.value).toString(10)
      }
    },
    validateValueAndExec(formData, dialogStatus) {
      const val = formData.value
      if (this.temp.type === 4) { // DWORD
        if (val > 4294967295) {
          this.$confirmBox(this.$t('pages.dwordTip'), this.$t('text.overflow')).then(() => {
            formData.value = '4294967295'
            this.execData(formData, dialogStatus)
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.execData(formData, dialogStatus)
        }
      } else if (this.temp.type === 11) { // QWORD
        // 大数据值比较
        // eslint-disable-next-line no-undef
        if (BigInt(val.toString()) > BigInt('18446744073709551615')) {
          this.$confirmBox(this.$t('pages.qwordTip'), this.$t('text.overflow')).then(() => {
            formData.value = '18446744073709551615'
            this.execData(formData, dialogStatus)
          }).catch(() => {
            this.submitting = false
          })
        } else {
          this.execData(formData, dialogStatus)
        }
      } else {
        this.execData(formData, dialogStatus)
      }
    },
    execData(formData, dialogStatus) {
      if (dialogStatus === 'create') {
        this.addRegistryKV(formData)
      } else if (dialogStatus === 'update') {
        this.updateRegistryKV(formData)
      }
    },
    handleTypeChange(val) {
      this.$refs.dataForm.clearValidate('type')
      this.temp.value = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.registry-kv-dlg {
  >>> textarea {
    white-space: nowrap; /* 禁止换行 */
    overflow-x: auto; /* 显示横向滚动条 */
    overflow-y: hidden; /* 隐藏纵向滚动条 */
  }
}
.key-name-label {
  &>>>.el-form-item__label::before {
    content: '*';
    color: #F56C6C;
  }
}
</style>
